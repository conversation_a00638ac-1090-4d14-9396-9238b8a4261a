// Code generated by protoc-gen-go. DO NOT EDIT.
// source: glory-reward/glory-reward.proto

package glory_reward // import "golang.52tt.com/protocol/services/glory-reward"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// FragmentRewardType 碎片获取途径
type FragmentRewardType int32

const (
	FragmentRewardType_FragmentRewardTypeInvalid  FragmentRewardType = 0
	FragmentRewardType_FragmentRewardTypeBase     FragmentRewardType = 1
	FragmentRewardType_FragmentRewardTypeTask     FragmentRewardType = 2
	FragmentRewardType_FragmentRewardTypeActivity FragmentRewardType = 3
	FragmentRewardType_FragmentRewardTypeTest     FragmentRewardType = 20
)

var FragmentRewardType_name = map[int32]string{
	0:  "FragmentRewardTypeInvalid",
	1:  "FragmentRewardTypeBase",
	2:  "FragmentRewardTypeTask",
	3:  "FragmentRewardTypeActivity",
	20: "FragmentRewardTypeTest",
}
var FragmentRewardType_value = map[string]int32{
	"FragmentRewardTypeInvalid":  0,
	"FragmentRewardTypeBase":     1,
	"FragmentRewardTypeTask":     2,
	"FragmentRewardTypeActivity": 3,
	"FragmentRewardTypeTest":     20,
}

func (x FragmentRewardType) String() string {
	return proto.EnumName(FragmentRewardType_name, int32(x))
}
func (FragmentRewardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{0}
}

// NoticeType 红点提醒类型
type NoticeType int32

const (
	NoticeType_NoticeTypeInvalid   NoticeType = 0
	NoticeType_NoticeTypeChannel   NoticeType = 1
	NoticeType_NoticeTypeHeader    NoticeType = 2
	NoticeType_NoticeTypeChallenge NoticeType = 3
)

var NoticeType_name = map[int32]string{
	0: "NoticeTypeInvalid",
	1: "NoticeTypeChannel",
	2: "NoticeTypeHeader",
	3: "NoticeTypeChallenge",
}
var NoticeType_value = map[string]int32{
	"NoticeTypeInvalid":   0,
	"NoticeTypeChannel":   1,
	"NoticeTypeHeader":    2,
	"NoticeTypeChallenge": 3,
}

func (x NoticeType) String() string {
	return proto.EnumName(NoticeType_name, int32(x))
}
func (NoticeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{1}
}

// FragmentType 星钻类型
type GloryFragmentType int32

const (
	GloryFragmentType_GloryFragmentTypeInvalid GloryFragmentType = 0
	GloryFragmentType_GloryFragmentTypeFame    GloryFragmentType = 1
	GloryFragmentType_GloryFragmentTypeGlory   GloryFragmentType = 2
)

var GloryFragmentType_name = map[int32]string{
	0: "GloryFragmentTypeInvalid",
	1: "GloryFragmentTypeFame",
	2: "GloryFragmentTypeGlory",
}
var GloryFragmentType_value = map[string]int32{
	"GloryFragmentTypeInvalid": 0,
	"GloryFragmentTypeFame":    1,
	"GloryFragmentTypeGlory":   2,
}

func (x GloryFragmentType) String() string {
	return proto.EnumName(GloryFragmentType_name, int32(x))
}
func (GloryFragmentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{2}
}

// RewardType 获取奖励的类型
type RewardType int32

const (
	RewardType_RewardTypeInvalid              RewardType = 0
	RewardType_RewardTypeFameBase             RewardType = 1
	RewardType_RewardTypeGloryBase            RewardType = 2
	RewardType_RewardTypeGloryMillionBase     RewardType = 3
	RewardType_RewardTypeTaskFirstSend        RewardType = 4
	RewardType_RewardTypeTaskGiftFirstSend    RewardType = 5
	RewardType_RewardTypeTaskSevenDays        RewardType = 6
	RewardType_RewardTypeTaskChallenge        RewardType = 7
	RewardType_RewardTypeTaskMillionChallenge RewardType = 8
	RewardType_RewardTypeTest                 RewardType = 20
)

var RewardType_name = map[int32]string{
	0:  "RewardTypeInvalid",
	1:  "RewardTypeFameBase",
	2:  "RewardTypeGloryBase",
	3:  "RewardTypeGloryMillionBase",
	4:  "RewardTypeTaskFirstSend",
	5:  "RewardTypeTaskGiftFirstSend",
	6:  "RewardTypeTaskSevenDays",
	7:  "RewardTypeTaskChallenge",
	8:  "RewardTypeTaskMillionChallenge",
	20: "RewardTypeTest",
}
var RewardType_value = map[string]int32{
	"RewardTypeInvalid":              0,
	"RewardTypeFameBase":             1,
	"RewardTypeGloryBase":            2,
	"RewardTypeGloryMillionBase":     3,
	"RewardTypeTaskFirstSend":        4,
	"RewardTypeTaskGiftFirstSend":    5,
	"RewardTypeTaskSevenDays":        6,
	"RewardTypeTaskChallenge":        7,
	"RewardTypeTaskMillionChallenge": 8,
	"RewardTypeTest":                 20,
}

func (x RewardType) String() string {
	return proto.EnumName(RewardType_name, int32(x))
}
func (RewardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{3}
}

// TaskType 任务完成状态
type TaskType int32

const (
	TaskType_TaskTypeInvalid   TaskType = 0
	TaskType_TaskTypeReward    TaskType = 1
	TaskType_TaskTypeNotReward TaskType = 2
)

var TaskType_name = map[int32]string{
	0: "TaskTypeInvalid",
	1: "TaskTypeReward",
	2: "TaskTypeNotReward",
}
var TaskType_value = map[string]int32{
	"TaskTypeInvalid":   0,
	"TaskTypeReward":    1,
	"TaskTypeNotReward": 2,
}

func (x TaskType) String() string {
	return proto.EnumName(TaskType_name, int32(x))
}
func (TaskType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{4}
}

type FloatingLayerSwitch int32

const (
	FloatingLayerSwitch_FloatingLayerSwitchInvalid FloatingLayerSwitch = 0
	FloatingLayerSwitch_FloatingLayerSwitchOpen    FloatingLayerSwitch = 1
	FloatingLayerSwitch_FloatingLayerSwitchClose   FloatingLayerSwitch = 2
)

var FloatingLayerSwitch_name = map[int32]string{
	0: "FloatingLayerSwitchInvalid",
	1: "FloatingLayerSwitchOpen",
	2: "FloatingLayerSwitchClose",
}
var FloatingLayerSwitch_value = map[string]int32{
	"FloatingLayerSwitchInvalid": 0,
	"FloatingLayerSwitchOpen":    1,
	"FloatingLayerSwitchClose":   2,
}

func (x FloatingLayerSwitch) String() string {
	return proto.EnumName(FloatingLayerSwitch_name, int32(x))
}
func (FloatingLayerSwitch) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{5}
}

// IsNeedNoticeReq 是否需要红点提醒
type IsNeedNoticeReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type                 NoticeType `protobuf:"varint,2,opt,name=type,proto3,enum=glory_reward.NoticeType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *IsNeedNoticeReq) Reset()         { *m = IsNeedNoticeReq{} }
func (m *IsNeedNoticeReq) String() string { return proto.CompactTextString(m) }
func (*IsNeedNoticeReq) ProtoMessage()    {}
func (*IsNeedNoticeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{0}
}
func (m *IsNeedNoticeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNeedNoticeReq.Unmarshal(m, b)
}
func (m *IsNeedNoticeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNeedNoticeReq.Marshal(b, m, deterministic)
}
func (dst *IsNeedNoticeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNeedNoticeReq.Merge(dst, src)
}
func (m *IsNeedNoticeReq) XXX_Size() int {
	return xxx_messageInfo_IsNeedNoticeReq.Size(m)
}
func (m *IsNeedNoticeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNeedNoticeReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsNeedNoticeReq proto.InternalMessageInfo

func (m *IsNeedNoticeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IsNeedNoticeReq) GetType() NoticeType {
	if m != nil {
		return m.Type
	}
	return NoticeType_NoticeTypeInvalid
}

type IsNeedNoticeResp struct {
	IsNotice             bool     `protobuf:"varint,1,opt,name=is_notice,json=isNotice,proto3" json:"is_notice,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsNeedNoticeResp) Reset()         { *m = IsNeedNoticeResp{} }
func (m *IsNeedNoticeResp) String() string { return proto.CompactTextString(m) }
func (*IsNeedNoticeResp) ProtoMessage()    {}
func (*IsNeedNoticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{1}
}
func (m *IsNeedNoticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNeedNoticeResp.Unmarshal(m, b)
}
func (m *IsNeedNoticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNeedNoticeResp.Marshal(b, m, deterministic)
}
func (dst *IsNeedNoticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNeedNoticeResp.Merge(dst, src)
}
func (m *IsNeedNoticeResp) XXX_Size() int {
	return xxx_messageInfo_IsNeedNoticeResp.Size(m)
}
func (m *IsNeedNoticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNeedNoticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsNeedNoticeResp proto.InternalMessageInfo

func (m *IsNeedNoticeResp) GetIsNotice() bool {
	if m != nil {
		return m.IsNotice
	}
	return false
}

// ReceiveRewardReq 领取任务奖励
type ReceiveRewardReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskId               []uint32 `protobuf:"varint,2,rep,packed,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveRewardReq) Reset()         { *m = ReceiveRewardReq{} }
func (m *ReceiveRewardReq) String() string { return proto.CompactTextString(m) }
func (*ReceiveRewardReq) ProtoMessage()    {}
func (*ReceiveRewardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{2}
}
func (m *ReceiveRewardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveRewardReq.Unmarshal(m, b)
}
func (m *ReceiveRewardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveRewardReq.Marshal(b, m, deterministic)
}
func (dst *ReceiveRewardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveRewardReq.Merge(dst, src)
}
func (m *ReceiveRewardReq) XXX_Size() int {
	return xxx_messageInfo_ReceiveRewardReq.Size(m)
}
func (m *ReceiveRewardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveRewardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveRewardReq proto.InternalMessageInfo

func (m *ReceiveRewardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReceiveRewardReq) GetTaskId() []uint32 {
	if m != nil {
		return m.TaskId
	}
	return nil
}

type ReceiveRewardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveRewardResp) Reset()         { *m = ReceiveRewardResp{} }
func (m *ReceiveRewardResp) String() string { return proto.CompactTextString(m) }
func (*ReceiveRewardResp) ProtoMessage()    {}
func (*ReceiveRewardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{3}
}
func (m *ReceiveRewardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveRewardResp.Unmarshal(m, b)
}
func (m *ReceiveRewardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveRewardResp.Marshal(b, m, deterministic)
}
func (dst *ReceiveRewardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveRewardResp.Merge(dst, src)
}
func (m *ReceiveRewardResp) XXX_Size() int {
	return xxx_messageInfo_ReceiveRewardResp.Size(m)
}
func (m *ReceiveRewardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveRewardResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveRewardResp proto.InternalMessageInfo

// TaskInfo 获取奖励的任务信息
type TaskInfo struct {
	TaskId               uint32            `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	FragType             GloryFragmentType `protobuf:"varint,2,opt,name=frag_type,json=fragType,proto3,enum=glory_reward.GloryFragmentType" json:"frag_type,omitempty"`
	RewardType           RewardType        `protobuf:"varint,3,opt,name=reward_type,json=rewardType,proto3,enum=glory_reward.RewardType" json:"reward_type,omitempty"`
	TaskType             TaskType          `protobuf:"varint,4,opt,name=task_type,json=taskType,proto3,enum=glory_reward.TaskType" json:"task_type,omitempty"`
	FinishNum            uint32            `protobuf:"varint,5,opt,name=finish_num,json=finishNum,proto3" json:"finish_num,omitempty"`
	NeedFinishNum        uint32            `protobuf:"varint,6,opt,name=need_finish_num,json=needFinishNum,proto3" json:"need_finish_num,omitempty"`
	GiftItemId           uint32            `protobuf:"varint,7,opt,name=gift_item_id,json=giftItemId,proto3" json:"gift_item_id,omitempty"`
	RewardNum            uint32            `protobuf:"varint,8,opt,name=reward_num,json=rewardNum,proto3" json:"reward_num,omitempty"`
	IsThisWeekTask       bool              `protobuf:"varint,9,opt,name=is_this_week_task,json=isThisWeekTask,proto3" json:"is_this_week_task,omitempty"`
	RewardTime           int64             `protobuf:"varint,10,opt,name=reward_time,json=rewardTime,proto3" json:"reward_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TaskInfo) Reset()         { *m = TaskInfo{} }
func (m *TaskInfo) String() string { return proto.CompactTextString(m) }
func (*TaskInfo) ProtoMessage()    {}
func (*TaskInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{4}
}
func (m *TaskInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskInfo.Unmarshal(m, b)
}
func (m *TaskInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskInfo.Marshal(b, m, deterministic)
}
func (dst *TaskInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskInfo.Merge(dst, src)
}
func (m *TaskInfo) XXX_Size() int {
	return xxx_messageInfo_TaskInfo.Size(m)
}
func (m *TaskInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TaskInfo proto.InternalMessageInfo

func (m *TaskInfo) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *TaskInfo) GetFragType() GloryFragmentType {
	if m != nil {
		return m.FragType
	}
	return GloryFragmentType_GloryFragmentTypeInvalid
}

func (m *TaskInfo) GetRewardType() RewardType {
	if m != nil {
		return m.RewardType
	}
	return RewardType_RewardTypeInvalid
}

func (m *TaskInfo) GetTaskType() TaskType {
	if m != nil {
		return m.TaskType
	}
	return TaskType_TaskTypeInvalid
}

func (m *TaskInfo) GetFinishNum() uint32 {
	if m != nil {
		return m.FinishNum
	}
	return 0
}

func (m *TaskInfo) GetNeedFinishNum() uint32 {
	if m != nil {
		return m.NeedFinishNum
	}
	return 0
}

func (m *TaskInfo) GetGiftItemId() uint32 {
	if m != nil {
		return m.GiftItemId
	}
	return 0
}

func (m *TaskInfo) GetRewardNum() uint32 {
	if m != nil {
		return m.RewardNum
	}
	return 0
}

func (m *TaskInfo) GetIsThisWeekTask() bool {
	if m != nil {
		return m.IsThisWeekTask
	}
	return false
}

func (m *TaskInfo) GetRewardTime() int64 {
	if m != nil {
		return m.RewardTime
	}
	return 0
}

type GetAllTaskListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllTaskListReq) Reset()         { *m = GetAllTaskListReq{} }
func (m *GetAllTaskListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllTaskListReq) ProtoMessage()    {}
func (*GetAllTaskListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{5}
}
func (m *GetAllTaskListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTaskListReq.Unmarshal(m, b)
}
func (m *GetAllTaskListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTaskListReq.Marshal(b, m, deterministic)
}
func (dst *GetAllTaskListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTaskListReq.Merge(dst, src)
}
func (m *GetAllTaskListReq) XXX_Size() int {
	return xxx_messageInfo_GetAllTaskListReq.Size(m)
}
func (m *GetAllTaskListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTaskListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTaskListReq proto.InternalMessageInfo

func (m *GetAllTaskListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAllTaskListResp struct {
	TaskList             []*TaskInfo `protobuf:"bytes,1,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	WeekTaskList         []*TaskInfo `protobuf:"bytes,2,rep,name=week_task_list,json=weekTaskList,proto3" json:"week_task_list,omitempty"`
	GiftTaskList         []*TaskInfo `protobuf:"bytes,3,rep,name=gift_task_list,json=giftTaskList,proto3" json:"gift_task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAllTaskListResp) Reset()         { *m = GetAllTaskListResp{} }
func (m *GetAllTaskListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllTaskListResp) ProtoMessage()    {}
func (*GetAllTaskListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{6}
}
func (m *GetAllTaskListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTaskListResp.Unmarshal(m, b)
}
func (m *GetAllTaskListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTaskListResp.Marshal(b, m, deterministic)
}
func (dst *GetAllTaskListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTaskListResp.Merge(dst, src)
}
func (m *GetAllTaskListResp) XXX_Size() int {
	return xxx_messageInfo_GetAllTaskListResp.Size(m)
}
func (m *GetAllTaskListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTaskListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTaskListResp proto.InternalMessageInfo

func (m *GetAllTaskListResp) GetTaskList() []*TaskInfo {
	if m != nil {
		return m.TaskList
	}
	return nil
}

func (m *GetAllTaskListResp) GetWeekTaskList() []*TaskInfo {
	if m != nil {
		return m.WeekTaskList
	}
	return nil
}

func (m *GetAllTaskListResp) GetGiftTaskList() []*TaskInfo {
	if m != nil {
		return m.GiftTaskList
	}
	return nil
}

type GetWeekTaskListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeekTaskListReq) Reset()         { *m = GetWeekTaskListReq{} }
func (m *GetWeekTaskListReq) String() string { return proto.CompactTextString(m) }
func (*GetWeekTaskListReq) ProtoMessage()    {}
func (*GetWeekTaskListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{7}
}
func (m *GetWeekTaskListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeekTaskListReq.Unmarshal(m, b)
}
func (m *GetWeekTaskListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeekTaskListReq.Marshal(b, m, deterministic)
}
func (dst *GetWeekTaskListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeekTaskListReq.Merge(dst, src)
}
func (m *GetWeekTaskListReq) XXX_Size() int {
	return xxx_messageInfo_GetWeekTaskListReq.Size(m)
}
func (m *GetWeekTaskListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeekTaskListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeekTaskListReq proto.InternalMessageInfo

func (m *GetWeekTaskListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetWeekTaskListResp struct {
	TaskList             []*TaskInfo `protobuf:"bytes,1,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	GiftTaskList         []*TaskInfo `protobuf:"bytes,2,rep,name=gift_task_list,json=giftTaskList,proto3" json:"gift_task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetWeekTaskListResp) Reset()         { *m = GetWeekTaskListResp{} }
func (m *GetWeekTaskListResp) String() string { return proto.CompactTextString(m) }
func (*GetWeekTaskListResp) ProtoMessage()    {}
func (*GetWeekTaskListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{8}
}
func (m *GetWeekTaskListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeekTaskListResp.Unmarshal(m, b)
}
func (m *GetWeekTaskListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeekTaskListResp.Marshal(b, m, deterministic)
}
func (dst *GetWeekTaskListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeekTaskListResp.Merge(dst, src)
}
func (m *GetWeekTaskListResp) XXX_Size() int {
	return xxx_messageInfo_GetWeekTaskListResp.Size(m)
}
func (m *GetWeekTaskListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeekTaskListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeekTaskListResp proto.InternalMessageInfo

func (m *GetWeekTaskListResp) GetTaskList() []*TaskInfo {
	if m != nil {
		return m.TaskList
	}
	return nil
}

func (m *GetWeekTaskListResp) GetGiftTaskList() []*TaskInfo {
	if m != nil {
		return m.GiftTaskList
	}
	return nil
}

// TaskInfo 获取奖励的任务信息
type FragmentRewardInfo struct {
	OrderId              string            `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	FragType             GloryFragmentType `protobuf:"varint,2,opt,name=frag_type,json=fragType,proto3,enum=glory_reward.GloryFragmentType" json:"frag_type,omitempty"`
	RewardType           RewardType        `protobuf:"varint,3,opt,name=reward_type,json=rewardType,proto3,enum=glory_reward.RewardType" json:"reward_type,omitempty"`
	TaskType             TaskType          `protobuf:"varint,4,opt,name=task_type,json=taskType,proto3,enum=glory_reward.TaskType" json:"task_type,omitempty"`
	Remark               string            `protobuf:"bytes,5,opt,name=remark,proto3" json:"remark,omitempty"`
	GiftId               uint32            `protobuf:"varint,6,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Count                uint32            `protobuf:"varint,7,opt,name=count,proto3" json:"count,omitempty"`
	RewardNum            uint32            `protobuf:"varint,8,opt,name=reward_num,json=rewardNum,proto3" json:"reward_num,omitempty"`
	RewardTime           int64             `protobuf:"varint,9,opt,name=reward_time,json=rewardTime,proto3" json:"reward_time,omitempty"`
	ExpireTime           int64             `protobuf:"varint,10,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	FragNum              uint32            `protobuf:"varint,11,opt,name=frag_num,json=fragNum,proto3" json:"frag_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *FragmentRewardInfo) Reset()         { *m = FragmentRewardInfo{} }
func (m *FragmentRewardInfo) String() string { return proto.CompactTextString(m) }
func (*FragmentRewardInfo) ProtoMessage()    {}
func (*FragmentRewardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{9}
}
func (m *FragmentRewardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FragmentRewardInfo.Unmarshal(m, b)
}
func (m *FragmentRewardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FragmentRewardInfo.Marshal(b, m, deterministic)
}
func (dst *FragmentRewardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FragmentRewardInfo.Merge(dst, src)
}
func (m *FragmentRewardInfo) XXX_Size() int {
	return xxx_messageInfo_FragmentRewardInfo.Size(m)
}
func (m *FragmentRewardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FragmentRewardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FragmentRewardInfo proto.InternalMessageInfo

func (m *FragmentRewardInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *FragmentRewardInfo) GetFragType() GloryFragmentType {
	if m != nil {
		return m.FragType
	}
	return GloryFragmentType_GloryFragmentTypeInvalid
}

func (m *FragmentRewardInfo) GetRewardType() RewardType {
	if m != nil {
		return m.RewardType
	}
	return RewardType_RewardTypeInvalid
}

func (m *FragmentRewardInfo) GetTaskType() TaskType {
	if m != nil {
		return m.TaskType
	}
	return TaskType_TaskTypeInvalid
}

func (m *FragmentRewardInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *FragmentRewardInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *FragmentRewardInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *FragmentRewardInfo) GetRewardNum() uint32 {
	if m != nil {
		return m.RewardNum
	}
	return 0
}

func (m *FragmentRewardInfo) GetRewardTime() int64 {
	if m != nil {
		return m.RewardTime
	}
	return 0
}

func (m *FragmentRewardInfo) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *FragmentRewardInfo) GetFragNum() uint32 {
	if m != nil {
		return m.FragNum
	}
	return 0
}

type GetFragmentDetailReq struct {
	Uid                  uint32             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32             `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32             `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	FragRewardType       FragmentRewardType `protobuf:"varint,4,opt,name=frag_reward_type,json=fragRewardType,proto3,enum=glory_reward.FragmentRewardType" json:"frag_reward_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetFragmentDetailReq) Reset()         { *m = GetFragmentDetailReq{} }
func (m *GetFragmentDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetFragmentDetailReq) ProtoMessage()    {}
func (*GetFragmentDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{10}
}
func (m *GetFragmentDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFragmentDetailReq.Unmarshal(m, b)
}
func (m *GetFragmentDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFragmentDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetFragmentDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFragmentDetailReq.Merge(dst, src)
}
func (m *GetFragmentDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetFragmentDetailReq.Size(m)
}
func (m *GetFragmentDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFragmentDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFragmentDetailReq proto.InternalMessageInfo

func (m *GetFragmentDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFragmentDetailReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetFragmentDetailReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetFragmentDetailReq) GetFragRewardType() FragmentRewardType {
	if m != nil {
		return m.FragRewardType
	}
	return FragmentRewardType_FragmentRewardTypeInvalid
}

type GetFragmentDetailResp struct {
	Details              []*FragmentRewardInfo `protobuf:"bytes,1,rep,name=details,proto3" json:"details,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetFragmentDetailResp) Reset()         { *m = GetFragmentDetailResp{} }
func (m *GetFragmentDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetFragmentDetailResp) ProtoMessage()    {}
func (*GetFragmentDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{11}
}
func (m *GetFragmentDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFragmentDetailResp.Unmarshal(m, b)
}
func (m *GetFragmentDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFragmentDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetFragmentDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFragmentDetailResp.Merge(dst, src)
}
func (m *GetFragmentDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetFragmentDetailResp.Size(m)
}
func (m *GetFragmentDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFragmentDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFragmentDetailResp proto.InternalMessageInfo

func (m *GetFragmentDetailResp) GetDetails() []*FragmentRewardInfo {
	if m != nil {
		return m.Details
	}
	return nil
}

type GetNoticeStatusReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNoticeStatusReq) Reset()         { *m = GetNoticeStatusReq{} }
func (m *GetNoticeStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetNoticeStatusReq) ProtoMessage()    {}
func (*GetNoticeStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{12}
}
func (m *GetNoticeStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNoticeStatusReq.Unmarshal(m, b)
}
func (m *GetNoticeStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNoticeStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetNoticeStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNoticeStatusReq.Merge(dst, src)
}
func (m *GetNoticeStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetNoticeStatusReq.Size(m)
}
func (m *GetNoticeStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNoticeStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNoticeStatusReq proto.InternalMessageInfo

type GetNoticeStatusResp struct {
	ChannelStatus        bool     `protobuf:"varint,1,opt,name=channel_status,json=channelStatus,proto3" json:"channel_status,omitempty"`
	HeaderStatus         bool     `protobuf:"varint,2,opt,name=header_status,json=headerStatus,proto3" json:"header_status,omitempty"`
	ChallengeStatus      bool     `protobuf:"varint,3,opt,name=challenge_status,json=challengeStatus,proto3" json:"challenge_status,omitempty"`
	HistoryTaskStatus    bool     `protobuf:"varint,4,opt,name=history_task_status,json=historyTaskStatus,proto3" json:"history_task_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNoticeStatusResp) Reset()         { *m = GetNoticeStatusResp{} }
func (m *GetNoticeStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetNoticeStatusResp) ProtoMessage()    {}
func (*GetNoticeStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{13}
}
func (m *GetNoticeStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNoticeStatusResp.Unmarshal(m, b)
}
func (m *GetNoticeStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNoticeStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetNoticeStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNoticeStatusResp.Merge(dst, src)
}
func (m *GetNoticeStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetNoticeStatusResp.Size(m)
}
func (m *GetNoticeStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNoticeStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNoticeStatusResp proto.InternalMessageInfo

func (m *GetNoticeStatusResp) GetChannelStatus() bool {
	if m != nil {
		return m.ChannelStatus
	}
	return false
}

func (m *GetNoticeStatusResp) GetHeaderStatus() bool {
	if m != nil {
		return m.HeaderStatus
	}
	return false
}

func (m *GetNoticeStatusResp) GetChallengeStatus() bool {
	if m != nil {
		return m.ChallengeStatus
	}
	return false
}

func (m *GetNoticeStatusResp) GetHistoryTaskStatus() bool {
	if m != nil {
		return m.HistoryTaskStatus
	}
	return false
}

type SetNoticeStatusReq struct {
	Type                 NoticeType `protobuf:"varint,1,opt,name=type,proto3,enum=glory_reward.NoticeType" json:"type,omitempty"`
	IsOpen               bool       `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SetNoticeStatusReq) Reset()         { *m = SetNoticeStatusReq{} }
func (m *SetNoticeStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetNoticeStatusReq) ProtoMessage()    {}
func (*SetNoticeStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{14}
}
func (m *SetNoticeStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNoticeStatusReq.Unmarshal(m, b)
}
func (m *SetNoticeStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNoticeStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetNoticeStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNoticeStatusReq.Merge(dst, src)
}
func (m *SetNoticeStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetNoticeStatusReq.Size(m)
}
func (m *SetNoticeStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNoticeStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetNoticeStatusReq proto.InternalMessageInfo

func (m *SetNoticeStatusReq) GetType() NoticeType {
	if m != nil {
		return m.Type
	}
	return NoticeType_NoticeTypeInvalid
}

func (m *SetNoticeStatusReq) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetNoticeStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNoticeStatusResp) Reset()         { *m = SetNoticeStatusResp{} }
func (m *SetNoticeStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetNoticeStatusResp) ProtoMessage()    {}
func (*SetNoticeStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{15}
}
func (m *SetNoticeStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNoticeStatusResp.Unmarshal(m, b)
}
func (m *SetNoticeStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNoticeStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetNoticeStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNoticeStatusResp.Merge(dst, src)
}
func (m *SetNoticeStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetNoticeStatusResp.Size(m)
}
func (m *SetNoticeStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNoticeStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetNoticeStatusResp proto.InternalMessageInfo

type GetEnterStatusReq struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Os                   uint32   `protobuf:"varint,2,opt,name=os,proto3" json:"os,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	IsGray               bool     `protobuf:"varint,4,opt,name=is_gray,json=isGray,proto3" json:"is_gray,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEnterStatusReq) Reset()         { *m = GetEnterStatusReq{} }
func (m *GetEnterStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetEnterStatusReq) ProtoMessage()    {}
func (*GetEnterStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{16}
}
func (m *GetEnterStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEnterStatusReq.Unmarshal(m, b)
}
func (m *GetEnterStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEnterStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetEnterStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEnterStatusReq.Merge(dst, src)
}
func (m *GetEnterStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetEnterStatusReq.Size(m)
}
func (m *GetEnterStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEnterStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEnterStatusReq proto.InternalMessageInfo

func (m *GetEnterStatusReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetEnterStatusReq) GetOs() uint32 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *GetEnterStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetEnterStatusReq) GetIsGray() bool {
	if m != nil {
		return m.IsGray
	}
	return false
}

type GetEnterStatusResp struct {
	IsOpen               bool     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEnterStatusResp) Reset()         { *m = GetEnterStatusResp{} }
func (m *GetEnterStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetEnterStatusResp) ProtoMessage()    {}
func (*GetEnterStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{17}
}
func (m *GetEnterStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEnterStatusResp.Unmarshal(m, b)
}
func (m *GetEnterStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEnterStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetEnterStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEnterStatusResp.Merge(dst, src)
}
func (m *GetEnterStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetEnterStatusResp.Size(m)
}
func (m *GetEnterStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEnterStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEnterStatusResp proto.InternalMessageInfo

func (m *GetEnterStatusResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *GetEnterStatusResp) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type AddFragmentReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FragType             GloryFragmentType `protobuf:"varint,2,opt,name=frag_type,json=fragType,proto3,enum=glory_reward.GloryFragmentType" json:"frag_type,omitempty"`
	Num                  uint32            `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddFragmentReq) Reset()         { *m = AddFragmentReq{} }
func (m *AddFragmentReq) String() string { return proto.CompactTextString(m) }
func (*AddFragmentReq) ProtoMessage()    {}
func (*AddFragmentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{18}
}
func (m *AddFragmentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFragmentReq.Unmarshal(m, b)
}
func (m *AddFragmentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFragmentReq.Marshal(b, m, deterministic)
}
func (dst *AddFragmentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFragmentReq.Merge(dst, src)
}
func (m *AddFragmentReq) XXX_Size() int {
	return xxx_messageInfo_AddFragmentReq.Size(m)
}
func (m *AddFragmentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFragmentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFragmentReq proto.InternalMessageInfo

func (m *AddFragmentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddFragmentReq) GetFragType() GloryFragmentType {
	if m != nil {
		return m.FragType
	}
	return GloryFragmentType_GloryFragmentTypeInvalid
}

func (m *AddFragmentReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type AddFragmentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFragmentResp) Reset()         { *m = AddFragmentResp{} }
func (m *AddFragmentResp) String() string { return proto.CompactTextString(m) }
func (*AddFragmentResp) ProtoMessage()    {}
func (*AddFragmentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{19}
}
func (m *AddFragmentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFragmentResp.Unmarshal(m, b)
}
func (m *AddFragmentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFragmentResp.Marshal(b, m, deterministic)
}
func (dst *AddFragmentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFragmentResp.Merge(dst, src)
}
func (m *AddFragmentResp) XXX_Size() int {
	return xxx_messageInfo_AddFragmentResp.Size(m)
}
func (m *AddFragmentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFragmentResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddFragmentResp proto.InternalMessageInfo

type PushTaskPopWindowReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FragType             []RewardType `protobuf:"varint,3,rep,packed,name=frag_type,json=fragType,proto3,enum=glory_reward.RewardType" json:"frag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PushTaskPopWindowReq) Reset()         { *m = PushTaskPopWindowReq{} }
func (m *PushTaskPopWindowReq) String() string { return proto.CompactTextString(m) }
func (*PushTaskPopWindowReq) ProtoMessage()    {}
func (*PushTaskPopWindowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{20}
}
func (m *PushTaskPopWindowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushTaskPopWindowReq.Unmarshal(m, b)
}
func (m *PushTaskPopWindowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushTaskPopWindowReq.Marshal(b, m, deterministic)
}
func (dst *PushTaskPopWindowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushTaskPopWindowReq.Merge(dst, src)
}
func (m *PushTaskPopWindowReq) XXX_Size() int {
	return xxx_messageInfo_PushTaskPopWindowReq.Size(m)
}
func (m *PushTaskPopWindowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushTaskPopWindowReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushTaskPopWindowReq proto.InternalMessageInfo

func (m *PushTaskPopWindowReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PushTaskPopWindowReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PushTaskPopWindowReq) GetFragType() []RewardType {
	if m != nil {
		return m.FragType
	}
	return nil
}

type PushTaskPopWindowResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushTaskPopWindowResp) Reset()         { *m = PushTaskPopWindowResp{} }
func (m *PushTaskPopWindowResp) String() string { return proto.CompactTextString(m) }
func (*PushTaskPopWindowResp) ProtoMessage()    {}
func (*PushTaskPopWindowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{21}
}
func (m *PushTaskPopWindowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushTaskPopWindowResp.Unmarshal(m, b)
}
func (m *PushTaskPopWindowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushTaskPopWindowResp.Marshal(b, m, deterministic)
}
func (dst *PushTaskPopWindowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushTaskPopWindowResp.Merge(dst, src)
}
func (m *PushTaskPopWindowResp) XXX_Size() int {
	return xxx_messageInfo_PushTaskPopWindowResp.Size(m)
}
func (m *PushTaskPopWindowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushTaskPopWindowResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushTaskPopWindowResp proto.InternalMessageInfo

// 浮层管理及入口提醒红点配置
type FloatingLayerConfig struct {
	NoticeStartTime      int64               `protobuf:"varint,1,opt,name=notice_start_time,json=noticeStartTime,proto3" json:"notice_start_time,omitempty"`
	NoticeEndTime        int64               `protobuf:"varint,2,opt,name=notice_end_time,json=noticeEndTime,proto3" json:"notice_end_time,omitempty"`
	NoticeSwitch         FloatingLayerSwitch `protobuf:"varint,3,opt,name=notice_switch,json=noticeSwitch,proto3,enum=glory_reward.FloatingLayerSwitch" json:"notice_switch,omitempty"`
	NoticeContent        string              `protobuf:"bytes,4,opt,name=notice_content,json=noticeContent,proto3" json:"notice_content,omitempty"`
	NoticeShowTime       uint32              `protobuf:"varint,5,opt,name=notice_show_time,json=noticeShowTime,proto3" json:"notice_show_time,omitempty"`
	RedPointSwitch       FloatingLayerSwitch `protobuf:"varint,6,opt,name=red_point_switch,json=redPointSwitch,proto3,enum=glory_reward.FloatingLayerSwitch" json:"red_point_switch,omitempty"`
	Version              string              `protobuf:"bytes,7,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *FloatingLayerConfig) Reset()         { *m = FloatingLayerConfig{} }
func (m *FloatingLayerConfig) String() string { return proto.CompactTextString(m) }
func (*FloatingLayerConfig) ProtoMessage()    {}
func (*FloatingLayerConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{22}
}
func (m *FloatingLayerConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FloatingLayerConfig.Unmarshal(m, b)
}
func (m *FloatingLayerConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FloatingLayerConfig.Marshal(b, m, deterministic)
}
func (dst *FloatingLayerConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FloatingLayerConfig.Merge(dst, src)
}
func (m *FloatingLayerConfig) XXX_Size() int {
	return xxx_messageInfo_FloatingLayerConfig.Size(m)
}
func (m *FloatingLayerConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_FloatingLayerConfig.DiscardUnknown(m)
}

var xxx_messageInfo_FloatingLayerConfig proto.InternalMessageInfo

func (m *FloatingLayerConfig) GetNoticeStartTime() int64 {
	if m != nil {
		return m.NoticeStartTime
	}
	return 0
}

func (m *FloatingLayerConfig) GetNoticeEndTime() int64 {
	if m != nil {
		return m.NoticeEndTime
	}
	return 0
}

func (m *FloatingLayerConfig) GetNoticeSwitch() FloatingLayerSwitch {
	if m != nil {
		return m.NoticeSwitch
	}
	return FloatingLayerSwitch_FloatingLayerSwitchInvalid
}

func (m *FloatingLayerConfig) GetNoticeContent() string {
	if m != nil {
		return m.NoticeContent
	}
	return ""
}

func (m *FloatingLayerConfig) GetNoticeShowTime() uint32 {
	if m != nil {
		return m.NoticeShowTime
	}
	return 0
}

func (m *FloatingLayerConfig) GetRedPointSwitch() FloatingLayerSwitch {
	if m != nil {
		return m.RedPointSwitch
	}
	return FloatingLayerSwitch_FloatingLayerSwitchInvalid
}

func (m *FloatingLayerConfig) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type GetFloatingLayerConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFloatingLayerConfigReq) Reset()         { *m = GetFloatingLayerConfigReq{} }
func (m *GetFloatingLayerConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetFloatingLayerConfigReq) ProtoMessage()    {}
func (*GetFloatingLayerConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{23}
}
func (m *GetFloatingLayerConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFloatingLayerConfigReq.Unmarshal(m, b)
}
func (m *GetFloatingLayerConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFloatingLayerConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetFloatingLayerConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFloatingLayerConfigReq.Merge(dst, src)
}
func (m *GetFloatingLayerConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetFloatingLayerConfigReq.Size(m)
}
func (m *GetFloatingLayerConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFloatingLayerConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFloatingLayerConfigReq proto.InternalMessageInfo

type GetFloatingLayerConfigResp struct {
	FloatingLayerConfig  *FloatingLayerConfig `protobuf:"bytes,1,opt,name=floating_layer_config,json=floatingLayerConfig,proto3" json:"floating_layer_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetFloatingLayerConfigResp) Reset()         { *m = GetFloatingLayerConfigResp{} }
func (m *GetFloatingLayerConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetFloatingLayerConfigResp) ProtoMessage()    {}
func (*GetFloatingLayerConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{24}
}
func (m *GetFloatingLayerConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFloatingLayerConfigResp.Unmarshal(m, b)
}
func (m *GetFloatingLayerConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFloatingLayerConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetFloatingLayerConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFloatingLayerConfigResp.Merge(dst, src)
}
func (m *GetFloatingLayerConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetFloatingLayerConfigResp.Size(m)
}
func (m *GetFloatingLayerConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFloatingLayerConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFloatingLayerConfigResp proto.InternalMessageInfo

func (m *GetFloatingLayerConfigResp) GetFloatingLayerConfig() *FloatingLayerConfig {
	if m != nil {
		return m.FloatingLayerConfig
	}
	return nil
}

type SetFloatingLayerConfigReq struct {
	FloatingLayerConfig  *FloatingLayerConfig `protobuf:"bytes,1,opt,name=floating_layer_config,json=floatingLayerConfig,proto3" json:"floating_layer_config,omitempty"`
	Operator             string               `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SetFloatingLayerConfigReq) Reset()         { *m = SetFloatingLayerConfigReq{} }
func (m *SetFloatingLayerConfigReq) String() string { return proto.CompactTextString(m) }
func (*SetFloatingLayerConfigReq) ProtoMessage()    {}
func (*SetFloatingLayerConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{25}
}
func (m *SetFloatingLayerConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFloatingLayerConfigReq.Unmarshal(m, b)
}
func (m *SetFloatingLayerConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFloatingLayerConfigReq.Marshal(b, m, deterministic)
}
func (dst *SetFloatingLayerConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFloatingLayerConfigReq.Merge(dst, src)
}
func (m *SetFloatingLayerConfigReq) XXX_Size() int {
	return xxx_messageInfo_SetFloatingLayerConfigReq.Size(m)
}
func (m *SetFloatingLayerConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFloatingLayerConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetFloatingLayerConfigReq proto.InternalMessageInfo

func (m *SetFloatingLayerConfigReq) GetFloatingLayerConfig() *FloatingLayerConfig {
	if m != nil {
		return m.FloatingLayerConfig
	}
	return nil
}

func (m *SetFloatingLayerConfigReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SetFloatingLayerConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetFloatingLayerConfigResp) Reset()         { *m = SetFloatingLayerConfigResp{} }
func (m *SetFloatingLayerConfigResp) String() string { return proto.CompactTextString(m) }
func (*SetFloatingLayerConfigResp) ProtoMessage()    {}
func (*SetFloatingLayerConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{26}
}
func (m *SetFloatingLayerConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFloatingLayerConfigResp.Unmarshal(m, b)
}
func (m *SetFloatingLayerConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFloatingLayerConfigResp.Marshal(b, m, deterministic)
}
func (dst *SetFloatingLayerConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFloatingLayerConfigResp.Merge(dst, src)
}
func (m *SetFloatingLayerConfigResp) XXX_Size() int {
	return xxx_messageInfo_SetFloatingLayerConfigResp.Size(m)
}
func (m *SetFloatingLayerConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFloatingLayerConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetFloatingLayerConfigResp proto.InternalMessageInfo

type GetWhiteListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteListReq) Reset()         { *m = GetWhiteListReq{} }
func (m *GetWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListReq) ProtoMessage()    {}
func (*GetWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{27}
}
func (m *GetWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListReq.Unmarshal(m, b)
}
func (m *GetWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListReq.Merge(dst, src)
}
func (m *GetWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListReq.Size(m)
}
func (m *GetWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListReq proto.InternalMessageInfo

type GetWhiteListResp struct {
	WhiteList            []uint32 `protobuf:"varint,1,rep,packed,name=white_list,json=whiteList,proto3" json:"white_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteListResp) Reset()         { *m = GetWhiteListResp{} }
func (m *GetWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListResp) ProtoMessage()    {}
func (*GetWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{28}
}
func (m *GetWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListResp.Unmarshal(m, b)
}
func (m *GetWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListResp.Merge(dst, src)
}
func (m *GetWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListResp.Size(m)
}
func (m *GetWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListResp proto.InternalMessageInfo

func (m *GetWhiteListResp) GetWhiteList() []uint32 {
	if m != nil {
		return m.WhiteList
	}
	return nil
}

// ---------------------------------------------------------
// 管理后台接口
// 添加白名单
type AddWhiteListReq struct {
	ItemIdList           []uint32 `protobuf:"varint,1,rep,packed,name=item_id_list,json=itemIdList,proto3" json:"item_id_list,omitempty"`
	Operator             string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddWhiteListReq) Reset()         { *m = AddWhiteListReq{} }
func (m *AddWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*AddWhiteListReq) ProtoMessage()    {}
func (*AddWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{29}
}
func (m *AddWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddWhiteListReq.Unmarshal(m, b)
}
func (m *AddWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *AddWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddWhiteListReq.Merge(dst, src)
}
func (m *AddWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_AddWhiteListReq.Size(m)
}
func (m *AddWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddWhiteListReq proto.InternalMessageInfo

func (m *AddWhiteListReq) GetItemIdList() []uint32 {
	if m != nil {
		return m.ItemIdList
	}
	return nil
}

func (m *AddWhiteListReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type AddWhiteListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddWhiteListResp) Reset()         { *m = AddWhiteListResp{} }
func (m *AddWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*AddWhiteListResp) ProtoMessage()    {}
func (*AddWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{30}
}
func (m *AddWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddWhiteListResp.Unmarshal(m, b)
}
func (m *AddWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *AddWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddWhiteListResp.Merge(dst, src)
}
func (m *AddWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_AddWhiteListResp.Size(m)
}
func (m *AddWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddWhiteListResp proto.InternalMessageInfo

// 删除白名单
type DeleteWhiteListReq struct {
	ItemIdList           []uint32 `protobuf:"varint,1,rep,packed,name=item_id_list,json=itemIdList,proto3" json:"item_id_list,omitempty"`
	Operator             string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteWhiteListReq) Reset()         { *m = DeleteWhiteListReq{} }
func (m *DeleteWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*DeleteWhiteListReq) ProtoMessage()    {}
func (*DeleteWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{31}
}
func (m *DeleteWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteWhiteListReq.Unmarshal(m, b)
}
func (m *DeleteWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *DeleteWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteWhiteListReq.Merge(dst, src)
}
func (m *DeleteWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_DeleteWhiteListReq.Size(m)
}
func (m *DeleteWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteWhiteListReq proto.InternalMessageInfo

func (m *DeleteWhiteListReq) GetItemIdList() []uint32 {
	if m != nil {
		return m.ItemIdList
	}
	return nil
}

func (m *DeleteWhiteListReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type DeleteWhiteListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteWhiteListResp) Reset()         { *m = DeleteWhiteListResp{} }
func (m *DeleteWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*DeleteWhiteListResp) ProtoMessage()    {}
func (*DeleteWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{32}
}
func (m *DeleteWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteWhiteListResp.Unmarshal(m, b)
}
func (m *DeleteWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *DeleteWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteWhiteListResp.Merge(dst, src)
}
func (m *DeleteWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_DeleteWhiteListResp.Size(m)
}
func (m *DeleteWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteWhiteListResp proto.InternalMessageInfo

// 获取当前白名单礼物配置列表
type GetWhiteListGiftConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteListGiftConfigReq) Reset()         { *m = GetWhiteListGiftConfigReq{} }
func (m *GetWhiteListGiftConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListGiftConfigReq) ProtoMessage()    {}
func (*GetWhiteListGiftConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{33}
}
func (m *GetWhiteListGiftConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListGiftConfigReq.Unmarshal(m, b)
}
func (m *GetWhiteListGiftConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListGiftConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListGiftConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListGiftConfigReq.Merge(dst, src)
}
func (m *GetWhiteListGiftConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListGiftConfigReq.Size(m)
}
func (m *GetWhiteListGiftConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListGiftConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListGiftConfigReq proto.InternalMessageInfo

type GetWhiteListGiftConfigResp struct {
	ItemList             []*PresentItemConfig `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetWhiteListGiftConfigResp) Reset()         { *m = GetWhiteListGiftConfigResp{} }
func (m *GetWhiteListGiftConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetWhiteListGiftConfigResp) ProtoMessage()    {}
func (*GetWhiteListGiftConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{34}
}
func (m *GetWhiteListGiftConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteListGiftConfigResp.Unmarshal(m, b)
}
func (m *GetWhiteListGiftConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteListGiftConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetWhiteListGiftConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteListGiftConfigResp.Merge(dst, src)
}
func (m *GetWhiteListGiftConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetWhiteListGiftConfigResp.Size(m)
}
func (m *GetWhiteListGiftConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteListGiftConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteListGiftConfigResp proto.InternalMessageInfo

func (m *GetWhiteListGiftConfigResp) GetItemList() []*PresentItemConfig {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 礼物配置信息
type PresentItemConfig struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string   `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,5,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,6,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           uint32   `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	IsDel                bool     `protobuf:"varint,9,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentItemConfig) Reset()         { *m = PresentItemConfig{} }
func (m *PresentItemConfig) String() string { return proto.CompactTextString(m) }
func (*PresentItemConfig) ProtoMessage()    {}
func (*PresentItemConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_reward_18a107700e2bdc1f, []int{35}
}
func (m *PresentItemConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentItemConfig.Unmarshal(m, b)
}
func (m *PresentItemConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentItemConfig.Marshal(b, m, deterministic)
}
func (dst *PresentItemConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentItemConfig.Merge(dst, src)
}
func (m *PresentItemConfig) XXX_Size() int {
	return xxx_messageInfo_PresentItemConfig.Size(m)
}
func (m *PresentItemConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentItemConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentItemConfig proto.InternalMessageInfo

func (m *PresentItemConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentItemConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PresentItemConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *PresentItemConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PresentItemConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *PresentItemConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *PresentItemConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *PresentItemConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PresentItemConfig) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func init() {
	proto.RegisterType((*IsNeedNoticeReq)(nil), "glory_reward.IsNeedNoticeReq")
	proto.RegisterType((*IsNeedNoticeResp)(nil), "glory_reward.IsNeedNoticeResp")
	proto.RegisterType((*ReceiveRewardReq)(nil), "glory_reward.ReceiveRewardReq")
	proto.RegisterType((*ReceiveRewardResp)(nil), "glory_reward.ReceiveRewardResp")
	proto.RegisterType((*TaskInfo)(nil), "glory_reward.TaskInfo")
	proto.RegisterType((*GetAllTaskListReq)(nil), "glory_reward.GetAllTaskListReq")
	proto.RegisterType((*GetAllTaskListResp)(nil), "glory_reward.GetAllTaskListResp")
	proto.RegisterType((*GetWeekTaskListReq)(nil), "glory_reward.GetWeekTaskListReq")
	proto.RegisterType((*GetWeekTaskListResp)(nil), "glory_reward.GetWeekTaskListResp")
	proto.RegisterType((*FragmentRewardInfo)(nil), "glory_reward.FragmentRewardInfo")
	proto.RegisterType((*GetFragmentDetailReq)(nil), "glory_reward.GetFragmentDetailReq")
	proto.RegisterType((*GetFragmentDetailResp)(nil), "glory_reward.GetFragmentDetailResp")
	proto.RegisterType((*GetNoticeStatusReq)(nil), "glory_reward.GetNoticeStatusReq")
	proto.RegisterType((*GetNoticeStatusResp)(nil), "glory_reward.GetNoticeStatusResp")
	proto.RegisterType((*SetNoticeStatusReq)(nil), "glory_reward.SetNoticeStatusReq")
	proto.RegisterType((*SetNoticeStatusResp)(nil), "glory_reward.SetNoticeStatusResp")
	proto.RegisterType((*GetEnterStatusReq)(nil), "glory_reward.GetEnterStatusReq")
	proto.RegisterType((*GetEnterStatusResp)(nil), "glory_reward.GetEnterStatusResp")
	proto.RegisterType((*AddFragmentReq)(nil), "glory_reward.AddFragmentReq")
	proto.RegisterType((*AddFragmentResp)(nil), "glory_reward.AddFragmentResp")
	proto.RegisterType((*PushTaskPopWindowReq)(nil), "glory_reward.PushTaskPopWindowReq")
	proto.RegisterType((*PushTaskPopWindowResp)(nil), "glory_reward.PushTaskPopWindowResp")
	proto.RegisterType((*FloatingLayerConfig)(nil), "glory_reward.FloatingLayerConfig")
	proto.RegisterType((*GetFloatingLayerConfigReq)(nil), "glory_reward.GetFloatingLayerConfigReq")
	proto.RegisterType((*GetFloatingLayerConfigResp)(nil), "glory_reward.GetFloatingLayerConfigResp")
	proto.RegisterType((*SetFloatingLayerConfigReq)(nil), "glory_reward.SetFloatingLayerConfigReq")
	proto.RegisterType((*SetFloatingLayerConfigResp)(nil), "glory_reward.SetFloatingLayerConfigResp")
	proto.RegisterType((*GetWhiteListReq)(nil), "glory_reward.GetWhiteListReq")
	proto.RegisterType((*GetWhiteListResp)(nil), "glory_reward.GetWhiteListResp")
	proto.RegisterType((*AddWhiteListReq)(nil), "glory_reward.AddWhiteListReq")
	proto.RegisterType((*AddWhiteListResp)(nil), "glory_reward.AddWhiteListResp")
	proto.RegisterType((*DeleteWhiteListReq)(nil), "glory_reward.DeleteWhiteListReq")
	proto.RegisterType((*DeleteWhiteListResp)(nil), "glory_reward.DeleteWhiteListResp")
	proto.RegisterType((*GetWhiteListGiftConfigReq)(nil), "glory_reward.GetWhiteListGiftConfigReq")
	proto.RegisterType((*GetWhiteListGiftConfigResp)(nil), "glory_reward.GetWhiteListGiftConfigResp")
	proto.RegisterType((*PresentItemConfig)(nil), "glory_reward.PresentItemConfig")
	proto.RegisterEnum("glory_reward.FragmentRewardType", FragmentRewardType_name, FragmentRewardType_value)
	proto.RegisterEnum("glory_reward.NoticeType", NoticeType_name, NoticeType_value)
	proto.RegisterEnum("glory_reward.GloryFragmentType", GloryFragmentType_name, GloryFragmentType_value)
	proto.RegisterEnum("glory_reward.RewardType", RewardType_name, RewardType_value)
	proto.RegisterEnum("glory_reward.TaskType", TaskType_name, TaskType_value)
	proto.RegisterEnum("glory_reward.FloatingLayerSwitch", FloatingLayerSwitch_name, FloatingLayerSwitch_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GloryWorldRewardClient is the client API for GloryWorldReward service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GloryWorldRewardClient interface {
	// 是否需要红点提醒
	IsNeedNotice(ctx context.Context, in *IsNeedNoticeReq, opts ...grpc.CallOption) (*IsNeedNoticeResp, error)
	// 领取任务奖励
	ReceiveReward(ctx context.Context, in *ReceiveRewardReq, opts ...grpc.CallOption) (*ReceiveRewardResp, error)
	// 获取完成待领取任务列表
	GetAllTaskList(ctx context.Context, in *GetAllTaskListReq, opts ...grpc.CallOption) (*GetAllTaskListResp, error)
	// 获取周挑战任务列表
	GetWeekTaskList(ctx context.Context, in *GetWeekTaskListReq, opts ...grpc.CallOption) (*GetWeekTaskListResp, error)
	// 获取碎片获取明细
	GetFragmentDetail(ctx context.Context, in *GetFragmentDetailReq, opts ...grpc.CallOption) (*GetFragmentDetailResp, error)
	// 查询红点提醒开启状态
	GetNoticeStatus(ctx context.Context, in *GetNoticeStatusReq, opts ...grpc.CallOption) (*GetNoticeStatusResp, error)
	// 设置红点提醒状态
	SetNoticeStatus(ctx context.Context, in *SetNoticeStatusReq, opts ...grpc.CallOption) (*SetNoticeStatusResp, error)
	// 获取入口状态
	GetEnterStatus(ctx context.Context, in *GetEnterStatusReq, opts ...grpc.CallOption) (*GetEnterStatusResp, error)
	// 订单回调
	// 查询时间范围内的订单总数和价值总数
	CountOrderNumAndValueByTime(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 查询时间范围内的OrderIds
	GetOrderIdsByTime(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 补单接口
	ReSendPkg(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 查询时间范围内礼物订单总数和价值总数
	CountPresentOrderNumAndValueByTime(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 查询时间范围内的礼物OrderIds
	GetPresentOrderIdsByTime(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 礼物补单接口
	PresentReSendPkg(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 测试接口
	// 手动增加星钻
	AddFragment(ctx context.Context, in *AddFragmentReq, opts ...grpc.CallOption) (*AddFragmentResp, error)
	// 手动推送弹窗
	PushTaskPopWindow(ctx context.Context, in *PushTaskPopWindowReq, opts ...grpc.CallOption) (*PushTaskPopWindowResp, error)
	// 浮层管理及入口提醒红点
	// 获取浮层管理及入口提醒红点
	GetFloatingLayerConfig(ctx context.Context, in *GetFloatingLayerConfigReq, opts ...grpc.CallOption) (*GetFloatingLayerConfigResp, error)
	// 修改浮层管理及入口提醒红点
	SetFloatingLayerConfig(ctx context.Context, in *SetFloatingLayerConfigReq, opts ...grpc.CallOption) (*SetFloatingLayerConfigResp, error)
	// 礼物白名单
	// 获取白名单列表
	GetWhiteList(ctx context.Context, in *GetWhiteListReq, opts ...grpc.CallOption) (*GetWhiteListResp, error)
	// 添加白名单
	AddWhiteList(ctx context.Context, in *AddWhiteListReq, opts ...grpc.CallOption) (*AddWhiteListResp, error)
	// 删除白名单
	DeleteWhiteList(ctx context.Context, in *DeleteWhiteListReq, opts ...grpc.CallOption) (*DeleteWhiteListResp, error)
	// 获取当前白名单礼物配置列表
	GetWhiteListGiftConfig(ctx context.Context, in *GetWhiteListGiftConfigReq, opts ...grpc.CallOption) (*GetWhiteListGiftConfigResp, error)
}

type gloryWorldRewardClient struct {
	cc *grpc.ClientConn
}

func NewGloryWorldRewardClient(cc *grpc.ClientConn) GloryWorldRewardClient {
	return &gloryWorldRewardClient{cc}
}

func (c *gloryWorldRewardClient) IsNeedNotice(ctx context.Context, in *IsNeedNoticeReq, opts ...grpc.CallOption) (*IsNeedNoticeResp, error) {
	out := new(IsNeedNoticeResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/IsNeedNotice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) ReceiveReward(ctx context.Context, in *ReceiveRewardReq, opts ...grpc.CallOption) (*ReceiveRewardResp, error) {
	out := new(ReceiveRewardResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/ReceiveReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) GetAllTaskList(ctx context.Context, in *GetAllTaskListReq, opts ...grpc.CallOption) (*GetAllTaskListResp, error) {
	out := new(GetAllTaskListResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/GetAllTaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) GetWeekTaskList(ctx context.Context, in *GetWeekTaskListReq, opts ...grpc.CallOption) (*GetWeekTaskListResp, error) {
	out := new(GetWeekTaskListResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/GetWeekTaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) GetFragmentDetail(ctx context.Context, in *GetFragmentDetailReq, opts ...grpc.CallOption) (*GetFragmentDetailResp, error) {
	out := new(GetFragmentDetailResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/GetFragmentDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) GetNoticeStatus(ctx context.Context, in *GetNoticeStatusReq, opts ...grpc.CallOption) (*GetNoticeStatusResp, error) {
	out := new(GetNoticeStatusResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/GetNoticeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) SetNoticeStatus(ctx context.Context, in *SetNoticeStatusReq, opts ...grpc.CallOption) (*SetNoticeStatusResp, error) {
	out := new(SetNoticeStatusResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/SetNoticeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) GetEnterStatus(ctx context.Context, in *GetEnterStatusReq, opts ...grpc.CallOption) (*GetEnterStatusResp, error) {
	out := new(GetEnterStatusResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/GetEnterStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) CountOrderNumAndValueByTime(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/CountOrderNumAndValueByTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) GetOrderIdsByTime(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/GetOrderIdsByTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) ReSendPkg(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/ReSendPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) CountPresentOrderNumAndValueByTime(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/CountPresentOrderNumAndValueByTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) GetPresentOrderIdsByTime(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/GetPresentOrderIdsByTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) PresentReSendPkg(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/PresentReSendPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) AddFragment(ctx context.Context, in *AddFragmentReq, opts ...grpc.CallOption) (*AddFragmentResp, error) {
	out := new(AddFragmentResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/AddFragment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) PushTaskPopWindow(ctx context.Context, in *PushTaskPopWindowReq, opts ...grpc.CallOption) (*PushTaskPopWindowResp, error) {
	out := new(PushTaskPopWindowResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/PushTaskPopWindow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) GetFloatingLayerConfig(ctx context.Context, in *GetFloatingLayerConfigReq, opts ...grpc.CallOption) (*GetFloatingLayerConfigResp, error) {
	out := new(GetFloatingLayerConfigResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/GetFloatingLayerConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) SetFloatingLayerConfig(ctx context.Context, in *SetFloatingLayerConfigReq, opts ...grpc.CallOption) (*SetFloatingLayerConfigResp, error) {
	out := new(SetFloatingLayerConfigResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/SetFloatingLayerConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) GetWhiteList(ctx context.Context, in *GetWhiteListReq, opts ...grpc.CallOption) (*GetWhiteListResp, error) {
	out := new(GetWhiteListResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/GetWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) AddWhiteList(ctx context.Context, in *AddWhiteListReq, opts ...grpc.CallOption) (*AddWhiteListResp, error) {
	out := new(AddWhiteListResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/AddWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) DeleteWhiteList(ctx context.Context, in *DeleteWhiteListReq, opts ...grpc.CallOption) (*DeleteWhiteListResp, error) {
	out := new(DeleteWhiteListResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/DeleteWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldRewardClient) GetWhiteListGiftConfig(ctx context.Context, in *GetWhiteListGiftConfigReq, opts ...grpc.CallOption) (*GetWhiteListGiftConfigResp, error) {
	out := new(GetWhiteListGiftConfigResp)
	err := c.cc.Invoke(ctx, "/glory_reward.GloryWorldReward/GetWhiteListGiftConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GloryWorldRewardServer is the server API for GloryWorldReward service.
type GloryWorldRewardServer interface {
	// 是否需要红点提醒
	IsNeedNotice(context.Context, *IsNeedNoticeReq) (*IsNeedNoticeResp, error)
	// 领取任务奖励
	ReceiveReward(context.Context, *ReceiveRewardReq) (*ReceiveRewardResp, error)
	// 获取完成待领取任务列表
	GetAllTaskList(context.Context, *GetAllTaskListReq) (*GetAllTaskListResp, error)
	// 获取周挑战任务列表
	GetWeekTaskList(context.Context, *GetWeekTaskListReq) (*GetWeekTaskListResp, error)
	// 获取碎片获取明细
	GetFragmentDetail(context.Context, *GetFragmentDetailReq) (*GetFragmentDetailResp, error)
	// 查询红点提醒开启状态
	GetNoticeStatus(context.Context, *GetNoticeStatusReq) (*GetNoticeStatusResp, error)
	// 设置红点提醒状态
	SetNoticeStatus(context.Context, *SetNoticeStatusReq) (*SetNoticeStatusResp, error)
	// 获取入口状态
	GetEnterStatus(context.Context, *GetEnterStatusReq) (*GetEnterStatusResp, error)
	// 订单回调
	// 查询时间范围内的订单总数和价值总数
	CountOrderNumAndValueByTime(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 查询时间范围内的OrderIds
	GetOrderIdsByTime(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 补单接口
	ReSendPkg(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 查询时间范围内礼物订单总数和价值总数
	CountPresentOrderNumAndValueByTime(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 查询时间范围内的礼物OrderIds
	GetPresentOrderIdsByTime(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 礼物补单接口
	PresentReSendPkg(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 测试接口
	// 手动增加星钻
	AddFragment(context.Context, *AddFragmentReq) (*AddFragmentResp, error)
	// 手动推送弹窗
	PushTaskPopWindow(context.Context, *PushTaskPopWindowReq) (*PushTaskPopWindowResp, error)
	// 浮层管理及入口提醒红点
	// 获取浮层管理及入口提醒红点
	GetFloatingLayerConfig(context.Context, *GetFloatingLayerConfigReq) (*GetFloatingLayerConfigResp, error)
	// 修改浮层管理及入口提醒红点
	SetFloatingLayerConfig(context.Context, *SetFloatingLayerConfigReq) (*SetFloatingLayerConfigResp, error)
	// 礼物白名单
	// 获取白名单列表
	GetWhiteList(context.Context, *GetWhiteListReq) (*GetWhiteListResp, error)
	// 添加白名单
	AddWhiteList(context.Context, *AddWhiteListReq) (*AddWhiteListResp, error)
	// 删除白名单
	DeleteWhiteList(context.Context, *DeleteWhiteListReq) (*DeleteWhiteListResp, error)
	// 获取当前白名单礼物配置列表
	GetWhiteListGiftConfig(context.Context, *GetWhiteListGiftConfigReq) (*GetWhiteListGiftConfigResp, error)
}

func RegisterGloryWorldRewardServer(s *grpc.Server, srv GloryWorldRewardServer) {
	s.RegisterService(&_GloryWorldReward_serviceDesc, srv)
}

func _GloryWorldReward_IsNeedNotice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsNeedNoticeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).IsNeedNotice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/IsNeedNotice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).IsNeedNotice(ctx, req.(*IsNeedNoticeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_ReceiveReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).ReceiveReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/ReceiveReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).ReceiveReward(ctx, req.(*ReceiveRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_GetAllTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllTaskListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).GetAllTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/GetAllTaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).GetAllTaskList(ctx, req.(*GetAllTaskListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_GetWeekTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeekTaskListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).GetWeekTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/GetWeekTaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).GetWeekTaskList(ctx, req.(*GetWeekTaskListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_GetFragmentDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFragmentDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).GetFragmentDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/GetFragmentDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).GetFragmentDetail(ctx, req.(*GetFragmentDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_GetNoticeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNoticeStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).GetNoticeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/GetNoticeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).GetNoticeStatus(ctx, req.(*GetNoticeStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_SetNoticeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetNoticeStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).SetNoticeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/SetNoticeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).SetNoticeStatus(ctx, req.(*SetNoticeStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_GetEnterStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).GetEnterStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/GetEnterStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).GetEnterStatus(ctx, req.(*GetEnterStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_CountOrderNumAndValueByTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).CountOrderNumAndValueByTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/CountOrderNumAndValueByTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).CountOrderNumAndValueByTime(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_GetOrderIdsByTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).GetOrderIdsByTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/GetOrderIdsByTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).GetOrderIdsByTime(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_ReSendPkg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).ReSendPkg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/ReSendPkg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).ReSendPkg(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_CountPresentOrderNumAndValueByTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).CountPresentOrderNumAndValueByTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/CountPresentOrderNumAndValueByTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).CountPresentOrderNumAndValueByTime(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_GetPresentOrderIdsByTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).GetPresentOrderIdsByTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/GetPresentOrderIdsByTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).GetPresentOrderIdsByTime(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_PresentReSendPkg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).PresentReSendPkg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/PresentReSendPkg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).PresentReSendPkg(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_AddFragment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFragmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).AddFragment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/AddFragment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).AddFragment(ctx, req.(*AddFragmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_PushTaskPopWindow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushTaskPopWindowReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).PushTaskPopWindow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/PushTaskPopWindow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).PushTaskPopWindow(ctx, req.(*PushTaskPopWindowReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_GetFloatingLayerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFloatingLayerConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).GetFloatingLayerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/GetFloatingLayerConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).GetFloatingLayerConfig(ctx, req.(*GetFloatingLayerConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_SetFloatingLayerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetFloatingLayerConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).SetFloatingLayerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/SetFloatingLayerConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).SetFloatingLayerConfig(ctx, req.(*SetFloatingLayerConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_GetWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).GetWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/GetWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).GetWhiteList(ctx, req.(*GetWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_AddWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).AddWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/AddWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).AddWhiteList(ctx, req.(*AddWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_DeleteWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).DeleteWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/DeleteWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).DeleteWhiteList(ctx, req.(*DeleteWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldReward_GetWhiteListGiftConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhiteListGiftConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldRewardServer).GetWhiteListGiftConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_reward.GloryWorldReward/GetWhiteListGiftConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldRewardServer).GetWhiteListGiftConfig(ctx, req.(*GetWhiteListGiftConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GloryWorldReward_serviceDesc = grpc.ServiceDesc{
	ServiceName: "glory_reward.GloryWorldReward",
	HandlerType: (*GloryWorldRewardServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "IsNeedNotice",
			Handler:    _GloryWorldReward_IsNeedNotice_Handler,
		},
		{
			MethodName: "ReceiveReward",
			Handler:    _GloryWorldReward_ReceiveReward_Handler,
		},
		{
			MethodName: "GetAllTaskList",
			Handler:    _GloryWorldReward_GetAllTaskList_Handler,
		},
		{
			MethodName: "GetWeekTaskList",
			Handler:    _GloryWorldReward_GetWeekTaskList_Handler,
		},
		{
			MethodName: "GetFragmentDetail",
			Handler:    _GloryWorldReward_GetFragmentDetail_Handler,
		},
		{
			MethodName: "GetNoticeStatus",
			Handler:    _GloryWorldReward_GetNoticeStatus_Handler,
		},
		{
			MethodName: "SetNoticeStatus",
			Handler:    _GloryWorldReward_SetNoticeStatus_Handler,
		},
		{
			MethodName: "GetEnterStatus",
			Handler:    _GloryWorldReward_GetEnterStatus_Handler,
		},
		{
			MethodName: "CountOrderNumAndValueByTime",
			Handler:    _GloryWorldReward_CountOrderNumAndValueByTime_Handler,
		},
		{
			MethodName: "GetOrderIdsByTime",
			Handler:    _GloryWorldReward_GetOrderIdsByTime_Handler,
		},
		{
			MethodName: "ReSendPkg",
			Handler:    _GloryWorldReward_ReSendPkg_Handler,
		},
		{
			MethodName: "CountPresentOrderNumAndValueByTime",
			Handler:    _GloryWorldReward_CountPresentOrderNumAndValueByTime_Handler,
		},
		{
			MethodName: "GetPresentOrderIdsByTime",
			Handler:    _GloryWorldReward_GetPresentOrderIdsByTime_Handler,
		},
		{
			MethodName: "PresentReSendPkg",
			Handler:    _GloryWorldReward_PresentReSendPkg_Handler,
		},
		{
			MethodName: "AddFragment",
			Handler:    _GloryWorldReward_AddFragment_Handler,
		},
		{
			MethodName: "PushTaskPopWindow",
			Handler:    _GloryWorldReward_PushTaskPopWindow_Handler,
		},
		{
			MethodName: "GetFloatingLayerConfig",
			Handler:    _GloryWorldReward_GetFloatingLayerConfig_Handler,
		},
		{
			MethodName: "SetFloatingLayerConfig",
			Handler:    _GloryWorldReward_SetFloatingLayerConfig_Handler,
		},
		{
			MethodName: "GetWhiteList",
			Handler:    _GloryWorldReward_GetWhiteList_Handler,
		},
		{
			MethodName: "AddWhiteList",
			Handler:    _GloryWorldReward_AddWhiteList_Handler,
		},
		{
			MethodName: "DeleteWhiteList",
			Handler:    _GloryWorldReward_DeleteWhiteList_Handler,
		},
		{
			MethodName: "GetWhiteListGiftConfig",
			Handler:    _GloryWorldReward_GetWhiteListGiftConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "glory-reward/glory-reward.proto",
}

func init() {
	proto.RegisterFile("glory-reward/glory-reward.proto", fileDescriptor_glory_reward_18a107700e2bdc1f)
}

var fileDescriptor_glory_reward_18a107700e2bdc1f = []byte{
	// 2125 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x19, 0xdb, 0x6e, 0x23, 0x49,
	0x75, 0x6c, 0x67, 0x1c, 0xfb, 0x24, 0x76, 0xda, 0xe5, 0x5c, 0x1c, 0x67, 0x66, 0x92, 0xe9, 0xd5,
	0x2e, 0xd9, 0x88, 0x4d, 0x20, 0xab, 0x7d, 0x00, 0x2d, 0x42, 0x99, 0xcc, 0x24, 0x9b, 0x61, 0x27,
	0x09, 0xed, 0x99, 0x09, 0x5a, 0x10, 0x56, 0xaf, 0xbb, 0x6c, 0x97, 0xd2, 0xee, 0xee, 0xed, 0x2a,
	0x27, 0xf8, 0x85, 0x67, 0x1e, 0x10, 0xfc, 0x02, 0x42, 0x20, 0x7e, 0x82, 0x57, 0x3e, 0x86, 0xbf,
	0x40, 0x75, 0xaa, 0xda, 0xee, 0x5b, 0x9c, 0xa0, 0x59, 0x1e, 0x78, 0x73, 0x9d, 0x5b, 0x9d, 0x7b,
	0x9d, 0xd3, 0x86, 0xed, 0x81, 0xeb, 0x87, 0x93, 0xcf, 0x42, 0x7a, 0x6b, 0x87, 0xce, 0x41, 0xfc,
	0xb0, 0x1f, 0x84, 0xbe, 0xf0, 0xc9, 0x32, 0xc2, 0xba, 0x0a, 0xd6, 0xde, 0x0e, 0x69, 0xcf, 0xf7,
	0x7a, 0xcc, 0xa5, 0x9f, 0xdd, 0x1c, 0x1e, 0xc4, 0x0f, 0x8a, 0xdc, 0xfc, 0x25, 0xac, 0x9c, 0xf1,
	0x73, 0x4a, 0x9d, 0x73, 0x5f, 0xb0, 0x1e, 0xb5, 0xe8, 0x77, 0xc4, 0x80, 0xd2, 0x98, 0x39, 0xad,
	0xc2, 0x4e, 0x61, 0xb7, 0x66, 0xc9, 0x9f, 0xe4, 0x87, 0xb0, 0x20, 0x26, 0x01, 0x6d, 0x15, 0x77,
	0x0a, 0xbb, 0xf5, 0xc3, 0xd6, 0x7e, 0xfc, 0x8a, 0x7d, 0xc5, 0xf8, 0x76, 0x12, 0x50, 0x0b, 0xa9,
	0xcc, 0x03, 0x30, 0x92, 0x22, 0x79, 0x40, 0xb6, 0xa0, 0xca, 0x78, 0xd7, 0x43, 0x00, 0x4a, 0xae,
	0x58, 0x15, 0xc6, 0x15, 0x81, 0xf9, 0x33, 0x30, 0x2c, 0xda, 0xa3, 0xec, 0x86, 0x5a, 0x28, 0x32,
	0x5f, 0x89, 0x0d, 0x58, 0x14, 0x36, 0xbf, 0xee, 0x32, 0xa7, 0x55, 0xdc, 0x29, 0xed, 0xd6, 0xac,
	0xb2, 0x3c, 0x9e, 0x39, 0x66, 0x13, 0x1a, 0x29, 0x76, 0x1e, 0x98, 0x7f, 0x2f, 0x41, 0xe5, 0xad,
	0xc4, 0x7b, 0x7d, 0x3f, 0xce, 0xaa, 0x04, 0x6a, 0x56, 0xf2, 0x25, 0x54, 0xfb, 0xa1, 0x3d, 0xe8,
	0xc6, 0xac, 0xdb, 0x4e, 0x5a, 0x77, 0x2a, 0x0f, 0x27, 0xa1, 0x3d, 0x18, 0x51, 0x4f, 0xa0, 0x91,
	0x15, 0xc9, 0x21, 0x7f, 0x91, 0x9f, 0xc0, 0x92, 0xa2, 0x52, 0xfc, 0xa5, 0x3c, 0xef, 0x28, 0x95,
	0x90, 0x11, 0xc2, 0xe9, 0x6f, 0xf2, 0x39, 0x54, 0x51, 0x23, 0x64, 0x5c, 0x40, 0xc6, 0xf5, 0x24,
	0xa3, 0x54, 0x5e, 0xdd, 0x27, 0xf4, 0x2f, 0xf2, 0x14, 0xa0, 0xcf, 0x3c, 0xc6, 0x87, 0x5d, 0x6f,
	0x3c, 0x6a, 0x3d, 0x46, 0x4b, 0xaa, 0x0a, 0x72, 0x3e, 0x1e, 0x91, 0x4f, 0x60, 0xc5, 0xa3, 0xd4,
	0xe9, 0xc6, 0x68, 0xca, 0x48, 0x53, 0x93, 0xe0, 0x93, 0x29, 0xdd, 0x0e, 0x2c, 0x0f, 0x58, 0x5f,
	0x74, 0x99, 0xa0, 0x23, 0xe9, 0x92, 0x45, 0x24, 0x02, 0x09, 0x3b, 0x13, 0x74, 0x74, 0xe6, 0xc8,
	0x8b, 0xb4, 0x61, 0x52, 0x48, 0x45, 0x5d, 0xa4, 0x20, 0x52, 0xc0, 0xa7, 0xd0, 0x60, 0xbc, 0x2b,
	0x86, 0x8c, 0x77, 0x6f, 0x29, 0xbd, 0xee, 0x4a, 0x05, 0x5b, 0x55, 0x0c, 0x6a, 0x9d, 0xf1, 0xb7,
	0x43, 0xc6, 0xaf, 0x28, 0xbd, 0x96, 0x06, 0x90, 0xed, 0x99, 0x8b, 0xd8, 0x88, 0xb6, 0x60, 0xa7,
	0xb0, 0x5b, 0x9a, 0x3a, 0x82, 0x8d, 0xa8, 0xf9, 0x31, 0x34, 0x4e, 0xa9, 0x38, 0x72, 0x5d, 0x49,
	0xfe, 0x35, 0xe3, 0x22, 0x37, 0xf8, 0xe6, 0xbf, 0x0a, 0x40, 0xd2, 0x74, 0x3c, 0x98, 0xba, 0xd1,
	0x65, 0x5c, 0xb4, 0x0a, 0x3b, 0xa5, 0xdd, 0xa5, 0x3c, 0x37, 0xca, 0x1c, 0x50, 0x6e, 0x94, 0x8c,
	0xe4, 0x4b, 0xa8, 0x4f, 0xd5, 0x56, 0x9c, 0xc5, 0xb9, 0x9c, 0xcb, 0xb7, 0xda, 0x9a, 0x88, 0x1b,
	0xbd, 0x37, 0xe3, 0x2e, 0xcd, 0xe7, 0x96, 0xd4, 0x11, 0xb7, 0xf9, 0x09, 0x9a, 0x71, 0x15, 0x13,
	0x98, 0x6f, 0xef, 0x1f, 0x0a, 0xd0, 0xcc, 0x10, 0x7e, 0x80, 0xc1, 0x29, 0x95, 0x8b, 0xff, 0x85,
	0xca, 0x7f, 0x29, 0x01, 0x89, 0x0a, 0x40, 0x65, 0x33, 0xd6, 0xd4, 0x26, 0x54, 0xfc, 0xd0, 0xa1,
	0x61, 0x54, 0x54, 0x55, 0x6b, 0x11, 0xcf, 0xff, 0x77, 0x55, 0xb5, 0x0e, 0xe5, 0x90, 0x8e, 0xec,
	0xf0, 0x1a, 0x2b, 0xaa, 0x6a, 0xe9, 0x93, 0x6c, 0x1a, 0xaa, 0x4c, 0x1c, 0x5d, 0x46, 0x65, 0xac,
	0x10, 0x87, 0xac, 0xc2, 0xe3, 0x9e, 0x3f, 0xf6, 0x84, 0x2e, 0x1c, 0x75, 0xb8, 0xaf, 0x66, 0x52,
	0x85, 0x50, 0x4d, 0x17, 0x82, 0x24, 0xa0, 0xbf, 0x0b, 0x58, 0x48, 0x13, 0x95, 0xa2, 0x40, 0x48,
	0xb0, 0x09, 0xe8, 0x23, 0x14, 0xbf, 0x84, 0xe2, 0x17, 0xe5, 0xf9, 0x7c, 0x3c, 0x32, 0xff, 0x5a,
	0x80, 0xd5, 0x53, 0x2a, 0x22, 0x87, 0xbe, 0xa4, 0xc2, 0x66, 0x6e, 0x7e, 0x17, 0x5d, 0x87, 0xb2,
	0xdf, 0xef, 0x73, 0x2a, 0x30, 0x30, 0x35, 0x4b, 0x9f, 0xa4, 0x51, 0x2e, 0x1b, 0x31, 0x81, 0xfe,
	0xae, 0x59, 0xea, 0x40, 0x5e, 0x83, 0x81, 0x77, 0xc6, 0x03, 0xa2, 0xfc, 0xba, 0x93, 0xf4, 0x6b,
	0x32, 0x41, 0xd0, 0xc3, 0x75, 0xc9, 0x39, 0x3b, 0x9b, 0x1d, 0x58, 0xcb, 0xd1, 0x91, 0x07, 0xe4,
	0xa7, 0xb0, 0xe8, 0xe0, 0x89, 0xeb, 0x8c, 0x9e, 0x2b, 0x1b, 0x33, 0x34, 0x62, 0x30, 0x57, 0xb1,
	0x9e, 0xd4, 0x3b, 0xd2, 0x11, 0xb6, 0x18, 0x73, 0x8b, 0x7e, 0x67, 0xfe, 0x53, 0x55, 0x4f, 0x12,
	0xcc, 0x03, 0xf2, 0x31, 0xd4, 0x7b, 0x43, 0xdb, 0xf3, 0xa8, 0xdb, 0xe5, 0x08, 0xd5, 0x4f, 0x51,
	0x4d, 0x43, 0x15, 0x29, 0xf9, 0x08, 0x6a, 0x43, 0x6a, 0xcb, 0xdc, 0xd6, 0x54, 0x45, 0xa4, 0x5a,
	0x56, 0x40, 0x4d, 0xf4, 0x29, 0x18, 0xbd, 0xa1, 0xed, 0xba, 0xd4, 0x1b, 0xd0, 0x88, 0xae, 0x84,
	0x74, 0x2b, 0x53, 0xb8, 0x26, 0xdd, 0x87, 0xe6, 0x90, 0x71, 0x21, 0x4d, 0xc2, 0xf4, 0xd4, 0xd4,
	0x0b, 0x48, 0xdd, 0xd0, 0x28, 0x99, 0x99, 0x8a, 0xde, 0xfc, 0x35, 0x90, 0x4e, 0xc6, 0xa8, 0xe9,
	0x23, 0x5c, 0x78, 0xc8, 0x23, 0x2c, 0xb3, 0x97, 0xf1, 0xae, 0x1f, 0x50, 0x4f, 0x6b, 0x5f, 0x66,
	0xfc, 0x22, 0xa0, 0x9e, 0xb9, 0x06, 0xcd, 0x4e, 0xd6, 0x35, 0xe6, 0x35, 0xf6, 0xe1, 0x57, 0x9e,
	0x88, 0x0c, 0x94, 0x57, 0x6e, 0x41, 0x55, 0x96, 0x02, 0x15, 0xb3, 0x97, 0xb3, 0xa2, 0x00, 0x67,
	0x0e, 0xa9, 0x43, 0xd1, 0xe7, 0x3a, 0x8b, 0x8a, 0x3e, 0x8f, 0x72, 0xad, 0x94, 0x78, 0xb1, 0x19,
	0xef, 0x0e, 0x42, 0x7b, 0xa2, 0x6d, 0x2d, 0x33, 0x7e, 0x1a, 0xda, 0x13, 0xf3, 0xe7, 0x18, 0xb5,
	0xc4, 0x65, 0x3c, 0x88, 0xab, 0x5c, 0x88, 0xab, 0x8c, 0x92, 0x43, 0x17, 0xaf, 0xaa, 0x5a, 0xf2,
	0xa7, 0x19, 0x42, 0xfd, 0xc8, 0x71, 0x66, 0x89, 0x91, 0x97, 0xe9, 0x1f, 0xd6, 0x85, 0x0c, 0x28,
	0xc9, 0x42, 0xd3, 0xd6, 0x78, 0xe3, 0x91, 0xd9, 0x80, 0x95, 0xc4, 0x9d, 0x3c, 0x30, 0x7f, 0x0f,
	0xab, 0x97, 0x63, 0x3e, 0x94, 0xa1, 0xbb, 0xf4, 0x83, 0x2b, 0xe6, 0x39, 0xfe, 0x6d, 0xbe, 0x32,
	0x4f, 0x01, 0xa2, 0xcc, 0xc3, 0xf9, 0x05, 0xbb, 0x83, 0x86, 0x9c, 0x39, 0xe4, 0x8b, 0xb8, 0xae,
	0xf2, 0x3d, 0x99, 0xd7, 0xf1, 0xa6, 0x4a, 0x9a, 0x1b, 0xb0, 0x96, 0x73, 0x3f, 0x0f, 0xcc, 0x7f,
	0x17, 0xa1, 0x79, 0xe2, 0xfa, 0xb6, 0x60, 0xde, 0xe0, 0x6b, 0x7b, 0x42, 0xc3, 0x63, 0xdf, 0xeb,
	0xb3, 0x01, 0xd9, 0x83, 0x86, 0x9a, 0xc1, 0x64, 0x0e, 0x86, 0x42, 0xb5, 0x9a, 0x02, 0xb6, 0x9a,
	0x15, 0x2f, 0x4a, 0x89, 0x50, 0x60, 0xbf, 0x91, 0xe3, 0x84, 0xa2, 0xa5, 0x9e, 0xee, 0x5a, 0x45,
	0xa4, 0xac, 0x29, 0xf0, 0x2b, 0x4f, 0x35, 0xae, 0x13, 0xa8, 0x45, 0x32, 0x6f, 0x99, 0xe8, 0x0d,
	0x75, 0xc7, 0x7e, 0x9e, 0x2a, 0xe2, 0xb8, 0x36, 0x1d, 0x24, 0xb4, 0x96, 0xf5, 0x95, 0x78, 0x92,
	0xc5, 0xa9, 0xe5, 0xf4, 0x7c, 0x4f, 0x50, 0x4f, 0x60, 0xd2, 0x54, 0xa3, 0xeb, 0x8e, 0x15, 0x90,
	0xec, 0x82, 0x11, 0x5d, 0x37, 0xf4, 0x6f, 0x95, 0x5e, 0x6a, 0x14, 0xd2, 0xec, 0x9d, 0xa1, 0x7f,
	0x8b, 0x8a, 0xfd, 0x02, 0x8c, 0x90, 0x3a, 0xdd, 0xc0, 0x67, 0x9e, 0x88, 0x74, 0x2b, 0x3f, 0x54,
	0xb7, 0x7a, 0x48, 0x9d, 0x4b, 0xc9, 0xa9, 0xb5, 0x6b, 0xc1, 0xe2, 0x0d, 0x0d, 0x39, 0xf3, 0x3d,
	0x6c, 0xfb, 0x55, 0x2b, 0x3a, 0x9a, 0x5b, 0xb0, 0x29, 0xfb, 0x5a, 0xd6, 0xdb, 0xb2, 0x13, 0x71,
	0x68, 0xdf, 0x85, 0xe4, 0x01, 0x79, 0x07, 0x6b, 0x7d, 0x8d, 0xea, 0xba, 0x12, 0x27, 0x4d, 0xef,
	0xb3, 0x01, 0x86, 0x64, 0x69, 0xae, 0x9a, 0x5a, 0x4a, 0xb3, 0x9f, 0x05, 0x9a, 0x7f, 0x2a, 0xc0,
	0x66, 0xe7, 0x2e, 0x95, 0xfe, 0x47, 0x97, 0x92, 0x36, 0x54, 0xfc, 0x80, 0x86, 0xb6, 0xf0, 0x43,
	0x5d, 0xa9, 0xd3, 0xb3, 0xf9, 0x04, 0xda, 0x9d, 0x3b, 0xbd, 0x20, 0x0b, 0x4b, 0x8e, 0x3a, 0x43,
	0x26, 0xa8, 0x1e, 0x88, 0xcc, 0x1f, 0x83, 0x91, 0x04, 0xf1, 0x40, 0x96, 0xd0, 0xad, 0x04, 0xcc,
	0x66, 0x9f, 0x9a, 0x55, 0xbd, 0x8d, 0x48, 0xcc, 0x0b, 0x2c, 0xcf, 0xb8, 0x14, 0x39, 0xe8, 0xea,
	0x19, 0x37, 0xce, 0x03, 0x0c, 0x87, 0x5c, 0x9c, 0x8c, 0xe6, 0x29, 0x4d, 0xc0, 0x48, 0x0a, 0xe4,
	0x81, 0x69, 0x01, 0x79, 0x49, 0x5d, 0x2a, 0xe8, 0xf7, 0x78, 0xcf, 0x1a, 0x34, 0x33, 0x32, 0x79,
	0xa0, 0xd3, 0x6a, 0x0a, 0x3b, 0x65, 0x7d, 0x31, 0x4b, 0xab, 0x6f, 0x30, 0xad, 0x72, 0x91, 0x3c,
	0x90, 0x9d, 0x0f, 0xf5, 0x89, 0x0d, 0x89, 0xa9, 0xce, 0x77, 0x19, 0x52, 0x4e, 0x3d, 0x1c, 0xf7,
	0x35, 0x5f, 0x45, 0x72, 0xa0, 0x23, 0xff, 0x58, 0x84, 0x46, 0x06, 0x8f, 0xcd, 0x59, 0xef, 0x0b,
	0x7a, 0x85, 0x52, 0xe6, 0x11, 0x02, 0x0b, 0x9e, 0xad, 0x7b, 0x43, 0xd5, 0xc2, 0xdf, 0x72, 0x54,
	0x61, 0x3d, 0xdf, 0xeb, 0xca, 0xae, 0x5d, 0x52, 0xd5, 0x22, 0xcf, 0xef, 0x42, 0x57, 0xce, 0x19,
	0x41, 0x28, 0x97, 0xc0, 0x05, 0x35, 0x67, 0xe0, 0x81, 0x3c, 0x87, 0x65, 0xda, 0xef, 0xd3, 0x9e,
	0xe8, 0x7e, 0x4b, 0x07, 0xcc, 0xd3, 0x05, 0xbd, 0xa4, 0x60, 0x2f, 0x24, 0x48, 0x86, 0x5f, 0x93,
	0x50, 0x2f, 0x9a, 0xc8, 0xaa, 0x0a, 0xf2, 0xca, 0x73, 0xe4, 0xf8, 0x34, 0x0e, 0x1c, 0x5b, 0xe8,
	0xf1, 0x49, 0xef, 0x34, 0x0a, 0x14, 0xcd, 0x57, 0xbd, 0x90, 0x4e, 0x09, 0xd4, 0x80, 0x06, 0x0a,
	0x84, 0x04, 0x6b, 0x50, 0x66, 0xbc, 0xeb, 0x50, 0x57, 0xaf, 0x32, 0x8f, 0x19, 0x7f, 0x49, 0xdd,
	0xbd, 0x7f, 0x14, 0xd2, 0xe3, 0xaf, 0xde, 0xc5, 0x36, 0xb3, 0xd0, 0x33, 0xef, 0xc6, 0x76, 0x99,
	0x63, 0x3c, 0x22, 0x6d, 0x58, 0xcf, 0xa2, 0x5f, 0xd8, 0x9c, 0x1a, 0x85, 0x7c, 0x9c, 0xec, 0xe1,
	0x46, 0x91, 0x3c, 0x83, 0x76, 0x16, 0x77, 0xd4, 0x13, 0xec, 0x86, 0x89, 0x89, 0x51, 0xba, 0x83,
	0x97, 0x72, 0x61, 0xac, 0xee, 0x31, 0x80, 0xd9, 0x18, 0x40, 0xd6, 0xa0, 0x31, 0x3b, 0xcd, 0x14,
	0x4b, 0x80, 0x8f, 0xd5, 0x03, 0x64, 0x14, 0xc8, 0x2a, 0x18, 0x33, 0xf0, 0x57, 0x38, 0xe7, 0x18,
	0x45, 0xb2, 0x01, 0xcd, 0x04, 0xb1, 0x9a, 0x6a, 0x8c, 0xd2, 0xde, 0x10, 0x1a, 0x99, 0xc7, 0x93,
	0x3c, 0x81, 0x56, 0x06, 0x38, 0xbb, 0x78, 0x13, 0xd6, 0x32, 0xd8, 0x13, 0x7b, 0xa4, 0x1d, 0x92,
	0x41, 0x21, 0xc0, 0x28, 0xee, 0xfd, 0xad, 0x08, 0x10, 0x73, 0xfb, 0x9a, 0xdc, 0xf5, 0xb3, 0xee,
	0x5e, 0x07, 0x32, 0x03, 0x4b, 0xa9, 0xda, 0xd5, 0x1b, 0xd0, 0x9c, 0xc1, 0x51, 0x24, 0x22, 0xd0,
	0xcf, 0x29, 0xc4, 0x1b, 0xe6, 0xba, 0xcc, 0xf7, 0x10, 0x5f, 0x22, 0x5b, 0xb0, 0x91, 0x8c, 0xcd,
	0x09, 0x0b, 0xb9, 0xe8, 0x50, 0xcf, 0x31, 0x16, 0xc8, 0x36, 0x6c, 0x25, 0x91, 0xb2, 0xfe, 0x66,
	0x04, 0x8f, 0xb3, 0xdc, 0x1d, 0x7a, 0x43, 0xbd, 0x97, 0xf6, 0x84, 0x1b, 0xe5, 0x2c, 0x72, 0xe6,
	0xd8, 0x45, 0x62, 0xc2, 0xb3, 0x24, 0x52, 0xab, 0x35, 0xa3, 0xa9, 0x10, 0x02, 0xf5, 0x4c, 0xec,
	0x5f, 0xab, 0xaf, 0x1d, 0xe8, 0xa3, 0x26, 0xac, 0x44, 0xbf, 0x67, 0x1e, 0x22, 0x50, 0x9f, 0xee,
	0x3e, 0xc8, 0x6c, 0x14, 0xa4, 0x33, 0x23, 0xd8, 0xb9, 0xaf, 0xf3, 0xc9, 0x28, 0xee, 0x05, 0xa9,
	0xd9, 0x41, 0xbf, 0x80, 0x32, 0x35, 0xb3, 0xe0, 0xd9, 0x0d, 0x5b, 0xb0, 0x91, 0x83, 0x97, 0x03,
	0x9c, 0x51, 0x90, 0xb9, 0x91, 0x83, 0x3c, 0x76, 0x7d, 0x19, 0x8d, 0xc3, 0x3f, 0xaf, 0x80, 0x81,
	0x41, 0xb8, 0xf2, 0x43, 0xd7, 0x51, 0x8a, 0x90, 0x0b, 0x58, 0x8e, 0x7f, 0x46, 0x22, 0x4f, 0x93,
	0x2d, 0x2c, 0xf5, 0xd5, 0xaa, 0xfd, 0x6c, 0x1e, 0x9a, 0x07, 0xe6, 0x23, 0x62, 0x41, 0x2d, 0xf1,
	0x9d, 0x88, 0x3c, 0x4b, 0x8f, 0x58, 0xc9, 0x6f, 0x50, 0xed, 0xed, 0xb9, 0x78, 0x94, 0xf9, 0x0e,
	0xea, 0xc9, 0xcf, 0x12, 0x24, 0x3d, 0x63, 0xa6, 0x3f, 0x6e, 0xb4, 0x77, 0xe6, 0x13, 0xa0, 0xd8,
	0x5f, 0xa9, 0x27, 0x31, 0xfe, 0xdd, 0x21, 0xcb, 0x96, 0xfa, 0x8a, 0xd0, 0x7e, 0x7e, 0x0f, 0x05,
	0x4a, 0xfe, 0x2d, 0xce, 0xf9, 0xc9, 0x2d, 0x8c, 0x98, 0x19, 0xce, 0xcc, 0x2a, 0xd9, 0xfe, 0xe8,
	0x5e, 0x9a, 0x98, 0xe6, 0xf1, 0xf5, 0x22, 0x47, 0xf3, 0xd4, 0x6a, 0x93, 0xa3, 0x79, 0x66, 0x3f,
	0x41, 0xc9, 0x9d, 0xf9, 0x92, 0x3b, 0xf7, 0x4a, 0xee, 0xe4, 0x4a, 0x56, 0x41, 0x8c, 0xad, 0x23,
	0x39, 0x41, 0x4c, 0x6e, 0x46, 0x39, 0x41, 0x4c, 0x6d, 0x33, 0xe6, 0x23, 0x72, 0x09, 0x5b, 0xc7,
	0xfe, 0xd8, 0x13, 0x17, 0xa1, 0x43, 0xc3, 0xf3, 0xf1, 0xe8, 0xc8, 0x73, 0xde, 0xdb, 0xee, 0x98,
	0xbe, 0x98, 0xa8, 0x7d, 0x5e, 0x26, 0x94, 0xfa, 0x1c, 0xfb, 0xfe, 0x70, 0x5f, 0x82, 0x2c, 0xdb,
	0x1b, 0x60, 0x2e, 0xaf, 0x27, 0x50, 0x28, 0x04, 0x1f, 0xf6, 0x33, 0x0c, 0xde, 0x85, 0xfa, 0xcc,
	0xc2, 0xef, 0x97, 0x93, 0x44, 0x45, 0x7c, 0x28, 0xea, 0x08, 0xaa, 0x16, 0x95, 0xed, 0xea, 0xf2,
	0x7a, 0x40, 0x9e, 0x24, 0xe8, 0x2c, 0x1a, 0xb8, 0x76, 0x8f, 0x22, 0x79, 0x56, 0x9b, 0x57, 0xa3,
	0x40, 0x4c, 0x50, 0xc4, 0x15, 0x98, 0xa8, 0x9a, 0x1e, 0x16, 0xbe, 0x3f, 0x33, 0x2f, 0xa1, 0x75,
	0x4a, 0x13, 0x62, 0x3f, 0xd4, 0xda, 0xaf, 0xc0, 0xd0, 0xe2, 0x3e, 0xd4, 0xe8, 0xd7, 0xb0, 0x14,
	0xdb, 0x02, 0xc9, 0x93, 0x64, 0x1e, 0x24, 0x97, 0xd2, 0xf6, 0xd3, 0x39, 0x58, 0x1e, 0x90, 0xdf,
	0x40, 0x23, 0xb3, 0xbe, 0xa5, 0x6b, 0x31, 0x6f, 0xbf, 0x4c, 0xd7, 0x62, 0xee, 0x0e, 0x48, 0xae,
	0x61, 0x3d, 0x7f, 0xf5, 0x20, 0x3f, 0xc8, 0x96, 0x72, 0xee, 0xaa, 0xd0, 0xde, 0x7d, 0x18, 0xa1,
	0xba, 0xac, 0xf3, 0xa0, 0xcb, 0x3a, 0x0f, 0xbd, 0xec, 0xee, 0x85, 0x81, 0xbc, 0x81, 0xe5, 0xf8,
	0xf4, 0x9b, 0x7e, 0x19, 0x52, 0xcb, 0x44, 0xfa, 0x65, 0xc8, 0x2c, 0x16, 0x6f, 0x60, 0x39, 0x3e,
	0xe8, 0x93, 0x6c, 0xd4, 0xe6, 0x89, 0x4b, 0xef, 0x08, 0xe4, 0x3d, 0xac, 0xa4, 0xe6, 0xf9, 0x74,
	0x9f, 0xca, 0xae, 0x10, 0xe9, 0x3e, 0x95, 0xb3, 0x10, 0xe8, 0x78, 0xe6, 0xcc, 0xfc, 0x39, 0xf1,
	0xcc, 0x5f, 0x1b, 0x72, 0xe2, 0x79, 0xc7, 0x0a, 0xf1, 0xe2, 0x47, 0xdf, 0xec, 0x0f, 0x7c, 0xd7,
	0xf6, 0x06, 0xfb, 0x5f, 0x1c, 0x0a, 0xb1, 0xdf, 0xf3, 0x47, 0x07, 0xf8, 0x7f, 0x51, 0xcf, 0x77,
	0x0f, 0x38, 0x0d, 0x6f, 0x58, 0x8f, 0xf2, 0xc4, 0xbf, 0x4f, 0xdf, 0x96, 0x11, 0xff, 0xf9, 0x7f,
	0x02, 0x00, 0x00, 0xff, 0xff, 0x34, 0x6e, 0x64, 0xaf, 0xa1, 0x1a, 0x00, 0x00,
}
