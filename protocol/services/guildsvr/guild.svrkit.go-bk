package Guild

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: src/guildsvr/guild.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for Guild service
const GuildMagic = uint16(14700)

// SvrkitClient API for Guild service

type GuildClientInterface interface {
	CreateGuild(ctx context.Context, uin uint32, in *CreateGuildReq, opts ...svrkit.CallOption) (*CreateGuildResp, error)
	GetGuild(ctx context.Context, uin uint32, in *GuildIdReq, opts ...svrkit.CallOption) (*GuildResp, error)
	SetShortId(ctx context.Context, uin uint32, in *SetShortIdReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	CheckNameExist(ctx context.Context, uin uint32, in *CheckNameExistReq, opts ...svrkit.CallOption) (*CheckNameExistResp, error)
	GuildAddGame(ctx context.Context, uin uint32, in *GuildAddGameReq, opts ...svrkit.CallOption) (*GuildAddGameResp, error)
	CheckGuildChairman(ctx context.Context, uin uint32, in *CheckGuildChairmanReq, opts ...svrkit.CallOption) (*CheckGuildChairmanResp, error)
	GetGuildMember(ctx context.Context, uin uint32, in *GetGuildMemberReq, opts ...svrkit.CallOption) (*GuildMemberResp, error)
	GetGuildGameList(ctx context.Context, uin uint32, in *GuildIdReq, opts ...svrkit.CallOption) (*GetGuildGameListResp, error)
	// 记录需要管理员批准的入会申请
	ApplyGuild(ctx context.Context, uin uint32, in *ApplyGuildReq, opts ...svrkit.CallOption) (*ApplyGuildResp, error)
	// 获取需要管理员批准的入会申请
	GetGuildApply(ctx context.Context, uin uint32, in *GetGuildApplyReq, opts ...svrkit.CallOption) (*GuildApplyResp, error)
	GetGuildMemberList(ctx context.Context, uin uint32, in *GetGuildMemberListReq, opts ...svrkit.CallOption) (*GuildMemberListResp, error)
	ReviewApplyGuild(ctx context.Context, uin uint32, in *ReviewApplyGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserGuildApplyList(ctx context.Context, uin uint32, in *GetUserGuildApplyListReq, opts ...svrkit.CallOption) (*GetUserGuildApplyListResp, error)
	GetGroupMemberList(ctx context.Context, uin uint32, in *GetGroupMemberListReq, opts ...svrkit.CallOption) (*GroupMemberListResp, error)
	AddGroupMember(ctx context.Context, uin uint32, in *AddGroupMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGroup(ctx context.Context, uin uint32, in *GetGroupReq, opts ...svrkit.CallOption) (*GroupResp, error)
	CreateGroup(ctx context.Context, uin uint32, in *CreateGroupReq, opts ...svrkit.CallOption) (*CreateGroupResp, error)
	UpdateGroupName(ctx context.Context, uin uint32, in *UpdateGroupNameReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	RemoveGroupMember(ctx context.Context, uin uint32, in *RemoveGroupMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGroupMember(ctx context.Context, uin uint32, in *GetGroupMemberReq, opts ...svrkit.CallOption) (*GroupMemberResp, error)
	GuildCheckin(ctx context.Context, uin uint32, in *GuildCheckinReq, opts ...svrkit.CallOption) (*GetGuildCheckinResp, error)
	GetGuildCheckin(ctx context.Context, uin uint32, in *GetGuildCheckinReq, opts ...svrkit.CallOption) (*GetGuildCheckinResp, error)
	GetGuildCheckinList(ctx context.Context, uin uint32, in *GetGuildCheckinListReq, opts ...svrkit.CallOption) (*GetGuildCheckinListResp, error)
	GetGuildCheckinCount(ctx context.Context, uin uint32, in *GetGuildCheckinCountReq, opts ...svrkit.CallOption) (*GetGuildCheckinCountResp, error)
	RemoveGuildMember(ctx context.Context, uin uint32, in *RemoveGuildMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	SetGuildMemberToAdmin(ctx context.Context, uin uint32, in *SetGuildMemberToAdminReq, opts ...svrkit.CallOption) (*SetGuildMemberToAdminResp, error)
	SetGuildAdminToMember(ctx context.Context, uin uint32, in *SetGuildAdminToMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	SetGroupMemberToAdmin(ctx context.Context, uin uint32, in *SetGroupMemberToAdminReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	SetGroupAdminToMember(ctx context.Context, uin uint32, in *SetGroupAdminToMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ApplyGroup(ctx context.Context, uin uint32, in *ApplyGroupReq, opts ...svrkit.CallOption) (*ApplyGroupResp, error)
	GetGroupApply(ctx context.Context, uin uint32, in *GetGroupApplyReq, opts ...svrkit.CallOption) (*GroupApplyResp, error)
	ReviewApplyGroup(ctx context.Context, uin uint32, in *ReviewApplyGroupReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	SetGroupMemberToOwner(ctx context.Context, uin uint32, in *SetGroupMemberToOwnerReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserGroupList(ctx context.Context, uin uint32, in *GetUserGroupListReq, opts ...svrkit.CallOption) (*GroupListResp, error)
	UpdateGroupMemberMute(ctx context.Context, uin uint32, in *UpdateGroupMemberMuteReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	SearchGuild(ctx context.Context, uin uint32, in *SearchGuildReq, opts ...svrkit.CallOption) (*GuildListResp, error)
	UpdateGroupMemberCard(ctx context.Context, uin uint32, in *UpdateGroupMemberCardReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GameGuildIdList(ctx context.Context, uin uint32, in *GameGuildIdListReq, opts ...svrkit.CallOption) (*GameGuildIdListResp, error)
	UpdateGroupNeedVerify(ctx context.Context, uin uint32, in *UpdateGroupNeedVerifyReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	PublishGuildBulletin(ctx context.Context, uin uint32, in *PublishGuildBulletinReq, opts ...svrkit.CallOption) (*PublishGuildBulletinResp, error)
	GetGuildBulletinList(ctx context.Context, uin uint32, in *GetGuildBulletinListReq, opts ...svrkit.CallOption) (*GetGuildBulletinListResp, error)
	UpdateGuild(ctx context.Context, uin uint32, in *UpdateGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateGroupAllMute(ctx context.Context, uin uint32, in *UpdateGroupAllMuteReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateGuildMemberPermission(ctx context.Context, uin uint32, in *UpdateGuildMemberPermissionReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DeleteGuildBulletin(ctx context.Context, uin uint32, in *DeleteGuildBulletinReq, opts ...svrkit.CallOption) (*DeleteGuildBulletinResp, error)
	UpdateGuildGameOrder(ctx context.Context, uin uint32, in *UpdateGuildGameOrderReq, opts ...svrkit.CallOption) (*UpdateGuildGameOrderResp, error)
	SetGroupOwnerToMember(ctx context.Context, uin uint32, in *SetGroupOwnerToMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildGroupList(ctx context.Context, uin uint32, in *GetGuildGroupListReq, opts ...svrkit.CallOption) (*GroupIdListResp, error)
	GetGuildGame(ctx context.Context, uin uint32, in *GetGuildGameReq, opts ...svrkit.CallOption) (*GetGuildGameResp, error)
	SetGuildGameUrl(ctx context.Context, uin uint32, in *SetGuildGameUrlReq, opts ...svrkit.CallOption) (*SetGuildGameUrlResp, error)
	DismissGuild(ctx context.Context, uin uint32, in *DismissGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GuildModfiyVerify(ctx context.Context, uin uint32, in *GuildModfiyVerifyReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GuildDirectJoin(ctx context.Context, uin uint32, in *GuildDirectJoinReq, opts ...svrkit.CallOption) (*GuildDirectJoinResp, error)
	GetGuildYesterdayCheckinCountBat(ctx context.Context, uin uint32, in *GetGuildYesterdayCheckinCountBatReq, opts ...svrkit.CallOption) (*GetGuildYesterdayCheckinCountBatResp, error)
	GetGuildBat(ctx context.Context, uin uint32, in *GetGuildBatReq, opts ...svrkit.CallOption) (*GetGuildBatResp, error)
	SetGuildGameUseCustomUrl(ctx context.Context, uin uint32, in *SetGuildGameUseCustomUrlReq, opts ...svrkit.CallOption) (*SetGuildGameUseCustomUrlResp, error)
	DismissGroup(ctx context.Context, uin uint32, in *DismissGroupReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildGamesUserCount(ctx context.Context, uin uint32, in *GetGuildGamesUserCountReq, opts ...svrkit.CallOption) (*GetGuildGamesUserCountResp, error)
	MarkGuildApplySendMsgSuccess(ctx context.Context, uin uint32, in *MarkGuildApplySendMsgSuccessReq, opts ...svrkit.CallOption) (*MarkGuildApplySendMsgSuccessResp, error)
	GetUserCurrentGuildApply(ctx context.Context, uin uint32, in *GetUserCurrentGuildApplyReq, opts ...svrkit.CallOption) (*GetUserCurrentGuildApplyResp, error)
	GuildGameDownloadReport(ctx context.Context, uin uint32, in *GuildGameDownloadReportReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildMemberGameDownloadList(ctx context.Context, uin uint32, in *GetMemberGameDownloadListReq, opts ...svrkit.CallOption) (*GetMemberGameDownloadListResp, error)
	SetGroupNotRecvMsg(ctx context.Context, uin uint32, in *SetGroupNotRecvMsgReq, opts ...svrkit.CallOption) (*SetGroupNotRecvMsgResp, error)
	ModifyGuildName(ctx context.Context, uin uint32, in *ModifyGuildNameReq, opts ...svrkit.CallOption) (*ModifyGuildNameResp, error)
	GetGuildMemberListByUids(ctx context.Context, uin uint32, in *GetGuildMemberListByUidsReq, opts ...svrkit.CallOption) (*GuildMemberListResp, error)
	CountGameGuild(ctx context.Context, uin uint32, in *CountGameGuildReq, opts ...svrkit.CallOption) (*CountGameGuildResp, error)
	GetAllGuildIdList(ctx context.Context, uin uint32, in *GetAllGuildIdListReq, opts ...svrkit.CallOption) (*GetAllGuildIdListResp, error)
	UpdateGameGuildUserCount(ctx context.Context, uin uint32, in *UpdateGameGuildUserCountReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	TopGameGuild(ctx context.Context, uin uint32, in *TopGameGuildReq, opts ...svrkit.CallOption) (*GuildListResp, error)
	GuildDeleteGame(ctx context.Context, uin uint32, in *GuildDeleteGameReq, opts ...svrkit.CallOption) (*GuildDeleteGameResp, error)
	GuildModifyGameGroupOrder(ctx context.Context, uin uint32, in *GuildModifyGameGroupOrderReq, opts ...svrkit.CallOption) (*GuildModifyGameGroupOrderResp, error)
	GuildRecoverGame(ctx context.Context, uin uint32, in *GuildRecoverGameReq, opts ...svrkit.CallOption) (*GuildRecoverGameResp, error)
	GuildGetGroupOrder(ctx context.Context, uin uint32, in *GuildGetGroupOrderReq, opts ...svrkit.CallOption) (*GuildGetGroupOrderResp, error)
	GetGuildMemberJoinedCount(ctx context.Context, uin uint32, in *GetGuildMemberJoinedCountReq, opts ...svrkit.CallOption) (*GetGuildMemberJoinedCountResp, error)
	UpdateGameGuildOnlineUserCount(ctx context.Context, uin uint32, in *UpdateGameGuildOnlineUserCountReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetAllDeletedGuildIdList(ctx context.Context, uin uint32, in *GetAllDeletedGuildIdListReq, opts ...svrkit.CallOption) (*GetAllDeletedGuildIdListResp, error)
	GuildSetUidNeedVerify(ctx context.Context, uin uint32, in *GuildSetUidNeedVerifyReq, opts ...svrkit.CallOption) (*GuildSetUidNeedVerifyResp, error)
	GuildCheckUidNeedVerify(ctx context.Context, uin uint32, in *GuildCheckUidNeedVerifyReq, opts ...svrkit.CallOption) (*GuildCheckUidNeedVerifyResp, error)
	GetTopGuildIdList(ctx context.Context, uin uint32, in *GetTopGuildIdListReq, opts ...svrkit.CallOption) (*GetTopGuildIdListResp, error)
	UpdateGuildGameLimit(ctx context.Context, uin uint32, in *UpdateGuildGameLimitReq, opts ...svrkit.CallOption) (*UpdateGuildGameLimitResp, error)
	UnSetShortId(ctx context.Context, uin uint32, in *SetShortIdReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	BatchGetGroup(ctx context.Context, uin uint32, in *BatchGetGroupReq, opts ...svrkit.CallOption) (*BatchGetGroupResp, error)
	TGroupGetUserCreateGroupCount(ctx context.Context, uin uint32, in *TGroupGetUserCreateGroupCountReq, opts ...svrkit.CallOption) (*TGroupGetUserCreateGroupCountResp, error)
	// 根据gameID获取备选的推荐公会ID列表
	GetRecommendGuildIDListByGameID(ctx context.Context, uin uint32, in *GetRecommendGuildIDListByGameIDReq, opts ...svrkit.CallOption) (*GetRecommendGuildIDListByGameIDResp, error)
	ModifyTGroupDesc(ctx context.Context, uin uint32, in *ModifyTGroupDescReq, opts ...svrkit.CallOption) (*ModifyTGroupDescResp, error)
	GetTGroupByDisplayId(ctx context.Context, uin uint32, in *GetTGroupByDisplayIdReq, opts ...svrkit.CallOption) (*GetTGroupByDisplayIdResp, error)
	GetUserGroupIDListByGuildID(ctx context.Context, uin uint32, in *GetUserGroupIDListByGuildIDReq, opts ...svrkit.CallOption) (*GetUserGroupIDListByGuildIDResp, error)
	TGroupSearchByGameId(ctx context.Context, uin uint32, in *TGroupSearchByGameIdReq, opts ...svrkit.CallOption) (*TGroupSearchByGameIdResp, error)
	TGroupGetUserJoinGroupList(ctx context.Context, uin uint32, in *TGroupGetUserJoinGroupListReq, opts ...svrkit.CallOption) (*TGroupGetUserJoinGroupListResp, error)
	GetGroupMuteUserList(ctx context.Context, uin uint32, in *GetGroupMuteUserListReq, opts ...svrkit.CallOption) (*GetGroupMuteUserListResp, error)
	GetAllGroupIdList(ctx context.Context, uin uint32, in *GetAllGroupIdListReq, opts ...svrkit.CallOption) (*GetAllGroupIdListResp, error)
	GetGroupMembersByUids(ctx context.Context, uin uint32, in *GetGroupMembersByUidsReq, opts ...svrkit.CallOption) (*GroupMemberListResp, error)
	// 对指定用户检查所在公会的全部群 获取这些用户在群内管理权限
	CheckUserGroupAdminInfoInAllGroup(ctx context.Context, uin uint32, in *CheckUserGroupAdminInfoInAllGroupReq, opts ...svrkit.CallOption) (*CheckUserGroupAdminInfoInAllGroupResp, error)
	UpdateGuildApplyExceed(ctx context.Context, uin uint32, in *UpdateGuildApplyExceedReq, opts ...svrkit.CallOption) (*UpdateGuildApplyExceedResp, error)
	UpdateGroupApplyExceed(ctx context.Context, uin uint32, in *UpdateGroupApplyExceedReq, opts ...svrkit.CallOption) (*UpdateGroupApplyExceedResp, error)
	GetGroupApplyByGroupId(ctx context.Context, uin uint32, in *GetGroupApplyByGroupIdReq, opts ...svrkit.CallOption) (*GetGroupApplyByGroupIdResp, error)
	// 判断用户是否在公会有头衔(会长 副会长 群主 群管理 or 普通银)
	// 注意如果目标用户不在该公会 也会返回用户没有头衔 为普通成员
	// 也就是该接口只用于判断是否有头衔 不能判断是否在公会
	CheckUserGuildDuty(ctx context.Context, uin uint32, in *CheckUserGuildDutyReq, opts ...svrkit.CallOption) (*CheckUserGuildDutyResp, error)
	GuildCheckinSupplement(ctx context.Context, uin uint32, in *GuildCheckinSupplementReq, opts ...svrkit.CallOption) (*GetGuildCheckinResp, error)
	GroupSetUidNeedVerify(ctx context.Context, uin uint32, in *GroupSetUidNeedVerifyReq, opts ...svrkit.CallOption) (*GroupSetUidNeedVerifyResp, error)
	GroupCheckUidNeedVerify(ctx context.Context, uin uint32, in *GroupCheckUidNeedVerifyReq, opts ...svrkit.CallOption) (*GroupCheckUidNeedVerifyResp, error)
	// 分段获取guild member list
	GetGuildMemberListSegment(ctx context.Context, uin uint32, in *GetGuildMemberListSegmentReq, opts ...svrkit.CallOption) (*GetGuildMemberListSegmentResp, error)
	CreateSpecialGuild(ctx context.Context, uin uint32, in *CreateSpecialGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ModifyGameTopGuild(ctx context.Context, uin uint32, in *ModifyGameTopGuildReq, opts ...svrkit.CallOption) (*ModifyGameTopGuildResp, error)
	ReportMemberGameLogin(ctx context.Context, uin uint32, in *ReportMemberGameLoginReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetMemberRecentGameLogin(ctx context.Context, uin uint32, in *GetMemberRecentGameLoginReq, opts ...svrkit.CallOption) (*GetMemberRecentGameLoginResp, error)
	GuildDonate(ctx context.Context, uin uint32, in *GuildDonateReq, opts ...svrkit.CallOption) (*GetGuildDonateResp, error)
	GetGuildDonate(ctx context.Context, uin uint32, in *GetGuildDonateReq, opts ...svrkit.CallOption) (*GetGuildDonateResp, error)
	GetGuildDonateList(ctx context.Context, uin uint32, in *GetGuildDonateListReq, opts ...svrkit.CallOption) (*GetGuildDonateListResp, error)
	GetGuildDonateCount(ctx context.Context, uin uint32, in *GetGuildDonateCountReq, opts ...svrkit.CallOption) (*GetGuildDonateCountResp, error)
	GetGuildYesterdayDonateCountBat(ctx context.Context, uin uint32, in *GetGuildYesterdayDonateCountBatReq, opts ...svrkit.CallOption) (*GetGuildYesterdayDonateCountBatResp, error)
	SetGuildMemberTitle(ctx context.Context, uin uint32, in *SetGuildMemberTitleReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildGameCountConfig(ctx context.Context, uin uint32, in *GetGuildGameCountConfigReq, opts ...svrkit.CallOption) (*GetGuildGameCountConfigResp, error)
	GetGuildExtraGameCount(ctx context.Context, uin uint32, in *GetGuildExtraGameCountReq, opts ...svrkit.CallOption) (*GetGuildExtraGameCountResp, error)
	AddGuildGameV2(ctx context.Context, uin uint32, in *AddGuildGameV2Req, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ModifyGroupGame(ctx context.Context, uin uint32, in *ModifyGroupGameReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildMemberTitleList(ctx context.Context, uin uint32, in *GetGuildMemberListSegmentReq, opts ...svrkit.CallOption) (*GuildMemberListResp, error)
	GetGuildCheckinListByUids(ctx context.Context, uin uint32, in *GetGuildCheckinListByUidsReq, opts ...svrkit.CallOption) (*GetGuildCheckinListResp, error)
	AddGuildExtraGameCount(ctx context.Context, uin uint32, in *AddGuildExtraGameCountReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	CreateGuildOfficial(ctx context.Context, uin uint32, in *CreateGuildOfficialReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelGuildOfficial(ctx context.Context, uin uint32, in *DelGuildOfficialReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	ModifyGuildOfficial(ctx context.Context, uin uint32, in *ModifyGuildOfficialReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildOfficialAll(ctx context.Context, uin uint32, in *GetGuildOfficialAllReq, opts ...svrkit.CallOption) (*GetGuildOfficialAllResp, error)
	AddGuildOfficialMember(ctx context.Context, uin uint32, in *AddGuildOfficialMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	RemoveGuildOfficialMember(ctx context.Context, uin uint32, in *RemoveGuildOfficialMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildOfficialMember(ctx context.Context, uin uint32, in *GetGuildOfficialMemberReq, opts ...svrkit.CallOption) (*GetGuildOfficialMemberResp, error)
	GetGuildOfficialByUid(ctx context.Context, uin uint32, in *GetGuildOfficialByUidReq, opts ...svrkit.CallOption) (*GetGuildOfficialByUidResp, error)
	GetGuildOfficialMemberMap(ctx context.Context, uin uint32, in *GetGuildOfficialMemberMapReq, opts ...svrkit.CallOption) (*GetGuildOfficialMemberMapResp, error)
	AddGuildOfficialMemberByName(ctx context.Context, uin uint32, in *AddGuildOfficialMemberByNameReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildOfficialById(ctx context.Context, uin uint32, in *GetGuildOfficialByIdReq, opts ...svrkit.CallOption) (*GetGuildOfficialByIdResp, error)
	UpdateGameGuildStarLevel(ctx context.Context, uin uint32, in *UpdateGameGuildStarLevelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	UpdateGameGuildStarLevelBat(ctx context.Context, uin uint32, in *UpdateGameGuildStarLevelBatReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildByMultiCond(ctx context.Context, uin uint32, in *GetGuildByMultiCondReq, opts ...svrkit.CallOption) (*GetGuildByMultiCondResp, error)
	GuildDeleteGames(ctx context.Context, uin uint32, in *GuildDeleteGamesReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取指定公会的 成员加入的历史流水记录
	GetGuildJoinHistory(ctx context.Context, uin uint32, in *GetGuildJoinHistoryReq, opts ...svrkit.CallOption) (*GetGuildJoinHistoryResp, error)
	GetGuildGameListOld(ctx context.Context, uin uint32, in *GuildIdReq, opts ...svrkit.CallOption) (*GetGuildGameListResp, error)
	GetGuildPopGames(ctx context.Context, uin uint32, in *GetGuildPopGamesReq, opts ...svrkit.CallOption) (*GetGuildPopGamesResp, error)
	// 获取 指定用户的 加入公会的历史流水记录
	GetUserJoinGuildHistory(ctx context.Context, uin uint32, in *GetUserJoinGuildHistoryReq, opts ...svrkit.CallOption) (*GetUserJoinGuildHistoryResp, error)
	// 修公会名数据用
	ResetGuildName(ctx context.Context, uin uint32, in *ResetGuildNameReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGroupByIds(ctx context.Context, uin uint32, in *GetGroupByIdsReq, opts ...svrkit.CallOption) (*GetGroupByIdsResp, error)
	// 加入公会黑名单
	SetGuildBlackList(ctx context.Context, uin uint32, in *SetGuildBlackListReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 解除公会黑名单
	RemoveGuildBlackList(ctx context.Context, uin uint32, in *RemoveGuildBlackListReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取公会黑名单
	GetGuildBlackList(ctx context.Context, uin uint32, in *GetGuildBlackListReq, opts ...svrkit.CallOption) (*GetGuildBlackListResp, error)
	// 用户是否在公会黑名单
	CheckUidInBlackList(ctx context.Context, uin uint32, in *CheckUidInBlackListReq, opts ...svrkit.CallOption) (*CheckUidInBlackListResp, error)
	// 公会状态
	GetGuildStatus(ctx context.Context, uin uint32, in *GetGuildStatusReq, opts ...svrkit.CallOption) (*GetGuildStatusRsp, error)
	GetTGroupIdByDisplayId(ctx context.Context, uin uint32, in *GetTGroupIdByDisplayIdReq, opts ...svrkit.CallOption) (*GetTGroupIdByDisplayIdResp, error)
	// 获取某种/些类型的所有group
	GetGroupIdsByType(ctx context.Context, uin uint32, in *GetGroupIdsByTypeReq, opts ...svrkit.CallOption) (*GetGroupIdsByTypeResp, error)
	// 查询tgroup禁言列表
	GetTGroupMuteList(ctx context.Context, uin uint32, in *GetTGroupMuteListReq, opts ...svrkit.CallOption) (*GetTGroupMuteListResp, error)
	DeleteGuildTrans(ctx context.Context, uin uint32, in *DeleteGuildTransReq, opts ...svrkit.CallOption) (*DeleteGuildTransResp, error)
	BatchDeleteGuildTrans(ctx context.Context, uin uint32, in *BatchDeleteGuildTransReq, opts ...svrkit.CallOption) (*BatchDeleteGuildTransResp, error)
	GetGuildTrans(ctx context.Context, uin uint32, in *GetGuildTransReq, opts ...svrkit.CallOption) (*GetGuildTransResp, error)
	BatchGetGuildTrans(ctx context.Context, uin uint32, in *BatchGetGuildTransReq, opts ...svrkit.CallOption) (*BatchGetGuildTransResp, error)
	// 批量检查公会成员的官员权限信息
	BatGetGuildOfficialByUids(ctx context.Context, uin uint32, in *BatGetGuildOfficialByUidsReq, opts ...svrkit.CallOption) (*BatGetGuildOfficialByUidsResp, error)
	// 获取公会内 有职权的人的列表 （包括会长 各类公会管理员 群主 群管理）
	GetGuildAllDutyList(ctx context.Context, uin uint32, in *GetGuildAllDutyListReq, opts ...svrkit.CallOption) (*GetGuildAllDutyListResp, error)
}

type GuildSvrkitClient struct {
	cc *svrkit.ClientConn
}

func NewGuildSvrkitClient(cc *svrkit.ClientConn) GuildClientInterface {
	return &GuildSvrkitClient{cc}
}

const (
	commandGuildGetSelfSvnInfo                    = 9995
	commandGuildEcho                              = 9999
	commandGuildCreateGuild                       = 1
	commandGuildGetGuild                          = 2
	commandGuildSetShortId                        = 3
	commandGuildCheckNameExist                    = 4
	commandGuildGuildAddGame                      = 5
	commandGuildCheckGuildChairman                = 6
	commandGuildGetGuildMember                    = 7
	commandGuildGetGuildGameList                  = 8
	commandGuildApplyGuild                        = 9
	commandGuildGetGuildApply                     = 10
	commandGuildGetGuildMemberList                = 11
	commandGuildReviewApplyGuild                  = 12
	commandGuildGetUserGuildApplyList             = 13
	commandGuildGetGroupMemberList                = 15
	commandGuildAddGroupMember                    = 16
	commandGuildGetGroup                          = 17
	commandGuildCreateGroup                       = 18
	commandGuildUpdateGroupName                   = 19
	commandGuildRemoveGroupMember                 = 20
	commandGuildGetGroupMember                    = 21
	commandGuildGuildCheckin                      = 22
	commandGuildGetGuildCheckin                   = 23
	commandGuildGetGuildCheckinList               = 24
	commandGuildGetGuildCheckinCount              = 25
	commandGuildRemoveGuildMember                 = 26
	commandGuildSetGuildMemberToAdmin             = 27
	commandGuildSetGuildAdminToMember             = 28
	commandGuildSetGroupMemberToAdmin             = 29
	commandGuildSetGroupAdminToMember             = 30
	commandGuildApplyGroup                        = 31
	commandGuildGetGroupApply                     = 32
	commandGuildReviewApplyGroup                  = 33
	commandGuildSetGroupMemberToOwner             = 34
	commandGuildGetUserGroupList                  = 35
	commandGuildUpdateGroupMemberMute             = 36
	commandGuildSearchGuild                       = 37
	commandGuildUpdateGroupMemberCard             = 38
	commandGuildGameGuildIdList                   = 39
	commandGuildUpdateGroupNeedVerify             = 40
	commandGuildPublishGuildBulletin              = 41
	commandGuildGetGuildBulletinList              = 42
	commandGuildUpdateGuild                       = 43
	commandGuildUpdateGroupAllMute                = 44
	commandGuildUpdateGuildMemberPermission       = 45
	commandGuildDeleteGuildBulletin               = 46
	commandGuildUpdateGuildGameOrder              = 47
	commandGuildSetGroupOwnerToMember             = 48
	commandGuildGetGuildGroupList                 = 49
	commandGuildGetGuildGame                      = 50
	commandGuildSetGuildGameUrl                   = 51
	commandGuildDismissGuild                      = 52
	commandGuildGuildModfiyVerify                 = 53
	commandGuildGuildDirectJoin                   = 54
	commandGuildGetGuildYesterdayCheckinCountBat  = 55
	commandGuildGetGuildBat                       = 56
	commandGuildSetGuildGameUseCustomUrl          = 57
	commandGuildDismissGroup                      = 58
	commandGuildGetGuildGamesUserCount            = 59
	commandGuildMarkGuildApplySendMsgSuccess      = 60
	commandGuildGetUserCurrentGuildApply          = 61
	commandGuildGuildGameDownloadReport           = 62
	commandGuildGetGuildMemberGameDownloadList    = 63
	commandGuildSetGroupNotRecvMsg                = 64
	commandGuildModifyGuildName                   = 65
	commandGuildGetGuildMemberListByUids          = 66
	commandGuildCountGameGuild                    = 67
	commandGuildGetAllGuildIdList                 = 68
	commandGuildUpdateGameGuildUserCount          = 69
	commandGuildTopGameGuild                      = 70
	commandGuildGuildDeleteGame                   = 71
	commandGuildGuildModifyGameGroupOrder         = 72
	commandGuildGuildRecoverGame                  = 73
	commandGuildGuildGetGroupOrder                = 74
	commandGuildGetGuildMemberJoinedCount         = 75
	commandGuildUpdateGameGuildOnlineUserCount    = 76
	commandGuildGetAllDeletedGuildIdList          = 77
	commandGuildGuildSetUidNeedVerify             = 78
	commandGuildGuildCheckUidNeedVerify           = 79
	commandGuildGetTopGuildIdList                 = 80
	commandGuildUpdateGuildGameLimit              = 81
	commandGuildUnSetShortId                      = 82
	commandGuildBatchGetGroup                     = 83
	commandGuildTGroupGetUserCreateGroupCount     = 84
	commandGuildGetRecommendGuildIDListByGameID   = 85
	commandGuildModifyTGroupDesc                  = 86
	commandGuildGetTGroupByDisplayId              = 87
	commandGuildGetUserGroupIDListByGuildID       = 88
	commandGuildTGroupSearchByGameId              = 89
	commandGuildTGroupGetUserJoinGroupList        = 90
	commandGuildGetGroupMuteUserList              = 91
	commandGuildGetAllGroupIdList                 = 92
	commandGuildGetGroupMembersByUids             = 93
	commandGuildCheckUserGroupAdminInfoInAllGroup = 94
	commandGuildUpdateGuildApplyExceed            = 95
	commandGuildUpdateGroupApplyExceed            = 96
	commandGuildGetGroupApplyByGroupId            = 97
	commandGuildCheckUserGuildDuty                = 98
	commandGuildGuildCheckinSupplement            = 99
	commandGuildGroupSetUidNeedVerify             = 100
	commandGuildGroupCheckUidNeedVerify           = 101
	commandGuildGetGuildMemberListSegment         = 111
	commandGuildCreateSpecialGuild                = 112
	commandGuildModifyGameTopGuild                = 113
	commandGuildReportMemberGameLogin             = 114
	commandGuildGetMemberRecentGameLogin          = 115
	commandGuildGuildDonate                       = 116
	commandGuildGetGuildDonate                    = 117
	commandGuildGetGuildDonateList                = 118
	commandGuildGetGuildDonateCount               = 119
	commandGuildGetGuildYesterdayDonateCountBat   = 120
	commandGuildSetGuildMemberTitle               = 121
	commandGuildGetGuildGameCountConfig           = 122
	commandGuildGetGuildExtraGameCount            = 123
	commandGuildAddGuildGameV2                    = 124
	commandGuildModifyGroupGame                   = 125
	commandGuildGetGuildMemberTitleList           = 126
	commandGuildGetGuildCheckinListByUids         = 127
	commandGuildAddGuildExtraGameCount            = 128
	commandGuildCreateGuildOfficial               = 129
	commandGuildDelGuildOfficial                  = 130
	commandGuildModifyGuildOfficial               = 131
	commandGuildGetGuildOfficialAll               = 132
	commandGuildAddGuildOfficialMember            = 133
	commandGuildRemoveGuildOfficialMember         = 134
	commandGuildGetGuildOfficialMember            = 135
	commandGuildGetGuildOfficialByUid             = 136
	commandGuildGetGuildOfficialMemberMap         = 137
	commandGuildAddGuildOfficialMemberByName      = 138
	commandGuildGetGuildOfficialById              = 139
	commandGuildUpdateGameGuildStarLevel          = 140
	commandGuildUpdateGameGuildStarLevelBat       = 141
	commandGuildGetGuildByMultiCond               = 142
	commandGuildGuildDeleteGames                  = 143
	commandGuildGetGuildJoinHistory               = 144
	commandGuildGetGuildGameListOld               = 145
	commandGuildGetGuildPopGames                  = 146
	commandGuildGetUserJoinGuildHistory           = 147
	commandGuildResetGuildName                    = 148
	commandGuildGetGroupByIds                     = 149
	commandGuildSetGuildBlackList                 = 150
	commandGuildRemoveGuildBlackList              = 151
	commandGuildGetGuildBlackList                 = 152
	commandGuildCheckUidInBlackList               = 153
	commandGuildGetGuildStatus                    = 155
	commandGuildGetTGroupIdByDisplayId            = 156
	commandGuildGetGroupIdsByType                 = 157
	commandGuildGetTGroupMuteList                 = 158
	commandGuildDeleteGuildTrans                  = 159
	commandGuildBatchDeleteGuildTrans             = 160
	commandGuildGetGuildTrans                     = 161
	commandGuildBatchGetGuildTrans                = 162
	commandGuildBatGetGuildOfficialByUids         = 163
	commandGuildGetGuildAllDutyList               = 164
)

func (c *GuildSvrkitClient) CreateGuild(ctx context.Context, uin uint32, in *CreateGuildReq, opts ...svrkit.CallOption) (*CreateGuildResp, error) {
	out := new(CreateGuildResp)
	err := c.cc.Invoke(ctx, uin, commandGuildCreateGuild, "/Guild.Guild/CreateGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuild(ctx context.Context, uin uint32, in *GuildIdReq, opts ...svrkit.CallOption) (*GuildResp, error) {
	out := new(GuildResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuild, "/Guild.Guild/GetGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetShortId(ctx context.Context, uin uint32, in *SetShortIdReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildSetShortId, "/Guild.Guild/SetShortId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) CheckNameExist(ctx context.Context, uin uint32, in *CheckNameExistReq, opts ...svrkit.CallOption) (*CheckNameExistResp, error) {
	out := new(CheckNameExistResp)
	err := c.cc.Invoke(ctx, uin, commandGuildCheckNameExist, "/Guild.Guild/CheckNameExist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildAddGame(ctx context.Context, uin uint32, in *GuildAddGameReq, opts ...svrkit.CallOption) (*GuildAddGameResp, error) {
	out := new(GuildAddGameResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildAddGame, "/Guild.Guild/GuildAddGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) CheckGuildChairman(ctx context.Context, uin uint32, in *CheckGuildChairmanReq, opts ...svrkit.CallOption) (*CheckGuildChairmanResp, error) {
	out := new(CheckGuildChairmanResp)
	err := c.cc.Invoke(ctx, uin, commandGuildCheckGuildChairman, "/Guild.Guild/CheckGuildChairman", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildMember(ctx context.Context, uin uint32, in *GetGuildMemberReq, opts ...svrkit.CallOption) (*GuildMemberResp, error) {
	out := new(GuildMemberResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildMember, "/Guild.Guild/GetGuildMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildGameList(ctx context.Context, uin uint32, in *GuildIdReq, opts ...svrkit.CallOption) (*GetGuildGameListResp, error) {
	out := new(GetGuildGameListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildGameList, "/Guild.Guild/GetGuildGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ApplyGuild(ctx context.Context, uin uint32, in *ApplyGuildReq, opts ...svrkit.CallOption) (*ApplyGuildResp, error) {
	out := new(ApplyGuildResp)
	err := c.cc.Invoke(ctx, uin, commandGuildApplyGuild, "/Guild.Guild/ApplyGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildApply(ctx context.Context, uin uint32, in *GetGuildApplyReq, opts ...svrkit.CallOption) (*GuildApplyResp, error) {
	out := new(GuildApplyResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildApply, "/Guild.Guild/GetGuildApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildMemberList(ctx context.Context, uin uint32, in *GetGuildMemberListReq, opts ...svrkit.CallOption) (*GuildMemberListResp, error) {
	out := new(GuildMemberListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildMemberList, "/Guild.Guild/GetGuildMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ReviewApplyGuild(ctx context.Context, uin uint32, in *ReviewApplyGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildReviewApplyGuild, "/Guild.Guild/ReviewApplyGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetUserGuildApplyList(ctx context.Context, uin uint32, in *GetUserGuildApplyListReq, opts ...svrkit.CallOption) (*GetUserGuildApplyListResp, error) {
	out := new(GetUserGuildApplyListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetUserGuildApplyList, "/Guild.Guild/GetUserGuildApplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGroupMemberList(ctx context.Context, uin uint32, in *GetGroupMemberListReq, opts ...svrkit.CallOption) (*GroupMemberListResp, error) {
	out := new(GroupMemberListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGroupMemberList, "/Guild.Guild/GetGroupMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) AddGroupMember(ctx context.Context, uin uint32, in *AddGroupMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildAddGroupMember, "/Guild.Guild/AddGroupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGroup(ctx context.Context, uin uint32, in *GetGroupReq, opts ...svrkit.CallOption) (*GroupResp, error) {
	out := new(GroupResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGroup, "/Guild.Guild/GetGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) CreateGroup(ctx context.Context, uin uint32, in *CreateGroupReq, opts ...svrkit.CallOption) (*CreateGroupResp, error) {
	out := new(CreateGroupResp)
	err := c.cc.Invoke(ctx, uin, commandGuildCreateGroup, "/Guild.Guild/CreateGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGroupName(ctx context.Context, uin uint32, in *UpdateGroupNameReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGroupName, "/Guild.Guild/UpdateGroupName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) RemoveGroupMember(ctx context.Context, uin uint32, in *RemoveGroupMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildRemoveGroupMember, "/Guild.Guild/RemoveGroupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGroupMember(ctx context.Context, uin uint32, in *GetGroupMemberReq, opts ...svrkit.CallOption) (*GroupMemberResp, error) {
	out := new(GroupMemberResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGroupMember, "/Guild.Guild/GetGroupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildCheckin(ctx context.Context, uin uint32, in *GuildCheckinReq, opts ...svrkit.CallOption) (*GetGuildCheckinResp, error) {
	out := new(GetGuildCheckinResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildCheckin, "/Guild.Guild/GuildCheckin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildCheckin(ctx context.Context, uin uint32, in *GetGuildCheckinReq, opts ...svrkit.CallOption) (*GetGuildCheckinResp, error) {
	out := new(GetGuildCheckinResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildCheckin, "/Guild.Guild/GetGuildCheckin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildCheckinList(ctx context.Context, uin uint32, in *GetGuildCheckinListReq, opts ...svrkit.CallOption) (*GetGuildCheckinListResp, error) {
	out := new(GetGuildCheckinListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildCheckinList, "/Guild.Guild/GetGuildCheckinList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildCheckinCount(ctx context.Context, uin uint32, in *GetGuildCheckinCountReq, opts ...svrkit.CallOption) (*GetGuildCheckinCountResp, error) {
	out := new(GetGuildCheckinCountResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildCheckinCount, "/Guild.Guild/GetGuildCheckinCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) RemoveGuildMember(ctx context.Context, uin uint32, in *RemoveGuildMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildRemoveGuildMember, "/Guild.Guild/RemoveGuildMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGuildMemberToAdmin(ctx context.Context, uin uint32, in *SetGuildMemberToAdminReq, opts ...svrkit.CallOption) (*SetGuildMemberToAdminResp, error) {
	out := new(SetGuildMemberToAdminResp)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGuildMemberToAdmin, "/Guild.Guild/SetGuildMemberToAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGuildAdminToMember(ctx context.Context, uin uint32, in *SetGuildAdminToMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGuildAdminToMember, "/Guild.Guild/SetGuildAdminToMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGroupMemberToAdmin(ctx context.Context, uin uint32, in *SetGroupMemberToAdminReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGroupMemberToAdmin, "/Guild.Guild/SetGroupMemberToAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGroupAdminToMember(ctx context.Context, uin uint32, in *SetGroupAdminToMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGroupAdminToMember, "/Guild.Guild/SetGroupAdminToMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ApplyGroup(ctx context.Context, uin uint32, in *ApplyGroupReq, opts ...svrkit.CallOption) (*ApplyGroupResp, error) {
	out := new(ApplyGroupResp)
	err := c.cc.Invoke(ctx, uin, commandGuildApplyGroup, "/Guild.Guild/ApplyGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGroupApply(ctx context.Context, uin uint32, in *GetGroupApplyReq, opts ...svrkit.CallOption) (*GroupApplyResp, error) {
	out := new(GroupApplyResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGroupApply, "/Guild.Guild/GetGroupApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ReviewApplyGroup(ctx context.Context, uin uint32, in *ReviewApplyGroupReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildReviewApplyGroup, "/Guild.Guild/ReviewApplyGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGroupMemberToOwner(ctx context.Context, uin uint32, in *SetGroupMemberToOwnerReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGroupMemberToOwner, "/Guild.Guild/SetGroupMemberToOwner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetUserGroupList(ctx context.Context, uin uint32, in *GetUserGroupListReq, opts ...svrkit.CallOption) (*GroupListResp, error) {
	out := new(GroupListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetUserGroupList, "/Guild.Guild/GetUserGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGroupMemberMute(ctx context.Context, uin uint32, in *UpdateGroupMemberMuteReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGroupMemberMute, "/Guild.Guild/UpdateGroupMemberMute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SearchGuild(ctx context.Context, uin uint32, in *SearchGuildReq, opts ...svrkit.CallOption) (*GuildListResp, error) {
	out := new(GuildListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildSearchGuild, "/Guild.Guild/SearchGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGroupMemberCard(ctx context.Context, uin uint32, in *UpdateGroupMemberCardReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGroupMemberCard, "/Guild.Guild/UpdateGroupMemberCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GameGuildIdList(ctx context.Context, uin uint32, in *GameGuildIdListReq, opts ...svrkit.CallOption) (*GameGuildIdListResp, error) {
	out := new(GameGuildIdListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGameGuildIdList, "/Guild.Guild/GameGuildIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGroupNeedVerify(ctx context.Context, uin uint32, in *UpdateGroupNeedVerifyReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGroupNeedVerify, "/Guild.Guild/UpdateGroupNeedVerify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) PublishGuildBulletin(ctx context.Context, uin uint32, in *PublishGuildBulletinReq, opts ...svrkit.CallOption) (*PublishGuildBulletinResp, error) {
	out := new(PublishGuildBulletinResp)
	err := c.cc.Invoke(ctx, uin, commandGuildPublishGuildBulletin, "/Guild.Guild/PublishGuildBulletin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildBulletinList(ctx context.Context, uin uint32, in *GetGuildBulletinListReq, opts ...svrkit.CallOption) (*GetGuildBulletinListResp, error) {
	out := new(GetGuildBulletinListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildBulletinList, "/Guild.Guild/GetGuildBulletinList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGuild(ctx context.Context, uin uint32, in *UpdateGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGuild, "/Guild.Guild/UpdateGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGroupAllMute(ctx context.Context, uin uint32, in *UpdateGroupAllMuteReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGroupAllMute, "/Guild.Guild/UpdateGroupAllMute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGuildMemberPermission(ctx context.Context, uin uint32, in *UpdateGuildMemberPermissionReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGuildMemberPermission, "/Guild.Guild/UpdateGuildMemberPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) DeleteGuildBulletin(ctx context.Context, uin uint32, in *DeleteGuildBulletinReq, opts ...svrkit.CallOption) (*DeleteGuildBulletinResp, error) {
	out := new(DeleteGuildBulletinResp)
	err := c.cc.Invoke(ctx, uin, commandGuildDeleteGuildBulletin, "/Guild.Guild/DeleteGuildBulletin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGuildGameOrder(ctx context.Context, uin uint32, in *UpdateGuildGameOrderReq, opts ...svrkit.CallOption) (*UpdateGuildGameOrderResp, error) {
	out := new(UpdateGuildGameOrderResp)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGuildGameOrder, "/Guild.Guild/UpdateGuildGameOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGroupOwnerToMember(ctx context.Context, uin uint32, in *SetGroupOwnerToMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGroupOwnerToMember, "/Guild.Guild/SetGroupOwnerToMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildGroupList(ctx context.Context, uin uint32, in *GetGuildGroupListReq, opts ...svrkit.CallOption) (*GroupIdListResp, error) {
	out := new(GroupIdListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildGroupList, "/Guild.Guild/GetGuildGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildGame(ctx context.Context, uin uint32, in *GetGuildGameReq, opts ...svrkit.CallOption) (*GetGuildGameResp, error) {
	out := new(GetGuildGameResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildGame, "/Guild.Guild/GetGuildGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGuildGameUrl(ctx context.Context, uin uint32, in *SetGuildGameUrlReq, opts ...svrkit.CallOption) (*SetGuildGameUrlResp, error) {
	out := new(SetGuildGameUrlResp)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGuildGameUrl, "/Guild.Guild/SetGuildGameUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) DismissGuild(ctx context.Context, uin uint32, in *DismissGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildDismissGuild, "/Guild.Guild/DismissGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildModfiyVerify(ctx context.Context, uin uint32, in *GuildModfiyVerifyReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildModfiyVerify, "/Guild.Guild/GuildModfiyVerify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildDirectJoin(ctx context.Context, uin uint32, in *GuildDirectJoinReq, opts ...svrkit.CallOption) (*GuildDirectJoinResp, error) {
	out := new(GuildDirectJoinResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildDirectJoin, "/Guild.Guild/GuildDirectJoin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildYesterdayCheckinCountBat(ctx context.Context, uin uint32, in *GetGuildYesterdayCheckinCountBatReq, opts ...svrkit.CallOption) (*GetGuildYesterdayCheckinCountBatResp, error) {
	out := new(GetGuildYesterdayCheckinCountBatResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildYesterdayCheckinCountBat, "/Guild.Guild/GetGuildYesterdayCheckinCountBat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildBat(ctx context.Context, uin uint32, in *GetGuildBatReq, opts ...svrkit.CallOption) (*GetGuildBatResp, error) {
	out := new(GetGuildBatResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildBat, "/Guild.Guild/GetGuildBat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGuildGameUseCustomUrl(ctx context.Context, uin uint32, in *SetGuildGameUseCustomUrlReq, opts ...svrkit.CallOption) (*SetGuildGameUseCustomUrlResp, error) {
	out := new(SetGuildGameUseCustomUrlResp)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGuildGameUseCustomUrl, "/Guild.Guild/SetGuildGameUseCustomUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) DismissGroup(ctx context.Context, uin uint32, in *DismissGroupReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildDismissGroup, "/Guild.Guild/DismissGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildGamesUserCount(ctx context.Context, uin uint32, in *GetGuildGamesUserCountReq, opts ...svrkit.CallOption) (*GetGuildGamesUserCountResp, error) {
	out := new(GetGuildGamesUserCountResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildGamesUserCount, "/Guild.Guild/GetGuildGamesUserCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) MarkGuildApplySendMsgSuccess(ctx context.Context, uin uint32, in *MarkGuildApplySendMsgSuccessReq, opts ...svrkit.CallOption) (*MarkGuildApplySendMsgSuccessResp, error) {
	out := new(MarkGuildApplySendMsgSuccessResp)
	err := c.cc.Invoke(ctx, uin, commandGuildMarkGuildApplySendMsgSuccess, "/Guild.Guild/MarkGuildApplySendMsgSuccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetUserCurrentGuildApply(ctx context.Context, uin uint32, in *GetUserCurrentGuildApplyReq, opts ...svrkit.CallOption) (*GetUserCurrentGuildApplyResp, error) {
	out := new(GetUserCurrentGuildApplyResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetUserCurrentGuildApply, "/Guild.Guild/GetUserCurrentGuildApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildGameDownloadReport(ctx context.Context, uin uint32, in *GuildGameDownloadReportReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildGameDownloadReport, "/Guild.Guild/GuildGameDownloadReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildMemberGameDownloadList(ctx context.Context, uin uint32, in *GetMemberGameDownloadListReq, opts ...svrkit.CallOption) (*GetMemberGameDownloadListResp, error) {
	out := new(GetMemberGameDownloadListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildMemberGameDownloadList, "/Guild.Guild/GetGuildMemberGameDownloadList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGroupNotRecvMsg(ctx context.Context, uin uint32, in *SetGroupNotRecvMsgReq, opts ...svrkit.CallOption) (*SetGroupNotRecvMsgResp, error) {
	out := new(SetGroupNotRecvMsgResp)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGroupNotRecvMsg, "/Guild.Guild/SetGroupNotRecvMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ModifyGuildName(ctx context.Context, uin uint32, in *ModifyGuildNameReq, opts ...svrkit.CallOption) (*ModifyGuildNameResp, error) {
	out := new(ModifyGuildNameResp)
	err := c.cc.Invoke(ctx, uin, commandGuildModifyGuildName, "/Guild.Guild/ModifyGuildName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildMemberListByUids(ctx context.Context, uin uint32, in *GetGuildMemberListByUidsReq, opts ...svrkit.CallOption) (*GuildMemberListResp, error) {
	out := new(GuildMemberListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildMemberListByUids, "/Guild.Guild/GetGuildMemberListByUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) CountGameGuild(ctx context.Context, uin uint32, in *CountGameGuildReq, opts ...svrkit.CallOption) (*CountGameGuildResp, error) {
	out := new(CountGameGuildResp)
	err := c.cc.Invoke(ctx, uin, commandGuildCountGameGuild, "/Guild.Guild/CountGameGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetAllGuildIdList(ctx context.Context, uin uint32, in *GetAllGuildIdListReq, opts ...svrkit.CallOption) (*GetAllGuildIdListResp, error) {
	out := new(GetAllGuildIdListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetAllGuildIdList, "/Guild.Guild/GetAllGuildIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGameGuildUserCount(ctx context.Context, uin uint32, in *UpdateGameGuildUserCountReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGameGuildUserCount, "/Guild.Guild/UpdateGameGuildUserCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) TopGameGuild(ctx context.Context, uin uint32, in *TopGameGuildReq, opts ...svrkit.CallOption) (*GuildListResp, error) {
	out := new(GuildListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildTopGameGuild, "/Guild.Guild/TopGameGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildDeleteGame(ctx context.Context, uin uint32, in *GuildDeleteGameReq, opts ...svrkit.CallOption) (*GuildDeleteGameResp, error) {
	out := new(GuildDeleteGameResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildDeleteGame, "/Guild.Guild/GuildDeleteGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildModifyGameGroupOrder(ctx context.Context, uin uint32, in *GuildModifyGameGroupOrderReq, opts ...svrkit.CallOption) (*GuildModifyGameGroupOrderResp, error) {
	out := new(GuildModifyGameGroupOrderResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildModifyGameGroupOrder, "/Guild.Guild/GuildModifyGameGroupOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildRecoverGame(ctx context.Context, uin uint32, in *GuildRecoverGameReq, opts ...svrkit.CallOption) (*GuildRecoverGameResp, error) {
	out := new(GuildRecoverGameResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildRecoverGame, "/Guild.Guild/GuildRecoverGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildGetGroupOrder(ctx context.Context, uin uint32, in *GuildGetGroupOrderReq, opts ...svrkit.CallOption) (*GuildGetGroupOrderResp, error) {
	out := new(GuildGetGroupOrderResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildGetGroupOrder, "/Guild.Guild/GuildGetGroupOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildMemberJoinedCount(ctx context.Context, uin uint32, in *GetGuildMemberJoinedCountReq, opts ...svrkit.CallOption) (*GetGuildMemberJoinedCountResp, error) {
	out := new(GetGuildMemberJoinedCountResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildMemberJoinedCount, "/Guild.Guild/GetGuildMemberJoinedCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGameGuildOnlineUserCount(ctx context.Context, uin uint32, in *UpdateGameGuildOnlineUserCountReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGameGuildOnlineUserCount, "/Guild.Guild/UpdateGameGuildOnlineUserCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetAllDeletedGuildIdList(ctx context.Context, uin uint32, in *GetAllDeletedGuildIdListReq, opts ...svrkit.CallOption) (*GetAllDeletedGuildIdListResp, error) {
	out := new(GetAllDeletedGuildIdListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetAllDeletedGuildIdList, "/Guild.Guild/GetAllDeletedGuildIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildSetUidNeedVerify(ctx context.Context, uin uint32, in *GuildSetUidNeedVerifyReq, opts ...svrkit.CallOption) (*GuildSetUidNeedVerifyResp, error) {
	out := new(GuildSetUidNeedVerifyResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildSetUidNeedVerify, "/Guild.Guild/GuildSetUidNeedVerify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildCheckUidNeedVerify(ctx context.Context, uin uint32, in *GuildCheckUidNeedVerifyReq, opts ...svrkit.CallOption) (*GuildCheckUidNeedVerifyResp, error) {
	out := new(GuildCheckUidNeedVerifyResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildCheckUidNeedVerify, "/Guild.Guild/GuildCheckUidNeedVerify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetTopGuildIdList(ctx context.Context, uin uint32, in *GetTopGuildIdListReq, opts ...svrkit.CallOption) (*GetTopGuildIdListResp, error) {
	out := new(GetTopGuildIdListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetTopGuildIdList, "/Guild.Guild/GetTopGuildIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGuildGameLimit(ctx context.Context, uin uint32, in *UpdateGuildGameLimitReq, opts ...svrkit.CallOption) (*UpdateGuildGameLimitResp, error) {
	out := new(UpdateGuildGameLimitResp)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGuildGameLimit, "/Guild.Guild/UpdateGuildGameLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UnSetShortId(ctx context.Context, uin uint32, in *SetShortIdReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUnSetShortId, "/Guild.Guild/UnSetShortId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) BatchGetGroup(ctx context.Context, uin uint32, in *BatchGetGroupReq, opts ...svrkit.CallOption) (*BatchGetGroupResp, error) {
	out := new(BatchGetGroupResp)
	err := c.cc.Invoke(ctx, uin, commandGuildBatchGetGroup, "/Guild.Guild/BatchGetGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) TGroupGetUserCreateGroupCount(ctx context.Context, uin uint32, in *TGroupGetUserCreateGroupCountReq, opts ...svrkit.CallOption) (*TGroupGetUserCreateGroupCountResp, error) {
	out := new(TGroupGetUserCreateGroupCountResp)
	err := c.cc.Invoke(ctx, uin, commandGuildTGroupGetUserCreateGroupCount, "/Guild.Guild/TGroupGetUserCreateGroupCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetRecommendGuildIDListByGameID(ctx context.Context, uin uint32, in *GetRecommendGuildIDListByGameIDReq, opts ...svrkit.CallOption) (*GetRecommendGuildIDListByGameIDResp, error) {
	out := new(GetRecommendGuildIDListByGameIDResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetRecommendGuildIDListByGameID, "/Guild.Guild/GetRecommendGuildIDListByGameID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ModifyTGroupDesc(ctx context.Context, uin uint32, in *ModifyTGroupDescReq, opts ...svrkit.CallOption) (*ModifyTGroupDescResp, error) {
	out := new(ModifyTGroupDescResp)
	err := c.cc.Invoke(ctx, uin, commandGuildModifyTGroupDesc, "/Guild.Guild/ModifyTGroupDesc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetTGroupByDisplayId(ctx context.Context, uin uint32, in *GetTGroupByDisplayIdReq, opts ...svrkit.CallOption) (*GetTGroupByDisplayIdResp, error) {
	out := new(GetTGroupByDisplayIdResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetTGroupByDisplayId, "/Guild.Guild/GetTGroupByDisplayId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetUserGroupIDListByGuildID(ctx context.Context, uin uint32, in *GetUserGroupIDListByGuildIDReq, opts ...svrkit.CallOption) (*GetUserGroupIDListByGuildIDResp, error) {
	out := new(GetUserGroupIDListByGuildIDResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetUserGroupIDListByGuildID, "/Guild.Guild/GetUserGroupIDListByGuildID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) TGroupSearchByGameId(ctx context.Context, uin uint32, in *TGroupSearchByGameIdReq, opts ...svrkit.CallOption) (*TGroupSearchByGameIdResp, error) {
	out := new(TGroupSearchByGameIdResp)
	err := c.cc.Invoke(ctx, uin, commandGuildTGroupSearchByGameId, "/Guild.Guild/TGroupSearchByGameId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) TGroupGetUserJoinGroupList(ctx context.Context, uin uint32, in *TGroupGetUserJoinGroupListReq, opts ...svrkit.CallOption) (*TGroupGetUserJoinGroupListResp, error) {
	out := new(TGroupGetUserJoinGroupListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildTGroupGetUserJoinGroupList, "/Guild.Guild/TGroupGetUserJoinGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGroupMuteUserList(ctx context.Context, uin uint32, in *GetGroupMuteUserListReq, opts ...svrkit.CallOption) (*GetGroupMuteUserListResp, error) {
	out := new(GetGroupMuteUserListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGroupMuteUserList, "/Guild.Guild/GetGroupMuteUserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetAllGroupIdList(ctx context.Context, uin uint32, in *GetAllGroupIdListReq, opts ...svrkit.CallOption) (*GetAllGroupIdListResp, error) {
	out := new(GetAllGroupIdListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetAllGroupIdList, "/Guild.Guild/GetAllGroupIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGroupMembersByUids(ctx context.Context, uin uint32, in *GetGroupMembersByUidsReq, opts ...svrkit.CallOption) (*GroupMemberListResp, error) {
	out := new(GroupMemberListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGroupMembersByUids, "/Guild.Guild/GetGroupMembersByUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) CheckUserGroupAdminInfoInAllGroup(ctx context.Context, uin uint32, in *CheckUserGroupAdminInfoInAllGroupReq, opts ...svrkit.CallOption) (*CheckUserGroupAdminInfoInAllGroupResp, error) {
	out := new(CheckUserGroupAdminInfoInAllGroupResp)
	err := c.cc.Invoke(ctx, uin, commandGuildCheckUserGroupAdminInfoInAllGroup, "/Guild.Guild/CheckUserGroupAdminInfoInAllGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGuildApplyExceed(ctx context.Context, uin uint32, in *UpdateGuildApplyExceedReq, opts ...svrkit.CallOption) (*UpdateGuildApplyExceedResp, error) {
	out := new(UpdateGuildApplyExceedResp)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGuildApplyExceed, "/Guild.Guild/UpdateGuildApplyExceed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGroupApplyExceed(ctx context.Context, uin uint32, in *UpdateGroupApplyExceedReq, opts ...svrkit.CallOption) (*UpdateGroupApplyExceedResp, error) {
	out := new(UpdateGroupApplyExceedResp)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGroupApplyExceed, "/Guild.Guild/UpdateGroupApplyExceed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGroupApplyByGroupId(ctx context.Context, uin uint32, in *GetGroupApplyByGroupIdReq, opts ...svrkit.CallOption) (*GetGroupApplyByGroupIdResp, error) {
	out := new(GetGroupApplyByGroupIdResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGroupApplyByGroupId, "/Guild.Guild/GetGroupApplyByGroupId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) CheckUserGuildDuty(ctx context.Context, uin uint32, in *CheckUserGuildDutyReq, opts ...svrkit.CallOption) (*CheckUserGuildDutyResp, error) {
	out := new(CheckUserGuildDutyResp)
	err := c.cc.Invoke(ctx, uin, commandGuildCheckUserGuildDuty, "/Guild.Guild/CheckUserGuildDuty", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildCheckinSupplement(ctx context.Context, uin uint32, in *GuildCheckinSupplementReq, opts ...svrkit.CallOption) (*GetGuildCheckinResp, error) {
	out := new(GetGuildCheckinResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildCheckinSupplement, "/Guild.Guild/GuildCheckinSupplement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GroupSetUidNeedVerify(ctx context.Context, uin uint32, in *GroupSetUidNeedVerifyReq, opts ...svrkit.CallOption) (*GroupSetUidNeedVerifyResp, error) {
	out := new(GroupSetUidNeedVerifyResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGroupSetUidNeedVerify, "/Guild.Guild/GroupSetUidNeedVerify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GroupCheckUidNeedVerify(ctx context.Context, uin uint32, in *GroupCheckUidNeedVerifyReq, opts ...svrkit.CallOption) (*GroupCheckUidNeedVerifyResp, error) {
	out := new(GroupCheckUidNeedVerifyResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGroupCheckUidNeedVerify, "/Guild.Guild/GroupCheckUidNeedVerify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildMemberListSegment(ctx context.Context, uin uint32, in *GetGuildMemberListSegmentReq, opts ...svrkit.CallOption) (*GetGuildMemberListSegmentResp, error) {
	out := new(GetGuildMemberListSegmentResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildMemberListSegment, "/Guild.Guild/GetGuildMemberListSegment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) CreateSpecialGuild(ctx context.Context, uin uint32, in *CreateSpecialGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildCreateSpecialGuild, "/Guild.Guild/CreateSpecialGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ModifyGameTopGuild(ctx context.Context, uin uint32, in *ModifyGameTopGuildReq, opts ...svrkit.CallOption) (*ModifyGameTopGuildResp, error) {
	out := new(ModifyGameTopGuildResp)
	err := c.cc.Invoke(ctx, uin, commandGuildModifyGameTopGuild, "/Guild.Guild/ModifyGameTopGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ReportMemberGameLogin(ctx context.Context, uin uint32, in *ReportMemberGameLoginReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildReportMemberGameLogin, "/Guild.Guild/ReportMemberGameLogin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetMemberRecentGameLogin(ctx context.Context, uin uint32, in *GetMemberRecentGameLoginReq, opts ...svrkit.CallOption) (*GetMemberRecentGameLoginResp, error) {
	out := new(GetMemberRecentGameLoginResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetMemberRecentGameLogin, "/Guild.Guild/GetMemberRecentGameLogin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildDonate(ctx context.Context, uin uint32, in *GuildDonateReq, opts ...svrkit.CallOption) (*GetGuildDonateResp, error) {
	out := new(GetGuildDonateResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildDonate, "/Guild.Guild/GuildDonate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildDonate(ctx context.Context, uin uint32, in *GetGuildDonateReq, opts ...svrkit.CallOption) (*GetGuildDonateResp, error) {
	out := new(GetGuildDonateResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildDonate, "/Guild.Guild/GetGuildDonate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildDonateList(ctx context.Context, uin uint32, in *GetGuildDonateListReq, opts ...svrkit.CallOption) (*GetGuildDonateListResp, error) {
	out := new(GetGuildDonateListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildDonateList, "/Guild.Guild/GetGuildDonateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildDonateCount(ctx context.Context, uin uint32, in *GetGuildDonateCountReq, opts ...svrkit.CallOption) (*GetGuildDonateCountResp, error) {
	out := new(GetGuildDonateCountResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildDonateCount, "/Guild.Guild/GetGuildDonateCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildYesterdayDonateCountBat(ctx context.Context, uin uint32, in *GetGuildYesterdayDonateCountBatReq, opts ...svrkit.CallOption) (*GetGuildYesterdayDonateCountBatResp, error) {
	out := new(GetGuildYesterdayDonateCountBatResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildYesterdayDonateCountBat, "/Guild.Guild/GetGuildYesterdayDonateCountBat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGuildMemberTitle(ctx context.Context, uin uint32, in *SetGuildMemberTitleReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGuildMemberTitle, "/Guild.Guild/SetGuildMemberTitle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildGameCountConfig(ctx context.Context, uin uint32, in *GetGuildGameCountConfigReq, opts ...svrkit.CallOption) (*GetGuildGameCountConfigResp, error) {
	out := new(GetGuildGameCountConfigResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildGameCountConfig, "/Guild.Guild/GetGuildGameCountConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildExtraGameCount(ctx context.Context, uin uint32, in *GetGuildExtraGameCountReq, opts ...svrkit.CallOption) (*GetGuildExtraGameCountResp, error) {
	out := new(GetGuildExtraGameCountResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildExtraGameCount, "/Guild.Guild/GetGuildExtraGameCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) AddGuildGameV2(ctx context.Context, uin uint32, in *AddGuildGameV2Req, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildAddGuildGameV2, "/Guild.Guild/AddGuildGameV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ModifyGroupGame(ctx context.Context, uin uint32, in *ModifyGroupGameReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildModifyGroupGame, "/Guild.Guild/ModifyGroupGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildMemberTitleList(ctx context.Context, uin uint32, in *GetGuildMemberListSegmentReq, opts ...svrkit.CallOption) (*GuildMemberListResp, error) {
	out := new(GuildMemberListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildMemberTitleList, "/Guild.Guild/GetGuildMemberTitleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildCheckinListByUids(ctx context.Context, uin uint32, in *GetGuildCheckinListByUidsReq, opts ...svrkit.CallOption) (*GetGuildCheckinListResp, error) {
	out := new(GetGuildCheckinListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildCheckinListByUids, "/Guild.Guild/GetGuildCheckinListByUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) AddGuildExtraGameCount(ctx context.Context, uin uint32, in *AddGuildExtraGameCountReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildAddGuildExtraGameCount, "/Guild.Guild/AddGuildExtraGameCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) CreateGuildOfficial(ctx context.Context, uin uint32, in *CreateGuildOfficialReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildCreateGuildOfficial, "/Guild.Guild/CreateGuildOfficial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) DelGuildOfficial(ctx context.Context, uin uint32, in *DelGuildOfficialReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildDelGuildOfficial, "/Guild.Guild/DelGuildOfficial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ModifyGuildOfficial(ctx context.Context, uin uint32, in *ModifyGuildOfficialReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildModifyGuildOfficial, "/Guild.Guild/ModifyGuildOfficial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildOfficialAll(ctx context.Context, uin uint32, in *GetGuildOfficialAllReq, opts ...svrkit.CallOption) (*GetGuildOfficialAllResp, error) {
	out := new(GetGuildOfficialAllResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildOfficialAll, "/Guild.Guild/GetGuildOfficialAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) AddGuildOfficialMember(ctx context.Context, uin uint32, in *AddGuildOfficialMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildAddGuildOfficialMember, "/Guild.Guild/AddGuildOfficialMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) RemoveGuildOfficialMember(ctx context.Context, uin uint32, in *RemoveGuildOfficialMemberReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildRemoveGuildOfficialMember, "/Guild.Guild/RemoveGuildOfficialMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildOfficialMember(ctx context.Context, uin uint32, in *GetGuildOfficialMemberReq, opts ...svrkit.CallOption) (*GetGuildOfficialMemberResp, error) {
	out := new(GetGuildOfficialMemberResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildOfficialMember, "/Guild.Guild/GetGuildOfficialMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildOfficialByUid(ctx context.Context, uin uint32, in *GetGuildOfficialByUidReq, opts ...svrkit.CallOption) (*GetGuildOfficialByUidResp, error) {
	out := new(GetGuildOfficialByUidResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildOfficialByUid, "/Guild.Guild/GetGuildOfficialByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildOfficialMemberMap(ctx context.Context, uin uint32, in *GetGuildOfficialMemberMapReq, opts ...svrkit.CallOption) (*GetGuildOfficialMemberMapResp, error) {
	out := new(GetGuildOfficialMemberMapResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildOfficialMemberMap, "/Guild.Guild/GetGuildOfficialMemberMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) AddGuildOfficialMemberByName(ctx context.Context, uin uint32, in *AddGuildOfficialMemberByNameReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildAddGuildOfficialMemberByName, "/Guild.Guild/AddGuildOfficialMemberByName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildOfficialById(ctx context.Context, uin uint32, in *GetGuildOfficialByIdReq, opts ...svrkit.CallOption) (*GetGuildOfficialByIdResp, error) {
	out := new(GetGuildOfficialByIdResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildOfficialById, "/Guild.Guild/GetGuildOfficialById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGameGuildStarLevel(ctx context.Context, uin uint32, in *UpdateGameGuildStarLevelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGameGuildStarLevel, "/Guild.Guild/UpdateGameGuildStarLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) UpdateGameGuildStarLevelBat(ctx context.Context, uin uint32, in *UpdateGameGuildStarLevelBatReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildUpdateGameGuildStarLevelBat, "/Guild.Guild/UpdateGameGuildStarLevelBat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildByMultiCond(ctx context.Context, uin uint32, in *GetGuildByMultiCondReq, opts ...svrkit.CallOption) (*GetGuildByMultiCondResp, error) {
	out := new(GetGuildByMultiCondResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildByMultiCond, "/Guild.Guild/GetGuildByMultiCond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GuildDeleteGames(ctx context.Context, uin uint32, in *GuildDeleteGamesReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildGuildDeleteGames, "/Guild.Guild/GuildDeleteGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildJoinHistory(ctx context.Context, uin uint32, in *GetGuildJoinHistoryReq, opts ...svrkit.CallOption) (*GetGuildJoinHistoryResp, error) {
	out := new(GetGuildJoinHistoryResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildJoinHistory, "/Guild.Guild/GetGuildJoinHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildGameListOld(ctx context.Context, uin uint32, in *GuildIdReq, opts ...svrkit.CallOption) (*GetGuildGameListResp, error) {
	out := new(GetGuildGameListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildGameListOld, "/Guild.Guild/GetGuildGameListOld", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildPopGames(ctx context.Context, uin uint32, in *GetGuildPopGamesReq, opts ...svrkit.CallOption) (*GetGuildPopGamesResp, error) {
	out := new(GetGuildPopGamesResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildPopGames, "/Guild.Guild/GetGuildPopGames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetUserJoinGuildHistory(ctx context.Context, uin uint32, in *GetUserJoinGuildHistoryReq, opts ...svrkit.CallOption) (*GetUserJoinGuildHistoryResp, error) {
	out := new(GetUserJoinGuildHistoryResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetUserJoinGuildHistory, "/Guild.Guild/GetUserJoinGuildHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) ResetGuildName(ctx context.Context, uin uint32, in *ResetGuildNameReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildResetGuildName, "/Guild.Guild/ResetGuildName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGroupByIds(ctx context.Context, uin uint32, in *GetGroupByIdsReq, opts ...svrkit.CallOption) (*GetGroupByIdsResp, error) {
	out := new(GetGroupByIdsResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGroupByIds, "/Guild.Guild/GetGroupByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) SetGuildBlackList(ctx context.Context, uin uint32, in *SetGuildBlackListReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildSetGuildBlackList, "/Guild.Guild/SetGuildBlackList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) RemoveGuildBlackList(ctx context.Context, uin uint32, in *RemoveGuildBlackListReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGuildRemoveGuildBlackList, "/Guild.Guild/RemoveGuildBlackList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildBlackList(ctx context.Context, uin uint32, in *GetGuildBlackListReq, opts ...svrkit.CallOption) (*GetGuildBlackListResp, error) {
	out := new(GetGuildBlackListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildBlackList, "/Guild.Guild/GetGuildBlackList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) CheckUidInBlackList(ctx context.Context, uin uint32, in *CheckUidInBlackListReq, opts ...svrkit.CallOption) (*CheckUidInBlackListResp, error) {
	out := new(CheckUidInBlackListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildCheckUidInBlackList, "/Guild.Guild/CheckUidInBlackList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildStatus(ctx context.Context, uin uint32, in *GetGuildStatusReq, opts ...svrkit.CallOption) (*GetGuildStatusRsp, error) {
	out := new(GetGuildStatusRsp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildStatus, "/Guild.Guild/GetGuildStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetTGroupIdByDisplayId(ctx context.Context, uin uint32, in *GetTGroupIdByDisplayIdReq, opts ...svrkit.CallOption) (*GetTGroupIdByDisplayIdResp, error) {
	out := new(GetTGroupIdByDisplayIdResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetTGroupIdByDisplayId, "/Guild.Guild/GetTGroupIdByDisplayId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGroupIdsByType(ctx context.Context, uin uint32, in *GetGroupIdsByTypeReq, opts ...svrkit.CallOption) (*GetGroupIdsByTypeResp, error) {
	out := new(GetGroupIdsByTypeResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGroupIdsByType, "/Guild.Guild/GetGroupIdsByType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetTGroupMuteList(ctx context.Context, uin uint32, in *GetTGroupMuteListReq, opts ...svrkit.CallOption) (*GetTGroupMuteListResp, error) {
	out := new(GetTGroupMuteListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetTGroupMuteList, "/Guild.Guild/GetTGroupMuteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) DeleteGuildTrans(ctx context.Context, uin uint32, in *DeleteGuildTransReq, opts ...svrkit.CallOption) (*DeleteGuildTransResp, error) {
	out := new(DeleteGuildTransResp)
	err := c.cc.Invoke(ctx, uin, commandGuildDeleteGuildTrans, "/Guild.Guild/DeleteGuildTrans", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) BatchDeleteGuildTrans(ctx context.Context, uin uint32, in *BatchDeleteGuildTransReq, opts ...svrkit.CallOption) (*BatchDeleteGuildTransResp, error) {
	out := new(BatchDeleteGuildTransResp)
	err := c.cc.Invoke(ctx, uin, commandGuildBatchDeleteGuildTrans, "/Guild.Guild/BatchDeleteGuildTrans", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildTrans(ctx context.Context, uin uint32, in *GetGuildTransReq, opts ...svrkit.CallOption) (*GetGuildTransResp, error) {
	out := new(GetGuildTransResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildTrans, "/Guild.Guild/GetGuildTrans", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) BatchGetGuildTrans(ctx context.Context, uin uint32, in *BatchGetGuildTransReq, opts ...svrkit.CallOption) (*BatchGetGuildTransResp, error) {
	out := new(BatchGetGuildTransResp)
	err := c.cc.Invoke(ctx, uin, commandGuildBatchGetGuildTrans, "/Guild.Guild/BatchGetGuildTrans", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) BatGetGuildOfficialByUids(ctx context.Context, uin uint32, in *BatGetGuildOfficialByUidsReq, opts ...svrkit.CallOption) (*BatGetGuildOfficialByUidsResp, error) {
	out := new(BatGetGuildOfficialByUidsResp)
	err := c.cc.Invoke(ctx, uin, commandGuildBatGetGuildOfficialByUids, "/Guild.Guild/BatGetGuildOfficialByUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GuildSvrkitClient) GetGuildAllDutyList(ctx context.Context, uin uint32, in *GetGuildAllDutyListReq, opts ...svrkit.CallOption) (*GetGuildAllDutyListResp, error) {
	out := new(GetGuildAllDutyListResp)
	err := c.cc.Invoke(ctx, uin, commandGuildGetGuildAllDutyList, "/Guild.Guild/GetGuildAllDutyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
