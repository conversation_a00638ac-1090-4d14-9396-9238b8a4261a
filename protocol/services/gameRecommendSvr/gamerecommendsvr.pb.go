// Code generated by protoc-gen-gogo.
// source: services/gameRecommend/gamerecommendsvr.proto
// DO NOT EDIT!

/*
	Package gamerecommendsvr is a generated protocol buffer package.

	It is generated from these files:
		services/gameRecommend/gamerecommendsvr.proto

	It has these top-level messages:
		TagInfo
		GetAllGameTagInfoListReq
		GetAllGameTagInfoListResp
		GetGameTagsByIdListReq
		GetGameTagInfoListByNameReq
		CreateGameTagInfoReq
		CreateGameTagInfoResp
		DelGameTagInfoReq
		DelGameTagInfoResp
		GetSameTagGameListReq
		GetSameTagGameListResp
		GameActivityInfo
		ComplexGameCard
		SimpleGameCard
		FixedTopListCard
		TagTopListCard
		OperActivityCard
		RecommendIntefaceCard
		GetAllTopListCardReq
		GetAllTopListCardResp
		GetAllTopListCardByTagReq
		AddTopListCardReq
		AddTopListCardResp
		DelTopListCardReq
		DelTopListCardResp
		FullUpdateTopListCardGameListReq
		FullUpdateTopListCardGameListResp
		GetAllOperActivityCardReq
		GetAllOperActivityCardResp
		AddOperActivityCardReq
		AddOperActivityCardResp
		DelOperActivityCardReq
		DelOperActivityCardResp
		GetOperActivityCardReq
		GetOperActivityCardResp
		UpdateOperActivityCardReq
		UpdateOperActivityCardResp
		GetUserCardListReq
		GetUserCardListResp
		NotifyUserInstallGameUpdateReq
		NotifyUserInstallGameUpdateResp
		GetGameTagListReq
*/
package gamerecommendsvr

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// ****************************************************************
type TagInfo struct {
	TagId   uint32 `protobuf:"varint,1,opt,name=tag_id,json=tagId" json:"tag_id"`
	TagName string `protobuf:"bytes,2,opt,name=tag_name,json=tagName" json:"tag_name"`
	TagType uint32 `protobuf:"varint,3,opt,name=tag_type,json=tagType" json:"tag_type"`
}

func (m *TagInfo) Reset()                    { *m = TagInfo{} }
func (m *TagInfo) String() string            { return proto.CompactTextString(m) }
func (*TagInfo) ProtoMessage()               {}
func (*TagInfo) Descriptor() ([]byte, []int) { return fileDescriptorGamerecommendsvr, []int{0} }

func (m *TagInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TagInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *TagInfo) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

// 获取游戏标签列表
type GetAllGameTagInfoListReq struct {
}

func (m *GetAllGameTagInfoListReq) Reset()         { *m = GetAllGameTagInfoListReq{} }
func (m *GetAllGameTagInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllGameTagInfoListReq) ProtoMessage()    {}
func (*GetAllGameTagInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{1}
}

type GetAllGameTagInfoListResp struct {
	TagList []*TagInfo `protobuf:"bytes,1,rep,name=tag_list,json=tagList" json:"tag_list,omitempty"`
}

func (m *GetAllGameTagInfoListResp) Reset()         { *m = GetAllGameTagInfoListResp{} }
func (m *GetAllGameTagInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllGameTagInfoListResp) ProtoMessage()    {}
func (*GetAllGameTagInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{2}
}

func (m *GetAllGameTagInfoListResp) GetTagList() []*TagInfo {
	if m != nil {
		return m.TagList
	}
	return nil
}

type GetGameTagsByIdListReq struct {
	TagIdList []uint32 `protobuf:"varint,1,rep,name=tag_id_list,json=tagIdList" json:"tag_id_list,omitempty"`
}

func (m *GetGameTagsByIdListReq) Reset()         { *m = GetGameTagsByIdListReq{} }
func (m *GetGameTagsByIdListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameTagsByIdListReq) ProtoMessage()    {}
func (*GetGameTagsByIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{3}
}

func (m *GetGameTagsByIdListReq) GetTagIdList() []uint32 {
	if m != nil {
		return m.TagIdList
	}
	return nil
}

// 根据标签名称查询标签
type GetGameTagInfoListByNameReq struct {
	TagName string `protobuf:"bytes,1,req,name=tag_name,json=tagName" json:"tag_name"`
}

func (m *GetGameTagInfoListByNameReq) Reset()         { *m = GetGameTagInfoListByNameReq{} }
func (m *GetGameTagInfoListByNameReq) String() string { return proto.CompactTextString(m) }
func (*GetGameTagInfoListByNameReq) ProtoMessage()    {}
func (*GetGameTagInfoListByNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{4}
}

func (m *GetGameTagInfoListByNameReq) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

// 添加游戏标签
type CreateGameTagInfoReq struct {
	TagName string `protobuf:"bytes,1,req,name=tag_name,json=tagName" json:"tag_name"`
	TagType uint32 `protobuf:"varint,2,opt,name=tag_type,json=tagType" json:"tag_type"`
}

func (m *CreateGameTagInfoReq) Reset()         { *m = CreateGameTagInfoReq{} }
func (m *CreateGameTagInfoReq) String() string { return proto.CompactTextString(m) }
func (*CreateGameTagInfoReq) ProtoMessage()    {}
func (*CreateGameTagInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{5}
}

func (m *CreateGameTagInfoReq) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *CreateGameTagInfoReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

type CreateGameTagInfoResp struct {
	TagInfo *TagInfo `protobuf:"bytes,1,req,name=tag_info,json=tagInfo" json:"tag_info,omitempty"`
}

func (m *CreateGameTagInfoResp) Reset()         { *m = CreateGameTagInfoResp{} }
func (m *CreateGameTagInfoResp) String() string { return proto.CompactTextString(m) }
func (*CreateGameTagInfoResp) ProtoMessage()    {}
func (*CreateGameTagInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{6}
}

func (m *CreateGameTagInfoResp) GetTagInfo() *TagInfo {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

// 删除游戏标签
type DelGameTagInfoReq struct {
	TagId uint32 `protobuf:"varint,1,req,name=tag_id,json=tagId" json:"tag_id"`
}

func (m *DelGameTagInfoReq) Reset()         { *m = DelGameTagInfoReq{} }
func (m *DelGameTagInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelGameTagInfoReq) ProtoMessage()    {}
func (*DelGameTagInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{7}
}

func (m *DelGameTagInfoReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type DelGameTagInfoResp struct {
}

func (m *DelGameTagInfoResp) Reset()         { *m = DelGameTagInfoResp{} }
func (m *DelGameTagInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelGameTagInfoResp) ProtoMessage()    {}
func (*DelGameTagInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{8}
}

// 根据游戏ID获取与该游戏具有相同标签的同类游戏
type GetSameTagGameListReq struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	Count  uint32 `protobuf:"varint,2,req,name=count" json:"count"`
}

func (m *GetSameTagGameListReq) Reset()         { *m = GetSameTagGameListReq{} }
func (m *GetSameTagGameListReq) String() string { return proto.CompactTextString(m) }
func (*GetSameTagGameListReq) ProtoMessage()    {}
func (*GetSameTagGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{9}
}

func (m *GetSameTagGameListReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetSameTagGameListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetSameTagGameListResp struct {
	GameIdList []uint32 `protobuf:"varint,1,rep,name=game_id_list,json=gameIdList" json:"game_id_list,omitempty"`
	TagInfo    *TagInfo `protobuf:"bytes,2,opt,name=tag_info,json=tagInfo" json:"tag_info,omitempty"`
}

func (m *GetSameTagGameListResp) Reset()         { *m = GetSameTagGameListResp{} }
func (m *GetSameTagGameListResp) String() string { return proto.CompactTextString(m) }
func (*GetSameTagGameListResp) ProtoMessage()    {}
func (*GetSameTagGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{10}
}

func (m *GetSameTagGameListResp) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

func (m *GetSameTagGameListResp) GetTagInfo() *TagInfo {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

//
type GameActivityInfo struct {
	Title        string `protobuf:"bytes,1,req,name=title" json:"title"`
	Url          string `protobuf:"bytes,2,req,name=url" json:"url"`
	ImgUrl       string `protobuf:"bytes,3,req,name=img_url,json=imgUrl" json:"img_url"`
	SubTitle     string `protobuf:"bytes,4,req,name=sub_title,json=subTitle" json:"sub_title"`
	LastUpdateTs uint32 `protobuf:"varint,5,req,name=last_update_ts,json=lastUpdateTs" json:"last_update_ts"`
	BeginTime    uint32 `protobuf:"varint,6,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime      uint32 `protobuf:"varint,7,req,name=end_time,json=endTime" json:"end_time"`
}

func (m *GameActivityInfo) Reset()         { *m = GameActivityInfo{} }
func (m *GameActivityInfo) String() string { return proto.CompactTextString(m) }
func (*GameActivityInfo) ProtoMessage()    {}
func (*GameActivityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{11}
}

func (m *GameActivityInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GameActivityInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GameActivityInfo) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *GameActivityInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GameActivityInfo) GetLastUpdateTs() uint32 {
	if m != nil {
		return m.LastUpdateTs
	}
	return 0
}

func (m *GameActivityInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GameActivityInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 复合游戏卡(2.9.2版本之后不显示这种卡片)
type ComplexGameCard struct {
	CardId  uint64            `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
	GameId  uint32            `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	TagList []*TagInfo        `protobuf:"bytes,3,rep,name=tag_list,json=tagList" json:"tag_list,omitempty"`
	ImgUrl  string            `protobuf:"bytes,4,opt,name=img_url,json=imgUrl" json:"img_url"`
	ActInfo *GameActivityInfo `protobuf:"bytes,5,opt,name=act_info,json=actInfo" json:"act_info,omitempty"`
}

func (m *ComplexGameCard) Reset()                    { *m = ComplexGameCard{} }
func (m *ComplexGameCard) String() string            { return proto.CompactTextString(m) }
func (*ComplexGameCard) ProtoMessage()               {}
func (*ComplexGameCard) Descriptor() ([]byte, []int) { return fileDescriptorGamerecommendsvr, []int{12} }

func (m *ComplexGameCard) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *ComplexGameCard) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ComplexGameCard) GetTagList() []*TagInfo {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *ComplexGameCard) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *ComplexGameCard) GetActInfo() *GameActivityInfo {
	if m != nil {
		return m.ActInfo
	}
	return nil
}

// 游戏下载卡(2.9.2版本之后不显示这种卡片)
type SimpleGameCard struct {
	CardId  uint64     `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
	GameId  uint32     `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	ImgUrl  string     `protobuf:"bytes,3,opt,name=img_url,json=imgUrl" json:"img_url"`
	TagList []*TagInfo `protobuf:"bytes,4,rep,name=tag_list,json=tagList" json:"tag_list,omitempty"`
}

func (m *SimpleGameCard) Reset()                    { *m = SimpleGameCard{} }
func (m *SimpleGameCard) String() string            { return proto.CompactTextString(m) }
func (*SimpleGameCard) ProtoMessage()               {}
func (*SimpleGameCard) Descriptor() ([]byte, []int) { return fileDescriptorGamerecommendsvr, []int{13} }

func (m *SimpleGameCard) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *SimpleGameCard) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SimpleGameCard) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *SimpleGameCard) GetTagList() []*TagInfo {
	if m != nil {
		return m.TagList
	}
	return nil
}

// 固定榜单卡
type FixedTopListCard struct {
	CardId          uint64   `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
	Title           string   `protobuf:"bytes,2,req,name=title" json:"title"`
	GameidList      []uint32 `protobuf:"varint,3,rep,name=gameid_list,json=gameidList" json:"gameid_list,omitempty"`
	LastUpdateTs    uint32   `protobuf:"varint,4,opt,name=last_update_ts,json=lastUpdateTs" json:"last_update_ts"`
	SpecToplistType uint32   `protobuf:"varint,5,opt,name=spec_toplist_type,json=specToplistType" json:"spec_toplist_type"`
}

func (m *FixedTopListCard) Reset()         { *m = FixedTopListCard{} }
func (m *FixedTopListCard) String() string { return proto.CompactTextString(m) }
func (*FixedTopListCard) ProtoMessage()    {}
func (*FixedTopListCard) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{14}
}

func (m *FixedTopListCard) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *FixedTopListCard) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *FixedTopListCard) GetGameidList() []uint32 {
	if m != nil {
		return m.GameidList
	}
	return nil
}

func (m *FixedTopListCard) GetLastUpdateTs() uint32 {
	if m != nil {
		return m.LastUpdateTs
	}
	return 0
}

func (m *FixedTopListCard) GetSpecToplistType() uint32 {
	if m != nil {
		return m.SpecToplistType
	}
	return 0
}

// 标签榜单卡
type TagTopListCard struct {
	CardId          uint64   `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
	Title           string   `protobuf:"bytes,2,req,name=title" json:"title"`
	Tag             *TagInfo `protobuf:"bytes,3,req,name=tag" json:"tag,omitempty"`
	GameidList      []uint32 `protobuf:"varint,4,rep,name=gameid_list,json=gameidList" json:"gameid_list,omitempty"`
	LastUpdateTs    uint32   `protobuf:"varint,5,opt,name=last_update_ts,json=lastUpdateTs" json:"last_update_ts"`
	SpecToplistType uint32   `protobuf:"varint,6,opt,name=spec_toplist_type,json=specToplistType" json:"spec_toplist_type"`
}

func (m *TagTopListCard) Reset()                    { *m = TagTopListCard{} }
func (m *TagTopListCard) String() string            { return proto.CompactTextString(m) }
func (*TagTopListCard) ProtoMessage()               {}
func (*TagTopListCard) Descriptor() ([]byte, []int) { return fileDescriptorGamerecommendsvr, []int{15} }

func (m *TagTopListCard) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *TagTopListCard) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *TagTopListCard) GetTag() *TagInfo {
	if m != nil {
		return m.Tag
	}
	return nil
}

func (m *TagTopListCard) GetGameidList() []uint32 {
	if m != nil {
		return m.GameidList
	}
	return nil
}

func (m *TagTopListCard) GetLastUpdateTs() uint32 {
	if m != nil {
		return m.LastUpdateTs
	}
	return 0
}

func (m *TagTopListCard) GetSpecToplistType() uint32 {
	if m != nil {
		return m.SpecToplistType
	}
	return 0
}

// 活动卡
type OperActivityCard struct {
	CardId        uint64            `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
	ActInfo       *GameActivityInfo `protobuf:"bytes,2,req,name=act_info,json=actInfo" json:"act_info,omitempty"`
	GameId        uint32            `protobuf:"varint,3,opt,name=game_id,json=gameId" json:"game_id"`
	NarrowImgUrl  string            `protobuf:"bytes,4,opt,name=narrow_img_url,json=narrowImgUrl" json:"narrow_img_url"`
	NarrowJumpUrl string            `protobuf:"bytes,5,opt,name=narrow_jump_url,json=narrowJumpUrl" json:"narrow_jump_url"`
}

func (m *OperActivityCard) Reset()         { *m = OperActivityCard{} }
func (m *OperActivityCard) String() string { return proto.CompactTextString(m) }
func (*OperActivityCard) ProtoMessage()    {}
func (*OperActivityCard) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{16}
}

func (m *OperActivityCard) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *OperActivityCard) GetActInfo() *GameActivityInfo {
	if m != nil {
		return m.ActInfo
	}
	return nil
}

func (m *OperActivityCard) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *OperActivityCard) GetNarrowImgUrl() string {
	if m != nil {
		return m.NarrowImgUrl
	}
	return ""
}

func (m *OperActivityCard) GetNarrowJumpUrl() string {
	if m != nil {
		return m.NarrowJumpUrl
	}
	return ""
}

type RecommendIntefaceCard struct {
	CardType   uint32 `protobuf:"varint,1,req,name=card_type,json=cardType" json:"card_type"`
	CardDetail []byte `protobuf:"bytes,2,opt,name=card_detail,json=cardDetail" json:"card_detail"`
}

func (m *RecommendIntefaceCard) Reset()         { *m = RecommendIntefaceCard{} }
func (m *RecommendIntefaceCard) String() string { return proto.CompactTextString(m) }
func (*RecommendIntefaceCard) ProtoMessage()    {}
func (*RecommendIntefaceCard) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{17}
}

func (m *RecommendIntefaceCard) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *RecommendIntefaceCard) GetCardDetail() []byte {
	if m != nil {
		return m.CardDetail
	}
	return nil
}

// 获取榜单类型卡列表
type GetAllTopListCardReq struct {
	CardType uint32 `protobuf:"varint,1,req,name=card_type,json=cardType" json:"card_type"`
}

func (m *GetAllTopListCardReq) Reset()         { *m = GetAllTopListCardReq{} }
func (m *GetAllTopListCardReq) String() string { return proto.CompactTextString(m) }
func (*GetAllTopListCardReq) ProtoMessage()    {}
func (*GetAllTopListCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{18}
}

func (m *GetAllTopListCardReq) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

type GetAllTopListCardResp struct {
	CardList []*RecommendIntefaceCard `protobuf:"bytes,1,rep,name=card_list,json=cardList" json:"card_list,omitempty"`
}

func (m *GetAllTopListCardResp) Reset()         { *m = GetAllTopListCardResp{} }
func (m *GetAllTopListCardResp) String() string { return proto.CompactTextString(m) }
func (*GetAllTopListCardResp) ProtoMessage()    {}
func (*GetAllTopListCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{19}
}

func (m *GetAllTopListCardResp) GetCardList() []*RecommendIntefaceCard {
	if m != nil {
		return m.CardList
	}
	return nil
}

// 获取榜单类型卡列表
type GetAllTopListCardByTagReq struct {
	TagId uint32 `protobuf:"varint,1,req,name=tag_id,json=tagId" json:"tag_id"`
}

func (m *GetAllTopListCardByTagReq) Reset()         { *m = GetAllTopListCardByTagReq{} }
func (m *GetAllTopListCardByTagReq) String() string { return proto.CompactTextString(m) }
func (*GetAllTopListCardByTagReq) ProtoMessage()    {}
func (*GetAllTopListCardByTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{20}
}

func (m *GetAllTopListCardByTagReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

// 添加榜单类型卡
type AddTopListCardReq struct {
	CardType        uint32   `protobuf:"varint,1,req,name=card_type,json=cardType" json:"card_type"`
	Title           string   `protobuf:"bytes,2,req,name=title" json:"title"`
	GameidList      []uint32 `protobuf:"varint,3,rep,name=gameid_list,json=gameidList" json:"gameid_list,omitempty"`
	Tag             *TagInfo `protobuf:"bytes,4,opt,name=tag" json:"tag,omitempty"`
	SpecToplistType uint32   `protobuf:"varint,5,opt,name=spec_toplist_type,json=specToplistType" json:"spec_toplist_type"`
}

func (m *AddTopListCardReq) Reset()         { *m = AddTopListCardReq{} }
func (m *AddTopListCardReq) String() string { return proto.CompactTextString(m) }
func (*AddTopListCardReq) ProtoMessage()    {}
func (*AddTopListCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{21}
}

func (m *AddTopListCardReq) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *AddTopListCardReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AddTopListCardReq) GetGameidList() []uint32 {
	if m != nil {
		return m.GameidList
	}
	return nil
}

func (m *AddTopListCardReq) GetTag() *TagInfo {
	if m != nil {
		return m.Tag
	}
	return nil
}

func (m *AddTopListCardReq) GetSpecToplistType() uint32 {
	if m != nil {
		return m.SpecToplistType
	}
	return 0
}

type AddTopListCardResp struct {
	CardId uint64 `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
}

func (m *AddTopListCardResp) Reset()         { *m = AddTopListCardResp{} }
func (m *AddTopListCardResp) String() string { return proto.CompactTextString(m) }
func (*AddTopListCardResp) ProtoMessage()    {}
func (*AddTopListCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{22}
}

func (m *AddTopListCardResp) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

// 删除榜单类型卡
type DelTopListCardReq struct {
	CardId uint64 `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
}

func (m *DelTopListCardReq) Reset()         { *m = DelTopListCardReq{} }
func (m *DelTopListCardReq) String() string { return proto.CompactTextString(m) }
func (*DelTopListCardReq) ProtoMessage()    {}
func (*DelTopListCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{23}
}

func (m *DelTopListCardReq) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type DelTopListCardResp struct {
}

func (m *DelTopListCardResp) Reset()         { *m = DelTopListCardResp{} }
func (m *DelTopListCardResp) String() string { return proto.CompactTextString(m) }
func (*DelTopListCardResp) ProtoMessage()    {}
func (*DelTopListCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{24}
}

// 全量新榜单类型卡中的游戏列表
type FullUpdateTopListCardGameListReq struct {
	CardId     uint64   `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
	GameidList []uint32 `protobuf:"varint,2,rep,name=gameid_list,json=gameidList" json:"gameid_list,omitempty"`
}

func (m *FullUpdateTopListCardGameListReq) Reset()         { *m = FullUpdateTopListCardGameListReq{} }
func (m *FullUpdateTopListCardGameListReq) String() string { return proto.CompactTextString(m) }
func (*FullUpdateTopListCardGameListReq) ProtoMessage()    {}
func (*FullUpdateTopListCardGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{25}
}

func (m *FullUpdateTopListCardGameListReq) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *FullUpdateTopListCardGameListReq) GetGameidList() []uint32 {
	if m != nil {
		return m.GameidList
	}
	return nil
}

type FullUpdateTopListCardGameListResp struct {
}

func (m *FullUpdateTopListCardGameListResp) Reset()         { *m = FullUpdateTopListCardGameListResp{} }
func (m *FullUpdateTopListCardGameListResp) String() string { return proto.CompactTextString(m) }
func (*FullUpdateTopListCardGameListResp) ProtoMessage()    {}
func (*FullUpdateTopListCardGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{26}
}

// 获取全量的活动卡
type GetAllOperActivityCardReq struct {
}

func (m *GetAllOperActivityCardReq) Reset()         { *m = GetAllOperActivityCardReq{} }
func (m *GetAllOperActivityCardReq) String() string { return proto.CompactTextString(m) }
func (*GetAllOperActivityCardReq) ProtoMessage()    {}
func (*GetAllOperActivityCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{27}
}

type GetAllOperActivityCardResp struct {
	CardList []*OperActivityCard `protobuf:"bytes,1,rep,name=card_list,json=cardList" json:"card_list,omitempty"`
}

func (m *GetAllOperActivityCardResp) Reset()         { *m = GetAllOperActivityCardResp{} }
func (m *GetAllOperActivityCardResp) String() string { return proto.CompactTextString(m) }
func (*GetAllOperActivityCardResp) ProtoMessage()    {}
func (*GetAllOperActivityCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{28}
}

func (m *GetAllOperActivityCardResp) GetCardList() []*OperActivityCard {
	if m != nil {
		return m.CardList
	}
	return nil
}

// 添加活动类型卡
type AddOperActivityCardReq struct {
	ActInfo       *GameActivityInfo `protobuf:"bytes,1,req,name=act_info,json=actInfo" json:"act_info,omitempty"`
	GameId        uint32            `protobuf:"varint,2,opt,name=game_id,json=gameId" json:"game_id"`
	NarrowImgUrl  string            `protobuf:"bytes,3,opt,name=narrow_img_url,json=narrowImgUrl" json:"narrow_img_url"`
	NarrowJumpUrl string            `protobuf:"bytes,4,opt,name=narrow_jump_url,json=narrowJumpUrl" json:"narrow_jump_url"`
}

func (m *AddOperActivityCardReq) Reset()         { *m = AddOperActivityCardReq{} }
func (m *AddOperActivityCardReq) String() string { return proto.CompactTextString(m) }
func (*AddOperActivityCardReq) ProtoMessage()    {}
func (*AddOperActivityCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{29}
}

func (m *AddOperActivityCardReq) GetActInfo() *GameActivityInfo {
	if m != nil {
		return m.ActInfo
	}
	return nil
}

func (m *AddOperActivityCardReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *AddOperActivityCardReq) GetNarrowImgUrl() string {
	if m != nil {
		return m.NarrowImgUrl
	}
	return ""
}

func (m *AddOperActivityCardReq) GetNarrowJumpUrl() string {
	if m != nil {
		return m.NarrowJumpUrl
	}
	return ""
}

type AddOperActivityCardResp struct {
	CardId uint64 `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
}

func (m *AddOperActivityCardResp) Reset()         { *m = AddOperActivityCardResp{} }
func (m *AddOperActivityCardResp) String() string { return proto.CompactTextString(m) }
func (*AddOperActivityCardResp) ProtoMessage()    {}
func (*AddOperActivityCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{30}
}

func (m *AddOperActivityCardResp) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

// 删除活动类型卡
type DelOperActivityCardReq struct {
	CardId uint64 `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
}

func (m *DelOperActivityCardReq) Reset()         { *m = DelOperActivityCardReq{} }
func (m *DelOperActivityCardReq) String() string { return proto.CompactTextString(m) }
func (*DelOperActivityCardReq) ProtoMessage()    {}
func (*DelOperActivityCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{31}
}

func (m *DelOperActivityCardReq) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type DelOperActivityCardResp struct {
}

func (m *DelOperActivityCardResp) Reset()         { *m = DelOperActivityCardResp{} }
func (m *DelOperActivityCardResp) String() string { return proto.CompactTextString(m) }
func (*DelOperActivityCardResp) ProtoMessage()    {}
func (*DelOperActivityCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{32}
}

// 取一个活动卡
type GetOperActivityCardReq struct {
	CardId uint64 `protobuf:"varint,1,req,name=card_id,json=cardId" json:"card_id"`
}

func (m *GetOperActivityCardReq) Reset()         { *m = GetOperActivityCardReq{} }
func (m *GetOperActivityCardReq) String() string { return proto.CompactTextString(m) }
func (*GetOperActivityCardReq) ProtoMessage()    {}
func (*GetOperActivityCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{33}
}

func (m *GetOperActivityCardReq) GetCardId() uint64 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type GetOperActivityCardResp struct {
	CardInfo *OperActivityCard `protobuf:"bytes,1,req,name=card_info,json=cardInfo" json:"card_info,omitempty"`
}

func (m *GetOperActivityCardResp) Reset()         { *m = GetOperActivityCardResp{} }
func (m *GetOperActivityCardResp) String() string { return proto.CompactTextString(m) }
func (*GetOperActivityCardResp) ProtoMessage()    {}
func (*GetOperActivityCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{34}
}

func (m *GetOperActivityCardResp) GetCardInfo() *OperActivityCard {
	if m != nil {
		return m.CardInfo
	}
	return nil
}

// 更新一个活动卡
type UpdateOperActivityCardReq struct {
	CardInfo *OperActivityCard `protobuf:"bytes,1,req,name=card_info,json=cardInfo" json:"card_info,omitempty"`
}

func (m *UpdateOperActivityCardReq) Reset()         { *m = UpdateOperActivityCardReq{} }
func (m *UpdateOperActivityCardReq) String() string { return proto.CompactTextString(m) }
func (*UpdateOperActivityCardReq) ProtoMessage()    {}
func (*UpdateOperActivityCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{35}
}

func (m *UpdateOperActivityCardReq) GetCardInfo() *OperActivityCard {
	if m != nil {
		return m.CardInfo
	}
	return nil
}

type UpdateOperActivityCardResp struct {
}

func (m *UpdateOperActivityCardResp) Reset()         { *m = UpdateOperActivityCardResp{} }
func (m *UpdateOperActivityCardResp) String() string { return proto.CompactTextString(m) }
func (*UpdateOperActivityCardResp) ProtoMessage()    {}
func (*UpdateOperActivityCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{36}
}

// 获取用户的卡片列表
type GetUserCardListReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	StartIdx   uint32 `protobuf:"varint,2,req,name=start_idx,json=startIdx" json:"start_idx"`
	CountLimit uint32 `protobuf:"varint,3,req,name=count_limit,json=countLimit" json:"count_limit"`
	ExGameid   uint32 `protobuf:"varint,4,opt,name=ex_gameid,json=exGameid" json:"ex_gameid"`
	VersionId  uint32 `protobuf:"varint,5,opt,name=version_id,json=versionId" json:"version_id"`
}

func (m *GetUserCardListReq) Reset()         { *m = GetUserCardListReq{} }
func (m *GetUserCardListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCardListReq) ProtoMessage()    {}
func (*GetUserCardListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{37}
}

func (m *GetUserCardListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCardListReq) GetStartIdx() uint32 {
	if m != nil {
		return m.StartIdx
	}
	return 0
}

func (m *GetUserCardListReq) GetCountLimit() uint32 {
	if m != nil {
		return m.CountLimit
	}
	return 0
}

func (m *GetUserCardListReq) GetExGameid() uint32 {
	if m != nil {
		return m.ExGameid
	}
	return 0
}

func (m *GetUserCardListReq) GetVersionId() uint32 {
	if m != nil {
		return m.VersionId
	}
	return 0
}

type GetUserCardListResp struct {
	CardList []*RecommendIntefaceCard `protobuf:"bytes,1,rep,name=card_list,json=cardList" json:"card_list,omitempty"`
}

func (m *GetUserCardListResp) Reset()         { *m = GetUserCardListResp{} }
func (m *GetUserCardListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCardListResp) ProtoMessage()    {}
func (*GetUserCardListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{38}
}

func (m *GetUserCardListResp) GetCardList() []*RecommendIntefaceCard {
	if m != nil {
		return m.CardList
	}
	return nil
}

// account 服务触发 用户安装的游戏更新
type NotifyUserInstallGameUpdateReq struct {
	Uid                 uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	InstallGameidList   []uint32 `protobuf:"varint,2,rep,name=install_gameid_list,json=installGameidList" json:"install_gameid_list,omitempty"`
	UninstallGameidList []uint32 `protobuf:"varint,3,rep,name=uninstall_gameid_list,json=uninstallGameidList" json:"uninstall_gameid_list,omitempty"`
}

func (m *NotifyUserInstallGameUpdateReq) Reset()         { *m = NotifyUserInstallGameUpdateReq{} }
func (m *NotifyUserInstallGameUpdateReq) String() string { return proto.CompactTextString(m) }
func (*NotifyUserInstallGameUpdateReq) ProtoMessage()    {}
func (*NotifyUserInstallGameUpdateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{39}
}

func (m *NotifyUserInstallGameUpdateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NotifyUserInstallGameUpdateReq) GetInstallGameidList() []uint32 {
	if m != nil {
		return m.InstallGameidList
	}
	return nil
}

func (m *NotifyUserInstallGameUpdateReq) GetUninstallGameidList() []uint32 {
	if m != nil {
		return m.UninstallGameidList
	}
	return nil
}

type NotifyUserInstallGameUpdateResp struct {
}

func (m *NotifyUserInstallGameUpdateResp) Reset()         { *m = NotifyUserInstallGameUpdateResp{} }
func (m *NotifyUserInstallGameUpdateResp) String() string { return proto.CompactTextString(m) }
func (*NotifyUserInstallGameUpdateResp) ProtoMessage()    {}
func (*NotifyUserInstallGameUpdateResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{40}
}

// 根据游戏ID获取标签信息
type GetGameTagListReq struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *GetGameTagListReq) Reset()         { *m = GetGameTagListReq{} }
func (m *GetGameTagListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameTagListReq) ProtoMessage()    {}
func (*GetGameTagListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamerecommendsvr, []int{41}
}

func (m *GetGameTagListReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func init() {
	proto.RegisterType((*TagInfo)(nil), "gamerecommendsvr.TagInfo")
	proto.RegisterType((*GetAllGameTagInfoListReq)(nil), "gamerecommendsvr.GetAllGameTagInfoListReq")
	proto.RegisterType((*GetAllGameTagInfoListResp)(nil), "gamerecommendsvr.GetAllGameTagInfoListResp")
	proto.RegisterType((*GetGameTagsByIdListReq)(nil), "gamerecommendsvr.GetGameTagsByIdListReq")
	proto.RegisterType((*GetGameTagInfoListByNameReq)(nil), "gamerecommendsvr.GetGameTagInfoListByNameReq")
	proto.RegisterType((*CreateGameTagInfoReq)(nil), "gamerecommendsvr.CreateGameTagInfoReq")
	proto.RegisterType((*CreateGameTagInfoResp)(nil), "gamerecommendsvr.CreateGameTagInfoResp")
	proto.RegisterType((*DelGameTagInfoReq)(nil), "gamerecommendsvr.DelGameTagInfoReq")
	proto.RegisterType((*DelGameTagInfoResp)(nil), "gamerecommendsvr.DelGameTagInfoResp")
	proto.RegisterType((*GetSameTagGameListReq)(nil), "gamerecommendsvr.GetSameTagGameListReq")
	proto.RegisterType((*GetSameTagGameListResp)(nil), "gamerecommendsvr.GetSameTagGameListResp")
	proto.RegisterType((*GameActivityInfo)(nil), "gamerecommendsvr.GameActivityInfo")
	proto.RegisterType((*ComplexGameCard)(nil), "gamerecommendsvr.ComplexGameCard")
	proto.RegisterType((*SimpleGameCard)(nil), "gamerecommendsvr.SimpleGameCard")
	proto.RegisterType((*FixedTopListCard)(nil), "gamerecommendsvr.FixedTopListCard")
	proto.RegisterType((*TagTopListCard)(nil), "gamerecommendsvr.TagTopListCard")
	proto.RegisterType((*OperActivityCard)(nil), "gamerecommendsvr.OperActivityCard")
	proto.RegisterType((*RecommendIntefaceCard)(nil), "gamerecommendsvr.RecommendIntefaceCard")
	proto.RegisterType((*GetAllTopListCardReq)(nil), "gamerecommendsvr.GetAllTopListCardReq")
	proto.RegisterType((*GetAllTopListCardResp)(nil), "gamerecommendsvr.GetAllTopListCardResp")
	proto.RegisterType((*GetAllTopListCardByTagReq)(nil), "gamerecommendsvr.GetAllTopListCardByTagReq")
	proto.RegisterType((*AddTopListCardReq)(nil), "gamerecommendsvr.AddTopListCardReq")
	proto.RegisterType((*AddTopListCardResp)(nil), "gamerecommendsvr.AddTopListCardResp")
	proto.RegisterType((*DelTopListCardReq)(nil), "gamerecommendsvr.DelTopListCardReq")
	proto.RegisterType((*DelTopListCardResp)(nil), "gamerecommendsvr.DelTopListCardResp")
	proto.RegisterType((*FullUpdateTopListCardGameListReq)(nil), "gamerecommendsvr.FullUpdateTopListCardGameListReq")
	proto.RegisterType((*FullUpdateTopListCardGameListResp)(nil), "gamerecommendsvr.FullUpdateTopListCardGameListResp")
	proto.RegisterType((*GetAllOperActivityCardReq)(nil), "gamerecommendsvr.GetAllOperActivityCardReq")
	proto.RegisterType((*GetAllOperActivityCardResp)(nil), "gamerecommendsvr.GetAllOperActivityCardResp")
	proto.RegisterType((*AddOperActivityCardReq)(nil), "gamerecommendsvr.AddOperActivityCardReq")
	proto.RegisterType((*AddOperActivityCardResp)(nil), "gamerecommendsvr.AddOperActivityCardResp")
	proto.RegisterType((*DelOperActivityCardReq)(nil), "gamerecommendsvr.DelOperActivityCardReq")
	proto.RegisterType((*DelOperActivityCardResp)(nil), "gamerecommendsvr.DelOperActivityCardResp")
	proto.RegisterType((*GetOperActivityCardReq)(nil), "gamerecommendsvr.GetOperActivityCardReq")
	proto.RegisterType((*GetOperActivityCardResp)(nil), "gamerecommendsvr.GetOperActivityCardResp")
	proto.RegisterType((*UpdateOperActivityCardReq)(nil), "gamerecommendsvr.UpdateOperActivityCardReq")
	proto.RegisterType((*UpdateOperActivityCardResp)(nil), "gamerecommendsvr.UpdateOperActivityCardResp")
	proto.RegisterType((*GetUserCardListReq)(nil), "gamerecommendsvr.GetUserCardListReq")
	proto.RegisterType((*GetUserCardListResp)(nil), "gamerecommendsvr.GetUserCardListResp")
	proto.RegisterType((*NotifyUserInstallGameUpdateReq)(nil), "gamerecommendsvr.NotifyUserInstallGameUpdateReq")
	proto.RegisterType((*NotifyUserInstallGameUpdateResp)(nil), "gamerecommendsvr.NotifyUserInstallGameUpdateResp")
	proto.RegisterType((*GetGameTagListReq)(nil), "gamerecommendsvr.GetGameTagListReq")
}
func (m *TagInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TagInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.TagId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.TagName)))
	i += copy(dAtA[i:], m.TagName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.TagType))
	return i, nil
}

func (m *GetAllGameTagInfoListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllGameTagInfoListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllGameTagInfoListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllGameTagInfoListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TagList) > 0 {
		for _, msg := range m.TagList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGameTagsByIdListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameTagsByIdListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TagIdList) > 0 {
		for _, num := range m.TagIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetGameTagInfoListByNameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameTagInfoListByNameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.TagName)))
	i += copy(dAtA[i:], m.TagName)
	return i, nil
}

func (m *CreateGameTagInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateGameTagInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.TagName)))
	i += copy(dAtA[i:], m.TagName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.TagType))
	return i, nil
}

func (m *CreateGameTagInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateGameTagInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TagInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tag_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.TagInfo.Size()))
		n1, err := m.TagInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *DelGameTagInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelGameTagInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.TagId))
	return i, nil
}

func (m *DelGameTagInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelGameTagInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetSameTagGameListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSameTagGameListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetSameTagGameListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSameTagGameListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameIdList) > 0 {
		for _, num := range m.GameIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(num))
		}
	}
	if m.TagInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.TagInfo.Size()))
		n2, err := m.TagInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *GameActivityInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameActivityInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.ImgUrl)))
	i += copy(dAtA[i:], m.ImgUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.SubTitle)))
	i += copy(dAtA[i:], m.SubTitle)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.LastUpdateTs))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.EndTime))
	return i, nil
}

func (m *ComplexGameCard) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ComplexGameCard) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.GameId))
	if len(m.TagList) > 0 {
		for _, msg := range m.TagList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.ImgUrl)))
	i += copy(dAtA[i:], m.ImgUrl)
	if m.ActInfo != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.ActInfo.Size()))
		n3, err := m.ActInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *SimpleGameCard) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleGameCard) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.ImgUrl)))
	i += copy(dAtA[i:], m.ImgUrl)
	if len(m.TagList) > 0 {
		for _, msg := range m.TagList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *FixedTopListCard) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FixedTopListCard) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	if len(m.GameidList) > 0 {
		for _, num := range m.GameidList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.LastUpdateTs))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.SpecToplistType))
	return i, nil
}

func (m *TagTopListCard) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TagTopListCard) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	if m.Tag == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tag")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.Tag.Size()))
		n4, err := m.Tag.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if len(m.GameidList) > 0 {
		for _, num := range m.GameidList {
			dAtA[i] = 0x20
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.LastUpdateTs))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.SpecToplistType))
	return i, nil
}

func (m *OperActivityCard) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OperActivityCard) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	if m.ActInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("act_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.ActInfo.Size()))
		n5, err := m.ActInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.NarrowImgUrl)))
	i += copy(dAtA[i:], m.NarrowImgUrl)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.NarrowJumpUrl)))
	i += copy(dAtA[i:], m.NarrowJumpUrl)
	return i, nil
}

func (m *RecommendIntefaceCard) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecommendIntefaceCard) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardType))
	if m.CardDetail != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.CardDetail)))
		i += copy(dAtA[i:], m.CardDetail)
	}
	return i, nil
}

func (m *GetAllTopListCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllTopListCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardType))
	return i, nil
}

func (m *GetAllTopListCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllTopListCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CardList) > 0 {
		for _, msg := range m.CardList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetAllTopListCardByTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllTopListCardByTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.TagId))
	return i, nil
}

func (m *AddTopListCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddTopListCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	if len(m.GameidList) > 0 {
		for _, num := range m.GameidList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(num))
		}
	}
	if m.Tag != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.Tag.Size()))
		n6, err := m.Tag.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.SpecToplistType))
	return i, nil
}

func (m *AddTopListCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddTopListCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	return i, nil
}

func (m *DelTopListCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelTopListCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	return i, nil
}

func (m *DelTopListCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelTopListCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *FullUpdateTopListCardGameListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FullUpdateTopListCardGameListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	if len(m.GameidList) > 0 {
		for _, num := range m.GameidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *FullUpdateTopListCardGameListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FullUpdateTopListCardGameListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllOperActivityCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllOperActivityCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllOperActivityCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllOperActivityCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CardList) > 0 {
		for _, msg := range m.CardList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddOperActivityCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddOperActivityCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ActInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("act_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.ActInfo.Size()))
		n7, err := m.ActInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.NarrowImgUrl)))
	i += copy(dAtA[i:], m.NarrowImgUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(len(m.NarrowJumpUrl)))
	i += copy(dAtA[i:], m.NarrowJumpUrl)
	return i, nil
}

func (m *AddOperActivityCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddOperActivityCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	return i, nil
}

func (m *DelOperActivityCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelOperActivityCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	return i, nil
}

func (m *DelOperActivityCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelOperActivityCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetOperActivityCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperActivityCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardId))
	return i, nil
}

func (m *GetOperActivityCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperActivityCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CardInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("card_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardInfo.Size()))
		n8, err := m.CardInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *UpdateOperActivityCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateOperActivityCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CardInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("card_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CardInfo.Size()))
		n9, err := m.CardInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *UpdateOperActivityCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateOperActivityCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserCardListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCardListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.StartIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.CountLimit))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.ExGameid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.VersionId))
	return i, nil
}

func (m *GetUserCardListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCardListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CardList) > 0 {
		for _, msg := range m.CardList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *NotifyUserInstallGameUpdateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyUserInstallGameUpdateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.Uid))
	if len(m.InstallGameidList) > 0 {
		for _, num := range m.InstallGameidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(num))
		}
	}
	if len(m.UninstallGameidList) > 0 {
		for _, num := range m.UninstallGameidList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintGamerecommendsvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *NotifyUserInstallGameUpdateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyUserInstallGameUpdateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetGameTagListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameTagListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamerecommendsvr(dAtA, i, uint64(m.GameId))
	return i, nil
}

func encodeFixed64Gamerecommendsvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Gamerecommendsvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGamerecommendsvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *TagInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.TagId))
	l = len(m.TagName)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	n += 1 + sovGamerecommendsvr(uint64(m.TagType))
	return n
}

func (m *GetAllGameTagInfoListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllGameTagInfoListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TagList) > 0 {
		for _, e := range m.TagList {
			l = e.Size()
			n += 1 + l + sovGamerecommendsvr(uint64(l))
		}
	}
	return n
}

func (m *GetGameTagsByIdListReq) Size() (n int) {
	var l int
	_ = l
	if len(m.TagIdList) > 0 {
		for _, e := range m.TagIdList {
			n += 1 + sovGamerecommendsvr(uint64(e))
		}
	}
	return n
}

func (m *GetGameTagInfoListByNameReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.TagName)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	return n
}

func (m *CreateGameTagInfoReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.TagName)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	n += 1 + sovGamerecommendsvr(uint64(m.TagType))
	return n
}

func (m *CreateGameTagInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.TagInfo != nil {
		l = m.TagInfo.Size()
		n += 1 + l + sovGamerecommendsvr(uint64(l))
	}
	return n
}

func (m *DelGameTagInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.TagId))
	return n
}

func (m *DelGameTagInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetSameTagGameListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.GameId))
	n += 1 + sovGamerecommendsvr(uint64(m.Count))
	return n
}

func (m *GetSameTagGameListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GameIdList) > 0 {
		for _, e := range m.GameIdList {
			n += 1 + sovGamerecommendsvr(uint64(e))
		}
	}
	if m.TagInfo != nil {
		l = m.TagInfo.Size()
		n += 1 + l + sovGamerecommendsvr(uint64(l))
	}
	return n
}

func (m *GameActivityInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Title)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	l = len(m.ImgUrl)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	l = len(m.SubTitle)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	n += 1 + sovGamerecommendsvr(uint64(m.LastUpdateTs))
	n += 1 + sovGamerecommendsvr(uint64(m.BeginTime))
	n += 1 + sovGamerecommendsvr(uint64(m.EndTime))
	return n
}

func (m *ComplexGameCard) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	n += 1 + sovGamerecommendsvr(uint64(m.GameId))
	if len(m.TagList) > 0 {
		for _, e := range m.TagList {
			l = e.Size()
			n += 1 + l + sovGamerecommendsvr(uint64(l))
		}
	}
	l = len(m.ImgUrl)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	if m.ActInfo != nil {
		l = m.ActInfo.Size()
		n += 1 + l + sovGamerecommendsvr(uint64(l))
	}
	return n
}

func (m *SimpleGameCard) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	n += 1 + sovGamerecommendsvr(uint64(m.GameId))
	l = len(m.ImgUrl)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	if len(m.TagList) > 0 {
		for _, e := range m.TagList {
			l = e.Size()
			n += 1 + l + sovGamerecommendsvr(uint64(l))
		}
	}
	return n
}

func (m *FixedTopListCard) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	l = len(m.Title)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	if len(m.GameidList) > 0 {
		for _, e := range m.GameidList {
			n += 1 + sovGamerecommendsvr(uint64(e))
		}
	}
	n += 1 + sovGamerecommendsvr(uint64(m.LastUpdateTs))
	n += 1 + sovGamerecommendsvr(uint64(m.SpecToplistType))
	return n
}

func (m *TagTopListCard) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	l = len(m.Title)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	if m.Tag != nil {
		l = m.Tag.Size()
		n += 1 + l + sovGamerecommendsvr(uint64(l))
	}
	if len(m.GameidList) > 0 {
		for _, e := range m.GameidList {
			n += 1 + sovGamerecommendsvr(uint64(e))
		}
	}
	n += 1 + sovGamerecommendsvr(uint64(m.LastUpdateTs))
	n += 1 + sovGamerecommendsvr(uint64(m.SpecToplistType))
	return n
}

func (m *OperActivityCard) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	if m.ActInfo != nil {
		l = m.ActInfo.Size()
		n += 1 + l + sovGamerecommendsvr(uint64(l))
	}
	n += 1 + sovGamerecommendsvr(uint64(m.GameId))
	l = len(m.NarrowImgUrl)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	l = len(m.NarrowJumpUrl)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	return n
}

func (m *RecommendIntefaceCard) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardType))
	if m.CardDetail != nil {
		l = len(m.CardDetail)
		n += 1 + l + sovGamerecommendsvr(uint64(l))
	}
	return n
}

func (m *GetAllTopListCardReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardType))
	return n
}

func (m *GetAllTopListCardResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CardList) > 0 {
		for _, e := range m.CardList {
			l = e.Size()
			n += 1 + l + sovGamerecommendsvr(uint64(l))
		}
	}
	return n
}

func (m *GetAllTopListCardByTagReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.TagId))
	return n
}

func (m *AddTopListCardReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardType))
	l = len(m.Title)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	if len(m.GameidList) > 0 {
		for _, e := range m.GameidList {
			n += 1 + sovGamerecommendsvr(uint64(e))
		}
	}
	if m.Tag != nil {
		l = m.Tag.Size()
		n += 1 + l + sovGamerecommendsvr(uint64(l))
	}
	n += 1 + sovGamerecommendsvr(uint64(m.SpecToplistType))
	return n
}

func (m *AddTopListCardResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	return n
}

func (m *DelTopListCardReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	return n
}

func (m *DelTopListCardResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *FullUpdateTopListCardGameListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	if len(m.GameidList) > 0 {
		for _, e := range m.GameidList {
			n += 1 + sovGamerecommendsvr(uint64(e))
		}
	}
	return n
}

func (m *FullUpdateTopListCardGameListResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllOperActivityCardReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllOperActivityCardResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CardList) > 0 {
		for _, e := range m.CardList {
			l = e.Size()
			n += 1 + l + sovGamerecommendsvr(uint64(l))
		}
	}
	return n
}

func (m *AddOperActivityCardReq) Size() (n int) {
	var l int
	_ = l
	if m.ActInfo != nil {
		l = m.ActInfo.Size()
		n += 1 + l + sovGamerecommendsvr(uint64(l))
	}
	n += 1 + sovGamerecommendsvr(uint64(m.GameId))
	l = len(m.NarrowImgUrl)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	l = len(m.NarrowJumpUrl)
	n += 1 + l + sovGamerecommendsvr(uint64(l))
	return n
}

func (m *AddOperActivityCardResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	return n
}

func (m *DelOperActivityCardReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	return n
}

func (m *DelOperActivityCardResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetOperActivityCardReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.CardId))
	return n
}

func (m *GetOperActivityCardResp) Size() (n int) {
	var l int
	_ = l
	if m.CardInfo != nil {
		l = m.CardInfo.Size()
		n += 1 + l + sovGamerecommendsvr(uint64(l))
	}
	return n
}

func (m *UpdateOperActivityCardReq) Size() (n int) {
	var l int
	_ = l
	if m.CardInfo != nil {
		l = m.CardInfo.Size()
		n += 1 + l + sovGamerecommendsvr(uint64(l))
	}
	return n
}

func (m *UpdateOperActivityCardResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserCardListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.Uid))
	n += 1 + sovGamerecommendsvr(uint64(m.StartIdx))
	n += 1 + sovGamerecommendsvr(uint64(m.CountLimit))
	n += 1 + sovGamerecommendsvr(uint64(m.ExGameid))
	n += 1 + sovGamerecommendsvr(uint64(m.VersionId))
	return n
}

func (m *GetUserCardListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CardList) > 0 {
		for _, e := range m.CardList {
			l = e.Size()
			n += 1 + l + sovGamerecommendsvr(uint64(l))
		}
	}
	return n
}

func (m *NotifyUserInstallGameUpdateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.Uid))
	if len(m.InstallGameidList) > 0 {
		for _, e := range m.InstallGameidList {
			n += 1 + sovGamerecommendsvr(uint64(e))
		}
	}
	if len(m.UninstallGameidList) > 0 {
		for _, e := range m.UninstallGameidList {
			n += 1 + sovGamerecommendsvr(uint64(e))
		}
	}
	return n
}

func (m *NotifyUserInstallGameUpdateResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetGameTagListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamerecommendsvr(uint64(m.GameId))
	return n
}

func sovGamerecommendsvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGamerecommendsvr(x uint64) (n int) {
	return sovGamerecommendsvr(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *TagInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TagInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TagInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllGameTagInfoListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllGameTagInfoListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllGameTagInfoListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllGameTagInfoListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllGameTagInfoListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllGameTagInfoListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagList = append(m.TagList, &TagInfo{})
			if err := m.TagList[len(m.TagList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameTagsByIdListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameTagsByIdListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameTagsByIdListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TagIdList = append(m.TagIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamerecommendsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamerecommendsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TagIdList = append(m.TagIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameTagInfoListByNameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameTagInfoListByNameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameTagInfoListByNameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateGameTagInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateGameTagInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateGameTagInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateGameTagInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateGameTagInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateGameTagInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TagInfo == nil {
				m.TagInfo = &TagInfo{}
			}
			if err := m.TagInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelGameTagInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelGameTagInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelGameTagInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelGameTagInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelGameTagInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelGameTagInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSameTagGameListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSameTagGameListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSameTagGameListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSameTagGameListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSameTagGameListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSameTagGameListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameIdList = append(m.GameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamerecommendsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamerecommendsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameIdList = append(m.GameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIdList", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TagInfo == nil {
				m.TagInfo = &TagInfo{}
			}
			if err := m.TagInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameActivityInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameActivityInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameActivityInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTs", wireType)
			}
			m.LastUpdateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("img_url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sub_title")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_ts")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ComplexGameCard) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ComplexGameCard: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ComplexGameCard: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagList = append(m.TagList, &TagInfo{})
			if err := m.TagList[len(m.TagList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ActInfo == nil {
				m.ActInfo = &GameActivityInfo{}
			}
			if err := m.ActInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SimpleGameCard) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleGameCard: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleGameCard: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagList = append(m.TagList, &TagInfo{})
			if err := m.TagList[len(m.TagList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FixedTopListCard) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FixedTopListCard: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FixedTopListCard: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameidList = append(m.GameidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamerecommendsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamerecommendsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameidList = append(m.GameidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameidList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTs", wireType)
			}
			m.LastUpdateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpecToplistType", wireType)
			}
			m.SpecToplistType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpecToplistType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TagTopListCard) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TagTopListCard: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TagTopListCard: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Tag == nil {
				m.Tag = &TagInfo{}
			}
			if err := m.Tag.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameidList = append(m.GameidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamerecommendsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamerecommendsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameidList = append(m.GameidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameidList", wireType)
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTs", wireType)
			}
			m.LastUpdateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpecToplistType", wireType)
			}
			m.SpecToplistType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpecToplistType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OperActivityCard) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OperActivityCard: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OperActivityCard: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ActInfo == nil {
				m.ActInfo = &GameActivityInfo{}
			}
			if err := m.ActInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NarrowImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NarrowImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NarrowJumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NarrowJumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("act_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecommendIntefaceCard) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecommendIntefaceCard: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecommendIntefaceCard: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardDetail", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardDetail = append(m.CardDetail[:0], dAtA[iNdEx:postIndex]...)
			if m.CardDetail == nil {
				m.CardDetail = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllTopListCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllTopListCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllTopListCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllTopListCardResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllTopListCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllTopListCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardList = append(m.CardList, &RecommendIntefaceCard{})
			if err := m.CardList[len(m.CardList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllTopListCardByTagReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllTopListCardByTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllTopListCardByTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddTopListCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddTopListCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddTopListCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameidList = append(m.GameidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamerecommendsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamerecommendsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameidList = append(m.GameidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameidList", wireType)
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Tag == nil {
				m.Tag = &TagInfo{}
			}
			if err := m.Tag.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpecToplistType", wireType)
			}
			m.SpecToplistType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpecToplistType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddTopListCardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddTopListCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddTopListCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelTopListCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelTopListCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelTopListCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelTopListCardResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelTopListCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelTopListCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FullUpdateTopListCardGameListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FullUpdateTopListCardGameListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FullUpdateTopListCardGameListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameidList = append(m.GameidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamerecommendsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamerecommendsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameidList = append(m.GameidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FullUpdateTopListCardGameListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FullUpdateTopListCardGameListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FullUpdateTopListCardGameListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllOperActivityCardReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllOperActivityCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllOperActivityCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllOperActivityCardResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllOperActivityCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllOperActivityCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardList = append(m.CardList, &OperActivityCard{})
			if err := m.CardList[len(m.CardList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddOperActivityCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddOperActivityCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddOperActivityCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ActInfo == nil {
				m.ActInfo = &GameActivityInfo{}
			}
			if err := m.ActInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NarrowImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NarrowImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NarrowJumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NarrowJumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("act_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddOperActivityCardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddOperActivityCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddOperActivityCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelOperActivityCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelOperActivityCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelOperActivityCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelOperActivityCardResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelOperActivityCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelOperActivityCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperActivityCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOperActivityCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOperActivityCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperActivityCardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOperActivityCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOperActivityCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CardInfo == nil {
				m.CardInfo = &OperActivityCard{}
			}
			if err := m.CardInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateOperActivityCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateOperActivityCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateOperActivityCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CardInfo == nil {
				m.CardInfo = &OperActivityCard{}
			}
			if err := m.CardInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateOperActivityCardResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateOperActivityCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateOperActivityCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCardListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCardListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCardListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartIdx", wireType)
			}
			m.StartIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CountLimit", wireType)
			}
			m.CountLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CountLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExGameid", wireType)
			}
			m.ExGameid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExGameid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionId", wireType)
			}
			m.VersionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_idx")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count_limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCardListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCardListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCardListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardList = append(m.CardList, &RecommendIntefaceCard{})
			if err := m.CardList[len(m.CardList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyUserInstallGameUpdateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyUserInstallGameUpdateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyUserInstallGameUpdateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.InstallGameidList = append(m.InstallGameidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamerecommendsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamerecommendsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.InstallGameidList = append(m.InstallGameidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field InstallGameidList", wireType)
			}
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UninstallGameidList = append(m.UninstallGameidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamerecommendsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamerecommendsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UninstallGameidList = append(m.UninstallGameidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UninstallGameidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyUserInstallGameUpdateResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyUserInstallGameUpdateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyUserInstallGameUpdateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameTagListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameTagListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameTagListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamerecommendsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamerecommendsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGamerecommendsvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGamerecommendsvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGamerecommendsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGamerecommendsvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGamerecommendsvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGamerecommendsvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGamerecommendsvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGamerecommendsvr   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/gameRecommend/gamerecommendsvr.proto", fileDescriptorGamerecommendsvr)
}

var fileDescriptorGamerecommendsvr = []byte{
	// 2068 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x59, 0xcf, 0x6f, 0x1b, 0xc7,
	0xf5, 0xcf, 0x92, 0x94, 0x44, 0x8d, 0x24, 0x4a, 0x1a, 0x59, 0x32, 0x45, 0xc7, 0xd2, 0x7a, 0xed,
	0x7c, 0xa3, 0x38, 0x92, 0x6d, 0x29, 0xf9, 0xb6, 0x29, 0xa1, 0x30, 0xb0, 0x2c, 0x58, 0x60, 0xe0,
	0xba, 0xa8, 0x42, 0x03, 0x41, 0xdb, 0x80, 0x58, 0x71, 0x47, 0xec, 0x3a, 0xbb, 0xcb, 0xc9, 0xce,
	0x50, 0xa1, 0x7a, 0x4a, 0x0f, 0x2d, 0xda, 0xa2, 0xbf, 0x50, 0x20, 0x3d, 0xf5, 0x56, 0xf7, 0xd6,
	0x3f, 0xa0, 0xa7, 0x9e, 0x73, 0x28, 0x8a, 0xa0, 0xe7, 0xa2, 0x2d, 0x9c, 0x43, 0xdd, 0x63, 0x4f,
	0x05, 0x7a, 0x2a, 0xde, 0xcc, 0xae, 0x38, 0xdc, 0x1d, 0x92, 0xcb, 0xd8, 0x37, 0x69, 0xe6, 0xcd,
	0x9b, 0xcf, 0xfb, 0xbc, 0x9f, 0xb3, 0x44, 0x3b, 0x8c, 0x84, 0x67, 0x6e, 0x8b, 0xb0, 0xdb, 0x6d,
	0xdb, 0x27, 0xc7, 0xa4, 0xd5, 0xf1, 0x7d, 0x12, 0x38, 0xe2, 0xbf, 0x30, 0xfe, 0x8f, 0x9d, 0x85,
	0xb7, 0x68, 0xd8, 0xe1, 0x1d, 0xbc, 0x94, 0x5c, 0xaf, 0xdc, 0x80, 0xbf, 0x3b, 0xc1, 0x6d, 0xee,
	0x9d, 0x51, 0xb7, 0xf5, 0xa1, 0x47, 0x6e, 0xb3, 0x0f, 0x4f, 0xba, 0xae, 0xc7, 0xdd, 0x80, 0x9f,
	0x53, 0x22, 0xcf, 0x59, 0xdf, 0x45, 0x33, 0x0d, 0xbb, 0x5d, 0x0f, 0x4e, 0x3b, 0xf8, 0x0a, 0x9a,
	0xe6, 0x76, 0xbb, 0xe9, 0x3a, 0x65, 0xc3, 0x34, 0xb6, 0x16, 0x0e, 0x0a, 0x9f, 0xfd, 0x6d, 0xf3,
	0xa5, 0xe3, 0x29, 0x6e, 0xb7, 0xeb, 0x0e, 0xde, 0x44, 0x45, 0xd8, 0x0c, 0x6c, 0x9f, 0x94, 0x73,
	0xa6, 0xb1, 0x35, 0x1b, 0x6d, 0xcf, 0x70, 0xbb, 0xfd, 0xd0, 0xf6, 0x49, 0x2c, 0x00, 0xaa, 0xcb,
	0x79, 0xe5, 0x3c, 0x08, 0x34, 0xce, 0x29, 0xb1, 0x2a, 0xa8, 0x7c, 0x44, 0xf8, 0x5d, 0xcf, 0x3b,
	0xb2, 0x7d, 0x12, 0xdd, 0xf9, 0xc0, 0x65, 0xfc, 0x98, 0x7c, 0x64, 0x7d, 0x13, 0xad, 0x0f, 0xd9,
	0x63, 0x14, 0xbf, 0x29, 0x35, 0x7b, 0x2e, 0xe3, 0x65, 0xc3, 0xcc, 0x6f, 0xcd, 0xed, 0xad, 0xdf,
	0x4a, 0xb1, 0x10, 0x1d, 0x12, 0xd7, 0xc1, 0x49, 0xeb, 0x2d, 0xb4, 0x76, 0x44, 0x78, 0xa4, 0x8f,
	0x1d, 0x9c, 0xd7, 0x9d, 0xe8, 0x32, 0xbc, 0x81, 0xe6, 0xa4, 0x9d, 0x7d, 0x95, 0x0b, 0xc7, 0xb3,
	0xc2, 0x4c, 0x71, 0xb2, 0x86, 0xae, 0xf4, 0x4f, 0xc6, 0x48, 0x0e, 0xce, 0x1f, 0x0a, 0x37, 0x7c,
	0x34, 0xc0, 0x84, 0x61, 0xe6, 0x52, 0x4c, 0x58, 0xef, 0xa3, 0x4b, 0xf7, 0x42, 0x62, 0x73, 0xa2,
	0xa8, 0xc8, 0x72, 0x70, 0x80, 0xc2, 0x9c, 0x8e, 0xc2, 0xaf, 0xa3, 0x55, 0x8d, 0xe6, 0x3e, 0x45,
	0x6e, 0x70, 0xda, 0x11, 0xaa, 0xc7, 0x52, 0x04, 0x7f, 0x58, 0x77, 0xd0, 0xf2, 0x21, 0xf1, 0x12,
	0x28, 0xd5, 0x28, 0xc8, 0x25, 0xa2, 0xc0, 0xba, 0x84, 0x70, 0xf2, 0x04, 0xa3, 0xd6, 0x31, 0x5a,
	0x3d, 0x22, 0xfc, 0x3d, 0xb9, 0x0a, 0x9b, 0x31, 0xd3, 0x57, 0xd1, 0x0c, 0xa0, 0x48, 0x2a, 0x9b,
	0x86, 0xc5, 0xba, 0x83, 0x2b, 0x68, 0xaa, 0xd5, 0xe9, 0x06, 0xbc, 0x9c, 0x53, 0x6f, 0x12, 0x4b,
	0x16, 0x15, 0xee, 0x4b, 0xe9, 0x64, 0x14, 0x9b, 0x68, 0x3e, 0x52, 0xaa, 0xfa, 0x0f, 0x49, 0x9d,
	0x20, 0x35, 0xc0, 0x06, 0xf0, 0x98, 0x8d, 0x8d, 0xff, 0x1a, 0x68, 0x09, 0x2e, 0xba, 0xdb, 0xe2,
	0xee, 0x99, 0xcb, 0xcf, 0x45, 0x4e, 0x54, 0xd0, 0x14, 0x77, 0xb9, 0x37, 0xe8, 0x30, 0xb9, 0x84,
	0xd7, 0x50, 0xbe, 0x1b, 0x7a, 0x02, 0x7c, 0xbc, 0x03, 0x0b, 0x60, 0xb5, 0xeb, 0xb7, 0x9b, 0xb0,
	0x97, 0x57, 0xf6, 0xa6, 0x5d, 0xbf, 0xfd, 0x28, 0xf4, 0xf0, 0x35, 0x34, 0xcb, 0xba, 0x27, 0x4d,
	0xa9, 0xb6, 0xa0, 0x08, 0x14, 0x59, 0xf7, 0xa4, 0x21, 0x34, 0xdf, 0x44, 0x25, 0xcf, 0x66, 0xbc,
	0xd9, 0xa5, 0x8e, 0xcd, 0x49, 0x93, 0xb3, 0xf2, 0x94, 0xc2, 0xd0, 0x3c, 0xec, 0x3d, 0x12, 0x5b,
	0x0d, 0x86, 0xaf, 0x23, 0x74, 0x42, 0xda, 0x6e, 0xd0, 0xe4, 0xae, 0x4f, 0xca, 0xd3, 0x8a, 0xdc,
	0xac, 0x58, 0x6f, 0xb8, 0x32, 0xb2, 0x48, 0xe0, 0x48, 0x91, 0x19, 0x45, 0x64, 0x86, 0x04, 0x0e,
	0x08, 0x58, 0x5f, 0x18, 0x68, 0xf1, 0x5e, 0xc7, 0xa7, 0x1e, 0xe9, 0x01, 0x07, 0xf7, 0xec, 0xd0,
	0x01, 0x3b, 0x5a, 0x76, 0xe8, 0xc4, 0xde, 0x2b, 0xc4, 0x76, 0xc0, 0x62, 0xdd, 0x51, 0x9d, 0x9b,
	0xd3, 0x38, 0x57, 0xcd, 0xda, 0x7c, 0xd6, 0xac, 0x55, 0xb9, 0x2b, 0x28, 0x55, 0x26, 0xe6, 0xee,
	0x6d, 0x54, 0xb4, 0x5b, 0x5c, 0x7a, 0x76, 0x4a, 0x78, 0xd6, 0x4a, 0x2b, 0x4d, 0x3a, 0xf1, 0x78,
	0xc6, 0x6e, 0x71, 0xe1, 0xe2, 0xdf, 0x1a, 0xa8, 0xf4, 0x9e, 0x0b, 0x56, 0xbe, 0x20, 0x23, 0x07,
	0x5c, 0x9d, 0x86, 0xab, 0x72, 0x50, 0xc8, 0x5c, 0xb9, 0xfe, 0x64, 0xa0, 0xa5, 0xfb, 0x6e, 0x8f,
	0x38, 0x8d, 0x0e, 0x85, 0x85, 0x2c, 0x38, 0x2f, 0xe2, 0x34, 0x97, 0x8e, 0xd3, 0x4d, 0x34, 0x07,
	0x97, 0xc6, 0xf9, 0x92, 0xef, 0xe7, 0x8b, 0x2b, 0xf3, 0x25, 0x1d, 0x6e, 0x05, 0xa5, 0xfa, 0x0c,
	0x86, 0xdb, 0x1d, 0xb4, 0xcc, 0x28, 0x69, 0x35, 0x79, 0x87, 0x82, 0x36, 0x59, 0xac, 0xa6, 0x14,
	0xf1, 0x45, 0xd8, 0x6e, 0xc8, 0x5d, 0x51, 0xb4, 0xfe, 0x63, 0xa0, 0x52, 0xc3, 0x6e, 0xbf, 0x20,
	0x63, 0x5e, 0x47, 0x79, 0x6e, 0xb7, 0x45, 0x62, 0x8d, 0x64, 0x13, 0xa4, 0x92, 0x96, 0x17, 0x32,
	0x58, 0x3e, 0x35, 0x99, 0xe5, 0xd3, 0xa3, 0x2c, 0xff, 0x97, 0x81, 0x96, 0xbe, 0x41, 0x49, 0x18,
	0x07, 0x63, 0x16, 0xdb, 0xd5, 0x08, 0xcf, 0x09, 0x23, 0x27, 0x89, 0x70, 0x35, 0x5e, 0xd5, 0x26,
	0x1c, 0xc7, 0xeb, 0x4d, 0x54, 0x0a, 0xec, 0x30, 0xec, 0x7c, 0xdc, 0xd4, 0x65, 0xd9, 0xbc, 0xdc,
	0xab, 0xcb, 0xe0, 0xdd, 0x46, 0x8b, 0x91, 0xec, 0xe3, 0xae, 0x4f, 0x85, 0xf0, 0x94, 0x22, 0xbc,
	0x20, 0x37, 0xdf, 0xed, 0xfa, 0xf4, 0x51, 0xe8, 0x59, 0x36, 0x5a, 0xbd, 0x98, 0x51, 0xea, 0x01,
	0x27, 0xa7, 0x76, 0x4b, 0x26, 0xd8, 0x35, 0x34, 0x2b, 0xec, 0x15, 0x74, 0xa9, 0x5d, 0xa0, 0x08,
	0xcb, 0xc0, 0x13, 0x7e, 0x05, 0xcd, 0x09, 0x11, 0x87, 0x70, 0xdb, 0xf5, 0x44, 0xc9, 0x9e, 0x8f,
	0x84, 0x10, 0x6c, 0x1c, 0x8a, 0x75, 0xeb, 0x6b, 0xe8, 0x92, 0x1c, 0x12, 0x94, 0x50, 0x82, 0x2e,
	0x33, 0xfe, 0x06, 0xeb, 0x03, 0xd1, 0xa1, 0x92, 0x47, 0x19, 0xc5, 0x87, 0xd1, 0x59, 0x65, 0xb8,
	0x78, 0x35, 0xcd, 0xb7, 0xd6, 0x32, 0xa9, 0x3e, 0x9a, 0x35, 0xd6, 0x53, 0xea, 0x0f, 0xce, 0x1b,
	0x76, 0x7b, 0x6c, 0x43, 0xfd, 0x8b, 0x81, 0x96, 0xef, 0x3a, 0xce, 0xc4, 0x16, 0x3d, 0x5f, 0xc2,
	0x47, 0x49, 0x54, 0x18, 0xd7, 0x1b, 0x45, 0x12, 0x4d, 0x9e, 0xf1, 0x6f, 0x20, 0x9c, 0xb4, 0x89,
	0xd1, 0x31, 0x81, 0x6f, 0xed, 0x89, 0x61, 0x24, 0x41, 0xc4, 0x98, 0x33, 0x72, 0x1c, 0x49, 0x5c,
	0x64, 0x9d, 0x20, 0xf3, 0x7e, 0xd7, 0xf3, 0xa2, 0xc4, 0xed, 0x6f, 0x26, 0x26, 0x93, 0x51, 0x59,
	0x98, 0x60, 0x30, 0x97, 0x64, 0xd0, 0xba, 0x8e, 0xae, 0x8d, 0xb9, 0x83, 0x51, 0xeb, 0x4a, 0x1c,
	0x16, 0xc9, 0x22, 0x00, 0x23, 0xef, 0x07, 0xa8, 0x32, 0x6c, 0x93, 0x51, 0xfc, 0x4e, 0x3a, 0x2e,
	0x35, 0x75, 0x20, 0x75, 0xb4, 0x1f, 0x92, 0x9f, 0x1b, 0x68, 0xed, 0xae, 0xe3, 0x68, 0x6e, 0x1e,
	0x28, 0x31, 0xc6, 0x73, 0x95, 0x98, 0x5c, 0xa6, 0x12, 0x93, 0x9f, 0xa4, 0xc4, 0x14, 0x86, 0x97,
	0x98, 0xb7, 0xd0, 0x65, 0xad, 0x45, 0xe3, 0x63, 0xeb, 0xab, 0x68, 0xed, 0x90, 0xe8, 0xbc, 0x30,
	0xee, 0xe0, 0x3a, 0xba, 0xac, 0x3d, 0xc8, 0x28, 0xe8, 0x3c, 0x22, 0xfc, 0x4b, 0xe8, 0xfc, 0x16,
	0xba, 0xac, 0x3d, 0xa8, 0x78, 0x7d, 0xb4, 0x6b, 0xf4, 0x5e, 0x17, 0x03, 0xce, 0x77, 0xd0, 0xba,
	0x0c, 0x49, 0x1d, 0xae, 0xe7, 0xd6, 0xfe, 0x32, 0xaa, 0x0c, 0xd3, 0xce, 0xa8, 0xf5, 0x47, 0x03,
	0xe1, 0x23, 0xc2, 0x1f, 0x31, 0x12, 0xde, 0x8b, 0xa2, 0x10, 0x6e, 0x85, 0x29, 0x39, 0x51, 0xfb,
	0x60, 0x41, 0x8c, 0xc1, 0xdc, 0x0e, 0x79, 0xd3, 0x75, 0x7a, 0x03, 0xb3, 0x55, 0x51, 0x2c, 0xd7,
	0x9d, 0x9e, 0xe8, 0x0b, 0xf0, 0x18, 0x68, 0x7a, 0xae, 0xef, 0x72, 0xd1, 0xf3, 0x17, 0x2e, 0xfa,
	0x02, 0x6c, 0x3c, 0x80, 0x75, 0xd0, 0x44, 0x7a, 0x4d, 0x99, 0x9c, 0x03, 0x93, 0x4b, 0x51, 0x0e,
	0xb3, 0xae, 0x03, 0x43, 0xf2, 0x19, 0x09, 0x99, 0xdb, 0x09, 0xc0, 0x2b, 0x6a, 0xf1, 0x9a, 0x8d,
	0xd6, 0xeb, 0x8e, 0xf5, 0x6d, 0xb4, 0x92, 0xc2, 0xff, 0xc2, 0x5a, 0xc4, 0x6f, 0x0c, 0xb4, 0xf1,
	0xb0, 0xc3, 0xdd, 0xd3, 0x73, 0xb8, 0xa0, 0x1e, 0x30, 0x6e, 0xcb, 0xd7, 0xae, 0x64, 0x74, 0x14,
	0x53, 0xb7, 0xd0, 0x8a, 0x2b, 0xe5, 0x9b, 0xe9, 0xa2, 0xb4, 0xec, 0xf6, 0x55, 0x45, 0xd5, 0x7d,
	0x0f, 0xad, 0x76, 0x03, 0xdd, 0x09, 0xd9, 0x08, 0x56, 0x2e, 0x36, 0xfb, 0x67, 0xac, 0x6b, 0x68,
	0x73, 0x24, 0x3a, 0x46, 0xa1, 0x40, 0xf7, 0x9f, 0xc5, 0xd9, 0x5e, 0x78, 0x7b, 0x7f, 0x58, 0x47,
	0xa9, 0x0f, 0x13, 0xf8, 0xa7, 0x46, 0xdc, 0x8d, 0x13, 0x6f, 0x6c, 0x7c, 0x53, 0x53, 0x87, 0x86,
	0x7c, 0x32, 0xa8, 0xbc, 0x9e, 0x59, 0x96, 0x51, 0xab, 0xf2, 0xc9, 0x93, 0x67, 0x79, 0xe3, 0x27,
	0x4f, 0x9e, 0xe5, 0x73, 0xdd, 0xea, 0xaf, 0x9e, 0x3c, 0xcb, 0xcf, 0xee, 0x74, 0xcd, 0xfd, 0xae,
	0xeb, 0xd4, 0x4c, 0xfc, 0x3b, 0x03, 0x2d, 0xa7, 0x5e, 0xd5, 0xf8, 0xff, 0xd2, 0xea, 0x75, 0x8f,
	0xfa, 0xca, 0xab, 0x99, 0xe4, 0x18, 0xb5, 0xde, 0x01, 0x08, 0x39, 0x80, 0x50, 0xe0, 0xd5, 0x9e,
	0x00, 0xb1, 0xbd, 0xc3, 0xcd, 0x7d, 0x6e, 0xb7, 0xcd, 0xc0, 0xf6, 0x49, 0xcd, 0xdc, 0xe9, 0xc9,
	0xff, 0xa0, 0xc1, 0x9a, 0xbb, 0x55, 0xd0, 0x6b, 0xee, 0x55, 0xa3, 0xb6, 0x5b, 0xc3, 0xdf, 0x43,
	0xa5, 0xc1, 0xb7, 0x37, 0xbe, 0x9e, 0xbe, 0x3b, 0xf5, 0x9e, 0xaf, 0xdc, 0x18, 0x2f, 0xc4, 0xa8,
	0x75, 0x15, 0xd0, 0xe5, 0x05, 0x41, 0x5c, 0x60, 0x9b, 0x8f, 0xb1, 0x09, 0x8e, 0x7e, 0x6f, 0x08,
	0xe7, 0x0f, 0x4e, 0x38, 0x3a, 0x8e, 0x74, 0x03, 0x9a, 0x8e, 0x23, 0xed, 0x34, 0x66, 0xdd, 0x07,
	0x14, 0xd3, 0x0a, 0x8a, 0xff, 0x07, 0x14, 0xb0, 0x6d, 0xc2, 0x60, 0xb1, 0x6d, 0x76, 0x02, 0xef,
	0xdc, 0x64, 0x5d, 0x4a, 0x3b, 0xdc, 0xdc, 0x7d, 0xb3, 0x1a, 0x3f, 0xae, 0xcc, 0xdd, 0xaf, 0x54,
	0x4f, 0xe1, 0xc5, 0xd4, 0x94, 0x54, 0xfd, 0xd3, 0x40, 0xa5, 0xc1, 0x09, 0x44, 0xc7, 0x55, 0x6a,
	0xee, 0xd2, 0x71, 0x95, 0x1e, 0x64, 0xac, 0x1f, 0x1b, 0x00, 0x73, 0x06, 0x60, 0x16, 0xc1, 0x95,
	0xed, 0x2a, 0x11, 0x60, 0xc3, 0x2f, 0x05, 0x56, 0xb8, 0x3d, 0xba, 0xc2, 0x7c, 0x28, 0x03, 0xa1,
	0x6d, 0xee, 0x8b, 0x04, 0x3a, 0x94, 0x07, 0x5a, 0x76, 0x60, 0x76, 0x19, 0xd9, 0x66, 0xd4, 0x73,
	0xe1, 0x00, 0xb9, 0xf0, 0x0c, 0xfe, 0xbe, 0x21, 0xa2, 0x62, 0x8c, 0xa5, 0xa9, 0xc1, 0x6a, 0x48,
	0x54, 0x24, 0x2d, 0xbd, 0x01, 0x86, 0x16, 0x15, 0x7f, 0xac, 0x80, 0x89, 0x8a, 0x94, 0x59, 0x3f,
	0xac, 0x41, 0x70, 0x2c, 0x26, 0x0a, 0x27, 0xbe, 0xa1, 0x75, 0x79, 0xa2, 0x37, 0x54, 0x5e, 0xc9,
	0x20, 0xc5, 0xa8, 0xf5, 0x00, 0x60, 0xcc, 0x0a, 0xbe, 0xbb, 0xd5, 0x5e, 0x95, 0x54, 0xe3, 0xe0,
	0x88, 0x73, 0x18, 0x28, 0x14, 0x1f, 0x3b, 0x4c, 0xd7, 0xe9, 0x49, 0x82, 0x48, 0xcf, 0x94, 0xa5,
	0xaf, 0x66, 0x02, 0xe6, 0xb8, 0xca, 0x1f, 0xd6, 0xf0, 0xcf, 0x0c, 0x51, 0xe7, 0x93, 0x5f, 0x06,
	0xf1, 0x96, 0x16, 0x8c, 0xe6, 0x03, 0xe2, 0x64, 0xa5, 0x67, 0x13, 0xc0, 0x23, 0x85, 0xc3, 0x52,
	0x94, 0x59, 0x4d, 0xd7, 0x31, 0x77, 0x6b, 0x26, 0xfe, 0xb3, 0x81, 0xae, 0x8c, 0xa8, 0xbd, 0xf8,
	0x4e, 0xfa, 0xb6, 0xd1, 0x8d, 0xa4, 0xb2, 0x3b, 0xe1, 0x09, 0x46, 0xad, 0x77, 0x01, 0xe5, 0x1c,
	0xa0, 0x9c, 0xee, 0x8a, 0x90, 0x4e, 0x13, 0x1c, 0x35, 0x0d, 0x41, 0xab, 0xa8, 0x0a, 0x10, 0xa6,
	0x17, 0xbd, 0xa4, 0xbf, 0x8c, 0xff, 0x6a, 0xa0, 0xab, 0x23, 0x87, 0x63, 0xbc, 0x97, 0x06, 0x38,
	0x6e, 0x62, 0xaf, 0xbc, 0x31, 0xf1, 0x19, 0x46, 0xad, 0x63, 0x30, 0x6b, 0x3e, 0x61, 0xd6, 0xdb,
	0x7d, 0xb3, 0xc0, 0x13, 0xb2, 0xbe, 0x9a, 0xd0, 0xa9, 0x2f, 0xcc, 0x1a, 0x91, 0x7d, 0xf8, 0xe7,
	0x86, 0x98, 0xfc, 0x34, 0x93, 0x3b, 0x1e, 0x1a, 0x18, 0x9a, 0x71, 0xac, 0xb2, 0x9d, 0x5d, 0x98,
	0x51, 0x6b, 0x1d, 0x2c, 0x29, 0x29, 0x1d, 0xac, 0x18, 0x5b, 0x81, 0xff, 0x6e, 0xa0, 0x15, 0xcd,
	0x60, 0xac, 0x0b, 0x68, 0xfd, 0x8b, 0xa0, 0xf2, 0x5a, 0x46, 0x49, 0x46, 0x2d, 0x06, 0x38, 0x16,
	0x01, 0xc7, 0x3c, 0xf0, 0xe9, 0x57, 0x1f, 0x57, 0x4f, 0xa2, 0xfa, 0xf7, 0x3e, 0x44, 0x49, 0x90,
	0xa8, 0x60, 0x35, 0x73, 0xc7, 0x37, 0xf7, 0xa3, 0xa7, 0x40, 0xcd, 0xdc, 0x79, 0x6c, 0xee, 0xc3,
	0xac, 0x6f, 0xca, 0xff, 0x4e, 0xe2, 0xbc, 0xe5, 0xae, 0x4f, 0x18, 0xb7, 0x7d, 0x1a, 0x65, 0x6f,
	0xe0, 0x28, 0x4b, 0xf8, 0x53, 0x03, 0xad, 0x68, 0xe6, 0x70, 0x9d, 0x85, 0xfa, 0x39, 0x5f, 0x67,
	0xe1, 0xb0, 0xc1, 0xfe, 0x35, 0xb0, 0x70, 0x49, 0x49, 0xd8, 0x32, 0x84, 0x49, 0x52, 0x54, 0x54,
	0xdf, 0x4f, 0x65, 0x29, 0xc9, 0x82, 0x4b, 0xff, 0x56, 0xd0, 0xe1, 0x1a, 0xf2, 0x38, 0x90, 0xb8,
	0x96, 0x33, 0xe1, 0xfa, 0x41, 0x0e, 0xad, 0xe9, 0x27, 0x75, 0x5d, 0x88, 0x0e, 0x7d, 0x31, 0xe8,
	0x42, 0x74, 0xc4, 0x03, 0xe0, 0x17, 0xa2, 0x2f, 0x62, 0x40, 0x58, 0x92, 0x7d, 0x51, 0x8d, 0x8e,
	0x70, 0x18, 0x5a, 0xb3, 0x1f, 0x36, 0x2f, 0x3a, 0x6e, 0x7e, 0x6d, 0x88, 0xdf, 0x9c, 0xb4, 0x3f,
	0xe5, 0xe0, 0x9d, 0x51, 0xf5, 0x3e, 0xf5, 0xb3, 0xcf, 0x64, 0x45, 0x7f, 0x03, 0x98, 0x58, 0x11,
	0xbe, 0x0a, 0x84, 0xf5, 0x0b, 0x3b, 0x81, 0x2c, 0xfa, 0xc2, 0x50, 0x18, 0x81, 0xd7, 0xf4, 0x5f,
	0x8c, 0x86, 0xd7, 0x10, 0xcd, 0xb7, 0xa5, 0xec, 0x93, 0xd5, 0xcb, 0x00, 0xe8, 0x92, 0x12, 0x3c,
	0x73, 0xfd, 0x2e, 0x54, 0xc3, 0x9f, 0x18, 0xa8, 0x34, 0x38, 0xdb, 0xeb, 0xa6, 0x88, 0xd4, 0xf4,
	0x3f, 0x19, 0x27, 0x02, 0xc2, 0xaa, 0x80, 0xd0, 0x8e, 0x20, 0x44, 0xde, 0x06, 0x08, 0x3f, 0x94,
	0xaf, 0xc7, 0xc4, 0x0f, 0x3e, 0x58, 0x6f, 0x60, 0xfa, 0xa7, 0xa6, 0xca, 0x56, 0x36, 0xc1, 0x78,
	0xd4, 0x5d, 0x53, 0x70, 0xcc, 0xc7, 0x38, 0x20, 0x1a, 0x2b, 0xd3, 0x3f, 0x7a, 0xf2, 0x2c, 0xff,
	0xef, 0x8f, 0x0f, 0x96, 0x3e, 0x7b, 0xba, 0x61, 0x7c, 0xfe, 0x74, 0xc3, 0xf8, 0xc7, 0xd3, 0x0d,
	0xe3, 0x97, 0x5f, 0x6c, 0xbc, 0xf4, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xba, 0xa2, 0xcc, 0x3a,
	0x92, 0x1d, 0x00, 0x00,
}
