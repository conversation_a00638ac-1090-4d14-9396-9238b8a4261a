// Code generated by protoc-gen-gogo.
// source: src/userTagSvr/usertag.proto
// DO NOT EDIT!

/*
	Package userTagSvr is a generated protocol buffer package.

	It is generated from these files:
		src/userTagSvr/usertag.proto

	It has these top-level messages:
		UserTagBase
		UserTagList
		UserAgeTagExt
		UserFindWhoTagExt
		GameOptSecondProp
		UserGameTagOpt
		GameScreenShot
		UserGameTagExt
		LevelImg
		ConfGameTagOpt
		ConfGameTagExt
		SimpleGameTagList
		SimpleGameTagExt
		UserOptPersonalTagClassify
		UserOptPersonalTagExt
		GetUserTagReq
		GetUserTagResp
		SetUserTagReq
		SetUserTagResp
		UserTagReplaceInfo
		SetUserOneTagReq
		SetUserOneTagResp
		BatGetUserTagReq
		BatGetUserTagResp
		GetTagConfigListReq
		GetTagConfigListResp
		CreateTagConfigReq
		CreateTagConfigResp
		ModifyTagConfigReq
		ModifyTagConfigResp
		DelTagConfigReq
		DelTagConfigResp
		SortTagConfigReq
		SortTagConfigResp
		CreateFindWhoTypeTagConfigReq
		CreateFindWhoTypeTagConfigResp
		CreateOptPersonalTagClassifyReq
		CreateOptPersonalTagClassifyResp
		GetOptPersonalTagClassifyListReq
		GetOptPersonalTagClassifyListResp
		GetOptPersonalTagByClassifyReq
		GetOptPersonalTagByClassifyResp
		ModifyOptPersonalTagClassifyNameReq
		ModifyOptPersonalTagClassifyNameResp
		SortOptPersonalTagClassifyReq
		SortOptPersonalTagClassifyResp
		DelOptPersonalTagClassifyReq
		DelOptPersonalTagClassifyResp
		GetUserGameTagReq
		GetUserGameTagResp
		GetUserGameTagDanReq
		GameTagDan
		GetUserGameTagDanResp
		GetSimpleGameTagReq
		GetSimpleGameTagResp
		BatGetSimpleGameTagReq
		BatGetSimpleGameTagResp
		NotifyChannelPlayChangeReq
		NotifyChannelPlayChangeResp
		SetUserGameNickReq
		SetUserGameNickResp
		SetUserGameScreenShotReq
		SetUserGameScreenShotResp
		ChangeRecommendStatusReq
		ChangeRecommendStatusResp
		GetRecommendStatusReq
		GetRecommendStatusResp
		SetMosaicImageContext
		SetMosaicImageReq
		SetMosaicImageResp
*/
package userTagSvr

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 想要找什么样的人 类型的标签
type EUserTagFindWhoType int32

const (
	EUserTagFindWhoType_ENUM_FINDWHOTAG_INVALID          EUserTagFindWhoType = 0
	EUserTagFindWhoType_ENUM_FINDWHOTAG_OPPOSITE_SEX     EUserTagFindWhoType = 1
	EUserTagFindWhoType_ENUM_FINDWHOTAG_ASSIGN_SEX       EUserTagFindWhoType = 2
	EUserTagFindWhoType_ENUM_FINDWHOTAG_CORRELATION_PAIR EUserTagFindWhoType = 3
	EUserTagFindWhoType_ENUM_FINDWHOTAG_GAME             EUserTagFindWhoType = 4
)

var EUserTagFindWhoType_name = map[int32]string{
	0: "ENUM_FINDWHOTAG_INVALID",
	1: "ENUM_FINDWHOTAG_OPPOSITE_SEX",
	2: "ENUM_FINDWHOTAG_ASSIGN_SEX",
	3: "ENUM_FINDWHOTAG_CORRELATION_PAIR",
	4: "ENUM_FINDWHOTAG_GAME",
}
var EUserTagFindWhoType_value = map[string]int32{
	"ENUM_FINDWHOTAG_INVALID":          0,
	"ENUM_FINDWHOTAG_OPPOSITE_SEX":     1,
	"ENUM_FINDWHOTAG_ASSIGN_SEX":       2,
	"ENUM_FINDWHOTAG_CORRELATION_PAIR": 3,
	"ENUM_FINDWHOTAG_GAME":             4,
}

func (x EUserTagFindWhoType) String() string {
	return proto.EnumName(EUserTagFindWhoType_name, int32(x))
}
func (EUserTagFindWhoType) EnumDescriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{0} }

type EGameScreenStatus int32

const (
	EGameScreenStatus_EGAMESCREEN_AUDITING     EGameScreenStatus = 0
	EGameScreenStatus_EGAMESCREEN_AUDIT_REJECT EGameScreenStatus = 1
	EGameScreenStatus_EGAMESCREEN_AUDIT_PASS   EGameScreenStatus = 2
)

var EGameScreenStatus_name = map[int32]string{
	0: "EGAMESCREEN_AUDITING",
	1: "EGAMESCREEN_AUDIT_REJECT",
	2: "EGAMESCREEN_AUDIT_PASS",
}
var EGameScreenStatus_value = map[string]int32{
	"EGAMESCREEN_AUDITING":     0,
	"EGAMESCREEN_AUDIT_REJECT": 1,
	"EGAMESCREEN_AUDIT_PASS":   2,
}

func (x EGameScreenStatus) String() string {
	return proto.EnumName(EGameScreenStatus_name, int32(x))
}
func (EGameScreenStatus) EnumDescriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{1} }

type EGameExtraOpt int32

const (
	EGameExtraOpt_ENUM_GAMEEXTRAOPT_INVALID               EGameExtraOpt = 0
	EGameExtraOpt_ENUM_GAMEEXTRAOPT_GAME_NICK             EGameExtraOpt = 1
	EGameExtraOpt_ENUM_GAMEEXTRAOPT_SCREENSHOT            EGameExtraOpt = 2
	EGameExtraOpt_ENUM_GAMEEXTRAOPT_GAMENICK_TEAM_VISIBLE EGameExtraOpt = 4
)

var EGameExtraOpt_name = map[int32]string{
	0: "ENUM_GAMEEXTRAOPT_INVALID",
	1: "ENUM_GAMEEXTRAOPT_GAME_NICK",
	2: "ENUM_GAMEEXTRAOPT_SCREENSHOT",
	4: "ENUM_GAMEEXTRAOPT_GAMENICK_TEAM_VISIBLE",
}
var EGameExtraOpt_value = map[string]int32{
	"ENUM_GAMEEXTRAOPT_INVALID":               0,
	"ENUM_GAMEEXTRAOPT_GAME_NICK":             1,
	"ENUM_GAMEEXTRAOPT_SCREENSHOT":            2,
	"ENUM_GAMEEXTRAOPT_GAMENICK_TEAM_VISIBLE": 4,
}

func (x EGameExtraOpt) String() string {
	return proto.EnumName(EGameExtraOpt_name, int32(x))
}
func (EGameExtraOpt) EnumDescriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{2} }

// 用户标签
type UserTagBase struct {
	TagType uint32 `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	TagName string `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	TagId   uint32 `protobuf:"varint,3,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagInfo []byte `protobuf:"bytes,4,opt,name=tag_info,json=tagInfo,proto3" json:"tag_info,omitempty"`
	IsDel   bool   `protobuf:"varint,5,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
}

func (m *UserTagBase) Reset()                    { *m = UserTagBase{} }
func (m *UserTagBase) String() string            { return proto.CompactTextString(m) }
func (*UserTagBase) ProtoMessage()               {}
func (*UserTagBase) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{0} }

func (m *UserTagBase) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *UserTagBase) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *UserTagBase) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *UserTagBase) GetTagInfo() []byte {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

func (m *UserTagBase) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type UserTagList struct {
	Uid     uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TagList []*UserTagBase `protobuf:"bytes,2,rep,name=tag_list,json=tagList" json:"tag_list,omitempty"`
}

func (m *UserTagList) Reset()                    { *m = UserTagList{} }
func (m *UserTagList) String() string            { return proto.CompactTextString(m) }
func (*UserTagList) ProtoMessage()               {}
func (*UserTagList) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{1} }

func (m *UserTagList) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserTagList) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

// 年龄段 类型的标签
type UserAgeTagExt struct {
	AgeYearMin uint32 `protobuf:"varint,1,opt,name=age_year_min,json=ageYearMin,proto3" json:"age_year_min,omitempty"`
	AgeYearMax uint32 `protobuf:"varint,2,opt,name=age_year_max,json=ageYearMax,proto3" json:"age_year_max,omitempty"`
}

func (m *UserAgeTagExt) Reset()                    { *m = UserAgeTagExt{} }
func (m *UserAgeTagExt) String() string            { return proto.CompactTextString(m) }
func (*UserAgeTagExt) ProtoMessage()               {}
func (*UserAgeTagExt) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{2} }

func (m *UserAgeTagExt) GetAgeYearMin() uint32 {
	if m != nil {
		return m.AgeYearMin
	}
	return 0
}

func (m *UserAgeTagExt) GetAgeYearMax() uint32 {
	if m != nil {
		return m.AgeYearMax
	}
	return 0
}

// 想要找什么样的人 类型的标签
type UserFindWhoTagExt struct {
	FindwhoType          uint32 `protobuf:"varint,1,opt,name=findwho_type,json=findwhoType,proto3" json:"findwho_type,omitempty"`
	AssignSex            uint32 `protobuf:"varint,2,opt,name=assign_sex,json=assignSex,proto3" json:"assign_sex,omitempty"`
	CorrelationPairTagId uint32 `protobuf:"varint,3,opt,name=correlation_pair_tag_id,json=correlationPairTagId,proto3" json:"correlation_pair_tag_id,omitempty"`
}

func (m *UserFindWhoTagExt) Reset()                    { *m = UserFindWhoTagExt{} }
func (m *UserFindWhoTagExt) String() string            { return proto.CompactTextString(m) }
func (*UserFindWhoTagExt) ProtoMessage()               {}
func (*UserFindWhoTagExt) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{3} }

func (m *UserFindWhoTagExt) GetFindwhoType() uint32 {
	if m != nil {
		return m.FindwhoType
	}
	return 0
}

func (m *UserFindWhoTagExt) GetAssignSex() uint32 {
	if m != nil {
		return m.AssignSex
	}
	return 0
}

func (m *UserFindWhoTagExt) GetCorrelationPairTagId() uint32 {
	if m != nil {
		return m.CorrelationPairTagId
	}
	return 0
}

// 选项二级属性
type GameOptSecondProp struct {
	OptProp   string   `protobuf:"bytes,1,opt,name=opt_prop,json=optProp,proto3" json:"opt_prop,omitempty"`
	ValueList []string `protobuf:"bytes,2,rep,name=value_list,json=valueList" json:"value_list,omitempty"`
}

func (m *GameOptSecondProp) Reset()                    { *m = GameOptSecondProp{} }
func (m *GameOptSecondProp) String() string            { return proto.CompactTextString(m) }
func (*GameOptSecondProp) ProtoMessage()               {}
func (*GameOptSecondProp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{4} }

func (m *GameOptSecondProp) GetOptProp() string {
	if m != nil {
		return m.OptProp
	}
	return ""
}

func (m *GameOptSecondProp) GetValueList() []string {
	if m != nil {
		return m.ValueList
	}
	return nil
}

type UserGameTagOpt struct {
	OptName           string               `protobuf:"bytes,1,opt,name=opt_name,json=optName,proto3" json:"opt_name,omitempty"`
	OptId             uint32               `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	IsSupportMutiSet  bool                 `protobuf:"varint,3,opt,name=is_support_muti_set,json=isSupportMutiSet,proto3" json:"is_support_muti_set,omitempty"`
	ValueConfList     []string             `protobuf:"bytes,4,rep,name=value_conf_list,json=valueConfList" json:"value_conf_list,omitempty"`
	ValueUsersetList  []string             `protobuf:"bytes,5,rep,name=value_userset_list,json=valueUsersetList" json:"value_userset_list,omitempty"`
	SupportMutiSetCnt uint32               `protobuf:"varint,6,opt,name=support_muti_set_cnt,json=supportMutiSetCnt,proto3" json:"support_muti_set_cnt,omitempty"`
	PartitionId       uint32               `protobuf:"varint,7,opt,name=partition_id,json=partitionId,proto3" json:"partition_id,omitempty"`
	PropValueList     []*GameOptSecondProp `protobuf:"bytes,8,rep,name=prop_value_list,json=propValueList" json:"prop_value_list,omitempty"`
}

func (m *UserGameTagOpt) Reset()                    { *m = UserGameTagOpt{} }
func (m *UserGameTagOpt) String() string            { return proto.CompactTextString(m) }
func (*UserGameTagOpt) ProtoMessage()               {}
func (*UserGameTagOpt) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{5} }

func (m *UserGameTagOpt) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *UserGameTagOpt) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *UserGameTagOpt) GetIsSupportMutiSet() bool {
	if m != nil {
		return m.IsSupportMutiSet
	}
	return false
}

func (m *UserGameTagOpt) GetValueConfList() []string {
	if m != nil {
		return m.ValueConfList
	}
	return nil
}

func (m *UserGameTagOpt) GetValueUsersetList() []string {
	if m != nil {
		return m.ValueUsersetList
	}
	return nil
}

func (m *UserGameTagOpt) GetSupportMutiSetCnt() uint32 {
	if m != nil {
		return m.SupportMutiSetCnt
	}
	return 0
}

func (m *UserGameTagOpt) GetPartitionId() uint32 {
	if m != nil {
		return m.PartitionId
	}
	return 0
}

func (m *UserGameTagOpt) GetPropValueList() []*GameOptSecondProp {
	if m != nil {
		return m.PropValueList
	}
	return nil
}

type GameScreenShot struct {
	AuditStatus             uint32 `protobuf:"varint,1,opt,name=audit_status,json=auditStatus,proto3" json:"audit_status,omitempty"`
	ImgUrl                  string `protobuf:"bytes,2,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	ImgUrlInvisibleGamenick string `protobuf:"bytes,3,opt,name=img_url_invisible_gamenick,json=imgUrlInvisibleGamenick,proto3" json:"img_url_invisible_gamenick,omitempty"`
	BeginAuditTime          uint32 `protobuf:"varint,4,opt,name=begin_audit_time,json=beginAuditTime,proto3" json:"begin_audit_time,omitempty"`
	Index                   uint32 `protobuf:"varint,5,opt,name=index,proto3" json:"index,omitempty"`
	UploadTime              uint32 `protobuf:"varint,6,opt,name=upload_time,json=uploadTime,proto3" json:"upload_time,omitempty"`
}

func (m *GameScreenShot) Reset()                    { *m = GameScreenShot{} }
func (m *GameScreenShot) String() string            { return proto.CompactTextString(m) }
func (*GameScreenShot) ProtoMessage()               {}
func (*GameScreenShot) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{6} }

func (m *GameScreenShot) GetAuditStatus() uint32 {
	if m != nil {
		return m.AuditStatus
	}
	return 0
}

func (m *GameScreenShot) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *GameScreenShot) GetImgUrlInvisibleGamenick() string {
	if m != nil {
		return m.ImgUrlInvisibleGamenick
	}
	return ""
}

func (m *GameScreenShot) GetBeginAuditTime() uint32 {
	if m != nil {
		return m.BeginAuditTime
	}
	return 0
}

func (m *GameScreenShot) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GameScreenShot) GetUploadTime() uint32 {
	if m != nil {
		return m.UploadTime
	}
	return 0
}

type UserGameTagExt struct {
	GameId             uint32            `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	BackImgUrl         string            `protobuf:"bytes,2,opt,name=back_img_url,json=backImgUrl,proto3" json:"back_img_url,omitempty"`
	OptList            []*UserGameTagOpt `protobuf:"bytes,3,rep,name=opt_list,json=optList" json:"opt_list,omitempty"`
	ThumbImgUrl        string            `protobuf:"bytes,4,opt,name=thumb_img_url,json=thumbImgUrl,proto3" json:"thumb_img_url,omitempty"`
	GameNickname       string            `protobuf:"bytes,5,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	GameScreenshot     *GameScreenShot   `protobuf:"bytes,6,opt,name=game_screenshot,json=gameScreenshot" json:"game_screenshot,omitempty"`
	GameScreenshotList []*GameScreenShot `protobuf:"bytes,7,rep,name=game_screenshot_list,json=gameScreenshotList" json:"game_screenshot_list,omitempty"`
}

func (m *UserGameTagExt) Reset()                    { *m = UserGameTagExt{} }
func (m *UserGameTagExt) String() string            { return proto.CompactTextString(m) }
func (*UserGameTagExt) ProtoMessage()               {}
func (*UserGameTagExt) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{7} }

func (m *UserGameTagExt) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserGameTagExt) GetBackImgUrl() string {
	if m != nil {
		return m.BackImgUrl
	}
	return ""
}

func (m *UserGameTagExt) GetOptList() []*UserGameTagOpt {
	if m != nil {
		return m.OptList
	}
	return nil
}

func (m *UserGameTagExt) GetThumbImgUrl() string {
	if m != nil {
		return m.ThumbImgUrl
	}
	return ""
}

func (m *UserGameTagExt) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *UserGameTagExt) GetGameScreenshot() *GameScreenShot {
	if m != nil {
		return m.GameScreenshot
	}
	return nil
}

func (m *UserGameTagExt) GetGameScreenshotList() []*GameScreenShot {
	if m != nil {
		return m.GameScreenshotList
	}
	return nil
}

type LevelImg struct {
	LevelName string `protobuf:"bytes,1,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	ImgUrl    string `protobuf:"bytes,2,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
}

func (m *LevelImg) Reset()                    { *m = LevelImg{} }
func (m *LevelImg) String() string            { return proto.CompactTextString(m) }
func (*LevelImg) ProtoMessage()               {}
func (*LevelImg) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{8} }

func (m *LevelImg) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LevelImg) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

type ConfGameTagOpt struct {
	OptName           string               `protobuf:"bytes,1,opt,name=opt_name,json=optName,proto3" json:"opt_name,omitempty"`
	OptId             uint32               `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	IsSupportMutiSet  bool                 `protobuf:"varint,3,opt,name=is_support_muti_set,json=isSupportMutiSet,proto3" json:"is_support_muti_set,omitempty"`
	ValueConfList     []string             `protobuf:"bytes,4,rep,name=value_conf_list,json=valueConfList" json:"value_conf_list,omitempty"`
	ValueUsersetList  []string             `protobuf:"bytes,5,rep,name=value_userset_list,json=valueUsersetList" json:"value_userset_list,omitempty"`
	SupportMutiSetCnt uint32               `protobuf:"varint,6,opt,name=support_muti_set_cnt,json=supportMutiSetCnt,proto3" json:"support_muti_set_cnt,omitempty"`
	PartitionId       uint32               `protobuf:"varint,7,opt,name=partition_id,json=partitionId,proto3" json:"partition_id,omitempty"`
	PropValueList     []*GameOptSecondProp `protobuf:"bytes,8,rep,name=prop_value_list,json=propValueList" json:"prop_value_list,omitempty"`
	IsMust            bool                 `protobuf:"varint,9,opt,name=is_must,json=isMust,proto3" json:"is_must,omitempty"`
	TextBoxStyle      uint32               `protobuf:"varint,10,opt,name=text_box_style,json=textBoxStyle,proto3" json:"text_box_style,omitempty"`
}

func (m *ConfGameTagOpt) Reset()                    { *m = ConfGameTagOpt{} }
func (m *ConfGameTagOpt) String() string            { return proto.CompactTextString(m) }
func (*ConfGameTagOpt) ProtoMessage()               {}
func (*ConfGameTagOpt) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{9} }

func (m *ConfGameTagOpt) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *ConfGameTagOpt) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *ConfGameTagOpt) GetIsSupportMutiSet() bool {
	if m != nil {
		return m.IsSupportMutiSet
	}
	return false
}

func (m *ConfGameTagOpt) GetValueConfList() []string {
	if m != nil {
		return m.ValueConfList
	}
	return nil
}

func (m *ConfGameTagOpt) GetValueUsersetList() []string {
	if m != nil {
		return m.ValueUsersetList
	}
	return nil
}

func (m *ConfGameTagOpt) GetSupportMutiSetCnt() uint32 {
	if m != nil {
		return m.SupportMutiSetCnt
	}
	return 0
}

func (m *ConfGameTagOpt) GetPartitionId() uint32 {
	if m != nil {
		return m.PartitionId
	}
	return 0
}

func (m *ConfGameTagOpt) GetPropValueList() []*GameOptSecondProp {
	if m != nil {
		return m.PropValueList
	}
	return nil
}

func (m *ConfGameTagOpt) GetIsMust() bool {
	if m != nil {
		return m.IsMust
	}
	return false
}

func (m *ConfGameTagOpt) GetTextBoxStyle() uint32 {
	if m != nil {
		return m.TextBoxStyle
	}
	return 0
}

type ConfGameTagExt struct {
	GameId               uint32            `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	BackImgUrl           string            `protobuf:"bytes,2,opt,name=back_img_url,json=backImgUrl,proto3" json:"back_img_url,omitempty"`
	OptList              []*ConfGameTagOpt `protobuf:"bytes,3,rep,name=opt_list,json=optList" json:"opt_list,omitempty"`
	ThumbImgUrl          string            `protobuf:"bytes,4,opt,name=thumb_img_url,json=thumbImgUrl,proto3" json:"thumb_img_url,omitempty"`
	GameNickname         string            `protobuf:"bytes,5,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	GameScreenshot       *GameScreenShot   `protobuf:"bytes,6,opt,name=game_screenshot,json=gameScreenshot" json:"game_screenshot,omitempty"`
	GameCardImgUrl       string            `protobuf:"bytes,7,opt,name=game_card_img_url,json=gameCardImgUrl,proto3" json:"game_card_img_url,omitempty"`
	GameBackImg          string            `protobuf:"bytes,8,opt,name=game_back_img,json=gameBackImg,proto3" json:"game_back_img,omitempty"`
	GameIconImg          string            `protobuf:"bytes,9,opt,name=game_icon_img,json=gameIconImg,proto3" json:"game_icon_img,omitempty"`
	GameCornerMarkImg    string            `protobuf:"bytes,10,opt,name=game_corner_mark_img,json=gameCornerMarkImg,proto3" json:"game_corner_mark_img,omitempty"`
	GameNoLevelImgUrl    string            `protobuf:"bytes,11,opt,name=game_no_level_img_url,json=gameNoLevelImgUrl,proto3" json:"game_no_level_img_url,omitempty"`
	LevelImgUrl          []*LevelImg       `protobuf:"bytes,12,rep,name=level_img_url,json=levelImgUrl" json:"level_img_url,omitempty"`
	MicLevelImgUrl       []*LevelImg       `protobuf:"bytes,13,rep,name=mic_level_img_url,json=micLevelImgUrl" json:"mic_level_img_url,omitempty"`
	GameBackImgMini      string            `protobuf:"bytes,14,opt,name=game_back_img_mini,json=gameBackImgMini,proto3" json:"game_back_img_mini,omitempty"`
	GameBackColorNum     uint32            `protobuf:"varint,15,opt,name=game_back_color_num,json=gameBackColorNum,proto3" json:"game_back_color_num,omitempty"`
	GameExtraOptSwitch   uint32            `protobuf:"varint,16,opt,name=game_extra_opt_switch,json=gameExtraOptSwitch,proto3" json:"game_extra_opt_switch,omitempty"`
	LevelImgUrlNew       []*LevelImg       `protobuf:"bytes,17,rep,name=level_img_url_new,json=levelImgUrlNew" json:"level_img_url_new,omitempty"`
	GameNoLevelImgUrlNew string            `protobuf:"bytes,18,opt,name=game_no_level_img_url_new,json=gameNoLevelImgUrlNew,proto3" json:"game_no_level_img_url_new,omitempty"`
}

func (m *ConfGameTagExt) Reset()                    { *m = ConfGameTagExt{} }
func (m *ConfGameTagExt) String() string            { return proto.CompactTextString(m) }
func (*ConfGameTagExt) ProtoMessage()               {}
func (*ConfGameTagExt) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{10} }

func (m *ConfGameTagExt) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ConfGameTagExt) GetBackImgUrl() string {
	if m != nil {
		return m.BackImgUrl
	}
	return ""
}

func (m *ConfGameTagExt) GetOptList() []*ConfGameTagOpt {
	if m != nil {
		return m.OptList
	}
	return nil
}

func (m *ConfGameTagExt) GetThumbImgUrl() string {
	if m != nil {
		return m.ThumbImgUrl
	}
	return ""
}

func (m *ConfGameTagExt) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *ConfGameTagExt) GetGameScreenshot() *GameScreenShot {
	if m != nil {
		return m.GameScreenshot
	}
	return nil
}

func (m *ConfGameTagExt) GetGameCardImgUrl() string {
	if m != nil {
		return m.GameCardImgUrl
	}
	return ""
}

func (m *ConfGameTagExt) GetGameBackImg() string {
	if m != nil {
		return m.GameBackImg
	}
	return ""
}

func (m *ConfGameTagExt) GetGameIconImg() string {
	if m != nil {
		return m.GameIconImg
	}
	return ""
}

func (m *ConfGameTagExt) GetGameCornerMarkImg() string {
	if m != nil {
		return m.GameCornerMarkImg
	}
	return ""
}

func (m *ConfGameTagExt) GetGameNoLevelImgUrl() string {
	if m != nil {
		return m.GameNoLevelImgUrl
	}
	return ""
}

func (m *ConfGameTagExt) GetLevelImgUrl() []*LevelImg {
	if m != nil {
		return m.LevelImgUrl
	}
	return nil
}

func (m *ConfGameTagExt) GetMicLevelImgUrl() []*LevelImg {
	if m != nil {
		return m.MicLevelImgUrl
	}
	return nil
}

func (m *ConfGameTagExt) GetGameBackImgMini() string {
	if m != nil {
		return m.GameBackImgMini
	}
	return ""
}

func (m *ConfGameTagExt) GetGameBackColorNum() uint32 {
	if m != nil {
		return m.GameBackColorNum
	}
	return 0
}

func (m *ConfGameTagExt) GetGameExtraOptSwitch() uint32 {
	if m != nil {
		return m.GameExtraOptSwitch
	}
	return 0
}

func (m *ConfGameTagExt) GetLevelImgUrlNew() []*LevelImg {
	if m != nil {
		return m.LevelImgUrlNew
	}
	return nil
}

func (m *ConfGameTagExt) GetGameNoLevelImgUrlNew() string {
	if m != nil {
		return m.GameNoLevelImgUrlNew
	}
	return ""
}

type SimpleGameTagList struct {
	Uid     uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ExtList []*SimpleGameTagExt `protobuf:"bytes,2,rep,name=ext_list,json=extList" json:"ext_list,omitempty"`
}

func (m *SimpleGameTagList) Reset()                    { *m = SimpleGameTagList{} }
func (m *SimpleGameTagList) String() string            { return proto.CompactTextString(m) }
func (*SimpleGameTagList) ProtoMessage()               {}
func (*SimpleGameTagList) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{11} }

func (m *SimpleGameTagList) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SimpleGameTagList) GetExtList() []*SimpleGameTagExt {
	if m != nil {
		return m.ExtList
	}
	return nil
}

type SimpleGameTagExt struct {
	GameId           uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName         string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameNickname     string   `protobuf:"bytes,3,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	GameArea         string   `protobuf:"bytes,4,opt,name=game_area,json=gameArea,proto3" json:"game_area,omitempty"`
	GameDan          string   `protobuf:"bytes,5,opt,name=game_dan,json=gameDan,proto3" json:"game_dan,omitempty"`
	Uid              uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	GameDanUrlForMic string   `protobuf:"bytes,7,opt,name=game_dan_url_for_mic,json=gameDanUrlForMic,proto3" json:"game_dan_url_for_mic,omitempty"`
	GameRole         string   `protobuf:"bytes,8,opt,name=game_role,json=gameRole,proto3" json:"game_role,omitempty"`
	GamePosition     []string `protobuf:"bytes,9,rep,name=game_position,json=gamePosition" json:"game_position,omitempty"`
	GameHeroList     []string `protobuf:"bytes,10,rep,name=game_hero_list,json=gameHeroList" json:"game_hero_list,omitempty"`
	GameScreenshot   string   `protobuf:"bytes,11,opt,name=game_screenshot,json=gameScreenshot,proto3" json:"game_screenshot,omitempty"`
	GameStyle        []string `protobuf:"bytes,12,rep,name=game_style,json=gameStyle" json:"game_style,omitempty"`
	GameLevelUrl     string   `protobuf:"bytes,13,opt,name=game_level_url,json=gameLevelUrl,proto3" json:"game_level_url,omitempty"`
	TagId            uint32   `protobuf:"varint,14,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	IsCompleted      bool     `protobuf:"varint,15,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed,omitempty"`
}

func (m *SimpleGameTagExt) Reset()                    { *m = SimpleGameTagExt{} }
func (m *SimpleGameTagExt) String() string            { return proto.CompactTextString(m) }
func (*SimpleGameTagExt) ProtoMessage()               {}
func (*SimpleGameTagExt) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{12} }

func (m *SimpleGameTagExt) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SimpleGameTagExt) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *SimpleGameTagExt) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *SimpleGameTagExt) GetGameArea() string {
	if m != nil {
		return m.GameArea
	}
	return ""
}

func (m *SimpleGameTagExt) GetGameDan() string {
	if m != nil {
		return m.GameDan
	}
	return ""
}

func (m *SimpleGameTagExt) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SimpleGameTagExt) GetGameDanUrlForMic() string {
	if m != nil {
		return m.GameDanUrlForMic
	}
	return ""
}

func (m *SimpleGameTagExt) GetGameRole() string {
	if m != nil {
		return m.GameRole
	}
	return ""
}

func (m *SimpleGameTagExt) GetGamePosition() []string {
	if m != nil {
		return m.GamePosition
	}
	return nil
}

func (m *SimpleGameTagExt) GetGameHeroList() []string {
	if m != nil {
		return m.GameHeroList
	}
	return nil
}

func (m *SimpleGameTagExt) GetGameScreenshot() string {
	if m != nil {
		return m.GameScreenshot
	}
	return ""
}

func (m *SimpleGameTagExt) GetGameStyle() []string {
	if m != nil {
		return m.GameStyle
	}
	return nil
}

func (m *SimpleGameTagExt) GetGameLevelUrl() string {
	if m != nil {
		return m.GameLevelUrl
	}
	return ""
}

func (m *SimpleGameTagExt) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *SimpleGameTagExt) GetIsCompleted() bool {
	if m != nil {
		return m.IsCompleted
	}
	return false
}

// 个性 类型的标签
type UserOptPersonalTagClassify struct {
	ClassifyId   uint32         `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	ClassifyName string         `protobuf:"bytes,2,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
	TagList      []*UserTagBase `protobuf:"bytes,3,rep,name=tag_list,json=tagList" json:"tag_list,omitempty"`
	IsDel        bool           `protobuf:"varint,4,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
}

func (m *UserOptPersonalTagClassify) Reset()         { *m = UserOptPersonalTagClassify{} }
func (m *UserOptPersonalTagClassify) String() string { return proto.CompactTextString(m) }
func (*UserOptPersonalTagClassify) ProtoMessage()    {}
func (*UserOptPersonalTagClassify) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{13}
}

func (m *UserOptPersonalTagClassify) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *UserOptPersonalTagClassify) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

func (m *UserOptPersonalTagClassify) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *UserOptPersonalTagClassify) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type UserOptPersonalTagExt struct {
	ClassifyId uint32 `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
}

func (m *UserOptPersonalTagExt) Reset()                    { *m = UserOptPersonalTagExt{} }
func (m *UserOptPersonalTagExt) String() string            { return proto.CompactTextString(m) }
func (*UserOptPersonalTagExt) ProtoMessage()               {}
func (*UserOptPersonalTagExt) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{14} }

func (m *UserOptPersonalTagExt) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

// 获取指定用户的全部标签列表
type GetUserTagReq struct {
	TargetUid    uint32 `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	IsNeedTagExt bool   `protobuf:"varint,2,opt,name=is_need_tag_ext,json=isNeedTagExt,proto3" json:"is_need_tag_ext,omitempty"`
}

func (m *GetUserTagReq) Reset()                    { *m = GetUserTagReq{} }
func (m *GetUserTagReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserTagReq) ProtoMessage()               {}
func (*GetUserTagReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{15} }

func (m *GetUserTagReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserTagReq) GetIsNeedTagExt() bool {
	if m != nil {
		return m.IsNeedTagExt
	}
	return false
}

type GetUserTagResp struct {
	UserTaglist *UserTagList `protobuf:"bytes,1,opt,name=user_taglist,json=userTaglist" json:"user_taglist,omitempty"`
}

func (m *GetUserTagResp) Reset()                    { *m = GetUserTagResp{} }
func (m *GetUserTagResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserTagResp) ProtoMessage()               {}
func (*GetUserTagResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{16} }

func (m *GetUserTagResp) GetUserTaglist() *UserTagList {
	if m != nil {
		return m.UserTaglist
	}
	return nil
}

// 设置用户自己的标签列表
type SetUserTagReq struct {
	TargetUid  uint32         `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TagType    uint32         `protobuf:"varint,2,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	TagList    []*UserTagBase `protobuf:"bytes,3,rep,name=tag_list,json=tagList" json:"tag_list,omitempty"`
	SettingCnt uint32         `protobuf:"varint,4,opt,name=setting_cnt,json=settingCnt,proto3" json:"setting_cnt,omitempty"`
	IgnoreGame bool           `protobuf:"varint,5,opt,name=ignore_game,json=ignoreGame,proto3" json:"ignore_game,omitempty"`
}

func (m *SetUserTagReq) Reset()                    { *m = SetUserTagReq{} }
func (m *SetUserTagReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserTagReq) ProtoMessage()               {}
func (*SetUserTagReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{17} }

func (m *SetUserTagReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SetUserTagReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *SetUserTagReq) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *SetUserTagReq) GetSettingCnt() uint32 {
	if m != nil {
		return m.SettingCnt
	}
	return 0
}

func (m *SetUserTagReq) GetIgnoreGame() bool {
	if m != nil {
		return m.IgnoreGame
	}
	return false
}

type SetUserTagResp struct {
}

func (m *SetUserTagResp) Reset()                    { *m = SetUserTagResp{} }
func (m *SetUserTagResp) String() string            { return proto.CompactTextString(m) }
func (*SetUserTagResp) ProtoMessage()               {}
func (*SetUserTagResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{18} }

type UserTagReplaceInfo struct {
	FromTagId   uint32 `protobuf:"varint,1,opt,name=from_tag_id,json=fromTagId,proto3" json:"from_tag_id,omitempty"`
	FromTagType uint32 `protobuf:"varint,2,opt,name=from_tag_type,json=fromTagType,proto3" json:"from_tag_type,omitempty"`
	ToTagId     uint32 `protobuf:"varint,3,opt,name=to_tag_id,json=toTagId,proto3" json:"to_tag_id,omitempty"`
	ToTagType   uint32 `protobuf:"varint,4,opt,name=to_tag_type,json=toTagType,proto3" json:"to_tag_type,omitempty"`
}

func (m *UserTagReplaceInfo) Reset()                    { *m = UserTagReplaceInfo{} }
func (m *UserTagReplaceInfo) String() string            { return proto.CompactTextString(m) }
func (*UserTagReplaceInfo) ProtoMessage()               {}
func (*UserTagReplaceInfo) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{19} }

func (m *UserTagReplaceInfo) GetFromTagId() uint32 {
	if m != nil {
		return m.FromTagId
	}
	return 0
}

func (m *UserTagReplaceInfo) GetFromTagType() uint32 {
	if m != nil {
		return m.FromTagType
	}
	return 0
}

func (m *UserTagReplaceInfo) GetToTagId() uint32 {
	if m != nil {
		return m.ToTagId
	}
	return 0
}

func (m *UserTagReplaceInfo) GetToTagType() uint32 {
	if m != nil {
		return m.ToTagType
	}
	return 0
}

type SetUserOneTagReq struct {
	TargetUid       uint32              `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TagType         uint32              `protobuf:"varint,2,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	OperType        uint32              `protobuf:"varint,4,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`
	TagBase         *UserTagBase        `protobuf:"bytes,5,opt,name=tag_base,json=tagBase" json:"tag_base,omitempty"`
	TagReplace      *UserTagReplaceInfo `protobuf:"bytes,6,opt,name=tag_replace,json=tagReplace" json:"tag_replace,omitempty"`
	TagSortValue    int32               `protobuf:"varint,7,opt,name=tag_sort_value,json=tagSortValue,proto3" json:"tag_sort_value,omitempty"`
	AppId           uint32              `protobuf:"varint,8,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId        uint32              `protobuf:"varint,9,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	JustSetGamenick bool                `protobuf:"varint,10,opt,name=just_set_gamenick,json=justSetGamenick,proto3" json:"just_set_gamenick,omitempty"`
}

func (m *SetUserOneTagReq) Reset()                    { *m = SetUserOneTagReq{} }
func (m *SetUserOneTagReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserOneTagReq) ProtoMessage()               {}
func (*SetUserOneTagReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{20} }

func (m *SetUserOneTagReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SetUserOneTagReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *SetUserOneTagReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *SetUserOneTagReq) GetTagBase() *UserTagBase {
	if m != nil {
		return m.TagBase
	}
	return nil
}

func (m *SetUserOneTagReq) GetTagReplace() *UserTagReplaceInfo {
	if m != nil {
		return m.TagReplace
	}
	return nil
}

func (m *SetUserOneTagReq) GetTagSortValue() int32 {
	if m != nil {
		return m.TagSortValue
	}
	return 0
}

func (m *SetUserOneTagReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SetUserOneTagReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SetUserOneTagReq) GetJustSetGamenick() bool {
	if m != nil {
		return m.JustSetGamenick
	}
	return false
}

type SetUserOneTagResp struct {
}

func (m *SetUserOneTagResp) Reset()                    { *m = SetUserOneTagResp{} }
func (m *SetUserOneTagResp) String() string            { return proto.CompactTextString(m) }
func (*SetUserOneTagResp) ProtoMessage()               {}
func (*SetUserOneTagResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{21} }

// 批量获取用户标签
type BatGetUserTagReq struct {
	UidList      []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	IsNeedTagExt bool     `protobuf:"varint,2,opt,name=is_need_tag_ext,json=isNeedTagExt,proto3" json:"is_need_tag_ext,omitempty"`
}

func (m *BatGetUserTagReq) Reset()                    { *m = BatGetUserTagReq{} }
func (m *BatGetUserTagReq) String() string            { return proto.CompactTextString(m) }
func (*BatGetUserTagReq) ProtoMessage()               {}
func (*BatGetUserTagReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{22} }

func (m *BatGetUserTagReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatGetUserTagReq) GetIsNeedTagExt() bool {
	if m != nil {
		return m.IsNeedTagExt
	}
	return false
}

type BatGetUserTagResp struct {
	UsertaglistList []*UserTagList `protobuf:"bytes,1,rep,name=usertaglist_list,json=usertaglistList" json:"usertaglist_list,omitempty"`
}

func (m *BatGetUserTagResp) Reset()                    { *m = BatGetUserTagResp{} }
func (m *BatGetUserTagResp) String() string            { return proto.CompactTextString(m) }
func (*BatGetUserTagResp) ProtoMessage()               {}
func (*BatGetUserTagResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{23} }

func (m *BatGetUserTagResp) GetUsertaglistList() []*UserTagList {
	if m != nil {
		return m.UsertaglistList
	}
	return nil
}

// 获取配置标签列表
type GetTagConfigListReq struct {
	TagType uint32 `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
}

func (m *GetTagConfigListReq) Reset()                    { *m = GetTagConfigListReq{} }
func (m *GetTagConfigListReq) String() string            { return proto.CompactTextString(m) }
func (*GetTagConfigListReq) ProtoMessage()               {}
func (*GetTagConfigListReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{24} }

func (m *GetTagConfigListReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

type GetTagConfigListResp struct {
	TagList    []*UserTagBase `protobuf:"bytes,1,rep,name=tag_list,json=tagList" json:"tag_list,omitempty"`
	GameIdList []uint32       `protobuf:"varint,2,rep,packed,name=game_id_list,json=gameIdList" json:"game_id_list,omitempty"`
}

func (m *GetTagConfigListResp) Reset()                    { *m = GetTagConfigListResp{} }
func (m *GetTagConfigListResp) String() string            { return proto.CompactTextString(m) }
func (*GetTagConfigListResp) ProtoMessage()               {}
func (*GetTagConfigListResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{25} }

func (m *GetTagConfigListResp) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *GetTagConfigListResp) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

// 创建普通标签配置
type CreateTagConfigReq struct {
	TagType uint32       `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	Tag     *UserTagBase `protobuf:"bytes,2,opt,name=tag" json:"tag,omitempty"`
}

func (m *CreateTagConfigReq) Reset()                    { *m = CreateTagConfigReq{} }
func (m *CreateTagConfigReq) String() string            { return proto.CompactTextString(m) }
func (*CreateTagConfigReq) ProtoMessage()               {}
func (*CreateTagConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{26} }

func (m *CreateTagConfigReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *CreateTagConfigReq) GetTag() *UserTagBase {
	if m != nil {
		return m.Tag
	}
	return nil
}

type CreateTagConfigResp struct {
	TagId uint32 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
}

func (m *CreateTagConfigResp) Reset()                    { *m = CreateTagConfigResp{} }
func (m *CreateTagConfigResp) String() string            { return proto.CompactTextString(m) }
func (*CreateTagConfigResp) ProtoMessage()               {}
func (*CreateTagConfigResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{27} }

func (m *CreateTagConfigResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

// 修改
type ModifyTagConfigReq struct {
	TagId uint32       `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Tag   *UserTagBase `protobuf:"bytes,2,opt,name=tag" json:"tag,omitempty"`
}

func (m *ModifyTagConfigReq) Reset()                    { *m = ModifyTagConfigReq{} }
func (m *ModifyTagConfigReq) String() string            { return proto.CompactTextString(m) }
func (*ModifyTagConfigReq) ProtoMessage()               {}
func (*ModifyTagConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{28} }

func (m *ModifyTagConfigReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ModifyTagConfigReq) GetTag() *UserTagBase {
	if m != nil {
		return m.Tag
	}
	return nil
}

type ModifyTagConfigResp struct {
}

func (m *ModifyTagConfigResp) Reset()                    { *m = ModifyTagConfigResp{} }
func (m *ModifyTagConfigResp) String() string            { return proto.CompactTextString(m) }
func (*ModifyTagConfigResp) ProtoMessage()               {}
func (*ModifyTagConfigResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{29} }

// 删除
type DelTagConfigReq struct {
	TagId     uint32 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	IsRecover bool   `protobuf:"varint,2,opt,name=is_recover,json=isRecover,proto3" json:"is_recover,omitempty"`
}

func (m *DelTagConfigReq) Reset()                    { *m = DelTagConfigReq{} }
func (m *DelTagConfigReq) String() string            { return proto.CompactTextString(m) }
func (*DelTagConfigReq) ProtoMessage()               {}
func (*DelTagConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{30} }

func (m *DelTagConfigReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *DelTagConfigReq) GetIsRecover() bool {
	if m != nil {
		return m.IsRecover
	}
	return false
}

type DelTagConfigResp struct {
}

func (m *DelTagConfigResp) Reset()                    { *m = DelTagConfigResp{} }
func (m *DelTagConfigResp) String() string            { return proto.CompactTextString(m) }
func (*DelTagConfigResp) ProtoMessage()               {}
func (*DelTagConfigResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{31} }

// 排序
type SortTagConfigReq struct {
	TagType                uint32   `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	TagIdList              []uint32 `protobuf:"varint,2,rep,packed,name=tag_id_list,json=tagIdList" json:"tag_id_list,omitempty"`
	PersonaltypeClassifyId uint32   `protobuf:"varint,3,opt,name=personaltype_classify_id,json=personaltypeClassifyId,proto3" json:"personaltype_classify_id,omitempty"`
}

func (m *SortTagConfigReq) Reset()                    { *m = SortTagConfigReq{} }
func (m *SortTagConfigReq) String() string            { return proto.CompactTextString(m) }
func (*SortTagConfigReq) ProtoMessage()               {}
func (*SortTagConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{32} }

func (m *SortTagConfigReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *SortTagConfigReq) GetTagIdList() []uint32 {
	if m != nil {
		return m.TagIdList
	}
	return nil
}

func (m *SortTagConfigReq) GetPersonaltypeClassifyId() uint32 {
	if m != nil {
		return m.PersonaltypeClassifyId
	}
	return 0
}

type SortTagConfigResp struct {
}

func (m *SortTagConfigResp) Reset()                    { *m = SortTagConfigResp{} }
func (m *SortTagConfigResp) String() string            { return proto.CompactTextString(m) }
func (*SortTagConfigResp) ProtoMessage()               {}
func (*SortTagConfigResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{33} }

// 创建findwho类型的标签
type CreateFindWhoTypeTagConfigReq struct {
	FindwhoType        uint32 `protobuf:"varint,1,opt,name=findwho_type,json=findwhoType,proto3" json:"findwho_type,omitempty"`
	TagName            string `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	AssignSex          uint32 `protobuf:"varint,3,opt,name=assign_sex,json=assignSex,proto3" json:"assign_sex,omitempty"`
	CorrelationTagName string `protobuf:"bytes,4,opt,name=correlation_tag_name,json=correlationTagName,proto3" json:"correlation_tag_name,omitempty"`
}

func (m *CreateFindWhoTypeTagConfigReq) Reset()         { *m = CreateFindWhoTypeTagConfigReq{} }
func (m *CreateFindWhoTypeTagConfigReq) String() string { return proto.CompactTextString(m) }
func (*CreateFindWhoTypeTagConfigReq) ProtoMessage()    {}
func (*CreateFindWhoTypeTagConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{34}
}

func (m *CreateFindWhoTypeTagConfigReq) GetFindwhoType() uint32 {
	if m != nil {
		return m.FindwhoType
	}
	return 0
}

func (m *CreateFindWhoTypeTagConfigReq) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *CreateFindWhoTypeTagConfigReq) GetAssignSex() uint32 {
	if m != nil {
		return m.AssignSex
	}
	return 0
}

func (m *CreateFindWhoTypeTagConfigReq) GetCorrelationTagName() string {
	if m != nil {
		return m.CorrelationTagName
	}
	return ""
}

type CreateFindWhoTypeTagConfigResp struct {
	FindwhoType      uint32 `protobuf:"varint,1,opt,name=findwho_type,json=findwhoType,proto3" json:"findwho_type,omitempty"`
	TagId            uint32 `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	CorrelationTagId uint32 `protobuf:"varint,3,opt,name=correlation_tag_id,json=correlationTagId,proto3" json:"correlation_tag_id,omitempty"`
}

func (m *CreateFindWhoTypeTagConfigResp) Reset()         { *m = CreateFindWhoTypeTagConfigResp{} }
func (m *CreateFindWhoTypeTagConfigResp) String() string { return proto.CompactTextString(m) }
func (*CreateFindWhoTypeTagConfigResp) ProtoMessage()    {}
func (*CreateFindWhoTypeTagConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{35}
}

func (m *CreateFindWhoTypeTagConfigResp) GetFindwhoType() uint32 {
	if m != nil {
		return m.FindwhoType
	}
	return 0
}

func (m *CreateFindWhoTypeTagConfigResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *CreateFindWhoTypeTagConfigResp) GetCorrelationTagId() uint32 {
	if m != nil {
		return m.CorrelationTagId
	}
	return 0
}

// 创建个性标签的分类
type CreateOptPersonalTagClassifyReq struct {
	ClassifyName string `protobuf:"bytes,1,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
}

func (m *CreateOptPersonalTagClassifyReq) Reset()         { *m = CreateOptPersonalTagClassifyReq{} }
func (m *CreateOptPersonalTagClassifyReq) String() string { return proto.CompactTextString(m) }
func (*CreateOptPersonalTagClassifyReq) ProtoMessage()    {}
func (*CreateOptPersonalTagClassifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{36}
}

func (m *CreateOptPersonalTagClassifyReq) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

type CreateOptPersonalTagClassifyResp struct {
	ClassifyId uint32 `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
}

func (m *CreateOptPersonalTagClassifyResp) Reset()         { *m = CreateOptPersonalTagClassifyResp{} }
func (m *CreateOptPersonalTagClassifyResp) String() string { return proto.CompactTextString(m) }
func (*CreateOptPersonalTagClassifyResp) ProtoMessage()    {}
func (*CreateOptPersonalTagClassifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{37}
}

func (m *CreateOptPersonalTagClassifyResp) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

// 获取全量分类列表
type GetOptPersonalTagClassifyListReq struct {
}

func (m *GetOptPersonalTagClassifyListReq) Reset()         { *m = GetOptPersonalTagClassifyListReq{} }
func (m *GetOptPersonalTagClassifyListReq) String() string { return proto.CompactTextString(m) }
func (*GetOptPersonalTagClassifyListReq) ProtoMessage()    {}
func (*GetOptPersonalTagClassifyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{38}
}

type GetOptPersonalTagClassifyListResp struct {
	ClassifyList []*UserOptPersonalTagClassify `protobuf:"bytes,1,rep,name=classify_list,json=classifyList" json:"classify_list,omitempty"`
}

func (m *GetOptPersonalTagClassifyListResp) Reset()         { *m = GetOptPersonalTagClassifyListResp{} }
func (m *GetOptPersonalTagClassifyListResp) String() string { return proto.CompactTextString(m) }
func (*GetOptPersonalTagClassifyListResp) ProtoMessage()    {}
func (*GetOptPersonalTagClassifyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{39}
}

func (m *GetOptPersonalTagClassifyListResp) GetClassifyList() []*UserOptPersonalTagClassify {
	if m != nil {
		return m.ClassifyList
	}
	return nil
}

// 根据分类获取个性标签列表
type GetOptPersonalTagByClassifyReq struct {
	ClassifyId uint32 `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
}

func (m *GetOptPersonalTagByClassifyReq) Reset()         { *m = GetOptPersonalTagByClassifyReq{} }
func (m *GetOptPersonalTagByClassifyReq) String() string { return proto.CompactTextString(m) }
func (*GetOptPersonalTagByClassifyReq) ProtoMessage()    {}
func (*GetOptPersonalTagByClassifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{40}
}

func (m *GetOptPersonalTagByClassifyReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

type GetOptPersonalTagByClassifyResp struct {
	TagList []*UserTagBase `protobuf:"bytes,1,rep,name=tag_list,json=tagList" json:"tag_list,omitempty"`
}

func (m *GetOptPersonalTagByClassifyResp) Reset()         { *m = GetOptPersonalTagByClassifyResp{} }
func (m *GetOptPersonalTagByClassifyResp) String() string { return proto.CompactTextString(m) }
func (*GetOptPersonalTagByClassifyResp) ProtoMessage()    {}
func (*GetOptPersonalTagByClassifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{41}
}

func (m *GetOptPersonalTagByClassifyResp) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

// 修改个性标签的分类的名字
type ModifyOptPersonalTagClassifyNameReq struct {
	ClassifyId   uint32 `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	ClassifyName string `protobuf:"bytes,2,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
}

func (m *ModifyOptPersonalTagClassifyNameReq) Reset()         { *m = ModifyOptPersonalTagClassifyNameReq{} }
func (m *ModifyOptPersonalTagClassifyNameReq) String() string { return proto.CompactTextString(m) }
func (*ModifyOptPersonalTagClassifyNameReq) ProtoMessage()    {}
func (*ModifyOptPersonalTagClassifyNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{42}
}

func (m *ModifyOptPersonalTagClassifyNameReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *ModifyOptPersonalTagClassifyNameReq) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

type ModifyOptPersonalTagClassifyNameResp struct {
}

func (m *ModifyOptPersonalTagClassifyNameResp) Reset()         { *m = ModifyOptPersonalTagClassifyNameResp{} }
func (m *ModifyOptPersonalTagClassifyNameResp) String() string { return proto.CompactTextString(m) }
func (*ModifyOptPersonalTagClassifyNameResp) ProtoMessage()    {}
func (*ModifyOptPersonalTagClassifyNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{43}
}

// 排序 个性标签的分类
type SortOptPersonalTagClassifyReq struct {
	ClassifyIdList []uint32 `protobuf:"varint,1,rep,packed,name=classify_id_list,json=classifyIdList" json:"classify_id_list,omitempty"`
}

func (m *SortOptPersonalTagClassifyReq) Reset()         { *m = SortOptPersonalTagClassifyReq{} }
func (m *SortOptPersonalTagClassifyReq) String() string { return proto.CompactTextString(m) }
func (*SortOptPersonalTagClassifyReq) ProtoMessage()    {}
func (*SortOptPersonalTagClassifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{44}
}

func (m *SortOptPersonalTagClassifyReq) GetClassifyIdList() []uint32 {
	if m != nil {
		return m.ClassifyIdList
	}
	return nil
}

type SortOptPersonalTagClassifyResp struct {
}

func (m *SortOptPersonalTagClassifyResp) Reset()         { *m = SortOptPersonalTagClassifyResp{} }
func (m *SortOptPersonalTagClassifyResp) String() string { return proto.CompactTextString(m) }
func (*SortOptPersonalTagClassifyResp) ProtoMessage()    {}
func (*SortOptPersonalTagClassifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{45}
}

// 删除个性标签的分类
type DelOptPersonalTagClassifyReq struct {
	ClassifyId uint32 `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	IsRecover  bool   `protobuf:"varint,2,opt,name=is_recover,json=isRecover,proto3" json:"is_recover,omitempty"`
}

func (m *DelOptPersonalTagClassifyReq) Reset()         { *m = DelOptPersonalTagClassifyReq{} }
func (m *DelOptPersonalTagClassifyReq) String() string { return proto.CompactTextString(m) }
func (*DelOptPersonalTagClassifyReq) ProtoMessage()    {}
func (*DelOptPersonalTagClassifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{46}
}

func (m *DelOptPersonalTagClassifyReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *DelOptPersonalTagClassifyReq) GetIsRecover() bool {
	if m != nil {
		return m.IsRecover
	}
	return false
}

type DelOptPersonalTagClassifyResp struct {
}

func (m *DelOptPersonalTagClassifyResp) Reset()         { *m = DelOptPersonalTagClassifyResp{} }
func (m *DelOptPersonalTagClassifyResp) String() string { return proto.CompactTextString(m) }
func (*DelOptPersonalTagClassifyResp) ProtoMessage()    {}
func (*DelOptPersonalTagClassifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{47}
}

type GetUserGameTagReq struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameName string `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
}

func (m *GetUserGameTagReq) Reset()                    { *m = GetUserGameTagReq{} }
func (m *GetUserGameTagReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserGameTagReq) ProtoMessage()               {}
func (*GetUserGameTagReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{48} }

func (m *GetUserGameTagReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserGameTagReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type GetUserGameTagResp struct {
	GameExt *UserGameTagExt `protobuf:"bytes,1,opt,name=game_ext,json=gameExt" json:"game_ext,omitempty"`
}

func (m *GetUserGameTagResp) Reset()                    { *m = GetUserGameTagResp{} }
func (m *GetUserGameTagResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserGameTagResp) ProtoMessage()               {}
func (*GetUserGameTagResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{49} }

func (m *GetUserGameTagResp) GetGameExt() *UserGameTagExt {
	if m != nil {
		return m.GameExt
	}
	return nil
}

// 获取用户游戏段位
type GetUserGameTagDanReq struct {
	UidList  []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	GameName string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
}

func (m *GetUserGameTagDanReq) Reset()                    { *m = GetUserGameTagDanReq{} }
func (m *GetUserGameTagDanReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserGameTagDanReq) ProtoMessage()               {}
func (*GetUserGameTagDanReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{50} }

func (m *GetUserGameTagDanReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetUserGameTagDanReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type GameTagDan struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameName string `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameDan  string `protobuf:"bytes,3,opt,name=game_dan,json=gameDan,proto3" json:"game_dan,omitempty"`
}

func (m *GameTagDan) Reset()                    { *m = GameTagDan{} }
func (m *GameTagDan) String() string            { return proto.CompactTextString(m) }
func (*GameTagDan) ProtoMessage()               {}
func (*GameTagDan) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{51} }

func (m *GameTagDan) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameTagDan) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameTagDan) GetGameDan() string {
	if m != nil {
		return m.GameDan
	}
	return ""
}

type GetUserGameTagDanResp struct {
	GameDanList []*GameTagDan `protobuf:"bytes,1,rep,name=game_dan_list,json=gameDanList" json:"game_dan_list,omitempty"`
}

func (m *GetUserGameTagDanResp) Reset()                    { *m = GetUserGameTagDanResp{} }
func (m *GetUserGameTagDanResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserGameTagDanResp) ProtoMessage()               {}
func (*GetUserGameTagDanResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{52} }

func (m *GetUserGameTagDanResp) GetGameDanList() []*GameTagDan {
	if m != nil {
		return m.GameDanList
	}
	return nil
}

type GetSimpleGameTagReq struct {
	UidList  []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	GameName string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
}

func (m *GetSimpleGameTagReq) Reset()                    { *m = GetSimpleGameTagReq{} }
func (m *GetSimpleGameTagReq) String() string            { return proto.CompactTextString(m) }
func (*GetSimpleGameTagReq) ProtoMessage()               {}
func (*GetSimpleGameTagReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{53} }

func (m *GetSimpleGameTagReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetSimpleGameTagReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type GetSimpleGameTagResp struct {
	GameExtList []*SimpleGameTagExt `protobuf:"bytes,1,rep,name=game_ext_list,json=gameExtList" json:"game_ext_list,omitempty"`
}

func (m *GetSimpleGameTagResp) Reset()                    { *m = GetSimpleGameTagResp{} }
func (m *GetSimpleGameTagResp) String() string            { return proto.CompactTextString(m) }
func (*GetSimpleGameTagResp) ProtoMessage()               {}
func (*GetSimpleGameTagResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{54} }

func (m *GetSimpleGameTagResp) GetGameExtList() []*SimpleGameTagExt {
	if m != nil {
		return m.GameExtList
	}
	return nil
}

type BatGetSimpleGameTagReq struct {
	UidList  []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	GameName []string `protobuf:"bytes,2,rep,name=game_name,json=gameName" json:"game_name,omitempty"`
}

func (m *BatGetSimpleGameTagReq) Reset()                    { *m = BatGetSimpleGameTagReq{} }
func (m *BatGetSimpleGameTagReq) String() string            { return proto.CompactTextString(m) }
func (*BatGetSimpleGameTagReq) ProtoMessage()               {}
func (*BatGetSimpleGameTagReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{55} }

func (m *BatGetSimpleGameTagReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatGetSimpleGameTagReq) GetGameName() []string {
	if m != nil {
		return m.GameName
	}
	return nil
}

type BatGetSimpleGameTagResp struct {
	GameExtList []*SimpleGameTagList `protobuf:"bytes,1,rep,name=game_ext_list,json=gameExtList" json:"game_ext_list,omitempty"`
}

func (m *BatGetSimpleGameTagResp) Reset()                    { *m = BatGetSimpleGameTagResp{} }
func (m *BatGetSimpleGameTagResp) String() string            { return proto.CompactTextString(m) }
func (*BatGetSimpleGameTagResp) ProtoMessage()               {}
func (*BatGetSimpleGameTagResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{56} }

func (m *BatGetSimpleGameTagResp) GetGameExtList() []*SimpleGameTagList {
	if m != nil {
		return m.GameExtList
	}
	return nil
}

type NotifyChannelPlayChangeReq struct {
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PlayType  uint32 `protobuf:"varint,2,opt,name=play_type,json=playType,proto3" json:"play_type,omitempty"`
	PlayName  string `protobuf:"bytes,3,opt,name=play_name,json=playName,proto3" json:"play_name,omitempty"`
}

func (m *NotifyChannelPlayChangeReq) Reset()         { *m = NotifyChannelPlayChangeReq{} }
func (m *NotifyChannelPlayChangeReq) String() string { return proto.CompactTextString(m) }
func (*NotifyChannelPlayChangeReq) ProtoMessage()    {}
func (*NotifyChannelPlayChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{57}
}

func (m *NotifyChannelPlayChangeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NotifyChannelPlayChangeReq) GetPlayType() uint32 {
	if m != nil {
		return m.PlayType
	}
	return 0
}

func (m *NotifyChannelPlayChangeReq) GetPlayName() string {
	if m != nil {
		return m.PlayName
	}
	return ""
}

type NotifyChannelPlayChangeResp struct {
}

func (m *NotifyChannelPlayChangeResp) Reset()         { *m = NotifyChannelPlayChangeResp{} }
func (m *NotifyChannelPlayChangeResp) String() string { return proto.CompactTextString(m) }
func (*NotifyChannelPlayChangeResp) ProtoMessage()    {}
func (*NotifyChannelPlayChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{58}
}

type SetUserGameNickReq struct {
	TargetUid uint32       `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TagType   uint32       `protobuf:"varint,2,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	OperType  uint32       `protobuf:"varint,3,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`
	TagBase   *UserTagBase `protobuf:"bytes,4,opt,name=tag_base,json=tagBase" json:"tag_base,omitempty"`
	AppId     uint32       `protobuf:"varint,5,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId  uint32       `protobuf:"varint,6,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
}

func (m *SetUserGameNickReq) Reset()                    { *m = SetUserGameNickReq{} }
func (m *SetUserGameNickReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserGameNickReq) ProtoMessage()               {}
func (*SetUserGameNickReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{59} }

func (m *SetUserGameNickReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SetUserGameNickReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *SetUserGameNickReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *SetUserGameNickReq) GetTagBase() *UserTagBase {
	if m != nil {
		return m.TagBase
	}
	return nil
}

func (m *SetUserGameNickReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SetUserGameNickReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type SetUserGameNickResp struct {
}

func (m *SetUserGameNickResp) Reset()                    { *m = SetUserGameNickResp{} }
func (m *SetUserGameNickResp) String() string            { return proto.CompactTextString(m) }
func (*SetUserGameNickResp) ProtoMessage()               {}
func (*SetUserGameNickResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{60} }

type SetUserGameScreenShotReq struct {
	TargetUid      uint32          `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TagType        uint32          `protobuf:"varint,2,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	OperType       uint32          `protobuf:"varint,3,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`
	TagId          uint32          `protobuf:"varint,4,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	GameScreenshot *GameScreenShot `protobuf:"bytes,5,opt,name=game_screenshot,json=gameScreenshot" json:"game_screenshot,omitempty"`
	AppId          uint32          `protobuf:"varint,6,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId       uint32          `protobuf:"varint,7,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
}

func (m *SetUserGameScreenShotReq) Reset()                    { *m = SetUserGameScreenShotReq{} }
func (m *SetUserGameScreenShotReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserGameScreenShotReq) ProtoMessage()               {}
func (*SetUserGameScreenShotReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{61} }

func (m *SetUserGameScreenShotReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SetUserGameScreenShotReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *SetUserGameScreenShotReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *SetUserGameScreenShotReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *SetUserGameScreenShotReq) GetGameScreenshot() *GameScreenShot {
	if m != nil {
		return m.GameScreenshot
	}
	return nil
}

func (m *SetUserGameScreenShotReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SetUserGameScreenShotReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type SetUserGameScreenShotResp struct {
}

func (m *SetUserGameScreenShotResp) Reset()         { *m = SetUserGameScreenShotResp{} }
func (m *SetUserGameScreenShotResp) String() string { return proto.CompactTextString(m) }
func (*SetUserGameScreenShotResp) ProtoMessage()    {}
func (*SetUserGameScreenShotResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{62}
}

// 改变推荐状态（是否向别人推荐自己）
type ChangeRecommendStatusReq struct {
	Uid    uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status uint32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (m *ChangeRecommendStatusReq) Reset()                    { *m = ChangeRecommendStatusReq{} }
func (m *ChangeRecommendStatusReq) String() string            { return proto.CompactTextString(m) }
func (*ChangeRecommendStatusReq) ProtoMessage()               {}
func (*ChangeRecommendStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{63} }

func (m *ChangeRecommendStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeRecommendStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type ChangeRecommendStatusResp struct {
}

func (m *ChangeRecommendStatusResp) Reset()         { *m = ChangeRecommendStatusResp{} }
func (m *ChangeRecommendStatusResp) String() string { return proto.CompactTextString(m) }
func (*ChangeRecommendStatusResp) ProtoMessage()    {}
func (*ChangeRecommendStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsertag, []int{64}
}

// 获取推荐状态
type GetRecommendStatusReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetRecommendStatusReq) Reset()                    { *m = GetRecommendStatusReq{} }
func (m *GetRecommendStatusReq) String() string            { return proto.CompactTextString(m) }
func (*GetRecommendStatusReq) ProtoMessage()               {}
func (*GetRecommendStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{65} }

func (m *GetRecommendStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRecommendStatusResp struct {
	Status uint32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (m *GetRecommendStatusResp) Reset()                    { *m = GetRecommendStatusResp{} }
func (m *GetRecommendStatusResp) String() string            { return proto.CompactTextString(m) }
func (*GetRecommendStatusResp) ProtoMessage()               {}
func (*GetRecommendStatusResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{66} }

func (m *GetRecommendStatusResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type SetMosaicImageContext struct {
	GameScreenshotReq *SetUserGameScreenShotReq `protobuf:"bytes,1,opt,name=game_screenshot_req,json=gameScreenshotReq" json:"game_screenshot_req,omitempty"`
	GameName          string                    `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
}

func (m *SetMosaicImageContext) Reset()                    { *m = SetMosaicImageContext{} }
func (m *SetMosaicImageContext) String() string            { return proto.CompactTextString(m) }
func (*SetMosaicImageContext) ProtoMessage()               {}
func (*SetMosaicImageContext) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{67} }

func (m *SetMosaicImageContext) GetGameScreenshotReq() *SetUserGameScreenShotReq {
	if m != nil {
		return m.GameScreenshotReq
	}
	return nil
}

func (m *SetMosaicImageContext) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type SetMosaicImageReq struct {
	Result         bool   `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MosaicImageUrl string `protobuf:"bytes,2,opt,name=mosaic_image_url,json=mosaicImageUrl,proto3" json:"mosaic_image_url,omitempty"`
	Context        []byte `protobuf:"bytes,3,opt,name=context,proto3" json:"context,omitempty"`
	Source         bool   `protobuf:"varint,4,opt,name=source,proto3" json:"source,omitempty"`
}

func (m *SetMosaicImageReq) Reset()                    { *m = SetMosaicImageReq{} }
func (m *SetMosaicImageReq) String() string            { return proto.CompactTextString(m) }
func (*SetMosaicImageReq) ProtoMessage()               {}
func (*SetMosaicImageReq) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{68} }

func (m *SetMosaicImageReq) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

func (m *SetMosaicImageReq) GetMosaicImageUrl() string {
	if m != nil {
		return m.MosaicImageUrl
	}
	return ""
}

func (m *SetMosaicImageReq) GetContext() []byte {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *SetMosaicImageReq) GetSource() bool {
	if m != nil {
		return m.Source
	}
	return false
}

type SetMosaicImageResp struct {
}

func (m *SetMosaicImageResp) Reset()                    { *m = SetMosaicImageResp{} }
func (m *SetMosaicImageResp) String() string            { return proto.CompactTextString(m) }
func (*SetMosaicImageResp) ProtoMessage()               {}
func (*SetMosaicImageResp) Descriptor() ([]byte, []int) { return fileDescriptorUsertag, []int{69} }

func init() {
	proto.RegisterType((*UserTagBase)(nil), "usertag.UserTagBase")
	proto.RegisterType((*UserTagList)(nil), "usertag.UserTagList")
	proto.RegisterType((*UserAgeTagExt)(nil), "usertag.UserAgeTagExt")
	proto.RegisterType((*UserFindWhoTagExt)(nil), "usertag.UserFindWhoTagExt")
	proto.RegisterType((*GameOptSecondProp)(nil), "usertag.GameOptSecondProp")
	proto.RegisterType((*UserGameTagOpt)(nil), "usertag.UserGameTagOpt")
	proto.RegisterType((*GameScreenShot)(nil), "usertag.GameScreenShot")
	proto.RegisterType((*UserGameTagExt)(nil), "usertag.UserGameTagExt")
	proto.RegisterType((*LevelImg)(nil), "usertag.LevelImg")
	proto.RegisterType((*ConfGameTagOpt)(nil), "usertag.ConfGameTagOpt")
	proto.RegisterType((*ConfGameTagExt)(nil), "usertag.ConfGameTagExt")
	proto.RegisterType((*SimpleGameTagList)(nil), "usertag.SimpleGameTagList")
	proto.RegisterType((*SimpleGameTagExt)(nil), "usertag.SimpleGameTagExt")
	proto.RegisterType((*UserOptPersonalTagClassify)(nil), "usertag.UserOptPersonalTagClassify")
	proto.RegisterType((*UserOptPersonalTagExt)(nil), "usertag.UserOptPersonalTagExt")
	proto.RegisterType((*GetUserTagReq)(nil), "usertag.GetUserTagReq")
	proto.RegisterType((*GetUserTagResp)(nil), "usertag.GetUserTagResp")
	proto.RegisterType((*SetUserTagReq)(nil), "usertag.SetUserTagReq")
	proto.RegisterType((*SetUserTagResp)(nil), "usertag.SetUserTagResp")
	proto.RegisterType((*UserTagReplaceInfo)(nil), "usertag.UserTagReplaceInfo")
	proto.RegisterType((*SetUserOneTagReq)(nil), "usertag.SetUserOneTagReq")
	proto.RegisterType((*SetUserOneTagResp)(nil), "usertag.SetUserOneTagResp")
	proto.RegisterType((*BatGetUserTagReq)(nil), "usertag.BatGetUserTagReq")
	proto.RegisterType((*BatGetUserTagResp)(nil), "usertag.BatGetUserTagResp")
	proto.RegisterType((*GetTagConfigListReq)(nil), "usertag.GetTagConfigListReq")
	proto.RegisterType((*GetTagConfigListResp)(nil), "usertag.GetTagConfigListResp")
	proto.RegisterType((*CreateTagConfigReq)(nil), "usertag.CreateTagConfigReq")
	proto.RegisterType((*CreateTagConfigResp)(nil), "usertag.CreateTagConfigResp")
	proto.RegisterType((*ModifyTagConfigReq)(nil), "usertag.ModifyTagConfigReq")
	proto.RegisterType((*ModifyTagConfigResp)(nil), "usertag.ModifyTagConfigResp")
	proto.RegisterType((*DelTagConfigReq)(nil), "usertag.DelTagConfigReq")
	proto.RegisterType((*DelTagConfigResp)(nil), "usertag.DelTagConfigResp")
	proto.RegisterType((*SortTagConfigReq)(nil), "usertag.SortTagConfigReq")
	proto.RegisterType((*SortTagConfigResp)(nil), "usertag.SortTagConfigResp")
	proto.RegisterType((*CreateFindWhoTypeTagConfigReq)(nil), "usertag.CreateFindWhoTypeTagConfigReq")
	proto.RegisterType((*CreateFindWhoTypeTagConfigResp)(nil), "usertag.CreateFindWhoTypeTagConfigResp")
	proto.RegisterType((*CreateOptPersonalTagClassifyReq)(nil), "usertag.CreateOptPersonalTagClassifyReq")
	proto.RegisterType((*CreateOptPersonalTagClassifyResp)(nil), "usertag.CreateOptPersonalTagClassifyResp")
	proto.RegisterType((*GetOptPersonalTagClassifyListReq)(nil), "usertag.GetOptPersonalTagClassifyListReq")
	proto.RegisterType((*GetOptPersonalTagClassifyListResp)(nil), "usertag.GetOptPersonalTagClassifyListResp")
	proto.RegisterType((*GetOptPersonalTagByClassifyReq)(nil), "usertag.GetOptPersonalTagByClassifyReq")
	proto.RegisterType((*GetOptPersonalTagByClassifyResp)(nil), "usertag.GetOptPersonalTagByClassifyResp")
	proto.RegisterType((*ModifyOptPersonalTagClassifyNameReq)(nil), "usertag.ModifyOptPersonalTagClassifyNameReq")
	proto.RegisterType((*ModifyOptPersonalTagClassifyNameResp)(nil), "usertag.ModifyOptPersonalTagClassifyNameResp")
	proto.RegisterType((*SortOptPersonalTagClassifyReq)(nil), "usertag.SortOptPersonalTagClassifyReq")
	proto.RegisterType((*SortOptPersonalTagClassifyResp)(nil), "usertag.SortOptPersonalTagClassifyResp")
	proto.RegisterType((*DelOptPersonalTagClassifyReq)(nil), "usertag.DelOptPersonalTagClassifyReq")
	proto.RegisterType((*DelOptPersonalTagClassifyResp)(nil), "usertag.DelOptPersonalTagClassifyResp")
	proto.RegisterType((*GetUserGameTagReq)(nil), "usertag.GetUserGameTagReq")
	proto.RegisterType((*GetUserGameTagResp)(nil), "usertag.GetUserGameTagResp")
	proto.RegisterType((*GetUserGameTagDanReq)(nil), "usertag.GetUserGameTagDanReq")
	proto.RegisterType((*GameTagDan)(nil), "usertag.GameTagDan")
	proto.RegisterType((*GetUserGameTagDanResp)(nil), "usertag.GetUserGameTagDanResp")
	proto.RegisterType((*GetSimpleGameTagReq)(nil), "usertag.GetSimpleGameTagReq")
	proto.RegisterType((*GetSimpleGameTagResp)(nil), "usertag.GetSimpleGameTagResp")
	proto.RegisterType((*BatGetSimpleGameTagReq)(nil), "usertag.BatGetSimpleGameTagReq")
	proto.RegisterType((*BatGetSimpleGameTagResp)(nil), "usertag.BatGetSimpleGameTagResp")
	proto.RegisterType((*NotifyChannelPlayChangeReq)(nil), "usertag.NotifyChannelPlayChangeReq")
	proto.RegisterType((*NotifyChannelPlayChangeResp)(nil), "usertag.NotifyChannelPlayChangeResp")
	proto.RegisterType((*SetUserGameNickReq)(nil), "usertag.SetUserGameNickReq")
	proto.RegisterType((*SetUserGameNickResp)(nil), "usertag.SetUserGameNickResp")
	proto.RegisterType((*SetUserGameScreenShotReq)(nil), "usertag.SetUserGameScreenShotReq")
	proto.RegisterType((*SetUserGameScreenShotResp)(nil), "usertag.SetUserGameScreenShotResp")
	proto.RegisterType((*ChangeRecommendStatusReq)(nil), "usertag.ChangeRecommendStatusReq")
	proto.RegisterType((*ChangeRecommendStatusResp)(nil), "usertag.ChangeRecommendStatusResp")
	proto.RegisterType((*GetRecommendStatusReq)(nil), "usertag.GetRecommendStatusReq")
	proto.RegisterType((*GetRecommendStatusResp)(nil), "usertag.GetRecommendStatusResp")
	proto.RegisterType((*SetMosaicImageContext)(nil), "usertag.SetMosaicImageContext")
	proto.RegisterType((*SetMosaicImageReq)(nil), "usertag.SetMosaicImageReq")
	proto.RegisterType((*SetMosaicImageResp)(nil), "usertag.SetMosaicImageResp")
	proto.RegisterEnum("usertag.EUserTagFindWhoType", EUserTagFindWhoType_name, EUserTagFindWhoType_value)
	proto.RegisterEnum("usertag.EGameScreenStatus", EGameScreenStatus_name, EGameScreenStatus_value)
	proto.RegisterEnum("usertag.EGameExtraOpt", EGameExtraOpt_name, EGameExtraOpt_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Usertag service

type UsertagClient interface {
	GetUserTag(ctx context.Context, in *GetUserTagReq, opts ...grpc.CallOption) (*GetUserTagResp, error)
	SetUserTag(ctx context.Context, in *SetUserTagReq, opts ...grpc.CallOption) (*SetUserTagResp, error)
	BatGetUserTag(ctx context.Context, in *BatGetUserTagReq, opts ...grpc.CallOption) (*BatGetUserTagResp, error)
	// 获取标签列表
	GetTagConfigList(ctx context.Context, in *GetTagConfigListReq, opts ...grpc.CallOption) (*GetTagConfigListResp, error)
	// 一般标签创建（ 年龄标签 游戏标签 个性标签 用这个创建）
	CreateTagConfig(ctx context.Context, in *CreateTagConfigReq, opts ...grpc.CallOption) (*CreateTagConfigResp, error)
	// 一般标签修改（ 就是全量覆盖，年龄标签 游戏标签 FindWho标签 个性标签 都可以用这个修改，包括修改名称 修改游戏属性 修改标签分类）
	ModifyTagConfig(ctx context.Context, in *ModifyTagConfigReq, opts ...grpc.CallOption) (*ModifyTagConfigResp, error)
	// 标签 删除 / 恢复
	DelTagConfig(ctx context.Context, in *DelTagConfigReq, opts ...grpc.CallOption) (*DelTagConfigResp, error)
	// 标签排序（ 年龄标签 游戏标签 FindWho标签的排序）
	SortTagConfig(ctx context.Context, in *SortTagConfigReq, opts ...grpc.CallOption) (*SortTagConfigResp, error)
	// findwho 标签的创建
	CreateFindWhoTypeTagConfig(ctx context.Context, in *CreateFindWhoTypeTagConfigReq, opts ...grpc.CallOption) (*CreateFindWhoTypeTagConfigResp, error)
	// 创建个性标签的分类
	CreateOptPersonalTagClassify(ctx context.Context, in *CreateOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*CreateOptPersonalTagClassifyResp, error)
	// 获取个性标签的分类列表
	GetOptPersonalTagClassifyList(ctx context.Context, in *GetOptPersonalTagClassifyListReq, opts ...grpc.CallOption) (*GetOptPersonalTagClassifyListResp, error)
	// 根据分类获取 这个分类下的个性标签的列表
	GetOptPersonalTagByClassify(ctx context.Context, in *GetOptPersonalTagByClassifyReq, opts ...grpc.CallOption) (*GetOptPersonalTagByClassifyResp, error)
	// 修改指定个性标签的分类de 名字
	ModifyOptPersonalTagClassifyName(ctx context.Context, in *ModifyOptPersonalTagClassifyNameReq, opts ...grpc.CallOption) (*ModifyOptPersonalTagClassifyNameResp, error)
	// 个性标签的分类的排序
	SortOptPersonalTagClassify(ctx context.Context, in *SortOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*SortOptPersonalTagClassifyResp, error)
	// 个性标签的分类的 删除 / 恢复
	DelOptPersonalTagClassify(ctx context.Context, in *DelOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*DelOptPersonalTagClassifyResp, error)
	SetUserOneTag(ctx context.Context, in *SetUserOneTagReq, opts ...grpc.CallOption) (*SetUserOneTagResp, error)
	GetSimpleGameTag(ctx context.Context, in *GetSimpleGameTagReq, opts ...grpc.CallOption) (*GetSimpleGameTagResp, error)
	NotifyChannelPlayChange(ctx context.Context, in *NotifyChannelPlayChangeReq, opts ...grpc.CallOption) (*NotifyChannelPlayChangeResp, error)
	BatGetSimpleGameTag(ctx context.Context, in *BatGetSimpleGameTagReq, opts ...grpc.CallOption) (*BatGetSimpleGameTagResp, error)
	// 单独设置游戏昵称/游戏截图，在审核后进行的设置
	SetUserGameNick(ctx context.Context, in *SetUserGameNickReq, opts ...grpc.CallOption) (*SetUserGameNickResp, error)
	SetUserGameScreenShot(ctx context.Context, in *SetUserGameScreenShotReq, opts ...grpc.CallOption) (*SetUserGameScreenShotResp, error)
	// 仅提供开关“屏蔽手机联系人”功能的记录。
	ChangeRecommendStatus(ctx context.Context, in *ChangeRecommendStatusReq, opts ...grpc.CallOption) (*ChangeRecommendStatusResp, error)
	GetRecommendStatus(ctx context.Context, in *GetRecommendStatusReq, opts ...grpc.CallOption) (*GetRecommendStatusResp, error)
	SetMosaicImage(ctx context.Context, in *SetMosaicImageReq, opts ...grpc.CallOption) (*SetMosaicImageResp, error)
}

type usertagClient struct {
	cc *grpc.ClientConn
}

func NewUsertagClient(cc *grpc.ClientConn) UsertagClient {
	return &usertagClient{cc}
}

func (c *usertagClient) GetUserTag(ctx context.Context, in *GetUserTagReq, opts ...grpc.CallOption) (*GetUserTagResp, error) {
	out := new(GetUserTagResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/GetUserTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) SetUserTag(ctx context.Context, in *SetUserTagReq, opts ...grpc.CallOption) (*SetUserTagResp, error) {
	out := new(SetUserTagResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/SetUserTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) BatGetUserTag(ctx context.Context, in *BatGetUserTagReq, opts ...grpc.CallOption) (*BatGetUserTagResp, error) {
	out := new(BatGetUserTagResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/BatGetUserTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) GetTagConfigList(ctx context.Context, in *GetTagConfigListReq, opts ...grpc.CallOption) (*GetTagConfigListResp, error) {
	out := new(GetTagConfigListResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/GetTagConfigList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) CreateTagConfig(ctx context.Context, in *CreateTagConfigReq, opts ...grpc.CallOption) (*CreateTagConfigResp, error) {
	out := new(CreateTagConfigResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/CreateTagConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) ModifyTagConfig(ctx context.Context, in *ModifyTagConfigReq, opts ...grpc.CallOption) (*ModifyTagConfigResp, error) {
	out := new(ModifyTagConfigResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/ModifyTagConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) DelTagConfig(ctx context.Context, in *DelTagConfigReq, opts ...grpc.CallOption) (*DelTagConfigResp, error) {
	out := new(DelTagConfigResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/DelTagConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) SortTagConfig(ctx context.Context, in *SortTagConfigReq, opts ...grpc.CallOption) (*SortTagConfigResp, error) {
	out := new(SortTagConfigResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/SortTagConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) CreateFindWhoTypeTagConfig(ctx context.Context, in *CreateFindWhoTypeTagConfigReq, opts ...grpc.CallOption) (*CreateFindWhoTypeTagConfigResp, error) {
	out := new(CreateFindWhoTypeTagConfigResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/CreateFindWhoTypeTagConfig", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) CreateOptPersonalTagClassify(ctx context.Context, in *CreateOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*CreateOptPersonalTagClassifyResp, error) {
	out := new(CreateOptPersonalTagClassifyResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/CreateOptPersonalTagClassify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) GetOptPersonalTagClassifyList(ctx context.Context, in *GetOptPersonalTagClassifyListReq, opts ...grpc.CallOption) (*GetOptPersonalTagClassifyListResp, error) {
	out := new(GetOptPersonalTagClassifyListResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/GetOptPersonalTagClassifyList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) GetOptPersonalTagByClassify(ctx context.Context, in *GetOptPersonalTagByClassifyReq, opts ...grpc.CallOption) (*GetOptPersonalTagByClassifyResp, error) {
	out := new(GetOptPersonalTagByClassifyResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/GetOptPersonalTagByClassify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) ModifyOptPersonalTagClassifyName(ctx context.Context, in *ModifyOptPersonalTagClassifyNameReq, opts ...grpc.CallOption) (*ModifyOptPersonalTagClassifyNameResp, error) {
	out := new(ModifyOptPersonalTagClassifyNameResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/ModifyOptPersonalTagClassifyName", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) SortOptPersonalTagClassify(ctx context.Context, in *SortOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*SortOptPersonalTagClassifyResp, error) {
	out := new(SortOptPersonalTagClassifyResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/SortOptPersonalTagClassify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) DelOptPersonalTagClassify(ctx context.Context, in *DelOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*DelOptPersonalTagClassifyResp, error) {
	out := new(DelOptPersonalTagClassifyResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/DelOptPersonalTagClassify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) SetUserOneTag(ctx context.Context, in *SetUserOneTagReq, opts ...grpc.CallOption) (*SetUserOneTagResp, error) {
	out := new(SetUserOneTagResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/SetUserOneTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) GetSimpleGameTag(ctx context.Context, in *GetSimpleGameTagReq, opts ...grpc.CallOption) (*GetSimpleGameTagResp, error) {
	out := new(GetSimpleGameTagResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/GetSimpleGameTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) NotifyChannelPlayChange(ctx context.Context, in *NotifyChannelPlayChangeReq, opts ...grpc.CallOption) (*NotifyChannelPlayChangeResp, error) {
	out := new(NotifyChannelPlayChangeResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/NotifyChannelPlayChange", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) BatGetSimpleGameTag(ctx context.Context, in *BatGetSimpleGameTagReq, opts ...grpc.CallOption) (*BatGetSimpleGameTagResp, error) {
	out := new(BatGetSimpleGameTagResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/BatGetSimpleGameTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) SetUserGameNick(ctx context.Context, in *SetUserGameNickReq, opts ...grpc.CallOption) (*SetUserGameNickResp, error) {
	out := new(SetUserGameNickResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/SetUserGameNick", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) SetUserGameScreenShot(ctx context.Context, in *SetUserGameScreenShotReq, opts ...grpc.CallOption) (*SetUserGameScreenShotResp, error) {
	out := new(SetUserGameScreenShotResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/SetUserGameScreenShot", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) ChangeRecommendStatus(ctx context.Context, in *ChangeRecommendStatusReq, opts ...grpc.CallOption) (*ChangeRecommendStatusResp, error) {
	out := new(ChangeRecommendStatusResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/ChangeRecommendStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) GetRecommendStatus(ctx context.Context, in *GetRecommendStatusReq, opts ...grpc.CallOption) (*GetRecommendStatusResp, error) {
	out := new(GetRecommendStatusResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/GetRecommendStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usertagClient) SetMosaicImage(ctx context.Context, in *SetMosaicImageReq, opts ...grpc.CallOption) (*SetMosaicImageResp, error) {
	out := new(SetMosaicImageResp)
	err := grpc.Invoke(ctx, "/usertag.usertag/SetMosaicImage", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Usertag service

type UsertagServer interface {
	GetUserTag(context.Context, *GetUserTagReq) (*GetUserTagResp, error)
	SetUserTag(context.Context, *SetUserTagReq) (*SetUserTagResp, error)
	BatGetUserTag(context.Context, *BatGetUserTagReq) (*BatGetUserTagResp, error)
	// 获取标签列表
	GetTagConfigList(context.Context, *GetTagConfigListReq) (*GetTagConfigListResp, error)
	// 一般标签创建（ 年龄标签 游戏标签 个性标签 用这个创建）
	CreateTagConfig(context.Context, *CreateTagConfigReq) (*CreateTagConfigResp, error)
	// 一般标签修改（ 就是全量覆盖，年龄标签 游戏标签 FindWho标签 个性标签 都可以用这个修改，包括修改名称 修改游戏属性 修改标签分类）
	ModifyTagConfig(context.Context, *ModifyTagConfigReq) (*ModifyTagConfigResp, error)
	// 标签 删除 / 恢复
	DelTagConfig(context.Context, *DelTagConfigReq) (*DelTagConfigResp, error)
	// 标签排序（ 年龄标签 游戏标签 FindWho标签的排序）
	SortTagConfig(context.Context, *SortTagConfigReq) (*SortTagConfigResp, error)
	// findwho 标签的创建
	CreateFindWhoTypeTagConfig(context.Context, *CreateFindWhoTypeTagConfigReq) (*CreateFindWhoTypeTagConfigResp, error)
	// 创建个性标签的分类
	CreateOptPersonalTagClassify(context.Context, *CreateOptPersonalTagClassifyReq) (*CreateOptPersonalTagClassifyResp, error)
	// 获取个性标签的分类列表
	GetOptPersonalTagClassifyList(context.Context, *GetOptPersonalTagClassifyListReq) (*GetOptPersonalTagClassifyListResp, error)
	// 根据分类获取 这个分类下的个性标签的列表
	GetOptPersonalTagByClassify(context.Context, *GetOptPersonalTagByClassifyReq) (*GetOptPersonalTagByClassifyResp, error)
	// 修改指定个性标签的分类de 名字
	ModifyOptPersonalTagClassifyName(context.Context, *ModifyOptPersonalTagClassifyNameReq) (*ModifyOptPersonalTagClassifyNameResp, error)
	// 个性标签的分类的排序
	SortOptPersonalTagClassify(context.Context, *SortOptPersonalTagClassifyReq) (*SortOptPersonalTagClassifyResp, error)
	// 个性标签的分类的 删除 / 恢复
	DelOptPersonalTagClassify(context.Context, *DelOptPersonalTagClassifyReq) (*DelOptPersonalTagClassifyResp, error)
	SetUserOneTag(context.Context, *SetUserOneTagReq) (*SetUserOneTagResp, error)
	GetSimpleGameTag(context.Context, *GetSimpleGameTagReq) (*GetSimpleGameTagResp, error)
	NotifyChannelPlayChange(context.Context, *NotifyChannelPlayChangeReq) (*NotifyChannelPlayChangeResp, error)
	BatGetSimpleGameTag(context.Context, *BatGetSimpleGameTagReq) (*BatGetSimpleGameTagResp, error)
	// 单独设置游戏昵称/游戏截图，在审核后进行的设置
	SetUserGameNick(context.Context, *SetUserGameNickReq) (*SetUserGameNickResp, error)
	SetUserGameScreenShot(context.Context, *SetUserGameScreenShotReq) (*SetUserGameScreenShotResp, error)
	// 仅提供开关“屏蔽手机联系人”功能的记录。
	ChangeRecommendStatus(context.Context, *ChangeRecommendStatusReq) (*ChangeRecommendStatusResp, error)
	GetRecommendStatus(context.Context, *GetRecommendStatusReq) (*GetRecommendStatusResp, error)
	SetMosaicImage(context.Context, *SetMosaicImageReq) (*SetMosaicImageResp, error)
}

func RegisterUsertagServer(s *grpc.Server, srv UsertagServer) {
	s.RegisterService(&_Usertag_serviceDesc, srv)
}

func _Usertag_GetUserTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).GetUserTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/GetUserTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).GetUserTag(ctx, req.(*GetUserTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_SetUserTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).SetUserTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/SetUserTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).SetUserTag(ctx, req.(*SetUserTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_BatGetUserTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).BatGetUserTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/BatGetUserTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).BatGetUserTag(ctx, req.(*BatGetUserTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_GetTagConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTagConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).GetTagConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/GetTagConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).GetTagConfigList(ctx, req.(*GetTagConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_CreateTagConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTagConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).CreateTagConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/CreateTagConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).CreateTagConfig(ctx, req.(*CreateTagConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_ModifyTagConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyTagConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).ModifyTagConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/ModifyTagConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).ModifyTagConfig(ctx, req.(*ModifyTagConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_DelTagConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTagConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).DelTagConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/DelTagConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).DelTagConfig(ctx, req.(*DelTagConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_SortTagConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortTagConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).SortTagConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/SortTagConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).SortTagConfig(ctx, req.(*SortTagConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_CreateFindWhoTypeTagConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFindWhoTypeTagConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).CreateFindWhoTypeTagConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/CreateFindWhoTypeTagConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).CreateFindWhoTypeTagConfig(ctx, req.(*CreateFindWhoTypeTagConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_CreateOptPersonalTagClassify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOptPersonalTagClassifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).CreateOptPersonalTagClassify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/CreateOptPersonalTagClassify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).CreateOptPersonalTagClassify(ctx, req.(*CreateOptPersonalTagClassifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_GetOptPersonalTagClassifyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOptPersonalTagClassifyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).GetOptPersonalTagClassifyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/GetOptPersonalTagClassifyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).GetOptPersonalTagClassifyList(ctx, req.(*GetOptPersonalTagClassifyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_GetOptPersonalTagByClassify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOptPersonalTagByClassifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).GetOptPersonalTagByClassify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/GetOptPersonalTagByClassify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).GetOptPersonalTagByClassify(ctx, req.(*GetOptPersonalTagByClassifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_ModifyOptPersonalTagClassifyName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyOptPersonalTagClassifyNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).ModifyOptPersonalTagClassifyName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/ModifyOptPersonalTagClassifyName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).ModifyOptPersonalTagClassifyName(ctx, req.(*ModifyOptPersonalTagClassifyNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_SortOptPersonalTagClassify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortOptPersonalTagClassifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).SortOptPersonalTagClassify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/SortOptPersonalTagClassify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).SortOptPersonalTagClassify(ctx, req.(*SortOptPersonalTagClassifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_DelOptPersonalTagClassify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelOptPersonalTagClassifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).DelOptPersonalTagClassify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/DelOptPersonalTagClassify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).DelOptPersonalTagClassify(ctx, req.(*DelOptPersonalTagClassifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_SetUserOneTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserOneTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).SetUserOneTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/SetUserOneTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).SetUserOneTag(ctx, req.(*SetUserOneTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_GetSimpleGameTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSimpleGameTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).GetSimpleGameTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/GetSimpleGameTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).GetSimpleGameTag(ctx, req.(*GetSimpleGameTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_NotifyChannelPlayChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyChannelPlayChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).NotifyChannelPlayChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/NotifyChannelPlayChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).NotifyChannelPlayChange(ctx, req.(*NotifyChannelPlayChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_BatGetSimpleGameTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetSimpleGameTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).BatGetSimpleGameTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/BatGetSimpleGameTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).BatGetSimpleGameTag(ctx, req.(*BatGetSimpleGameTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_SetUserGameNick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserGameNickReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).SetUserGameNick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/SetUserGameNick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).SetUserGameNick(ctx, req.(*SetUserGameNickReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_SetUserGameScreenShot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserGameScreenShotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).SetUserGameScreenShot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/SetUserGameScreenShot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).SetUserGameScreenShot(ctx, req.(*SetUserGameScreenShotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_ChangeRecommendStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeRecommendStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).ChangeRecommendStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/ChangeRecommendStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).ChangeRecommendStatus(ctx, req.(*ChangeRecommendStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_GetRecommendStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).GetRecommendStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/GetRecommendStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).GetRecommendStatus(ctx, req.(*GetRecommendStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Usertag_SetMosaicImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMosaicImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsertagServer).SetMosaicImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usertag.usertag/SetMosaicImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsertagServer).SetMosaicImage(ctx, req.(*SetMosaicImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Usertag_serviceDesc = grpc.ServiceDesc{
	ServiceName: "usertag.usertag",
	HandlerType: (*UsertagServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserTag",
			Handler:    _Usertag_GetUserTag_Handler,
		},
		{
			MethodName: "SetUserTag",
			Handler:    _Usertag_SetUserTag_Handler,
		},
		{
			MethodName: "BatGetUserTag",
			Handler:    _Usertag_BatGetUserTag_Handler,
		},
		{
			MethodName: "GetTagConfigList",
			Handler:    _Usertag_GetTagConfigList_Handler,
		},
		{
			MethodName: "CreateTagConfig",
			Handler:    _Usertag_CreateTagConfig_Handler,
		},
		{
			MethodName: "ModifyTagConfig",
			Handler:    _Usertag_ModifyTagConfig_Handler,
		},
		{
			MethodName: "DelTagConfig",
			Handler:    _Usertag_DelTagConfig_Handler,
		},
		{
			MethodName: "SortTagConfig",
			Handler:    _Usertag_SortTagConfig_Handler,
		},
		{
			MethodName: "CreateFindWhoTypeTagConfig",
			Handler:    _Usertag_CreateFindWhoTypeTagConfig_Handler,
		},
		{
			MethodName: "CreateOptPersonalTagClassify",
			Handler:    _Usertag_CreateOptPersonalTagClassify_Handler,
		},
		{
			MethodName: "GetOptPersonalTagClassifyList",
			Handler:    _Usertag_GetOptPersonalTagClassifyList_Handler,
		},
		{
			MethodName: "GetOptPersonalTagByClassify",
			Handler:    _Usertag_GetOptPersonalTagByClassify_Handler,
		},
		{
			MethodName: "ModifyOptPersonalTagClassifyName",
			Handler:    _Usertag_ModifyOptPersonalTagClassifyName_Handler,
		},
		{
			MethodName: "SortOptPersonalTagClassify",
			Handler:    _Usertag_SortOptPersonalTagClassify_Handler,
		},
		{
			MethodName: "DelOptPersonalTagClassify",
			Handler:    _Usertag_DelOptPersonalTagClassify_Handler,
		},
		{
			MethodName: "SetUserOneTag",
			Handler:    _Usertag_SetUserOneTag_Handler,
		},
		{
			MethodName: "GetSimpleGameTag",
			Handler:    _Usertag_GetSimpleGameTag_Handler,
		},
		{
			MethodName: "NotifyChannelPlayChange",
			Handler:    _Usertag_NotifyChannelPlayChange_Handler,
		},
		{
			MethodName: "BatGetSimpleGameTag",
			Handler:    _Usertag_BatGetSimpleGameTag_Handler,
		},
		{
			MethodName: "SetUserGameNick",
			Handler:    _Usertag_SetUserGameNick_Handler,
		},
		{
			MethodName: "SetUserGameScreenShot",
			Handler:    _Usertag_SetUserGameScreenShot_Handler,
		},
		{
			MethodName: "ChangeRecommendStatus",
			Handler:    _Usertag_ChangeRecommendStatus_Handler,
		},
		{
			MethodName: "GetRecommendStatus",
			Handler:    _Usertag_GetRecommendStatus_Handler,
		},
		{
			MethodName: "SetMosaicImage",
			Handler:    _Usertag_SetMosaicImage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/userTagSvr/usertag.proto",
}

func (m *UserTagBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserTagBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TagType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagType))
	}
	if len(m.TagName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.TagName)))
		i += copy(dAtA[i:], m.TagName)
	}
	if m.TagId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagId))
	}
	if len(m.TagInfo) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.TagInfo)))
		i += copy(dAtA[i:], m.TagInfo)
	}
	if m.IsDel {
		dAtA[i] = 0x28
		i++
		if m.IsDel {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *UserTagList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserTagList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Uid))
	}
	if len(m.TagList) > 0 {
		for _, msg := range m.TagList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UserAgeTagExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserAgeTagExt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.AgeYearMin != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.AgeYearMin))
	}
	if m.AgeYearMax != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.AgeYearMax))
	}
	return i, nil
}

func (m *UserFindWhoTagExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserFindWhoTagExt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FindwhoType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.FindwhoType))
	}
	if m.AssignSex != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.AssignSex))
	}
	if m.CorrelationPairTagId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.CorrelationPairTagId))
	}
	return i, nil
}

func (m *GameOptSecondProp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameOptSecondProp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OptProp) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.OptProp)))
		i += copy(dAtA[i:], m.OptProp)
	}
	if len(m.ValueList) > 0 {
		for _, s := range m.ValueList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *UserGameTagOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserGameTagOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OptName) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.OptName)))
		i += copy(dAtA[i:], m.OptName)
	}
	if m.OptId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.OptId))
	}
	if m.IsSupportMutiSet {
		dAtA[i] = 0x18
		i++
		if m.IsSupportMutiSet {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if len(m.ValueConfList) > 0 {
		for _, s := range m.ValueConfList {
			dAtA[i] = 0x22
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.ValueUsersetList) > 0 {
		for _, s := range m.ValueUsersetList {
			dAtA[i] = 0x2a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if m.SupportMutiSetCnt != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.SupportMutiSetCnt))
	}
	if m.PartitionId != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.PartitionId))
	}
	if len(m.PropValueList) > 0 {
		for _, msg := range m.PropValueList {
			dAtA[i] = 0x42
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GameScreenShot) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameScreenShot) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.AuditStatus != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.AuditStatus))
	}
	if len(m.ImgUrl) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.ImgUrl)))
		i += copy(dAtA[i:], m.ImgUrl)
	}
	if len(m.ImgUrlInvisibleGamenick) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.ImgUrlInvisibleGamenick)))
		i += copy(dAtA[i:], m.ImgUrlInvisibleGamenick)
	}
	if m.BeginAuditTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.BeginAuditTime))
	}
	if m.Index != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Index))
	}
	if m.UploadTime != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.UploadTime))
	}
	return i, nil
}

func (m *UserGameTagExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserGameTagExt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GameId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.GameId))
	}
	if len(m.BackImgUrl) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.BackImgUrl)))
		i += copy(dAtA[i:], m.BackImgUrl)
	}
	if len(m.OptList) > 0 {
		for _, msg := range m.OptList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ThumbImgUrl) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.ThumbImgUrl)))
		i += copy(dAtA[i:], m.ThumbImgUrl)
	}
	if len(m.GameNickname) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameNickname)))
		i += copy(dAtA[i:], m.GameNickname)
	}
	if m.GameScreenshot != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.GameScreenshot.Size()))
		n1, err := m.GameScreenshot.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if len(m.GameScreenshotList) > 0 {
		for _, msg := range m.GameScreenshotList {
			dAtA[i] = 0x3a
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *LevelImg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LevelImg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LevelName) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.LevelName)))
		i += copy(dAtA[i:], m.LevelName)
	}
	if len(m.ImgUrl) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.ImgUrl)))
		i += copy(dAtA[i:], m.ImgUrl)
	}
	return i, nil
}

func (m *ConfGameTagOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfGameTagOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OptName) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.OptName)))
		i += copy(dAtA[i:], m.OptName)
	}
	if m.OptId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.OptId))
	}
	if m.IsSupportMutiSet {
		dAtA[i] = 0x18
		i++
		if m.IsSupportMutiSet {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if len(m.ValueConfList) > 0 {
		for _, s := range m.ValueConfList {
			dAtA[i] = 0x22
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.ValueUsersetList) > 0 {
		for _, s := range m.ValueUsersetList {
			dAtA[i] = 0x2a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if m.SupportMutiSetCnt != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.SupportMutiSetCnt))
	}
	if m.PartitionId != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.PartitionId))
	}
	if len(m.PropValueList) > 0 {
		for _, msg := range m.PropValueList {
			dAtA[i] = 0x42
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.IsMust {
		dAtA[i] = 0x48
		i++
		if m.IsMust {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.TextBoxStyle != 0 {
		dAtA[i] = 0x50
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TextBoxStyle))
	}
	return i, nil
}

func (m *ConfGameTagExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfGameTagExt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GameId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.GameId))
	}
	if len(m.BackImgUrl) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.BackImgUrl)))
		i += copy(dAtA[i:], m.BackImgUrl)
	}
	if len(m.OptList) > 0 {
		for _, msg := range m.OptList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ThumbImgUrl) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.ThumbImgUrl)))
		i += copy(dAtA[i:], m.ThumbImgUrl)
	}
	if len(m.GameNickname) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameNickname)))
		i += copy(dAtA[i:], m.GameNickname)
	}
	if m.GameScreenshot != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.GameScreenshot.Size()))
		n2, err := m.GameScreenshot.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.GameCardImgUrl) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameCardImgUrl)))
		i += copy(dAtA[i:], m.GameCardImgUrl)
	}
	if len(m.GameBackImg) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameBackImg)))
		i += copy(dAtA[i:], m.GameBackImg)
	}
	if len(m.GameIconImg) > 0 {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameIconImg)))
		i += copy(dAtA[i:], m.GameIconImg)
	}
	if len(m.GameCornerMarkImg) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameCornerMarkImg)))
		i += copy(dAtA[i:], m.GameCornerMarkImg)
	}
	if len(m.GameNoLevelImgUrl) > 0 {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameNoLevelImgUrl)))
		i += copy(dAtA[i:], m.GameNoLevelImgUrl)
	}
	if len(m.LevelImgUrl) > 0 {
		for _, msg := range m.LevelImgUrl {
			dAtA[i] = 0x62
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.MicLevelImgUrl) > 0 {
		for _, msg := range m.MicLevelImgUrl {
			dAtA[i] = 0x6a
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.GameBackImgMini) > 0 {
		dAtA[i] = 0x72
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameBackImgMini)))
		i += copy(dAtA[i:], m.GameBackImgMini)
	}
	if m.GameBackColorNum != 0 {
		dAtA[i] = 0x78
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.GameBackColorNum))
	}
	if m.GameExtraOptSwitch != 0 {
		dAtA[i] = 0x80
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.GameExtraOptSwitch))
	}
	if len(m.LevelImgUrlNew) > 0 {
		for _, msg := range m.LevelImgUrlNew {
			dAtA[i] = 0x8a
			i++
			dAtA[i] = 0x1
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.GameNoLevelImgUrlNew) > 0 {
		dAtA[i] = 0x92
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameNoLevelImgUrlNew)))
		i += copy(dAtA[i:], m.GameNoLevelImgUrlNew)
	}
	return i, nil
}

func (m *SimpleGameTagList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleGameTagList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Uid))
	}
	if len(m.ExtList) > 0 {
		for _, msg := range m.ExtList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SimpleGameTagExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleGameTagExt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GameId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.GameId))
	}
	if len(m.GameName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameName)))
		i += copy(dAtA[i:], m.GameName)
	}
	if len(m.GameNickname) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameNickname)))
		i += copy(dAtA[i:], m.GameNickname)
	}
	if len(m.GameArea) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameArea)))
		i += copy(dAtA[i:], m.GameArea)
	}
	if len(m.GameDan) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameDan)))
		i += copy(dAtA[i:], m.GameDan)
	}
	if m.Uid != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Uid))
	}
	if len(m.GameDanUrlForMic) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameDanUrlForMic)))
		i += copy(dAtA[i:], m.GameDanUrlForMic)
	}
	if len(m.GameRole) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameRole)))
		i += copy(dAtA[i:], m.GameRole)
	}
	if len(m.GamePosition) > 0 {
		for _, s := range m.GamePosition {
			dAtA[i] = 0x4a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.GameHeroList) > 0 {
		for _, s := range m.GameHeroList {
			dAtA[i] = 0x52
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.GameScreenshot) > 0 {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameScreenshot)))
		i += copy(dAtA[i:], m.GameScreenshot)
	}
	if len(m.GameStyle) > 0 {
		for _, s := range m.GameStyle {
			dAtA[i] = 0x62
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.GameLevelUrl) > 0 {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameLevelUrl)))
		i += copy(dAtA[i:], m.GameLevelUrl)
	}
	if m.TagId != 0 {
		dAtA[i] = 0x70
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagId))
	}
	if m.IsCompleted {
		dAtA[i] = 0x78
		i++
		if m.IsCompleted {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *UserOptPersonalTagClassify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserOptPersonalTagClassify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ClassifyId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.ClassifyId))
	}
	if len(m.ClassifyName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.ClassifyName)))
		i += copy(dAtA[i:], m.ClassifyName)
	}
	if len(m.TagList) > 0 {
		for _, msg := range m.TagList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.IsDel {
		dAtA[i] = 0x20
		i++
		if m.IsDel {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *UserOptPersonalTagExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserOptPersonalTagExt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ClassifyId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.ClassifyId))
	}
	return i, nil
}

func (m *GetUserTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TargetUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TargetUid))
	}
	if m.IsNeedTagExt {
		dAtA[i] = 0x10
		i++
		if m.IsNeedTagExt {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *GetUserTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserTaglist != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.UserTaglist.Size()))
		n3, err := m.UserTaglist.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *SetUserTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TargetUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TargetUid))
	}
	if m.TagType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagType))
	}
	if len(m.TagList) > 0 {
		for _, msg := range m.TagList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.SettingCnt != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.SettingCnt))
	}
	if m.IgnoreGame {
		dAtA[i] = 0x28
		i++
		if m.IgnoreGame {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *SetUserTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UserTagReplaceInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserTagReplaceInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FromTagId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.FromTagId))
	}
	if m.FromTagType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.FromTagType))
	}
	if m.ToTagId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.ToTagId))
	}
	if m.ToTagType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.ToTagType))
	}
	return i, nil
}

func (m *SetUserOneTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserOneTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TargetUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TargetUid))
	}
	if m.TagType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagType))
	}
	if m.OperType != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.OperType))
	}
	if m.TagBase != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagBase.Size()))
		n4, err := m.TagBase.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.TagReplace != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagReplace.Size()))
		n5, err := m.TagReplace.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.TagSortValue != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagSortValue))
	}
	if m.AppId != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.AppId))
	}
	if m.MarketId != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.MarketId))
	}
	if m.JustSetGamenick {
		dAtA[i] = 0x50
		i++
		if m.JustSetGamenick {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *SetUserOneTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserOneTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatGetUserTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		dAtA7 := make([]byte, len(m.UidList)*10)
		var j6 int
		for _, num := range m.UidList {
			for num >= 1<<7 {
				dAtA7[j6] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j6++
			}
			dAtA7[j6] = uint8(num)
			j6++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(j6))
		i += copy(dAtA[i:], dAtA7[:j6])
	}
	if m.IsNeedTagExt {
		dAtA[i] = 0x10
		i++
		if m.IsNeedTagExt {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *BatGetUserTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UsertaglistList) > 0 {
		for _, msg := range m.UsertaglistList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetTagConfigListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagConfigListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TagType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagType))
	}
	return i, nil
}

func (m *GetTagConfigListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagConfigListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TagList) > 0 {
		for _, msg := range m.TagList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.GameIdList) > 0 {
		dAtA9 := make([]byte, len(m.GameIdList)*10)
		var j8 int
		for _, num := range m.GameIdList {
			for num >= 1<<7 {
				dAtA9[j8] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j8++
			}
			dAtA9[j8] = uint8(num)
			j8++
		}
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(j8))
		i += copy(dAtA[i:], dAtA9[:j8])
	}
	return i, nil
}

func (m *CreateTagConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTagConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TagType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagType))
	}
	if m.Tag != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Tag.Size()))
		n10, err := m.Tag.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *CreateTagConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTagConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TagId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagId))
	}
	return i, nil
}

func (m *ModifyTagConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyTagConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TagId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagId))
	}
	if m.Tag != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Tag.Size()))
		n11, err := m.Tag.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *ModifyTagConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyTagConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelTagConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelTagConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TagId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagId))
	}
	if m.IsRecover {
		dAtA[i] = 0x10
		i++
		if m.IsRecover {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *DelTagConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelTagConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SortTagConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SortTagConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TagType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagType))
	}
	if len(m.TagIdList) > 0 {
		dAtA13 := make([]byte, len(m.TagIdList)*10)
		var j12 int
		for _, num := range m.TagIdList {
			for num >= 1<<7 {
				dAtA13[j12] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j12++
			}
			dAtA13[j12] = uint8(num)
			j12++
		}
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(j12))
		i += copy(dAtA[i:], dAtA13[:j12])
	}
	if m.PersonaltypeClassifyId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.PersonaltypeClassifyId))
	}
	return i, nil
}

func (m *SortTagConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SortTagConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CreateFindWhoTypeTagConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateFindWhoTypeTagConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FindwhoType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.FindwhoType))
	}
	if len(m.TagName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.TagName)))
		i += copy(dAtA[i:], m.TagName)
	}
	if m.AssignSex != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.AssignSex))
	}
	if len(m.CorrelationTagName) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.CorrelationTagName)))
		i += copy(dAtA[i:], m.CorrelationTagName)
	}
	return i, nil
}

func (m *CreateFindWhoTypeTagConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateFindWhoTypeTagConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FindwhoType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.FindwhoType))
	}
	if m.TagId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagId))
	}
	if m.CorrelationTagId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.CorrelationTagId))
	}
	return i, nil
}

func (m *CreateOptPersonalTagClassifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateOptPersonalTagClassifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ClassifyName) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.ClassifyName)))
		i += copy(dAtA[i:], m.ClassifyName)
	}
	return i, nil
}

func (m *CreateOptPersonalTagClassifyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateOptPersonalTagClassifyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ClassifyId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.ClassifyId))
	}
	return i, nil
}

func (m *GetOptPersonalTagClassifyListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOptPersonalTagClassifyListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetOptPersonalTagClassifyListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOptPersonalTagClassifyListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ClassifyList) > 0 {
		for _, msg := range m.ClassifyList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetOptPersonalTagByClassifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOptPersonalTagByClassifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ClassifyId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.ClassifyId))
	}
	return i, nil
}

func (m *GetOptPersonalTagByClassifyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOptPersonalTagByClassifyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TagList) > 0 {
		for _, msg := range m.TagList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ModifyOptPersonalTagClassifyNameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyOptPersonalTagClassifyNameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ClassifyId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.ClassifyId))
	}
	if len(m.ClassifyName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.ClassifyName)))
		i += copy(dAtA[i:], m.ClassifyName)
	}
	return i, nil
}

func (m *ModifyOptPersonalTagClassifyNameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyOptPersonalTagClassifyNameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SortOptPersonalTagClassifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SortOptPersonalTagClassifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ClassifyIdList) > 0 {
		dAtA15 := make([]byte, len(m.ClassifyIdList)*10)
		var j14 int
		for _, num := range m.ClassifyIdList {
			for num >= 1<<7 {
				dAtA15[j14] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j14++
			}
			dAtA15[j14] = uint8(num)
			j14++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(j14))
		i += copy(dAtA[i:], dAtA15[:j14])
	}
	return i, nil
}

func (m *SortOptPersonalTagClassifyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SortOptPersonalTagClassifyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelOptPersonalTagClassifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelOptPersonalTagClassifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ClassifyId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.ClassifyId))
	}
	if m.IsRecover {
		dAtA[i] = 0x10
		i++
		if m.IsRecover {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *DelOptPersonalTagClassifyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelOptPersonalTagClassifyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserGameTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserGameTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Uid))
	}
	if len(m.GameName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameName)))
		i += copy(dAtA[i:], m.GameName)
	}
	return i, nil
}

func (m *GetUserGameTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserGameTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GameExt != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.GameExt.Size()))
		n16, err := m.GameExt.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	return i, nil
}

func (m *GetUserGameTagDanReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserGameTagDanReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		dAtA18 := make([]byte, len(m.UidList)*10)
		var j17 int
		for _, num := range m.UidList {
			for num >= 1<<7 {
				dAtA18[j17] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j17++
			}
			dAtA18[j17] = uint8(num)
			j17++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(j17))
		i += copy(dAtA[i:], dAtA18[:j17])
	}
	if len(m.GameName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameName)))
		i += copy(dAtA[i:], m.GameName)
	}
	return i, nil
}

func (m *GameTagDan) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameTagDan) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Uid))
	}
	if len(m.GameName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameName)))
		i += copy(dAtA[i:], m.GameName)
	}
	if len(m.GameDan) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameDan)))
		i += copy(dAtA[i:], m.GameDan)
	}
	return i, nil
}

func (m *GetUserGameTagDanResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserGameTagDanResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameDanList) > 0 {
		for _, msg := range m.GameDanList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetSimpleGameTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSimpleGameTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		dAtA20 := make([]byte, len(m.UidList)*10)
		var j19 int
		for _, num := range m.UidList {
			for num >= 1<<7 {
				dAtA20[j19] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j19++
			}
			dAtA20[j19] = uint8(num)
			j19++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(j19))
		i += copy(dAtA[i:], dAtA20[:j19])
	}
	if len(m.GameName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameName)))
		i += copy(dAtA[i:], m.GameName)
	}
	return i, nil
}

func (m *GetSimpleGameTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSimpleGameTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameExtList) > 0 {
		for _, msg := range m.GameExtList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatGetSimpleGameTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetSimpleGameTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		dAtA22 := make([]byte, len(m.UidList)*10)
		var j21 int
		for _, num := range m.UidList {
			for num >= 1<<7 {
				dAtA22[j21] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j21++
			}
			dAtA22[j21] = uint8(num)
			j21++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(j21))
		i += copy(dAtA[i:], dAtA22[:j21])
	}
	if len(m.GameName) > 0 {
		for _, s := range m.GameName {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *BatGetSimpleGameTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetSimpleGameTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameExtList) > 0 {
		for _, msg := range m.GameExtList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintUsertag(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *NotifyChannelPlayChangeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyChannelPlayChangeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChannelId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.ChannelId))
	}
	if m.PlayType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.PlayType))
	}
	if len(m.PlayName) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.PlayName)))
		i += copy(dAtA[i:], m.PlayName)
	}
	return i, nil
}

func (m *NotifyChannelPlayChangeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyChannelPlayChangeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetUserGameNickReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserGameNickReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TargetUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TargetUid))
	}
	if m.TagType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagType))
	}
	if m.OperType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.OperType))
	}
	if m.TagBase != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagBase.Size()))
		n23, err := m.TagBase.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	if m.AppId != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.AppId))
	}
	if m.MarketId != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.MarketId))
	}
	return i, nil
}

func (m *SetUserGameNickResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserGameNickResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetUserGameScreenShotReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserGameScreenShotReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TargetUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TargetUid))
	}
	if m.TagType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagType))
	}
	if m.OperType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.OperType))
	}
	if m.TagId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.TagId))
	}
	if m.GameScreenshot != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.GameScreenshot.Size()))
		n24, err := m.GameScreenshot.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	if m.AppId != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.AppId))
	}
	if m.MarketId != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.MarketId))
	}
	return i, nil
}

func (m *SetUserGameScreenShotResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserGameScreenShotResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ChangeRecommendStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeRecommendStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Uid))
	}
	if m.Status != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Status))
	}
	return i, nil
}

func (m *ChangeRecommendStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeRecommendStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetRecommendStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetRecommendStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Status != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.Status))
	}
	return i, nil
}

func (m *SetMosaicImageContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetMosaicImageContext) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GameScreenshotReq != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(m.GameScreenshotReq.Size()))
		n25, err := m.GameScreenshotReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	if len(m.GameName) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.GameName)))
		i += copy(dAtA[i:], m.GameName)
	}
	return i, nil
}

func (m *SetMosaicImageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetMosaicImageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Result {
		dAtA[i] = 0x8
		i++
		if m.Result {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if len(m.MosaicImageUrl) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.MosaicImageUrl)))
		i += copy(dAtA[i:], m.MosaicImageUrl)
	}
	if len(m.Context) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintUsertag(dAtA, i, uint64(len(m.Context)))
		i += copy(dAtA[i:], m.Context)
	}
	if m.Source {
		dAtA[i] = 0x20
		i++
		if m.Source {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *SetMosaicImageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetMosaicImageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Usertag(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Usertag(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintUsertag(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UserTagBase) Size() (n int) {
	var l int
	_ = l
	if m.TagType != 0 {
		n += 1 + sovUsertag(uint64(m.TagType))
	}
	l = len(m.TagName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.TagId != 0 {
		n += 1 + sovUsertag(uint64(m.TagId))
	}
	l = len(m.TagInfo)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.IsDel {
		n += 2
	}
	return n
}

func (m *UserTagList) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUsertag(uint64(m.Uid))
	}
	if len(m.TagList) > 0 {
		for _, e := range m.TagList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *UserAgeTagExt) Size() (n int) {
	var l int
	_ = l
	if m.AgeYearMin != 0 {
		n += 1 + sovUsertag(uint64(m.AgeYearMin))
	}
	if m.AgeYearMax != 0 {
		n += 1 + sovUsertag(uint64(m.AgeYearMax))
	}
	return n
}

func (m *UserFindWhoTagExt) Size() (n int) {
	var l int
	_ = l
	if m.FindwhoType != 0 {
		n += 1 + sovUsertag(uint64(m.FindwhoType))
	}
	if m.AssignSex != 0 {
		n += 1 + sovUsertag(uint64(m.AssignSex))
	}
	if m.CorrelationPairTagId != 0 {
		n += 1 + sovUsertag(uint64(m.CorrelationPairTagId))
	}
	return n
}

func (m *GameOptSecondProp) Size() (n int) {
	var l int
	_ = l
	l = len(m.OptProp)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if len(m.ValueList) > 0 {
		for _, s := range m.ValueList {
			l = len(s)
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *UserGameTagOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.OptName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.OptId != 0 {
		n += 1 + sovUsertag(uint64(m.OptId))
	}
	if m.IsSupportMutiSet {
		n += 2
	}
	if len(m.ValueConfList) > 0 {
		for _, s := range m.ValueConfList {
			l = len(s)
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	if len(m.ValueUsersetList) > 0 {
		for _, s := range m.ValueUsersetList {
			l = len(s)
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	if m.SupportMutiSetCnt != 0 {
		n += 1 + sovUsertag(uint64(m.SupportMutiSetCnt))
	}
	if m.PartitionId != 0 {
		n += 1 + sovUsertag(uint64(m.PartitionId))
	}
	if len(m.PropValueList) > 0 {
		for _, e := range m.PropValueList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *GameScreenShot) Size() (n int) {
	var l int
	_ = l
	if m.AuditStatus != 0 {
		n += 1 + sovUsertag(uint64(m.AuditStatus))
	}
	l = len(m.ImgUrl)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.ImgUrlInvisibleGamenick)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.BeginAuditTime != 0 {
		n += 1 + sovUsertag(uint64(m.BeginAuditTime))
	}
	if m.Index != 0 {
		n += 1 + sovUsertag(uint64(m.Index))
	}
	if m.UploadTime != 0 {
		n += 1 + sovUsertag(uint64(m.UploadTime))
	}
	return n
}

func (m *UserGameTagExt) Size() (n int) {
	var l int
	_ = l
	if m.GameId != 0 {
		n += 1 + sovUsertag(uint64(m.GameId))
	}
	l = len(m.BackImgUrl)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if len(m.OptList) > 0 {
		for _, e := range m.OptList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	l = len(m.ThumbImgUrl)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameNickname)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.GameScreenshot != nil {
		l = m.GameScreenshot.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	if len(m.GameScreenshotList) > 0 {
		for _, e := range m.GameScreenshotList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *LevelImg) Size() (n int) {
	var l int
	_ = l
	l = len(m.LevelName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.ImgUrl)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *ConfGameTagOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.OptName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.OptId != 0 {
		n += 1 + sovUsertag(uint64(m.OptId))
	}
	if m.IsSupportMutiSet {
		n += 2
	}
	if len(m.ValueConfList) > 0 {
		for _, s := range m.ValueConfList {
			l = len(s)
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	if len(m.ValueUsersetList) > 0 {
		for _, s := range m.ValueUsersetList {
			l = len(s)
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	if m.SupportMutiSetCnt != 0 {
		n += 1 + sovUsertag(uint64(m.SupportMutiSetCnt))
	}
	if m.PartitionId != 0 {
		n += 1 + sovUsertag(uint64(m.PartitionId))
	}
	if len(m.PropValueList) > 0 {
		for _, e := range m.PropValueList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	if m.IsMust {
		n += 2
	}
	if m.TextBoxStyle != 0 {
		n += 1 + sovUsertag(uint64(m.TextBoxStyle))
	}
	return n
}

func (m *ConfGameTagExt) Size() (n int) {
	var l int
	_ = l
	if m.GameId != 0 {
		n += 1 + sovUsertag(uint64(m.GameId))
	}
	l = len(m.BackImgUrl)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if len(m.OptList) > 0 {
		for _, e := range m.OptList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	l = len(m.ThumbImgUrl)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameNickname)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.GameScreenshot != nil {
		l = m.GameScreenshot.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameCardImgUrl)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameBackImg)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameIconImg)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameCornerMarkImg)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameNoLevelImgUrl)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if len(m.LevelImgUrl) > 0 {
		for _, e := range m.LevelImgUrl {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	if len(m.MicLevelImgUrl) > 0 {
		for _, e := range m.MicLevelImgUrl {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	l = len(m.GameBackImgMini)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.GameBackColorNum != 0 {
		n += 1 + sovUsertag(uint64(m.GameBackColorNum))
	}
	if m.GameExtraOptSwitch != 0 {
		n += 2 + sovUsertag(uint64(m.GameExtraOptSwitch))
	}
	if len(m.LevelImgUrlNew) > 0 {
		for _, e := range m.LevelImgUrlNew {
			l = e.Size()
			n += 2 + l + sovUsertag(uint64(l))
		}
	}
	l = len(m.GameNoLevelImgUrlNew)
	if l > 0 {
		n += 2 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *SimpleGameTagList) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUsertag(uint64(m.Uid))
	}
	if len(m.ExtList) > 0 {
		for _, e := range m.ExtList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *SimpleGameTagExt) Size() (n int) {
	var l int
	_ = l
	if m.GameId != 0 {
		n += 1 + sovUsertag(uint64(m.GameId))
	}
	l = len(m.GameName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameNickname)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameArea)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameDan)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.Uid != 0 {
		n += 1 + sovUsertag(uint64(m.Uid))
	}
	l = len(m.GameDanUrlForMic)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameRole)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if len(m.GamePosition) > 0 {
		for _, s := range m.GamePosition {
			l = len(s)
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	if len(m.GameHeroList) > 0 {
		for _, s := range m.GameHeroList {
			l = len(s)
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	l = len(m.GameScreenshot)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if len(m.GameStyle) > 0 {
		for _, s := range m.GameStyle {
			l = len(s)
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	l = len(m.GameLevelUrl)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.TagId != 0 {
		n += 1 + sovUsertag(uint64(m.TagId))
	}
	if m.IsCompleted {
		n += 2
	}
	return n
}

func (m *UserOptPersonalTagClassify) Size() (n int) {
	var l int
	_ = l
	if m.ClassifyId != 0 {
		n += 1 + sovUsertag(uint64(m.ClassifyId))
	}
	l = len(m.ClassifyName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if len(m.TagList) > 0 {
		for _, e := range m.TagList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	if m.IsDel {
		n += 2
	}
	return n
}

func (m *UserOptPersonalTagExt) Size() (n int) {
	var l int
	_ = l
	if m.ClassifyId != 0 {
		n += 1 + sovUsertag(uint64(m.ClassifyId))
	}
	return n
}

func (m *GetUserTagReq) Size() (n int) {
	var l int
	_ = l
	if m.TargetUid != 0 {
		n += 1 + sovUsertag(uint64(m.TargetUid))
	}
	if m.IsNeedTagExt {
		n += 2
	}
	return n
}

func (m *GetUserTagResp) Size() (n int) {
	var l int
	_ = l
	if m.UserTaglist != nil {
		l = m.UserTaglist.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *SetUserTagReq) Size() (n int) {
	var l int
	_ = l
	if m.TargetUid != 0 {
		n += 1 + sovUsertag(uint64(m.TargetUid))
	}
	if m.TagType != 0 {
		n += 1 + sovUsertag(uint64(m.TagType))
	}
	if len(m.TagList) > 0 {
		for _, e := range m.TagList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	if m.SettingCnt != 0 {
		n += 1 + sovUsertag(uint64(m.SettingCnt))
	}
	if m.IgnoreGame {
		n += 2
	}
	return n
}

func (m *SetUserTagResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UserTagReplaceInfo) Size() (n int) {
	var l int
	_ = l
	if m.FromTagId != 0 {
		n += 1 + sovUsertag(uint64(m.FromTagId))
	}
	if m.FromTagType != 0 {
		n += 1 + sovUsertag(uint64(m.FromTagType))
	}
	if m.ToTagId != 0 {
		n += 1 + sovUsertag(uint64(m.ToTagId))
	}
	if m.ToTagType != 0 {
		n += 1 + sovUsertag(uint64(m.ToTagType))
	}
	return n
}

func (m *SetUserOneTagReq) Size() (n int) {
	var l int
	_ = l
	if m.TargetUid != 0 {
		n += 1 + sovUsertag(uint64(m.TargetUid))
	}
	if m.TagType != 0 {
		n += 1 + sovUsertag(uint64(m.TagType))
	}
	if m.OperType != 0 {
		n += 1 + sovUsertag(uint64(m.OperType))
	}
	if m.TagBase != nil {
		l = m.TagBase.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.TagReplace != nil {
		l = m.TagReplace.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.TagSortValue != 0 {
		n += 1 + sovUsertag(uint64(m.TagSortValue))
	}
	if m.AppId != 0 {
		n += 1 + sovUsertag(uint64(m.AppId))
	}
	if m.MarketId != 0 {
		n += 1 + sovUsertag(uint64(m.MarketId))
	}
	if m.JustSetGamenick {
		n += 2
	}
	return n
}

func (m *SetUserOneTagResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatGetUserTagReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		l = 0
		for _, e := range m.UidList {
			l += sovUsertag(uint64(e))
		}
		n += 1 + sovUsertag(uint64(l)) + l
	}
	if m.IsNeedTagExt {
		n += 2
	}
	return n
}

func (m *BatGetUserTagResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UsertaglistList) > 0 {
		for _, e := range m.UsertaglistList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *GetTagConfigListReq) Size() (n int) {
	var l int
	_ = l
	if m.TagType != 0 {
		n += 1 + sovUsertag(uint64(m.TagType))
	}
	return n
}

func (m *GetTagConfigListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TagList) > 0 {
		for _, e := range m.TagList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	if len(m.GameIdList) > 0 {
		l = 0
		for _, e := range m.GameIdList {
			l += sovUsertag(uint64(e))
		}
		n += 1 + sovUsertag(uint64(l)) + l
	}
	return n
}

func (m *CreateTagConfigReq) Size() (n int) {
	var l int
	_ = l
	if m.TagType != 0 {
		n += 1 + sovUsertag(uint64(m.TagType))
	}
	if m.Tag != nil {
		l = m.Tag.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *CreateTagConfigResp) Size() (n int) {
	var l int
	_ = l
	if m.TagId != 0 {
		n += 1 + sovUsertag(uint64(m.TagId))
	}
	return n
}

func (m *ModifyTagConfigReq) Size() (n int) {
	var l int
	_ = l
	if m.TagId != 0 {
		n += 1 + sovUsertag(uint64(m.TagId))
	}
	if m.Tag != nil {
		l = m.Tag.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *ModifyTagConfigResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelTagConfigReq) Size() (n int) {
	var l int
	_ = l
	if m.TagId != 0 {
		n += 1 + sovUsertag(uint64(m.TagId))
	}
	if m.IsRecover {
		n += 2
	}
	return n
}

func (m *DelTagConfigResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SortTagConfigReq) Size() (n int) {
	var l int
	_ = l
	if m.TagType != 0 {
		n += 1 + sovUsertag(uint64(m.TagType))
	}
	if len(m.TagIdList) > 0 {
		l = 0
		for _, e := range m.TagIdList {
			l += sovUsertag(uint64(e))
		}
		n += 1 + sovUsertag(uint64(l)) + l
	}
	if m.PersonaltypeClassifyId != 0 {
		n += 1 + sovUsertag(uint64(m.PersonaltypeClassifyId))
	}
	return n
}

func (m *SortTagConfigResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CreateFindWhoTypeTagConfigReq) Size() (n int) {
	var l int
	_ = l
	if m.FindwhoType != 0 {
		n += 1 + sovUsertag(uint64(m.FindwhoType))
	}
	l = len(m.TagName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.AssignSex != 0 {
		n += 1 + sovUsertag(uint64(m.AssignSex))
	}
	l = len(m.CorrelationTagName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *CreateFindWhoTypeTagConfigResp) Size() (n int) {
	var l int
	_ = l
	if m.FindwhoType != 0 {
		n += 1 + sovUsertag(uint64(m.FindwhoType))
	}
	if m.TagId != 0 {
		n += 1 + sovUsertag(uint64(m.TagId))
	}
	if m.CorrelationTagId != 0 {
		n += 1 + sovUsertag(uint64(m.CorrelationTagId))
	}
	return n
}

func (m *CreateOptPersonalTagClassifyReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.ClassifyName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *CreateOptPersonalTagClassifyResp) Size() (n int) {
	var l int
	_ = l
	if m.ClassifyId != 0 {
		n += 1 + sovUsertag(uint64(m.ClassifyId))
	}
	return n
}

func (m *GetOptPersonalTagClassifyListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetOptPersonalTagClassifyListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ClassifyList) > 0 {
		for _, e := range m.ClassifyList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *GetOptPersonalTagByClassifyReq) Size() (n int) {
	var l int
	_ = l
	if m.ClassifyId != 0 {
		n += 1 + sovUsertag(uint64(m.ClassifyId))
	}
	return n
}

func (m *GetOptPersonalTagByClassifyResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TagList) > 0 {
		for _, e := range m.TagList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *ModifyOptPersonalTagClassifyNameReq) Size() (n int) {
	var l int
	_ = l
	if m.ClassifyId != 0 {
		n += 1 + sovUsertag(uint64(m.ClassifyId))
	}
	l = len(m.ClassifyName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *ModifyOptPersonalTagClassifyNameResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SortOptPersonalTagClassifyReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ClassifyIdList) > 0 {
		l = 0
		for _, e := range m.ClassifyIdList {
			l += sovUsertag(uint64(e))
		}
		n += 1 + sovUsertag(uint64(l)) + l
	}
	return n
}

func (m *SortOptPersonalTagClassifyResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelOptPersonalTagClassifyReq) Size() (n int) {
	var l int
	_ = l
	if m.ClassifyId != 0 {
		n += 1 + sovUsertag(uint64(m.ClassifyId))
	}
	if m.IsRecover {
		n += 2
	}
	return n
}

func (m *DelOptPersonalTagClassifyResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserGameTagReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUsertag(uint64(m.Uid))
	}
	l = len(m.GameName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *GetUserGameTagResp) Size() (n int) {
	var l int
	_ = l
	if m.GameExt != nil {
		l = m.GameExt.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *GetUserGameTagDanReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		l = 0
		for _, e := range m.UidList {
			l += sovUsertag(uint64(e))
		}
		n += 1 + sovUsertag(uint64(l)) + l
	}
	l = len(m.GameName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *GameTagDan) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUsertag(uint64(m.Uid))
	}
	l = len(m.GameName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameDan)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *GetUserGameTagDanResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GameDanList) > 0 {
		for _, e := range m.GameDanList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *GetSimpleGameTagReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		l = 0
		for _, e := range m.UidList {
			l += sovUsertag(uint64(e))
		}
		n += 1 + sovUsertag(uint64(l)) + l
	}
	l = len(m.GameName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *GetSimpleGameTagResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GameExtList) > 0 {
		for _, e := range m.GameExtList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *BatGetSimpleGameTagReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		l = 0
		for _, e := range m.UidList {
			l += sovUsertag(uint64(e))
		}
		n += 1 + sovUsertag(uint64(l)) + l
	}
	if len(m.GameName) > 0 {
		for _, s := range m.GameName {
			l = len(s)
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *BatGetSimpleGameTagResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GameExtList) > 0 {
		for _, e := range m.GameExtList {
			l = e.Size()
			n += 1 + l + sovUsertag(uint64(l))
		}
	}
	return n
}

func (m *NotifyChannelPlayChangeReq) Size() (n int) {
	var l int
	_ = l
	if m.ChannelId != 0 {
		n += 1 + sovUsertag(uint64(m.ChannelId))
	}
	if m.PlayType != 0 {
		n += 1 + sovUsertag(uint64(m.PlayType))
	}
	l = len(m.PlayName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *NotifyChannelPlayChangeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetUserGameNickReq) Size() (n int) {
	var l int
	_ = l
	if m.TargetUid != 0 {
		n += 1 + sovUsertag(uint64(m.TargetUid))
	}
	if m.TagType != 0 {
		n += 1 + sovUsertag(uint64(m.TagType))
	}
	if m.OperType != 0 {
		n += 1 + sovUsertag(uint64(m.OperType))
	}
	if m.TagBase != nil {
		l = m.TagBase.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.AppId != 0 {
		n += 1 + sovUsertag(uint64(m.AppId))
	}
	if m.MarketId != 0 {
		n += 1 + sovUsertag(uint64(m.MarketId))
	}
	return n
}

func (m *SetUserGameNickResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetUserGameScreenShotReq) Size() (n int) {
	var l int
	_ = l
	if m.TargetUid != 0 {
		n += 1 + sovUsertag(uint64(m.TargetUid))
	}
	if m.TagType != 0 {
		n += 1 + sovUsertag(uint64(m.TagType))
	}
	if m.OperType != 0 {
		n += 1 + sovUsertag(uint64(m.OperType))
	}
	if m.TagId != 0 {
		n += 1 + sovUsertag(uint64(m.TagId))
	}
	if m.GameScreenshot != nil {
		l = m.GameScreenshot.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.AppId != 0 {
		n += 1 + sovUsertag(uint64(m.AppId))
	}
	if m.MarketId != 0 {
		n += 1 + sovUsertag(uint64(m.MarketId))
	}
	return n
}

func (m *SetUserGameScreenShotResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ChangeRecommendStatusReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUsertag(uint64(m.Uid))
	}
	if m.Status != 0 {
		n += 1 + sovUsertag(uint64(m.Status))
	}
	return n
}

func (m *ChangeRecommendStatusResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetRecommendStatusReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovUsertag(uint64(m.Uid))
	}
	return n
}

func (m *GetRecommendStatusResp) Size() (n int) {
	var l int
	_ = l
	if m.Status != 0 {
		n += 1 + sovUsertag(uint64(m.Status))
	}
	return n
}

func (m *SetMosaicImageContext) Size() (n int) {
	var l int
	_ = l
	if m.GameScreenshotReq != nil {
		l = m.GameScreenshotReq.Size()
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.GameName)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	return n
}

func (m *SetMosaicImageReq) Size() (n int) {
	var l int
	_ = l
	if m.Result {
		n += 2
	}
	l = len(m.MosaicImageUrl)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	l = len(m.Context)
	if l > 0 {
		n += 1 + l + sovUsertag(uint64(l))
	}
	if m.Source {
		n += 2
	}
	return n
}

func (m *SetMosaicImageResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovUsertag(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozUsertag(x uint64) (n int) {
	return sovUsertag(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *UserTagBase) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserTagBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserTagBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagInfo = append(m.TagInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.TagInfo == nil {
				m.TagInfo = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDel = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserTagList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserTagList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserTagList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagList = append(m.TagList, &UserTagBase{})
			if err := m.TagList[len(m.TagList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserAgeTagExt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserAgeTagExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserAgeTagExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AgeYearMin", wireType)
			}
			m.AgeYearMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AgeYearMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AgeYearMax", wireType)
			}
			m.AgeYearMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AgeYearMax |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserFindWhoTagExt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserFindWhoTagExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserFindWhoTagExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindwhoType", wireType)
			}
			m.FindwhoType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FindwhoType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AssignSex", wireType)
			}
			m.AssignSex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AssignSex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CorrelationPairTagId", wireType)
			}
			m.CorrelationPairTagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CorrelationPairTagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameOptSecondProp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameOptSecondProp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameOptSecondProp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptProp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OptProp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValueList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ValueList = append(m.ValueList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserGameTagOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserGameTagOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserGameTagOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OptName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptId", wireType)
			}
			m.OptId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsSupportMutiSet", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsSupportMutiSet = bool(v != 0)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValueConfList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ValueConfList = append(m.ValueConfList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValueUsersetList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ValueUsersetList = append(m.ValueUsersetList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SupportMutiSetCnt", wireType)
			}
			m.SupportMutiSetCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportMutiSetCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PartitionId", wireType)
			}
			m.PartitionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PartitionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PropValueList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PropValueList = append(m.PropValueList, &GameOptSecondProp{})
			if err := m.PropValueList[len(m.PropValueList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameScreenShot) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameScreenShot: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameScreenShot: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuditStatus", wireType)
			}
			m.AuditStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AuditStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgUrlInvisibleGamenick", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgUrlInvisibleGamenick = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginAuditTime", wireType)
			}
			m.BeginAuditTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginAuditTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UploadTime", wireType)
			}
			m.UploadTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UploadTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserGameTagExt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserGameTagExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserGameTagExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BackImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BackImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OptList = append(m.OptList, &UserGameTagOpt{})
			if err := m.OptList[len(m.OptList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThumbImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ThumbImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameScreenshot", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GameScreenshot == nil {
				m.GameScreenshot = &GameScreenShot{}
			}
			if err := m.GameScreenshot.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameScreenshotList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameScreenshotList = append(m.GameScreenshotList, &GameScreenShot{})
			if err := m.GameScreenshotList[len(m.GameScreenshotList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LevelImg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LevelImg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LevelImg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LevelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfGameTagOpt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfGameTagOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfGameTagOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OptName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptId", wireType)
			}
			m.OptId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsSupportMutiSet", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsSupportMutiSet = bool(v != 0)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValueConfList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ValueConfList = append(m.ValueConfList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValueUsersetList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ValueUsersetList = append(m.ValueUsersetList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SupportMutiSetCnt", wireType)
			}
			m.SupportMutiSetCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupportMutiSetCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PartitionId", wireType)
			}
			m.PartitionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PartitionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PropValueList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PropValueList = append(m.PropValueList, &GameOptSecondProp{})
			if err := m.PropValueList[len(m.PropValueList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsMust", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMust = bool(v != 0)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TextBoxStyle", wireType)
			}
			m.TextBoxStyle = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TextBoxStyle |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfGameTagExt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfGameTagExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfGameTagExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BackImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BackImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OptList = append(m.OptList, &ConfGameTagOpt{})
			if err := m.OptList[len(m.OptList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThumbImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ThumbImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameScreenshot", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GameScreenshot == nil {
				m.GameScreenshot = &GameScreenShot{}
			}
			if err := m.GameScreenshot.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameCardImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameCardImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameBackImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameBackImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIconImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameIconImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameCornerMarkImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameCornerMarkImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameNoLevelImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameNoLevelImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelImgUrl", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LevelImgUrl = append(m.LevelImgUrl, &LevelImg{})
			if err := m.LevelImgUrl[len(m.LevelImgUrl)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicLevelImgUrl", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MicLevelImgUrl = append(m.MicLevelImgUrl, &LevelImg{})
			if err := m.MicLevelImgUrl[len(m.MicLevelImgUrl)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameBackImgMini", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameBackImgMini = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameBackColorNum", wireType)
			}
			m.GameBackColorNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameBackColorNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameExtraOptSwitch", wireType)
			}
			m.GameExtraOptSwitch = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameExtraOptSwitch |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelImgUrlNew", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LevelImgUrlNew = append(m.LevelImgUrlNew, &LevelImg{})
			if err := m.LevelImgUrlNew[len(m.LevelImgUrlNew)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameNoLevelImgUrlNew", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameNoLevelImgUrlNew = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SimpleGameTagList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleGameTagList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleGameTagList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtList = append(m.ExtList, &SimpleGameTagExt{})
			if err := m.ExtList[len(m.ExtList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SimpleGameTagExt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleGameTagExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleGameTagExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameArea", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameArea = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameDan", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameDan = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameDanUrlForMic", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameDanUrlForMic = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameRole", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameRole = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GamePosition", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GamePosition = append(m.GamePosition, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameHeroList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameHeroList = append(m.GameHeroList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameScreenshot", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameScreenshot = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameStyle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameStyle = append(m.GameStyle, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameLevelUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameLevelUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCompleted", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCompleted = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserOptPersonalTagClassify) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserOptPersonalTagClassify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserOptPersonalTagClassify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyId", wireType)
			}
			m.ClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClassifyName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagList = append(m.TagList, &UserTagBase{})
			if err := m.TagList[len(m.TagList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDel = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserOptPersonalTagExt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserOptPersonalTagExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserOptPersonalTagExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyId", wireType)
			}
			m.ClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserTagReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedTagExt", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedTagExt = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserTagResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserTaglist", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserTaglist == nil {
				m.UserTaglist = &UserTagList{}
			}
			if err := m.UserTaglist.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserTagReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagList = append(m.TagList, &UserTagBase{})
			if err := m.TagList[len(m.TagList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SettingCnt", wireType)
			}
			m.SettingCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SettingCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IgnoreGame", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IgnoreGame = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserTagResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserTagReplaceInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserTagReplaceInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserTagReplaceInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromTagId", wireType)
			}
			m.FromTagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromTagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromTagType", wireType)
			}
			m.FromTagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromTagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToTagId", wireType)
			}
			m.ToTagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToTagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToTagType", wireType)
			}
			m.ToTagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToTagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserOneTagReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserOneTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserOneTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagBase", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TagBase == nil {
				m.TagBase = &UserTagBase{}
			}
			if err := m.TagBase.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagReplace", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TagReplace == nil {
				m.TagReplace = &UserTagReplaceInfo{}
			}
			if err := m.TagReplace.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagSortValue", wireType)
			}
			m.TagSortValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagSortValue |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JustSetGamenick", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.JustSetGamenick = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserOneTagResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserOneTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserOneTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserTagReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUsertag
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUsertag
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedTagExt", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedTagExt = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserTagResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UsertaglistList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UsertaglistList = append(m.UsertaglistList, &UserTagList{})
			if err := m.UsertaglistList[len(m.UsertaglistList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagConfigListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTagConfigListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTagConfigListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagConfigListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTagConfigListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTagConfigListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagList = append(m.TagList, &UserTagBase{})
			if err := m.TagList[len(m.TagList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameIdList = append(m.GameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUsertag
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUsertag
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameIdList = append(m.GameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTagConfigReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateTagConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateTagConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Tag == nil {
				m.Tag = &UserTagBase{}
			}
			if err := m.Tag.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTagConfigResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateTagConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateTagConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyTagConfigReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyTagConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyTagConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Tag == nil {
				m.Tag = &UserTagBase{}
			}
			if err := m.Tag.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyTagConfigResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyTagConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyTagConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelTagConfigReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelTagConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelTagConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsRecover", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsRecover = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelTagConfigResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelTagConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelTagConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SortTagConfigReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SortTagConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SortTagConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TagIdList = append(m.TagIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUsertag
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUsertag
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TagIdList = append(m.TagIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagIdList", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PersonaltypeClassifyId", wireType)
			}
			m.PersonaltypeClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PersonaltypeClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SortTagConfigResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SortTagConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SortTagConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateFindWhoTypeTagConfigReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateFindWhoTypeTagConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateFindWhoTypeTagConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindwhoType", wireType)
			}
			m.FindwhoType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FindwhoType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AssignSex", wireType)
			}
			m.AssignSex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AssignSex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CorrelationTagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CorrelationTagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateFindWhoTypeTagConfigResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateFindWhoTypeTagConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateFindWhoTypeTagConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindwhoType", wireType)
			}
			m.FindwhoType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FindwhoType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CorrelationTagId", wireType)
			}
			m.CorrelationTagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CorrelationTagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateOptPersonalTagClassifyReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateOptPersonalTagClassifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateOptPersonalTagClassifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClassifyName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateOptPersonalTagClassifyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateOptPersonalTagClassifyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateOptPersonalTagClassifyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyId", wireType)
			}
			m.ClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOptPersonalTagClassifyListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOptPersonalTagClassifyListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOptPersonalTagClassifyListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOptPersonalTagClassifyListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOptPersonalTagClassifyListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOptPersonalTagClassifyListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClassifyList = append(m.ClassifyList, &UserOptPersonalTagClassify{})
			if err := m.ClassifyList[len(m.ClassifyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOptPersonalTagByClassifyReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOptPersonalTagByClassifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOptPersonalTagByClassifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyId", wireType)
			}
			m.ClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOptPersonalTagByClassifyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOptPersonalTagByClassifyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOptPersonalTagByClassifyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagList = append(m.TagList, &UserTagBase{})
			if err := m.TagList[len(m.TagList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyOptPersonalTagClassifyNameReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyOptPersonalTagClassifyNameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyOptPersonalTagClassifyNameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyId", wireType)
			}
			m.ClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClassifyName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyOptPersonalTagClassifyNameResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyOptPersonalTagClassifyNameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyOptPersonalTagClassifyNameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SortOptPersonalTagClassifyReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SortOptPersonalTagClassifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SortOptPersonalTagClassifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ClassifyIdList = append(m.ClassifyIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUsertag
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUsertag
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ClassifyIdList = append(m.ClassifyIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SortOptPersonalTagClassifyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SortOptPersonalTagClassifyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SortOptPersonalTagClassifyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelOptPersonalTagClassifyReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelOptPersonalTagClassifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelOptPersonalTagClassifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClassifyId", wireType)
			}
			m.ClassifyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClassifyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsRecover", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsRecover = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelOptPersonalTagClassifyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelOptPersonalTagClassifyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelOptPersonalTagClassifyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserGameTagReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserGameTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserGameTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserGameTagResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserGameTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserGameTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameExt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GameExt == nil {
				m.GameExt = &UserGameTagExt{}
			}
			if err := m.GameExt.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserGameTagDanReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserGameTagDanReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserGameTagDanReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUsertag
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUsertag
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameTagDan) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameTagDan: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameTagDan: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameDan", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameDan = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserGameTagDanResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserGameTagDanResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserGameTagDanResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameDanList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameDanList = append(m.GameDanList, &GameTagDan{})
			if err := m.GameDanList[len(m.GameDanList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSimpleGameTagReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSimpleGameTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSimpleGameTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUsertag
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUsertag
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSimpleGameTagResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSimpleGameTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSimpleGameTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameExtList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameExtList = append(m.GameExtList, &SimpleGameTagExt{})
			if err := m.GameExtList[len(m.GameExtList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetSimpleGameTagReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetSimpleGameTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetSimpleGameTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUsertag
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUsertag
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = append(m.GameName, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetSimpleGameTagResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetSimpleGameTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetSimpleGameTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameExtList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameExtList = append(m.GameExtList, &SimpleGameTagList{})
			if err := m.GameExtList[len(m.GameExtList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyChannelPlayChangeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyChannelPlayChangeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyChannelPlayChangeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PlayType", wireType)
			}
			m.PlayType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PlayType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PlayName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PlayName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyChannelPlayChangeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyChannelPlayChangeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyChannelPlayChangeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserGameNickReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserGameNickReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserGameNickReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagBase", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TagBase == nil {
				m.TagBase = &UserTagBase{}
			}
			if err := m.TagBase.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserGameNickResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserGameNickResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserGameNickResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserGameScreenShotReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserGameScreenShotReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserGameScreenShotReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameScreenshot", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GameScreenshot == nil {
				m.GameScreenshot = &GameScreenShot{}
			}
			if err := m.GameScreenshot.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserGameScreenShotResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserGameScreenShotResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserGameScreenShotResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeRecommendStatusReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChangeRecommendStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChangeRecommendStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeRecommendStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChangeRecommendStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChangeRecommendStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendStatusReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetMosaicImageContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetMosaicImageContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetMosaicImageContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameScreenshotReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GameScreenshotReq == nil {
				m.GameScreenshotReq = &SetUserGameScreenShotReq{}
			}
			if err := m.GameScreenshotReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetMosaicImageReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetMosaicImageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetMosaicImageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Result = bool(v != 0)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MosaicImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MosaicImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthUsertag
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Context = append(m.Context[:0], dAtA[iNdEx:postIndex]...)
			if m.Context == nil {
				m.Context = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Source = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetMosaicImageResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetMosaicImageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetMosaicImageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsertag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsertag
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipUsertag(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUsertag
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUsertag
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthUsertag
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowUsertag
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipUsertag(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthUsertag = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUsertag   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/userTagSvr/usertag.proto", fileDescriptorUsertag) }

var fileDescriptorUsertag = []byte{
	// 3764 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0xcd, 0x6f, 0x1b, 0xc9,
	0x72, 0xf7, 0x88, 0xfa, 0x62, 0x51, 0x94, 0xa8, 0x96, 0x6c, 0x51, 0xd4, 0x17, 0x3d, 0xeb, 0xf5,
	0xca, 0x5e, 0xdb, 0xf2, 0x3a, 0x59, 0xec, 0x83, 0xe3, 0x28, 0xd1, 0x07, 0x57, 0x66, 0x62, 0x51,
	0x0a, 0x49, 0xed, 0xbe, 0x4d, 0x80, 0x0c, 0xc6, 0x9c, 0x16, 0xdd, 0xcf, 0xe4, 0xcc, 0xbc, 0xe9,
	0xa1, 0x4d, 0x21, 0x48, 0x90, 0x5c, 0xb2, 0xef, 0x05, 0x0f, 0x48, 0x10, 0xbc, 0x4b, 0x0e, 0x79,
	0xc8, 0x61, 0x4f, 0x39, 0xe4, 0x94, 0x43, 0x0e, 0xef, 0x0f, 0xc8, 0x71, 0x2f, 0x39, 0x05, 0x48,
	0x82, 0xcd, 0x65, 0x91, 0x7f, 0x20, 0xb7, 0x20, 0xe8, 0xea, 0x19, 0x4e, 0xcf, 0x90, 0x43, 0xd1,
	0xd9, 0x2c, 0x90, 0x43, 0x6e, 0x9a, 0xae, 0xea, 0xaa, 0x5f, 0x55, 0x57, 0x75, 0x57, 0x15, 0x05,
	0x9b, 0xdc, 0x6b, 0xed, 0xf5, 0x38, 0xf5, 0x9a, 0x66, 0xbb, 0xf1, 0xc6, 0xc3, 0x3f, 0x7d, 0xb3,
	0xfd, 0xc8, 0xf5, 0x1c, 0xdf, 0x21, 0x73, 0xc1, 0x67, 0xe9, 0x4e, 0xcb, 0xe9, 0x76, 0x1d, 0x7b,
	0xcf, 0xef, 0xbc, 0x71, 0x59, 0xeb, 0x75, 0x87, 0xee, 0xf1, 0xd7, 0x2f, 0x7b, 0xac, 0xe3, 0x33,
	0xdb, 0xbf, 0x72, 0xa9, 0x64, 0xd7, 0x7f, 0xa6, 0x41, 0xee, 0x42, 0xca, 0x3a, 0x34, 0x39, 0x25,
	0xeb, 0x30, 0xef, 0x9b, 0x6d, 0x43, 0x70, 0x14, 0xb5, 0xb2, 0xb6, 0x9b, 0xaf, 0xcf, 0xf9, 0x66,
	0xbb, 0x79, 0xe5, 0x0e, 0x48, 0xb6, 0xd9, 0xa5, 0xc5, 0xa9, 0xb2, 0xb6, 0x9b, 0x45, 0x52, 0xcd,
	0xec, 0x52, 0x72, 0x13, 0x66, 0x05, 0x89, 0x59, 0xc5, 0x0c, 0xee, 0x99, 0xf1, 0xcd, 0x76, 0xd5,
	0x0a, 0x77, 0x30, 0xfb, 0xd2, 0x29, 0x4e, 0x97, 0xb5, 0xdd, 0x05, 0xdc, 0x51, 0xb5, 0x2f, 0x1d,
	0xb1, 0x83, 0x71, 0xc3, 0xa2, 0x9d, 0xe2, 0x4c, 0x59, 0xdb, 0x9d, 0xaf, 0xcf, 0x30, 0x7e, 0x4c,
	0x3b, 0xfa, 0xf9, 0x00, 0xcd, 0x0b, 0xc6, 0x7d, 0x52, 0x80, 0x4c, 0x8f, 0x59, 0x01, 0x10, 0xf1,
	0x27, 0xd9, 0x93, 0x22, 0x3b, 0x8c, 0xfb, 0xc5, 0xa9, 0x72, 0x66, 0x37, 0xf7, 0x64, 0xf5, 0x51,
	0xe8, 0x00, 0xc5, 0x0e, 0x54, 0x24, 0x44, 0xe8, 0x0d, 0xc8, 0x8b, 0xf5, 0x83, 0x36, 0x6d, 0x9a,
	0xed, 0x4a, 0xdf, 0x27, 0x65, 0x58, 0x30, 0xdb, 0xd4, 0xb8, 0xa2, 0xa6, 0x67, 0x74, 0x99, 0x1d,
	0x08, 0x07, 0xb3, 0x4d, 0xbf, 0xa0, 0xa6, 0x77, 0xca, 0xec, 0x38, 0x87, 0xd9, 0x47, 0x63, 0x15,
	0x0e, 0xb3, 0x2f, 0xbc, 0xb6, 0x2c, 0xa4, 0x7e, 0xca, 0x6c, 0xeb, 0xf3, 0x57, 0x4e, 0x20, 0xf9,
	0x36, 0x2c, 0x5c, 0x32, 0xdb, 0x7a, 0xfb, 0xca, 0x51, 0xfd, 0x97, 0x0b, 0xd6, 0xd0, 0x87, 0x5b,
	0x00, 0x26, 0xe7, 0xac, 0x6d, 0x1b, 0x9c, 0x86, 0x82, 0xb3, 0x72, 0xa5, 0x41, 0xfb, 0xe4, 0x63,
	0x58, 0x6b, 0x39, 0x9e, 0x47, 0x3b, 0xa6, 0xcf, 0x1c, 0xdb, 0x70, 0x4d, 0xe6, 0x19, 0x31, 0xc7,
	0xae, 0x2a, 0xe4, 0x73, 0x93, 0x09, 0x73, 0xab, 0x96, 0x7e, 0x0a, 0xcb, 0x27, 0x66, 0x97, 0x9e,
	0xb9, 0x7e, 0x83, 0xb6, 0x1c, 0xdb, 0x3a, 0xf7, 0x1c, 0x57, 0x38, 0xdf, 0x71, 0x7d, 0xc3, 0xf5,
	0x1c, 0x17, 0x91, 0x64, 0xeb, 0x73, 0x8e, 0xeb, 0x23, 0x69, 0x0b, 0xe0, 0x8d, 0xd9, 0xe9, 0xd1,
	0xc8, 0x8d, 0xd9, 0x7a, 0x16, 0x57, 0xd0, 0x65, 0xff, 0x3a, 0x05, 0x8b, 0xc2, 0x3a, 0x21, 0xb3,
	0x69, 0xb6, 0xcf, 0x5c, 0x3f, 0x14, 0x86, 0x67, 0x1f, 0x09, 0x0b, 0xcf, 0x5e, 0x90, 0x98, 0x15,
	0x98, 0x33, 0xe3, 0xb8, 0x7e, 0xd5, 0x22, 0x0f, 0x61, 0x85, 0x71, 0x83, 0xf7, 0x5c, 0xd7, 0xf1,
	0x7c, 0xa3, 0xdb, 0xf3, 0x99, 0xc1, 0xa9, 0x8f, 0x66, 0xcc, 0xd7, 0x0b, 0x8c, 0x37, 0x24, 0xe5,
	0xb4, 0xe7, 0xb3, 0x06, 0xf5, 0xc9, 0x5d, 0x58, 0x92, 0x90, 0x5a, 0x8e, 0x7d, 0x29, 0x71, 0x4d,
	0x23, 0xae, 0x3c, 0x2e, 0x1f, 0x39, 0xf6, 0x25, 0x46, 0xc4, 0x03, 0x20, 0x92, 0x4f, 0x1c, 0x3a,
	0xa7, 0xbe, 0x64, 0x9d, 0x41, 0xd6, 0x02, 0x52, 0x2e, 0x24, 0x01, 0xb9, 0xf7, 0x60, 0x35, 0x89,
	0xc0, 0x68, 0xd9, 0x7e, 0x71, 0x16, 0x91, 0x2e, 0xf3, 0x18, 0x86, 0x23, 0x1b, 0x8f, 0xd0, 0x35,
	0x3d, 0x9f, 0xa1, 0xfb, 0x99, 0x55, 0x9c, 0x93, 0x47, 0x38, 0x58, 0xab, 0x5a, 0xe4, 0x10, 0x96,
	0x84, 0x4f, 0x0d, 0xc5, 0x83, 0xf3, 0x18, 0x88, 0xa5, 0x41, 0x20, 0x0e, 0x1d, 0x46, 0x3d, 0x2f,
	0xb6, 0x7c, 0x36, 0xf0, 0xf0, 0x7f, 0x68, 0xb0, 0x28, 0x98, 0x1a, 0x2d, 0x8f, 0x52, 0xbb, 0xf1,
	0xca, 0x41, 0xcd, 0x66, 0xcf, 0x62, 0xbe, 0xc1, 0x7d, 0xd3, 0xef, 0xf1, 0x30, 0x78, 0x70, 0xad,
	0x81, 0x4b, 0x64, 0x0d, 0xe6, 0x58, 0xb7, 0x6d, 0xf4, 0xbc, 0x4e, 0x90, 0x7f, 0xb3, 0xac, 0xdb,
	0xbe, 0xf0, 0x3a, 0xe4, 0xd7, 0xa0, 0x14, 0x10, 0x0c, 0x66, 0xbf, 0x61, 0x9c, 0xbd, 0xec, 0x50,
	0xa3, 0x6d, 0x76, 0xa9, 0xcd, 0x5a, 0xaf, 0xd1, 0xe5, 0xd9, 0xfa, 0x9a, 0xe4, 0xad, 0x86, 0xf4,
	0x93, 0x80, 0x4c, 0x76, 0xa1, 0xf0, 0x92, 0xb6, 0x99, 0x6d, 0x48, 0xf5, 0x3e, 0xeb, 0x52, 0x4c,
	0xd6, 0x7c, 0x7d, 0x11, 0xd7, 0x0f, 0xc4, 0x72, 0x93, 0x75, 0x29, 0x59, 0x85, 0x19, 0x66, 0x5b,
	0xb4, 0x8f, 0x29, 0x9b, 0xaf, 0xcb, 0x0f, 0xb2, 0x03, 0xb9, 0x9e, 0xdb, 0x71, 0x4c, 0x4b, 0x6e,
	0x95, 0xae, 0x05, 0xb9, 0x24, 0xb6, 0xe9, 0xff, 0x14, 0x0f, 0x27, 0x91, 0x29, 0x6b, 0x30, 0x27,
	0xe0, 0x19, 0x83, 0xdc, 0x9e, 0x15, 0x9f, 0x55, 0x4b, 0xa4, 0xde, 0x4b, 0xb3, 0xf5, 0xda, 0x88,
	0xdb, 0x09, 0x62, 0xad, 0x2a, 0x6d, 0x7d, 0x22, 0x23, 0x11, 0xfd, 0x9e, 0x41, 0xbf, 0xaf, 0xc5,
	0x2e, 0x80, 0x28, 0x68, 0x31, 0x44, 0x31, 0x0c, 0x74, 0xc8, 0xfb, 0xaf, 0x7a, 0xdd, 0x97, 0x03,
	0xb1, 0xd3, 0x28, 0x36, 0x87, 0x8b, 0x81, 0xdc, 0xf7, 0x20, 0x8f, 0x90, 0x84, 0x4f, 0x30, 0xcc,
	0x67, 0x90, 0x67, 0x41, 0x2c, 0xd6, 0x82, 0x35, 0xf2, 0x9b, 0xb0, 0x84, 0x4c, 0x1c, 0xcf, 0x8d,
	0xbf, 0x72, 0x64, 0x28, 0xa9, 0x18, 0xe2, 0xc7, 0x5a, 0x5f, 0x6c, 0x0f, 0xbe, 0x05, 0x3b, 0xa9,
	0xc2, 0x6a, 0x42, 0x82, 0x34, 0x65, 0x2e, 0x61, 0x4a, 0x42, 0x0c, 0x89, 0x8b, 0xc1, 0x20, 0x3a,
	0x84, 0xf9, 0x17, 0xf4, 0x0d, 0xed, 0x54, 0xbb, 0x6d, 0x91, 0xd1, 0x1d, 0xf1, 0xb7, 0x9a, 0xa1,
	0x59, 0x5c, 0xc1, 0x1c, 0x4d, 0x8b, 0x1c, 0xfd, 0x17, 0x19, 0x58, 0x14, 0xb9, 0xf5, 0xff, 0xa9,
	0xfe, 0x3d, 0xa5, 0x3a, 0xba, 0x9e, 0x1b, 0xdd, 0x1e, 0xf7, 0x8b, 0x59, 0x74, 0xc8, 0x2c, 0xe3,
	0xa7, 0x3d, 0xee, 0x93, 0x3b, 0xb0, 0xe8, 0xd3, 0xbe, 0x6f, 0xbc, 0x74, 0xfa, 0x06, 0xf7, 0xaf,
	0x3a, 0xb4, 0x08, 0x88, 0x60, 0x41, 0xac, 0x1e, 0x3a, 0xfd, 0x86, 0x58, 0xd3, 0xff, 0x79, 0x36,
	0x76, 0x40, 0xdf, 0x63, 0xf2, 0xc4, 0xc3, 0xe0, 0xff, 0x64, 0xf2, 0xdc, 0x83, 0x65, 0x94, 0xd0,
	0x32, 0x3d, 0x6b, 0x00, 0x67, 0x0e, 0x55, 0x21, 0xeb, 0x91, 0xe9, 0x59, 0x01, 0x22, 0x3d, 0x40,
	0x14, 0x3a, 0xa4, 0x38, 0x2f, 0x51, 0x8b, 0xc5, 0x43, 0xe9, 0x90, 0x01, 0x0f, 0x6b, 0x89, 0x08,
	0xe8, 0xb6, 0xf1, 0x80, 0x02, 0x9e, 0x6a, 0xcb, 0xb1, 0x05, 0xcf, 0x5e, 0x90, 0xaf, 0x2d, 0xc7,
	0xb3, 0xa9, 0x28, 0x07, 0x3c, 0x29, 0x0e, 0x90, 0x15, 0xe1, 0x1c, 0x21, 0xe9, 0xd4, 0xf4, 0x50,
	0xe8, 0x63, 0xb8, 0x29, 0x5d, 0xe1, 0x18, 0x32, 0x23, 0x43, 0x9c, 0xb9, 0x68, 0x47, 0xcd, 0x09,
	0x13, 0x57, 0x40, 0xfd, 0x18, 0xf2, 0x71, 0xce, 0x05, 0x3c, 0x99, 0xe5, 0x81, 0x57, 0x42, 0xe6,
	0x7a, 0xae, 0xa3, 0x6c, 0x7b, 0x06, 0xcb, 0x5d, 0xd6, 0x4a, 0x28, 0xc9, 0xa7, 0x6d, 0x5d, 0xec,
	0xb2, 0x96, 0xaa, 0xf4, 0x43, 0x20, 0x31, 0xff, 0x88, 0x52, 0x88, 0x15, 0x17, 0x11, 0xe3, 0x92,
	0xe2, 0xa4, 0x53, 0x66, 0x33, 0x91, 0xe0, 0x11, 0x73, 0xcb, 0xe9, 0x38, 0x9e, 0x61, 0xf7, 0xba,
	0xc5, 0x25, 0x8c, 0xbe, 0x42, 0xc8, 0x7d, 0x24, 0x08, 0xb5, 0x5e, 0x97, 0x7c, 0x14, 0xb8, 0x80,
	0xf6, 0x7d, 0xcf, 0x34, 0x44, 0xc0, 0xf1, 0xb7, 0xcc, 0x6f, 0xbd, 0x2a, 0x16, 0x70, 0x03, 0x2a,
	0xae, 0x08, 0x9a, 0x48, 0x1c, 0xa4, 0x08, 0x63, 0x62, 0x86, 0x18, 0x36, 0x7d, 0x5b, 0x5c, 0x4e,
	0x35, 0x46, 0xf1, 0x43, 0x8d, 0xbe, 0x25, 0x9f, 0xc0, 0xfa, 0x48, 0x9f, 0xa3, 0x14, 0x82, 0x36,
	0xad, 0x0e, 0xf9, 0xbd, 0x46, 0xdf, 0xea, 0xbf, 0x07, 0xcb, 0x0d, 0xd6, 0x75, 0xe5, 0x6b, 0x98,
	0x5e, 0x74, 0xfe, 0x2a, 0xcc, 0x8b, 0x4c, 0x55, 0x8a, 0xce, 0xf5, 0x01, 0xa8, 0xd8, 0xfe, 0x4a,
	0xdf, 0xaf, 0xcf, 0xd1, 0xbe, 0xbc, 0x9f, 0xff, 0x33, 0x03, 0x85, 0x24, 0x35, 0x3d, 0x79, 0x37,
	0x20, 0x2b, 0x6d, 0x88, 0xca, 0xeb, 0x79, 0xc4, 0x2c, 0x52, 0x67, 0x28, 0xbf, 0x32, 0x23, 0xf2,
	0x2b, 0x94, 0x60, 0x7a, 0xd4, 0x0c, 0x92, 0x14, 0x25, 0x1c, 0x78, 0xd4, 0x14, 0xb7, 0x3a, 0x12,
	0x2d, 0xd3, 0x0e, 0x92, 0x13, 0x71, 0x1c, 0x9b, 0x76, 0x68, 0xef, 0x6c, 0x64, 0xef, 0xa3, 0x20,
	0xe8, 0x2d, 0xd3, 0x46, 0x37, 0x5e, 0x3a, 0xa2, 0x54, 0x6e, 0x05, 0xa9, 0x56, 0x08, 0x36, 0x5e,
	0x78, 0x9d, 0x4f, 0x1d, 0xef, 0x94, 0xb5, 0x06, 0x9a, 0x3d, 0xa7, 0x43, 0x83, 0x44, 0x43, 0x6d,
	0x75, 0xa7, 0x13, 0x61, 0x77, 0x1d, 0x8e, 0xf7, 0x6a, 0x31, 0x8b, 0x37, 0x38, 0x62, 0x3f, 0x0f,
	0xd6, 0xc4, 0x65, 0x88, 0x4c, 0xaf, 0xa8, 0xe7, 0x48, 0x3f, 0x43, 0xc4, 0xf5, 0x9c, 0x7a, 0x0e,
	0x9e, 0xcc, 0x07, 0xc3, 0x37, 0x48, 0x2e, 0xca, 0x7e, 0xe5, 0xa2, 0xd8, 0x02, 0x90, 0x8c, 0x78,
	0xaf, 0x2e, 0xc8, 0x02, 0x17, 0x79, 0xc4, 0xc2, 0x40, 0x9b, 0x0c, 0x16, 0x99, 0x37, 0x03, 0x7f,
	0x62, 0x88, 0x88, 0x14, 0x89, 0x9a, 0x9a, 0x45, 0xb5, 0xa9, 0xb9, 0x0d, 0x0b, 0x8c, 0x1b, 0x2d,
	0x47, 0x1c, 0xac, 0x4f, 0x2d, 0xcc, 0x82, 0xf9, 0x7a, 0x8e, 0xf1, 0xa3, 0x70, 0x49, 0xff, 0x5b,
	0x0d, 0x4a, 0xe2, 0x6d, 0x3a, 0x73, 0xfd, 0x73, 0xea, 0x71, 0xc7, 0x36, 0x3b, 0x4d, 0xb3, 0x7d,
	0xd4, 0x11, 0x85, 0xfe, 0xe5, 0x95, 0xa8, 0x98, 0x5a, 0xc1, 0xdf, 0x51, 0x1c, 0x40, 0xb8, 0x54,
	0xb5, 0x84, 0xcb, 0x06, 0x0c, 0x4a, 0x3c, 0x2c, 0x84, 0x8b, 0x18, 0x13, 0x6a, 0x27, 0x94, 0x99,
	0xa0, 0x13, 0x52, 0x5a, 0xae, 0x69, 0xb5, 0xe5, 0xfa, 0x01, 0xdc, 0x1c, 0xc6, 0x2a, 0x42, 0xf5,
	0x3a, 0x98, 0xfa, 0x05, 0xe4, 0x4f, 0xa8, 0x1f, 0xe8, 0xaa, 0xd3, 0x1f, 0x0b, 0xb7, 0xfb, 0xa6,
	0xd7, 0xa6, 0xbe, 0x11, 0x25, 0x50, 0x56, 0xae, 0x5c, 0x30, 0x8b, 0xbc, 0x0f, 0x4b, 0x8c, 0x1b,
	0x36, 0xa5, 0x16, 0x36, 0x35, 0xb4, 0xef, 0xa3, 0x61, 0xf3, 0xf5, 0x05, 0xc6, 0x6b, 0x94, 0x5a,
	0x52, 0xaf, 0x5e, 0x85, 0x45, 0x55, 0x2c, 0x77, 0xc9, 0x27, 0xb0, 0x20, 0x2c, 0x13, 0xbb, 0xd0,
	0x5c, 0x0d, 0x9f, 0x8d, 0x21, 0x73, 0x85, 0x95, 0xf5, 0x5c, 0xd0, 0x19, 0x0b, 0x46, 0xfd, 0x97,
	0x1a, 0xe4, 0x1b, 0xef, 0x02, 0x51, 0x6d, 0x7f, 0xa7, 0xe2, 0xed, 0xef, 0x3b, 0xfb, 0x7b, 0x07,
	0x72, 0x9c, 0xfa, 0x3e, 0xb3, 0xdb, 0x58, 0x88, 0xc8, 0x9a, 0x1a, 0x82, 0x25, 0x51, 0x81, 0xec,
	0x40, 0x8e, 0xb5, 0x6d, 0xc7, 0x93, 0xb5, 0x7a, 0xd0, 0x08, 0x83, 0x5c, 0x12, 0x57, 0x86, 0x5e,
	0x80, 0xc5, 0x46, 0xcc, 0x13, 0xfa, 0xcf, 0x35, 0x20, 0x83, 0x6f, 0xb7, 0x63, 0xb6, 0x28, 0x76,
	0xd3, 0xdb, 0x90, 0xbb, 0xf4, 0x9c, 0x6e, 0xd8, 0x2b, 0x06, 0x66, 0x89, 0x25, 0x6c, 0x10, 0xc5,
	0x4b, 0x37, 0xa0, 0x2b, 0xb6, 0xe5, 0x02, 0x0e, 0xb4, 0xaf, 0x04, 0x59, 0xdf, 0x89, 0x77, 0x9b,
	0x73, 0xbe, 0x23, 0xf7, 0x6f, 0x43, 0x2e, 0xa0, 0xe1, 0xee, 0xe9, 0xc0, 0x6d, 0x4e, 0xb0, 0x57,
	0xff, 0x97, 0x29, 0x28, 0x04, 0x48, 0xcf, 0x6c, 0xfa, 0x9d, 0x5d, 0xbd, 0x01, 0x59, 0xc7, 0x15,
	0xe7, 0x1d, 0x29, 0x9b, 0x17, 0x0b, 0xea, 0x39, 0xbc, 0x34, 0xb9, 0x74, 0xd9, 0xb8, 0x73, 0xc0,
	0x91, 0xc6, 0x33, 0xc8, 0x89, 0x0d, 0x9e, 0xf4, 0x57, 0x50, 0x73, 0x6c, 0x24, 0xf7, 0x28, 0xee,
	0xac, 0x83, 0x3f, 0xf8, 0xc6, 0x32, 0xcd, 0x6c, 0x1b, 0x5c, 0x14, 0x96, 0x58, 0x07, 0xe2, 0x2d,
	0x38, 0x53, 0x5f, 0xf0, 0xcd, 0x76, 0xc3, 0xf1, 0x7c, 0xac, 0xf4, 0x44, 0x6e, 0x99, 0xae, 0x2b,
	0x3c, 0x37, 0x2f, 0xef, 0x0a, 0xd3, 0x75, 0xe5, 0xa5, 0x2e, 0x2a, 0x06, 0x8a, 0x35, 0x73, 0x56,
	0x1a, 0x22, 0x17, 0xaa, 0x16, 0xb9, 0x0f, 0xcb, 0x3f, 0xea, 0x71, 0x1f, 0x2b, 0xd5, 0x41, 0xb3,
	0x06, 0x18, 0x04, 0x4b, 0x82, 0xd0, 0xa0, 0x7e, 0xd8, 0xa4, 0xe9, 0x2b, 0xb0, 0x9c, 0xf0, 0x2f,
	0x77, 0xf5, 0x26, 0x14, 0x0e, 0x4d, 0x3f, 0x9e, 0x82, 0xeb, 0x30, 0xdf, 0x63, 0x96, 0x11, 0xa4,
	0x49, 0x46, 0x78, 0xb5, 0xc7, 0x2c, 0x8c, 0xc7, 0x09, 0xd3, 0xaf, 0x09, 0xcb, 0x09, 0xa9, 0xdc,
	0x25, 0xbf, 0x01, 0x85, 0xc0, 0x5f, 0x42, 0x72, 0x24, 0x3e, 0x2d, 0x0b, 0x97, 0x14, 0x6e, 0x7c,
	0x0c, 0x1f, 0xc3, 0xca, 0x09, 0xf5, 0xc5, 0x2d, 0xe8, 0xd8, 0x97, 0x4c, 0x32, 0x49, 0xb8, 0x29,
	0xe3, 0x26, 0x9d, 0xc1, 0xea, 0xf0, 0x0e, 0xee, 0xc6, 0xf2, 0x50, 0x9b, 0x24, 0x0f, 0xcb, 0xb0,
	0x10, 0x3c, 0xb9, 0xd1, 0x0b, 0x9e, 0xaf, 0x83, 0x7c, 0x77, 0x11, 0xdc, 0xe7, 0x40, 0x8e, 0x3c,
	0x6a, 0xfa, 0x74, 0xa0, 0x6d, 0x3c, 0x36, 0x72, 0x17, 0x32, 0xbe, 0xd9, 0x46, 0xf7, 0xa5, 0xa9,
	0x17, 0x0c, 0xfa, 0x03, 0x58, 0x19, 0x12, 0xcc, 0x5d, 0xe5, 0x65, 0xd1, 0x94, 0x97, 0x45, 0x6f,
	0x00, 0x39, 0x75, 0x2c, 0x76, 0x79, 0x15, 0x83, 0x31, 0x9a, 0x79, 0x62, 0x08, 0x37, 0x61, 0x65,
	0x48, 0x28, 0x77, 0xf5, 0x13, 0x58, 0x3a, 0xa6, 0x9d, 0x49, 0x14, 0x6d, 0x01, 0x30, 0x6e, 0x78,
	0xb4, 0xe5, 0xbc, 0xa1, 0x5e, 0x10, 0x31, 0x59, 0xc6, 0xeb, 0x72, 0x41, 0x27, 0x50, 0x88, 0x0b,
	0xe2, 0xae, 0xfe, 0xa5, 0x06, 0x05, 0x91, 0x1b, 0x93, 0xba, 0x73, 0x5b, 0x66, 0x68, 0xfc, 0x80,
	0xb2, 0xa8, 0x1e, 0x4f, 0xf0, 0x07, 0x50, 0x74, 0x83, 0xb7, 0x49, 0x6c, 0x37, 0xd4, 0x67, 0x49,
	0xde, 0x54, 0xb7, 0x54, 0xfa, 0x51, 0xf4, 0x44, 0x89, 0xbc, 0x89, 0x03, 0xe1, 0xae, 0xfe, 0x77,
	0x1a, 0x6c, 0xc9, 0x63, 0x09, 0xe7, 0x77, 0x57, 0x6e, 0xfc, 0xe8, 0x27, 0x98, 0xe4, 0x8d, 0x99,
	0x86, 0xc6, 0x87, 0x7c, 0x99, 0xe4, 0x90, 0xef, 0x31, 0xa8, 0x53, 0x3c, 0x63, 0x20, 0x45, 0x96,
	0x6c, 0x44, 0xa1, 0x35, 0xa5, 0x40, 0xfd, 0x27, 0x1a, 0x6c, 0x8f, 0x03, 0xcc, 0xdd, 0x49, 0x10,
	0x47, 0xe7, 0x3b, 0xa5, 0x9e, 0xef, 0x03, 0x20, 0x49, 0x38, 0x03, 0xb7, 0x16, 0xe2, 0x60, 0xaa,
	0x96, 0xfe, 0x29, 0xec, 0x48, 0x24, 0xa3, 0x6b, 0x1b, 0xe1, 0xbc, 0xa1, 0xea, 0x45, 0x1b, 0xae,
	0x5e, 0xf4, 0x23, 0x28, 0x8f, 0x97, 0xc3, 0xdd, 0xeb, 0x0b, 0x10, 0x1d, 0xca, 0x27, 0xd4, 0x1f,
	0x2d, 0x21, 0xb8, 0x61, 0xf4, 0x2e, 0xdc, 0xbe, 0x86, 0x87, 0xbb, 0xe4, 0xb9, 0x02, 0x59, 0xb9,
	0x58, 0xde, 0x8b, 0xa5, 0x55, 0x0a, 0xd2, 0x81, 0x5d, 0x78, 0x95, 0x1c, 0xc0, 0xf6, 0x90, 0xba,
	0xc3, 0x2b, 0xd5, 0x3d, 0xd7, 0x5a, 0x55, 0x87, 0x9d, 0xb1, 0x22, 0xfe, 0x07, 0x77, 0xa0, 0xfe,
	0x1a, 0xde, 0x93, 0xb7, 0xc0, 0x68, 0x23, 0xc4, 0x91, 0x4c, 0x82, 0x6d, 0xa2, 0xca, 0x54, 0xbf,
	0x0b, 0x77, 0xae, 0x57, 0xc6, 0x5d, 0xbd, 0x0a, 0x5b, 0x22, 0x39, 0xd3, 0x23, 0x69, 0x17, 0x0a,
	0x0a, 0x1c, 0xf5, 0x51, 0x5b, 0x8c, 0x30, 0xa1, 0x7d, 0x65, 0xd8, 0x1e, 0x27, 0x8a, 0xbb, 0xfa,
	0xef, 0xc3, 0xe6, 0x31, 0xed, 0xa4, 0xeb, 0xba, 0xd6, 0xf4, 0x6b, 0xee, 0xc1, 0x1d, 0xd8, 0x1a,
	0x23, 0x9f, 0xbb, 0xfa, 0x21, 0x2c, 0x07, 0x8f, 0x6a, 0xd0, 0x0e, 0x0a, 0xad, 0xc3, 0xbd, 0xe6,
	0xb8, 0x3e, 0x50, 0x7f, 0x0e, 0x24, 0x29, 0x83, 0xbb, 0xe4, 0x49, 0xd0, 0xdb, 0x89, 0x17, 0x5d,
	0x4b, 0x4c, 0x54, 0xe2, 0x83, 0x57, 0xd9, 0xf4, 0x89, 0x57, 0xbe, 0x86, 0xaf, 0xab, 0x42, 0x3d,
	0x36, 0xed, 0x6b, 0xea, 0x87, 0xb1, 0xc8, 0x3e, 0x03, 0x88, 0x04, 0xbd, 0xa3, 0x59, 0xb1, 0xe6,
	0x34, 0x13, 0x6b, 0x4e, 0xf5, 0x73, 0xb8, 0x39, 0x02, 0x27, 0xf6, 0x04, 0xf9, 0x41, 0x8f, 0xaa,
	0xe4, 0xc1, 0x4a, 0x6c, 0x96, 0x14, 0xf0, 0xe7, 0x02, 0x69, 0x18, 0x2a, 0xa7, 0x58, 0x89, 0xc4,
	0x1a, 0xf3, 0xef, 0x62, 0xf8, 0x05, 0x3a, 0x32, 0x21, 0x8e, 0xbb, 0xe4, 0xd7, 0x03, 0x7c, 0x83,
	0xc1, 0x81, 0x76, 0xdd, 0xe0, 0x20, 0x17, 0x9c, 0x0d, 0xa2, 0x3c, 0x87, 0x5b, 0xb2, 0x0a, 0xfb,
	0x0e, 0x40, 0x33, 0x31, 0xa0, 0x5f, 0xc0, 0xda, 0x48, 0x89, 0xdc, 0x25, 0xfb, 0xa3, 0xb1, 0x96,
	0x46, 0x63, 0x95, 0x6d, 0x96, 0x0a, 0xb6, 0x07, 0xa5, 0x9a, 0xe3, 0xb3, 0xcb, 0xab, 0xa3, 0x57,
	0xa6, 0x6d, 0xd3, 0xce, 0x79, 0xc7, 0xc4, 0x3f, 0xdb, 0x34, 0xe8, 0x03, 0x5a, 0x72, 0x5d, 0xe9,
	0x4d, 0x82, 0x15, 0x59, 0x23, 0xbb, 0x1d, 0xf3, 0x4a, 0x6d, 0x04, 0xe6, 0xc5, 0x42, 0xd8, 0x09,
	0x20, 0x51, 0x19, 0x7a, 0x20, 0x11, 0x2d, 0xda, 0x82, 0x8d, 0x54, 0xb5, 0xdc, 0xd5, 0xbf, 0xd6,
	0x80, 0x34, 0xa2, 0xd8, 0xa9, 0xb1, 0xd6, 0xeb, 0xff, 0xc5, 0xb6, 0x24, 0x33, 0xa6, 0x2d, 0x99,
	0x9e, 0xa4, 0x2d, 0x89, 0x5a, 0x86, 0x99, 0xd4, 0x96, 0x61, 0x36, 0xde, 0x32, 0x88, 0x62, 0x6e,
	0xc8, 0x22, 0xee, 0xea, 0xff, 0xa5, 0x41, 0x51, 0x59, 0x57, 0x26, 0xa8, 0xdf, 0x9f, 0xbd, 0x51,
	0x35, 0x31, 0xad, 0x56, 0x13, 0x23, 0x86, 0xbc, 0x33, 0xef, 0x36, 0xe4, 0x8d, 0xfc, 0x32, 0x9b,
	0xea, 0x97, 0xb9, 0x84, 0x5f, 0x36, 0x60, 0x3d, 0xc5, 0x7e, 0xee, 0xea, 0xc7, 0x50, 0x0c, 0xa3,
	0xa2, 0xe5, 0x74, 0xbb, 0xd4, 0xb6, 0xe4, 0xef, 0x69, 0xa3, 0xef, 0xdf, 0x5b, 0x30, 0x1b, 0xfc,
	0x02, 0x27, 0xbd, 0x11, 0x7c, 0x09, 0x15, 0x29, 0x52, 0xb8, 0xab, 0xdf, 0xc3, 0x5b, 0x6a, 0x12,
	0xf9, 0xfa, 0x63, 0xb8, 0x35, 0x8a, 0x95, 0xbb, 0xa9, 0x9a, 0xbf, 0xd4, 0xe0, 0x66, 0x83, 0xfa,
	0xa7, 0x0e, 0x37, 0x59, 0xab, 0xda, 0x35, 0xdb, 0xf4, 0xc8, 0xb1, 0x7d, 0xda, 0xf7, 0xc9, 0xef,
	0x04, 0x73, 0x59, 0xe5, 0xc7, 0x24, 0x8f, 0xfe, 0x38, 0x78, 0x03, 0x6e, 0x47, 0xd9, 0x9b, 0x12,
	0x1a, 0x72, 0x18, 0x1d, 0xb9, 0x5e, 0x00, 0x1e, 0x7b, 0xd7, 0x7d, 0xa9, 0x61, 0x1b, 0xaa, 0x20,
	0x11, 0x5b, 0x6e, 0xc1, 0xac, 0x47, 0x79, 0xaf, 0x23, 0x1f, 0x9f, 0xf9, 0x7a, 0xf0, 0x25, 0x5e,
	0xef, 0x2e, 0x72, 0x1a, 0x4c, 0xb0, 0x2a, 0x3f, 0x49, 0x2c, 0x76, 0x23, 0x09, 0x17, 0x5e, 0x87,
	0x14, 0x61, 0xae, 0x25, 0x4d, 0xc2, 0x30, 0x5b, 0xa8, 0x87, 0x9f, 0xe8, 0x13, 0xa7, 0xe7, 0xb5,
	0x68, 0x30, 0xb3, 0x0a, 0xbe, 0xf4, 0x55, 0x4c, 0xed, 0x18, 0x10, 0xee, 0xde, 0xff, 0x7b, 0x0d,
	0x56, 0x2a, 0x41, 0xb2, 0x29, 0x95, 0x32, 0xd9, 0x80, 0xb5, 0x4a, 0xed, 0xe2, 0xd4, 0xf8, 0xb4,
	0x5a, 0x3b, 0xfe, 0xfc, 0xf9, 0x59, 0xf3, 0xe0, 0xc4, 0xa8, 0xd6, 0x3e, 0x3b, 0x78, 0x51, 0x3d,
	0x2e, 0xdc, 0x20, 0x65, 0xd8, 0x4c, 0x12, 0xcf, 0xce, 0xcf, 0xcf, 0x1a, 0xd5, 0x66, 0xc5, 0x68,
	0x54, 0x7e, 0x58, 0xd0, 0xc8, 0x36, 0x94, 0x92, 0x1c, 0x07, 0x8d, 0x46, 0xf5, 0xa4, 0x86, 0xf4,
	0x29, 0x72, 0x07, 0xca, 0x49, 0xfa, 0xd1, 0x59, 0xbd, 0x5e, 0x79, 0x71, 0xd0, 0xac, 0x9e, 0xd5,
	0x8c, 0xf3, 0x83, 0x6a, 0xbd, 0x90, 0x21, 0x45, 0x58, 0x4d, 0x72, 0x9d, 0x1c, 0x9c, 0x56, 0x0a,
	0xd3, 0xf7, 0xdb, 0xb0, 0x5c, 0x51, 0x0e, 0x47, 0xfe, 0xd8, 0x2b, 0xd8, 0x05, 0xbd, 0x71, 0x54,
	0xaf, 0x54, 0x6a, 0xc6, 0xc1, 0xc5, 0x71, 0xb5, 0x59, 0xad, 0x9d, 0x14, 0x6e, 0x90, 0x4d, 0x28,
	0x0e, 0x51, 0x8c, 0x7a, 0xe5, 0xb7, 0x2a, 0x47, 0xcd, 0x82, 0x46, 0x4a, 0x70, 0x6b, 0x98, 0x7a,
	0x7e, 0xd0, 0x68, 0x14, 0xa6, 0xee, 0xff, 0x42, 0x83, 0x3c, 0x6a, 0x0a, 0xa7, 0xef, 0x64, 0x0b,
	0xd6, 0x11, 0x94, 0xd8, 0x51, 0xf9, 0x61, 0xb3, 0x7e, 0x70, 0x76, 0xde, 0x54, 0x7c, 0xb3, 0x03,
	0x1b, 0xc3, 0x64, 0xf1, 0x61, 0xd4, 0xaa, 0x47, 0xbf, 0x5d, 0xd0, 0x06, 0xce, 0x8b, 0x31, 0x48,
	0xd5, 0x8d, 0xe7, 0x67, 0xcd, 0xc2, 0x14, 0xf9, 0x10, 0x3e, 0x18, 0x2d, 0x42, 0x48, 0x30, 0x9a,
	0x95, 0x83, 0x53, 0xe3, 0xb3, 0x6a, 0xa3, 0x7a, 0xf8, 0xa2, 0x52, 0x98, 0x7e, 0xf2, 0xcb, 0x22,
	0x84, 0xff, 0xbf, 0x42, 0xbe, 0x00, 0x88, 0x86, 0x10, 0xe4, 0x56, 0x74, 0x7d, 0xa8, 0xf3, 0x8e,
	0xd2, 0xda, 0xc8, 0x75, 0xee, 0xea, 0xa5, 0x3f, 0xfe, 0xea, 0xdb, 0x8c, 0xf6, 0x67, 0x5f, 0x7d,
	0x9b, 0x99, 0xea, 0x3d, 0xfd, 0xcb, 0xaf, 0xbe, 0xcd, 0x64, 0x1f, 0xf6, 0xca, 0xcf, 0x7a, 0xcc,
	0xda, 0x2f, 0x13, 0x0a, 0xd0, 0x18, 0x25, 0xba, 0x91, 0x22, 0x3a, 0x31, 0x84, 0xbb, 0x2b, 0x44,
	0x4f, 0x09, 0xd1, 0xd3, 0xbd, 0xa7, 0x7d, 0x14, 0xbe, 0x32, 0x10, 0xfe, 0xb0, 0x5f, 0x7e, 0x26,
	0xae, 0xc2, 0xe3, 0x7d, 0xf2, 0x33, 0x0d, 0xf2, 0xb1, 0x51, 0x0a, 0x89, 0x5e, 0xff, 0xe4, 0xe0,
	0xa6, 0x54, 0x4a, 0x23, 0x89, 0x1b, 0x4c, 0x28, 0xcc, 0xc4, 0x14, 0x7e, 0x14, 0x28, 0x14, 0xcf,
	0x75, 0xb9, 0xc3, 0x5e, 0xd3, 0x72, 0x8f, 0x59, 0x1f, 0x3d, 0xe8, 0x31, 0xeb, 0x89, 0xc4, 0xc0,
	0x78, 0xd9, 0xa6, 0xd4, 0x2a, 0xd3, 0xbe, 0x5f, 0x66, 0xf6, 0xa5, 0xb3, 0x4f, 0xae, 0xa0, 0x90,
	0x1c, 0xa8, 0x90, 0x4d, 0xd5, 0x7d, 0xc9, 0xe9, 0x4c, 0x69, 0x6b, 0x0c, 0x95, 0xbb, 0xfa, 0xae,
	0x80, 0x95, 0x13, 0xb0, 0xe6, 0x7b, 0x4f, 0xfd, 0xa7, 0xf6, 0x53, 0x17, 0xa1, 0x2d, 0x47, 0xbe,
	0xf0, 0x9f, 0x89, 0xb7, 0x63, 0x9f, 0xfc, 0x21, 0x2c, 0x25, 0xe6, 0x20, 0x24, 0x1a, 0xc0, 0x0d,
	0x8f, 0x5e, 0x4a, 0x9b, 0xe9, 0x44, 0xee, 0xea, 0x7b, 0x42, 0xef, 0x02, 0xba, 0xa3, 0x1f, 0xe8,
	0xdc, 0x0c, 0xdc, 0x5e, 0x46, 0x7d, 0xe5, 0x87, 0xae, 0xfc, 0x12, 0xb7, 0xd8, 0x7e, 0x99, 0xfc,
	0x08, 0x96, 0x12, 0x33, 0x10, 0x45, 0xfd, 0xf0, 0xc8, 0x45, 0x51, 0x3f, 0x6a, 0x74, 0xb2, 0x29,
	0xd4, 0xe7, 0x31, 0xb2, 0xe4, 0x59, 0xe4, 0x02, 0xe5, 0x06, 0xb3, 0xf6, 0x09, 0x87, 0x05, 0x75,
	0x1e, 0x42, 0x8a, 0x03, 0x59, 0x89, 0x79, 0x4b, 0x69, 0x3d, 0x85, 0xc2, 0x5d, 0xfd, 0x91, 0x50,
	0xb1, 0x18, 0xb3, 0x70, 0x43, 0x51, 0x82, 0xf6, 0x31, 0x5e, 0x0e, 0x9a, 0x91, 0x7d, 0x62, 0x42,
	0x3e, 0x36, 0xe6, 0x50, 0x02, 0x2d, 0x39, 0x87, 0x51, 0x02, 0x6d, 0x78, 0x32, 0xb2, 0x2e, 0xf4,
	0x2e, 0x29, 0x49, 0x33, 0x1f, 0x9e, 0x25, 0xf9, 0x73, 0x0d, 0x4a, 0xe9, 0x33, 0x08, 0x72, 0x37,
	0x71, 0x62, 0x29, 0x93, 0x95, 0xd2, 0x07, 0x13, 0xf1, 0x71, 0x57, 0x2f, 0x0b, 0x28, 0x37, 0x83,
	0xe0, 0x12, 0x4e, 0xe0, 0xc9, 0x2c, 0xfe, 0x2b, 0x0d, 0x36, 0xc7, 0xcd, 0x10, 0xc8, 0x6e, 0x42,
	0x57, 0x6a, 0xf3, 0x57, 0xba, 0x37, 0x21, 0x27, 0x77, 0xf5, 0x3b, 0x02, 0xd7, 0x8e, 0x72, 0xfa,
	0x2b, 0xe2, 0x60, 0xc2, 0x3e, 0x31, 0x8c, 0xb8, 0x2f, 0x35, 0xd8, 0x1a, 0x3b, 0x76, 0x20, 0xf7,
	0xd4, 0xdc, 0x1a, 0x3b, 0xc2, 0x28, 0xdd, 0x9f, 0x94, 0x95, 0xbb, 0xfa, 0x92, 0x80, 0x57, 0x16,
	0xf0, 0x6e, 0x08, 0x70, 0x37, 0xc8, 0xcf, 0x35, 0xd8, 0x18, 0x33, 0x4e, 0x20, 0x1f, 0xa4, 0x0b,
	0x8f, 0xcd, 0x2d, 0x4a, 0xbb, 0x93, 0x31, 0x72, 0x57, 0xd7, 0x05, 0x86, 0xdb, 0x8a, 0x8b, 0x96,
	0x63, 0x2e, 0xc2, 0xc3, 0xfb, 0x07, 0x0d, 0xca, 0xd7, 0x0d, 0x09, 0xc8, 0x83, 0x44, 0x1e, 0x8e,
	0x1d, 0x5e, 0x94, 0x1e, 0xbe, 0x03, 0x37, 0x77, 0xf5, 0x4f, 0x04, 0x4a, 0x3d, 0x96, 0x63, 0x77,
	0x86, 0x70, 0x8e, 0x3a, 0xdb, 0x3f, 0x82, 0x52, 0xfa, 0xac, 0x41, 0x49, 0x84, 0xb1, 0xb3, 0x0d,
	0x25, 0x11, 0xae, 0x19, 0x5c, 0xe0, 0x89, 0xbe, 0xa7, 0x9c, 0xe8, 0x5f, 0x6b, 0xb0, 0x9e, 0x3a,
	0x6a, 0x20, 0xef, 0xab, 0xb7, 0x4a, 0xba, 0xfa, 0xbb, 0x93, 0xb0, 0x71, 0x57, 0xff, 0x48, 0x68,
	0xbf, 0x13, 0x78, 0xc9, 0x42, 0x2f, 0x6d, 0xab, 0x4e, 0xc1, 0xeb, 0xa8, 0xfc, 0xd0, 0x2a, 0x3f,
	0x1b, 0x5c, 0x46, 0xf6, 0xe0, 0x37, 0x37, 0xf9, 0x5b, 0x85, 0x7a, 0x19, 0x25, 0x7e, 0x23, 0x52,
	0x2f, 0xa3, 0xa1, 0x9f, 0x37, 0xf0, 0x99, 0x7d, 0xff, 0xfa, 0x67, 0xf6, 0x0f, 0xf0, 0x5d, 0x8b,
	0xb5, 0xa8, 0xf1, 0x77, 0x2d, 0xd9, 0x42, 0xc7, 0xdf, 0xb5, 0xa1, 0x76, 0x58, 0xbf, 0x2f, 0x14,
	0xef, 0x06, 0x8a, 0xe5, 0xb5, 0xb3, 0x16, 0x29, 0xe6, 0xe5, 0x67, 0xa2, 0x20, 0x96, 0xd1, 0x40,
	0xfe, 0x46, 0x83, 0xb5, 0x94, 0x26, 0x94, 0x44, 0xe3, 0xc3, 0xf4, 0xee, 0xb8, 0x74, 0xe7, 0x7a,
	0x26, 0xee, 0xea, 0x4f, 0x05, 0xa4, 0x7b, 0x02, 0xd2, 0xac, 0x78, 0x6a, 0x25, 0xa8, 0xf7, 0xd5,
	0x87, 0x76, 0xd0, 0x43, 0x4b, 0x88, 0x83, 0xae, 0x79, 0x9f, 0xfc, 0x54, 0x83, 0x95, 0x11, 0x9d,
	0x3f, 0xd9, 0x49, 0x54, 0x1c, 0x43, 0x6e, 0x2a, 0x8f, 0x67, 0x08, 0x5f, 0xe2, 0xfb, 0x31, 0x4f,
	0x6d, 0x8e, 0xf6, 0x54, 0x59, 0x14, 0x2a, 0xfb, 0xc4, 0x81, 0xa5, 0x44, 0x03, 0xab, 0xbc, 0xc4,
	0xc3, 0xcd, 0xba, 0xf2, 0x12, 0x8f, 0xea, 0x7b, 0x6f, 0x0b, 0xf5, 0x1f, 0xc6, 0xd4, 0x2f, 0xc6,
	0xd4, 0xef, 0x93, 0x3f, 0x91, 0xcd, 0xd3, 0x70, 0xff, 0x43, 0xae, 0xef, 0x8f, 0x4a, 0xfa, 0x75,
	0x2c, 0x21, 0x86, 0x07, 0x63, 0x31, 0xfc, 0xa9, 0x06, 0x37, 0x47, 0xf6, 0x8e, 0x0a, 0x86, 0xb4,
	0x0e, 0x55, 0xc1, 0x90, 0xde, 0x7e, 0x62, 0xa6, 0x3c, 0x8c, 0x61, 0x58, 0x29, 0x3f, 0xec, 0x85,
	0x18, 0x9e, 0xc9, 0x46, 0x72, 0x9f, 0xb8, 0x38, 0x3e, 0x4c, 0x82, 0xd8, 0x56, 0xb3, 0x61, 0x04,
	0x82, 0x9d, 0xb1, 0xf4, 0xb0, 0x6a, 0x78, 0xa4, 0x56, 0x0d, 0xa1, 0x72, 0x62, 0xe1, 0x2f, 0xd8,
	0x4a, 0x9f, 0x46, 0x62, 0x19, 0x1f, 0xef, 0x24, 0x4b, 0x1b, 0xa9, 0xb4, 0x50, 0xcb, 0xde, 0x28,
	0x2d, 0xa5, 0xd9, 0x9f, 0x7c, 0xf5, 0x6d, 0xe6, 0xa7, 0x57, 0x87, 0x87, 0xff, 0xf8, 0xcd, 0xb6,
	0xf6, 0xf5, 0x37, 0xdb, 0xda, 0xbf, 0x7d, 0xb3, 0xad, 0xfd, 0xc5, 0xbf, 0x6f, 0xdf, 0xf8, 0xdd,
	0xc7, 0x6d, 0xa7, 0x63, 0xda, 0xed, 0x47, 0x1f, 0x3f, 0xf1, 0xfd, 0x47, 0x2d, 0xa7, 0xbb, 0x87,
	0xff, 0xf5, 0xde, 0x72, 0x3a, 0x7b, 0x9c, 0x7a, 0x6f, 0x58, 0x8b, 0xf2, 0xbd, 0xf8, 0x7f, 0xd3,
	0xbf, 0x9c, 0x45, 0x8e, 0x5f, 0xf9, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0xde, 0x44, 0xb6, 0x2d,
	0x66, 0x2f, 0x00, 0x00,
}
