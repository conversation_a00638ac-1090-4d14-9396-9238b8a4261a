// Code generated by protoc-gen-go. DO NOT EDIT.
// source: censoring-proxy/text.proto

package censoring_proxy // import "golang.52tt.com/protocol/services/censoring-proxy"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TextData struct {
	Metadata             *Metadata `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Content              string    `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *TextData) Reset()         { *m = TextData{} }
func (m *TextData) String() string { return proto.CompactTextString(m) }
func (*TextData) ProtoMessage()    {}
func (*TextData) Descriptor() ([]byte, []int) {
	return fileDescriptor_text_482ad360992f57f5, []int{0}
}
func (m *TextData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TextData.Unmarshal(m, b)
}
func (m *TextData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TextData.Marshal(b, m, deterministic)
}
func (dst *TextData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TextData.Merge(dst, src)
}
func (m *TextData) XXX_Size() int {
	return xxx_messageInfo_TextData.Size(m)
}
func (m *TextData) XXX_DiscardUnknown() {
	xxx_messageInfo_TextData.DiscardUnknown(m)
}

var xxx_messageInfo_TextData proto.InternalMessageInfo

func (m *TextData) GetMetadata() *Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (m *TextData) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type ScanTextReq struct {
	Context              *TaskContext `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	TextData             *TextData    `protobuf:"bytes,2,opt,name=text_data,json=textData,proto3" json:"text_data,omitempty"`
	Callback             *Callback    `protobuf:"bytes,3,opt,name=callback,proto3" json:"callback,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ScanTextReq) Reset()         { *m = ScanTextReq{} }
func (m *ScanTextReq) String() string { return proto.CompactTextString(m) }
func (*ScanTextReq) ProtoMessage()    {}
func (*ScanTextReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_text_482ad360992f57f5, []int{1}
}
func (m *ScanTextReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanTextReq.Unmarshal(m, b)
}
func (m *ScanTextReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanTextReq.Marshal(b, m, deterministic)
}
func (dst *ScanTextReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanTextReq.Merge(dst, src)
}
func (m *ScanTextReq) XXX_Size() int {
	return xxx_messageInfo_ScanTextReq.Size(m)
}
func (m *ScanTextReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanTextReq.DiscardUnknown(m)
}

var xxx_messageInfo_ScanTextReq proto.InternalMessageInfo

func (m *ScanTextReq) GetContext() *TaskContext {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *ScanTextReq) GetTextData() *TextData {
	if m != nil {
		return m.TextData
	}
	return nil
}

func (m *ScanTextReq) GetCallback() *Callback {
	if m != nil {
		return m.Callback
	}
	return nil
}

type ScanTextResp struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScanTextResp) Reset()         { *m = ScanTextResp{} }
func (m *ScanTextResp) String() string { return proto.CompactTextString(m) }
func (*ScanTextResp) ProtoMessage()    {}
func (*ScanTextResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_text_482ad360992f57f5, []int{2}
}
func (m *ScanTextResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanTextResp.Unmarshal(m, b)
}
func (m *ScanTextResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanTextResp.Marshal(b, m, deterministic)
}
func (dst *ScanTextResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanTextResp.Merge(dst, src)
}
func (m *ScanTextResp) XXX_Size() int {
	return xxx_messageInfo_ScanTextResp.Size(m)
}
func (m *ScanTextResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanTextResp.DiscardUnknown(m)
}

var xxx_messageInfo_ScanTextResp proto.InternalMessageInfo

func (m *ScanTextResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type QueryTextTaskResultReq struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryTextTaskResultReq) Reset()         { *m = QueryTextTaskResultReq{} }
func (m *QueryTextTaskResultReq) String() string { return proto.CompactTextString(m) }
func (*QueryTextTaskResultReq) ProtoMessage()    {}
func (*QueryTextTaskResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_text_482ad360992f57f5, []int{3}
}
func (m *QueryTextTaskResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTextTaskResultReq.Unmarshal(m, b)
}
func (m *QueryTextTaskResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTextTaskResultReq.Marshal(b, m, deterministic)
}
func (dst *QueryTextTaskResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTextTaskResultReq.Merge(dst, src)
}
func (m *QueryTextTaskResultReq) XXX_Size() int {
	return xxx_messageInfo_QueryTextTaskResultReq.Size(m)
}
func (m *QueryTextTaskResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTextTaskResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTextTaskResultReq proto.InternalMessageInfo

func (m *QueryTextTaskResultReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type QueryTextTaskResultResp struct {
	TaskStatus           TaskStatus `protobuf:"varint,1,opt,name=task_status,json=taskStatus,proto3,enum=censoring_proxy.TaskStatus" json:"task_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *QueryTextTaskResultResp) Reset()         { *m = QueryTextTaskResultResp{} }
func (m *QueryTextTaskResultResp) String() string { return proto.CompactTextString(m) }
func (*QueryTextTaskResultResp) ProtoMessage()    {}
func (*QueryTextTaskResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_text_482ad360992f57f5, []int{4}
}
func (m *QueryTextTaskResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryTextTaskResultResp.Unmarshal(m, b)
}
func (m *QueryTextTaskResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryTextTaskResultResp.Marshal(b, m, deterministic)
}
func (dst *QueryTextTaskResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryTextTaskResultResp.Merge(dst, src)
}
func (m *QueryTextTaskResultResp) XXX_Size() int {
	return xxx_messageInfo_QueryTextTaskResultResp.Size(m)
}
func (m *QueryTextTaskResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryTextTaskResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryTextTaskResultResp proto.InternalMessageInfo

func (m *QueryTextTaskResultResp) GetTaskStatus() TaskStatus {
	if m != nil {
		return m.TaskStatus
	}
	return TaskStatus_SUCCESS
}

// 多文本审核
type ScanMultiTextReq struct {
	Context              *TaskContext `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	TextData             []*TextData  `protobuf:"bytes,2,rep,name=text_data,json=textData,proto3" json:"text_data,omitempty"`
	Callback             *Callback    `protobuf:"bytes,3,opt,name=callback,proto3" json:"callback,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ScanMultiTextReq) Reset()         { *m = ScanMultiTextReq{} }
func (m *ScanMultiTextReq) String() string { return proto.CompactTextString(m) }
func (*ScanMultiTextReq) ProtoMessage()    {}
func (*ScanMultiTextReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_text_482ad360992f57f5, []int{5}
}
func (m *ScanMultiTextReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanMultiTextReq.Unmarshal(m, b)
}
func (m *ScanMultiTextReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanMultiTextReq.Marshal(b, m, deterministic)
}
func (dst *ScanMultiTextReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanMultiTextReq.Merge(dst, src)
}
func (m *ScanMultiTextReq) XXX_Size() int {
	return xxx_messageInfo_ScanMultiTextReq.Size(m)
}
func (m *ScanMultiTextReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanMultiTextReq.DiscardUnknown(m)
}

var xxx_messageInfo_ScanMultiTextReq proto.InternalMessageInfo

func (m *ScanMultiTextReq) GetContext() *TaskContext {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *ScanMultiTextReq) GetTextData() []*TextData {
	if m != nil {
		return m.TextData
	}
	return nil
}

func (m *ScanMultiTextReq) GetCallback() *Callback {
	if m != nil {
		return m.Callback
	}
	return nil
}

type ScanMultiTextResp struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScanMultiTextResp) Reset()         { *m = ScanMultiTextResp{} }
func (m *ScanMultiTextResp) String() string { return proto.CompactTextString(m) }
func (*ScanMultiTextResp) ProtoMessage()    {}
func (*ScanMultiTextResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_text_482ad360992f57f5, []int{6}
}
func (m *ScanMultiTextResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanMultiTextResp.Unmarshal(m, b)
}
func (m *ScanMultiTextResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanMultiTextResp.Marshal(b, m, deterministic)
}
func (dst *ScanMultiTextResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanMultiTextResp.Merge(dst, src)
}
func (m *ScanMultiTextResp) XXX_Size() int {
	return xxx_messageInfo_ScanMultiTextResp.Size(m)
}
func (m *ScanMultiTextResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanMultiTextResp.DiscardUnknown(m)
}

var xxx_messageInfo_ScanMultiTextResp proto.InternalMessageInfo

func (m *ScanMultiTextResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func init() {
	proto.RegisterType((*TextData)(nil), "censoring_proxy.TextData")
	proto.RegisterType((*ScanTextReq)(nil), "censoring_proxy.ScanTextReq")
	proto.RegisterType((*ScanTextResp)(nil), "censoring_proxy.ScanTextResp")
	proto.RegisterType((*QueryTextTaskResultReq)(nil), "censoring_proxy.QueryTextTaskResultReq")
	proto.RegisterType((*QueryTextTaskResultResp)(nil), "censoring_proxy.QueryTextTaskResultResp")
	proto.RegisterType((*ScanMultiTextReq)(nil), "censoring_proxy.ScanMultiTextReq")
	proto.RegisterType((*ScanMultiTextResp)(nil), "censoring_proxy.ScanMultiTextResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TextClient is the client API for Text service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TextClient interface {
	AsyncScanText(ctx context.Context, in *ScanTextReq, opts ...grpc.CallOption) (*ScanTextResp, error)
	QueryTextTaskResult(ctx context.Context, in *QueryTextTaskResultReq, opts ...grpc.CallOption) (*QueryTextTaskResultResp, error)
	AsyncScanMultiText(ctx context.Context, in *ScanMultiTextReq, opts ...grpc.CallOption) (*ScanMultiTextResp, error)
}

type textClient struct {
	cc *grpc.ClientConn
}

func NewTextClient(cc *grpc.ClientConn) TextClient {
	return &textClient{cc}
}

func (c *textClient) AsyncScanText(ctx context.Context, in *ScanTextReq, opts ...grpc.CallOption) (*ScanTextResp, error) {
	out := new(ScanTextResp)
	err := c.cc.Invoke(ctx, "/censoring_proxy.Text/AsyncScanText", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *textClient) QueryTextTaskResult(ctx context.Context, in *QueryTextTaskResultReq, opts ...grpc.CallOption) (*QueryTextTaskResultResp, error) {
	out := new(QueryTextTaskResultResp)
	err := c.cc.Invoke(ctx, "/censoring_proxy.Text/QueryTextTaskResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *textClient) AsyncScanMultiText(ctx context.Context, in *ScanMultiTextReq, opts ...grpc.CallOption) (*ScanMultiTextResp, error) {
	out := new(ScanMultiTextResp)
	err := c.cc.Invoke(ctx, "/censoring_proxy.Text/AsyncScanMultiText", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TextServer is the server API for Text service.
type TextServer interface {
	AsyncScanText(context.Context, *ScanTextReq) (*ScanTextResp, error)
	QueryTextTaskResult(context.Context, *QueryTextTaskResultReq) (*QueryTextTaskResultResp, error)
	AsyncScanMultiText(context.Context, *ScanMultiTextReq) (*ScanMultiTextResp, error)
}

func RegisterTextServer(s *grpc.Server, srv TextServer) {
	s.RegisterService(&_Text_serviceDesc, srv)
}

func _Text_AsyncScanText_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanTextReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TextServer).AsyncScanText(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/censoring_proxy.Text/AsyncScanText",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TextServer).AsyncScanText(ctx, req.(*ScanTextReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Text_QueryTextTaskResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTextTaskResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TextServer).QueryTextTaskResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/censoring_proxy.Text/QueryTextTaskResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TextServer).QueryTextTaskResult(ctx, req.(*QueryTextTaskResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Text_AsyncScanMultiText_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanMultiTextReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TextServer).AsyncScanMultiText(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/censoring_proxy.Text/AsyncScanMultiText",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TextServer).AsyncScanMultiText(ctx, req.(*ScanMultiTextReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Text_serviceDesc = grpc.ServiceDesc{
	ServiceName: "censoring_proxy.Text",
	HandlerType: (*TextServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AsyncScanText",
			Handler:    _Text_AsyncScanText_Handler,
		},
		{
			MethodName: "QueryTextTaskResult",
			Handler:    _Text_QueryTextTaskResult_Handler,
		},
		{
			MethodName: "AsyncScanMultiText",
			Handler:    _Text_AsyncScanMultiText_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "censoring-proxy/text.proto",
}

func init() { proto.RegisterFile("censoring-proxy/text.proto", fileDescriptor_text_482ad360992f57f5) }

var fileDescriptor_text_482ad360992f57f5 = []byte{
	// 428 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x94, 0xcf, 0x8e, 0xd3, 0x30,
	0x10, 0xc6, 0x95, 0x2e, 0xda, 0x6d, 0xa7, 0xfc, 0x35, 0x12, 0x1b, 0xc2, 0x82, 0x96, 0x5c, 0xb6,
	0x07, 0x48, 0xb4, 0x59, 0xed, 0x9e, 0xb8, 0x40, 0xb9, 0x20, 0xd1, 0x03, 0x6e, 0x25, 0x24, 0x7a,
	0xa8, 0x5c, 0xd7, 0x0a, 0x51, 0xdd, 0x38, 0xc4, 0x13, 0x94, 0x3e, 0x0b, 0xaf, 0xc1, 0x85, 0xb7,
	0x43, 0x71, 0x93, 0x50, 0xa5, 0x69, 0xe1, 0xc0, 0x9e, 0x12, 0x6b, 0x7e, 0x9f, 0xfd, 0x7d, 0x33,
	0x89, 0xc1, 0xe1, 0x22, 0xd6, 0x2a, 0x8d, 0xe2, 0xf0, 0x75, 0x92, 0xaa, 0x7c, 0xed, 0xa3, 0xc8,
	0xd1, 0x4b, 0x52, 0x85, 0x8a, 0x3c, 0xa8, 0x6b, 0x33, 0x53, 0x73, 0xce, 0x9a, 0x70, 0x28, 0xd5,
	0x9c, 0xc9, 0x0d, 0xee, 0xbc, 0x68, 0x56, 0x39, 0x93, 0x72, 0xce, 0xf8, 0x72, 0x53, 0x77, 0xa7,
	0xd0, 0x9d, 0x88, 0x1c, 0xdf, 0x33, 0x64, 0xe4, 0x1a, 0xba, 0x2b, 0x81, 0x6c, 0xc1, 0x90, 0xd9,
	0xd6, 0xb9, 0x35, 0xe8, 0x07, 0x4f, 0xbd, 0xc6, 0x69, 0xde, 0xa8, 0x04, 0x68, 0x8d, 0x12, 0x1b,
	0x4e, 0xb8, 0x8a, 0x51, 0xc4, 0x68, 0x77, 0xce, 0xad, 0x41, 0x8f, 0x56, 0x4b, 0xf7, 0xa7, 0x05,
	0xfd, 0x31, 0x67, 0x71, 0x71, 0x02, 0x15, 0xdf, 0xc8, 0x4d, 0x49, 0xe6, 0x58, 0xee, 0x7f, 0xb6,
	0xb3, 0xff, 0x84, 0xe9, 0xe5, 0x70, 0xc3, 0xd0, 0x0a, 0x26, 0x37, 0xd0, 0x2b, 0x9e, 0x33, 0xe3,
	0xac, 0xb3, 0xc7, 0x59, 0x15, 0x83, 0x76, 0x71, 0x2b, 0x50, 0x15, 0xd7, 0x3e, 0xda, 0x23, 0x1b,
	0x96, 0x00, 0xad, 0x51, 0xf7, 0x02, 0xee, 0xfe, 0x71, 0xad, 0x13, 0x72, 0x0a, 0x27, 0xc8, 0xf4,
	0x72, 0x16, 0x2d, 0x8c, 0xed, 0x1e, 0x3d, 0x2e, 0x96, 0x1f, 0x16, 0xee, 0x25, 0x3c, 0xf9, 0x94,
	0x89, 0x74, 0x5d, 0x90, 0x85, 0x71, 0x2a, 0x74, 0x26, 0x4d, 0xd2, 0xbd, 0x92, 0xcf, 0x70, 0xda,
	0x2a, 0xd1, 0x09, 0x79, 0x03, 0x7d, 0xa3, 0xd1, 0xc8, 0x30, 0xd3, 0x46, 0x77, 0x3f, 0x78, 0xd6,
	0xda, 0xa1, 0xb1, 0x41, 0x28, 0x60, 0xfd, 0xee, 0xfe, 0xb2, 0xe0, 0x61, 0xe1, 0x7a, 0x94, 0x49,
	0x8c, 0xfe, 0x73, 0xc3, 0x8f, 0x6e, 0xb9, 0xe1, 0xaf, 0xe0, 0x51, 0xc3, 0xfa, 0x81, 0xae, 0x07,
	0x3f, 0x3a, 0x70, 0xa7, 0xa0, 0xc8, 0x47, 0xb8, 0xf7, 0x56, 0xaf, 0x63, 0x5e, 0x0d, 0x8b, 0xec,
	0xa6, 0xdb, 0xfa, 0xfa, 0x9c, 0xe7, 0x07, 0xaa, 0x3a, 0x21, 0x5f, 0xe1, 0x71, 0xcb, 0x64, 0xc8,
	0xc5, 0x8e, 0xaa, 0x7d, 0xe4, 0xce, 0xe0, 0xdf, 0x40, 0x9d, 0x90, 0x29, 0x90, 0xda, 0x77, 0x9d,
	0x99, 0xbc, 0x6c, 0xb5, 0xb7, 0x3d, 0x4e, 0xc7, 0xfd, 0x1b, 0xa2, 0x93, 0x77, 0x57, 0x5f, 0x2e,
	0x43, 0x25, 0x59, 0x1c, 0x7a, 0xd7, 0x01, 0xa2, 0xc7, 0xd5, 0xca, 0x37, 0x7f, 0x3a, 0x57, 0xd2,
	0xd7, 0x22, 0xfd, 0x1e, 0x71, 0xa1, 0xfd, 0xc6, 0xa5, 0x30, 0x3f, 0x36, 0xc8, 0xd5, 0xef, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x35, 0x0d, 0x04, 0xc1, 0x79, 0x04, 0x00, 0x00,
}
