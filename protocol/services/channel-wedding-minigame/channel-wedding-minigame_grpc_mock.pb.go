// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/channel-wedding-minigame/channel-wedding-minigame.proto

package channel_wedding_minigame

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockChannelWeddingMinigameClient is a mock of ChannelWeddingMinigameClient interface.
type MockChannelWeddingMinigameClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelWeddingMinigameClientMockRecorder
}

// MockChannelWeddingMinigameClientMockRecorder is the mock recorder for MockChannelWeddingMinigameClient.
type MockChannelWeddingMinigameClientMockRecorder struct {
	mock *MockChannelWeddingMinigameClient
}

// NewMockChannelWeddingMinigameClient creates a new mock instance.
func NewMockChannelWeddingMinigameClient(ctrl *gomock.Controller) *MockChannelWeddingMinigameClient {
	mock := &MockChannelWeddingMinigameClient{ctrl: ctrl}
	mock.recorder = &MockChannelWeddingMinigameClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelWeddingMinigameClient) EXPECT() *MockChannelWeddingMinigameClientMockRecorder {
	return m.recorder
}

// ApplyToJoinChairGame mocks base method.
func (m *MockChannelWeddingMinigameClient) ApplyToJoinChairGame(ctx context.Context, in *ApplyToJoinChairGameRequest, opts ...grpc.CallOption) (*ApplyToJoinChairGameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplyToJoinChairGame", varargs...)
	ret0, _ := ret[0].(*ApplyToJoinChairGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyToJoinChairGame indicates an expected call of ApplyToJoinChairGame.
func (mr *MockChannelWeddingMinigameClientMockRecorder) ApplyToJoinChairGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyToJoinChairGame", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).ApplyToJoinChairGame), varargs...)
}

// BatGetIfChannelInChairGame mocks base method.
func (m *MockChannelWeddingMinigameClient) BatGetIfChannelInChairGame(ctx context.Context, in *BatGetIfChannelInChairGameReq, opts ...grpc.CallOption) (*BatGetIfChannelInChairGameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetIfChannelInChairGame", varargs...)
	ret0, _ := ret[0].(*BatGetIfChannelInChairGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetIfChannelInChairGame indicates an expected call of BatGetIfChannelInChairGame.
func (mr *MockChannelWeddingMinigameClientMockRecorder) BatGetIfChannelInChairGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetIfChannelInChairGame", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).BatGetIfChannelInChairGame), varargs...)
}

// ForceCloseChairGame mocks base method.
func (m *MockChannelWeddingMinigameClient) ForceCloseChairGame(ctx context.Context, in *ForceCloseChairGameRequest, opts ...grpc.CallOption) (*ForceCloseChairGameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ForceCloseChairGame", varargs...)
	ret0, _ := ret[0].(*ForceCloseChairGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForceCloseChairGame indicates an expected call of ForceCloseChairGame.
func (mr *MockChannelWeddingMinigameClientMockRecorder) ForceCloseChairGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceCloseChairGame", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).ForceCloseChairGame), varargs...)
}

// GetAwardOrderIds mocks base method.
func (m *MockChannelWeddingMinigameClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAwardOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardOrderIds indicates an expected call of GetAwardOrderIds.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GetAwardOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardOrderIds", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GetAwardOrderIds), varargs...)
}

// GetAwardPackageOrderIds mocks base method.
func (m *MockChannelWeddingMinigameClient) GetAwardPackageOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAwardPackageOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardPackageOrderIds indicates an expected call of GetAwardPackageOrderIds.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GetAwardPackageOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardPackageOrderIds", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GetAwardPackageOrderIds), varargs...)
}

// GetAwardPackageTotalCount mocks base method.
func (m *MockChannelWeddingMinigameClient) GetAwardPackageTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAwardPackageTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardPackageTotalCount indicates an expected call of GetAwardPackageTotalCount.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GetAwardPackageTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardPackageTotalCount", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GetAwardPackageTotalCount), varargs...)
}

// GetAwardTotalCount mocks base method.
func (m *MockChannelWeddingMinigameClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAwardTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalCount indicates an expected call of GetAwardTotalCount.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GetAwardTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalCount", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GetAwardTotalCount), varargs...)
}

// GetChairGameApplyList mocks base method.
func (m *MockChannelWeddingMinigameClient) GetChairGameApplyList(ctx context.Context, in *GetChairGameApplyListRequest, opts ...grpc.CallOption) (*GetChairGameApplyListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChairGameApplyList", varargs...)
	ret0, _ := ret[0].(*GetChairGameApplyListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameApplyList indicates an expected call of GetChairGameApplyList.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GetChairGameApplyList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameApplyList", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GetChairGameApplyList), varargs...)
}

// GetChairGameInfo mocks base method.
func (m *MockChannelWeddingMinigameClient) GetChairGameInfo(ctx context.Context, in *GetChairGameInfoRequest, opts ...grpc.CallOption) (*GetChairGameInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChairGameInfo", varargs...)
	ret0, _ := ret[0].(*GetChairGameInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameInfo indicates an expected call of GetChairGameInfo.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GetChairGameInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameInfo", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GetChairGameInfo), varargs...)
}

// GetChairGameReward mocks base method.
func (m *MockChannelWeddingMinigameClient) GetChairGameReward(ctx context.Context, in *GetChairGameRewardRequest, opts ...grpc.CallOption) (*GetChairGameRewardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChairGameReward", varargs...)
	ret0, _ := ret[0].(*GetChairGameRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameReward indicates an expected call of GetChairGameReward.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GetChairGameReward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameReward", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GetChairGameReward), varargs...)
}

// GetCurChairGamePlayers mocks base method.
func (m *MockChannelWeddingMinigameClient) GetCurChairGamePlayers(ctx context.Context, in *GetCurChairGamePlayersRequest, opts ...grpc.CallOption) (*GetCurChairGamePlayersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCurChairGamePlayers", varargs...)
	ret0, _ := ret[0].(*GetCurChairGamePlayersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurChairGamePlayers indicates an expected call of GetCurChairGamePlayers.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GetCurChairGamePlayers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurChairGamePlayers", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GetCurChairGamePlayers), varargs...)
}

// GetTBeanOrderIds mocks base method.
func (m *MockChannelWeddingMinigameClient) GetTBeanOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTBeanOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanOrderIds indicates an expected call of GetTBeanOrderIds.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GetTBeanOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanOrderIds", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GetTBeanOrderIds), varargs...)
}

// GetTBeanTotalCount mocks base method.
func (m *MockChannelWeddingMinigameClient) GetTBeanTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTBeanTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanTotalCount indicates an expected call of GetTBeanTotalCount.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GetTBeanTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanTotalCount", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GetTBeanTotalCount), varargs...)
}

// GrabChair mocks base method.
func (m *MockChannelWeddingMinigameClient) GrabChair(ctx context.Context, in *GrabChairRequest, opts ...grpc.CallOption) (*GrabChairResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GrabChair", varargs...)
	ret0, _ := ret[0].(*GrabChairResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GrabChair indicates an expected call of GrabChair.
func (mr *MockChannelWeddingMinigameClientMockRecorder) GrabChair(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrabChair", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).GrabChair), varargs...)
}

// PushCurChairGameInfo mocks base method.
func (m *MockChannelWeddingMinigameClient) PushCurChairGameInfo(ctx context.Context, in *PushCurChairGameInfoReq, opts ...grpc.CallOption) (*PushCurChairGameInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PushCurChairGameInfo", varargs...)
	ret0, _ := ret[0].(*PushCurChairGameInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PushCurChairGameInfo indicates an expected call of PushCurChairGameInfo.
func (mr *MockChannelWeddingMinigameClientMockRecorder) PushCurChairGameInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushCurChairGameInfo", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).PushCurChairGameInfo), varargs...)
}

// ReissueAward mocks base method.
func (m *MockChannelWeddingMinigameClient) ReissueAward(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReissueAward", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReissueAward indicates an expected call of ReissueAward.
func (mr *MockChannelWeddingMinigameClientMockRecorder) ReissueAward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReissueAward", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).ReissueAward), varargs...)
}

// SetChairGameReward mocks base method.
func (m *MockChannelWeddingMinigameClient) SetChairGameReward(ctx context.Context, in *SetChairGameRewardRequest, opts ...grpc.CallOption) (*SetChairGameRewardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChairGameReward", varargs...)
	ret0, _ := ret[0].(*SetChairGameRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChairGameReward indicates an expected call of SetChairGameReward.
func (mr *MockChannelWeddingMinigameClientMockRecorder) SetChairGameReward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChairGameReward", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).SetChairGameReward), varargs...)
}

// SetChairGameToNextRound mocks base method.
func (m *MockChannelWeddingMinigameClient) SetChairGameToNextRound(ctx context.Context, in *SetChairGameToNextRoundRequest, opts ...grpc.CallOption) (*SetChairGameToNextRoundResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChairGameToNextRound", varargs...)
	ret0, _ := ret[0].(*SetChairGameToNextRoundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChairGameToNextRound indicates an expected call of SetChairGameToNextRound.
func (mr *MockChannelWeddingMinigameClientMockRecorder) SetChairGameToNextRound(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChairGameToNextRound", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).SetChairGameToNextRound), varargs...)
}

// StartChairGame mocks base method.
func (m *MockChannelWeddingMinigameClient) StartChairGame(ctx context.Context, in *StartChairGameRequest, opts ...grpc.CallOption) (*StartChairGameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartChairGame", varargs...)
	ret0, _ := ret[0].(*StartChairGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartChairGame indicates an expected call of StartChairGame.
func (mr *MockChannelWeddingMinigameClientMockRecorder) StartChairGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartChairGame", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).StartChairGame), varargs...)
}

// StartGrabChair mocks base method.
func (m *MockChannelWeddingMinigameClient) StartGrabChair(ctx context.Context, in *StartGrabChairRequest, opts ...grpc.CallOption) (*StartGrabChairResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartGrabChair", varargs...)
	ret0, _ := ret[0].(*StartGrabChairResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartGrabChair indicates an expected call of StartGrabChair.
func (mr *MockChannelWeddingMinigameClientMockRecorder) StartGrabChair(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartGrabChair", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).StartGrabChair), varargs...)
}

// StartNewGamePreCheck mocks base method.
func (m *MockChannelWeddingMinigameClient) StartNewGamePreCheck(ctx context.Context, in *StartChairGameRequest, opts ...grpc.CallOption) (*StartChairGameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartNewGamePreCheck", varargs...)
	ret0, _ := ret[0].(*StartChairGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartNewGamePreCheck indicates an expected call of StartNewGamePreCheck.
func (mr *MockChannelWeddingMinigameClientMockRecorder) StartNewGamePreCheck(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartNewGamePreCheck", reflect.TypeOf((*MockChannelWeddingMinigameClient)(nil).StartNewGamePreCheck), varargs...)
}

// MockChannelWeddingMinigameServer is a mock of ChannelWeddingMinigameServer interface.
type MockChannelWeddingMinigameServer struct {
	ctrl     *gomock.Controller
	recorder *MockChannelWeddingMinigameServerMockRecorder
}

// MockChannelWeddingMinigameServerMockRecorder is the mock recorder for MockChannelWeddingMinigameServer.
type MockChannelWeddingMinigameServerMockRecorder struct {
	mock *MockChannelWeddingMinigameServer
}

// NewMockChannelWeddingMinigameServer creates a new mock instance.
func NewMockChannelWeddingMinigameServer(ctrl *gomock.Controller) *MockChannelWeddingMinigameServer {
	mock := &MockChannelWeddingMinigameServer{ctrl: ctrl}
	mock.recorder = &MockChannelWeddingMinigameServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelWeddingMinigameServer) EXPECT() *MockChannelWeddingMinigameServerMockRecorder {
	return m.recorder
}

// ApplyToJoinChairGame mocks base method.
func (m *MockChannelWeddingMinigameServer) ApplyToJoinChairGame(ctx context.Context, in *ApplyToJoinChairGameRequest) (*ApplyToJoinChairGameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyToJoinChairGame", ctx, in)
	ret0, _ := ret[0].(*ApplyToJoinChairGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyToJoinChairGame indicates an expected call of ApplyToJoinChairGame.
func (mr *MockChannelWeddingMinigameServerMockRecorder) ApplyToJoinChairGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyToJoinChairGame", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).ApplyToJoinChairGame), ctx, in)
}

// BatGetIfChannelInChairGame mocks base method.
func (m *MockChannelWeddingMinigameServer) BatGetIfChannelInChairGame(ctx context.Context, in *BatGetIfChannelInChairGameReq) (*BatGetIfChannelInChairGameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetIfChannelInChairGame", ctx, in)
	ret0, _ := ret[0].(*BatGetIfChannelInChairGameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetIfChannelInChairGame indicates an expected call of BatGetIfChannelInChairGame.
func (mr *MockChannelWeddingMinigameServerMockRecorder) BatGetIfChannelInChairGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetIfChannelInChairGame", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).BatGetIfChannelInChairGame), ctx, in)
}

// ForceCloseChairGame mocks base method.
func (m *MockChannelWeddingMinigameServer) ForceCloseChairGame(ctx context.Context, in *ForceCloseChairGameRequest) (*ForceCloseChairGameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ForceCloseChairGame", ctx, in)
	ret0, _ := ret[0].(*ForceCloseChairGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForceCloseChairGame indicates an expected call of ForceCloseChairGame.
func (mr *MockChannelWeddingMinigameServerMockRecorder) ForceCloseChairGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceCloseChairGame", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).ForceCloseChairGame), ctx, in)
}

// GetAwardOrderIds mocks base method.
func (m *MockChannelWeddingMinigameServer) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardOrderIds indicates an expected call of GetAwardOrderIds.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GetAwardOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardOrderIds", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GetAwardOrderIds), ctx, in)
}

// GetAwardPackageOrderIds mocks base method.
func (m *MockChannelWeddingMinigameServer) GetAwardPackageOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardPackageOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardPackageOrderIds indicates an expected call of GetAwardPackageOrderIds.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GetAwardPackageOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardPackageOrderIds", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GetAwardPackageOrderIds), ctx, in)
}

// GetAwardPackageTotalCount mocks base method.
func (m *MockChannelWeddingMinigameServer) GetAwardPackageTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardPackageTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardPackageTotalCount indicates an expected call of GetAwardPackageTotalCount.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GetAwardPackageTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardPackageTotalCount", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GetAwardPackageTotalCount), ctx, in)
}

// GetAwardTotalCount mocks base method.
func (m *MockChannelWeddingMinigameServer) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalCount indicates an expected call of GetAwardTotalCount.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GetAwardTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalCount", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GetAwardTotalCount), ctx, in)
}

// GetChairGameApplyList mocks base method.
func (m *MockChannelWeddingMinigameServer) GetChairGameApplyList(ctx context.Context, in *GetChairGameApplyListRequest) (*GetChairGameApplyListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameApplyList", ctx, in)
	ret0, _ := ret[0].(*GetChairGameApplyListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameApplyList indicates an expected call of GetChairGameApplyList.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GetChairGameApplyList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameApplyList", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GetChairGameApplyList), ctx, in)
}

// GetChairGameInfo mocks base method.
func (m *MockChannelWeddingMinigameServer) GetChairGameInfo(ctx context.Context, in *GetChairGameInfoRequest) (*GetChairGameInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameInfo", ctx, in)
	ret0, _ := ret[0].(*GetChairGameInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameInfo indicates an expected call of GetChairGameInfo.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GetChairGameInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameInfo", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GetChairGameInfo), ctx, in)
}

// GetChairGameReward mocks base method.
func (m *MockChannelWeddingMinigameServer) GetChairGameReward(ctx context.Context, in *GetChairGameRewardRequest) (*GetChairGameRewardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameReward", ctx, in)
	ret0, _ := ret[0].(*GetChairGameRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChairGameReward indicates an expected call of GetChairGameReward.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GetChairGameReward(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameReward", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GetChairGameReward), ctx, in)
}

// GetCurChairGamePlayers mocks base method.
func (m *MockChannelWeddingMinigameServer) GetCurChairGamePlayers(ctx context.Context, in *GetCurChairGamePlayersRequest) (*GetCurChairGamePlayersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurChairGamePlayers", ctx, in)
	ret0, _ := ret[0].(*GetCurChairGamePlayersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurChairGamePlayers indicates an expected call of GetCurChairGamePlayers.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GetCurChairGamePlayers(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurChairGamePlayers", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GetCurChairGamePlayers), ctx, in)
}

// GetTBeanOrderIds mocks base method.
func (m *MockChannelWeddingMinigameServer) GetTBeanOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTBeanOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanOrderIds indicates an expected call of GetTBeanOrderIds.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GetTBeanOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanOrderIds", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GetTBeanOrderIds), ctx, in)
}

// GetTBeanTotalCount mocks base method.
func (m *MockChannelWeddingMinigameServer) GetTBeanTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTBeanTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTBeanTotalCount indicates an expected call of GetTBeanTotalCount.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GetTBeanTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTBeanTotalCount", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GetTBeanTotalCount), ctx, in)
}

// GrabChair mocks base method.
func (m *MockChannelWeddingMinigameServer) GrabChair(ctx context.Context, in *GrabChairRequest) (*GrabChairResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GrabChair", ctx, in)
	ret0, _ := ret[0].(*GrabChairResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GrabChair indicates an expected call of GrabChair.
func (mr *MockChannelWeddingMinigameServerMockRecorder) GrabChair(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrabChair", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).GrabChair), ctx, in)
}

// PushCurChairGameInfo mocks base method.
func (m *MockChannelWeddingMinigameServer) PushCurChairGameInfo(ctx context.Context, in *PushCurChairGameInfoReq) (*PushCurChairGameInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushCurChairGameInfo", ctx, in)
	ret0, _ := ret[0].(*PushCurChairGameInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PushCurChairGameInfo indicates an expected call of PushCurChairGameInfo.
func (mr *MockChannelWeddingMinigameServerMockRecorder) PushCurChairGameInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushCurChairGameInfo", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).PushCurChairGameInfo), ctx, in)
}

// ReissueAward mocks base method.
func (m *MockChannelWeddingMinigameServer) ReissueAward(ctx context.Context, in *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReissueAward", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReissueAward indicates an expected call of ReissueAward.
func (mr *MockChannelWeddingMinigameServerMockRecorder) ReissueAward(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReissueAward", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).ReissueAward), ctx, in)
}

// SetChairGameReward mocks base method.
func (m *MockChannelWeddingMinigameServer) SetChairGameReward(ctx context.Context, in *SetChairGameRewardRequest) (*SetChairGameRewardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChairGameReward", ctx, in)
	ret0, _ := ret[0].(*SetChairGameRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChairGameReward indicates an expected call of SetChairGameReward.
func (mr *MockChannelWeddingMinigameServerMockRecorder) SetChairGameReward(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChairGameReward", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).SetChairGameReward), ctx, in)
}

// SetChairGameToNextRound mocks base method.
func (m *MockChannelWeddingMinigameServer) SetChairGameToNextRound(ctx context.Context, in *SetChairGameToNextRoundRequest) (*SetChairGameToNextRoundResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChairGameToNextRound", ctx, in)
	ret0, _ := ret[0].(*SetChairGameToNextRoundResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChairGameToNextRound indicates an expected call of SetChairGameToNextRound.
func (mr *MockChannelWeddingMinigameServerMockRecorder) SetChairGameToNextRound(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChairGameToNextRound", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).SetChairGameToNextRound), ctx, in)
}

// StartChairGame mocks base method.
func (m *MockChannelWeddingMinigameServer) StartChairGame(ctx context.Context, in *StartChairGameRequest) (*StartChairGameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartChairGame", ctx, in)
	ret0, _ := ret[0].(*StartChairGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartChairGame indicates an expected call of StartChairGame.
func (mr *MockChannelWeddingMinigameServerMockRecorder) StartChairGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartChairGame", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).StartChairGame), ctx, in)
}

// StartGrabChair mocks base method.
func (m *MockChannelWeddingMinigameServer) StartGrabChair(ctx context.Context, in *StartGrabChairRequest) (*StartGrabChairResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartGrabChair", ctx, in)
	ret0, _ := ret[0].(*StartGrabChairResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartGrabChair indicates an expected call of StartGrabChair.
func (mr *MockChannelWeddingMinigameServerMockRecorder) StartGrabChair(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartGrabChair", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).StartGrabChair), ctx, in)
}

// StartNewGamePreCheck mocks base method.
func (m *MockChannelWeddingMinigameServer) StartNewGamePreCheck(ctx context.Context, in *StartChairGameRequest) (*StartChairGameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartNewGamePreCheck", ctx, in)
	ret0, _ := ret[0].(*StartChairGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartNewGamePreCheck indicates an expected call of StartNewGamePreCheck.
func (mr *MockChannelWeddingMinigameServerMockRecorder) StartNewGamePreCheck(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartNewGamePreCheck", reflect.TypeOf((*MockChannelWeddingMinigameServer)(nil).StartNewGamePreCheck), ctx, in)
}
