// Code generated by protoc-gen-gogo.
// source: services/2019NewYearBeat/newyearbeat2019.proto
// DO NOT EDIT!

/*
	Package newyearbeat2019 is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/2019NewYearBeat/newyearbeat2019.proto

	It has these top-level messages:
		GetActConfigReq
		GetActConfigResp
		ReportBeatResultReq
		ReportBeatResultResp
*/
package newyearbeat2019

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

//
type GetActConfigReq struct {
}

func (m *GetActConfigReq) Reset()                    { *m = GetActConfigReq{} }
func (m *GetActConfigReq) String() string            { return proto.CompactTextString(m) }
func (*GetActConfigReq) ProtoMessage()               {}
func (*GetActConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorNewyearbeat2019, []int{0} }

type GetActConfigResp struct {
	ActBeginTs          uint32 `protobuf:"varint,1,opt,name=act_begin_ts,json=actBeginTs,proto3" json:"act_begin_ts,omitempty"`
	ActEndTs            uint32 `protobuf:"varint,2,opt,name=act_end_ts,json=actEndTs,proto3" json:"act_end_ts,omitempty"`
	IntervalPeriodTs    uint32 `protobuf:"varint,3,opt,name=interval_period_ts,json=intervalPeriodTs,proto3" json:"interval_period_ts,omitempty"`
	PayedNewyearCallsTs uint32 `protobuf:"varint,4,opt,name=payed_newyear_calls_ts,json=payedNewyearCallsTs,proto3" json:"payed_newyear_calls_ts,omitempty"`
}

func (m *GetActConfigResp) Reset()                    { *m = GetActConfigResp{} }
func (m *GetActConfigResp) String() string            { return proto.CompactTextString(m) }
func (*GetActConfigResp) ProtoMessage()               {}
func (*GetActConfigResp) Descriptor() ([]byte, []int) { return fileDescriptorNewyearbeat2019, []int{1} }

func (m *GetActConfigResp) GetActBeginTs() uint32 {
	if m != nil {
		return m.ActBeginTs
	}
	return 0
}

func (m *GetActConfigResp) GetActEndTs() uint32 {
	if m != nil {
		return m.ActEndTs
	}
	return 0
}

func (m *GetActConfigResp) GetIntervalPeriodTs() uint32 {
	if m != nil {
		return m.IntervalPeriodTs
	}
	return 0
}

func (m *GetActConfigResp) GetPayedNewyearCallsTs() uint32 {
	if m != nil {
		return m.PayedNewyearCallsTs
	}
	return 0
}

type ReportBeatResultReq struct {
	Uid     uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid     uint32 `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	BeatCnt uint32 `protobuf:"varint,3,opt,name=beat_cnt,json=beatCnt,proto3" json:"beat_cnt,omitempty"`
}

func (m *ReportBeatResultReq) Reset()         { *m = ReportBeatResultReq{} }
func (m *ReportBeatResultReq) String() string { return proto.CompactTextString(m) }
func (*ReportBeatResultReq) ProtoMessage()    {}
func (*ReportBeatResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNewyearbeat2019, []int{2}
}

func (m *ReportBeatResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportBeatResultReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ReportBeatResultReq) GetBeatCnt() uint32 {
	if m != nil {
		return m.BeatCnt
	}
	return 0
}

type ReportBeatResultResp struct {
}

func (m *ReportBeatResultResp) Reset()         { *m = ReportBeatResultResp{} }
func (m *ReportBeatResultResp) String() string { return proto.CompactTextString(m) }
func (*ReportBeatResultResp) ProtoMessage()    {}
func (*ReportBeatResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNewyearbeat2019, []int{3}
}

func init() {
	proto.RegisterType((*GetActConfigReq)(nil), "newyearbeat2019.GetActConfigReq")
	proto.RegisterType((*GetActConfigResp)(nil), "newyearbeat2019.GetActConfigResp")
	proto.RegisterType((*ReportBeatResultReq)(nil), "newyearbeat2019.ReportBeatResultReq")
	proto.RegisterType((*ReportBeatResultResp)(nil), "newyearbeat2019.ReportBeatResultResp")
}
func (m *GetActConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetActConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ActBeginTs != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintNewyearbeat2019(dAtA, i, uint64(m.ActBeginTs))
	}
	if m.ActEndTs != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintNewyearbeat2019(dAtA, i, uint64(m.ActEndTs))
	}
	if m.IntervalPeriodTs != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintNewyearbeat2019(dAtA, i, uint64(m.IntervalPeriodTs))
	}
	if m.PayedNewyearCallsTs != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintNewyearbeat2019(dAtA, i, uint64(m.PayedNewyearCallsTs))
	}
	return i, nil
}

func (m *ReportBeatResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportBeatResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintNewyearbeat2019(dAtA, i, uint64(m.Uid))
	}
	if m.Cid != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintNewyearbeat2019(dAtA, i, uint64(m.Cid))
	}
	if m.BeatCnt != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintNewyearbeat2019(dAtA, i, uint64(m.BeatCnt))
	}
	return i, nil
}

func (m *ReportBeatResultResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportBeatResultResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Newyearbeat2019(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Newyearbeat2019(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintNewyearbeat2019(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetActConfigReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetActConfigResp) Size() (n int) {
	var l int
	_ = l
	if m.ActBeginTs != 0 {
		n += 1 + sovNewyearbeat2019(uint64(m.ActBeginTs))
	}
	if m.ActEndTs != 0 {
		n += 1 + sovNewyearbeat2019(uint64(m.ActEndTs))
	}
	if m.IntervalPeriodTs != 0 {
		n += 1 + sovNewyearbeat2019(uint64(m.IntervalPeriodTs))
	}
	if m.PayedNewyearCallsTs != 0 {
		n += 1 + sovNewyearbeat2019(uint64(m.PayedNewyearCallsTs))
	}
	return n
}

func (m *ReportBeatResultReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovNewyearbeat2019(uint64(m.Uid))
	}
	if m.Cid != 0 {
		n += 1 + sovNewyearbeat2019(uint64(m.Cid))
	}
	if m.BeatCnt != 0 {
		n += 1 + sovNewyearbeat2019(uint64(m.BeatCnt))
	}
	return n
}

func (m *ReportBeatResultResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovNewyearbeat2019(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozNewyearbeat2019(x uint64) (n int) {
	return sovNewyearbeat2019(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GetActConfigReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewyearbeat2019
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNewyearbeat2019(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewyearbeat2019
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActConfigResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewyearbeat2019
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActBeginTs", wireType)
			}
			m.ActBeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewyearbeat2019
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActBeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActEndTs", wireType)
			}
			m.ActEndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewyearbeat2019
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActEndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IntervalPeriodTs", wireType)
			}
			m.IntervalPeriodTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewyearbeat2019
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IntervalPeriodTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PayedNewyearCallsTs", wireType)
			}
			m.PayedNewyearCallsTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewyearbeat2019
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PayedNewyearCallsTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNewyearbeat2019(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewyearbeat2019
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportBeatResultReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewyearbeat2019
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportBeatResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportBeatResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewyearbeat2019
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewyearbeat2019
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeatCnt", wireType)
			}
			m.BeatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewyearbeat2019
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNewyearbeat2019(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewyearbeat2019
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportBeatResultResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewyearbeat2019
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportBeatResultResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportBeatResultResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNewyearbeat2019(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewyearbeat2019
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipNewyearbeat2019(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowNewyearbeat2019
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNewyearbeat2019
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNewyearbeat2019
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthNewyearbeat2019
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowNewyearbeat2019
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipNewyearbeat2019(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthNewyearbeat2019 = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowNewyearbeat2019   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/2019NewYearBeat/newyearbeat2019.proto", fileDescriptorNewyearbeat2019)
}

var fileDescriptorNewyearbeat2019 = []byte{
	// 440 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x92, 0x41, 0x6b, 0xd4, 0x40,
	0x14, 0xc7, 0x9b, 0xa6, 0xd4, 0xf2, 0xa8, 0x6c, 0x9c, 0xca, 0xa2, 0x8b, 0x2c, 0x31, 0x54, 0x50,
	0xb0, 0xbb, 0xda, 0x9e, 0x2c, 0xa5, 0xe0, 0x2e, 0xe2, 0xad, 0x48, 0xc8, 0xc5, 0x53, 0x98, 0x9d,
	0x3c, 0xcb, 0xd0, 0x74, 0x32, 0xcd, 0xbc, 0xec, 0x76, 0x6f, 0x82, 0x17, 0x29, 0x1e, 0xc4, 0xcf,
	0xb0, 0x5f, 0xc1, 0xef, 0xe0, 0xd1, 0x8f, 0x20, 0xeb, 0x65, 0x3f, 0x86, 0xbc, 0x74, 0x05, 0xcd,
	0x0a, 0xf6, 0x12, 0x66, 0xde, 0xef, 0xfd, 0xdf, 0xfb, 0xe7, 0x9f, 0x40, 0xcf, 0x61, 0x39, 0xd6,
	0x0a, 0x5d, 0x7f, 0xff, 0xd9, 0xf3, 0x17, 0x27, 0x38, 0x79, 0x8b, 0xb2, 0x1c, 0xa0, 0xa4, 0xbe,
	0xc1, 0xc9, 0x14, 0x65, 0x39, 0x42, 0x49, 0x8c, 0x7a, 0xb6, 0x2c, 0xa8, 0x10, 0xad, 0x46, 0xb9,
	0xb3, 0xab, 0x8a, 0xf3, 0xf3, 0xc2, 0xf4, 0x29, 0x1f, 0x5b, 0xad, 0xce, 0x72, 0xec, 0xbb, 0xb3,
	0x51, 0xa5, 0x73, 0xd2, 0x86, 0xa6, 0x16, 0xaf, 0x65, 0xd1, 0x1d, 0x68, 0xbd, 0x46, 0x7a, 0xa9,
	0x68, 0x58, 0x98, 0x77, 0xfa, 0x34, 0xc6, 0x8b, 0xe8, 0xab, 0x07, 0xc1, 0xdf, 0x35, 0x67, 0x45,
	0x08, 0xdb, 0x52, 0x51, 0x3a, 0xc2, 0x53, 0x6d, 0x52, 0x72, 0xf7, 0xbc, 0xd0, 0x7b, 0x7c, 0x3b,
	0x06, 0xa9, 0x68, 0xc0, 0xa5, 0xc4, 0x89, 0x07, 0xc0, 0xb7, 0x14, 0x4d, 0xc6, 0x7c, 0xbd, 0xe6,
	0x5b, 0x52, 0xd1, 0x2b, 0x93, 0x25, 0x4e, 0x3c, 0x05, 0xa1, 0x0d, 0x61, 0x39, 0x96, 0x79, 0x6a,
	0xb1, 0xd4, 0x45, 0xdd, 0xe5, 0xd7, 0x5d, 0xc1, 0x6f, 0xf2, 0xa6, 0x06, 0x89, 0x13, 0x07, 0xd0,
	0xb6, 0x72, 0x8a, 0x59, 0xba, 0x7c, 0xa9, 0x54, 0xc9, 0x3c, 0x77, 0xac, 0xd8, 0xa8, 0x15, 0x3b,
	0x35, 0x3d, 0xb9, 0x86, 0x43, 0x66, 0x89, 0x8b, 0x12, 0xd8, 0x89, 0xd1, 0x16, 0x25, 0x71, 0x4a,
	0x31, 0xba, 0x2a, 0xa7, 0x18, 0x2f, 0x44, 0x00, 0x7e, 0xa5, 0xb3, 0xa5, 0x61, 0x3e, 0x72, 0x45,
	0xe9, 0x6c, 0x69, 0x91, 0x8f, 0xe2, 0x3e, 0x6c, 0x71, 0x6e, 0xa9, 0x32, 0xb4, 0xf4, 0x74, 0x8b,
	0xef, 0x43, 0x43, 0x51, 0x1b, 0xee, 0xae, 0x4e, 0x75, 0x76, 0xff, 0xd3, 0x3a, 0x34, 0x23, 0x17,
	0x19, 0x6c, 0xff, 0x19, 0x9c, 0x08, 0x7b, 0xcd, 0x6f, 0xd5, 0xc8, 0xba, 0xf3, 0xf0, 0x3f, 0x1d,
	0xce, 0x46, 0xad, 0xf7, 0xb3, 0x85, 0xef, 0x5d, 0xcd, 0x16, 0xfe, 0xda, 0x17, 0x7e, 0x88, 0x2b,
	0x0f, 0x82, 0xa6, 0x25, 0xb1, 0xbb, 0x32, 0xe8, 0x1f, 0x59, 0x74, 0x1e, 0xdd, 0xa0, 0xcb, 0xd9,
	0xe8, 0x09, 0xaf, 0x04, 0x5e, 0xb9, 0x51, 0x1d, 0x5e, 0x1e, 0xf2, 0xda, 0xf6, 0x5e, 0x15, 0x1e,
	0x55, 0x3a, 0x3b, 0x0e, 0xf7, 0x2e, 0xc3, 0x23, 0x96, 0x87, 0xca, 0xd0, 0x71, 0x67, 0xf3, 0xe3,
	0x6c, 0xe1, 0x7f, 0x98, 0x0c, 0x82, 0x6f, 0xf3, 0xae, 0xf7, 0x7d, 0xde, 0xf5, 0x7e, 0xcc, 0xbb,
	0xde, 0xe7, 0x9f, 0xdd, 0xb5, 0xd1, 0x66, 0xfd, 0x83, 0x1d, 0xfc, 0x0a, 0x00, 0x00, 0xff, 0xff,
	0x98, 0x72, 0x00, 0xdb, 0xc9, 0x02, 0x00, 0x00,
}
