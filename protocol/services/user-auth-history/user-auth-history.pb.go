// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user-auth-history/user-auth-history.proto

package user_auth_history // import "golang.52tt.com/protocol/services/user-auth-history"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LOGIN_OP_TYPE int32

const (
	LOGIN_OP_TYPE_LOGIN_OP_Nil          LOGIN_OP_TYPE = 0
	LOGIN_OP_TYPE_LOGIN_OP_Reg          LOGIN_OP_TYPE = 1
	LOGIN_OP_TYPE_LOGIN_OP_Manual       LOGIN_OP_TYPE = 2
	LOGIN_OP_TYPE_LOGIN_OP_AutoLogin    LOGIN_OP_TYPE = 3
	LOGIN_OP_TYPE_LOGIN_OP_Sdk_Activate LOGIN_OP_TYPE = 4
	LOGIN_OP_TYPE_LOGIN_OP_IOS_Activate LOGIN_OP_TYPE = 5
)

var LOGIN_OP_TYPE_name = map[int32]string{
	0: "LOGIN_OP_Nil",
	1: "LOGIN_OP_Reg",
	2: "LOGIN_OP_Manual",
	3: "LOGIN_OP_AutoLogin",
	4: "LOGIN_OP_Sdk_Activate",
	5: "LOGIN_OP_IOS_Activate",
}
var LOGIN_OP_TYPE_value = map[string]int32{
	"LOGIN_OP_Nil":          0,
	"LOGIN_OP_Reg":          1,
	"LOGIN_OP_Manual":       2,
	"LOGIN_OP_AutoLogin":    3,
	"LOGIN_OP_Sdk_Activate": 4,
	"LOGIN_OP_IOS_Activate": 5,
}

func (x LOGIN_OP_TYPE) String() string {
	return proto.EnumName(LOGIN_OP_TYPE_name, int32(x))
}
func (LOGIN_OP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{0}
}

type UserLoginInfo struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OpType               uint32   `protobuf:"varint,2,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	Result               int32    `protobuf:"varint,3,opt,name=result,proto3" json:"result,omitempty"`
	Phone                string   `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	ThirdPartyType       uint32   `protobuf:"varint,5,opt,name=third_party_type,json=thirdPartyType,proto3" json:"third_party_type,omitempty"`
	Openid               string   `protobuf:"bytes,6,opt,name=openid,proto3" json:"openid,omitempty"`
	Imei                 string   `protobuf:"bytes,10,opt,name=imei,proto3" json:"imei,omitempty"`
	OsVer                string   `protobuf:"bytes,11,opt,name=os_ver,json=osVer,proto3" json:"os_ver,omitempty"`
	OsType               string   `protobuf:"bytes,12,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	DeviceModel          string   `protobuf:"bytes,13,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`
	Signature            string   `protobuf:"bytes,14,opt,name=signature,proto3" json:"signature,omitempty"`
	DeviceInfo           string   `protobuf:"bytes,15,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`
	IsEmulator           uint32   `protobuf:"varint,16,opt,name=is_emulator,json=isEmulator,proto3" json:"is_emulator,omitempty"`
	DeviceId             string   `protobuf:"bytes,17,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientVer            uint32   `protobuf:"varint,18,opt,name=client_ver,json=clientVer,proto3" json:"client_ver,omitempty"`
	ClientIp             string   `protobuf:"bytes,19,opt,name=clientIp,proto3" json:"clientIp,omitempty"`
	ClientChannelId      string   `protobuf:"bytes,20,opt,name=client_channel_id,json=clientChannelId,proto3" json:"client_channel_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,21,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	TerminalType         uint32   `protobuf:"varint,22,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	IsQuickLoginFromSdk  bool     `protobuf:"varint,23,opt,name=is_quick_login_from_sdk,json=isQuickLoginFromSdk,proto3" json:"is_quick_login_from_sdk,omitempty"`
	LoginTime            string   `protobuf:"bytes,24,opt,name=login_time,json=loginTime,proto3" json:"login_time,omitempty"`
	ClientPort           int32    `protobuf:"varint,25,opt,name=client_port,json=clientPort,proto3" json:"client_port,omitempty"`
	Idfa                 string   `protobuf:"bytes,26,opt,name=idfa,proto3" json:"idfa,omitempty"`
	LoginAccount         string   `protobuf:"bytes,27,opt,name=login_account,json=loginAccount,proto3" json:"login_account,omitempty"`
	Username             string   `protobuf:"bytes,28,opt,name=username,proto3" json:"username,omitempty"`
	Alias                string   `protobuf:"bytes,29,opt,name=alias,proto3" json:"alias,omitempty"`
	Nickname             string   `protobuf:"bytes,30,opt,name=nickname,proto3" json:"nickname,omitempty"`
	UserSignature        string   `protobuf:"bytes,31,opt,name=user_signature,json=userSignature,proto3" json:"user_signature,omitempty"`
	ProxyIp              string   `protobuf:"bytes,32,opt,name=proxy_ip,json=proxyIp,proto3" json:"proxy_ip,omitempty"`
	ProxyPort            uint32   `protobuf:"varint,33,opt,name=proxy_port,json=proxyPort,proto3" json:"proxy_port,omitempty"`
	ClientId             uint32   `protobuf:"varint,34,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,35,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLoginInfo) Reset()         { *m = UserLoginInfo{} }
func (m *UserLoginInfo) String() string { return proto.CompactTextString(m) }
func (*UserLoginInfo) ProtoMessage()    {}
func (*UserLoginInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{0}
}
func (m *UserLoginInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLoginInfo.Unmarshal(m, b)
}
func (m *UserLoginInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLoginInfo.Marshal(b, m, deterministic)
}
func (dst *UserLoginInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLoginInfo.Merge(dst, src)
}
func (m *UserLoginInfo) XXX_Size() int {
	return xxx_messageInfo_UserLoginInfo.Size(m)
}
func (m *UserLoginInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLoginInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserLoginInfo proto.InternalMessageInfo

func (m *UserLoginInfo) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLoginInfo) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *UserLoginInfo) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *UserLoginInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *UserLoginInfo) GetThirdPartyType() uint32 {
	if m != nil {
		return m.ThirdPartyType
	}
	return 0
}

func (m *UserLoginInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *UserLoginInfo) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *UserLoginInfo) GetOsVer() string {
	if m != nil {
		return m.OsVer
	}
	return ""
}

func (m *UserLoginInfo) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *UserLoginInfo) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

func (m *UserLoginInfo) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *UserLoginInfo) GetDeviceInfo() string {
	if m != nil {
		return m.DeviceInfo
	}
	return ""
}

func (m *UserLoginInfo) GetIsEmulator() uint32 {
	if m != nil {
		return m.IsEmulator
	}
	return 0
}

func (m *UserLoginInfo) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserLoginInfo) GetClientVer() uint32 {
	if m != nil {
		return m.ClientVer
	}
	return 0
}

func (m *UserLoginInfo) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *UserLoginInfo) GetClientChannelId() string {
	if m != nil {
		return m.ClientChannelId
	}
	return ""
}

func (m *UserLoginInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *UserLoginInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *UserLoginInfo) GetIsQuickLoginFromSdk() bool {
	if m != nil {
		return m.IsQuickLoginFromSdk
	}
	return false
}

func (m *UserLoginInfo) GetLoginTime() string {
	if m != nil {
		return m.LoginTime
	}
	return ""
}

func (m *UserLoginInfo) GetClientPort() int32 {
	if m != nil {
		return m.ClientPort
	}
	return 0
}

func (m *UserLoginInfo) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *UserLoginInfo) GetLoginAccount() string {
	if m != nil {
		return m.LoginAccount
	}
	return ""
}

func (m *UserLoginInfo) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *UserLoginInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *UserLoginInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserLoginInfo) GetUserSignature() string {
	if m != nil {
		return m.UserSignature
	}
	return ""
}

func (m *UserLoginInfo) GetProxyIp() string {
	if m != nil {
		return m.ProxyIp
	}
	return ""
}

func (m *UserLoginInfo) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *UserLoginInfo) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *UserLoginInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GetUserLoginHistoryReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            int64    `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLoginHistoryReq) Reset()         { *m = GetUserLoginHistoryReq{} }
func (m *GetUserLoginHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginHistoryReq) ProtoMessage()    {}
func (*GetUserLoginHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{1}
}
func (m *GetUserLoginHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginHistoryReq.Unmarshal(m, b)
}
func (m *GetUserLoginHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginHistoryReq.Merge(dst, src)
}
func (m *GetUserLoginHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginHistoryReq.Size(m)
}
func (m *GetUserLoginHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginHistoryReq proto.InternalMessageInfo

func (m *GetUserLoginHistoryReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLoginHistoryReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUserLoginHistoryReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetUserLoginHistoryResp struct {
	LoginHistory         []*UserLoginInfo `protobuf:"bytes,1,rep,name=login_history,json=loginHistory,proto3" json:"login_history,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserLoginHistoryResp) Reset()         { *m = GetUserLoginHistoryResp{} }
func (m *GetUserLoginHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginHistoryResp) ProtoMessage()    {}
func (*GetUserLoginHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{2}
}
func (m *GetUserLoginHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginHistoryResp.Unmarshal(m, b)
}
func (m *GetUserLoginHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginHistoryResp.Merge(dst, src)
}
func (m *GetUserLoginHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginHistoryResp.Size(m)
}
func (m *GetUserLoginHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginHistoryResp proto.InternalMessageInfo

func (m *GetUserLoginHistoryResp) GetLoginHistory() []*UserLoginInfo {
	if m != nil {
		return m.LoginHistory
	}
	return nil
}

type RecordUserLoginReq struct {
	Info                 *UserLoginInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Invalid              uint32         `protobuf:"varint,2,opt,name=invalid,proto3" json:"invalid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RecordUserLoginReq) Reset()         { *m = RecordUserLoginReq{} }
func (m *RecordUserLoginReq) String() string { return proto.CompactTextString(m) }
func (*RecordUserLoginReq) ProtoMessage()    {}
func (*RecordUserLoginReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{3}
}
func (m *RecordUserLoginReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordUserLoginReq.Unmarshal(m, b)
}
func (m *RecordUserLoginReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordUserLoginReq.Marshal(b, m, deterministic)
}
func (dst *RecordUserLoginReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordUserLoginReq.Merge(dst, src)
}
func (m *RecordUserLoginReq) XXX_Size() int {
	return xxx_messageInfo_RecordUserLoginReq.Size(m)
}
func (m *RecordUserLoginReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordUserLoginReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordUserLoginReq proto.InternalMessageInfo

func (m *RecordUserLoginReq) GetInfo() *UserLoginInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *RecordUserLoginReq) GetInvalid() uint32 {
	if m != nil {
		return m.Invalid
	}
	return 0
}

type RecordUserLoginResp struct {
	IsNewUsualDevice     bool     `protobuf:"varint,1,opt,name=is_new_usual_device,json=isNewUsualDevice,proto3" json:"is_new_usual_device,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordUserLoginResp) Reset()         { *m = RecordUserLoginResp{} }
func (m *RecordUserLoginResp) String() string { return proto.CompactTextString(m) }
func (*RecordUserLoginResp) ProtoMessage()    {}
func (*RecordUserLoginResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{4}
}
func (m *RecordUserLoginResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordUserLoginResp.Unmarshal(m, b)
}
func (m *RecordUserLoginResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordUserLoginResp.Marshal(b, m, deterministic)
}
func (dst *RecordUserLoginResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordUserLoginResp.Merge(dst, src)
}
func (m *RecordUserLoginResp) XXX_Size() int {
	return xxx_messageInfo_RecordUserLoginResp.Size(m)
}
func (m *RecordUserLoginResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordUserLoginResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordUserLoginResp proto.InternalMessageInfo

func (m *RecordUserLoginResp) GetIsNewUsualDevice() bool {
	if m != nil {
		return m.IsNewUsualDevice
	}
	return false
}

type UserLoginHit struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LoginAt              int64    `protobuf:"varint,2,opt,name=login_at,json=loginAt,proto3" json:"login_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLoginHit) Reset()         { *m = UserLoginHit{} }
func (m *UserLoginHit) String() string { return proto.CompactTextString(m) }
func (*UserLoginHit) ProtoMessage()    {}
func (*UserLoginHit) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{5}
}
func (m *UserLoginHit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLoginHit.Unmarshal(m, b)
}
func (m *UserLoginHit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLoginHit.Marshal(b, m, deterministic)
}
func (dst *UserLoginHit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLoginHit.Merge(dst, src)
}
func (m *UserLoginHit) XXX_Size() int {
	return xxx_messageInfo_UserLoginHit.Size(m)
}
func (m *UserLoginHit) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLoginHit.DiscardUnknown(m)
}

var xxx_messageInfo_UserLoginHit proto.InternalMessageInfo

func (m *UserLoginHit) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLoginHit) GetLoginAt() int64 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

type GetUserLoginWithDeviceReq struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	BeginLoginAt         int64    `protobuf:"varint,2,opt,name=begin_login_at,json=beginLoginAt,proto3" json:"begin_login_at,omitempty"`
	EndLoginAt           int64    `protobuf:"varint,3,opt,name=end_login_at,json=endLoginAt,proto3" json:"end_login_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLoginWithDeviceReq) Reset()         { *m = GetUserLoginWithDeviceReq{} }
func (m *GetUserLoginWithDeviceReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginWithDeviceReq) ProtoMessage()    {}
func (*GetUserLoginWithDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{6}
}
func (m *GetUserLoginWithDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginWithDeviceReq.Unmarshal(m, b)
}
func (m *GetUserLoginWithDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginWithDeviceReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginWithDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginWithDeviceReq.Merge(dst, src)
}
func (m *GetUserLoginWithDeviceReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginWithDeviceReq.Size(m)
}
func (m *GetUserLoginWithDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginWithDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginWithDeviceReq proto.InternalMessageInfo

func (m *GetUserLoginWithDeviceReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetUserLoginWithDeviceReq) GetBeginLoginAt() int64 {
	if m != nil {
		return m.BeginLoginAt
	}
	return 0
}

func (m *GetUserLoginWithDeviceReq) GetEndLoginAt() int64 {
	if m != nil {
		return m.EndLoginAt
	}
	return 0
}

type GetUserLoginWithDeviceResp struct {
	DeviceId             string          `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	LoginUsers           []*UserLoginHit `protobuf:"bytes,2,rep,name=login_users,json=loginUsers,proto3" json:"login_users,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserLoginWithDeviceResp) Reset()         { *m = GetUserLoginWithDeviceResp{} }
func (m *GetUserLoginWithDeviceResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginWithDeviceResp) ProtoMessage()    {}
func (*GetUserLoginWithDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{7}
}
func (m *GetUserLoginWithDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginWithDeviceResp.Unmarshal(m, b)
}
func (m *GetUserLoginWithDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginWithDeviceResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginWithDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginWithDeviceResp.Merge(dst, src)
}
func (m *GetUserLoginWithDeviceResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginWithDeviceResp.Size(m)
}
func (m *GetUserLoginWithDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginWithDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginWithDeviceResp proto.InternalMessageInfo

func (m *GetUserLoginWithDeviceResp) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetUserLoginWithDeviceResp) GetLoginUsers() []*UserLoginHit {
	if m != nil {
		return m.LoginUsers
	}
	return nil
}

type TrackUserLoginReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LoginAt              int64    `protobuf:"varint,2,opt,name=login_at,json=loginAt,proto3" json:"login_at,omitempty"`
	DeviceId             string   `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Imei                 string   `protobuf:"bytes,4,opt,name=imei,proto3" json:"imei,omitempty"`
	Ip                   string   `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TrackUserLoginReq) Reset()         { *m = TrackUserLoginReq{} }
func (m *TrackUserLoginReq) String() string { return proto.CompactTextString(m) }
func (*TrackUserLoginReq) ProtoMessage()    {}
func (*TrackUserLoginReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{8}
}
func (m *TrackUserLoginReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrackUserLoginReq.Unmarshal(m, b)
}
func (m *TrackUserLoginReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrackUserLoginReq.Marshal(b, m, deterministic)
}
func (dst *TrackUserLoginReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrackUserLoginReq.Merge(dst, src)
}
func (m *TrackUserLoginReq) XXX_Size() int {
	return xxx_messageInfo_TrackUserLoginReq.Size(m)
}
func (m *TrackUserLoginReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TrackUserLoginReq.DiscardUnknown(m)
}

var xxx_messageInfo_TrackUserLoginReq proto.InternalMessageInfo

func (m *TrackUserLoginReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TrackUserLoginReq) GetLoginAt() int64 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

func (m *TrackUserLoginReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *TrackUserLoginReq) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *TrackUserLoginReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type TrackUserLoginResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TrackUserLoginResp) Reset()         { *m = TrackUserLoginResp{} }
func (m *TrackUserLoginResp) String() string { return proto.CompactTextString(m) }
func (*TrackUserLoginResp) ProtoMessage()    {}
func (*TrackUserLoginResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{9}
}
func (m *TrackUserLoginResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrackUserLoginResp.Unmarshal(m, b)
}
func (m *TrackUserLoginResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrackUserLoginResp.Marshal(b, m, deterministic)
}
func (dst *TrackUserLoginResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrackUserLoginResp.Merge(dst, src)
}
func (m *TrackUserLoginResp) XXX_Size() int {
	return xxx_messageInfo_TrackUserLoginResp.Size(m)
}
func (m *TrackUserLoginResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TrackUserLoginResp.DiscardUnknown(m)
}

var xxx_messageInfo_TrackUserLoginResp proto.InternalMessageInfo

type IsUserInvalidReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsUserInvalidReq) Reset()         { *m = IsUserInvalidReq{} }
func (m *IsUserInvalidReq) String() string { return proto.CompactTextString(m) }
func (*IsUserInvalidReq) ProtoMessage()    {}
func (*IsUserInvalidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{10}
}
func (m *IsUserInvalidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsUserInvalidReq.Unmarshal(m, b)
}
func (m *IsUserInvalidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsUserInvalidReq.Marshal(b, m, deterministic)
}
func (dst *IsUserInvalidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsUserInvalidReq.Merge(dst, src)
}
func (m *IsUserInvalidReq) XXX_Size() int {
	return xxx_messageInfo_IsUserInvalidReq.Size(m)
}
func (m *IsUserInvalidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsUserInvalidReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsUserInvalidReq proto.InternalMessageInfo

func (m *IsUserInvalidReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsUserInvalidResp struct {
	Invalid              bool     `protobuf:"varint,1,opt,name=invalid,proto3" json:"invalid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsUserInvalidResp) Reset()         { *m = IsUserInvalidResp{} }
func (m *IsUserInvalidResp) String() string { return proto.CompactTextString(m) }
func (*IsUserInvalidResp) ProtoMessage()    {}
func (*IsUserInvalidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{11}
}
func (m *IsUserInvalidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsUserInvalidResp.Unmarshal(m, b)
}
func (m *IsUserInvalidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsUserInvalidResp.Marshal(b, m, deterministic)
}
func (dst *IsUserInvalidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsUserInvalidResp.Merge(dst, src)
}
func (m *IsUserInvalidResp) XXX_Size() int {
	return xxx_messageInfo_IsUserInvalidResp.Size(m)
}
func (m *IsUserInvalidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsUserInvalidResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsUserInvalidResp proto.InternalMessageInfo

func (m *IsUserInvalidResp) GetInvalid() bool {
	if m != nil {
		return m.Invalid
	}
	return false
}

type GetUserLoginDeviceReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLoginDeviceReq) Reset()         { *m = GetUserLoginDeviceReq{} }
func (m *GetUserLoginDeviceReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginDeviceReq) ProtoMessage()    {}
func (*GetUserLoginDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{12}
}
func (m *GetUserLoginDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginDeviceReq.Unmarshal(m, b)
}
func (m *GetUserLoginDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginDeviceReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginDeviceReq.Merge(dst, src)
}
func (m *GetUserLoginDeviceReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginDeviceReq.Size(m)
}
func (m *GetUserLoginDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginDeviceReq proto.InternalMessageInfo

func (m *GetUserLoginDeviceReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UserLoginDevice struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	LoginAt              int64    `protobuf:"varint,2,opt,name=login_at,json=loginAt,proto3" json:"login_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLoginDevice) Reset()         { *m = UserLoginDevice{} }
func (m *UserLoginDevice) String() string { return proto.CompactTextString(m) }
func (*UserLoginDevice) ProtoMessage()    {}
func (*UserLoginDevice) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{13}
}
func (m *UserLoginDevice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLoginDevice.Unmarshal(m, b)
}
func (m *UserLoginDevice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLoginDevice.Marshal(b, m, deterministic)
}
func (dst *UserLoginDevice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLoginDevice.Merge(dst, src)
}
func (m *UserLoginDevice) XXX_Size() int {
	return xxx_messageInfo_UserLoginDevice.Size(m)
}
func (m *UserLoginDevice) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLoginDevice.DiscardUnknown(m)
}

var xxx_messageInfo_UserLoginDevice proto.InternalMessageInfo

func (m *UserLoginDevice) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserLoginDevice) GetLoginAt() int64 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

type GetUserLoginDeviceResp struct {
	Uid                  uint64             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LoginDevices         []*UserLoginDevice `protobuf:"bytes,2,rep,name=login_devices,json=loginDevices,proto3" json:"login_devices,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserLoginDeviceResp) Reset()         { *m = GetUserLoginDeviceResp{} }
func (m *GetUserLoginDeviceResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLoginDeviceResp) ProtoMessage()    {}
func (*GetUserLoginDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{14}
}
func (m *GetUserLoginDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLoginDeviceResp.Unmarshal(m, b)
}
func (m *GetUserLoginDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLoginDeviceResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLoginDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLoginDeviceResp.Merge(dst, src)
}
func (m *GetUserLoginDeviceResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLoginDeviceResp.Size(m)
}
func (m *GetUserLoginDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLoginDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLoginDeviceResp proto.InternalMessageInfo

func (m *GetUserLoginDeviceResp) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLoginDeviceResp) GetLoginDevices() []*UserLoginDevice {
	if m != nil {
		return m.LoginDevices
	}
	return nil
}

type GetUserLastLoginInfoReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLastLoginInfoReq) Reset()         { *m = GetUserLastLoginInfoReq{} }
func (m *GetUserLastLoginInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLastLoginInfoReq) ProtoMessage()    {}
func (*GetUserLastLoginInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{15}
}
func (m *GetUserLastLoginInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLastLoginInfoReq.Unmarshal(m, b)
}
func (m *GetUserLastLoginInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLastLoginInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLastLoginInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLastLoginInfoReq.Merge(dst, src)
}
func (m *GetUserLastLoginInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLastLoginInfoReq.Size(m)
}
func (m *GetUserLastLoginInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLastLoginInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLastLoginInfoReq proto.InternalMessageInfo

func (m *GetUserLastLoginInfoReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type BatchGetUserLastLoginInfoReq struct {
	UidList              []uint64 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserLastLoginInfoReq) Reset()         { *m = BatchGetUserLastLoginInfoReq{} }
func (m *BatchGetUserLastLoginInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserLastLoginInfoReq) ProtoMessage()    {}
func (*BatchGetUserLastLoginInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{16}
}
func (m *BatchGetUserLastLoginInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserLastLoginInfoReq.Unmarshal(m, b)
}
func (m *BatchGetUserLastLoginInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserLastLoginInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserLastLoginInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserLastLoginInfoReq.Merge(dst, src)
}
func (m *BatchGetUserLastLoginInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserLastLoginInfoReq.Size(m)
}
func (m *BatchGetUserLastLoginInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserLastLoginInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserLastLoginInfoReq proto.InternalMessageInfo

func (m *BatchGetUserLastLoginInfoReq) GetUidList() []uint64 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserLastLoginInfoResp struct {
	InfoList             []*UserLoginInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetUserLastLoginInfoResp) Reset()         { *m = BatchGetUserLastLoginInfoResp{} }
func (m *BatchGetUserLastLoginInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserLastLoginInfoResp) ProtoMessage()    {}
func (*BatchGetUserLastLoginInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{17}
}
func (m *BatchGetUserLastLoginInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserLastLoginInfoResp.Unmarshal(m, b)
}
func (m *BatchGetUserLastLoginInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserLastLoginInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserLastLoginInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserLastLoginInfoResp.Merge(dst, src)
}
func (m *BatchGetUserLastLoginInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserLastLoginInfoResp.Size(m)
}
func (m *BatchGetUserLastLoginInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserLastLoginInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserLastLoginInfoResp proto.InternalMessageInfo

func (m *BatchGetUserLastLoginInfoResp) GetInfoList() []*UserLoginInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type VerifyCAPTCHASuccessReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	VerifyReason         uint32   `protobuf:"varint,2,opt,name=verify_reason,json=verifyReason,proto3" json:"verify_reason,omitempty"`
	DeviceId             string   `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VerifyCAPTCHASuccessReq) Reset()         { *m = VerifyCAPTCHASuccessReq{} }
func (m *VerifyCAPTCHASuccessReq) String() string { return proto.CompactTextString(m) }
func (*VerifyCAPTCHASuccessReq) ProtoMessage()    {}
func (*VerifyCAPTCHASuccessReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{18}
}
func (m *VerifyCAPTCHASuccessReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VerifyCAPTCHASuccessReq.Unmarshal(m, b)
}
func (m *VerifyCAPTCHASuccessReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VerifyCAPTCHASuccessReq.Marshal(b, m, deterministic)
}
func (dst *VerifyCAPTCHASuccessReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerifyCAPTCHASuccessReq.Merge(dst, src)
}
func (m *VerifyCAPTCHASuccessReq) XXX_Size() int {
	return xxx_messageInfo_VerifyCAPTCHASuccessReq.Size(m)
}
func (m *VerifyCAPTCHASuccessReq) XXX_DiscardUnknown() {
	xxx_messageInfo_VerifyCAPTCHASuccessReq.DiscardUnknown(m)
}

var xxx_messageInfo_VerifyCAPTCHASuccessReq proto.InternalMessageInfo

func (m *VerifyCAPTCHASuccessReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VerifyCAPTCHASuccessReq) GetVerifyReason() uint32 {
	if m != nil {
		return m.VerifyReason
	}
	return 0
}

func (m *VerifyCAPTCHASuccessReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type VerifyCAPTCHASuccessResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VerifyCAPTCHASuccessResp) Reset()         { *m = VerifyCAPTCHASuccessResp{} }
func (m *VerifyCAPTCHASuccessResp) String() string { return proto.CompactTextString(m) }
func (*VerifyCAPTCHASuccessResp) ProtoMessage()    {}
func (*VerifyCAPTCHASuccessResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{19}
}
func (m *VerifyCAPTCHASuccessResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VerifyCAPTCHASuccessResp.Unmarshal(m, b)
}
func (m *VerifyCAPTCHASuccessResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VerifyCAPTCHASuccessResp.Marshal(b, m, deterministic)
}
func (dst *VerifyCAPTCHASuccessResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerifyCAPTCHASuccessResp.Merge(dst, src)
}
func (m *VerifyCAPTCHASuccessResp) XXX_Size() int {
	return xxx_messageInfo_VerifyCAPTCHASuccessResp.Size(m)
}
func (m *VerifyCAPTCHASuccessResp) XXX_DiscardUnknown() {
	xxx_messageInfo_VerifyCAPTCHASuccessResp.DiscardUnknown(m)
}

var xxx_messageInfo_VerifyCAPTCHASuccessResp proto.InternalMessageInfo

type GetLastVerifySuccessInfoReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	VerifyReason         uint32   `protobuf:"varint,2,opt,name=verify_reason,json=verifyReason,proto3" json:"verify_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastVerifySuccessInfoReq) Reset()         { *m = GetLastVerifySuccessInfoReq{} }
func (m *GetLastVerifySuccessInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetLastVerifySuccessInfoReq) ProtoMessage()    {}
func (*GetLastVerifySuccessInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{20}
}
func (m *GetLastVerifySuccessInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastVerifySuccessInfoReq.Unmarshal(m, b)
}
func (m *GetLastVerifySuccessInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastVerifySuccessInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetLastVerifySuccessInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastVerifySuccessInfoReq.Merge(dst, src)
}
func (m *GetLastVerifySuccessInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetLastVerifySuccessInfoReq.Size(m)
}
func (m *GetLastVerifySuccessInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastVerifySuccessInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastVerifySuccessInfoReq proto.InternalMessageInfo

func (m *GetLastVerifySuccessInfoReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLastVerifySuccessInfoReq) GetVerifyReason() uint32 {
	if m != nil {
		return m.VerifyReason
	}
	return 0
}

type GetLastVerifySuccessInfoResp struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Timestamp            int64    `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastVerifySuccessInfoResp) Reset()         { *m = GetLastVerifySuccessInfoResp{} }
func (m *GetLastVerifySuccessInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetLastVerifySuccessInfoResp) ProtoMessage()    {}
func (*GetLastVerifySuccessInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{21}
}
func (m *GetLastVerifySuccessInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastVerifySuccessInfoResp.Unmarshal(m, b)
}
func (m *GetLastVerifySuccessInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastVerifySuccessInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetLastVerifySuccessInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastVerifySuccessInfoResp.Merge(dst, src)
}
func (m *GetLastVerifySuccessInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetLastVerifySuccessInfoResp.Size(m)
}
func (m *GetLastVerifySuccessInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastVerifySuccessInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastVerifySuccessInfoResp proto.InternalMessageInfo

func (m *GetLastVerifySuccessInfoResp) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetLastVerifySuccessInfoResp) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type GetUserRegInfoReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRegInfoReq) Reset()         { *m = GetUserRegInfoReq{} }
func (m *GetUserRegInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRegInfoReq) ProtoMessage()    {}
func (*GetUserRegInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{22}
}
func (m *GetUserRegInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRegInfoReq.Unmarshal(m, b)
}
func (m *GetUserRegInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRegInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRegInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRegInfoReq.Merge(dst, src)
}
func (m *GetUserRegInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRegInfoReq.Size(m)
}
func (m *GetUserRegInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRegInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRegInfoReq proto.InternalMessageInfo

func (m *GetUserRegInfoReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRegInfoResp struct {
	Info                 *UserLoginInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserRegInfoResp) Reset()         { *m = GetUserRegInfoResp{} }
func (m *GetUserRegInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRegInfoResp) ProtoMessage()    {}
func (*GetUserRegInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{23}
}
func (m *GetUserRegInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRegInfoResp.Unmarshal(m, b)
}
func (m *GetUserRegInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRegInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRegInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRegInfoResp.Merge(dst, src)
}
func (m *GetUserRegInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRegInfoResp.Size(m)
}
func (m *GetUserRegInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRegInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRegInfoResp proto.InternalMessageInfo

func (m *GetUserRegInfoResp) GetInfo() *UserLoginInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchGetUserLoginDeviceReq struct {
	Uids                 []uint64 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserLoginDeviceReq) Reset()         { *m = BatchGetUserLoginDeviceReq{} }
func (m *BatchGetUserLoginDeviceReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserLoginDeviceReq) ProtoMessage()    {}
func (*BatchGetUserLoginDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{24}
}
func (m *BatchGetUserLoginDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserLoginDeviceReq.Unmarshal(m, b)
}
func (m *BatchGetUserLoginDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserLoginDeviceReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserLoginDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserLoginDeviceReq.Merge(dst, src)
}
func (m *BatchGetUserLoginDeviceReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserLoginDeviceReq.Size(m)
}
func (m *BatchGetUserLoginDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserLoginDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserLoginDeviceReq proto.InternalMessageInfo

func (m *BatchGetUserLoginDeviceReq) GetUids() []uint64 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchUserLoginDevice struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             string   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	LoginAt              int64    `protobuf:"varint,3,opt,name=login_at,json=loginAt,proto3" json:"login_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUserLoginDevice) Reset()         { *m = BatchUserLoginDevice{} }
func (m *BatchUserLoginDevice) String() string { return proto.CompactTextString(m) }
func (*BatchUserLoginDevice) ProtoMessage()    {}
func (*BatchUserLoginDevice) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{25}
}
func (m *BatchUserLoginDevice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUserLoginDevice.Unmarshal(m, b)
}
func (m *BatchUserLoginDevice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUserLoginDevice.Marshal(b, m, deterministic)
}
func (dst *BatchUserLoginDevice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUserLoginDevice.Merge(dst, src)
}
func (m *BatchUserLoginDevice) XXX_Size() int {
	return xxx_messageInfo_BatchUserLoginDevice.Size(m)
}
func (m *BatchUserLoginDevice) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUserLoginDevice.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUserLoginDevice proto.InternalMessageInfo

func (m *BatchUserLoginDevice) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchUserLoginDevice) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BatchUserLoginDevice) GetLoginAt() int64 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

type BatchGetUserLoginDeviceResp struct {
	LoginDevices         []*BatchUserLoginDevice `protobuf:"bytes,1,rep,name=login_devices,json=loginDevices,proto3" json:"login_devices,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetUserLoginDeviceResp) Reset()         { *m = BatchGetUserLoginDeviceResp{} }
func (m *BatchGetUserLoginDeviceResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserLoginDeviceResp) ProtoMessage()    {}
func (*BatchGetUserLoginDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{26}
}
func (m *BatchGetUserLoginDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserLoginDeviceResp.Unmarshal(m, b)
}
func (m *BatchGetUserLoginDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserLoginDeviceResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserLoginDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserLoginDeviceResp.Merge(dst, src)
}
func (m *BatchGetUserLoginDeviceResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserLoginDeviceResp.Size(m)
}
func (m *BatchGetUserLoginDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserLoginDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserLoginDeviceResp proto.InternalMessageInfo

func (m *BatchGetUserLoginDeviceResp) GetLoginDevices() []*BatchUserLoginDevice {
	if m != nil {
		return m.LoginDevices
	}
	return nil
}

type RegUserInfo struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Phone                string   `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
	RegAt                string   `protobuf:"bytes,3,opt,name=reg_at,json=regAt,proto3" json:"reg_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegUserInfo) Reset()         { *m = RegUserInfo{} }
func (m *RegUserInfo) String() string { return proto.CompactTextString(m) }
func (*RegUserInfo) ProtoMessage()    {}
func (*RegUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{27}
}
func (m *RegUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegUserInfo.Unmarshal(m, b)
}
func (m *RegUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegUserInfo.Marshal(b, m, deterministic)
}
func (dst *RegUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegUserInfo.Merge(dst, src)
}
func (m *RegUserInfo) XXX_Size() int {
	return xxx_messageInfo_RegUserInfo.Size(m)
}
func (m *RegUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RegUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RegUserInfo proto.InternalMessageInfo

func (m *RegUserInfo) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RegUserInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RegUserInfo) GetRegAt() string {
	if m != nil {
		return m.RegAt
	}
	return ""
}

type GetDeviceIdInfoReq struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDeviceIdInfoReq) Reset()         { *m = GetDeviceIdInfoReq{} }
func (m *GetDeviceIdInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetDeviceIdInfoReq) ProtoMessage()    {}
func (*GetDeviceIdInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{28}
}
func (m *GetDeviceIdInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeviceIdInfoReq.Unmarshal(m, b)
}
func (m *GetDeviceIdInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeviceIdInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetDeviceIdInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeviceIdInfoReq.Merge(dst, src)
}
func (m *GetDeviceIdInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetDeviceIdInfoReq.Size(m)
}
func (m *GetDeviceIdInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeviceIdInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeviceIdInfoReq proto.InternalMessageInfo

func (m *GetDeviceIdInfoReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetDeviceIdInfoResp struct {
	RegUserCount         int64          `protobuf:"varint,1,opt,name=reg_user_count,json=regUserCount,proto3" json:"reg_user_count,omitempty"`
	RegInfoList          []*RegUserInfo `protobuf:"bytes,2,rep,name=reg_info_list,json=regInfoList,proto3" json:"reg_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetDeviceIdInfoResp) Reset()         { *m = GetDeviceIdInfoResp{} }
func (m *GetDeviceIdInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetDeviceIdInfoResp) ProtoMessage()    {}
func (*GetDeviceIdInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_auth_history_a324bf55fbafd2ff, []int{29}
}
func (m *GetDeviceIdInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeviceIdInfoResp.Unmarshal(m, b)
}
func (m *GetDeviceIdInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeviceIdInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetDeviceIdInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeviceIdInfoResp.Merge(dst, src)
}
func (m *GetDeviceIdInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetDeviceIdInfoResp.Size(m)
}
func (m *GetDeviceIdInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeviceIdInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeviceIdInfoResp proto.InternalMessageInfo

func (m *GetDeviceIdInfoResp) GetRegUserCount() int64 {
	if m != nil {
		return m.RegUserCount
	}
	return 0
}

func (m *GetDeviceIdInfoResp) GetRegInfoList() []*RegUserInfo {
	if m != nil {
		return m.RegInfoList
	}
	return nil
}

func init() {
	proto.RegisterType((*UserLoginInfo)(nil), "user_auth_history.UserLoginInfo")
	proto.RegisterType((*GetUserLoginHistoryReq)(nil), "user_auth_history.GetUserLoginHistoryReq")
	proto.RegisterType((*GetUserLoginHistoryResp)(nil), "user_auth_history.GetUserLoginHistoryResp")
	proto.RegisterType((*RecordUserLoginReq)(nil), "user_auth_history.RecordUserLoginReq")
	proto.RegisterType((*RecordUserLoginResp)(nil), "user_auth_history.RecordUserLoginResp")
	proto.RegisterType((*UserLoginHit)(nil), "user_auth_history.UserLoginHit")
	proto.RegisterType((*GetUserLoginWithDeviceReq)(nil), "user_auth_history.GetUserLoginWithDeviceReq")
	proto.RegisterType((*GetUserLoginWithDeviceResp)(nil), "user_auth_history.GetUserLoginWithDeviceResp")
	proto.RegisterType((*TrackUserLoginReq)(nil), "user_auth_history.TrackUserLoginReq")
	proto.RegisterType((*TrackUserLoginResp)(nil), "user_auth_history.TrackUserLoginResp")
	proto.RegisterType((*IsUserInvalidReq)(nil), "user_auth_history.IsUserInvalidReq")
	proto.RegisterType((*IsUserInvalidResp)(nil), "user_auth_history.IsUserInvalidResp")
	proto.RegisterType((*GetUserLoginDeviceReq)(nil), "user_auth_history.GetUserLoginDeviceReq")
	proto.RegisterType((*UserLoginDevice)(nil), "user_auth_history.UserLoginDevice")
	proto.RegisterType((*GetUserLoginDeviceResp)(nil), "user_auth_history.GetUserLoginDeviceResp")
	proto.RegisterType((*GetUserLastLoginInfoReq)(nil), "user_auth_history.GetUserLastLoginInfoReq")
	proto.RegisterType((*BatchGetUserLastLoginInfoReq)(nil), "user_auth_history.BatchGetUserLastLoginInfoReq")
	proto.RegisterType((*BatchGetUserLastLoginInfoResp)(nil), "user_auth_history.BatchGetUserLastLoginInfoResp")
	proto.RegisterType((*VerifyCAPTCHASuccessReq)(nil), "user_auth_history.VerifyCAPTCHASuccessReq")
	proto.RegisterType((*VerifyCAPTCHASuccessResp)(nil), "user_auth_history.VerifyCAPTCHASuccessResp")
	proto.RegisterType((*GetLastVerifySuccessInfoReq)(nil), "user_auth_history.GetLastVerifySuccessInfoReq")
	proto.RegisterType((*GetLastVerifySuccessInfoResp)(nil), "user_auth_history.GetLastVerifySuccessInfoResp")
	proto.RegisterType((*GetUserRegInfoReq)(nil), "user_auth_history.GetUserRegInfoReq")
	proto.RegisterType((*GetUserRegInfoResp)(nil), "user_auth_history.GetUserRegInfoResp")
	proto.RegisterType((*BatchGetUserLoginDeviceReq)(nil), "user_auth_history.BatchGetUserLoginDeviceReq")
	proto.RegisterType((*BatchUserLoginDevice)(nil), "user_auth_history.BatchUserLoginDevice")
	proto.RegisterType((*BatchGetUserLoginDeviceResp)(nil), "user_auth_history.BatchGetUserLoginDeviceResp")
	proto.RegisterType((*RegUserInfo)(nil), "user_auth_history.RegUserInfo")
	proto.RegisterType((*GetDeviceIdInfoReq)(nil), "user_auth_history.GetDeviceIdInfoReq")
	proto.RegisterType((*GetDeviceIdInfoResp)(nil), "user_auth_history.GetDeviceIdInfoResp")
	proto.RegisterEnum("user_auth_history.LOGIN_OP_TYPE", LOGIN_OP_TYPE_name, LOGIN_OP_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserAuthHistoryClient is the client API for UserAuthHistory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserAuthHistoryClient interface {
	GetUserLoginHistory(ctx context.Context, in *GetUserLoginHistoryReq, opts ...grpc.CallOption) (*GetUserLoginHistoryResp, error)
	RecordUserLogin(ctx context.Context, in *RecordUserLoginReq, opts ...grpc.CallOption) (*RecordUserLoginResp, error)
	GetUserLoginWithDevice(ctx context.Context, in *GetUserLoginWithDeviceReq, opts ...grpc.CallOption) (*GetUserLoginWithDeviceResp, error)
	GetUserLoginDevice(ctx context.Context, in *GetUserLoginDeviceReq, opts ...grpc.CallOption) (*GetUserLoginDeviceResp, error)
	BatchGetUserLoginDevice(ctx context.Context, in *BatchGetUserLoginDeviceReq, opts ...grpc.CallOption) (*BatchGetUserLoginDeviceResp, error)
	GetUserLastLoginInfo(ctx context.Context, in *GetUserLastLoginInfoReq, opts ...grpc.CallOption) (*UserLoginInfo, error)
	BatchGetUserLastLoginInfo(ctx context.Context, in *BatchGetUserLastLoginInfoReq, opts ...grpc.CallOption) (*BatchGetUserLastLoginInfoResp, error)
	VerifyCAPTCHASuccess(ctx context.Context, in *VerifyCAPTCHASuccessReq, opts ...grpc.CallOption) (*VerifyCAPTCHASuccessResp, error)
	GetLastVerifySuccessInfo(ctx context.Context, in *GetLastVerifySuccessInfoReq, opts ...grpc.CallOption) (*GetLastVerifySuccessInfoResp, error)
	GetUserRegInfo(ctx context.Context, in *GetUserRegInfoReq, opts ...grpc.CallOption) (*GetUserRegInfoResp, error)
	GetDeviceIdInfo(ctx context.Context, in *GetDeviceIdInfoReq, opts ...grpc.CallOption) (*GetDeviceIdInfoResp, error)
	TrackUserLogin(ctx context.Context, in *TrackUserLoginReq, opts ...grpc.CallOption) (*TrackUserLoginResp, error)
	IsUserInvalid(ctx context.Context, in *IsUserInvalidReq, opts ...grpc.CallOption) (*IsUserInvalidResp, error)
}

type userAuthHistoryClient struct {
	cc *grpc.ClientConn
}

func NewUserAuthHistoryClient(cc *grpc.ClientConn) UserAuthHistoryClient {
	return &userAuthHistoryClient{cc}
}

func (c *userAuthHistoryClient) GetUserLoginHistory(ctx context.Context, in *GetUserLoginHistoryReq, opts ...grpc.CallOption) (*GetUserLoginHistoryResp, error) {
	out := new(GetUserLoginHistoryResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/GetUserLoginHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) RecordUserLogin(ctx context.Context, in *RecordUserLoginReq, opts ...grpc.CallOption) (*RecordUserLoginResp, error) {
	out := new(RecordUserLoginResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/RecordUserLogin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) GetUserLoginWithDevice(ctx context.Context, in *GetUserLoginWithDeviceReq, opts ...grpc.CallOption) (*GetUserLoginWithDeviceResp, error) {
	out := new(GetUserLoginWithDeviceResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/GetUserLoginWithDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) GetUserLoginDevice(ctx context.Context, in *GetUserLoginDeviceReq, opts ...grpc.CallOption) (*GetUserLoginDeviceResp, error) {
	out := new(GetUserLoginDeviceResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/GetUserLoginDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) BatchGetUserLoginDevice(ctx context.Context, in *BatchGetUserLoginDeviceReq, opts ...grpc.CallOption) (*BatchGetUserLoginDeviceResp, error) {
	out := new(BatchGetUserLoginDeviceResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/BatchGetUserLoginDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) GetUserLastLoginInfo(ctx context.Context, in *GetUserLastLoginInfoReq, opts ...grpc.CallOption) (*UserLoginInfo, error) {
	out := new(UserLoginInfo)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/GetUserLastLoginInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) BatchGetUserLastLoginInfo(ctx context.Context, in *BatchGetUserLastLoginInfoReq, opts ...grpc.CallOption) (*BatchGetUserLastLoginInfoResp, error) {
	out := new(BatchGetUserLastLoginInfoResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/BatchGetUserLastLoginInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) VerifyCAPTCHASuccess(ctx context.Context, in *VerifyCAPTCHASuccessReq, opts ...grpc.CallOption) (*VerifyCAPTCHASuccessResp, error) {
	out := new(VerifyCAPTCHASuccessResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/VerifyCAPTCHASuccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) GetLastVerifySuccessInfo(ctx context.Context, in *GetLastVerifySuccessInfoReq, opts ...grpc.CallOption) (*GetLastVerifySuccessInfoResp, error) {
	out := new(GetLastVerifySuccessInfoResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/GetLastVerifySuccessInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) GetUserRegInfo(ctx context.Context, in *GetUserRegInfoReq, opts ...grpc.CallOption) (*GetUserRegInfoResp, error) {
	out := new(GetUserRegInfoResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/GetUserRegInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) GetDeviceIdInfo(ctx context.Context, in *GetDeviceIdInfoReq, opts ...grpc.CallOption) (*GetDeviceIdInfoResp, error) {
	out := new(GetDeviceIdInfoResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/GetDeviceIdInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) TrackUserLogin(ctx context.Context, in *TrackUserLoginReq, opts ...grpc.CallOption) (*TrackUserLoginResp, error) {
	out := new(TrackUserLoginResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/TrackUserLogin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAuthHistoryClient) IsUserInvalid(ctx context.Context, in *IsUserInvalidReq, opts ...grpc.CallOption) (*IsUserInvalidResp, error) {
	out := new(IsUserInvalidResp)
	err := c.cc.Invoke(ctx, "/user_auth_history.UserAuthHistory/IsUserInvalid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserAuthHistoryServer is the server API for UserAuthHistory service.
type UserAuthHistoryServer interface {
	GetUserLoginHistory(context.Context, *GetUserLoginHistoryReq) (*GetUserLoginHistoryResp, error)
	RecordUserLogin(context.Context, *RecordUserLoginReq) (*RecordUserLoginResp, error)
	GetUserLoginWithDevice(context.Context, *GetUserLoginWithDeviceReq) (*GetUserLoginWithDeviceResp, error)
	GetUserLoginDevice(context.Context, *GetUserLoginDeviceReq) (*GetUserLoginDeviceResp, error)
	BatchGetUserLoginDevice(context.Context, *BatchGetUserLoginDeviceReq) (*BatchGetUserLoginDeviceResp, error)
	GetUserLastLoginInfo(context.Context, *GetUserLastLoginInfoReq) (*UserLoginInfo, error)
	BatchGetUserLastLoginInfo(context.Context, *BatchGetUserLastLoginInfoReq) (*BatchGetUserLastLoginInfoResp, error)
	VerifyCAPTCHASuccess(context.Context, *VerifyCAPTCHASuccessReq) (*VerifyCAPTCHASuccessResp, error)
	GetLastVerifySuccessInfo(context.Context, *GetLastVerifySuccessInfoReq) (*GetLastVerifySuccessInfoResp, error)
	GetUserRegInfo(context.Context, *GetUserRegInfoReq) (*GetUserRegInfoResp, error)
	GetDeviceIdInfo(context.Context, *GetDeviceIdInfoReq) (*GetDeviceIdInfoResp, error)
	TrackUserLogin(context.Context, *TrackUserLoginReq) (*TrackUserLoginResp, error)
	IsUserInvalid(context.Context, *IsUserInvalidReq) (*IsUserInvalidResp, error)
}

func RegisterUserAuthHistoryServer(s *grpc.Server, srv UserAuthHistoryServer) {
	s.RegisterService(&_UserAuthHistory_serviceDesc, srv)
}

func _UserAuthHistory_GetUserLoginHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).GetUserLoginHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/GetUserLoginHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).GetUserLoginHistory(ctx, req.(*GetUserLoginHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_RecordUserLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordUserLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).RecordUserLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/RecordUserLogin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).RecordUserLogin(ctx, req.(*RecordUserLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_GetUserLoginWithDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginWithDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).GetUserLoginWithDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/GetUserLoginWithDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).GetUserLoginWithDevice(ctx, req.(*GetUserLoginWithDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_GetUserLoginDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLoginDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).GetUserLoginDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/GetUserLoginDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).GetUserLoginDevice(ctx, req.(*GetUserLoginDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_BatchGetUserLoginDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserLoginDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).BatchGetUserLoginDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/BatchGetUserLoginDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).BatchGetUserLoginDevice(ctx, req.(*BatchGetUserLoginDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_GetUserLastLoginInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLastLoginInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).GetUserLastLoginInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/GetUserLastLoginInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).GetUserLastLoginInfo(ctx, req.(*GetUserLastLoginInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_BatchGetUserLastLoginInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserLastLoginInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).BatchGetUserLastLoginInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/BatchGetUserLastLoginInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).BatchGetUserLastLoginInfo(ctx, req.(*BatchGetUserLastLoginInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_VerifyCAPTCHASuccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyCAPTCHASuccessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).VerifyCAPTCHASuccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/VerifyCAPTCHASuccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).VerifyCAPTCHASuccess(ctx, req.(*VerifyCAPTCHASuccessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_GetLastVerifySuccessInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastVerifySuccessInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).GetLastVerifySuccessInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/GetLastVerifySuccessInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).GetLastVerifySuccessInfo(ctx, req.(*GetLastVerifySuccessInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_GetUserRegInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRegInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).GetUserRegInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/GetUserRegInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).GetUserRegInfo(ctx, req.(*GetUserRegInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_GetDeviceIdInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceIdInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).GetDeviceIdInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/GetDeviceIdInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).GetDeviceIdInfo(ctx, req.(*GetDeviceIdInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_TrackUserLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrackUserLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).TrackUserLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/TrackUserLogin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).TrackUserLogin(ctx, req.(*TrackUserLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAuthHistory_IsUserInvalid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsUserInvalidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAuthHistoryServer).IsUserInvalid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_auth_history.UserAuthHistory/IsUserInvalid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAuthHistoryServer).IsUserInvalid(ctx, req.(*IsUserInvalidReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserAuthHistory_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user_auth_history.UserAuthHistory",
	HandlerType: (*UserAuthHistoryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserLoginHistory",
			Handler:    _UserAuthHistory_GetUserLoginHistory_Handler,
		},
		{
			MethodName: "RecordUserLogin",
			Handler:    _UserAuthHistory_RecordUserLogin_Handler,
		},
		{
			MethodName: "GetUserLoginWithDevice",
			Handler:    _UserAuthHistory_GetUserLoginWithDevice_Handler,
		},
		{
			MethodName: "GetUserLoginDevice",
			Handler:    _UserAuthHistory_GetUserLoginDevice_Handler,
		},
		{
			MethodName: "BatchGetUserLoginDevice",
			Handler:    _UserAuthHistory_BatchGetUserLoginDevice_Handler,
		},
		{
			MethodName: "GetUserLastLoginInfo",
			Handler:    _UserAuthHistory_GetUserLastLoginInfo_Handler,
		},
		{
			MethodName: "BatchGetUserLastLoginInfo",
			Handler:    _UserAuthHistory_BatchGetUserLastLoginInfo_Handler,
		},
		{
			MethodName: "VerifyCAPTCHASuccess",
			Handler:    _UserAuthHistory_VerifyCAPTCHASuccess_Handler,
		},
		{
			MethodName: "GetLastVerifySuccessInfo",
			Handler:    _UserAuthHistory_GetLastVerifySuccessInfo_Handler,
		},
		{
			MethodName: "GetUserRegInfo",
			Handler:    _UserAuthHistory_GetUserRegInfo_Handler,
		},
		{
			MethodName: "GetDeviceIdInfo",
			Handler:    _UserAuthHistory_GetDeviceIdInfo_Handler,
		},
		{
			MethodName: "TrackUserLogin",
			Handler:    _UserAuthHistory_TrackUserLogin_Handler,
		},
		{
			MethodName: "IsUserInvalid",
			Handler:    _UserAuthHistory_IsUserInvalid_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user-auth-history/user-auth-history.proto",
}

func init() {
	proto.RegisterFile("user-auth-history/user-auth-history.proto", fileDescriptor_user_auth_history_a324bf55fbafd2ff)
}

var fileDescriptor_user_auth_history_a324bf55fbafd2ff = []byte{
	// 1623 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x18, 0xeb, 0x52, 0xdb, 0xcc,
	0x15, 0x63, 0x03, 0xf6, 0xb1, 0x0d, 0x66, 0xb9, 0x09, 0x03, 0xc1, 0x11, 0xd0, 0x9a, 0xa4, 0x40,
	0x9a, 0xcb, 0x8f, 0x4e, 0xa7, 0x33, 0x25, 0x24, 0x25, 0xee, 0x90, 0x84, 0x0a, 0x92, 0x4e, 0x3a,
	0x9d, 0x2a, 0x8a, 0xb5, 0xd8, 0x3b, 0xb6, 0x25, 0x45, 0xbb, 0x22, 0xa5, 0x9d, 0x4e, 0xdb, 0x97,
	0xe8, 0x33, 0xf4, 0x7d, 0xfa, 0x42, 0x9d, 0x3d, 0x2b, 0xd9, 0x96, 0x2c, 0x1b, 0xf3, 0xfd, 0xd3,
	0x9e, 0xfb, 0xfd, 0x9c, 0x11, 0x1c, 0x06, 0x9c, 0xfa, 0x47, 0x56, 0x20, 0xda, 0x47, 0x6d, 0xc6,
	0x85, 0xeb, 0xdf, 0x9d, 0x8c, 0x40, 0x8e, 0x3d, 0xdf, 0x15, 0x2e, 0x59, 0x96, 0x08, 0x53, 0x22,
	0xcc, 0x10, 0xa1, 0xff, 0x6f, 0x01, 0xca, 0x9f, 0x38, 0xf5, 0x2f, 0xdc, 0x16, 0x73, 0x1a, 0xce,
	0x8d, 0x4b, 0x2a, 0x90, 0x0d, 0x98, 0xad, 0x65, 0x6a, 0x99, 0x7a, 0xce, 0x90, 0x9f, 0x64, 0x03,
	0x16, 0x5c, 0xcf, 0x14, 0x77, 0x1e, 0xd5, 0x66, 0x6b, 0x99, 0x7a, 0xd9, 0x98, 0x77, 0xbd, 0xeb,
	0x3b, 0x8f, 0x92, 0x75, 0x98, 0xf7, 0x29, 0x0f, 0xba, 0x42, 0xcb, 0xd6, 0x32, 0xf5, 0x39, 0x23,
	0x7c, 0x91, 0x55, 0x98, 0xf3, 0xda, 0xae, 0x43, 0xb5, 0x5c, 0x2d, 0x53, 0x2f, 0x18, 0xea, 0x41,
	0xea, 0x50, 0x11, 0x6d, 0xe6, 0xdb, 0xa6, 0x67, 0xf9, 0xe2, 0x4e, 0xc9, 0x9b, 0x43, 0x79, 0x8b,
	0x08, 0xbf, 0x94, 0xe0, 0x48, 0xae, 0xeb, 0x51, 0x87, 0xd9, 0xda, 0x3c, 0x0a, 0x08, 0x5f, 0x84,
	0x40, 0x8e, 0xf5, 0x28, 0xd3, 0x00, 0xa1, 0xf8, 0x4d, 0xd6, 0x60, 0xde, 0xe5, 0xe6, 0x2d, 0xf5,
	0xb5, 0xa2, 0x52, 0xe6, 0xf2, 0xcf, 0xd4, 0x47, 0x9b, 0xb9, 0xd2, 0x51, 0x0a, 0x65, 0x70, 0x94,
	0xfd, 0x18, 0x4a, 0x36, 0xbd, 0x65, 0x4d, 0x6a, 0xf6, 0x5c, 0x9b, 0x76, 0xb5, 0x32, 0x62, 0x8b,
	0x0a, 0xf6, 0x5e, 0x82, 0xc8, 0x36, 0x14, 0x38, 0x6b, 0x39, 0x96, 0x08, 0x7c, 0xaa, 0x2d, 0x22,
	0x7e, 0x00, 0x20, 0xbb, 0x10, 0x12, 0x9b, 0xcc, 0xb9, 0x71, 0xb5, 0x25, 0xc4, 0x83, 0x02, 0x61,
	0x00, 0x77, 0xa1, 0xc8, 0xb8, 0x49, 0x7b, 0x41, 0xd7, 0x12, 0xae, 0xaf, 0x55, 0xd0, 0x45, 0x60,
	0xfc, 0x6d, 0x08, 0x21, 0x5b, 0x50, 0x88, 0x24, 0xd8, 0xda, 0x32, 0xf2, 0xe7, 0x43, 0x7e, 0x9b,
	0xec, 0x00, 0x34, 0xbb, 0x8c, 0x3a, 0x02, 0x7d, 0x22, 0xc8, 0x5c, 0x50, 0x10, 0xe9, 0x57, 0x15,
	0xf2, 0xea, 0xd1, 0xf0, 0xb4, 0x15, 0xc5, 0x1a, 0xbd, 0xc9, 0x13, 0x58, 0x0e, 0x59, 0x9b, 0x6d,
	0xcb, 0x71, 0x68, 0x57, 0xca, 0x5f, 0x45, 0xa2, 0x25, 0x85, 0x38, 0x53, 0xf0, 0x86, 0x2d, 0x8d,
	0x0c, 0x69, 0x31, 0x46, 0x6b, 0xca, 0x48, 0x05, 0xc2, 0x38, 0xed, 0x41, 0x59, 0x50, 0xbf, 0xc7,
	0x1c, 0xab, 0xab, 0x48, 0xd6, 0x91, 0xa4, 0x14, 0x01, 0x91, 0xe8, 0x25, 0x6c, 0x30, 0x6e, 0x7e,
	0x0f, 0x58, 0xb3, 0x63, 0x76, 0x65, 0x05, 0x99, 0x37, 0xbe, 0xdb, 0x33, 0xb9, 0xdd, 0xd1, 0x36,
	0x6a, 0x99, 0x7a, 0xde, 0x58, 0x61, 0xfc, 0x0f, 0x12, 0x8b, 0xe5, 0xf5, 0x3b, 0xdf, 0xed, 0x5d,
	0xd9, 0x1d, 0xe9, 0xa2, 0x22, 0x16, 0xac, 0x47, 0x35, 0x4d, 0x05, 0x18, 0x21, 0xd7, 0xac, 0x47,
	0x87, 0x4c, 0xf3, 0x5c, 0x5f, 0x68, 0x9b, 0x58, 0x5a, 0xa1, 0x69, 0x97, 0xae, 0x2f, 0xb0, 0x0c,
	0xec, 0x1b, 0x4b, 0xab, 0x86, 0x65, 0x60, 0xdf, 0x58, 0xd2, 0x5c, 0x25, 0xd3, 0x6a, 0x36, 0xdd,
	0xc0, 0x11, 0xda, 0x16, 0x22, 0x4b, 0x08, 0x3c, 0x55, 0x30, 0x19, 0x3c, 0xd9, 0x01, 0x8e, 0xd5,
	0xa3, 0xda, 0xb6, 0x0a, 0x5e, 0xf4, 0x96, 0x35, 0x6b, 0x75, 0x99, 0xc5, 0xb5, 0x1d, 0x55, 0x46,
	0xf8, 0x90, 0x1c, 0x0e, 0x6b, 0x76, 0x90, 0xe3, 0x91, 0xe2, 0x88, 0xde, 0xe4, 0x00, 0x16, 0xb1,
	0x9f, 0x06, 0xb5, 0xb2, 0x8b, 0x14, 0x65, 0x09, 0xbd, 0xea, 0xd7, 0xcb, 0x26, 0xe4, 0x3d, 0xdf,
	0xfd, 0xeb, 0x9d, 0xc9, 0x3c, 0xad, 0x86, 0x04, 0x0b, 0xf8, 0x6e, 0x78, 0x32, 0x10, 0x0a, 0x85,
	0x8e, 0x3e, 0x56, 0xb9, 0x46, 0x08, 0xfa, 0xb9, 0x05, 0x61, 0xe2, 0x65, 0x1e, 0x75, 0xc4, 0x46,
	0xc9, 0xb6, 0x25, 0xb2, 0x67, 0xf9, 0x1d, 0x8a, 0xc8, 0x3d, 0x85, 0x54, 0x80, 0x86, 0xad, 0xdb,
	0xb0, 0x7e, 0x4e, 0x45, 0xbf, 0xaf, 0xdf, 0xa9, 0x66, 0x37, 0xe8, 0xf7, 0x94, 0xee, 0xde, 0x01,
	0xf8, 0x46, 0xfb, 0xd9, 0x90, 0x0d, 0x9e, 0x35, 0x0a, 0x08, 0xc1, 0x6c, 0x6c, 0x42, 0x9e, 0x3a,
	0xb6, 0x42, 0x66, 0x11, 0xb9, 0x40, 0x1d, 0x5b, 0xa2, 0xf4, 0xaf, 0xb0, 0x91, 0xaa, 0x85, 0x7b,
	0xe4, 0x6d, 0x94, 0x8e, 0x70, 0xce, 0x68, 0x99, 0x5a, 0xb6, 0x5e, 0x7c, 0x5e, 0x3b, 0x1e, 0x99,
	0x40, 0xc7, 0xb1, 0xe9, 0x13, 0x26, 0x2c, 0x14, 0xa5, 0xdb, 0x40, 0x0c, 0xda, 0x74, 0x7d, 0xbb,
	0x4f, 0x24, 0x7d, 0x78, 0x09, 0x39, 0x6c, 0x3d, 0xe9, 0xc4, 0x34, 0x32, 0x91, 0x9a, 0x68, 0xb0,
	0xc0, 0x9c, 0x5b, 0xab, 0xcb, 0xec, 0x70, 0x8a, 0x45, 0x4f, 0xfd, 0x0d, 0xac, 0x8c, 0x68, 0xe1,
	0x1e, 0x39, 0x82, 0x15, 0xc6, 0x4d, 0x87, 0xfe, 0x30, 0x03, 0x1e, 0x58, 0x5d, 0x53, 0xb5, 0x28,
	0x6a, 0xcd, 0x1b, 0x15, 0xc6, 0x3f, 0xd0, 0x1f, 0x9f, 0x24, 0xe2, 0x0d, 0xc2, 0xf5, 0x5f, 0x43,
	0x69, 0x28, 0x14, 0x22, 0x25, 0xd2, 0x9b, 0x90, 0x0f, 0x6b, 0x54, 0x84, 0x71, 0x5e, 0x50, 0xe5,
	0x29, 0xf4, 0x7f, 0x65, 0x60, 0x73, 0x38, 0x96, 0x7f, 0x64, 0xa2, 0xad, 0xe4, 0x4a, 0x87, 0x63,
	0x03, 0x23, 0x93, 0x18, 0x18, 0xfb, 0xb0, 0xa8, 0xf2, 0x97, 0x90, 0x5d, 0x42, 0xe8, 0x85, 0x52,
	0x40, 0x6a, 0x50, 0x92, 0x69, 0xec, 0xd3, 0xa8, 0x54, 0x02, 0x75, 0xec, 0x90, 0x42, 0xff, 0x3b,
	0x54, 0xc7, 0x59, 0xc0, 0xbd, 0xc9, 0x26, 0xfc, 0x16, 0x8a, 0x4a, 0xb0, 0xcc, 0x04, 0xd7, 0x66,
	0x31, 0xd7, 0xbb, 0x93, 0xf2, 0xf2, 0x8e, 0x09, 0x43, 0x0d, 0x01, 0x09, 0xe2, 0xfa, 0xbf, 0x33,
	0xb0, 0x7c, 0xed, 0x5b, 0xcd, 0x4e, 0x2c, 0xd1, 0x0f, 0x09, 0x61, 0xdc, 0xc2, 0x6c, 0xc2, 0xc2,
	0x68, 0x73, 0xe4, 0x86, 0x36, 0xc7, 0x22, 0xcc, 0x32, 0x0f, 0x37, 0x50, 0xc1, 0x98, 0x65, 0x9e,
	0xbe, 0x0a, 0x24, 0x69, 0x02, 0xf7, 0xf4, 0x7d, 0xa8, 0x34, 0xb8, 0x04, 0x35, 0x54, 0xb5, 0xa4,
	0xda, 0xa5, 0x1f, 0xc1, 0x72, 0x82, 0x8a, 0x7b, 0xc3, 0x15, 0xa7, 0x8a, 0xa6, 0x5f, 0x71, 0x87,
	0xb0, 0x36, 0x1c, 0xeb, 0x41, 0xa6, 0x47, 0x25, 0x37, 0x60, 0x29, 0x41, 0x37, 0x39, 0x17, 0x13,
	0x8a, 0x8c, 0xc7, 0xa7, 0xc2, 0x50, 0x76, 0x47, 0x03, 0x7d, 0x1e, 0x35, 0xb0, 0x12, 0x1c, 0x25,
	0x55, 0x9f, 0x94, 0xd4, 0x50, 0xa0, 0x6a, 0x61, 0xf5, 0xe0, 0xfa, 0xd3, 0xc1, 0x90, 0xb0, 0xb8,
	0x18, 0x34, 0x65, 0xaa, 0xb3, 0xbf, 0x82, 0xed, 0xd7, 0x96, 0x68, 0xb6, 0xc7, 0x71, 0x6c, 0x42,
	0x3e, 0x60, 0xb6, 0xd9, 0x65, 0x5c, 0xe0, 0x44, 0xc9, 0x19, 0x0b, 0x01, 0xb3, 0x2f, 0x18, 0x17,
	0xfa, 0x5f, 0x60, 0x67, 0x02, 0x2b, 0xf7, 0xc8, 0x6f, 0xa0, 0x20, 0xe7, 0xc0, 0x80, 0x79, 0x9a,
	0xd1, 0x91, 0x97, 0x2c, 0x28, 0xbf, 0x07, 0x1b, 0x9f, 0xa9, 0xcf, 0x6e, 0xee, 0xce, 0x4e, 0x2f,
	0xaf, 0xcf, 0xde, 0x9d, 0x5e, 0x05, 0xcd, 0x26, 0xe5, 0x3c, 0xbd, 0x4c, 0xf7, 0xa0, 0x7c, 0x8b,
	0xc4, 0xa6, 0x4f, 0x2d, 0xee, 0x3a, 0xe1, 0xc4, 0x29, 0x29, 0xa0, 0x81, 0xb0, 0x89, 0x05, 0xab,
	0x57, 0x41, 0x4b, 0x57, 0xc7, 0x3d, 0xfd, 0x1a, 0xb6, 0xce, 0xa9, 0x90, 0x1e, 0x2a, 0x92, 0x10,
	0x37, 0x36, 0xac, 0x53, 0x99, 0xa3, 0x7f, 0x81, 0xed, 0xf1, 0x52, 0xef, 0x9b, 0x00, 0xdb, 0x50,
	0x90, 0x1b, 0x82, 0x0b, 0xab, 0xe7, 0x45, 0x3b, 0xa4, 0x0f, 0xd0, 0x0f, 0x60, 0x39, 0x4c, 0x8b,
	0x41, 0x5b, 0xe3, 0xb3, 0xff, 0x7b, 0x20, 0x49, 0x32, 0xee, 0xfd, 0xb4, 0x69, 0xaf, 0x3f, 0x83,
	0x6a, 0xac, 0x1c, 0xe2, 0x6d, 0x46, 0x20, 0x17, 0x30, 0x9b, 0x87, 0x35, 0x84, 0xdf, 0xfa, 0x57,
	0x58, 0x45, 0x8e, 0x64, 0xb7, 0x8d, 0x86, 0x33, 0x16, 0x89, 0xd9, 0x09, 0xfd, 0x97, 0x8d, 0xf7,
	0x5f, 0x07, 0xb6, 0xc6, 0xda, 0xc4, 0x3d, 0x72, 0x91, 0x6c, 0x39, 0x55, 0xa4, 0x3f, 0x4f, 0xf1,
	0x38, 0xcd, 0xd0, 0x44, 0xdf, 0x5d, 0x40, 0xd1, 0xa0, 0x2d, 0x35, 0x92, 0x52, 0xaf, 0xfa, 0xfe,
	0x91, 0x3e, 0x3b, 0x7c, 0xa4, 0xaf, 0xc9, 0x93, 0xbe, 0x15, 0x19, 0x5f, 0x30, 0xe6, 0x7c, 0xda,
	0x3a, 0x15, 0xfa, 0x2f, 0x31, 0x35, 0x6f, 0x42, 0x27, 0xa3, 0x14, 0x4e, 0x2a, 0x09, 0xfd, 0x9f,
	0xb0, 0x32, 0xc2, 0xc2, 0x3d, 0xb9, 0xae, 0xa4, 0x02, 0xf4, 0x49, 0x5d, 0x6a, 0x19, 0xb5, 0xae,
	0x7c, 0x65, 0xed, 0x19, 0x5e, 0x6a, 0xaf, 0xa1, 0x2c, 0xa9, 0x06, 0x0d, 0xab, 0xc6, 0xcf, 0xa3,
	0x94, 0x58, 0x0c, 0x79, 0x69, 0x14, 0x7d, 0x55, 0x38, 0xb2, 0x63, 0x9f, 0xfc, 0x27, 0x03, 0xe5,
	0x8b, 0x8f, 0xe7, 0x8d, 0x0f, 0xe6, 0xc7, 0x4b, 0xf3, 0xfa, 0xcb, 0xe5, 0x5b, 0x52, 0x81, 0x52,
	0x1f, 0xf0, 0x81, 0x75, 0x2b, 0x33, 0x31, 0x88, 0x41, 0x5b, 0x95, 0x0c, 0x59, 0x81, 0xa5, 0x3e,
	0xe4, 0xbd, 0xe5, 0x04, 0x56, 0xb7, 0x32, 0x4b, 0xd6, 0x81, 0xf4, 0x81, 0xa7, 0x81, 0x70, 0x31,
	0xec, 0x95, 0x2c, 0xd9, 0x84, 0xb5, 0x3e, 0xfc, 0xca, 0xee, 0x98, 0xa7, 0x4d, 0xc1, 0x6e, 0x2d,
	0x41, 0x2b, 0xb9, 0x18, 0xaa, 0xf1, 0xf1, 0x6a, 0x80, 0x9a, 0x7b, 0xfe, 0xdf, 0xa2, 0x9a, 0xe9,
	0xa7, 0x81, 0x68, 0x87, 0x97, 0x0e, 0x71, 0x30, 0x5a, 0xc9, 0x5b, 0x8a, 0x1c, 0xa6, 0x38, 0x9c,
	0x7e, 0xd9, 0x55, 0x9f, 0x4c, 0x4b, 0xca, 0x3d, 0x7d, 0x86, 0x7c, 0x83, 0xa5, 0xc4, 0xcd, 0x43,
	0x0e, 0x52, 0x83, 0x9b, 0xbc, 0xbe, 0xaa, 0x3f, 0x9b, 0x86, 0x0c, 0x75, 0xfc, 0x88, 0xef, 0x9b,
	0xc1, 0x45, 0x41, 0x7e, 0x71, 0x8f, 0xad, 0xb1, 0xf3, 0xa7, 0x7a, 0xf4, 0x00, 0x6a, 0x54, 0xdc,
	0xe9, 0x0f, 0x92, 0xe1, 0x46, 0xae, 0xdf, 0x23, 0x66, 0xa0, 0xf0, 0x70, 0x4a, 0x4a, 0x54, 0xf6,
	0x37, 0xd8, 0x18, 0xd3, 0xd5, 0xe4, 0x68, 0x5c, 0xeb, 0xa6, 0xab, 0x3d, 0x7e, 0x08, 0x39, 0xea,
	0xbe, 0x81, 0xd5, 0xb4, 0x7d, 0x47, 0x26, 0xd5, 0x42, 0x62, 0xa7, 0x56, 0xef, 0x9d, 0xa8, 0xfa,
	0x0c, 0x91, 0xe7, 0xe9, 0xd8, 0xed, 0x4a, 0x4e, 0xee, 0xb3, 0x3b, 0xa9, 0xf2, 0xd9, 0xc3, 0x18,
	0xd0, 0xd5, 0xef, 0xb0, 0x9a, 0xb6, 0x10, 0x53, 0x5d, 0x1d, 0xb3, 0xa8, 0xab, 0x4f, 0xa7, 0xa6,
	0x45, 0x95, 0xff, 0x00, 0x6d, 0xdc, 0x46, 0x24, 0xc7, 0xe9, 0x11, 0x1e, 0xb7, 0x94, 0xab, 0x27,
	0x0f, 0xa2, 0x47, 0xf5, 0x26, 0x2c, 0xc6, 0xd7, 0x21, 0xd9, 0x1f, 0x9f, 0xd6, 0xc1, 0x62, 0xad,
	0x1e, 0x4c, 0x41, 0x15, 0xcd, 0x80, 0xc4, 0x84, 0x26, 0x63, 0x78, 0x13, 0x83, 0x3f, 0x75, 0x06,
	0xa4, 0x0c, 0x7b, 0xe5, 0x44, 0xfc, 0xa8, 0x4e, 0x75, 0x62, 0xe4, 0xf4, 0x4f, 0x75, 0x22, 0xe5,
	0x3a, 0x9f, 0x21, 0x7f, 0x86, 0x72, 0xec, 0xf2, 0x26, 0x7b, 0x29, 0x9c, 0xc9, 0x0b, 0xbe, 0xba,
	0x7f, 0x3f, 0x91, 0x94, 0xfe, 0xfa, 0xd5, 0x9f, 0x5e, 0xb4, 0xdc, 0xae, 0xe5, 0xb4, 0x8e, 0x5f,
	0x3d, 0x17, 0xe2, 0xb8, 0xe9, 0xf6, 0x4e, 0xf0, 0x57, 0x5a, 0xd3, 0xed, 0x9e, 0x70, 0xea, 0xe3,
	0xaa, 0x1d, 0xfd, 0xdd, 0xf6, 0x6d, 0x1e, 0x89, 0x5e, 0xfc, 0x3f, 0x00, 0x00, 0xff, 0xff, 0xdf,
	0x88, 0xaa, 0x5b, 0x9c, 0x13, 0x00, 0x00,
}
