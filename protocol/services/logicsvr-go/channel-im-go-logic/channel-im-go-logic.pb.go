// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/channel-im-go-logic/channel-im-go-logic.proto

package channel_im_go_logic // import "golang.52tt.com/protocol/services/logicsvr-go/channel-im-go-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel "golang.52tt.com/protocol/app/channel"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelImGoLogicClient is the client API for ChannelImGoLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelImGoLogicClient interface {
	SendChannelTextMsg(ctx context.Context, in *channel.SendChannelTextMsgReq, opts ...grpc.CallOption) (*channel.SendChannelTextMsgResp, error)
	GetChannelMsg(ctx context.Context, in *channel.GetChannelMsgReq, opts ...grpc.CallOption) (*channel.GetChannelMsgResp, error)
	SendChannelAttachmentMsg(ctx context.Context, in *channel.SendChannelAttachmentMsgReq, opts ...grpc.CallOption) (*channel.SendChannelAttachmentMsgResp, error)
	DownloadChannelMsgAttachment(ctx context.Context, in *channel.DownloadChannelMsgAttachmentReq, opts ...grpc.CallOption) (*channel.DownloadChannelMsgAttachmentResp, error)
}

type channelImGoLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelImGoLogicClient(cc *grpc.ClientConn) ChannelImGoLogicClient {
	return &channelImGoLogicClient{cc}
}

func (c *channelImGoLogicClient) SendChannelTextMsg(ctx context.Context, in *channel.SendChannelTextMsgReq, opts ...grpc.CallOption) (*channel.SendChannelTextMsgResp, error) {
	out := new(channel.SendChannelTextMsgResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelImGoLogic/SendChannelTextMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelImGoLogicClient) GetChannelMsg(ctx context.Context, in *channel.GetChannelMsgReq, opts ...grpc.CallOption) (*channel.GetChannelMsgResp, error) {
	out := new(channel.GetChannelMsgResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelImGoLogic/GetChannelMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelImGoLogicClient) SendChannelAttachmentMsg(ctx context.Context, in *channel.SendChannelAttachmentMsgReq, opts ...grpc.CallOption) (*channel.SendChannelAttachmentMsgResp, error) {
	out := new(channel.SendChannelAttachmentMsgResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelImGoLogic/SendChannelAttachmentMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelImGoLogicClient) DownloadChannelMsgAttachment(ctx context.Context, in *channel.DownloadChannelMsgAttachmentReq, opts ...grpc.CallOption) (*channel.DownloadChannelMsgAttachmentResp, error) {
	out := new(channel.DownloadChannelMsgAttachmentResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelImGoLogic/DownloadChannelMsgAttachment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelImGoLogicServer is the server API for ChannelImGoLogic service.
type ChannelImGoLogicServer interface {
	SendChannelTextMsg(context.Context, *channel.SendChannelTextMsgReq) (*channel.SendChannelTextMsgResp, error)
	GetChannelMsg(context.Context, *channel.GetChannelMsgReq) (*channel.GetChannelMsgResp, error)
	SendChannelAttachmentMsg(context.Context, *channel.SendChannelAttachmentMsgReq) (*channel.SendChannelAttachmentMsgResp, error)
	DownloadChannelMsgAttachment(context.Context, *channel.DownloadChannelMsgAttachmentReq) (*channel.DownloadChannelMsgAttachmentResp, error)
}

func RegisterChannelImGoLogicServer(s *grpc.Server, srv ChannelImGoLogicServer) {
	s.RegisterService(&_ChannelImGoLogic_serviceDesc, srv)
}

func _ChannelImGoLogic_SendChannelTextMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel.SendChannelTextMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelImGoLogicServer).SendChannelTextMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelImGoLogic/SendChannelTextMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelImGoLogicServer).SendChannelTextMsg(ctx, req.(*channel.SendChannelTextMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelImGoLogic_GetChannelMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel.GetChannelMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelImGoLogicServer).GetChannelMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelImGoLogic/GetChannelMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelImGoLogicServer).GetChannelMsg(ctx, req.(*channel.GetChannelMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelImGoLogic_SendChannelAttachmentMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel.SendChannelAttachmentMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelImGoLogicServer).SendChannelAttachmentMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelImGoLogic/SendChannelAttachmentMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelImGoLogicServer).SendChannelAttachmentMsg(ctx, req.(*channel.SendChannelAttachmentMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelImGoLogic_DownloadChannelMsgAttachment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel.DownloadChannelMsgAttachmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelImGoLogicServer).DownloadChannelMsgAttachment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelImGoLogic/DownloadChannelMsgAttachment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelImGoLogicServer).DownloadChannelMsgAttachment(ctx, req.(*channel.DownloadChannelMsgAttachmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelImGoLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.ChannelImGoLogic",
	HandlerType: (*ChannelImGoLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendChannelTextMsg",
			Handler:    _ChannelImGoLogic_SendChannelTextMsg_Handler,
		},
		{
			MethodName: "GetChannelMsg",
			Handler:    _ChannelImGoLogic_GetChannelMsg_Handler,
		},
		{
			MethodName: "SendChannelAttachmentMsg",
			Handler:    _ChannelImGoLogic_SendChannelAttachmentMsg_Handler,
		},
		{
			MethodName: "DownloadChannelMsgAttachment",
			Handler:    _ChannelImGoLogic_DownloadChannelMsgAttachment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/channel-im-go-logic/channel-im-go-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/channel-im-go-logic/channel-im-go-logic.proto", fileDescriptor_channel_im_go_logic_e1d849ff151a7fe8)
}

var fileDescriptor_channel_im_go_logic_e1d849ff151a7fe8 = []byte{
	// 272 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x92, 0x4d, 0x4b, 0x03, 0x31,
	0x10, 0x86, 0x45, 0xd0, 0x43, 0x40, 0x90, 0xa0, 0xa0, 0x8b, 0xa0, 0xa8, 0x17, 0x0f, 0x9b, 0x40,
	0xc5, 0x83, 0x17, 0xa1, 0x56, 0x28, 0x82, 0xbd, 0xa8, 0x27, 0x2f, 0x25, 0xa6, 0x43, 0xba, 0x98,
	0x64, 0xe2, 0x66, 0x68, 0xf5, 0x57, 0xfb, 0x17, 0x24, 0xd9, 0x96, 0xb6, 0xda, 0x8f, 0xd3, 0xb2,
	0xcf, 0xbc, 0xf3, 0xbc, 0x81, 0x84, 0xdd, 0x5a, 0x34, 0x95, 0x8e, 0xa3, 0xba, 0x34, 0x28, 0xf5,
	0x50, 0x79, 0x0f, 0xb6, 0xac, 0x5c, 0x69, 0xb0, 0xcc, 0x93, 0x65, 0x4c, 0x84, 0x1a, 0x09, 0xf9,
	0x4e, 0xfe, 0x29, 0xae, 0xe6, 0x0d, 0x46, 0x11, 0x8c, 0xd5, 0xb7, 0xc4, 0x40, 0x15, 0xfa, 0x38,
	0xfd, 0x36, 0x1b, 0x05, 0x57, 0x21, 0x4c, 0x85, 0xfd, 0x86, 0xb5, 0x7e, 0xb6, 0xd9, 0x7e, 0xa7,
	0x41, 0x8f, 0xae, 0x8b, 0x4f, 0x49, 0xc6, 0x7b, 0x8c, 0xbf, 0x80, 0x1f, 0x4c, 0xf8, 0x2b, 0x7c,
	0x51, 0x2f, 0x1a, 0x7e, 0x2c, 0x8c, 0x12, 0xff, 0xf9, 0x33, 0x7c, 0x16, 0xc5, 0xaa, 0x51, 0x0c,
	0xe7, 0x5b, 0xfc, 0x8e, 0xed, 0x75, 0x81, 0x26, 0xa3, 0x64, 0x3a, 0x48, 0xf1, 0x05, 0x94, 0x24,
	0x87, 0x4b, 0x68, 0xde, 0xef, 0xb3, 0xa3, 0x39, 0x77, 0x9b, 0x48, 0xe9, 0xa1, 0x03, 0x9f, 0x0f,
	0x75, 0xfa, 0xa7, 0x79, 0x61, 0x9a, 0xac, 0x67, 0xeb, 0x03, 0xb9, 0xe0, 0x83, 0x9d, 0x3c, 0xe0,
	0xd8, 0x5b, 0x54, 0x83, 0x59, 0xf9, 0x2c, 0xc8, 0x2f, 0x92, 0x63, 0x5d, 0x22, 0x15, 0x5d, 0x6e,
	0x0e, 0xa5, 0xb2, 0xfb, 0xce, 0x5b, 0xdb, 0xa0, 0x55, 0xde, 0x88, 0x9b, 0x16, 0x91, 0xd0, 0xe8,
	0x64, 0xbe, 0x0a, 0x8d, 0x56, 0x46, 0xa8, 0x47, 0x95, 0x86, 0x28, 0x37, 0x3c, 0x8b, 0xf7, 0xdd,
	0xbc, 0x72, 0xfd, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xa7, 0x05, 0x1b, 0x49, 0x40, 0x02, 0x00, 0x00,
}
