// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/channel-open-game-controller-logic/channel-open-game-controller-logic.proto

package channel_open_game_controller_logic // import "golang.52tt.com/protocol/services/logicsvr-go/channel-open-game-controller-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel_open_game_controller "golang.52tt.com/protocol/app/channel-open-game-controller"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelOpenGameControllerLogicClient is the client API for ChannelOpenGameControllerLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelOpenGameControllerLogicClient interface {
	GetChannelGamePlayerOpenid(ctx context.Context, in *channel_open_game_controller.GetChannelGamePlayerOpenidReq, opts ...grpc.CallOption) (*channel_open_game_controller.GetChannelGamePlayerOpenidResp, error)
	JoinChannelGame(ctx context.Context, in *channel_open_game_controller.JoinChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.JoinChannelGameResp, error)
	QuitChannelGame(ctx context.Context, in *channel_open_game_controller.QuitChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.QuitChannelGameResp, error)
	ReadyChannelGame(ctx context.Context, in *channel_open_game_controller.ReadyChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.ReadyChannelGameResp, error)
	UnReadyChannelGame(ctx context.Context, in *channel_open_game_controller.UnReadyChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.UnReadyChannelGameResp, error)
	GetChannelGameStatusInfo(ctx context.Context, in *channel_open_game_controller.GetChannelGameStatusInfoReq, opts ...grpc.CallOption) (*channel_open_game_controller.GetChannelGameStatusInfoResp, error)
	ExitChannelGame(ctx context.Context, in *channel_open_game_controller.ExitChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.ExitChannelGameResp, error)
	StartChannelGame(ctx context.Context, in *channel_open_game_controller.StartChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.StartChannelGameResp, error)
	SetChannelGameModeInfo(ctx context.Context, in *channel_open_game_controller.SetChannelGameModeInfoReq, opts ...grpc.CallOption) (*channel_open_game_controller.SetChannelGameModeInfoResp, error)
	SetChannelGamePlayerLoading(ctx context.Context, in *channel_open_game_controller.SetChannelGamePlayerLoadingReq, opts ...grpc.CallOption) (*channel_open_game_controller.SetChannelGamePlayerLoadingResp, error)
	BatchGetUidByOpenid(ctx context.Context, in *channel_open_game_controller.BatchGetUidByOpenidReq, opts ...grpc.CallOption) (*channel_open_game_controller.BatchGetUidByOpenidResp, error)
	CheckUserStartGamePermission(ctx context.Context, in *channel_open_game_controller.CheckUserStartGamePermissionRequest, opts ...grpc.CallOption) (*channel_open_game_controller.CheckUserStartGamePermissionResponse, error)
}

type channelOpenGameControllerLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelOpenGameControllerLogicClient(cc *grpc.ClientConn) ChannelOpenGameControllerLogicClient {
	return &channelOpenGameControllerLogicClient{cc}
}

func (c *channelOpenGameControllerLogicClient) GetChannelGamePlayerOpenid(ctx context.Context, in *channel_open_game_controller.GetChannelGamePlayerOpenidReq, opts ...grpc.CallOption) (*channel_open_game_controller.GetChannelGamePlayerOpenidResp, error) {
	out := new(channel_open_game_controller.GetChannelGamePlayerOpenidResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/GetChannelGamePlayerOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) JoinChannelGame(ctx context.Context, in *channel_open_game_controller.JoinChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.JoinChannelGameResp, error) {
	out := new(channel_open_game_controller.JoinChannelGameResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/JoinChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) QuitChannelGame(ctx context.Context, in *channel_open_game_controller.QuitChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.QuitChannelGameResp, error) {
	out := new(channel_open_game_controller.QuitChannelGameResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/QuitChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) ReadyChannelGame(ctx context.Context, in *channel_open_game_controller.ReadyChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.ReadyChannelGameResp, error) {
	out := new(channel_open_game_controller.ReadyChannelGameResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/ReadyChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) UnReadyChannelGame(ctx context.Context, in *channel_open_game_controller.UnReadyChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.UnReadyChannelGameResp, error) {
	out := new(channel_open_game_controller.UnReadyChannelGameResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/UnReadyChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) GetChannelGameStatusInfo(ctx context.Context, in *channel_open_game_controller.GetChannelGameStatusInfoReq, opts ...grpc.CallOption) (*channel_open_game_controller.GetChannelGameStatusInfoResp, error) {
	out := new(channel_open_game_controller.GetChannelGameStatusInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/GetChannelGameStatusInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) ExitChannelGame(ctx context.Context, in *channel_open_game_controller.ExitChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.ExitChannelGameResp, error) {
	out := new(channel_open_game_controller.ExitChannelGameResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/ExitChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) StartChannelGame(ctx context.Context, in *channel_open_game_controller.StartChannelGameReq, opts ...grpc.CallOption) (*channel_open_game_controller.StartChannelGameResp, error) {
	out := new(channel_open_game_controller.StartChannelGameResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/StartChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) SetChannelGameModeInfo(ctx context.Context, in *channel_open_game_controller.SetChannelGameModeInfoReq, opts ...grpc.CallOption) (*channel_open_game_controller.SetChannelGameModeInfoResp, error) {
	out := new(channel_open_game_controller.SetChannelGameModeInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/SetChannelGameModeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) SetChannelGamePlayerLoading(ctx context.Context, in *channel_open_game_controller.SetChannelGamePlayerLoadingReq, opts ...grpc.CallOption) (*channel_open_game_controller.SetChannelGamePlayerLoadingResp, error) {
	out := new(channel_open_game_controller.SetChannelGamePlayerLoadingResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/SetChannelGamePlayerLoading", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) BatchGetUidByOpenid(ctx context.Context, in *channel_open_game_controller.BatchGetUidByOpenidReq, opts ...grpc.CallOption) (*channel_open_game_controller.BatchGetUidByOpenidResp, error) {
	out := new(channel_open_game_controller.BatchGetUidByOpenidResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/BatchGetUidByOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameControllerLogicClient) CheckUserStartGamePermission(ctx context.Context, in *channel_open_game_controller.CheckUserStartGamePermissionRequest, opts ...grpc.CallOption) (*channel_open_game_controller.CheckUserStartGamePermissionResponse, error) {
	out := new(channel_open_game_controller.CheckUserStartGamePermissionResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelOpenGameControllerLogic/CheckUserStartGamePermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelOpenGameControllerLogicServer is the server API for ChannelOpenGameControllerLogic service.
type ChannelOpenGameControllerLogicServer interface {
	GetChannelGamePlayerOpenid(context.Context, *channel_open_game_controller.GetChannelGamePlayerOpenidReq) (*channel_open_game_controller.GetChannelGamePlayerOpenidResp, error)
	JoinChannelGame(context.Context, *channel_open_game_controller.JoinChannelGameReq) (*channel_open_game_controller.JoinChannelGameResp, error)
	QuitChannelGame(context.Context, *channel_open_game_controller.QuitChannelGameReq) (*channel_open_game_controller.QuitChannelGameResp, error)
	ReadyChannelGame(context.Context, *channel_open_game_controller.ReadyChannelGameReq) (*channel_open_game_controller.ReadyChannelGameResp, error)
	UnReadyChannelGame(context.Context, *channel_open_game_controller.UnReadyChannelGameReq) (*channel_open_game_controller.UnReadyChannelGameResp, error)
	GetChannelGameStatusInfo(context.Context, *channel_open_game_controller.GetChannelGameStatusInfoReq) (*channel_open_game_controller.GetChannelGameStatusInfoResp, error)
	ExitChannelGame(context.Context, *channel_open_game_controller.ExitChannelGameReq) (*channel_open_game_controller.ExitChannelGameResp, error)
	StartChannelGame(context.Context, *channel_open_game_controller.StartChannelGameReq) (*channel_open_game_controller.StartChannelGameResp, error)
	SetChannelGameModeInfo(context.Context, *channel_open_game_controller.SetChannelGameModeInfoReq) (*channel_open_game_controller.SetChannelGameModeInfoResp, error)
	SetChannelGamePlayerLoading(context.Context, *channel_open_game_controller.SetChannelGamePlayerLoadingReq) (*channel_open_game_controller.SetChannelGamePlayerLoadingResp, error)
	BatchGetUidByOpenid(context.Context, *channel_open_game_controller.BatchGetUidByOpenidReq) (*channel_open_game_controller.BatchGetUidByOpenidResp, error)
	CheckUserStartGamePermission(context.Context, *channel_open_game_controller.CheckUserStartGamePermissionRequest) (*channel_open_game_controller.CheckUserStartGamePermissionResponse, error)
}

func RegisterChannelOpenGameControllerLogicServer(s *grpc.Server, srv ChannelOpenGameControllerLogicServer) {
	s.RegisterService(&_ChannelOpenGameControllerLogic_serviceDesc, srv)
}

func _ChannelOpenGameControllerLogic_GetChannelGamePlayerOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.GetChannelGamePlayerOpenidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).GetChannelGamePlayerOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/GetChannelGamePlayerOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).GetChannelGamePlayerOpenid(ctx, req.(*channel_open_game_controller.GetChannelGamePlayerOpenidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_JoinChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.JoinChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).JoinChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/JoinChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).JoinChannelGame(ctx, req.(*channel_open_game_controller.JoinChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_QuitChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.QuitChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).QuitChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/QuitChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).QuitChannelGame(ctx, req.(*channel_open_game_controller.QuitChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_ReadyChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.ReadyChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).ReadyChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/ReadyChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).ReadyChannelGame(ctx, req.(*channel_open_game_controller.ReadyChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_UnReadyChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.UnReadyChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).UnReadyChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/UnReadyChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).UnReadyChannelGame(ctx, req.(*channel_open_game_controller.UnReadyChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_GetChannelGameStatusInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.GetChannelGameStatusInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).GetChannelGameStatusInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/GetChannelGameStatusInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).GetChannelGameStatusInfo(ctx, req.(*channel_open_game_controller.GetChannelGameStatusInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_ExitChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.ExitChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).ExitChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/ExitChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).ExitChannelGame(ctx, req.(*channel_open_game_controller.ExitChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_StartChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.StartChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).StartChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/StartChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).StartChannelGame(ctx, req.(*channel_open_game_controller.StartChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_SetChannelGameModeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.SetChannelGameModeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).SetChannelGameModeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/SetChannelGameModeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).SetChannelGameModeInfo(ctx, req.(*channel_open_game_controller.SetChannelGameModeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_SetChannelGamePlayerLoading_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.SetChannelGamePlayerLoadingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).SetChannelGamePlayerLoading(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/SetChannelGamePlayerLoading",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).SetChannelGamePlayerLoading(ctx, req.(*channel_open_game_controller.SetChannelGamePlayerLoadingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_BatchGetUidByOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.BatchGetUidByOpenidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).BatchGetUidByOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/BatchGetUidByOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).BatchGetUidByOpenid(ctx, req.(*channel_open_game_controller.BatchGetUidByOpenidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameControllerLogic_CheckUserStartGamePermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game_controller.CheckUserStartGamePermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameControllerLogicServer).CheckUserStartGamePermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelOpenGameControllerLogic/CheckUserStartGamePermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameControllerLogicServer).CheckUserStartGamePermission(ctx, req.(*channel_open_game_controller.CheckUserStartGamePermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelOpenGameControllerLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.ChannelOpenGameControllerLogic",
	HandlerType: (*ChannelOpenGameControllerLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelGamePlayerOpenid",
			Handler:    _ChannelOpenGameControllerLogic_GetChannelGamePlayerOpenid_Handler,
		},
		{
			MethodName: "JoinChannelGame",
			Handler:    _ChannelOpenGameControllerLogic_JoinChannelGame_Handler,
		},
		{
			MethodName: "QuitChannelGame",
			Handler:    _ChannelOpenGameControllerLogic_QuitChannelGame_Handler,
		},
		{
			MethodName: "ReadyChannelGame",
			Handler:    _ChannelOpenGameControllerLogic_ReadyChannelGame_Handler,
		},
		{
			MethodName: "UnReadyChannelGame",
			Handler:    _ChannelOpenGameControllerLogic_UnReadyChannelGame_Handler,
		},
		{
			MethodName: "GetChannelGameStatusInfo",
			Handler:    _ChannelOpenGameControllerLogic_GetChannelGameStatusInfo_Handler,
		},
		{
			MethodName: "ExitChannelGame",
			Handler:    _ChannelOpenGameControllerLogic_ExitChannelGame_Handler,
		},
		{
			MethodName: "StartChannelGame",
			Handler:    _ChannelOpenGameControllerLogic_StartChannelGame_Handler,
		},
		{
			MethodName: "SetChannelGameModeInfo",
			Handler:    _ChannelOpenGameControllerLogic_SetChannelGameModeInfo_Handler,
		},
		{
			MethodName: "SetChannelGamePlayerLoading",
			Handler:    _ChannelOpenGameControllerLogic_SetChannelGamePlayerLoading_Handler,
		},
		{
			MethodName: "BatchGetUidByOpenid",
			Handler:    _ChannelOpenGameControllerLogic_BatchGetUidByOpenid_Handler,
		},
		{
			MethodName: "CheckUserStartGamePermission",
			Handler:    _ChannelOpenGameControllerLogic_CheckUserStartGamePermission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/channel-open-game-controller-logic/channel-open-game-controller-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/channel-open-game-controller-logic/channel-open-game-controller-logic.proto", fileDescriptor_channel_open_game_controller_logic_5bf4e33054cfc726)
}

var fileDescriptor_channel_open_game_controller_logic_5bf4e33054cfc726 = []byte{
	// 553 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x96, 0x4d, 0x8b, 0xd3, 0x40,
	0x18, 0xc7, 0x19, 0x7c, 0x41, 0xe7, 0xa0, 0x32, 0x82, 0x48, 0x14, 0x0f, 0xde, 0x3c, 0x24, 0x81,
	0x5d, 0x57, 0xc5, 0xb7, 0x95, 0xd6, 0xa5, 0x28, 0x2b, 0xae, 0x2d, 0x45, 0xf0, 0x52, 0xc6, 0xe4,
	0x31, 0x1d, 0x4c, 0x67, 0xb2, 0x99, 0xe9, 0x6a, 0x3f, 0x81, 0x20, 0x08, 0x82, 0x57, 0x41, 0x5d,
	0xdf, 0x56, 0xc4, 0xab, 0xe0, 0xa7, 0xf0, 0xe8, 0x87, 0xc8, 0xcd, 0x4f, 0x20, 0x93, 0xa6, 0x4e,
	0x9b, 0x54, 0x93, 0xb4, 0xa7, 0x10, 0x78, 0x7e, 0xff, 0xf9, 0x25, 0xf9, 0x67, 0x12, 0x7c, 0x3f,
	0x14, 0x01, 0xf3, 0xe4, 0x4e, 0x6c, 0x07, 0xc2, 0xf5, 0xfa, 0x94, 0x73, 0x08, 0x6d, 0x11, 0x01,
	0xb7, 0x03, 0x3a, 0x00, 0xdb, 0x13, 0x5c, 0xc5, 0x22, 0x0c, 0x21, 0xb6, 0xd3, 0xc1, 0x0a, 0x23,
	0x4e, 0x14, 0x0b, 0x25, 0xc8, 0x81, 0xf4, 0xc4, 0xb2, 0x69, 0x14, 0x55, 0x80, 0x7a, 0x63, 0xca,
	0x3a, 0x37, 0xad, 0x13, 0x50, 0x05, 0x4f, 0xe8, 0xc8, 0x15, 0x91, 0x62, 0x82, 0xcb, 0xc9, 0x71,
	0x3c, 0xba, 0xf2, 0xeb, 0x08, 0x3e, 0xd3, 0x1c, 0x07, 0xdf, 0x8d, 0x80, 0xb7, 0xe8, 0x00, 0x9a,
	0x7f, 0x53, 0x37, 0x75, 0x0c, 0xd9, 0x43, 0xd8, 0x6a, 0x81, 0xca, 0xa6, 0xf4, 0xc4, 0x56, 0x48,
	0x47, 0x10, 0xeb, 0x79, 0xe6, 0x93, 0xeb, 0x4e, 0x40, 0x9d, 0xcc, 0xad, 0xa7, 0xdd, 0x7a, 0xda,
	0xad, 0x67, 0xdc, 0x9c, 0x7f, 0xc3, 0x6d, 0xd8, 0xb6, 0xd6, 0x97, 0xe2, 0x65, 0x74, 0xf6, 0xf0,
	0xef, 0x9f, 0x3f, 0x9c, 0xfd, 0x87, 0xde, 0x24, 0x88, 0x3c, 0x43, 0xf8, 0xe8, 0x6d, 0xc1, 0xf8,
	0xd4, 0x38, 0x59, 0x2d, 0xcd, 0xcf, 0x11, 0x5a, 0xea, 0x7c, 0x7d, 0xc8, 0x98, 0xbc, 0xcd, 0x4c,
	0xee, 0x0d, 0x99, 0xaa, 0x67, 0x92, 0x23, 0xaa, 0x99, 0x14, 0x20, 0x63, 0xf2, 0x2e, 0x41, 0xe4,
	0x39, 0xc2, 0xc7, 0xda, 0x40, 0xfd, 0xd1, 0xb4, 0x4a, 0x79, 0x6a, 0x1e, 0xd1, 0x2e, 0x6b, 0x0b,
	0x50, 0x46, 0x66, 0x37, 0x41, 0xe4, 0x25, 0xc2, 0xa4, 0xcb, 0x0b, 0x3a, 0x17, 0x4a, 0x83, 0x8b,
	0x90, 0x16, 0xba, 0xb8, 0x10, 0x67, 0x94, 0xde, 0x27, 0x88, 0xec, 0x22, 0x7c, 0x72, 0xb6, 0x61,
	0x1d, 0x45, 0xd5, 0x50, 0xde, 0xe2, 0x8f, 0x04, 0xb9, 0x5a, 0xb3, 0x9c, 0x06, 0xd5, 0x7a, 0xd7,
	0x96, 0xa0, 0x8d, 0xe4, 0x87, 0xac, 0x4e, 0x1b, 0x4f, 0xeb, 0xd6, 0x29, 0x47, 0x54, 0xab, 0x53,
	0x01, 0x32, 0x26, 0x1f, 0xb3, 0x3a, 0x75, 0x14, 0x8d, 0x55, 0xbd, 0x3a, 0xe5, 0x91, 0x6a, 0x75,
	0x2a, 0x52, 0x46, 0xe6, 0x53, 0x82, 0xc8, 0x6b, 0x84, 0x4f, 0x74, 0x66, 0x6e, 0xe1, 0x1d, 0xe1,
	0x43, 0xfa, 0xe4, 0x2e, 0x97, 0x87, 0xcf, 0x05, 0xb5, 0xd8, 0x95, 0x85, 0x59, 0xa3, 0xf7, 0x39,
	0x41, 0xe4, 0x2b, 0xc2, 0xa7, 0x3a, 0x73, 0x36, 0xaf, 0x4d, 0x41, 0x7d, 0xc6, 0x03, 0xb2, 0x5e,
	0x73, 0x9d, 0x19, 0x5a, 0x8b, 0xde, 0x58, 0x2e, 0xc0, 0xd8, 0xee, 0x25, 0x88, 0xbc, 0x42, 0xf8,
	0x78, 0x83, 0x2a, 0xaf, 0xdf, 0x02, 0xd5, 0x65, 0x7e, 0x63, 0x94, 0x6d, 0xf0, 0xe5, 0x2f, 0xd9,
	0x1c, 0x4a, 0xdb, 0x5d, 0x5a, 0x0c, 0x34, 0x56, 0x5f, 0x12, 0x44, 0xbe, 0x23, 0x7c, 0xba, 0xd9,
	0x07, 0xef, 0x71, 0x57, 0x42, 0x9c, 0xf6, 0x21, 0xbd, 0x10, 0x88, 0x07, 0x4c, 0x4a, 0x26, 0x38,
	0xb9, 0x59, 0xba, 0xca, 0xff, 0xf0, 0x36, 0x6c, 0x0f, 0x41, 0x2a, 0x6b, 0x63, 0xc9, 0x14, 0x19,
	0x09, 0x2e, 0x61, 0x22, 0xfe, 0xed, 0xc5, 0xbe, 0x46, 0xfb, 0xc1, 0x56, 0x20, 0x42, 0xca, 0x03,
	0x67, 0x6d, 0x45, 0x29, 0xc7, 0x13, 0x03, 0x37, 0xfd, 0xe4, 0x7a, 0x22, 0x74, 0x25, 0xc4, 0x3b,
	0xcc, 0x03, 0xe9, 0xd6, 0xfb, 0x6f, 0x78, 0x78, 0x30, 0x4d, 0x58, 0xfd, 0x13, 0x00, 0x00, 0xff,
	0xff, 0xc8, 0xc7, 0x80, 0x3c, 0x70, 0x08, 0x00, 0x00,
}
