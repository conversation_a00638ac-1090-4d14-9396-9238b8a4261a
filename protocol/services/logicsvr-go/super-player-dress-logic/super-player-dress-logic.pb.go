// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/super-player-dress-logic/super-player-dress-logic.proto

package super_player_dress_logic // import "golang.52tt.com/protocol/services/logicsvr-go/super-player-dress-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import super_player_dress_logic "golang.52tt.com/protocol/app/super-player-dress-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SuperPlayerDressLogicClient is the client API for SuperPlayerDressLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SuperPlayerDressLogicClient interface {
	// 获取会员装扮配置
	GetDressConfigList(ctx context.Context, in *super_player_dress_logic.GetDressConfigListReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetDressConfigListResp, error)
	// 获取会员装扮配置版本
	GetDressConfigMaxVersion(ctx context.Context, in *super_player_dress_logic.GetDressConfigMaxVersionReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetDressConfigMaxVersionResp, error)
	// 获取房间当前装扮套装
	GetChannelCurrDressId(ctx context.Context, in *super_player_dress_logic.GetChannelCurrDressIdReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetChannelCurrDressIdResp, error)
	// 获取用户当前特别关心装扮
	GetUserCurrSpecialConcernDressId(ctx context.Context, in *super_player_dress_logic.GetUserCurrSpecialConcernDressIdReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetUserCurrSpecialConcernDressIdResp, error)
	// 取消佩戴房间当前装扮套装
	RemoveChannelCurrDressId(ctx context.Context, in *super_player_dress_logic.RemoveChannelCurrDressIdReq, opts ...grpc.CallOption) (*super_player_dress_logic.RemoveChannelCurrDressIdResp, error)
	// 获取用户当前聊天气泡装扮
	GetUserCurrChatBubbleDressId(ctx context.Context, in *super_player_dress_logic.GetUserCurrChatBubbleDressIdReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetUserCurrChatBubbleDressIdResp, error)
	// 获取用户当前聊天背景装扮
	GetUserCurrChatBgDressIdList(ctx context.Context, in *super_player_dress_logic.GetUserCurrChatBgDressIdListReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetUserCurrChatBgDressIdListResp, error)
}

type superPlayerDressLogicClient struct {
	cc *grpc.ClientConn
}

func NewSuperPlayerDressLogicClient(cc *grpc.ClientConn) SuperPlayerDressLogicClient {
	return &superPlayerDressLogicClient{cc}
}

func (c *superPlayerDressLogicClient) GetDressConfigList(ctx context.Context, in *super_player_dress_logic.GetDressConfigListReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetDressConfigListResp, error) {
	out := new(super_player_dress_logic.GetDressConfigListResp)
	err := c.cc.Invoke(ctx, "/logic.SuperPlayerDressLogic/GetDressConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressLogicClient) GetDressConfigMaxVersion(ctx context.Context, in *super_player_dress_logic.GetDressConfigMaxVersionReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetDressConfigMaxVersionResp, error) {
	out := new(super_player_dress_logic.GetDressConfigMaxVersionResp)
	err := c.cc.Invoke(ctx, "/logic.SuperPlayerDressLogic/GetDressConfigMaxVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressLogicClient) GetChannelCurrDressId(ctx context.Context, in *super_player_dress_logic.GetChannelCurrDressIdReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetChannelCurrDressIdResp, error) {
	out := new(super_player_dress_logic.GetChannelCurrDressIdResp)
	err := c.cc.Invoke(ctx, "/logic.SuperPlayerDressLogic/GetChannelCurrDressId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressLogicClient) GetUserCurrSpecialConcernDressId(ctx context.Context, in *super_player_dress_logic.GetUserCurrSpecialConcernDressIdReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetUserCurrSpecialConcernDressIdResp, error) {
	out := new(super_player_dress_logic.GetUserCurrSpecialConcernDressIdResp)
	err := c.cc.Invoke(ctx, "/logic.SuperPlayerDressLogic/GetUserCurrSpecialConcernDressId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressLogicClient) RemoveChannelCurrDressId(ctx context.Context, in *super_player_dress_logic.RemoveChannelCurrDressIdReq, opts ...grpc.CallOption) (*super_player_dress_logic.RemoveChannelCurrDressIdResp, error) {
	out := new(super_player_dress_logic.RemoveChannelCurrDressIdResp)
	err := c.cc.Invoke(ctx, "/logic.SuperPlayerDressLogic/RemoveChannelCurrDressId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressLogicClient) GetUserCurrChatBubbleDressId(ctx context.Context, in *super_player_dress_logic.GetUserCurrChatBubbleDressIdReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetUserCurrChatBubbleDressIdResp, error) {
	out := new(super_player_dress_logic.GetUserCurrChatBubbleDressIdResp)
	err := c.cc.Invoke(ctx, "/logic.SuperPlayerDressLogic/GetUserCurrChatBubbleDressId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superPlayerDressLogicClient) GetUserCurrChatBgDressIdList(ctx context.Context, in *super_player_dress_logic.GetUserCurrChatBgDressIdListReq, opts ...grpc.CallOption) (*super_player_dress_logic.GetUserCurrChatBgDressIdListResp, error) {
	out := new(super_player_dress_logic.GetUserCurrChatBgDressIdListResp)
	err := c.cc.Invoke(ctx, "/logic.SuperPlayerDressLogic/GetUserCurrChatBgDressIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SuperPlayerDressLogicServer is the server API for SuperPlayerDressLogic service.
type SuperPlayerDressLogicServer interface {
	// 获取会员装扮配置
	GetDressConfigList(context.Context, *super_player_dress_logic.GetDressConfigListReq) (*super_player_dress_logic.GetDressConfigListResp, error)
	// 获取会员装扮配置版本
	GetDressConfigMaxVersion(context.Context, *super_player_dress_logic.GetDressConfigMaxVersionReq) (*super_player_dress_logic.GetDressConfigMaxVersionResp, error)
	// 获取房间当前装扮套装
	GetChannelCurrDressId(context.Context, *super_player_dress_logic.GetChannelCurrDressIdReq) (*super_player_dress_logic.GetChannelCurrDressIdResp, error)
	// 获取用户当前特别关心装扮
	GetUserCurrSpecialConcernDressId(context.Context, *super_player_dress_logic.GetUserCurrSpecialConcernDressIdReq) (*super_player_dress_logic.GetUserCurrSpecialConcernDressIdResp, error)
	// 取消佩戴房间当前装扮套装
	RemoveChannelCurrDressId(context.Context, *super_player_dress_logic.RemoveChannelCurrDressIdReq) (*super_player_dress_logic.RemoveChannelCurrDressIdResp, error)
	// 获取用户当前聊天气泡装扮
	GetUserCurrChatBubbleDressId(context.Context, *super_player_dress_logic.GetUserCurrChatBubbleDressIdReq) (*super_player_dress_logic.GetUserCurrChatBubbleDressIdResp, error)
	// 获取用户当前聊天背景装扮
	GetUserCurrChatBgDressIdList(context.Context, *super_player_dress_logic.GetUserCurrChatBgDressIdListReq) (*super_player_dress_logic.GetUserCurrChatBgDressIdListResp, error)
}

func RegisterSuperPlayerDressLogicServer(s *grpc.Server, srv SuperPlayerDressLogicServer) {
	s.RegisterService(&_SuperPlayerDressLogic_serviceDesc, srv)
}

func _SuperPlayerDressLogic_GetDressConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_player_dress_logic.GetDressConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressLogicServer).GetDressConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperPlayerDressLogic/GetDressConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressLogicServer).GetDressConfigList(ctx, req.(*super_player_dress_logic.GetDressConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDressLogic_GetDressConfigMaxVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_player_dress_logic.GetDressConfigMaxVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressLogicServer).GetDressConfigMaxVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperPlayerDressLogic/GetDressConfigMaxVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressLogicServer).GetDressConfigMaxVersion(ctx, req.(*super_player_dress_logic.GetDressConfigMaxVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDressLogic_GetChannelCurrDressId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_player_dress_logic.GetChannelCurrDressIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressLogicServer).GetChannelCurrDressId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperPlayerDressLogic/GetChannelCurrDressId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressLogicServer).GetChannelCurrDressId(ctx, req.(*super_player_dress_logic.GetChannelCurrDressIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDressLogic_GetUserCurrSpecialConcernDressId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_player_dress_logic.GetUserCurrSpecialConcernDressIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressLogicServer).GetUserCurrSpecialConcernDressId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperPlayerDressLogic/GetUserCurrSpecialConcernDressId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressLogicServer).GetUserCurrSpecialConcernDressId(ctx, req.(*super_player_dress_logic.GetUserCurrSpecialConcernDressIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDressLogic_RemoveChannelCurrDressId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_player_dress_logic.RemoveChannelCurrDressIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressLogicServer).RemoveChannelCurrDressId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperPlayerDressLogic/RemoveChannelCurrDressId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressLogicServer).RemoveChannelCurrDressId(ctx, req.(*super_player_dress_logic.RemoveChannelCurrDressIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDressLogic_GetUserCurrChatBubbleDressId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_player_dress_logic.GetUserCurrChatBubbleDressIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressLogicServer).GetUserCurrChatBubbleDressId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperPlayerDressLogic/GetUserCurrChatBubbleDressId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressLogicServer).GetUserCurrChatBubbleDressId(ctx, req.(*super_player_dress_logic.GetUserCurrChatBubbleDressIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperPlayerDressLogic_GetUserCurrChatBgDressIdList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_player_dress_logic.GetUserCurrChatBgDressIdListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperPlayerDressLogicServer).GetUserCurrChatBgDressIdList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperPlayerDressLogic/GetUserCurrChatBgDressIdList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperPlayerDressLogicServer).GetUserCurrChatBgDressIdList(ctx, req.(*super_player_dress_logic.GetUserCurrChatBgDressIdListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SuperPlayerDressLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.SuperPlayerDressLogic",
	HandlerType: (*SuperPlayerDressLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDressConfigList",
			Handler:    _SuperPlayerDressLogic_GetDressConfigList_Handler,
		},
		{
			MethodName: "GetDressConfigMaxVersion",
			Handler:    _SuperPlayerDressLogic_GetDressConfigMaxVersion_Handler,
		},
		{
			MethodName: "GetChannelCurrDressId",
			Handler:    _SuperPlayerDressLogic_GetChannelCurrDressId_Handler,
		},
		{
			MethodName: "GetUserCurrSpecialConcernDressId",
			Handler:    _SuperPlayerDressLogic_GetUserCurrSpecialConcernDressId_Handler,
		},
		{
			MethodName: "RemoveChannelCurrDressId",
			Handler:    _SuperPlayerDressLogic_RemoveChannelCurrDressId_Handler,
		},
		{
			MethodName: "GetUserCurrChatBubbleDressId",
			Handler:    _SuperPlayerDressLogic_GetUserCurrChatBubbleDressId_Handler,
		},
		{
			MethodName: "GetUserCurrChatBgDressIdList",
			Handler:    _SuperPlayerDressLogic_GetUserCurrChatBgDressIdList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/super-player-dress-logic/super-player-dress-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/super-player-dress-logic/super-player-dress-logic.proto", fileDescriptor_super_player_dress_logic_7e09eb3be939aaa2)
}

var fileDescriptor_super_player_dress_logic_7e09eb3be939aaa2 = []byte{
	// 427 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x94, 0x4f, 0xeb, 0xd3, 0x30,
	0x18, 0xc7, 0x29, 0xa2, 0xcc, 0x1c, 0x03, 0x83, 0x51, 0x2c, 0x8a, 0x9e, 0x3c, 0xb4, 0x85, 0x8d,
	0x89, 0x82, 0x82, 0xae, 0xe2, 0x14, 0x26, 0xc8, 0x86, 0x1e, 0xbc, 0x94, 0xac, 0x7b, 0xcc, 0x0a,
	0x5d, 0x12, 0x93, 0x6c, 0xba, 0x57, 0xe0, 0x55, 0x8f, 0xbe, 0x00, 0xff, 0x7b, 0xf1, 0xb8, 0x57,
	0xb1, 0xf7, 0xe2, 0xd1, 0x93, 0x24, 0xfd, 0x8d, 0x6e, 0x6c, 0xdd, 0xd6, 0xed, 0x54, 0xda, 0x7c,
	0x3f, 0xdf, 0x7e, 0x9a, 0xd0, 0x07, 0x45, 0x19, 0xa7, 0x69, 0xa2, 0x66, 0xd2, 0xa7, 0x3c, 0x54,
	0x53, 0x01, 0xd2, 0x17, 0x19, 0x99, 0x83, 0xf4, 0x47, 0x12, 0x94, 0xf2, 0xed, 0x72, 0xe9, 0x42,
	0x20, 0x24, 0xd7, 0x1c, 0x5f, 0xb6, 0x37, 0xee, 0xed, 0xf5, 0x2e, 0x4a, 0x34, 0xbc, 0x23, 0xf3,
	0x90, 0x0b, 0x9d, 0x72, 0xa6, 0x56, 0xd7, 0x9c, 0x70, 0x6f, 0x11, 0x21, 0x4a, 0x5b, 0xe3, 0x3c,
	0xd4, 0xfc, 0x74, 0x15, 0xd5, 0x07, 0x26, 0xf3, 0xc2, 0x46, 0x1e, 0x9b, 0x44, 0xcf, 0x04, 0xf0,
	0x07, 0x07, 0xe1, 0x2e, 0x68, 0xfb, 0x24, 0xe2, 0xec, 0x4d, 0x4a, 0x7b, 0xa9, 0xd2, 0xb8, 0x19,
	0x50, 0x12, 0xd8, 0xd6, 0x38, 0x6f, 0x8d, 0x6d, 0x6b, 0x9c, 0xbb, 0x6e, 0x03, 0x7d, 0x78, 0xeb,
	0xb6, 0x2a, 0x33, 0x4a, 0xdc, 0xac, 0xfd, 0x5d, 0x2e, 0x82, 0x4b, 0xb5, 0xaf, 0x1e, 0xfe, 0xec,
	0xa0, 0xc6, 0x66, 0xe8, 0x39, 0x79, 0xff, 0x0a, 0xa4, 0x4a, 0x39, 0xc3, 0x77, 0x2b, 0x74, 0x17,
	0x98, 0xb1, 0xba, 0x77, 0x22, 0x59, 0xb8, 0x7d, 0xf3, 0xf0, 0x47, 0x07, 0xd5, 0xbb, 0xa0, 0xa3,
	0x31, 0x61, 0x0c, 0xb2, 0x68, 0x2a, 0xf3, 0x2d, 0x7c, 0x36, 0xc2, 0xed, 0x43, 0xf5, 0xdb, 0x8c,
	0xb1, 0xba, 0x73, 0x0a, 0x56, 0x28, 0x7d, 0xf7, 0xf0, 0x1f, 0x07, 0xdd, 0xe8, 0x82, 0x7e, 0xa9,
	0x40, 0x9a, 0xd0, 0x40, 0x40, 0x92, 0x92, 0x2c, 0xe2, 0x2c, 0x01, 0xc9, 0x56, 0x76, 0x0f, 0x0f,
	0xbd, 0x66, 0x2f, 0x6e, 0x44, 0x1f, 0x9d, 0xd9, 0x50, 0x38, 0xff, 0xc8, 0x8f, 0xb8, 0x0f, 0x13,
	0x3e, 0x83, 0x1d, 0x3b, 0xb9, 0xff, 0x88, 0xcb, 0xb0, 0xc3, 0x47, 0x5c, 0x4e, 0x16, 0x6e, 0x3f,
	0x3d, 0xfc, 0xc5, 0x41, 0xd7, 0xd6, 0x3e, 0x27, 0x1a, 0x13, 0xdd, 0x99, 0x0e, 0x87, 0x19, 0xac,
	0xfc, 0xee, 0x1f, 0xbb, 0x13, 0x5b, 0xa8, 0x71, 0x7c, 0x70, 0x06, 0x5d, 0x78, 0xfe, 0xda, 0xed,
	0x49, 0x2f, 0xa2, 0xf6, 0xd7, 0xad, 0xe6, 0xb9, 0x8e, 0x56, 0xf7, 0xdc, 0xa4, 0x0b, 0xcf, 0xdf,
	0x9e, 0x7b, 0xfd, 0xdf, 0x72, 0x11, 0xb8, 0xa8, 0x51, 0x36, 0x9a, 0x3a, 0x4f, 0x5f, 0x3f, 0xa1,
	0x3c, 0x23, 0x8c, 0x06, 0xed, 0xa6, 0xd6, 0x41, 0xc2, 0x27, 0xa1, 0x1d, 0x56, 0x09, 0xcf, 0x42,
	0x05, 0x72, 0x96, 0x26, 0xa0, 0xc2, 0x63, 0x66, 0xea, 0xf0, 0x8a, 0xe5, 0x5a, 0xff, 0x03, 0x00,
	0x00, 0xff, 0xff, 0x6c, 0x1d, 0x73, 0x32, 0x82, 0x05, 0x00, 0x00,
}
