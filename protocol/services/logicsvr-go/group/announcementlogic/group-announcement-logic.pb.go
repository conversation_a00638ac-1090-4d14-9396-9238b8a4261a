// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/group/group-announcement-logic.proto

package announcementlogic // import "golang.52tt.com/protocol/services/logicsvr-go/group/announcementlogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import group "golang.52tt.com/protocol/app/group"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GroupAnnouncementLogicClient is the client API for GroupAnnouncementLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GroupAnnouncementLogicClient interface {
	GetGroupAnnouncementList(ctx context.Context, in *group.GetGroupAnnouncementListReq, opts ...grpc.CallOption) (*group.GetGroupAnnouncementListResp, error)
	PrepareApplyGroupAnnouncement(ctx context.Context, in *group.PrepareApplyGroupAnnouncementReq, opts ...grpc.CallOption) (*group.PrepareApplyGroupAnnouncementResp, error)
	DelGroupAnnouncement(ctx context.Context, in *group.DelGroupAnnouncementReq, opts ...grpc.CallOption) (*group.DelGroupAnnouncementResp, error)
	GetGroupAnnouncementCheck(ctx context.Context, in *group.GetGroupAnnouncementCheckReq, opts ...grpc.CallOption) (*group.GetGroupAnnouncementCheckResp, error)
	GetGroupAnnouncementByID(ctx context.Context, in *group.GetGroupAnnouncementByIDReq, opts ...grpc.CallOption) (*group.GetGroupAnnouncementByIDResp, error)
	GetGroupAnnouncementEditAuth(ctx context.Context, in *group.GetGroupAnnouncementEditAuthReq, opts ...grpc.CallOption) (*group.GetGroupAnnouncementEditAuthResp, error)
}

type groupAnnouncementLogicClient struct {
	cc *grpc.ClientConn
}

func NewGroupAnnouncementLogicClient(cc *grpc.ClientConn) GroupAnnouncementLogicClient {
	return &groupAnnouncementLogicClient{cc}
}

func (c *groupAnnouncementLogicClient) GetGroupAnnouncementList(ctx context.Context, in *group.GetGroupAnnouncementListReq, opts ...grpc.CallOption) (*group.GetGroupAnnouncementListResp, error) {
	out := new(group.GetGroupAnnouncementListResp)
	err := c.cc.Invoke(ctx, "/logic.GroupAnnouncementLogic/GetGroupAnnouncementList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupAnnouncementLogicClient) PrepareApplyGroupAnnouncement(ctx context.Context, in *group.PrepareApplyGroupAnnouncementReq, opts ...grpc.CallOption) (*group.PrepareApplyGroupAnnouncementResp, error) {
	out := new(group.PrepareApplyGroupAnnouncementResp)
	err := c.cc.Invoke(ctx, "/logic.GroupAnnouncementLogic/PrepareApplyGroupAnnouncement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupAnnouncementLogicClient) DelGroupAnnouncement(ctx context.Context, in *group.DelGroupAnnouncementReq, opts ...grpc.CallOption) (*group.DelGroupAnnouncementResp, error) {
	out := new(group.DelGroupAnnouncementResp)
	err := c.cc.Invoke(ctx, "/logic.GroupAnnouncementLogic/DelGroupAnnouncement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupAnnouncementLogicClient) GetGroupAnnouncementCheck(ctx context.Context, in *group.GetGroupAnnouncementCheckReq, opts ...grpc.CallOption) (*group.GetGroupAnnouncementCheckResp, error) {
	out := new(group.GetGroupAnnouncementCheckResp)
	err := c.cc.Invoke(ctx, "/logic.GroupAnnouncementLogic/GetGroupAnnouncementCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupAnnouncementLogicClient) GetGroupAnnouncementByID(ctx context.Context, in *group.GetGroupAnnouncementByIDReq, opts ...grpc.CallOption) (*group.GetGroupAnnouncementByIDResp, error) {
	out := new(group.GetGroupAnnouncementByIDResp)
	err := c.cc.Invoke(ctx, "/logic.GroupAnnouncementLogic/GetGroupAnnouncementByID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupAnnouncementLogicClient) GetGroupAnnouncementEditAuth(ctx context.Context, in *group.GetGroupAnnouncementEditAuthReq, opts ...grpc.CallOption) (*group.GetGroupAnnouncementEditAuthResp, error) {
	out := new(group.GetGroupAnnouncementEditAuthResp)
	err := c.cc.Invoke(ctx, "/logic.GroupAnnouncementLogic/GetGroupAnnouncementEditAuth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroupAnnouncementLogicServer is the server API for GroupAnnouncementLogic service.
type GroupAnnouncementLogicServer interface {
	GetGroupAnnouncementList(context.Context, *group.GetGroupAnnouncementListReq) (*group.GetGroupAnnouncementListResp, error)
	PrepareApplyGroupAnnouncement(context.Context, *group.PrepareApplyGroupAnnouncementReq) (*group.PrepareApplyGroupAnnouncementResp, error)
	DelGroupAnnouncement(context.Context, *group.DelGroupAnnouncementReq) (*group.DelGroupAnnouncementResp, error)
	GetGroupAnnouncementCheck(context.Context, *group.GetGroupAnnouncementCheckReq) (*group.GetGroupAnnouncementCheckResp, error)
	GetGroupAnnouncementByID(context.Context, *group.GetGroupAnnouncementByIDReq) (*group.GetGroupAnnouncementByIDResp, error)
	GetGroupAnnouncementEditAuth(context.Context, *group.GetGroupAnnouncementEditAuthReq) (*group.GetGroupAnnouncementEditAuthResp, error)
}

func RegisterGroupAnnouncementLogicServer(s *grpc.Server, srv GroupAnnouncementLogicServer) {
	s.RegisterService(&_GroupAnnouncementLogic_serviceDesc, srv)
}

func _GroupAnnouncementLogic_GetGroupAnnouncementList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(group.GetGroupAnnouncementListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupAnnouncementLogicServer).GetGroupAnnouncementList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GroupAnnouncementLogic/GetGroupAnnouncementList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupAnnouncementLogicServer).GetGroupAnnouncementList(ctx, req.(*group.GetGroupAnnouncementListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupAnnouncementLogic_PrepareApplyGroupAnnouncement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(group.PrepareApplyGroupAnnouncementReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupAnnouncementLogicServer).PrepareApplyGroupAnnouncement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GroupAnnouncementLogic/PrepareApplyGroupAnnouncement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupAnnouncementLogicServer).PrepareApplyGroupAnnouncement(ctx, req.(*group.PrepareApplyGroupAnnouncementReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupAnnouncementLogic_DelGroupAnnouncement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(group.DelGroupAnnouncementReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupAnnouncementLogicServer).DelGroupAnnouncement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GroupAnnouncementLogic/DelGroupAnnouncement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupAnnouncementLogicServer).DelGroupAnnouncement(ctx, req.(*group.DelGroupAnnouncementReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupAnnouncementLogic_GetGroupAnnouncementCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(group.GetGroupAnnouncementCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupAnnouncementLogicServer).GetGroupAnnouncementCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GroupAnnouncementLogic/GetGroupAnnouncementCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupAnnouncementLogicServer).GetGroupAnnouncementCheck(ctx, req.(*group.GetGroupAnnouncementCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupAnnouncementLogic_GetGroupAnnouncementByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(group.GetGroupAnnouncementByIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupAnnouncementLogicServer).GetGroupAnnouncementByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GroupAnnouncementLogic/GetGroupAnnouncementByID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupAnnouncementLogicServer).GetGroupAnnouncementByID(ctx, req.(*group.GetGroupAnnouncementByIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupAnnouncementLogic_GetGroupAnnouncementEditAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(group.GetGroupAnnouncementEditAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupAnnouncementLogicServer).GetGroupAnnouncementEditAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GroupAnnouncementLogic/GetGroupAnnouncementEditAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupAnnouncementLogicServer).GetGroupAnnouncementEditAuth(ctx, req.(*group.GetGroupAnnouncementEditAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GroupAnnouncementLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.GroupAnnouncementLogic",
	HandlerType: (*GroupAnnouncementLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGroupAnnouncementList",
			Handler:    _GroupAnnouncementLogic_GetGroupAnnouncementList_Handler,
		},
		{
			MethodName: "PrepareApplyGroupAnnouncement",
			Handler:    _GroupAnnouncementLogic_PrepareApplyGroupAnnouncement_Handler,
		},
		{
			MethodName: "DelGroupAnnouncement",
			Handler:    _GroupAnnouncementLogic_DelGroupAnnouncement_Handler,
		},
		{
			MethodName: "GetGroupAnnouncementCheck",
			Handler:    _GroupAnnouncementLogic_GetGroupAnnouncementCheck_Handler,
		},
		{
			MethodName: "GetGroupAnnouncementByID",
			Handler:    _GroupAnnouncementLogic_GetGroupAnnouncementByID_Handler,
		},
		{
			MethodName: "GetGroupAnnouncementEditAuth",
			Handler:    _GroupAnnouncementLogic_GetGroupAnnouncementEditAuth_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/group/group-announcement-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/group/group-announcement-logic.proto", fileDescriptor_group_announcement_logic_c8b6bb77dc2a83aa)
}

var fileDescriptor_group_announcement_logic_c8b6bb77dc2a83aa = []byte{
	// 357 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x93, 0xbb, 0x4e, 0xf3, 0x30,
	0x18, 0x86, 0x97, 0xbf, 0xd6, 0x8f, 0x27, 0x64, 0x21, 0xa0, 0xa1, 0x9c, 0xcb, 0xc0, 0x50, 0x07,
	0x15, 0x71, 0x01, 0x2d, 0xad, 0x2a, 0x24, 0x06, 0xc4, 0x88, 0x90, 0x90, 0x09, 0x56, 0x1a, 0x35,
	0x8d, 0x8d, 0xed, 0xb6, 0xea, 0xfd, 0xf8, 0x26, 0x7a, 0x03, 0xac, 0x9d, 0x39, 0x5e, 0x04, 0x23,
	0x13, 0xb2, 0x43, 0x95, 0xa0, 0xd4, 0x2d, 0x4b, 0xa2, 0xe8, 0x7d, 0xbe, 0xf7, 0x91, 0xec, 0x7c,
	0xf0, 0x24, 0x66, 0x61, 0x14, 0xc8, 0xa1, 0xa8, 0x85, 0xcc, 0x0f, 0x05, 0x1b, 0xf0, 0xf4, 0x59,
	0x23, 0x49, 0xc2, 0x06, 0x49, 0x40, 0xfb, 0x34, 0x51, 0x35, 0x0b, 0x61, 0x2e, 0x98, 0x62, 0xa8,
	0x64, 0x3f, 0xbc, 0x55, 0xc2, 0x7f, 0xd0, 0xbb, 0x34, 0xf0, 0x8e, 0x7f, 0x55, 0x11, 0x45, 0x47,
	0x64, 0xec, 0x33, 0xae, 0x22, 0x96, 0xc8, 0xd9, 0x3b, 0x45, 0xeb, 0x4f, 0x25, 0xb8, 0xde, 0x31,
	0xb3, 0x8d, 0x9c, 0xe5, 0xd2, 0x8c, 0xa3, 0x2e, 0xdc, 0xec, 0x50, 0x55, 0x0c, 0x23, 0xa9, 0xd0,
	0x2e, 0x0e, 0x09, 0x76, 0xa5, 0xd7, 0xf4, 0xd1, 0xdb, 0x5b, 0x0c, 0x48, 0x7e, 0xb0, 0xf2, 0x39,
	0x9d, 0xe0, 0x7f, 0xff, 0x9f, 0x35, 0x40, 0x23, 0xb8, 0x7d, 0x25, 0x28, 0x27, 0x82, 0x36, 0x38,
	0x8f, 0xc7, 0x85, 0x19, 0x54, 0x35, 0x6d, 0x0b, 0x11, 0xe3, 0x3c, 0xfa, 0x03, 0x95, 0x89, 0x5f,
	0x34, 0x40, 0xb7, 0x70, 0xad, 0x45, 0xe3, 0xa2, 0x6f, 0xcb, 0x34, 0xcd, 0x4b, 0x8c, 0xa6, 0xe2,
	0x0e, 0xb3, 0xf6, 0x57, 0x0d, 0x50, 0x0f, 0x96, 0xe7, 0x9d, 0xc0, 0x79, 0x97, 0x06, 0x3d, 0xe4,
	0x3c, 0x20, 0x1b, 0x1b, 0xcf, 0xfe, 0x12, 0x22, 0x93, 0xbd, 0x69, 0xe0, 0xba, 0xad, 0xe6, 0xf8,
	0xa2, 0xe5, 0xbe, 0x2d, 0x93, 0x2e, 0xbc, 0xad, 0x14, 0xc8, 0x4c, 0xef, 0x1a, 0x20, 0x05, 0x2b,
	0xf3, 0xd0, 0xf6, 0x43, 0xa4, 0x1a, 0x03, 0xd5, 0x45, 0x87, 0xae, 0xb2, 0x19, 0x61, 0x8c, 0xd5,
	0xe5, 0x50, 0x66, 0xfd, 0xd0, 0xc0, 0xdb, 0xf9, 0x9a, 0x4e, 0x70, 0x19, 0x6e, 0xe4, 0x76, 0x22,
	0xbf, 0x12, 0xcd, 0xce, 0x4d, 0x3b, 0x64, 0x31, 0x49, 0x42, 0x7c, 0x56, 0x57, 0x0a, 0x07, 0xac,
	0xef, 0xdb, 0x3f, 0x3c, 0x60, 0xb1, 0x2f, 0xa9, 0x18, 0x46, 0x01, 0x95, 0x7e, 0x71, 0xc5, 0xf2,
	0xcb, 0x65, 0xd3, 0x7b, 0x60, 0xc7, 0x4e, 0xbf, 0x03, 0x00, 0x00, 0xff, 0xff, 0xa5, 0xfa, 0xb6,
	0x05, 0x90, 0x03, 0x00, 0x00,
}
