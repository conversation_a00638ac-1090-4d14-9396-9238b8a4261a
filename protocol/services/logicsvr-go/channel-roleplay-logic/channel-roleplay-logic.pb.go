// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-roleplay-logic.proto

package channel_roleplay_logic // import "golang.52tt.com/protocol/services/logicsvr-go/channel-roleplay-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel_roleplay_logic "golang.52tt.com/protocol/app/channel-roleplay-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelRoleplayLogicClient is the client API for ChannelRoleplayLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelRoleplayLogicClient interface {
	// 获取房间内麦上用户角色信息
	GetChannelHoldMicUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelHoldMicUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelHoldMicUserRoleListResp, error)
	// 修改自己的房间内角色信息
	SetMyChannelRole(ctx context.Context, in *channel_roleplay_logic.SetMyChannelRoleReq, opts ...grpc.CallOption) (*channel_roleplay_logic.SetMyChannelRoleResp, error)
	EnterBox(ctx context.Context, in *channel_roleplay_logic.EnterBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.EnterBoxResp, error)
	ExitBox(ctx context.Context, in *channel_roleplay_logic.ExitBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.ExitBoxResp, error)
	HandleApplyBox(ctx context.Context, in *channel_roleplay_logic.HandleApplyBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.HandleApplyBoxResp, error)
	GetBoxInfo(ctx context.Context, in *channel_roleplay_logic.GetBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfoResp, error)
	GetBoxInfosByLimit(ctx context.Context, in *channel_roleplay_logic.GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfosByLimitResp, error)
	GetChannelUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelUserRoleListResp, error)
	// 创建/修改子频道
	UpsertBoxInfo(ctx context.Context, in *channel_roleplay_logic.UpsertBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.UpsertBoxInfoResp, error)
	// 删除子频道
	DelBoxInfo(ctx context.Context, in *channel_roleplay_logic.DelBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.DelBoxInfoResp, error)
	// 主房间开启公共频道
	OpenCommonMic(ctx context.Context, in *channel_roleplay_logic.OpenCommonMicReq, opts ...grpc.CallOption) (*channel_roleplay_logic.OpenCommonMicResp, error)
}

type channelRoleplayLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelRoleplayLogicClient(cc *grpc.ClientConn) ChannelRoleplayLogicClient {
	return &channelRoleplayLogicClient{cc}
}

func (c *channelRoleplayLogicClient) GetChannelHoldMicUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelHoldMicUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelHoldMicUserRoleListResp, error) {
	out := new(channel_roleplay_logic.GetChannelHoldMicUserRoleListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/GetChannelHoldMicUserRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) SetMyChannelRole(ctx context.Context, in *channel_roleplay_logic.SetMyChannelRoleReq, opts ...grpc.CallOption) (*channel_roleplay_logic.SetMyChannelRoleResp, error) {
	out := new(channel_roleplay_logic.SetMyChannelRoleResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/SetMyChannelRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) EnterBox(ctx context.Context, in *channel_roleplay_logic.EnterBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.EnterBoxResp, error) {
	out := new(channel_roleplay_logic.EnterBoxResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/EnterBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) ExitBox(ctx context.Context, in *channel_roleplay_logic.ExitBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.ExitBoxResp, error) {
	out := new(channel_roleplay_logic.ExitBoxResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/ExitBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) HandleApplyBox(ctx context.Context, in *channel_roleplay_logic.HandleApplyBoxReq, opts ...grpc.CallOption) (*channel_roleplay_logic.HandleApplyBoxResp, error) {
	out := new(channel_roleplay_logic.HandleApplyBoxResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/HandleApplyBox", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) GetBoxInfo(ctx context.Context, in *channel_roleplay_logic.GetBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfoResp, error) {
	out := new(channel_roleplay_logic.GetBoxInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/GetBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) GetBoxInfosByLimit(ctx context.Context, in *channel_roleplay_logic.GetBoxInfosByLimitReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetBoxInfosByLimitResp, error) {
	out := new(channel_roleplay_logic.GetBoxInfosByLimitResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/GetBoxInfosByLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) GetChannelUserRoleList(ctx context.Context, in *channel_roleplay_logic.GetChannelUserRoleListReq, opts ...grpc.CallOption) (*channel_roleplay_logic.GetChannelUserRoleListResp, error) {
	out := new(channel_roleplay_logic.GetChannelUserRoleListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/GetChannelUserRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) UpsertBoxInfo(ctx context.Context, in *channel_roleplay_logic.UpsertBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.UpsertBoxInfoResp, error) {
	out := new(channel_roleplay_logic.UpsertBoxInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/UpsertBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) DelBoxInfo(ctx context.Context, in *channel_roleplay_logic.DelBoxInfoReq, opts ...grpc.CallOption) (*channel_roleplay_logic.DelBoxInfoResp, error) {
	out := new(channel_roleplay_logic.DelBoxInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/DelBoxInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRoleplayLogicClient) OpenCommonMic(ctx context.Context, in *channel_roleplay_logic.OpenCommonMicReq, opts ...grpc.CallOption) (*channel_roleplay_logic.OpenCommonMicResp, error) {
	out := new(channel_roleplay_logic.OpenCommonMicResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelRoleplayLogic/OpenCommonMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelRoleplayLogicServer is the server API for ChannelRoleplayLogic service.
type ChannelRoleplayLogicServer interface {
	// 获取房间内麦上用户角色信息
	GetChannelHoldMicUserRoleList(context.Context, *channel_roleplay_logic.GetChannelHoldMicUserRoleListReq) (*channel_roleplay_logic.GetChannelHoldMicUserRoleListResp, error)
	// 修改自己的房间内角色信息
	SetMyChannelRole(context.Context, *channel_roleplay_logic.SetMyChannelRoleReq) (*channel_roleplay_logic.SetMyChannelRoleResp, error)
	EnterBox(context.Context, *channel_roleplay_logic.EnterBoxReq) (*channel_roleplay_logic.EnterBoxResp, error)
	ExitBox(context.Context, *channel_roleplay_logic.ExitBoxReq) (*channel_roleplay_logic.ExitBoxResp, error)
	HandleApplyBox(context.Context, *channel_roleplay_logic.HandleApplyBoxReq) (*channel_roleplay_logic.HandleApplyBoxResp, error)
	GetBoxInfo(context.Context, *channel_roleplay_logic.GetBoxInfoReq) (*channel_roleplay_logic.GetBoxInfoResp, error)
	GetBoxInfosByLimit(context.Context, *channel_roleplay_logic.GetBoxInfosByLimitReq) (*channel_roleplay_logic.GetBoxInfosByLimitResp, error)
	GetChannelUserRoleList(context.Context, *channel_roleplay_logic.GetChannelUserRoleListReq) (*channel_roleplay_logic.GetChannelUserRoleListResp, error)
	// 创建/修改子频道
	UpsertBoxInfo(context.Context, *channel_roleplay_logic.UpsertBoxInfoReq) (*channel_roleplay_logic.UpsertBoxInfoResp, error)
	// 删除子频道
	DelBoxInfo(context.Context, *channel_roleplay_logic.DelBoxInfoReq) (*channel_roleplay_logic.DelBoxInfoResp, error)
	// 主房间开启公共频道
	OpenCommonMic(context.Context, *channel_roleplay_logic.OpenCommonMicReq) (*channel_roleplay_logic.OpenCommonMicResp, error)
}

func RegisterChannelRoleplayLogicServer(s *grpc.Server, srv ChannelRoleplayLogicServer) {
	s.RegisterService(&_ChannelRoleplayLogic_serviceDesc, srv)
}

func _ChannelRoleplayLogic_GetChannelHoldMicUserRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetChannelHoldMicUserRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetChannelHoldMicUserRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/GetChannelHoldMicUserRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetChannelHoldMicUserRoleList(ctx, req.(*channel_roleplay_logic.GetChannelHoldMicUserRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_SetMyChannelRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.SetMyChannelRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).SetMyChannelRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/SetMyChannelRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).SetMyChannelRole(ctx, req.(*channel_roleplay_logic.SetMyChannelRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_EnterBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.EnterBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).EnterBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/EnterBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).EnterBox(ctx, req.(*channel_roleplay_logic.EnterBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_ExitBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.ExitBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).ExitBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/ExitBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).ExitBox(ctx, req.(*channel_roleplay_logic.ExitBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_HandleApplyBox_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.HandleApplyBoxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).HandleApplyBox(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/HandleApplyBox",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).HandleApplyBox(ctx, req.(*channel_roleplay_logic.HandleApplyBoxReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_GetBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/GetBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfo(ctx, req.(*channel_roleplay_logic.GetBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_GetBoxInfosByLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetBoxInfosByLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfosByLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/GetBoxInfosByLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetBoxInfosByLimit(ctx, req.(*channel_roleplay_logic.GetBoxInfosByLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_GetChannelUserRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.GetChannelUserRoleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).GetChannelUserRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/GetChannelUserRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).GetChannelUserRoleList(ctx, req.(*channel_roleplay_logic.GetChannelUserRoleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_UpsertBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.UpsertBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).UpsertBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/UpsertBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).UpsertBoxInfo(ctx, req.(*channel_roleplay_logic.UpsertBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_DelBoxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.DelBoxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).DelBoxInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/DelBoxInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).DelBoxInfo(ctx, req.(*channel_roleplay_logic.DelBoxInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRoleplayLogic_OpenCommonMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_roleplay_logic.OpenCommonMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRoleplayLogicServer).OpenCommonMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelRoleplayLogic/OpenCommonMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRoleplayLogicServer).OpenCommonMic(ctx, req.(*channel_roleplay_logic.OpenCommonMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelRoleplayLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.ChannelRoleplayLogic",
	HandlerType: (*ChannelRoleplayLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelHoldMicUserRoleList",
			Handler:    _ChannelRoleplayLogic_GetChannelHoldMicUserRoleList_Handler,
		},
		{
			MethodName: "SetMyChannelRole",
			Handler:    _ChannelRoleplayLogic_SetMyChannelRole_Handler,
		},
		{
			MethodName: "EnterBox",
			Handler:    _ChannelRoleplayLogic_EnterBox_Handler,
		},
		{
			MethodName: "ExitBox",
			Handler:    _ChannelRoleplayLogic_ExitBox_Handler,
		},
		{
			MethodName: "HandleApplyBox",
			Handler:    _ChannelRoleplayLogic_HandleApplyBox_Handler,
		},
		{
			MethodName: "GetBoxInfo",
			Handler:    _ChannelRoleplayLogic_GetBoxInfo_Handler,
		},
		{
			MethodName: "GetBoxInfosByLimit",
			Handler:    _ChannelRoleplayLogic_GetBoxInfosByLimit_Handler,
		},
		{
			MethodName: "GetChannelUserRoleList",
			Handler:    _ChannelRoleplayLogic_GetChannelUserRoleList_Handler,
		},
		{
			MethodName: "UpsertBoxInfo",
			Handler:    _ChannelRoleplayLogic_UpsertBoxInfo_Handler,
		},
		{
			MethodName: "DelBoxInfo",
			Handler:    _ChannelRoleplayLogic_DelBoxInfo_Handler,
		},
		{
			MethodName: "OpenCommonMic",
			Handler:    _ChannelRoleplayLogic_OpenCommonMic_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-roleplay-logic.proto",
}

func init() {
	proto.RegisterFile("channel-roleplay-logic.proto", fileDescriptor_channel_roleplay_logic_f93ab748488304a4)
}

var fileDescriptor_channel_roleplay_logic_f93ab748488304a4 = []byte{
	// 501 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x94, 0x3d, 0x6f, 0x13, 0x31,
	0x18, 0xc7, 0x15, 0x01, 0xa6, 0x18, 0x81, 0xd0, 0x23, 0x54, 0xa1, 0x88, 0x0e, 0x20, 0x51, 0xa8,
	0x68, 0xee, 0xa0, 0xd0, 0x09, 0x16, 0xd2, 0x96, 0x16, 0x29, 0x11, 0x52, 0x51, 0x17, 0x96, 0xc8,
	0xb8, 0xee, 0x61, 0xc9, 0xb1, 0xcd, 0xd9, 0x94, 0xdc, 0xc2, 0x54, 0x09, 0x46, 0xbe, 0x04, 0x9b,
	0xbf, 0x40, 0x47, 0xde, 0x5f, 0x96, 0x7e, 0x17, 0x46, 0x26, 0xe4, 0x5c, 0x82, 0x93, 0x28, 0x72,
	0x2f, 0x9d, 0x4e, 0x27, 0xff, 0x9e, 0xff, 0xef, 0xb1, 0x1e, 0xdb, 0xf8, 0x2a, 0x7d, 0x49, 0xa4,
	0x64, 0xa2, 0x91, 0x2b, 0xc1, 0xb4, 0x20, 0x45, 0x43, 0xa8, 0x8c, 0xd3, 0x44, 0xe7, 0xca, 0x2a,
	0x38, 0xd3, 0xff, 0xa9, 0x2f, 0xf5, 0x3f, 0x66, 0x3f, 0x6f, 0x64, 0x2a, 0xcd, 0x88, 0x65, 0x6f,
	0x48, 0x91, 0x2a, 0x6d, 0xb9, 0x92, 0x66, 0xf8, 0x2d, 0x2b, 0xea, 0xd7, 0x88, 0xd6, 0xe9, 0xf4,
	0xcc, 0x4e, 0x89, 0xac, 0xbc, 0x3f, 0x8f, 0x2f, 0xaf, 0x95, 0xc4, 0xf6, 0x00, 0x68, 0xf9, 0x75,
	0xf8, 0x58, 0xc3, 0x0b, 0x9b, 0xcc, 0x0e, 0xd6, 0xb6, 0x94, 0xd8, 0x6d, 0x73, 0xba, 0x63, 0x58,
	0xee, 0xb1, 0x16, 0x37, 0x16, 0x1e, 0x24, 0x19, 0x49, 0x06, 0xe9, 0x9d, 0x61, 0x7a, 0xa7, 0xec,
	0x38, 0x5a, 0xb9, 0xcd, 0x5e, 0xd5, 0x1f, 0x9e, 0xbc, 0xd8, 0xe8, 0xeb, 0xe7, 0xfe, 0x1c, 0x1d,
	0x26, 0xa7, 0xe7, 0x3e, 0x39, 0x04, 0x6f, 0xf1, 0xa5, 0x67, 0xcc, 0xb6, 0x8b, 0x91, 0x4d, 0x40,
	0x12, 0x09, 0x9f, 0x84, 0x7d, 0x33, 0xe9, 0x4c, 0x7c, 0xf0, 0x7f, 0x76, 0x08, 0xf6, 0xf0, 0xdc,
	0x86, 0xb4, 0x2c, 0x6f, 0xaa, 0x1e, 0x2c, 0x46, 0x72, 0x86, 0x90, 0xf7, 0xdd, 0xac, 0xc4, 0x05,
	0xcf, 0x17, 0x87, 0x80, 0xe2, 0xb3, 0x1b, 0x3d, 0x6e, 0xbd, 0xe6, 0x46, 0xac, 0xbc, 0x64, 0xbc,
	0x65, 0xb1, 0x0a, 0x16, 0x24, 0x5f, 0x1d, 0x82, 0x1e, 0xbe, 0xb8, 0x45, 0xe4, 0xae, 0x60, 0x8f,
	0xb4, 0x16, 0x85, 0x77, 0x2d, 0x47, 0x42, 0xc6, 0x51, 0xaf, 0x6c, 0xcc, 0x40, 0x07, 0xf3, 0x37,
	0x87, 0x40, 0x60, 0xbc, 0xc9, 0x7c, 0x4b, 0x4f, 0xe4, 0x9e, 0x82, 0x5b, 0xf1, 0xd3, 0x31, 0xc0,
	0xbc, 0x71, 0xa9, 0x22, 0x19, 0x6c, 0xdf, 0x1d, 0x82, 0x83, 0x1a, 0x86, 0xb0, 0x6a, 0x9a, 0x45,
	0x8b, 0x77, 0xb9, 0x85, 0x3b, 0x95, 0xc2, 0x86, 0xb8, 0xd7, 0xdf, 0x9d, 0xb1, 0x22, 0xb4, 0xf1,
	0xc3, 0x21, 0xf8, 0x50, 0xc3, 0xf3, 0xe1, 0xb0, 0x8f, 0x5d, 0xae, 0xfb, 0x95, 0xee, 0xc7, 0xe4,
	0xad, 0x5a, 0x3d, 0x41, 0x55, 0x68, 0xe9, 0xa7, 0x43, 0xf0, 0x1a, 0x5f, 0xd8, 0xd1, 0x86, 0xe5,
	0xff, 0x47, 0x71, 0x3b, 0x12, 0x39, 0x46, 0x7a, 0xff, 0x72, 0x75, 0x38, 0x68, 0x7f, 0x95, 0xe3,
	0x5f, 0x67, 0xa2, 0xca, 0xf8, 0x03, 0x76, 0xdc, 0xf8, 0x47, 0xc9, 0x60, 0xfb, 0x5d, 0x6e, 0xf2,
	0xa9, 0x66, 0x72, 0x4d, 0x75, 0xbb, 0x4a, 0xb6, 0x39, 0x8d, 0x6e, 0x72, 0x8c, 0x3c, 0x6e, 0x93,
	0x13, 0x70, 0xd0, 0x1e, 0xbc, 0x3b, 0x55, 0x5f, 0xf8, 0x7b, 0x74, 0x98, 0x5c, 0xc1, 0xf3, 0xd3,
	0x5f, 0xe4, 0xe6, 0xe3, 0xe7, 0xeb, 0x99, 0x12, 0x44, 0x66, 0xc9, 0xea, 0x8a, 0xb5, 0x09, 0x55,
	0xdd, 0xb4, 0xff, 0x46, 0x53, 0x25, 0x52, 0xc3, 0xf2, 0x7d, 0x4e, 0x99, 0x49, 0x47, 0x1f, 0xff,
	0xe9, 0x39, 0x2f, 0x50, 0xbf, 0xea, 0xde, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x68, 0x0e, 0x32,
	0x4f, 0x4e, 0x06, 0x00, 0x00,
}
