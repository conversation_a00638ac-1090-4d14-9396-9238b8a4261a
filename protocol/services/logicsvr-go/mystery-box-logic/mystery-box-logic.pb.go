// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/mystery-box-logic/mystery-box-logic.proto

package mystery_box_logic // import "golang.52tt.com/protocol/services/logicsvr-go/mystery-box-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import mystery_box_logic "golang.52tt.com/protocol/app/mystery-box-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MysteryBoxLogicClient is the client API for MysteryBoxLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MysteryBoxLogicClient interface {
	GetMysteryBoxWin(ctx context.Context, in *mystery_box_logic.GetMysteryBoxWinReq, opts ...grpc.CallOption) (*mystery_box_logic.GetMysteryBoxWinResp, error)
	GetMysteryBoxTask(ctx context.Context, in *mystery_box_logic.GetMysteryBoxTaskReq, opts ...grpc.CallOption) (*mystery_box_logic.GetMysteryBoxTaskResp, error)
}

type mysteryBoxLogicClient struct {
	cc *grpc.ClientConn
}

func NewMysteryBoxLogicClient(cc *grpc.ClientConn) MysteryBoxLogicClient {
	return &mysteryBoxLogicClient{cc}
}

func (c *mysteryBoxLogicClient) GetMysteryBoxWin(ctx context.Context, in *mystery_box_logic.GetMysteryBoxWinReq, opts ...grpc.CallOption) (*mystery_box_logic.GetMysteryBoxWinResp, error) {
	out := new(mystery_box_logic.GetMysteryBoxWinResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryBoxLogic/GetMysteryBoxWin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryBoxLogicClient) GetMysteryBoxTask(ctx context.Context, in *mystery_box_logic.GetMysteryBoxTaskReq, opts ...grpc.CallOption) (*mystery_box_logic.GetMysteryBoxTaskResp, error) {
	out := new(mystery_box_logic.GetMysteryBoxTaskResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryBoxLogic/GetMysteryBoxTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MysteryBoxLogicServer is the server API for MysteryBoxLogic service.
type MysteryBoxLogicServer interface {
	GetMysteryBoxWin(context.Context, *mystery_box_logic.GetMysteryBoxWinReq) (*mystery_box_logic.GetMysteryBoxWinResp, error)
	GetMysteryBoxTask(context.Context, *mystery_box_logic.GetMysteryBoxTaskReq) (*mystery_box_logic.GetMysteryBoxTaskResp, error)
}

func RegisterMysteryBoxLogicServer(s *grpc.Server, srv MysteryBoxLogicServer) {
	s.RegisterService(&_MysteryBoxLogic_serviceDesc, srv)
}

func _MysteryBoxLogic_GetMysteryBoxWin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_box_logic.GetMysteryBoxWinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryBoxLogicServer).GetMysteryBoxWin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryBoxLogic/GetMysteryBoxWin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryBoxLogicServer).GetMysteryBoxWin(ctx, req.(*mystery_box_logic.GetMysteryBoxWinReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryBoxLogic_GetMysteryBoxTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_box_logic.GetMysteryBoxTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryBoxLogicServer).GetMysteryBoxTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryBoxLogic/GetMysteryBoxTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryBoxLogicServer).GetMysteryBoxTask(ctx, req.(*mystery_box_logic.GetMysteryBoxTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MysteryBoxLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.MysteryBoxLogic",
	HandlerType: (*MysteryBoxLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMysteryBoxWin",
			Handler:    _MysteryBoxLogic_GetMysteryBoxWin_Handler,
		},
		{
			MethodName: "GetMysteryBoxTask",
			Handler:    _MysteryBoxLogic_GetMysteryBoxTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/mystery-box-logic/mystery-box-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/mystery-box-logic/mystery-box-logic.proto", fileDescriptor_mystery_box_logic_fafe30e9a69a1a21)
}

var fileDescriptor_mystery_box_logic_fafe30e9a69a1a21 = []byte{
	// 242 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x32, 0xcd, 0xc9, 0x4f, 0xcf,
	0x4c, 0x2e, 0x2e, 0x2b, 0xd2, 0x4d, 0xcf, 0xd7, 0xcf, 0xad, 0x2c, 0x2e, 0x49, 0x2d, 0xaa, 0xd4,
	0x4d, 0xca, 0xaf, 0xd0, 0x05, 0x8b, 0x63, 0x8a, 0xe8, 0x15, 0x14, 0xe5, 0x97, 0xe4, 0x0b, 0xb1,
	0x82, 0x39, 0x52, 0x9a, 0xc8, 0xba, 0xd3, 0x13, 0x4b, 0x52, 0xcb, 0x13, 0x2b, 0xf5, 0xf3, 0x0b,
	0x4a, 0x32, 0xf3, 0xf3, 0x8a, 0x61, 0x34, 0x44, 0x87, 0x94, 0x4c, 0x62, 0x41, 0x01, 0xa6, 0x71,
	0xf1, 0x10, 0x59, 0xa3, 0x59, 0x4c, 0x5c, 0xfc, 0xbe, 0x10, 0x49, 0xa7, 0xfc, 0x0a, 0x1f, 0x90,
	0x94, 0x50, 0x09, 0x97, 0x80, 0x7b, 0x6a, 0x09, 0x42, 0x34, 0x3c, 0x33, 0x4f, 0x48, 0x53, 0x2f,
	0x3d, 0x51, 0x0f, 0x6a, 0x4a, 0x7c, 0x52, 0x7e, 0x45, 0x3c, 0xc4, 0x51, 0xe8, 0xea, 0x82, 0x52,
	0x0b, 0xa5, 0xb4, 0x88, 0x55, 0x5a, 0x5c, 0xa0, 0xc4, 0xf9, 0xe9, 0xfc, 0x4e, 0x3d, 0x16, 0x8e,
	0x7d, 0x4d, 0x4c, 0x42, 0xe5, 0x5c, 0x82, 0x28, 0x4a, 0x42, 0x12, 0x8b, 0xb3, 0x85, 0x88, 0x31,
	0x0b, 0xa4, 0x10, 0x64, 0xaf, 0x36, 0xd1, 0x6a, 0x11, 0x16, 0xef, 0x6f, 0x62, 0x92, 0x92, 0xf8,
	0x75, 0x7e, 0xa7, 0x9e, 0x30, 0x97, 0x20, 0x46, 0x18, 0x39, 0x39, 0x46, 0xd9, 0xa7, 0xe7, 0xe7,
	0x24, 0xe6, 0xa5, 0xeb, 0x99, 0x1a, 0x95, 0x94, 0xe8, 0x25, 0xe7, 0xe7, 0xea, 0x83, 0x43, 0x2d,
	0x39, 0x3f, 0x47, 0xbf, 0x38, 0xb5, 0xa8, 0x2c, 0x33, 0x39, 0xb5, 0x58, 0x1f, 0x6f, 0x3c, 0x26,
	0xb1, 0x81, 0x35, 0x18, 0x03, 0x02, 0x00, 0x00, 0xff, 0xff, 0xa2, 0xad, 0xdd, 0x96, 0xef, 0x01,
	0x00, 0x00,
}
