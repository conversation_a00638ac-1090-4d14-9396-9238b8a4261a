// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/super-channel-logic/super-channe-logic.proto

package logic

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import super_channel "golang.52tt.com/protocol/app/super-channel"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SuperChannelLogicClient is the client API for SuperChannelLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SuperChannelLogicClient interface {
	Enter(ctx context.Context, in *super_channel.SuperChannelEnterReq, opts ...grpc.CallOption) (*super_channel.SuperChannelEnterResp, error)
	Quit(ctx context.Context, in *super_channel.SuperChannelQuitReq, opts ...grpc.CallOption) (*super_channel.SuperChannelQuitResp, error)
	HoldMic(ctx context.Context, in *super_channel.SuperChannelHoldMicReq, opts ...grpc.CallOption) (*super_channel.SuperChannelHoldMicResp, error)
	ReleaseMic(ctx context.Context, in *super_channel.SuperChannelReleaseMicReq, opts ...grpc.CallOption) (*super_channel.SuperChannelReleaseMicResp, error)
	SetMicStatus(ctx context.Context, in *super_channel.SuperChannelSetMicStatusReq, opts ...grpc.CallOption) (*super_channel.SuperChannelSetMicStatusResp, error)
	SetMicMode(ctx context.Context, in *super_channel.SuperChannelSetMicModeReq, opts ...grpc.CallOption) (*super_channel.SuperChannelSetMicModeResp, error)
	GetMicList(ctx context.Context, in *super_channel.SuperChannelGetMicListReq, opts ...grpc.CallOption) (*super_channel.SuperChannelGetMicListResp, error)
	GetMemberList(ctx context.Context, in *super_channel.SuperChannelGetMemberListReq, opts ...grpc.CallOption) (*super_channel.SuperChannelGetMemberListResp, error)
	GetExtInfo(ctx context.Context, in *super_channel.SuperChannelGetExtInfoReq, opts ...grpc.CallOption) (*super_channel.SuperChannelGetExtInfoResp, error)
	SendHoldMicInvite(ctx context.Context, in *super_channel.SuperChannelSendHoldMicInviteReq, opts ...grpc.CallOption) (*super_channel.SuperChannelSendHoldMicInviteResp, error)
	ReplyHoldMicInvite(ctx context.Context, in *super_channel.SuperChannelReplyHoldMicInviteReq, opts ...grpc.CallOption) (*super_channel.SuperChannelReplyHoldMicInviteResp, error)
	ChangeMic(ctx context.Context, in *super_channel.SuperChannelChangeMicReq, opts ...grpc.CallOption) (*super_channel.SuperChannelChangeMicResp, error)
	GetChannelList(ctx context.Context, in *super_channel.SuperChannelGetChannelListReq, opts ...grpc.CallOption) (*super_channel.SuperChannelGetChannelListResp, error)
	CplSearch(ctx context.Context, in *super_channel.SuperChannelSearchReq, opts ...grpc.CallOption) (*super_channel.SuperChannelSearchResp, error)
	GetSuperChannelSchemeInfo(ctx context.Context, in *super_channel.GetSuperChannelSchemeInfoReq, opts ...grpc.CallOption) (*super_channel.GetSuperChannelSchemeInfoResp, error)
}

type superChannelLogicClient struct {
	cc *grpc.ClientConn
}

func NewSuperChannelLogicClient(cc *grpc.ClientConn) SuperChannelLogicClient {
	return &superChannelLogicClient{cc}
}

func (c *superChannelLogicClient) Enter(ctx context.Context, in *super_channel.SuperChannelEnterReq, opts ...grpc.CallOption) (*super_channel.SuperChannelEnterResp, error) {
	out := new(super_channel.SuperChannelEnterResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/Enter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) Quit(ctx context.Context, in *super_channel.SuperChannelQuitReq, opts ...grpc.CallOption) (*super_channel.SuperChannelQuitResp, error) {
	out := new(super_channel.SuperChannelQuitResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/Quit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) HoldMic(ctx context.Context, in *super_channel.SuperChannelHoldMicReq, opts ...grpc.CallOption) (*super_channel.SuperChannelHoldMicResp, error) {
	out := new(super_channel.SuperChannelHoldMicResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/HoldMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) ReleaseMic(ctx context.Context, in *super_channel.SuperChannelReleaseMicReq, opts ...grpc.CallOption) (*super_channel.SuperChannelReleaseMicResp, error) {
	out := new(super_channel.SuperChannelReleaseMicResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/ReleaseMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) SetMicStatus(ctx context.Context, in *super_channel.SuperChannelSetMicStatusReq, opts ...grpc.CallOption) (*super_channel.SuperChannelSetMicStatusResp, error) {
	out := new(super_channel.SuperChannelSetMicStatusResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/SetMicStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) SetMicMode(ctx context.Context, in *super_channel.SuperChannelSetMicModeReq, opts ...grpc.CallOption) (*super_channel.SuperChannelSetMicModeResp, error) {
	out := new(super_channel.SuperChannelSetMicModeResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/SetMicMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) GetMicList(ctx context.Context, in *super_channel.SuperChannelGetMicListReq, opts ...grpc.CallOption) (*super_channel.SuperChannelGetMicListResp, error) {
	out := new(super_channel.SuperChannelGetMicListResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/GetMicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) GetMemberList(ctx context.Context, in *super_channel.SuperChannelGetMemberListReq, opts ...grpc.CallOption) (*super_channel.SuperChannelGetMemberListResp, error) {
	out := new(super_channel.SuperChannelGetMemberListResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/GetMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) GetExtInfo(ctx context.Context, in *super_channel.SuperChannelGetExtInfoReq, opts ...grpc.CallOption) (*super_channel.SuperChannelGetExtInfoResp, error) {
	out := new(super_channel.SuperChannelGetExtInfoResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/GetExtInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) SendHoldMicInvite(ctx context.Context, in *super_channel.SuperChannelSendHoldMicInviteReq, opts ...grpc.CallOption) (*super_channel.SuperChannelSendHoldMicInviteResp, error) {
	out := new(super_channel.SuperChannelSendHoldMicInviteResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/SendHoldMicInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) ReplyHoldMicInvite(ctx context.Context, in *super_channel.SuperChannelReplyHoldMicInviteReq, opts ...grpc.CallOption) (*super_channel.SuperChannelReplyHoldMicInviteResp, error) {
	out := new(super_channel.SuperChannelReplyHoldMicInviteResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/ReplyHoldMicInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) ChangeMic(ctx context.Context, in *super_channel.SuperChannelChangeMicReq, opts ...grpc.CallOption) (*super_channel.SuperChannelChangeMicResp, error) {
	out := new(super_channel.SuperChannelChangeMicResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/ChangeMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) GetChannelList(ctx context.Context, in *super_channel.SuperChannelGetChannelListReq, opts ...grpc.CallOption) (*super_channel.SuperChannelGetChannelListResp, error) {
	out := new(super_channel.SuperChannelGetChannelListResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/GetChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) CplSearch(ctx context.Context, in *super_channel.SuperChannelSearchReq, opts ...grpc.CallOption) (*super_channel.SuperChannelSearchResp, error) {
	out := new(super_channel.SuperChannelSearchResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/CplSearch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *superChannelLogicClient) GetSuperChannelSchemeInfo(ctx context.Context, in *super_channel.GetSuperChannelSchemeInfoReq, opts ...grpc.CallOption) (*super_channel.GetSuperChannelSchemeInfoResp, error) {
	out := new(super_channel.GetSuperChannelSchemeInfoResp)
	err := c.cc.Invoke(ctx, "/logic.SuperChannelLogic/GetSuperChannelSchemeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SuperChannelLogicServer is the server API for SuperChannelLogic service.
type SuperChannelLogicServer interface {
	Enter(context.Context, *super_channel.SuperChannelEnterReq) (*super_channel.SuperChannelEnterResp, error)
	Quit(context.Context, *super_channel.SuperChannelQuitReq) (*super_channel.SuperChannelQuitResp, error)
	HoldMic(context.Context, *super_channel.SuperChannelHoldMicReq) (*super_channel.SuperChannelHoldMicResp, error)
	ReleaseMic(context.Context, *super_channel.SuperChannelReleaseMicReq) (*super_channel.SuperChannelReleaseMicResp, error)
	SetMicStatus(context.Context, *super_channel.SuperChannelSetMicStatusReq) (*super_channel.SuperChannelSetMicStatusResp, error)
	SetMicMode(context.Context, *super_channel.SuperChannelSetMicModeReq) (*super_channel.SuperChannelSetMicModeResp, error)
	GetMicList(context.Context, *super_channel.SuperChannelGetMicListReq) (*super_channel.SuperChannelGetMicListResp, error)
	GetMemberList(context.Context, *super_channel.SuperChannelGetMemberListReq) (*super_channel.SuperChannelGetMemberListResp, error)
	GetExtInfo(context.Context, *super_channel.SuperChannelGetExtInfoReq) (*super_channel.SuperChannelGetExtInfoResp, error)
	SendHoldMicInvite(context.Context, *super_channel.SuperChannelSendHoldMicInviteReq) (*super_channel.SuperChannelSendHoldMicInviteResp, error)
	ReplyHoldMicInvite(context.Context, *super_channel.SuperChannelReplyHoldMicInviteReq) (*super_channel.SuperChannelReplyHoldMicInviteResp, error)
	ChangeMic(context.Context, *super_channel.SuperChannelChangeMicReq) (*super_channel.SuperChannelChangeMicResp, error)
	GetChannelList(context.Context, *super_channel.SuperChannelGetChannelListReq) (*super_channel.SuperChannelGetChannelListResp, error)
	CplSearch(context.Context, *super_channel.SuperChannelSearchReq) (*super_channel.SuperChannelSearchResp, error)
	GetSuperChannelSchemeInfo(context.Context, *super_channel.GetSuperChannelSchemeInfoReq) (*super_channel.GetSuperChannelSchemeInfoResp, error)
}

func RegisterSuperChannelLogicServer(s *grpc.Server, srv SuperChannelLogicServer) {
	s.RegisterService(&_SuperChannelLogic_serviceDesc, srv)
}

func _SuperChannelLogic_Enter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelEnterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).Enter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/Enter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).Enter(ctx, req.(*super_channel.SuperChannelEnterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_Quit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelQuitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).Quit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/Quit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).Quit(ctx, req.(*super_channel.SuperChannelQuitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_HoldMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelHoldMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).HoldMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/HoldMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).HoldMic(ctx, req.(*super_channel.SuperChannelHoldMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_ReleaseMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelReleaseMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).ReleaseMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/ReleaseMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).ReleaseMic(ctx, req.(*super_channel.SuperChannelReleaseMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_SetMicStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelSetMicStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).SetMicStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/SetMicStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).SetMicStatus(ctx, req.(*super_channel.SuperChannelSetMicStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_SetMicMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelSetMicModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).SetMicMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/SetMicMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).SetMicMode(ctx, req.(*super_channel.SuperChannelSetMicModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_GetMicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelGetMicListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).GetMicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/GetMicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).GetMicList(ctx, req.(*super_channel.SuperChannelGetMicListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_GetMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelGetMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).GetMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/GetMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).GetMemberList(ctx, req.(*super_channel.SuperChannelGetMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_GetExtInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelGetExtInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).GetExtInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/GetExtInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).GetExtInfo(ctx, req.(*super_channel.SuperChannelGetExtInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_SendHoldMicInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelSendHoldMicInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).SendHoldMicInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/SendHoldMicInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).SendHoldMicInvite(ctx, req.(*super_channel.SuperChannelSendHoldMicInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_ReplyHoldMicInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelReplyHoldMicInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).ReplyHoldMicInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/ReplyHoldMicInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).ReplyHoldMicInvite(ctx, req.(*super_channel.SuperChannelReplyHoldMicInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_ChangeMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelChangeMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).ChangeMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/ChangeMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).ChangeMic(ctx, req.(*super_channel.SuperChannelChangeMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_GetChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelGetChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).GetChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/GetChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).GetChannelList(ctx, req.(*super_channel.SuperChannelGetChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_CplSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.SuperChannelSearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).CplSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/CplSearch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).CplSearch(ctx, req.(*super_channel.SuperChannelSearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SuperChannelLogic_GetSuperChannelSchemeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(super_channel.GetSuperChannelSchemeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SuperChannelLogicServer).GetSuperChannelSchemeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SuperChannelLogic/GetSuperChannelSchemeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SuperChannelLogicServer).GetSuperChannelSchemeInfo(ctx, req.(*super_channel.GetSuperChannelSchemeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SuperChannelLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.SuperChannelLogic",
	HandlerType: (*SuperChannelLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Enter",
			Handler:    _SuperChannelLogic_Enter_Handler,
		},
		{
			MethodName: "Quit",
			Handler:    _SuperChannelLogic_Quit_Handler,
		},
		{
			MethodName: "HoldMic",
			Handler:    _SuperChannelLogic_HoldMic_Handler,
		},
		{
			MethodName: "ReleaseMic",
			Handler:    _SuperChannelLogic_ReleaseMic_Handler,
		},
		{
			MethodName: "SetMicStatus",
			Handler:    _SuperChannelLogic_SetMicStatus_Handler,
		},
		{
			MethodName: "SetMicMode",
			Handler:    _SuperChannelLogic_SetMicMode_Handler,
		},
		{
			MethodName: "GetMicList",
			Handler:    _SuperChannelLogic_GetMicList_Handler,
		},
		{
			MethodName: "GetMemberList",
			Handler:    _SuperChannelLogic_GetMemberList_Handler,
		},
		{
			MethodName: "GetExtInfo",
			Handler:    _SuperChannelLogic_GetExtInfo_Handler,
		},
		{
			MethodName: "SendHoldMicInvite",
			Handler:    _SuperChannelLogic_SendHoldMicInvite_Handler,
		},
		{
			MethodName: "ReplyHoldMicInvite",
			Handler:    _SuperChannelLogic_ReplyHoldMicInvite_Handler,
		},
		{
			MethodName: "ChangeMic",
			Handler:    _SuperChannelLogic_ChangeMic_Handler,
		},
		{
			MethodName: "GetChannelList",
			Handler:    _SuperChannelLogic_GetChannelList_Handler,
		},
		{
			MethodName: "CplSearch",
			Handler:    _SuperChannelLogic_CplSearch_Handler,
		},
		{
			MethodName: "GetSuperChannelSchemeInfo",
			Handler:    _SuperChannelLogic_GetSuperChannelSchemeInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/super-channel-logic/super-channe-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/super-channel-logic/super-channe-logic.proto", fileDescriptor_super_channe_logic_0c1d74a791a30525)
}

var fileDescriptor_super_channe_logic_0c1d74a791a30525 = []byte{
	// 516 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x94, 0xcd, 0x6e, 0xd3, 0x40,
	0x10, 0xc7, 0x2f, 0xed, 0x42, 0x56, 0x80, 0xd4, 0x41, 0x88, 0xc4, 0x50, 0x28, 0x15, 0x45, 0xe2,
	0x10, 0x57, 0x82, 0x0b, 0xf7, 0x52, 0x85, 0x4a, 0xcd, 0x81, 0x58, 0xe2, 0x84, 0x54, 0x39, 0xce,
	0xe0, 0x58, 0xb8, 0x5e, 0x63, 0x6f, 0x0a, 0x7d, 0x88, 0x7d, 0x08, 0xbe, 0xbf, 0x11, 0xd7, 0x3e,
	0x45, 0xdf, 0x85, 0x23, 0x07, 0x84, 0x66, 0x9d, 0x7a, 0x1d, 0x7f, 0x35, 0xa7, 0x28, 0xf3, 0xfb,
	0xef, 0x6f, 0x46, 0xde, 0xd1, 0xf2, 0x47, 0xa1, 0xf0, 0x03, 0x2f, 0x3d, 0x4a, 0xfa, 0xbe, 0xd8,
	0x4e, 0x67, 0x31, 0x26, 0x7d, 0x6f, 0xea, 0x46, 0x11, 0x86, 0x7d, 0x4d, 0x16, 0x6a, 0x59, 0xc9,
	0x8e, 0x13, 0x21, 0x05, 0xac, 0xea, 0x3f, 0xd6, 0xfd, 0xa2, 0xc0, 0x77, 0x25, 0xbe, 0x76, 0x8f,
	0xb7, 0x45, 0x2c, 0x03, 0x11, 0xa5, 0x67, 0xbf, 0xd9, 0x09, 0xab, 0xeb, 0xc6, 0xf1, 0x62, 0x8f,
	0x83, 0x8c, 0x3c, 0xf8, 0xc7, 0xf9, 0x9a, 0x43, 0x60, 0x27, 0xab, 0xef, 0x93, 0x13, 0x06, 0x7c,
	0x75, 0x37, 0x92, 0x98, 0x40, 0xd7, 0xf6, 0x5d, 0xbb, 0xc8, 0x75, 0x79, 0x84, 0xaf, 0xac, 0x5e,
	0x03, 0x49, 0xe3, 0xcd, 0xce, 0x9f, 0xd3, 0x13, 0x7b, 0xe5, 0xe2, 0x5b, 0xc5, 0xe0, 0x31, 0x5f,
	0x79, 0x3a, 0x0b, 0x24, 0x5c, 0x2f, 0xa7, 0xa9, 0x4a, 0x9a, 0x6e, 0x3d, 0x30, 0x96, 0x77, 0x8a,
	0xc1, 0x90, 0x5f, 0x78, 0x22, 0xc2, 0xc9, 0x30, 0xf0, 0xc0, 0x2a, 0xe7, 0xe7, 0x80, 0x5c, 0x37,
	0x1a, 0x99, 0xd1, 0xbd, 0x57, 0x0c, 0x9e, 0x71, 0x3e, 0xc2, 0x10, 0xdd, 0x14, 0xc9, 0xb8, 0x5e,
	0x3e, 0x65, 0x18, 0x49, 0x6f, 0xb5, 0x61, 0xe3, 0xfd, 0xa0, 0x18, 0x3c, 0xe7, 0x97, 0x1c, 0x94,
	0xc3, 0xc0, 0x73, 0xa4, 0x2b, 0x67, 0x29, 0xdc, 0x2e, 0x1f, 0x2d, 0x52, 0x72, 0x6f, 0xb4, 0x07,
	0x8c, 0xfd, 0x63, 0x36, 0x75, 0x86, 0x87, 0x62, 0x82, 0xd5, 0xa9, 0x0d, 0xab, 0x9d, 0xba, 0x88,
	0x8d, 0xf7, 0x53, 0xe6, 0x1d, 0x68, 0xb8, 0x1f, 0xa4, 0xb2, 0xea, 0x35, 0xac, 0xd6, 0x5b, 0xc4,
	0xc6, 0xfb, 0x59, 0x31, 0x38, 0xe0, 0x97, 0x09, 0xe2, 0xe1, 0x18, 0x13, 0xad, 0xde, 0xa8, 0x3b,
	0x9b, 0x63, 0xb2, 0xdf, 0x39, 0x27, 0x61, 0x1a, 0x7c, 0xc9, 0x07, 0xdf, 0x7d, 0x23, 0xf7, 0xa2,
	0x17, 0xa2, 0x76, 0xf0, 0x39, 0x6b, 0x1a, 0x3c, 0xc7, 0xc6, 0xfb, 0x55, 0x31, 0x78, 0xc9, 0xd7,
	0x1c, 0x8c, 0x26, 0xf3, 0xe5, 0xd9, 0x8b, 0x8e, 0x02, 0x89, 0x70, 0xb7, 0xfa, 0x41, 0x4b, 0x11,
	0xea, 0xb2, 0xb5, 0x44, 0xca, 0x34, 0xfb, 0xa6, 0x18, 0x44, 0x1c, 0x46, 0x18, 0x87, 0xc7, 0x8b,
	0xdd, 0xb6, 0xaa, 0x4b, 0x57, 0xce, 0x50, 0xbb, 0x7b, 0xcb, 0xc4, 0x4c, 0xbf, 0xef, 0x8a, 0x81,
	0xc3, 0x3b, 0x94, 0xf5, 0xf5, 0xea, 0xdf, 0x2c, 0x9f, 0xcf, 0x11, 0xd9, 0xd7, 0x5b, 0xa8, 0x91,
	0xfe, 0x50, 0x0c, 0xc6, 0xfc, 0xca, 0x00, 0xe5, 0xd9, 0x0b, 0x42, 0x77, 0x5d, 0x77, 0x93, 0x05,
	0x4e, 0xfa, 0xcd, 0xf3, 0x22, 0xa6, 0xc7, 0x4f, 0xfd, 0x06, 0x74, 0x76, 0xe2, 0xd0, 0x41, 0x37,
	0xf1, 0xa6, 0xd0, 0xab, 0x7e, 0x67, 0xaa, 0x93, 0xd6, 0x6a, 0x42, 0x46, 0xf7, 0x4b, 0x5f, 0x72,
	0x6f, 0x80, 0x72, 0x21, 0xe7, 0x4d, 0xf1, 0x10, 0xf5, 0x2e, 0xe9, 0x4d, 0x6d, 0xc4, 0xf9, 0xa6,
	0xb6, 0x24, 0x4c, 0xb3, 0xdf, 0x8a, 0x59, 0xd6, 0xdf, 0xd3, 0x13, 0xfb, 0x1a, 0xbf, 0x5a, 0xf3,
	0xca, 0x8f, 0x99, 0x7e, 0x87, 0x1f, 0xfe, 0x0f, 0x00, 0x00, 0xff, 0xff, 0x4a, 0xdc, 0x6c, 0xe8,
	0x0f, 0x06, 0x00, 0x00,
}
