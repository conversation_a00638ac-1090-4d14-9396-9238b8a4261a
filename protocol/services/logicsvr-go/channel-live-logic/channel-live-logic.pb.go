// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/channel-live-logic/channel-live-logic.proto

package channel_live_logic // import "golang.52tt.com/protocol/services/logicsvr-go/channel-live-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel_live_logic "golang.52tt.com/protocol/app/channel-live-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelLiveLogicClient is the client API for ChannelLiveLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelLiveLogicClient interface {
	GetLiveChannelInfo(ctx context.Context, in *channel_live_logic.GetLiveChannelInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetLiveChannelInfoResp, error)
	ChannelLiveHeartbeat(ctx context.Context, in *channel_live_logic.ChannelLiveHeartbeatReq, opts ...grpc.CallOption) (*channel_live_logic.ChannelLiveHeartbeatResp, error)
	SetChannelLiveStatus(ctx context.Context, in *channel_live_logic.SetChannelLiveStatusReq, opts ...grpc.CallOption) (*channel_live_logic.SetChannelLiveStatusResp, error)
	GetChannelLiveStatus(ctx context.Context, in *channel_live_logic.GetChannelLiveStatusReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveStatusResp, error)
	ApplyPk(ctx context.Context, in *channel_live_logic.ApplyPkReq, opts ...grpc.CallOption) (*channel_live_logic.ApplyPkResp, error)
	HandlerApply(ctx context.Context, in *channel_live_logic.HandlerApplyReq, opts ...grpc.CallOption) (*channel_live_logic.HandlerApplyResp, error)
	BatchGetChannelLiveStatusByAccount(ctx context.Context, in *channel_live_logic.BatchGetChannelLiveStatusByAccountReq, opts ...grpc.CallOption) (*channel_live_logic.BatchGetChannelLiveStatusByAccountResp, error)
	SetPkStatus(ctx context.Context, in *channel_live_logic.SetPkStatusReq, opts ...grpc.CallOption) (*channel_live_logic.SetPkStatusResp, error)
	CancelPKApply(ctx context.Context, in *channel_live_logic.CancelPKApplyReq, opts ...grpc.CallOption) (*channel_live_logic.CancelPKApplyResp, error)
	GetChannelLivePKRecord(ctx context.Context, in *channel_live_logic.GetChannelLivePKRecordReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLivePKRecordResp, error)
	GetChannelLivePkRankUser(ctx context.Context, in *channel_live_logic.GetChannelLivePkRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLivePkRankUserResp, error)
	GetChannelLiveRankUser(ctx context.Context, in *channel_live_logic.GetChannelLiveRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveRankUserResp, error)
	GetChannelLiveWatchTimeRankUser(ctx context.Context, in *channel_live_logic.GetChannelLiveWatchTimeRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveWatchTimeRankUserResp, error)
	GetChannelLiveData(ctx context.Context, in *channel_live_logic.GetChannelLiveDataReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveDataResp, error)
	GetFansRankList(ctx context.Context, in *channel_live_logic.GetFansRankListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansRankListResp, error)
	GetFansInfo(ctx context.Context, in *channel_live_logic.GetFansInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansInfoResp, error)
	GetAnchorFansInfo(ctx context.Context, in *channel_live_logic.GetAnchorFansInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorFansInfoResp, error)
	SearchAnchor(ctx context.Context, in *channel_live_logic.SearchAnchorReq, opts ...grpc.CallOption) (*channel_live_logic.SearchAnchorResp, error)
	ReportClientIDChange(ctx context.Context, in *channel_live_logic.ReportClientIDChangeReq, opts ...grpc.CallOption) (*channel_live_logic.ReportClientIDChangeResp, error)
	GetApplyList(ctx context.Context, in *channel_live_logic.GetApplyListReq, opts ...grpc.CallOption) (*channel_live_logic.GetApplyListResp, error)
	GetPkInfo(ctx context.Context, in *channel_live_logic.GetPkInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetPkInfoResp, error)
	GetMyToolList(ctx context.Context, in *channel_live_logic.GetMyToolListReq, opts ...grpc.CallOption) (*channel_live_logic.GetMyToolListResp, error)
	GetItemConfig(ctx context.Context, in *channel_live_logic.GetItemConfigReq, opts ...grpc.CallOption) (*channel_live_logic.GetItemConfigResp, error)
	SetChannelLiveOpponentMicFlag(ctx context.Context, in *channel_live_logic.SetChannelLiveOpponentMicFlagReq, opts ...grpc.CallOption) (*channel_live_logic.SetChannelLiveOpponentMicFlagResp, error)
	StartPkMatch(ctx context.Context, in *channel_live_logic.StartPkMatchReq, opts ...grpc.CallOption) (*channel_live_logic.StartPkMatchResp, error)
	CancelPkMatch(ctx context.Context, in *channel_live_logic.CancelPkMatchReq, opts ...grpc.CallOption) (*channel_live_logic.CancelPkMatchResp, error)
	GetPKMatchInfo(ctx context.Context, in *channel_live_logic.GetPKMatchInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetPKMatchInfoResp, error)
	GetUserMissionList(ctx context.Context, in *channel_live_logic.GetUserMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetUserMissionListResp, error)
	GetFansMissionList(ctx context.Context, in *channel_live_logic.GetFansMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansMissionListResp, error)
	GetActorMissionList(ctx context.Context, in *channel_live_logic.GetActorMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetActorMissionListResp, error)
	HandleUserMissionAtInterval(ctx context.Context, in *channel_live_logic.HandleUserMissionAtIntervalReq, opts ...grpc.CallOption) (*channel_live_logic.HandleUserMissionAtIntervalResp, error)
	HandleShareLiveChannelMission(ctx context.Context, in *channel_live_logic.HandleShareLiveChannelMissionReq, opts ...grpc.CallOption) (*channel_live_logic.HandleShareLiveChannelMissionResp, error)
	GetProcessActorMissionDesc(ctx context.Context, in *channel_live_logic.GetProcessActorMissionDescReq, opts ...grpc.CallOption) (*channel_live_logic.GetProcessActorMissionDescResp, error)
	HandleFansMissionAtInterval(ctx context.Context, in *channel_live_logic.HandleFansMissionAtIntervalReq, opts ...grpc.CallOption) (*channel_live_logic.HandleFansMissionAtIntervalResp, error)
	GetAnchorHonorNameplate(ctx context.Context, in *channel_live_logic.GetAnchorHonorNameplateReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorHonorNameplateResp, error)
	GetRankingList(ctx context.Context, in *channel_live_logic.GetRankingListReq, opts ...grpc.CallOption) (*channel_live_logic.GetRankingListResp, error)
	GetFansAddedGroupList(ctx context.Context, in *channel_live_logic.GetFansAddedGroupListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansAddedGroupListResp, error)
	SetFansGroupName(ctx context.Context, in *channel_live_logic.SetFansGroupNameReq, opts ...grpc.CallOption) (*channel_live_logic.SetFansGroupNameResp, error)
	CheckSetGroupNamePermit(ctx context.Context, in *channel_live_logic.CheckSetGroupNamePermitReq, opts ...grpc.CallOption) (*channel_live_logic.CheckSetGroupNamePermitResp, error)
	CheckUserIsFans(ctx context.Context, in *channel_live_logic.CheckUserIsFansReq, opts ...grpc.CallOption) (*channel_live_logic.CheckUserIsFansResp, error)
	ChannelLiveReport(ctx context.Context, in *channel_live_logic.ChannelLiveReportReq, opts ...grpc.CallOption) (*channel_live_logic.ChannelLiveReportResp, error)
	AcceptAppointPk(ctx context.Context, in *channel_live_logic.AcceptAppointPkReq, opts ...grpc.CallOption) (*channel_live_logic.AcceptAppointPkResp, error)
	ConfirmAppointPkPush(ctx context.Context, in *channel_live_logic.ConfirmAppointPkPushReq, opts ...grpc.CallOption) (*channel_live_logic.ConfirmAppointPkPushResp, error)
	GetAppointPkInfo(ctx context.Context, in *channel_live_logic.GetAppointPkInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetAppointPkInfoResp, error)
	GetAnchorValidPlateList(ctx context.Context, in *channel_live_logic.GetAnchorValidPlateListReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorValidPlateListResp, error)
	WearAnchorPlate(ctx context.Context, in *channel_live_logic.WearAnchorPlateReq, opts ...grpc.CallOption) (*channel_live_logic.WearAnchorPlateResp, error)
	// 直播多人PK
	GetChannelLiveMultiPkPermission(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkPermissionRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkPermissionResponse, error)
	SerarchPkAnchor(ctx context.Context, in *channel_live_logic.SerarchMultiPkAnchorRequest, opts ...grpc.CallOption) (*channel_live_logic.SerarchMultiPkAnchorResponse, error)
	ApplyChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.ApplyChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.ApplyChannelLiveMultiPkResponse, error)
	MatchMultiPk(ctx context.Context, in *channel_live_logic.MatchMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.MatchMultiPkResponse, error)
	CancelMatchMultiPk(ctx context.Context, in *channel_live_logic.CancelMatchMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.CancelMatchMultiPkResponse, error)
	AcceptChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.AcceptChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.AcceptChannelLiveMultiPkResponse, error)
	StartChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.StartChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.StartChannelLiveMultiPkResponse, error)
	GetChannelLiveMultiPkRank(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkRankRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkRankResponse, error)
	GetChannelLiveMultiPkKnightList(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkKnightListRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkKnightListResponse, error)
	GetChannelLiveMultiPkRecordList(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkRecordListRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkRecordListResponse, error)
	CancelChannelLiveMultiPkTeam(ctx context.Context, in *channel_live_logic.CancelChannelLiveMultiPkTeamRequest, opts ...grpc.CallOption) (*channel_live_logic.CancelChannelLiveMultiPkTeamResponse, error)
	StopChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.StopChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.StopChannelLiveMultiPkResponse, error)
	DisinviteChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.DisinviteChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.DisinviteChannelLiveMultiPkResponse, error)
	GetChannelLiveMultiPkTeamInfo(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkTeamInfoResponse, error)
	InitChannelLiveMultiPkTeam(ctx context.Context, in *channel_live_logic.InitChannelLiveMultiPkTeamRequest, opts ...grpc.CallOption) (*channel_live_logic.InitChannelLiveMultiPkTeamResponse, error)
	LeaveFansGroup(ctx context.Context, in *channel_live_logic.LeaveFansGroupRequest, opts ...grpc.CallOption) (*channel_live_logic.LeaveFansGroupResponse, error)
	GetVirtualLiveChannelSecret(ctx context.Context, in *channel_live_logic.GetVirtualLiveChannelSecretRequest, opts ...grpc.CallOption) (*channel_live_logic.GetVirtualLiveChannelSecretResponse, error)
	GetUserFansGiftPri(ctx context.Context, in *channel_live_logic.GetUserFansGiftPriRequest, opts ...grpc.CallOption) (*channel_live_logic.GetUserFansGiftPriResponse, error)
}

type channelLiveLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelLiveLogicClient(cc *grpc.ClientConn) ChannelLiveLogicClient {
	return &channelLiveLogicClient{cc}
}

func (c *channelLiveLogicClient) GetLiveChannelInfo(ctx context.Context, in *channel_live_logic.GetLiveChannelInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetLiveChannelInfoResp, error) {
	out := new(channel_live_logic.GetLiveChannelInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetLiveChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ChannelLiveHeartbeat(ctx context.Context, in *channel_live_logic.ChannelLiveHeartbeatReq, opts ...grpc.CallOption) (*channel_live_logic.ChannelLiveHeartbeatResp, error) {
	out := new(channel_live_logic.ChannelLiveHeartbeatResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/ChannelLiveHeartbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SetChannelLiveStatus(ctx context.Context, in *channel_live_logic.SetChannelLiveStatusReq, opts ...grpc.CallOption) (*channel_live_logic.SetChannelLiveStatusResp, error) {
	out := new(channel_live_logic.SetChannelLiveStatusResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/SetChannelLiveStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveStatus(ctx context.Context, in *channel_live_logic.GetChannelLiveStatusReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveStatusResp, error) {
	out := new(channel_live_logic.GetChannelLiveStatusResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLiveStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ApplyPk(ctx context.Context, in *channel_live_logic.ApplyPkReq, opts ...grpc.CallOption) (*channel_live_logic.ApplyPkResp, error) {
	out := new(channel_live_logic.ApplyPkResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/ApplyPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) HandlerApply(ctx context.Context, in *channel_live_logic.HandlerApplyReq, opts ...grpc.CallOption) (*channel_live_logic.HandlerApplyResp, error) {
	out := new(channel_live_logic.HandlerApplyResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/HandlerApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) BatchGetChannelLiveStatusByAccount(ctx context.Context, in *channel_live_logic.BatchGetChannelLiveStatusByAccountReq, opts ...grpc.CallOption) (*channel_live_logic.BatchGetChannelLiveStatusByAccountResp, error) {
	out := new(channel_live_logic.BatchGetChannelLiveStatusByAccountResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/BatchGetChannelLiveStatusByAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SetPkStatus(ctx context.Context, in *channel_live_logic.SetPkStatusReq, opts ...grpc.CallOption) (*channel_live_logic.SetPkStatusResp, error) {
	out := new(channel_live_logic.SetPkStatusResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/SetPkStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CancelPKApply(ctx context.Context, in *channel_live_logic.CancelPKApplyReq, opts ...grpc.CallOption) (*channel_live_logic.CancelPKApplyResp, error) {
	out := new(channel_live_logic.CancelPKApplyResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/CancelPKApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLivePKRecord(ctx context.Context, in *channel_live_logic.GetChannelLivePKRecordReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLivePKRecordResp, error) {
	out := new(channel_live_logic.GetChannelLivePKRecordResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLivePKRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLivePkRankUser(ctx context.Context, in *channel_live_logic.GetChannelLivePkRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLivePkRankUserResp, error) {
	out := new(channel_live_logic.GetChannelLivePkRankUserResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLivePkRankUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveRankUser(ctx context.Context, in *channel_live_logic.GetChannelLiveRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveRankUserResp, error) {
	out := new(channel_live_logic.GetChannelLiveRankUserResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLiveRankUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveWatchTimeRankUser(ctx context.Context, in *channel_live_logic.GetChannelLiveWatchTimeRankUserReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveWatchTimeRankUserResp, error) {
	out := new(channel_live_logic.GetChannelLiveWatchTimeRankUserResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLiveWatchTimeRankUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveData(ctx context.Context, in *channel_live_logic.GetChannelLiveDataReq, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveDataResp, error) {
	out := new(channel_live_logic.GetChannelLiveDataResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLiveData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetFansRankList(ctx context.Context, in *channel_live_logic.GetFansRankListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansRankListResp, error) {
	out := new(channel_live_logic.GetFansRankListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetFansRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetFansInfo(ctx context.Context, in *channel_live_logic.GetFansInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansInfoResp, error) {
	out := new(channel_live_logic.GetFansInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetFansInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetAnchorFansInfo(ctx context.Context, in *channel_live_logic.GetAnchorFansInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorFansInfoResp, error) {
	out := new(channel_live_logic.GetAnchorFansInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetAnchorFansInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SearchAnchor(ctx context.Context, in *channel_live_logic.SearchAnchorReq, opts ...grpc.CallOption) (*channel_live_logic.SearchAnchorResp, error) {
	out := new(channel_live_logic.SearchAnchorResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/SearchAnchor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ReportClientIDChange(ctx context.Context, in *channel_live_logic.ReportClientIDChangeReq, opts ...grpc.CallOption) (*channel_live_logic.ReportClientIDChangeResp, error) {
	out := new(channel_live_logic.ReportClientIDChangeResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/ReportClientIDChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetApplyList(ctx context.Context, in *channel_live_logic.GetApplyListReq, opts ...grpc.CallOption) (*channel_live_logic.GetApplyListResp, error) {
	out := new(channel_live_logic.GetApplyListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetApplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetPkInfo(ctx context.Context, in *channel_live_logic.GetPkInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetPkInfoResp, error) {
	out := new(channel_live_logic.GetPkInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetMyToolList(ctx context.Context, in *channel_live_logic.GetMyToolListReq, opts ...grpc.CallOption) (*channel_live_logic.GetMyToolListResp, error) {
	out := new(channel_live_logic.GetMyToolListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetMyToolList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetItemConfig(ctx context.Context, in *channel_live_logic.GetItemConfigReq, opts ...grpc.CallOption) (*channel_live_logic.GetItemConfigResp, error) {
	out := new(channel_live_logic.GetItemConfigResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetItemConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SetChannelLiveOpponentMicFlag(ctx context.Context, in *channel_live_logic.SetChannelLiveOpponentMicFlagReq, opts ...grpc.CallOption) (*channel_live_logic.SetChannelLiveOpponentMicFlagResp, error) {
	out := new(channel_live_logic.SetChannelLiveOpponentMicFlagResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/SetChannelLiveOpponentMicFlag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) StartPkMatch(ctx context.Context, in *channel_live_logic.StartPkMatchReq, opts ...grpc.CallOption) (*channel_live_logic.StartPkMatchResp, error) {
	out := new(channel_live_logic.StartPkMatchResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/StartPkMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CancelPkMatch(ctx context.Context, in *channel_live_logic.CancelPkMatchReq, opts ...grpc.CallOption) (*channel_live_logic.CancelPkMatchResp, error) {
	out := new(channel_live_logic.CancelPkMatchResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/CancelPkMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetPKMatchInfo(ctx context.Context, in *channel_live_logic.GetPKMatchInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetPKMatchInfoResp, error) {
	out := new(channel_live_logic.GetPKMatchInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetPKMatchInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetUserMissionList(ctx context.Context, in *channel_live_logic.GetUserMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetUserMissionListResp, error) {
	out := new(channel_live_logic.GetUserMissionListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetUserMissionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetFansMissionList(ctx context.Context, in *channel_live_logic.GetFansMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansMissionListResp, error) {
	out := new(channel_live_logic.GetFansMissionListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetFansMissionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetActorMissionList(ctx context.Context, in *channel_live_logic.GetActorMissionListReq, opts ...grpc.CallOption) (*channel_live_logic.GetActorMissionListResp, error) {
	out := new(channel_live_logic.GetActorMissionListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetActorMissionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) HandleUserMissionAtInterval(ctx context.Context, in *channel_live_logic.HandleUserMissionAtIntervalReq, opts ...grpc.CallOption) (*channel_live_logic.HandleUserMissionAtIntervalResp, error) {
	out := new(channel_live_logic.HandleUserMissionAtIntervalResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/HandleUserMissionAtInterval", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) HandleShareLiveChannelMission(ctx context.Context, in *channel_live_logic.HandleShareLiveChannelMissionReq, opts ...grpc.CallOption) (*channel_live_logic.HandleShareLiveChannelMissionResp, error) {
	out := new(channel_live_logic.HandleShareLiveChannelMissionResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/HandleShareLiveChannelMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetProcessActorMissionDesc(ctx context.Context, in *channel_live_logic.GetProcessActorMissionDescReq, opts ...grpc.CallOption) (*channel_live_logic.GetProcessActorMissionDescResp, error) {
	out := new(channel_live_logic.GetProcessActorMissionDescResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetProcessActorMissionDesc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) HandleFansMissionAtInterval(ctx context.Context, in *channel_live_logic.HandleFansMissionAtIntervalReq, opts ...grpc.CallOption) (*channel_live_logic.HandleFansMissionAtIntervalResp, error) {
	out := new(channel_live_logic.HandleFansMissionAtIntervalResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/HandleFansMissionAtInterval", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetAnchorHonorNameplate(ctx context.Context, in *channel_live_logic.GetAnchorHonorNameplateReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorHonorNameplateResp, error) {
	out := new(channel_live_logic.GetAnchorHonorNameplateResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetAnchorHonorNameplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetRankingList(ctx context.Context, in *channel_live_logic.GetRankingListReq, opts ...grpc.CallOption) (*channel_live_logic.GetRankingListResp, error) {
	out := new(channel_live_logic.GetRankingListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetRankingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetFansAddedGroupList(ctx context.Context, in *channel_live_logic.GetFansAddedGroupListReq, opts ...grpc.CallOption) (*channel_live_logic.GetFansAddedGroupListResp, error) {
	out := new(channel_live_logic.GetFansAddedGroupListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetFansAddedGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SetFansGroupName(ctx context.Context, in *channel_live_logic.SetFansGroupNameReq, opts ...grpc.CallOption) (*channel_live_logic.SetFansGroupNameResp, error) {
	out := new(channel_live_logic.SetFansGroupNameResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/SetFansGroupName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CheckSetGroupNamePermit(ctx context.Context, in *channel_live_logic.CheckSetGroupNamePermitReq, opts ...grpc.CallOption) (*channel_live_logic.CheckSetGroupNamePermitResp, error) {
	out := new(channel_live_logic.CheckSetGroupNamePermitResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/CheckSetGroupNamePermit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CheckUserIsFans(ctx context.Context, in *channel_live_logic.CheckUserIsFansReq, opts ...grpc.CallOption) (*channel_live_logic.CheckUserIsFansResp, error) {
	out := new(channel_live_logic.CheckUserIsFansResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/CheckUserIsFans", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ChannelLiveReport(ctx context.Context, in *channel_live_logic.ChannelLiveReportReq, opts ...grpc.CallOption) (*channel_live_logic.ChannelLiveReportResp, error) {
	out := new(channel_live_logic.ChannelLiveReportResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/ChannelLiveReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) AcceptAppointPk(ctx context.Context, in *channel_live_logic.AcceptAppointPkReq, opts ...grpc.CallOption) (*channel_live_logic.AcceptAppointPkResp, error) {
	out := new(channel_live_logic.AcceptAppointPkResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/AcceptAppointPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ConfirmAppointPkPush(ctx context.Context, in *channel_live_logic.ConfirmAppointPkPushReq, opts ...grpc.CallOption) (*channel_live_logic.ConfirmAppointPkPushResp, error) {
	out := new(channel_live_logic.ConfirmAppointPkPushResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/ConfirmAppointPkPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetAppointPkInfo(ctx context.Context, in *channel_live_logic.GetAppointPkInfoReq, opts ...grpc.CallOption) (*channel_live_logic.GetAppointPkInfoResp, error) {
	out := new(channel_live_logic.GetAppointPkInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetAppointPkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetAnchorValidPlateList(ctx context.Context, in *channel_live_logic.GetAnchorValidPlateListReq, opts ...grpc.CallOption) (*channel_live_logic.GetAnchorValidPlateListResp, error) {
	out := new(channel_live_logic.GetAnchorValidPlateListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetAnchorValidPlateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) WearAnchorPlate(ctx context.Context, in *channel_live_logic.WearAnchorPlateReq, opts ...grpc.CallOption) (*channel_live_logic.WearAnchorPlateResp, error) {
	out := new(channel_live_logic.WearAnchorPlateResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/WearAnchorPlate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveMultiPkPermission(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkPermissionRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkPermissionResponse, error) {
	out := new(channel_live_logic.GetChannelLiveMultiPkPermissionResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLiveMultiPkPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) SerarchPkAnchor(ctx context.Context, in *channel_live_logic.SerarchMultiPkAnchorRequest, opts ...grpc.CallOption) (*channel_live_logic.SerarchMultiPkAnchorResponse, error) {
	out := new(channel_live_logic.SerarchMultiPkAnchorResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/SerarchPkAnchor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) ApplyChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.ApplyChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.ApplyChannelLiveMultiPkResponse, error) {
	out := new(channel_live_logic.ApplyChannelLiveMultiPkResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/ApplyChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) MatchMultiPk(ctx context.Context, in *channel_live_logic.MatchMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.MatchMultiPkResponse, error) {
	out := new(channel_live_logic.MatchMultiPkResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/MatchMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CancelMatchMultiPk(ctx context.Context, in *channel_live_logic.CancelMatchMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.CancelMatchMultiPkResponse, error) {
	out := new(channel_live_logic.CancelMatchMultiPkResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/CancelMatchMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) AcceptChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.AcceptChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.AcceptChannelLiveMultiPkResponse, error) {
	out := new(channel_live_logic.AcceptChannelLiveMultiPkResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/AcceptChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) StartChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.StartChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.StartChannelLiveMultiPkResponse, error) {
	out := new(channel_live_logic.StartChannelLiveMultiPkResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/StartChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveMultiPkRank(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkRankRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkRankResponse, error) {
	out := new(channel_live_logic.GetChannelLiveMultiPkRankResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLiveMultiPkRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveMultiPkKnightList(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkKnightListRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkKnightListResponse, error) {
	out := new(channel_live_logic.GetChannelLiveMultiPkKnightListResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLiveMultiPkKnightList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveMultiPkRecordList(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkRecordListRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkRecordListResponse, error) {
	out := new(channel_live_logic.GetChannelLiveMultiPkRecordListResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLiveMultiPkRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) CancelChannelLiveMultiPkTeam(ctx context.Context, in *channel_live_logic.CancelChannelLiveMultiPkTeamRequest, opts ...grpc.CallOption) (*channel_live_logic.CancelChannelLiveMultiPkTeamResponse, error) {
	out := new(channel_live_logic.CancelChannelLiveMultiPkTeamResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/CancelChannelLiveMultiPkTeam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) StopChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.StopChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.StopChannelLiveMultiPkResponse, error) {
	out := new(channel_live_logic.StopChannelLiveMultiPkResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/StopChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) DisinviteChannelLiveMultiPk(ctx context.Context, in *channel_live_logic.DisinviteChannelLiveMultiPkRequest, opts ...grpc.CallOption) (*channel_live_logic.DisinviteChannelLiveMultiPkResponse, error) {
	out := new(channel_live_logic.DisinviteChannelLiveMultiPkResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/DisinviteChannelLiveMultiPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetChannelLiveMultiPkTeamInfo(ctx context.Context, in *channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest, opts ...grpc.CallOption) (*channel_live_logic.GetChannelLiveMultiPkTeamInfoResponse, error) {
	out := new(channel_live_logic.GetChannelLiveMultiPkTeamInfoResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetChannelLiveMultiPkTeamInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) InitChannelLiveMultiPkTeam(ctx context.Context, in *channel_live_logic.InitChannelLiveMultiPkTeamRequest, opts ...grpc.CallOption) (*channel_live_logic.InitChannelLiveMultiPkTeamResponse, error) {
	out := new(channel_live_logic.InitChannelLiveMultiPkTeamResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/InitChannelLiveMultiPkTeam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) LeaveFansGroup(ctx context.Context, in *channel_live_logic.LeaveFansGroupRequest, opts ...grpc.CallOption) (*channel_live_logic.LeaveFansGroupResponse, error) {
	out := new(channel_live_logic.LeaveFansGroupResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/LeaveFansGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetVirtualLiveChannelSecret(ctx context.Context, in *channel_live_logic.GetVirtualLiveChannelSecretRequest, opts ...grpc.CallOption) (*channel_live_logic.GetVirtualLiveChannelSecretResponse, error) {
	out := new(channel_live_logic.GetVirtualLiveChannelSecretResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetVirtualLiveChannelSecret", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveLogicClient) GetUserFansGiftPri(ctx context.Context, in *channel_live_logic.GetUserFansGiftPriRequest, opts ...grpc.CallOption) (*channel_live_logic.GetUserFansGiftPriResponse, error) {
	out := new(channel_live_logic.GetUserFansGiftPriResponse)
	err := c.cc.Invoke(ctx, "/logic.ChannelLiveLogic/GetUserFansGiftPri", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelLiveLogicServer is the server API for ChannelLiveLogic service.
type ChannelLiveLogicServer interface {
	GetLiveChannelInfo(context.Context, *channel_live_logic.GetLiveChannelInfoReq) (*channel_live_logic.GetLiveChannelInfoResp, error)
	ChannelLiveHeartbeat(context.Context, *channel_live_logic.ChannelLiveHeartbeatReq) (*channel_live_logic.ChannelLiveHeartbeatResp, error)
	SetChannelLiveStatus(context.Context, *channel_live_logic.SetChannelLiveStatusReq) (*channel_live_logic.SetChannelLiveStatusResp, error)
	GetChannelLiveStatus(context.Context, *channel_live_logic.GetChannelLiveStatusReq) (*channel_live_logic.GetChannelLiveStatusResp, error)
	ApplyPk(context.Context, *channel_live_logic.ApplyPkReq) (*channel_live_logic.ApplyPkResp, error)
	HandlerApply(context.Context, *channel_live_logic.HandlerApplyReq) (*channel_live_logic.HandlerApplyResp, error)
	BatchGetChannelLiveStatusByAccount(context.Context, *channel_live_logic.BatchGetChannelLiveStatusByAccountReq) (*channel_live_logic.BatchGetChannelLiveStatusByAccountResp, error)
	SetPkStatus(context.Context, *channel_live_logic.SetPkStatusReq) (*channel_live_logic.SetPkStatusResp, error)
	CancelPKApply(context.Context, *channel_live_logic.CancelPKApplyReq) (*channel_live_logic.CancelPKApplyResp, error)
	GetChannelLivePKRecord(context.Context, *channel_live_logic.GetChannelLivePKRecordReq) (*channel_live_logic.GetChannelLivePKRecordResp, error)
	GetChannelLivePkRankUser(context.Context, *channel_live_logic.GetChannelLivePkRankUserReq) (*channel_live_logic.GetChannelLivePkRankUserResp, error)
	GetChannelLiveRankUser(context.Context, *channel_live_logic.GetChannelLiveRankUserReq) (*channel_live_logic.GetChannelLiveRankUserResp, error)
	GetChannelLiveWatchTimeRankUser(context.Context, *channel_live_logic.GetChannelLiveWatchTimeRankUserReq) (*channel_live_logic.GetChannelLiveWatchTimeRankUserResp, error)
	GetChannelLiveData(context.Context, *channel_live_logic.GetChannelLiveDataReq) (*channel_live_logic.GetChannelLiveDataResp, error)
	GetFansRankList(context.Context, *channel_live_logic.GetFansRankListReq) (*channel_live_logic.GetFansRankListResp, error)
	GetFansInfo(context.Context, *channel_live_logic.GetFansInfoReq) (*channel_live_logic.GetFansInfoResp, error)
	GetAnchorFansInfo(context.Context, *channel_live_logic.GetAnchorFansInfoReq) (*channel_live_logic.GetAnchorFansInfoResp, error)
	SearchAnchor(context.Context, *channel_live_logic.SearchAnchorReq) (*channel_live_logic.SearchAnchorResp, error)
	ReportClientIDChange(context.Context, *channel_live_logic.ReportClientIDChangeReq) (*channel_live_logic.ReportClientIDChangeResp, error)
	GetApplyList(context.Context, *channel_live_logic.GetApplyListReq) (*channel_live_logic.GetApplyListResp, error)
	GetPkInfo(context.Context, *channel_live_logic.GetPkInfoReq) (*channel_live_logic.GetPkInfoResp, error)
	GetMyToolList(context.Context, *channel_live_logic.GetMyToolListReq) (*channel_live_logic.GetMyToolListResp, error)
	GetItemConfig(context.Context, *channel_live_logic.GetItemConfigReq) (*channel_live_logic.GetItemConfigResp, error)
	SetChannelLiveOpponentMicFlag(context.Context, *channel_live_logic.SetChannelLiveOpponentMicFlagReq) (*channel_live_logic.SetChannelLiveOpponentMicFlagResp, error)
	StartPkMatch(context.Context, *channel_live_logic.StartPkMatchReq) (*channel_live_logic.StartPkMatchResp, error)
	CancelPkMatch(context.Context, *channel_live_logic.CancelPkMatchReq) (*channel_live_logic.CancelPkMatchResp, error)
	GetPKMatchInfo(context.Context, *channel_live_logic.GetPKMatchInfoReq) (*channel_live_logic.GetPKMatchInfoResp, error)
	GetUserMissionList(context.Context, *channel_live_logic.GetUserMissionListReq) (*channel_live_logic.GetUserMissionListResp, error)
	GetFansMissionList(context.Context, *channel_live_logic.GetFansMissionListReq) (*channel_live_logic.GetFansMissionListResp, error)
	GetActorMissionList(context.Context, *channel_live_logic.GetActorMissionListReq) (*channel_live_logic.GetActorMissionListResp, error)
	HandleUserMissionAtInterval(context.Context, *channel_live_logic.HandleUserMissionAtIntervalReq) (*channel_live_logic.HandleUserMissionAtIntervalResp, error)
	HandleShareLiveChannelMission(context.Context, *channel_live_logic.HandleShareLiveChannelMissionReq) (*channel_live_logic.HandleShareLiveChannelMissionResp, error)
	GetProcessActorMissionDesc(context.Context, *channel_live_logic.GetProcessActorMissionDescReq) (*channel_live_logic.GetProcessActorMissionDescResp, error)
	HandleFansMissionAtInterval(context.Context, *channel_live_logic.HandleFansMissionAtIntervalReq) (*channel_live_logic.HandleFansMissionAtIntervalResp, error)
	GetAnchorHonorNameplate(context.Context, *channel_live_logic.GetAnchorHonorNameplateReq) (*channel_live_logic.GetAnchorHonorNameplateResp, error)
	GetRankingList(context.Context, *channel_live_logic.GetRankingListReq) (*channel_live_logic.GetRankingListResp, error)
	GetFansAddedGroupList(context.Context, *channel_live_logic.GetFansAddedGroupListReq) (*channel_live_logic.GetFansAddedGroupListResp, error)
	SetFansGroupName(context.Context, *channel_live_logic.SetFansGroupNameReq) (*channel_live_logic.SetFansGroupNameResp, error)
	CheckSetGroupNamePermit(context.Context, *channel_live_logic.CheckSetGroupNamePermitReq) (*channel_live_logic.CheckSetGroupNamePermitResp, error)
	CheckUserIsFans(context.Context, *channel_live_logic.CheckUserIsFansReq) (*channel_live_logic.CheckUserIsFansResp, error)
	ChannelLiveReport(context.Context, *channel_live_logic.ChannelLiveReportReq) (*channel_live_logic.ChannelLiveReportResp, error)
	AcceptAppointPk(context.Context, *channel_live_logic.AcceptAppointPkReq) (*channel_live_logic.AcceptAppointPkResp, error)
	ConfirmAppointPkPush(context.Context, *channel_live_logic.ConfirmAppointPkPushReq) (*channel_live_logic.ConfirmAppointPkPushResp, error)
	GetAppointPkInfo(context.Context, *channel_live_logic.GetAppointPkInfoReq) (*channel_live_logic.GetAppointPkInfoResp, error)
	GetAnchorValidPlateList(context.Context, *channel_live_logic.GetAnchorValidPlateListReq) (*channel_live_logic.GetAnchorValidPlateListResp, error)
	WearAnchorPlate(context.Context, *channel_live_logic.WearAnchorPlateReq) (*channel_live_logic.WearAnchorPlateResp, error)
	// 直播多人PK
	GetChannelLiveMultiPkPermission(context.Context, *channel_live_logic.GetChannelLiveMultiPkPermissionRequest) (*channel_live_logic.GetChannelLiveMultiPkPermissionResponse, error)
	SerarchPkAnchor(context.Context, *channel_live_logic.SerarchMultiPkAnchorRequest) (*channel_live_logic.SerarchMultiPkAnchorResponse, error)
	ApplyChannelLiveMultiPk(context.Context, *channel_live_logic.ApplyChannelLiveMultiPkRequest) (*channel_live_logic.ApplyChannelLiveMultiPkResponse, error)
	MatchMultiPk(context.Context, *channel_live_logic.MatchMultiPkRequest) (*channel_live_logic.MatchMultiPkResponse, error)
	CancelMatchMultiPk(context.Context, *channel_live_logic.CancelMatchMultiPkRequest) (*channel_live_logic.CancelMatchMultiPkResponse, error)
	AcceptChannelLiveMultiPk(context.Context, *channel_live_logic.AcceptChannelLiveMultiPkRequest) (*channel_live_logic.AcceptChannelLiveMultiPkResponse, error)
	StartChannelLiveMultiPk(context.Context, *channel_live_logic.StartChannelLiveMultiPkRequest) (*channel_live_logic.StartChannelLiveMultiPkResponse, error)
	GetChannelLiveMultiPkRank(context.Context, *channel_live_logic.GetChannelLiveMultiPkRankRequest) (*channel_live_logic.GetChannelLiveMultiPkRankResponse, error)
	GetChannelLiveMultiPkKnightList(context.Context, *channel_live_logic.GetChannelLiveMultiPkKnightListRequest) (*channel_live_logic.GetChannelLiveMultiPkKnightListResponse, error)
	GetChannelLiveMultiPkRecordList(context.Context, *channel_live_logic.GetChannelLiveMultiPkRecordListRequest) (*channel_live_logic.GetChannelLiveMultiPkRecordListResponse, error)
	CancelChannelLiveMultiPkTeam(context.Context, *channel_live_logic.CancelChannelLiveMultiPkTeamRequest) (*channel_live_logic.CancelChannelLiveMultiPkTeamResponse, error)
	StopChannelLiveMultiPk(context.Context, *channel_live_logic.StopChannelLiveMultiPkRequest) (*channel_live_logic.StopChannelLiveMultiPkResponse, error)
	DisinviteChannelLiveMultiPk(context.Context, *channel_live_logic.DisinviteChannelLiveMultiPkRequest) (*channel_live_logic.DisinviteChannelLiveMultiPkResponse, error)
	GetChannelLiveMultiPkTeamInfo(context.Context, *channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest) (*channel_live_logic.GetChannelLiveMultiPkTeamInfoResponse, error)
	InitChannelLiveMultiPkTeam(context.Context, *channel_live_logic.InitChannelLiveMultiPkTeamRequest) (*channel_live_logic.InitChannelLiveMultiPkTeamResponse, error)
	LeaveFansGroup(context.Context, *channel_live_logic.LeaveFansGroupRequest) (*channel_live_logic.LeaveFansGroupResponse, error)
	GetVirtualLiveChannelSecret(context.Context, *channel_live_logic.GetVirtualLiveChannelSecretRequest) (*channel_live_logic.GetVirtualLiveChannelSecretResponse, error)
	GetUserFansGiftPri(context.Context, *channel_live_logic.GetUserFansGiftPriRequest) (*channel_live_logic.GetUserFansGiftPriResponse, error)
}

func RegisterChannelLiveLogicServer(s *grpc.Server, srv ChannelLiveLogicServer) {
	s.RegisterService(&_ChannelLiveLogic_serviceDesc, srv)
}

func _ChannelLiveLogic_GetLiveChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetLiveChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetLiveChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetLiveChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetLiveChannelInfo(ctx, req.(*channel_live_logic.GetLiveChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ChannelLiveHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ChannelLiveHeartbeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ChannelLiveHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/ChannelLiveHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ChannelLiveHeartbeat(ctx, req.(*channel_live_logic.ChannelLiveHeartbeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SetChannelLiveStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SetChannelLiveStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SetChannelLiveStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/SetChannelLiveStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SetChannelLiveStatus(ctx, req.(*channel_live_logic.SetChannelLiveStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLiveStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveStatus(ctx, req.(*channel_live_logic.GetChannelLiveStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ApplyPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ApplyPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ApplyPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/ApplyPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ApplyPk(ctx, req.(*channel_live_logic.ApplyPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_HandlerApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.HandlerApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).HandlerApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/HandlerApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).HandlerApply(ctx, req.(*channel_live_logic.HandlerApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_BatchGetChannelLiveStatusByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.BatchGetChannelLiveStatusByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).BatchGetChannelLiveStatusByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/BatchGetChannelLiveStatusByAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).BatchGetChannelLiveStatusByAccount(ctx, req.(*channel_live_logic.BatchGetChannelLiveStatusByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SetPkStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SetPkStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SetPkStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/SetPkStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SetPkStatus(ctx, req.(*channel_live_logic.SetPkStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CancelPKApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CancelPKApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CancelPKApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/CancelPKApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CancelPKApply(ctx, req.(*channel_live_logic.CancelPKApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLivePKRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLivePKRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLivePKRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLivePKRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLivePKRecord(ctx, req.(*channel_live_logic.GetChannelLivePKRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLivePkRankUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLivePkRankUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLivePkRankUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLivePkRankUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLivePkRankUser(ctx, req.(*channel_live_logic.GetChannelLivePkRankUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveRankUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveRankUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveRankUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLiveRankUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveRankUser(ctx, req.(*channel_live_logic.GetChannelLiveRankUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveWatchTimeRankUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveWatchTimeRankUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveWatchTimeRankUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLiveWatchTimeRankUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveWatchTimeRankUser(ctx, req.(*channel_live_logic.GetChannelLiveWatchTimeRankUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLiveData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveData(ctx, req.(*channel_live_logic.GetChannelLiveDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetFansRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetFansRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetFansRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetFansRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetFansRankList(ctx, req.(*channel_live_logic.GetFansRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetFansInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetFansInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetFansInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetFansInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetFansInfo(ctx, req.(*channel_live_logic.GetFansInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetAnchorFansInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetAnchorFansInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetAnchorFansInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetAnchorFansInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetAnchorFansInfo(ctx, req.(*channel_live_logic.GetAnchorFansInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SearchAnchor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SearchAnchorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SearchAnchor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/SearchAnchor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SearchAnchor(ctx, req.(*channel_live_logic.SearchAnchorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ReportClientIDChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ReportClientIDChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ReportClientIDChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/ReportClientIDChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ReportClientIDChange(ctx, req.(*channel_live_logic.ReportClientIDChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetApplyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetApplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetApplyList(ctx, req.(*channel_live_logic.GetApplyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetPkInfo(ctx, req.(*channel_live_logic.GetPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetMyToolList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetMyToolListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetMyToolList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetMyToolList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetMyToolList(ctx, req.(*channel_live_logic.GetMyToolListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetItemConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetItemConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetItemConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetItemConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetItemConfig(ctx, req.(*channel_live_logic.GetItemConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SetChannelLiveOpponentMicFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SetChannelLiveOpponentMicFlagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SetChannelLiveOpponentMicFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/SetChannelLiveOpponentMicFlag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SetChannelLiveOpponentMicFlag(ctx, req.(*channel_live_logic.SetChannelLiveOpponentMicFlagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_StartPkMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.StartPkMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).StartPkMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/StartPkMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).StartPkMatch(ctx, req.(*channel_live_logic.StartPkMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CancelPkMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CancelPkMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CancelPkMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/CancelPkMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CancelPkMatch(ctx, req.(*channel_live_logic.CancelPkMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetPKMatchInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetPKMatchInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetPKMatchInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetPKMatchInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetPKMatchInfo(ctx, req.(*channel_live_logic.GetPKMatchInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetUserMissionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetUserMissionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetUserMissionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetUserMissionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetUserMissionList(ctx, req.(*channel_live_logic.GetUserMissionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetFansMissionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetFansMissionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetFansMissionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetFansMissionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetFansMissionList(ctx, req.(*channel_live_logic.GetFansMissionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetActorMissionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetActorMissionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetActorMissionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetActorMissionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetActorMissionList(ctx, req.(*channel_live_logic.GetActorMissionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_HandleUserMissionAtInterval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.HandleUserMissionAtIntervalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).HandleUserMissionAtInterval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/HandleUserMissionAtInterval",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).HandleUserMissionAtInterval(ctx, req.(*channel_live_logic.HandleUserMissionAtIntervalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_HandleShareLiveChannelMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.HandleShareLiveChannelMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).HandleShareLiveChannelMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/HandleShareLiveChannelMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).HandleShareLiveChannelMission(ctx, req.(*channel_live_logic.HandleShareLiveChannelMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetProcessActorMissionDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetProcessActorMissionDescReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetProcessActorMissionDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetProcessActorMissionDesc",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetProcessActorMissionDesc(ctx, req.(*channel_live_logic.GetProcessActorMissionDescReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_HandleFansMissionAtInterval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.HandleFansMissionAtIntervalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).HandleFansMissionAtInterval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/HandleFansMissionAtInterval",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).HandleFansMissionAtInterval(ctx, req.(*channel_live_logic.HandleFansMissionAtIntervalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetAnchorHonorNameplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetAnchorHonorNameplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetAnchorHonorNameplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetAnchorHonorNameplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetAnchorHonorNameplate(ctx, req.(*channel_live_logic.GetAnchorHonorNameplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetRankingList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetRankingListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetRankingList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetRankingList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetRankingList(ctx, req.(*channel_live_logic.GetRankingListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetFansAddedGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetFansAddedGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetFansAddedGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetFansAddedGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetFansAddedGroupList(ctx, req.(*channel_live_logic.GetFansAddedGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SetFansGroupName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SetFansGroupNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SetFansGroupName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/SetFansGroupName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SetFansGroupName(ctx, req.(*channel_live_logic.SetFansGroupNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CheckSetGroupNamePermit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CheckSetGroupNamePermitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CheckSetGroupNamePermit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/CheckSetGroupNamePermit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CheckSetGroupNamePermit(ctx, req.(*channel_live_logic.CheckSetGroupNamePermitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CheckUserIsFans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CheckUserIsFansReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CheckUserIsFans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/CheckUserIsFans",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CheckUserIsFans(ctx, req.(*channel_live_logic.CheckUserIsFansReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ChannelLiveReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ChannelLiveReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ChannelLiveReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/ChannelLiveReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ChannelLiveReport(ctx, req.(*channel_live_logic.ChannelLiveReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_AcceptAppointPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.AcceptAppointPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).AcceptAppointPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/AcceptAppointPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).AcceptAppointPk(ctx, req.(*channel_live_logic.AcceptAppointPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ConfirmAppointPkPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ConfirmAppointPkPushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ConfirmAppointPkPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/ConfirmAppointPkPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ConfirmAppointPkPush(ctx, req.(*channel_live_logic.ConfirmAppointPkPushReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetAppointPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetAppointPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetAppointPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetAppointPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetAppointPkInfo(ctx, req.(*channel_live_logic.GetAppointPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetAnchorValidPlateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetAnchorValidPlateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetAnchorValidPlateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetAnchorValidPlateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetAnchorValidPlateList(ctx, req.(*channel_live_logic.GetAnchorValidPlateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_WearAnchorPlate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.WearAnchorPlateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).WearAnchorPlate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/WearAnchorPlate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).WearAnchorPlate(ctx, req.(*channel_live_logic.WearAnchorPlateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveMultiPkPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveMultiPkPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLiveMultiPkPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkPermission(ctx, req.(*channel_live_logic.GetChannelLiveMultiPkPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_SerarchPkAnchor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.SerarchMultiPkAnchorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).SerarchPkAnchor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/SerarchPkAnchor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).SerarchPkAnchor(ctx, req.(*channel_live_logic.SerarchMultiPkAnchorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_ApplyChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.ApplyChannelLiveMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).ApplyChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/ApplyChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).ApplyChannelLiveMultiPk(ctx, req.(*channel_live_logic.ApplyChannelLiveMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_MatchMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.MatchMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).MatchMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/MatchMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).MatchMultiPk(ctx, req.(*channel_live_logic.MatchMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CancelMatchMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CancelMatchMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CancelMatchMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/CancelMatchMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CancelMatchMultiPk(ctx, req.(*channel_live_logic.CancelMatchMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_AcceptChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.AcceptChannelLiveMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).AcceptChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/AcceptChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).AcceptChannelLiveMultiPk(ctx, req.(*channel_live_logic.AcceptChannelLiveMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_StartChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.StartChannelLiveMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).StartChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/StartChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).StartChannelLiveMultiPk(ctx, req.(*channel_live_logic.StartChannelLiveMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveMultiPkRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveMultiPkRankRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLiveMultiPkRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkRank(ctx, req.(*channel_live_logic.GetChannelLiveMultiPkRankRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveMultiPkKnightList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveMultiPkKnightListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkKnightList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLiveMultiPkKnightList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkKnightList(ctx, req.(*channel_live_logic.GetChannelLiveMultiPkKnightListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveMultiPkRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveMultiPkRecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLiveMultiPkRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkRecordList(ctx, req.(*channel_live_logic.GetChannelLiveMultiPkRecordListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_CancelChannelLiveMultiPkTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.CancelChannelLiveMultiPkTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).CancelChannelLiveMultiPkTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/CancelChannelLiveMultiPkTeam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).CancelChannelLiveMultiPkTeam(ctx, req.(*channel_live_logic.CancelChannelLiveMultiPkTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_StopChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.StopChannelLiveMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).StopChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/StopChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).StopChannelLiveMultiPk(ctx, req.(*channel_live_logic.StopChannelLiveMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_DisinviteChannelLiveMultiPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.DisinviteChannelLiveMultiPkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).DisinviteChannelLiveMultiPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/DisinviteChannelLiveMultiPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).DisinviteChannelLiveMultiPk(ctx, req.(*channel_live_logic.DisinviteChannelLiveMultiPkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetChannelLiveMultiPkTeamInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkTeamInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetChannelLiveMultiPkTeamInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetChannelLiveMultiPkTeamInfo(ctx, req.(*channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_InitChannelLiveMultiPkTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.InitChannelLiveMultiPkTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).InitChannelLiveMultiPkTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/InitChannelLiveMultiPkTeam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).InitChannelLiveMultiPkTeam(ctx, req.(*channel_live_logic.InitChannelLiveMultiPkTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_LeaveFansGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.LeaveFansGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).LeaveFansGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/LeaveFansGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).LeaveFansGroup(ctx, req.(*channel_live_logic.LeaveFansGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetVirtualLiveChannelSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetVirtualLiveChannelSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetVirtualLiveChannelSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetVirtualLiveChannelSecret",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetVirtualLiveChannelSecret(ctx, req.(*channel_live_logic.GetVirtualLiveChannelSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveLogic_GetUserFansGiftPri_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_live_logic.GetUserFansGiftPriRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveLogicServer).GetUserFansGiftPri(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLiveLogic/GetUserFansGiftPri",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveLogicServer).GetUserFansGiftPri(ctx, req.(*channel_live_logic.GetUserFansGiftPriRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelLiveLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.ChannelLiveLogic",
	HandlerType: (*ChannelLiveLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLiveChannelInfo",
			Handler:    _ChannelLiveLogic_GetLiveChannelInfo_Handler,
		},
		{
			MethodName: "ChannelLiveHeartbeat",
			Handler:    _ChannelLiveLogic_ChannelLiveHeartbeat_Handler,
		},
		{
			MethodName: "SetChannelLiveStatus",
			Handler:    _ChannelLiveLogic_SetChannelLiveStatus_Handler,
		},
		{
			MethodName: "GetChannelLiveStatus",
			Handler:    _ChannelLiveLogic_GetChannelLiveStatus_Handler,
		},
		{
			MethodName: "ApplyPk",
			Handler:    _ChannelLiveLogic_ApplyPk_Handler,
		},
		{
			MethodName: "HandlerApply",
			Handler:    _ChannelLiveLogic_HandlerApply_Handler,
		},
		{
			MethodName: "BatchGetChannelLiveStatusByAccount",
			Handler:    _ChannelLiveLogic_BatchGetChannelLiveStatusByAccount_Handler,
		},
		{
			MethodName: "SetPkStatus",
			Handler:    _ChannelLiveLogic_SetPkStatus_Handler,
		},
		{
			MethodName: "CancelPKApply",
			Handler:    _ChannelLiveLogic_CancelPKApply_Handler,
		},
		{
			MethodName: "GetChannelLivePKRecord",
			Handler:    _ChannelLiveLogic_GetChannelLivePKRecord_Handler,
		},
		{
			MethodName: "GetChannelLivePkRankUser",
			Handler:    _ChannelLiveLogic_GetChannelLivePkRankUser_Handler,
		},
		{
			MethodName: "GetChannelLiveRankUser",
			Handler:    _ChannelLiveLogic_GetChannelLiveRankUser_Handler,
		},
		{
			MethodName: "GetChannelLiveWatchTimeRankUser",
			Handler:    _ChannelLiveLogic_GetChannelLiveWatchTimeRankUser_Handler,
		},
		{
			MethodName: "GetChannelLiveData",
			Handler:    _ChannelLiveLogic_GetChannelLiveData_Handler,
		},
		{
			MethodName: "GetFansRankList",
			Handler:    _ChannelLiveLogic_GetFansRankList_Handler,
		},
		{
			MethodName: "GetFansInfo",
			Handler:    _ChannelLiveLogic_GetFansInfo_Handler,
		},
		{
			MethodName: "GetAnchorFansInfo",
			Handler:    _ChannelLiveLogic_GetAnchorFansInfo_Handler,
		},
		{
			MethodName: "SearchAnchor",
			Handler:    _ChannelLiveLogic_SearchAnchor_Handler,
		},
		{
			MethodName: "ReportClientIDChange",
			Handler:    _ChannelLiveLogic_ReportClientIDChange_Handler,
		},
		{
			MethodName: "GetApplyList",
			Handler:    _ChannelLiveLogic_GetApplyList_Handler,
		},
		{
			MethodName: "GetPkInfo",
			Handler:    _ChannelLiveLogic_GetPkInfo_Handler,
		},
		{
			MethodName: "GetMyToolList",
			Handler:    _ChannelLiveLogic_GetMyToolList_Handler,
		},
		{
			MethodName: "GetItemConfig",
			Handler:    _ChannelLiveLogic_GetItemConfig_Handler,
		},
		{
			MethodName: "SetChannelLiveOpponentMicFlag",
			Handler:    _ChannelLiveLogic_SetChannelLiveOpponentMicFlag_Handler,
		},
		{
			MethodName: "StartPkMatch",
			Handler:    _ChannelLiveLogic_StartPkMatch_Handler,
		},
		{
			MethodName: "CancelPkMatch",
			Handler:    _ChannelLiveLogic_CancelPkMatch_Handler,
		},
		{
			MethodName: "GetPKMatchInfo",
			Handler:    _ChannelLiveLogic_GetPKMatchInfo_Handler,
		},
		{
			MethodName: "GetUserMissionList",
			Handler:    _ChannelLiveLogic_GetUserMissionList_Handler,
		},
		{
			MethodName: "GetFansMissionList",
			Handler:    _ChannelLiveLogic_GetFansMissionList_Handler,
		},
		{
			MethodName: "GetActorMissionList",
			Handler:    _ChannelLiveLogic_GetActorMissionList_Handler,
		},
		{
			MethodName: "HandleUserMissionAtInterval",
			Handler:    _ChannelLiveLogic_HandleUserMissionAtInterval_Handler,
		},
		{
			MethodName: "HandleShareLiveChannelMission",
			Handler:    _ChannelLiveLogic_HandleShareLiveChannelMission_Handler,
		},
		{
			MethodName: "GetProcessActorMissionDesc",
			Handler:    _ChannelLiveLogic_GetProcessActorMissionDesc_Handler,
		},
		{
			MethodName: "HandleFansMissionAtInterval",
			Handler:    _ChannelLiveLogic_HandleFansMissionAtInterval_Handler,
		},
		{
			MethodName: "GetAnchorHonorNameplate",
			Handler:    _ChannelLiveLogic_GetAnchorHonorNameplate_Handler,
		},
		{
			MethodName: "GetRankingList",
			Handler:    _ChannelLiveLogic_GetRankingList_Handler,
		},
		{
			MethodName: "GetFansAddedGroupList",
			Handler:    _ChannelLiveLogic_GetFansAddedGroupList_Handler,
		},
		{
			MethodName: "SetFansGroupName",
			Handler:    _ChannelLiveLogic_SetFansGroupName_Handler,
		},
		{
			MethodName: "CheckSetGroupNamePermit",
			Handler:    _ChannelLiveLogic_CheckSetGroupNamePermit_Handler,
		},
		{
			MethodName: "CheckUserIsFans",
			Handler:    _ChannelLiveLogic_CheckUserIsFans_Handler,
		},
		{
			MethodName: "ChannelLiveReport",
			Handler:    _ChannelLiveLogic_ChannelLiveReport_Handler,
		},
		{
			MethodName: "AcceptAppointPk",
			Handler:    _ChannelLiveLogic_AcceptAppointPk_Handler,
		},
		{
			MethodName: "ConfirmAppointPkPush",
			Handler:    _ChannelLiveLogic_ConfirmAppointPkPush_Handler,
		},
		{
			MethodName: "GetAppointPkInfo",
			Handler:    _ChannelLiveLogic_GetAppointPkInfo_Handler,
		},
		{
			MethodName: "GetAnchorValidPlateList",
			Handler:    _ChannelLiveLogic_GetAnchorValidPlateList_Handler,
		},
		{
			MethodName: "WearAnchorPlate",
			Handler:    _ChannelLiveLogic_WearAnchorPlate_Handler,
		},
		{
			MethodName: "GetChannelLiveMultiPkPermission",
			Handler:    _ChannelLiveLogic_GetChannelLiveMultiPkPermission_Handler,
		},
		{
			MethodName: "SerarchPkAnchor",
			Handler:    _ChannelLiveLogic_SerarchPkAnchor_Handler,
		},
		{
			MethodName: "ApplyChannelLiveMultiPk",
			Handler:    _ChannelLiveLogic_ApplyChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "MatchMultiPk",
			Handler:    _ChannelLiveLogic_MatchMultiPk_Handler,
		},
		{
			MethodName: "CancelMatchMultiPk",
			Handler:    _ChannelLiveLogic_CancelMatchMultiPk_Handler,
		},
		{
			MethodName: "AcceptChannelLiveMultiPk",
			Handler:    _ChannelLiveLogic_AcceptChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "StartChannelLiveMultiPk",
			Handler:    _ChannelLiveLogic_StartChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "GetChannelLiveMultiPkRank",
			Handler:    _ChannelLiveLogic_GetChannelLiveMultiPkRank_Handler,
		},
		{
			MethodName: "GetChannelLiveMultiPkKnightList",
			Handler:    _ChannelLiveLogic_GetChannelLiveMultiPkKnightList_Handler,
		},
		{
			MethodName: "GetChannelLiveMultiPkRecordList",
			Handler:    _ChannelLiveLogic_GetChannelLiveMultiPkRecordList_Handler,
		},
		{
			MethodName: "CancelChannelLiveMultiPkTeam",
			Handler:    _ChannelLiveLogic_CancelChannelLiveMultiPkTeam_Handler,
		},
		{
			MethodName: "StopChannelLiveMultiPk",
			Handler:    _ChannelLiveLogic_StopChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "DisinviteChannelLiveMultiPk",
			Handler:    _ChannelLiveLogic_DisinviteChannelLiveMultiPk_Handler,
		},
		{
			MethodName: "GetChannelLiveMultiPkTeamInfo",
			Handler:    _ChannelLiveLogic_GetChannelLiveMultiPkTeamInfo_Handler,
		},
		{
			MethodName: "InitChannelLiveMultiPkTeam",
			Handler:    _ChannelLiveLogic_InitChannelLiveMultiPkTeam_Handler,
		},
		{
			MethodName: "LeaveFansGroup",
			Handler:    _ChannelLiveLogic_LeaveFansGroup_Handler,
		},
		{
			MethodName: "GetVirtualLiveChannelSecret",
			Handler:    _ChannelLiveLogic_GetVirtualLiveChannelSecret_Handler,
		},
		{
			MethodName: "GetUserFansGiftPri",
			Handler:    _ChannelLiveLogic_GetUserFansGiftPri_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/channel-live-logic/channel-live-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/channel-live-logic/channel-live-logic.proto", fileDescriptor_channel_live_logic_44d530115796d5a1)
}

var fileDescriptor_channel_live_logic_44d530115796d5a1 = []byte{
	// 1788 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x9a, 0xeb, 0x8f, 0xd5, 0x44,
	0x1f, 0xc7, 0xd3, 0x3c, 0x79, 0x9e, 0x87, 0x67, 0x1e, 0x04, 0x1c, 0x15, 0x70, 0x5b, 0x72, 0x22,
	0x0a, 0x72, 0xdb, 0xb3, 0xec, 0xc2, 0x72, 0xc7, 0xb8, 0x97, 0x70, 0xd8, 0xec, 0xae, 0x9e, 0xec,
	0x41, 0x48, 0x78, 0x83, 0xa5, 0x3b, 0x7b, 0x4e, 0x73, 0xba, 0x6d, 0x69, 0xe7, 0x1c, 0xdc, 0x98,
	0x18, 0x11, 0x14, 0xc5, 0x0b, 0x0a, 0x42, 0x8c, 0x18, 0x13, 0x10, 0xaf, 0x28, 0xe0, 0xed, 0x85,
	0x7f, 0x85, 0x7f, 0x88, 0xf1, 0x86, 0xf7, 0xc4, 0x5b, 0x66, 0xda, 0x9e, 0xe9, 0x9c, 0x76, 0xa6,
	0xed, 0xf1, 0x15, 0x59, 0xfa, 0xf9, 0xf6, 0xfb, 0x6d, 0x67, 0xe6, 0x37, 0xbf, 0xe9, 0x2e, 0xd8,
	0x6e, 0x39, 0x75, 0xd3, 0xf0, 0xdb, 0x5e, 0x7f, 0xdd, 0x19, 0x30, 0x1a, 0xba, 0x6d, 0x23, 0xab,
	0xdf, 0x32, 0xdb, 0xa8, 0x9f, 0x5e, 0x48, 0xf9, 0xaf, 0xb2, 0xeb, 0x39, 0xd8, 0x81, 0xff, 0xa6,
	0x3f, 0xf4, 0xad, 0x8f, 0xeb, 0xeb, 0x3a, 0x46, 0xc7, 0xf5, 0x85, 0x01, 0xc7, 0xc5, 0xa6, 0x63,
	0xfb, 0xd1, 0xbf, 0x81, 0xa2, 0x6f, 0x95, 0xee, 0xba, 0x29, 0xf7, 0x3b, 0x12, 0x5c, 0x1e, 0xfa,
	0x6a, 0x14, 0x2c, 0x1b, 0x0b, 0xae, 0x4e, 0x99, 0x6d, 0x34, 0x45, 0xae, 0xc1, 0xc7, 0x01, 0xac,
	0x20, 0x4c, 0x7e, 0x0e, 0x2f, 0x4d, 0xd8, 0x73, 0x0e, 0xdc, 0x54, 0xae, 0xeb, 0xe5, 0xf0, 0x4e,
	0x47, 0xc8, 0x9d, 0x8e, 0x04, 0xc9, 0x92, 0xe8, 0x0c, 0x3a, 0xd6, 0xd7, 0x5f, 0x80, 0xf6, 0xdd,
	0xd5, 0x8b, 0x6e, 0x7d, 0xf9, 0x45, 0xf9, 0x5f, 0x8b, 0x7e, 0x57, 0xe1, 0x09, 0x05, 0xdc, 0x19,
	0x4b, 0xb4, 0x1f, 0xe9, 0x1e, 0x3e, 0x8a, 0x74, 0x0c, 0xcb, 0x82, 0x3b, 0xa6, 0xc1, 0x24, 0xc1,
	0x40, 0x21, 0x9e, 0x65, 0xf8, 0x23, 0xc8, 0x50, 0x43, 0x38, 0x46, 0xd6, 0xb0, 0x8e, 0x5b, 0xbe,
	0x30, 0x43, 0x1a, 0x2c, 0xcb, 0x90, 0xce, 0xb3, 0x0c, 0x7f, 0x06, 0x19, 0x2a, 0x45, 0x32, 0x54,
	0x0a, 0x66, 0xa8, 0x64, 0x64, 0xf8, 0x4b, 0x85, 0x87, 0xc1, 0x7f, 0x47, 0x5c, 0xd7, 0x5a, 0xa8,
	0x36, 0xe1, 0x3d, 0x82, 0xbb, 0x84, 0xd7, 0x89, 0xd1, 0xea, 0x2c, 0x84, 0xdd, 0xfb, 0x49, 0x0d,
	0x9a, 0x60, 0xf1, 0x7e, 0xdd, 0x9e, 0xb5, 0x90, 0x47, 0xaf, 0xc3, 0xb5, 0x02, 0x75, 0x1c, 0x22,
	0x2e, 0xf7, 0xe7, 0xe2, 0x98, 0xd5, 0x09, 0x0d, 0x5e, 0x53, 0xc0, 0xea, 0x51, 0x1d, 0x1b, 0x8d,
	0xb4, 0x47, 0x1e, 0x5d, 0x18, 0x31, 0x0c, 0xa7, 0x65, 0x63, 0xb8, 0x47, 0x70, 0xe7, 0x6c, 0x29,
	0xc9, 0xb5, 0xf7, 0x1f, 0xa8, 0x59, 0xda, 0xa7, 0x34, 0x38, 0x07, 0xfe, 0x5f, 0x43, 0xb8, 0xda,
	0x0c, 0x87, 0x7b, 0x8d, 0x78, 0x0a, 0x45, 0x0c, 0xb1, 0x5f, 0x9b, 0x07, 0x63, 0x3e, 0x27, 0x35,
	0x68, 0x81, 0xdb, 0xc6, 0x74, 0xdb, 0x40, 0x56, 0x75, 0x32, 0x18, 0x01, 0xd1, 0x9b, 0xe5, 0x28,
	0xe2, 0xb5, 0x2e, 0x1f, 0xc8, 0xdc, 0x4e, 0x69, 0xf0, 0xb4, 0x02, 0x96, 0xf3, 0x6f, 0xa1, 0x3a,
	0x39, 0x83, 0x0c, 0xc7, 0x9b, 0x85, 0x9b, 0x73, 0x4d, 0xd0, 0x08, 0x27, 0x01, 0x06, 0x0b, 0x2a,
	0x58, 0x92, 0xa7, 0x35, 0xf8, 0xa2, 0x02, 0x56, 0x76, 0x81, 0xcd, 0x19, 0xdd, 0x6e, 0x3e, 0xe2,
	0x23, 0x0f, 0x0e, 0xe5, 0xbb, 0x73, 0x47, 0x40, 0xd2, 0x6c, 0x29, 0xac, 0x61, 0x79, 0x9e, 0x49,
	0x7b, 0x33, 0x9d, 0x34, 0xf9, 0xde, 0x4c, 0x3c, 0xcb, 0x60, 0x41, 0x05, 0x4b, 0x72, 0x5a, 0x83,
	0x57, 0x14, 0x50, 0xe2, 0xc1, 0x43, 0x64, 0xf2, 0x1e, 0x30, 0xe7, 0x59, 0xa4, 0x9d, 0xb9, 0x0c,
	0x12, 0x3a, 0x92, 0x6d, 0x57, 0xaf, 0x52, 0x16, 0xf2, 0x59, 0x2d, 0xdc, 0x9c, 0x62, 0x82, 0x71,
	0x1d, 0xeb, 0xb2, 0xcd, 0xa9, 0x0b, 0xcd, 0xd8, 0x9c, 0x12, 0x34, 0x33, 0x7f, 0x4e, 0x83, 0x1e,
	0x58, 0x5a, 0x41, 0x78, 0x9f, 0x6e, 0xfb, 0x24, 0xdd, 0x94, 0xe9, 0x63, 0xb8, 0x5e, 0x7c, 0xaf,
	0x38, 0x47, 0x6c, 0x37, 0xe4, 0x45, 0x99, 0xe7, 0x19, 0x5a, 0x0f, 0x42, 0x80, 0x6e, 0xc3, 0x6b,
	0xe4, 0x37, 0x89, 0xf6, 0xdf, 0xb5, 0x79, 0x30, 0xe6, 0xf3, 0xbc, 0x06, 0x1f, 0x03, 0xb7, 0x57,
	0x10, 0x1e, 0xb1, 0x8d, 0x86, 0xe3, 0x75, 0xdc, 0x36, 0x8a, 0x6f, 0xc3, 0x93, 0xc4, 0x73, 0x53,
	0x7e, 0x98, 0x39, 0xbf, 0x40, 0xb7, 0x82, 0x1a, 0xd2, 0x3d, 0xa3, 0x11, 0x50, 0x50, 0x5c, 0xcb,
	0x18, 0x24, 0xdb, 0x0a, 0x78, 0x8e, 0x59, 0xbd, 0xa4, 0xd1, 0x5d, 0x75, 0x06, 0xb9, 0x8e, 0x87,
	0xc7, 0x2c, 0x13, 0xd9, 0x78, 0x62, 0x9c, 0x0c, 0x78, 0x1d, 0x09, 0x77, 0xd5, 0x34, 0x58, 0xb6,
	0xab, 0xa6, 0xf3, 0x2c, 0xc3, 0x59, 0xfa, 0xb8, 0xe4, 0x8d, 0x90, 0x22, 0x49, 0x67, 0x90, 0x64,
	0xa8, 0x3a, 0x90, 0xec, 0x71, 0x79, 0x8e, 0x59, 0xdd, 0x52, 0xe1, 0xa3, 0xe0, 0x7f, 0x15, 0xb2,
	0x01, 0xd0, 0xb1, 0xbc, 0x57, 0xac, 0x0f, 0x08, 0x62, 0x72, 0x5f, 0x36, 0xc4, 0x1c, 0x7e, 0x50,
	0xc9, 0x2e, 0x52, 0x41, 0x78, 0x7a, 0xe1, 0x80, 0xe3, 0x58, 0xf4, 0x69, 0x24, 0x29, 0x19, 0x25,
	0xdb, 0x45, 0xba, 0x40, 0xe6, 0xf6, 0x63, 0xe4, 0x36, 0x81, 0xd1, 0xfc, 0x98, 0x63, 0xcf, 0x99,
	0x75, 0x99, 0x1b, 0xa3, 0x32, 0xdc, 0xe2, 0x20, 0x73, 0xfb, 0x49, 0x85, 0x6f, 0x28, 0x60, 0x15,
	0xdf, 0xa9, 0x3d, 0xec, 0xba, 0x8e, 0x8d, 0x6c, 0x3c, 0x6d, 0x1a, 0xfb, 0x2c, 0xbd, 0x0e, 0xb7,
	0xe7, 0xea, 0xef, 0xba, 0x54, 0x24, 0xce, 0x8e, 0xde, 0x84, 0x2c, 0xde, 0xcf, 0x2a, 0x5d, 0x36,
	0x58, 0xf7, 0x70, 0xb5, 0x39, 0x4d, 0x6a, 0xa6, 0x78, 0xd9, 0xc4, 0x20, 0xe9, 0xb2, 0xe1, 0x38,
	0x66, 0xf5, 0x8b, 0x1a, 0xeb, 0x15, 0x42, 0xaf, 0x8c, 0x5e, 0x81, 0x99, 0xad, 0xcb, 0x07, 0x32,
	0xb7, 0x5f, 0x55, 0xe8, 0x80, 0x25, 0x64, 0xba, 0x4d, 0xd2, 0x6b, 0x74, 0xea, 0x4a, 0x46, 0x2f,
	0x86, 0x11, 0xbf, 0xf5, 0x39, 0x49, 0x66, 0xf8, 0x9b, 0x1a, 0xee, 0x29, 0x64, 0xb3, 0x99, 0x36,
	0x7d, 0xdf, 0x74, 0x6c, 0x3a, 0x93, 0x25, 0xe5, 0xac, 0x0b, 0xcd, 0xd8, 0x53, 0x12, 0x34, 0x33,
	0x7f, 0x39, 0xda, 0xd0, 0x48, 0x69, 0xcc, 0x69, 0xde, 0x85, 0x66, 0x98, 0x27, 0x68, 0x66, 0xfe,
	0x8a, 0x06, 0x9f, 0x00, 0x77, 0x90, 0xf2, 0x61, 0x60, 0x87, 0x7b, 0x74, 0xc9, 0xfd, 0xba, 0x59,
	0x62, 0x5f, 0x2e, 0x82, 0x33, 0xff, 0x73, 0x1a, 0xbc, 0xa8, 0x00, 0x35, 0xe8, 0xdc, 0x63, 0x2f,
	0x69, 0x04, 0x4f, 0xd8, 0x18, 0x79, 0x6d, 0xdd, 0x82, 0xc3, 0xd2, 0x6e, 0x3f, 0x55, 0x43, 0x02,
	0x6d, 0xeb, 0x45, 0xc6, 0x82, 0x9d, 0xd7, 0xe8, 0xda, 0x0f, 0xe8, 0x5a, 0x43, 0xf7, 0x50, 0xec,
	0xcc, 0x1a, 0x2a, 0x85, 0x6b, 0x5f, 0xaa, 0x92, 0xad, 0xfd, 0x0c, 0x21, 0x8b, 0xf7, 0xaa, 0x06,
	0xcf, 0x2b, 0xa0, 0x8f, 0x4c, 0x69, 0xcf, 0x31, 0x90, 0xef, 0xc7, 0x5f, 0xf2, 0x38, 0xf2, 0x0d,
	0xb8, 0x55, 0xb2, 0x0a, 0xd2, 0x25, 0x24, 0xd8, 0x70, 0x0f, 0x2a, 0x96, 0xea, 0x42, 0x7c, 0x34,
	0x63, 0xb3, 0x2e, 0xf7, 0x68, 0xa6, 0x6a, 0xb2, 0x47, 0x53, 0x20, 0x63, 0xc1, 0x2e, 0x6a, 0xf0,
	0x8c, 0x02, 0x56, 0x74, 0xba, 0x90, 0xfd, 0x8e, 0xed, 0x78, 0x0f, 0xe9, 0xf3, 0xc8, 0xb5, 0x74,
	0x8c, 0xe0, 0x60, 0x56, 0xd7, 0xc2, 0xf3, 0x24, 0xd0, 0x50, 0x51, 0x09, 0x0b, 0xf3, 0xba, 0x16,
	0x96, 0x37, 0xd2, 0xed, 0x99, 0x76, 0x9d, 0x2e, 0x37, 0x49, 0x79, 0x8b, 0x61, 0x19, 0xe5, 0x8d,
	0x23, 0x99, 0xe1, 0x25, 0x0d, 0x9e, 0x52, 0xc0, 0x5d, 0x61, 0x25, 0x18, 0x99, 0x9d, 0x45, 0xb3,
	0x15, 0xcf, 0x69, 0xb9, 0xd4, 0x78, 0x40, 0x5e, 0x37, 0x78, 0x9a, 0xf8, 0x6f, 0x2e, 0x26, 0x60,
	0x31, 0xae, 0x6b, 0xb0, 0x05, 0x96, 0xd5, 0x02, 0x8c, 0x12, 0xe4, 0xf5, 0xc0, 0x0d, 0xe2, 0x7d,
	0x90, 0x03, 0x89, 0xf7, 0xc6, 0xdc, 0x2c, 0xb3, 0xbd, 0x11, 0x8c, 0xfd, 0x58, 0x03, 0x19, 0xcd,
	0x1a, 0xc2, 0x1d, 0xa6, 0x8a, 0xbc, 0x79, 0x13, 0x0b, 0xc7, 0x5e, 0xc0, 0xcb, 0xc6, 0x5e, 0x28,
	0x61, 0x61, 0x6e, 0xd2, 0x03, 0x04, 0x05, 0x49, 0x09, 0x9a, 0xf0, 0x49, 0x6c, 0xe1, 0x01, 0xa2,
	0x8b, 0x93, 0x1d, 0x20, 0x12, 0x28, 0xf3, 0xfc, 0x98, 0x36, 0xf6, 0xf1, 0xb3, 0x1f, 0x6d, 0x50,
	0x85, 0x8d, 0x7d, 0x82, 0x94, 0x35, 0xf6, 0x29, 0x30, 0x73, 0xfe, 0x84, 0x3e, 0xed, 0x88, 0x61,
	0x20, 0x97, 0xf4, 0xa7, 0x8e, 0x69, 0xe3, 0x6a, 0x53, 0xf8, 0xb4, 0x5d, 0x9c, 0xec, 0x69, 0x13,
	0x28, 0xf3, 0xfc, 0x36, 0xfc, 0x7e, 0x48, 0xba, 0x39, 0x6f, 0xbe, 0x83, 0x54, 0x5b, 0x7e, 0x43,
	0xfc, 0xfd, 0x30, 0x05, 0x96, 0x7e, 0x3f, 0x4c, 0xe5, 0x59, 0x86, 0xef, 0x54, 0x32, 0xd3, 0x83,
	0xa6, 0x3c, 0x20, 0x68, 0x0b, 0xb3, 0x41, 0xda, 0xbd, 0x33, 0x50, 0x36, 0xd3, 0x93, 0x2c, 0xb3,
	0xfd, 0x5e, 0xe5, 0xab, 0xdc, 0x41, 0xdd, 0x32, 0x67, 0xab, 0xa4, 0xfc, 0xd0, 0x95, 0x9e, 0x59,
	0xe5, 0x78, 0x3e, 0x57, 0x95, 0xeb, 0x96, 0xb0, 0x30, 0x5f, 0xab, 0x64, 0xec, 0x0f, 0x21, 0xdd,
	0x0b, 0x48, 0x0a, 0x09, 0xc7, 0xbe, 0x8b, 0x93, 0x8d, 0x7d, 0x02, 0x65, 0x9e, 0xdf, 0xa8, 0xf0,
	0x83, 0xc4, 0x07, 0x8c, 0xe9, 0x96, 0x85, 0xcd, 0x6a, 0x93, 0xae, 0xc5, 0x60, 0xdb, 0xde, 0x9b,
	0xeb, 0xec, 0x9f, 0xd0, 0xcd, 0xa0, 0x63, 0x2d, 0xe4, 0xe3, 0xbe, 0x07, 0x7a, 0x95, 0xfb, 0xae,
	0x63, 0xfb, 0x28, 0x0a, 0x7b, 0xb9, 0x44, 0xaa, 0xf2, 0xd2, 0x1a, 0xf2, 0xc8, 0x51, 0xb5, 0xda,
	0x0c, 0x4f, 0xbe, 0x43, 0xc2, 0x12, 0x47, 0xb9, 0xf0, 0xb6, 0x9d, 0x13, 0x30, 0x4d, 0xb4, 0xa5,
	0x90, 0x86, 0x8f, 0x71, 0xa5, 0x04, 0xcf, 0x29, 0x60, 0x05, 0x3d, 0x3e, 0x26, 0xe3, 0x0b, 0xf7,
	0x6b, 0x01, 0x1f, 0x25, 0xda, 0x56, 0x54, 0xc6, 0x87, 0x7a, 0xab, 0x04, 0x8f, 0x81, 0xc5, 0xb4,
	0x57, 0x8f, 0x82, 0x88, 0xa6, 0x43, 0x1c, 0x8a, 0xdc, 0x37, 0xe6, 0x62, 0x79, 0xcb, 0xab, 0x25,
	0x78, 0x52, 0x01, 0x30, 0x38, 0x94, 0x70, 0xce, 0x9b, 0xa5, 0xe7, 0x97, 0x34, 0xff, 0xc1, 0x02,
	0x0a, 0x3e, 0xc5, 0xdb, 0x25, 0x78, 0x41, 0x01, 0x2b, 0x83, 0xfa, 0x96, 0x32, 0x1c, 0xdb, 0xa4,
	0x05, 0x51, 0x3c, 0x1e, 0xdb, 0x0b, 0xeb, 0xf8, 0x5c, 0xef, 0x04, 0xb3, 0x84, 0x9e, 0x0f, 0x0b,
	0xcc, 0x12, 0x01, 0x9f, 0x35, 0x4b, 0x84, 0x32, 0x3e, 0xd4, 0xbb, 0x25, 0xf8, 0x9a, 0x02, 0xee,
	0x4e, 0x5d, 0x77, 0xa4, 0x15, 0x12, 0xf6, 0xe7, 0x42, 0x45, 0x14, 0x6c, 0x47, 0x71, 0x21, 0x1f,
	0xed, 0xbd, 0x92, 0xb8, 0x12, 0x4d, 0xda, 0x66, 0xbd, 0x81, 0x69, 0x49, 0x2e, 0x54, 0x89, 0x98,
	0xae, 0xa7, 0x4a, 0x14, 0x97, 0xf3, 0x61, 0xdf, 0x97, 0x84, 0x0d, 0xbe, 0x9f, 0x17, 0x0f, 0xcb,
	0x74, 0x3d, 0x85, 0x8d, 0xcb, 0xf9, 0xb0, 0xd7, 0x4a, 0xf0, 0xb2, 0x02, 0xb4, 0x60, 0x29, 0x25,
	0x85, 0x07, 0x90, 0x3e, 0x0f, 0x77, 0x49, 0xd7, 0x5f, 0xba, 0x28, 0x8a, 0xb9, 0xbb, 0x27, 0x2d,
	0x9f, 0xf1, 0xc3, 0x12, 0x3c, 0xab, 0x80, 0xe5, 0x35, 0xec, 0xb8, 0x29, 0x8b, 0x65, 0xab, 0x70,
	0xd6, 0xa7, 0xe1, 0x51, 0xae, 0xe1, 0x82, 0x2a, 0x3e, 0xd1, 0x47, 0x25, 0xf8, 0xa6, 0x02, 0xd4,
	0x71, 0xd3, 0x37, 0xed, 0xb6, 0x89, 0x51, 0x4a, 0x2c, 0xd1, 0x67, 0x7d, 0x89, 0x26, 0xca, 0xb6,
	0xab, 0x17, 0x29, 0x1f, 0xf0, 0x7a, 0x09, 0x5e, 0x55, 0xc0, 0xaa, 0xd4, 0xc9, 0x40, 0x5e, 0x31,
	0x6d, 0xa0, 0x76, 0x17, 0x99, 0x42, 0x91, 0x2a, 0x0a, 0xb9, 0xa7, 0x37, 0x31, 0x1f, 0xf3, 0x46,
	0x09, 0x5e, 0x52, 0x40, 0xdf, 0x84, 0x6d, 0x0a, 0x44, 0x50, 0x54, 0x3a, 0xc4, 0x92, 0x28, 0xe0,
	0xce, 0x1e, 0x94, 0x7c, 0xba, 0x9b, 0x25, 0x78, 0x1c, 0x2c, 0x99, 0x42, 0x7a, 0x1b, 0x75, 0x8e,
	0x43, 0xc2, 0xcf, 0x48, 0x3c, 0x16, 0x85, 0xe8, 0xcf, 0x49, 0xf3, 0xc6, 0x9f, 0x6a, 0x74, 0x7a,
	0x55, 0x10, 0x3e, 0x68, 0x7a, 0xb8, 0xa5, 0x5b, 0xb1, 0xaf, 0x17, 0x35, 0x64, 0x78, 0x08, 0xcb,
	0x7e, 0x6b, 0x24, 0xd2, 0x64, 0x4d, 0x2f, 0xa9, 0x94, 0x0f, 0xf8, 0x99, 0x46, 0x77, 0xf7, 0xf0,
	0x4b, 0x1c, 0x7d, 0x0e, 0x73, 0x0e, 0x57, 0x3d, 0x53, 0xf6, 0x0b, 0xb6, 0x2e, 0x34, 0x6b, 0x77,
	0x4f, 0x53, 0xf0, 0x29, 0x3e, 0xd7, 0x46, 0x47, 0x0f, 0x3f, 0x58, 0x77, 0x2c, 0xdd, 0xae, 0x97,
	0x87, 0x87, 0x30, 0x2e, 0x1b, 0xce, 0xfc, 0x00, 0xfd, 0x33, 0x0c, 0xc3, 0xb1, 0x06, 0x7c, 0xe4,
	0xb5, 0x4d, 0x03, 0xf9, 0x03, 0xf2, 0xbf, 0x0d, 0x39, 0xfa, 0x1f, 0xaa, 0xd8, 0xf2, 0x77, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x8f, 0x7f, 0xe9, 0x20, 0x44, 0x22, 0x00, 0x00,
}
