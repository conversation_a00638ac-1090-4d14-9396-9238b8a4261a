// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oauth2-logic.proto

package oauth2logic // import "golang.52tt.com/protocol/services/logicsvr-go/oauth2logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import oauth2logic "golang.52tt.com/protocol/app/oauth2logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// OAuth2LogicClient is the client API for OAuth2Logic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OAuth2LogicClient interface {
	GetScope(ctx context.Context, in *oauth2logic.GetScopeReq, opts ...grpc.CallOption) (*oauth2logic.GetScopeResp, error)
	GetAuthorizeCode(ctx context.Context, in *oauth2logic.GetAuthorizeCodeReq, opts ...grpc.CallOption) (*oauth2logic.GetAuthorizeCodeResp, error)
}

type oAuth2LogicClient struct {
	cc *grpc.ClientConn
}

func NewOAuth2LogicClient(cc *grpc.ClientConn) OAuth2LogicClient {
	return &oAuth2LogicClient{cc}
}

func (c *oAuth2LogicClient) GetScope(ctx context.Context, in *oauth2logic.GetScopeReq, opts ...grpc.CallOption) (*oauth2logic.GetScopeResp, error) {
	out := new(oauth2logic.GetScopeResp)
	err := c.cc.Invoke(ctx, "/logic.OAuth2Logic/GetScope", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oAuth2LogicClient) GetAuthorizeCode(ctx context.Context, in *oauth2logic.GetAuthorizeCodeReq, opts ...grpc.CallOption) (*oauth2logic.GetAuthorizeCodeResp, error) {
	out := new(oauth2logic.GetAuthorizeCodeResp)
	err := c.cc.Invoke(ctx, "/logic.OAuth2Logic/GetAuthorizeCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OAuth2LogicServer is the server API for OAuth2Logic service.
type OAuth2LogicServer interface {
	GetScope(context.Context, *oauth2logic.GetScopeReq) (*oauth2logic.GetScopeResp, error)
	GetAuthorizeCode(context.Context, *oauth2logic.GetAuthorizeCodeReq) (*oauth2logic.GetAuthorizeCodeResp, error)
}

func RegisterOAuth2LogicServer(s *grpc.Server, srv OAuth2LogicServer) {
	s.RegisterService(&_OAuth2Logic_serviceDesc, srv)
}

func _OAuth2Logic_GetScope_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(oauth2logic.GetScopeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuth2LogicServer).GetScope(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.OAuth2Logic/GetScope",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuth2LogicServer).GetScope(ctx, req.(*oauth2logic.GetScopeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OAuth2Logic_GetAuthorizeCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(oauth2logic.GetAuthorizeCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuth2LogicServer).GetAuthorizeCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.OAuth2Logic/GetAuthorizeCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuth2LogicServer).GetAuthorizeCode(ctx, req.(*oauth2logic.GetAuthorizeCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _OAuth2Logic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.OAuth2Logic",
	HandlerType: (*OAuth2LogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetScope",
			Handler:    _OAuth2Logic_GetScope_Handler,
		},
		{
			MethodName: "GetAuthorizeCode",
			Handler:    _OAuth2Logic_GetAuthorizeCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oauth2-logic.proto",
}

func init() { proto.RegisterFile("oauth2-logic.proto", fileDescriptor_oauth2_logic_5bb81147dd43bea8) }

var fileDescriptor_oauth2_logic_5bb81147dd43bea8 = []byte{
	// 225 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0xca, 0x4f, 0x2c, 0x2d,
	0xc9, 0x30, 0xd2, 0xcd, 0xc9, 0x4f, 0xcf, 0x4c, 0xd6, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62,
	0x05, 0x73, 0xa4, 0x34, 0xc1, 0x54, 0x71, 0x59, 0x91, 0x6e, 0x7a, 0xbe, 0x7e, 0x7a, 0x62, 0x49,
	0x6a, 0x79, 0x62, 0xa5, 0x7e, 0x7e, 0x41, 0x49, 0x66, 0x7e, 0x5e, 0x31, 0x8c, 0x86, 0xe8, 0x90,
	0x12, 0x4f, 0x2c, 0x28, 0xd0, 0x47, 0x36, 0x29, 0x1e, 0x22, 0x61, 0x74, 0x92, 0x91, 0x8b, 0xdb,
	0xdf, 0x11, 0x24, 0xee, 0x03, 0x12, 0x16, 0x0a, 0xe0, 0xe2, 0x70, 0x4f, 0x2d, 0x09, 0x4e, 0xce,
	0x2f, 0x48, 0x15, 0x92, 0xd6, 0x4b, 0x4f, 0xd4, 0x83, 0x68, 0x82, 0xd8, 0x0e, 0x93, 0x09, 0x4a,
	0x2d, 0x94, 0x92, 0xc1, 0x2d, 0x59, 0x5c, 0xa0, 0xc4, 0xf9, 0xe9, 0xfc, 0x4e, 0x3d, 0x16, 0x8e,
	0x6d, 0x6f, 0x19, 0x85, 0x32, 0xb8, 0x04, 0xdc, 0x53, 0x4b, 0x40, 0x56, 0xe4, 0x17, 0x65, 0x56,
	0xa5, 0x3a, 0xe7, 0xa7, 0xa4, 0x0a, 0x29, 0x63, 0xd1, 0x8c, 0xa2, 0x02, 0x64, 0x83, 0x0a, 0x61,
	0x45, 0x08, 0x9b, 0xb6, 0xbf, 0x65, 0x74, 0xb2, 0x8e, 0xb2, 0x4c, 0xcf, 0xcf, 0x49, 0xcc, 0x4b,
	0xd7, 0x33, 0x35, 0x2a, 0x29, 0xd1, 0x4b, 0xce, 0xcf, 0xd5, 0x07, 0x7b, 0x32, 0x39, 0x3f, 0x47,
	0xbf, 0x38, 0xb5, 0xa8, 0x2c, 0x33, 0x39, 0xb5, 0x58, 0x1f, 0x39, 0xcc, 0x90, 0x0c, 0x4f, 0x62,
	0x03, 0x2b, 0x35, 0x06, 0x04, 0x00, 0x00, 0xff, 0xff, 0x43, 0x05, 0x66, 0x75, 0x70, 0x01, 0x00,
	0x00,
}
