// Code generated by protoc-gen-go. DO NOT EDIT.
// source: audio-post/audio-post.proto

package audio_post // import "golang.52tt.com/protocol/services/audio-post"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AudioPostScriptTab struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostScriptTab) Reset()         { *m = AudioPostScriptTab{} }
func (m *AudioPostScriptTab) String() string { return proto.CompactTextString(m) }
func (*AudioPostScriptTab) ProtoMessage()    {}
func (*AudioPostScriptTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{0}
}
func (m *AudioPostScriptTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostScriptTab.Unmarshal(m, b)
}
func (m *AudioPostScriptTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostScriptTab.Marshal(b, m, deterministic)
}
func (dst *AudioPostScriptTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostScriptTab.Merge(dst, src)
}
func (m *AudioPostScriptTab) XXX_Size() int {
	return xxx_messageInfo_AudioPostScriptTab.Size(m)
}
func (m *AudioPostScriptTab) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostScriptTab.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostScriptTab proto.InternalMessageInfo

func (m *AudioPostScriptTab) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AudioPostScriptTab) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type InsertAudioPostScriptTabReq struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertAudioPostScriptTabReq) Reset()         { *m = InsertAudioPostScriptTabReq{} }
func (m *InsertAudioPostScriptTabReq) String() string { return proto.CompactTextString(m) }
func (*InsertAudioPostScriptTabReq) ProtoMessage()    {}
func (*InsertAudioPostScriptTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{1}
}
func (m *InsertAudioPostScriptTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertAudioPostScriptTabReq.Unmarshal(m, b)
}
func (m *InsertAudioPostScriptTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertAudioPostScriptTabReq.Marshal(b, m, deterministic)
}
func (dst *InsertAudioPostScriptTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertAudioPostScriptTabReq.Merge(dst, src)
}
func (m *InsertAudioPostScriptTabReq) XXX_Size() int {
	return xxx_messageInfo_InsertAudioPostScriptTabReq.Size(m)
}
func (m *InsertAudioPostScriptTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertAudioPostScriptTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsertAudioPostScriptTabReq proto.InternalMessageInfo

func (m *InsertAudioPostScriptTabReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type InsertAudioPostScriptTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertAudioPostScriptTabResp) Reset()         { *m = InsertAudioPostScriptTabResp{} }
func (m *InsertAudioPostScriptTabResp) String() string { return proto.CompactTextString(m) }
func (*InsertAudioPostScriptTabResp) ProtoMessage()    {}
func (*InsertAudioPostScriptTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{2}
}
func (m *InsertAudioPostScriptTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertAudioPostScriptTabResp.Unmarshal(m, b)
}
func (m *InsertAudioPostScriptTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertAudioPostScriptTabResp.Marshal(b, m, deterministic)
}
func (dst *InsertAudioPostScriptTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertAudioPostScriptTabResp.Merge(dst, src)
}
func (m *InsertAudioPostScriptTabResp) XXX_Size() int {
	return xxx_messageInfo_InsertAudioPostScriptTabResp.Size(m)
}
func (m *InsertAudioPostScriptTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertAudioPostScriptTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_InsertAudioPostScriptTabResp proto.InternalMessageInfo

type UpdateAudioPostScriptTabReq struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAudioPostScriptTabReq) Reset()         { *m = UpdateAudioPostScriptTabReq{} }
func (m *UpdateAudioPostScriptTabReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAudioPostScriptTabReq) ProtoMessage()    {}
func (*UpdateAudioPostScriptTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{3}
}
func (m *UpdateAudioPostScriptTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAudioPostScriptTabReq.Unmarshal(m, b)
}
func (m *UpdateAudioPostScriptTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAudioPostScriptTabReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAudioPostScriptTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAudioPostScriptTabReq.Merge(dst, src)
}
func (m *UpdateAudioPostScriptTabReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAudioPostScriptTabReq.Size(m)
}
func (m *UpdateAudioPostScriptTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAudioPostScriptTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAudioPostScriptTabReq proto.InternalMessageInfo

func (m *UpdateAudioPostScriptTabReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateAudioPostScriptTabReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type UpdateAudioPostScriptTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAudioPostScriptTabResp) Reset()         { *m = UpdateAudioPostScriptTabResp{} }
func (m *UpdateAudioPostScriptTabResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAudioPostScriptTabResp) ProtoMessage()    {}
func (*UpdateAudioPostScriptTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{4}
}
func (m *UpdateAudioPostScriptTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAudioPostScriptTabResp.Unmarshal(m, b)
}
func (m *UpdateAudioPostScriptTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAudioPostScriptTabResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAudioPostScriptTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAudioPostScriptTabResp.Merge(dst, src)
}
func (m *UpdateAudioPostScriptTabResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAudioPostScriptTabResp.Size(m)
}
func (m *UpdateAudioPostScriptTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAudioPostScriptTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAudioPostScriptTabResp proto.InternalMessageInfo

type DeleteAudioPostScriptTabReq struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAudioPostScriptTabReq) Reset()         { *m = DeleteAudioPostScriptTabReq{} }
func (m *DeleteAudioPostScriptTabReq) String() string { return proto.CompactTextString(m) }
func (*DeleteAudioPostScriptTabReq) ProtoMessage()    {}
func (*DeleteAudioPostScriptTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{5}
}
func (m *DeleteAudioPostScriptTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAudioPostScriptTabReq.Unmarshal(m, b)
}
func (m *DeleteAudioPostScriptTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAudioPostScriptTabReq.Marshal(b, m, deterministic)
}
func (dst *DeleteAudioPostScriptTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAudioPostScriptTabReq.Merge(dst, src)
}
func (m *DeleteAudioPostScriptTabReq) XXX_Size() int {
	return xxx_messageInfo_DeleteAudioPostScriptTabReq.Size(m)
}
func (m *DeleteAudioPostScriptTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAudioPostScriptTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAudioPostScriptTabReq proto.InternalMessageInfo

func (m *DeleteAudioPostScriptTabReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteAudioPostScriptTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAudioPostScriptTabResp) Reset()         { *m = DeleteAudioPostScriptTabResp{} }
func (m *DeleteAudioPostScriptTabResp) String() string { return proto.CompactTextString(m) }
func (*DeleteAudioPostScriptTabResp) ProtoMessage()    {}
func (*DeleteAudioPostScriptTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{6}
}
func (m *DeleteAudioPostScriptTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAudioPostScriptTabResp.Unmarshal(m, b)
}
func (m *DeleteAudioPostScriptTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAudioPostScriptTabResp.Marshal(b, m, deterministic)
}
func (dst *DeleteAudioPostScriptTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAudioPostScriptTabResp.Merge(dst, src)
}
func (m *DeleteAudioPostScriptTabResp) XXX_Size() int {
	return xxx_messageInfo_DeleteAudioPostScriptTabResp.Size(m)
}
func (m *DeleteAudioPostScriptTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAudioPostScriptTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAudioPostScriptTabResp proto.InternalMessageInfo

type AudioPostScriptTabsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostScriptTabsReq) Reset()         { *m = AudioPostScriptTabsReq{} }
func (m *AudioPostScriptTabsReq) String() string { return proto.CompactTextString(m) }
func (*AudioPostScriptTabsReq) ProtoMessage()    {}
func (*AudioPostScriptTabsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{7}
}
func (m *AudioPostScriptTabsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostScriptTabsReq.Unmarshal(m, b)
}
func (m *AudioPostScriptTabsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostScriptTabsReq.Marshal(b, m, deterministic)
}
func (dst *AudioPostScriptTabsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostScriptTabsReq.Merge(dst, src)
}
func (m *AudioPostScriptTabsReq) XXX_Size() int {
	return xxx_messageInfo_AudioPostScriptTabsReq.Size(m)
}
func (m *AudioPostScriptTabsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostScriptTabsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostScriptTabsReq proto.InternalMessageInfo

type AudioPostScriptTabsResp struct {
	Tabs                 []*AudioPostScriptTab `protobuf:"bytes,1,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *AudioPostScriptTabsResp) Reset()         { *m = AudioPostScriptTabsResp{} }
func (m *AudioPostScriptTabsResp) String() string { return proto.CompactTextString(m) }
func (*AudioPostScriptTabsResp) ProtoMessage()    {}
func (*AudioPostScriptTabsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{8}
}
func (m *AudioPostScriptTabsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostScriptTabsResp.Unmarshal(m, b)
}
func (m *AudioPostScriptTabsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostScriptTabsResp.Marshal(b, m, deterministic)
}
func (dst *AudioPostScriptTabsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostScriptTabsResp.Merge(dst, src)
}
func (m *AudioPostScriptTabsResp) XXX_Size() int {
	return xxx_messageInfo_AudioPostScriptTabsResp.Size(m)
}
func (m *AudioPostScriptTabsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostScriptTabsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostScriptTabsResp proto.InternalMessageInfo

func (m *AudioPostScriptTabsResp) GetTabs() []*AudioPostScriptTab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

type AudioPostScript struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sections             []string `protobuf:"bytes,2,rep,name=sections,proto3" json:"sections,omitempty"`
	TabId                int32    `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabTitle             string   `protobuf:"bytes,4,opt,name=tab_title,json=tabTitle,proto3" json:"tab_title,omitempty"`
	Title                string   `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostScript) Reset()         { *m = AudioPostScript{} }
func (m *AudioPostScript) String() string { return proto.CompactTextString(m) }
func (*AudioPostScript) ProtoMessage()    {}
func (*AudioPostScript) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{9}
}
func (m *AudioPostScript) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostScript.Unmarshal(m, b)
}
func (m *AudioPostScript) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostScript.Marshal(b, m, deterministic)
}
func (dst *AudioPostScript) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostScript.Merge(dst, src)
}
func (m *AudioPostScript) XXX_Size() int {
	return xxx_messageInfo_AudioPostScript.Size(m)
}
func (m *AudioPostScript) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostScript.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostScript proto.InternalMessageInfo

func (m *AudioPostScript) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AudioPostScript) GetSections() []string {
	if m != nil {
		return m.Sections
	}
	return nil
}

func (m *AudioPostScript) GetTabId() int32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *AudioPostScript) GetTabTitle() string {
	if m != nil {
		return m.TabTitle
	}
	return ""
}

func (m *AudioPostScript) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type InsertAudioPostScriptReq struct {
	Sections             []string `protobuf:"bytes,1,rep,name=sections,proto3" json:"sections,omitempty"`
	TabId                int32    `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertAudioPostScriptReq) Reset()         { *m = InsertAudioPostScriptReq{} }
func (m *InsertAudioPostScriptReq) String() string { return proto.CompactTextString(m) }
func (*InsertAudioPostScriptReq) ProtoMessage()    {}
func (*InsertAudioPostScriptReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{10}
}
func (m *InsertAudioPostScriptReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertAudioPostScriptReq.Unmarshal(m, b)
}
func (m *InsertAudioPostScriptReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertAudioPostScriptReq.Marshal(b, m, deterministic)
}
func (dst *InsertAudioPostScriptReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertAudioPostScriptReq.Merge(dst, src)
}
func (m *InsertAudioPostScriptReq) XXX_Size() int {
	return xxx_messageInfo_InsertAudioPostScriptReq.Size(m)
}
func (m *InsertAudioPostScriptReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertAudioPostScriptReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsertAudioPostScriptReq proto.InternalMessageInfo

func (m *InsertAudioPostScriptReq) GetSections() []string {
	if m != nil {
		return m.Sections
	}
	return nil
}

func (m *InsertAudioPostScriptReq) GetTabId() int32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *InsertAudioPostScriptReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type InsertAudioPostScriptResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertAudioPostScriptResp) Reset()         { *m = InsertAudioPostScriptResp{} }
func (m *InsertAudioPostScriptResp) String() string { return proto.CompactTextString(m) }
func (*InsertAudioPostScriptResp) ProtoMessage()    {}
func (*InsertAudioPostScriptResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{11}
}
func (m *InsertAudioPostScriptResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertAudioPostScriptResp.Unmarshal(m, b)
}
func (m *InsertAudioPostScriptResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertAudioPostScriptResp.Marshal(b, m, deterministic)
}
func (dst *InsertAudioPostScriptResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertAudioPostScriptResp.Merge(dst, src)
}
func (m *InsertAudioPostScriptResp) XXX_Size() int {
	return xxx_messageInfo_InsertAudioPostScriptResp.Size(m)
}
func (m *InsertAudioPostScriptResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertAudioPostScriptResp.DiscardUnknown(m)
}

var xxx_messageInfo_InsertAudioPostScriptResp proto.InternalMessageInfo

type UpdateAudioPostScriptReq struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sections             []string `protobuf:"bytes,2,rep,name=sections,proto3" json:"sections,omitempty"`
	TabId                int32    `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Title                string   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAudioPostScriptReq) Reset()         { *m = UpdateAudioPostScriptReq{} }
func (m *UpdateAudioPostScriptReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAudioPostScriptReq) ProtoMessage()    {}
func (*UpdateAudioPostScriptReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{12}
}
func (m *UpdateAudioPostScriptReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAudioPostScriptReq.Unmarshal(m, b)
}
func (m *UpdateAudioPostScriptReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAudioPostScriptReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAudioPostScriptReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAudioPostScriptReq.Merge(dst, src)
}
func (m *UpdateAudioPostScriptReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAudioPostScriptReq.Size(m)
}
func (m *UpdateAudioPostScriptReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAudioPostScriptReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAudioPostScriptReq proto.InternalMessageInfo

func (m *UpdateAudioPostScriptReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateAudioPostScriptReq) GetSections() []string {
	if m != nil {
		return m.Sections
	}
	return nil
}

func (m *UpdateAudioPostScriptReq) GetTabId() int32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UpdateAudioPostScriptReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type UpdateAudioPostScriptResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAudioPostScriptResp) Reset()         { *m = UpdateAudioPostScriptResp{} }
func (m *UpdateAudioPostScriptResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAudioPostScriptResp) ProtoMessage()    {}
func (*UpdateAudioPostScriptResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{13}
}
func (m *UpdateAudioPostScriptResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAudioPostScriptResp.Unmarshal(m, b)
}
func (m *UpdateAudioPostScriptResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAudioPostScriptResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAudioPostScriptResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAudioPostScriptResp.Merge(dst, src)
}
func (m *UpdateAudioPostScriptResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAudioPostScriptResp.Size(m)
}
func (m *UpdateAudioPostScriptResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAudioPostScriptResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAudioPostScriptResp proto.InternalMessageInfo

type DeleteAudioPostScriptReq struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAudioPostScriptReq) Reset()         { *m = DeleteAudioPostScriptReq{} }
func (m *DeleteAudioPostScriptReq) String() string { return proto.CompactTextString(m) }
func (*DeleteAudioPostScriptReq) ProtoMessage()    {}
func (*DeleteAudioPostScriptReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{14}
}
func (m *DeleteAudioPostScriptReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAudioPostScriptReq.Unmarshal(m, b)
}
func (m *DeleteAudioPostScriptReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAudioPostScriptReq.Marshal(b, m, deterministic)
}
func (dst *DeleteAudioPostScriptReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAudioPostScriptReq.Merge(dst, src)
}
func (m *DeleteAudioPostScriptReq) XXX_Size() int {
	return xxx_messageInfo_DeleteAudioPostScriptReq.Size(m)
}
func (m *DeleteAudioPostScriptReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAudioPostScriptReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAudioPostScriptReq proto.InternalMessageInfo

func (m *DeleteAudioPostScriptReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DeleteAudioPostScriptResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteAudioPostScriptResp) Reset()         { *m = DeleteAudioPostScriptResp{} }
func (m *DeleteAudioPostScriptResp) String() string { return proto.CompactTextString(m) }
func (*DeleteAudioPostScriptResp) ProtoMessage()    {}
func (*DeleteAudioPostScriptResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{15}
}
func (m *DeleteAudioPostScriptResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteAudioPostScriptResp.Unmarshal(m, b)
}
func (m *DeleteAudioPostScriptResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteAudioPostScriptResp.Marshal(b, m, deterministic)
}
func (dst *DeleteAudioPostScriptResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAudioPostScriptResp.Merge(dst, src)
}
func (m *DeleteAudioPostScriptResp) XXX_Size() int {
	return xxx_messageInfo_DeleteAudioPostScriptResp.Size(m)
}
func (m *DeleteAudioPostScriptResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAudioPostScriptResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAudioPostScriptResp proto.InternalMessageInfo

type AudioPostScriptsReq struct {
	TabIds               []int32  `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	Offset               int32    `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                int32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostScriptsReq) Reset()         { *m = AudioPostScriptsReq{} }
func (m *AudioPostScriptsReq) String() string { return proto.CompactTextString(m) }
func (*AudioPostScriptsReq) ProtoMessage()    {}
func (*AudioPostScriptsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{16}
}
func (m *AudioPostScriptsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostScriptsReq.Unmarshal(m, b)
}
func (m *AudioPostScriptsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostScriptsReq.Marshal(b, m, deterministic)
}
func (dst *AudioPostScriptsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostScriptsReq.Merge(dst, src)
}
func (m *AudioPostScriptsReq) XXX_Size() int {
	return xxx_messageInfo_AudioPostScriptsReq.Size(m)
}
func (m *AudioPostScriptsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostScriptsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostScriptsReq proto.InternalMessageInfo

func (m *AudioPostScriptsReq) GetTabIds() []int32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *AudioPostScriptsReq) GetOffset() int32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *AudioPostScriptsReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type AudioPostScriptsResp struct {
	Scripts              []*AudioPostScript `protobuf:"bytes,1,rep,name=scripts,proto3" json:"scripts,omitempty"`
	Cnt                  int32              `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AudioPostScriptsResp) Reset()         { *m = AudioPostScriptsResp{} }
func (m *AudioPostScriptsResp) String() string { return proto.CompactTextString(m) }
func (*AudioPostScriptsResp) ProtoMessage()    {}
func (*AudioPostScriptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{17}
}
func (m *AudioPostScriptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostScriptsResp.Unmarshal(m, b)
}
func (m *AudioPostScriptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostScriptsResp.Marshal(b, m, deterministic)
}
func (dst *AudioPostScriptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostScriptsResp.Merge(dst, src)
}
func (m *AudioPostScriptsResp) XXX_Size() int {
	return xxx_messageInfo_AudioPostScriptsResp.Size(m)
}
func (m *AudioPostScriptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostScriptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostScriptsResp proto.InternalMessageInfo

func (m *AudioPostScriptsResp) GetScripts() []*AudioPostScript {
	if m != nil {
		return m.Scripts
	}
	return nil
}

func (m *AudioPostScriptsResp) GetCnt() int32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type RandomAudioPostScriptsReq struct {
	Limit                int32    `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RandomAudioPostScriptsReq) Reset()         { *m = RandomAudioPostScriptsReq{} }
func (m *RandomAudioPostScriptsReq) String() string { return proto.CompactTextString(m) }
func (*RandomAudioPostScriptsReq) ProtoMessage()    {}
func (*RandomAudioPostScriptsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{18}
}
func (m *RandomAudioPostScriptsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RandomAudioPostScriptsReq.Unmarshal(m, b)
}
func (m *RandomAudioPostScriptsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RandomAudioPostScriptsReq.Marshal(b, m, deterministic)
}
func (dst *RandomAudioPostScriptsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RandomAudioPostScriptsReq.Merge(dst, src)
}
func (m *RandomAudioPostScriptsReq) XXX_Size() int {
	return xxx_messageInfo_RandomAudioPostScriptsReq.Size(m)
}
func (m *RandomAudioPostScriptsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RandomAudioPostScriptsReq.DiscardUnknown(m)
}

var xxx_messageInfo_RandomAudioPostScriptsReq proto.InternalMessageInfo

func (m *RandomAudioPostScriptsReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type RandomAudioPostScriptsResp struct {
	Scripts              []*AudioPostScript `protobuf:"bytes,1,rep,name=scripts,proto3" json:"scripts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *RandomAudioPostScriptsResp) Reset()         { *m = RandomAudioPostScriptsResp{} }
func (m *RandomAudioPostScriptsResp) String() string { return proto.CompactTextString(m) }
func (*RandomAudioPostScriptsResp) ProtoMessage()    {}
func (*RandomAudioPostScriptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{19}
}
func (m *RandomAudioPostScriptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RandomAudioPostScriptsResp.Unmarshal(m, b)
}
func (m *RandomAudioPostScriptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RandomAudioPostScriptsResp.Marshal(b, m, deterministic)
}
func (dst *RandomAudioPostScriptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RandomAudioPostScriptsResp.Merge(dst, src)
}
func (m *RandomAudioPostScriptsResp) XXX_Size() int {
	return xxx_messageInfo_RandomAudioPostScriptsResp.Size(m)
}
func (m *RandomAudioPostScriptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RandomAudioPostScriptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_RandomAudioPostScriptsResp proto.InternalMessageInfo

func (m *RandomAudioPostScriptsResp) GetScripts() []*AudioPostScript {
	if m != nil {
		return m.Scripts
	}
	return nil
}

type AudioPostScriptsForMobileReq struct {
	TabIds               []int32  `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	Offset               int32    `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                int32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostScriptsForMobileReq) Reset()         { *m = AudioPostScriptsForMobileReq{} }
func (m *AudioPostScriptsForMobileReq) String() string { return proto.CompactTextString(m) }
func (*AudioPostScriptsForMobileReq) ProtoMessage()    {}
func (*AudioPostScriptsForMobileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{20}
}
func (m *AudioPostScriptsForMobileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostScriptsForMobileReq.Unmarshal(m, b)
}
func (m *AudioPostScriptsForMobileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostScriptsForMobileReq.Marshal(b, m, deterministic)
}
func (dst *AudioPostScriptsForMobileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostScriptsForMobileReq.Merge(dst, src)
}
func (m *AudioPostScriptsForMobileReq) XXX_Size() int {
	return xxx_messageInfo_AudioPostScriptsForMobileReq.Size(m)
}
func (m *AudioPostScriptsForMobileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostScriptsForMobileReq.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostScriptsForMobileReq proto.InternalMessageInfo

func (m *AudioPostScriptsForMobileReq) GetTabIds() []int32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *AudioPostScriptsForMobileReq) GetOffset() int32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *AudioPostScriptsForMobileReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *AudioPostScriptsForMobileReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type AudioPostScriptsForMobileResp struct {
	Scripts              []*AudioPostScript `protobuf:"bytes,1,rep,name=scripts,proto3" json:"scripts,omitempty"`
	Uid                  uint32             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AudioPostScriptsForMobileResp) Reset()         { *m = AudioPostScriptsForMobileResp{} }
func (m *AudioPostScriptsForMobileResp) String() string { return proto.CompactTextString(m) }
func (*AudioPostScriptsForMobileResp) ProtoMessage()    {}
func (*AudioPostScriptsForMobileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{21}
}
func (m *AudioPostScriptsForMobileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostScriptsForMobileResp.Unmarshal(m, b)
}
func (m *AudioPostScriptsForMobileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostScriptsForMobileResp.Marshal(b, m, deterministic)
}
func (dst *AudioPostScriptsForMobileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostScriptsForMobileResp.Merge(dst, src)
}
func (m *AudioPostScriptsForMobileResp) XXX_Size() int {
	return xxx_messageInfo_AudioPostScriptsForMobileResp.Size(m)
}
func (m *AudioPostScriptsForMobileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostScriptsForMobileResp.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostScriptsForMobileResp proto.InternalMessageInfo

func (m *AudioPostScriptsForMobileResp) GetScripts() []*AudioPostScript {
	if m != nil {
		return m.Scripts
	}
	return nil
}

func (m *AudioPostScriptsForMobileResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelRandListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelRandListReq) Reset()         { *m = DelRandListReq{} }
func (m *DelRandListReq) String() string { return proto.CompactTextString(m) }
func (*DelRandListReq) ProtoMessage()    {}
func (*DelRandListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{22}
}
func (m *DelRandListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelRandListReq.Unmarshal(m, b)
}
func (m *DelRandListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelRandListReq.Marshal(b, m, deterministic)
}
func (dst *DelRandListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelRandListReq.Merge(dst, src)
}
func (m *DelRandListReq) XXX_Size() int {
	return xxx_messageInfo_DelRandListReq.Size(m)
}
func (m *DelRandListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelRandListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelRandListReq proto.InternalMessageInfo

func (m *DelRandListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelRandListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelRandListResp) Reset()         { *m = DelRandListResp{} }
func (m *DelRandListResp) String() string { return proto.CompactTextString(m) }
func (*DelRandListResp) ProtoMessage()    {}
func (*DelRandListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{23}
}
func (m *DelRandListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelRandListResp.Unmarshal(m, b)
}
func (m *DelRandListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelRandListResp.Marshal(b, m, deterministic)
}
func (dst *DelRandListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelRandListResp.Merge(dst, src)
}
func (m *DelRandListResp) XXX_Size() int {
	return xxx_messageInfo_DelRandListResp.Size(m)
}
func (m *DelRandListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelRandListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelRandListResp proto.InternalMessageInfo

type AudioPostImage struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Id                   int32    `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostImage) Reset()         { *m = AudioPostImage{} }
func (m *AudioPostImage) String() string { return proto.CompactTextString(m) }
func (*AudioPostImage) ProtoMessage()    {}
func (*AudioPostImage) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{24}
}
func (m *AudioPostImage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostImage.Unmarshal(m, b)
}
func (m *AudioPostImage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostImage.Marshal(b, m, deterministic)
}
func (dst *AudioPostImage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostImage.Merge(dst, src)
}
func (m *AudioPostImage) XXX_Size() int {
	return xxx_messageInfo_AudioPostImage.Size(m)
}
func (m *AudioPostImage) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostImage.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostImage proto.InternalMessageInfo

func (m *AudioPostImage) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *AudioPostImage) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type RandomAudioPostImagesReq struct {
	Limit                int32    `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RandomAudioPostImagesReq) Reset()         { *m = RandomAudioPostImagesReq{} }
func (m *RandomAudioPostImagesReq) String() string { return proto.CompactTextString(m) }
func (*RandomAudioPostImagesReq) ProtoMessage()    {}
func (*RandomAudioPostImagesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{25}
}
func (m *RandomAudioPostImagesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RandomAudioPostImagesReq.Unmarshal(m, b)
}
func (m *RandomAudioPostImagesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RandomAudioPostImagesReq.Marshal(b, m, deterministic)
}
func (dst *RandomAudioPostImagesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RandomAudioPostImagesReq.Merge(dst, src)
}
func (m *RandomAudioPostImagesReq) XXX_Size() int {
	return xxx_messageInfo_RandomAudioPostImagesReq.Size(m)
}
func (m *RandomAudioPostImagesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RandomAudioPostImagesReq.DiscardUnknown(m)
}

var xxx_messageInfo_RandomAudioPostImagesReq proto.InternalMessageInfo

func (m *RandomAudioPostImagesReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type RandomAudioPostImagesResp struct {
	Images               []*AudioPostImage `protobuf:"bytes,1,rep,name=images,proto3" json:"images,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RandomAudioPostImagesResp) Reset()         { *m = RandomAudioPostImagesResp{} }
func (m *RandomAudioPostImagesResp) String() string { return proto.CompactTextString(m) }
func (*RandomAudioPostImagesResp) ProtoMessage()    {}
func (*RandomAudioPostImagesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{26}
}
func (m *RandomAudioPostImagesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RandomAudioPostImagesResp.Unmarshal(m, b)
}
func (m *RandomAudioPostImagesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RandomAudioPostImagesResp.Marshal(b, m, deterministic)
}
func (dst *RandomAudioPostImagesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RandomAudioPostImagesResp.Merge(dst, src)
}
func (m *RandomAudioPostImagesResp) XXX_Size() int {
	return xxx_messageInfo_RandomAudioPostImagesResp.Size(m)
}
func (m *RandomAudioPostImagesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RandomAudioPostImagesResp.DiscardUnknown(m)
}

var xxx_messageInfo_RandomAudioPostImagesResp proto.InternalMessageInfo

func (m *RandomAudioPostImagesResp) GetImages() []*AudioPostImage {
	if m != nil {
		return m.Images
	}
	return nil
}

type AudioPostImagesReq struct {
	Offset               int32    `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                int32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostImagesReq) Reset()         { *m = AudioPostImagesReq{} }
func (m *AudioPostImagesReq) String() string { return proto.CompactTextString(m) }
func (*AudioPostImagesReq) ProtoMessage()    {}
func (*AudioPostImagesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{27}
}
func (m *AudioPostImagesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostImagesReq.Unmarshal(m, b)
}
func (m *AudioPostImagesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostImagesReq.Marshal(b, m, deterministic)
}
func (dst *AudioPostImagesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostImagesReq.Merge(dst, src)
}
func (m *AudioPostImagesReq) XXX_Size() int {
	return xxx_messageInfo_AudioPostImagesReq.Size(m)
}
func (m *AudioPostImagesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostImagesReq.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostImagesReq proto.InternalMessageInfo

func (m *AudioPostImagesReq) GetOffset() int32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *AudioPostImagesReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type AudioPostImagesResp struct {
	Images               []*AudioPostImage `protobuf:"bytes,1,rep,name=images,proto3" json:"images,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AudioPostImagesResp) Reset()         { *m = AudioPostImagesResp{} }
func (m *AudioPostImagesResp) String() string { return proto.CompactTextString(m) }
func (*AudioPostImagesResp) ProtoMessage()    {}
func (*AudioPostImagesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{28}
}
func (m *AudioPostImagesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostImagesResp.Unmarshal(m, b)
}
func (m *AudioPostImagesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostImagesResp.Marshal(b, m, deterministic)
}
func (dst *AudioPostImagesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostImagesResp.Merge(dst, src)
}
func (m *AudioPostImagesResp) XXX_Size() int {
	return xxx_messageInfo_AudioPostImagesResp.Size(m)
}
func (m *AudioPostImagesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostImagesResp.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostImagesResp proto.InternalMessageInfo

func (m *AudioPostImagesResp) GetImages() []*AudioPostImage {
	if m != nil {
		return m.Images
	}
	return nil
}

type AudioPostMusic struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Singer               string   `protobuf:"bytes,3,opt,name=singer,proto3" json:"singer,omitempty"`
	Id                   int32    `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	Abstract             string   `protobuf:"bytes,5,opt,name=abstract,proto3" json:"abstract,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostMusic) Reset()         { *m = AudioPostMusic{} }
func (m *AudioPostMusic) String() string { return proto.CompactTextString(m) }
func (*AudioPostMusic) ProtoMessage()    {}
func (*AudioPostMusic) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{29}
}
func (m *AudioPostMusic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostMusic.Unmarshal(m, b)
}
func (m *AudioPostMusic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostMusic.Marshal(b, m, deterministic)
}
func (dst *AudioPostMusic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostMusic.Merge(dst, src)
}
func (m *AudioPostMusic) XXX_Size() int {
	return xxx_messageInfo_AudioPostMusic.Size(m)
}
func (m *AudioPostMusic) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostMusic.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostMusic proto.InternalMessageInfo

func (m *AudioPostMusic) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *AudioPostMusic) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AudioPostMusic) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *AudioPostMusic) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AudioPostMusic) GetAbstract() string {
	if m != nil {
		return m.Abstract
	}
	return ""
}

type RandomAudioPostMusicsReq struct {
	Limit                int32    `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RandomAudioPostMusicsReq) Reset()         { *m = RandomAudioPostMusicsReq{} }
func (m *RandomAudioPostMusicsReq) String() string { return proto.CompactTextString(m) }
func (*RandomAudioPostMusicsReq) ProtoMessage()    {}
func (*RandomAudioPostMusicsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{30}
}
func (m *RandomAudioPostMusicsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RandomAudioPostMusicsReq.Unmarshal(m, b)
}
func (m *RandomAudioPostMusicsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RandomAudioPostMusicsReq.Marshal(b, m, deterministic)
}
func (dst *RandomAudioPostMusicsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RandomAudioPostMusicsReq.Merge(dst, src)
}
func (m *RandomAudioPostMusicsReq) XXX_Size() int {
	return xxx_messageInfo_RandomAudioPostMusicsReq.Size(m)
}
func (m *RandomAudioPostMusicsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RandomAudioPostMusicsReq.DiscardUnknown(m)
}

var xxx_messageInfo_RandomAudioPostMusicsReq proto.InternalMessageInfo

func (m *RandomAudioPostMusicsReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type RandomAudioPostMusicsResp struct {
	Musics               []*AudioPostMusic `protobuf:"bytes,1,rep,name=musics,proto3" json:"musics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RandomAudioPostMusicsResp) Reset()         { *m = RandomAudioPostMusicsResp{} }
func (m *RandomAudioPostMusicsResp) String() string { return proto.CompactTextString(m) }
func (*RandomAudioPostMusicsResp) ProtoMessage()    {}
func (*RandomAudioPostMusicsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{31}
}
func (m *RandomAudioPostMusicsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RandomAudioPostMusicsResp.Unmarshal(m, b)
}
func (m *RandomAudioPostMusicsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RandomAudioPostMusicsResp.Marshal(b, m, deterministic)
}
func (dst *RandomAudioPostMusicsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RandomAudioPostMusicsResp.Merge(dst, src)
}
func (m *RandomAudioPostMusicsResp) XXX_Size() int {
	return xxx_messageInfo_RandomAudioPostMusicsResp.Size(m)
}
func (m *RandomAudioPostMusicsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RandomAudioPostMusicsResp.DiscardUnknown(m)
}

var xxx_messageInfo_RandomAudioPostMusicsResp proto.InternalMessageInfo

func (m *RandomAudioPostMusicsResp) GetMusics() []*AudioPostMusic {
	if m != nil {
		return m.Musics
	}
	return nil
}

type AudioPostMusicsReq struct {
	TabIds               []int32  `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	Offset               int32    `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                int32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostMusicsReq) Reset()         { *m = AudioPostMusicsReq{} }
func (m *AudioPostMusicsReq) String() string { return proto.CompactTextString(m) }
func (*AudioPostMusicsReq) ProtoMessage()    {}
func (*AudioPostMusicsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{32}
}
func (m *AudioPostMusicsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostMusicsReq.Unmarshal(m, b)
}
func (m *AudioPostMusicsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostMusicsReq.Marshal(b, m, deterministic)
}
func (dst *AudioPostMusicsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostMusicsReq.Merge(dst, src)
}
func (m *AudioPostMusicsReq) XXX_Size() int {
	return xxx_messageInfo_AudioPostMusicsReq.Size(m)
}
func (m *AudioPostMusicsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostMusicsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostMusicsReq proto.InternalMessageInfo

func (m *AudioPostMusicsReq) GetTabIds() []int32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *AudioPostMusicsReq) GetOffset() int32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *AudioPostMusicsReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type AudioPostMusicsResp struct {
	Musics               []*AudioPostMusic `protobuf:"bytes,1,rep,name=musics,proto3" json:"musics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AudioPostMusicsResp) Reset()         { *m = AudioPostMusicsResp{} }
func (m *AudioPostMusicsResp) String() string { return proto.CompactTextString(m) }
func (*AudioPostMusicsResp) ProtoMessage()    {}
func (*AudioPostMusicsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{33}
}
func (m *AudioPostMusicsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostMusicsResp.Unmarshal(m, b)
}
func (m *AudioPostMusicsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostMusicsResp.Marshal(b, m, deterministic)
}
func (dst *AudioPostMusicsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostMusicsResp.Merge(dst, src)
}
func (m *AudioPostMusicsResp) XXX_Size() int {
	return xxx_messageInfo_AudioPostMusicsResp.Size(m)
}
func (m *AudioPostMusicsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostMusicsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostMusicsResp proto.InternalMessageInfo

func (m *AudioPostMusicsResp) GetMusics() []*AudioPostMusic {
	if m != nil {
		return m.Musics
	}
	return nil
}

type AudioPostMusicTab struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostMusicTab) Reset()         { *m = AudioPostMusicTab{} }
func (m *AudioPostMusicTab) String() string { return proto.CompactTextString(m) }
func (*AudioPostMusicTab) ProtoMessage()    {}
func (*AudioPostMusicTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{34}
}
func (m *AudioPostMusicTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostMusicTab.Unmarshal(m, b)
}
func (m *AudioPostMusicTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostMusicTab.Marshal(b, m, deterministic)
}
func (dst *AudioPostMusicTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostMusicTab.Merge(dst, src)
}
func (m *AudioPostMusicTab) XXX_Size() int {
	return xxx_messageInfo_AudioPostMusicTab.Size(m)
}
func (m *AudioPostMusicTab) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostMusicTab.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostMusicTab proto.InternalMessageInfo

func (m *AudioPostMusicTab) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AudioPostMusicTab) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type AudioPostMusicTabsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioPostMusicTabsReq) Reset()         { *m = AudioPostMusicTabsReq{} }
func (m *AudioPostMusicTabsReq) String() string { return proto.CompactTextString(m) }
func (*AudioPostMusicTabsReq) ProtoMessage()    {}
func (*AudioPostMusicTabsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{35}
}
func (m *AudioPostMusicTabsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostMusicTabsReq.Unmarshal(m, b)
}
func (m *AudioPostMusicTabsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostMusicTabsReq.Marshal(b, m, deterministic)
}
func (dst *AudioPostMusicTabsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostMusicTabsReq.Merge(dst, src)
}
func (m *AudioPostMusicTabsReq) XXX_Size() int {
	return xxx_messageInfo_AudioPostMusicTabsReq.Size(m)
}
func (m *AudioPostMusicTabsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostMusicTabsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostMusicTabsReq proto.InternalMessageInfo

type AudioPostMusicTabsResp struct {
	Tabs                 []*AudioPostMusicTab `protobuf:"bytes,1,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AudioPostMusicTabsResp) Reset()         { *m = AudioPostMusicTabsResp{} }
func (m *AudioPostMusicTabsResp) String() string { return proto.CompactTextString(m) }
func (*AudioPostMusicTabsResp) ProtoMessage()    {}
func (*AudioPostMusicTabsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{36}
}
func (m *AudioPostMusicTabsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioPostMusicTabsResp.Unmarshal(m, b)
}
func (m *AudioPostMusicTabsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioPostMusicTabsResp.Marshal(b, m, deterministic)
}
func (dst *AudioPostMusicTabsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioPostMusicTabsResp.Merge(dst, src)
}
func (m *AudioPostMusicTabsResp) XXX_Size() int {
	return xxx_messageInfo_AudioPostMusicTabsResp.Size(m)
}
func (m *AudioPostMusicTabsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioPostMusicTabsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AudioPostMusicTabsResp proto.InternalMessageInfo

func (m *AudioPostMusicTabsResp) GetTabs() []*AudioPostMusicTab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

type XunfeiSignatureReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *XunfeiSignatureReq) Reset()         { *m = XunfeiSignatureReq{} }
func (m *XunfeiSignatureReq) String() string { return proto.CompactTextString(m) }
func (*XunfeiSignatureReq) ProtoMessage()    {}
func (*XunfeiSignatureReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{37}
}
func (m *XunfeiSignatureReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_XunfeiSignatureReq.Unmarshal(m, b)
}
func (m *XunfeiSignatureReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_XunfeiSignatureReq.Marshal(b, m, deterministic)
}
func (dst *XunfeiSignatureReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_XunfeiSignatureReq.Merge(dst, src)
}
func (m *XunfeiSignatureReq) XXX_Size() int {
	return xxx_messageInfo_XunfeiSignatureReq.Size(m)
}
func (m *XunfeiSignatureReq) XXX_DiscardUnknown() {
	xxx_messageInfo_XunfeiSignatureReq.DiscardUnknown(m)
}

var xxx_messageInfo_XunfeiSignatureReq proto.InternalMessageInfo

type XunfeiSignatureResp struct {
	AuthString           string   `protobuf:"bytes,1,opt,name=auth_string,json=authString,proto3" json:"auth_string,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *XunfeiSignatureResp) Reset()         { *m = XunfeiSignatureResp{} }
func (m *XunfeiSignatureResp) String() string { return proto.CompactTextString(m) }
func (*XunfeiSignatureResp) ProtoMessage()    {}
func (*XunfeiSignatureResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_post_9488c53929d2f122, []int{38}
}
func (m *XunfeiSignatureResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_XunfeiSignatureResp.Unmarshal(m, b)
}
func (m *XunfeiSignatureResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_XunfeiSignatureResp.Marshal(b, m, deterministic)
}
func (dst *XunfeiSignatureResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_XunfeiSignatureResp.Merge(dst, src)
}
func (m *XunfeiSignatureResp) XXX_Size() int {
	return xxx_messageInfo_XunfeiSignatureResp.Size(m)
}
func (m *XunfeiSignatureResp) XXX_DiscardUnknown() {
	xxx_messageInfo_XunfeiSignatureResp.DiscardUnknown(m)
}

var xxx_messageInfo_XunfeiSignatureResp proto.InternalMessageInfo

func (m *XunfeiSignatureResp) GetAuthString() string {
	if m != nil {
		return m.AuthString
	}
	return ""
}

func init() {
	proto.RegisterType((*AudioPostScriptTab)(nil), "audio_post.AudioPostScriptTab")
	proto.RegisterType((*InsertAudioPostScriptTabReq)(nil), "audio_post.InsertAudioPostScriptTabReq")
	proto.RegisterType((*InsertAudioPostScriptTabResp)(nil), "audio_post.InsertAudioPostScriptTabResp")
	proto.RegisterType((*UpdateAudioPostScriptTabReq)(nil), "audio_post.UpdateAudioPostScriptTabReq")
	proto.RegisterType((*UpdateAudioPostScriptTabResp)(nil), "audio_post.UpdateAudioPostScriptTabResp")
	proto.RegisterType((*DeleteAudioPostScriptTabReq)(nil), "audio_post.DeleteAudioPostScriptTabReq")
	proto.RegisterType((*DeleteAudioPostScriptTabResp)(nil), "audio_post.DeleteAudioPostScriptTabResp")
	proto.RegisterType((*AudioPostScriptTabsReq)(nil), "audio_post.AudioPostScriptTabsReq")
	proto.RegisterType((*AudioPostScriptTabsResp)(nil), "audio_post.AudioPostScriptTabsResp")
	proto.RegisterType((*AudioPostScript)(nil), "audio_post.AudioPostScript")
	proto.RegisterType((*InsertAudioPostScriptReq)(nil), "audio_post.InsertAudioPostScriptReq")
	proto.RegisterType((*InsertAudioPostScriptResp)(nil), "audio_post.InsertAudioPostScriptResp")
	proto.RegisterType((*UpdateAudioPostScriptReq)(nil), "audio_post.UpdateAudioPostScriptReq")
	proto.RegisterType((*UpdateAudioPostScriptResp)(nil), "audio_post.UpdateAudioPostScriptResp")
	proto.RegisterType((*DeleteAudioPostScriptReq)(nil), "audio_post.DeleteAudioPostScriptReq")
	proto.RegisterType((*DeleteAudioPostScriptResp)(nil), "audio_post.DeleteAudioPostScriptResp")
	proto.RegisterType((*AudioPostScriptsReq)(nil), "audio_post.AudioPostScriptsReq")
	proto.RegisterType((*AudioPostScriptsResp)(nil), "audio_post.AudioPostScriptsResp")
	proto.RegisterType((*RandomAudioPostScriptsReq)(nil), "audio_post.RandomAudioPostScriptsReq")
	proto.RegisterType((*RandomAudioPostScriptsResp)(nil), "audio_post.RandomAudioPostScriptsResp")
	proto.RegisterType((*AudioPostScriptsForMobileReq)(nil), "audio_post.AudioPostScriptsForMobileReq")
	proto.RegisterType((*AudioPostScriptsForMobileResp)(nil), "audio_post.AudioPostScriptsForMobileResp")
	proto.RegisterType((*DelRandListReq)(nil), "audio_post.DelRandListReq")
	proto.RegisterType((*DelRandListResp)(nil), "audio_post.DelRandListResp")
	proto.RegisterType((*AudioPostImage)(nil), "audio_post.AudioPostImage")
	proto.RegisterType((*RandomAudioPostImagesReq)(nil), "audio_post.RandomAudioPostImagesReq")
	proto.RegisterType((*RandomAudioPostImagesResp)(nil), "audio_post.RandomAudioPostImagesResp")
	proto.RegisterType((*AudioPostImagesReq)(nil), "audio_post.AudioPostImagesReq")
	proto.RegisterType((*AudioPostImagesResp)(nil), "audio_post.AudioPostImagesResp")
	proto.RegisterType((*AudioPostMusic)(nil), "audio_post.AudioPostMusic")
	proto.RegisterType((*RandomAudioPostMusicsReq)(nil), "audio_post.RandomAudioPostMusicsReq")
	proto.RegisterType((*RandomAudioPostMusicsResp)(nil), "audio_post.RandomAudioPostMusicsResp")
	proto.RegisterType((*AudioPostMusicsReq)(nil), "audio_post.AudioPostMusicsReq")
	proto.RegisterType((*AudioPostMusicsResp)(nil), "audio_post.AudioPostMusicsResp")
	proto.RegisterType((*AudioPostMusicTab)(nil), "audio_post.AudioPostMusicTab")
	proto.RegisterType((*AudioPostMusicTabsReq)(nil), "audio_post.AudioPostMusicTabsReq")
	proto.RegisterType((*AudioPostMusicTabsResp)(nil), "audio_post.AudioPostMusicTabsResp")
	proto.RegisterType((*XunfeiSignatureReq)(nil), "audio_post.XunfeiSignatureReq")
	proto.RegisterType((*XunfeiSignatureResp)(nil), "audio_post.XunfeiSignatureResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AudioPostClient is the client API for AudioPost service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AudioPostClient interface {
	// 声音动态台本分类
	InsertAudioPostScriptTab(ctx context.Context, in *InsertAudioPostScriptTabReq, opts ...grpc.CallOption) (*InsertAudioPostScriptTabResp, error)
	UpdateAudioPostScriptTab(ctx context.Context, in *UpdateAudioPostScriptTabReq, opts ...grpc.CallOption) (*UpdateAudioPostScriptTabResp, error)
	DeleteAudioPostScriptTab(ctx context.Context, in *DeleteAudioPostScriptTabReq, opts ...grpc.CallOption) (*DeleteAudioPostScriptTabResp, error)
	AudioPostScriptTabs(ctx context.Context, in *AudioPostScriptTabsReq, opts ...grpc.CallOption) (*AudioPostScriptTabsResp, error)
	// 声音动态台本
	InsertAudioPostScript(ctx context.Context, in *InsertAudioPostScriptReq, opts ...grpc.CallOption) (*InsertAudioPostScriptResp, error)
	UpdateAudioPostScript(ctx context.Context, in *UpdateAudioPostScriptReq, opts ...grpc.CallOption) (*UpdateAudioPostScriptResp, error)
	DeleteAudioPostScript(ctx context.Context, in *DeleteAudioPostScriptReq, opts ...grpc.CallOption) (*DeleteAudioPostScriptResp, error)
	AudioPostScripts(ctx context.Context, in *AudioPostScriptsReq, opts ...grpc.CallOption) (*AudioPostScriptsResp, error)
	RandomAudioPostScripts(ctx context.Context, in *RandomAudioPostScriptsReq, opts ...grpc.CallOption) (*RandomAudioPostScriptsResp, error)
	AudioPostScriptsForMobile(ctx context.Context, in *AudioPostScriptsForMobileReq, opts ...grpc.CallOption) (*AudioPostScriptsForMobileResp, error)
	DelRandList(ctx context.Context, in *DelRandListReq, opts ...grpc.CallOption) (*DelRandListResp, error)
	// 声音动态背景图片
	RandomAudioPostImages(ctx context.Context, in *RandomAudioPostImagesReq, opts ...grpc.CallOption) (*RandomAudioPostImagesResp, error)
	AudioPostImages(ctx context.Context, in *AudioPostImagesReq, opts ...grpc.CallOption) (*AudioPostImagesResp, error)
	// 声音动态背景音乐
	RandomAudioPostMusics(ctx context.Context, in *RandomAudioPostMusicsReq, opts ...grpc.CallOption) (*RandomAudioPostMusicsResp, error)
	AudioPostMusics(ctx context.Context, in *AudioPostMusicsReq, opts ...grpc.CallOption) (*AudioPostMusicsResp, error)
	AudioPostMusicTabs(ctx context.Context, in *AudioPostMusicTabsReq, opts ...grpc.CallOption) (*AudioPostMusicTabsResp, error)
	// 科大讯飞
	XunfeiSignature(ctx context.Context, in *XunfeiSignatureReq, opts ...grpc.CallOption) (*XunfeiSignatureResp, error)
}

type audioPostClient struct {
	cc *grpc.ClientConn
}

func NewAudioPostClient(cc *grpc.ClientConn) AudioPostClient {
	return &audioPostClient{cc}
}

func (c *audioPostClient) InsertAudioPostScriptTab(ctx context.Context, in *InsertAudioPostScriptTabReq, opts ...grpc.CallOption) (*InsertAudioPostScriptTabResp, error) {
	out := new(InsertAudioPostScriptTabResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/InsertAudioPostScriptTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) UpdateAudioPostScriptTab(ctx context.Context, in *UpdateAudioPostScriptTabReq, opts ...grpc.CallOption) (*UpdateAudioPostScriptTabResp, error) {
	out := new(UpdateAudioPostScriptTabResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/UpdateAudioPostScriptTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) DeleteAudioPostScriptTab(ctx context.Context, in *DeleteAudioPostScriptTabReq, opts ...grpc.CallOption) (*DeleteAudioPostScriptTabResp, error) {
	out := new(DeleteAudioPostScriptTabResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/DeleteAudioPostScriptTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) AudioPostScriptTabs(ctx context.Context, in *AudioPostScriptTabsReq, opts ...grpc.CallOption) (*AudioPostScriptTabsResp, error) {
	out := new(AudioPostScriptTabsResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/AudioPostScriptTabs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) InsertAudioPostScript(ctx context.Context, in *InsertAudioPostScriptReq, opts ...grpc.CallOption) (*InsertAudioPostScriptResp, error) {
	out := new(InsertAudioPostScriptResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/InsertAudioPostScript", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) UpdateAudioPostScript(ctx context.Context, in *UpdateAudioPostScriptReq, opts ...grpc.CallOption) (*UpdateAudioPostScriptResp, error) {
	out := new(UpdateAudioPostScriptResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/UpdateAudioPostScript", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) DeleteAudioPostScript(ctx context.Context, in *DeleteAudioPostScriptReq, opts ...grpc.CallOption) (*DeleteAudioPostScriptResp, error) {
	out := new(DeleteAudioPostScriptResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/DeleteAudioPostScript", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) AudioPostScripts(ctx context.Context, in *AudioPostScriptsReq, opts ...grpc.CallOption) (*AudioPostScriptsResp, error) {
	out := new(AudioPostScriptsResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/AudioPostScripts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) RandomAudioPostScripts(ctx context.Context, in *RandomAudioPostScriptsReq, opts ...grpc.CallOption) (*RandomAudioPostScriptsResp, error) {
	out := new(RandomAudioPostScriptsResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/RandomAudioPostScripts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) AudioPostScriptsForMobile(ctx context.Context, in *AudioPostScriptsForMobileReq, opts ...grpc.CallOption) (*AudioPostScriptsForMobileResp, error) {
	out := new(AudioPostScriptsForMobileResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/AudioPostScriptsForMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) DelRandList(ctx context.Context, in *DelRandListReq, opts ...grpc.CallOption) (*DelRandListResp, error) {
	out := new(DelRandListResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/DelRandList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) RandomAudioPostImages(ctx context.Context, in *RandomAudioPostImagesReq, opts ...grpc.CallOption) (*RandomAudioPostImagesResp, error) {
	out := new(RandomAudioPostImagesResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/RandomAudioPostImages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) AudioPostImages(ctx context.Context, in *AudioPostImagesReq, opts ...grpc.CallOption) (*AudioPostImagesResp, error) {
	out := new(AudioPostImagesResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/AudioPostImages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) RandomAudioPostMusics(ctx context.Context, in *RandomAudioPostMusicsReq, opts ...grpc.CallOption) (*RandomAudioPostMusicsResp, error) {
	out := new(RandomAudioPostMusicsResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/RandomAudioPostMusics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) AudioPostMusics(ctx context.Context, in *AudioPostMusicsReq, opts ...grpc.CallOption) (*AudioPostMusicsResp, error) {
	out := new(AudioPostMusicsResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/AudioPostMusics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) AudioPostMusicTabs(ctx context.Context, in *AudioPostMusicTabsReq, opts ...grpc.CallOption) (*AudioPostMusicTabsResp, error) {
	out := new(AudioPostMusicTabsResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/AudioPostMusicTabs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioPostClient) XunfeiSignature(ctx context.Context, in *XunfeiSignatureReq, opts ...grpc.CallOption) (*XunfeiSignatureResp, error) {
	out := new(XunfeiSignatureResp)
	err := c.cc.Invoke(ctx, "/audio_post.AudioPost/XunfeiSignature", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AudioPostServer is the server API for AudioPost service.
type AudioPostServer interface {
	// 声音动态台本分类
	InsertAudioPostScriptTab(context.Context, *InsertAudioPostScriptTabReq) (*InsertAudioPostScriptTabResp, error)
	UpdateAudioPostScriptTab(context.Context, *UpdateAudioPostScriptTabReq) (*UpdateAudioPostScriptTabResp, error)
	DeleteAudioPostScriptTab(context.Context, *DeleteAudioPostScriptTabReq) (*DeleteAudioPostScriptTabResp, error)
	AudioPostScriptTabs(context.Context, *AudioPostScriptTabsReq) (*AudioPostScriptTabsResp, error)
	// 声音动态台本
	InsertAudioPostScript(context.Context, *InsertAudioPostScriptReq) (*InsertAudioPostScriptResp, error)
	UpdateAudioPostScript(context.Context, *UpdateAudioPostScriptReq) (*UpdateAudioPostScriptResp, error)
	DeleteAudioPostScript(context.Context, *DeleteAudioPostScriptReq) (*DeleteAudioPostScriptResp, error)
	AudioPostScripts(context.Context, *AudioPostScriptsReq) (*AudioPostScriptsResp, error)
	RandomAudioPostScripts(context.Context, *RandomAudioPostScriptsReq) (*RandomAudioPostScriptsResp, error)
	AudioPostScriptsForMobile(context.Context, *AudioPostScriptsForMobileReq) (*AudioPostScriptsForMobileResp, error)
	DelRandList(context.Context, *DelRandListReq) (*DelRandListResp, error)
	// 声音动态背景图片
	RandomAudioPostImages(context.Context, *RandomAudioPostImagesReq) (*RandomAudioPostImagesResp, error)
	AudioPostImages(context.Context, *AudioPostImagesReq) (*AudioPostImagesResp, error)
	// 声音动态背景音乐
	RandomAudioPostMusics(context.Context, *RandomAudioPostMusicsReq) (*RandomAudioPostMusicsResp, error)
	AudioPostMusics(context.Context, *AudioPostMusicsReq) (*AudioPostMusicsResp, error)
	AudioPostMusicTabs(context.Context, *AudioPostMusicTabsReq) (*AudioPostMusicTabsResp, error)
	// 科大讯飞
	XunfeiSignature(context.Context, *XunfeiSignatureReq) (*XunfeiSignatureResp, error)
}

func RegisterAudioPostServer(s *grpc.Server, srv AudioPostServer) {
	s.RegisterService(&_AudioPost_serviceDesc, srv)
}

func _AudioPost_InsertAudioPostScriptTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertAudioPostScriptTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).InsertAudioPostScriptTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/InsertAudioPostScriptTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).InsertAudioPostScriptTab(ctx, req.(*InsertAudioPostScriptTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_UpdateAudioPostScriptTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAudioPostScriptTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).UpdateAudioPostScriptTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/UpdateAudioPostScriptTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).UpdateAudioPostScriptTab(ctx, req.(*UpdateAudioPostScriptTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_DeleteAudioPostScriptTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAudioPostScriptTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).DeleteAudioPostScriptTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/DeleteAudioPostScriptTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).DeleteAudioPostScriptTab(ctx, req.(*DeleteAudioPostScriptTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_AudioPostScriptTabs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AudioPostScriptTabsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).AudioPostScriptTabs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/AudioPostScriptTabs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).AudioPostScriptTabs(ctx, req.(*AudioPostScriptTabsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_InsertAudioPostScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertAudioPostScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).InsertAudioPostScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/InsertAudioPostScript",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).InsertAudioPostScript(ctx, req.(*InsertAudioPostScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_UpdateAudioPostScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAudioPostScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).UpdateAudioPostScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/UpdateAudioPostScript",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).UpdateAudioPostScript(ctx, req.(*UpdateAudioPostScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_DeleteAudioPostScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAudioPostScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).DeleteAudioPostScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/DeleteAudioPostScript",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).DeleteAudioPostScript(ctx, req.(*DeleteAudioPostScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_AudioPostScripts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AudioPostScriptsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).AudioPostScripts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/AudioPostScripts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).AudioPostScripts(ctx, req.(*AudioPostScriptsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_RandomAudioPostScripts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RandomAudioPostScriptsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).RandomAudioPostScripts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/RandomAudioPostScripts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).RandomAudioPostScripts(ctx, req.(*RandomAudioPostScriptsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_AudioPostScriptsForMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AudioPostScriptsForMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).AudioPostScriptsForMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/AudioPostScriptsForMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).AudioPostScriptsForMobile(ctx, req.(*AudioPostScriptsForMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_DelRandList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelRandListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).DelRandList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/DelRandList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).DelRandList(ctx, req.(*DelRandListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_RandomAudioPostImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RandomAudioPostImagesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).RandomAudioPostImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/RandomAudioPostImages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).RandomAudioPostImages(ctx, req.(*RandomAudioPostImagesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_AudioPostImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AudioPostImagesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).AudioPostImages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/AudioPostImages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).AudioPostImages(ctx, req.(*AudioPostImagesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_RandomAudioPostMusics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RandomAudioPostMusicsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).RandomAudioPostMusics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/RandomAudioPostMusics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).RandomAudioPostMusics(ctx, req.(*RandomAudioPostMusicsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_AudioPostMusics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AudioPostMusicsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).AudioPostMusics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/AudioPostMusics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).AudioPostMusics(ctx, req.(*AudioPostMusicsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_AudioPostMusicTabs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AudioPostMusicTabsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).AudioPostMusicTabs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/AudioPostMusicTabs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).AudioPostMusicTabs(ctx, req.(*AudioPostMusicTabsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioPost_XunfeiSignature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(XunfeiSignatureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioPostServer).XunfeiSignature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_post.AudioPost/XunfeiSignature",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioPostServer).XunfeiSignature(ctx, req.(*XunfeiSignatureReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AudioPost_serviceDesc = grpc.ServiceDesc{
	ServiceName: "audio_post.AudioPost",
	HandlerType: (*AudioPostServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InsertAudioPostScriptTab",
			Handler:    _AudioPost_InsertAudioPostScriptTab_Handler,
		},
		{
			MethodName: "UpdateAudioPostScriptTab",
			Handler:    _AudioPost_UpdateAudioPostScriptTab_Handler,
		},
		{
			MethodName: "DeleteAudioPostScriptTab",
			Handler:    _AudioPost_DeleteAudioPostScriptTab_Handler,
		},
		{
			MethodName: "AudioPostScriptTabs",
			Handler:    _AudioPost_AudioPostScriptTabs_Handler,
		},
		{
			MethodName: "InsertAudioPostScript",
			Handler:    _AudioPost_InsertAudioPostScript_Handler,
		},
		{
			MethodName: "UpdateAudioPostScript",
			Handler:    _AudioPost_UpdateAudioPostScript_Handler,
		},
		{
			MethodName: "DeleteAudioPostScript",
			Handler:    _AudioPost_DeleteAudioPostScript_Handler,
		},
		{
			MethodName: "AudioPostScripts",
			Handler:    _AudioPost_AudioPostScripts_Handler,
		},
		{
			MethodName: "RandomAudioPostScripts",
			Handler:    _AudioPost_RandomAudioPostScripts_Handler,
		},
		{
			MethodName: "AudioPostScriptsForMobile",
			Handler:    _AudioPost_AudioPostScriptsForMobile_Handler,
		},
		{
			MethodName: "DelRandList",
			Handler:    _AudioPost_DelRandList_Handler,
		},
		{
			MethodName: "RandomAudioPostImages",
			Handler:    _AudioPost_RandomAudioPostImages_Handler,
		},
		{
			MethodName: "AudioPostImages",
			Handler:    _AudioPost_AudioPostImages_Handler,
		},
		{
			MethodName: "RandomAudioPostMusics",
			Handler:    _AudioPost_RandomAudioPostMusics_Handler,
		},
		{
			MethodName: "AudioPostMusics",
			Handler:    _AudioPost_AudioPostMusics_Handler,
		},
		{
			MethodName: "AudioPostMusicTabs",
			Handler:    _AudioPost_AudioPostMusicTabs_Handler,
		},
		{
			MethodName: "XunfeiSignature",
			Handler:    _AudioPost_XunfeiSignature_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "audio-post/audio-post.proto",
}

func init() {
	proto.RegisterFile("audio-post/audio-post.proto", fileDescriptor_audio_post_9488c53929d2f122)
}

var fileDescriptor_audio_post_9488c53929d2f122 = []byte{
	// 1019 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0x7f, 0x6f, 0xdb, 0x36,
	0x10, 0x85, 0x1d, 0xdb, 0x6d, 0x2e, 0x68, 0xda, 0xb2, 0xf9, 0xa1, 0x48, 0xa9, 0xe3, 0x71, 0xeb,
	0xe6, 0x0d, 0xab, 0xb3, 0xba, 0xe8, 0x80, 0xed, 0xbf, 0x75, 0x45, 0x81, 0x60, 0x0b, 0x36, 0x28,
	0x2d, 0x30, 0x6c, 0xc3, 0x02, 0x59, 0x52, 0x5c, 0x02, 0xb6, 0xc4, 0x8a, 0xf4, 0x06, 0xec, 0x0b,
	0x0c, 0xfb, 0xd6, 0x05, 0x29, 0x5a, 0xa6, 0x2c, 0x92, 0x71, 0x9b, 0xfe, 0x27, 0x92, 0x77, 0xf7,
	0x1e, 0xdf, 0x5d, 0x78, 0x17, 0x43, 0x10, 0x2d, 0x12, 0x92, 0x3f, 0xa6, 0x39, 0xe3, 0xa7, 0xab,
	0xcf, 0x11, 0x2d, 0x72, 0x9e, 0x23, 0x90, 0x3b, 0x97, 0x62, 0x07, 0x7f, 0x0f, 0xe8, 0x07, 0xb1,
	0xfa, 0x35, 0x67, 0xfc, 0x22, 0x2e, 0x08, 0xe5, 0xaf, 0xa2, 0x09, 0xda, 0x85, 0x36, 0x49, 0xbc,
	0xd6, 0xa0, 0x35, 0xec, 0x86, 0x6d, 0x92, 0xa0, 0x3d, 0xe8, 0x72, 0xc2, 0x67, 0xa9, 0xd7, 0x1e,
	0xb4, 0x86, 0xdb, 0x61, 0xb9, 0xc0, 0x4f, 0x21, 0x38, 0xcb, 0x58, 0x5a, 0xf0, 0x66, 0x84, 0x30,
	0x7d, 0xbb, 0x72, 0x6a, 0xe9, 0x4e, 0x7d, 0x38, 0xb6, 0x3b, 0x31, 0x8a, 0x7f, 0x84, 0xe0, 0x35,
	0x4d, 0x22, 0x9e, 0x9a, 0x83, 0x6e, 0xc6, 0xac, 0x0f, 0xc7, 0xf6, 0x20, 0x8c, 0xe2, 0xc7, 0x10,
	0xbc, 0x48, 0x67, 0xe9, 0x86, 0x20, 0x22, 0x9c, 0xdd, 0x9c, 0x51, 0xec, 0xc1, 0x41, 0xf3, 0x84,
	0x85, 0xe9, 0x5b, 0x7c, 0x0e, 0x87, 0xc6, 0x13, 0x46, 0xd1, 0x18, 0x3a, 0x3c, 0x9a, 0x30, 0xaf,
	0x35, 0xd8, 0x1a, 0xee, 0x8c, 0xfb, 0xa3, 0x55, 0x52, 0x46, 0x06, 0x18, 0x69, 0x8b, 0xff, 0x6b,
	0xc1, 0xdd, 0xb5, 0xc3, 0x86, 0x22, 0x3e, 0xdc, 0x66, 0x69, 0xcc, 0x49, 0x9e, 0x31, 0xaf, 0x3d,
	0xd8, 0x1a, 0x6e, 0x87, 0xd5, 0x1a, 0xed, 0x43, 0x8f, 0x47, 0x93, 0x4b, 0x92, 0x78, 0x5b, 0xd2,
	0xbe, 0xcb, 0xa3, 0xc9, 0x59, 0x82, 0x02, 0xd8, 0x16, 0xdb, 0xa5, 0x90, 0x1d, 0x29, 0xe4, 0x6d,
	0x1e, 0x4d, 0x5e, 0x89, 0xf5, 0x4a, 0xe1, 0xae, 0xae, 0x70, 0x0c, 0x9e, 0x31, 0x8d, 0x42, 0x3e,
	0x9d, 0x41, 0xcb, 0xca, 0xa0, 0xad, 0x33, 0xa8, 0x40, 0xb6, 0x74, 0x90, 0x00, 0x8e, 0x2c, 0x20,
	0x8c, 0x62, 0x06, 0x9e, 0x31, 0xc7, 0xa6, 0x2a, 0xf9, 0x00, 0x4d, 0x2a, 0x46, 0x9d, 0x35, 0x46,
	0x16, 0x50, 0x46, 0xf1, 0x57, 0xe0, 0x19, 0xcb, 0xc4, 0x54, 0x52, 0x01, 0x1c, 0x59, 0x6c, 0x19,
	0xc5, 0x7f, 0xc2, 0x83, 0xb5, 0x6d, 0x51, 0x4c, 0xe8, 0x10, 0x6e, 0x95, 0x4c, 0x4b, 0x59, 0xbb,
	0x61, 0x4f, 0x52, 0x65, 0xe8, 0x00, 0x7a, 0xf9, 0xd5, 0x15, 0x4b, 0xb9, 0x12, 0x55, 0xad, 0xc4,
	0x1d, 0x66, 0x64, 0x4e, 0xf8, 0xf2, 0x66, 0x72, 0x81, 0x2f, 0x61, 0xaf, 0x19, 0x9d, 0x51, 0xf4,
	0x0c, 0x6e, 0xb1, 0x72, 0xa9, 0x6a, 0x32, 0x70, 0xd4, 0x64, 0xb8, 0xb4, 0x45, 0xf7, 0x60, 0x2b,
	0xce, 0x96, 0xc8, 0xe2, 0x13, 0x3f, 0x81, 0xa3, 0x30, 0xca, 0x92, 0x7c, 0x6e, 0xba, 0x44, 0xc5,
	0xa9, 0xa5, 0x73, 0xba, 0x00, 0xdf, 0xe6, 0xf2, 0xc1, 0xcc, 0xf0, 0x3f, 0x70, 0xbc, 0x1e, 0xee,
	0x65, 0x5e, 0x9c, 0xe7, 0x13, 0x32, 0x4b, 0x3f, 0x9e, 0x9e, 0x42, 0x80, 0x05, 0x49, 0x64, 0x9d,
	0xdc, 0x09, 0xc5, 0x27, 0x7e, 0x03, 0x0f, 0x1d, 0xc0, 0x37, 0x92, 0x7a, 0xa1, 0xfe, 0x72, 0x14,
	0x12, 0x86, 0xdd, 0x17, 0xe9, 0x4c, 0x48, 0xf7, 0x33, 0x61, 0xb2, 0xd0, 0x94, 0x4d, 0x6b, 0x65,
	0x73, 0x1f, 0xee, 0xd6, 0x6c, 0x18, 0xc5, 0x63, 0xd8, 0xad, 0x40, 0xce, 0xe6, 0xd1, 0x34, 0x95,
	0x6e, 0xc5, 0x4c, 0x3d, 0xd5, 0xe2, 0x53, 0x55, 0x6c, 0xbb, 0xaa, 0xd8, 0x6f, 0xc0, 0x5b, 0x4b,
	0x91, 0xf4, 0x74, 0x24, 0xf5, 0x97, 0x46, 0x1d, 0x2c, 0x3d, 0xe4, 0xf3, 0xd7, 0x23, 0x72, 0xa5,
	0x14, 0xf0, 0x8d, 0x0a, 0x48, 0x87, 0x50, 0x59, 0xe2, 0xe7, 0x5a, 0xb3, 0x5a, 0x81, 0xbf, 0x5f,
	0xf5, 0x9f, 0x69, 0x7f, 0x5b, 0x37, 0xa4, 0xf3, 0xaf, 0xa6, 0xe2, 0xf9, 0x82, 0x91, 0xd8, 0xa0,
	0x22, 0x82, 0x4e, 0x16, 0xcd, 0x97, 0xed, 0x49, 0x7e, 0x0b, 0xc2, 0x8c, 0x64, 0xd3, 0xb4, 0x50,
	0xaf, 0x9d, 0x5a, 0x29, 0xc5, 0x3b, 0xfa, 0xab, 0x15, 0x4d, 0x18, 0x2f, 0xa2, 0x98, 0xab, 0xc7,
	0xb7, 0x5a, 0x1b, 0xb2, 0x21, 0x19, 0xbc, 0x57, 0x36, 0x96, 0x1e, 0xe5, 0xf5, 0xe7, 0x72, 0xe5,
	0xbc, 0xbe, 0x74, 0x08, 0x95, 0x25, 0xfe, 0x43, 0xcb, 0xc6, 0x0a, 0xfc, 0x23, 0x3d, 0x52, 0x7a,
	0x9a, 0x6e, 0xc8, 0xf3, 0x3b, 0xb8, 0x5f, 0x3f, 0xd9, 0x7c, 0xc2, 0x39, 0x84, 0xfd, 0x86, 0xab,
	0xec, 0xeb, 0x3f, 0x69, 0x1d, 0x5f, 0x3b, 0x60, 0x14, 0x3d, 0xa9, 0xb5, 0xf5, 0x87, 0x76, 0x7e,
	0xab, 0xae, 0xbe, 0x07, 0xe8, 0xb7, 0x45, 0x76, 0x95, 0x92, 0x0b, 0x32, 0xcd, 0x22, 0xbe, 0x28,
	0xc4, 0xeb, 0x84, 0xbf, 0x85, 0x07, 0x8d, 0x5d, 0x46, 0xd1, 0x09, 0xec, 0x44, 0x0b, 0xfe, 0xe6,
	0x92, 0xf1, 0x82, 0x64, 0x53, 0x55, 0x6a, 0x20, 0xb6, 0x2e, 0xe4, 0xce, 0xf8, 0xff, 0x3b, 0xb0,
	0x5d, 0x21, 0xa1, 0xb9, 0xa5, 0x4f, 0x0b, 0x0d, 0xbe, 0xd0, 0xc9, 0x39, 0x26, 0x39, 0x7f, 0xb8,
	0x99, 0x21, 0xa3, 0x02, 0xce, 0x36, 0x78, 0xd5, 0xe1, 0x1c, 0x33, 0x5e, 0x1d, 0xce, 0x35, 0xc7,
	0x09, 0x38, 0xdb, 0x60, 0x56, 0x87, 0x73, 0x4c, 0x7b, 0x75, 0x38, 0xd7, 0x9c, 0x87, 0xfe, 0x6a,
	0xf4, 0x65, 0x91, 0x76, 0x84, 0xdd, 0xb3, 0x9b, 0x28, 0x18, 0xff, 0xd3, 0x6b, 0x6d, 0x18, 0x45,
	0x09, 0xec, 0x1b, 0xd5, 0x45, 0x9f, 0x5d, 0x9b, 0x00, 0x81, 0xf1, 0x68, 0x03, 0xab, 0x12, 0xc5,
	0x28, 0x6a, 0x1d, 0xc5, 0x36, 0x5b, 0xd5, 0x51, 0xac, 0xc3, 0x90, 0x40, 0x31, 0x6a, 0x59, 0x47,
	0xb1, 0xcd, 0x4b, 0x75, 0x14, 0xeb, 0xa4, 0x84, 0x5e, 0xc3, 0xbd, 0xf5, 0x4e, 0x8b, 0x4e, 0x1c,
	0x52, 0xcb, 0x5c, 0x0c, 0xdc, 0x06, 0x8c, 0xa2, 0x29, 0x1c, 0x98, 0xc7, 0x11, 0x54, 0xe3, 0x65,
	0x9d, 0x72, 0xfc, 0xcf, 0x37, 0x31, 0x63, 0x14, 0x51, 0x38, 0xb2, 0x4e, 0x0a, 0x68, 0xe8, 0xe2,
	0xa9, 0x4f, 0x32, 0xfe, 0x97, 0x1b, 0x5a, 0x32, 0x8a, 0x5e, 0xc2, 0x8e, 0x36, 0x0d, 0x20, 0x7f,
	0x4d, 0x67, 0x6d, 0x94, 0xf0, 0x03, 0xeb, 0x59, 0x99, 0x5f, 0x63, 0x73, 0xaf, 0xe7, 0xd7, 0x36,
	0x31, 0xf8, 0x8f, 0x36, 0xb0, 0x62, 0x14, 0x85, 0xda, 0xff, 0x3b, 0x2a, 0x7e, 0xdf, 0xde, 0x99,
	0x65, 0xe4, 0x13, 0xe7, 0xb9, 0x91, 0x79, 0xd9, 0x60, 0x9c, 0xcc, 0xab, 0x06, 0xe7, 0x64, 0xae,
	0x75, 0x2a, 0x9d, 0xb9, 0x8a, 0xdf, 0xb7, 0x37, 0x03, 0x07, 0x73, 0x2d, 0x66, 0xa3, 0xe3, 0xca,
	0xe7, 0xe7, 0x13, 0x67, 0x8f, 0x91, 0x91, 0xf1, 0x75, 0x26, 0x25, 0xe1, 0xb5, 0x7e, 0x53, 0x27,
	0xdc, 0x6c, 0x51, 0x75, 0xc2, 0x86, 0x66, 0xf5, 0x7c, 0xf4, 0xfb, 0xd7, 0xd3, 0x7c, 0x16, 0x65,
	0xd3, 0xd1, 0xb3, 0x31, 0xe7, 0xa3, 0x38, 0x9f, 0x9f, 0xca, 0x9f, 0x20, 0xe2, 0x7c, 0x76, 0xca,
	0xd2, 0xe2, 0x6f, 0x12, 0xa7, 0x4c, 0xfb, 0x7d, 0x62, 0xd2, 0x93, 0xa7, 0x4f, 0xdf, 0x05, 0x00,
	0x00, 0xff, 0xff, 0xc3, 0x22, 0x2c, 0x9c, 0xbf, 0x10, 0x00, 0x00,
}
