// Code generated by protoc-gen-go. DO NOT EDIT.
// source: statistics/statistics.proto

package statistics // import "golang.52tt.com/protocol/services/statistics"

import (
	fmt "fmt"

	proto "github.com/golang/protobuf/proto"

	math "math"

	context "golang.org/x/net/context"

	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 砸蛋记录
type SmashRecord struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PackId               uint32   `protobuf:"varint,5,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackWorth            uint32   `protobuf:"varint,6,opt,name=pack_worth,json=packWorth,proto3" json:"pack_worth,omitempty"`
	PackName             string   `protobuf:"bytes,7,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	CreateTime           uint32   `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SmashRecord) Reset()         { *m = SmashRecord{} }
func (m *SmashRecord) String() string { return proto.CompactTextString(m) }
func (*SmashRecord) ProtoMessage()    {}
func (*SmashRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_statistics_19df5834aeb73dda, []int{0}
}
func (m *SmashRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashRecord.Unmarshal(m, b)
}
func (m *SmashRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashRecord.Marshal(b, m, deterministic)
}
func (dst *SmashRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashRecord.Merge(dst, src)
}
func (m *SmashRecord) XXX_Size() int {
	return xxx_messageInfo_SmashRecord.Size(m)
}
func (m *SmashRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashRecord.DiscardUnknown(m)
}

var xxx_messageInfo_SmashRecord proto.InternalMessageInfo

func (m *SmashRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SmashRecord) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *SmashRecord) GetPackWorth() uint32 {
	if m != nil {
		return m.PackWorth
	}
	return 0
}

func (m *SmashRecord) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *SmashRecord) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type GetSmashRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            uint32   `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PackId               uint32   `protobuf:"varint,6,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSmashRecordReq) Reset()         { *m = GetSmashRecordReq{} }
func (m *GetSmashRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetSmashRecordReq) ProtoMessage()    {}
func (*GetSmashRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_statistics_19df5834aeb73dda, []int{1}
}
func (m *GetSmashRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashRecordReq.Unmarshal(m, b)
}
func (m *GetSmashRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetSmashRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashRecordReq.Merge(dst, src)
}
func (m *GetSmashRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetSmashRecordReq.Size(m)
}
func (m *GetSmashRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashRecordReq proto.InternalMessageInfo

func (m *GetSmashRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSmashRecordReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetSmashRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetSmashRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetSmashRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetSmashRecordReq) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

type GetSmashRecordResp struct {
	SmashRecordList      []*SmashRecord `protobuf:"bytes,1,rep,name=smash_record_list,json=smashRecordList,proto3" json:"smash_record_list,omitempty"`
	PageCount            uint32         `protobuf:"varint,2,opt,name=page_count,json=pageCount,proto3" json:"page_count,omitempty"`
	Total                uint32         `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Cost                 uint64         `protobuf:"varint,4,opt,name=cost,proto3" json:"cost,omitempty"`
	Out                  uint64         `protobuf:"varint,5,opt,name=out,proto3" json:"out,omitempty"`
	Profit               uint64         `protobuf:"varint,6,opt,name=profit,proto3" json:"profit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetSmashRecordResp) Reset()         { *m = GetSmashRecordResp{} }
func (m *GetSmashRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetSmashRecordResp) ProtoMessage()    {}
func (*GetSmashRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_statistics_19df5834aeb73dda, []int{2}
}
func (m *GetSmashRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSmashRecordResp.Unmarshal(m, b)
}
func (m *GetSmashRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSmashRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetSmashRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSmashRecordResp.Merge(dst, src)
}
func (m *GetSmashRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetSmashRecordResp.Size(m)
}
func (m *GetSmashRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSmashRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSmashRecordResp proto.InternalMessageInfo

func (m *GetSmashRecordResp) GetSmashRecordList() []*SmashRecord {
	if m != nil {
		return m.SmashRecordList
	}
	return nil
}

func (m *GetSmashRecordResp) GetPageCount() uint32 {
	if m != nil {
		return m.PageCount
	}
	return 0
}

func (m *GetSmashRecordResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetSmashRecordResp) GetCost() uint64 {
	if m != nil {
		return m.Cost
	}
	return 0
}

func (m *GetSmashRecordResp) GetOut() uint64 {
	if m != nil {
		return m.Out
	}
	return 0
}

func (m *GetSmashRecordResp) GetProfit() uint64 {
	if m != nil {
		return m.Profit
	}
	return 0
}

// 兑换记录
type ConversionRecord struct {
	ConversionId uint32 `protobuf:"varint,1,opt,name=conversion_id,json=conversionId,proto3" json:"conversion_id,omitempty"`
	//  map<uint32, uint32> debrisid_2_num = 2; //需要的碎片ID和数量
	GiftId               string   `protobuf:"bytes,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftNum              uint32   `protobuf:"varint,4,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	GiftName             string   `protobuf:"bytes,5,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	ConversionType       uint32   `protobuf:"varint,6,opt,name=conversion_type,json=conversionType,proto3" json:"conversion_type,omitempty"`
	ShowType             uint32   `protobuf:"varint,7,opt,name=show_type,json=showType,proto3" json:"show_type,omitempty"`
	HoldDay              uint32   `protobuf:"varint,9,opt,name=hold_day,json=holdDay,proto3" json:"hold_day,omitempty"`
	Uid                  uint32   `protobuf:"varint,10,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,11,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Price                uint32   `protobuf:"varint,12,opt,name=price,proto3" json:"price,omitempty"`
	CreateAt             uint32   `protobuf:"varint,13,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	DebrisidNameNum      string   `protobuf:"bytes,14,opt,name=debrisid_name_num,json=debrisidNameNum,proto3" json:"debrisid_name_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConversionRecord) Reset()         { *m = ConversionRecord{} }
func (m *ConversionRecord) String() string { return proto.CompactTextString(m) }
func (*ConversionRecord) ProtoMessage()    {}
func (*ConversionRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_statistics_19df5834aeb73dda, []int{3}
}
func (m *ConversionRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConversionRecord.Unmarshal(m, b)
}
func (m *ConversionRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConversionRecord.Marshal(b, m, deterministic)
}
func (dst *ConversionRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConversionRecord.Merge(dst, src)
}
func (m *ConversionRecord) XXX_Size() int {
	return xxx_messageInfo_ConversionRecord.Size(m)
}
func (m *ConversionRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ConversionRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ConversionRecord proto.InternalMessageInfo

func (m *ConversionRecord) GetConversionId() uint32 {
	if m != nil {
		return m.ConversionId
	}
	return 0
}

func (m *ConversionRecord) GetGiftId() string {
	if m != nil {
		return m.GiftId
	}
	return ""
}

func (m *ConversionRecord) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *ConversionRecord) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *ConversionRecord) GetConversionType() uint32 {
	if m != nil {
		return m.ConversionType
	}
	return 0
}

func (m *ConversionRecord) GetShowType() uint32 {
	if m != nil {
		return m.ShowType
	}
	return 0
}

func (m *ConversionRecord) GetHoldDay() uint32 {
	if m != nil {
		return m.HoldDay
	}
	return 0
}

func (m *ConversionRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConversionRecord) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ConversionRecord) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ConversionRecord) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *ConversionRecord) GetDebrisidNameNum() string {
	if m != nil {
		return m.DebrisidNameNum
	}
	return ""
}

type GetConversionRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            uint32   `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	TargetUid            uint32   `protobuf:"varint,6,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConversionRecordReq) Reset()         { *m = GetConversionRecordReq{} }
func (m *GetConversionRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetConversionRecordReq) ProtoMessage()    {}
func (*GetConversionRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_statistics_19df5834aeb73dda, []int{4}
}
func (m *GetConversionRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConversionRecordReq.Unmarshal(m, b)
}
func (m *GetConversionRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConversionRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetConversionRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConversionRecordReq.Merge(dst, src)
}
func (m *GetConversionRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetConversionRecordReq.Size(m)
}
func (m *GetConversionRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConversionRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConversionRecordReq proto.InternalMessageInfo

func (m *GetConversionRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetConversionRecordReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetConversionRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetConversionRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetConversionRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetConversionRecordReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetConversionRecordResp struct {
	ConversionRecordList []*ConversionRecord `protobuf:"bytes,1,rep,name=conversion_record_list,json=conversionRecordList,proto3" json:"conversion_record_list,omitempty"`
	PageCount            uint32              `protobuf:"varint,2,opt,name=page_count,json=pageCount,proto3" json:"page_count,omitempty"`
	Total                uint32              `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	SumPrice             uint32              `protobuf:"varint,4,opt,name=sumPrice,proto3" json:"sumPrice,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetConversionRecordResp) Reset()         { *m = GetConversionRecordResp{} }
func (m *GetConversionRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetConversionRecordResp) ProtoMessage()    {}
func (*GetConversionRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_statistics_19df5834aeb73dda, []int{5}
}
func (m *GetConversionRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConversionRecordResp.Unmarshal(m, b)
}
func (m *GetConversionRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConversionRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetConversionRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConversionRecordResp.Merge(dst, src)
}
func (m *GetConversionRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetConversionRecordResp.Size(m)
}
func (m *GetConversionRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConversionRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConversionRecordResp proto.InternalMessageInfo

func (m *GetConversionRecordResp) GetConversionRecordList() []*ConversionRecord {
	if m != nil {
		return m.ConversionRecordList
	}
	return nil
}

func (m *GetConversionRecordResp) GetPageCount() uint32 {
	if m != nil {
		return m.PageCount
	}
	return 0
}

func (m *GetConversionRecordResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetConversionRecordResp) GetSumPrice() uint32 {
	if m != nil {
		return m.SumPrice
	}
	return 0
}

func init() {
	proto.RegisterType((*SmashRecord)(nil), "statistics.SmashRecord")
	proto.RegisterType((*GetSmashRecordReq)(nil), "statistics.GetSmashRecordReq")
	proto.RegisterType((*GetSmashRecordResp)(nil), "statistics.GetSmashRecordResp")
	proto.RegisterType((*ConversionRecord)(nil), "statistics.ConversionRecord")
	proto.RegisterType((*GetConversionRecordReq)(nil), "statistics.GetConversionRecordReq")
	proto.RegisterType((*GetConversionRecordResp)(nil), "statistics.GetConversionRecordResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// StatisticsServerClient is the client API for StatisticsServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type StatisticsServerClient interface {
	//  查询砸蛋记录
	GetSmashRecord(ctx context.Context, in *GetSmashRecordReq, opts ...grpc.CallOption) (*GetSmashRecordResp, error)
	//  查询兑换记录
	GetConversionRecord(ctx context.Context, in *GetConversionRecordReq, opts ...grpc.CallOption) (*GetConversionRecordResp, error)
}

type statisticsServerClient struct {
	cc *grpc.ClientConn
}

func NewStatisticsServerClient(cc *grpc.ClientConn) StatisticsServerClient {
	return &statisticsServerClient{cc}
}

func (c *statisticsServerClient) GetSmashRecord(ctx context.Context, in *GetSmashRecordReq, opts ...grpc.CallOption) (*GetSmashRecordResp, error) {
	out := new(GetSmashRecordResp)
	err := c.cc.Invoke(ctx, "/statistics.StatisticsServer/GetSmashRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statisticsServerClient) GetConversionRecord(ctx context.Context, in *GetConversionRecordReq, opts ...grpc.CallOption) (*GetConversionRecordResp, error) {
	out := new(GetConversionRecordResp)
	err := c.cc.Invoke(ctx, "/statistics.StatisticsServer/GetConversionRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StatisticsServerServer is the server API for StatisticsServer service.
type StatisticsServerServer interface {
	//  查询砸蛋记录
	GetSmashRecord(context.Context, *GetSmashRecordReq) (*GetSmashRecordResp, error)
	//  查询兑换记录
	GetConversionRecord(context.Context, *GetConversionRecordReq) (*GetConversionRecordResp, error)
}

func RegisterStatisticsServerServer(s *grpc.Server, srv StatisticsServerServer) {
	s.RegisterService(&_StatisticsServer_serviceDesc, srv)
}

func _StatisticsServer_GetSmashRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSmashRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatisticsServerServer).GetSmashRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/statistics.StatisticsServer/GetSmashRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatisticsServerServer).GetSmashRecord(ctx, req.(*GetSmashRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatisticsServer_GetConversionRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConversionRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatisticsServerServer).GetConversionRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/statistics.StatisticsServer/GetConversionRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatisticsServerServer).GetConversionRecord(ctx, req.(*GetConversionRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _StatisticsServer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "statistics.StatisticsServer",
	HandlerType: (*StatisticsServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSmashRecord",
			Handler:    _StatisticsServer_GetSmashRecord_Handler,
		},
		{
			MethodName: "GetConversionRecord",
			Handler:    _StatisticsServer_GetConversionRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "statistics/statistics.proto",
}

func init() {
	proto.RegisterFile("statistics/statistics.proto", fileDescriptor_statistics_19df5834aeb73dda)
}

var fileDescriptor_statistics_19df5834aeb73dda = []byte{
	// 675 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x95, 0xcf, 0x6e, 0xd3, 0x4a,
	0x14, 0xc6, 0xaf, 0x9b, 0xd4, 0x49, 0x4e, 0xfa, 0x77, 0x6e, 0xd5, 0xba, 0xed, 0xed, 0xa5, 0x72,
	0x17, 0x54, 0x08, 0xa5, 0x52, 0x11, 0x0f, 0x00, 0x45, 0xaa, 0x2a, 0xa1, 0x0a, 0xdc, 0x22, 0x24,
	0x36, 0x66, 0x6a, 0x4f, 0x9d, 0x11, 0xb1, 0xc7, 0xcc, 0x8c, 0x5b, 0xe5, 0x41, 0x78, 0x01, 0xf6,
	0xbc, 0x02, 0x5b, 0x36, 0xbc, 0x10, 0x3b, 0x74, 0xce, 0xb8, 0x8d, 0x9b, 0x46, 0x20, 0xb1, 0x61,
	0x37, 0xe7, 0xf7, 0xcd, 0xe4, 0x9c, 0xf3, 0xcd, 0x19, 0x07, 0xb6, 0x8d, 0xe5, 0x56, 0x1a, 0x2b,
	0x13, 0x73, 0x30, 0x59, 0x0e, 0x4a, 0xad, 0xac, 0x62, 0x30, 0x21, 0xe1, 0x27, 0x0f, 0xfa, 0x67,
	0x39, 0x37, 0xc3, 0x48, 0x24, 0x4a, 0xa7, 0x6c, 0x05, 0x5a, 0x95, 0x4c, 0x03, 0x6f, 0xd7, 0xdb,
	0x5f, 0x8c, 0x70, 0xc9, 0x36, 0xa0, 0x53, 0xf2, 0xe4, 0x43, 0x2c, 0xd3, 0x60, 0x9e, 0xa8, 0x8f,
	0xe1, 0x49, 0xca, 0x76, 0x00, 0x48, 0xb8, 0x56, 0xda, 0x0e, 0x03, 0x9f, 0xb4, 0x1e, 0x92, 0xb7,
	0x08, 0xd8, 0x36, 0x50, 0x10, 0x17, 0x3c, 0x17, 0x41, 0x67, 0xd7, 0xdb, 0xef, 0x45, 0x5d, 0x04,
	0xa7, 0x3c, 0x17, 0xec, 0x01, 0xf4, 0x13, 0x2d, 0xb8, 0x15, 0xb1, 0x95, 0xb9, 0x08, 0xfa, 0x74,
	0x18, 0x1c, 0x3a, 0x97, 0xb9, 0x08, 0x3f, 0x7b, 0xb0, 0x7a, 0x2c, 0x6c, 0xa3, 0xb4, 0x48, 0x7c,
	0x9c, 0x51, 0x1d, 0x83, 0x76, 0xc9, 0x33, 0x11, 0xcc, 0x11, 0xa2, 0x35, 0x5b, 0x83, 0xf9, 0x91,
	0xcc, 0xa5, 0x0d, 0x5a, 0x04, 0x5d, 0x80, 0xe5, 0x5e, 0x88, 0x4c, 0x16, 0x2e, 0x63, 0xdb, 0x95,
	0x4b, 0x04, 0x13, 0xb2, 0x4d, 0xe8, 0x8a, 0x22, 0x75, 0xa2, 0xeb, 0xb3, 0x23, 0x8a, 0x94, 0xa4,
	0x86, 0x03, 0x7e, 0xd3, 0x81, 0xf0, 0xbb, 0x07, 0x6c, 0xba, 0x48, 0x53, 0xb2, 0x23, 0x58, 0x35,
	0x88, 0x62, 0x4d, 0x2c, 0x1e, 0x49, 0x63, 0x03, 0x6f, 0xb7, 0xb5, 0xdf, 0x3f, 0xdc, 0x18, 0x34,
	0x6e, 0xa3, 0x79, 0x6e, 0xd9, 0x4c, 0x82, 0x97, 0xd2, 0x58, 0xe7, 0x6e, 0x26, 0xe2, 0x44, 0x55,
	0x85, 0xad, 0xdb, 0xeb, 0x21, 0x39, 0x42, 0x80, 0x3d, 0x5a, 0x65, 0xf9, 0xe8, 0xa6, 0x47, 0x0a,
	0xd0, 0x8d, 0x44, 0x19, 0x4b, 0xdd, 0xb5, 0x23, 0x5a, 0xa3, 0x67, 0xaa, 0xb2, 0xd4, 0x53, 0x3b,
	0xc2, 0x25, 0x5b, 0x07, 0xbf, 0xd4, 0xea, 0x52, 0x5a, 0x6a, 0xa7, 0x1d, 0xd5, 0x51, 0xf8, 0x63,
	0x0e, 0x56, 0x8e, 0x54, 0x71, 0x25, 0xb4, 0x91, 0xaa, 0xa8, 0x07, 0x62, 0x0f, 0x16, 0x93, 0x5b,
	0x16, 0xdf, 0x9a, 0xbf, 0x30, 0x81, 0x27, 0x34, 0x23, 0x99, 0xbc, 0xb4, 0x28, 0xb7, 0xe8, 0xa6,
	0x7d, 0x0c, 0x4f, 0x52, 0x74, 0x95, 0x84, 0xa2, 0xca, 0x6b, 0xcb, 0x69, 0xe3, 0x69, 0x95, 0xe3,
	0x7c, 0x38, 0x89, 0xd7, 0x8e, 0xf7, 0x22, 0xda, 0x4b, 0xf3, 0xf1, 0x10, 0x96, 0x1b, 0x59, 0xed,
	0xb8, 0x14, 0xb5, 0xf5, 0x4b, 0x13, 0x7c, 0x3e, 0x2e, 0x05, 0xfe, 0x8a, 0x19, 0xaa, 0x6b, 0xb7,
	0xa5, 0x43, 0x5b, 0xba, 0x08, 0x48, 0xdc, 0x84, 0xee, 0x50, 0x8d, 0xd2, 0x38, 0xe5, 0xe3, 0xa0,
	0xe7, 0xb2, 0x63, 0xfc, 0x82, 0x8f, 0x6f, 0x26, 0x09, 0x26, 0x93, 0xb4, 0x03, 0x60, 0xb9, 0xce,
	0x84, 0x8d, 0x51, 0x70, 0x13, 0xd9, 0x73, 0xe4, 0x8d, 0x4c, 0xd1, 0xf0, 0x52, 0xcb, 0x44, 0x04,
	0x0b, 0xce, 0x70, 0x0a, 0x30, 0x7d, 0x3d, 0xc7, 0xdc, 0x06, 0x8b, 0x2e, 0xbd, 0x03, 0xcf, 0x2c,
	0x7b, 0x04, 0xab, 0xa9, 0xb8, 0xd0, 0xd2, 0xc8, 0x94, 0xba, 0x24, 0x17, 0x96, 0xa8, 0xd3, 0xe5,
	0x1b, 0x01, 0xbb, 0x3d, 0xad, 0xf2, 0xf0, 0x8b, 0x07, 0xeb, 0xc7, 0xc2, 0x4e, 0xdb, 0xff, 0xf7,
	0x86, 0xfe, 0xae, 0x1d, 0xfe, 0x94, 0x1d, 0xe1, 0x57, 0x0f, 0x36, 0x66, 0xd6, 0x6b, 0x4a, 0x16,
	0xc1, 0x7a, 0xe3, 0xf2, 0xee, 0x3f, 0x82, 0xff, 0x9a, 0x8f, 0xe0, 0xde, 0x2f, 0xac, 0x25, 0x53,
	0xe4, 0xcf, 0x9f, 0xc3, 0x16, 0x74, 0x4d, 0x95, 0xbf, 0xa2, 0x6b, 0x6b, 0xd7, 0xb3, 0x51, 0xc7,
	0x87, 0xdf, 0x3c, 0x58, 0x39, 0xbb, 0x2d, 0xe3, 0x4c, 0xe8, 0x2b, 0xa1, 0xd9, 0x6b, 0x58, 0xba,
	0xfb, 0x9e, 0xd9, 0x4e, 0xb3, 0xd6, 0x7b, 0x1f, 0xa4, 0xad, 0xff, 0x7f, 0x25, 0x9b, 0x32, 0xfc,
	0x87, 0xbd, 0x87, 0x7f, 0x67, 0xf8, 0xc4, 0xc2, 0xa9, 0x83, 0x33, 0x2e, 0x7e, 0x6b, 0xef, 0xb7,
	0x7b, 0x30, 0xc3, 0xf3, 0xc1, 0xbb, 0xc7, 0x99, 0x1a, 0xf1, 0x22, 0x1b, 0x3c, 0x3d, 0xb4, 0x76,
	0x90, 0xa8, 0xfc, 0x80, 0xbe, 0xf3, 0x89, 0x1a, 0x1d, 0x18, 0xa1, 0xaf, 0x64, 0x22, 0x9a, 0x7f,
	0x02, 0x17, 0x3e, 0xa9, 0x4f, 0x7e, 0x06, 0x00, 0x00, 0xff, 0xff, 0x20, 0x59, 0xe4, 0xf7, 0x24,
	0x06, 0x00, 0x00,
}
