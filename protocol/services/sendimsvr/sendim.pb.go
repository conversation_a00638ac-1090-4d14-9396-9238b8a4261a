// Code generated by protoc-gen-gogo.
// source: src/sendimsvr/sendim.proto
// DO NOT EDIT!

/*
	Package sendim is a generated protocol buffer package.

	It is generated from these files:
		src/sendimsvr/sendim.proto

	It has these top-level messages:
		SendSyncReq
		SendSyncResp
		SendAsyncReq
		SendAsyncResp
		SendToUserReq
		SendToUserResp
		WithdrawReq
		WithdrawResp
		ImMsg
		Sender
		Receiver
		Content
		ImTextNormal
		ImTextWithHighlightUrl
		ImNewExtended
		ImOfficialMessage
		OfficialMessageBody
		OfficialMessageAction
		SendImBizStatus
		SendImTaskStatus
		SendImTaskUnitStage
		SendImData
		ImAccount
		WithdrawToken
		MultiWithdrawToken
*/
package sendim

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type Sender_SenderType int32

const (
	Sender_Nil    Sender_SenderType = 0
	Sender_User   Sender_SenderType = 1
	Sender_Public Sender_SenderType = 2
)

var Sender_SenderType_name = map[int32]string{
	0: "Nil",
	1: "User",
	2: "Public",
}
var Sender_SenderType_value = map[string]int32{
	"Nil":    0,
	"User":   1,
	"Public": 2,
}

func (x Sender_SenderType) Enum() *Sender_SenderType {
	p := new(Sender_SenderType)
	*p = x
	return p
}
func (x Sender_SenderType) String() string {
	return proto.EnumName(Sender_SenderType_name, int32(x))
}
func (x *Sender_SenderType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Sender_SenderType_value, data, "Sender_SenderType")
	if err != nil {
		return err
	}
	*x = Sender_SenderType(value)
	return nil
}
func (Sender_SenderType) EnumDescriptor() ([]byte, []int) { return fileDescriptorSendim, []int{9, 0} }

type Receiver_ReceiverType int32

const (
	Receiver_Nil       Receiver_ReceiverType = 0
	Receiver_User      Receiver_ReceiverType = 1
	Receiver_Public    Receiver_ReceiverType = 2
	Receiver_Broadcast Receiver_ReceiverType = 3
)

var Receiver_ReceiverType_name = map[int32]string{
	0: "Nil",
	1: "User",
	2: "Public",
	3: "Broadcast",
}
var Receiver_ReceiverType_value = map[string]int32{
	"Nil":       0,
	"User":      1,
	"Public":    2,
	"Broadcast": 3,
}

func (x Receiver_ReceiverType) Enum() *Receiver_ReceiverType {
	p := new(Receiver_ReceiverType)
	*p = x
	return p
}
func (x Receiver_ReceiverType) String() string {
	return proto.EnumName(Receiver_ReceiverType_name, int32(x))
}
func (x *Receiver_ReceiverType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Receiver_ReceiverType_value, data, "Receiver_ReceiverType")
	if err != nil {
		return err
	}
	*x = Receiver_ReceiverType(value)
	return nil
}
func (Receiver_ReceiverType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorSendim, []int{10, 0}
}

type Content_ContentType int32

const (
	Content_Nil Content_ContentType = 0
	// 最普通的文本，对应 proto/pbfile/im.proto TEXT_MSG (= 1)
	Content_Text Content_ContentType = 1
	// 带高亮和跳转的文本，对应 proto/pbfile/im.proto TT_COMMON_TEXT_NOTIFY (= 37)
	Content_TextWithHighlightUrl Content_ContentType = 2
	// 带图片、标题、跳转、内容等的公众号推送消息，对应 src/proto/pbfile/im.proto OFFICAIL_MESSAGE_SINGLE(22)/OFFICAIL_MESSAGE_BUNCH(23)
	Content_OfficialMessage Content_ContentType = 3
	// 扩展字段 对应 proto/pbfile/im.proto NEW_EXTENDED (= 21)
	Content_NewExtended Content_ContentType = 4
)

var Content_ContentType_name = map[int32]string{
	0: "Nil",
	1: "Text",
	2: "TextWithHighlightUrl",
	3: "OfficialMessage",
	4: "NewExtended",
}
var Content_ContentType_value = map[string]int32{
	"Nil":                  0,
	"Text":                 1,
	"TextWithHighlightUrl": 2,
	"OfficialMessage":      3,
	"NewExtended":          4,
}

func (x Content_ContentType) Enum() *Content_ContentType {
	p := new(Content_ContentType)
	*p = x
	return p
}
func (x Content_ContentType) String() string {
	return proto.EnumName(Content_ContentType_name, int32(x))
}
func (x *Content_ContentType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Content_ContentType_value, data, "Content_ContentType")
	if err != nil {
		return err
	}
	*x = Content_ContentType(value)
	return nil
}
func (Content_ContentType) EnumDescriptor() ([]byte, []int) { return fileDescriptorSendim, []int{11, 0} }

type OfficialMessageAction_Action int32

const (
	OfficialMessageAction_IN_APP_NAVIGATION OfficialMessageAction_Action = 1
	OfficialMessageAction_GLOBAL_NAVIGATION OfficialMessageAction_Action = 2
)

var OfficialMessageAction_Action_name = map[int32]string{
	1: "IN_APP_NAVIGATION",
	2: "GLOBAL_NAVIGATION",
}
var OfficialMessageAction_Action_value = map[string]int32{
	"IN_APP_NAVIGATION": 1,
	"GLOBAL_NAVIGATION": 2,
}

func (x OfficialMessageAction_Action) Enum() *OfficialMessageAction_Action {
	p := new(OfficialMessageAction_Action)
	*p = x
	return p
}
func (x OfficialMessageAction_Action) String() string {
	return proto.EnumName(OfficialMessageAction_Action_name, int32(x))
}
func (x *OfficialMessageAction_Action) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OfficialMessageAction_Action_value, data, "OfficialMessageAction_Action")
	if err != nil {
		return err
	}
	*x = OfficialMessageAction_Action(value)
	return nil
}
func (OfficialMessageAction_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorSendim, []int{17, 0}
}

type SendImBizStatus_Value int32

const (
	SendImBizStatus_Nil       SendImBizStatus_Value = 0
	SendImBizStatus_Done      SendImBizStatus_Value = 1
	SendImBizStatus_Splitting SendImBizStatus_Value = 2
	SendImBizStatus_ToSplit   SendImBizStatus_Value = 3
)

var SendImBizStatus_Value_name = map[int32]string{
	0: "Nil",
	1: "Done",
	2: "Splitting",
	3: "ToSplit",
}
var SendImBizStatus_Value_value = map[string]int32{
	"Nil":       0,
	"Done":      1,
	"Splitting": 2,
	"ToSplit":   3,
}

func (x SendImBizStatus_Value) Enum() *SendImBizStatus_Value {
	p := new(SendImBizStatus_Value)
	*p = x
	return p
}
func (x SendImBizStatus_Value) String() string {
	return proto.EnumName(SendImBizStatus_Value_name, int32(x))
}
func (x *SendImBizStatus_Value) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SendImBizStatus_Value_value, data, "SendImBizStatus_Value")
	if err != nil {
		return err
	}
	*x = SendImBizStatus_Value(value)
	return nil
}
func (SendImBizStatus_Value) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorSendim, []int{18, 0}
}

type SendImTaskStatus_Value int32

const (
	SendImTaskStatus_Nil     SendImTaskStatus_Value = 0
	SendImTaskStatus_Done    SendImTaskStatus_Value = 1
	SendImTaskStatus_Sending SendImTaskStatus_Value = 2
	SendImTaskStatus_ToSend  SendImTaskStatus_Value = 3
	SendImTaskStatus_Failed  SendImTaskStatus_Value = 4
	SendImTaskStatus_Timeout SendImTaskStatus_Value = 5
)

var SendImTaskStatus_Value_name = map[int32]string{
	0: "Nil",
	1: "Done",
	2: "Sending",
	3: "ToSend",
	4: "Failed",
	5: "Timeout",
}
var SendImTaskStatus_Value_value = map[string]int32{
	"Nil":     0,
	"Done":    1,
	"Sending": 2,
	"ToSend":  3,
	"Failed":  4,
	"Timeout": 5,
}

func (x SendImTaskStatus_Value) Enum() *SendImTaskStatus_Value {
	p := new(SendImTaskStatus_Value)
	*p = x
	return p
}
func (x SendImTaskStatus_Value) String() string {
	return proto.EnumName(SendImTaskStatus_Value_name, int32(x))
}
func (x *SendImTaskStatus_Value) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SendImTaskStatus_Value_value, data, "SendImTaskStatus_Value")
	if err != nil {
		return err
	}
	*x = SendImTaskStatus_Value(value)
	return nil
}
func (SendImTaskStatus_Value) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorSendim, []int{19, 0}
}

type SendImTaskUnitStage_Value int32

const (
	SendImTaskUnitStage_Init     SendImTaskUnitStage_Value = 0
	SendImTaskUnitStage_Complete SendImTaskUnitStage_Value = 1
)

var SendImTaskUnitStage_Value_name = map[int32]string{
	0: "Init",
	1: "Complete",
}
var SendImTaskUnitStage_Value_value = map[string]int32{
	"Init":     0,
	"Complete": 1,
}

func (x SendImTaskUnitStage_Value) Enum() *SendImTaskUnitStage_Value {
	p := new(SendImTaskUnitStage_Value)
	*p = x
	return p
}
func (x SendImTaskUnitStage_Value) String() string {
	return proto.EnumName(SendImTaskUnitStage_Value_name, int32(x))
}
func (x *SendImTaskUnitStage_Value) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SendImTaskUnitStage_Value_value, data, "SendImTaskUnitStage_Value")
	if err != nil {
		return err
	}
	*x = SendImTaskUnitStage_Value(value)
	return nil
}
func (SendImTaskUnitStage_Value) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorSendim, []int{20, 0}
}

type SendSyncReq struct {
	Sender     *Sender   `protobuf:"bytes,1,req,name=sender" json:"sender,omitempty"`
	Receiver   *Receiver `protobuf:"bytes,2,req,name=receiver" json:"receiver,omitempty"`
	Msg        *ImMsg    `protobuf:"bytes,3,req,name=msg" json:"msg,omitempty"`
	WithNotify bool      `protobuf:"varint,4,opt,name=with_notify,json=withNotify" json:"with_notify"`
}

func (m *SendSyncReq) Reset()                    { *m = SendSyncReq{} }
func (m *SendSyncReq) String() string            { return proto.CompactTextString(m) }
func (*SendSyncReq) ProtoMessage()               {}
func (*SendSyncReq) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{0} }

func (m *SendSyncReq) GetSender() *Sender {
	if m != nil {
		return m.Sender
	}
	return nil
}

func (m *SendSyncReq) GetReceiver() *Receiver {
	if m != nil {
		return m.Receiver
	}
	return nil
}

func (m *SendSyncReq) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *SendSyncReq) GetWithNotify() bool {
	if m != nil {
		return m.WithNotify
	}
	return false
}

type SendSyncResp struct {
	WithdrawToken string `protobuf:"bytes,1,opt,name=withdraw_token,json=withdrawToken" json:"withdraw_token"`
}

func (m *SendSyncResp) Reset()                    { *m = SendSyncResp{} }
func (m *SendSyncResp) String() string            { return proto.CompactTextString(m) }
func (*SendSyncResp) ProtoMessage()               {}
func (*SendSyncResp) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{1} }

func (m *SendSyncResp) GetWithdrawToken() string {
	if m != nil {
		return m.WithdrawToken
	}
	return ""
}

type SendAsyncReq struct {
	Sender     *Sender   `protobuf:"bytes,1,req,name=sender" json:"sender,omitempty"`
	Receiver   *Receiver `protobuf:"bytes,2,req,name=receiver" json:"receiver,omitempty"`
	Msg        *ImMsg    `protobuf:"bytes,3,req,name=msg" json:"msg,omitempty"`
	WithNotify bool      `protobuf:"varint,4,opt,name=with_notify,json=withNotify" json:"with_notify"`
}

func (m *SendAsyncReq) Reset()                    { *m = SendAsyncReq{} }
func (m *SendAsyncReq) String() string            { return proto.CompactTextString(m) }
func (*SendAsyncReq) ProtoMessage()               {}
func (*SendAsyncReq) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{2} }

func (m *SendAsyncReq) GetSender() *Sender {
	if m != nil {
		return m.Sender
	}
	return nil
}

func (m *SendAsyncReq) GetReceiver() *Receiver {
	if m != nil {
		return m.Receiver
	}
	return nil
}

func (m *SendAsyncReq) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *SendAsyncReq) GetWithNotify() bool {
	if m != nil {
		return m.WithNotify
	}
	return false
}

type SendAsyncResp struct {
	WithdrawToken string `protobuf:"bytes,1,opt,name=withdraw_token,json=withdrawToken" json:"withdraw_token"`
}

func (m *SendAsyncResp) Reset()                    { *m = SendAsyncResp{} }
func (m *SendAsyncResp) String() string            { return proto.CompactTextString(m) }
func (*SendAsyncResp) ProtoMessage()               {}
func (*SendAsyncResp) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{3} }

func (m *SendAsyncResp) GetWithdrawToken() string {
	if m != nil {
		return m.WithdrawToken
	}
	return ""
}

// 支持消息与接收者一对一，同步/异步发送
type SendToUserReq struct {
	Sender     *Sender  `protobuf:"bytes,1,opt,name=sender" json:"sender,omitempty"`
	UidList    []uint32 `protobuf:"varint,2,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	MsgList    []*ImMsg `protobuf:"bytes,3,rep,name=msg_list,json=msgList" json:"msg_list,omitempty"`
	WithNotify bool     `protobuf:"varint,4,opt,name=with_notify,json=withNotify" json:"with_notify"`
	IsAsync    bool     `protobuf:"varint,5,opt,name=is_async,json=isAsync" json:"is_async"`
}

func (m *SendToUserReq) Reset()                    { *m = SendToUserReq{} }
func (m *SendToUserReq) String() string            { return proto.CompactTextString(m) }
func (*SendToUserReq) ProtoMessage()               {}
func (*SendToUserReq) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{4} }

func (m *SendToUserReq) GetSender() *Sender {
	if m != nil {
		return m.Sender
	}
	return nil
}

func (m *SendToUserReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *SendToUserReq) GetMsgList() []*ImMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

func (m *SendToUserReq) GetWithNotify() bool {
	if m != nil {
		return m.WithNotify
	}
	return false
}

func (m *SendToUserReq) GetIsAsync() bool {
	if m != nil {
		return m.IsAsync
	}
	return false
}

type SendToUserResp struct {
}

func (m *SendToUserResp) Reset()                    { *m = SendToUserResp{} }
func (m *SendToUserResp) String() string            { return proto.CompactTextString(m) }
func (*SendToUserResp) ProtoMessage()               {}
func (*SendToUserResp) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{5} }

type WithdrawReq struct {
	WithdrawToken string `protobuf:"bytes,1,req,name=withdraw_token,json=withdrawToken" json:"withdraw_token"`
}

func (m *WithdrawReq) Reset()                    { *m = WithdrawReq{} }
func (m *WithdrawReq) String() string            { return proto.CompactTextString(m) }
func (*WithdrawReq) ProtoMessage()               {}
func (*WithdrawReq) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{6} }

func (m *WithdrawReq) GetWithdrawToken() string {
	if m != nil {
		return m.WithdrawToken
	}
	return ""
}

type WithdrawResp struct {
}

func (m *WithdrawResp) Reset()                    { *m = WithdrawResp{} }
func (m *WithdrawResp) String() string            { return proto.CompactTextString(m) }
func (*WithdrawResp) ProtoMessage()               {}
func (*WithdrawResp) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{7} }

type ImMsg struct {
	Content       *Content `protobuf:"bytes,1,req,name=content" json:"content,omitempty"`
	ClientMsgId   uint32   `protobuf:"varint,2,opt,name=client_msg_id,json=clientMsgId" json:"client_msg_id"`
	ClientMsgTime uint32   `protobuf:"varint,3,opt,name=client_msg_time,json=clientMsgTime" json:"client_msg_time"`
	// 取值: ttvoice/huanyou/zaiya, 空表示所有
	AppName string `protobuf:"bytes,4,opt,name=app_name,json=appName" json:"app_name"`
	// 取值: android/ios/pc, 空表示全平台
	AppPlatform string `protobuf:"bytes,5,opt,name=app_platform,json=appPlatform" json:"app_platform"`
	// 消息过期时间, second
	ExpiredAt uint32 `protobuf:"varint,6,opt,name=expired_at,json=expiredAt" json:"expired_at"`
}

func (m *ImMsg) Reset()                    { *m = ImMsg{} }
func (m *ImMsg) String() string            { return proto.CompactTextString(m) }
func (*ImMsg) ProtoMessage()               {}
func (*ImMsg) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{8} }

func (m *ImMsg) GetContent() *Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *ImMsg) GetClientMsgId() uint32 {
	if m != nil {
		return m.ClientMsgId
	}
	return 0
}

func (m *ImMsg) GetClientMsgTime() uint32 {
	if m != nil {
		return m.ClientMsgTime
	}
	return 0
}

func (m *ImMsg) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

func (m *ImMsg) GetAppPlatform() string {
	if m != nil {
		return m.AppPlatform
	}
	return ""
}

func (m *ImMsg) GetExpiredAt() uint32 {
	if m != nil {
		return m.ExpiredAt
	}
	return 0
}

type Sender struct {
	Type int32  `protobuf:"varint,1,req,name=type" json:"type"`
	Id   uint32 `protobuf:"varint,2,req,name=id" json:"id"`
}

func (m *Sender) Reset()                    { *m = Sender{} }
func (m *Sender) String() string            { return proto.CompactTextString(m) }
func (*Sender) ProtoMessage()               {}
func (*Sender) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{9} }

func (m *Sender) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Sender) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type Receiver struct {
	Type   int32    `protobuf:"varint,1,req,name=type" json:"type"`
	IdList []uint32 `protobuf:"varint,2,rep,name=id_list,json=idList" json:"id_list,omitempty"`
}

func (m *Receiver) Reset()                    { *m = Receiver{} }
func (m *Receiver) String() string            { return proto.CompactTextString(m) }
func (*Receiver) ProtoMessage()               {}
func (*Receiver) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{10} }

func (m *Receiver) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Receiver) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type Content struct {
	Type            int32                   `protobuf:"varint,1,req,name=type" json:"type"`
	TextNormal      *ImTextNormal           `protobuf:"bytes,2,opt,name=text_normal,json=textNormal" json:"text_normal,omitempty"`
	TextHlUrl       *ImTextWithHighlightUrl `protobuf:"bytes,3,opt,name=text_hl_url,json=textHlUrl" json:"text_hl_url,omitempty"`
	OfficialMessage *ImOfficialMessage      `protobuf:"bytes,4,opt,name=official_message,json=officialMessage" json:"official_message,omitempty"`
	NewExtended     *ImNewExtended          `protobuf:"bytes,5,opt,name=new_extended,json=newExtended" json:"new_extended,omitempty"`
}

func (m *Content) Reset()                    { *m = Content{} }
func (m *Content) String() string            { return proto.CompactTextString(m) }
func (*Content) ProtoMessage()               {}
func (*Content) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{11} }

func (m *Content) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Content) GetTextNormal() *ImTextNormal {
	if m != nil {
		return m.TextNormal
	}
	return nil
}

func (m *Content) GetTextHlUrl() *ImTextWithHighlightUrl {
	if m != nil {
		return m.TextHlUrl
	}
	return nil
}

func (m *Content) GetOfficialMessage() *ImOfficialMessage {
	if m != nil {
		return m.OfficialMessage
	}
	return nil
}

func (m *Content) GetNewExtended() *ImNewExtended {
	if m != nil {
		return m.NewExtended
	}
	return nil
}

// Content::Text
type ImTextNormal struct {
	Content string `protobuf:"bytes,1,req,name=content" json:"content"`
}

func (m *ImTextNormal) Reset()                    { *m = ImTextNormal{} }
func (m *ImTextNormal) String() string            { return proto.CompactTextString(m) }
func (*ImTextNormal) ProtoMessage()               {}
func (*ImTextNormal) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{12} }

func (m *ImTextNormal) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// Content::TextWithHighlightUrl
type ImTextWithHighlightUrl struct {
	Content   string `protobuf:"bytes,1,req,name=content" json:"content"`
	Highlight string `protobuf:"bytes,2,req,name=highlight" json:"highlight"`
	Url       string `protobuf:"bytes,3,req,name=url" json:"url"`
}

func (m *ImTextWithHighlightUrl) Reset()                    { *m = ImTextWithHighlightUrl{} }
func (m *ImTextWithHighlightUrl) String() string            { return proto.CompactTextString(m) }
func (*ImTextWithHighlightUrl) ProtoMessage()               {}
func (*ImTextWithHighlightUrl) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{13} }

func (m *ImTextWithHighlightUrl) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ImTextWithHighlightUrl) GetHighlight() string {
	if m != nil {
		return m.Highlight
	}
	return ""
}

func (m *ImTextWithHighlightUrl) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

// Content::IM_CONTENT_NEW_EXTENDED
type ImNewExtended struct {
	Content string `protobuf:"bytes,1,req,name=content" json:"content"`
}

func (m *ImNewExtended) Reset()                    { *m = ImNewExtended{} }
func (m *ImNewExtended) String() string            { return proto.CompactTextString(m) }
func (*ImNewExtended) ProtoMessage()               {}
func (*ImNewExtended) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{14} }

func (m *ImNewExtended) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// Content::OfficialMessage
// 对应 proto/pbfile/ga_base.proto : OfficialMessageSingle、OfficialMessageBunch
type ImOfficialMessage struct {
	MessageId uint32                 `protobuf:"varint,1,req,name=message_id,json=messageId" json:"message_id"`
	Messages  []*OfficialMessageBody `protobuf:"bytes,2,rep,name=messages" json:"messages,omitempty"`
}

func (m *ImOfficialMessage) Reset()                    { *m = ImOfficialMessage{} }
func (m *ImOfficialMessage) String() string            { return proto.CompactTextString(m) }
func (*ImOfficialMessage) ProtoMessage()               {}
func (*ImOfficialMessage) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{15} }

func (m *ImOfficialMessage) GetMessageId() uint32 {
	if m != nil {
		return m.MessageId
	}
	return 0
}

func (m *ImOfficialMessage) GetMessages() []*OfficialMessageBody {
	if m != nil {
		return m.Messages
	}
	return nil
}

// 对应 proto/pbfile/ga_base.proto : OfficialMessageBody
type OfficialMessageBody struct {
	Title   string                 `protobuf:"bytes,1,req,name=title" json:"title"`
	Content string                 `protobuf:"bytes,2,req,name=content" json:"content"`
	At      uint32                 `protobuf:"varint,3,req,name=at" json:"at"`
	Image   string                 `protobuf:"bytes,4,opt,name=image" json:"image"`
	Action  *OfficialMessageAction `protobuf:"bytes,5,opt,name=action" json:"action,omitempty"`
}

func (m *OfficialMessageBody) Reset()                    { *m = OfficialMessageBody{} }
func (m *OfficialMessageBody) String() string            { return proto.CompactTextString(m) }
func (*OfficialMessageBody) ProtoMessage()               {}
func (*OfficialMessageBody) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{16} }

func (m *OfficialMessageBody) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *OfficialMessageBody) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *OfficialMessageBody) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *OfficialMessageBody) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *OfficialMessageBody) GetAction() *OfficialMessageAction {
	if m != nil {
		return m.Action
	}
	return nil
}

// action 对应 proto/pbfile/ga_base.proto : ControlBlock
type OfficialMessageAction struct {
	Action uint32 `protobuf:"varint,1,req,name=action" json:"action"`
	Uri    string `protobuf:"bytes,2,opt,name=uri" json:"uri"`
}

func (m *OfficialMessageAction) Reset()                    { *m = OfficialMessageAction{} }
func (m *OfficialMessageAction) String() string            { return proto.CompactTextString(m) }
func (*OfficialMessageAction) ProtoMessage()               {}
func (*OfficialMessageAction) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{17} }

func (m *OfficialMessageAction) GetAction() uint32 {
	if m != nil {
		return m.Action
	}
	return 0
}

func (m *OfficialMessageAction) GetUri() string {
	if m != nil {
		return m.Uri
	}
	return ""
}

type SendImBizStatus struct {
}

func (m *SendImBizStatus) Reset()                    { *m = SendImBizStatus{} }
func (m *SendImBizStatus) String() string            { return proto.CompactTextString(m) }
func (*SendImBizStatus) ProtoMessage()               {}
func (*SendImBizStatus) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{18} }

type SendImTaskStatus struct {
}

func (m *SendImTaskStatus) Reset()                    { *m = SendImTaskStatus{} }
func (m *SendImTaskStatus) String() string            { return proto.CompactTextString(m) }
func (*SendImTaskStatus) ProtoMessage()               {}
func (*SendImTaskStatus) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{19} }

type SendImTaskUnitStage struct {
}

func (m *SendImTaskUnitStage) Reset()                    { *m = SendImTaskUnitStage{} }
func (m *SendImTaskUnitStage) String() string            { return proto.CompactTextString(m) }
func (*SendImTaskUnitStage) ProtoMessage()               {}
func (*SendImTaskUnitStage) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{20} }

type SendImData struct {
	Sender        *Sender    `protobuf:"bytes,1,opt,name=sender" json:"sender,omitempty"`
	SenderAccount *ImAccount `protobuf:"bytes,2,opt,name=sender_account,json=senderAccount" json:"sender_account,omitempty"`
	Receiver      *Receiver  `protobuf:"bytes,3,opt,name=receiver" json:"receiver,omitempty"`
	MsgList       []*ImMsg   `protobuf:"bytes,4,rep,name=msg_list,json=msgList" json:"msg_list,omitempty"`
	WithNotify    bool       `protobuf:"varint,5,opt,name=with_notify,json=withNotify" json:"with_notify"`
}

func (m *SendImData) Reset()                    { *m = SendImData{} }
func (m *SendImData) String() string            { return proto.CompactTextString(m) }
func (*SendImData) ProtoMessage()               {}
func (*SendImData) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{21} }

func (m *SendImData) GetSender() *Sender {
	if m != nil {
		return m.Sender
	}
	return nil
}

func (m *SendImData) GetSenderAccount() *ImAccount {
	if m != nil {
		return m.SenderAccount
	}
	return nil
}

func (m *SendImData) GetReceiver() *Receiver {
	if m != nil {
		return m.Receiver
	}
	return nil
}

func (m *SendImData) GetMsgList() []*ImMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

func (m *SendImData) GetWithNotify() bool {
	if m != nil {
		return m.WithNotify
	}
	return false
}

type ImAccount struct {
	Id       uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Account  string `protobuf:"bytes,2,req,name=account" json:"account"`
	Nickname string `protobuf:"bytes,3,req,name=nickname" json:"nickname"`
	Alias    string `protobuf:"bytes,4,opt,name=alias" json:"alias"`
}

func (m *ImAccount) Reset()                    { *m = ImAccount{} }
func (m *ImAccount) String() string            { return proto.CompactTextString(m) }
func (*ImAccount) ProtoMessage()               {}
func (*ImAccount) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{22} }

func (m *ImAccount) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ImAccount) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ImAccount) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ImAccount) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

type WithdrawToken struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix string `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	SeqId  uint32 `protobuf:"varint,3,req,name=seq_id,json=seqId" json:"seq_id"`
}

func (m *WithdrawToken) Reset()                    { *m = WithdrawToken{} }
func (m *WithdrawToken) String() string            { return proto.CompactTextString(m) }
func (*WithdrawToken) ProtoMessage()               {}
func (*WithdrawToken) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{23} }

func (m *WithdrawToken) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WithdrawToken) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *WithdrawToken) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

type MultiWithdrawToken struct {
	Tokens []*WithdrawToken `protobuf:"bytes,1,rep,name=tokens" json:"tokens,omitempty"`
}

func (m *MultiWithdrawToken) Reset()                    { *m = MultiWithdrawToken{} }
func (m *MultiWithdrawToken) String() string            { return proto.CompactTextString(m) }
func (*MultiWithdrawToken) ProtoMessage()               {}
func (*MultiWithdrawToken) Descriptor() ([]byte, []int) { return fileDescriptorSendim, []int{24} }

func (m *MultiWithdrawToken) GetTokens() []*WithdrawToken {
	if m != nil {
		return m.Tokens
	}
	return nil
}

func init() {
	proto.RegisterType((*SendSyncReq)(nil), "sendim.SendSyncReq")
	proto.RegisterType((*SendSyncResp)(nil), "sendim.SendSyncResp")
	proto.RegisterType((*SendAsyncReq)(nil), "sendim.SendAsyncReq")
	proto.RegisterType((*SendAsyncResp)(nil), "sendim.SendAsyncResp")
	proto.RegisterType((*SendToUserReq)(nil), "sendim.SendToUserReq")
	proto.RegisterType((*SendToUserResp)(nil), "sendim.SendToUserResp")
	proto.RegisterType((*WithdrawReq)(nil), "sendim.WithdrawReq")
	proto.RegisterType((*WithdrawResp)(nil), "sendim.WithdrawResp")
	proto.RegisterType((*ImMsg)(nil), "sendim.ImMsg")
	proto.RegisterType((*Sender)(nil), "sendim.Sender")
	proto.RegisterType((*Receiver)(nil), "sendim.Receiver")
	proto.RegisterType((*Content)(nil), "sendim.Content")
	proto.RegisterType((*ImTextNormal)(nil), "sendim.ImTextNormal")
	proto.RegisterType((*ImTextWithHighlightUrl)(nil), "sendim.ImTextWithHighlightUrl")
	proto.RegisterType((*ImNewExtended)(nil), "sendim.ImNewExtended")
	proto.RegisterType((*ImOfficialMessage)(nil), "sendim.ImOfficialMessage")
	proto.RegisterType((*OfficialMessageBody)(nil), "sendim.OfficialMessageBody")
	proto.RegisterType((*OfficialMessageAction)(nil), "sendim.OfficialMessageAction")
	proto.RegisterType((*SendImBizStatus)(nil), "sendim.SendImBizStatus")
	proto.RegisterType((*SendImTaskStatus)(nil), "sendim.SendImTaskStatus")
	proto.RegisterType((*SendImTaskUnitStage)(nil), "sendim.SendImTaskUnitStage")
	proto.RegisterType((*SendImData)(nil), "sendim.SendImData")
	proto.RegisterType((*ImAccount)(nil), "sendim.ImAccount")
	proto.RegisterType((*WithdrawToken)(nil), "sendim.WithdrawToken")
	proto.RegisterType((*MultiWithdrawToken)(nil), "sendim.MultiWithdrawToken")
	proto.RegisterEnum("sendim.Sender_SenderType", Sender_SenderType_name, Sender_SenderType_value)
	proto.RegisterEnum("sendim.Receiver_ReceiverType", Receiver_ReceiverType_name, Receiver_ReceiverType_value)
	proto.RegisterEnum("sendim.Content_ContentType", Content_ContentType_name, Content_ContentType_value)
	proto.RegisterEnum("sendim.OfficialMessageAction_Action", OfficialMessageAction_Action_name, OfficialMessageAction_Action_value)
	proto.RegisterEnum("sendim.SendImBizStatus_Value", SendImBizStatus_Value_name, SendImBizStatus_Value_value)
	proto.RegisterEnum("sendim.SendImTaskStatus_Value", SendImTaskStatus_Value_name, SendImTaskStatus_Value_value)
	proto.RegisterEnum("sendim.SendImTaskUnitStage_Value", SendImTaskUnitStage_Value_name, SendImTaskUnitStage_Value_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for SendIm service

type SendImClient interface {
	SendSync(ctx context.Context, in *SendSyncReq, opts ...grpc.CallOption) (*SendSyncResp, error)
	SendAsync(ctx context.Context, in *SendAsyncReq, opts ...grpc.CallOption) (*SendAsyncResp, error)
	// 撤回
	Withdraw(ctx context.Context, in *WithdrawReq, opts ...grpc.CallOption) (*WithdrawResp, error)
	SendToUser(ctx context.Context, in *SendToUserReq, opts ...grpc.CallOption) (*SendToUserResp, error)
}

type sendImClient struct {
	cc *grpc.ClientConn
}

func NewSendImClient(cc *grpc.ClientConn) SendImClient {
	return &sendImClient{cc}
}

func (c *sendImClient) SendSync(ctx context.Context, in *SendSyncReq, opts ...grpc.CallOption) (*SendSyncResp, error) {
	out := new(SendSyncResp)
	err := grpc.Invoke(ctx, "/sendim.SendIm/SendSync", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendImClient) SendAsync(ctx context.Context, in *SendAsyncReq, opts ...grpc.CallOption) (*SendAsyncResp, error) {
	out := new(SendAsyncResp)
	err := grpc.Invoke(ctx, "/sendim.SendIm/SendAsync", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendImClient) Withdraw(ctx context.Context, in *WithdrawReq, opts ...grpc.CallOption) (*WithdrawResp, error) {
	out := new(WithdrawResp)
	err := grpc.Invoke(ctx, "/sendim.SendIm/Withdraw", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendImClient) SendToUser(ctx context.Context, in *SendToUserReq, opts ...grpc.CallOption) (*SendToUserResp, error) {
	out := new(SendToUserResp)
	err := grpc.Invoke(ctx, "/sendim.SendIm/SendToUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for SendIm service

type SendImServer interface {
	SendSync(context.Context, *SendSyncReq) (*SendSyncResp, error)
	SendAsync(context.Context, *SendAsyncReq) (*SendAsyncResp, error)
	// 撤回
	Withdraw(context.Context, *WithdrawReq) (*WithdrawResp, error)
	SendToUser(context.Context, *SendToUserReq) (*SendToUserResp, error)
}

func RegisterSendImServer(s *grpc.Server, srv SendImServer) {
	s.RegisterService(&_SendIm_serviceDesc, srv)
}

func _SendIm_SendSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendImServer).SendSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sendim.SendIm/SendSync",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendImServer).SendSync(ctx, req.(*SendSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendIm_SendAsync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendAsyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendImServer).SendAsync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sendim.SendIm/SendAsync",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendImServer).SendAsync(ctx, req.(*SendAsyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendIm_Withdraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithdrawReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendImServer).Withdraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sendim.SendIm/Withdraw",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendImServer).Withdraw(ctx, req.(*WithdrawReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendIm_SendToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendImServer).SendToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sendim.SendIm/SendToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendImServer).SendToUser(ctx, req.(*SendToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SendIm_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sendim.SendIm",
	HandlerType: (*SendImServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendSync",
			Handler:    _SendIm_SendSync_Handler,
		},
		{
			MethodName: "SendAsync",
			Handler:    _SendIm_SendAsync_Handler,
		},
		{
			MethodName: "Withdraw",
			Handler:    _SendIm_Withdraw_Handler,
		},
		{
			MethodName: "SendToUser",
			Handler:    _SendIm_SendToUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/sendimsvr/sendim.proto",
}

func (m *SendSyncReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendSyncReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Sender == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sender")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Sender.Size()))
		n1, err := m.Sender.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.Receiver == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("receiver")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Receiver.Size()))
		n2, err := m.Receiver.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Msg.Size()))
		n3, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x20
	i++
	if m.WithNotify {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SendSyncResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendSyncResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.WithdrawToken)))
	i += copy(dAtA[i:], m.WithdrawToken)
	return i, nil
}

func (m *SendAsyncReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendAsyncReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Sender == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sender")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Sender.Size()))
		n4, err := m.Sender.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.Receiver == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("receiver")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Receiver.Size()))
		n5, err := m.Receiver.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Msg.Size()))
		n6, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x20
	i++
	if m.WithNotify {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SendAsyncResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendAsyncResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.WithdrawToken)))
	i += copy(dAtA[i:], m.WithdrawToken)
	return i, nil
}

func (m *SendToUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendToUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Sender != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Sender.Size()))
		n7, err := m.Sender.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintSendim(dAtA, i, uint64(num))
		}
	}
	if len(m.MsgList) > 0 {
		for _, msg := range m.MsgList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintSendim(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x20
	i++
	if m.WithNotify {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	if m.IsAsync {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SendToUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendToUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *WithdrawReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WithdrawReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.WithdrawToken)))
	i += copy(dAtA[i:], m.WithdrawToken)
	return i, nil
}

func (m *WithdrawResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WithdrawResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ImMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Content == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("content")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Content.Size()))
		n8, err := m.Content.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.ClientMsgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.ClientMsgTime))
	dAtA[i] = 0x22
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.AppName)))
	i += copy(dAtA[i:], m.AppName)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.AppPlatform)))
	i += copy(dAtA[i:], m.AppPlatform)
	dAtA[i] = 0x30
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.ExpiredAt))
	return i, nil
}

func (m *Sender) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Sender) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *Receiver) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Receiver) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.Type))
	if len(m.IdList) > 0 {
		for _, num := range m.IdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintSendim(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *Content) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Content) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.Type))
	if m.TextNormal != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.TextNormal.Size()))
		n9, err := m.TextNormal.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if m.TextHlUrl != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.TextHlUrl.Size()))
		n10, err := m.TextHlUrl.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if m.OfficialMessage != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.OfficialMessage.Size()))
		n11, err := m.OfficialMessage.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if m.NewExtended != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.NewExtended.Size()))
		n12, err := m.NewExtended.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *ImTextNormal) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImTextNormal) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	return i, nil
}

func (m *ImTextWithHighlightUrl) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImTextWithHighlightUrl) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Highlight)))
	i += copy(dAtA[i:], m.Highlight)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	return i, nil
}

func (m *ImNewExtended) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImNewExtended) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	return i, nil
}

func (m *ImOfficialMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImOfficialMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.MessageId))
	if len(m.Messages) > 0 {
		for _, msg := range m.Messages {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSendim(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *OfficialMessageBody) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfficialMessageBody) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x12
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x18
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.At))
	dAtA[i] = 0x22
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Image)))
	i += copy(dAtA[i:], m.Image)
	if m.Action != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Action.Size()))
		n13, err := m.Action.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	return i, nil
}

func (m *OfficialMessageAction) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfficialMessageAction) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.Action))
	dAtA[i] = 0x12
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Uri)))
	i += copy(dAtA[i:], m.Uri)
	return i, nil
}

func (m *SendImBizStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendImBizStatus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SendImTaskStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendImTaskStatus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SendImTaskUnitStage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendImTaskUnitStage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SendImData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendImData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Sender != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Sender.Size()))
		n14, err := m.Sender.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	if m.SenderAccount != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.SenderAccount.Size()))
		n15, err := m.SenderAccount.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	if m.Receiver != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSendim(dAtA, i, uint64(m.Receiver.Size()))
		n16, err := m.Receiver.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	if len(m.MsgList) > 0 {
		for _, msg := range m.MsgList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintSendim(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	if m.WithNotify {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ImAccount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImAccount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x22
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Alias)))
	i += copy(dAtA[i:], m.Alias)
	return i, nil
}

func (m *WithdrawToken) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WithdrawToken) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintSendim(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	dAtA[i] = 0x18
	i++
	i = encodeVarintSendim(dAtA, i, uint64(m.SeqId))
	return i, nil
}

func (m *MultiWithdrawToken) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MultiWithdrawToken) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Tokens) > 0 {
		for _, msg := range m.Tokens {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSendim(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Sendim(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Sendim(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintSendim(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *SendSyncReq) Size() (n int) {
	var l int
	_ = l
	if m.Sender != nil {
		l = m.Sender.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if m.Receiver != nil {
		l = m.Receiver.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	n += 2
	return n
}

func (m *SendSyncResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.WithdrawToken)
	n += 1 + l + sovSendim(uint64(l))
	return n
}

func (m *SendAsyncReq) Size() (n int) {
	var l int
	_ = l
	if m.Sender != nil {
		l = m.Sender.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if m.Receiver != nil {
		l = m.Receiver.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	n += 2
	return n
}

func (m *SendAsyncResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.WithdrawToken)
	n += 1 + l + sovSendim(uint64(l))
	return n
}

func (m *SendToUserReq) Size() (n int) {
	var l int
	_ = l
	if m.Sender != nil {
		l = m.Sender.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovSendim(uint64(e))
		}
	}
	if len(m.MsgList) > 0 {
		for _, e := range m.MsgList {
			l = e.Size()
			n += 1 + l + sovSendim(uint64(l))
		}
	}
	n += 2
	n += 2
	return n
}

func (m *SendToUserResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *WithdrawReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.WithdrawToken)
	n += 1 + l + sovSendim(uint64(l))
	return n
}

func (m *WithdrawResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ImMsg) Size() (n int) {
	var l int
	_ = l
	if m.Content != nil {
		l = m.Content.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	n += 1 + sovSendim(uint64(m.ClientMsgId))
	n += 1 + sovSendim(uint64(m.ClientMsgTime))
	l = len(m.AppName)
	n += 1 + l + sovSendim(uint64(l))
	l = len(m.AppPlatform)
	n += 1 + l + sovSendim(uint64(l))
	n += 1 + sovSendim(uint64(m.ExpiredAt))
	return n
}

func (m *Sender) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSendim(uint64(m.Type))
	n += 1 + sovSendim(uint64(m.Id))
	return n
}

func (m *Receiver) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSendim(uint64(m.Type))
	if len(m.IdList) > 0 {
		for _, e := range m.IdList {
			n += 1 + sovSendim(uint64(e))
		}
	}
	return n
}

func (m *Content) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSendim(uint64(m.Type))
	if m.TextNormal != nil {
		l = m.TextNormal.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if m.TextHlUrl != nil {
		l = m.TextHlUrl.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if m.OfficialMessage != nil {
		l = m.OfficialMessage.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if m.NewExtended != nil {
		l = m.NewExtended.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	return n
}

func (m *ImTextNormal) Size() (n int) {
	var l int
	_ = l
	l = len(m.Content)
	n += 1 + l + sovSendim(uint64(l))
	return n
}

func (m *ImTextWithHighlightUrl) Size() (n int) {
	var l int
	_ = l
	l = len(m.Content)
	n += 1 + l + sovSendim(uint64(l))
	l = len(m.Highlight)
	n += 1 + l + sovSendim(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovSendim(uint64(l))
	return n
}

func (m *ImNewExtended) Size() (n int) {
	var l int
	_ = l
	l = len(m.Content)
	n += 1 + l + sovSendim(uint64(l))
	return n
}

func (m *ImOfficialMessage) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSendim(uint64(m.MessageId))
	if len(m.Messages) > 0 {
		for _, e := range m.Messages {
			l = e.Size()
			n += 1 + l + sovSendim(uint64(l))
		}
	}
	return n
}

func (m *OfficialMessageBody) Size() (n int) {
	var l int
	_ = l
	l = len(m.Title)
	n += 1 + l + sovSendim(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovSendim(uint64(l))
	n += 1 + sovSendim(uint64(m.At))
	l = len(m.Image)
	n += 1 + l + sovSendim(uint64(l))
	if m.Action != nil {
		l = m.Action.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	return n
}

func (m *OfficialMessageAction) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSendim(uint64(m.Action))
	l = len(m.Uri)
	n += 1 + l + sovSendim(uint64(l))
	return n
}

func (m *SendImBizStatus) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SendImTaskStatus) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SendImTaskUnitStage) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SendImData) Size() (n int) {
	var l int
	_ = l
	if m.Sender != nil {
		l = m.Sender.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if m.SenderAccount != nil {
		l = m.SenderAccount.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if m.Receiver != nil {
		l = m.Receiver.Size()
		n += 1 + l + sovSendim(uint64(l))
	}
	if len(m.MsgList) > 0 {
		for _, e := range m.MsgList {
			l = e.Size()
			n += 1 + l + sovSendim(uint64(l))
		}
	}
	n += 2
	return n
}

func (m *ImAccount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSendim(uint64(m.Id))
	l = len(m.Account)
	n += 1 + l + sovSendim(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovSendim(uint64(l))
	l = len(m.Alias)
	n += 1 + l + sovSendim(uint64(l))
	return n
}

func (m *WithdrawToken) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSendim(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovSendim(uint64(l))
	n += 1 + sovSendim(uint64(m.SeqId))
	return n
}

func (m *MultiWithdrawToken) Size() (n int) {
	var l int
	_ = l
	if len(m.Tokens) > 0 {
		for _, e := range m.Tokens {
			l = e.Size()
			n += 1 + l + sovSendim(uint64(l))
		}
	}
	return n
}

func sovSendim(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozSendim(x uint64) (n int) {
	return sovSendim(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *SendSyncReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendSyncReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendSyncReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sender", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Sender == nil {
				m.Sender = &Sender{}
			}
			if err := m.Sender.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Receiver", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Receiver == nil {
				m.Receiver = &Receiver{}
			}
			if err := m.Receiver.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &ImMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithNotify", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithNotify = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sender")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("receiver")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendSyncResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendSyncResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendSyncResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithdrawToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.WithdrawToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendAsyncReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendAsyncReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendAsyncReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sender", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Sender == nil {
				m.Sender = &Sender{}
			}
			if err := m.Sender.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Receiver", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Receiver == nil {
				m.Receiver = &Receiver{}
			}
			if err := m.Receiver.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &ImMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithNotify", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithNotify = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sender")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("receiver")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendAsyncResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendAsyncResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendAsyncResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithdrawToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.WithdrawToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendToUserReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendToUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendToUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sender", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Sender == nil {
				m.Sender = &Sender{}
			}
			if err := m.Sender.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSendim
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSendim
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSendim
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSendim
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgList = append(m.MsgList, &ImMsg{})
			if err := m.MsgList[len(m.MsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithNotify", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithNotify = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAsync", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAsync = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendToUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendToUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendToUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WithdrawReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WithdrawReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WithdrawReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithdrawToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.WithdrawToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("withdraw_token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WithdrawResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WithdrawResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WithdrawResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Content == nil {
				m.Content = &Content{}
			}
			if err := m.Content.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientMsgId", wireType)
			}
			m.ClientMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientMsgTime", wireType)
			}
			m.ClientMsgTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppPlatform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppPlatform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpiredAt", wireType)
			}
			m.ExpiredAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpiredAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Sender) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Sender: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Sender: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Receiver) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Receiver: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Receiver: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSendim
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.IdList = append(m.IdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSendim
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSendim
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSendim
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.IdList = append(m.IdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Content) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Content: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Content: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TextNormal", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TextNormal == nil {
				m.TextNormal = &ImTextNormal{}
			}
			if err := m.TextNormal.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TextHlUrl", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TextHlUrl == nil {
				m.TextHlUrl = &ImTextWithHighlightUrl{}
			}
			if err := m.TextHlUrl.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficialMessage", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.OfficialMessage == nil {
				m.OfficialMessage = &ImOfficialMessage{}
			}
			if err := m.OfficialMessage.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewExtended", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NewExtended == nil {
				m.NewExtended = &ImNewExtended{}
			}
			if err := m.NewExtended.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImTextNormal) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImTextNormal: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImTextNormal: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImTextWithHighlightUrl) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImTextWithHighlightUrl: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImTextWithHighlightUrl: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Highlight", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Highlight = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("highlight")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImNewExtended) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImNewExtended: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImNewExtended: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImOfficialMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImOfficialMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImOfficialMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MessageId", wireType)
			}
			m.MessageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MessageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Messages", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Messages = append(m.Messages, &OfficialMessageBody{})
			if err := m.Messages[len(m.Messages)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("message_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfficialMessageBody) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OfficialMessageBody: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OfficialMessageBody: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Image", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Image = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Action", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Action == nil {
				m.Action = &OfficialMessageAction{}
			}
			if err := m.Action.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfficialMessageAction) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OfficialMessageAction: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OfficialMessageAction: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Action", wireType)
			}
			m.Action = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Action |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uri", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Uri = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("action")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendImBizStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendImBizStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendImBizStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendImTaskStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendImTaskStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendImTaskStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendImTaskUnitStage) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendImTaskUnitStage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendImTaskUnitStage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendImData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendImData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendImData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sender", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Sender == nil {
				m.Sender = &Sender{}
			}
			if err := m.Sender.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SenderAccount", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SenderAccount == nil {
				m.SenderAccount = &ImAccount{}
			}
			if err := m.SenderAccount.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Receiver", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Receiver == nil {
				m.Receiver = &Receiver{}
			}
			if err := m.Receiver.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgList = append(m.MsgList, &ImMsg{})
			if err := m.MsgList[len(m.MsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithNotify", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithNotify = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImAccount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImAccount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImAccount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Alias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Alias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("nickname")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WithdrawToken) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WithdrawToken: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WithdrawToken: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqId", wireType)
			}
			m.SeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MultiWithdrawToken) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MultiWithdrawToken: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MultiWithdrawToken: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tokens", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSendim
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Tokens = append(m.Tokens, &WithdrawToken{})
			if err := m.Tokens[len(m.Tokens)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSendim(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSendim
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipSendim(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSendim
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSendim
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthSendim
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowSendim
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipSendim(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthSendim = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSendim   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/sendimsvr/sendim.proto", fileDescriptorSendim) }

var fileDescriptorSendim = []byte{
	// 1571 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x57, 0xcd, 0x6f, 0x1c, 0x49,
	0x15, 0x77, 0x77, 0xcf, 0xe7, 0x6b, 0x8f, 0xa7, 0x53, 0x71, 0xc2, 0xac, 0x77, 0x71, 0x5a, 0xcd,
	0xd7, 0x44, 0xeb, 0x71, 0xb4, 0x23, 0x6d, 0x58, 0x0d, 0x23, 0x4b, 0xe3, 0x84, 0xdd, 0x1d, 0x29,
	0x99, 0x44, 0xed, 0xf1, 0xee, 0x21, 0x8a, 0x26, 0xed, 0xe9, 0xf2, 0xb8, 0xe4, 0xfe, 0xf2, 0x54,
	0x4d, 0x6c, 0xc3, 0x01, 0x2e, 0x08, 0x84, 0x84, 0x84, 0xf8, 0x03, 0x90, 0x90, 0x7c, 0x83, 0x03,
	0x5c, 0x11, 0x47, 0x0e, 0x7b, 0x83, 0xbf, 0x00, 0xa1, 0x70, 0xf1, 0x01, 0xce, 0x08, 0x89, 0x03,
	0xaa, 0xea, 0xea, 0x9e, 0x6e, 0x7b, 0xec, 0x75, 0x6e, 0xec, 0xad, 0xeb, 0xf7, 0x7e, 0xf5, 0xaa,
	0x7e, 0xef, 0xbd, 0xaa, 0x7a, 0x0d, 0x6b, 0x74, 0x3a, 0x7e, 0x40, 0x71, 0xe0, 0x12, 0x9f, 0xbe,
	0x9e, 0xca, 0xaf, 0xcd, 0x68, 0x1a, 0xb2, 0x10, 0x95, 0xe2, 0xd1, 0xda, 0x37, 0xc7, 0xa1, 0xef,
	0x87, 0xc1, 0x03, 0xe6, 0xbd, 0x8e, 0xc8, 0xf8, 0xd0, 0xc3, 0x0f, 0xe8, 0xe1, 0xde, 0x8c, 0x78,
	0x8c, 0x04, 0xec, 0x34, 0xc2, 0x31, 0xdb, 0xfa, 0xad, 0x02, 0xfa, 0x0e, 0x0e, 0xdc, 0x9d, 0xd3,
	0x60, 0x6c, 0xe3, 0x23, 0xf4, 0x6d, 0x10, 0xf3, 0xf1, 0xb4, 0xa1, 0x98, 0x6a, 0x53, 0x6f, 0xaf,
	0x6c, 0x4a, 0xe7, 0x3b, 0x02, 0xb5, 0xa5, 0x15, 0x6d, 0x40, 0x65, 0x8a, 0xc7, 0x98, 0xbc, 0xc6,
	0xd3, 0x86, 0x2a, 0x98, 0x46, 0xc2, 0xb4, 0x25, 0x6e, 0xa7, 0x0c, 0x74, 0x0f, 0x34, 0x9f, 0x4e,
	0x1a, 0x9a, 0x20, 0xd6, 0x12, 0x62, 0xdf, 0x7f, 0x4a, 0x27, 0x36, 0xb7, 0xa0, 0x6f, 0x81, 0x7e,
	0x4c, 0xd8, 0xc1, 0x28, 0x08, 0x19, 0xd9, 0x3f, 0x6d, 0x14, 0x4c, 0xa5, 0x59, 0xd9, 0x2e, 0x7c,
	0xf1, 0xb7, 0x7b, 0x4b, 0x36, 0x70, 0xc3, 0x40, 0xe0, 0xd6, 0xf7, 0x60, 0x79, 0xbe, 0x59, 0x1a,
	0xa1, 0xf7, 0x61, 0x85, 0x5b, 0xdd, 0xa9, 0x73, 0x3c, 0x62, 0xe1, 0x21, 0x0e, 0x1a, 0x8a, 0xa9,
	0x34, 0xab, 0x72, 0x66, 0x2d, 0xb1, 0x0d, 0xb9, 0xc9, 0xfa, 0x9d, 0x12, 0xcf, 0xee, 0xd1, 0xaf,
	0x84, 0xd6, 0x2e, 0xd4, 0x32, 0xbb, 0x7d, 0x5b, 0xb1, 0x7f, 0x56, 0xe2, 0xe9, 0xc3, 0x70, 0x97,
	0xe2, 0xe9, 0x45, 0xb5, 0xca, 0x35, 0x6a, 0xdf, 0x81, 0xca, 0x8c, 0xb8, 0x23, 0x8f, 0x50, 0xd6,
	0x50, 0x4d, 0xad, 0x59, 0xb3, 0xcb, 0x33, 0xe2, 0x3e, 0x21, 0x94, 0xa1, 0x26, 0x54, 0x7c, 0x3a,
	0x89, 0x4d, 0x9a, 0xa9, 0x5d, 0xd6, 0x57, 0xf6, 0xe9, 0x44, 0x30, 0x6f, 0xa6, 0x11, 0xdd, 0x83,
	0x0a, 0xa1, 0x23, 0x87, 0x4b, 0x6c, 0x14, 0x33, 0x9c, 0x32, 0xa1, 0x42, 0xb7, 0x65, 0xc0, 0x4a,
	0x56, 0x05, 0x8d, 0xac, 0x0e, 0xe8, 0x9f, 0x4b, 0xa5, 0x5c, 0xd5, 0xa2, 0xa0, 0xa8, 0x57, 0x05,
	0x65, 0x05, 0x96, 0xe7, 0x73, 0x69, 0x64, 0xfd, 0x47, 0x81, 0xa2, 0xd8, 0x38, 0xba, 0x0f, 0xe5,
	0x71, 0x18, 0x30, 0x1c, 0x30, 0x59, 0x0b, 0xf5, 0x44, 0xd8, 0xa3, 0x18, 0xb6, 0x13, 0x3b, 0x6a,
	0x42, 0x6d, 0xec, 0x11, 0x1c, 0xb0, 0x11, 0x8f, 0x05, 0x71, 0x1b, 0xaa, 0xa9, 0x34, 0x6b, 0x72,
	0x41, 0x3d, 0x36, 0x3d, 0xa5, 0x93, 0xbe, 0x8b, 0x36, 0xa0, 0x9e, 0x61, 0x32, 0xe2, 0xe3, 0x86,
	0x96, 0xe1, 0xd6, 0x52, 0xee, 0x90, 0xf8, 0x98, 0xc7, 0xc2, 0x89, 0xa2, 0x51, 0xe0, 0xf8, 0x58,
	0xc4, 0x2b, 0xd1, 0x50, 0x76, 0xa2, 0x68, 0xe0, 0xf8, 0x18, 0x7d, 0x07, 0x96, 0x39, 0x21, 0xf2,
	0x1c, 0xb6, 0x1f, 0x4e, 0x7d, 0x11, 0xb0, 0x84, 0xa4, 0x3b, 0x51, 0xf4, 0x5c, 0x1a, 0xd0, 0x37,
	0x00, 0xf0, 0x49, 0x44, 0xa6, 0xd8, 0x1d, 0x39, 0xac, 0x51, 0xca, 0x2c, 0x59, 0x95, 0x78, 0x8f,
	0x59, 0x18, 0x4a, 0x71, 0xe2, 0x51, 0x03, 0x0a, 0xfc, 0x42, 0x10, 0xc2, 0x8b, 0x92, 0x28, 0x10,
	0xb4, 0x0a, 0xaa, 0xd0, 0xa7, 0xa6, 0x0e, 0x54, 0xe2, 0x5a, 0xef, 0x03, 0xc4, 0x33, 0x87, 0x9c,
	0x53, 0x06, 0x6d, 0x40, 0x3c, 0x63, 0x09, 0x55, 0xa0, 0xc0, 0x93, 0x64, 0x28, 0x08, 0xa0, 0xf4,
	0x7c, 0xb6, 0xe7, 0x91, 0xb1, 0xa1, 0x5a, 0x3f, 0x84, 0x4a, 0x72, 0x46, 0xae, 0x59, 0xe8, 0x6b,
	0x50, 0xce, 0x97, 0x5c, 0x29, 0xae, 0x38, 0xab, 0x0b, 0xcb, 0xc9, 0xf4, 0x1b, 0xac, 0x86, 0x6a,
	0x50, 0xdd, 0x9e, 0x86, 0x8e, 0x3b, 0x76, 0x28, 0x33, 0x34, 0xeb, 0xbf, 0x2a, 0x94, 0x65, 0xfe,
	0xae, 0x59, 0xfc, 0x43, 0xd0, 0x19, 0x3e, 0x61, 0xa3, 0x20, 0x9c, 0xfa, 0x8e, 0x27, 0xd2, 0xa9,
	0xb7, 0x57, 0xe7, 0x85, 0x3d, 0xc4, 0x27, 0x6c, 0x20, 0x6c, 0x36, 0xb0, 0xf4, 0x1b, 0x6d, 0xc9,
	0x69, 0x07, 0xde, 0x68, 0x36, 0xf5, 0x44, 0x66, 0xf5, 0xf6, 0x7a, 0x7e, 0x1a, 0xaf, 0xb6, 0x4f,
	0xc9, 0xe4, 0xc0, 0x23, 0x93, 0x03, 0xb6, 0x3b, 0xf5, 0xec, 0x2a, 0x9f, 0xf2, 0xa9, 0xb7, 0x3b,
	0xf5, 0xd0, 0x63, 0x30, 0xc2, 0xfd, 0x7d, 0x32, 0x26, 0x8e, 0x37, 0xf2, 0x31, 0xa5, 0xce, 0x24,
	0xce, 0xbb, 0xde, 0x7e, 0x67, 0xee, 0xe4, 0x99, 0x64, 0x3c, 0x8d, 0x09, 0x76, 0x3d, 0xcc, 0x03,
	0xe8, 0x23, 0x58, 0x0e, 0xf0, 0xf1, 0x08, 0x9f, 0x30, 0x9e, 0x12, 0x57, 0x14, 0x85, 0xde, 0xbe,
	0x33, 0xf7, 0x30, 0xc0, 0xc7, 0xdf, 0x97, 0x46, 0x5b, 0x0f, 0xe6, 0x03, 0xeb, 0x15, 0xe8, 0x32,
	0x36, 0x97, 0x22, 0xcb, 0xb7, 0x6e, 0x28, 0xa8, 0x01, 0xab, 0x8b, 0x44, 0x18, 0x2a, 0xba, 0x0d,
	0xf5, 0x0b, 0x3b, 0x33, 0x34, 0x54, 0x07, 0x3d, 0xb3, 0x98, 0x51, 0xb0, 0x36, 0x61, 0x39, 0x1b,
	0x3d, 0xb4, 0x9e, 0x3f, 0x64, 0x69, 0x81, 0x4b, 0xd0, 0x62, 0x70, 0x77, 0x71, 0xd8, 0xbe, 0x6c,
	0x26, 0xb2, 0xa0, 0x7a, 0x90, 0xf0, 0x45, 0xbd, 0x26, 0x8c, 0x39, 0x8c, 0xee, 0x82, 0x16, 0xe7,
	0x69, 0x6e, 0xe5, 0x80, 0xf5, 0x00, 0x6a, 0xb9, 0x28, 0x7d, 0xe9, 0x36, 0x8f, 0xe0, 0xd6, 0xa5,
	0xc4, 0xf0, 0x33, 0x27, 0x93, 0xc8, 0xaf, 0x04, 0x25, 0x73, 0x64, 0xaa, 0x12, 0xef, 0xbb, 0xe8,
	0xbb, 0x50, 0x91, 0x03, 0x2a, 0xea, 0x5c, 0x6f, 0xbf, 0x9b, 0x24, 0xea, 0x82, 0xbf, 0xed, 0xd0,
	0x3d, 0xb5, 0x53, 0xb2, 0xf5, 0x47, 0x05, 0x6e, 0x2f, 0x60, 0xa0, 0x35, 0x28, 0x32, 0xc2, 0x3c,
	0x9c, 0xdb, 0x68, 0x0c, 0x65, 0x65, 0xa8, 0x8b, 0x62, 0xb6, 0x0a, 0xaa, 0xc3, 0x44, 0x38, 0xd2,
	0xc3, 0xed, 0x30, 0xee, 0x91, 0xf8, 0x49, 0x29, 0xa6, 0x1e, 0x05, 0x84, 0x3e, 0x84, 0x92, 0x33,
	0x66, 0x24, 0x0c, 0x64, 0x95, 0x7d, 0xfd, 0x8a, 0xcd, 0xf7, 0x04, 0xc9, 0x96, 0x64, 0xeb, 0x27,
	0x0a, 0xdc, 0x59, 0xc8, 0x40, 0xef, 0xa5, 0x0e, 0xb3, 0x01, 0x93, 0x58, 0x9c, 0x30, 0x22, 0xce,
	0x63, 0x26, 0x61, 0xc4, 0x7a, 0x08, 0x25, 0x39, 0xff, 0x0e, 0xdc, 0xea, 0x0f, 0x46, 0xbd, 0xe7,
	0xcf, 0x47, 0x83, 0xde, 0x67, 0xfd, 0x4f, 0x7a, 0xc3, 0xfe, 0xb3, 0x81, 0xa1, 0x70, 0xf8, 0x93,
	0x27, 0xcf, 0xb6, 0x7b, 0x4f, 0xb2, 0xb0, 0x6a, 0xf5, 0xa1, 0xce, 0xef, 0xad, 0xbe, 0xbf, 0x4d,
	0x7e, 0xb0, 0xc3, 0x1c, 0x36, 0xa3, 0xd6, 0x43, 0x28, 0x7e, 0xe6, 0x78, 0xb3, 0x7c, 0xf5, 0x3f,
	0x0e, 0x03, 0x6c, 0x28, 0xfc, 0x2e, 0xd9, 0x89, 0x3c, 0xc2, 0x18, 0x09, 0x26, 0x86, 0x8a, 0x74,
	0x28, 0x0f, 0x43, 0x01, 0x18, 0x9a, 0xf5, 0x0a, 0x8c, 0xd8, 0xd5, 0xd0, 0xa1, 0x87, 0xd2, 0xd7,
	0x93, 0x6b, 0x7c, 0xe9, 0x50, 0xe6, 0xfc, 0xd8, 0x13, 0x40, 0x69, 0x18, 0xf2, 0xa1, 0xa1, 0xf1,
	0xef, 0x8f, 0x1d, 0xe2, 0xf1, 0xe3, 0x22, 0x56, 0x20, 0x3e, 0x0e, 0x67, 0xcc, 0x28, 0x5a, 0x0f,
	0xe1, 0xf6, 0x7c, 0x85, 0xdd, 0x80, 0xb0, 0x1d, 0xe6, 0x4c, 0xb0, 0x75, 0x2f, 0x59, 0xa4, 0x02,
	0x85, 0x7e, 0x40, 0x98, 0xb1, 0x84, 0x96, 0xa1, 0xf2, 0x28, 0xf4, 0x23, 0x0f, 0x33, 0x6c, 0x28,
	0xd6, 0xbf, 0x94, 0xf8, 0x76, 0xee, 0xfb, 0x8f, 0x1d, 0xe6, 0xdc, 0xf8, 0xd1, 0xff, 0x08, 0x56,
	0xe2, 0xaf, 0x91, 0x33, 0x1e, 0x87, 0x33, 0x51, 0x33, 0x9c, 0x7f, 0x6b, 0x7e, 0x91, 0xf4, 0x62,
	0x83, 0x5d, 0x8b, 0x89, 0x72, 0x98, 0x6b, 0x8e, 0xe2, 0x3b, 0xf0, 0xba, 0xe6, 0x28, 0xdb, 0x41,
	0x14, 0xde, 0xa6, 0x83, 0x28, 0x5e, 0xd1, 0x25, 0xfd, 0x08, 0xaa, 0xe9, 0xd6, 0xe4, 0x7b, 0xa5,
	0xe4, 0xdf, 0x2b, 0x7e, 0x10, 0xe6, 0xa2, 0x32, 0x07, 0x41, 0x82, 0xc8, 0x84, 0x4a, 0x40, 0xc6,
	0x87, 0xe2, 0xe1, 0xcd, 0xde, 0x0e, 0x29, 0xca, 0x0f, 0x85, 0xe3, 0x11, 0x87, 0xe6, 0x0f, 0x85,
	0x80, 0xac, 0x57, 0x50, 0xfb, 0x3c, 0xdb, 0x64, 0x5c, 0xb1, 0x89, 0xf7, 0xa0, 0x44, 0x67, 0xfb,
	0xfb, 0xe4, 0x24, 0xb7, 0x07, 0x89, 0xa1, 0x77, 0x79, 0x9a, 0x8e, 0xf8, 0xcd, 0x91, 0x3d, 0x8f,
	0x45, 0x8a, 0x8f, 0xfa, 0xae, 0xf5, 0x08, 0xd0, 0xd3, 0x99, 0xc7, 0x48, 0x7e, 0x99, 0x16, 0x94,
	0x44, 0xbf, 0x43, 0x1b, 0x8a, 0x88, 0x63, 0x7a, 0xe5, 0xe7, 0x68, 0xb6, 0x24, 0xb5, 0xff, 0x59,
	0x8a, 0xdf, 0xfb, 0xbe, 0x8f, 0x7e, 0xaa, 0x42, 0x25, 0xe9, 0xa2, 0xd1, 0xed, 0x6c, 0x41, 0xc8,
	0x9f, 0x80, 0xb5, 0xd5, 0xcb, 0x20, 0x8d, 0xac, 0xbf, 0x28, 0x3f, 0x3e, 0x3b, 0xd7, 0x94, 0x9f,
	0x9f, 0x9d, 0x6b, 0x2b, 0x61, 0x87, 0x75, 0xdc, 0x8e, 0xd3, 0x89, 0x3a, 0x7b, 0xb8, 0x13, 0xfc,
	0xea, 0xec, 0x5c, 0xfb, 0x83, 0xd2, 0x0a, 0xcd, 0x6e, 0x5c, 0x1b, 0x1b, 0x26, 0x7f, 0x58, 0x3b,
	0xc4, 0xdd, 0x32, 0x5b, 0xcc, 0xec, 0x26, 0xd9, 0x4f, 0xe1, 0x0f, 0x36, 0x88, 0xdb, 0xde, 0xd8,
	0xdc, 0xdc, 0xdc, 0x32, 0x5b, 0xae, 0xd9, 0x95, 0x37, 0x92, 0x34, 0xcb, 0x51, 0x2b, 0xfa, 0x60,
	0xfe, 0xd9, 0xde, 0x32, 0x5f, 0xb4, 0x1c, 0xb3, 0xeb, 0x44, 0x51, 0x8b, 0x67, 0x64, 0xeb, 0xa5,
	0xf9, 0xa2, 0x15, 0x99, 0xdd, 0xa4, 0x17, 0x12, 0xe3, 0xbd, 0xe6, 0x5e, 0xc8, 0x0e, 0x4c, 0xbe,
	0x85, 0xfb, 0x7c, 0x8c, 0xcd, 0x6e, 0xdc, 0xee, 0xd0, 0x91, 0xc3, 0x04, 0x23, 0x68, 0xf2, 0xca,
	0x31, 0xe3, 0x92, 0xba, 0xff, 0x12, 0xfd, 0x5b, 0x81, 0x6a, 0xda, 0x63, 0xa3, 0x9c, 0xea, 0xe4,
	0x27, 0x61, 0xed, 0xce, 0x02, 0x94, 0x46, 0xd6, 0x9f, 0x44, 0x30, 0xd4, 0x2b, 0x82, 0xf1, 0x6b,
	0x11, 0x8c, 0x5c, 0x10, 0x16, 0x4b, 0xbf, 0xb1, 0x62, 0x31, 0xe5, 0x82, 0xea, 0x14, 0x7b, 0x7b,
	0xe5, 0x1f, 0x43, 0x25, 0xa9, 0x93, 0x79, 0x09, 0x64, 0xfa, 0xea, 0x79, 0x09, 0xe4, 0x1a, 0xe6,
	0x3a, 0x17, 0xad, 0x71, 0xd1, 0x4b, 0x5c, 0xe6, 0x12, 0xfa, 0x85, 0x1a, 0x5f, 0x37, 0x71, 0x83,
	0x8e, 0x72, 0xc1, 0x4a, 0x7f, 0x3d, 0xd6, 0xee, 0x2e, 0x82, 0xd3, 0x8a, 0x2a, 0x70, 0x7f, 0xf5,
	0x7c, 0x10, 0x29, 0x77, 0xff, 0xfb, 0xab, 0x4b, 0x6a, 0xc6, 0x23, 0x39, 0xfb, 0x7f, 0x08, 0xa5,
	0xf9, 0xa2, 0x45, 0xcd, 0xae, 0xf8, 0x85, 0xd9, 0x7a, 0xb9, 0x56, 0xfa, 0xd9, 0xd9, 0xb9, 0xf6,
	0x9b, 0x93, 0x6d, 0xe3, 0x8b, 0x37, 0xeb, 0xca, 0x5f, 0xdf, 0xac, 0x2b, 0x7f, 0x7f, 0xb3, 0xae,
	0xfc, 0xf2, 0x1f, 0xeb, 0x4b, 0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0x6e, 0x8a, 0x9e, 0x20, 0xb3,
	0x0f, 0x00, 0x00,
}
