// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse-listening-rank/muse-listening-rank.proto

package muse_listening_rank // import "golang.52tt.com/protocol/services/muse-listening-rank"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetSchoolRankReq_Level int32

const (
	GetSchoolRankReq_level_city    GetSchoolRankReq_Level = 0
	GetSchoolRankReq_level_country GetSchoolRankReq_Level = 1
)

var GetSchoolRankReq_Level_name = map[int32]string{
	0: "level_city",
	1: "level_country",
}
var GetSchoolRankReq_Level_value = map[string]int32{
	"level_city":    0,
	"level_country": 1,
}

func (x GetSchoolRankReq_Level) String() string {
	return proto.EnumName(GetSchoolRankReq_Level_name, int32(x))
}
func (GetSchoolRankReq_Level) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{27, 0}
}

type GeoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GeoReq) Reset()         { *m = GeoReq{} }
func (m *GeoReq) String() string { return proto.CompactTextString(m) }
func (*GeoReq) ProtoMessage()    {}
func (*GeoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{0}
}
func (m *GeoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeoReq.Unmarshal(m, b)
}
func (m *GeoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeoReq.Marshal(b, m, deterministic)
}
func (dst *GeoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeoReq.Merge(dst, src)
}
func (m *GeoReq) XXX_Size() int {
	return xxx_messageInfo_GeoReq.Size(m)
}
func (m *GeoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GeoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GeoReq proto.InternalMessageInfo

type GeoResp struct {
	Province             []*Province `protobuf:"bytes,1,rep,name=province,proto3" json:"province,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GeoResp) Reset()         { *m = GeoResp{} }
func (m *GeoResp) String() string { return proto.CompactTextString(m) }
func (*GeoResp) ProtoMessage()    {}
func (*GeoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{1}
}
func (m *GeoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeoResp.Unmarshal(m, b)
}
func (m *GeoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeoResp.Marshal(b, m, deterministic)
}
func (dst *GeoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeoResp.Merge(dst, src)
}
func (m *GeoResp) XXX_Size() int {
	return xxx_messageInfo_GeoResp.Size(m)
}
func (m *GeoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GeoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GeoResp proto.InternalMessageInfo

func (m *GeoResp) GetProvince() []*Province {
	if m != nil {
		return m.Province
	}
	return nil
}

type Province struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	City                 []*City  `protobuf:"bytes,3,rep,name=city,proto3" json:"city,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Province) Reset()         { *m = Province{} }
func (m *Province) String() string { return proto.CompactTextString(m) }
func (*Province) ProtoMessage()    {}
func (*Province) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{2}
}
func (m *Province) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Province.Unmarshal(m, b)
}
func (m *Province) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Province.Marshal(b, m, deterministic)
}
func (dst *Province) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Province.Merge(dst, src)
}
func (m *Province) XXX_Size() int {
	return xxx_messageInfo_Province.Size(m)
}
func (m *Province) XXX_DiscardUnknown() {
	xxx_messageInfo_Province.DiscardUnknown(m)
}

var xxx_messageInfo_Province proto.InternalMessageInfo

func (m *Province) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *Province) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Province) GetCity() []*City {
	if m != nil {
		return m.City
	}
	return nil
}

type City struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *City) Reset()         { *m = City{} }
func (m *City) String() string { return proto.CompactTextString(m) }
func (*City) ProtoMessage()    {}
func (*City) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{3}
}
func (m *City) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_City.Unmarshal(m, b)
}
func (m *City) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_City.Marshal(b, m, deterministic)
}
func (dst *City) XXX_Merge(src proto.Message) {
	xxx_messageInfo_City.Merge(dst, src)
}
func (m *City) XXX_Size() int {
	return xxx_messageInfo_City.Size(m)
}
func (m *City) XXX_DiscardUnknown() {
	xxx_messageInfo_City.DiscardUnknown(m)
}

var xxx_messageInfo_City proto.InternalMessageInfo

func (m *City) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *City) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type ListSchoolReq struct {
	SchoolName           string   `protobuf:"bytes,1,opt,name=school_name,json=schoolName,proto3" json:"school_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListSchoolReq) Reset()         { *m = ListSchoolReq{} }
func (m *ListSchoolReq) String() string { return proto.CompactTextString(m) }
func (*ListSchoolReq) ProtoMessage()    {}
func (*ListSchoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{4}
}
func (m *ListSchoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSchoolReq.Unmarshal(m, b)
}
func (m *ListSchoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSchoolReq.Marshal(b, m, deterministic)
}
func (dst *ListSchoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSchoolReq.Merge(dst, src)
}
func (m *ListSchoolReq) XXX_Size() int {
	return xxx_messageInfo_ListSchoolReq.Size(m)
}
func (m *ListSchoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSchoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListSchoolReq proto.InternalMessageInfo

func (m *ListSchoolReq) GetSchoolName() string {
	if m != nil {
		return m.SchoolName
	}
	return ""
}

type ListSchoolResp struct {
	Schools              []*ChannelSchool `protobuf:"bytes,1,rep,name=schools,proto3" json:"schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ListSchoolResp) Reset()         { *m = ListSchoolResp{} }
func (m *ListSchoolResp) String() string { return proto.CompactTextString(m) }
func (*ListSchoolResp) ProtoMessage()    {}
func (*ListSchoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{5}
}
func (m *ListSchoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSchoolResp.Unmarshal(m, b)
}
func (m *ListSchoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSchoolResp.Marshal(b, m, deterministic)
}
func (dst *ListSchoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSchoolResp.Merge(dst, src)
}
func (m *ListSchoolResp) XXX_Size() int {
	return xxx_messageInfo_ListSchoolResp.Size(m)
}
func (m *ListSchoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSchoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListSchoolResp proto.InternalMessageInfo

func (m *ListSchoolResp) GetSchools() []*ChannelSchool {
	if m != nil {
		return m.Schools
	}
	return nil
}

type PreChannelSchoolBindReq struct {
	Ttids                []string `protobuf:"bytes,1,rep,name=ttids,proto3" json:"ttids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreChannelSchoolBindReq) Reset()         { *m = PreChannelSchoolBindReq{} }
func (m *PreChannelSchoolBindReq) String() string { return proto.CompactTextString(m) }
func (*PreChannelSchoolBindReq) ProtoMessage()    {}
func (*PreChannelSchoolBindReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{6}
}
func (m *PreChannelSchoolBindReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreChannelSchoolBindReq.Unmarshal(m, b)
}
func (m *PreChannelSchoolBindReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreChannelSchoolBindReq.Marshal(b, m, deterministic)
}
func (dst *PreChannelSchoolBindReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreChannelSchoolBindReq.Merge(dst, src)
}
func (m *PreChannelSchoolBindReq) XXX_Size() int {
	return xxx_messageInfo_PreChannelSchoolBindReq.Size(m)
}
func (m *PreChannelSchoolBindReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PreChannelSchoolBindReq.DiscardUnknown(m)
}

var xxx_messageInfo_PreChannelSchoolBindReq proto.InternalMessageInfo

func (m *PreChannelSchoolBindReq) GetTtids() []string {
	if m != nil {
		return m.Ttids
	}
	return nil
}

type PreChannelSchoolBindResp struct {
	Item                 []*ListChannelSchoolItem `protobuf:"bytes,1,rep,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PreChannelSchoolBindResp) Reset()         { *m = PreChannelSchoolBindResp{} }
func (m *PreChannelSchoolBindResp) String() string { return proto.CompactTextString(m) }
func (*PreChannelSchoolBindResp) ProtoMessage()    {}
func (*PreChannelSchoolBindResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{7}
}
func (m *PreChannelSchoolBindResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreChannelSchoolBindResp.Unmarshal(m, b)
}
func (m *PreChannelSchoolBindResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreChannelSchoolBindResp.Marshal(b, m, deterministic)
}
func (dst *PreChannelSchoolBindResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreChannelSchoolBindResp.Merge(dst, src)
}
func (m *PreChannelSchoolBindResp) XXX_Size() int {
	return xxx_messageInfo_PreChannelSchoolBindResp.Size(m)
}
func (m *PreChannelSchoolBindResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PreChannelSchoolBindResp.DiscardUnknown(m)
}

var xxx_messageInfo_PreChannelSchoolBindResp proto.InternalMessageInfo

func (m *PreChannelSchoolBindResp) GetItem() []*ListChannelSchoolItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type DelChannelSchoolBindReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelSchoolBindReq) Reset()         { *m = DelChannelSchoolBindReq{} }
func (m *DelChannelSchoolBindReq) String() string { return proto.CompactTextString(m) }
func (*DelChannelSchoolBindReq) ProtoMessage()    {}
func (*DelChannelSchoolBindReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{8}
}
func (m *DelChannelSchoolBindReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelSchoolBindReq.Unmarshal(m, b)
}
func (m *DelChannelSchoolBindReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelSchoolBindReq.Marshal(b, m, deterministic)
}
func (dst *DelChannelSchoolBindReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelSchoolBindReq.Merge(dst, src)
}
func (m *DelChannelSchoolBindReq) XXX_Size() int {
	return xxx_messageInfo_DelChannelSchoolBindReq.Size(m)
}
func (m *DelChannelSchoolBindReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelSchoolBindReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelSchoolBindReq proto.InternalMessageInfo

func (m *DelChannelSchoolBindReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelChannelSchoolBindResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelSchoolBindResp) Reset()         { *m = DelChannelSchoolBindResp{} }
func (m *DelChannelSchoolBindResp) String() string { return proto.CompactTextString(m) }
func (*DelChannelSchoolBindResp) ProtoMessage()    {}
func (*DelChannelSchoolBindResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{9}
}
func (m *DelChannelSchoolBindResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelSchoolBindResp.Unmarshal(m, b)
}
func (m *DelChannelSchoolBindResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelSchoolBindResp.Marshal(b, m, deterministic)
}
func (dst *DelChannelSchoolBindResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelSchoolBindResp.Merge(dst, src)
}
func (m *DelChannelSchoolBindResp) XXX_Size() int {
	return xxx_messageInfo_DelChannelSchoolBindResp.Size(m)
}
func (m *DelChannelSchoolBindResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelSchoolBindResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelSchoolBindResp proto.InternalMessageInfo

type UpdateChannelSchoolBindReq struct {
	ChannelSchools       []*ChannelSchoolBind `protobuf:"bytes,1,rep,name=channel_schools,json=channelSchools,proto3" json:"channel_schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateChannelSchoolBindReq) Reset()         { *m = UpdateChannelSchoolBindReq{} }
func (m *UpdateChannelSchoolBindReq) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelSchoolBindReq) ProtoMessage()    {}
func (*UpdateChannelSchoolBindReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{10}
}
func (m *UpdateChannelSchoolBindReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelSchoolBindReq.Unmarshal(m, b)
}
func (m *UpdateChannelSchoolBindReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelSchoolBindReq.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelSchoolBindReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelSchoolBindReq.Merge(dst, src)
}
func (m *UpdateChannelSchoolBindReq) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelSchoolBindReq.Size(m)
}
func (m *UpdateChannelSchoolBindReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelSchoolBindReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelSchoolBindReq proto.InternalMessageInfo

func (m *UpdateChannelSchoolBindReq) GetChannelSchools() []*ChannelSchoolBind {
	if m != nil {
		return m.ChannelSchools
	}
	return nil
}

type UpdateChannelSchoolBindResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelSchoolBindResp) Reset()         { *m = UpdateChannelSchoolBindResp{} }
func (m *UpdateChannelSchoolBindResp) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelSchoolBindResp) ProtoMessage()    {}
func (*UpdateChannelSchoolBindResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{11}
}
func (m *UpdateChannelSchoolBindResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelSchoolBindResp.Unmarshal(m, b)
}
func (m *UpdateChannelSchoolBindResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelSchoolBindResp.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelSchoolBindResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelSchoolBindResp.Merge(dst, src)
}
func (m *UpdateChannelSchoolBindResp) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelSchoolBindResp.Size(m)
}
func (m *UpdateChannelSchoolBindResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelSchoolBindResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelSchoolBindResp proto.InternalMessageInfo

type ChannelSchoolBind struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	SchoolId             string   `protobuf:"bytes,3,opt,name=school_id,json=schoolId,proto3" json:"school_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSchoolBind) Reset()         { *m = ChannelSchoolBind{} }
func (m *ChannelSchoolBind) String() string { return proto.CompactTextString(m) }
func (*ChannelSchoolBind) ProtoMessage()    {}
func (*ChannelSchoolBind) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{12}
}
func (m *ChannelSchoolBind) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSchoolBind.Unmarshal(m, b)
}
func (m *ChannelSchoolBind) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSchoolBind.Marshal(b, m, deterministic)
}
func (dst *ChannelSchoolBind) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSchoolBind.Merge(dst, src)
}
func (m *ChannelSchoolBind) XXX_Size() int {
	return xxx_messageInfo_ChannelSchoolBind.Size(m)
}
func (m *ChannelSchoolBind) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSchoolBind.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSchoolBind proto.InternalMessageInfo

func (m *ChannelSchoolBind) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ChannelSchoolBind) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ChannelSchoolBind) GetSchoolId() string {
	if m != nil {
		return m.SchoolId
	}
	return ""
}

type UpdateSchoolsReq struct {
	Schools              []*ChannelSchool `protobuf:"bytes,1,rep,name=schools,proto3" json:"schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UpdateSchoolsReq) Reset()         { *m = UpdateSchoolsReq{} }
func (m *UpdateSchoolsReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSchoolsReq) ProtoMessage()    {}
func (*UpdateSchoolsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{13}
}
func (m *UpdateSchoolsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSchoolsReq.Unmarshal(m, b)
}
func (m *UpdateSchoolsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSchoolsReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSchoolsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSchoolsReq.Merge(dst, src)
}
func (m *UpdateSchoolsReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSchoolsReq.Size(m)
}
func (m *UpdateSchoolsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSchoolsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSchoolsReq proto.InternalMessageInfo

func (m *UpdateSchoolsReq) GetSchools() []*ChannelSchool {
	if m != nil {
		return m.Schools
	}
	return nil
}

type UpdateSchoolsResp struct {
	FailedSchoolName     []string `protobuf:"bytes,1,rep,name=failed_school_name,json=failedSchoolName,proto3" json:"failed_school_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSchoolsResp) Reset()         { *m = UpdateSchoolsResp{} }
func (m *UpdateSchoolsResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSchoolsResp) ProtoMessage()    {}
func (*UpdateSchoolsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{14}
}
func (m *UpdateSchoolsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSchoolsResp.Unmarshal(m, b)
}
func (m *UpdateSchoolsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSchoolsResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSchoolsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSchoolsResp.Merge(dst, src)
}
func (m *UpdateSchoolsResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSchoolsResp.Size(m)
}
func (m *UpdateSchoolsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSchoolsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSchoolsResp proto.InternalMessageInfo

func (m *UpdateSchoolsResp) GetFailedSchoolName() []string {
	if m != nil {
		return m.FailedSchoolName
	}
	return nil
}

type ListChannelWithSchoolReq struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	SchoolName           string   `protobuf:"bytes,4,opt,name=school_name,json=schoolName,proto3" json:"school_name,omitempty"`
	OffsetId             string   `protobuf:"bytes,5,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Limit                uint32   `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListChannelWithSchoolReq) Reset()         { *m = ListChannelWithSchoolReq{} }
func (m *ListChannelWithSchoolReq) String() string { return proto.CompactTextString(m) }
func (*ListChannelWithSchoolReq) ProtoMessage()    {}
func (*ListChannelWithSchoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{15}
}
func (m *ListChannelWithSchoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelWithSchoolReq.Unmarshal(m, b)
}
func (m *ListChannelWithSchoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelWithSchoolReq.Marshal(b, m, deterministic)
}
func (dst *ListChannelWithSchoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelWithSchoolReq.Merge(dst, src)
}
func (m *ListChannelWithSchoolReq) XXX_Size() int {
	return xxx_messageInfo_ListChannelWithSchoolReq.Size(m)
}
func (m *ListChannelWithSchoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelWithSchoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelWithSchoolReq proto.InternalMessageInfo

func (m *ListChannelWithSchoolReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ListChannelWithSchoolReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListChannelWithSchoolReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ListChannelWithSchoolReq) GetSchoolName() string {
	if m != nil {
		return m.SchoolName
	}
	return ""
}

func (m *ListChannelWithSchoolReq) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *ListChannelWithSchoolReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type ListChannelWithSchoolResp struct {
	Item                 []*ListChannelSchoolItem `protobuf:"bytes,1,rep,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ListChannelWithSchoolResp) Reset()         { *m = ListChannelWithSchoolResp{} }
func (m *ListChannelWithSchoolResp) String() string { return proto.CompactTextString(m) }
func (*ListChannelWithSchoolResp) ProtoMessage()    {}
func (*ListChannelWithSchoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{16}
}
func (m *ListChannelWithSchoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelWithSchoolResp.Unmarshal(m, b)
}
func (m *ListChannelWithSchoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelWithSchoolResp.Marshal(b, m, deterministic)
}
func (dst *ListChannelWithSchoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelWithSchoolResp.Merge(dst, src)
}
func (m *ListChannelWithSchoolResp) XXX_Size() int {
	return xxx_messageInfo_ListChannelWithSchoolResp.Size(m)
}
func (m *ListChannelWithSchoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelWithSchoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelWithSchoolResp proto.InternalMessageInfo

func (m *ListChannelWithSchoolResp) GetItem() []*ListChannelSchoolItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type ListChannelSchoolItem struct {
	Id                   string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Ttid                 string         `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	ChannelId            uint32         `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string         `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	School               *ChannelSchool `protobuf:"bytes,5,opt,name=school,proto3" json:"school,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ListChannelSchoolItem) Reset()         { *m = ListChannelSchoolItem{} }
func (m *ListChannelSchoolItem) String() string { return proto.CompactTextString(m) }
func (*ListChannelSchoolItem) ProtoMessage()    {}
func (*ListChannelSchoolItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{17}
}
func (m *ListChannelSchoolItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelSchoolItem.Unmarshal(m, b)
}
func (m *ListChannelSchoolItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelSchoolItem.Marshal(b, m, deterministic)
}
func (dst *ListChannelSchoolItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelSchoolItem.Merge(dst, src)
}
func (m *ListChannelSchoolItem) XXX_Size() int {
	return xxx_messageInfo_ListChannelSchoolItem.Size(m)
}
func (m *ListChannelSchoolItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelSchoolItem.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelSchoolItem proto.InternalMessageInfo

func (m *ListChannelSchoolItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ListChannelSchoolItem) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ListChannelSchoolItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListChannelSchoolItem) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ListChannelSchoolItem) GetSchool() *ChannelSchool {
	if m != nil {
		return m.School
	}
	return nil
}

type ChannelSchool struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SchoolId             string   `protobuf:"bytes,2,opt,name=school_id,json=schoolId,proto3" json:"school_id,omitempty"`
	SchoolName           string   `protobuf:"bytes,3,opt,name=school_name,json=schoolName,proto3" json:"school_name,omitempty"`
	CityId               string   `protobuf:"bytes,4,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	CityName             string   `protobuf:"bytes,5,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	ProvinceId           string   `protobuf:"bytes,6,opt,name=province_id,json=provinceId,proto3" json:"province_id,omitempty"`
	ProvinceName         string   `protobuf:"bytes,7,opt,name=province_name,json=provinceName,proto3" json:"province_name,omitempty"`
	SchoolCode           string   `protobuf:"bytes,8,opt,name=school_code,json=schoolCode,proto3" json:"school_code,omitempty"`
	IsBinded             bool     `protobuf:"varint,9,opt,name=is_binded,json=isBinded,proto3" json:"is_binded,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSchool) Reset()         { *m = ChannelSchool{} }
func (m *ChannelSchool) String() string { return proto.CompactTextString(m) }
func (*ChannelSchool) ProtoMessage()    {}
func (*ChannelSchool) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{18}
}
func (m *ChannelSchool) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSchool.Unmarshal(m, b)
}
func (m *ChannelSchool) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSchool.Marshal(b, m, deterministic)
}
func (dst *ChannelSchool) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSchool.Merge(dst, src)
}
func (m *ChannelSchool) XXX_Size() int {
	return xxx_messageInfo_ChannelSchool.Size(m)
}
func (m *ChannelSchool) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSchool.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSchool proto.InternalMessageInfo

func (m *ChannelSchool) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ChannelSchool) GetSchoolId() string {
	if m != nil {
		return m.SchoolId
	}
	return ""
}

func (m *ChannelSchool) GetSchoolName() string {
	if m != nil {
		return m.SchoolName
	}
	return ""
}

func (m *ChannelSchool) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

func (m *ChannelSchool) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *ChannelSchool) GetProvinceId() string {
	if m != nil {
		return m.ProvinceId
	}
	return ""
}

func (m *ChannelSchool) GetProvinceName() string {
	if m != nil {
		return m.ProvinceName
	}
	return ""
}

func (m *ChannelSchool) GetSchoolCode() string {
	if m != nil {
		return m.SchoolCode
	}
	return ""
}

func (m *ChannelSchool) GetIsBinded() bool {
	if m != nil {
		return m.IsBinded
	}
	return false
}

type AddFlowerReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Flower               int64    `protobuf:"varint,2,opt,name=flower,proto3" json:"flower,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFlowerReq) Reset()         { *m = AddFlowerReq{} }
func (m *AddFlowerReq) String() string { return proto.CompactTextString(m) }
func (*AddFlowerReq) ProtoMessage()    {}
func (*AddFlowerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{19}
}
func (m *AddFlowerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFlowerReq.Unmarshal(m, b)
}
func (m *AddFlowerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFlowerReq.Marshal(b, m, deterministic)
}
func (dst *AddFlowerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFlowerReq.Merge(dst, src)
}
func (m *AddFlowerReq) XXX_Size() int {
	return xxx_messageInfo_AddFlowerReq.Size(m)
}
func (m *AddFlowerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFlowerReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFlowerReq proto.InternalMessageInfo

func (m *AddFlowerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddFlowerReq) GetFlower() int64 {
	if m != nil {
		return m.Flower
	}
	return 0
}

type AddFlowerResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Flower               int64    `protobuf:"varint,2,opt,name=flower,proto3" json:"flower,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFlowerResp) Reset()         { *m = AddFlowerResp{} }
func (m *AddFlowerResp) String() string { return proto.CompactTextString(m) }
func (*AddFlowerResp) ProtoMessage()    {}
func (*AddFlowerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{20}
}
func (m *AddFlowerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFlowerResp.Unmarshal(m, b)
}
func (m *AddFlowerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFlowerResp.Marshal(b, m, deterministic)
}
func (dst *AddFlowerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFlowerResp.Merge(dst, src)
}
func (m *AddFlowerResp) XXX_Size() int {
	return xxx_messageInfo_AddFlowerResp.Size(m)
}
func (m *AddFlowerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFlowerResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddFlowerResp proto.InternalMessageInfo

func (m *AddFlowerResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddFlowerResp) GetFlower() int64 {
	if m != nil {
		return m.Flower
	}
	return 0
}

type GetUserInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoReq) Reset()         { *m = GetUserInfoReq{} }
func (m *GetUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoReq) ProtoMessage()    {}
func (*GetUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{21}
}
func (m *GetUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoReq.Unmarshal(m, b)
}
func (m *GetUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoReq.Merge(dst, src)
}
func (m *GetUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoReq.Size(m)
}
func (m *GetUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoReq proto.InternalMessageInfo

func (m *GetUserInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserInfoResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Flower               int64    `protobuf:"varint,4,opt,name=flower,proto3" json:"flower,omitempty"`
	School               *School  `protobuf:"bytes,5,opt,name=school,proto3" json:"school,omitempty"`
	UserRankInSchool     int32    `protobuf:"varint,6,opt,name=user_rank_in_school,json=userRankInSchool,proto3" json:"user_rank_in_school,omitempty"`
	SchoolRankInCity     int32    `protobuf:"varint,7,opt,name=school_rank_in_city,json=schoolRankInCity,proto3" json:"school_rank_in_city,omitempty"`
	SchoolRankInCountry  int32    `protobuf:"varint,8,opt,name=school_rank_in_country,json=schoolRankInCountry,proto3" json:"school_rank_in_country,omitempty"`
	ChannelId            uint32   `protobuf:"varint,9,opt,name=channelId,proto3" json:"channelId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoResp) Reset()         { *m = GetUserInfoResp{} }
func (m *GetUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoResp) ProtoMessage()    {}
func (*GetUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{22}
}
func (m *GetUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoResp.Unmarshal(m, b)
}
func (m *GetUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoResp.Merge(dst, src)
}
func (m *GetUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoResp.Size(m)
}
func (m *GetUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoResp proto.InternalMessageInfo

func (m *GetUserInfoResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserInfoResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetUserInfoResp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetUserInfoResp) GetFlower() int64 {
	if m != nil {
		return m.Flower
	}
	return 0
}

func (m *GetUserInfoResp) GetSchool() *School {
	if m != nil {
		return m.School
	}
	return nil
}

func (m *GetUserInfoResp) GetUserRankInSchool() int32 {
	if m != nil {
		return m.UserRankInSchool
	}
	return 0
}

func (m *GetUserInfoResp) GetSchoolRankInCity() int32 {
	if m != nil {
		return m.SchoolRankInCity
	}
	return 0
}

func (m *GetUserInfoResp) GetSchoolRankInCountry() int32 {
	if m != nil {
		return m.SchoolRankInCountry
	}
	return 0
}

func (m *GetUserInfoResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type School struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	CityId               string   `protobuf:"bytes,4,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	CityName             string   `protobuf:"bytes,5,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	RealName             string   `protobuf:"bytes,6,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	Phone                string   `protobuf:"bytes,7,opt,name=phone,proto3" json:"phone,omitempty"`
	SourceList           []string `protobuf:"bytes,8,rep,name=source_list,json=sourceList,proto3" json:"source_list,omitempty"`
	ObjId                string   `protobuf:"bytes,9,opt,name=obj_id,json=objId,proto3" json:"obj_id,omitempty"`
	SchoolCode           string   `protobuf:"bytes,10,opt,name=school_code,json=schoolCode,proto3" json:"school_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *School) Reset()         { *m = School{} }
func (m *School) String() string { return proto.CompactTextString(m) }
func (*School) ProtoMessage()    {}
func (*School) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{23}
}
func (m *School) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_School.Unmarshal(m, b)
}
func (m *School) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_School.Marshal(b, m, deterministic)
}
func (dst *School) XXX_Merge(src proto.Message) {
	xxx_messageInfo_School.Merge(dst, src)
}
func (m *School) XXX_Size() int {
	return xxx_messageInfo_School.Size(m)
}
func (m *School) XXX_DiscardUnknown() {
	xxx_messageInfo_School.DiscardUnknown(m)
}

var xxx_messageInfo_School proto.InternalMessageInfo

func (m *School) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *School) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *School) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *School) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

func (m *School) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *School) GetRealName() string {
	if m != nil {
		return m.RealName
	}
	return ""
}

func (m *School) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *School) GetSourceList() []string {
	if m != nil {
		return m.SourceList
	}
	return nil
}

func (m *School) GetObjId() string {
	if m != nil {
		return m.ObjId
	}
	return ""
}

func (m *School) GetSchoolCode() string {
	if m != nil {
		return m.SchoolCode
	}
	return ""
}

type GetUserRankInSchoolReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRankInSchoolReq) Reset()         { *m = GetUserRankInSchoolReq{} }
func (m *GetUserRankInSchoolReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRankInSchoolReq) ProtoMessage()    {}
func (*GetUserRankInSchoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{24}
}
func (m *GetUserRankInSchoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankInSchoolReq.Unmarshal(m, b)
}
func (m *GetUserRankInSchoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankInSchoolReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRankInSchoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankInSchoolReq.Merge(dst, src)
}
func (m *GetUserRankInSchoolReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRankInSchoolReq.Size(m)
}
func (m *GetUserRankInSchoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankInSchoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankInSchoolReq proto.InternalMessageInfo

func (m *GetUserRankInSchoolReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UserWithFlower struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	School               *School  `protobuf:"bytes,4,opt,name=school,proto3" json:"school,omitempty"`
	Flower               int64    `protobuf:"varint,5,opt,name=flower,proto3" json:"flower,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserWithFlower) Reset()         { *m = UserWithFlower{} }
func (m *UserWithFlower) String() string { return proto.CompactTextString(m) }
func (*UserWithFlower) ProtoMessage()    {}
func (*UserWithFlower) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{25}
}
func (m *UserWithFlower) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserWithFlower.Unmarshal(m, b)
}
func (m *UserWithFlower) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserWithFlower.Marshal(b, m, deterministic)
}
func (dst *UserWithFlower) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserWithFlower.Merge(dst, src)
}
func (m *UserWithFlower) XXX_Size() int {
	return xxx_messageInfo_UserWithFlower.Size(m)
}
func (m *UserWithFlower) XXX_DiscardUnknown() {
	xxx_messageInfo_UserWithFlower.DiscardUnknown(m)
}

var xxx_messageInfo_UserWithFlower proto.InternalMessageInfo

func (m *UserWithFlower) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserWithFlower) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserWithFlower) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserWithFlower) GetSchool() *School {
	if m != nil {
		return m.School
	}
	return nil
}

func (m *UserWithFlower) GetFlower() int64 {
	if m != nil {
		return m.Flower
	}
	return 0
}

type GetUserRankInSchoolResp struct {
	Users                []*UserWithFlower `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserRankInSchoolResp) Reset()         { *m = GetUserRankInSchoolResp{} }
func (m *GetUserRankInSchoolResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRankInSchoolResp) ProtoMessage()    {}
func (*GetUserRankInSchoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{26}
}
func (m *GetUserRankInSchoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankInSchoolResp.Unmarshal(m, b)
}
func (m *GetUserRankInSchoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankInSchoolResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRankInSchoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankInSchoolResp.Merge(dst, src)
}
func (m *GetUserRankInSchoolResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRankInSchoolResp.Size(m)
}
func (m *GetUserRankInSchoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankInSchoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankInSchoolResp proto.InternalMessageInfo

func (m *GetUserRankInSchoolResp) GetUsers() []*UserWithFlower {
	if m != nil {
		return m.Users
	}
	return nil
}

type GetSchoolRankReq struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Level                GetSchoolRankReq_Level `protobuf:"varint,2,opt,name=level,proto3,enum=muse_listening_rank.GetSchoolRankReq_Level" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetSchoolRankReq) Reset()         { *m = GetSchoolRankReq{} }
func (m *GetSchoolRankReq) String() string { return proto.CompactTextString(m) }
func (*GetSchoolRankReq) ProtoMessage()    {}
func (*GetSchoolRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{27}
}
func (m *GetSchoolRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchoolRankReq.Unmarshal(m, b)
}
func (m *GetSchoolRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchoolRankReq.Marshal(b, m, deterministic)
}
func (dst *GetSchoolRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchoolRankReq.Merge(dst, src)
}
func (m *GetSchoolRankReq) XXX_Size() int {
	return xxx_messageInfo_GetSchoolRankReq.Size(m)
}
func (m *GetSchoolRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchoolRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchoolRankReq proto.InternalMessageInfo

func (m *GetSchoolRankReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSchoolRankReq) GetLevel() GetSchoolRankReq_Level {
	if m != nil {
		return m.Level
	}
	return GetSchoolRankReq_level_city
}

type GetSchoolRankResp struct {
	Schools              []*SchoolWithFlower `protobuf:"bytes,1,rep,name=schools,proto3" json:"schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetSchoolRankResp) Reset()         { *m = GetSchoolRankResp{} }
func (m *GetSchoolRankResp) String() string { return proto.CompactTextString(m) }
func (*GetSchoolRankResp) ProtoMessage()    {}
func (*GetSchoolRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{28}
}
func (m *GetSchoolRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchoolRankResp.Unmarshal(m, b)
}
func (m *GetSchoolRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchoolRankResp.Marshal(b, m, deterministic)
}
func (dst *GetSchoolRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchoolRankResp.Merge(dst, src)
}
func (m *GetSchoolRankResp) XXX_Size() int {
	return xxx_messageInfo_GetSchoolRankResp.Size(m)
}
func (m *GetSchoolRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchoolRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchoolRankResp proto.InternalMessageInfo

func (m *GetSchoolRankResp) GetSchools() []*SchoolWithFlower {
	if m != nil {
		return m.Schools
	}
	return nil
}

type SchoolWithFlower struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	CityId               string   `protobuf:"bytes,4,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	CityName             string   `protobuf:"bytes,5,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	Flower               int64    `protobuf:"varint,6,opt,name=flower,proto3" json:"flower,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SchoolWithFlower) Reset()         { *m = SchoolWithFlower{} }
func (m *SchoolWithFlower) String() string { return proto.CompactTextString(m) }
func (*SchoolWithFlower) ProtoMessage()    {}
func (*SchoolWithFlower) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{29}
}
func (m *SchoolWithFlower) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SchoolWithFlower.Unmarshal(m, b)
}
func (m *SchoolWithFlower) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SchoolWithFlower.Marshal(b, m, deterministic)
}
func (dst *SchoolWithFlower) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SchoolWithFlower.Merge(dst, src)
}
func (m *SchoolWithFlower) XXX_Size() int {
	return xxx_messageInfo_SchoolWithFlower.Size(m)
}
func (m *SchoolWithFlower) XXX_DiscardUnknown() {
	xxx_messageInfo_SchoolWithFlower.DiscardUnknown(m)
}

var xxx_messageInfo_SchoolWithFlower proto.InternalMessageInfo

func (m *SchoolWithFlower) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SchoolWithFlower) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SchoolWithFlower) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *SchoolWithFlower) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

func (m *SchoolWithFlower) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *SchoolWithFlower) GetFlower() int64 {
	if m != nil {
		return m.Flower
	}
	return 0
}

type GetMirrorIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMirrorIdReq) Reset()         { *m = GetMirrorIdReq{} }
func (m *GetMirrorIdReq) String() string { return proto.CompactTextString(m) }
func (*GetMirrorIdReq) ProtoMessage()    {}
func (*GetMirrorIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{30}
}
func (m *GetMirrorIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorIdReq.Unmarshal(m, b)
}
func (m *GetMirrorIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorIdReq.Marshal(b, m, deterministic)
}
func (dst *GetMirrorIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorIdReq.Merge(dst, src)
}
func (m *GetMirrorIdReq) XXX_Size() int {
	return xxx_messageInfo_GetMirrorIdReq.Size(m)
}
func (m *GetMirrorIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorIdReq proto.InternalMessageInfo

func (m *GetMirrorIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMirrorIdResp struct {
	MirrorId             string   `protobuf:"bytes,1,opt,name=mirror_id,json=mirrorId,proto3" json:"mirror_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMirrorIdResp) Reset()         { *m = GetMirrorIdResp{} }
func (m *GetMirrorIdResp) String() string { return proto.CompactTextString(m) }
func (*GetMirrorIdResp) ProtoMessage()    {}
func (*GetMirrorIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{31}
}
func (m *GetMirrorIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorIdResp.Unmarshal(m, b)
}
func (m *GetMirrorIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorIdResp.Marshal(b, m, deterministic)
}
func (dst *GetMirrorIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorIdResp.Merge(dst, src)
}
func (m *GetMirrorIdResp) XXX_Size() int {
	return xxx_messageInfo_GetMirrorIdResp.Size(m)
}
func (m *GetMirrorIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorIdResp proto.InternalMessageInfo

func (m *GetMirrorIdResp) GetMirrorId() string {
	if m != nil {
		return m.MirrorId
	}
	return ""
}

type GetMirrorDataReq struct {
	MirrorId             string   `protobuf:"bytes,1,opt,name=mirror_id,json=mirrorId,proto3" json:"mirror_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMirrorDataReq) Reset()         { *m = GetMirrorDataReq{} }
func (m *GetMirrorDataReq) String() string { return proto.CompactTextString(m) }
func (*GetMirrorDataReq) ProtoMessage()    {}
func (*GetMirrorDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{32}
}
func (m *GetMirrorDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorDataReq.Unmarshal(m, b)
}
func (m *GetMirrorDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorDataReq.Marshal(b, m, deterministic)
}
func (dst *GetMirrorDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorDataReq.Merge(dst, src)
}
func (m *GetMirrorDataReq) XXX_Size() int {
	return xxx_messageInfo_GetMirrorDataReq.Size(m)
}
func (m *GetMirrorDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorDataReq proto.InternalMessageInfo

func (m *GetMirrorDataReq) GetMirrorId() string {
	if m != nil {
		return m.MirrorId
	}
	return ""
}

type GetMirrorDataResp struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Account              string              `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Flower               int64               `protobuf:"varint,4,opt,name=flower,proto3" json:"flower,omitempty"`
	School               *School             `protobuf:"bytes,5,opt,name=school,proto3" json:"school,omitempty"`
	UserRankInSchool     int32               `protobuf:"varint,6,opt,name=user_rank_in_school,json=userRankInSchool,proto3" json:"user_rank_in_school,omitempty"`
	SchoolRankInCity     int32               `protobuf:"varint,7,opt,name=school_rank_in_city,json=schoolRankInCity,proto3" json:"school_rank_in_city,omitempty"`
	SchoolRankInCountry  int32               `protobuf:"varint,8,opt,name=school_rank_in_country,json=schoolRankInCountry,proto3" json:"school_rank_in_country,omitempty"`
	ChannelId            uint32              `protobuf:"varint,9,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Users                []*UserWithFlower   `protobuf:"bytes,10,rep,name=users,proto3" json:"users,omitempty"`
	SchoolsInCity        []*SchoolWithFlower `protobuf:"bytes,11,rep,name=schools_in_city,json=schoolsInCity,proto3" json:"schools_in_city,omitempty"`
	Schools              []*SchoolWithFlower `protobuf:"bytes,12,rep,name=schools,proto3" json:"schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetMirrorDataResp) Reset()         { *m = GetMirrorDataResp{} }
func (m *GetMirrorDataResp) String() string { return proto.CompactTextString(m) }
func (*GetMirrorDataResp) ProtoMessage()    {}
func (*GetMirrorDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_listening_rank_ba829d3d362987fa, []int{33}
}
func (m *GetMirrorDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorDataResp.Unmarshal(m, b)
}
func (m *GetMirrorDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorDataResp.Marshal(b, m, deterministic)
}
func (dst *GetMirrorDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorDataResp.Merge(dst, src)
}
func (m *GetMirrorDataResp) XXX_Size() int {
	return xxx_messageInfo_GetMirrorDataResp.Size(m)
}
func (m *GetMirrorDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorDataResp proto.InternalMessageInfo

func (m *GetMirrorDataResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMirrorDataResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetMirrorDataResp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetMirrorDataResp) GetFlower() int64 {
	if m != nil {
		return m.Flower
	}
	return 0
}

func (m *GetMirrorDataResp) GetSchool() *School {
	if m != nil {
		return m.School
	}
	return nil
}

func (m *GetMirrorDataResp) GetUserRankInSchool() int32 {
	if m != nil {
		return m.UserRankInSchool
	}
	return 0
}

func (m *GetMirrorDataResp) GetSchoolRankInCity() int32 {
	if m != nil {
		return m.SchoolRankInCity
	}
	return 0
}

func (m *GetMirrorDataResp) GetSchoolRankInCountry() int32 {
	if m != nil {
		return m.SchoolRankInCountry
	}
	return 0
}

func (m *GetMirrorDataResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMirrorDataResp) GetUsers() []*UserWithFlower {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *GetMirrorDataResp) GetSchoolsInCity() []*SchoolWithFlower {
	if m != nil {
		return m.SchoolsInCity
	}
	return nil
}

func (m *GetMirrorDataResp) GetSchools() []*SchoolWithFlower {
	if m != nil {
		return m.Schools
	}
	return nil
}

func init() {
	proto.RegisterType((*GeoReq)(nil), "muse_listening_rank.GeoReq")
	proto.RegisterType((*GeoResp)(nil), "muse_listening_rank.GeoResp")
	proto.RegisterType((*Province)(nil), "muse_listening_rank.Province")
	proto.RegisterType((*City)(nil), "muse_listening_rank.City")
	proto.RegisterType((*ListSchoolReq)(nil), "muse_listening_rank.ListSchoolReq")
	proto.RegisterType((*ListSchoolResp)(nil), "muse_listening_rank.ListSchoolResp")
	proto.RegisterType((*PreChannelSchoolBindReq)(nil), "muse_listening_rank.PreChannelSchoolBindReq")
	proto.RegisterType((*PreChannelSchoolBindResp)(nil), "muse_listening_rank.PreChannelSchoolBindResp")
	proto.RegisterType((*DelChannelSchoolBindReq)(nil), "muse_listening_rank.DelChannelSchoolBindReq")
	proto.RegisterType((*DelChannelSchoolBindResp)(nil), "muse_listening_rank.DelChannelSchoolBindResp")
	proto.RegisterType((*UpdateChannelSchoolBindReq)(nil), "muse_listening_rank.UpdateChannelSchoolBindReq")
	proto.RegisterType((*UpdateChannelSchoolBindResp)(nil), "muse_listening_rank.UpdateChannelSchoolBindResp")
	proto.RegisterType((*ChannelSchoolBind)(nil), "muse_listening_rank.ChannelSchoolBind")
	proto.RegisterType((*UpdateSchoolsReq)(nil), "muse_listening_rank.UpdateSchoolsReq")
	proto.RegisterType((*UpdateSchoolsResp)(nil), "muse_listening_rank.UpdateSchoolsResp")
	proto.RegisterType((*ListChannelWithSchoolReq)(nil), "muse_listening_rank.ListChannelWithSchoolReq")
	proto.RegisterType((*ListChannelWithSchoolResp)(nil), "muse_listening_rank.ListChannelWithSchoolResp")
	proto.RegisterType((*ListChannelSchoolItem)(nil), "muse_listening_rank.ListChannelSchoolItem")
	proto.RegisterType((*ChannelSchool)(nil), "muse_listening_rank.ChannelSchool")
	proto.RegisterType((*AddFlowerReq)(nil), "muse_listening_rank.AddFlowerReq")
	proto.RegisterType((*AddFlowerResp)(nil), "muse_listening_rank.AddFlowerResp")
	proto.RegisterType((*GetUserInfoReq)(nil), "muse_listening_rank.GetUserInfoReq")
	proto.RegisterType((*GetUserInfoResp)(nil), "muse_listening_rank.GetUserInfoResp")
	proto.RegisterType((*School)(nil), "muse_listening_rank.School")
	proto.RegisterType((*GetUserRankInSchoolReq)(nil), "muse_listening_rank.GetUserRankInSchoolReq")
	proto.RegisterType((*UserWithFlower)(nil), "muse_listening_rank.UserWithFlower")
	proto.RegisterType((*GetUserRankInSchoolResp)(nil), "muse_listening_rank.GetUserRankInSchoolResp")
	proto.RegisterType((*GetSchoolRankReq)(nil), "muse_listening_rank.GetSchoolRankReq")
	proto.RegisterType((*GetSchoolRankResp)(nil), "muse_listening_rank.GetSchoolRankResp")
	proto.RegisterType((*SchoolWithFlower)(nil), "muse_listening_rank.SchoolWithFlower")
	proto.RegisterType((*GetMirrorIdReq)(nil), "muse_listening_rank.GetMirrorIdReq")
	proto.RegisterType((*GetMirrorIdResp)(nil), "muse_listening_rank.GetMirrorIdResp")
	proto.RegisterType((*GetMirrorDataReq)(nil), "muse_listening_rank.GetMirrorDataReq")
	proto.RegisterType((*GetMirrorDataResp)(nil), "muse_listening_rank.GetMirrorDataResp")
	proto.RegisterEnum("muse_listening_rank.GetSchoolRankReq_Level", GetSchoolRankReq_Level_name, GetSchoolRankReq_Level_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MuseListeningRankClient is the client API for MuseListeningRank service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MuseListeningRankClient interface {
	GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error)
	GetUserRankInSchool(ctx context.Context, in *GetUserRankInSchoolReq, opts ...grpc.CallOption) (*GetUserRankInSchoolResp, error)
	GetSchoolRank(ctx context.Context, in *GetSchoolRankReq, opts ...grpc.CallOption) (*GetSchoolRankResp, error)
	GetMirrorId(ctx context.Context, in *GetMirrorIdReq, opts ...grpc.CallOption) (*GetMirrorIdResp, error)
	GetMirrorData(ctx context.Context, in *GetMirrorDataReq, opts ...grpc.CallOption) (*GetMirrorDataResp, error)
	AddFlower(ctx context.Context, in *AddFlowerReq, opts ...grpc.CallOption) (*AddFlowerResp, error)
	ListChannelWithSchool(ctx context.Context, in *ListChannelWithSchoolReq, opts ...grpc.CallOption) (*ListChannelWithSchoolResp, error)
	UpdateChannelSchoolBind(ctx context.Context, in *UpdateChannelSchoolBindReq, opts ...grpc.CallOption) (*UpdateChannelSchoolBindResp, error)
	DelChannelSchoolBind(ctx context.Context, in *DelChannelSchoolBindReq, opts ...grpc.CallOption) (*DelChannelSchoolBindResp, error)
	PreChannelSchoolBind(ctx context.Context, in *PreChannelSchoolBindReq, opts ...grpc.CallOption) (*PreChannelSchoolBindResp, error)
	ListSchool(ctx context.Context, in *ListSchoolReq, opts ...grpc.CallOption) (*ListSchoolResp, error)
	UpdateSchools(ctx context.Context, in *UpdateSchoolsReq, opts ...grpc.CallOption) (*UpdateSchoolsResp, error)
	Geo(ctx context.Context, in *GeoReq, opts ...grpc.CallOption) (*GeoResp, error)
}

type museListeningRankClient struct {
	cc *grpc.ClientConn
}

func NewMuseListeningRankClient(cc *grpc.ClientConn) MuseListeningRankClient {
	return &museListeningRankClient{cc}
}

func (c *museListeningRankClient) GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error) {
	out := new(GetUserInfoResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/GetUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) GetUserRankInSchool(ctx context.Context, in *GetUserRankInSchoolReq, opts ...grpc.CallOption) (*GetUserRankInSchoolResp, error) {
	out := new(GetUserRankInSchoolResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/GetUserRankInSchool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) GetSchoolRank(ctx context.Context, in *GetSchoolRankReq, opts ...grpc.CallOption) (*GetSchoolRankResp, error) {
	out := new(GetSchoolRankResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/GetSchoolRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) GetMirrorId(ctx context.Context, in *GetMirrorIdReq, opts ...grpc.CallOption) (*GetMirrorIdResp, error) {
	out := new(GetMirrorIdResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/GetMirrorId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) GetMirrorData(ctx context.Context, in *GetMirrorDataReq, opts ...grpc.CallOption) (*GetMirrorDataResp, error) {
	out := new(GetMirrorDataResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/GetMirrorData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) AddFlower(ctx context.Context, in *AddFlowerReq, opts ...grpc.CallOption) (*AddFlowerResp, error) {
	out := new(AddFlowerResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/AddFlower", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) ListChannelWithSchool(ctx context.Context, in *ListChannelWithSchoolReq, opts ...grpc.CallOption) (*ListChannelWithSchoolResp, error) {
	out := new(ListChannelWithSchoolResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/ListChannelWithSchool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) UpdateChannelSchoolBind(ctx context.Context, in *UpdateChannelSchoolBindReq, opts ...grpc.CallOption) (*UpdateChannelSchoolBindResp, error) {
	out := new(UpdateChannelSchoolBindResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/UpdateChannelSchoolBind", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) DelChannelSchoolBind(ctx context.Context, in *DelChannelSchoolBindReq, opts ...grpc.CallOption) (*DelChannelSchoolBindResp, error) {
	out := new(DelChannelSchoolBindResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/DelChannelSchoolBind", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) PreChannelSchoolBind(ctx context.Context, in *PreChannelSchoolBindReq, opts ...grpc.CallOption) (*PreChannelSchoolBindResp, error) {
	out := new(PreChannelSchoolBindResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/PreChannelSchoolBind", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) ListSchool(ctx context.Context, in *ListSchoolReq, opts ...grpc.CallOption) (*ListSchoolResp, error) {
	out := new(ListSchoolResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/ListSchool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) UpdateSchools(ctx context.Context, in *UpdateSchoolsReq, opts ...grpc.CallOption) (*UpdateSchoolsResp, error) {
	out := new(UpdateSchoolsResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/UpdateSchools", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museListeningRankClient) Geo(ctx context.Context, in *GeoReq, opts ...grpc.CallOption) (*GeoResp, error) {
	out := new(GeoResp)
	err := c.cc.Invoke(ctx, "/muse_listening_rank.MuseListeningRank/Geo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MuseListeningRankServer is the server API for MuseListeningRank service.
type MuseListeningRankServer interface {
	GetUserInfo(context.Context, *GetUserInfoReq) (*GetUserInfoResp, error)
	GetUserRankInSchool(context.Context, *GetUserRankInSchoolReq) (*GetUserRankInSchoolResp, error)
	GetSchoolRank(context.Context, *GetSchoolRankReq) (*GetSchoolRankResp, error)
	GetMirrorId(context.Context, *GetMirrorIdReq) (*GetMirrorIdResp, error)
	GetMirrorData(context.Context, *GetMirrorDataReq) (*GetMirrorDataResp, error)
	AddFlower(context.Context, *AddFlowerReq) (*AddFlowerResp, error)
	ListChannelWithSchool(context.Context, *ListChannelWithSchoolReq) (*ListChannelWithSchoolResp, error)
	UpdateChannelSchoolBind(context.Context, *UpdateChannelSchoolBindReq) (*UpdateChannelSchoolBindResp, error)
	DelChannelSchoolBind(context.Context, *DelChannelSchoolBindReq) (*DelChannelSchoolBindResp, error)
	PreChannelSchoolBind(context.Context, *PreChannelSchoolBindReq) (*PreChannelSchoolBindResp, error)
	ListSchool(context.Context, *ListSchoolReq) (*ListSchoolResp, error)
	UpdateSchools(context.Context, *UpdateSchoolsReq) (*UpdateSchoolsResp, error)
	Geo(context.Context, *GeoReq) (*GeoResp, error)
}

func RegisterMuseListeningRankServer(s *grpc.Server, srv MuseListeningRankServer) {
	s.RegisterService(&_MuseListeningRank_serviceDesc, srv)
}

func _MuseListeningRank_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/GetUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).GetUserInfo(ctx, req.(*GetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_GetUserRankInSchool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRankInSchoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).GetUserRankInSchool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/GetUserRankInSchool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).GetUserRankInSchool(ctx, req.(*GetUserRankInSchoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_GetSchoolRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSchoolRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).GetSchoolRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/GetSchoolRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).GetSchoolRank(ctx, req.(*GetSchoolRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_GetMirrorId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMirrorIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).GetMirrorId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/GetMirrorId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).GetMirrorId(ctx, req.(*GetMirrorIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_GetMirrorData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMirrorDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).GetMirrorData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/GetMirrorData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).GetMirrorData(ctx, req.(*GetMirrorDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_AddFlower_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFlowerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).AddFlower(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/AddFlower",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).AddFlower(ctx, req.(*AddFlowerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_ListChannelWithSchool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChannelWithSchoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).ListChannelWithSchool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/ListChannelWithSchool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).ListChannelWithSchool(ctx, req.(*ListChannelWithSchoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_UpdateChannelSchoolBind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChannelSchoolBindReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).UpdateChannelSchoolBind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/UpdateChannelSchoolBind",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).UpdateChannelSchoolBind(ctx, req.(*UpdateChannelSchoolBindReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_DelChannelSchoolBind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelChannelSchoolBindReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).DelChannelSchoolBind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/DelChannelSchoolBind",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).DelChannelSchoolBind(ctx, req.(*DelChannelSchoolBindReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_PreChannelSchoolBind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreChannelSchoolBindReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).PreChannelSchoolBind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/PreChannelSchoolBind",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).PreChannelSchoolBind(ctx, req.(*PreChannelSchoolBindReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_ListSchool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSchoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).ListSchool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/ListSchool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).ListSchool(ctx, req.(*ListSchoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_UpdateSchools_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSchoolsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).UpdateSchools(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/UpdateSchools",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).UpdateSchools(ctx, req.(*UpdateSchoolsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseListeningRank_Geo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GeoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseListeningRankServer).Geo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_listening_rank.MuseListeningRank/Geo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseListeningRankServer).Geo(ctx, req.(*GeoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MuseListeningRank_serviceDesc = grpc.ServiceDesc{
	ServiceName: "muse_listening_rank.MuseListeningRank",
	HandlerType: (*MuseListeningRankServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserInfo",
			Handler:    _MuseListeningRank_GetUserInfo_Handler,
		},
		{
			MethodName: "GetUserRankInSchool",
			Handler:    _MuseListeningRank_GetUserRankInSchool_Handler,
		},
		{
			MethodName: "GetSchoolRank",
			Handler:    _MuseListeningRank_GetSchoolRank_Handler,
		},
		{
			MethodName: "GetMirrorId",
			Handler:    _MuseListeningRank_GetMirrorId_Handler,
		},
		{
			MethodName: "GetMirrorData",
			Handler:    _MuseListeningRank_GetMirrorData_Handler,
		},
		{
			MethodName: "AddFlower",
			Handler:    _MuseListeningRank_AddFlower_Handler,
		},
		{
			MethodName: "ListChannelWithSchool",
			Handler:    _MuseListeningRank_ListChannelWithSchool_Handler,
		},
		{
			MethodName: "UpdateChannelSchoolBind",
			Handler:    _MuseListeningRank_UpdateChannelSchoolBind_Handler,
		},
		{
			MethodName: "DelChannelSchoolBind",
			Handler:    _MuseListeningRank_DelChannelSchoolBind_Handler,
		},
		{
			MethodName: "PreChannelSchoolBind",
			Handler:    _MuseListeningRank_PreChannelSchoolBind_Handler,
		},
		{
			MethodName: "ListSchool",
			Handler:    _MuseListeningRank_ListSchool_Handler,
		},
		{
			MethodName: "UpdateSchools",
			Handler:    _MuseListeningRank_UpdateSchools_Handler,
		},
		{
			MethodName: "Geo",
			Handler:    _MuseListeningRank_Geo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "muse-listening-rank/muse-listening-rank.proto",
}

func init() {
	proto.RegisterFile("muse-listening-rank/muse-listening-rank.proto", fileDescriptor_muse_listening_rank_ba829d3d362987fa)
}

var fileDescriptor_muse_listening_rank_ba829d3d362987fa = []byte{
	// 1372 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0x4d, 0x6f, 0xdb, 0x46,
	0x13, 0x7e, 0xf5, 0x2d, 0x8d, 0x2d, 0x59, 0x5e, 0x3b, 0xb6, 0x22, 0xc5, 0x78, 0x93, 0x75, 0x13,
	0xb8, 0x4e, 0x24, 0x05, 0x36, 0x82, 0x36, 0x45, 0xd1, 0xd6, 0x8e, 0x51, 0x43, 0x40, 0x9c, 0x1a,
	0x74, 0x8c, 0x16, 0x29, 0x50, 0x81, 0x26, 0xd7, 0x36, 0x63, 0x89, 0x64, 0xb8, 0x94, 0x0b, 0x03,
	0x3d, 0xf4, 0x3f, 0x14, 0x68, 0x2f, 0xed, 0xb5, 0xff, 0xa1, 0xf7, 0xde, 0xfb, 0x97, 0x8a, 0xfd,
	0xa0, 0xf8, 0xe1, 0xa5, 0x25, 0xc5, 0x45, 0x4f, 0xbd, 0x71, 0x67, 0xe6, 0xd9, 0x99, 0x9d, 0x79,
	0x66, 0x48, 0x2e, 0xb4, 0x87, 0x23, 0x4a, 0xda, 0x03, 0x8b, 0xfa, 0xc4, 0xb6, 0xec, 0xb3, 0xb6,
	0xa7, 0xdb, 0x17, 0x5d, 0x85, 0xac, 0xe3, 0x7a, 0x8e, 0xef, 0xa0, 0x25, 0xa6, 0xea, 0x8f, 0x55,
	0x7d, 0xa6, 0xc2, 0x65, 0x28, 0xee, 0x13, 0x47, 0x23, 0xef, 0xf0, 0x1e, 0x94, 0xf8, 0x13, 0x75,
	0xd1, 0x73, 0x28, 0xbb, 0x9e, 0x73, 0x69, 0xd9, 0x06, 0x69, 0x64, 0xee, 0xe7, 0x36, 0xe6, 0xb6,
	0xd6, 0x3a, 0x0a, 0x70, 0xe7, 0x50, 0x1a, 0x69, 0x63, 0x73, 0xac, 0x43, 0x39, 0x90, 0x22, 0x04,
	0x79, 0xc3, 0x31, 0xd9, 0x16, 0x99, 0x8d, 0x8a, 0xc6, 0x9f, 0x99, 0xcc, 0xd6, 0x87, 0xa4, 0x91,
	0x15, 0x32, 0xf6, 0x8c, 0xda, 0x90, 0x37, 0x2c, 0xff, 0xaa, 0x91, 0xe3, 0xae, 0xee, 0x2a, 0x5d,
	0xbd, 0xb0, 0xfc, 0x2b, 0x8d, 0x9b, 0xe1, 0x0e, 0xe4, 0xd9, 0x6a, 0xda, 0xed, 0xf1, 0x53, 0xa8,
	0xbe, 0xb4, 0xa8, 0x7f, 0x64, 0x9c, 0x3b, 0xce, 0x40, 0x23, 0xef, 0xd0, 0xff, 0x61, 0x8e, 0xf2,
	0x45, 0x9f, 0xdb, 0x0a, 0x3c, 0x08, 0xd1, 0x2b, 0x86, 0x78, 0x05, 0xb5, 0x28, 0x82, 0xba, 0xe8,
	0x53, 0x28, 0x09, 0x3d, 0x95, 0x09, 0xc1, 0xea, 0x28, 0xcf, 0x75, 0xdb, 0x26, 0x03, 0x09, 0x0c,
	0x20, 0xb8, 0x0b, 0xab, 0x87, 0x1e, 0x89, 0x29, 0x77, 0x2d, 0xdb, 0x64, 0xb1, 0x2c, 0x43, 0xc1,
	0xf7, 0x2d, 0x53, 0x6c, 0x5b, 0xd1, 0xc4, 0x02, 0xbf, 0x81, 0x86, 0x1a, 0x40, 0x5d, 0xf4, 0x19,
	0xe4, 0x2d, 0x9f, 0x0c, 0x65, 0x1c, 0x9b, 0xca, 0x38, 0x58, 0xf4, 0x31, 0x74, 0xcf, 0x27, 0x43,
	0x8d, 0xe3, 0xf0, 0x87, 0xb0, 0xba, 0x47, 0x06, 0xca, 0x60, 0x6a, 0x90, 0xb5, 0x4c, 0x99, 0x8f,
	0xac, 0x65, 0xe2, 0x26, 0x34, 0xd4, 0xa6, 0xd4, 0xc5, 0x43, 0x68, 0x1e, 0xbb, 0xa6, 0xee, 0xab,
	0x8f, 0xf5, 0x15, 0x2c, 0x18, 0x42, 0xde, 0x8f, 0xe7, 0xed, 0xd1, 0xe4, 0xbc, 0xf1, 0x3d, 0x6a,
	0x46, 0x54, 0x44, 0xf1, 0x1a, 0xb4, 0x52, 0xdd, 0x51, 0x17, 0xbf, 0x86, 0xc5, 0x6b, 0x8a, 0xe4,
	0x71, 0x18, 0x39, 0x58, 0x7a, 0x03, 0x72, 0xb0, 0x67, 0xd4, 0x82, 0x8a, 0xe4, 0x82, 0x65, 0x36,
	0x72, 0x5c, 0x51, 0x16, 0x82, 0x9e, 0x89, 0x0f, 0xa1, 0x2e, 0x9c, 0xca, 0x28, 0xd8, 0xc9, 0x6e,
	0xc7, 0x84, 0x1d, 0x58, 0x4c, 0xec, 0x48, 0x5d, 0xf4, 0x04, 0xd0, 0xa9, 0x6e, 0x0d, 0x88, 0xd9,
	0x8f, 0xd3, 0x92, 0x11, 0xa2, 0x2e, 0x34, 0x47, 0x21, 0x39, 0xff, 0xcc, 0x40, 0x23, 0x52, 0xdf,
	0xaf, 0x2d, 0xff, 0x3c, 0xa4, 0x76, 0x70, 0xc4, 0x4c, 0xe4, 0x88, 0x6b, 0x00, 0x41, 0x2d, 0xe4,
	0xe1, 0xab, 0x5a, 0x45, 0x4a, 0x7a, 0x26, 0x7a, 0x00, 0xf3, 0x81, 0x9a, 0xfb, 0x15, 0x49, 0x98,
	0x93, 0x32, 0xe6, 0x32, 0xd9, 0x30, 0xf9, 0x64, 0xc3, 0xb0, 0x2c, 0x3a, 0xa7, 0xa7, 0x94, 0xf8,
	0xcc, 0x43, 0x41, 0x64, 0x51, 0x08, 0x7a, 0x26, 0xa3, 0xf8, 0xc0, 0x1a, 0x5a, 0x7e, 0xa3, 0xc8,
	0x5d, 0x8b, 0x05, 0xfe, 0x16, 0xee, 0xa6, 0x9c, 0xe2, 0x1f, 0xe0, 0xf8, 0x1f, 0x19, 0xb8, 0xa3,
	0xd4, 0x4f, 0xc5, 0x89, 0x78, 0xc2, 0x72, 0x93, 0x12, 0x96, 0xbf, 0x9e, 0xb0, 0x4f, 0xa0, 0x28,
	0xb2, 0xc3, 0x93, 0x31, 0x1d, 0x47, 0x24, 0x02, 0xff, 0x92, 0x85, 0x6a, 0x4c, 0x73, 0x2d, 0xe6,
	0x18, 0x67, 0xb3, 0x71, 0xce, 0x26, 0x6b, 0x95, 0xbb, 0x56, 0xab, 0x55, 0x28, 0xb1, 0x31, 0xca,
	0xb0, 0x22, 0xf2, 0x22, 0x5b, 0xf6, 0xf8, 0xb6, 0x5c, 0xc1, 0x71, 0xb2, 0x88, 0x4c, 0x10, 0x50,
	0x20, 0x98, 0xf1, 0x0c, 0x59, 0x14, 0xdb, 0x06, 0xa2, 0x9e, 0x89, 0xd6, 0xa1, 0x3a, 0x36, 0xe0,
	0x3b, 0x94, 0xb8, 0xc9, 0x7c, 0x20, 0x4c, 0x10, 0x89, 0x4f, 0xee, 0x72, 0x34, 0xb8, 0x17, 0x6c,
	0x7e, 0xb7, 0xa0, 0x62, 0xd1, 0xfe, 0x89, 0x65, 0x9b, 0xc4, 0x6c, 0x54, 0xee, 0x67, 0x36, 0xca,
	0x5a, 0xd9, 0xa2, 0xbb, 0x7c, 0x8d, 0x3f, 0x86, 0xf9, 0x1d, 0xd3, 0xfc, 0x72, 0xe0, 0x7c, 0x4f,
	0x3c, 0x46, 0xf6, 0x3a, 0xe4, 0x46, 0x32, 0x31, 0x55, 0x8d, 0x3d, 0xa2, 0x15, 0x28, 0x9e, 0x72,
	0x35, 0x4f, 0x4b, 0x4e, 0x93, 0x2b, 0xfc, 0x1c, 0xaa, 0x11, 0x24, 0x75, 0x67, 0x80, 0x62, 0xa8,
	0xed, 0x13, 0xff, 0x98, 0x12, 0xaf, 0x67, 0x9f, 0x3a, 0x4a, 0xb7, 0xf8, 0xaf, 0x2c, 0x2c, 0xc4,
	0x8c, 0x94, 0x1e, 0x54, 0xaf, 0xbe, 0x06, 0x94, 0x74, 0xc3, 0x70, 0x46, 0xb6, 0x2f, 0x2b, 0x15,
	0x2c, 0x23, 0xf1, 0xe4, 0xa3, 0xf1, 0xa0, 0xed, 0x04, 0xb5, 0x5a, 0x4a, 0x6a, 0xc5, 0x39, 0x85,
	0xda, 0xb0, 0x34, 0xa2, 0xc4, 0xe3, 0xba, 0xbe, 0x65, 0xcb, 0x39, 0xc3, 0xab, 0x58, 0xd0, 0xea,
	0x4c, 0xa5, 0xe9, 0xf6, 0x45, 0xcf, 0x3e, 0x1a, 0x9b, 0xcb, 0x32, 0x05, 0x00, 0xfe, 0x7e, 0x2e,
	0x09, 0x73, 0xa1, 0x12, 0x00, 0xfe, 0x22, 0xde, 0x86, 0x95, 0xa4, 0x39, 0x3b, 0x82, 0x77, 0xc5,
	0x0b, 0x5c, 0xd0, 0x96, 0x62, 0x08, 0xa1, 0x42, 0xf7, 0x20, 0x6c, 0x29, 0x5e, 0xe9, 0x68, 0x8f,
	0xe1, 0x1f, 0xb3, 0x50, 0x4c, 0x61, 0xbf, 0x2a, 0x8d, 0x75, 0xc8, 0x59, 0xc3, 0x33, 0x99, 0x42,
	0xf6, 0xf8, 0x9e, 0x2c, 0x6f, 0x41, 0xc5, 0x23, 0xba, 0x6c, 0x1d, 0xc1, 0xf1, 0x32, 0x13, 0x70,
	0xe5, 0x32, 0x14, 0xdc, 0x73, 0xc7, 0x0e, 0x98, 0x2d, 0x16, 0x9c, 0xd2, 0xce, 0xc8, 0x33, 0x44,
	0x09, 0x1a, 0x65, 0x3e, 0xb5, 0x41, 0x88, 0xd8, 0x08, 0x42, 0x77, 0xa0, 0xe8, 0x9c, 0xbc, 0x65,
	0x81, 0x54, 0x04, 0xce, 0x39, 0x79, 0x1b, 0xeb, 0x53, 0xde, 0x0a, 0x90, 0x6c, 0x05, 0xbc, 0x09,
	0x2b, 0x92, 0x53, 0xd1, 0xda, 0xa8, 0x09, 0xf8, 0x5b, 0x06, 0x6a, 0xcc, 0x92, 0x8d, 0x51, 0xc1,
	0xf2, 0x5b, 0xf3, 0x2f, 0xe4, 0x59, 0x7e, 0x7a, 0x9e, 0x85, 0xa4, 0x2d, 0xc4, 0x9a, 0xe8, 0x35,
	0xac, 0x2a, 0xcf, 0xc2, 0xbf, 0x35, 0x0b, 0x8c, 0x7f, 0xc1, 0xdb, 0x74, 0x5d, 0xe9, 0x26, 0x7e,
	0x36, 0x4d, 0x20, 0xf0, 0x4f, 0x19, 0xa8, 0xef, 0x93, 0xe0, 0x33, 0x4d, 0xb7, 0x2f, 0xd4, 0x43,
	0x61, 0x07, 0x0a, 0x03, 0x72, 0x49, 0x06, 0xfc, 0xe0, 0xb5, 0xad, 0xc7, 0x4a, 0x0f, 0xc9, 0x7d,
	0x3a, 0x2f, 0x19, 0x44, 0x13, 0x48, 0xbc, 0x09, 0x05, 0xbe, 0x46, 0x35, 0x00, 0x2e, 0xe1, 0x0d,
	0x51, 0xff, 0x1f, 0x5a, 0x84, 0xaa, 0x5c, 0x0b, 0x5a, 0xd7, 0x33, 0xec, 0x53, 0x24, 0xb1, 0x19,
	0x75, 0xd1, 0xe7, 0xc9, 0xaf, 0x86, 0x87, 0x37, 0xa4, 0x33, 0x72, 0xd2, 0xf1, 0x87, 0xc3, 0xcf,
	0x19, 0xa8, 0x27, 0xb5, 0xff, 0x6a, 0x6b, 0x84, 0xa5, 0x2d, 0x2a, 0xe6, 0xe3, 0x81, 0xe5, 0x79,
	0x8e, 0xd7, 0x33, 0xd5, 0xf4, 0xec, 0xf0, 0xf1, 0x18, 0xda, 0x50, 0x97, 0xf9, 0x1a, 0xf2, 0x75,
	0x7f, 0x7c, 0x82, 0xf2, 0x50, 0x1a, 0xe0, 0x2e, 0xaf, 0xab, 0xb0, 0xdf, 0xd3, 0x7d, 0x9d, 0xed,
	0x7a, 0x23, 0xe0, 0xd7, 0x3c, 0x4f, 0x7a, 0x14, 0xf1, 0xdf, 0x08, 0xbe, 0xdd, 0x08, 0x0e, 0x1b,
	0x13, 0x66, 0x6d, 0x4c, 0x74, 0x00, 0x0b, 0x92, 0xb7, 0xe3, 0xc0, 0xe7, 0x66, 0x61, 0x7d, 0x55,
	0xa2, 0xe5, 0xe1, 0x22, 0xcd, 0x33, 0xff, 0x3e, 0xcd, 0xb3, 0xf5, 0x3b, 0xc0, 0xe2, 0xc1, 0x88,
	0xf2, 0x79, 0xcc, 0x01, 0x2c, 0x0f, 0xe8, 0x1b, 0x98, 0x8b, 0xbc, 0xb4, 0xd1, 0x7a, 0xda, 0x5c,
	0x88, 0xbc, 0xfb, 0x9b, 0x1f, 0x4c, 0x36, 0xa2, 0x2e, 0x72, 0x61, 0x49, 0x31, 0xee, 0xd0, 0xe3,
	0x9b, 0xc0, 0x89, 0x21, 0xdf, 0x7c, 0x32, 0xbd, 0x31, 0x75, 0xd1, 0x77, 0x50, 0x8d, 0x0d, 0x1d,
	0xf4, 0x70, 0xaa, 0x29, 0xd7, 0x7c, 0x34, 0x8d, 0x19, 0x75, 0x65, 0xae, 0x82, 0x0e, 0x4e, 0xcf,
	0x55, 0x64, 0x0e, 0xa4, 0xe7, 0x2a, 0x36, 0x08, 0x44, 0xe4, 0x61, 0xe7, 0xa6, 0x47, 0x1e, 0x9b,
	0x07, 0xe9, 0x91, 0x27, 0x86, 0x80, 0x06, 0x95, 0xf1, 0xa7, 0x1f, 0x7a, 0xa0, 0x04, 0x45, 0x3f,
	0x2a, 0x9b, 0x78, 0x92, 0x09, 0x75, 0xd1, 0x65, 0xec, 0xef, 0x22, 0xfc, 0x77, 0x41, 0xed, 0x49,
	0x7f, 0x2a, 0xb1, 0xbf, 0xb5, 0x66, 0x67, 0x16, 0x73, 0xea, 0xa2, 0x1f, 0x60, 0x35, 0xe5, 0x27,
	0x18, 0x75, 0xd5, 0xed, 0x99, 0xfa, 0x87, 0xde, 0x7c, 0x3a, 0x1b, 0x80, 0xba, 0x88, 0xc2, 0xb2,
	0xea, 0x36, 0x00, 0xa9, 0x99, 0x9a, 0x72, 0xc7, 0xd0, 0x6c, 0xcf, 0x60, 0x2d, 0x9c, 0xaa, 0x6e,
	0x42, 0x52, 0x9c, 0xa6, 0xdc, 0xb2, 0xa4, 0x38, 0x4d, 0xbd, 0x62, 0x39, 0x06, 0x08, 0xef, 0x7f,
	0x10, 0x4e, 0xad, 0x52, 0x58, 0xc9, 0xf5, 0x89, 0x36, 0x82, 0xea, 0xb1, 0x9f, 0xff, 0x14, 0xaa,
	0x27, 0xaf, 0x1c, 0x52, 0xa8, 0x7e, 0xfd, 0x1e, 0xe1, 0x0b, 0xc8, 0xed, 0x13, 0x07, 0xb5, 0x52,
	0x3a, 0x83, 0x0f, 0xb0, 0x7b, 0xe9, 0x4a, 0xea, 0xee, 0x7e, 0xf4, 0xe6, 0xd9, 0x99, 0x33, 0xd0,
	0xed, 0xb3, 0xce, 0xb3, 0x2d, 0xdf, 0xef, 0x18, 0xce, 0xb0, 0xcb, 0xef, 0x0e, 0x0d, 0x67, 0xd0,
	0xa5, 0xc4, 0xbb, 0xb4, 0x0c, 0x42, 0x55, 0x37, 0x8c, 0x27, 0x45, 0x6e, 0xb6, 0xfd, 0x77, 0x00,
	0x00, 0x00, 0xff, 0xff, 0xfb, 0xd4, 0xd0, 0x8a, 0x93, 0x14, 0x00, 0x00,
}
