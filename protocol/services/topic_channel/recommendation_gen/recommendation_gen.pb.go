// Code generated by protoc-gen-go. DO NOT EDIT.
// source: topic_channel/recommendation_gen.proto

package recommendation_gen // import "golang.52tt.com/protocol/services/topic_channel/recommendation_gen"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import common "golang.52tt.com/protocol/services/rcmd/common"
import rcmd_channel_label "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label"
import recommendation_common "golang.52tt.com/protocol/services/topic_channel/recommendation_common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// IM页房间推荐分组
type IMChannelListABGroup int32

const (
	IMChannelListABGroup_DEFAULT             IMChannelListABGroup = 0
	IMChannelListABGroup_RegLess72Hour_Exp_A IMChannelListABGroup = 1
	IMChannelListABGroup_RegLess72Hour_Exp_B IMChannelListABGroup = 2
	IMChannelListABGroup_RegMore72Hour_Exp   IMChannelListABGroup = 3
)

var IMChannelListABGroup_name = map[int32]string{
	0: "DEFAULT",
	1: "RegLess72Hour_Exp_A",
	2: "RegLess72Hour_Exp_B",
	3: "RegMore72Hour_Exp",
}
var IMChannelListABGroup_value = map[string]int32{
	"DEFAULT":             0,
	"RegLess72Hour_Exp_A": 1,
	"RegLess72Hour_Exp_B": 2,
	"RegMore72Hour_Exp":   3,
}

func (x IMChannelListABGroup) String() string {
	return proto.EnumName(IMChannelListABGroup_name, int32(x))
}
func (IMChannelListABGroup) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{0}
}

type REGULATORY_LEVEL int32

const (
	REGULATORY_LEVEL_FREE         REGULATORY_LEVEL = 0
	REGULATORY_LEVEL_SIMPLE_MINOR REGULATORY_LEVEL = 1
)

var REGULATORY_LEVEL_name = map[int32]string{
	0: "FREE",
	1: "SIMPLE_MINOR",
}
var REGULATORY_LEVEL_value = map[string]int32{
	"FREE":         0,
	"SIMPLE_MINOR": 1,
}

func (x REGULATORY_LEVEL) String() string {
	return proto.EnumName(REGULATORY_LEVEL_name, int32(x))
}
func (REGULATORY_LEVEL) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{1}
}

type PrefGameLabelType int32

const (
	PrefGameLabelType_SYSTEM PrefGameLabelType = 0
	PrefGameLabelType_CUSTOM PrefGameLabelType = 1
)

var PrefGameLabelType_name = map[int32]string{
	0: "SYSTEM",
	1: "CUSTOM",
}
var PrefGameLabelType_value = map[string]int32{
	"SYSTEM": 0,
	"CUSTOM": 1,
}

func (x PrefGameLabelType) String() string {
	return proto.EnumName(PrefGameLabelType_name, int32(x))
}
func (PrefGameLabelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{2}
}

type BusinessBlockEnum int32

const (
	BusinessBlockEnum_RoomMode      BusinessBlockEnum = 0
	BusinessBlockEnum_GameCondition BusinessBlockEnum = 1
)

var BusinessBlockEnum_name = map[int32]string{
	0: "RoomMode",
	1: "GameCondition",
}
var BusinessBlockEnum_value = map[string]int32{
	"RoomMode":      0,
	"GameCondition": 1,
}

func (x BusinessBlockEnum) String() string {
	return proto.EnumName(BusinessBlockEnum_name, int32(x))
}
func (BusinessBlockEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{3}
}

type BusinessElemEnum int32

const (
	BusinessElemEnum_NoLimit BusinessElemEnum = 0
	BusinessElemEnum_Single  BusinessElemEnum = 1
	BusinessElemEnum_Double  BusinessElemEnum = 2
	BusinessElemEnum_Waiting BusinessElemEnum = 3
	BusinessElemEnum_Started BusinessElemEnum = 4
)

var BusinessElemEnum_name = map[int32]string{
	0: "NoLimit",
	1: "Single",
	2: "Double",
	3: "Waiting",
	4: "Started",
}
var BusinessElemEnum_value = map[string]int32{
	"NoLimit": 0,
	"Single":  1,
	"Double":  2,
	"Waiting": 3,
	"Started": 4,
}

func (x BusinessElemEnum) String() string {
	return proto.EnumName(BusinessElemEnum_name, int32(x))
}
func (BusinessElemEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{4}
}

type RCMDLabel int32

const (
	RCMDLabel_None                RCMDLabel = 0
	RCMDLabel_GangUpWithHomeOwner RCMDLabel = 1
	RCMDLabel_ChatWithHomeOwner   RCMDLabel = 2
)

var RCMDLabel_name = map[int32]string{
	0: "None",
	1: "GangUpWithHomeOwner",
	2: "ChatWithHomeOwner",
}
var RCMDLabel_value = map[string]int32{
	"None":                0,
	"GangUpWithHomeOwner": 1,
	"ChatWithHomeOwner":   2,
}

func (x RCMDLabel) String() string {
	return proto.EnumName(RCMDLabel_name, int32(x))
}
func (RCMDLabel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{5}
}

type GetRCMDCfgReq_SOURCE int32

const (
	GetRCMDCfgReq_SOURCE_DEFAULT GetRCMDCfgReq_SOURCE = 0
	GetRCMDCfgReq_SOURCE_TT      GetRCMDCfgReq_SOURCE = 1
)

var GetRCMDCfgReq_SOURCE_name = map[int32]string{
	0: "SOURCE_DEFAULT",
	1: "SOURCE_TT",
}
var GetRCMDCfgReq_SOURCE_value = map[string]int32{
	"SOURCE_DEFAULT": 0,
	"SOURCE_TT":      1,
}

func (x GetRCMDCfgReq_SOURCE) String() string {
	return proto.EnumName(GetRCMDCfgReq_SOURCE_name, int32(x))
}
func (GetRCMDCfgReq_SOURCE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{2, 0}
}

type GetRecommendationListReq_GetListMode int32

const (
	GetRecommendationListReq_DEFAULT  GetRecommendationListReq_GetListMode = 0
	GetRecommendationListReq_NEXTPAGE GetRecommendationListReq_GetListMode = 1
	GetRecommendationListReq_REFRESH  GetRecommendationListReq_GetListMode = 2
)

var GetRecommendationListReq_GetListMode_name = map[int32]string{
	0: "DEFAULT",
	1: "NEXTPAGE",
	2: "REFRESH",
}
var GetRecommendationListReq_GetListMode_value = map[string]int32{
	"DEFAULT":  0,
	"NEXTPAGE": 1,
	"REFRESH":  2,
}

func (x GetRecommendationListReq_GetListMode) String() string {
	return proto.EnumName(GetRecommendationListReq_GetListMode_name, int32(x))
}
func (GetRecommendationListReq_GetListMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{8, 0}
}

type GetRecommendationListReq_Environment int32

const (
	GetRecommendationListReq_production GetRecommendationListReq_Environment = 0
	GetRecommendationListReq_staging    GetRecommendationListReq_Environment = 1
	GetRecommendationListReq_test       GetRecommendationListReq_Environment = 2
)

var GetRecommendationListReq_Environment_name = map[int32]string{
	0: "production",
	1: "staging",
	2: "test",
}
var GetRecommendationListReq_Environment_value = map[string]int32{
	"production": 0,
	"staging":    1,
	"test":       2,
}

func (x GetRecommendationListReq_Environment) String() string {
	return proto.EnumName(GetRecommendationListReq_Environment_name, int32(x))
}
func (GetRecommendationListReq_Environment) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{8, 1}
}

type ChannelInfo_LocShowType int32

const (
	ChannelInfo_LocShowType_DEFAULT  ChannelInfo_LocShowType = 0
	ChannelInfo_LocShowType_PROVINCE ChannelInfo_LocShowType = 2
	ChannelInfo_LocShowType_CITY     ChannelInfo_LocShowType = 3
)

var ChannelInfo_LocShowType_name = map[int32]string{
	0: "LocShowType_DEFAULT",
	2: "LocShowType_PROVINCE",
	3: "LocShowType_CITY",
}
var ChannelInfo_LocShowType_value = map[string]int32{
	"LocShowType_DEFAULT":  0,
	"LocShowType_PROVINCE": 2,
	"LocShowType_CITY":     3,
}

func (x ChannelInfo_LocShowType) String() string {
	return proto.EnumName(ChannelInfo_LocShowType_name, int32(x))
}
func (ChannelInfo_LocShowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{9, 0}
}

type GetRecommendationListResp_NotifyType int32

const (
	GetRecommendationListResp_DEFAULT     GetRecommendationListResp_NotifyType = 0
	GetRecommendationListResp_APPOINTMENT GetRecommendationListResp_NotifyType = 1
	GetRecommendationListResp_RefreshSucc GetRecommendationListResp_NotifyType = 2
)

var GetRecommendationListResp_NotifyType_name = map[int32]string{
	0: "DEFAULT",
	1: "APPOINTMENT",
	2: "RefreshSucc",
}
var GetRecommendationListResp_NotifyType_value = map[string]int32{
	"DEFAULT":     0,
	"APPOINTMENT": 1,
	"RefreshSucc": 2,
}

func (x GetRecommendationListResp_NotifyType) String() string {
	return proto.EnumName(GetRecommendationListResp_NotifyType_name, int32(x))
}
func (GetRecommendationListResp_NotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{10, 0}
}

type BaseRCMDResp_PlanBStrategy int32

const (
	BaseRCMDResp_PlanBStrategy_INVALID BaseRCMDResp_PlanBStrategy = 0
	BaseRCMDResp_PlanBStrategy_TT      BaseRCMDResp_PlanBStrategy = 1
	BaseRCMDResp_PlanBStrategy_RANDOM  BaseRCMDResp_PlanBStrategy = 2
)

var BaseRCMDResp_PlanBStrategy_name = map[int32]string{
	0: "PlanBStrategy_INVALID",
	1: "PlanBStrategy_TT",
	2: "PlanBStrategy_RANDOM",
}
var BaseRCMDResp_PlanBStrategy_value = map[string]int32{
	"PlanBStrategy_INVALID": 0,
	"PlanBStrategy_TT":      1,
	"PlanBStrategy_RANDOM":  2,
}

func (x BaseRCMDResp_PlanBStrategy) String() string {
	return proto.EnumName(BaseRCMDResp_PlanBStrategy_name, int32(x))
}
func (BaseRCMDResp_PlanBStrategy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{11, 0}
}

type SetTopicChannelQualityReq_TopicChannelQuality int32

const (
	SetTopicChannelQualityReq_DEFAULT SetTopicChannelQualityReq_TopicChannelQuality = 0
	SetTopicChannelQualityReq_LOW     SetTopicChannelQualityReq_TopicChannelQuality = 1
)

var SetTopicChannelQualityReq_TopicChannelQuality_name = map[int32]string{
	0: "DEFAULT",
	1: "LOW",
}
var SetTopicChannelQualityReq_TopicChannelQuality_value = map[string]int32{
	"DEFAULT": 0,
	"LOW":     1,
}

func (x SetTopicChannelQualityReq_TopicChannelQuality) String() string {
	return proto.EnumName(SetTopicChannelQualityReq_TopicChannelQuality_name, int32(x))
}
func (SetTopicChannelQualityReq_TopicChannelQuality) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{12, 0}
}

type NegativeFeedEvent_UpdateType int32

const (
	NegativeFeedEvent_Invalid     NegativeFeedEvent_UpdateType = 0
	NegativeFeedEvent_User        NegativeFeedEvent_UpdateType = 1
	NegativeFeedEvent_Tab         NegativeFeedEvent_UpdateType = 2
	NegativeFeedEvent_ChannelCond NegativeFeedEvent_UpdateType = 3
	NegativeFeedEvent_ChannelName NegativeFeedEvent_UpdateType = 4
)

var NegativeFeedEvent_UpdateType_name = map[int32]string{
	0: "Invalid",
	1: "User",
	2: "Tab",
	3: "ChannelCond",
	4: "ChannelName",
}
var NegativeFeedEvent_UpdateType_value = map[string]int32{
	"Invalid":     0,
	"User":        1,
	"Tab":         2,
	"ChannelCond": 3,
	"ChannelName": 4,
}

func (x NegativeFeedEvent_UpdateType) String() string {
	return proto.EnumName(NegativeFeedEvent_UpdateType_name, int32(x))
}
func (NegativeFeedEvent_UpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{15, 0}
}

type ResetFilterReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResetFilterReq) Reset()         { *m = ResetFilterReq{} }
func (m *ResetFilterReq) String() string { return proto.CompactTextString(m) }
func (*ResetFilterReq) ProtoMessage()    {}
func (*ResetFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{0}
}
func (m *ResetFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetFilterReq.Unmarshal(m, b)
}
func (m *ResetFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetFilterReq.Marshal(b, m, deterministic)
}
func (dst *ResetFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetFilterReq.Merge(dst, src)
}
func (m *ResetFilterReq) XXX_Size() int {
	return xxx_messageInfo_ResetFilterReq.Size(m)
}
func (m *ResetFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_ResetFilterReq proto.InternalMessageInfo

func (m *ResetFilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ResetFilterResp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResetFilterResp) Reset()         { *m = ResetFilterResp{} }
func (m *ResetFilterResp) String() string { return proto.CompactTextString(m) }
func (*ResetFilterResp) ProtoMessage()    {}
func (*ResetFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{1}
}
func (m *ResetFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetFilterResp.Unmarshal(m, b)
}
func (m *ResetFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetFilterResp.Marshal(b, m, deterministic)
}
func (dst *ResetFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetFilterResp.Merge(dst, src)
}
func (m *ResetFilterResp) XXX_Size() int {
	return xxx_messageInfo_ResetFilterResp.Size(m)
}
func (m *ResetFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_ResetFilterResp proto.InternalMessageInfo

func (m *ResetFilterResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ResetFilterResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GetRCMDCfgReq struct {
	Source               GetRCMDCfgReq_SOURCE `protobuf:"varint,1,opt,name=source,proto3,enum=topic_channel.recommendation_gen.GetRCMDCfgReq_SOURCE" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetRCMDCfgReq) Reset()         { *m = GetRCMDCfgReq{} }
func (m *GetRCMDCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetRCMDCfgReq) ProtoMessage()    {}
func (*GetRCMDCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{2}
}
func (m *GetRCMDCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRCMDCfgReq.Unmarshal(m, b)
}
func (m *GetRCMDCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRCMDCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetRCMDCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRCMDCfgReq.Merge(dst, src)
}
func (m *GetRCMDCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetRCMDCfgReq.Size(m)
}
func (m *GetRCMDCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRCMDCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRCMDCfgReq proto.InternalMessageInfo

func (m *GetRCMDCfgReq) GetSource() GetRCMDCfgReq_SOURCE {
	if m != nil {
		return m.Source
	}
	return GetRCMDCfgReq_SOURCE_DEFAULT
}

type GetRCMDCfgResp struct {
	SupervisorWhiteList  []uint32 `protobuf:"varint,1,rep,packed,name=supervisor_white_list,json=supervisorWhiteList,proto3" json:"supervisor_white_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRCMDCfgResp) Reset()         { *m = GetRCMDCfgResp{} }
func (m *GetRCMDCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetRCMDCfgResp) ProtoMessage()    {}
func (*GetRCMDCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{3}
}
func (m *GetRCMDCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRCMDCfgResp.Unmarshal(m, b)
}
func (m *GetRCMDCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRCMDCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetRCMDCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRCMDCfgResp.Merge(dst, src)
}
func (m *GetRCMDCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetRCMDCfgResp.Size(m)
}
func (m *GetRCMDCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRCMDCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRCMDCfgResp proto.InternalMessageInfo

func (m *GetRCMDCfgResp) GetSupervisorWhiteList() []uint32 {
	if m != nil {
		return m.SupervisorWhiteList
	}
	return nil
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{4}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

type RecommendationReqReserve struct {
	ImListExpGroup       IMChannelListABGroup `protobuf:"varint,1,opt,name=im_list_exp_group,json=imListExpGroup,proto3,enum=topic_channel.recommendation_gen.IMChannelListABGroup" json:"im_list_exp_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *RecommendationReqReserve) Reset()         { *m = RecommendationReqReserve{} }
func (m *RecommendationReqReserve) String() string { return proto.CompactTextString(m) }
func (*RecommendationReqReserve) ProtoMessage()    {}
func (*RecommendationReqReserve) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{5}
}
func (m *RecommendationReqReserve) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendationReqReserve.Unmarshal(m, b)
}
func (m *RecommendationReqReserve) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendationReqReserve.Marshal(b, m, deterministic)
}
func (dst *RecommendationReqReserve) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendationReqReserve.Merge(dst, src)
}
func (m *RecommendationReqReserve) XXX_Size() int {
	return xxx_messageInfo_RecommendationReqReserve.Size(m)
}
func (m *RecommendationReqReserve) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendationReqReserve.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendationReqReserve proto.InternalMessageInfo

func (m *RecommendationReqReserve) GetImListExpGroup() IMChannelListABGroup {
	if m != nil {
		return m.ImListExpGroup
	}
	return IMChannelListABGroup_DEFAULT
}

type PrefGameLabel struct {
	Id                   uint32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Val                  string            `protobuf:"bytes,2,opt,name=val,proto3" json:"val,omitempty"`
	Type                 PrefGameLabelType `protobuf:"varint,3,opt,name=type,proto3,enum=topic_channel.recommendation_gen.PrefGameLabelType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PrefGameLabel) Reset()         { *m = PrefGameLabel{} }
func (m *PrefGameLabel) String() string { return proto.CompactTextString(m) }
func (*PrefGameLabel) ProtoMessage()    {}
func (*PrefGameLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{6}
}
func (m *PrefGameLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrefGameLabel.Unmarshal(m, b)
}
func (m *PrefGameLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrefGameLabel.Marshal(b, m, deterministic)
}
func (dst *PrefGameLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrefGameLabel.Merge(dst, src)
}
func (m *PrefGameLabel) XXX_Size() int {
	return xxx_messageInfo_PrefGameLabel.Size(m)
}
func (m *PrefGameLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_PrefGameLabel.DiscardUnknown(m)
}

var xxx_messageInfo_PrefGameLabel proto.InternalMessageInfo

func (m *PrefGameLabel) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PrefGameLabel) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *PrefGameLabel) GetType() PrefGameLabelType {
	if m != nil {
		return m.Type
	}
	return PrefGameLabelType_SYSTEM
}

type PrefGame struct {
	TabId                uint32           `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Labels               []*PrefGameLabel `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PrefGame) Reset()         { *m = PrefGame{} }
func (m *PrefGame) String() string { return proto.CompactTextString(m) }
func (*PrefGame) ProtoMessage()    {}
func (*PrefGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{7}
}
func (m *PrefGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrefGame.Unmarshal(m, b)
}
func (m *PrefGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrefGame.Marshal(b, m, deterministic)
}
func (dst *PrefGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrefGame.Merge(dst, src)
}
func (m *PrefGame) XXX_Size() int {
	return xxx_messageInfo_PrefGame.Size(m)
}
func (m *PrefGame) XXX_DiscardUnknown() {
	xxx_messageInfo_PrefGame.DiscardUnknown(m)
}

var xxx_messageInfo_PrefGame proto.InternalMessageInfo

func (m *PrefGame) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *PrefGame) GetLabels() []*PrefGameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

type GetRecommendationListReq struct {
	Uid                  uint32                                     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32                                     `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TabId                uint32                                     `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BlockOptions         []*BlockOption                             `protobuf:"bytes,4,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	GetMode              GetRecommendationListReq_GetListMode       `protobuf:"varint,5,opt,name=get_mode,json=getMode,proto3,enum=topic_channel.recommendation_gen.GetRecommendationListReq_GetListMode" json:"get_mode,omitempty"`
	ChannelEnterSource   uint32                                     `protobuf:"varint,6,opt,name=channel_enter_source,json=channelEnterSource,proto3" json:"channel_enter_source,omitempty"`
	ClientType           uint32                                     `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32                                     `protobuf:"varint,8,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	MarketId             uint32                                     `protobuf:"varint,9,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Env                  GetRecommendationListReq_Environment       `protobuf:"varint,10,opt,name=env,proto3,enum=topic_channel.recommendation_gen.GetRecommendationListReq_Environment" json:"env,omitempty"`
	ChannelPackageId     string                                     `protobuf:"bytes,11,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	TraceId              string                                     `protobuf:"bytes,12,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	ModelVersion         string                                     `protobuf:"bytes,13,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	DebugFlag            uint32                                     `protobuf:"varint,14,opt,name=debug_flag,json=debugFlag,proto3" json:"debug_flag,omitempty"`
	Reserve              string                                     `protobuf:"bytes,15,opt,name=reserve,proto3" json:"reserve,omitempty"`
	BaseReq              *common.RcmdBaseReq                        `protobuf:"bytes,16,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Sex                  uint32                                     `protobuf:"varint,17,opt,name=sex,proto3" json:"sex,omitempty"`
	BrowseList           *common.RcmdBrowseInfo                     `protobuf:"bytes,18,opt,name=browse_list,json=browseList,proto3" json:"browse_list,omitempty"`
	RegulatoryLevel      REGULATORY_LEVEL                           `protobuf:"varint,19,opt,name=regulatory_level,json=regulatoryLevel,proto3,enum=topic_channel.recommendation_gen.REGULATORY_LEVEL" json:"regulatory_level,omitempty"`
	IsMinorityParentTab  bool                                       `protobuf:"varint,20,opt,name=is_minority_parent_tab,json=isMinorityParentTab,proto3" json:"is_minority_parent_tab,omitempty"`
	CategoryIds          []uint32                                   `protobuf:"varint,22,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	TabIds               []uint32                                   `protobuf:"varint,23,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	IsQmFramework        bool                                       `protobuf:"varint,24,opt,name=is_qm_framework,json=isQmFramework,proto3" json:"is_qm_framework,omitempty"`
	PrefGames            []*PrefGame                                `protobuf:"bytes,25,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	Labels               []*rcmd_channel_label.GameLabel            `protobuf:"bytes,26,rep,name=labels,proto3" json:"labels,omitempty"`
	BusinessFilter       []*GetRecommendationListReq_BusinessFilter `protobuf:"bytes,27,rep,name=business_filter,json=businessFilter,proto3" json:"business_filter,omitempty"`
	GameSettings         []*recommendation_common.TcGameSetting     `protobuf:"bytes,28,rep,name=game_settings,json=gameSettings,proto3" json:"game_settings,omitempty"`
	IsFallbackReq        bool                                       `protobuf:"varint,29,opt,name=is_fallback_req,json=isFallbackReq,proto3" json:"is_fallback_req,omitempty"`
	InterestLabels       []string                                   `protobuf:"bytes,30,rep,name=interest_labels,json=interestLabels,proto3" json:"interest_labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *GetRecommendationListReq) Reset()         { *m = GetRecommendationListReq{} }
func (m *GetRecommendationListReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationListReq) ProtoMessage()    {}
func (*GetRecommendationListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{8}
}
func (m *GetRecommendationListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationListReq.Unmarshal(m, b)
}
func (m *GetRecommendationListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationListReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationListReq.Merge(dst, src)
}
func (m *GetRecommendationListReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationListReq.Size(m)
}
func (m *GetRecommendationListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationListReq proto.InternalMessageInfo

func (m *GetRecommendationListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendationListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetRecommendationListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetRecommendationListReq) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *GetRecommendationListReq) GetGetMode() GetRecommendationListReq_GetListMode {
	if m != nil {
		return m.GetMode
	}
	return GetRecommendationListReq_DEFAULT
}

func (m *GetRecommendationListReq) GetChannelEnterSource() uint32 {
	if m != nil {
		return m.ChannelEnterSource
	}
	return 0
}

func (m *GetRecommendationListReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *GetRecommendationListReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetRecommendationListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetRecommendationListReq) GetEnv() GetRecommendationListReq_Environment {
	if m != nil {
		return m.Env
	}
	return GetRecommendationListReq_production
}

func (m *GetRecommendationListReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *GetRecommendationListReq) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetRecommendationListReq) GetModelVersion() string {
	if m != nil {
		return m.ModelVersion
	}
	return ""
}

func (m *GetRecommendationListReq) GetDebugFlag() uint32 {
	if m != nil {
		return m.DebugFlag
	}
	return 0
}

func (m *GetRecommendationListReq) GetReserve() string {
	if m != nil {
		return m.Reserve
	}
	return ""
}

func (m *GetRecommendationListReq) GetBaseReq() *common.RcmdBaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRecommendationListReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetRecommendationListReq) GetBrowseList() *common.RcmdBrowseInfo {
	if m != nil {
		return m.BrowseList
	}
	return nil
}

func (m *GetRecommendationListReq) GetRegulatoryLevel() REGULATORY_LEVEL {
	if m != nil {
		return m.RegulatoryLevel
	}
	return REGULATORY_LEVEL_FREE
}

func (m *GetRecommendationListReq) GetIsMinorityParentTab() bool {
	if m != nil {
		return m.IsMinorityParentTab
	}
	return false
}

func (m *GetRecommendationListReq) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *GetRecommendationListReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GetRecommendationListReq) GetIsQmFramework() bool {
	if m != nil {
		return m.IsQmFramework
	}
	return false
}

func (m *GetRecommendationListReq) GetPrefGames() []*PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *GetRecommendationListReq) GetLabels() []*rcmd_channel_label.GameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *GetRecommendationListReq) GetBusinessFilter() []*GetRecommendationListReq_BusinessFilter {
	if m != nil {
		return m.BusinessFilter
	}
	return nil
}

func (m *GetRecommendationListReq) GetGameSettings() []*recommendation_common.TcGameSetting {
	if m != nil {
		return m.GameSettings
	}
	return nil
}

func (m *GetRecommendationListReq) GetIsFallbackReq() bool {
	if m != nil {
		return m.IsFallbackReq
	}
	return false
}

func (m *GetRecommendationListReq) GetInterestLabels() []string {
	if m != nil {
		return m.InterestLabels
	}
	return nil
}

// 业务筛选字段
type GetRecommendationListReq_BusinessFilter struct {
	// 业务block
	BlockType BusinessBlockEnum `protobuf:"varint,1,opt,name=block_type,json=blockType,proto3,enum=topic_channel.recommendation_gen.BusinessBlockEnum" json:"block_type,omitempty"`
	// 业务elem
	ElemType             []BusinessElemEnum `protobuf:"varint,2,rep,packed,name=elem_type,json=elemType,proto3,enum=topic_channel.recommendation_gen.BusinessElemEnum" json:"elem_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetRecommendationListReq_BusinessFilter) Reset() {
	*m = GetRecommendationListReq_BusinessFilter{}
}
func (m *GetRecommendationListReq_BusinessFilter) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationListReq_BusinessFilter) ProtoMessage()    {}
func (*GetRecommendationListReq_BusinessFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{8, 0}
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Unmarshal(m, b)
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationListReq_BusinessFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Merge(dst, src)
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationListReq_BusinessFilter.Size(m)
}
func (m *GetRecommendationListReq_BusinessFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationListReq_BusinessFilter.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationListReq_BusinessFilter proto.InternalMessageInfo

func (m *GetRecommendationListReq_BusinessFilter) GetBlockType() BusinessBlockEnum {
	if m != nil {
		return m.BlockType
	}
	return BusinessBlockEnum_RoomMode
}

func (m *GetRecommendationListReq_BusinessFilter) GetElemType() []BusinessElemEnum {
	if m != nil {
		return m.ElemType
	}
	return nil
}

type ChannelInfo struct {
	TagId                uint32                  `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	RecallFlag           uint32                  `protobuf:"varint,2,opt,name=recall_flag,json=recallFlag,proto3" json:"recall_flag,omitempty"`
	Loc                  *common.LocationInfo    `protobuf:"bytes,3,opt,name=loc,proto3" json:"loc,omitempty"`
	RcmdLabels           []RCMDLabel             `protobuf:"varint,4,rep,packed,name=rcmd_labels,json=rcmdLabels,proto3,enum=topic_channel.recommendation_gen.RCMDLabel" json:"rcmd_labels,omitempty"`
	LocShowType          ChannelInfo_LocShowType `protobuf:"varint,5,opt,name=loc_show_type,json=locShowType,proto3,enum=topic_channel.recommendation_gen.ChannelInfo_LocShowType" json:"loc_show_type,omitempty"`
	IsNewUserUndertake   bool                    `protobuf:"varint,6,opt,name=is_new_user_undertake,json=isNewUserUndertake,proto3" json:"is_new_user_undertake,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ChannelInfo) Reset()         { *m = ChannelInfo{} }
func (m *ChannelInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelInfo) ProtoMessage()    {}
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{9}
}
func (m *ChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfo.Unmarshal(m, b)
}
func (m *ChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfo.Merge(dst, src)
}
func (m *ChannelInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelInfo.Size(m)
}
func (m *ChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfo proto.InternalMessageInfo

func (m *ChannelInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelInfo) GetRecallFlag() uint32 {
	if m != nil {
		return m.RecallFlag
	}
	return 0
}

func (m *ChannelInfo) GetLoc() *common.LocationInfo {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *ChannelInfo) GetRcmdLabels() []RCMDLabel {
	if m != nil {
		return m.RcmdLabels
	}
	return nil
}

func (m *ChannelInfo) GetLocShowType() ChannelInfo_LocShowType {
	if m != nil {
		return m.LocShowType
	}
	return ChannelInfo_LocShowType_DEFAULT
}

func (m *ChannelInfo) GetIsNewUserUndertake() bool {
	if m != nil {
		return m.IsNewUserUndertake
	}
	return false
}

type GetRecommendationListResp struct {
	ChannelId            []uint32                               `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BottomReached        bool                                   `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	ChannelInfoMap       map[uint32]*ChannelInfo                `protobuf:"bytes,3,rep,name=channel_info_map,json=channelInfoMap,proto3" json:"channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TraceId              string                                 `protobuf:"bytes,4,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	DebugInfoMap         map[string]string                      `protobuf:"bytes,5,rep,name=debug_info_map,json=debugInfoMap,proto3" json:"debug_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SelfLoc              *common.LocationInfo                   `protobuf:"bytes,6,opt,name=self_loc,json=selfLoc,proto3" json:"self_loc,omitempty"`
	PrefGames            []*PrefGame                            `protobuf:"bytes,7,rep,name=pref_games,json=prefGames,proto3" json:"pref_games,omitempty"`
	InsertPos            uint32                                 `protobuf:"varint,8,opt,name=insert_pos,json=insertPos,proto3" json:"insert_pos,omitempty"`
	NotifyList           []GetRecommendationListResp_NotifyType `protobuf:"varint,9,rep,packed,name=notify_list,json=notifyList,proto3,enum=topic_channel.recommendation_gen.GetRecommendationListResp_NotifyType" json:"notify_list,omitempty"`
	Footprint            string                                 `protobuf:"bytes,10,opt,name=footprint,proto3" json:"footprint,omitempty"`
	BaseResp             *BaseRCMDResp                          `protobuf:"bytes,11,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetRecommendationListResp) Reset()         { *m = GetRecommendationListResp{} }
func (m *GetRecommendationListResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationListResp) ProtoMessage()    {}
func (*GetRecommendationListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{10}
}
func (m *GetRecommendationListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendationListResp.Unmarshal(m, b)
}
func (m *GetRecommendationListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendationListResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendationListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationListResp.Merge(dst, src)
}
func (m *GetRecommendationListResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendationListResp.Size(m)
}
func (m *GetRecommendationListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationListResp proto.InternalMessageInfo

func (m *GetRecommendationListResp) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *GetRecommendationListResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

func (m *GetRecommendationListResp) GetChannelInfoMap() map[uint32]*ChannelInfo {
	if m != nil {
		return m.ChannelInfoMap
	}
	return nil
}

func (m *GetRecommendationListResp) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetRecommendationListResp) GetDebugInfoMap() map[string]string {
	if m != nil {
		return m.DebugInfoMap
	}
	return nil
}

func (m *GetRecommendationListResp) GetSelfLoc() *common.LocationInfo {
	if m != nil {
		return m.SelfLoc
	}
	return nil
}

func (m *GetRecommendationListResp) GetPrefGames() []*PrefGame {
	if m != nil {
		return m.PrefGames
	}
	return nil
}

func (m *GetRecommendationListResp) GetInsertPos() uint32 {
	if m != nil {
		return m.InsertPos
	}
	return 0
}

func (m *GetRecommendationListResp) GetNotifyList() []GetRecommendationListResp_NotifyType {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

func (m *GetRecommendationListResp) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

func (m *GetRecommendationListResp) GetBaseResp() *BaseRCMDResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type BaseRCMDResp struct {
	Ok                   bool                       `protobuf:"varint,1,opt,name=ok,proto3" json:"ok,omitempty"`
	Code                 uint64                     `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string                     `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	PlanBStrategy        BaseRCMDResp_PlanBStrategy `protobuf:"varint,4,opt,name=plan_b_strategy,json=planBStrategy,proto3,enum=topic_channel.recommendation_gen.BaseRCMDResp_PlanBStrategy" json:"plan_b_strategy,omitempty"`
	IsAlgo               bool                       `protobuf:"varint,5,opt,name=is_algo,json=isAlgo,proto3" json:"is_algo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *BaseRCMDResp) Reset()         { *m = BaseRCMDResp{} }
func (m *BaseRCMDResp) String() string { return proto.CompactTextString(m) }
func (*BaseRCMDResp) ProtoMessage()    {}
func (*BaseRCMDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{11}
}
func (m *BaseRCMDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseRCMDResp.Unmarshal(m, b)
}
func (m *BaseRCMDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseRCMDResp.Marshal(b, m, deterministic)
}
func (dst *BaseRCMDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseRCMDResp.Merge(dst, src)
}
func (m *BaseRCMDResp) XXX_Size() int {
	return xxx_messageInfo_BaseRCMDResp.Size(m)
}
func (m *BaseRCMDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseRCMDResp.DiscardUnknown(m)
}

var xxx_messageInfo_BaseRCMDResp proto.InternalMessageInfo

func (m *BaseRCMDResp) GetOk() bool {
	if m != nil {
		return m.Ok
	}
	return false
}

func (m *BaseRCMDResp) GetCode() uint64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseRCMDResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *BaseRCMDResp) GetPlanBStrategy() BaseRCMDResp_PlanBStrategy {
	if m != nil {
		return m.PlanBStrategy
	}
	return BaseRCMDResp_PlanBStrategy_INVALID
}

func (m *BaseRCMDResp) GetIsAlgo() bool {
	if m != nil {
		return m.IsAlgo
	}
	return false
}

type SetTopicChannelQualityReq struct {
	ChannelId            []uint32                                      `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Quality              SetTopicChannelQualityReq_TopicChannelQuality `protobuf:"varint,2,opt,name=quality,proto3,enum=topic_channel.recommendation_gen.SetTopicChannelQualityReq_TopicChannelQuality" json:"quality,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *SetTopicChannelQualityReq) Reset()         { *m = SetTopicChannelQualityReq{} }
func (m *SetTopicChannelQualityReq) String() string { return proto.CompactTextString(m) }
func (*SetTopicChannelQualityReq) ProtoMessage()    {}
func (*SetTopicChannelQualityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{12}
}
func (m *SetTopicChannelQualityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTopicChannelQualityReq.Unmarshal(m, b)
}
func (m *SetTopicChannelQualityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTopicChannelQualityReq.Marshal(b, m, deterministic)
}
func (dst *SetTopicChannelQualityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTopicChannelQualityReq.Merge(dst, src)
}
func (m *SetTopicChannelQualityReq) XXX_Size() int {
	return xxx_messageInfo_SetTopicChannelQualityReq.Size(m)
}
func (m *SetTopicChannelQualityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTopicChannelQualityReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTopicChannelQualityReq proto.InternalMessageInfo

func (m *SetTopicChannelQualityReq) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *SetTopicChannelQualityReq) GetQuality() SetTopicChannelQualityReq_TopicChannelQuality {
	if m != nil {
		return m.Quality
	}
	return SetTopicChannelQualityReq_DEFAULT
}

type SetTopicChannelQualityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTopicChannelQualityResp) Reset()         { *m = SetTopicChannelQualityResp{} }
func (m *SetTopicChannelQualityResp) String() string { return proto.CompactTextString(m) }
func (*SetTopicChannelQualityResp) ProtoMessage()    {}
func (*SetTopicChannelQualityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{13}
}
func (m *SetTopicChannelQualityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTopicChannelQualityResp.Unmarshal(m, b)
}
func (m *SetTopicChannelQualityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTopicChannelQualityResp.Marshal(b, m, deterministic)
}
func (dst *SetTopicChannelQualityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTopicChannelQualityResp.Merge(dst, src)
}
func (m *SetTopicChannelQualityResp) XXX_Size() int {
	return xxx_messageInfo_SetTopicChannelQualityResp.Size(m)
}
func (m *SetTopicChannelQualityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTopicChannelQualityResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTopicChannelQualityResp proto.InternalMessageInfo

type CompanionChannelEvent struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompanionChannelEvent) Reset()         { *m = CompanionChannelEvent{} }
func (m *CompanionChannelEvent) String() string { return proto.CompactTextString(m) }
func (*CompanionChannelEvent) ProtoMessage()    {}
func (*CompanionChannelEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{14}
}
func (m *CompanionChannelEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompanionChannelEvent.Unmarshal(m, b)
}
func (m *CompanionChannelEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompanionChannelEvent.Marshal(b, m, deterministic)
}
func (dst *CompanionChannelEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompanionChannelEvent.Merge(dst, src)
}
func (m *CompanionChannelEvent) XXX_Size() int {
	return xxx_messageInfo_CompanionChannelEvent.Size(m)
}
func (m *CompanionChannelEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_CompanionChannelEvent.DiscardUnknown(m)
}

var xxx_messageInfo_CompanionChannelEvent proto.InternalMessageInfo

func (m *CompanionChannelEvent) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type NegativeFeedEvent struct {
	Uid                  uint32                         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserMap              map[uint32]int64               `protobuf:"bytes,2,rep,name=user_map,json=userMap,proto3" json:"user_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	TabMap               map[uint32]int64               `protobuf:"bytes,3,rep,name=tab_map,json=tabMap,proto3" json:"tab_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelCondMap       map[string]int64               `protobuf:"bytes,4,rep,name=channel_cond_map,json=channelCondMap,proto3" json:"channel_cond_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelNameMap       map[string]int64               `protobuf:"bytes,5,rep,name=channel_name_map,json=channelNameMap,proto3" json:"channel_name_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UpdateTypeList       []NegativeFeedEvent_UpdateType `protobuf:"varint,6,rep,packed,name=update_type_list,json=updateTypeList,proto3,enum=topic_channel.recommendation_gen.NegativeFeedEvent_UpdateType" json:"update_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *NegativeFeedEvent) Reset()         { *m = NegativeFeedEvent{} }
func (m *NegativeFeedEvent) String() string { return proto.CompactTextString(m) }
func (*NegativeFeedEvent) ProtoMessage()    {}
func (*NegativeFeedEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{15}
}
func (m *NegativeFeedEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NegativeFeedEvent.Unmarshal(m, b)
}
func (m *NegativeFeedEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NegativeFeedEvent.Marshal(b, m, deterministic)
}
func (dst *NegativeFeedEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NegativeFeedEvent.Merge(dst, src)
}
func (m *NegativeFeedEvent) XXX_Size() int {
	return xxx_messageInfo_NegativeFeedEvent.Size(m)
}
func (m *NegativeFeedEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_NegativeFeedEvent.DiscardUnknown(m)
}

var xxx_messageInfo_NegativeFeedEvent proto.InternalMessageInfo

func (m *NegativeFeedEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NegativeFeedEvent) GetUserMap() map[uint32]int64 {
	if m != nil {
		return m.UserMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetTabMap() map[uint32]int64 {
	if m != nil {
		return m.TabMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetChannelCondMap() map[string]int64 {
	if m != nil {
		return m.ChannelCondMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetChannelNameMap() map[string]int64 {
	if m != nil {
		return m.ChannelNameMap
	}
	return nil
}

func (m *NegativeFeedEvent) GetUpdateTypeList() []NegativeFeedEvent_UpdateType {
	if m != nil {
		return m.UpdateTypeList
	}
	return nil
}

type UserNegativeFeed struct {
	UserMap              map[uint32]int64 `protobuf:"bytes,1,rep,name=user_map,json=userMap,proto3" json:"user_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	TabMap               map[uint32]int64 `protobuf:"bytes,2,rep,name=tab_map,json=tabMap,proto3" json:"tab_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelCondMap       map[string]int64 `protobuf:"bytes,3,rep,name=channel_cond_map,json=channelCondMap,proto3" json:"channel_cond_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ChannelNameMap       map[string]int64 `protobuf:"bytes,4,rep,name=channel_name_map,json=channelNameMap,proto3" json:"channel_name_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserNegativeFeed) Reset()         { *m = UserNegativeFeed{} }
func (m *UserNegativeFeed) String() string { return proto.CompactTextString(m) }
func (*UserNegativeFeed) ProtoMessage()    {}
func (*UserNegativeFeed) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{16}
}
func (m *UserNegativeFeed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNegativeFeed.Unmarshal(m, b)
}
func (m *UserNegativeFeed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNegativeFeed.Marshal(b, m, deterministic)
}
func (dst *UserNegativeFeed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNegativeFeed.Merge(dst, src)
}
func (m *UserNegativeFeed) XXX_Size() int {
	return xxx_messageInfo_UserNegativeFeed.Size(m)
}
func (m *UserNegativeFeed) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNegativeFeed.DiscardUnknown(m)
}

var xxx_messageInfo_UserNegativeFeed proto.InternalMessageInfo

func (m *UserNegativeFeed) GetUserMap() map[uint32]int64 {
	if m != nil {
		return m.UserMap
	}
	return nil
}

func (m *UserNegativeFeed) GetTabMap() map[uint32]int64 {
	if m != nil {
		return m.TabMap
	}
	return nil
}

func (m *UserNegativeFeed) GetChannelCondMap() map[string]int64 {
	if m != nil {
		return m.ChannelCondMap
	}
	return nil
}

func (m *UserNegativeFeed) GetChannelNameMap() map[string]int64 {
	if m != nil {
		return m.ChannelNameMap
	}
	return nil
}

type ChannelTitleTokenizeEvent struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Tags                 []string `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelTitleTokenizeEvent) Reset()         { *m = ChannelTitleTokenizeEvent{} }
func (m *ChannelTitleTokenizeEvent) String() string { return proto.CompactTextString(m) }
func (*ChannelTitleTokenizeEvent) ProtoMessage()    {}
func (*ChannelTitleTokenizeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{17}
}
func (m *ChannelTitleTokenizeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTitleTokenizeEvent.Unmarshal(m, b)
}
func (m *ChannelTitleTokenizeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTitleTokenizeEvent.Marshal(b, m, deterministic)
}
func (dst *ChannelTitleTokenizeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTitleTokenizeEvent.Merge(dst, src)
}
func (m *ChannelTitleTokenizeEvent) XXX_Size() int {
	return xxx_messageInfo_ChannelTitleTokenizeEvent.Size(m)
}
func (m *ChannelTitleTokenizeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTitleTokenizeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTitleTokenizeEvent proto.InternalMessageInfo

func (m *ChannelTitleTokenizeEvent) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ChannelTitleTokenizeEvent) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

type PlaymateIntentionLimitEvent struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LimitTime            int64    `protobuf:"varint,2,opt,name=limit_time,json=limitTime,proto3" json:"limit_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateIntentionLimitEvent) Reset()         { *m = PlaymateIntentionLimitEvent{} }
func (m *PlaymateIntentionLimitEvent) String() string { return proto.CompactTextString(m) }
func (*PlaymateIntentionLimitEvent) ProtoMessage()    {}
func (*PlaymateIntentionLimitEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recommendation_gen_f7b347291b8b5742, []int{18}
}
func (m *PlaymateIntentionLimitEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateIntentionLimitEvent.Unmarshal(m, b)
}
func (m *PlaymateIntentionLimitEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateIntentionLimitEvent.Marshal(b, m, deterministic)
}
func (dst *PlaymateIntentionLimitEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateIntentionLimitEvent.Merge(dst, src)
}
func (m *PlaymateIntentionLimitEvent) XXX_Size() int {
	return xxx_messageInfo_PlaymateIntentionLimitEvent.Size(m)
}
func (m *PlaymateIntentionLimitEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateIntentionLimitEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateIntentionLimitEvent proto.InternalMessageInfo

func (m *PlaymateIntentionLimitEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PlaymateIntentionLimitEvent) GetLimitTime() int64 {
	if m != nil {
		return m.LimitTime
	}
	return 0
}

func init() {
	proto.RegisterType((*ResetFilterReq)(nil), "topic_channel.recommendation_gen.ResetFilterReq")
	proto.RegisterType((*ResetFilterResp)(nil), "topic_channel.recommendation_gen.ResetFilterResp")
	proto.RegisterType((*GetRCMDCfgReq)(nil), "topic_channel.recommendation_gen.GetRCMDCfgReq")
	proto.RegisterType((*GetRCMDCfgResp)(nil), "topic_channel.recommendation_gen.GetRCMDCfgResp")
	proto.RegisterType((*BlockOption)(nil), "topic_channel.recommendation_gen.BlockOption")
	proto.RegisterType((*RecommendationReqReserve)(nil), "topic_channel.recommendation_gen.RecommendationReqReserve")
	proto.RegisterType((*PrefGameLabel)(nil), "topic_channel.recommendation_gen.PrefGameLabel")
	proto.RegisterType((*PrefGame)(nil), "topic_channel.recommendation_gen.PrefGame")
	proto.RegisterType((*GetRecommendationListReq)(nil), "topic_channel.recommendation_gen.GetRecommendationListReq")
	proto.RegisterType((*GetRecommendationListReq_BusinessFilter)(nil), "topic_channel.recommendation_gen.GetRecommendationListReq.BusinessFilter")
	proto.RegisterType((*ChannelInfo)(nil), "topic_channel.recommendation_gen.ChannelInfo")
	proto.RegisterType((*GetRecommendationListResp)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp")
	proto.RegisterMapType((map[uint32]*ChannelInfo)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp.ChannelInfoMapEntry")
	proto.RegisterMapType((map[string]string)(nil), "topic_channel.recommendation_gen.GetRecommendationListResp.DebugInfoMapEntry")
	proto.RegisterType((*BaseRCMDResp)(nil), "topic_channel.recommendation_gen.BaseRCMDResp")
	proto.RegisterType((*SetTopicChannelQualityReq)(nil), "topic_channel.recommendation_gen.SetTopicChannelQualityReq")
	proto.RegisterType((*SetTopicChannelQualityResp)(nil), "topic_channel.recommendation_gen.SetTopicChannelQualityResp")
	proto.RegisterType((*CompanionChannelEvent)(nil), "topic_channel.recommendation_gen.CompanionChannelEvent")
	proto.RegisterType((*NegativeFeedEvent)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.ChannelCondMapEntry")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.ChannelNameMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.TabMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.NegativeFeedEvent.UserMapEntry")
	proto.RegisterType((*UserNegativeFeed)(nil), "topic_channel.recommendation_gen.UserNegativeFeed")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.ChannelCondMapEntry")
	proto.RegisterMapType((map[string]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.ChannelNameMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.TabMapEntry")
	proto.RegisterMapType((map[uint32]int64)(nil), "topic_channel.recommendation_gen.UserNegativeFeed.UserMapEntry")
	proto.RegisterType((*ChannelTitleTokenizeEvent)(nil), "topic_channel.recommendation_gen.ChannelTitleTokenizeEvent")
	proto.RegisterType((*PlaymateIntentionLimitEvent)(nil), "topic_channel.recommendation_gen.PlaymateIntentionLimitEvent")
	proto.RegisterEnum("topic_channel.recommendation_gen.IMChannelListABGroup", IMChannelListABGroup_name, IMChannelListABGroup_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.REGULATORY_LEVEL", REGULATORY_LEVEL_name, REGULATORY_LEVEL_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.PrefGameLabelType", PrefGameLabelType_name, PrefGameLabelType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.BusinessBlockEnum", BusinessBlockEnum_name, BusinessBlockEnum_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.BusinessElemEnum", BusinessElemEnum_name, BusinessElemEnum_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.RCMDLabel", RCMDLabel_name, RCMDLabel_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRCMDCfgReq_SOURCE", GetRCMDCfgReq_SOURCE_name, GetRCMDCfgReq_SOURCE_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListReq_GetListMode", GetRecommendationListReq_GetListMode_name, GetRecommendationListReq_GetListMode_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListReq_Environment", GetRecommendationListReq_Environment_name, GetRecommendationListReq_Environment_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.ChannelInfo_LocShowType", ChannelInfo_LocShowType_name, ChannelInfo_LocShowType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.GetRecommendationListResp_NotifyType", GetRecommendationListResp_NotifyType_name, GetRecommendationListResp_NotifyType_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.BaseRCMDResp_PlanBStrategy", BaseRCMDResp_PlanBStrategy_name, BaseRCMDResp_PlanBStrategy_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.SetTopicChannelQualityReq_TopicChannelQuality", SetTopicChannelQualityReq_TopicChannelQuality_name, SetTopicChannelQualityReq_TopicChannelQuality_value)
	proto.RegisterEnum("topic_channel.recommendation_gen.NegativeFeedEvent_UpdateType", NegativeFeedEvent_UpdateType_name, NegativeFeedEvent_UpdateType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GenRecommendationClient is the client API for GenRecommendation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GenRecommendationClient interface {
	// 获取推荐房列表（个数为1的时候特殊处理，获取最合适的/home/<USER>/go_project/tt19/rcmd/api/rcmd/topic_channel）
	GetRecommendationList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error)
	// expose for algorithm 0.2
	RecallChannelList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error)
	SetTopicChannelQuality(ctx context.Context, in *SetTopicChannelQualityReq, opts ...grpc.CallOption) (*SetTopicChannelQualityResp, error)
	// rcmd-common config
	GetRCMDCfg(ctx context.Context, in *GetRCMDCfgReq, opts ...grpc.CallOption) (*GetRCMDCfgResp, error)
	// 清空下发过滤器
	ResetFilter(ctx context.Context, in *ResetFilterReq, opts ...grpc.CallOption) (*ResetFilterResp, error)
}

type genRecommendationClient struct {
	cc *grpc.ClientConn
}

func NewGenRecommendationClient(cc *grpc.ClientConn) GenRecommendationClient {
	return &genRecommendationClient{cc}
}

func (c *genRecommendationClient) GetRecommendationList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error) {
	out := new(GetRecommendationListResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/GetRecommendationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) RecallChannelList(ctx context.Context, in *GetRecommendationListReq, opts ...grpc.CallOption) (*GetRecommendationListResp, error) {
	out := new(GetRecommendationListResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/RecallChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) SetTopicChannelQuality(ctx context.Context, in *SetTopicChannelQualityReq, opts ...grpc.CallOption) (*SetTopicChannelQualityResp, error) {
	out := new(SetTopicChannelQualityResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/SetTopicChannelQuality", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) GetRCMDCfg(ctx context.Context, in *GetRCMDCfgReq, opts ...grpc.CallOption) (*GetRCMDCfgResp, error) {
	out := new(GetRCMDCfgResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/GetRCMDCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genRecommendationClient) ResetFilter(ctx context.Context, in *ResetFilterReq, opts ...grpc.CallOption) (*ResetFilterResp, error) {
	out := new(ResetFilterResp)
	err := c.cc.Invoke(ctx, "/topic_channel.recommendation_gen.GenRecommendation/ResetFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GenRecommendationServer is the server API for GenRecommendation service.
type GenRecommendationServer interface {
	// 获取推荐房列表（个数为1的时候特殊处理，获取最合适的/home/<USER>/go_project/tt19/rcmd/api/rcmd/topic_channel）
	GetRecommendationList(context.Context, *GetRecommendationListReq) (*GetRecommendationListResp, error)
	// expose for algorithm 0.2
	RecallChannelList(context.Context, *GetRecommendationListReq) (*GetRecommendationListResp, error)
	SetTopicChannelQuality(context.Context, *SetTopicChannelQualityReq) (*SetTopicChannelQualityResp, error)
	// rcmd-common config
	GetRCMDCfg(context.Context, *GetRCMDCfgReq) (*GetRCMDCfgResp, error)
	// 清空下发过滤器
	ResetFilter(context.Context, *ResetFilterReq) (*ResetFilterResp, error)
}

func RegisterGenRecommendationServer(s *grpc.Server, srv GenRecommendationServer) {
	s.RegisterService(&_GenRecommendation_serviceDesc, srv)
}

func _GenRecommendation_GetRecommendationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).GetRecommendationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/GetRecommendationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).GetRecommendationList(ctx, req.(*GetRecommendationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_RecallChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).RecallChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/RecallChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).RecallChannelList(ctx, req.(*GetRecommendationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_SetTopicChannelQuality_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTopicChannelQualityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).SetTopicChannelQuality(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/SetTopicChannelQuality",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).SetTopicChannelQuality(ctx, req.(*SetTopicChannelQualityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_GetRCMDCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRCMDCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).GetRCMDCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/GetRCMDCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).GetRCMDCfg(ctx, req.(*GetRCMDCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenRecommendation_ResetFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenRecommendationServer).ResetFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.recommendation_gen.GenRecommendation/ResetFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenRecommendationServer).ResetFilter(ctx, req.(*ResetFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GenRecommendation_serviceDesc = grpc.ServiceDesc{
	ServiceName: "topic_channel.recommendation_gen.GenRecommendation",
	HandlerType: (*GenRecommendationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecommendationList",
			Handler:    _GenRecommendation_GetRecommendationList_Handler,
		},
		{
			MethodName: "RecallChannelList",
			Handler:    _GenRecommendation_RecallChannelList_Handler,
		},
		{
			MethodName: "SetTopicChannelQuality",
			Handler:    _GenRecommendation_SetTopicChannelQuality_Handler,
		},
		{
			MethodName: "GetRCMDCfg",
			Handler:    _GenRecommendation_GetRCMDCfg_Handler,
		},
		{
			MethodName: "ResetFilter",
			Handler:    _GenRecommendation_ResetFilter_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "topic_channel/recommendation_gen.proto",
}

func init() {
	proto.RegisterFile("topic_channel/recommendation_gen.proto", fileDescriptor_recommendation_gen_f7b347291b8b5742)
}

var fileDescriptor_recommendation_gen_f7b347291b8b5742 = []byte{
	// 2588 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x59, 0xdd, 0x52, 0xe3, 0xc8,
	0xf5, 0x1f, 0xd9, 0x80, 0xed, 0x63, 0x6c, 0x44, 0x03, 0x3b, 0x82, 0xd9, 0xf9, 0xff, 0x89, 0x52,
	0xd9, 0x10, 0x26, 0x81, 0x5d, 0x66, 0x37, 0x9b, 0xfd, 0xa8, 0xdd, 0x80, 0x31, 0xac, 0x2b, 0xb6,
	0x61, 0x65, 0x33, 0xb3, 0x1f, 0xb5, 0xa5, 0x6a, 0x4b, 0x8d, 0x50, 0x90, 0xd4, 0x1a, 0x75, 0x1b,
	0x86, 0xad, 0xca, 0x1b, 0xec, 0x45, 0xaa, 0xf2, 0x00, 0xb9, 0x4d, 0xe5, 0x2a, 0x37, 0x79, 0x8f,
	0x54, 0x9e, 0x26, 0xb9, 0x4b, 0x75, 0xb7, 0x6c, 0x4b, 0xc0, 0x2c, 0x5f, 0x17, 0xb9, 0xc8, 0x95,
	0xbb, 0xcf, 0xe9, 0xfe, 0x9d, 0xee, 0xf3, 0xdd, 0x32, 0xbc, 0xc3, 0x69, 0xec, 0x3b, 0xb6, 0x73,
	0x82, 0xa3, 0x88, 0x04, 0x9b, 0x09, 0x71, 0x68, 0x18, 0x92, 0xc8, 0xc5, 0xdc, 0xa7, 0x91, 0xed,
	0x91, 0x68, 0x23, 0x4e, 0x28, 0xa7, 0x68, 0x35, 0xb7, 0x6e, 0xe3, 0xea, 0xba, 0x95, 0x05, 0x41,
	0xa1, 0xd1, 0xa6, 0xfa, 0x51, 0xdb, 0x56, 0x2e, 0xc3, 0x3b, 0xa1, 0x3b, 0x9a, 0xd8, 0x01, 0x1e,
	0x90, 0x20, 0x5d, 0xf7, 0x8b, 0x1f, 0x3d, 0x46, 0x16, 0xd2, 0x34, 0xa1, 0x6e, 0x11, 0x46, 0xf8,
	0x9e, 0x1f, 0x70, 0x92, 0x58, 0xe4, 0x15, 0xd2, 0xa1, 0x38, 0xf4, 0x5d, 0x43, 0x5b, 0xd5, 0xd6,
	0x6a, 0x96, 0x18, 0x9a, 0x1f, 0xc2, 0x5c, 0x6e, 0x0d, 0x8b, 0x11, 0x82, 0x29, 0x87, 0xba, 0x24,
	0x5d, 0x25, 0xc7, 0x62, 0x63, 0xc8, 0x3c, 0xa3, 0xb0, 0xaa, 0xad, 0x55, 0x2c, 0x31, 0x34, 0x7f,
	0xd0, 0xa0, 0xb6, 0x4f, 0xb8, 0xd5, 0xe8, 0xec, 0x36, 0x8e, 0x3d, 0x01, 0xde, 0x85, 0x19, 0x46,
	0x87, 0x89, 0xa3, 0x76, 0xd6, 0xb7, 0x7e, 0xbd, 0x71, 0x93, 0x26, 0x36, 0x72, 0x00, 0x1b, 0xbd,
	0x83, 0x23, 0xab, 0xd1, 0xb4, 0x52, 0x14, 0xf3, 0x19, 0xcc, 0x28, 0x0a, 0x42, 0x50, 0x57, 0x23,
	0x7b, 0xb7, 0xb9, 0xb7, 0x7d, 0xd4, 0xee, 0xeb, 0x8f, 0x50, 0x0d, 0x2a, 0x29, 0xad, 0xdf, 0xd7,
	0x35, 0x73, 0x17, 0xea, 0x59, 0x30, 0x16, 0xa3, 0x2d, 0x58, 0x62, 0xc3, 0x98, 0x24, 0x67, 0x3e,
	0xa3, 0x89, 0x7d, 0x7e, 0xe2, 0x73, 0x62, 0x07, 0x3e, 0xe3, 0x86, 0xb6, 0x5a, 0x5c, 0xab, 0x59,
	0x0b, 0x13, 0xe6, 0x4b, 0xc1, 0x6b, 0xfb, 0x8c, 0x9b, 0xdb, 0x50, 0xdd, 0x09, 0xa8, 0x73, 0x7a,
	0x10, 0x8b, 0x23, 0xa2, 0x65, 0x28, 0x0f, 0xc4, 0xd4, 0x1e, 0xeb, 0xac, 0x24, 0xe7, 0x2d, 0x17,
	0x3d, 0x86, 0x12, 0x09, 0x48, 0x28, 0x38, 0x05, 0xc9, 0x99, 0x11, 0xd3, 0x96, 0x6b, 0xfe, 0x01,
	0x0c, 0x2b, 0x77, 0x51, 0x8b, 0xbc, 0x12, 0x1a, 0x4e, 0xce, 0x08, 0xc2, 0x30, 0xef, 0x87, 0xf2,
	0x10, 0x36, 0x79, 0x1d, 0xdb, 0x5e, 0x42, 0x87, 0xf1, 0xed, 0x95, 0xd5, 0xea, 0x34, 0x14, 0x53,
	0x1c, 0x75, 0x7b, 0x67, 0x5f, 0xec, 0xb6, 0xea, 0x7e, 0x28, 0xa6, 0xcd, 0xd7, 0xb1, 0x9c, 0x9b,
	0xdf, 0x43, 0xed, 0x30, 0x21, 0xc7, 0xfb, 0x38, 0x24, 0x6d, 0xe1, 0x35, 0xa8, 0x0e, 0x85, 0xf1,
	0xe9, 0x0b, 0xbe, 0x2b, 0x2c, 0x79, 0x86, 0x83, 0x91, 0x25, 0xcf, 0x70, 0x80, 0xf6, 0x61, 0x8a,
	0x5f, 0xc4, 0xc4, 0x28, 0xca, 0x83, 0x3c, 0xbf, 0xf9, 0x20, 0x39, 0x01, 0xfd, 0x8b, 0x98, 0x58,
	0x12, 0xc0, 0xfc, 0x3d, 0x94, 0x47, 0x2c, 0xb4, 0x04, 0x33, 0x1c, 0x0f, 0x26, 0x8a, 0x9b, 0xe6,
	0x78, 0xd0, 0x72, 0xd1, 0x3e, 0xcc, 0x48, 0x67, 0x66, 0x46, 0x61, 0xb5, 0xb8, 0x56, 0xdd, 0xda,
	0xbc, 0xa3, 0x34, 0x2b, 0xdd, 0x6e, 0xfe, 0xab, 0x06, 0x86, 0x30, 0x78, 0x6e, 0xbd, 0xd0, 0xc4,
	0xb5, 0x6e, 0x8e, 0x16, 0x61, 0x3a, 0xf0, 0x43, 0x9f, 0xa7, 0xc6, 0x52, 0x93, 0xcc, 0x21, 0x8b,
	0xd9, 0x43, 0x5a, 0x50, 0x53, 0x66, 0xa7, 0xd2, 0x0d, 0x98, 0x31, 0x25, 0xcf, 0xfa, 0xab, 0x9b,
	0xcf, 0x9a, 0x71, 0x1e, 0x6b, 0x76, 0x30, 0x99, 0x30, 0x84, 0xa1, 0xec, 0x11, 0x6e, 0x87, 0x22,
	0xb0, 0xa6, 0xa5, 0xa2, 0xf7, 0x6e, 0x17, 0x1e, 0xd7, 0x5d, 0x50, 0x30, 0xc4, 0xb0, 0x43, 0x5d,
	0x62, 0x95, 0x3c, 0x22, 0x07, 0xe8, 0x5d, 0x58, 0x1c, 0x25, 0x0c, 0x12, 0x71, 0x92, 0xd8, 0x69,
	0x34, 0xce, 0xc8, 0xbb, 0xa1, 0x94, 0xd7, 0x14, 0xac, 0x9e, 0xe4, 0xa0, 0xff, 0x87, 0xaa, 0x13,
	0xf8, 0x24, 0xe2, 0xb6, 0x74, 0x80, 0x92, 0x5c, 0x08, 0x8a, 0x24, 0xec, 0x8a, 0x7e, 0x06, 0xf5,
	0x74, 0xc1, 0x19, 0x49, 0x98, 0x4f, 0x23, 0xa3, 0x2c, 0xd7, 0xd4, 0x14, 0xf5, 0x85, 0x22, 0xa2,
	0x27, 0x50, 0x09, 0x71, 0x72, 0x4a, 0xb8, 0x50, 0x65, 0x45, 0xae, 0x28, 0x2b, 0x42, 0xcb, 0x45,
	0x5f, 0x41, 0x91, 0x44, 0x67, 0x06, 0x3c, 0xf8, 0xd2, 0xcd, 0xe8, 0xcc, 0x4f, 0x68, 0x14, 0x92,
	0x88, 0x5b, 0x02, 0x12, 0xfd, 0x12, 0x46, 0x97, 0xb2, 0x63, 0xec, 0x9c, 0x62, 0x8f, 0x08, 0xf9,
	0x55, 0xe9, 0xd9, 0x7a, 0xca, 0x39, 0x54, 0x8c, 0x96, 0x2b, 0x82, 0x99, 0x27, 0xd8, 0x91, 0x6b,
	0x66, 0xe5, 0x9a, 0x92, 0x9c, 0xb7, 0x5c, 0xf4, 0x53, 0xa8, 0x09, 0xc3, 0x04, 0xe3, 0x5b, 0xd6,
	0x24, 0x7f, 0x56, 0x12, 0x47, 0x97, 0x7c, 0x0a, 0xe0, 0x92, 0xc1, 0xd0, 0xb3, 0x8f, 0x03, 0xec,
	0x19, 0x75, 0x79, 0xcb, 0x8a, 0xa4, 0xec, 0x05, 0xd8, 0x43, 0x06, 0x94, 0x12, 0x15, 0xe6, 0xc6,
	0x9c, 0x42, 0x4f, 0xa7, 0xe8, 0x39, 0x94, 0x07, 0x98, 0x11, 0x3b, 0x21, 0xaf, 0x0c, 0x7d, 0x55,
	0x5b, 0xab, 0x6e, 0x19, 0x1b, 0x22, 0xbd, 0x6f, 0xa4, 0xc9, 0xda, 0x72, 0x42, 0x77, 0x07, 0x33,
	0x22, 0x12, 0x45, 0x69, 0xa0, 0x06, 0xc2, 0x85, 0x19, 0x79, 0x6d, 0xcc, 0x2b, 0x17, 0x66, 0xe4,
	0x35, 0xfa, 0x14, 0xaa, 0x83, 0x84, 0x9e, 0xb3, 0x34, 0x8b, 0x21, 0x89, 0xf4, 0xe4, 0x2a, 0x92,
	0x5c, 0xd3, 0x8a, 0x8e, 0xa9, 0x05, 0x6a, 0xbd, 0xd0, 0x1f, 0xfa, 0x0e, 0xf4, 0x84, 0x78, 0xc3,
	0x00, 0x73, 0x9a, 0x5c, 0xd8, 0x01, 0x39, 0x23, 0x81, 0xb1, 0x20, 0x4d, 0xb2, 0x75, 0xb3, 0x49,
	0xac, 0xe6, 0xfe, 0x51, 0x7b, 0xbb, 0x7f, 0x60, 0x7d, 0x6d, 0xb7, 0x9b, 0x2f, 0x9a, 0x6d, 0x6b,
	0x6e, 0x82, 0xd5, 0x16, 0x50, 0xe8, 0x39, 0xbc, 0xe5, 0x33, 0x3b, 0xf4, 0x23, 0x9a, 0xf8, 0xfc,
	0xc2, 0x8e, 0x71, 0x22, 0xbd, 0x0a, 0x0f, 0x8c, 0xc5, 0x55, 0x6d, 0xad, 0x6c, 0x2d, 0xf8, 0xac,
	0x93, 0x32, 0x0f, 0x25, 0xaf, 0x8f, 0x07, 0xe8, 0x27, 0x30, 0xeb, 0x60, 0x4e, 0x3c, 0x71, 0x22,
	0xdf, 0x65, 0xc6, 0x5b, 0x32, 0x31, 0x57, 0x47, 0xb4, 0x96, 0xcb, 0x44, 0x9a, 0x55, 0x11, 0xca,
	0x8c, 0xc7, 0x92, 0x3b, 0x23, 0x43, 0x94, 0xa1, 0x77, 0x60, 0xce, 0x67, 0xf6, 0xab, 0xd0, 0x3e,
	0x4e, 0x70, 0x48, 0xce, 0x69, 0x72, 0x6a, 0x18, 0x52, 0x52, 0xcd, 0x67, 0x5f, 0x86, 0x7b, 0x23,
	0x22, 0x6a, 0x01, 0xc4, 0x09, 0x39, 0xb6, 0x3d, 0x1c, 0x12, 0x66, 0x2c, 0xcb, 0x40, 0x5e, 0xbf,
	0x7d, 0xd2, 0xb1, 0x2a, 0x71, 0x3a, 0x62, 0xa8, 0x31, 0xce, 0x5d, 0x2b, 0x12, 0xe6, 0xd9, 0x65,
	0x98, 0xab, 0x25, 0xfb, 0x4a, 0xde, 0x42, 0x09, 0xcc, 0x0d, 0x86, 0xcc, 0x8f, 0x08, 0x63, 0xf6,
	0xb1, 0xac, 0xb9, 0xc6, 0x13, 0x89, 0xd6, 0x7a, 0x40, 0x64, 0xec, 0xa4, 0x88, 0x69, 0x11, 0xaf,
	0x0f, 0x72, 0x73, 0xf4, 0x12, 0x6a, 0xe2, 0xfa, 0x36, 0x23, 0x9c, 0xfb, 0x91, 0xc7, 0x8c, 0xb7,
	0xa5, 0xc4, 0x1b, 0x0c, 0x9f, 0xfa, 0x54, 0xdf, 0x11, 0x97, 0xe8, 0xa9, 0xad, 0xd6, 0xac, 0x37,
	0x99, 0x8c, 0x8c, 0x70, 0x8c, 0x83, 0x60, 0x80, 0x9d, 0x53, 0xe9, 0xe0, 0x4f, 0x47, 0x46, 0xd8,
	0x4b, 0xa9, 0xc2, 0x99, 0x7f, 0x0e, 0x73, 0xbe, 0x48, 0x3b, 0x84, 0x71, 0x3b, 0x55, 0xe1, 0xff,
	0xad, 0x16, 0xd7, 0x2a, 0x56, 0x7d, 0x44, 0x96, 0x4a, 0x62, 0x2b, 0x7f, 0xd7, 0xa0, 0x9e, 0xbf,
	0x0c, 0xb2, 0x00, 0x54, 0x32, 0x96, 0x29, 0x4a, 0xbb, 0x6d, 0x8d, 0x1a, 0xa1, 0xc8, 0x8c, 0xdc,
	0x8c, 0x86, 0xa1, 0x55, 0x91, 0x30, 0x32, 0xad, 0x1d, 0x40, 0x45, 0x16, 0x6f, 0x09, 0x29, 0x0a,
	0xd1, 0xad, 0xa2, 0x60, 0x04, 0xd9, 0x0c, 0x48, 0x28, 0x11, 0xcb, 0x02, 0x44, 0x00, 0x9a, 0x1f,
	0x40, 0x35, 0x93, 0x92, 0x51, 0x15, 0x4a, 0x93, 0x46, 0x65, 0x16, 0xca, 0xdd, 0xe6, 0x57, 0xfd,
	0xc3, 0xed, 0xfd, 0xa6, 0xae, 0x09, 0x96, 0xd5, 0xdc, 0xb3, 0x9a, 0xbd, 0x2f, 0xf4, 0x82, 0xf9,
	0x3e, 0x54, 0x33, 0x49, 0x0d, 0xd5, 0x85, 0xaf, 0x52, 0x77, 0xe8, 0x08, 0x91, 0xfa, 0x23, 0xb1,
	0x96, 0x71, 0xec, 0xf9, 0x91, 0xa7, 0x6b, 0xa8, 0x0c, 0x53, 0x9c, 0x30, 0xae, 0x17, 0xcc, 0xbf,
	0x16, 0xa1, 0x9a, 0x76, 0x02, 0x22, 0xcc, 0x55, 0x15, 0xf3, 0x72, 0xa5, 0xd6, 0x6b, 0xb9, 0x22,
	0xb9, 0x27, 0xc4, 0xc1, 0x41, 0xa0, 0x12, 0x96, 0x2a, 0x7c, 0xa0, 0x48, 0x32, 0x63, 0x3d, 0x83,
	0x62, 0x40, 0x1d, 0x59, 0xfa, 0xaa, 0x5b, 0xcb, 0xb9, 0x44, 0xd2, 0xa6, 0x8e, 0xbc, 0xb7, 0x4c,
	0x23, 0x62, 0x15, 0x6a, 0x43, 0x55, 0xfa, 0x77, 0x6a, 0xbe, 0x29, 0xa9, 0xb4, 0x67, 0xb7, 0x48,
	0x1d, 0x8d, 0xce, 0xae, 0x8a, 0x00, 0x10, 0xfb, 0x95, 0x9d, 0xd1, 0x77, 0x50, 0x0b, 0xa8, 0x63,
	0xb3, 0x13, 0x7a, 0xae, 0x8c, 0xa0, 0x4a, 0xe2, 0x47, 0x37, 0xe3, 0x65, 0x2e, 0x2e, 0x4e, 0xd9,
	0x3b, 0xa1, 0xe7, 0xb2, 0x03, 0xa9, 0x06, 0x93, 0x09, 0x7a, 0x0f, 0x96, 0x7c, 0x66, 0x47, 0xe4,
	0xdc, 0x1e, 0x32, 0x92, 0xd8, 0xc3, 0xc8, 0x25, 0x09, 0xc7, 0xa7, 0xaa, 0x14, 0x96, 0x2d, 0xe4,
	0xb3, 0x2e, 0x39, 0x3f, 0x62, 0x24, 0x39, 0x1a, 0x71, 0xcc, 0x17, 0x50, 0xcd, 0xc0, 0xa1, 0xc7,
	0xb0, 0x90, 0x99, 0x66, 0xda, 0x4e, 0x03, 0x16, 0xb3, 0x8c, 0x43, 0xeb, 0xe0, 0x45, 0xab, 0xdb,
	0x68, 0xea, 0x05, 0xb4, 0x08, 0x7a, 0x96, 0xd3, 0x68, 0xf5, 0xbf, 0xd6, 0x8b, 0xe6, 0x3f, 0x4a,
	0xb0, 0xfc, 0x86, 0xb8, 0x65, 0xb1, 0xa8, 0x29, 0xa3, 0x84, 0x21, 0xcd, 0x27, 0x32, 0x5c, 0x25,
	0xa5, 0xb4, 0x5c, 0x51, 0x7e, 0x07, 0x94, 0x73, 0x1a, 0xda, 0x09, 0xc1, 0xce, 0x09, 0x51, 0xbd,
	0x66, 0xd9, 0xaa, 0x29, 0xaa, 0xa5, 0x88, 0xe8, 0x02, 0xf4, 0x31, 0x4a, 0x74, 0x4c, 0xed, 0x10,
	0xc7, 0x46, 0x51, 0x86, 0xf8, 0xc1, 0xbd, 0x93, 0x0a, 0x8b, 0xb3, 0xaa, 0xee, 0xe0, 0xb8, 0x19,
	0xf1, 0xe4, 0xc2, 0xaa, 0x3b, 0x39, 0x62, 0xae, 0xa8, 0x4e, 0xe5, 0x8b, 0x2a, 0x83, 0xba, 0xaa,
	0x97, 0xe3, 0x33, 0x4d, 0xcb, 0x33, 0x75, 0x1e, 0x72, 0xa6, 0x5d, 0x81, 0x98, 0x3b, 0xd1, 0xac,
	0x9b, 0x21, 0xa1, 0xf7, 0xa1, 0xcc, 0x48, 0x70, 0x6c, 0x0b, 0xc7, 0x9e, 0xb9, 0xc9, 0xb1, 0x4b,
	0x62, 0x69, 0x9b, 0x3a, 0x97, 0x8a, 0x44, 0xe9, 0x21, 0x45, 0xe2, 0x29, 0x80, 0x1f, 0x31, 0x92,
	0x70, 0x3b, 0xa6, 0x2c, 0xed, 0x96, 0x2a, 0x8a, 0x72, 0x48, 0x19, 0xf2, 0xa0, 0x1a, 0x51, 0xee,
	0x1f, 0x5f, 0xa8, 0x22, 0x5e, 0x91, 0x61, 0xb4, 0xf7, 0x10, 0x8d, 0x74, 0x25, 0x9c, 0x8c, 0x01,
	0x50, 0xd0, 0xb2, 0xde, 0xbf, 0x0d, 0x95, 0x63, 0x4a, 0x79, 0x9c, 0xf8, 0x11, 0x97, 0xbd, 0x57,
	0xc5, 0x9a, 0x10, 0xd0, 0xef, 0xa0, 0x92, 0xb6, 0x24, 0x2c, 0x96, 0x0d, 0x53, 0x75, 0x6b, 0xe3,
	0x16, 0x09, 0x50, 0xf4, 0x26, 0x8d, 0xce, 0xae, 0x90, 0x6b, 0x95, 0x55, 0xa7, 0xc2, 0xe2, 0x95,
	0x18, 0x16, 0xae, 0x71, 0x15, 0xd1, 0xc1, 0x9c, 0x92, 0x8b, 0x51, 0x13, 0x7e, 0x4a, 0x2e, 0x50,
	0x03, 0xa6, 0xcf, 0x70, 0x30, 0x24, 0xd2, 0x8b, 0x6f, 0xd5, 0x4f, 0x67, 0x70, 0x2d, 0xb5, 0xf7,
	0xe3, 0xc2, 0x6f, 0xb4, 0x95, 0xcf, 0x61, 0xfe, 0x8a, 0x23, 0x64, 0xe5, 0x55, 0x94, 0xbc, 0xc5,
	0xac, 0xbc, 0x4a, 0x06, 0xc0, 0xfc, 0x04, 0x60, 0xa2, 0xb7, 0x7c, 0xba, 0x9e, 0x83, 0xea, 0xf6,
	0xe1, 0xe1, 0x41, 0xab, 0xdb, 0xef, 0x34, 0xbb, 0x7d, 0x5d, 0x13, 0x04, 0x8b, 0x1c, 0x27, 0x84,
	0x9d, 0xf4, 0x86, 0x8e, 0xa3, 0x17, 0xcc, 0x3f, 0x17, 0x60, 0x36, 0xab, 0x0a, 0xf1, 0xc4, 0xa2,
	0xa7, 0x52, 0x70, 0xd9, 0x2a, 0xd0, 0xd3, 0xf1, 0x03, 0x5a, 0x88, 0x9d, 0xca, 0x3f, 0xa0, 0x8b,
	0xe3, 0x07, 0x34, 0x72, 0x61, 0x2e, 0x0e, 0x70, 0x64, 0x0f, 0x6c, 0xc6, 0x13, 0xd1, 0xf2, 0x5c,
	0xc8, 0x08, 0xaa, 0x6f, 0x7d, 0x7a, 0x37, 0x4b, 0x6c, 0x1c, 0x06, 0x38, 0xda, 0xe9, 0xa5, 0x18,
	0x56, 0x2d, 0xce, 0x4e, 0x45, 0x03, 0xe5, 0x33, 0x1b, 0x07, 0x1e, 0x95, 0x39, 0xb6, 0x6c, 0xcd,
	0xf8, 0x6c, 0x3b, 0xf0, 0xa8, 0xf9, 0x0d, 0xd4, 0x72, 0x1b, 0xd1, 0x32, 0x2c, 0xe5, 0x08, 0x76,
	0xab, 0xfb, 0x62, 0xbb, 0xdd, 0xda, 0xd5, 0x1f, 0x89, 0xd4, 0x96, 0x67, 0x89, 0x27, 0xb7, 0x48,
	0x85, 0x79, 0xaa, 0xb5, 0xdd, 0xdd, 0x3d, 0xe8, 0xe8, 0x05, 0xf3, 0x9f, 0x1a, 0x2c, 0xf7, 0x08,
	0xef, 0x8b, 0x6b, 0xa4, 0x26, 0xfc, 0x72, 0x88, 0x03, 0x9f, 0x5f, 0x88, 0x6e, 0xe0, 0x86, 0xa4,
	0xe7, 0x43, 0xe9, 0x95, 0x5a, 0x2c, 0x15, 0x58, 0xbf, 0x4d, 0x12, 0x7b, 0xa3, 0xb0, 0x8d, 0xeb,
	0xc8, 0x23, 0x7c, 0xf3, 0x19, 0x2c, 0x5c, 0xc3, 0xcf, 0xfb, 0x43, 0x09, 0x8a, 0xed, 0x83, 0x97,
	0xba, 0x66, 0xbe, 0x0d, 0x2b, 0x6f, 0x12, 0xc3, 0x62, 0xf3, 0x73, 0x58, 0x6a, 0xd0, 0x30, 0xc6,
	0x91, 0x4f, 0xa3, 0x94, 0xdd, 0x3c, 0x13, 0x45, 0xfd, 0x1d, 0x98, 0x9b, 0xdc, 0x36, 0xfb, 0x01,
	0xa2, 0x36, 0xbe, 0xb2, 0xfc, 0xf4, 0xf0, 0xef, 0x19, 0x98, 0xef, 0x12, 0x0f, 0x73, 0xff, 0x8c,
	0xec, 0x11, 0xe2, 0xaa, 0xdd, 0x57, 0x5f, 0xb2, 0xdf, 0x42, 0x59, 0x16, 0x35, 0x91, 0x50, 0xd5,
	0x1b, 0xfa, 0xb7, 0x37, 0xeb, 0xe7, 0x0a, 0xf0, 0x86, 0xa8, 0x7e, 0xe3, 0x1c, 0x5a, 0x1a, 0xaa,
	0x19, 0xfa, 0x4a, 0xb5, 0xdb, 0x93, 0x02, 0xf2, 0xf9, 0x7d, 0xb0, 0xfb, 0x78, 0x30, 0x86, 0x16,
	0xfd, 0xba, 0x40, 0x7e, 0x35, 0xa9, 0x51, 0x0e, 0x8d, 0x5c, 0x29, 0x42, 0x3d, 0xab, 0xf7, 0xef,
	0x23, 0x22, 0x55, 0x71, 0x83, 0x46, 0xee, 0x95, 0xda, 0x94, 0x12, 0xb3, 0x22, 0x23, 0xd1, 0xfe,
	0x4e, 0x4a, 0xd0, 0x43, 0x44, 0x76, 0x71, 0x48, 0xae, 0x88, 0x4c, 0x89, 0xe8, 0x04, 0xf4, 0x61,
	0xec, 0x62, 0x4e, 0x64, 0x57, 0xa3, 0xac, 0x3d, 0x23, 0x73, 0xfc, 0x67, 0xf7, 0x32, 0x92, 0xc4,
	0x92, 0xb9, 0xbd, 0x3e, 0x1c, 0x8f, 0x85, 0xbb, 0xac, 0x7c, 0x0c, 0xb3, 0x59, 0x13, 0x5e, 0x93,
	0x6d, 0x73, 0xd9, 0xaf, 0x98, 0x4d, 0x9f, 0x1f, 0x41, 0x35, 0x63, 0xa2, 0x3b, 0x6d, 0xdd, 0x1e,
	0xe7, 0xfa, 0xac, 0xea, 0x6f, 0xca, 0xbd, 0x6f, 0x80, 0xc8, 0xaa, 0xf2, 0x2e, 0x10, 0x66, 0x17,
	0x60, 0xa2, 0x1a, 0x11, 0xae, 0xad, 0xe8, 0x0c, 0x07, 0xbe, 0xab, 0x3f, 0x12, 0x6d, 0xb2, 0xd0,
	0x8b, 0xae, 0x89, 0xc0, 0xed, 0xe3, 0x81, 0x5e, 0x10, 0x09, 0x3c, 0x73, 0x66, 0xbd, 0x98, 0x21,
	0x88, 0x13, 0xe8, 0x53, 0xe6, 0x5f, 0xa6, 0x41, 0x17, 0xbb, 0xb2, 0x16, 0x40, 0xdf, 0x64, 0x02,
	0x4d, 0xbb, 0x6d, 0x30, 0x5c, 0x46, 0x79, 0x43, 0x9c, 0xbd, 0x9c, 0xc4, 0x99, 0x8a, 0xe1, 0xcf,
	0xee, 0x01, 0x7d, 0x5d, 0x98, 0xc5, 0xd7, 0x84, 0x99, 0x8a, 0xe4, 0xbd, 0x7b, 0x48, 0xb8, 0x4d,
	0x94, 0xc5, 0xd7, 0x44, 0xd9, 0xd4, 0x43, 0x25, 0xfe, 0x58, 0x90, 0xfd, 0x6f, 0xbb, 0xfe, 0x36,
	0x2c, 0xa7, 0x10, 0x7d, 0x9f, 0x07, 0xa4, 0x4f, 0x4f, 0x49, 0xe4, 0x7f, 0x4f, 0xc6, 0xd5, 0xc2,
	0x99, 0x54, 0x0b, 0xc7, 0x77, 0x45, 0x2b, 0xc2, 0xb1, 0xa7, 0xbe, 0xb6, 0x56, 0x2c, 0x39, 0x36,
	0xbf, 0x85, 0x27, 0x87, 0x01, 0xbe, 0x08, 0x31, 0x27, 0xad, 0x88, 0x93, 0x48, 0xb5, 0x93, 0xa1,
	0xcf, 0x15, 0xc8, 0xe5, 0xf2, 0xac, 0xe5, 0xcb, 0xf3, 0x53, 0x00, 0xf9, 0xf1, 0xd4, 0xe6, 0x7e,
	0x38, 0x3a, 0x5f, 0x45, 0x52, 0xfa, 0x7e, 0x48, 0xd6, 0x03, 0x58, 0xbc, 0xee, 0x3b, 0x75, 0xbe,
	0xa6, 0x3e, 0x86, 0x05, 0x8b, 0x78, 0x6d, 0xc2, 0xd8, 0x87, 0x5b, 0x5f, 0xd0, 0x61, 0x62, 0x37,
	0x5f, 0xc7, 0xf6, 0xb6, 0xae, 0x5d, 0xcf, 0xd8, 0xd1, 0x0b, 0x68, 0x09, 0xe6, 0x2d, 0xe2, 0x75,
	0x68, 0x42, 0x26, 0x0c, 0xbd, 0xb8, 0xbe, 0x01, 0xfa, 0xe5, 0x6f, 0x53, 0x22, 0x03, 0xec, 0x59,
	0xcd, 0xa6, 0xfe, 0x08, 0xe9, 0x30, 0xdb, 0x6b, 0x75, 0x0e, 0xdb, 0x4d, 0xbb, 0xd3, 0xea, 0x1e,
	0x58, 0xba, 0xb6, 0xfe, 0x0c, 0xe6, 0xaf, 0x7c, 0xbc, 0x46, 0x00, 0x33, 0xbd, 0xaf, 0x7b, 0xfd,
	0x66, 0x47, 0x7f, 0x24, 0xc6, 0x8d, 0xa3, 0x5e, 0xff, 0xa0, 0xa3, 0x6b, 0xeb, 0xef, 0xc3, 0xfc,
	0x95, 0xaf, 0x08, 0xe2, 0x35, 0x6f, 0x51, 0x1a, 0x8a, 0x67, 0xbe, 0xfe, 0x08, 0xcd, 0x43, 0x4d,
	0x60, 0x09, 0x87, 0xf0, 0xe5, 0xa3, 0x5d, 0x5b, 0xff, 0x12, 0xf4, 0xcb, 0x1f, 0x0a, 0xc4, 0xe5,
	0xbb, 0x54, 0xaa, 0x58, 0x89, 0xe8, 0xf9, 0x91, 0x17, 0x10, 0x5d, 0x13, 0xe3, 0x5d, 0x3a, 0x1c,
	0x04, 0x44, 0x2f, 0x88, 0x45, 0x2f, 0xb1, 0xcf, 0xc5, 0x6b, 0xbf, 0x28, 0x26, 0x3d, 0x8e, 0x13,
	0x4e, 0x5c, 0x7d, 0x6a, 0xbd, 0x09, 0x95, 0xf1, 0x33, 0x5a, 0x5c, 0xaf, 0x4b, 0x23, 0xa2, 0xb4,
	0xb8, 0x8f, 0x23, 0xef, 0x28, 0x7e, 0xe9, 0xf3, 0x93, 0x2f, 0x68, 0x48, 0x0e, 0xce, 0x23, 0x99,
	0xf9, 0x96, 0x60, 0xbe, 0x71, 0x82, 0x79, 0x9e, 0x5c, 0xd8, 0xfa, 0xdb, 0x34, 0xcc, 0xef, 0x93,
	0x28, 0xff, 0x8e, 0x40, 0x7f, 0xd4, 0x60, 0xe9, 0xda, 0xd7, 0x05, 0xfa, 0xf8, 0xfe, 0x5f, 0xa4,
	0x56, 0x3e, 0x79, 0xc0, 0x93, 0x06, 0xfd, 0xa0, 0x09, 0x6b, 0x3b, 0x38, 0x08, 0x32, 0x8e, 0xf4,
	0xdf, 0x3b, 0xce, 0x9f, 0x34, 0x78, 0xeb, 0xfa, 0xce, 0x0f, 0x7d, 0xf2, 0x80, 0xd6, 0x74, 0xe5,
	0xd3, 0xfb, 0x6f, 0x66, 0x31, 0xa2, 0x00, 0x93, 0x3f, 0xbc, 0xd0, 0xe6, 0x1d, 0xff, 0x6b, 0x5b,
	0x79, 0xf7, 0x6e, 0x1b, 0x58, 0x8c, 0x12, 0xf1, 0x0e, 0x1a, 0xff, 0x53, 0x88, 0x6e, 0x01, 0x90,
	0xff, 0xf3, 0x71, 0xe5, 0xbd, 0x3b, 0xee, 0x60, 0xf1, 0xce, 0xee, 0x37, 0x3b, 0x1e, 0x0d, 0x70,
	0xe4, 0x6d, 0x7c, 0xb0, 0xc5, 0xb9, 0x78, 0xc4, 0x6f, 0xca, 0xbf, 0x36, 0x1d, 0x1a, 0x6c, 0x32,
	0x92, 0x9c, 0xf9, 0x0e, 0x61, 0x9b, 0x37, 0xfd, 0x2f, 0x3b, 0x98, 0x91, 0x7b, 0x9e, 0xff, 0x27,
	0x00, 0x00, 0xff, 0xff, 0x3d, 0x52, 0x4f, 0xd6, 0xc2, 0x1d, 0x00, 0x00,
}
