// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-operate-permission-checker/channel-operate-permission-checker.proto

package channel_operate_permission_checker // import "golang.52tt.com/protocol/services/channel-operate-permission-checker"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CheckOperatePermissionReq struct {
	BusinessId           uint32   `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	Opt                  uint32   `protobuf:"varint,2,opt,name=opt,proto3" json:"opt,omitempty"`
	OptUid               uint32   `protobuf:"varint,3,opt,name=opt_uid,json=optUid,proto3" json:"opt_uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,4,opt,name=cid,proto3" json:"cid,omitempty"`
	SchemeId             uint32   `protobuf:"varint,5,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeDetailType     uint32   `protobuf:"varint,6,opt,name=scheme_detail_type,json=schemeDetailType,proto3" json:"scheme_detail_type,omitempty"`
	ChannelBindId        uint32   `protobuf:"varint,7,opt,name=channel_bind_id,json=channelBindId,proto3" json:"channel_bind_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,8,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	TargetUid            uint32   `protobuf:"varint,9,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	RuleName             string   `protobuf:"bytes,10,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	ExtendPbData         []byte   `protobuf:"bytes,11,opt,name=extend_pb_data,json=extendPbData,proto3" json:"extend_pb_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckOperatePermissionReq) Reset()         { *m = CheckOperatePermissionReq{} }
func (m *CheckOperatePermissionReq) String() string { return proto.CompactTextString(m) }
func (*CheckOperatePermissionReq) ProtoMessage()    {}
func (*CheckOperatePermissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_operate_permission_checker_5557b3047c7a56aa, []int{0}
}
func (m *CheckOperatePermissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckOperatePermissionReq.Unmarshal(m, b)
}
func (m *CheckOperatePermissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckOperatePermissionReq.Marshal(b, m, deterministic)
}
func (dst *CheckOperatePermissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckOperatePermissionReq.Merge(dst, src)
}
func (m *CheckOperatePermissionReq) XXX_Size() int {
	return xxx_messageInfo_CheckOperatePermissionReq.Size(m)
}
func (m *CheckOperatePermissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckOperatePermissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckOperatePermissionReq proto.InternalMessageInfo

func (m *CheckOperatePermissionReq) GetBusinessId() uint32 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *CheckOperatePermissionReq) GetOpt() uint32 {
	if m != nil {
		return m.Opt
	}
	return 0
}

func (m *CheckOperatePermissionReq) GetOptUid() uint32 {
	if m != nil {
		return m.OptUid
	}
	return 0
}

func (m *CheckOperatePermissionReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *CheckOperatePermissionReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *CheckOperatePermissionReq) GetSchemeDetailType() uint32 {
	if m != nil {
		return m.SchemeDetailType
	}
	return 0
}

func (m *CheckOperatePermissionReq) GetChannelBindId() uint32 {
	if m != nil {
		return m.ChannelBindId
	}
	return 0
}

func (m *CheckOperatePermissionReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *CheckOperatePermissionReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *CheckOperatePermissionReq) GetRuleName() string {
	if m != nil {
		return m.RuleName
	}
	return ""
}

func (m *CheckOperatePermissionReq) GetExtendPbData() []byte {
	if m != nil {
		return m.ExtendPbData
	}
	return nil
}

type CheckOperatePermissionResp struct {
	PermissionErrCode    int32    `protobuf:"varint,1,opt,name=permission_err_code,json=permissionErrCode,proto3" json:"permission_err_code,omitempty"`
	PermissionErrMsg     string   `protobuf:"bytes,2,opt,name=permission_err_msg,json=permissionErrMsg,proto3" json:"permission_err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckOperatePermissionResp) Reset()         { *m = CheckOperatePermissionResp{} }
func (m *CheckOperatePermissionResp) String() string { return proto.CompactTextString(m) }
func (*CheckOperatePermissionResp) ProtoMessage()    {}
func (*CheckOperatePermissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_operate_permission_checker_5557b3047c7a56aa, []int{1}
}
func (m *CheckOperatePermissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckOperatePermissionResp.Unmarshal(m, b)
}
func (m *CheckOperatePermissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckOperatePermissionResp.Marshal(b, m, deterministic)
}
func (dst *CheckOperatePermissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckOperatePermissionResp.Merge(dst, src)
}
func (m *CheckOperatePermissionResp) XXX_Size() int {
	return xxx_messageInfo_CheckOperatePermissionResp.Size(m)
}
func (m *CheckOperatePermissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckOperatePermissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckOperatePermissionResp proto.InternalMessageInfo

func (m *CheckOperatePermissionResp) GetPermissionErrCode() int32 {
	if m != nil {
		return m.PermissionErrCode
	}
	return 0
}

func (m *CheckOperatePermissionResp) GetPermissionErrMsg() string {
	if m != nil {
		return m.PermissionErrMsg
	}
	return ""
}

// 麦位相关操作携带扩展字段
type MicOperateExtendInfo struct {
	MicId                uint32   `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	MicState             uint32   `protobuf:"varint,2,opt,name=mic_state,json=micState,proto3" json:"mic_state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicOperateExtendInfo) Reset()         { *m = MicOperateExtendInfo{} }
func (m *MicOperateExtendInfo) String() string { return proto.CompactTextString(m) }
func (*MicOperateExtendInfo) ProtoMessage()    {}
func (*MicOperateExtendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_operate_permission_checker_5557b3047c7a56aa, []int{2}
}
func (m *MicOperateExtendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicOperateExtendInfo.Unmarshal(m, b)
}
func (m *MicOperateExtendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicOperateExtendInfo.Marshal(b, m, deterministic)
}
func (dst *MicOperateExtendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicOperateExtendInfo.Merge(dst, src)
}
func (m *MicOperateExtendInfo) XXX_Size() int {
	return xxx_messageInfo_MicOperateExtendInfo.Size(m)
}
func (m *MicOperateExtendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicOperateExtendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicOperateExtendInfo proto.InternalMessageInfo

func (m *MicOperateExtendInfo) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *MicOperateExtendInfo) GetMicState() uint32 {
	if m != nil {
		return m.MicState
	}
	return 0
}

func init() {
	proto.RegisterType((*CheckOperatePermissionReq)(nil), "channel_operate_permission_checker.CheckOperatePermissionReq")
	proto.RegisterType((*CheckOperatePermissionResp)(nil), "channel_operate_permission_checker.CheckOperatePermissionResp")
	proto.RegisterType((*MicOperateExtendInfo)(nil), "channel_operate_permission_checker.MicOperateExtendInfo")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelOperatePermissionCheckerClient is the client API for ChannelOperatePermissionChecker service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelOperatePermissionCheckerClient interface {
	// 如果没有操作权限，应该把对应的错误码填到resp的permission_err_code里，不能直接在接口里返回没有操作权限的错误,接口里返回的错误会用来做熔断降级
	CheckOperatePermission(ctx context.Context, in *CheckOperatePermissionReq, opts ...grpc.CallOption) (*CheckOperatePermissionResp, error)
}

type channelOperatePermissionCheckerClient struct {
	cc *grpc.ClientConn
}

func NewChannelOperatePermissionCheckerClient(cc *grpc.ClientConn) ChannelOperatePermissionCheckerClient {
	return &channelOperatePermissionCheckerClient{cc}
}

func (c *channelOperatePermissionCheckerClient) CheckOperatePermission(ctx context.Context, in *CheckOperatePermissionReq, opts ...grpc.CallOption) (*CheckOperatePermissionResp, error) {
	out := new(CheckOperatePermissionResp)
	err := c.cc.Invoke(ctx, "/channel_operate_permission_checker.ChannelOperatePermissionChecker/CheckOperatePermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelOperatePermissionCheckerServer is the server API for ChannelOperatePermissionChecker service.
type ChannelOperatePermissionCheckerServer interface {
	// 如果没有操作权限，应该把对应的错误码填到resp的permission_err_code里，不能直接在接口里返回没有操作权限的错误,接口里返回的错误会用来做熔断降级
	CheckOperatePermission(context.Context, *CheckOperatePermissionReq) (*CheckOperatePermissionResp, error)
}

func RegisterChannelOperatePermissionCheckerServer(s *grpc.Server, srv ChannelOperatePermissionCheckerServer) {
	s.RegisterService(&_ChannelOperatePermissionChecker_serviceDesc, srv)
}

func _ChannelOperatePermissionChecker_CheckOperatePermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckOperatePermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOperatePermissionCheckerServer).CheckOperatePermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_operate_permission_checker.ChannelOperatePermissionChecker/CheckOperatePermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOperatePermissionCheckerServer).CheckOperatePermission(ctx, req.(*CheckOperatePermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelOperatePermissionChecker_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_operate_permission_checker.ChannelOperatePermissionChecker",
	HandlerType: (*ChannelOperatePermissionCheckerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckOperatePermission",
			Handler:    _ChannelOperatePermissionChecker_CheckOperatePermission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-operate-permission-checker/channel-operate-permission-checker.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-operate-permission-checker/channel-operate-permission-checker.proto", fileDescriptor_channel_operate_permission_checker_5557b3047c7a56aa)
}

var fileDescriptor_channel_operate_permission_checker_5557b3047c7a56aa = []byte{
	// 492 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x93, 0xcd, 0x6e, 0x13, 0x31,
	0x10, 0x80, 0xd9, 0x96, 0xa4, 0xc9, 0x24, 0x85, 0x60, 0xfe, 0x96, 0x22, 0xd4, 0x10, 0x21, 0x94,
	0x03, 0xd9, 0x48, 0x45, 0x1c, 0xe1, 0xd0, 0xa4, 0x48, 0x41, 0x2a, 0x54, 0x0b, 0xbd, 0xf4, 0xb2,
	0x72, 0xec, 0x61, 0x63, 0x75, 0xd7, 0x76, 0x6d, 0xa7, 0xa2, 0xbc, 0x09, 0x0f, 0xc3, 0x43, 0xf1,
	0x06, 0x68, 0xed, 0x0d, 0xe1, 0xaf, 0xa2, 0x12, 0x37, 0xef, 0x37, 0x9f, 0x67, 0x3d, 0x33, 0x36,
	0x9c, 0x38, 0x37, 0x3e, 0x5b, 0x0a, 0x76, 0x6a, 0x45, 0x71, 0x8e, 0x66, 0xcc, 0x16, 0x54, 0x4a,
	0x2c, 0x46, 0x4a, 0xa3, 0xa1, 0x0e, 0x47, 0x1a, 0x4d, 0x29, 0xac, 0x15, 0x4a, 0x8e, 0xd8, 0x02,
	0xd9, 0xe9, 0x95, 0x94, 0x44, 0x1b, 0xe5, 0x14, 0x19, 0xd4, 0x66, 0x56, 0x9b, 0xd9, 0xda, 0xcc,
	0x6a, 0x73, 0xf0, 0x6d, 0x03, 0x1e, 0x4c, 0xaa, 0xf5, 0xbb, 0xe0, 0x1c, 0xfd, 0x50, 0x52, 0x3c,
	0x23, 0xbb, 0xd0, 0x99, 0x2f, 0xad, 0x90, 0x68, 0x6d, 0x26, 0x78, 0x1c, 0xf5, 0xa3, 0xe1, 0x76,
	0x0a, 0x2b, 0x34, 0xe3, 0xa4, 0x07, 0x9b, 0x4a, 0xbb, 0x78, 0xc3, 0x07, 0xaa, 0x25, 0xb9, 0x0f,
	0x5b, 0x4a, 0xbb, 0x6c, 0x29, 0x78, 0xbc, 0xe9, 0x69, 0x53, 0x69, 0x77, 0x2c, 0xbc, 0xca, 0x04,
	0x8f, 0xaf, 0x07, 0x95, 0x09, 0x4e, 0x1e, 0x42, 0xdb, 0xb2, 0x05, 0x96, 0x58, 0xe5, 0x6e, 0x78,
	0xde, 0x0a, 0x60, 0xc6, 0xc9, 0x33, 0x20, 0x75, 0x90, 0xa3, 0xa3, 0xa2, 0xc8, 0xdc, 0x85, 0xc6,
	0xb8, 0xe9, 0xad, 0x5e, 0x88, 0x4c, 0x7d, 0xe0, 0xc3, 0x85, 0x46, 0xf2, 0x14, 0x6e, 0xae, 0x8a,
	0x9d, 0x0b, 0xc9, 0xab, 0x84, 0x5b, 0x5e, 0xdd, 0xae, 0xf1, 0xbe, 0x90, 0x7c, 0xc6, 0xc9, 0x63,
	0xe8, 0xae, 0x3c, 0x9f, 0xaf, 0xe5, 0xa5, 0x4e, 0xcd, 0x7c, 0xaa, 0x47, 0x00, 0x8e, 0x9a, 0x1c,
	0x43, 0x0d, 0x6d, 0x2f, 0xb4, 0x03, 0x39, 0x0e, 0x87, 0x36, 0xcb, 0x02, 0x33, 0x49, 0x4b, 0x8c,
	0xa1, 0x1f, 0x0d, 0xdb, 0x69, 0xab, 0x02, 0x6f, 0x69, 0x89, 0xe4, 0x09, 0xdc, 0xc0, 0x4f, 0x0e,
	0x25, 0xcf, 0xf4, 0x3c, 0xe3, 0xd4, 0xd1, 0xb8, 0xd3, 0x8f, 0x86, 0xdd, 0xb4, 0x1b, 0xe8, 0xd1,
	0x7c, 0x4a, 0x1d, 0x1d, 0x7c, 0x86, 0x9d, 0xcb, 0x5a, 0x6e, 0x35, 0x49, 0xe0, 0xf6, 0x4f, 0x73,
	0x42, 0x63, 0x32, 0xa6, 0x38, 0xfa, 0xde, 0x37, 0xd2, 0x5b, 0xeb, 0xd0, 0x81, 0x31, 0x13, 0xc5,
	0xb1, 0x6a, 0xd4, 0x6f, 0x7e, 0x69, 0x73, 0x3f, 0x91, 0x76, 0xda, 0xfb, 0x45, 0x3f, 0xb4, 0xf9,
	0xe0, 0x0d, 0xdc, 0x39, 0x14, 0xac, 0xfe, 0xf3, 0x81, 0x3f, 0xd5, 0x4c, 0x7e, 0x54, 0xe4, 0x2e,
	0x34, 0x4b, 0xc1, 0xd6, 0x43, 0x6e, 0x94, 0x82, 0xcd, 0x7c, 0xb5, 0x15, 0xb6, 0x8e, 0x3a, 0xac,
	0xa7, 0xdc, 0x2a, 0x05, 0x7b, 0x5f, 0x7d, 0xef, 0x7d, 0x8d, 0x60, 0x77, 0x12, 0x3a, 0xf7, 0x47,
	0x29, 0x93, 0x70, 0xbf, 0xc8, 0x97, 0x08, 0xee, 0xfd, 0xbd, 0x58, 0xf2, 0x32, 0xf9, 0xf7, 0xfd,
	0x4c, 0x2e, 0xbd, 0x9b, 0x3b, 0xaf, 0xfe, 0x67, 0xbb, 0xd5, 0x83, 0x6b, 0xfb, 0xaf, 0x4f, 0xa6,
	0xb9, 0x2a, 0xa8, 0xcc, 0x93, 0x17, 0x7b, 0xce, 0x25, 0x4c, 0x95, 0x63, 0xff, 0x70, 0x98, 0x2a,
	0xc6, 0x16, 0xcd, 0xb9, 0x60, 0x68, 0xaf, 0xf0, 0xda, 0xe6, 0x4d, 0xbf, 0xeb, 0xf9, 0xf7, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x1e, 0x53, 0x23, 0xf5, 0xcc, 0x03, 0x00, 0x00,
}
