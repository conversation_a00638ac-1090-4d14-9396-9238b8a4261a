// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/sign-anchor-stats/sign-anchor-stats.proto

package sign_anchor_stats

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockSignAnchorStatsClient is a mock of SignAnchorStatsClient interface.
type MockSignAnchorStatsClient struct {
	ctrl     *gomock.Controller
	recorder *MockSignAnchorStatsClientMockRecorder
}

// MockSignAnchorStatsClientMockRecorder is the mock recorder for MockSignAnchorStatsClient.
type MockSignAnchorStatsClientMockRecorder struct {
	mock *MockSignAnchorStatsClient
}

// NewMockSignAnchorStatsClient creates a new mock instance.
func NewMockSignAnchorStatsClient(ctrl *gomock.Controller) *MockSignAnchorStatsClient {
	mock := &MockSignAnchorStatsClient{ctrl: ctrl}
	mock.recorder = &MockSignAnchorStatsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSignAnchorStatsClient) EXPECT() *MockSignAnchorStatsClientMockRecorder {
	return m.recorder
}

// AddHallTaskConf mocks base method.
func (m *MockSignAnchorStatsClient) AddHallTaskConf(ctx context.Context, in *AddHallTaskConfReq, opts ...grpc.CallOption) (*AddHallTaskConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddHallTaskConf", varargs...)
	ret0, _ := ret[0].(*AddHallTaskConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddHallTaskConf indicates an expected call of AddHallTaskConf.
func (mr *MockSignAnchorStatsClientMockRecorder) AddHallTaskConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddHallTaskConf", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).AddHallTaskConf), varargs...)
}

// AddMultiPlayerHall mocks base method.
func (m *MockSignAnchorStatsClient) AddMultiPlayerHall(ctx context.Context, in *AddMultiPlayerHallReq, opts ...grpc.CallOption) (*AddMultiPlayerHallResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddMultiPlayerHall", varargs...)
	ret0, _ := ret[0].(*AddMultiPlayerHallResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMultiPlayerHall indicates an expected call of AddMultiPlayerHall.
func (mr *MockSignAnchorStatsClientMockRecorder) AddMultiPlayerHall(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMultiPlayerHall", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).AddMultiPlayerHall), varargs...)
}

// BatchGetBindGuildInfo mocks base method.
func (m *MockSignAnchorStatsClient) BatchGetBindGuildInfo(ctx context.Context, in *BatchGetBindGuildInfoReq, opts ...grpc.CallOption) (*BatchGetBindGuildInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetBindGuildInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetBindGuildInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBindGuildInfo indicates an expected call of BatchGetBindGuildInfo.
func (mr *MockSignAnchorStatsClientMockRecorder) BatchGetBindGuildInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBindGuildInfo", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).BatchGetBindGuildInfo), varargs...)
}

// CheckUserInteractEntry mocks base method.
func (m *MockSignAnchorStatsClient) CheckUserInteractEntry(ctx context.Context, in *CheckUserInteractEntryReq, opts ...grpc.CallOption) (*CheckUserInteractEntryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckUserInteractEntry", varargs...)
	ret0, _ := ret[0].(*CheckUserInteractEntryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserInteractEntry indicates an expected call of CheckUserInteractEntry.
func (mr *MockSignAnchorStatsClientMockRecorder) CheckUserInteractEntry(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserInteractEntry", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).CheckUserInteractEntry), varargs...)
}

// DelHallTask mocks base method.
func (m *MockSignAnchorStatsClient) DelHallTask(ctx context.Context, in *DelHallTaskReq, opts ...grpc.CallOption) (*DelHallTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelHallTask", varargs...)
	ret0, _ := ret[0].(*DelHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelHallTask indicates an expected call of DelHallTask.
func (mr *MockSignAnchorStatsClientMockRecorder) DelHallTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHallTask", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).DelHallTask), varargs...)
}

// DelHallTaskConf mocks base method.
func (m *MockSignAnchorStatsClient) DelHallTaskConf(ctx context.Context, in *DelHallTaskConfReq, opts ...grpc.CallOption) (*DelHallTaskConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelHallTaskConf", varargs...)
	ret0, _ := ret[0].(*DelHallTaskConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelHallTaskConf indicates an expected call of DelHallTaskConf.
func (mr *MockSignAnchorStatsClientMockRecorder) DelHallTaskConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHallTaskConf", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).DelHallTaskConf), varargs...)
}

// DelMultiPlayerHall mocks base method.
func (m *MockSignAnchorStatsClient) DelMultiPlayerHall(ctx context.Context, in *DelMultiPlayerHallReq, opts ...grpc.CallOption) (*DelMultiPlayerHallResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelMultiPlayerHall", varargs...)
	ret0, _ := ret[0].(*DelMultiPlayerHallResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelMultiPlayerHall indicates an expected call of DelMultiPlayerHall.
func (mr *MockSignAnchorStatsClientMockRecorder) DelMultiPlayerHall(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMultiPlayerHall", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).DelMultiPlayerHall), varargs...)
}

// DistributeHallTask mocks base method.
func (m *MockSignAnchorStatsClient) DistributeHallTask(ctx context.Context, in *DistributeHallTaskReq, opts ...grpc.CallOption) (*DistributeHallTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DistributeHallTask", varargs...)
	ret0, _ := ret[0].(*DistributeHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DistributeHallTask indicates an expected call of DistributeHallTask.
func (mr *MockSignAnchorStatsClientMockRecorder) DistributeHallTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistributeHallTask", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).DistributeHallTask), varargs...)
}

// GetBindGuildInfo mocks base method.
func (m *MockSignAnchorStatsClient) GetBindGuildInfo(ctx context.Context, in *GetBindGuildInfoReq, opts ...grpc.CallOption) (*GetBindGuildInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBindGuildInfo", varargs...)
	ret0, _ := ret[0].(*GetBindGuildInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindGuildInfo indicates an expected call of GetBindGuildInfo.
func (mr *MockSignAnchorStatsClientMockRecorder) GetBindGuildInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindGuildInfo", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetBindGuildInfo), varargs...)
}

// GetBindGuildInfoList mocks base method.
func (m *MockSignAnchorStatsClient) GetBindGuildInfoList(ctx context.Context, in *GetBindGuildInfoListReq, opts ...grpc.CallOption) (*GetBindGuildInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBindGuildInfoList", varargs...)
	ret0, _ := ret[0].(*GetBindGuildInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindGuildInfoList indicates an expected call of GetBindGuildInfoList.
func (mr *MockSignAnchorStatsClientMockRecorder) GetBindGuildInfoList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindGuildInfoList", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetBindGuildInfoList), varargs...)
}

// GetGuildChannelList mocks base method.
func (m *MockSignAnchorStatsClient) GetGuildChannelList(ctx context.Context, in *GetGuildChannelListReq, opts ...grpc.CallOption) (*GetGuildChannelListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildChannelList", varargs...)
	ret0, _ := ret[0].(*GetGuildChannelListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildChannelList indicates an expected call of GetGuildChannelList.
func (mr *MockSignAnchorStatsClientMockRecorder) GetGuildChannelList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildChannelList", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetGuildChannelList), varargs...)
}

// GetGuildHallTask mocks base method.
func (m *MockSignAnchorStatsClient) GetGuildHallTask(ctx context.Context, in *GetGuildHallTaskReq, opts ...grpc.CallOption) (*GetGuildHallTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildHallTask", varargs...)
	ret0, _ := ret[0].(*GetGuildHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildHallTask indicates an expected call of GetGuildHallTask.
func (mr *MockSignAnchorStatsClientMockRecorder) GetGuildHallTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildHallTask", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetGuildHallTask), varargs...)
}

// GetGuildHallTaskStats mocks base method.
func (m *MockSignAnchorStatsClient) GetGuildHallTaskStats(ctx context.Context, in *GetGuildHallTaskStatsReq, opts ...grpc.CallOption) (*GetGuildHallTaskStatsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildHallTaskStats", varargs...)
	ret0, _ := ret[0].(*GetGuildHallTaskStatsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildHallTaskStats indicates an expected call of GetGuildHallTaskStats.
func (mr *MockSignAnchorStatsClientMockRecorder) GetGuildHallTaskStats(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildHallTaskStats", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetGuildHallTaskStats), varargs...)
}

// GetGuildHallTaskStatsDetial mocks base method.
func (m *MockSignAnchorStatsClient) GetGuildHallTaskStatsDetial(ctx context.Context, in *GetGuildHallTaskStatsDetialReq, opts ...grpc.CallOption) (*GetGuildHallTaskStatsDetialResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildHallTaskStatsDetial", varargs...)
	ret0, _ := ret[0].(*GetGuildHallTaskStatsDetialResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildHallTaskStatsDetial indicates an expected call of GetGuildHallTaskStatsDetial.
func (mr *MockSignAnchorStatsClientMockRecorder) GetGuildHallTaskStatsDetial(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildHallTaskStatsDetial", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetGuildHallTaskStatsDetial), varargs...)
}

// GetGuildMonthlyStatsInfoList mocks base method.
func (m *MockSignAnchorStatsClient) GetGuildMonthlyStatsInfoList(ctx context.Context, in *GetGuildMonthlyStatsInfoListReq, opts ...grpc.CallOption) (*GetGuildMonthlyStatsInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildMonthlyStatsInfoList", varargs...)
	ret0, _ := ret[0].(*GetGuildMonthlyStatsInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildMonthlyStatsInfoList indicates an expected call of GetGuildMonthlyStatsInfoList.
func (mr *MockSignAnchorStatsClientMockRecorder) GetGuildMonthlyStatsInfoList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildMonthlyStatsInfoList", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetGuildMonthlyStatsInfoList), varargs...)
}

// GetGuildMultiPlayerHall mocks base method.
func (m *MockSignAnchorStatsClient) GetGuildMultiPlayerHall(ctx context.Context, in *GetGuildMultiPlayerHallReq, opts ...grpc.CallOption) (*GetGuildMultiPlayerHallResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildMultiPlayerHall", varargs...)
	ret0, _ := ret[0].(*GetGuildMultiPlayerHallResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildMultiPlayerHall indicates an expected call of GetGuildMultiPlayerHall.
func (mr *MockSignAnchorStatsClientMockRecorder) GetGuildMultiPlayerHall(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildMultiPlayerHall", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetGuildMultiPlayerHall), varargs...)
}

// GetHallTask mocks base method.
func (m *MockSignAnchorStatsClient) GetHallTask(ctx context.Context, in *GetHallTaskReq, opts ...grpc.CallOption) (*GetHallTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHallTask", varargs...)
	ret0, _ := ret[0].(*GetHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTask indicates an expected call of GetHallTask.
func (mr *MockSignAnchorStatsClientMockRecorder) GetHallTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTask", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetHallTask), varargs...)
}

// GetHallTaskCacheInfo mocks base method.
func (m *MockSignAnchorStatsClient) GetHallTaskCacheInfo(ctx context.Context, in *GetHallTaskReq, opts ...grpc.CallOption) (*GetHallTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHallTaskCacheInfo", varargs...)
	ret0, _ := ret[0].(*GetHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskCacheInfo indicates an expected call of GetHallTaskCacheInfo.
func (mr *MockSignAnchorStatsClientMockRecorder) GetHallTaskCacheInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskCacheInfo", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetHallTaskCacheInfo), varargs...)
}

// GetHallTaskConfById mocks base method.
func (m *MockSignAnchorStatsClient) GetHallTaskConfById(ctx context.Context, in *GetHallTaskConfByIdReq, opts ...grpc.CallOption) (*GetHallTaskConfByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHallTaskConfById", varargs...)
	ret0, _ := ret[0].(*GetHallTaskConfByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskConfById indicates an expected call of GetHallTaskConfById.
func (mr *MockSignAnchorStatsClientMockRecorder) GetHallTaskConfById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskConfById", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetHallTaskConfById), varargs...)
}

// GetHallTaskConfList mocks base method.
func (m *MockSignAnchorStatsClient) GetHallTaskConfList(ctx context.Context, in *GetHallTaskConfListReq, opts ...grpc.CallOption) (*GetHallTaskConfListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHallTaskConfList", varargs...)
	ret0, _ := ret[0].(*GetHallTaskConfListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskConfList indicates an expected call of GetHallTaskConfList.
func (mr *MockSignAnchorStatsClientMockRecorder) GetHallTaskConfList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskConfList", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetHallTaskConfList), varargs...)
}

// GetHallTaskDistributeHistory mocks base method.
func (m *MockSignAnchorStatsClient) GetHallTaskDistributeHistory(ctx context.Context, in *GetHallTaskDistributeHistoryReq, opts ...grpc.CallOption) (*GetHallTaskDistributeHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHallTaskDistributeHistory", varargs...)
	ret0, _ := ret[0].(*GetHallTaskDistributeHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskDistributeHistory indicates an expected call of GetHallTaskDistributeHistory.
func (mr *MockSignAnchorStatsClientMockRecorder) GetHallTaskDistributeHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskDistributeHistory", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetHallTaskDistributeHistory), varargs...)
}

// GetHallTaskHistory mocks base method.
func (m *MockSignAnchorStatsClient) GetHallTaskHistory(ctx context.Context, in *GetHallTaskHistoryReq, opts ...grpc.CallOption) (*GetHallTaskHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHallTaskHistory", varargs...)
	ret0, _ := ret[0].(*GetHallTaskHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskHistory indicates an expected call of GetHallTaskHistory.
func (mr *MockSignAnchorStatsClientMockRecorder) GetHallTaskHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskHistory", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetHallTaskHistory), varargs...)
}

// GetMultiAnchorChannelStat mocks base method.
func (m *MockSignAnchorStatsClient) GetMultiAnchorChannelStat(ctx context.Context, in *GetMultiAnchorChannelStatReq, opts ...grpc.CallOption) (*GetMultiAnchorChannelStatResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiAnchorChannelStat", varargs...)
	ret0, _ := ret[0].(*GetMultiAnchorChannelStatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiAnchorChannelStat indicates an expected call of GetMultiAnchorChannelStat.
func (mr *MockSignAnchorStatsClientMockRecorder) GetMultiAnchorChannelStat(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorChannelStat", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetMultiAnchorChannelStat), varargs...)
}

// GetMultiAnchorDailyStatsList mocks base method.
func (m *MockSignAnchorStatsClient) GetMultiAnchorDailyStatsList(ctx context.Context, in *GetMultiAnchorDailyStatsListReq, opts ...grpc.CallOption) (*GetMultiAnchorDailyStatsListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiAnchorDailyStatsList", varargs...)
	ret0, _ := ret[0].(*GetMultiAnchorDailyStatsListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiAnchorDailyStatsList indicates an expected call of GetMultiAnchorDailyStatsList.
func (mr *MockSignAnchorStatsClientMockRecorder) GetMultiAnchorDailyStatsList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorDailyStatsList", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetMultiAnchorDailyStatsList), varargs...)
}

// GetMultiAnchorDailyStatsListByGuildId mocks base method.
func (m *MockSignAnchorStatsClient) GetMultiAnchorDailyStatsListByGuildId(ctx context.Context, in *GetMultiAnchorDailyStatsListByGuildIdReq, opts ...grpc.CallOption) (*GetMultiAnchorDailyStatsListByGuildIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiAnchorDailyStatsListByGuildId", varargs...)
	ret0, _ := ret[0].(*GetMultiAnchorDailyStatsListByGuildIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiAnchorDailyStatsListByGuildId indicates an expected call of GetMultiAnchorDailyStatsListByGuildId.
func (mr *MockSignAnchorStatsClientMockRecorder) GetMultiAnchorDailyStatsListByGuildId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorDailyStatsListByGuildId", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetMultiAnchorDailyStatsListByGuildId), varargs...)
}

// GetMultiAnchorMonthStat mocks base method.
func (m *MockSignAnchorStatsClient) GetMultiAnchorMonthStat(ctx context.Context, in *GetMultiAnchorMonthStatReq, opts ...grpc.CallOption) (*GetMultiAnchorMonthStatResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiAnchorMonthStat", varargs...)
	ret0, _ := ret[0].(*GetMultiAnchorMonthStatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiAnchorMonthStat indicates an expected call of GetMultiAnchorMonthStat.
func (mr *MockSignAnchorStatsClientMockRecorder) GetMultiAnchorMonthStat(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorMonthStat", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetMultiAnchorMonthStat), varargs...)
}

// GetMultiPlayerBaseInfo mocks base method.
func (m *MockSignAnchorStatsClient) GetMultiPlayerBaseInfo(ctx context.Context, in *GetMultiPlayerBaseInfoReq, opts ...grpc.CallOption) (*GetMultiPlayerBaseInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiPlayerBaseInfo", varargs...)
	ret0, _ := ret[0].(*GetMultiPlayerBaseInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiPlayerBaseInfo indicates an expected call of GetMultiPlayerBaseInfo.
func (mr *MockSignAnchorStatsClientMockRecorder) GetMultiPlayerBaseInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerBaseInfo", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetMultiPlayerBaseInfo), varargs...)
}

// GetMultiPlayerHomepage mocks base method.
func (m *MockSignAnchorStatsClient) GetMultiPlayerHomepage(ctx context.Context, in *GetMultiPlayerHomepageReq, opts ...grpc.CallOption) (*GetMultiPlayerHomepageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiPlayerHomepage", varargs...)
	ret0, _ := ret[0].(*GetMultiPlayerHomepageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiPlayerHomepage indicates an expected call of GetMultiPlayerHomepage.
func (mr *MockSignAnchorStatsClientMockRecorder) GetMultiPlayerHomepage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerHomepage", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetMultiPlayerHomepage), varargs...)
}

// GetMultiPlayerMonthCommunityInfo mocks base method.
func (m *MockSignAnchorStatsClient) GetMultiPlayerMonthCommunityInfo(ctx context.Context, in *GetMultiPlayerMonthCommunityInfoReq, opts ...grpc.CallOption) (*GetMultiPlayerMonthCommunityInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiPlayerMonthCommunityInfo", varargs...)
	ret0, _ := ret[0].(*GetMultiPlayerMonthCommunityInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiPlayerMonthCommunityInfo indicates an expected call of GetMultiPlayerMonthCommunityInfo.
func (mr *MockSignAnchorStatsClientMockRecorder) GetMultiPlayerMonthCommunityInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerMonthCommunityInfo", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetMultiPlayerMonthCommunityInfo), varargs...)
}

// GetMultiPlayerMonthConsumeTop10 mocks base method.
func (m *MockSignAnchorStatsClient) GetMultiPlayerMonthConsumeTop10(ctx context.Context, in *GetMultiPlayerMonthConsumeTop10Req, opts ...grpc.CallOption) (*GetMultiPlayerMonthConsumeTop10Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiPlayerMonthConsumeTop10", varargs...)
	ret0, _ := ret[0].(*GetMultiPlayerMonthConsumeTop10Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiPlayerMonthConsumeTop10 indicates an expected call of GetMultiPlayerMonthConsumeTop10.
func (mr *MockSignAnchorStatsClientMockRecorder) GetMultiPlayerMonthConsumeTop10(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerMonthConsumeTop10", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetMultiPlayerMonthConsumeTop10), varargs...)
}

// GetMultiThisMonthChannelStat mocks base method.
func (m *MockSignAnchorStatsClient) GetMultiThisMonthChannelStat(ctx context.Context, in *GetMultiThisMonthChannelStatReq, opts ...grpc.CallOption) (*GetMultiThisMonthChannelStatResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiThisMonthChannelStat", varargs...)
	ret0, _ := ret[0].(*GetMultiThisMonthChannelStatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiThisMonthChannelStat indicates an expected call of GetMultiThisMonthChannelStat.
func (mr *MockSignAnchorStatsClientMockRecorder) GetMultiThisMonthChannelStat(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiThisMonthChannelStat", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetMultiThisMonthChannelStat), varargs...)
}

// GetPgcDailyInfoList mocks base method.
func (m *MockSignAnchorStatsClient) GetPgcDailyInfoList(ctx context.Context, in *GetPgcDailyInfoListReq, opts ...grpc.CallOption) (*GetPgcDailyInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPgcDailyInfoList", varargs...)
	ret0, _ := ret[0].(*GetPgcDailyInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcDailyInfoList indicates an expected call of GetPgcDailyInfoList.
func (mr *MockSignAnchorStatsClientMockRecorder) GetPgcDailyInfoList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcDailyInfoList", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetPgcDailyInfoList), varargs...)
}

// GetPgcMonthlyInfoList mocks base method.
func (m *MockSignAnchorStatsClient) GetPgcMonthlyInfoList(ctx context.Context, in *GetPgcMonthlyInfoListReq, opts ...grpc.CallOption) (*GetPgcMonthlyInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPgcMonthlyInfoList", varargs...)
	ret0, _ := ret[0].(*GetPgcMonthlyInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcMonthlyInfoList indicates an expected call of GetPgcMonthlyInfoList.
func (mr *MockSignAnchorStatsClientMockRecorder) GetPgcMonthlyInfoList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcMonthlyInfoList", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetPgcMonthlyInfoList), varargs...)
}

// GetPresentOrderCount mocks base method.
func (m *MockSignAnchorStatsClient) GetPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentOrderCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentOrderCount indicates an expected call of GetPresentOrderCount.
func (mr *MockSignAnchorStatsClientMockRecorder) GetPresentOrderCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentOrderCount", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetPresentOrderCount), varargs...)
}

// GetPresentOrderList mocks base method.
func (m *MockSignAnchorStatsClient) GetPresentOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentOrderList", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentOrderList indicates an expected call of GetPresentOrderList.
func (mr *MockSignAnchorStatsClientMockRecorder) GetPresentOrderList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentOrderList", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetPresentOrderList), varargs...)
}

// GetTicketOrderCount mocks base method.
func (m *MockSignAnchorStatsClient) GetTicketOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTicketOrderCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketOrderCount indicates an expected call of GetTicketOrderCount.
func (mr *MockSignAnchorStatsClientMockRecorder) GetTicketOrderCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketOrderCount", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetTicketOrderCount), varargs...)
}

// GetTicketOrderList mocks base method.
func (m *MockSignAnchorStatsClient) GetTicketOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTicketOrderList", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketOrderList indicates an expected call of GetTicketOrderList.
func (mr *MockSignAnchorStatsClientMockRecorder) GetTicketOrderList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketOrderList", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetTicketOrderList), varargs...)
}

// GetUserInteractInfo mocks base method.
func (m *MockSignAnchorStatsClient) GetUserInteractInfo(ctx context.Context, in *GetUserInteractInfoReq, opts ...grpc.CallOption) (*GetUserInteractInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserInteractInfo", varargs...)
	ret0, _ := ret[0].(*GetUserInteractInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInteractInfo indicates an expected call of GetUserInteractInfo.
func (mr *MockSignAnchorStatsClientMockRecorder) GetUserInteractInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInteractInfo", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetUserInteractInfo), varargs...)
}

// GetUserInteractViewPer mocks base method.
func (m *MockSignAnchorStatsClient) GetUserInteractViewPer(ctx context.Context, in *GetUserInteractViewPerReq, opts ...grpc.CallOption) (*GetUserInteractViewPerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserInteractViewPer", varargs...)
	ret0, _ := ret[0].(*GetUserInteractViewPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInteractViewPer indicates an expected call of GetUserInteractViewPer.
func (mr *MockSignAnchorStatsClientMockRecorder) GetUserInteractViewPer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInteractViewPer", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetUserInteractViewPer), varargs...)
}

// GetUserTbeanConsume mocks base method.
func (m *MockSignAnchorStatsClient) GetUserTbeanConsume(ctx context.Context, in *GetUserTbeanConsumeReq, opts ...grpc.CallOption) (*GetUserTbeanConsumeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserTbeanConsume", varargs...)
	ret0, _ := ret[0].(*GetUserTbeanConsumeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTbeanConsume indicates an expected call of GetUserTbeanConsume.
func (mr *MockSignAnchorStatsClientMockRecorder) GetUserTbeanConsume(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTbeanConsume", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetUserTbeanConsume), varargs...)
}

// GetValidHoldDayUid mocks base method.
func (m *MockSignAnchorStatsClient) GetValidHoldDayUid(ctx context.Context, in *GetValidHoldDayUidReq, opts ...grpc.CallOption) (*GetValidHoldDayUidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetValidHoldDayUid", varargs...)
	ret0, _ := ret[0].(*GetValidHoldDayUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetValidHoldDayUid indicates an expected call of GetValidHoldDayUid.
func (mr *MockSignAnchorStatsClientMockRecorder) GetValidHoldDayUid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidHoldDayUid", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).GetValidHoldDayUid), varargs...)
}

// ListMultiPlayerHall mocks base method.
func (m *MockSignAnchorStatsClient) ListMultiPlayerHall(ctx context.Context, in *ListMultiPlayerHallReq, opts ...grpc.CallOption) (*ListMultiPlayerHallResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListMultiPlayerHall", varargs...)
	ret0, _ := ret[0].(*ListMultiPlayerHallResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMultiPlayerHall indicates an expected call of ListMultiPlayerHall.
func (mr *MockSignAnchorStatsClientMockRecorder) ListMultiPlayerHall(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMultiPlayerHall", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).ListMultiPlayerHall), varargs...)
}

// ReplaceOrder mocks base method.
func (m *MockSignAnchorStatsClient) ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReplaceOrder", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReplaceOrder indicates an expected call of ReplaceOrder.
func (mr *MockSignAnchorStatsClientMockRecorder) ReplaceOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplaceOrder", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).ReplaceOrder), varargs...)
}

// SetUserInteractViewPer mocks base method.
func (m *MockSignAnchorStatsClient) SetUserInteractViewPer(ctx context.Context, in *SetUserInteractViewPerReq, opts ...grpc.CallOption) (*SetUserInteractViewPerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserInteractViewPer", varargs...)
	ret0, _ := ret[0].(*SetUserInteractViewPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserInteractViewPer indicates an expected call of SetUserInteractViewPer.
func (mr *MockSignAnchorStatsClientMockRecorder) SetUserInteractViewPer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserInteractViewPer", reflect.TypeOf((*MockSignAnchorStatsClient)(nil).SetUserInteractViewPer), varargs...)
}

// MockSignAnchorStatsServer is a mock of SignAnchorStatsServer interface.
type MockSignAnchorStatsServer struct {
	ctrl     *gomock.Controller
	recorder *MockSignAnchorStatsServerMockRecorder
}

// MockSignAnchorStatsServerMockRecorder is the mock recorder for MockSignAnchorStatsServer.
type MockSignAnchorStatsServerMockRecorder struct {
	mock *MockSignAnchorStatsServer
}

// NewMockSignAnchorStatsServer creates a new mock instance.
func NewMockSignAnchorStatsServer(ctrl *gomock.Controller) *MockSignAnchorStatsServer {
	mock := &MockSignAnchorStatsServer{ctrl: ctrl}
	mock.recorder = &MockSignAnchorStatsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSignAnchorStatsServer) EXPECT() *MockSignAnchorStatsServerMockRecorder {
	return m.recorder
}

// AddHallTaskConf mocks base method.
func (m *MockSignAnchorStatsServer) AddHallTaskConf(ctx context.Context, in *AddHallTaskConfReq) (*AddHallTaskConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddHallTaskConf", ctx, in)
	ret0, _ := ret[0].(*AddHallTaskConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddHallTaskConf indicates an expected call of AddHallTaskConf.
func (mr *MockSignAnchorStatsServerMockRecorder) AddHallTaskConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddHallTaskConf", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).AddHallTaskConf), ctx, in)
}

// AddMultiPlayerHall mocks base method.
func (m *MockSignAnchorStatsServer) AddMultiPlayerHall(ctx context.Context, in *AddMultiPlayerHallReq) (*AddMultiPlayerHallResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMultiPlayerHall", ctx, in)
	ret0, _ := ret[0].(*AddMultiPlayerHallResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMultiPlayerHall indicates an expected call of AddMultiPlayerHall.
func (mr *MockSignAnchorStatsServerMockRecorder) AddMultiPlayerHall(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMultiPlayerHall", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).AddMultiPlayerHall), ctx, in)
}

// BatchGetBindGuildInfo mocks base method.
func (m *MockSignAnchorStatsServer) BatchGetBindGuildInfo(ctx context.Context, in *BatchGetBindGuildInfoReq) (*BatchGetBindGuildInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBindGuildInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetBindGuildInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBindGuildInfo indicates an expected call of BatchGetBindGuildInfo.
func (mr *MockSignAnchorStatsServerMockRecorder) BatchGetBindGuildInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBindGuildInfo", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).BatchGetBindGuildInfo), ctx, in)
}

// CheckUserInteractEntry mocks base method.
func (m *MockSignAnchorStatsServer) CheckUserInteractEntry(ctx context.Context, in *CheckUserInteractEntryReq) (*CheckUserInteractEntryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserInteractEntry", ctx, in)
	ret0, _ := ret[0].(*CheckUserInteractEntryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserInteractEntry indicates an expected call of CheckUserInteractEntry.
func (mr *MockSignAnchorStatsServerMockRecorder) CheckUserInteractEntry(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserInteractEntry", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).CheckUserInteractEntry), ctx, in)
}

// DelHallTask mocks base method.
func (m *MockSignAnchorStatsServer) DelHallTask(ctx context.Context, in *DelHallTaskReq) (*DelHallTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelHallTask", ctx, in)
	ret0, _ := ret[0].(*DelHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelHallTask indicates an expected call of DelHallTask.
func (mr *MockSignAnchorStatsServerMockRecorder) DelHallTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHallTask", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).DelHallTask), ctx, in)
}

// DelHallTaskConf mocks base method.
func (m *MockSignAnchorStatsServer) DelHallTaskConf(ctx context.Context, in *DelHallTaskConfReq) (*DelHallTaskConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelHallTaskConf", ctx, in)
	ret0, _ := ret[0].(*DelHallTaskConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelHallTaskConf indicates an expected call of DelHallTaskConf.
func (mr *MockSignAnchorStatsServerMockRecorder) DelHallTaskConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHallTaskConf", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).DelHallTaskConf), ctx, in)
}

// DelMultiPlayerHall mocks base method.
func (m *MockSignAnchorStatsServer) DelMultiPlayerHall(ctx context.Context, in *DelMultiPlayerHallReq) (*DelMultiPlayerHallResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMultiPlayerHall", ctx, in)
	ret0, _ := ret[0].(*DelMultiPlayerHallResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelMultiPlayerHall indicates an expected call of DelMultiPlayerHall.
func (mr *MockSignAnchorStatsServerMockRecorder) DelMultiPlayerHall(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMultiPlayerHall", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).DelMultiPlayerHall), ctx, in)
}

// DistributeHallTask mocks base method.
func (m *MockSignAnchorStatsServer) DistributeHallTask(ctx context.Context, in *DistributeHallTaskReq) (*DistributeHallTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DistributeHallTask", ctx, in)
	ret0, _ := ret[0].(*DistributeHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DistributeHallTask indicates an expected call of DistributeHallTask.
func (mr *MockSignAnchorStatsServerMockRecorder) DistributeHallTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistributeHallTask", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).DistributeHallTask), ctx, in)
}

// GetBindGuildInfo mocks base method.
func (m *MockSignAnchorStatsServer) GetBindGuildInfo(ctx context.Context, in *GetBindGuildInfoReq) (*GetBindGuildInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindGuildInfo", ctx, in)
	ret0, _ := ret[0].(*GetBindGuildInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindGuildInfo indicates an expected call of GetBindGuildInfo.
func (mr *MockSignAnchorStatsServerMockRecorder) GetBindGuildInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindGuildInfo", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetBindGuildInfo), ctx, in)
}

// GetBindGuildInfoList mocks base method.
func (m *MockSignAnchorStatsServer) GetBindGuildInfoList(ctx context.Context, in *GetBindGuildInfoListReq) (*GetBindGuildInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBindGuildInfoList", ctx, in)
	ret0, _ := ret[0].(*GetBindGuildInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBindGuildInfoList indicates an expected call of GetBindGuildInfoList.
func (mr *MockSignAnchorStatsServerMockRecorder) GetBindGuildInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBindGuildInfoList", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetBindGuildInfoList), ctx, in)
}

// GetGuildChannelList mocks base method.
func (m *MockSignAnchorStatsServer) GetGuildChannelList(ctx context.Context, in *GetGuildChannelListReq) (*GetGuildChannelListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildChannelList", ctx, in)
	ret0, _ := ret[0].(*GetGuildChannelListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildChannelList indicates an expected call of GetGuildChannelList.
func (mr *MockSignAnchorStatsServerMockRecorder) GetGuildChannelList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildChannelList", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetGuildChannelList), ctx, in)
}

// GetGuildHallTask mocks base method.
func (m *MockSignAnchorStatsServer) GetGuildHallTask(ctx context.Context, in *GetGuildHallTaskReq) (*GetGuildHallTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildHallTask", ctx, in)
	ret0, _ := ret[0].(*GetGuildHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildHallTask indicates an expected call of GetGuildHallTask.
func (mr *MockSignAnchorStatsServerMockRecorder) GetGuildHallTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildHallTask", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetGuildHallTask), ctx, in)
}

// GetGuildHallTaskStats mocks base method.
func (m *MockSignAnchorStatsServer) GetGuildHallTaskStats(ctx context.Context, in *GetGuildHallTaskStatsReq) (*GetGuildHallTaskStatsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildHallTaskStats", ctx, in)
	ret0, _ := ret[0].(*GetGuildHallTaskStatsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildHallTaskStats indicates an expected call of GetGuildHallTaskStats.
func (mr *MockSignAnchorStatsServerMockRecorder) GetGuildHallTaskStats(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildHallTaskStats", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetGuildHallTaskStats), ctx, in)
}

// GetGuildHallTaskStatsDetial mocks base method.
func (m *MockSignAnchorStatsServer) GetGuildHallTaskStatsDetial(ctx context.Context, in *GetGuildHallTaskStatsDetialReq) (*GetGuildHallTaskStatsDetialResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildHallTaskStatsDetial", ctx, in)
	ret0, _ := ret[0].(*GetGuildHallTaskStatsDetialResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildHallTaskStatsDetial indicates an expected call of GetGuildHallTaskStatsDetial.
func (mr *MockSignAnchorStatsServerMockRecorder) GetGuildHallTaskStatsDetial(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildHallTaskStatsDetial", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetGuildHallTaskStatsDetial), ctx, in)
}

// GetGuildMonthlyStatsInfoList mocks base method.
func (m *MockSignAnchorStatsServer) GetGuildMonthlyStatsInfoList(ctx context.Context, in *GetGuildMonthlyStatsInfoListReq) (*GetGuildMonthlyStatsInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildMonthlyStatsInfoList", ctx, in)
	ret0, _ := ret[0].(*GetGuildMonthlyStatsInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildMonthlyStatsInfoList indicates an expected call of GetGuildMonthlyStatsInfoList.
func (mr *MockSignAnchorStatsServerMockRecorder) GetGuildMonthlyStatsInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildMonthlyStatsInfoList", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetGuildMonthlyStatsInfoList), ctx, in)
}

// GetGuildMultiPlayerHall mocks base method.
func (m *MockSignAnchorStatsServer) GetGuildMultiPlayerHall(ctx context.Context, in *GetGuildMultiPlayerHallReq) (*GetGuildMultiPlayerHallResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildMultiPlayerHall", ctx, in)
	ret0, _ := ret[0].(*GetGuildMultiPlayerHallResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildMultiPlayerHall indicates an expected call of GetGuildMultiPlayerHall.
func (mr *MockSignAnchorStatsServerMockRecorder) GetGuildMultiPlayerHall(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildMultiPlayerHall", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetGuildMultiPlayerHall), ctx, in)
}

// GetHallTask mocks base method.
func (m *MockSignAnchorStatsServer) GetHallTask(ctx context.Context, in *GetHallTaskReq) (*GetHallTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTask", ctx, in)
	ret0, _ := ret[0].(*GetHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTask indicates an expected call of GetHallTask.
func (mr *MockSignAnchorStatsServerMockRecorder) GetHallTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTask", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetHallTask), ctx, in)
}

// GetHallTaskCacheInfo mocks base method.
func (m *MockSignAnchorStatsServer) GetHallTaskCacheInfo(ctx context.Context, in *GetHallTaskReq) (*GetHallTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTaskCacheInfo", ctx, in)
	ret0, _ := ret[0].(*GetHallTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskCacheInfo indicates an expected call of GetHallTaskCacheInfo.
func (mr *MockSignAnchorStatsServerMockRecorder) GetHallTaskCacheInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskCacheInfo", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetHallTaskCacheInfo), ctx, in)
}

// GetHallTaskConfById mocks base method.
func (m *MockSignAnchorStatsServer) GetHallTaskConfById(ctx context.Context, in *GetHallTaskConfByIdReq) (*GetHallTaskConfByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTaskConfById", ctx, in)
	ret0, _ := ret[0].(*GetHallTaskConfByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskConfById indicates an expected call of GetHallTaskConfById.
func (mr *MockSignAnchorStatsServerMockRecorder) GetHallTaskConfById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskConfById", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetHallTaskConfById), ctx, in)
}

// GetHallTaskConfList mocks base method.
func (m *MockSignAnchorStatsServer) GetHallTaskConfList(ctx context.Context, in *GetHallTaskConfListReq) (*GetHallTaskConfListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTaskConfList", ctx, in)
	ret0, _ := ret[0].(*GetHallTaskConfListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskConfList indicates an expected call of GetHallTaskConfList.
func (mr *MockSignAnchorStatsServerMockRecorder) GetHallTaskConfList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskConfList", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetHallTaskConfList), ctx, in)
}

// GetHallTaskDistributeHistory mocks base method.
func (m *MockSignAnchorStatsServer) GetHallTaskDistributeHistory(ctx context.Context, in *GetHallTaskDistributeHistoryReq) (*GetHallTaskDistributeHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTaskDistributeHistory", ctx, in)
	ret0, _ := ret[0].(*GetHallTaskDistributeHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskDistributeHistory indicates an expected call of GetHallTaskDistributeHistory.
func (mr *MockSignAnchorStatsServerMockRecorder) GetHallTaskDistributeHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskDistributeHistory", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetHallTaskDistributeHistory), ctx, in)
}

// GetHallTaskHistory mocks base method.
func (m *MockSignAnchorStatsServer) GetHallTaskHistory(ctx context.Context, in *GetHallTaskHistoryReq) (*GetHallTaskHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHallTaskHistory", ctx, in)
	ret0, _ := ret[0].(*GetHallTaskHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHallTaskHistory indicates an expected call of GetHallTaskHistory.
func (mr *MockSignAnchorStatsServerMockRecorder) GetHallTaskHistory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHallTaskHistory", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetHallTaskHistory), ctx, in)
}

// GetMultiAnchorChannelStat mocks base method.
func (m *MockSignAnchorStatsServer) GetMultiAnchorChannelStat(ctx context.Context, in *GetMultiAnchorChannelStatReq) (*GetMultiAnchorChannelStatResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiAnchorChannelStat", ctx, in)
	ret0, _ := ret[0].(*GetMultiAnchorChannelStatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiAnchorChannelStat indicates an expected call of GetMultiAnchorChannelStat.
func (mr *MockSignAnchorStatsServerMockRecorder) GetMultiAnchorChannelStat(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorChannelStat", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetMultiAnchorChannelStat), ctx, in)
}

// GetMultiAnchorDailyStatsList mocks base method.
func (m *MockSignAnchorStatsServer) GetMultiAnchorDailyStatsList(ctx context.Context, in *GetMultiAnchorDailyStatsListReq) (*GetMultiAnchorDailyStatsListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiAnchorDailyStatsList", ctx, in)
	ret0, _ := ret[0].(*GetMultiAnchorDailyStatsListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiAnchorDailyStatsList indicates an expected call of GetMultiAnchorDailyStatsList.
func (mr *MockSignAnchorStatsServerMockRecorder) GetMultiAnchorDailyStatsList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorDailyStatsList", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetMultiAnchorDailyStatsList), ctx, in)
}

// GetMultiAnchorDailyStatsListByGuildId mocks base method.
func (m *MockSignAnchorStatsServer) GetMultiAnchorDailyStatsListByGuildId(ctx context.Context, in *GetMultiAnchorDailyStatsListByGuildIdReq) (*GetMultiAnchorDailyStatsListByGuildIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiAnchorDailyStatsListByGuildId", ctx, in)
	ret0, _ := ret[0].(*GetMultiAnchorDailyStatsListByGuildIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiAnchorDailyStatsListByGuildId indicates an expected call of GetMultiAnchorDailyStatsListByGuildId.
func (mr *MockSignAnchorStatsServerMockRecorder) GetMultiAnchorDailyStatsListByGuildId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorDailyStatsListByGuildId", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetMultiAnchorDailyStatsListByGuildId), ctx, in)
}

// GetMultiAnchorMonthStat mocks base method.
func (m *MockSignAnchorStatsServer) GetMultiAnchorMonthStat(ctx context.Context, in *GetMultiAnchorMonthStatReq) (*GetMultiAnchorMonthStatResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiAnchorMonthStat", ctx, in)
	ret0, _ := ret[0].(*GetMultiAnchorMonthStatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiAnchorMonthStat indicates an expected call of GetMultiAnchorMonthStat.
func (mr *MockSignAnchorStatsServerMockRecorder) GetMultiAnchorMonthStat(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiAnchorMonthStat", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetMultiAnchorMonthStat), ctx, in)
}

// GetMultiPlayerBaseInfo mocks base method.
func (m *MockSignAnchorStatsServer) GetMultiPlayerBaseInfo(ctx context.Context, in *GetMultiPlayerBaseInfoReq) (*GetMultiPlayerBaseInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerBaseInfo", ctx, in)
	ret0, _ := ret[0].(*GetMultiPlayerBaseInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiPlayerBaseInfo indicates an expected call of GetMultiPlayerBaseInfo.
func (mr *MockSignAnchorStatsServerMockRecorder) GetMultiPlayerBaseInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerBaseInfo", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetMultiPlayerBaseInfo), ctx, in)
}

// GetMultiPlayerHomepage mocks base method.
func (m *MockSignAnchorStatsServer) GetMultiPlayerHomepage(ctx context.Context, in *GetMultiPlayerHomepageReq) (*GetMultiPlayerHomepageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerHomepage", ctx, in)
	ret0, _ := ret[0].(*GetMultiPlayerHomepageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiPlayerHomepage indicates an expected call of GetMultiPlayerHomepage.
func (mr *MockSignAnchorStatsServerMockRecorder) GetMultiPlayerHomepage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerHomepage", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetMultiPlayerHomepage), ctx, in)
}

// GetMultiPlayerMonthCommunityInfo mocks base method.
func (m *MockSignAnchorStatsServer) GetMultiPlayerMonthCommunityInfo(ctx context.Context, in *GetMultiPlayerMonthCommunityInfoReq) (*GetMultiPlayerMonthCommunityInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerMonthCommunityInfo", ctx, in)
	ret0, _ := ret[0].(*GetMultiPlayerMonthCommunityInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiPlayerMonthCommunityInfo indicates an expected call of GetMultiPlayerMonthCommunityInfo.
func (mr *MockSignAnchorStatsServerMockRecorder) GetMultiPlayerMonthCommunityInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerMonthCommunityInfo", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetMultiPlayerMonthCommunityInfo), ctx, in)
}

// GetMultiPlayerMonthConsumeTop10 mocks base method.
func (m *MockSignAnchorStatsServer) GetMultiPlayerMonthConsumeTop10(ctx context.Context, in *GetMultiPlayerMonthConsumeTop10Req) (*GetMultiPlayerMonthConsumeTop10Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerMonthConsumeTop10", ctx, in)
	ret0, _ := ret[0].(*GetMultiPlayerMonthConsumeTop10Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiPlayerMonthConsumeTop10 indicates an expected call of GetMultiPlayerMonthConsumeTop10.
func (mr *MockSignAnchorStatsServerMockRecorder) GetMultiPlayerMonthConsumeTop10(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerMonthConsumeTop10", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetMultiPlayerMonthConsumeTop10), ctx, in)
}

// GetMultiThisMonthChannelStat mocks base method.
func (m *MockSignAnchorStatsServer) GetMultiThisMonthChannelStat(ctx context.Context, in *GetMultiThisMonthChannelStatReq) (*GetMultiThisMonthChannelStatResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiThisMonthChannelStat", ctx, in)
	ret0, _ := ret[0].(*GetMultiThisMonthChannelStatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiThisMonthChannelStat indicates an expected call of GetMultiThisMonthChannelStat.
func (mr *MockSignAnchorStatsServerMockRecorder) GetMultiThisMonthChannelStat(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiThisMonthChannelStat", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetMultiThisMonthChannelStat), ctx, in)
}

// GetPgcDailyInfoList mocks base method.
func (m *MockSignAnchorStatsServer) GetPgcDailyInfoList(ctx context.Context, in *GetPgcDailyInfoListReq) (*GetPgcDailyInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPgcDailyInfoList", ctx, in)
	ret0, _ := ret[0].(*GetPgcDailyInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcDailyInfoList indicates an expected call of GetPgcDailyInfoList.
func (mr *MockSignAnchorStatsServerMockRecorder) GetPgcDailyInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcDailyInfoList", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetPgcDailyInfoList), ctx, in)
}

// GetPgcMonthlyInfoList mocks base method.
func (m *MockSignAnchorStatsServer) GetPgcMonthlyInfoList(ctx context.Context, in *GetPgcMonthlyInfoListReq) (*GetPgcMonthlyInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPgcMonthlyInfoList", ctx, in)
	ret0, _ := ret[0].(*GetPgcMonthlyInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPgcMonthlyInfoList indicates an expected call of GetPgcMonthlyInfoList.
func (mr *MockSignAnchorStatsServerMockRecorder) GetPgcMonthlyInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPgcMonthlyInfoList", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetPgcMonthlyInfoList), ctx, in)
}

// GetPresentOrderCount mocks base method.
func (m *MockSignAnchorStatsServer) GetPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentOrderCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentOrderCount indicates an expected call of GetPresentOrderCount.
func (mr *MockSignAnchorStatsServerMockRecorder) GetPresentOrderCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentOrderCount", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetPresentOrderCount), ctx, in)
}

// GetPresentOrderList mocks base method.
func (m *MockSignAnchorStatsServer) GetPresentOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentOrderList", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentOrderList indicates an expected call of GetPresentOrderList.
func (mr *MockSignAnchorStatsServerMockRecorder) GetPresentOrderList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentOrderList", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetPresentOrderList), ctx, in)
}

// GetTicketOrderCount mocks base method.
func (m *MockSignAnchorStatsServer) GetTicketOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTicketOrderCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketOrderCount indicates an expected call of GetTicketOrderCount.
func (mr *MockSignAnchorStatsServerMockRecorder) GetTicketOrderCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketOrderCount", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetTicketOrderCount), ctx, in)
}

// GetTicketOrderList mocks base method.
func (m *MockSignAnchorStatsServer) GetTicketOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTicketOrderList", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketOrderList indicates an expected call of GetTicketOrderList.
func (mr *MockSignAnchorStatsServerMockRecorder) GetTicketOrderList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketOrderList", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetTicketOrderList), ctx, in)
}

// GetUserInteractInfo mocks base method.
func (m *MockSignAnchorStatsServer) GetUserInteractInfo(ctx context.Context, in *GetUserInteractInfoReq) (*GetUserInteractInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInteractInfo", ctx, in)
	ret0, _ := ret[0].(*GetUserInteractInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInteractInfo indicates an expected call of GetUserInteractInfo.
func (mr *MockSignAnchorStatsServerMockRecorder) GetUserInteractInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInteractInfo", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetUserInteractInfo), ctx, in)
}

// GetUserInteractViewPer mocks base method.
func (m *MockSignAnchorStatsServer) GetUserInteractViewPer(ctx context.Context, in *GetUserInteractViewPerReq) (*GetUserInteractViewPerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInteractViewPer", ctx, in)
	ret0, _ := ret[0].(*GetUserInteractViewPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInteractViewPer indicates an expected call of GetUserInteractViewPer.
func (mr *MockSignAnchorStatsServerMockRecorder) GetUserInteractViewPer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInteractViewPer", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetUserInteractViewPer), ctx, in)
}

// GetUserTbeanConsume mocks base method.
func (m *MockSignAnchorStatsServer) GetUserTbeanConsume(ctx context.Context, in *GetUserTbeanConsumeReq) (*GetUserTbeanConsumeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTbeanConsume", ctx, in)
	ret0, _ := ret[0].(*GetUserTbeanConsumeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTbeanConsume indicates an expected call of GetUserTbeanConsume.
func (mr *MockSignAnchorStatsServerMockRecorder) GetUserTbeanConsume(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTbeanConsume", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetUserTbeanConsume), ctx, in)
}

// GetValidHoldDayUid mocks base method.
func (m *MockSignAnchorStatsServer) GetValidHoldDayUid(ctx context.Context, in *GetValidHoldDayUidReq) (*GetValidHoldDayUidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetValidHoldDayUid", ctx, in)
	ret0, _ := ret[0].(*GetValidHoldDayUidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetValidHoldDayUid indicates an expected call of GetValidHoldDayUid.
func (mr *MockSignAnchorStatsServerMockRecorder) GetValidHoldDayUid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidHoldDayUid", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).GetValidHoldDayUid), ctx, in)
}

// ListMultiPlayerHall mocks base method.
func (m *MockSignAnchorStatsServer) ListMultiPlayerHall(ctx context.Context, in *ListMultiPlayerHallReq) (*ListMultiPlayerHallResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMultiPlayerHall", ctx, in)
	ret0, _ := ret[0].(*ListMultiPlayerHallResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMultiPlayerHall indicates an expected call of ListMultiPlayerHall.
func (mr *MockSignAnchorStatsServerMockRecorder) ListMultiPlayerHall(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMultiPlayerHall", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).ListMultiPlayerHall), ctx, in)
}

// ReplaceOrder mocks base method.
func (m *MockSignAnchorStatsServer) ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReplaceOrder", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReplaceOrder indicates an expected call of ReplaceOrder.
func (mr *MockSignAnchorStatsServerMockRecorder) ReplaceOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplaceOrder", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).ReplaceOrder), ctx, in)
}

// SetUserInteractViewPer mocks base method.
func (m *MockSignAnchorStatsServer) SetUserInteractViewPer(ctx context.Context, in *SetUserInteractViewPerReq) (*SetUserInteractViewPerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserInteractViewPer", ctx, in)
	ret0, _ := ret[0].(*SetUserInteractViewPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserInteractViewPer indicates an expected call of SetUserInteractViewPer.
func (mr *MockSignAnchorStatsServerMockRecorder) SetUserInteractViewPer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserInteractViewPer", reflect.TypeOf((*MockSignAnchorStatsServer)(nil).SetUserInteractViewPer), ctx, in)
}
