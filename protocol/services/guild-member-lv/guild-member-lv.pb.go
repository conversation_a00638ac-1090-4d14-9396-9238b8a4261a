// Code generated by protoc-gen-go. DO NOT EDIT.
// source: guild/guild-member-lv.proto

package guild_member_lv // import "golang.52tt.com/protocol/services/guild-member-lv"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 个人贡献信息
type StMemberContributionInfo struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	TotalContribution    uint32   `protobuf:"varint,3,opt,name=total_contribution,json=totalContribution,proto3" json:"total_contribution,omitempty"`
	ValidContribution    uint32   `protobuf:"varint,4,opt,name=valid_contribution,json=validContribution,proto3" json:"valid_contribution,omitempty"`
	MemberLv             uint32   `protobuf:"varint,5,opt,name=member_lv,json=memberLv,proto3" json:"member_lv,omitempty"`
	MaxMemberLv          uint32   `protobuf:"varint,6,opt,name=max_member_lv,json=maxMemberLv,proto3" json:"max_member_lv,omitempty"`
	CurLvContribution    uint32   `protobuf:"varint,7,opt,name=cur_lv_contribution,json=curLvContribution,proto3" json:"cur_lv_contribution,omitempty"`
	MaxLvContribution    uint32   `protobuf:"varint,8,opt,name=max_lv_contribution,json=maxLvContribution,proto3" json:"max_lv_contribution,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StMemberContributionInfo) Reset()         { *m = StMemberContributionInfo{} }
func (m *StMemberContributionInfo) String() string { return proto.CompactTextString(m) }
func (*StMemberContributionInfo) ProtoMessage()    {}
func (*StMemberContributionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_member_lv_ebe9842103173510, []int{0}
}
func (m *StMemberContributionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StMemberContributionInfo.Unmarshal(m, b)
}
func (m *StMemberContributionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StMemberContributionInfo.Marshal(b, m, deterministic)
}
func (dst *StMemberContributionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StMemberContributionInfo.Merge(dst, src)
}
func (m *StMemberContributionInfo) XXX_Size() int {
	return xxx_messageInfo_StMemberContributionInfo.Size(m)
}
func (m *StMemberContributionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StMemberContributionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StMemberContributionInfo proto.InternalMessageInfo

func (m *StMemberContributionInfo) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StMemberContributionInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StMemberContributionInfo) GetTotalContribution() uint32 {
	if m != nil {
		return m.TotalContribution
	}
	return 0
}

func (m *StMemberContributionInfo) GetValidContribution() uint32 {
	if m != nil {
		return m.ValidContribution
	}
	return 0
}

func (m *StMemberContributionInfo) GetMemberLv() uint32 {
	if m != nil {
		return m.MemberLv
	}
	return 0
}

func (m *StMemberContributionInfo) GetMaxMemberLv() uint32 {
	if m != nil {
		return m.MaxMemberLv
	}
	return 0
}

func (m *StMemberContributionInfo) GetCurLvContribution() uint32 {
	if m != nil {
		return m.CurLvContribution
	}
	return 0
}

func (m *StMemberContributionInfo) GetMaxLvContribution() uint32 {
	if m != nil {
		return m.MaxLvContribution
	}
	return 0
}

// 获取个人贡献
type GetMemberContributionReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMemberContributionReq) Reset()         { *m = GetMemberContributionReq{} }
func (m *GetMemberContributionReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberContributionReq) ProtoMessage()    {}
func (*GetMemberContributionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_member_lv_ebe9842103173510, []int{1}
}
func (m *GetMemberContributionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberContributionReq.Unmarshal(m, b)
}
func (m *GetMemberContributionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberContributionReq.Marshal(b, m, deterministic)
}
func (dst *GetMemberContributionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberContributionReq.Merge(dst, src)
}
func (m *GetMemberContributionReq) XXX_Size() int {
	return xxx_messageInfo_GetMemberContributionReq.Size(m)
}
func (m *GetMemberContributionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberContributionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberContributionReq proto.InternalMessageInfo

func (m *GetMemberContributionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMemberContributionReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMemberContributionResp struct {
	Contribution         *StMemberContributionInfo `protobuf:"bytes,1,opt,name=contribution,proto3" json:"contribution,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetMemberContributionResp) Reset()         { *m = GetMemberContributionResp{} }
func (m *GetMemberContributionResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberContributionResp) ProtoMessage()    {}
func (*GetMemberContributionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_member_lv_ebe9842103173510, []int{2}
}
func (m *GetMemberContributionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberContributionResp.Unmarshal(m, b)
}
func (m *GetMemberContributionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberContributionResp.Marshal(b, m, deterministic)
}
func (dst *GetMemberContributionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberContributionResp.Merge(dst, src)
}
func (m *GetMemberContributionResp) XXX_Size() int {
	return xxx_messageInfo_GetMemberContributionResp.Size(m)
}
func (m *GetMemberContributionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberContributionResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberContributionResp proto.InternalMessageInfo

func (m *GetMemberContributionResp) GetContribution() *StMemberContributionInfo {
	if m != nil {
		return m.Contribution
	}
	return nil
}

func init() {
	proto.RegisterType((*StMemberContributionInfo)(nil), "guild_member_lv.StMemberContributionInfo")
	proto.RegisterType((*GetMemberContributionReq)(nil), "guild_member_lv.GetMemberContributionReq")
	proto.RegisterType((*GetMemberContributionResp)(nil), "guild_member_lv.GetMemberContributionResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GuildMemberLvClient is the client API for GuildMemberLv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GuildMemberLvClient interface {
	GetMemberContribution(ctx context.Context, in *GetMemberContributionReq, opts ...grpc.CallOption) (*GetMemberContributionResp, error)
}

type guildMemberLvClient struct {
	cc *grpc.ClientConn
}

func NewGuildMemberLvClient(cc *grpc.ClientConn) GuildMemberLvClient {
	return &guildMemberLvClient{cc}
}

func (c *guildMemberLvClient) GetMemberContribution(ctx context.Context, in *GetMemberContributionReq, opts ...grpc.CallOption) (*GetMemberContributionResp, error) {
	out := new(GetMemberContributionResp)
	err := c.cc.Invoke(ctx, "/guild_member_lv.GuildMemberLv/GetMemberContribution", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GuildMemberLvServer is the server API for GuildMemberLv service.
type GuildMemberLvServer interface {
	GetMemberContribution(context.Context, *GetMemberContributionReq) (*GetMemberContributionResp, error)
}

func RegisterGuildMemberLvServer(s *grpc.Server, srv GuildMemberLvServer) {
	s.RegisterService(&_GuildMemberLv_serviceDesc, srv)
}

func _GuildMemberLv_GetMemberContribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberContributionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildMemberLvServer).GetMemberContribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_member_lv.GuildMemberLv/GetMemberContribution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildMemberLvServer).GetMemberContribution(ctx, req.(*GetMemberContributionReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GuildMemberLv_serviceDesc = grpc.ServiceDesc{
	ServiceName: "guild_member_lv.GuildMemberLv",
	HandlerType: (*GuildMemberLvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMemberContribution",
			Handler:    _GuildMemberLv_GetMemberContribution_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "guild/guild-member-lv.proto",
}

func init() {
	proto.RegisterFile("guild/guild-member-lv.proto", fileDescriptor_guild_member_lv_ebe9842103173510)
}

var fileDescriptor_guild_member_lv_ebe9842103173510 = []byte{
	// 343 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x92, 0x41, 0x4f, 0xc2, 0x30,
	0x14, 0xc7, 0x1d, 0x20, 0xe0, 0x43, 0xa2, 0x96, 0x98, 0x14, 0xb9, 0x90, 0x9d, 0xd0, 0x84, 0x12,
	0x21, 0x7e, 0x01, 0x3d, 0x10, 0x12, 0xb8, 0xcc, 0x9b, 0x17, 0x32, 0xb6, 0x49, 0x6a, 0xda, 0x75,
	0x6e, 0x5d, 0xc3, 0xd5, 0x2f, 0xe7, 0xe7, 0x32, 0x6d, 0x15, 0x29, 0x42, 0xe2, 0x65, 0x59, 0xdf,
	0xfb, 0xf5, 0xff, 0x6f, 0xff, 0x7d, 0xd0, 0x5b, 0x97, 0x94, 0xc5, 0x23, 0xf3, 0x1d, 0xf2, 0x84,
	0xaf, 0x92, 0x7c, 0xc8, 0x14, 0xc9, 0x72, 0x21, 0x05, 0xba, 0x30, 0xe5, 0xa5, 0x2d, 0x2f, 0x99,
	0xf2, 0x3f, 0x2b, 0x80, 0x9f, 0xe5, 0xc2, 0xac, 0x9f, 0x44, 0x2a, 0x73, 0xba, 0x2a, 0x25, 0x15,
	0xe9, 0x2c, 0x7d, 0x15, 0xe8, 0x12, 0xaa, 0x25, 0x8d, 0xb1, 0xd7, 0xf7, 0x06, 0xb5, 0x40, 0xff,
	0xa2, 0x2e, 0x34, 0xad, 0x02, 0x8d, 0x71, 0xa5, 0xef, 0x0d, 0xda, 0x41, 0xc3, 0xac, 0x67, 0x31,
	0x1a, 0x02, 0x92, 0x42, 0x86, 0x6c, 0x19, 0xed, 0xc8, 0xe0, 0xaa, 0x81, 0xae, 0x4c, 0x67, 0x57,
	0x5f, 0xe3, 0x2a, 0x64, 0x34, 0x76, 0xf1, 0x9a, 0xc5, 0x4d, 0xc7, 0xc1, 0x7b, 0x70, 0xb6, 0x3d,
	0x34, 0x3e, 0x35, 0x54, 0xd3, 0x16, 0xe6, 0x0a, 0xf9, 0xd0, 0xe6, 0xe1, 0xe6, 0xf7, 0x56, 0xb8,
	0x6e, 0x80, 0x16, 0x0f, 0x37, 0x8b, 0x1f, 0x86, 0x40, 0x27, 0x2a, 0x75, 0xd3, 0x35, 0x6c, 0x58,
	0xc3, 0xa8, 0xcc, 0xe7, 0xca, 0x31, 0x24, 0xd0, 0xd1, 0x9a, 0xfb, 0x7c, 0xd3, 0xf2, 0x3c, 0xdc,
	0xb8, 0xbc, 0x3f, 0x05, 0x3c, 0x4d, 0x0e, 0x04, 0x19, 0x24, 0xef, 0x4e, 0x6a, 0x9e, 0x9b, 0xda,
	0x77, 0xc4, 0x95, 0x6d, 0xc4, 0xfe, 0x1b, 0x74, 0x8f, 0x08, 0x15, 0x19, 0x5a, 0xc0, 0xb9, 0x73,
	0x1c, 0xad, 0xd6, 0x1a, 0xdf, 0x92, 0xbd, 0x67, 0x25, 0xc7, 0x9e, 0x34, 0x70, 0xb6, 0x8f, 0x3f,
	0x3c, 0x68, 0x4f, 0xf5, 0xd6, 0x6d, 0x4c, 0x19, 0x5c, 0x1f, 0x74, 0x47, 0x7f, 0x3d, 0x8e, 0x5d,
	0xf7, 0xe6, 0xee, 0xbf, 0x68, 0x91, 0xf9, 0x27, 0x8f, 0x93, 0x97, 0xfb, 0xb5, 0x60, 0x61, 0xba,
	0x26, 0x0f, 0x63, 0x29, 0x49, 0x24, 0xf8, 0xc8, 0xcc, 0x6a, 0x24, 0xd8, 0xa8, 0x48, 0x72, 0x45,
	0xa3, 0xa4, 0xd8, 0x9f, 0xe6, 0x55, 0xdd, 0x20, 0x93, 0xaf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x30,
	0x1f, 0xff, 0x84, 0xed, 0x02, 0x00, 0x00,
}
