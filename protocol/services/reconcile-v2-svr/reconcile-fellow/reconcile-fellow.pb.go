// Code generated by protoc-gen-go. DO NOT EDIT.
// source: reconcile-v2-svr/reconcile-fellow.proto

package reconcile_fellow // import "golang.52tt.com/protocol/services/reconcile-v2-svr/reconcile-fellow"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ReconcileFellowClient is the client API for ReconcileFellow service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ReconcileFellowClient interface {
	// 获取时间范围内的积分数量和金额
	GetFellowInviteOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 获取时间范围内的订单列表
	GetFellowInviteOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	FixFellowOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
}

type reconcileFellowClient struct {
	cc *grpc.ClientConn
}

func NewReconcileFellowClient(cc *grpc.ClientConn) ReconcileFellowClient {
	return &reconcileFellowClient{cc}
}

func (c *reconcileFellowClient) GetFellowInviteOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/ReconcileFellow.ReconcileFellow/GetFellowInviteOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reconcileFellowClient) GetFellowInviteOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/ReconcileFellow.ReconcileFellow/GetFellowInviteOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reconcileFellowClient) FixFellowOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/ReconcileFellow.ReconcileFellow/FixFellowOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReconcileFellowServer is the server API for ReconcileFellow service.
type ReconcileFellowServer interface {
	// 获取时间范围内的积分数量和金额
	GetFellowInviteOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 获取时间范围内的订单列表
	GetFellowInviteOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	FixFellowOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
}

func RegisterReconcileFellowServer(s *grpc.Server, srv ReconcileFellowServer) {
	s.RegisterService(&_ReconcileFellow_serviceDesc, srv)
}

func _ReconcileFellow_GetFellowInviteOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReconcileFellowServer).GetFellowInviteOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ReconcileFellow.ReconcileFellow/GetFellowInviteOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReconcileFellowServer).GetFellowInviteOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReconcileFellow_GetFellowInviteOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReconcileFellowServer).GetFellowInviteOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ReconcileFellow.ReconcileFellow/GetFellowInviteOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReconcileFellowServer).GetFellowInviteOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReconcileFellow_FixFellowOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReconcileFellowServer).FixFellowOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ReconcileFellow.ReconcileFellow/FixFellowOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReconcileFellowServer).FixFellowOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ReconcileFellow_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ReconcileFellow.ReconcileFellow",
	HandlerType: (*ReconcileFellowServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFellowInviteOrderCount",
			Handler:    _ReconcileFellow_GetFellowInviteOrderCount_Handler,
		},
		{
			MethodName: "GetFellowInviteOrderList",
			Handler:    _ReconcileFellow_GetFellowInviteOrderList_Handler,
		},
		{
			MethodName: "FixFellowOrder",
			Handler:    _ReconcileFellow_FixFellowOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "reconcile-v2-svr/reconcile-fellow.proto",
}

func init() {
	proto.RegisterFile("reconcile-v2-svr/reconcile-fellow.proto", fileDescriptor_reconcile_fellow_3587890141c59735)
}

var fileDescriptor_reconcile_fellow_3587890141c59735 = []byte{
	// 232 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x2f, 0x4a, 0x4d, 0xce,
	0xcf, 0x4b, 0xce, 0xcc, 0x49, 0xd5, 0x2d, 0x33, 0xd2, 0x2d, 0x2e, 0x2b, 0xd2, 0x47, 0x08, 0xa4,
	0xa5, 0xe6, 0xe4, 0xe4, 0x97, 0xeb, 0x15, 0x14, 0xe5, 0x97, 0xe4, 0x0b, 0xf1, 0x07, 0xc1, 0xc4,
	0xdd, 0xc0, 0xc2, 0x52, 0xf2, 0xc8, 0x3a, 0xf5, 0x91, 0x39, 0x10, 0x1d, 0x46, 0x8d, 0x4c, 0x5c,
	0xe8, 0x9a, 0x84, 0x02, 0xb8, 0x24, 0xdd, 0x53, 0x4b, 0x20, 0x1c, 0xcf, 0xbc, 0xb2, 0xcc, 0x92,
	0x54, 0xff, 0xa2, 0x94, 0xd4, 0x22, 0xe7, 0xfc, 0xd2, 0xbc, 0x12, 0x21, 0x49, 0x3d, 0xb8, 0xf2,
	0x30, 0x23, 0xbd, 0x90, 0xcc, 0xdc, 0xd4, 0xa0, 0xc4, 0xbc, 0xf4, 0xd4, 0xa0, 0xd4, 0x42, 0x29,
	0x31, 0x14, 0x29, 0xb0, 0xf2, 0xa0, 0xd4, 0xe2, 0x02, 0x25, 0x06, 0xa1, 0x20, 0x2e, 0x09, 0x6c,
	0x26, 0xfa, 0x64, 0x16, 0xe3, 0x35, 0x10, 0x55, 0x0a, 0xac, 0xc5, 0x33, 0xa5, 0x18, 0x6a, 0xa6,
	0x07, 0x17, 0x9f, 0x5b, 0x66, 0x05, 0xc4, 0x4c, 0xb0, 0x94, 0x90, 0x0c, 0x8a, 0xf2, 0xa0, 0xd4,
	0x82, 0x9c, 0xc4, 0x64, 0x88, 0x45, 0x98, 0xae, 0x73, 0xcd, 0x2d, 0x28, 0xa9, 0x84, 0x98, 0xe4,
	0xe4, 0x1a, 0xe5, 0x9c, 0x9e, 0x9f, 0x93, 0x98, 0x97, 0xae, 0x67, 0x6a, 0x54, 0x52, 0xa2, 0x97,
	0x9c, 0x9f, 0xab, 0x0f, 0x0e, 0x9c, 0xe4, 0xfc, 0x1c, 0xfd, 0xe2, 0xd4, 0xa2, 0xb2, 0xcc, 0xe4,
	0xd4, 0x62, 0x7d, 0x82, 0x51, 0x90, 0xc4, 0x06, 0xd6, 0x64, 0x0c, 0x08, 0x00, 0x00, 0xff, 0xff,
	0x1a, 0x47, 0x14, 0x20, 0xae, 0x01, 0x00, 0x00,
}
