// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-convene.proto

package channel_convene

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 异步批量操作类型, 表字段collect_stat_action->type
type CollectStatInfoType int32

const (
	CollectStatInfoType_CollectStatInfoType_Unknow                 CollectStatInfoType = 0
	CollectStatInfoType_CollectStatInfoType_BatchSetCollectionInfo CollectStatInfoType = 1
)

var CollectStatInfoType_name = map[int32]string{
	0: "CollectStatInfoType_Unknow",
	1: "CollectStatInfoType_BatchSetCollectionInfo",
}

var CollectStatInfoType_value = map[string]int32{
	"CollectStatInfoType_Unknow":                 0,
	"CollectStatInfoType_BatchSetCollectionInfo": 1,
}

func (x CollectStatInfoType) String() string {
	return proto.EnumName(CollectStatInfoType_name, int32(x))
}

func (CollectStatInfoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{0}
}

type ChannelConveneEmptyResp struct {
	Ret                  int32    `protobuf:"varint,1,opt,name=ret,proto3" json:"ret,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelConveneEmptyResp) Reset()         { *m = ChannelConveneEmptyResp{} }
func (m *ChannelConveneEmptyResp) String() string { return proto.CompactTextString(m) }
func (*ChannelConveneEmptyResp) ProtoMessage()    {}
func (*ChannelConveneEmptyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{0}
}

func (m *ChannelConveneEmptyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelConveneEmptyResp.Unmarshal(m, b)
}
func (m *ChannelConveneEmptyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelConveneEmptyResp.Marshal(b, m, deterministic)
}
func (m *ChannelConveneEmptyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelConveneEmptyResp.Merge(m, src)
}
func (m *ChannelConveneEmptyResp) XXX_Size() int {
	return xxx_messageInfo_ChannelConveneEmptyResp.Size(m)
}
func (m *ChannelConveneEmptyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelConveneEmptyResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelConveneEmptyResp proto.InternalMessageInfo

func (m *ChannelConveneEmptyResp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

type ChannelConveneObjUint32 struct {
	Val                  uint32   `protobuf:"varint,1,opt,name=val,proto3" json:"val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelConveneObjUint32) Reset()         { *m = ChannelConveneObjUint32{} }
func (m *ChannelConveneObjUint32) String() string { return proto.CompactTextString(m) }
func (*ChannelConveneObjUint32) ProtoMessage()    {}
func (*ChannelConveneObjUint32) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{1}
}

func (m *ChannelConveneObjUint32) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelConveneObjUint32.Unmarshal(m, b)
}
func (m *ChannelConveneObjUint32) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelConveneObjUint32.Marshal(b, m, deterministic)
}
func (m *ChannelConveneObjUint32) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelConveneObjUint32.Merge(m, src)
}
func (m *ChannelConveneObjUint32) XXX_Size() int {
	return xxx_messageInfo_ChannelConveneObjUint32.Size(m)
}
func (m *ChannelConveneObjUint32) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelConveneObjUint32.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelConveneObjUint32 proto.InternalMessageInfo

func (m *ChannelConveneObjUint32) GetVal() uint32 {
	if m != nil {
		return m.Val
	}
	return 0
}

// 收藏频道
type CollectChannelReq struct {
	ChannelId            uint32                   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32                   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	CollectSource        *ChannelConveneObjUint32 `protobuf:"bytes,3,opt,name=collect_source,json=collectSource,proto3" json:"collect_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *CollectChannelReq) Reset()         { *m = CollectChannelReq{} }
func (m *CollectChannelReq) String() string { return proto.CompactTextString(m) }
func (*CollectChannelReq) ProtoMessage()    {}
func (*CollectChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{2}
}

func (m *CollectChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CollectChannelReq.Unmarshal(m, b)
}
func (m *CollectChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CollectChannelReq.Marshal(b, m, deterministic)
}
func (m *CollectChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CollectChannelReq.Merge(m, src)
}
func (m *CollectChannelReq) XXX_Size() int {
	return xxx_messageInfo_CollectChannelReq.Size(m)
}
func (m *CollectChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CollectChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_CollectChannelReq proto.InternalMessageInfo

func (m *CollectChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CollectChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CollectChannelReq) GetCollectSource() *ChannelConveneObjUint32 {
	if m != nil {
		return m.CollectSource
	}
	return nil
}

// 取消收藏频道
type RemoveChannelCollectionReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveChannelCollectionReq) Reset()         { *m = RemoveChannelCollectionReq{} }
func (m *RemoveChannelCollectionReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelCollectionReq) ProtoMessage()    {}
func (*RemoveChannelCollectionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{3}
}

func (m *RemoveChannelCollectionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveChannelCollectionReq.Unmarshal(m, b)
}
func (m *RemoveChannelCollectionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveChannelCollectionReq.Marshal(b, m, deterministic)
}
func (m *RemoveChannelCollectionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveChannelCollectionReq.Merge(m, src)
}
func (m *RemoveChannelCollectionReq) XXX_Size() int {
	return xxx_messageInfo_RemoveChannelCollectionReq.Size(m)
}
func (m *RemoveChannelCollectionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveChannelCollectionReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveChannelCollectionReq proto.InternalMessageInfo

func (m *RemoveChannelCollectionReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *RemoveChannelCollectionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 是否已收藏频道
type HasCollectChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HasCollectChannelReq) Reset()         { *m = HasCollectChannelReq{} }
func (m *HasCollectChannelReq) String() string { return proto.CompactTextString(m) }
func (*HasCollectChannelReq) ProtoMessage()    {}
func (*HasCollectChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{4}
}

func (m *HasCollectChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HasCollectChannelReq.Unmarshal(m, b)
}
func (m *HasCollectChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HasCollectChannelReq.Marshal(b, m, deterministic)
}
func (m *HasCollectChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HasCollectChannelReq.Merge(m, src)
}
func (m *HasCollectChannelReq) XXX_Size() int {
	return xxx_messageInfo_HasCollectChannelReq.Size(m)
}
func (m *HasCollectChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HasCollectChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_HasCollectChannelReq proto.InternalMessageInfo

func (m *HasCollectChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HasCollectChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type HasCollectChannelResp struct {
	IsCollected          bool     `protobuf:"varint,1,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HasCollectChannelResp) Reset()         { *m = HasCollectChannelResp{} }
func (m *HasCollectChannelResp) String() string { return proto.CompactTextString(m) }
func (*HasCollectChannelResp) ProtoMessage()    {}
func (*HasCollectChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{5}
}

func (m *HasCollectChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HasCollectChannelResp.Unmarshal(m, b)
}
func (m *HasCollectChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HasCollectChannelResp.Marshal(b, m, deterministic)
}
func (m *HasCollectChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HasCollectChannelResp.Merge(m, src)
}
func (m *HasCollectChannelResp) XXX_Size() int {
	return xxx_messageInfo_HasCollectChannelResp.Size(m)
}
func (m *HasCollectChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HasCollectChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_HasCollectChannelResp proto.InternalMessageInfo

func (m *HasCollectChannelResp) GetIsCollected() bool {
	if m != nil {
		return m.IsCollected
	}
	return false
}

// 从指定的频道列表中获取收藏的频道
type BatchHasCollectChannelReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchHasCollectChannelReq) Reset()         { *m = BatchHasCollectChannelReq{} }
func (m *BatchHasCollectChannelReq) String() string { return proto.CompactTextString(m) }
func (*BatchHasCollectChannelReq) ProtoMessage()    {}
func (*BatchHasCollectChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{6}
}

func (m *BatchHasCollectChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchHasCollectChannelReq.Unmarshal(m, b)
}
func (m *BatchHasCollectChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchHasCollectChannelReq.Marshal(b, m, deterministic)
}
func (m *BatchHasCollectChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchHasCollectChannelReq.Merge(m, src)
}
func (m *BatchHasCollectChannelReq) XXX_Size() int {
	return xxx_messageInfo_BatchHasCollectChannelReq.Size(m)
}
func (m *BatchHasCollectChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchHasCollectChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchHasCollectChannelReq proto.InternalMessageInfo

func (m *BatchHasCollectChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *BatchHasCollectChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type BatchHasCollectChannelResp struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchHasCollectChannelResp) Reset()         { *m = BatchHasCollectChannelResp{} }
func (m *BatchHasCollectChannelResp) String() string { return proto.CompactTextString(m) }
func (*BatchHasCollectChannelResp) ProtoMessage()    {}
func (*BatchHasCollectChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{7}
}

func (m *BatchHasCollectChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchHasCollectChannelResp.Unmarshal(m, b)
}
func (m *BatchHasCollectChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchHasCollectChannelResp.Marshal(b, m, deterministic)
}
func (m *BatchHasCollectChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchHasCollectChannelResp.Merge(m, src)
}
func (m *BatchHasCollectChannelResp) XXX_Size() int {
	return xxx_messageInfo_BatchHasCollectChannelResp.Size(m)
}
func (m *BatchHasCollectChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchHasCollectChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchHasCollectChannelResp proto.InternalMessageInfo

func (m *BatchHasCollectChannelResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

// 获取用户的收藏频道列表，redis缓存
type GetChannelCollectionListByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCollectionListByUidReq) Reset()         { *m = GetChannelCollectionListByUidReq{} }
func (m *GetChannelCollectionListByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionListByUidReq) ProtoMessage()    {}
func (*GetChannelCollectionListByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{8}
}

func (m *GetChannelCollectionListByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCollectionListByUidReq.Unmarshal(m, b)
}
func (m *GetChannelCollectionListByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCollectionListByUidReq.Marshal(b, m, deterministic)
}
func (m *GetChannelCollectionListByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCollectionListByUidReq.Merge(m, src)
}
func (m *GetChannelCollectionListByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelCollectionListByUidReq.Size(m)
}
func (m *GetChannelCollectionListByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCollectionListByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCollectionListByUidReq proto.InternalMessageInfo

func (m *GetChannelCollectionListByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetChannelCollectionListByUidResp struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCollectionListByUidResp) Reset()         { *m = GetChannelCollectionListByUidResp{} }
func (m *GetChannelCollectionListByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionListByUidResp) ProtoMessage()    {}
func (*GetChannelCollectionListByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{9}
}

func (m *GetChannelCollectionListByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCollectionListByUidResp.Unmarshal(m, b)
}
func (m *GetChannelCollectionListByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCollectionListByUidResp.Marshal(b, m, deterministic)
}
func (m *GetChannelCollectionListByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCollectionListByUidResp.Merge(m, src)
}
func (m *GetChannelCollectionListByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelCollectionListByUidResp.Size(m)
}
func (m *GetChannelCollectionListByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCollectionListByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCollectionListByUidResp proto.InternalMessageInfo

func (m *GetChannelCollectionListByUidResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

// 获取用户的收藏信息列表，mysql从库
type GetChannelCollectionInfoListByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCollectionInfoListByUidReq) Reset()         { *m = GetChannelCollectionInfoListByUidReq{} }
func (m *GetChannelCollectionInfoListByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionInfoListByUidReq) ProtoMessage()    {}
func (*GetChannelCollectionInfoListByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{10}
}

func (m *GetChannelCollectionInfoListByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCollectionInfoListByUidReq.Unmarshal(m, b)
}
func (m *GetChannelCollectionInfoListByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCollectionInfoListByUidReq.Marshal(b, m, deterministic)
}
func (m *GetChannelCollectionInfoListByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCollectionInfoListByUidReq.Merge(m, src)
}
func (m *GetChannelCollectionInfoListByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelCollectionInfoListByUidReq.Size(m)
}
func (m *GetChannelCollectionInfoListByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCollectionInfoListByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCollectionInfoListByUidReq proto.InternalMessageInfo

func (m *GetChannelCollectionInfoListByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UserCollectionInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CollectSource        uint32   `protobuf:"varint,2,opt,name=collect_source,json=collectSource,proto3" json:"collect_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserCollectionInfo) Reset()         { *m = UserCollectionInfo{} }
func (m *UserCollectionInfo) String() string { return proto.CompactTextString(m) }
func (*UserCollectionInfo) ProtoMessage()    {}
func (*UserCollectionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{11}
}

func (m *UserCollectionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCollectionInfo.Unmarshal(m, b)
}
func (m *UserCollectionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCollectionInfo.Marshal(b, m, deterministic)
}
func (m *UserCollectionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCollectionInfo.Merge(m, src)
}
func (m *UserCollectionInfo) XXX_Size() int {
	return xxx_messageInfo_UserCollectionInfo.Size(m)
}
func (m *UserCollectionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCollectionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserCollectionInfo proto.InternalMessageInfo

func (m *UserCollectionInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserCollectionInfo) GetCollectSource() uint32 {
	if m != nil {
		return m.CollectSource
	}
	return 0
}

type GetChannelCollectionInfoListByUidResp struct {
	CollectInfoList      []*UserCollectionInfo `protobuf:"bytes,1,rep,name=collect_info_list,json=collectInfoList,proto3" json:"collect_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetChannelCollectionInfoListByUidResp) Reset()         { *m = GetChannelCollectionInfoListByUidResp{} }
func (m *GetChannelCollectionInfoListByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionInfoListByUidResp) ProtoMessage()    {}
func (*GetChannelCollectionInfoListByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{12}
}

func (m *GetChannelCollectionInfoListByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCollectionInfoListByUidResp.Unmarshal(m, b)
}
func (m *GetChannelCollectionInfoListByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCollectionInfoListByUidResp.Marshal(b, m, deterministic)
}
func (m *GetChannelCollectionInfoListByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCollectionInfoListByUidResp.Merge(m, src)
}
func (m *GetChannelCollectionInfoListByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelCollectionInfoListByUidResp.Size(m)
}
func (m *GetChannelCollectionInfoListByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCollectionInfoListByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCollectionInfoListByUidResp proto.InternalMessageInfo

func (m *GetChannelCollectionInfoListByUidResp) GetCollectInfoList() []*UserCollectionInfo {
	if m != nil {
		return m.CollectInfoList
	}
	return nil
}

// 批量更新用户的收藏房间信息，仅更新
type BatchSetUserCollectionInfoReq struct {
	UidList              []uint32            `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	UserCollectInfo      *UserCollectionInfo `protobuf:"bytes,2,opt,name=user_collect_info,json=userCollectInfo,proto3" json:"user_collect_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchSetUserCollectionInfoReq) Reset()         { *m = BatchSetUserCollectionInfoReq{} }
func (m *BatchSetUserCollectionInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetUserCollectionInfoReq) ProtoMessage()    {}
func (*BatchSetUserCollectionInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{13}
}

func (m *BatchSetUserCollectionInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetUserCollectionInfoReq.Unmarshal(m, b)
}
func (m *BatchSetUserCollectionInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetUserCollectionInfoReq.Marshal(b, m, deterministic)
}
func (m *BatchSetUserCollectionInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetUserCollectionInfoReq.Merge(m, src)
}
func (m *BatchSetUserCollectionInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetUserCollectionInfoReq.Size(m)
}
func (m *BatchSetUserCollectionInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetUserCollectionInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetUserCollectionInfoReq proto.InternalMessageInfo

func (m *BatchSetUserCollectionInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchSetUserCollectionInfoReq) GetUserCollectInfo() *UserCollectionInfo {
	if m != nil {
		return m.UserCollectInfo
	}
	return nil
}

type StChannelCollectMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CollectTs            uint32   `protobuf:"varint,2,opt,name=collect_ts,json=collectTs,proto3" json:"collect_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StChannelCollectMember) Reset()         { *m = StChannelCollectMember{} }
func (m *StChannelCollectMember) String() string { return proto.CompactTextString(m) }
func (*StChannelCollectMember) ProtoMessage()    {}
func (*StChannelCollectMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{14}
}

func (m *StChannelCollectMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StChannelCollectMember.Unmarshal(m, b)
}
func (m *StChannelCollectMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StChannelCollectMember.Marshal(b, m, deterministic)
}
func (m *StChannelCollectMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StChannelCollectMember.Merge(m, src)
}
func (m *StChannelCollectMember) XXX_Size() int {
	return xxx_messageInfo_StChannelCollectMember.Size(m)
}
func (m *StChannelCollectMember) XXX_DiscardUnknown() {
	xxx_messageInfo_StChannelCollectMember.DiscardUnknown(m)
}

var xxx_messageInfo_StChannelCollectMember proto.InternalMessageInfo

func (m *StChannelCollectMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StChannelCollectMember) GetCollectTs() uint32 {
	if m != nil {
		return m.CollectTs
	}
	return 0
}

type StChannelConveneMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ConveneStatus        uint32   `protobuf:"varint,3,opt,name=convene_status,json=conveneStatus,proto3" json:"convene_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StChannelConveneMember) Reset()         { *m = StChannelConveneMember{} }
func (m *StChannelConveneMember) String() string { return proto.CompactTextString(m) }
func (*StChannelConveneMember) ProtoMessage()    {}
func (*StChannelConveneMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{15}
}

func (m *StChannelConveneMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StChannelConveneMember.Unmarshal(m, b)
}
func (m *StChannelConveneMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StChannelConveneMember.Marshal(b, m, deterministic)
}
func (m *StChannelConveneMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StChannelConveneMember.Merge(m, src)
}
func (m *StChannelConveneMember) XXX_Size() int {
	return xxx_messageInfo_StChannelConveneMember.Size(m)
}
func (m *StChannelConveneMember) XXX_DiscardUnknown() {
	xxx_messageInfo_StChannelConveneMember.DiscardUnknown(m)
}

var xxx_messageInfo_StChannelConveneMember proto.InternalMessageInfo

func (m *StChannelConveneMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StChannelConveneMember) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StChannelConveneMember) GetConveneStatus() uint32 {
	if m != nil {
		return m.ConveneStatus
	}
	return 0
}

type StChannelConveneInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LastConveneTs        uint32   `protobuf:"varint,2,opt,name=last_convene_ts,json=lastConveneTs,proto3" json:"last_convene_ts,omitempty"`
	LastCancelTs         uint32   `protobuf:"varint,3,opt,name=last_cancel_ts,json=lastCancelTs,proto3" json:"last_cancel_ts,omitempty"`
	ConfirmCount         uint32   `protobuf:"varint,4,opt,name=confirm_count,json=confirmCount,proto3" json:"confirm_count,omitempty"`
	ConfirmCountReal     uint32   `protobuf:"varint,5,opt,name=confirm_count_real,json=confirmCountReal,proto3" json:"confirm_count_real,omitempty"`
	ConveneMsg           []byte   `protobuf:"bytes,6,opt,name=convene_msg,json=conveneMsg,proto3" json:"convene_msg,omitempty"`
	ValidConveneTs       uint32   `protobuf:"varint,7,opt,name=valid_convene_ts,json=validConveneTs,proto3" json:"valid_convene_ts,omitempty"`
	ConveneDurationMin   uint32   `protobuf:"varint,8,opt,name=convene_duration_min,json=conveneDurationMin,proto3" json:"convene_duration_min,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StChannelConveneInfo) Reset()         { *m = StChannelConveneInfo{} }
func (m *StChannelConveneInfo) String() string { return proto.CompactTextString(m) }
func (*StChannelConveneInfo) ProtoMessage()    {}
func (*StChannelConveneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{16}
}

func (m *StChannelConveneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StChannelConveneInfo.Unmarshal(m, b)
}
func (m *StChannelConveneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StChannelConveneInfo.Marshal(b, m, deterministic)
}
func (m *StChannelConveneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StChannelConveneInfo.Merge(m, src)
}
func (m *StChannelConveneInfo) XXX_Size() int {
	return xxx_messageInfo_StChannelConveneInfo.Size(m)
}
func (m *StChannelConveneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StChannelConveneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StChannelConveneInfo proto.InternalMessageInfo

func (m *StChannelConveneInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StChannelConveneInfo) GetLastConveneTs() uint32 {
	if m != nil {
		return m.LastConveneTs
	}
	return 0
}

func (m *StChannelConveneInfo) GetLastCancelTs() uint32 {
	if m != nil {
		return m.LastCancelTs
	}
	return 0
}

func (m *StChannelConveneInfo) GetConfirmCount() uint32 {
	if m != nil {
		return m.ConfirmCount
	}
	return 0
}

func (m *StChannelConveneInfo) GetConfirmCountReal() uint32 {
	if m != nil {
		return m.ConfirmCountReal
	}
	return 0
}

func (m *StChannelConveneInfo) GetConveneMsg() []byte {
	if m != nil {
		return m.ConveneMsg
	}
	return nil
}

func (m *StChannelConveneInfo) GetValidConveneTs() uint32 {
	if m != nil {
		return m.ValidConveneTs
	}
	return 0
}

func (m *StChannelConveneInfo) GetConveneDurationMin() uint32 {
	if m != nil {
		return m.ConveneDurationMin
	}
	return 0
}

// 获取收藏频道的用户列表
type GetChannelCollectionMemberListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCollectionMemberListReq) Reset()         { *m = GetChannelCollectionMemberListReq{} }
func (m *GetChannelCollectionMemberListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionMemberListReq) ProtoMessage()    {}
func (*GetChannelCollectionMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{17}
}

func (m *GetChannelCollectionMemberListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCollectionMemberListReq.Unmarshal(m, b)
}
func (m *GetChannelCollectionMemberListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCollectionMemberListReq.Marshal(b, m, deterministic)
}
func (m *GetChannelCollectionMemberListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCollectionMemberListReq.Merge(m, src)
}
func (m *GetChannelCollectionMemberListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelCollectionMemberListReq.Size(m)
}
func (m *GetChannelCollectionMemberListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCollectionMemberListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCollectionMemberListReq proto.InternalMessageInfo

func (m *GetChannelCollectionMemberListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelCollectionMemberListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetChannelCollectionMemberListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetChannelCollectionMemberListResp struct {
	MemberList           []*StChannelCollectMember `protobuf:"bytes,1,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetChannelCollectionMemberListResp) Reset()         { *m = GetChannelCollectionMemberListResp{} }
func (m *GetChannelCollectionMemberListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionMemberListResp) ProtoMessage()    {}
func (*GetChannelCollectionMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{18}
}

func (m *GetChannelCollectionMemberListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCollectionMemberListResp.Unmarshal(m, b)
}
func (m *GetChannelCollectionMemberListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCollectionMemberListResp.Marshal(b, m, deterministic)
}
func (m *GetChannelCollectionMemberListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCollectionMemberListResp.Merge(m, src)
}
func (m *GetChannelCollectionMemberListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelCollectionMemberListResp.Size(m)
}
func (m *GetChannelCollectionMemberListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCollectionMemberListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCollectionMemberListResp proto.InternalMessageInfo

func (m *GetChannelCollectionMemberListResp) GetMemberList() []*StChannelCollectMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

// 获取收藏频道的用户数
type GetChannelCollectionMemberCountReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCollectionMemberCountReq) Reset()         { *m = GetChannelCollectionMemberCountReq{} }
func (m *GetChannelCollectionMemberCountReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionMemberCountReq) ProtoMessage()    {}
func (*GetChannelCollectionMemberCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{19}
}

func (m *GetChannelCollectionMemberCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCollectionMemberCountReq.Unmarshal(m, b)
}
func (m *GetChannelCollectionMemberCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCollectionMemberCountReq.Marshal(b, m, deterministic)
}
func (m *GetChannelCollectionMemberCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCollectionMemberCountReq.Merge(m, src)
}
func (m *GetChannelCollectionMemberCountReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelCollectionMemberCountReq.Size(m)
}
func (m *GetChannelCollectionMemberCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCollectionMemberCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCollectionMemberCountReq proto.InternalMessageInfo

func (m *GetChannelCollectionMemberCountReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelCollectionMemberCountResp struct {
	MemberCount          uint32   `protobuf:"varint,1,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelCollectionMemberCountResp) Reset()         { *m = GetChannelCollectionMemberCountResp{} }
func (m *GetChannelCollectionMemberCountResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCollectionMemberCountResp) ProtoMessage()    {}
func (*GetChannelCollectionMemberCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{20}
}

func (m *GetChannelCollectionMemberCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCollectionMemberCountResp.Unmarshal(m, b)
}
func (m *GetChannelCollectionMemberCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCollectionMemberCountResp.Marshal(b, m, deterministic)
}
func (m *GetChannelCollectionMemberCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCollectionMemberCountResp.Merge(m, src)
}
func (m *GetChannelCollectionMemberCountResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelCollectionMemberCountResp.Size(m)
}
func (m *GetChannelCollectionMemberCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCollectionMemberCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCollectionMemberCountResp proto.InternalMessageInfo

func (m *GetChannelCollectionMemberCountResp) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

// 获取频道的召集信息
type GetChannelConveneInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelConveneInfoReq) Reset()         { *m = GetChannelConveneInfoReq{} }
func (m *GetChannelConveneInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelConveneInfoReq) ProtoMessage()    {}
func (*GetChannelConveneInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{21}
}

func (m *GetChannelConveneInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelConveneInfoReq.Unmarshal(m, b)
}
func (m *GetChannelConveneInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelConveneInfoReq.Marshal(b, m, deterministic)
}
func (m *GetChannelConveneInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelConveneInfoReq.Merge(m, src)
}
func (m *GetChannelConveneInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelConveneInfoReq.Size(m)
}
func (m *GetChannelConveneInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelConveneInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelConveneInfoReq proto.InternalMessageInfo

func (m *GetChannelConveneInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelConveneInfoResp struct {
	ConveneInfo          *StChannelConveneInfo `protobuf:"bytes,1,opt,name=convene_info,json=conveneInfo,proto3" json:"convene_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetChannelConveneInfoResp) Reset()         { *m = GetChannelConveneInfoResp{} }
func (m *GetChannelConveneInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelConveneInfoResp) ProtoMessage()    {}
func (*GetChannelConveneInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{22}
}

func (m *GetChannelConveneInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelConveneInfoResp.Unmarshal(m, b)
}
func (m *GetChannelConveneInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelConveneInfoResp.Marshal(b, m, deterministic)
}
func (m *GetChannelConveneInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelConveneInfoResp.Merge(m, src)
}
func (m *GetChannelConveneInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelConveneInfoResp.Size(m)
}
func (m *GetChannelConveneInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelConveneInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelConveneInfoResp proto.InternalMessageInfo

func (m *GetChannelConveneInfoResp) GetConveneInfo() *StChannelConveneInfo {
	if m != nil {
		return m.ConveneInfo
	}
	return nil
}

// 召集收藏频道的用户
type ConveneChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ConveneMsg           string   `protobuf:"bytes,2,opt,name=convene_msg,json=conveneMsg,proto3" json:"convene_msg,omitempty"`
	ChannelOnlineMembers []uint32 `protobuf:"varint,3,rep,packed,name=channel_online_members,json=channelOnlineMembers,proto3" json:"channel_online_members,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConveneChannelReq) Reset()         { *m = ConveneChannelReq{} }
func (m *ConveneChannelReq) String() string { return proto.CompactTextString(m) }
func (*ConveneChannelReq) ProtoMessage()    {}
func (*ConveneChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{23}
}

func (m *ConveneChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConveneChannelReq.Unmarshal(m, b)
}
func (m *ConveneChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConveneChannelReq.Marshal(b, m, deterministic)
}
func (m *ConveneChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConveneChannelReq.Merge(m, src)
}
func (m *ConveneChannelReq) XXX_Size() int {
	return xxx_messageInfo_ConveneChannelReq.Size(m)
}
func (m *ConveneChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConveneChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConveneChannelReq proto.InternalMessageInfo

func (m *ConveneChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ConveneChannelReq) GetConveneMsg() string {
	if m != nil {
		return m.ConveneMsg
	}
	return ""
}

func (m *ConveneChannelReq) GetChannelOnlineMembers() []uint32 {
	if m != nil {
		return m.ChannelOnlineMembers
	}
	return nil
}

func (m *ConveneChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ConveneChannelResp struct {
	ConveneInfo          *StChannelConveneInfo `protobuf:"bytes,1,opt,name=convene_info,json=conveneInfo,proto3" json:"convene_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ConveneChannelResp) Reset()         { *m = ConveneChannelResp{} }
func (m *ConveneChannelResp) String() string { return proto.CompactTextString(m) }
func (*ConveneChannelResp) ProtoMessage()    {}
func (*ConveneChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{24}
}

func (m *ConveneChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConveneChannelResp.Unmarshal(m, b)
}
func (m *ConveneChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConveneChannelResp.Marshal(b, m, deterministic)
}
func (m *ConveneChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConveneChannelResp.Merge(m, src)
}
func (m *ConveneChannelResp) XXX_Size() int {
	return xxx_messageInfo_ConveneChannelResp.Size(m)
}
func (m *ConveneChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConveneChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConveneChannelResp proto.InternalMessageInfo

func (m *ConveneChannelResp) GetConveneInfo() *StChannelConveneInfo {
	if m != nil {
		return m.ConveneInfo
	}
	return nil
}

// 取消召集
type DisableConveneChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisableConveneChannelReq) Reset()         { *m = DisableConveneChannelReq{} }
func (m *DisableConveneChannelReq) String() string { return proto.CompactTextString(m) }
func (*DisableConveneChannelReq) ProtoMessage()    {}
func (*DisableConveneChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{25}
}

func (m *DisableConveneChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisableConveneChannelReq.Unmarshal(m, b)
}
func (m *DisableConveneChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisableConveneChannelReq.Marshal(b, m, deterministic)
}
func (m *DisableConveneChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisableConveneChannelReq.Merge(m, src)
}
func (m *DisableConveneChannelReq) XXX_Size() int {
	return xxx_messageInfo_DisableConveneChannelReq.Size(m)
}
func (m *DisableConveneChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DisableConveneChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DisableConveneChannelReq proto.InternalMessageInfo

func (m *DisableConveneChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DisableConveneChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 用户响应频道召集
type ConfirmChannelConveneReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmChannelConveneReq) Reset()         { *m = ConfirmChannelConveneReq{} }
func (m *ConfirmChannelConveneReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmChannelConveneReq) ProtoMessage()    {}
func (*ConfirmChannelConveneReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{26}
}

func (m *ConfirmChannelConveneReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmChannelConveneReq.Unmarshal(m, b)
}
func (m *ConfirmChannelConveneReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmChannelConveneReq.Marshal(b, m, deterministic)
}
func (m *ConfirmChannelConveneReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmChannelConveneReq.Merge(m, src)
}
func (m *ConfirmChannelConveneReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmChannelConveneReq.Size(m)
}
func (m *ConfirmChannelConveneReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmChannelConveneReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmChannelConveneReq proto.InternalMessageInfo

func (m *ConfirmChannelConveneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ConfirmChannelConveneResp struct {
	ChannelList          []*StChannelConveneInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ConfirmChannelConveneResp) Reset()         { *m = ConfirmChannelConveneResp{} }
func (m *ConfirmChannelConveneResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmChannelConveneResp) ProtoMessage()    {}
func (*ConfirmChannelConveneResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{27}
}

func (m *ConfirmChannelConveneResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmChannelConveneResp.Unmarshal(m, b)
}
func (m *ConfirmChannelConveneResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmChannelConveneResp.Marshal(b, m, deterministic)
}
func (m *ConfirmChannelConveneResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmChannelConveneResp.Merge(m, src)
}
func (m *ConfirmChannelConveneResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmChannelConveneResp.Size(m)
}
func (m *ConfirmChannelConveneResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmChannelConveneResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmChannelConveneResp proto.InternalMessageInfo

func (m *ConfirmChannelConveneResp) GetChannelList() []*StChannelConveneInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

// 更新用户的召集状态
type UpdateUserConveneStatusReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserConveneStatusReq) Reset()         { *m = UpdateUserConveneStatusReq{} }
func (m *UpdateUserConveneStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserConveneStatusReq) ProtoMessage()    {}
func (*UpdateUserConveneStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{28}
}

func (m *UpdateUserConveneStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserConveneStatusReq.Unmarshal(m, b)
}
func (m *UpdateUserConveneStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserConveneStatusReq.Marshal(b, m, deterministic)
}
func (m *UpdateUserConveneStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserConveneStatusReq.Merge(m, src)
}
func (m *UpdateUserConveneStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUserConveneStatusReq.Size(m)
}
func (m *UpdateUserConveneStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserConveneStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserConveneStatusReq proto.InternalMessageInfo

func (m *UpdateUserConveneStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateUserConveneStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdateUserConveneStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 获取响应的召集成员列表
type GetConveneMemberListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConveneMemberListReq) Reset()         { *m = GetConveneMemberListReq{} }
func (m *GetConveneMemberListReq) String() string { return proto.CompactTextString(m) }
func (*GetConveneMemberListReq) ProtoMessage()    {}
func (*GetConveneMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{29}
}

func (m *GetConveneMemberListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConveneMemberListReq.Unmarshal(m, b)
}
func (m *GetConveneMemberListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConveneMemberListReq.Marshal(b, m, deterministic)
}
func (m *GetConveneMemberListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConveneMemberListReq.Merge(m, src)
}
func (m *GetConveneMemberListReq) XXX_Size() int {
	return xxx_messageInfo_GetConveneMemberListReq.Size(m)
}
func (m *GetConveneMemberListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConveneMemberListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConveneMemberListReq proto.InternalMessageInfo

func (m *GetConveneMemberListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetConveneMemberListResp struct {
	MemberList           []*StChannelConveneMember `protobuf:"bytes,1,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetConveneMemberListResp) Reset()         { *m = GetConveneMemberListResp{} }
func (m *GetConveneMemberListResp) String() string { return proto.CompactTextString(m) }
func (*GetConveneMemberListResp) ProtoMessage()    {}
func (*GetConveneMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{30}
}

func (m *GetConveneMemberListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConveneMemberListResp.Unmarshal(m, b)
}
func (m *GetConveneMemberListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConveneMemberListResp.Marshal(b, m, deterministic)
}
func (m *GetConveneMemberListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConveneMemberListResp.Merge(m, src)
}
func (m *GetConveneMemberListResp) XXX_Size() int {
	return xxx_messageInfo_GetConveneMemberListResp.Size(m)
}
func (m *GetConveneMemberListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConveneMemberListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConveneMemberListResp proto.InternalMessageInfo

func (m *GetConveneMemberListResp) GetMemberList() []*StChannelConveneMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

// 获取用户的频道召集响应信息
type GetUserConveneInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserConveneInfoReq) Reset()         { *m = GetUserConveneInfoReq{} }
func (m *GetUserConveneInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserConveneInfoReq) ProtoMessage()    {}
func (*GetUserConveneInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{31}
}

func (m *GetUserConveneInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserConveneInfoReq.Unmarshal(m, b)
}
func (m *GetUserConveneInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserConveneInfoReq.Marshal(b, m, deterministic)
}
func (m *GetUserConveneInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserConveneInfoReq.Merge(m, src)
}
func (m *GetUserConveneInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserConveneInfoReq.Size(m)
}
func (m *GetUserConveneInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserConveneInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserConveneInfoReq proto.InternalMessageInfo

func (m *GetUserConveneInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserConveneInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserConveneInfoResp struct {
	MemberInfo           *StChannelConveneMember `protobuf:"bytes,1,opt,name=member_info,json=memberInfo,proto3" json:"member_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetUserConveneInfoResp) Reset()         { *m = GetUserConveneInfoResp{} }
func (m *GetUserConveneInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserConveneInfoResp) ProtoMessage()    {}
func (*GetUserConveneInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{32}
}

func (m *GetUserConveneInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserConveneInfoResp.Unmarshal(m, b)
}
func (m *GetUserConveneInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserConveneInfoResp.Marshal(b, m, deterministic)
}
func (m *GetUserConveneInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserConveneInfoResp.Merge(m, src)
}
func (m *GetUserConveneInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserConveneInfoResp.Size(m)
}
func (m *GetUserConveneInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserConveneInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserConveneInfoResp proto.InternalMessageInfo

func (m *GetUserConveneInfoResp) GetMemberInfo() *StChannelConveneMember {
	if m != nil {
		return m.MemberInfo
	}
	return nil
}

// 获取指定用户响应状态的用户数量
type GetConfirmCountByStatusReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConfirmCountByStatusReq) Reset()         { *m = GetConfirmCountByStatusReq{} }
func (m *GetConfirmCountByStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetConfirmCountByStatusReq) ProtoMessage()    {}
func (*GetConfirmCountByStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{33}
}

func (m *GetConfirmCountByStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfirmCountByStatusReq.Unmarshal(m, b)
}
func (m *GetConfirmCountByStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfirmCountByStatusReq.Marshal(b, m, deterministic)
}
func (m *GetConfirmCountByStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfirmCountByStatusReq.Merge(m, src)
}
func (m *GetConfirmCountByStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetConfirmCountByStatusReq.Size(m)
}
func (m *GetConfirmCountByStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfirmCountByStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfirmCountByStatusReq proto.InternalMessageInfo

func (m *GetConfirmCountByStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetConfirmCountByStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetConfirmCountByStatusResp struct {
	MemberCount          uint32   `protobuf:"varint,1,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConfirmCountByStatusResp) Reset()         { *m = GetConfirmCountByStatusResp{} }
func (m *GetConfirmCountByStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetConfirmCountByStatusResp) ProtoMessage()    {}
func (*GetConfirmCountByStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cab598b0798e78ef, []int{34}
}

func (m *GetConfirmCountByStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConfirmCountByStatusResp.Unmarshal(m, b)
}
func (m *GetConfirmCountByStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConfirmCountByStatusResp.Marshal(b, m, deterministic)
}
func (m *GetConfirmCountByStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConfirmCountByStatusResp.Merge(m, src)
}
func (m *GetConfirmCountByStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetConfirmCountByStatusResp.Size(m)
}
func (m *GetConfirmCountByStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConfirmCountByStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConfirmCountByStatusResp proto.InternalMessageInfo

func (m *GetConfirmCountByStatusResp) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func init() {
	proto.RegisterEnum("channel_convene.CollectStatInfoType", CollectStatInfoType_name, CollectStatInfoType_value)
	proto.RegisterType((*ChannelConveneEmptyResp)(nil), "channel_convene.ChannelConveneEmptyResp")
	proto.RegisterType((*ChannelConveneObjUint32)(nil), "channel_convene.ChannelConveneObjUint32")
	proto.RegisterType((*CollectChannelReq)(nil), "channel_convene.CollectChannelReq")
	proto.RegisterType((*RemoveChannelCollectionReq)(nil), "channel_convene.RemoveChannelCollectionReq")
	proto.RegisterType((*HasCollectChannelReq)(nil), "channel_convene.HasCollectChannelReq")
	proto.RegisterType((*HasCollectChannelResp)(nil), "channel_convene.HasCollectChannelResp")
	proto.RegisterType((*BatchHasCollectChannelReq)(nil), "channel_convene.BatchHasCollectChannelReq")
	proto.RegisterType((*BatchHasCollectChannelResp)(nil), "channel_convene.BatchHasCollectChannelResp")
	proto.RegisterType((*GetChannelCollectionListByUidReq)(nil), "channel_convene.GetChannelCollectionListByUidReq")
	proto.RegisterType((*GetChannelCollectionListByUidResp)(nil), "channel_convene.GetChannelCollectionListByUidResp")
	proto.RegisterType((*GetChannelCollectionInfoListByUidReq)(nil), "channel_convene.GetChannelCollectionInfoListByUidReq")
	proto.RegisterType((*UserCollectionInfo)(nil), "channel_convene.UserCollectionInfo")
	proto.RegisterType((*GetChannelCollectionInfoListByUidResp)(nil), "channel_convene.GetChannelCollectionInfoListByUidResp")
	proto.RegisterType((*BatchSetUserCollectionInfoReq)(nil), "channel_convene.BatchSetUserCollectionInfoReq")
	proto.RegisterType((*StChannelCollectMember)(nil), "channel_convene.StChannelCollectMember")
	proto.RegisterType((*StChannelConveneMember)(nil), "channel_convene.StChannelConveneMember")
	proto.RegisterType((*StChannelConveneInfo)(nil), "channel_convene.StChannelConveneInfo")
	proto.RegisterType((*GetChannelCollectionMemberListReq)(nil), "channel_convene.GetChannelCollectionMemberListReq")
	proto.RegisterType((*GetChannelCollectionMemberListResp)(nil), "channel_convene.GetChannelCollectionMemberListResp")
	proto.RegisterType((*GetChannelCollectionMemberCountReq)(nil), "channel_convene.GetChannelCollectionMemberCountReq")
	proto.RegisterType((*GetChannelCollectionMemberCountResp)(nil), "channel_convene.GetChannelCollectionMemberCountResp")
	proto.RegisterType((*GetChannelConveneInfoReq)(nil), "channel_convene.GetChannelConveneInfoReq")
	proto.RegisterType((*GetChannelConveneInfoResp)(nil), "channel_convene.GetChannelConveneInfoResp")
	proto.RegisterType((*ConveneChannelReq)(nil), "channel_convene.ConveneChannelReq")
	proto.RegisterType((*ConveneChannelResp)(nil), "channel_convene.ConveneChannelResp")
	proto.RegisterType((*DisableConveneChannelReq)(nil), "channel_convene.DisableConveneChannelReq")
	proto.RegisterType((*ConfirmChannelConveneReq)(nil), "channel_convene.ConfirmChannelConveneReq")
	proto.RegisterType((*ConfirmChannelConveneResp)(nil), "channel_convene.ConfirmChannelConveneResp")
	proto.RegisterType((*UpdateUserConveneStatusReq)(nil), "channel_convene.UpdateUserConveneStatusReq")
	proto.RegisterType((*GetConveneMemberListReq)(nil), "channel_convene.GetConveneMemberListReq")
	proto.RegisterType((*GetConveneMemberListResp)(nil), "channel_convene.GetConveneMemberListResp")
	proto.RegisterType((*GetUserConveneInfoReq)(nil), "channel_convene.GetUserConveneInfoReq")
	proto.RegisterType((*GetUserConveneInfoResp)(nil), "channel_convene.GetUserConveneInfoResp")
	proto.RegisterType((*GetConfirmCountByStatusReq)(nil), "channel_convene.GetConfirmCountByStatusReq")
	proto.RegisterType((*GetConfirmCountByStatusResp)(nil), "channel_convene.GetConfirmCountByStatusResp")
}

func init() { proto.RegisterFile("channel-convene.proto", fileDescriptor_cab598b0798e78ef) }

var fileDescriptor_cab598b0798e78ef = []byte{
	// 1307 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0x5d, 0x4f, 0xe3, 0x46,
	0x17, 0x7e, 0x0d, 0x0b, 0x0b, 0x87, 0xef, 0x79, 0x03, 0x04, 0x57, 0xec, 0xb2, 0x66, 0x61, 0xb3,
	0x40, 0x43, 0x37, 0xb0, 0xd5, 0xb6, 0x57, 0x15, 0xa1, 0x02, 0xb4, 0x45, 0x48, 0x86, 0x54, 0xea,
	0x5e, 0x6c, 0xe4, 0x38, 0x03, 0xeb, 0xae, 0x63, 0x0f, 0x19, 0x27, 0x2d, 0x7f, 0xa0, 0x52, 0xd5,
	0x5e, 0x55, 0xea, 0x55, 0x7f, 0x55, 0xd5, 0x3f, 0x54, 0xcd, 0x87, 0xb1, 0x1d, 0x8f, 0x13, 0x9b,
	0xed, 0x5d, 0x72, 0xfc, 0xcc, 0x33, 0xe7, 0x9c, 0x39, 0x5f, 0x33, 0xb0, 0x6c, 0x7f, 0xb0, 0x3c,
	0x0f, 0xbb, 0x9f, 0xdb, 0xbe, 0xd7, 0xc7, 0x1e, 0xae, 0x92, 0xae, 0x1f, 0xf8, 0x68, 0x41, 0x8a,
	0x9b, 0x52, 0x6c, 0xec, 0xc2, 0x6a, 0x5d, 0x88, 0xea, 0x42, 0xf2, 0x6d, 0x87, 0x04, 0x77, 0x26,
	0xa6, 0x04, 0x2d, 0xc2, 0x78, 0x17, 0x07, 0x65, 0x6d, 0x43, 0xab, 0x4c, 0x98, 0xec, 0x67, 0x1a,
	0x7c, 0xd1, 0xfa, 0xb1, 0xe1, 0x78, 0xc1, 0x41, 0x8d, 0x81, 0xfb, 0x96, 0xcb, 0xc1, 0x73, 0x26,
	0xfb, 0x69, 0xfc, 0xa9, 0xc1, 0x52, 0xdd, 0x77, 0x5d, 0x6c, 0x07, 0x72, 0x91, 0x89, 0x6f, 0xd1,
	0x3a, 0x40, 0xa8, 0x82, 0xd3, 0x96, 0xf0, 0x69, 0x29, 0x39, 0x6b, 0x33, 0x9a, 0x9e, 0xd3, 0x2e,
	0x8f, 0x09, 0x9a, 0x9e, 0xd3, 0x46, 0x17, 0x30, 0x6f, 0x0b, 0x96, 0x26, 0xf5, 0x7b, 0x5d, 0x1b,
	0x97, 0xc7, 0x37, 0xb4, 0xca, 0x4c, 0xad, 0x52, 0x1d, 0x30, 0xa5, 0x9a, 0xa1, 0x9a, 0x39, 0x27,
	0xd7, 0x5f, 0xf2, 0xe5, 0xc6, 0xf7, 0xa0, 0x9b, 0xb8, 0xe3, 0xf7, 0xf1, 0x3d, 0x9e, 0x7f, 0x75,
	0x7c, 0x8f, 0xe9, 0xb7, 0x0d, 0x0b, 0x91, 0x7e, 0x4d, 0xd7, 0xa1, 0xcc, 0x01, 0xe3, 0x95, 0x39,
	0x73, 0xee, 0x5e, 0xc9, 0xef, 0x1c, 0x1a, 0xa4, 0x15, 0x35, 0x4e, 0xa0, 0x74, 0x6a, 0xd1, 0x4f,
	0xb7, 0xd8, 0xf8, 0x1a, 0x96, 0x15, 0x44, 0x94, 0xa0, 0x67, 0x30, 0xeb, 0xd0, 0xa6, 0xb4, 0x06,
	0x0b, 0xae, 0x29, 0x73, 0xc6, 0x09, 0xb1, 0xb8, 0x6d, 0x34, 0x60, 0xed, 0xc8, 0x0a, 0xec, 0x0f,
	0x4a, 0x4d, 0x1e, 0x6e, 0xdb, 0x31, 0xe8, 0x59, 0xb4, 0x94, 0xe4, 0xe5, 0x35, 0x0e, 0x61, 0xe3,
	0x04, 0x07, 0x29, 0xb7, 0xb3, 0x6f, 0x47, 0x77, 0x0d, 0xa7, 0xcd, 0x74, 0x94, 0x7b, 0x6b, 0xd1,
	0xde, 0x6f, 0xe1, 0xd9, 0x88, 0x55, 0x05, 0x54, 0x78, 0x03, 0xcf, 0x55, 0x64, 0x67, 0xde, 0xb5,
	0x3f, 0x42, 0x8d, 0x77, 0x80, 0x1a, 0x14, 0x77, 0x93, 0x6b, 0x46, 0x1d, 0xee, 0x56, 0x2a, 0x78,
	0x85, 0x53, 0x07, 0x42, 0xf2, 0x67, 0xd8, 0xca, 0xa1, 0x15, 0x25, 0xe8, 0x02, 0x96, 0x42, 0x3e,
	0xc7, 0xbb, 0xf6, 0x23, 0x43, 0x67, 0x6a, 0x9b, 0xa9, 0x7c, 0x48, 0xab, 0x6b, 0x2e, 0xc8, 0xd5,
	0x21, 0xb3, 0xf1, 0x9b, 0x06, 0xeb, 0xfc, 0x64, 0x2f, 0x71, 0xa0, 0xc0, 0xe3, 0x5b, 0xb4, 0x06,
	0x53, 0xbd, 0xa4, 0x4b, 0x1f, 0xf7, 0x1c, 0x11, 0x27, 0x17, 0xb0, 0xd4, 0xa3, 0xb8, 0xdb, 0x8c,
	0xab, 0xc4, 0x0d, 0xcc, 0xab, 0x4d, 0x2f, 0x92, 0x31, 0x81, 0x71, 0x06, 0x2b, 0x97, 0x03, 0x6e,
	0x38, 0xc7, 0x9d, 0x16, 0xee, 0xa6, 0xcf, 0x83, 0x7b, 0x5e, 0xee, 0x1b, 0x50, 0xe9, 0xd6, 0x69,
	0x29, 0xb9, 0xa2, 0x06, 0x49, 0x50, 0x71, 0x15, 0x86, 0x52, 0x45, 0x87, 0x38, 0xa6, 0x3c, 0x44,
	0xce, 0xd0, 0xa4, 0x81, 0x15, 0xf4, 0x28, 0xaf, 0x40, 0xfc, 0x10, 0xb9, 0xf4, 0x92, 0x0b, 0x8d,
	0x7f, 0xc6, 0xa0, 0x34, 0xb8, 0x65, 0x9e, 0x18, 0xd9, 0x86, 0x05, 0xd7, 0xa2, 0x41, 0xe8, 0xa8,
	0xc8, 0x9a, 0x39, 0x26, 0x96, 0x44, 0x57, 0x14, 0x3d, 0x87, 0x79, 0x81, 0xb3, 0x3c, 0x1b, 0xbb,
	0x0c, 0x26, 0xd4, 0x98, 0xe5, 0x30, 0x2e, 0xbc, 0xa2, 0x68, 0x13, 0x98, 0x5a, 0xd7, 0x4e, 0xb7,
	0xd3, 0xb4, 0xfd, 0x9e, 0x17, 0x94, 0x1f, 0x09, 0x90, 0x14, 0xd6, 0x99, 0x0c, 0xed, 0x01, 0x4a,
	0x80, 0x9a, 0x5d, 0x6c, 0xb9, 0xe5, 0x09, 0x8e, 0x5c, 0x8c, 0x23, 0x4d, 0x6c, 0xb9, 0xe8, 0x29,
	0xcc, 0x84, 0xba, 0x75, 0xe8, 0x4d, 0x79, 0x72, 0x43, 0xab, 0xcc, 0x9a, 0x20, 0x45, 0xe7, 0xf4,
	0x06, 0x55, 0x60, 0xb1, 0x6f, 0xb9, 0x4e, 0x3b, 0x6e, 0xc2, 0x63, 0x4e, 0x36, 0xcf, 0xe5, 0x91,
	0x0d, 0x5f, 0x40, 0x29, 0xc4, 0xb4, 0x7b, 0x5d, 0x8b, 0x45, 0x42, 0xb3, 0xe3, 0x78, 0xe5, 0x29,
	0x8e, 0x46, 0xf2, 0xdb, 0xb1, 0xfc, 0x74, 0xee, 0x78, 0x06, 0x51, 0x67, 0xbf, 0x38, 0x4b, 0x16,
	0x85, 0x39, 0x4a, 0xec, 0x0a, 0x4c, 0xfa, 0xd7, 0xd7, 0x14, 0x07, 0xd2, 0xb1, 0xf2, 0x1f, 0x2a,
	0xc1, 0x84, 0xeb, 0x74, 0x9c, 0x40, 0x3a, 0x52, 0xfc, 0x31, 0x3c, 0x30, 0x46, 0xed, 0x48, 0x09,
	0x3a, 0x85, 0x99, 0x0e, 0x97, 0xc4, 0x73, 0xf0, 0x45, 0x2a, 0xea, 0xd5, 0xe1, 0x6c, 0x42, 0xe7,
	0x9e, 0xcd, 0xa8, 0x0f, 0xdb, 0x4f, 0x9e, 0xc2, 0x28, 0x13, 0x8d, 0x53, 0xd8, 0x1c, 0x49, 0x22,
	0x3a, 0x88, 0xd4, 0x5a, 0x04, 0x87, 0xe0, 0x91, 0x96, 0x70, 0x98, 0xf1, 0x15, 0x94, 0xe3, 0x4c,
	0xf7, 0x61, 0x9c, 0x43, 0x09, 0x0c, 0x6b, 0x19, 0x4b, 0xb9, 0xc3, 0x66, 0xc3, 0xa3, 0xe7, 0x75,
	0x42, 0xe3, 0x75, 0x62, 0x6b, 0x98, 0xc7, 0x22, 0x82, 0x30, 0x00, 0x79, 0x95, 0xf8, 0x8b, 0x0f,
	0x16, 0xfc, 0x7f, 0xfe, 0x36, 0x3b, 0x10, 0xc4, 0x2c, 0x10, 0xa6, 0x13, 0x41, 0x7c, 0x08, 0x2b,
	0xe1, 0x7a, 0xdf, 0x73, 0x1d, 0x86, 0xe3, 0x5e, 0x61, 0x69, 0xc6, 0xaa, 0x5e, 0x49, 0x7e, 0xbd,
	0xe0, 0x1f, 0x85, 0x63, 0x69, 0x58, 0x4c, 0x1e, 0x45, 0x7d, 0xe2, 0x3d, 0xa0, 0x41, 0xe5, 0xfe,
	0x53, 0xeb, 0xdf, 0x42, 0xf9, 0xd8, 0xa1, 0x56, 0xcb, 0xc5, 0x85, 0x7d, 0x90, 0xee, 0xeb, 0x7b,
	0x50, 0xae, 0xcb, 0x74, 0x4f, 0x6c, 0xab, 0x6e, 0x81, 0x18, 0xd6, 0x32, 0xd0, 0xd2, 0x42, 0xb9,
	0x77, 0x2c, 0x23, 0x72, 0x5b, 0x28, 0x64, 0x3c, 0x21, 0x30, 0xe8, 0x0d, 0xd2, 0xb6, 0x02, 0x2c,
	0x5a, 0x46, 0xac, 0xc6, 0xe6, 0xcb, 0x75, 0x59, 0xa4, 0x65, 0xae, 0x8b, 0x7f, 0xa1, 0x35, 0xe3,
	0x91, 0x35, 0x6f, 0x60, 0x95, 0x45, 0x6b, 0xbc, 0x37, 0xe4, 0xab, 0x27, 0x46, 0x5b, 0xa4, 0x48,
	0x7a, 0xe5, 0x83, 0xea, 0x42, 0x8c, 0x25, 0x51, 0x17, 0x4e, 0x61, 0xf9, 0x24, 0x6c, 0xca, 0x05,
	0xb2, 0x50, 0x71, 0xca, 0x2d, 0x58, 0x51, 0x31, 0x25, 0xb4, 0x8d, 0x45, 0x65, 0x51, 0x6d, 0x79,
	0x58, 0x5e, 0x82, 0x2e, 0x7c, 0x72, 0xdf, 0x3b, 0x8e, 0xee, 0x3e, 0xf5, 0xd0, 0x8c, 0x6f, 0xe0,
	0xb3, 0x4c, 0xd2, 0x5c, 0xd5, 0x6c, 0x07, 0xc3, 0xff, 0x65, 0x35, 0x64, 0xeb, 0x98, 0xa6, 0x57,
	0x77, 0x04, 0xa3, 0x27, 0xa0, 0x2b, 0xc4, 0xcd, 0x86, 0xf7, 0xd1, 0xf3, 0x7f, 0x5a, 0xfc, 0x1f,
	0xaa, 0xc2, 0x8e, 0xea, 0x7b, 0x38, 0x29, 0x25, 0xe7, 0x98, 0x45, 0xad, 0xf6, 0xf7, 0x02, 0xcc,
	0x27, 0x5d, 0x84, 0xde, 0xc3, 0x7c, 0x72, 0x54, 0x46, 0x46, 0xfa, 0xc6, 0x32, 0x38, 0xa2, 0xeb,
	0xa3, 0x6e, 0x35, 0xd1, 0xed, 0x8c, 0xc0, 0x6a, 0xc6, 0x35, 0x06, 0xed, 0xa6, 0x48, 0xb2, 0x2f,
	0x3c, 0x05, 0x76, 0xfc, 0x45, 0x83, 0xf5, 0xa1, 0x93, 0x38, 0x7a, 0x95, 0xe2, 0x1a, 0x35, 0xef,
	0xeb, 0xb5, 0xa2, 0x4b, 0x28, 0x41, 0x7f, 0x68, 0xea, 0xa1, 0x20, 0x31, 0x2f, 0xa3, 0xd7, 0xb9,
	0x98, 0x07, 0x27, 0x7f, 0xfd, 0xcb, 0x87, 0x2c, 0xa3, 0x04, 0xfd, 0xaa, 0xc1, 0x93, 0xe1, 0x73,
	0x03, 0xca, 0x67, 0x6b, 0xa2, 0x14, 0xe9, 0x07, 0x85, 0xd7, 0x50, 0x82, 0x3c, 0x5e, 0x3a, 0x14,
	0xa3, 0xe8, 0xcb, 0xa1, 0x6c, 0xf1, 0x2a, 0xa3, 0xef, 0xe4, 0x85, 0x52, 0x82, 0x7e, 0x60, 0xb1,
	0x1e, 0x6f, 0x46, 0xca, 0x58, 0x1f, 0xe8, 0x56, 0xfa, 0xe6, 0x48, 0x8c, 0x30, 0x45, 0xd9, 0xee,
	0x14, 0xa6, 0x64, 0xb5, 0xc5, 0x42, 0xa6, 0x78, 0xb0, 0xac, 0xec, 0x71, 0x8a, 0xfd, 0xb2, 0x3a,
	0xa7, 0x62, 0xbf, 0xec, 0xb6, 0x49, 0x60, 0x35, 0xa3, 0xd9, 0x29, 0xd2, 0x38, 0xbb, 0x2d, 0x16,
	0x48, 0xe3, 0x16, 0x2c, 0xa5, 0xae, 0xf1, 0x28, 0xdd, 0xa7, 0x55, 0x2f, 0x08, 0xfa, 0x76, 0x1e,
	0x18, 0x25, 0xe8, 0x23, 0x94, 0x54, 0x1d, 0x12, 0x55, 0x94, 0x27, 0xa1, 0x68, 0xc1, 0xfa, 0xcb,
	0x9c, 0x48, 0x4a, 0x10, 0x06, 0x94, 0x6e, 0x6f, 0x68, 0x5b, 0x45, 0x90, 0xee, 0xa6, 0xfa, 0x8b,
	0x5c, 0x38, 0x4a, 0x50, 0x10, 0xce, 0x0b, 0xa9, 0x66, 0xa4, 0x38, 0xa9, 0xec, 0x5e, 0xa8, 0xef,
	0xe5, 0x07, 0x53, 0x82, 0x7e, 0xd7, 0xe0, 0xe9, 0x88, 0xc9, 0x1e, 0x15, 0xa9, 0x11, 0xe1, 0x85,
	0x42, 0x3f, 0x2c, 0xbe, 0x88, 0x12, 0x74, 0x0b, 0x2b, 0xea, 0x87, 0x20, 0x94, 0x0e, 0xfa, 0xcc,
	0x87, 0x28, 0x7d, 0x37, 0x37, 0x96, 0x12, 0xd4, 0x97, 0x6f, 0x4f, 0xca, 0x17, 0x0a, 0x54, 0x55,
	0x53, 0x65, 0x3d, 0x67, 0xe4, 0xcf, 0x93, 0xa3, 0x83, 0x77, 0xaf, 0x6e, 0x7c, 0xd7, 0xf2, 0x6e,
	0xaa, 0xaf, 0x6b, 0x41, 0x50, 0xb5, 0xfd, 0xce, 0x3e, 0x7f, 0x43, 0xb5, 0x7d, 0x77, 0x9f, 0xe2,
	0x6e, 0xdf, 0xb1, 0x31, 0xdd, 0x1f, 0x78, 0x65, 0x6d, 0x4d, 0x72, 0xc8, 0xc1, 0xbf, 0x01, 0x00,
	0x00, 0xff, 0xff, 0xfd, 0xaf, 0x9b, 0x78, 0x7f, 0x15, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelConveneClient is the client API for ChannelConvene service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelConveneClient interface {
	CollectChannel(ctx context.Context, in *CollectChannelReq, opts ...grpc.CallOption) (*ChannelConveneEmptyResp, error)
	RemoveChannelCollection(ctx context.Context, in *RemoveChannelCollectionReq, opts ...grpc.CallOption) (*ChannelConveneEmptyResp, error)
	GetChannelCollectionListByUid(ctx context.Context, in *GetChannelCollectionListByUidReq, opts ...grpc.CallOption) (*GetChannelCollectionListByUidResp, error)
	GetChannelCollectionInfoListByUid(ctx context.Context, in *GetChannelCollectionInfoListByUidReq, opts ...grpc.CallOption) (*GetChannelCollectionInfoListByUidResp, error)
	GetChannelCollectionMemberList(ctx context.Context, in *GetChannelCollectionMemberListReq, opts ...grpc.CallOption) (*GetChannelCollectionMemberListResp, error)
	GetChannelConveneInfo(ctx context.Context, in *GetChannelConveneInfoReq, opts ...grpc.CallOption) (*GetChannelConveneInfoResp, error)
	ConveneChannel(ctx context.Context, in *ConveneChannelReq, opts ...grpc.CallOption) (*ConveneChannelResp, error)
	DisableConveneChannel(ctx context.Context, in *DisableConveneChannelReq, opts ...grpc.CallOption) (*GetChannelConveneInfoResp, error)
	ConfirmChannelConvene(ctx context.Context, in *ConfirmChannelConveneReq, opts ...grpc.CallOption) (*ConfirmChannelConveneResp, error)
	UpdateUserConveneStatus(ctx context.Context, in *UpdateUserConveneStatusReq, opts ...grpc.CallOption) (*ChannelConveneEmptyResp, error)
	HasCollectChannel(ctx context.Context, in *HasCollectChannelReq, opts ...grpc.CallOption) (*HasCollectChannelResp, error)
	GetConveneMemberList(ctx context.Context, in *GetConveneMemberListReq, opts ...grpc.CallOption) (*GetConveneMemberListResp, error)
	GetUserConveneInfo(ctx context.Context, in *GetUserConveneInfoReq, opts ...grpc.CallOption) (*GetUserConveneInfoResp, error)
	GetConfirmCountByStatus(ctx context.Context, in *GetConfirmCountByStatusReq, opts ...grpc.CallOption) (*GetConfirmCountByStatusResp, error)
	GetChannelCollectionMemberCount(ctx context.Context, in *GetChannelCollectionMemberCountReq, opts ...grpc.CallOption) (*GetChannelCollectionMemberCountResp, error)
	BatchHasCollectChannel(ctx context.Context, in *BatchHasCollectChannelReq, opts ...grpc.CallOption) (*BatchHasCollectChannelResp, error)
	BatchSetUserCollectionInfo(ctx context.Context, in *BatchSetUserCollectionInfoReq, opts ...grpc.CallOption) (*ChannelConveneEmptyResp, error)
}

type channelConveneClient struct {
	cc *grpc.ClientConn
}

func NewChannelConveneClient(cc *grpc.ClientConn) ChannelConveneClient {
	return &channelConveneClient{cc}
}

func (c *channelConveneClient) CollectChannel(ctx context.Context, in *CollectChannelReq, opts ...grpc.CallOption) (*ChannelConveneEmptyResp, error) {
	out := new(ChannelConveneEmptyResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/CollectChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) RemoveChannelCollection(ctx context.Context, in *RemoveChannelCollectionReq, opts ...grpc.CallOption) (*ChannelConveneEmptyResp, error) {
	out := new(ChannelConveneEmptyResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/RemoveChannelCollection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetChannelCollectionListByUid(ctx context.Context, in *GetChannelCollectionListByUidReq, opts ...grpc.CallOption) (*GetChannelCollectionListByUidResp, error) {
	out := new(GetChannelCollectionListByUidResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/GetChannelCollectionListByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetChannelCollectionInfoListByUid(ctx context.Context, in *GetChannelCollectionInfoListByUidReq, opts ...grpc.CallOption) (*GetChannelCollectionInfoListByUidResp, error) {
	out := new(GetChannelCollectionInfoListByUidResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/GetChannelCollectionInfoListByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetChannelCollectionMemberList(ctx context.Context, in *GetChannelCollectionMemberListReq, opts ...grpc.CallOption) (*GetChannelCollectionMemberListResp, error) {
	out := new(GetChannelCollectionMemberListResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/GetChannelCollectionMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetChannelConveneInfo(ctx context.Context, in *GetChannelConveneInfoReq, opts ...grpc.CallOption) (*GetChannelConveneInfoResp, error) {
	out := new(GetChannelConveneInfoResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/GetChannelConveneInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) ConveneChannel(ctx context.Context, in *ConveneChannelReq, opts ...grpc.CallOption) (*ConveneChannelResp, error) {
	out := new(ConveneChannelResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/ConveneChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) DisableConveneChannel(ctx context.Context, in *DisableConveneChannelReq, opts ...grpc.CallOption) (*GetChannelConveneInfoResp, error) {
	out := new(GetChannelConveneInfoResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/DisableConveneChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) ConfirmChannelConvene(ctx context.Context, in *ConfirmChannelConveneReq, opts ...grpc.CallOption) (*ConfirmChannelConveneResp, error) {
	out := new(ConfirmChannelConveneResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/ConfirmChannelConvene", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) UpdateUserConveneStatus(ctx context.Context, in *UpdateUserConveneStatusReq, opts ...grpc.CallOption) (*ChannelConveneEmptyResp, error) {
	out := new(ChannelConveneEmptyResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/UpdateUserConveneStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) HasCollectChannel(ctx context.Context, in *HasCollectChannelReq, opts ...grpc.CallOption) (*HasCollectChannelResp, error) {
	out := new(HasCollectChannelResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/HasCollectChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetConveneMemberList(ctx context.Context, in *GetConveneMemberListReq, opts ...grpc.CallOption) (*GetConveneMemberListResp, error) {
	out := new(GetConveneMemberListResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/GetConveneMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetUserConveneInfo(ctx context.Context, in *GetUserConveneInfoReq, opts ...grpc.CallOption) (*GetUserConveneInfoResp, error) {
	out := new(GetUserConveneInfoResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/GetUserConveneInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetConfirmCountByStatus(ctx context.Context, in *GetConfirmCountByStatusReq, opts ...grpc.CallOption) (*GetConfirmCountByStatusResp, error) {
	out := new(GetConfirmCountByStatusResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/GetConfirmCountByStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) GetChannelCollectionMemberCount(ctx context.Context, in *GetChannelCollectionMemberCountReq, opts ...grpc.CallOption) (*GetChannelCollectionMemberCountResp, error) {
	out := new(GetChannelCollectionMemberCountResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/GetChannelCollectionMemberCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) BatchHasCollectChannel(ctx context.Context, in *BatchHasCollectChannelReq, opts ...grpc.CallOption) (*BatchHasCollectChannelResp, error) {
	out := new(BatchHasCollectChannelResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/BatchHasCollectChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelConveneClient) BatchSetUserCollectionInfo(ctx context.Context, in *BatchSetUserCollectionInfoReq, opts ...grpc.CallOption) (*ChannelConveneEmptyResp, error) {
	out := new(ChannelConveneEmptyResp)
	err := c.cc.Invoke(ctx, "/channel_convene.ChannelConvene/BatchSetUserCollectionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelConveneServer is the server API for ChannelConvene service.
type ChannelConveneServer interface {
	CollectChannel(context.Context, *CollectChannelReq) (*ChannelConveneEmptyResp, error)
	RemoveChannelCollection(context.Context, *RemoveChannelCollectionReq) (*ChannelConveneEmptyResp, error)
	GetChannelCollectionListByUid(context.Context, *GetChannelCollectionListByUidReq) (*GetChannelCollectionListByUidResp, error)
	GetChannelCollectionInfoListByUid(context.Context, *GetChannelCollectionInfoListByUidReq) (*GetChannelCollectionInfoListByUidResp, error)
	GetChannelCollectionMemberList(context.Context, *GetChannelCollectionMemberListReq) (*GetChannelCollectionMemberListResp, error)
	GetChannelConveneInfo(context.Context, *GetChannelConveneInfoReq) (*GetChannelConveneInfoResp, error)
	ConveneChannel(context.Context, *ConveneChannelReq) (*ConveneChannelResp, error)
	DisableConveneChannel(context.Context, *DisableConveneChannelReq) (*GetChannelConveneInfoResp, error)
	ConfirmChannelConvene(context.Context, *ConfirmChannelConveneReq) (*ConfirmChannelConveneResp, error)
	UpdateUserConveneStatus(context.Context, *UpdateUserConveneStatusReq) (*ChannelConveneEmptyResp, error)
	HasCollectChannel(context.Context, *HasCollectChannelReq) (*HasCollectChannelResp, error)
	GetConveneMemberList(context.Context, *GetConveneMemberListReq) (*GetConveneMemberListResp, error)
	GetUserConveneInfo(context.Context, *GetUserConveneInfoReq) (*GetUserConveneInfoResp, error)
	GetConfirmCountByStatus(context.Context, *GetConfirmCountByStatusReq) (*GetConfirmCountByStatusResp, error)
	GetChannelCollectionMemberCount(context.Context, *GetChannelCollectionMemberCountReq) (*GetChannelCollectionMemberCountResp, error)
	BatchHasCollectChannel(context.Context, *BatchHasCollectChannelReq) (*BatchHasCollectChannelResp, error)
	BatchSetUserCollectionInfo(context.Context, *BatchSetUserCollectionInfoReq) (*ChannelConveneEmptyResp, error)
}

// UnimplementedChannelConveneServer can be embedded to have forward compatible implementations.
type UnimplementedChannelConveneServer struct {
}

func (*UnimplementedChannelConveneServer) CollectChannel(ctx context.Context, req *CollectChannelReq) (*ChannelConveneEmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CollectChannel not implemented")
}
func (*UnimplementedChannelConveneServer) RemoveChannelCollection(ctx context.Context, req *RemoveChannelCollectionReq) (*ChannelConveneEmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveChannelCollection not implemented")
}
func (*UnimplementedChannelConveneServer) GetChannelCollectionListByUid(ctx context.Context, req *GetChannelCollectionListByUidReq) (*GetChannelCollectionListByUidResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChannelCollectionListByUid not implemented")
}
func (*UnimplementedChannelConveneServer) GetChannelCollectionInfoListByUid(ctx context.Context, req *GetChannelCollectionInfoListByUidReq) (*GetChannelCollectionInfoListByUidResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChannelCollectionInfoListByUid not implemented")
}
func (*UnimplementedChannelConveneServer) GetChannelCollectionMemberList(ctx context.Context, req *GetChannelCollectionMemberListReq) (*GetChannelCollectionMemberListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChannelCollectionMemberList not implemented")
}
func (*UnimplementedChannelConveneServer) GetChannelConveneInfo(ctx context.Context, req *GetChannelConveneInfoReq) (*GetChannelConveneInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChannelConveneInfo not implemented")
}
func (*UnimplementedChannelConveneServer) ConveneChannel(ctx context.Context, req *ConveneChannelReq) (*ConveneChannelResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConveneChannel not implemented")
}
func (*UnimplementedChannelConveneServer) DisableConveneChannel(ctx context.Context, req *DisableConveneChannelReq) (*GetChannelConveneInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableConveneChannel not implemented")
}
func (*UnimplementedChannelConveneServer) ConfirmChannelConvene(ctx context.Context, req *ConfirmChannelConveneReq) (*ConfirmChannelConveneResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmChannelConvene not implemented")
}
func (*UnimplementedChannelConveneServer) UpdateUserConveneStatus(ctx context.Context, req *UpdateUserConveneStatusReq) (*ChannelConveneEmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserConveneStatus not implemented")
}
func (*UnimplementedChannelConveneServer) HasCollectChannel(ctx context.Context, req *HasCollectChannelReq) (*HasCollectChannelResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HasCollectChannel not implemented")
}
func (*UnimplementedChannelConveneServer) GetConveneMemberList(ctx context.Context, req *GetConveneMemberListReq) (*GetConveneMemberListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConveneMemberList not implemented")
}
func (*UnimplementedChannelConveneServer) GetUserConveneInfo(ctx context.Context, req *GetUserConveneInfoReq) (*GetUserConveneInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserConveneInfo not implemented")
}
func (*UnimplementedChannelConveneServer) GetConfirmCountByStatus(ctx context.Context, req *GetConfirmCountByStatusReq) (*GetConfirmCountByStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfirmCountByStatus not implemented")
}
func (*UnimplementedChannelConveneServer) GetChannelCollectionMemberCount(ctx context.Context, req *GetChannelCollectionMemberCountReq) (*GetChannelCollectionMemberCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChannelCollectionMemberCount not implemented")
}
func (*UnimplementedChannelConveneServer) BatchHasCollectChannel(ctx context.Context, req *BatchHasCollectChannelReq) (*BatchHasCollectChannelResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchHasCollectChannel not implemented")
}
func (*UnimplementedChannelConveneServer) BatchSetUserCollectionInfo(ctx context.Context, req *BatchSetUserCollectionInfoReq) (*ChannelConveneEmptyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchSetUserCollectionInfo not implemented")
}

func RegisterChannelConveneServer(s *grpc.Server, srv ChannelConveneServer) {
	s.RegisterService(&_ChannelConvene_serviceDesc, srv)
}

func _ChannelConvene_CollectChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CollectChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).CollectChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/CollectChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).CollectChannel(ctx, req.(*CollectChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_RemoveChannelCollection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelCollectionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).RemoveChannelCollection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/RemoveChannelCollection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).RemoveChannelCollection(ctx, req.(*RemoveChannelCollectionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetChannelCollectionListByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCollectionListByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetChannelCollectionListByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/GetChannelCollectionListByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetChannelCollectionListByUid(ctx, req.(*GetChannelCollectionListByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetChannelCollectionInfoListByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCollectionInfoListByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetChannelCollectionInfoListByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/GetChannelCollectionInfoListByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetChannelCollectionInfoListByUid(ctx, req.(*GetChannelCollectionInfoListByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetChannelCollectionMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCollectionMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetChannelCollectionMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/GetChannelCollectionMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetChannelCollectionMemberList(ctx, req.(*GetChannelCollectionMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetChannelConveneInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelConveneInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetChannelConveneInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/GetChannelConveneInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetChannelConveneInfo(ctx, req.(*GetChannelConveneInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_ConveneChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConveneChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).ConveneChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/ConveneChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).ConveneChannel(ctx, req.(*ConveneChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_DisableConveneChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableConveneChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).DisableConveneChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/DisableConveneChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).DisableConveneChannel(ctx, req.(*DisableConveneChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_ConfirmChannelConvene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmChannelConveneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).ConfirmChannelConvene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/ConfirmChannelConvene",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).ConfirmChannelConvene(ctx, req.(*ConfirmChannelConveneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_UpdateUserConveneStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserConveneStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).UpdateUserConveneStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/UpdateUserConveneStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).UpdateUserConveneStatus(ctx, req.(*UpdateUserConveneStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_HasCollectChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HasCollectChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).HasCollectChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/HasCollectChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).HasCollectChannel(ctx, req.(*HasCollectChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetConveneMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConveneMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetConveneMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/GetConveneMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetConveneMemberList(ctx, req.(*GetConveneMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetUserConveneInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserConveneInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetUserConveneInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/GetUserConveneInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetUserConveneInfo(ctx, req.(*GetUserConveneInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetConfirmCountByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfirmCountByStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetConfirmCountByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/GetConfirmCountByStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetConfirmCountByStatus(ctx, req.(*GetConfirmCountByStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_GetChannelCollectionMemberCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCollectionMemberCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).GetChannelCollectionMemberCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/GetChannelCollectionMemberCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).GetChannelCollectionMemberCount(ctx, req.(*GetChannelCollectionMemberCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_BatchHasCollectChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchHasCollectChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).BatchHasCollectChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/BatchHasCollectChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).BatchHasCollectChannel(ctx, req.(*BatchHasCollectChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelConvene_BatchSetUserCollectionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetUserCollectionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelConveneServer).BatchSetUserCollectionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_convene.ChannelConvene/BatchSetUserCollectionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelConveneServer).BatchSetUserCollectionInfo(ctx, req.(*BatchSetUserCollectionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelConvene_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_convene.ChannelConvene",
	HandlerType: (*ChannelConveneServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CollectChannel",
			Handler:    _ChannelConvene_CollectChannel_Handler,
		},
		{
			MethodName: "RemoveChannelCollection",
			Handler:    _ChannelConvene_RemoveChannelCollection_Handler,
		},
		{
			MethodName: "GetChannelCollectionListByUid",
			Handler:    _ChannelConvene_GetChannelCollectionListByUid_Handler,
		},
		{
			MethodName: "GetChannelCollectionInfoListByUid",
			Handler:    _ChannelConvene_GetChannelCollectionInfoListByUid_Handler,
		},
		{
			MethodName: "GetChannelCollectionMemberList",
			Handler:    _ChannelConvene_GetChannelCollectionMemberList_Handler,
		},
		{
			MethodName: "GetChannelConveneInfo",
			Handler:    _ChannelConvene_GetChannelConveneInfo_Handler,
		},
		{
			MethodName: "ConveneChannel",
			Handler:    _ChannelConvene_ConveneChannel_Handler,
		},
		{
			MethodName: "DisableConveneChannel",
			Handler:    _ChannelConvene_DisableConveneChannel_Handler,
		},
		{
			MethodName: "ConfirmChannelConvene",
			Handler:    _ChannelConvene_ConfirmChannelConvene_Handler,
		},
		{
			MethodName: "UpdateUserConveneStatus",
			Handler:    _ChannelConvene_UpdateUserConveneStatus_Handler,
		},
		{
			MethodName: "HasCollectChannel",
			Handler:    _ChannelConvene_HasCollectChannel_Handler,
		},
		{
			MethodName: "GetConveneMemberList",
			Handler:    _ChannelConvene_GetConveneMemberList_Handler,
		},
		{
			MethodName: "GetUserConveneInfo",
			Handler:    _ChannelConvene_GetUserConveneInfo_Handler,
		},
		{
			MethodName: "GetConfirmCountByStatus",
			Handler:    _ChannelConvene_GetConfirmCountByStatus_Handler,
		},
		{
			MethodName: "GetChannelCollectionMemberCount",
			Handler:    _ChannelConvene_GetChannelCollectionMemberCount_Handler,
		},
		{
			MethodName: "BatchHasCollectChannel",
			Handler:    _ChannelConvene_BatchHasCollectChannel_Handler,
		},
		{
			MethodName: "BatchSetUserCollectionInfo",
			Handler:    _ChannelConvene_BatchSetUserCollectionInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-convene.proto",
}
