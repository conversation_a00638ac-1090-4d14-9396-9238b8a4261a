// Code generated by protoc-gen-go. DO NOT EDIT.
// source: extend/apollo/annotations.proto

package apollo // import "golang.52tt.com/protocol/services/extend/apollo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import descriptor "github.com/golang/protobuf/protoc-gen-go/descriptor"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ConfigType int32

const (
	ConfigType_UNKNOWN ConfigType = 0
	ConfigType_STATIC  ConfigType = 1
	ConfigType_DYNAMIC ConfigType = 2
)

var ConfigType_name = map[int32]string{
	0: "UNKNOWN",
	1: "STATIC",
	2: "DYNAMIC",
}
var ConfigType_value = map[string]int32{
	"UNKNOWN": 0,
	"STATIC":  1,
	"DYNAMIC": 2,
}

func (x ConfigType) String() string {
	return proto.EnumName(ConfigType_name, int32(x))
}
func (ConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_annotations_da6436d8d5a3d9d4, []int{0}
}

var E_Namespace = &proto.ExtensionDesc{
	ExtendedType:  (*descriptor.MessageOptions)(nil),
	ExtensionType: (*string)(nil),
	Field:         1001,
	Name:          "apollo.namespace",
	Tag:           "bytes,1001,opt,name=namespace",
	Filename:      "extend/apollo/annotations.proto",
}

var E_ConfigType = &proto.ExtensionDesc{
	ExtendedType:  (*descriptor.MessageOptions)(nil),
	ExtensionType: (*ConfigType)(nil),
	Field:         1002,
	Name:          "apollo.config_type",
	Tag:           "varint,1002,opt,name=config_type,json=configType,enum=apollo.ConfigType",
	Filename:      "extend/apollo/annotations.proto",
}

var E_AppId = &proto.ExtensionDesc{
	ExtendedType:  (*descriptor.FileOptions)(nil),
	ExtensionType: (*string)(nil),
	Field:         1001,
	Name:          "apollo.app_id",
	Tag:           "bytes,1001,opt,name=app_id,json=appId",
	Filename:      "extend/apollo/annotations.proto",
}

var E_Secret = &proto.ExtensionDesc{
	ExtendedType:  (*descriptor.FileOptions)(nil),
	ExtensionType: (*string)(nil),
	Field:         1002,
	Name:          "apollo.secret",
	Tag:           "bytes,1002,opt,name=secret",
	Filename:      "extend/apollo/annotations.proto",
}

var E_ConfigPath = &proto.ExtensionDesc{
	ExtendedType:  (*descriptor.FileOptions)(nil),
	ExtensionType: (*string)(nil),
	Field:         1003,
	Name:          "apollo.config_path",
	Tag:           "bytes,1003,opt,name=config_path,json=configPath",
	Filename:      "extend/apollo/annotations.proto",
}

func init() {
	proto.RegisterEnum("apollo.ConfigType", ConfigType_name, ConfigType_value)
	proto.RegisterExtension(E_Namespace)
	proto.RegisterExtension(E_ConfigType)
	proto.RegisterExtension(E_AppId)
	proto.RegisterExtension(E_Secret)
	proto.RegisterExtension(E_ConfigPath)
}

func init() {
	proto.RegisterFile("extend/apollo/annotations.proto", fileDescriptor_annotations_da6436d8d5a3d9d4)
}

var fileDescriptor_annotations_da6436d8d5a3d9d4 = []byte{
	// 304 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x91, 0xc1, 0x4b, 0x02, 0x41,
	0x14, 0xc6, 0x33, 0x68, 0xc5, 0x11, 0x42, 0xf6, 0x14, 0x11, 0xe8, 0x31, 0x3a, 0xcc, 0x90, 0xe5,
	0x65, 0x0f, 0x82, 0x19, 0x81, 0x84, 0x6b, 0x98, 0x12, 0x75, 0x91, 0x71, 0xf6, 0x39, 0x0e, 0x8c,
	0xf3, 0x86, 0x9d, 0x29, 0xea, 0x4f, 0xce, 0xfe, 0x89, 0x70, 0xc7, 0x94, 0xea, 0xb0, 0xb7, 0x61,
	0xde, 0xf7, 0xfb, 0xbe, 0xef, 0xf1, 0x48, 0x13, 0xde, 0x3d, 0x98, 0x8c, 0x71, 0x8b, 0x5a, 0x23,
	0xe3, 0xc6, 0xa0, 0xe7, 0x5e, 0xa1, 0x71, 0xd4, 0xe6, 0xe8, 0x31, 0x8e, 0xc2, 0xe4, 0xb4, 0x25,
	0x11, 0xa5, 0x06, 0x56, 0xfc, 0xce, 0x5f, 0x17, 0x2c, 0x03, 0x27, 0x72, 0x65, 0x3d, 0xe6, 0x41,
	0x79, 0xd1, 0x26, 0xa4, 0x8f, 0x66, 0xa1, 0xe4, 0xe4, 0xc3, 0x42, 0x5c, 0x27, 0xd5, 0x69, 0x7a,
	0x9f, 0x8e, 0x9e, 0xd2, 0xc6, 0x41, 0x4c, 0x48, 0xf4, 0x38, 0xe9, 0x4d, 0x06, 0xfd, 0x46, 0x65,
	0x33, 0xb8, 0x7d, 0x4e, 0x7b, 0xc3, 0x41, 0xbf, 0x71, 0x98, 0x74, 0x49, 0xcd, 0xf0, 0x15, 0x38,
	0xcb, 0x05, 0xc4, 0x4d, 0x1a, 0x32, 0xe8, 0x4f, 0x06, 0x1d, 0x82, 0x73, 0x5c, 0xc2, 0xc8, 0x16,
	0x8d, 0x4e, 0x3e, 0xab, 0xad, 0xca, 0x79, 0x6d, 0xbc, 0x47, 0x92, 0x29, 0xa9, 0x8b, 0x22, 0x73,
	0xe6, 0x37, 0xa1, 0xa5, 0x0e, 0xeb, 0x8d, 0xc3, 0x71, 0x3b, 0xa6, 0x61, 0x2b, 0xba, 0x2f, 0x3c,
	0x26, 0x62, 0xf7, 0x4e, 0xae, 0x49, 0xc4, 0xad, 0x9d, 0xa9, 0x2c, 0x3e, 0xfb, 0xe7, 0x78, 0xa7,
	0xf4, 0xdf, 0x42, 0x47, 0xdc, 0xda, 0x41, 0x96, 0x74, 0x48, 0xe4, 0x40, 0xe4, 0xe0, 0x4b, 0xa8,
	0x75, 0xa0, 0xb6, 0xe2, 0xa4, 0xbb, 0xdb, 0xc1, 0x72, 0xbf, 0x2c, 0x61, 0xbf, 0x02, 0xbb, 0x2d,
	0xfb, 0xc0, 0xfd, 0xf2, 0xe6, 0xf2, 0x85, 0x49, 0xd4, 0xdc, 0x48, 0xda, 0x69, 0x7b, 0x4f, 0x05,
	0xae, 0xc2, 0x91, 0x04, 0x6a, 0xe6, 0x20, 0x7f, 0x53, 0x02, 0x1c, 0xfb, 0x75, 0xe6, 0x79, 0x54,
	0x08, 0xae, 0xbe, 0x03, 0x00, 0x00, 0xff, 0xff, 0x91, 0xa4, 0x76, 0x9e, 0xfe, 0x01, 0x00, 0x00,
}
