// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-guide/channel-guide.proto

package channel_guide // import "golang.52tt.com/protocol/services/channel-guide"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AppoinmentType int32

const (
	AppoinmentType_NoneAppoinment   AppoinmentType = 0
	AppoinmentType_FirstAppoinment  AppoinmentType = 1
	AppoinmentType_SecondAppoinment AppoinmentType = 2
)

var AppoinmentType_name = map[int32]string{
	0: "NoneAppoinment",
	1: "FirstAppoinment",
	2: "SecondAppoinment",
}
var AppoinmentType_value = map[string]int32{
	"NoneAppoinment":   0,
	"FirstAppoinment":  1,
	"SecondAppoinment": 2,
}

func (x AppoinmentType) String() string {
	return proto.EnumName(AppoinmentType_name, int32(x))
}
func (AppoinmentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{0}
}

type JoinStatus int32

const (
	JoinStatus_JoinStatusGoOn      JoinStatus = 0
	JoinStatus_JoinStatusSuc       JoinStatus = 1
	JoinStatus_JoinStatusSucExceed JoinStatus = 2
	JoinStatus_JoinStatusFail      JoinStatus = 3
)

var JoinStatus_name = map[int32]string{
	0: "JoinStatusGoOn",
	1: "JoinStatusSuc",
	2: "JoinStatusSucExceed",
	3: "JoinStatusFail",
}
var JoinStatus_value = map[string]int32{
	"JoinStatusGoOn":      0,
	"JoinStatusSuc":       1,
	"JoinStatusSucExceed": 2,
	"JoinStatusFail":      3,
}

func (x JoinStatus) String() string {
	return proto.EnumName(JoinStatus_name, int32(x))
}
func (JoinStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{1}
}

type GameGroupConf struct {
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GroupId              uint32   `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameGroupConf) Reset()         { *m = GameGroupConf{} }
func (m *GameGroupConf) String() string { return proto.CompactTextString(m) }
func (*GameGroupConf) ProtoMessage()    {}
func (*GameGroupConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{0}
}
func (m *GameGroupConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameGroupConf.Unmarshal(m, b)
}
func (m *GameGroupConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameGroupConf.Marshal(b, m, deterministic)
}
func (dst *GameGroupConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameGroupConf.Merge(dst, src)
}
func (m *GameGroupConf) XXX_Size() int {
	return xxx_messageInfo_GameGroupConf.Size(m)
}
func (m *GameGroupConf) XXX_DiscardUnknown() {
	xxx_messageInfo_GameGroupConf.DiscardUnknown(m)
}

var xxx_messageInfo_GameGroupConf proto.InternalMessageInfo

func (m *GameGroupConf) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameGroupConf) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetGameGroupConfListReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GroupId              uint32   `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameGroupConfListReq) Reset()         { *m = GetGameGroupConfListReq{} }
func (m *GetGameGroupConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameGroupConfListReq) ProtoMessage()    {}
func (*GetGameGroupConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{1}
}
func (m *GetGameGroupConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameGroupConfListReq.Unmarshal(m, b)
}
func (m *GetGameGroupConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameGroupConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameGroupConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameGroupConfListReq.Merge(dst, src)
}
func (m *GetGameGroupConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameGroupConfListReq.Size(m)
}
func (m *GetGameGroupConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameGroupConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameGroupConfListReq proto.InternalMessageInfo

func (m *GetGameGroupConfListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGameGroupConfListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGameGroupConfListReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGameGroupConfListReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetGameGroupConfListRsp struct {
	ConfList             []*GameGroupConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGameGroupConfListRsp) Reset()         { *m = GetGameGroupConfListRsp{} }
func (m *GetGameGroupConfListRsp) String() string { return proto.CompactTextString(m) }
func (*GetGameGroupConfListRsp) ProtoMessage()    {}
func (*GetGameGroupConfListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{2}
}
func (m *GetGameGroupConfListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameGroupConfListRsp.Unmarshal(m, b)
}
func (m *GetGameGroupConfListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameGroupConfListRsp.Marshal(b, m, deterministic)
}
func (dst *GetGameGroupConfListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameGroupConfListRsp.Merge(dst, src)
}
func (m *GetGameGroupConfListRsp) XXX_Size() int {
	return xxx_messageInfo_GetGameGroupConfListRsp.Size(m)
}
func (m *GetGameGroupConfListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameGroupConfListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameGroupConfListRsp proto.InternalMessageInfo

func (m *GetGameGroupConfListRsp) GetConfList() []*GameGroupConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type CreateGameGroupConfReq struct {
	// required
	GameId uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	// required
	GroupId              uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGameGroupConfReq) Reset()         { *m = CreateGameGroupConfReq{} }
func (m *CreateGameGroupConfReq) String() string { return proto.CompactTextString(m) }
func (*CreateGameGroupConfReq) ProtoMessage()    {}
func (*CreateGameGroupConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{3}
}
func (m *CreateGameGroupConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGameGroupConfReq.Unmarshal(m, b)
}
func (m *CreateGameGroupConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGameGroupConfReq.Marshal(b, m, deterministic)
}
func (dst *CreateGameGroupConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGameGroupConfReq.Merge(dst, src)
}
func (m *CreateGameGroupConfReq) XXX_Size() int {
	return xxx_messageInfo_CreateGameGroupConfReq.Size(m)
}
func (m *CreateGameGroupConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGameGroupConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGameGroupConfReq proto.InternalMessageInfo

func (m *CreateGameGroupConfReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CreateGameGroupConfReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type CreateGameGroupConfRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateGameGroupConfRsp) Reset()         { *m = CreateGameGroupConfRsp{} }
func (m *CreateGameGroupConfRsp) String() string { return proto.CompactTextString(m) }
func (*CreateGameGroupConfRsp) ProtoMessage()    {}
func (*CreateGameGroupConfRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{4}
}
func (m *CreateGameGroupConfRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateGameGroupConfRsp.Unmarshal(m, b)
}
func (m *CreateGameGroupConfRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateGameGroupConfRsp.Marshal(b, m, deterministic)
}
func (dst *CreateGameGroupConfRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateGameGroupConfRsp.Merge(dst, src)
}
func (m *CreateGameGroupConfRsp) XXX_Size() int {
	return xxx_messageInfo_CreateGameGroupConfRsp.Size(m)
}
func (m *CreateGameGroupConfRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateGameGroupConfRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateGameGroupConfRsp proto.InternalMessageInfo

type DelGameGroupConfReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GroupId              uint32   `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameGroupConfReq) Reset()         { *m = DelGameGroupConfReq{} }
func (m *DelGameGroupConfReq) String() string { return proto.CompactTextString(m) }
func (*DelGameGroupConfReq) ProtoMessage()    {}
func (*DelGameGroupConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{5}
}
func (m *DelGameGroupConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameGroupConfReq.Unmarshal(m, b)
}
func (m *DelGameGroupConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameGroupConfReq.Marshal(b, m, deterministic)
}
func (dst *DelGameGroupConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameGroupConfReq.Merge(dst, src)
}
func (m *DelGameGroupConfReq) XXX_Size() int {
	return xxx_messageInfo_DelGameGroupConfReq.Size(m)
}
func (m *DelGameGroupConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameGroupConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameGroupConfReq proto.InternalMessageInfo

func (m *DelGameGroupConfReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *DelGameGroupConfReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type DelGameGroupConfRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGameGroupConfRsp) Reset()         { *m = DelGameGroupConfRsp{} }
func (m *DelGameGroupConfRsp) String() string { return proto.CompactTextString(m) }
func (*DelGameGroupConfRsp) ProtoMessage()    {}
func (*DelGameGroupConfRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{6}
}
func (m *DelGameGroupConfRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGameGroupConfRsp.Unmarshal(m, b)
}
func (m *DelGameGroupConfRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGameGroupConfRsp.Marshal(b, m, deterministic)
}
func (dst *DelGameGroupConfRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGameGroupConfRsp.Merge(dst, src)
}
func (m *DelGameGroupConfRsp) XXX_Size() int {
	return xxx_messageInfo_DelGameGroupConfRsp.Size(m)
}
func (m *DelGameGroupConfRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGameGroupConfRsp.DiscardUnknown(m)
}

var xxx_messageInfo_DelGameGroupConfRsp proto.InternalMessageInfo

type GetGameGroupListReq struct {
	// required
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameGroupListReq) Reset()         { *m = GetGameGroupListReq{} }
func (m *GetGameGroupListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameGroupListReq) ProtoMessage()    {}
func (*GetGameGroupListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{7}
}
func (m *GetGameGroupListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameGroupListReq.Unmarshal(m, b)
}
func (m *GetGameGroupListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameGroupListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameGroupListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameGroupListReq.Merge(dst, src)
}
func (m *GetGameGroupListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameGroupListReq.Size(m)
}
func (m *GetGameGroupListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameGroupListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameGroupListReq proto.InternalMessageInfo

func (m *GetGameGroupListReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 游戏群组
type GameGroup struct {
	// 群id
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 群名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 群account
	Account string `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	// 群人数
	MemberNum uint32 `protobuf:"varint,4,opt,name=member_num,json=memberNum,proto3" json:"member_num,omitempty"`
	// 用户是否已加入了群组
	UserJoined bool `protobuf:"varint,5,opt,name=user_joined,json=userJoined,proto3" json:"user_joined,omitempty"`
	// 进入群组是否需要验证
	NeedVerify uint32 `protobuf:"varint,7,opt,name=need_verify,json=needVerify,proto3" json:"need_verify,omitempty"`
	// 群成员列表
	MemberList           []*GameGroup_Member `protobuf:"bytes,6,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GameGroup) Reset()         { *m = GameGroup{} }
func (m *GameGroup) String() string { return proto.CompactTextString(m) }
func (*GameGroup) ProtoMessage()    {}
func (*GameGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{8}
}
func (m *GameGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameGroup.Unmarshal(m, b)
}
func (m *GameGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameGroup.Marshal(b, m, deterministic)
}
func (dst *GameGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameGroup.Merge(dst, src)
}
func (m *GameGroup) XXX_Size() int {
	return xxx_messageInfo_GameGroup.Size(m)
}
func (m *GameGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_GameGroup.DiscardUnknown(m)
}

var xxx_messageInfo_GameGroup proto.InternalMessageInfo

func (m *GameGroup) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GameGroup) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameGroup) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GameGroup) GetMemberNum() uint32 {
	if m != nil {
		return m.MemberNum
	}
	return 0
}

func (m *GameGroup) GetUserJoined() bool {
	if m != nil {
		return m.UserJoined
	}
	return false
}

func (m *GameGroup) GetNeedVerify() uint32 {
	if m != nil {
		return m.NeedVerify
	}
	return 0
}

func (m *GameGroup) GetMemberList() []*GameGroup_Member {
	if m != nil {
		return m.MemberList
	}
	return nil
}

// 游戏群组成员
type GameGroup_Member struct {
	// 群成员 tt id, 用来拼接群头像链接
	Username             string   `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameGroup_Member) Reset()         { *m = GameGroup_Member{} }
func (m *GameGroup_Member) String() string { return proto.CompactTextString(m) }
func (*GameGroup_Member) ProtoMessage()    {}
func (*GameGroup_Member) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{8, 0}
}
func (m *GameGroup_Member) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameGroup_Member.Unmarshal(m, b)
}
func (m *GameGroup_Member) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameGroup_Member.Marshal(b, m, deterministic)
}
func (dst *GameGroup_Member) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameGroup_Member.Merge(dst, src)
}
func (m *GameGroup_Member) XXX_Size() int {
	return xxx_messageInfo_GameGroup_Member.Size(m)
}
func (m *GameGroup_Member) XXX_DiscardUnknown() {
	xxx_messageInfo_GameGroup_Member.DiscardUnknown(m)
}

var xxx_messageInfo_GameGroup_Member proto.InternalMessageInfo

func (m *GameGroup_Member) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

type GetGameGroupListRsp struct {
	GroupIdList          []uint32 `protobuf:"varint,1,rep,packed,name=group_id_list,json=groupIdList,proto3" json:"group_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameGroupListRsp) Reset()         { *m = GetGameGroupListRsp{} }
func (m *GetGameGroupListRsp) String() string { return proto.CompactTextString(m) }
func (*GetGameGroupListRsp) ProtoMessage()    {}
func (*GetGameGroupListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{9}
}
func (m *GetGameGroupListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameGroupListRsp.Unmarshal(m, b)
}
func (m *GetGameGroupListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameGroupListRsp.Marshal(b, m, deterministic)
}
func (dst *GetGameGroupListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameGroupListRsp.Merge(dst, src)
}
func (m *GetGameGroupListRsp) XXX_Size() int {
	return xxx_messageInfo_GetGameGroupListRsp.Size(m)
}
func (m *GetGameGroupListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameGroupListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameGroupListRsp proto.InternalMessageInfo

func (m *GetGameGroupListRsp) GetGroupIdList() []uint32 {
	if m != nil {
		return m.GroupIdList
	}
	return nil
}

type GetBookingGangConfReq struct {
	// required
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBookingGangConfReq) Reset()         { *m = GetBookingGangConfReq{} }
func (m *GetBookingGangConfReq) String() string { return proto.CompactTextString(m) }
func (*GetBookingGangConfReq) ProtoMessage()    {}
func (*GetBookingGangConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{10}
}
func (m *GetBookingGangConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBookingGangConfReq.Unmarshal(m, b)
}
func (m *GetBookingGangConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBookingGangConfReq.Marshal(b, m, deterministic)
}
func (dst *GetBookingGangConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBookingGangConfReq.Merge(dst, src)
}
func (m *GetBookingGangConfReq) XXX_Size() int {
	return xxx_messageInfo_GetBookingGangConfReq.Size(m)
}
func (m *GetBookingGangConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBookingGangConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBookingGangConfReq proto.InternalMessageInfo

func (m *GetBookingGangConfReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetBookingGangConfRsp struct {
	Enabled              bool     `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBookingGangConfRsp) Reset()         { *m = GetBookingGangConfRsp{} }
func (m *GetBookingGangConfRsp) String() string { return proto.CompactTextString(m) }
func (*GetBookingGangConfRsp) ProtoMessage()    {}
func (*GetBookingGangConfRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{11}
}
func (m *GetBookingGangConfRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBookingGangConfRsp.Unmarshal(m, b)
}
func (m *GetBookingGangConfRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBookingGangConfRsp.Marshal(b, m, deterministic)
}
func (dst *GetBookingGangConfRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBookingGangConfRsp.Merge(dst, src)
}
func (m *GetBookingGangConfRsp) XXX_Size() int {
	return xxx_messageInfo_GetBookingGangConfRsp.Size(m)
}
func (m *GetBookingGangConfRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBookingGangConfRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBookingGangConfRsp proto.InternalMessageInfo

func (m *GetBookingGangConfRsp) GetEnabled() bool {
	if m != nil {
		return m.Enabled
	}
	return false
}

type SetBookingGangConfReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Enabled              bool     `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBookingGangConfReq) Reset()         { *m = SetBookingGangConfReq{} }
func (m *SetBookingGangConfReq) String() string { return proto.CompactTextString(m) }
func (*SetBookingGangConfReq) ProtoMessage()    {}
func (*SetBookingGangConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{12}
}
func (m *SetBookingGangConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBookingGangConfReq.Unmarshal(m, b)
}
func (m *SetBookingGangConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBookingGangConfReq.Marshal(b, m, deterministic)
}
func (dst *SetBookingGangConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBookingGangConfReq.Merge(dst, src)
}
func (m *SetBookingGangConfReq) XXX_Size() int {
	return xxx_messageInfo_SetBookingGangConfReq.Size(m)
}
func (m *SetBookingGangConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBookingGangConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBookingGangConfReq proto.InternalMessageInfo

func (m *SetBookingGangConfReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetBookingGangConfReq) GetEnabled() bool {
	if m != nil {
		return m.Enabled
	}
	return false
}

type SetBookingGangConfRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBookingGangConfRsp) Reset()         { *m = SetBookingGangConfRsp{} }
func (m *SetBookingGangConfRsp) String() string { return proto.CompactTextString(m) }
func (*SetBookingGangConfRsp) ProtoMessage()    {}
func (*SetBookingGangConfRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{13}
}
func (m *SetBookingGangConfRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBookingGangConfRsp.Unmarshal(m, b)
}
func (m *SetBookingGangConfRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBookingGangConfRsp.Marshal(b, m, deterministic)
}
func (dst *SetBookingGangConfRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBookingGangConfRsp.Merge(dst, src)
}
func (m *SetBookingGangConfRsp) XXX_Size() int {
	return xxx_messageInfo_SetBookingGangConfRsp.Size(m)
}
func (m *SetBookingGangConfRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBookingGangConfRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBookingGangConfRsp proto.InternalMessageInfo

type GetEnabledBookingGangReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEnabledBookingGangReq) Reset()         { *m = GetEnabledBookingGangReq{} }
func (m *GetEnabledBookingGangReq) String() string { return proto.CompactTextString(m) }
func (*GetEnabledBookingGangReq) ProtoMessage()    {}
func (*GetEnabledBookingGangReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{14}
}
func (m *GetEnabledBookingGangReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEnabledBookingGangReq.Unmarshal(m, b)
}
func (m *GetEnabledBookingGangReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEnabledBookingGangReq.Marshal(b, m, deterministic)
}
func (dst *GetEnabledBookingGangReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEnabledBookingGangReq.Merge(dst, src)
}
func (m *GetEnabledBookingGangReq) XXX_Size() int {
	return xxx_messageInfo_GetEnabledBookingGangReq.Size(m)
}
func (m *GetEnabledBookingGangReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEnabledBookingGangReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEnabledBookingGangReq proto.InternalMessageInfo

type GetEnabledBookingGangRsp struct {
	TabIdList            []uint32 `protobuf:"varint,1,rep,packed,name=tab_id_list,json=tabIdList,proto3" json:"tab_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEnabledBookingGangRsp) Reset()         { *m = GetEnabledBookingGangRsp{} }
func (m *GetEnabledBookingGangRsp) String() string { return proto.CompactTextString(m) }
func (*GetEnabledBookingGangRsp) ProtoMessage()    {}
func (*GetEnabledBookingGangRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{15}
}
func (m *GetEnabledBookingGangRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEnabledBookingGangRsp.Unmarshal(m, b)
}
func (m *GetEnabledBookingGangRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEnabledBookingGangRsp.Marshal(b, m, deterministic)
}
func (dst *GetEnabledBookingGangRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEnabledBookingGangRsp.Merge(dst, src)
}
func (m *GetEnabledBookingGangRsp) XXX_Size() int {
	return xxx_messageInfo_GetEnabledBookingGangRsp.Size(m)
}
func (m *GetEnabledBookingGangRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEnabledBookingGangRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEnabledBookingGangRsp proto.InternalMessageInfo

func (m *GetEnabledBookingGangRsp) GetTabIdList() []uint32 {
	if m != nil {
		return m.TabIdList
	}
	return nil
}

type GetGangConfReq struct {
	// required
	GameId uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	// required
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangConfReq) Reset()         { *m = GetGangConfReq{} }
func (m *GetGangConfReq) String() string { return proto.CompactTextString(m) }
func (*GetGangConfReq) ProtoMessage()    {}
func (*GetGangConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{16}
}
func (m *GetGangConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangConfReq.Unmarshal(m, b)
}
func (m *GetGangConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangConfReq.Marshal(b, m, deterministic)
}
func (dst *GetGangConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangConfReq.Merge(dst, src)
}
func (m *GetGangConfReq) XXX_Size() int {
	return xxx_messageInfo_GetGangConfReq.Size(m)
}
func (m *GetGangConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangConfReq proto.InternalMessageInfo

func (m *GetGangConfReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGangConfReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGangConfRsp struct {
	// 游戏群组信息
	GroupList []*GameGroup `protobuf:"bytes,1,rep,name=group_list,json=groupList,proto3" json:"group_list,omitempty"`
	// 是否开启对应游戏预约开黑配置
	EnabledBooking bool `protobuf:"varint,2,opt,name=enabled_booking,json=enabledBooking,proto3" json:"enabled_booking,omitempty"`
	// 预约开黑时间选项(秒)
	BookingDurations []int64 `protobuf:"varint,3,rep,packed,name=booking_durations,json=bookingDurations,proto3" json:"booking_durations,omitempty"`
	// 预约开黑倒计时(秒)
	BookingCountdown uint32 `protobuf:"varint,4,opt,name=booking_countdown,json=bookingCountdown,proto3" json:"booking_countdown,omitempty"`
	// 预约开黑状态   对应 AppointmentType
	BookingState uint32 `protobuf:"varint,5,opt,name=booking_state,json=bookingState,proto3" json:"booking_state,omitempty"`
	// 用户所在群组 account
	MyGroupAccList       []string `protobuf:"bytes,6,rep,name=my_group_acc_list,json=myGroupAccList,proto3" json:"my_group_acc_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangConfRsp) Reset()         { *m = GetGangConfRsp{} }
func (m *GetGangConfRsp) String() string { return proto.CompactTextString(m) }
func (*GetGangConfRsp) ProtoMessage()    {}
func (*GetGangConfRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{17}
}
func (m *GetGangConfRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangConfRsp.Unmarshal(m, b)
}
func (m *GetGangConfRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangConfRsp.Marshal(b, m, deterministic)
}
func (dst *GetGangConfRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangConfRsp.Merge(dst, src)
}
func (m *GetGangConfRsp) XXX_Size() int {
	return xxx_messageInfo_GetGangConfRsp.Size(m)
}
func (m *GetGangConfRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangConfRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangConfRsp proto.InternalMessageInfo

func (m *GetGangConfRsp) GetGroupList() []*GameGroup {
	if m != nil {
		return m.GroupList
	}
	return nil
}

func (m *GetGangConfRsp) GetEnabledBooking() bool {
	if m != nil {
		return m.EnabledBooking
	}
	return false
}

func (m *GetGangConfRsp) GetBookingDurations() []int64 {
	if m != nil {
		return m.BookingDurations
	}
	return nil
}

func (m *GetGangConfRsp) GetBookingCountdown() uint32 {
	if m != nil {
		return m.BookingCountdown
	}
	return 0
}

func (m *GetGangConfRsp) GetBookingState() uint32 {
	if m != nil {
		return m.BookingState
	}
	return 0
}

func (m *GetGangConfRsp) GetMyGroupAccList() []string {
	if m != nil {
		return m.MyGroupAccList
	}
	return nil
}

type GetGangDynamicConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangDynamicConfReq) Reset()         { *m = GetGangDynamicConfReq{} }
func (m *GetGangDynamicConfReq) String() string { return proto.CompactTextString(m) }
func (*GetGangDynamicConfReq) ProtoMessage()    {}
func (*GetGangDynamicConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{18}
}
func (m *GetGangDynamicConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangDynamicConfReq.Unmarshal(m, b)
}
func (m *GetGangDynamicConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangDynamicConfReq.Marshal(b, m, deterministic)
}
func (dst *GetGangDynamicConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangDynamicConfReq.Merge(dst, src)
}
func (m *GetGangDynamicConfReq) XXX_Size() int {
	return xxx_messageInfo_GetGangDynamicConfReq.Size(m)
}
func (m *GetGangDynamicConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangDynamicConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangDynamicConfReq proto.InternalMessageInfo

type GetGangDynamicConfRsp struct {
	// 等待用户确认进房倒计时(秒)
	WaitConfirmSec uint32 `protobuf:"varint,1,opt,name=wait_confirm_sec,json=waitConfirmSec,proto3" json:"wait_confirm_sec,omitempty"`
	// 用户等待进房倒计时(秒)
	WaitEnterSec         uint32   `protobuf:"varint,2,opt,name=wait_enter_sec,json=waitEnterSec,proto3" json:"wait_enter_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGangDynamicConfRsp) Reset()         { *m = GetGangDynamicConfRsp{} }
func (m *GetGangDynamicConfRsp) String() string { return proto.CompactTextString(m) }
func (*GetGangDynamicConfRsp) ProtoMessage()    {}
func (*GetGangDynamicConfRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{19}
}
func (m *GetGangDynamicConfRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangDynamicConfRsp.Unmarshal(m, b)
}
func (m *GetGangDynamicConfRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangDynamicConfRsp.Marshal(b, m, deterministic)
}
func (dst *GetGangDynamicConfRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangDynamicConfRsp.Merge(dst, src)
}
func (m *GetGangDynamicConfRsp) XXX_Size() int {
	return xxx_messageInfo_GetGangDynamicConfRsp.Size(m)
}
func (m *GetGangDynamicConfRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangDynamicConfRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangDynamicConfRsp proto.InternalMessageInfo

func (m *GetGangDynamicConfRsp) GetWaitConfirmSec() uint32 {
	if m != nil {
		return m.WaitConfirmSec
	}
	return 0
}

func (m *GetGangDynamicConfRsp) GetWaitEnterSec() uint32 {
	if m != nil {
		return m.WaitEnterSec
	}
	return 0
}

type CloseGameGroupGuideReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CloseGameGroupGuideReq) Reset()         { *m = CloseGameGroupGuideReq{} }
func (m *CloseGameGroupGuideReq) String() string { return proto.CompactTextString(m) }
func (*CloseGameGroupGuideReq) ProtoMessage()    {}
func (*CloseGameGroupGuideReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{20}
}
func (m *CloseGameGroupGuideReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloseGameGroupGuideReq.Unmarshal(m, b)
}
func (m *CloseGameGroupGuideReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloseGameGroupGuideReq.Marshal(b, m, deterministic)
}
func (dst *CloseGameGroupGuideReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseGameGroupGuideReq.Merge(dst, src)
}
func (m *CloseGameGroupGuideReq) XXX_Size() int {
	return xxx_messageInfo_CloseGameGroupGuideReq.Size(m)
}
func (m *CloseGameGroupGuideReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseGameGroupGuideReq.DiscardUnknown(m)
}

var xxx_messageInfo_CloseGameGroupGuideReq proto.InternalMessageInfo

func (m *CloseGameGroupGuideReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CloseGameGroupGuideReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type CloseGameGroupGuideRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CloseGameGroupGuideRsp) Reset()         { *m = CloseGameGroupGuideRsp{} }
func (m *CloseGameGroupGuideRsp) String() string { return proto.CompactTextString(m) }
func (*CloseGameGroupGuideRsp) ProtoMessage()    {}
func (*CloseGameGroupGuideRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{21}
}
func (m *CloseGameGroupGuideRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloseGameGroupGuideRsp.Unmarshal(m, b)
}
func (m *CloseGameGroupGuideRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloseGameGroupGuideRsp.Marshal(b, m, deterministic)
}
func (dst *CloseGameGroupGuideRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseGameGroupGuideRsp.Merge(dst, src)
}
func (m *CloseGameGroupGuideRsp) XXX_Size() int {
	return xxx_messageInfo_CloseGameGroupGuideRsp.Size(m)
}
func (m *CloseGameGroupGuideRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseGameGroupGuideRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CloseGameGroupGuideRsp proto.InternalMessageInfo

type GameCardOpt struct {
	OptName              string   `protobuf:"bytes,1,opt,name=opt_name,json=optName,proto3" json:"opt_name,omitempty"`
	ValueList            []string `protobuf:"bytes,2,rep,name=value_list,json=valueList,proto3" json:"value_list,omitempty"`
	OptId                uint32   `protobuf:"varint,3,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameCardOpt) Reset()         { *m = GameCardOpt{} }
func (m *GameCardOpt) String() string { return proto.CompactTextString(m) }
func (*GameCardOpt) ProtoMessage()    {}
func (*GameCardOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{22}
}
func (m *GameCardOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCardOpt.Unmarshal(m, b)
}
func (m *GameCardOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCardOpt.Marshal(b, m, deterministic)
}
func (dst *GameCardOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCardOpt.Merge(dst, src)
}
func (m *GameCardOpt) XXX_Size() int {
	return xxx_messageInfo_GameCardOpt.Size(m)
}
func (m *GameCardOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCardOpt.DiscardUnknown(m)
}

var xxx_messageInfo_GameCardOpt proto.InternalMessageInfo

func (m *GameCardOpt) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *GameCardOpt) GetValueList() []string {
	if m != nil {
		return m.ValueList
	}
	return nil
}

func (m *GameCardOpt) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

type AppoinmentReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AppoinmentType       AppoinmentType `protobuf:"varint,2,opt,name=appoinment_type,json=appoinmentType,proto3,enum=channel_guide.AppoinmentType" json:"appoinment_type,omitempty"`
	OptList              []*GameCardOpt `protobuf:"bytes,3,rep,name=opt_list,json=optList,proto3" json:"opt_list,omitempty"`
	TimeDuration         uint32         `protobuf:"varint,4,opt,name=time_duration,json=timeDuration,proto3" json:"time_duration,omitempty"`
	GameTabId            uint32         `protobuf:"varint,5,opt,name=game_tab_id,json=gameTabId,proto3" json:"game_tab_id,omitempty"`
	GameTabName          string         `protobuf:"bytes,6,opt,name=game_tab_name,json=gameTabName,proto3" json:"game_tab_name,omitempty"`
	IsSet                bool           `protobuf:"varint,7,opt,name=is_set,json=isSet,proto3" json:"is_set,omitempty"`
	OverTime             int64          `protobuf:"varint,8,opt,name=over_time,json=overTime,proto3" json:"over_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AppoinmentReq) Reset()         { *m = AppoinmentReq{} }
func (m *AppoinmentReq) String() string { return proto.CompactTextString(m) }
func (*AppoinmentReq) ProtoMessage()    {}
func (*AppoinmentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{23}
}
func (m *AppoinmentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppoinmentReq.Unmarshal(m, b)
}
func (m *AppoinmentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppoinmentReq.Marshal(b, m, deterministic)
}
func (dst *AppoinmentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppoinmentReq.Merge(dst, src)
}
func (m *AppoinmentReq) XXX_Size() int {
	return xxx_messageInfo_AppoinmentReq.Size(m)
}
func (m *AppoinmentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AppoinmentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AppoinmentReq proto.InternalMessageInfo

func (m *AppoinmentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AppoinmentReq) GetAppoinmentType() AppoinmentType {
	if m != nil {
		return m.AppoinmentType
	}
	return AppoinmentType_NoneAppoinment
}

func (m *AppoinmentReq) GetOptList() []*GameCardOpt {
	if m != nil {
		return m.OptList
	}
	return nil
}

func (m *AppoinmentReq) GetTimeDuration() uint32 {
	if m != nil {
		return m.TimeDuration
	}
	return 0
}

func (m *AppoinmentReq) GetGameTabId() uint32 {
	if m != nil {
		return m.GameTabId
	}
	return 0
}

func (m *AppoinmentReq) GetGameTabName() string {
	if m != nil {
		return m.GameTabName
	}
	return ""
}

func (m *AppoinmentReq) GetIsSet() bool {
	if m != nil {
		return m.IsSet
	}
	return false
}

func (m *AppoinmentReq) GetOverTime() int64 {
	if m != nil {
		return m.OverTime
	}
	return 0
}

type AppoinmentRsp struct {
	AppoinmentType       AppoinmentType `protobuf:"varint,1,opt,name=appoinment_type,json=appoinmentType,proto3,enum=channel_guide.AppoinmentType" json:"appoinment_type,omitempty"`
	SelectAppoinmentTime []int64        `protobuf:"varint,2,rep,packed,name=select_appoinment_time,json=selectAppoinmentTime,proto3" json:"select_appoinment_time,omitempty"`
	OverTime             int64          `protobuf:"varint,3,opt,name=over_time,json=overTime,proto3" json:"over_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AppoinmentRsp) Reset()         { *m = AppoinmentRsp{} }
func (m *AppoinmentRsp) String() string { return proto.CompactTextString(m) }
func (*AppoinmentRsp) ProtoMessage()    {}
func (*AppoinmentRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{24}
}
func (m *AppoinmentRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppoinmentRsp.Unmarshal(m, b)
}
func (m *AppoinmentRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppoinmentRsp.Marshal(b, m, deterministic)
}
func (dst *AppoinmentRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppoinmentRsp.Merge(dst, src)
}
func (m *AppoinmentRsp) XXX_Size() int {
	return xxx_messageInfo_AppoinmentRsp.Size(m)
}
func (m *AppoinmentRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AppoinmentRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AppoinmentRsp proto.InternalMessageInfo

func (m *AppoinmentRsp) GetAppoinmentType() AppoinmentType {
	if m != nil {
		return m.AppoinmentType
	}
	return AppoinmentType_NoneAppoinment
}

func (m *AppoinmentRsp) GetSelectAppoinmentTime() []int64 {
	if m != nil {
		return m.SelectAppoinmentTime
	}
	return nil
}

func (m *AppoinmentRsp) GetOverTime() int64 {
	if m != nil {
		return m.OverTime
	}
	return 0
}

type TeamNofifyInfo struct {
	TeamUids             []uint32 `protobuf:"varint,1,rep,packed,name=team_uids,json=teamUids,proto3" json:"team_uids,omitempty"`
	GameTabId            uint32   `protobuf:"varint,2,opt,name=game_tab_id,json=gameTabId,proto3" json:"game_tab_id,omitempty"`
	GameTabName          string   `protobuf:"bytes,3,opt,name=game_tab_name,json=gameTabName,proto3" json:"game_tab_name,omitempty"`
	Teamid               string   `protobuf:"bytes,4,opt,name=teamid,proto3" json:"teamid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TeamNofifyInfo) Reset()         { *m = TeamNofifyInfo{} }
func (m *TeamNofifyInfo) String() string { return proto.CompactTextString(m) }
func (*TeamNofifyInfo) ProtoMessage()    {}
func (*TeamNofifyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{25}
}
func (m *TeamNofifyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamNofifyInfo.Unmarshal(m, b)
}
func (m *TeamNofifyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamNofifyInfo.Marshal(b, m, deterministic)
}
func (dst *TeamNofifyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamNofifyInfo.Merge(dst, src)
}
func (m *TeamNofifyInfo) XXX_Size() int {
	return xxx_messageInfo_TeamNofifyInfo.Size(m)
}
func (m *TeamNofifyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamNofifyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TeamNofifyInfo proto.InternalMessageInfo

func (m *TeamNofifyInfo) GetTeamUids() []uint32 {
	if m != nil {
		return m.TeamUids
	}
	return nil
}

func (m *TeamNofifyInfo) GetGameTabId() uint32 {
	if m != nil {
		return m.GameTabId
	}
	return 0
}

func (m *TeamNofifyInfo) GetGameTabName() string {
	if m != nil {
		return m.GameTabName
	}
	return ""
}

func (m *TeamNofifyInfo) GetTeamid() string {
	if m != nil {
		return m.Teamid
	}
	return ""
}

type SetTeamNotifyReq struct {
	NotifyInfo           []*TeamNofifyInfo `protobuf:"bytes,1,rep,name=notify_info,json=notifyInfo,proto3" json:"notify_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SetTeamNotifyReq) Reset()         { *m = SetTeamNotifyReq{} }
func (m *SetTeamNotifyReq) String() string { return proto.CompactTextString(m) }
func (*SetTeamNotifyReq) ProtoMessage()    {}
func (*SetTeamNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{26}
}
func (m *SetTeamNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTeamNotifyReq.Unmarshal(m, b)
}
func (m *SetTeamNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTeamNotifyReq.Marshal(b, m, deterministic)
}
func (dst *SetTeamNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTeamNotifyReq.Merge(dst, src)
}
func (m *SetTeamNotifyReq) XXX_Size() int {
	return xxx_messageInfo_SetTeamNotifyReq.Size(m)
}
func (m *SetTeamNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTeamNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTeamNotifyReq proto.InternalMessageInfo

func (m *SetTeamNotifyReq) GetNotifyInfo() []*TeamNofifyInfo {
	if m != nil {
		return m.NotifyInfo
	}
	return nil
}

type TeamNotifyRspInfo struct {
	OriTeamid            string   `protobuf:"bytes,1,opt,name=ori_teamid,json=oriTeamid,proto3" json:"ori_teamid,omitempty"`
	DstTeamid            string   `protobuf:"bytes,2,opt,name=dst_teamid,json=dstTeamid,proto3" json:"dst_teamid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TeamNotifyRspInfo) Reset()         { *m = TeamNotifyRspInfo{} }
func (m *TeamNotifyRspInfo) String() string { return proto.CompactTextString(m) }
func (*TeamNotifyRspInfo) ProtoMessage()    {}
func (*TeamNotifyRspInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{27}
}
func (m *TeamNotifyRspInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamNotifyRspInfo.Unmarshal(m, b)
}
func (m *TeamNotifyRspInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamNotifyRspInfo.Marshal(b, m, deterministic)
}
func (dst *TeamNotifyRspInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamNotifyRspInfo.Merge(dst, src)
}
func (m *TeamNotifyRspInfo) XXX_Size() int {
	return xxx_messageInfo_TeamNotifyRspInfo.Size(m)
}
func (m *TeamNotifyRspInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamNotifyRspInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TeamNotifyRspInfo proto.InternalMessageInfo

func (m *TeamNotifyRspInfo) GetOriTeamid() string {
	if m != nil {
		return m.OriTeamid
	}
	return ""
}

func (m *TeamNotifyRspInfo) GetDstTeamid() string {
	if m != nil {
		return m.DstTeamid
	}
	return ""
}

type SetTeamNotifyRsp struct {
	RspInfo              []*TeamNotifyRspInfo `protobuf:"bytes,1,rep,name=rsp_info,json=rspInfo,proto3" json:"rsp_info,omitempty"`
	WaitConfirmSec       uint32               `protobuf:"varint,2,opt,name=wait_confirm_sec,json=waitConfirmSec,proto3" json:"wait_confirm_sec,omitempty"`
	WaitEnterSec         uint32               `protobuf:"varint,3,opt,name=wait_enter_sec,json=waitEnterSec,proto3" json:"wait_enter_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SetTeamNotifyRsp) Reset()         { *m = SetTeamNotifyRsp{} }
func (m *SetTeamNotifyRsp) String() string { return proto.CompactTextString(m) }
func (*SetTeamNotifyRsp) ProtoMessage()    {}
func (*SetTeamNotifyRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{28}
}
func (m *SetTeamNotifyRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTeamNotifyRsp.Unmarshal(m, b)
}
func (m *SetTeamNotifyRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTeamNotifyRsp.Marshal(b, m, deterministic)
}
func (dst *SetTeamNotifyRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTeamNotifyRsp.Merge(dst, src)
}
func (m *SetTeamNotifyRsp) XXX_Size() int {
	return xxx_messageInfo_SetTeamNotifyRsp.Size(m)
}
func (m *SetTeamNotifyRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTeamNotifyRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTeamNotifyRsp proto.InternalMessageInfo

func (m *SetTeamNotifyRsp) GetRspInfo() []*TeamNotifyRspInfo {
	if m != nil {
		return m.RspInfo
	}
	return nil
}

func (m *SetTeamNotifyRsp) GetWaitConfirmSec() uint32 {
	if m != nil {
		return m.WaitConfirmSec
	}
	return 0
}

func (m *SetTeamNotifyRsp) GetWaitEnterSec() uint32 {
	if m != nil {
		return m.WaitEnterSec
	}
	return 0
}

type JoinCarTeamReq struct {
	GameTabId            uint32   `protobuf:"varint,1,opt,name=game_tab_id,json=gameTabId,proto3" json:"game_tab_id,omitempty"`
	GameTabName          string   `protobuf:"bytes,2,opt,name=game_tab_name,json=gameTabName,proto3" json:"game_tab_name,omitempty"`
	Teamid               string   `protobuf:"bytes,3,opt,name=teamid,proto3" json:"teamid,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinCarTeamReq) Reset()         { *m = JoinCarTeamReq{} }
func (m *JoinCarTeamReq) String() string { return proto.CompactTextString(m) }
func (*JoinCarTeamReq) ProtoMessage()    {}
func (*JoinCarTeamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{29}
}
func (m *JoinCarTeamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinCarTeamReq.Unmarshal(m, b)
}
func (m *JoinCarTeamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinCarTeamReq.Marshal(b, m, deterministic)
}
func (dst *JoinCarTeamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinCarTeamReq.Merge(dst, src)
}
func (m *JoinCarTeamReq) XXX_Size() int {
	return xxx_messageInfo_JoinCarTeamReq.Size(m)
}
func (m *JoinCarTeamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinCarTeamReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinCarTeamReq proto.InternalMessageInfo

func (m *JoinCarTeamReq) GetGameTabId() uint32 {
	if m != nil {
		return m.GameTabId
	}
	return 0
}

func (m *JoinCarTeamReq) GetGameTabName() string {
	if m != nil {
		return m.GameTabName
	}
	return ""
}

func (m *JoinCarTeamReq) GetTeamid() string {
	if m != nil {
		return m.Teamid
	}
	return ""
}

func (m *JoinCarTeamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type JoinCarTeamRsp struct {
	JoinStatus           JoinStatus `protobuf:"varint,1,opt,name=join_status,json=joinStatus,proto3,enum=channel_guide.JoinStatus" json:"join_status,omitempty"`
	Channelid            uint32     `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid,omitempty"`
	TeamSucUids          []uint32   `protobuf:"varint,3,rep,packed,name=team_suc_uids,json=teamSucUids,proto3" json:"team_suc_uids,omitempty"`
	TeamUids             []uint32   `protobuf:"varint,4,rep,packed,name=team_uids,json=teamUids,proto3" json:"team_uids,omitempty"`
	OriTeamid            string     `protobuf:"bytes,5,opt,name=ori_teamid,json=oriTeamid,proto3" json:"ori_teamid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *JoinCarTeamRsp) Reset()         { *m = JoinCarTeamRsp{} }
func (m *JoinCarTeamRsp) String() string { return proto.CompactTextString(m) }
func (*JoinCarTeamRsp) ProtoMessage()    {}
func (*JoinCarTeamRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{30}
}
func (m *JoinCarTeamRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinCarTeamRsp.Unmarshal(m, b)
}
func (m *JoinCarTeamRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinCarTeamRsp.Marshal(b, m, deterministic)
}
func (dst *JoinCarTeamRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinCarTeamRsp.Merge(dst, src)
}
func (m *JoinCarTeamRsp) XXX_Size() int {
	return xxx_messageInfo_JoinCarTeamRsp.Size(m)
}
func (m *JoinCarTeamRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinCarTeamRsp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinCarTeamRsp proto.InternalMessageInfo

func (m *JoinCarTeamRsp) GetJoinStatus() JoinStatus {
	if m != nil {
		return m.JoinStatus
	}
	return JoinStatus_JoinStatusGoOn
}

func (m *JoinCarTeamRsp) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *JoinCarTeamRsp) GetTeamSucUids() []uint32 {
	if m != nil {
		return m.TeamSucUids
	}
	return nil
}

func (m *JoinCarTeamRsp) GetTeamUids() []uint32 {
	if m != nil {
		return m.TeamUids
	}
	return nil
}

func (m *JoinCarTeamRsp) GetOriTeamid() string {
	if m != nil {
		return m.OriTeamid
	}
	return ""
}

type TeamingInfo struct {
	GameTabId            uint32   `protobuf:"varint,1,opt,name=game_tab_id,json=gameTabId,proto3" json:"game_tab_id,omitempty"`
	GameTabName          string   `protobuf:"bytes,2,opt,name=game_tab_name,json=gameTabName,proto3" json:"game_tab_name,omitempty"`
	Teamid               string   `protobuf:"bytes,3,opt,name=teamid,proto3" json:"teamid,omitempty"`
	SucUids              []uint32 `protobuf:"varint,4,rep,packed,name=suc_uids,json=sucUids,proto3" json:"suc_uids,omitempty"`
	AllUids              []uint32 `protobuf:"varint,5,rep,packed,name=all_uids,json=allUids,proto3" json:"all_uids,omitempty"`
	OriTeamid            string   `protobuf:"bytes,6,opt,name=ori_teamid,json=oriTeamid,proto3" json:"ori_teamid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TeamingInfo) Reset()         { *m = TeamingInfo{} }
func (m *TeamingInfo) String() string { return proto.CompactTextString(m) }
func (*TeamingInfo) ProtoMessage()    {}
func (*TeamingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{31}
}
func (m *TeamingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamingInfo.Unmarshal(m, b)
}
func (m *TeamingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamingInfo.Marshal(b, m, deterministic)
}
func (dst *TeamingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamingInfo.Merge(dst, src)
}
func (m *TeamingInfo) XXX_Size() int {
	return xxx_messageInfo_TeamingInfo.Size(m)
}
func (m *TeamingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TeamingInfo proto.InternalMessageInfo

func (m *TeamingInfo) GetGameTabId() uint32 {
	if m != nil {
		return m.GameTabId
	}
	return 0
}

func (m *TeamingInfo) GetGameTabName() string {
	if m != nil {
		return m.GameTabName
	}
	return ""
}

func (m *TeamingInfo) GetTeamid() string {
	if m != nil {
		return m.Teamid
	}
	return ""
}

func (m *TeamingInfo) GetSucUids() []uint32 {
	if m != nil {
		return m.SucUids
	}
	return nil
}

func (m *TeamingInfo) GetAllUids() []uint32 {
	if m != nil {
		return m.AllUids
	}
	return nil
}

func (m *TeamingInfo) GetOriTeamid() string {
	if m != nil {
		return m.OriTeamid
	}
	return ""
}

type GetTeamingReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTeamingReq) Reset()         { *m = GetTeamingReq{} }
func (m *GetTeamingReq) String() string { return proto.CompactTextString(m) }
func (*GetTeamingReq) ProtoMessage()    {}
func (*GetTeamingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{32}
}
func (m *GetTeamingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTeamingReq.Unmarshal(m, b)
}
func (m *GetTeamingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTeamingReq.Marshal(b, m, deterministic)
}
func (dst *GetTeamingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTeamingReq.Merge(dst, src)
}
func (m *GetTeamingReq) XXX_Size() int {
	return xxx_messageInfo_GetTeamingReq.Size(m)
}
func (m *GetTeamingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTeamingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTeamingReq proto.InternalMessageInfo

type GetTeamingRsp struct {
	Infos                []*TeamingInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetTeamingRsp) Reset()         { *m = GetTeamingRsp{} }
func (m *GetTeamingRsp) String() string { return proto.CompactTextString(m) }
func (*GetTeamingRsp) ProtoMessage()    {}
func (*GetTeamingRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{33}
}
func (m *GetTeamingRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTeamingRsp.Unmarshal(m, b)
}
func (m *GetTeamingRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTeamingRsp.Marshal(b, m, deterministic)
}
func (dst *GetTeamingRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTeamingRsp.Merge(dst, src)
}
func (m *GetTeamingRsp) XXX_Size() int {
	return xxx_messageInfo_GetTeamingRsp.Size(m)
}
func (m *GetTeamingRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTeamingRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTeamingRsp proto.InternalMessageInfo

func (m *GetTeamingRsp) GetInfos() []*TeamingInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type SetTeamChannelReq struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid,omitempty"`
	Teamid               string   `protobuf:"bytes,2,opt,name=teamid,proto3" json:"teamid,omitempty"`
	SucUids              []uint32 `protobuf:"varint,3,rep,packed,name=suc_uids,json=sucUids,proto3" json:"suc_uids,omitempty"`
	GameTabid            uint32   `protobuf:"varint,4,opt,name=game_tabid,json=gameTabid,proto3" json:"game_tabid,omitempty"`
	IsExceed             bool     `protobuf:"varint,5,opt,name=is_exceed,json=isExceed,proto3" json:"is_exceed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTeamChannelReq) Reset()         { *m = SetTeamChannelReq{} }
func (m *SetTeamChannelReq) String() string { return proto.CompactTextString(m) }
func (*SetTeamChannelReq) ProtoMessage()    {}
func (*SetTeamChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{34}
}
func (m *SetTeamChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTeamChannelReq.Unmarshal(m, b)
}
func (m *SetTeamChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTeamChannelReq.Marshal(b, m, deterministic)
}
func (dst *SetTeamChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTeamChannelReq.Merge(dst, src)
}
func (m *SetTeamChannelReq) XXX_Size() int {
	return xxx_messageInfo_SetTeamChannelReq.Size(m)
}
func (m *SetTeamChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTeamChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTeamChannelReq proto.InternalMessageInfo

func (m *SetTeamChannelReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *SetTeamChannelReq) GetTeamid() string {
	if m != nil {
		return m.Teamid
	}
	return ""
}

func (m *SetTeamChannelReq) GetSucUids() []uint32 {
	if m != nil {
		return m.SucUids
	}
	return nil
}

func (m *SetTeamChannelReq) GetGameTabid() uint32 {
	if m != nil {
		return m.GameTabid
	}
	return 0
}

func (m *SetTeamChannelReq) GetIsExceed() bool {
	if m != nil {
		return m.IsExceed
	}
	return false
}

type SetTeamChannelRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTeamChannelRsp) Reset()         { *m = SetTeamChannelRsp{} }
func (m *SetTeamChannelRsp) String() string { return proto.CompactTextString(m) }
func (*SetTeamChannelRsp) ProtoMessage()    {}
func (*SetTeamChannelRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{35}
}
func (m *SetTeamChannelRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTeamChannelRsp.Unmarshal(m, b)
}
func (m *SetTeamChannelRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTeamChannelRsp.Marshal(b, m, deterministic)
}
func (dst *SetTeamChannelRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTeamChannelRsp.Merge(dst, src)
}
func (m *SetTeamChannelRsp) XXX_Size() int {
	return xxx_messageInfo_SetTeamChannelRsp.Size(m)
}
func (m *SetTeamChannelRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTeamChannelRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTeamChannelRsp proto.InternalMessageInfo

type GetTeamFightGuideReq struct {
	IsMaster             bool     `protobuf:"varint,1,opt,name=is_master,json=isMaster,proto3" json:"is_master,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTeamFightGuideReq) Reset()         { *m = GetTeamFightGuideReq{} }
func (m *GetTeamFightGuideReq) String() string { return proto.CompactTextString(m) }
func (*GetTeamFightGuideReq) ProtoMessage()    {}
func (*GetTeamFightGuideReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{36}
}
func (m *GetTeamFightGuideReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTeamFightGuideReq.Unmarshal(m, b)
}
func (m *GetTeamFightGuideReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTeamFightGuideReq.Marshal(b, m, deterministic)
}
func (dst *GetTeamFightGuideReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTeamFightGuideReq.Merge(dst, src)
}
func (m *GetTeamFightGuideReq) XXX_Size() int {
	return xxx_messageInfo_GetTeamFightGuideReq.Size(m)
}
func (m *GetTeamFightGuideReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTeamFightGuideReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTeamFightGuideReq proto.InternalMessageInfo

func (m *GetTeamFightGuideReq) GetIsMaster() bool {
	if m != nil {
		return m.IsMaster
	}
	return false
}

type GetTeamFightGuideRsp struct {
	NewestVersion        uint32   `protobuf:"varint,1,opt,name=newest_version,json=newestVersion,proto3" json:"newest_version,omitempty"`
	Urls                 []string `protobuf:"bytes,2,rep,name=urls,proto3" json:"urls,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTeamFightGuideRsp) Reset()         { *m = GetTeamFightGuideRsp{} }
func (m *GetTeamFightGuideRsp) String() string { return proto.CompactTextString(m) }
func (*GetTeamFightGuideRsp) ProtoMessage()    {}
func (*GetTeamFightGuideRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_guide_c6065a379b73a81e, []int{37}
}
func (m *GetTeamFightGuideRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTeamFightGuideRsp.Unmarshal(m, b)
}
func (m *GetTeamFightGuideRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTeamFightGuideRsp.Marshal(b, m, deterministic)
}
func (dst *GetTeamFightGuideRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTeamFightGuideRsp.Merge(dst, src)
}
func (m *GetTeamFightGuideRsp) XXX_Size() int {
	return xxx_messageInfo_GetTeamFightGuideRsp.Size(m)
}
func (m *GetTeamFightGuideRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTeamFightGuideRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTeamFightGuideRsp proto.InternalMessageInfo

func (m *GetTeamFightGuideRsp) GetNewestVersion() uint32 {
	if m != nil {
		return m.NewestVersion
	}
	return 0
}

func (m *GetTeamFightGuideRsp) GetUrls() []string {
	if m != nil {
		return m.Urls
	}
	return nil
}

func init() {
	proto.RegisterType((*GameGroupConf)(nil), "channel_guide.GameGroupConf")
	proto.RegisterType((*GetGameGroupConfListReq)(nil), "channel_guide.GetGameGroupConfListReq")
	proto.RegisterType((*GetGameGroupConfListRsp)(nil), "channel_guide.GetGameGroupConfListRsp")
	proto.RegisterType((*CreateGameGroupConfReq)(nil), "channel_guide.CreateGameGroupConfReq")
	proto.RegisterType((*CreateGameGroupConfRsp)(nil), "channel_guide.CreateGameGroupConfRsp")
	proto.RegisterType((*DelGameGroupConfReq)(nil), "channel_guide.DelGameGroupConfReq")
	proto.RegisterType((*DelGameGroupConfRsp)(nil), "channel_guide.DelGameGroupConfRsp")
	proto.RegisterType((*GetGameGroupListReq)(nil), "channel_guide.GetGameGroupListReq")
	proto.RegisterType((*GameGroup)(nil), "channel_guide.GameGroup")
	proto.RegisterType((*GameGroup_Member)(nil), "channel_guide.GameGroup.Member")
	proto.RegisterType((*GetGameGroupListRsp)(nil), "channel_guide.GetGameGroupListRsp")
	proto.RegisterType((*GetBookingGangConfReq)(nil), "channel_guide.GetBookingGangConfReq")
	proto.RegisterType((*GetBookingGangConfRsp)(nil), "channel_guide.GetBookingGangConfRsp")
	proto.RegisterType((*SetBookingGangConfReq)(nil), "channel_guide.SetBookingGangConfReq")
	proto.RegisterType((*SetBookingGangConfRsp)(nil), "channel_guide.SetBookingGangConfRsp")
	proto.RegisterType((*GetEnabledBookingGangReq)(nil), "channel_guide.GetEnabledBookingGangReq")
	proto.RegisterType((*GetEnabledBookingGangRsp)(nil), "channel_guide.GetEnabledBookingGangRsp")
	proto.RegisterType((*GetGangConfReq)(nil), "channel_guide.GetGangConfReq")
	proto.RegisterType((*GetGangConfRsp)(nil), "channel_guide.GetGangConfRsp")
	proto.RegisterType((*GetGangDynamicConfReq)(nil), "channel_guide.GetGangDynamicConfReq")
	proto.RegisterType((*GetGangDynamicConfRsp)(nil), "channel_guide.GetGangDynamicConfRsp")
	proto.RegisterType((*CloseGameGroupGuideReq)(nil), "channel_guide.CloseGameGroupGuideReq")
	proto.RegisterType((*CloseGameGroupGuideRsp)(nil), "channel_guide.CloseGameGroupGuideRsp")
	proto.RegisterType((*GameCardOpt)(nil), "channel_guide.GameCardOpt")
	proto.RegisterType((*AppoinmentReq)(nil), "channel_guide.AppoinmentReq")
	proto.RegisterType((*AppoinmentRsp)(nil), "channel_guide.AppoinmentRsp")
	proto.RegisterType((*TeamNofifyInfo)(nil), "channel_guide.TeamNofifyInfo")
	proto.RegisterType((*SetTeamNotifyReq)(nil), "channel_guide.SetTeamNotifyReq")
	proto.RegisterType((*TeamNotifyRspInfo)(nil), "channel_guide.TeamNotifyRspInfo")
	proto.RegisterType((*SetTeamNotifyRsp)(nil), "channel_guide.SetTeamNotifyRsp")
	proto.RegisterType((*JoinCarTeamReq)(nil), "channel_guide.JoinCarTeamReq")
	proto.RegisterType((*JoinCarTeamRsp)(nil), "channel_guide.JoinCarTeamRsp")
	proto.RegisterType((*TeamingInfo)(nil), "channel_guide.TeamingInfo")
	proto.RegisterType((*GetTeamingReq)(nil), "channel_guide.GetTeamingReq")
	proto.RegisterType((*GetTeamingRsp)(nil), "channel_guide.GetTeamingRsp")
	proto.RegisterType((*SetTeamChannelReq)(nil), "channel_guide.SetTeamChannelReq")
	proto.RegisterType((*SetTeamChannelRsp)(nil), "channel_guide.SetTeamChannelRsp")
	proto.RegisterType((*GetTeamFightGuideReq)(nil), "channel_guide.GetTeamFightGuideReq")
	proto.RegisterType((*GetTeamFightGuideRsp)(nil), "channel_guide.GetTeamFightGuideRsp")
	proto.RegisterEnum("channel_guide.AppoinmentType", AppoinmentType_name, AppoinmentType_value)
	proto.RegisterEnum("channel_guide.JoinStatus", JoinStatus_name, JoinStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelGuideClient is the client API for ChannelGuide service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelGuideClient interface {
	// 获取游戏群组配置列表
	GetGameGroupConfList(ctx context.Context, in *GetGameGroupConfListReq, opts ...grpc.CallOption) (*GetGameGroupConfListRsp, error)
	// 创建游戏群组配置
	CreateGameGroupConf(ctx context.Context, in *CreateGameGroupConfReq, opts ...grpc.CallOption) (*CreateGameGroupConfRsp, error)
	// 删除游戏群组配置
	DelGameGroupConf(ctx context.Context, in *DelGameGroupConfReq, opts ...grpc.CallOption) (*DelGameGroupConfRsp, error)
	// 获取游戏群组列表
	GetGameGroupList(ctx context.Context, in *GetGameGroupListReq, opts ...grpc.CallOption) (*GetGameGroupListRsp, error)
	// 获取预约开黑配置
	GetBookingGangConf(ctx context.Context, in *GetBookingGangConfReq, opts ...grpc.CallOption) (*GetBookingGangConfRsp, error)
	// 设置预约开黑开关
	SetBookingGangConf(ctx context.Context, in *SetBookingGangConfReq, opts ...grpc.CallOption) (*SetBookingGangConfRsp, error)
	// 获取开启配置的游戏tab
	GetEnabledBookingGang(ctx context.Context, in *GetEnabledBookingGangReq, opts ...grpc.CallOption) (*GetEnabledBookingGangRsp, error)
	// 获取所有开黑配置
	GetGangConf(ctx context.Context, in *GetGangConfReq, opts ...grpc.CallOption) (*GetGangConfRsp, error)
	// 开黑动态配置
	GetGangDynamicConf(ctx context.Context, in *GetGangDynamicConfReq, opts ...grpc.CallOption) (*GetGangDynamicConfRsp, error)
	// 关闭游戏群组引导
	CloseGameGroupGuide(ctx context.Context, in *CloseGameGroupGuideReq, opts ...grpc.CallOption) (*CloseGameGroupGuideRsp, error)
	Appoinment(ctx context.Context, in *AppoinmentReq, opts ...grpc.CallOption) (*AppoinmentRsp, error)
	SetTeamNotify(ctx context.Context, in *SetTeamNotifyReq, opts ...grpc.CallOption) (*SetTeamNotifyRsp, error)
	JoinCarTeam(ctx context.Context, in *JoinCarTeamReq, opts ...grpc.CallOption) (*JoinCarTeamRsp, error)
	GetTeaming(ctx context.Context, in *GetTeamingReq, opts ...grpc.CallOption) (*GetTeamingRsp, error)
	SetTeamChannel(ctx context.Context, in *SetTeamChannelReq, opts ...grpc.CallOption) (*SetTeamChannelRsp, error)
	// 团战新手引导
	GetTeamFightGuide(ctx context.Context, in *GetTeamFightGuideReq, opts ...grpc.CallOption) (*GetTeamFightGuideRsp, error)
}

type channelGuideClient struct {
	cc *grpc.ClientConn
}

func NewChannelGuideClient(cc *grpc.ClientConn) ChannelGuideClient {
	return &channelGuideClient{cc}
}

func (c *channelGuideClient) GetGameGroupConfList(ctx context.Context, in *GetGameGroupConfListReq, opts ...grpc.CallOption) (*GetGameGroupConfListRsp, error) {
	out := new(GetGameGroupConfListRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/GetGameGroupConfList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) CreateGameGroupConf(ctx context.Context, in *CreateGameGroupConfReq, opts ...grpc.CallOption) (*CreateGameGroupConfRsp, error) {
	out := new(CreateGameGroupConfRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/CreateGameGroupConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) DelGameGroupConf(ctx context.Context, in *DelGameGroupConfReq, opts ...grpc.CallOption) (*DelGameGroupConfRsp, error) {
	out := new(DelGameGroupConfRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/DelGameGroupConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) GetGameGroupList(ctx context.Context, in *GetGameGroupListReq, opts ...grpc.CallOption) (*GetGameGroupListRsp, error) {
	out := new(GetGameGroupListRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/GetGameGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) GetBookingGangConf(ctx context.Context, in *GetBookingGangConfReq, opts ...grpc.CallOption) (*GetBookingGangConfRsp, error) {
	out := new(GetBookingGangConfRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/GetBookingGangConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) SetBookingGangConf(ctx context.Context, in *SetBookingGangConfReq, opts ...grpc.CallOption) (*SetBookingGangConfRsp, error) {
	out := new(SetBookingGangConfRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/SetBookingGangConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) GetEnabledBookingGang(ctx context.Context, in *GetEnabledBookingGangReq, opts ...grpc.CallOption) (*GetEnabledBookingGangRsp, error) {
	out := new(GetEnabledBookingGangRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/GetEnabledBookingGang", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) GetGangConf(ctx context.Context, in *GetGangConfReq, opts ...grpc.CallOption) (*GetGangConfRsp, error) {
	out := new(GetGangConfRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/GetGangConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) GetGangDynamicConf(ctx context.Context, in *GetGangDynamicConfReq, opts ...grpc.CallOption) (*GetGangDynamicConfRsp, error) {
	out := new(GetGangDynamicConfRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/GetGangDynamicConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) CloseGameGroupGuide(ctx context.Context, in *CloseGameGroupGuideReq, opts ...grpc.CallOption) (*CloseGameGroupGuideRsp, error) {
	out := new(CloseGameGroupGuideRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/CloseGameGroupGuide", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) Appoinment(ctx context.Context, in *AppoinmentReq, opts ...grpc.CallOption) (*AppoinmentRsp, error) {
	out := new(AppoinmentRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/Appoinment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) SetTeamNotify(ctx context.Context, in *SetTeamNotifyReq, opts ...grpc.CallOption) (*SetTeamNotifyRsp, error) {
	out := new(SetTeamNotifyRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/SetTeamNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) JoinCarTeam(ctx context.Context, in *JoinCarTeamReq, opts ...grpc.CallOption) (*JoinCarTeamRsp, error) {
	out := new(JoinCarTeamRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/JoinCarTeam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) GetTeaming(ctx context.Context, in *GetTeamingReq, opts ...grpc.CallOption) (*GetTeamingRsp, error) {
	out := new(GetTeamingRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/GetTeaming", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) SetTeamChannel(ctx context.Context, in *SetTeamChannelReq, opts ...grpc.CallOption) (*SetTeamChannelRsp, error) {
	out := new(SetTeamChannelRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/SetTeamChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGuideClient) GetTeamFightGuide(ctx context.Context, in *GetTeamFightGuideReq, opts ...grpc.CallOption) (*GetTeamFightGuideRsp, error) {
	out := new(GetTeamFightGuideRsp)
	err := c.cc.Invoke(ctx, "/channel_guide.ChannelGuide/GetTeamFightGuide", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelGuideServer is the server API for ChannelGuide service.
type ChannelGuideServer interface {
	// 获取游戏群组配置列表
	GetGameGroupConfList(context.Context, *GetGameGroupConfListReq) (*GetGameGroupConfListRsp, error)
	// 创建游戏群组配置
	CreateGameGroupConf(context.Context, *CreateGameGroupConfReq) (*CreateGameGroupConfRsp, error)
	// 删除游戏群组配置
	DelGameGroupConf(context.Context, *DelGameGroupConfReq) (*DelGameGroupConfRsp, error)
	// 获取游戏群组列表
	GetGameGroupList(context.Context, *GetGameGroupListReq) (*GetGameGroupListRsp, error)
	// 获取预约开黑配置
	GetBookingGangConf(context.Context, *GetBookingGangConfReq) (*GetBookingGangConfRsp, error)
	// 设置预约开黑开关
	SetBookingGangConf(context.Context, *SetBookingGangConfReq) (*SetBookingGangConfRsp, error)
	// 获取开启配置的游戏tab
	GetEnabledBookingGang(context.Context, *GetEnabledBookingGangReq) (*GetEnabledBookingGangRsp, error)
	// 获取所有开黑配置
	GetGangConf(context.Context, *GetGangConfReq) (*GetGangConfRsp, error)
	// 开黑动态配置
	GetGangDynamicConf(context.Context, *GetGangDynamicConfReq) (*GetGangDynamicConfRsp, error)
	// 关闭游戏群组引导
	CloseGameGroupGuide(context.Context, *CloseGameGroupGuideReq) (*CloseGameGroupGuideRsp, error)
	Appoinment(context.Context, *AppoinmentReq) (*AppoinmentRsp, error)
	SetTeamNotify(context.Context, *SetTeamNotifyReq) (*SetTeamNotifyRsp, error)
	JoinCarTeam(context.Context, *JoinCarTeamReq) (*JoinCarTeamRsp, error)
	GetTeaming(context.Context, *GetTeamingReq) (*GetTeamingRsp, error)
	SetTeamChannel(context.Context, *SetTeamChannelReq) (*SetTeamChannelRsp, error)
	// 团战新手引导
	GetTeamFightGuide(context.Context, *GetTeamFightGuideReq) (*GetTeamFightGuideRsp, error)
}

func RegisterChannelGuideServer(s *grpc.Server, srv ChannelGuideServer) {
	s.RegisterService(&_ChannelGuide_serviceDesc, srv)
}

func _ChannelGuide_GetGameGroupConfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameGroupConfListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).GetGameGroupConfList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/GetGameGroupConfList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).GetGameGroupConfList(ctx, req.(*GetGameGroupConfListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_CreateGameGroupConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGameGroupConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).CreateGameGroupConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/CreateGameGroupConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).CreateGameGroupConf(ctx, req.(*CreateGameGroupConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_DelGameGroupConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGameGroupConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).DelGameGroupConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/DelGameGroupConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).DelGameGroupConf(ctx, req.(*DelGameGroupConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_GetGameGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).GetGameGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/GetGameGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).GetGameGroupList(ctx, req.(*GetGameGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_GetBookingGangConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBookingGangConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).GetBookingGangConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/GetBookingGangConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).GetBookingGangConf(ctx, req.(*GetBookingGangConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_SetBookingGangConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBookingGangConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).SetBookingGangConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/SetBookingGangConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).SetBookingGangConf(ctx, req.(*SetBookingGangConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_GetEnabledBookingGang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnabledBookingGangReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).GetEnabledBookingGang(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/GetEnabledBookingGang",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).GetEnabledBookingGang(ctx, req.(*GetEnabledBookingGangReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_GetGangConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGangConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).GetGangConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/GetGangConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).GetGangConf(ctx, req.(*GetGangConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_GetGangDynamicConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGangDynamicConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).GetGangDynamicConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/GetGangDynamicConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).GetGangDynamicConf(ctx, req.(*GetGangDynamicConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_CloseGameGroupGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseGameGroupGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).CloseGameGroupGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/CloseGameGroupGuide",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).CloseGameGroupGuide(ctx, req.(*CloseGameGroupGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_Appoinment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppoinmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).Appoinment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/Appoinment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).Appoinment(ctx, req.(*AppoinmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_SetTeamNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTeamNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).SetTeamNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/SetTeamNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).SetTeamNotify(ctx, req.(*SetTeamNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_JoinCarTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinCarTeamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).JoinCarTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/JoinCarTeam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).JoinCarTeam(ctx, req.(*JoinCarTeamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_GetTeaming_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTeamingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).GetTeaming(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/GetTeaming",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).GetTeaming(ctx, req.(*GetTeamingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_SetTeamChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTeamChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).SetTeamChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/SetTeamChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).SetTeamChannel(ctx, req.(*SetTeamChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGuide_GetTeamFightGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTeamFightGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGuideServer).GetTeamFightGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_guide.ChannelGuide/GetTeamFightGuide",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGuideServer).GetTeamFightGuide(ctx, req.(*GetTeamFightGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelGuide_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_guide.ChannelGuide",
	HandlerType: (*ChannelGuideServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGameGroupConfList",
			Handler:    _ChannelGuide_GetGameGroupConfList_Handler,
		},
		{
			MethodName: "CreateGameGroupConf",
			Handler:    _ChannelGuide_CreateGameGroupConf_Handler,
		},
		{
			MethodName: "DelGameGroupConf",
			Handler:    _ChannelGuide_DelGameGroupConf_Handler,
		},
		{
			MethodName: "GetGameGroupList",
			Handler:    _ChannelGuide_GetGameGroupList_Handler,
		},
		{
			MethodName: "GetBookingGangConf",
			Handler:    _ChannelGuide_GetBookingGangConf_Handler,
		},
		{
			MethodName: "SetBookingGangConf",
			Handler:    _ChannelGuide_SetBookingGangConf_Handler,
		},
		{
			MethodName: "GetEnabledBookingGang",
			Handler:    _ChannelGuide_GetEnabledBookingGang_Handler,
		},
		{
			MethodName: "GetGangConf",
			Handler:    _ChannelGuide_GetGangConf_Handler,
		},
		{
			MethodName: "GetGangDynamicConf",
			Handler:    _ChannelGuide_GetGangDynamicConf_Handler,
		},
		{
			MethodName: "CloseGameGroupGuide",
			Handler:    _ChannelGuide_CloseGameGroupGuide_Handler,
		},
		{
			MethodName: "Appoinment",
			Handler:    _ChannelGuide_Appoinment_Handler,
		},
		{
			MethodName: "SetTeamNotify",
			Handler:    _ChannelGuide_SetTeamNotify_Handler,
		},
		{
			MethodName: "JoinCarTeam",
			Handler:    _ChannelGuide_JoinCarTeam_Handler,
		},
		{
			MethodName: "GetTeaming",
			Handler:    _ChannelGuide_GetTeaming_Handler,
		},
		{
			MethodName: "SetTeamChannel",
			Handler:    _ChannelGuide_SetTeamChannel_Handler,
		},
		{
			MethodName: "GetTeamFightGuide",
			Handler:    _ChannelGuide_GetTeamFightGuide_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-guide/channel-guide.proto",
}

func init() {
	proto.RegisterFile("channel-guide/channel-guide.proto", fileDescriptor_channel_guide_c6065a379b73a81e)
}

var fileDescriptor_channel_guide_c6065a379b73a81e = []byte{
	// 1766 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0x5b, 0x6f, 0x23, 0x49,
	0x15, 0x9e, 0x76, 0x27, 0xbe, 0x1c, 0xc7, 0x8e, 0x53, 0xb9, 0x39, 0xcd, 0x84, 0x09, 0x3d, 0xb3,
	0x6c, 0x58, 0x44, 0xb2, 0x9b, 0x65, 0x85, 0x76, 0x57, 0x42, 0x64, 0x3d, 0x13, 0x2b, 0xa3, 0x9d,
	0x8c, 0xb6, 0x1d, 0xe6, 0x01, 0x21, 0x9a, 0x4e, 0x77, 0xd9, 0x53, 0xbb, 0x7d, 0x9b, 0xae, 0x72,
	0x06, 0x0b, 0x89, 0x77, 0x5e, 0x79, 0x46, 0x82, 0x67, 0xfe, 0x03, 0xfc, 0x00, 0xfe, 0x04, 0x7f,
	0x05, 0xd5, 0xa5, 0xed, 0xbe, 0x39, 0x36, 0x08, 0xde, 0xba, 0xce, 0x39, 0x75, 0x2e, 0xdf, 0x39,
	0x75, 0xaa, 0x4e, 0xc3, 0x0f, 0xdc, 0xb7, 0x4e, 0x18, 0x62, 0xff, 0x27, 0x93, 0x29, 0xf1, 0xf0,
	0x79, 0x6e, 0x75, 0x16, 0x27, 0x11, 0x8b, 0x50, 0x47, 0x11, 0x6d, 0x41, 0x34, 0x07, 0xd0, 0x19,
	0x3a, 0x01, 0x1e, 0x26, 0xd1, 0x34, 0x1e, 0x44, 0xe1, 0x18, 0x1d, 0x42, 0x63, 0xe2, 0x04, 0xd8,
	0x26, 0x5e, 0xbf, 0x76, 0xa2, 0x9d, 0x76, 0xac, 0x3a, 0x5f, 0x5e, 0x7b, 0xe8, 0x08, 0x9a, 0x13,
	0x2e, 0xc5, 0x39, 0xba, 0xe0, 0x34, 0xc4, 0xfa, 0xda, 0x33, 0x7f, 0x0f, 0x87, 0x43, 0xcc, 0x72,
	0x7a, 0xbe, 0x26, 0x94, 0x59, 0xf8, 0x1d, 0x3a, 0x80, 0x7a, 0x34, 0x1e, 0x53, 0xcc, 0xfa, 0x9a,
	0xd4, 0x26, 0x57, 0x68, 0x0f, 0x36, 0x7d, 0x12, 0x10, 0xa6, 0x8c, 0xc8, 0x45, 0xd6, 0xb8, 0xbe,
	0xd4, 0xf8, 0x46, 0xde, 0xf8, 0xed, 0x12, 0xe3, 0x34, 0x46, 0x9f, 0x43, 0xcb, 0x8d, 0xc2, 0xb1,
	0xed, 0x13, 0xca, 0xed, 0xeb, 0xa7, 0xed, 0x8b, 0xc7, 0x67, 0xb9, 0xf8, 0xcf, 0x72, 0xfb, 0xac,
	0xa6, 0xab, 0x76, 0x9b, 0x5f, 0xc3, 0xc1, 0x20, 0xc1, 0x0e, 0xc3, 0x79, 0x01, 0xfc, 0x2e, 0xeb,
	0xa3, 0xb6, 0xd4, 0xc7, 0x5a, 0xde, 0xc7, 0x7e, 0xb5, 0x36, 0x1a, 0x9b, 0xd7, 0xb0, 0xfb, 0x1c,
	0xfb, 0xff, 0x13, 0x23, 0xfb, 0x15, 0xaa, 0x68, 0x6c, 0x9e, 0xc1, 0x6e, 0x16, 0x9f, 0x34, 0x31,
	0xcb, 0x2c, 0x98, 0x7f, 0xaa, 0x41, 0x6b, 0x2e, 0x8d, 0xba, 0x50, 0x9b, 0x4b, 0xd4, 0x88, 0x87,
	0x10, 0x6c, 0x84, 0x4e, 0x80, 0x85, 0xed, 0x96, 0x25, 0xbe, 0x51, 0x1f, 0x1a, 0x8e, 0xeb, 0x46,
	0xd3, 0x90, 0x89, 0xac, 0xb5, 0xac, 0x74, 0x89, 0x8e, 0x01, 0x02, 0x1c, 0xdc, 0xe1, 0xc4, 0x0e,
	0xa7, 0x81, 0x4a, 0x5c, 0x4b, 0x52, 0x6e, 0xa6, 0x01, 0x7a, 0x02, 0xed, 0x29, 0xc5, 0x89, 0xfd,
	0x6d, 0x44, 0x42, 0xec, 0xf5, 0x37, 0x4f, 0xb4, 0xd3, 0xa6, 0x05, 0x9c, 0xf4, 0x52, 0x50, 0xb8,
	0x40, 0x88, 0xb1, 0x67, 0xdf, 0xe3, 0x84, 0x8c, 0x67, 0xfd, 0x86, 0x50, 0x00, 0x9c, 0xf4, 0x46,
	0x50, 0xd0, 0x2f, 0xa0, 0xad, 0x0c, 0x88, 0x1c, 0xd7, 0x45, 0x8e, 0x9f, 0x2c, 0xcb, 0xf1, 0xd9,
	0x2b, 0x21, 0x6b, 0x29, 0xa7, 0x38, 0x14, 0xc6, 0x33, 0xa8, 0x4b, 0x2a, 0x32, 0xa0, 0xc9, 0x4d,
	0x8b, 0xf0, 0x34, 0x11, 0xc7, 0x7c, 0x6d, 0x7e, 0x5e, 0x01, 0x22, 0x8d, 0x91, 0x09, 0x9d, 0x34,
	0x1b, 0x8b, 0x22, 0xeb, 0x58, 0x6d, 0x95, 0x12, 0x51, 0x49, 0x1f, 0xc3, 0xfe, 0x10, 0xb3, 0xaf,
	0xa2, 0xe8, 0x3b, 0x12, 0x4e, 0x86, 0x4e, 0x38, 0x59, 0x95, 0x63, 0xf3, 0x93, 0xca, 0x1d, 0x34,
	0xe6, 0x40, 0xe3, 0xd0, 0xb9, 0xf3, 0xb1, 0xdc, 0xd1, 0xb4, 0xd2, 0xa5, 0xf9, 0x12, 0xf6, 0x47,
	0xff, 0x91, 0x91, 0xac, 0xae, 0x5a, 0x5e, 0xd7, 0x61, 0xa5, 0x2e, 0x1a, 0x9b, 0x06, 0xf4, 0x87,
	0x98, 0xbd, 0x90, 0x62, 0x19, 0xbe, 0x85, 0xdf, 0x99, 0x5f, 0x2c, 0xe3, 0xd1, 0x18, 0x7d, 0x1f,
	0xda, 0xcc, 0xb9, 0x2b, 0x60, 0xd4, 0x62, 0xce, 0x9d, 0x42, 0xe8, 0x4b, 0xe8, 0x0a, 0x70, 0xd7,
	0xf0, 0xba, 0x07, 0xfa, 0x74, 0x5e, 0xf9, 0xfc, 0xd3, 0xfc, 0x73, 0x2d, 0xbf, 0x9b, 0xc6, 0xe8,
	0x67, 0x00, 0x32, 0x2b, 0x99, 0x73, 0xdf, 0x5f, 0x56, 0x13, 0x56, 0x6b, 0x92, 0x66, 0x14, 0x7d,
	0x08, 0xdb, 0x0a, 0x04, 0xfb, 0x4e, 0x86, 0xa0, 0xb0, 0xe9, 0xe2, 0x5c, 0x60, 0xe8, 0xc7, 0xb0,
	0xa3, 0x04, 0x6c, 0x6f, 0x9a, 0x38, 0x8c, 0x44, 0x21, 0xed, 0xeb, 0x27, 0xfa, 0xa9, 0x6e, 0xf5,
	0x14, 0xe3, 0x79, 0x4a, 0xcf, 0x0a, 0x8b, 0x53, 0xe1, 0x45, 0xef, 0x43, 0x75, 0x16, 0x52, 0xe1,
	0x41, 0x4a, 0x47, 0x4f, 0xa1, 0x93, 0x0a, 0x53, 0xe6, 0x30, 0x2c, 0x0e, 0x45, 0xc7, 0xda, 0x52,
	0xc4, 0x11, 0xa7, 0xa1, 0x1f, 0xc1, 0x4e, 0x30, 0xb3, 0x65, 0x8c, 0x8e, 0xeb, 0x2e, 0x6a, 0xbf,
	0x65, 0x75, 0x83, 0x99, 0x08, 0xeb, 0xd2, 0x75, 0x05, 0xb6, 0x87, 0xa2, 0x96, 0x38, 0x3a, 0xcf,
	0x67, 0xa1, 0x13, 0x10, 0x57, 0x41, 0x6c, 0x4e, 0x2a, 0x19, 0x34, 0x46, 0xa7, 0xd0, 0x7b, 0xef,
	0x10, 0x66, 0xf3, 0x56, 0x48, 0x92, 0xc0, 0xa6, 0xd8, 0x55, 0x49, 0xe8, 0x72, 0xfa, 0x40, 0x92,
	0x47, 0xd8, 0x45, 0xcf, 0x40, 0x50, 0x6c, 0x1c, 0x32, 0x9c, 0x08, 0x39, 0x99, 0x97, 0x2d, 0x4e,
	0x7d, 0xc1, 0x89, 0x23, 0xec, 0x9a, 0x97, 0x70, 0x30, 0xf0, 0x23, 0xba, 0x68, 0x7d, 0x43, 0x9e,
	0x00, 0x9e, 0x65, 0x95, 0x4c, 0x6d, 0x9e, 0x4c, 0xb4, 0x0f, 0x75, 0x59, 0x29, 0xe9, 0xb5, 0x20,
	0x8a, 0x44, 0xb4, 0xcf, 0x0a, 0x15, 0x34, 0x36, 0x7f, 0x03, 0x6d, 0x4e, 0x1c, 0x38, 0x89, 0xf7,
	0x3a, 0x66, 0xbc, 0x3b, 0x46, 0x31, 0xb3, 0x33, 0x47, 0xb8, 0x11, 0xc5, 0xec, 0x86, 0x37, 0xa9,
	0x63, 0x80, 0x7b, 0xc7, 0x9f, 0x62, 0x09, 0x56, 0x4d, 0x80, 0xd5, 0x12, 0x14, 0x91, 0xfa, 0x7d,
	0xa8, 0xf3, 0x9d, 0xf3, 0x8b, 0x67, 0x33, 0x8a, 0xd9, 0xb5, 0x67, 0xfe, 0xbd, 0x06, 0x9d, 0xcb,
	0x38, 0x8e, 0x48, 0x18, 0xe0, 0x90, 0x55, 0x3b, 0x7d, 0x05, 0xdb, 0xce, 0x5c, 0xc4, 0x66, 0xb3,
	0x58, 0x76, 0xc7, 0xee, 0xc5, 0x71, 0xa1, 0xe6, 0x16, 0x8a, 0x6e, 0x67, 0x31, 0xb6, 0xba, 0x4e,
	0x6e, 0x8d, 0x3e, 0x93, 0xce, 0x0b, 0xff, 0x74, 0x51, 0xb4, 0x46, 0x45, 0xd1, 0xaa, 0x50, 0x45,
	0x60, 0xc2, 0xf3, 0xa7, 0xd0, 0x61, 0x24, 0xc0, 0xf3, 0x42, 0x54, 0xa5, 0xb5, 0xc5, 0x89, 0x69,
	0x11, 0xf2, 0x23, 0x28, 0x0e, 0x94, 0x42, 0x57, 0x16, 0x55, 0x8b, 0x93, 0x6e, 0x39, 0xc2, 0xa2,
	0x91, 0xa5, 0x7c, 0x81, 0x5e, 0x5d, 0xa0, 0xd7, 0x56, 0x12, 0x02, 0xc1, 0x7d, 0xa8, 0x13, 0x6a,
	0xf3, 0xab, 0xbc, 0x21, 0x0e, 0xc5, 0x26, 0xa1, 0x23, 0xcc, 0xd0, 0xf7, 0xa0, 0x15, 0xdd, 0xe3,
	0xc4, 0xe6, 0xf6, 0xfa, 0xcd, 0x13, 0xed, 0x54, 0xb7, 0x9a, 0x9c, 0x70, 0x4b, 0x02, 0x6c, 0xfe,
	0x4d, 0xcb, 0xe1, 0x47, 0xe3, 0x2a, 0xb4, 0xb4, 0xff, 0x06, 0xad, 0x9f, 0xc2, 0x01, 0xc5, 0x3e,
	0x76, 0x99, 0x9d, 0x55, 0x47, 0xc4, 0xd5, 0xc4, 0xcf, 0xe1, 0x9e, 0xe4, 0x66, 0xb4, 0x90, 0x00,
	0xe7, 0x9d, 0xd5, 0x0b, 0xce, 0xfe, 0x51, 0x83, 0xee, 0x2d, 0x76, 0x82, 0x9b, 0x68, 0x4c, 0xc6,
	0xb3, 0xeb, 0x70, 0x1c, 0x71, 0x79, 0x86, 0x9d, 0xc0, 0x9e, 0x12, 0x8f, 0xaa, 0xc6, 0xd5, 0xe4,
	0x84, 0x5f, 0x12, 0x8f, 0x16, 0x41, 0xad, 0xad, 0x04, 0x55, 0x2f, 0x83, 0x7a, 0x00, 0x75, 0xae,
	0x4f, 0x3d, 0x6b, 0x5a, 0x96, 0x5a, 0x99, 0x16, 0xf4, 0x46, 0x98, 0x49, 0x6f, 0x18, 0x19, 0xcf,
	0x78, 0xe9, 0xfd, 0x1c, 0xda, 0xa1, 0x58, 0xd8, 0x24, 0x1c, 0x47, 0xaa, 0xb1, 0x15, 0x61, 0xcb,
	0x07, 0x60, 0x81, 0xdc, 0xc1, 0xbf, 0xcd, 0x6f, 0x60, 0x27, 0xa3, 0x90, 0xc6, 0x22, 0xc2, 0x63,
	0x80, 0x28, 0x21, 0xb6, 0x72, 0x42, 0x1e, 0x9a, 0x56, 0x94, 0x90, 0x5b, 0x41, 0xe0, 0x6c, 0x8f,
	0xb2, 0x94, 0x2d, 0x6f, 0xfd, 0x96, 0x47, 0x99, 0x64, 0x9b, 0x7f, 0xd1, 0x8a, 0x7e, 0xd2, 0x18,
	0x7d, 0x09, 0xcd, 0x84, 0xc6, 0x59, 0x27, 0x4f, 0x2a, 0x9d, 0xcc, 0xb8, 0x61, 0x35, 0x12, 0xe5,
	0x4f, 0x55, 0xfb, 0xa9, 0xad, 0xd9, 0x7e, 0xf4, 0x8a, 0xf6, 0xf3, 0x07, 0xe8, 0xf2, 0xc7, 0xc4,
	0xc0, 0x49, 0xb8, 0x51, 0x0e, 0x63, 0x21, 0x6d, 0xda, 0xca, 0xb4, 0xd5, 0x1e, 0x4a, 0x9b, 0x9e,
	0x4d, 0x5b, 0xda, 0x1d, 0x36, 0x16, 0xf7, 0xd3, 0x3f, 0xb5, 0xbc, 0x03, 0x34, 0x46, 0x5f, 0x40,
	0x9b, 0xbf, 0x78, 0x44, 0x83, 0x9f, 0x52, 0x55, 0xfe, 0x47, 0x05, 0x88, 0xf8, 0x9e, 0x91, 0x10,
	0xb0, 0xe0, 0xdb, 0xf9, 0x37, 0x7a, 0x0c, 0x2d, 0x25, 0xb7, 0xa8, 0xb8, 0x39, 0x81, 0xbb, 0x2e,
	0xca, 0x95, 0x4e, 0x5d, 0x59, 0xb2, 0xba, 0x7c, 0x8f, 0x70, 0xe2, 0x68, 0xea, 0x8a, 0xaa, 0xcd,
	0x95, 0xf4, 0x46, 0xa1, 0xa4, 0xf3, 0xd5, 0xb0, 0x59, 0xa8, 0x06, 0xf3, 0x1f, 0x1a, 0xb4, 0xc5,
	0x67, 0x38, 0x11, 0xc9, 0xfa, 0x7f, 0x42, 0x79, 0x04, 0xcd, 0x79, 0x18, 0xd2, 0xcd, 0x06, 0x55,
	0x21, 0x1c, 0x41, 0xd3, 0xf1, 0x7d, 0xc9, 0xda, 0x94, 0x2c, 0xc7, 0xf7, 0x2b, 0x02, 0xa8, 0x17,
	0x03, 0xd8, 0x86, 0xce, 0x50, 0x96, 0x2b, 0x91, 0xef, 0x96, 0xcb, 0x1c, 0x81, 0xc6, 0xe8, 0x63,
	0xd8, 0xe4, 0x85, 0x4b, 0x55, 0xe5, 0x1a, 0x15, 0x95, 0xab, 0xa2, 0xb7, 0xa4, 0xa0, 0xf9, 0x57,
	0x0d, 0x76, 0xd4, 0x19, 0x18, 0x48, 0x59, 0x5e, 0x65, 0xb9, 0x44, 0x69, 0xc5, 0x44, 0x2d, 0x82,
	0xae, 0x2d, 0x0d, 0x5a, 0xcf, 0x07, 0x7d, 0x0c, 0x90, 0x62, 0x39, 0xaf, 0xb0, 0x14, 0x6a, 0xe2,
	0xf1, 0xb4, 0x12, 0x6a, 0xe3, 0xdf, 0xb9, 0x78, 0xfe, 0x92, 0x6e, 0x12, 0xfa, 0x42, 0xac, 0xcd,
	0xdd, 0x92, 0x87, 0x34, 0x36, 0x3f, 0x85, 0x3d, 0x15, 0xfa, 0x15, 0x99, 0xbc, 0x65, 0xf3, 0x6b,
	0x59, 0x6a, 0x0a, 0x1c, 0xca, 0x70, 0xa2, 0xde, 0x99, 0x4d, 0x42, 0x5f, 0x89, 0xb5, 0xf9, 0x4d,
	0xd5, 0x26, 0x1a, 0xa3, 0x0f, 0xa0, 0x1b, 0xe2, 0xf7, 0x98, 0x32, 0xfe, 0x56, 0xa7, 0xfc, 0x1a,
	0x92, 0x31, 0x77, 0x24, 0xf5, 0x8d, 0x24, 0xf2, 0xf1, 0x61, 0x9a, 0xf8, 0x54, 0xdd, 0xbf, 0xe2,
	0xfb, 0xa3, 0xd7, 0xd0, 0xcd, 0xf7, 0x7a, 0x84, 0xa0, 0x7b, 0x13, 0x85, 0x78, 0x41, 0xed, 0x3d,
	0x42, 0xbb, 0xb0, 0x7d, 0x45, 0x12, 0x9a, 0x69, 0xe8, 0x3d, 0x0d, 0xed, 0xf1, 0xee, 0xe3, 0x46,
	0xa1, 0x97, 0xa1, 0xd6, 0x3e, 0xfa, 0x2d, 0xc0, 0xe2, 0xf4, 0x70, 0x65, 0x8b, 0xd5, 0x30, 0x7a,
	0x1d, 0xf6, 0x1e, 0xa1, 0x1d, 0xe8, 0x2c, 0x68, 0xa3, 0xa9, 0xdb, 0xd3, 0xd0, 0x21, 0xec, 0xe6,
	0x48, 0x12, 0xb9, 0x5e, 0x2d, 0xbf, 0xff, 0xca, 0x21, 0x7e, 0x4f, 0xbf, 0xf8, 0x57, 0x1b, 0xb6,
	0x14, 0x92, 0x02, 0x01, 0xf4, 0x56, 0xc0, 0x52, 0x1a, 0x42, 0xd1, 0x0f, 0x8b, 0x37, 0x78, 0xf5,
	0x98, 0x6c, 0xac, 0x25, 0x47, 0x63, 0xf3, 0x11, 0xc2, 0xb0, 0x5b, 0x31, 0x4a, 0xa2, 0x0f, 0x0a,
	0x0a, 0xaa, 0x87, 0x57, 0x63, 0x1d, 0x31, 0x61, 0xe6, 0xd7, 0xd0, 0x2b, 0x0e, 0x93, 0xc8, 0x2c,
	0x6c, 0xae, 0x18, 0x5c, 0x8d, 0x95, 0x32, 0xa9, 0xf6, 0xe2, 0x38, 0x55, 0xd2, 0x5e, 0x31, 0xb4,
	0x1a, 0x2b, 0x65, 0x84, 0xf6, 0x3b, 0x40, 0xe5, 0xf9, 0x09, 0x3d, 0x2b, 0xef, 0x2d, 0xcf, 0x4b,
	0xc6, 0x1a, 0x52, 0xa9, 0x8d, 0xd1, 0x6a, 0x1b, 0xa3, 0xb5, 0x6c, 0x8c, 0x96, 0xd8, 0xf8, 0x4e,
	0x3c, 0xd1, 0xcb, 0x33, 0x15, 0xfa, 0xb0, 0xec, 0x64, 0xe5, 0x54, 0x66, 0xac, 0x27, 0x28, 0x8c,
	0xbd, 0x82, 0x76, 0x66, 0x8c, 0x42, 0xc7, 0x55, 0x48, 0x2f, 0x42, 0x78, 0x88, 0x9d, 0xc9, 0x41,
	0x61, 0xbc, 0xa8, 0xca, 0x41, 0x79, 0x34, 0x31, 0xd6, 0x90, 0x9a, 0x1f, 0x85, 0xf2, 0x58, 0x50,
	0x3e, 0x0a, 0x95, 0xd3, 0x87, 0xb1, 0x8e, 0x98, 0x30, 0xf3, 0x12, 0x60, 0xd1, 0x5e, 0xd0, 0xe3,
	0xa5, 0xcf, 0x54, 0xae, 0xf4, 0x01, 0xae, 0xd0, 0x35, 0x82, 0x4e, 0xee, 0xb9, 0x84, 0x9e, 0x94,
	0x6b, 0x21, 0xf7, 0xe8, 0x33, 0x1e, 0x16, 0x48, 0x53, 0x97, 0x79, 0x61, 0x94, 0x52, 0x97, 0x7f,
	0xfe, 0x18, 0x0f, 0xb1, 0xd3, 0x78, 0x17, 0x57, 0x62, 0x29, 0xde, 0xdc, 0xf5, 0x69, 0x3c, 0xc0,
	0x15, 0xba, 0xde, 0x40, 0x37, 0x7f, 0xf1, 0xa0, 0x93, 0xea, 0x78, 0x16, 0x37, 0xa7, 0xb1, 0x42,
	0x42, 0xe8, 0xb5, 0x61, 0xa7, 0x74, 0x0d, 0xa1, 0xa7, 0xd5, 0xce, 0xe4, 0x6e, 0x37, 0x63, 0xb5,
	0x10, 0x37, 0xf0, 0xd5, 0x27, 0xbf, 0x3a, 0x9f, 0x44, 0xbe, 0x13, 0x4e, 0xce, 0x3e, 0xbb, 0x60,
	0xec, 0xcc, 0x8d, 0x82, 0x73, 0xf1, 0xff, 0xd4, 0x8d, 0xfc, 0x73, 0x8a, 0x93, 0x7b, 0xe2, 0x62,
	0x9a, 0xff, 0xbf, 0x7a, 0x57, 0x17, 0x02, 0x9f, 0xfe, 0x3b, 0x00, 0x00, 0xff, 0xff, 0x12, 0x42,
	0x6b, 0x63, 0x85, 0x15, 0x00, 0x00,
}
