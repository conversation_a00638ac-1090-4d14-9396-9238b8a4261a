// Code generated by protoc-gen-gogo.
// source: src/newbie/newbie.proto
// DO NOT EDIT!

/*
	Package newbie is a generated protocol buffer package.

	It is generated from these files:
		src/newbie/newbie.proto

	It has these top-level messages:
		CheckDevRegCountReq
		CheckDevRegCountResp
		CheckUserRegDevReq
		CheckUserRegDevResp
		UpdateUserRegDevReq
		UpdateUserRegDevResp
		UserFirstLoginInfo
		CheckUserFirstLoginTimeReq
		CheckUserFirstLoginTimeResp
		UserPkgInfo
		UserGameInfo
		BatchGetUserRegPkgGameInfoReq
		BatchGetUserRegPkgGameInfoResp
*/
package newbie

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type CheckDevRegCountReq struct {
	DevInfoHex string `protobuf:"bytes,1,req,name=dev_info_hex,json=devInfoHex" json:"dev_info_hex"`
}

func (m *CheckDevRegCountReq) Reset()                    { *m = CheckDevRegCountReq{} }
func (m *CheckDevRegCountReq) String() string            { return proto.CompactTextString(m) }
func (*CheckDevRegCountReq) ProtoMessage()               {}
func (*CheckDevRegCountReq) Descriptor() ([]byte, []int) { return fileDescriptorNewbie, []int{0} }

func (m *CheckDevRegCountReq) GetDevInfoHex() string {
	if m != nil {
		return m.DevInfoHex
	}
	return ""
}

type CheckDevRegCountResp struct {
	Count uint32 `protobuf:"varint,1,req,name=count" json:"count"`
}

func (m *CheckDevRegCountResp) Reset()                    { *m = CheckDevRegCountResp{} }
func (m *CheckDevRegCountResp) String() string            { return proto.CompactTextString(m) }
func (*CheckDevRegCountResp) ProtoMessage()               {}
func (*CheckDevRegCountResp) Descriptor() ([]byte, []int) { return fileDescriptorNewbie, []int{1} }

func (m *CheckDevRegCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type CheckUserRegDevReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *CheckUserRegDevReq) Reset()                    { *m = CheckUserRegDevReq{} }
func (m *CheckUserRegDevReq) String() string            { return proto.CompactTextString(m) }
func (*CheckUserRegDevReq) ProtoMessage()               {}
func (*CheckUserRegDevReq) Descriptor() ([]byte, []int) { return fileDescriptorNewbie, []int{2} }

func (m *CheckUserRegDevReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUserRegDevResp struct {
	DevHex   string `protobuf:"bytes,1,req,name=dev_hex,json=devHex" json:"dev_hex"`
	DevCount uint32 `protobuf:"varint,2,req,name=dev_count,json=devCount" json:"dev_count"`
}

func (m *CheckUserRegDevResp) Reset()                    { *m = CheckUserRegDevResp{} }
func (m *CheckUserRegDevResp) String() string            { return proto.CompactTextString(m) }
func (*CheckUserRegDevResp) ProtoMessage()               {}
func (*CheckUserRegDevResp) Descriptor() ([]byte, []int) { return fileDescriptorNewbie, []int{3} }

func (m *CheckUserRegDevResp) GetDevHex() string {
	if m != nil {
		return m.DevHex
	}
	return ""
}

func (m *CheckUserRegDevResp) GetDevCount() uint32 {
	if m != nil {
		return m.DevCount
	}
	return 0
}

type UpdateUserRegDevReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	DevHex   string `protobuf:"bytes,4,opt,name=dev_hex,json=devHex" json:"dev_hex"`
	DevCount uint32 `protobuf:"varint,5,opt,name=dev_count,json=devCount" json:"dev_count"`
}

func (m *UpdateUserRegDevReq) Reset()                    { *m = UpdateUserRegDevReq{} }
func (m *UpdateUserRegDevReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateUserRegDevReq) ProtoMessage()               {}
func (*UpdateUserRegDevReq) Descriptor() ([]byte, []int) { return fileDescriptorNewbie, []int{4} }

func (m *UpdateUserRegDevReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserRegDevReq) GetDevHex() string {
	if m != nil {
		return m.DevHex
	}
	return ""
}

func (m *UpdateUserRegDevReq) GetDevCount() uint32 {
	if m != nil {
		return m.DevCount
	}
	return 0
}

type UpdateUserRegDevResp struct {
}

func (m *UpdateUserRegDevResp) Reset()                    { *m = UpdateUserRegDevResp{} }
func (m *UpdateUserRegDevResp) String() string            { return proto.CompactTextString(m) }
func (*UpdateUserRegDevResp) ProtoMessage()               {}
func (*UpdateUserRegDevResp) Descriptor() ([]byte, []int) { return fileDescriptorNewbie, []int{5} }

type UserFirstLoginInfo struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	AppId        uint32 `protobuf:"varint,2,opt,name=app_id,json=appId" json:"app_id"`
	MarketId     uint32 `protobuf:"varint,3,opt,name=market_id,json=marketId" json:"market_id"`
	DevHex       string `protobuf:"bytes,4,opt,name=dev_hex,json=devHex" json:"dev_hex"`
	DevCount     uint32 `protobuf:"varint,5,opt,name=dev_count,json=devCount" json:"dev_count"`
	FirstLoginTs uint32 `protobuf:"varint,6,opt,name=first_login_ts,json=firstLoginTs" json:"first_login_ts"`
}

func (m *UserFirstLoginInfo) Reset()                    { *m = UserFirstLoginInfo{} }
func (m *UserFirstLoginInfo) String() string            { return proto.CompactTextString(m) }
func (*UserFirstLoginInfo) ProtoMessage()               {}
func (*UserFirstLoginInfo) Descriptor() ([]byte, []int) { return fileDescriptorNewbie, []int{6} }

func (m *UserFirstLoginInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserFirstLoginInfo) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *UserFirstLoginInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *UserFirstLoginInfo) GetDevHex() string {
	if m != nil {
		return m.DevHex
	}
	return ""
}

func (m *UserFirstLoginInfo) GetDevCount() uint32 {
	if m != nil {
		return m.DevCount
	}
	return 0
}

func (m *UserFirstLoginInfo) GetFirstLoginTs() uint32 {
	if m != nil {
		return m.FirstLoginTs
	}
	return 0
}

type CheckUserFirstLoginTimeReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *CheckUserFirstLoginTimeReq) Reset()                    { *m = CheckUserFirstLoginTimeReq{} }
func (m *CheckUserFirstLoginTimeReq) String() string            { return proto.CompactTextString(m) }
func (*CheckUserFirstLoginTimeReq) ProtoMessage()               {}
func (*CheckUserFirstLoginTimeReq) Descriptor() ([]byte, []int) { return fileDescriptorNewbie, []int{7} }

func (m *CheckUserFirstLoginTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUserFirstLoginTimeResp struct {
	FirstLoginList []*UserFirstLoginInfo `protobuf:"bytes,1,rep,name=first_login_list,json=firstLoginList" json:"first_login_list,omitempty"`
}

func (m *CheckUserFirstLoginTimeResp) Reset()         { *m = CheckUserFirstLoginTimeResp{} }
func (m *CheckUserFirstLoginTimeResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserFirstLoginTimeResp) ProtoMessage()    {}
func (*CheckUserFirstLoginTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNewbie, []int{8}
}

func (m *CheckUserFirstLoginTimeResp) GetFirstLoginList() []*UserFirstLoginInfo {
	if m != nil {
		return m.FirstLoginList
	}
	return nil
}

// 用户注册时的渠道包信息
type UserPkgInfo struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelPkg string `protobuf:"bytes,2,req,name=channel_pkg,json=channelPkg" json:"channel_pkg"`
}

func (m *UserPkgInfo) Reset()                    { *m = UserPkgInfo{} }
func (m *UserPkgInfo) String() string            { return proto.CompactTextString(m) }
func (*UserPkgInfo) ProtoMessage()               {}
func (*UserPkgInfo) Descriptor() ([]byte, []int) { return fileDescriptorNewbie, []int{9} }

func (m *UserPkgInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserPkgInfo) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

// 用户注册时选的游戏标签信息
type UserGameInfo struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameName string `protobuf:"bytes,2,req,name=game_name,json=gameName" json:"game_name"`
}

func (m *UserGameInfo) Reset()                    { *m = UserGameInfo{} }
func (m *UserGameInfo) String() string            { return proto.CompactTextString(m) }
func (*UserGameInfo) ProtoMessage()               {}
func (*UserGameInfo) Descriptor() ([]byte, []int) { return fileDescriptorNewbie, []int{10} }

func (m *UserGameInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserGameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

// 批量获取用户注册渠道包和标签信息
type BatchGetUserRegPkgGameInfoReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetUserRegPkgGameInfoReq) Reset()         { *m = BatchGetUserRegPkgGameInfoReq{} }
func (m *BatchGetUserRegPkgGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserRegPkgGameInfoReq) ProtoMessage()    {}
func (*BatchGetUserRegPkgGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorNewbie, []int{11}
}

func (m *BatchGetUserRegPkgGameInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserRegPkgGameInfoResp struct {
	ChannelPkgList []*UserPkgInfo  `protobuf:"bytes,1,rep,name=channel_pkg_list,json=channelPkgList" json:"channel_pkg_list,omitempty"`
	GameNameList   []*UserGameInfo `protobuf:"bytes,2,rep,name=game_name_list,json=gameNameList" json:"game_name_list,omitempty"`
}

func (m *BatchGetUserRegPkgGameInfoResp) Reset()         { *m = BatchGetUserRegPkgGameInfoResp{} }
func (m *BatchGetUserRegPkgGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserRegPkgGameInfoResp) ProtoMessage()    {}
func (*BatchGetUserRegPkgGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorNewbie, []int{12}
}

func (m *BatchGetUserRegPkgGameInfoResp) GetChannelPkgList() []*UserPkgInfo {
	if m != nil {
		return m.ChannelPkgList
	}
	return nil
}

func (m *BatchGetUserRegPkgGameInfoResp) GetGameNameList() []*UserGameInfo {
	if m != nil {
		return m.GameNameList
	}
	return nil
}

func init() {
	proto.RegisterType((*CheckDevRegCountReq)(nil), "newbie.CheckDevRegCountReq")
	proto.RegisterType((*CheckDevRegCountResp)(nil), "newbie.CheckDevRegCountResp")
	proto.RegisterType((*CheckUserRegDevReq)(nil), "newbie.CheckUserRegDevReq")
	proto.RegisterType((*CheckUserRegDevResp)(nil), "newbie.CheckUserRegDevResp")
	proto.RegisterType((*UpdateUserRegDevReq)(nil), "newbie.UpdateUserRegDevReq")
	proto.RegisterType((*UpdateUserRegDevResp)(nil), "newbie.UpdateUserRegDevResp")
	proto.RegisterType((*UserFirstLoginInfo)(nil), "newbie.UserFirstLoginInfo")
	proto.RegisterType((*CheckUserFirstLoginTimeReq)(nil), "newbie.CheckUserFirstLoginTimeReq")
	proto.RegisterType((*CheckUserFirstLoginTimeResp)(nil), "newbie.CheckUserFirstLoginTimeResp")
	proto.RegisterType((*UserPkgInfo)(nil), "newbie.UserPkgInfo")
	proto.RegisterType((*UserGameInfo)(nil), "newbie.UserGameInfo")
	proto.RegisterType((*BatchGetUserRegPkgGameInfoReq)(nil), "newbie.BatchGetUserRegPkgGameInfoReq")
	proto.RegisterType((*BatchGetUserRegPkgGameInfoResp)(nil), "newbie.BatchGetUserRegPkgGameInfoResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Newbie service

type NewbieClient interface {
	// 查询设备信息
	CheckDevRegCount(ctx context.Context, in *CheckDevRegCountReq, opts ...grpc.CallOption) (*CheckDevRegCountResp, error)
	// 用户注册信息
	CheckUserRegDev(ctx context.Context, in *CheckUserRegDevReq, opts ...grpc.CallOption) (*CheckUserRegDevResp, error)
	// 用户首次登录信息
	CheckUserFirstLoginTime(ctx context.Context, in *CheckUserFirstLoginTimeReq, opts ...grpc.CallOption) (*CheckUserFirstLoginTimeResp, error)
	// 修改用户注册的设备信息
	UpdateUserRegDev(ctx context.Context, in *UpdateUserRegDevReq, opts ...grpc.CallOption) (*UpdateUserRegDevResp, error)
	// 批量获取用户注册时的渠道包和游戏标签信息
	BatchGetUserRegPkgGameInfo(ctx context.Context, in *BatchGetUserRegPkgGameInfoReq, opts ...grpc.CallOption) (*BatchGetUserRegPkgGameInfoResp, error)
}

type newbieClient struct {
	cc *grpc.ClientConn
}

func NewNewbieClient(cc *grpc.ClientConn) NewbieClient {
	return &newbieClient{cc}
}

func (c *newbieClient) CheckDevRegCount(ctx context.Context, in *CheckDevRegCountReq, opts ...grpc.CallOption) (*CheckDevRegCountResp, error) {
	out := new(CheckDevRegCountResp)
	err := grpc.Invoke(ctx, "/newbie.newbie/CheckDevRegCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newbieClient) CheckUserRegDev(ctx context.Context, in *CheckUserRegDevReq, opts ...grpc.CallOption) (*CheckUserRegDevResp, error) {
	out := new(CheckUserRegDevResp)
	err := grpc.Invoke(ctx, "/newbie.newbie/CheckUserRegDev", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newbieClient) CheckUserFirstLoginTime(ctx context.Context, in *CheckUserFirstLoginTimeReq, opts ...grpc.CallOption) (*CheckUserFirstLoginTimeResp, error) {
	out := new(CheckUserFirstLoginTimeResp)
	err := grpc.Invoke(ctx, "/newbie.newbie/CheckUserFirstLoginTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newbieClient) UpdateUserRegDev(ctx context.Context, in *UpdateUserRegDevReq, opts ...grpc.CallOption) (*UpdateUserRegDevResp, error) {
	out := new(UpdateUserRegDevResp)
	err := grpc.Invoke(ctx, "/newbie.newbie/UpdateUserRegDev", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newbieClient) BatchGetUserRegPkgGameInfo(ctx context.Context, in *BatchGetUserRegPkgGameInfoReq, opts ...grpc.CallOption) (*BatchGetUserRegPkgGameInfoResp, error) {
	out := new(BatchGetUserRegPkgGameInfoResp)
	err := grpc.Invoke(ctx, "/newbie.newbie/BatchGetUserRegPkgGameInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Newbie service

type NewbieServer interface {
	// 查询设备信息
	CheckDevRegCount(context.Context, *CheckDevRegCountReq) (*CheckDevRegCountResp, error)
	// 用户注册信息
	CheckUserRegDev(context.Context, *CheckUserRegDevReq) (*CheckUserRegDevResp, error)
	// 用户首次登录信息
	CheckUserFirstLoginTime(context.Context, *CheckUserFirstLoginTimeReq) (*CheckUserFirstLoginTimeResp, error)
	// 修改用户注册的设备信息
	UpdateUserRegDev(context.Context, *UpdateUserRegDevReq) (*UpdateUserRegDevResp, error)
	// 批量获取用户注册时的渠道包和游戏标签信息
	BatchGetUserRegPkgGameInfo(context.Context, *BatchGetUserRegPkgGameInfoReq) (*BatchGetUserRegPkgGameInfoResp, error)
}

func RegisterNewbieServer(s *grpc.Server, srv NewbieServer) {
	s.RegisterService(&_Newbie_serviceDesc, srv)
}

func _Newbie_CheckDevRegCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckDevRegCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewbieServer).CheckDevRegCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/newbie.newbie/CheckDevRegCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewbieServer).CheckDevRegCount(ctx, req.(*CheckDevRegCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Newbie_CheckUserRegDev_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserRegDevReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewbieServer).CheckUserRegDev(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/newbie.newbie/CheckUserRegDev",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewbieServer).CheckUserRegDev(ctx, req.(*CheckUserRegDevReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Newbie_CheckUserFirstLoginTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserFirstLoginTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewbieServer).CheckUserFirstLoginTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/newbie.newbie/CheckUserFirstLoginTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewbieServer).CheckUserFirstLoginTime(ctx, req.(*CheckUserFirstLoginTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Newbie_UpdateUserRegDev_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserRegDevReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewbieServer).UpdateUserRegDev(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/newbie.newbie/UpdateUserRegDev",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewbieServer).UpdateUserRegDev(ctx, req.(*UpdateUserRegDevReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Newbie_BatchGetUserRegPkgGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserRegPkgGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewbieServer).BatchGetUserRegPkgGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/newbie.newbie/BatchGetUserRegPkgGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewbieServer).BatchGetUserRegPkgGameInfo(ctx, req.(*BatchGetUserRegPkgGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Newbie_serviceDesc = grpc.ServiceDesc{
	ServiceName: "newbie.newbie",
	HandlerType: (*NewbieServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckDevRegCount",
			Handler:    _Newbie_CheckDevRegCount_Handler,
		},
		{
			MethodName: "CheckUserRegDev",
			Handler:    _Newbie_CheckUserRegDev_Handler,
		},
		{
			MethodName: "CheckUserFirstLoginTime",
			Handler:    _Newbie_CheckUserFirstLoginTime_Handler,
		},
		{
			MethodName: "UpdateUserRegDev",
			Handler:    _Newbie_UpdateUserRegDev_Handler,
		},
		{
			MethodName: "BatchGetUserRegPkgGameInfo",
			Handler:    _Newbie_BatchGetUserRegPkgGameInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/newbie/newbie.proto",
}

func (m *CheckDevRegCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckDevRegCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(len(m.DevInfoHex)))
	i += copy(dAtA[i:], m.DevInfoHex)
	return i, nil
}

func (m *CheckDevRegCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckDevRegCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *CheckUserRegDevReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserRegDevReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CheckUserRegDevResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserRegDevResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(len(m.DevHex)))
	i += copy(dAtA[i:], m.DevHex)
	dAtA[i] = 0x10
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.DevCount))
	return i, nil
}

func (m *UpdateUserRegDevReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserRegDevReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x22
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(len(m.DevHex)))
	i += copy(dAtA[i:], m.DevHex)
	dAtA[i] = 0x28
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.DevCount))
	return i, nil
}

func (m *UpdateUserRegDevResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserRegDevResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UserFirstLoginInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserFirstLoginInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.MarketId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(len(m.DevHex)))
	i += copy(dAtA[i:], m.DevHex)
	dAtA[i] = 0x28
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.DevCount))
	dAtA[i] = 0x30
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.FirstLoginTs))
	return i, nil
}

func (m *CheckUserFirstLoginTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFirstLoginTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CheckUserFirstLoginTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFirstLoginTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.FirstLoginList) > 0 {
		for _, msg := range m.FirstLoginList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintNewbie(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UserPkgInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserPkgInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(len(m.ChannelPkg)))
	i += copy(dAtA[i:], m.ChannelPkg)
	return i, nil
}

func (m *UserGameInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserGameInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintNewbie(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	return i, nil
}

func (m *BatchGetUserRegPkgGameInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserRegPkgGameInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintNewbie(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetUserRegPkgGameInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserRegPkgGameInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelPkgList) > 0 {
		for _, msg := range m.ChannelPkgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintNewbie(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.GameNameList) > 0 {
		for _, msg := range m.GameNameList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintNewbie(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Newbie(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Newbie(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintNewbie(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CheckDevRegCountReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.DevInfoHex)
	n += 1 + l + sovNewbie(uint64(l))
	return n
}

func (m *CheckDevRegCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNewbie(uint64(m.Count))
	return n
}

func (m *CheckUserRegDevReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNewbie(uint64(m.Uid))
	return n
}

func (m *CheckUserRegDevResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.DevHex)
	n += 1 + l + sovNewbie(uint64(l))
	n += 1 + sovNewbie(uint64(m.DevCount))
	return n
}

func (m *UpdateUserRegDevReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNewbie(uint64(m.Uid))
	l = len(m.DevHex)
	n += 1 + l + sovNewbie(uint64(l))
	n += 1 + sovNewbie(uint64(m.DevCount))
	return n
}

func (m *UpdateUserRegDevResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UserFirstLoginInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNewbie(uint64(m.Uid))
	n += 1 + sovNewbie(uint64(m.AppId))
	n += 1 + sovNewbie(uint64(m.MarketId))
	l = len(m.DevHex)
	n += 1 + l + sovNewbie(uint64(l))
	n += 1 + sovNewbie(uint64(m.DevCount))
	n += 1 + sovNewbie(uint64(m.FirstLoginTs))
	return n
}

func (m *CheckUserFirstLoginTimeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNewbie(uint64(m.Uid))
	return n
}

func (m *CheckUserFirstLoginTimeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.FirstLoginList) > 0 {
		for _, e := range m.FirstLoginList {
			l = e.Size()
			n += 1 + l + sovNewbie(uint64(l))
		}
	}
	return n
}

func (m *UserPkgInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNewbie(uint64(m.Uid))
	l = len(m.ChannelPkg)
	n += 1 + l + sovNewbie(uint64(l))
	return n
}

func (m *UserGameInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovNewbie(uint64(m.Uid))
	l = len(m.GameName)
	n += 1 + l + sovNewbie(uint64(l))
	return n
}

func (m *BatchGetUserRegPkgGameInfoReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovNewbie(uint64(e))
		}
	}
	return n
}

func (m *BatchGetUserRegPkgGameInfoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelPkgList) > 0 {
		for _, e := range m.ChannelPkgList {
			l = e.Size()
			n += 1 + l + sovNewbie(uint64(l))
		}
	}
	if len(m.GameNameList) > 0 {
		for _, e := range m.GameNameList {
			l = e.Size()
			n += 1 + l + sovNewbie(uint64(l))
		}
	}
	return n
}

func sovNewbie(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozNewbie(x uint64) (n int) {
	return sovNewbie(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *CheckDevRegCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckDevRegCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckDevRegCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DevInfoHex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNewbie
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DevInfoHex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("dev_info_hex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckDevRegCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckDevRegCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckDevRegCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserRegDevReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserRegDevReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserRegDevReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserRegDevResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserRegDevResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserRegDevResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DevHex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNewbie
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DevHex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DevCount", wireType)
			}
			m.DevCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DevCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("dev_hex")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("dev_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserRegDevReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserRegDevReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserRegDevReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DevHex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNewbie
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DevHex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DevCount", wireType)
			}
			m.DevCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DevCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserRegDevResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserRegDevResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserRegDevResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserFirstLoginInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserFirstLoginInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserFirstLoginInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DevHex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNewbie
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DevHex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DevCount", wireType)
			}
			m.DevCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DevCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FirstLoginTs", wireType)
			}
			m.FirstLoginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FirstLoginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFirstLoginTimeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFirstLoginTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFirstLoginTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFirstLoginTimeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFirstLoginTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFirstLoginTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FirstLoginList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNewbie
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FirstLoginList = append(m.FirstLoginList, &UserFirstLoginInfo{})
			if err := m.FirstLoginList[len(m.FirstLoginList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserPkgInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserPkgInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserPkgInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelPkg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNewbie
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelPkg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_pkg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserGameInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserGameInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserGameInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthNewbie
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("game_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserRegPkgGameInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserRegPkgGameInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserRegPkgGameInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNewbie
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowNewbie
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthNewbie
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowNewbie
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserRegPkgGameInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserRegPkgGameInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserRegPkgGameInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelPkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNewbie
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelPkgList = append(m.ChannelPkgList, &UserPkgInfo{})
			if err := m.ChannelPkgList[len(m.ChannelPkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameNameList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthNewbie
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameNameList = append(m.GameNameList, &UserGameInfo{})
			if err := m.GameNameList[len(m.GameNameList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipNewbie(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthNewbie
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipNewbie(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowNewbie
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowNewbie
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthNewbie
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowNewbie
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipNewbie(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthNewbie = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowNewbie   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/newbie/newbie.proto", fileDescriptorNewbie) }

var fileDescriptorNewbie = []byte{
	// 755 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x54, 0x4d, 0x6f, 0xd3, 0x5a,
	0x10, 0xad, 0xe3, 0x26, 0x4d, 0xa7, 0x69, 0x5e, 0xde, 0x6d, 0x5f, 0x3f, 0x9c, 0xd7, 0xe0, 0x1a,
	0x5a, 0x2a, 0x44, 0x5a, 0x11, 0xb1, 0x8a, 0xda, 0x2c, 0xda, 0x8a, 0x36, 0x52, 0x85, 0xaa, 0xa8,
	0x15, 0xcb, 0xc8, 0xb5, 0x6f, 0x9c, 0x2b, 0xc7, 0xf6, 0x6d, 0xae, 0x1d, 0xc2, 0x02, 0x89, 0x25,
	0x02, 0x21, 0x21, 0x58, 0xb2, 0xcd, 0x8f, 0xe9, 0x92, 0x35, 0x0b, 0x84, 0xca, 0x26, 0xff, 0x02,
	0x74, 0x1d, 0x27, 0x71, 0x3e, 0xe9, 0x82, 0x4d, 0x22, 0xcf, 0xcc, 0x99, 0x73, 0x66, 0x3c, 0x3e,
	0xb0, 0xca, 0xea, 0xda, 0x9e, 0x8d, 0x5f, 0x5e, 0x11, 0x1c, 0xfc, 0xed, 0xd2, 0xba, 0xe3, 0x3a,
	0x28, 0xd6, 0x79, 0x92, 0x1e, 0x68, 0x8e, 0x65, 0x39, 0xf6, 0x9e, 0x5b, 0x6b, 0x50, 0xa2, 0x99,
	0x35, 0xbc, 0xc7, 0xcc, 0x2b, 0x8f, 0xd4, 0x5c, 0x62, 0xbb, 0xaf, 0x68, 0x50, 0xad, 0x1c, 0xc0,
	0xd2, 0x51, 0x15, 0x6b, 0xe6, 0x31, 0x6e, 0x94, 0xb0, 0x71, 0xe4, 0x78, 0xb6, 0x5b, 0xc2, 0xd7,
	0x68, 0x1b, 0x12, 0x3a, 0x6e, 0x94, 0x89, 0x5d, 0x71, 0xca, 0x55, 0xdc, 0x5c, 0x13, 0xe4, 0xc8,
	0xce, 0xfc, 0xe1, 0xec, 0xcd, 0xf7, 0x7b, 0x33, 0x25, 0xd0, 0x71, 0xa3, 0x68, 0x57, 0x9c, 0x53,
	0xdc, 0x54, 0x72, 0xb0, 0x3c, 0x0a, 0x67, 0x14, 0x49, 0x10, 0xd5, 0xf8, 0x83, 0x0f, 0x5c, 0x0c,
	0x80, 0x9d, 0x90, 0xf2, 0x18, 0x90, 0x8f, 0xb9, 0x64, 0xb8, 0x5e, 0xc2, 0x86, 0x0f, 0xbd, 0x46,
	0x2b, 0x20, 0x7a, 0x44, 0x1f, 0xa8, 0xe7, 0x01, 0xe5, 0x45, 0x20, 0x30, 0x5c, 0xcd, 0x28, 0xda,
	0x80, 0x39, 0x2e, 0x70, 0x58, 0x5b, 0x4c, 0xc7, 0x8d, 0x53, 0xdc, 0x44, 0x9b, 0x30, 0xcf, 0xd3,
	0x1d, 0x0d, 0x91, 0x50, 0xcf, 0xb8, 0x8e, 0x1b, 0xbe, 0x4c, 0xc5, 0x81, 0xa5, 0x4b, 0xaa, 0xab,
	0x2e, 0xbe, 0x93, 0x8e, 0x30, 0xe1, 0xac, 0x2c, 0x4c, 0x27, 0x8c, 0xca, 0xc2, 0x18, 0xc2, 0x15,
	0x58, 0x1e, 0x25, 0x64, 0x54, 0xf9, 0x26, 0x00, 0xe2, 0xa1, 0x67, 0xa4, 0xce, 0xdc, 0x33, 0xc7,
	0x20, 0x36, 0xdf, 0xee, 0x44, 0x21, 0x69, 0x88, 0xa9, 0x94, 0x96, 0x89, 0xbe, 0x16, 0x09, 0xd1,
	0x44, 0x55, 0x4a, 0x8b, 0x3a, 0x97, 0x61, 0xa9, 0x75, 0x13, 0xbb, 0x3c, 0x2f, 0x86, 0x65, 0x74,
	0xc2, 0xc5, 0xbf, 0x30, 0x08, 0x7a, 0x04, 0xc9, 0x0a, 0xd7, 0x5a, 0xae, 0x71, 0xb1, 0x65, 0x97,
	0xad, 0xc5, 0x42, 0x75, 0x89, 0x4a, 0x6f, 0x8e, 0x0b, 0xa6, 0x3c, 0x05, 0xa9, 0xf7, 0xfa, 0xfa,
	0x03, 0x5e, 0x10, 0x0b, 0x4f, 0x7b, 0xe9, 0x1a, 0xa4, 0x27, 0xa2, 0x18, 0x45, 0xc7, 0x90, 0x0a,
	0x0b, 0xa8, 0x11, 0xc6, 0x0f, 0x4d, 0xdc, 0x59, 0xc8, 0x49, 0xbb, 0xc1, 0xb7, 0x30, 0xba, 0xd0,
	0x52, 0xb2, 0x2f, 0xec, 0x8c, 0x30, 0x57, 0x39, 0x83, 0x05, 0x5e, 0x75, 0x6e, 0x1a, 0x53, 0xf7,
	0xbd, 0x05, 0x0b, 0x5a, 0x55, 0xb5, 0x6d, 0x5c, 0x2b, 0x53, 0xd3, 0xf0, 0x8f, 0xa9, 0xf7, 0x25,
	0x04, 0x89, 0x73, 0xd3, 0x50, 0x8a, 0x90, 0xe0, 0xdd, 0x4e, 0x54, 0x0b, 0x4f, 0x6d, 0xb7, 0x09,
	0xf3, 0x86, 0x6a, 0xe1, 0xb2, 0xad, 0x5a, 0x78, 0xa0, 0x59, 0x9c, 0x87, 0x9f, 0xab, 0x16, 0x56,
	0xf2, 0xb0, 0x71, 0xa8, 0xba, 0x5a, 0xf5, 0x04, 0xbb, 0xc1, 0xa9, 0x9c, 0x9b, 0x46, 0xb7, 0x31,
	0x5f, 0xdb, 0x3a, 0xc4, 0x3d, 0xa2, 0xf7, 0xe7, 0x5e, 0x2c, 0xcd, 0x79, 0x44, 0xf7, 0x87, 0xfa,
	0x22, 0x40, 0x66, 0x1a, 0x98, 0x51, 0x74, 0x00, 0xa9, 0xd0, 0x40, 0xe1, 0xed, 0x2d, 0x85, 0xb7,
	0x17, 0xec, 0xa5, 0x94, 0xec, 0x0f, 0xc9, 0x19, 0x50, 0x1e, 0x92, 0xbd, 0x01, 0x3a, 0xe0, 0x88,
	0x0f, 0x5e, 0x0e, 0x83, 0x7b, 0x84, 0x89, 0xee, 0x54, 0x1c, 0x9b, 0xfb, 0x10, 0x85, 0xc0, 0x9e,
	0xd0, 0x35, 0xa4, 0x86, 0x9d, 0x03, 0xa5, 0xbb, 0x2d, 0xc6, 0x58, 0x92, 0xf4, 0xff, 0xe4, 0x24,
	0xa3, 0x8a, 0xf2, 0xa6, 0xd5, 0x16, 0x85, 0x77, 0xad, 0xb6, 0x18, 0x69, 0xe6, 0x3f, 0xb5, 0xda,
	0xe2, 0xbf, 0xd9, 0xe6, 0x7e, 0xd8, 0xc2, 0x0a, 0x32, 0xc2, 0xf0, 0xcf, 0x90, 0x95, 0x20, 0x69,
	0xa0, 0xe9, 0x80, 0x13, 0x48, 0xe9, 0x89, 0x39, 0x46, 0x95, 0x75, 0xce, 0x17, 0xf1, 0xf9, 0x3c,
	0x9f, 0x2f, 0x9e, 0xf5, 0xf6, 0x3d, 0xa2, 0x17, 0x64, 0xf4, 0x1a, 0x56, 0x27, 0x1c, 0x2f, 0x52,
	0x46, 0x5a, 0x8e, 0x7c, 0x13, 0xd2, 0xfd, 0x3f, 0xd6, 0x74, 0xe9, 0xc5, 0xb1, 0xf4, 0xef, 0x05,
	0x48, 0x0d, 0xfb, 0x4c, 0x7f, 0xb3, 0x63, 0x2c, 0xaf, 0xbf, 0xd9, 0xb1, 0xf6, 0xb4, 0xcf, 0xa9,
	0x80, 0x53, 0xc5, 0xbc, 0x7c, 0x33, 0x6f, 0xfb, 0x74, 0x0f, 0xbb, 0x74, 0x23, 0x5b, 0xce, 0xda,
	0x3c, 0x20, 0xfb, 0xfe, 0x51, 0x90, 0xd1, 0x67, 0x01, 0xa4, 0xc9, 0xf7, 0x88, 0xb6, 0xba, 0xd4,
	0x53, 0x0f, 0x5e, 0xda, 0xbe, 0x4b, 0x19, 0xa3, 0xca, 0x36, 0xd7, 0x9a, 0x08, 0x5d, 0xc1, 0x7f,
	0xd9, 0x26, 0xd7, 0xc9, 0xcf, 0x54, 0xf6, 0x88, 0xfe, 0x84, 0xff, 0xe4, 0x0a, 0x52, 0xec, 0x6d,
	0xab, 0x2d, 0xfe, 0x6a, 0x1c, 0xa6, 0x6e, 0x6e, 0x33, 0xc2, 0xd7, 0xdb, 0x8c, 0xf0, 0xe3, 0x36,
	0x23, 0x7c, 0xfc, 0x99, 0x99, 0xf9, 0x1d, 0x00, 0x00, 0xff, 0xff, 0x46, 0xf4, 0x50, 0x9a, 0x57,
	0x07, 0x00, 0x00,
}
