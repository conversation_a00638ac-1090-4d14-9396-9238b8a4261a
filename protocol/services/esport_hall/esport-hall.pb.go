// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/esport-hall/esport-hall.proto

package esport_hall // import "golang.52tt.com/protocol/services/esport_hall"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// buf:lint:ignore ENUM_PASCAL_CASE
type RECOMMEND_TYPE int32

const (
	RECOMMEND_TYPE_RECOMMEND_TYPE_UNKNOWN RECOMMEND_TYPE = 0
	RECOMMEND_TYPE_RECOMMEND_TYPE_EFFECT  RECOMMEND_TYPE = 1
	RECOMMEND_TYPE_RECOMMEND_TYPE_WAIT    RECOMMEND_TYPE = 2
	RECOMMEND_TYPE_RECOMMEND_TYPE_EXPIRE  RECOMMEND_TYPE = 3
)

var RECOMMEND_TYPE_name = map[int32]string{
	0: "RECOMMEND_TYPE_UNKNOWN",
	1: "RECOMMEND_TYPE_EFFECT",
	2: "RECOMMEND_TYPE_WAIT",
	3: "RECOMMEND_TYPE_EXPIRE",
}
var RECOMMEND_TYPE_value = map[string]int32{
	"RECOMMEND_TYPE_UNKNOWN": 0,
	"RECOMMEND_TYPE_EFFECT":  1,
	"RECOMMEND_TYPE_WAIT":    2,
	"RECOMMEND_TYPE_EXPIRE":  3,
}

func (x RECOMMEND_TYPE) String() string {
	return proto.EnumName(RECOMMEND_TYPE_name, int32(x))
}
func (RECOMMEND_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{0}
}

type IgnoreReCoachOnUGCOperation int32

const (
	IgnoreReCoachOnUGCOperation_IGNORE_RE_COACH_ON_UGC_OPERATION_UNSPECIFIED IgnoreReCoachOnUGCOperation = 0
	IgnoreReCoachOnUGCOperation_IGNORE_RE_COACH_ON_UGC_OPERATION_UNLIKE      IgnoreReCoachOnUGCOperation = 1
)

var IgnoreReCoachOnUGCOperation_name = map[int32]string{
	0: "IGNORE_RE_COACH_ON_UGC_OPERATION_UNSPECIFIED",
	1: "IGNORE_RE_COACH_ON_UGC_OPERATION_UNLIKE",
}
var IgnoreReCoachOnUGCOperation_value = map[string]int32{
	"IGNORE_RE_COACH_ON_UGC_OPERATION_UNSPECIFIED": 0,
	"IGNORE_RE_COACH_ON_UGC_OPERATION_UNLIKE":      1,
}

func (x IgnoreReCoachOnUGCOperation) String() string {
	return proto.EnumName(IgnoreReCoachOnUGCOperation_name, int32(x))
}
func (IgnoreReCoachOnUGCOperation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{1}
}

type GetGameCoachListRequest_ReqSource int32

const (
	GetGameCoachListRequest_REQ_SOURCE_UNSPECIFIED GetGameCoachListRequest_ReqSource = 0
	GetGameCoachListRequest_REQ_SOURCE_ESPORT_AREA GetGameCoachListRequest_ReqSource = 1
)

var GetGameCoachListRequest_ReqSource_name = map[int32]string{
	0: "REQ_SOURCE_UNSPECIFIED",
	1: "REQ_SOURCE_ESPORT_AREA",
}
var GetGameCoachListRequest_ReqSource_value = map[string]int32{
	"REQ_SOURCE_UNSPECIFIED": 0,
	"REQ_SOURCE_ESPORT_AREA": 1,
}

func (x GetGameCoachListRequest_ReqSource) String() string {
	return proto.EnumName(GetGameCoachListRequest_ReqSource_name, int32(x))
}
func (GetGameCoachListRequest_ReqSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{18, 0}
}

type GameProperty_PropertyType int32

const (
	GameProperty_PROPERTY_TYPE_UNSPECIFIED GameProperty_PropertyType = 0
	GameProperty_PROPERTY_TYPE_GENDER      GameProperty_PropertyType = 1
	GameProperty_PROPERTY_TYPE_PRICE       GameProperty_PropertyType = 2
	GameProperty_PROPERTY_TYPE_CUSTOM      GameProperty_PropertyType = 3
	GameProperty_PROPERTY_TYPE_LABEL       GameProperty_PropertyType = 4
)

var GameProperty_PropertyType_name = map[int32]string{
	0: "PROPERTY_TYPE_UNSPECIFIED",
	1: "PROPERTY_TYPE_GENDER",
	2: "PROPERTY_TYPE_PRICE",
	3: "PROPERTY_TYPE_CUSTOM",
	4: "PROPERTY_TYPE_LABEL",
}
var GameProperty_PropertyType_value = map[string]int32{
	"PROPERTY_TYPE_UNSPECIFIED": 0,
	"PROPERTY_TYPE_GENDER":      1,
	"PROPERTY_TYPE_PRICE":       2,
	"PROPERTY_TYPE_CUSTOM":      3,
	"PROPERTY_TYPE_LABEL":       4,
}

func (x GameProperty_PropertyType) String() string {
	return proto.EnumName(GameProperty_PropertyType_name, int32(x))
}
func (GameProperty_PropertyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{54, 0}
}

type GameProperty_SelectType int32

const (
	GameProperty_SELECT_TYPE_UNSPECIFIED GameProperty_SelectType = 0
	GameProperty_SELECT_TYPE_SINGLE      GameProperty_SelectType = 1
	GameProperty_SELECT_TYPE_MULTI       GameProperty_SelectType = 2
)

var GameProperty_SelectType_name = map[int32]string{
	0: "SELECT_TYPE_UNSPECIFIED",
	1: "SELECT_TYPE_SINGLE",
	2: "SELECT_TYPE_MULTI",
}
var GameProperty_SelectType_value = map[string]int32{
	"SELECT_TYPE_UNSPECIFIED": 0,
	"SELECT_TYPE_SINGLE":      1,
	"SELECT_TYPE_MULTI":       2,
}

func (x GameProperty_SelectType) String() string {
	return proto.EnumName(GameProperty_SelectType_name, int32(x))
}
func (GameProperty_SelectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{54, 1}
}

type InitUserSkillInfoRequest struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SkillList            []*SkillInfo `protobuf:"bytes,2,rep,name=skill_list,json=skillList,proto3" json:"skill_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *InitUserSkillInfoRequest) Reset()         { *m = InitUserSkillInfoRequest{} }
func (m *InitUserSkillInfoRequest) String() string { return proto.CompactTextString(m) }
func (*InitUserSkillInfoRequest) ProtoMessage()    {}
func (*InitUserSkillInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{0}
}
func (m *InitUserSkillInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitUserSkillInfoRequest.Unmarshal(m, b)
}
func (m *InitUserSkillInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitUserSkillInfoRequest.Marshal(b, m, deterministic)
}
func (dst *InitUserSkillInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitUserSkillInfoRequest.Merge(dst, src)
}
func (m *InitUserSkillInfoRequest) XXX_Size() int {
	return xxx_messageInfo_InitUserSkillInfoRequest.Size(m)
}
func (m *InitUserSkillInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InitUserSkillInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InitUserSkillInfoRequest proto.InternalMessageInfo

func (m *InitUserSkillInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *InitUserSkillInfoRequest) GetSkillList() []*SkillInfo {
	if m != nil {
		return m.SkillList
	}
	return nil
}

type InitUserSkillInfoResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InitUserSkillInfoResponse) Reset()         { *m = InitUserSkillInfoResponse{} }
func (m *InitUserSkillInfoResponse) String() string { return proto.CompactTextString(m) }
func (*InitUserSkillInfoResponse) ProtoMessage()    {}
func (*InitUserSkillInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{1}
}
func (m *InitUserSkillInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitUserSkillInfoResponse.Unmarshal(m, b)
}
func (m *InitUserSkillInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitUserSkillInfoResponse.Marshal(b, m, deterministic)
}
func (dst *InitUserSkillInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitUserSkillInfoResponse.Merge(dst, src)
}
func (m *InitUserSkillInfoResponse) XXX_Size() int {
	return xxx_messageInfo_InitUserSkillInfoResponse.Size(m)
}
func (m *InitUserSkillInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InitUserSkillInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InitUserSkillInfoResponse proto.InternalMessageInfo

// 获取当前可见技能商品
type GetVisibleSkillProductListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	WithSort             bool     `protobuf:"varint,2,opt,name=with_sort,json=withSort,proto3" json:"with_sort,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVisibleSkillProductListRequest) Reset()         { *m = GetVisibleSkillProductListRequest{} }
func (m *GetVisibleSkillProductListRequest) String() string { return proto.CompactTextString(m) }
func (*GetVisibleSkillProductListRequest) ProtoMessage()    {}
func (*GetVisibleSkillProductListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{2}
}
func (m *GetVisibleSkillProductListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVisibleSkillProductListRequest.Unmarshal(m, b)
}
func (m *GetVisibleSkillProductListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVisibleSkillProductListRequest.Marshal(b, m, deterministic)
}
func (dst *GetVisibleSkillProductListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVisibleSkillProductListRequest.Merge(dst, src)
}
func (m *GetVisibleSkillProductListRequest) XXX_Size() int {
	return xxx_messageInfo_GetVisibleSkillProductListRequest.Size(m)
}
func (m *GetVisibleSkillProductListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVisibleSkillProductListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVisibleSkillProductListRequest proto.InternalMessageInfo

func (m *GetVisibleSkillProductListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetVisibleSkillProductListRequest) GetWithSort() bool {
	if m != nil {
		return m.WithSort
	}
	return false
}

type GetVisibleSkillProductListResponse struct {
	ProductList          []*SkillProduct `protobuf:"bytes,1,rep,name=product_list,json=productList,proto3" json:"product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetVisibleSkillProductListResponse) Reset()         { *m = GetVisibleSkillProductListResponse{} }
func (m *GetVisibleSkillProductListResponse) String() string { return proto.CompactTextString(m) }
func (*GetVisibleSkillProductListResponse) ProtoMessage()    {}
func (*GetVisibleSkillProductListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{3}
}
func (m *GetVisibleSkillProductListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVisibleSkillProductListResponse.Unmarshal(m, b)
}
func (m *GetVisibleSkillProductListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVisibleSkillProductListResponse.Marshal(b, m, deterministic)
}
func (dst *GetVisibleSkillProductListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVisibleSkillProductListResponse.Merge(dst, src)
}
func (m *GetVisibleSkillProductListResponse) XXX_Size() int {
	return xxx_messageInfo_GetVisibleSkillProductListResponse.Size(m)
}
func (m *GetVisibleSkillProductListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVisibleSkillProductListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVisibleSkillProductListResponse proto.InternalMessageInfo

func (m *GetVisibleSkillProductListResponse) GetProductList() []*SkillProduct {
	if m != nil {
		return m.ProductList
	}
	return nil
}

// 获取所有技能
type GetAllSkillListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllSkillListRequest) Reset()         { *m = GetAllSkillListRequest{} }
func (m *GetAllSkillListRequest) String() string { return proto.CompactTextString(m) }
func (*GetAllSkillListRequest) ProtoMessage()    {}
func (*GetAllSkillListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{4}
}
func (m *GetAllSkillListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSkillListRequest.Unmarshal(m, b)
}
func (m *GetAllSkillListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSkillListRequest.Marshal(b, m, deterministic)
}
func (dst *GetAllSkillListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSkillListRequest.Merge(dst, src)
}
func (m *GetAllSkillListRequest) XXX_Size() int {
	return xxx_messageInfo_GetAllSkillListRequest.Size(m)
}
func (m *GetAllSkillListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSkillListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSkillListRequest proto.InternalMessageInfo

func (m *GetAllSkillListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAllSkillListResponse struct {
	ProductList          []*SkillProduct `protobuf:"bytes,1,rep,name=product_list,json=productList,proto3" json:"product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllSkillListResponse) Reset()         { *m = GetAllSkillListResponse{} }
func (m *GetAllSkillListResponse) String() string { return proto.CompactTextString(m) }
func (*GetAllSkillListResponse) ProtoMessage()    {}
func (*GetAllSkillListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{5}
}
func (m *GetAllSkillListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSkillListResponse.Unmarshal(m, b)
}
func (m *GetAllSkillListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSkillListResponse.Marshal(b, m, deterministic)
}
func (dst *GetAllSkillListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSkillListResponse.Merge(dst, src)
}
func (m *GetAllSkillListResponse) XXX_Size() int {
	return xxx_messageInfo_GetAllSkillListResponse.Size(m)
}
func (m *GetAllSkillListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSkillListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSkillListResponse proto.InternalMessageInfo

func (m *GetAllSkillListResponse) GetProductList() []*SkillProduct {
	if m != nil {
		return m.ProductList
	}
	return nil
}

// 开启/关闭接单
type SetSkillReceiveSwitchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SkillId              uint32   `protobuf:"varint,2,opt,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
	Switch               bool     `protobuf:"varint,3,opt,name=switch,proto3" json:"switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSkillReceiveSwitchRequest) Reset()         { *m = SetSkillReceiveSwitchRequest{} }
func (m *SetSkillReceiveSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetSkillReceiveSwitchRequest) ProtoMessage()    {}
func (*SetSkillReceiveSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{6}
}
func (m *SetSkillReceiveSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSkillReceiveSwitchRequest.Unmarshal(m, b)
}
func (m *SetSkillReceiveSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSkillReceiveSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetSkillReceiveSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSkillReceiveSwitchRequest.Merge(dst, src)
}
func (m *SetSkillReceiveSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetSkillReceiveSwitchRequest.Size(m)
}
func (m *SetSkillReceiveSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSkillReceiveSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetSkillReceiveSwitchRequest proto.InternalMessageInfo

func (m *SetSkillReceiveSwitchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetSkillReceiveSwitchRequest) GetSkillId() uint32 {
	if m != nil {
		return m.SkillId
	}
	return 0
}

func (m *SetSkillReceiveSwitchRequest) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

type SetSkillReceiveSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSkillReceiveSwitchResponse) Reset()         { *m = SetSkillReceiveSwitchResponse{} }
func (m *SetSkillReceiveSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetSkillReceiveSwitchResponse) ProtoMessage()    {}
func (*SetSkillReceiveSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{7}
}
func (m *SetSkillReceiveSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSkillReceiveSwitchResponse.Unmarshal(m, b)
}
func (m *SetSkillReceiveSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSkillReceiveSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetSkillReceiveSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSkillReceiveSwitchResponse.Merge(dst, src)
}
func (m *SetSkillReceiveSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetSkillReceiveSwitchResponse.Size(m)
}
func (m *SetSkillReceiveSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSkillReceiveSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetSkillReceiveSwitchResponse proto.InternalMessageInfo

// 获取接单时间
type GetReceiveTimeFrameRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReceiveTimeFrameRequest) Reset()         { *m = GetReceiveTimeFrameRequest{} }
func (m *GetReceiveTimeFrameRequest) String() string { return proto.CompactTextString(m) }
func (*GetReceiveTimeFrameRequest) ProtoMessage()    {}
func (*GetReceiveTimeFrameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{8}
}
func (m *GetReceiveTimeFrameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiveTimeFrameRequest.Unmarshal(m, b)
}
func (m *GetReceiveTimeFrameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiveTimeFrameRequest.Marshal(b, m, deterministic)
}
func (dst *GetReceiveTimeFrameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiveTimeFrameRequest.Merge(dst, src)
}
func (m *GetReceiveTimeFrameRequest) XXX_Size() int {
	return xxx_messageInfo_GetReceiveTimeFrameRequest.Size(m)
}
func (m *GetReceiveTimeFrameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiveTimeFrameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiveTimeFrameRequest proto.InternalMessageInfo

func (m *GetReceiveTimeFrameRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetReceiveTimeFrameResponse struct {
	StartTime            uint32   `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	DayOfWeek            []bool   `protobuf:"varint,4,rep,packed,name=day_of_week,json=dayOfWeek,proto3" json:"day_of_week,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReceiveTimeFrameResponse) Reset()         { *m = GetReceiveTimeFrameResponse{} }
func (m *GetReceiveTimeFrameResponse) String() string { return proto.CompactTextString(m) }
func (*GetReceiveTimeFrameResponse) ProtoMessage()    {}
func (*GetReceiveTimeFrameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{9}
}
func (m *GetReceiveTimeFrameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiveTimeFrameResponse.Unmarshal(m, b)
}
func (m *GetReceiveTimeFrameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiveTimeFrameResponse.Marshal(b, m, deterministic)
}
func (dst *GetReceiveTimeFrameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiveTimeFrameResponse.Merge(dst, src)
}
func (m *GetReceiveTimeFrameResponse) XXX_Size() int {
	return xxx_messageInfo_GetReceiveTimeFrameResponse.Size(m)
}
func (m *GetReceiveTimeFrameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiveTimeFrameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiveTimeFrameResponse proto.InternalMessageInfo

func (m *GetReceiveTimeFrameResponse) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetReceiveTimeFrameResponse) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetReceiveTimeFrameResponse) GetDayOfWeek() []bool {
	if m != nil {
		return m.DayOfWeek
	}
	return nil
}

// 设置接单时间
type SetReceiveTimeFrameRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	StartTime            uint32   `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	DayOfWeek            []bool   `protobuf:"varint,4,rep,packed,name=day_of_week,json=dayOfWeek,proto3" json:"day_of_week,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetReceiveTimeFrameRequest) Reset()         { *m = SetReceiveTimeFrameRequest{} }
func (m *SetReceiveTimeFrameRequest) String() string { return proto.CompactTextString(m) }
func (*SetReceiveTimeFrameRequest) ProtoMessage()    {}
func (*SetReceiveTimeFrameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{10}
}
func (m *SetReceiveTimeFrameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetReceiveTimeFrameRequest.Unmarshal(m, b)
}
func (m *SetReceiveTimeFrameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetReceiveTimeFrameRequest.Marshal(b, m, deterministic)
}
func (dst *SetReceiveTimeFrameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetReceiveTimeFrameRequest.Merge(dst, src)
}
func (m *SetReceiveTimeFrameRequest) XXX_Size() int {
	return xxx_messageInfo_SetReceiveTimeFrameRequest.Size(m)
}
func (m *SetReceiveTimeFrameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetReceiveTimeFrameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetReceiveTimeFrameRequest proto.InternalMessageInfo

func (m *SetReceiveTimeFrameRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetReceiveTimeFrameRequest) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *SetReceiveTimeFrameRequest) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SetReceiveTimeFrameRequest) GetDayOfWeek() []bool {
	if m != nil {
		return m.DayOfWeek
	}
	return nil
}

type SetReceiveTimeFrameResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetReceiveTimeFrameResponse) Reset()         { *m = SetReceiveTimeFrameResponse{} }
func (m *SetReceiveTimeFrameResponse) String() string { return proto.CompactTextString(m) }
func (*SetReceiveTimeFrameResponse) ProtoMessage()    {}
func (*SetReceiveTimeFrameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{11}
}
func (m *SetReceiveTimeFrameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetReceiveTimeFrameResponse.Unmarshal(m, b)
}
func (m *SetReceiveTimeFrameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetReceiveTimeFrameResponse.Marshal(b, m, deterministic)
}
func (dst *SetReceiveTimeFrameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetReceiveTimeFrameResponse.Merge(dst, src)
}
func (m *SetReceiveTimeFrameResponse) XXX_Size() int {
	return xxx_messageInfo_SetReceiveTimeFrameResponse.Size(m)
}
func (m *SetReceiveTimeFrameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetReceiveTimeFrameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetReceiveTimeFrameResponse proto.InternalMessageInfo

// 设置技能价格
type SetSkillPriceRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SkillId              uint32   `protobuf:"varint,2,opt,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
	Price                uint32   `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSkillPriceRequest) Reset()         { *m = SetSkillPriceRequest{} }
func (m *SetSkillPriceRequest) String() string { return proto.CompactTextString(m) }
func (*SetSkillPriceRequest) ProtoMessage()    {}
func (*SetSkillPriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{12}
}
func (m *SetSkillPriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSkillPriceRequest.Unmarshal(m, b)
}
func (m *SetSkillPriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSkillPriceRequest.Marshal(b, m, deterministic)
}
func (dst *SetSkillPriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSkillPriceRequest.Merge(dst, src)
}
func (m *SetSkillPriceRequest) XXX_Size() int {
	return xxx_messageInfo_SetSkillPriceRequest.Size(m)
}
func (m *SetSkillPriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSkillPriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetSkillPriceRequest proto.InternalMessageInfo

func (m *SetSkillPriceRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetSkillPriceRequest) GetSkillId() uint32 {
	if m != nil {
		return m.SkillId
	}
	return 0
}

func (m *SetSkillPriceRequest) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

type SetSkillPriceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSkillPriceResponse) Reset()         { *m = SetSkillPriceResponse{} }
func (m *SetSkillPriceResponse) String() string { return proto.CompactTextString(m) }
func (*SetSkillPriceResponse) ProtoMessage()    {}
func (*SetSkillPriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{13}
}
func (m *SetSkillPriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSkillPriceResponse.Unmarshal(m, b)
}
func (m *SetSkillPriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSkillPriceResponse.Marshal(b, m, deterministic)
}
func (dst *SetSkillPriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSkillPriceResponse.Merge(dst, src)
}
func (m *SetSkillPriceResponse) XXX_Size() int {
	return xxx_messageInfo_SetSkillPriceResponse.Size(m)
}
func (m *SetSkillPriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSkillPriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetSkillPriceResponse proto.InternalMessageInfo

// 获取商品信息
type GetSkillProductInfoRequest struct {
	ProductId            uint32   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	InviteId             uint32   `protobuf:"varint,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	CoachUid             uint32   `protobuf:"varint,3,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSkillProductInfoRequest) Reset()         { *m = GetSkillProductInfoRequest{} }
func (m *GetSkillProductInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetSkillProductInfoRequest) ProtoMessage()    {}
func (*GetSkillProductInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{14}
}
func (m *GetSkillProductInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSkillProductInfoRequest.Unmarshal(m, b)
}
func (m *GetSkillProductInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSkillProductInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetSkillProductInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSkillProductInfoRequest.Merge(dst, src)
}
func (m *GetSkillProductInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetSkillProductInfoRequest.Size(m)
}
func (m *GetSkillProductInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSkillProductInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSkillProductInfoRequest proto.InternalMessageInfo

func (m *GetSkillProductInfoRequest) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetSkillProductInfoRequest) GetInviteId() uint32 {
	if m != nil {
		return m.InviteId
	}
	return 0
}

func (m *GetSkillProductInfoRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type GetSkillProductInfoResponse struct {
	Product              *SkillProduct `protobuf:"bytes,1,opt,name=product,proto3" json:"product,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSkillProductInfoResponse) Reset()         { *m = GetSkillProductInfoResponse{} }
func (m *GetSkillProductInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetSkillProductInfoResponse) ProtoMessage()    {}
func (*GetSkillProductInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{15}
}
func (m *GetSkillProductInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSkillProductInfoResponse.Unmarshal(m, b)
}
func (m *GetSkillProductInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSkillProductInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetSkillProductInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSkillProductInfoResponse.Merge(dst, src)
}
func (m *GetSkillProductInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetSkillProductInfoResponse.Size(m)
}
func (m *GetSkillProductInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSkillProductInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSkillProductInfoResponse proto.InternalMessageInfo

func (m *GetSkillProductInfoResponse) GetProduct() *SkillProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

// 通过gameId获取商品信息
type GetSkillProductInfoByGameIdRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSkillProductInfoByGameIdRequest) Reset()         { *m = GetSkillProductInfoByGameIdRequest{} }
func (m *GetSkillProductInfoByGameIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetSkillProductInfoByGameIdRequest) ProtoMessage()    {}
func (*GetSkillProductInfoByGameIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{16}
}
func (m *GetSkillProductInfoByGameIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSkillProductInfoByGameIdRequest.Unmarshal(m, b)
}
func (m *GetSkillProductInfoByGameIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSkillProductInfoByGameIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetSkillProductInfoByGameIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSkillProductInfoByGameIdRequest.Merge(dst, src)
}
func (m *GetSkillProductInfoByGameIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetSkillProductInfoByGameIdRequest.Size(m)
}
func (m *GetSkillProductInfoByGameIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSkillProductInfoByGameIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSkillProductInfoByGameIdRequest proto.InternalMessageInfo

func (m *GetSkillProductInfoByGameIdRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetSkillProductInfoByGameIdRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type GetSkillProductInfoByGameIdResponse struct {
	Product              *SkillProduct `protobuf:"bytes,1,opt,name=product,proto3" json:"product,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSkillProductInfoByGameIdResponse) Reset()         { *m = GetSkillProductInfoByGameIdResponse{} }
func (m *GetSkillProductInfoByGameIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetSkillProductInfoByGameIdResponse) ProtoMessage()    {}
func (*GetSkillProductInfoByGameIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{17}
}
func (m *GetSkillProductInfoByGameIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSkillProductInfoByGameIdResponse.Unmarshal(m, b)
}
func (m *GetSkillProductInfoByGameIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSkillProductInfoByGameIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetSkillProductInfoByGameIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSkillProductInfoByGameIdResponse.Merge(dst, src)
}
func (m *GetSkillProductInfoByGameIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetSkillProductInfoByGameIdResponse.Size(m)
}
func (m *GetSkillProductInfoByGameIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSkillProductInfoByGameIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSkillProductInfoByGameIdResponse proto.InternalMessageInfo

func (m *GetSkillProductInfoByGameIdResponse) GetProduct() *SkillProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

// 获取某款游戏的教练列表
type GetGameCoachListRequest struct {
	GameId               uint32          `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Offset               uint32          `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	PropertyList         []*GameProperty `protobuf:"bytes,4,rep,name=property_list,json=propertyList,proto3" json:"property_list,omitempty"`
	ReqSource            uint32          `protobuf:"varint,5,opt,name=req_source,json=reqSource,proto3" json:"req_source,omitempty"`
	ExposeType           uint32          `protobuf:"varint,6,opt,name=expose_type,json=exposeType,proto3" json:"expose_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGameCoachListRequest) Reset()         { *m = GetGameCoachListRequest{} }
func (m *GetGameCoachListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGameCoachListRequest) ProtoMessage()    {}
func (*GetGameCoachListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{18}
}
func (m *GetGameCoachListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameCoachListRequest.Unmarshal(m, b)
}
func (m *GetGameCoachListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameCoachListRequest.Marshal(b, m, deterministic)
}
func (dst *GetGameCoachListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameCoachListRequest.Merge(dst, src)
}
func (m *GetGameCoachListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGameCoachListRequest.Size(m)
}
func (m *GetGameCoachListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameCoachListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameCoachListRequest proto.InternalMessageInfo

func (m *GetGameCoachListRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGameCoachListRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGameCoachListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGameCoachListRequest) GetPropertyList() []*GameProperty {
	if m != nil {
		return m.PropertyList
	}
	return nil
}

func (m *GetGameCoachListRequest) GetReqSource() uint32 {
	if m != nil {
		return m.ReqSource
	}
	return 0
}

func (m *GetGameCoachListRequest) GetExposeType() uint32 {
	if m != nil {
		return m.ExposeType
	}
	return 0
}

type GetCoachListResponse struct {
	CoachList            []*GameCoachInfo `protobuf:"bytes,1,rep,name=coach_list,json=coachList,proto3" json:"coach_list,omitempty"`
	NextOffset           uint32           `protobuf:"varint,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetCoachListResponse) Reset()         { *m = GetCoachListResponse{} }
func (m *GetCoachListResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachListResponse) ProtoMessage()    {}
func (*GetCoachListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{19}
}
func (m *GetCoachListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachListResponse.Unmarshal(m, b)
}
func (m *GetCoachListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachListResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachListResponse.Merge(dst, src)
}
func (m *GetCoachListResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachListResponse.Size(m)
}
func (m *GetCoachListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachListResponse proto.InternalMessageInfo

func (m *GetCoachListResponse) GetCoachList() []*GameCoachInfo {
	if m != nil {
		return m.CoachList
	}
	return nil
}

func (m *GetCoachListResponse) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

type BatchGetSkillProductInfoRequest struct {
	ProductIdList        []uint32 `protobuf:"varint,1,rep,packed,name=product_id_list,json=productIdList,proto3" json:"product_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetSkillProductInfoRequest) Reset()         { *m = BatchGetSkillProductInfoRequest{} }
func (m *BatchGetSkillProductInfoRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetSkillProductInfoRequest) ProtoMessage()    {}
func (*BatchGetSkillProductInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{20}
}
func (m *BatchGetSkillProductInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetSkillProductInfoRequest.Unmarshal(m, b)
}
func (m *BatchGetSkillProductInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetSkillProductInfoRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetSkillProductInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetSkillProductInfoRequest.Merge(dst, src)
}
func (m *BatchGetSkillProductInfoRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetSkillProductInfoRequest.Size(m)
}
func (m *BatchGetSkillProductInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetSkillProductInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetSkillProductInfoRequest proto.InternalMessageInfo

func (m *BatchGetSkillProductInfoRequest) GetProductIdList() []uint32 {
	if m != nil {
		return m.ProductIdList
	}
	return nil
}

type BatchGetSkillProductInfoResponse struct {
	ProductList          []*SkillProduct `protobuf:"bytes,1,rep,name=product_list,json=productList,proto3" json:"product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetSkillProductInfoResponse) Reset()         { *m = BatchGetSkillProductInfoResponse{} }
func (m *BatchGetSkillProductInfoResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetSkillProductInfoResponse) ProtoMessage()    {}
func (*BatchGetSkillProductInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{21}
}
func (m *BatchGetSkillProductInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetSkillProductInfoResponse.Unmarshal(m, b)
}
func (m *BatchGetSkillProductInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetSkillProductInfoResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetSkillProductInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetSkillProductInfoResponse.Merge(dst, src)
}
func (m *BatchGetSkillProductInfoResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetSkillProductInfoResponse.Size(m)
}
func (m *BatchGetSkillProductInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetSkillProductInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetSkillProductInfoResponse proto.InternalMessageInfo

func (m *BatchGetSkillProductInfoResponse) GetProductList() []*SkillProduct {
	if m != nil {
		return m.ProductList
	}
	return nil
}

// 获取某款游戏的教练列表
type GetGameCoachListByUidRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CoachUidList         []uint32 `protobuf:"varint,2,rep,packed,name=coach_uid_list,json=coachUidList,proto3" json:"coach_uid_list,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	ExposeType           uint32   `protobuf:"varint,5,opt,name=expose_type,json=exposeType,proto3" json:"expose_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameCoachListByUidRequest) Reset()         { *m = GetGameCoachListByUidRequest{} }
func (m *GetGameCoachListByUidRequest) String() string { return proto.CompactTextString(m) }
func (*GetGameCoachListByUidRequest) ProtoMessage()    {}
func (*GetGameCoachListByUidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{22}
}
func (m *GetGameCoachListByUidRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameCoachListByUidRequest.Unmarshal(m, b)
}
func (m *GetGameCoachListByUidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameCoachListByUidRequest.Marshal(b, m, deterministic)
}
func (dst *GetGameCoachListByUidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameCoachListByUidRequest.Merge(dst, src)
}
func (m *GetGameCoachListByUidRequest) XXX_Size() int {
	return xxx_messageInfo_GetGameCoachListByUidRequest.Size(m)
}
func (m *GetGameCoachListByUidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameCoachListByUidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameCoachListByUidRequest proto.InternalMessageInfo

func (m *GetGameCoachListByUidRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGameCoachListByUidRequest) GetCoachUidList() []uint32 {
	if m != nil {
		return m.CoachUidList
	}
	return nil
}

func (m *GetGameCoachListByUidRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGameCoachListByUidRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGameCoachListByUidRequest) GetExposeType() uint32 {
	if m != nil {
		return m.ExposeType
	}
	return 0
}

type GetGameCoachListByUidResponse struct {
	CoachList            []*GameCoachInfo `protobuf:"bytes,1,rep,name=coach_list,json=coachList,proto3" json:"coach_list,omitempty"`
	NextOffset           uint32           `protobuf:"varint,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGameCoachListByUidResponse) Reset()         { *m = GetGameCoachListByUidResponse{} }
func (m *GetGameCoachListByUidResponse) String() string { return proto.CompactTextString(m) }
func (*GetGameCoachListByUidResponse) ProtoMessage()    {}
func (*GetGameCoachListByUidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{23}
}
func (m *GetGameCoachListByUidResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameCoachListByUidResponse.Unmarshal(m, b)
}
func (m *GetGameCoachListByUidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameCoachListByUidResponse.Marshal(b, m, deterministic)
}
func (dst *GetGameCoachListByUidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameCoachListByUidResponse.Merge(dst, src)
}
func (m *GetGameCoachListByUidResponse) XXX_Size() int {
	return xxx_messageInfo_GetGameCoachListByUidResponse.Size(m)
}
func (m *GetGameCoachListByUidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameCoachListByUidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameCoachListByUidResponse proto.InternalMessageInfo

func (m *GetGameCoachListByUidResponse) GetCoachList() []*GameCoachInfo {
	if m != nil {
		return m.CoachList
	}
	return nil
}

func (m *GetGameCoachListByUidResponse) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

// 上报曝光的大神
type ReportExposeCoachRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ExposeCoachList      []uint32 `protobuf:"varint,2,rep,packed,name=expose_coach_list,json=exposeCoachList,proto3" json:"expose_coach_list,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ExposeType           uint32   `protobuf:"varint,4,opt,name=expose_type,json=exposeType,proto3" json:"expose_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportExposeCoachRequest) Reset()         { *m = ReportExposeCoachRequest{} }
func (m *ReportExposeCoachRequest) String() string { return proto.CompactTextString(m) }
func (*ReportExposeCoachRequest) ProtoMessage()    {}
func (*ReportExposeCoachRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{24}
}
func (m *ReportExposeCoachRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportExposeCoachRequest.Unmarshal(m, b)
}
func (m *ReportExposeCoachRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportExposeCoachRequest.Marshal(b, m, deterministic)
}
func (dst *ReportExposeCoachRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportExposeCoachRequest.Merge(dst, src)
}
func (m *ReportExposeCoachRequest) XXX_Size() int {
	return xxx_messageInfo_ReportExposeCoachRequest.Size(m)
}
func (m *ReportExposeCoachRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportExposeCoachRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportExposeCoachRequest proto.InternalMessageInfo

func (m *ReportExposeCoachRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportExposeCoachRequest) GetExposeCoachList() []uint32 {
	if m != nil {
		return m.ExposeCoachList
	}
	return nil
}

func (m *ReportExposeCoachRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ReportExposeCoachRequest) GetExposeType() uint32 {
	if m != nil {
		return m.ExposeType
	}
	return 0
}

type ReportExposeCoachResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportExposeCoachResponse) Reset()         { *m = ReportExposeCoachResponse{} }
func (m *ReportExposeCoachResponse) String() string { return proto.CompactTextString(m) }
func (*ReportExposeCoachResponse) ProtoMessage()    {}
func (*ReportExposeCoachResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{25}
}
func (m *ReportExposeCoachResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportExposeCoachResponse.Unmarshal(m, b)
}
func (m *ReportExposeCoachResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportExposeCoachResponse.Marshal(b, m, deterministic)
}
func (dst *ReportExposeCoachResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportExposeCoachResponse.Merge(dst, src)
}
func (m *ReportExposeCoachResponse) XXX_Size() int {
	return xxx_messageInfo_ReportExposeCoachResponse.Size(m)
}
func (m *ReportExposeCoachResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportExposeCoachResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportExposeCoachResponse proto.InternalMessageInfo

// 邀请下单
type InviteOrderRequest struct {
	Uid                  uint32    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	InviteUid            uint32    `protobuf:"varint,2,opt,name=invite_uid,json=inviteUid,proto3" json:"invite_uid,omitempty"`
	ProductId            uint32    `protobuf:"varint,3,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	OrderCnt             uint32    `protobuf:"varint,4,opt,name=order_cnt,json=orderCnt,proto3" json:"order_cnt,omitempty"`
	Comment              string    `protobuf:"bytes,5,opt,name=comment,proto3" json:"comment,omitempty"`
	GameInfo             *GameInfo `protobuf:"bytes,6,opt,name=game_info,json=gameInfo,proto3" json:"game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *InviteOrderRequest) Reset()         { *m = InviteOrderRequest{} }
func (m *InviteOrderRequest) String() string { return proto.CompactTextString(m) }
func (*InviteOrderRequest) ProtoMessage()    {}
func (*InviteOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{26}
}
func (m *InviteOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteOrderRequest.Unmarshal(m, b)
}
func (m *InviteOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteOrderRequest.Marshal(b, m, deterministic)
}
func (dst *InviteOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteOrderRequest.Merge(dst, src)
}
func (m *InviteOrderRequest) XXX_Size() int {
	return xxx_messageInfo_InviteOrderRequest.Size(m)
}
func (m *InviteOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InviteOrderRequest proto.InternalMessageInfo

func (m *InviteOrderRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *InviteOrderRequest) GetInviteUid() uint32 {
	if m != nil {
		return m.InviteUid
	}
	return 0
}

func (m *InviteOrderRequest) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *InviteOrderRequest) GetOrderCnt() uint32 {
	if m != nil {
		return m.OrderCnt
	}
	return 0
}

func (m *InviteOrderRequest) GetComment() string {
	if m != nil {
		return m.Comment
	}
	return ""
}

func (m *InviteOrderRequest) GetGameInfo() *GameInfo {
	if m != nil {
		return m.GameInfo
	}
	return nil
}

type InviteOrderResponse struct {
	InviteId             uint32   `protobuf:"varint,1,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InviteOrderResponse) Reset()         { *m = InviteOrderResponse{} }
func (m *InviteOrderResponse) String() string { return proto.CompactTextString(m) }
func (*InviteOrderResponse) ProtoMessage()    {}
func (*InviteOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{27}
}
func (m *InviteOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteOrderResponse.Unmarshal(m, b)
}
func (m *InviteOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteOrderResponse.Marshal(b, m, deterministic)
}
func (dst *InviteOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteOrderResponse.Merge(dst, src)
}
func (m *InviteOrderResponse) XXX_Size() int {
	return xxx_messageInfo_InviteOrderResponse.Size(m)
}
func (m *InviteOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InviteOrderResponse proto.InternalMessageInfo

func (m *InviteOrderResponse) GetInviteId() uint32 {
	if m != nil {
		return m.InviteId
	}
	return 0
}

type HandleInviteOrderRequest struct {
	InviteId             uint32   `protobuf:"varint,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleInviteOrderRequest) Reset()         { *m = HandleInviteOrderRequest{} }
func (m *HandleInviteOrderRequest) String() string { return proto.CompactTextString(m) }
func (*HandleInviteOrderRequest) ProtoMessage()    {}
func (*HandleInviteOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{28}
}
func (m *HandleInviteOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleInviteOrderRequest.Unmarshal(m, b)
}
func (m *HandleInviteOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleInviteOrderRequest.Marshal(b, m, deterministic)
}
func (dst *HandleInviteOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleInviteOrderRequest.Merge(dst, src)
}
func (m *HandleInviteOrderRequest) XXX_Size() int {
	return xxx_messageInfo_HandleInviteOrderRequest.Size(m)
}
func (m *HandleInviteOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleInviteOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HandleInviteOrderRequest proto.InternalMessageInfo

func (m *HandleInviteOrderRequest) GetInviteId() uint32 {
	if m != nil {
		return m.InviteId
	}
	return 0
}

func (m *HandleInviteOrderRequest) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type HandleInviteOrderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleInviteOrderResponse) Reset()         { *m = HandleInviteOrderResponse{} }
func (m *HandleInviteOrderResponse) String() string { return proto.CompactTextString(m) }
func (*HandleInviteOrderResponse) ProtoMessage()    {}
func (*HandleInviteOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{29}
}
func (m *HandleInviteOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleInviteOrderResponse.Unmarshal(m, b)
}
func (m *HandleInviteOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleInviteOrderResponse.Marshal(b, m, deterministic)
}
func (dst *HandleInviteOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleInviteOrderResponse.Merge(dst, src)
}
func (m *HandleInviteOrderResponse) XXX_Size() int {
	return xxx_messageInfo_HandleInviteOrderResponse.Size(m)
}
func (m *HandleInviteOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleInviteOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HandleInviteOrderResponse proto.InternalMessageInfo

type DelSkillProductRequest struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	SkillId              []uint32 `protobuf:"varint,2,rep,packed,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSkillProductRequest) Reset()         { *m = DelSkillProductRequest{} }
func (m *DelSkillProductRequest) String() string { return proto.CompactTextString(m) }
func (*DelSkillProductRequest) ProtoMessage()    {}
func (*DelSkillProductRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{30}
}
func (m *DelSkillProductRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSkillProductRequest.Unmarshal(m, b)
}
func (m *DelSkillProductRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSkillProductRequest.Marshal(b, m, deterministic)
}
func (dst *DelSkillProductRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSkillProductRequest.Merge(dst, src)
}
func (m *DelSkillProductRequest) XXX_Size() int {
	return xxx_messageInfo_DelSkillProductRequest.Size(m)
}
func (m *DelSkillProductRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSkillProductRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelSkillProductRequest proto.InternalMessageInfo

func (m *DelSkillProductRequest) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *DelSkillProductRequest) GetSkillId() []uint32 {
	if m != nil {
		return m.SkillId
	}
	return nil
}

type DelSkillProductResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSkillProductResponse) Reset()         { *m = DelSkillProductResponse{} }
func (m *DelSkillProductResponse) String() string { return proto.CompactTextString(m) }
func (*DelSkillProductResponse) ProtoMessage()    {}
func (*DelSkillProductResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{31}
}
func (m *DelSkillProductResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSkillProductResponse.Unmarshal(m, b)
}
func (m *DelSkillProductResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSkillProductResponse.Marshal(b, m, deterministic)
}
func (dst *DelSkillProductResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSkillProductResponse.Merge(dst, src)
}
func (m *DelSkillProductResponse) XXX_Size() int {
	return xxx_messageInfo_DelSkillProductResponse.Size(m)
}
func (m *DelSkillProductResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSkillProductResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DelSkillProductResponse proto.InternalMessageInfo

type TestRequest struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	ParamJson            string   `protobuf:"bytes,2,opt,name=param_json,json=paramJson,proto3" json:"param_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestRequest) Reset()         { *m = TestRequest{} }
func (m *TestRequest) String() string { return proto.CompactTextString(m) }
func (*TestRequest) ProtoMessage()    {}
func (*TestRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{32}
}
func (m *TestRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestRequest.Unmarshal(m, b)
}
func (m *TestRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestRequest.Marshal(b, m, deterministic)
}
func (dst *TestRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestRequest.Merge(dst, src)
}
func (m *TestRequest) XXX_Size() int {
	return xxx_messageInfo_TestRequest.Size(m)
}
func (m *TestRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TestRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TestRequest proto.InternalMessageInfo

func (m *TestRequest) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *TestRequest) GetParamJson() string {
	if m != nil {
		return m.ParamJson
	}
	return ""
}

type TestResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestResponse) Reset()         { *m = TestResponse{} }
func (m *TestResponse) String() string { return proto.CompactTextString(m) }
func (*TestResponse) ProtoMessage()    {}
func (*TestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{33}
}
func (m *TestResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestResponse.Unmarshal(m, b)
}
func (m *TestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestResponse.Marshal(b, m, deterministic)
}
func (dst *TestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestResponse.Merge(dst, src)
}
func (m *TestResponse) XXX_Size() int {
	return xxx_messageInfo_TestResponse.Size(m)
}
func (m *TestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TestResponse proto.InternalMessageInfo

type GetGamePricePropertyRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGamePricePropertyRequest) Reset()         { *m = GetGamePricePropertyRequest{} }
func (m *GetGamePricePropertyRequest) String() string { return proto.CompactTextString(m) }
func (*GetGamePricePropertyRequest) ProtoMessage()    {}
func (*GetGamePricePropertyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{34}
}
func (m *GetGamePricePropertyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePricePropertyRequest.Unmarshal(m, b)
}
func (m *GetGamePricePropertyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePricePropertyRequest.Marshal(b, m, deterministic)
}
func (dst *GetGamePricePropertyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePricePropertyRequest.Merge(dst, src)
}
func (m *GetGamePricePropertyRequest) XXX_Size() int {
	return xxx_messageInfo_GetGamePricePropertyRequest.Size(m)
}
func (m *GetGamePricePropertyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePricePropertyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePricePropertyRequest proto.InternalMessageInfo

func (m *GetGamePricePropertyRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetGamePricePropertyResponse struct {
	PriceProperty        *GameProperty `protobuf:"bytes,1,opt,name=price_property,json=priceProperty,proto3" json:"price_property,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetGamePricePropertyResponse) Reset()         { *m = GetGamePricePropertyResponse{} }
func (m *GetGamePricePropertyResponse) String() string { return proto.CompactTextString(m) }
func (*GetGamePricePropertyResponse) ProtoMessage()    {}
func (*GetGamePricePropertyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{35}
}
func (m *GetGamePricePropertyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGamePricePropertyResponse.Unmarshal(m, b)
}
func (m *GetGamePricePropertyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGamePricePropertyResponse.Marshal(b, m, deterministic)
}
func (dst *GetGamePricePropertyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGamePricePropertyResponse.Merge(dst, src)
}
func (m *GetGamePricePropertyResponse) XXX_Size() int {
	return xxx_messageInfo_GetGamePricePropertyResponse.Size(m)
}
func (m *GetGamePricePropertyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGamePricePropertyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGamePricePropertyResponse proto.InternalMessageInfo

func (m *GetGamePricePropertyResponse) GetPriceProperty() *GameProperty {
	if m != nil {
		return m.PriceProperty
	}
	return nil
}

type GetSkillProductByUidGameIdRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSkillProductByUidGameIdRequest) Reset()         { *m = GetSkillProductByUidGameIdRequest{} }
func (m *GetSkillProductByUidGameIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetSkillProductByUidGameIdRequest) ProtoMessage()    {}
func (*GetSkillProductByUidGameIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{36}
}
func (m *GetSkillProductByUidGameIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSkillProductByUidGameIdRequest.Unmarshal(m, b)
}
func (m *GetSkillProductByUidGameIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSkillProductByUidGameIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetSkillProductByUidGameIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSkillProductByUidGameIdRequest.Merge(dst, src)
}
func (m *GetSkillProductByUidGameIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetSkillProductByUidGameIdRequest.Size(m)
}
func (m *GetSkillProductByUidGameIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSkillProductByUidGameIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSkillProductByUidGameIdRequest proto.InternalMessageInfo

func (m *GetSkillProductByUidGameIdRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSkillProductByUidGameIdRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetSkillProductByUidGameIdResponse struct {
	SkillProduct         *SkillProduct `protobuf:"bytes,1,opt,name=skill_product,json=skillProduct,proto3" json:"skill_product,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSkillProductByUidGameIdResponse) Reset()         { *m = GetSkillProductByUidGameIdResponse{} }
func (m *GetSkillProductByUidGameIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetSkillProductByUidGameIdResponse) ProtoMessage()    {}
func (*GetSkillProductByUidGameIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{37}
}
func (m *GetSkillProductByUidGameIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSkillProductByUidGameIdResponse.Unmarshal(m, b)
}
func (m *GetSkillProductByUidGameIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSkillProductByUidGameIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetSkillProductByUidGameIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSkillProductByUidGameIdResponse.Merge(dst, src)
}
func (m *GetSkillProductByUidGameIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetSkillProductByUidGameIdResponse.Size(m)
}
func (m *GetSkillProductByUidGameIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSkillProductByUidGameIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSkillProductByUidGameIdResponse proto.InternalMessageInfo

func (m *GetSkillProductByUidGameIdResponse) GetSkillProduct() *SkillProduct {
	if m != nil {
		return m.SkillProduct
	}
	return nil
}

// =================================== 运营后台相关 ============================================
type AddCoachRecommendRequest struct {
	RecommendInfo        *CoachRecommendInfo `protobuf:"bytes,1,opt,name=recommend_info,json=recommendInfo,proto3" json:"recommend_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AddCoachRecommendRequest) Reset()         { *m = AddCoachRecommendRequest{} }
func (m *AddCoachRecommendRequest) String() string { return proto.CompactTextString(m) }
func (*AddCoachRecommendRequest) ProtoMessage()    {}
func (*AddCoachRecommendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{38}
}
func (m *AddCoachRecommendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCoachRecommendRequest.Unmarshal(m, b)
}
func (m *AddCoachRecommendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCoachRecommendRequest.Marshal(b, m, deterministic)
}
func (dst *AddCoachRecommendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCoachRecommendRequest.Merge(dst, src)
}
func (m *AddCoachRecommendRequest) XXX_Size() int {
	return xxx_messageInfo_AddCoachRecommendRequest.Size(m)
}
func (m *AddCoachRecommendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCoachRecommendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddCoachRecommendRequest proto.InternalMessageInfo

func (m *AddCoachRecommendRequest) GetRecommendInfo() *CoachRecommendInfo {
	if m != nil {
		return m.RecommendInfo
	}
	return nil
}

type AddCoachRecommendResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCoachRecommendResponse) Reset()         { *m = AddCoachRecommendResponse{} }
func (m *AddCoachRecommendResponse) String() string { return proto.CompactTextString(m) }
func (*AddCoachRecommendResponse) ProtoMessage()    {}
func (*AddCoachRecommendResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{39}
}
func (m *AddCoachRecommendResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCoachRecommendResponse.Unmarshal(m, b)
}
func (m *AddCoachRecommendResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCoachRecommendResponse.Marshal(b, m, deterministic)
}
func (dst *AddCoachRecommendResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCoachRecommendResponse.Merge(dst, src)
}
func (m *AddCoachRecommendResponse) XXX_Size() int {
	return xxx_messageInfo_AddCoachRecommendResponse.Size(m)
}
func (m *AddCoachRecommendResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCoachRecommendResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddCoachRecommendResponse proto.InternalMessageInfo

type GetCoachRecommendRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	RecommendType        uint32   `protobuf:"varint,3,opt,name=recommend_type,json=recommendType,proto3" json:"recommend_type,omitempty"`
	StartTime            uint32   `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PageNum              uint32   `protobuf:"varint,6,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachRecommendRequest) Reset()         { *m = GetCoachRecommendRequest{} }
func (m *GetCoachRecommendRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachRecommendRequest) ProtoMessage()    {}
func (*GetCoachRecommendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{40}
}
func (m *GetCoachRecommendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachRecommendRequest.Unmarshal(m, b)
}
func (m *GetCoachRecommendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachRecommendRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachRecommendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachRecommendRequest.Merge(dst, src)
}
func (m *GetCoachRecommendRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachRecommendRequest.Size(m)
}
func (m *GetCoachRecommendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachRecommendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachRecommendRequest proto.InternalMessageInfo

func (m *GetCoachRecommendRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetCoachRecommendRequest) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetCoachRecommendRequest) GetRecommendType() uint32 {
	if m != nil {
		return m.RecommendType
	}
	return 0
}

func (m *GetCoachRecommendRequest) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetCoachRecommendRequest) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetCoachRecommendRequest) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetCoachRecommendRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetCoachRecommendResponse struct {
	RecommendList        []*CoachRecommendInfo `protobuf:"bytes,1,rep,name=recommend_list,json=recommendList,proto3" json:"recommend_list,omitempty"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetCoachRecommendResponse) Reset()         { *m = GetCoachRecommendResponse{} }
func (m *GetCoachRecommendResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachRecommendResponse) ProtoMessage()    {}
func (*GetCoachRecommendResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{41}
}
func (m *GetCoachRecommendResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachRecommendResponse.Unmarshal(m, b)
}
func (m *GetCoachRecommendResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachRecommendResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachRecommendResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachRecommendResponse.Merge(dst, src)
}
func (m *GetCoachRecommendResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachRecommendResponse.Size(m)
}
func (m *GetCoachRecommendResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachRecommendResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachRecommendResponse proto.InternalMessageInfo

func (m *GetCoachRecommendResponse) GetRecommendList() []*CoachRecommendInfo {
	if m != nil {
		return m.RecommendList
	}
	return nil
}

func (m *GetCoachRecommendResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CoachRecommendInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	CoachType            uint32   `protobuf:"varint,5,opt,name=coach_type,json=coachType,proto3" json:"coach_type,omitempty"`
	Sort                 float64  `protobuf:"fixed64,6,opt,name=sort,proto3" json:"sort,omitempty"`
	StartTime            uint32   `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	RecommendType        uint32   `protobuf:"varint,9,opt,name=recommend_type,json=recommendType,proto3" json:"recommend_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,10,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachRecommendInfo) Reset()         { *m = CoachRecommendInfo{} }
func (m *CoachRecommendInfo) String() string { return proto.CompactTextString(m) }
func (*CoachRecommendInfo) ProtoMessage()    {}
func (*CoachRecommendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{42}
}
func (m *CoachRecommendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachRecommendInfo.Unmarshal(m, b)
}
func (m *CoachRecommendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachRecommendInfo.Marshal(b, m, deterministic)
}
func (dst *CoachRecommendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachRecommendInfo.Merge(dst, src)
}
func (m *CoachRecommendInfo) XXX_Size() int {
	return xxx_messageInfo_CoachRecommendInfo.Size(m)
}
func (m *CoachRecommendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachRecommendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CoachRecommendInfo proto.InternalMessageInfo

func (m *CoachRecommendInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CoachRecommendInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CoachRecommendInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *CoachRecommendInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CoachRecommendInfo) GetCoachType() uint32 {
	if m != nil {
		return m.CoachType
	}
	return 0
}

func (m *CoachRecommendInfo) GetSort() float64 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *CoachRecommendInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *CoachRecommendInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *CoachRecommendInfo) GetRecommendType() uint32 {
	if m != nil {
		return m.RecommendType
	}
	return 0
}

func (m *CoachRecommendInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UpdateCoachRecommendRequest struct {
	RecommendInfo        *CoachRecommendInfo `protobuf:"bytes,1,opt,name=recommend_info,json=recommendInfo,proto3" json:"recommend_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UpdateCoachRecommendRequest) Reset()         { *m = UpdateCoachRecommendRequest{} }
func (m *UpdateCoachRecommendRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateCoachRecommendRequest) ProtoMessage()    {}
func (*UpdateCoachRecommendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{43}
}
func (m *UpdateCoachRecommendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCoachRecommendRequest.Unmarshal(m, b)
}
func (m *UpdateCoachRecommendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCoachRecommendRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateCoachRecommendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCoachRecommendRequest.Merge(dst, src)
}
func (m *UpdateCoachRecommendRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateCoachRecommendRequest.Size(m)
}
func (m *UpdateCoachRecommendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCoachRecommendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCoachRecommendRequest proto.InternalMessageInfo

func (m *UpdateCoachRecommendRequest) GetRecommendInfo() *CoachRecommendInfo {
	if m != nil {
		return m.RecommendInfo
	}
	return nil
}

type UpdateCoachRecommendResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCoachRecommendResponse) Reset()         { *m = UpdateCoachRecommendResponse{} }
func (m *UpdateCoachRecommendResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateCoachRecommendResponse) ProtoMessage()    {}
func (*UpdateCoachRecommendResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{44}
}
func (m *UpdateCoachRecommendResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCoachRecommendResponse.Unmarshal(m, b)
}
func (m *UpdateCoachRecommendResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCoachRecommendResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateCoachRecommendResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCoachRecommendResponse.Merge(dst, src)
}
func (m *UpdateCoachRecommendResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateCoachRecommendResponse.Size(m)
}
func (m *UpdateCoachRecommendResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCoachRecommendResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCoachRecommendResponse proto.InternalMessageInfo

type DelCoachRecommendRequest struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCoachRecommendRequest) Reset()         { *m = DelCoachRecommendRequest{} }
func (m *DelCoachRecommendRequest) String() string { return proto.CompactTextString(m) }
func (*DelCoachRecommendRequest) ProtoMessage()    {}
func (*DelCoachRecommendRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{45}
}
func (m *DelCoachRecommendRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCoachRecommendRequest.Unmarshal(m, b)
}
func (m *DelCoachRecommendRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCoachRecommendRequest.Marshal(b, m, deterministic)
}
func (dst *DelCoachRecommendRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCoachRecommendRequest.Merge(dst, src)
}
func (m *DelCoachRecommendRequest) XXX_Size() int {
	return xxx_messageInfo_DelCoachRecommendRequest.Size(m)
}
func (m *DelCoachRecommendRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCoachRecommendRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelCoachRecommendRequest proto.InternalMessageInfo

func (m *DelCoachRecommendRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelCoachRecommendResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCoachRecommendResponse) Reset()         { *m = DelCoachRecommendResponse{} }
func (m *DelCoachRecommendResponse) String() string { return proto.CompactTextString(m) }
func (*DelCoachRecommendResponse) ProtoMessage()    {}
func (*DelCoachRecommendResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{46}
}
func (m *DelCoachRecommendResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCoachRecommendResponse.Unmarshal(m, b)
}
func (m *DelCoachRecommendResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCoachRecommendResponse.Marshal(b, m, deterministic)
}
func (dst *DelCoachRecommendResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCoachRecommendResponse.Merge(dst, src)
}
func (m *DelCoachRecommendResponse) XXX_Size() int {
	return xxx_messageInfo_DelCoachRecommendResponse.Size(m)
}
func (m *DelCoachRecommendResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCoachRecommendResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DelCoachRecommendResponse proto.InternalMessageInfo

// 技能商品信息
type SkillProduct struct {
	Id                   uint32         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GameId               uint32         `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Price                uint32         `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	Switch               bool           `protobuf:"varint,4,opt,name=switch,proto3" json:"switch,omitempty"`
	Uid                  uint32         `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	Tag                  string         `protobuf:"bytes,6,opt,name=tag,proto3" json:"tag,omitempty"`
	MaxOrderCnt          uint32         `protobuf:"varint,7,opt,name=max_order_cnt,json=maxOrderCnt,proto3" json:"max_order_cnt,omitempty"`
	OrderCnt             uint32         `protobuf:"varint,8,opt,name=order_cnt,json=orderCnt,proto3" json:"order_cnt,omitempty"`
	UintType             uint32         `protobuf:"varint,9,opt,name=uint_type,json=uintType,proto3" json:"uint_type,omitempty"`
	GuaranteeWinTexts    []string       `protobuf:"bytes,10,rep,name=guarantee_win_texts,json=guaranteeWinTexts,proto3" json:"guarantee_win_texts,omitempty"`
	IsGuaranteeWin       bool           `protobuf:"varint,11,opt,name=is_guarantee_win,json=isGuaranteeWin,proto3" json:"is_guarantee_win,omitempty"`
	PropertyList         []*SectionInfo `protobuf:"bytes,12,rep,name=property_list,json=propertyList,proto3" json:"property_list,omitempty"`
	IsNewCoach           bool           `protobuf:"varint,13,opt,name=is_new_coach,json=isNewCoach,proto3" json:"is_new_coach,omitempty"`
	IsQuickReceive       bool           `protobuf:"varint,14,opt,name=is_quick_receive,json=isQuickReceive,proto3" json:"is_quick_receive,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SkillProduct) Reset()         { *m = SkillProduct{} }
func (m *SkillProduct) String() string { return proto.CompactTextString(m) }
func (*SkillProduct) ProtoMessage()    {}
func (*SkillProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{47}
}
func (m *SkillProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkillProduct.Unmarshal(m, b)
}
func (m *SkillProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkillProduct.Marshal(b, m, deterministic)
}
func (dst *SkillProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkillProduct.Merge(dst, src)
}
func (m *SkillProduct) XXX_Size() int {
	return xxx_messageInfo_SkillProduct.Size(m)
}
func (m *SkillProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_SkillProduct.DiscardUnknown(m)
}

var xxx_messageInfo_SkillProduct proto.InternalMessageInfo

func (m *SkillProduct) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SkillProduct) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SkillProduct) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *SkillProduct) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

func (m *SkillProduct) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SkillProduct) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *SkillProduct) GetMaxOrderCnt() uint32 {
	if m != nil {
		return m.MaxOrderCnt
	}
	return 0
}

func (m *SkillProduct) GetOrderCnt() uint32 {
	if m != nil {
		return m.OrderCnt
	}
	return 0
}

func (m *SkillProduct) GetUintType() uint32 {
	if m != nil {
		return m.UintType
	}
	return 0
}

func (m *SkillProduct) GetGuaranteeWinTexts() []string {
	if m != nil {
		return m.GuaranteeWinTexts
	}
	return nil
}

func (m *SkillProduct) GetIsGuaranteeWin() bool {
	if m != nil {
		return m.IsGuaranteeWin
	}
	return false
}

func (m *SkillProduct) GetPropertyList() []*SectionInfo {
	if m != nil {
		return m.PropertyList
	}
	return nil
}

func (m *SkillProduct) GetIsNewCoach() bool {
	if m != nil {
		return m.IsNewCoach
	}
	return false
}

func (m *SkillProduct) GetIsQuickReceive() bool {
	if m != nil {
		return m.IsQuickReceive
	}
	return false
}

type SkillInfo struct {
	SkillId              uint32         `protobuf:"varint,1,opt,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
	PropertyList         []*SectionInfo `protobuf:"bytes,2,rep,name=property_list,json=propertyList,proto3" json:"property_list,omitempty"`
	DefaultPrice         uint32         `protobuf:"varint,3,opt,name=default_price,json=defaultPrice,proto3" json:"default_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SkillInfo) Reset()         { *m = SkillInfo{} }
func (m *SkillInfo) String() string { return proto.CompactTextString(m) }
func (*SkillInfo) ProtoMessage()    {}
func (*SkillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{48}
}
func (m *SkillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkillInfo.Unmarshal(m, b)
}
func (m *SkillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkillInfo.Marshal(b, m, deterministic)
}
func (dst *SkillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkillInfo.Merge(dst, src)
}
func (m *SkillInfo) XXX_Size() int {
	return xxx_messageInfo_SkillInfo.Size(m)
}
func (m *SkillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SkillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SkillInfo proto.InternalMessageInfo

func (m *SkillInfo) GetSkillId() uint32 {
	if m != nil {
		return m.SkillId
	}
	return 0
}

func (m *SkillInfo) GetPropertyList() []*SectionInfo {
	if m != nil {
		return m.PropertyList
	}
	return nil
}

func (m *SkillInfo) GetDefaultPrice() uint32 {
	if m != nil {
		return m.DefaultPrice
	}
	return 0
}

// 段位等信息
type SectionInfo struct {
	SectionName          string   `protobuf:"bytes,1,opt,name=section_name,json=sectionName,proto3" json:"section_name,omitempty"`
	IsRank               bool     `protobuf:"varint,2,opt,name=is_rank,json=isRank,proto3" json:"is_rank,omitempty"`
	ItemList             []string `protobuf:"bytes,3,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	SectionId            uint32   `protobuf:"varint,4,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SectionInfo) Reset()         { *m = SectionInfo{} }
func (m *SectionInfo) String() string { return proto.CompactTextString(m) }
func (*SectionInfo) ProtoMessage()    {}
func (*SectionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{49}
}
func (m *SectionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SectionInfo.Unmarshal(m, b)
}
func (m *SectionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SectionInfo.Marshal(b, m, deterministic)
}
func (dst *SectionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SectionInfo.Merge(dst, src)
}
func (m *SectionInfo) XXX_Size() int {
	return xxx_messageInfo_SectionInfo.Size(m)
}
func (m *SectionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SectionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SectionInfo proto.InternalMessageInfo

func (m *SectionInfo) GetSectionName() string {
	if m != nil {
		return m.SectionName
	}
	return ""
}

func (m *SectionInfo) GetIsRank() bool {
	if m != nil {
		return m.IsRank
	}
	return false
}

func (m *SectionInfo) GetItemList() []string {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *SectionInfo) GetSectionId() uint32 {
	if m != nil {
		return m.SectionId
	}
	return 0
}

type GameCoachInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Tag                  string   `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	Price                uint32   `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	IsOnline             bool     `protobuf:"varint,4,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	UintType             uint32   `protobuf:"varint,7,opt,name=uint_type,json=uintType,proto3" json:"uint_type,omitempty"`
	IsLimit              bool     `protobuf:"varint,8,opt,name=is_limit,json=isLimit,proto3" json:"is_limit,omitempty"`
	IsInTime             bool     `protobuf:"varint,9,opt,name=is_in_time,json=isInTime,proto3" json:"is_in_time,omitempty"`
	Race                 float32  `protobuf:"fixed32,10,opt,name=race,proto3" json:"race,omitempty"`
	IsNewCoach           bool     `protobuf:"varint,11,opt,name=is_new_coach,json=isNewCoach,proto3" json:"is_new_coach,omitempty"`
	IsFirstRound         bool     `protobuf:"varint,12,opt,name=is_first_round,json=isFirstRound,proto3" json:"is_first_round,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameCoachInfo) Reset()         { *m = GameCoachInfo{} }
func (m *GameCoachInfo) String() string { return proto.CompactTextString(m) }
func (*GameCoachInfo) ProtoMessage()    {}
func (*GameCoachInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{50}
}
func (m *GameCoachInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCoachInfo.Unmarshal(m, b)
}
func (m *GameCoachInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCoachInfo.Marshal(b, m, deterministic)
}
func (dst *GameCoachInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCoachInfo.Merge(dst, src)
}
func (m *GameCoachInfo) XXX_Size() int {
	return xxx_messageInfo_GameCoachInfo.Size(m)
}
func (m *GameCoachInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCoachInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameCoachInfo proto.InternalMessageInfo

func (m *GameCoachInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameCoachInfo) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *GameCoachInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GameCoachInfo) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

func (m *GameCoachInfo) GetUintType() uint32 {
	if m != nil {
		return m.UintType
	}
	return 0
}

func (m *GameCoachInfo) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

func (m *GameCoachInfo) GetIsInTime() bool {
	if m != nil {
		return m.IsInTime
	}
	return false
}

func (m *GameCoachInfo) GetRace() float32 {
	if m != nil {
		return m.Race
	}
	return 0
}

func (m *GameCoachInfo) GetIsNewCoach() bool {
	if m != nil {
		return m.IsNewCoach
	}
	return false
}

func (m *GameCoachInfo) GetIsFirstRound() bool {
	if m != nil {
		return m.IsFirstRound
	}
	return false
}

type GameInfo struct {
	Id                   uint32                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Price                *PriceInfo            `protobuf:"bytes,5,opt,name=price,proto3" json:"price,omitempty"`
	Icon                 string                `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`
	NewCustomerUseDetail *NewCustomerUseDetail `protobuf:"bytes,7,opt,name=new_customer_use_detail,json=newCustomerUseDetail,proto3" json:"new_customer_use_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GameInfo) Reset()         { *m = GameInfo{} }
func (m *GameInfo) String() string { return proto.CompactTextString(m) }
func (*GameInfo) ProtoMessage()    {}
func (*GameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{51}
}
func (m *GameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInfo.Unmarshal(m, b)
}
func (m *GameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInfo.Marshal(b, m, deterministic)
}
func (dst *GameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInfo.Merge(dst, src)
}
func (m *GameInfo) XXX_Size() int {
	return xxx_messageInfo_GameInfo.Size(m)
}
func (m *GameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameInfo proto.InternalMessageInfo

func (m *GameInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GameInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameInfo) GetPrice() *PriceInfo {
	if m != nil {
		return m.Price
	}
	return nil
}

func (m *GameInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GameInfo) GetNewCustomerUseDetail() *NewCustomerUseDetail {
	if m != nil {
		return m.NewCustomerUseDetail
	}
	return nil
}

type PriceInfo struct {
	Price                 uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	PriceUnit             string   `protobuf:"bytes,2,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit,omitempty"`
	MeasureCnt            uint32   `protobuf:"varint,3,opt,name=measure_cnt,json=measureCnt,proto3" json:"measure_cnt,omitempty"`
	MeasureUnit           string   `protobuf:"bytes,4,opt,name=measure_unit,json=measureUnit,proto3" json:"measure_unit,omitempty"`
	UnitType              uint32   `protobuf:"varint,5,opt,name=unit_type,json=unitType,proto3" json:"unit_type,omitempty"`
	HasFirstRoundDiscount bool     `protobuf:"varint,6,opt,name=has_first_round_discount,json=hasFirstRoundDiscount,proto3" json:"has_first_round_discount,omitempty"`
	FirstRoundPrice       uint32   `protobuf:"varint,7,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price,omitempty"`
	HasDiscount           bool     `protobuf:"varint,8,opt,name=has_discount,json=hasDiscount,proto3" json:"has_discount,omitempty"`
	DiscountPrice         uint32   `protobuf:"varint,9,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price,omitempty"`
	DiscountType          uint32   `protobuf:"varint,10,opt,name=discount_type,json=discountType,proto3" json:"discount_type,omitempty"`
	DiscountDesc          string   `protobuf:"bytes,11,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *PriceInfo) Reset()         { *m = PriceInfo{} }
func (m *PriceInfo) String() string { return proto.CompactTextString(m) }
func (*PriceInfo) ProtoMessage()    {}
func (*PriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{52}
}
func (m *PriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PriceInfo.Unmarshal(m, b)
}
func (m *PriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PriceInfo.Marshal(b, m, deterministic)
}
func (dst *PriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PriceInfo.Merge(dst, src)
}
func (m *PriceInfo) XXX_Size() int {
	return xxx_messageInfo_PriceInfo.Size(m)
}
func (m *PriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PriceInfo proto.InternalMessageInfo

func (m *PriceInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PriceInfo) GetPriceUnit() string {
	if m != nil {
		return m.PriceUnit
	}
	return ""
}

func (m *PriceInfo) GetMeasureCnt() uint32 {
	if m != nil {
		return m.MeasureCnt
	}
	return 0
}

func (m *PriceInfo) GetMeasureUnit() string {
	if m != nil {
		return m.MeasureUnit
	}
	return ""
}

func (m *PriceInfo) GetUnitType() uint32 {
	if m != nil {
		return m.UnitType
	}
	return 0
}

func (m *PriceInfo) GetHasFirstRoundDiscount() bool {
	if m != nil {
		return m.HasFirstRoundDiscount
	}
	return false
}

func (m *PriceInfo) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

func (m *PriceInfo) GetHasDiscount() bool {
	if m != nil {
		return m.HasDiscount
	}
	return false
}

func (m *PriceInfo) GetDiscountPrice() uint32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *PriceInfo) GetDiscountType() uint32 {
	if m != nil {
		return m.DiscountType
	}
	return 0
}

func (m *PriceInfo) GetDiscountDesc() string {
	if m != nil {
		return m.DiscountDesc
	}
	return ""
}

// 新客优惠使用详情
type NewCustomerUseDetail struct {
	UseNewCustomerDiscount bool     `protobuf:"varint,1,opt,name=use_new_customer_discount,json=useNewCustomerDiscount,proto3" json:"use_new_customer_discount,omitempty"`
	NewCustomerPrice       uint32   `protobuf:"varint,2,opt,name=new_customer_price,json=newCustomerPrice,proto3" json:"new_customer_price,omitempty"`
	PlatBonusFee           uint32   `protobuf:"varint,3,opt,name=plat_bonus_fee,json=platBonusFee,proto3" json:"plat_bonus_fee,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *NewCustomerUseDetail) Reset()         { *m = NewCustomerUseDetail{} }
func (m *NewCustomerUseDetail) String() string { return proto.CompactTextString(m) }
func (*NewCustomerUseDetail) ProtoMessage()    {}
func (*NewCustomerUseDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{53}
}
func (m *NewCustomerUseDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewCustomerUseDetail.Unmarshal(m, b)
}
func (m *NewCustomerUseDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewCustomerUseDetail.Marshal(b, m, deterministic)
}
func (dst *NewCustomerUseDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewCustomerUseDetail.Merge(dst, src)
}
func (m *NewCustomerUseDetail) XXX_Size() int {
	return xxx_messageInfo_NewCustomerUseDetail.Size(m)
}
func (m *NewCustomerUseDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_NewCustomerUseDetail.DiscardUnknown(m)
}

var xxx_messageInfo_NewCustomerUseDetail proto.InternalMessageInfo

func (m *NewCustomerUseDetail) GetUseNewCustomerDiscount() bool {
	if m != nil {
		return m.UseNewCustomerDiscount
	}
	return false
}

func (m *NewCustomerUseDetail) GetNewCustomerPrice() uint32 {
	if m != nil {
		return m.NewCustomerPrice
	}
	return 0
}

func (m *NewCustomerUseDetail) GetPlatBonusFee() uint32 {
	if m != nil {
		return m.PlatBonusFee
	}
	return 0
}

// 游戏属性
type GameProperty struct {
	Id                   uint32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ValList              []*GamePropertyVal `protobuf:"bytes,3,rep,name=val_list,json=valList,proto3" json:"val_list,omitempty"`
	PropertyType         uint32             `protobuf:"varint,4,opt,name=property_type,json=propertyType,proto3" json:"property_type,omitempty"`
	SelectType           uint32             `protobuf:"varint,5,opt,name=select_type,json=selectType,proto3" json:"select_type,omitempty"`
	Expose               bool               `protobuf:"varint,6,opt,name=expose,proto3" json:"expose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GameProperty) Reset()         { *m = GameProperty{} }
func (m *GameProperty) String() string { return proto.CompactTextString(m) }
func (*GameProperty) ProtoMessage()    {}
func (*GameProperty) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{54}
}
func (m *GameProperty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameProperty.Unmarshal(m, b)
}
func (m *GameProperty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameProperty.Marshal(b, m, deterministic)
}
func (dst *GameProperty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameProperty.Merge(dst, src)
}
func (m *GameProperty) XXX_Size() int {
	return xxx_messageInfo_GameProperty.Size(m)
}
func (m *GameProperty) XXX_DiscardUnknown() {
	xxx_messageInfo_GameProperty.DiscardUnknown(m)
}

var xxx_messageInfo_GameProperty proto.InternalMessageInfo

func (m *GameProperty) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GameProperty) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameProperty) GetValList() []*GamePropertyVal {
	if m != nil {
		return m.ValList
	}
	return nil
}

func (m *GameProperty) GetPropertyType() uint32 {
	if m != nil {
		return m.PropertyType
	}
	return 0
}

func (m *GameProperty) GetSelectType() uint32 {
	if m != nil {
		return m.SelectType
	}
	return 0
}

func (m *GameProperty) GetExpose() bool {
	if m != nil {
		return m.Expose
	}
	return false
}

// 游戏属性值
type GamePropertyVal struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GamePropertyVal) Reset()         { *m = GamePropertyVal{} }
func (m *GamePropertyVal) String() string { return proto.CompactTextString(m) }
func (*GamePropertyVal) ProtoMessage()    {}
func (*GamePropertyVal) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{55}
}
func (m *GamePropertyVal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePropertyVal.Unmarshal(m, b)
}
func (m *GamePropertyVal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePropertyVal.Marshal(b, m, deterministic)
}
func (dst *GamePropertyVal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePropertyVal.Merge(dst, src)
}
func (m *GamePropertyVal) XXX_Size() int {
	return xxx_messageInfo_GamePropertyVal.Size(m)
}
func (m *GamePropertyVal) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePropertyVal.DiscardUnknown(m)
}

var xxx_messageInfo_GamePropertyVal proto.InternalMessageInfo

func (m *GamePropertyVal) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GamePropertyVal) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 设置秒接单开关
type SetQuickReceiveSwitchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Switch               bool     `protobuf:"varint,2,opt,name=switch,proto3" json:"switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetQuickReceiveSwitchRequest) Reset()         { *m = SetQuickReceiveSwitchRequest{} }
func (m *SetQuickReceiveSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetQuickReceiveSwitchRequest) ProtoMessage()    {}
func (*SetQuickReceiveSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{56}
}
func (m *SetQuickReceiveSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetQuickReceiveSwitchRequest.Unmarshal(m, b)
}
func (m *SetQuickReceiveSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetQuickReceiveSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetQuickReceiveSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetQuickReceiveSwitchRequest.Merge(dst, src)
}
func (m *SetQuickReceiveSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetQuickReceiveSwitchRequest.Size(m)
}
func (m *SetQuickReceiveSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetQuickReceiveSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetQuickReceiveSwitchRequest proto.InternalMessageInfo

func (m *SetQuickReceiveSwitchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetQuickReceiveSwitchRequest) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

type SetQuickReceiveSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetQuickReceiveSwitchResponse) Reset()         { *m = SetQuickReceiveSwitchResponse{} }
func (m *SetQuickReceiveSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetQuickReceiveSwitchResponse) ProtoMessage()    {}
func (*SetQuickReceiveSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{57}
}
func (m *SetQuickReceiveSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetQuickReceiveSwitchResponse.Unmarshal(m, b)
}
func (m *SetQuickReceiveSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetQuickReceiveSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetQuickReceiveSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetQuickReceiveSwitchResponse.Merge(dst, src)
}
func (m *SetQuickReceiveSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetQuickReceiveSwitchResponse.Size(m)
}
func (m *SetQuickReceiveSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetQuickReceiveSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetQuickReceiveSwitchResponse proto.InternalMessageInfo

// 获取秒接单开关状态
type GetQuickReceiveSwitchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickReceiveSwitchRequest) Reset()         { *m = GetQuickReceiveSwitchRequest{} }
func (m *GetQuickReceiveSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*GetQuickReceiveSwitchRequest) ProtoMessage()    {}
func (*GetQuickReceiveSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{58}
}
func (m *GetQuickReceiveSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickReceiveSwitchRequest.Unmarshal(m, b)
}
func (m *GetQuickReceiveSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickReceiveSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *GetQuickReceiveSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickReceiveSwitchRequest.Merge(dst, src)
}
func (m *GetQuickReceiveSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_GetQuickReceiveSwitchRequest.Size(m)
}
func (m *GetQuickReceiveSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickReceiveSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickReceiveSwitchRequest proto.InternalMessageInfo

func (m *GetQuickReceiveSwitchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetQuickReceiveSwitchResponse struct {
	Switch               bool     `protobuf:"varint,1,opt,name=switch,proto3" json:"switch,omitempty"`
	CouldOpen            bool     `protobuf:"varint,2,opt,name=could_open,json=couldOpen,proto3" json:"could_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickReceiveSwitchResponse) Reset()         { *m = GetQuickReceiveSwitchResponse{} }
func (m *GetQuickReceiveSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*GetQuickReceiveSwitchResponse) ProtoMessage()    {}
func (*GetQuickReceiveSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{59}
}
func (m *GetQuickReceiveSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickReceiveSwitchResponse.Unmarshal(m, b)
}
func (m *GetQuickReceiveSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickReceiveSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *GetQuickReceiveSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickReceiveSwitchResponse.Merge(dst, src)
}
func (m *GetQuickReceiveSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_GetQuickReceiveSwitchResponse.Size(m)
}
func (m *GetQuickReceiveSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickReceiveSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickReceiveSwitchResponse proto.InternalMessageInfo

func (m *GetQuickReceiveSwitchResponse) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

func (m *GetQuickReceiveSwitchResponse) GetCouldOpen() bool {
	if m != nil {
		return m.CouldOpen
	}
	return false
}

// 获取秒接单开关状态
type BatchGetQuickReceiveSwitchRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetQuickReceiveSwitchRequest) Reset()         { *m = BatchGetQuickReceiveSwitchRequest{} }
func (m *BatchGetQuickReceiveSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetQuickReceiveSwitchRequest) ProtoMessage()    {}
func (*BatchGetQuickReceiveSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{60}
}
func (m *BatchGetQuickReceiveSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetQuickReceiveSwitchRequest.Unmarshal(m, b)
}
func (m *BatchGetQuickReceiveSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetQuickReceiveSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetQuickReceiveSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetQuickReceiveSwitchRequest.Merge(dst, src)
}
func (m *BatchGetQuickReceiveSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetQuickReceiveSwitchRequest.Size(m)
}
func (m *BatchGetQuickReceiveSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetQuickReceiveSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetQuickReceiveSwitchRequest proto.InternalMessageInfo

func (m *BatchGetQuickReceiveSwitchRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetQuickReceiveSwitchResponse struct {
	SwitchMap            map[uint32]bool `protobuf:"bytes,1,rep,name=switch_map,json=switchMap,proto3" json:"switch_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetQuickReceiveSwitchResponse) Reset()         { *m = BatchGetQuickReceiveSwitchResponse{} }
func (m *BatchGetQuickReceiveSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetQuickReceiveSwitchResponse) ProtoMessage()    {}
func (*BatchGetQuickReceiveSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{61}
}
func (m *BatchGetQuickReceiveSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetQuickReceiveSwitchResponse.Unmarshal(m, b)
}
func (m *BatchGetQuickReceiveSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetQuickReceiveSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetQuickReceiveSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetQuickReceiveSwitchResponse.Merge(dst, src)
}
func (m *BatchGetQuickReceiveSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetQuickReceiveSwitchResponse.Size(m)
}
func (m *BatchGetQuickReceiveSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetQuickReceiveSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetQuickReceiveSwitchResponse proto.InternalMessageInfo

func (m *BatchGetQuickReceiveSwitchResponse) GetSwitchMap() map[uint32]bool {
	if m != nil {
		return m.SwitchMap
	}
	return nil
}

type HasFamousPlayerRequest struct {
	SkillId              uint32   `protobuf:"varint,1,opt,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HasFamousPlayerRequest) Reset()         { *m = HasFamousPlayerRequest{} }
func (m *HasFamousPlayerRequest) String() string { return proto.CompactTextString(m) }
func (*HasFamousPlayerRequest) ProtoMessage()    {}
func (*HasFamousPlayerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{62}
}
func (m *HasFamousPlayerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HasFamousPlayerRequest.Unmarshal(m, b)
}
func (m *HasFamousPlayerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HasFamousPlayerRequest.Marshal(b, m, deterministic)
}
func (dst *HasFamousPlayerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HasFamousPlayerRequest.Merge(dst, src)
}
func (m *HasFamousPlayerRequest) XXX_Size() int {
	return xxx_messageInfo_HasFamousPlayerRequest.Size(m)
}
func (m *HasFamousPlayerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HasFamousPlayerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HasFamousPlayerRequest proto.InternalMessageInfo

func (m *HasFamousPlayerRequest) GetSkillId() uint32 {
	if m != nil {
		return m.SkillId
	}
	return 0
}

type HasFamousPlayerResponse struct {
	Cnt                  uint32   `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HasFamousPlayerResponse) Reset()         { *m = HasFamousPlayerResponse{} }
func (m *HasFamousPlayerResponse) String() string { return proto.CompactTextString(m) }
func (*HasFamousPlayerResponse) ProtoMessage()    {}
func (*HasFamousPlayerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{63}
}
func (m *HasFamousPlayerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HasFamousPlayerResponse.Unmarshal(m, b)
}
func (m *HasFamousPlayerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HasFamousPlayerResponse.Marshal(b, m, deterministic)
}
func (dst *HasFamousPlayerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HasFamousPlayerResponse.Merge(dst, src)
}
func (m *HasFamousPlayerResponse) XXX_Size() int {
	return xxx_messageInfo_HasFamousPlayerResponse.Size(m)
}
func (m *HasFamousPlayerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HasFamousPlayerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HasFamousPlayerResponse proto.InternalMessageInfo

func (m *HasFamousPlayerResponse) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type SetGuaranteeWinSwitchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SkillId              uint32   `protobuf:"varint,2,opt,name=skill_id,json=skillId,proto3" json:"skill_id,omitempty"`
	Switch               bool     `protobuf:"varint,3,opt,name=switch,proto3" json:"switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGuaranteeWinSwitchRequest) Reset()         { *m = SetGuaranteeWinSwitchRequest{} }
func (m *SetGuaranteeWinSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetGuaranteeWinSwitchRequest) ProtoMessage()    {}
func (*SetGuaranteeWinSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{64}
}
func (m *SetGuaranteeWinSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGuaranteeWinSwitchRequest.Unmarshal(m, b)
}
func (m *SetGuaranteeWinSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGuaranteeWinSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetGuaranteeWinSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGuaranteeWinSwitchRequest.Merge(dst, src)
}
func (m *SetGuaranteeWinSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetGuaranteeWinSwitchRequest.Size(m)
}
func (m *SetGuaranteeWinSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGuaranteeWinSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetGuaranteeWinSwitchRequest proto.InternalMessageInfo

func (m *SetGuaranteeWinSwitchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetGuaranteeWinSwitchRequest) GetSkillId() uint32 {
	if m != nil {
		return m.SkillId
	}
	return 0
}

func (m *SetGuaranteeWinSwitchRequest) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

type SetGuaranteeWinSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGuaranteeWinSwitchResponse) Reset()         { *m = SetGuaranteeWinSwitchResponse{} }
func (m *SetGuaranteeWinSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetGuaranteeWinSwitchResponse) ProtoMessage()    {}
func (*SetGuaranteeWinSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{65}
}
func (m *SetGuaranteeWinSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGuaranteeWinSwitchResponse.Unmarshal(m, b)
}
func (m *SetGuaranteeWinSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGuaranteeWinSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetGuaranteeWinSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGuaranteeWinSwitchResponse.Merge(dst, src)
}
func (m *SetGuaranteeWinSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetGuaranteeWinSwitchResponse.Size(m)
}
func (m *SetGuaranteeWinSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGuaranteeWinSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetGuaranteeWinSwitchResponse proto.InternalMessageInfo

// ***************** 电竞游戏名片客户端接口 ****************
type EsportGameCardInfo struct {
	GameIcon             string                    `protobuf:"bytes,1,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	TopContent           string                    `protobuf:"bytes,2,opt,name=top_content,json=topContent,proto3" json:"top_content,omitempty"`
	InfoItemList         []*EsportGameCardInfoItem `protobuf:"bytes,3,rep,name=info_item_list,json=infoItemList,proto3" json:"info_item_list,omitempty"`
	CardId               uint32                    `protobuf:"varint,4,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	GameId               uint32                    `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *EsportGameCardInfo) Reset()         { *m = EsportGameCardInfo{} }
func (m *EsportGameCardInfo) String() string { return proto.CompactTextString(m) }
func (*EsportGameCardInfo) ProtoMessage()    {}
func (*EsportGameCardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{66}
}
func (m *EsportGameCardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportGameCardInfo.Unmarshal(m, b)
}
func (m *EsportGameCardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportGameCardInfo.Marshal(b, m, deterministic)
}
func (dst *EsportGameCardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportGameCardInfo.Merge(dst, src)
}
func (m *EsportGameCardInfo) XXX_Size() int {
	return xxx_messageInfo_EsportGameCardInfo.Size(m)
}
func (m *EsportGameCardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportGameCardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EsportGameCardInfo proto.InternalMessageInfo

func (m *EsportGameCardInfo) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *EsportGameCardInfo) GetTopContent() string {
	if m != nil {
		return m.TopContent
	}
	return ""
}

func (m *EsportGameCardInfo) GetInfoItemList() []*EsportGameCardInfoItem {
	if m != nil {
		return m.InfoItemList
	}
	return nil
}

func (m *EsportGameCardInfo) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *EsportGameCardInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type EsportGameCardInfoItem struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportGameCardInfoItem) Reset()         { *m = EsportGameCardInfoItem{} }
func (m *EsportGameCardInfoItem) String() string { return proto.CompactTextString(m) }
func (*EsportGameCardInfoItem) ProtoMessage()    {}
func (*EsportGameCardInfoItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{67}
}
func (m *EsportGameCardInfoItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportGameCardInfoItem.Unmarshal(m, b)
}
func (m *EsportGameCardInfoItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportGameCardInfoItem.Marshal(b, m, deterministic)
}
func (dst *EsportGameCardInfoItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportGameCardInfoItem.Merge(dst, src)
}
func (m *EsportGameCardInfoItem) XXX_Size() int {
	return xxx_messageInfo_EsportGameCardInfoItem.Size(m)
}
func (m *EsportGameCardInfoItem) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportGameCardInfoItem.DiscardUnknown(m)
}

var xxx_messageInfo_EsportGameCardInfoItem proto.InternalMessageInfo

func (m *EsportGameCardInfoItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *EsportGameCardInfoItem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// 获取游戏名片配置信息
type GetEsportGameCardConfigRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEsportGameCardConfigRequest) Reset()         { *m = GetEsportGameCardConfigRequest{} }
func (m *GetEsportGameCardConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameCardConfigRequest) ProtoMessage()    {}
func (*GetEsportGameCardConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{68}
}
func (m *GetEsportGameCardConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameCardConfigRequest.Unmarshal(m, b)
}
func (m *GetEsportGameCardConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameCardConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameCardConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameCardConfigRequest.Merge(dst, src)
}
func (m *GetEsportGameCardConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameCardConfigRequest.Size(m)
}
func (m *GetEsportGameCardConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameCardConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameCardConfigRequest proto.InternalMessageInfo

func (m *GetEsportGameCardConfigRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetEsportGameCardConfigResponse struct {
	InfoItemList         []*EsportGameCardInfoItem `protobuf:"bytes,1,rep,name=info_item_list,json=infoItemList,proto3" json:"info_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetEsportGameCardConfigResponse) Reset()         { *m = GetEsportGameCardConfigResponse{} }
func (m *GetEsportGameCardConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameCardConfigResponse) ProtoMessage()    {}
func (*GetEsportGameCardConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{69}
}
func (m *GetEsportGameCardConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameCardConfigResponse.Unmarshal(m, b)
}
func (m *GetEsportGameCardConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameCardConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameCardConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameCardConfigResponse.Merge(dst, src)
}
func (m *GetEsportGameCardConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameCardConfigResponse.Size(m)
}
func (m *GetEsportGameCardConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameCardConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameCardConfigResponse proto.InternalMessageInfo

func (m *GetEsportGameCardConfigResponse) GetInfoItemList() []*EsportGameCardInfoItem {
	if m != nil {
		return m.InfoItemList
	}
	return nil
}

// 查询游戏名片信息
type GetEsportGameCardInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CardId               uint32   `protobuf:"varint,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEsportGameCardInfoRequest) Reset()         { *m = GetEsportGameCardInfoRequest{} }
func (m *GetEsportGameCardInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameCardInfoRequest) ProtoMessage()    {}
func (*GetEsportGameCardInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{70}
}
func (m *GetEsportGameCardInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameCardInfoRequest.Unmarshal(m, b)
}
func (m *GetEsportGameCardInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameCardInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameCardInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameCardInfoRequest.Merge(dst, src)
}
func (m *GetEsportGameCardInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameCardInfoRequest.Size(m)
}
func (m *GetEsportGameCardInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameCardInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameCardInfoRequest proto.InternalMessageInfo

func (m *GetEsportGameCardInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetEsportGameCardInfoRequest) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type GetEsportGameCardInfoResponse struct {
	InfoItemList         []*EsportGameCardInfoItem `protobuf:"bytes,1,rep,name=info_item_list,json=infoItemList,proto3" json:"info_item_list,omitempty"`
	GameId               uint32                    `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetEsportGameCardInfoResponse) Reset()         { *m = GetEsportGameCardInfoResponse{} }
func (m *GetEsportGameCardInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameCardInfoResponse) ProtoMessage()    {}
func (*GetEsportGameCardInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{71}
}
func (m *GetEsportGameCardInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameCardInfoResponse.Unmarshal(m, b)
}
func (m *GetEsportGameCardInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameCardInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameCardInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameCardInfoResponse.Merge(dst, src)
}
func (m *GetEsportGameCardInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameCardInfoResponse.Size(m)
}
func (m *GetEsportGameCardInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameCardInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameCardInfoResponse proto.InternalMessageInfo

func (m *GetEsportGameCardInfoResponse) GetInfoItemList() []*EsportGameCardInfoItem {
	if m != nil {
		return m.InfoItemList
	}
	return nil
}

func (m *GetEsportGameCardInfoResponse) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 创建游戏名片
type CreateEsportGameCardRequest struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32                    `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CardId               uint32                    `protobuf:"varint,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	InfoItemList         []*EsportGameCardInfoItem `protobuf:"bytes,4,rep,name=info_item_list,json=infoItemList,proto3" json:"info_item_list,omitempty"`
	MsgId                uint64                    `protobuf:"varint,5,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	TargetUid            uint32                    `protobuf:"varint,6,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *CreateEsportGameCardRequest) Reset()         { *m = CreateEsportGameCardRequest{} }
func (m *CreateEsportGameCardRequest) String() string { return proto.CompactTextString(m) }
func (*CreateEsportGameCardRequest) ProtoMessage()    {}
func (*CreateEsportGameCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{72}
}
func (m *CreateEsportGameCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateEsportGameCardRequest.Unmarshal(m, b)
}
func (m *CreateEsportGameCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateEsportGameCardRequest.Marshal(b, m, deterministic)
}
func (dst *CreateEsportGameCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateEsportGameCardRequest.Merge(dst, src)
}
func (m *CreateEsportGameCardRequest) XXX_Size() int {
	return xxx_messageInfo_CreateEsportGameCardRequest.Size(m)
}
func (m *CreateEsportGameCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateEsportGameCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateEsportGameCardRequest proto.InternalMessageInfo

func (m *CreateEsportGameCardRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateEsportGameCardRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CreateEsportGameCardRequest) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *CreateEsportGameCardRequest) GetInfoItemList() []*EsportGameCardInfoItem {
	if m != nil {
		return m.InfoItemList
	}
	return nil
}

func (m *CreateEsportGameCardRequest) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *CreateEsportGameCardRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type CreateEsportGameCardResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateEsportGameCardResponse) Reset()         { *m = CreateEsportGameCardResponse{} }
func (m *CreateEsportGameCardResponse) String() string { return proto.CompactTextString(m) }
func (*CreateEsportGameCardResponse) ProtoMessage()    {}
func (*CreateEsportGameCardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{73}
}
func (m *CreateEsportGameCardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateEsportGameCardResponse.Unmarshal(m, b)
}
func (m *CreateEsportGameCardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateEsportGameCardResponse.Marshal(b, m, deterministic)
}
func (dst *CreateEsportGameCardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateEsportGameCardResponse.Merge(dst, src)
}
func (m *CreateEsportGameCardResponse) XXX_Size() int {
	return xxx_messageInfo_CreateEsportGameCardResponse.Size(m)
}
func (m *CreateEsportGameCardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateEsportGameCardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateEsportGameCardResponse proto.InternalMessageInfo

// 更新游戏名片
type UpdateEsportGameCardRequest struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32                    `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CardId               uint32                    `protobuf:"varint,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	InfoItemList         []*EsportGameCardInfoItem `protobuf:"bytes,4,rep,name=info_item_list,json=infoItemList,proto3" json:"info_item_list,omitempty"`
	MsgId                uint64                    `protobuf:"varint,5,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	TargetUid            uint32                    `protobuf:"varint,6,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdateEsportGameCardRequest) Reset()         { *m = UpdateEsportGameCardRequest{} }
func (m *UpdateEsportGameCardRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateEsportGameCardRequest) ProtoMessage()    {}
func (*UpdateEsportGameCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{74}
}
func (m *UpdateEsportGameCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEsportGameCardRequest.Unmarshal(m, b)
}
func (m *UpdateEsportGameCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEsportGameCardRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateEsportGameCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEsportGameCardRequest.Merge(dst, src)
}
func (m *UpdateEsportGameCardRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateEsportGameCardRequest.Size(m)
}
func (m *UpdateEsportGameCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEsportGameCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEsportGameCardRequest proto.InternalMessageInfo

func (m *UpdateEsportGameCardRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateEsportGameCardRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UpdateEsportGameCardRequest) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *UpdateEsportGameCardRequest) GetInfoItemList() []*EsportGameCardInfoItem {
	if m != nil {
		return m.InfoItemList
	}
	return nil
}

func (m *UpdateEsportGameCardRequest) GetMsgId() uint64 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *UpdateEsportGameCardRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type UpdateEsportGameCardResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateEsportGameCardResponse) Reset()         { *m = UpdateEsportGameCardResponse{} }
func (m *UpdateEsportGameCardResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateEsportGameCardResponse) ProtoMessage()    {}
func (*UpdateEsportGameCardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{75}
}
func (m *UpdateEsportGameCardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEsportGameCardResponse.Unmarshal(m, b)
}
func (m *UpdateEsportGameCardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEsportGameCardResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateEsportGameCardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEsportGameCardResponse.Merge(dst, src)
}
func (m *UpdateEsportGameCardResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateEsportGameCardResponse.Size(m)
}
func (m *UpdateEsportGameCardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEsportGameCardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEsportGameCardResponse proto.InternalMessageInfo

type GetEsportGameCardListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEsportGameCardListRequest) Reset()         { *m = GetEsportGameCardListRequest{} }
func (m *GetEsportGameCardListRequest) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameCardListRequest) ProtoMessage()    {}
func (*GetEsportGameCardListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{76}
}
func (m *GetEsportGameCardListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameCardListRequest.Unmarshal(m, b)
}
func (m *GetEsportGameCardListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameCardListRequest.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameCardListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameCardListRequest.Merge(dst, src)
}
func (m *GetEsportGameCardListRequest) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameCardListRequest.Size(m)
}
func (m *GetEsportGameCardListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameCardListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameCardListRequest proto.InternalMessageInfo

func (m *GetEsportGameCardListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetEsportGameCardListResponse struct {
	CardList             []*EsportGameCardInfo `protobuf:"bytes,1,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetEsportGameCardListResponse) Reset()         { *m = GetEsportGameCardListResponse{} }
func (m *GetEsportGameCardListResponse) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameCardListResponse) ProtoMessage()    {}
func (*GetEsportGameCardListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{77}
}
func (m *GetEsportGameCardListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameCardListResponse.Unmarshal(m, b)
}
func (m *GetEsportGameCardListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameCardListResponse.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameCardListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameCardListResponse.Merge(dst, src)
}
func (m *GetEsportGameCardListResponse) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameCardListResponse.Size(m)
}
func (m *GetEsportGameCardListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameCardListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameCardListResponse proto.InternalMessageInfo

func (m *GetEsportGameCardListResponse) GetCardList() []*EsportGameCardInfo {
	if m != nil {
		return m.CardList
	}
	return nil
}

type SendEsportGameCardRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CardId               uint32   `protobuf:"varint,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	TargetUid            uint32   `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendEsportGameCardRequest) Reset()         { *m = SendEsportGameCardRequest{} }
func (m *SendEsportGameCardRequest) String() string { return proto.CompactTextString(m) }
func (*SendEsportGameCardRequest) ProtoMessage()    {}
func (*SendEsportGameCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{78}
}
func (m *SendEsportGameCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendEsportGameCardRequest.Unmarshal(m, b)
}
func (m *SendEsportGameCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendEsportGameCardRequest.Marshal(b, m, deterministic)
}
func (dst *SendEsportGameCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendEsportGameCardRequest.Merge(dst, src)
}
func (m *SendEsportGameCardRequest) XXX_Size() int {
	return xxx_messageInfo_SendEsportGameCardRequest.Size(m)
}
func (m *SendEsportGameCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendEsportGameCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendEsportGameCardRequest proto.InternalMessageInfo

func (m *SendEsportGameCardRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendEsportGameCardRequest) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *SendEsportGameCardRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type SendEsportGameCardResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendEsportGameCardResponse) Reset()         { *m = SendEsportGameCardResponse{} }
func (m *SendEsportGameCardResponse) String() string { return proto.CompactTextString(m) }
func (*SendEsportGameCardResponse) ProtoMessage()    {}
func (*SendEsportGameCardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{79}
}
func (m *SendEsportGameCardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendEsportGameCardResponse.Unmarshal(m, b)
}
func (m *SendEsportGameCardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendEsportGameCardResponse.Marshal(b, m, deterministic)
}
func (dst *SendEsportGameCardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendEsportGameCardResponse.Merge(dst, src)
}
func (m *SendEsportGameCardResponse) XXX_Size() int {
	return xxx_messageInfo_SendEsportGameCardResponse.Size(m)
}
func (m *SendEsportGameCardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendEsportGameCardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendEsportGameCardResponse proto.InternalMessageInfo

type DeleteEsportGameCardRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CardId               uint32   `protobuf:"varint,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteEsportGameCardRequest) Reset()         { *m = DeleteEsportGameCardRequest{} }
func (m *DeleteEsportGameCardRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteEsportGameCardRequest) ProtoMessage()    {}
func (*DeleteEsportGameCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{80}
}
func (m *DeleteEsportGameCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteEsportGameCardRequest.Unmarshal(m, b)
}
func (m *DeleteEsportGameCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteEsportGameCardRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteEsportGameCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteEsportGameCardRequest.Merge(dst, src)
}
func (m *DeleteEsportGameCardRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteEsportGameCardRequest.Size(m)
}
func (m *DeleteEsportGameCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteEsportGameCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteEsportGameCardRequest proto.InternalMessageInfo

func (m *DeleteEsportGameCardRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeleteEsportGameCardRequest) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

type DeleteEsportGameCardResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteEsportGameCardResponse) Reset()         { *m = DeleteEsportGameCardResponse{} }
func (m *DeleteEsportGameCardResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteEsportGameCardResponse) ProtoMessage()    {}
func (*DeleteEsportGameCardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{81}
}
func (m *DeleteEsportGameCardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteEsportGameCardResponse.Unmarshal(m, b)
}
func (m *DeleteEsportGameCardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteEsportGameCardResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteEsportGameCardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteEsportGameCardResponse.Merge(dst, src)
}
func (m *DeleteEsportGameCardResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteEsportGameCardResponse.Size(m)
}
func (m *DeleteEsportGameCardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteEsportGameCardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteEsportGameCardResponse proto.InternalMessageInfo

type SetFirstRoundSwitchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	IsOpen               bool     `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetFirstRoundSwitchRequest) Reset()         { *m = SetFirstRoundSwitchRequest{} }
func (m *SetFirstRoundSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetFirstRoundSwitchRequest) ProtoMessage()    {}
func (*SetFirstRoundSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{82}
}
func (m *SetFirstRoundSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFirstRoundSwitchRequest.Unmarshal(m, b)
}
func (m *SetFirstRoundSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFirstRoundSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetFirstRoundSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFirstRoundSwitchRequest.Merge(dst, src)
}
func (m *SetFirstRoundSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetFirstRoundSwitchRequest.Size(m)
}
func (m *SetFirstRoundSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFirstRoundSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetFirstRoundSwitchRequest proto.InternalMessageInfo

func (m *SetFirstRoundSwitchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetFirstRoundSwitchRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetFirstRoundSwitchRequest) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetFirstRoundSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetFirstRoundSwitchResponse) Reset()         { *m = SetFirstRoundSwitchResponse{} }
func (m *SetFirstRoundSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetFirstRoundSwitchResponse) ProtoMessage()    {}
func (*SetFirstRoundSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{83}
}
func (m *SetFirstRoundSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFirstRoundSwitchResponse.Unmarshal(m, b)
}
func (m *SetFirstRoundSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFirstRoundSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetFirstRoundSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFirstRoundSwitchResponse.Merge(dst, src)
}
func (m *SetFirstRoundSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetFirstRoundSwitchResponse.Size(m)
}
func (m *SetFirstRoundSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFirstRoundSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetFirstRoundSwitchResponse proto.InternalMessageInfo

type GetFirstRoundDiscountInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFirstRoundDiscountInfoRequest) Reset()         { *m = GetFirstRoundDiscountInfoRequest{} }
func (m *GetFirstRoundDiscountInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundDiscountInfoRequest) ProtoMessage()    {}
func (*GetFirstRoundDiscountInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{84}
}
func (m *GetFirstRoundDiscountInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundDiscountInfoRequest.Unmarshal(m, b)
}
func (m *GetFirstRoundDiscountInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundDiscountInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundDiscountInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundDiscountInfoRequest.Merge(dst, src)
}
func (m *GetFirstRoundDiscountInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundDiscountInfoRequest.Size(m)
}
func (m *GetFirstRoundDiscountInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundDiscountInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundDiscountInfoRequest proto.InternalMessageInfo

func (m *GetFirstRoundDiscountInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFirstRoundDiscountInfoRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetFirstRoundDiscountInfoResponse struct {
	ShowSwitch           bool     `protobuf:"varint,1,opt,name=show_switch,json=showSwitch,proto3" json:"show_switch,omitempty"`
	FirstRoundPrice      uint32   `protobuf:"varint,2,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price,omitempty"`
	IsOpen               bool     `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFirstRoundDiscountInfoResponse) Reset()         { *m = GetFirstRoundDiscountInfoResponse{} }
func (m *GetFirstRoundDiscountInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundDiscountInfoResponse) ProtoMessage()    {}
func (*GetFirstRoundDiscountInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{85}
}
func (m *GetFirstRoundDiscountInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundDiscountInfoResponse.Unmarshal(m, b)
}
func (m *GetFirstRoundDiscountInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundDiscountInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundDiscountInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundDiscountInfoResponse.Merge(dst, src)
}
func (m *GetFirstRoundDiscountInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundDiscountInfoResponse.Size(m)
}
func (m *GetFirstRoundDiscountInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundDiscountInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundDiscountInfoResponse proto.InternalMessageInfo

func (m *GetFirstRoundDiscountInfoResponse) GetShowSwitch() bool {
	if m != nil {
		return m.ShowSwitch
	}
	return false
}

func (m *GetFirstRoundDiscountInfoResponse) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

func (m *GetFirstRoundDiscountInfoResponse) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type GetFirstRoundDiscountGameListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFirstRoundDiscountGameListRequest) Reset()         { *m = GetFirstRoundDiscountGameListRequest{} }
func (m *GetFirstRoundDiscountGameListRequest) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundDiscountGameListRequest) ProtoMessage()    {}
func (*GetFirstRoundDiscountGameListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{86}
}
func (m *GetFirstRoundDiscountGameListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundDiscountGameListRequest.Unmarshal(m, b)
}
func (m *GetFirstRoundDiscountGameListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundDiscountGameListRequest.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundDiscountGameListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundDiscountGameListRequest.Merge(dst, src)
}
func (m *GetFirstRoundDiscountGameListRequest) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundDiscountGameListRequest.Size(m)
}
func (m *GetFirstRoundDiscountGameListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundDiscountGameListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundDiscountGameListRequest proto.InternalMessageInfo

type FirstRoundDiscountGameInfo struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	FirstRoundPrice      uint32   `protobuf:"varint,2,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FirstRoundDiscountGameInfo) Reset()         { *m = FirstRoundDiscountGameInfo{} }
func (m *FirstRoundDiscountGameInfo) String() string { return proto.CompactTextString(m) }
func (*FirstRoundDiscountGameInfo) ProtoMessage()    {}
func (*FirstRoundDiscountGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{87}
}
func (m *FirstRoundDiscountGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FirstRoundDiscountGameInfo.Unmarshal(m, b)
}
func (m *FirstRoundDiscountGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FirstRoundDiscountGameInfo.Marshal(b, m, deterministic)
}
func (dst *FirstRoundDiscountGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FirstRoundDiscountGameInfo.Merge(dst, src)
}
func (m *FirstRoundDiscountGameInfo) XXX_Size() int {
	return xxx_messageInfo_FirstRoundDiscountGameInfo.Size(m)
}
func (m *FirstRoundDiscountGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FirstRoundDiscountGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FirstRoundDiscountGameInfo proto.InternalMessageInfo

func (m *FirstRoundDiscountGameInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *FirstRoundDiscountGameInfo) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

type GetFirstRoundDiscountGameListResponse struct {
	GameList             []*FirstRoundDiscountGameInfo `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetFirstRoundDiscountGameListResponse) Reset()         { *m = GetFirstRoundDiscountGameListResponse{} }
func (m *GetFirstRoundDiscountGameListResponse) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundDiscountGameListResponse) ProtoMessage()    {}
func (*GetFirstRoundDiscountGameListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{88}
}
func (m *GetFirstRoundDiscountGameListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundDiscountGameListResponse.Unmarshal(m, b)
}
func (m *GetFirstRoundDiscountGameListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundDiscountGameListResponse.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundDiscountGameListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundDiscountGameListResponse.Merge(dst, src)
}
func (m *GetFirstRoundDiscountGameListResponse) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundDiscountGameListResponse.Size(m)
}
func (m *GetFirstRoundDiscountGameListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundDiscountGameListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundDiscountGameListResponse proto.InternalMessageInfo

func (m *GetFirstRoundDiscountGameListResponse) GetGameList() []*FirstRoundDiscountGameInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

type CheckFirstRoundOrderRightRequest struct {
	PlayerUid            uint32   `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CheckDayLimit        bool     `protobuf:"varint,4,opt,name=check_day_limit,json=checkDayLimit,proto3" json:"check_day_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckFirstRoundOrderRightRequest) Reset()         { *m = CheckFirstRoundOrderRightRequest{} }
func (m *CheckFirstRoundOrderRightRequest) String() string { return proto.CompactTextString(m) }
func (*CheckFirstRoundOrderRightRequest) ProtoMessage()    {}
func (*CheckFirstRoundOrderRightRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{89}
}
func (m *CheckFirstRoundOrderRightRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFirstRoundOrderRightRequest.Unmarshal(m, b)
}
func (m *CheckFirstRoundOrderRightRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFirstRoundOrderRightRequest.Marshal(b, m, deterministic)
}
func (dst *CheckFirstRoundOrderRightRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFirstRoundOrderRightRequest.Merge(dst, src)
}
func (m *CheckFirstRoundOrderRightRequest) XXX_Size() int {
	return xxx_messageInfo_CheckFirstRoundOrderRightRequest.Size(m)
}
func (m *CheckFirstRoundOrderRightRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFirstRoundOrderRightRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFirstRoundOrderRightRequest proto.InternalMessageInfo

func (m *CheckFirstRoundOrderRightRequest) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *CheckFirstRoundOrderRightRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *CheckFirstRoundOrderRightRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CheckFirstRoundOrderRightRequest) GetCheckDayLimit() bool {
	if m != nil {
		return m.CheckDayLimit
	}
	return false
}

type CheckFirstRoundOrderRightResponse struct {
	FirstRoundPrice      uint32   `protobuf:"varint,1,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckFirstRoundOrderRightResponse) Reset()         { *m = CheckFirstRoundOrderRightResponse{} }
func (m *CheckFirstRoundOrderRightResponse) String() string { return proto.CompactTextString(m) }
func (*CheckFirstRoundOrderRightResponse) ProtoMessage()    {}
func (*CheckFirstRoundOrderRightResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{90}
}
func (m *CheckFirstRoundOrderRightResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFirstRoundOrderRightResponse.Unmarshal(m, b)
}
func (m *CheckFirstRoundOrderRightResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFirstRoundOrderRightResponse.Marshal(b, m, deterministic)
}
func (dst *CheckFirstRoundOrderRightResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFirstRoundOrderRightResponse.Merge(dst, src)
}
func (m *CheckFirstRoundOrderRightResponse) XXX_Size() int {
	return xxx_messageInfo_CheckFirstRoundOrderRightResponse.Size(m)
}
func (m *CheckFirstRoundOrderRightResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFirstRoundOrderRightResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFirstRoundOrderRightResponse proto.InternalMessageInfo

func (m *CheckFirstRoundOrderRightResponse) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

type GetFirstRoundOrderRightRequest struct {
	PlayerUid            uint32   `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFirstRoundOrderRightRequest) Reset()         { *m = GetFirstRoundOrderRightRequest{} }
func (m *GetFirstRoundOrderRightRequest) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundOrderRightRequest) ProtoMessage()    {}
func (*GetFirstRoundOrderRightRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{91}
}
func (m *GetFirstRoundOrderRightRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundOrderRightRequest.Unmarshal(m, b)
}
func (m *GetFirstRoundOrderRightRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundOrderRightRequest.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundOrderRightRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundOrderRightRequest.Merge(dst, src)
}
func (m *GetFirstRoundOrderRightRequest) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundOrderRightRequest.Size(m)
}
func (m *GetFirstRoundOrderRightRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundOrderRightRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundOrderRightRequest proto.InternalMessageInfo

func (m *GetFirstRoundOrderRightRequest) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *GetFirstRoundOrderRightRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetFirstRoundOrderRightRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetFirstRoundOrderRightResponse struct {
	FirstRoundPrice               uint32   `protobuf:"varint,1,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price,omitempty"`
	HasFirstRoundDiscount         bool     `protobuf:"varint,2,opt,name=has_first_round_discount,json=hasFirstRoundDiscount,proto3" json:"has_first_round_discount,omitempty"`
	TodayCanUseFirstRoundDiscount bool     `protobuf:"varint,3,opt,name=today_can_use_first_round_discount,json=todayCanUseFirstRoundDiscount,proto3" json:"today_can_use_first_round_discount,omitempty"`
	XXX_NoUnkeyedLiteral          struct{} `json:"-"`
	XXX_unrecognized              []byte   `json:"-"`
	XXX_sizecache                 int32    `json:"-"`
}

func (m *GetFirstRoundOrderRightResponse) Reset()         { *m = GetFirstRoundOrderRightResponse{} }
func (m *GetFirstRoundOrderRightResponse) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundOrderRightResponse) ProtoMessage()    {}
func (*GetFirstRoundOrderRightResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{92}
}
func (m *GetFirstRoundOrderRightResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundOrderRightResponse.Unmarshal(m, b)
}
func (m *GetFirstRoundOrderRightResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundOrderRightResponse.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundOrderRightResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundOrderRightResponse.Merge(dst, src)
}
func (m *GetFirstRoundOrderRightResponse) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundOrderRightResponse.Size(m)
}
func (m *GetFirstRoundOrderRightResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundOrderRightResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundOrderRightResponse proto.InternalMessageInfo

func (m *GetFirstRoundOrderRightResponse) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

func (m *GetFirstRoundOrderRightResponse) GetHasFirstRoundDiscount() bool {
	if m != nil {
		return m.HasFirstRoundDiscount
	}
	return false
}

func (m *GetFirstRoundOrderRightResponse) GetTodayCanUseFirstRoundDiscount() bool {
	if m != nil {
		return m.TodayCanUseFirstRoundDiscount
	}
	return false
}

type ClearFirstRoundOrderRequest struct {
	PlayerUid            uint32   `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearFirstRoundOrderRequest) Reset()         { *m = ClearFirstRoundOrderRequest{} }
func (m *ClearFirstRoundOrderRequest) String() string { return proto.CompactTextString(m) }
func (*ClearFirstRoundOrderRequest) ProtoMessage()    {}
func (*ClearFirstRoundOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{93}
}
func (m *ClearFirstRoundOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearFirstRoundOrderRequest.Unmarshal(m, b)
}
func (m *ClearFirstRoundOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearFirstRoundOrderRequest.Marshal(b, m, deterministic)
}
func (dst *ClearFirstRoundOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearFirstRoundOrderRequest.Merge(dst, src)
}
func (m *ClearFirstRoundOrderRequest) XXX_Size() int {
	return xxx_messageInfo_ClearFirstRoundOrderRequest.Size(m)
}
func (m *ClearFirstRoundOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearFirstRoundOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ClearFirstRoundOrderRequest proto.InternalMessageInfo

func (m *ClearFirstRoundOrderRequest) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *ClearFirstRoundOrderRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *ClearFirstRoundOrderRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ClearFirstRoundOrderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearFirstRoundOrderResponse) Reset()         { *m = ClearFirstRoundOrderResponse{} }
func (m *ClearFirstRoundOrderResponse) String() string { return proto.CompactTextString(m) }
func (*ClearFirstRoundOrderResponse) ProtoMessage()    {}
func (*ClearFirstRoundOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{94}
}
func (m *ClearFirstRoundOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearFirstRoundOrderResponse.Unmarshal(m, b)
}
func (m *ClearFirstRoundOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearFirstRoundOrderResponse.Marshal(b, m, deterministic)
}
func (dst *ClearFirstRoundOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearFirstRoundOrderResponse.Merge(dst, src)
}
func (m *ClearFirstRoundOrderResponse) XXX_Size() int {
	return xxx_messageInfo_ClearFirstRoundOrderResponse.Size(m)
}
func (m *ClearFirstRoundOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearFirstRoundOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ClearFirstRoundOrderResponse proto.InternalMessageInfo

type GetFirstRoundLabelByUidRequest struct {
	PlayerUid            uint32   `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CoachUidList         []uint32 `protobuf:"varint,2,rep,packed,name=coach_uid_list,json=coachUidList,proto3" json:"coach_uid_list,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFirstRoundLabelByUidRequest) Reset()         { *m = GetFirstRoundLabelByUidRequest{} }
func (m *GetFirstRoundLabelByUidRequest) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundLabelByUidRequest) ProtoMessage()    {}
func (*GetFirstRoundLabelByUidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{95}
}
func (m *GetFirstRoundLabelByUidRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundLabelByUidRequest.Unmarshal(m, b)
}
func (m *GetFirstRoundLabelByUidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundLabelByUidRequest.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundLabelByUidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundLabelByUidRequest.Merge(dst, src)
}
func (m *GetFirstRoundLabelByUidRequest) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundLabelByUidRequest.Size(m)
}
func (m *GetFirstRoundLabelByUidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundLabelByUidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundLabelByUidRequest proto.InternalMessageInfo

func (m *GetFirstRoundLabelByUidRequest) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *GetFirstRoundLabelByUidRequest) GetCoachUidList() []uint32 {
	if m != nil {
		return m.CoachUidList
	}
	return nil
}

func (m *GetFirstRoundLabelByUidRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetFirstRoundLabelByUidResponse struct {
	LabelMap             map[uint32]*FirstRoundLabel `protobuf:"bytes,1,rep,name=label_map,json=labelMap,proto3" json:"label_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetFirstRoundLabelByUidResponse) Reset()         { *m = GetFirstRoundLabelByUidResponse{} }
func (m *GetFirstRoundLabelByUidResponse) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundLabelByUidResponse) ProtoMessage()    {}
func (*GetFirstRoundLabelByUidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{96}
}
func (m *GetFirstRoundLabelByUidResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundLabelByUidResponse.Unmarshal(m, b)
}
func (m *GetFirstRoundLabelByUidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundLabelByUidResponse.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundLabelByUidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundLabelByUidResponse.Merge(dst, src)
}
func (m *GetFirstRoundLabelByUidResponse) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundLabelByUidResponse.Size(m)
}
func (m *GetFirstRoundLabelByUidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundLabelByUidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundLabelByUidResponse proto.InternalMessageInfo

func (m *GetFirstRoundLabelByUidResponse) GetLabelMap() map[uint32]*FirstRoundLabel {
	if m != nil {
		return m.LabelMap
	}
	return nil
}

type GetFirstRoundLabelBySkillRequest struct {
	PlayerUid            uint32   `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFirstRoundLabelBySkillRequest) Reset()         { *m = GetFirstRoundLabelBySkillRequest{} }
func (m *GetFirstRoundLabelBySkillRequest) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundLabelBySkillRequest) ProtoMessage()    {}
func (*GetFirstRoundLabelBySkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{97}
}
func (m *GetFirstRoundLabelBySkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundLabelBySkillRequest.Unmarshal(m, b)
}
func (m *GetFirstRoundLabelBySkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundLabelBySkillRequest.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundLabelBySkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundLabelBySkillRequest.Merge(dst, src)
}
func (m *GetFirstRoundLabelBySkillRequest) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundLabelBySkillRequest.Size(m)
}
func (m *GetFirstRoundLabelBySkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundLabelBySkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundLabelBySkillRequest proto.InternalMessageInfo

func (m *GetFirstRoundLabelBySkillRequest) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *GetFirstRoundLabelBySkillRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type GetFirstRoundLabelBySkillResponse struct {
	LabelMap             map[uint32]*FirstRoundLabel `protobuf:"bytes,1,rep,name=label_map,json=labelMap,proto3" json:"label_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetFirstRoundLabelBySkillResponse) Reset()         { *m = GetFirstRoundLabelBySkillResponse{} }
func (m *GetFirstRoundLabelBySkillResponse) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundLabelBySkillResponse) ProtoMessage()    {}
func (*GetFirstRoundLabelBySkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{98}
}
func (m *GetFirstRoundLabelBySkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundLabelBySkillResponse.Unmarshal(m, b)
}
func (m *GetFirstRoundLabelBySkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundLabelBySkillResponse.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundLabelBySkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundLabelBySkillResponse.Merge(dst, src)
}
func (m *GetFirstRoundLabelBySkillResponse) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundLabelBySkillResponse.Size(m)
}
func (m *GetFirstRoundLabelBySkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundLabelBySkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundLabelBySkillResponse proto.InternalMessageInfo

func (m *GetFirstRoundLabelBySkillResponse) GetLabelMap() map[uint32]*FirstRoundLabel {
	if m != nil {
		return m.LabelMap
	}
	return nil
}

type FirstRoundLabel struct {
	PlayerUid             uint32   `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CoachUid              uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId                uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	HasFirstRoundDiscount bool     `protobuf:"varint,4,opt,name=has_first_round_discount,json=hasFirstRoundDiscount,proto3" json:"has_first_round_discount,omitempty"`
	FirstRoundPrice       uint32   `protobuf:"varint,5,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *FirstRoundLabel) Reset()         { *m = FirstRoundLabel{} }
func (m *FirstRoundLabel) String() string { return proto.CompactTextString(m) }
func (*FirstRoundLabel) ProtoMessage()    {}
func (*FirstRoundLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{99}
}
func (m *FirstRoundLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FirstRoundLabel.Unmarshal(m, b)
}
func (m *FirstRoundLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FirstRoundLabel.Marshal(b, m, deterministic)
}
func (dst *FirstRoundLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FirstRoundLabel.Merge(dst, src)
}
func (m *FirstRoundLabel) XXX_Size() int {
	return xxx_messageInfo_FirstRoundLabel.Size(m)
}
func (m *FirstRoundLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_FirstRoundLabel.DiscardUnknown(m)
}

var xxx_messageInfo_FirstRoundLabel proto.InternalMessageInfo

func (m *FirstRoundLabel) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *FirstRoundLabel) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *FirstRoundLabel) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *FirstRoundLabel) GetHasFirstRoundDiscount() bool {
	if m != nil {
		return m.HasFirstRoundDiscount
	}
	return false
}

func (m *FirstRoundLabel) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

type SetNewCustomerSwitchRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	IsOpen               bool     `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNewCustomerSwitchRequest) Reset()         { *m = SetNewCustomerSwitchRequest{} }
func (m *SetNewCustomerSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetNewCustomerSwitchRequest) ProtoMessage()    {}
func (*SetNewCustomerSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{100}
}
func (m *SetNewCustomerSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNewCustomerSwitchRequest.Unmarshal(m, b)
}
func (m *SetNewCustomerSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNewCustomerSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetNewCustomerSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNewCustomerSwitchRequest.Merge(dst, src)
}
func (m *SetNewCustomerSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetNewCustomerSwitchRequest.Size(m)
}
func (m *SetNewCustomerSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNewCustomerSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetNewCustomerSwitchRequest proto.InternalMessageInfo

func (m *SetNewCustomerSwitchRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetNewCustomerSwitchRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetNewCustomerSwitchRequest) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetNewCustomerSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNewCustomerSwitchResponse) Reset()         { *m = SetNewCustomerSwitchResponse{} }
func (m *SetNewCustomerSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetNewCustomerSwitchResponse) ProtoMessage()    {}
func (*SetNewCustomerSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{101}
}
func (m *SetNewCustomerSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNewCustomerSwitchResponse.Unmarshal(m, b)
}
func (m *SetNewCustomerSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNewCustomerSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetNewCustomerSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNewCustomerSwitchResponse.Merge(dst, src)
}
func (m *SetNewCustomerSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetNewCustomerSwitchResponse.Size(m)
}
func (m *SetNewCustomerSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNewCustomerSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetNewCustomerSwitchResponse proto.InternalMessageInfo

type GetNewCustomerDiscountInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewCustomerDiscountInfoRequest) Reset()         { *m = GetNewCustomerDiscountInfoRequest{} }
func (m *GetNewCustomerDiscountInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetNewCustomerDiscountInfoRequest) ProtoMessage()    {}
func (*GetNewCustomerDiscountInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{102}
}
func (m *GetNewCustomerDiscountInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewCustomerDiscountInfoRequest.Unmarshal(m, b)
}
func (m *GetNewCustomerDiscountInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewCustomerDiscountInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetNewCustomerDiscountInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewCustomerDiscountInfoRequest.Merge(dst, src)
}
func (m *GetNewCustomerDiscountInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetNewCustomerDiscountInfoRequest.Size(m)
}
func (m *GetNewCustomerDiscountInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewCustomerDiscountInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewCustomerDiscountInfoRequest proto.InternalMessageInfo

func (m *GetNewCustomerDiscountInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetNewCustomerDiscountInfoRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetNewCustomerDiscountInfoResponse struct {
	ShowSwitch           bool     `protobuf:"varint,1,opt,name=show_switch,json=showSwitch,proto3" json:"show_switch,omitempty"`
	NewCustomerPrice     uint32   `protobuf:"varint,2,opt,name=new_customer_price,json=newCustomerPrice,proto3" json:"new_customer_price,omitempty"`
	PlatBonusFee         uint32   `protobuf:"varint,3,opt,name=plat_bonus_fee,json=platBonusFee,proto3" json:"plat_bonus_fee,omitempty"`
	IsOpen               bool     `protobuf:"varint,4,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewCustomerDiscountInfoResponse) Reset()         { *m = GetNewCustomerDiscountInfoResponse{} }
func (m *GetNewCustomerDiscountInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewCustomerDiscountInfoResponse) ProtoMessage()    {}
func (*GetNewCustomerDiscountInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{103}
}
func (m *GetNewCustomerDiscountInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewCustomerDiscountInfoResponse.Unmarshal(m, b)
}
func (m *GetNewCustomerDiscountInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewCustomerDiscountInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetNewCustomerDiscountInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewCustomerDiscountInfoResponse.Merge(dst, src)
}
func (m *GetNewCustomerDiscountInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewCustomerDiscountInfoResponse.Size(m)
}
func (m *GetNewCustomerDiscountInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewCustomerDiscountInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewCustomerDiscountInfoResponse proto.InternalMessageInfo

func (m *GetNewCustomerDiscountInfoResponse) GetShowSwitch() bool {
	if m != nil {
		return m.ShowSwitch
	}
	return false
}

func (m *GetNewCustomerDiscountInfoResponse) GetNewCustomerPrice() uint32 {
	if m != nil {
		return m.NewCustomerPrice
	}
	return 0
}

func (m *GetNewCustomerDiscountInfoResponse) GetPlatBonusFee() uint32 {
	if m != nil {
		return m.PlatBonusFee
	}
	return 0
}

func (m *GetNewCustomerDiscountInfoResponse) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type ClearNewCustomerOrderRequest struct {
	PlayerUid            uint32   `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearNewCustomerOrderRequest) Reset()         { *m = ClearNewCustomerOrderRequest{} }
func (m *ClearNewCustomerOrderRequest) String() string { return proto.CompactTextString(m) }
func (*ClearNewCustomerOrderRequest) ProtoMessage()    {}
func (*ClearNewCustomerOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{104}
}
func (m *ClearNewCustomerOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearNewCustomerOrderRequest.Unmarshal(m, b)
}
func (m *ClearNewCustomerOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearNewCustomerOrderRequest.Marshal(b, m, deterministic)
}
func (dst *ClearNewCustomerOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearNewCustomerOrderRequest.Merge(dst, src)
}
func (m *ClearNewCustomerOrderRequest) XXX_Size() int {
	return xxx_messageInfo_ClearNewCustomerOrderRequest.Size(m)
}
func (m *ClearNewCustomerOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearNewCustomerOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ClearNewCustomerOrderRequest proto.InternalMessageInfo

func (m *ClearNewCustomerOrderRequest) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

type ClearNewCustomerOrderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearNewCustomerOrderResponse) Reset()         { *m = ClearNewCustomerOrderResponse{} }
func (m *ClearNewCustomerOrderResponse) String() string { return proto.CompactTextString(m) }
func (*ClearNewCustomerOrderResponse) ProtoMessage()    {}
func (*ClearNewCustomerOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{105}
}
func (m *ClearNewCustomerOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearNewCustomerOrderResponse.Unmarshal(m, b)
}
func (m *ClearNewCustomerOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearNewCustomerOrderResponse.Marshal(b, m, deterministic)
}
func (dst *ClearNewCustomerOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearNewCustomerOrderResponse.Merge(dst, src)
}
func (m *ClearNewCustomerOrderResponse) XXX_Size() int {
	return xxx_messageInfo_ClearNewCustomerOrderResponse.Size(m)
}
func (m *ClearNewCustomerOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearNewCustomerOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ClearNewCustomerOrderResponse proto.InternalMessageInfo

type NewCustomerPriceInfo struct {
	PlayerUid              uint32   `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CoachUid               uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId                 uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	HasNewCustomerDiscount bool     `protobuf:"varint,4,opt,name=has_new_customer_discount,json=hasNewCustomerDiscount,proto3" json:"has_new_customer_discount,omitempty"`
	NewCustomerPrice       uint32   `protobuf:"varint,5,opt,name=new_customer_price,json=newCustomerPrice,proto3" json:"new_customer_price,omitempty"`
	PlatBonusFee           uint32   `protobuf:"varint,6,opt,name=plat_bonus_fee,json=platBonusFee,proto3" json:"plat_bonus_fee,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *NewCustomerPriceInfo) Reset()         { *m = NewCustomerPriceInfo{} }
func (m *NewCustomerPriceInfo) String() string { return proto.CompactTextString(m) }
func (*NewCustomerPriceInfo) ProtoMessage()    {}
func (*NewCustomerPriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{106}
}
func (m *NewCustomerPriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewCustomerPriceInfo.Unmarshal(m, b)
}
func (m *NewCustomerPriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewCustomerPriceInfo.Marshal(b, m, deterministic)
}
func (dst *NewCustomerPriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewCustomerPriceInfo.Merge(dst, src)
}
func (m *NewCustomerPriceInfo) XXX_Size() int {
	return xxx_messageInfo_NewCustomerPriceInfo.Size(m)
}
func (m *NewCustomerPriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NewCustomerPriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NewCustomerPriceInfo proto.InternalMessageInfo

func (m *NewCustomerPriceInfo) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *NewCustomerPriceInfo) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *NewCustomerPriceInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *NewCustomerPriceInfo) GetHasNewCustomerDiscount() bool {
	if m != nil {
		return m.HasNewCustomerDiscount
	}
	return false
}

func (m *NewCustomerPriceInfo) GetNewCustomerPrice() uint32 {
	if m != nil {
		return m.NewCustomerPrice
	}
	return 0
}

func (m *NewCustomerPriceInfo) GetPlatBonusFee() uint32 {
	if m != nil {
		return m.PlatBonusFee
	}
	return 0
}

type GetNewCustomerPriceByUidRequest struct {
	PlayerUid            uint32   `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CoachUidList         []uint32 `protobuf:"varint,2,rep,packed,name=coach_uid_list,json=coachUidList,proto3" json:"coach_uid_list,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewCustomerPriceByUidRequest) Reset()         { *m = GetNewCustomerPriceByUidRequest{} }
func (m *GetNewCustomerPriceByUidRequest) String() string { return proto.CompactTextString(m) }
func (*GetNewCustomerPriceByUidRequest) ProtoMessage()    {}
func (*GetNewCustomerPriceByUidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{107}
}
func (m *GetNewCustomerPriceByUidRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewCustomerPriceByUidRequest.Unmarshal(m, b)
}
func (m *GetNewCustomerPriceByUidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewCustomerPriceByUidRequest.Marshal(b, m, deterministic)
}
func (dst *GetNewCustomerPriceByUidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewCustomerPriceByUidRequest.Merge(dst, src)
}
func (m *GetNewCustomerPriceByUidRequest) XXX_Size() int {
	return xxx_messageInfo_GetNewCustomerPriceByUidRequest.Size(m)
}
func (m *GetNewCustomerPriceByUidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewCustomerPriceByUidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewCustomerPriceByUidRequest proto.InternalMessageInfo

func (m *GetNewCustomerPriceByUidRequest) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *GetNewCustomerPriceByUidRequest) GetCoachUidList() []uint32 {
	if m != nil {
		return m.CoachUidList
	}
	return nil
}

func (m *GetNewCustomerPriceByUidRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetNewCustomerPriceByUidResponse struct {
	PriceMap             map[uint32]*NewCustomerPriceInfo `protobuf:"bytes,1,rep,name=price_map,json=priceMap,proto3" json:"price_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetNewCustomerPriceByUidResponse) Reset()         { *m = GetNewCustomerPriceByUidResponse{} }
func (m *GetNewCustomerPriceByUidResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewCustomerPriceByUidResponse) ProtoMessage()    {}
func (*GetNewCustomerPriceByUidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{108}
}
func (m *GetNewCustomerPriceByUidResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewCustomerPriceByUidResponse.Unmarshal(m, b)
}
func (m *GetNewCustomerPriceByUidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewCustomerPriceByUidResponse.Marshal(b, m, deterministic)
}
func (dst *GetNewCustomerPriceByUidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewCustomerPriceByUidResponse.Merge(dst, src)
}
func (m *GetNewCustomerPriceByUidResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewCustomerPriceByUidResponse.Size(m)
}
func (m *GetNewCustomerPriceByUidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewCustomerPriceByUidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewCustomerPriceByUidResponse proto.InternalMessageInfo

func (m *GetNewCustomerPriceByUidResponse) GetPriceMap() map[uint32]*NewCustomerPriceInfo {
	if m != nil {
		return m.PriceMap
	}
	return nil
}

type GetNewCustomerPriceBySkillRequest struct {
	PlayerUid            uint32   `protobuf:"varint,1,opt,name=player_uid,json=playerUid,proto3" json:"player_uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewCustomerPriceBySkillRequest) Reset()         { *m = GetNewCustomerPriceBySkillRequest{} }
func (m *GetNewCustomerPriceBySkillRequest) String() string { return proto.CompactTextString(m) }
func (*GetNewCustomerPriceBySkillRequest) ProtoMessage()    {}
func (*GetNewCustomerPriceBySkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{109}
}
func (m *GetNewCustomerPriceBySkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewCustomerPriceBySkillRequest.Unmarshal(m, b)
}
func (m *GetNewCustomerPriceBySkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewCustomerPriceBySkillRequest.Marshal(b, m, deterministic)
}
func (dst *GetNewCustomerPriceBySkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewCustomerPriceBySkillRequest.Merge(dst, src)
}
func (m *GetNewCustomerPriceBySkillRequest) XXX_Size() int {
	return xxx_messageInfo_GetNewCustomerPriceBySkillRequest.Size(m)
}
func (m *GetNewCustomerPriceBySkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewCustomerPriceBySkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewCustomerPriceBySkillRequest proto.InternalMessageInfo

func (m *GetNewCustomerPriceBySkillRequest) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *GetNewCustomerPriceBySkillRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type GetNewCustomerPriceBySkillResponse struct {
	PriceMap             map[uint32]*NewCustomerPriceInfo `protobuf:"bytes,1,rep,name=price_map,json=priceMap,proto3" json:"price_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetNewCustomerPriceBySkillResponse) Reset()         { *m = GetNewCustomerPriceBySkillResponse{} }
func (m *GetNewCustomerPriceBySkillResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewCustomerPriceBySkillResponse) ProtoMessage()    {}
func (*GetNewCustomerPriceBySkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{110}
}
func (m *GetNewCustomerPriceBySkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewCustomerPriceBySkillResponse.Unmarshal(m, b)
}
func (m *GetNewCustomerPriceBySkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewCustomerPriceBySkillResponse.Marshal(b, m, deterministic)
}
func (dst *GetNewCustomerPriceBySkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewCustomerPriceBySkillResponse.Merge(dst, src)
}
func (m *GetNewCustomerPriceBySkillResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewCustomerPriceBySkillResponse.Size(m)
}
func (m *GetNewCustomerPriceBySkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewCustomerPriceBySkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewCustomerPriceBySkillResponse proto.InternalMessageInfo

func (m *GetNewCustomerPriceBySkillResponse) GetPriceMap() map[uint32]*NewCustomerPriceInfo {
	if m != nil {
		return m.PriceMap
	}
	return nil
}

// 获取推荐技能
type GetRecommendSkillProductReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ReqNum               uint32   `protobuf:"varint,2,opt,name=req_num,json=reqNum,proto3" json:"req_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendSkillProductReq) Reset()         { *m = GetRecommendSkillProductReq{} }
func (m *GetRecommendSkillProductReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendSkillProductReq) ProtoMessage()    {}
func (*GetRecommendSkillProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{111}
}
func (m *GetRecommendSkillProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendSkillProductReq.Unmarshal(m, b)
}
func (m *GetRecommendSkillProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendSkillProductReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendSkillProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendSkillProductReq.Merge(dst, src)
}
func (m *GetRecommendSkillProductReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendSkillProductReq.Size(m)
}
func (m *GetRecommendSkillProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendSkillProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendSkillProductReq proto.InternalMessageInfo

func (m *GetRecommendSkillProductReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetRecommendSkillProductReq) GetReqNum() uint32 {
	if m != nil {
		return m.ReqNum
	}
	return 0
}

type GetRecommendSkillProductResp struct {
	SkillProductList     []*SkillProduct `protobuf:"bytes,1,rep,name=skill_product_list,json=skillProductList,proto3" json:"skill_product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRecommendSkillProductResp) Reset()         { *m = GetRecommendSkillProductResp{} }
func (m *GetRecommendSkillProductResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendSkillProductResp) ProtoMessage()    {}
func (*GetRecommendSkillProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{112}
}
func (m *GetRecommendSkillProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendSkillProductResp.Unmarshal(m, b)
}
func (m *GetRecommendSkillProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendSkillProductResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendSkillProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendSkillProductResp.Merge(dst, src)
}
func (m *GetRecommendSkillProductResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendSkillProductResp.Size(m)
}
func (m *GetRecommendSkillProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendSkillProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendSkillProductResp proto.InternalMessageInfo

func (m *GetRecommendSkillProductResp) GetSkillProductList() []*SkillProduct {
	if m != nil {
		return m.SkillProductList
	}
	return nil
}

type SetUserToRealCoachRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CustomerUid          uint32   `protobuf:"varint,2,opt,name=customer_uid,json=customerUid,proto3" json:"customer_uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,3,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId               uint32   `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserToRealCoachRequest) Reset()         { *m = SetUserToRealCoachRequest{} }
func (m *SetUserToRealCoachRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserToRealCoachRequest) ProtoMessage()    {}
func (*SetUserToRealCoachRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{113}
}
func (m *SetUserToRealCoachRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserToRealCoachRequest.Unmarshal(m, b)
}
func (m *SetUserToRealCoachRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserToRealCoachRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserToRealCoachRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserToRealCoachRequest.Merge(dst, src)
}
func (m *SetUserToRealCoachRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserToRealCoachRequest.Size(m)
}
func (m *SetUserToRealCoachRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserToRealCoachRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserToRealCoachRequest proto.InternalMessageInfo

func (m *SetUserToRealCoachRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserToRealCoachRequest) GetCustomerUid() uint32 {
	if m != nil {
		return m.CustomerUid
	}
	return 0
}

func (m *SetUserToRealCoachRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *SetUserToRealCoachRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type SetUserToRealCoachResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserToRealCoachResponse) Reset()         { *m = SetUserToRealCoachResponse{} }
func (m *SetUserToRealCoachResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserToRealCoachResponse) ProtoMessage()    {}
func (*SetUserToRealCoachResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{114}
}
func (m *SetUserToRealCoachResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserToRealCoachResponse.Unmarshal(m, b)
}
func (m *SetUserToRealCoachResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserToRealCoachResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserToRealCoachResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserToRealCoachResponse.Merge(dst, src)
}
func (m *SetUserToRealCoachResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserToRealCoachResponse.Size(m)
}
func (m *SetUserToRealCoachResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserToRealCoachResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserToRealCoachResponse proto.InternalMessageInfo

type SearchCoachRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	CoachUidList         []uint32 `protobuf:"varint,2,rep,packed,name=coach_uid_list,json=coachUidList,proto3" json:"coach_uid_list,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchCoachRequest) Reset()         { *m = SearchCoachRequest{} }
func (m *SearchCoachRequest) String() string { return proto.CompactTextString(m) }
func (*SearchCoachRequest) ProtoMessage()    {}
func (*SearchCoachRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{115}
}
func (m *SearchCoachRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchCoachRequest.Unmarshal(m, b)
}
func (m *SearchCoachRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchCoachRequest.Marshal(b, m, deterministic)
}
func (dst *SearchCoachRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchCoachRequest.Merge(dst, src)
}
func (m *SearchCoachRequest) XXX_Size() int {
	return xxx_messageInfo_SearchCoachRequest.Size(m)
}
func (m *SearchCoachRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchCoachRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchCoachRequest proto.InternalMessageInfo

func (m *SearchCoachRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SearchCoachRequest) GetCoachUidList() []uint32 {
	if m != nil {
		return m.CoachUidList
	}
	return nil
}

func (m *SearchCoachRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchCoachRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type SearchCoachResponse struct {
	CoachList            []*GameCoachInfo `protobuf:"bytes,1,rep,name=coach_list,json=coachList,proto3" json:"coach_list,omitempty"`
	NextOffset           uint32           `protobuf:"varint,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SearchCoachResponse) Reset()         { *m = SearchCoachResponse{} }
func (m *SearchCoachResponse) String() string { return proto.CompactTextString(m) }
func (*SearchCoachResponse) ProtoMessage()    {}
func (*SearchCoachResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{116}
}
func (m *SearchCoachResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchCoachResponse.Unmarshal(m, b)
}
func (m *SearchCoachResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchCoachResponse.Marshal(b, m, deterministic)
}
func (dst *SearchCoachResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchCoachResponse.Merge(dst, src)
}
func (m *SearchCoachResponse) XXX_Size() int {
	return xxx_messageInfo_SearchCoachResponse.Size(m)
}
func (m *SearchCoachResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchCoachResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchCoachResponse proto.InternalMessageInfo

func (m *SearchCoachResponse) GetCoachList() []*GameCoachInfo {
	if m != nil {
		return m.CoachList
	}
	return nil
}

func (m *SearchCoachResponse) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

// 更新不再推荐状态请求消息
type AddIgnoreRecommendCoachRequest struct {
	Uid                  uint32                      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CoachId              uint32                      `protobuf:"varint,2,opt,name=coach_id,json=coachId,proto3" json:"coach_id,omitempty"`
	Cid                  uint32                      `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	Operation            IgnoreReCoachOnUGCOperation `protobuf:"varint,4,opt,name=operation,proto3,enum=esport_hall.IgnoreReCoachOnUGCOperation" json:"operation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *AddIgnoreRecommendCoachRequest) Reset()         { *m = AddIgnoreRecommendCoachRequest{} }
func (m *AddIgnoreRecommendCoachRequest) String() string { return proto.CompactTextString(m) }
func (*AddIgnoreRecommendCoachRequest) ProtoMessage()    {}
func (*AddIgnoreRecommendCoachRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{117}
}
func (m *AddIgnoreRecommendCoachRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddIgnoreRecommendCoachRequest.Unmarshal(m, b)
}
func (m *AddIgnoreRecommendCoachRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddIgnoreRecommendCoachRequest.Marshal(b, m, deterministic)
}
func (dst *AddIgnoreRecommendCoachRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddIgnoreRecommendCoachRequest.Merge(dst, src)
}
func (m *AddIgnoreRecommendCoachRequest) XXX_Size() int {
	return xxx_messageInfo_AddIgnoreRecommendCoachRequest.Size(m)
}
func (m *AddIgnoreRecommendCoachRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddIgnoreRecommendCoachRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddIgnoreRecommendCoachRequest proto.InternalMessageInfo

func (m *AddIgnoreRecommendCoachRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddIgnoreRecommendCoachRequest) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *AddIgnoreRecommendCoachRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *AddIgnoreRecommendCoachRequest) GetOperation() IgnoreReCoachOnUGCOperation {
	if m != nil {
		return m.Operation
	}
	return IgnoreReCoachOnUGCOperation_IGNORE_RE_COACH_ON_UGC_OPERATION_UNSPECIFIED
}

// 更新不再推荐状态响应消息
type AddIgnoreRecommendCoachResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddIgnoreRecommendCoachResponse) Reset()         { *m = AddIgnoreRecommendCoachResponse{} }
func (m *AddIgnoreRecommendCoachResponse) String() string { return proto.CompactTextString(m) }
func (*AddIgnoreRecommendCoachResponse) ProtoMessage()    {}
func (*AddIgnoreRecommendCoachResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{118}
}
func (m *AddIgnoreRecommendCoachResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddIgnoreRecommendCoachResponse.Unmarshal(m, b)
}
func (m *AddIgnoreRecommendCoachResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddIgnoreRecommendCoachResponse.Marshal(b, m, deterministic)
}
func (dst *AddIgnoreRecommendCoachResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddIgnoreRecommendCoachResponse.Merge(dst, src)
}
func (m *AddIgnoreRecommendCoachResponse) XXX_Size() int {
	return xxx_messageInfo_AddIgnoreRecommendCoachResponse.Size(m)
}
func (m *AddIgnoreRecommendCoachResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddIgnoreRecommendCoachResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddIgnoreRecommendCoachResponse proto.InternalMessageInfo

type GetReCoachForUGCRequest struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32          `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Cid                  uint32          `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	PropertyList         []*GameProperty `protobuf:"bytes,4,rep,name=property_list,json=propertyList,proto3" json:"property_list,omitempty"`
	Limit                uint32          `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetReCoachForUGCRequest) Reset()         { *m = GetReCoachForUGCRequest{} }
func (m *GetReCoachForUGCRequest) String() string { return proto.CompactTextString(m) }
func (*GetReCoachForUGCRequest) ProtoMessage()    {}
func (*GetReCoachForUGCRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{119}
}
func (m *GetReCoachForUGCRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReCoachForUGCRequest.Unmarshal(m, b)
}
func (m *GetReCoachForUGCRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReCoachForUGCRequest.Marshal(b, m, deterministic)
}
func (dst *GetReCoachForUGCRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReCoachForUGCRequest.Merge(dst, src)
}
func (m *GetReCoachForUGCRequest) XXX_Size() int {
	return xxx_messageInfo_GetReCoachForUGCRequest.Size(m)
}
func (m *GetReCoachForUGCRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReCoachForUGCRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetReCoachForUGCRequest proto.InternalMessageInfo

func (m *GetReCoachForUGCRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetReCoachForUGCRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetReCoachForUGCRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetReCoachForUGCRequest) GetPropertyList() []*GameProperty {
	if m != nil {
		return m.PropertyList
	}
	return nil
}

func (m *GetReCoachForUGCRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetReCoachForUGCResponse struct {
	CoachList            []*GameCoachInfo `protobuf:"bytes,1,rep,name=coach_list,json=coachList,proto3" json:"coach_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetReCoachForUGCResponse) Reset()         { *m = GetReCoachForUGCResponse{} }
func (m *GetReCoachForUGCResponse) String() string { return proto.CompactTextString(m) }
func (*GetReCoachForUGCResponse) ProtoMessage()    {}
func (*GetReCoachForUGCResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{120}
}
func (m *GetReCoachForUGCResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReCoachForUGCResponse.Unmarshal(m, b)
}
func (m *GetReCoachForUGCResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReCoachForUGCResponse.Marshal(b, m, deterministic)
}
func (dst *GetReCoachForUGCResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReCoachForUGCResponse.Merge(dst, src)
}
func (m *GetReCoachForUGCResponse) XXX_Size() int {
	return xxx_messageInfo_GetReCoachForUGCResponse.Size(m)
}
func (m *GetReCoachForUGCResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReCoachForUGCResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetReCoachForUGCResponse proto.InternalMessageInfo

func (m *GetReCoachForUGCResponse) GetCoachList() []*GameCoachInfo {
	if m != nil {
		return m.CoachList
	}
	return nil
}

type GetCoachMinPriceRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IncludeFirstRound    bool     `protobuf:"varint,2,opt,name=include_first_round,json=includeFirstRound,proto3" json:"include_first_round,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachMinPriceRequest) Reset()         { *m = GetCoachMinPriceRequest{} }
func (m *GetCoachMinPriceRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachMinPriceRequest) ProtoMessage()    {}
func (*GetCoachMinPriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{121}
}
func (m *GetCoachMinPriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachMinPriceRequest.Unmarshal(m, b)
}
func (m *GetCoachMinPriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachMinPriceRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachMinPriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachMinPriceRequest.Merge(dst, src)
}
func (m *GetCoachMinPriceRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachMinPriceRequest.Size(m)
}
func (m *GetCoachMinPriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachMinPriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachMinPriceRequest proto.InternalMessageInfo

func (m *GetCoachMinPriceRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCoachMinPriceRequest) GetIncludeFirstRound() bool {
	if m != nil {
		return m.IncludeFirstRound
	}
	return false
}

type GetCoachMinPriceResponse struct {
	MinPrice             uint32   `protobuf:"varint,1,opt,name=min_price,json=minPrice,proto3" json:"min_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachMinPriceResponse) Reset()         { *m = GetCoachMinPriceResponse{} }
func (m *GetCoachMinPriceResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachMinPriceResponse) ProtoMessage()    {}
func (*GetCoachMinPriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{122}
}
func (m *GetCoachMinPriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachMinPriceResponse.Unmarshal(m, b)
}
func (m *GetCoachMinPriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachMinPriceResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachMinPriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachMinPriceResponse.Merge(dst, src)
}
func (m *GetCoachMinPriceResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachMinPriceResponse.Size(m)
}
func (m *GetCoachMinPriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachMinPriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachMinPriceResponse proto.InternalMessageInfo

func (m *GetCoachMinPriceResponse) GetMinPrice() uint32 {
	if m != nil {
		return m.MinPrice
	}
	return 0
}

type IncentiveTaskNode struct {
	TaskName             string   `protobuf:"bytes,1,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	Progress             string   `protobuf:"bytes,2,opt,name=progress,proto3" json:"progress,omitempty"`
	Target               string   `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
	Unit                 string   `protobuf:"bytes,4,opt,name=unit,proto3" json:"unit,omitempty"`
	IsReach              bool     `protobuf:"varint,5,opt,name=is_reach,json=isReach,proto3" json:"is_reach,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncentiveTaskNode) Reset()         { *m = IncentiveTaskNode{} }
func (m *IncentiveTaskNode) String() string { return proto.CompactTextString(m) }
func (*IncentiveTaskNode) ProtoMessage()    {}
func (*IncentiveTaskNode) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{123}
}
func (m *IncentiveTaskNode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncentiveTaskNode.Unmarshal(m, b)
}
func (m *IncentiveTaskNode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncentiveTaskNode.Marshal(b, m, deterministic)
}
func (dst *IncentiveTaskNode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncentiveTaskNode.Merge(dst, src)
}
func (m *IncentiveTaskNode) XXX_Size() int {
	return xxx_messageInfo_IncentiveTaskNode.Size(m)
}
func (m *IncentiveTaskNode) XXX_DiscardUnknown() {
	xxx_messageInfo_IncentiveTaskNode.DiscardUnknown(m)
}

var xxx_messageInfo_IncentiveTaskNode proto.InternalMessageInfo

func (m *IncentiveTaskNode) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *IncentiveTaskNode) GetProgress() string {
	if m != nil {
		return m.Progress
	}
	return ""
}

func (m *IncentiveTaskNode) GetTarget() string {
	if m != nil {
		return m.Target
	}
	return ""
}

func (m *IncentiveTaskNode) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

func (m *IncentiveTaskNode) GetIsReach() bool {
	if m != nil {
		return m.IsReach
	}
	return false
}

type IncentiveTask struct {
	Addition             uint32               `protobuf:"varint,1,opt,name=addition,proto3" json:"addition,omitempty"`
	IsReach              bool                 `protobuf:"varint,2,opt,name=is_reach,json=isReach,proto3" json:"is_reach,omitempty"`
	TaskNodeList         []*IncentiveTaskNode `protobuf:"bytes,3,rep,name=task_node_list,json=taskNodeList,proto3" json:"task_node_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *IncentiveTask) Reset()         { *m = IncentiveTask{} }
func (m *IncentiveTask) String() string { return proto.CompactTextString(m) }
func (*IncentiveTask) ProtoMessage()    {}
func (*IncentiveTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{124}
}
func (m *IncentiveTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncentiveTask.Unmarshal(m, b)
}
func (m *IncentiveTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncentiveTask.Marshal(b, m, deterministic)
}
func (dst *IncentiveTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncentiveTask.Merge(dst, src)
}
func (m *IncentiveTask) XXX_Size() int {
	return xxx_messageInfo_IncentiveTask.Size(m)
}
func (m *IncentiveTask) XXX_DiscardUnknown() {
	xxx_messageInfo_IncentiveTask.DiscardUnknown(m)
}

var xxx_messageInfo_IncentiveTask proto.InternalMessageInfo

func (m *IncentiveTask) GetAddition() uint32 {
	if m != nil {
		return m.Addition
	}
	return 0
}

func (m *IncentiveTask) GetIsReach() bool {
	if m != nil {
		return m.IsReach
	}
	return false
}

func (m *IncentiveTask) GetTaskNodeList() []*IncentiveTaskNode {
	if m != nil {
		return m.TaskNodeList
	}
	return nil
}

type GetCoachIncentiveTaskInfoRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachIncentiveTaskInfoRequest) Reset()         { *m = GetCoachIncentiveTaskInfoRequest{} }
func (m *GetCoachIncentiveTaskInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachIncentiveTaskInfoRequest) ProtoMessage()    {}
func (*GetCoachIncentiveTaskInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{125}
}
func (m *GetCoachIncentiveTaskInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoRequest.Unmarshal(m, b)
}
func (m *GetCoachIncentiveTaskInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachIncentiveTaskInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachIncentiveTaskInfoRequest.Merge(dst, src)
}
func (m *GetCoachIncentiveTaskInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoRequest.Size(m)
}
func (m *GetCoachIncentiveTaskInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachIncentiveTaskInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachIncentiveTaskInfoRequest proto.InternalMessageInfo

type GetCoachIncentiveTaskInfoResponse struct {
	LastViewTime         uint32           `protobuf:"varint,1,opt,name=last_view_time,json=lastViewTime,proto3" json:"last_view_time,omitempty"`
	TaskList             []*IncentiveTask `protobuf:"bytes,2,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetCoachIncentiveTaskInfoResponse) Reset()         { *m = GetCoachIncentiveTaskInfoResponse{} }
func (m *GetCoachIncentiveTaskInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachIncentiveTaskInfoResponse) ProtoMessage()    {}
func (*GetCoachIncentiveTaskInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{126}
}
func (m *GetCoachIncentiveTaskInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoResponse.Unmarshal(m, b)
}
func (m *GetCoachIncentiveTaskInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachIncentiveTaskInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachIncentiveTaskInfoResponse.Merge(dst, src)
}
func (m *GetCoachIncentiveTaskInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoResponse.Size(m)
}
func (m *GetCoachIncentiveTaskInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachIncentiveTaskInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachIncentiveTaskInfoResponse proto.InternalMessageInfo

func (m *GetCoachIncentiveTaskInfoResponse) GetLastViewTime() uint32 {
	if m != nil {
		return m.LastViewTime
	}
	return 0
}

func (m *GetCoachIncentiveTaskInfoResponse) GetTaskList() []*IncentiveTask {
	if m != nil {
		return m.TaskList
	}
	return nil
}

type ReBuildUserFirstRoundCacheRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReBuildUserFirstRoundCacheRequest) Reset()         { *m = ReBuildUserFirstRoundCacheRequest{} }
func (m *ReBuildUserFirstRoundCacheRequest) String() string { return proto.CompactTextString(m) }
func (*ReBuildUserFirstRoundCacheRequest) ProtoMessage()    {}
func (*ReBuildUserFirstRoundCacheRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{127}
}
func (m *ReBuildUserFirstRoundCacheRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReBuildUserFirstRoundCacheRequest.Unmarshal(m, b)
}
func (m *ReBuildUserFirstRoundCacheRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReBuildUserFirstRoundCacheRequest.Marshal(b, m, deterministic)
}
func (dst *ReBuildUserFirstRoundCacheRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReBuildUserFirstRoundCacheRequest.Merge(dst, src)
}
func (m *ReBuildUserFirstRoundCacheRequest) XXX_Size() int {
	return xxx_messageInfo_ReBuildUserFirstRoundCacheRequest.Size(m)
}
func (m *ReBuildUserFirstRoundCacheRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReBuildUserFirstRoundCacheRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReBuildUserFirstRoundCacheRequest proto.InternalMessageInfo

func (m *ReBuildUserFirstRoundCacheRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type ReBuildUserFirstRoundCacheResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReBuildUserFirstRoundCacheResponse) Reset()         { *m = ReBuildUserFirstRoundCacheResponse{} }
func (m *ReBuildUserFirstRoundCacheResponse) String() string { return proto.CompactTextString(m) }
func (*ReBuildUserFirstRoundCacheResponse) ProtoMessage()    {}
func (*ReBuildUserFirstRoundCacheResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{128}
}
func (m *ReBuildUserFirstRoundCacheResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReBuildUserFirstRoundCacheResponse.Unmarshal(m, b)
}
func (m *ReBuildUserFirstRoundCacheResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReBuildUserFirstRoundCacheResponse.Marshal(b, m, deterministic)
}
func (dst *ReBuildUserFirstRoundCacheResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReBuildUserFirstRoundCacheResponse.Merge(dst, src)
}
func (m *ReBuildUserFirstRoundCacheResponse) XXX_Size() int {
	return xxx_messageInfo_ReBuildUserFirstRoundCacheResponse.Size(m)
}
func (m *ReBuildUserFirstRoundCacheResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReBuildUserFirstRoundCacheResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReBuildUserFirstRoundCacheResponse proto.InternalMessageInfo

type GetGlobalRcmdCoachRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGlobalRcmdCoachRequest) Reset()         { *m = GetGlobalRcmdCoachRequest{} }
func (m *GetGlobalRcmdCoachRequest) String() string { return proto.CompactTextString(m) }
func (*GetGlobalRcmdCoachRequest) ProtoMessage()    {}
func (*GetGlobalRcmdCoachRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{129}
}
func (m *GetGlobalRcmdCoachRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGlobalRcmdCoachRequest.Unmarshal(m, b)
}
func (m *GetGlobalRcmdCoachRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGlobalRcmdCoachRequest.Marshal(b, m, deterministic)
}
func (dst *GetGlobalRcmdCoachRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGlobalRcmdCoachRequest.Merge(dst, src)
}
func (m *GetGlobalRcmdCoachRequest) XXX_Size() int {
	return xxx_messageInfo_GetGlobalRcmdCoachRequest.Size(m)
}
func (m *GetGlobalRcmdCoachRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGlobalRcmdCoachRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGlobalRcmdCoachRequest proto.InternalMessageInfo

func (m *GetGlobalRcmdCoachRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGlobalRcmdCoachRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetGlobalRcmdCoachResponse struct {
	CoachUid                 uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	EsportRcmdStrategyId     uint32   `protobuf:"varint,2,opt,name=esport_rcmd_strategy_id,json=esportRcmdStrategyId,proto3" json:"esport_rcmd_strategy_id,omitempty"`
	EsportRcmdRecallSourceId uint32   `protobuf:"varint,3,opt,name=esport_rcmd_recall_source_id,json=esportRcmdRecallSourceId,proto3" json:"esport_rcmd_recall_source_id,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *GetGlobalRcmdCoachResponse) Reset()         { *m = GetGlobalRcmdCoachResponse{} }
func (m *GetGlobalRcmdCoachResponse) String() string { return proto.CompactTextString(m) }
func (*GetGlobalRcmdCoachResponse) ProtoMessage()    {}
func (*GetGlobalRcmdCoachResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{130}
}
func (m *GetGlobalRcmdCoachResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGlobalRcmdCoachResponse.Unmarshal(m, b)
}
func (m *GetGlobalRcmdCoachResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGlobalRcmdCoachResponse.Marshal(b, m, deterministic)
}
func (dst *GetGlobalRcmdCoachResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGlobalRcmdCoachResponse.Merge(dst, src)
}
func (m *GetGlobalRcmdCoachResponse) XXX_Size() int {
	return xxx_messageInfo_GetGlobalRcmdCoachResponse.Size(m)
}
func (m *GetGlobalRcmdCoachResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGlobalRcmdCoachResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGlobalRcmdCoachResponse proto.InternalMessageInfo

func (m *GetGlobalRcmdCoachResponse) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetGlobalRcmdCoachResponse) GetEsportRcmdStrategyId() uint32 {
	if m != nil {
		return m.EsportRcmdStrategyId
	}
	return 0
}

func (m *GetGlobalRcmdCoachResponse) GetEsportRcmdRecallSourceId() uint32 {
	if m != nil {
		return m.EsportRcmdRecallSourceId
	}
	return 0
}

type GetOperationCoachRecommendWithoutExposedRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOperationCoachRecommendWithoutExposedRequest) Reset() {
	*m = GetOperationCoachRecommendWithoutExposedRequest{}
}
func (m *GetOperationCoachRecommendWithoutExposedRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetOperationCoachRecommendWithoutExposedRequest) ProtoMessage() {}
func (*GetOperationCoachRecommendWithoutExposedRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{131}
}
func (m *GetOperationCoachRecommendWithoutExposedRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOperationCoachRecommendWithoutExposedRequest.Unmarshal(m, b)
}
func (m *GetOperationCoachRecommendWithoutExposedRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOperationCoachRecommendWithoutExposedRequest.Marshal(b, m, deterministic)
}
func (dst *GetOperationCoachRecommendWithoutExposedRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperationCoachRecommendWithoutExposedRequest.Merge(dst, src)
}
func (m *GetOperationCoachRecommendWithoutExposedRequest) XXX_Size() int {
	return xxx_messageInfo_GetOperationCoachRecommendWithoutExposedRequest.Size(m)
}
func (m *GetOperationCoachRecommendWithoutExposedRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperationCoachRecommendWithoutExposedRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperationCoachRecommendWithoutExposedRequest proto.InternalMessageInfo

func (m *GetOperationCoachRecommendWithoutExposedRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetOperationCoachRecommendWithoutExposedRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetOperationCoachRecommendWithoutExposedResponse struct {
	ProductList          []*GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem `protobuf:"bytes,1,rep,name=product_list,json=productList,proto3" json:"product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                                              `json:"-"`
	XXX_unrecognized     []byte                                                                `json:"-"`
	XXX_sizecache        int32                                                                 `json:"-"`
}

func (m *GetOperationCoachRecommendWithoutExposedResponse) Reset() {
	*m = GetOperationCoachRecommendWithoutExposedResponse{}
}
func (m *GetOperationCoachRecommendWithoutExposedResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetOperationCoachRecommendWithoutExposedResponse) ProtoMessage() {}
func (*GetOperationCoachRecommendWithoutExposedResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{132}
}
func (m *GetOperationCoachRecommendWithoutExposedResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse.Unmarshal(m, b)
}
func (m *GetOperationCoachRecommendWithoutExposedResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse.Marshal(b, m, deterministic)
}
func (dst *GetOperationCoachRecommendWithoutExposedResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse.Merge(dst, src)
}
func (m *GetOperationCoachRecommendWithoutExposedResponse) XXX_Size() int {
	return xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse.Size(m)
}
func (m *GetOperationCoachRecommendWithoutExposedResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse proto.InternalMessageInfo

func (m *GetOperationCoachRecommendWithoutExposedResponse) GetProductList() []*GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem {
	if m != nil {
		return m.ProductList
	}
	return nil
}

type GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem struct {
	ProductId            uint32   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) Reset() {
	*m = GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem{}
}
func (m *GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) String() string {
	return proto.CompactTextString(m)
}
func (*GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) ProtoMessage() {}
func (*GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{132, 0}
}
func (m *GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem.Unmarshal(m, b)
}
func (m *GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem.Marshal(b, m, deterministic)
}
func (dst *GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem.Merge(dst, src)
}
func (m *GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) XXX_Size() int {
	return xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem.Size(m)
}
func (m *GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem proto.InternalMessageInfo

func (m *GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetCoachIncentiveAdditionRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachIncentiveAdditionRequest) Reset()         { *m = GetCoachIncentiveAdditionRequest{} }
func (m *GetCoachIncentiveAdditionRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachIncentiveAdditionRequest) ProtoMessage()    {}
func (*GetCoachIncentiveAdditionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{133}
}
func (m *GetCoachIncentiveAdditionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachIncentiveAdditionRequest.Unmarshal(m, b)
}
func (m *GetCoachIncentiveAdditionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachIncentiveAdditionRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachIncentiveAdditionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachIncentiveAdditionRequest.Merge(dst, src)
}
func (m *GetCoachIncentiveAdditionRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachIncentiveAdditionRequest.Size(m)
}
func (m *GetCoachIncentiveAdditionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachIncentiveAdditionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachIncentiveAdditionRequest proto.InternalMessageInfo

func (m *GetCoachIncentiveAdditionRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetCoachIncentiveAdditionResponse struct {
	Addition             uint32   `protobuf:"varint,1,opt,name=addition,proto3" json:"addition,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachIncentiveAdditionResponse) Reset()         { *m = GetCoachIncentiveAdditionResponse{} }
func (m *GetCoachIncentiveAdditionResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachIncentiveAdditionResponse) ProtoMessage()    {}
func (*GetCoachIncentiveAdditionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{134}
}
func (m *GetCoachIncentiveAdditionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachIncentiveAdditionResponse.Unmarshal(m, b)
}
func (m *GetCoachIncentiveAdditionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachIncentiveAdditionResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachIncentiveAdditionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachIncentiveAdditionResponse.Merge(dst, src)
}
func (m *GetCoachIncentiveAdditionResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachIncentiveAdditionResponse.Size(m)
}
func (m *GetCoachIncentiveAdditionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachIncentiveAdditionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachIncentiveAdditionResponse proto.InternalMessageInfo

func (m *GetCoachIncentiveAdditionResponse) GetAddition() uint32 {
	if m != nil {
		return m.Addition
	}
	return 0
}

type SaveUserTopGamePreSelectConfigRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameIdList           []uint32 `protobuf:"varint,2,rep,packed,name=game_id_list,json=gameIdList,proto3" json:"game_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveUserTopGamePreSelectConfigRequest) Reset()         { *m = SaveUserTopGamePreSelectConfigRequest{} }
func (m *SaveUserTopGamePreSelectConfigRequest) String() string { return proto.CompactTextString(m) }
func (*SaveUserTopGamePreSelectConfigRequest) ProtoMessage()    {}
func (*SaveUserTopGamePreSelectConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{135}
}
func (m *SaveUserTopGamePreSelectConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveUserTopGamePreSelectConfigRequest.Unmarshal(m, b)
}
func (m *SaveUserTopGamePreSelectConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveUserTopGamePreSelectConfigRequest.Marshal(b, m, deterministic)
}
func (dst *SaveUserTopGamePreSelectConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveUserTopGamePreSelectConfigRequest.Merge(dst, src)
}
func (m *SaveUserTopGamePreSelectConfigRequest) XXX_Size() int {
	return xxx_messageInfo_SaveUserTopGamePreSelectConfigRequest.Size(m)
}
func (m *SaveUserTopGamePreSelectConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveUserTopGamePreSelectConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SaveUserTopGamePreSelectConfigRequest proto.InternalMessageInfo

func (m *SaveUserTopGamePreSelectConfigRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SaveUserTopGamePreSelectConfigRequest) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

type SaveUserTopGamePreSelectConfigResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SaveUserTopGamePreSelectConfigResponse) Reset() {
	*m = SaveUserTopGamePreSelectConfigResponse{}
}
func (m *SaveUserTopGamePreSelectConfigResponse) String() string { return proto.CompactTextString(m) }
func (*SaveUserTopGamePreSelectConfigResponse) ProtoMessage()    {}
func (*SaveUserTopGamePreSelectConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{136}
}
func (m *SaveUserTopGamePreSelectConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SaveUserTopGamePreSelectConfigResponse.Unmarshal(m, b)
}
func (m *SaveUserTopGamePreSelectConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SaveUserTopGamePreSelectConfigResponse.Marshal(b, m, deterministic)
}
func (dst *SaveUserTopGamePreSelectConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SaveUserTopGamePreSelectConfigResponse.Merge(dst, src)
}
func (m *SaveUserTopGamePreSelectConfigResponse) XXX_Size() int {
	return xxx_messageInfo_SaveUserTopGamePreSelectConfigResponse.Size(m)
}
func (m *SaveUserTopGamePreSelectConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SaveUserTopGamePreSelectConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SaveUserTopGamePreSelectConfigResponse proto.InternalMessageInfo

type GetUserTopGamePreSelectConfigRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTopGamePreSelectConfigRequest) Reset()         { *m = GetUserTopGamePreSelectConfigRequest{} }
func (m *GetUserTopGamePreSelectConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserTopGamePreSelectConfigRequest) ProtoMessage()    {}
func (*GetUserTopGamePreSelectConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{137}
}
func (m *GetUserTopGamePreSelectConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTopGamePreSelectConfigRequest.Unmarshal(m, b)
}
func (m *GetUserTopGamePreSelectConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTopGamePreSelectConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserTopGamePreSelectConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTopGamePreSelectConfigRequest.Merge(dst, src)
}
func (m *GetUserTopGamePreSelectConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserTopGamePreSelectConfigRequest.Size(m)
}
func (m *GetUserTopGamePreSelectConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTopGamePreSelectConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTopGamePreSelectConfigRequest proto.InternalMessageInfo

func (m *GetUserTopGamePreSelectConfigRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserTopGamePreSelectConfigResponse struct {
	GameIdList           []uint32 `protobuf:"varint,1,rep,packed,name=game_id_list,json=gameIdList,proto3" json:"game_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTopGamePreSelectConfigResponse) Reset()         { *m = GetUserTopGamePreSelectConfigResponse{} }
func (m *GetUserTopGamePreSelectConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserTopGamePreSelectConfigResponse) ProtoMessage()    {}
func (*GetUserTopGamePreSelectConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_hall_701db7d7416106bf, []int{138}
}
func (m *GetUserTopGamePreSelectConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTopGamePreSelectConfigResponse.Unmarshal(m, b)
}
func (m *GetUserTopGamePreSelectConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTopGamePreSelectConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserTopGamePreSelectConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTopGamePreSelectConfigResponse.Merge(dst, src)
}
func (m *GetUserTopGamePreSelectConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserTopGamePreSelectConfigResponse.Size(m)
}
func (m *GetUserTopGamePreSelectConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTopGamePreSelectConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTopGamePreSelectConfigResponse proto.InternalMessageInfo

func (m *GetUserTopGamePreSelectConfigResponse) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

func init() {
	proto.RegisterType((*InitUserSkillInfoRequest)(nil), "esport_hall.InitUserSkillInfoRequest")
	proto.RegisterType((*InitUserSkillInfoResponse)(nil), "esport_hall.InitUserSkillInfoResponse")
	proto.RegisterType((*GetVisibleSkillProductListRequest)(nil), "esport_hall.GetVisibleSkillProductListRequest")
	proto.RegisterType((*GetVisibleSkillProductListResponse)(nil), "esport_hall.GetVisibleSkillProductListResponse")
	proto.RegisterType((*GetAllSkillListRequest)(nil), "esport_hall.GetAllSkillListRequest")
	proto.RegisterType((*GetAllSkillListResponse)(nil), "esport_hall.GetAllSkillListResponse")
	proto.RegisterType((*SetSkillReceiveSwitchRequest)(nil), "esport_hall.SetSkillReceiveSwitchRequest")
	proto.RegisterType((*SetSkillReceiveSwitchResponse)(nil), "esport_hall.SetSkillReceiveSwitchResponse")
	proto.RegisterType((*GetReceiveTimeFrameRequest)(nil), "esport_hall.GetReceiveTimeFrameRequest")
	proto.RegisterType((*GetReceiveTimeFrameResponse)(nil), "esport_hall.GetReceiveTimeFrameResponse")
	proto.RegisterType((*SetReceiveTimeFrameRequest)(nil), "esport_hall.SetReceiveTimeFrameRequest")
	proto.RegisterType((*SetReceiveTimeFrameResponse)(nil), "esport_hall.SetReceiveTimeFrameResponse")
	proto.RegisterType((*SetSkillPriceRequest)(nil), "esport_hall.SetSkillPriceRequest")
	proto.RegisterType((*SetSkillPriceResponse)(nil), "esport_hall.SetSkillPriceResponse")
	proto.RegisterType((*GetSkillProductInfoRequest)(nil), "esport_hall.GetSkillProductInfoRequest")
	proto.RegisterType((*GetSkillProductInfoResponse)(nil), "esport_hall.GetSkillProductInfoResponse")
	proto.RegisterType((*GetSkillProductInfoByGameIdRequest)(nil), "esport_hall.GetSkillProductInfoByGameIdRequest")
	proto.RegisterType((*GetSkillProductInfoByGameIdResponse)(nil), "esport_hall.GetSkillProductInfoByGameIdResponse")
	proto.RegisterType((*GetGameCoachListRequest)(nil), "esport_hall.GetGameCoachListRequest")
	proto.RegisterType((*GetCoachListResponse)(nil), "esport_hall.GetCoachListResponse")
	proto.RegisterType((*BatchGetSkillProductInfoRequest)(nil), "esport_hall.BatchGetSkillProductInfoRequest")
	proto.RegisterType((*BatchGetSkillProductInfoResponse)(nil), "esport_hall.BatchGetSkillProductInfoResponse")
	proto.RegisterType((*GetGameCoachListByUidRequest)(nil), "esport_hall.GetGameCoachListByUidRequest")
	proto.RegisterType((*GetGameCoachListByUidResponse)(nil), "esport_hall.GetGameCoachListByUidResponse")
	proto.RegisterType((*ReportExposeCoachRequest)(nil), "esport_hall.ReportExposeCoachRequest")
	proto.RegisterType((*ReportExposeCoachResponse)(nil), "esport_hall.ReportExposeCoachResponse")
	proto.RegisterType((*InviteOrderRequest)(nil), "esport_hall.InviteOrderRequest")
	proto.RegisterType((*InviteOrderResponse)(nil), "esport_hall.InviteOrderResponse")
	proto.RegisterType((*HandleInviteOrderRequest)(nil), "esport_hall.HandleInviteOrderRequest")
	proto.RegisterType((*HandleInviteOrderResponse)(nil), "esport_hall.HandleInviteOrderResponse")
	proto.RegisterType((*DelSkillProductRequest)(nil), "esport_hall.DelSkillProductRequest")
	proto.RegisterType((*DelSkillProductResponse)(nil), "esport_hall.DelSkillProductResponse")
	proto.RegisterType((*TestRequest)(nil), "esport_hall.TestRequest")
	proto.RegisterType((*TestResponse)(nil), "esport_hall.TestResponse")
	proto.RegisterType((*GetGamePricePropertyRequest)(nil), "esport_hall.GetGamePricePropertyRequest")
	proto.RegisterType((*GetGamePricePropertyResponse)(nil), "esport_hall.GetGamePricePropertyResponse")
	proto.RegisterType((*GetSkillProductByUidGameIdRequest)(nil), "esport_hall.GetSkillProductByUidGameIdRequest")
	proto.RegisterType((*GetSkillProductByUidGameIdResponse)(nil), "esport_hall.GetSkillProductByUidGameIdResponse")
	proto.RegisterType((*AddCoachRecommendRequest)(nil), "esport_hall.AddCoachRecommendRequest")
	proto.RegisterType((*AddCoachRecommendResponse)(nil), "esport_hall.AddCoachRecommendResponse")
	proto.RegisterType((*GetCoachRecommendRequest)(nil), "esport_hall.GetCoachRecommendRequest")
	proto.RegisterType((*GetCoachRecommendResponse)(nil), "esport_hall.GetCoachRecommendResponse")
	proto.RegisterType((*CoachRecommendInfo)(nil), "esport_hall.CoachRecommendInfo")
	proto.RegisterType((*UpdateCoachRecommendRequest)(nil), "esport_hall.UpdateCoachRecommendRequest")
	proto.RegisterType((*UpdateCoachRecommendResponse)(nil), "esport_hall.UpdateCoachRecommendResponse")
	proto.RegisterType((*DelCoachRecommendRequest)(nil), "esport_hall.DelCoachRecommendRequest")
	proto.RegisterType((*DelCoachRecommendResponse)(nil), "esport_hall.DelCoachRecommendResponse")
	proto.RegisterType((*SkillProduct)(nil), "esport_hall.SkillProduct")
	proto.RegisterType((*SkillInfo)(nil), "esport_hall.SkillInfo")
	proto.RegisterType((*SectionInfo)(nil), "esport_hall.SectionInfo")
	proto.RegisterType((*GameCoachInfo)(nil), "esport_hall.GameCoachInfo")
	proto.RegisterType((*GameInfo)(nil), "esport_hall.GameInfo")
	proto.RegisterType((*PriceInfo)(nil), "esport_hall.PriceInfo")
	proto.RegisterType((*NewCustomerUseDetail)(nil), "esport_hall.NewCustomerUseDetail")
	proto.RegisterType((*GameProperty)(nil), "esport_hall.GameProperty")
	proto.RegisterType((*GamePropertyVal)(nil), "esport_hall.GamePropertyVal")
	proto.RegisterType((*SetQuickReceiveSwitchRequest)(nil), "esport_hall.SetQuickReceiveSwitchRequest")
	proto.RegisterType((*SetQuickReceiveSwitchResponse)(nil), "esport_hall.SetQuickReceiveSwitchResponse")
	proto.RegisterType((*GetQuickReceiveSwitchRequest)(nil), "esport_hall.GetQuickReceiveSwitchRequest")
	proto.RegisterType((*GetQuickReceiveSwitchResponse)(nil), "esport_hall.GetQuickReceiveSwitchResponse")
	proto.RegisterType((*BatchGetQuickReceiveSwitchRequest)(nil), "esport_hall.BatchGetQuickReceiveSwitchRequest")
	proto.RegisterType((*BatchGetQuickReceiveSwitchResponse)(nil), "esport_hall.BatchGetQuickReceiveSwitchResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "esport_hall.BatchGetQuickReceiveSwitchResponse.SwitchMapEntry")
	proto.RegisterType((*HasFamousPlayerRequest)(nil), "esport_hall.HasFamousPlayerRequest")
	proto.RegisterType((*HasFamousPlayerResponse)(nil), "esport_hall.HasFamousPlayerResponse")
	proto.RegisterType((*SetGuaranteeWinSwitchRequest)(nil), "esport_hall.SetGuaranteeWinSwitchRequest")
	proto.RegisterType((*SetGuaranteeWinSwitchResponse)(nil), "esport_hall.SetGuaranteeWinSwitchResponse")
	proto.RegisterType((*EsportGameCardInfo)(nil), "esport_hall.EsportGameCardInfo")
	proto.RegisterType((*EsportGameCardInfoItem)(nil), "esport_hall.EsportGameCardInfoItem")
	proto.RegisterType((*GetEsportGameCardConfigRequest)(nil), "esport_hall.GetEsportGameCardConfigRequest")
	proto.RegisterType((*GetEsportGameCardConfigResponse)(nil), "esport_hall.GetEsportGameCardConfigResponse")
	proto.RegisterType((*GetEsportGameCardInfoRequest)(nil), "esport_hall.GetEsportGameCardInfoRequest")
	proto.RegisterType((*GetEsportGameCardInfoResponse)(nil), "esport_hall.GetEsportGameCardInfoResponse")
	proto.RegisterType((*CreateEsportGameCardRequest)(nil), "esport_hall.CreateEsportGameCardRequest")
	proto.RegisterType((*CreateEsportGameCardResponse)(nil), "esport_hall.CreateEsportGameCardResponse")
	proto.RegisterType((*UpdateEsportGameCardRequest)(nil), "esport_hall.UpdateEsportGameCardRequest")
	proto.RegisterType((*UpdateEsportGameCardResponse)(nil), "esport_hall.UpdateEsportGameCardResponse")
	proto.RegisterType((*GetEsportGameCardListRequest)(nil), "esport_hall.GetEsportGameCardListRequest")
	proto.RegisterType((*GetEsportGameCardListResponse)(nil), "esport_hall.GetEsportGameCardListResponse")
	proto.RegisterType((*SendEsportGameCardRequest)(nil), "esport_hall.SendEsportGameCardRequest")
	proto.RegisterType((*SendEsportGameCardResponse)(nil), "esport_hall.SendEsportGameCardResponse")
	proto.RegisterType((*DeleteEsportGameCardRequest)(nil), "esport_hall.DeleteEsportGameCardRequest")
	proto.RegisterType((*DeleteEsportGameCardResponse)(nil), "esport_hall.DeleteEsportGameCardResponse")
	proto.RegisterType((*SetFirstRoundSwitchRequest)(nil), "esport_hall.SetFirstRoundSwitchRequest")
	proto.RegisterType((*SetFirstRoundSwitchResponse)(nil), "esport_hall.SetFirstRoundSwitchResponse")
	proto.RegisterType((*GetFirstRoundDiscountInfoRequest)(nil), "esport_hall.GetFirstRoundDiscountInfoRequest")
	proto.RegisterType((*GetFirstRoundDiscountInfoResponse)(nil), "esport_hall.GetFirstRoundDiscountInfoResponse")
	proto.RegisterType((*GetFirstRoundDiscountGameListRequest)(nil), "esport_hall.GetFirstRoundDiscountGameListRequest")
	proto.RegisterType((*FirstRoundDiscountGameInfo)(nil), "esport_hall.FirstRoundDiscountGameInfo")
	proto.RegisterType((*GetFirstRoundDiscountGameListResponse)(nil), "esport_hall.GetFirstRoundDiscountGameListResponse")
	proto.RegisterType((*CheckFirstRoundOrderRightRequest)(nil), "esport_hall.CheckFirstRoundOrderRightRequest")
	proto.RegisterType((*CheckFirstRoundOrderRightResponse)(nil), "esport_hall.CheckFirstRoundOrderRightResponse")
	proto.RegisterType((*GetFirstRoundOrderRightRequest)(nil), "esport_hall.GetFirstRoundOrderRightRequest")
	proto.RegisterType((*GetFirstRoundOrderRightResponse)(nil), "esport_hall.GetFirstRoundOrderRightResponse")
	proto.RegisterType((*ClearFirstRoundOrderRequest)(nil), "esport_hall.ClearFirstRoundOrderRequest")
	proto.RegisterType((*ClearFirstRoundOrderResponse)(nil), "esport_hall.ClearFirstRoundOrderResponse")
	proto.RegisterType((*GetFirstRoundLabelByUidRequest)(nil), "esport_hall.GetFirstRoundLabelByUidRequest")
	proto.RegisterType((*GetFirstRoundLabelByUidResponse)(nil), "esport_hall.GetFirstRoundLabelByUidResponse")
	proto.RegisterMapType((map[uint32]*FirstRoundLabel)(nil), "esport_hall.GetFirstRoundLabelByUidResponse.LabelMapEntry")
	proto.RegisterType((*GetFirstRoundLabelBySkillRequest)(nil), "esport_hall.GetFirstRoundLabelBySkillRequest")
	proto.RegisterType((*GetFirstRoundLabelBySkillResponse)(nil), "esport_hall.GetFirstRoundLabelBySkillResponse")
	proto.RegisterMapType((map[uint32]*FirstRoundLabel)(nil), "esport_hall.GetFirstRoundLabelBySkillResponse.LabelMapEntry")
	proto.RegisterType((*FirstRoundLabel)(nil), "esport_hall.FirstRoundLabel")
	proto.RegisterType((*SetNewCustomerSwitchRequest)(nil), "esport_hall.SetNewCustomerSwitchRequest")
	proto.RegisterType((*SetNewCustomerSwitchResponse)(nil), "esport_hall.SetNewCustomerSwitchResponse")
	proto.RegisterType((*GetNewCustomerDiscountInfoRequest)(nil), "esport_hall.GetNewCustomerDiscountInfoRequest")
	proto.RegisterType((*GetNewCustomerDiscountInfoResponse)(nil), "esport_hall.GetNewCustomerDiscountInfoResponse")
	proto.RegisterType((*ClearNewCustomerOrderRequest)(nil), "esport_hall.ClearNewCustomerOrderRequest")
	proto.RegisterType((*ClearNewCustomerOrderResponse)(nil), "esport_hall.ClearNewCustomerOrderResponse")
	proto.RegisterType((*NewCustomerPriceInfo)(nil), "esport_hall.NewCustomerPriceInfo")
	proto.RegisterType((*GetNewCustomerPriceByUidRequest)(nil), "esport_hall.GetNewCustomerPriceByUidRequest")
	proto.RegisterType((*GetNewCustomerPriceByUidResponse)(nil), "esport_hall.GetNewCustomerPriceByUidResponse")
	proto.RegisterMapType((map[uint32]*NewCustomerPriceInfo)(nil), "esport_hall.GetNewCustomerPriceByUidResponse.PriceMapEntry")
	proto.RegisterType((*GetNewCustomerPriceBySkillRequest)(nil), "esport_hall.GetNewCustomerPriceBySkillRequest")
	proto.RegisterType((*GetNewCustomerPriceBySkillResponse)(nil), "esport_hall.GetNewCustomerPriceBySkillResponse")
	proto.RegisterMapType((map[uint32]*NewCustomerPriceInfo)(nil), "esport_hall.GetNewCustomerPriceBySkillResponse.PriceMapEntry")
	proto.RegisterType((*GetRecommendSkillProductReq)(nil), "esport_hall.GetRecommendSkillProductReq")
	proto.RegisterType((*GetRecommendSkillProductResp)(nil), "esport_hall.GetRecommendSkillProductResp")
	proto.RegisterType((*SetUserToRealCoachRequest)(nil), "esport_hall.SetUserToRealCoachRequest")
	proto.RegisterType((*SetUserToRealCoachResponse)(nil), "esport_hall.SetUserToRealCoachResponse")
	proto.RegisterType((*SearchCoachRequest)(nil), "esport_hall.SearchCoachRequest")
	proto.RegisterType((*SearchCoachResponse)(nil), "esport_hall.SearchCoachResponse")
	proto.RegisterType((*AddIgnoreRecommendCoachRequest)(nil), "esport_hall.AddIgnoreRecommendCoachRequest")
	proto.RegisterType((*AddIgnoreRecommendCoachResponse)(nil), "esport_hall.AddIgnoreRecommendCoachResponse")
	proto.RegisterType((*GetReCoachForUGCRequest)(nil), "esport_hall.GetReCoachForUGCRequest")
	proto.RegisterType((*GetReCoachForUGCResponse)(nil), "esport_hall.GetReCoachForUGCResponse")
	proto.RegisterType((*GetCoachMinPriceRequest)(nil), "esport_hall.GetCoachMinPriceRequest")
	proto.RegisterType((*GetCoachMinPriceResponse)(nil), "esport_hall.GetCoachMinPriceResponse")
	proto.RegisterType((*IncentiveTaskNode)(nil), "esport_hall.IncentiveTaskNode")
	proto.RegisterType((*IncentiveTask)(nil), "esport_hall.IncentiveTask")
	proto.RegisterType((*GetCoachIncentiveTaskInfoRequest)(nil), "esport_hall.GetCoachIncentiveTaskInfoRequest")
	proto.RegisterType((*GetCoachIncentiveTaskInfoResponse)(nil), "esport_hall.GetCoachIncentiveTaskInfoResponse")
	proto.RegisterType((*ReBuildUserFirstRoundCacheRequest)(nil), "esport_hall.ReBuildUserFirstRoundCacheRequest")
	proto.RegisterType((*ReBuildUserFirstRoundCacheResponse)(nil), "esport_hall.ReBuildUserFirstRoundCacheResponse")
	proto.RegisterType((*GetGlobalRcmdCoachRequest)(nil), "esport_hall.GetGlobalRcmdCoachRequest")
	proto.RegisterType((*GetGlobalRcmdCoachResponse)(nil), "esport_hall.GetGlobalRcmdCoachResponse")
	proto.RegisterType((*GetOperationCoachRecommendWithoutExposedRequest)(nil), "esport_hall.GetOperationCoachRecommendWithoutExposedRequest")
	proto.RegisterType((*GetOperationCoachRecommendWithoutExposedResponse)(nil), "esport_hall.GetOperationCoachRecommendWithoutExposedResponse")
	proto.RegisterType((*GetOperationCoachRecommendWithoutExposedResponse_SimpleProductItem)(nil), "esport_hall.GetOperationCoachRecommendWithoutExposedResponse.SimpleProductItem")
	proto.RegisterType((*GetCoachIncentiveAdditionRequest)(nil), "esport_hall.GetCoachIncentiveAdditionRequest")
	proto.RegisterType((*GetCoachIncentiveAdditionResponse)(nil), "esport_hall.GetCoachIncentiveAdditionResponse")
	proto.RegisterType((*SaveUserTopGamePreSelectConfigRequest)(nil), "esport_hall.SaveUserTopGamePreSelectConfigRequest")
	proto.RegisterType((*SaveUserTopGamePreSelectConfigResponse)(nil), "esport_hall.SaveUserTopGamePreSelectConfigResponse")
	proto.RegisterType((*GetUserTopGamePreSelectConfigRequest)(nil), "esport_hall.GetUserTopGamePreSelectConfigRequest")
	proto.RegisterType((*GetUserTopGamePreSelectConfigResponse)(nil), "esport_hall.GetUserTopGamePreSelectConfigResponse")
	proto.RegisterEnum("esport_hall.RECOMMEND_TYPE", RECOMMEND_TYPE_name, RECOMMEND_TYPE_value)
	proto.RegisterEnum("esport_hall.IgnoreReCoachOnUGCOperation", IgnoreReCoachOnUGCOperation_name, IgnoreReCoachOnUGCOperation_value)
	proto.RegisterEnum("esport_hall.GetGameCoachListRequest_ReqSource", GetGameCoachListRequest_ReqSource_name, GetGameCoachListRequest_ReqSource_value)
	proto.RegisterEnum("esport_hall.GameProperty_PropertyType", GameProperty_PropertyType_name, GameProperty_PropertyType_value)
	proto.RegisterEnum("esport_hall.GameProperty_SelectType", GameProperty_SelectType_name, GameProperty_SelectType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EsportHallClient is the client API for EsportHall service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EsportHallClient interface {
	// 初始化用户技能信息
	InitUserSkillInfo(ctx context.Context, in *InitUserSkillInfoRequest, opts ...grpc.CallOption) (*InitUserSkillInfoResponse, error)
	// 获取当前可见技能商品
	GetVisibleSkillProductList(ctx context.Context, in *GetVisibleSkillProductListRequest, opts ...grpc.CallOption) (*GetVisibleSkillProductListResponse, error)
	// 获取所有技能
	GetAllSkillList(ctx context.Context, in *GetAllSkillListRequest, opts ...grpc.CallOption) (*GetAllSkillListResponse, error)
	// 获取运营推荐大神、排除已曝光大神
	GetOperationCoachRecommendWithoutExposed(ctx context.Context, in *GetOperationCoachRecommendWithoutExposedRequest, opts ...grpc.CallOption) (*GetOperationCoachRecommendWithoutExposedResponse, error)
	// 设置技能开关
	SetSkillReceiveSwitch(ctx context.Context, in *SetSkillReceiveSwitchRequest, opts ...grpc.CallOption) (*SetSkillReceiveSwitchResponse, error)
	// 获取接单时段信息
	GetReceiveTimeFrame(ctx context.Context, in *GetReceiveTimeFrameRequest, opts ...grpc.CallOption) (*GetReceiveTimeFrameResponse, error)
	// 设置接单时段
	SetReceiveTimeFrame(ctx context.Context, in *SetReceiveTimeFrameRequest, opts ...grpc.CallOption) (*SetReceiveTimeFrameResponse, error)
	// 设置技能价格
	SetSkillPrice(ctx context.Context, in *SetSkillPriceRequest, opts ...grpc.CallOption) (*SetSkillPriceResponse, error)
	// 设置首局优惠开关
	SetFirstRoundSwitch(ctx context.Context, in *SetFirstRoundSwitchRequest, opts ...grpc.CallOption) (*SetFirstRoundSwitchResponse, error)
	// 获取首局优惠信息
	GetFirstRoundDiscountInfo(ctx context.Context, in *GetFirstRoundDiscountInfoRequest, opts ...grpc.CallOption) (*GetFirstRoundDiscountInfoResponse, error)
	// 获取所有首局优惠游戏
	GetFirstRoundDiscountGameList(ctx context.Context, in *GetFirstRoundDiscountGameListRequest, opts ...grpc.CallOption) (*GetFirstRoundDiscountGameListResponse, error)
	// 检查首局优惠下单权限
	CheckFirstRoundOrderRight(ctx context.Context, in *CheckFirstRoundOrderRightRequest, opts ...grpc.CallOption) (*CheckFirstRoundOrderRightResponse, error)
	// 获取首局优惠下单权限
	GetFirstRoundOrderRight(ctx context.Context, in *GetFirstRoundOrderRightRequest, opts ...grpc.CallOption) (*GetFirstRoundOrderRightResponse, error)
	// 清理首局优惠订单，debug用
	ClearFirstRoundOrder(ctx context.Context, in *ClearFirstRoundOrderRequest, opts ...grpc.CallOption) (*ClearFirstRoundOrderResponse, error)
	// 获取首局优惠标签-通过uid
	GetFirstRoundLabelByUid(ctx context.Context, in *GetFirstRoundLabelByUidRequest, opts ...grpc.CallOption) (*GetFirstRoundLabelByUidResponse, error)
	// 获取首局优惠标签-通过skill
	GetFirstRoundLabelBySkill(ctx context.Context, in *GetFirstRoundLabelBySkillRequest, opts ...grpc.CallOption) (*GetFirstRoundLabelBySkillResponse, error)
	// 设置新客价开关
	SetNewCustomerSwitch(ctx context.Context, in *SetNewCustomerSwitchRequest, opts ...grpc.CallOption) (*SetNewCustomerSwitchResponse, error)
	// 获取新客价信息
	GetNewCustomerDiscountInfo(ctx context.Context, in *GetNewCustomerDiscountInfoRequest, opts ...grpc.CallOption) (*GetNewCustomerDiscountInfoResponse, error)
	// 清理新客价订单，debug用
	ClearNewCustomerOrder(ctx context.Context, in *ClearNewCustomerOrderRequest, opts ...grpc.CallOption) (*ClearNewCustomerOrderResponse, error)
	// 获取新客价-通过uid
	GetNewCustomerPriceByUid(ctx context.Context, in *GetNewCustomerPriceByUidRequest, opts ...grpc.CallOption) (*GetNewCustomerPriceByUidResponse, error)
	// 获取新客价-通过skill
	GetNewCustomerPriceBySkill(ctx context.Context, in *GetNewCustomerPriceBySkillRequest, opts ...grpc.CallOption) (*GetNewCustomerPriceBySkillResponse, error)
	// 获取技能商品信息
	GetSkillProductInfo(ctx context.Context, in *GetSkillProductInfoRequest, opts ...grpc.CallOption) (*GetSkillProductInfoResponse, error)
	// 通过gameId获取技能商品信息
	GetSkillProductInfoByGameId(ctx context.Context, in *GetSkillProductInfoByGameIdRequest, opts ...grpc.CallOption) (*GetSkillProductInfoByGameIdResponse, error)
	// 获取某款游戏的教练列表
	GetGameCoachList(ctx context.Context, in *GetGameCoachListRequest, opts ...grpc.CallOption) (*GetCoachListResponse, error)
	// 获取某款游戏的教练列表(通过uid过滤)
	GetGameCoachListByUid(ctx context.Context, in *GetGameCoachListByUidRequest, opts ...grpc.CallOption) (*GetGameCoachListByUidResponse, error)
	// 上报曝光大神
	ReportExposeCoach(ctx context.Context, in *ReportExposeCoachRequest, opts ...grpc.CallOption) (*ReportExposeCoachResponse, error)
	// 邀请下单
	InviteOrder(ctx context.Context, in *InviteOrderRequest, opts ...grpc.CallOption) (*InviteOrderResponse, error)
	// 处理邀请下单
	HandleInviteOrder(ctx context.Context, in *HandleInviteOrderRequest, opts ...grpc.CallOption) (*HandleInviteOrderResponse, error)
	// 删除技能商品
	DelSkillProduct(ctx context.Context, in *DelSkillProductRequest, opts ...grpc.CallOption) (*DelSkillProductResponse, error)
	// 批量获取技能商品信息
	BatchGetSkillProductInfo(ctx context.Context, in *BatchGetSkillProductInfoRequest, opts ...grpc.CallOption) (*BatchGetSkillProductInfoResponse, error)
	// 测试
	Test(ctx context.Context, in *TestRequest, opts ...grpc.CallOption) (*TestResponse, error)
	// 获取游戏价格属性
	GetGamePriceProperty(ctx context.Context, in *GetGamePricePropertyRequest, opts ...grpc.CallOption) (*GetGamePricePropertyResponse, error)
	// 获取某款游戏的技能商品
	GetSkillProductByUidGameId(ctx context.Context, in *GetSkillProductByUidGameIdRequest, opts ...grpc.CallOption) (*GetSkillProductByUidGameIdResponse, error)
	// 设置秒接单开关
	SetQuickReceiveSwitch(ctx context.Context, in *SetQuickReceiveSwitchRequest, opts ...grpc.CallOption) (*SetQuickReceiveSwitchResponse, error)
	// 获取秒接单开关状态
	GetQuickReceiveSwitch(ctx context.Context, in *GetQuickReceiveSwitchRequest, opts ...grpc.CallOption) (*GetQuickReceiveSwitchResponse, error)
	// 批量获取秒接单开关状态
	BatchGetQuickReceiveSwitch(ctx context.Context, in *BatchGetQuickReceiveSwitchRequest, opts ...grpc.CallOption) (*BatchGetQuickReceiveSwitchResponse, error)
	// 获取知名选手数量
	HasFamousPlayer(ctx context.Context, in *HasFamousPlayerRequest, opts ...grpc.CallOption) (*HasFamousPlayerResponse, error)
	// 查询游戏名片信息
	GetEsportGameCardInfo(ctx context.Context, in *GetEsportGameCardInfoRequest, opts ...grpc.CallOption) (*GetEsportGameCardInfoResponse, error)
	// 创建游戏名片
	CreateEsportGameCard(ctx context.Context, in *CreateEsportGameCardRequest, opts ...grpc.CallOption) (*CreateEsportGameCardResponse, error)
	// 更新游戏名片
	UpdateEsportGameCard(ctx context.Context, in *UpdateEsportGameCardRequest, opts ...grpc.CallOption) (*UpdateEsportGameCardResponse, error)
	// 获取游戏名片列表
	GetEsportGameCardList(ctx context.Context, in *GetEsportGameCardListRequest, opts ...grpc.CallOption) (*GetEsportGameCardListResponse, error)
	// 发送游戏名片
	SendEsportGameCard(ctx context.Context, in *SendEsportGameCardRequest, opts ...grpc.CallOption) (*SendEsportGameCardResponse, error)
	// 删除游戏名片
	DeleteEsportGameCard(ctx context.Context, in *DeleteEsportGameCardRequest, opts ...grpc.CallOption) (*DeleteEsportGameCardResponse, error)
	// 设置包赢开关状态
	SetGuaranteeWinSwitch(ctx context.Context, in *SetGuaranteeWinSwitchRequest, opts ...grpc.CallOption) (*SetGuaranteeWinSwitchResponse, error)
	// 王者tab大神卡
	GetRecommendSkillProduct(ctx context.Context, in *GetRecommendSkillProductReq, opts ...grpc.CallOption) (*GetRecommendSkillProductResp, error)
	// 获取全局弹窗推荐大神
	GetGlobalRcmdCoach(ctx context.Context, in *GetGlobalRcmdCoachRequest, opts ...grpc.CallOption) (*GetGlobalRcmdCoachResponse, error)
	// 设置用户->客服聊天对应的真实大神uid
	SetUserToRealCoach(ctx context.Context, in *SetUserToRealCoachRequest, opts ...grpc.CallOption) (*SetUserToRealCoachResponse, error)
	// 获取教练最低价格
	GetCoachMinPrice(ctx context.Context, in *GetCoachMinPriceRequest, opts ...grpc.CallOption) (*GetCoachMinPriceResponse, error)
	// 获取激励任务信息
	GetCoachIncentiveTaskInfo(ctx context.Context, in *GetCoachIncentiveTaskInfoRequest, opts ...grpc.CallOption) (*GetCoachIncentiveTaskInfoResponse, error)
	// =============================== 运营后台相关 ========================================
	AddCoachRecommend(ctx context.Context, in *AddCoachRecommendRequest, opts ...grpc.CallOption) (*AddCoachRecommendResponse, error)
	GetCoachRecommend(ctx context.Context, in *GetCoachRecommendRequest, opts ...grpc.CallOption) (*GetCoachRecommendResponse, error)
	UpdateCoachRecommend(ctx context.Context, in *UpdateCoachRecommendRequest, opts ...grpc.CallOption) (*UpdateCoachRecommendResponse, error)
	DelCoachRecommend(ctx context.Context, in *DelCoachRecommendRequest, opts ...grpc.CallOption) (*DelCoachRecommendResponse, error)
	// SearchCoach 搜索大神，使用推荐列表排序，如果不在推荐列表，则按照默认排序
	SearchCoach(ctx context.Context, in *SearchCoachRequest, opts ...grpc.CallOption) (*SearchCoachResponse, error)
	// =============================== UGC房特供接口 ==========================
	// 更新用户的推荐卡片状态
	AddIgnoreRecommendCoach(ctx context.Context, in *AddIgnoreRecommendCoachRequest, opts ...grpc.CallOption) (*AddIgnoreRecommendCoachResponse, error)
	GetReCoachForUGC(ctx context.Context, in *GetReCoachForUGCRequest, opts ...grpc.CallOption) (*GetReCoachForUGCResponse, error)
	// =============================== 内部接口 ==========================
	// 重建用户使用了首单优惠的缓存
	ReBuildUserFirstRoundCache(ctx context.Context, in *ReBuildUserFirstRoundCacheRequest, opts ...grpc.CallOption) (*ReBuildUserFirstRoundCacheResponse, error)
	// 获取大神激励任务分成比例
	GetCoachIncentiveAddition(ctx context.Context, in *GetCoachIncentiveAdditionRequest, opts ...grpc.CallOption) (*GetCoachIncentiveAdditionResponse, error)
	// 保存用户的top game列表的前置选择配置
	SaveUserTopGamePreSelectConfig(ctx context.Context, in *SaveUserTopGamePreSelectConfigRequest, opts ...grpc.CallOption) (*SaveUserTopGamePreSelectConfigResponse, error)
	// 获取用户的top game列表的前置选择配置
	GetUserTopGamePreSelectConfig(ctx context.Context, in *GetUserTopGamePreSelectConfigRequest, opts ...grpc.CallOption) (*GetUserTopGamePreSelectConfigResponse, error)
}

type esportHallClient struct {
	cc *grpc.ClientConn
}

func NewEsportHallClient(cc *grpc.ClientConn) EsportHallClient {
	return &esportHallClient{cc}
}

func (c *esportHallClient) InitUserSkillInfo(ctx context.Context, in *InitUserSkillInfoRequest, opts ...grpc.CallOption) (*InitUserSkillInfoResponse, error) {
	out := new(InitUserSkillInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/InitUserSkillInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetVisibleSkillProductList(ctx context.Context, in *GetVisibleSkillProductListRequest, opts ...grpc.CallOption) (*GetVisibleSkillProductListResponse, error) {
	out := new(GetVisibleSkillProductListResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetVisibleSkillProductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetAllSkillList(ctx context.Context, in *GetAllSkillListRequest, opts ...grpc.CallOption) (*GetAllSkillListResponse, error) {
	out := new(GetAllSkillListResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetAllSkillList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetOperationCoachRecommendWithoutExposed(ctx context.Context, in *GetOperationCoachRecommendWithoutExposedRequest, opts ...grpc.CallOption) (*GetOperationCoachRecommendWithoutExposedResponse, error) {
	out := new(GetOperationCoachRecommendWithoutExposedResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetOperationCoachRecommendWithoutExposed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SetSkillReceiveSwitch(ctx context.Context, in *SetSkillReceiveSwitchRequest, opts ...grpc.CallOption) (*SetSkillReceiveSwitchResponse, error) {
	out := new(SetSkillReceiveSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SetSkillReceiveSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetReceiveTimeFrame(ctx context.Context, in *GetReceiveTimeFrameRequest, opts ...grpc.CallOption) (*GetReceiveTimeFrameResponse, error) {
	out := new(GetReceiveTimeFrameResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetReceiveTimeFrame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SetReceiveTimeFrame(ctx context.Context, in *SetReceiveTimeFrameRequest, opts ...grpc.CallOption) (*SetReceiveTimeFrameResponse, error) {
	out := new(SetReceiveTimeFrameResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SetReceiveTimeFrame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SetSkillPrice(ctx context.Context, in *SetSkillPriceRequest, opts ...grpc.CallOption) (*SetSkillPriceResponse, error) {
	out := new(SetSkillPriceResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SetSkillPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SetFirstRoundSwitch(ctx context.Context, in *SetFirstRoundSwitchRequest, opts ...grpc.CallOption) (*SetFirstRoundSwitchResponse, error) {
	out := new(SetFirstRoundSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SetFirstRoundSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetFirstRoundDiscountInfo(ctx context.Context, in *GetFirstRoundDiscountInfoRequest, opts ...grpc.CallOption) (*GetFirstRoundDiscountInfoResponse, error) {
	out := new(GetFirstRoundDiscountInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetFirstRoundDiscountInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetFirstRoundDiscountGameList(ctx context.Context, in *GetFirstRoundDiscountGameListRequest, opts ...grpc.CallOption) (*GetFirstRoundDiscountGameListResponse, error) {
	out := new(GetFirstRoundDiscountGameListResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetFirstRoundDiscountGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) CheckFirstRoundOrderRight(ctx context.Context, in *CheckFirstRoundOrderRightRequest, opts ...grpc.CallOption) (*CheckFirstRoundOrderRightResponse, error) {
	out := new(CheckFirstRoundOrderRightResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/CheckFirstRoundOrderRight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetFirstRoundOrderRight(ctx context.Context, in *GetFirstRoundOrderRightRequest, opts ...grpc.CallOption) (*GetFirstRoundOrderRightResponse, error) {
	out := new(GetFirstRoundOrderRightResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetFirstRoundOrderRight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) ClearFirstRoundOrder(ctx context.Context, in *ClearFirstRoundOrderRequest, opts ...grpc.CallOption) (*ClearFirstRoundOrderResponse, error) {
	out := new(ClearFirstRoundOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/ClearFirstRoundOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetFirstRoundLabelByUid(ctx context.Context, in *GetFirstRoundLabelByUidRequest, opts ...grpc.CallOption) (*GetFirstRoundLabelByUidResponse, error) {
	out := new(GetFirstRoundLabelByUidResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetFirstRoundLabelByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetFirstRoundLabelBySkill(ctx context.Context, in *GetFirstRoundLabelBySkillRequest, opts ...grpc.CallOption) (*GetFirstRoundLabelBySkillResponse, error) {
	out := new(GetFirstRoundLabelBySkillResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetFirstRoundLabelBySkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SetNewCustomerSwitch(ctx context.Context, in *SetNewCustomerSwitchRequest, opts ...grpc.CallOption) (*SetNewCustomerSwitchResponse, error) {
	out := new(SetNewCustomerSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SetNewCustomerSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetNewCustomerDiscountInfo(ctx context.Context, in *GetNewCustomerDiscountInfoRequest, opts ...grpc.CallOption) (*GetNewCustomerDiscountInfoResponse, error) {
	out := new(GetNewCustomerDiscountInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetNewCustomerDiscountInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) ClearNewCustomerOrder(ctx context.Context, in *ClearNewCustomerOrderRequest, opts ...grpc.CallOption) (*ClearNewCustomerOrderResponse, error) {
	out := new(ClearNewCustomerOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/ClearNewCustomerOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetNewCustomerPriceByUid(ctx context.Context, in *GetNewCustomerPriceByUidRequest, opts ...grpc.CallOption) (*GetNewCustomerPriceByUidResponse, error) {
	out := new(GetNewCustomerPriceByUidResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetNewCustomerPriceByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetNewCustomerPriceBySkill(ctx context.Context, in *GetNewCustomerPriceBySkillRequest, opts ...grpc.CallOption) (*GetNewCustomerPriceBySkillResponse, error) {
	out := new(GetNewCustomerPriceBySkillResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetNewCustomerPriceBySkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetSkillProductInfo(ctx context.Context, in *GetSkillProductInfoRequest, opts ...grpc.CallOption) (*GetSkillProductInfoResponse, error) {
	out := new(GetSkillProductInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetSkillProductInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetSkillProductInfoByGameId(ctx context.Context, in *GetSkillProductInfoByGameIdRequest, opts ...grpc.CallOption) (*GetSkillProductInfoByGameIdResponse, error) {
	out := new(GetSkillProductInfoByGameIdResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetSkillProductInfoByGameId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetGameCoachList(ctx context.Context, in *GetGameCoachListRequest, opts ...grpc.CallOption) (*GetCoachListResponse, error) {
	out := new(GetCoachListResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetGameCoachList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetGameCoachListByUid(ctx context.Context, in *GetGameCoachListByUidRequest, opts ...grpc.CallOption) (*GetGameCoachListByUidResponse, error) {
	out := new(GetGameCoachListByUidResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetGameCoachListByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) ReportExposeCoach(ctx context.Context, in *ReportExposeCoachRequest, opts ...grpc.CallOption) (*ReportExposeCoachResponse, error) {
	out := new(ReportExposeCoachResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/ReportExposeCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) InviteOrder(ctx context.Context, in *InviteOrderRequest, opts ...grpc.CallOption) (*InviteOrderResponse, error) {
	out := new(InviteOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/InviteOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) HandleInviteOrder(ctx context.Context, in *HandleInviteOrderRequest, opts ...grpc.CallOption) (*HandleInviteOrderResponse, error) {
	out := new(HandleInviteOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/HandleInviteOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) DelSkillProduct(ctx context.Context, in *DelSkillProductRequest, opts ...grpc.CallOption) (*DelSkillProductResponse, error) {
	out := new(DelSkillProductResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/DelSkillProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) BatchGetSkillProductInfo(ctx context.Context, in *BatchGetSkillProductInfoRequest, opts ...grpc.CallOption) (*BatchGetSkillProductInfoResponse, error) {
	out := new(BatchGetSkillProductInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/BatchGetSkillProductInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) Test(ctx context.Context, in *TestRequest, opts ...grpc.CallOption) (*TestResponse, error) {
	out := new(TestResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/Test", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetGamePriceProperty(ctx context.Context, in *GetGamePricePropertyRequest, opts ...grpc.CallOption) (*GetGamePricePropertyResponse, error) {
	out := new(GetGamePricePropertyResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetGamePriceProperty", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetSkillProductByUidGameId(ctx context.Context, in *GetSkillProductByUidGameIdRequest, opts ...grpc.CallOption) (*GetSkillProductByUidGameIdResponse, error) {
	out := new(GetSkillProductByUidGameIdResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetSkillProductByUidGameId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SetQuickReceiveSwitch(ctx context.Context, in *SetQuickReceiveSwitchRequest, opts ...grpc.CallOption) (*SetQuickReceiveSwitchResponse, error) {
	out := new(SetQuickReceiveSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SetQuickReceiveSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetQuickReceiveSwitch(ctx context.Context, in *GetQuickReceiveSwitchRequest, opts ...grpc.CallOption) (*GetQuickReceiveSwitchResponse, error) {
	out := new(GetQuickReceiveSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetQuickReceiveSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) BatchGetQuickReceiveSwitch(ctx context.Context, in *BatchGetQuickReceiveSwitchRequest, opts ...grpc.CallOption) (*BatchGetQuickReceiveSwitchResponse, error) {
	out := new(BatchGetQuickReceiveSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/BatchGetQuickReceiveSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) HasFamousPlayer(ctx context.Context, in *HasFamousPlayerRequest, opts ...grpc.CallOption) (*HasFamousPlayerResponse, error) {
	out := new(HasFamousPlayerResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/HasFamousPlayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetEsportGameCardInfo(ctx context.Context, in *GetEsportGameCardInfoRequest, opts ...grpc.CallOption) (*GetEsportGameCardInfoResponse, error) {
	out := new(GetEsportGameCardInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetEsportGameCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) CreateEsportGameCard(ctx context.Context, in *CreateEsportGameCardRequest, opts ...grpc.CallOption) (*CreateEsportGameCardResponse, error) {
	out := new(CreateEsportGameCardResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/CreateEsportGameCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) UpdateEsportGameCard(ctx context.Context, in *UpdateEsportGameCardRequest, opts ...grpc.CallOption) (*UpdateEsportGameCardResponse, error) {
	out := new(UpdateEsportGameCardResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/UpdateEsportGameCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetEsportGameCardList(ctx context.Context, in *GetEsportGameCardListRequest, opts ...grpc.CallOption) (*GetEsportGameCardListResponse, error) {
	out := new(GetEsportGameCardListResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetEsportGameCardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SendEsportGameCard(ctx context.Context, in *SendEsportGameCardRequest, opts ...grpc.CallOption) (*SendEsportGameCardResponse, error) {
	out := new(SendEsportGameCardResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SendEsportGameCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) DeleteEsportGameCard(ctx context.Context, in *DeleteEsportGameCardRequest, opts ...grpc.CallOption) (*DeleteEsportGameCardResponse, error) {
	out := new(DeleteEsportGameCardResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/DeleteEsportGameCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SetGuaranteeWinSwitch(ctx context.Context, in *SetGuaranteeWinSwitchRequest, opts ...grpc.CallOption) (*SetGuaranteeWinSwitchResponse, error) {
	out := new(SetGuaranteeWinSwitchResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SetGuaranteeWinSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetRecommendSkillProduct(ctx context.Context, in *GetRecommendSkillProductReq, opts ...grpc.CallOption) (*GetRecommendSkillProductResp, error) {
	out := new(GetRecommendSkillProductResp)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetRecommendSkillProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetGlobalRcmdCoach(ctx context.Context, in *GetGlobalRcmdCoachRequest, opts ...grpc.CallOption) (*GetGlobalRcmdCoachResponse, error) {
	out := new(GetGlobalRcmdCoachResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetGlobalRcmdCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SetUserToRealCoach(ctx context.Context, in *SetUserToRealCoachRequest, opts ...grpc.CallOption) (*SetUserToRealCoachResponse, error) {
	out := new(SetUserToRealCoachResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SetUserToRealCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetCoachMinPrice(ctx context.Context, in *GetCoachMinPriceRequest, opts ...grpc.CallOption) (*GetCoachMinPriceResponse, error) {
	out := new(GetCoachMinPriceResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetCoachMinPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetCoachIncentiveTaskInfo(ctx context.Context, in *GetCoachIncentiveTaskInfoRequest, opts ...grpc.CallOption) (*GetCoachIncentiveTaskInfoResponse, error) {
	out := new(GetCoachIncentiveTaskInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetCoachIncentiveTaskInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) AddCoachRecommend(ctx context.Context, in *AddCoachRecommendRequest, opts ...grpc.CallOption) (*AddCoachRecommendResponse, error) {
	out := new(AddCoachRecommendResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/AddCoachRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetCoachRecommend(ctx context.Context, in *GetCoachRecommendRequest, opts ...grpc.CallOption) (*GetCoachRecommendResponse, error) {
	out := new(GetCoachRecommendResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetCoachRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) UpdateCoachRecommend(ctx context.Context, in *UpdateCoachRecommendRequest, opts ...grpc.CallOption) (*UpdateCoachRecommendResponse, error) {
	out := new(UpdateCoachRecommendResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/UpdateCoachRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) DelCoachRecommend(ctx context.Context, in *DelCoachRecommendRequest, opts ...grpc.CallOption) (*DelCoachRecommendResponse, error) {
	out := new(DelCoachRecommendResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/DelCoachRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SearchCoach(ctx context.Context, in *SearchCoachRequest, opts ...grpc.CallOption) (*SearchCoachResponse, error) {
	out := new(SearchCoachResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SearchCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) AddIgnoreRecommendCoach(ctx context.Context, in *AddIgnoreRecommendCoachRequest, opts ...grpc.CallOption) (*AddIgnoreRecommendCoachResponse, error) {
	out := new(AddIgnoreRecommendCoachResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/AddIgnoreRecommendCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetReCoachForUGC(ctx context.Context, in *GetReCoachForUGCRequest, opts ...grpc.CallOption) (*GetReCoachForUGCResponse, error) {
	out := new(GetReCoachForUGCResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetReCoachForUGC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) ReBuildUserFirstRoundCache(ctx context.Context, in *ReBuildUserFirstRoundCacheRequest, opts ...grpc.CallOption) (*ReBuildUserFirstRoundCacheResponse, error) {
	out := new(ReBuildUserFirstRoundCacheResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/ReBuildUserFirstRoundCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetCoachIncentiveAddition(ctx context.Context, in *GetCoachIncentiveAdditionRequest, opts ...grpc.CallOption) (*GetCoachIncentiveAdditionResponse, error) {
	out := new(GetCoachIncentiveAdditionResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetCoachIncentiveAddition", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) SaveUserTopGamePreSelectConfig(ctx context.Context, in *SaveUserTopGamePreSelectConfigRequest, opts ...grpc.CallOption) (*SaveUserTopGamePreSelectConfigResponse, error) {
	out := new(SaveUserTopGamePreSelectConfigResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/SaveUserTopGamePreSelectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportHallClient) GetUserTopGamePreSelectConfig(ctx context.Context, in *GetUserTopGamePreSelectConfigRequest, opts ...grpc.CallOption) (*GetUserTopGamePreSelectConfigResponse, error) {
	out := new(GetUserTopGamePreSelectConfigResponse)
	err := c.cc.Invoke(ctx, "/esport_hall.EsportHall/GetUserTopGamePreSelectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EsportHallServer is the server API for EsportHall service.
type EsportHallServer interface {
	// 初始化用户技能信息
	InitUserSkillInfo(context.Context, *InitUserSkillInfoRequest) (*InitUserSkillInfoResponse, error)
	// 获取当前可见技能商品
	GetVisibleSkillProductList(context.Context, *GetVisibleSkillProductListRequest) (*GetVisibleSkillProductListResponse, error)
	// 获取所有技能
	GetAllSkillList(context.Context, *GetAllSkillListRequest) (*GetAllSkillListResponse, error)
	// 获取运营推荐大神、排除已曝光大神
	GetOperationCoachRecommendWithoutExposed(context.Context, *GetOperationCoachRecommendWithoutExposedRequest) (*GetOperationCoachRecommendWithoutExposedResponse, error)
	// 设置技能开关
	SetSkillReceiveSwitch(context.Context, *SetSkillReceiveSwitchRequest) (*SetSkillReceiveSwitchResponse, error)
	// 获取接单时段信息
	GetReceiveTimeFrame(context.Context, *GetReceiveTimeFrameRequest) (*GetReceiveTimeFrameResponse, error)
	// 设置接单时段
	SetReceiveTimeFrame(context.Context, *SetReceiveTimeFrameRequest) (*SetReceiveTimeFrameResponse, error)
	// 设置技能价格
	SetSkillPrice(context.Context, *SetSkillPriceRequest) (*SetSkillPriceResponse, error)
	// 设置首局优惠开关
	SetFirstRoundSwitch(context.Context, *SetFirstRoundSwitchRequest) (*SetFirstRoundSwitchResponse, error)
	// 获取首局优惠信息
	GetFirstRoundDiscountInfo(context.Context, *GetFirstRoundDiscountInfoRequest) (*GetFirstRoundDiscountInfoResponse, error)
	// 获取所有首局优惠游戏
	GetFirstRoundDiscountGameList(context.Context, *GetFirstRoundDiscountGameListRequest) (*GetFirstRoundDiscountGameListResponse, error)
	// 检查首局优惠下单权限
	CheckFirstRoundOrderRight(context.Context, *CheckFirstRoundOrderRightRequest) (*CheckFirstRoundOrderRightResponse, error)
	// 获取首局优惠下单权限
	GetFirstRoundOrderRight(context.Context, *GetFirstRoundOrderRightRequest) (*GetFirstRoundOrderRightResponse, error)
	// 清理首局优惠订单，debug用
	ClearFirstRoundOrder(context.Context, *ClearFirstRoundOrderRequest) (*ClearFirstRoundOrderResponse, error)
	// 获取首局优惠标签-通过uid
	GetFirstRoundLabelByUid(context.Context, *GetFirstRoundLabelByUidRequest) (*GetFirstRoundLabelByUidResponse, error)
	// 获取首局优惠标签-通过skill
	GetFirstRoundLabelBySkill(context.Context, *GetFirstRoundLabelBySkillRequest) (*GetFirstRoundLabelBySkillResponse, error)
	// 设置新客价开关
	SetNewCustomerSwitch(context.Context, *SetNewCustomerSwitchRequest) (*SetNewCustomerSwitchResponse, error)
	// 获取新客价信息
	GetNewCustomerDiscountInfo(context.Context, *GetNewCustomerDiscountInfoRequest) (*GetNewCustomerDiscountInfoResponse, error)
	// 清理新客价订单，debug用
	ClearNewCustomerOrder(context.Context, *ClearNewCustomerOrderRequest) (*ClearNewCustomerOrderResponse, error)
	// 获取新客价-通过uid
	GetNewCustomerPriceByUid(context.Context, *GetNewCustomerPriceByUidRequest) (*GetNewCustomerPriceByUidResponse, error)
	// 获取新客价-通过skill
	GetNewCustomerPriceBySkill(context.Context, *GetNewCustomerPriceBySkillRequest) (*GetNewCustomerPriceBySkillResponse, error)
	// 获取技能商品信息
	GetSkillProductInfo(context.Context, *GetSkillProductInfoRequest) (*GetSkillProductInfoResponse, error)
	// 通过gameId获取技能商品信息
	GetSkillProductInfoByGameId(context.Context, *GetSkillProductInfoByGameIdRequest) (*GetSkillProductInfoByGameIdResponse, error)
	// 获取某款游戏的教练列表
	GetGameCoachList(context.Context, *GetGameCoachListRequest) (*GetCoachListResponse, error)
	// 获取某款游戏的教练列表(通过uid过滤)
	GetGameCoachListByUid(context.Context, *GetGameCoachListByUidRequest) (*GetGameCoachListByUidResponse, error)
	// 上报曝光大神
	ReportExposeCoach(context.Context, *ReportExposeCoachRequest) (*ReportExposeCoachResponse, error)
	// 邀请下单
	InviteOrder(context.Context, *InviteOrderRequest) (*InviteOrderResponse, error)
	// 处理邀请下单
	HandleInviteOrder(context.Context, *HandleInviteOrderRequest) (*HandleInviteOrderResponse, error)
	// 删除技能商品
	DelSkillProduct(context.Context, *DelSkillProductRequest) (*DelSkillProductResponse, error)
	// 批量获取技能商品信息
	BatchGetSkillProductInfo(context.Context, *BatchGetSkillProductInfoRequest) (*BatchGetSkillProductInfoResponse, error)
	// 测试
	Test(context.Context, *TestRequest) (*TestResponse, error)
	// 获取游戏价格属性
	GetGamePriceProperty(context.Context, *GetGamePricePropertyRequest) (*GetGamePricePropertyResponse, error)
	// 获取某款游戏的技能商品
	GetSkillProductByUidGameId(context.Context, *GetSkillProductByUidGameIdRequest) (*GetSkillProductByUidGameIdResponse, error)
	// 设置秒接单开关
	SetQuickReceiveSwitch(context.Context, *SetQuickReceiveSwitchRequest) (*SetQuickReceiveSwitchResponse, error)
	// 获取秒接单开关状态
	GetQuickReceiveSwitch(context.Context, *GetQuickReceiveSwitchRequest) (*GetQuickReceiveSwitchResponse, error)
	// 批量获取秒接单开关状态
	BatchGetQuickReceiveSwitch(context.Context, *BatchGetQuickReceiveSwitchRequest) (*BatchGetQuickReceiveSwitchResponse, error)
	// 获取知名选手数量
	HasFamousPlayer(context.Context, *HasFamousPlayerRequest) (*HasFamousPlayerResponse, error)
	// 查询游戏名片信息
	GetEsportGameCardInfo(context.Context, *GetEsportGameCardInfoRequest) (*GetEsportGameCardInfoResponse, error)
	// 创建游戏名片
	CreateEsportGameCard(context.Context, *CreateEsportGameCardRequest) (*CreateEsportGameCardResponse, error)
	// 更新游戏名片
	UpdateEsportGameCard(context.Context, *UpdateEsportGameCardRequest) (*UpdateEsportGameCardResponse, error)
	// 获取游戏名片列表
	GetEsportGameCardList(context.Context, *GetEsportGameCardListRequest) (*GetEsportGameCardListResponse, error)
	// 发送游戏名片
	SendEsportGameCard(context.Context, *SendEsportGameCardRequest) (*SendEsportGameCardResponse, error)
	// 删除游戏名片
	DeleteEsportGameCard(context.Context, *DeleteEsportGameCardRequest) (*DeleteEsportGameCardResponse, error)
	// 设置包赢开关状态
	SetGuaranteeWinSwitch(context.Context, *SetGuaranteeWinSwitchRequest) (*SetGuaranteeWinSwitchResponse, error)
	// 王者tab大神卡
	GetRecommendSkillProduct(context.Context, *GetRecommendSkillProductReq) (*GetRecommendSkillProductResp, error)
	// 获取全局弹窗推荐大神
	GetGlobalRcmdCoach(context.Context, *GetGlobalRcmdCoachRequest) (*GetGlobalRcmdCoachResponse, error)
	// 设置用户->客服聊天对应的真实大神uid
	SetUserToRealCoach(context.Context, *SetUserToRealCoachRequest) (*SetUserToRealCoachResponse, error)
	// 获取教练最低价格
	GetCoachMinPrice(context.Context, *GetCoachMinPriceRequest) (*GetCoachMinPriceResponse, error)
	// 获取激励任务信息
	GetCoachIncentiveTaskInfo(context.Context, *GetCoachIncentiveTaskInfoRequest) (*GetCoachIncentiveTaskInfoResponse, error)
	// =============================== 运营后台相关 ========================================
	AddCoachRecommend(context.Context, *AddCoachRecommendRequest) (*AddCoachRecommendResponse, error)
	GetCoachRecommend(context.Context, *GetCoachRecommendRequest) (*GetCoachRecommendResponse, error)
	UpdateCoachRecommend(context.Context, *UpdateCoachRecommendRequest) (*UpdateCoachRecommendResponse, error)
	DelCoachRecommend(context.Context, *DelCoachRecommendRequest) (*DelCoachRecommendResponse, error)
	// SearchCoach 搜索大神，使用推荐列表排序，如果不在推荐列表，则按照默认排序
	SearchCoach(context.Context, *SearchCoachRequest) (*SearchCoachResponse, error)
	// =============================== UGC房特供接口 ==========================
	// 更新用户的推荐卡片状态
	AddIgnoreRecommendCoach(context.Context, *AddIgnoreRecommendCoachRequest) (*AddIgnoreRecommendCoachResponse, error)
	GetReCoachForUGC(context.Context, *GetReCoachForUGCRequest) (*GetReCoachForUGCResponse, error)
	// =============================== 内部接口 ==========================
	// 重建用户使用了首单优惠的缓存
	ReBuildUserFirstRoundCache(context.Context, *ReBuildUserFirstRoundCacheRequest) (*ReBuildUserFirstRoundCacheResponse, error)
	// 获取大神激励任务分成比例
	GetCoachIncentiveAddition(context.Context, *GetCoachIncentiveAdditionRequest) (*GetCoachIncentiveAdditionResponse, error)
	// 保存用户的top game列表的前置选择配置
	SaveUserTopGamePreSelectConfig(context.Context, *SaveUserTopGamePreSelectConfigRequest) (*SaveUserTopGamePreSelectConfigResponse, error)
	// 获取用户的top game列表的前置选择配置
	GetUserTopGamePreSelectConfig(context.Context, *GetUserTopGamePreSelectConfigRequest) (*GetUserTopGamePreSelectConfigResponse, error)
}

func RegisterEsportHallServer(s *grpc.Server, srv EsportHallServer) {
	s.RegisterService(&_EsportHall_serviceDesc, srv)
}

func _EsportHall_InitUserSkillInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitUserSkillInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).InitUserSkillInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/InitUserSkillInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).InitUserSkillInfo(ctx, req.(*InitUserSkillInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetVisibleSkillProductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVisibleSkillProductListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetVisibleSkillProductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetVisibleSkillProductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetVisibleSkillProductList(ctx, req.(*GetVisibleSkillProductListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetAllSkillList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllSkillListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetAllSkillList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetAllSkillList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetAllSkillList(ctx, req.(*GetAllSkillListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetOperationCoachRecommendWithoutExposed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperationCoachRecommendWithoutExposedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetOperationCoachRecommendWithoutExposed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetOperationCoachRecommendWithoutExposed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetOperationCoachRecommendWithoutExposed(ctx, req.(*GetOperationCoachRecommendWithoutExposedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SetSkillReceiveSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSkillReceiveSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SetSkillReceiveSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SetSkillReceiveSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SetSkillReceiveSwitch(ctx, req.(*SetSkillReceiveSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetReceiveTimeFrame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReceiveTimeFrameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetReceiveTimeFrame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetReceiveTimeFrame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetReceiveTimeFrame(ctx, req.(*GetReceiveTimeFrameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SetReceiveTimeFrame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetReceiveTimeFrameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SetReceiveTimeFrame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SetReceiveTimeFrame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SetReceiveTimeFrame(ctx, req.(*SetReceiveTimeFrameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SetSkillPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSkillPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SetSkillPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SetSkillPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SetSkillPrice(ctx, req.(*SetSkillPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SetFirstRoundSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetFirstRoundSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SetFirstRoundSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SetFirstRoundSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SetFirstRoundSwitch(ctx, req.(*SetFirstRoundSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetFirstRoundDiscountInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFirstRoundDiscountInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetFirstRoundDiscountInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetFirstRoundDiscountInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetFirstRoundDiscountInfo(ctx, req.(*GetFirstRoundDiscountInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetFirstRoundDiscountGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFirstRoundDiscountGameListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetFirstRoundDiscountGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetFirstRoundDiscountGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetFirstRoundDiscountGameList(ctx, req.(*GetFirstRoundDiscountGameListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_CheckFirstRoundOrderRight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckFirstRoundOrderRightRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).CheckFirstRoundOrderRight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/CheckFirstRoundOrderRight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).CheckFirstRoundOrderRight(ctx, req.(*CheckFirstRoundOrderRightRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetFirstRoundOrderRight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFirstRoundOrderRightRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetFirstRoundOrderRight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetFirstRoundOrderRight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetFirstRoundOrderRight(ctx, req.(*GetFirstRoundOrderRightRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_ClearFirstRoundOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearFirstRoundOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).ClearFirstRoundOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/ClearFirstRoundOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).ClearFirstRoundOrder(ctx, req.(*ClearFirstRoundOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetFirstRoundLabelByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFirstRoundLabelByUidRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetFirstRoundLabelByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetFirstRoundLabelByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetFirstRoundLabelByUid(ctx, req.(*GetFirstRoundLabelByUidRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetFirstRoundLabelBySkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFirstRoundLabelBySkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetFirstRoundLabelBySkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetFirstRoundLabelBySkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetFirstRoundLabelBySkill(ctx, req.(*GetFirstRoundLabelBySkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SetNewCustomerSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetNewCustomerSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SetNewCustomerSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SetNewCustomerSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SetNewCustomerSwitch(ctx, req.(*SetNewCustomerSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetNewCustomerDiscountInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewCustomerDiscountInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetNewCustomerDiscountInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetNewCustomerDiscountInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetNewCustomerDiscountInfo(ctx, req.(*GetNewCustomerDiscountInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_ClearNewCustomerOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearNewCustomerOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).ClearNewCustomerOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/ClearNewCustomerOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).ClearNewCustomerOrder(ctx, req.(*ClearNewCustomerOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetNewCustomerPriceByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewCustomerPriceByUidRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetNewCustomerPriceByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetNewCustomerPriceByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetNewCustomerPriceByUid(ctx, req.(*GetNewCustomerPriceByUidRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetNewCustomerPriceBySkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewCustomerPriceBySkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetNewCustomerPriceBySkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetNewCustomerPriceBySkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetNewCustomerPriceBySkill(ctx, req.(*GetNewCustomerPriceBySkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetSkillProductInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSkillProductInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetSkillProductInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetSkillProductInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetSkillProductInfo(ctx, req.(*GetSkillProductInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetSkillProductInfoByGameId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSkillProductInfoByGameIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetSkillProductInfoByGameId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetSkillProductInfoByGameId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetSkillProductInfoByGameId(ctx, req.(*GetSkillProductInfoByGameIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetGameCoachList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameCoachListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetGameCoachList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetGameCoachList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetGameCoachList(ctx, req.(*GetGameCoachListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetGameCoachListByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameCoachListByUidRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetGameCoachListByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetGameCoachListByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetGameCoachListByUid(ctx, req.(*GetGameCoachListByUidRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_ReportExposeCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportExposeCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).ReportExposeCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/ReportExposeCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).ReportExposeCoach(ctx, req.(*ReportExposeCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_InviteOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InviteOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).InviteOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/InviteOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).InviteOrder(ctx, req.(*InviteOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_HandleInviteOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleInviteOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).HandleInviteOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/HandleInviteOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).HandleInviteOrder(ctx, req.(*HandleInviteOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_DelSkillProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSkillProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).DelSkillProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/DelSkillProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).DelSkillProduct(ctx, req.(*DelSkillProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_BatchGetSkillProductInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetSkillProductInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).BatchGetSkillProductInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/BatchGetSkillProductInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).BatchGetSkillProductInfo(ctx, req.(*BatchGetSkillProductInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_Test_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).Test(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/Test",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).Test(ctx, req.(*TestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetGamePriceProperty_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGamePricePropertyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetGamePriceProperty(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetGamePriceProperty",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetGamePriceProperty(ctx, req.(*GetGamePricePropertyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetSkillProductByUidGameId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSkillProductByUidGameIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetSkillProductByUidGameId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetSkillProductByUidGameId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetSkillProductByUidGameId(ctx, req.(*GetSkillProductByUidGameIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SetQuickReceiveSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetQuickReceiveSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SetQuickReceiveSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SetQuickReceiveSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SetQuickReceiveSwitch(ctx, req.(*SetQuickReceiveSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetQuickReceiveSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickReceiveSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetQuickReceiveSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetQuickReceiveSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetQuickReceiveSwitch(ctx, req.(*GetQuickReceiveSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_BatchGetQuickReceiveSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetQuickReceiveSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).BatchGetQuickReceiveSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/BatchGetQuickReceiveSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).BatchGetQuickReceiveSwitch(ctx, req.(*BatchGetQuickReceiveSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_HasFamousPlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HasFamousPlayerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).HasFamousPlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/HasFamousPlayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).HasFamousPlayer(ctx, req.(*HasFamousPlayerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetEsportGameCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEsportGameCardInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetEsportGameCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetEsportGameCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetEsportGameCardInfo(ctx, req.(*GetEsportGameCardInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_CreateEsportGameCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEsportGameCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).CreateEsportGameCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/CreateEsportGameCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).CreateEsportGameCard(ctx, req.(*CreateEsportGameCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_UpdateEsportGameCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEsportGameCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).UpdateEsportGameCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/UpdateEsportGameCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).UpdateEsportGameCard(ctx, req.(*UpdateEsportGameCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetEsportGameCardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEsportGameCardListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetEsportGameCardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetEsportGameCardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetEsportGameCardList(ctx, req.(*GetEsportGameCardListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SendEsportGameCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendEsportGameCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SendEsportGameCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SendEsportGameCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SendEsportGameCard(ctx, req.(*SendEsportGameCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_DeleteEsportGameCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEsportGameCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).DeleteEsportGameCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/DeleteEsportGameCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).DeleteEsportGameCard(ctx, req.(*DeleteEsportGameCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SetGuaranteeWinSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGuaranteeWinSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SetGuaranteeWinSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SetGuaranteeWinSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SetGuaranteeWinSwitch(ctx, req.(*SetGuaranteeWinSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetRecommendSkillProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendSkillProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetRecommendSkillProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetRecommendSkillProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetRecommendSkillProduct(ctx, req.(*GetRecommendSkillProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetGlobalRcmdCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGlobalRcmdCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetGlobalRcmdCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetGlobalRcmdCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetGlobalRcmdCoach(ctx, req.(*GetGlobalRcmdCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SetUserToRealCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserToRealCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SetUserToRealCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SetUserToRealCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SetUserToRealCoach(ctx, req.(*SetUserToRealCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetCoachMinPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachMinPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetCoachMinPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetCoachMinPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetCoachMinPrice(ctx, req.(*GetCoachMinPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetCoachIncentiveTaskInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachIncentiveTaskInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetCoachIncentiveTaskInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetCoachIncentiveTaskInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetCoachIncentiveTaskInfo(ctx, req.(*GetCoachIncentiveTaskInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_AddCoachRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCoachRecommendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).AddCoachRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/AddCoachRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).AddCoachRecommend(ctx, req.(*AddCoachRecommendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetCoachRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachRecommendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetCoachRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetCoachRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetCoachRecommend(ctx, req.(*GetCoachRecommendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_UpdateCoachRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCoachRecommendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).UpdateCoachRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/UpdateCoachRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).UpdateCoachRecommend(ctx, req.(*UpdateCoachRecommendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_DelCoachRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCoachRecommendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).DelCoachRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/DelCoachRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).DelCoachRecommend(ctx, req.(*DelCoachRecommendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SearchCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SearchCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SearchCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SearchCoach(ctx, req.(*SearchCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_AddIgnoreRecommendCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddIgnoreRecommendCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).AddIgnoreRecommendCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/AddIgnoreRecommendCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).AddIgnoreRecommendCoach(ctx, req.(*AddIgnoreRecommendCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetReCoachForUGC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReCoachForUGCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetReCoachForUGC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetReCoachForUGC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetReCoachForUGC(ctx, req.(*GetReCoachForUGCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_ReBuildUserFirstRoundCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReBuildUserFirstRoundCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).ReBuildUserFirstRoundCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/ReBuildUserFirstRoundCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).ReBuildUserFirstRoundCache(ctx, req.(*ReBuildUserFirstRoundCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetCoachIncentiveAddition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachIncentiveAdditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetCoachIncentiveAddition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetCoachIncentiveAddition",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetCoachIncentiveAddition(ctx, req.(*GetCoachIncentiveAdditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_SaveUserTopGamePreSelectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveUserTopGamePreSelectConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).SaveUserTopGamePreSelectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/SaveUserTopGamePreSelectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).SaveUserTopGamePreSelectConfig(ctx, req.(*SaveUserTopGamePreSelectConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportHall_GetUserTopGamePreSelectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTopGamePreSelectConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportHallServer).GetUserTopGamePreSelectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_hall.EsportHall/GetUserTopGamePreSelectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportHallServer).GetUserTopGamePreSelectConfig(ctx, req.(*GetUserTopGamePreSelectConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _EsportHall_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_hall.EsportHall",
	HandlerType: (*EsportHallServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InitUserSkillInfo",
			Handler:    _EsportHall_InitUserSkillInfo_Handler,
		},
		{
			MethodName: "GetVisibleSkillProductList",
			Handler:    _EsportHall_GetVisibleSkillProductList_Handler,
		},
		{
			MethodName: "GetAllSkillList",
			Handler:    _EsportHall_GetAllSkillList_Handler,
		},
		{
			MethodName: "GetOperationCoachRecommendWithoutExposed",
			Handler:    _EsportHall_GetOperationCoachRecommendWithoutExposed_Handler,
		},
		{
			MethodName: "SetSkillReceiveSwitch",
			Handler:    _EsportHall_SetSkillReceiveSwitch_Handler,
		},
		{
			MethodName: "GetReceiveTimeFrame",
			Handler:    _EsportHall_GetReceiveTimeFrame_Handler,
		},
		{
			MethodName: "SetReceiveTimeFrame",
			Handler:    _EsportHall_SetReceiveTimeFrame_Handler,
		},
		{
			MethodName: "SetSkillPrice",
			Handler:    _EsportHall_SetSkillPrice_Handler,
		},
		{
			MethodName: "SetFirstRoundSwitch",
			Handler:    _EsportHall_SetFirstRoundSwitch_Handler,
		},
		{
			MethodName: "GetFirstRoundDiscountInfo",
			Handler:    _EsportHall_GetFirstRoundDiscountInfo_Handler,
		},
		{
			MethodName: "GetFirstRoundDiscountGameList",
			Handler:    _EsportHall_GetFirstRoundDiscountGameList_Handler,
		},
		{
			MethodName: "CheckFirstRoundOrderRight",
			Handler:    _EsportHall_CheckFirstRoundOrderRight_Handler,
		},
		{
			MethodName: "GetFirstRoundOrderRight",
			Handler:    _EsportHall_GetFirstRoundOrderRight_Handler,
		},
		{
			MethodName: "ClearFirstRoundOrder",
			Handler:    _EsportHall_ClearFirstRoundOrder_Handler,
		},
		{
			MethodName: "GetFirstRoundLabelByUid",
			Handler:    _EsportHall_GetFirstRoundLabelByUid_Handler,
		},
		{
			MethodName: "GetFirstRoundLabelBySkill",
			Handler:    _EsportHall_GetFirstRoundLabelBySkill_Handler,
		},
		{
			MethodName: "SetNewCustomerSwitch",
			Handler:    _EsportHall_SetNewCustomerSwitch_Handler,
		},
		{
			MethodName: "GetNewCustomerDiscountInfo",
			Handler:    _EsportHall_GetNewCustomerDiscountInfo_Handler,
		},
		{
			MethodName: "ClearNewCustomerOrder",
			Handler:    _EsportHall_ClearNewCustomerOrder_Handler,
		},
		{
			MethodName: "GetNewCustomerPriceByUid",
			Handler:    _EsportHall_GetNewCustomerPriceByUid_Handler,
		},
		{
			MethodName: "GetNewCustomerPriceBySkill",
			Handler:    _EsportHall_GetNewCustomerPriceBySkill_Handler,
		},
		{
			MethodName: "GetSkillProductInfo",
			Handler:    _EsportHall_GetSkillProductInfo_Handler,
		},
		{
			MethodName: "GetSkillProductInfoByGameId",
			Handler:    _EsportHall_GetSkillProductInfoByGameId_Handler,
		},
		{
			MethodName: "GetGameCoachList",
			Handler:    _EsportHall_GetGameCoachList_Handler,
		},
		{
			MethodName: "GetGameCoachListByUid",
			Handler:    _EsportHall_GetGameCoachListByUid_Handler,
		},
		{
			MethodName: "ReportExposeCoach",
			Handler:    _EsportHall_ReportExposeCoach_Handler,
		},
		{
			MethodName: "InviteOrder",
			Handler:    _EsportHall_InviteOrder_Handler,
		},
		{
			MethodName: "HandleInviteOrder",
			Handler:    _EsportHall_HandleInviteOrder_Handler,
		},
		{
			MethodName: "DelSkillProduct",
			Handler:    _EsportHall_DelSkillProduct_Handler,
		},
		{
			MethodName: "BatchGetSkillProductInfo",
			Handler:    _EsportHall_BatchGetSkillProductInfo_Handler,
		},
		{
			MethodName: "Test",
			Handler:    _EsportHall_Test_Handler,
		},
		{
			MethodName: "GetGamePriceProperty",
			Handler:    _EsportHall_GetGamePriceProperty_Handler,
		},
		{
			MethodName: "GetSkillProductByUidGameId",
			Handler:    _EsportHall_GetSkillProductByUidGameId_Handler,
		},
		{
			MethodName: "SetQuickReceiveSwitch",
			Handler:    _EsportHall_SetQuickReceiveSwitch_Handler,
		},
		{
			MethodName: "GetQuickReceiveSwitch",
			Handler:    _EsportHall_GetQuickReceiveSwitch_Handler,
		},
		{
			MethodName: "BatchGetQuickReceiveSwitch",
			Handler:    _EsportHall_BatchGetQuickReceiveSwitch_Handler,
		},
		{
			MethodName: "HasFamousPlayer",
			Handler:    _EsportHall_HasFamousPlayer_Handler,
		},
		{
			MethodName: "GetEsportGameCardInfo",
			Handler:    _EsportHall_GetEsportGameCardInfo_Handler,
		},
		{
			MethodName: "CreateEsportGameCard",
			Handler:    _EsportHall_CreateEsportGameCard_Handler,
		},
		{
			MethodName: "UpdateEsportGameCard",
			Handler:    _EsportHall_UpdateEsportGameCard_Handler,
		},
		{
			MethodName: "GetEsportGameCardList",
			Handler:    _EsportHall_GetEsportGameCardList_Handler,
		},
		{
			MethodName: "SendEsportGameCard",
			Handler:    _EsportHall_SendEsportGameCard_Handler,
		},
		{
			MethodName: "DeleteEsportGameCard",
			Handler:    _EsportHall_DeleteEsportGameCard_Handler,
		},
		{
			MethodName: "SetGuaranteeWinSwitch",
			Handler:    _EsportHall_SetGuaranteeWinSwitch_Handler,
		},
		{
			MethodName: "GetRecommendSkillProduct",
			Handler:    _EsportHall_GetRecommendSkillProduct_Handler,
		},
		{
			MethodName: "GetGlobalRcmdCoach",
			Handler:    _EsportHall_GetGlobalRcmdCoach_Handler,
		},
		{
			MethodName: "SetUserToRealCoach",
			Handler:    _EsportHall_SetUserToRealCoach_Handler,
		},
		{
			MethodName: "GetCoachMinPrice",
			Handler:    _EsportHall_GetCoachMinPrice_Handler,
		},
		{
			MethodName: "GetCoachIncentiveTaskInfo",
			Handler:    _EsportHall_GetCoachIncentiveTaskInfo_Handler,
		},
		{
			MethodName: "AddCoachRecommend",
			Handler:    _EsportHall_AddCoachRecommend_Handler,
		},
		{
			MethodName: "GetCoachRecommend",
			Handler:    _EsportHall_GetCoachRecommend_Handler,
		},
		{
			MethodName: "UpdateCoachRecommend",
			Handler:    _EsportHall_UpdateCoachRecommend_Handler,
		},
		{
			MethodName: "DelCoachRecommend",
			Handler:    _EsportHall_DelCoachRecommend_Handler,
		},
		{
			MethodName: "SearchCoach",
			Handler:    _EsportHall_SearchCoach_Handler,
		},
		{
			MethodName: "AddIgnoreRecommendCoach",
			Handler:    _EsportHall_AddIgnoreRecommendCoach_Handler,
		},
		{
			MethodName: "GetReCoachForUGC",
			Handler:    _EsportHall_GetReCoachForUGC_Handler,
		},
		{
			MethodName: "ReBuildUserFirstRoundCache",
			Handler:    _EsportHall_ReBuildUserFirstRoundCache_Handler,
		},
		{
			MethodName: "GetCoachIncentiveAddition",
			Handler:    _EsportHall_GetCoachIncentiveAddition_Handler,
		},
		{
			MethodName: "SaveUserTopGamePreSelectConfig",
			Handler:    _EsportHall_SaveUserTopGamePreSelectConfig_Handler,
		},
		{
			MethodName: "GetUserTopGamePreSelectConfig",
			Handler:    _EsportHall_GetUserTopGamePreSelectConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/esport-hall/esport-hall.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/esport-hall/esport-hall.proto", fileDescriptor_esport_hall_701db7d7416106bf)
}

var fileDescriptor_esport_hall_701db7d7416106bf = []byte{
	// 5215 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3c, 0x4d, 0x73, 0xdc, 0x56,
	0x72, 0xc4, 0x0c, 0x29, 0xce, 0x34, 0x39, 0x14, 0x05, 0x49, 0xe4, 0x70, 0xa8, 0x2f, 0xc2, 0x92,
	0x4c, 0xcb, 0x32, 0xe5, 0xd0, 0x71, 0xbc, 0xde, 0xd8, 0xce, 0x4a, 0xc3, 0x21, 0x35, 0x59, 0x89,
	0xc3, 0xc5, 0x90, 0x96, 0xed, 0x8d, 0x0d, 0x43, 0x98, 0x47, 0x12, 0x4b, 0x0c, 0x30, 0x02, 0x30,
	0xa4, 0xe9, 0xdd, 0xc4, 0x95, 0x4d, 0x65, 0x6b, 0x37, 0xb5, 0xa7, 0xf8, 0x92, 0xca, 0x25, 0xa7,
	0xad, 0x54, 0xaa, 0xf6, 0x90, 0xca, 0x2d, 0xc7, 0x5c, 0x93, 0x54, 0xa5, 0x72, 0xc8, 0x21, 0x55,
	0x39, 0x6c, 0xe5, 0x9c, 0x5f, 0x90, 0x53, 0xea, 0x7d, 0x60, 0xf0, 0xf0, 0xf0, 0x00, 0x0c, 0x65,
	0xc9, 0x5b, 0x95, 0xdb, 0xe0, 0x7d, 0xf4, 0xeb, 0xd7, 0xdd, 0xaf, 0xbb, 0x5f, 0x77, 0xbf, 0x81,
	0xbb, 0x61, 0x78, 0xef, 0xd9, 0xd0, 0xb6, 0x8e, 0x02, 0xdb, 0x39, 0x46, 0xfe, 0x3d, 0x14, 0x0c,
	0x3c, 0x3f, 0x7c, 0xe3, 0xd0, 0x74, 0x1c, 0xfe, 0xf7, 0xda, 0xc0, 0xf7, 0x42, 0x4f, 0x9d, 0xa1,
	0x4d, 0x06, 0x6e, 0xd2, 0x2c, 0xa8, 0xb7, 0x5d, 0x3b, 0xdc, 0x0b, 0x90, 0xdf, 0x3d, 0xb2, 0x1d,
	0xa7, 0xed, 0xee, 0x7b, 0x3a, 0x7a, 0x36, 0x44, 0x41, 0xa8, 0xce, 0x43, 0x79, 0x68, 0xf7, 0xea,
	0xca, 0x0d, 0x65, 0xb5, 0xa6, 0xe3, 0x9f, 0xea, 0xdb, 0x00, 0x01, 0x1e, 0x65, 0x38, 0x76, 0x10,
	0xd6, 0x4b, 0x37, 0xca, 0xab, 0x33, 0xeb, 0x0b, 0x6b, 0x1c, 0xbc, 0xb5, 0x18, 0x48, 0x95, 0x8c,
	0x7c, 0x64, 0x07, 0xa1, 0xb6, 0x0c, 0x4b, 0x92, 0x45, 0x82, 0x81, 0xe7, 0x06, 0x48, 0xd3, 0x61,
	0x65, 0x0b, 0x85, 0x1f, 0xda, 0x81, 0xfd, 0xd4, 0x41, 0xa4, 0x7b, 0xc7, 0xf7, 0x7a, 0x43, 0x2b,
	0xc4, 0x53, 0xb3, 0x51, 0x59, 0x86, 0xea, 0x89, 0x1d, 0x1e, 0x1a, 0x81, 0xe7, 0x63, 0x4c, 0x94,
	0xd5, 0x8a, 0x5e, 0xc1, 0x0d, 0x5d, 0xcf, 0x0f, 0xb5, 0xa7, 0xa0, 0xe5, 0xc1, 0xa4, 0x2b, 0xab,
	0xef, 0xc1, 0xec, 0x80, 0x36, 0xd3, 0xfd, 0x28, 0x64, 0x3f, 0x4b, 0xe9, 0xfd, 0xb0, 0xc9, 0xfa,
	0xcc, 0x20, 0x86, 0xa2, 0xdd, 0x81, 0x85, 0x2d, 0x14, 0xde, 0x77, 0x9c, 0x6e, 0xb4, 0xcf, 0x4c,
	0x64, 0xb5, 0x27, 0xb0, 0x98, 0x1a, 0xfb, 0x42, 0x90, 0xb0, 0xe0, 0x4a, 0x17, 0x85, 0xa4, 0x5f,
	0x47, 0x16, 0xb2, 0x8f, 0x51, 0xf7, 0xc4, 0x0e, 0xad, 0xc3, 0x6c, 0xba, 0x2d, 0x41, 0x85, 0xb2,
	0xd0, 0xee, 0x11, 0xb2, 0xd5, 0xf4, 0x69, 0xf2, 0xdd, 0xee, 0xa9, 0x0b, 0x70, 0x2e, 0x20, 0xb3,
	0xeb, 0x65, 0x42, 0x4f, 0xf6, 0xa5, 0x5d, 0x87, 0xab, 0x19, 0x8b, 0x30, 0x16, 0xae, 0x41, 0x63,
	0x0b, 0x85, 0xac, 0x6f, 0xd7, 0xee, 0xa3, 0x4d, 0xdf, 0xec, 0xa3, 0x6c, 0x72, 0x9c, 0xc0, 0xb2,
	0x74, 0x3c, 0x23, 0xc9, 0x55, 0x80, 0x20, 0x34, 0xfd, 0xd0, 0x08, 0xed, 0x3e, 0x62, 0x48, 0x56,
	0x49, 0x0b, 0x1e, 0x8b, 0x77, 0x80, 0xdc, 0x1e, 0xed, 0x2c, 0xd3, 0x1d, 0x20, 0xb7, 0x47, 0xba,
	0xae, 0xc1, 0x4c, 0xcf, 0x3c, 0x35, 0xbc, 0x7d, 0xe3, 0x04, 0xa1, 0xa3, 0xfa, 0xe4, 0x8d, 0xf2,
	0x6a, 0x45, 0xaf, 0xf6, 0xcc, 0xd3, 0xce, 0xfe, 0x13, 0x84, 0x8e, 0xb4, 0x9f, 0x2b, 0xd0, 0xe8,
	0x9e, 0x01, 0xd3, 0x97, 0x88, 0xca, 0x55, 0x58, 0xee, 0x66, 0xd3, 0x40, 0xfb, 0x18, 0x2e, 0x45,
	0x34, 0xdf, 0xf1, 0x6d, 0x0b, 0x3d, 0x17, 0x43, 0x2f, 0xc1, 0xd4, 0x00, 0x4f, 0x66, 0xb8, 0xd1,
	0x0f, 0x6d, 0x11, 0x2e, 0x0b, 0xa0, 0xd9, 0x9a, 0x43, 0xc2, 0x46, 0x5e, 0xd8, 0x78, 0x6d, 0x70,
	0x15, 0x20, 0x12, 0xd4, 0x11, 0x02, 0x55, 0xd6, 0xd2, 0x26, 0xe7, 0xd1, 0x76, 0x8f, 0xed, 0x10,
	0xc5, 0x78, 0x54, 0x68, 0x03, 0xed, 0xb4, 0x3c, 0xd3, 0x3a, 0x34, 0x30, 0xee, 0x14, 0x99, 0x0a,
	0x69, 0xd8, 0xb3, 0x7b, 0x9a, 0x4e, 0xa4, 0x21, 0xbd, 0x2c, 0x93, 0x86, 0xb7, 0x60, 0x9a, 0xad,
	0x42, 0x16, 0xcd, 0x3d, 0x1b, 0xd1, 0x48, 0xed, 0x13, 0xa2, 0x00, 0x44, 0x98, 0x0f, 0x4e, 0xb7,
	0xcc, 0x3e, 0x6a, 0xf7, 0xa2, 0x2d, 0x2d, 0xc2, 0xf4, 0x81, 0xd9, 0x47, 0xf1, 0x7e, 0xce, 0x1d,
	0x90, 0xfe, 0x24, 0xbe, 0x25, 0x01, 0xdf, 0x4f, 0xe0, 0x95, 0x5c, 0xd8, 0xdf, 0x04, 0xef, 0x5f,
	0x95, 0x88, 0xa6, 0xc0, 0xa0, 0x9a, 0x78, 0x3d, 0x5e, 0xad, 0x64, 0x62, 0xbb, 0x00, 0xe7, 0xbc,
	0xfd, 0xfd, 0x00, 0x85, 0x0c, 0x55, 0xf6, 0x85, 0xd9, 0xef, 0xd8, 0x7d, 0x3b, 0x8c, 0xd8, 0x4f,
	0x3e, 0xd4, 0x0f, 0xa0, 0x36, 0xf0, 0xbd, 0x01, 0xf2, 0xc3, 0x53, 0xaa, 0x71, 0x26, 0x25, 0x1a,
	0x07, 0x23, 0xb0, 0xc3, 0x46, 0xe9, 0xb3, 0xd1, 0x78, 0x8c, 0x0d, 0x96, 0x03, 0x1f, 0x3d, 0x33,
	0x02, 0x6f, 0xe8, 0x5b, 0xa8, 0x3e, 0x45, 0xe5, 0xc0, 0x47, 0xcf, 0xba, 0xa4, 0x41, 0xbd, 0x0e,
	0x33, 0xe8, 0x8b, 0x81, 0x17, 0x20, 0x23, 0x3c, 0x1d, 0xa0, 0xfa, 0x39, 0xd2, 0x0f, 0xb4, 0x69,
	0xf7, 0x74, 0x80, 0xb4, 0x26, 0x54, 0xf5, 0xd1, 0xe8, 0x06, 0x2c, 0xe8, 0xad, 0x1f, 0x18, 0xdd,
	0xce, 0x9e, 0xde, 0x6c, 0x19, 0x7b, 0xdb, 0xdd, 0x9d, 0x56, 0xb3, 0xbd, 0xd9, 0x6e, 0x6d, 0xcc,
	0x4f, 0x08, 0x7d, 0xad, 0xee, 0x4e, 0x47, 0xdf, 0x35, 0xee, 0xeb, 0xad, 0xfb, 0xf3, 0x8a, 0xe6,
	0xc3, 0xa5, 0x2d, 0x14, 0x72, 0x24, 0x62, 0x44, 0x7f, 0x17, 0x80, 0x32, 0x8e, 0xd3, 0xa5, 0x8d,
	0xd4, 0xce, 0xc8, 0x3c, 0x6a, 0xa4, 0xac, 0x08, 0x04, 0x46, 0xdc, 0x45, 0x5f, 0x84, 0x46, 0x82,
	0x94, 0x80, 0x9b, 0x3a, 0xa4, 0x45, 0x6b, 0xc3, 0xf5, 0x07, 0x66, 0x68, 0x1d, 0xe6, 0x9c, 0x91,
	0xdb, 0x70, 0x3e, 0x3e, 0x23, 0x31, 0x0e, 0x35, 0xbd, 0x36, 0x3a, 0x28, 0x44, 0x6d, 0x7f, 0x0e,
	0x37, 0xb2, 0x41, 0xbd, 0x10, 0xc3, 0xf0, 0x6b, 0x05, 0xae, 0x88, 0x82, 0xf4, 0xe0, 0x74, 0xcf,
	0x2e, 0x96, 0xfd, 0x9b, 0x30, 0x37, 0x92, 0xfd, 0xd8, 0xce, 0xd7, 0xf4, 0xd9, 0xe8, 0x00, 0x10,
	0x6a, 0xc5, 0x32, 0x57, 0x96, 0xcb, 0xdc, 0x24, 0x2f, 0x73, 0x82, 0x50, 0x4c, 0xa5, 0x84, 0xe2,
	0xc7, 0x70, 0x35, 0x03, 0xdb, 0x6f, 0x81, 0xb1, 0x7f, 0xa9, 0x40, 0x5d, 0x47, 0x18, 0x52, 0x8b,
	0x60, 0x44, 0xa0, 0x64, 0x2b, 0xdc, 0x3b, 0x70, 0x81, 0x6d, 0x86, 0xc3, 0x88, 0xd2, 0xe8, 0x3c,
	0x8a, 0x01, 0x90, 0xb5, 0x39, 0x2a, 0x97, 0x13, 0x54, 0x16, 0x28, 0x32, 0x99, 0xa2, 0xc8, 0x32,
	0x2c, 0x49, 0x70, 0x62, 0x9a, 0xfa, 0xdf, 0x14, 0x50, 0xdb, 0x44, 0xb9, 0x76, 0xfc, 0x1e, 0xf2,
	0x73, 0xed, 0x17, 0xd3, 0xca, 0xb1, 0x26, 0x63, 0x7a, 0x7a, 0x8f, 0x76, 0x73, 0x3a, 0xbd, 0x2c,
	0xd1, 0xe9, 0x1e, 0x86, 0x6f, 0x58, 0x6e, 0xc4, 0xd0, 0x0a, 0x69, 0x68, 0xba, 0xa1, 0x5a, 0x87,
	0x69, 0xcb, 0xeb, 0xf7, 0x91, 0x1b, 0x12, 0x7e, 0x56, 0xf5, 0xe8, 0x53, 0x5d, 0x87, 0x2a, 0xdd,
	0xb4, 0xbb, 0xef, 0x11, 0x05, 0x30, 0xb3, 0x7e, 0x39, 0xc5, 0x2a, 0xc2, 0xa5, 0xca, 0x01, 0xfb,
	0xa5, 0xad, 0xc3, 0xc5, 0xc4, 0x86, 0x18, 0xdb, 0x13, 0x56, 0x45, 0x49, 0x5a, 0x15, 0xad, 0x03,
	0xf5, 0x87, 0xa6, 0xdb, 0x73, 0x90, 0x84, 0x14, 0xb9, 0xe6, 0x08, 0x3b, 0x3a, 0xa1, 0x19, 0x0e,
	0x83, 0x88, 0x29, 0xf4, 0x0b, 0xd3, 0x5c, 0x02, 0x90, 0xd1, 0xbc, 0x05, 0x0b, 0x1b, 0xc8, 0x49,
	0x9c, 0x38, 0x91, 0xec, 0x65, 0xb9, 0x4d, 0x2e, 0x73, 0x36, 0x59, 0x5b, 0x82, 0xc5, 0x14, 0x18,
	0xb6, 0xc2, 0xf7, 0x60, 0x66, 0x17, 0xc5, 0xfa, 0x5e, 0x85, 0x49, 0x22, 0x1b, 0x74, 0xdb, 0xe4,
	0x37, 0x61, 0x98, 0xe9, 0x9b, 0x7d, 0xe3, 0x47, 0x81, 0xe7, 0x92, 0x7d, 0x55, 0xf5, 0x2a, 0x69,
	0xf9, 0xc3, 0xc0, 0x73, 0xb5, 0x39, 0x98, 0xa5, 0x10, 0x18, 0xc4, 0xdf, 0x23, 0xa6, 0x95, 0x2a,
	0x73, 0xdb, 0x8a, 0x35, 0x7a, 0x81, 0x0e, 0xd0, 0x3e, 0x1f, 0x29, 0x0f, 0x61, 0x1e, 0x63, 0xcb,
	0xf7, 0x60, 0x8e, 0xf8, 0x12, 0x46, 0x64, 0x19, 0xa4, 0x26, 0x2e, 0x61, 0x44, 0x6a, 0x03, 0x1e,
	0x92, 0xb6, 0x4d, 0xbc, 0x7e, 0x9e, 0x0c, 0xe4, 0xbc, 0x27, 0xed, 0x73, 0x5a, 0x9e, 0x39, 0x8c,
	0x4b, 0x09, 0x8c, 0x7b, 0x29, 0x83, 0x9f, 0x80, 0xc7, 0xf0, 0xfe, 0x00, 0x6a, 0x94, 0x2f, 0x63,
	0x5b, 0xe6, 0xd9, 0x80, 0xfb, 0xd2, 0x9e, 0x42, 0xfd, 0x7e, 0xaf, 0xc7, 0xce, 0x22, 0x15, 0xf7,
	0x11, 0xb2, 0x9b, 0x30, 0xe7, 0x47, 0x6d, 0x54, 0xf4, 0x29, 0xf0, 0xeb, 0x09, 0xe0, 0xc9, 0xb9,
	0xe4, 0x10, 0xd4, 0x7c, 0xfe, 0x13, 0x0b, 0xa1, 0x64, 0x0d, 0xc6, 0xd0, 0xdf, 0x28, 0x50, 0x8f,
	0x0c, 0x5f, 0x0a, 0x83, 0x4c, 0x95, 0x8e, 0x25, 0x29, 0x64, 0x24, 0xab, 0xea, 0xe4, 0xb7, 0x7a,
	0x8b, 0x47, 0x97, 0xc8, 0x19, 0x3d, 0x0b, 0x31, 0x36, 0xbb, 0x4c, 0xe0, 0x38, 0x07, 0x78, 0x32,
	0xcf, 0x01, 0x9e, 0x4a, 0x3a, 0xc0, 0x4b, 0x50, 0x19, 0x98, 0x07, 0xc8, 0x70, 0x87, 0x7d, 0xe6,
	0x05, 0x4c, 0xe3, 0xef, 0xed, 0x61, 0x1f, 0x1f, 0x4e, 0xd2, 0x15, 0xd8, 0x5f, 0xa2, 0xfa, 0x34,
	0x3d, 0x9c, 0xb8, 0xa1, 0x6b, 0x7f, 0x89, 0xb4, 0x53, 0x58, 0x92, 0xec, 0x90, 0x31, 0x30, 0x41,
	0x64, 0xce, 0x14, 0x9c, 0x81, 0xc8, 0x44, 0x2f, 0x5f, 0x82, 0xa9, 0xd0, 0x0b, 0x4d, 0x87, 0x49,
	0x11, 0xfd, 0xd0, 0xbe, 0x2e, 0x81, 0x9a, 0x9e, 0xab, 0xce, 0x41, 0x89, 0x91, 0xb4, 0xaa, 0x97,
	0x72, 0x84, 0x70, 0x44, 0xe7, 0x32, 0x47, 0xe7, 0x06, 0x54, 0x5c, 0xdb, 0x3a, 0x72, 0x4d, 0x46,
	0xbe, 0xaa, 0x3e, 0xfa, 0xc6, 0xc4, 0xa5, 0x26, 0x84, 0xb3, 0x8a, 0xd4, 0x70, 0x11, 0xda, 0xab,
	0x30, 0x49, 0x6e, 0xb7, 0x98, 0x7a, 0x8a, 0x4e, 0x7e, 0x0b, 0xfc, 0x98, 0xce, 0xe3, 0x47, 0x25,
	0xc9, 0x8f, 0x34, 0xc3, 0xab, 0x32, 0x86, 0xb3, 0x33, 0x07, 0xf1, 0x6d, 0x0d, 0xc1, 0xf2, 0xde,
	0xa0, 0x67, 0x86, 0xe8, 0xe5, 0xca, 0xfd, 0x35, 0xb8, 0x22, 0x5f, 0x86, 0x89, 0xfe, 0x1d, 0xa8,
	0x6f, 0x20, 0x47, 0x8e, 0x83, 0xc0, 0x21, 0x7c, 0x86, 0x24, 0x63, 0x19, 0xa0, 0x7f, 0x2d, 0xc3,
	0x2c, 0x7f, 0xc6, 0xb9, 0xd9, 0xb5, 0x7c, 0xfe, 0x4a, 0xef, 0x53, 0xdc, 0xb5, 0x79, 0x92, 0xbf,
	0x36, 0x47, 0x94, 0x9c, 0x8a, 0xb5, 0xd7, 0x3c, 0x94, 0x43, 0xf3, 0x80, 0xf0, 0xb3, 0xaa, 0xe3,
	0x9f, 0xaa, 0x06, 0xb5, 0xbe, 0xf9, 0x85, 0x11, 0x5b, 0x59, 0xca, 0xd1, 0x99, 0xbe, 0xf9, 0x45,
	0x27, 0x32, 0xb4, 0x09, 0x2b, 0x5c, 0x11, 0xac, 0xf0, 0x32, 0x54, 0x87, 0xb6, 0x1b, 0xf2, 0x0c,
	0xad, 0xe0, 0x06, 0xc2, 0xcb, 0x35, 0xb8, 0x78, 0x30, 0x34, 0x7d, 0xd3, 0x0d, 0x11, 0x32, 0x4e,
	0x6c, 0xd7, 0x08, 0xd1, 0x17, 0x61, 0x50, 0x87, 0x1b, 0xe5, 0xd5, 0xaa, 0x7e, 0x61, 0xd4, 0xf5,
	0xc4, 0x76, 0x77, 0x71, 0x87, 0xba, 0x0a, 0xf3, 0x76, 0x60, 0x24, 0xa6, 0xd4, 0x67, 0xc8, 0x9e,
	0xe6, 0xec, 0x60, 0x8b, 0x1b, 0xae, 0xbe, 0x2f, 0x5e, 0x22, 0x66, 0xc9, 0x31, 0xac, 0x27, 0x15,
	0x29, 0xb2, 0x42, 0xdb, 0x73, 0x09, 0xb3, 0x93, 0x77, 0x88, 0x1b, 0x30, 0x6b, 0x07, 0x86, 0x8b,
	0x4e, 0xa8, 0x0b, 0x55, 0xaf, 0x91, 0x45, 0xc0, 0x0e, 0xb6, 0xd1, 0x09, 0xe1, 0x1a, 0x43, 0x85,
	0x04, 0xb5, 0x0c, 0x9f, 0x5e, 0x92, 0xeb, 0x73, 0x11, 0x2a, 0x3f, 0xc0, 0xcd, 0xec, 0xea, 0xac,
	0xfd, 0x85, 0x02, 0xd5, 0x51, 0x54, 0x29, 0x61, 0x79, 0x95, 0xe4, 0x6d, 0x38, 0x85, 0x73, 0xe9,
	0x4c, 0x38, 0xbf, 0x02, 0xb5, 0x1e, 0xda, 0x37, 0x87, 0x4e, 0x68, 0xf0, 0x42, 0x30, 0xcb, 0x1a,
	0x89, 0xa1, 0xd4, 0xfe, 0x5c, 0x81, 0x19, 0x0e, 0x84, 0xba, 0x02, 0xb3, 0x01, 0xfd, 0x34, 0x88,
	0x06, 0xa0, 0x22, 0x3a, 0xc3, 0xda, 0xb6, 0xb1, 0x12, 0x58, 0x84, 0x69, 0x3b, 0x30, 0x7c, 0xd3,
	0x3d, 0x62, 0x61, 0xac, 0x73, 0x76, 0xa0, 0x9b, 0xee, 0x11, 0x71, 0x61, 0x42, 0xd4, 0xa7, 0xb8,
	0x96, 0x09, 0xcf, 0x2a, 0xb8, 0x21, 0xba, 0x85, 0x45, 0x80, 0xed, 0xde, 0x48, 0x2f, 0xb3, 0x95,
	0x7b, 0xda, 0x5f, 0x95, 0xa0, 0x96, 0x70, 0x88, 0x25, 0xb6, 0x94, 0x49, 0x63, 0x29, 0x96, 0x46,
	0xb9, 0x7c, 0x63, 0x3c, 0x02, 0xc3, 0x73, 0x1d, 0xdb, 0x45, 0x4c, 0xc4, 0x2b, 0x76, 0xd0, 0x21,
	0xdf, 0x49, 0xf9, 0x9b, 0x16, 0xe4, 0x6f, 0x09, 0x2a, 0x76, 0x60, 0xd0, 0xfb, 0x40, 0x85, 0x4c,
	0x9c, 0xb6, 0x83, 0x47, 0xe4, 0x46, 0x70, 0x05, 0xc0, 0x0e, 0x0c, 0x2c, 0x92, 0x58, 0x55, 0x55,
	0x23, 0xa8, 0x6d, 0x97, 0xe8, 0x2a, 0x15, 0x26, 0x7d, 0xd3, 0x42, 0x44, 0x0b, 0x95, 0x74, 0xf2,
	0x3b, 0x25, 0x33, 0x33, 0x29, 0x99, 0xb9, 0x09, 0x73, 0x76, 0x60, 0xec, 0xdb, 0x7e, 0x10, 0x1a,
	0xbe, 0x37, 0x74, 0x7b, 0xf5, 0x59, 0x32, 0x66, 0xd6, 0x0e, 0x36, 0x71, 0xa3, 0x8e, 0xdb, 0xb4,
	0x7f, 0x56, 0xa0, 0x12, 0x39, 0xa0, 0xa9, 0xa3, 0xaf, 0xc2, 0x24, 0xe1, 0x13, 0xb3, 0x94, 0x44,
	0x4b, 0xdf, 0x8d, 0xa8, 0x32, 0x45, 0xf4, 0x5a, 0x32, 0xde, 0x49, 0xd8, 0x4e, 0xa4, 0x85, 0x51,
	0x4b, 0x85, 0x49, 0xdb, 0xf2, 0x5c, 0x76, 0xc8, 0xc9, 0x6f, 0xf5, 0x23, 0x58, 0x24, 0x78, 0x0f,
	0x83, 0xd0, 0xeb, 0x23, 0xdf, 0x18, 0x06, 0xc8, 0xe8, 0xa1, 0xd0, 0xb4, 0x1d, 0x42, 0xb2, 0x99,
	0xf5, 0x95, 0x04, 0x4c, 0xbc, 0x21, 0x36, 0x74, 0x2f, 0x40, 0x1b, 0x64, 0xa0, 0x7e, 0xc9, 0x95,
	0xb4, 0x6a, 0x7f, 0x5d, 0x86, 0xea, 0x08, 0x85, 0x98, 0x7f, 0x0a, 0xcf, 0x3f, 0xe2, 0xe4, 0x63,
	0x67, 0x6d, 0xe8, 0xda, 0xe1, 0xc8, 0x67, 0xc4, 0x2d, 0x7b, 0x2e, 0xbd, 0x9b, 0xf5, 0x91, 0x19,
	0x0c, 0x7d, 0x44, 0x14, 0x0c, 0x65, 0x3d, 0xb0, 0x26, 0xac, 0x62, 0x56, 0x60, 0x36, 0x1a, 0x40,
	0x20, 0x50, 0x2b, 0x16, 0x4d, 0x22, 0x30, 0xb0, 0x14, 0xb8, 0x76, 0xc8, 0xdb, 0xb1, 0x0a, 0x6e,
	0x20, 0x52, 0xf0, 0x0e, 0xd4, 0x0f, 0xcd, 0x04, 0x5f, 0x8c, 0x9e, 0x1d, 0x58, 0xde, 0xd0, 0xa5,
	0xa6, 0xad, 0xa2, 0x5f, 0x3e, 0x34, 0x39, 0x0e, 0x6d, 0xb0, 0x4e, 0x7c, 0xd1, 0xe2, 0x27, 0xd1,
	0xad, 0x51, 0x19, 0x3b, 0xbf, 0x3f, 0x1a, 0x4e, 0xb6, 0x8f, 0x91, 0xc4, 0x8b, 0x8c, 0x00, 0x53,
	0x71, 0x9b, 0x39, 0x34, 0x83, 0x11, 0xb8, 0x5b, 0x30, 0x17, 0x75, 0x33, 0x58, 0xcc, 0x00, 0x46,
	0xad, 0x14, 0x12, 0x3e, 0xe7, 0xd1, 0x30, 0xb2, 0x1f, 0x60, 0xe7, 0x9c, 0x35, 0x92, 0x3d, 0xf1,
	0x83, 0x7a, 0x28, 0xb0, 0x88, 0x34, 0x56, 0xe3, 0x41, 0x1b, 0x28, 0xb0, 0xb4, 0x5f, 0x29, 0x70,
	0x49, 0xc6, 0x4b, 0xf5, 0x5d, 0x58, 0xc2, 0x22, 0x90, 0x90, 0x89, 0x11, 0xe6, 0x0a, 0xc1, 0x7c,
	0x61, 0x18, 0x20, 0x6e, 0xee, 0x68, 0x13, 0x77, 0x41, 0x4d, 0x4c, 0xa3, 0x1b, 0xa1, 0x66, 0x6a,
	0x9e, 0x13, 0x11, 0xba, 0x97, 0x9b, 0x30, 0x37, 0x70, 0xcc, 0xd0, 0x78, 0xea, 0xb9, 0xc3, 0xc0,
	0xd8, 0x47, 0x23, 0xa5, 0x85, 0x5b, 0x1f, 0xe0, 0xc6, 0x4d, 0x84, 0xb4, 0x7f, 0x2c, 0xc3, 0x2c,
	0xef, 0xab, 0x8f, 0x75, 0x2a, 0xde, 0x81, 0xca, 0xb1, 0xe9, 0xc4, 0xca, 0x69, 0x66, 0xfd, 0x4a,
	0xa6, 0xf3, 0xff, 0xa1, 0xe9, 0xe8, 0xd3, 0xc7, 0xa6, 0x13, 0xe9, 0xd1, 0x91, 0x1a, 0xe6, 0xee,
	0xbe, 0x23, 0x65, 0x4b, 0xe8, 0x7b, 0x1d, 0x66, 0x02, 0xe4, 0x20, 0x2b, 0x21, 0x52, 0x40, 0x9b,
	0xc8, 0x80, 0x05, 0x38, 0x47, 0x2f, 0xcb, 0x4c, 0x84, 0xd8, 0x97, 0xf6, 0xb5, 0x02, 0xb3, 0x3b,
	0x3c, 0xa4, 0xab, 0xb0, 0xb4, 0xa3, 0x77, 0x76, 0x5a, 0xfa, 0xee, 0xc7, 0xc6, 0xee, 0xc7, 0x3b,
	0x62, 0x90, 0xa9, 0x0e, 0x97, 0x92, 0xdd, 0x5b, 0xad, 0xed, 0x8d, 0x96, 0x3e, 0xaf, 0xa8, 0x8b,
	0x70, 0x31, 0xd9, 0xb3, 0xa3, 0xb7, 0x9b, 0xad, 0xf9, 0x52, 0x7a, 0x4a, 0x73, 0xaf, 0xbb, 0xdb,
	0x79, 0x3c, 0x5f, 0x4e, 0x4f, 0x79, 0x74, 0xff, 0x41, 0xeb, 0xd1, 0xfc, 0xa4, 0xf6, 0x11, 0x40,
	0x37, 0xc6, 0x7d, 0x19, 0x16, 0xbb, 0xad, 0x47, 0xad, 0xe6, 0xae, 0x0c, 0xa1, 0x05, 0x50, 0xf9,
	0xce, 0x6e, 0x7b, 0x7b, 0xeb, 0x51, 0x6b, 0x5e, 0x51, 0x2f, 0xc3, 0x05, 0xbe, 0xfd, 0xf1, 0xde,
	0xa3, 0xdd, 0xf6, 0x7c, 0x49, 0x7b, 0x1b, 0xce, 0x0b, 0x94, 0x1e, 0x87, 0x7b, 0xda, 0x43, 0x92,
	0x37, 0xe0, 0xed, 0x68, 0x51, 0xde, 0x20, 0xf6, 0x72, 0x4a, 0x92, 0xe4, 0x80, 0x0c, 0x12, 0x73,
	0xb7, 0xde, 0x24, 0x77, 0xc9, 0x33, 0x2c, 0xa5, 0x7d, 0x48, 0x82, 0x41, 0xd9, 0x20, 0x39, 0x5c,
	0x94, 0x84, 0xc7, 0x45, 0xfc, 0xe9, 0xa1, 0xd3, 0x33, 0xbc, 0x01, 0x72, 0x19, 0x9e, 0x55, 0xd2,
	0xd2, 0x19, 0x20, 0x57, 0xfb, 0x00, 0x56, 0xa2, 0xa8, 0x5b, 0x36, 0x3a, 0x4b, 0x50, 0x19, 0x26,
	0x63, 0x77, 0xd3, 0x43, 0x1a, 0xf3, 0xd2, 0xfe, 0x49, 0x01, 0x2d, 0x0f, 0x00, 0xc3, 0xee, 0x53,
	0x00, 0x8a, 0x8f, 0xd1, 0x37, 0x07, 0xec, 0x7e, 0xf2, 0x41, 0xe2, 0x6c, 0x14, 0x03, 0x59, 0xa3,
	0x9f, 0x8f, 0xcd, 0x41, 0xcb, 0x0d, 0xfd, 0x53, 0xbd, 0x1a, 0x44, 0xdf, 0x8d, 0xf7, 0x60, 0x2e,
	0xd9, 0x89, 0x29, 0x78, 0x84, 0x4e, 0x23, 0x0a, 0x1e, 0xa1, 0x53, 0x6c, 0x08, 0x8e, 0x4d, 0x67,
	0x88, 0x18, 0x0d, 0xe8, 0xc7, 0x77, 0x4b, 0xdf, 0x51, 0xb4, 0xb7, 0x60, 0xe1, 0xa1, 0x19, 0x6c,
	0x9a, 0x7d, 0x6f, 0x18, 0xec, 0x38, 0xe6, 0x69, 0x1c, 0x31, 0xc9, 0xf6, 0x9c, 0xb4, 0xd7, 0x61,
	0x31, 0x35, 0x89, 0x6d, 0x76, 0x1e, 0xca, 0x16, 0x53, 0x5a, 0x35, 0x1d, 0xff, 0x64, 0x29, 0x29,
	0xde, 0x5b, 0x7c, 0x69, 0x29, 0x29, 0xd9, 0x22, 0x5c, 0x84, 0xac, 0x45, 0x48, 0x4e, 0xdc, 0x20,
	0xd3, 0xa7, 0x57, 0xb9, 0xe5, 0x28, 0x34, 0x85, 0x4d, 0x34, 0x75, 0xc6, 0x68, 0x0c, 0x0a, 0x9b,
	0xe9, 0xeb, 0x30, 0x13, 0x7a, 0x03, 0xc3, 0xf2, 0xdc, 0x10, 0xb9, 0x91, 0xa5, 0x84, 0xd0, 0x1b,
	0x34, 0x69, 0x8b, 0xda, 0x86, 0x39, 0x7c, 0xc1, 0x31, 0x92, 0x6e, 0xd9, 0xcc, 0xfa, 0x2b, 0x09,
	0xee, 0xa6, 0x97, 0x6d, 0x87, 0xa8, 0xaf, 0xcf, 0xda, 0xec, 0x57, 0x14, 0x18, 0xb4, 0x4c, 0xbf,
	0x17, 0x3b, 0x6f, 0xe7, 0xf0, 0x67, 0x3b, 0x71, 0xf9, 0x98, 0x4a, 0x44, 0x38, 0x1e, 0xc2, 0x82,
	0x1c, 0x32, 0xb9, 0xcc, 0xda, 0xa1, 0x13, 0x79, 0x97, 0xf4, 0x83, 0xc6, 0xe7, 0xf8, 0x9d, 0x44,
	0x9f, 0xda, 0xbb, 0x70, 0x6d, 0x0b, 0x85, 0x49, 0x60, 0x4d, 0xcf, 0xdd, 0xb7, 0x0f, 0x0a, 0x03,
	0x43, 0x0e, 0x5c, 0xcf, 0x9c, 0xca, 0x24, 0x22, 0x4d, 0x24, 0xe5, 0x39, 0x89, 0xa4, 0xb5, 0x89,
	0xea, 0x48, 0x0f, 0xcd, 0x8d, 0x0f, 0x45, 0x64, 0x2d, 0xf1, 0x64, 0xd5, 0xfe, 0x4c, 0x21, 0x4a,
	0x45, 0x06, 0xeb, 0x85, 0xe3, 0x9d, 0x1d, 0xa5, 0xfa, 0x6f, 0x05, 0x96, 0x9b, 0x3e, 0x32, 0x43,
	0x94, 0x84, 0x73, 0xf6, 0x80, 0x17, 0xbf, 0xd3, 0x72, 0x42, 0x80, 0xd2, 0xfb, 0x98, 0x7c, 0xde,
	0x7d, 0x5c, 0x86, 0x73, 0xfd, 0xe0, 0x20, 0x12, 0xc5, 0x49, 0x7d, 0xaa, 0x1f, 0x1c, 0xb4, 0x49,
	0xd4, 0x38, 0x34, 0xfd, 0x03, 0x14, 0x92, 0xa0, 0x32, 0x8d, 0xed, 0x54, 0x69, 0xcb, 0x9e, 0xdd,
	0xc3, 0x17, 0x79, 0xf9, 0x1e, 0xd9, 0xd1, 0xc4, 0x44, 0xa0, 0x37, 0xfd, 0xff, 0xdf, 0x44, 0x90,
	0xef, 0x31, 0x61, 0x15, 0x93, 0x9d, 0xf9, 0x35, 0x04, 0x9f, 0x4a, 0x04, 0x58, 0xa8, 0x24, 0xa8,
	0x12, 0x62, 0x64, 0x86, 0xc5, 0x24, 0xc2, 0x5f, 0xb1, 0x18, 0x14, 0x0d, 0xc1, 0x52, 0x17, 0xb9,
	0xbd, 0x33, 0xb0, 0x44, 0x7a, 0xd0, 0x04, 0xba, 0x94, 0x45, 0xba, 0x5c, 0x81, 0x86, 0x6c, 0x19,
	0x46, 0x95, 0x87, 0xb0, 0xbc, 0x81, 0x1c, 0x74, 0x26, 0xc9, 0x90, 0x9f, 0xf7, 0x6b, 0x70, 0x45,
	0x0e, 0x89, 0xad, 0xf4, 0x39, 0x29, 0x04, 0x88, 0x2f, 0x1d, 0x45, 0x36, 0x2a, 0x4f, 0x04, 0xf1,
	0xed, 0x18, 0x3b, 0x1c, 0xe5, 0xe8, 0xfa, 0x4e, 0xbc, 0x0d, 0x9a, 0xe0, 0x4f, 0xaf, 0xc0, 0x10,
	0x78, 0x0c, 0x37, 0xb6, 0xf8, 0xee, 0xc8, 0xc3, 0x2f, 0xd4, 0x6f, 0x72, 0xcd, 0xf2, 0x0b, 0x85,
	0x04, 0xd4, 0xb3, 0xe0, 0x31, 0x11, 0xc1, 0x6e, 0xf5, 0xa1, 0x77, 0x62, 0x24, 0xbc, 0x27, 0xc0,
	0x4d, 0x14, 0x3b, 0xf9, 0x95, 0xab, 0x24, 0xbf, 0x72, 0x65, 0xee, 0xfc, 0x36, 0xdc, 0x94, 0xa2,
	0x82, 0x99, 0xc0, 0xc9, 0xb8, 0x66, 0x42, 0x43, 0x3e, 0x88, 0x98, 0xea, 0xcc, 0x68, 0xf6, 0x19,
	0x70, 0xd4, 0xfa, 0x70, 0xab, 0x00, 0x15, 0x46, 0x99, 0x0d, 0xe6, 0x18, 0x70, 0x87, 0xe7, 0xd5,
	0xc4, 0xe1, 0xc9, 0xc6, 0x94, 0x7a, 0x10, 0xe4, 0x10, 0xfd, 0x8d, 0x02, 0x37, 0x9a, 0x87, 0xc8,
	0x3a, 0x8a, 0x47, 0xd3, 0x24, 0x92, 0x7d, 0x70, 0x18, 0xf2, 0x85, 0x14, 0xc4, 0x89, 0x32, 0x86,
	0x5c, 0x21, 0x05, 0x69, 0xd9, 0xb3, 0xf3, 0x6b, 0x0f, 0xb2, 0xf3, 0x89, 0xb7, 0xe1, 0xbc, 0x85,
	0x17, 0x36, 0x7a, 0xe6, 0xa9, 0x11, 0x67, 0x60, 0x2b, 0x7a, 0x8d, 0x34, 0x6f, 0x98, 0xa7, 0x24,
	0xee, 0xa2, 0x75, 0x60, 0x25, 0x07, 0x41, 0x46, 0x0c, 0x29, 0x85, 0x15, 0x39, 0x85, 0x87, 0xc4,
	0x99, 0xf8, 0xb6, 0xf7, 0xab, 0xfd, 0xbb, 0x42, 0x3c, 0x91, 0x17, 0xb5, 0x8d, 0xdc, 0x20, 0x45,
	0x29, 0x2f, 0x48, 0xd1, 0x06, 0x2d, 0xf4, 0x30, 0xd1, 0x2d, 0xd3, 0x25, 0x81, 0x1d, 0x29, 0x08,
	0x7a, 0x40, 0xae, 0x92, 0x91, 0x4d, 0xd3, 0xdd, 0x0b, 0x50, 0x1a, 0x94, 0xe6, 0xc3, 0x72, 0xd3,
	0x41, 0xa6, 0x2f, 0x6e, 0xea, 0x65, 0xd2, 0x11, 0x1b, 0x6b, 0xe9, 0x9a, 0x4c, 0x4d, 0xfd, 0x89,
	0xc0, 0xde, 0x47, 0xe6, 0x53, 0xe4, 0x24, 0x0a, 0x09, 0x0a, 0xd0, 0x1a, 0xaf, 0x9c, 0x20, 0x13,
	0xbf, 0xff, 0x10, 0xf9, 0xcc, 0x23, 0xc0, 0xf8, 0xfc, 0x04, 0xaa, 0x0e, 0x6e, 0xe5, 0xee, 0x5b,
	0xdf, 0x4d, 0xc6, 0x22, 0xf2, 0x01, 0xac, 0x91, 0xa6, 0xd1, 0x5d, 0xab, 0xe2, 0xb0, 0xcf, 0xc6,
	0xc7, 0x50, 0x4b, 0x74, 0x49, 0x6e, 0x5a, 0xeb, 0xfc, 0x4d, 0x4b, 0x8c, 0x81, 0x08, 0x8b, 0xf2,
	0xf7, 0xb0, 0xcf, 0x04, 0xf5, 0xcf, 0xb0, 0x62, 0x45, 0x76, 0xdf, 0x98, 0xe1, 0xda, 0x7f, 0x8a,
	0xf6, 0x20, 0xb9, 0x00, 0xa3, 0xdc, 0xc7, 0x69, 0xca, 0xbd, 0x57, 0x48, 0xb9, 0x04, 0x88, 0xdf,
	0x06, 0xed, 0xfe, 0x45, 0x81, 0xf3, 0x42, 0xf7, 0xcb, 0x51, 0xaa, 0x79, 0x4a, 0x61, 0xf2, 0xcc,
	0x91, 0xcb, 0x29, 0xb9, 0x02, 0x35, 0x89, 0x9f, 0xc0, 0xc5, 0xfa, 0x5e, 0xbc, 0x2b, 0x72, 0x8d,
	0x5c, 0xc9, 0x25, 0x4b, 0xb0, 0x43, 0x4e, 0x93, 0xf1, 0x92, 0x70, 0xe3, 0x73, 0x3a, 0x23, 0xff,
	0xa0, 0x90, 0x6c, 0x7c, 0x26, 0xc0, 0x71, 0xbd, 0x91, 0x97, 0x10, 0xec, 0xe4, 0x89, 0x34, 0x99,
	0x20, 0xd2, 0xfb, 0x4c, 0x13, 0x72, 0x58, 0x9f, 0x41, 0xfd, 0x6a, 0xd7, 0xe1, 0x6a, 0xc6, 0x74,
	0x46, 0xe4, 0xff, 0x4d, 0x46, 0x83, 0xe3, 0xa8, 0xfd, 0x4b, 0x11, 0xdd, 0x77, 0x61, 0x09, 0x8b,
	0xae, 0x3c, 0xc4, 0x4c, 0x37, 0xbe, 0x70, 0x68, 0x06, 0xe3, 0x87, 0x98, 0xa7, 0xc6, 0xa6, 0xfa,
	0x39, 0x49, 0x88, 0xf9, 0x2b, 0xa2, 0xc5, 0xc5, 0xed, 0x7f, 0x8b, 0x76, 0xe4, 0xbf, 0x14, 0xa2,
	0x70, 0x33, 0x30, 0x60, 0x02, 0xf9, 0x11, 0xd0, 0xbc, 0x08, 0xa7, 0x0e, 0x7f, 0x5f, 0x54, 0x87,
	0xb9, 0x10, 0x68, 0x3a, 0x28, 0xd6, 0x86, 0x03, 0xf6, 0xd9, 0xf8, 0x0c, 0x6a, 0x89, 0x2e, 0x89,
	0x36, 0x7c, 0x27, 0xa9, 0x0d, 0x33, 0x53, 0x42, 0x5c, 0xc6, 0x29, 0x56, 0x89, 0x86, 0x78, 0x82,
	0x19, 0x6e, 0x2f, 0xcc, 0x9e, 0xfc, 0x26, 0x75, 0xa4, 0x93, 0x2b, 0x30, 0x0a, 0x7e, 0x92, 0xa6,
	0xe0, 0xfb, 0xc5, 0x14, 0x4c, 0x5a, 0x94, 0xdf, 0x16, 0x0d, 0x3b, 0x51, 0x55, 0x3a, 0xad, 0x17,
	0x10, 0x2a, 0xbd, 0xb2, 0xef, 0x23, 0x8b, 0x30, 0xed, 0xa3, 0x67, 0xa4, 0xce, 0x85, 0xa9, 0x41,
	0x1f, 0x3d, 0xdb, 0x1e, 0xf6, 0xb5, 0x03, 0x72, 0xc7, 0xcf, 0x00, 0x18, 0x0c, 0xd4, 0x2d, 0x50,
	0x13, 0xd5, 0x48, 0x63, 0xd6, 0x79, 0xce, 0x07, 0xc2, 0x83, 0x06, 0xed, 0x67, 0x0a, 0xbe, 0xbc,
	0x93, 0xf7, 0x15, 0xbb, 0x9e, 0x8e, 0x4c, 0xa7, 0xa0, 0x82, 0x71, 0x05, 0x66, 0xe3, 0x5c, 0xe4,
	0x88, 0xd9, 0x33, 0x51, 0x5b, 0x4a, 0x18, 0xca, 0xd9, 0x5a, 0x67, 0x32, 0x71, 0xca, 0xc8, 0xed,
	0x3e, 0x8d, 0x07, 0xd3, 0x80, 0x7f, 0xaa, 0x80, 0xda, 0x45, 0xa6, 0x6f, 0x1d, 0x26, 0xf0, 0xfb,
	0x36, 0x2b, 0x51, 0xb5, 0x67, 0x70, 0x31, 0x81, 0xc2, 0xb7, 0x50, 0x5e, 0xfa, 0xb7, 0x0a, 0x5c,
	0xbb, 0xdf, 0xeb, 0xb5, 0x0f, 0x5c, 0xcf, 0x47, 0x23, 0x69, 0x28, 0x60, 0xd1, 0x12, 0x50, 0x72,
	0x73, 0x31, 0x71, 0xf2, 0xdd, 0x26, 0x79, 0x7b, 0x6b, 0xc4, 0x14, 0xfc, 0x53, 0xdd, 0x84, 0xaa,
	0x37, 0x40, 0xbe, 0x19, 0xda, 0x1e, 0xb5, 0x6a, 0x73, 0xeb, 0xab, 0x09, 0xe4, 0xa3, 0xb5, 0xc9,
	0xa2, 0x1d, 0x77, 0x6f, 0xab, 0xd9, 0x89, 0xc6, 0xeb, 0xf1, 0x54, 0x6d, 0x05, 0xae, 0x67, 0x22,
	0xca, 0x78, 0xf8, 0x6b, 0x85, 0x14, 0xa8, 0x33, 0x50, 0x9b, 0x9e, 0xbf, 0xb7, 0xd5, 0x7c, 0x0e,
	0x57, 0x25, 0xbd, 0x87, 0x6f, 0x5a, 0x96, 0x3e, 0x62, 0xf7, 0x14, 0xcf, 0xee, 0x3d, 0x52, 0x2e,
	0x27, 0x60, 0xfb, 0x8d, 0x79, 0xae, 0xfd, 0x90, 0x10, 0x81, 0x74, 0x3d, 0xb6, 0xdd, 0x82, 0x07,
	0x1a, 0x6b, 0x70, 0xd1, 0x76, 0x2d, 0x67, 0xd8, 0x4b, 0xdc, 0x0d, 0xd9, 0xad, 0xf2, 0x02, 0xeb,
	0xe2, 0x0a, 0x14, 0xde, 0x89, 0x4b, 0xfc, 0x62, 0xe0, 0x71, 0x3d, 0x6c, 0xdf, 0x76, 0x13, 0x57,
	0xd9, 0x4a, 0x9f, 0x0d, 0xd2, 0xbe, 0x56, 0xe0, 0x42, 0xdb, 0xb5, 0x90, 0x1b, 0xda, 0xc7, 0x68,
	0xd7, 0x0c, 0x8e, 0xb6, 0xbd, 0x1e, 0x99, 0x12, 0x9a, 0xc1, 0x11, 0x5f, 0x7f, 0x52, 0xc1, 0x0d,
	0xa4, 0xf8, 0xa4, 0x01, 0x95, 0x81, 0xef, 0x1d, 0xf8, 0x28, 0x08, 0x58, 0x96, 0x60, 0xf4, 0x8d,
	0x0f, 0x16, 0x8d, 0xdb, 0xb1, 0x7a, 0x36, 0xf6, 0xa5, 0xaa, 0x30, 0xc9, 0xd5, 0x01, 0x90, 0xdf,
	0xac, 0xd2, 0xc3, 0x47, 0xa6, 0x75, 0x48, 0x18, 0x40, 0x2a, 0x3d, 0x74, 0xfc, 0xa9, 0xfd, 0x52,
	0x81, 0x5a, 0x02, 0x2b, 0xbc, 0xa8, 0xd9, 0xeb, 0xd9, 0x44, 0x5a, 0xd9, 0x1e, 0xa2, 0xef, 0x04,
	0xa0, 0x52, 0x02, 0x90, 0xba, 0x01, 0x73, 0x74, 0x23, 0x5e, 0x0f, 0xf1, 0xd9, 0x97, 0x6b, 0x49,
	0x51, 0x17, 0x09, 0xa0, 0xcf, 0x86, 0xec, 0x17, 0x61, 0x9d, 0x46, 0xfc, 0x00, 0xc6, 0x55, 0x6e,
	0x28, 0xe7, 0xea, 0x6a, 0x3f, 0xa5, 0x97, 0xa7, 0xac, 0x41, 0x8c, 0x17, 0x37, 0x61, 0xce, 0x31,
	0x83, 0xd0, 0x38, 0xb6, 0xd1, 0x09, 0x2d, 0x63, 0xa1, 0x9b, 0x99, 0xc5, 0xad, 0x1f, 0xda, 0xe8,
	0x84, 0x94, 0xb2, 0xbc, 0xc3, 0xc8, 0xcf, 0x55, 0x1c, 0x35, 0xb2, 0x11, 0xa6, 0xac, 0x21, 0x88,
	0x7e, 0x00, 0x2b, 0x3a, 0x7a, 0x30, 0xb4, 0x9d, 0x1e, 0xd6, 0xa7, 0xb1, 0x7c, 0x34, 0x4d, 0xeb,
	0x10, 0x8d, 0x91, 0xad, 0xbc, 0x09, 0x5a, 0xde, 0x7c, 0x76, 0x9e, 0x37, 0x49, 0xb5, 0xe5, 0x96,
	0xe3, 0x3d, 0x35, 0x1d, 0xdd, 0xea, 0x17, 0xa9, 0xa5, 0x4c, 0x97, 0xff, 0xef, 0x15, 0xf2, 0x78,
	0x28, 0x05, 0x28, 0x96, 0xdb, 0xd8, 0x9c, 0x28, 0x82, 0x39, 0x79, 0x1b, 0x16, 0x19, 0x41, 0x7c,
	0xab, 0xdf, 0x33, 0x82, 0xd0, 0x37, 0x43, 0x74, 0x70, 0x1a, 0x2f, 0x72, 0x89, 0x76, 0x63, 0xb0,
	0x5d, 0xd6, 0xd9, 0xc6, 0x1a, 0xe3, 0x0a, 0x3f, 0xcd, 0x47, 0x96, 0xe9, 0x38, 0xec, 0x5d, 0x4a,
	0xec, 0x00, 0xd6, 0xe3, 0xb9, 0x3a, 0x19, 0x41, 0x5f, 0x9e, 0xb4, 0x7b, 0xda, 0x1f, 0xc1, 0xbd,
	0x2d, 0x14, 0x8e, 0x14, 0x61, 0xb2, 0x5a, 0xf0, 0x89, 0x1d, 0x1e, 0x7a, 0x43, 0x56, 0x7c, 0xff,
	0x3c, 0x05, 0xc9, 0xff, 0xa3, 0xc0, 0x9b, 0xe3, 0x83, 0x67, 0x64, 0xf2, 0xa5, 0x6f, 0x3e, 0x3a,
	0xa2, 0x07, 0x75, 0x26, 0xa0, 0x6b, 0x5d, 0xbb, 0x3f, 0x70, 0x50, 0xf4, 0xc4, 0x24, 0x44, 0xfd,
	0xc4, 0x4b, 0x91, 0xc6, 0x06, 0x5c, 0x48, 0x8d, 0x28, 0x7a, 0xec, 0xc5, 0xe8, 0x50, 0x8a, 0xb3,
	0x13, 0xbf, 0x2b, 0x39, 0x56, 0xf7, 0xd9, 0xa1, 0xce, 0xce, 0x69, 0xfc, 0x81, 0xe4, 0x9c, 0xc5,
	0xb3, 0x18, 0x51, 0x72, 0xd4, 0x85, 0xf6, 0x43, 0xb8, 0xd5, 0x35, 0x8f, 0x11, 0xf5, 0x38, 0x06,
	0xd4, 0x3c, 0x20, 0x5a, 0x6a, 0x91, 0xcc, 0x68, 0xa6, 0x39, 0x77, 0x03, 0x66, 0x19, 0xe7, 0x78,
	0xdf, 0x02, 0x28, 0xfb, 0xc8, 0x09, 0x5a, 0x85, 0xdb, 0x45, 0xc0, 0xd9, 0x29, 0xfa, 0x0e, 0x89,
	0x78, 0x3f, 0x07, 0x16, 0x5a, 0x9b, 0x04, 0xa8, 0x8b, 0x97, 0x48, 0xa1, 0xab, 0x88, 0xe8, 0xde,
	0xf9, 0x31, 0xcc, 0xe9, 0xad, 0x66, 0xe7, 0xf1, 0xe3, 0xd6, 0xf6, 0x06, 0x29, 0x12, 0xa1, 0x2f,
	0xa8, 0xf8, 0x16, 0x63, 0x6f, 0xfb, 0xfb, 0xdb, 0x9d, 0x27, 0xdb, 0xf3, 0x13, 0xea, 0x12, 0x5c,
	0x16, 0xfa, 0x5a, 0x9b, 0x9b, 0xad, 0xe6, 0x2e, 0xad, 0x7c, 0x11, 0xba, 0x9e, 0xdc, 0x6f, 0xef,
	0xce, 0x97, 0x64, 0x73, 0x3e, 0xda, 0x69, 0xeb, 0xad, 0xf9, 0xf2, 0x9d, 0x9f, 0xc0, 0x72, 0x8e,
	0x93, 0xa1, 0xbe, 0x09, 0x77, 0xdb, 0x5b, 0xdb, 0x1d, 0xbd, 0x65, 0xe8, 0x2d, 0xa3, 0xd9, 0xb9,
	0xdf, 0x7c, 0x68, 0x74, 0xb6, 0x8d, 0xbd, 0xad, 0xa6, 0xd1, 0xd9, 0x69, 0xe9, 0xf7, 0x77, 0xdb,
	0xf8, 0x2b, 0x51, 0x07, 0xf3, 0x3a, 0xbc, 0x3a, 0xc6, 0x8c, 0x47, 0xed, 0xef, 0xb7, 0xe6, 0x95,
	0xf5, 0x5f, 0xbe, 0x0d, 0x40, 0x13, 0x3d, 0x0f, 0x4d, 0xc7, 0x51, 0x7b, 0xd8, 0x0e, 0x0a, 0xef,
	0x8d, 0xd5, 0x5b, 0x82, 0xd6, 0x95, 0x3f, 0x7a, 0x6e, 0xdc, 0x2e, 0x1a, 0xc6, 0x58, 0x3e, 0xa1,
	0x7e, 0x45, 0x34, 0x5e, 0xc6, 0x23, 0x63, 0x75, 0x4d, 0x3c, 0xb4, 0xf9, 0x2f, 0x9c, 0x1b, 0xf7,
	0xc6, 0x1e, 0x3f, 0x42, 0xe0, 0x33, 0x38, 0x2f, 0xbc, 0x2a, 0x56, 0x5f, 0x11, 0xa1, 0x48, 0xde,
	0x27, 0x37, 0x6e, 0xe6, 0x0f, 0x1a, 0xc1, 0xff, 0x3b, 0x05, 0x56, 0xc7, 0xd5, 0x36, 0xea, 0x7b,
	0xcf, 0xa9, 0xa4, 0x28, 0x4a, 0xef, 0x7f, 0x23, 0x15, 0xa7, 0x4d, 0xa8, 0x83, 0xf8, 0x51, 0x6b,
	0xa2, 0xa0, 0x46, 0x7d, 0x4d, 0x28, 0xef, 0xcd, 0x7e, 0x2c, 0xdd, 0xb8, 0x33, 0xce, 0xd0, 0xd1,
	0x8a, 0x3f, 0x82, 0x8b, 0x92, 0x47, 0xcc, 0xea, 0xab, 0xe2, 0x4e, 0x32, 0x1e, 0x1b, 0x37, 0x56,
	0x8b, 0x07, 0xf2, 0x6b, 0x75, 0x0b, 0xd7, 0xea, 0x8e, 0xbb, 0x56, 0x37, 0x77, 0xad, 0x8f, 0xa0,
	0x96, 0x78, 0x1e, 0xac, 0xae, 0x48, 0xc9, 0xc2, 0x3b, 0xbd, 0x0d, 0x2d, 0x6f, 0x88, 0xb0, 0x0b,
	0x31, 0x23, 0x9a, 0xde, 0x45, 0x46, 0x56, 0x36, 0xbd, 0x8b, 0xcc, 0xe4, 0xea, 0x84, 0xfa, 0x13,
	0xe2, 0xd7, 0xc8, 0xd3, 0xa1, 0xea, 0x1b, 0xd9, 0x31, 0x6e, 0x49, 0xe4, 0xb3, 0xb1, 0x36, 0xee,
	0xf0, 0xd1, 0xea, 0x3f, 0xa7, 0xd5, 0x26, 0xd9, 0x79, 0x47, 0xf5, 0x77, 0x8a, 0x61, 0x0a, 0xe9,
	0xd2, 0xc6, 0xfa, 0x59, 0xa6, 0xf0, 0x84, 0xc8, 0x4c, 0xf8, 0x09, 0x84, 0x28, 0xca, 0x5c, 0x0a,
	0x84, 0x28, 0xcc, 0x23, 0x6a, 0x13, 0xea, 0x31, 0xb9, 0x28, 0x49, 0xd7, 0x7e, 0x3d, 0x7b, 0x3b,
	0xe9, 0x95, 0xef, 0x8e, 0x37, 0x78, 0xb4, 0x6e, 0x1f, 0x2e, 0xc9, 0xd2, 0x5a, 0x6a, 0x52, 0x84,
	0x72, 0xb2, 0x6d, 0x8d, 0xd7, 0xc6, 0x18, 0x99, 0xb9, 0xcd, 0x38, 0xc7, 0x94, 0xb7, 0xcd, 0x54,
	0x2e, 0x2d, 0x6f, 0x9b, 0xe9, 0xb4, 0x95, 0x44, 0xca, 0xf9, 0x0c, 0x4d, 0x9e, 0x94, 0x4b, 0xb2,
	0x4d, 0x79, 0x52, 0x2e, 0x4b, 0xfc, 0x50, 0x22, 0xcb, 0xd2, 0x0a, 0x6a, 0xea, 0x9c, 0x66, 0x25,
	0x37, 0x1a, 0xaf, 0x8d, 0x31, 0x52, 0xb0, 0xb7, 0x19, 0x49, 0x85, 0xb4, 0xbd, 0xcd, 0x4f, 0x67,
	0xa4, 0xed, 0x6d, 0x41, 0xb6, 0x82, 0xda, 0x18, 0x69, 0x88, 0x5f, 0x95, 0xc8, 0x4a, 0x46, 0x16,
	0x41, 0xb0, 0x31, 0xf9, 0x19, 0x83, 0x09, 0xf5, 0x94, 0x84, 0x02, 0xa4, 0x21, 0x67, 0xf5, 0xee,
	0x98, 0x91, 0x69, 0xba, 0xee, 0x1b, 0x67, 0x8a, 0x63, 0xcb, 0xa8, 0xcd, 0xc7, 0x6a, 0x73, 0xa9,
	0x2d, 0x09, 0x3d, 0xe7, 0x52, 0x5b, 0x16, 0x04, 0x1e, 0xd9, 0x57, 0xf1, 0x79, 0x7c, 0xda, 0xbe,
	0x66, 0xbc, 0xc5, 0x4f, 0xdb, 0xd7, 0xac, 0x97, 0xf6, 0xda, 0x84, 0xfa, 0x53, 0x45, 0xfa, 0x1f,
	0x14, 0xd1, 0x7f, 0x3a, 0xa8, 0xf7, 0x8a, 0x60, 0x09, 0xff, 0x2c, 0xd1, 0x78, 0x73, 0xfc, 0x09,
	0x23, 0x24, 0x3e, 0x85, 0x79, 0xf1, 0x0d, 0xbc, 0x9a, 0x72, 0xd5, 0x64, 0xff, 0x0c, 0xd1, 0x58,
	0x11, 0x47, 0xa5, 0xfe, 0x18, 0x81, 0x4a, 0xaf, 0xf4, 0x89, 0xbd, 0x20, 0xbd, 0x79, 0x7f, 0x1a,
	0x20, 0x48, 0x6f, 0xee, 0x8b, 0x7d, 0x6d, 0x02, 0xbb, 0xe1, 0xa9, 0x27, 0xec, 0x82, 0x1b, 0x9e,
	0xf5, 0xec, 0x5e, 0x70, 0xc3, 0xb3, 0x5f, 0xc2, 0x4f, 0xa8, 0x3a, 0xcc, 0x70, 0xcf, 0xb5, 0xd5,
	0xeb, 0x82, 0xff, 0x2e, 0xbe, 0x0c, 0x6f, 0xdc, 0xc8, 0x1e, 0xc0, 0x63, 0x9e, 0x7a, 0x08, 0x2e,
	0x60, 0x9e, 0xf5, 0xf2, 0x5c, 0xc0, 0x3c, 0xfb, 0x3d, 0x39, 0xf1, 0xdf, 0x85, 0xa7, 0xe0, 0x82,
	0xff, 0x2e, 0x7f, 0x6f, 0x2e, 0xf8, 0xef, 0x59, 0xaf, 0xc9, 0x89, 0xf6, 0xc8, 0xfa, 0x97, 0x09,
	0x41, 0x7b, 0x14, 0xfc, 0xaf, 0x85, 0xa0, 0x3d, 0x8a, 0xfe, 0xba, 0x42, 0x9b, 0x50, 0xdf, 0x87,
	0xc9, 0x5d, 0x7c, 0xe1, 0x4d, 0x3e, 0xae, 0xe3, 0x5e, 0xb7, 0x37, 0x96, 0x24, 0x3d, 0xbc, 0x65,
	0x91, 0xbd, 0x3f, 0x57, 0x57, 0x65, 0xf2, 0x27, 0x7b, 0xda, 0xde, 0x78, 0x6d, 0x8c, 0x91, 0x82,
	0xae, 0xcb, 0x78, 0x3c, 0x9e, 0xd6, 0x75, 0xf9, 0xaf, 0xd6, 0x1b, 0xf7, 0xc6, 0x1e, 0x2f, 0xdc,
	0x5e, 0xd2, 0xcf, 0x01, 0xd2, 0xb7, 0x97, 0xcc, 0x87, 0x0b, 0xe9, 0xdb, 0x4b, 0xce, 0x9b, 0x8c,
	0x48, 0x1b, 0x14, 0xae, 0xb8, 0x35, 0xfe, 0x8a, 0x5b, 0x05, 0x2b, 0x7e, 0x05, 0x8d, 0xec, 0x77,
	0x0f, 0x02, 0x91, 0x0b, 0x9f, 0x69, 0x08, 0x44, 0x2e, 0x7e, 0x50, 0x41, 0x8f, 0x9b, 0xf0, 0x8a,
	0x41, 0x38, 0x6e, 0xf2, 0x87, 0x11, 0xc2, 0x71, 0xcb, 0x78, 0x08, 0x31, 0x22, 0xa9, 0xe4, 0xd1,
	0x41, 0x8a, 0xa4, 0x99, 0x15, 0xed, 0x69, 0x92, 0x66, 0x17, 0xac, 0x33, 0x2f, 0x57, 0x52, 0x69,
	0x2d, 0x7a, 0xb9, 0xd9, 0x05, 0xe7, 0xa2, 0x97, 0x9b, 0x57, 0xb6, 0x4d, 0x96, 0x93, 0xd5, 0x34,
	0x0b, 0xcb, 0xe5, 0x94, 0x76, 0x0b, 0xcb, 0xe5, 0x16, 0x48, 0xcb, 0xe9, 0x49, 0x8c, 0x62, 0x01,
	0x3d, 0x79, 0xcb, 0x78, 0x67, 0x9c, 0xa1, 0xa3, 0x15, 0x0f, 0x40, 0x4d, 0x17, 0x27, 0xab, 0xb7,
	0x85, 0x83, 0x95, 0x51, 0x24, 0xdd, 0x78, 0xb5, 0x70, 0x1c, 0x4f, 0x49, 0x59, 0x75, 0xb2, 0x40,
	0xc9, 0x9c, 0x52, 0x68, 0x81, 0x92, 0xb9, 0xa5, 0xce, 0x91, 0x7a, 0x49, 0xbf, 0x96, 0x49, 0xab,
	0x97, 0xcc, 0x67, 0x3b, 0x69, 0xf5, 0x92, 0xf3, 0xf8, 0x66, 0x42, 0x7d, 0xc6, 0xf2, 0x6e, 0x92,
	0xd4, 0xb7, 0x2a, 0x0b, 0x7c, 0x48, 0x53, 0xee, 0x69, 0x25, 0x9e, 0x99, 0x4b, 0xd7, 0x26, 0x54,
	0x04, 0x6a, 0x3a, 0x01, 0x21, 0x30, 0x2f, 0x33, 0xd5, 0xd1, 0x78, 0xb5, 0x70, 0x1c, 0x8b, 0xc3,
	0x22, 0x2c, 0x23, 0x62, 0x8a, 0x3b, 0x25, 0x23, 0x19, 0xb9, 0xf8, 0x94, 0x8c, 0x64, 0xe5, 0xca,
	0x55, 0x93, 0x38, 0x83, 0x89, 0x24, 0x60, 0xda, 0x19, 0x94, 0x25, 0x20, 0x1b, 0xb7, 0x0a, 0x46,
	0x09, 0x97, 0x47, 0x79, 0x92, 0x2b, 0x7d, 0x79, 0xcc, 0xcd, 0x98, 0xa5, 0x2f, 0x8f, 0xf9, 0xb9,
	0x33, 0xea, 0x62, 0xa5, 0xfe, 0xe6, 0x44, 0x70, 0xb1, 0xb2, 0xfe, 0x6a, 0x45, 0x70, 0xb1, 0xb2,
	0xff, 0x2d, 0x85, 0xac, 0x92, 0xfa, 0x33, 0x11, 0x55, 0x4e, 0xa1, 0x82, 0x55, 0x32, 0xff, 0x93,
	0x84, 0x57, 0x8c, 0xc2, 0x42, 0x32, 0xc5, 0x28, 0x5f, 0xeb, 0xb5, 0x31, 0x46, 0xf2, 0x9b, 0x4a,
	0xfd, 0xbb, 0x85, 0xb0, 0xa9, 0xac, 0x7f, 0xca, 0x10, 0x36, 0x95, 0xfd, 0x27, 0x19, 0xc4, 0xaf,
	0xe6, 0x2a, 0x25, 0x04, 0xbf, 0x3a, 0x5d, 0xc6, 0x21, 0xf8, 0xd5, 0x92, 0x22, 0x0b, 0x6d, 0x42,
	0x0d, 0x61, 0x31, 0xa3, 0xc0, 0x40, 0x88, 0x93, 0xe4, 0xd7, 0x4b, 0x08, 0x71, 0x92, 0x82, 0x9a,
	0x05, 0xd5, 0x20, 0x67, 0x29, 0x51, 0x04, 0x90, 0x3e, 0x4b, 0xb2, 0x8a, 0x86, 0xf4, 0x59, 0x92,
	0x57, 0x12, 0xfc, 0x31, 0x34, 0xb2, 0x53, 0xad, 0x82, 0x6b, 0x53, 0x98, 0xd3, 0x15, 0x5c, 0x9b,
	0xe2, 0x1c, 0xae, 0xfa, 0xa5, 0xe4, 0x20, 0x47, 0x59, 0xb4, 0xa2, 0x83, 0x2c, 0xe4, 0xe8, 0x8a,
	0x0e, 0x72, 0x2a, 0x39, 0xf7, 0x0b, 0x05, 0xae, 0xe5, 0x27, 0xc9, 0xd4, 0x64, 0xdc, 0x72, 0xac,
	0x74, 0x5d, 0xe3, 0xad, 0x33, 0xcd, 0x61, 0xb8, 0xfc, 0x8c, 0x46, 0x5d, 0x73, 0x50, 0x49, 0x45,
	0x5d, 0x8b, 0x31, 0x59, 0x3f, 0xcb, 0x14, 0x8a, 0xc8, 0x83, 0x7b, 0x9f, 0xbc, 0x71, 0xe0, 0x39,
	0xa6, 0x7b, 0xb0, 0xf6, 0xf6, 0x7a, 0x18, 0xae, 0x59, 0x5e, 0xff, 0x1e, 0xf9, 0xeb, 0x5d, 0xcb,
	0x73, 0xee, 0x05, 0xc8, 0x3f, 0xb6, 0x2d, 0x14, 0xdc, 0xe3, 0xc0, 0x3e, 0x3d, 0x47, 0xba, 0xdf,
	0xfa, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x63, 0x4a, 0x90, 0x91, 0xc9, 0x57, 0x00, 0x00,
}
