// Code generated by protoc-gen-gogo.
// source: src/channelol/channelol.proto
// DO NOT EDIT!

/*
	Package channelOL is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/channelol/channelol.proto

	It has these top-level messages:
		ChannelMember
		GetUserChannelReq
		GetUserChannelResp
		GetChannelMemberListReq
		GetChannelMemberListResp
		GetChannelMemberUidListReq
		GetChannelMemberUidListResp
		ChannelMemberListInfo
		BatchGetChannelMemberListReq
		BatchGetChannelMemberListResp
		NotifyPcHelperJoinReq
		NotifyPcHelperJoinResp
		NobilityInfo
		AddChannelMemberReq
		AddChannelMemberResp
		RemoveChannelMemberReq
		RemoveChannelMemberResp
		RemoveChannelReq
		RemoveChannelResp
		MemberFootprint
		GetChannelMemberFootprintReq
		GetChannelMemberFootprintResp
		GetChannelMemberReq
		GetChannelMemberResp
		GetChannelMemberSizeReq
		GetChannelMemberSizeResp
		BatchGetChannelMemberSizeReq
		BatchGetChannelMemberSizeResp
		GetUserChannelIdReq
		UserChannelId
		GetUserChannelIdResp
		BatchGetUserChannelIdReq
		BatchGetUserChannelIdResp
		AddChannelAdminReq
		AddChannelAdminResp
		RemoveChannelAdminReq
		RemoveChannelAdminResp
		GetChannelAdminSizeReq
		GetChannelAdminSizeResp
		CleanChannelAdminReq
		CleanChannelAdminResp
		ChannelStat
		GetChannelStatReq
		GetChannelStatResp
		HoldMicReq
		HoldMicResp
		ReleaseMicReq
		ReleaseMicResp
		MuteChannelMemberReq
		MuteChannelMemberResp
		UnmuteChannelMemberReq
		UnmuteChannelMemberResp
		GetAllChannelOnlineMemberCountReq
		GetAllChannelOnlineMemberCountResp
		GetLiveChannelListReq
		GetLiveChannelListResp
		CheckUserIsChannelAdminReq
		UserAdminRet
		CheckUserIsChannelAdminResp
		ExpireChannelMemberReq
		ExpireChannelMemberResp
*/
package channelOL

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 房间成员
type ChannelOlMemberFlagBitmap int32

const (
	ChannelOlMemberFlagBitmap_CHANNELOL_IS_ADMIN    ChannelOlMemberFlagBitmap = 1
	ChannelOlMemberFlagBitmap_CHANNELOL_IS_HOLDMIC  ChannelOlMemberFlagBitmap = 2
	ChannelOlMemberFlagBitmap_CHANNELOL_IS_MUTE     ChannelOlMemberFlagBitmap = 4
	ChannelOlMemberFlagBitmap_CHANNELOL_IS_PCHELPER ChannelOlMemberFlagBitmap = 8
)

var ChannelOlMemberFlagBitmap_name = map[int32]string{
	1: "CHANNELOL_IS_ADMIN",
	2: "CHANNELOL_IS_HOLDMIC",
	4: "CHANNELOL_IS_MUTE",
	8: "CHANNELOL_IS_PCHELPER",
}
var ChannelOlMemberFlagBitmap_value = map[string]int32{
	"CHANNELOL_IS_ADMIN":    1,
	"CHANNELOL_IS_HOLDMIC":  2,
	"CHANNELOL_IS_MUTE":     4,
	"CHANNELOL_IS_PCHELPER": 8,
}

func (x ChannelOlMemberFlagBitmap) Enum() *ChannelOlMemberFlagBitmap {
	p := new(ChannelOlMemberFlagBitmap)
	*p = x
	return p
}
func (x ChannelOlMemberFlagBitmap) String() string {
	return proto.EnumName(ChannelOlMemberFlagBitmap_name, int32(x))
}
func (x *ChannelOlMemberFlagBitmap) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelOlMemberFlagBitmap_value, data, "ChannelOlMemberFlagBitmap")
	if err != nil {
		return err
	}
	*x = ChannelOlMemberFlagBitmap(value)
	return nil
}
func (ChannelOlMemberFlagBitmap) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{0}
}

type ChannelMember struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IsHoldmic      bool   `protobuf:"varint,2,opt,name=is_holdmic,json=isHoldmic" json:"is_holdmic"`
	IsMute         bool   `protobuf:"varint,3,opt,name=is_mute,json=isMute" json:"is_mute"`
	CostValue      uint32 `protobuf:"varint,4,opt,name=cost_value,json=costValue" json:"cost_value"`
	Ts             uint32 `protobuf:"varint,5,opt,name=ts" json:"ts"`
	IsHavePcHelper bool   `protobuf:"varint,6,opt,name=is_have_pc_helper,json=isHavePcHelper" json:"is_have_pc_helper"`
	IsRobot        bool   `protobuf:"varint,7,opt,name=is_robot,json=isRobot" json:"is_robot"`
	Flag           uint32 `protobuf:"varint,8,opt,name=flag" json:"flag"`
}

func (m *ChannelMember) Reset()                    { *m = ChannelMember{} }
func (m *ChannelMember) String() string            { return proto.CompactTextString(m) }
func (*ChannelMember) ProtoMessage()               {}
func (*ChannelMember) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{0} }

func (m *ChannelMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMember) GetIsHoldmic() bool {
	if m != nil {
		return m.IsHoldmic
	}
	return false
}

func (m *ChannelMember) GetIsMute() bool {
	if m != nil {
		return m.IsMute
	}
	return false
}

func (m *ChannelMember) GetCostValue() uint32 {
	if m != nil {
		return m.CostValue
	}
	return 0
}

func (m *ChannelMember) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *ChannelMember) GetIsHavePcHelper() bool {
	if m != nil {
		return m.IsHavePcHelper
	}
	return false
}

func (m *ChannelMember) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

func (m *ChannelMember) GetFlag() uint32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

// 获取用户当前房间 （ 废弃接口 请使用 GetUserChannelIdReq ）
type GetUserChannelReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserChannelReq) Reset()                    { *m = GetUserChannelReq{} }
func (m *GetUserChannelReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserChannelReq) ProtoMessage()               {}
func (*GetUserChannelReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{1} }

func (m *GetUserChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserChannelResp struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	AppId     uint32 `protobuf:"varint,2,opt,name=app_id,json=appId" json:"app_id"`
	// 房间成员属性 内容数值仅供参考
	MemberInfo *ChannelMember `protobuf:"bytes,3,opt,name=member_info,json=memberInfo" json:"member_info,omitempty"`
}

func (m *GetUserChannelResp) Reset()                    { *m = GetUserChannelResp{} }
func (m *GetUserChannelResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserChannelResp) ProtoMessage()               {}
func (*GetUserChannelResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{2} }

func (m *GetUserChannelResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserChannelResp) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GetUserChannelResp) GetMemberInfo() *ChannelMember {
	if m != nil {
		return m.MemberInfo
	}
	return nil
}

// 获取房间成员列表
type GetChannelMemberListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	StartIdx  uint32 `protobuf:"varint,2,req,name=start_idx,json=startIdx" json:"start_idx"`
	Limit     uint32 `protobuf:"varint,3,req,name=limit" json:"limit"`
}

func (m *GetChannelMemberListReq) Reset()                    { *m = GetChannelMemberListReq{} }
func (m *GetChannelMemberListReq) String() string            { return proto.CompactTextString(m) }
func (*GetChannelMemberListReq) ProtoMessage()               {}
func (*GetChannelMemberListReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{3} }

func (m *GetChannelMemberListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelMemberListReq) GetStartIdx() uint32 {
	if m != nil {
		return m.StartIdx
	}
	return 0
}

func (m *GetChannelMemberListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetChannelMemberListResp struct {
	MemberList []*ChannelMember `protobuf:"bytes,1,rep,name=member_list,json=memberList" json:"member_list,omitempty"`
	AllSize    uint32           `protobuf:"varint,2,req,name=all_size,json=allSize" json:"all_size"`
}

func (m *GetChannelMemberListResp) Reset()         { *m = GetChannelMemberListResp{} }
func (m *GetChannelMemberListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMemberListResp) ProtoMessage()    {}
func (*GetChannelMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{4}
}

func (m *GetChannelMemberListResp) GetMemberList() []*ChannelMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *GetChannelMemberListResp) GetAllSize() uint32 {
	if m != nil {
		return m.AllSize
	}
	return 0
}

// 获取房间成员uid列表
type GetChannelMemberUidListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	StartIdx  uint32 `protobuf:"varint,2,req,name=start_idx,json=startIdx" json:"start_idx"`
	Limit     uint32 `protobuf:"varint,3,req,name=limit" json:"limit"`
}

func (m *GetChannelMemberUidListReq) Reset()         { *m = GetChannelMemberUidListReq{} }
func (m *GetChannelMemberUidListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMemberUidListReq) ProtoMessage()    {}
func (*GetChannelMemberUidListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{5}
}

func (m *GetChannelMemberUidListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelMemberUidListReq) GetStartIdx() uint32 {
	if m != nil {
		return m.StartIdx
	}
	return 0
}

func (m *GetChannelMemberUidListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetChannelMemberUidListResp struct {
	MemberUidList []uint32 `protobuf:"varint,1,rep,name=member_uid_list,json=memberUidList" json:"member_uid_list,omitempty"`
}

func (m *GetChannelMemberUidListResp) Reset()         { *m = GetChannelMemberUidListResp{} }
func (m *GetChannelMemberUidListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMemberUidListResp) ProtoMessage()    {}
func (*GetChannelMemberUidListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{6}
}

func (m *GetChannelMemberUidListResp) GetMemberUidList() []uint32 {
	if m != nil {
		return m.MemberUidList
	}
	return nil
}

// 批量获取一组房间的指定个数成员列表
type ChannelMemberListInfo struct {
	ChannelId  uint32           `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	MemberList []*ChannelMember `protobuf:"bytes,2,rep,name=member_list,json=memberList" json:"member_list,omitempty"`
	AllSize    uint32           `protobuf:"varint,3,req,name=all_size,json=allSize" json:"all_size"`
}

func (m *ChannelMemberListInfo) Reset()                    { *m = ChannelMemberListInfo{} }
func (m *ChannelMemberListInfo) String() string            { return proto.CompactTextString(m) }
func (*ChannelMemberListInfo) ProtoMessage()               {}
func (*ChannelMemberListInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{7} }

func (m *ChannelMemberListInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMemberListInfo) GetMemberList() []*ChannelMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *ChannelMemberListInfo) GetAllSize() uint32 {
	if m != nil {
		return m.AllSize
	}
	return 0
}

type BatchGetChannelMemberListReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	MemberCount   uint32   `protobuf:"varint,2,req,name=member_count,json=memberCount" json:"member_count"`
}

func (m *BatchGetChannelMemberListReq) Reset()         { *m = BatchGetChannelMemberListReq{} }
func (m *BatchGetChannelMemberListReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelMemberListReq) ProtoMessage()    {}
func (*BatchGetChannelMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{8}
}

func (m *BatchGetChannelMemberListReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *BatchGetChannelMemberListReq) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

type BatchGetChannelMemberListResp struct {
	ChannelmemberinfoList []*ChannelMemberListInfo `protobuf:"bytes,1,rep,name=channelmemberinfo_list,json=channelmemberinfoList" json:"channelmemberinfo_list,omitempty"`
}

func (m *BatchGetChannelMemberListResp) Reset()         { *m = BatchGetChannelMemberListResp{} }
func (m *BatchGetChannelMemberListResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelMemberListResp) ProtoMessage()    {}
func (*BatchGetChannelMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{9}
}

func (m *BatchGetChannelMemberListResp) GetChannelmemberinfoList() []*ChannelMemberListInfo {
	if m != nil {
		return m.ChannelmemberinfoList
	}
	return nil
}

// PC助手加入房间(前提是该用户的手机版本已经进入了房间)
type NotifyPcHelperJoinReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *NotifyPcHelperJoinReq) Reset()                    { *m = NotifyPcHelperJoinReq{} }
func (m *NotifyPcHelperJoinReq) String() string            { return proto.CompactTextString(m) }
func (*NotifyPcHelperJoinReq) ProtoMessage()               {}
func (*NotifyPcHelperJoinReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{10} }

func (m *NotifyPcHelperJoinReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NotifyPcHelperJoinReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type NotifyPcHelperJoinResp struct {
}

func (m *NotifyPcHelperJoinResp) Reset()                    { *m = NotifyPcHelperJoinResp{} }
func (m *NotifyPcHelperJoinResp) String() string            { return proto.CompactTextString(m) }
func (*NotifyPcHelperJoinResp) ProtoMessage()               {}
func (*NotifyPcHelperJoinResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{11} }

// 贵族相关信息
type NobilityInfo struct {
	Level     uint32 `protobuf:"varint,1,opt,name=level" json:"level"`
	Value     uint64 `protobuf:"varint,2,opt,name=value" json:"value"`
	KeepValue uint64 `protobuf:"varint,3,opt,name=keep_value,json=keepValue" json:"keep_value"`
	CycleTs   uint32 `protobuf:"varint,4,opt,name=cycle_ts,json=cycleTs" json:"cycle_ts"`
	Invisible bool   `protobuf:"varint,5,opt,name=invisible" json:"invisible"`
}

func (m *NobilityInfo) Reset()                    { *m = NobilityInfo{} }
func (m *NobilityInfo) String() string            { return proto.CompactTextString(m) }
func (*NobilityInfo) ProtoMessage()               {}
func (*NobilityInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{12} }

func (m *NobilityInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *NobilityInfo) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *NobilityInfo) GetKeepValue() uint64 {
	if m != nil {
		return m.KeepValue
	}
	return 0
}

func (m *NobilityInfo) GetCycleTs() uint32 {
	if m != nil {
		return m.CycleTs
	}
	return 0
}

func (m *NobilityInfo) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

//
// move from channelsvr, 2018.06
//
type AddChannelMemberReq struct {
	AppId       uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId   uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType uint32 `protobuf:"varint,3,req,name=channel_type,json=channelType" json:"channel_type"`
	Uid         uint32 `protobuf:"varint,4,req,name=uid" json:"uid"`
	// 下面都是进房携带的附加信息 基本用来做push
	HistoryGiftCost  uint32        `protobuf:"varint,5,opt,name=history_gift_cost,json=historyGiftCost" json:"history_gift_cost"`
	IsMute           bool          `protobuf:"varint,6,opt,name=is_mute,json=isMute" json:"is_mute"`
	IsRobot          bool          `protobuf:"varint,7,opt,name=is_robot,json=isRobot" json:"is_robot"`
	IsPchelper       bool          `protobuf:"varint,8,opt,name=is_pchelper,json=isPchelper" json:"is_pchelper"`
	IsAdmin          bool          `protobuf:"varint,9,opt,name=is_admin,json=isAdmin" json:"is_admin"`
	ChannelDisplayId uint32        `protobuf:"varint,10,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	IsRoomHasPwd     bool          `protobuf:"varint,11,opt,name=is_room_has_pwd,json=isRoomHasPwd" json:"is_room_has_pwd"`
	MarketId         uint32        `protobuf:"varint,12,opt,name=market_id,json=marketId" json:"market_id"`
	NobilityInfo     *NobilityInfo `protobuf:"bytes,13,opt,name=nobility_info,json=nobilityInfo" json:"nobility_info,omitempty"`
	Source           uint32        `protobuf:"varint,14,opt,name=source" json:"source"`
	FollowFriendUid  uint32        `protobuf:"varint,15,opt,name=follow_friend_uid,json=followFriendUid" json:"follow_friend_uid"`
	HideFootprint    bool          `protobuf:"varint,16,opt,name=hide_footprint,json=hideFootprint" json:"hide_footprint"`
	LastChannelId    uint32        `protobuf:"varint,17,opt,name=last_channel_id,json=lastChannelId" json:"last_channel_id"`
	ChannelViewId    string        `protobuf:"bytes,18,opt,name=channel_view_id,json=channelViewId" json:"channel_view_id"`
}

func (m *AddChannelMemberReq) Reset()                    { *m = AddChannelMemberReq{} }
func (m *AddChannelMemberReq) String() string            { return proto.CompactTextString(m) }
func (*AddChannelMemberReq) ProtoMessage()               {}
func (*AddChannelMemberReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{13} }

func (m *AddChannelMemberReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *AddChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddChannelMemberReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *AddChannelMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddChannelMemberReq) GetHistoryGiftCost() uint32 {
	if m != nil {
		return m.HistoryGiftCost
	}
	return 0
}

func (m *AddChannelMemberReq) GetIsMute() bool {
	if m != nil {
		return m.IsMute
	}
	return false
}

func (m *AddChannelMemberReq) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

func (m *AddChannelMemberReq) GetIsPchelper() bool {
	if m != nil {
		return m.IsPchelper
	}
	return false
}

func (m *AddChannelMemberReq) GetIsAdmin() bool {
	if m != nil {
		return m.IsAdmin
	}
	return false
}

func (m *AddChannelMemberReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *AddChannelMemberReq) GetIsRoomHasPwd() bool {
	if m != nil {
		return m.IsRoomHasPwd
	}
	return false
}

func (m *AddChannelMemberReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *AddChannelMemberReq) GetNobilityInfo() *NobilityInfo {
	if m != nil {
		return m.NobilityInfo
	}
	return nil
}

func (m *AddChannelMemberReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AddChannelMemberReq) GetFollowFriendUid() uint32 {
	if m != nil {
		return m.FollowFriendUid
	}
	return 0
}

func (m *AddChannelMemberReq) GetHideFootprint() bool {
	if m != nil {
		return m.HideFootprint
	}
	return false
}

func (m *AddChannelMemberReq) GetLastChannelId() uint32 {
	if m != nil {
		return m.LastChannelId
	}
	return 0
}

func (m *AddChannelMemberReq) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type AddChannelMemberResp struct {
	IsAlreadyIn     bool   `protobuf:"varint,1,opt,name=is_already_in,json=isAlreadyIn" json:"is_already_in"`
	MemberSize      uint32 `protobuf:"varint,2,opt,name=member_size,json=memberSize" json:"member_size"`
	AdminMemberSize uint32 `protobuf:"varint,3,opt,name=admin_member_size,json=adminMemberSize" json:"admin_member_size"`
	ServerMsTs      uint64 `protobuf:"varint,4,opt,name=server_ms_ts,json=serverMsTs" json:"server_ms_ts"`
}

func (m *AddChannelMemberResp) Reset()                    { *m = AddChannelMemberResp{} }
func (m *AddChannelMemberResp) String() string            { return proto.CompactTextString(m) }
func (*AddChannelMemberResp) ProtoMessage()               {}
func (*AddChannelMemberResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{14} }

func (m *AddChannelMemberResp) GetIsAlreadyIn() bool {
	if m != nil {
		return m.IsAlreadyIn
	}
	return false
}

func (m *AddChannelMemberResp) GetMemberSize() uint32 {
	if m != nil {
		return m.MemberSize
	}
	return 0
}

func (m *AddChannelMemberResp) GetAdminMemberSize() uint32 {
	if m != nil {
		return m.AdminMemberSize
	}
	return 0
}

func (m *AddChannelMemberResp) GetServerMsTs() uint64 {
	if m != nil {
		return m.ServerMsTs
	}
	return 0
}

type RemoveChannelMemberReq struct {
	AppId       uint32 `protobuf:"varint,1,req,name=app_id,json=appId" json:"app_id"`
	ChannelId   uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType uint32 `protobuf:"varint,3,req,name=channel_type,json=channelType" json:"channel_type"`
	Uid         uint32 `protobuf:"varint,4,req,name=uid" json:"uid"`
	// 下面都是退房携带的附加信息 基本用来做push
	IsAdmin          bool   `protobuf:"varint,5,opt,name=is_admin,json=isAdmin" json:"is_admin"`
	IsChangeChannel  bool   `protobuf:"varint,6,opt,name=is_change_channel,json=isChangeChannel" json:"is_change_channel"`
	ChannelDisplayId uint32 `protobuf:"varint,7,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	ChannelCreateUid uint32 `protobuf:"varint,8,opt,name=channel_create_uid,json=channelCreateUid" json:"channel_create_uid"`
	// 用来区分是被踢出房还是自己退房
	OpUid         uint32 `protobuf:"varint,9,opt,name=op_uid,json=opUid" json:"op_uid"`
	ChannelViewId string `protobuf:"bytes,10,opt,name=channel_view_id,json=channelViewId" json:"channel_view_id"`
}

func (m *RemoveChannelMemberReq) Reset()                    { *m = RemoveChannelMemberReq{} }
func (m *RemoveChannelMemberReq) String() string            { return proto.CompactTextString(m) }
func (*RemoveChannelMemberReq) ProtoMessage()               {}
func (*RemoveChannelMemberReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{15} }

func (m *RemoveChannelMemberReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetIsAdmin() bool {
	if m != nil {
		return m.IsAdmin
	}
	return false
}

func (m *RemoveChannelMemberReq) GetIsChangeChannel() bool {
	if m != nil {
		return m.IsChangeChannel
	}
	return false
}

func (m *RemoveChannelMemberReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetChannelCreateUid() uint32 {
	if m != nil {
		return m.ChannelCreateUid
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type RemoveChannelMemberResp struct {
	Exist           bool   `protobuf:"varint,1,opt,name=exist" json:"exist"`
	Duration        uint32 `protobuf:"varint,2,opt,name=duration" json:"duration"`
	LeftMemberCount uint32 `protobuf:"varint,3,opt,name=left_member_count,json=leftMemberCount" json:"left_member_count"`
	LeftAdminCount  uint32 `protobuf:"varint,4,opt,name=left_admin_count,json=leftAdminCount" json:"left_admin_count"`
	ServerMsTs      uint64 `protobuf:"varint,5,opt,name=server_ms_ts,json=serverMsTs" json:"server_ms_ts"`
}

func (m *RemoveChannelMemberResp) Reset()         { *m = RemoveChannelMemberResp{} }
func (m *RemoveChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelMemberResp) ProtoMessage()    {}
func (*RemoveChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{16}
}

func (m *RemoveChannelMemberResp) GetExist() bool {
	if m != nil {
		return m.Exist
	}
	return false
}

func (m *RemoveChannelMemberResp) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *RemoveChannelMemberResp) GetLeftMemberCount() uint32 {
	if m != nil {
		return m.LeftMemberCount
	}
	return 0
}

func (m *RemoveChannelMemberResp) GetLeftAdminCount() uint32 {
	if m != nil {
		return m.LeftAdminCount
	}
	return 0
}

func (m *RemoveChannelMemberResp) GetServerMsTs() uint64 {
	if m != nil {
		return m.ServerMsTs
	}
	return 0
}

type RemoveChannelReq struct {
	AppId         uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId     uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ReturnMembers bool   `protobuf:"varint,3,opt,name=return_members,json=returnMembers" json:"return_members"`
}

func (m *RemoveChannelReq) Reset()                    { *m = RemoveChannelReq{} }
func (m *RemoveChannelReq) String() string            { return proto.CompactTextString(m) }
func (*RemoveChannelReq) ProtoMessage()               {}
func (*RemoveChannelReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{17} }

func (m *RemoveChannelReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *RemoveChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemoveChannelReq) GetReturnMembers() bool {
	if m != nil {
		return m.ReturnMembers
	}
	return false
}

type RemoveChannelResp struct {
	Members []uint32 `protobuf:"varint,1,rep,name=members" json:"members,omitempty"`
}

func (m *RemoveChannelResp) Reset()                    { *m = RemoveChannelResp{} }
func (m *RemoveChannelResp) String() string            { return proto.CompactTextString(m) }
func (*RemoveChannelResp) ProtoMessage()               {}
func (*RemoveChannelResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{18} }

func (m *RemoveChannelResp) GetMembers() []uint32 {
	if m != nil {
		return m.Members
	}
	return nil
}

type MemberFootprint struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Timestamp uint32 `protobuf:"varint,2,req,name=timestamp" json:"timestamp"`
}

func (m *MemberFootprint) Reset()                    { *m = MemberFootprint{} }
func (m *MemberFootprint) String() string            { return proto.CompactTextString(m) }
func (*MemberFootprint) ProtoMessage()               {}
func (*MemberFootprint) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{19} }

func (m *MemberFootprint) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MemberFootprint) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type GetChannelMemberFootprintReq struct {
	AppId     uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelMemberFootprintReq) Reset()         { *m = GetChannelMemberFootprintReq{} }
func (m *GetChannelMemberFootprintReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMemberFootprintReq) ProtoMessage()    {}
func (*GetChannelMemberFootprintReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{20}
}

func (m *GetChannelMemberFootprintReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GetChannelMemberFootprintReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMemberFootprintResp struct {
	Footprints []*MemberFootprint `protobuf:"bytes,1,rep,name=footprints" json:"footprints,omitempty"`
}

func (m *GetChannelMemberFootprintResp) Reset()         { *m = GetChannelMemberFootprintResp{} }
func (m *GetChannelMemberFootprintResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMemberFootprintResp) ProtoMessage()    {}
func (*GetChannelMemberFootprintResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{21}
}

func (m *GetChannelMemberFootprintResp) GetFootprints() []*MemberFootprint {
	if m != nil {
		return m.Footprints
	}
	return nil
}

type GetChannelMemberReq struct {
	AppId     uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
}

func (m *GetChannelMemberReq) Reset()                    { *m = GetChannelMemberReq{} }
func (m *GetChannelMemberReq) String() string            { return proto.CompactTextString(m) }
func (*GetChannelMemberReq) ProtoMessage()               {}
func (*GetChannelMemberReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{22} }

func (m *GetChannelMemberReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GetChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetChannelMemberResp struct {
	Member *ChannelMember `protobuf:"bytes,1,opt,name=member" json:"member,omitempty"`
}

func (m *GetChannelMemberResp) Reset()                    { *m = GetChannelMemberResp{} }
func (m *GetChannelMemberResp) String() string            { return proto.CompactTextString(m) }
func (*GetChannelMemberResp) ProtoMessage()               {}
func (*GetChannelMemberResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{23} }

func (m *GetChannelMemberResp) GetMember() *ChannelMember {
	if m != nil {
		return m.Member
	}
	return nil
}

type GetChannelMemberSizeReq struct {
	AppId     uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelMemberSizeReq) Reset()         { *m = GetChannelMemberSizeReq{} }
func (m *GetChannelMemberSizeReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMemberSizeReq) ProtoMessage()    {}
func (*GetChannelMemberSizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{24}
}

func (m *GetChannelMemberSizeReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GetChannelMemberSizeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMemberSizeResp struct {
	Size_ uint32 `protobuf:"varint,1,opt,name=size" json:"size"`
}

func (m *GetChannelMemberSizeResp) Reset()         { *m = GetChannelMemberSizeResp{} }
func (m *GetChannelMemberSizeResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMemberSizeResp) ProtoMessage()    {}
func (*GetChannelMemberSizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{25}
}

func (m *GetChannelMemberSizeResp) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

type BatchGetChannelMemberSizeReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *BatchGetChannelMemberSizeReq) Reset()         { *m = BatchGetChannelMemberSizeReq{} }
func (m *BatchGetChannelMemberSizeReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelMemberSizeReq) ProtoMessage()    {}
func (*BatchGetChannelMemberSizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{26}
}

func (m *BatchGetChannelMemberSizeReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetChannelMemberSizeResp struct {
	MembersizeList []uint32 `protobuf:"varint,1,rep,name=membersize_list,json=membersizeList" json:"membersize_list,omitempty"`
}

func (m *BatchGetChannelMemberSizeResp) Reset()         { *m = BatchGetChannelMemberSizeResp{} }
func (m *BatchGetChannelMemberSizeResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelMemberSizeResp) ProtoMessage()    {}
func (*BatchGetChannelMemberSizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{27}
}

func (m *BatchGetChannelMemberSizeResp) GetMembersizeList() []uint32 {
	if m != nil {
		return m.MembersizeList
	}
	return nil
}

type GetUserChannelIdReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserChannelIdReq) Reset()                    { *m = GetUserChannelIdReq{} }
func (m *GetUserChannelIdReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserChannelIdReq) ProtoMessage()               {}
func (*GetUserChannelIdReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{28} }

func (m *GetUserChannelIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UserChannelId struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	AppId     uint32 `protobuf:"varint,2,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,3,req,name=channel_id,json=channelId" json:"channel_id"`
	Flag      uint32 `protobuf:"varint,4,opt,name=flag" json:"flag"`
}

func (m *UserChannelId) Reset()                    { *m = UserChannelId{} }
func (m *UserChannelId) String() string            { return proto.CompactTextString(m) }
func (*UserChannelId) ProtoMessage()               {}
func (*UserChannelId) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{29} }

func (m *UserChannelId) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserChannelId) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *UserChannelId) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserChannelId) GetFlag() uint32 {
	if m != nil {
		return m.Flag
	}
	return 0
}

type GetUserChannelIdResp struct {
	Result *UserChannelId `protobuf:"bytes,1,opt,name=result" json:"result,omitempty"`
}

func (m *GetUserChannelIdResp) Reset()                    { *m = GetUserChannelIdResp{} }
func (m *GetUserChannelIdResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserChannelIdResp) ProtoMessage()               {}
func (*GetUserChannelIdResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{30} }

func (m *GetUserChannelIdResp) GetResult() *UserChannelId {
	if m != nil {
		return m.Result
	}
	return nil
}

type BatchGetUserChannelIdReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetUserChannelIdReq) Reset()         { *m = BatchGetUserChannelIdReq{} }
func (m *BatchGetUserChannelIdReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserChannelIdReq) ProtoMessage()    {}
func (*BatchGetUserChannelIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{31}
}

func (m *BatchGetUserChannelIdReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserChannelIdResp struct {
	Results []*UserChannelId `protobuf:"bytes,1,rep,name=results" json:"results,omitempty"`
}

func (m *BatchGetUserChannelIdResp) Reset()         { *m = BatchGetUserChannelIdResp{} }
func (m *BatchGetUserChannelIdResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserChannelIdResp) ProtoMessage()    {}
func (*BatchGetUserChannelIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{32}
}

func (m *BatchGetUserChannelIdResp) GetResults() []*UserChannelId {
	if m != nil {
		return m.Results
	}
	return nil
}

type AddChannelAdminReq struct {
	AppId     uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
}

func (m *AddChannelAdminReq) Reset()                    { *m = AddChannelAdminReq{} }
func (m *AddChannelAdminReq) String() string            { return proto.CompactTextString(m) }
func (*AddChannelAdminReq) ProtoMessage()               {}
func (*AddChannelAdminReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{33} }

func (m *AddChannelAdminReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *AddChannelAdminReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddChannelAdminReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type AddChannelAdminResp struct {
}

func (m *AddChannelAdminResp) Reset()                    { *m = AddChannelAdminResp{} }
func (m *AddChannelAdminResp) String() string            { return proto.CompactTextString(m) }
func (*AddChannelAdminResp) ProtoMessage()               {}
func (*AddChannelAdminResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{34} }

type RemoveChannelAdminReq struct {
	AppId     uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
}

func (m *RemoveChannelAdminReq) Reset()                    { *m = RemoveChannelAdminReq{} }
func (m *RemoveChannelAdminReq) String() string            { return proto.CompactTextString(m) }
func (*RemoveChannelAdminReq) ProtoMessage()               {}
func (*RemoveChannelAdminReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{35} }

func (m *RemoveChannelAdminReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *RemoveChannelAdminReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemoveChannelAdminReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type RemoveChannelAdminResp struct {
}

func (m *RemoveChannelAdminResp) Reset()                    { *m = RemoveChannelAdminResp{} }
func (m *RemoveChannelAdminResp) String() string            { return proto.CompactTextString(m) }
func (*RemoveChannelAdminResp) ProtoMessage()               {}
func (*RemoveChannelAdminResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{36} }

type GetChannelAdminSizeReq struct {
	AppId     uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelAdminSizeReq) Reset()                    { *m = GetChannelAdminSizeReq{} }
func (m *GetChannelAdminSizeReq) String() string            { return proto.CompactTextString(m) }
func (*GetChannelAdminSizeReq) ProtoMessage()               {}
func (*GetChannelAdminSizeReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{37} }

func (m *GetChannelAdminSizeReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GetChannelAdminSizeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelAdminSizeResp struct {
	Size_ uint32 `protobuf:"varint,1,opt,name=size" json:"size"`
}

func (m *GetChannelAdminSizeResp) Reset()         { *m = GetChannelAdminSizeResp{} }
func (m *GetChannelAdminSizeResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelAdminSizeResp) ProtoMessage()    {}
func (*GetChannelAdminSizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{38}
}

func (m *GetChannelAdminSizeResp) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

type CleanChannelAdminReq struct {
	AppId     uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *CleanChannelAdminReq) Reset()                    { *m = CleanChannelAdminReq{} }
func (m *CleanChannelAdminReq) String() string            { return proto.CompactTextString(m) }
func (*CleanChannelAdminReq) ProtoMessage()               {}
func (*CleanChannelAdminReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{39} }

func (m *CleanChannelAdminReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *CleanChannelAdminReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CleanChannelAdminResp struct {
}

func (m *CleanChannelAdminResp) Reset()                    { *m = CleanChannelAdminResp{} }
func (m *CleanChannelAdminResp) String() string            { return proto.CompactTextString(m) }
func (*CleanChannelAdminResp) ProtoMessage()               {}
func (*CleanChannelAdminResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{40} }

type ChannelStat struct {
	OnlinePeak   uint32 `protobuf:"varint,1,req,name=online_peak,json=onlinePeak" json:"online_peak"`
	OnlinePeakAt uint32 `protobuf:"varint,2,req,name=online_peak_at,json=onlinePeakAt" json:"online_peak_at"`
}

func (m *ChannelStat) Reset()                    { *m = ChannelStat{} }
func (m *ChannelStat) String() string            { return proto.CompactTextString(m) }
func (*ChannelStat) ProtoMessage()               {}
func (*ChannelStat) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{41} }

func (m *ChannelStat) GetOnlinePeak() uint32 {
	if m != nil {
		return m.OnlinePeak
	}
	return 0
}

func (m *ChannelStat) GetOnlinePeakAt() uint32 {
	if m != nil {
		return m.OnlinePeakAt
	}
	return 0
}

type GetChannelStatReq struct {
	AppId     uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelStatReq) Reset()                    { *m = GetChannelStatReq{} }
func (m *GetChannelStatReq) String() string            { return proto.CompactTextString(m) }
func (*GetChannelStatReq) ProtoMessage()               {}
func (*GetChannelStatReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{42} }

func (m *GetChannelStatReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GetChannelStatReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelStatResp struct {
	Stat *ChannelStat `protobuf:"bytes,1,opt,name=stat" json:"stat,omitempty"`
}

func (m *GetChannelStatResp) Reset()                    { *m = GetChannelStatResp{} }
func (m *GetChannelStatResp) String() string            { return proto.CompactTextString(m) }
func (*GetChannelStatResp) ProtoMessage()               {}
func (*GetChannelStatResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{43} }

func (m *GetChannelStatResp) GetStat() *ChannelStat {
	if m != nil {
		return m.Stat
	}
	return nil
}

type HoldMicReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	AppId     uint32 `protobuf:"varint,2,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,3,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *HoldMicReq) Reset()                    { *m = HoldMicReq{} }
func (m *HoldMicReq) String() string            { return proto.CompactTextString(m) }
func (*HoldMicReq) ProtoMessage()               {}
func (*HoldMicReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{44} }

func (m *HoldMicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HoldMicReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *HoldMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type HoldMicResp struct {
}

func (m *HoldMicResp) Reset()                    { *m = HoldMicResp{} }
func (m *HoldMicResp) String() string            { return proto.CompactTextString(m) }
func (*HoldMicResp) ProtoMessage()               {}
func (*HoldMicResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{45} }

type ReleaseMicReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	AppId     uint32 `protobuf:"varint,2,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,3,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *ReleaseMicReq) Reset()                    { *m = ReleaseMicReq{} }
func (m *ReleaseMicReq) String() string            { return proto.CompactTextString(m) }
func (*ReleaseMicReq) ProtoMessage()               {}
func (*ReleaseMicReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{46} }

func (m *ReleaseMicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReleaseMicReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ReleaseMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ReleaseMicResp struct {
}

func (m *ReleaseMicResp) Reset()                    { *m = ReleaseMicResp{} }
func (m *ReleaseMicResp) String() string            { return proto.CompactTextString(m) }
func (*ReleaseMicResp) ProtoMessage()               {}
func (*ReleaseMicResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{47} }

type MuteChannelMemberReq struct {
	AppId     uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
}

func (m *MuteChannelMemberReq) Reset()                    { *m = MuteChannelMemberReq{} }
func (m *MuteChannelMemberReq) String() string            { return proto.CompactTextString(m) }
func (*MuteChannelMemberReq) ProtoMessage()               {}
func (*MuteChannelMemberReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{48} }

func (m *MuteChannelMemberReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *MuteChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuteChannelMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type MuteChannelMemberResp struct {
}

func (m *MuteChannelMemberResp) Reset()                    { *m = MuteChannelMemberResp{} }
func (m *MuteChannelMemberResp) String() string            { return proto.CompactTextString(m) }
func (*MuteChannelMemberResp) ProtoMessage()               {}
func (*MuteChannelMemberResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{49} }

type UnmuteChannelMemberReq struct {
	AppId     uint32 `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
}

func (m *UnmuteChannelMemberReq) Reset()                    { *m = UnmuteChannelMemberReq{} }
func (m *UnmuteChannelMemberReq) String() string            { return proto.CompactTextString(m) }
func (*UnmuteChannelMemberReq) ProtoMessage()               {}
func (*UnmuteChannelMemberReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{50} }

func (m *UnmuteChannelMemberReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *UnmuteChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UnmuteChannelMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UnmuteChannelMemberResp struct {
}

func (m *UnmuteChannelMemberResp) Reset()         { *m = UnmuteChannelMemberResp{} }
func (m *UnmuteChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*UnmuteChannelMemberResp) ProtoMessage()    {}
func (*UnmuteChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{51}
}

type GetAllChannelOnlineMemberCountReq struct {
}

func (m *GetAllChannelOnlineMemberCountReq) Reset()         { *m = GetAllChannelOnlineMemberCountReq{} }
func (m *GetAllChannelOnlineMemberCountReq) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelOnlineMemberCountReq) ProtoMessage()    {}
func (*GetAllChannelOnlineMemberCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{52}
}

type GetAllChannelOnlineMemberCountResp struct {
	OnlineCount uint32 `protobuf:"varint,1,req,name=online_count,json=onlineCount" json:"online_count"`
}

func (m *GetAllChannelOnlineMemberCountResp) Reset()         { *m = GetAllChannelOnlineMemberCountResp{} }
func (m *GetAllChannelOnlineMemberCountResp) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelOnlineMemberCountResp) ProtoMessage()    {}
func (*GetAllChannelOnlineMemberCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{53}
}

func (m *GetAllChannelOnlineMemberCountResp) GetOnlineCount() uint32 {
	if m != nil {
		return m.OnlineCount
	}
	return 0
}

type GetLiveChannelListReq struct {
}

func (m *GetLiveChannelListReq) Reset()                    { *m = GetLiveChannelListReq{} }
func (m *GetLiveChannelListReq) String() string            { return proto.CompactTextString(m) }
func (*GetLiveChannelListReq) ProtoMessage()               {}
func (*GetLiveChannelListReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{54} }

type GetLiveChannelListResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetLiveChannelListResp) Reset()                    { *m = GetLiveChannelListResp{} }
func (m *GetLiveChannelListResp) String() string            { return proto.CompactTextString(m) }
func (*GetLiveChannelListResp) ProtoMessage()               {}
func (*GetLiveChannelListResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{55} }

func (m *GetLiveChannelListResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type CheckUserIsChannelAdminReq struct {
	AppId     uint32   `protobuf:"varint,1,opt,name=app_id,json=appId" json:"app_id"`
	ChannelId uint32   `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	UidList   []uint32 `protobuf:"varint,3,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *CheckUserIsChannelAdminReq) Reset()         { *m = CheckUserIsChannelAdminReq{} }
func (m *CheckUserIsChannelAdminReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserIsChannelAdminReq) ProtoMessage()    {}
func (*CheckUserIsChannelAdminReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{56}
}

func (m *CheckUserIsChannelAdminReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *CheckUserIsChannelAdminReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckUserIsChannelAdminReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type UserAdminRet struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IsAdmin bool   `protobuf:"varint,2,req,name=is_admin,json=isAdmin" json:"is_admin"`
}

func (m *UserAdminRet) Reset()                    { *m = UserAdminRet{} }
func (m *UserAdminRet) String() string            { return proto.CompactTextString(m) }
func (*UserAdminRet) ProtoMessage()               {}
func (*UserAdminRet) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{57} }

func (m *UserAdminRet) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserAdminRet) GetIsAdmin() bool {
	if m != nil {
		return m.IsAdmin
	}
	return false
}

type CheckUserIsChannelAdminResp struct {
	RetList []*UserAdminRet `protobuf:"bytes,1,rep,name=ret_list,json=retList" json:"ret_list,omitempty"`
}

func (m *CheckUserIsChannelAdminResp) Reset()         { *m = CheckUserIsChannelAdminResp{} }
func (m *CheckUserIsChannelAdminResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserIsChannelAdminResp) ProtoMessage()    {}
func (*CheckUserIsChannelAdminResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{58}
}

func (m *CheckUserIsChannelAdminResp) GetRetList() []*UserAdminRet {
	if m != nil {
		return m.RetList
	}
	return nil
}

type ExpireChannelMemberReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *ExpireChannelMemberReq) Reset()                    { *m = ExpireChannelMemberReq{} }
func (m *ExpireChannelMemberReq) String() string            { return proto.CompactTextString(m) }
func (*ExpireChannelMemberReq) ProtoMessage()               {}
func (*ExpireChannelMemberReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelol, []int{59} }

func (m *ExpireChannelMemberReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type ExpireChannelMemberResp struct {
}

func (m *ExpireChannelMemberResp) Reset()         { *m = ExpireChannelMemberResp{} }
func (m *ExpireChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*ExpireChannelMemberResp) ProtoMessage()    {}
func (*ExpireChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelol, []int{60}
}

func init() {
	proto.RegisterType((*ChannelMember)(nil), "channelOL.ChannelMember")
	proto.RegisterType((*GetUserChannelReq)(nil), "channelOL.GetUserChannelReq")
	proto.RegisterType((*GetUserChannelResp)(nil), "channelOL.GetUserChannelResp")
	proto.RegisterType((*GetChannelMemberListReq)(nil), "channelOL.GetChannelMemberListReq")
	proto.RegisterType((*GetChannelMemberListResp)(nil), "channelOL.GetChannelMemberListResp")
	proto.RegisterType((*GetChannelMemberUidListReq)(nil), "channelOL.GetChannelMemberUidListReq")
	proto.RegisterType((*GetChannelMemberUidListResp)(nil), "channelOL.GetChannelMemberUidListResp")
	proto.RegisterType((*ChannelMemberListInfo)(nil), "channelOL.ChannelMemberListInfo")
	proto.RegisterType((*BatchGetChannelMemberListReq)(nil), "channelOL.BatchGetChannelMemberListReq")
	proto.RegisterType((*BatchGetChannelMemberListResp)(nil), "channelOL.BatchGetChannelMemberListResp")
	proto.RegisterType((*NotifyPcHelperJoinReq)(nil), "channelOL.NotifyPcHelperJoinReq")
	proto.RegisterType((*NotifyPcHelperJoinResp)(nil), "channelOL.NotifyPcHelperJoinResp")
	proto.RegisterType((*NobilityInfo)(nil), "channelOL.NobilityInfo")
	proto.RegisterType((*AddChannelMemberReq)(nil), "channelOL.AddChannelMemberReq")
	proto.RegisterType((*AddChannelMemberResp)(nil), "channelOL.AddChannelMemberResp")
	proto.RegisterType((*RemoveChannelMemberReq)(nil), "channelOL.RemoveChannelMemberReq")
	proto.RegisterType((*RemoveChannelMemberResp)(nil), "channelOL.RemoveChannelMemberResp")
	proto.RegisterType((*RemoveChannelReq)(nil), "channelOL.RemoveChannelReq")
	proto.RegisterType((*RemoveChannelResp)(nil), "channelOL.RemoveChannelResp")
	proto.RegisterType((*MemberFootprint)(nil), "channelOL.MemberFootprint")
	proto.RegisterType((*GetChannelMemberFootprintReq)(nil), "channelOL.GetChannelMemberFootprintReq")
	proto.RegisterType((*GetChannelMemberFootprintResp)(nil), "channelOL.GetChannelMemberFootprintResp")
	proto.RegisterType((*GetChannelMemberReq)(nil), "channelOL.GetChannelMemberReq")
	proto.RegisterType((*GetChannelMemberResp)(nil), "channelOL.GetChannelMemberResp")
	proto.RegisterType((*GetChannelMemberSizeReq)(nil), "channelOL.GetChannelMemberSizeReq")
	proto.RegisterType((*GetChannelMemberSizeResp)(nil), "channelOL.GetChannelMemberSizeResp")
	proto.RegisterType((*BatchGetChannelMemberSizeReq)(nil), "channelOL.BatchGetChannelMemberSizeReq")
	proto.RegisterType((*BatchGetChannelMemberSizeResp)(nil), "channelOL.BatchGetChannelMemberSizeResp")
	proto.RegisterType((*GetUserChannelIdReq)(nil), "channelOL.GetUserChannelIdReq")
	proto.RegisterType((*UserChannelId)(nil), "channelOL.UserChannelId")
	proto.RegisterType((*GetUserChannelIdResp)(nil), "channelOL.GetUserChannelIdResp")
	proto.RegisterType((*BatchGetUserChannelIdReq)(nil), "channelOL.BatchGetUserChannelIdReq")
	proto.RegisterType((*BatchGetUserChannelIdResp)(nil), "channelOL.BatchGetUserChannelIdResp")
	proto.RegisterType((*AddChannelAdminReq)(nil), "channelOL.AddChannelAdminReq")
	proto.RegisterType((*AddChannelAdminResp)(nil), "channelOL.AddChannelAdminResp")
	proto.RegisterType((*RemoveChannelAdminReq)(nil), "channelOL.RemoveChannelAdminReq")
	proto.RegisterType((*RemoveChannelAdminResp)(nil), "channelOL.RemoveChannelAdminResp")
	proto.RegisterType((*GetChannelAdminSizeReq)(nil), "channelOL.GetChannelAdminSizeReq")
	proto.RegisterType((*GetChannelAdminSizeResp)(nil), "channelOL.GetChannelAdminSizeResp")
	proto.RegisterType((*CleanChannelAdminReq)(nil), "channelOL.CleanChannelAdminReq")
	proto.RegisterType((*CleanChannelAdminResp)(nil), "channelOL.CleanChannelAdminResp")
	proto.RegisterType((*ChannelStat)(nil), "channelOL.ChannelStat")
	proto.RegisterType((*GetChannelStatReq)(nil), "channelOL.GetChannelStatReq")
	proto.RegisterType((*GetChannelStatResp)(nil), "channelOL.GetChannelStatResp")
	proto.RegisterType((*HoldMicReq)(nil), "channelOL.HoldMicReq")
	proto.RegisterType((*HoldMicResp)(nil), "channelOL.HoldMicResp")
	proto.RegisterType((*ReleaseMicReq)(nil), "channelOL.ReleaseMicReq")
	proto.RegisterType((*ReleaseMicResp)(nil), "channelOL.ReleaseMicResp")
	proto.RegisterType((*MuteChannelMemberReq)(nil), "channelOL.MuteChannelMemberReq")
	proto.RegisterType((*MuteChannelMemberResp)(nil), "channelOL.MuteChannelMemberResp")
	proto.RegisterType((*UnmuteChannelMemberReq)(nil), "channelOL.UnmuteChannelMemberReq")
	proto.RegisterType((*UnmuteChannelMemberResp)(nil), "channelOL.UnmuteChannelMemberResp")
	proto.RegisterType((*GetAllChannelOnlineMemberCountReq)(nil), "channelOL.GetAllChannelOnlineMemberCountReq")
	proto.RegisterType((*GetAllChannelOnlineMemberCountResp)(nil), "channelOL.GetAllChannelOnlineMemberCountResp")
	proto.RegisterType((*GetLiveChannelListReq)(nil), "channelOL.GetLiveChannelListReq")
	proto.RegisterType((*GetLiveChannelListResp)(nil), "channelOL.GetLiveChannelListResp")
	proto.RegisterType((*CheckUserIsChannelAdminReq)(nil), "channelOL.CheckUserIsChannelAdminReq")
	proto.RegisterType((*UserAdminRet)(nil), "channelOL.UserAdminRet")
	proto.RegisterType((*CheckUserIsChannelAdminResp)(nil), "channelOL.CheckUserIsChannelAdminResp")
	proto.RegisterType((*ExpireChannelMemberReq)(nil), "channelOL.ExpireChannelMemberReq")
	proto.RegisterType((*ExpireChannelMemberResp)(nil), "channelOL.ExpireChannelMemberResp")
	proto.RegisterEnum("channelOL.ChannelOlMemberFlagBitmap", ChannelOlMemberFlagBitmap_name, ChannelOlMemberFlagBitmap_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ChannelOL service

type ChannelOLClient interface {
	GetUserChannel(ctx context.Context, in *GetUserChannelReq, opts ...grpc.CallOption) (*GetUserChannelResp, error)
	GetChannelMemberList(ctx context.Context, in *GetChannelMemberListReq, opts ...grpc.CallOption) (*GetChannelMemberListResp, error)
	NotifyPcHelperJoin(ctx context.Context, in *NotifyPcHelperJoinReq, opts ...grpc.CallOption) (*NotifyPcHelperJoinResp, error)
	// 批量获取一组房间的指定个数成员列表 deprecated @see channelolmember.proto
	BatchGetChannelMemberList(ctx context.Context, in *BatchGetChannelMemberListReq, opts ...grpc.CallOption) (*BatchGetChannelMemberListResp, error)
	//
	// move from channelsvr, 2018.06
	//
	AddChannelMember(ctx context.Context, in *AddChannelMemberReq, opts ...grpc.CallOption) (*AddChannelMemberResp, error)
	RemoveChannelMember(ctx context.Context, in *RemoveChannelMemberReq, opts ...grpc.CallOption) (*RemoveChannelMemberResp, error)
	RemoveChannel(ctx context.Context, in *RemoveChannelReq, opts ...grpc.CallOption) (*RemoveChannelResp, error)
	GetChannelMemberFootprint(ctx context.Context, in *GetChannelMemberFootprintReq, opts ...grpc.CallOption) (*GetChannelMemberFootprintResp, error)
	GetChannelMember(ctx context.Context, in *GetChannelMemberReq, opts ...grpc.CallOption) (*GetChannelMemberResp, error)
	GetChannelMemberSize(ctx context.Context, in *GetChannelMemberSizeReq, opts ...grpc.CallOption) (*GetChannelMemberSizeResp, error)
	GetUserChannelId(ctx context.Context, in *GetUserChannelIdReq, opts ...grpc.CallOption) (*GetUserChannelIdResp, error)
	BatchGetUserChannelId(ctx context.Context, in *BatchGetUserChannelIdReq, opts ...grpc.CallOption) (*BatchGetUserChannelIdResp, error)
	// 在channelol在线信息中 记录房间当前在线的管理员信息
	AddChannelAdmin(ctx context.Context, in *AddChannelAdminReq, opts ...grpc.CallOption) (*AddChannelAdminResp, error)
	RemoveChannelAdmin(ctx context.Context, in *RemoveChannelAdminReq, opts ...grpc.CallOption) (*RemoveChannelAdminResp, error)
	GetChannelAdminSize(ctx context.Context, in *GetChannelAdminSizeReq, opts ...grpc.CallOption) (*GetChannelAdminSizeResp, error)
	CleanChannelAdmin(ctx context.Context, in *CleanChannelAdminReq, opts ...grpc.CallOption) (*CleanChannelAdminResp, error)
	GetChannelStat(ctx context.Context, in *GetChannelStatReq, opts ...grpc.CallOption) (*GetChannelStatResp, error)
	// 在channelol在线信息中 记录用户的是否上麦
	HoldMic(ctx context.Context, in *HoldMicReq, opts ...grpc.CallOption) (*HoldMicResp, error)
	ReleaseMic(ctx context.Context, in *ReleaseMicReq, opts ...grpc.CallOption) (*ReleaseMicResp, error)
	// 在channelol在线信息中 记录用户的是否被禁言
	MuteChannelMember(ctx context.Context, in *MuteChannelMemberReq, opts ...grpc.CallOption) (*MuteChannelMemberResp, error)
	UnmuteChannelMember(ctx context.Context, in *UnmuteChannelMemberReq, opts ...grpc.CallOption) (*UnmuteChannelMemberResp, error)
	BatchGetChannelMemberSize(ctx context.Context, in *BatchGetChannelMemberSizeReq, opts ...grpc.CallOption) (*BatchGetChannelMemberSizeResp, error)
	GetChannelMemberUidList(ctx context.Context, in *GetChannelMemberUidListReq, opts ...grpc.CallOption) (*GetChannelMemberUidListResp, error)
	CheckUserIsChannelAdmin(ctx context.Context, in *CheckUserIsChannelAdminReq, opts ...grpc.CallOption) (*CheckUserIsChannelAdminResp, error)
	ExpireChannelMember(ctx context.Context, in *ExpireChannelMemberReq, opts ...grpc.CallOption) (*ExpireChannelMemberResp, error)
}

type channelOLClient struct {
	cc *grpc.ClientConn
}

func NewChannelOLClient(cc *grpc.ClientConn) ChannelOLClient {
	return &channelOLClient{cc}
}

func (c *channelOLClient) GetUserChannel(ctx context.Context, in *GetUserChannelReq, opts ...grpc.CallOption) (*GetUserChannelResp, error) {
	out := new(GetUserChannelResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/GetUserChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) GetChannelMemberList(ctx context.Context, in *GetChannelMemberListReq, opts ...grpc.CallOption) (*GetChannelMemberListResp, error) {
	out := new(GetChannelMemberListResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/GetChannelMemberList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) NotifyPcHelperJoin(ctx context.Context, in *NotifyPcHelperJoinReq, opts ...grpc.CallOption) (*NotifyPcHelperJoinResp, error) {
	out := new(NotifyPcHelperJoinResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/NotifyPcHelperJoin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) BatchGetChannelMemberList(ctx context.Context, in *BatchGetChannelMemberListReq, opts ...grpc.CallOption) (*BatchGetChannelMemberListResp, error) {
	out := new(BatchGetChannelMemberListResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/BatchGetChannelMemberList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) AddChannelMember(ctx context.Context, in *AddChannelMemberReq, opts ...grpc.CallOption) (*AddChannelMemberResp, error) {
	out := new(AddChannelMemberResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/AddChannelMember", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) RemoveChannelMember(ctx context.Context, in *RemoveChannelMemberReq, opts ...grpc.CallOption) (*RemoveChannelMemberResp, error) {
	out := new(RemoveChannelMemberResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/RemoveChannelMember", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) RemoveChannel(ctx context.Context, in *RemoveChannelReq, opts ...grpc.CallOption) (*RemoveChannelResp, error) {
	out := new(RemoveChannelResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/RemoveChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) GetChannelMemberFootprint(ctx context.Context, in *GetChannelMemberFootprintReq, opts ...grpc.CallOption) (*GetChannelMemberFootprintResp, error) {
	out := new(GetChannelMemberFootprintResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/GetChannelMemberFootprint", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) GetChannelMember(ctx context.Context, in *GetChannelMemberReq, opts ...grpc.CallOption) (*GetChannelMemberResp, error) {
	out := new(GetChannelMemberResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/GetChannelMember", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) GetChannelMemberSize(ctx context.Context, in *GetChannelMemberSizeReq, opts ...grpc.CallOption) (*GetChannelMemberSizeResp, error) {
	out := new(GetChannelMemberSizeResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/GetChannelMemberSize", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) GetUserChannelId(ctx context.Context, in *GetUserChannelIdReq, opts ...grpc.CallOption) (*GetUserChannelIdResp, error) {
	out := new(GetUserChannelIdResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/GetUserChannelId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) BatchGetUserChannelId(ctx context.Context, in *BatchGetUserChannelIdReq, opts ...grpc.CallOption) (*BatchGetUserChannelIdResp, error) {
	out := new(BatchGetUserChannelIdResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/BatchGetUserChannelId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) AddChannelAdmin(ctx context.Context, in *AddChannelAdminReq, opts ...grpc.CallOption) (*AddChannelAdminResp, error) {
	out := new(AddChannelAdminResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/AddChannelAdmin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) RemoveChannelAdmin(ctx context.Context, in *RemoveChannelAdminReq, opts ...grpc.CallOption) (*RemoveChannelAdminResp, error) {
	out := new(RemoveChannelAdminResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/RemoveChannelAdmin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) GetChannelAdminSize(ctx context.Context, in *GetChannelAdminSizeReq, opts ...grpc.CallOption) (*GetChannelAdminSizeResp, error) {
	out := new(GetChannelAdminSizeResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/GetChannelAdminSize", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) CleanChannelAdmin(ctx context.Context, in *CleanChannelAdminReq, opts ...grpc.CallOption) (*CleanChannelAdminResp, error) {
	out := new(CleanChannelAdminResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/CleanChannelAdmin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) GetChannelStat(ctx context.Context, in *GetChannelStatReq, opts ...grpc.CallOption) (*GetChannelStatResp, error) {
	out := new(GetChannelStatResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/GetChannelStat", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) HoldMic(ctx context.Context, in *HoldMicReq, opts ...grpc.CallOption) (*HoldMicResp, error) {
	out := new(HoldMicResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/HoldMic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) ReleaseMic(ctx context.Context, in *ReleaseMicReq, opts ...grpc.CallOption) (*ReleaseMicResp, error) {
	out := new(ReleaseMicResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/ReleaseMic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) MuteChannelMember(ctx context.Context, in *MuteChannelMemberReq, opts ...grpc.CallOption) (*MuteChannelMemberResp, error) {
	out := new(MuteChannelMemberResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/MuteChannelMember", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) UnmuteChannelMember(ctx context.Context, in *UnmuteChannelMemberReq, opts ...grpc.CallOption) (*UnmuteChannelMemberResp, error) {
	out := new(UnmuteChannelMemberResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/UnmuteChannelMember", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) BatchGetChannelMemberSize(ctx context.Context, in *BatchGetChannelMemberSizeReq, opts ...grpc.CallOption) (*BatchGetChannelMemberSizeResp, error) {
	out := new(BatchGetChannelMemberSizeResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/BatchGetChannelMemberSize", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) GetChannelMemberUidList(ctx context.Context, in *GetChannelMemberUidListReq, opts ...grpc.CallOption) (*GetChannelMemberUidListResp, error) {
	out := new(GetChannelMemberUidListResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/GetChannelMemberUidList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) CheckUserIsChannelAdmin(ctx context.Context, in *CheckUserIsChannelAdminReq, opts ...grpc.CallOption) (*CheckUserIsChannelAdminResp, error) {
	out := new(CheckUserIsChannelAdminResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/CheckUserIsChannelAdmin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOLClient) ExpireChannelMember(ctx context.Context, in *ExpireChannelMemberReq, opts ...grpc.CallOption) (*ExpireChannelMemberResp, error) {
	out := new(ExpireChannelMemberResp)
	err := grpc.Invoke(ctx, "/channelOL.channelOL/ExpireChannelMember", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ChannelOL service

type ChannelOLServer interface {
	GetUserChannel(context.Context, *GetUserChannelReq) (*GetUserChannelResp, error)
	GetChannelMemberList(context.Context, *GetChannelMemberListReq) (*GetChannelMemberListResp, error)
	NotifyPcHelperJoin(context.Context, *NotifyPcHelperJoinReq) (*NotifyPcHelperJoinResp, error)
	// 批量获取一组房间的指定个数成员列表 deprecated @see channelolmember.proto
	BatchGetChannelMemberList(context.Context, *BatchGetChannelMemberListReq) (*BatchGetChannelMemberListResp, error)
	//
	// move from channelsvr, 2018.06
	//
	AddChannelMember(context.Context, *AddChannelMemberReq) (*AddChannelMemberResp, error)
	RemoveChannelMember(context.Context, *RemoveChannelMemberReq) (*RemoveChannelMemberResp, error)
	RemoveChannel(context.Context, *RemoveChannelReq) (*RemoveChannelResp, error)
	GetChannelMemberFootprint(context.Context, *GetChannelMemberFootprintReq) (*GetChannelMemberFootprintResp, error)
	GetChannelMember(context.Context, *GetChannelMemberReq) (*GetChannelMemberResp, error)
	GetChannelMemberSize(context.Context, *GetChannelMemberSizeReq) (*GetChannelMemberSizeResp, error)
	GetUserChannelId(context.Context, *GetUserChannelIdReq) (*GetUserChannelIdResp, error)
	BatchGetUserChannelId(context.Context, *BatchGetUserChannelIdReq) (*BatchGetUserChannelIdResp, error)
	// 在channelol在线信息中 记录房间当前在线的管理员信息
	AddChannelAdmin(context.Context, *AddChannelAdminReq) (*AddChannelAdminResp, error)
	RemoveChannelAdmin(context.Context, *RemoveChannelAdminReq) (*RemoveChannelAdminResp, error)
	GetChannelAdminSize(context.Context, *GetChannelAdminSizeReq) (*GetChannelAdminSizeResp, error)
	CleanChannelAdmin(context.Context, *CleanChannelAdminReq) (*CleanChannelAdminResp, error)
	GetChannelStat(context.Context, *GetChannelStatReq) (*GetChannelStatResp, error)
	// 在channelol在线信息中 记录用户的是否上麦
	HoldMic(context.Context, *HoldMicReq) (*HoldMicResp, error)
	ReleaseMic(context.Context, *ReleaseMicReq) (*ReleaseMicResp, error)
	// 在channelol在线信息中 记录用户的是否被禁言
	MuteChannelMember(context.Context, *MuteChannelMemberReq) (*MuteChannelMemberResp, error)
	UnmuteChannelMember(context.Context, *UnmuteChannelMemberReq) (*UnmuteChannelMemberResp, error)
	BatchGetChannelMemberSize(context.Context, *BatchGetChannelMemberSizeReq) (*BatchGetChannelMemberSizeResp, error)
	GetChannelMemberUidList(context.Context, *GetChannelMemberUidListReq) (*GetChannelMemberUidListResp, error)
	CheckUserIsChannelAdmin(context.Context, *CheckUserIsChannelAdminReq) (*CheckUserIsChannelAdminResp, error)
	ExpireChannelMember(context.Context, *ExpireChannelMemberReq) (*ExpireChannelMemberResp, error)
}

func RegisterChannelOLServer(s *grpc.Server, srv ChannelOLServer) {
	s.RegisterService(&_ChannelOL_serviceDesc, srv)
}

func _ChannelOL_GetUserChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).GetUserChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/GetUserChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).GetUserChannel(ctx, req.(*GetUserChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_GetChannelMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).GetChannelMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/GetChannelMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).GetChannelMemberList(ctx, req.(*GetChannelMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_NotifyPcHelperJoin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPcHelperJoinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).NotifyPcHelperJoin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/NotifyPcHelperJoin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).NotifyPcHelperJoin(ctx, req.(*NotifyPcHelperJoinReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_BatchGetChannelMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).BatchGetChannelMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/BatchGetChannelMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).BatchGetChannelMemberList(ctx, req.(*BatchGetChannelMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_AddChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).AddChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/AddChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).AddChannelMember(ctx, req.(*AddChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_RemoveChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).RemoveChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/RemoveChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).RemoveChannelMember(ctx, req.(*RemoveChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_RemoveChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).RemoveChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/RemoveChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).RemoveChannel(ctx, req.(*RemoveChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_GetChannelMemberFootprint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMemberFootprintReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).GetChannelMemberFootprint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/GetChannelMemberFootprint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).GetChannelMemberFootprint(ctx, req.(*GetChannelMemberFootprintReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_GetChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).GetChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/GetChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).GetChannelMember(ctx, req.(*GetChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_GetChannelMemberSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMemberSizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).GetChannelMemberSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/GetChannelMemberSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).GetChannelMemberSize(ctx, req.(*GetChannelMemberSizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_GetUserChannelId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserChannelIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).GetUserChannelId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/GetUserChannelId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).GetUserChannelId(ctx, req.(*GetUserChannelIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_BatchGetUserChannelId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserChannelIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).BatchGetUserChannelId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/BatchGetUserChannelId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).BatchGetUserChannelId(ctx, req.(*BatchGetUserChannelIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_AddChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).AddChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/AddChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).AddChannelAdmin(ctx, req.(*AddChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_RemoveChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).RemoveChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/RemoveChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).RemoveChannelAdmin(ctx, req.(*RemoveChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_GetChannelAdminSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelAdminSizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).GetChannelAdminSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/GetChannelAdminSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).GetChannelAdminSize(ctx, req.(*GetChannelAdminSizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_CleanChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).CleanChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/CleanChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).CleanChannelAdmin(ctx, req.(*CleanChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_GetChannelStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).GetChannelStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/GetChannelStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).GetChannelStat(ctx, req.(*GetChannelStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_HoldMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HoldMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).HoldMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/HoldMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).HoldMic(ctx, req.(*HoldMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_ReleaseMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).ReleaseMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/ReleaseMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).ReleaseMic(ctx, req.(*ReleaseMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_MuteChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MuteChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).MuteChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/MuteChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).MuteChannelMember(ctx, req.(*MuteChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_UnmuteChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnmuteChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).UnmuteChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/UnmuteChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).UnmuteChannelMember(ctx, req.(*UnmuteChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_BatchGetChannelMemberSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelMemberSizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).BatchGetChannelMemberSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/BatchGetChannelMemberSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).BatchGetChannelMemberSize(ctx, req.(*BatchGetChannelMemberSizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_GetChannelMemberUidList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMemberUidListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).GetChannelMemberUidList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/GetChannelMemberUidList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).GetChannelMemberUidList(ctx, req.(*GetChannelMemberUidListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_CheckUserIsChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserIsChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).CheckUserIsChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/CheckUserIsChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).CheckUserIsChannelAdmin(ctx, req.(*CheckUserIsChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOL_ExpireChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpireChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOLServer).ExpireChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelOL.channelOL/ExpireChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOLServer).ExpireChannelMember(ctx, req.(*ExpireChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelOL_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelOL.channelOL",
	HandlerType: (*ChannelOLServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserChannel",
			Handler:    _ChannelOL_GetUserChannel_Handler,
		},
		{
			MethodName: "GetChannelMemberList",
			Handler:    _ChannelOL_GetChannelMemberList_Handler,
		},
		{
			MethodName: "NotifyPcHelperJoin",
			Handler:    _ChannelOL_NotifyPcHelperJoin_Handler,
		},
		{
			MethodName: "BatchGetChannelMemberList",
			Handler:    _ChannelOL_BatchGetChannelMemberList_Handler,
		},
		{
			MethodName: "AddChannelMember",
			Handler:    _ChannelOL_AddChannelMember_Handler,
		},
		{
			MethodName: "RemoveChannelMember",
			Handler:    _ChannelOL_RemoveChannelMember_Handler,
		},
		{
			MethodName: "RemoveChannel",
			Handler:    _ChannelOL_RemoveChannel_Handler,
		},
		{
			MethodName: "GetChannelMemberFootprint",
			Handler:    _ChannelOL_GetChannelMemberFootprint_Handler,
		},
		{
			MethodName: "GetChannelMember",
			Handler:    _ChannelOL_GetChannelMember_Handler,
		},
		{
			MethodName: "GetChannelMemberSize",
			Handler:    _ChannelOL_GetChannelMemberSize_Handler,
		},
		{
			MethodName: "GetUserChannelId",
			Handler:    _ChannelOL_GetUserChannelId_Handler,
		},
		{
			MethodName: "BatchGetUserChannelId",
			Handler:    _ChannelOL_BatchGetUserChannelId_Handler,
		},
		{
			MethodName: "AddChannelAdmin",
			Handler:    _ChannelOL_AddChannelAdmin_Handler,
		},
		{
			MethodName: "RemoveChannelAdmin",
			Handler:    _ChannelOL_RemoveChannelAdmin_Handler,
		},
		{
			MethodName: "GetChannelAdminSize",
			Handler:    _ChannelOL_GetChannelAdminSize_Handler,
		},
		{
			MethodName: "CleanChannelAdmin",
			Handler:    _ChannelOL_CleanChannelAdmin_Handler,
		},
		{
			MethodName: "GetChannelStat",
			Handler:    _ChannelOL_GetChannelStat_Handler,
		},
		{
			MethodName: "HoldMic",
			Handler:    _ChannelOL_HoldMic_Handler,
		},
		{
			MethodName: "ReleaseMic",
			Handler:    _ChannelOL_ReleaseMic_Handler,
		},
		{
			MethodName: "MuteChannelMember",
			Handler:    _ChannelOL_MuteChannelMember_Handler,
		},
		{
			MethodName: "UnmuteChannelMember",
			Handler:    _ChannelOL_UnmuteChannelMember_Handler,
		},
		{
			MethodName: "BatchGetChannelMemberSize",
			Handler:    _ChannelOL_BatchGetChannelMemberSize_Handler,
		},
		{
			MethodName: "GetChannelMemberUidList",
			Handler:    _ChannelOL_GetChannelMemberUidList_Handler,
		},
		{
			MethodName: "CheckUserIsChannelAdmin",
			Handler:    _ChannelOL_CheckUserIsChannelAdmin_Handler,
		},
		{
			MethodName: "ExpireChannelMember",
			Handler:    _ChannelOL_ExpireChannelMember_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelol/channelol.proto",
}

func (m *ChannelMember) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMember) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.IsHoldmic {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.IsMute {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.CostValue))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Ts))
	dAtA[i] = 0x30
	i++
	if m.IsHavePcHelper {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	if m.IsRobot {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Flag))
	return i, nil
}

func (m *GetUserChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	if m.MemberInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintChannelol(dAtA, i, uint64(m.MemberInfo.Size()))
		n1, err := m.MemberInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetChannelMemberListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMemberListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.StartIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetChannelMemberListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMemberListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MemberList) > 0 {
		for _, msg := range m.MemberList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AllSize))
	return i, nil
}

func (m *GetChannelMemberUidListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMemberUidListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.StartIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetChannelMemberUidListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMemberUidListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MemberUidList) > 0 {
		for _, num := range m.MemberUidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *ChannelMemberListInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMemberListInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	if len(m.MemberList) > 0 {
		for _, msg := range m.MemberList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AllSize))
	return i, nil
}

func (m *BatchGetChannelMemberListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetChannelMemberListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.MemberCount))
	return i, nil
}

func (m *BatchGetChannelMemberListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetChannelMemberListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelmemberinfoList) > 0 {
		for _, msg := range m.ChannelmemberinfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *NotifyPcHelperJoinReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyPcHelperJoinReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *NotifyPcHelperJoinResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyPcHelperJoinResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *NobilityInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NobilityInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Value))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.KeepValue))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.CycleTs))
	dAtA[i] = 0x28
	i++
	if m.Invisible {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *AddChannelMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.HistoryGiftCost))
	dAtA[i] = 0x30
	i++
	if m.IsMute {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	if m.IsRobot {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	if m.IsPchelper {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x48
	i++
	if m.IsAdmin {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x50
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x58
	i++
	if m.IsRoomHasPwd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x60
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.MarketId))
	if m.NobilityInfo != nil {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintChannelol(dAtA, i, uint64(m.NobilityInfo.Size()))
		n2, err := m.NobilityInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x70
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Source))
	dAtA[i] = 0x78
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.FollowFriendUid))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	if m.HideFootprint {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.LastChannelId))
	dAtA[i] = 0x92
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(len(m.ChannelViewId)))
	i += copy(dAtA[i:], m.ChannelViewId)
	return i, nil
}

func (m *AddChannelMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsAlreadyIn {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.MemberSize))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AdminMemberSize))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ServerMsTs))
	return i, nil
}

func (m *RemoveChannelMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x28
	i++
	if m.IsAdmin {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	if m.IsChangeChannel {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelCreateUid))
	dAtA[i] = 0x48
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x52
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(len(m.ChannelViewId)))
	i += copy(dAtA[i:], m.ChannelViewId)
	return i, nil
}

func (m *RemoveChannelMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.Exist {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Duration))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.LeftMemberCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.LeftAdminCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ServerMsTs))
	return i, nil
}

func (m *RemoveChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	if m.ReturnMembers {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RemoveChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Members) > 0 {
		for _, num := range m.Members {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *MemberFootprint) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MemberFootprint) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Timestamp))
	return i, nil
}

func (m *GetChannelMemberFootprintReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMemberFootprintReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelMemberFootprintResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMemberFootprintResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Footprints) > 0 {
		for _, msg := range m.Footprints {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetChannelMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetChannelMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Member != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelol(dAtA, i, uint64(m.Member.Size()))
		n3, err := m.Member.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *GetChannelMemberSizeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMemberSizeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelMemberSizeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMemberSizeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Size_))
	return i, nil
}

func (m *BatchGetChannelMemberSizeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetChannelMemberSizeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetChannelMemberSizeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetChannelMemberSizeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MembersizeList) > 0 {
		for _, num := range m.MembersizeList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetUserChannelIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserChannelIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *UserChannelId) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserChannelId) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Flag))
	return i, nil
}

func (m *GetUserChannelIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserChannelIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Result != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelol(dAtA, i, uint64(m.Result.Size()))
		n4, err := m.Result.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *BatchGetUserChannelIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserChannelIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetUserChannelIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserChannelIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Results) > 0 {
		for _, msg := range m.Results {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddChannelAdminReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelAdminReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *AddChannelAdminResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelAdminResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RemoveChannelAdminReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelAdminReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *RemoveChannelAdminResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelAdminResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelAdminSizeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelAdminSizeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelAdminSizeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelAdminSizeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Size_))
	return i, nil
}

func (m *CleanChannelAdminReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanChannelAdminReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *CleanChannelAdminResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanChannelAdminResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ChannelStat) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelStat) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.OnlinePeak))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.OnlinePeakAt))
	return i, nil
}

func (m *GetChannelStatReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelStatReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelStatResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelStatResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Stat != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelol(dAtA, i, uint64(m.Stat.Size()))
		n5, err := m.Stat.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *HoldMicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HoldMicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *HoldMicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HoldMicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ReleaseMicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReleaseMicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *ReleaseMicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReleaseMicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *MuteChannelMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MuteChannelMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *MuteChannelMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MuteChannelMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UnmuteChannelMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnmuteChannelMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *UnmuteChannelMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnmuteChannelMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllChannelOnlineMemberCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllChannelOnlineMemberCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllChannelOnlineMemberCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllChannelOnlineMemberCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.OnlineCount))
	return i, nil
}

func (m *GetLiveChannelListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLiveChannelListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetLiveChannelListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLiveChannelListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CheckUserIsChannelAdminReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserIsChannelAdminReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.ChannelId))
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *UserAdminRet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserAdminRet) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelol(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.IsAdmin {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CheckUserIsChannelAdminResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserIsChannelAdminResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RetList) > 0 {
		for _, msg := range m.RetList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ExpireChannelMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExpireChannelMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelol(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *ExpireChannelMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExpireChannelMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Channelol(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channelol(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelol(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ChannelMember) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Uid))
	n += 2
	n += 2
	n += 1 + sovChannelol(uint64(m.CostValue))
	n += 1 + sovChannelol(uint64(m.Ts))
	n += 2
	n += 2
	n += 1 + sovChannelol(uint64(m.Flag))
	return n
}

func (m *GetUserChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Uid))
	return n
}

func (m *GetUserChannelResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.AppId))
	if m.MemberInfo != nil {
		l = m.MemberInfo.Size()
		n += 1 + l + sovChannelol(uint64(l))
	}
	return n
}

func (m *GetChannelMemberListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.StartIdx))
	n += 1 + sovChannelol(uint64(m.Limit))
	return n
}

func (m *GetChannelMemberListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MemberList) > 0 {
		for _, e := range m.MemberList {
			l = e.Size()
			n += 1 + l + sovChannelol(uint64(l))
		}
	}
	n += 1 + sovChannelol(uint64(m.AllSize))
	return n
}

func (m *GetChannelMemberUidListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.StartIdx))
	n += 1 + sovChannelol(uint64(m.Limit))
	return n
}

func (m *GetChannelMemberUidListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MemberUidList) > 0 {
		for _, e := range m.MemberUidList {
			n += 1 + sovChannelol(uint64(e))
		}
	}
	return n
}

func (m *ChannelMemberListInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.ChannelId))
	if len(m.MemberList) > 0 {
		for _, e := range m.MemberList {
			l = e.Size()
			n += 1 + l + sovChannelol(uint64(l))
		}
	}
	n += 1 + sovChannelol(uint64(m.AllSize))
	return n
}

func (m *BatchGetChannelMemberListReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelol(uint64(e))
		}
	}
	n += 1 + sovChannelol(uint64(m.MemberCount))
	return n
}

func (m *BatchGetChannelMemberListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelmemberinfoList) > 0 {
		for _, e := range m.ChannelmemberinfoList {
			l = e.Size()
			n += 1 + l + sovChannelol(uint64(l))
		}
	}
	return n
}

func (m *NotifyPcHelperJoinReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Uid))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	return n
}

func (m *NotifyPcHelperJoinResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *NobilityInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Level))
	n += 1 + sovChannelol(uint64(m.Value))
	n += 1 + sovChannelol(uint64(m.KeepValue))
	n += 1 + sovChannelol(uint64(m.CycleTs))
	n += 2
	return n
}

func (m *AddChannelMemberReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.ChannelType))
	n += 1 + sovChannelol(uint64(m.Uid))
	n += 1 + sovChannelol(uint64(m.HistoryGiftCost))
	n += 2
	n += 2
	n += 2
	n += 2
	n += 1 + sovChannelol(uint64(m.ChannelDisplayId))
	n += 2
	n += 1 + sovChannelol(uint64(m.MarketId))
	if m.NobilityInfo != nil {
		l = m.NobilityInfo.Size()
		n += 1 + l + sovChannelol(uint64(l))
	}
	n += 1 + sovChannelol(uint64(m.Source))
	n += 1 + sovChannelol(uint64(m.FollowFriendUid))
	n += 3
	n += 2 + sovChannelol(uint64(m.LastChannelId))
	l = len(m.ChannelViewId)
	n += 2 + l + sovChannelol(uint64(l))
	return n
}

func (m *AddChannelMemberResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	n += 1 + sovChannelol(uint64(m.MemberSize))
	n += 1 + sovChannelol(uint64(m.AdminMemberSize))
	n += 1 + sovChannelol(uint64(m.ServerMsTs))
	return n
}

func (m *RemoveChannelMemberReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.ChannelType))
	n += 1 + sovChannelol(uint64(m.Uid))
	n += 2
	n += 2
	n += 1 + sovChannelol(uint64(m.ChannelDisplayId))
	n += 1 + sovChannelol(uint64(m.ChannelCreateUid))
	n += 1 + sovChannelol(uint64(m.OpUid))
	l = len(m.ChannelViewId)
	n += 1 + l + sovChannelol(uint64(l))
	return n
}

func (m *RemoveChannelMemberResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	n += 1 + sovChannelol(uint64(m.Duration))
	n += 1 + sovChannelol(uint64(m.LeftMemberCount))
	n += 1 + sovChannelol(uint64(m.LeftAdminCount))
	n += 1 + sovChannelol(uint64(m.ServerMsTs))
	return n
}

func (m *RemoveChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 2
	return n
}

func (m *RemoveChannelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.Members) > 0 {
		for _, e := range m.Members {
			n += 1 + sovChannelol(uint64(e))
		}
	}
	return n
}

func (m *MemberFootprint) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Uid))
	n += 1 + sovChannelol(uint64(m.Timestamp))
	return n
}

func (m *GetChannelMemberFootprintReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	return n
}

func (m *GetChannelMemberFootprintResp) Size() (n int) {
	var l int
	_ = l
	if len(m.Footprints) > 0 {
		for _, e := range m.Footprints {
			l = e.Size()
			n += 1 + l + sovChannelol(uint64(l))
		}
	}
	return n
}

func (m *GetChannelMemberReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.Uid))
	return n
}

func (m *GetChannelMemberResp) Size() (n int) {
	var l int
	_ = l
	if m.Member != nil {
		l = m.Member.Size()
		n += 1 + l + sovChannelol(uint64(l))
	}
	return n
}

func (m *GetChannelMemberSizeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	return n
}

func (m *GetChannelMemberSizeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Size_))
	return n
}

func (m *BatchGetChannelMemberSizeReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelol(uint64(e))
		}
	}
	return n
}

func (m *BatchGetChannelMemberSizeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MembersizeList) > 0 {
		for _, e := range m.MembersizeList {
			n += 1 + sovChannelol(uint64(e))
		}
	}
	return n
}

func (m *GetUserChannelIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Uid))
	return n
}

func (m *UserChannelId) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Uid))
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.Flag))
	return n
}

func (m *GetUserChannelIdResp) Size() (n int) {
	var l int
	_ = l
	if m.Result != nil {
		l = m.Result.Size()
		n += 1 + l + sovChannelol(uint64(l))
	}
	return n
}

func (m *BatchGetUserChannelIdReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovChannelol(uint64(e))
		}
	}
	return n
}

func (m *BatchGetUserChannelIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.Results) > 0 {
		for _, e := range m.Results {
			l = e.Size()
			n += 1 + l + sovChannelol(uint64(l))
		}
	}
	return n
}

func (m *AddChannelAdminReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.Uid))
	return n
}

func (m *AddChannelAdminResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RemoveChannelAdminReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.Uid))
	return n
}

func (m *RemoveChannelAdminResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelAdminSizeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	return n
}

func (m *GetChannelAdminSizeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Size_))
	return n
}

func (m *CleanChannelAdminReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	return n
}

func (m *CleanChannelAdminResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ChannelStat) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.OnlinePeak))
	n += 1 + sovChannelol(uint64(m.OnlinePeakAt))
	return n
}

func (m *GetChannelStatReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	return n
}

func (m *GetChannelStatResp) Size() (n int) {
	var l int
	_ = l
	if m.Stat != nil {
		l = m.Stat.Size()
		n += 1 + l + sovChannelol(uint64(l))
	}
	return n
}

func (m *HoldMicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Uid))
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	return n
}

func (m *HoldMicResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ReleaseMicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Uid))
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	return n
}

func (m *ReleaseMicResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *MuteChannelMemberReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.Uid))
	return n
}

func (m *MuteChannelMemberResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UnmuteChannelMemberReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	n += 1 + sovChannelol(uint64(m.Uid))
	return n
}

func (m *UnmuteChannelMemberResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllChannelOnlineMemberCountReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllChannelOnlineMemberCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.OnlineCount))
	return n
}

func (m *GetLiveChannelListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetLiveChannelListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelol(uint64(e))
		}
	}
	return n
}

func (m *CheckUserIsChannelAdminReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.AppId))
	n += 1 + sovChannelol(uint64(m.ChannelId))
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovChannelol(uint64(e))
		}
	}
	return n
}

func (m *UserAdminRet) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelol(uint64(m.Uid))
	n += 2
	return n
}

func (m *CheckUserIsChannelAdminResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RetList) > 0 {
		for _, e := range m.RetList {
			l = e.Size()
			n += 1 + l + sovChannelol(uint64(l))
		}
	}
	return n
}

func (m *ExpireChannelMemberReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovChannelol(uint64(e))
		}
	}
	return n
}

func (m *ExpireChannelMemberResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovChannelol(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelol(x uint64) (n int) {
	return sovChannelol(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ChannelMember) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMember: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMember: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsHoldmic", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsHoldmic = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsMute", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMute = bool(v != 0)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CostValue", wireType)
			}
			m.CostValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CostValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsHavePcHelper", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsHavePcHelper = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsRobot", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsRobot = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Flag", wireType)
			}
			m.Flag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Flag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserChannelResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MemberInfo == nil {
				m.MemberInfo = &ChannelMember{}
			}
			if err := m.MemberInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMemberListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMemberListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMemberListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartIdx", wireType)
			}
			m.StartIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("start_idx")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMemberListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMemberListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMemberListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberList = append(m.MemberList, &ChannelMember{})
			if err := m.MemberList[len(m.MemberList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllSize", wireType)
			}
			m.AllSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AllSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("all_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMemberUidListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMemberUidListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMemberUidListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartIdx", wireType)
			}
			m.StartIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("start_idx")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMemberUidListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMemberUidListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMemberUidListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MemberUidList = append(m.MemberUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelol
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelol
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MemberUidList = append(m.MemberUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMemberListInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMemberListInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMemberListInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberList = append(m.MemberList, &ChannelMember{})
			if err := m.MemberList[len(m.MemberList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllSize", wireType)
			}
			m.AllSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AllSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("all_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetChannelMemberListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetChannelMemberListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetChannelMemberListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelol
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelol
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberCount", wireType)
			}
			m.MemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("member_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetChannelMemberListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetChannelMemberListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetChannelMemberListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelmemberinfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelmemberinfoList = append(m.ChannelmemberinfoList, &ChannelMemberListInfo{})
			if err := m.ChannelmemberinfoList[len(m.ChannelmemberinfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyPcHelperJoinReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyPcHelperJoinReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyPcHelperJoinReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyPcHelperJoinResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyPcHelperJoinResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyPcHelperJoinResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NobilityInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NobilityInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NobilityInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KeepValue", wireType)
			}
			m.KeepValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeepValue |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CycleTs", wireType)
			}
			m.CycleTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CycleTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Invisible", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Invisible = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HistoryGiftCost", wireType)
			}
			m.HistoryGiftCost = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HistoryGiftCost |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsMute", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMute = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsRobot", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsRobot = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPchelper", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPchelper = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdmin", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdmin = bool(v != 0)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsRoomHasPwd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsRoomHasPwd = bool(v != 0)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NobilityInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NobilityInfo == nil {
				m.NobilityInfo = &NobilityInfo{}
			}
			if err := m.NobilityInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowFriendUid", wireType)
			}
			m.FollowFriendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FollowFriendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HideFootprint", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HideFootprint = bool(v != 0)
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastChannelId", wireType)
			}
			m.LastChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 18:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelViewId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelViewId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelMemberResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAlreadyIn", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAlreadyIn = bool(v != 0)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberSize", wireType)
			}
			m.MemberSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdminMemberSize", wireType)
			}
			m.AdminMemberSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdminMemberSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerMsTs", wireType)
			}
			m.ServerMsTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerMsTs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdmin", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdmin = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsChangeChannel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsChangeChannel = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelCreateUid", wireType)
			}
			m.ChannelCreateUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelCreateUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelViewId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelViewId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelMemberResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Exist", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Exist = bool(v != 0)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Duration", wireType)
			}
			m.Duration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Duration |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeftMemberCount", wireType)
			}
			m.LeftMemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeftMemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeftAdminCount", wireType)
			}
			m.LeftAdminCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeftAdminCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerMsTs", wireType)
			}
			m.ServerMsTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerMsTs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReturnMembers", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReturnMembers = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Members = append(m.Members, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelol
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelol
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Members = append(m.Members, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field Members", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MemberFootprint) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MemberFootprint: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MemberFootprint: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMemberFootprintReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMemberFootprintReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMemberFootprintReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMemberFootprintResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMemberFootprintResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMemberFootprintResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Footprints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Footprints = append(m.Footprints, &MemberFootprint{})
			if err := m.Footprints[len(m.Footprints)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMemberResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Member", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Member == nil {
				m.Member = &ChannelMember{}
			}
			if err := m.Member.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMemberSizeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMemberSizeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMemberSizeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMemberSizeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMemberSizeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMemberSizeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetChannelMemberSizeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetChannelMemberSizeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetChannelMemberSizeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelol
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelol
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetChannelMemberSizeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetChannelMemberSizeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetChannelMemberSizeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MembersizeList = append(m.MembersizeList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelol
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelol
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MembersizeList = append(m.MembersizeList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MembersizeList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserChannelIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserChannelIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserChannelIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserChannelId) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserChannelId: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserChannelId: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Flag", wireType)
			}
			m.Flag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Flag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserChannelIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserChannelIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserChannelIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Result == nil {
				m.Result = &UserChannelId{}
			}
			if err := m.Result.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserChannelIdReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserChannelIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserChannelIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelol
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelol
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserChannelIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserChannelIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserChannelIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Results", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Results = append(m.Results, &UserChannelId{})
			if err := m.Results[len(m.Results)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelAdminReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelAdminReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelAdminReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelAdminResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelAdminResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelAdminResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelAdminReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelAdminReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelAdminReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelAdminResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelAdminResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelAdminResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelAdminSizeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelAdminSizeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelAdminSizeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelAdminSizeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelAdminSizeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelAdminSizeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanChannelAdminReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CleanChannelAdminReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CleanChannelAdminReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanChannelAdminResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CleanChannelAdminResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CleanChannelAdminResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelStat) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelStat: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelStat: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlinePeak", wireType)
			}
			m.OnlinePeak = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlinePeak |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlinePeakAt", wireType)
			}
			m.OnlinePeakAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlinePeakAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("online_peak")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("online_peak_at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelStatReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelStatReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelStatReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelStatResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelStatResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelStatResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Stat", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Stat == nil {
				m.Stat = &ChannelStat{}
			}
			if err := m.Stat.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HoldMicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HoldMicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HoldMicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HoldMicResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HoldMicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HoldMicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReleaseMicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReleaseMicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReleaseMicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReleaseMicResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReleaseMicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReleaseMicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MuteChannelMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MuteChannelMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MuteChannelMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MuteChannelMemberResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MuteChannelMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MuteChannelMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnmuteChannelMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UnmuteChannelMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UnmuteChannelMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnmuteChannelMemberResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UnmuteChannelMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UnmuteChannelMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllChannelOnlineMemberCountReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllChannelOnlineMemberCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllChannelOnlineMemberCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllChannelOnlineMemberCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllChannelOnlineMemberCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllChannelOnlineMemberCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineCount", wireType)
			}
			m.OnlineCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("online_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLiveChannelListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLiveChannelListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLiveChannelListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLiveChannelListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLiveChannelListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLiveChannelListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelol
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelol
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserIsChannelAdminReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserIsChannelAdminReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserIsChannelAdminReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelol
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelol
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserAdminRet) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserAdminRet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserAdminRet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdmin", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdmin = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_admin")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserIsChannelAdminResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserIsChannelAdminResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserIsChannelAdminResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelol
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RetList = append(m.RetList, &UserAdminRet{})
			if err := m.RetList[len(m.RetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExpireChannelMemberReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExpireChannelMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExpireChannelMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelol
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelol
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExpireChannelMemberResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExpireChannelMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExpireChannelMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelol(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelol
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelol(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelol
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelol
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelol
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelol
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelol(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelol = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelol   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/channelol/channelol.proto", fileDescriptorChannelol) }

var fileDescriptorChannelol = []byte{
	// 2766 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x3a, 0x5d, 0x6f, 0x1b, 0xc7,
	0xb5, 0x59, 0x51, 0x1f, 0xd4, 0x91, 0x28, 0x51, 0x63, 0x51, 0x5a, 0x51, 0x96, 0xbc, 0x1e, 0x27,
	0xb6, 0x11, 0xcb, 0x72, 0xe2, 0xdc, 0x7b, 0x81, 0x2b, 0x08, 0x4a, 0x64, 0xc5, 0xb1, 0xd8, 0x4a,
	0xb6, 0x2b, 0xdb, 0x69, 0x51, 0xc3, 0xd8, 0xae, 0xb8, 0x43, 0x6b, 0xa0, 0x25, 0x77, 0xcc, 0x59,
	0xca, 0x52, 0x80, 0x36, 0x49, 0x5b, 0xb4, 0x45, 0xd0, 0x87, 0x26, 0x4f, 0x7d, 0x6c, 0x01, 0x3f,
	0x15, 0x28, 0xd0, 0x9f, 0xd0, 0xbe, 0xe5, 0x31, 0xbf, 0xa0, 0x68, 0x5d, 0x14, 0xc8, 0x4b, 0xff,
	0x43, 0x31, 0xb3, 0xbb, 0xe4, 0xcc, 0x72, 0x77, 0x45, 0x34, 0x8a, 0xfb, 0x22, 0x68, 0xcf, 0x39,
	0x33, 0xe7, 0x73, 0xce, 0x97, 0x04, 0x4b, 0xbc, 0x5d, 0xbf, 0x51, 0x3f, 0x70, 0x5a, 0x2d, 0xe2,
	0xf9, 0x5e, 0xef, 0xb7, 0x55, 0xd6, 0xf6, 0x03, 0x1f, 0x8d, 0x47, 0x80, 0x7b, 0x3b, 0xd5, 0xd7,
	0xeb, 0x7e, 0xb3, 0xe9, 0xb7, 0x6e, 0x04, 0xde, 0x11, 0xa3, 0xf5, 0x43, 0x8f, 0xdc, 0xe0, 0x87,
	0xfb, 0x1d, 0xea, 0x05, 0xb4, 0x15, 0x9c, 0x30, 0x12, 0x1e, 0xc0, 0x5f, 0x0c, 0x41, 0x69, 0x2b,
	0x3c, 0xb3, 0x4b, 0x9a, 0xfb, 0xa4, 0x8d, 0xe6, 0xa0, 0xd0, 0xa1, 0xae, 0x69, 0x58, 0x43, 0x57,
	0x4b, 0xb7, 0x86, 0xbf, 0xfc, 0xeb, 0x85, 0xd7, 0xf6, 0x04, 0x00, 0x5d, 0x02, 0xa0, 0xdc, 0x3e,
	0xf0, 0x3d, 0xb7, 0x49, 0xeb, 0xe6, 0x90, 0x65, 0x5c, 0x2d, 0x46, 0xe8, 0x71, 0xca, 0xb7, 0x43,
	0x30, 0x5a, 0x82, 0x31, 0xca, 0xed, 0x66, 0x27, 0x20, 0x66, 0x41, 0xa1, 0x18, 0xa5, 0x7c, 0xb7,
	0x13, 0x10, 0x71, 0x47, 0xdd, 0xe7, 0x81, 0x7d, 0xe4, 0x78, 0x1d, 0x62, 0x0e, 0x5b, 0x46, 0x97,
	0xc5, 0xb8, 0x80, 0x7f, 0x28, 0xc0, 0x68, 0x16, 0x86, 0x02, 0x6e, 0x8e, 0x28, 0xc8, 0xa1, 0x80,
	0xa3, 0x1b, 0x30, 0x23, 0xd8, 0x3b, 0x47, 0xc4, 0x66, 0x75, 0xfb, 0x80, 0x78, 0x8c, 0xb4, 0xcd,
	0x51, 0x85, 0xc7, 0x14, 0xe5, 0xdb, 0xce, 0x11, 0xb9, 0x5f, 0xdf, 0x96, 0x38, 0x74, 0x01, 0x8a,
	0x94, 0xdb, 0x6d, 0x7f, 0xdf, 0x0f, 0xcc, 0x31, 0x85, 0x6e, 0x8c, 0xf2, 0x3d, 0x01, 0x44, 0x26,
	0x0c, 0x37, 0x3c, 0xe7, 0xa9, 0x59, 0x54, 0x38, 0x49, 0x08, 0xbe, 0x06, 0x33, 0x77, 0x48, 0xf0,
	0x88, 0x93, 0x76, 0x64, 0x9a, 0x3d, 0xf2, 0x2c, 0xcb, 0x2e, 0xf8, 0x73, 0x03, 0x50, 0x92, 0x9a,
	0x33, 0xa9, 0x6a, 0xf8, 0x69, 0x27, 0x4e, 0xc5, 0x3e, 0xaa, 0xb9, 0x68, 0x11, 0x46, 0x1d, 0xc6,
	0x04, 0xc1, 0x90, 0x22, 0xc4, 0x88, 0xc3, 0x58, 0xcd, 0x45, 0xff, 0x0f, 0x13, 0x4d, 0xe9, 0x12,
	0x9b, 0xb6, 0x1a, 0xbe, 0xb4, 0xe7, 0xc4, 0x4d, 0x73, 0xb5, 0xeb, 0xe1, 0x55, 0xcd, 0x6f, 0x7b,
	0x10, 0x12, 0xd7, 0x5a, 0x0d, 0x1f, 0xff, 0x18, 0xe6, 0xef, 0x90, 0x40, 0xc3, 0xef, 0x50, 0x1e,
	0x08, 0x35, 0x06, 0x92, 0xeb, 0x22, 0x8c, 0xf3, 0xc0, 0x69, 0x07, 0x36, 0x75, 0x8f, 0xcd, 0x21,
	0x85, 0xa6, 0x28, 0xc1, 0x35, 0xf7, 0x18, 0x55, 0x61, 0xc4, 0xa3, 0x4d, 0x1a, 0x98, 0x05, 0x05,
	0x1d, 0x82, 0xf0, 0x11, 0x98, 0xe9, 0xec, 0x39, 0x53, 0xb4, 0xf2, 0x28, 0x0f, 0x4c, 0xc3, 0x2a,
	0x0c, 0xa2, 0x95, 0x38, 0x2e, 0x3c, 0xea, 0x78, 0x9e, 0xcd, 0xe9, 0x47, 0x44, 0x13, 0x6a, 0xcc,
	0xf1, 0xbc, 0x07, 0xf4, 0x23, 0x82, 0x3f, 0x31, 0xa0, 0x9a, 0x64, 0xfc, 0x88, 0xba, 0xaf, 0x52,
	0xf5, 0xdb, 0xb0, 0x98, 0x29, 0x01, 0x67, 0xe8, 0x32, 0x4c, 0x47, 0xda, 0x77, 0xa8, 0xdb, 0xb3,
	0x40, 0x69, 0xaf, 0xd4, 0x54, 0x69, 0xf1, 0x6f, 0x0d, 0xa8, 0xf4, 0xd9, 0x4f, 0xb8, 0x76, 0x30,
	0x25, 0x12, 0x46, 0x1e, 0xfa, 0x0f, 0x8d, 0x5c, 0x48, 0x33, 0xb2, 0x0f, 0xe7, 0x6f, 0x39, 0x41,
	0xfd, 0x20, 0x2b, 0xc0, 0x2e, 0xc3, 0x74, 0x4f, 0x40, 0x4d, 0xc5, 0xae, 0x7c, 0x92, 0xd1, 0x15,
	0x98, 0x8c, 0x64, 0xac, 0xfb, 0x9d, 0x56, 0xa0, 0xd9, 0x3a, 0x92, 0x7e, 0x4b, 0x20, 0xf0, 0x31,
	0x2c, 0xe5, 0x30, 0xe4, 0x0c, 0x7d, 0x1f, 0xe6, 0xa2, 0xab, 0xc3, 0x63, 0xe2, 0xb9, 0xa8, 0xd1,
	0x65, 0x65, 0x29, 0x1e, 0x1b, 0x75, 0xaf, 0xd2, 0x77, 0x5e, 0x7a, 0xe1, 0x21, 0x54, 0xee, 0xfa,
	0x01, 0x6d, 0x9c, 0xc4, 0x49, 0xe5, 0x3b, 0x3e, 0x6d, 0xe5, 0xe4, 0x82, 0x84, 0x73, 0x86, 0x52,
	0x9d, 0x83, 0x4d, 0x98, 0x4b, 0xbb, 0x95, 0x33, 0xfc, 0x27, 0x03, 0x26, 0xef, 0xfa, 0xfb, 0xd4,
	0xa3, 0xc1, 0x89, 0x74, 0xb6, 0x88, 0x34, 0x72, 0x44, 0x3c, 0xd3, 0x50, 0xd3, 0x83, 0x04, 0x09,
	0x5c, 0x98, 0x46, 0x45, 0xea, 0x18, 0x8e, 0x71, 0x12, 0x24, 0xe4, 0x38, 0x24, 0x84, 0x45, 0x79,
	0xb6, 0xa0, 0x10, 0x8c, 0x0b, 0x78, 0x98, 0x67, 0x2f, 0x40, 0xb1, 0x7e, 0x52, 0xf7, 0x88, 0x1d,
	0x70, 0x2d, 0x15, 0x8f, 0x49, 0xe8, 0x43, 0x8e, 0x30, 0x8c, 0xd3, 0xd6, 0x11, 0xe5, 0x74, 0xdf,
	0x23, 0x32, 0x1f, 0xf7, 0x12, 0x7e, 0x0c, 0xc6, 0xff, 0x1a, 0x81, 0x73, 0x9b, 0xae, 0xab, 0xc7,
	0x13, 0x79, 0xa6, 0x64, 0x36, 0xa3, 0x3f, 0xb3, 0x0d, 0x62, 0x26, 0x11, 0x1f, 0x31, 0x91, 0xa8,
	0x57, 0x5a, 0x30, 0x4e, 0x44, 0x98, 0x87, 0x27, 0x8c, 0xc4, 0xce, 0x18, 0x4e, 0x3a, 0xe3, 0x2d,
	0x98, 0x39, 0xa0, 0x3c, 0xf0, 0xdb, 0x27, 0xf6, 0x53, 0xda, 0x08, 0x6c, 0x51, 0x61, 0xb4, 0xb2,
	0x32, 0x1d, 0xa1, 0xef, 0xd0, 0x46, 0xb0, 0xe5, 0xf3, 0x40, 0xad, 0x5e, 0xa3, 0x29, 0xd5, 0xeb,
	0xd4, 0x8a, 0xf2, 0x06, 0x4c, 0x50, 0x6e, 0xb3, 0x7a, 0x54, 0x9d, 0x8a, 0x0a, 0x0d, 0x50, 0x7e,
	0x3f, 0x82, 0x47, 0xf7, 0x38, 0x6e, 0x93, 0xb6, 0xcc, 0x71, 0xfd, 0x9e, 0x4d, 0x01, 0x44, 0x37,
	0x01, 0xc5, 0xaa, 0xbb, 0x94, 0x33, 0xcf, 0x39, 0x11, 0x76, 0x02, 0x45, 0xf4, 0x72, 0x84, 0x7f,
	0x3f, 0x44, 0xd7, 0x5c, 0x74, 0x0d, 0xa6, 0xa5, 0x70, 0x7e, 0xd3, 0x3e, 0x70, 0xb8, 0xcd, 0x9e,
	0xbb, 0xe6, 0x84, 0x72, 0xf7, 0xa4, 0x90, 0xd1, 0x6f, 0x6e, 0x3b, 0xfc, 0xfe, 0x73, 0x99, 0xe4,
	0x9a, 0x4e, 0xfb, 0x90, 0x88, 0x2c, 0x67, 0x4e, 0x2a, 0xf7, 0x16, 0x43, 0x70, 0xcd, 0x45, 0xeb,
	0x50, 0x6a, 0x45, 0xa1, 0x18, 0xd6, 0x9f, 0x92, 0xac, 0x3f, 0xf3, 0xca, 0x5b, 0x52, 0x43, 0x75,
	0x6f, 0xb2, 0xa5, 0x06, 0xee, 0x79, 0x18, 0xe5, 0x7e, 0xa7, 0x5d, 0x27, 0xe6, 0x94, 0x72, 0x7b,
	0x04, 0x13, 0x9e, 0x69, 0xf8, 0x9e, 0xe7, 0x3f, 0xb7, 0x1b, 0x6d, 0x4a, 0x5a, 0xae, 0x48, 0x86,
	0xe6, 0xb4, 0xea, 0x99, 0x10, 0xfd, 0x81, 0xc4, 0x3e, 0xa2, 0x42, 0xbb, 0xa9, 0x03, 0xea, 0x12,
	0xbb, 0xe1, 0xfb, 0x01, 0x6b, 0xd3, 0x56, 0x60, 0x96, 0x15, 0xe5, 0x4a, 0x02, 0xf7, 0x41, 0x8c,
	0x42, 0x2b, 0x30, 0xed, 0x39, 0x3c, 0xb0, 0x95, 0x18, 0x9b, 0x51, 0x2e, 0x2f, 0x09, 0xe4, 0x56,
	0x37, 0xce, 0x56, 0x7a, 0xf9, 0xea, 0x88, 0x92, 0xe7, 0x82, 0x1a, 0x59, 0xc6, 0xd5, 0xf1, 0x98,
	0x3a, 0x42, 0x7e, 0x48, 0xc9, 0xf3, 0x9a, 0x8b, 0xff, 0x62, 0xc0, 0x6c, 0x7f, 0xbc, 0x73, 0x86,
	0xae, 0x42, 0x49, 0x38, 0xd5, 0x6b, 0x13, 0xc7, 0x15, 0x16, 0x93, 0x71, 0x1f, 0x0b, 0x38, 0x41,
	0xf9, 0x66, 0x88, 0xa9, 0xb5, 0x44, 0x94, 0x44, 0x89, 0x2f, 0xaa, 0x64, 0x3d, 0xd1, 0xa2, 0x44,
	0x2c, 0xf2, 0xac, 0x30, 0x92, 0x0c, 0x11, 0x5b, 0x25, 0x2e, 0xa8, 0x46, 0x92, 0xe8, 0xdd, 0xde,
	0x89, 0xcb, 0x30, 0xc9, 0x49, 0xfb, 0x88, 0xb4, 0xed, 0x26, 0x8f, 0x1f, 0x75, 0xfc, 0xee, 0x21,
	0xc4, 0xec, 0xf2, 0x87, 0x1c, 0xff, 0xbe, 0x00, 0x73, 0x7b, 0xa4, 0xe9, 0x1f, 0x91, 0xdc, 0x67,
	0x3b, 0xf4, 0xdf, 0x79, 0xb6, 0xea, 0xeb, 0x18, 0x49, 0x7b, 0x1d, 0x6f, 0xc9, 0x4e, 0x50, 0x5c,
	0xf5, 0x94, 0xc4, 0x3e, 0xd6, 0xde, 0xeb, 0x34, 0xe5, 0x5b, 0x12, 0x1b, 0xa9, 0x97, 0xf1, 0x9e,
	0xc6, 0x72, 0xdf, 0x93, 0x72, 0xa6, 0xde, 0x26, 0x4e, 0x40, 0x64, 0x90, 0x16, 0x53, 0xce, 0x6c,
	0x49, 0xb4, 0x88, 0xd2, 0x45, 0x18, 0xf5, 0x99, 0xa4, 0x1b, 0x57, 0x93, 0x9e, 0xcf, 0x04, 0x32,
	0x25, 0xce, 0x20, 0x3b, 0xce, 0xfe, 0x6e, 0xc0, 0x7c, 0xaa, 0x8f, 0x38, 0x13, 0x99, 0x9f, 0x1c,
	0x87, 0xe5, 0xad, 0xa7, 0x74, 0x08, 0x42, 0x16, 0x14, 0xdd, 0x4e, 0xdb, 0x09, 0xa8, 0xdf, 0xd2,
	0x22, 0xab, 0x0b, 0x15, 0xe6, 0xf3, 0x48, 0x23, 0xb0, 0xb5, 0xe2, 0xab, 0xc5, 0x95, 0x40, 0xef,
	0xf6, 0x0a, 0x30, 0x5a, 0x85, 0xb2, 0x3c, 0x11, 0x86, 0x63, 0x78, 0x40, 0x2d, 0x18, 0x53, 0x02,
	0x2b, 0x7d, 0x13, 0xd2, 0x27, 0xe3, 0x70, 0x24, 0x23, 0x0e, 0x3f, 0x35, 0xa0, 0xac, 0xe9, 0x78,
	0x36, 0x85, 0xe3, 0x1a, 0x4c, 0xb5, 0x49, 0xd0, 0x69, 0xc7, 0x2f, 0x87, 0x6b, 0xa3, 0x48, 0x29,
	0xc4, 0x85, 0xfa, 0x71, 0x7c, 0x1d, 0x66, 0x12, 0x22, 0x70, 0x86, 0x4c, 0x18, 0x8b, 0x8f, 0x86,
	0xad, 0x4b, 0xfc, 0x89, 0x77, 0x61, 0x3a, 0x3c, 0xd9, 0xcb, 0x36, 0x59, 0xbd, 0x00, 0x86, 0xf1,
	0x80, 0x36, 0x09, 0x0f, 0x9c, 0x26, 0xd3, 0x45, 0xed, 0x82, 0xf1, 0x8f, 0xe0, 0x7c, 0xb2, 0xab,
	0xe9, 0x5e, 0x7c, 0x26, 0xc6, 0xc0, 0x8f, 0x61, 0x29, 0x87, 0x03, 0x67, 0x68, 0x0d, 0xa0, 0x9b,
	0x54, 0x79, 0xd4, 0x30, 0x55, 0x95, 0x24, 0x9f, 0x3c, 0xa3, 0x50, 0x63, 0x1f, 0xce, 0x25, 0x2f,
	0x3f, 0x1b, 0x17, 0x46, 0x36, 0x2d, 0x24, 0x67, 0xad, 0x6d, 0x98, 0xed, 0x67, 0xc8, 0x19, 0x7a,
	0x0b, 0x46, 0x43, 0x0f, 0x49, 0x8e, 0x79, 0xad, 0x6e, 0x44, 0x87, 0x1f, 0xf7, 0x4f, 0x48, 0x22,
	0x87, 0x9e, 0x8d, 0xd1, 0xff, 0xa7, 0x7f, 0xfe, 0x09, 0x2f, 0x97, 0xb1, 0x35, 0x2c, 0x33, 0xb9,
	0x7a, 0xb7, 0x84, 0xe0, 0x0f, 0x32, 0x1a, 0xeb, 0x58, 0xae, 0x01, 0x1b, 0x6b, 0xbc, 0x9d, 0xd1,
	0x2f, 0x77, 0x45, 0xb8, 0x12, 0x0f, 0x21, 0x82, 0xad, 0x7a, 0xd1, 0x54, 0x0f, 0x2c, 0x6f, 0xba,
	0x2e, 0xfd, 0xab, 0x4c, 0xb6, 0x35, 0x37, 0x6f, 0x12, 0xfe, 0x99, 0x01, 0x25, 0x8d, 0x38, 0xf3,
	0x6d, 0xe4, 0xce, 0xbd, 0xba, 0x89, 0x0b, 0xe9, 0x11, 0x12, 0x0f, 0xef, 0xc3, 0x7d, 0xc3, 0x7b,
	0x18, 0x23, 0x09, 0xa1, 0xc3, 0x18, 0x69, 0x13, 0xde, 0xf1, 0x82, 0x94, 0x18, 0xd1, 0xa9, 0x23,
	0x3a, 0xfc, 0xbf, 0x60, 0xc6, 0x86, 0xec, 0xb3, 0xc1, 0x02, 0x14, 0x13, 0x13, 0xdc, 0x58, 0x27,
	0x9a, 0xdd, 0xee, 0xc1, 0x42, 0xc6, 0x31, 0xce, 0xd0, 0x4d, 0x18, 0x0b, 0x6f, 0xe7, 0x29, 0xa3,
	0xaf, 0x4e, 0x1e, 0x13, 0xe2, 0x16, 0xa0, 0x5e, 0xcb, 0x21, 0xf3, 0xec, 0xb7, 0xfb, 0xca, 0x2a,
	0x6a, 0x4b, 0x1f, 0xf1, 0xe3, 0x0c, 0x3f, 0x83, 0x8a, 0x96, 0x2a, 0x5f, 0x81, 0x24, 0x66, 0xa2,
	0x51, 0xe9, 0x09, 0xf3, 0x43, 0x98, 0xeb, 0xc5, 0xb7, 0x04, 0x9f, 0xdd, 0xf3, 0x7d, 0x47, 0xcd,
	0x0d, 0xca, 0xdd, 0xb9, 0xaf, 0xf7, 0x07, 0x30, 0xbb, 0xe5, 0x11, 0xa7, 0x75, 0xe6, 0xc6, 0xc1,
	0xf3, 0x50, 0x49, 0xb9, 0x99, 0x8b, 0xea, 0x31, 0x11, 0xc1, 0x1e, 0x04, 0x8e, 0x9c, 0x3e, 0xfc,
	0x96, 0x47, 0x5b, 0xc4, 0x66, 0xc4, 0x39, 0xd4, 0x1e, 0x1d, 0x84, 0x88, 0xfb, 0xc4, 0x39, 0x44,
	0x6f, 0xc2, 0x94, 0x42, 0x66, 0x3b, 0xfa, 0xe4, 0x3d, 0xd9, 0xa3, 0xdc, 0x0c, 0xf0, 0x23, 0xb9,
	0x08, 0x53, 0x98, 0x9c, 0x8d, 0x46, 0xef, 0xc9, 0x8d, 0x99, 0x76, 0x2d, 0x67, 0xe8, 0x4d, 0x18,
	0xe6, 0x81, 0x13, 0x3f, 0xcf, 0xb9, 0xfe, 0x14, 0x2e, 0x29, 0x25, 0x0d, 0x6e, 0x00, 0x6c, 0xfb,
	0x9e, 0xbb, 0x4b, 0xeb, 0x79, 0xe3, 0xf8, 0x37, 0x4e, 0x33, 0xb8, 0x04, 0x13, 0x5d, 0x3e, 0x9c,
	0x61, 0x0a, 0xa5, 0x3d, 0xe2, 0x11, 0x87, 0x93, 0x6f, 0x9d, 0x73, 0x19, 0xa6, 0x54, 0x56, 0x9c,
	0x61, 0x06, 0xb3, 0x62, 0x0c, 0x7d, 0x85, 0xe5, 0x76, 0x1e, 0x2a, 0x29, 0x1c, 0x39, 0xc3, 0x6d,
	0x98, 0x7b, 0xd4, 0x6a, 0xbe, 0x5a, 0x61, 0x16, 0x60, 0x3e, 0x95, 0x27, 0x67, 0xf8, 0x12, 0x5c,
	0xbc, 0x43, 0x82, 0x4d, 0xcf, 0x8b, 0x50, 0xf7, 0x64, 0x0c, 0x2b, 0x2d, 0xec, 0x1e, 0x79, 0x86,
	0x77, 0x01, 0x9f, 0x46, 0x24, 0x6b, 0x63, 0xf4, 0x02, 0xa2, 0x3e, 0x57, 0xf5, 0x6c, 0xf4, 0xbc,
	0xc2, 0xad, 0xd4, 0x3c, 0x54, 0xee, 0x90, 0x60, 0x87, 0x76, 0x73, 0x53, 0xb4, 0xff, 0xc2, 0xef,
	0xc9, 0xcc, 0xd4, 0x87, 0x08, 0x97, 0x7f, 0x03, 0x15, 0xf0, 0x13, 0xa8, 0x6e, 0x1d, 0x90, 0xfa,
	0xa1, 0x28, 0x07, 0x35, 0x7e, 0xf6, 0xd9, 0x56, 0xad, 0x5d, 0x05, 0xbd, 0x76, 0xdd, 0x81, 0x49,
	0xc1, 0x35, 0x62, 0x96, 0xdd, 0xdc, 0xaa, 0x43, 0x9a, 0xe0, 0x92, 0x1c, 0xd2, 0xf0, 0xf7, 0x60,
	0x31, 0x53, 0x07, 0x59, 0x06, 0x8b, 0x6d, 0x12, 0xa8, 0x4b, 0xba, 0xf9, 0x44, 0x1d, 0x8c, 0x45,
	0x10, 0x65, 0x30, 0x90, 0xb2, 0xbd, 0x03, 0x73, 0xb7, 0x8f, 0x19, 0x6d, 0xf7, 0x07, 0x5d, 0x4e,
	0x31, 0x5e, 0x80, 0xf9, 0xd4, 0x43, 0x9c, 0xbd, 0xf9, 0x31, 0x2c, 0xc4, 0xa1, 0x10, 0x77, 0xc6,
	0x9e, 0xf3, 0xf4, 0x16, 0x0d, 0x9a, 0x0e, 0x43, 0x73, 0x80, 0xb6, 0xb6, 0x37, 0xef, 0xde, 0xbd,
	0xbd, 0x73, 0x6f, 0xc7, 0xae, 0x3d, 0xb0, 0x37, 0xdf, 0xdf, 0xad, 0xdd, 0x2d, 0x1b, 0xc8, 0x84,
	0x59, 0x0d, 0xbe, 0x7d, 0x6f, 0xe7, 0xfd, 0xdd, 0xda, 0x56, 0x79, 0x08, 0x55, 0x60, 0x46, 0xc3,
	0xec, 0x3e, 0x7a, 0x78, 0xbb, 0x3c, 0x8c, 0x16, 0xa0, 0xa2, 0x81, 0xef, 0x6f, 0x6d, 0xdf, 0xde,
	0xb9, 0x7f, 0x7b, 0xaf, 0x5c, 0xbc, 0xf9, 0xd5, 0x22, 0xf4, 0xfe, 0x5e, 0x83, 0x0e, 0x60, 0x4a,
	0xef, 0x18, 0xd0, 0x79, 0xc5, 0x24, 0x7d, 0x7f, 0x8f, 0xa8, 0x2e, 0xe5, 0x60, 0x39, 0xc3, 0x0b,
	0x9f, 0xbc, 0xf8, 0xba, 0x30, 0xf4, 0x99, 0xf8, 0xd1, 0x59, 0xfb, 0xe2, 0xc5, 0xd7, 0x85, 0xe2,
	0xf5, 0x8e, 0xb5, 0xde, 0xa1, 0xee, 0x06, 0x7a, 0x61, 0xf4, 0xb7, 0xd1, 0x72, 0x25, 0x8b, 0xf5,
	0x2b, 0xd3, 0xd6, 0xbb, 0xd5, 0x4b, 0xa7, 0xd2, 0x70, 0x86, 0xb7, 0x04, 0xf3, 0x82, 0x60, 0x3e,
	0x7a, 0xbc, 0xb6, 0xbf, 0xe6, 0x49, 0x01, 0x56, 0xaf, 0x1f, 0x5b, 0xeb, 0xd1, 0x61, 0x8b, 0xba,
	0x1b, 0xd6, 0xe3, 0xeb, 0xfb, 0xd6, 0xfa, 0x3e, 0x79, 0x4a, 0x5b, 0x16, 0x75, 0x8f, 0x37, 0x9e,
	0x58, 0x8f, 0xaf, 0x7b, 0xd6, 0xba, 0xdc, 0xa3, 0x5b, 0x1b, 0x4f, 0xd0, 0x2f, 0x0c, 0x40, 0xfd,
	0x8b, 0x52, 0x64, 0x69, 0x1b, 0xa8, 0x94, 0xed, 0x6c, 0xf5, 0xe2, 0x29, 0x14, 0x9c, 0xe1, 0x6b,
	0x42, 0xc0, 0x61, 0x21, 0xe0, 0x70, 0x67, 0xed, 0x58, 0x8a, 0x67, 0xc6, 0xf6, 0xb1, 0x12, 0x72,
	0xa2, 0x3f, 0x18, 0xbd, 0x8e, 0xae, 0xdf, 0x68, 0x57, 0x14, 0x6e, 0x79, 0x8b, 0xf1, 0xea, 0xd5,
	0xc1, 0x08, 0x39, 0xc3, 0x6b, 0x42, 0xba, 0x11, 0x29, 0xdd, 0x71, 0x64, 0xbc, 0x2b, 0x8a, 0x50,
	0xb6, 0x14, 0xd2, 0xb3, 0xd6, 0xc3, 0x76, 0xdd, 0x92, 0x99, 0xca, 0x92, 0x76, 0xdb, 0x40, 0x2f,
	0x0d, 0x28, 0x27, 0x17, 0x54, 0x68, 0x59, 0x61, 0x9d, 0xb2, 0xad, 0xad, 0x5e, 0xc8, 0xc5, 0x73,
	0x86, 0x7f, 0x6d, 0x08, 0x91, 0x26, 0x84, 0x48, 0x93, 0xc7, 0x6b, 0x9d, 0xb5, 0x60, 0x8d, 0xaf,
	0x35, 0xdb, 0xcc, 0x11, 0xa2, 0x3d, 0xeb, 0x13, 0xad, 0x6b, 0xc8, 0xa0, 0x87, 0x08, 0x4e, 0x18,
	0x11, 0x2e, 0xe7, 0xd6, 0xfa, 0x53, 0xda, 0x08, 0xac, 0xba, 0xff, 0x20, 0x90, 0x2e, 0x6f, 0xae,
	0x58, 0x22, 0xab, 0xbf, 0x2b, 0x7e, 0x6f, 0xaf, 0x58, 0x72, 0xbd, 0x2a, 0x3f, 0xd8, 0x8a, 0x15,
	0xaf, 0x52, 0xe5, 0xb7, 0xb3, 0x62, 0xc9, 0x84, 0xf3, 0xee, 0x13, 0xf4, 0x67, 0x03, 0xce, 0xa5,
	0x6c, 0x47, 0x90, 0xea, 0xf9, 0xf4, 0x0d, 0x57, 0x15, 0x9f, 0x46, 0xc2, 0x19, 0xde, 0x17, 0xca,
	0x4e, 0x0a, 0x65, 0x8b, 0x22, 0x3a, 0x82, 0x35, 0x87, 0x0b, 0x45, 0xbf, 0x3b, 0x88, 0xa2, 0x0f,
	0x23, 0x3d, 0x9d, 0x95, 0x48, 0x58, 0xa1, 0xf3, 0x4a, 0xb8, 0xad, 0xb2, 0x22, 0x9a, 0x77, 0x9f,
	0x58, 0xc8, 0x13, 0xad, 0x84, 0xc2, 0x1e, 0x2d, 0x66, 0x09, 0x26, 0xa4, 0x3e, 0x9f, 0x8d, 0xe4,
	0x0c, 0x5b, 0x42, 0xde, 0x92, 0x7c, 0xeb, 0x61, 0x2c, 0x4f, 0x27, 0x24, 0x45, 0x9f, 0x1b, 0xb0,
	0x90, 0xb9, 0x07, 0xd0, 0x42, 0x38, 0x6f, 0x1f, 0xa1, 0x85, 0x70, 0xee, 0x5a, 0x01, 0x5f, 0x14,
	0x22, 0x4d, 0x29, 0x22, 0x95, 0x93, 0xc6, 0x43, 0x9f, 0x1a, 0x50, 0x4e, 0x5e, 0xa2, 0x45, 0x6a,
	0xca, 0x6e, 0x41, 0x8b, 0xd4, 0xb4, 0x55, 0x00, 0x5e, 0x11, 0x8c, 0xa7, 0xa3, 0xb7, 0x13, 0x66,
	0xbe, 0x85, 0x4c, 0xbf, 0xa1, 0x9f, 0xa6, 0xa4, 0x42, 0xb9, 0x4b, 0xcd, 0x4b, 0x85, 0xd1, 0xa4,
	0x91, 0x9b, 0x0a, 0xe3, 0x89, 0x21, 0x34, 0x44, 0x39, 0xd7, 0x10, 0xbe, 0xb4, 0x83, 0x3e, 0x39,
	0x2f, 0x67, 0x66, 0x77, 0x39, 0x7f, 0x26, 0xed, 0xd0, 0x37, 0x68, 0xe2, 0xaa, 0xe0, 0x3b, 0xa3,
	0xe4, 0xff, 0xf1, 0x9e, 0xd6, 0x9f, 0x19, 0x50, 0x49, 0x1d, 0x51, 0xd1, 0xa5, 0x94, 0x1c, 0xd5,
	0xc7, 0xfb, 0xf5, 0xd3, 0x89, 0x38, 0xc3, 0x97, 0x85, 0x00, 0x48, 0x11, 0xa0, 0x12, 0x09, 0xf0,
	0xf6, 0x8a, 0xd5, 0xa1, 0xee, 0xcd, 0x15, 0x6b, 0x75, 0x75, 0x75, 0xc3, 0x42, 0x3f, 0x81, 0xe9,
	0xc4, 0xb4, 0x89, 0x96, 0x52, 0xd3, 0x51, 0xdc, 0x01, 0x55, 0x97, 0xf3, 0xd0, 0x71, 0x08, 0x9c,
	0x1b, 0x34, 0x04, 0x7e, 0x69, 0x00, 0xea, 0x1f, 0x32, 0xb5, 0x32, 0x93, 0x3a, 0xf6, 0x56, 0x2f,
	0x9e, 0x42, 0x11, 0x4b, 0x32, 0x3b, 0xa8, 0x24, 0x1f, 0xab, 0xeb, 0xb4, 0xee, 0xdc, 0xa9, 0x25,
	0xb5, 0xf4, 0x99, 0xb7, 0x8a, 0x4f, 0x23, 0x89, 0x03, 0xb1, 0x92, 0x1b, 0x88, 0x27, 0x30, 0xd3,
	0x37, 0x69, 0x22, 0x35, 0xd2, 0xd2, 0x26, 0xdc, 0xaa, 0x95, 0x4f, 0x10, 0xb3, 0x9e, 0xcb, 0x65,
	0xcd, 0x64, 0xf7, 0xa3, 0x8e, 0xb3, 0xe7, 0x53, 0x75, 0x8a, 0x86, 0xd0, 0x64, 0xf7, 0x93, 0x98,
	0x25, 0x43, 0x8e, 0xf3, 0xb9, 0x1c, 0x1b, 0x30, 0x16, 0x8d, 0x76, 0xa8, 0xa2, 0x5c, 0xd6, 0x1b,
	0x2b, 0xab, 0x73, 0x69, 0xe0, 0xd8, 0xab, 0xe6, 0xa0, 0x5e, 0x6d, 0x01, 0xf4, 0x06, 0x39, 0x64,
	0x6a, 0x41, 0xa3, 0x8c, 0x92, 0xd5, 0x85, 0x0c, 0x4c, 0xdc, 0xad, 0x2c, 0x68, 0x0c, 0xcd, 0x2c,
	0x86, 0xe8, 0xe7, 0x06, 0xcc, 0xf4, 0x4d, 0x6d, 0x9a, 0x17, 0xd3, 0xa6, 0x48, 0xcd, 0x8b, 0xe9,
	0x43, 0x9f, 0x54, 0xbb, 0x3a, 0xa8, 0xda, 0x9f, 0x19, 0x70, 0x2e, 0x65, 0x5e, 0xd3, 0xa2, 0x39,
	0x7d, 0x86, 0xd4, 0xa2, 0x39, 0x6b, 0xe4, 0x93, 0xc2, 0x2c, 0x0e, 0x2a, 0xcc, 0xe7, 0x59, 0x1d,
	0x9c, 0x7c, 0x60, 0xa7, 0x76, 0x70, 0xf1, 0x33, 0xbb, 0x3a, 0x18, 0x61, 0x1c, 0x7f, 0x4b, 0xb9,
	0xf1, 0xf7, 0x47, 0xa3, 0x7f, 0x05, 0x1d, 0xfd, 0xfb, 0x07, 0x7a, 0x23, 0xa7, 0xb2, 0xf4, 0xfe,
	0xa1, 0xa5, 0x7a, 0x79, 0x10, 0xb2, 0xb8, 0x1d, 0xbf, 0xf0, 0x0d, 0xdb, 0xf1, 0xdf, 0x19, 0x30,
	0x9f, 0x31, 0xd2, 0x69, 0xf2, 0x66, 0x8f, 0xae, 0x9a, 0xbc, 0x39, 0xd3, 0x21, 0xfe, 0x3f, 0x21,
	0xaf, 0xa5, 0x39, 0xf7, 0x52, 0x86, 0x73, 0xe3, 0x62, 0x22, 0x6a, 0x09, 0x3a, 0x86, 0x73, 0x29,
	0xc3, 0x9e, 0x16, 0x72, 0xe9, 0x13, 0xa4, 0x16, 0x72, 0x19, 0xf3, 0x62, 0x58, 0x51, 0x71, 0x4a,
	0x45, 0x7d, 0x7b, 0xa3, 0x3a, 0xfa, 0xab, 0x17, 0x5f, 0x17, 0xfe, 0x79, 0x74, 0xab, 0xfc, 0xe5,
	0xcb, 0x65, 0xe3, 0xab, 0x97, 0xcb, 0xc6, 0xdf, 0x5e, 0x2e, 0x1b, 0xbf, 0xf9, 0xc7, 0xf2, 0x6b,
	0xff, 0x0e, 0x00, 0x00, 0xff, 0xff, 0x2f, 0x34, 0x99, 0xb5, 0xb1, 0x27, 0x00, 0x00,
}
