// Code generated by protoc-gen-gogo.
// source: src/channelol/asyncjob/asyncjob.proto
// DO NOT EDIT!

/*
	Package channelol_asyncjob is a generated protocol buffer package.

	It is generated from these files:
		src/channelol/asyncjob/asyncjob.proto

	It has these top-level messages:
		AddMemberNotify
		RemoveMemberNotify
		ExpiredMembersNotify
		RemoveExpiredMemberNotify
		ChannelDismissNotify
		MemberRankChangeNotify
*/
package channelol_asyncjob

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"

import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

import "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type Type int32

const (
	Type_ET_ADD_CHANNEL_MEMER    Type = 1
	Type_ET_REMOVE_CHANNEL_MEMER Type = 2
	Type_ET_MEMBER_RANK_CHANGE   Type = 3
	Type_ET_EXPIRED_MEMBER_LIST  Type = 4
	Type_ET_REMOVE_EXPIRE_MEMBER Type = 5
	Type_ET_CHANNEL_DISMISS      Type = 6
)

var Type_name = map[int32]string{
	1: "ET_ADD_CHANNEL_MEMER",
	2: "ET_REMOVE_CHANNEL_MEMER",
	3: "ET_MEMBER_RANK_CHANGE",
	4: "ET_EXPIRED_MEMBER_LIST",
	5: "ET_REMOVE_EXPIRE_MEMBER",
	6: "ET_CHANNEL_DISMISS",
}
var Type_value = map[string]int32{
	"ET_ADD_CHANNEL_MEMER":    1,
	"ET_REMOVE_CHANNEL_MEMER": 2,
	"ET_MEMBER_RANK_CHANGE":   3,
	"ET_EXPIRED_MEMBER_LIST":  4,
	"ET_REMOVE_EXPIRE_MEMBER": 5,
	"ET_CHANNEL_DISMISS":      6,
}

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}
func (x Type) String() string {
	return proto.EnumName(Type_name, int32(x))
}
func (x *Type) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Type_value, data, "Type")
	if err != nil {
		return err
	}
	*x = Type(value)
	return nil
}
func (Type) EnumDescriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{0} }

// 房间有人进入
type AddMemberNotify struct {
	ChannelId          uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid                uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	AppId              uint32 `protobuf:"varint,3,opt,name=app_id,json=appId" json:"app_id"`
	ChannelType        uint32 `protobuf:"varint,4,opt,name=channel_type,json=channelType" json:"channel_type"`
	ChannelDisplayId   uint32 `protobuf:"varint,5,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	At                 uint32 `protobuf:"varint,6,opt,name=at" json:"at"`
	AtMs               uint64 `protobuf:"varint,7,opt,name=at_ms,json=atMs" json:"at_ms"`
	IsAdmin            bool   `protobuf:"varint,8,opt,name=is_admin,json=isAdmin" json:"is_admin"`
	RemainOnlineMember uint32 `protobuf:"varint,9,opt,name=remain_online_member,json=remainOnlineMember" json:"remain_online_member"`
	RemainOnlineAdmin  uint32 `protobuf:"varint,10,opt,name=remain_online_admin,json=remainOnlineAdmin" json:"remain_online_admin"`
	IsPwd              bool   `protobuf:"varint,11,opt,name=is_pwd,json=isPwd" json:"is_pwd"`
}

func (m *AddMemberNotify) Reset()                    { *m = AddMemberNotify{} }
func (m *AddMemberNotify) String() string            { return proto.CompactTextString(m) }
func (*AddMemberNotify) ProtoMessage()               {}
func (*AddMemberNotify) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{0} }

func (m *AddMemberNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddMemberNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddMemberNotify) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *AddMemberNotify) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *AddMemberNotify) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *AddMemberNotify) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *AddMemberNotify) GetAtMs() uint64 {
	if m != nil {
		return m.AtMs
	}
	return 0
}

func (m *AddMemberNotify) GetIsAdmin() bool {
	if m != nil {
		return m.IsAdmin
	}
	return false
}

func (m *AddMemberNotify) GetRemainOnlineMember() uint32 {
	if m != nil {
		return m.RemainOnlineMember
	}
	return 0
}

func (m *AddMemberNotify) GetRemainOnlineAdmin() uint32 {
	if m != nil {
		return m.RemainOnlineAdmin
	}
	return 0
}

func (m *AddMemberNotify) GetIsPwd() bool {
	if m != nil {
		return m.IsPwd
	}
	return false
}

type RemoveMemberNotify struct {
	ChannelId          uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid                uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	AppId              uint32 `protobuf:"varint,3,req,name=app_id,json=appId" json:"app_id"`
	ChannelType        uint32 `protobuf:"varint,4,req,name=channel_type,json=channelType" json:"channel_type"`
	Duration           uint32 `protobuf:"varint,5,req,name=duration" json:"duration"`
	RemainOnlineMember uint32 `protobuf:"varint,6,opt,name=remain_online_member,json=remainOnlineMember" json:"remain_online_member"`
	RemainOnlineAdmin  uint32 `protobuf:"varint,7,opt,name=remain_online_admin,json=remainOnlineAdmin" json:"remain_online_admin"`
	IsAdmin            bool   `protobuf:"varint,8,opt,name=is_admin,json=isAdmin" json:"is_admin"`
	ChannelDisplayId   uint32 `protobuf:"varint,9,opt,name=channel_display_id,json=channelDisplayId" json:"channel_display_id"`
	At                 uint32 `protobuf:"varint,10,opt,name=at" json:"at"`
	AtMs               uint64 `protobuf:"varint,11,opt,name=at_ms,json=atMs" json:"at_ms"`
	ChannelCreaterUid  uint32 `protobuf:"varint,12,opt,name=channel_creater_uid,json=channelCreaterUid" json:"channel_creater_uid"`
}

func (m *RemoveMemberNotify) Reset()                    { *m = RemoveMemberNotify{} }
func (m *RemoveMemberNotify) String() string            { return proto.CompactTextString(m) }
func (*RemoveMemberNotify) ProtoMessage()               {}
func (*RemoveMemberNotify) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{1} }

func (m *RemoveMemberNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemoveMemberNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveMemberNotify) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *RemoveMemberNotify) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *RemoveMemberNotify) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *RemoveMemberNotify) GetRemainOnlineMember() uint32 {
	if m != nil {
		return m.RemainOnlineMember
	}
	return 0
}

func (m *RemoveMemberNotify) GetRemainOnlineAdmin() uint32 {
	if m != nil {
		return m.RemainOnlineAdmin
	}
	return 0
}

func (m *RemoveMemberNotify) GetIsAdmin() bool {
	if m != nil {
		return m.IsAdmin
	}
	return false
}

func (m *RemoveMemberNotify) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *RemoveMemberNotify) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func (m *RemoveMemberNotify) GetAtMs() uint64 {
	if m != nil {
		return m.AtMs
	}
	return 0
}

func (m *RemoveMemberNotify) GetChannelCreaterUid() uint32 {
	if m != nil {
		return m.ChannelCreaterUid
	}
	return 0
}

type ExpiredMembersNotify struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *ExpiredMembersNotify) Reset()                    { *m = ExpiredMembersNotify{} }
func (m *ExpiredMembersNotify) String() string            { return proto.CompactTextString(m) }
func (*ExpiredMembersNotify) ProtoMessage()               {}
func (*ExpiredMembersNotify) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{2} }

func (m *ExpiredMembersNotify) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type RemoveExpiredMemberNotify struct {
	Notify *RemoveMemberNotify `protobuf:"bytes,1,req,name=notify" json:"notify,omitempty"`
}

func (m *RemoveExpiredMemberNotify) Reset()         { *m = RemoveExpiredMemberNotify{} }
func (m *RemoveExpiredMemberNotify) String() string { return proto.CompactTextString(m) }
func (*RemoveExpiredMemberNotify) ProtoMessage()    {}
func (*RemoveExpiredMemberNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorAsyncjob, []int{3}
}

func (m *RemoveExpiredMemberNotify) GetNotify() *RemoveMemberNotify {
	if m != nil {
		return m.Notify
	}
	return nil
}

type ChannelDismissNotify struct {
	ChannelId   uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	AppId       uint32   `protobuf:"varint,2,req,name=app_id,json=appId" json:"app_id"`
	ChannelType uint32   `protobuf:"varint,3,req,name=channel_type,json=channelType" json:"channel_type"`
	UidList     []uint32 `protobuf:"varint,4,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *ChannelDismissNotify) Reset()                    { *m = ChannelDismissNotify{} }
func (m *ChannelDismissNotify) String() string            { return proto.CompactTextString(m) }
func (*ChannelDismissNotify) ProtoMessage()               {}
func (*ChannelDismissNotify) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{4} }

func (m *ChannelDismissNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelDismissNotify) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ChannelDismissNotify) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelDismissNotify) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 废弃
type MemberRankChangeNotify struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	AppId       uint32 `protobuf:"varint,2,req,name=app_id,json=appId" json:"app_id"`
	MemberCount uint32 `protobuf:"varint,3,req,name=member_count,json=memberCount" json:"member_count"`
}

func (m *MemberRankChangeNotify) Reset()                    { *m = MemberRankChangeNotify{} }
func (m *MemberRankChangeNotify) String() string            { return proto.CompactTextString(m) }
func (*MemberRankChangeNotify) ProtoMessage()               {}
func (*MemberRankChangeNotify) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{5} }

func (m *MemberRankChangeNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MemberRankChangeNotify) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *MemberRankChangeNotify) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func init() {
	proto.RegisterType((*AddMemberNotify)(nil), "channelol.asyncjob.AddMemberNotify")
	proto.RegisterType((*RemoveMemberNotify)(nil), "channelol.asyncjob.RemoveMemberNotify")
	proto.RegisterType((*ExpiredMembersNotify)(nil), "channelol.asyncjob.ExpiredMembersNotify")
	proto.RegisterType((*RemoveExpiredMemberNotify)(nil), "channelol.asyncjob.RemoveExpiredMemberNotify")
	proto.RegisterType((*ChannelDismissNotify)(nil), "channelol.asyncjob.ChannelDismissNotify")
	proto.RegisterType((*MemberRankChangeNotify)(nil), "channelol.asyncjob.MemberRankChangeNotify")
	proto.RegisterEnum("channelol.asyncjob.Type", Type_name, Type_value)
}
func (m *AddMemberNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddMemberNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.At))
	dAtA[i] = 0x38
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.AtMs))
	dAtA[i] = 0x40
	i++
	if m.IsAdmin {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x48
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.RemainOnlineMember))
	dAtA[i] = 0x50
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.RemainOnlineAdmin))
	dAtA[i] = 0x58
	i++
	if m.IsPwd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RemoveMemberNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveMemberNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.Duration))
	dAtA[i] = 0x30
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.RemainOnlineMember))
	dAtA[i] = 0x38
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.RemainOnlineAdmin))
	dAtA[i] = 0x40
	i++
	if m.IsAdmin {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x48
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ChannelDisplayId))
	dAtA[i] = 0x50
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.At))
	dAtA[i] = 0x58
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.AtMs))
	dAtA[i] = 0x60
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ChannelCreaterUid))
	return i, nil
}

func (m *ExpiredMembersNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExpiredMembersNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintAsyncjob(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *RemoveExpiredMemberNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveExpiredMemberNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Notify == nil {
		return 0, github_com_gogo_protobuf_proto.NewRequiredNotSetError("notify")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAsyncjob(dAtA, i, uint64(m.Notify.Size()))
		n1, err := m.Notify.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *ChannelDismissNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelDismissNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ChannelType))
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x20
			i++
			i = encodeVarintAsyncjob(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *MemberRankChangeNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MemberRankChangeNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.MemberCount))
	return i, nil
}

func encodeFixed64Asyncjob(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Asyncjob(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAsyncjob(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AddMemberNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.ChannelId))
	n += 1 + sovAsyncjob(uint64(m.Uid))
	n += 1 + sovAsyncjob(uint64(m.AppId))
	n += 1 + sovAsyncjob(uint64(m.ChannelType))
	n += 1 + sovAsyncjob(uint64(m.ChannelDisplayId))
	n += 1 + sovAsyncjob(uint64(m.At))
	n += 1 + sovAsyncjob(uint64(m.AtMs))
	n += 2
	n += 1 + sovAsyncjob(uint64(m.RemainOnlineMember))
	n += 1 + sovAsyncjob(uint64(m.RemainOnlineAdmin))
	n += 2
	return n
}

func (m *RemoveMemberNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.ChannelId))
	n += 1 + sovAsyncjob(uint64(m.Uid))
	n += 1 + sovAsyncjob(uint64(m.AppId))
	n += 1 + sovAsyncjob(uint64(m.ChannelType))
	n += 1 + sovAsyncjob(uint64(m.Duration))
	n += 1 + sovAsyncjob(uint64(m.RemainOnlineMember))
	n += 1 + sovAsyncjob(uint64(m.RemainOnlineAdmin))
	n += 2
	n += 1 + sovAsyncjob(uint64(m.ChannelDisplayId))
	n += 1 + sovAsyncjob(uint64(m.At))
	n += 1 + sovAsyncjob(uint64(m.AtMs))
	n += 1 + sovAsyncjob(uint64(m.ChannelCreaterUid))
	return n
}

func (m *ExpiredMembersNotify) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovAsyncjob(uint64(e))
		}
	}
	return n
}

func (m *RemoveExpiredMemberNotify) Size() (n int) {
	var l int
	_ = l
	if m.Notify != nil {
		l = m.Notify.Size()
		n += 1 + l + sovAsyncjob(uint64(l))
	}
	return n
}

func (m *ChannelDismissNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.ChannelId))
	n += 1 + sovAsyncjob(uint64(m.AppId))
	n += 1 + sovAsyncjob(uint64(m.ChannelType))
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovAsyncjob(uint64(e))
		}
	}
	return n
}

func (m *MemberRankChangeNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.ChannelId))
	n += 1 + sovAsyncjob(uint64(m.AppId))
	n += 1 + sovAsyncjob(uint64(m.MemberCount))
	return n
}

func sovAsyncjob(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAsyncjob(x uint64) (n int) {
	return sovAsyncjob(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *AddMemberNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: AddMemberNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: AddMemberNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AtMs", wireType)
			}
			m.AtMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AtMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsAdmin", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdmin = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RemainOnlineMember", wireType)
			}
			m.RemainOnlineMember = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainOnlineMember |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RemainOnlineAdmin", wireType)
			}
			m.RemainOnlineAdmin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainOnlineAdmin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsPwd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPwd = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveMemberNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: RemoveMemberNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: RemoveMemberNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Duration", wireType)
			}
			m.Duration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Duration |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RemainOnlineMember", wireType)
			}
			m.RemainOnlineMember = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainOnlineMember |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RemainOnlineAdmin", wireType)
			}
			m.RemainOnlineAdmin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainOnlineAdmin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsAdmin", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdmin = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelDisplayId", wireType)
			}
			m.ChannelDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AtMs", wireType)
			}
			m.AtMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AtMs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelCreaterUid", wireType)
			}
			m.ChannelCreaterUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelCreaterUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("duration")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExpiredMembersNotify) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ExpiredMembersNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ExpiredMembersNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAsyncjob
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAsyncjob
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAsyncjob
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAsyncjob
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt1.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveExpiredMemberNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: RemoveExpiredMemberNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: RemoveExpiredMemberNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Notify", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAsyncjob
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Notify == nil {
				m.Notify = &RemoveMemberNotify{}
			}
			if err := m.Notify.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("notify")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelDismissNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelDismissNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelDismissNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAsyncjob
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAsyncjob
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAsyncjob
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAsyncjob
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt1.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MemberRankChangeNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: MemberRankChangeNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: MemberRankChangeNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MemberCount", wireType)
			}
			m.MemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("member_count")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipAsyncjob(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAsyncjob
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAsyncjob
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAsyncjob(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAsyncjob = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAsyncjob   = fmt1.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/channelol/asyncjob/asyncjob.proto", fileDescriptorAsyncjob) }

var fileDescriptorAsyncjob = []byte{
	// 643 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x54, 0xcb, 0x6e, 0xd3, 0x40,
	0x14, 0xad, 0x1f, 0x79, 0xf4, 0xa6, 0x15, 0x66, 0x1a, 0x82, 0x43, 0xa5, 0x34, 0x0a, 0x02, 0x22,
	0x16, 0xa9, 0xa8, 0x2a, 0x96, 0x48, 0x69, 0x32, 0x02, 0x8b, 0x26, 0xad, 0x1c, 0x83, 0x90, 0x58,
	0x8c, 0xa6, 0x99, 0x01, 0x06, 0xe2, 0x87, 0x3c, 0x0e, 0x25, 0x6b, 0x7e, 0x80, 0x0f, 0x60, 0xcb,
	0x82, 0x3f, 0xe9, 0x92, 0x2f, 0x40, 0xa8, 0xfd, 0x11, 0xe4, 0xd8, 0x09, 0x0e, 0x6d, 0x09, 0x20,
	0xd8, 0x4d, 0xee, 0x39, 0x67, 0xee, 0x99, 0x7b, 0x4f, 0x0c, 0xb7, 0x64, 0x38, 0xdc, 0x1e, 0xbe,
	0xa2, 0x9e, 0xc7, 0x47, 0xfe, 0x68, 0x9b, 0xca, 0x89, 0x37, 0x7c, 0xed, 0x1f, 0xcd, 0x0f, 0xad,
	0x20, 0xf4, 0x23, 0x1f, 0xa1, 0x39, 0xa5, 0x35, 0x43, 0x1a, 0x9f, 0x34, 0xb8, 0xd2, 0x66, 0xac,
	0xc7, 0xdd, 0x23, 0x1e, 0xf6, 0xfd, 0x48, 0xbc, 0x98, 0xa0, 0x9b, 0x00, 0x29, 0x93, 0x08, 0x66,
	0x2a, 0x75, 0xb5, 0xb9, 0xbe, 0xa7, 0x9f, 0x7c, 0xdd, 0x5a, 0xb1, 0x57, 0xd3, 0xba, 0xc5, 0x50,
	0x05, 0xb4, 0xb1, 0x60, 0xa6, 0x9a, 0x41, 0xe3, 0x02, 0xda, 0x84, 0x3c, 0x0d, 0x82, 0x58, 0xa8,
	0xd5, 0x95, 0x39, 0x94, 0xa3, 0x41, 0x60, 0x31, 0x74, 0x07, 0xd6, 0x66, 0x37, 0x47, 0x93, 0x80,
	0x9b, 0x7a, 0x86, 0x52, 0x4a, 0x11, 0x67, 0x12, 0x70, 0xb4, 0x03, 0x33, 0xb3, 0x84, 0x09, 0x19,
	0x8c, 0xe8, 0x24, 0xbe, 0x31, 0x97, 0xa1, 0x1b, 0x29, 0xde, 0x4d, 0x60, 0x8b, 0xa1, 0x32, 0xa8,
	0x34, 0x32, 0xf3, 0x19, 0x8e, 0x4a, 0x23, 0x54, 0x85, 0x1c, 0x8d, 0x88, 0x2b, 0xcd, 0x42, 0x5d,
	0x69, 0xea, 0x29, 0xa0, 0xd3, 0xa8, 0x27, 0xd1, 0x16, 0x14, 0x85, 0x24, 0x94, 0xb9, 0xc2, 0x33,
	0x8b, 0x75, 0xa5, 0x59, 0x4c, 0xd1, 0x82, 0x90, 0xed, 0xb8, 0x88, 0xee, 0x43, 0x39, 0xe4, 0x2e,
	0x15, 0x1e, 0xf1, 0xbd, 0x91, 0xf0, 0x38, 0x71, 0xa7, 0x63, 0x32, 0x57, 0x33, 0x3d, 0x50, 0xc2,
	0x38, 0x98, 0x12, 0x92, 0x31, 0xa2, 0x5d, 0xd8, 0x58, 0xd4, 0x25, 0x3d, 0x20, 0x23, 0xbb, 0x9a,
	0x95, 0x25, 0xdd, 0x36, 0x21, 0x2f, 0x24, 0x09, 0x8e, 0x99, 0x59, 0xca, 0x98, 0xc9, 0x09, 0x79,
	0x78, 0xcc, 0x1a, 0x67, 0x1a, 0x20, 0x9b, 0xbb, 0xfe, 0x5b, 0xfe, 0x7f, 0x56, 0xa5, 0x2e, 0x5f,
	0x95, 0x7a, 0xf1, 0xaa, 0xea, 0x50, 0x64, 0xe3, 0x90, 0x46, 0xc2, 0xf7, 0xcc, 0x5c, 0x86, 0x34,
	0xaf, 0x5e, 0x3a, 0xc6, 0xfc, 0xdf, 0x8d, 0xb1, 0xf0, 0xeb, 0x31, 0x2e, 0xdd, 0xea, 0xc5, 0xd9,
	0x5a, 0xfd, 0x8d, 0x6c, 0xc1, 0x65, 0xd9, 0x2a, 0x9d, 0xcb, 0xd6, 0x2e, 0x6c, 0xcc, 0x9a, 0x0c,
	0x43, 0x4e, 0x23, 0x1e, 0x92, 0x78, 0x07, 0x6b, 0x59, 0xef, 0x29, 0xa1, 0x93, 0xe0, 0x4f, 0x04,
	0x6b, 0xdc, 0x83, 0x32, 0x7e, 0x17, 0x88, 0x90, 0xa7, 0x7f, 0x48, 0x99, 0xae, 0xb9, 0x0a, 0xc5,
	0xb1, 0x60, 0x64, 0x24, 0x64, 0x64, 0x2a, 0x75, 0xad, 0xb9, 0x6e, 0x17, 0xc6, 0x82, 0xed, 0x0b,
	0x19, 0x35, 0x9e, 0x43, 0x35, 0xc9, 0xc5, 0x82, 0x30, 0xd5, 0x3d, 0x80, 0xbc, 0x37, 0x3d, 0x4d,
	0xa3, 0x51, 0xda, 0xb9, 0xdd, 0x3a, 0xff, 0x09, 0x68, 0x9d, 0x8f, 0x95, 0x9d, 0xaa, 0x1a, 0x1f,
	0x15, 0x28, 0x77, 0xe6, 0xb3, 0x70, 0x85, 0x94, 0x7f, 0x92, 0xbb, 0x1f, 0xf9, 0x52, 0x97, 0xe7,
	0x4b, 0xbb, 0x2c, 0x5f, 0xd9, 0xb7, 0xeb, 0x8b, 0x6f, 0x7f, 0xaf, 0x40, 0x25, 0xf1, 0x6d, 0x53,
	0xef, 0x4d, 0x6c, 0xf4, 0x25, 0xff, 0x97, 0x06, 0x93, 0x9c, 0x92, 0xa1, 0x3f, 0xf6, 0xa2, 0x45,
	0x83, 0x09, 0xd2, 0x89, 0x81, 0xbb, 0x9f, 0x15, 0xd0, 0xa7, 0x4e, 0x4d, 0x28, 0x63, 0x87, 0xb4,
	0xbb, 0x5d, 0xd2, 0x79, 0xd4, 0xee, 0xf7, 0xf1, 0x3e, 0xe9, 0xe1, 0x1e, 0xb6, 0x0d, 0x05, 0x6d,
	0xc2, 0x75, 0xec, 0x10, 0x1b, 0xf7, 0x0e, 0x9e, 0xe2, 0x9f, 0x40, 0x15, 0x55, 0xe1, 0x1a, 0x76,
	0xe2, 0x5f, 0x7b, 0xd8, 0x26, 0x76, 0xbb, 0xff, 0x78, 0xca, 0x78, 0x88, 0x0d, 0x0d, 0xdd, 0x80,
	0x0a, 0x76, 0x08, 0x7e, 0x76, 0x68, 0xd9, 0xb8, 0x3b, 0xa3, 0xec, 0x5b, 0x03, 0xc7, 0xd0, 0x17,
	0xef, 0x4c, 0x28, 0x29, 0xc3, 0xc8, 0xa1, 0x0a, 0x20, 0xec, 0xcc, 0x3b, 0x75, 0xad, 0x41, 0xcf,
	0x1a, 0x0c, 0x8c, 0xfc, 0x9e, 0x71, 0x72, 0x5a, 0x53, 0xbe, 0x9c, 0xd6, 0x94, 0x6f, 0xa7, 0x35,
	0xe5, 0xc3, 0x59, 0x6d, 0xe5, 0x7b, 0x00, 0x00, 0x00, 0xff, 0xff, 0x83, 0x18, 0xed, 0x00, 0x3c,
	0x06, 0x00, 0x00,
}
