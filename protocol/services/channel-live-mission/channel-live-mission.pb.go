// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-live-mission/channel-live-mission.proto

package channel_live_mission // import "golang.52tt.com/protocol/services/channel-live-mission"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MissionStatus int32

const (
	MissionStatus_Processing MissionStatus = 0
	MissionStatus_Finish     MissionStatus = 1
)

var MissionStatus_name = map[int32]string{
	0: "Processing",
	1: "Finish",
}
var MissionStatus_value = map[string]int32{
	"Processing": 0,
	"Finish":     1,
}

func (x MissionStatus) String() string {
	return proto.EnumName(MissionStatus_name, int32(x))
}
func (MissionStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{0}
}

type ChannelLiveMissionInfo_MissionType int32

const (
	ChannelLiveMissionInfo_Unknown      ChannelLiveMissionInfo_MissionType = 0
	ChannelLiveMissionInfo_Daily        ChannelLiveMissionInfo_MissionType = 1
	ChannelLiveMissionInfo_Accumulative ChannelLiveMissionInfo_MissionType = 2
	ChannelLiveMissionInfo_Continuation ChannelLiveMissionInfo_MissionType = 3
)

var ChannelLiveMissionInfo_MissionType_name = map[int32]string{
	0: "Unknown",
	1: "Daily",
	2: "Accumulative",
	3: "Continuation",
}
var ChannelLiveMissionInfo_MissionType_value = map[string]int32{
	"Unknown":      0,
	"Daily":        1,
	"Accumulative": 2,
	"Continuation": 3,
}

func (x ChannelLiveMissionInfo_MissionType) String() string {
	return proto.EnumName(ChannelLiveMissionInfo_MissionType_name, int32(x))
}
func (ChannelLiveMissionInfo_MissionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{0, 0}
}

// 点击‘去完成’的操作类型
type ChannelLiveMissionInfo_MissionOperType int32

const (
	ChannelLiveMissionInfo_None             ChannelLiveMissionInfo_MissionOperType = 0
	ChannelLiveMissionInfo_GiftRack         ChannelLiveMissionInfo_MissionOperType = 1
	ChannelLiveMissionInfo_ShareLiveChannel ChannelLiveMissionInfo_MissionOperType = 2
	ChannelLiveMissionInfo_JumpFansGift     ChannelLiveMissionInfo_MissionOperType = 3
)

var ChannelLiveMissionInfo_MissionOperType_name = map[int32]string{
	0: "None",
	1: "GiftRack",
	2: "ShareLiveChannel",
	3: "JumpFansGift",
}
var ChannelLiveMissionInfo_MissionOperType_value = map[string]int32{
	"None":             0,
	"GiftRack":         1,
	"ShareLiveChannel": 2,
	"JumpFansGift":     3,
}

func (x ChannelLiveMissionInfo_MissionOperType) String() string {
	return proto.EnumName(ChannelLiveMissionInfo_MissionOperType_name, int32(x))
}
func (ChannelLiveMissionInfo_MissionOperType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{0, 1}
}

type ActorMissionInfo_MissionType int32

const (
	ActorMissionInfo_UnknownMission        ActorMissionInfo_MissionType = 0
	ActorMissionInfo_DayIncomeMission      ActorMissionInfo_MissionType = 1
	ActorMissionInfo_DayTimeLengthMission  ActorMissionInfo_MissionType = 2
	ActorMissionInfo_WeekIncomeMission     ActorMissionInfo_MissionType = 3
	ActorMissionInfo_WeekTimeLengthMission ActorMissionInfo_MissionType = 4
	ActorMissionInfo_MonthIncomeMission    ActorMissionInfo_MissionType = 5
)

var ActorMissionInfo_MissionType_name = map[int32]string{
	0: "UnknownMission",
	1: "DayIncomeMission",
	2: "DayTimeLengthMission",
	3: "WeekIncomeMission",
	4: "WeekTimeLengthMission",
	5: "MonthIncomeMission",
}
var ActorMissionInfo_MissionType_value = map[string]int32{
	"UnknownMission":        0,
	"DayIncomeMission":      1,
	"DayTimeLengthMission":  2,
	"WeekIncomeMission":     3,
	"WeekTimeLengthMission": 4,
	"MonthIncomeMission":    5,
}

func (x ActorMissionInfo_MissionType) String() string {
	return proto.EnumName(ActorMissionInfo_MissionType_name, int32(x))
}
func (ActorMissionInfo_MissionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{11, 0}
}

type MissionAwardInfo_MissionType int32

const (
	MissionAwardInfo_Mission_Invalid MissionAwardInfo_MissionType = 0
	MissionAwardInfo_Mission_Fans    MissionAwardInfo_MissionType = 1
)

var MissionAwardInfo_MissionType_name = map[int32]string{
	0: "Mission_Invalid",
	1: "Mission_Fans",
}
var MissionAwardInfo_MissionType_value = map[string]int32{
	"Mission_Invalid": 0,
	"Mission_Fans":    1,
}

func (x MissionAwardInfo_MissionType) String() string {
	return proto.EnumName(MissionAwardInfo_MissionType_name, int32(x))
}
func (MissionAwardInfo_MissionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{19, 0}
}

// 定时任务类型
type TriggerTimerReq_TimerType int32

const (
	TriggerTimerReq_Timer_Type_Invalid                TriggerTimerReq_TimerType = 0
	TriggerTimerReq_Timer_Type_AwardWeekActorMission  TriggerTimerReq_TimerType = 1
	TriggerTimerReq_Timer_Type_AwardMonthActorMission TriggerTimerReq_TimerType = 2
)

var TriggerTimerReq_TimerType_name = map[int32]string{
	0: "Timer_Type_Invalid",
	1: "Timer_Type_AwardWeekActorMission",
	2: "Timer_Type_AwardMonthActorMission",
}
var TriggerTimerReq_TimerType_value = map[string]int32{
	"Timer_Type_Invalid":                0,
	"Timer_Type_AwardWeekActorMission":  1,
	"Timer_Type_AwardMonthActorMission": 2,
}

func (x TriggerTimerReq_TimerType) String() string {
	return proto.EnumName(TriggerTimerReq_TimerType_name, int32(x))
}
func (TriggerTimerReq_TimerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{24, 0}
}

// 直播任务相关
type ChannelLiveMissionInfo struct {
	MissionId            uint32   `protobuf:"varint,1,opt,name=mission_id,json=missionId,proto3" json:"mission_id,omitempty"`
	MissionDesc          string   `protobuf:"bytes,2,opt,name=mission_desc,json=missionDesc,proto3" json:"mission_desc,omitempty"`
	MissionType          uint32   `protobuf:"varint,3,opt,name=mission_type,json=missionType,proto3" json:"mission_type,omitempty"`
	MissionName          string   `protobuf:"bytes,4,opt,name=mission_name,json=missionName,proto3" json:"mission_name,omitempty"`
	MissionIconUrl       string   `protobuf:"bytes,5,opt,name=mission_icon_url,json=missionIconUrl,proto3" json:"mission_icon_url,omitempty"`
	UpdateTime           int64    `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Status               uint32   `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	FinishCnt            uint32   `protobuf:"varint,8,opt,name=finish_cnt,json=finishCnt,proto3" json:"finish_cnt,omitempty"`
	GoalCnt              uint32   `protobuf:"varint,9,opt,name=goal_cnt,json=goalCnt,proto3" json:"goal_cnt,omitempty"`
	ActorUid             uint32   `protobuf:"varint,10,opt,name=actor_uid,json=actorUid,proto3" json:"actor_uid,omitempty"`
	OperType             uint32   `protobuf:"varint,11,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`
	AwardNum             uint32   `protobuf:"varint,12,opt,name=award_num,json=awardNum,proto3" json:"award_num,omitempty"`
	ProgressDesc         string   `protobuf:"bytes,13,opt,name=progress_desc,json=progressDesc,proto3" json:"progress_desc,omitempty"`
	KnightDesc           string   `protobuf:"bytes,14,opt,name=knight_desc,json=knightDesc,proto3" json:"knight_desc,omitempty"`
	BubbleDesc           string   `protobuf:"bytes,15,opt,name=bubble_desc,json=bubbleDesc,proto3" json:"bubble_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLiveMissionInfo) Reset()         { *m = ChannelLiveMissionInfo{} }
func (m *ChannelLiveMissionInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveMissionInfo) ProtoMessage()    {}
func (*ChannelLiveMissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{0}
}
func (m *ChannelLiveMissionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveMissionInfo.Unmarshal(m, b)
}
func (m *ChannelLiveMissionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveMissionInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveMissionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveMissionInfo.Merge(dst, src)
}
func (m *ChannelLiveMissionInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveMissionInfo.Size(m)
}
func (m *ChannelLiveMissionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveMissionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveMissionInfo proto.InternalMessageInfo

func (m *ChannelLiveMissionInfo) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *ChannelLiveMissionInfo) GetMissionDesc() string {
	if m != nil {
		return m.MissionDesc
	}
	return ""
}

func (m *ChannelLiveMissionInfo) GetMissionType() uint32 {
	if m != nil {
		return m.MissionType
	}
	return 0
}

func (m *ChannelLiveMissionInfo) GetMissionName() string {
	if m != nil {
		return m.MissionName
	}
	return ""
}

func (m *ChannelLiveMissionInfo) GetMissionIconUrl() string {
	if m != nil {
		return m.MissionIconUrl
	}
	return ""
}

func (m *ChannelLiveMissionInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ChannelLiveMissionInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelLiveMissionInfo) GetFinishCnt() uint32 {
	if m != nil {
		return m.FinishCnt
	}
	return 0
}

func (m *ChannelLiveMissionInfo) GetGoalCnt() uint32 {
	if m != nil {
		return m.GoalCnt
	}
	return 0
}

func (m *ChannelLiveMissionInfo) GetActorUid() uint32 {
	if m != nil {
		return m.ActorUid
	}
	return 0
}

func (m *ChannelLiveMissionInfo) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *ChannelLiveMissionInfo) GetAwardNum() uint32 {
	if m != nil {
		return m.AwardNum
	}
	return 0
}

func (m *ChannelLiveMissionInfo) GetProgressDesc() string {
	if m != nil {
		return m.ProgressDesc
	}
	return ""
}

func (m *ChannelLiveMissionInfo) GetKnightDesc() string {
	if m != nil {
		return m.KnightDesc
	}
	return ""
}

func (m *ChannelLiveMissionInfo) GetBubbleDesc() string {
	if m != nil {
		return m.BubbleDesc
	}
	return ""
}

type GetUserMissionReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserMissionReq) Reset()         { *m = GetUserMissionReq{} }
func (m *GetUserMissionReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMissionReq) ProtoMessage()    {}
func (*GetUserMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{1}
}
func (m *GetUserMissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMissionReq.Unmarshal(m, b)
}
func (m *GetUserMissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMissionReq.Marshal(b, m, deterministic)
}
func (dst *GetUserMissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMissionReq.Merge(dst, src)
}
func (m *GetUserMissionReq) XXX_Size() int {
	return xxx_messageInfo_GetUserMissionReq.Size(m)
}
func (m *GetUserMissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMissionReq proto.InternalMessageInfo

func (m *GetUserMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserMissionResp struct {
	MissionList          []*ChannelLiveMissionInfo `protobuf:"bytes,1,rep,name=mission_list,json=missionList,proto3" json:"mission_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetUserMissionResp) Reset()         { *m = GetUserMissionResp{} }
func (m *GetUserMissionResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMissionResp) ProtoMessage()    {}
func (*GetUserMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{2}
}
func (m *GetUserMissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMissionResp.Unmarshal(m, b)
}
func (m *GetUserMissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMissionResp.Marshal(b, m, deterministic)
}
func (dst *GetUserMissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMissionResp.Merge(dst, src)
}
func (m *GetUserMissionResp) XXX_Size() int {
	return xxx_messageInfo_GetUserMissionResp.Size(m)
}
func (m *GetUserMissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMissionResp proto.InternalMessageInfo

func (m *GetUserMissionResp) GetMissionList() []*ChannelLiveMissionInfo {
	if m != nil {
		return m.MissionList
	}
	return nil
}

type HandleUserMissionReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MissionId            uint32   `protobuf:"varint,2,opt,name=mission_id,json=missionId,proto3" json:"mission_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IncrFinishCnt        uint32   `protobuf:"varint,4,opt,name=incr_finish_cnt,json=incrFinishCnt,proto3" json:"incr_finish_cnt,omitempty"`
	AntiRepeatedMem      uint32   `protobuf:"varint,5,opt,name=anti_repeated_mem,json=antiRepeatedMem,proto3" json:"anti_repeated_mem,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleUserMissionReq) Reset()         { *m = HandleUserMissionReq{} }
func (m *HandleUserMissionReq) String() string { return proto.CompactTextString(m) }
func (*HandleUserMissionReq) ProtoMessage()    {}
func (*HandleUserMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{3}
}
func (m *HandleUserMissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleUserMissionReq.Unmarshal(m, b)
}
func (m *HandleUserMissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleUserMissionReq.Marshal(b, m, deterministic)
}
func (dst *HandleUserMissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleUserMissionReq.Merge(dst, src)
}
func (m *HandleUserMissionReq) XXX_Size() int {
	return xxx_messageInfo_HandleUserMissionReq.Size(m)
}
func (m *HandleUserMissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleUserMissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleUserMissionReq proto.InternalMessageInfo

func (m *HandleUserMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleUserMissionReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *HandleUserMissionReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HandleUserMissionReq) GetIncrFinishCnt() uint32 {
	if m != nil {
		return m.IncrFinishCnt
	}
	return 0
}

func (m *HandleUserMissionReq) GetAntiRepeatedMem() uint32 {
	if m != nil {
		return m.AntiRepeatedMem
	}
	return 0
}

type HandleUserMissionResp struct {
	Status               uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleUserMissionResp) Reset()         { *m = HandleUserMissionResp{} }
func (m *HandleUserMissionResp) String() string { return proto.CompactTextString(m) }
func (*HandleUserMissionResp) ProtoMessage()    {}
func (*HandleUserMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{4}
}
func (m *HandleUserMissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleUserMissionResp.Unmarshal(m, b)
}
func (m *HandleUserMissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleUserMissionResp.Marshal(b, m, deterministic)
}
func (dst *HandleUserMissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleUserMissionResp.Merge(dst, src)
}
func (m *HandleUserMissionResp) XXX_Size() int {
	return xxx_messageInfo_HandleUserMissionResp.Size(m)
}
func (m *HandleUserMissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleUserMissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleUserMissionResp proto.InternalMessageInfo

func (m *HandleUserMissionResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type SwitchTimeUserMissionTsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Switch               bool     `protobuf:"varint,2,opt,name=switch,proto3" json:"switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchTimeUserMissionTsReq) Reset()         { *m = SwitchTimeUserMissionTsReq{} }
func (m *SwitchTimeUserMissionTsReq) String() string { return proto.CompactTextString(m) }
func (*SwitchTimeUserMissionTsReq) ProtoMessage()    {}
func (*SwitchTimeUserMissionTsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{5}
}
func (m *SwitchTimeUserMissionTsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchTimeUserMissionTsReq.Unmarshal(m, b)
}
func (m *SwitchTimeUserMissionTsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchTimeUserMissionTsReq.Marshal(b, m, deterministic)
}
func (dst *SwitchTimeUserMissionTsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchTimeUserMissionTsReq.Merge(dst, src)
}
func (m *SwitchTimeUserMissionTsReq) XXX_Size() int {
	return xxx_messageInfo_SwitchTimeUserMissionTsReq.Size(m)
}
func (m *SwitchTimeUserMissionTsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchTimeUserMissionTsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchTimeUserMissionTsReq proto.InternalMessageInfo

func (m *SwitchTimeUserMissionTsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SwitchTimeUserMissionTsReq) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

type SwitchTimeUserMissionTsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchTimeUserMissionTsResp) Reset()         { *m = SwitchTimeUserMissionTsResp{} }
func (m *SwitchTimeUserMissionTsResp) String() string { return proto.CompactTextString(m) }
func (*SwitchTimeUserMissionTsResp) ProtoMessage()    {}
func (*SwitchTimeUserMissionTsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{6}
}
func (m *SwitchTimeUserMissionTsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchTimeUserMissionTsResp.Unmarshal(m, b)
}
func (m *SwitchTimeUserMissionTsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchTimeUserMissionTsResp.Marshal(b, m, deterministic)
}
func (dst *SwitchTimeUserMissionTsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchTimeUserMissionTsResp.Merge(dst, src)
}
func (m *SwitchTimeUserMissionTsResp) XXX_Size() int {
	return xxx_messageInfo_SwitchTimeUserMissionTsResp.Size(m)
}
func (m *SwitchTimeUserMissionTsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchTimeUserMissionTsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchTimeUserMissionTsResp proto.InternalMessageInfo

type GetFansMissionReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActorUid             uint32   `protobuf:"varint,2,opt,name=actor_uid,json=actorUid,proto3" json:"actor_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFansMissionReq) Reset()         { *m = GetFansMissionReq{} }
func (m *GetFansMissionReq) String() string { return proto.CompactTextString(m) }
func (*GetFansMissionReq) ProtoMessage()    {}
func (*GetFansMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{7}
}
func (m *GetFansMissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFansMissionReq.Unmarshal(m, b)
}
func (m *GetFansMissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFansMissionReq.Marshal(b, m, deterministic)
}
func (dst *GetFansMissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFansMissionReq.Merge(dst, src)
}
func (m *GetFansMissionReq) XXX_Size() int {
	return xxx_messageInfo_GetFansMissionReq.Size(m)
}
func (m *GetFansMissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFansMissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFansMissionReq proto.InternalMessageInfo

func (m *GetFansMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFansMissionReq) GetActorUid() uint32 {
	if m != nil {
		return m.ActorUid
	}
	return 0
}

func (m *GetFansMissionReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetFansMissionResp struct {
	MissionList          []*ChannelLiveMissionInfo `protobuf:"bytes,1,rep,name=mission_list,json=missionList,proto3" json:"mission_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetFansMissionResp) Reset()         { *m = GetFansMissionResp{} }
func (m *GetFansMissionResp) String() string { return proto.CompactTextString(m) }
func (*GetFansMissionResp) ProtoMessage()    {}
func (*GetFansMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{8}
}
func (m *GetFansMissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFansMissionResp.Unmarshal(m, b)
}
func (m *GetFansMissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFansMissionResp.Marshal(b, m, deterministic)
}
func (dst *GetFansMissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFansMissionResp.Merge(dst, src)
}
func (m *GetFansMissionResp) XXX_Size() int {
	return xxx_messageInfo_GetFansMissionResp.Size(m)
}
func (m *GetFansMissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFansMissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFansMissionResp proto.InternalMessageInfo

func (m *GetFansMissionResp) GetMissionList() []*ChannelLiveMissionInfo {
	if m != nil {
		return m.MissionList
	}
	return nil
}

type HandleFansMissionReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ActorUid             uint32   `protobuf:"varint,2,opt,name=actor_uid,json=actorUid,proto3" json:"actor_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MissionId            uint32   `protobuf:"varint,4,opt,name=mission_id,json=missionId,proto3" json:"mission_id,omitempty"`
	IncrFinishCnt        uint32   `protobuf:"varint,5,opt,name=incr_finish_cnt,json=incrFinishCnt,proto3" json:"incr_finish_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleFansMissionReq) Reset()         { *m = HandleFansMissionReq{} }
func (m *HandleFansMissionReq) String() string { return proto.CompactTextString(m) }
func (*HandleFansMissionReq) ProtoMessage()    {}
func (*HandleFansMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{9}
}
func (m *HandleFansMissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleFansMissionReq.Unmarshal(m, b)
}
func (m *HandleFansMissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleFansMissionReq.Marshal(b, m, deterministic)
}
func (dst *HandleFansMissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleFansMissionReq.Merge(dst, src)
}
func (m *HandleFansMissionReq) XXX_Size() int {
	return xxx_messageInfo_HandleFansMissionReq.Size(m)
}
func (m *HandleFansMissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleFansMissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleFansMissionReq proto.InternalMessageInfo

func (m *HandleFansMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleFansMissionReq) GetActorUid() uint32 {
	if m != nil {
		return m.ActorUid
	}
	return 0
}

func (m *HandleFansMissionReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HandleFansMissionReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *HandleFansMissionReq) GetIncrFinishCnt() uint32 {
	if m != nil {
		return m.IncrFinishCnt
	}
	return 0
}

type HandleFansMissionResp struct {
	Status               uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleFansMissionResp) Reset()         { *m = HandleFansMissionResp{} }
func (m *HandleFansMissionResp) String() string { return proto.CompactTextString(m) }
func (*HandleFansMissionResp) ProtoMessage()    {}
func (*HandleFansMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{10}
}
func (m *HandleFansMissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleFansMissionResp.Unmarshal(m, b)
}
func (m *HandleFansMissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleFansMissionResp.Marshal(b, m, deterministic)
}
func (dst *HandleFansMissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleFansMissionResp.Merge(dst, src)
}
func (m *HandleFansMissionResp) XXX_Size() int {
	return xxx_messageInfo_HandleFansMissionResp.Size(m)
}
func (m *HandleFansMissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleFansMissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleFansMissionResp proto.InternalMessageInfo

func (m *HandleFansMissionResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 主播任务
type ActorMissionInfo struct {
	MissionName          string                 `protobuf:"bytes,1,opt,name=mission_name,json=missionName,proto3" json:"mission_name,omitempty"`
	MissionLevel         uint32                 `protobuf:"varint,2,opt,name=mission_level,json=missionLevel,proto3" json:"mission_level,omitempty"`
	AwardDesc            string                 `protobuf:"bytes,3,opt,name=award_desc,json=awardDesc,proto3" json:"award_desc,omitempty"`
	MissionType          uint32                 `protobuf:"varint,4,opt,name=mission_type,json=missionType,proto3" json:"mission_type,omitempty"`
	MissionStatus        uint32                 `protobuf:"varint,5,opt,name=mission_status,json=missionStatus,proto3" json:"mission_status,omitempty"`
	SubMissions          []*ActorSubMissionInfo `protobuf:"bytes,6,rep,name=sub_missions,json=subMissions,proto3" json:"sub_missions,omitempty"`
	IsCurrentShow        bool                   `protobuf:"varint,7,opt,name=is_current_show,json=isCurrentShow,proto3" json:"is_current_show,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ActorMissionInfo) Reset()         { *m = ActorMissionInfo{} }
func (m *ActorMissionInfo) String() string { return proto.CompactTextString(m) }
func (*ActorMissionInfo) ProtoMessage()    {}
func (*ActorMissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{11}
}
func (m *ActorMissionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActorMissionInfo.Unmarshal(m, b)
}
func (m *ActorMissionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActorMissionInfo.Marshal(b, m, deterministic)
}
func (dst *ActorMissionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActorMissionInfo.Merge(dst, src)
}
func (m *ActorMissionInfo) XXX_Size() int {
	return xxx_messageInfo_ActorMissionInfo.Size(m)
}
func (m *ActorMissionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ActorMissionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ActorMissionInfo proto.InternalMessageInfo

func (m *ActorMissionInfo) GetMissionName() string {
	if m != nil {
		return m.MissionName
	}
	return ""
}

func (m *ActorMissionInfo) GetMissionLevel() uint32 {
	if m != nil {
		return m.MissionLevel
	}
	return 0
}

func (m *ActorMissionInfo) GetAwardDesc() string {
	if m != nil {
		return m.AwardDesc
	}
	return ""
}

func (m *ActorMissionInfo) GetMissionType() uint32 {
	if m != nil {
		return m.MissionType
	}
	return 0
}

func (m *ActorMissionInfo) GetMissionStatus() uint32 {
	if m != nil {
		return m.MissionStatus
	}
	return 0
}

func (m *ActorMissionInfo) GetSubMissions() []*ActorSubMissionInfo {
	if m != nil {
		return m.SubMissions
	}
	return nil
}

func (m *ActorMissionInfo) GetIsCurrentShow() bool {
	if m != nil {
		return m.IsCurrentShow
	}
	return false
}

// 主播子任务
type ActorSubMissionInfo struct {
	SubName              string   `protobuf:"bytes,1,opt,name=sub_name,json=subName,proto3" json:"sub_name,omitempty"`
	ProgressDesc         string   `protobuf:"bytes,2,opt,name=progress_desc,json=progressDesc,proto3" json:"progress_desc,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActorSubMissionInfo) Reset()         { *m = ActorSubMissionInfo{} }
func (m *ActorSubMissionInfo) String() string { return proto.CompactTextString(m) }
func (*ActorSubMissionInfo) ProtoMessage()    {}
func (*ActorSubMissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{12}
}
func (m *ActorSubMissionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActorSubMissionInfo.Unmarshal(m, b)
}
func (m *ActorSubMissionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActorSubMissionInfo.Marshal(b, m, deterministic)
}
func (dst *ActorSubMissionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActorSubMissionInfo.Merge(dst, src)
}
func (m *ActorSubMissionInfo) XXX_Size() int {
	return xxx_messageInfo_ActorSubMissionInfo.Size(m)
}
func (m *ActorSubMissionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ActorSubMissionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ActorSubMissionInfo proto.InternalMessageInfo

func (m *ActorSubMissionInfo) GetSubName() string {
	if m != nil {
		return m.SubName
	}
	return ""
}

func (m *ActorSubMissionInfo) GetProgressDesc() string {
	if m != nil {
		return m.ProgressDesc
	}
	return ""
}

func (m *ActorSubMissionInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetActorMissionReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActorMissionReq) Reset()         { *m = GetActorMissionReq{} }
func (m *GetActorMissionReq) String() string { return proto.CompactTextString(m) }
func (*GetActorMissionReq) ProtoMessage()    {}
func (*GetActorMissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{13}
}
func (m *GetActorMissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActorMissionReq.Unmarshal(m, b)
}
func (m *GetActorMissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActorMissionReq.Marshal(b, m, deterministic)
}
func (dst *GetActorMissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActorMissionReq.Merge(dst, src)
}
func (m *GetActorMissionReq) XXX_Size() int {
	return xxx_messageInfo_GetActorMissionReq.Size(m)
}
func (m *GetActorMissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActorMissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActorMissionReq proto.InternalMessageInfo

func (m *GetActorMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetActorMissionReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetActorMissionResp struct {
	MissionList          []*ActorMissionInfo `protobuf:"bytes,1,rep,name=mission_list,json=missionList,proto3" json:"mission_list,omitempty"`
	MissionDesc          string              `protobuf:"bytes,2,opt,name=mission_desc,json=missionDesc,proto3" json:"mission_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetActorMissionResp) Reset()         { *m = GetActorMissionResp{} }
func (m *GetActorMissionResp) String() string { return proto.CompactTextString(m) }
func (*GetActorMissionResp) ProtoMessage()    {}
func (*GetActorMissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{14}
}
func (m *GetActorMissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActorMissionResp.Unmarshal(m, b)
}
func (m *GetActorMissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActorMissionResp.Marshal(b, m, deterministic)
}
func (dst *GetActorMissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActorMissionResp.Merge(dst, src)
}
func (m *GetActorMissionResp) XXX_Size() int {
	return xxx_messageInfo_GetActorMissionResp.Size(m)
}
func (m *GetActorMissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActorMissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActorMissionResp proto.InternalMessageInfo

func (m *GetActorMissionResp) GetMissionList() []*ActorMissionInfo {
	if m != nil {
		return m.MissionList
	}
	return nil
}

func (m *GetActorMissionResp) GetMissionDesc() string {
	if m != nil {
		return m.MissionDesc
	}
	return ""
}

// 进行中的任务相关描述
type ProcessActorMissionDesc struct {
	ProgressDesc         string   `protobuf:"bytes,1,opt,name=progress_desc,json=progressDesc,proto3" json:"progress_desc,omitempty"`
	AwardDesc            string   `protobuf:"bytes,2,opt,name=award_desc,json=awardDesc,proto3" json:"award_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcessActorMissionDesc) Reset()         { *m = ProcessActorMissionDesc{} }
func (m *ProcessActorMissionDesc) String() string { return proto.CompactTextString(m) }
func (*ProcessActorMissionDesc) ProtoMessage()    {}
func (*ProcessActorMissionDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{15}
}
func (m *ProcessActorMissionDesc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcessActorMissionDesc.Unmarshal(m, b)
}
func (m *ProcessActorMissionDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcessActorMissionDesc.Marshal(b, m, deterministic)
}
func (dst *ProcessActorMissionDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcessActorMissionDesc.Merge(dst, src)
}
func (m *ProcessActorMissionDesc) XXX_Size() int {
	return xxx_messageInfo_ProcessActorMissionDesc.Size(m)
}
func (m *ProcessActorMissionDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcessActorMissionDesc.DiscardUnknown(m)
}

var xxx_messageInfo_ProcessActorMissionDesc proto.InternalMessageInfo

func (m *ProcessActorMissionDesc) GetProgressDesc() string {
	if m != nil {
		return m.ProgressDesc
	}
	return ""
}

func (m *ProcessActorMissionDesc) GetAwardDesc() string {
	if m != nil {
		return m.AwardDesc
	}
	return ""
}

type GetProcessActorMissionDescResp struct {
	ProcessMissionList   []*ProcessActorMissionDesc `protobuf:"bytes,1,rep,name=process_mission_list,json=processMissionList,proto3" json:"process_mission_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetProcessActorMissionDescResp) Reset()         { *m = GetProcessActorMissionDescResp{} }
func (m *GetProcessActorMissionDescResp) String() string { return proto.CompactTextString(m) }
func (*GetProcessActorMissionDescResp) ProtoMessage()    {}
func (*GetProcessActorMissionDescResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{16}
}
func (m *GetProcessActorMissionDescResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetProcessActorMissionDescResp.Unmarshal(m, b)
}
func (m *GetProcessActorMissionDescResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetProcessActorMissionDescResp.Marshal(b, m, deterministic)
}
func (dst *GetProcessActorMissionDescResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetProcessActorMissionDescResp.Merge(dst, src)
}
func (m *GetProcessActorMissionDescResp) XXX_Size() int {
	return xxx_messageInfo_GetProcessActorMissionDescResp.Size(m)
}
func (m *GetProcessActorMissionDescResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetProcessActorMissionDescResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetProcessActorMissionDescResp proto.InternalMessageInfo

func (m *GetProcessActorMissionDescResp) GetProcessMissionList() []*ProcessActorMissionDesc {
	if m != nil {
		return m.ProcessMissionList
	}
	return nil
}

// 根据主播心跳每次增加直播时长次数（一次5s）
type IncrActorLiveTimeCntReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelLiveId        uint64   `protobuf:"varint,3,opt,name=channel_live_id,json=channelLiveId,proto3" json:"channel_live_id,omitempty"`
	IsLiving             bool     `protobuf:"varint,4,opt,name=is_living,json=isLiving,proto3" json:"is_living,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrActorLiveTimeCntReq) Reset()         { *m = IncrActorLiveTimeCntReq{} }
func (m *IncrActorLiveTimeCntReq) String() string { return proto.CompactTextString(m) }
func (*IncrActorLiveTimeCntReq) ProtoMessage()    {}
func (*IncrActorLiveTimeCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{17}
}
func (m *IncrActorLiveTimeCntReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrActorLiveTimeCntReq.Unmarshal(m, b)
}
func (m *IncrActorLiveTimeCntReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrActorLiveTimeCntReq.Marshal(b, m, deterministic)
}
func (dst *IncrActorLiveTimeCntReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrActorLiveTimeCntReq.Merge(dst, src)
}
func (m *IncrActorLiveTimeCntReq) XXX_Size() int {
	return xxx_messageInfo_IncrActorLiveTimeCntReq.Size(m)
}
func (m *IncrActorLiveTimeCntReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrActorLiveTimeCntReq.DiscardUnknown(m)
}

var xxx_messageInfo_IncrActorLiveTimeCntReq proto.InternalMessageInfo

func (m *IncrActorLiveTimeCntReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IncrActorLiveTimeCntReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *IncrActorLiveTimeCntReq) GetChannelLiveId() uint64 {
	if m != nil {
		return m.ChannelLiveId
	}
	return 0
}

func (m *IncrActorLiveTimeCntReq) GetIsLiving() bool {
	if m != nil {
		return m.IsLiving
	}
	return false
}

type IncrActorLiveTimeCntResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IncrActorLiveTimeCntResp) Reset()         { *m = IncrActorLiveTimeCntResp{} }
func (m *IncrActorLiveTimeCntResp) String() string { return proto.CompactTextString(m) }
func (*IncrActorLiveTimeCntResp) ProtoMessage()    {}
func (*IncrActorLiveTimeCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{18}
}
func (m *IncrActorLiveTimeCntResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IncrActorLiveTimeCntResp.Unmarshal(m, b)
}
func (m *IncrActorLiveTimeCntResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IncrActorLiveTimeCntResp.Marshal(b, m, deterministic)
}
func (dst *IncrActorLiveTimeCntResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IncrActorLiveTimeCntResp.Merge(dst, src)
}
func (m *IncrActorLiveTimeCntResp) XXX_Size() int {
	return xxx_messageInfo_IncrActorLiveTimeCntResp.Size(m)
}
func (m *IncrActorLiveTimeCntResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IncrActorLiveTimeCntResp.DiscardUnknown(m)
}

var xxx_messageInfo_IncrActorLiveTimeCntResp proto.InternalMessageInfo

type MissionAwardInfo struct {
	MissionType          uint32   `protobuf:"varint,1,opt,name=mission_type,json=missionType,proto3" json:"mission_type,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	AwardConfId          uint32   `protobuf:"varint,3,opt,name=award_conf_id,json=awardConfId,proto3" json:"award_conf_id,omitempty"`
	BeginTs              uint32   `protobuf:"varint,4,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,5,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MissionAwardInfo) Reset()         { *m = MissionAwardInfo{} }
func (m *MissionAwardInfo) String() string { return proto.CompactTextString(m) }
func (*MissionAwardInfo) ProtoMessage()    {}
func (*MissionAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{19}
}
func (m *MissionAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MissionAwardInfo.Unmarshal(m, b)
}
func (m *MissionAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MissionAwardInfo.Marshal(b, m, deterministic)
}
func (dst *MissionAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MissionAwardInfo.Merge(dst, src)
}
func (m *MissionAwardInfo) XXX_Size() int {
	return xxx_messageInfo_MissionAwardInfo.Size(m)
}
func (m *MissionAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MissionAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MissionAwardInfo proto.InternalMessageInfo

func (m *MissionAwardInfo) GetMissionType() uint32 {
	if m != nil {
		return m.MissionType
	}
	return 0
}

func (m *MissionAwardInfo) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *MissionAwardInfo) GetAwardConfId() uint32 {
	if m != nil {
		return m.AwardConfId
	}
	return 0
}

func (m *MissionAwardInfo) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *MissionAwardInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

// 发放直播间任务奖励配置
type GrantLiveMissionAwardReq struct {
	AwardInfo            *MissionAwardInfo `protobuf:"bytes,1,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GrantLiveMissionAwardReq) Reset()         { *m = GrantLiveMissionAwardReq{} }
func (m *GrantLiveMissionAwardReq) String() string { return proto.CompactTextString(m) }
func (*GrantLiveMissionAwardReq) ProtoMessage()    {}
func (*GrantLiveMissionAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{20}
}
func (m *GrantLiveMissionAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantLiveMissionAwardReq.Unmarshal(m, b)
}
func (m *GrantLiveMissionAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantLiveMissionAwardReq.Marshal(b, m, deterministic)
}
func (dst *GrantLiveMissionAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantLiveMissionAwardReq.Merge(dst, src)
}
func (m *GrantLiveMissionAwardReq) XXX_Size() int {
	return xxx_messageInfo_GrantLiveMissionAwardReq.Size(m)
}
func (m *GrantLiveMissionAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantLiveMissionAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GrantLiveMissionAwardReq proto.InternalMessageInfo

func (m *GrantLiveMissionAwardReq) GetAwardInfo() *MissionAwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

type GrantLiveMissionAwardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantLiveMissionAwardResp) Reset()         { *m = GrantLiveMissionAwardResp{} }
func (m *GrantLiveMissionAwardResp) String() string { return proto.CompactTextString(m) }
func (*GrantLiveMissionAwardResp) ProtoMessage()    {}
func (*GrantLiveMissionAwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{21}
}
func (m *GrantLiveMissionAwardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantLiveMissionAwardResp.Unmarshal(m, b)
}
func (m *GrantLiveMissionAwardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantLiveMissionAwardResp.Marshal(b, m, deterministic)
}
func (dst *GrantLiveMissionAwardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantLiveMissionAwardResp.Merge(dst, src)
}
func (m *GrantLiveMissionAwardResp) XXX_Size() int {
	return xxx_messageInfo_GrantLiveMissionAwardResp.Size(m)
}
func (m *GrantLiveMissionAwardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantLiveMissionAwardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GrantLiveMissionAwardResp proto.InternalMessageInfo

// 修改某个粉丝任务的更新时间
type UpdateFansMissionTimeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	MissionId            uint32   `protobuf:"varint,3,opt,name=mission_id,json=missionId,proto3" json:"mission_id,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFansMissionTimeReq) Reset()         { *m = UpdateFansMissionTimeReq{} }
func (m *UpdateFansMissionTimeReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFansMissionTimeReq) ProtoMessage()    {}
func (*UpdateFansMissionTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{22}
}
func (m *UpdateFansMissionTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFansMissionTimeReq.Unmarshal(m, b)
}
func (m *UpdateFansMissionTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFansMissionTimeReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFansMissionTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFansMissionTimeReq.Merge(dst, src)
}
func (m *UpdateFansMissionTimeReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFansMissionTimeReq.Size(m)
}
func (m *UpdateFansMissionTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFansMissionTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFansMissionTimeReq proto.InternalMessageInfo

func (m *UpdateFansMissionTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateFansMissionTimeReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *UpdateFansMissionTimeReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *UpdateFansMissionTimeReq) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type UpdateFansMissionTimeRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFansMissionTimeRsp) Reset()         { *m = UpdateFansMissionTimeRsp{} }
func (m *UpdateFansMissionTimeRsp) String() string { return proto.CompactTextString(m) }
func (*UpdateFansMissionTimeRsp) ProtoMessage()    {}
func (*UpdateFansMissionTimeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{23}
}
func (m *UpdateFansMissionTimeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFansMissionTimeRsp.Unmarshal(m, b)
}
func (m *UpdateFansMissionTimeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFansMissionTimeRsp.Marshal(b, m, deterministic)
}
func (dst *UpdateFansMissionTimeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFansMissionTimeRsp.Merge(dst, src)
}
func (m *UpdateFansMissionTimeRsp) XXX_Size() int {
	return xxx_messageInfo_UpdateFansMissionTimeRsp.Size(m)
}
func (m *UpdateFansMissionTimeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFansMissionTimeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFansMissionTimeRsp proto.InternalMessageInfo

// 触发定时任务
type TriggerTimerReq struct {
	TimerType            TriggerTimerReq_TimerType `protobuf:"varint,1,opt,name=timer_type,json=timerType,proto3,enum=channel_live_mission.TriggerTimerReq_TimerType" json:"timer_type,omitempty"`
	SettleTs             uint32                    `protobuf:"varint,2,opt,name=settle_ts,json=settleTs,proto3" json:"settle_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *TriggerTimerReq) Reset()         { *m = TriggerTimerReq{} }
func (m *TriggerTimerReq) String() string { return proto.CompactTextString(m) }
func (*TriggerTimerReq) ProtoMessage()    {}
func (*TriggerTimerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{24}
}
func (m *TriggerTimerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerTimerReq.Unmarshal(m, b)
}
func (m *TriggerTimerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerTimerReq.Marshal(b, m, deterministic)
}
func (dst *TriggerTimerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerTimerReq.Merge(dst, src)
}
func (m *TriggerTimerReq) XXX_Size() int {
	return xxx_messageInfo_TriggerTimerReq.Size(m)
}
func (m *TriggerTimerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerTimerReq.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerTimerReq proto.InternalMessageInfo

func (m *TriggerTimerReq) GetTimerType() TriggerTimerReq_TimerType {
	if m != nil {
		return m.TimerType
	}
	return TriggerTimerReq_Timer_Type_Invalid
}

func (m *TriggerTimerReq) GetSettleTs() uint32 {
	if m != nil {
		return m.SettleTs
	}
	return 0
}

type TriggerTimerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TriggerTimerResp) Reset()         { *m = TriggerTimerResp{} }
func (m *TriggerTimerResp) String() string { return proto.CompactTextString(m) }
func (*TriggerTimerResp) ProtoMessage()    {}
func (*TriggerTimerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_mission_910ae2877b3c5a27, []int{25}
}
func (m *TriggerTimerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerTimerResp.Unmarshal(m, b)
}
func (m *TriggerTimerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerTimerResp.Marshal(b, m, deterministic)
}
func (dst *TriggerTimerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerTimerResp.Merge(dst, src)
}
func (m *TriggerTimerResp) XXX_Size() int {
	return xxx_messageInfo_TriggerTimerResp.Size(m)
}
func (m *TriggerTimerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerTimerResp.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerTimerResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ChannelLiveMissionInfo)(nil), "channel_live_mission.ChannelLiveMissionInfo")
	proto.RegisterType((*GetUserMissionReq)(nil), "channel_live_mission.GetUserMissionReq")
	proto.RegisterType((*GetUserMissionResp)(nil), "channel_live_mission.GetUserMissionResp")
	proto.RegisterType((*HandleUserMissionReq)(nil), "channel_live_mission.HandleUserMissionReq")
	proto.RegisterType((*HandleUserMissionResp)(nil), "channel_live_mission.HandleUserMissionResp")
	proto.RegisterType((*SwitchTimeUserMissionTsReq)(nil), "channel_live_mission.SwitchTimeUserMissionTsReq")
	proto.RegisterType((*SwitchTimeUserMissionTsResp)(nil), "channel_live_mission.SwitchTimeUserMissionTsResp")
	proto.RegisterType((*GetFansMissionReq)(nil), "channel_live_mission.GetFansMissionReq")
	proto.RegisterType((*GetFansMissionResp)(nil), "channel_live_mission.GetFansMissionResp")
	proto.RegisterType((*HandleFansMissionReq)(nil), "channel_live_mission.HandleFansMissionReq")
	proto.RegisterType((*HandleFansMissionResp)(nil), "channel_live_mission.HandleFansMissionResp")
	proto.RegisterType((*ActorMissionInfo)(nil), "channel_live_mission.ActorMissionInfo")
	proto.RegisterType((*ActorSubMissionInfo)(nil), "channel_live_mission.ActorSubMissionInfo")
	proto.RegisterType((*GetActorMissionReq)(nil), "channel_live_mission.GetActorMissionReq")
	proto.RegisterType((*GetActorMissionResp)(nil), "channel_live_mission.GetActorMissionResp")
	proto.RegisterType((*ProcessActorMissionDesc)(nil), "channel_live_mission.ProcessActorMissionDesc")
	proto.RegisterType((*GetProcessActorMissionDescResp)(nil), "channel_live_mission.GetProcessActorMissionDescResp")
	proto.RegisterType((*IncrActorLiveTimeCntReq)(nil), "channel_live_mission.IncrActorLiveTimeCntReq")
	proto.RegisterType((*IncrActorLiveTimeCntResp)(nil), "channel_live_mission.IncrActorLiveTimeCntResp")
	proto.RegisterType((*MissionAwardInfo)(nil), "channel_live_mission.MissionAwardInfo")
	proto.RegisterType((*GrantLiveMissionAwardReq)(nil), "channel_live_mission.GrantLiveMissionAwardReq")
	proto.RegisterType((*GrantLiveMissionAwardResp)(nil), "channel_live_mission.GrantLiveMissionAwardResp")
	proto.RegisterType((*UpdateFansMissionTimeReq)(nil), "channel_live_mission.UpdateFansMissionTimeReq")
	proto.RegisterType((*UpdateFansMissionTimeRsp)(nil), "channel_live_mission.UpdateFansMissionTimeRsp")
	proto.RegisterType((*TriggerTimerReq)(nil), "channel_live_mission.TriggerTimerReq")
	proto.RegisterType((*TriggerTimerResp)(nil), "channel_live_mission.TriggerTimerResp")
	proto.RegisterEnum("channel_live_mission.MissionStatus", MissionStatus_name, MissionStatus_value)
	proto.RegisterEnum("channel_live_mission.ChannelLiveMissionInfo_MissionType", ChannelLiveMissionInfo_MissionType_name, ChannelLiveMissionInfo_MissionType_value)
	proto.RegisterEnum("channel_live_mission.ChannelLiveMissionInfo_MissionOperType", ChannelLiveMissionInfo_MissionOperType_name, ChannelLiveMissionInfo_MissionOperType_value)
	proto.RegisterEnum("channel_live_mission.ActorMissionInfo_MissionType", ActorMissionInfo_MissionType_name, ActorMissionInfo_MissionType_value)
	proto.RegisterEnum("channel_live_mission.MissionAwardInfo_MissionType", MissionAwardInfo_MissionType_name, MissionAwardInfo_MissionType_value)
	proto.RegisterEnum("channel_live_mission.TriggerTimerReq_TimerType", TriggerTimerReq_TimerType_name, TriggerTimerReq_TimerType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelLiveMissionClient is the client API for ChannelLiveMission service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelLiveMissionClient interface {
	GetUserMission(ctx context.Context, in *GetUserMissionReq, opts ...grpc.CallOption) (*GetUserMissionResp, error)
	HandleUserMission(ctx context.Context, in *HandleUserMissionReq, opts ...grpc.CallOption) (*HandleUserMissionResp, error)
	GetFansMission(ctx context.Context, in *GetFansMissionReq, opts ...grpc.CallOption) (*GetFansMissionResp, error)
	HandleFansMission(ctx context.Context, in *HandleFansMissionReq, opts ...grpc.CallOption) (*HandleFansMissionResp, error)
	GetActorMission(ctx context.Context, in *GetActorMissionReq, opts ...grpc.CallOption) (*GetActorMissionResp, error)
	IncrActorLiveTimeCnt(ctx context.Context, in *IncrActorLiveTimeCntReq, opts ...grpc.CallOption) (*IncrActorLiveTimeCntResp, error)
	SwitchTimeUserMissionTs(ctx context.Context, in *SwitchTimeUserMissionTsReq, opts ...grpc.CallOption) (*SwitchTimeUserMissionTsResp, error)
	// 发放主播奖励积分对账
	GetAwardAnchorScoreCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardAnchorScoreOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 补单 放在上游
	FixAnchorScoreByOrderId(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	GrantLiveMissionAward(ctx context.Context, in *GrantLiveMissionAwardReq, opts ...grpc.CallOption) (*GrantLiveMissionAwardResp, error)
	// 修改某个粉丝任务的更新时间
	UpdateFansMissionTime(ctx context.Context, in *UpdateFansMissionTimeReq, opts ...grpc.CallOption) (*UpdateFansMissionTimeRsp, error)
	// 触发定时任务
	TriggerTimer(ctx context.Context, in *TriggerTimerReq, opts ...grpc.CallOption) (*TriggerTimerResp, error)
}

type channelLiveMissionClient struct {
	cc *grpc.ClientConn
}

func NewChannelLiveMissionClient(cc *grpc.ClientConn) ChannelLiveMissionClient {
	return &channelLiveMissionClient{cc}
}

func (c *channelLiveMissionClient) GetUserMission(ctx context.Context, in *GetUserMissionReq, opts ...grpc.CallOption) (*GetUserMissionResp, error) {
	out := new(GetUserMissionResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/GetUserMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) HandleUserMission(ctx context.Context, in *HandleUserMissionReq, opts ...grpc.CallOption) (*HandleUserMissionResp, error) {
	out := new(HandleUserMissionResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/HandleUserMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) GetFansMission(ctx context.Context, in *GetFansMissionReq, opts ...grpc.CallOption) (*GetFansMissionResp, error) {
	out := new(GetFansMissionResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/GetFansMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) HandleFansMission(ctx context.Context, in *HandleFansMissionReq, opts ...grpc.CallOption) (*HandleFansMissionResp, error) {
	out := new(HandleFansMissionResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/HandleFansMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) GetActorMission(ctx context.Context, in *GetActorMissionReq, opts ...grpc.CallOption) (*GetActorMissionResp, error) {
	out := new(GetActorMissionResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/GetActorMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) IncrActorLiveTimeCnt(ctx context.Context, in *IncrActorLiveTimeCntReq, opts ...grpc.CallOption) (*IncrActorLiveTimeCntResp, error) {
	out := new(IncrActorLiveTimeCntResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/IncrActorLiveTimeCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) SwitchTimeUserMissionTs(ctx context.Context, in *SwitchTimeUserMissionTsReq, opts ...grpc.CallOption) (*SwitchTimeUserMissionTsResp, error) {
	out := new(SwitchTimeUserMissionTsResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/SwitchTimeUserMissionTs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) GetAwardAnchorScoreCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/GetAwardAnchorScoreCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) GetAwardAnchorScoreOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/GetAwardAnchorScoreOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) FixAnchorScoreByOrderId(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/FixAnchorScoreByOrderId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) GrantLiveMissionAward(ctx context.Context, in *GrantLiveMissionAwardReq, opts ...grpc.CallOption) (*GrantLiveMissionAwardResp, error) {
	out := new(GrantLiveMissionAwardResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/GrantLiveMissionAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) UpdateFansMissionTime(ctx context.Context, in *UpdateFansMissionTimeReq, opts ...grpc.CallOption) (*UpdateFansMissionTimeRsp, error) {
	out := new(UpdateFansMissionTimeRsp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/UpdateFansMissionTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveMissionClient) TriggerTimer(ctx context.Context, in *TriggerTimerReq, opts ...grpc.CallOption) (*TriggerTimerResp, error) {
	out := new(TriggerTimerResp)
	err := c.cc.Invoke(ctx, "/channel_live_mission.ChannelLiveMission/TriggerTimer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelLiveMissionServer is the server API for ChannelLiveMission service.
type ChannelLiveMissionServer interface {
	GetUserMission(context.Context, *GetUserMissionReq) (*GetUserMissionResp, error)
	HandleUserMission(context.Context, *HandleUserMissionReq) (*HandleUserMissionResp, error)
	GetFansMission(context.Context, *GetFansMissionReq) (*GetFansMissionResp, error)
	HandleFansMission(context.Context, *HandleFansMissionReq) (*HandleFansMissionResp, error)
	GetActorMission(context.Context, *GetActorMissionReq) (*GetActorMissionResp, error)
	IncrActorLiveTimeCnt(context.Context, *IncrActorLiveTimeCntReq) (*IncrActorLiveTimeCntResp, error)
	SwitchTimeUserMissionTs(context.Context, *SwitchTimeUserMissionTsReq) (*SwitchTimeUserMissionTsResp, error)
	// 发放主播奖励积分对账
	GetAwardAnchorScoreCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardAnchorScoreOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 补单 放在上游
	FixAnchorScoreByOrderId(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	GrantLiveMissionAward(context.Context, *GrantLiveMissionAwardReq) (*GrantLiveMissionAwardResp, error)
	// 修改某个粉丝任务的更新时间
	UpdateFansMissionTime(context.Context, *UpdateFansMissionTimeReq) (*UpdateFansMissionTimeRsp, error)
	// 触发定时任务
	TriggerTimer(context.Context, *TriggerTimerReq) (*TriggerTimerResp, error)
}

func RegisterChannelLiveMissionServer(s *grpc.Server, srv ChannelLiveMissionServer) {
	s.RegisterService(&_ChannelLiveMission_serviceDesc, srv)
}

func _ChannelLiveMission_GetUserMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).GetUserMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/GetUserMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).GetUserMission(ctx, req.(*GetUserMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_HandleUserMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleUserMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).HandleUserMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/HandleUserMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).HandleUserMission(ctx, req.(*HandleUserMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_GetFansMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFansMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).GetFansMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/GetFansMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).GetFansMission(ctx, req.(*GetFansMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_HandleFansMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleFansMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).HandleFansMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/HandleFansMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).HandleFansMission(ctx, req.(*HandleFansMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_GetActorMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActorMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).GetActorMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/GetActorMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).GetActorMission(ctx, req.(*GetActorMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_IncrActorLiveTimeCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrActorLiveTimeCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).IncrActorLiveTimeCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/IncrActorLiveTimeCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).IncrActorLiveTimeCnt(ctx, req.(*IncrActorLiveTimeCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_SwitchTimeUserMissionTs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchTimeUserMissionTsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).SwitchTimeUserMissionTs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/SwitchTimeUserMissionTs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).SwitchTimeUserMissionTs(ctx, req.(*SwitchTimeUserMissionTsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_GetAwardAnchorScoreCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).GetAwardAnchorScoreCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/GetAwardAnchorScoreCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).GetAwardAnchorScoreCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_GetAwardAnchorScoreOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).GetAwardAnchorScoreOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/GetAwardAnchorScoreOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).GetAwardAnchorScoreOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_FixAnchorScoreByOrderId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).FixAnchorScoreByOrderId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/FixAnchorScoreByOrderId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).FixAnchorScoreByOrderId(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_GrantLiveMissionAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantLiveMissionAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).GrantLiveMissionAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/GrantLiveMissionAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).GrantLiveMissionAward(ctx, req.(*GrantLiveMissionAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_UpdateFansMissionTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFansMissionTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).UpdateFansMissionTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/UpdateFansMissionTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).UpdateFansMissionTime(ctx, req.(*UpdateFansMissionTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveMission_TriggerTimer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerTimerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveMissionServer).TriggerTimer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_mission.ChannelLiveMission/TriggerTimer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveMissionServer).TriggerTimer(ctx, req.(*TriggerTimerReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelLiveMission_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_live_mission.ChannelLiveMission",
	HandlerType: (*ChannelLiveMissionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserMission",
			Handler:    _ChannelLiveMission_GetUserMission_Handler,
		},
		{
			MethodName: "HandleUserMission",
			Handler:    _ChannelLiveMission_HandleUserMission_Handler,
		},
		{
			MethodName: "GetFansMission",
			Handler:    _ChannelLiveMission_GetFansMission_Handler,
		},
		{
			MethodName: "HandleFansMission",
			Handler:    _ChannelLiveMission_HandleFansMission_Handler,
		},
		{
			MethodName: "GetActorMission",
			Handler:    _ChannelLiveMission_GetActorMission_Handler,
		},
		{
			MethodName: "IncrActorLiveTimeCnt",
			Handler:    _ChannelLiveMission_IncrActorLiveTimeCnt_Handler,
		},
		{
			MethodName: "SwitchTimeUserMissionTs",
			Handler:    _ChannelLiveMission_SwitchTimeUserMissionTs_Handler,
		},
		{
			MethodName: "GetAwardAnchorScoreCount",
			Handler:    _ChannelLiveMission_GetAwardAnchorScoreCount_Handler,
		},
		{
			MethodName: "GetAwardAnchorScoreOrderIds",
			Handler:    _ChannelLiveMission_GetAwardAnchorScoreOrderIds_Handler,
		},
		{
			MethodName: "FixAnchorScoreByOrderId",
			Handler:    _ChannelLiveMission_FixAnchorScoreByOrderId_Handler,
		},
		{
			MethodName: "GrantLiveMissionAward",
			Handler:    _ChannelLiveMission_GrantLiveMissionAward_Handler,
		},
		{
			MethodName: "UpdateFansMissionTime",
			Handler:    _ChannelLiveMission_UpdateFansMissionTime_Handler,
		},
		{
			MethodName: "TriggerTimer",
			Handler:    _ChannelLiveMission_TriggerTimer_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-live-mission/channel-live-mission.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-live-mission/channel-live-mission.proto", fileDescriptor_channel_live_mission_910ae2877b3c5a27)
}

var fileDescriptor_channel_live_mission_910ae2877b3c5a27 = []byte{
	// 1676 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xcd, 0x72, 0xe3, 0x4a,
	0x15, 0x8e, 0xec, 0xfc, 0xd8, 0xc7, 0x71, 0xa2, 0xe9, 0x49, 0x26, 0x8a, 0xc3, 0x70, 0x73, 0x75,
	0xc9, 0x90, 0x3b, 0x43, 0x1c, 0x08, 0x3f, 0xc5, 0x8a, 0xaa, 0xdc, 0x64, 0x12, 0x4c, 0xe5, 0x87,
	0xeb, 0x24, 0x50, 0x45, 0x15, 0xa5, 0x92, 0xa5, 0x8e, 0xdd, 0x15, 0xa9, 0xa5, 0xab, 0x6e, 0x39,
	0x98, 0x15, 0x14, 0x4b, 0x16, 0xac, 0x29, 0x1e, 0x81, 0x37, 0xe0, 0x45, 0xd8, 0xf2, 0x08, 0xec,
	0xd9, 0x50, 0xdd, 0x6a, 0x39, 0x92, 0x2c, 0x99, 0x84, 0xaa, 0xd9, 0xa9, 0x4f, 0x9f, 0x3e, 0x7d,
	0x7e, 0xbe, 0xf3, 0x9d, 0xb6, 0xe1, 0x67, 0x9c, 0x1f, 0x7e, 0x13, 0x13, 0xe7, 0x81, 0x11, 0x6f,
	0x8c, 0xa3, 0x43, 0x67, 0x64, 0x53, 0x8a, 0xbd, 0x03, 0x8f, 0x8c, 0xf1, 0x81, 0x4f, 0x18, 0x23,
	0x01, 0x2d, 0x15, 0x76, 0xc3, 0x28, 0xe0, 0x01, 0xda, 0x50, 0x7b, 0x96, 0xd8, 0xb3, 0xd4, 0x5e,
	0xa7, 0x5b, 0xb0, 0x1a, 0x61, 0x27, 0xa0, 0x0e, 0xf1, 0xf0, 0xc1, 0xf8, 0x28, 0xb7, 0x48, 0xac,
	0x98, 0x7f, 0x5d, 0x82, 0x37, 0x27, 0x89, 0xa1, 0x0b, 0x32, 0xc6, 0x97, 0x89, 0x99, 0x1e, 0xbd,
	0x0f, 0xd0, 0x5b, 0x00, 0x65, 0xd5, 0x22, 0xae, 0xa1, 0xed, 0x6a, 0xfb, 0xed, 0x7e, 0x53, 0x49,
	0x7a, 0x2e, 0xfa, 0x1c, 0x56, 0xd3, 0x6d, 0x17, 0x33, 0xc7, 0xa8, 0xed, 0x6a, 0xfb, 0xcd, 0x7e,
	0x4b, 0xc9, 0x4e, 0x31, 0x73, 0xb2, 0x2a, 0x7c, 0x12, 0x62, 0xa3, 0x2e, 0x6d, 0xa4, 0x2a, 0xb7,
	0x93, 0x10, 0x67, 0x55, 0xa8, 0xed, 0x63, 0x63, 0x31, 0x67, 0xe5, 0xca, 0xf6, 0x31, 0xda, 0x07,
	0x7d, 0xea, 0x87, 0x13, 0x50, 0x2b, 0x8e, 0x3c, 0x63, 0x49, 0xaa, 0xad, 0xa5, 0xde, 0x38, 0x01,
	0xbd, 0x8b, 0x3c, 0xf4, 0x19, 0xb4, 0xe2, 0xd0, 0xb5, 0x39, 0xb6, 0x38, 0xf1, 0xb1, 0xb1, 0xbc,
	0xab, 0xed, 0xd7, 0xfb, 0x90, 0x88, 0x6e, 0x89, 0x8f, 0xd1, 0x1b, 0x58, 0x66, 0xdc, 0xe6, 0x31,
	0x33, 0x56, 0xa4, 0x2b, 0x6a, 0x25, 0x42, 0xbd, 0x27, 0x94, 0xb0, 0x91, 0xe5, 0x50, 0x6e, 0x34,
	0x92, 0x50, 0x13, 0xc9, 0x09, 0xe5, 0x68, 0x1b, 0x1a, 0xc3, 0xc0, 0xf6, 0xe4, 0x66, 0x53, 0x6e,
	0xae, 0x88, 0xb5, 0xd8, 0xda, 0x81, 0xa6, 0xed, 0xf0, 0x20, 0xb2, 0x62, 0xe2, 0x1a, 0x20, 0xf7,
	0x1a, 0x52, 0x70, 0x47, 0x5c, 0xb1, 0x19, 0x84, 0x38, 0x4a, 0x82, 0x6f, 0x25, 0x9b, 0x42, 0x20,
	0x23, 0x17, 0x27, 0x1f, 0xed, 0xc8, 0xb5, 0x68, 0xec, 0x1b, 0xab, 0xea, 0xa4, 0x10, 0x5c, 0xc5,
	0x3e, 0xfa, 0x02, 0xda, 0x61, 0x14, 0x0c, 0x23, 0xcc, 0x58, 0x92, 0xdd, 0xb6, 0x0c, 0x78, 0x35,
	0x15, 0xca, 0xf4, 0x7e, 0x06, 0xad, 0x07, 0x4a, 0x86, 0x23, 0x9e, 0xa8, 0xac, 0x49, 0x15, 0x48,
	0x44, 0xa9, 0xc2, 0x20, 0x1e, 0x0c, 0x3c, 0x9c, 0x28, 0xac, 0x27, 0x0a, 0x89, 0x48, 0x28, 0x98,
	0x3d, 0x68, 0x5d, 0x66, 0x8a, 0xd1, 0x82, 0x95, 0x3b, 0xfa, 0x40, 0x83, 0x47, 0xaa, 0x2f, 0xa0,
	0x26, 0x2c, 0x9d, 0xda, 0xc4, 0x9b, 0xe8, 0x1a, 0xd2, 0x61, 0xf5, 0xd8, 0x71, 0x62, 0x3f, 0xf6,
	0x6c, 0x4e, 0xc6, 0x58, 0xaf, 0x09, 0xc9, 0x49, 0x40, 0x39, 0xa1, 0xb1, 0xcd, 0x49, 0x40, 0xf5,
	0xba, 0xf9, 0x35, 0xac, 0x2b, 0x53, 0xd7, 0x69, 0x84, 0x0d, 0x58, 0xbc, 0x0a, 0x28, 0xd6, 0x17,
	0xd0, 0x2a, 0x34, 0xce, 0xc9, 0x3d, 0xef, 0xdb, 0xce, 0x83, 0xae, 0xa1, 0x0d, 0xd0, 0x6f, 0x46,
	0x76, 0x84, 0x05, 0xe0, 0x14, 0xf6, 0x12, 0x93, 0xbf, 0x88, 0xfd, 0xf0, 0xcc, 0xa6, 0x4c, 0xe8,
	0xea, 0x75, 0x73, 0x0f, 0x5e, 0x9d, 0x63, 0x7e, 0xc7, 0x70, 0xa4, 0x2c, 0xf7, 0xf1, 0x37, 0x48,
	0x87, 0x7a, 0x3c, 0x85, 0xa3, 0xf8, 0x34, 0x31, 0xa0, 0xa2, 0x1a, 0x0b, 0xd1, 0xf5, 0x13, 0xb0,
	0x3c, 0xc2, 0xb8, 0xa1, 0xed, 0xd6, 0xf7, 0x5b, 0x47, 0xdf, 0xeb, 0x96, 0x75, 0x4d, 0xb7, 0xbc,
	0x03, 0xa6, 0x30, 0xbc, 0x20, 0x8c, 0x9b, 0xff, 0xd0, 0x60, 0xe3, 0xe7, 0x36, 0x75, 0x3d, 0xfc,
	0xbf, 0x3c, 0x2a, 0x74, 0x4e, 0xad, 0xd8, 0x39, 0x6f, 0x01, 0x52, 0x2f, 0x88, 0xab, 0x9a, 0xa2,
	0xa9, 0x24, 0x3d, 0x17, 0xbd, 0x83, 0x75, 0x42, 0x9d, 0xc8, 0xca, 0x20, 0x72, 0x51, 0xea, 0xb4,
	0x85, 0xf8, 0x6c, 0x8a, 0xca, 0xf7, 0xf0, 0xca, 0xa6, 0x9c, 0x58, 0x11, 0x0e, 0xb1, 0xcd, 0xb1,
	0x6b, 0xf9, 0xd8, 0x97, 0x8d, 0xd1, 0xee, 0xaf, 0x8b, 0x8d, 0xbe, 0x92, 0x5f, 0x62, 0xdf, 0x3c,
	0x84, 0xcd, 0x12, 0xdf, 0x59, 0x98, 0xe9, 0x08, 0x2d, 0xdb, 0x11, 0xe6, 0x19, 0x74, 0x6e, 0x1e,
	0x09, 0x77, 0x46, 0xa2, 0x6f, 0x32, 0x87, 0x6e, 0x59, 0x79, 0xc8, 0xc2, 0x8e, 0xd4, 0x97, 0xe1,
	0x36, 0xfa, 0x6a, 0x65, 0xbe, 0x85, 0x9d, 0x4a, 0x3b, 0x2c, 0x34, 0x6d, 0x59, 0x62, 0x51, 0xf3,
	0xb9, 0x09, 0xcd, 0x75, 0x59, 0xad, 0xd0, 0x65, 0xf3, 0xd3, 0xa9, 0xe0, 0x91, 0xbb, 0xe2, 0x53,
	0xc0, 0xe3, 0xef, 0x53, 0x78, 0x7c, 0xca, 0x68, 0x0a, 0xd0, 0x5a, 0x2c, 0x42, 0xab, 0x04, 0x3b,
	0x4b, 0x25, 0xd8, 0x79, 0xc2, 0x43, 0x31, 0x2f, 0x55, 0x78, 0xf8, 0x67, 0x1d, 0xf4, 0x63, 0xe1,
	0x63, 0x76, 0x42, 0x14, 0xc9, 0x5b, 0x9b, 0x25, 0xef, 0x2f, 0xa0, 0x3d, 0xcd, 0x33, 0x1e, 0x63,
	0x4f, 0xc5, 0x9b, 0x9e, 0xbb, 0x10, 0x32, 0x11, 0x54, 0x42, 0x85, 0x92, 0xa6, 0xea, 0xd2, 0x4a,
	0x42, 0x8e, 0xa5, 0x63, 0x64, 0x71, 0x76, 0x8c, 0xec, 0x41, 0x3a, 0x0b, 0x2c, 0xe5, 0xbe, 0x0a,
	0x5b, 0x49, 0x6f, 0x12, 0x9e, 0xbf, 0x80, 0x55, 0x16, 0x0f, 0xd2, 0xba, 0x32, 0x63, 0x59, 0x56,
	0xfd, 0xcb, 0xf2, 0xaa, 0xcb, 0x70, 0x6f, 0xe2, 0x41, 0xae, 0xe4, 0x6c, 0xba, 0x66, 0x32, 0xd9,
	0xcc, 0x72, 0xe2, 0x28, 0xc2, 0x94, 0x5b, 0x6c, 0x14, 0x3c, 0xca, 0xb1, 0xd2, 0xe8, 0xb7, 0x09,
	0x3b, 0x49, 0xa4, 0x37, 0xa3, 0xe0, 0xd1, 0xfc, 0x9b, 0x96, 0xa7, 0x59, 0x04, 0x6b, 0x8a, 0x66,
	0x95, 0x54, 0x5f, 0x10, 0x9c, 0x78, 0x6a, 0x4f, 0x7a, 0xd4, 0x09, 0xfc, 0x14, 0x63, 0xba, 0x86,
	0x0c, 0xd8, 0x38, 0xb5, 0x27, 0xa2, 0x75, 0x2e, 0x30, 0x1d, 0xf2, 0x51, 0xba, 0x53, 0x43, 0x9b,
	0xf0, 0xea, 0xd7, 0x18, 0x3f, 0xe4, 0x0f, 0xd4, 0xd1, 0x36, 0x6c, 0x0a, 0xf1, 0xec, 0x89, 0x45,
	0xf4, 0x06, 0xd0, 0x65, 0x40, 0xf9, 0x28, 0x7f, 0x64, 0xc9, 0xf4, 0xe1, 0x75, 0x49, 0xa4, 0x62,
	0xe6, 0x89, 0x54, 0x65, 0xea, 0xba, 0xc2, 0xe2, 0x41, 0x5a, 0xd3, 0xfc, 0x70, 0xaa, 0x95, 0x0c,
	0xa7, 0x27, 0x20, 0xd5, 0x73, 0x40, 0xfa, 0x28, 0xdb, 0x31, 0x0b, 0xa5, 0x4a, 0x0e, 0xcd, 0xf4,
	0x41, 0xad, 0xd8, 0xd5, 0x7f, 0xd2, 0xe0, 0xf5, 0x8c, 0x1d, 0x16, 0xa2, 0x5e, 0x69, 0x5f, 0xbf,
	0x9b, 0x53, 0xe1, 0xaa, 0x8e, 0x7e, 0xc6, 0x03, 0xc7, 0xfc, 0x2d, 0x6c, 0xfd, 0x32, 0x0a, 0x1c,
	0xcc, 0x58, 0xd6, 0x94, 0x8c, 0x7f, 0x26, 0x49, 0x5a, 0x49, 0x92, 0xf2, 0xc0, 0xaf, 0x15, 0x80,
	0x6f, 0xfe, 0x51, 0x83, 0x6f, 0x9f, 0x63, 0x5e, 0x71, 0x85, 0x8c, 0xd7, 0x82, 0x8d, 0x30, 0xd9,
	0xb6, 0x4a, 0xe2, 0x3e, 0x28, 0x8f, 0xbb, 0xca, 0x20, 0x52, 0xa6, 0x2e, 0x33, 0xbc, 0xf6, 0x17,
	0x0d, 0xb6, 0x7a, 0xd4, 0x89, 0xa4, 0xb2, 0x60, 0x40, 0x81, 0xad, 0x13, 0xca, 0xff, 0x9f, 0xaa,
	0x89, 0x8e, 0xc9, 0x39, 0xa4, 0x18, 0x6e, 0xb1, 0xdf, 0x76, 0x9e, 0x28, 0xb6, 0x27, 0x19, 0x92,
	0x30, 0xa1, 0x42, 0xe8, 0x50, 0xb6, 0x7b, 0xa3, 0xdf, 0x20, 0xec, 0x42, 0xae, 0xcd, 0x0e, 0x18,
	0xe5, 0x0e, 0xb1, 0xd0, 0xfc, 0x97, 0x06, 0xba, 0xf2, 0xfe, 0x58, 0xa4, 0xb1, 0x48, 0x53, 0x92,
	0x3f, 0xb4, 0x59, 0xfe, 0x10, 0x85, 0xa0, 0xce, 0x28, 0xc7, 0xc9, 0xcd, 0x44, 0x22, 0x48, 0xd9,
	0x84, 0x76, 0x52, 0x27, 0x27, 0xa0, 0xf7, 0x4f, 0xbc, 0xdc, 0x92, 0xc2, 0x93, 0x80, 0xde, 0xf7,
	0x5c, 0xd1, 0x30, 0x03, 0x3c, 0x24, 0xd4, 0xe2, 0x4c, 0x31, 0xd4, 0x8a, 0x5c, 0xdf, 0x32, 0xb4,
	0x09, 0xcb, 0x98, 0xba, 0x62, 0x23, 0x61, 0xa5, 0x25, 0x4c, 0xdd, 0x5b, 0x66, 0xfe, 0x28, 0x4f,
	0x0b, 0xaf, 0xa7, 0x2f, 0x28, 0xab, 0x47, 0xc7, 0xb6, 0x47, 0x5c, 0x7d, 0x41, 0xbc, 0x8a, 0x52,
	0xa1, 0xa0, 0x6a, 0x5d, 0x33, 0x6d, 0x30, 0xce, 0x23, 0x9b, 0xf2, 0xcc, 0x34, 0x92, 0xa1, 0x8a,
	0x82, 0x7c, 0x4c, 0xf1, 0x44, 0xe8, 0x7d, 0x20, 0xe3, 0xac, 0xc4, 0x7e, 0x31, 0x4b, 0x0a, 0x77,
	0xe2, 0xd3, 0xdc, 0x81, 0xed, 0x8a, 0x2b, 0x58, 0x68, 0xfe, 0x59, 0x03, 0xe3, 0x4e, 0x3e, 0xa9,
	0x33, 0xb3, 0x43, 0xd4, 0xa0, 0x12, 0x11, 0xf3, 0x32, 0x9b, 0x9f, 0x67, 0xf5, 0xe2, 0x3c, 0x2b,
	0xbc, 0xe8, 0x93, 0xbc, 0x66, 0x5e, 0xf4, 0x02, 0x0c, 0xe5, 0xce, 0xb0, 0xd0, 0xfc, 0xb7, 0x06,
	0xeb, 0xb7, 0x11, 0x19, 0x0e, 0x71, 0x24, 0x44, 0x91, 0x70, 0xf0, 0x0a, 0x40, 0x58, 0x8a, 0x9e,
	0x90, 0xb0, 0x76, 0x74, 0x58, 0x9e, 0xa1, 0xc2, 0xd1, 0xae, 0xfc, 0x10, 0x95, 0xea, 0x37, 0x79,
	0xfa, 0x29, 0x90, 0xca, 0x30, 0xe7, 0x1e, 0x16, 0xd5, 0x55, 0xb3, 0x3c, 0x11, 0xdc, 0x32, 0x33,
	0x84, 0xe6, 0xf4, 0x90, 0xe0, 0x5f, 0xb9, 0xb0, 0xc4, 0x2a, 0x53, 0xe1, 0xef, 0xc0, 0x6e, 0x46,
	0x2e, 0xf3, 0x2c, 0x28, 0x3c, 0xdb, 0x9c, 0xba, 0x86, 0xf6, 0xe0, 0xf3, 0xa2, 0x96, 0x64, 0xf3,
	0x9c, 0x5a, 0xcd, 0x44, 0xa0, 0xe7, 0xdd, 0x66, 0xe1, 0xfb, 0x0f, 0xd0, 0xbe, 0xcc, 0x4d, 0xc1,
	0x35, 0x00, 0xc5, 0x00, 0x84, 0x0e, 0xf5, 0x05, 0x04, 0xb0, 0x9c, 0xbc, 0x0c, 0x74, 0xed, 0xe8,
	0x3f, 0x00, 0x68, 0xf6, 0xb9, 0x83, 0x30, 0xac, 0xe5, 0xdf, 0xd8, 0xe8, 0xbb, 0xe5, 0x49, 0x9b,
	0x79, 0xb0, 0x77, 0xf6, 0x9f, 0xa7, 0xc8, 0x42, 0x73, 0x01, 0x51, 0x78, 0x35, 0xf3, 0x4c, 0x45,
	0xef, 0xcb, 0x0d, 0x94, 0xbd, 0xc5, 0x3b, 0x1f, 0x9e, 0xad, 0x2b, 0xef, 0x4b, 0xc2, 0xca, 0x40,
	0x67, 0x4e, 0x58, 0xf9, 0x67, 0xdd, 0x9c, 0xb0, 0x0a, 0x4f, 0xaa, 0x6c, 0x58, 0xd9, 0x9b, 0xe6,
	0x86, 0x55, 0xb8, 0xec, 0xc3, 0xb3, 0x75, 0xe5, 0x7d, 0x23, 0x58, 0x2f, 0xcc, 0x46, 0x54, 0xed,
	0x6e, 0x61, 0x14, 0x77, 0xbe, 0x7c, 0xa6, 0xa6, 0xbc, 0xe9, 0x11, 0x36, 0xca, 0xb8, 0x18, 0x55,
	0x0c, 0x9e, 0x8a, 0x41, 0xd2, 0xe9, 0xbe, 0x44, 0x5d, 0x5e, 0xfc, 0x07, 0x0d, 0xb6, 0x2a, 0x7e,
	0x58, 0xa0, 0xef, 0x97, 0x5b, 0xab, 0xfe, 0x3d, 0xd3, 0xf9, 0xc1, 0x0b, 0x4f, 0x48, 0x17, 0xae,
	0xc1, 0x10, 0x49, 0x11, 0xad, 0x78, 0x2c, 0xf9, 0xec, 0xc6, 0x09, 0x22, 0x7c, 0x12, 0xc4, 0x94,
	0xa3, 0xed, 0x6e, 0x3f, 0xfd, 0xaf, 0xe5, 0x57, 0x47, 0x92, 0x39, 0xfa, 0x36, 0x1d, 0x0a, 0x8a,
	0xec, 0xbc, 0xc9, 0x6d, 0x49, 0x75, 0x65, 0xf0, 0x0e, 0x76, 0x4a, 0x0c, 0x5e, 0x47, 0x2e, 0x8e,
	0x7a, 0x2e, 0x9b, 0x67, 0x33, 0xbf, 0x95, 0x9e, 0x50, 0x66, 0xbf, 0x86, 0xad, 0x33, 0xf2, 0xbb,
	0x8c, 0xc5, 0xaf, 0x26, 0x4a, 0x03, 0x7d, 0x2b, 0x77, 0xae, 0x8f, 0x43, 0xcf, 0x76, 0x92, 0x0b,
	0x67, 0x3d, 0xfd, 0xe8, 0x87, 0x7c, 0xa2, 0x4c, 0xfe, 0x1e, 0x36, 0x4b, 0x07, 0x04, 0xaa, 0x28,
	0x64, 0xd5, 0xc0, 0xea, 0x1c, 0xbe, 0x48, 0x5f, 0xde, 0x3d, 0x81, 0xcd, 0x52, 0xc6, 0xaf, 0xba,
	0xbb, 0x6a, 0x56, 0x75, 0x5e, 0xa4, 0x2f, 0xaf, 0xb6, 0x60, 0x35, 0xcb, 0xae, 0x68, 0xef, 0x59,
	0x83, 0xa3, 0xf3, 0xee, 0x39, 0x6a, 0xe2, 0x82, 0xaf, 0x7e, 0xfa, 0x9b, 0x9f, 0x0c, 0x03, 0xcf,
	0xa6, 0xc3, 0xee, 0x8f, 0x8f, 0x38, 0xef, 0x3a, 0x81, 0x7f, 0x28, 0xff, 0xa6, 0x73, 0x02, 0xef,
	0x90, 0xe1, 0x68, 0x4c, 0x1c, 0xcc, 0x4a, 0xff, 0x13, 0x1c, 0x2c, 0x4b, 0xbd, 0x1f, 0xfe, 0x37,
	0x00, 0x00, 0xff, 0xff, 0xde, 0xaf, 0x2f, 0x99, 0x56, 0x14, 0x00, 0x00,
}
