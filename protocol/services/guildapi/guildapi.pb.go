// Code generated by protoc-gen-go. DO NOT EDIT.
// source: src/guildapi/guildapi.proto

package guildapi

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type E_REQ_SOURCE_TYPE int32

const (
	E_REQ_SOURCE_TYPE_ENUM_REQ_SOURCE_INVALID E_REQ_SOURCE_TYPE = 0
	E_REQ_SOURCE_TYPE_ENUM_REQ_SOURCE_LOGIC   E_REQ_SOURCE_TYPE = 1
	E_REQ_SOURCE_TYPE_ENUM_REQ_SOURCE_GOLOGIC E_REQ_SOURCE_TYPE = 2
	E_REQ_SOURCE_TYPE_ENUM_REQ_SOURCE_GOSVR   E_REQ_SOURCE_TYPE = 3
	E_REQ_SOURCE_TYPE_ENUM_REQ_SOURCE_CPPSVR  E_REQ_SOURCE_TYPE = 4
	E_REQ_SOURCE_TYPE_ENUM_REQ_SOURCE_TOOLS   E_REQ_SOURCE_TYPE = 5
)

var E_REQ_SOURCE_TYPE_name = map[int32]string{
	0: "ENUM_REQ_SOURCE_INVALID",
	1: "ENUM_REQ_SOURCE_LOGIC",
	2: "ENUM_REQ_SOURCE_GOLOGIC",
	3: "ENUM_REQ_SOURCE_GOSVR",
	4: "ENUM_REQ_SOURCE_CPPSVR",
	5: "ENUM_REQ_SOURCE_TOOLS",
}
var E_REQ_SOURCE_TYPE_value = map[string]int32{
	"ENUM_REQ_SOURCE_INVALID": 0,
	"ENUM_REQ_SOURCE_LOGIC":   1,
	"ENUM_REQ_SOURCE_GOLOGIC": 2,
	"ENUM_REQ_SOURCE_GOSVR":   3,
	"ENUM_REQ_SOURCE_CPPSVR":  4,
	"ENUM_REQ_SOURCE_TOOLS":   5,
}

func (x E_REQ_SOURCE_TYPE) String() string {
	return proto.EnumName(E_REQ_SOURCE_TYPE_name, int32(x))
}
func (E_REQ_SOURCE_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guildapi_bd8b909cf62deec1, []int{0}
}

type ApiBaseReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AppId                uint32   `protobuf:"varint,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	SourceType           uint32   `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	SourceMsg            string   `protobuf:"bytes,5,opt,name=source_msg,json=sourceMsg,proto3" json:"source_msg,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,6,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	ClientType           uint32   `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientIp             string   `protobuf:"bytes,8,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	ClientTerminal       uint32   `protobuf:"varint,9,opt,name=client_terminal,json=clientTerminal,proto3" json:"client_terminal,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApiBaseReq) Reset()         { *m = ApiBaseReq{} }
func (m *ApiBaseReq) String() string { return proto.CompactTextString(m) }
func (*ApiBaseReq) ProtoMessage()    {}
func (*ApiBaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guildapi_bd8b909cf62deec1, []int{0}
}
func (m *ApiBaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApiBaseReq.Unmarshal(m, b)
}
func (m *ApiBaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApiBaseReq.Marshal(b, m, deterministic)
}
func (dst *ApiBaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApiBaseReq.Merge(dst, src)
}
func (m *ApiBaseReq) XXX_Size() int {
	return xxx_messageInfo_ApiBaseReq.Size(m)
}
func (m *ApiBaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApiBaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApiBaseReq proto.InternalMessageInfo

func (m *ApiBaseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApiBaseReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *ApiBaseReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ApiBaseReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *ApiBaseReq) GetSourceMsg() string {
	if m != nil {
		return m.SourceMsg
	}
	return ""
}

func (m *ApiBaseReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *ApiBaseReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ApiBaseReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *ApiBaseReq) GetClientTerminal() uint32 {
	if m != nil {
		return m.ClientTerminal
	}
	return 0
}

type ApiBaseResp struct {
	Ret                  int32    `protobuf:"varint,1,opt,name=ret,proto3" json:"ret,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApiBaseResp) Reset()         { *m = ApiBaseResp{} }
func (m *ApiBaseResp) String() string { return proto.CompactTextString(m) }
func (*ApiBaseResp) ProtoMessage()    {}
func (*ApiBaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guildapi_bd8b909cf62deec1, []int{1}
}
func (m *ApiBaseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApiBaseResp.Unmarshal(m, b)
}
func (m *ApiBaseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApiBaseResp.Marshal(b, m, deterministic)
}
func (dst *ApiBaseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApiBaseResp.Merge(dst, src)
}
func (m *ApiBaseResp) XXX_Size() int {
	return xxx_messageInfo_ApiBaseResp.Size(m)
}
func (m *ApiBaseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApiBaseResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApiBaseResp proto.InternalMessageInfo

func (m *ApiBaseResp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

func (m *ApiBaseResp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

// 公会解散
type GuildDismissReq struct {
	BaseReq              *ApiBaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GuildId              uint32      `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GuildDismissReq) Reset()         { *m = GuildDismissReq{} }
func (m *GuildDismissReq) String() string { return proto.CompactTextString(m) }
func (*GuildDismissReq) ProtoMessage()    {}
func (*GuildDismissReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guildapi_bd8b909cf62deec1, []int{2}
}
func (m *GuildDismissReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildDismissReq.Unmarshal(m, b)
}
func (m *GuildDismissReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildDismissReq.Marshal(b, m, deterministic)
}
func (dst *GuildDismissReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildDismissReq.Merge(dst, src)
}
func (m *GuildDismissReq) XXX_Size() int {
	return xxx_messageInfo_GuildDismissReq.Size(m)
}
func (m *GuildDismissReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildDismissReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildDismissReq proto.InternalMessageInfo

func (m *GuildDismissReq) GetBaseReq() *ApiBaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildDismissReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GuildDismissResp struct {
	BaseResp             *ApiBaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GuildDismissResp) Reset()         { *m = GuildDismissResp{} }
func (m *GuildDismissResp) String() string { return proto.CompactTextString(m) }
func (*GuildDismissResp) ProtoMessage()    {}
func (*GuildDismissResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guildapi_bd8b909cf62deec1, []int{3}
}
func (m *GuildDismissResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildDismissResp.Unmarshal(m, b)
}
func (m *GuildDismissResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildDismissResp.Marshal(b, m, deterministic)
}
func (dst *GuildDismissResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildDismissResp.Merge(dst, src)
}
func (m *GuildDismissResp) XXX_Size() int {
	return xxx_messageInfo_GuildDismissResp.Size(m)
}
func (m *GuildDismissResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildDismissResp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildDismissResp proto.InternalMessageInfo

func (m *GuildDismissResp) GetBaseResp() *ApiBaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 公会创建
type GuildCreateReq struct {
	BaseReq              *ApiBaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OwnerUid             uint32      `protobuf:"varint,2,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	Name                 string      `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string      `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	Prefix               string      `protobuf:"bytes,5,opt,name=prefix,proto3" json:"prefix,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GuildCreateReq) Reset()         { *m = GuildCreateReq{} }
func (m *GuildCreateReq) String() string { return proto.CompactTextString(m) }
func (*GuildCreateReq) ProtoMessage()    {}
func (*GuildCreateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guildapi_bd8b909cf62deec1, []int{4}
}
func (m *GuildCreateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildCreateReq.Unmarshal(m, b)
}
func (m *GuildCreateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildCreateReq.Marshal(b, m, deterministic)
}
func (dst *GuildCreateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildCreateReq.Merge(dst, src)
}
func (m *GuildCreateReq) XXX_Size() int {
	return xxx_messageInfo_GuildCreateReq.Size(m)
}
func (m *GuildCreateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildCreateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildCreateReq proto.InternalMessageInfo

func (m *GuildCreateReq) GetBaseReq() *ApiBaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCreateReq) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *GuildCreateReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GuildCreateReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GuildCreateReq) GetPrefix() string {
	if m != nil {
		return m.Prefix
	}
	return ""
}

type GuildCreateResp struct {
	BaseResp             *ApiBaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GuildId              uint32       `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GuildCreateResp) Reset()         { *m = GuildCreateResp{} }
func (m *GuildCreateResp) String() string { return proto.CompactTextString(m) }
func (*GuildCreateResp) ProtoMessage()    {}
func (*GuildCreateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guildapi_bd8b909cf62deec1, []int{5}
}
func (m *GuildCreateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildCreateResp.Unmarshal(m, b)
}
func (m *GuildCreateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildCreateResp.Marshal(b, m, deterministic)
}
func (dst *GuildCreateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildCreateResp.Merge(dst, src)
}
func (m *GuildCreateResp) XXX_Size() int {
	return xxx_messageInfo_GuildCreateResp.Size(m)
}
func (m *GuildCreateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildCreateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildCreateResp proto.InternalMessageInfo

func (m *GuildCreateResp) GetBaseResp() *ApiBaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCreateResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func init() {
	proto.RegisterType((*ApiBaseReq)(nil), "guildapi.ApiBaseReq")
	proto.RegisterType((*ApiBaseResp)(nil), "guildapi.ApiBaseResp")
	proto.RegisterType((*GuildDismissReq)(nil), "guildapi.GuildDismissReq")
	proto.RegisterType((*GuildDismissResp)(nil), "guildapi.GuildDismissResp")
	proto.RegisterType((*GuildCreateReq)(nil), "guildapi.GuildCreateReq")
	proto.RegisterType((*GuildCreateResp)(nil), "guildapi.GuildCreateResp")
	proto.RegisterEnum("guildapi.E_REQ_SOURCE_TYPE", E_REQ_SOURCE_TYPE_name, E_REQ_SOURCE_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GuildapiClient is the client API for Guildapi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GuildapiClient interface {
	GuildDismiss(ctx context.Context, in *GuildDismissReq, opts ...grpc.CallOption) (*GuildDismissResp, error)
	GuildCreate(ctx context.Context, in *GuildCreateReq, opts ...grpc.CallOption) (*GuildCreateResp, error)
}

type guildapiClient struct {
	cc *grpc.ClientConn
}

func NewGuildapiClient(cc *grpc.ClientConn) GuildapiClient {
	return &guildapiClient{cc}
}

func (c *guildapiClient) GuildDismiss(ctx context.Context, in *GuildDismissReq, opts ...grpc.CallOption) (*GuildDismissResp, error) {
	out := new(GuildDismissResp)
	err := c.cc.Invoke(ctx, "/guildapi.guildapi/GuildDismiss", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildapiClient) GuildCreate(ctx context.Context, in *GuildCreateReq, opts ...grpc.CallOption) (*GuildCreateResp, error) {
	out := new(GuildCreateResp)
	err := c.cc.Invoke(ctx, "/guildapi.guildapi/GuildCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GuildapiServer is the server API for Guildapi service.
type GuildapiServer interface {
	GuildDismiss(context.Context, *GuildDismissReq) (*GuildDismissResp, error)
	GuildCreate(context.Context, *GuildCreateReq) (*GuildCreateResp, error)
}

func RegisterGuildapiServer(s *grpc.Server, srv GuildapiServer) {
	s.RegisterService(&_Guildapi_serviceDesc, srv)
}

func _Guildapi_GuildDismiss_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildDismissReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildapiServer).GuildDismiss(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildapi.guildapi/GuildDismiss",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildapiServer).GuildDismiss(ctx, req.(*GuildDismissReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Guildapi_GuildCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildapiServer).GuildCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildapi.guildapi/GuildCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildapiServer).GuildCreate(ctx, req.(*GuildCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Guildapi_serviceDesc = grpc.ServiceDesc{
	ServiceName: "guildapi.guildapi",
	HandlerType: (*GuildapiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GuildDismiss",
			Handler:    _Guildapi_GuildDismiss_Handler,
		},
		{
			MethodName: "GuildCreate",
			Handler:    _Guildapi_GuildCreate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/guildapi/guildapi.proto",
}

func init() {
	proto.RegisterFile("src/guildapi/guildapi.proto", fileDescriptor_guildapi_bd8b909cf62deec1)
}

var fileDescriptor_guildapi_bd8b909cf62deec1 = []byte{
	// 554 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x54, 0x4d, 0x6f, 0xd3, 0x40,
	0x10, 0xad, 0xf3, 0x69, 0x4f, 0x68, 0x6b, 0x56, 0xa4, 0x75, 0x12, 0x21, 0x2a, 0x4b, 0x88, 0x8a,
	0x43, 0x2b, 0x85, 0x0b, 0xd7, 0x92, 0x84, 0xc8, 0x52, 0x52, 0x87, 0xcd, 0x87, 0xc4, 0x01, 0x19,
	0x27, 0x5e, 0xa2, 0x15, 0x49, 0xbc, 0xdd, 0x75, 0x80, 0xfe, 0x14, 0x2e, 0x88, 0xdf, 0xc1, 0xaf,
	0x43, 0xde, 0xdd, 0x38, 0x21, 0x4a, 0x39, 0xf4, 0x36, 0xf3, 0xde, 0xce, 0xbc, 0xdd, 0x37, 0x63,
	0x43, 0x43, 0xf0, 0xd9, 0xf5, 0x7c, 0x4d, 0x17, 0x51, 0xc8, 0x68, 0x16, 0x5c, 0x31, 0x1e, 0x27,
	0x31, 0x32, 0x37, 0xb9, 0xfb, 0x3b, 0x07, 0x70, 0xc3, 0xe8, 0xbb, 0x50, 0x10, 0x4c, 0xee, 0x90,
	0x0d, 0xf9, 0x35, 0x8d, 0x1c, 0xe3, 0xc2, 0xb8, 0x3c, 0xc6, 0x69, 0x88, 0xaa, 0x50, 0x0a, 0x19,
	0x0b, 0x68, 0xe4, 0xe4, 0x24, 0x58, 0x0c, 0x19, 0xf3, 0x22, 0xd4, 0x00, 0x6b, 0x19, 0xf2, 0xaf,
	0x24, 0x49, 0x99, 0xbc, 0x64, 0x4c, 0x05, 0x78, 0x11, 0x7a, 0x01, 0x15, 0x11, 0xaf, 0xf9, 0x8c,
	0x04, 0xc9, 0x3d, 0x23, 0x4e, 0x41, 0xd2, 0xa0, 0xa0, 0xd1, 0x3d, 0x23, 0xe8, 0x39, 0xe8, 0x2c,
	0x58, 0x8a, 0xb9, 0x53, 0xbc, 0x30, 0x2e, 0x2d, 0x6c, 0x29, 0xa4, 0x2f, 0xe6, 0xe8, 0x25, 0x9c,
	0xcc, 0x16, 0x94, 0xac, 0x92, 0xe0, 0x1b, 0xe1, 0x82, 0xc6, 0x2b, 0xa7, 0x24, 0x5b, 0x1c, 0x2b,
	0x74, 0xa2, 0xc0, 0x54, 0x46, 0x1f, 0x93, 0x32, 0x65, 0x25, 0xa3, 0x20, 0x29, 0xd3, 0x00, 0x4b,
	0x1f, 0xa0, 0xcc, 0x31, 0xa5, 0x8a, 0xa9, 0x00, 0x8f, 0xa1, 0x57, 0x70, 0xba, 0xa9, 0x26, 0x7c,
	0x49, 0x57, 0xe1, 0xc2, 0xb1, 0x64, 0x07, 0xad, 0x3d, 0xd2, 0xa8, 0xfb, 0x16, 0x2a, 0x99, 0x43,
	0x82, 0xa5, 0x16, 0x71, 0x92, 0x48, 0x8b, 0x8a, 0x38, 0x0d, 0xd1, 0x39, 0x94, 0x09, 0xe7, 0xf2,
	0x29, 0x39, 0x29, 0x52, 0x22, 0x9c, 0xf7, 0xc5, 0xdc, 0xfd, 0x04, 0xa7, 0xdd, 0xd4, 0xe8, 0x36,
	0x15, 0x4b, 0x2a, 0x44, 0x6a, 0xf0, 0x35, 0x98, 0xd3, 0x50, 0x90, 0x80, 0x93, 0x3b, 0xd9, 0xa2,
	0xd2, 0x7c, 0x76, 0x95, 0x0d, 0x67, 0x3b, 0x08, 0x5c, 0x9e, 0xea, 0x89, 0xd4, 0x40, 0x0d, 0x6b,
	0x3b, 0x81, 0xb2, 0xcc, 0xbd, 0xc8, 0x7d, 0x0f, 0xf6, 0xbf, 0xed, 0x05, 0x43, 0x4d, 0xb0, 0x74,
	0x7f, 0xc1, 0xb4, 0x40, 0xf5, 0x80, 0x80, 0x60, 0xd8, 0x9c, 0xea, 0xc8, 0xfd, 0x65, 0xc0, 0x89,
	0x6c, 0xd4, 0xe2, 0x24, 0x4c, 0xc8, 0xa3, 0xae, 0xd9, 0x00, 0x2b, 0xfe, 0xbe, 0x22, 0x3c, 0x58,
	0x67, 0xf7, 0x34, 0x25, 0x30, 0xa6, 0x11, 0x42, 0x50, 0x58, 0x85, 0x4b, 0x22, 0xf7, 0xc4, 0xc2,
	0x32, 0x4e, 0xb1, 0x88, 0x88, 0x99, 0x5c, 0x0e, 0x0b, 0xcb, 0x18, 0x9d, 0x41, 0x89, 0x71, 0xf2,
	0x85, 0xfe, 0xd0, 0x2b, 0xa1, 0x33, 0xf7, 0xb3, 0xf6, 0x71, 0x73, 0xbf, 0xc7, 0xbd, 0xf3, 0x3f,
	0x56, 0xbe, 0xfe, 0x63, 0xc0, 0xd3, 0x4e, 0x80, 0x3b, 0x1f, 0x82, 0xa1, 0x3f, 0xc6, 0xad, 0x4e,
	0x30, 0xfa, 0x38, 0xe8, 0xa0, 0x06, 0x9c, 0x77, 0x6e, 0xc7, 0xfd, 0x5d, 0xdc, 0xbb, 0x9d, 0xdc,
	0xf4, 0xbc, 0xb6, 0x7d, 0x84, 0x6a, 0x50, 0xdd, 0x27, 0x7b, 0x7e, 0xd7, 0x6b, 0xd9, 0xc6, 0xa1,
	0xba, 0xae, 0xaf, 0xc8, 0xdc, 0xa1, 0xba, 0xae, 0x3f, 0x9c, 0x60, 0x3b, 0x8f, 0xea, 0x70, 0xb6,
	0x4f, 0xb5, 0x06, 0x83, 0x94, 0x2b, 0x1c, 0x2a, 0x1b, 0xf9, 0x7e, 0x6f, 0x68, 0x17, 0x9b, 0x3f,
	0x0d, 0xc8, 0x3e, 0x68, 0xd4, 0x85, 0x27, 0xbb, 0x4b, 0x81, 0x6a, 0x5b, 0x57, 0xf6, 0x76, 0xb1,
	0x5e, 0x7f, 0x88, 0x12, 0xcc, 0x3d, 0x42, 0x6d, 0xa8, 0xec, 0x98, 0x8e, 0x9c, 0xbd, 0xc3, 0xd9,
	0xae, 0xd4, 0x6b, 0x0f, 0x30, 0x69, 0x97, 0x69, 0x49, 0xfe, 0x70, 0xde, 0xfc, 0x0d, 0x00, 0x00,
	0xff, 0xff, 0x31, 0x2f, 0xac, 0xab, 0x8f, 0x04, 0x00, 0x00,
}
