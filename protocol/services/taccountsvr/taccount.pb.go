// Code generated by protoc-gen-gogo.
// source: services/taccount/taccount.proto
// DO NOT EDIT!

/*
	Package taccount is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/taccount/taccount.proto

	It has these top-level messages:
		GetPackageNameMsg
*/
package taccount

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type GetPackageNameMsg struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	Url    string `protobuf:"bytes,2,req,name=url" json:"url"`
}

func (m *GetPackageNameMsg) Reset()                    { *m = GetPackageNameMsg{} }
func (m *GetPackageNameMsg) String() string            { return proto.CompactTextString(m) }
func (*GetPackageNameMsg) ProtoMessage()               {}
func (*GetPackageNameMsg) Descriptor() ([]byte, []int) { return fileDescriptorTaccount, []int{0} }

func (m *GetPackageNameMsg) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetPackageNameMsg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func init() {
	proto.RegisterType((*GetPackageNameMsg)(nil), "taccount.GetPackageNameMsg")
}
func (m *GetPackageNameMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPackageNameMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTaccount(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTaccount(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	return i, nil
}

func encodeFixed64Taccount(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Taccount(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintTaccount(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetPackageNameMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTaccount(uint64(m.GameId))
	l = len(m.Url)
	n += 1 + l + sovTaccount(uint64(l))
	return n
}

func sovTaccount(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozTaccount(x uint64) (n int) {
	return sovTaccount(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GetPackageNameMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTaccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPackageNameMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPackageNameMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTaccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTaccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTaccount
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTaccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTaccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipTaccount(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTaccount
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTaccount
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTaccount
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthTaccount
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowTaccount
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipTaccount(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthTaccount = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTaccount   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/taccount/taccount.proto", fileDescriptorTaccount) }

var fileDescriptorTaccount = []byte{
	// 1235 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x97, 0xcf, 0x6f, 0xdc, 0x44,
	0x14, 0xc7, 0xe3, 0xa4, 0x4d, 0xd3, 0xc9, 0x8f, 0xb6, 0x93, 0xa6, 0x4d, 0x43, 0x13, 0x86, 0x82,
	0x44, 0x0f, 0xdd, 0x06, 0xf5, 0x00, 0xc8, 0xb2, 0x2c, 0x65, 0xa5, 0x76, 0x29, 0x6d, 0x20, 0x64,
	0x1b, 0x38, 0x70, 0xa8, 0xbc, 0xf6, 0x5b, 0x67, 0x58, 0x7b, 0xc6, 0x9a, 0x19, 0x27, 0xb8, 0x17,
	0x28, 0x15, 0x3f, 0x44, 0x0f, 0x20, 0x0e, 0xdc, 0x11, 0xe1, 0xd2, 0x3f, 0x82, 0x73, 0x2f, 0x48,
	0xfc, 0x05, 0x08, 0x95, 0x4b, 0xfe, 0x09, 0x24, 0xf4, 0xc6, 0xde, 0x6c, 0x02, 0x1b, 0x14, 0xc9,
	0x7b, 0xdb, 0xf5, 0xae, 0xf4, 0xfd, 0xcc, 0xf7, 0x7d, 0xdf, 0x9b, 0x67, 0xc2, 0x34, 0xa8, 0x1d,
	0x1e, 0x82, 0x5e, 0x35, 0x41, 0x18, 0xca, 0x5c, 0x98, 0x83, 0x0f, 0x37, 0x33, 0x25, 0x8d, 0xa4,
	0x53, 0xfd, 0xef, 0x4b, 0xaf, 0x85, 0x32, 0x4d, 0xa5, 0x58, 0x35, 0xc9, 0x4e, 0xc6, 0xc3, 0x5e,
	0x02, 0xab, 0xba, 0xd7, 0xc9, 0x79, 0x62, 0xb8, 0x30, 0x45, 0x06, 0xe5, 0xff, 0xaf, 0xbd, 0x4b,
	0x2e, 0xb4, 0xc0, 0x6c, 0x04, 0x61, 0x2f, 0x88, 0xe1, 0xbd, 0x20, 0x85, 0x75, 0x1d, 0xd3, 0x65,
	0x72, 0x26, 0x0e, 0x52, 0x78, 0xc8, 0xa3, 0x45, 0x87, 0x8d, 0x5f, 0x9f, 0x6d, 0x9e, 0x7a, 0xfe,
	0xc7, 0xcb, 0x63, 0x9b, 0x93, 0xf8, 0xf0, 0x6e, 0x44, 0x2f, 0x91, 0x89, 0x5c, 0x25, 0x8b, 0xe3,
	0x6c, 0xfc, 0xfa, 0xd9, 0xea, 0x27, 0x7c, 0x70, 0xeb, 0xef, 0x65, 0x32, 0xf5, 0x60, 0xad, 0x94,
	0xa7, 0x8f, 0x1d, 0x72, 0x3a, 0x91, 0x31, 0x17, 0xf4, 0xea, 0xcd, 0x03, 0x84, 0x9b, 0xed, 0x7b,
	0xcd, 0x12, 0xe1, 0x76, 0x9a, 0x99, 0xe2, 0xe1, 0x46, 0x73, 0xe9, 0x7f, 0x7f, 0xbd, 0xe6, 0x7d,
	0xbe, 0xb7, 0x3f, 0xe1, 0x7c, 0xbb, 0xb7, 0x3f, 0x31, 0x99, 0xbb, 0xa9, 0x9b, 0xb9, 0x3f, 0xec,
	0xed, 0x4f, 0xbc, 0xde, 0xc8, 0x99, 0x97, 0x6b, 0x50, 0x22, 0x48, 0xc1, 0x67, 0x8d, 0x94, 0x79,
	0xa9, 0xec, 0xf0, 0x04, 0x3f, 0x67, 0xcc, 0xcb, 0x02, 0xad, 0x77, 0xa5, 0x8a, 0x7c, 0xaa, 0xc9,
	0x74, 0x1b, 0x44, 0xb4, 0x0e, 0x5a, 0x07, 0x31, 0xd4, 0x02, 0xb9, 0x8e, 0x20, 0xe3, 0x08, 0x72,
	0xca, 0xb8, 0xa9, 0xc5, 0x58, 0x68, 0x18, 0xe6, 0xa1, 0x95, 0x47, 0x10, 0xe8, 0x13, 0x87, 0xcc,
	0x7e, 0x08, 0x8a, 0x77, 0x8b, 0x51, 0xe8, 0xbe, 0x89, 0xba, 0x13, 0xd6, 0x00, 0xd4, 0x15, 0x56,
	0xf9, 0x95, 0xa1, 0xca, 0xac, 0x21, 0x98, 0x27, 0xf2, 0xb4, 0x03, 0xca, 0x47, 0xfb, 0x17, 0x36,
	0x21, 0xe6, 0xda, 0x80, 0x6a, 0x16, 0xeb, 0xf6, 0x1f, 0x1b, 0xdb, 0x52, 0xd4, 0xa3, 0xb9, 0x81,
	0x34, 0xa7, 0xac, 0x0b, 0xfd, 0x62, 0x5c, 0x39, 0xde, 0xfe, 0x27, 0x0e, 0x99, 0x1f, 0x30, 0xdc,
	0xc7, 0x30, 0x60, 0xc2, 0x6a, 0x11, 0xbc, 0x81, 0x04, 0xa7, 0x2d, 0x81, 0xa8, 0x08, 0x96, 0xf1,
	0xdc, 0x36, 0x6a, 0xac, 0x0a, 0xc4, 0x11, 0x8a, 0x8f, 0xc9, 0x4c, 0x1f, 0x62, 0x2d, 0x37, 0xb2,
	0x96, 0xfa, 0x39, 0x54, 0x9f, 0x44, 0xf5, 0x31, 0x54, 0x1e, 0xa3, 0x05, 0x99, 0xdd, 0x04, 0x8d,
	0x0d, 0x54, 0xca, 0xd5, 0x77, 0xf7, 0xcc, 0x49, 0xdd, 0xfd, 0xce, 0x21, 0x73, 0xeb, 0x32, 0xe2,
	0xdd, 0x62, 0x24, 0xe2, 0x6b, 0x28, 0x3e, 0x55, 0x75, 0x9a, 0xac, 0x82, 0x76, 0xc3, 0x76, 0x1a,
	0x8f, 0x7c, 0xd6, 0x90, 0xcc, 0x93, 0x49, 0xc4, 0x0e, 0xf4, 0xcb, 0xb4, 0xc1, 0xee, 0xe0, 0x09,
	0xfd, 0xd2, 0x21, 0x73, 0x6d, 0x08, 0x54, 0xb8, 0xdd, 0x0a, 0x52, 0xb8, 0xcf, 0xb5, 0xa9, 0x45,
	0xf4, 0x16, 0x12, 0x9d, 0xb5, 0x44, 0x3d, 0x57, 0xba, 0xda, 0x12, 0x5d, 0x6b, 0xf4, 0x98, 0xd7,
	0x83, 0xa2, 0x62, 0x40, 0xaa, 0x6e, 0x57, 0x83, 0xf1, 0x59, 0x43, 0x33, 0x4f, 0xf3, 0x47, 0xe0,
	0xd3, 0xaf, 0x1c, 0x72, 0x79, 0x2b, 0x8b, 0x02, 0x03, 0xc8, 0xb1, 0x11, 0x28, 0x23, 0x40, 0xb5,
	0x4d, 0x60, 0x72, 0x5d, 0x0b, 0x68, 0x15, 0x81, 0x88, 0xad, 0x4f, 0x5c, 0xd5, 0xe7, 0x6a, 0x23,
	0x66, 0x1e, 0x8e, 0x45, 0x66, 0x4d, 0xca, 0x98, 0xc7, 0x35, 0xcb, 0x4a, 0x45, 0x9f, 0x3e, 0x73,
	0xc8, 0xf4, 0x83, 0x22, 0x83, 0x16, 0xef, 0x9a, 0x8d, 0x5e, 0x5c, 0x4b, 0xfc, 0x21, 0x8a, 0x4f,
	0xa3, 0x38, 0x89, 0x5d, 0xe1, 0x72, 0x37, 0x74, 0x73, 0x8b, 0x70, 0xe7, 0x28, 0x82, 0x60, 0x5e,
	0xcc, 0xbb, 0x86, 0x65, 0xe5, 0x48, 0xef, 0xb7, 0x04, 0x67, 0x1e, 0x17, 0x46, 0x49, 0x9f, 0x35,
	0x42, 0xe6, 0x85, 0x52, 0x18, 0x10, 0x68, 0x9a, 0x9d, 0xa4, 0x41, 0x0c, 0x3e, 0xdd, 0x21, 0x73,
	0x2d, 0x30, 0x15, 0x6a, 0xed, 0xe2, 0xd9, 0x79, 0x39, 0x63, 0xbd, 0xea, 0x97, 0x6e, 0x61, 0x78,
	0xb5, 0x7e, 0x72, 0xc8, 0x79, 0x4c, 0x70, 0xa5, 0xbc, 0x96, 0x65, 0x49, 0x51, 0x4b, 0xfa, 0x03,
	0x94, 0x9e, 0x45, 0xe9, 0xa9, 0xd8, 0x0d, 0x5c, 0x59, 0xf9, 0xe4, 0x5a, 0x9f, 0x72, 0x9e, 0x44,
	0xa5, 0x51, 0x01, 0xf3, 0x02, 0xd4, 0x62, 0x7c, 0x10, 0x24, 0x50, 0xac, 0x1a, 0xa0, 0xd6, 0x19,
	0x99, 0x31, 0xbc, 0x66, 0x7c, 0xfa, 0xb3, 0x43, 0xe8, 0x26, 0x7c, 0x02, 0xa1, 0x19, 0x2d, 0xe5,
	0xdc, 0x01, 0xa5, 0x3a, 0x11, 0xa5, 0x62, 0x9e, 0xb2, 0x18, 0x4c, 0x41, 0xa0, 0xa5, 0x18, 0x60,
	0xf2, 0xc8, 0xa7, 0x3f, 0x3a, 0xe4, 0xc2, 0xa1, 0xb8, 0xb5, 0x41, 0xf1, 0x20, 0xa9, 0x05, 0x79,
	0x1b, 0x21, 0xcf, 0x55, 0x89, 0x2f, 0xab, 0x78, 0xcb, 0x02, 0x1e, 0x4e, 0x98, 0x25, 0xc3, 0x72,
	0x5a, 0xbd, 0x7e, 0xa6, 0x40, 0xb1, 0x6a, 0x13, 0xf1, 0xe9, 0x6f, 0x0e, 0x99, 0xbe, 0x1d, 0x71,
	0x33, 0x8a, 0x3e, 0x78, 0xec, 0x20, 0xd3, 0x79, 0x64, 0x9a, 0xcb, 0xdc, 0x41, 0x2b, 0x94, 0x74,
	0x11, 0xb6, 0x60, 0x49, 0xd7, 0x8b, 0x4b, 0xb2, 0x91, 0x74, 0x87, 0x3d, 0x20, 0xd7, 0x4c, 0x6f,
	0xcb, 0x5d, 0x9f, 0x3e, 0x75, 0xc8, 0xfc, 0xa0, 0x57, 0x6c, 0x16, 0x6a, 0x37, 0xcc, 0xdb, 0x78,
	0xac, 0x0b, 0x76, 0xda, 0x61, 0xc3, 0xc4, 0xf6, 0x38, 0xaf, 0x0e, 0x6d, 0x19, 0x76, 0x24, 0x23,
	0xf4, 0xeb, 0xc1, 0xb8, 0xab, 0x0a, 0xbf, 0x2d, 0x77, 0x47, 0x30, 0xee, 0xec, 0x55, 0x4b, 0x6d,
	0xf1, 0xb3, 0xca, 0xde, 0xe5, 0xff, 0xda, 0x7b, 0xd8, 0x97, 0x5f, 0x1c, 0x42, 0x5b, 0x7c, 0xa7,
	0xcf, 0xf1, 0x40, 0xb6, 0x10, 0xb2, 0x16, 0x44, 0x1b, 0x21, 0xe6, 0x6d, 0x9b, 0x94, 0xd5, 0x2e,
	0xdb, 0xc4, 0x1b, 0x5e, 0xe7, 0x41, 0xdf, 0x88, 0xe3, 0xfa, 0x19, 0x1d, 0x7b, 0x44, 0xa8, 0x5d,
	0x7a, 0xed, 0x98, 0x1e, 0xc9, 0x5d, 0x65, 0xc7, 0xdd, 0xc5, 0x93, 0x8c, 0xbb, 0xa7, 0x0e, 0xb9,
	0x78, 0x74, 0xce, 0x36, 0x8b, 0x56, 0xdd, 0xad, 0xc8, 0x5e, 0x95, 0x0b, 0x36, 0x3c, 0xf1, 0xa1,
	0xab, 0xf2, 0x48, 0xf4, 0x87, 0xd2, 0xfc, 0xea, 0x90, 0xc5, 0x16, 0x18, 0x1b, 0xe1, 0x0a, 0xc9,
	0xd6, 0xac, 0xb6, 0x21, 0x5d, 0x24, 0xba, 0x84, 0x44, 0x33, 0x25, 0x91, 0x72, 0x23, 0xd7, 0x58,
	0xae, 0x7b, 0xff, 0x2a, 0xd5, 0xf0, 0x88, 0x2b, 0xe6, 0x49, 0x15, 0x81, 0x62, 0x9d, 0xc2, 0x67,
	0x8d, 0x88, 0x79, 0x8c, 0x6b, 0x16, 0x81, 0x0e, 0x7d, 0x86, 0xdb, 0xaf, 0xb6, 0xf9, 0xf6, 0xe9,
	0xa7, 0x64, 0xa9, 0x05, 0x66, 0x4b, 0x83, 0xb2, 0xdc, 0x77, 0x45, 0x57, 0x36, 0x8b, 0xad, 0xea,
	0xdd, 0xa0, 0xd6, 0x09, 0x56, 0xf0, 0x04, 0x97, 0xf1, 0x04, 0xe3, 0xe5, 0x32, 0x34, 0x8b, 0xa1,
	0x3a, 0x78, 0xed, 0xa0, 0x9a, 0x2c, 0xb4, 0xc0, 0xb4, 0xd3, 0x20, 0x49, 0xee, 0x04, 0x21, 0x34,
	0x8b, 0xfe, 0x9b, 0x4f, 0x6d, 0xd1, 0xc5, 0xe3, 0x45, 0x9f, 0x0d, 0x26, 0x4f, 0xd6, 0x8b, 0xdf,
	0xcf, 0xde, 0xe1, 0xda, 0x48, 0x55, 0xef, 0x26, 0xfa, 0x08, 0x35, 0xaf, 0xd8, 0xcd, 0xa2, 0xe3,
	0x82, 0x2d, 0x56, 0x60, 0xb5, 0xfd, 0x46, 0x87, 0x79, 0x1d, 0x88, 0xb9, 0x30, 0xdc, 0x8e, 0x49,
	0x60, 0x1e, 0x88, 0xa8, 0xfa, 0x82, 0x37, 0x53, 0x35, 0xe0, 0x8f, 0x09, 0xd7, 0x67, 0x64, 0x3e,
	0x94, 0x49, 0x02, 0xa1, 0x59, 0xe7, 0x5a, 0x73, 0x29, 0x9a, 0x52, 0xd4, 0x9c, 0x49, 0x76, 0x45,
	0x5e, 0xb2, 0x7d, 0x96, 0xbb, 0xbd, 0x6a, 0x45, 0x3e, 0xd8, 0x51, 0x7b, 0xcc, 0x4b, 0x4b, 0x25,
	0xd6, 0x83, 0xc2, 0xa7, 0x40, 0x66, 0xaa, 0x70, 0x34, 0x8b, 0x2d, 0x5e, 0x6f, 0x10, 0x5d, 0x41,
	0xe5, 0x97, 0x6c, 0x65, 0xca, 0x11, 0x34, 0xd5, 0xd7, 0x5d, 0x9a, 0xfc, 0x66, 0x6f, 0x7f, 0xe2,
	0x8b, 0x56, 0xf3, 0xfc, 0xf3, 0x17, 0x2b, 0xce, 0xef, 0x2f, 0x56, 0x9c, 0x3f, 0x5f, 0xac, 0x38,
	0xdf, 0xff, 0xb5, 0x32, 0xf6, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x9a, 0xc3, 0x09, 0x2d, 0xb0,
	0x0f, 0x00, 0x00,
}
