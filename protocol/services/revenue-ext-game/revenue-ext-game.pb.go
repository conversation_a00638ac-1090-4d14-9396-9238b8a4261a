// Code generated by protoc-gen-go. DO NOT EDIT.
// source: revenue-ext-game/revenue-ext-game.proto

package revenue_ext_game // import "golang.52tt.com/protocol/services/revenue-ext-game"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 外部游戏类型
type ExtGameType int32

const (
	ExtGameType_EXT_GAME_TYPE_UNSPECIFIED      ExtGameType = 0
	ExtGameType_EXT_GAME_TYPE_CUTE_PETS_WAR    ExtGameType = 1
	ExtGameType_EXT_GAME_TYPE_SHEEP_WOLVES_WAR ExtGameType = 2
)

var ExtGameType_name = map[int32]string{
	0: "EXT_GAME_TYPE_UNSPECIFIED",
	1: "EXT_GAME_TYPE_CUTE_PETS_WAR",
	2: "EXT_GAME_TYPE_SHEEP_WOLVES_WAR",
}
var ExtGameType_value = map[string]int32{
	"EXT_GAME_TYPE_UNSPECIFIED":      0,
	"EXT_GAME_TYPE_CUTE_PETS_WAR":    1,
	"EXT_GAME_TYPE_SHEEP_WOLVES_WAR": 2,
}

func (x ExtGameType) String() string {
	return proto.EnumName(ExtGameType_name, int32(x))
}
func (ExtGameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{0}
}

type GameRankType int32

const (
	GameRankType_GAME_RANK_TYPE_UNSPECIFIED            GameRankType = 0
	GameRankType_GAME_RANK_TYPE_WORLD_RANK_LAST_N_DAYS GameRankType = 1
)

var GameRankType_name = map[int32]string{
	0: "GAME_RANK_TYPE_UNSPECIFIED",
	1: "GAME_RANK_TYPE_WORLD_RANK_LAST_N_DAYS",
}
var GameRankType_value = map[string]int32{
	"GAME_RANK_TYPE_UNSPECIFIED":            0,
	"GAME_RANK_TYPE_WORLD_RANK_LAST_N_DAYS": 1,
}

func (x GameRankType) String() string {
	return proto.EnumName(GameRankType_name, int32(x))
}
func (GameRankType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{1}
}

type GetDataReportTaskStatusResp_Status int32

const (
	GetDataReportTaskStatusResp_STATUS_UNSPECIFIED GetDataReportTaskStatusResp_Status = 0
	GetDataReportTaskStatusResp_STATUS_RUNNING     GetDataReportTaskStatusResp_Status = 1
	GetDataReportTaskStatusResp_STATUS_PENDING     GetDataReportTaskStatusResp_Status = 2
	GetDataReportTaskStatusResp_STATUS_NOT_EXIST   GetDataReportTaskStatusResp_Status = 3
)

var GetDataReportTaskStatusResp_Status_name = map[int32]string{
	0: "STATUS_UNSPECIFIED",
	1: "STATUS_RUNNING",
	2: "STATUS_PENDING",
	3: "STATUS_NOT_EXIST",
}
var GetDataReportTaskStatusResp_Status_value = map[string]int32{
	"STATUS_UNSPECIFIED": 0,
	"STATUS_RUNNING":     1,
	"STATUS_PENDING":     2,
	"STATUS_NOT_EXIST":   3,
}

func (x GetDataReportTaskStatusResp_Status) String() string {
	return proto.EnumName(GetDataReportTaskStatusResp_Status_name, int32(x))
}
func (GetDataReportTaskStatusResp_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{29, 0}
}

type GetChannelExtGameAccessStatusResp_StreamType int32

const (
	GetChannelExtGameAccessStatusResp_STREAM_TYPE_UNSPECIFIED GetChannelExtGameAccessStatusResp_StreamType = 0
	GetChannelExtGameAccessStatusResp_STREAM_TYPE_RTC         GetChannelExtGameAccessStatusResp_StreamType = 1
	GetChannelExtGameAccessStatusResp_STREAM_TYPE_L3          GetChannelExtGameAccessStatusResp_StreamType = 2
	GetChannelExtGameAccessStatusResp_STREAM_TYPE_CDN         GetChannelExtGameAccessStatusResp_StreamType = 3
)

var GetChannelExtGameAccessStatusResp_StreamType_name = map[int32]string{
	0: "STREAM_TYPE_UNSPECIFIED",
	1: "STREAM_TYPE_RTC",
	2: "STREAM_TYPE_L3",
	3: "STREAM_TYPE_CDN",
}
var GetChannelExtGameAccessStatusResp_StreamType_value = map[string]int32{
	"STREAM_TYPE_UNSPECIFIED": 0,
	"STREAM_TYPE_RTC":         1,
	"STREAM_TYPE_L3":          2,
	"STREAM_TYPE_CDN":         3,
}

func (x GetChannelExtGameAccessStatusResp_StreamType) String() string {
	return proto.EnumName(GetChannelExtGameAccessStatusResp_StreamType_name, int32(x))
}
func (GetChannelExtGameAccessStatusResp_StreamType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{36, 0}
}

type GetExtGameRankNameplateReq_ShowType int32

const (
	GetExtGameRankNameplateReq_SHOW_TYPE_UNSPECIFIED       GetExtGameRankNameplateReq_ShowType = 0
	GetExtGameRankNameplateReq_SHOW_TYPE_CHANNEL_USER_CARD GetExtGameRankNameplateReq_ShowType = 1
	GetExtGameRankNameplateReq_SHOW_TYPE_PERSONAL_PAGE     GetExtGameRankNameplateReq_ShowType = 2
)

var GetExtGameRankNameplateReq_ShowType_name = map[int32]string{
	0: "SHOW_TYPE_UNSPECIFIED",
	1: "SHOW_TYPE_CHANNEL_USER_CARD",
	2: "SHOW_TYPE_PERSONAL_PAGE",
}
var GetExtGameRankNameplateReq_ShowType_value = map[string]int32{
	"SHOW_TYPE_UNSPECIFIED":       0,
	"SHOW_TYPE_CHANNEL_USER_CARD": 1,
	"SHOW_TYPE_PERSONAL_PAGE":     2,
}

func (x GetExtGameRankNameplateReq_ShowType) String() string {
	return proto.EnumName(GetExtGameRankNameplateReq_ShowType_name, int32(x))
}
func (GetExtGameRankNameplateReq_ShowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{47, 0}
}

type CampButtonCfg struct {
	ButtonColor          string   `protobuf:"bytes,1,opt,name=button_color,json=buttonColor,proto3" json:"button_color,omitempty"`
	ButtonText           string   `protobuf:"bytes,2,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	JoinText             string   `protobuf:"bytes,3,opt,name=join_text,json=joinText,proto3" json:"join_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CampButtonCfg) Reset()         { *m = CampButtonCfg{} }
func (m *CampButtonCfg) String() string { return proto.CompactTextString(m) }
func (*CampButtonCfg) ProtoMessage()    {}
func (*CampButtonCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{0}
}
func (m *CampButtonCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CampButtonCfg.Unmarshal(m, b)
}
func (m *CampButtonCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CampButtonCfg.Marshal(b, m, deterministic)
}
func (dst *CampButtonCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CampButtonCfg.Merge(dst, src)
}
func (m *CampButtonCfg) XXX_Size() int {
	return xxx_messageInfo_CampButtonCfg.Size(m)
}
func (m *CampButtonCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_CampButtonCfg.DiscardUnknown(m)
}

var xxx_messageInfo_CampButtonCfg proto.InternalMessageInfo

func (m *CampButtonCfg) GetButtonColor() string {
	if m != nil {
		return m.ButtonColor
	}
	return ""
}

func (m *CampButtonCfg) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

func (m *CampButtonCfg) GetJoinText() string {
	if m != nil {
		return m.JoinText
	}
	return ""
}

// 操作区域的配置
type ExtGameOpCfg struct {
	CampButtonList       []*CampButtonCfg `protobuf:"bytes,1,rep,name=camp_button_list,json=campButtonList,proto3" json:"camp_button_list,omitempty"`
	QuickGiftEnable      bool             `protobuf:"varint,2,opt,name=quick_gift_enable,json=quickGiftEnable,proto3" json:"quick_gift_enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ExtGameOpCfg) Reset()         { *m = ExtGameOpCfg{} }
func (m *ExtGameOpCfg) String() string { return proto.CompactTextString(m) }
func (*ExtGameOpCfg) ProtoMessage()    {}
func (*ExtGameOpCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{1}
}
func (m *ExtGameOpCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtGameOpCfg.Unmarshal(m, b)
}
func (m *ExtGameOpCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtGameOpCfg.Marshal(b, m, deterministic)
}
func (dst *ExtGameOpCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtGameOpCfg.Merge(dst, src)
}
func (m *ExtGameOpCfg) XXX_Size() int {
	return xxx_messageInfo_ExtGameOpCfg.Size(m)
}
func (m *ExtGameOpCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtGameOpCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ExtGameOpCfg proto.InternalMessageInfo

func (m *ExtGameOpCfg) GetCampButtonList() []*CampButtonCfg {
	if m != nil {
		return m.CampButtonList
	}
	return nil
}

func (m *ExtGameOpCfg) GetQuickGiftEnable() bool {
	if m != nil {
		return m.QuickGiftEnable
	}
	return false
}

// 游戏专属礼物
type ExtGameGiftCfg struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExtGameGiftCfg) Reset()         { *m = ExtGameGiftCfg{} }
func (m *ExtGameGiftCfg) String() string { return proto.CompactTextString(m) }
func (*ExtGameGiftCfg) ProtoMessage()    {}
func (*ExtGameGiftCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{2}
}
func (m *ExtGameGiftCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtGameGiftCfg.Unmarshal(m, b)
}
func (m *ExtGameGiftCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtGameGiftCfg.Marshal(b, m, deterministic)
}
func (dst *ExtGameGiftCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtGameGiftCfg.Merge(dst, src)
}
func (m *ExtGameGiftCfg) XXX_Size() int {
	return xxx_messageInfo_ExtGameGiftCfg.Size(m)
}
func (m *ExtGameGiftCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtGameGiftCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ExtGameGiftCfg proto.InternalMessageInfo

func (m *ExtGameGiftCfg) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *ExtGameGiftCfg) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type ExtGameCfg struct {
	GameType              ExtGameType       `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	Name                  string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                  string            `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	PicUrl                string            `protobuf:"bytes,4,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	CmsUrl                string            `protobuf:"bytes,5,opt,name=cms_url,json=cmsUrl,proto3" json:"cms_url,omitempty"`
	GiftList              []*ExtGameGiftCfg `protobuf:"bytes,6,rep,name=gift_list,json=giftList,proto3" json:"gift_list,omitempty"`
	ResourceDisplayEnable bool              `protobuf:"varint,7,opt,name=resource_display_enable,json=resourceDisplayEnable,proto3" json:"resource_display_enable,omitempty"`
	RankType              uint32            `protobuf:"varint,8,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}          `json:"-"`
	XXX_unrecognized      []byte            `json:"-"`
	XXX_sizecache         int32             `json:"-"`
}

func (m *ExtGameCfg) Reset()         { *m = ExtGameCfg{} }
func (m *ExtGameCfg) String() string { return proto.CompactTextString(m) }
func (*ExtGameCfg) ProtoMessage()    {}
func (*ExtGameCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{3}
}
func (m *ExtGameCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtGameCfg.Unmarshal(m, b)
}
func (m *ExtGameCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtGameCfg.Marshal(b, m, deterministic)
}
func (dst *ExtGameCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtGameCfg.Merge(dst, src)
}
func (m *ExtGameCfg) XXX_Size() int {
	return xxx_messageInfo_ExtGameCfg.Size(m)
}
func (m *ExtGameCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtGameCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ExtGameCfg proto.InternalMessageInfo

func (m *ExtGameCfg) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

func (m *ExtGameCfg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ExtGameCfg) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ExtGameCfg) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *ExtGameCfg) GetCmsUrl() string {
	if m != nil {
		return m.CmsUrl
	}
	return ""
}

func (m *ExtGameCfg) GetGiftList() []*ExtGameGiftCfg {
	if m != nil {
		return m.GiftList
	}
	return nil
}

func (m *ExtGameCfg) GetResourceDisplayEnable() bool {
	if m != nil {
		return m.ResourceDisplayEnable
	}
	return false
}

func (m *ExtGameCfg) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

type SetExtGameOpCfgReq struct {
	GameType             ExtGameType   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              string        `protobuf:"bytes,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	OpCfg                *ExtGameOpCfg `protobuf:"bytes,4,opt,name=op_cfg,json=opCfg,proto3" json:"op_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetExtGameOpCfgReq) Reset()         { *m = SetExtGameOpCfgReq{} }
func (m *SetExtGameOpCfgReq) String() string { return proto.CompactTextString(m) }
func (*SetExtGameOpCfgReq) ProtoMessage()    {}
func (*SetExtGameOpCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{4}
}
func (m *SetExtGameOpCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetExtGameOpCfgReq.Unmarshal(m, b)
}
func (m *SetExtGameOpCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetExtGameOpCfgReq.Marshal(b, m, deterministic)
}
func (dst *SetExtGameOpCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetExtGameOpCfgReq.Merge(dst, src)
}
func (m *SetExtGameOpCfgReq) XXX_Size() int {
	return xxx_messageInfo_SetExtGameOpCfgReq.Size(m)
}
func (m *SetExtGameOpCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetExtGameOpCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetExtGameOpCfgReq proto.InternalMessageInfo

func (m *SetExtGameOpCfgReq) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

func (m *SetExtGameOpCfgReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetExtGameOpCfgReq) GetRoundId() string {
	if m != nil {
		return m.RoundId
	}
	return ""
}

func (m *SetExtGameOpCfgReq) GetOpCfg() *ExtGameOpCfg {
	if m != nil {
		return m.OpCfg
	}
	return nil
}

type SetExtGameOpCfgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetExtGameOpCfgResp) Reset()         { *m = SetExtGameOpCfgResp{} }
func (m *SetExtGameOpCfgResp) String() string { return proto.CompactTextString(m) }
func (*SetExtGameOpCfgResp) ProtoMessage()    {}
func (*SetExtGameOpCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{5}
}
func (m *SetExtGameOpCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetExtGameOpCfgResp.Unmarshal(m, b)
}
func (m *SetExtGameOpCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetExtGameOpCfgResp.Marshal(b, m, deterministic)
}
func (dst *SetExtGameOpCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetExtGameOpCfgResp.Merge(dst, src)
}
func (m *SetExtGameOpCfgResp) XXX_Size() int {
	return xxx_messageInfo_SetExtGameOpCfgResp.Size(m)
}
func (m *SetExtGameOpCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetExtGameOpCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetExtGameOpCfgResp proto.InternalMessageInfo

type ReportGameEndReq struct {
	GameType             ExtGameType                  `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	ChannelId            uint32                       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              string                       `protobuf:"bytes,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	InfoList             []*ReportGameEndReq_RankInfo `protobuf:"bytes,4,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ReportGameEndReq) Reset()         { *m = ReportGameEndReq{} }
func (m *ReportGameEndReq) String() string { return proto.CompactTextString(m) }
func (*ReportGameEndReq) ProtoMessage()    {}
func (*ReportGameEndReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{6}
}
func (m *ReportGameEndReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportGameEndReq.Unmarshal(m, b)
}
func (m *ReportGameEndReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportGameEndReq.Marshal(b, m, deterministic)
}
func (dst *ReportGameEndReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportGameEndReq.Merge(dst, src)
}
func (m *ReportGameEndReq) XXX_Size() int {
	return xxx_messageInfo_ReportGameEndReq.Size(m)
}
func (m *ReportGameEndReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportGameEndReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportGameEndReq proto.InternalMessageInfo

func (m *ReportGameEndReq) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

func (m *ReportGameEndReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportGameEndReq) GetRoundId() string {
	if m != nil {
		return m.RoundId
	}
	return ""
}

func (m *ReportGameEndReq) GetInfoList() []*ReportGameEndReq_RankInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type ReportGameEndReq_RankInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FakeUid              uint32   `protobuf:"varint,2,opt,name=fake_uid,json=fakeUid,proto3" json:"fake_uid,omitempty"`
	Score                uint64   `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportGameEndReq_RankInfo) Reset()         { *m = ReportGameEndReq_RankInfo{} }
func (m *ReportGameEndReq_RankInfo) String() string { return proto.CompactTextString(m) }
func (*ReportGameEndReq_RankInfo) ProtoMessage()    {}
func (*ReportGameEndReq_RankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{6, 0}
}
func (m *ReportGameEndReq_RankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportGameEndReq_RankInfo.Unmarshal(m, b)
}
func (m *ReportGameEndReq_RankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportGameEndReq_RankInfo.Marshal(b, m, deterministic)
}
func (dst *ReportGameEndReq_RankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportGameEndReq_RankInfo.Merge(dst, src)
}
func (m *ReportGameEndReq_RankInfo) XXX_Size() int {
	return xxx_messageInfo_ReportGameEndReq_RankInfo.Size(m)
}
func (m *ReportGameEndReq_RankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportGameEndReq_RankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReportGameEndReq_RankInfo proto.InternalMessageInfo

func (m *ReportGameEndReq_RankInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportGameEndReq_RankInfo) GetFakeUid() uint32 {
	if m != nil {
		return m.FakeUid
	}
	return 0
}

func (m *ReportGameEndReq_RankInfo) GetScore() uint64 {
	if m != nil {
		return m.Score
	}
	return 0
}

type ReportGameEndResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportGameEndResp) Reset()         { *m = ReportGameEndResp{} }
func (m *ReportGameEndResp) String() string { return proto.CompactTextString(m) }
func (*ReportGameEndResp) ProtoMessage()    {}
func (*ReportGameEndResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{7}
}
func (m *ReportGameEndResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportGameEndResp.Unmarshal(m, b)
}
func (m *ReportGameEndResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportGameEndResp.Marshal(b, m, deterministic)
}
func (dst *ReportGameEndResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportGameEndResp.Merge(dst, src)
}
func (m *ReportGameEndResp) XXX_Size() int {
	return xxx_messageInfo_ReportGameEndResp.Size(m)
}
func (m *ReportGameEndResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportGameEndResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportGameEndResp proto.InternalMessageInfo

type SetUserCampReq struct {
	GameType             ExtGameType `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	ChannelId            uint32      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              string      `protobuf:"bytes,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	Uid                  uint32      `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	RecodeUid            uint32      `protobuf:"varint,5,opt,name=recode_uid,json=recodeUid,proto3" json:"recode_uid,omitempty"`
	Camp                 string      `protobuf:"bytes,6,opt,name=camp,proto3" json:"camp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetUserCampReq) Reset()         { *m = SetUserCampReq{} }
func (m *SetUserCampReq) String() string { return proto.CompactTextString(m) }
func (*SetUserCampReq) ProtoMessage()    {}
func (*SetUserCampReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{8}
}
func (m *SetUserCampReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserCampReq.Unmarshal(m, b)
}
func (m *SetUserCampReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserCampReq.Marshal(b, m, deterministic)
}
func (dst *SetUserCampReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserCampReq.Merge(dst, src)
}
func (m *SetUserCampReq) XXX_Size() int {
	return xxx_messageInfo_SetUserCampReq.Size(m)
}
func (m *SetUserCampReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserCampReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserCampReq proto.InternalMessageInfo

func (m *SetUserCampReq) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

func (m *SetUserCampReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetUserCampReq) GetRoundId() string {
	if m != nil {
		return m.RoundId
	}
	return ""
}

func (m *SetUserCampReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserCampReq) GetRecodeUid() uint32 {
	if m != nil {
		return m.RecodeUid
	}
	return 0
}

func (m *SetUserCampReq) GetCamp() string {
	if m != nil {
		return m.Camp
	}
	return ""
}

type SetUserCampResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserCampResp) Reset()         { *m = SetUserCampResp{} }
func (m *SetUserCampResp) String() string { return proto.CompactTextString(m) }
func (*SetUserCampResp) ProtoMessage()    {}
func (*SetUserCampResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{9}
}
func (m *SetUserCampResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserCampResp.Unmarshal(m, b)
}
func (m *SetUserCampResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserCampResp.Marshal(b, m, deterministic)
}
func (dst *SetUserCampResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserCampResp.Merge(dst, src)
}
func (m *SetUserCampResp) XXX_Size() int {
	return xxx_messageInfo_SetUserCampResp.Size(m)
}
func (m *SetUserCampResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserCampResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserCampResp proto.InternalMessageInfo

// 获取用户的游戏信息
type GetUserExtGameInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserExtGameInfoReq) Reset()         { *m = GetUserExtGameInfoReq{} }
func (m *GetUserExtGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserExtGameInfoReq) ProtoMessage()    {}
func (*GetUserExtGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{10}
}
func (m *GetUserExtGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExtGameInfoReq.Unmarshal(m, b)
}
func (m *GetUserExtGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExtGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserExtGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExtGameInfoReq.Merge(dst, src)
}
func (m *GetUserExtGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserExtGameInfoReq.Size(m)
}
func (m *GetUserExtGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExtGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExtGameInfoReq proto.InternalMessageInfo

func (m *GetUserExtGameInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserExtGameInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUserExtGameInfoResp struct {
	RoundId              string        `protobuf:"bytes,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	GameConf             *ExtGameCfg   `protobuf:"bytes,2,opt,name=game_conf,json=gameConf,proto3" json:"game_conf,omitempty"`
	OpConf               *ExtGameOpCfg `protobuf:"bytes,3,opt,name=op_conf,json=opConf,proto3" json:"op_conf,omitempty"`
	UserCamp             string        `protobuf:"bytes,4,opt,name=user_camp,json=userCamp,proto3" json:"user_camp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserExtGameInfoResp) Reset()         { *m = GetUserExtGameInfoResp{} }
func (m *GetUserExtGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserExtGameInfoResp) ProtoMessage()    {}
func (*GetUserExtGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{11}
}
func (m *GetUserExtGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExtGameInfoResp.Unmarshal(m, b)
}
func (m *GetUserExtGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExtGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserExtGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExtGameInfoResp.Merge(dst, src)
}
func (m *GetUserExtGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserExtGameInfoResp.Size(m)
}
func (m *GetUserExtGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExtGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExtGameInfoResp proto.InternalMessageInfo

func (m *GetUserExtGameInfoResp) GetRoundId() string {
	if m != nil {
		return m.RoundId
	}
	return ""
}

func (m *GetUserExtGameInfoResp) GetGameConf() *ExtGameCfg {
	if m != nil {
		return m.GameConf
	}
	return nil
}

func (m *GetUserExtGameInfoResp) GetOpConf() *ExtGameOpCfg {
	if m != nil {
		return m.OpConf
	}
	return nil
}

func (m *GetUserExtGameInfoResp) GetUserCamp() string {
	if m != nil {
		return m.UserCamp
	}
	return ""
}

// 获取游戏配置列表
type GetExtGameCfgListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtGameCfgListReq) Reset()         { *m = GetExtGameCfgListReq{} }
func (m *GetExtGameCfgListReq) String() string { return proto.CompactTextString(m) }
func (*GetExtGameCfgListReq) ProtoMessage()    {}
func (*GetExtGameCfgListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{12}
}
func (m *GetExtGameCfgListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtGameCfgListReq.Unmarshal(m, b)
}
func (m *GetExtGameCfgListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtGameCfgListReq.Marshal(b, m, deterministic)
}
func (dst *GetExtGameCfgListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtGameCfgListReq.Merge(dst, src)
}
func (m *GetExtGameCfgListReq) XXX_Size() int {
	return xxx_messageInfo_GetExtGameCfgListReq.Size(m)
}
func (m *GetExtGameCfgListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtGameCfgListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtGameCfgListReq proto.InternalMessageInfo

func (m *GetExtGameCfgListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetExtGameCfgListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetExtGameCfgListResp struct {
	ConfList             []*ExtGameCfg `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetExtGameCfgListResp) Reset()         { *m = GetExtGameCfgListResp{} }
func (m *GetExtGameCfgListResp) String() string { return proto.CompactTextString(m) }
func (*GetExtGameCfgListResp) ProtoMessage()    {}
func (*GetExtGameCfgListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{13}
}
func (m *GetExtGameCfgListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtGameCfgListResp.Unmarshal(m, b)
}
func (m *GetExtGameCfgListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtGameCfgListResp.Marshal(b, m, deterministic)
}
func (dst *GetExtGameCfgListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtGameCfgListResp.Merge(dst, src)
}
func (m *GetExtGameCfgListResp) XXX_Size() int {
	return xxx_messageInfo_GetExtGameCfgListResp.Size(m)
}
func (m *GetExtGameCfgListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtGameCfgListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtGameCfgListResp proto.InternalMessageInfo

func (m *GetExtGameCfgListResp) GetConfList() []*ExtGameCfg {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type MountExtGameReq struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameType             ExtGameType `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *MountExtGameReq) Reset()         { *m = MountExtGameReq{} }
func (m *MountExtGameReq) String() string { return proto.CompactTextString(m) }
func (*MountExtGameReq) ProtoMessage()    {}
func (*MountExtGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{14}
}
func (m *MountExtGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MountExtGameReq.Unmarshal(m, b)
}
func (m *MountExtGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MountExtGameReq.Marshal(b, m, deterministic)
}
func (dst *MountExtGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MountExtGameReq.Merge(dst, src)
}
func (m *MountExtGameReq) XXX_Size() int {
	return xxx_messageInfo_MountExtGameReq.Size(m)
}
func (m *MountExtGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MountExtGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_MountExtGameReq proto.InternalMessageInfo

func (m *MountExtGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MountExtGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MountExtGameReq) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

type MountExtGameResp struct {
	Serial               string   `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MountExtGameResp) Reset()         { *m = MountExtGameResp{} }
func (m *MountExtGameResp) String() string { return proto.CompactTextString(m) }
func (*MountExtGameResp) ProtoMessage()    {}
func (*MountExtGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{15}
}
func (m *MountExtGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MountExtGameResp.Unmarshal(m, b)
}
func (m *MountExtGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MountExtGameResp.Marshal(b, m, deterministic)
}
func (dst *MountExtGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MountExtGameResp.Merge(dst, src)
}
func (m *MountExtGameResp) XXX_Size() int {
	return xxx_messageInfo_MountExtGameResp.Size(m)
}
func (m *MountExtGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MountExtGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_MountExtGameResp proto.InternalMessageInfo

func (m *MountExtGameResp) GetSerial() string {
	if m != nil {
		return m.Serial
	}
	return ""
}

type GetMountExtGameReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMountExtGameReq) Reset()         { *m = GetMountExtGameReq{} }
func (m *GetMountExtGameReq) String() string { return proto.CompactTextString(m) }
func (*GetMountExtGameReq) ProtoMessage()    {}
func (*GetMountExtGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{16}
}
func (m *GetMountExtGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMountExtGameReq.Unmarshal(m, b)
}
func (m *GetMountExtGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMountExtGameReq.Marshal(b, m, deterministic)
}
func (dst *GetMountExtGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMountExtGameReq.Merge(dst, src)
}
func (m *GetMountExtGameReq) XXX_Size() int {
	return xxx_messageInfo_GetMountExtGameReq.Size(m)
}
func (m *GetMountExtGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMountExtGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMountExtGameReq proto.InternalMessageInfo

func (m *GetMountExtGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMountExtGameResp struct {
	GameType             ExtGameType `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetMountExtGameResp) Reset()         { *m = GetMountExtGameResp{} }
func (m *GetMountExtGameResp) String() string { return proto.CompactTextString(m) }
func (*GetMountExtGameResp) ProtoMessage()    {}
func (*GetMountExtGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{17}
}
func (m *GetMountExtGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMountExtGameResp.Unmarshal(m, b)
}
func (m *GetMountExtGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMountExtGameResp.Marshal(b, m, deterministic)
}
func (dst *GetMountExtGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMountExtGameResp.Merge(dst, src)
}
func (m *GetMountExtGameResp) XXX_Size() int {
	return xxx_messageInfo_GetMountExtGameResp.Size(m)
}
func (m *GetMountExtGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMountExtGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMountExtGameResp proto.InternalMessageInfo

func (m *GetMountExtGameResp) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

type BatchGetMountExtGameReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetMountExtGameReq) Reset()         { *m = BatchGetMountExtGameReq{} }
func (m *BatchGetMountExtGameReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetMountExtGameReq) ProtoMessage()    {}
func (*BatchGetMountExtGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{18}
}
func (m *BatchGetMountExtGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMountExtGameReq.Unmarshal(m, b)
}
func (m *BatchGetMountExtGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMountExtGameReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetMountExtGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMountExtGameReq.Merge(dst, src)
}
func (m *BatchGetMountExtGameReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetMountExtGameReq.Size(m)
}
func (m *BatchGetMountExtGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMountExtGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMountExtGameReq proto.InternalMessageInfo

func (m *BatchGetMountExtGameReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetMountExtGameResp struct {
	CidToGame            map[uint32]uint32 `protobuf:"bytes,1,rep,name=cid_to_game,json=cidToGame,proto3" json:"cid_to_game,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetMountExtGameResp) Reset()         { *m = BatchGetMountExtGameResp{} }
func (m *BatchGetMountExtGameResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetMountExtGameResp) ProtoMessage()    {}
func (*BatchGetMountExtGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{19}
}
func (m *BatchGetMountExtGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMountExtGameResp.Unmarshal(m, b)
}
func (m *BatchGetMountExtGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMountExtGameResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetMountExtGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMountExtGameResp.Merge(dst, src)
}
func (m *BatchGetMountExtGameResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetMountExtGameResp.Size(m)
}
func (m *BatchGetMountExtGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMountExtGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMountExtGameResp proto.InternalMessageInfo

func (m *BatchGetMountExtGameResp) GetCidToGame() map[uint32]uint32 {
	if m != nil {
		return m.CidToGame
	}
	return nil
}

type UnmountExtGameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameType             uint32   `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnmountExtGameReq) Reset()         { *m = UnmountExtGameReq{} }
func (m *UnmountExtGameReq) String() string { return proto.CompactTextString(m) }
func (*UnmountExtGameReq) ProtoMessage()    {}
func (*UnmountExtGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{20}
}
func (m *UnmountExtGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnmountExtGameReq.Unmarshal(m, b)
}
func (m *UnmountExtGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnmountExtGameReq.Marshal(b, m, deterministic)
}
func (dst *UnmountExtGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnmountExtGameReq.Merge(dst, src)
}
func (m *UnmountExtGameReq) XXX_Size() int {
	return xxx_messageInfo_UnmountExtGameReq.Size(m)
}
func (m *UnmountExtGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnmountExtGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnmountExtGameReq proto.InternalMessageInfo

func (m *UnmountExtGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnmountExtGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UnmountExtGameReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type UnmountExtGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnmountExtGameResp) Reset()         { *m = UnmountExtGameResp{} }
func (m *UnmountExtGameResp) String() string { return proto.CompactTextString(m) }
func (*UnmountExtGameResp) ProtoMessage()    {}
func (*UnmountExtGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{21}
}
func (m *UnmountExtGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnmountExtGameResp.Unmarshal(m, b)
}
func (m *UnmountExtGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnmountExtGameResp.Marshal(b, m, deterministic)
}
func (dst *UnmountExtGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnmountExtGameResp.Merge(dst, src)
}
func (m *UnmountExtGameResp) XXX_Size() int {
	return xxx_messageInfo_UnmountExtGameResp.Size(m)
}
func (m *UnmountExtGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnmountExtGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnmountExtGameResp proto.InternalMessageInfo

type GetChannelBySerialReq struct {
	GameType             ExtGameType `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	Serial               string      `protobuf:"bytes,2,opt,name=serial,proto3" json:"serial,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetChannelBySerialReq) Reset()         { *m = GetChannelBySerialReq{} }
func (m *GetChannelBySerialReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelBySerialReq) ProtoMessage()    {}
func (*GetChannelBySerialReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{22}
}
func (m *GetChannelBySerialReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBySerialReq.Unmarshal(m, b)
}
func (m *GetChannelBySerialReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBySerialReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelBySerialReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBySerialReq.Merge(dst, src)
}
func (m *GetChannelBySerialReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelBySerialReq.Size(m)
}
func (m *GetChannelBySerialReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBySerialReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBySerialReq proto.InternalMessageInfo

func (m *GetChannelBySerialReq) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

func (m *GetChannelBySerialReq) GetSerial() string {
	if m != nil {
		return m.Serial
	}
	return ""
}

type GetChannelBySerialResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelBySerialResp) Reset()         { *m = GetChannelBySerialResp{} }
func (m *GetChannelBySerialResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelBySerialResp) ProtoMessage()    {}
func (*GetChannelBySerialResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{23}
}
func (m *GetChannelBySerialResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBySerialResp.Unmarshal(m, b)
}
func (m *GetChannelBySerialResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBySerialResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelBySerialResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBySerialResp.Merge(dst, src)
}
func (m *GetChannelBySerialResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelBySerialResp.Size(m)
}
func (m *GetChannelBySerialResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBySerialResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBySerialResp proto.InternalMessageInfo

func (m *GetChannelBySerialResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type StartDataReportReq struct {
	GameType             ExtGameType `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	ChannelId            uint32      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TaskTypeList         []string    `protobuf:"bytes,3,rep,name=task_type_list,json=taskTypeList,proto3" json:"task_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *StartDataReportReq) Reset()         { *m = StartDataReportReq{} }
func (m *StartDataReportReq) String() string { return proto.CompactTextString(m) }
func (*StartDataReportReq) ProtoMessage()    {}
func (*StartDataReportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{24}
}
func (m *StartDataReportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartDataReportReq.Unmarshal(m, b)
}
func (m *StartDataReportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartDataReportReq.Marshal(b, m, deterministic)
}
func (dst *StartDataReportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartDataReportReq.Merge(dst, src)
}
func (m *StartDataReportReq) XXX_Size() int {
	return xxx_messageInfo_StartDataReportReq.Size(m)
}
func (m *StartDataReportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartDataReportReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartDataReportReq proto.InternalMessageInfo

func (m *StartDataReportReq) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

func (m *StartDataReportReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartDataReportReq) GetTaskTypeList() []string {
	if m != nil {
		return m.TaskTypeList
	}
	return nil
}

type StartDataReportResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartDataReportResp) Reset()         { *m = StartDataReportResp{} }
func (m *StartDataReportResp) String() string { return proto.CompactTextString(m) }
func (*StartDataReportResp) ProtoMessage()    {}
func (*StartDataReportResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{25}
}
func (m *StartDataReportResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartDataReportResp.Unmarshal(m, b)
}
func (m *StartDataReportResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartDataReportResp.Marshal(b, m, deterministic)
}
func (dst *StartDataReportResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartDataReportResp.Merge(dst, src)
}
func (m *StartDataReportResp) XXX_Size() int {
	return xxx_messageInfo_StartDataReportResp.Size(m)
}
func (m *StartDataReportResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartDataReportResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartDataReportResp proto.InternalMessageInfo

type StopDataReportReq struct {
	GameType             ExtGameType `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	ChannelId            uint32      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TaskTypeList         []string    `protobuf:"bytes,3,rep,name=task_type_list,json=taskTypeList,proto3" json:"task_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *StopDataReportReq) Reset()         { *m = StopDataReportReq{} }
func (m *StopDataReportReq) String() string { return proto.CompactTextString(m) }
func (*StopDataReportReq) ProtoMessage()    {}
func (*StopDataReportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{26}
}
func (m *StopDataReportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopDataReportReq.Unmarshal(m, b)
}
func (m *StopDataReportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopDataReportReq.Marshal(b, m, deterministic)
}
func (dst *StopDataReportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopDataReportReq.Merge(dst, src)
}
func (m *StopDataReportReq) XXX_Size() int {
	return xxx_messageInfo_StopDataReportReq.Size(m)
}
func (m *StopDataReportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopDataReportReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopDataReportReq proto.InternalMessageInfo

func (m *StopDataReportReq) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

func (m *StopDataReportReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StopDataReportReq) GetTaskTypeList() []string {
	if m != nil {
		return m.TaskTypeList
	}
	return nil
}

type StopDataReportResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopDataReportResp) Reset()         { *m = StopDataReportResp{} }
func (m *StopDataReportResp) String() string { return proto.CompactTextString(m) }
func (*StopDataReportResp) ProtoMessage()    {}
func (*StopDataReportResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{27}
}
func (m *StopDataReportResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopDataReportResp.Unmarshal(m, b)
}
func (m *StopDataReportResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopDataReportResp.Marshal(b, m, deterministic)
}
func (dst *StopDataReportResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopDataReportResp.Merge(dst, src)
}
func (m *StopDataReportResp) XXX_Size() int {
	return xxx_messageInfo_StopDataReportResp.Size(m)
}
func (m *StopDataReportResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopDataReportResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopDataReportResp proto.InternalMessageInfo

type GetDataReportTaskStatusReq struct {
	GameType             ExtGameType `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	ChannelId            uint32      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TaskType             string      `protobuf:"bytes,3,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetDataReportTaskStatusReq) Reset()         { *m = GetDataReportTaskStatusReq{} }
func (m *GetDataReportTaskStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetDataReportTaskStatusReq) ProtoMessage()    {}
func (*GetDataReportTaskStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{28}
}
func (m *GetDataReportTaskStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDataReportTaskStatusReq.Unmarshal(m, b)
}
func (m *GetDataReportTaskStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDataReportTaskStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetDataReportTaskStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDataReportTaskStatusReq.Merge(dst, src)
}
func (m *GetDataReportTaskStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetDataReportTaskStatusReq.Size(m)
}
func (m *GetDataReportTaskStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDataReportTaskStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDataReportTaskStatusReq proto.InternalMessageInfo

func (m *GetDataReportTaskStatusReq) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

func (m *GetDataReportTaskStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetDataReportTaskStatusReq) GetTaskType() string {
	if m != nil {
		return m.TaskType
	}
	return ""
}

type GetDataReportTaskStatusResp struct {
	Status               GetDataReportTaskStatusResp_Status `protobuf:"varint,1,opt,name=status,proto3,enum=revenue_ext_game.GetDataReportTaskStatusResp_Status" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *GetDataReportTaskStatusResp) Reset()         { *m = GetDataReportTaskStatusResp{} }
func (m *GetDataReportTaskStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetDataReportTaskStatusResp) ProtoMessage()    {}
func (*GetDataReportTaskStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{29}
}
func (m *GetDataReportTaskStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDataReportTaskStatusResp.Unmarshal(m, b)
}
func (m *GetDataReportTaskStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDataReportTaskStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetDataReportTaskStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDataReportTaskStatusResp.Merge(dst, src)
}
func (m *GetDataReportTaskStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetDataReportTaskStatusResp.Size(m)
}
func (m *GetDataReportTaskStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDataReportTaskStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDataReportTaskStatusResp proto.InternalMessageInfo

func (m *GetDataReportTaskStatusResp) GetStatus() GetDataReportTaskStatusResp_Status {
	if m != nil {
		return m.Status
	}
	return GetDataReportTaskStatusResp_STATUS_UNSPECIFIED
}

type GiftInfo struct {
	GiftId               string   `protobuf:"bytes,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,2,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	GiftAmount           uint32   `protobuf:"varint,3,opt,name=gift_amount,json=giftAmount,proto3" json:"gift_amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftInfo) Reset()         { *m = GiftInfo{} }
func (m *GiftInfo) String() string { return proto.CompactTextString(m) }
func (*GiftInfo) ProtoMessage()    {}
func (*GiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{30}
}
func (m *GiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftInfo.Unmarshal(m, b)
}
func (m *GiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftInfo.Marshal(b, m, deterministic)
}
func (dst *GiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftInfo.Merge(dst, src)
}
func (m *GiftInfo) XXX_Size() int {
	return xxx_messageInfo_GiftInfo.Size(m)
}
func (m *GiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GiftInfo proto.InternalMessageInfo

func (m *GiftInfo) GetGiftId() string {
	if m != nil {
		return m.GiftId
	}
	return ""
}

func (m *GiftInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *GiftInfo) GetGiftAmount() uint32 {
	if m != nil {
		return m.GiftAmount
	}
	return 0
}

// 测试用
type ManualDataReportDemoReq struct {
	MsgType              string    `protobuf:"bytes,1,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"`
	GiftInfo             *GiftInfo `protobuf:"bytes,2,opt,name=gift_info,json=giftInfo,proto3" json:"gift_info,omitempty"`
	PublicText           string    `protobuf:"bytes,3,opt,name=public_text,json=publicText,proto3" json:"public_text,omitempty"`
	ChannelId            uint32    `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ContinuousNum        uint32    `protobuf:"varint,5,opt,name=continuous_num,json=continuousNum,proto3" json:"continuous_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ManualDataReportDemoReq) Reset()         { *m = ManualDataReportDemoReq{} }
func (m *ManualDataReportDemoReq) String() string { return proto.CompactTextString(m) }
func (*ManualDataReportDemoReq) ProtoMessage()    {}
func (*ManualDataReportDemoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{31}
}
func (m *ManualDataReportDemoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualDataReportDemoReq.Unmarshal(m, b)
}
func (m *ManualDataReportDemoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualDataReportDemoReq.Marshal(b, m, deterministic)
}
func (dst *ManualDataReportDemoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualDataReportDemoReq.Merge(dst, src)
}
func (m *ManualDataReportDemoReq) XXX_Size() int {
	return xxx_messageInfo_ManualDataReportDemoReq.Size(m)
}
func (m *ManualDataReportDemoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualDataReportDemoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ManualDataReportDemoReq proto.InternalMessageInfo

func (m *ManualDataReportDemoReq) GetMsgType() string {
	if m != nil {
		return m.MsgType
	}
	return ""
}

func (m *ManualDataReportDemoReq) GetGiftInfo() *GiftInfo {
	if m != nil {
		return m.GiftInfo
	}
	return nil
}

func (m *ManualDataReportDemoReq) GetPublicText() string {
	if m != nil {
		return m.PublicText
	}
	return ""
}

func (m *ManualDataReportDemoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ManualDataReportDemoReq) GetContinuousNum() uint32 {
	if m != nil {
		return m.ContinuousNum
	}
	return 0
}

type ManualDataReportDemoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualDataReportDemoResp) Reset()         { *m = ManualDataReportDemoResp{} }
func (m *ManualDataReportDemoResp) String() string { return proto.CompactTextString(m) }
func (*ManualDataReportDemoResp) ProtoMessage()    {}
func (*ManualDataReportDemoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{32}
}
func (m *ManualDataReportDemoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualDataReportDemoResp.Unmarshal(m, b)
}
func (m *ManualDataReportDemoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualDataReportDemoResp.Marshal(b, m, deterministic)
}
func (dst *ManualDataReportDemoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualDataReportDemoResp.Merge(dst, src)
}
func (m *ManualDataReportDemoResp) XXX_Size() int {
	return xxx_messageInfo_ManualDataReportDemoResp.Size(m)
}
func (m *ManualDataReportDemoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualDataReportDemoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ManualDataReportDemoResp proto.InternalMessageInfo

// 用户点击“我想玩”上报
type ReportUserWantPlayReq struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameType             ExtGameType `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ReportUserWantPlayReq) Reset()         { *m = ReportUserWantPlayReq{} }
func (m *ReportUserWantPlayReq) String() string { return proto.CompactTextString(m) }
func (*ReportUserWantPlayReq) ProtoMessage()    {}
func (*ReportUserWantPlayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{33}
}
func (m *ReportUserWantPlayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserWantPlayReq.Unmarshal(m, b)
}
func (m *ReportUserWantPlayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserWantPlayReq.Marshal(b, m, deterministic)
}
func (dst *ReportUserWantPlayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserWantPlayReq.Merge(dst, src)
}
func (m *ReportUserWantPlayReq) XXX_Size() int {
	return xxx_messageInfo_ReportUserWantPlayReq.Size(m)
}
func (m *ReportUserWantPlayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserWantPlayReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserWantPlayReq proto.InternalMessageInfo

func (m *ReportUserWantPlayReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportUserWantPlayReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportUserWantPlayReq) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

type ReportUserWantPlayResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportUserWantPlayResp) Reset()         { *m = ReportUserWantPlayResp{} }
func (m *ReportUserWantPlayResp) String() string { return proto.CompactTextString(m) }
func (*ReportUserWantPlayResp) ProtoMessage()    {}
func (*ReportUserWantPlayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{34}
}
func (m *ReportUserWantPlayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserWantPlayResp.Unmarshal(m, b)
}
func (m *ReportUserWantPlayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserWantPlayResp.Marshal(b, m, deterministic)
}
func (dst *ReportUserWantPlayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserWantPlayResp.Merge(dst, src)
}
func (m *ReportUserWantPlayResp) XXX_Size() int {
	return xxx_messageInfo_ReportUserWantPlayResp.Size(m)
}
func (m *ReportUserWantPlayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserWantPlayResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserWantPlayResp proto.InternalMessageInfo

type GetChannelExtGameAccessStatusReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelExtGameAccessStatusReq) Reset()         { *m = GetChannelExtGameAccessStatusReq{} }
func (m *GetChannelExtGameAccessStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelExtGameAccessStatusReq) ProtoMessage()    {}
func (*GetChannelExtGameAccessStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{35}
}
func (m *GetChannelExtGameAccessStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelExtGameAccessStatusReq.Unmarshal(m, b)
}
func (m *GetChannelExtGameAccessStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelExtGameAccessStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelExtGameAccessStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelExtGameAccessStatusReq.Merge(dst, src)
}
func (m *GetChannelExtGameAccessStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelExtGameAccessStatusReq.Size(m)
}
func (m *GetChannelExtGameAccessStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelExtGameAccessStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelExtGameAccessStatusReq proto.InternalMessageInfo

func (m *GetChannelExtGameAccessStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelExtGameAccessStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetChannelExtGameAccessStatusResp struct {
	AccessStatus         bool                                         `protobuf:"varint,1,opt,name=access_status,json=accessStatus,proto3" json:"access_status,omitempty"`
	StreamType           GetChannelExtGameAccessStatusResp_StreamType `protobuf:"varint,2,opt,name=stream_type,json=streamType,proto3,enum=revenue_ext_game.GetChannelExtGameAccessStatusResp_StreamType" json:"stream_type,omitempty"`
	StreamUrl            string                                       `protobuf:"bytes,3,opt,name=stream_url,json=streamUrl,proto3" json:"stream_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetChannelExtGameAccessStatusResp) Reset()         { *m = GetChannelExtGameAccessStatusResp{} }
func (m *GetChannelExtGameAccessStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelExtGameAccessStatusResp) ProtoMessage()    {}
func (*GetChannelExtGameAccessStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{36}
}
func (m *GetChannelExtGameAccessStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelExtGameAccessStatusResp.Unmarshal(m, b)
}
func (m *GetChannelExtGameAccessStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelExtGameAccessStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelExtGameAccessStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelExtGameAccessStatusResp.Merge(dst, src)
}
func (m *GetChannelExtGameAccessStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelExtGameAccessStatusResp.Size(m)
}
func (m *GetChannelExtGameAccessStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelExtGameAccessStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelExtGameAccessStatusResp proto.InternalMessageInfo

func (m *GetChannelExtGameAccessStatusResp) GetAccessStatus() bool {
	if m != nil {
		return m.AccessStatus
	}
	return false
}

func (m *GetChannelExtGameAccessStatusResp) GetStreamType() GetChannelExtGameAccessStatusResp_StreamType {
	if m != nil {
		return m.StreamType
	}
	return GetChannelExtGameAccessStatusResp_STREAM_TYPE_UNSPECIFIED
}

func (m *GetChannelExtGameAccessStatusResp) GetStreamUrl() string {
	if m != nil {
		return m.StreamUrl
	}
	return ""
}

type SetGameScoresRankReq struct {
	GameType             ExtGameType                      `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=revenue_ext_game.ExtGameType" json:"game_type,omitempty"`
	RankType             uint32                           `protobuf:"varint,2,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	RankName             string                           `protobuf:"bytes,3,opt,name=rank_name,json=rankName,proto3" json:"rank_name,omitempty"`
	InfoList             []*SetGameScoresRankReq_RankInfo `protobuf:"bytes,4,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *SetGameScoresRankReq) Reset()         { *m = SetGameScoresRankReq{} }
func (m *SetGameScoresRankReq) String() string { return proto.CompactTextString(m) }
func (*SetGameScoresRankReq) ProtoMessage()    {}
func (*SetGameScoresRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{37}
}
func (m *SetGameScoresRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameScoresRankReq.Unmarshal(m, b)
}
func (m *SetGameScoresRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameScoresRankReq.Marshal(b, m, deterministic)
}
func (dst *SetGameScoresRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameScoresRankReq.Merge(dst, src)
}
func (m *SetGameScoresRankReq) XXX_Size() int {
	return xxx_messageInfo_SetGameScoresRankReq.Size(m)
}
func (m *SetGameScoresRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameScoresRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameScoresRankReq proto.InternalMessageInfo

func (m *SetGameScoresRankReq) GetGameType() ExtGameType {
	if m != nil {
		return m.GameType
	}
	return ExtGameType_EXT_GAME_TYPE_UNSPECIFIED
}

func (m *SetGameScoresRankReq) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *SetGameScoresRankReq) GetRankName() string {
	if m != nil {
		return m.RankName
	}
	return ""
}

func (m *SetGameScoresRankReq) GetInfoList() []*SetGameScoresRankReq_RankInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type SetGameScoresRankReq_RankInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RecordUid            uint32   `protobuf:"varint,2,opt,name=record_uid,json=recordUid,proto3" json:"record_uid,omitempty"`
	Scores               uint64   `protobuf:"varint,3,opt,name=scores,proto3" json:"scores,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameScoresRankReq_RankInfo) Reset()         { *m = SetGameScoresRankReq_RankInfo{} }
func (m *SetGameScoresRankReq_RankInfo) String() string { return proto.CompactTextString(m) }
func (*SetGameScoresRankReq_RankInfo) ProtoMessage()    {}
func (*SetGameScoresRankReq_RankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{37, 0}
}
func (m *SetGameScoresRankReq_RankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameScoresRankReq_RankInfo.Unmarshal(m, b)
}
func (m *SetGameScoresRankReq_RankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameScoresRankReq_RankInfo.Marshal(b, m, deterministic)
}
func (dst *SetGameScoresRankReq_RankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameScoresRankReq_RankInfo.Merge(dst, src)
}
func (m *SetGameScoresRankReq_RankInfo) XXX_Size() int {
	return xxx_messageInfo_SetGameScoresRankReq_RankInfo.Size(m)
}
func (m *SetGameScoresRankReq_RankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameScoresRankReq_RankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameScoresRankReq_RankInfo proto.InternalMessageInfo

func (m *SetGameScoresRankReq_RankInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetGameScoresRankReq_RankInfo) GetRecordUid() uint32 {
	if m != nil {
		return m.RecordUid
	}
	return 0
}

func (m *SetGameScoresRankReq_RankInfo) GetScores() uint64 {
	if m != nil {
		return m.Scores
	}
	return 0
}

type SetGameScoresRankResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameScoresRankResp) Reset()         { *m = SetGameScoresRankResp{} }
func (m *SetGameScoresRankResp) String() string { return proto.CompactTextString(m) }
func (*SetGameScoresRankResp) ProtoMessage()    {}
func (*SetGameScoresRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{38}
}
func (m *SetGameScoresRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameScoresRankResp.Unmarshal(m, b)
}
func (m *SetGameScoresRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameScoresRankResp.Marshal(b, m, deterministic)
}
func (dst *SetGameScoresRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameScoresRankResp.Merge(dst, src)
}
func (m *SetGameScoresRankResp) XXX_Size() int {
	return xxx_messageInfo_SetGameScoresRankResp.Size(m)
}
func (m *SetGameScoresRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameScoresRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameScoresRankResp proto.InternalMessageInfo

type UKWProfile struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	Medal                string   `protobuf:"bytes,5,opt,name=medal,proto3" json:"medal,omitempty"`
	HeadFrame            string   `protobuf:"bytes,6,opt,name=head_frame,json=headFrame,proto3" json:"head_frame,omitempty"`
	FakeUid              uint32   `protobuf:"varint,7,opt,name=fake_uid,json=fakeUid,proto3" json:"fake_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UKWProfile) Reset()         { *m = UKWProfile{} }
func (m *UKWProfile) String() string { return proto.CompactTextString(m) }
func (*UKWProfile) ProtoMessage()    {}
func (*UKWProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{39}
}
func (m *UKWProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UKWProfile.Unmarshal(m, b)
}
func (m *UKWProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UKWProfile.Marshal(b, m, deterministic)
}
func (dst *UKWProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UKWProfile.Merge(dst, src)
}
func (m *UKWProfile) XXX_Size() int {
	return xxx_messageInfo_UKWProfile.Size(m)
}
func (m *UKWProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UKWProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UKWProfile proto.InternalMessageInfo

func (m *UKWProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UKWProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UKWProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UKWProfile) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *UKWProfile) GetMedal() string {
	if m != nil {
		return m.Medal
	}
	return ""
}

func (m *UKWProfile) GetHeadFrame() string {
	if m != nil {
		return m.HeadFrame
	}
	return ""
}

func (m *UKWProfile) GetFakeUid() uint32 {
	if m != nil {
		return m.FakeUid
	}
	return 0
}

type UserRankInfo struct {
	Uid                  uint32      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scores               uint64      `protobuf:"varint,2,opt,name=scores,proto3" json:"scores,omitempty"`
	Rank                 uint32      `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`
	UkwInfo              *UKWProfile `protobuf:"bytes,4,opt,name=ukw_info,json=ukwInfo,proto3" json:"ukw_info,omitempty"`
	NameplateUrlList     []string    `protobuf:"bytes,5,rep,name=nameplate_url_list,json=nameplateUrlList,proto3" json:"nameplate_url_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UserRankInfo) Reset()         { *m = UserRankInfo{} }
func (m *UserRankInfo) String() string { return proto.CompactTextString(m) }
func (*UserRankInfo) ProtoMessage()    {}
func (*UserRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{40}
}
func (m *UserRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRankInfo.Unmarshal(m, b)
}
func (m *UserRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRankInfo.Marshal(b, m, deterministic)
}
func (dst *UserRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRankInfo.Merge(dst, src)
}
func (m *UserRankInfo) XXX_Size() int {
	return xxx_messageInfo_UserRankInfo.Size(m)
}
func (m *UserRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserRankInfo proto.InternalMessageInfo

func (m *UserRankInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRankInfo) GetScores() uint64 {
	if m != nil {
		return m.Scores
	}
	return 0
}

func (m *UserRankInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *UserRankInfo) GetUkwInfo() *UKWProfile {
	if m != nil {
		return m.UkwInfo
	}
	return nil
}

func (m *UserRankInfo) GetNameplateUrlList() []string {
	if m != nil {
		return m.NameplateUrlList
	}
	return nil
}

// 获取游戏世界榜单
type GetExtGameScoreRankReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FakeUid              uint32   `protobuf:"varint,2,opt,name=fake_uid,json=fakeUid,proto3" json:"fake_uid,omitempty"`
	IsUkw                bool     `protobuf:"varint,3,opt,name=is_ukw,json=isUkw,proto3" json:"is_ukw,omitempty"`
	GameType             uint32   `protobuf:"varint,4,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	RankType             uint32   `protobuf:"varint,5,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	PageToken            string   `protobuf:"bytes,6,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32   `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtGameScoreRankReq) Reset()         { *m = GetExtGameScoreRankReq{} }
func (m *GetExtGameScoreRankReq) String() string { return proto.CompactTextString(m) }
func (*GetExtGameScoreRankReq) ProtoMessage()    {}
func (*GetExtGameScoreRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{41}
}
func (m *GetExtGameScoreRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtGameScoreRankReq.Unmarshal(m, b)
}
func (m *GetExtGameScoreRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtGameScoreRankReq.Marshal(b, m, deterministic)
}
func (dst *GetExtGameScoreRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtGameScoreRankReq.Merge(dst, src)
}
func (m *GetExtGameScoreRankReq) XXX_Size() int {
	return xxx_messageInfo_GetExtGameScoreRankReq.Size(m)
}
func (m *GetExtGameScoreRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtGameScoreRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtGameScoreRankReq proto.InternalMessageInfo

func (m *GetExtGameScoreRankReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetExtGameScoreRankReq) GetFakeUid() uint32 {
	if m != nil {
		return m.FakeUid
	}
	return 0
}

func (m *GetExtGameScoreRankReq) GetIsUkw() bool {
	if m != nil {
		return m.IsUkw
	}
	return false
}

func (m *GetExtGameScoreRankReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GetExtGameScoreRankReq) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *GetExtGameScoreRankReq) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *GetExtGameScoreRankReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetExtGameScoreRankResp struct {
	RankList             []*UserRankInfo `protobuf:"bytes,1,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	UserRank             *UserRankInfo   `protobuf:"bytes,2,opt,name=user_rank,json=userRank,proto3" json:"user_rank,omitempty"`
	NextPageToken        string          `protobuf:"bytes,3,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	RankName             string          `protobuf:"bytes,4,opt,name=rank_name,json=rankName,proto3" json:"rank_name,omitempty"`
	RankDesc             string          `protobuf:"bytes,5,opt,name=rank_desc,json=rankDesc,proto3" json:"rank_desc,omitempty"`
	RankDescHighLight    string          `protobuf:"bytes,6,opt,name=rank_desc_high_light,json=rankDescHighLight,proto3" json:"rank_desc_high_light,omitempty"`
	RankCmsUrlSuffix     string          `protobuf:"bytes,7,opt,name=rank_cms_url_suffix,json=rankCmsUrlSuffix,proto3" json:"rank_cms_url_suffix,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetExtGameScoreRankResp) Reset()         { *m = GetExtGameScoreRankResp{} }
func (m *GetExtGameScoreRankResp) String() string { return proto.CompactTextString(m) }
func (*GetExtGameScoreRankResp) ProtoMessage()    {}
func (*GetExtGameScoreRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{42}
}
func (m *GetExtGameScoreRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtGameScoreRankResp.Unmarshal(m, b)
}
func (m *GetExtGameScoreRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtGameScoreRankResp.Marshal(b, m, deterministic)
}
func (dst *GetExtGameScoreRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtGameScoreRankResp.Merge(dst, src)
}
func (m *GetExtGameScoreRankResp) XXX_Size() int {
	return xxx_messageInfo_GetExtGameScoreRankResp.Size(m)
}
func (m *GetExtGameScoreRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtGameScoreRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtGameScoreRankResp proto.InternalMessageInfo

func (m *GetExtGameScoreRankResp) GetRankList() []*UserRankInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetExtGameScoreRankResp) GetUserRank() *UserRankInfo {
	if m != nil {
		return m.UserRank
	}
	return nil
}

func (m *GetExtGameScoreRankResp) GetNextPageToken() string {
	if m != nil {
		return m.NextPageToken
	}
	return ""
}

func (m *GetExtGameScoreRankResp) GetRankName() string {
	if m != nil {
		return m.RankName
	}
	return ""
}

func (m *GetExtGameScoreRankResp) GetRankDesc() string {
	if m != nil {
		return m.RankDesc
	}
	return ""
}

func (m *GetExtGameScoreRankResp) GetRankDescHighLight() string {
	if m != nil {
		return m.RankDescHighLight
	}
	return ""
}

func (m *GetExtGameScoreRankResp) GetRankCmsUrlSuffix() string {
	if m != nil {
		return m.RankCmsUrlSuffix
	}
	return ""
}

type UserRank struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Rank                 uint32   `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRank) Reset()         { *m = UserRank{} }
func (m *UserRank) String() string { return proto.CompactTextString(m) }
func (*UserRank) ProtoMessage()    {}
func (*UserRank) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{43}
}
func (m *UserRank) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRank.Unmarshal(m, b)
}
func (m *UserRank) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRank.Marshal(b, m, deterministic)
}
func (dst *UserRank) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRank.Merge(dst, src)
}
func (m *UserRank) XXX_Size() int {
	return xxx_messageInfo_UserRank.Size(m)
}
func (m *UserRank) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRank.DiscardUnknown(m)
}

var xxx_messageInfo_UserRank proto.InternalMessageInfo

func (m *UserRank) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRank) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type GameRankHonorInfo struct {
	GameType             uint32      `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	RankType             uint32      `protobuf:"varint,2,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	GameName             string      `protobuf:"bytes,3,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	RankName             string      `protobuf:"bytes,4,opt,name=rank_name,json=rankName,proto3" json:"rank_name,omitempty"`
	InfoList             []*UserRank `protobuf:"bytes,5,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GameRankHonorInfo) Reset()         { *m = GameRankHonorInfo{} }
func (m *GameRankHonorInfo) String() string { return proto.CompactTextString(m) }
func (*GameRankHonorInfo) ProtoMessage()    {}
func (*GameRankHonorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{44}
}
func (m *GameRankHonorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameRankHonorInfo.Unmarshal(m, b)
}
func (m *GameRankHonorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameRankHonorInfo.Marshal(b, m, deterministic)
}
func (dst *GameRankHonorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameRankHonorInfo.Merge(dst, src)
}
func (m *GameRankHonorInfo) XXX_Size() int {
	return xxx_messageInfo_GameRankHonorInfo.Size(m)
}
func (m *GameRankHonorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameRankHonorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameRankHonorInfo proto.InternalMessageInfo

func (m *GameRankHonorInfo) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GameRankHonorInfo) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *GameRankHonorInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameRankHonorInfo) GetRankName() string {
	if m != nil {
		return m.RankName
	}
	return ""
}

func (m *GameRankHonorInfo) GetInfoList() []*UserRank {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 荣誉外显获取榜单信息
type GetExtGameRankHonorInfoReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	RankType             uint32   `protobuf:"varint,2,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	TopN                 uint32   `protobuf:"varint,3,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtGameRankHonorInfoReq) Reset()         { *m = GetExtGameRankHonorInfoReq{} }
func (m *GetExtGameRankHonorInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetExtGameRankHonorInfoReq) ProtoMessage()    {}
func (*GetExtGameRankHonorInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{45}
}
func (m *GetExtGameRankHonorInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtGameRankHonorInfoReq.Unmarshal(m, b)
}
func (m *GetExtGameRankHonorInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtGameRankHonorInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetExtGameRankHonorInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtGameRankHonorInfoReq.Merge(dst, src)
}
func (m *GetExtGameRankHonorInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetExtGameRankHonorInfoReq.Size(m)
}
func (m *GetExtGameRankHonorInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtGameRankHonorInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtGameRankHonorInfoReq proto.InternalMessageInfo

func (m *GetExtGameRankHonorInfoReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GetExtGameRankHonorInfoReq) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *GetExtGameRankHonorInfoReq) GetTopN() uint32 {
	if m != nil {
		return m.TopN
	}
	return 0
}

type GetExtGameRankHonorInfoResp struct {
	Info                 *GameRankHonorInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetExtGameRankHonorInfoResp) Reset()         { *m = GetExtGameRankHonorInfoResp{} }
func (m *GetExtGameRankHonorInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetExtGameRankHonorInfoResp) ProtoMessage()    {}
func (*GetExtGameRankHonorInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{46}
}
func (m *GetExtGameRankHonorInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtGameRankHonorInfoResp.Unmarshal(m, b)
}
func (m *GetExtGameRankHonorInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtGameRankHonorInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetExtGameRankHonorInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtGameRankHonorInfoResp.Merge(dst, src)
}
func (m *GetExtGameRankHonorInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetExtGameRankHonorInfoResp.Size(m)
}
func (m *GetExtGameRankHonorInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtGameRankHonorInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtGameRankHonorInfoResp proto.InternalMessageInfo

func (m *GetExtGameRankHonorInfoResp) GetInfo() *GameRankHonorInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetExtGameRankNameplateReq struct {
	Uid                  uint32                              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ShowType             GetExtGameRankNameplateReq_ShowType `protobuf:"varint,2,opt,name=show_type,json=showType,proto3,enum=revenue_ext_game.GetExtGameRankNameplateReq_ShowType" json:"show_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetExtGameRankNameplateReq) Reset()         { *m = GetExtGameRankNameplateReq{} }
func (m *GetExtGameRankNameplateReq) String() string { return proto.CompactTextString(m) }
func (*GetExtGameRankNameplateReq) ProtoMessage()    {}
func (*GetExtGameRankNameplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{47}
}
func (m *GetExtGameRankNameplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtGameRankNameplateReq.Unmarshal(m, b)
}
func (m *GetExtGameRankNameplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtGameRankNameplateReq.Marshal(b, m, deterministic)
}
func (dst *GetExtGameRankNameplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtGameRankNameplateReq.Merge(dst, src)
}
func (m *GetExtGameRankNameplateReq) XXX_Size() int {
	return xxx_messageInfo_GetExtGameRankNameplateReq.Size(m)
}
func (m *GetExtGameRankNameplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtGameRankNameplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtGameRankNameplateReq proto.InternalMessageInfo

func (m *GetExtGameRankNameplateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetExtGameRankNameplateReq) GetShowType() GetExtGameRankNameplateReq_ShowType {
	if m != nil {
		return m.ShowType
	}
	return GetExtGameRankNameplateReq_SHOW_TYPE_UNSPECIFIED
}

type GetExtGameRankNameplateResp struct {
	InfoList             []string `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtGameRankNameplateResp) Reset()         { *m = GetExtGameRankNameplateResp{} }
func (m *GetExtGameRankNameplateResp) String() string { return proto.CompactTextString(m) }
func (*GetExtGameRankNameplateResp) ProtoMessage()    {}
func (*GetExtGameRankNameplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{48}
}
func (m *GetExtGameRankNameplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtGameRankNameplateResp.Unmarshal(m, b)
}
func (m *GetExtGameRankNameplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtGameRankNameplateResp.Marshal(b, m, deterministic)
}
func (dst *GetExtGameRankNameplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtGameRankNameplateResp.Merge(dst, src)
}
func (m *GetExtGameRankNameplateResp) XXX_Size() int {
	return xxx_messageInfo_GetExtGameRankNameplateResp.Size(m)
}
func (m *GetExtGameRankNameplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtGameRankNameplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtGameRankNameplateResp proto.InternalMessageInfo

func (m *GetExtGameRankNameplateResp) GetInfoList() []string {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type ChatPkMem struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	MemLayout            []uint32 `protobuf:"varint,2,rep,packed,name=mem_layout,json=memLayout,proto3" json:"mem_layout,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatPkMem) Reset()         { *m = ChatPkMem{} }
func (m *ChatPkMem) String() string { return proto.CompactTextString(m) }
func (*ChatPkMem) ProtoMessage()    {}
func (*ChatPkMem) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{49}
}
func (m *ChatPkMem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatPkMem.Unmarshal(m, b)
}
func (m *ChatPkMem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatPkMem.Marshal(b, m, deterministic)
}
func (dst *ChatPkMem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatPkMem.Merge(dst, src)
}
func (m *ChatPkMem) XXX_Size() int {
	return xxx_messageInfo_ChatPkMem.Size(m)
}
func (m *ChatPkMem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatPkMem.DiscardUnknown(m)
}

var xxx_messageInfo_ChatPkMem proto.InternalMessageInfo

func (m *ChatPkMem) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ChatPkMem) GetMemLayout() []uint32 {
	if m != nil {
		return m.MemLayout
	}
	return nil
}

type StartChatPkReq struct {
	GameType             uint32       `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	PkId                 string       `protobuf:"bytes,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	Cid                  uint32       `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	MemList              []*ChatPkMem `protobuf:"bytes,4,rep,name=mem_list,json=memList,proto3" json:"mem_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartChatPkReq) Reset()         { *m = StartChatPkReq{} }
func (m *StartChatPkReq) String() string { return proto.CompactTextString(m) }
func (*StartChatPkReq) ProtoMessage()    {}
func (*StartChatPkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{50}
}
func (m *StartChatPkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChatPkReq.Unmarshal(m, b)
}
func (m *StartChatPkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChatPkReq.Marshal(b, m, deterministic)
}
func (dst *StartChatPkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChatPkReq.Merge(dst, src)
}
func (m *StartChatPkReq) XXX_Size() int {
	return xxx_messageInfo_StartChatPkReq.Size(m)
}
func (m *StartChatPkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChatPkReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartChatPkReq proto.InternalMessageInfo

func (m *StartChatPkReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *StartChatPkReq) GetPkId() string {
	if m != nil {
		return m.PkId
	}
	return ""
}

func (m *StartChatPkReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *StartChatPkReq) GetMemList() []*ChatPkMem {
	if m != nil {
		return m.MemList
	}
	return nil
}

type StartChatPkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartChatPkResp) Reset()         { *m = StartChatPkResp{} }
func (m *StartChatPkResp) String() string { return proto.CompactTextString(m) }
func (*StartChatPkResp) ProtoMessage()    {}
func (*StartChatPkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{51}
}
func (m *StartChatPkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChatPkResp.Unmarshal(m, b)
}
func (m *StartChatPkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChatPkResp.Marshal(b, m, deterministic)
}
func (dst *StartChatPkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChatPkResp.Merge(dst, src)
}
func (m *StartChatPkResp) XXX_Size() int {
	return xxx_messageInfo_StartChatPkResp.Size(m)
}
func (m *StartChatPkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChatPkResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartChatPkResp proto.InternalMessageInfo

type StopChatPkReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	PkId                 string   `protobuf:"bytes,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopChatPkReq) Reset()         { *m = StopChatPkReq{} }
func (m *StopChatPkReq) String() string { return proto.CompactTextString(m) }
func (*StopChatPkReq) ProtoMessage()    {}
func (*StopChatPkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{52}
}
func (m *StopChatPkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopChatPkReq.Unmarshal(m, b)
}
func (m *StopChatPkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopChatPkReq.Marshal(b, m, deterministic)
}
func (dst *StopChatPkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopChatPkReq.Merge(dst, src)
}
func (m *StopChatPkReq) XXX_Size() int {
	return xxx_messageInfo_StopChatPkReq.Size(m)
}
func (m *StopChatPkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopChatPkReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopChatPkReq proto.InternalMessageInfo

func (m *StopChatPkReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *StopChatPkReq) GetPkId() string {
	if m != nil {
		return m.PkId
	}
	return ""
}

type StopChatPkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopChatPkResp) Reset()         { *m = StopChatPkResp{} }
func (m *StopChatPkResp) String() string { return proto.CompactTextString(m) }
func (*StopChatPkResp) ProtoMessage()    {}
func (*StopChatPkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{53}
}
func (m *StopChatPkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopChatPkResp.Unmarshal(m, b)
}
func (m *StopChatPkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopChatPkResp.Marshal(b, m, deterministic)
}
func (dst *StopChatPkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopChatPkResp.Merge(dst, src)
}
func (m *StopChatPkResp) XXX_Size() int {
	return xxx_messageInfo_StopChatPkResp.Size(m)
}
func (m *StopChatPkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopChatPkResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopChatPkResp proto.InternalMessageInfo

type ChatPkHeartbeatReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	PkId                 string   `protobuf:"bytes,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatPkHeartbeatReq) Reset()         { *m = ChatPkHeartbeatReq{} }
func (m *ChatPkHeartbeatReq) String() string { return proto.CompactTextString(m) }
func (*ChatPkHeartbeatReq) ProtoMessage()    {}
func (*ChatPkHeartbeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{54}
}
func (m *ChatPkHeartbeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatPkHeartbeatReq.Unmarshal(m, b)
}
func (m *ChatPkHeartbeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatPkHeartbeatReq.Marshal(b, m, deterministic)
}
func (dst *ChatPkHeartbeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatPkHeartbeatReq.Merge(dst, src)
}
func (m *ChatPkHeartbeatReq) XXX_Size() int {
	return xxx_messageInfo_ChatPkHeartbeatReq.Size(m)
}
func (m *ChatPkHeartbeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatPkHeartbeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChatPkHeartbeatReq proto.InternalMessageInfo

func (m *ChatPkHeartbeatReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *ChatPkHeartbeatReq) GetPkId() string {
	if m != nil {
		return m.PkId
	}
	return ""
}

type ChatPkHeartbeatResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatPkHeartbeatResp) Reset()         { *m = ChatPkHeartbeatResp{} }
func (m *ChatPkHeartbeatResp) String() string { return proto.CompactTextString(m) }
func (*ChatPkHeartbeatResp) ProtoMessage()    {}
func (*ChatPkHeartbeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{55}
}
func (m *ChatPkHeartbeatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatPkHeartbeatResp.Unmarshal(m, b)
}
func (m *ChatPkHeartbeatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatPkHeartbeatResp.Marshal(b, m, deterministic)
}
func (dst *ChatPkHeartbeatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatPkHeartbeatResp.Merge(dst, src)
}
func (m *ChatPkHeartbeatResp) XXX_Size() int {
	return xxx_messageInfo_ChatPkHeartbeatResp.Size(m)
}
func (m *ChatPkHeartbeatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatPkHeartbeatResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChatPkHeartbeatResp proto.InternalMessageInfo

type ChannelExtGameAccessConf struct {
	ConfId               uint32   `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelType          uint32   `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	BeginTime            uint32   `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	GameType             uint32   `protobuf:"varint,8,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelExtGameAccessConf) Reset()         { *m = ChannelExtGameAccessConf{} }
func (m *ChannelExtGameAccessConf) String() string { return proto.CompactTextString(m) }
func (*ChannelExtGameAccessConf) ProtoMessage()    {}
func (*ChannelExtGameAccessConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{56}
}
func (m *ChannelExtGameAccessConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelExtGameAccessConf.Unmarshal(m, b)
}
func (m *ChannelExtGameAccessConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelExtGameAccessConf.Marshal(b, m, deterministic)
}
func (dst *ChannelExtGameAccessConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelExtGameAccessConf.Merge(dst, src)
}
func (m *ChannelExtGameAccessConf) XXX_Size() int {
	return xxx_messageInfo_ChannelExtGameAccessConf.Size(m)
}
func (m *ChannelExtGameAccessConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelExtGameAccessConf.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelExtGameAccessConf proto.InternalMessageInfo

func (m *ChannelExtGameAccessConf) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *ChannelExtGameAccessConf) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelExtGameAccessConf) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelExtGameAccessConf) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelExtGameAccessConf) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ChannelExtGameAccessConf) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ChannelExtGameAccessConf) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ChannelExtGameAccessConf) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type BatSetChannelExtGameAccessConfReq struct {
	// uint32 begin_time = 1; // 生效开始时间 废弃
	// uint32 end_time = 2;   // 生效结束时间 废弃
	ConfList             []*ChannelExtGameAccessConf `protobuf:"bytes,3,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	OpId                 string                      `protobuf:"bytes,4,opt,name=op_id,json=opId,proto3" json:"op_id,omitempty"`
	OpName               string                      `protobuf:"bytes,5,opt,name=op_name,json=opName,proto3" json:"op_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatSetChannelExtGameAccessConfReq) Reset()         { *m = BatSetChannelExtGameAccessConfReq{} }
func (m *BatSetChannelExtGameAccessConfReq) String() string { return proto.CompactTextString(m) }
func (*BatSetChannelExtGameAccessConfReq) ProtoMessage()    {}
func (*BatSetChannelExtGameAccessConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{57}
}
func (m *BatSetChannelExtGameAccessConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatSetChannelExtGameAccessConfReq.Unmarshal(m, b)
}
func (m *BatSetChannelExtGameAccessConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatSetChannelExtGameAccessConfReq.Marshal(b, m, deterministic)
}
func (dst *BatSetChannelExtGameAccessConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatSetChannelExtGameAccessConfReq.Merge(dst, src)
}
func (m *BatSetChannelExtGameAccessConfReq) XXX_Size() int {
	return xxx_messageInfo_BatSetChannelExtGameAccessConfReq.Size(m)
}
func (m *BatSetChannelExtGameAccessConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatSetChannelExtGameAccessConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatSetChannelExtGameAccessConfReq proto.InternalMessageInfo

func (m *BatSetChannelExtGameAccessConfReq) GetConfList() []*ChannelExtGameAccessConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *BatSetChannelExtGameAccessConfReq) GetOpId() string {
	if m != nil {
		return m.OpId
	}
	return ""
}

func (m *BatSetChannelExtGameAccessConfReq) GetOpName() string {
	if m != nil {
		return m.OpName
	}
	return ""
}

type BatSetChannelExtGameAccessConfResp struct {
	TimeConflictCidList  []uint32 `protobuf:"varint,1,rep,packed,name=time_conflict_cid_list,json=timeConflictCidList,proto3" json:"time_conflict_cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatSetChannelExtGameAccessConfResp) Reset()         { *m = BatSetChannelExtGameAccessConfResp{} }
func (m *BatSetChannelExtGameAccessConfResp) String() string { return proto.CompactTextString(m) }
func (*BatSetChannelExtGameAccessConfResp) ProtoMessage()    {}
func (*BatSetChannelExtGameAccessConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{58}
}
func (m *BatSetChannelExtGameAccessConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatSetChannelExtGameAccessConfResp.Unmarshal(m, b)
}
func (m *BatSetChannelExtGameAccessConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatSetChannelExtGameAccessConfResp.Marshal(b, m, deterministic)
}
func (dst *BatSetChannelExtGameAccessConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatSetChannelExtGameAccessConfResp.Merge(dst, src)
}
func (m *BatSetChannelExtGameAccessConfResp) XXX_Size() int {
	return xxx_messageInfo_BatSetChannelExtGameAccessConfResp.Size(m)
}
func (m *BatSetChannelExtGameAccessConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatSetChannelExtGameAccessConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatSetChannelExtGameAccessConfResp proto.InternalMessageInfo

func (m *BatSetChannelExtGameAccessConfResp) GetTimeConflictCidList() []uint32 {
	if m != nil {
		return m.TimeConflictCidList
	}
	return nil
}

//
// uid != 0 时，默认通过主播uid进行查询
// uid==0且channel_id<>0时，通过channel_id 进行查询
// uid==0且channel_id==0时，按更新时间降序进行分页查询
type GetChannelExtGameAccessConfReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PageLimit            uint32   `protobuf:"varint,3,opt,name=page_limit,json=pageLimit,proto3" json:"page_limit,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelExtGameAccessConfReq) Reset()         { *m = GetChannelExtGameAccessConfReq{} }
func (m *GetChannelExtGameAccessConfReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelExtGameAccessConfReq) ProtoMessage()    {}
func (*GetChannelExtGameAccessConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{59}
}
func (m *GetChannelExtGameAccessConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelExtGameAccessConfReq.Unmarshal(m, b)
}
func (m *GetChannelExtGameAccessConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelExtGameAccessConfReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelExtGameAccessConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelExtGameAccessConfReq.Merge(dst, src)
}
func (m *GetChannelExtGameAccessConfReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelExtGameAccessConfReq.Size(m)
}
func (m *GetChannelExtGameAccessConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelExtGameAccessConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelExtGameAccessConfReq proto.InternalMessageInfo

func (m *GetChannelExtGameAccessConfReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelExtGameAccessConfReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelExtGameAccessConfReq) GetPageLimit() uint32 {
	if m != nil {
		return m.PageLimit
	}
	return 0
}

func (m *GetChannelExtGameAccessConfReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

type GetChannelExtGameAccessConfResp struct {
	TotalCnt             uint32                      `protobuf:"varint,1,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	ConfList             []*ChannelExtGameAccessConf `protobuf:"bytes,3,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetChannelExtGameAccessConfResp) Reset()         { *m = GetChannelExtGameAccessConfResp{} }
func (m *GetChannelExtGameAccessConfResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelExtGameAccessConfResp) ProtoMessage()    {}
func (*GetChannelExtGameAccessConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{60}
}
func (m *GetChannelExtGameAccessConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelExtGameAccessConfResp.Unmarshal(m, b)
}
func (m *GetChannelExtGameAccessConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelExtGameAccessConfResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelExtGameAccessConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelExtGameAccessConfResp.Merge(dst, src)
}
func (m *GetChannelExtGameAccessConfResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelExtGameAccessConfResp.Size(m)
}
func (m *GetChannelExtGameAccessConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelExtGameAccessConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelExtGameAccessConfResp proto.InternalMessageInfo

func (m *GetChannelExtGameAccessConfResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *GetChannelExtGameAccessConfResp) GetConfList() []*ChannelExtGameAccessConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type BatDelChannelExtGameAccessReq struct {
	ConfIdList           []uint32 `protobuf:"varint,1,rep,packed,name=conf_id_list,json=confIdList,proto3" json:"conf_id_list,omitempty"`
	OpId                 string   `protobuf:"bytes,2,opt,name=op_id,json=opId,proto3" json:"op_id,omitempty"`
	OpName               string   `protobuf:"bytes,3,opt,name=op_name,json=opName,proto3" json:"op_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelChannelExtGameAccessReq) Reset()         { *m = BatDelChannelExtGameAccessReq{} }
func (m *BatDelChannelExtGameAccessReq) String() string { return proto.CompactTextString(m) }
func (*BatDelChannelExtGameAccessReq) ProtoMessage()    {}
func (*BatDelChannelExtGameAccessReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{61}
}
func (m *BatDelChannelExtGameAccessReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelChannelExtGameAccessReq.Unmarshal(m, b)
}
func (m *BatDelChannelExtGameAccessReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelChannelExtGameAccessReq.Marshal(b, m, deterministic)
}
func (dst *BatDelChannelExtGameAccessReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelChannelExtGameAccessReq.Merge(dst, src)
}
func (m *BatDelChannelExtGameAccessReq) XXX_Size() int {
	return xxx_messageInfo_BatDelChannelExtGameAccessReq.Size(m)
}
func (m *BatDelChannelExtGameAccessReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelChannelExtGameAccessReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelChannelExtGameAccessReq proto.InternalMessageInfo

func (m *BatDelChannelExtGameAccessReq) GetConfIdList() []uint32 {
	if m != nil {
		return m.ConfIdList
	}
	return nil
}

func (m *BatDelChannelExtGameAccessReq) GetOpId() string {
	if m != nil {
		return m.OpId
	}
	return ""
}

func (m *BatDelChannelExtGameAccessReq) GetOpName() string {
	if m != nil {
		return m.OpName
	}
	return ""
}

type BatDelChannelExtGameAccessResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelChannelExtGameAccessResp) Reset()         { *m = BatDelChannelExtGameAccessResp{} }
func (m *BatDelChannelExtGameAccessResp) String() string { return proto.CompactTextString(m) }
func (*BatDelChannelExtGameAccessResp) ProtoMessage()    {}
func (*BatDelChannelExtGameAccessResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{62}
}
func (m *BatDelChannelExtGameAccessResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelChannelExtGameAccessResp.Unmarshal(m, b)
}
func (m *BatDelChannelExtGameAccessResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelChannelExtGameAccessResp.Marshal(b, m, deterministic)
}
func (dst *BatDelChannelExtGameAccessResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelChannelExtGameAccessResp.Merge(dst, src)
}
func (m *BatDelChannelExtGameAccessResp) XXX_Size() int {
	return xxx_messageInfo_BatDelChannelExtGameAccessResp.Size(m)
}
func (m *BatDelChannelExtGameAccessResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelChannelExtGameAccessResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelChannelExtGameAccessResp proto.InternalMessageInfo

type UpdateChannelExtGameAccessConfReq struct {
	ConfInfo             *ChannelExtGameAccessConf `protobuf:"bytes,1,opt,name=conf_info,json=confInfo,proto3" json:"conf_info,omitempty"`
	OpId                 string                    `protobuf:"bytes,2,opt,name=op_id,json=opId,proto3" json:"op_id,omitempty"`
	OpName               string                    `protobuf:"bytes,3,opt,name=op_name,json=opName,proto3" json:"op_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdateChannelExtGameAccessConfReq) Reset()         { *m = UpdateChannelExtGameAccessConfReq{} }
func (m *UpdateChannelExtGameAccessConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelExtGameAccessConfReq) ProtoMessage()    {}
func (*UpdateChannelExtGameAccessConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{63}
}
func (m *UpdateChannelExtGameAccessConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelExtGameAccessConfReq.Unmarshal(m, b)
}
func (m *UpdateChannelExtGameAccessConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelExtGameAccessConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelExtGameAccessConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelExtGameAccessConfReq.Merge(dst, src)
}
func (m *UpdateChannelExtGameAccessConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelExtGameAccessConfReq.Size(m)
}
func (m *UpdateChannelExtGameAccessConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelExtGameAccessConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelExtGameAccessConfReq proto.InternalMessageInfo

func (m *UpdateChannelExtGameAccessConfReq) GetConfInfo() *ChannelExtGameAccessConf {
	if m != nil {
		return m.ConfInfo
	}
	return nil
}

func (m *UpdateChannelExtGameAccessConfReq) GetOpId() string {
	if m != nil {
		return m.OpId
	}
	return ""
}

func (m *UpdateChannelExtGameAccessConfReq) GetOpName() string {
	if m != nil {
		return m.OpName
	}
	return ""
}

type UpdateChannelExtGameAccessConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelExtGameAccessConfResp) Reset()         { *m = UpdateChannelExtGameAccessConfResp{} }
func (m *UpdateChannelExtGameAccessConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelExtGameAccessConfResp) ProtoMessage()    {}
func (*UpdateChannelExtGameAccessConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_ext_game_2a06350e26d72787, []int{64}
}
func (m *UpdateChannelExtGameAccessConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelExtGameAccessConfResp.Unmarshal(m, b)
}
func (m *UpdateChannelExtGameAccessConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelExtGameAccessConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelExtGameAccessConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelExtGameAccessConfResp.Merge(dst, src)
}
func (m *UpdateChannelExtGameAccessConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelExtGameAccessConfResp.Size(m)
}
func (m *UpdateChannelExtGameAccessConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelExtGameAccessConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelExtGameAccessConfResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*CampButtonCfg)(nil), "revenue_ext_game.CampButtonCfg")
	proto.RegisterType((*ExtGameOpCfg)(nil), "revenue_ext_game.ExtGameOpCfg")
	proto.RegisterType((*ExtGameGiftCfg)(nil), "revenue_ext_game.ExtGameGiftCfg")
	proto.RegisterType((*ExtGameCfg)(nil), "revenue_ext_game.ExtGameCfg")
	proto.RegisterType((*SetExtGameOpCfgReq)(nil), "revenue_ext_game.SetExtGameOpCfgReq")
	proto.RegisterType((*SetExtGameOpCfgResp)(nil), "revenue_ext_game.SetExtGameOpCfgResp")
	proto.RegisterType((*ReportGameEndReq)(nil), "revenue_ext_game.ReportGameEndReq")
	proto.RegisterType((*ReportGameEndReq_RankInfo)(nil), "revenue_ext_game.ReportGameEndReq.RankInfo")
	proto.RegisterType((*ReportGameEndResp)(nil), "revenue_ext_game.ReportGameEndResp")
	proto.RegisterType((*SetUserCampReq)(nil), "revenue_ext_game.SetUserCampReq")
	proto.RegisterType((*SetUserCampResp)(nil), "revenue_ext_game.SetUserCampResp")
	proto.RegisterType((*GetUserExtGameInfoReq)(nil), "revenue_ext_game.GetUserExtGameInfoReq")
	proto.RegisterType((*GetUserExtGameInfoResp)(nil), "revenue_ext_game.GetUserExtGameInfoResp")
	proto.RegisterType((*GetExtGameCfgListReq)(nil), "revenue_ext_game.GetExtGameCfgListReq")
	proto.RegisterType((*GetExtGameCfgListResp)(nil), "revenue_ext_game.GetExtGameCfgListResp")
	proto.RegisterType((*MountExtGameReq)(nil), "revenue_ext_game.MountExtGameReq")
	proto.RegisterType((*MountExtGameResp)(nil), "revenue_ext_game.MountExtGameResp")
	proto.RegisterType((*GetMountExtGameReq)(nil), "revenue_ext_game.GetMountExtGameReq")
	proto.RegisterType((*GetMountExtGameResp)(nil), "revenue_ext_game.GetMountExtGameResp")
	proto.RegisterType((*BatchGetMountExtGameReq)(nil), "revenue_ext_game.BatchGetMountExtGameReq")
	proto.RegisterType((*BatchGetMountExtGameResp)(nil), "revenue_ext_game.BatchGetMountExtGameResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "revenue_ext_game.BatchGetMountExtGameResp.CidToGameEntry")
	proto.RegisterType((*UnmountExtGameReq)(nil), "revenue_ext_game.UnmountExtGameReq")
	proto.RegisterType((*UnmountExtGameResp)(nil), "revenue_ext_game.UnmountExtGameResp")
	proto.RegisterType((*GetChannelBySerialReq)(nil), "revenue_ext_game.GetChannelBySerialReq")
	proto.RegisterType((*GetChannelBySerialResp)(nil), "revenue_ext_game.GetChannelBySerialResp")
	proto.RegisterType((*StartDataReportReq)(nil), "revenue_ext_game.StartDataReportReq")
	proto.RegisterType((*StartDataReportResp)(nil), "revenue_ext_game.StartDataReportResp")
	proto.RegisterType((*StopDataReportReq)(nil), "revenue_ext_game.StopDataReportReq")
	proto.RegisterType((*StopDataReportResp)(nil), "revenue_ext_game.StopDataReportResp")
	proto.RegisterType((*GetDataReportTaskStatusReq)(nil), "revenue_ext_game.GetDataReportTaskStatusReq")
	proto.RegisterType((*GetDataReportTaskStatusResp)(nil), "revenue_ext_game.GetDataReportTaskStatusResp")
	proto.RegisterType((*GiftInfo)(nil), "revenue_ext_game.GiftInfo")
	proto.RegisterType((*ManualDataReportDemoReq)(nil), "revenue_ext_game.ManualDataReportDemoReq")
	proto.RegisterType((*ManualDataReportDemoResp)(nil), "revenue_ext_game.ManualDataReportDemoResp")
	proto.RegisterType((*ReportUserWantPlayReq)(nil), "revenue_ext_game.ReportUserWantPlayReq")
	proto.RegisterType((*ReportUserWantPlayResp)(nil), "revenue_ext_game.ReportUserWantPlayResp")
	proto.RegisterType((*GetChannelExtGameAccessStatusReq)(nil), "revenue_ext_game.GetChannelExtGameAccessStatusReq")
	proto.RegisterType((*GetChannelExtGameAccessStatusResp)(nil), "revenue_ext_game.GetChannelExtGameAccessStatusResp")
	proto.RegisterType((*SetGameScoresRankReq)(nil), "revenue_ext_game.SetGameScoresRankReq")
	proto.RegisterType((*SetGameScoresRankReq_RankInfo)(nil), "revenue_ext_game.SetGameScoresRankReq.RankInfo")
	proto.RegisterType((*SetGameScoresRankResp)(nil), "revenue_ext_game.SetGameScoresRankResp")
	proto.RegisterType((*UKWProfile)(nil), "revenue_ext_game.UKWProfile")
	proto.RegisterType((*UserRankInfo)(nil), "revenue_ext_game.UserRankInfo")
	proto.RegisterType((*GetExtGameScoreRankReq)(nil), "revenue_ext_game.GetExtGameScoreRankReq")
	proto.RegisterType((*GetExtGameScoreRankResp)(nil), "revenue_ext_game.GetExtGameScoreRankResp")
	proto.RegisterType((*UserRank)(nil), "revenue_ext_game.UserRank")
	proto.RegisterType((*GameRankHonorInfo)(nil), "revenue_ext_game.GameRankHonorInfo")
	proto.RegisterType((*GetExtGameRankHonorInfoReq)(nil), "revenue_ext_game.GetExtGameRankHonorInfoReq")
	proto.RegisterType((*GetExtGameRankHonorInfoResp)(nil), "revenue_ext_game.GetExtGameRankHonorInfoResp")
	proto.RegisterType((*GetExtGameRankNameplateReq)(nil), "revenue_ext_game.GetExtGameRankNameplateReq")
	proto.RegisterType((*GetExtGameRankNameplateResp)(nil), "revenue_ext_game.GetExtGameRankNameplateResp")
	proto.RegisterType((*ChatPkMem)(nil), "revenue_ext_game.ChatPkMem")
	proto.RegisterType((*StartChatPkReq)(nil), "revenue_ext_game.StartChatPkReq")
	proto.RegisterType((*StartChatPkResp)(nil), "revenue_ext_game.StartChatPkResp")
	proto.RegisterType((*StopChatPkReq)(nil), "revenue_ext_game.StopChatPkReq")
	proto.RegisterType((*StopChatPkResp)(nil), "revenue_ext_game.StopChatPkResp")
	proto.RegisterType((*ChatPkHeartbeatReq)(nil), "revenue_ext_game.ChatPkHeartbeatReq")
	proto.RegisterType((*ChatPkHeartbeatResp)(nil), "revenue_ext_game.ChatPkHeartbeatResp")
	proto.RegisterType((*ChannelExtGameAccessConf)(nil), "revenue_ext_game.ChannelExtGameAccessConf")
	proto.RegisterType((*BatSetChannelExtGameAccessConfReq)(nil), "revenue_ext_game.BatSetChannelExtGameAccessConfReq")
	proto.RegisterType((*BatSetChannelExtGameAccessConfResp)(nil), "revenue_ext_game.BatSetChannelExtGameAccessConfResp")
	proto.RegisterType((*GetChannelExtGameAccessConfReq)(nil), "revenue_ext_game.GetChannelExtGameAccessConfReq")
	proto.RegisterType((*GetChannelExtGameAccessConfResp)(nil), "revenue_ext_game.GetChannelExtGameAccessConfResp")
	proto.RegisterType((*BatDelChannelExtGameAccessReq)(nil), "revenue_ext_game.BatDelChannelExtGameAccessReq")
	proto.RegisterType((*BatDelChannelExtGameAccessResp)(nil), "revenue_ext_game.BatDelChannelExtGameAccessResp")
	proto.RegisterType((*UpdateChannelExtGameAccessConfReq)(nil), "revenue_ext_game.UpdateChannelExtGameAccessConfReq")
	proto.RegisterType((*UpdateChannelExtGameAccessConfResp)(nil), "revenue_ext_game.UpdateChannelExtGameAccessConfResp")
	proto.RegisterEnum("revenue_ext_game.ExtGameType", ExtGameType_name, ExtGameType_value)
	proto.RegisterEnum("revenue_ext_game.GameRankType", GameRankType_name, GameRankType_value)
	proto.RegisterEnum("revenue_ext_game.GetDataReportTaskStatusResp_Status", GetDataReportTaskStatusResp_Status_name, GetDataReportTaskStatusResp_Status_value)
	proto.RegisterEnum("revenue_ext_game.GetChannelExtGameAccessStatusResp_StreamType", GetChannelExtGameAccessStatusResp_StreamType_name, GetChannelExtGameAccessStatusResp_StreamType_value)
	proto.RegisterEnum("revenue_ext_game.GetExtGameRankNameplateReq_ShowType", GetExtGameRankNameplateReq_ShowType_name, GetExtGameRankNameplateReq_ShowType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RevenueExtGameClient is the client API for RevenueExtGame service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RevenueExtGameClient interface {
	// 游戏开始时设置操作区域配置
	SetExtGameOpCfg(ctx context.Context, in *SetExtGameOpCfgReq, opts ...grpc.CallOption) (*SetExtGameOpCfgResp, error)
	// 设置用户阵营
	SetUserCamp(ctx context.Context, in *SetUserCampReq, opts ...grpc.CallOption) (*SetUserCampResp, error)
	// 获取用户游戏信息
	GetUserExtGameInfo(ctx context.Context, in *GetUserExtGameInfoReq, opts ...grpc.CallOption) (*GetUserExtGameInfoResp, error)
	// 获取游戏配置列表
	GetExtGameCfgList(ctx context.Context, in *GetExtGameCfgListReq, opts ...grpc.CallOption) (*GetExtGameCfgListResp, error)
	// 启动小游戏挂载
	MountExtGame(ctx context.Context, in *MountExtGameReq, opts ...grpc.CallOption) (*MountExtGameResp, error)
	// 获取房间挂载的游戏
	GetMountExtGame(ctx context.Context, in *GetMountExtGameReq, opts ...grpc.CallOption) (*GetMountExtGameResp, error)
	// 批量获取房间挂载的游戏
	BatchGetMountExtGame(ctx context.Context, in *BatchGetMountExtGameReq, opts ...grpc.CallOption) (*BatchGetMountExtGameResp, error)
	// 停止小游戏挂载
	UnmountExtGame(ctx context.Context, in *UnmountExtGameReq, opts ...grpc.CallOption) (*UnmountExtGameResp, error)
	// 根据口令获取房间id
	GetChannelBySerial(ctx context.Context, in *GetChannelBySerialReq, opts ...grpc.CallOption) (*GetChannelBySerialResp, error)
	// 启动游戏数据推送
	StartDataReport(ctx context.Context, in *StartDataReportReq, opts ...grpc.CallOption) (*StartDataReportResp, error)
	// 停止游戏数据推送
	StopDataReport(ctx context.Context, in *StopDataReportReq, opts ...grpc.CallOption) (*StopDataReportResp, error)
	// 查询任务状态
	GetDataReportTaskStatus(ctx context.Context, in *GetDataReportTaskStatusReq, opts ...grpc.CallOption) (*GetDataReportTaskStatusResp, error)
	// “我想玩”上报
	ReportUserWantPlay(ctx context.Context, in *ReportUserWantPlayReq, opts ...grpc.CallOption) (*ReportUserWantPlayResp, error)
	// “游戏结束”上报
	ReportGameEnd(ctx context.Context, in *ReportGameEndReq, opts ...grpc.CallOption) (*ReportGameEndResp, error)
	// 任务数据手动推送（测试用）
	ManualDataReportDemo(ctx context.Context, in *ManualDataReportDemoReq, opts ...grpc.CallOption) (*ManualDataReportDemoResp, error)
	// 查询房间入口权限状态
	GetChannelExtGameAccessStatus(ctx context.Context, in *GetChannelExtGameAccessStatusReq, opts ...grpc.CallOption) (*GetChannelExtGameAccessStatusResp, error)
	// ============= 6.34.0 积分榜单 =============
	// 设置游戏榜单
	SetGameScoresRank(ctx context.Context, in *SetGameScoresRankReq, opts ...grpc.CallOption) (*SetGameScoresRankResp, error)
	// 获取游戏榜单
	GetExtGameScoreRank(ctx context.Context, in *GetExtGameScoreRankReq, opts ...grpc.CallOption) (*GetExtGameScoreRankResp, error)
	// 获取榜单荣誉信息
	GetExtGameRankHonorInfo(ctx context.Context, in *GetExtGameRankHonorInfoReq, opts ...grpc.CallOption) (*GetExtGameRankHonorInfoResp, error)
	// 获取用户榜单铭牌信息
	GetExtGameRankNameplate(ctx context.Context, in *GetExtGameRankNameplateReq, opts ...grpc.CallOption) (*GetExtGameRankNameplateResp, error)
	// ============= 6.34.0 连麦pk =============
	// 开始连麦pk
	StartChatPk(ctx context.Context, in *StartChatPkReq, opts ...grpc.CallOption) (*StartChatPkResp, error)
	// 结束连麦pk
	StopChatPk(ctx context.Context, in *StopChatPkReq, opts ...grpc.CallOption) (*StopChatPkResp, error)
	// 连麦pk心跳
	ChatPkHeartbeat(ctx context.Context, in *ChatPkHeartbeatReq, opts ...grpc.CallOption) (*ChatPkHeartbeatResp, error)
	// 批量发放直播间互动游戏入口权限
	BatSetChannelExtGameAccessConf(ctx context.Context, in *BatSetChannelExtGameAccessConfReq, opts ...grpc.CallOption) (*BatSetChannelExtGameAccessConfResp, error)
	// 分页获取直播间互动游戏入口权限信息
	GetChannelExtGameAccessConf(ctx context.Context, in *GetChannelExtGameAccessConfReq, opts ...grpc.CallOption) (*GetChannelExtGameAccessConfResp, error)
	// 批量回收直播间互动游戏入口权限
	BatDelChannelExtGameAccess(ctx context.Context, in *BatDelChannelExtGameAccessReq, opts ...grpc.CallOption) (*BatDelChannelExtGameAccessResp, error)
	// 修改直播间互动游戏入口权限信息
	UpdateChannelExtGameAccessConf(ctx context.Context, in *UpdateChannelExtGameAccessConfReq, opts ...grpc.CallOption) (*UpdateChannelExtGameAccessConfResp, error)
}

type revenueExtGameClient struct {
	cc *grpc.ClientConn
}

func NewRevenueExtGameClient(cc *grpc.ClientConn) RevenueExtGameClient {
	return &revenueExtGameClient{cc}
}

func (c *revenueExtGameClient) SetExtGameOpCfg(ctx context.Context, in *SetExtGameOpCfgReq, opts ...grpc.CallOption) (*SetExtGameOpCfgResp, error) {
	out := new(SetExtGameOpCfgResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/SetExtGameOpCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) SetUserCamp(ctx context.Context, in *SetUserCampReq, opts ...grpc.CallOption) (*SetUserCampResp, error) {
	out := new(SetUserCampResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/SetUserCamp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) GetUserExtGameInfo(ctx context.Context, in *GetUserExtGameInfoReq, opts ...grpc.CallOption) (*GetUserExtGameInfoResp, error) {
	out := new(GetUserExtGameInfoResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/GetUserExtGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) GetExtGameCfgList(ctx context.Context, in *GetExtGameCfgListReq, opts ...grpc.CallOption) (*GetExtGameCfgListResp, error) {
	out := new(GetExtGameCfgListResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/GetExtGameCfgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) MountExtGame(ctx context.Context, in *MountExtGameReq, opts ...grpc.CallOption) (*MountExtGameResp, error) {
	out := new(MountExtGameResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/MountExtGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) GetMountExtGame(ctx context.Context, in *GetMountExtGameReq, opts ...grpc.CallOption) (*GetMountExtGameResp, error) {
	out := new(GetMountExtGameResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/GetMountExtGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) BatchGetMountExtGame(ctx context.Context, in *BatchGetMountExtGameReq, opts ...grpc.CallOption) (*BatchGetMountExtGameResp, error) {
	out := new(BatchGetMountExtGameResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/BatchGetMountExtGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) UnmountExtGame(ctx context.Context, in *UnmountExtGameReq, opts ...grpc.CallOption) (*UnmountExtGameResp, error) {
	out := new(UnmountExtGameResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/UnmountExtGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) GetChannelBySerial(ctx context.Context, in *GetChannelBySerialReq, opts ...grpc.CallOption) (*GetChannelBySerialResp, error) {
	out := new(GetChannelBySerialResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/GetChannelBySerial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) StartDataReport(ctx context.Context, in *StartDataReportReq, opts ...grpc.CallOption) (*StartDataReportResp, error) {
	out := new(StartDataReportResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/StartDataReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) StopDataReport(ctx context.Context, in *StopDataReportReq, opts ...grpc.CallOption) (*StopDataReportResp, error) {
	out := new(StopDataReportResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/StopDataReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) GetDataReportTaskStatus(ctx context.Context, in *GetDataReportTaskStatusReq, opts ...grpc.CallOption) (*GetDataReportTaskStatusResp, error) {
	out := new(GetDataReportTaskStatusResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/GetDataReportTaskStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) ReportUserWantPlay(ctx context.Context, in *ReportUserWantPlayReq, opts ...grpc.CallOption) (*ReportUserWantPlayResp, error) {
	out := new(ReportUserWantPlayResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/ReportUserWantPlay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) ReportGameEnd(ctx context.Context, in *ReportGameEndReq, opts ...grpc.CallOption) (*ReportGameEndResp, error) {
	out := new(ReportGameEndResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/ReportGameEnd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) ManualDataReportDemo(ctx context.Context, in *ManualDataReportDemoReq, opts ...grpc.CallOption) (*ManualDataReportDemoResp, error) {
	out := new(ManualDataReportDemoResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/ManualDataReportDemo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) GetChannelExtGameAccessStatus(ctx context.Context, in *GetChannelExtGameAccessStatusReq, opts ...grpc.CallOption) (*GetChannelExtGameAccessStatusResp, error) {
	out := new(GetChannelExtGameAccessStatusResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/GetChannelExtGameAccessStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) SetGameScoresRank(ctx context.Context, in *SetGameScoresRankReq, opts ...grpc.CallOption) (*SetGameScoresRankResp, error) {
	out := new(SetGameScoresRankResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/SetGameScoresRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) GetExtGameScoreRank(ctx context.Context, in *GetExtGameScoreRankReq, opts ...grpc.CallOption) (*GetExtGameScoreRankResp, error) {
	out := new(GetExtGameScoreRankResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/GetExtGameScoreRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) GetExtGameRankHonorInfo(ctx context.Context, in *GetExtGameRankHonorInfoReq, opts ...grpc.CallOption) (*GetExtGameRankHonorInfoResp, error) {
	out := new(GetExtGameRankHonorInfoResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/GetExtGameRankHonorInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) GetExtGameRankNameplate(ctx context.Context, in *GetExtGameRankNameplateReq, opts ...grpc.CallOption) (*GetExtGameRankNameplateResp, error) {
	out := new(GetExtGameRankNameplateResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/GetExtGameRankNameplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) StartChatPk(ctx context.Context, in *StartChatPkReq, opts ...grpc.CallOption) (*StartChatPkResp, error) {
	out := new(StartChatPkResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/StartChatPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) StopChatPk(ctx context.Context, in *StopChatPkReq, opts ...grpc.CallOption) (*StopChatPkResp, error) {
	out := new(StopChatPkResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/StopChatPk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) ChatPkHeartbeat(ctx context.Context, in *ChatPkHeartbeatReq, opts ...grpc.CallOption) (*ChatPkHeartbeatResp, error) {
	out := new(ChatPkHeartbeatResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/ChatPkHeartbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) BatSetChannelExtGameAccessConf(ctx context.Context, in *BatSetChannelExtGameAccessConfReq, opts ...grpc.CallOption) (*BatSetChannelExtGameAccessConfResp, error) {
	out := new(BatSetChannelExtGameAccessConfResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/BatSetChannelExtGameAccessConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) GetChannelExtGameAccessConf(ctx context.Context, in *GetChannelExtGameAccessConfReq, opts ...grpc.CallOption) (*GetChannelExtGameAccessConfResp, error) {
	out := new(GetChannelExtGameAccessConfResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/GetChannelExtGameAccessConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) BatDelChannelExtGameAccess(ctx context.Context, in *BatDelChannelExtGameAccessReq, opts ...grpc.CallOption) (*BatDelChannelExtGameAccessResp, error) {
	out := new(BatDelChannelExtGameAccessResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/BatDelChannelExtGameAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueExtGameClient) UpdateChannelExtGameAccessConf(ctx context.Context, in *UpdateChannelExtGameAccessConfReq, opts ...grpc.CallOption) (*UpdateChannelExtGameAccessConfResp, error) {
	out := new(UpdateChannelExtGameAccessConfResp)
	err := c.cc.Invoke(ctx, "/revenue_ext_game.RevenueExtGame/UpdateChannelExtGameAccessConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RevenueExtGameServer is the server API for RevenueExtGame service.
type RevenueExtGameServer interface {
	// 游戏开始时设置操作区域配置
	SetExtGameOpCfg(context.Context, *SetExtGameOpCfgReq) (*SetExtGameOpCfgResp, error)
	// 设置用户阵营
	SetUserCamp(context.Context, *SetUserCampReq) (*SetUserCampResp, error)
	// 获取用户游戏信息
	GetUserExtGameInfo(context.Context, *GetUserExtGameInfoReq) (*GetUserExtGameInfoResp, error)
	// 获取游戏配置列表
	GetExtGameCfgList(context.Context, *GetExtGameCfgListReq) (*GetExtGameCfgListResp, error)
	// 启动小游戏挂载
	MountExtGame(context.Context, *MountExtGameReq) (*MountExtGameResp, error)
	// 获取房间挂载的游戏
	GetMountExtGame(context.Context, *GetMountExtGameReq) (*GetMountExtGameResp, error)
	// 批量获取房间挂载的游戏
	BatchGetMountExtGame(context.Context, *BatchGetMountExtGameReq) (*BatchGetMountExtGameResp, error)
	// 停止小游戏挂载
	UnmountExtGame(context.Context, *UnmountExtGameReq) (*UnmountExtGameResp, error)
	// 根据口令获取房间id
	GetChannelBySerial(context.Context, *GetChannelBySerialReq) (*GetChannelBySerialResp, error)
	// 启动游戏数据推送
	StartDataReport(context.Context, *StartDataReportReq) (*StartDataReportResp, error)
	// 停止游戏数据推送
	StopDataReport(context.Context, *StopDataReportReq) (*StopDataReportResp, error)
	// 查询任务状态
	GetDataReportTaskStatus(context.Context, *GetDataReportTaskStatusReq) (*GetDataReportTaskStatusResp, error)
	// “我想玩”上报
	ReportUserWantPlay(context.Context, *ReportUserWantPlayReq) (*ReportUserWantPlayResp, error)
	// “游戏结束”上报
	ReportGameEnd(context.Context, *ReportGameEndReq) (*ReportGameEndResp, error)
	// 任务数据手动推送（测试用）
	ManualDataReportDemo(context.Context, *ManualDataReportDemoReq) (*ManualDataReportDemoResp, error)
	// 查询房间入口权限状态
	GetChannelExtGameAccessStatus(context.Context, *GetChannelExtGameAccessStatusReq) (*GetChannelExtGameAccessStatusResp, error)
	// ============= 6.34.0 积分榜单 =============
	// 设置游戏榜单
	SetGameScoresRank(context.Context, *SetGameScoresRankReq) (*SetGameScoresRankResp, error)
	// 获取游戏榜单
	GetExtGameScoreRank(context.Context, *GetExtGameScoreRankReq) (*GetExtGameScoreRankResp, error)
	// 获取榜单荣誉信息
	GetExtGameRankHonorInfo(context.Context, *GetExtGameRankHonorInfoReq) (*GetExtGameRankHonorInfoResp, error)
	// 获取用户榜单铭牌信息
	GetExtGameRankNameplate(context.Context, *GetExtGameRankNameplateReq) (*GetExtGameRankNameplateResp, error)
	// ============= 6.34.0 连麦pk =============
	// 开始连麦pk
	StartChatPk(context.Context, *StartChatPkReq) (*StartChatPkResp, error)
	// 结束连麦pk
	StopChatPk(context.Context, *StopChatPkReq) (*StopChatPkResp, error)
	// 连麦pk心跳
	ChatPkHeartbeat(context.Context, *ChatPkHeartbeatReq) (*ChatPkHeartbeatResp, error)
	// 批量发放直播间互动游戏入口权限
	BatSetChannelExtGameAccessConf(context.Context, *BatSetChannelExtGameAccessConfReq) (*BatSetChannelExtGameAccessConfResp, error)
	// 分页获取直播间互动游戏入口权限信息
	GetChannelExtGameAccessConf(context.Context, *GetChannelExtGameAccessConfReq) (*GetChannelExtGameAccessConfResp, error)
	// 批量回收直播间互动游戏入口权限
	BatDelChannelExtGameAccess(context.Context, *BatDelChannelExtGameAccessReq) (*BatDelChannelExtGameAccessResp, error)
	// 修改直播间互动游戏入口权限信息
	UpdateChannelExtGameAccessConf(context.Context, *UpdateChannelExtGameAccessConfReq) (*UpdateChannelExtGameAccessConfResp, error)
}

func RegisterRevenueExtGameServer(s *grpc.Server, srv RevenueExtGameServer) {
	s.RegisterService(&_RevenueExtGame_serviceDesc, srv)
}

func _RevenueExtGame_SetExtGameOpCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetExtGameOpCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).SetExtGameOpCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/SetExtGameOpCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).SetExtGameOpCfg(ctx, req.(*SetExtGameOpCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_SetUserCamp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserCampReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).SetUserCamp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/SetUserCamp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).SetUserCamp(ctx, req.(*SetUserCampReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_GetUserExtGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserExtGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).GetUserExtGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/GetUserExtGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).GetUserExtGameInfo(ctx, req.(*GetUserExtGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_GetExtGameCfgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtGameCfgListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).GetExtGameCfgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/GetExtGameCfgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).GetExtGameCfgList(ctx, req.(*GetExtGameCfgListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_MountExtGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MountExtGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).MountExtGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/MountExtGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).MountExtGame(ctx, req.(*MountExtGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_GetMountExtGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMountExtGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).GetMountExtGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/GetMountExtGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).GetMountExtGame(ctx, req.(*GetMountExtGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_BatchGetMountExtGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetMountExtGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).BatchGetMountExtGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/BatchGetMountExtGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).BatchGetMountExtGame(ctx, req.(*BatchGetMountExtGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_UnmountExtGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnmountExtGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).UnmountExtGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/UnmountExtGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).UnmountExtGame(ctx, req.(*UnmountExtGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_GetChannelBySerial_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelBySerialReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).GetChannelBySerial(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/GetChannelBySerial",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).GetChannelBySerial(ctx, req.(*GetChannelBySerialReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_StartDataReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartDataReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).StartDataReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/StartDataReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).StartDataReport(ctx, req.(*StartDataReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_StopDataReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopDataReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).StopDataReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/StopDataReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).StopDataReport(ctx, req.(*StopDataReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_GetDataReportTaskStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataReportTaskStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).GetDataReportTaskStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/GetDataReportTaskStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).GetDataReportTaskStatus(ctx, req.(*GetDataReportTaskStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_ReportUserWantPlay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportUserWantPlayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).ReportUserWantPlay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/ReportUserWantPlay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).ReportUserWantPlay(ctx, req.(*ReportUserWantPlayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_ReportGameEnd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportGameEndReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).ReportGameEnd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/ReportGameEnd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).ReportGameEnd(ctx, req.(*ReportGameEndReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_ManualDataReportDemo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManualDataReportDemoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).ManualDataReportDemo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/ManualDataReportDemo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).ManualDataReportDemo(ctx, req.(*ManualDataReportDemoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_GetChannelExtGameAccessStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelExtGameAccessStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).GetChannelExtGameAccessStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/GetChannelExtGameAccessStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).GetChannelExtGameAccessStatus(ctx, req.(*GetChannelExtGameAccessStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_SetGameScoresRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGameScoresRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).SetGameScoresRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/SetGameScoresRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).SetGameScoresRank(ctx, req.(*SetGameScoresRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_GetExtGameScoreRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtGameScoreRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).GetExtGameScoreRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/GetExtGameScoreRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).GetExtGameScoreRank(ctx, req.(*GetExtGameScoreRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_GetExtGameRankHonorInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtGameRankHonorInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).GetExtGameRankHonorInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/GetExtGameRankHonorInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).GetExtGameRankHonorInfo(ctx, req.(*GetExtGameRankHonorInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_GetExtGameRankNameplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtGameRankNameplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).GetExtGameRankNameplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/GetExtGameRankNameplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).GetExtGameRankNameplate(ctx, req.(*GetExtGameRankNameplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_StartChatPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartChatPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).StartChatPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/StartChatPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).StartChatPk(ctx, req.(*StartChatPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_StopChatPk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopChatPkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).StopChatPk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/StopChatPk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).StopChatPk(ctx, req.(*StopChatPkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_ChatPkHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatPkHeartbeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).ChatPkHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/ChatPkHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).ChatPkHeartbeat(ctx, req.(*ChatPkHeartbeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_BatSetChannelExtGameAccessConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatSetChannelExtGameAccessConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).BatSetChannelExtGameAccessConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/BatSetChannelExtGameAccessConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).BatSetChannelExtGameAccessConf(ctx, req.(*BatSetChannelExtGameAccessConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_GetChannelExtGameAccessConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelExtGameAccessConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).GetChannelExtGameAccessConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/GetChannelExtGameAccessConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).GetChannelExtGameAccessConf(ctx, req.(*GetChannelExtGameAccessConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_BatDelChannelExtGameAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatDelChannelExtGameAccessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).BatDelChannelExtGameAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/BatDelChannelExtGameAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).BatDelChannelExtGameAccess(ctx, req.(*BatDelChannelExtGameAccessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueExtGame_UpdateChannelExtGameAccessConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChannelExtGameAccessConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueExtGameServer).UpdateChannelExtGameAccessConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenue_ext_game.RevenueExtGame/UpdateChannelExtGameAccessConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueExtGameServer).UpdateChannelExtGameAccessConf(ctx, req.(*UpdateChannelExtGameAccessConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RevenueExtGame_serviceDesc = grpc.ServiceDesc{
	ServiceName: "revenue_ext_game.RevenueExtGame",
	HandlerType: (*RevenueExtGameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetExtGameOpCfg",
			Handler:    _RevenueExtGame_SetExtGameOpCfg_Handler,
		},
		{
			MethodName: "SetUserCamp",
			Handler:    _RevenueExtGame_SetUserCamp_Handler,
		},
		{
			MethodName: "GetUserExtGameInfo",
			Handler:    _RevenueExtGame_GetUserExtGameInfo_Handler,
		},
		{
			MethodName: "GetExtGameCfgList",
			Handler:    _RevenueExtGame_GetExtGameCfgList_Handler,
		},
		{
			MethodName: "MountExtGame",
			Handler:    _RevenueExtGame_MountExtGame_Handler,
		},
		{
			MethodName: "GetMountExtGame",
			Handler:    _RevenueExtGame_GetMountExtGame_Handler,
		},
		{
			MethodName: "BatchGetMountExtGame",
			Handler:    _RevenueExtGame_BatchGetMountExtGame_Handler,
		},
		{
			MethodName: "UnmountExtGame",
			Handler:    _RevenueExtGame_UnmountExtGame_Handler,
		},
		{
			MethodName: "GetChannelBySerial",
			Handler:    _RevenueExtGame_GetChannelBySerial_Handler,
		},
		{
			MethodName: "StartDataReport",
			Handler:    _RevenueExtGame_StartDataReport_Handler,
		},
		{
			MethodName: "StopDataReport",
			Handler:    _RevenueExtGame_StopDataReport_Handler,
		},
		{
			MethodName: "GetDataReportTaskStatus",
			Handler:    _RevenueExtGame_GetDataReportTaskStatus_Handler,
		},
		{
			MethodName: "ReportUserWantPlay",
			Handler:    _RevenueExtGame_ReportUserWantPlay_Handler,
		},
		{
			MethodName: "ReportGameEnd",
			Handler:    _RevenueExtGame_ReportGameEnd_Handler,
		},
		{
			MethodName: "ManualDataReportDemo",
			Handler:    _RevenueExtGame_ManualDataReportDemo_Handler,
		},
		{
			MethodName: "GetChannelExtGameAccessStatus",
			Handler:    _RevenueExtGame_GetChannelExtGameAccessStatus_Handler,
		},
		{
			MethodName: "SetGameScoresRank",
			Handler:    _RevenueExtGame_SetGameScoresRank_Handler,
		},
		{
			MethodName: "GetExtGameScoreRank",
			Handler:    _RevenueExtGame_GetExtGameScoreRank_Handler,
		},
		{
			MethodName: "GetExtGameRankHonorInfo",
			Handler:    _RevenueExtGame_GetExtGameRankHonorInfo_Handler,
		},
		{
			MethodName: "GetExtGameRankNameplate",
			Handler:    _RevenueExtGame_GetExtGameRankNameplate_Handler,
		},
		{
			MethodName: "StartChatPk",
			Handler:    _RevenueExtGame_StartChatPk_Handler,
		},
		{
			MethodName: "StopChatPk",
			Handler:    _RevenueExtGame_StopChatPk_Handler,
		},
		{
			MethodName: "ChatPkHeartbeat",
			Handler:    _RevenueExtGame_ChatPkHeartbeat_Handler,
		},
		{
			MethodName: "BatSetChannelExtGameAccessConf",
			Handler:    _RevenueExtGame_BatSetChannelExtGameAccessConf_Handler,
		},
		{
			MethodName: "GetChannelExtGameAccessConf",
			Handler:    _RevenueExtGame_GetChannelExtGameAccessConf_Handler,
		},
		{
			MethodName: "BatDelChannelExtGameAccess",
			Handler:    _RevenueExtGame_BatDelChannelExtGameAccess_Handler,
		},
		{
			MethodName: "UpdateChannelExtGameAccessConf",
			Handler:    _RevenueExtGame_UpdateChannelExtGameAccessConf_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "revenue-ext-game/revenue-ext-game.proto",
}

func init() {
	proto.RegisterFile("revenue-ext-game/revenue-ext-game.proto", fileDescriptor_revenue_ext_game_2a06350e26d72787)
}

var fileDescriptor_revenue_ext_game_2a06350e26d72787 = []byte{
	// 3091 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3a, 0x4b, 0x6f, 0x1b, 0xd7,
	0xb9, 0x1a, 0x52, 0x0f, 0xf2, 0xd3, 0x8b, 0x3a, 0x92, 0x2c, 0x99, 0xba, 0xb2, 0xe4, 0x89, 0x93,
	0x38, 0xbe, 0xb1, 0x9c, 0x2b, 0x27, 0xf1, 0xcd, 0xab, 0x00, 0x4d, 0x31, 0x92, 0x10, 0x99, 0x56,
	0x86, 0x64, 0x14, 0x07, 0x68, 0x27, 0xe3, 0xe1, 0x21, 0x35, 0x25, 0x39, 0x33, 0xe2, 0x19, 0xda,
	0x52, 0x16, 0x05, 0x0a, 0xa4, 0x28, 0x50, 0xb4, 0x8b, 0x02, 0xed, 0xa6, 0xbb, 0xfe, 0x82, 0xa2,
	0x28, 0xd0, 0x45, 0x57, 0x2d, 0xd0, 0x5d, 0x77, 0x45, 0xfb, 0x1b, 0xfa, 0x33, 0x5a, 0x9c, 0xef,
	0xcc, 0x70, 0x1e, 0x3c, 0x24, 0x25, 0xbb, 0x69, 0xba, 0xe2, 0x9c, 0xef, 0xbc, 0xbe, 0xf7, 0xeb,
	0x10, 0x5e, 0xef, 0xd2, 0x67, 0xd4, 0xee, 0xd1, 0xbb, 0xf4, 0xdc, 0xbb, 0xdb, 0x34, 0x3a, 0xf4,
	0x5e, 0x12, 0xb0, 0xe3, 0x76, 0x1d, 0xcf, 0x21, 0x39, 0x1f, 0xae, 0xd3, 0x73, 0x4f, 0xe7, 0x70,
	0xd5, 0x85, 0xf9, 0xa2, 0xd1, 0x71, 0x1f, 0xf6, 0x3c, 0xcf, 0xb1, 0x8b, 0x8d, 0x26, 0xb9, 0x09,
	0x73, 0x4f, 0x71, 0xa0, 0x9b, 0x4e, 0xdb, 0xe9, 0xae, 0x2b, 0xdb, 0xca, 0xed, 0xac, 0x36, 0x2b,
	0x60, 0x45, 0x0e, 0x22, 0x5b, 0xe0, 0x0f, 0x75, 0x8f, 0x9e, 0x7b, 0xeb, 0x29, 0x5c, 0x01, 0x02,
	0x54, 0xa5, 0xe7, 0x1e, 0xd9, 0x80, 0xec, 0xf7, 0x1d, 0xcb, 0x9f, 0x4e, 0xe3, 0x74, 0x86, 0x03,
	0xf8, 0xa4, 0xfa, 0x23, 0x05, 0xe6, 0x4a, 0xe7, 0xde, 0xbe, 0xd1, 0xa1, 0x8f, 0x5d, 0x7e, 0xe3,
	0x21, 0xe4, 0x4c, 0xa3, 0xe3, 0xea, 0xfe, 0x99, 0x6d, 0x8b, 0x79, 0xeb, 0xca, 0x76, 0xfa, 0xf6,
	0xec, 0xee, 0xd6, 0x4e, 0x12, 0xdf, 0x9d, 0x18, 0xb2, 0xda, 0x82, 0xd9, 0x1f, 0x1e, 0x59, 0xcc,
	0x23, 0x77, 0x60, 0xe9, 0xac, 0x67, 0x99, 0x2d, 0xbd, 0x69, 0x35, 0x3c, 0x9d, 0xda, 0xc6, 0xd3,
	0x36, 0x45, 0xfc, 0x32, 0xda, 0x22, 0x4e, 0xec, 0x5b, 0x0d, 0xaf, 0x84, 0x60, 0xf5, 0x23, 0x58,
	0xf0, 0xd1, 0xe0, 0x40, 0x8e, 0xc8, 0x1a, 0xcc, 0xe0, 0x3e, 0xab, 0x8e, 0x54, 0xcf, 0x6b, 0xd3,
	0x7c, 0x78, 0x58, 0x27, 0x04, 0x26, 0xeb, 0x94, 0x99, 0x3e, 0xa5, 0xf8, 0xad, 0xfe, 0x3e, 0x05,
	0xe0, 0xef, 0xe7, 0x7b, 0xdf, 0x87, 0x2c, 0xc7, 0x4f, 0xf7, 0x2e, 0x5c, 0x8a, 0xbb, 0x17, 0x76,
	0x37, 0x07, 0xb1, 0xf7, 0x37, 0x54, 0x2f, 0x5c, 0xaa, 0x65, 0x9a, 0xfe, 0x17, 0x3f, 0xde, 0x36,
	0x3a, 0x34, 0x38, 0x9e, 0x7f, 0xf7, 0xaf, 0x4c, 0x87, 0x57, 0x72, 0xfc, 0x5c, 0xcb, 0xd4, 0x7b,
	0xdd, 0xf6, 0xfa, 0x24, 0x82, 0xa7, 0x5d, 0xcb, 0xac, 0x75, 0xdb, 0x7c, 0xc2, 0xec, 0x30, 0x9c,
	0x98, 0x12, 0x13, 0x66, 0x87, 0xf1, 0x89, 0x8f, 0x20, 0x8b, 0x14, 0x21, 0x4f, 0xa7, 0x91, 0xa7,
	0xdb, 0x43, 0xb1, 0xf2, 0xd9, 0xa0, 0x65, 0xf8, 0x16, 0x64, 0xe7, 0xbb, 0xb0, 0xd6, 0xa5, 0xcc,
	0xe9, 0x75, 0x4d, 0xaa, 0xd7, 0x2d, 0xe6, 0xb6, 0x8d, 0x8b, 0x80, 0xa9, 0x33, 0xc8, 0xd4, 0xd5,
	0x60, 0x7a, 0x4f, 0xcc, 0x0a, 0xd6, 0x72, 0xf9, 0x77, 0x0d, 0xbb, 0x25, 0x98, 0x91, 0x41, 0x56,
	0x66, 0x38, 0x80, 0x53, 0xab, 0xfe, 0x49, 0x01, 0x52, 0xa1, 0x5e, 0x54, 0x05, 0x34, 0x7a, 0xf6,
	0x52, 0x0c, 0xdc, 0x04, 0x30, 0x4f, 0x0d, 0xdb, 0xa6, 0x6d, 0x2e, 0xbb, 0x14, 0x5e, 0x98, 0xf5,
	0x21, 0x87, 0x75, 0x72, 0x1d, 0x32, 0x5d, 0xa7, 0x67, 0xd7, 0xf9, 0xa4, 0xe0, 0xe7, 0x0c, 0x8e,
	0x0f, 0xeb, 0xe4, 0x1d, 0x98, 0x76, 0x5c, 0xdd, 0x6c, 0x34, 0x91, 0xa3, 0xb3, 0xbb, 0x37, 0x86,
	0x5e, 0x29, 0x10, 0x9d, 0x72, 0xf8, 0x8f, 0xba, 0x0a, 0xcb, 0x03, 0x24, 0x30, 0x57, 0xfd, 0x75,
	0x0a, 0x72, 0x1a, 0x75, 0x9d, 0x2e, 0xc2, 0x4b, 0x76, 0xfd, 0xdb, 0x23, 0xec, 0x00, 0xb2, 0x96,
	0xdd, 0x70, 0x84, 0xe4, 0x27, 0x51, 0xf2, 0xff, 0x3b, 0x78, 0x6b, 0x12, 0xd9, 0x1d, 0xcd, 0xb0,
	0x5b, 0x87, 0x76, 0xc3, 0xd1, 0x32, 0x7c, 0x37, 0x57, 0x82, 0xfc, 0x23, 0xc8, 0x04, 0x50, 0x92,
	0x83, 0x74, 0xaf, 0x6f, 0x1d, 0xfc, 0x93, 0xa3, 0xd0, 0x30, 0x5a, 0x54, 0xef, 0xf5, 0xf1, 0x9b,
	0xe1, 0xe3, 0x9a, 0x55, 0x27, 0x2b, 0x30, 0xc5, 0x4c, 0xa7, 0x4b, 0x11, 0xb5, 0x49, 0x4d, 0x0c,
	0xd4, 0x65, 0x58, 0x4a, 0xdc, 0xca, 0x5c, 0xf5, 0x2f, 0x0a, 0x2c, 0x54, 0xa8, 0x57, 0x63, 0xb4,
	0xcb, 0x0d, 0xfc, 0xdb, 0x63, 0x9b, 0x4f, 0xe0, 0x64, 0x48, 0xe0, 0x26, 0x40, 0x97, 0x9a, 0x4e,
	0x5d, 0x90, 0x38, 0x25, 0xce, 0x12, 0x10, 0x4e, 0x24, 0x81, 0x49, 0xee, 0x83, 0xd6, 0xa7, 0x85,
	0x9d, 0xf2, 0x6f, 0x75, 0x09, 0x16, 0x63, 0xc4, 0x30, 0x57, 0x3d, 0x80, 0xd5, 0x7d, 0x01, 0xf2,
	0x31, 0x46, 0x26, 0xd3, 0x33, 0x09, 0x47, 0x47, 0x23, 0xaf, 0xfe, 0x59, 0x81, 0x6b, 0xb2, 0xa3,
	0x98, 0x1b, 0xa3, 0x4b, 0x89, 0xd3, 0xf5, 0x9e, 0xcf, 0x4d, 0xd3, 0xb1, 0x1b, 0x78, 0xe6, 0xec,
	0xee, 0xff, 0x0c, 0xe5, 0xa6, 0x70, 0x02, 0xfc, 0xc3, 0xb1, 0x1b, 0xe4, 0x01, 0xcc, 0x70, 0x13,
	0xe1, 0x1b, 0xd3, 0x97, 0xb2, 0x91, 0x69, 0xc7, 0xc5, 0x8d, 0x1b, 0x90, 0xed, 0x31, 0xda, 0xd5,
	0x91, 0x3f, 0xc2, 0x61, 0x65, 0x7a, 0x3e, 0x53, 0xd4, 0x7d, 0x58, 0xd9, 0xef, 0x5b, 0x50, 0xb1,
	0xd1, 0xe4, 0xaa, 0xf6, 0x42, 0xfc, 0xd0, 0x90, 0xb3, 0xc9, 0x83, 0x98, 0xcb, 0x49, 0xe6, 0x48,
	0x47, 0xe3, 0xc9, 0x18, 0x92, 0xf9, 0x72, 0xbe, 0x5d, 0xfd, 0x01, 0x2c, 0x3e, 0x72, 0x7a, 0x76,
	0x70, 0xea, 0x8b, 0xe0, 0x15, 0xd7, 0xdf, 0xf4, 0x95, 0xf4, 0x57, 0xbd, 0x03, 0xb9, 0xf8, 0xfd,
	0xcc, 0x25, 0xd7, 0x60, 0x9a, 0xd1, 0xae, 0x65, 0xb4, 0x7d, 0xd1, 0xfa, 0x23, 0xf5, 0x3e, 0x90,
	0x7d, 0xea, 0x25, 0xd1, 0x8d, 0x23, 0xa7, 0x24, 0x99, 0xf6, 0x29, 0x2c, 0x0f, 0x6c, 0x62, 0xee,
	0xcb, 0xd8, 0x9c, 0x5a, 0x80, 0xb5, 0x87, 0x86, 0x67, 0x9e, 0x4a, 0x90, 0x79, 0x0d, 0x16, 0x43,
	0x64, 0x42, 0x79, 0xcc, 0x6b, 0xf3, 0x7d, 0x8c, 0x90, 0xed, 0xbf, 0x53, 0x60, 0x5d, 0x7e, 0x06,
	0x73, 0xc9, 0x13, 0x98, 0x35, 0xad, 0xba, 0xee, 0x39, 0x88, 0x84, 0x2f, 0xd0, 0xf7, 0x06, 0xb1,
	0x1b, 0x76, 0xc0, 0x4e, 0xd1, 0xaa, 0x57, 0x1d, 0xe1, 0x74, 0xbc, 0xee, 0x85, 0x96, 0x35, 0x83,
	0x71, 0xfe, 0x43, 0x58, 0x88, 0x4f, 0x72, 0x69, 0xb7, 0xe8, 0x45, 0x20, 0xed, 0x16, 0xbd, 0xe0,
	0xce, 0xec, 0x99, 0xd1, 0xee, 0x51, 0x5f, 0xd0, 0x62, 0xf0, 0x7e, 0xea, 0xff, 0x15, 0xd5, 0x80,
	0xa5, 0x9a, 0xdd, 0x79, 0x59, 0x75, 0xd9, 0x48, 0xaa, 0xcb, 0x7c, 0x84, 0xb7, 0x2b, 0x40, 0x92,
	0x57, 0x30, 0x57, 0x6d, 0xa1, 0xe6, 0x17, 0xc5, 0x11, 0x0f, 0x2f, 0x2a, 0xa8, 0x0f, 0x2f, 0xeb,
	0x3a, 0x43, 0x35, 0x4b, 0xc5, 0xd4, 0xec, 0x01, 0x7a, 0x9d, 0x81, 0xcb, 0x98, 0x3b, 0x4e, 0xd5,
	0x7e, 0xc9, 0xc3, 0xbd, 0x67, 0x74, 0xbd, 0x3d, 0xc3, 0x33, 0x84, 0xe7, 0xff, 0x86, 0xdd, 0xfb,
	0x2d, 0x58, 0xf0, 0x0c, 0x26, 0xb2, 0x0f, 0xa1, 0x6d, 0xe9, 0xed, 0xf4, 0xed, 0xac, 0x36, 0xc7,
	0xa1, 0xfc, 0x00, 0x54, 0x36, 0x1e, 0xc2, 0x93, 0x68, 0x31, 0x57, 0xfd, 0x85, 0x02, 0x4b, 0x15,
	0xcf, 0x71, 0xff, 0xcb, 0xb0, 0x5d, 0xe1, 0x4c, 0x8c, 0x63, 0x25, 0x90, 0xcd, 0xef, 0xd3, 0x08,
	0x09, 0x55, 0x83, 0xb5, 0x2a, 0x9e, 0xe1, 0xf5, 0xd8, 0x37, 0x8c, 0xf5, 0x06, 0x64, 0xfb, 0x58,
	0x07, 0x19, 0x7e, 0x80, 0x30, 0x8f, 0xe6, 0x1b, 0x43, 0xd1, 0x62, 0x2e, 0x39, 0x82, 0x69, 0x86,
	0x23, 0x1f, 0xa9, 0xb7, 0x07, 0x91, 0x1a, 0xb1, 0x7d, 0xc7, 0xff, 0xf4, 0xcf, 0x50, 0xbf, 0x07,
	0xd3, 0x02, 0x42, 0xae, 0x01, 0xa9, 0x54, 0x0b, 0xd5, 0x5a, 0x45, 0xaf, 0x95, 0x2b, 0xc7, 0xa5,
	0xe2, 0xe1, 0xc7, 0x87, 0xa5, 0xbd, 0xdc, 0x04, 0x21, 0xb0, 0xe0, 0xc3, 0xb5, 0x5a, 0xb9, 0x7c,
	0x58, 0xde, 0xcf, 0x29, 0x11, 0xd8, 0x71, 0xa9, 0xbc, 0xc7, 0x61, 0x29, 0xb2, 0x02, 0x39, 0x1f,
	0x56, 0x7e, 0x5c, 0xd5, 0x4b, 0x9f, 0x1f, 0x56, 0xaa, 0xb9, 0xb4, 0x6a, 0x42, 0x86, 0x67, 0xc6,
	0x98, 0xff, 0x24, 0x2a, 0x84, 0x6c, 0xbf, 0x42, 0xd8, 0x04, 0xc0, 0x09, 0xb7, 0x6b, 0x99, 0x81,
	0x8f, 0xc0, 0xd4, 0xfb, 0x98, 0x03, 0x78, 0xc5, 0x84, 0xd3, 0x06, 0x1a, 0xb1, 0x6f, 0xdf, 0xb8,
	0xa3, 0x80, 0x10, 0xf5, 0x6f, 0x0a, 0xac, 0x3d, 0x32, 0xec, 0x9e, 0xd1, 0x0e, 0xc9, 0xde, 0xa3,
	0x1d, 0x4c, 0x11, 0xae, 0x43, 0xa6, 0xc3, 0x9a, 0xa1, 0x14, 0xb3, 0xda, 0x4c, 0x87, 0x35, 0x51,
	0x4a, 0x0f, 0xfc, 0xfc, 0x9e, 0x27, 0x6b, 0x7e, 0x58, 0xcf, 0x4b, 0x98, 0xe9, 0xa3, 0x2f, 0x32,
	0x7b, 0x24, 0x64, 0x0b, 0x66, 0xdd, 0xde, 0xd3, 0xb6, 0x65, 0x46, 0x6b, 0x34, 0x10, 0x20, 0x2c,
	0xe1, 0xe2, 0xf2, 0x9f, 0x4c, 0xca, 0xff, 0x55, 0x58, 0x30, 0x1d, 0xdb, 0xb3, 0xec, 0x9e, 0xd3,
	0x63, 0xba, 0xdd, 0xeb, 0xf8, 0x99, 0xd1, 0x7c, 0x08, 0x2d, 0xf7, 0x3a, 0x6a, 0x1e, 0xd6, 0xe5,
	0x54, 0x31, 0x57, 0xfd, 0x5a, 0x81, 0x55, 0x01, 0xe2, 0xb9, 0xcc, 0x89, 0x61, 0x7b, 0xc7, 0x6d,
	0xe3, 0xe2, 0x3f, 0x1e, 0x6b, 0xd7, 0xe1, 0x9a, 0x0c, 0x0b, 0xe6, 0xaa, 0x15, 0xd8, 0x0e, 0x5d,
	0x9e, 0xbf, 0xb9, 0x60, 0x9a, 0x94, 0xb1, 0xd0, 0xc4, 0x46, 0x3b, 0xbf, 0x80, 0x92, 0x54, 0x9f,
	0x12, 0xf5, 0xb7, 0x29, 0xb8, 0x39, 0xe6, 0x54, 0xe6, 0x92, 0x57, 0x60, 0xde, 0x40, 0x98, 0x1e,
	0x31, 0x94, 0x8c, 0x36, 0x67, 0x44, 0x16, 0x12, 0x1d, 0x66, 0x99, 0xd7, 0xa5, 0x46, 0x47, 0xd0,
	0x9d, 0x42, 0xba, 0xbf, 0x23, 0xb5, 0xa5, 0xd1, 0xd7, 0xed, 0x54, 0xf0, 0x18, 0x64, 0x0c, 0xb0,
	0xfe, 0x37, 0x27, 0xce, 0xbf, 0x80, 0x57, 0x96, 0x42, 0x47, 0xb2, 0x02, 0x52, 0xeb, 0xb6, 0xd5,
	0x26, 0x40, 0xb8, 0x91, 0x6c, 0xc0, 0x5a, 0xa5, 0xaa, 0x95, 0x0a, 0x8f, 0xf4, 0xea, 0x93, 0xe3,
	0x52, 0xc2, 0x02, 0x97, 0x61, 0x31, 0x3a, 0xa9, 0x55, 0x8b, 0x81, 0x09, 0x86, 0xc0, 0xa3, 0xfb,
	0xb9, 0x54, 0x72, 0x61, 0x71, 0xaf, 0x9c, 0x4b, 0xab, 0xbf, 0x49, 0xc1, 0x4a, 0x85, 0x22, 0xea,
	0x15, 0x5e, 0x43, 0x30, 0x5e, 0x8f, 0xbc, 0xac, 0x83, 0x8b, 0xd5, 0xa8, 0xa9, 0x78, 0x8d, 0xda,
	0x9f, 0xc4, 0xb2, 0xdc, 0x77, 0x6f, 0x1c, 0x50, 0xe6, 0xa5, 0xf9, 0xd1, 0x60, 0x69, 0x75, 0x6f,
	0xf0, 0x56, 0x19, 0xc2, 0xb2, 0xf2, 0xaa, 0x32, 0xb2, 0xbc, 0xf2, 0xab, 0x8f, 0x6e, 0x3d, 0x52,
	0x60, 0x65, 0x05, 0x84, 0x57, 0x1f, 0x3c, 0x5a, 0xe3, 0x05, 0x7e, 0x8d, 0xe5, 0x8f, 0xd4, 0x35,
	0x58, 0x95, 0xdc, 0xcf, 0x5c, 0xf5, 0x0f, 0x0a, 0x40, 0xed, 0x93, 0x93, 0xe3, 0xae, 0xd3, 0xb0,
	0xda, 0x54, 0x72, 0xe1, 0x3a, 0xcc, 0x18, 0xa6, 0x89, 0x5e, 0x4a, 0x24, 0x00, 0xc1, 0x90, 0xe4,
	0x21, 0x63, 0x5b, 0x66, 0x2b, 0xca, 0x92, 0x60, 0xcc, 0xb3, 0xa3, 0x36, 0x7d, 0x46, 0xdb, 0xbe,
	0xa3, 0x10, 0x03, 0x0e, 0xed, 0xd0, 0xba, 0x11, 0x34, 0x25, 0xc4, 0x80, 0x93, 0x74, 0x4a, 0x8d,
	0xba, 0xde, 0xe8, 0xf2, 0x93, 0x44, 0xdd, 0x94, 0xe5, 0x90, 0x8f, 0x39, 0x20, 0x56, 0x50, 0xce,
	0xc4, 0x0a, 0x4a, 0x9e, 0x1f, 0xce, 0x71, 0x2b, 0x1d, 0xc1, 0xaf, 0x90, 0x21, 0xa9, 0x28, 0x43,
	0x78, 0x99, 0xc6, 0xe5, 0xe7, 0x7b, 0x5e, 0xfc, 0x26, 0x0f, 0x20, 0xd3, 0x6b, 0x3d, 0x17, 0xbe,
	0x73, 0x72, 0x58, 0x49, 0x14, 0x32, 0x4b, 0x9b, 0xe9, 0xb5, 0x9e, 0xe3, 0xb5, 0x6f, 0x02, 0xe1,
	0x54, 0xbb, 0x6d, 0xc3, 0xa3, 0xdc, 0x34, 0x84, 0x26, 0x4c, 0x61, 0xd8, 0xce, 0xf5, 0x67, 0x6a,
	0xdd, 0x36, 0x86, 0xee, 0xbf, 0x8a, 0x82, 0xcd, 0xd7, 0x42, 0x94, 0x47, 0xa0, 0xbf, 0x57, 0x2a,
	0xa7, 0x57, 0x61, 0xda, 0x62, 0x7a, 0xaf, 0xf5, 0x1c, 0x89, 0xc8, 0x68, 0x53, 0x16, 0xab, 0xb5,
	0x9e, 0xc7, 0x13, 0xc7, 0xc9, 0x78, 0xe2, 0x18, 0x57, 0xf2, 0xa9, 0x84, 0x92, 0x6f, 0x02, 0xb8,
	0x46, 0x93, 0xea, 0x9e, 0xd3, 0xa2, 0x76, 0x20, 0x08, 0x0e, 0xa9, 0x72, 0x00, 0xdf, 0x8b, 0xd3,
	0xcc, 0xfa, 0x8a, 0xfa, 0x92, 0xc8, 0x70, 0x40, 0xc5, 0xfa, 0x8a, 0xaa, 0x7f, 0x4f, 0xc1, 0x9a,
	0x94, 0x28, 0xe6, 0x92, 0x0f, 0xfc, 0x4b, 0x23, 0x85, 0x97, 0xa4, 0x64, 0x8c, 0x0a, 0x52, 0x20,
	0x85, 0x2d, 0xa7, 0x0f, 0xfc, 0xa2, 0x11, 0xa5, 0x95, 0x1a, 0x56, 0x6f, 0xc6, 0x37, 0xf7, 0xfc,
	0x11, 0x2f, 0x34, 0x6c, 0xbe, 0x26, 0x42, 0x96, 0xd0, 0xd4, 0x79, 0x0e, 0x3e, 0x8e, 0x92, 0x16,
	0x9a, 0xf7, 0x64, 0xc2, 0xbc, 0x83, 0x49, 0x6c, 0xbf, 0x4d, 0x85, 0x93, 0x7b, 0x94, 0x99, 0xe4,
	0x1e, 0xac, 0xf4, 0x27, 0xf5, 0x53, 0xab, 0x79, 0xaa, 0xb7, 0xad, 0xe6, 0xa9, 0xe7, 0x73, 0x6f,
	0x29, 0x58, 0x77, 0x60, 0x35, 0x4f, 0x8f, 0xf8, 0x04, 0xb9, 0x0b, 0xcb, 0xb8, 0xc1, 0xef, 0xcf,
	0xe9, 0xac, 0xd7, 0x68, 0x58, 0xe7, 0xc8, 0xcf, 0xac, 0x96, 0xe3, 0x53, 0x45, 0x6c, 0xd5, 0x55,
	0x10, 0xae, 0xbe, 0x05, 0x99, 0x80, 0x36, 0x89, 0x76, 0x04, 0x5a, 0x9c, 0x0a, 0xb5, 0x58, 0xfd,
	0xa3, 0x02, 0x4b, 0x58, 0x12, 0x18, 0x76, 0xeb, 0xc0, 0xb1, 0x9d, 0x2e, 0xaa, 0xe8, 0x46, 0xd2,
	0x33, 0xce, 0x5f, 0xc1, 0xf5, 0xe1, 0xce, 0xa8, 0x9d, 0x73, 0x40, 0x8c, 0x37, 0x52, 0xc6, 0x3d,
	0x88, 0xfa, 0xc5, 0x29, 0x94, 0x7b, 0x7e, 0xb8, 0xe8, 0x42, 0x17, 0xa8, 0xb6, 0x30, 0x8b, 0x0d,
	0x4a, 0x9b, 0x28, 0x1d, 0xdc, 0x48, 0x5e, 0x9c, 0x94, 0x65, 0x98, 0xf2, 0x1c, 0x57, 0xb7, 0x03,
	0xab, 0xf7, 0x1c, 0xb7, 0xac, 0x7e, 0x86, 0xb9, 0xa9, 0xfc, 0x32, 0xe6, 0x92, 0x07, 0x30, 0x89,
	0x0e, 0x41, 0x41, 0xd5, 0x7b, 0x45, 0x12, 0x4d, 0x07, 0xb6, 0xe1, 0x06, 0xf5, 0x1f, 0x4a, 0x92,
	0x8a, 0x72, 0xe0, 0x09, 0xe4, 0xa6, 0xae, 0x41, 0x96, 0x9d, 0x3a, 0xcf, 0xa3, 0xc1, 0xfb, 0x1d,
	0x69, 0xf0, 0x1e, 0x72, 0xe4, 0x4e, 0xe5, 0xd4, 0x79, 0x2e, 0x82, 0x1a, 0xf3, 0xbf, 0x78, 0xae,
	0x1a, 0x40, 0xc9, 0x75, 0x58, 0xad, 0x1c, 0x3c, 0x3e, 0x91, 0x85, 0xe3, 0x2d, 0xd8, 0x08, 0xa7,
	0x8a, 0x07, 0x85, 0x72, 0xb9, 0x74, 0xa4, 0xd7, 0x2a, 0x25, 0x4d, 0x2f, 0x16, 0xb4, 0xbd, 0x9c,
	0x82, 0xc1, 0xbc, 0xbf, 0xe0, 0xb8, 0xa4, 0x55, 0x1e, 0x97, 0x0b, 0x47, 0xfa, 0x71, 0x61, 0xbf,
	0x94, 0x4b, 0xa9, 0xef, 0x27, 0x39, 0x18, 0xc1, 0x8a, 0xb9, 0x5c, 0x24, 0xa1, 0x1a, 0x28, 0xe8,
	0x14, 0x43, 0x51, 0x7f, 0x08, 0xd9, 0xe2, 0xa9, 0xe1, 0x1d, 0xb7, 0x1e, 0xd1, 0x0e, 0xe7, 0x89,
	0x19, 0xf2, 0xc4, 0x14, 0xe1, 0xae, 0x43, 0x3b, 0x7a, 0xdb, 0xb8, 0x70, 0x7a, 0x3c, 0x00, 0xa5,
	0x79, 0xb8, 0xeb, 0xd0, 0xce, 0x11, 0x02, 0xd4, 0x9f, 0x2a, 0xb0, 0x80, 0x45, 0x9b, 0x38, 0x63,
	0xac, 0x76, 0x2c, 0xc3, 0x94, 0xdb, 0x0a, 0x32, 0xc6, 0xac, 0x36, 0xe9, 0xb6, 0x44, 0x4e, 0x66,
	0xfa, 0x8d, 0x3f, 0xff, 0xd6, 0x77, 0x21, 0x83, 0xb7, 0x86, 0xf1, 0x7c, 0x43, 0xf2, 0xf0, 0x10,
	0xa0, 0xad, 0xcd, 0x70, 0x84, 0x38, 0x31, 0x4b, 0xb0, 0x18, 0xc3, 0x86, 0xb9, 0x6a, 0x01, 0xe6,
	0x79, 0x9d, 0xf6, 0x12, 0xf8, 0xa9, 0x39, 0x4e, 0x63, 0x78, 0x04, 0x73, 0xd5, 0x8f, 0x81, 0x88,
	0xd1, 0x01, 0x35, 0xba, 0xde, 0x53, 0x6a, 0x78, 0x2f, 0x76, 0xf2, 0x2a, 0x2c, 0x0f, 0x9c, 0xc3,
	0x5c, 0xf5, 0x9f, 0x0a, 0xac, 0xcb, 0x12, 0x44, 0x6c, 0xe2, 0xad, 0xc1, 0x0c, 0x76, 0xd1, 0xc2,
	0x37, 0x11, 0x3e, 0x3c, 0x1c, 0x9b, 0x92, 0xfb, 0xfa, 0x9e, 0x0e, 0xf5, 0xfd, 0x26, 0xcc, 0x05,
	0x1b, 0x22, 0xb1, 0x6a, 0xd6, 0x87, 0x05, 0x11, 0xe9, 0x29, 0x6d, 0x5a, 0xb6, 0xee, 0x59, 0x9d,
	0x20, 0x5e, 0x65, 0x11, 0x52, 0xb5, 0x44, 0x6a, 0x40, 0xed, 0xba, 0x98, 0x9c, 0x16, 0xc1, 0x91,
	0xda, 0x75, 0x9c, 0xda, 0x82, 0xd9, 0x9e, 0x5b, 0xe7, 0xf1, 0x18, 0x67, 0x45, 0xb8, 0x02, 0x01,
	0xc2, 0x05, 0x31, 0x6e, 0x65, 0x12, 0xfd, 0x95, 0x5f, 0x29, 0x70, 0xf3, 0xa1, 0xe1, 0x55, 0xe4,
	0x89, 0x32, 0xe7, 0x03, 0x67, 0xf8, 0x7e, 0xb4, 0xa1, 0x98, 0x46, 0x3d, 0xb9, 0x23, 0xd5, 0x13,
	0xf9, 0x09, 0xfd, 0xf6, 0x22, 0x17, 0x8e, 0xe3, 0x06, 0x65, 0x55, 0x56, 0x9b, 0x74, 0xdc, 0xc3,
	0x3a, 0x67, 0x34, 0xf7, 0x55, 0x86, 0x4f, 0x78, 0x56, 0x9b, 0x76, 0x5c, 0x6e, 0x58, 0xea, 0x13,
	0x50, 0xc7, 0xe1, 0xc6, 0x5c, 0x72, 0x1f, 0xae, 0x71, 0xca, 0xb1, 0x4f, 0xdb, 0xb6, 0x4c, 0x4f,
	0x37, 0xe3, 0xad, 0xb6, 0x65, 0x3e, 0x5b, 0xf4, 0x27, 0x8b, 0x96, 0x68, 0xb8, 0x7d, 0xad, 0xc0,
	0x8d, 0xfd, 0xd1, 0x44, 0x5f, 0xb9, 0x16, 0x0b, 0xb2, 0x8a, 0xb6, 0xd5, 0xb1, 0x82, 0x4a, 0x17,
	0x13, 0x89, 0x23, 0x0e, 0xe0, 0x21, 0x8c, 0x0f, 0x7c, 0xe9, 0xe3, 0xb7, 0xfa, 0x63, 0x05, 0xb6,
	0xf6, 0xc7, 0xd0, 0xb7, 0x01, 0x59, 0xcf, 0xf1, 0x8c, 0xb6, 0x6e, 0xda, 0x5e, 0xa0, 0xed, 0x08,
	0x28, 0xda, 0xde, 0xbf, 0x4d, 0x32, 0xaa, 0x03, 0x9b, 0x0f, 0x0d, 0x6f, 0x8f, 0xb6, 0x65, 0x6b,
	0x39, 0x3b, 0xb6, 0x61, 0xce, 0x37, 0x87, 0x28, 0x73, 0x41, 0xd8, 0x44, 0x5c, 0xb8, 0x29, 0xb9,
	0x70, 0xd3, 0x31, 0xe1, 0x6e, 0xc3, 0x8d, 0x51, 0x17, 0x32, 0x17, 0x75, 0xb3, 0x86, 0x7a, 0x7c,
	0x19, 0xdd, 0x8c, 0xc4, 0xae, 0x2b, 0x73, 0x00, 0x13, 0x87, 0xab, 0xa1, 0x7f, 0x0b, 0xd4, 0x71,
	0xb8, 0x31, 0xf7, 0xce, 0x19, 0xcc, 0x46, 0x6a, 0x30, 0xb2, 0x09, 0xd7, 0x4b, 0x9f, 0x57, 0xf5,
	0xfd, 0xc2, 0xa3, 0xd2, 0x90, 0xe0, 0x14, 0x9f, 0x2e, 0xd6, 0xaa, 0x3c, 0x08, 0x55, 0x2b, 0xfa,
	0x49, 0x41, 0xcb, 0x29, 0x44, 0x85, 0x1b, 0xf1, 0x05, 0x95, 0x83, 0x52, 0xe9, 0x58, 0x3f, 0x79,
	0x7c, 0xf4, 0x59, 0x49, 0xac, 0x49, 0xdd, 0x79, 0x02, 0x73, 0x41, 0x74, 0xc2, 0x3b, 0x6f, 0x40,
	0x1e, 0xd7, 0x6b, 0x85, 0xf2, 0x27, 0xb2, 0x4b, 0xdf, 0x80, 0x57, 0x13, 0xf3, 0x27, 0x8f, 0xb5,
	0xa3, 0x3d, 0x31, 0x3e, 0x2a, 0x54, 0xaa, 0x7a, 0x59, 0xdf, 0x2b, 0x3c, 0xa9, 0xe4, 0x94, 0xdd,
	0x9f, 0xaf, 0xc1, 0x82, 0x26, 0x38, 0xeb, 0x53, 0x45, 0xbe, 0xc4, 0x07, 0x9f, 0xd8, 0xa3, 0xf6,
	0x2d, 0x69, 0x45, 0x98, 0x78, 0xf4, 0xcc, 0xbf, 0x7a, 0x89, 0x55, 0xcc, 0x55, 0x27, 0x48, 0x15,
	0x66, 0x23, 0x4f, 0x4a, 0x64, 0x5b, 0xba, 0x2f, 0xf2, 0x7c, 0x96, 0xbf, 0x39, 0x66, 0x05, 0x9e,
	0x6a, 0xe1, 0xdb, 0x41, 0xe2, 0x29, 0x89, 0xbc, 0x2e, 0xcd, 0x42, 0x06, 0xdf, 0xae, 0xf2, 0xb7,
	0x2f, 0xb7, 0x10, 0xaf, 0x6a, 0xc0, 0xd2, 0xc0, 0x33, 0x0d, 0x79, 0x6d, 0x54, 0xbe, 0x13, 0x3e,
	0x0a, 0xe5, 0x5f, 0xbf, 0xd4, 0x3a, 0xbc, 0xe7, 0x04, 0xe6, 0xa2, 0x9d, 0x7f, 0x22, 0xe1, 0x43,
	0xe2, 0x79, 0x22, 0xaf, 0x8e, 0x5b, 0x82, 0x07, 0x7f, 0x09, 0x8b, 0x89, 0x57, 0x05, 0x99, 0x8c,
	0x07, 0x5f, 0x3f, 0x64, 0x32, 0x96, 0x3c, 0x4f, 0xa8, 0x13, 0xc4, 0x81, 0x15, 0xd9, 0xe3, 0x05,
	0x79, 0xe3, 0xb2, 0x8f, 0x1c, 0x67, 0xf9, 0x3b, 0x97, 0x7f, 0x0f, 0x51, 0x27, 0xc8, 0x77, 0x61,
	0x21, 0xfe, 0xac, 0x40, 0x24, 0xf9, 0xee, 0xc0, 0xdb, 0x46, 0xfe, 0xd6, 0xf8, 0x45, 0x11, 0xed,
	0x4a, 0x3c, 0x19, 0x0c, 0xd1, 0xae, 0xc1, 0x57, 0x8c, 0x21, 0xda, 0x25, 0x79, 0x81, 0x10, 0xc2,
	0x49, 0x34, 0xf3, 0xa5, 0x06, 0x38, 0xf0, 0x0c, 0x21, 0x35, 0x40, 0xc9, 0xab, 0x00, 0xf2, 0x2a,
	0xde, 0x80, 0x97, 0xf1, 0x6a, 0xe0, 0xe1, 0x20, 0x7f, 0x6b, 0xfc, 0x22, 0x3c, 0xfe, 0x1c, 0xcb,
	0x69, 0x59, 0xcb, 0x9b, 0xbc, 0x79, 0x85, 0xee, 0xf8, 0x59, 0xfe, 0xee, 0x95, 0x7a, 0xe9, 0x42,
	0x4a, 0x83, 0xfd, 0x4f, 0x99, 0x94, 0xa4, 0xbd, 0x5a, 0x99, 0x94, 0x86, 0xb4, 0x53, 0x27, 0xc8,
	0x17, 0x30, 0x1f, 0x7b, 0xfa, 0x27, 0xea, 0xf8, 0x7f, 0x24, 0xe4, 0x5f, 0x19, 0xbb, 0x26, 0x30,
	0x1e, 0x59, 0xa7, 0x59, 0x66, 0x3c, 0x43, 0xfa, 0xec, 0x32, 0xe3, 0x19, 0xda, 0xbc, 0x9e, 0x20,
	0x3f, 0x51, 0x60, 0x73, 0x64, 0x67, 0x95, 0xec, 0x5e, 0xb9, 0x15, 0x7b, 0x96, 0xbf, 0xff, 0x02,
	0xed, 0x5b, 0xe1, 0x5d, 0x07, 0xfa, 0x7d, 0x32, 0xef, 0x2a, 0x6b, 0x4a, 0xca, 0xbc, 0xab, 0xbc,
	0x79, 0x38, 0x41, 0xda, 0xf8, 0x6e, 0x9c, 0xec, 0xfa, 0x90, 0xdb, 0xa3, 0xfc, 0x73, 0xb4, 0xe3,
	0x95, 0x7f, 0xe3, 0x92, 0x2b, 0x23, 0x46, 0x21, 0x2b, 0xd5, 0x87, 0x18, 0xc5, 0x90, 0x16, 0xc2,
	0x10, 0xa3, 0x18, 0xd6, 0x03, 0x90, 0xdd, 0xdc, 0x2f, 0x71, 0xc7, 0xdf, 0x1c, 0xad, 0xd1, 0xc7,
	0xdf, 0x1c, 0xab, 0x9d, 0xfd, 0x40, 0x1f, 0xd6, 0x94, 0xd2, 0x40, 0x1f, 0x2b, 0x80, 0xa5, 0x81,
	0x3e, 0x51, 0x94, 0x4e, 0x90, 0x4f, 0x01, 0xc2, 0x9a, 0x92, 0x6c, 0xc9, 0x9d, 0x52, 0x78, 0xe6,
	0xf6, 0xe8, 0x05, 0x81, 0xcb, 0x4d, 0x14, 0x93, 0x32, 0x97, 0x3b, 0x58, 0xb7, 0xca, 0x5c, 0xae,
	0xac, 0x2a, 0x9d, 0x20, 0x3f, 0x53, 0x30, 0x39, 0x1e, 0x51, 0xf9, 0x90, 0xfb, 0xd2, 0x78, 0x37,
	0xba, 0x8e, 0xcb, 0xbf, 0x7d, 0xf5, 0x4d, 0x88, 0xcf, 0xd7, 0xe2, 0x59, 0x73, 0x28, 0x32, 0x6f,
	0x5d, 0xda, 0x76, 0x03, 0x4c, 0xfe, 0xef, 0x8a, 0x3b, 0x10, 0x8d, 0x1f, 0x2a, 0x90, 0x1f, 0x5e,
	0x33, 0x90, 0x7b, 0x52, 0xea, 0x86, 0x97, 0x34, 0xf9, 0xb7, 0xae, 0xb6, 0xa1, 0x2f, 0x9a, 0xd1,
	0x89, 0xbf, 0x4c, 0x34, 0x63, 0xcb, 0x18, 0x99, 0x68, 0xc6, 0xd7, 0x17, 0xea, 0xc4, 0xc3, 0xb7,
	0xbf, 0xd8, 0x6d, 0x3a, 0x6d, 0xc3, 0x6e, 0xee, 0xbc, 0xb3, 0xeb, 0x79, 0x3b, 0xa6, 0xd3, 0xb9,
	0x87, 0x7f, 0x78, 0x35, 0x9d, 0xf6, 0x3d, 0x46, 0xbb, 0xcf, 0x2c, 0x93, 0xb2, 0x81, 0xff, 0xc4,
	0x3e, 0x9d, 0xc6, 0x35, 0xf7, 0xff, 0x15, 0x00, 0x00, 0xff, 0xff, 0x3e, 0xe4, 0xf5, 0x54, 0x3f,
	0x2b, 0x00, 0x00,
}
