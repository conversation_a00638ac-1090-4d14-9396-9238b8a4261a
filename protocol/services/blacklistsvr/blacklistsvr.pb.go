// Code generated by protoc-gen-gogo.
// source: src/blacklistsvr/blacklistsvr.proto
// DO NOT EDIT!

/*
	Package blacklistsvr is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/blacklistsvr/blacklistsvr.proto

	It has these top-level messages:
		OperationRiskInfo
		GetRandomBlackUserReq
		GetRandomBlackUserResp
		IsBlackUserReq
		IsBlackUserResp
		BlackUserInfo
		RemBlackUserReq
		RemBlackUserResp
*/
package blacklistsvr

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type OperationRiskInfo struct {
	RiskLevel string `protobuf:"bytes,1,opt,name=riskLevel" json:"riskLevel"`
	Message   string `protobuf:"bytes,2,opt,name=message" json:"message"`
	Score     uint32 `protobuf:"varint,3,opt,name=score" json:"score"`
}

func (m *OperationRiskInfo) Reset()                    { *m = OperationRiskInfo{} }
func (m *OperationRiskInfo) String() string            { return proto.CompactTextString(m) }
func (*OperationRiskInfo) ProtoMessage()               {}
func (*OperationRiskInfo) Descriptor() ([]byte, []int) { return fileDescriptorBlacklistsvr, []int{0} }

func (m *OperationRiskInfo) GetRiskLevel() string {
	if m != nil {
		return m.RiskLevel
	}
	return ""
}

func (m *OperationRiskInfo) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *OperationRiskInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type GetRandomBlackUserReq struct {
	Count uint32 `protobuf:"varint,1,req,name=count" json:"count"`
}

func (m *GetRandomBlackUserReq) Reset()         { *m = GetRandomBlackUserReq{} }
func (m *GetRandomBlackUserReq) String() string { return proto.CompactTextString(m) }
func (*GetRandomBlackUserReq) ProtoMessage()    {}
func (*GetRandomBlackUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorBlacklistsvr, []int{1}
}

func (m *GetRandomBlackUserReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetRandomBlackUserResp struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *GetRandomBlackUserResp) Reset()         { *m = GetRandomBlackUserResp{} }
func (m *GetRandomBlackUserResp) String() string { return proto.CompactTextString(m) }
func (*GetRandomBlackUserResp) ProtoMessage()    {}
func (*GetRandomBlackUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorBlacklistsvr, []int{2}
}

func (m *GetRandomBlackUserResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type IsBlackUserReq struct {
}

func (m *IsBlackUserReq) Reset()                    { *m = IsBlackUserReq{} }
func (m *IsBlackUserReq) String() string            { return proto.CompactTextString(m) }
func (*IsBlackUserReq) ProtoMessage()               {}
func (*IsBlackUserReq) Descriptor() ([]byte, []int) { return fileDescriptorBlacklistsvr, []int{3} }

type IsBlackUserResp struct {
	IsBlackUser bool `protobuf:"varint,1,opt,name=is_black_user,json=isBlackUser" json:"is_black_user"`
}

func (m *IsBlackUserResp) Reset()                    { *m = IsBlackUserResp{} }
func (m *IsBlackUserResp) String() string            { return proto.CompactTextString(m) }
func (*IsBlackUserResp) ProtoMessage()               {}
func (*IsBlackUserResp) Descriptor() ([]byte, []int) { return fileDescriptorBlacklistsvr, []int{4} }

func (m *IsBlackUserResp) GetIsBlackUser() bool {
	if m != nil {
		return m.IsBlackUser
	}
	return false
}

type BlackUserInfo struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Sex uint32 `protobuf:"varint,2,req,name=sex" json:"sex"`
}

func (m *BlackUserInfo) Reset()                    { *m = BlackUserInfo{} }
func (m *BlackUserInfo) String() string            { return proto.CompactTextString(m) }
func (*BlackUserInfo) ProtoMessage()               {}
func (*BlackUserInfo) Descriptor() ([]byte, []int) { return fileDescriptorBlacklistsvr, []int{5} }

func (m *BlackUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BlackUserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type RemBlackUserReq struct {
	BlackUserList []uint32 `protobuf:"varint,1,rep,name=black_user_list,json=blackUserList" json:"black_user_list,omitempty"`
}

func (m *RemBlackUserReq) Reset()                    { *m = RemBlackUserReq{} }
func (m *RemBlackUserReq) String() string            { return proto.CompactTextString(m) }
func (*RemBlackUserReq) ProtoMessage()               {}
func (*RemBlackUserReq) Descriptor() ([]byte, []int) { return fileDescriptorBlacklistsvr, []int{6} }

func (m *RemBlackUserReq) GetBlackUserList() []uint32 {
	if m != nil {
		return m.BlackUserList
	}
	return nil
}

type RemBlackUserResp struct {
}

func (m *RemBlackUserResp) Reset()                    { *m = RemBlackUserResp{} }
func (m *RemBlackUserResp) String() string            { return proto.CompactTextString(m) }
func (*RemBlackUserResp) ProtoMessage()               {}
func (*RemBlackUserResp) Descriptor() ([]byte, []int) { return fileDescriptorBlacklistsvr, []int{7} }

func init() {
	proto.RegisterType((*OperationRiskInfo)(nil), "blacklistsvr.OperationRiskInfo")
	proto.RegisterType((*GetRandomBlackUserReq)(nil), "blacklistsvr.GetRandomBlackUserReq")
	proto.RegisterType((*GetRandomBlackUserResp)(nil), "blacklistsvr.GetRandomBlackUserResp")
	proto.RegisterType((*IsBlackUserReq)(nil), "blacklistsvr.IsBlackUserReq")
	proto.RegisterType((*IsBlackUserResp)(nil), "blacklistsvr.IsBlackUserResp")
	proto.RegisterType((*BlackUserInfo)(nil), "blacklistsvr.BlackUserInfo")
	proto.RegisterType((*RemBlackUserReq)(nil), "blacklistsvr.RemBlackUserReq")
	proto.RegisterType((*RemBlackUserResp)(nil), "blacklistsvr.RemBlackUserResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Blacklistsvr service

type BlacklistsvrClient interface {
	GetRandomBlackUser(ctx context.Context, in *GetRandomBlackUserReq, opts ...grpc.CallOption) (*GetRandomBlackUserResp, error)
	IsBlackUser(ctx context.Context, in *IsBlackUserReq, opts ...grpc.CallOption) (*IsBlackUserResp, error)
	RemBlackUser(ctx context.Context, in *RemBlackUserReq, opts ...grpc.CallOption) (*RemBlackUserResp, error)
}

type blacklistsvrClient struct {
	cc *grpc.ClientConn
}

func NewBlacklistsvrClient(cc *grpc.ClientConn) BlacklistsvrClient {
	return &blacklistsvrClient{cc}
}

func (c *blacklistsvrClient) GetRandomBlackUser(ctx context.Context, in *GetRandomBlackUserReq, opts ...grpc.CallOption) (*GetRandomBlackUserResp, error) {
	out := new(GetRandomBlackUserResp)
	err := grpc.Invoke(ctx, "/blacklistsvr.blacklistsvr/GetRandomBlackUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blacklistsvrClient) IsBlackUser(ctx context.Context, in *IsBlackUserReq, opts ...grpc.CallOption) (*IsBlackUserResp, error) {
	out := new(IsBlackUserResp)
	err := grpc.Invoke(ctx, "/blacklistsvr.blacklistsvr/IsBlackUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *blacklistsvrClient) RemBlackUser(ctx context.Context, in *RemBlackUserReq, opts ...grpc.CallOption) (*RemBlackUserResp, error) {
	out := new(RemBlackUserResp)
	err := grpc.Invoke(ctx, "/blacklistsvr.blacklistsvr/RemBlackUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Blacklistsvr service

type BlacklistsvrServer interface {
	GetRandomBlackUser(context.Context, *GetRandomBlackUserReq) (*GetRandomBlackUserResp, error)
	IsBlackUser(context.Context, *IsBlackUserReq) (*IsBlackUserResp, error)
	RemBlackUser(context.Context, *RemBlackUserReq) (*RemBlackUserResp, error)
}

func RegisterBlacklistsvrServer(s *grpc.Server, srv BlacklistsvrServer) {
	s.RegisterService(&_Blacklistsvr_serviceDesc, srv)
}

func _Blacklistsvr_GetRandomBlackUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRandomBlackUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlacklistsvrServer).GetRandomBlackUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/blacklistsvr.blacklistsvr/GetRandomBlackUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlacklistsvrServer).GetRandomBlackUser(ctx, req.(*GetRandomBlackUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Blacklistsvr_IsBlackUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsBlackUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlacklistsvrServer).IsBlackUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/blacklistsvr.blacklistsvr/IsBlackUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlacklistsvrServer).IsBlackUser(ctx, req.(*IsBlackUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Blacklistsvr_RemBlackUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemBlackUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BlacklistsvrServer).RemBlackUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/blacklistsvr.blacklistsvr/RemBlackUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BlacklistsvrServer).RemBlackUser(ctx, req.(*RemBlackUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Blacklistsvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "blacklistsvr.blacklistsvr",
	HandlerType: (*BlacklistsvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRandomBlackUser",
			Handler:    _Blacklistsvr_GetRandomBlackUser_Handler,
		},
		{
			MethodName: "IsBlackUser",
			Handler:    _Blacklistsvr_IsBlackUser_Handler,
		},
		{
			MethodName: "RemBlackUser",
			Handler:    _Blacklistsvr_RemBlackUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/blacklistsvr/blacklistsvr.proto",
}

func (m *OperationRiskInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OperationRiskInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintBlacklistsvr(dAtA, i, uint64(len(m.RiskLevel)))
	i += copy(dAtA[i:], m.RiskLevel)
	dAtA[i] = 0x12
	i++
	i = encodeVarintBlacklistsvr(dAtA, i, uint64(len(m.Message)))
	i += copy(dAtA[i:], m.Message)
	dAtA[i] = 0x18
	i++
	i = encodeVarintBlacklistsvr(dAtA, i, uint64(m.Score))
	return i, nil
}

func (m *GetRandomBlackUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRandomBlackUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBlacklistsvr(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetRandomBlackUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRandomBlackUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintBlacklistsvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *IsBlackUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IsBlackUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *IsBlackUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IsBlackUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsBlackUser {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *BlackUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BlackUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintBlacklistsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintBlacklistsvr(dAtA, i, uint64(m.Sex))
	return i, nil
}

func (m *RemBlackUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemBlackUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.BlackUserList) > 0 {
		for _, num := range m.BlackUserList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintBlacklistsvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *RemBlackUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemBlackUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Blacklistsvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Blacklistsvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintBlacklistsvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *OperationRiskInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.RiskLevel)
	n += 1 + l + sovBlacklistsvr(uint64(l))
	l = len(m.Message)
	n += 1 + l + sovBlacklistsvr(uint64(l))
	n += 1 + sovBlacklistsvr(uint64(m.Score))
	return n
}

func (m *GetRandomBlackUserReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBlacklistsvr(uint64(m.Count))
	return n
}

func (m *GetRandomBlackUserResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovBlacklistsvr(uint64(e))
		}
	}
	return n
}

func (m *IsBlackUserReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *IsBlackUserResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *BlackUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovBlacklistsvr(uint64(m.Uid))
	n += 1 + sovBlacklistsvr(uint64(m.Sex))
	return n
}

func (m *RemBlackUserReq) Size() (n int) {
	var l int
	_ = l
	if len(m.BlackUserList) > 0 {
		for _, e := range m.BlackUserList {
			n += 1 + sovBlacklistsvr(uint64(e))
		}
	}
	return n
}

func (m *RemBlackUserResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovBlacklistsvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozBlacklistsvr(x uint64) (n int) {
	return sovBlacklistsvr(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *OperationRiskInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBlacklistsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OperationRiskInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OperationRiskInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RiskLevel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBlacklistsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBlacklistsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RiskLevel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBlacklistsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBlacklistsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBlacklistsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBlacklistsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBlacklistsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRandomBlackUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBlacklistsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRandomBlackUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRandomBlackUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBlacklistsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipBlacklistsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBlacklistsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRandomBlackUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBlacklistsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRandomBlackUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRandomBlackUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBlacklistsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBlacklistsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBlacklistsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBlacklistsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBlacklistsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBlacklistsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IsBlackUserReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBlacklistsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IsBlackUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IsBlackUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBlacklistsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBlacklistsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IsBlackUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBlacklistsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IsBlackUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IsBlackUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsBlackUser", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBlacklistsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBlackUser = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBlacklistsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBlacklistsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BlackUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBlacklistsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BlackUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BlackUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBlacklistsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBlacklistsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipBlacklistsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBlacklistsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemBlackUserReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBlacklistsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemBlackUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemBlackUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBlacklistsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.BlackUserList = append(m.BlackUserList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBlacklistsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBlacklistsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBlacklistsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.BlackUserList = append(m.BlackUserList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field BlackUserList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBlacklistsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBlacklistsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemBlackUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBlacklistsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemBlackUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemBlackUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBlacklistsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBlacklistsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipBlacklistsvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowBlacklistsvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBlacklistsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBlacklistsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthBlacklistsvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowBlacklistsvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipBlacklistsvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthBlacklistsvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowBlacklistsvr   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/blacklistsvr/blacklistsvr.proto", fileDescriptorBlacklistsvr) }

var fileDescriptorBlacklistsvr = []byte{
	// 486 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x92, 0xc1, 0x6e, 0xd3, 0x40,
	0x14, 0x45, 0x3b, 0x76, 0xa1, 0xe9, 0x6b, 0x43, 0xc2, 0x48, 0x54, 0xae, 0x05, 0xae, 0x35, 0x2d,
	0x28, 0x9b, 0x36, 0x12, 0x5d, 0x61, 0xaa, 0x22, 0x65, 0x83, 0x2a, 0x55, 0x42, 0xb2, 0xc4, 0x3a,
	0x4a, 0xec, 0x01, 0x46, 0x76, 0xec, 0xc1, 0xcf, 0x13, 0x85, 0xae, 0x58, 0x21, 0xc4, 0x0a, 0xf1,
	0x0d, 0xd9, 0xf2, 0x1f, 0x5d, 0xf2, 0x05, 0x08, 0x85, 0x4d, 0xbe, 0x80, 0x35, 0x1a, 0xb7, 0x21,
	0x9e, 0x52, 0xb5, 0x4b, 0xdf, 0xf7, 0xee, 0x9c, 0xb9, 0x77, 0x0c, 0xbb, 0x58, 0x44, 0xdd, 0x61,
	0x3a, 0x88, 0x92, 0x54, 0x60, 0x89, 0xe3, 0xc2, 0xf8, 0x38, 0x90, 0x45, 0x5e, 0xe6, 0x74, 0xb3,
	0xae, 0xb9, 0x7b, 0x51, 0x3e, 0x1a, 0xe5, 0x59, 0xb7, 0x4c, 0xc7, 0x52, 0x44, 0x49, 0xca, 0xbb,
	0x98, 0x0c, 0x95, 0x48, 0x4b, 0x91, 0x95, 0x1f, 0x24, 0xbf, 0xf0, 0x30, 0x84, 0xfb, 0xaf, 0x24,
	0x2f, 0x06, 0xa5, 0xc8, 0xb3, 0x50, 0x60, 0x72, 0x92, 0xbd, 0xc9, 0x29, 0x83, 0xf5, 0x42, 0x60,
	0x72, 0xca, 0xc7, 0x3c, 0x75, 0x88, 0x4f, 0x3a, 0xeb, 0xbd, 0xd5, 0xf3, 0x9f, 0x3b, 0x2b, 0xe1,
	0x52, 0xa6, 0x1e, 0xac, 0x8d, 0x38, 0xe2, 0xe0, 0x2d, 0x77, 0xac, 0xda, 0xc6, 0x42, 0xa4, 0x2e,
	0xdc, 0xc1, 0x28, 0x2f, 0xb8, 0x63, 0xfb, 0xa4, 0xd3, 0xbc, 0x9c, 0x5e, 0x48, 0xec, 0x10, 0x1e,
	0xbc, 0xe4, 0x65, 0x38, 0xc8, 0xe2, 0x7c, 0xd4, 0xd3, 0x77, 0x7e, 0x8d, 0xbc, 0x08, 0xf9, 0x7b,
	0x6d, 0x8a, 0x72, 0x95, 0x95, 0x0e, 0xf1, 0xad, 0xa5, 0xa9, 0x92, 0xd8, 0x21, 0x6c, 0x5d, 0x67,
	0x42, 0x49, 0xb7, 0xa1, 0xa1, 0x44, 0xdc, 0xd7, 0xc1, 0x1d, 0xe2, 0xdb, 0x9d, 0x66, 0xb8, 0xa6,
	0x44, 0x7c, 0x2a, 0xb0, 0x64, 0x6d, 0xb8, 0x77, 0x82, 0x75, 0x04, 0x7b, 0x0e, 0x2d, 0x43, 0x41,
	0x49, 0x3b, 0xd0, 0x14, 0xd8, 0xaf, 0xca, 0xeb, 0x2b, 0xe4, 0x45, 0x15, 0xb9, 0x71, 0x49, 0xdf,
	0x10, 0xcb, 0x6d, 0xf6, 0x02, 0x9a, 0xff, 0x3e, 0xaa, 0xa6, 0xb6, 0xc0, 0x56, 0x22, 0x36, 0xae,
	0xab, 0x05, 0xad, 0x23, 0x9f, 0x38, 0x56, 0x5d, 0x47, 0x3e, 0x61, 0xcf, 0xa0, 0x15, 0x72, 0x33,
	0xf3, 0x13, 0x68, 0x2d, 0xd1, 0xf5, 0x10, 0xcd, 0xe1, 0x62, 0xad, 0x8a, 0x42, 0xa1, 0x6d, 0x5a,
	0x51, 0x3e, 0xfd, 0x63, 0x81, 0xf1, 0xe8, 0xf4, 0x13, 0x01, 0xfa, 0x7f, 0x4b, 0x74, 0xf7, 0xc0,
	0xf8, 0x5d, 0xae, 0x2d, 0xdf, 0xdd, 0xbb, 0x7d, 0x09, 0x25, 0x7b, 0xfc, 0x71, 0x3a, 0xb7, 0xc9,
	0x97, 0xe9, 0xdc, 0x5e, 0x55, 0x01, 0x06, 0xdf, 0xa6, 0x73, 0x9b, 0xee, 0x2b, 0xff, 0x48, 0x89,
	0xf8, 0xd8, 0xdf, 0x47, 0xff, 0x08, 0xc5, 0x19, 0x3f, 0xa6, 0x09, 0x6c, 0xd4, 0x6a, 0xa6, 0x0f,
	0xcd, 0xb3, 0xcd, 0x37, 0x71, 0x1f, 0xdd, 0x30, 0x45, 0xc9, 0x76, 0x34, 0xd2, 0xd2, 0xc8, 0x86,
	0x0a, 0x26, 0x01, 0x06, 0x45, 0x85, 0x6d, 0x2c, 0xb0, 0xf4, 0x1d, 0x6c, 0xd6, 0xab, 0xa1, 0x57,
	0xce, 0xbb, 0xd2, 0xb8, 0xeb, 0xdd, 0x34, 0x46, 0xc9, 0xb6, 0x35, 0xcf, 0xd6, 0x3c, 0x4b, 0x99,
	0x24, 0xf7, 0xee, 0xe7, 0xe9, 0xdc, 0xfe, 0x7e, 0xd6, 0x6b, 0x9f, 0xcf, 0x3c, 0xf2, 0x63, 0xe6,
	0x91, 0x5f, 0x33, 0x8f, 0x7c, 0xfd, 0xed, 0xad, 0xfc, 0x0d, 0x00, 0x00, 0xff, 0xff, 0xdd, 0x02,
	0x6e, 0x3e, 0xa2, 0x03, 0x00, 0x00,
}
