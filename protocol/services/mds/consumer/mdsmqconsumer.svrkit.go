package mds

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: src/mds/consumer/mdsmqconsumer.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for MdsMqConsumer service
const MdsMqConsumerMagic = uint16(15666)

// Client API for MdsMqConsumer service

type MdsMqConsumerClientInterface interface {
}

type MdsMqConsumerClient struct {
	cc *svrkit.ClientConn
}

func NewMdsMqConsumerClient(cc *svrkit.ClientConn) MdsMqConsumerClientInterface {
	return &MdsMqConsumerClient{cc}
}

const (
	commandMdsMqConsumerGetSelfSvnInfo = 9995
	commandMdsMqConsumerEcho           = 9999
)
