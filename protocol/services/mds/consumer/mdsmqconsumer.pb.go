// Code generated by protoc-gen-gogo.
// source: src/mds/consumer/mdsmqconsumer.proto
// DO NOT EDIT!

/*
	Package mds is a generated protocol buffer package.

	It is generated from these files:
		src/mds/consumer/mdsmqconsumer.proto

	It has these top-level messages:
*/
package mds

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

func init() { proto.RegisterFile("src/mds/consumer/mdsmqconsumer.proto", fileDescriptorMdsmqconsumer) }

var fileDescriptorMdsmqconsumer = []byte{
	// 132 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x29, 0x2e, 0x4a, 0xd6,
	0xcf, 0x4d, 0x29, 0xd6, 0x4f, 0xce, 0xcf, 0x2b, 0x2e, 0xcd, 0x4d, 0x2d, 0x02, 0x71, 0x72, 0x0b,
	0x61, 0x3c, 0xbd, 0x82, 0xa2, 0xfc, 0x92, 0x7c, 0x21, 0xe6, 0xdc, 0x94, 0x62, 0x29, 0x95, 0xe4,
	0xfc, 0xdc, 0xdc, 0xfc, 0x3c, 0xfd, 0x92, 0x9c, 0xb2, 0x82, 0xcc, 0xe4, 0xec, 0x9c, 0x54, 0xfd,
	0xe2, 0xec, 0xa4, 0xd2, 0xcc, 0x9c, 0x92, 0xcc, 0xbc, 0x92, 0xca, 0x82, 0x54, 0x88, 0x52, 0x23,
	0x71, 0x2e, 0x5e, 0xdf, 0x94, 0x62, 0xdf, 0x42, 0x67, 0xa8, 0x09, 0x52, 0x6c, 0x1d, 0x4b, 0x5e,
	0x30, 0x6f, 0xaa, 0x72, 0x12, 0x38, 0xf1, 0x48, 0x8e, 0xf1, 0xc2, 0x23, 0x39, 0xc6, 0x07, 0x8f,
	0xe4, 0x18, 0x27, 0x3c, 0x96, 0x63, 0x00, 0x04, 0x00, 0x00, 0xff, 0xff, 0xf6, 0x16, 0xc5, 0x24,
	0x7c, 0x00, 0x00, 0x00,
}
