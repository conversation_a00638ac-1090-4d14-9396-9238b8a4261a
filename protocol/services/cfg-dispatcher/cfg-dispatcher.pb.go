// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cfg-dispatcher/cfg-dispatcher.proto

package cfg_dispatcher // import "golang.52tt.com/protocol/services/cfg-dispatcher"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import grpc_transport_cfg "golang.52tt.com/protocol/app/grpc-transport-cfg"
import transport "golang.52tt.com/protocol/app/transport"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 跟EOSType对齐
type ClientType int32

const (
	ClientType_ENUM_CT_UNKNOWN  ClientType = 0
	ClientType_ENUM_CT_ANDROID  ClientType = 1
	ClientType_ENUM_CT_IOS      ClientType = 2
	ClientType_ENUM_CT_WINPHONE ClientType = 3
	ClientType_ENUM_CT_MACOSX   ClientType = 4
	ClientType_ENUM_CT_WINDOWS  ClientType = 5
	ClientType_ENUM_CT_LINUX    ClientType = 6
	ClientType_ENUM_CT_MAX      ClientType = 15
)

var ClientType_name = map[int32]string{
	0:  "ENUM_CT_UNKNOWN",
	1:  "ENUM_CT_ANDROID",
	2:  "ENUM_CT_IOS",
	3:  "ENUM_CT_WINPHONE",
	4:  "ENUM_CT_MACOSX",
	5:  "ENUM_CT_WINDOWS",
	6:  "ENUM_CT_LINUX",
	15: "ENUM_CT_MAX",
}
var ClientType_value = map[string]int32{
	"ENUM_CT_UNKNOWN":  0,
	"ENUM_CT_ANDROID":  1,
	"ENUM_CT_IOS":      2,
	"ENUM_CT_WINPHONE": 3,
	"ENUM_CT_MACOSX":   4,
	"ENUM_CT_WINDOWS":  5,
	"ENUM_CT_LINUX":    6,
	"ENUM_CT_MAX":      15,
}

func (x ClientType) String() string {
	return proto.EnumName(ClientType_name, int32(x))
}
func (ClientType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{0}
}

type EndpointV2_TLSConfig_TLSMode int32

const (
	EndpointV2_TLSConfig_TLS_MODE_DISABLE EndpointV2_TLSConfig_TLSMode = 0
	EndpointV2_TLSConfig_TLS_MODE_SIMPLE  EndpointV2_TLSConfig_TLSMode = 1
	EndpointV2_TLSConfig_TLS_MODE_MUTUAL  EndpointV2_TLSConfig_TLSMode = 2
)

var EndpointV2_TLSConfig_TLSMode_name = map[int32]string{
	0: "TLS_MODE_DISABLE",
	1: "TLS_MODE_SIMPLE",
	2: "TLS_MODE_MUTUAL",
}
var EndpointV2_TLSConfig_TLSMode_value = map[string]int32{
	"TLS_MODE_DISABLE": 0,
	"TLS_MODE_SIMPLE":  1,
	"TLS_MODE_MUTUAL":  2,
}

func (x EndpointV2_TLSConfig_TLSMode) String() string {
	return proto.EnumName(EndpointV2_TLSConfig_TLSMode_name, int32(x))
}
func (EndpointV2_TLSConfig_TLSMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{8, 0, 0}
}

type Rules_StringMatch_MatchType int32

const (
	Rules_StringMatch_MATCH_TYPE_UNSPECIFIED Rules_StringMatch_MatchType = 0
	Rules_StringMatch_MATCH_TYPE_EXACT       Rules_StringMatch_MatchType = 1
	Rules_StringMatch_MATCH_TYPE_PREFIX      Rules_StringMatch_MatchType = 2
	Rules_StringMatch_MATCH_TYPE_ALL         Rules_StringMatch_MatchType = 3
)

var Rules_StringMatch_MatchType_name = map[int32]string{
	0: "MATCH_TYPE_UNSPECIFIED",
	1: "MATCH_TYPE_EXACT",
	2: "MATCH_TYPE_PREFIX",
	3: "MATCH_TYPE_ALL",
}
var Rules_StringMatch_MatchType_value = map[string]int32{
	"MATCH_TYPE_UNSPECIFIED": 0,
	"MATCH_TYPE_EXACT":       1,
	"MATCH_TYPE_PREFIX":      2,
	"MATCH_TYPE_ALL":         3,
}

func (x Rules_StringMatch_MatchType) String() string {
	return proto.EnumName(Rules_StringMatch_MatchType_name, int32(x))
}
func (Rules_StringMatch_MatchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{10, 0, 0}
}

type Rules_Rule_Action int32

const (
	Rules_Rule_ACTION_UNSPECIFIED Rules_Rule_Action = 0
	Rules_Rule_ACTION_ALLOW       Rules_Rule_Action = 1
	Rules_Rule_ACTION_DENY        Rules_Rule_Action = 2
)

var Rules_Rule_Action_name = map[int32]string{
	0: "ACTION_UNSPECIFIED",
	1: "ACTION_ALLOW",
	2: "ACTION_DENY",
}
var Rules_Rule_Action_value = map[string]int32{
	"ACTION_UNSPECIFIED": 0,
	"ACTION_ALLOW":       1,
	"ACTION_DENY":        2,
}

func (x Rules_Rule_Action) String() string {
	return proto.EnumName(Rules_Rule_Action_name, int32(x))
}
func (Rules_Rule_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{10, 1, 0}
}

type GetGrpcEndpointsReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientType           ClientType `protobuf:"varint,2,opt,name=client_type,json=clientType,proto3,enum=cfg_dispatcher.ClientType" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetGrpcEndpointsReq) Reset()         { *m = GetGrpcEndpointsReq{} }
func (m *GetGrpcEndpointsReq) String() string { return proto.CompactTextString(m) }
func (*GetGrpcEndpointsReq) ProtoMessage()    {}
func (*GetGrpcEndpointsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{0}
}
func (m *GetGrpcEndpointsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGrpcEndpointsReq.Unmarshal(m, b)
}
func (m *GetGrpcEndpointsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGrpcEndpointsReq.Marshal(b, m, deterministic)
}
func (dst *GetGrpcEndpointsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGrpcEndpointsReq.Merge(dst, src)
}
func (m *GetGrpcEndpointsReq) XXX_Size() int {
	return xxx_messageInfo_GetGrpcEndpointsReq.Size(m)
}
func (m *GetGrpcEndpointsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGrpcEndpointsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGrpcEndpointsReq proto.InternalMessageInfo

func (m *GetGrpcEndpointsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGrpcEndpointsReq) GetClientType() ClientType {
	if m != nil {
		return m.ClientType
	}
	return ClientType_ENUM_CT_UNKNOWN
}

type GetGrpcEndpointsResp struct {
	TransportConfig      *transport.TransportConfig `protobuf:"bytes,1,opt,name=transport_config,json=transportConfig,proto3" json:"transport_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetGrpcEndpointsResp) Reset()         { *m = GetGrpcEndpointsResp{} }
func (m *GetGrpcEndpointsResp) String() string { return proto.CompactTextString(m) }
func (*GetGrpcEndpointsResp) ProtoMessage()    {}
func (*GetGrpcEndpointsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{1}
}
func (m *GetGrpcEndpointsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGrpcEndpointsResp.Unmarshal(m, b)
}
func (m *GetGrpcEndpointsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGrpcEndpointsResp.Marshal(b, m, deterministic)
}
func (dst *GetGrpcEndpointsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGrpcEndpointsResp.Merge(dst, src)
}
func (m *GetGrpcEndpointsResp) XXX_Size() int {
	return xxx_messageInfo_GetGrpcEndpointsResp.Size(m)
}
func (m *GetGrpcEndpointsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGrpcEndpointsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGrpcEndpointsResp proto.InternalMessageInfo

func (m *GetGrpcEndpointsResp) GetTransportConfig() *transport.TransportConfig {
	if m != nil {
		return m.TransportConfig
	}
	return nil
}

type AppInfo struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientType           ClientType `protobuf:"varint,2,opt,name=client_type,json=clientType,proto3,enum=cfg_dispatcher.ClientType" json:"client_type,omitempty"`
	AppVersionInt        uint32     `protobuf:"varint,3,opt,name=app_version_int,json=appVersionInt,proto3" json:"app_version_int,omitempty"`
	MarketId             uint32     `protobuf:"varint,4,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AppInfo) Reset()         { *m = AppInfo{} }
func (m *AppInfo) String() string { return proto.CompactTextString(m) }
func (*AppInfo) ProtoMessage()    {}
func (*AppInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{2}
}
func (m *AppInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppInfo.Unmarshal(m, b)
}
func (m *AppInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppInfo.Marshal(b, m, deterministic)
}
func (dst *AppInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppInfo.Merge(dst, src)
}
func (m *AppInfo) XXX_Size() int {
	return xxx_messageInfo_AppInfo.Size(m)
}
func (m *AppInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AppInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AppInfo proto.InternalMessageInfo

func (m *AppInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AppInfo) GetClientType() ClientType {
	if m != nil {
		return m.ClientType
	}
	return ClientType_ENUM_CT_UNKNOWN
}

func (m *AppInfo) GetAppVersionInt() uint32 {
	if m != nil {
		return m.AppVersionInt
	}
	return 0
}

func (m *AppInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GetTransportConfigV2Request struct {
	AppInfo              *AppInfo `protobuf:"bytes,1,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTransportConfigV2Request) Reset()         { *m = GetTransportConfigV2Request{} }
func (m *GetTransportConfigV2Request) String() string { return proto.CompactTextString(m) }
func (*GetTransportConfigV2Request) ProtoMessage()    {}
func (*GetTransportConfigV2Request) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{3}
}
func (m *GetTransportConfigV2Request) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTransportConfigV2Request.Unmarshal(m, b)
}
func (m *GetTransportConfigV2Request) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTransportConfigV2Request.Marshal(b, m, deterministic)
}
func (dst *GetTransportConfigV2Request) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTransportConfigV2Request.Merge(dst, src)
}
func (m *GetTransportConfigV2Request) XXX_Size() int {
	return xxx_messageInfo_GetTransportConfigV2Request.Size(m)
}
func (m *GetTransportConfigV2Request) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTransportConfigV2Request.DiscardUnknown(m)
}

var xxx_messageInfo_GetTransportConfigV2Request proto.InternalMessageInfo

func (m *GetTransportConfigV2Request) GetAppInfo() *AppInfo {
	if m != nil {
		return m.AppInfo
	}
	return nil
}

type GetTransportConfigV2Response struct {
	TransportConfig_V2   *grpc_transport_cfg.TransportConfigV2 `protobuf:"bytes,1,opt,name=transport_config_V2,json=transportConfigV2,proto3" json:"transport_config_V2,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *GetTransportConfigV2Response) Reset()         { *m = GetTransportConfigV2Response{} }
func (m *GetTransportConfigV2Response) String() string { return proto.CompactTextString(m) }
func (*GetTransportConfigV2Response) ProtoMessage()    {}
func (*GetTransportConfigV2Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{4}
}
func (m *GetTransportConfigV2Response) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTransportConfigV2Response.Unmarshal(m, b)
}
func (m *GetTransportConfigV2Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTransportConfigV2Response.Marshal(b, m, deterministic)
}
func (dst *GetTransportConfigV2Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTransportConfigV2Response.Merge(dst, src)
}
func (m *GetTransportConfigV2Response) XXX_Size() int {
	return xxx_messageInfo_GetTransportConfigV2Response.Size(m)
}
func (m *GetTransportConfigV2Response) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTransportConfigV2Response.DiscardUnknown(m)
}

var xxx_messageInfo_GetTransportConfigV2Response proto.InternalMessageInfo

func (m *GetTransportConfigV2Response) GetTransportConfig_V2() *grpc_transport_cfg.TransportConfigV2 {
	if m != nil {
		return m.TransportConfig_V2
	}
	return nil
}

type Scope struct {
	Gt                   uint32   `protobuf:"varint,1,opt,name=gt,proto3" json:"gt,omitempty"`
	Lt                   uint32   `protobuf:"varint,2,opt,name=lt,proto3" json:"lt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Scope) Reset()         { *m = Scope{} }
func (m *Scope) String() string { return proto.CompactTextString(m) }
func (*Scope) ProtoMessage()    {}
func (*Scope) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{5}
}
func (m *Scope) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Scope.Unmarshal(m, b)
}
func (m *Scope) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Scope.Marshal(b, m, deterministic)
}
func (dst *Scope) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Scope.Merge(dst, src)
}
func (m *Scope) XXX_Size() int {
	return xxx_messageInfo_Scope.Size(m)
}
func (m *Scope) XXX_DiscardUnknown() {
	xxx_messageInfo_Scope.DiscardUnknown(m)
}

var xxx_messageInfo_Scope proto.InternalMessageInfo

func (m *Scope) GetGt() uint32 {
	if m != nil {
		return m.Gt
	}
	return 0
}

func (m *Scope) GetLt() uint32 {
	if m != nil {
		return m.Lt
	}
	return 0
}

type UidIntMatch struct {
	// Types that are valid to be assigned to Action:
	//	*UidIntMatch_Exact
	//	*UidIntMatch_Scope
	//	*UidIntMatch_Modulo_
	Action               isUidIntMatch_Action `protobuf_oneof:"action"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UidIntMatch) Reset()         { *m = UidIntMatch{} }
func (m *UidIntMatch) String() string { return proto.CompactTextString(m) }
func (*UidIntMatch) ProtoMessage()    {}
func (*UidIntMatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{6}
}
func (m *UidIntMatch) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UidIntMatch.Unmarshal(m, b)
}
func (m *UidIntMatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UidIntMatch.Marshal(b, m, deterministic)
}
func (dst *UidIntMatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UidIntMatch.Merge(dst, src)
}
func (m *UidIntMatch) XXX_Size() int {
	return xxx_messageInfo_UidIntMatch.Size(m)
}
func (m *UidIntMatch) XXX_DiscardUnknown() {
	xxx_messageInfo_UidIntMatch.DiscardUnknown(m)
}

var xxx_messageInfo_UidIntMatch proto.InternalMessageInfo

type isUidIntMatch_Action interface {
	isUidIntMatch_Action()
}

type UidIntMatch_Exact struct {
	Exact uint32 `protobuf:"varint,1,opt,name=exact,proto3,oneof"`
}

type UidIntMatch_Scope struct {
	Scope *Scope `protobuf:"bytes,2,opt,name=scope,proto3,oneof"`
}

type UidIntMatch_Modulo_ struct {
	Modulo *UidIntMatch_Modulo `protobuf:"bytes,3,opt,name=modulo,proto3,oneof"`
}

func (*UidIntMatch_Exact) isUidIntMatch_Action() {}

func (*UidIntMatch_Scope) isUidIntMatch_Action() {}

func (*UidIntMatch_Modulo_) isUidIntMatch_Action() {}

func (m *UidIntMatch) GetAction() isUidIntMatch_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (m *UidIntMatch) GetExact() uint32 {
	if x, ok := m.GetAction().(*UidIntMatch_Exact); ok {
		return x.Exact
	}
	return 0
}

func (m *UidIntMatch) GetScope() *Scope {
	if x, ok := m.GetAction().(*UidIntMatch_Scope); ok {
		return x.Scope
	}
	return nil
}

func (m *UidIntMatch) GetModulo() *UidIntMatch_Modulo {
	if x, ok := m.GetAction().(*UidIntMatch_Modulo_); ok {
		return x.Modulo
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*UidIntMatch) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _UidIntMatch_OneofMarshaler, _UidIntMatch_OneofUnmarshaler, _UidIntMatch_OneofSizer, []interface{}{
		(*UidIntMatch_Exact)(nil),
		(*UidIntMatch_Scope)(nil),
		(*UidIntMatch_Modulo_)(nil),
	}
}

func _UidIntMatch_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*UidIntMatch)
	// action
	switch x := m.Action.(type) {
	case *UidIntMatch_Exact:
		b.EncodeVarint(1<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.Exact))
	case *UidIntMatch_Scope:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Scope); err != nil {
			return err
		}
	case *UidIntMatch_Modulo_:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Modulo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("UidIntMatch.Action has unexpected type %T", x)
	}
	return nil
}

func _UidIntMatch_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*UidIntMatch)
	switch tag {
	case 1: // action.exact
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Action = &UidIntMatch_Exact{uint32(x)}
		return true, err
	case 2: // action.scope
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Scope)
		err := b.DecodeMessage(msg)
		m.Action = &UidIntMatch_Scope{msg}
		return true, err
	case 3: // action.modulo
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UidIntMatch_Modulo)
		err := b.DecodeMessage(msg)
		m.Action = &UidIntMatch_Modulo_{msg}
		return true, err
	default:
		return false, nil
	}
}

func _UidIntMatch_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*UidIntMatch)
	// action
	switch x := m.Action.(type) {
	case *UidIntMatch_Exact:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.Exact))
	case *UidIntMatch_Scope:
		s := proto.Size(x.Scope)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *UidIntMatch_Modulo_:
		s := proto.Size(x.Modulo)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type UidIntMatch_Modulo struct {
	Divisor              uint32   `protobuf:"varint,1,opt,name=divisor,proto3" json:"divisor,omitempty"`
	Remainders           []uint32 `protobuf:"varint,2,rep,packed,name=remainders,proto3" json:"remainders,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UidIntMatch_Modulo) Reset()         { *m = UidIntMatch_Modulo{} }
func (m *UidIntMatch_Modulo) String() string { return proto.CompactTextString(m) }
func (*UidIntMatch_Modulo) ProtoMessage()    {}
func (*UidIntMatch_Modulo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{6, 0}
}
func (m *UidIntMatch_Modulo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UidIntMatch_Modulo.Unmarshal(m, b)
}
func (m *UidIntMatch_Modulo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UidIntMatch_Modulo.Marshal(b, m, deterministic)
}
func (dst *UidIntMatch_Modulo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UidIntMatch_Modulo.Merge(dst, src)
}
func (m *UidIntMatch_Modulo) XXX_Size() int {
	return xxx_messageInfo_UidIntMatch_Modulo.Size(m)
}
func (m *UidIntMatch_Modulo) XXX_DiscardUnknown() {
	xxx_messageInfo_UidIntMatch_Modulo.DiscardUnknown(m)
}

var xxx_messageInfo_UidIntMatch_Modulo proto.InternalMessageInfo

func (m *UidIntMatch_Modulo) GetDivisor() uint32 {
	if m != nil {
		return m.Divisor
	}
	return 0
}

func (m *UidIntMatch_Modulo) GetRemainders() []uint32 {
	if m != nil {
		return m.Remainders
	}
	return nil
}

type AppVersionIntMatch struct {
	// Types that are valid to be assigned to Action:
	//	*AppVersionIntMatch_Gt
	//	*AppVersionIntMatch_Lt
	Action               isAppVersionIntMatch_Action `protobuf_oneof:"action"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *AppVersionIntMatch) Reset()         { *m = AppVersionIntMatch{} }
func (m *AppVersionIntMatch) String() string { return proto.CompactTextString(m) }
func (*AppVersionIntMatch) ProtoMessage()    {}
func (*AppVersionIntMatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{7}
}
func (m *AppVersionIntMatch) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppVersionIntMatch.Unmarshal(m, b)
}
func (m *AppVersionIntMatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppVersionIntMatch.Marshal(b, m, deterministic)
}
func (dst *AppVersionIntMatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppVersionIntMatch.Merge(dst, src)
}
func (m *AppVersionIntMatch) XXX_Size() int {
	return xxx_messageInfo_AppVersionIntMatch.Size(m)
}
func (m *AppVersionIntMatch) XXX_DiscardUnknown() {
	xxx_messageInfo_AppVersionIntMatch.DiscardUnknown(m)
}

var xxx_messageInfo_AppVersionIntMatch proto.InternalMessageInfo

type isAppVersionIntMatch_Action interface {
	isAppVersionIntMatch_Action()
}

type AppVersionIntMatch_Gt struct {
	Gt uint32 `protobuf:"varint,1,opt,name=gt,proto3,oneof"`
}

type AppVersionIntMatch_Lt struct {
	Lt uint32 `protobuf:"varint,2,opt,name=lt,proto3,oneof"`
}

func (*AppVersionIntMatch_Gt) isAppVersionIntMatch_Action() {}

func (*AppVersionIntMatch_Lt) isAppVersionIntMatch_Action() {}

func (m *AppVersionIntMatch) GetAction() isAppVersionIntMatch_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (m *AppVersionIntMatch) GetGt() uint32 {
	if x, ok := m.GetAction().(*AppVersionIntMatch_Gt); ok {
		return x.Gt
	}
	return 0
}

func (m *AppVersionIntMatch) GetLt() uint32 {
	if x, ok := m.GetAction().(*AppVersionIntMatch_Lt); ok {
		return x.Lt
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*AppVersionIntMatch) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _AppVersionIntMatch_OneofMarshaler, _AppVersionIntMatch_OneofUnmarshaler, _AppVersionIntMatch_OneofSizer, []interface{}{
		(*AppVersionIntMatch_Gt)(nil),
		(*AppVersionIntMatch_Lt)(nil),
	}
}

func _AppVersionIntMatch_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*AppVersionIntMatch)
	// action
	switch x := m.Action.(type) {
	case *AppVersionIntMatch_Gt:
		b.EncodeVarint(1<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.Gt))
	case *AppVersionIntMatch_Lt:
		b.EncodeVarint(2<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.Lt))
	case nil:
	default:
		return fmt.Errorf("AppVersionIntMatch.Action has unexpected type %T", x)
	}
	return nil
}

func _AppVersionIntMatch_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*AppVersionIntMatch)
	switch tag {
	case 1: // action.gt
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Action = &AppVersionIntMatch_Gt{uint32(x)}
		return true, err
	case 2: // action.lt
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Action = &AppVersionIntMatch_Lt{uint32(x)}
		return true, err
	default:
		return false, nil
	}
}

func _AppVersionIntMatch_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*AppVersionIntMatch)
	// action
	switch x := m.Action.(type) {
	case *AppVersionIntMatch_Gt:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.Gt))
	case *AppVersionIntMatch_Lt:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.Lt))
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 跟transport_v2.proto定义一样，因为gogo插件的原因，这里只好单独拎出来
type EndpointV2 struct {
	// address like: dev-apv2.ttyuyin.com:443
	Address              string                `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	TlsConfig            *EndpointV2_TLSConfig `protobuf:"bytes,2,opt,name=tls_config,json=tlsConfig,proto3" json:"tls_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *EndpointV2) Reset()         { *m = EndpointV2{} }
func (m *EndpointV2) String() string { return proto.CompactTextString(m) }
func (*EndpointV2) ProtoMessage()    {}
func (*EndpointV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{8}
}
func (m *EndpointV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndpointV2.Unmarshal(m, b)
}
func (m *EndpointV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndpointV2.Marshal(b, m, deterministic)
}
func (dst *EndpointV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndpointV2.Merge(dst, src)
}
func (m *EndpointV2) XXX_Size() int {
	return xxx_messageInfo_EndpointV2.Size(m)
}
func (m *EndpointV2) XXX_DiscardUnknown() {
	xxx_messageInfo_EndpointV2.DiscardUnknown(m)
}

var xxx_messageInfo_EndpointV2 proto.InternalMessageInfo

func (m *EndpointV2) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *EndpointV2) GetTlsConfig() *EndpointV2_TLSConfig {
	if m != nil {
		return m.TlsConfig
	}
	return nil
}

type EndpointV2_TLSConfig struct {
	Mode                 EndpointV2_TLSConfig_TLSMode `protobuf:"varint,1,opt,name=mode,proto3,enum=cfg_dispatcher.EndpointV2_TLSConfig_TLSMode" json:"mode,omitempty"`
	Authority            string                       `protobuf:"bytes,2,opt,name=authority,proto3" json:"authority,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *EndpointV2_TLSConfig) Reset()         { *m = EndpointV2_TLSConfig{} }
func (m *EndpointV2_TLSConfig) String() string { return proto.CompactTextString(m) }
func (*EndpointV2_TLSConfig) ProtoMessage()    {}
func (*EndpointV2_TLSConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{8, 0}
}
func (m *EndpointV2_TLSConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndpointV2_TLSConfig.Unmarshal(m, b)
}
func (m *EndpointV2_TLSConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndpointV2_TLSConfig.Marshal(b, m, deterministic)
}
func (dst *EndpointV2_TLSConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndpointV2_TLSConfig.Merge(dst, src)
}
func (m *EndpointV2_TLSConfig) XXX_Size() int {
	return xxx_messageInfo_EndpointV2_TLSConfig.Size(m)
}
func (m *EndpointV2_TLSConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_EndpointV2_TLSConfig.DiscardUnknown(m)
}

var xxx_messageInfo_EndpointV2_TLSConfig proto.InternalMessageInfo

func (m *EndpointV2_TLSConfig) GetMode() EndpointV2_TLSConfig_TLSMode {
	if m != nil {
		return m.Mode
	}
	return EndpointV2_TLSConfig_TLS_MODE_DISABLE
}

func (m *EndpointV2_TLSConfig) GetAuthority() string {
	if m != nil {
		return m.Authority
	}
	return ""
}

type EndpointRule struct {
	Rules                []*EndpointRule_Rule `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *EndpointRule) Reset()         { *m = EndpointRule{} }
func (m *EndpointRule) String() string { return proto.CompactTextString(m) }
func (*EndpointRule) ProtoMessage()    {}
func (*EndpointRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{9}
}
func (m *EndpointRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndpointRule.Unmarshal(m, b)
}
func (m *EndpointRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndpointRule.Marshal(b, m, deterministic)
}
func (dst *EndpointRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndpointRule.Merge(dst, src)
}
func (m *EndpointRule) XXX_Size() int {
	return xxx_messageInfo_EndpointRule.Size(m)
}
func (m *EndpointRule) XXX_DiscardUnknown() {
	xxx_messageInfo_EndpointRule.DiscardUnknown(m)
}

var xxx_messageInfo_EndpointRule proto.InternalMessageInfo

func (m *EndpointRule) GetRules() []*EndpointRule_Rule {
	if m != nil {
		return m.Rules
	}
	return nil
}

type EndpointRule_AppRule struct {
	Uid                  *UidIntMatch        `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientType           []uint32            `protobuf:"varint,2,rep,packed,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	MarketId             []uint32            `protobuf:"varint,3,rep,packed,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	AppVersionInt        *AppVersionIntMatch `protobuf:"bytes,4,opt,name=app_version_int,json=appVersionInt,proto3" json:"app_version_int,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *EndpointRule_AppRule) Reset()         { *m = EndpointRule_AppRule{} }
func (m *EndpointRule_AppRule) String() string { return proto.CompactTextString(m) }
func (*EndpointRule_AppRule) ProtoMessage()    {}
func (*EndpointRule_AppRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{9, 0}
}
func (m *EndpointRule_AppRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndpointRule_AppRule.Unmarshal(m, b)
}
func (m *EndpointRule_AppRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndpointRule_AppRule.Marshal(b, m, deterministic)
}
func (dst *EndpointRule_AppRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndpointRule_AppRule.Merge(dst, src)
}
func (m *EndpointRule_AppRule) XXX_Size() int {
	return xxx_messageInfo_EndpointRule_AppRule.Size(m)
}
func (m *EndpointRule_AppRule) XXX_DiscardUnknown() {
	xxx_messageInfo_EndpointRule_AppRule.DiscardUnknown(m)
}

var xxx_messageInfo_EndpointRule_AppRule proto.InternalMessageInfo

func (m *EndpointRule_AppRule) GetUid() *UidIntMatch {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *EndpointRule_AppRule) GetClientType() []uint32 {
	if m != nil {
		return m.ClientType
	}
	return nil
}

func (m *EndpointRule_AppRule) GetMarketId() []uint32 {
	if m != nil {
		return m.MarketId
	}
	return nil
}

func (m *EndpointRule_AppRule) GetAppVersionInt() *AppVersionIntMatch {
	if m != nil {
		return m.AppVersionInt
	}
	return nil
}

type EndpointRule_Rule struct {
	AppRule              *EndpointRule_AppRule `protobuf:"bytes,1,opt,name=app_rule,json=appRule,proto3" json:"app_rule,omitempty"`
	EndpointList         []*EndpointV2         `protobuf:"bytes,2,rep,name=endpoint_list,json=endpointList,proto3" json:"endpoint_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *EndpointRule_Rule) Reset()         { *m = EndpointRule_Rule{} }
func (m *EndpointRule_Rule) String() string { return proto.CompactTextString(m) }
func (*EndpointRule_Rule) ProtoMessage()    {}
func (*EndpointRule_Rule) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{9, 1}
}
func (m *EndpointRule_Rule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndpointRule_Rule.Unmarshal(m, b)
}
func (m *EndpointRule_Rule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndpointRule_Rule.Marshal(b, m, deterministic)
}
func (dst *EndpointRule_Rule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndpointRule_Rule.Merge(dst, src)
}
func (m *EndpointRule_Rule) XXX_Size() int {
	return xxx_messageInfo_EndpointRule_Rule.Size(m)
}
func (m *EndpointRule_Rule) XXX_DiscardUnknown() {
	xxx_messageInfo_EndpointRule_Rule.DiscardUnknown(m)
}

var xxx_messageInfo_EndpointRule_Rule proto.InternalMessageInfo

func (m *EndpointRule_Rule) GetAppRule() *EndpointRule_AppRule {
	if m != nil {
		return m.AppRule
	}
	return nil
}

func (m *EndpointRule_Rule) GetEndpointList() []*EndpointV2 {
	if m != nil {
		return m.EndpointList
	}
	return nil
}

type Rules struct {
	Rules                []*Rules_Rule        `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
	GzipBlackRules       []*Rules_StringMatch `protobuf:"bytes,2,rep,name=gzip_black_rules,json=gzipBlackRules,proto3" json:"gzip_black_rules,omitempty"`
	GzipWhiteRules       []*Rules_StringMatch `protobuf:"bytes,3,rep,name=gzip_white_rules,json=gzipWhiteRules,proto3" json:"gzip_white_rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *Rules) Reset()         { *m = Rules{} }
func (m *Rules) String() string { return proto.CompactTextString(m) }
func (*Rules) ProtoMessage()    {}
func (*Rules) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{10}
}
func (m *Rules) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Rules.Unmarshal(m, b)
}
func (m *Rules) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Rules.Marshal(b, m, deterministic)
}
func (dst *Rules) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Rules.Merge(dst, src)
}
func (m *Rules) XXX_Size() int {
	return xxx_messageInfo_Rules.Size(m)
}
func (m *Rules) XXX_DiscardUnknown() {
	xxx_messageInfo_Rules.DiscardUnknown(m)
}

var xxx_messageInfo_Rules proto.InternalMessageInfo

func (m *Rules) GetRules() []*Rules_Rule {
	if m != nil {
		return m.Rules
	}
	return nil
}

func (m *Rules) GetGzipBlackRules() []*Rules_StringMatch {
	if m != nil {
		return m.GzipBlackRules
	}
	return nil
}

func (m *Rules) GetGzipWhiteRules() []*Rules_StringMatch {
	if m != nil {
		return m.GzipWhiteRules
	}
	return nil
}

// 跟transport_v2.proto定义一样，因为gogo插件的原因，这里只好单独拎出来
type Rules_StringMatch struct {
	MatchType            Rules_StringMatch_MatchType `protobuf:"varint,1,opt,name=match_type,json=matchType,proto3,enum=cfg_dispatcher.Rules_StringMatch_MatchType" json:"match_type,omitempty"`
	MatchValue           string                      `protobuf:"bytes,2,opt,name=match_value,json=matchValue,proto3" json:"match_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *Rules_StringMatch) Reset()         { *m = Rules_StringMatch{} }
func (m *Rules_StringMatch) String() string { return proto.CompactTextString(m) }
func (*Rules_StringMatch) ProtoMessage()    {}
func (*Rules_StringMatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{10, 0}
}
func (m *Rules_StringMatch) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Rules_StringMatch.Unmarshal(m, b)
}
func (m *Rules_StringMatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Rules_StringMatch.Marshal(b, m, deterministic)
}
func (dst *Rules_StringMatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Rules_StringMatch.Merge(dst, src)
}
func (m *Rules_StringMatch) XXX_Size() int {
	return xxx_messageInfo_Rules_StringMatch.Size(m)
}
func (m *Rules_StringMatch) XXX_DiscardUnknown() {
	xxx_messageInfo_Rules_StringMatch.DiscardUnknown(m)
}

var xxx_messageInfo_Rules_StringMatch proto.InternalMessageInfo

func (m *Rules_StringMatch) GetMatchType() Rules_StringMatch_MatchType {
	if m != nil {
		return m.MatchType
	}
	return Rules_StringMatch_MATCH_TYPE_UNSPECIFIED
}

func (m *Rules_StringMatch) GetMatchValue() string {
	if m != nil {
		return m.MatchValue
	}
	return ""
}

type Rules_Rule struct {
	Action               Rules_Rule_Action    `protobuf:"varint,1,opt,name=action,proto3,enum=cfg_dispatcher.Rules_Rule_Action" json:"action,omitempty"`
	AppRule              *Rules_Rule_AppRule  `protobuf:"bytes,2,opt,name=app_rule,json=appRule,proto3" json:"app_rule,omitempty"`
	ApiRule              []*Rules_StringMatch `protobuf:"bytes,3,rep,name=api_rule,json=apiRule,proto3" json:"api_rule,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *Rules_Rule) Reset()         { *m = Rules_Rule{} }
func (m *Rules_Rule) String() string { return proto.CompactTextString(m) }
func (*Rules_Rule) ProtoMessage()    {}
func (*Rules_Rule) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{10, 1}
}
func (m *Rules_Rule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Rules_Rule.Unmarshal(m, b)
}
func (m *Rules_Rule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Rules_Rule.Marshal(b, m, deterministic)
}
func (dst *Rules_Rule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Rules_Rule.Merge(dst, src)
}
func (m *Rules_Rule) XXX_Size() int {
	return xxx_messageInfo_Rules_Rule.Size(m)
}
func (m *Rules_Rule) XXX_DiscardUnknown() {
	xxx_messageInfo_Rules_Rule.DiscardUnknown(m)
}

var xxx_messageInfo_Rules_Rule proto.InternalMessageInfo

func (m *Rules_Rule) GetAction() Rules_Rule_Action {
	if m != nil {
		return m.Action
	}
	return Rules_Rule_ACTION_UNSPECIFIED
}

func (m *Rules_Rule) GetAppRule() *Rules_Rule_AppRule {
	if m != nil {
		return m.AppRule
	}
	return nil
}

func (m *Rules_Rule) GetApiRule() []*Rules_StringMatch {
	if m != nil {
		return m.ApiRule
	}
	return nil
}

type Rules_Rule_AppRule struct {
	Uid        *UidIntMatch `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientType []uint32     `protobuf:"varint,2,rep,packed,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	MarketId   []uint32     `protobuf:"varint,3,rep,packed,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	// app_version_int 只需要大于、或者小于
	AppVersionInt        *AppVersionIntMatch `protobuf:"bytes,4,opt,name=app_version_int,json=appVersionInt,proto3" json:"app_version_int,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *Rules_Rule_AppRule) Reset()         { *m = Rules_Rule_AppRule{} }
func (m *Rules_Rule_AppRule) String() string { return proto.CompactTextString(m) }
func (*Rules_Rule_AppRule) ProtoMessage()    {}
func (*Rules_Rule_AppRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39, []int{10, 1, 0}
}
func (m *Rules_Rule_AppRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Rules_Rule_AppRule.Unmarshal(m, b)
}
func (m *Rules_Rule_AppRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Rules_Rule_AppRule.Marshal(b, m, deterministic)
}
func (dst *Rules_Rule_AppRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Rules_Rule_AppRule.Merge(dst, src)
}
func (m *Rules_Rule_AppRule) XXX_Size() int {
	return xxx_messageInfo_Rules_Rule_AppRule.Size(m)
}
func (m *Rules_Rule_AppRule) XXX_DiscardUnknown() {
	xxx_messageInfo_Rules_Rule_AppRule.DiscardUnknown(m)
}

var xxx_messageInfo_Rules_Rule_AppRule proto.InternalMessageInfo

func (m *Rules_Rule_AppRule) GetUid() *UidIntMatch {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *Rules_Rule_AppRule) GetClientType() []uint32 {
	if m != nil {
		return m.ClientType
	}
	return nil
}

func (m *Rules_Rule_AppRule) GetMarketId() []uint32 {
	if m != nil {
		return m.MarketId
	}
	return nil
}

func (m *Rules_Rule_AppRule) GetAppVersionInt() *AppVersionIntMatch {
	if m != nil {
		return m.AppVersionInt
	}
	return nil
}

func init() {
	proto.RegisterType((*GetGrpcEndpointsReq)(nil), "cfg_dispatcher.GetGrpcEndpointsReq")
	proto.RegisterType((*GetGrpcEndpointsResp)(nil), "cfg_dispatcher.GetGrpcEndpointsResp")
	proto.RegisterType((*AppInfo)(nil), "cfg_dispatcher.AppInfo")
	proto.RegisterType((*GetTransportConfigV2Request)(nil), "cfg_dispatcher.GetTransportConfigV2Request")
	proto.RegisterType((*GetTransportConfigV2Response)(nil), "cfg_dispatcher.GetTransportConfigV2Response")
	proto.RegisterType((*Scope)(nil), "cfg_dispatcher.Scope")
	proto.RegisterType((*UidIntMatch)(nil), "cfg_dispatcher.UidIntMatch")
	proto.RegisterType((*UidIntMatch_Modulo)(nil), "cfg_dispatcher.UidIntMatch.Modulo")
	proto.RegisterType((*AppVersionIntMatch)(nil), "cfg_dispatcher.AppVersionIntMatch")
	proto.RegisterType((*EndpointV2)(nil), "cfg_dispatcher.EndpointV2")
	proto.RegisterType((*EndpointV2_TLSConfig)(nil), "cfg_dispatcher.EndpointV2.TLSConfig")
	proto.RegisterType((*EndpointRule)(nil), "cfg_dispatcher.EndpointRule")
	proto.RegisterType((*EndpointRule_AppRule)(nil), "cfg_dispatcher.EndpointRule.AppRule")
	proto.RegisterType((*EndpointRule_Rule)(nil), "cfg_dispatcher.EndpointRule.Rule")
	proto.RegisterType((*Rules)(nil), "cfg_dispatcher.Rules")
	proto.RegisterType((*Rules_StringMatch)(nil), "cfg_dispatcher.Rules.StringMatch")
	proto.RegisterType((*Rules_Rule)(nil), "cfg_dispatcher.Rules.Rule")
	proto.RegisterType((*Rules_Rule_AppRule)(nil), "cfg_dispatcher.Rules.Rule.AppRule")
	proto.RegisterEnum("cfg_dispatcher.ClientType", ClientType_name, ClientType_value)
	proto.RegisterEnum("cfg_dispatcher.EndpointV2_TLSConfig_TLSMode", EndpointV2_TLSConfig_TLSMode_name, EndpointV2_TLSConfig_TLSMode_value)
	proto.RegisterEnum("cfg_dispatcher.Rules_StringMatch_MatchType", Rules_StringMatch_MatchType_name, Rules_StringMatch_MatchType_value)
	proto.RegisterEnum("cfg_dispatcher.Rules_Rule_Action", Rules_Rule_Action_name, Rules_Rule_Action_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CfgDispatcherClient is the client API for CfgDispatcher service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CfgDispatcherClient interface {
	// 先放在这个服务，获取客户端grpc通道的配置接口
	GetGrpcEndpoints(ctx context.Context, in *GetGrpcEndpointsReq, opts ...grpc.CallOption) (*GetGrpcEndpointsResp, error)
	GetTransportConfigV2(ctx context.Context, in *GetTransportConfigV2Request, opts ...grpc.CallOption) (*GetTransportConfigV2Response, error)
}

type cfgDispatcherClient struct {
	cc *grpc.ClientConn
}

func NewCfgDispatcherClient(cc *grpc.ClientConn) CfgDispatcherClient {
	return &cfgDispatcherClient{cc}
}

func (c *cfgDispatcherClient) GetGrpcEndpoints(ctx context.Context, in *GetGrpcEndpointsReq, opts ...grpc.CallOption) (*GetGrpcEndpointsResp, error) {
	out := new(GetGrpcEndpointsResp)
	err := c.cc.Invoke(ctx, "/cfg_dispatcher.CfgDispatcher/GetGrpcEndpoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cfgDispatcherClient) GetTransportConfigV2(ctx context.Context, in *GetTransportConfigV2Request, opts ...grpc.CallOption) (*GetTransportConfigV2Response, error) {
	out := new(GetTransportConfigV2Response)
	err := c.cc.Invoke(ctx, "/cfg_dispatcher.CfgDispatcher/GetTransportConfigV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CfgDispatcherServer is the server API for CfgDispatcher service.
type CfgDispatcherServer interface {
	// 先放在这个服务，获取客户端grpc通道的配置接口
	GetGrpcEndpoints(context.Context, *GetGrpcEndpointsReq) (*GetGrpcEndpointsResp, error)
	GetTransportConfigV2(context.Context, *GetTransportConfigV2Request) (*GetTransportConfigV2Response, error)
}

func RegisterCfgDispatcherServer(s *grpc.Server, srv CfgDispatcherServer) {
	s.RegisterService(&_CfgDispatcher_serviceDesc, srv)
}

func _CfgDispatcher_GetGrpcEndpoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGrpcEndpointsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CfgDispatcherServer).GetGrpcEndpoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cfg_dispatcher.CfgDispatcher/GetGrpcEndpoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CfgDispatcherServer).GetGrpcEndpoints(ctx, req.(*GetGrpcEndpointsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CfgDispatcher_GetTransportConfigV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransportConfigV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CfgDispatcherServer).GetTransportConfigV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cfg_dispatcher.CfgDispatcher/GetTransportConfigV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CfgDispatcherServer).GetTransportConfigV2(ctx, req.(*GetTransportConfigV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

var _CfgDispatcher_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cfg_dispatcher.CfgDispatcher",
	HandlerType: (*CfgDispatcherServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGrpcEndpoints",
			Handler:    _CfgDispatcher_GetGrpcEndpoints_Handler,
		},
		{
			MethodName: "GetTransportConfigV2",
			Handler:    _CfgDispatcher_GetTransportConfigV2_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cfg-dispatcher/cfg-dispatcher.proto",
}

func init() {
	proto.RegisterFile("cfg-dispatcher/cfg-dispatcher.proto", fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39)
}

var fileDescriptor_cfg_dispatcher_69c9e5b1e6f2ae39 = []byte{
	// 1216 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x56, 0xdd, 0x72, 0xdb, 0x44,
	0x14, 0xb6, 0xec, 0xfc, 0xd4, 0xc7, 0x75, 0xa2, 0x6e, 0xda, 0x92, 0x71, 0x3b, 0xd0, 0xaa, 0x05,
	0x32, 0xb4, 0x55, 0x3a, 0x62, 0x18, 0x86, 0xa1, 0xfc, 0x28, 0xb6, 0xda, 0xa8, 0xb5, 0xe5, 0x20,
	0xdb, 0x49, 0xca, 0x8d, 0x46, 0x95, 0xd6, 0x8a, 0xa6, 0xb2, 0xa4, 0x4a, 0xeb, 0xd0, 0x72, 0xcf,
	0x0c, 0x6f, 0xc0, 0x15, 0x77, 0x5c, 0xf1, 0x00, 0x3c, 0x00, 0x57, 0x3c, 0x06, 0x17, 0xf0, 0x1e,
	0xcc, 0xee, 0x4a, 0xb6, 0x22, 0xbb, 0x6d, 0x98, 0xe1, 0x8a, 0x1b, 0xcd, 0xee, 0xb7, 0xdf, 0xf9,
	0x76, 0xcf, 0xd9, 0xb3, 0x47, 0x07, 0x6e, 0x39, 0x63, 0xef, 0x9e, 0xeb, 0xa7, 0xb1, 0x4d, 0x9c,
	0x13, 0x9c, 0xec, 0x9e, 0x9d, 0xca, 0x71, 0x12, 0x91, 0x08, 0x6d, 0x38, 0x63, 0xcf, 0x9a, 0xa3,
	0xad, 0x4d, 0x92, 0xd8, 0x61, 0x1a, 0x47, 0x09, 0xe1, 0x84, 0xd6, 0xfb, 0x5e, 0x12, 0x3b, 0xd6,
	0x0c, 0xb5, 0x9c, 0xb1, 0xb7, 0x3b, 0x9f, 0x9d, 0x2a, 0x9c, 0x26, 0xb9, 0xb0, 0xf5, 0x08, 0x93,
	0x47, 0x49, 0xec, 0x68, 0xa1, 0x1b, 0x47, 0x7e, 0x48, 0x52, 0x13, 0xbf, 0x40, 0x22, 0xd4, 0xa6,
	0xbe, 0xbb, 0x2d, 0xdc, 0x10, 0x76, 0x9a, 0x26, 0x1d, 0xa2, 0xcf, 0xa1, 0xe1, 0x04, 0x3e, 0x0e,
	0x89, 0x45, 0x5e, 0xc5, 0x78, 0xbb, 0x7a, 0x43, 0xd8, 0xd9, 0x50, 0x5a, 0xf2, 0xd9, 0x63, 0xc8,
	0x6d, 0x46, 0x19, 0xbe, 0x8a, 0xb1, 0x09, 0xce, 0x6c, 0x2c, 0x1d, 0xc2, 0xe5, 0xc5, 0x5d, 0xd2,
	0x18, 0x7d, 0x09, 0x62, 0xe1, 0x84, 0x51, 0x38, 0xf6, 0x3d, 0xb6, 0x67, 0x43, 0xd9, 0x92, 0x3d,
	0x5b, 0x1e, 0xe6, 0x6b, 0x6d, 0xb6, 0x64, 0xce, 0x9d, 0xe4, 0x80, 0xf4, 0xb3, 0x00, 0xeb, 0x6a,
	0x1c, 0xeb, 0xe1, 0x38, 0xfa, 0x8f, 0x8f, 0x8c, 0x3e, 0x80, 0x4d, 0x3b, 0x8e, 0xad, 0x53, 0x9c,
	0xa4, 0x7e, 0x14, 0x5a, 0x7e, 0x48, 0xb6, 0x6b, 0x4c, 0xba, 0x69, 0xc7, 0xf1, 0x21, 0x47, 0xf5,
	0x90, 0xa0, 0x6b, 0x50, 0x9f, 0xd8, 0xc9, 0x73, 0x4c, 0x2c, 0xdf, 0xdd, 0x5e, 0x61, 0x8c, 0x0b,
	0x1c, 0xd0, 0x5d, 0xe9, 0x1b, 0xb8, 0xf6, 0x08, 0x93, 0x92, 0x1b, 0x87, 0x8a, 0x89, 0x5f, 0x4c,
	0x71, 0x4a, 0x90, 0x02, 0x17, 0xe8, 0x1e, 0x7e, 0x38, 0x8e, 0x32, 0xb7, 0xdf, 0x29, 0x9f, 0x2e,
	0xf3, 0xce, 0x5c, 0xb7, 0xf9, 0x40, 0x7a, 0x09, 0xd7, 0x97, 0x4b, 0xa6, 0x71, 0x14, 0xa6, 0x18,
	0x1d, 0xc3, 0x56, 0x39, 0xa4, 0xd6, 0xa1, 0x92, 0xc9, 0xef, 0xd0, 0xa8, 0x2e, 0x26, 0x86, 0xbc,
	0x28, 0x77, 0x89, 0x94, 0x21, 0xe9, 0x43, 0x58, 0x1d, 0x38, 0x51, 0x8c, 0xd1, 0x06, 0x54, 0x3d,
	0x92, 0x05, 0xba, 0xea, 0x11, 0x3a, 0x0f, 0x08, 0x0b, 0x6f, 0xd3, 0xac, 0x06, 0x44, 0xfa, 0x53,
	0x80, 0xc6, 0xc8, 0x77, 0xf5, 0x90, 0xf4, 0xa8, 0x17, 0xe8, 0x2a, 0xac, 0xe2, 0x97, 0xb6, 0x93,
	0x99, 0xec, 0x57, 0x4c, 0x3e, 0x45, 0xf7, 0x60, 0x35, 0xa5, 0x82, 0xcc, 0xb4, 0xa1, 0x5c, 0x29,
	0xfb, 0xce, 0x76, 0xa3, 0x74, 0xc6, 0x42, 0x0f, 0x60, 0x6d, 0x12, 0xb9, 0xd3, 0x20, 0x62, 0x17,
	0xd1, 0x50, 0xa4, 0x32, 0xbf, 0xb0, 0xa7, 0xdc, 0x63, 0xcc, 0xfd, 0x8a, 0x99, 0xd9, 0xb4, 0xf6,
	0x60, 0x8d, 0x63, 0x68, 0x1b, 0xd6, 0x5d, 0xff, 0xd4, 0x4f, 0xa3, 0x24, 0xf3, 0x21, 0x9f, 0xa2,
	0x77, 0x01, 0x12, 0x3c, 0xb1, 0xfd, 0xd0, 0xc5, 0x49, 0xba, 0x5d, 0xbd, 0x51, 0xdb, 0x69, 0x9a,
	0x05, 0x64, 0xef, 0x02, 0xac, 0xd9, 0x0e, 0xf1, 0xa3, 0x50, 0xda, 0x03, 0xa4, 0x16, 0xd3, 0x80,
	0x3b, 0x2a, 0xce, 0x03, 0xb3, 0x5f, 0x61, 0xa1, 0x11, 0xe7, 0xa1, 0xa1, 0x48, 0x40, 0x0a, 0x1a,
	0xbf, 0x54, 0x01, 0xf2, 0xe7, 0x70, 0xa8, 0xd0, 0x63, 0xd9, 0xae, 0x9b, 0xe0, 0x34, 0x65, 0x0a,
	0x75, 0x33, 0x9f, 0xa2, 0x36, 0x00, 0x09, 0xd2, 0xfc, 0x7d, 0xf0, 0x60, 0xdd, 0x2e, 0x3b, 0x3f,
	0x57, 0x92, 0x87, 0xdd, 0x41, 0xf6, 0x60, 0xea, 0x24, 0x48, 0xf9, 0xb0, 0xf5, 0x9b, 0x00, 0xf5,
	0xd9, 0x02, 0xfa, 0x1a, 0x56, 0x26, 0x91, 0x8b, 0xd9, 0x4e, 0x1b, 0xca, 0xdd, 0xf3, 0x88, 0xd1,
	0x51, 0x2f, 0x72, 0xb1, 0xc9, 0x2c, 0xd1, 0x75, 0xa8, 0xdb, 0x53, 0x72, 0x12, 0x25, 0x3e, 0x79,
	0xc5, 0xce, 0x54, 0x37, 0xe7, 0x80, 0xa4, 0xc3, 0x7a, 0x46, 0x47, 0x97, 0x41, 0x1c, 0x76, 0x07,
	0x56, 0xaf, 0xdf, 0xd1, 0xac, 0x8e, 0x3e, 0x50, 0xf7, 0xba, 0x9a, 0x58, 0x41, 0x5b, 0xb0, 0x39,
	0x43, 0x07, 0x7a, 0xef, 0xa0, 0xab, 0x89, 0xc2, 0x19, 0xb0, 0x37, 0x1a, 0x8e, 0xd4, 0xae, 0x58,
	0x95, 0x7e, 0xaa, 0xc1, 0xc5, 0xfc, 0x3c, 0xe6, 0x34, 0xc0, 0xe8, 0x53, 0x58, 0x4d, 0xa6, 0x01,
	0xa6, 0x61, 0xaa, 0xed, 0x34, 0x94, 0x9b, 0xaf, 0x3b, 0x3c, 0x25, 0xcb, 0xf4, 0x63, 0x72, 0x7e,
	0xeb, 0x77, 0x5e, 0x2d, 0x98, 0xc8, 0xbd, 0x79, 0xb5, 0x68, 0x28, 0xd7, 0xde, 0x90, 0x49, 0xbc,
	0x94, 0xbc, 0x57, 0x2e, 0x25, 0x2c, 0x35, 0x0a, 0xe5, 0xe2, 0x4c, 0x19, 0xa8, 0xb1, 0xe5, 0x59,
	0x19, 0x40, 0x8f, 0x17, 0x6b, 0xc9, 0xca, 0xf2, 0x14, 0x5e, 0x4c, 0xaa, 0x52, 0xbd, 0x69, 0xfd,
	0x28, 0xc0, 0x0a, 0xf3, 0xe0, 0x2b, 0x5e, 0x3c, 0xa8, 0x6b, 0x99, 0x1b, 0xb7, 0xdf, 0x18, 0x89,
	0xcc, 0x73, 0x56, 0x49, 0x32, 0x81, 0x26, 0xce, 0x08, 0x56, 0xe0, 0xa7, 0x84, 0x79, 0xd5, 0x58,
	0x2c, 0x90, 0xf3, 0x64, 0x30, 0x2f, 0xe6, 0x06, 0x5d, 0x3f, 0x25, 0xd2, 0x0f, 0xeb, 0xb0, 0x4a,
	0x95, 0x52, 0x74, 0xff, 0xec, 0x95, 0x2c, 0x48, 0x30, 0x56, 0xf1, 0x2e, 0xd0, 0x13, 0x10, 0xbd,
	0xef, 0xfd, 0xd8, 0x7a, 0x16, 0xd8, 0xce, 0x73, 0x8b, 0x1b, 0x57, 0x97, 0xdf, 0x27, 0x37, 0x1e,
	0x90, 0xc4, 0x0f, 0x3d, 0x1e, 0x92, 0x0d, 0x6a, 0xba, 0x47, 0x2d, 0xcd, 0x33, 0x62, 0xdf, 0x9d,
	0xf8, 0x04, 0x67, 0x62, 0xb5, 0x7f, 0x25, 0x76, 0x44, 0x2d, 0xd9, 0x5a, 0xeb, 0x2f, 0x01, 0x1a,
	0x85, 0x75, 0xf4, 0x18, 0x60, 0x42, 0x07, 0xfc, 0xe6, 0xf9, 0x83, 0xb9, 0xf3, 0x56, 0x59, 0x99,
	0x7d, 0xd9, 0x5f, 0xa5, 0x3e, 0xc9, 0x87, 0x34, 0x8d, 0xb8, 0xd6, 0xa9, 0x1d, 0x4c, 0x71, 0xf6,
	0x6c, 0xb8, 0xfc, 0x21, 0x45, 0xa4, 0x13, 0xa8, 0xcf, 0x0c, 0x51, 0x0b, 0xae, 0xf6, 0xd4, 0x61,
	0x7b, 0xdf, 0x1a, 0x3e, 0x3d, 0xd0, 0xac, 0x91, 0x31, 0x38, 0xd0, 0xda, 0xfa, 0x43, 0x5d, 0xeb,
	0x88, 0x15, 0xfa, 0xaa, 0x0a, 0x6b, 0xda, 0xb1, 0xda, 0x1e, 0x8a, 0x02, 0xba, 0x02, 0x97, 0x0a,
	0xe8, 0x81, 0xa9, 0x3d, 0xd4, 0x8f, 0xc5, 0x2a, 0x42, 0xb0, 0x51, 0x80, 0xd5, 0x6e, 0x57, 0xac,
	0xb5, 0xfe, 0xa8, 0x65, 0x79, 0xf4, 0x59, 0x5e, 0x90, 0x32, 0xdf, 0x6e, 0xbe, 0xfe, 0xf2, 0x64,
	0x95, 0x11, 0xcd, 0xcc, 0x00, 0x7d, 0x51, 0x48, 0xc1, 0xea, 0xf2, 0x84, 0x2e, 0x1a, 0x97, 0x13,
	0xf0, 0x01, 0x35, 0xf7, 0xb9, 0xf9, 0xb9, 0xaf, 0x6b, 0xdd, 0x8e, 0x7d, 0x8a, 0xfe, 0x2f, 0x5e,
	0xb3, 0xd4, 0x86, 0x35, 0x1e, 0x53, 0x74, 0x15, 0x90, 0xda, 0x1e, 0xea, 0x7d, 0xa3, 0x74, 0xd1,
	0x22, 0x5c, 0xcc, 0x70, 0xb5, 0xdb, 0xed, 0x1f, 0x89, 0x02, 0xda, 0x84, 0x46, 0x86, 0x74, 0x34,
	0xe3, 0xa9, 0x58, 0xfd, 0xe8, 0x57, 0x01, 0x60, 0xde, 0xc5, 0xd0, 0x2a, 0xaa, 0x19, 0xa3, 0x9e,
	0xd5, 0x1e, 0x5a, 0x23, 0xe3, 0x89, 0xd1, 0x3f, 0x32, 0x78, 0xbd, 0xcd, 0x41, 0xd5, 0xe8, 0x98,
	0x7d, 0xbd, 0xc3, 0x95, 0x72, 0x50, 0xef, 0x0f, 0xc4, 0x2a, 0xcd, 0xaa, 0x1c, 0x38, 0xd2, 0x8d,
	0x83, 0xfd, 0xbe, 0xa1, 0x89, 0x35, 0x9a, 0x3e, 0x39, 0xda, 0x53, 0xdb, 0xfd, 0xc1, 0xb1, 0xb8,
	0x52, 0xd4, 0x3b, 0xd2, 0x8d, 0x4e, 0xff, 0x68, 0x20, 0xae, 0xa2, 0x4b, 0xd0, 0xcc, 0xc1, 0xae,
	0x6e, 0x8c, 0x8e, 0xc5, 0xb5, 0xe2, 0x16, 0x3d, 0xf5, 0x58, 0xdc, 0x54, 0xfe, 0x16, 0xa0, 0xd9,
	0x1e, 0x7b, 0x9d, 0x59, 0x94, 0x90, 0x05, 0x62, 0xb9, 0x39, 0x44, 0xb7, 0xca, 0xa1, 0x5c, 0xd2,
	0xa4, 0xb6, 0x6e, 0xbf, 0x9d, 0x94, 0xc6, 0x52, 0x05, 0xa5, 0xac, 0xfb, 0x5c, 0xe8, 0x71, 0xd0,
	0x9d, 0x25, 0xf6, 0xaf, 0xeb, 0xd5, 0x5a, 0x77, 0xcf, 0x47, 0xe6, 0x5d, 0x98, 0x54, 0xd9, 0x53,
	0xbe, 0xbd, 0xef, 0x45, 0x81, 0x1d, 0x7a, 0xf2, 0x27, 0x0a, 0x21, 0xb2, 0x13, 0x4d, 0x76, 0x59,
	0xc7, 0xed, 0x44, 0xc1, 0x6e, 0x8a, 0x93, 0x53, 0xdf, 0xc1, 0x69, 0xa9, 0xb5, 0x7f, 0xb6, 0xc6,
	0x18, 0x1f, 0xff, 0x13, 0x00, 0x00, 0xff, 0xff, 0x86, 0x66, 0x4e, 0x57, 0x02, 0x0c, 0x00, 0x00,
}
