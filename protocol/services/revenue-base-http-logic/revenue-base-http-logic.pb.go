// Code generated by protoc-gen-go. DO NOT EDIT.
// source: revenue-base-http-logic.proto

package revenue_base_http_logic // import "golang.52tt.com/protocol/services/revenue-base-http-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UserRecallBindStatus int32

const (
	UserRecallBindStatus_UserRecallBindStatus_Null      UserRecallBindStatus = 0
	UserRecallBindStatus_UserRecallBindStatus_NoBind    UserRecallBindStatus = 1
	UserRecallBindStatus_UserRecallBindStatus_Bind      UserRecallBindStatus = 2
	UserRecallBindStatus_UserRecallBindStatus_BindOther UserRecallBindStatus = 3
)

var UserRecallBindStatus_name = map[int32]string{
	0: "UserRecallBindStatus_Null",
	1: "UserRecallBindStatus_NoBind",
	2: "UserRecallBindStatus_Bind",
	3: "UserRecallBindStatus_BindOther",
}
var UserRecallBindStatus_value = map[string]int32{
	"UserRecallBindStatus_Null":      0,
	"UserRecallBindStatus_NoBind":    1,
	"UserRecallBindStatus_Bind":      2,
	"UserRecallBindStatus_BindOther": 3,
}

func (x UserRecallBindStatus) String() string {
	return proto.EnumName(UserRecallBindStatus_name, int32(x))
}
func (UserRecallBindStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{0}
}

type NumericRankReq_Type int32

const (
	NumericRankReq_TYPE_UNKNOWN NumericRankReq_Type = 0
	NumericRankReq_TYPE_CHARM   NumericRankReq_Type = 1
	NumericRankReq_TYPE_RICH    NumericRankReq_Type = 2
)

var NumericRankReq_Type_name = map[int32]string{
	0: "TYPE_UNKNOWN",
	1: "TYPE_CHARM",
	2: "TYPE_RICH",
}
var NumericRankReq_Type_value = map[string]int32{
	"TYPE_UNKNOWN": 0,
	"TYPE_CHARM":   1,
	"TYPE_RICH":    2,
}

func (x NumericRankReq_Type) String() string {
	return proto.EnumName(NumericRankReq_Type_name, int32(x))
}
func (NumericRankReq_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{32, 0}
}

type NumericRankReq_TimeSpan int32

const (
	NumericRankReq_TIME_SPAN_UNKNOWN NumericRankReq_TimeSpan = 0
	NumericRankReq_TIME_SPAN_DAY     NumericRankReq_TimeSpan = 1
	NumericRankReq_TIME_SPAN_WEEK    NumericRankReq_TimeSpan = 2
	NumericRankReq_TIME_SPAN_MONTH   NumericRankReq_TimeSpan = 3
)

var NumericRankReq_TimeSpan_name = map[int32]string{
	0: "TIME_SPAN_UNKNOWN",
	1: "TIME_SPAN_DAY",
	2: "TIME_SPAN_WEEK",
	3: "TIME_SPAN_MONTH",
}
var NumericRankReq_TimeSpan_value = map[string]int32{
	"TIME_SPAN_UNKNOWN": 0,
	"TIME_SPAN_DAY":     1,
	"TIME_SPAN_WEEK":    2,
	"TIME_SPAN_MONTH":   3,
}

func (x NumericRankReq_TimeSpan) String() string {
	return proto.EnumName(NumericRankReq_TimeSpan_name, int32(x))
}
func (NumericRankReq_TimeSpan) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{32, 1}
}

type GetTreasurePrivilegeHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTreasurePrivilegeHistoryReq) Reset()         { *m = GetTreasurePrivilegeHistoryReq{} }
func (m *GetTreasurePrivilegeHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetTreasurePrivilegeHistoryReq) ProtoMessage()    {}
func (*GetTreasurePrivilegeHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{0}
}
func (m *GetTreasurePrivilegeHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryReq.Unmarshal(m, b)
}
func (m *GetTreasurePrivilegeHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetTreasurePrivilegeHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTreasurePrivilegeHistoryReq.Merge(dst, src)
}
func (m *GetTreasurePrivilegeHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryReq.Size(m)
}
func (m *GetTreasurePrivilegeHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTreasurePrivilegeHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTreasurePrivilegeHistoryReq proto.InternalMessageInfo

func (m *GetTreasurePrivilegeHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetTreasurePrivilegeHistoryResp struct {
	HistoryList          []*TreasurePrivilegeHistory `protobuf:"bytes,1,rep,name=history_list,json=historyList,proto3" json:"history_list"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetTreasurePrivilegeHistoryResp) Reset()         { *m = GetTreasurePrivilegeHistoryResp{} }
func (m *GetTreasurePrivilegeHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetTreasurePrivilegeHistoryResp) ProtoMessage()    {}
func (*GetTreasurePrivilegeHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{1}
}
func (m *GetTreasurePrivilegeHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryResp.Unmarshal(m, b)
}
func (m *GetTreasurePrivilegeHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetTreasurePrivilegeHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTreasurePrivilegeHistoryResp.Merge(dst, src)
}
func (m *GetTreasurePrivilegeHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryResp.Size(m)
}
func (m *GetTreasurePrivilegeHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTreasurePrivilegeHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTreasurePrivilegeHistoryResp proto.InternalMessageInfo

func (m *GetTreasurePrivilegeHistoryResp) GetHistoryList() []*TreasurePrivilegeHistory {
	if m != nil {
		return m.HistoryList
	}
	return nil
}

type TreasurePrivilegeHistory struct {
	GiftIcon             string   `protobuf:"bytes,1,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftPrice            uint32   `protobuf:"varint,3,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price"`
	GainTime             uint32   `protobuf:"varint,4,opt,name=gain_time,json=gainTime,proto3" json:"gain_time"`
	FinTime              uint32   `protobuf:"varint,5,opt,name=fin_time,json=finTime,proto3" json:"fin_time"`
	FinSeconds           uint32   `protobuf:"varint,6,opt,name=fin_seconds,json=finSeconds,proto3" json:"fin_seconds"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TreasurePrivilegeHistory) Reset()         { *m = TreasurePrivilegeHistory{} }
func (m *TreasurePrivilegeHistory) String() string { return proto.CompactTextString(m) }
func (*TreasurePrivilegeHistory) ProtoMessage()    {}
func (*TreasurePrivilegeHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{2}
}
func (m *TreasurePrivilegeHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TreasurePrivilegeHistory.Unmarshal(m, b)
}
func (m *TreasurePrivilegeHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TreasurePrivilegeHistory.Marshal(b, m, deterministic)
}
func (dst *TreasurePrivilegeHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TreasurePrivilegeHistory.Merge(dst, src)
}
func (m *TreasurePrivilegeHistory) XXX_Size() int {
	return xxx_messageInfo_TreasurePrivilegeHistory.Size(m)
}
func (m *TreasurePrivilegeHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_TreasurePrivilegeHistory.DiscardUnknown(m)
}

var xxx_messageInfo_TreasurePrivilegeHistory proto.InternalMessageInfo

func (m *TreasurePrivilegeHistory) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *TreasurePrivilegeHistory) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *TreasurePrivilegeHistory) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *TreasurePrivilegeHistory) GetGainTime() uint32 {
	if m != nil {
		return m.GainTime
	}
	return 0
}

func (m *TreasurePrivilegeHistory) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *TreasurePrivilegeHistory) GetFinSeconds() uint32 {
	if m != nil {
		return m.FinSeconds
	}
	return 0
}

// -------------------- 用户召回协议定义 begin ----------------------------------
type EmptyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyResp) Reset()         { *m = EmptyResp{} }
func (m *EmptyResp) String() string { return proto.CompactTextString(m) }
func (*EmptyResp) ProtoMessage()    {}
func (*EmptyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{3}
}
func (m *EmptyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyResp.Unmarshal(m, b)
}
func (m *EmptyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyResp.Marshal(b, m, deterministic)
}
func (dst *EmptyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyResp.Merge(dst, src)
}
func (m *EmptyResp) XXX_Size() int {
	return xxx_messageInfo_EmptyResp.Size(m)
}
func (m *EmptyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyResp.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyResp proto.InternalMessageInfo

type SendRecallReq struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	RecallType           uint32   `protobuf:"varint,2,opt,name=recall_type,json=recallType,proto3" json:"recall_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendRecallReq) Reset()         { *m = SendRecallReq{} }
func (m *SendRecallReq) String() string { return proto.CompactTextString(m) }
func (*SendRecallReq) ProtoMessage()    {}
func (*SendRecallReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{4}
}
func (m *SendRecallReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendRecallReq.Unmarshal(m, b)
}
func (m *SendRecallReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendRecallReq.Marshal(b, m, deterministic)
}
func (dst *SendRecallReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendRecallReq.Merge(dst, src)
}
func (m *SendRecallReq) XXX_Size() int {
	return xxx_messageInfo_SendRecallReq.Size(m)
}
func (m *SendRecallReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendRecallReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendRecallReq proto.InternalMessageInfo

func (m *SendRecallReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendRecallReq) GetRecallType() uint32 {
	if m != nil {
		return m.RecallType
	}
	return 0
}

type GetRecallPrizeReq struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecallPrizeReq) Reset()         { *m = GetRecallPrizeReq{} }
func (m *GetRecallPrizeReq) String() string { return proto.CompactTextString(m) }
func (*GetRecallPrizeReq) ProtoMessage()    {}
func (*GetRecallPrizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{5}
}
func (m *GetRecallPrizeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallPrizeReq.Unmarshal(m, b)
}
func (m *GetRecallPrizeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallPrizeReq.Marshal(b, m, deterministic)
}
func (dst *GetRecallPrizeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallPrizeReq.Merge(dst, src)
}
func (m *GetRecallPrizeReq) XXX_Size() int {
	return xxx_messageInfo_GetRecallPrizeReq.Size(m)
}
func (m *GetRecallPrizeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallPrizeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallPrizeReq proto.InternalMessageInfo

func (m *GetRecallPrizeReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetRecallListResp struct {
	OpUserInfo           *UserProfile      `protobuf:"bytes,1,opt,name=op_user_info,json=opUserInfo,proto3" json:"op_user_info"`
	List                 []*RecallUserInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetRecallListResp) Reset()         { *m = GetRecallListResp{} }
func (m *GetRecallListResp) String() string { return proto.CompactTextString(m) }
func (*GetRecallListResp) ProtoMessage()    {}
func (*GetRecallListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{6}
}
func (m *GetRecallListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallListResp.Unmarshal(m, b)
}
func (m *GetRecallListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallListResp.Marshal(b, m, deterministic)
}
func (dst *GetRecallListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallListResp.Merge(dst, src)
}
func (m *GetRecallListResp) XXX_Size() int {
	return xxx_messageInfo_GetRecallListResp.Size(m)
}
func (m *GetRecallListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallListResp proto.InternalMessageInfo

func (m *GetRecallListResp) GetOpUserInfo() *UserProfile {
	if m != nil {
		return m.OpUserInfo
	}
	return nil
}

func (m *GetRecallListResp) GetList() []*RecallUserInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type RecallUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	Alias                string   `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex"`
	Nickname             string   `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname"`
	Status               uint32   `protobuf:"varint,6,opt,name=status,proto3" json:"status"`
	FellowType           uint32   `protobuf:"varint,7,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type"`
	Content              string   `protobuf:"bytes,8,opt,name=content,proto3" json:"content"`
	GiftPrice            uint32   `protobuf:"varint,9,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price"`
	GiftCnt              uint32   `protobuf:"varint,10,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt"`
	GiftName             string   `protobuf:"bytes,11,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftIconUrl          string   `protobuf:"bytes,12,opt,name=gift_icon_url,json=giftIconUrl,proto3" json:"gift_icon_url"`
	InviteToken          string   `protobuf:"bytes,13,opt,name=invite_token,json=inviteToken,proto3" json:"invite_token"`
	Remark               string   `protobuf:"bytes,14,opt,name=remark,proto3" json:"remark"`
	InviteUrl            string   `protobuf:"bytes,15,opt,name=invite_url,json=inviteUrl,proto3" json:"invite_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallUserInfo) Reset()         { *m = RecallUserInfo{} }
func (m *RecallUserInfo) String() string { return proto.CompactTextString(m) }
func (*RecallUserInfo) ProtoMessage()    {}
func (*RecallUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{7}
}
func (m *RecallUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallUserInfo.Unmarshal(m, b)
}
func (m *RecallUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallUserInfo.Marshal(b, m, deterministic)
}
func (dst *RecallUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallUserInfo.Merge(dst, src)
}
func (m *RecallUserInfo) XXX_Size() int {
	return xxx_messageInfo_RecallUserInfo.Size(m)
}
func (m *RecallUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecallUserInfo proto.InternalMessageInfo

func (m *RecallUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecallUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *RecallUserInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *RecallUserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *RecallUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *RecallUserInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *RecallUserInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *RecallUserInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *RecallUserInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *RecallUserInfo) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

func (m *RecallUserInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *RecallUserInfo) GetGiftIconUrl() string {
	if m != nil {
		return m.GiftIconUrl
	}
	return ""
}

func (m *RecallUserInfo) GetInviteToken() string {
	if m != nil {
		return m.InviteToken
	}
	return ""
}

func (m *RecallUserInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *RecallUserInfo) GetInviteUrl() string {
	if m != nil {
		return m.InviteUrl
	}
	return ""
}

type UserProfile struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname"`
	AccountAlias         string   `protobuf:"bytes,4,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias"`
	Sex                  uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserProfile) Reset()         { *m = UserProfile{} }
func (m *UserProfile) String() string { return proto.CompactTextString(m) }
func (*UserProfile) ProtoMessage()    {}
func (*UserProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{8}
}
func (m *UserProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProfile.Unmarshal(m, b)
}
func (m *UserProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProfile.Marshal(b, m, deterministic)
}
func (dst *UserProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProfile.Merge(dst, src)
}
func (m *UserProfile) XXX_Size() int {
	return xxx_messageInfo_UserProfile.Size(m)
}
func (m *UserProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UserProfile proto.InternalMessageInfo

func (m *UserProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserProfile) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *UserProfile) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type GetInviteInfoReq struct {
	InviteTtid           string   `protobuf:"bytes,1,opt,name=invite_ttid,json=inviteTtid,proto3" json:"invite_ttid"`
	ToTtid               string   `protobuf:"bytes,2,opt,name=to_ttid,json=toTtid,proto3" json:"to_ttid"`
	Token                string   `protobuf:"bytes,3,opt,name=token,proto3" json:"token"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInviteInfoReq) Reset()         { *m = GetInviteInfoReq{} }
func (m *GetInviteInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetInviteInfoReq) ProtoMessage()    {}
func (*GetInviteInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{9}
}
func (m *GetInviteInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInviteInfoReq.Unmarshal(m, b)
}
func (m *GetInviteInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInviteInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetInviteInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInviteInfoReq.Merge(dst, src)
}
func (m *GetInviteInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetInviteInfoReq.Size(m)
}
func (m *GetInviteInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInviteInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInviteInfoReq proto.InternalMessageInfo

func (m *GetInviteInfoReq) GetInviteTtid() string {
	if m != nil {
		return m.InviteTtid
	}
	return ""
}

func (m *GetInviteInfoReq) GetToTtid() string {
	if m != nil {
		return m.ToTtid
	}
	return ""
}

func (m *GetInviteInfoReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type GetInviteInfoResp struct {
	InviteUserInfo       *UserInfo   `protobuf:"bytes,1,opt,name=invite_user_info,json=inviteUserInfo,proto3" json:"invite_user_info"`
	ToUserInfo           *UserInfo   `protobuf:"bytes,2,opt,name=to_user_info,json=toUserInfo,proto3" json:"to_user_info"`
	GiftList             []*GiftInfo `protobuf:"bytes,3,rep,name=gift_list,json=giftList,proto3" json:"gift_list"`
	Status               uint32      `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetInviteInfoResp) Reset()         { *m = GetInviteInfoResp{} }
func (m *GetInviteInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetInviteInfoResp) ProtoMessage()    {}
func (*GetInviteInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{10}
}
func (m *GetInviteInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInviteInfoResp.Unmarshal(m, b)
}
func (m *GetInviteInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInviteInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetInviteInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInviteInfoResp.Merge(dst, src)
}
func (m *GetInviteInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetInviteInfoResp.Size(m)
}
func (m *GetInviteInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInviteInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInviteInfoResp proto.InternalMessageInfo

func (m *GetInviteInfoResp) GetInviteUserInfo() *UserInfo {
	if m != nil {
		return m.InviteUserInfo
	}
	return nil
}

func (m *GetInviteInfoResp) GetToUserInfo() *UserInfo {
	if m != nil {
		return m.ToUserInfo
	}
	return nil
}

func (m *GetInviteInfoResp) GetGiftList() []*GiftInfo {
	if m != nil {
		return m.GiftList
	}
	return nil
}

func (m *GetInviteInfoResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type UserInfo struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname"`
	AccountAlias         string   `protobuf:"bytes,3,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{11}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserInfo) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

type GiftInfo struct {
	GiftIcon             string   `protobuf:"bytes,1,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftPrice            uint32   `protobuf:"varint,3,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price"`
	GiftCnt              uint32   `protobuf:"varint,4,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt"`
	NobLevel             uint32   `protobuf:"varint,5,opt,name=nob_level,json=nobLevel,proto3" json:"nob_level"`
	ExpireDay            uint32   `protobuf:"varint,6,opt,name=expire_day,json=expireDay,proto3" json:"expire_day"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftInfo) Reset()         { *m = GiftInfo{} }
func (m *GiftInfo) String() string { return proto.CompactTextString(m) }
func (*GiftInfo) ProtoMessage()    {}
func (*GiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{12}
}
func (m *GiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftInfo.Unmarshal(m, b)
}
func (m *GiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftInfo.Marshal(b, m, deterministic)
}
func (dst *GiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftInfo.Merge(dst, src)
}
func (m *GiftInfo) XXX_Size() int {
	return xxx_messageInfo_GiftInfo.Size(m)
}
func (m *GiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GiftInfo proto.InternalMessageInfo

func (m *GiftInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *GiftInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *GiftInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *GiftInfo) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

func (m *GiftInfo) GetNobLevel() uint32 {
	if m != nil {
		return m.NobLevel
	}
	return 0
}

func (m *GiftInfo) GetExpireDay() uint32 {
	if m != nil {
		return m.ExpireDay
	}
	return 0
}

type GetBindResp struct {
	BindUid              uint32   `protobuf:"varint,1,opt,name=bind_uid,json=bindUid,proto3" json:"bind_uid"`
	BindNick             string   `protobuf:"bytes,2,opt,name=bind_nick,json=bindNick,proto3" json:"bind_nick"`
	BindAccount          string   `protobuf:"bytes,3,opt,name=bind_account,json=bindAccount,proto3" json:"bind_account"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBindResp) Reset()         { *m = GetBindResp{} }
func (m *GetBindResp) String() string { return proto.CompactTextString(m) }
func (*GetBindResp) ProtoMessage()    {}
func (*GetBindResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{13}
}
func (m *GetBindResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindResp.Unmarshal(m, b)
}
func (m *GetBindResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindResp.Marshal(b, m, deterministic)
}
func (dst *GetBindResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindResp.Merge(dst, src)
}
func (m *GetBindResp) XXX_Size() int {
	return xxx_messageInfo_GetBindResp.Size(m)
}
func (m *GetBindResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindResp proto.InternalMessageInfo

func (m *GetBindResp) GetBindUid() uint32 {
	if m != nil {
		return m.BindUid
	}
	return 0
}

func (m *GetBindResp) GetBindNick() string {
	if m != nil {
		return m.BindNick
	}
	return ""
}

func (m *GetBindResp) GetBindAccount() string {
	if m != nil {
		return m.BindAccount
	}
	return ""
}

type GetUserInfoReq struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoReq) Reset()         { *m = GetUserInfoReq{} }
func (m *GetUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoReq) ProtoMessage()    {}
func (*GetUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{14}
}
func (m *GetUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoReq.Unmarshal(m, b)
}
func (m *GetUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoReq.Merge(dst, src)
}
func (m *GetUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoReq.Size(m)
}
func (m *GetUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoReq proto.InternalMessageInfo

func (m *GetUserInfoReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type GetUserInfoResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Nick                 string   `protobuf:"bytes,2,opt,name=nick,proto3" json:"nick"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInfoResp) Reset()         { *m = GetUserInfoResp{} }
func (m *GetUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserInfoResp) ProtoMessage()    {}
func (*GetUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{15}
}
func (m *GetUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInfoResp.Unmarshal(m, b)
}
func (m *GetUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInfoResp.Merge(dst, src)
}
func (m *GetUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserInfoResp.Size(m)
}
func (m *GetUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInfoResp proto.InternalMessageInfo

func (m *GetUserInfoResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserInfoResp) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

func (m *GetUserInfoResp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type BindReq struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindReq) Reset()         { *m = BindReq{} }
func (m *BindReq) String() string { return proto.CompactTextString(m) }
func (*BindReq) ProtoMessage()    {}
func (*BindReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{16}
}
func (m *BindReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindReq.Unmarshal(m, b)
}
func (m *BindReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindReq.Marshal(b, m, deterministic)
}
func (dst *BindReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindReq.Merge(dst, src)
}
func (m *BindReq) XXX_Size() int {
	return xxx_messageInfo_BindReq.Size(m)
}
func (m *BindReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BindReq.DiscardUnknown(m)
}

var xxx_messageInfo_BindReq proto.InternalMessageInfo

func (m *BindReq) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

// -------------------- VIP begin ----------------------------------
// 获取用户vip礼包入口信息
type GetUserVipGiftPackageEntranceReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserVipGiftPackageEntranceReq) Reset()         { *m = GetUserVipGiftPackageEntranceReq{} }
func (m *GetUserVipGiftPackageEntranceReq) String() string { return proto.CompactTextString(m) }
func (*GetUserVipGiftPackageEntranceReq) ProtoMessage()    {}
func (*GetUserVipGiftPackageEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{17}
}
func (m *GetUserVipGiftPackageEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVipGiftPackageEntranceReq.Unmarshal(m, b)
}
func (m *GetUserVipGiftPackageEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVipGiftPackageEntranceReq.Marshal(b, m, deterministic)
}
func (dst *GetUserVipGiftPackageEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVipGiftPackageEntranceReq.Merge(dst, src)
}
func (m *GetUserVipGiftPackageEntranceReq) XXX_Size() int {
	return xxx_messageInfo_GetUserVipGiftPackageEntranceReq.Size(m)
}
func (m *GetUserVipGiftPackageEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVipGiftPackageEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVipGiftPackageEntranceReq proto.InternalMessageInfo

type GetUserVipGiftPackageEntranceResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	RichVal              uint64   `protobuf:"varint,2,opt,name=rich_val,json=richVal,proto3" json:"rich_val"`
	CurVipLevel          uint32   `protobuf:"varint,3,opt,name=cur_vip_level,json=curVipLevel,proto3" json:"cur_vip_level"`
	IsConditionMet       bool     `protobuf:"varint,4,opt,name=is_condition_met,json=isConditionMet,proto3" json:"is_condition_met"`
	ShowRedDot           bool     `protobuf:"varint,5,opt,name=show_red_dot,json=showRedDot,proto3" json:"show_red_dot"`
	IsBlocked            bool     `protobuf:"varint,6,opt,name=is_blocked,json=isBlocked,proto3" json:"is_blocked"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserVipGiftPackageEntranceResp) Reset()         { *m = GetUserVipGiftPackageEntranceResp{} }
func (m *GetUserVipGiftPackageEntranceResp) String() string { return proto.CompactTextString(m) }
func (*GetUserVipGiftPackageEntranceResp) ProtoMessage()    {}
func (*GetUserVipGiftPackageEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{18}
}
func (m *GetUserVipGiftPackageEntranceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVipGiftPackageEntranceResp.Unmarshal(m, b)
}
func (m *GetUserVipGiftPackageEntranceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVipGiftPackageEntranceResp.Marshal(b, m, deterministic)
}
func (dst *GetUserVipGiftPackageEntranceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVipGiftPackageEntranceResp.Merge(dst, src)
}
func (m *GetUserVipGiftPackageEntranceResp) XXX_Size() int {
	return xxx_messageInfo_GetUserVipGiftPackageEntranceResp.Size(m)
}
func (m *GetUserVipGiftPackageEntranceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVipGiftPackageEntranceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVipGiftPackageEntranceResp proto.InternalMessageInfo

func (m *GetUserVipGiftPackageEntranceResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserVipGiftPackageEntranceResp) GetRichVal() uint64 {
	if m != nil {
		return m.RichVal
	}
	return 0
}

func (m *GetUserVipGiftPackageEntranceResp) GetCurVipLevel() uint32 {
	if m != nil {
		return m.CurVipLevel
	}
	return 0
}

func (m *GetUserVipGiftPackageEntranceResp) GetIsConditionMet() bool {
	if m != nil {
		return m.IsConditionMet
	}
	return false
}

func (m *GetUserVipGiftPackageEntranceResp) GetShowRedDot() bool {
	if m != nil {
		return m.ShowRedDot
	}
	return false
}

func (m *GetUserVipGiftPackageEntranceResp) GetIsBlocked() bool {
	if m != nil {
		return m.IsBlocked
	}
	return false
}

// 获取用户vip礼包信息
type GetUserVipGiftPackageInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserVipGiftPackageInfoReq) Reset()         { *m = GetUserVipGiftPackageInfoReq{} }
func (m *GetUserVipGiftPackageInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserVipGiftPackageInfoReq) ProtoMessage()    {}
func (*GetUserVipGiftPackageInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{19}
}
func (m *GetUserVipGiftPackageInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVipGiftPackageInfoReq.Unmarshal(m, b)
}
func (m *GetUserVipGiftPackageInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVipGiftPackageInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserVipGiftPackageInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVipGiftPackageInfoReq.Merge(dst, src)
}
func (m *GetUserVipGiftPackageInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserVipGiftPackageInfoReq.Size(m)
}
func (m *GetUserVipGiftPackageInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVipGiftPackageInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVipGiftPackageInfoReq proto.InternalMessageInfo

type GetUserVipGiftPackageInfoResp struct {
	Uid              uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	RichVal          uint64 `protobuf:"varint,2,opt,name=rich_val,json=richVal,proto3" json:"rich_val"`
	CurVipLevel      uint32 `protobuf:"varint,3,opt,name=cur_vip_level,json=curVipLevel,proto3" json:"cur_vip_level"`
	Vip5RichVal      uint64 `protobuf:"varint,5,opt,name=vip5_rich_val,json=vip5RichVal,proto3" json:"vip5_rich_val"`
	IsConditionMet   bool   `protobuf:"varint,6,opt,name=is_condition_met,json=isConditionMet,proto3" json:"is_condition_met"`
	IsFirstMet       bool   `protobuf:"varint,7,opt,name=is_first_met,json=isFirstMet,proto3" json:"is_first_met"`
	IsReceived       bool   `protobuf:"varint,8,opt,name=is_received,json=isReceived,proto3" json:"is_received"`
	IsReceivedFromOp bool   `protobuf:"varint,9,opt,name=is_received_from_op,json=isReceivedFromOp,proto3" json:"is_received_from_op"`
	// 待领取状态
	VipGiftList             []*VipGiftPackageItem `protobuf:"bytes,10,rep,name=vip_gift_list,json=vipGiftList,proto3" json:"vip_gift_list"`
	AvailableGiftTotalPrice uint64                `protobuf:"varint,11,opt,name=available_gift_total_price,json=availableGiftTotalPrice,proto3" json:"available_gift_total_price"`
	// 已领取状态
	ReceivedGiftList       []*VipGiftPackageItem `protobuf:"bytes,12,rep,name=received_gift_list,json=receivedGiftList,proto3" json:"received_gift_list"`
	ReceivedGiftTotalPrice uint64                `protobuf:"varint,13,opt,name=received_gift_total_price,json=receivedGiftTotalPrice,proto3" json:"received_gift_total_price"`
	ReceivedTime           uint64                `protobuf:"varint,14,opt,name=received_time,json=receivedTime,proto3" json:"received_time"`
	ConfirmText            string                `protobuf:"bytes,15,opt,name=confirm_text,json=confirmText,proto3" json:"confirm_text"`
	Username               string                `protobuf:"bytes,16,opt,name=username,proto3" json:"username"`
	Nickname               string                `protobuf:"bytes,17,opt,name=nickname,proto3" json:"nickname"`
	XXX_NoUnkeyedLiteral   struct{}              `json:"-"`
	XXX_unrecognized       []byte                `json:"-"`
	XXX_sizecache          int32                 `json:"-"`
}

func (m *GetUserVipGiftPackageInfoResp) Reset()         { *m = GetUserVipGiftPackageInfoResp{} }
func (m *GetUserVipGiftPackageInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserVipGiftPackageInfoResp) ProtoMessage()    {}
func (*GetUserVipGiftPackageInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{20}
}
func (m *GetUserVipGiftPackageInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVipGiftPackageInfoResp.Unmarshal(m, b)
}
func (m *GetUserVipGiftPackageInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVipGiftPackageInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserVipGiftPackageInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVipGiftPackageInfoResp.Merge(dst, src)
}
func (m *GetUserVipGiftPackageInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserVipGiftPackageInfoResp.Size(m)
}
func (m *GetUserVipGiftPackageInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVipGiftPackageInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVipGiftPackageInfoResp proto.InternalMessageInfo

func (m *GetUserVipGiftPackageInfoResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserVipGiftPackageInfoResp) GetRichVal() uint64 {
	if m != nil {
		return m.RichVal
	}
	return 0
}

func (m *GetUserVipGiftPackageInfoResp) GetCurVipLevel() uint32 {
	if m != nil {
		return m.CurVipLevel
	}
	return 0
}

func (m *GetUserVipGiftPackageInfoResp) GetVip5RichVal() uint64 {
	if m != nil {
		return m.Vip5RichVal
	}
	return 0
}

func (m *GetUserVipGiftPackageInfoResp) GetIsConditionMet() bool {
	if m != nil {
		return m.IsConditionMet
	}
	return false
}

func (m *GetUserVipGiftPackageInfoResp) GetIsFirstMet() bool {
	if m != nil {
		return m.IsFirstMet
	}
	return false
}

func (m *GetUserVipGiftPackageInfoResp) GetIsReceived() bool {
	if m != nil {
		return m.IsReceived
	}
	return false
}

func (m *GetUserVipGiftPackageInfoResp) GetIsReceivedFromOp() bool {
	if m != nil {
		return m.IsReceivedFromOp
	}
	return false
}

func (m *GetUserVipGiftPackageInfoResp) GetVipGiftList() []*VipGiftPackageItem {
	if m != nil {
		return m.VipGiftList
	}
	return nil
}

func (m *GetUserVipGiftPackageInfoResp) GetAvailableGiftTotalPrice() uint64 {
	if m != nil {
		return m.AvailableGiftTotalPrice
	}
	return 0
}

func (m *GetUserVipGiftPackageInfoResp) GetReceivedGiftList() []*VipGiftPackageItem {
	if m != nil {
		return m.ReceivedGiftList
	}
	return nil
}

func (m *GetUserVipGiftPackageInfoResp) GetReceivedGiftTotalPrice() uint64 {
	if m != nil {
		return m.ReceivedGiftTotalPrice
	}
	return 0
}

func (m *GetUserVipGiftPackageInfoResp) GetReceivedTime() uint64 {
	if m != nil {
		return m.ReceivedTime
	}
	return 0
}

func (m *GetUserVipGiftPackageInfoResp) GetConfirmText() string {
	if m != nil {
		return m.ConfirmText
	}
	return ""
}

func (m *GetUserVipGiftPackageInfoResp) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *GetUserVipGiftPackageInfoResp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

// 礼包礼物信息
type VipGiftPackageItem struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	GiftCount            uint32   `protobuf:"varint,3,opt,name=gift_count,json=giftCount,proto3" json:"gift_count"`
	GiftPrice            uint64   `protobuf:"varint,4,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price"`
	GiftIcon             string   `protobuf:"bytes,5,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon"`
	Ranking              uint32   `protobuf:"varint,6,opt,name=ranking,proto3" json:"ranking"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VipGiftPackageItem) Reset()         { *m = VipGiftPackageItem{} }
func (m *VipGiftPackageItem) String() string { return proto.CompactTextString(m) }
func (*VipGiftPackageItem) ProtoMessage()    {}
func (*VipGiftPackageItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{21}
}
func (m *VipGiftPackageItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipGiftPackageItem.Unmarshal(m, b)
}
func (m *VipGiftPackageItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipGiftPackageItem.Marshal(b, m, deterministic)
}
func (dst *VipGiftPackageItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipGiftPackageItem.Merge(dst, src)
}
func (m *VipGiftPackageItem) XXX_Size() int {
	return xxx_messageInfo_VipGiftPackageItem.Size(m)
}
func (m *VipGiftPackageItem) XXX_DiscardUnknown() {
	xxx_messageInfo_VipGiftPackageItem.DiscardUnknown(m)
}

var xxx_messageInfo_VipGiftPackageItem proto.InternalMessageInfo

func (m *VipGiftPackageItem) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *VipGiftPackageItem) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *VipGiftPackageItem) GetGiftCount() uint32 {
	if m != nil {
		return m.GiftCount
	}
	return 0
}

func (m *VipGiftPackageItem) GetGiftPrice() uint64 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *VipGiftPackageItem) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *VipGiftPackageItem) GetRanking() uint32 {
	if m != nil {
		return m.Ranking
	}
	return 0
}

// 提交领取礼包
type SubmitReceiveVipGiftPackageReq struct {
	GiftList             []*VipGiftPackageItem `protobuf:"bytes,1,rep,name=gift_list,json=giftList,proto3" json:"gift_list"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SubmitReceiveVipGiftPackageReq) Reset()         { *m = SubmitReceiveVipGiftPackageReq{} }
func (m *SubmitReceiveVipGiftPackageReq) String() string { return proto.CompactTextString(m) }
func (*SubmitReceiveVipGiftPackageReq) ProtoMessage()    {}
func (*SubmitReceiveVipGiftPackageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{22}
}
func (m *SubmitReceiveVipGiftPackageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitReceiveVipGiftPackageReq.Unmarshal(m, b)
}
func (m *SubmitReceiveVipGiftPackageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitReceiveVipGiftPackageReq.Marshal(b, m, deterministic)
}
func (dst *SubmitReceiveVipGiftPackageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitReceiveVipGiftPackageReq.Merge(dst, src)
}
func (m *SubmitReceiveVipGiftPackageReq) XXX_Size() int {
	return xxx_messageInfo_SubmitReceiveVipGiftPackageReq.Size(m)
}
func (m *SubmitReceiveVipGiftPackageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitReceiveVipGiftPackageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitReceiveVipGiftPackageReq proto.InternalMessageInfo

func (m *SubmitReceiveVipGiftPackageReq) GetGiftList() []*VipGiftPackageItem {
	if m != nil {
		return m.GiftList
	}
	return nil
}

type SubmitReceiveVipGiftPackageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitReceiveVipGiftPackageResp) Reset()         { *m = SubmitReceiveVipGiftPackageResp{} }
func (m *SubmitReceiveVipGiftPackageResp) String() string { return proto.CompactTextString(m) }
func (*SubmitReceiveVipGiftPackageResp) ProtoMessage()    {}
func (*SubmitReceiveVipGiftPackageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{23}
}
func (m *SubmitReceiveVipGiftPackageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitReceiveVipGiftPackageResp.Unmarshal(m, b)
}
func (m *SubmitReceiveVipGiftPackageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitReceiveVipGiftPackageResp.Marshal(b, m, deterministic)
}
func (dst *SubmitReceiveVipGiftPackageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitReceiveVipGiftPackageResp.Merge(dst, src)
}
func (m *SubmitReceiveVipGiftPackageResp) XXX_Size() int {
	return xxx_messageInfo_SubmitReceiveVipGiftPackageResp.Size(m)
}
func (m *SubmitReceiveVipGiftPackageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitReceiveVipGiftPackageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitReceiveVipGiftPackageResp proto.InternalMessageInfo

// -------------------- 神秘人 begin ----------------------------------
type BuyYKWReq struct {
	Uid                        string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	RequestId                  string   `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id"`
	ClientVersion              uint32   `protobuf:"varint,3,opt,name=client_version,json=clientVersion,proto3" json:"client_version"`
	ClientType                 uint32   `protobuf:"varint,4,opt,name=client_type,json=clientType,proto3" json:"client_type"`
	FaceAuthToken              string   `protobuf:"bytes,5,opt,name=face_auth_token,json=faceAuthToken,proto3" json:"face_auth_token"`
	DeviceId                   string   `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	FaceAuthProviderCode       string   `protobuf:"bytes,7,opt,name=face_auth_provider_code,json=faceAuthProviderCode,proto3" json:"face_auth_provider_code"`
	OrderTypeId                uint32   `protobuf:"varint,8,opt,name=order_type_id,json=orderTypeId,proto3" json:"order_type_id"`
	FaceAuthProviderResultData string   `protobuf:"bytes,9,opt,name=face_auth_provider_result_data,json=faceAuthProviderResultData,proto3" json:"face_auth_provider_result_data"`
	FaceAuthResultToken        string   `protobuf:"bytes,10,opt,name=face_auth_result_token,json=faceAuthResultToken,proto3" json:"face_auth_result_token"`
	XXX_NoUnkeyedLiteral       struct{} `json:"-"`
	XXX_unrecognized           []byte   `json:"-"`
	XXX_sizecache              int32    `json:"-"`
}

func (m *BuyYKWReq) Reset()         { *m = BuyYKWReq{} }
func (m *BuyYKWReq) String() string { return proto.CompactTextString(m) }
func (*BuyYKWReq) ProtoMessage()    {}
func (*BuyYKWReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{24}
}
func (m *BuyYKWReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyYKWReq.Unmarshal(m, b)
}
func (m *BuyYKWReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyYKWReq.Marshal(b, m, deterministic)
}
func (dst *BuyYKWReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyYKWReq.Merge(dst, src)
}
func (m *BuyYKWReq) XXX_Size() int {
	return xxx_messageInfo_BuyYKWReq.Size(m)
}
func (m *BuyYKWReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyYKWReq.DiscardUnknown(m)
}

var xxx_messageInfo_BuyYKWReq proto.InternalMessageInfo

func (m *BuyYKWReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *BuyYKWReq) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *BuyYKWReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *BuyYKWReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *BuyYKWReq) GetFaceAuthToken() string {
	if m != nil {
		return m.FaceAuthToken
	}
	return ""
}

func (m *BuyYKWReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *BuyYKWReq) GetFaceAuthProviderCode() string {
	if m != nil {
		return m.FaceAuthProviderCode
	}
	return ""
}

func (m *BuyYKWReq) GetOrderTypeId() uint32 {
	if m != nil {
		return m.OrderTypeId
	}
	return 0
}

func (m *BuyYKWReq) GetFaceAuthProviderResultData() string {
	if m != nil {
		return m.FaceAuthProviderResultData
	}
	return ""
}

func (m *BuyYKWReq) GetFaceAuthResultToken() string {
	if m != nil {
		return m.FaceAuthResultToken
	}
	return ""
}

type BuyYKWResp struct {
	AuthScene            uint32   `protobuf:"varint,1,opt,name=auth_scene,json=authScene,proto3" json:"auth_scene"`
	RequestId            string   `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id"`
	FaceAuthContextJson  string   `protobuf:"bytes,3,opt,name=face_auth_context_json,json=faceAuthContextJson,proto3" json:"face_auth_context_json"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyYKWResp) Reset()         { *m = BuyYKWResp{} }
func (m *BuyYKWResp) String() string { return proto.CompactTextString(m) }
func (*BuyYKWResp) ProtoMessage()    {}
func (*BuyYKWResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{25}
}
func (m *BuyYKWResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyYKWResp.Unmarshal(m, b)
}
func (m *BuyYKWResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyYKWResp.Marshal(b, m, deterministic)
}
func (dst *BuyYKWResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyYKWResp.Merge(dst, src)
}
func (m *BuyYKWResp) XXX_Size() int {
	return xxx_messageInfo_BuyYKWResp.Size(m)
}
func (m *BuyYKWResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyYKWResp.DiscardUnknown(m)
}

var xxx_messageInfo_BuyYKWResp proto.InternalMessageInfo

func (m *BuyYKWResp) GetAuthScene() uint32 {
	if m != nil {
		return m.AuthScene
	}
	return 0
}

func (m *BuyYKWResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *BuyYKWResp) GetFaceAuthContextJson() string {
	if m != nil {
		return m.FaceAuthContextJson
	}
	return ""
}

type YKWConfInfo struct {
	Price                uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YKWConfInfo) Reset()         { *m = YKWConfInfo{} }
func (m *YKWConfInfo) String() string { return proto.CompactTextString(m) }
func (*YKWConfInfo) ProtoMessage()    {}
func (*YKWConfInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{26}
}
func (m *YKWConfInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YKWConfInfo.Unmarshal(m, b)
}
func (m *YKWConfInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YKWConfInfo.Marshal(b, m, deterministic)
}
func (dst *YKWConfInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YKWConfInfo.Merge(dst, src)
}
func (m *YKWConfInfo) XXX_Size() int {
	return xxx_messageInfo_YKWConfInfo.Size(m)
}
func (m *YKWConfInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_YKWConfInfo.DiscardUnknown(m)
}

var xxx_messageInfo_YKWConfInfo proto.InternalMessageInfo

func (m *YKWConfInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

// 神秘人订单类型信息
type YKWOrderType struct {
	OrderTypeId          uint32   `protobuf:"varint,1,opt,name=order_type_id,json=orderTypeId,proto3" json:"order_type_id"`
	Price                uint32   `protobuf:"varint,2,opt,name=price,proto3" json:"price"`
	EffectiveTime        uint32   `protobuf:"varint,3,opt,name=effective_time,json=effectiveTime,proto3" json:"effective_time"`
	Discount             string   `protobuf:"bytes,4,opt,name=discount,proto3" json:"discount"`
	OriginalPrice        uint32   `protobuf:"varint,5,opt,name=original_price,json=originalPrice,proto3" json:"original_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YKWOrderType) Reset()         { *m = YKWOrderType{} }
func (m *YKWOrderType) String() string { return proto.CompactTextString(m) }
func (*YKWOrderType) ProtoMessage()    {}
func (*YKWOrderType) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{27}
}
func (m *YKWOrderType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YKWOrderType.Unmarshal(m, b)
}
func (m *YKWOrderType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YKWOrderType.Marshal(b, m, deterministic)
}
func (dst *YKWOrderType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YKWOrderType.Merge(dst, src)
}
func (m *YKWOrderType) XXX_Size() int {
	return xxx_messageInfo_YKWOrderType.Size(m)
}
func (m *YKWOrderType) XXX_DiscardUnknown() {
	xxx_messageInfo_YKWOrderType.DiscardUnknown(m)
}

var xxx_messageInfo_YKWOrderType proto.InternalMessageInfo

func (m *YKWOrderType) GetOrderTypeId() uint32 {
	if m != nil {
		return m.OrderTypeId
	}
	return 0
}

func (m *YKWOrderType) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *YKWOrderType) GetEffectiveTime() uint32 {
	if m != nil {
		return m.EffectiveTime
	}
	return 0
}

func (m *YKWOrderType) GetDiscount() string {
	if m != nil {
		return m.Discount
	}
	return ""
}

func (m *YKWOrderType) GetOriginalPrice() uint32 {
	if m != nil {
		return m.OriginalPrice
	}
	return 0
}

type GetYKWInfoReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYKWInfoReq) Reset()         { *m = GetYKWInfoReq{} }
func (m *GetYKWInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetYKWInfoReq) ProtoMessage()    {}
func (*GetYKWInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{28}
}
func (m *GetYKWInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYKWInfoReq.Unmarshal(m, b)
}
func (m *GetYKWInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYKWInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetYKWInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYKWInfoReq.Merge(dst, src)
}
func (m *GetYKWInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetYKWInfoReq.Size(m)
}
func (m *GetYKWInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYKWInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYKWInfoReq proto.InternalMessageInfo

func (m *GetYKWInfoReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type GetYKWInfoResp struct {
	Nickname             string          `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname"`
	Status               uint32          `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	Switch               uint32          `protobuf:"varint,3,opt,name=switch,proto3" json:"switch"`
	ServerTime           uint32          `protobuf:"varint,4,opt,name=server_time,json=serverTime,proto3" json:"server_time"`
	ExpireTime           uint32          `protobuf:"varint,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time"`
	YkwConf              *YKWConfInfo    `protobuf:"bytes,6,opt,name=ykw_conf,json=ykwConf,proto3" json:"ykw_conf"`
	YkwOrderTypes        []*YKWOrderType `protobuf:"bytes,7,rep,name=ykw_order_types,json=ykwOrderTypes,proto3" json:"ykw_order_types"`
	NobLevel             int32           `protobuf:"varint,8,opt,name=nob_level,json=nobLevel,proto3" json:"nob_level"`
	NobName              string          `protobuf:"bytes,9,opt,name=nob_name,json=nobName,proto3" json:"nob_name"`
	IsOpenEnterStealth   uint32          `protobuf:"varint,10,opt,name=is_open_enter_stealth,json=isOpenEnterStealth,proto3" json:"is_open_enter_stealth"`
	CanBeOpen            uint32          `protobuf:"varint,11,opt,name=can_be_open,json=canBeOpen,proto3" json:"can_be_open"`
	EnterNotice          uint32          `protobuf:"varint,12,opt,name=enter_notice,json=enterNotice,proto3" json:"enter_notice"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetYKWInfoResp) Reset()         { *m = GetYKWInfoResp{} }
func (m *GetYKWInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetYKWInfoResp) ProtoMessage()    {}
func (*GetYKWInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{29}
}
func (m *GetYKWInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYKWInfoResp.Unmarshal(m, b)
}
func (m *GetYKWInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYKWInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetYKWInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYKWInfoResp.Merge(dst, src)
}
func (m *GetYKWInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetYKWInfoResp.Size(m)
}
func (m *GetYKWInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYKWInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYKWInfoResp proto.InternalMessageInfo

func (m *GetYKWInfoResp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GetYKWInfoResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetYKWInfoResp) GetSwitch() uint32 {
	if m != nil {
		return m.Switch
	}
	return 0
}

func (m *GetYKWInfoResp) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *GetYKWInfoResp) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *GetYKWInfoResp) GetYkwConf() *YKWConfInfo {
	if m != nil {
		return m.YkwConf
	}
	return nil
}

func (m *GetYKWInfoResp) GetYkwOrderTypes() []*YKWOrderType {
	if m != nil {
		return m.YkwOrderTypes
	}
	return nil
}

func (m *GetYKWInfoResp) GetNobLevel() int32 {
	if m != nil {
		return m.NobLevel
	}
	return 0
}

func (m *GetYKWInfoResp) GetNobName() string {
	if m != nil {
		return m.NobName
	}
	return ""
}

func (m *GetYKWInfoResp) GetIsOpenEnterStealth() uint32 {
	if m != nil {
		return m.IsOpenEnterStealth
	}
	return 0
}

func (m *GetYKWInfoResp) GetCanBeOpen() uint32 {
	if m != nil {
		return m.CanBeOpen
	}
	return 0
}

func (m *GetYKWInfoResp) GetEnterNotice() uint32 {
	if m != nil {
		return m.EnterNotice
	}
	return 0
}

// -------------------- 佩戴皮肤 begin ----------------------------------
type GetBuyCommodityDataSuccessRequest struct {
	// uint32 uid = 1;                       // 用户ID
	BigTradeNo           string   `protobuf:"bytes,2,opt,name=big_trade_no,json=bigTradeNo,proto3" json:"big_trade_no"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBuyCommodityDataSuccessRequest) Reset()         { *m = GetBuyCommodityDataSuccessRequest{} }
func (m *GetBuyCommodityDataSuccessRequest) String() string { return proto.CompactTextString(m) }
func (*GetBuyCommodityDataSuccessRequest) ProtoMessage()    {}
func (*GetBuyCommodityDataSuccessRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{30}
}
func (m *GetBuyCommodityDataSuccessRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBuyCommodityDataSuccessRequest.Unmarshal(m, b)
}
func (m *GetBuyCommodityDataSuccessRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBuyCommodityDataSuccessRequest.Marshal(b, m, deterministic)
}
func (dst *GetBuyCommodityDataSuccessRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBuyCommodityDataSuccessRequest.Merge(dst, src)
}
func (m *GetBuyCommodityDataSuccessRequest) XXX_Size() int {
	return xxx_messageInfo_GetBuyCommodityDataSuccessRequest.Size(m)
}
func (m *GetBuyCommodityDataSuccessRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBuyCommodityDataSuccessRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBuyCommodityDataSuccessRequest proto.InternalMessageInfo

func (m *GetBuyCommodityDataSuccessRequest) GetBigTradeNo() string {
	if m != nil {
		return m.BigTradeNo
	}
	return ""
}

type GetBuyCommodityDataSuccessResponse struct {
	BuySkinNameList      []string `protobuf:"bytes,1,rep,name=buy_skin_name_list,json=buySkinNameList,proto3" json:"buy_skin_name_list"` // Deprecated: Do not use.
	SuitName             string   `protobuf:"bytes,2,opt,name=suit_name,json=suitName,proto3" json:"suit_name"`
	SuitResourceCnt      uint32   `protobuf:"varint,3,opt,name=suit_resource_cnt,json=suitResourceCnt,proto3" json:"suit_resource_cnt"`
	ItemCnt              uint32   `protobuf:"varint,4,opt,name=item_cnt,json=itemCnt,proto3" json:"item_cnt"`
	SkinNameList         []string `protobuf:"bytes,5,rep,name=skin_name_list,json=skinNameList,proto3" json:"skin_name_list"` // Deprecated: Do not use.
	UserSex              int32    `protobuf:"varint,6,opt,name=user_sex,json=userSex,proto3" json:"user_sex"`
	BuyIdList            []uint32 `protobuf:"varint,7,rep,packed,name=buy_id_list,json=buyIdList,proto3" json:"buy_id_list"`
	SkinIdList           []uint32 `protobuf:"varint,8,rep,packed,name=skin_id_list,json=skinIdList,proto3" json:"skin_id_list"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBuyCommodityDataSuccessResponse) Reset()         { *m = GetBuyCommodityDataSuccessResponse{} }
func (m *GetBuyCommodityDataSuccessResponse) String() string { return proto.CompactTextString(m) }
func (*GetBuyCommodityDataSuccessResponse) ProtoMessage()    {}
func (*GetBuyCommodityDataSuccessResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{31}
}
func (m *GetBuyCommodityDataSuccessResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBuyCommodityDataSuccessResponse.Unmarshal(m, b)
}
func (m *GetBuyCommodityDataSuccessResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBuyCommodityDataSuccessResponse.Marshal(b, m, deterministic)
}
func (dst *GetBuyCommodityDataSuccessResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBuyCommodityDataSuccessResponse.Merge(dst, src)
}
func (m *GetBuyCommodityDataSuccessResponse) XXX_Size() int {
	return xxx_messageInfo_GetBuyCommodityDataSuccessResponse.Size(m)
}
func (m *GetBuyCommodityDataSuccessResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBuyCommodityDataSuccessResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBuyCommodityDataSuccessResponse proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *GetBuyCommodityDataSuccessResponse) GetBuySkinNameList() []string {
	if m != nil {
		return m.BuySkinNameList
	}
	return nil
}

func (m *GetBuyCommodityDataSuccessResponse) GetSuitName() string {
	if m != nil {
		return m.SuitName
	}
	return ""
}

func (m *GetBuyCommodityDataSuccessResponse) GetSuitResourceCnt() uint32 {
	if m != nil {
		return m.SuitResourceCnt
	}
	return 0
}

func (m *GetBuyCommodityDataSuccessResponse) GetItemCnt() uint32 {
	if m != nil {
		return m.ItemCnt
	}
	return 0
}

// Deprecated: Do not use.
func (m *GetBuyCommodityDataSuccessResponse) GetSkinNameList() []string {
	if m != nil {
		return m.SkinNameList
	}
	return nil
}

func (m *GetBuyCommodityDataSuccessResponse) GetUserSex() int32 {
	if m != nil {
		return m.UserSex
	}
	return 0
}

func (m *GetBuyCommodityDataSuccessResponse) GetBuyIdList() []uint32 {
	if m != nil {
		return m.BuyIdList
	}
	return nil
}

func (m *GetBuyCommodityDataSuccessResponse) GetSkinIdList() []uint32 {
	if m != nil {
		return m.SkinIdList
	}
	return nil
}

// -------------------- 财富榜 begin ----------------------------------
// from c++ accounthttplogic
type NumericRankReq struct {
	Type                 NumericRankReq_Type     `protobuf:"varint,1,opt,name=type,proto3,enum=revenue_base_http_logic.NumericRankReq_Type" json:"type"`
	TimeSpan             NumericRankReq_TimeSpan `protobuf:"varint,2,opt,name=time_span,json=timeSpan,proto3,enum=revenue_base_http_logic.NumericRankReq_TimeSpan" json:"time_span"`
	Page                 uint32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page"`
	PageNum              uint32                  `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *NumericRankReq) Reset()         { *m = NumericRankReq{} }
func (m *NumericRankReq) String() string { return proto.CompactTextString(m) }
func (*NumericRankReq) ProtoMessage()    {}
func (*NumericRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{32}
}
func (m *NumericRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NumericRankReq.Unmarshal(m, b)
}
func (m *NumericRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NumericRankReq.Marshal(b, m, deterministic)
}
func (dst *NumericRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NumericRankReq.Merge(dst, src)
}
func (m *NumericRankReq) XXX_Size() int {
	return xxx_messageInfo_NumericRankReq.Size(m)
}
func (m *NumericRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NumericRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_NumericRankReq proto.InternalMessageInfo

func (m *NumericRankReq) GetType() NumericRankReq_Type {
	if m != nil {
		return m.Type
	}
	return NumericRankReq_TYPE_UNKNOWN
}

func (m *NumericRankReq) GetTimeSpan() NumericRankReq_TimeSpan {
	if m != nil {
		return m.TimeSpan
	}
	return NumericRankReq_TIME_SPAN_UNKNOWN
}

func (m *NumericRankReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *NumericRankReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type NumericRankResp struct {
	List                 []*NumericRankResp_User `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	ServerTime           int64                   `protobuf:"varint,2,opt,name=server_time,json=serverTime,proto3" json:"server_time"`
	Total                uint32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	User                 *NumericRankResp_User   `protobuf:"bytes,4,opt,name=user,proto3" json:"user"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *NumericRankResp) Reset()         { *m = NumericRankResp{} }
func (m *NumericRankResp) String() string { return proto.CompactTextString(m) }
func (*NumericRankResp) ProtoMessage()    {}
func (*NumericRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{33}
}
func (m *NumericRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NumericRankResp.Unmarshal(m, b)
}
func (m *NumericRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NumericRankResp.Marshal(b, m, deterministic)
}
func (dst *NumericRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NumericRankResp.Merge(dst, src)
}
func (m *NumericRankResp) XXX_Size() int {
	return xxx_messageInfo_NumericRankResp.Size(m)
}
func (m *NumericRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NumericRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_NumericRankResp proto.InternalMessageInfo

func (m *NumericRankResp) GetList() []*NumericRankResp_User {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *NumericRankResp) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *NumericRankResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *NumericRankResp) GetUser() *NumericRankResp_User {
	if m != nil {
		return m.User
	}
	return nil
}

type NumericRankResp_User struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account"`
	AnimateHeadFrame     bool     `protobuf:"varint,2,opt,name=animate_head_frame,json=animateHeadFrame,proto3" json:"animate_head_frame"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	CharmValue           uint64   `protobuf:"varint,4,opt,name=charm_value,json=charmValue,proto3" json:"charm_value"`
	ExtendJson           string   `protobuf:"bytes,5,opt,name=extend_json,json=extendJson,proto3" json:"extend_json"`
	HeadFrame            string   `protobuf:"bytes,6,opt,name=head_frame,json=headFrame,proto3" json:"head_frame"`
	HeadwearKey          string   `protobuf:"bytes,7,opt,name=headwear_key,json=headwearKey,proto3" json:"headwear_key"`
	LiveStatus           uint32   `protobuf:"varint,8,opt,name=live_status,json=liveStatus,proto3" json:"live_status"`
	Name                 string   `protobuf:"bytes,9,opt,name=name,proto3" json:"name"`
	RichValue            uint64   `protobuf:"varint,10,opt,name=rich_value,json=richValue,proto3" json:"rich_value"`
	Uid                  uint32   `protobuf:"varint,11,opt,name=uid,proto3" json:"uid"`
	DistanceForecast     uint64   `protobuf:"varint,12,opt,name=distance_forecast,json=distanceForecast,proto3" json:"distance_forecast"`
	Rank                 uint32   `protobuf:"varint,13,opt,name=rank,proto3" json:"rank"`
	RankingForecast      uint32   `protobuf:"varint,14,opt,name=ranking_forecast,json=rankingForecast,proto3" json:"ranking_forecast"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NumericRankResp_User) Reset()         { *m = NumericRankResp_User{} }
func (m *NumericRankResp_User) String() string { return proto.CompactTextString(m) }
func (*NumericRankResp_User) ProtoMessage()    {}
func (*NumericRankResp_User) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991, []int{33, 0}
}
func (m *NumericRankResp_User) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NumericRankResp_User.Unmarshal(m, b)
}
func (m *NumericRankResp_User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NumericRankResp_User.Marshal(b, m, deterministic)
}
func (dst *NumericRankResp_User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NumericRankResp_User.Merge(dst, src)
}
func (m *NumericRankResp_User) XXX_Size() int {
	return xxx_messageInfo_NumericRankResp_User.Size(m)
}
func (m *NumericRankResp_User) XXX_DiscardUnknown() {
	xxx_messageInfo_NumericRankResp_User.DiscardUnknown(m)
}

var xxx_messageInfo_NumericRankResp_User proto.InternalMessageInfo

func (m *NumericRankResp_User) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *NumericRankResp_User) GetAnimateHeadFrame() bool {
	if m != nil {
		return m.AnimateHeadFrame
	}
	return false
}

func (m *NumericRankResp_User) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NumericRankResp_User) GetCharmValue() uint64 {
	if m != nil {
		return m.CharmValue
	}
	return 0
}

func (m *NumericRankResp_User) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *NumericRankResp_User) GetHeadFrame() string {
	if m != nil {
		return m.HeadFrame
	}
	return ""
}

func (m *NumericRankResp_User) GetHeadwearKey() string {
	if m != nil {
		return m.HeadwearKey
	}
	return ""
}

func (m *NumericRankResp_User) GetLiveStatus() uint32 {
	if m != nil {
		return m.LiveStatus
	}
	return 0
}

func (m *NumericRankResp_User) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NumericRankResp_User) GetRichValue() uint64 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *NumericRankResp_User) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NumericRankResp_User) GetDistanceForecast() uint64 {
	if m != nil {
		return m.DistanceForecast
	}
	return 0
}

func (m *NumericRankResp_User) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *NumericRankResp_User) GetRankingForecast() uint32 {
	if m != nil {
		return m.RankingForecast
	}
	return 0
}

func init() {
	proto.RegisterType((*GetTreasurePrivilegeHistoryReq)(nil), "revenue_base_http_logic.GetTreasurePrivilegeHistoryReq")
	proto.RegisterType((*GetTreasurePrivilegeHistoryResp)(nil), "revenue_base_http_logic.GetTreasurePrivilegeHistoryResp")
	proto.RegisterType((*TreasurePrivilegeHistory)(nil), "revenue_base_http_logic.TreasurePrivilegeHistory")
	proto.RegisterType((*EmptyResp)(nil), "revenue_base_http_logic.EmptyResp")
	proto.RegisterType((*SendRecallReq)(nil), "revenue_base_http_logic.SendRecallReq")
	proto.RegisterType((*GetRecallPrizeReq)(nil), "revenue_base_http_logic.GetRecallPrizeReq")
	proto.RegisterType((*GetRecallListResp)(nil), "revenue_base_http_logic.GetRecallListResp")
	proto.RegisterType((*RecallUserInfo)(nil), "revenue_base_http_logic.RecallUserInfo")
	proto.RegisterType((*UserProfile)(nil), "revenue_base_http_logic.UserProfile")
	proto.RegisterType((*GetInviteInfoReq)(nil), "revenue_base_http_logic.GetInviteInfoReq")
	proto.RegisterType((*GetInviteInfoResp)(nil), "revenue_base_http_logic.GetInviteInfoResp")
	proto.RegisterType((*UserInfo)(nil), "revenue_base_http_logic.UserInfo")
	proto.RegisterType((*GiftInfo)(nil), "revenue_base_http_logic.GiftInfo")
	proto.RegisterType((*GetBindResp)(nil), "revenue_base_http_logic.GetBindResp")
	proto.RegisterType((*GetUserInfoReq)(nil), "revenue_base_http_logic.GetUserInfoReq")
	proto.RegisterType((*GetUserInfoResp)(nil), "revenue_base_http_logic.GetUserInfoResp")
	proto.RegisterType((*BindReq)(nil), "revenue_base_http_logic.BindReq")
	proto.RegisterType((*GetUserVipGiftPackageEntranceReq)(nil), "revenue_base_http_logic.GetUserVipGiftPackageEntranceReq")
	proto.RegisterType((*GetUserVipGiftPackageEntranceResp)(nil), "revenue_base_http_logic.GetUserVipGiftPackageEntranceResp")
	proto.RegisterType((*GetUserVipGiftPackageInfoReq)(nil), "revenue_base_http_logic.GetUserVipGiftPackageInfoReq")
	proto.RegisterType((*GetUserVipGiftPackageInfoResp)(nil), "revenue_base_http_logic.GetUserVipGiftPackageInfoResp")
	proto.RegisterType((*VipGiftPackageItem)(nil), "revenue_base_http_logic.VipGiftPackageItem")
	proto.RegisterType((*SubmitReceiveVipGiftPackageReq)(nil), "revenue_base_http_logic.SubmitReceiveVipGiftPackageReq")
	proto.RegisterType((*SubmitReceiveVipGiftPackageResp)(nil), "revenue_base_http_logic.SubmitReceiveVipGiftPackageResp")
	proto.RegisterType((*BuyYKWReq)(nil), "revenue_base_http_logic.BuyYKWReq")
	proto.RegisterType((*BuyYKWResp)(nil), "revenue_base_http_logic.BuyYKWResp")
	proto.RegisterType((*YKWConfInfo)(nil), "revenue_base_http_logic.YKWConfInfo")
	proto.RegisterType((*YKWOrderType)(nil), "revenue_base_http_logic.YKWOrderType")
	proto.RegisterType((*GetYKWInfoReq)(nil), "revenue_base_http_logic.GetYKWInfoReq")
	proto.RegisterType((*GetYKWInfoResp)(nil), "revenue_base_http_logic.GetYKWInfoResp")
	proto.RegisterType((*GetBuyCommodityDataSuccessRequest)(nil), "revenue_base_http_logic.GetBuyCommodityDataSuccessRequest")
	proto.RegisterType((*GetBuyCommodityDataSuccessResponse)(nil), "revenue_base_http_logic.GetBuyCommodityDataSuccessResponse")
	proto.RegisterType((*NumericRankReq)(nil), "revenue_base_http_logic.NumericRankReq")
	proto.RegisterType((*NumericRankResp)(nil), "revenue_base_http_logic.NumericRankResp")
	proto.RegisterType((*NumericRankResp_User)(nil), "revenue_base_http_logic.NumericRankResp.User")
	proto.RegisterEnum("revenue_base_http_logic.UserRecallBindStatus", UserRecallBindStatus_name, UserRecallBindStatus_value)
	proto.RegisterEnum("revenue_base_http_logic.NumericRankReq_Type", NumericRankReq_Type_name, NumericRankReq_Type_value)
	proto.RegisterEnum("revenue_base_http_logic.NumericRankReq_TimeSpan", NumericRankReq_TimeSpan_name, NumericRankReq_TimeSpan_value)
}

func init() {
	proto.RegisterFile("revenue-base-http-logic.proto", fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991)
}

var fileDescriptor_revenue_base_http_logic_6b1f4eda0bab4991 = []byte{
	// 2561 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x59, 0xcf, 0x6f, 0x1b, 0xc7,
	0xf5, 0x0f, 0x45, 0x4a, 0x22, 0x1f, 0x7f, 0x88, 0xde, 0x38, 0x31, 0x65, 0x7f, 0xfd, 0x43, 0x9b,
	0xe4, 0x5b, 0x37, 0x89, 0xed, 0x46, 0x41, 0x50, 0x04, 0x01, 0xda, 0x4a, 0xb2, 0x2c, 0xab, 0x8a,
	0x25, 0x75, 0x25, 0xdb, 0x50, 0x81, 0x62, 0xb1, 0xdc, 0x1d, 0x8a, 0x13, 0x2e, 0x67, 0xd6, 0x3b,
	0xb3, 0x94, 0xd9, 0x4b, 0xff, 0x81, 0x1e, 0x7b, 0x68, 0x0f, 0xbd, 0xf7, 0xdc, 0x63, 0x2f, 0x2d,
	0xd0, 0x6b, 0x8f, 0x3d, 0x16, 0xe8, 0xb5, 0xff, 0x44, 0x81, 0xe2, 0xbd, 0x99, 0x25, 0x97, 0x92,
	0x28, 0xdb, 0x87, 0x9c, 0xc4, 0xf7, 0x79, 0x6f, 0xde, 0xbc, 0x79, 0xbf, 0xe6, 0xcd, 0x0a, 0x6e,
	0xa7, 0x6c, 0xc4, 0x44, 0xc6, 0x1e, 0x74, 0x03, 0xc5, 0x1e, 0xf4, 0xb5, 0x4e, 0x1e, 0xc4, 0xf2,
	0x94, 0x87, 0x0f, 0x93, 0x54, 0x6a, 0xe9, 0xdc, 0xb0, 0x6c, 0x1f, 0xd9, 0x3e, 0xb2, 0x7d, 0x62,
	0xbb, 0xeb, 0x70, 0x67, 0x87, 0xe9, 0xe3, 0x94, 0x05, 0x2a, 0x4b, 0xd9, 0x61, 0xca, 0x47, 0x3c,
	0x66, 0xa7, 0xec, 0x29, 0x57, 0x5a, 0xa6, 0x63, 0x8f, 0xbd, 0x72, 0xda, 0x50, 0xce, 0x78, 0xd4,
	0x29, 0xdd, 0x2b, 0xdd, 0x6f, 0x7a, 0xf8, 0xd3, 0x3d, 0x83, 0xbb, 0x57, 0xae, 0x51, 0x89, 0x73,
	0x0c, 0x8d, 0xbe, 0x21, 0xfd, 0x98, 0x2b, 0xdd, 0x29, 0xdd, 0x2b, 0xdf, 0xaf, 0xaf, 0x7f, 0xf1,
	0x70, 0x8e, 0x19, 0x0f, 0xe7, 0x2a, 0xab, 0x5b, 0x35, 0xdf, 0x72, 0xa5, 0xdd, 0x7f, 0x94, 0xa0,
	0x33, 0x4f, 0xd2, 0xb9, 0x05, 0xb5, 0x53, 0xde, 0xd3, 0x3e, 0x0f, 0xa5, 0x20, 0x6b, 0x6b, 0x5e,
	0x15, 0x81, 0xdd, 0x50, 0x8a, 0x09, 0x53, 0x04, 0x43, 0xd6, 0x59, 0x98, 0x32, 0xf7, 0x83, 0x21,
	0x73, 0x6e, 0x03, 0x10, 0x33, 0x49, 0x79, 0xc8, 0x3a, 0x65, 0x3a, 0x28, 0x89, 0x1f, 0x22, 0x40,
	0x6b, 0x03, 0x2e, 0x7c, 0xcd, 0x87, 0xac, 0x53, 0x21, 0x6e, 0x15, 0x81, 0x63, 0x3e, 0x64, 0xce,
	0x2a, 0x54, 0x7b, 0x39, 0x6f, 0x91, 0x78, 0xcb, 0x3d, 0xcb, 0xba, 0x0b, 0x75, 0x64, 0x29, 0x16,
	0x4a, 0x11, 0xa9, 0xce, 0x12, 0x71, 0xa1, 0xc7, 0xc5, 0x91, 0x41, 0xdc, 0x3a, 0xd4, 0xb6, 0x87,
	0x89, 0x26, 0x8f, 0xb9, 0x07, 0xd0, 0x3c, 0x62, 0x22, 0xf2, 0x58, 0x18, 0xc4, 0x31, 0xfa, 0xfd,
	0x36, 0x80, 0x0e, 0xd2, 0x53, 0xa6, 0xfd, 0xa9, 0xfb, 0x6b, 0x06, 0x79, 0xce, 0x23, 0xd4, 0x9e,
	0x92, 0xac, 0xaf, 0xc7, 0x89, 0x39, 0x53, 0xd3, 0x03, 0x03, 0x1d, 0x8f, 0x13, 0xe6, 0xae, 0xc3,
	0xb5, 0x1d, 0xa6, 0x8d, 0xbe, 0xc3, 0x94, 0xff, 0x9a, 0xbd, 0x59, 0xa9, 0xfb, 0xfb, 0x52, 0x61,
	0x11, 0xba, 0x9c, 0x82, 0xf9, 0x04, 0x1a, 0x32, 0xf1, 0x33, 0xc5, 0x52, 0x9f, 0x8b, 0x9e, 0xa4,
	0x65, 0xf5, 0xf5, 0x8f, 0xe7, 0x06, 0xf3, 0xb9, 0x62, 0xe9, 0x61, 0x2a, 0x7b, 0x3c, 0x66, 0x1e,
	0xc8, 0x04, 0xc9, 0x5d, 0xd1, 0x93, 0xce, 0x37, 0x50, 0xa1, 0x64, 0x58, 0xa0, 0x64, 0xf8, 0xc1,
	0xdc, 0xf5, 0x66, 0xfb, 0x7c, 0x99, 0x47, 0x8b, 0xdc, 0x3f, 0x95, 0xa1, 0x35, 0xcb, 0xb8, 0x98,
	0x99, 0x4e, 0x07, 0x96, 0x83, 0x30, 0x94, 0x99, 0xd0, 0x36, 0xc8, 0x39, 0xe9, 0x5c, 0x87, 0xc5,
	0x20, 0xe6, 0x81, 0xa2, 0xf0, 0xd6, 0x3c, 0x43, 0xa0, 0x06, 0xc5, 0x5e, 0xdb, 0xa0, 0xe2, 0x4f,
	0xe7, 0x26, 0x54, 0x05, 0x0f, 0x07, 0x94, 0x27, 0x8b, 0x26, 0x4f, 0x72, 0xda, 0xf9, 0x10, 0x96,
	0x94, 0x0e, 0x74, 0x96, 0xc7, 0xd2, 0x52, 0x14, 0x68, 0x16, 0xc7, 0xf2, 0xcc, 0x84, 0x62, 0xd9,
	0x06, 0x9a, 0x20, 0x0c, 0x05, 0x9a, 0x15, 0x4a, 0xa1, 0x99, 0xd0, 0x9d, 0xaa, 0x31, 0xcb, 0x92,
	0xe7, 0x52, 0xaf, 0x76, 0x3e, 0xf5, 0x56, 0x81, 0xb2, 0xd4, 0x0f, 0x85, 0xee, 0x80, 0xc9, 0x2e,
	0xa4, 0xb7, 0x84, 0x9e, 0xcd, 0xe8, 0xfa, 0xb9, 0x8c, 0x76, 0xa1, 0x39, 0xa9, 0x05, 0x3f, 0x4b,
	0xe3, 0x4e, 0x83, 0x04, 0xea, 0x79, 0x3d, 0x3c, 0x4f, 0x63, 0x67, 0x0d, 0x1a, 0x5c, 0x8c, 0xb8,
	0x66, 0xbe, 0x96, 0x03, 0x26, 0x3a, 0x4d, 0x23, 0x62, 0xb0, 0x63, 0x84, 0xf0, 0xc0, 0x29, 0x1b,
	0x06, 0xe9, 0xa0, 0xd3, 0x22, 0xa6, 0xa5, 0xd0, 0x6a, 0xbb, 0x14, 0x75, 0xaf, 0x10, 0xaf, 0x66,
	0x90, 0xe7, 0x69, 0xec, 0xfe, 0xb6, 0x04, 0xf5, 0x42, 0x0e, 0xbc, 0x53, 0x9c, 0x8a, 0xfe, 0x2f,
	0x9f, 0xf3, 0xff, 0x47, 0xd0, 0xb4, 0x62, 0xbe, 0x89, 0x65, 0x85, 0x04, 0x1a, 0x16, 0xdc, 0x28,
	0x86, 0x74, 0x71, 0x12, 0x52, 0xb7, 0x0b, 0xed, 0x1d, 0xa6, 0x77, 0xc9, 0x3c, 0x4a, 0x28, 0xf6,
	0x0a, 0x43, 0x96, 0x1f, 0x5e, 0x5b, 0xd3, 0x6a, 0x9e, 0x3d, 0xd4, 0xb1, 0xe6, 0x91, 0x73, 0x03,
	0x96, 0xb5, 0x34, 0x4c, 0x63, 0xe1, 0x92, 0x96, 0xc4, 0xb8, 0x0e, 0x8b, 0xc6, 0x5f, 0x36, 0x91,
	0x88, 0x70, 0xff, 0x6b, 0x0a, 0xa7, 0xb8, 0x89, 0x4a, 0x9c, 0x3d, 0x68, 0xe7, 0x7e, 0x3a, 0x57,
	0x3c, 0x6b, 0x57, 0x16, 0x0f, 0x29, 0x68, 0x59, 0x87, 0xe6, 0xd9, 0xbe, 0x05, 0x0d, 0x2d, 0x0b,
	0x8a, 0x16, 0xde, 0x56, 0x11, 0x68, 0x39, 0x51, 0xf2, 0x13, 0x9b, 0x35, 0x54, 0x87, 0x65, 0xaa,
	0xc3, 0xf9, 0x1a, 0x76, 0x30, 0x5b, 0x50, 0x03, 0x25, 0x16, 0xb6, 0x83, 0x42, 0x09, 0x54, 0x8a,
	0x25, 0xe0, 0x32, 0xa8, 0x4e, 0xf6, 0x28, 0x04, 0xb7, 0x34, 0x3f, 0xb8, 0x0b, 0x6f, 0x0a, 0x6e,
	0xf9, 0x62, 0x70, 0xdd, 0xbf, 0x94, 0xa0, 0x9a, 0x5b, 0xf5, 0xfd, 0x35, 0xfc, 0x62, 0xd5, 0x55,
	0x2e, 0x54, 0x9d, 0x90, 0x5d, 0x3f, 0x66, 0x23, 0x16, 0xdb, 0x1c, 0xab, 0x0a, 0xd9, 0xfd, 0x16,
	0x69, 0x54, 0xcb, 0x5e, 0x27, 0x3c, 0x65, 0x7e, 0x14, 0x8c, 0x6d, 0x8f, 0xa8, 0x19, 0xe4, 0x71,
	0x30, 0x76, 0xfb, 0x50, 0xdf, 0x61, 0x7a, 0x93, 0x63, 0x93, 0x57, 0x09, 0xee, 0xd2, 0xe5, 0x22,
	0x2a, 0x34, 0xe2, 0x65, 0xa4, 0xb1, 0xb7, 0xdf, 0x82, 0x1a, 0xb1, 0xd0, 0x39, 0xb9, 0xf1, 0x08,
	0xec, 0xf3, 0x70, 0x80, 0x75, 0x4b, 0xcc, 0xdc, 0xc7, 0xc6, 0x4f, 0x75, 0xc4, 0x36, 0x0c, 0xe4,
	0x7e, 0x0c, 0xad, 0x1d, 0xa6, 0x27, 0x09, 0xc0, 0x5e, 0x39, 0x0e, 0x54, 0x0a, 0x89, 0x4e, 0xbf,
	0xdd, 0x5f, 0xc0, 0xca, 0x8c, 0x94, 0x4a, 0x2e, 0xa9, 0x54, 0x07, 0x2a, 0x05, 0x2b, 0xe8, 0x77,
	0x31, 0xc0, 0xe5, 0x99, 0x00, 0xbb, 0xb7, 0x61, 0xd9, 0x9c, 0xef, 0xf2, 0x1d, 0x5d, 0xb8, 0x67,
	0x77, 0x7c, 0xc1, 0x13, 0x8c, 0xe3, 0x61, 0x10, 0x0e, 0x82, 0x53, 0xb6, 0x2d, 0x74, 0x1a, 0x88,
	0x10, 0x6f, 0x28, 0xf7, 0xdf, 0x25, 0x58, 0x7b, 0x83, 0xd0, 0xa5, 0x86, 0xae, 0x42, 0x35, 0xe5,
	0x61, 0xdf, 0x1f, 0x05, 0x31, 0x19, 0x5b, 0xf1, 0x96, 0x91, 0x7e, 0x11, 0xc4, 0xd8, 0x0d, 0xc3,
	0x2c, 0xf5, 0x47, 0x3c, 0xb1, 0x81, 0x33, 0x11, 0xaf, 0x87, 0x19, 0x6e, 0x61, 0x62, 0x77, 0x1f,
	0xda, 0x5c, 0xf9, 0x78, 0x2f, 0x73, 0xcd, 0xa5, 0xf0, 0x87, 0xcc, 0xc4, 0xbe, 0xea, 0xb5, 0xb8,
	0xda, 0xca, 0xe1, 0x67, 0x4c, 0x3b, 0xf7, 0xa0, 0xa1, 0xfa, 0xf2, 0xcc, 0x4f, 0x59, 0xe4, 0x47,
	0x52, 0x53, 0x16, 0x54, 0x3d, 0x40, 0xcc, 0x63, 0xd1, 0x63, 0x49, 0x4d, 0x9d, 0x2b, 0xbf, 0x1b,
	0xcb, 0x70, 0xc0, 0x22, 0xca, 0x83, 0xaa, 0x57, 0xe3, 0x6a, 0xd3, 0x00, 0xee, 0x1d, 0xf8, 0xbf,
	0x4b, 0x0f, 0x68, 0x63, 0xe5, 0xfe, 0x73, 0x11, 0x6e, 0x5f, 0x21, 0xf0, 0x7d, 0x9c, 0xde, 0x85,
	0xe6, 0x88, 0x27, 0x5f, 0xf9, 0x13, 0x1d, 0x8b, 0xa4, 0xa3, 0x8e, 0xa0, 0x67, 0xf5, 0x5c, 0xe6,
	0xa1, 0xa5, 0x79, 0x1e, 0xe2, 0xca, 0xef, 0xf1, 0x54, 0x69, 0x92, 0x5a, 0x36, 0x1e, 0xe2, 0xea,
	0x09, 0x42, 0x28, 0x81, 0xed, 0x57, 0xf9, 0x29, 0x0b, 0x19, 0x1f, 0xb1, 0x88, 0x2e, 0x45, 0x12,
	0xf0, 0x2c, 0xe2, 0x3c, 0x80, 0xf7, 0x0b, 0x02, 0x7e, 0x2f, 0x95, 0x43, 0x5f, 0x26, 0x74, 0x41,
	0x56, 0xbd, 0xf6, 0x54, 0xf0, 0x49, 0x2a, 0x87, 0x07, 0x89, 0x73, 0x40, 0xf6, 0xfb, 0xd3, 0xd6,
	0x06, 0xd4, 0xda, 0x3e, 0x9b, 0xdb, 0xda, 0xce, 0x39, 0x56, 0xb3, 0x21, 0x1d, 0x76, 0x27, 0xef,
	0x73, 0xdf, 0xc0, 0xcd, 0x60, 0x14, 0xf0, 0x38, 0xe8, 0xc6, 0xcc, 0xa8, 0xd5, 0x52, 0x07, 0xb1,
	0xed, 0x18, 0x75, 0xf2, 0xce, 0x8d, 0x89, 0x04, 0x2e, 0x3b, 0x46, 0xbe, 0xe9, 0x1f, 0x27, 0xe0,
	0x4c, 0x2c, 0x9f, 0x9a, 0xd4, 0x78, 0x77, 0x93, 0xda, 0xb9, 0x9a, 0x89, 0x5d, 0x5f, 0xc3, 0xea,
	0xac, 0xea, 0xa2, 0x59, 0x4d, 0x32, 0xeb, 0xc3, 0xe2, 0xa2, 0x82, 0x55, 0x1f, 0x41, 0x73, 0xb2,
	0x94, 0xc6, 0xd5, 0x16, 0x89, 0x37, 0x72, 0x90, 0x66, 0xd6, 0x35, 0x68, 0x84, 0x52, 0xf4, 0x78,
	0x3a, 0xf4, 0x35, 0x7b, 0xad, 0xed, 0xdd, 0x5e, 0xb7, 0xd8, 0x31, 0x7b, 0x4d, 0x4d, 0x1c, 0x2f,
	0x21, 0x6a, 0xac, 0x6d, 0xd3, 0x9b, 0x72, 0x7a, 0xa6, 0xc1, 0x5f, 0x9b, 0x6d, 0xf0, 0xee, 0x5f,
	0x4b, 0xe0, 0x5c, 0x3c, 0x23, 0x5e, 0xb4, 0xa6, 0x8b, 0xe7, 0xf9, 0xbc, 0x44, 0x3d, 0x3c, 0x7a,
	0xbb, 0x0e, 0x3e, 0xed, 0x42, 0xb6, 0x83, 0x6f, 0xd1, 0x45, 0x33, 0xdb, 0xe0, 0x2b, 0x74, 0xd0,
	0x73, 0x13, 0xfd, 0xe4, 0xe6, 0x58, 0x3c, 0x77, 0x73, 0x74, 0x60, 0x39, 0x0d, 0xc4, 0x80, 0x8b,
	0x53, 0xdb, 0xc2, 0x73, 0xd2, 0xfd, 0x0e, 0xee, 0x1c, 0x65, 0xdd, 0x21, 0xd7, 0x36, 0xfb, 0x66,
	0x4f, 0x83, 0x4d, 0xef, 0x69, 0xf1, 0x7a, 0x2d, 0xbd, 0x7b, 0xc0, 0x27, 0x17, 0xad, 0xbb, 0x06,
	0x77, 0xaf, 0xdc, 0x4b, 0x25, 0xee, 0x1f, 0xcb, 0x50, 0xdb, 0xcc, 0xc6, 0x27, 0x7b, 0x2f, 0xcf,
	0x3d, 0xd3, 0x6a, 0xa6, 0x27, 0xdc, 0x06, 0x48, 0xd9, 0xab, 0x8c, 0x29, 0x72, 0xae, 0xf1, 0x60,
	0xcd, 0x22, 0xbb, 0x91, 0xf3, 0x09, 0xb4, 0xc2, 0x98, 0x33, 0xa1, 0xfd, 0x11, 0x4b, 0x15, 0x97,
	0xc2, 0xba, 0xb1, 0x69, 0xd0, 0x17, 0x06, 0xc4, 0x52, 0xb5, 0x62, 0x34, 0xdc, 0x9a, 0xfb, 0x10,
	0x0c, 0x44, 0xc3, 0xed, 0xff, 0xc3, 0x4a, 0x2f, 0x08, 0x99, 0x1f, 0x64, 0xba, 0x6f, 0x47, 0x49,
	0xe3, 0xd2, 0x26, 0xc2, 0x1b, 0x99, 0xee, 0x9b, 0x61, 0xf2, 0x16, 0xd4, 0x22, 0x36, 0xe2, 0x21,
	0x43, 0x6b, 0x96, 0x8c, 0xd3, 0x0d, 0xb0, 0x1b, 0x39, 0x5f, 0xc1, 0x8d, 0xa9, 0x92, 0x24, 0x95,
	0x23, 0x1e, 0xb1, 0xd4, 0x0f, 0x65, 0x64, 0xc6, 0xe9, 0x9a, 0x77, 0x3d, 0x57, 0x76, 0x68, 0x99,
	0x5b, 0x32, 0xa2, 0x39, 0x57, 0xa6, 0x28, 0x89, 0xb6, 0xa1, 0xde, 0xaa, 0xe9, 0x6d, 0x04, 0xa2,
	0x75, 0xbb, 0x91, 0xb3, 0x09, 0x77, 0x2e, 0x51, 0x9d, 0x32, 0x95, 0xc5, 0xda, 0x8f, 0x02, 0x1d,
	0x50, 0x57, 0xa9, 0x79, 0x37, 0xcf, 0xef, 0xe0, 0x91, 0xc8, 0xe3, 0x40, 0x07, 0xce, 0x97, 0xf0,
	0xe1, 0x54, 0x87, 0x5d, 0x6a, 0x8e, 0x0a, 0xb4, 0xf6, 0xfd, 0x7c, 0xad, 0x59, 0x43, 0x07, 0x76,
	0x7f, 0x03, 0x90, 0x87, 0x47, 0x25, 0x18, 0x0d, 0x5a, 0xad, 0x42, 0x26, 0x58, 0xfe, 0xf2, 0x42,
	0xe4, 0x08, 0x81, 0x37, 0x05, 0x6b, 0xc6, 0x00, 0x7a, 0x3c, 0xbc, 0xd6, 0xfe, 0x77, 0x4a, 0xe6,
	0x63, 0xe8, 0xc4, 0x80, 0x2d, 0xc3, 0xfb, 0xb9, 0x92, 0xc2, 0xfd, 0x08, 0xea, 0x27, 0x7b, 0x2f,
	0xb7, 0xa4, 0xe8, 0xd1, 0xbc, 0x74, 0x1d, 0x16, 0x4d, 0x3d, 0x98, 0xcd, 0x0d, 0xe1, 0xfe, 0xb9,
	0x04, 0x8d, 0x93, 0xbd, 0x97, 0x07, 0xb9, 0xc7, 0x2e, 0xfa, 0xb4, 0x74, 0xd1, 0xa7, 0x13, 0x55,
	0x0b, 0x05, 0x55, 0x98, 0x51, 0xac, 0xd7, 0x63, 0xa1, 0xe6, 0x23, 0x66, 0x5a, 0x8c, 0xcd, 0xa8,
	0x09, 0x4a, 0x3d, 0xe6, 0x26, 0x54, 0x23, 0xae, 0x4c, 0xe5, 0x56, 0x6c, 0x1e, 0x58, 0x1a, 0x55,
	0xc8, 0x94, 0x9f, 0x72, 0x31, 0x69, 0x6a, 0x66, 0xc8, 0x6a, 0xe6, 0x28, 0x15, 0xb0, 0xbb, 0x06,
	0xcd, 0x1d, 0xa6, 0x4f, 0xf6, 0x5e, 0xe6, 0xf3, 0xcd, 0x85, 0xec, 0x77, 0xff, 0x55, 0xa6, 0x21,
	0x68, 0x22, 0xa3, 0x92, 0x99, 0xee, 0x54, 0x9a, 0xfb, 0xb6, 0x5b, 0x98, 0x79, 0xdb, 0x21, 0x7e,
	0xc6, 0x75, 0xd8, 0xb7, 0x67, 0xb1, 0x14, 0x96, 0x85, 0x62, 0xe9, 0x08, 0xdd, 0x34, 0xfd, 0x2c,
	0x00, 0x06, 0xca, 0x5f, 0xff, 0x76, 0x18, 0x2c, 0x7c, 0x1b, 0xb0, 0xf3, 0x21, 0x09, 0xfc, 0x14,
	0xaa, 0xe3, 0xc1, 0x19, 0x06, 0xb3, 0x47, 0xe5, 0x70, 0xd5, 0x8b, 0xba, 0x10, 0x46, 0x6f, 0x79,
	0x3c, 0x38, 0x43, 0xc2, 0x79, 0x06, 0x2b, 0xa8, 0x60, 0x1a, 0x2c, 0xd5, 0x59, 0xa6, 0x96, 0xf3,
	0xc9, 0x55, 0x7a, 0x26, 0x81, 0xf6, 0x9a, 0xe3, 0xc1, 0xd9, 0x84, 0x52, 0xb3, 0xa3, 0x2d, 0xd6,
	0xd1, 0x62, 0x61, 0xb4, 0x5d, 0x05, 0xfc, 0x6d, 0x7a, 0xb1, 0x29, 0x97, 0x65, 0x21, 0xbb, 0xd4,
	0x8a, 0xbf, 0x80, 0x0f, 0xb8, 0xf2, 0x65, 0xc2, 0x84, 0xcf, 0x84, 0x66, 0xa9, 0xaf, 0x34, 0x0b,
	0x62, 0xdd, 0xb7, 0x0f, 0x56, 0x87, 0xab, 0x83, 0x84, 0x89, 0x6d, 0x64, 0x1d, 0x19, 0x8e, 0x73,
	0x07, 0xea, 0x61, 0x20, 0xfc, 0x2e, 0xa3, 0x65, 0x74, 0x9d, 0x36, 0xbd, 0x5a, 0x18, 0x88, 0x4d,
	0x86, 0xb2, 0x78, 0x0b, 0x19, 0x55, 0x42, 0x6a, 0xcc, 0x81, 0x86, 0xc9, 0x40, 0xc2, 0xf6, 0x09,
	0x72, 0xb7, 0x69, 0x4a, 0xdc, 0xcc, 0xc6, 0x5b, 0x72, 0x38, 0x94, 0x11, 0xd7, 0x63, 0x2c, 0xd4,
	0xa3, 0x2c, 0x0c, 0x99, 0x52, 0x9e, 0xa9, 0x1b, 0x1c, 0x44, 0xba, 0xfc, 0xd4, 0xd7, 0x69, 0x10,
	0x31, 0x5f, 0x48, 0x5b, 0x56, 0xd0, 0xe5, 0xa7, 0xc7, 0x08, 0xed, 0x4b, 0xf7, 0x6f, 0x0b, 0xe0,
	0x5e, 0xa5, 0x47, 0x25, 0x52, 0x28, 0xe6, 0x3c, 0x02, 0xa7, 0x9b, 0x8d, 0x7d, 0x35, 0xe0, 0x82,
	0x7c, 0x30, 0x6d, 0xf0, 0xb5, 0xcd, 0x85, 0x4e, 0xc9, 0x5b, 0xe9, 0x66, 0xe3, 0xa3, 0x01, 0x17,
	0xe8, 0x10, 0xba, 0xa7, 0x6f, 0x41, 0x4d, 0x65, 0x7c, 0xf6, 0xf2, 0x42, 0x80, 0x3c, 0xf6, 0x29,
	0x5c, 0x23, 0x66, 0xca, 0x94, 0xcc, 0xd2, 0x90, 0xd1, 0x43, 0xc3, 0xa4, 0xd7, 0x0a, 0x32, 0x3c,
	0x8b, 0xe3, 0x83, 0x63, 0x15, 0xaa, 0x5c, 0xb3, 0x61, 0xf1, 0x2d, 0x82, 0x34, 0xb2, 0xee, 0x43,
	0xeb, 0x9c, 0x41, 0x8b, 0x13, 0x83, 0x1a, 0xaa, 0x68, 0xcd, 0xaa, 0xb9, 0xb2, 0x7d, 0x7c, 0x18,
	0x2f, 0x51, 0x64, 0x97, 0x91, 0x3e, 0x62, 0xaf, 0x31, 0x14, 0x78, 0x32, 0x1e, 0x19, 0x0d, 0x98,
	0x40, 0x4d, 0xaf, 0xd6, 0xcd, 0xc6, 0xbb, 0x11, 0x2d, 0xc5, 0x69, 0x17, 0x37, 0xc9, 0x05, 0xaa,
	0x24, 0x00, 0x88, 0x19, 0x09, 0xf7, 0x3f, 0x0b, 0xd0, 0xda, 0xcf, 0x86, 0x2c, 0xe5, 0xa1, 0x17,
	0x88, 0x01, 0x56, 0xe3, 0xcf, 0xa0, 0x42, 0x97, 0x05, 0x16, 0x59, 0x6b, 0xfd, 0xf3, 0xb9, 0xe9,
	0x38, 0xbb, 0xec, 0x21, 0x65, 0x25, 0xad, 0x74, 0x9e, 0x41, 0x0d, 0xcb, 0xc6, 0x57, 0x49, 0x20,
	0xc8, 0x7f, 0xad, 0xf5, 0x1f, 0xbd, 0xb5, 0x1a, 0x3e, 0x64, 0x47, 0x49, 0x20, 0xbc, 0xaa, 0xb6,
	0xbf, 0xf0, 0x31, 0x92, 0x04, 0xa7, 0x79, 0x3f, 0xa2, 0xdf, 0xe8, 0x14, 0xfc, 0xeb, 0x8b, 0x6c,
	0x98, 0x7b, 0x16, 0xe9, 0xfd, 0x6c, 0xe8, 0xfe, 0x18, 0x2a, 0xd4, 0x0a, 0xdb, 0xd0, 0x38, 0x3e,
	0x39, 0xdc, 0xf6, 0x9f, 0xef, 0xef, 0xed, 0x1f, 0xbc, 0xdc, 0x6f, 0xbf, 0xe7, 0xb4, 0x00, 0x08,
	0xd9, 0x7a, 0xba, 0xe1, 0x3d, 0x6b, 0x97, 0x9c, 0x26, 0xd4, 0x88, 0xf6, 0x76, 0xb7, 0x9e, 0xb6,
	0x17, 0xdc, 0x5f, 0x41, 0x35, 0xdf, 0xdd, 0xf9, 0x00, 0xae, 0x1d, 0xef, 0x3e, 0xdb, 0xf6, 0x8f,
	0x0e, 0x37, 0xf6, 0x0b, 0x1a, 0xae, 0x41, 0x73, 0x0a, 0x3f, 0xde, 0x38, 0x69, 0x97, 0x1c, 0x07,
	0x5a, 0x53, 0xe8, 0xe5, 0xf6, 0xf6, 0x5e, 0x7b, 0xc1, 0x79, 0x1f, 0x56, 0xa6, 0xd8, 0xb3, 0x83,
	0xfd, 0xe3, 0xa7, 0xed, 0xb2, 0xfb, 0xbb, 0x45, 0x58, 0x99, 0x39, 0xac, 0x4a, 0x9c, 0x0d, 0xfb,
	0x51, 0xcd, 0x4c, 0x1b, 0x0f, 0xde, 0xce, 0x49, 0x2a, 0xa1, 0xcf, 0x03, 0xe6, 0xd3, 0xda, 0xf9,
	0x5e, 0x86, 0xee, 0x2e, 0xcf, 0xf4, 0x32, 0xfa, 0xe6, 0xa1, 0x83, 0xfc, 0xe9, 0x60, 0x08, 0xdc,
	0x19, 0xb3, 0x88, 0x9c, 0xf7, 0xee, 0x3b, 0xe3, 0xd2, 0x9b, 0x7f, 0x2f, 0x43, 0x05, 0xc9, 0x2b,
	0xbe, 0x19, 0x7c, 0x0e, 0x4e, 0x20, 0xf8, 0x30, 0xd0, 0xcc, 0xef, 0xb3, 0x00, 0x9f, 0x02, 0x79,
	0x49, 0x55, 0xbd, 0xb6, 0xe5, 0x3c, 0x65, 0x41, 0xf4, 0x24, 0xb5, 0x73, 0x61, 0xd8, 0x0f, 0x84,
	0x60, 0x31, 0xde, 0x5c, 0x76, 0x2e, 0xb4, 0xc8, 0x2e, 0x7d, 0x34, 0x0d, 0xfb, 0x41, 0x3a, 0xc4,
	0x37, 0x4e, 0x96, 0x0f, 0x86, 0x40, 0xd0, 0x0b, 0x44, 0x4c, 0xd7, 0xd6, 0x4c, 0x44, 0xe6, 0x72,
	0x35, 0x83, 0x0c, 0x18, 0x08, 0xef, 0x54, 0xdc, 0xa0, 0x60, 0x86, 0x19, 0x63, 0x6a, 0xfd, 0xc9,
	0xfe, 0x6b, 0xd0, 0x40, 0xe2, 0x8c, 0x05, 0xa9, 0x3f, 0x60, 0x63, 0x3b, 0xbc, 0xd4, 0x73, 0x6c,
	0x8f, 0x8d, 0x71, 0x8b, 0x18, 0x2f, 0x48, 0x7b, 0xdd, 0x98, 0x89, 0x05, 0x10, 0x3a, 0x32, 0x57,
	0x0e, 0x3e, 0xb9, 0xa7, 0x7d, 0x96, 0x7e, 0xd3, 0x78, 0x60, 0xdf, 0x66, 0x19, 0xa3, 0xce, 0x5a,
	0xf1, 0x6a, 0xf6, 0x85, 0x97, 0x4d, 0xbe, 0xb0, 0xd5, 0xa7, 0x0f, 0xc2, 0xcf, 0xe0, 0x5a, 0xc4,
	0x95, 0xc6, 0x07, 0xb3, 0xdf, 0x93, 0x29, 0x0b, 0x03, 0x7a, 0x82, 0xe0, 0xba, 0x76, 0xce, 0x78,
	0x62, 0x71, 0xdc, 0x11, 0x67, 0x5c, 0x7a, 0x40, 0x34, 0x3d, 0xfa, 0xed, 0xfc, 0x10, 0xda, 0x76,
	0xee, 0x9d, 0xae, 0x6f, 0x99, 0x1e, 0x65, 0xf1, 0x7c, 0xf9, 0xa7, 0x7f, 0x28, 0xc1, 0x75, 0x0a,
	0x2a, 0x7d, 0x9e, 0xdd, 0xe4, 0x22, 0xb2, 0x27, 0xb9, 0x0d, 0xab, 0x97, 0xe1, 0xfe, 0x7e, 0x16,
	0xc7, 0xed, 0xf7, 0x9c, 0xbb, 0x70, 0xeb, 0x72, 0xb6, 0x44, 0xa2, 0x5d, 0x9a, 0xbb, 0x9e, 0xd8,
	0x0b, 0x8e, 0x0b, 0x77, 0xe6, 0xb2, 0x0f, 0x74, 0x9f, 0xa5, 0xed, 0xf2, 0xe6, 0x37, 0xbf, 0xfc,
	0xfa, 0x54, 0xc6, 0x81, 0x38, 0x7d, 0xf8, 0xd5, 0xba, 0xd6, 0x0f, 0x43, 0x39, 0x7c, 0x44, 0xff,
	0x11, 0x09, 0x65, 0xfc, 0x08, 0x33, 0x9c, 0x87, 0x4c, 0x3d, 0x9a, 0xf3, 0xbf, 0x93, 0xee, 0x12,
	0x89, 0x7e, 0xf9, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf1, 0xc4, 0x4d, 0xaa, 0x5d, 0x19, 0x00,
	0x00,
}
