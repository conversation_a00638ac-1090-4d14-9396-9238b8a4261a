// Code generated by protoc-gen-go. DO NOT EDIT.
// source: chat-card/card-event.proto

package chatcard // import "golang.52tt.com/protocol/services/chatcard"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 卡片事件类型
type ChatCardEventType int32

const (
	ChatCardEventType_ChatCardEventType_UNDEFINED ChatCardEventType = 0
	ChatCardEventType_ChatCardEventType_CLOSE     ChatCardEventType = 1
	ChatCardEventType_ChatCardEventType_OPEN      ChatCardEventType = 2
	ChatCardEventType_ChatCardEventType_RESET     ChatCardEventType = 3
)

var ChatCardEventType_name = map[int32]string{
	0: "ChatCardEventType_UNDEFINED",
	1: "ChatCardEventType_CLOSE",
	2: "ChatCardEventType_OPEN",
	3: "ChatCardEventType_RESET",
}
var ChatCardEventType_value = map[string]int32{
	"ChatCardEventType_UNDEFINED": 0,
	"ChatCardEventType_CLOSE":     1,
	"ChatCardEventType_OPEN":      2,
	"ChatCardEventType_RESET":     3,
}

func (x ChatCardEventType) String() string {
	return proto.EnumName(ChatCardEventType_name, int32(x))
}
func (ChatCardEventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_card_event_ee998a30cf2c23cf, []int{0}
}

// 打招呼事件类型
type ChatEventType int32

const (
	ChatEventType_ChatEventType_UNDEFINED ChatEventType = 0
	ChatEventType_ChatEventType_SAYHI     ChatEventType = 1
	ChatEventType_ChatEventType_REPLY     ChatEventType = 2
)

var ChatEventType_name = map[int32]string{
	0: "ChatEventType_UNDEFINED",
	1: "ChatEventType_SAYHI",
	2: "ChatEventType_REPLY",
}
var ChatEventType_value = map[string]int32{
	"ChatEventType_UNDEFINED": 0,
	"ChatEventType_SAYHI":     1,
	"ChatEventType_REPLY":     2,
}

func (x ChatEventType) String() string {
	return proto.EnumName(ChatEventType_name, int32(x))
}
func (ChatEventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_card_event_ee998a30cf2c23cf, []int{1}
}

// 卡片消息
type ChatCardEvent struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Slogan               string            `protobuf:"bytes,2,opt,name=slogan,proto3" json:"slogan,omitempty"`
	Tags                 []*Tag            `protobuf:"bytes,3,rep,name=tags,proto3" json:"tags,omitempty"`
	Type                 ChatCardEventType `protobuf:"varint,4,opt,name=type,proto3,enum=chatcard.ChatCardEventType" json:"type,omitempty"`
	Ip                   string            `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
	BgType               ChatCardBgType    `protobuf:"varint,6,opt,name=bg_type,json=bgType,proto3,enum=chatcard.ChatCardBgType" json:"bg_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChatCardEvent) Reset()         { *m = ChatCardEvent{} }
func (m *ChatCardEvent) String() string { return proto.CompactTextString(m) }
func (*ChatCardEvent) ProtoMessage()    {}
func (*ChatCardEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_card_event_ee998a30cf2c23cf, []int{0}
}
func (m *ChatCardEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatCardEvent.Unmarshal(m, b)
}
func (m *ChatCardEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatCardEvent.Marshal(b, m, deterministic)
}
func (dst *ChatCardEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatCardEvent.Merge(dst, src)
}
func (m *ChatCardEvent) XXX_Size() int {
	return xxx_messageInfo_ChatCardEvent.Size(m)
}
func (m *ChatCardEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatCardEvent.DiscardUnknown(m)
}

var xxx_messageInfo_ChatCardEvent proto.InternalMessageInfo

func (m *ChatCardEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChatCardEvent) GetSlogan() string {
	if m != nil {
		return m.Slogan
	}
	return ""
}

func (m *ChatCardEvent) GetTags() []*Tag {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *ChatCardEvent) GetType() ChatCardEventType {
	if m != nil {
		return m.Type
	}
	return ChatCardEventType_ChatCardEventType_UNDEFINED
}

func (m *ChatCardEvent) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *ChatCardEvent) GetBgType() ChatCardBgType {
	if m != nil {
		return m.BgType
	}
	return ChatCardBgType_ChatCardBgType_Undefined
}

// 打招呼消息
type ChatEvent struct {
	InviterUid           uint32        `protobuf:"varint,1,opt,name=inviter_uid,json=inviterUid,proto3" json:"inviter_uid,omitempty"`
	InviteeUid           uint32        `protobuf:"varint,2,opt,name=invitee_uid,json=inviteeUid,proto3" json:"invitee_uid,omitempty"`
	OccurrenceTime       uint32        `protobuf:"varint,3,opt,name=occurrence_time,json=occurrenceTime,proto3" json:"occurrence_time,omitempty"`
	Type                 ChatEventType `protobuf:"varint,4,opt,name=type,proto3,enum=chatcard.ChatEventType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChatEvent) Reset()         { *m = ChatEvent{} }
func (m *ChatEvent) String() string { return proto.CompactTextString(m) }
func (*ChatEvent) ProtoMessage()    {}
func (*ChatEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_card_event_ee998a30cf2c23cf, []int{1}
}
func (m *ChatEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatEvent.Unmarshal(m, b)
}
func (m *ChatEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatEvent.Marshal(b, m, deterministic)
}
func (dst *ChatEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatEvent.Merge(dst, src)
}
func (m *ChatEvent) XXX_Size() int {
	return xxx_messageInfo_ChatEvent.Size(m)
}
func (m *ChatEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatEvent.DiscardUnknown(m)
}

var xxx_messageInfo_ChatEvent proto.InternalMessageInfo

func (m *ChatEvent) GetInviterUid() uint32 {
	if m != nil {
		return m.InviterUid
	}
	return 0
}

func (m *ChatEvent) GetInviteeUid() uint32 {
	if m != nil {
		return m.InviteeUid
	}
	return 0
}

func (m *ChatEvent) GetOccurrenceTime() uint32 {
	if m != nil {
		return m.OccurrenceTime
	}
	return 0
}

func (m *ChatEvent) GetType() ChatEventType {
	if m != nil {
		return m.Type
	}
	return ChatEventType_ChatEventType_UNDEFINED
}

func init() {
	proto.RegisterType((*ChatCardEvent)(nil), "chatcard.ChatCardEvent")
	proto.RegisterType((*ChatEvent)(nil), "chatcard.ChatEvent")
	proto.RegisterEnum("chatcard.ChatCardEventType", ChatCardEventType_name, ChatCardEventType_value)
	proto.RegisterEnum("chatcard.ChatEventType", ChatEventType_name, ChatEventType_value)
}

func init() {
	proto.RegisterFile("chat-card/card-event.proto", fileDescriptor_card_event_ee998a30cf2c23cf)
}

var fileDescriptor_card_event_ee998a30cf2c23cf = []byte{
	// 405 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x92, 0xd1, 0x6e, 0xda, 0x30,
	0x14, 0x86, 0xe7, 0x84, 0x65, 0xeb, 0xa9, 0x60, 0x99, 0x27, 0x95, 0x0c, 0x2e, 0x9a, 0xf5, 0x66,
	0x11, 0x5b, 0x13, 0x8d, 0x69, 0x0f, 0xb0, 0x52, 0x4f, 0xab, 0x54, 0x51, 0x14, 0xc2, 0x05, 0xbb,
	0x58, 0x64, 0x1c, 0x2b, 0x58, 0x82, 0x24, 0x4a, 0x0c, 0x12, 0xaf, 0xb0, 0xd7, 0xd8, 0x0b, 0xed,
	0x91, 0xa6, 0x18, 0x50, 0x48, 0xc3, 0x4d, 0x62, 0x9f, 0xff, 0xf3, 0x7f, 0xce, 0x6f, 0x19, 0x7a,
	0x6c, 0x49, 0xe5, 0x2d, 0xa3, 0x79, 0xe4, 0x95, 0x9f, 0x5b, 0xbe, 0xe5, 0x89, 0x74, 0xb3, 0x3c,
	0x95, 0x29, 0x7e, 0x5d, 0x6a, 0x65, 0xb5, 0xf7, 0xfe, 0x84, 0x3a, 0xae, 0xf6, 0xd0, 0xcd, 0x3f,
	0x04, 0xed, 0xd1, 0x92, 0xca, 0x11, 0xcd, 0x23, 0x52, 0x1e, 0xc6, 0x26, 0xe8, 0x1b, 0x11, 0x59,
	0xc8, 0x46, 0x4e, 0xdb, 0x2f, 0x97, 0xf8, 0x0a, 0x8c, 0x62, 0x95, 0xc6, 0x34, 0xb1, 0x34, 0x1b,
	0x39, 0x17, 0xfe, 0x61, 0x87, 0x3f, 0x40, 0x4b, 0xd2, 0xb8, 0xb0, 0x74, 0x5b, 0x77, 0x2e, 0x87,
	0x6d, 0xf7, 0xd8, 0xcf, 0x0d, 0x68, 0xec, 0x2b, 0x09, 0x7b, 0xd0, 0x92, 0xbb, 0x8c, 0x5b, 0x2d,
	0x1b, 0x39, 0x9d, 0x61, 0xbf, 0x42, 0x6a, 0x3d, 0x83, 0x5d, 0xc6, 0x7d, 0x05, 0xe2, 0x0e, 0x68,
	0x22, 0xb3, 0x5e, 0xaa, 0x3e, 0x9a, 0xc8, 0xf0, 0x17, 0x78, 0xb5, 0x88, 0x43, 0xe5, 0x61, 0x28,
	0x0f, 0xab, 0xe9, 0x71, 0x17, 0x2b, 0x03, 0x63, 0xa1, 0xfe, 0x37, 0x7f, 0x11, 0x5c, 0x94, 0xd2,
	0x3e, 0xce, 0x35, 0x5c, 0x8a, 0x64, 0x2b, 0x24, 0xcf, 0xc3, 0x2a, 0x16, 0x1c, 0x4a, 0x33, 0x11,
	0x55, 0x00, 0x57, 0x80, 0x76, 0x0a, 0xf0, 0x12, 0xf8, 0x08, 0x6f, 0x52, 0xc6, 0x36, 0x79, 0xce,
	0x13, 0xc6, 0x43, 0x29, 0xd6, 0xdc, 0xd2, 0x15, 0xd4, 0xa9, 0xca, 0x81, 0x58, 0x73, 0xfc, 0xa9,
	0x16, 0xb6, 0x5b, 0x1f, 0xf4, 0x59, 0xd0, 0xc1, 0x1f, 0x04, 0x6f, 0x1b, 0x97, 0x80, 0xaf, 0xa1,
	0xdf, 0x28, 0x86, 0xb3, 0xf1, 0x3d, 0xf9, 0xf1, 0x30, 0x26, 0xf7, 0xe6, 0x0b, 0xdc, 0x87, 0x6e,
	0x13, 0x18, 0x3d, 0x3e, 0x4d, 0x89, 0x89, 0x70, 0x0f, 0xae, 0x9a, 0xe2, 0xd3, 0x84, 0x8c, 0x4d,
	0xed, 0xfc, 0x41, 0x9f, 0x4c, 0x49, 0x60, 0xea, 0x83, 0xdf, 0xfb, 0x47, 0x50, 0xcd, 0x71, 0xa0,
	0xcf, 0xcf, 0xd0, 0x85, 0x77, 0x75, 0x71, 0xfa, 0x7d, 0xfe, 0xf3, 0xc1, 0x44, 0x4d, 0xc1, 0x27,
	0x93, 0xc7, 0xb9, 0xa9, 0xdd, 0x7d, 0xfe, 0x35, 0x88, 0xd3, 0x15, 0x4d, 0x62, 0xf7, 0xdb, 0x50,
	0x4a, 0x97, 0xa5, 0x6b, 0x4f, 0x3d, 0x3f, 0x96, 0xae, 0xbc, 0x82, 0xe7, 0x5b, 0xc1, 0x78, 0xe1,
	0x1d, 0xaf, 0x6b, 0x61, 0x28, 0xed, 0xeb, 0xff, 0x00, 0x00, 0x00, 0xff, 0xff, 0x95, 0x5d, 0x84,
	0x77, 0xdd, 0x02, 0x00, 0x00,
}
