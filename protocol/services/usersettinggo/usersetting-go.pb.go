// Code generated by protoc-gen-go. DO NOT EDIT.
// source: usersetting-go/usersetting-go.proto

package usersettinggo // import "golang.52tt.com/protocol/services/usersettinggo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PhoneType int32

const (
	PhoneType_PhoneTypeAll     PhoneType = 0
	PhoneType_PhoneTypeAndorid PhoneType = 1
	PhoneType_PhoneTypeIos     PhoneType = 2
)

var PhoneType_name = map[int32]string{
	0: "PhoneTypeAll",
	1: "PhoneTypeAndorid",
	2: "PhoneTypeIos",
}
var PhoneType_value = map[string]int32{
	"PhoneTypeAll":     0,
	"PhoneTypeAndorid": 1,
	"PhoneTypeIos":     2,
}

func (x PhoneType) String() string {
	return proto.EnumName(PhoneType_name, int32(x))
}
func (PhoneType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{0}
}

type Setting struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Value                string   `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	Type                 uint32   `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Setting) Reset()         { *m = Setting{} }
func (m *Setting) String() string { return proto.CompactTextString(m) }
func (*Setting) ProtoMessage()    {}
func (*Setting) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{0}
}
func (m *Setting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Setting.Unmarshal(m, b)
}
func (m *Setting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Setting.Marshal(b, m, deterministic)
}
func (dst *Setting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Setting.Merge(dst, src)
}
func (m *Setting) XXX_Size() int {
	return xxx_messageInfo_Setting.Size(m)
}
func (m *Setting) XXX_DiscardUnknown() {
	xxx_messageInfo_Setting.DiscardUnknown(m)
}

var xxx_messageInfo_Setting proto.InternalMessageInfo

func (m *Setting) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Setting) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *Setting) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *Setting) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type SetSettingsReq struct {
	Setting              *Setting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSettingsReq) Reset()         { *m = SetSettingsReq{} }
func (m *SetSettingsReq) String() string { return proto.CompactTextString(m) }
func (*SetSettingsReq) ProtoMessage()    {}
func (*SetSettingsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{1}
}
func (m *SetSettingsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSettingsReq.Unmarshal(m, b)
}
func (m *SetSettingsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSettingsReq.Marshal(b, m, deterministic)
}
func (dst *SetSettingsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSettingsReq.Merge(dst, src)
}
func (m *SetSettingsReq) XXX_Size() int {
	return xxx_messageInfo_SetSettingsReq.Size(m)
}
func (m *SetSettingsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSettingsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetSettingsReq proto.InternalMessageInfo

func (m *SetSettingsReq) GetSetting() *Setting {
	if m != nil {
		return m.Setting
	}
	return nil
}

type SetSettingsResp struct {
	Result               int32    `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSettingsResp) Reset()         { *m = SetSettingsResp{} }
func (m *SetSettingsResp) String() string { return proto.CompactTextString(m) }
func (*SetSettingsResp) ProtoMessage()    {}
func (*SetSettingsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{2}
}
func (m *SetSettingsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSettingsResp.Unmarshal(m, b)
}
func (m *SetSettingsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSettingsResp.Marshal(b, m, deterministic)
}
func (dst *SetSettingsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSettingsResp.Merge(dst, src)
}
func (m *SetSettingsResp) XXX_Size() int {
	return xxx_messageInfo_SetSettingsResp.Size(m)
}
func (m *SetSettingsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSettingsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetSettingsResp proto.InternalMessageInfo

func (m *SetSettingsResp) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

type DelSettingsReq struct {
	Setting              *Setting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSettingsReq) Reset()         { *m = DelSettingsReq{} }
func (m *DelSettingsReq) String() string { return proto.CompactTextString(m) }
func (*DelSettingsReq) ProtoMessage()    {}
func (*DelSettingsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{3}
}
func (m *DelSettingsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSettingsReq.Unmarshal(m, b)
}
func (m *DelSettingsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSettingsReq.Marshal(b, m, deterministic)
}
func (dst *DelSettingsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSettingsReq.Merge(dst, src)
}
func (m *DelSettingsReq) XXX_Size() int {
	return xxx_messageInfo_DelSettingsReq.Size(m)
}
func (m *DelSettingsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSettingsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelSettingsReq proto.InternalMessageInfo

func (m *DelSettingsReq) GetSetting() *Setting {
	if m != nil {
		return m.Setting
	}
	return nil
}

type DelSettingsRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSettingsRsp) Reset()         { *m = DelSettingsRsp{} }
func (m *DelSettingsRsp) String() string { return proto.CompactTextString(m) }
func (*DelSettingsRsp) ProtoMessage()    {}
func (*DelSettingsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{4}
}
func (m *DelSettingsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSettingsRsp.Unmarshal(m, b)
}
func (m *DelSettingsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSettingsRsp.Marshal(b, m, deterministic)
}
func (dst *DelSettingsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSettingsRsp.Merge(dst, src)
}
func (m *DelSettingsRsp) XXX_Size() int {
	return xxx_messageInfo_DelSettingsRsp.Size(m)
}
func (m *DelSettingsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSettingsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_DelSettingsRsp proto.InternalMessageInfo

type MultiType struct {
	TypeList             uint32   `protobuf:"varint,1,opt,name=type_list,json=typeList,proto3" json:"type_list,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiType) Reset()         { *m = MultiType{} }
func (m *MultiType) String() string { return proto.CompactTextString(m) }
func (*MultiType) ProtoMessage()    {}
func (*MultiType) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{5}
}
func (m *MultiType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiType.Unmarshal(m, b)
}
func (m *MultiType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiType.Marshal(b, m, deterministic)
}
func (dst *MultiType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiType.Merge(dst, src)
}
func (m *MultiType) XXX_Size() int {
	return xxx_messageInfo_MultiType.Size(m)
}
func (m *MultiType) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiType.DiscardUnknown(m)
}

var xxx_messageInfo_MultiType proto.InternalMessageInfo

func (m *MultiType) GetTypeList() uint32 {
	if m != nil {
		return m.TypeList
	}
	return 0
}

func (m *MultiType) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type MultiSettings struct {
	SettingList          []*Setting `protobuf:"bytes,1,rep,name=setting_list,json=settingList,proto3" json:"setting_list,omitempty"`
	Uid                  uint32     `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *MultiSettings) Reset()         { *m = MultiSettings{} }
func (m *MultiSettings) String() string { return proto.CompactTextString(m) }
func (*MultiSettings) ProtoMessage()    {}
func (*MultiSettings) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{6}
}
func (m *MultiSettings) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiSettings.Unmarshal(m, b)
}
func (m *MultiSettings) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiSettings.Marshal(b, m, deterministic)
}
func (dst *MultiSettings) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiSettings.Merge(dst, src)
}
func (m *MultiSettings) XXX_Size() int {
	return xxx_messageInfo_MultiSettings.Size(m)
}
func (m *MultiSettings) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiSettings.DiscardUnknown(m)
}

var xxx_messageInfo_MultiSettings proto.InternalMessageInfo

func (m *MultiSettings) GetSettingList() []*Setting {
	if m != nil {
		return m.SettingList
	}
	return nil
}

func (m *MultiSettings) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type MultiKey struct {
	KeyList              []string `protobuf:"bytes,1,rep,name=key_list,json=keyList,proto3" json:"key_list,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiKey) Reset()         { *m = MultiKey{} }
func (m *MultiKey) String() string { return proto.CompactTextString(m) }
func (*MultiKey) ProtoMessage()    {}
func (*MultiKey) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{7}
}
func (m *MultiKey) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiKey.Unmarshal(m, b)
}
func (m *MultiKey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiKey.Marshal(b, m, deterministic)
}
func (dst *MultiKey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiKey.Merge(dst, src)
}
func (m *MultiKey) XXX_Size() int {
	return xxx_messageInfo_MultiKey.Size(m)
}
func (m *MultiKey) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiKey.DiscardUnknown(m)
}

var xxx_messageInfo_MultiKey proto.InternalMessageInfo

func (m *MultiKey) GetKeyList() []string {
	if m != nil {
		return m.KeyList
	}
	return nil
}

func (m *MultiKey) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMultiSettingsByKeyReq struct {
	MulKeyList           []*MultiKey `protobuf:"bytes,1,rep,name=mul_key_list,json=mulKeyList,proto3" json:"mul_key_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetMultiSettingsByKeyReq) Reset()         { *m = GetMultiSettingsByKeyReq{} }
func (m *GetMultiSettingsByKeyReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiSettingsByKeyReq) ProtoMessage()    {}
func (*GetMultiSettingsByKeyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{8}
}
func (m *GetMultiSettingsByKeyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiSettingsByKeyReq.Unmarshal(m, b)
}
func (m *GetMultiSettingsByKeyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiSettingsByKeyReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiSettingsByKeyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiSettingsByKeyReq.Merge(dst, src)
}
func (m *GetMultiSettingsByKeyReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiSettingsByKeyReq.Size(m)
}
func (m *GetMultiSettingsByKeyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiSettingsByKeyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiSettingsByKeyReq proto.InternalMessageInfo

func (m *GetMultiSettingsByKeyReq) GetMulKeyList() []*MultiKey {
	if m != nil {
		return m.MulKeyList
	}
	return nil
}

type GetMultiSettingsByKeyResp struct {
	MultiSettingsList    []*MultiSettings `protobuf:"bytes,1,rep,name=multi_settings_list,json=multiSettingsList,proto3" json:"multi_settings_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetMultiSettingsByKeyResp) Reset()         { *m = GetMultiSettingsByKeyResp{} }
func (m *GetMultiSettingsByKeyResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiSettingsByKeyResp) ProtoMessage()    {}
func (*GetMultiSettingsByKeyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{9}
}
func (m *GetMultiSettingsByKeyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiSettingsByKeyResp.Unmarshal(m, b)
}
func (m *GetMultiSettingsByKeyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiSettingsByKeyResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiSettingsByKeyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiSettingsByKeyResp.Merge(dst, src)
}
func (m *GetMultiSettingsByKeyResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiSettingsByKeyResp.Size(m)
}
func (m *GetMultiSettingsByKeyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiSettingsByKeyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiSettingsByKeyResp proto.InternalMessageInfo

func (m *GetMultiSettingsByKeyResp) GetMultiSettingsList() []*MultiSettings {
	if m != nil {
		return m.MultiSettingsList
	}
	return nil
}

type GetMultiSettingsByTypeReq struct {
	MulTypeList          []*MultiType `protobuf:"bytes,1,rep,name=mul_type_list,json=mulTypeList,proto3" json:"mul_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMultiSettingsByTypeReq) Reset()         { *m = GetMultiSettingsByTypeReq{} }
func (m *GetMultiSettingsByTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiSettingsByTypeReq) ProtoMessage()    {}
func (*GetMultiSettingsByTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{10}
}
func (m *GetMultiSettingsByTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiSettingsByTypeReq.Unmarshal(m, b)
}
func (m *GetMultiSettingsByTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiSettingsByTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiSettingsByTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiSettingsByTypeReq.Merge(dst, src)
}
func (m *GetMultiSettingsByTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiSettingsByTypeReq.Size(m)
}
func (m *GetMultiSettingsByTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiSettingsByTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiSettingsByTypeReq proto.InternalMessageInfo

func (m *GetMultiSettingsByTypeReq) GetMulTypeList() []*MultiType {
	if m != nil {
		return m.MulTypeList
	}
	return nil
}

type GetMultiSettingsByTypeResp struct {
	MultiSettingsList    []*MultiSettings `protobuf:"bytes,1,rep,name=multi_settings_list,json=multiSettingsList,proto3" json:"multi_settings_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetMultiSettingsByTypeResp) Reset()         { *m = GetMultiSettingsByTypeResp{} }
func (m *GetMultiSettingsByTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiSettingsByTypeResp) ProtoMessage()    {}
func (*GetMultiSettingsByTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{11}
}
func (m *GetMultiSettingsByTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiSettingsByTypeResp.Unmarshal(m, b)
}
func (m *GetMultiSettingsByTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiSettingsByTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiSettingsByTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiSettingsByTypeResp.Merge(dst, src)
}
func (m *GetMultiSettingsByTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiSettingsByTypeResp.Size(m)
}
func (m *GetMultiSettingsByTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiSettingsByTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiSettingsByTypeResp proto.InternalMessageInfo

func (m *GetMultiSettingsByTypeResp) GetMultiSettingsList() []*MultiSettings {
	if m != nil {
		return m.MultiSettingsList
	}
	return nil
}

type GetSingleSettingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	KeyList              []string `protobuf:"bytes,2,rep,name=key_list,json=keyList,proto3" json:"key_list,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingleSettingReq) Reset()         { *m = GetSingleSettingReq{} }
func (m *GetSingleSettingReq) String() string { return proto.CompactTextString(m) }
func (*GetSingleSettingReq) ProtoMessage()    {}
func (*GetSingleSettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{12}
}
func (m *GetSingleSettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingleSettingReq.Unmarshal(m, b)
}
func (m *GetSingleSettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingleSettingReq.Marshal(b, m, deterministic)
}
func (dst *GetSingleSettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingleSettingReq.Merge(dst, src)
}
func (m *GetSingleSettingReq) XXX_Size() int {
	return xxx_messageInfo_GetSingleSettingReq.Size(m)
}
func (m *GetSingleSettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingleSettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingleSettingReq proto.InternalMessageInfo

func (m *GetSingleSettingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSingleSettingReq) GetKeyList() []string {
	if m != nil {
		return m.KeyList
	}
	return nil
}

func (m *GetSingleSettingReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetSingleSettingResp struct {
	SettingList          []*Setting `protobuf:"bytes,1,rep,name=setting_list,json=settingList,proto3" json:"setting_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetSingleSettingResp) Reset()         { *m = GetSingleSettingResp{} }
func (m *GetSingleSettingResp) String() string { return proto.CompactTextString(m) }
func (*GetSingleSettingResp) ProtoMessage()    {}
func (*GetSingleSettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{13}
}
func (m *GetSingleSettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingleSettingResp.Unmarshal(m, b)
}
func (m *GetSingleSettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingleSettingResp.Marshal(b, m, deterministic)
}
func (dst *GetSingleSettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingleSettingResp.Merge(dst, src)
}
func (m *GetSingleSettingResp) XXX_Size() int {
	return xxx_messageInfo_GetSingleSettingResp.Size(m)
}
func (m *GetSingleSettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingleSettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingleSettingResp proto.InternalMessageInfo

func (m *GetSingleSettingResp) GetSettingList() []*Setting {
	if m != nil {
		return m.SettingList
	}
	return nil
}

type GetSettingsBySingleKeyReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSettingsBySingleKeyReq) Reset()         { *m = GetSettingsBySingleKeyReq{} }
func (m *GetSettingsBySingleKeyReq) String() string { return proto.CompactTextString(m) }
func (*GetSettingsBySingleKeyReq) ProtoMessage()    {}
func (*GetSettingsBySingleKeyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{14}
}
func (m *GetSettingsBySingleKeyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettingsBySingleKeyReq.Unmarshal(m, b)
}
func (m *GetSettingsBySingleKeyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettingsBySingleKeyReq.Marshal(b, m, deterministic)
}
func (dst *GetSettingsBySingleKeyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettingsBySingleKeyReq.Merge(dst, src)
}
func (m *GetSettingsBySingleKeyReq) XXX_Size() int {
	return xxx_messageInfo_GetSettingsBySingleKeyReq.Size(m)
}
func (m *GetSettingsBySingleKeyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettingsBySingleKeyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettingsBySingleKeyReq proto.InternalMessageInfo

func (m *GetSettingsBySingleKeyReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetSettingsBySingleKeyReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *GetSettingsBySingleKeyReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type FullSettingInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Value                string   `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FullSettingInfo) Reset()         { *m = FullSettingInfo{} }
func (m *FullSettingInfo) String() string { return proto.CompactTextString(m) }
func (*FullSettingInfo) ProtoMessage()    {}
func (*FullSettingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{15}
}
func (m *FullSettingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FullSettingInfo.Unmarshal(m, b)
}
func (m *FullSettingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FullSettingInfo.Marshal(b, m, deterministic)
}
func (dst *FullSettingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FullSettingInfo.Merge(dst, src)
}
func (m *FullSettingInfo) XXX_Size() int {
	return xxx_messageInfo_FullSettingInfo.Size(m)
}
func (m *FullSettingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FullSettingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FullSettingInfo proto.InternalMessageInfo

func (m *FullSettingInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FullSettingInfo) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *FullSettingInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *FullSettingInfo) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type GetSettingsBySingleKeyResp struct {
	MulFullSettingList   []*FullSettingInfo `protobuf:"bytes,1,rep,name=mul_full_setting_list,json=mulFullSettingList,proto3" json:"mul_full_setting_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetSettingsBySingleKeyResp) Reset()         { *m = GetSettingsBySingleKeyResp{} }
func (m *GetSettingsBySingleKeyResp) String() string { return proto.CompactTextString(m) }
func (*GetSettingsBySingleKeyResp) ProtoMessage()    {}
func (*GetSettingsBySingleKeyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{16}
}
func (m *GetSettingsBySingleKeyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettingsBySingleKeyResp.Unmarshal(m, b)
}
func (m *GetSettingsBySingleKeyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettingsBySingleKeyResp.Marshal(b, m, deterministic)
}
func (dst *GetSettingsBySingleKeyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettingsBySingleKeyResp.Merge(dst, src)
}
func (m *GetSettingsBySingleKeyResp) XXX_Size() int {
	return xxx_messageInfo_GetSettingsBySingleKeyResp.Size(m)
}
func (m *GetSettingsBySingleKeyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettingsBySingleKeyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettingsBySingleKeyResp proto.InternalMessageInfo

func (m *GetSettingsBySingleKeyResp) GetMulFullSettingList() []*FullSettingInfo {
	if m != nil {
		return m.MulFullSettingList
	}
	return nil
}

type ContractInfo struct {
	ContractVersion      string    `protobuf:"bytes,1,opt,name=contract_version,json=contractVersion,proto3" json:"contract_version,omitempty"`
	ContractUrl          string    `protobuf:"bytes,2,opt,name=contract_url,json=contractUrl,proto3" json:"contract_url,omitempty"`
	ConcealUrl           string    `protobuf:"bytes,3,opt,name=conceal_url,json=concealUrl,proto3" json:"conceal_url,omitempty"`
	Appid                uint32    `protobuf:"varint,4,opt,name=appid,proto3" json:"appid,omitempty"`
	Marketid             uint32    `protobuf:"varint,5,opt,name=marketid,proto3" json:"marketid,omitempty"`
	UpdateTime           int64     `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	PhoneType            PhoneType `protobuf:"varint,7,opt,name=phone_type,json=phoneType,proto3,enum=usersettinggo.PhoneType" json:"phone_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ContractInfo) Reset()         { *m = ContractInfo{} }
func (m *ContractInfo) String() string { return proto.CompactTextString(m) }
func (*ContractInfo) ProtoMessage()    {}
func (*ContractInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{17}
}
func (m *ContractInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContractInfo.Unmarshal(m, b)
}
func (m *ContractInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContractInfo.Marshal(b, m, deterministic)
}
func (dst *ContractInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContractInfo.Merge(dst, src)
}
func (m *ContractInfo) XXX_Size() int {
	return xxx_messageInfo_ContractInfo.Size(m)
}
func (m *ContractInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ContractInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ContractInfo proto.InternalMessageInfo

func (m *ContractInfo) GetContractVersion() string {
	if m != nil {
		return m.ContractVersion
	}
	return ""
}

func (m *ContractInfo) GetContractUrl() string {
	if m != nil {
		return m.ContractUrl
	}
	return ""
}

func (m *ContractInfo) GetConcealUrl() string {
	if m != nil {
		return m.ConcealUrl
	}
	return ""
}

func (m *ContractInfo) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *ContractInfo) GetMarketid() uint32 {
	if m != nil {
		return m.Marketid
	}
	return 0
}

func (m *ContractInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ContractInfo) GetPhoneType() PhoneType {
	if m != nil {
		return m.PhoneType
	}
	return PhoneType_PhoneTypeAll
}

type GetContractInfosReq struct {
	Offset               uint32    `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Marketid             uint32    `protobuf:"varint,3,opt,name=marketid,proto3" json:"marketid,omitempty"`
	PhoneType            PhoneType `protobuf:"varint,4,opt,name=phone_type,json=phoneType,proto3,enum=usersettinggo.PhoneType" json:"phone_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetContractInfosReq) Reset()         { *m = GetContractInfosReq{} }
func (m *GetContractInfosReq) String() string { return proto.CompactTextString(m) }
func (*GetContractInfosReq) ProtoMessage()    {}
func (*GetContractInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{18}
}
func (m *GetContractInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContractInfosReq.Unmarshal(m, b)
}
func (m *GetContractInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContractInfosReq.Marshal(b, m, deterministic)
}
func (dst *GetContractInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContractInfosReq.Merge(dst, src)
}
func (m *GetContractInfosReq) XXX_Size() int {
	return xxx_messageInfo_GetContractInfosReq.Size(m)
}
func (m *GetContractInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContractInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetContractInfosReq proto.InternalMessageInfo

func (m *GetContractInfosReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetContractInfosReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetContractInfosReq) GetMarketid() uint32 {
	if m != nil {
		return m.Marketid
	}
	return 0
}

func (m *GetContractInfosReq) GetPhoneType() PhoneType {
	if m != nil {
		return m.PhoneType
	}
	return PhoneType_PhoneTypeAll
}

type GetContractInfosRsp struct {
	Infos                []*ContractInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	TotalCnt             uint32          `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetContractInfosRsp) Reset()         { *m = GetContractInfosRsp{} }
func (m *GetContractInfosRsp) String() string { return proto.CompactTextString(m) }
func (*GetContractInfosRsp) ProtoMessage()    {}
func (*GetContractInfosRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{19}
}
func (m *GetContractInfosRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContractInfosRsp.Unmarshal(m, b)
}
func (m *GetContractInfosRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContractInfosRsp.Marshal(b, m, deterministic)
}
func (dst *GetContractInfosRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContractInfosRsp.Merge(dst, src)
}
func (m *GetContractInfosRsp) XXX_Size() int {
	return xxx_messageInfo_GetContractInfosRsp.Size(m)
}
func (m *GetContractInfosRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContractInfosRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetContractInfosRsp proto.InternalMessageInfo

func (m *GetContractInfosRsp) GetInfos() []*ContractInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *GetContractInfosRsp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type GetLastestContractInfoReq struct {
	Appid                uint32    `protobuf:"varint,1,opt,name=appid,proto3" json:"appid,omitempty"`
	Marketid             uint32    `protobuf:"varint,2,opt,name=marketid,proto3" json:"marketid,omitempty"`
	PhoneType            PhoneType `protobuf:"varint,3,opt,name=phone_type,json=phoneType,proto3,enum=usersettinggo.PhoneType" json:"phone_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetLastestContractInfoReq) Reset()         { *m = GetLastestContractInfoReq{} }
func (m *GetLastestContractInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetLastestContractInfoReq) ProtoMessage()    {}
func (*GetLastestContractInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{20}
}
func (m *GetLastestContractInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastestContractInfoReq.Unmarshal(m, b)
}
func (m *GetLastestContractInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastestContractInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetLastestContractInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastestContractInfoReq.Merge(dst, src)
}
func (m *GetLastestContractInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetLastestContractInfoReq.Size(m)
}
func (m *GetLastestContractInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastestContractInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastestContractInfoReq proto.InternalMessageInfo

func (m *GetLastestContractInfoReq) GetAppid() uint32 {
	if m != nil {
		return m.Appid
	}
	return 0
}

func (m *GetLastestContractInfoReq) GetMarketid() uint32 {
	if m != nil {
		return m.Marketid
	}
	return 0
}

func (m *GetLastestContractInfoReq) GetPhoneType() PhoneType {
	if m != nil {
		return m.PhoneType
	}
	return PhoneType_PhoneTypeAll
}

type GetLastestContractInfoRsp struct {
	Info                 *ContractInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetLastestContractInfoRsp) Reset()         { *m = GetLastestContractInfoRsp{} }
func (m *GetLastestContractInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GetLastestContractInfoRsp) ProtoMessage()    {}
func (*GetLastestContractInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{21}
}
func (m *GetLastestContractInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastestContractInfoRsp.Unmarshal(m, b)
}
func (m *GetLastestContractInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastestContractInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GetLastestContractInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastestContractInfoRsp.Merge(dst, src)
}
func (m *GetLastestContractInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GetLastestContractInfoRsp.Size(m)
}
func (m *GetLastestContractInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastestContractInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastestContractInfoRsp proto.InternalMessageInfo

func (m *GetLastestContractInfoRsp) GetInfo() *ContractInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetContractInfoReq struct {
	Info                 *ContractInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetContractInfoReq) Reset()         { *m = SetContractInfoReq{} }
func (m *SetContractInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetContractInfoReq) ProtoMessage()    {}
func (*SetContractInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{22}
}
func (m *SetContractInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetContractInfoReq.Unmarshal(m, b)
}
func (m *SetContractInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetContractInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetContractInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetContractInfoReq.Merge(dst, src)
}
func (m *SetContractInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetContractInfoReq.Size(m)
}
func (m *SetContractInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetContractInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetContractInfoReq proto.InternalMessageInfo

func (m *SetContractInfoReq) GetInfo() *ContractInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetContractInfoRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetContractInfoRsp) Reset()         { *m = SetContractInfoRsp{} }
func (m *SetContractInfoRsp) String() string { return proto.CompactTextString(m) }
func (*SetContractInfoRsp) ProtoMessage()    {}
func (*SetContractInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usersetting_go_e1767789bdbe6893, []int{23}
}
func (m *SetContractInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetContractInfoRsp.Unmarshal(m, b)
}
func (m *SetContractInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetContractInfoRsp.Marshal(b, m, deterministic)
}
func (dst *SetContractInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetContractInfoRsp.Merge(dst, src)
}
func (m *SetContractInfoRsp) XXX_Size() int {
	return xxx_messageInfo_SetContractInfoRsp.Size(m)
}
func (m *SetContractInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetContractInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetContractInfoRsp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*Setting)(nil), "usersettinggo.Setting")
	proto.RegisterType((*SetSettingsReq)(nil), "usersettinggo.SetSettingsReq")
	proto.RegisterType((*SetSettingsResp)(nil), "usersettinggo.SetSettingsResp")
	proto.RegisterType((*DelSettingsReq)(nil), "usersettinggo.DelSettingsReq")
	proto.RegisterType((*DelSettingsRsp)(nil), "usersettinggo.DelSettingsRsp")
	proto.RegisterType((*MultiType)(nil), "usersettinggo.MultiType")
	proto.RegisterType((*MultiSettings)(nil), "usersettinggo.MultiSettings")
	proto.RegisterType((*MultiKey)(nil), "usersettinggo.MultiKey")
	proto.RegisterType((*GetMultiSettingsByKeyReq)(nil), "usersettinggo.GetMultiSettingsByKeyReq")
	proto.RegisterType((*GetMultiSettingsByKeyResp)(nil), "usersettinggo.GetMultiSettingsByKeyResp")
	proto.RegisterType((*GetMultiSettingsByTypeReq)(nil), "usersettinggo.GetMultiSettingsByTypeReq")
	proto.RegisterType((*GetMultiSettingsByTypeResp)(nil), "usersettinggo.GetMultiSettingsByTypeResp")
	proto.RegisterType((*GetSingleSettingReq)(nil), "usersettinggo.GetSingleSettingReq")
	proto.RegisterType((*GetSingleSettingResp)(nil), "usersettinggo.GetSingleSettingResp")
	proto.RegisterType((*GetSettingsBySingleKeyReq)(nil), "usersettinggo.GetSettingsBySingleKeyReq")
	proto.RegisterType((*FullSettingInfo)(nil), "usersettinggo.FullSettingInfo")
	proto.RegisterType((*GetSettingsBySingleKeyResp)(nil), "usersettinggo.GetSettingsBySingleKeyResp")
	proto.RegisterType((*ContractInfo)(nil), "usersettinggo.ContractInfo")
	proto.RegisterType((*GetContractInfosReq)(nil), "usersettinggo.GetContractInfosReq")
	proto.RegisterType((*GetContractInfosRsp)(nil), "usersettinggo.GetContractInfosRsp")
	proto.RegisterType((*GetLastestContractInfoReq)(nil), "usersettinggo.GetLastestContractInfoReq")
	proto.RegisterType((*GetLastestContractInfoRsp)(nil), "usersettinggo.GetLastestContractInfoRsp")
	proto.RegisterType((*SetContractInfoReq)(nil), "usersettinggo.SetContractInfoReq")
	proto.RegisterType((*SetContractInfoRsp)(nil), "usersettinggo.SetContractInfoRsp")
	proto.RegisterEnum("usersettinggo.PhoneType", PhoneType_name, PhoneType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserSettingsClient is the client API for UserSettings service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserSettingsClient interface {
	SetSettings(ctx context.Context, in *SetSettingsReq, opts ...grpc.CallOption) (*SetSettingsResp, error)
	DelSettings(ctx context.Context, in *DelSettingsReq, opts ...grpc.CallOption) (*DelSettingsRsp, error)
	GetSingleSetting(ctx context.Context, in *GetSingleSettingReq, opts ...grpc.CallOption) (*GetSingleSettingResp, error)
	GetContractInfos(ctx context.Context, in *GetContractInfosReq, opts ...grpc.CallOption) (*GetContractInfosRsp, error)
	GetLastestContractInfo(ctx context.Context, in *GetLastestContractInfoReq, opts ...grpc.CallOption) (*GetLastestContractInfoRsp, error)
	SetContractInfo(ctx context.Context, in *SetContractInfoReq, opts ...grpc.CallOption) (*SetContractInfoRsp, error)
}

type userSettingsClient struct {
	cc *grpc.ClientConn
}

func NewUserSettingsClient(cc *grpc.ClientConn) UserSettingsClient {
	return &userSettingsClient{cc}
}

func (c *userSettingsClient) SetSettings(ctx context.Context, in *SetSettingsReq, opts ...grpc.CallOption) (*SetSettingsResp, error) {
	out := new(SetSettingsResp)
	err := c.cc.Invoke(ctx, "/usersettinggo.UserSettings/SetSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userSettingsClient) DelSettings(ctx context.Context, in *DelSettingsReq, opts ...grpc.CallOption) (*DelSettingsRsp, error) {
	out := new(DelSettingsRsp)
	err := c.cc.Invoke(ctx, "/usersettinggo.UserSettings/DelSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userSettingsClient) GetSingleSetting(ctx context.Context, in *GetSingleSettingReq, opts ...grpc.CallOption) (*GetSingleSettingResp, error) {
	out := new(GetSingleSettingResp)
	err := c.cc.Invoke(ctx, "/usersettinggo.UserSettings/GetSingleSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userSettingsClient) GetContractInfos(ctx context.Context, in *GetContractInfosReq, opts ...grpc.CallOption) (*GetContractInfosRsp, error) {
	out := new(GetContractInfosRsp)
	err := c.cc.Invoke(ctx, "/usersettinggo.UserSettings/GetContractInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userSettingsClient) GetLastestContractInfo(ctx context.Context, in *GetLastestContractInfoReq, opts ...grpc.CallOption) (*GetLastestContractInfoRsp, error) {
	out := new(GetLastestContractInfoRsp)
	err := c.cc.Invoke(ctx, "/usersettinggo.UserSettings/GetLastestContractInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userSettingsClient) SetContractInfo(ctx context.Context, in *SetContractInfoReq, opts ...grpc.CallOption) (*SetContractInfoRsp, error) {
	out := new(SetContractInfoRsp)
	err := c.cc.Invoke(ctx, "/usersettinggo.UserSettings/SetContractInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserSettingsServer is the server API for UserSettings service.
type UserSettingsServer interface {
	SetSettings(context.Context, *SetSettingsReq) (*SetSettingsResp, error)
	DelSettings(context.Context, *DelSettingsReq) (*DelSettingsRsp, error)
	GetSingleSetting(context.Context, *GetSingleSettingReq) (*GetSingleSettingResp, error)
	GetContractInfos(context.Context, *GetContractInfosReq) (*GetContractInfosRsp, error)
	GetLastestContractInfo(context.Context, *GetLastestContractInfoReq) (*GetLastestContractInfoRsp, error)
	SetContractInfo(context.Context, *SetContractInfoReq) (*SetContractInfoRsp, error)
}

func RegisterUserSettingsServer(s *grpc.Server, srv UserSettingsServer) {
	s.RegisterService(&_UserSettings_serviceDesc, srv)
}

func _UserSettings_SetSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSettingsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSettingsServer).SetSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usersettinggo.UserSettings/SetSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSettingsServer).SetSettings(ctx, req.(*SetSettingsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserSettings_DelSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSettingsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSettingsServer).DelSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usersettinggo.UserSettings/DelSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSettingsServer).DelSettings(ctx, req.(*DelSettingsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserSettings_GetSingleSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingleSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSettingsServer).GetSingleSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usersettinggo.UserSettings/GetSingleSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSettingsServer).GetSingleSetting(ctx, req.(*GetSingleSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserSettings_GetContractInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContractInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSettingsServer).GetContractInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usersettinggo.UserSettings/GetContractInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSettingsServer).GetContractInfos(ctx, req.(*GetContractInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserSettings_GetLastestContractInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastestContractInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSettingsServer).GetLastestContractInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usersettinggo.UserSettings/GetLastestContractInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSettingsServer).GetLastestContractInfo(ctx, req.(*GetLastestContractInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserSettings_SetContractInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetContractInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserSettingsServer).SetContractInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usersettinggo.UserSettings/SetContractInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserSettingsServer).SetContractInfo(ctx, req.(*SetContractInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserSettings_serviceDesc = grpc.ServiceDesc{
	ServiceName: "usersettinggo.UserSettings",
	HandlerType: (*UserSettingsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetSettings",
			Handler:    _UserSettings_SetSettings_Handler,
		},
		{
			MethodName: "DelSettings",
			Handler:    _UserSettings_DelSettings_Handler,
		},
		{
			MethodName: "GetSingleSetting",
			Handler:    _UserSettings_GetSingleSetting_Handler,
		},
		{
			MethodName: "GetContractInfos",
			Handler:    _UserSettings_GetContractInfos_Handler,
		},
		{
			MethodName: "GetLastestContractInfo",
			Handler:    _UserSettings_GetLastestContractInfo_Handler,
		},
		{
			MethodName: "SetContractInfo",
			Handler:    _UserSettings_SetContractInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "usersetting-go/usersetting-go.proto",
}

func init() {
	proto.RegisterFile("usersetting-go/usersetting-go.proto", fileDescriptor_usersetting_go_e1767789bdbe6893)
}

var fileDescriptor_usersetting_go_e1767789bdbe6893 = []byte{
	// 947 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0xdd, 0x6e, 0xdb, 0x36,
	0x14, 0xae, 0xfc, 0x13, 0xdb, 0xc7, 0x76, 0xa3, 0xb1, 0x5e, 0xa6, 0xb8, 0x5b, 0x97, 0xaa, 0x37,
	0xee, 0x80, 0xd9, 0x6b, 0x86, 0xa1, 0xe8, 0xb0, 0x9b, 0xa5, 0xeb, 0x8a, 0x22, 0xe9, 0xb0, 0x2a,
	0x49, 0x87, 0x0e, 0x19, 0x0c, 0xcd, 0x66, 0x3c, 0x2e, 0x94, 0xc8, 0x8a, 0x54, 0x00, 0xbf, 0xc0,
	0x80, 0xbd, 0xc1, 0x5e, 0x64, 0xef, 0x37, 0x90, 0xa2, 0x6c, 0x49, 0x96, 0x1d, 0x0f, 0xe9, 0x1d,
	0xcf, 0xd1, 0xe1, 0x77, 0xbe, 0xf3, 0x2b, 0xc2, 0xa3, 0x58, 0xe0, 0x48, 0x60, 0x29, 0x49, 0x38,
	0xfb, 0x72, 0xc6, 0x46, 0x79, 0x71, 0xc8, 0x23, 0x26, 0x19, 0xea, 0x66, 0xb4, 0x33, 0xe6, 0xfe,
	0x02, 0x8d, 0xd3, 0x44, 0x40, 0x36, 0x54, 0x63, 0x32, 0x75, 0xac, 0x03, 0x6b, 0xd0, 0xf5, 0xd4,
	0x51, 0x69, 0xae, 0xf0, 0xdc, 0xa9, 0x1c, 0x58, 0x83, 0x96, 0xa7, 0x8e, 0xa8, 0x07, 0xf5, 0x6b,
	0x9f, 0xc6, 0xd8, 0xa9, 0x6a, 0x5d, 0x22, 0x20, 0x04, 0x35, 0x39, 0xe7, 0xd8, 0xa9, 0xe9, 0xab,
	0xfa, 0xec, 0x1e, 0xc1, 0xdd, 0x53, 0x2c, 0x0d, 0xb6, 0xf0, 0xf0, 0x7b, 0xf4, 0x15, 0x34, 0x8c,
	0x5f, 0xed, 0xa3, 0x7d, 0xb8, 0x37, 0xcc, 0x71, 0x19, 0x1a, 0x63, 0x2f, 0x35, 0x73, 0x1f, 0xc3,
	0x6e, 0x0e, 0x43, 0x70, 0xb4, 0x07, 0x3b, 0x11, 0x16, 0x31, 0x95, 0x1a, 0xa3, 0xee, 0x19, 0x49,
	0xb9, 0xfb, 0x01, 0xd3, 0xdb, 0xb9, 0xb3, 0xf3, 0x18, 0x82, 0xbb, 0xdf, 0x42, 0xeb, 0x75, 0x4c,
	0x25, 0x39, 0x9b, 0x73, 0x8c, 0xee, 0x43, 0x4b, 0x45, 0x36, 0xa6, 0x44, 0x48, 0x93, 0xa5, 0xa6,
	0x52, 0x9c, 0x10, 0x21, 0xd3, 0xe4, 0x55, 0x16, 0xc9, 0x73, 0x2f, 0xa0, 0xab, 0xef, 0xa6, 0x78,
	0xe8, 0x19, 0x74, 0x8c, 0xa7, 0x14, 0xa2, 0xba, 0x81, 0x55, 0xdb, 0xa8, 0xd6, 0xa0, 0x3f, 0x85,
	0xa6, 0x46, 0x3f, 0xc6, 0x73, 0xb4, 0x0f, 0xcd, 0x2b, 0x3c, 0x5f, 0x82, 0xb6, 0xbc, 0xc6, 0x15,
	0x9e, 0xaf, 0xb9, 0x78, 0x0e, 0xce, 0x4b, 0x2c, 0x73, 0xcc, 0x8e, 0xe6, 0xc7, 0x78, 0xae, 0x52,
	0xf6, 0x0c, 0x3a, 0x41, 0x4c, 0xc7, 0x39, 0xb0, 0xf6, 0xe1, 0x27, 0x05, 0x86, 0xa9, 0x5f, 0x0f,
	0x82, 0x98, 0x1e, 0x27, 0x8e, 0x5c, 0x02, 0xfb, 0x6b, 0x60, 0x05, 0x47, 0x27, 0x70, 0x2f, 0x50,
	0x5f, 0xc6, 0x06, 0x44, 0x64, 0xe1, 0x3f, 0x2d, 0x83, 0x5f, 0x14, 0xe1, 0xa3, 0x20, 0x2b, 0x6a,
	0x57, 0xef, 0xca, 0x5c, 0xa9, 0x0a, 0xa9, 0x10, 0xbe, 0x83, 0xae, 0x0a, 0x21, 0x5b, 0x28, 0xe5,
	0xc4, 0x29, 0x73, 0xa2, 0xef, 0xb4, 0x83, 0x98, 0x9e, 0x99, 0x2a, 0xba, 0x7f, 0x42, 0x7f, 0x1d,
	0xf4, 0x07, 0x0f, 0xe3, 0x2d, 0xdc, 0x7b, 0x89, 0xe5, 0x29, 0x09, 0x67, 0x14, 0xa7, 0x45, 0xc7,
	0xef, 0x4b, 0xa6, 0x30, 0x5b, 0xde, 0x4a, 0xbe, 0xbc, 0xe9, 0xe0, 0x55, 0x33, 0x83, 0xf7, 0x06,
	0x7a, 0xab, 0xb8, 0x82, 0xdf, 0xa2, 0xfd, 0xdc, 0x0b, 0x9d, 0xf1, 0x65, 0x46, 0x12, 0x70, 0xd3,
	0x34, 0xfb, 0xd0, 0x8c, 0xc9, 0x74, 0x89, 0xd9, 0xf5, 0x1a, 0x31, 0x99, 0xa6, 0xdd, 0x57, 0xd8,
	0x1f, 0x65, 0x84, 0xc7, 0xb0, 0xfb, 0x63, 0x4c, 0xd3, 0xb9, 0x7b, 0x15, 0x5e, 0xb2, 0xad, 0x56,
	0x51, 0x09, 0xd4, 0x72, 0x3d, 0xd5, 0x32, 0xeb, 0xc9, 0x65, 0xba, 0xaa, 0xa5, 0xf4, 0x05, 0x47,
	0x6f, 0xe0, 0x63, 0xd5, 0x31, 0x97, 0x31, 0xa5, 0xe3, 0x92, 0x04, 0x3d, 0x28, 0x24, 0xa8, 0x40,
	0xd5, 0x43, 0x41, 0x4c, 0x33, 0x3a, 0x9d, 0xaf, 0xbf, 0x2b, 0xd0, 0x79, 0xce, 0x42, 0x19, 0xf9,
	0x13, 0xa9, 0xe3, 0x79, 0x0c, 0xf6, 0xc4, 0xc8, 0xe3, 0x6b, 0x1c, 0x09, 0xc2, 0x42, 0x1d, 0x5c,
	0xcb, 0xdb, 0x4d, 0xf5, 0x6f, 0x13, 0x35, 0x7a, 0x08, 0x9d, 0x85, 0x69, 0x1c, 0x51, 0x13, 0x71,
	0x3b, 0xd5, 0x9d, 0x47, 0x14, 0x7d, 0x0e, 0x4a, 0x9c, 0x60, 0x9f, 0x6a, 0x8b, 0x64, 0x15, 0x83,
	0x51, 0x29, 0x83, 0x1e, 0xd4, 0x7d, 0xce, 0xc9, 0xd4, 0x2c, 0xe4, 0x44, 0x40, 0x7d, 0x68, 0x06,
	0x7e, 0x74, 0x85, 0x25, 0x99, 0x3a, 0xf5, 0x64, 0x7d, 0xa5, 0xb2, 0x82, 0x8c, 0xf9, 0xd4, 0x97,
	0x78, 0x2c, 0x49, 0x80, 0x9d, 0x9d, 0x03, 0x6b, 0x50, 0xf5, 0x20, 0x51, 0x9d, 0x91, 0x00, 0xa3,
	0xa7, 0x00, 0xfc, 0x0f, 0x16, 0x62, 0x3d, 0x59, 0x4e, 0xe3, 0xc0, 0x1a, 0xdc, 0x5d, 0x19, 0xaa,
	0x9f, 0x95, 0x81, 0x9e, 0x96, 0x16, 0x4f, 0x8f, 0xee, 0x3f, 0x96, 0xee, 0xf3, 0x6c, 0x3a, 0xf4,
	0x7a, 0xde, 0x83, 0x1d, 0x76, 0x79, 0x29, 0x70, 0xba, 0x4a, 0x8d, 0xa4, 0xb8, 0x53, 0x12, 0x10,
	0x69, 0x76, 0x56, 0x22, 0xe4, 0xb8, 0x57, 0x0b, 0xdc, 0xf3, 0xd4, 0x6a, 0xdb, 0x53, 0xc3, 0x25,
	0xcc, 0x04, 0x47, 0x4f, 0xa0, 0x4e, 0xd4, 0xd9, 0x34, 0xc0, 0xfd, 0x02, 0x54, 0xd6, 0xde, 0x4b,
	0x2c, 0xf5, 0xaf, 0x81, 0x49, 0x9f, 0x8e, 0x27, 0x61, 0x4a, 0xbc, 0xa9, 0x15, 0xcf, 0x43, 0xe9,
	0xfe, 0x65, 0xe9, 0xf1, 0x39, 0xf1, 0x85, 0xc4, 0x22, 0xe7, 0x4e, 0xe5, 0x61, 0x51, 0x2b, 0x6b,
	0x5d, 0xad, 0x2a, 0x1b, 0xe3, 0xad, 0x6e, 0x1f, 0xef, 0xc9, 0x5a, 0x1e, 0x82, 0xa3, 0x11, 0xd4,
	0x54, 0x2c, 0xe6, 0x5f, 0xb9, 0x31, 0x68, 0x6d, 0xe8, 0xbe, 0x00, 0x74, 0x8a, 0x57, 0xc2, 0xf9,
	0xdf, 0x30, 0xbd, 0x55, 0x18, 0xc1, 0xbf, 0x78, 0x01, 0xad, 0x45, 0x08, 0xc8, 0x86, 0xce, 0x42,
	0xf8, 0x9e, 0x52, 0xfb, 0x0e, 0xea, 0x81, 0xbd, 0xd4, 0x84, 0x53, 0x16, 0x91, 0xa9, 0x6d, 0xe5,
	0xec, 0x5e, 0x31, 0x61, 0x57, 0x0e, 0xff, 0xad, 0x41, 0xe7, 0x5c, 0xe0, 0x68, 0xf1, 0x0f, 0xfe,
	0x09, 0xda, 0x99, 0x17, 0x05, 0xfa, 0x6c, 0x75, 0xfb, 0x65, 0x9e, 0x10, 0xfd, 0x07, 0x9b, 0x3e,
	0x0b, 0xee, 0xde, 0x41, 0xaf, 0xa1, 0x9d, 0x79, 0x32, 0xac, 0xe0, 0xe5, 0x9f, 0x24, 0xfd, 0x4d,
	0x9f, 0x35, 0xdc, 0x6f, 0x60, 0x17, 0x77, 0x37, 0x72, 0x0b, 0x97, 0x4a, 0x7e, 0x1a, 0xfd, 0x47,
	0x37, 0xda, 0x68, 0xf8, 0x0b, 0x0d, 0x9f, 0x6b, 0xf8, 0x32, 0xf8, 0xe2, 0xac, 0xf6, 0x6f, 0xb4,
	0xd1, 0xe8, 0x21, 0xec, 0x95, 0xb7, 0x17, 0x1a, 0xac, 0xde, 0x2f, 0x9f, 0x86, 0xfe, 0x96, 0x96,
	0xda, 0xdf, 0x3b, 0xfd, 0x3a, 0xcc, 0x39, 0x7a, 0xb8, 0x5a, 0xb0, 0xa2, 0x87, 0x9b, 0x4c, 0x14,
	0xf4, 0xd1, 0x93, 0x5f, 0x47, 0x33, 0x46, 0xfd, 0x70, 0x36, 0xfc, 0xe6, 0x50, 0xca, 0xe1, 0x84,
	0x05, 0x23, 0xfd, 0x7a, 0x9e, 0x30, 0x3a, 0x12, 0x38, 0xba, 0x26, 0x13, 0x2c, 0x46, 0x39, 0x9c,
	0xdf, 0x77, 0xb4, 0xc1, 0xd7, 0xff, 0x05, 0x00, 0x00, 0xff, 0xff, 0xea, 0x83, 0x69, 0xeb, 0x85,
	0x0b, 0x00, 0x00,
}
