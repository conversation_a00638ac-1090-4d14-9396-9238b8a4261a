// Code generated by protoc-gen-go. DO NOT EDIT.
// source: security-go/security-go.proto

package security_go // import "golang.52tt.com/protocol/services/security-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SIGNING_IDENTITY int32

const (
	SIGNING_IDENTITY_SIGN_ANCHOR_INDENTITY_UNSPECIFIED                SIGNING_IDENTITY = 0
	SIGNING_IDENTITY_SIGN_ANCHOR_INDENTITY_MULTIPLAYER                SIGNING_IDENTITY = 1
	SIGNING_IDENTITY_SIGN_ANCHOR_INDENTITY_RADIO_LIVE                 SIGNING_IDENTITY = 2
	SIGNING_IDENTITY_SIGN_ANCHOR_INDENTITY_MULTIPLAYER_AND_RADIO_LIVE SIGNING_IDENTITY = 3
)

var SIGNING_IDENTITY_name = map[int32]string{
	0: "SIGN_ANCHOR_INDENTITY_UNSPECIFIED",
	1: "SIGN_ANCHOR_INDENTITY_MULTIPLAYER",
	2: "SIGN_ANCHOR_INDENTITY_RADIO_LIVE",
	3: "SIGN_ANCHOR_INDENTITY_MULTIPLAYER_AND_RADIO_LIVE",
}
var SIGNING_IDENTITY_value = map[string]int32{
	"SIGN_ANCHOR_INDENTITY_UNSPECIFIED":                0,
	"SIGN_ANCHOR_INDENTITY_MULTIPLAYER":                1,
	"SIGN_ANCHOR_INDENTITY_RADIO_LIVE":                 2,
	"SIGN_ANCHOR_INDENTITY_MULTIPLAYER_AND_RADIO_LIVE": 3,
}

func (x SIGNING_IDENTITY) String() string {
	return proto.EnumName(SIGNING_IDENTITY_name, int32(x))
}
func (SIGNING_IDENTITY) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_security_go_f840f553dc72b0b4, []int{0}
}

type UnregWhiteUserInfo struct {
	Ttid                 string           `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Uid                  uint32           `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	SigningIdentity      SIGNING_IDENTITY `protobuf:"varint,3,opt,name=signing_identity,json=signingIdentity,proto3,enum=security_go.SIGNING_IDENTITY" json:"signing_identity,omitempty"`
	FinallyScore         int64            `protobuf:"varint,4,opt,name=finally_score,json=finallyScore,proto3" json:"finally_score,omitempty"`
	BindPhone            string           `protobuf:"bytes,5,opt,name=bind_phone,json=bindPhone,proto3" json:"bind_phone,omitempty"`
	DeviceModel          string           `protobuf:"bytes,6,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`
	ClientType           uint32           `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	Remark               string           `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`
	OperationTime        int64            `protobuf:"varint,9,opt,name=operation_time,json=operationTime,proto3" json:"operation_time,omitempty"`
	OperatorName         string           `protobuf:"bytes,10,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UnregWhiteUserInfo) Reset()         { *m = UnregWhiteUserInfo{} }
func (m *UnregWhiteUserInfo) String() string { return proto.CompactTextString(m) }
func (*UnregWhiteUserInfo) ProtoMessage()    {}
func (*UnregWhiteUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_security_go_f840f553dc72b0b4, []int{0}
}
func (m *UnregWhiteUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnregWhiteUserInfo.Unmarshal(m, b)
}
func (m *UnregWhiteUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnregWhiteUserInfo.Marshal(b, m, deterministic)
}
func (dst *UnregWhiteUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnregWhiteUserInfo.Merge(dst, src)
}
func (m *UnregWhiteUserInfo) XXX_Size() int {
	return xxx_messageInfo_UnregWhiteUserInfo.Size(m)
}
func (m *UnregWhiteUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UnregWhiteUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UnregWhiteUserInfo proto.InternalMessageInfo

func (m *UnregWhiteUserInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *UnregWhiteUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnregWhiteUserInfo) GetSigningIdentity() SIGNING_IDENTITY {
	if m != nil {
		return m.SigningIdentity
	}
	return SIGNING_IDENTITY_SIGN_ANCHOR_INDENTITY_UNSPECIFIED
}

func (m *UnregWhiteUserInfo) GetFinallyScore() int64 {
	if m != nil {
		return m.FinallyScore
	}
	return 0
}

func (m *UnregWhiteUserInfo) GetBindPhone() string {
	if m != nil {
		return m.BindPhone
	}
	return ""
}

func (m *UnregWhiteUserInfo) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

func (m *UnregWhiteUserInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *UnregWhiteUserInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *UnregWhiteUserInfo) GetOperationTime() int64 {
	if m != nil {
		return m.OperationTime
	}
	return 0
}

func (m *UnregWhiteUserInfo) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

type GetUnregWhiteUserInfoReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            int64    `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUnregWhiteUserInfoReq) Reset()         { *m = GetUnregWhiteUserInfoReq{} }
func (m *GetUnregWhiteUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUnregWhiteUserInfoReq) ProtoMessage()    {}
func (*GetUnregWhiteUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_security_go_f840f553dc72b0b4, []int{1}
}
func (m *GetUnregWhiteUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUnregWhiteUserInfoReq.Unmarshal(m, b)
}
func (m *GetUnregWhiteUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUnregWhiteUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUnregWhiteUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUnregWhiteUserInfoReq.Merge(dst, src)
}
func (m *GetUnregWhiteUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUnregWhiteUserInfoReq.Size(m)
}
func (m *GetUnregWhiteUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUnregWhiteUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUnregWhiteUserInfoReq proto.InternalMessageInfo

func (m *GetUnregWhiteUserInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetUnregWhiteUserInfoReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUnregWhiteUserInfoReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetUnregWhiteUserInfoReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUnregWhiteUserInfoReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetUnregWhiteUserInfoResp struct {
	UserInfoList         []*UnregWhiteUserInfo `protobuf:"bytes,1,rep,name=user_info_list,json=userInfoList,proto3" json:"user_info_list,omitempty"`
	TotalCnt             uint32                `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetUnregWhiteUserInfoResp) Reset()         { *m = GetUnregWhiteUserInfoResp{} }
func (m *GetUnregWhiteUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUnregWhiteUserInfoResp) ProtoMessage()    {}
func (*GetUnregWhiteUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_security_go_f840f553dc72b0b4, []int{2}
}
func (m *GetUnregWhiteUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUnregWhiteUserInfoResp.Unmarshal(m, b)
}
func (m *GetUnregWhiteUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUnregWhiteUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUnregWhiteUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUnregWhiteUserInfoResp.Merge(dst, src)
}
func (m *GetUnregWhiteUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUnregWhiteUserInfoResp.Size(m)
}
func (m *GetUnregWhiteUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUnregWhiteUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUnregWhiteUserInfoResp proto.InternalMessageInfo

func (m *GetUnregWhiteUserInfoResp) GetUserInfoList() []*UnregWhiteUserInfo {
	if m != nil {
		return m.UserInfoList
	}
	return nil
}

func (m *GetUnregWhiteUserInfoResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type AddUserUnregWhiteReq struct {
	WhiteUserList        []*UnregWhiteUserInfo `protobuf:"bytes,1,rep,name=white_user_list,json=whiteUserList,proto3" json:"white_user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *AddUserUnregWhiteReq) Reset()         { *m = AddUserUnregWhiteReq{} }
func (m *AddUserUnregWhiteReq) String() string { return proto.CompactTextString(m) }
func (*AddUserUnregWhiteReq) ProtoMessage()    {}
func (*AddUserUnregWhiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_security_go_f840f553dc72b0b4, []int{3}
}
func (m *AddUserUnregWhiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserUnregWhiteReq.Unmarshal(m, b)
}
func (m *AddUserUnregWhiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserUnregWhiteReq.Marshal(b, m, deterministic)
}
func (dst *AddUserUnregWhiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserUnregWhiteReq.Merge(dst, src)
}
func (m *AddUserUnregWhiteReq) XXX_Size() int {
	return xxx_messageInfo_AddUserUnregWhiteReq.Size(m)
}
func (m *AddUserUnregWhiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserUnregWhiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserUnregWhiteReq proto.InternalMessageInfo

func (m *AddUserUnregWhiteReq) GetWhiteUserList() []*UnregWhiteUserInfo {
	if m != nil {
		return m.WhiteUserList
	}
	return nil
}

type AddUserUnregWhiteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserUnregWhiteResp) Reset()         { *m = AddUserUnregWhiteResp{} }
func (m *AddUserUnregWhiteResp) String() string { return proto.CompactTextString(m) }
func (*AddUserUnregWhiteResp) ProtoMessage()    {}
func (*AddUserUnregWhiteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_security_go_f840f553dc72b0b4, []int{4}
}
func (m *AddUserUnregWhiteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserUnregWhiteResp.Unmarshal(m, b)
}
func (m *AddUserUnregWhiteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserUnregWhiteResp.Marshal(b, m, deterministic)
}
func (dst *AddUserUnregWhiteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserUnregWhiteResp.Merge(dst, src)
}
func (m *AddUserUnregWhiteResp) XXX_Size() int {
	return xxx_messageInfo_AddUserUnregWhiteResp.Size(m)
}
func (m *AddUserUnregWhiteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserUnregWhiteResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserUnregWhiteResp proto.InternalMessageInfo

type CheckUnregWhiteUserReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUnregWhiteUserReq) Reset()         { *m = CheckUnregWhiteUserReq{} }
func (m *CheckUnregWhiteUserReq) String() string { return proto.CompactTextString(m) }
func (*CheckUnregWhiteUserReq) ProtoMessage()    {}
func (*CheckUnregWhiteUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_security_go_f840f553dc72b0b4, []int{5}
}
func (m *CheckUnregWhiteUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUnregWhiteUserReq.Unmarshal(m, b)
}
func (m *CheckUnregWhiteUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUnregWhiteUserReq.Marshal(b, m, deterministic)
}
func (dst *CheckUnregWhiteUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUnregWhiteUserReq.Merge(dst, src)
}
func (m *CheckUnregWhiteUserReq) XXX_Size() int {
	return xxx_messageInfo_CheckUnregWhiteUserReq.Size(m)
}
func (m *CheckUnregWhiteUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUnregWhiteUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUnregWhiteUserReq proto.InternalMessageInfo

func (m *CheckUnregWhiteUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUnregWhiteUserResp struct {
	IsWhite              bool     `protobuf:"varint,1,opt,name=isWhite,proto3" json:"isWhite,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUnregWhiteUserResp) Reset()         { *m = CheckUnregWhiteUserResp{} }
func (m *CheckUnregWhiteUserResp) String() string { return proto.CompactTextString(m) }
func (*CheckUnregWhiteUserResp) ProtoMessage()    {}
func (*CheckUnregWhiteUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_security_go_f840f553dc72b0b4, []int{6}
}
func (m *CheckUnregWhiteUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUnregWhiteUserResp.Unmarshal(m, b)
}
func (m *CheckUnregWhiteUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUnregWhiteUserResp.Marshal(b, m, deterministic)
}
func (dst *CheckUnregWhiteUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUnregWhiteUserResp.Merge(dst, src)
}
func (m *CheckUnregWhiteUserResp) XXX_Size() int {
	return xxx_messageInfo_CheckUnregWhiteUserResp.Size(m)
}
func (m *CheckUnregWhiteUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUnregWhiteUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUnregWhiteUserResp proto.InternalMessageInfo

func (m *CheckUnregWhiteUserResp) GetIsWhite() bool {
	if m != nil {
		return m.IsWhite
	}
	return false
}

func init() {
	proto.RegisterType((*UnregWhiteUserInfo)(nil), "security_go.UnregWhiteUserInfo")
	proto.RegisterType((*GetUnregWhiteUserInfoReq)(nil), "security_go.GetUnregWhiteUserInfoReq")
	proto.RegisterType((*GetUnregWhiteUserInfoResp)(nil), "security_go.GetUnregWhiteUserInfoResp")
	proto.RegisterType((*AddUserUnregWhiteReq)(nil), "security_go.AddUserUnregWhiteReq")
	proto.RegisterType((*AddUserUnregWhiteResp)(nil), "security_go.AddUserUnregWhiteResp")
	proto.RegisterType((*CheckUnregWhiteUserReq)(nil), "security_go.CheckUnregWhiteUserReq")
	proto.RegisterType((*CheckUnregWhiteUserResp)(nil), "security_go.CheckUnregWhiteUserResp")
	proto.RegisterEnum("security_go.SIGNING_IDENTITY", SIGNING_IDENTITY_name, SIGNING_IDENTITY_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SecurityGoClient is the client API for SecurityGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SecurityGoClient interface {
	GetUnregWhiteUserInfo(ctx context.Context, in *GetUnregWhiteUserInfoReq, opts ...grpc.CallOption) (*GetUnregWhiteUserInfoResp, error)
	AddUserUnregWhite(ctx context.Context, in *AddUserUnregWhiteReq, opts ...grpc.CallOption) (*AddUserUnregWhiteResp, error)
	CheckUnregWhiteUser(ctx context.Context, in *CheckUnregWhiteUserReq, opts ...grpc.CallOption) (*CheckUnregWhiteUserResp, error)
}

type securityGoClient struct {
	cc *grpc.ClientConn
}

func NewSecurityGoClient(cc *grpc.ClientConn) SecurityGoClient {
	return &securityGoClient{cc}
}

func (c *securityGoClient) GetUnregWhiteUserInfo(ctx context.Context, in *GetUnregWhiteUserInfoReq, opts ...grpc.CallOption) (*GetUnregWhiteUserInfoResp, error) {
	out := new(GetUnregWhiteUserInfoResp)
	err := c.cc.Invoke(ctx, "/security_go.SecurityGo/GetUnregWhiteUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityGoClient) AddUserUnregWhite(ctx context.Context, in *AddUserUnregWhiteReq, opts ...grpc.CallOption) (*AddUserUnregWhiteResp, error) {
	out := new(AddUserUnregWhiteResp)
	err := c.cc.Invoke(ctx, "/security_go.SecurityGo/AddUserUnregWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securityGoClient) CheckUnregWhiteUser(ctx context.Context, in *CheckUnregWhiteUserReq, opts ...grpc.CallOption) (*CheckUnregWhiteUserResp, error) {
	out := new(CheckUnregWhiteUserResp)
	err := c.cc.Invoke(ctx, "/security_go.SecurityGo/CheckUnregWhiteUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SecurityGoServer is the server API for SecurityGo service.
type SecurityGoServer interface {
	GetUnregWhiteUserInfo(context.Context, *GetUnregWhiteUserInfoReq) (*GetUnregWhiteUserInfoResp, error)
	AddUserUnregWhite(context.Context, *AddUserUnregWhiteReq) (*AddUserUnregWhiteResp, error)
	CheckUnregWhiteUser(context.Context, *CheckUnregWhiteUserReq) (*CheckUnregWhiteUserResp, error)
}

func RegisterSecurityGoServer(s *grpc.Server, srv SecurityGoServer) {
	s.RegisterService(&_SecurityGo_serviceDesc, srv)
}

func _SecurityGo_GetUnregWhiteUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnregWhiteUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityGoServer).GetUnregWhiteUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security_go.SecurityGo/GetUnregWhiteUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityGoServer).GetUnregWhiteUserInfo(ctx, req.(*GetUnregWhiteUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecurityGo_AddUserUnregWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserUnregWhiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityGoServer).AddUserUnregWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security_go.SecurityGo/AddUserUnregWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityGoServer).AddUserUnregWhite(ctx, req.(*AddUserUnregWhiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecurityGo_CheckUnregWhiteUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUnregWhiteUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityGoServer).CheckUnregWhiteUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security_go.SecurityGo/CheckUnregWhiteUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityGoServer).CheckUnregWhiteUser(ctx, req.(*CheckUnregWhiteUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SecurityGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "security_go.SecurityGo",
	HandlerType: (*SecurityGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUnregWhiteUserInfo",
			Handler:    _SecurityGo_GetUnregWhiteUserInfo_Handler,
		},
		{
			MethodName: "AddUserUnregWhite",
			Handler:    _SecurityGo_AddUserUnregWhite_Handler,
		},
		{
			MethodName: "CheckUnregWhiteUser",
			Handler:    _SecurityGo_CheckUnregWhiteUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "security-go/security-go.proto",
}

func init() {
	proto.RegisterFile("security-go/security-go.proto", fileDescriptor_security_go_f840f553dc72b0b4)
}

var fileDescriptor_security_go_f840f553dc72b0b4 = []byte{
	// 735 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x54, 0xef, 0x6e, 0xe2, 0x46,
	0x10, 0x3f, 0xc3, 0x25, 0xc0, 0x00, 0x39, 0xba, 0xbd, 0x3f, 0x0e, 0x55, 0x14, 0xe2, 0xbb, 0x54,
	0xe8, 0xa4, 0x83, 0x8a, 0x6b, 0x1f, 0x80, 0x02, 0xe5, 0x2c, 0x11, 0x27, 0x32, 0xd0, 0x2a, 0x55,
	0xa5, 0x15, 0xb1, 0x17, 0xb3, 0x8a, 0xbd, 0xeb, 0x7a, 0x97, 0xa6, 0x7c, 0xea, 0x93, 0xf4, 0x39,
	0xfa, 0xa5, 0x8f, 0x90, 0x87, 0xe9, 0xe7, 0x7e, 0xaa, 0x76, 0x31, 0x09, 0x34, 0x8e, 0xd2, 0x7e,
	0xf2, 0xce, 0x6f, 0xe6, 0x37, 0xf3, 0xdb, 0x99, 0xf1, 0xc2, 0x91, 0x20, 0xde, 0x32, 0xa1, 0x72,
	0xf5, 0x21, 0xe0, 0xed, 0xad, 0x73, 0x2b, 0x4e, 0xb8, 0xe4, 0xa8, 0xbc, 0x81, 0x70, 0xc0, 0xeb,
	0xc7, 0xe4, 0x57, 0x49, 0x98, 0xa0, 0x9c, 0xb5, 0x79, 0x2c, 0x29, 0x67, 0x62, 0xf3, 0x5d, 0x47,
	0x5b, 0x7f, 0xe5, 0x00, 0x4d, 0x59, 0x42, 0x82, 0x1f, 0x16, 0x54, 0x92, 0xa9, 0x20, 0x89, 0xcd,
	0xe6, 0x1c, 0x21, 0x78, 0x2e, 0x25, 0xf5, 0x4d, 0xa3, 0x61, 0x34, 0x4b, 0xae, 0x3e, 0xa3, 0x1a,
	0xe4, 0x97, 0xd4, 0x37, 0x73, 0x0d, 0xa3, 0x59, 0x75, 0xd5, 0x11, 0x7d, 0x82, 0x9a, 0xa0, 0x01,
	0xa3, 0x2c, 0xc0, 0xd4, 0x27, 0x4c, 0x52, 0xb9, 0x32, 0xf3, 0x0d, 0xa3, 0x79, 0xd0, 0x39, 0x6a,
	0x6d, 0xa9, 0x68, 0x8d, 0xed, 0xa1, 0x63, 0x3b, 0x43, 0x6c, 0xf7, 0x07, 0xce, 0xc4, 0x9e, 0x5c,
	0xba, 0x2f, 0x52, 0x9a, 0x9d, 0xb2, 0xd0, 0x5b, 0xa8, 0xce, 0x29, 0x9b, 0x85, 0xe1, 0x0a, 0x0b,
	0x8f, 0x27, 0xc4, 0x7c, 0xde, 0x30, 0x9a, 0x79, 0xb7, 0x92, 0x82, 0x63, 0x85, 0xa1, 0x23, 0x80,
	0x2b, 0xca, 0x7c, 0x1c, 0x2f, 0x38, 0x23, 0xe6, 0x9e, 0x96, 0x56, 0x52, 0xc8, 0x85, 0x02, 0xd0,
	0x09, 0x54, 0x7c, 0xf2, 0x0b, 0xf5, 0x08, 0x8e, 0xb8, 0x4f, 0x42, 0x73, 0x5f, 0x07, 0x94, 0xd7,
	0xd8, 0x99, 0x82, 0xd0, 0x31, 0x94, 0xbd, 0x90, 0x12, 0x26, 0xb1, 0x5c, 0xc5, 0xc4, 0x2c, 0xe8,
	0xab, 0xc0, 0x1a, 0x9a, 0xac, 0x62, 0x82, 0x5e, 0xc3, 0x7e, 0x42, 0xa2, 0x59, 0x72, 0x6d, 0x16,
	0x35, 0x3b, 0xb5, 0xd0, 0x29, 0x1c, 0xf0, 0x98, 0x24, 0x33, 0xd5, 0x3a, 0x2c, 0x69, 0x44, 0xcc,
	0x92, 0x16, 0x58, 0xbd, 0x43, 0x27, 0x34, 0x22, 0xea, 0x1a, 0x6b, 0x80, 0x27, 0x98, 0xcd, 0x22,
	0x62, 0x82, 0xce, 0x52, 0xd9, 0x80, 0xce, 0x2c, 0x22, 0xd6, 0xef, 0x06, 0x98, 0x43, 0x22, 0x1f,
	0x76, 0xdd, 0x25, 0x3f, 0xa3, 0x43, 0x28, 0x2e, 0xa9, 0x8f, 0x43, 0x2a, 0xa4, 0x69, 0x34, 0xf2,
	0xcd, 0xaa, 0x5b, 0x58, 0x52, 0x7f, 0x44, 0x85, 0x54, 0xda, 0xf8, 0x7c, 0x2e, 0x88, 0x4c, 0x47,
	0x90, 0x5a, 0xe8, 0x25, 0xec, 0x85, 0x34, 0xa2, 0x52, 0xb7, 0xbe, 0xea, 0xae, 0x0d, 0xdd, 0x2c,
	0x12, 0xd0, 0x54, 0xed, 0xba, 0x9d, 0x25, 0x8d, 0x68, 0xa5, 0x87, 0x50, 0x24, 0xcc, 0x5f, 0x3b,
	0xf7, 0xb4, 0xb3, 0x40, 0x98, 0xaf, 0x5c, 0xd6, 0x6f, 0x70, 0xf8, 0x88, 0x3c, 0x11, 0xa3, 0x01,
	0x1c, 0x2c, 0x05, 0x49, 0x30, 0x65, 0x73, 0x7e, 0xaf, 0xb2, 0xdc, 0x39, 0xde, 0x19, 0x78, 0x06,
	0xb9, 0xb2, 0x4c, 0x4f, 0xfa, 0x2e, 0x5f, 0x40, 0x49, 0x72, 0x39, 0x0b, 0xb1, 0xc7, 0x36, 0xd7,
	0x29, 0x6a, 0xa0, 0xc7, 0xa4, 0x85, 0xe1, 0x65, 0xd7, 0xf7, 0x15, 0xf3, 0x3e, 0x8f, 0xea, 0xcd,
	0x10, 0x5e, 0xdc, 0xa8, 0x33, 0xd6, 0x0a, 0xfe, 0x4f, 0xf1, 0xea, 0xcd, 0xc6, 0x54, 0xd5, 0xad,
	0x37, 0xf0, 0x2a, 0xa3, 0x80, 0x88, 0xad, 0xf7, 0xf0, 0xba, 0xb7, 0x20, 0xde, 0xf5, 0x6e, 0x0a,
	0x55, 0x3b, 0x5d, 0x7e, 0xe3, 0x6e, 0xf9, 0xad, 0x8f, 0xf0, 0x26, 0x33, 0x56, 0xc4, 0xc8, 0x84,
	0x02, 0x15, 0x1a, 0xd2, 0x84, 0xa2, 0xbb, 0x31, 0xdf, 0xff, 0x69, 0x40, 0xed, 0xdf, 0x7f, 0x03,
	0x3a, 0x85, 0x13, 0x85, 0xe1, 0xae, 0xd3, 0xfb, 0x74, 0xee, 0x62, 0xdb, 0x49, 0x1d, 0x78, 0xea,
	0x8c, 0x2f, 0x06, 0x3d, 0xfb, 0x3b, 0x7b, 0xd0, 0xaf, 0x3d, 0x7b, 0x3c, 0xec, 0x6c, 0x3a, 0x9a,
	0xd8, 0x17, 0xa3, 0xee, 0xe5, 0xc0, 0xad, 0x19, 0xe8, 0x1d, 0x34, 0xb2, 0xc3, 0xdc, 0x6e, 0xdf,
	0x3e, 0xc7, 0x23, 0xfb, 0xfb, 0x41, 0x2d, 0x87, 0xbe, 0x86, 0xaf, 0x9e, 0x4c, 0x86, 0xbb, 0x4e,
	0x7f, 0x9b, 0x95, 0xef, 0xdc, 0xe6, 0x00, 0xc6, 0x69, 0xab, 0x87, 0x1c, 0x2d, 0xe0, 0x55, 0xe6,
	0xa6, 0xa0, 0xd3, 0x9d, 0x81, 0x3c, 0xb6, 0xec, 0xf5, 0x2f, 0xff, 0x4b, 0x98, 0x88, 0xad, 0x67,
	0xe8, 0x27, 0xf8, 0xec, 0xc1, 0xc4, 0xd0, 0xc9, 0x0e, 0x3d, 0x6b, 0x65, 0xea, 0xd6, 0x53, 0x21,
	0x3a, 0xfb, 0x15, 0x7c, 0x9e, 0x31, 0x4a, 0xf4, 0x76, 0x87, 0x9c, 0xbd, 0x18, 0xf5, 0x77, 0x4f,
	0x07, 0xa9, 0x1a, 0x75, 0xf4, 0xf7, 0x1f, 0xb7, 0x93, 0x2a, 0x94, 0xb7, 0x1e, 0xec, 0x6f, 0xdb,
	0x3f, 0x7e, 0x08, 0x78, 0x38, 0x63, 0x41, 0xeb, 0x9b, 0x8e, 0x94, 0x2d, 0x8f, 0x47, 0x6d, 0xfd,
	0x2a, 0x7b, 0x3c, 0x6c, 0x0b, 0x92, 0xa8, 0x77, 0x4b, 0x6c, 0xbf, 0xf0, 0x57, 0xfb, 0xda, 0xfd,
	0xf1, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x41, 0x51, 0xf9, 0xa5, 0x03, 0x06, 0x00, 0x00,
}
