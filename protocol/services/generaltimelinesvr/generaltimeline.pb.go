// Code generated by protoc-gen-gogo.
// source: services/generaltimeline/generaltimeline.proto
// DO NOT EDIT!

/*
	Package GeneralTimeline is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/generaltimeline/generaltimeline.proto

	It has these top-level messages:
		GeneralTimelineMsg
		NewGameCircleMsg
		CircleUpdateMsg
		MallActivity
		MallActivityMsg
		AppFileUpdateMsg
		GameTabUpdateMsg
		RedPacketActivityInfo
		RedPacketStageInfo
		RedPacketUpdateMsg
		WriteTimelineMsgReq
		WriteTimelineMsgResp
		WriteUpdateMsgReq
		WriteUpdateMsgResp
		PullTimelineMsgReq
		PullTimelineMsgResp
		MarkReadedReq
		MarkReadedResp
		GetReadedReq
		GetReadedResp
		GetLastSeqidReq
		GetLastSeqidResp
*/
package GeneralTimeline

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type GeneralTimelineMsg_TYPE int32

const (
	GeneralTimelineMsg_NEW_GAME_Circle_MSG GeneralTimelineMsg_TYPE = 1
	GeneralTimelineMsg_CIRCLE_UPDATE_MSG   GeneralTimelineMsg_TYPE = 2
	GeneralTimelineMsg_MALL_ACT_MSG        GeneralTimelineMsg_TYPE = 3
	GeneralTimelineMsg_APP_FILE_UPDATE     GeneralTimelineMsg_TYPE = 4
	GeneralTimelineMsg_GAME_TAB_MSG        GeneralTimelineMsg_TYPE = 5
	GeneralTimelineMsg_RED_PACKET_MSG      GeneralTimelineMsg_TYPE = 6
)

var GeneralTimelineMsg_TYPE_name = map[int32]string{
	1: "NEW_GAME_Circle_MSG",
	2: "CIRCLE_UPDATE_MSG",
	3: "MALL_ACT_MSG",
	4: "APP_FILE_UPDATE",
	5: "GAME_TAB_MSG",
	6: "RED_PACKET_MSG",
}
var GeneralTimelineMsg_TYPE_value = map[string]int32{
	"NEW_GAME_Circle_MSG": 1,
	"CIRCLE_UPDATE_MSG":   2,
	"MALL_ACT_MSG":        3,
	"APP_FILE_UPDATE":     4,
	"GAME_TAB_MSG":        5,
	"RED_PACKET_MSG":      6,
}

func (x GeneralTimelineMsg_TYPE) Enum() *GeneralTimelineMsg_TYPE {
	p := new(GeneralTimelineMsg_TYPE)
	*p = x
	return p
}
func (x GeneralTimelineMsg_TYPE) String() string {
	return proto.EnumName(GeneralTimelineMsg_TYPE_name, int32(x))
}
func (x *GeneralTimelineMsg_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GeneralTimelineMsg_TYPE_value, data, "GeneralTimelineMsg_TYPE")
	if err != nil {
		return err
	}
	*x = GeneralTimelineMsg_TYPE(value)
	return nil
}
func (GeneralTimelineMsg_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{0, 0}
}

type GameTabUpdateMsg_TYPE int32

const (
	GameTabUpdateMsg_GAMETAB_TYPE_WELFARE   GameTabUpdateMsg_TYPE = 1
	GameTabUpdateMsg_GAMETAB_TYPE_BEST_GAME GameTabUpdateMsg_TYPE = 2
	GameTabUpdateMsg_GAMETAB_TYPE_NEW_GAME  GameTabUpdateMsg_TYPE = 3
	GameTabUpdateMsg_GAMETAB_TYPE_HOT_GAME  GameTabUpdateMsg_TYPE = 4
)

var GameTabUpdateMsg_TYPE_name = map[int32]string{
	1: "GAMETAB_TYPE_WELFARE",
	2: "GAMETAB_TYPE_BEST_GAME",
	3: "GAMETAB_TYPE_NEW_GAME",
	4: "GAMETAB_TYPE_HOT_GAME",
}
var GameTabUpdateMsg_TYPE_value = map[string]int32{
	"GAMETAB_TYPE_WELFARE":   1,
	"GAMETAB_TYPE_BEST_GAME": 2,
	"GAMETAB_TYPE_NEW_GAME":  3,
	"GAMETAB_TYPE_HOT_GAME":  4,
}

func (x GameTabUpdateMsg_TYPE) Enum() *GameTabUpdateMsg_TYPE {
	p := new(GameTabUpdateMsg_TYPE)
	*p = x
	return p
}
func (x GameTabUpdateMsg_TYPE) String() string {
	return proto.EnumName(GameTabUpdateMsg_TYPE_name, int32(x))
}
func (x *GameTabUpdateMsg_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GameTabUpdateMsg_TYPE_value, data, "GameTabUpdateMsg_TYPE")
	if err != nil {
		return err
	}
	*x = GameTabUpdateMsg_TYPE(value)
	return nil
}
func (GameTabUpdateMsg_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{6, 0}
}

type RedPacketStageInfo_StageType int32

const (
	RedPacketStageInfo_LOTTERY        RedPacketStageInfo_StageType = 1
	RedPacketStageInfo_MASS_GUILDBUFF RedPacketStageInfo_StageType = 2
	RedPacketStageInfo_PREPARE        RedPacketStageInfo_StageType = 3
	RedPacketStageInfo_DEFAULT        RedPacketStageInfo_StageType = 4
)

var RedPacketStageInfo_StageType_name = map[int32]string{
	1: "LOTTERY",
	2: "MASS_GUILDBUFF",
	3: "PREPARE",
	4: "DEFAULT",
}
var RedPacketStageInfo_StageType_value = map[string]int32{
	"LOTTERY":        1,
	"MASS_GUILDBUFF": 2,
	"PREPARE":        3,
	"DEFAULT":        4,
}

func (x RedPacketStageInfo_StageType) Enum() *RedPacketStageInfo_StageType {
	p := new(RedPacketStageInfo_StageType)
	*p = x
	return p
}
func (x RedPacketStageInfo_StageType) String() string {
	return proto.EnumName(RedPacketStageInfo_StageType_name, int32(x))
}
func (x *RedPacketStageInfo_StageType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RedPacketStageInfo_StageType_value, data, "RedPacketStageInfo_StageType")
	if err != nil {
		return err
	}
	*x = RedPacketStageInfo_StageType(value)
	return nil
}
func (RedPacketStageInfo_StageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{8, 0}
}

// ------------------------------------------
// timeline的通用存储结构，所有消息都是包含在这个结构体里面。
// ------------------------------------------
type GeneralTimelineMsg struct {
	Type   uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	Seqid  uint32 `protobuf:"varint,2,req,name=seqid" json:"seqid"`
	MsgBin []byte `protobuf:"bytes,3,req,name=msg_bin,json=msgBin" json:"msg_bin"`
}

func (m *GeneralTimelineMsg) Reset()         { *m = GeneralTimelineMsg{} }
func (m *GeneralTimelineMsg) String() string { return proto.CompactTextString(m) }
func (*GeneralTimelineMsg) ProtoMessage()    {}
func (*GeneralTimelineMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{0}
}

func (m *GeneralTimelineMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GeneralTimelineMsg) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

func (m *GeneralTimelineMsg) GetMsgBin() []byte {
	if m != nil {
		return m.MsgBin
	}
	return nil
}

type NewGameCircleMsg struct {
	CircleId uint32 `protobuf:"varint,1,req,name=circle_id,json=circleId" json:"circle_id"`
	NewBegin uint32 `protobuf:"varint,2,req,name=new_begin,json=newBegin" json:"new_begin"`
	NewEnd   uint32 `protobuf:"varint,3,req,name=new_end,json=newEnd" json:"new_end"`
}

func (m *NewGameCircleMsg) Reset()                    { *m = NewGameCircleMsg{} }
func (m *NewGameCircleMsg) String() string            { return proto.CompactTextString(m) }
func (*NewGameCircleMsg) ProtoMessage()               {}
func (*NewGameCircleMsg) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{1} }

func (m *NewGameCircleMsg) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *NewGameCircleMsg) GetNewBegin() uint32 {
	if m != nil {
		return m.NewBegin
	}
	return 0
}

func (m *NewGameCircleMsg) GetNewEnd() uint32 {
	if m != nil {
		return m.NewEnd
	}
	return 0
}

type CircleUpdateMsg struct {
	CircleId uint32 `protobuf:"varint,1,req,name=circle_id,json=circleId" json:"circle_id"`
	IsDelete bool   `protobuf:"varint,2,req,name=is_delete,json=isDelete" json:"is_delete"`
}

func (m *CircleUpdateMsg) Reset()                    { *m = CircleUpdateMsg{} }
func (m *CircleUpdateMsg) String() string            { return proto.CompactTextString(m) }
func (*CircleUpdateMsg) ProtoMessage()               {}
func (*CircleUpdateMsg) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{2} }

func (m *CircleUpdateMsg) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleUpdateMsg) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

type MallActivity struct {
	ActId     uint32 `protobuf:"varint,1,req,name=act_id,json=actId" json:"act_id"`
	Url       string `protobuf:"bytes,2,req,name=url" json:"url"`
	Title     string `protobuf:"bytes,3,req,name=title" json:"title"`
	SubTitle  string `protobuf:"bytes,4,opt,name=sub_title,json=subTitle" json:"sub_title"`
	BeginTime uint64 `protobuf:"varint,5,req,name=begin_time,json=beginTime" json:"begin_time"`
}

func (m *MallActivity) Reset()                    { *m = MallActivity{} }
func (m *MallActivity) String() string            { return proto.CompactTextString(m) }
func (*MallActivity) ProtoMessage()               {}
func (*MallActivity) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{3} }

func (m *MallActivity) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *MallActivity) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *MallActivity) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MallActivity) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *MallActivity) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

type MallActivityMsg struct {
	CurrentAct *MallActivity `protobuf:"bytes,1,opt,name=current_act,json=currentAct" json:"current_act,omitempty"`
	AppId      uint32        `protobuf:"varint,2,opt,name=app_id,json=appId" json:"app_id"`
}

func (m *MallActivityMsg) Reset()                    { *m = MallActivityMsg{} }
func (m *MallActivityMsg) String() string            { return proto.CompactTextString(m) }
func (*MallActivityMsg) ProtoMessage()               {}
func (*MallActivityMsg) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{4} }

func (m *MallActivityMsg) GetCurrentAct() *MallActivity {
	if m != nil {
		return m.CurrentAct
	}
	return nil
}

func (m *MallActivityMsg) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

type AppFileUpdateMsg struct {
	AppId       uint32 `protobuf:"varint,1,req,name=app_id,json=appId" json:"app_id"`
	FileName    string `protobuf:"bytes,2,req,name=file_name,json=fileName" json:"file_name"`
	DownloadUrl string `protobuf:"bytes,3,req,name=download_url,json=downloadUrl" json:"download_url"`
	Md5Sum      string `protobuf:"bytes,4,req,name=md5_sum,json=md5Sum" json:"md5_sum"`
}

func (m *AppFileUpdateMsg) Reset()                    { *m = AppFileUpdateMsg{} }
func (m *AppFileUpdateMsg) String() string            { return proto.CompactTextString(m) }
func (*AppFileUpdateMsg) ProtoMessage()               {}
func (*AppFileUpdateMsg) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{5} }

func (m *AppFileUpdateMsg) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *AppFileUpdateMsg) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *AppFileUpdateMsg) GetDownloadUrl() string {
	if m != nil {
		return m.DownloadUrl
	}
	return ""
}

func (m *AppFileUpdateMsg) GetMd5Sum() string {
	if m != nil {
		return m.Md5Sum
	}
	return ""
}

type GameTabUpdateMsg struct {
	MsgType uint32 `protobuf:"varint,1,req,name=msg_type,json=msgType" json:"msg_type"`
}

func (m *GameTabUpdateMsg) Reset()                    { *m = GameTabUpdateMsg{} }
func (m *GameTabUpdateMsg) String() string            { return proto.CompactTextString(m) }
func (*GameTabUpdateMsg) ProtoMessage()               {}
func (*GameTabUpdateMsg) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{6} }

func (m *GameTabUpdateMsg) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

// *
// 红包活动定义
type RedPacketActivityInfo struct {
	Id           uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Name         string `protobuf:"bytes,2,req,name=name" json:"name"`
	BeginTime    uint64 `protobuf:"varint,3,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime      uint64 `protobuf:"varint,4,req,name=end_time,json=endTime" json:"end_time"`
	ComboTimeout uint32 `protobuf:"varint,5,opt,name=combo_timeout,json=comboTimeout" json:"combo_timeout"`
}

func (m *RedPacketActivityInfo) Reset()         { *m = RedPacketActivityInfo{} }
func (m *RedPacketActivityInfo) String() string { return proto.CompactTextString(m) }
func (*RedPacketActivityInfo) ProtoMessage()    {}
func (*RedPacketActivityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{7}
}

func (m *RedPacketActivityInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RedPacketActivityInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RedPacketActivityInfo) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *RedPacketActivityInfo) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *RedPacketActivityInfo) GetComboTimeout() uint32 {
	if m != nil {
		return m.ComboTimeout
	}
	return 0
}

// *
// 红包阶段定义
type RedPacketStageInfo struct {
	StageId    uint32   `protobuf:"varint,1,req,name=stage_id,json=stageId" json:"stage_id"`
	ActivityId uint32   `protobuf:"varint,2,req,name=activity_id,json=activityId" json:"activity_id"`
	Type       uint32   `protobuf:"varint,3,req,name=type" json:"type"`
	Name       string   `protobuf:"bytes,4,opt,name=name" json:"name"`
	BeginTime  uint64   `protobuf:"varint,5,opt,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime    uint64   `protobuf:"varint,6,opt,name=end_time,json=endTime" json:"end_time"`
	AdTextList []string `protobuf:"bytes,7,rep,name=ad_text_list,json=adTextList" json:"ad_text_list,omitempty"`
	// 阶段内发送抢红包请求的最小间隔, 精确至`毫秒`
	// 客户端必须严格按照此间隔控制请求频率
	MinReqInterval uint32 `protobuf:"varint,8,req,name=min_req_interval,json=minReqInterval" json:"min_req_interval"`
}

func (m *RedPacketStageInfo) Reset()         { *m = RedPacketStageInfo{} }
func (m *RedPacketStageInfo) String() string { return proto.CompactTextString(m) }
func (*RedPacketStageInfo) ProtoMessage()    {}
func (*RedPacketStageInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{8}
}

func (m *RedPacketStageInfo) GetStageId() uint32 {
	if m != nil {
		return m.StageId
	}
	return 0
}

func (m *RedPacketStageInfo) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *RedPacketStageInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RedPacketStageInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RedPacketStageInfo) GetBeginTime() uint64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *RedPacketStageInfo) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *RedPacketStageInfo) GetAdTextList() []string {
	if m != nil {
		return m.AdTextList
	}
	return nil
}

func (m *RedPacketStageInfo) GetMinReqInterval() uint32 {
	if m != nil {
		return m.MinReqInterval
	}
	return 0
}

type RedPacketUpdateMsg struct {
	Act       *RedPacketActivityInfo `protobuf:"bytes,1,opt,name=act" json:"act,omitempty"`
	StageList []*RedPacketStageInfo  `protobuf:"bytes,2,rep,name=stage_list,json=stageList" json:"stage_list,omitempty"`
}

func (m *RedPacketUpdateMsg) Reset()         { *m = RedPacketUpdateMsg{} }
func (m *RedPacketUpdateMsg) String() string { return proto.CompactTextString(m) }
func (*RedPacketUpdateMsg) ProtoMessage()    {}
func (*RedPacketUpdateMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{9}
}

func (m *RedPacketUpdateMsg) GetAct() *RedPacketActivityInfo {
	if m != nil {
		return m.Act
	}
	return nil
}

func (m *RedPacketUpdateMsg) GetStageList() []*RedPacketStageInfo {
	if m != nil {
		return m.StageList
	}
	return nil
}

// 使用setx/set存储
type WriteTimelineMsgReq struct {
	Msg *GeneralTimelineMsg `protobuf:"bytes,1,req,name=msg" json:"msg,omitempty"`
	Ttl uint32              `protobuf:"varint,2,opt,name=ttl" json:"ttl"`
}

func (m *WriteTimelineMsgReq) Reset()         { *m = WriteTimelineMsgReq{} }
func (m *WriteTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*WriteTimelineMsgReq) ProtoMessage()    {}
func (*WriteTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{10}
}

func (m *WriteTimelineMsgReq) GetMsg() *GeneralTimelineMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *WriteTimelineMsgReq) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

type WriteTimelineMsgResp struct {
}

func (m *WriteTimelineMsgResp) Reset()         { *m = WriteTimelineMsgResp{} }
func (m *WriteTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*WriteTimelineMsgResp) ProtoMessage()    {}
func (*WriteTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{11}
}

// 使用hset存储
type WriteUpdateMsgReq struct {
	Bucket string              `protobuf:"bytes,1,req,name=bucket" json:"bucket"`
	Key    string              `protobuf:"bytes,2,req,name=key" json:"key"`
	Msg    *GeneralTimelineMsg `protobuf:"bytes,3,req,name=msg" json:"msg,omitempty"`
}

func (m *WriteUpdateMsgReq) Reset()         { *m = WriteUpdateMsgReq{} }
func (m *WriteUpdateMsgReq) String() string { return proto.CompactTextString(m) }
func (*WriteUpdateMsgReq) ProtoMessage()    {}
func (*WriteUpdateMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{12}
}

func (m *WriteUpdateMsgReq) GetBucket() string {
	if m != nil {
		return m.Bucket
	}
	return ""
}

func (m *WriteUpdateMsgReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *WriteUpdateMsgReq) GetMsg() *GeneralTimelineMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type WriteUpdateMsgResp struct {
}

func (m *WriteUpdateMsgResp) Reset()         { *m = WriteUpdateMsgResp{} }
func (m *WriteUpdateMsgResp) String() string { return proto.CompactTextString(m) }
func (*WriteUpdateMsgResp) ProtoMessage()    {}
func (*WriteUpdateMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{13}
}

// ------------------------------------------
// 拉取timeline
// ------------------------------------------
type PullTimelineMsgReq struct {
	StartSeqid      uint32   `protobuf:"varint,1,req,name=start_seqid,json=startSeqid" json:"start_seqid"`
	Limit           uint32   `protobuf:"varint,2,req,name=limit" json:"limit"`
	BucketList      []string `protobuf:"bytes,3,rep,name=bucket_list,json=bucketList" json:"bucket_list,omitempty"`
	IncludeTimeline bool     `protobuf:"varint,4,opt,name=include_timeline,json=includeTimeline" json:"include_timeline"`
}

func (m *PullTimelineMsgReq) Reset()         { *m = PullTimelineMsgReq{} }
func (m *PullTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*PullTimelineMsgReq) ProtoMessage()    {}
func (*PullTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{14}
}

func (m *PullTimelineMsgReq) GetStartSeqid() uint32 {
	if m != nil {
		return m.StartSeqid
	}
	return 0
}

func (m *PullTimelineMsgReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *PullTimelineMsgReq) GetBucketList() []string {
	if m != nil {
		return m.BucketList
	}
	return nil
}

func (m *PullTimelineMsgReq) GetIncludeTimeline() bool {
	if m != nil {
		return m.IncludeTimeline
	}
	return false
}

type PullTimelineMsgResp struct {
	MsgList []*GeneralTimelineMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList" json:"msg_list,omitempty"`
}

func (m *PullTimelineMsgResp) Reset()         { *m = PullTimelineMsgResp{} }
func (m *PullTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*PullTimelineMsgResp) ProtoMessage()    {}
func (*PullTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGeneraltimeline, []int{15}
}

func (m *PullTimelineMsgResp) GetMsgList() []*GeneralTimelineMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

// ------------------------------------------
// 用户标记已读
// ------------------------------------------
type MarkReadedReq struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Seqid uint32 `protobuf:"varint,3,req,name=seqid" json:"seqid"`
}

func (m *MarkReadedReq) Reset()                    { *m = MarkReadedReq{} }
func (m *MarkReadedReq) String() string            { return proto.CompactTextString(m) }
func (*MarkReadedReq) ProtoMessage()               {}
func (*MarkReadedReq) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{16} }

func (m *MarkReadedReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MarkReadedReq) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

type MarkReadedResp struct {
}

func (m *MarkReadedResp) Reset()                    { *m = MarkReadedResp{} }
func (m *MarkReadedResp) String() string            { return proto.CompactTextString(m) }
func (*MarkReadedResp) ProtoMessage()               {}
func (*MarkReadedResp) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{17} }

type GetReadedReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetReadedReq) Reset()                    { *m = GetReadedReq{} }
func (m *GetReadedReq) String() string            { return proto.CompactTextString(m) }
func (*GetReadedReq) ProtoMessage()               {}
func (*GetReadedReq) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{18} }

func (m *GetReadedReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetReadedResp struct {
	Seqid uint32 `protobuf:"varint,1,req,name=seqid" json:"seqid"`
}

func (m *GetReadedResp) Reset()                    { *m = GetReadedResp{} }
func (m *GetReadedResp) String() string            { return proto.CompactTextString(m) }
func (*GetReadedResp) ProtoMessage()               {}
func (*GetReadedResp) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{19} }

func (m *GetReadedResp) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

// -----------------------------------------------------
// 最新的seq id
// -----------------------------------------------------
type GetLastSeqidReq struct {
}

func (m *GetLastSeqidReq) Reset()                    { *m = GetLastSeqidReq{} }
func (m *GetLastSeqidReq) String() string            { return proto.CompactTextString(m) }
func (*GetLastSeqidReq) ProtoMessage()               {}
func (*GetLastSeqidReq) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{20} }

type GetLastSeqidResp struct {
	Seqid uint32 `protobuf:"varint,1,req,name=seqid" json:"seqid"`
}

func (m *GetLastSeqidResp) Reset()                    { *m = GetLastSeqidResp{} }
func (m *GetLastSeqidResp) String() string            { return proto.CompactTextString(m) }
func (*GetLastSeqidResp) ProtoMessage()               {}
func (*GetLastSeqidResp) Descriptor() ([]byte, []int) { return fileDescriptorGeneraltimeline, []int{21} }

func (m *GetLastSeqidResp) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

func init() {
	proto.RegisterType((*GeneralTimelineMsg)(nil), "GeneralTimeline.GeneralTimelineMsg")
	proto.RegisterType((*NewGameCircleMsg)(nil), "GeneralTimeline.NewGameCircleMsg")
	proto.RegisterType((*CircleUpdateMsg)(nil), "GeneralTimeline.CircleUpdateMsg")
	proto.RegisterType((*MallActivity)(nil), "GeneralTimeline.MallActivity")
	proto.RegisterType((*MallActivityMsg)(nil), "GeneralTimeline.MallActivityMsg")
	proto.RegisterType((*AppFileUpdateMsg)(nil), "GeneralTimeline.AppFileUpdateMsg")
	proto.RegisterType((*GameTabUpdateMsg)(nil), "GeneralTimeline.GameTabUpdateMsg")
	proto.RegisterType((*RedPacketActivityInfo)(nil), "GeneralTimeline.RedPacketActivityInfo")
	proto.RegisterType((*RedPacketStageInfo)(nil), "GeneralTimeline.RedPacketStageInfo")
	proto.RegisterType((*RedPacketUpdateMsg)(nil), "GeneralTimeline.RedPacketUpdateMsg")
	proto.RegisterType((*WriteTimelineMsgReq)(nil), "GeneralTimeline.WriteTimelineMsgReq")
	proto.RegisterType((*WriteTimelineMsgResp)(nil), "GeneralTimeline.WriteTimelineMsgResp")
	proto.RegisterType((*WriteUpdateMsgReq)(nil), "GeneralTimeline.WriteUpdateMsgReq")
	proto.RegisterType((*WriteUpdateMsgResp)(nil), "GeneralTimeline.WriteUpdateMsgResp")
	proto.RegisterType((*PullTimelineMsgReq)(nil), "GeneralTimeline.PullTimelineMsgReq")
	proto.RegisterType((*PullTimelineMsgResp)(nil), "GeneralTimeline.PullTimelineMsgResp")
	proto.RegisterType((*MarkReadedReq)(nil), "GeneralTimeline.MarkReadedReq")
	proto.RegisterType((*MarkReadedResp)(nil), "GeneralTimeline.MarkReadedResp")
	proto.RegisterType((*GetReadedReq)(nil), "GeneralTimeline.GetReadedReq")
	proto.RegisterType((*GetReadedResp)(nil), "GeneralTimeline.GetReadedResp")
	proto.RegisterType((*GetLastSeqidReq)(nil), "GeneralTimeline.GetLastSeqidReq")
	proto.RegisterType((*GetLastSeqidResp)(nil), "GeneralTimeline.GetLastSeqidResp")
	proto.RegisterEnum("GeneralTimeline.GeneralTimelineMsg_TYPE", GeneralTimelineMsg_TYPE_name, GeneralTimelineMsg_TYPE_value)
	proto.RegisterEnum("GeneralTimeline.GameTabUpdateMsg_TYPE", GameTabUpdateMsg_TYPE_name, GameTabUpdateMsg_TYPE_value)
	proto.RegisterEnum("GeneralTimeline.RedPacketStageInfo_StageType", RedPacketStageInfo_StageType_name, RedPacketStageInfo_StageType_value)
}
func (m *GeneralTimelineMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GeneralTimelineMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Seqid))
	if m.MsgBin != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.MsgBin)))
		i += copy(dAtA[i:], m.MsgBin)
	}
	return i, nil
}

func (m *NewGameCircleMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NewGameCircleMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.NewBegin))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.NewEnd))
	return i, nil
}

func (m *CircleUpdateMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleUpdateMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x10
	i++
	if m.IsDelete {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *MallActivity) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MallActivity) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.ActId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.SubTitle)))
	i += copy(dAtA[i:], m.SubTitle)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.BeginTime))
	return i, nil
}

func (m *MallActivityMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MallActivityMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CurrentAct != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.CurrentAct.Size()))
		n1, err := m.CurrentAct.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.AppId))
	return i, nil
}

func (m *AppFileUpdateMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AppFileUpdateMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.FileName)))
	i += copy(dAtA[i:], m.FileName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.DownloadUrl)))
	i += copy(dAtA[i:], m.DownloadUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.Md5Sum)))
	i += copy(dAtA[i:], m.Md5Sum)
	return i, nil
}

func (m *GameTabUpdateMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameTabUpdateMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.MsgType))
	return i, nil
}

func (m *RedPacketActivityInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketActivityInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.ComboTimeout))
	return i, nil
}

func (m *RedPacketStageInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketStageInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.StageId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.BeginTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.EndTime))
	if len(m.AdTextList) > 0 {
		for _, s := range m.AdTextList {
			dAtA[i] = 0x3a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x40
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.MinReqInterval))
	return i, nil
}

func (m *RedPacketUpdateMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPacketUpdateMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Act != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Act.Size()))
		n2, err := m.Act.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.StageList) > 0 {
		for _, msg := range m.StageList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGeneraltimeline(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *WriteTimelineMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteTimelineMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Msg.Size()))
		n3, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Ttl))
	return i, nil
}

func (m *WriteTimelineMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteTimelineMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *WriteUpdateMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteUpdateMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.Bucket)))
	i += copy(dAtA[i:], m.Bucket)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Msg.Size()))
		n4, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *WriteUpdateMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteUpdateMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *PullTimelineMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullTimelineMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.StartSeqid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Limit))
	if len(m.BucketList) > 0 {
		for _, s := range m.BucketList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x20
	i++
	if m.IncludeTimeline {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *PullTimelineMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullTimelineMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, msg := range m.MsgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGeneraltimeline(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *MarkReadedReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkReadedReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Seqid))
	return i, nil
}

func (m *MarkReadedResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkReadedResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetReadedReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetReadedReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetReadedResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetReadedResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Seqid))
	return i, nil
}

func (m *GetLastSeqidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLastSeqidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetLastSeqidResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLastSeqidResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGeneraltimeline(dAtA, i, uint64(m.Seqid))
	return i, nil
}

func encodeFixed64Generaltimeline(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Generaltimeline(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGeneraltimeline(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GeneralTimelineMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.Type))
	n += 1 + sovGeneraltimeline(uint64(m.Seqid))
	if m.MsgBin != nil {
		l = len(m.MsgBin)
		n += 1 + l + sovGeneraltimeline(uint64(l))
	}
	return n
}

func (m *NewGameCircleMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.CircleId))
	n += 1 + sovGeneraltimeline(uint64(m.NewBegin))
	n += 1 + sovGeneraltimeline(uint64(m.NewEnd))
	return n
}

func (m *CircleUpdateMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.CircleId))
	n += 2
	return n
}

func (m *MallActivity) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.ActId))
	l = len(m.Url)
	n += 1 + l + sovGeneraltimeline(uint64(l))
	l = len(m.Title)
	n += 1 + l + sovGeneraltimeline(uint64(l))
	l = len(m.SubTitle)
	n += 1 + l + sovGeneraltimeline(uint64(l))
	n += 1 + sovGeneraltimeline(uint64(m.BeginTime))
	return n
}

func (m *MallActivityMsg) Size() (n int) {
	var l int
	_ = l
	if m.CurrentAct != nil {
		l = m.CurrentAct.Size()
		n += 1 + l + sovGeneraltimeline(uint64(l))
	}
	n += 1 + sovGeneraltimeline(uint64(m.AppId))
	return n
}

func (m *AppFileUpdateMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.AppId))
	l = len(m.FileName)
	n += 1 + l + sovGeneraltimeline(uint64(l))
	l = len(m.DownloadUrl)
	n += 1 + l + sovGeneraltimeline(uint64(l))
	l = len(m.Md5Sum)
	n += 1 + l + sovGeneraltimeline(uint64(l))
	return n
}

func (m *GameTabUpdateMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.MsgType))
	return n
}

func (m *RedPacketActivityInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.Id))
	l = len(m.Name)
	n += 1 + l + sovGeneraltimeline(uint64(l))
	n += 1 + sovGeneraltimeline(uint64(m.BeginTime))
	n += 1 + sovGeneraltimeline(uint64(m.EndTime))
	n += 1 + sovGeneraltimeline(uint64(m.ComboTimeout))
	return n
}

func (m *RedPacketStageInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.StageId))
	n += 1 + sovGeneraltimeline(uint64(m.ActivityId))
	n += 1 + sovGeneraltimeline(uint64(m.Type))
	l = len(m.Name)
	n += 1 + l + sovGeneraltimeline(uint64(l))
	n += 1 + sovGeneraltimeline(uint64(m.BeginTime))
	n += 1 + sovGeneraltimeline(uint64(m.EndTime))
	if len(m.AdTextList) > 0 {
		for _, s := range m.AdTextList {
			l = len(s)
			n += 1 + l + sovGeneraltimeline(uint64(l))
		}
	}
	n += 1 + sovGeneraltimeline(uint64(m.MinReqInterval))
	return n
}

func (m *RedPacketUpdateMsg) Size() (n int) {
	var l int
	_ = l
	if m.Act != nil {
		l = m.Act.Size()
		n += 1 + l + sovGeneraltimeline(uint64(l))
	}
	if len(m.StageList) > 0 {
		for _, e := range m.StageList {
			l = e.Size()
			n += 1 + l + sovGeneraltimeline(uint64(l))
		}
	}
	return n
}

func (m *WriteTimelineMsgReq) Size() (n int) {
	var l int
	_ = l
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovGeneraltimeline(uint64(l))
	}
	n += 1 + sovGeneraltimeline(uint64(m.Ttl))
	return n
}

func (m *WriteTimelineMsgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *WriteUpdateMsgReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Bucket)
	n += 1 + l + sovGeneraltimeline(uint64(l))
	l = len(m.Key)
	n += 1 + l + sovGeneraltimeline(uint64(l))
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovGeneraltimeline(uint64(l))
	}
	return n
}

func (m *WriteUpdateMsgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *PullTimelineMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.StartSeqid))
	n += 1 + sovGeneraltimeline(uint64(m.Limit))
	if len(m.BucketList) > 0 {
		for _, s := range m.BucketList {
			l = len(s)
			n += 1 + l + sovGeneraltimeline(uint64(l))
		}
	}
	n += 2
	return n
}

func (m *PullTimelineMsgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, e := range m.MsgList {
			l = e.Size()
			n += 1 + l + sovGeneraltimeline(uint64(l))
		}
	}
	return n
}

func (m *MarkReadedReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.Uid))
	n += 1 + sovGeneraltimeline(uint64(m.Seqid))
	return n
}

func (m *MarkReadedResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetReadedReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.Uid))
	return n
}

func (m *GetReadedResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.Seqid))
	return n
}

func (m *GetLastSeqidReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetLastSeqidResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGeneraltimeline(uint64(m.Seqid))
	return n
}

func sovGeneraltimeline(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGeneraltimeline(x uint64) (n int) {
	return sovGeneraltimeline(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GeneralTimelineMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GeneralTimelineMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GeneralTimelineMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgBin = append(m.MsgBin[:0], dAtA[iNdEx:postIndex]...)
			if m.MsgBin == nil {
				m.MsgBin = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_bin")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NewGameCircleMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NewGameCircleMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NewGameCircleMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewBegin", wireType)
			}
			m.NewBegin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewBegin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewEnd", wireType)
			}
			m.NewEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("new_begin")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("new_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleUpdateMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleUpdateMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleUpdateMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDelete", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDelete = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_delete")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MallActivity) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MallActivity: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MallActivity: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActId", wireType)
			}
			m.ActId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("act_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MallActivityMsg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MallActivityMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MallActivityMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentAct", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CurrentAct == nil {
				m.CurrentAct = &MallActivity{}
			}
			if err := m.CurrentAct.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AppFileUpdateMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AppFileUpdateMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AppFileUpdateMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FileName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FileName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DownloadUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DownloadUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Md5Sum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Md5Sum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("file_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("download_url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("md5_sum")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameTabUpdateMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameTabUpdateMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameTabUpdateMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgType", wireType)
			}
			m.MsgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketActivityInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketActivityInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketActivityInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ComboTimeout", wireType)
			}
			m.ComboTimeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ComboTimeout |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketStageInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketStageInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketStageInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageId", wireType)
			}
			m.StageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			m.BeginTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdTextList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdTextList = append(m.AdTextList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MinReqInterval", wireType)
			}
			m.MinReqInterval = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinReqInterval |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stage_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("min_req_interval")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPacketUpdateMsg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPacketUpdateMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPacketUpdateMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Act", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Act == nil {
				m.Act = &RedPacketActivityInfo{}
			}
			if err := m.Act.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StageList = append(m.StageList, &RedPacketStageInfo{})
			if err := m.StageList[len(m.StageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteTimelineMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WriteTimelineMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WriteTimelineMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &GeneralTimelineMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteTimelineMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WriteTimelineMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WriteTimelineMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteUpdateMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WriteUpdateMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WriteUpdateMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Bucket", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Bucket = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &GeneralTimelineMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("bucket")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteUpdateMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WriteUpdateMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WriteUpdateMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullTimelineMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullTimelineMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullTimelineMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartSeqid", wireType)
			}
			m.StartSeqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartSeqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BucketList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BucketList = append(m.BucketList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncludeTimeline", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IncludeTimeline = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_seqid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullTimelineMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullTimelineMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullTimelineMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgList = append(m.MsgList, &GeneralTimelineMsg{})
			if err := m.MsgList[len(m.MsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkReadedReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MarkReadedReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MarkReadedReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkReadedResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MarkReadedResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MarkReadedResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetReadedReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetReadedReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetReadedReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetReadedResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetReadedResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetReadedResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLastSeqidReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLastSeqidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLastSeqidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLastSeqidResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLastSeqidResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLastSeqidResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGeneraltimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeneraltimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGeneraltimeline(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGeneraltimeline
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGeneraltimeline
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGeneraltimeline
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGeneraltimeline
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGeneraltimeline(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGeneraltimeline = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGeneraltimeline   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/generaltimeline/generaltimeline.proto", fileDescriptorGeneraltimeline)
}

var fileDescriptorGeneraltimeline = []byte{
	// 1454 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x56, 0xcd, 0x6e, 0xdb, 0x46,
	0x10, 0x36, 0x45, 0x49, 0x96, 0x47, 0xb6, 0xc5, 0xac, 0x9d, 0x54, 0x51, 0x1b, 0x9b, 0x61, 0x7e,
	0xea, 0xa2, 0xb0, 0x0d, 0xa4, 0x08, 0x50, 0x04, 0x86, 0x01, 0xc9, 0x96, 0x5d, 0xa1, 0x72, 0x22,
	0xd0, 0x32, 0x8c, 0x9c, 0x08, 0x8a, 0xbb, 0x36, 0xb6, 0x26, 0x29, 0x9a, 0xbb, 0xb4, 0xe3, 0x5b,
	0xd0, 0x1e, 0x5a, 0xb4, 0x97, 0x22, 0xc7, 0x9e, 0x0d, 0x14, 0x7d, 0x83, 0x3e, 0x42, 0x8e, 0x7d,
	0x82, 0xa2, 0x48, 0x2f, 0xbe, 0xa5, 0x0f, 0xd0, 0x43, 0xb1, 0x4b, 0xfd, 0x50, 0x94, 0x12, 0x37,
	0x47, 0xce, 0x7c, 0xbb, 0x33, 0xf3, 0xcd, 0x37, 0xc3, 0x85, 0x35, 0x46, 0xc2, 0x33, 0xea, 0x10,
	0xb6, 0x7e, 0x4c, 0x7c, 0x12, 0xda, 0x2e, 0xa7, 0x1e, 0x71, 0xa9, 0x4f, 0xd2, 0xdf, 0x6b, 0x41,
	0xd8, 0xe5, 0x5d, 0x54, 0xda, 0x8d, 0xcd, 0xed, 0x9e, 0xb9, 0x72, 0xdf, 0xe9, 0x7a, 0x5e, 0xd7,
	0x5f, 0xe7, 0xee, 0x59, 0x40, 0x9d, 0x13, 0x97, 0xac, 0xb3, 0x93, 0x4e, 0x44, 0x5d, 0x4e, 0x7d,
	0x7e, 0x11, 0xf4, 0x8e, 0x19, 0x6f, 0x15, 0x40, 0xa9, 0x93, 0x7b, 0xec, 0x18, 0x95, 0x21, 0x2b,
	0x40, 0x65, 0x45, 0xcf, 0xac, 0xcc, 0xd5, 0xb2, 0xaf, 0xff, 0x5c, 0x9e, 0x32, 0xa5, 0x05, 0x55,
	0x20, 0xc7, 0xc8, 0x29, 0xc5, 0xe5, 0x4c, 0xc2, 0x15, 0x9b, 0xd0, 0x1d, 0x98, 0xf6, 0xd8, 0xb1,
	0xd5, 0xa1, 0x7e, 0x59, 0xd5, 0x33, 0x2b, 0xb3, 0x3d, 0x6f, 0xde, 0x63, 0xc7, 0x35, 0xea, 0x1b,
	0xdf, 0x29, 0x90, 0x6d, 0x3f, 0x6f, 0xd5, 0xd1, 0x47, 0xb0, 0xf0, 0xb4, 0x7e, 0x68, 0xed, 0x56,
	0xf7, 0xea, 0xd6, 0x16, 0x0d, 0x1d, 0x97, 0x58, 0x7b, 0xfb, 0xbb, 0x9a, 0x82, 0x6e, 0xc2, 0x8d,
	0xad, 0x86, 0xb9, 0xd5, 0xac, 0x5b, 0x07, 0xad, 0xed, 0x6a, 0xbb, 0x2e, 0xcd, 0x19, 0xa4, 0xc1,
	0xec, 0x5e, 0xb5, 0xd9, 0xb4, 0xaa, 0x5b, 0x6d, 0x69, 0x51, 0xd1, 0x02, 0x94, 0xaa, 0xad, 0x96,
	0xb5, 0xd3, 0x18, 0x40, 0xb5, 0xac, 0x80, 0xc9, 0x2b, 0xdb, 0xd5, 0x9a, 0x84, 0xe5, 0x10, 0x82,
	0x79, 0xb3, 0xbe, 0x6d, 0xb5, 0xaa, 0x5b, 0x5f, 0xd7, 0xe3, 0xa3, 0x79, 0xe3, 0x1c, 0xb4, 0xa7,
	0xe4, 0x7c, 0xd7, 0xf6, 0x48, 0x1c, 0x5a, 0x94, 0x7b, 0x17, 0x66, 0x9c, 0x38, 0x0f, 0x8a, 0x47,
	0x6a, 0x2e, 0xc4, 0xe6, 0x06, 0x16, 0x10, 0x9f, 0x9c, 0x5b, 0x1d, 0x72, 0x4c, 0xfd, 0x91, 0xda,
	0x0b, 0x3e, 0x39, 0xaf, 0x09, 0xab, 0x28, 0x5f, 0x40, 0x88, 0x8f, 0x65, 0xf9, 0x7d, 0x40, 0xde,
	0x27, 0xe7, 0x75, 0x1f, 0x1b, 0x87, 0x50, 0x8a, 0x23, 0x1e, 0x04, 0xd8, 0xe6, 0x1f, 0x10, 0x97,
	0x32, 0x0b, 0x13, 0x97, 0x70, 0x22, 0xe3, 0x16, 0xfa, 0x10, 0xca, 0xb6, 0xa5, 0xd5, 0xf8, 0x55,
	0x81, 0xd9, 0x3d, 0xdb, 0x75, 0xab, 0x0e, 0xa7, 0x67, 0x94, 0x5f, 0xa0, 0x8f, 0x21, 0x6f, 0x3b,
	0x3c, 0x7d, 0x67, 0xce, 0x76, 0x78, 0x03, 0xa3, 0x5b, 0xa0, 0x46, 0xa1, 0x2b, 0xaf, 0x9a, 0xe9,
	0x79, 0x84, 0x41, 0x34, 0x96, 0x53, 0xee, 0x12, 0x99, 0x7b, 0xdf, 0x13, 0x9b, 0x44, 0x12, 0x2c,
	0xea, 0x58, 0xb1, 0x3f, 0xab, 0x2b, 0x03, 0x7f, 0x81, 0x45, 0x9d, 0xb6, 0x84, 0xdc, 0x03, 0x90,
	0xdc, 0x58, 0x42, 0x97, 0xe5, 0x9c, 0x9e, 0x59, 0xc9, 0xf6, 0x30, 0x33, 0xd2, 0x2e, 0xd4, 0x65,
	0xf8, 0x50, 0x4a, 0x26, 0x2a, 0x28, 0xd8, 0x84, 0xa2, 0x13, 0x85, 0x21, 0xf1, 0xb9, 0x65, 0x3b,
	0xbc, 0xac, 0xe8, 0xca, 0x4a, 0xf1, 0xd1, 0x9d, 0xb5, 0x94, 0x26, 0xd7, 0x92, 0xc7, 0x4c, 0xe8,
	0x9d, 0xa8, 0x3a, 0x5c, 0xd6, 0x1a, 0x04, 0x96, 0x14, 0xa4, 0x92, 0xa8, 0x35, 0x08, 0x1a, 0xd8,
	0xf8, 0x45, 0x01, 0xad, 0x1a, 0x04, 0x3b, 0x34, 0x49, 0xfa, 0xf0, 0xc4, 0x28, 0x3b, 0xe2, 0x84,
	0xa8, 0xf4, 0x88, 0xba, 0xc4, 0xf2, 0x6d, 0x8f, 0x8c, 0x70, 0x54, 0x10, 0xe6, 0xa7, 0xb6, 0x47,
	0xd0, 0xa7, 0x30, 0x8b, 0xbb, 0xe7, 0xbe, 0xdb, 0xb5, 0xb1, 0x25, 0x98, 0x4c, 0xf2, 0x55, 0xec,
	0x7b, 0x0e, 0x42, 0x57, 0x8e, 0x03, 0x7e, 0x6c, 0xb1, 0xc8, 0x2b, 0x67, 0x13, 0x98, 0xbc, 0x87,
	0x1f, 0xef, 0x47, 0x9e, 0x68, 0x9b, 0x26, 0x64, 0xd8, 0xb6, 0x3b, 0xc3, 0xe4, 0x96, 0xa1, 0x20,
	0x46, 0x68, 0x6c, 0xf8, 0xc4, 0x60, 0xb5, 0x2f, 0x02, 0x62, 0x84, 0xbd, 0x19, 0x2a, 0xc3, 0xa2,
	0x10, 0xbb, 0xd0, 0xba, 0xf8, 0xb6, 0x0e, 0xeb, 0xcd, 0x9d, 0xaa, 0x59, 0xd7, 0x14, 0x54, 0x81,
	0x5b, 0x23, 0x9e, 0x5a, 0x7d, 0xbf, 0x2d, 0x67, 0x4d, 0xcb, 0xa0, 0xdb, 0x70, 0x73, 0xc4, 0xd7,
	0x1f, 0x43, 0x4d, 0x1d, 0x73, 0x7d, 0xf5, 0xac, 0x77, 0x2a, 0x6b, 0xfc, 0xae, 0xc0, 0x4d, 0x93,
	0xe0, 0x96, 0xed, 0x9c, 0x10, 0xde, 0xef, 0x42, 0xc3, 0x3f, 0xea, 0xa2, 0x45, 0xc8, 0xa4, 0x78,
	0xcc, 0x50, 0x2c, 0xb6, 0xc7, 0x18, 0x7f, 0xd2, 0x92, 0x52, 0x89, 0x3a, 0x51, 0x25, 0x82, 0x03,
	0xe2, 0xe3, 0x18, 0x92, 0x4d, 0x40, 0xa6, 0x89, 0x8f, 0x25, 0xe0, 0x33, 0x98, 0x73, 0xba, 0x5e,
	0xa7, 0x2b, 0x21, 0xdd, 0x88, 0x97, 0x73, 0x89, 0xd6, 0xcf, 0x4a, 0x57, 0x3b, 0xf6, 0x18, 0xff,
	0x64, 0x00, 0x0d, 0x52, 0xdf, 0xe7, 0xf6, 0x31, 0x91, 0x79, 0x2f, 0x43, 0x81, 0x89, 0x8f, 0xb4,
	0x0a, 0xa6, 0xa5, 0xb5, 0x81, 0xd1, 0x03, 0x28, 0xda, 0xbd, 0x42, 0xad, 0xd4, 0xb2, 0x83, 0xbe,
	0xa3, 0x81, 0x07, 0x7b, 0x52, 0x1d, 0xdb, 0x93, 0x7d, 0x0e, 0x92, 0xd3, 0x32, 0x89, 0x03, 0x91,
	0xfa, 0x35, 0x1c, 0xe4, 0x13, 0x90, 0x01, 0x07, 0x3a, 0xcc, 0xda, 0xd8, 0xe2, 0xe4, 0x05, 0xb7,
	0x5c, 0xca, 0x78, 0x79, 0x5a, 0x57, 0x57, 0x66, 0x4c, 0xb0, 0x71, 0x9b, 0xbc, 0xe0, 0x4d, 0xca,
	0x38, 0x5a, 0x03, 0xcd, 0xa3, 0xbe, 0x15, 0x92, 0x53, 0x8b, 0xfa, 0x9c, 0x84, 0x67, 0xb6, 0x5b,
	0x2e, 0x24, 0xf2, 0x9c, 0xf7, 0xa8, 0x6f, 0x92, 0xd3, 0x46, 0xcf, 0x67, 0xec, 0xc0, 0x8c, 0x24,
	0x48, 0xc8, 0x0c, 0x15, 0x61, 0xba, 0xf9, 0xac, 0xdd, 0xae, 0x9b, 0xcf, 0x35, 0x45, 0xac, 0xd1,
	0xbd, 0xea, 0xfe, 0xbe, 0xb5, 0x7b, 0xd0, 0x68, 0x6e, 0xd7, 0x0e, 0x76, 0x76, 0xb4, 0x8c, 0x00,
	0xb4, 0xcc, 0x7a, 0x4b, 0x48, 0x4e, 0x15, 0x1f, 0xdb, 0xf5, 0x9d, 0xea, 0x41, 0xb3, 0xad, 0x65,
	0x8d, 0x57, 0x4a, 0x82, 0xf2, 0xa1, 0xb2, 0xbf, 0x04, 0x75, 0x38, 0xe0, 0x0f, 0xc7, 0x06, 0x7c,
	0xa2, 0xbe, 0x4c, 0x71, 0x04, 0xd5, 0x00, 0xe2, 0x66, 0xc9, 0x42, 0x33, 0xba, 0xba, 0x52, 0x7c,
	0x74, 0xef, 0xdd, 0x17, 0x0c, 0xba, 0x6c, 0xce, 0xc8, 0x63, 0x82, 0x0c, 0x03, 0xc3, 0xc2, 0x61,
	0x48, 0x39, 0x49, 0xfc, 0xe4, 0x4c, 0x72, 0x8a, 0x1e, 0x83, 0xea, 0xb1, 0x63, 0x29, 0x81, 0x49,
	0x77, 0x8e, 0xff, 0x19, 0x4d, 0x81, 0x17, 0x3b, 0x94, 0x73, 0x77, 0x64, 0xe3, 0x08, 0x83, 0x71,
	0x0b, 0x16, 0xc7, 0xa3, 0xb0, 0xc0, 0x78, 0xa9, 0xc0, 0x0d, 0xe9, 0x18, 0xd0, 0x21, 0x82, 0x7f,
	0x02, 0xf9, 0x4e, 0x24, 0x32, 0x96, 0xf1, 0x07, 0xeb, 0x21, 0xb6, 0x89, 0x18, 0x27, 0xe4, 0x62,
	0x74, 0x4f, 0x9f, 0x90, 0x8b, 0x7e, 0xca, 0xea, 0x87, 0xa5, 0x6c, 0x2c, 0x02, 0x4a, 0x67, 0xc0,
	0x02, 0xe3, 0x37, 0x05, 0x50, 0x2b, 0x72, 0xdd, 0x14, 0x2d, 0x0f, 0xa0, 0xc8, 0xb8, 0x1d, 0x72,
	0x2b, 0xfe, 0xd5, 0x27, 0x27, 0x04, 0xa4, 0x63, 0x5f, 0xfe, 0xef, 0x2b, 0x90, 0x73, 0xa9, 0x47,
	0xf9, 0xe8, 0x5b, 0x40, 0x9a, 0xd0, 0x32, 0x14, 0xe3, 0x42, 0xe2, 0xae, 0xa9, 0xb1, 0x3c, 0x63,
	0x93, 0x94, 0xe7, 0x3a, 0x68, 0xd4, 0x77, 0xdc, 0x08, 0x13, 0xab, 0xff, 0x94, 0x91, 0xc3, 0xd2,
	0xff, 0xbf, 0x95, 0x7a, 0xde, 0x7e, 0x6a, 0xc6, 0x01, 0x2c, 0x8c, 0xa5, 0xca, 0x02, 0xb4, 0x19,
	0x6f, 0x4c, 0x19, 0x45, 0x79, 0x87, 0x36, 0x26, 0x90, 0x22, 0x16, 0xaa, 0x54, 0xc6, 0x16, 0xcc,
	0xed, 0xd9, 0xe1, 0x89, 0x49, 0x6c, 0x4c, 0xb0, 0x28, 0x5e, 0xfc, 0x20, 0x53, 0x45, 0x0b, 0xc3,
	0xf0, 0xe5, 0xa3, 0x8e, 0xbd, 0x7c, 0x0c, 0x0d, 0xe6, 0x93, 0x97, 0xb0, 0xc0, 0x78, 0x08, 0xb3,
	0xbb, 0x84, 0x5f, 0x7b, 0xab, 0xf1, 0x39, 0xcc, 0x25, 0x70, 0x2c, 0x18, 0x86, 0x51, 0xc6, 0xc3,
	0xdc, 0x80, 0xd2, 0x2e, 0xe1, 0x4d, 0x9b, 0xc5, 0x0d, 0x30, 0xc9, 0xa9, 0xb1, 0x06, 0xda, 0xa8,
	0xe9, 0xfd, 0x57, 0x3c, 0x7a, 0x9b, 0x83, 0xf4, 0x53, 0x11, 0x7d, 0xaf, 0x80, 0x96, 0xd6, 0x2d,
	0xba, 0x3f, 0xc6, 0xe2, 0x84, 0x01, 0xaa, 0x3c, 0xf8, 0x1f, 0x28, 0xc1, 0xc6, 0xcb, 0xcb, 0x2b,
	0x55, 0xf9, 0xf1, 0xf2, 0x4a, 0xcd, 0xd2, 0x27, 0xec, 0xc9, 0xab, 0xcb, 0x2b, 0x75, 0x61, 0x95,
	0xea, 0x1b, 0x14, 0x6f, 0xea, 0xab, 0x4c, 0xdf, 0x60, 0xd1, 0xd1, 0x11, 0x7d, 0xb1, 0x89, 0xb0,
	0x64, 0x6d, 0x50, 0x0d, 0xd2, 0x27, 0xb4, 0x72, 0xa4, 0xfe, 0xca, 0xdd, 0x6b, 0x10, 0x2c, 0x30,
	0x4a, 0x22, 0x78, 0x46, 0x04, 0x9f, 0x12, 0x81, 0xa7, 0xd0, 0x4f, 0x0a, 0x94, 0x52, 0x52, 0x42,
	0xe3, 0xa2, 0x19, 0x9f, 0x8b, 0xca, 0xfd, 0xeb, 0x41, 0x2c, 0x30, 0x56, 0x45, 0x3c, 0x55, 0x16,
	0xdb, 0x79, 0xe2, 0xca, 0x62, 0x2b, 0xfa, 0x6a, 0x47, 0xdf, 0x88, 0xd7, 0x3e, 0x23, 0xa7, 0x9b,
	0xfa, 0xaa, 0xab, 0x6f, 0xc8, 0x41, 0xd9, 0x44, 0xdf, 0x2a, 0x00, 0x43, 0xf1, 0xa0, 0xa5, 0x09,
	0x6f, 0x9f, 0x84, 0x3c, 0x2b, 0xcb, 0xef, 0xf5, 0xb3, 0xc0, 0xf8, 0x42, 0x84, 0xcf, 0x8a, 0xf0,
	0x79, 0xc1, 0x35, 0x96, 0x09, 0x2c, 0x4d, 0x60, 0x5b, 0x5f, 0xc5, 0xfa, 0x86, 0x54, 0xc5, 0x26,
	0x22, 0x30, 0x33, 0x90, 0x21, 0xba, 0x33, 0x89, 0xd3, 0x61, 0x06, 0x4b, 0xef, 0x73, 0xb3, 0xc0,
	0xb8, 0x2d, 0x12, 0xc8, 0x89, 0x04, 0x32, 0x91, 0x0c, 0x5e, 0x58, 0x8d, 0xf4, 0x8d, 0x48, 0x84,
	0xf9, 0x06, 0xe6, 0x47, 0xb7, 0x10, 0x32, 0x26, 0x0b, 0x28, 0xb9, 0x28, 0x2b, 0xf7, 0xae, 0xc5,
	0xf4, 0xbb, 0x9c, 0x1f, 0x76, 0xb9, 0x92, 0xff, 0xe1, 0xf2, 0x4a, 0xfd, 0x37, 0xaa, 0x69, 0xaf,
	0xdf, 0x2c, 0x29, 0x7f, 0xbc, 0x59, 0x52, 0xfe, 0x7a, 0xb3, 0xa4, 0xfc, 0xfc, 0xf7, 0xd2, 0xd4,
	0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xdc, 0x56, 0x1c, 0x52, 0x5c, 0x0d, 0x00, 0x00,
}
