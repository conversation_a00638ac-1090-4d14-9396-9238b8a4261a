// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/userpresent-go/userpresent-go.proto

package userpresent_go // import "golang.52tt.com/protocol/services/userpresent-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 购买礼物的货币
type PresentPriceType int32

const (
	PresentPriceType_PRESENT_PRICE_UNKNOWN     PresentPriceType = 0
	PresentPriceType_PRESENT_PRICE_RED_DIAMOND PresentPriceType = 1
	PresentPriceType_PRESENT_PRICE_TBEAN       PresentPriceType = 2
)

var PresentPriceType_name = map[int32]string{
	0: "PRESENT_PRICE_UNKNOWN",
	1: "PRESENT_PRICE_RED_DIAMOND",
	2: "PRESENT_PRICE_TBEAN",
}
var PresentPriceType_value = map[string]int32{
	"PRESENT_PRICE_UNKNOWN":     0,
	"PRESENT_PRICE_RED_DIAMOND": 1,
	"PRESENT_PRICE_TBEAN":       2,
}

func (x PresentPriceType) String() string {
	return proto.EnumName(PresentPriceType_name, int32(x))
}
func (PresentPriceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{0}
}

// 礼物配置列表类型
type ConfigListTypeBitMap int32

const (
	ConfigListTypeBitMap_CONFIG_UNLIMIT     ConfigListTypeBitMap = 0
	ConfigListTypeBitMap_CONFIG_NOT_EXPIRED ConfigListTypeBitMap = 1
	ConfigListTypeBitMap_CONFIG_NOT_DELETED ConfigListTypeBitMap = 2
)

var ConfigListTypeBitMap_name = map[int32]string{
	0: "CONFIG_UNLIMIT",
	1: "CONFIG_NOT_EXPIRED",
	2: "CONFIG_NOT_DELETED",
}
var ConfigListTypeBitMap_value = map[string]int32{
	"CONFIG_UNLIMIT":     0,
	"CONFIG_NOT_EXPIRED": 1,
	"CONFIG_NOT_DELETED": 2,
}

func (x ConfigListTypeBitMap) String() string {
	return proto.EnumName(ConfigListTypeBitMap_name, int32(x))
}
func (ConfigListTypeBitMap) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{1}
}

type PresentEnterType int32

const (
	PresentEnterType_PresentEnterTypeUnknown  PresentEnterType = 0
	PresentEnterType_PresentEnterTypeShelf    PresentEnterType = 1
	PresentEnterType_PresentEnterTypeLottery  PresentEnterType = 2
	PresentEnterType_PresentEnterTypeWishList PresentEnterType = 3
)

var PresentEnterType_name = map[int32]string{
	0: "PresentEnterTypeUnknown",
	1: "PresentEnterTypeShelf",
	2: "PresentEnterTypeLottery",
	3: "PresentEnterTypeWishList",
}
var PresentEnterType_value = map[string]int32{
	"PresentEnterTypeUnknown":  0,
	"PresentEnterTypeShelf":    1,
	"PresentEnterTypeLottery":  2,
	"PresentEnterTypeWishList": 3,
}

func (x PresentEnterType) String() string {
	return proto.EnumName(PresentEnterType_name, int32(x))
}
func (PresentEnterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{2}
}

// 礼物绑定类型
type PresentSceneType int32

const (
	PresentSceneType_UNKNOWN_PRESENT PresentSceneType = 0
	PresentSceneType_CHANNEL_PRESENT PresentSceneType = 1
	PresentSceneType_GUILD_PRESENT   PresentSceneType = 2
	PresentSceneType_USER_PRESENT    PresentSceneType = 3
)

var PresentSceneType_name = map[int32]string{
	0: "UNKNOWN_PRESENT",
	1: "CHANNEL_PRESENT",
	2: "GUILD_PRESENT",
	3: "USER_PRESENT",
}
var PresentSceneType_value = map[string]int32{
	"UNKNOWN_PRESENT": 0,
	"CHANNEL_PRESENT": 1,
	"GUILD_PRESENT":   2,
	"USER_PRESENT":    3,
}

func (x PresentSceneType) String() string {
	return proto.EnumName(PresentSceneType_name, int32(x))
}
func (PresentSceneType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{3}
}

type StConfigIosExtend struct {
	VideoEffectUrl       []byte   `protobuf:"bytes,1,opt,name=video_effect_url,json=videoEffectUrl,proto3" json:"video_effect_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StConfigIosExtend) Reset()         { *m = StConfigIosExtend{} }
func (m *StConfigIosExtend) String() string { return proto.CompactTextString(m) }
func (*StConfigIosExtend) ProtoMessage()    {}
func (*StConfigIosExtend) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{0}
}
func (m *StConfigIosExtend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StConfigIosExtend.Unmarshal(m, b)
}
func (m *StConfigIosExtend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StConfigIosExtend.Marshal(b, m, deterministic)
}
func (dst *StConfigIosExtend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StConfigIosExtend.Merge(dst, src)
}
func (m *StConfigIosExtend) XXX_Size() int {
	return xxx_messageInfo_StConfigIosExtend.Size(m)
}
func (m *StConfigIosExtend) XXX_DiscardUnknown() {
	xxx_messageInfo_StConfigIosExtend.DiscardUnknown(m)
}

var xxx_messageInfo_StConfigIosExtend proto.InternalMessageInfo

func (m *StConfigIosExtend) GetVideoEffectUrl() []byte {
	if m != nil {
		return m.VideoEffectUrl
	}
	return nil
}

type StPresentItemConfigExtend struct {
	ItemId               uint32             `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	VideoEffectUrl       []byte             `protobuf:"bytes,2,opt,name=video_effect_url,json=videoEffectUrl,proto3" json:"video_effect_url,omitempty"`
	ShowEffect           uint32             `protobuf:"varint,3,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	UnshowBatchOption    bool               `protobuf:"varint,4,opt,name=unshow_batch_option,json=unshowBatchOption,proto3" json:"unshow_batch_option,omitempty"`
	IsTest               bool               `protobuf:"varint,5,opt,name=is_test,json=isTest,proto3" json:"is_test,omitempty"`
	FlowId               uint32             `protobuf:"varint,6,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	IosExtend            *StConfigIosExtend `protobuf:"bytes,7,opt,name=ios_extend,json=iosExtend,proto3" json:"ios_extend,omitempty"`
	NotifyAll            bool               `protobuf:"varint,8,opt,name=notify_all,json=notifyAll,proto3" json:"notify_all,omitempty"`
	Tag                  uint32             `protobuf:"varint,9,opt,name=tag,proto3" json:"tag,omitempty"`
	ForceSendable        bool               `protobuf:"varint,10,opt,name=force_sendable,json=forceSendable,proto3" json:"force_sendable,omitempty"`
	NobilityLevel        uint32             `protobuf:"varint,11,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	UnshowPresentShelf   bool               `protobuf:"varint,12,opt,name=unshow_present_shelf,json=unshowPresentShelf,proto3" json:"unshow_present_shelf,omitempty"`
	ShowEffectEnd        bool               `protobuf:"varint,13,opt,name=show_effect_end,json=showEffectEnd,proto3" json:"show_effect_end,omitempty"`
	EffectEndDelay       bool               `protobuf:"varint,14,opt,name=effect_end_delay,json=effectEndDelay,proto3" json:"effect_end_delay,omitempty"`
	CustomText           []*CustomText      `protobuf:"bytes,15,rep,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	SmallVapUrl          string             `protobuf:"bytes,16,opt,name=small_vap_url,json=smallVapUrl,proto3" json:"small_vap_url,omitempty"`
	SmallVapMd5          string             `protobuf:"bytes,17,opt,name=small_vap_md5,json=smallVapMd5,proto3" json:"small_vap_md5,omitempty"`
	MicEffectUrl         string             `protobuf:"bytes,18,opt,name=mic_effect_url,json=micEffectUrl,proto3" json:"mic_effect_url,omitempty"`
	MicEffectMd5         string             `protobuf:"bytes,19,opt,name=mic_effect_md5,json=micEffectMd5,proto3" json:"mic_effect_md5,omitempty"`
	FusionPresent        bool               `protobuf:"varint,20,opt,name=fusion_present,json=fusionPresent,proto3" json:"fusion_present,omitempty"`
	IsBoxBreaking        bool               `protobuf:"varint,21,opt,name=is_box_breaking,json=isBoxBreaking,proto3" json:"is_box_breaking,omitempty"`
	FansLevel            uint32             `protobuf:"varint,22,opt,name=fans_level,json=fansLevel,proto3" json:"fans_level,omitempty"`
	OriginIconUrl        string             `protobuf:"bytes,23,opt,name=origin_icon_url,json=originIconUrl,proto3" json:"origin_icon_url,omitempty"`
	MarkId               uint64             `protobuf:"varint,24,opt,name=mark_id,json=markId,proto3" json:"mark_id,omitempty"`
	MarkName             string             `protobuf:"bytes,25,opt,name=mark_name,json=markName,proto3" json:"mark_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *StPresentItemConfigExtend) Reset()         { *m = StPresentItemConfigExtend{} }
func (m *StPresentItemConfigExtend) String() string { return proto.CompactTextString(m) }
func (*StPresentItemConfigExtend) ProtoMessage()    {}
func (*StPresentItemConfigExtend) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{1}
}
func (m *StPresentItemConfigExtend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPresentItemConfigExtend.Unmarshal(m, b)
}
func (m *StPresentItemConfigExtend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPresentItemConfigExtend.Marshal(b, m, deterministic)
}
func (dst *StPresentItemConfigExtend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPresentItemConfigExtend.Merge(dst, src)
}
func (m *StPresentItemConfigExtend) XXX_Size() int {
	return xxx_messageInfo_StPresentItemConfigExtend.Size(m)
}
func (m *StPresentItemConfigExtend) XXX_DiscardUnknown() {
	xxx_messageInfo_StPresentItemConfigExtend.DiscardUnknown(m)
}

var xxx_messageInfo_StPresentItemConfigExtend proto.InternalMessageInfo

func (m *StPresentItemConfigExtend) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetVideoEffectUrl() []byte {
	if m != nil {
		return m.VideoEffectUrl
	}
	return nil
}

func (m *StPresentItemConfigExtend) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetUnshowBatchOption() bool {
	if m != nil {
		return m.UnshowBatchOption
	}
	return false
}

func (m *StPresentItemConfigExtend) GetIsTest() bool {
	if m != nil {
		return m.IsTest
	}
	return false
}

func (m *StPresentItemConfigExtend) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetIosExtend() *StConfigIosExtend {
	if m != nil {
		return m.IosExtend
	}
	return nil
}

func (m *StPresentItemConfigExtend) GetNotifyAll() bool {
	if m != nil {
		return m.NotifyAll
	}
	return false
}

func (m *StPresentItemConfigExtend) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetForceSendable() bool {
	if m != nil {
		return m.ForceSendable
	}
	return false
}

func (m *StPresentItemConfigExtend) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetUnshowPresentShelf() bool {
	if m != nil {
		return m.UnshowPresentShelf
	}
	return false
}

func (m *StPresentItemConfigExtend) GetShowEffectEnd() bool {
	if m != nil {
		return m.ShowEffectEnd
	}
	return false
}

func (m *StPresentItemConfigExtend) GetEffectEndDelay() bool {
	if m != nil {
		return m.EffectEndDelay
	}
	return false
}

func (m *StPresentItemConfigExtend) GetCustomText() []*CustomText {
	if m != nil {
		return m.CustomText
	}
	return nil
}

func (m *StPresentItemConfigExtend) GetSmallVapUrl() string {
	if m != nil {
		return m.SmallVapUrl
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetSmallVapMd5() string {
	if m != nil {
		return m.SmallVapMd5
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetMicEffectUrl() string {
	if m != nil {
		return m.MicEffectUrl
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetMicEffectMd5() string {
	if m != nil {
		return m.MicEffectMd5
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetFusionPresent() bool {
	if m != nil {
		return m.FusionPresent
	}
	return false
}

func (m *StPresentItemConfigExtend) GetIsBoxBreaking() bool {
	if m != nil {
		return m.IsBoxBreaking
	}
	return false
}

func (m *StPresentItemConfigExtend) GetFansLevel() uint32 {
	if m != nil {
		return m.FansLevel
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetOriginIconUrl() string {
	if m != nil {
		return m.OriginIconUrl
	}
	return ""
}

func (m *StPresentItemConfigExtend) GetMarkId() uint64 {
	if m != nil {
		return m.MarkId
	}
	return 0
}

func (m *StPresentItemConfigExtend) GetMarkName() string {
	if m != nil {
		return m.MarkName
	}
	return ""
}

type CustomText struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Text                 []string `protobuf:"bytes,2,rep,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomText) Reset()         { *m = CustomText{} }
func (m *CustomText) String() string { return proto.CompactTextString(m) }
func (*CustomText) ProtoMessage()    {}
func (*CustomText) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{2}
}
func (m *CustomText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomText.Unmarshal(m, b)
}
func (m *CustomText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomText.Marshal(b, m, deterministic)
}
func (dst *CustomText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomText.Merge(dst, src)
}
func (m *CustomText) XXX_Size() int {
	return xxx_messageInfo_CustomText.Size(m)
}
func (m *CustomText) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomText.DiscardUnknown(m)
}

var xxx_messageInfo_CustomText proto.InternalMessageInfo

func (m *CustomText) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CustomText) GetText() []string {
	if m != nil {
		return m.Text
	}
	return nil
}

// 礼物配置信息
type StPresentItemConfig struct {
	ItemId               uint32                     `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Name                 string                     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string                     `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32                     `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Score                uint32                     `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	Charm                uint32                     `protobuf:"varint,6,opt,name=charm,proto3" json:"charm,omitempty"`
	Rank                 uint32                     `protobuf:"varint,7,opt,name=rank,proto3" json:"rank,omitempty"`
	EffectBegin          uint32                     `protobuf:"varint,8,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                     `protobuf:"varint,9,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	UpdateTime           uint32                     `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           uint32                     `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	IsDel                bool                       `protobuf:"varint,12,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	PriceType            uint32                     `protobuf:"varint,13,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	RichValue            uint32                     `protobuf:"varint,14,opt,name=rich_value,json=richValue,proto3" json:"rich_value,omitempty"`
	Extend               *StPresentItemConfigExtend `protobuf:"bytes,15,opt,name=extend,proto3" json:"extend,omitempty"`
	RankFloat            float32                    `protobuf:"fixed32,16,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *StPresentItemConfig) Reset()         { *m = StPresentItemConfig{} }
func (m *StPresentItemConfig) String() string { return proto.CompactTextString(m) }
func (*StPresentItemConfig) ProtoMessage()    {}
func (*StPresentItemConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{3}
}
func (m *StPresentItemConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPresentItemConfig.Unmarshal(m, b)
}
func (m *StPresentItemConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPresentItemConfig.Marshal(b, m, deterministic)
}
func (dst *StPresentItemConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPresentItemConfig.Merge(dst, src)
}
func (m *StPresentItemConfig) XXX_Size() int {
	return xxx_messageInfo_StPresentItemConfig.Size(m)
}
func (m *StPresentItemConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_StPresentItemConfig.DiscardUnknown(m)
}

var xxx_messageInfo_StPresentItemConfig proto.InternalMessageInfo

func (m *StPresentItemConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StPresentItemConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StPresentItemConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *StPresentItemConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *StPresentItemConfig) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *StPresentItemConfig) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *StPresentItemConfig) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *StPresentItemConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *StPresentItemConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *StPresentItemConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *StPresentItemConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StPresentItemConfig) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *StPresentItemConfig) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *StPresentItemConfig) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *StPresentItemConfig) GetExtend() *StPresentItemConfigExtend {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *StPresentItemConfig) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

// 用户的礼物汇总
type StUserPresentSummary struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StUserPresentSummary) Reset()         { *m = StUserPresentSummary{} }
func (m *StUserPresentSummary) String() string { return proto.CompactTextString(m) }
func (*StUserPresentSummary) ProtoMessage()    {}
func (*StUserPresentSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{4}
}
func (m *StUserPresentSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StUserPresentSummary.Unmarshal(m, b)
}
func (m *StUserPresentSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StUserPresentSummary.Marshal(b, m, deterministic)
}
func (dst *StUserPresentSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StUserPresentSummary.Merge(dst, src)
}
func (m *StUserPresentSummary) XXX_Size() int {
	return xxx_messageInfo_StUserPresentSummary.Size(m)
}
func (m *StUserPresentSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_StUserPresentSummary.DiscardUnknown(m)
}

var xxx_messageInfo_StUserPresentSummary proto.InternalMessageInfo

func (m *StUserPresentSummary) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StUserPresentSummary) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StUserPresentSummary) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 用户的礼物明细
type StUserPresentDetail struct {
	FromUid              uint32               `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	TargetUid            uint32               `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,3,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	ReceiveTime          uint32               `protobuf:"varint,4,opt,name=receive_time,json=receiveTime,proto3" json:"receive_time,omitempty"`
	ItemCount            uint32               `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	AddCharm             uint32               `protobuf:"varint,6,opt,name=add_charm,json=addCharm,proto3" json:"add_charm,omitempty"`
	OrderId              string               `protobuf:"bytes,7,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ItemId               uint32               `protobuf:"varint,8,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Score                uint32               `protobuf:"varint,9,opt,name=score,proto3" json:"score,omitempty"`
	SendSource           uint32               `protobuf:"varint,10,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	SendMethod           uint32               `protobuf:"varint,11,opt,name=send_method,json=sendMethod,proto3" json:"send_method,omitempty"`
	AddRich              uint32               `protobuf:"varint,12,opt,name=add_rich,json=addRich,proto3" json:"add_rich,omitempty"`
	IsUkw                bool                 `protobuf:"varint,13,opt,name=is_ukw,json=isUkw,proto3" json:"is_ukw,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *StUserPresentDetail) Reset()         { *m = StUserPresentDetail{} }
func (m *StUserPresentDetail) String() string { return proto.CompactTextString(m) }
func (*StUserPresentDetail) ProtoMessage()    {}
func (*StUserPresentDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{5}
}
func (m *StUserPresentDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StUserPresentDetail.Unmarshal(m, b)
}
func (m *StUserPresentDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StUserPresentDetail.Marshal(b, m, deterministic)
}
func (dst *StUserPresentDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StUserPresentDetail.Merge(dst, src)
}
func (m *StUserPresentDetail) XXX_Size() int {
	return xxx_messageInfo_StUserPresentDetail.Size(m)
}
func (m *StUserPresentDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_StUserPresentDetail.DiscardUnknown(m)
}

var xxx_messageInfo_StUserPresentDetail proto.InternalMessageInfo

func (m *StUserPresentDetail) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *StUserPresentDetail) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *StUserPresentDetail) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

func (m *StUserPresentDetail) GetReceiveTime() uint32 {
	if m != nil {
		return m.ReceiveTime
	}
	return 0
}

func (m *StUserPresentDetail) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *StUserPresentDetail) GetAddCharm() uint32 {
	if m != nil {
		return m.AddCharm
	}
	return 0
}

func (m *StUserPresentDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *StUserPresentDetail) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StUserPresentDetail) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *StUserPresentDetail) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *StUserPresentDetail) GetSendMethod() uint32 {
	if m != nil {
		return m.SendMethod
	}
	return 0
}

func (m *StUserPresentDetail) GetAddRich() uint32 {
	if m != nil {
		return m.AddRich
	}
	return 0
}

func (m *StUserPresentDetail) GetIsUkw() bool {
	if m != nil {
		return m.IsUkw
	}
	return false
}

type GetPresentConfigListReq struct {
	TypeBitmap           uint32   `protobuf:"varint,1,opt,name=type_bitmap,json=typeBitmap,proto3" json:"type_bitmap,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigListReq) Reset()         { *m = GetPresentConfigListReq{} }
func (m *GetPresentConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListReq) ProtoMessage()    {}
func (*GetPresentConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{6}
}
func (m *GetPresentConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListReq.Unmarshal(m, b)
}
func (m *GetPresentConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListReq.Merge(dst, src)
}
func (m *GetPresentConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListReq.Size(m)
}
func (m *GetPresentConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListReq proto.InternalMessageInfo

func (m *GetPresentConfigListReq) GetTypeBitmap() uint32 {
	if m != nil {
		return m.TypeBitmap
	}
	return 0
}

func (m *GetPresentConfigListReq) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetPresentConfigListResp struct {
	ItemList             []*StPresentItemConfig `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	UpdateTime           uint32                 `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPresentConfigListResp) Reset()         { *m = GetPresentConfigListResp{} }
func (m *GetPresentConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListResp) ProtoMessage()    {}
func (*GetPresentConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{7}
}
func (m *GetPresentConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListResp.Unmarshal(m, b)
}
func (m *GetPresentConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListResp.Merge(dst, src)
}
func (m *GetPresentConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListResp.Size(m)
}
func (m *GetPresentConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListResp proto.InternalMessageInfo

func (m *GetPresentConfigListResp) GetItemList() []*StPresentItemConfig {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GetPresentConfigListResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetPresentConfigListV3Req struct {
	TypeBitmap           uint32   `protobuf:"varint,1,opt,name=type_bitmap,json=typeBitmap,proto3" json:"type_bitmap,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigListV3Req) Reset()         { *m = GetPresentConfigListV3Req{} }
func (m *GetPresentConfigListV3Req) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListV3Req) ProtoMessage()    {}
func (*GetPresentConfigListV3Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{8}
}
func (m *GetPresentConfigListV3Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListV3Req.Unmarshal(m, b)
}
func (m *GetPresentConfigListV3Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListV3Req.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListV3Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListV3Req.Merge(dst, src)
}
func (m *GetPresentConfigListV3Req) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListV3Req.Size(m)
}
func (m *GetPresentConfigListV3Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListV3Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListV3Req proto.InternalMessageInfo

func (m *GetPresentConfigListV3Req) GetTypeBitmap() uint32 {
	if m != nil {
		return m.TypeBitmap
	}
	return 0
}

func (m *GetPresentConfigListV3Req) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetPresentConfigListV3Resp struct {
	ItemList             []*PresentConfigNew      `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	LastUpdateTime       uint32                   `protobuf:"varint,2,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	EnterBlackList       []*PresentEnterBlacklist `protobuf:"bytes,3,rep,name=enter_black_list,json=enterBlackList,proto3" json:"enter_black_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetPresentConfigListV3Resp) Reset()         { *m = GetPresentConfigListV3Resp{} }
func (m *GetPresentConfigListV3Resp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListV3Resp) ProtoMessage()    {}
func (*GetPresentConfigListV3Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{9}
}
func (m *GetPresentConfigListV3Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListV3Resp.Unmarshal(m, b)
}
func (m *GetPresentConfigListV3Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListV3Resp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListV3Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListV3Resp.Merge(dst, src)
}
func (m *GetPresentConfigListV3Resp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListV3Resp.Size(m)
}
func (m *GetPresentConfigListV3Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListV3Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListV3Resp proto.InternalMessageInfo

func (m *GetPresentConfigListV3Resp) GetItemList() []*PresentConfigNew {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GetPresentConfigListV3Resp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *GetPresentConfigListV3Resp) GetEnterBlackList() []*PresentEnterBlacklist {
	if m != nil {
		return m.EnterBlackList
	}
	return nil
}

// 增加礼物配置
type AddPresentConfigReq struct {
	Name                 string                     `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string                     `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32                     `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	EffectBegin          uint32                     `protobuf:"varint,4,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                     `protobuf:"varint,5,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	Rank                 uint32                     `protobuf:"varint,6,opt,name=rank,proto3" json:"rank,omitempty"`
	PriceType            uint32                     `protobuf:"varint,7,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	Extend               *StPresentItemConfigExtend `protobuf:"bytes,8,opt,name=extend,proto3" json:"extend,omitempty"`
	RankFloat            float32                    `protobuf:"fixed32,9,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *AddPresentConfigReq) Reset()         { *m = AddPresentConfigReq{} }
func (m *AddPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddPresentConfigReq) ProtoMessage()    {}
func (*AddPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{10}
}
func (m *AddPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentConfigReq.Unmarshal(m, b)
}
func (m *AddPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentConfigReq.Merge(dst, src)
}
func (m *AddPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddPresentConfigReq.Size(m)
}
func (m *AddPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentConfigReq proto.InternalMessageInfo

func (m *AddPresentConfigReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AddPresentConfigReq) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *AddPresentConfigReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *AddPresentConfigReq) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *AddPresentConfigReq) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *AddPresentConfigReq) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *AddPresentConfigReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *AddPresentConfigReq) GetExtend() *StPresentItemConfigExtend {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *AddPresentConfigReq) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

type AddPresentConfigResp struct {
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddPresentConfigResp) Reset()         { *m = AddPresentConfigResp{} }
func (m *AddPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddPresentConfigResp) ProtoMessage()    {}
func (*AddPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{11}
}
func (m *AddPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentConfigResp.Unmarshal(m, b)
}
func (m *AddPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentConfigResp.Merge(dst, src)
}
func (m *AddPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddPresentConfigResp.Size(m)
}
func (m *AddPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentConfigResp proto.InternalMessageInfo

func (m *AddPresentConfigResp) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

// 删除礼物配置
type DelPresentConfigReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentConfigReq) Reset()         { *m = DelPresentConfigReq{} }
func (m *DelPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelPresentConfigReq) ProtoMessage()    {}
func (*DelPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{12}
}
func (m *DelPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentConfigReq.Unmarshal(m, b)
}
func (m *DelPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentConfigReq.Merge(dst, src)
}
func (m *DelPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelPresentConfigReq.Size(m)
}
func (m *DelPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentConfigReq proto.InternalMessageInfo

func (m *DelPresentConfigReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type DelPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentConfigResp) Reset()         { *m = DelPresentConfigResp{} }
func (m *DelPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelPresentConfigResp) ProtoMessage()    {}
func (*DelPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{13}
}
func (m *DelPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentConfigResp.Unmarshal(m, b)
}
func (m *DelPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentConfigResp.Merge(dst, src)
}
func (m *DelPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelPresentConfigResp.Size(m)
}
func (m *DelPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentConfigResp proto.InternalMessageInfo

// 更新礼物配置
type UpdatePresentConfigReq struct {
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdatePresentConfigReq) Reset()         { *m = UpdatePresentConfigReq{} }
func (m *UpdatePresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentConfigReq) ProtoMessage()    {}
func (*UpdatePresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{14}
}
func (m *UpdatePresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentConfigReq.Unmarshal(m, b)
}
func (m *UpdatePresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentConfigReq.Merge(dst, src)
}
func (m *UpdatePresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentConfigReq.Size(m)
}
func (m *UpdatePresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentConfigReq proto.InternalMessageInfo

func (m *UpdatePresentConfigReq) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

type UpdatePresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentConfigResp) Reset()         { *m = UpdatePresentConfigResp{} }
func (m *UpdatePresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentConfigResp) ProtoMessage()    {}
func (*UpdatePresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{15}
}
func (m *UpdatePresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentConfigResp.Unmarshal(m, b)
}
func (m *UpdatePresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentConfigResp.Merge(dst, src)
}
func (m *UpdatePresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentConfigResp.Size(m)
}
func (m *UpdatePresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentConfigResp proto.InternalMessageInfo

type GetPresentConfigByIdReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigByIdReq) Reset()         { *m = GetPresentConfigByIdReq{} }
func (m *GetPresentConfigByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdReq) ProtoMessage()    {}
func (*GetPresentConfigByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{16}
}
func (m *GetPresentConfigByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdReq.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdReq.Merge(dst, src)
}
func (m *GetPresentConfigByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdReq.Size(m)
}
func (m *GetPresentConfigByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdReq proto.InternalMessageInfo

func (m *GetPresentConfigByIdReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetPresentConfigByIdResp struct {
	ItemConfig           *PresentConfigNew `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPresentConfigByIdResp) Reset()         { *m = GetPresentConfigByIdResp{} }
func (m *GetPresentConfigByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdResp) ProtoMessage()    {}
func (*GetPresentConfigByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{17}
}
func (m *GetPresentConfigByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdResp.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdResp.Merge(dst, src)
}
func (m *GetPresentConfigByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdResp.Size(m)
}
func (m *GetPresentConfigByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdResp proto.InternalMessageInfo

func (m *GetPresentConfigByIdResp) GetItemConfig() *PresentConfigNew {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

type GetPresentConfigListByIdListReq struct {
	ItemId               []uint32 `protobuf:"varint,1,rep,packed,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigListByIdListReq) Reset()         { *m = GetPresentConfigListByIdListReq{} }
func (m *GetPresentConfigListByIdListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListByIdListReq) ProtoMessage()    {}
func (*GetPresentConfigListByIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{18}
}
func (m *GetPresentConfigListByIdListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListByIdListReq.Unmarshal(m, b)
}
func (m *GetPresentConfigListByIdListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListByIdListReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListByIdListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListByIdListReq.Merge(dst, src)
}
func (m *GetPresentConfigListByIdListReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListByIdListReq.Size(m)
}
func (m *GetPresentConfigListByIdListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListByIdListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListByIdListReq proto.InternalMessageInfo

func (m *GetPresentConfigListByIdListReq) GetItemId() []uint32 {
	if m != nil {
		return m.ItemId
	}
	return nil
}

type GetPresentConfigListByIdListResp struct {
	ItemConfig           []*PresentConfigNew `protobuf:"bytes,1,rep,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPresentConfigListByIdListResp) Reset()         { *m = GetPresentConfigListByIdListResp{} }
func (m *GetPresentConfigListByIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListByIdListResp) ProtoMessage()    {}
func (*GetPresentConfigListByIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{19}
}
func (m *GetPresentConfigListByIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListByIdListResp.Unmarshal(m, b)
}
func (m *GetPresentConfigListByIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListByIdListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListByIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListByIdListResp.Merge(dst, src)
}
func (m *GetPresentConfigListByIdListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListByIdListResp.Size(m)
}
func (m *GetPresentConfigListByIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListByIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListByIdListResp proto.InternalMessageInfo

func (m *GetPresentConfigListByIdListResp) GetItemConfig() []*PresentConfigNew {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

// 用来模拟旧的resp
type GetPresentConfigByIdOldResp struct {
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPresentConfigByIdOldResp) Reset()         { *m = GetPresentConfigByIdOldResp{} }
func (m *GetPresentConfigByIdOldResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdOldResp) ProtoMessage()    {}
func (*GetPresentConfigByIdOldResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{20}
}
func (m *GetPresentConfigByIdOldResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdOldResp.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdOldResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdOldResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdOldResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdOldResp.Merge(dst, src)
}
func (m *GetPresentConfigByIdOldResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdOldResp.Size(m)
}
func (m *GetPresentConfigByIdOldResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdOldResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdOldResp proto.InternalMessageInfo

func (m *GetPresentConfigByIdOldResp) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

type GetPresentConfigByIdListResp struct {
	ItemList             []*StPresentItemConfig `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPresentConfigByIdListResp) Reset()         { *m = GetPresentConfigByIdListResp{} }
func (m *GetPresentConfigByIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdListResp) ProtoMessage()    {}
func (*GetPresentConfigByIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{21}
}
func (m *GetPresentConfigByIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdListResp.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdListResp.Merge(dst, src)
}
func (m *GetPresentConfigByIdListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdListResp.Size(m)
}
func (m *GetPresentConfigByIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdListResp proto.InternalMessageInfo

func (m *GetPresentConfigByIdListResp) GetItemList() []*StPresentItemConfig {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 礼物配置 - 新
type PresentConfigNew struct {
	BaseConfig           *PresentBaseConfig   `protobuf:"bytes,1,opt,name=base_config,json=baseConfig,proto3" json:"base_config,omitempty"`
	EffectConfig         *PresentEffectConfig `protobuf:"bytes,2,opt,name=effect_config,json=effectConfig,proto3" json:"effect_config,omitempty"`
	EnterConfig          *PresentEnterConfig  `protobuf:"bytes,3,opt,name=enter_config,json=enterConfig,proto3" json:"enter_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *PresentConfigNew) Reset()         { *m = PresentConfigNew{} }
func (m *PresentConfigNew) String() string { return proto.CompactTextString(m) }
func (*PresentConfigNew) ProtoMessage()    {}
func (*PresentConfigNew) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{22}
}
func (m *PresentConfigNew) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentConfigNew.Unmarshal(m, b)
}
func (m *PresentConfigNew) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentConfigNew.Marshal(b, m, deterministic)
}
func (dst *PresentConfigNew) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentConfigNew.Merge(dst, src)
}
func (m *PresentConfigNew) XXX_Size() int {
	return xxx_messageInfo_PresentConfigNew.Size(m)
}
func (m *PresentConfigNew) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentConfigNew.DiscardUnknown(m)
}

var xxx_messageInfo_PresentConfigNew proto.InternalMessageInfo

func (m *PresentConfigNew) GetBaseConfig() *PresentBaseConfig {
	if m != nil {
		return m.BaseConfig
	}
	return nil
}

func (m *PresentConfigNew) GetEffectConfig() *PresentEffectConfig {
	if m != nil {
		return m.EffectConfig
	}
	return nil
}

func (m *PresentConfigNew) GetEnterConfig() *PresentEnterConfig {
	if m != nil {
		return m.EnterConfig
	}
	return nil
}

// 新版的礼物配置 - 基础配置
type PresentBaseConfig struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string   `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	IsDel                bool     `protobuf:"varint,5,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	PriceType            uint32   `protobuf:"varint,6,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	RichValue            uint32   `protobuf:"varint,7,opt,name=rich_value,json=richValue,proto3" json:"rich_value,omitempty"`
	Score                uint32   `protobuf:"varint,8,opt,name=score,proto3" json:"score,omitempty"`
	Charm                uint32   `protobuf:"varint,9,opt,name=charm,proto3" json:"charm,omitempty"`
	ForceSendable        bool     `protobuf:"varint,10,opt,name=force_sendable,json=forceSendable,proto3" json:"force_sendable,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           uint32   `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	IsTest               bool     `protobuf:"varint,13,opt,name=is_test,json=isTest,proto3" json:"is_test,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentBaseConfig) Reset()         { *m = PresentBaseConfig{} }
func (m *PresentBaseConfig) String() string { return proto.CompactTextString(m) }
func (*PresentBaseConfig) ProtoMessage()    {}
func (*PresentBaseConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{23}
}
func (m *PresentBaseConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBaseConfig.Unmarshal(m, b)
}
func (m *PresentBaseConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBaseConfig.Marshal(b, m, deterministic)
}
func (dst *PresentBaseConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBaseConfig.Merge(dst, src)
}
func (m *PresentBaseConfig) XXX_Size() int {
	return xxx_messageInfo_PresentBaseConfig.Size(m)
}
func (m *PresentBaseConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBaseConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBaseConfig proto.InternalMessageInfo

func (m *PresentBaseConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentBaseConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PresentBaseConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *PresentBaseConfig) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PresentBaseConfig) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *PresentBaseConfig) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *PresentBaseConfig) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *PresentBaseConfig) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PresentBaseConfig) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *PresentBaseConfig) GetForceSendable() bool {
	if m != nil {
		return m.ForceSendable
	}
	return false
}

func (m *PresentBaseConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *PresentBaseConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PresentBaseConfig) GetIsTest() bool {
	if m != nil {
		return m.IsTest
	}
	return false
}

// 新版的礼物配置 - 入口配置
type PresentEnterConfig struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	UnshowBatchOption    bool     `protobuf:"varint,2,opt,name=unshow_batch_option,json=unshowBatchOption,proto3" json:"unshow_batch_option,omitempty"`
	Rank                 float32  `protobuf:"fixed32,3,opt,name=rank,proto3" json:"rank,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,4,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,5,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	NobilityLevel        uint32   `protobuf:"varint,6,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	UnshowPresentShelf   bool     `protobuf:"varint,7,opt,name=unshow_present_shelf,json=unshowPresentShelf,proto3" json:"unshow_present_shelf,omitempty"`
	ShowEffectEnd        bool     `protobuf:"varint,8,opt,name=show_effect_end,json=showEffectEnd,proto3" json:"show_effect_end,omitempty"`
	EffectEndDelay       bool     `protobuf:"varint,9,opt,name=effect_end_delay,json=effectEndDelay,proto3" json:"effect_end_delay,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Tag                  uint32   `protobuf:"varint,11,opt,name=tag,proto3" json:"tag,omitempty"`
	FansLevel            uint32   `protobuf:"varint,12,opt,name=fans_level,json=fansLevel,proto3" json:"fans_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentEnterConfig) Reset()         { *m = PresentEnterConfig{} }
func (m *PresentEnterConfig) String() string { return proto.CompactTextString(m) }
func (*PresentEnterConfig) ProtoMessage()    {}
func (*PresentEnterConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{24}
}
func (m *PresentEnterConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEnterConfig.Unmarshal(m, b)
}
func (m *PresentEnterConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEnterConfig.Marshal(b, m, deterministic)
}
func (dst *PresentEnterConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEnterConfig.Merge(dst, src)
}
func (m *PresentEnterConfig) XXX_Size() int {
	return xxx_messageInfo_PresentEnterConfig.Size(m)
}
func (m *PresentEnterConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEnterConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEnterConfig proto.InternalMessageInfo

func (m *PresentEnterConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentEnterConfig) GetUnshowBatchOption() bool {
	if m != nil {
		return m.UnshowBatchOption
	}
	return false
}

func (m *PresentEnterConfig) GetRank() float32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *PresentEnterConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *PresentEnterConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *PresentEnterConfig) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *PresentEnterConfig) GetUnshowPresentShelf() bool {
	if m != nil {
		return m.UnshowPresentShelf
	}
	return false
}

func (m *PresentEnterConfig) GetShowEffectEnd() bool {
	if m != nil {
		return m.ShowEffectEnd
	}
	return false
}

func (m *PresentEnterConfig) GetEffectEndDelay() bool {
	if m != nil {
		return m.EffectEndDelay
	}
	return false
}

func (m *PresentEnterConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *PresentEnterConfig) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *PresentEnterConfig) GetFansLevel() uint32 {
	if m != nil {
		return m.FansLevel
	}
	return 0
}

// 新版的礼物配置 - 特效配置
type PresentEffectConfig struct {
	ItemId               uint32        `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	VideoEffectUrl       []byte        `protobuf:"bytes,2,opt,name=video_effect_url,json=videoEffectUrl,proto3" json:"video_effect_url,omitempty"`
	ShowEffect           uint32        `protobuf:"varint,3,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	FlowId               uint32        `protobuf:"varint,4,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	NotifyAll            bool          `protobuf:"varint,5,opt,name=notify_all,json=notifyAll,proto3" json:"notify_all,omitempty"`
	CustomText           []*CustomText `protobuf:"bytes,6,rep,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	SmallVapUrl          string        `protobuf:"bytes,7,opt,name=small_vap_url,json=smallVapUrl,proto3" json:"small_vap_url,omitempty"`
	SmallVapMd5          string        `protobuf:"bytes,8,opt,name=small_vap_md5,json=smallVapMd5,proto3" json:"small_vap_md5,omitempty"`
	MicEffectUrl         string        `protobuf:"bytes,9,opt,name=mic_effect_url,json=micEffectUrl,proto3" json:"mic_effect_url,omitempty"`
	MicEffectMd5         string        `protobuf:"bytes,10,opt,name=mic_effect_md5,json=micEffectMd5,proto3" json:"mic_effect_md5,omitempty"`
	UpdateTime           uint32        `protobuf:"varint,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	IsBoxBreaking        bool          `protobuf:"varint,12,opt,name=is_box_breaking,json=isBoxBreaking,proto3" json:"is_box_breaking,omitempty"`
	FusionPresent        bool          `protobuf:"varint,13,opt,name=fusion_present,json=fusionPresent,proto3" json:"fusion_present,omitempty"`
	IosVideoEffectUrl    []byte        `protobuf:"bytes,14,opt,name=ios_video_effect_url,json=iosVideoEffectUrl,proto3" json:"ios_video_effect_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PresentEffectConfig) Reset()         { *m = PresentEffectConfig{} }
func (m *PresentEffectConfig) String() string { return proto.CompactTextString(m) }
func (*PresentEffectConfig) ProtoMessage()    {}
func (*PresentEffectConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{25}
}
func (m *PresentEffectConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectConfig.Unmarshal(m, b)
}
func (m *PresentEffectConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectConfig.Marshal(b, m, deterministic)
}
func (dst *PresentEffectConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectConfig.Merge(dst, src)
}
func (m *PresentEffectConfig) XXX_Size() int {
	return xxx_messageInfo_PresentEffectConfig.Size(m)
}
func (m *PresentEffectConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectConfig proto.InternalMessageInfo

func (m *PresentEffectConfig) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentEffectConfig) GetVideoEffectUrl() []byte {
	if m != nil {
		return m.VideoEffectUrl
	}
	return nil
}

func (m *PresentEffectConfig) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *PresentEffectConfig) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *PresentEffectConfig) GetNotifyAll() bool {
	if m != nil {
		return m.NotifyAll
	}
	return false
}

func (m *PresentEffectConfig) GetCustomText() []*CustomText {
	if m != nil {
		return m.CustomText
	}
	return nil
}

func (m *PresentEffectConfig) GetSmallVapUrl() string {
	if m != nil {
		return m.SmallVapUrl
	}
	return ""
}

func (m *PresentEffectConfig) GetSmallVapMd5() string {
	if m != nil {
		return m.SmallVapMd5
	}
	return ""
}

func (m *PresentEffectConfig) GetMicEffectUrl() string {
	if m != nil {
		return m.MicEffectUrl
	}
	return ""
}

func (m *PresentEffectConfig) GetMicEffectMd5() string {
	if m != nil {
		return m.MicEffectMd5
	}
	return ""
}

func (m *PresentEffectConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *PresentEffectConfig) GetIsBoxBreaking() bool {
	if m != nil {
		return m.IsBoxBreaking
	}
	return false
}

func (m *PresentEffectConfig) GetFusionPresent() bool {
	if m != nil {
		return m.FusionPresent
	}
	return false
}

func (m *PresentEffectConfig) GetIosVideoEffectUrl() []byte {
	if m != nil {
		return m.IosVideoEffectUrl
	}
	return nil
}

type PresentEnterBlacklist struct {
	EnterType            PresentEnterType `protobuf:"varint,1,opt,name=enter_type,json=enterType,proto3,enum=userpresent_go.PresentEnterType" json:"enter_type,omitempty"`
	GiftItemList         []uint32         `protobuf:"varint,2,rep,packed,name=gift_item_list,json=giftItemList,proto3" json:"gift_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PresentEnterBlacklist) Reset()         { *m = PresentEnterBlacklist{} }
func (m *PresentEnterBlacklist) String() string { return proto.CompactTextString(m) }
func (*PresentEnterBlacklist) ProtoMessage()    {}
func (*PresentEnterBlacklist) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{26}
}
func (m *PresentEnterBlacklist) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEnterBlacklist.Unmarshal(m, b)
}
func (m *PresentEnterBlacklist) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEnterBlacklist.Marshal(b, m, deterministic)
}
func (dst *PresentEnterBlacklist) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEnterBlacklist.Merge(dst, src)
}
func (m *PresentEnterBlacklist) XXX_Size() int {
	return xxx_messageInfo_PresentEnterBlacklist.Size(m)
}
func (m *PresentEnterBlacklist) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEnterBlacklist.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEnterBlacklist proto.InternalMessageInfo

func (m *PresentEnterBlacklist) GetEnterType() PresentEnterType {
	if m != nil {
		return m.EnterType
	}
	return PresentEnterType_PresentEnterTypeUnknown
}

func (m *PresentEnterBlacklist) GetGiftItemList() []uint32 {
	if m != nil {
		return m.GiftItemList
	}
	return nil
}

type GetLivePresentOrderListReq struct {
	ToUid                uint32   `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeginTime            uint32   `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLivePresentOrderListReq) Reset()         { *m = GetLivePresentOrderListReq{} }
func (m *GetLivePresentOrderListReq) String() string { return proto.CompactTextString(m) }
func (*GetLivePresentOrderListReq) ProtoMessage()    {}
func (*GetLivePresentOrderListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{27}
}
func (m *GetLivePresentOrderListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLivePresentOrderListReq.Unmarshal(m, b)
}
func (m *GetLivePresentOrderListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLivePresentOrderListReq.Marshal(b, m, deterministic)
}
func (dst *GetLivePresentOrderListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLivePresentOrderListReq.Merge(dst, src)
}
func (m *GetLivePresentOrderListReq) XXX_Size() int {
	return xxx_messageInfo_GetLivePresentOrderListReq.Size(m)
}
func (m *GetLivePresentOrderListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLivePresentOrderListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLivePresentOrderListReq proto.InternalMessageInfo

func (m *GetLivePresentOrderListReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *GetLivePresentOrderListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetLivePresentOrderListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetLivePresentOrderListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetLivePresentOrderListResp struct {
	OrderList            []*LivePresentOrder `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetLivePresentOrderListResp) Reset()         { *m = GetLivePresentOrderListResp{} }
func (m *GetLivePresentOrderListResp) String() string { return proto.CompactTextString(m) }
func (*GetLivePresentOrderListResp) ProtoMessage()    {}
func (*GetLivePresentOrderListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{28}
}
func (m *GetLivePresentOrderListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLivePresentOrderListResp.Unmarshal(m, b)
}
func (m *GetLivePresentOrderListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLivePresentOrderListResp.Marshal(b, m, deterministic)
}
func (dst *GetLivePresentOrderListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLivePresentOrderListResp.Merge(dst, src)
}
func (m *GetLivePresentOrderListResp) XXX_Size() int {
	return xxx_messageInfo_GetLivePresentOrderListResp.Size(m)
}
func (m *GetLivePresentOrderListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLivePresentOrderListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLivePresentOrderListResp proto.InternalMessageInfo

func (m *GetLivePresentOrderListResp) GetOrderList() []*LivePresentOrder {
	if m != nil {
		return m.OrderList
	}
	return nil
}

type LivePresentOrder struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ItemId               uint32   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	ToUid                uint32   `protobuf:"varint,4,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LivePresentOrder) Reset()         { *m = LivePresentOrder{} }
func (m *LivePresentOrder) String() string { return proto.CompactTextString(m) }
func (*LivePresentOrder) ProtoMessage()    {}
func (*LivePresentOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{29}
}
func (m *LivePresentOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LivePresentOrder.Unmarshal(m, b)
}
func (m *LivePresentOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LivePresentOrder.Marshal(b, m, deterministic)
}
func (dst *LivePresentOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LivePresentOrder.Merge(dst, src)
}
func (m *LivePresentOrder) XXX_Size() int {
	return xxx_messageInfo_LivePresentOrder.Size(m)
}
func (m *LivePresentOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_LivePresentOrder.DiscardUnknown(m)
}

var xxx_messageInfo_LivePresentOrder proto.InternalMessageInfo

func (m *LivePresentOrder) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *LivePresentOrder) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LivePresentOrder) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *LivePresentOrder) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *LivePresentOrder) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type StSceneInfo struct {
	SceneId              uint32   `protobuf:"varint,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneType            uint32   `protobuf:"varint,2,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	SubSceneId           uint32   `protobuf:"varint,3,opt,name=sub_scene_id,json=subSceneId,proto3" json:"sub_scene_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StSceneInfo) Reset()         { *m = StSceneInfo{} }
func (m *StSceneInfo) String() string { return proto.CompactTextString(m) }
func (*StSceneInfo) ProtoMessage()    {}
func (*StSceneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{30}
}
func (m *StSceneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StSceneInfo.Unmarshal(m, b)
}
func (m *StSceneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StSceneInfo.Marshal(b, m, deterministic)
}
func (dst *StSceneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StSceneInfo.Merge(dst, src)
}
func (m *StSceneInfo) XXX_Size() int {
	return xxx_messageInfo_StSceneInfo.Size(m)
}
func (m *StSceneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StSceneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StSceneInfo proto.InternalMessageInfo

func (m *StSceneInfo) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *StSceneInfo) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *StSceneInfo) GetSubSceneId() uint32 {
	if m != nil {
		return m.SubSceneId
	}
	return 0
}

type StScenePresentSummary struct {
	SceneId              uint32   `protobuf:"varint,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneType            uint32   `protobuf:"varint,2,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	ItemId               uint32   `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	SubSceneId           uint32   `protobuf:"varint,5,opt,name=sub_scene_id,json=subSceneId,proto3" json:"sub_scene_id,omitempty"`
	Count64              uint64   `protobuf:"varint,6,opt,name=count64,proto3" json:"count64,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StScenePresentSummary) Reset()         { *m = StScenePresentSummary{} }
func (m *StScenePresentSummary) String() string { return proto.CompactTextString(m) }
func (*StScenePresentSummary) ProtoMessage()    {}
func (*StScenePresentSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{31}
}
func (m *StScenePresentSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StScenePresentSummary.Unmarshal(m, b)
}
func (m *StScenePresentSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StScenePresentSummary.Marshal(b, m, deterministic)
}
func (dst *StScenePresentSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StScenePresentSummary.Merge(dst, src)
}
func (m *StScenePresentSummary) XXX_Size() int {
	return xxx_messageInfo_StScenePresentSummary.Size(m)
}
func (m *StScenePresentSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_StScenePresentSummary.DiscardUnknown(m)
}

var xxx_messageInfo_StScenePresentSummary proto.InternalMessageInfo

func (m *StScenePresentSummary) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *StScenePresentSummary) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *StScenePresentSummary) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StScenePresentSummary) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *StScenePresentSummary) GetSubSceneId() uint32 {
	if m != nil {
		return m.SubSceneId
	}
	return 0
}

func (m *StScenePresentSummary) GetCount64() uint64 {
	if m != nil {
		return m.Count64
	}
	return 0
}

type StScenePresentDetail struct {
	SceneId              uint32               `protobuf:"varint,1,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneType            uint32               `protobuf:"varint,2,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	FromUid              uint32               `protobuf:"varint,3,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ToUid                uint32               `protobuf:"varint,4,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,5,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	Count                uint32               `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`
	OrderId              string               `protobuf:"bytes,7,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	CreateTime           uint32               `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Count64              uint64               `protobuf:"varint,9,opt,name=count64,proto3" json:"count64,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *StScenePresentDetail) Reset()         { *m = StScenePresentDetail{} }
func (m *StScenePresentDetail) String() string { return proto.CompactTextString(m) }
func (*StScenePresentDetail) ProtoMessage()    {}
func (*StScenePresentDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{32}
}
func (m *StScenePresentDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StScenePresentDetail.Unmarshal(m, b)
}
func (m *StScenePresentDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StScenePresentDetail.Marshal(b, m, deterministic)
}
func (dst *StScenePresentDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StScenePresentDetail.Merge(dst, src)
}
func (m *StScenePresentDetail) XXX_Size() int {
	return xxx_messageInfo_StScenePresentDetail.Size(m)
}
func (m *StScenePresentDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_StScenePresentDetail.DiscardUnknown(m)
}

var xxx_messageInfo_StScenePresentDetail proto.InternalMessageInfo

func (m *StScenePresentDetail) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *StScenePresentDetail) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *StScenePresentDetail) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *StScenePresentDetail) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *StScenePresentDetail) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

func (m *StScenePresentDetail) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *StScenePresentDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *StScenePresentDetail) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StScenePresentDetail) GetCount64() uint64 {
	if m != nil {
		return m.Count64
	}
	return 0
}

// 记录场景中送出的礼物
type RecordSceneSendPresentReq struct {
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32               `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	OrderId              string               `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,4,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	SceneList            []*StSceneInfo       `protobuf:"bytes,5,rep,name=scene_list,json=sceneList,proto3" json:"scene_list,omitempty"`
	ItemCount            uint32               `protobuf:"varint,6,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	SendTime             uint32               `protobuf:"varint,7,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *RecordSceneSendPresentReq) Reset()         { *m = RecordSceneSendPresentReq{} }
func (m *RecordSceneSendPresentReq) String() string { return proto.CompactTextString(m) }
func (*RecordSceneSendPresentReq) ProtoMessage()    {}
func (*RecordSceneSendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{33}
}
func (m *RecordSceneSendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordSceneSendPresentReq.Unmarshal(m, b)
}
func (m *RecordSceneSendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordSceneSendPresentReq.Marshal(b, m, deterministic)
}
func (dst *RecordSceneSendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordSceneSendPresentReq.Merge(dst, src)
}
func (m *RecordSceneSendPresentReq) XXX_Size() int {
	return xxx_messageInfo_RecordSceneSendPresentReq.Size(m)
}
func (m *RecordSceneSendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordSceneSendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordSceneSendPresentReq proto.InternalMessageInfo

func (m *RecordSceneSendPresentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordSceneSendPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *RecordSceneSendPresentReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *RecordSceneSendPresentReq) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

func (m *RecordSceneSendPresentReq) GetSceneList() []*StSceneInfo {
	if m != nil {
		return m.SceneList
	}
	return nil
}

func (m *RecordSceneSendPresentReq) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *RecordSceneSendPresentReq) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

// 获取场景的礼物汇总
type GetScenePresentSummaryReq struct {
	SceneInfo            *StSceneInfo `protobuf:"bytes,1,opt,name=scene_info,json=sceneInfo,proto3" json:"scene_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetScenePresentSummaryReq) Reset()         { *m = GetScenePresentSummaryReq{} }
func (m *GetScenePresentSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetScenePresentSummaryReq) ProtoMessage()    {}
func (*GetScenePresentSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{34}
}
func (m *GetScenePresentSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenePresentSummaryReq.Unmarshal(m, b)
}
func (m *GetScenePresentSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenePresentSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetScenePresentSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenePresentSummaryReq.Merge(dst, src)
}
func (m *GetScenePresentSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetScenePresentSummaryReq.Size(m)
}
func (m *GetScenePresentSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenePresentSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenePresentSummaryReq proto.InternalMessageInfo

func (m *GetScenePresentSummaryReq) GetSceneInfo() *StSceneInfo {
	if m != nil {
		return m.SceneInfo
	}
	return nil
}

type GetScenePresentSummaryResp struct {
	SummaryList          []*StScenePresentSummary `protobuf:"bytes,1,rep,name=summary_list,json=summaryList,proto3" json:"summary_list,omitempty"`
	TotalValue           uint32                   `protobuf:"varint,2,opt,name=total_value,json=totalValue,proto3" json:"total_value,omitempty"`
	TotalCount           uint32                   `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	TotalCount64         uint64                   `protobuf:"varint,4,opt,name=total_count64,json=totalCount64,proto3" json:"total_count64,omitempty"`
	TotalValue64         uint64                   `protobuf:"varint,5,opt,name=total_value64,json=totalValue64,proto3" json:"total_value64,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetScenePresentSummaryResp) Reset()         { *m = GetScenePresentSummaryResp{} }
func (m *GetScenePresentSummaryResp) String() string { return proto.CompactTextString(m) }
func (*GetScenePresentSummaryResp) ProtoMessage()    {}
func (*GetScenePresentSummaryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{35}
}
func (m *GetScenePresentSummaryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenePresentSummaryResp.Unmarshal(m, b)
}
func (m *GetScenePresentSummaryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenePresentSummaryResp.Marshal(b, m, deterministic)
}
func (dst *GetScenePresentSummaryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenePresentSummaryResp.Merge(dst, src)
}
func (m *GetScenePresentSummaryResp) XXX_Size() int {
	return xxx_messageInfo_GetScenePresentSummaryResp.Size(m)
}
func (m *GetScenePresentSummaryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenePresentSummaryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenePresentSummaryResp proto.InternalMessageInfo

func (m *GetScenePresentSummaryResp) GetSummaryList() []*StScenePresentSummary {
	if m != nil {
		return m.SummaryList
	}
	return nil
}

func (m *GetScenePresentSummaryResp) GetTotalValue() uint32 {
	if m != nil {
		return m.TotalValue
	}
	return 0
}

func (m *GetScenePresentSummaryResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetScenePresentSummaryResp) GetTotalCount64() uint64 {
	if m != nil {
		return m.TotalCount64
	}
	return 0
}

func (m *GetScenePresentSummaryResp) GetTotalValue64() uint64 {
	if m != nil {
		return m.TotalValue64
	}
	return 0
}

// 通知清空场景的礼物信息
type ClearScenePresentReq struct {
	SceneInfo            *StSceneInfo `protobuf:"bytes,1,opt,name=scene_info,json=sceneInfo,proto3" json:"scene_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ClearScenePresentReq) Reset()         { *m = ClearScenePresentReq{} }
func (m *ClearScenePresentReq) String() string { return proto.CompactTextString(m) }
func (*ClearScenePresentReq) ProtoMessage()    {}
func (*ClearScenePresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{36}
}
func (m *ClearScenePresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearScenePresentReq.Unmarshal(m, b)
}
func (m *ClearScenePresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearScenePresentReq.Marshal(b, m, deterministic)
}
func (dst *ClearScenePresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearScenePresentReq.Merge(dst, src)
}
func (m *ClearScenePresentReq) XXX_Size() int {
	return xxx_messageInfo_ClearScenePresentReq.Size(m)
}
func (m *ClearScenePresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearScenePresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearScenePresentReq proto.InternalMessageInfo

func (m *ClearScenePresentReq) GetSceneInfo() *StSceneInfo {
	if m != nil {
		return m.SceneInfo
	}
	return nil
}

// 获取场景的礼物明细
type GetScenePresentDetailListReq struct {
	SceneInfo            *StSceneInfo `protobuf:"bytes,1,opt,name=scene_info,json=sceneInfo,proto3" json:"scene_info,omitempty"`
	BeginTime            uint32       `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32       `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Offset               uint32       `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32       `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetScenePresentDetailListReq) Reset()         { *m = GetScenePresentDetailListReq{} }
func (m *GetScenePresentDetailListReq) String() string { return proto.CompactTextString(m) }
func (*GetScenePresentDetailListReq) ProtoMessage()    {}
func (*GetScenePresentDetailListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{37}
}
func (m *GetScenePresentDetailListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenePresentDetailListReq.Unmarshal(m, b)
}
func (m *GetScenePresentDetailListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenePresentDetailListReq.Marshal(b, m, deterministic)
}
func (dst *GetScenePresentDetailListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenePresentDetailListReq.Merge(dst, src)
}
func (m *GetScenePresentDetailListReq) XXX_Size() int {
	return xxx_messageInfo_GetScenePresentDetailListReq.Size(m)
}
func (m *GetScenePresentDetailListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenePresentDetailListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenePresentDetailListReq proto.InternalMessageInfo

func (m *GetScenePresentDetailListReq) GetSceneInfo() *StSceneInfo {
	if m != nil {
		return m.SceneInfo
	}
	return nil
}

func (m *GetScenePresentDetailListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetScenePresentDetailListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetScenePresentDetailListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetScenePresentDetailListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetScenePresentDetailListResp struct {
	DetailList           []*StScenePresentDetail `protobuf:"bytes,1,rep,name=detail_list,json=detailList,proto3" json:"detail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetScenePresentDetailListResp) Reset()         { *m = GetScenePresentDetailListResp{} }
func (m *GetScenePresentDetailListResp) String() string { return proto.CompactTextString(m) }
func (*GetScenePresentDetailListResp) ProtoMessage()    {}
func (*GetScenePresentDetailListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{38}
}
func (m *GetScenePresentDetailListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenePresentDetailListResp.Unmarshal(m, b)
}
func (m *GetScenePresentDetailListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenePresentDetailListResp.Marshal(b, m, deterministic)
}
func (dst *GetScenePresentDetailListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenePresentDetailListResp.Merge(dst, src)
}
func (m *GetScenePresentDetailListResp) XXX_Size() int {
	return xxx_messageInfo_GetScenePresentDetailListResp.Size(m)
}
func (m *GetScenePresentDetailListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenePresentDetailListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenePresentDetailListResp proto.InternalMessageInfo

func (m *GetScenePresentDetailListResp) GetDetailList() []*StScenePresentDetail {
	if m != nil {
		return m.DetailList
	}
	return nil
}

type RecordSceneSendPresentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordSceneSendPresentResp) Reset()         { *m = RecordSceneSendPresentResp{} }
func (m *RecordSceneSendPresentResp) String() string { return proto.CompactTextString(m) }
func (*RecordSceneSendPresentResp) ProtoMessage()    {}
func (*RecordSceneSendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{39}
}
func (m *RecordSceneSendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordSceneSendPresentResp.Unmarshal(m, b)
}
func (m *RecordSceneSendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordSceneSendPresentResp.Marshal(b, m, deterministic)
}
func (dst *RecordSceneSendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordSceneSendPresentResp.Merge(dst, src)
}
func (m *RecordSceneSendPresentResp) XXX_Size() int {
	return xxx_messageInfo_RecordSceneSendPresentResp.Size(m)
}
func (m *RecordSceneSendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordSceneSendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordSceneSendPresentResp proto.InternalMessageInfo

type ClearScenePresentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearScenePresentResp) Reset()         { *m = ClearScenePresentResp{} }
func (m *ClearScenePresentResp) String() string { return proto.CompactTextString(m) }
func (*ClearScenePresentResp) ProtoMessage()    {}
func (*ClearScenePresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{40}
}
func (m *ClearScenePresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearScenePresentResp.Unmarshal(m, b)
}
func (m *ClearScenePresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearScenePresentResp.Marshal(b, m, deterministic)
}
func (dst *ClearScenePresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearScenePresentResp.Merge(dst, src)
}
func (m *ClearScenePresentResp) XXX_Size() int {
	return xxx_messageInfo_ClearScenePresentResp.Size(m)
}
func (m *ClearScenePresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearScenePresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearScenePresentResp proto.InternalMessageInfo

type NamingPresentInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GiftId               uint32   `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	NamingContent        string   `protobuf:"bytes,4,opt,name=naming_content,json=namingContent,proto3" json:"naming_content,omitempty"`
	BeginTs              uint32   `protobuf:"varint,5,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Account              string   `protobuf:"bytes,7,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NamingPresentInfo) Reset()         { *m = NamingPresentInfo{} }
func (m *NamingPresentInfo) String() string { return proto.CompactTextString(m) }
func (*NamingPresentInfo) ProtoMessage()    {}
func (*NamingPresentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{41}
}
func (m *NamingPresentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NamingPresentInfo.Unmarshal(m, b)
}
func (m *NamingPresentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NamingPresentInfo.Marshal(b, m, deterministic)
}
func (dst *NamingPresentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NamingPresentInfo.Merge(dst, src)
}
func (m *NamingPresentInfo) XXX_Size() int {
	return xxx_messageInfo_NamingPresentInfo.Size(m)
}
func (m *NamingPresentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NamingPresentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NamingPresentInfo proto.InternalMessageInfo

func (m *NamingPresentInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NamingPresentInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NamingPresentInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *NamingPresentInfo) GetNamingContent() string {
	if m != nil {
		return m.NamingContent
	}
	return ""
}

func (m *NamingPresentInfo) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *NamingPresentInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *NamingPresentInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type AddNamingPresentInfoReq struct {
	Info                 *NamingPresentInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddNamingPresentInfoReq) Reset()         { *m = AddNamingPresentInfoReq{} }
func (m *AddNamingPresentInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddNamingPresentInfoReq) ProtoMessage()    {}
func (*AddNamingPresentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{42}
}
func (m *AddNamingPresentInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNamingPresentInfoReq.Unmarshal(m, b)
}
func (m *AddNamingPresentInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNamingPresentInfoReq.Marshal(b, m, deterministic)
}
func (dst *AddNamingPresentInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNamingPresentInfoReq.Merge(dst, src)
}
func (m *AddNamingPresentInfoReq) XXX_Size() int {
	return xxx_messageInfo_AddNamingPresentInfoReq.Size(m)
}
func (m *AddNamingPresentInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNamingPresentInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddNamingPresentInfoReq proto.InternalMessageInfo

func (m *AddNamingPresentInfoReq) GetInfo() *NamingPresentInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AddNamingPresentInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddNamingPresentInfoResp) Reset()         { *m = AddNamingPresentInfoResp{} }
func (m *AddNamingPresentInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddNamingPresentInfoResp) ProtoMessage()    {}
func (*AddNamingPresentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{43}
}
func (m *AddNamingPresentInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNamingPresentInfoResp.Unmarshal(m, b)
}
func (m *AddNamingPresentInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNamingPresentInfoResp.Marshal(b, m, deterministic)
}
func (dst *AddNamingPresentInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNamingPresentInfoResp.Merge(dst, src)
}
func (m *AddNamingPresentInfoResp) XXX_Size() int {
	return xxx_messageInfo_AddNamingPresentInfoResp.Size(m)
}
func (m *AddNamingPresentInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNamingPresentInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddNamingPresentInfoResp proto.InternalMessageInfo

type UpdateNamingPresentInfoReq struct {
	Info                 *NamingPresentInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateNamingPresentInfoReq) Reset()         { *m = UpdateNamingPresentInfoReq{} }
func (m *UpdateNamingPresentInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateNamingPresentInfoReq) ProtoMessage()    {}
func (*UpdateNamingPresentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{44}
}
func (m *UpdateNamingPresentInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNamingPresentInfoReq.Unmarshal(m, b)
}
func (m *UpdateNamingPresentInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNamingPresentInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateNamingPresentInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNamingPresentInfoReq.Merge(dst, src)
}
func (m *UpdateNamingPresentInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateNamingPresentInfoReq.Size(m)
}
func (m *UpdateNamingPresentInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNamingPresentInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNamingPresentInfoReq proto.InternalMessageInfo

func (m *UpdateNamingPresentInfoReq) GetInfo() *NamingPresentInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateNamingPresentInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNamingPresentInfoResp) Reset()         { *m = UpdateNamingPresentInfoResp{} }
func (m *UpdateNamingPresentInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateNamingPresentInfoResp) ProtoMessage()    {}
func (*UpdateNamingPresentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{45}
}
func (m *UpdateNamingPresentInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNamingPresentInfoResp.Unmarshal(m, b)
}
func (m *UpdateNamingPresentInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNamingPresentInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateNamingPresentInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNamingPresentInfoResp.Merge(dst, src)
}
func (m *UpdateNamingPresentInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateNamingPresentInfoResp.Size(m)
}
func (m *UpdateNamingPresentInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNamingPresentInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNamingPresentInfoResp proto.InternalMessageInfo

type DelNamingPresentInfoReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelNamingPresentInfoReq) Reset()         { *m = DelNamingPresentInfoReq{} }
func (m *DelNamingPresentInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelNamingPresentInfoReq) ProtoMessage()    {}
func (*DelNamingPresentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{46}
}
func (m *DelNamingPresentInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelNamingPresentInfoReq.Unmarshal(m, b)
}
func (m *DelNamingPresentInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelNamingPresentInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelNamingPresentInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelNamingPresentInfoReq.Merge(dst, src)
}
func (m *DelNamingPresentInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelNamingPresentInfoReq.Size(m)
}
func (m *DelNamingPresentInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelNamingPresentInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelNamingPresentInfoReq proto.InternalMessageInfo

func (m *DelNamingPresentInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelNamingPresentInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelNamingPresentInfoResp) Reset()         { *m = DelNamingPresentInfoResp{} }
func (m *DelNamingPresentInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelNamingPresentInfoResp) ProtoMessage()    {}
func (*DelNamingPresentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{47}
}
func (m *DelNamingPresentInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelNamingPresentInfoResp.Unmarshal(m, b)
}
func (m *DelNamingPresentInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelNamingPresentInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelNamingPresentInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelNamingPresentInfoResp.Merge(dst, src)
}
func (m *DelNamingPresentInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelNamingPresentInfoResp.Size(m)
}
func (m *DelNamingPresentInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelNamingPresentInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelNamingPresentInfoResp proto.InternalMessageInfo

type GetNamingPresentInfoListReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	GiftId               uint32   `protobuf:"varint,4,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	BeginTs              uint32   `protobuf:"varint,5,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNamingPresentInfoListReq) Reset()         { *m = GetNamingPresentInfoListReq{} }
func (m *GetNamingPresentInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetNamingPresentInfoListReq) ProtoMessage()    {}
func (*GetNamingPresentInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{48}
}
func (m *GetNamingPresentInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNamingPresentInfoListReq.Unmarshal(m, b)
}
func (m *GetNamingPresentInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNamingPresentInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetNamingPresentInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNamingPresentInfoListReq.Merge(dst, src)
}
func (m *GetNamingPresentInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetNamingPresentInfoListReq.Size(m)
}
func (m *GetNamingPresentInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNamingPresentInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNamingPresentInfoListReq proto.InternalMessageInfo

func (m *GetNamingPresentInfoListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetNamingPresentInfoListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetNamingPresentInfoListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetNamingPresentInfoListReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *GetNamingPresentInfoListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetNamingPresentInfoListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetNamingPresentInfoListResp struct {
	InfoList             []*NamingPresentInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	NextOffset           uint32               `protobuf:"varint,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetNamingPresentInfoListResp) Reset()         { *m = GetNamingPresentInfoListResp{} }
func (m *GetNamingPresentInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetNamingPresentInfoListResp) ProtoMessage()    {}
func (*GetNamingPresentInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{49}
}
func (m *GetNamingPresentInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNamingPresentInfoListResp.Unmarshal(m, b)
}
func (m *GetNamingPresentInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNamingPresentInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetNamingPresentInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNamingPresentInfoListResp.Merge(dst, src)
}
func (m *GetNamingPresentInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetNamingPresentInfoListResp.Size(m)
}
func (m *GetNamingPresentInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNamingPresentInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNamingPresentInfoListResp proto.InternalMessageInfo

func (m *GetNamingPresentInfoListResp) GetInfoList() []*NamingPresentInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetNamingPresentInfoListResp) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

func (m *GetNamingPresentInfoListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 获取所有生效的礼物冠名信息
type GetValidNamingPresentInfosReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetValidNamingPresentInfosReq) Reset()         { *m = GetValidNamingPresentInfosReq{} }
func (m *GetValidNamingPresentInfosReq) String() string { return proto.CompactTextString(m) }
func (*GetValidNamingPresentInfosReq) ProtoMessage()    {}
func (*GetValidNamingPresentInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{50}
}
func (m *GetValidNamingPresentInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetValidNamingPresentInfosReq.Unmarshal(m, b)
}
func (m *GetValidNamingPresentInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetValidNamingPresentInfosReq.Marshal(b, m, deterministic)
}
func (dst *GetValidNamingPresentInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetValidNamingPresentInfosReq.Merge(dst, src)
}
func (m *GetValidNamingPresentInfosReq) XXX_Size() int {
	return xxx_messageInfo_GetValidNamingPresentInfosReq.Size(m)
}
func (m *GetValidNamingPresentInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetValidNamingPresentInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetValidNamingPresentInfosReq proto.InternalMessageInfo

type GetValidNamingPresentInfosResp struct {
	InfoList             []*NamingPresentInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetValidNamingPresentInfosResp) Reset()         { *m = GetValidNamingPresentInfosResp{} }
func (m *GetValidNamingPresentInfosResp) String() string { return proto.CompactTextString(m) }
func (*GetValidNamingPresentInfosResp) ProtoMessage()    {}
func (*GetValidNamingPresentInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{51}
}
func (m *GetValidNamingPresentInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetValidNamingPresentInfosResp.Unmarshal(m, b)
}
func (m *GetValidNamingPresentInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetValidNamingPresentInfosResp.Marshal(b, m, deterministic)
}
func (dst *GetValidNamingPresentInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetValidNamingPresentInfosResp.Merge(dst, src)
}
func (m *GetValidNamingPresentInfosResp) XXX_Size() int {
	return xxx_messageInfo_GetValidNamingPresentInfosResp.Size(m)
}
func (m *GetValidNamingPresentInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetValidNamingPresentInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetValidNamingPresentInfosResp proto.InternalMessageInfo

func (m *GetValidNamingPresentInfosResp) GetInfoList() []*NamingPresentInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 赠送礼物
type SendPresentReq struct {
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32               `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	OrderId              string               `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ItemId               uint32               `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ChannelId            uint32               `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GuildId              uint32               `protobuf:"varint,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ItemCount            uint32               `protobuf:"varint,7,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	AddCharm             uint32               `protobuf:"varint,8,opt,name=add_charm,json=addCharm,proto3" json:"add_charm,omitempty"`
	SendTime             uint32               `protobuf:"varint,9,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,10,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	OptInvalid           bool                 `protobuf:"varint,11,opt,name=opt_invalid,json=optInvalid,proto3" json:"opt_invalid,omitempty"`
	AsyncFlag            bool                 `protobuf:"varint,12,opt,name=async_flag,json=asyncFlag,proto3" json:"async_flag,omitempty"`
	UserFromIp           string               `protobuf:"bytes,13,opt,name=user_from_ip,json=userFromIp,proto3" json:"user_from_ip,omitempty"`
	ChannelType          uint32               `protobuf:"varint,14,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ItemSource           uint32               `protobuf:"varint,15,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	ChannelName          string               `protobuf:"bytes,16,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelDisplayId     uint32               `protobuf:"varint,17,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	SendSource           uint32               `protobuf:"varint,18,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	SendPlatform         uint32               `protobuf:"varint,19,opt,name=send_platform,json=sendPlatform,proto3" json:"send_platform,omitempty"`
	BatchType            uint32               `protobuf:"varint,20,opt,name=batch_type,json=batchType,proto3" json:"batch_type,omitempty"`
	AppId                uint32               `protobuf:"varint,21,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32               `protobuf:"varint,22,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ReceiverGuildId      uint32               `protobuf:"varint,23,opt,name=receiver_guild_id,json=receiverGuildId,proto3" json:"receiver_guild_id,omitempty"`
	GiverGuildId         uint32               `protobuf:"varint,24,opt,name=giver_guild_id,json=giverGuildId,proto3" json:"giver_guild_id,omitempty"`
	AddRich              uint32               `protobuf:"varint,25,opt,name=add_rich,json=addRich,proto3" json:"add_rich,omitempty"`
	SendMethod           uint32               `protobuf:"varint,26,opt,name=send_method,json=sendMethod,proto3" json:"send_method,omitempty"`
	BindChannelId        uint32               `protobuf:"varint,27,opt,name=bind_channel_id,json=bindChannelId,proto3" json:"bind_channel_id,omitempty"`
	DealToken            string               `protobuf:"bytes,28,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	FromUkwAccount       string               `protobuf:"bytes,29,opt,name=from_ukw_account,json=fromUkwAccount,proto3" json:"from_ukw_account,omitempty"`
	FromUkwNickname      string               `protobuf:"bytes,30,opt,name=from_ukw_nickname,json=fromUkwNickname,proto3" json:"from_ukw_nickname,omitempty"`
	ToUkwAccount         string               `protobuf:"bytes,31,opt,name=to_ukw_account,json=toUkwAccount,proto3" json:"to_ukw_account,omitempty"`
	ToUkwNickname        string               `protobuf:"bytes,32,opt,name=to_ukw_nickname,json=toUkwNickname,proto3" json:"to_ukw_nickname,omitempty"`
	ChannelGameId        uint32               `protobuf:"varint,33,opt,name=channel_game_id,json=channelGameId,proto3" json:"channel_game_id,omitempty"`
	IsVirtualLive        bool                 `protobuf:"varint,34,opt,name=is_virtual_live,json=isVirtualLive,proto3" json:"is_virtual_live,omitempty"`
	ScoreType            uint32               `protobuf:"varint,35,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
	SendChannelId        uint32               `protobuf:"varint,36,opt,name=send_channel_id,json=sendChannelId,proto3" json:"send_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SendPresentReq) Reset()         { *m = SendPresentReq{} }
func (m *SendPresentReq) String() string { return proto.CompactTextString(m) }
func (*SendPresentReq) ProtoMessage()    {}
func (*SendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{52}
}
func (m *SendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPresentReq.Unmarshal(m, b)
}
func (m *SendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPresentReq.Marshal(b, m, deterministic)
}
func (dst *SendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPresentReq.Merge(dst, src)
}
func (m *SendPresentReq) XXX_Size() int {
	return xxx_messageInfo_SendPresentReq.Size(m)
}
func (m *SendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendPresentReq proto.InternalMessageInfo

func (m *SendPresentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendPresentReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SendPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SendPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendPresentReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SendPresentReq) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *SendPresentReq) GetAddCharm() uint32 {
	if m != nil {
		return m.AddCharm
	}
	return 0
}

func (m *SendPresentReq) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *SendPresentReq) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

func (m *SendPresentReq) GetOptInvalid() bool {
	if m != nil {
		return m.OptInvalid
	}
	return false
}

func (m *SendPresentReq) GetAsyncFlag() bool {
	if m != nil {
		return m.AsyncFlag
	}
	return false
}

func (m *SendPresentReq) GetUserFromIp() string {
	if m != nil {
		return m.UserFromIp
	}
	return ""
}

func (m *SendPresentReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SendPresentReq) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *SendPresentReq) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *SendPresentReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *SendPresentReq) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *SendPresentReq) GetSendPlatform() uint32 {
	if m != nil {
		return m.SendPlatform
	}
	return 0
}

func (m *SendPresentReq) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *SendPresentReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SendPresentReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SendPresentReq) GetReceiverGuildId() uint32 {
	if m != nil {
		return m.ReceiverGuildId
	}
	return 0
}

func (m *SendPresentReq) GetGiverGuildId() uint32 {
	if m != nil {
		return m.GiverGuildId
	}
	return 0
}

func (m *SendPresentReq) GetAddRich() uint32 {
	if m != nil {
		return m.AddRich
	}
	return 0
}

func (m *SendPresentReq) GetSendMethod() uint32 {
	if m != nil {
		return m.SendMethod
	}
	return 0
}

func (m *SendPresentReq) GetBindChannelId() uint32 {
	if m != nil {
		return m.BindChannelId
	}
	return 0
}

func (m *SendPresentReq) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *SendPresentReq) GetFromUkwAccount() string {
	if m != nil {
		return m.FromUkwAccount
	}
	return ""
}

func (m *SendPresentReq) GetFromUkwNickname() string {
	if m != nil {
		return m.FromUkwNickname
	}
	return ""
}

func (m *SendPresentReq) GetToUkwAccount() string {
	if m != nil {
		return m.ToUkwAccount
	}
	return ""
}

func (m *SendPresentReq) GetToUkwNickname() string {
	if m != nil {
		return m.ToUkwNickname
	}
	return ""
}

func (m *SendPresentReq) GetChannelGameId() uint32 {
	if m != nil {
		return m.ChannelGameId
	}
	return 0
}

func (m *SendPresentReq) GetIsVirtualLive() bool {
	if m != nil {
		return m.IsVirtualLive
	}
	return false
}

func (m *SendPresentReq) GetScoreType() uint32 {
	if m != nil {
		return m.ScoreType
	}
	return 0
}

func (m *SendPresentReq) GetSendChannelId() uint32 {
	if m != nil {
		return m.SendChannelId
	}
	return 0
}

type SendPresentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendPresentResp) Reset()         { *m = SendPresentResp{} }
func (m *SendPresentResp) String() string { return proto.CompactTextString(m) }
func (*SendPresentResp) ProtoMessage()    {}
func (*SendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{53}
}
func (m *SendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPresentResp.Unmarshal(m, b)
}
func (m *SendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPresentResp.Marshal(b, m, deterministic)
}
func (dst *SendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPresentResp.Merge(dst, src)
}
func (m *SendPresentResp) XXX_Size() int {
	return xxx_messageInfo_SendPresentResp.Size(m)
}
func (m *SendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendPresentResp proto.InternalMessageInfo

// 获取用户收到的礼物明细
type GetUserPresentDetailListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPresentDetailListReq) Reset()         { *m = GetUserPresentDetailListReq{} }
func (m *GetUserPresentDetailListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentDetailListReq) ProtoMessage()    {}
func (*GetUserPresentDetailListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{54}
}
func (m *GetUserPresentDetailListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentDetailListReq.Unmarshal(m, b)
}
func (m *GetUserPresentDetailListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentDetailListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentDetailListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentDetailListReq.Merge(dst, src)
}
func (m *GetUserPresentDetailListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentDetailListReq.Size(m)
}
func (m *GetUserPresentDetailListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentDetailListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentDetailListReq proto.InternalMessageInfo

func (m *GetUserPresentDetailListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserPresentDetailListResp struct {
	DetailList           []*StUserPresentDetail `protobuf:"bytes,1,rep,name=detail_list,json=detailList,proto3" json:"detail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserPresentDetailListResp) Reset()         { *m = GetUserPresentDetailListResp{} }
func (m *GetUserPresentDetailListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentDetailListResp) ProtoMessage()    {}
func (*GetUserPresentDetailListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{55}
}
func (m *GetUserPresentDetailListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentDetailListResp.Unmarshal(m, b)
}
func (m *GetUserPresentDetailListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentDetailListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentDetailListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentDetailListResp.Merge(dst, src)
}
func (m *GetUserPresentDetailListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentDetailListResp.Size(m)
}
func (m *GetUserPresentDetailListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentDetailListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentDetailListResp proto.InternalMessageInfo

func (m *GetUserPresentDetailListResp) GetDetailList() []*StUserPresentDetail {
	if m != nil {
		return m.DetailList
	}
	return nil
}

// 获取用户送出的礼物明细
type GetUserPresentSendDetailListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPresentSendDetailListReq) Reset()         { *m = GetUserPresentSendDetailListReq{} }
func (m *GetUserPresentSendDetailListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentSendDetailListReq) ProtoMessage()    {}
func (*GetUserPresentSendDetailListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{56}
}
func (m *GetUserPresentSendDetailListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentSendDetailListReq.Unmarshal(m, b)
}
func (m *GetUserPresentSendDetailListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentSendDetailListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentSendDetailListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentSendDetailListReq.Merge(dst, src)
}
func (m *GetUserPresentSendDetailListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentSendDetailListReq.Size(m)
}
func (m *GetUserPresentSendDetailListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentSendDetailListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentSendDetailListReq proto.InternalMessageInfo

func (m *GetUserPresentSendDetailListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserPresentSendDetailListResp struct {
	DetailList           []*StUserPresentDetail `protobuf:"bytes,1,rep,name=detail_list,json=detailList,proto3" json:"detail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserPresentSendDetailListResp) Reset()         { *m = GetUserPresentSendDetailListResp{} }
func (m *GetUserPresentSendDetailListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentSendDetailListResp) ProtoMessage()    {}
func (*GetUserPresentSendDetailListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{57}
}
func (m *GetUserPresentSendDetailListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentSendDetailListResp.Unmarshal(m, b)
}
func (m *GetUserPresentSendDetailListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentSendDetailListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentSendDetailListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentSendDetailListResp.Merge(dst, src)
}
func (m *GetUserPresentSendDetailListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentSendDetailListResp.Size(m)
}
func (m *GetUserPresentSendDetailListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentSendDetailListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentSendDetailListResp proto.InternalMessageInfo

func (m *GetUserPresentSendDetailListResp) GetDetailList() []*StUserPresentDetail {
	if m != nil {
		return m.DetailList
	}
	return nil
}

// 获取用户的礼物数量总览
type GetUserPresentSummaryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsSend               bool     `protobuf:"varint,2,opt,name=is_send,json=isSend,proto3" json:"is_send,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPresentSummaryReq) Reset()         { *m = GetUserPresentSummaryReq{} }
func (m *GetUserPresentSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentSummaryReq) ProtoMessage()    {}
func (*GetUserPresentSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{58}
}
func (m *GetUserPresentSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentSummaryReq.Unmarshal(m, b)
}
func (m *GetUserPresentSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentSummaryReq.Merge(dst, src)
}
func (m *GetUserPresentSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentSummaryReq.Size(m)
}
func (m *GetUserPresentSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentSummaryReq proto.InternalMessageInfo

func (m *GetUserPresentSummaryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPresentSummaryReq) GetIsSend() bool {
	if m != nil {
		return m.IsSend
	}
	return false
}

type GetUserPresentSummaryResp struct {
	SummaryList          []*StUserPresentSummary `protobuf:"bytes,1,rep,name=summary_list,json=summaryList,proto3" json:"summary_list,omitempty"`
	TotalValue           uint32                  `protobuf:"varint,2,opt,name=total_value,json=totalValue,proto3" json:"total_value,omitempty"`
	TotalCount           uint32                  `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetUserPresentSummaryResp) Reset()         { *m = GetUserPresentSummaryResp{} }
func (m *GetUserPresentSummaryResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentSummaryResp) ProtoMessage()    {}
func (*GetUserPresentSummaryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{59}
}
func (m *GetUserPresentSummaryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentSummaryResp.Unmarshal(m, b)
}
func (m *GetUserPresentSummaryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentSummaryResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentSummaryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentSummaryResp.Merge(dst, src)
}
func (m *GetUserPresentSummaryResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentSummaryResp.Size(m)
}
func (m *GetUserPresentSummaryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentSummaryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentSummaryResp proto.InternalMessageInfo

func (m *GetUserPresentSummaryResp) GetSummaryList() []*StUserPresentSummary {
	if m != nil {
		return m.SummaryList
	}
	return nil
}

func (m *GetUserPresentSummaryResp) GetTotalValue() uint32 {
	if m != nil {
		return m.TotalValue
	}
	return 0
}

func (m *GetUserPresentSummaryResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type GetUserPresentSummaryByItemListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsSend               bool     `protobuf:"varint,2,opt,name=is_send,json=isSend,proto3" json:"is_send,omitempty"`
	ItemList             []uint32 `protobuf:"varint,3,rep,packed,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPresentSummaryByItemListReq) Reset()         { *m = GetUserPresentSummaryByItemListReq{} }
func (m *GetUserPresentSummaryByItemListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentSummaryByItemListReq) ProtoMessage()    {}
func (*GetUserPresentSummaryByItemListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{60}
}
func (m *GetUserPresentSummaryByItemListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentSummaryByItemListReq.Unmarshal(m, b)
}
func (m *GetUserPresentSummaryByItemListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentSummaryByItemListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentSummaryByItemListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentSummaryByItemListReq.Merge(dst, src)
}
func (m *GetUserPresentSummaryByItemListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentSummaryByItemListReq.Size(m)
}
func (m *GetUserPresentSummaryByItemListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentSummaryByItemListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentSummaryByItemListReq proto.InternalMessageInfo

func (m *GetUserPresentSummaryByItemListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPresentSummaryByItemListReq) GetIsSend() bool {
	if m != nil {
		return m.IsSend
	}
	return false
}

func (m *GetUserPresentSummaryByItemListReq) GetItemList() []uint32 {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type GetUserPresentSummaryByItemListResp struct {
	SummaryList          []*StUserPresentSummary `protobuf:"bytes,1,rep,name=summary_list,json=summaryList,proto3" json:"summary_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetUserPresentSummaryByItemListResp) Reset()         { *m = GetUserPresentSummaryByItemListResp{} }
func (m *GetUserPresentSummaryByItemListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentSummaryByItemListResp) ProtoMessage()    {}
func (*GetUserPresentSummaryByItemListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{61}
}
func (m *GetUserPresentSummaryByItemListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentSummaryByItemListResp.Unmarshal(m, b)
}
func (m *GetUserPresentSummaryByItemListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentSummaryByItemListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentSummaryByItemListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentSummaryByItemListResp.Merge(dst, src)
}
func (m *GetUserPresentSummaryByItemListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentSummaryByItemListResp.Size(m)
}
func (m *GetUserPresentSummaryByItemListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentSummaryByItemListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentSummaryByItemListResp proto.InternalMessageInfo

func (m *GetUserPresentSummaryByItemListResp) GetSummaryList() []*StUserPresentSummary {
	if m != nil {
		return m.SummaryList
	}
	return nil
}

// 获取礼物配置的更新时间
type GetPresentConfigUpdateTimeReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigUpdateTimeReq) Reset()         { *m = GetPresentConfigUpdateTimeReq{} }
func (m *GetPresentConfigUpdateTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigUpdateTimeReq) ProtoMessage()    {}
func (*GetPresentConfigUpdateTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{62}
}
func (m *GetPresentConfigUpdateTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigUpdateTimeReq.Unmarshal(m, b)
}
func (m *GetPresentConfigUpdateTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigUpdateTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigUpdateTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigUpdateTimeReq.Merge(dst, src)
}
func (m *GetPresentConfigUpdateTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigUpdateTimeReq.Size(m)
}
func (m *GetPresentConfigUpdateTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigUpdateTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigUpdateTimeReq proto.InternalMessageInfo

type GetPresentConfigUpdateTimeResp struct {
	UpdateTime           uint32   `protobuf:"varint,1,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigUpdateTimeResp) Reset()         { *m = GetPresentConfigUpdateTimeResp{} }
func (m *GetPresentConfigUpdateTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigUpdateTimeResp) ProtoMessage()    {}
func (*GetPresentConfigUpdateTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{63}
}
func (m *GetPresentConfigUpdateTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigUpdateTimeResp.Unmarshal(m, b)
}
func (m *GetPresentConfigUpdateTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigUpdateTimeResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigUpdateTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigUpdateTimeResp.Merge(dst, src)
}
func (m *GetPresentConfigUpdateTimeResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigUpdateTimeResp.Size(m)
}
func (m *GetPresentConfigUpdateTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigUpdateTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigUpdateTimeResp proto.InternalMessageInfo

func (m *GetPresentConfigUpdateTimeResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 获取礼物订单的状态
type GetPresentOrderStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentOrderStatusReq) Reset()         { *m = GetPresentOrderStatusReq{} }
func (m *GetPresentOrderStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentOrderStatusReq) ProtoMessage()    {}
func (*GetPresentOrderStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{64}
}
func (m *GetPresentOrderStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentOrderStatusReq.Unmarshal(m, b)
}
func (m *GetPresentOrderStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentOrderStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentOrderStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentOrderStatusReq.Merge(dst, src)
}
func (m *GetPresentOrderStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentOrderStatusReq.Size(m)
}
func (m *GetPresentOrderStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentOrderStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentOrderStatusReq proto.InternalMessageInfo

func (m *GetPresentOrderStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPresentOrderStatusReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetPresentOrderStatusResp struct {
	OrderStatus          uint32   `protobuf:"varint,1,opt,name=order_status,json=orderStatus,proto3" json:"order_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentOrderStatusResp) Reset()         { *m = GetPresentOrderStatusResp{} }
func (m *GetPresentOrderStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentOrderStatusResp) ProtoMessage()    {}
func (*GetPresentOrderStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{65}
}
func (m *GetPresentOrderStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentOrderStatusResp.Unmarshal(m, b)
}
func (m *GetPresentOrderStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentOrderStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentOrderStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentOrderStatusResp.Merge(dst, src)
}
func (m *GetPresentOrderStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentOrderStatusResp.Size(m)
}
func (m *GetPresentOrderStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentOrderStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentOrderStatusResp proto.InternalMessageInfo

func (m *GetPresentOrderStatusResp) GetOrderStatus() uint32 {
	if m != nil {
		return m.OrderStatus
	}
	return 0
}

// 动效模板配置
type DynamicEffectTemplate struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Url                  string   `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,5,opt,name=md5,proto3" json:"md5,omitempty"`
	OpAccount            string   `protobuf:"bytes,6,opt,name=op_account,json=opAccount,proto3" json:"op_account,omitempty"`
	UpdateTs             uint32   `protobuf:"varint,7,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	BgColor              string   `protobuf:"bytes,8,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DynamicEffectTemplate) Reset()         { *m = DynamicEffectTemplate{} }
func (m *DynamicEffectTemplate) String() string { return proto.CompactTextString(m) }
func (*DynamicEffectTemplate) ProtoMessage()    {}
func (*DynamicEffectTemplate) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{66}
}
func (m *DynamicEffectTemplate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DynamicEffectTemplate.Unmarshal(m, b)
}
func (m *DynamicEffectTemplate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DynamicEffectTemplate.Marshal(b, m, deterministic)
}
func (dst *DynamicEffectTemplate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DynamicEffectTemplate.Merge(dst, src)
}
func (m *DynamicEffectTemplate) XXX_Size() int {
	return xxx_messageInfo_DynamicEffectTemplate.Size(m)
}
func (m *DynamicEffectTemplate) XXX_DiscardUnknown() {
	xxx_messageInfo_DynamicEffectTemplate.DiscardUnknown(m)
}

var xxx_messageInfo_DynamicEffectTemplate proto.InternalMessageInfo

func (m *DynamicEffectTemplate) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DynamicEffectTemplate) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DynamicEffectTemplate) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *DynamicEffectTemplate) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DynamicEffectTemplate) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *DynamicEffectTemplate) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

func (m *DynamicEffectTemplate) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *DynamicEffectTemplate) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

type AddDynamicEffectTemplateReq struct {
	TemplateInfo         *DynamicEffectTemplate `protobuf:"bytes,1,opt,name=template_info,json=templateInfo,proto3" json:"template_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AddDynamicEffectTemplateReq) Reset()         { *m = AddDynamicEffectTemplateReq{} }
func (m *AddDynamicEffectTemplateReq) String() string { return proto.CompactTextString(m) }
func (*AddDynamicEffectTemplateReq) ProtoMessage()    {}
func (*AddDynamicEffectTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{67}
}
func (m *AddDynamicEffectTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDynamicEffectTemplateReq.Unmarshal(m, b)
}
func (m *AddDynamicEffectTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDynamicEffectTemplateReq.Marshal(b, m, deterministic)
}
func (dst *AddDynamicEffectTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDynamicEffectTemplateReq.Merge(dst, src)
}
func (m *AddDynamicEffectTemplateReq) XXX_Size() int {
	return xxx_messageInfo_AddDynamicEffectTemplateReq.Size(m)
}
func (m *AddDynamicEffectTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDynamicEffectTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddDynamicEffectTemplateReq proto.InternalMessageInfo

func (m *AddDynamicEffectTemplateReq) GetTemplateInfo() *DynamicEffectTemplate {
	if m != nil {
		return m.TemplateInfo
	}
	return nil
}

type AddDynamicEffectTemplateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddDynamicEffectTemplateResp) Reset()         { *m = AddDynamicEffectTemplateResp{} }
func (m *AddDynamicEffectTemplateResp) String() string { return proto.CompactTextString(m) }
func (*AddDynamicEffectTemplateResp) ProtoMessage()    {}
func (*AddDynamicEffectTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{68}
}
func (m *AddDynamicEffectTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDynamicEffectTemplateResp.Unmarshal(m, b)
}
func (m *AddDynamicEffectTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDynamicEffectTemplateResp.Marshal(b, m, deterministic)
}
func (dst *AddDynamicEffectTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDynamicEffectTemplateResp.Merge(dst, src)
}
func (m *AddDynamicEffectTemplateResp) XXX_Size() int {
	return xxx_messageInfo_AddDynamicEffectTemplateResp.Size(m)
}
func (m *AddDynamicEffectTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDynamicEffectTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddDynamicEffectTemplateResp proto.InternalMessageInfo

type UpdateDynamicEffectTemplateReq struct {
	TemplateInfo         *DynamicEffectTemplate `protobuf:"bytes,1,opt,name=template_info,json=templateInfo,proto3" json:"template_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *UpdateDynamicEffectTemplateReq) Reset()         { *m = UpdateDynamicEffectTemplateReq{} }
func (m *UpdateDynamicEffectTemplateReq) String() string { return proto.CompactTextString(m) }
func (*UpdateDynamicEffectTemplateReq) ProtoMessage()    {}
func (*UpdateDynamicEffectTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{69}
}
func (m *UpdateDynamicEffectTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDynamicEffectTemplateReq.Unmarshal(m, b)
}
func (m *UpdateDynamicEffectTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDynamicEffectTemplateReq.Marshal(b, m, deterministic)
}
func (dst *UpdateDynamicEffectTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDynamicEffectTemplateReq.Merge(dst, src)
}
func (m *UpdateDynamicEffectTemplateReq) XXX_Size() int {
	return xxx_messageInfo_UpdateDynamicEffectTemplateReq.Size(m)
}
func (m *UpdateDynamicEffectTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDynamicEffectTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDynamicEffectTemplateReq proto.InternalMessageInfo

func (m *UpdateDynamicEffectTemplateReq) GetTemplateInfo() *DynamicEffectTemplate {
	if m != nil {
		return m.TemplateInfo
	}
	return nil
}

type UpdateDynamicEffectTemplateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDynamicEffectTemplateResp) Reset()         { *m = UpdateDynamicEffectTemplateResp{} }
func (m *UpdateDynamicEffectTemplateResp) String() string { return proto.CompactTextString(m) }
func (*UpdateDynamicEffectTemplateResp) ProtoMessage()    {}
func (*UpdateDynamicEffectTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{70}
}
func (m *UpdateDynamicEffectTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDynamicEffectTemplateResp.Unmarshal(m, b)
}
func (m *UpdateDynamicEffectTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDynamicEffectTemplateResp.Marshal(b, m, deterministic)
}
func (dst *UpdateDynamicEffectTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDynamicEffectTemplateResp.Merge(dst, src)
}
func (m *UpdateDynamicEffectTemplateResp) XXX_Size() int {
	return xxx_messageInfo_UpdateDynamicEffectTemplateResp.Size(m)
}
func (m *UpdateDynamicEffectTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDynamicEffectTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDynamicEffectTemplateResp proto.InternalMessageInfo

type DelDynamicEffectTemplateReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDynamicEffectTemplateReq) Reset()         { *m = DelDynamicEffectTemplateReq{} }
func (m *DelDynamicEffectTemplateReq) String() string { return proto.CompactTextString(m) }
func (*DelDynamicEffectTemplateReq) ProtoMessage()    {}
func (*DelDynamicEffectTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{71}
}
func (m *DelDynamicEffectTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDynamicEffectTemplateReq.Unmarshal(m, b)
}
func (m *DelDynamicEffectTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDynamicEffectTemplateReq.Marshal(b, m, deterministic)
}
func (dst *DelDynamicEffectTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDynamicEffectTemplateReq.Merge(dst, src)
}
func (m *DelDynamicEffectTemplateReq) XXX_Size() int {
	return xxx_messageInfo_DelDynamicEffectTemplateReq.Size(m)
}
func (m *DelDynamicEffectTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDynamicEffectTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelDynamicEffectTemplateReq proto.InternalMessageInfo

func (m *DelDynamicEffectTemplateReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelDynamicEffectTemplateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDynamicEffectTemplateResp) Reset()         { *m = DelDynamicEffectTemplateResp{} }
func (m *DelDynamicEffectTemplateResp) String() string { return proto.CompactTextString(m) }
func (*DelDynamicEffectTemplateResp) ProtoMessage()    {}
func (*DelDynamicEffectTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{72}
}
func (m *DelDynamicEffectTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDynamicEffectTemplateResp.Unmarshal(m, b)
}
func (m *DelDynamicEffectTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDynamicEffectTemplateResp.Marshal(b, m, deterministic)
}
func (dst *DelDynamicEffectTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDynamicEffectTemplateResp.Merge(dst, src)
}
func (m *DelDynamicEffectTemplateResp) XXX_Size() int {
	return xxx_messageInfo_DelDynamicEffectTemplateResp.Size(m)
}
func (m *DelDynamicEffectTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDynamicEffectTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelDynamicEffectTemplateResp proto.InternalMessageInfo

type GetDynamicEffectTemplateListReq struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDynamicEffectTemplateListReq) Reset()         { *m = GetDynamicEffectTemplateListReq{} }
func (m *GetDynamicEffectTemplateListReq) String() string { return proto.CompactTextString(m) }
func (*GetDynamicEffectTemplateListReq) ProtoMessage()    {}
func (*GetDynamicEffectTemplateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{73}
}
func (m *GetDynamicEffectTemplateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDynamicEffectTemplateListReq.Unmarshal(m, b)
}
func (m *GetDynamicEffectTemplateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDynamicEffectTemplateListReq.Marshal(b, m, deterministic)
}
func (dst *GetDynamicEffectTemplateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDynamicEffectTemplateListReq.Merge(dst, src)
}
func (m *GetDynamicEffectTemplateListReq) XXX_Size() int {
	return xxx_messageInfo_GetDynamicEffectTemplateListReq.Size(m)
}
func (m *GetDynamicEffectTemplateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDynamicEffectTemplateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDynamicEffectTemplateListReq proto.InternalMessageInfo

func (m *GetDynamicEffectTemplateListReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetDynamicEffectTemplateListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetDynamicEffectTemplateListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetDynamicEffectTemplateListResp struct {
	InfoList             []*DynamicEffectTemplate `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	TotalCnt             uint32                   `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	NextOffset           uint32                   `protobuf:"varint,3,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetDynamicEffectTemplateListResp) Reset()         { *m = GetDynamicEffectTemplateListResp{} }
func (m *GetDynamicEffectTemplateListResp) String() string { return proto.CompactTextString(m) }
func (*GetDynamicEffectTemplateListResp) ProtoMessage()    {}
func (*GetDynamicEffectTemplateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{74}
}
func (m *GetDynamicEffectTemplateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDynamicEffectTemplateListResp.Unmarshal(m, b)
}
func (m *GetDynamicEffectTemplateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDynamicEffectTemplateListResp.Marshal(b, m, deterministic)
}
func (dst *GetDynamicEffectTemplateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDynamicEffectTemplateListResp.Merge(dst, src)
}
func (m *GetDynamicEffectTemplateListResp) XXX_Size() int {
	return xxx_messageInfo_GetDynamicEffectTemplateListResp.Size(m)
}
func (m *GetDynamicEffectTemplateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDynamicEffectTemplateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDynamicEffectTemplateListResp proto.InternalMessageInfo

func (m *GetDynamicEffectTemplateListResp) GetInfoList() []*DynamicEffectTemplate {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetDynamicEffectTemplateListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *GetDynamicEffectTemplateListResp) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

// 礼物动效模板配置
type PresentEffectTemplateConfig struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PresentId            uint32   `protobuf:"varint,2,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	PresentCnt           uint32   `protobuf:"varint,3,opt,name=present_cnt,json=presentCnt,proto3" json:"present_cnt,omitempty"`
	TemplateId           uint32   `protobuf:"varint,4,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	OpAccount            string   `protobuf:"bytes,5,opt,name=op_account,json=opAccount,proto3" json:"op_account,omitempty"`
	UpdateTs             uint32   `protobuf:"varint,6,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentEffectTemplateConfig) Reset()         { *m = PresentEffectTemplateConfig{} }
func (m *PresentEffectTemplateConfig) String() string { return proto.CompactTextString(m) }
func (*PresentEffectTemplateConfig) ProtoMessage()    {}
func (*PresentEffectTemplateConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{75}
}
func (m *PresentEffectTemplateConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectTemplateConfig.Unmarshal(m, b)
}
func (m *PresentEffectTemplateConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectTemplateConfig.Marshal(b, m, deterministic)
}
func (dst *PresentEffectTemplateConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectTemplateConfig.Merge(dst, src)
}
func (m *PresentEffectTemplateConfig) XXX_Size() int {
	return xxx_messageInfo_PresentEffectTemplateConfig.Size(m)
}
func (m *PresentEffectTemplateConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectTemplateConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectTemplateConfig proto.InternalMessageInfo

func (m *PresentEffectTemplateConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PresentEffectTemplateConfig) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *PresentEffectTemplateConfig) GetPresentCnt() uint32 {
	if m != nil {
		return m.PresentCnt
	}
	return 0
}

func (m *PresentEffectTemplateConfig) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *PresentEffectTemplateConfig) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

func (m *PresentEffectTemplateConfig) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

type AddPresentEffectTemplateConfigReq struct {
	Info                 *PresentEffectTemplateConfig `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *AddPresentEffectTemplateConfigReq) Reset()         { *m = AddPresentEffectTemplateConfigReq{} }
func (m *AddPresentEffectTemplateConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddPresentEffectTemplateConfigReq) ProtoMessage()    {}
func (*AddPresentEffectTemplateConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{76}
}
func (m *AddPresentEffectTemplateConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentEffectTemplateConfigReq.Unmarshal(m, b)
}
func (m *AddPresentEffectTemplateConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentEffectTemplateConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddPresentEffectTemplateConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentEffectTemplateConfigReq.Merge(dst, src)
}
func (m *AddPresentEffectTemplateConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddPresentEffectTemplateConfigReq.Size(m)
}
func (m *AddPresentEffectTemplateConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentEffectTemplateConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentEffectTemplateConfigReq proto.InternalMessageInfo

func (m *AddPresentEffectTemplateConfigReq) GetInfo() *PresentEffectTemplateConfig {
	if m != nil {
		return m.Info
	}
	return nil
}

type AddPresentEffectTemplateConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPresentEffectTemplateConfigResp) Reset()         { *m = AddPresentEffectTemplateConfigResp{} }
func (m *AddPresentEffectTemplateConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddPresentEffectTemplateConfigResp) ProtoMessage()    {}
func (*AddPresentEffectTemplateConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{77}
}
func (m *AddPresentEffectTemplateConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentEffectTemplateConfigResp.Unmarshal(m, b)
}
func (m *AddPresentEffectTemplateConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentEffectTemplateConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddPresentEffectTemplateConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentEffectTemplateConfigResp.Merge(dst, src)
}
func (m *AddPresentEffectTemplateConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddPresentEffectTemplateConfigResp.Size(m)
}
func (m *AddPresentEffectTemplateConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentEffectTemplateConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentEffectTemplateConfigResp proto.InternalMessageInfo

type UpdatePresentEffectTemplateConfigReq struct {
	Info                 *PresentEffectTemplateConfig `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *UpdatePresentEffectTemplateConfigReq) Reset()         { *m = UpdatePresentEffectTemplateConfigReq{} }
func (m *UpdatePresentEffectTemplateConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentEffectTemplateConfigReq) ProtoMessage()    {}
func (*UpdatePresentEffectTemplateConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{78}
}
func (m *UpdatePresentEffectTemplateConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentEffectTemplateConfigReq.Unmarshal(m, b)
}
func (m *UpdatePresentEffectTemplateConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentEffectTemplateConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentEffectTemplateConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentEffectTemplateConfigReq.Merge(dst, src)
}
func (m *UpdatePresentEffectTemplateConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentEffectTemplateConfigReq.Size(m)
}
func (m *UpdatePresentEffectTemplateConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentEffectTemplateConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentEffectTemplateConfigReq proto.InternalMessageInfo

func (m *UpdatePresentEffectTemplateConfigReq) GetInfo() *PresentEffectTemplateConfig {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdatePresentEffectTemplateConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentEffectTemplateConfigResp) Reset()         { *m = UpdatePresentEffectTemplateConfigResp{} }
func (m *UpdatePresentEffectTemplateConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentEffectTemplateConfigResp) ProtoMessage()    {}
func (*UpdatePresentEffectTemplateConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{79}
}
func (m *UpdatePresentEffectTemplateConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentEffectTemplateConfigResp.Unmarshal(m, b)
}
func (m *UpdatePresentEffectTemplateConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentEffectTemplateConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentEffectTemplateConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentEffectTemplateConfigResp.Merge(dst, src)
}
func (m *UpdatePresentEffectTemplateConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentEffectTemplateConfigResp.Size(m)
}
func (m *UpdatePresentEffectTemplateConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentEffectTemplateConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentEffectTemplateConfigResp proto.InternalMessageInfo

type DelPresentEffectTemplateConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentEffectTemplateConfigReq) Reset()         { *m = DelPresentEffectTemplateConfigReq{} }
func (m *DelPresentEffectTemplateConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelPresentEffectTemplateConfigReq) ProtoMessage()    {}
func (*DelPresentEffectTemplateConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{80}
}
func (m *DelPresentEffectTemplateConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentEffectTemplateConfigReq.Unmarshal(m, b)
}
func (m *DelPresentEffectTemplateConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentEffectTemplateConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelPresentEffectTemplateConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentEffectTemplateConfigReq.Merge(dst, src)
}
func (m *DelPresentEffectTemplateConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelPresentEffectTemplateConfigReq.Size(m)
}
func (m *DelPresentEffectTemplateConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentEffectTemplateConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentEffectTemplateConfigReq proto.InternalMessageInfo

func (m *DelPresentEffectTemplateConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelPresentEffectTemplateConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentEffectTemplateConfigResp) Reset()         { *m = DelPresentEffectTemplateConfigResp{} }
func (m *DelPresentEffectTemplateConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelPresentEffectTemplateConfigResp) ProtoMessage()    {}
func (*DelPresentEffectTemplateConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{81}
}
func (m *DelPresentEffectTemplateConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentEffectTemplateConfigResp.Unmarshal(m, b)
}
func (m *DelPresentEffectTemplateConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentEffectTemplateConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelPresentEffectTemplateConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentEffectTemplateConfigResp.Merge(dst, src)
}
func (m *DelPresentEffectTemplateConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelPresentEffectTemplateConfigResp.Size(m)
}
func (m *DelPresentEffectTemplateConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentEffectTemplateConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentEffectTemplateConfigResp proto.InternalMessageInfo

type GetPresentEffectTemplateConfigListReq struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	TemplateId           uint32   `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentEffectTemplateConfigListReq) Reset()         { *m = GetPresentEffectTemplateConfigListReq{} }
func (m *GetPresentEffectTemplateConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentEffectTemplateConfigListReq) ProtoMessage()    {}
func (*GetPresentEffectTemplateConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{82}
}
func (m *GetPresentEffectTemplateConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentEffectTemplateConfigListReq.Unmarshal(m, b)
}
func (m *GetPresentEffectTemplateConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentEffectTemplateConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentEffectTemplateConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentEffectTemplateConfigListReq.Merge(dst, src)
}
func (m *GetPresentEffectTemplateConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentEffectTemplateConfigListReq.Size(m)
}
func (m *GetPresentEffectTemplateConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentEffectTemplateConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentEffectTemplateConfigListReq proto.InternalMessageInfo

func (m *GetPresentEffectTemplateConfigListReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *GetPresentEffectTemplateConfigListReq) GetTemplateId() uint32 {
	if m != nil {
		return m.TemplateId
	}
	return 0
}

func (m *GetPresentEffectTemplateConfigListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPresentEffectTemplateConfigListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetPresentEffectTemplateConfigListResp struct {
	ConfigList           []*PresentEffectTemplateConfig `protobuf:"bytes,1,rep,name=config_list,json=configList,proto3" json:"config_list,omitempty"`
	TotalCnt             uint32                         `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	NextOffset           uint32                         `protobuf:"varint,3,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *GetPresentEffectTemplateConfigListResp) Reset() {
	*m = GetPresentEffectTemplateConfigListResp{}
}
func (m *GetPresentEffectTemplateConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentEffectTemplateConfigListResp) ProtoMessage()    {}
func (*GetPresentEffectTemplateConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{83}
}
func (m *GetPresentEffectTemplateConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentEffectTemplateConfigListResp.Unmarshal(m, b)
}
func (m *GetPresentEffectTemplateConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentEffectTemplateConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentEffectTemplateConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentEffectTemplateConfigListResp.Merge(dst, src)
}
func (m *GetPresentEffectTemplateConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentEffectTemplateConfigListResp.Size(m)
}
func (m *GetPresentEffectTemplateConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentEffectTemplateConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentEffectTemplateConfigListResp proto.InternalMessageInfo

func (m *GetPresentEffectTemplateConfigListResp) GetConfigList() []*PresentEffectTemplateConfig {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

func (m *GetPresentEffectTemplateConfigListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *GetPresentEffectTemplateConfigListResp) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

// 获取动效模板配置和礼物动效配置
type GetPresentDynamicEffectTemplateConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentDynamicEffectTemplateConfigReq) Reset() {
	*m = GetPresentDynamicEffectTemplateConfigReq{}
}
func (m *GetPresentDynamicEffectTemplateConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentDynamicEffectTemplateConfigReq) ProtoMessage()    {}
func (*GetPresentDynamicEffectTemplateConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{84}
}
func (m *GetPresentDynamicEffectTemplateConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentDynamicEffectTemplateConfigReq.Unmarshal(m, b)
}
func (m *GetPresentDynamicEffectTemplateConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentDynamicEffectTemplateConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentDynamicEffectTemplateConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentDynamicEffectTemplateConfigReq.Merge(dst, src)
}
func (m *GetPresentDynamicEffectTemplateConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentDynamicEffectTemplateConfigReq.Size(m)
}
func (m *GetPresentDynamicEffectTemplateConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentDynamicEffectTemplateConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentDynamicEffectTemplateConfigReq proto.InternalMessageInfo

type GetPresentDynamicEffectTemplateConfigResp struct {
	TemplateList         []*DynamicEffectTemplate       `protobuf:"bytes,1,rep,name=template_list,json=templateList,proto3" json:"template_list,omitempty"`
	PresentEffectList    []*PresentEffectTemplateConfig `protobuf:"bytes,2,rep,name=present_effect_list,json=presentEffectList,proto3" json:"present_effect_list,omitempty"`
	InfoVersion          uint32                         `protobuf:"varint,3,opt,name=info_version,json=infoVersion,proto3" json:"info_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *GetPresentDynamicEffectTemplateConfigResp) Reset() {
	*m = GetPresentDynamicEffectTemplateConfigResp{}
}
func (m *GetPresentDynamicEffectTemplateConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentDynamicEffectTemplateConfigResp) ProtoMessage()    {}
func (*GetPresentDynamicEffectTemplateConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{85}
}
func (m *GetPresentDynamicEffectTemplateConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentDynamicEffectTemplateConfigResp.Unmarshal(m, b)
}
func (m *GetPresentDynamicEffectTemplateConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentDynamicEffectTemplateConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentDynamicEffectTemplateConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentDynamicEffectTemplateConfigResp.Merge(dst, src)
}
func (m *GetPresentDynamicEffectTemplateConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentDynamicEffectTemplateConfigResp.Size(m)
}
func (m *GetPresentDynamicEffectTemplateConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentDynamicEffectTemplateConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentDynamicEffectTemplateConfigResp proto.InternalMessageInfo

func (m *GetPresentDynamicEffectTemplateConfigResp) GetTemplateList() []*DynamicEffectTemplate {
	if m != nil {
		return m.TemplateList
	}
	return nil
}

func (m *GetPresentDynamicEffectTemplateConfigResp) GetPresentEffectList() []*PresentEffectTemplateConfig {
	if m != nil {
		return m.PresentEffectList
	}
	return nil
}

func (m *GetPresentDynamicEffectTemplateConfigResp) GetInfoVersion() uint32 {
	if m != nil {
		return m.InfoVersion
	}
	return 0
}

// 获取非全屏礼物动效模板配置的更新时间
type GetPresentDynamicTemplateConfUpdateTimeReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentDynamicTemplateConfUpdateTimeReq) Reset() {
	*m = GetPresentDynamicTemplateConfUpdateTimeReq{}
}
func (m *GetPresentDynamicTemplateConfUpdateTimeReq) String() string {
	return proto.CompactTextString(m)
}
func (*GetPresentDynamicTemplateConfUpdateTimeReq) ProtoMessage() {}
func (*GetPresentDynamicTemplateConfUpdateTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{86}
}
func (m *GetPresentDynamicTemplateConfUpdateTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeReq.Unmarshal(m, b)
}
func (m *GetPresentDynamicTemplateConfUpdateTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentDynamicTemplateConfUpdateTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeReq.Merge(dst, src)
}
func (m *GetPresentDynamicTemplateConfUpdateTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeReq.Size(m)
}
func (m *GetPresentDynamicTemplateConfUpdateTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeReq proto.InternalMessageInfo

type GetPresentDynamicTemplateConfUpdateTimeResp struct {
	UpdateTs             uint32   `protobuf:"varint,1,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentDynamicTemplateConfUpdateTimeResp) Reset() {
	*m = GetPresentDynamicTemplateConfUpdateTimeResp{}
}
func (m *GetPresentDynamicTemplateConfUpdateTimeResp) String() string {
	return proto.CompactTextString(m)
}
func (*GetPresentDynamicTemplateConfUpdateTimeResp) ProtoMessage() {}
func (*GetPresentDynamicTemplateConfUpdateTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{87}
}
func (m *GetPresentDynamicTemplateConfUpdateTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeResp.Unmarshal(m, b)
}
func (m *GetPresentDynamicTemplateConfUpdateTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentDynamicTemplateConfUpdateTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeResp.Merge(dst, src)
}
func (m *GetPresentDynamicTemplateConfUpdateTimeResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeResp.Size(m)
}
func (m *GetPresentDynamicTemplateConfUpdateTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentDynamicTemplateConfUpdateTimeResp proto.InternalMessageInfo

func (m *GetPresentDynamicTemplateConfUpdateTimeResp) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

// 获取指定礼物动效配置
type GetPresentDETConfigByIdReq struct {
	PresentId            uint32   `protobuf:"varint,1,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentDETConfigByIdReq) Reset()         { *m = GetPresentDETConfigByIdReq{} }
func (m *GetPresentDETConfigByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentDETConfigByIdReq) ProtoMessage()    {}
func (*GetPresentDETConfigByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{88}
}
func (m *GetPresentDETConfigByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentDETConfigByIdReq.Unmarshal(m, b)
}
func (m *GetPresentDETConfigByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentDETConfigByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentDETConfigByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentDETConfigByIdReq.Merge(dst, src)
}
func (m *GetPresentDETConfigByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentDETConfigByIdReq.Size(m)
}
func (m *GetPresentDETConfigByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentDETConfigByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentDETConfigByIdReq proto.InternalMessageInfo

func (m *GetPresentDETConfigByIdReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

type GetPresentDETConfigByIdResp struct {
	PresentEffectList    []*PresentEffectTemplateConfig `protobuf:"bytes,1,rep,name=present_effect_list,json=presentEffectList,proto3" json:"present_effect_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *GetPresentDETConfigByIdResp) Reset()         { *m = GetPresentDETConfigByIdResp{} }
func (m *GetPresentDETConfigByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentDETConfigByIdResp) ProtoMessage()    {}
func (*GetPresentDETConfigByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{89}
}
func (m *GetPresentDETConfigByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentDETConfigByIdResp.Unmarshal(m, b)
}
func (m *GetPresentDETConfigByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentDETConfigByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentDETConfigByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentDETConfigByIdResp.Merge(dst, src)
}
func (m *GetPresentDETConfigByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentDETConfigByIdResp.Size(m)
}
func (m *GetPresentDETConfigByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentDETConfigByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentDETConfigByIdResp proto.InternalMessageInfo

func (m *GetPresentDETConfigByIdResp) GetPresentEffectList() []*PresentEffectTemplateConfig {
	if m != nil {
		return m.PresentEffectList
	}
	return nil
}

// 获取礼物流光配置
type GetPresentFlowConfigByIdReq struct {
	FlowId               uint32   `protobuf:"varint,1,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentFlowConfigByIdReq) Reset()         { *m = GetPresentFlowConfigByIdReq{} }
func (m *GetPresentFlowConfigByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlowConfigByIdReq) ProtoMessage()    {}
func (*GetPresentFlowConfigByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{90}
}
func (m *GetPresentFlowConfigByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFlowConfigByIdReq.Unmarshal(m, b)
}
func (m *GetPresentFlowConfigByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFlowConfigByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentFlowConfigByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFlowConfigByIdReq.Merge(dst, src)
}
func (m *GetPresentFlowConfigByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentFlowConfigByIdReq.Size(m)
}
func (m *GetPresentFlowConfigByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFlowConfigByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFlowConfigByIdReq proto.InternalMessageInfo

func (m *GetPresentFlowConfigByIdReq) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

type GetPresentFlowConfigByIdResp struct {
	FlowConfig           *StPresentFlowConfig `protobuf:"bytes,1,opt,name=flow_config,json=flowConfig,proto3" json:"flow_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPresentFlowConfigByIdResp) Reset()         { *m = GetPresentFlowConfigByIdResp{} }
func (m *GetPresentFlowConfigByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlowConfigByIdResp) ProtoMessage()    {}
func (*GetPresentFlowConfigByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{91}
}
func (m *GetPresentFlowConfigByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFlowConfigByIdResp.Unmarshal(m, b)
}
func (m *GetPresentFlowConfigByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFlowConfigByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentFlowConfigByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFlowConfigByIdResp.Merge(dst, src)
}
func (m *GetPresentFlowConfigByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentFlowConfigByIdResp.Size(m)
}
func (m *GetPresentFlowConfigByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFlowConfigByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFlowConfigByIdResp proto.InternalMessageInfo

func (m *GetPresentFlowConfigByIdResp) GetFlowConfig() *StPresentFlowConfig {
	if m != nil {
		return m.FlowConfig
	}
	return nil
}

// 获取礼物流光配置
type GetPresentFlowConfigListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentFlowConfigListReq) Reset()         { *m = GetPresentFlowConfigListReq{} }
func (m *GetPresentFlowConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlowConfigListReq) ProtoMessage()    {}
func (*GetPresentFlowConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{92}
}
func (m *GetPresentFlowConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFlowConfigListReq.Unmarshal(m, b)
}
func (m *GetPresentFlowConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFlowConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentFlowConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFlowConfigListReq.Merge(dst, src)
}
func (m *GetPresentFlowConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentFlowConfigListReq.Size(m)
}
func (m *GetPresentFlowConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFlowConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFlowConfigListReq proto.InternalMessageInfo

type GetPresentFlowConfigListResp struct {
	FlowList             []*StPresentFlowConfig `protobuf:"bytes,1,rep,name=flow_list,json=flowList,proto3" json:"flow_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPresentFlowConfigListResp) Reset()         { *m = GetPresentFlowConfigListResp{} }
func (m *GetPresentFlowConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlowConfigListResp) ProtoMessage()    {}
func (*GetPresentFlowConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{93}
}
func (m *GetPresentFlowConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFlowConfigListResp.Unmarshal(m, b)
}
func (m *GetPresentFlowConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFlowConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentFlowConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFlowConfigListResp.Merge(dst, src)
}
func (m *GetPresentFlowConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentFlowConfigListResp.Size(m)
}
func (m *GetPresentFlowConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFlowConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFlowConfigListResp proto.InternalMessageInfo

func (m *GetPresentFlowConfigListResp) GetFlowList() []*StPresentFlowConfig {
	if m != nil {
		return m.FlowList
	}
	return nil
}

// 获取礼物流光配置的更新时间
type GetPresentFlowConfigUpdateTimeReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentFlowConfigUpdateTimeReq) Reset()         { *m = GetPresentFlowConfigUpdateTimeReq{} }
func (m *GetPresentFlowConfigUpdateTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlowConfigUpdateTimeReq) ProtoMessage()    {}
func (*GetPresentFlowConfigUpdateTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{94}
}
func (m *GetPresentFlowConfigUpdateTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFlowConfigUpdateTimeReq.Unmarshal(m, b)
}
func (m *GetPresentFlowConfigUpdateTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFlowConfigUpdateTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentFlowConfigUpdateTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFlowConfigUpdateTimeReq.Merge(dst, src)
}
func (m *GetPresentFlowConfigUpdateTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentFlowConfigUpdateTimeReq.Size(m)
}
func (m *GetPresentFlowConfigUpdateTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFlowConfigUpdateTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFlowConfigUpdateTimeReq proto.InternalMessageInfo

type GetPresentFlowConfigUpdateTimeResp struct {
	UpdateTime           uint32   `protobuf:"varint,1,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentFlowConfigUpdateTimeResp) Reset()         { *m = GetPresentFlowConfigUpdateTimeResp{} }
func (m *GetPresentFlowConfigUpdateTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlowConfigUpdateTimeResp) ProtoMessage()    {}
func (*GetPresentFlowConfigUpdateTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{95}
}
func (m *GetPresentFlowConfigUpdateTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFlowConfigUpdateTimeResp.Unmarshal(m, b)
}
func (m *GetPresentFlowConfigUpdateTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFlowConfigUpdateTimeResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentFlowConfigUpdateTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFlowConfigUpdateTimeResp.Merge(dst, src)
}
func (m *GetPresentFlowConfigUpdateTimeResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentFlowConfigUpdateTimeResp.Size(m)
}
func (m *GetPresentFlowConfigUpdateTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFlowConfigUpdateTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFlowConfigUpdateTimeResp proto.InternalMessageInfo

func (m *GetPresentFlowConfigUpdateTimeResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 增加礼物流光配置
type AddPresentFlowConfigReq struct {
	FlowUrl              string   `protobuf:"bytes,1,opt,name=flow_url,json=flowUrl,proto3" json:"flow_url,omitempty"`
	FlowMd5              string   `protobuf:"bytes,2,opt,name=flow_md5,json=flowMd5,proto3" json:"flow_md5,omitempty"`
	FlowDesc             string   `protobuf:"bytes,3,opt,name=flow_desc,json=flowDesc,proto3" json:"flow_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPresentFlowConfigReq) Reset()         { *m = AddPresentFlowConfigReq{} }
func (m *AddPresentFlowConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddPresentFlowConfigReq) ProtoMessage()    {}
func (*AddPresentFlowConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{96}
}
func (m *AddPresentFlowConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentFlowConfigReq.Unmarshal(m, b)
}
func (m *AddPresentFlowConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentFlowConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddPresentFlowConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentFlowConfigReq.Merge(dst, src)
}
func (m *AddPresentFlowConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddPresentFlowConfigReq.Size(m)
}
func (m *AddPresentFlowConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentFlowConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentFlowConfigReq proto.InternalMessageInfo

func (m *AddPresentFlowConfigReq) GetFlowUrl() string {
	if m != nil {
		return m.FlowUrl
	}
	return ""
}

func (m *AddPresentFlowConfigReq) GetFlowMd5() string {
	if m != nil {
		return m.FlowMd5
	}
	return ""
}

func (m *AddPresentFlowConfigReq) GetFlowDesc() string {
	if m != nil {
		return m.FlowDesc
	}
	return ""
}

// 删除礼物流光配置
type DelPresentFlowConfigReq struct {
	FlowId               uint32   `protobuf:"varint,1,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentFlowConfigReq) Reset()         { *m = DelPresentFlowConfigReq{} }
func (m *DelPresentFlowConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelPresentFlowConfigReq) ProtoMessage()    {}
func (*DelPresentFlowConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{97}
}
func (m *DelPresentFlowConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentFlowConfigReq.Unmarshal(m, b)
}
func (m *DelPresentFlowConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentFlowConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelPresentFlowConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentFlowConfigReq.Merge(dst, src)
}
func (m *DelPresentFlowConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelPresentFlowConfigReq.Size(m)
}
func (m *DelPresentFlowConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentFlowConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentFlowConfigReq proto.InternalMessageInfo

func (m *DelPresentFlowConfigReq) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

type UpdatePresentFlowConfigReq struct {
	FlowId               uint32   `protobuf:"varint,1,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	FlowUrl              string   `protobuf:"bytes,2,opt,name=flow_url,json=flowUrl,proto3" json:"flow_url,omitempty"`
	FlowMd5              string   `protobuf:"bytes,3,opt,name=flow_md5,json=flowMd5,proto3" json:"flow_md5,omitempty"`
	FlowDesc             string   `protobuf:"bytes,4,opt,name=flow_desc,json=flowDesc,proto3" json:"flow_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentFlowConfigReq) Reset()         { *m = UpdatePresentFlowConfigReq{} }
func (m *UpdatePresentFlowConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentFlowConfigReq) ProtoMessage()    {}
func (*UpdatePresentFlowConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{98}
}
func (m *UpdatePresentFlowConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentFlowConfigReq.Unmarshal(m, b)
}
func (m *UpdatePresentFlowConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentFlowConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentFlowConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentFlowConfigReq.Merge(dst, src)
}
func (m *UpdatePresentFlowConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentFlowConfigReq.Size(m)
}
func (m *UpdatePresentFlowConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentFlowConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentFlowConfigReq proto.InternalMessageInfo

func (m *UpdatePresentFlowConfigReq) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *UpdatePresentFlowConfigReq) GetFlowUrl() string {
	if m != nil {
		return m.FlowUrl
	}
	return ""
}

func (m *UpdatePresentFlowConfigReq) GetFlowMd5() string {
	if m != nil {
		return m.FlowMd5
	}
	return ""
}

func (m *UpdatePresentFlowConfigReq) GetFlowDesc() string {
	if m != nil {
		return m.FlowDesc
	}
	return ""
}

type AddPresentFlowConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPresentFlowConfigResp) Reset()         { *m = AddPresentFlowConfigResp{} }
func (m *AddPresentFlowConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddPresentFlowConfigResp) ProtoMessage()    {}
func (*AddPresentFlowConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{99}
}
func (m *AddPresentFlowConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentFlowConfigResp.Unmarshal(m, b)
}
func (m *AddPresentFlowConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentFlowConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddPresentFlowConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentFlowConfigResp.Merge(dst, src)
}
func (m *AddPresentFlowConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddPresentFlowConfigResp.Size(m)
}
func (m *AddPresentFlowConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentFlowConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentFlowConfigResp proto.InternalMessageInfo

type DelPresentFlowConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentFlowConfigResp) Reset()         { *m = DelPresentFlowConfigResp{} }
func (m *DelPresentFlowConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelPresentFlowConfigResp) ProtoMessage()    {}
func (*DelPresentFlowConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{100}
}
func (m *DelPresentFlowConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentFlowConfigResp.Unmarshal(m, b)
}
func (m *DelPresentFlowConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentFlowConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelPresentFlowConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentFlowConfigResp.Merge(dst, src)
}
func (m *DelPresentFlowConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelPresentFlowConfigResp.Size(m)
}
func (m *DelPresentFlowConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentFlowConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentFlowConfigResp proto.InternalMessageInfo

type UpdatePresentFlowConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentFlowConfigResp) Reset()         { *m = UpdatePresentFlowConfigResp{} }
func (m *UpdatePresentFlowConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentFlowConfigResp) ProtoMessage()    {}
func (*UpdatePresentFlowConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{101}
}
func (m *UpdatePresentFlowConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentFlowConfigResp.Unmarshal(m, b)
}
func (m *UpdatePresentFlowConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentFlowConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentFlowConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentFlowConfigResp.Merge(dst, src)
}
func (m *UpdatePresentFlowConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentFlowConfigResp.Size(m)
}
func (m *UpdatePresentFlowConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentFlowConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentFlowConfigResp proto.InternalMessageInfo

// 礼物流光配置
type StPresentFlowConfig struct {
	FlowId               uint32   `protobuf:"varint,1,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Desc                 string   `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StPresentFlowConfig) Reset()         { *m = StPresentFlowConfig{} }
func (m *StPresentFlowConfig) String() string { return proto.CompactTextString(m) }
func (*StPresentFlowConfig) ProtoMessage()    {}
func (*StPresentFlowConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{102}
}
func (m *StPresentFlowConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPresentFlowConfig.Unmarshal(m, b)
}
func (m *StPresentFlowConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPresentFlowConfig.Marshal(b, m, deterministic)
}
func (dst *StPresentFlowConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPresentFlowConfig.Merge(dst, src)
}
func (m *StPresentFlowConfig) XXX_Size() int {
	return xxx_messageInfo_StPresentFlowConfig.Size(m)
}
func (m *StPresentFlowConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_StPresentFlowConfig.DiscardUnknown(m)
}

var xxx_messageInfo_StPresentFlowConfig proto.InternalMessageInfo

func (m *StPresentFlowConfig) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *StPresentFlowConfig) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *StPresentFlowConfig) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *StPresentFlowConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *StPresentFlowConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StPresentFlowConfig) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type AddChanceItemSourceReq struct {
	SourceType           uint32   `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	PlayType             uint32   `protobuf:"varint,2,opt,name=play_type,json=playType,proto3" json:"play_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChanceItemSourceReq) Reset()         { *m = AddChanceItemSourceReq{} }
func (m *AddChanceItemSourceReq) String() string { return proto.CompactTextString(m) }
func (*AddChanceItemSourceReq) ProtoMessage()    {}
func (*AddChanceItemSourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{103}
}
func (m *AddChanceItemSourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChanceItemSourceReq.Unmarshal(m, b)
}
func (m *AddChanceItemSourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChanceItemSourceReq.Marshal(b, m, deterministic)
}
func (dst *AddChanceItemSourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChanceItemSourceReq.Merge(dst, src)
}
func (m *AddChanceItemSourceReq) XXX_Size() int {
	return xxx_messageInfo_AddChanceItemSourceReq.Size(m)
}
func (m *AddChanceItemSourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChanceItemSourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChanceItemSourceReq proto.InternalMessageInfo

func (m *AddChanceItemSourceReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *AddChanceItemSourceReq) GetPlayType() uint32 {
	if m != nil {
		return m.PlayType
	}
	return 0
}

type AddChanceItemSourceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChanceItemSourceResp) Reset()         { *m = AddChanceItemSourceResp{} }
func (m *AddChanceItemSourceResp) String() string { return proto.CompactTextString(m) }
func (*AddChanceItemSourceResp) ProtoMessage()    {}
func (*AddChanceItemSourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{104}
}
func (m *AddChanceItemSourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChanceItemSourceResp.Unmarshal(m, b)
}
func (m *AddChanceItemSourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChanceItemSourceResp.Marshal(b, m, deterministic)
}
func (dst *AddChanceItemSourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChanceItemSourceResp.Merge(dst, src)
}
func (m *AddChanceItemSourceResp) XXX_Size() int {
	return xxx_messageInfo_AddChanceItemSourceResp.Size(m)
}
func (m *AddChanceItemSourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChanceItemSourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChanceItemSourceResp proto.InternalMessageInfo

type DelChanceItemSourceReq struct {
	SourceType           uint32   `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChanceItemSourceReq) Reset()         { *m = DelChanceItemSourceReq{} }
func (m *DelChanceItemSourceReq) String() string { return proto.CompactTextString(m) }
func (*DelChanceItemSourceReq) ProtoMessage()    {}
func (*DelChanceItemSourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{105}
}
func (m *DelChanceItemSourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChanceItemSourceReq.Unmarshal(m, b)
}
func (m *DelChanceItemSourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChanceItemSourceReq.Marshal(b, m, deterministic)
}
func (dst *DelChanceItemSourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChanceItemSourceReq.Merge(dst, src)
}
func (m *DelChanceItemSourceReq) XXX_Size() int {
	return xxx_messageInfo_DelChanceItemSourceReq.Size(m)
}
func (m *DelChanceItemSourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChanceItemSourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelChanceItemSourceReq proto.InternalMessageInfo

func (m *DelChanceItemSourceReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

type DelChanceItemSourceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChanceItemSourceResp) Reset()         { *m = DelChanceItemSourceResp{} }
func (m *DelChanceItemSourceResp) String() string { return proto.CompactTextString(m) }
func (*DelChanceItemSourceResp) ProtoMessage()    {}
func (*DelChanceItemSourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{106}
}
func (m *DelChanceItemSourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChanceItemSourceResp.Unmarshal(m, b)
}
func (m *DelChanceItemSourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChanceItemSourceResp.Marshal(b, m, deterministic)
}
func (dst *DelChanceItemSourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChanceItemSourceResp.Merge(dst, src)
}
func (m *DelChanceItemSourceResp) XXX_Size() int {
	return xxx_messageInfo_DelChanceItemSourceResp.Size(m)
}
func (m *DelChanceItemSourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChanceItemSourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelChanceItemSourceResp proto.InternalMessageInfo

type GetOrderLogByOrderIdsReq struct {
	OrderIds             []string `protobuf:"bytes,1,rep,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderLogByOrderIdsReq) Reset()         { *m = GetOrderLogByOrderIdsReq{} }
func (m *GetOrderLogByOrderIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderLogByOrderIdsReq) ProtoMessage()    {}
func (*GetOrderLogByOrderIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{107}
}
func (m *GetOrderLogByOrderIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderLogByOrderIdsReq.Unmarshal(m, b)
}
func (m *GetOrderLogByOrderIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderLogByOrderIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderLogByOrderIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderLogByOrderIdsReq.Merge(dst, src)
}
func (m *GetOrderLogByOrderIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderLogByOrderIdsReq.Size(m)
}
func (m *GetOrderLogByOrderIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderLogByOrderIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderLogByOrderIdsReq proto.InternalMessageInfo

func (m *GetOrderLogByOrderIdsReq) GetOrderIds() []string {
	if m != nil {
		return m.OrderIds
	}
	return nil
}

type GetOrderLogByOrderIdsResp struct {
	OrderLogList         []*StUserPresentOrderLog `protobuf:"bytes,1,rep,name=order_log_list,json=orderLogList,proto3" json:"order_log_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetOrderLogByOrderIdsResp) Reset()         { *m = GetOrderLogByOrderIdsResp{} }
func (m *GetOrderLogByOrderIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderLogByOrderIdsResp) ProtoMessage()    {}
func (*GetOrderLogByOrderIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{108}
}
func (m *GetOrderLogByOrderIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderLogByOrderIdsResp.Unmarshal(m, b)
}
func (m *GetOrderLogByOrderIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderLogByOrderIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderLogByOrderIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderLogByOrderIdsResp.Merge(dst, src)
}
func (m *GetOrderLogByOrderIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderLogByOrderIdsResp.Size(m)
}
func (m *GetOrderLogByOrderIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderLogByOrderIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderLogByOrderIdsResp proto.InternalMessageInfo

func (m *GetOrderLogByOrderIdsResp) GetOrderLogList() []*StUserPresentOrderLog {
	if m != nil {
		return m.OrderLogList
	}
	return nil
}

type StUserPresentOrderLog struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChangeScore          uint32   `protobuf:"varint,3,opt,name=change_score,json=changeScore,proto3" json:"change_score,omitempty"`
	OrderId              string   `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	DealToken            string   `protobuf:"bytes,6,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	ScoreType            uint32   `protobuf:"varint,7,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StUserPresentOrderLog) Reset()         { *m = StUserPresentOrderLog{} }
func (m *StUserPresentOrderLog) String() string { return proto.CompactTextString(m) }
func (*StUserPresentOrderLog) ProtoMessage()    {}
func (*StUserPresentOrderLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{109}
}
func (m *StUserPresentOrderLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StUserPresentOrderLog.Unmarshal(m, b)
}
func (m *StUserPresentOrderLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StUserPresentOrderLog.Marshal(b, m, deterministic)
}
func (dst *StUserPresentOrderLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StUserPresentOrderLog.Merge(dst, src)
}
func (m *StUserPresentOrderLog) XXX_Size() int {
	return xxx_messageInfo_StUserPresentOrderLog.Size(m)
}
func (m *StUserPresentOrderLog) XXX_DiscardUnknown() {
	xxx_messageInfo_StUserPresentOrderLog.DiscardUnknown(m)
}

var xxx_messageInfo_StUserPresentOrderLog proto.InternalMessageInfo

func (m *StUserPresentOrderLog) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *StUserPresentOrderLog) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *StUserPresentOrderLog) GetChangeScore() uint32 {
	if m != nil {
		return m.ChangeScore
	}
	return 0
}

func (m *StUserPresentOrderLog) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *StUserPresentOrderLog) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StUserPresentOrderLog) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *StUserPresentOrderLog) GetScoreType() uint32 {
	if m != nil {
		return m.ScoreType
	}
	return 0
}

type BatchSendPresentReq struct {
	PresentList          []*SendPresentItem `protobuf:"bytes,1,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchSendPresentReq) Reset()         { *m = BatchSendPresentReq{} }
func (m *BatchSendPresentReq) String() string { return proto.CompactTextString(m) }
func (*BatchSendPresentReq) ProtoMessage()    {}
func (*BatchSendPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{110}
}
func (m *BatchSendPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSendPresentReq.Unmarshal(m, b)
}
func (m *BatchSendPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSendPresentReq.Marshal(b, m, deterministic)
}
func (dst *BatchSendPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSendPresentReq.Merge(dst, src)
}
func (m *BatchSendPresentReq) XXX_Size() int {
	return xxx_messageInfo_BatchSendPresentReq.Size(m)
}
func (m *BatchSendPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSendPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSendPresentReq proto.InternalMessageInfo

func (m *BatchSendPresentReq) GetPresentList() []*SendPresentItem {
	if m != nil {
		return m.PresentList
	}
	return nil
}

type BatchSendPresentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSendPresentResp) Reset()         { *m = BatchSendPresentResp{} }
func (m *BatchSendPresentResp) String() string { return proto.CompactTextString(m) }
func (*BatchSendPresentResp) ProtoMessage()    {}
func (*BatchSendPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{111}
}
func (m *BatchSendPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSendPresentResp.Unmarshal(m, b)
}
func (m *BatchSendPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSendPresentResp.Marshal(b, m, deterministic)
}
func (dst *BatchSendPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSendPresentResp.Merge(dst, src)
}
func (m *BatchSendPresentResp) XXX_Size() int {
	return xxx_messageInfo_BatchSendPresentResp.Size(m)
}
func (m *BatchSendPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSendPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSendPresentResp proto.InternalMessageInfo

type SendPresentItem struct {
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32               `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	OrderId              string               `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ItemId               uint32               `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ChannelId            uint32               `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GuildId              uint32               `protobuf:"varint,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ItemCount            uint32               `protobuf:"varint,7,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	AddCharm             uint32               `protobuf:"varint,8,opt,name=add_charm,json=addCharm,proto3" json:"add_charm,omitempty"`
	SendTime             uint32               `protobuf:"varint,9,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ItemConfig           *StPresentItemConfig `protobuf:"bytes,10,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	OptInvalid           bool                 `protobuf:"varint,11,opt,name=opt_invalid,json=optInvalid,proto3" json:"opt_invalid,omitempty"`
	AsyncFlag            bool                 `protobuf:"varint,12,opt,name=async_flag,json=asyncFlag,proto3" json:"async_flag,omitempty"`
	UserFromIp           string               `protobuf:"bytes,13,opt,name=user_from_ip,json=userFromIp,proto3" json:"user_from_ip,omitempty"`
	ChannelType          uint32               `protobuf:"varint,14,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ItemSource           uint32               `protobuf:"varint,15,opt,name=item_source,json=itemSource,proto3" json:"item_source,omitempty"`
	ChannelName          string               `protobuf:"bytes,16,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelDisplayId     uint32               `protobuf:"varint,17,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	SendSource           uint32               `protobuf:"varint,18,opt,name=send_source,json=sendSource,proto3" json:"send_source,omitempty"`
	SendPlatform         uint32               `protobuf:"varint,19,opt,name=send_platform,json=sendPlatform,proto3" json:"send_platform,omitempty"`
	BatchType            uint32               `protobuf:"varint,20,opt,name=batch_type,json=batchType,proto3" json:"batch_type,omitempty"`
	AppId                uint32               `protobuf:"varint,21,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32               `protobuf:"varint,22,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ReceiverGuildId      uint32               `protobuf:"varint,23,opt,name=receiver_guild_id,json=receiverGuildId,proto3" json:"receiver_guild_id,omitempty"`
	GiverGuildId         uint32               `protobuf:"varint,24,opt,name=giver_guild_id,json=giverGuildId,proto3" json:"giver_guild_id,omitempty"`
	AddRich              uint32               `protobuf:"varint,25,opt,name=add_rich,json=addRich,proto3" json:"add_rich,omitempty"`
	SendMethod           uint32               `protobuf:"varint,26,opt,name=send_method,json=sendMethod,proto3" json:"send_method,omitempty"`
	BindChannelId        uint32               `protobuf:"varint,27,opt,name=bind_channel_id,json=bindChannelId,proto3" json:"bind_channel_id,omitempty"`
	DealToken            string               `protobuf:"bytes,28,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	FromUkwAccount       string               `protobuf:"bytes,29,opt,name=from_ukw_account,json=fromUkwAccount,proto3" json:"from_ukw_account,omitempty"`
	FromUkwNickname      string               `protobuf:"bytes,30,opt,name=from_ukw_nickname,json=fromUkwNickname,proto3" json:"from_ukw_nickname,omitempty"`
	ToUkwAccount         string               `protobuf:"bytes,31,opt,name=to_ukw_account,json=toUkwAccount,proto3" json:"to_ukw_account,omitempty"`
	ToUkwNickname        string               `protobuf:"bytes,32,opt,name=to_ukw_nickname,json=toUkwNickname,proto3" json:"to_ukw_nickname,omitempty"`
	ChannelGameId        uint32               `protobuf:"varint,33,opt,name=channel_game_id,json=channelGameId,proto3" json:"channel_game_id,omitempty"`
	IsVirtualLive        bool                 `protobuf:"varint,34,opt,name=is_virtual_live,json=isVirtualLive,proto3" json:"is_virtual_live,omitempty"`
	ScoreType            uint32               `protobuf:"varint,35,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SendPresentItem) Reset()         { *m = SendPresentItem{} }
func (m *SendPresentItem) String() string { return proto.CompactTextString(m) }
func (*SendPresentItem) ProtoMessage()    {}
func (*SendPresentItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{112}
}
func (m *SendPresentItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPresentItem.Unmarshal(m, b)
}
func (m *SendPresentItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPresentItem.Marshal(b, m, deterministic)
}
func (dst *SendPresentItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPresentItem.Merge(dst, src)
}
func (m *SendPresentItem) XXX_Size() int {
	return xxx_messageInfo_SendPresentItem.Size(m)
}
func (m *SendPresentItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPresentItem.DiscardUnknown(m)
}

var xxx_messageInfo_SendPresentItem proto.InternalMessageInfo

func (m *SendPresentItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendPresentItem) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendPresentItem) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SendPresentItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SendPresentItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendPresentItem) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SendPresentItem) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *SendPresentItem) GetAddCharm() uint32 {
	if m != nil {
		return m.AddCharm
	}
	return 0
}

func (m *SendPresentItem) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *SendPresentItem) GetItemConfig() *StPresentItemConfig {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

func (m *SendPresentItem) GetOptInvalid() bool {
	if m != nil {
		return m.OptInvalid
	}
	return false
}

func (m *SendPresentItem) GetAsyncFlag() bool {
	if m != nil {
		return m.AsyncFlag
	}
	return false
}

func (m *SendPresentItem) GetUserFromIp() string {
	if m != nil {
		return m.UserFromIp
	}
	return ""
}

func (m *SendPresentItem) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SendPresentItem) GetItemSource() uint32 {
	if m != nil {
		return m.ItemSource
	}
	return 0
}

func (m *SendPresentItem) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *SendPresentItem) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *SendPresentItem) GetSendSource() uint32 {
	if m != nil {
		return m.SendSource
	}
	return 0
}

func (m *SendPresentItem) GetSendPlatform() uint32 {
	if m != nil {
		return m.SendPlatform
	}
	return 0
}

func (m *SendPresentItem) GetBatchType() uint32 {
	if m != nil {
		return m.BatchType
	}
	return 0
}

func (m *SendPresentItem) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SendPresentItem) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SendPresentItem) GetReceiverGuildId() uint32 {
	if m != nil {
		return m.ReceiverGuildId
	}
	return 0
}

func (m *SendPresentItem) GetGiverGuildId() uint32 {
	if m != nil {
		return m.GiverGuildId
	}
	return 0
}

func (m *SendPresentItem) GetAddRich() uint32 {
	if m != nil {
		return m.AddRich
	}
	return 0
}

func (m *SendPresentItem) GetSendMethod() uint32 {
	if m != nil {
		return m.SendMethod
	}
	return 0
}

func (m *SendPresentItem) GetBindChannelId() uint32 {
	if m != nil {
		return m.BindChannelId
	}
	return 0
}

func (m *SendPresentItem) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *SendPresentItem) GetFromUkwAccount() string {
	if m != nil {
		return m.FromUkwAccount
	}
	return ""
}

func (m *SendPresentItem) GetFromUkwNickname() string {
	if m != nil {
		return m.FromUkwNickname
	}
	return ""
}

func (m *SendPresentItem) GetToUkwAccount() string {
	if m != nil {
		return m.ToUkwAccount
	}
	return ""
}

func (m *SendPresentItem) GetToUkwNickname() string {
	if m != nil {
		return m.ToUkwNickname
	}
	return ""
}

func (m *SendPresentItem) GetChannelGameId() uint32 {
	if m != nil {
		return m.ChannelGameId
	}
	return 0
}

func (m *SendPresentItem) GetIsVirtualLive() bool {
	if m != nil {
		return m.IsVirtualLive
	}
	return false
}

func (m *SendPresentItem) GetScoreType() uint32 {
	if m != nil {
		return m.ScoreType
	}
	return 0
}

type AddPresentMarkConfigReq struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	MarkUrl              string   `protobuf:"bytes,2,opt,name=mark_url,json=markUrl,proto3" json:"mark_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPresentMarkConfigReq) Reset()         { *m = AddPresentMarkConfigReq{} }
func (m *AddPresentMarkConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddPresentMarkConfigReq) ProtoMessage()    {}
func (*AddPresentMarkConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{113}
}
func (m *AddPresentMarkConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentMarkConfigReq.Unmarshal(m, b)
}
func (m *AddPresentMarkConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentMarkConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddPresentMarkConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentMarkConfigReq.Merge(dst, src)
}
func (m *AddPresentMarkConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddPresentMarkConfigReq.Size(m)
}
func (m *AddPresentMarkConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentMarkConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentMarkConfigReq proto.InternalMessageInfo

func (m *AddPresentMarkConfigReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AddPresentMarkConfigReq) GetMarkUrl() string {
	if m != nil {
		return m.MarkUrl
	}
	return ""
}

type AddPresentMarkConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPresentMarkConfigResp) Reset()         { *m = AddPresentMarkConfigResp{} }
func (m *AddPresentMarkConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddPresentMarkConfigResp) ProtoMessage()    {}
func (*AddPresentMarkConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{114}
}
func (m *AddPresentMarkConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentMarkConfigResp.Unmarshal(m, b)
}
func (m *AddPresentMarkConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentMarkConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddPresentMarkConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentMarkConfigResp.Merge(dst, src)
}
func (m *AddPresentMarkConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddPresentMarkConfigResp.Size(m)
}
func (m *AddPresentMarkConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentMarkConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentMarkConfigResp proto.InternalMessageInfo

type BatchAddPresentMarkConfigReq struct {
	MarkList             []*PresentMarkConfigItem `protobuf:"bytes,1,rep,name=mark_list,json=markList,proto3" json:"mark_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchAddPresentMarkConfigReq) Reset()         { *m = BatchAddPresentMarkConfigReq{} }
func (m *BatchAddPresentMarkConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddPresentMarkConfigReq) ProtoMessage()    {}
func (*BatchAddPresentMarkConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{115}
}
func (m *BatchAddPresentMarkConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddPresentMarkConfigReq.Unmarshal(m, b)
}
func (m *BatchAddPresentMarkConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddPresentMarkConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddPresentMarkConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddPresentMarkConfigReq.Merge(dst, src)
}
func (m *BatchAddPresentMarkConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddPresentMarkConfigReq.Size(m)
}
func (m *BatchAddPresentMarkConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddPresentMarkConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddPresentMarkConfigReq proto.InternalMessageInfo

func (m *BatchAddPresentMarkConfigReq) GetMarkList() []*PresentMarkConfigItem {
	if m != nil {
		return m.MarkList
	}
	return nil
}

type PresentMarkConfigItem struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	MarkUrl              string   `protobuf:"bytes,2,opt,name=mark_url,json=markUrl,proto3" json:"mark_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentMarkConfigItem) Reset()         { *m = PresentMarkConfigItem{} }
func (m *PresentMarkConfigItem) String() string { return proto.CompactTextString(m) }
func (*PresentMarkConfigItem) ProtoMessage()    {}
func (*PresentMarkConfigItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{116}
}
func (m *PresentMarkConfigItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentMarkConfigItem.Unmarshal(m, b)
}
func (m *PresentMarkConfigItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentMarkConfigItem.Marshal(b, m, deterministic)
}
func (dst *PresentMarkConfigItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentMarkConfigItem.Merge(dst, src)
}
func (m *PresentMarkConfigItem) XXX_Size() int {
	return xxx_messageInfo_PresentMarkConfigItem.Size(m)
}
func (m *PresentMarkConfigItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentMarkConfigItem.DiscardUnknown(m)
}

var xxx_messageInfo_PresentMarkConfigItem proto.InternalMessageInfo

func (m *PresentMarkConfigItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PresentMarkConfigItem) GetMarkUrl() string {
	if m != nil {
		return m.MarkUrl
	}
	return ""
}

type BatchAddPresentMarkConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddPresentMarkConfigResp) Reset()         { *m = BatchAddPresentMarkConfigResp{} }
func (m *BatchAddPresentMarkConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddPresentMarkConfigResp) ProtoMessage()    {}
func (*BatchAddPresentMarkConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{117}
}
func (m *BatchAddPresentMarkConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddPresentMarkConfigResp.Unmarshal(m, b)
}
func (m *BatchAddPresentMarkConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddPresentMarkConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddPresentMarkConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddPresentMarkConfigResp.Merge(dst, src)
}
func (m *BatchAddPresentMarkConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddPresentMarkConfigResp.Size(m)
}
func (m *BatchAddPresentMarkConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddPresentMarkConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddPresentMarkConfigResp proto.InternalMessageInfo

type GetPresentMarkConfigListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentMarkConfigListReq) Reset()         { *m = GetPresentMarkConfigListReq{} }
func (m *GetPresentMarkConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentMarkConfigListReq) ProtoMessage()    {}
func (*GetPresentMarkConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{118}
}
func (m *GetPresentMarkConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentMarkConfigListReq.Unmarshal(m, b)
}
func (m *GetPresentMarkConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentMarkConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentMarkConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentMarkConfigListReq.Merge(dst, src)
}
func (m *GetPresentMarkConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentMarkConfigListReq.Size(m)
}
func (m *GetPresentMarkConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentMarkConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentMarkConfigListReq proto.InternalMessageInfo

type GetPresentMarkConfigListResp struct {
	MarkList             []*StPresentMarkConfig `protobuf:"bytes,1,rep,name=mark_list,json=markList,proto3" json:"mark_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetPresentMarkConfigListResp) Reset()         { *m = GetPresentMarkConfigListResp{} }
func (m *GetPresentMarkConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentMarkConfigListResp) ProtoMessage()    {}
func (*GetPresentMarkConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{119}
}
func (m *GetPresentMarkConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentMarkConfigListResp.Unmarshal(m, b)
}
func (m *GetPresentMarkConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentMarkConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentMarkConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentMarkConfigListResp.Merge(dst, src)
}
func (m *GetPresentMarkConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentMarkConfigListResp.Size(m)
}
func (m *GetPresentMarkConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentMarkConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentMarkConfigListResp proto.InternalMessageInfo

func (m *GetPresentMarkConfigListResp) GetMarkList() []*StPresentMarkConfig {
	if m != nil {
		return m.MarkList
	}
	return nil
}

type DelPresentMarkConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentMarkConfigReq) Reset()         { *m = DelPresentMarkConfigReq{} }
func (m *DelPresentMarkConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelPresentMarkConfigReq) ProtoMessage()    {}
func (*DelPresentMarkConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{120}
}
func (m *DelPresentMarkConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentMarkConfigReq.Unmarshal(m, b)
}
func (m *DelPresentMarkConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentMarkConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelPresentMarkConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentMarkConfigReq.Merge(dst, src)
}
func (m *DelPresentMarkConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelPresentMarkConfigReq.Size(m)
}
func (m *DelPresentMarkConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentMarkConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentMarkConfigReq proto.InternalMessageInfo

func (m *DelPresentMarkConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelPresentMarkConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentMarkConfigResp) Reset()         { *m = DelPresentMarkConfigResp{} }
func (m *DelPresentMarkConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelPresentMarkConfigResp) ProtoMessage()    {}
func (*DelPresentMarkConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{121}
}
func (m *DelPresentMarkConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentMarkConfigResp.Unmarshal(m, b)
}
func (m *DelPresentMarkConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentMarkConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelPresentMarkConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentMarkConfigResp.Merge(dst, src)
}
func (m *DelPresentMarkConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelPresentMarkConfigResp.Size(m)
}
func (m *DelPresentMarkConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentMarkConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentMarkConfigResp proto.InternalMessageInfo

type GetPresentMarkIconByPresentIdReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentMarkIconByPresentIdReq) Reset()         { *m = GetPresentMarkIconByPresentIdReq{} }
func (m *GetPresentMarkIconByPresentIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentMarkIconByPresentIdReq) ProtoMessage()    {}
func (*GetPresentMarkIconByPresentIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{122}
}
func (m *GetPresentMarkIconByPresentIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentMarkIconByPresentIdReq.Unmarshal(m, b)
}
func (m *GetPresentMarkIconByPresentIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentMarkIconByPresentIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentMarkIconByPresentIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentMarkIconByPresentIdReq.Merge(dst, src)
}
func (m *GetPresentMarkIconByPresentIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentMarkIconByPresentIdReq.Size(m)
}
func (m *GetPresentMarkIconByPresentIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentMarkIconByPresentIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentMarkIconByPresentIdReq proto.InternalMessageInfo

func (m *GetPresentMarkIconByPresentIdReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetPresentMarkIconByPresentIdResp struct {
	OriginIconUrl        string               `protobuf:"bytes,1,opt,name=origin_icon_url,json=originIconUrl,proto3" json:"origin_icon_url,omitempty"`
	MarkConfig           *StPresentMarkConfig `protobuf:"bytes,2,opt,name=mark_config,json=markConfig,proto3" json:"mark_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPresentMarkIconByPresentIdResp) Reset()         { *m = GetPresentMarkIconByPresentIdResp{} }
func (m *GetPresentMarkIconByPresentIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentMarkIconByPresentIdResp) ProtoMessage()    {}
func (*GetPresentMarkIconByPresentIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{123}
}
func (m *GetPresentMarkIconByPresentIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentMarkIconByPresentIdResp.Unmarshal(m, b)
}
func (m *GetPresentMarkIconByPresentIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentMarkIconByPresentIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentMarkIconByPresentIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentMarkIconByPresentIdResp.Merge(dst, src)
}
func (m *GetPresentMarkIconByPresentIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentMarkIconByPresentIdResp.Size(m)
}
func (m *GetPresentMarkIconByPresentIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentMarkIconByPresentIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentMarkIconByPresentIdResp proto.InternalMessageInfo

func (m *GetPresentMarkIconByPresentIdResp) GetOriginIconUrl() string {
	if m != nil {
		return m.OriginIconUrl
	}
	return ""
}

func (m *GetPresentMarkIconByPresentIdResp) GetMarkConfig() *StPresentMarkConfig {
	if m != nil {
		return m.MarkConfig
	}
	return nil
}

type StPresentMarkConfig struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MarkUrl              string   `protobuf:"bytes,3,opt,name=mark_url,json=markUrl,proto3" json:"mark_url,omitempty"`
	UpdateTime           uint64   `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StPresentMarkConfig) Reset()         { *m = StPresentMarkConfig{} }
func (m *StPresentMarkConfig) String() string { return proto.CompactTextString(m) }
func (*StPresentMarkConfig) ProtoMessage()    {}
func (*StPresentMarkConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{124}
}
func (m *StPresentMarkConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPresentMarkConfig.Unmarshal(m, b)
}
func (m *StPresentMarkConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPresentMarkConfig.Marshal(b, m, deterministic)
}
func (dst *StPresentMarkConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPresentMarkConfig.Merge(dst, src)
}
func (m *StPresentMarkConfig) XXX_Size() int {
	return xxx_messageInfo_StPresentMarkConfig.Size(m)
}
func (m *StPresentMarkConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_StPresentMarkConfig.DiscardUnknown(m)
}

var xxx_messageInfo_StPresentMarkConfig proto.InternalMessageInfo

func (m *StPresentMarkConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StPresentMarkConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StPresentMarkConfig) GetMarkUrl() string {
	if m != nil {
		return m.MarkUrl
	}
	return ""
}

func (m *StPresentMarkConfig) GetUpdateTime() uint64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type FellowPresentConfig struct {
	UniqueBackgroundUrl  string   `protobuf:"bytes,1,opt,name=unique_background_url,json=uniqueBackgroundUrl,proto3" json:"unique_background_url,omitempty"`
	UniqueSourceType     uint32   `protobuf:"varint,2,opt,name=unique_source_type,json=uniqueSourceType,proto3" json:"unique_source_type,omitempty"`
	UniqueMd5            string   `protobuf:"bytes,3,opt,name=unique_md5,json=uniqueMd5,proto3" json:"unique_md5,omitempty"`
	MultiBackgroundUrl   string   `protobuf:"bytes,4,opt,name=multi_background_url,json=multiBackgroundUrl,proto3" json:"multi_background_url,omitempty"`
	MultiSourceType      uint32   `protobuf:"varint,5,opt,name=multi_source_type,json=multiSourceType,proto3" json:"multi_source_type,omitempty"`
	MultiMd5             string   `protobuf:"bytes,6,opt,name=multi_md5,json=multiMd5,proto3" json:"multi_md5,omitempty"`
	UniqueBackgroundImg  string   `protobuf:"bytes,7,opt,name=unique_background_img,json=uniqueBackgroundImg,proto3" json:"unique_background_img,omitempty"`
	MultiBackgroundImg   string   `protobuf:"bytes,8,opt,name=multi_background_img,json=multiBackgroundImg,proto3" json:"multi_background_img,omitempty"`
	SourceZip            string   `protobuf:"bytes,9,opt,name=source_zip,json=sourceZip,proto3" json:"source_zip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowPresentConfig) Reset()         { *m = FellowPresentConfig{} }
func (m *FellowPresentConfig) String() string { return proto.CompactTextString(m) }
func (*FellowPresentConfig) ProtoMessage()    {}
func (*FellowPresentConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{125}
}
func (m *FellowPresentConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowPresentConfig.Unmarshal(m, b)
}
func (m *FellowPresentConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowPresentConfig.Marshal(b, m, deterministic)
}
func (dst *FellowPresentConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowPresentConfig.Merge(dst, src)
}
func (m *FellowPresentConfig) XXX_Size() int {
	return xxx_messageInfo_FellowPresentConfig.Size(m)
}
func (m *FellowPresentConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowPresentConfig.DiscardUnknown(m)
}

var xxx_messageInfo_FellowPresentConfig proto.InternalMessageInfo

func (m *FellowPresentConfig) GetUniqueBackgroundUrl() string {
	if m != nil {
		return m.UniqueBackgroundUrl
	}
	return ""
}

func (m *FellowPresentConfig) GetUniqueSourceType() uint32 {
	if m != nil {
		return m.UniqueSourceType
	}
	return 0
}

func (m *FellowPresentConfig) GetUniqueMd5() string {
	if m != nil {
		return m.UniqueMd5
	}
	return ""
}

func (m *FellowPresentConfig) GetMultiBackgroundUrl() string {
	if m != nil {
		return m.MultiBackgroundUrl
	}
	return ""
}

func (m *FellowPresentConfig) GetMultiSourceType() uint32 {
	if m != nil {
		return m.MultiSourceType
	}
	return 0
}

func (m *FellowPresentConfig) GetMultiMd5() string {
	if m != nil {
		return m.MultiMd5
	}
	return ""
}

func (m *FellowPresentConfig) GetUniqueBackgroundImg() string {
	if m != nil {
		return m.UniqueBackgroundImg
	}
	return ""
}

func (m *FellowPresentConfig) GetMultiBackgroundImg() string {
	if m != nil {
		return m.MultiBackgroundImg
	}
	return ""
}

func (m *FellowPresentConfig) GetSourceZip() string {
	if m != nil {
		return m.SourceZip
	}
	return ""
}

// 根据礼物id获取礼物配置
type GetPresentConfigByIdBackendReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigByIdBackendReq) Reset()         { *m = GetPresentConfigByIdBackendReq{} }
func (m *GetPresentConfigByIdBackendReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdBackendReq) ProtoMessage()    {}
func (*GetPresentConfigByIdBackendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{126}
}
func (m *GetPresentConfigByIdBackendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdBackendReq.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdBackendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdBackendReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdBackendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdBackendReq.Merge(dst, src)
}
func (m *GetPresentConfigByIdBackendReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdBackendReq.Size(m)
}
func (m *GetPresentConfigByIdBackendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdBackendReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdBackendReq proto.InternalMessageInfo

func (m *GetPresentConfigByIdBackendReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetPresentConfigByIdBackendResp struct {
	ItemConfig           *StPresentItemConfigBackend `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	TestingPresentId     uint32                      `protobuf:"varint,2,opt,name=testing_present_id,json=testingPresentId,proto3" json:"testing_present_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetPresentConfigByIdBackendResp) Reset()         { *m = GetPresentConfigByIdBackendResp{} }
func (m *GetPresentConfigByIdBackendResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigByIdBackendResp) ProtoMessage()    {}
func (*GetPresentConfigByIdBackendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{127}
}
func (m *GetPresentConfigByIdBackendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigByIdBackendResp.Unmarshal(m, b)
}
func (m *GetPresentConfigByIdBackendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigByIdBackendResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigByIdBackendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigByIdBackendResp.Merge(dst, src)
}
func (m *GetPresentConfigByIdBackendResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigByIdBackendResp.Size(m)
}
func (m *GetPresentConfigByIdBackendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigByIdBackendResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigByIdBackendResp proto.InternalMessageInfo

func (m *GetPresentConfigByIdBackendResp) GetItemConfig() *StPresentItemConfigBackend {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

func (m *GetPresentConfigByIdBackendResp) GetTestingPresentId() uint32 {
	if m != nil {
		return m.TestingPresentId
	}
	return 0
}

type GetPresentConfigListBackendReq struct {
	TypeBitmap           uint32   `protobuf:"varint,1,opt,name=type_bitmap,json=typeBitmap,proto3" json:"type_bitmap,omitempty"`
	GetAll               bool     `protobuf:"varint,2,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentConfigListBackendReq) Reset()         { *m = GetPresentConfigListBackendReq{} }
func (m *GetPresentConfigListBackendReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListBackendReq) ProtoMessage()    {}
func (*GetPresentConfigListBackendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{128}
}
func (m *GetPresentConfigListBackendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListBackendReq.Unmarshal(m, b)
}
func (m *GetPresentConfigListBackendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListBackendReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListBackendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListBackendReq.Merge(dst, src)
}
func (m *GetPresentConfigListBackendReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListBackendReq.Size(m)
}
func (m *GetPresentConfigListBackendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListBackendReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListBackendReq proto.InternalMessageInfo

func (m *GetPresentConfigListBackendReq) GetTypeBitmap() uint32 {
	if m != nil {
		return m.TypeBitmap
	}
	return 0
}

func (m *GetPresentConfigListBackendReq) GetGetAll() bool {
	if m != nil {
		return m.GetAll
	}
	return false
}

type GetPresentConfigListBackendResp struct {
	ItemList             []*StPresentItemConfigBackend `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetPresentConfigListBackendResp) Reset()         { *m = GetPresentConfigListBackendResp{} }
func (m *GetPresentConfigListBackendResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentConfigListBackendResp) ProtoMessage()    {}
func (*GetPresentConfigListBackendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{129}
}
func (m *GetPresentConfigListBackendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentConfigListBackendResp.Unmarshal(m, b)
}
func (m *GetPresentConfigListBackendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentConfigListBackendResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentConfigListBackendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentConfigListBackendResp.Merge(dst, src)
}
func (m *GetPresentConfigListBackendResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentConfigListBackendResp.Size(m)
}
func (m *GetPresentConfigListBackendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentConfigListBackendResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentConfigListBackendResp proto.InternalMessageInfo

func (m *GetPresentConfigListBackendResp) GetItemList() []*StPresentItemConfigBackend {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 增加礼物配置
type AddPresentConfigBackendReq struct {
	Name                 string                            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string                            `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32                            `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	EffectBegin          uint32                            `protobuf:"varint,4,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                            `protobuf:"varint,5,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	Rank                 uint32                            `protobuf:"varint,6,opt,name=rank,proto3" json:"rank,omitempty"`
	PriceType            uint32                            `protobuf:"varint,7,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	Extend               *StPresentItemConfigExtendBackend `protobuf:"bytes,8,opt,name=extend,proto3" json:"extend,omitempty"`
	Fellow               *FellowPresentConfig              `protobuf:"bytes,9,opt,name=fellow,proto3" json:"fellow,omitempty"`
	DelayInfo            []*EffectDelayLevelInfo           `protobuf:"bytes,10,rep,name=delay_info,json=delayInfo,proto3" json:"delay_info,omitempty"`
	RankFloat            float32                           `protobuf:"fixed32,11,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	TestingPresentId     uint32                            `protobuf:"varint,12,opt,name=testing_present_id,json=testingPresentId,proto3" json:"testing_present_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *AddPresentConfigBackendReq) Reset()         { *m = AddPresentConfigBackendReq{} }
func (m *AddPresentConfigBackendReq) String() string { return proto.CompactTextString(m) }
func (*AddPresentConfigBackendReq) ProtoMessage()    {}
func (*AddPresentConfigBackendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{130}
}
func (m *AddPresentConfigBackendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentConfigBackendReq.Unmarshal(m, b)
}
func (m *AddPresentConfigBackendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentConfigBackendReq.Marshal(b, m, deterministic)
}
func (dst *AddPresentConfigBackendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentConfigBackendReq.Merge(dst, src)
}
func (m *AddPresentConfigBackendReq) XXX_Size() int {
	return xxx_messageInfo_AddPresentConfigBackendReq.Size(m)
}
func (m *AddPresentConfigBackendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentConfigBackendReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentConfigBackendReq proto.InternalMessageInfo

func (m *AddPresentConfigBackendReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AddPresentConfigBackendReq) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *AddPresentConfigBackendReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *AddPresentConfigBackendReq) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *AddPresentConfigBackendReq) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *AddPresentConfigBackendReq) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *AddPresentConfigBackendReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *AddPresentConfigBackendReq) GetExtend() *StPresentItemConfigExtendBackend {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *AddPresentConfigBackendReq) GetFellow() *FellowPresentConfig {
	if m != nil {
		return m.Fellow
	}
	return nil
}

func (m *AddPresentConfigBackendReq) GetDelayInfo() []*EffectDelayLevelInfo {
	if m != nil {
		return m.DelayInfo
	}
	return nil
}

func (m *AddPresentConfigBackendReq) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

func (m *AddPresentConfigBackendReq) GetTestingPresentId() uint32 {
	if m != nil {
		return m.TestingPresentId
	}
	return 0
}

type EffectDelayLevelInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	SendCount            uint32   `protobuf:"varint,2,opt,name=send_count,json=sendCount,proto3" json:"send_count,omitempty"`
	DayCount             uint32   `protobuf:"varint,3,opt,name=day_count,json=dayCount,proto3" json:"day_count,omitempty"`
	ExpireDayCount       uint32   `protobuf:"varint,4,opt,name=expire_day_count,json=expireDayCount,proto3" json:"expire_day_count,omitempty"`
	NoticeDayCount       uint32   `protobuf:"varint,5,opt,name=notice_day_count,json=noticeDayCount,proto3" json:"notice_day_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EffectDelayLevelInfo) Reset()         { *m = EffectDelayLevelInfo{} }
func (m *EffectDelayLevelInfo) String() string { return proto.CompactTextString(m) }
func (*EffectDelayLevelInfo) ProtoMessage()    {}
func (*EffectDelayLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{131}
}
func (m *EffectDelayLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EffectDelayLevelInfo.Unmarshal(m, b)
}
func (m *EffectDelayLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EffectDelayLevelInfo.Marshal(b, m, deterministic)
}
func (dst *EffectDelayLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EffectDelayLevelInfo.Merge(dst, src)
}
func (m *EffectDelayLevelInfo) XXX_Size() int {
	return xxx_messageInfo_EffectDelayLevelInfo.Size(m)
}
func (m *EffectDelayLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EffectDelayLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EffectDelayLevelInfo proto.InternalMessageInfo

func (m *EffectDelayLevelInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetSendCount() uint32 {
	if m != nil {
		return m.SendCount
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetDayCount() uint32 {
	if m != nil {
		return m.DayCount
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetExpireDayCount() uint32 {
	if m != nil {
		return m.ExpireDayCount
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetNoticeDayCount() uint32 {
	if m != nil {
		return m.NoticeDayCount
	}
	return 0
}

type AddPresentConfigBackendResp struct {
	ItemConfig           *StPresentItemConfigBackend `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *AddPresentConfigBackendResp) Reset()         { *m = AddPresentConfigBackendResp{} }
func (m *AddPresentConfigBackendResp) String() string { return proto.CompactTextString(m) }
func (*AddPresentConfigBackendResp) ProtoMessage()    {}
func (*AddPresentConfigBackendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{132}
}
func (m *AddPresentConfigBackendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentConfigBackendResp.Unmarshal(m, b)
}
func (m *AddPresentConfigBackendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentConfigBackendResp.Marshal(b, m, deterministic)
}
func (dst *AddPresentConfigBackendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentConfigBackendResp.Merge(dst, src)
}
func (m *AddPresentConfigBackendResp) XXX_Size() int {
	return xxx_messageInfo_AddPresentConfigBackendResp.Size(m)
}
func (m *AddPresentConfigBackendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentConfigBackendResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentConfigBackendResp proto.InternalMessageInfo

func (m *AddPresentConfigBackendResp) GetItemConfig() *StPresentItemConfigBackend {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

// 删除礼物配置
type DelPresentConfigBackendReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentConfigBackendReq) Reset()         { *m = DelPresentConfigBackendReq{} }
func (m *DelPresentConfigBackendReq) String() string { return proto.CompactTextString(m) }
func (*DelPresentConfigBackendReq) ProtoMessage()    {}
func (*DelPresentConfigBackendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{133}
}
func (m *DelPresentConfigBackendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentConfigBackendReq.Unmarshal(m, b)
}
func (m *DelPresentConfigBackendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentConfigBackendReq.Marshal(b, m, deterministic)
}
func (dst *DelPresentConfigBackendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentConfigBackendReq.Merge(dst, src)
}
func (m *DelPresentConfigBackendReq) XXX_Size() int {
	return xxx_messageInfo_DelPresentConfigBackendReq.Size(m)
}
func (m *DelPresentConfigBackendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentConfigBackendReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentConfigBackendReq proto.InternalMessageInfo

func (m *DelPresentConfigBackendReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type DelPresentConfigBackendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentConfigBackendResp) Reset()         { *m = DelPresentConfigBackendResp{} }
func (m *DelPresentConfigBackendResp) String() string { return proto.CompactTextString(m) }
func (*DelPresentConfigBackendResp) ProtoMessage()    {}
func (*DelPresentConfigBackendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{134}
}
func (m *DelPresentConfigBackendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentConfigBackendResp.Unmarshal(m, b)
}
func (m *DelPresentConfigBackendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentConfigBackendResp.Marshal(b, m, deterministic)
}
func (dst *DelPresentConfigBackendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentConfigBackendResp.Merge(dst, src)
}
func (m *DelPresentConfigBackendResp) XXX_Size() int {
	return xxx_messageInfo_DelPresentConfigBackendResp.Size(m)
}
func (m *DelPresentConfigBackendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentConfigBackendResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentConfigBackendResp proto.InternalMessageInfo

// 更新礼物配置
type UpdatePresentConfigBackendReq struct {
	ItemConfig           *StPresentItemConfigBackend `protobuf:"bytes,1,opt,name=item_config,json=itemConfig,proto3" json:"item_config,omitempty"`
	TestingPresentId     uint32                      `protobuf:"varint,2,opt,name=testing_present_id,json=testingPresentId,proto3" json:"testing_present_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *UpdatePresentConfigBackendReq) Reset()         { *m = UpdatePresentConfigBackendReq{} }
func (m *UpdatePresentConfigBackendReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentConfigBackendReq) ProtoMessage()    {}
func (*UpdatePresentConfigBackendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{135}
}
func (m *UpdatePresentConfigBackendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentConfigBackendReq.Unmarshal(m, b)
}
func (m *UpdatePresentConfigBackendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentConfigBackendReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentConfigBackendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentConfigBackendReq.Merge(dst, src)
}
func (m *UpdatePresentConfigBackendReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentConfigBackendReq.Size(m)
}
func (m *UpdatePresentConfigBackendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentConfigBackendReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentConfigBackendReq proto.InternalMessageInfo

func (m *UpdatePresentConfigBackendReq) GetItemConfig() *StPresentItemConfigBackend {
	if m != nil {
		return m.ItemConfig
	}
	return nil
}

func (m *UpdatePresentConfigBackendReq) GetTestingPresentId() uint32 {
	if m != nil {
		return m.TestingPresentId
	}
	return 0
}

type UpdatePresentConfigBackendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentConfigBackendResp) Reset()         { *m = UpdatePresentConfigBackendResp{} }
func (m *UpdatePresentConfigBackendResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentConfigBackendResp) ProtoMessage()    {}
func (*UpdatePresentConfigBackendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{136}
}
func (m *UpdatePresentConfigBackendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentConfigBackendResp.Unmarshal(m, b)
}
func (m *UpdatePresentConfigBackendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentConfigBackendResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentConfigBackendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentConfigBackendResp.Merge(dst, src)
}
func (m *UpdatePresentConfigBackendResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentConfigBackendResp.Size(m)
}
func (m *UpdatePresentConfigBackendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentConfigBackendResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentConfigBackendResp proto.InternalMessageInfo

type UserPresentSend struct {
	ToUid                uint32   `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToTtid               uint32   `protobuf:"varint,2,opt,name=to_ttid,json=toTtid,proto3" json:"to_ttid,omitempty"`
	ToNickname           string   `protobuf:"bytes,3,opt,name=to_nickname,json=toNickname,proto3" json:"to_nickname,omitempty"`
	ItemId               uint32   `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName             string   `protobuf:"bytes,5,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemCount            uint32   `protobuf:"varint,6,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	AddRich              uint32   `protobuf:"varint,7,opt,name=add_rich,json=addRich,proto3" json:"add_rich,omitempty"`
	SendTime             uint32   `protobuf:"varint,8,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPresentSend) Reset()         { *m = UserPresentSend{} }
func (m *UserPresentSend) String() string { return proto.CompactTextString(m) }
func (*UserPresentSend) ProtoMessage()    {}
func (*UserPresentSend) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{137}
}
func (m *UserPresentSend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresentSend.Unmarshal(m, b)
}
func (m *UserPresentSend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresentSend.Marshal(b, m, deterministic)
}
func (dst *UserPresentSend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresentSend.Merge(dst, src)
}
func (m *UserPresentSend) XXX_Size() int {
	return xxx_messageInfo_UserPresentSend.Size(m)
}
func (m *UserPresentSend) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresentSend.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresentSend proto.InternalMessageInfo

func (m *UserPresentSend) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *UserPresentSend) GetToTtid() uint32 {
	if m != nil {
		return m.ToTtid
	}
	return 0
}

func (m *UserPresentSend) GetToNickname() string {
	if m != nil {
		return m.ToNickname
	}
	return ""
}

func (m *UserPresentSend) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UserPresentSend) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *UserPresentSend) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserPresentSend) GetAddRich() uint32 {
	if m != nil {
		return m.AddRich
	}
	return 0
}

func (m *UserPresentSend) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

type UserPresentReceive struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromTtid             uint32   `protobuf:"varint,2,opt,name=from_ttid,json=fromTtid,proto3" json:"from_ttid,omitempty"`
	FromNickname         string   `protobuf:"bytes,3,opt,name=from_nickname,json=fromNickname,proto3" json:"from_nickname,omitempty"`
	ItemId               uint32   `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName             string   `protobuf:"bytes,5,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemCount            uint32   `protobuf:"varint,6,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	AddCharm             uint32   `protobuf:"varint,7,opt,name=add_charm,json=addCharm,proto3" json:"add_charm,omitempty"`
	AddScore             uint32   `protobuf:"varint,8,opt,name=add_score,json=addScore,proto3" json:"add_score,omitempty"`
	ReceiveTime          uint32   `protobuf:"varint,9,opt,name=receive_time,json=receiveTime,proto3" json:"receive_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPresentReceive) Reset()         { *m = UserPresentReceive{} }
func (m *UserPresentReceive) String() string { return proto.CompactTextString(m) }
func (*UserPresentReceive) ProtoMessage()    {}
func (*UserPresentReceive) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{138}
}
func (m *UserPresentReceive) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresentReceive.Unmarshal(m, b)
}
func (m *UserPresentReceive) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresentReceive.Marshal(b, m, deterministic)
}
func (dst *UserPresentReceive) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresentReceive.Merge(dst, src)
}
func (m *UserPresentReceive) XXX_Size() int {
	return xxx_messageInfo_UserPresentReceive.Size(m)
}
func (m *UserPresentReceive) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresentReceive.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresentReceive proto.InternalMessageInfo

func (m *UserPresentReceive) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *UserPresentReceive) GetFromTtid() uint32 {
	if m != nil {
		return m.FromTtid
	}
	return 0
}

func (m *UserPresentReceive) GetFromNickname() string {
	if m != nil {
		return m.FromNickname
	}
	return ""
}

func (m *UserPresentReceive) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UserPresentReceive) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *UserPresentReceive) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserPresentReceive) GetAddCharm() uint32 {
	if m != nil {
		return m.AddCharm
	}
	return 0
}

func (m *UserPresentReceive) GetAddScore() uint32 {
	if m != nil {
		return m.AddScore
	}
	return 0
}

func (m *UserPresentReceive) GetReceiveTime() uint32 {
	if m != nil {
		return m.ReceiveTime
	}
	return 0
}

// 用户送礼查询
type GetUserPresentSendReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTs              uint32   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPresentSendReq) Reset()         { *m = GetUserPresentSendReq{} }
func (m *GetUserPresentSendReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentSendReq) ProtoMessage()    {}
func (*GetUserPresentSendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{139}
}
func (m *GetUserPresentSendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentSendReq.Unmarshal(m, b)
}
func (m *GetUserPresentSendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentSendReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentSendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentSendReq.Merge(dst, src)
}
func (m *GetUserPresentSendReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentSendReq.Size(m)
}
func (m *GetUserPresentSendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentSendReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentSendReq proto.InternalMessageInfo

func (m *GetUserPresentSendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPresentSendReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetUserPresentSendReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetUserPresentSendResp struct {
	PresentSendDetail    []*UserPresentSend `protobuf:"bytes,1,rep,name=present_send_detail,json=presentSendDetail,proto3" json:"present_send_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserPresentSendResp) Reset()         { *m = GetUserPresentSendResp{} }
func (m *GetUserPresentSendResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentSendResp) ProtoMessage()    {}
func (*GetUserPresentSendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{140}
}
func (m *GetUserPresentSendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentSendResp.Unmarshal(m, b)
}
func (m *GetUserPresentSendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentSendResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentSendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentSendResp.Merge(dst, src)
}
func (m *GetUserPresentSendResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentSendResp.Size(m)
}
func (m *GetUserPresentSendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentSendResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentSendResp proto.InternalMessageInfo

func (m *GetUserPresentSendResp) GetPresentSendDetail() []*UserPresentSend {
	if m != nil {
		return m.PresentSendDetail
	}
	return nil
}

// 用户送礼查询
type GetUserPresentReceiveReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTs              uint32   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPresentReceiveReq) Reset()         { *m = GetUserPresentReceiveReq{} }
func (m *GetUserPresentReceiveReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentReceiveReq) ProtoMessage()    {}
func (*GetUserPresentReceiveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{141}
}
func (m *GetUserPresentReceiveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentReceiveReq.Unmarshal(m, b)
}
func (m *GetUserPresentReceiveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentReceiveReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentReceiveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentReceiveReq.Merge(dst, src)
}
func (m *GetUserPresentReceiveReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentReceiveReq.Size(m)
}
func (m *GetUserPresentReceiveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentReceiveReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentReceiveReq proto.InternalMessageInfo

func (m *GetUserPresentReceiveReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPresentReceiveReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetUserPresentReceiveReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetUserPresentReceiveResp struct {
	PresentReceiveDetail []*UserPresentReceive `protobuf:"bytes,1,rep,name=present_receive_detail,json=presentReceiveDetail,proto3" json:"present_receive_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetUserPresentReceiveResp) Reset()         { *m = GetUserPresentReceiveResp{} }
func (m *GetUserPresentReceiveResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPresentReceiveResp) ProtoMessage()    {}
func (*GetUserPresentReceiveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{142}
}
func (m *GetUserPresentReceiveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPresentReceiveResp.Unmarshal(m, b)
}
func (m *GetUserPresentReceiveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPresentReceiveResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPresentReceiveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPresentReceiveResp.Merge(dst, src)
}
func (m *GetUserPresentReceiveResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPresentReceiveResp.Size(m)
}
func (m *GetUserPresentReceiveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPresentReceiveResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPresentReceiveResp proto.InternalMessageInfo

func (m *GetUserPresentReceiveResp) GetPresentReceiveDetail() []*UserPresentReceive {
	if m != nil {
		return m.PresentReceiveDetail
	}
	return nil
}

// 用户送礼查询
type GetAllFellowPresentReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllFellowPresentReq) Reset()         { *m = GetAllFellowPresentReq{} }
func (m *GetAllFellowPresentReq) String() string { return proto.CompactTextString(m) }
func (*GetAllFellowPresentReq) ProtoMessage()    {}
func (*GetAllFellowPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{143}
}
func (m *GetAllFellowPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllFellowPresentReq.Unmarshal(m, b)
}
func (m *GetAllFellowPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllFellowPresentReq.Marshal(b, m, deterministic)
}
func (dst *GetAllFellowPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllFellowPresentReq.Merge(dst, src)
}
func (m *GetAllFellowPresentReq) XXX_Size() int {
	return xxx_messageInfo_GetAllFellowPresentReq.Size(m)
}
func (m *GetAllFellowPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllFellowPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllFellowPresentReq proto.InternalMessageInfo

type GetAllFellowPresentResp struct {
	FellowPresentList    []*StPresentItemConfigBackend `protobuf:"bytes,1,rep,name=fellow_present_list,json=fellowPresentList,proto3" json:"fellow_present_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetAllFellowPresentResp) Reset()         { *m = GetAllFellowPresentResp{} }
func (m *GetAllFellowPresentResp) String() string { return proto.CompactTextString(m) }
func (*GetAllFellowPresentResp) ProtoMessage()    {}
func (*GetAllFellowPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{144}
}
func (m *GetAllFellowPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllFellowPresentResp.Unmarshal(m, b)
}
func (m *GetAllFellowPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllFellowPresentResp.Marshal(b, m, deterministic)
}
func (dst *GetAllFellowPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllFellowPresentResp.Merge(dst, src)
}
func (m *GetAllFellowPresentResp) XXX_Size() int {
	return xxx_messageInfo_GetAllFellowPresentResp.Size(m)
}
func (m *GetAllFellowPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllFellowPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllFellowPresentResp proto.InternalMessageInfo

func (m *GetAllFellowPresentResp) GetFellowPresentList() []*StPresentItemConfigBackend {
	if m != nil {
		return m.FellowPresentList
	}
	return nil
}

// 礼物配置信息
type StPresentItemConfigBackend struct {
	ItemId               uint32                            `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Name                 string                            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl              string                            `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price                uint32                            `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Score                uint32                            `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	Charm                uint32                            `protobuf:"varint,6,opt,name=charm,proto3" json:"charm,omitempty"`
	Rank                 uint32                            `protobuf:"varint,7,opt,name=rank,proto3" json:"rank,omitempty"`
	EffectBegin          uint32                            `protobuf:"varint,8,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                            `protobuf:"varint,9,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	UpdateTime           uint32                            `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           uint32                            `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	IsDel                bool                              `protobuf:"varint,12,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	PriceType            uint32                            `protobuf:"varint,13,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	RichValue            uint32                            `protobuf:"varint,14,opt,name=rich_value,json=richValue,proto3" json:"rich_value,omitempty"`
	Extend               *StPresentItemConfigExtendBackend `protobuf:"bytes,15,opt,name=extend,proto3" json:"extend,omitempty"`
	Fellow               *FellowPresentConfig              `protobuf:"bytes,16,opt,name=fellow,proto3" json:"fellow,omitempty"`
	DelayInfo            []*EffectDelayLevelInfo           `protobuf:"bytes,17,rep,name=delay_info,json=delayInfo,proto3" json:"delay_info,omitempty"`
	IsBanned             bool                              `protobuf:"varint,18,opt,name=is_banned,json=isBanned,proto3" json:"is_banned,omitempty"`
	RankFloat            float32                           `protobuf:"fixed32,19,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *StPresentItemConfigBackend) Reset()         { *m = StPresentItemConfigBackend{} }
func (m *StPresentItemConfigBackend) String() string { return proto.CompactTextString(m) }
func (*StPresentItemConfigBackend) ProtoMessage()    {}
func (*StPresentItemConfigBackend) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{145}
}
func (m *StPresentItemConfigBackend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPresentItemConfigBackend.Unmarshal(m, b)
}
func (m *StPresentItemConfigBackend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPresentItemConfigBackend.Marshal(b, m, deterministic)
}
func (dst *StPresentItemConfigBackend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPresentItemConfigBackend.Merge(dst, src)
}
func (m *StPresentItemConfigBackend) XXX_Size() int {
	return xxx_messageInfo_StPresentItemConfigBackend.Size(m)
}
func (m *StPresentItemConfigBackend) XXX_DiscardUnknown() {
	xxx_messageInfo_StPresentItemConfigBackend.DiscardUnknown(m)
}

var xxx_messageInfo_StPresentItemConfigBackend proto.InternalMessageInfo

func (m *StPresentItemConfigBackend) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StPresentItemConfigBackend) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *StPresentItemConfigBackend) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *StPresentItemConfigBackend) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetRichValue() uint32 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *StPresentItemConfigBackend) GetExtend() *StPresentItemConfigExtendBackend {
	if m != nil {
		return m.Extend
	}
	return nil
}

func (m *StPresentItemConfigBackend) GetFellow() *FellowPresentConfig {
	if m != nil {
		return m.Fellow
	}
	return nil
}

func (m *StPresentItemConfigBackend) GetDelayInfo() []*EffectDelayLevelInfo {
	if m != nil {
		return m.DelayInfo
	}
	return nil
}

func (m *StPresentItemConfigBackend) GetIsBanned() bool {
	if m != nil {
		return m.IsBanned
	}
	return false
}

func (m *StPresentItemConfigBackend) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

type StPresentItemConfigExtendBackend struct {
	ItemId               uint32             `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	VideoEffectUrl       []byte             `protobuf:"bytes,2,opt,name=video_effect_url,json=videoEffectUrl,proto3" json:"video_effect_url,omitempty"`
	ShowEffect           uint32             `protobuf:"varint,3,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	UnshowBatchOption    bool               `protobuf:"varint,4,opt,name=unshow_batch_option,json=unshowBatchOption,proto3" json:"unshow_batch_option,omitempty"`
	IsTest               bool               `protobuf:"varint,5,opt,name=is_test,json=isTest,proto3" json:"is_test,omitempty"`
	FlowId               uint32             `protobuf:"varint,6,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	IosExtend            *StConfigIosExtend `protobuf:"bytes,7,opt,name=ios_extend,json=iosExtend,proto3" json:"ios_extend,omitempty"`
	NotifyAll            bool               `protobuf:"varint,8,opt,name=notify_all,json=notifyAll,proto3" json:"notify_all,omitempty"`
	Tag                  uint32             `protobuf:"varint,9,opt,name=tag,proto3" json:"tag,omitempty"`
	ForceSendable        bool               `protobuf:"varint,10,opt,name=force_sendable,json=forceSendable,proto3" json:"force_sendable,omitempty"`
	NobilityLevel        uint32             `protobuf:"varint,11,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	UnshowPresentShelf   bool               `protobuf:"varint,12,opt,name=unshow_present_shelf,json=unshowPresentShelf,proto3" json:"unshow_present_shelf,omitempty"`
	ShowEffectEnd        bool               `protobuf:"varint,13,opt,name=show_effect_end,json=showEffectEnd,proto3" json:"show_effect_end,omitempty"`
	EffectEndDelay       bool               `protobuf:"varint,14,opt,name=effect_end_delay,json=effectEndDelay,proto3" json:"effect_end_delay,omitempty"`
	CustomText           []*CustomText      `protobuf:"bytes,15,rep,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	MicEffectUrl         string             `protobuf:"bytes,16,opt,name=mic_effect_url,json=micEffectUrl,proto3" json:"mic_effect_url,omitempty"`
	MicEffectMd5         string             `protobuf:"bytes,17,opt,name=mic_effect_md5,json=micEffectMd5,proto3" json:"mic_effect_md5,omitempty"`
	SmallVapEffectUrl    string             `protobuf:"bytes,18,opt,name=small_vap_effect_url,json=smallVapEffectUrl,proto3" json:"small_vap_effect_url,omitempty"`
	SmallVapEffectMd5    string             `protobuf:"bytes,19,opt,name=small_vap_effect_md5,json=smallVapEffectMd5,proto3" json:"small_vap_effect_md5,omitempty"`
	FusionPresent        bool               `protobuf:"varint,20,opt,name=fusion_present,json=fusionPresent,proto3" json:"fusion_present,omitempty"`
	IsBoxBreaking        bool               `protobuf:"varint,21,opt,name=is_box_breaking,json=isBoxBreaking,proto3" json:"is_box_breaking,omitempty"`
	FansLevel            uint32             `protobuf:"varint,22,opt,name=fans_level,json=fansLevel,proto3" json:"fans_level,omitempty"`
	OriginIconUrl        string             `protobuf:"bytes,23,opt,name=origin_icon_url,json=originIconUrl,proto3" json:"origin_icon_url,omitempty"`
	MarkId               uint64             `protobuf:"varint,24,opt,name=mark_id,json=markId,proto3" json:"mark_id,omitempty"`
	MarkName             string             `protobuf:"bytes,25,opt,name=mark_name,json=markName,proto3" json:"mark_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *StPresentItemConfigExtendBackend) Reset()         { *m = StPresentItemConfigExtendBackend{} }
func (m *StPresentItemConfigExtendBackend) String() string { return proto.CompactTextString(m) }
func (*StPresentItemConfigExtendBackend) ProtoMessage()    {}
func (*StPresentItemConfigExtendBackend) Descriptor() ([]byte, []int) {
	return fileDescriptor_userpresent_go_dc34c46671c2d6c2, []int{146}
}
func (m *StPresentItemConfigExtendBackend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPresentItemConfigExtendBackend.Unmarshal(m, b)
}
func (m *StPresentItemConfigExtendBackend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPresentItemConfigExtendBackend.Marshal(b, m, deterministic)
}
func (dst *StPresentItemConfigExtendBackend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPresentItemConfigExtendBackend.Merge(dst, src)
}
func (m *StPresentItemConfigExtendBackend) XXX_Size() int {
	return xxx_messageInfo_StPresentItemConfigExtendBackend.Size(m)
}
func (m *StPresentItemConfigExtendBackend) XXX_DiscardUnknown() {
	xxx_messageInfo_StPresentItemConfigExtendBackend.DiscardUnknown(m)
}

var xxx_messageInfo_StPresentItemConfigExtendBackend proto.InternalMessageInfo

func (m *StPresentItemConfigExtendBackend) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *StPresentItemConfigExtendBackend) GetVideoEffectUrl() []byte {
	if m != nil {
		return m.VideoEffectUrl
	}
	return nil
}

func (m *StPresentItemConfigExtendBackend) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *StPresentItemConfigExtendBackend) GetUnshowBatchOption() bool {
	if m != nil {
		return m.UnshowBatchOption
	}
	return false
}

func (m *StPresentItemConfigExtendBackend) GetIsTest() bool {
	if m != nil {
		return m.IsTest
	}
	return false
}

func (m *StPresentItemConfigExtendBackend) GetFlowId() uint32 {
	if m != nil {
		return m.FlowId
	}
	return 0
}

func (m *StPresentItemConfigExtendBackend) GetIosExtend() *StConfigIosExtend {
	if m != nil {
		return m.IosExtend
	}
	return nil
}

func (m *StPresentItemConfigExtendBackend) GetNotifyAll() bool {
	if m != nil {
		return m.NotifyAll
	}
	return false
}

func (m *StPresentItemConfigExtendBackend) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *StPresentItemConfigExtendBackend) GetForceSendable() bool {
	if m != nil {
		return m.ForceSendable
	}
	return false
}

func (m *StPresentItemConfigExtendBackend) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *StPresentItemConfigExtendBackend) GetUnshowPresentShelf() bool {
	if m != nil {
		return m.UnshowPresentShelf
	}
	return false
}

func (m *StPresentItemConfigExtendBackend) GetShowEffectEnd() bool {
	if m != nil {
		return m.ShowEffectEnd
	}
	return false
}

func (m *StPresentItemConfigExtendBackend) GetEffectEndDelay() bool {
	if m != nil {
		return m.EffectEndDelay
	}
	return false
}

func (m *StPresentItemConfigExtendBackend) GetCustomText() []*CustomText {
	if m != nil {
		return m.CustomText
	}
	return nil
}

func (m *StPresentItemConfigExtendBackend) GetMicEffectUrl() string {
	if m != nil {
		return m.MicEffectUrl
	}
	return ""
}

func (m *StPresentItemConfigExtendBackend) GetMicEffectMd5() string {
	if m != nil {
		return m.MicEffectMd5
	}
	return ""
}

func (m *StPresentItemConfigExtendBackend) GetSmallVapEffectUrl() string {
	if m != nil {
		return m.SmallVapEffectUrl
	}
	return ""
}

func (m *StPresentItemConfigExtendBackend) GetSmallVapEffectMd5() string {
	if m != nil {
		return m.SmallVapEffectMd5
	}
	return ""
}

func (m *StPresentItemConfigExtendBackend) GetFusionPresent() bool {
	if m != nil {
		return m.FusionPresent
	}
	return false
}

func (m *StPresentItemConfigExtendBackend) GetIsBoxBreaking() bool {
	if m != nil {
		return m.IsBoxBreaking
	}
	return false
}

func (m *StPresentItemConfigExtendBackend) GetFansLevel() uint32 {
	if m != nil {
		return m.FansLevel
	}
	return 0
}

func (m *StPresentItemConfigExtendBackend) GetOriginIconUrl() string {
	if m != nil {
		return m.OriginIconUrl
	}
	return ""
}

func (m *StPresentItemConfigExtendBackend) GetMarkId() uint64 {
	if m != nil {
		return m.MarkId
	}
	return 0
}

func (m *StPresentItemConfigExtendBackend) GetMarkName() string {
	if m != nil {
		return m.MarkName
	}
	return ""
}

func init() {
	proto.RegisterType((*StConfigIosExtend)(nil), "userpresent_go.StConfigIosExtend")
	proto.RegisterType((*StPresentItemConfigExtend)(nil), "userpresent_go.StPresentItemConfigExtend")
	proto.RegisterType((*CustomText)(nil), "userpresent_go.CustomText")
	proto.RegisterType((*StPresentItemConfig)(nil), "userpresent_go.StPresentItemConfig")
	proto.RegisterType((*StUserPresentSummary)(nil), "userpresent_go.StUserPresentSummary")
	proto.RegisterType((*StUserPresentDetail)(nil), "userpresent_go.StUserPresentDetail")
	proto.RegisterType((*GetPresentConfigListReq)(nil), "userpresent_go.GetPresentConfigListReq")
	proto.RegisterType((*GetPresentConfigListResp)(nil), "userpresent_go.GetPresentConfigListResp")
	proto.RegisterType((*GetPresentConfigListV3Req)(nil), "userpresent_go.GetPresentConfigListV3Req")
	proto.RegisterType((*GetPresentConfigListV3Resp)(nil), "userpresent_go.GetPresentConfigListV3Resp")
	proto.RegisterType((*AddPresentConfigReq)(nil), "userpresent_go.AddPresentConfigReq")
	proto.RegisterType((*AddPresentConfigResp)(nil), "userpresent_go.AddPresentConfigResp")
	proto.RegisterType((*DelPresentConfigReq)(nil), "userpresent_go.DelPresentConfigReq")
	proto.RegisterType((*DelPresentConfigResp)(nil), "userpresent_go.DelPresentConfigResp")
	proto.RegisterType((*UpdatePresentConfigReq)(nil), "userpresent_go.UpdatePresentConfigReq")
	proto.RegisterType((*UpdatePresentConfigResp)(nil), "userpresent_go.UpdatePresentConfigResp")
	proto.RegisterType((*GetPresentConfigByIdReq)(nil), "userpresent_go.GetPresentConfigByIdReq")
	proto.RegisterType((*GetPresentConfigByIdResp)(nil), "userpresent_go.GetPresentConfigByIdResp")
	proto.RegisterType((*GetPresentConfigListByIdListReq)(nil), "userpresent_go.GetPresentConfigListByIdListReq")
	proto.RegisterType((*GetPresentConfigListByIdListResp)(nil), "userpresent_go.GetPresentConfigListByIdListResp")
	proto.RegisterType((*GetPresentConfigByIdOldResp)(nil), "userpresent_go.GetPresentConfigByIdOldResp")
	proto.RegisterType((*GetPresentConfigByIdListResp)(nil), "userpresent_go.GetPresentConfigByIdListResp")
	proto.RegisterType((*PresentConfigNew)(nil), "userpresent_go.PresentConfigNew")
	proto.RegisterType((*PresentBaseConfig)(nil), "userpresent_go.PresentBaseConfig")
	proto.RegisterType((*PresentEnterConfig)(nil), "userpresent_go.PresentEnterConfig")
	proto.RegisterType((*PresentEffectConfig)(nil), "userpresent_go.PresentEffectConfig")
	proto.RegisterType((*PresentEnterBlacklist)(nil), "userpresent_go.PresentEnterBlacklist")
	proto.RegisterType((*GetLivePresentOrderListReq)(nil), "userpresent_go.GetLivePresentOrderListReq")
	proto.RegisterType((*GetLivePresentOrderListResp)(nil), "userpresent_go.GetLivePresentOrderListResp")
	proto.RegisterType((*LivePresentOrder)(nil), "userpresent_go.LivePresentOrder")
	proto.RegisterType((*StSceneInfo)(nil), "userpresent_go.StSceneInfo")
	proto.RegisterType((*StScenePresentSummary)(nil), "userpresent_go.StScenePresentSummary")
	proto.RegisterType((*StScenePresentDetail)(nil), "userpresent_go.StScenePresentDetail")
	proto.RegisterType((*RecordSceneSendPresentReq)(nil), "userpresent_go.RecordSceneSendPresentReq")
	proto.RegisterType((*GetScenePresentSummaryReq)(nil), "userpresent_go.GetScenePresentSummaryReq")
	proto.RegisterType((*GetScenePresentSummaryResp)(nil), "userpresent_go.GetScenePresentSummaryResp")
	proto.RegisterType((*ClearScenePresentReq)(nil), "userpresent_go.ClearScenePresentReq")
	proto.RegisterType((*GetScenePresentDetailListReq)(nil), "userpresent_go.GetScenePresentDetailListReq")
	proto.RegisterType((*GetScenePresentDetailListResp)(nil), "userpresent_go.GetScenePresentDetailListResp")
	proto.RegisterType((*RecordSceneSendPresentResp)(nil), "userpresent_go.RecordSceneSendPresentResp")
	proto.RegisterType((*ClearScenePresentResp)(nil), "userpresent_go.ClearScenePresentResp")
	proto.RegisterType((*NamingPresentInfo)(nil), "userpresent_go.NamingPresentInfo")
	proto.RegisterType((*AddNamingPresentInfoReq)(nil), "userpresent_go.AddNamingPresentInfoReq")
	proto.RegisterType((*AddNamingPresentInfoResp)(nil), "userpresent_go.AddNamingPresentInfoResp")
	proto.RegisterType((*UpdateNamingPresentInfoReq)(nil), "userpresent_go.UpdateNamingPresentInfoReq")
	proto.RegisterType((*UpdateNamingPresentInfoResp)(nil), "userpresent_go.UpdateNamingPresentInfoResp")
	proto.RegisterType((*DelNamingPresentInfoReq)(nil), "userpresent_go.DelNamingPresentInfoReq")
	proto.RegisterType((*DelNamingPresentInfoResp)(nil), "userpresent_go.DelNamingPresentInfoResp")
	proto.RegisterType((*GetNamingPresentInfoListReq)(nil), "userpresent_go.GetNamingPresentInfoListReq")
	proto.RegisterType((*GetNamingPresentInfoListResp)(nil), "userpresent_go.GetNamingPresentInfoListResp")
	proto.RegisterType((*GetValidNamingPresentInfosReq)(nil), "userpresent_go.GetValidNamingPresentInfosReq")
	proto.RegisterType((*GetValidNamingPresentInfosResp)(nil), "userpresent_go.GetValidNamingPresentInfosResp")
	proto.RegisterType((*SendPresentReq)(nil), "userpresent_go.SendPresentReq")
	proto.RegisterType((*SendPresentResp)(nil), "userpresent_go.SendPresentResp")
	proto.RegisterType((*GetUserPresentDetailListReq)(nil), "userpresent_go.GetUserPresentDetailListReq")
	proto.RegisterType((*GetUserPresentDetailListResp)(nil), "userpresent_go.GetUserPresentDetailListResp")
	proto.RegisterType((*GetUserPresentSendDetailListReq)(nil), "userpresent_go.GetUserPresentSendDetailListReq")
	proto.RegisterType((*GetUserPresentSendDetailListResp)(nil), "userpresent_go.GetUserPresentSendDetailListResp")
	proto.RegisterType((*GetUserPresentSummaryReq)(nil), "userpresent_go.GetUserPresentSummaryReq")
	proto.RegisterType((*GetUserPresentSummaryResp)(nil), "userpresent_go.GetUserPresentSummaryResp")
	proto.RegisterType((*GetUserPresentSummaryByItemListReq)(nil), "userpresent_go.GetUserPresentSummaryByItemListReq")
	proto.RegisterType((*GetUserPresentSummaryByItemListResp)(nil), "userpresent_go.GetUserPresentSummaryByItemListResp")
	proto.RegisterType((*GetPresentConfigUpdateTimeReq)(nil), "userpresent_go.GetPresentConfigUpdateTimeReq")
	proto.RegisterType((*GetPresentConfigUpdateTimeResp)(nil), "userpresent_go.GetPresentConfigUpdateTimeResp")
	proto.RegisterType((*GetPresentOrderStatusReq)(nil), "userpresent_go.GetPresentOrderStatusReq")
	proto.RegisterType((*GetPresentOrderStatusResp)(nil), "userpresent_go.GetPresentOrderStatusResp")
	proto.RegisterType((*DynamicEffectTemplate)(nil), "userpresent_go.DynamicEffectTemplate")
	proto.RegisterType((*AddDynamicEffectTemplateReq)(nil), "userpresent_go.AddDynamicEffectTemplateReq")
	proto.RegisterType((*AddDynamicEffectTemplateResp)(nil), "userpresent_go.AddDynamicEffectTemplateResp")
	proto.RegisterType((*UpdateDynamicEffectTemplateReq)(nil), "userpresent_go.UpdateDynamicEffectTemplateReq")
	proto.RegisterType((*UpdateDynamicEffectTemplateResp)(nil), "userpresent_go.UpdateDynamicEffectTemplateResp")
	proto.RegisterType((*DelDynamicEffectTemplateReq)(nil), "userpresent_go.DelDynamicEffectTemplateReq")
	proto.RegisterType((*DelDynamicEffectTemplateResp)(nil), "userpresent_go.DelDynamicEffectTemplateResp")
	proto.RegisterType((*GetDynamicEffectTemplateListReq)(nil), "userpresent_go.GetDynamicEffectTemplateListReq")
	proto.RegisterType((*GetDynamicEffectTemplateListResp)(nil), "userpresent_go.GetDynamicEffectTemplateListResp")
	proto.RegisterType((*PresentEffectTemplateConfig)(nil), "userpresent_go.PresentEffectTemplateConfig")
	proto.RegisterType((*AddPresentEffectTemplateConfigReq)(nil), "userpresent_go.AddPresentEffectTemplateConfigReq")
	proto.RegisterType((*AddPresentEffectTemplateConfigResp)(nil), "userpresent_go.AddPresentEffectTemplateConfigResp")
	proto.RegisterType((*UpdatePresentEffectTemplateConfigReq)(nil), "userpresent_go.UpdatePresentEffectTemplateConfigReq")
	proto.RegisterType((*UpdatePresentEffectTemplateConfigResp)(nil), "userpresent_go.UpdatePresentEffectTemplateConfigResp")
	proto.RegisterType((*DelPresentEffectTemplateConfigReq)(nil), "userpresent_go.DelPresentEffectTemplateConfigReq")
	proto.RegisterType((*DelPresentEffectTemplateConfigResp)(nil), "userpresent_go.DelPresentEffectTemplateConfigResp")
	proto.RegisterType((*GetPresentEffectTemplateConfigListReq)(nil), "userpresent_go.GetPresentEffectTemplateConfigListReq")
	proto.RegisterType((*GetPresentEffectTemplateConfigListResp)(nil), "userpresent_go.GetPresentEffectTemplateConfigListResp")
	proto.RegisterType((*GetPresentDynamicEffectTemplateConfigReq)(nil), "userpresent_go.GetPresentDynamicEffectTemplateConfigReq")
	proto.RegisterType((*GetPresentDynamicEffectTemplateConfigResp)(nil), "userpresent_go.GetPresentDynamicEffectTemplateConfigResp")
	proto.RegisterType((*GetPresentDynamicTemplateConfUpdateTimeReq)(nil), "userpresent_go.GetPresentDynamicTemplateConfUpdateTimeReq")
	proto.RegisterType((*GetPresentDynamicTemplateConfUpdateTimeResp)(nil), "userpresent_go.GetPresentDynamicTemplateConfUpdateTimeResp")
	proto.RegisterType((*GetPresentDETConfigByIdReq)(nil), "userpresent_go.GetPresentDETConfigByIdReq")
	proto.RegisterType((*GetPresentDETConfigByIdResp)(nil), "userpresent_go.GetPresentDETConfigByIdResp")
	proto.RegisterType((*GetPresentFlowConfigByIdReq)(nil), "userpresent_go.GetPresentFlowConfigByIdReq")
	proto.RegisterType((*GetPresentFlowConfigByIdResp)(nil), "userpresent_go.GetPresentFlowConfigByIdResp")
	proto.RegisterType((*GetPresentFlowConfigListReq)(nil), "userpresent_go.GetPresentFlowConfigListReq")
	proto.RegisterType((*GetPresentFlowConfigListResp)(nil), "userpresent_go.GetPresentFlowConfigListResp")
	proto.RegisterType((*GetPresentFlowConfigUpdateTimeReq)(nil), "userpresent_go.GetPresentFlowConfigUpdateTimeReq")
	proto.RegisterType((*GetPresentFlowConfigUpdateTimeResp)(nil), "userpresent_go.GetPresentFlowConfigUpdateTimeResp")
	proto.RegisterType((*AddPresentFlowConfigReq)(nil), "userpresent_go.AddPresentFlowConfigReq")
	proto.RegisterType((*DelPresentFlowConfigReq)(nil), "userpresent_go.DelPresentFlowConfigReq")
	proto.RegisterType((*UpdatePresentFlowConfigReq)(nil), "userpresent_go.UpdatePresentFlowConfigReq")
	proto.RegisterType((*AddPresentFlowConfigResp)(nil), "userpresent_go.AddPresentFlowConfigResp")
	proto.RegisterType((*DelPresentFlowConfigResp)(nil), "userpresent_go.DelPresentFlowConfigResp")
	proto.RegisterType((*UpdatePresentFlowConfigResp)(nil), "userpresent_go.UpdatePresentFlowConfigResp")
	proto.RegisterType((*StPresentFlowConfig)(nil), "userpresent_go.StPresentFlowConfig")
	proto.RegisterType((*AddChanceItemSourceReq)(nil), "userpresent_go.AddChanceItemSourceReq")
	proto.RegisterType((*AddChanceItemSourceResp)(nil), "userpresent_go.AddChanceItemSourceResp")
	proto.RegisterType((*DelChanceItemSourceReq)(nil), "userpresent_go.DelChanceItemSourceReq")
	proto.RegisterType((*DelChanceItemSourceResp)(nil), "userpresent_go.DelChanceItemSourceResp")
	proto.RegisterType((*GetOrderLogByOrderIdsReq)(nil), "userpresent_go.GetOrderLogByOrderIdsReq")
	proto.RegisterType((*GetOrderLogByOrderIdsResp)(nil), "userpresent_go.GetOrderLogByOrderIdsResp")
	proto.RegisterType((*StUserPresentOrderLog)(nil), "userpresent_go.StUserPresentOrderLog")
	proto.RegisterType((*BatchSendPresentReq)(nil), "userpresent_go.BatchSendPresentReq")
	proto.RegisterType((*BatchSendPresentResp)(nil), "userpresent_go.BatchSendPresentResp")
	proto.RegisterType((*SendPresentItem)(nil), "userpresent_go.SendPresentItem")
	proto.RegisterType((*AddPresentMarkConfigReq)(nil), "userpresent_go.AddPresentMarkConfigReq")
	proto.RegisterType((*AddPresentMarkConfigResp)(nil), "userpresent_go.AddPresentMarkConfigResp")
	proto.RegisterType((*BatchAddPresentMarkConfigReq)(nil), "userpresent_go.BatchAddPresentMarkConfigReq")
	proto.RegisterType((*PresentMarkConfigItem)(nil), "userpresent_go.PresentMarkConfigItem")
	proto.RegisterType((*BatchAddPresentMarkConfigResp)(nil), "userpresent_go.BatchAddPresentMarkConfigResp")
	proto.RegisterType((*GetPresentMarkConfigListReq)(nil), "userpresent_go.GetPresentMarkConfigListReq")
	proto.RegisterType((*GetPresentMarkConfigListResp)(nil), "userpresent_go.GetPresentMarkConfigListResp")
	proto.RegisterType((*DelPresentMarkConfigReq)(nil), "userpresent_go.DelPresentMarkConfigReq")
	proto.RegisterType((*DelPresentMarkConfigResp)(nil), "userpresent_go.DelPresentMarkConfigResp")
	proto.RegisterType((*GetPresentMarkIconByPresentIdReq)(nil), "userpresent_go.GetPresentMarkIconByPresentIdReq")
	proto.RegisterType((*GetPresentMarkIconByPresentIdResp)(nil), "userpresent_go.GetPresentMarkIconByPresentIdResp")
	proto.RegisterType((*StPresentMarkConfig)(nil), "userpresent_go.StPresentMarkConfig")
	proto.RegisterType((*FellowPresentConfig)(nil), "userpresent_go.FellowPresentConfig")
	proto.RegisterType((*GetPresentConfigByIdBackendReq)(nil), "userpresent_go.GetPresentConfigByIdBackendReq")
	proto.RegisterType((*GetPresentConfigByIdBackendResp)(nil), "userpresent_go.GetPresentConfigByIdBackendResp")
	proto.RegisterType((*GetPresentConfigListBackendReq)(nil), "userpresent_go.GetPresentConfigListBackendReq")
	proto.RegisterType((*GetPresentConfigListBackendResp)(nil), "userpresent_go.GetPresentConfigListBackendResp")
	proto.RegisterType((*AddPresentConfigBackendReq)(nil), "userpresent_go.AddPresentConfigBackendReq")
	proto.RegisterType((*EffectDelayLevelInfo)(nil), "userpresent_go.EffectDelayLevelInfo")
	proto.RegisterType((*AddPresentConfigBackendResp)(nil), "userpresent_go.AddPresentConfigBackendResp")
	proto.RegisterType((*DelPresentConfigBackendReq)(nil), "userpresent_go.DelPresentConfigBackendReq")
	proto.RegisterType((*DelPresentConfigBackendResp)(nil), "userpresent_go.DelPresentConfigBackendResp")
	proto.RegisterType((*UpdatePresentConfigBackendReq)(nil), "userpresent_go.UpdatePresentConfigBackendReq")
	proto.RegisterType((*UpdatePresentConfigBackendResp)(nil), "userpresent_go.UpdatePresentConfigBackendResp")
	proto.RegisterType((*UserPresentSend)(nil), "userpresent_go.UserPresentSend")
	proto.RegisterType((*UserPresentReceive)(nil), "userpresent_go.UserPresentReceive")
	proto.RegisterType((*GetUserPresentSendReq)(nil), "userpresent_go.GetUserPresentSendReq")
	proto.RegisterType((*GetUserPresentSendResp)(nil), "userpresent_go.GetUserPresentSendResp")
	proto.RegisterType((*GetUserPresentReceiveReq)(nil), "userpresent_go.GetUserPresentReceiveReq")
	proto.RegisterType((*GetUserPresentReceiveResp)(nil), "userpresent_go.GetUserPresentReceiveResp")
	proto.RegisterType((*GetAllFellowPresentReq)(nil), "userpresent_go.GetAllFellowPresentReq")
	proto.RegisterType((*GetAllFellowPresentResp)(nil), "userpresent_go.GetAllFellowPresentResp")
	proto.RegisterType((*StPresentItemConfigBackend)(nil), "userpresent_go.StPresentItemConfigBackend")
	proto.RegisterType((*StPresentItemConfigExtendBackend)(nil), "userpresent_go.StPresentItemConfigExtendBackend")
	proto.RegisterEnum("userpresent_go.PresentPriceType", PresentPriceType_name, PresentPriceType_value)
	proto.RegisterEnum("userpresent_go.ConfigListTypeBitMap", ConfigListTypeBitMap_name, ConfigListTypeBitMap_value)
	proto.RegisterEnum("userpresent_go.PresentEnterType", PresentEnterType_name, PresentEnterType_value)
	proto.RegisterEnum("userpresent_go.PresentSceneType", PresentSceneType_name, PresentSceneType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserPresentGOClient is the client API for UserPresentGO service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserPresentGOClient interface {
	// 礼物列表 - 旧版
	GetPresentConfigList(ctx context.Context, in *GetPresentConfigListReq, opts ...grpc.CallOption) (*GetPresentConfigListResp, error)
	// 增删查改
	AddPresentConfig(ctx context.Context, in *AddPresentConfigReq, opts ...grpc.CallOption) (*AddPresentConfigResp, error)
	UpdatePresentConfig(ctx context.Context, in *UpdatePresentConfigReq, opts ...grpc.CallOption) (*UpdatePresentConfigResp, error)
	DelPresentConfig(ctx context.Context, in *DelPresentConfigReq, opts ...grpc.CallOption) (*DelPresentConfigResp, error)
	// 查询礼物列表 - 新版 - V2旧的userpresent协议用过了
	GetPresentConfigListV3(ctx context.Context, in *GetPresentConfigListV3Req, opts ...grpc.CallOption) (*GetPresentConfigListV3Resp, error)
	GetPresentConfigById(ctx context.Context, in *GetPresentConfigByIdReq, opts ...grpc.CallOption) (*GetPresentConfigByIdResp, error)
	GetPresentConfigListByIdList(ctx context.Context, in *GetPresentConfigListByIdListReq, opts ...grpc.CallOption) (*GetPresentConfigListByIdListResp, error)
	GetPresentConfigUpdateTime(ctx context.Context, in *GetPresentConfigUpdateTimeReq, opts ...grpc.CallOption) (*GetPresentConfigUpdateTimeResp, error)
	// 获取虚拟直播间主播某时间段收礼流水
	GetLivePresentOrderList(ctx context.Context, in *GetLivePresentOrderListReq, opts ...grpc.CallOption) (*GetLivePresentOrderListResp, error)
	// 场景礼物
	RecordSceneSendPresent(ctx context.Context, in *RecordSceneSendPresentReq, opts ...grpc.CallOption) (*RecordSceneSendPresentResp, error)
	GetScenePresentSummary(ctx context.Context, in *GetScenePresentSummaryReq, opts ...grpc.CallOption) (*GetScenePresentSummaryResp, error)
	ClearScenePresent(ctx context.Context, in *ClearScenePresentReq, opts ...grpc.CallOption) (*ClearScenePresentResp, error)
	GetScenePresentDetailList(ctx context.Context, in *GetScenePresentDetailListReq, opts ...grpc.CallOption) (*GetScenePresentDetailListResp, error)
	// 冠名礼物
	AddNamingPresentInfo(ctx context.Context, in *AddNamingPresentInfoReq, opts ...grpc.CallOption) (*AddNamingPresentInfoResp, error)
	UpdateNamingPresentInfo(ctx context.Context, in *UpdateNamingPresentInfoReq, opts ...grpc.CallOption) (*UpdateNamingPresentInfoResp, error)
	DelNamingPresentInfo(ctx context.Context, in *DelNamingPresentInfoReq, opts ...grpc.CallOption) (*DelNamingPresentInfoResp, error)
	GetNamingPresentInfoList(ctx context.Context, in *GetNamingPresentInfoListReq, opts ...grpc.CallOption) (*GetNamingPresentInfoListResp, error)
	GetValidNamingPresentInfos(ctx context.Context, in *GetValidNamingPresentInfosReq, opts ...grpc.CallOption) (*GetValidNamingPresentInfosResp, error)
	// 送礼
	SendPresent(ctx context.Context, in *SendPresentReq, opts ...grpc.CallOption) (*SendPresentResp, error)
	BatchSendPresent(ctx context.Context, in *BatchSendPresentReq, opts ...grpc.CallOption) (*BatchSendPresentResp, error)
	// 查询用户送礼
	GetUserPresentDetailList(ctx context.Context, in *GetUserPresentDetailListReq, opts ...grpc.CallOption) (*GetUserPresentDetailListResp, error)
	GetUserPresentSendDetailList(ctx context.Context, in *GetUserPresentSendDetailListReq, opts ...grpc.CallOption) (*GetUserPresentSendDetailListResp, error)
	// 礼物汇总
	GetUserPresentSummary(ctx context.Context, in *GetUserPresentSummaryReq, opts ...grpc.CallOption) (*GetUserPresentSummaryResp, error)
	GetUserPresentSummaryByItemList(ctx context.Context, in *GetUserPresentSummaryByItemListReq, opts ...grpc.CallOption) (*GetUserPresentSummaryByItemListResp, error)
	// 对账相关
	GetPresentOrderStatus(ctx context.Context, in *GetPresentOrderStatusReq, opts ...grpc.CallOption) (*GetPresentOrderStatusResp, error)
	GetOrderLogByOrderIds(ctx context.Context, in *GetOrderLogByOrderIdsReq, opts ...grpc.CallOption) (*GetOrderLogByOrderIdsResp, error)
	// 流光
	GetPresentFlowConfigById(ctx context.Context, in *GetPresentFlowConfigByIdReq, opts ...grpc.CallOption) (*GetPresentFlowConfigByIdResp, error)
	GetPresentFlowConfigList(ctx context.Context, in *GetPresentFlowConfigListReq, opts ...grpc.CallOption) (*GetPresentFlowConfigListResp, error)
	GetPresentFlowConfigUpdateTime(ctx context.Context, in *GetPresentFlowConfigUpdateTimeReq, opts ...grpc.CallOption) (*GetPresentFlowConfigUpdateTimeResp, error)
	AddPresentFlowConfig(ctx context.Context, in *AddPresentFlowConfigReq, opts ...grpc.CallOption) (*AddPresentFlowConfigResp, error)
	DelPresentFlowConfig(ctx context.Context, in *DelPresentFlowConfigReq, opts ...grpc.CallOption) (*DelPresentFlowConfigResp, error)
	UpdatePresentFlowConfig(ctx context.Context, in *UpdatePresentFlowConfigReq, opts ...grpc.CallOption) (*UpdatePresentFlowConfigResp, error)
	// 动效模版
	AddDynamicEffectTemplate(ctx context.Context, in *AddDynamicEffectTemplateReq, opts ...grpc.CallOption) (*AddDynamicEffectTemplateResp, error)
	UpdateDynamicEffectTemplate(ctx context.Context, in *UpdateDynamicEffectTemplateReq, opts ...grpc.CallOption) (*UpdateDynamicEffectTemplateResp, error)
	DelDynamicEffectTemplate(ctx context.Context, in *DelDynamicEffectTemplateReq, opts ...grpc.CallOption) (*DelDynamicEffectTemplateResp, error)
	GetDynamicEffectTemplateList(ctx context.Context, in *GetDynamicEffectTemplateListReq, opts ...grpc.CallOption) (*GetDynamicEffectTemplateListResp, error)
	AddPresentEffectTemplateConfig(ctx context.Context, in *AddPresentEffectTemplateConfigReq, opts ...grpc.CallOption) (*AddPresentEffectTemplateConfigResp, error)
	UpdatePresentEffectTemplateConfig(ctx context.Context, in *UpdatePresentEffectTemplateConfigReq, opts ...grpc.CallOption) (*UpdatePresentEffectTemplateConfigResp, error)
	DelPresentEffectTemplateConfig(ctx context.Context, in *DelPresentEffectTemplateConfigReq, opts ...grpc.CallOption) (*DelPresentEffectTemplateConfigResp, error)
	GetPresentEffectTemplateConfigList(ctx context.Context, in *GetPresentEffectTemplateConfigListReq, opts ...grpc.CallOption) (*GetPresentEffectTemplateConfigListResp, error)
	GetPresentDynamicEffectTemplateConfig(ctx context.Context, in *GetPresentDynamicEffectTemplateConfigReq, opts ...grpc.CallOption) (*GetPresentDynamicEffectTemplateConfigResp, error)
	GetPresentDynamicTemplateConfUpdateTime(ctx context.Context, in *GetPresentDynamicTemplateConfUpdateTimeReq, opts ...grpc.CallOption) (*GetPresentDynamicTemplateConfUpdateTimeResp, error)
	GetPresentDETConfigById(ctx context.Context, in *GetPresentDETConfigByIdReq, opts ...grpc.CallOption) (*GetPresentDETConfigByIdResp, error)
	// 娱乐玩法来源
	AddChanceItemSource(ctx context.Context, in *AddChanceItemSourceReq, opts ...grpc.CallOption) (*AddChanceItemSourceResp, error)
	DelChanceItemSource(ctx context.Context, in *DelChanceItemSourceReq, opts ...grpc.CallOption) (*DelChanceItemSourceResp, error)
	// 礼物角标
	AddPresentMarkConfig(ctx context.Context, in *AddPresentMarkConfigReq, opts ...grpc.CallOption) (*AddPresentMarkConfigResp, error)
	BatchAddPresentMarkConfig(ctx context.Context, in *BatchAddPresentMarkConfigReq, opts ...grpc.CallOption) (*BatchAddPresentMarkConfigResp, error)
	DelPresentMarkConfig(ctx context.Context, in *DelPresentMarkConfigReq, opts ...grpc.CallOption) (*DelPresentMarkConfigResp, error)
	GetPresentMarkConfigList(ctx context.Context, in *GetPresentMarkConfigListReq, opts ...grpc.CallOption) (*GetPresentMarkConfigListResp, error)
	GetPresentMarkIconByPresentId(ctx context.Context, in *GetPresentMarkIconByPresentIdReq, opts ...grpc.CallOption) (*GetPresentMarkIconByPresentIdResp, error)
	// 后台专用
	GetPresentConfigListBackend(ctx context.Context, in *GetPresentConfigListBackendReq, opts ...grpc.CallOption) (*GetPresentConfigListBackendResp, error)
	GetPresentConfigByIdBackend(ctx context.Context, in *GetPresentConfigByIdBackendReq, opts ...grpc.CallOption) (*GetPresentConfigByIdBackendResp, error)
	UpdatePresentConfigBackend(ctx context.Context, in *UpdatePresentConfigBackendReq, opts ...grpc.CallOption) (*UpdatePresentConfigBackendResp, error)
	DelPresentConfigBackend(ctx context.Context, in *DelPresentConfigBackendReq, opts ...grpc.CallOption) (*DelPresentConfigBackendResp, error)
	AddPresentConfigBackend(ctx context.Context, in *AddPresentConfigBackendReq, opts ...grpc.CallOption) (*AddPresentConfigBackendResp, error)
	GetUserPresentSend(ctx context.Context, in *GetUserPresentSendReq, opts ...grpc.CallOption) (*GetUserPresentSendResp, error)
	GetUserPresentReceive(ctx context.Context, in *GetUserPresentReceiveReq, opts ...grpc.CallOption) (*GetUserPresentReceiveResp, error)
	GetAllFellowPresent(ctx context.Context, in *GetAllFellowPresentReq, opts ...grpc.CallOption) (*GetAllFellowPresentResp, error)
}

type userPresentGOClient struct {
	cc *grpc.ClientConn
}

func NewUserPresentGOClient(cc *grpc.ClientConn) UserPresentGOClient {
	return &userPresentGOClient{cc}
}

func (c *userPresentGOClient) GetPresentConfigList(ctx context.Context, in *GetPresentConfigListReq, opts ...grpc.CallOption) (*GetPresentConfigListResp, error) {
	out := new(GetPresentConfigListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) AddPresentConfig(ctx context.Context, in *AddPresentConfigReq, opts ...grpc.CallOption) (*AddPresentConfigResp, error) {
	out := new(AddPresentConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/AddPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) UpdatePresentConfig(ctx context.Context, in *UpdatePresentConfigReq, opts ...grpc.CallOption) (*UpdatePresentConfigResp, error) {
	out := new(UpdatePresentConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/UpdatePresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) DelPresentConfig(ctx context.Context, in *DelPresentConfigReq, opts ...grpc.CallOption) (*DelPresentConfigResp, error) {
	out := new(DelPresentConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/DelPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentConfigListV3(ctx context.Context, in *GetPresentConfigListV3Req, opts ...grpc.CallOption) (*GetPresentConfigListV3Resp, error) {
	out := new(GetPresentConfigListV3Resp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigListV3", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentConfigById(ctx context.Context, in *GetPresentConfigByIdReq, opts ...grpc.CallOption) (*GetPresentConfigByIdResp, error) {
	out := new(GetPresentConfigByIdResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentConfigListByIdList(ctx context.Context, in *GetPresentConfigListByIdListReq, opts ...grpc.CallOption) (*GetPresentConfigListByIdListResp, error) {
	out := new(GetPresentConfigListByIdListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigListByIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentConfigUpdateTime(ctx context.Context, in *GetPresentConfigUpdateTimeReq, opts ...grpc.CallOption) (*GetPresentConfigUpdateTimeResp, error) {
	out := new(GetPresentConfigUpdateTimeResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigUpdateTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetLivePresentOrderList(ctx context.Context, in *GetLivePresentOrderListReq, opts ...grpc.CallOption) (*GetLivePresentOrderListResp, error) {
	out := new(GetLivePresentOrderListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetLivePresentOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) RecordSceneSendPresent(ctx context.Context, in *RecordSceneSendPresentReq, opts ...grpc.CallOption) (*RecordSceneSendPresentResp, error) {
	out := new(RecordSceneSendPresentResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/RecordSceneSendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetScenePresentSummary(ctx context.Context, in *GetScenePresentSummaryReq, opts ...grpc.CallOption) (*GetScenePresentSummaryResp, error) {
	out := new(GetScenePresentSummaryResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetScenePresentSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) ClearScenePresent(ctx context.Context, in *ClearScenePresentReq, opts ...grpc.CallOption) (*ClearScenePresentResp, error) {
	out := new(ClearScenePresentResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/ClearScenePresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetScenePresentDetailList(ctx context.Context, in *GetScenePresentDetailListReq, opts ...grpc.CallOption) (*GetScenePresentDetailListResp, error) {
	out := new(GetScenePresentDetailListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetScenePresentDetailList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) AddNamingPresentInfo(ctx context.Context, in *AddNamingPresentInfoReq, opts ...grpc.CallOption) (*AddNamingPresentInfoResp, error) {
	out := new(AddNamingPresentInfoResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/AddNamingPresentInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) UpdateNamingPresentInfo(ctx context.Context, in *UpdateNamingPresentInfoReq, opts ...grpc.CallOption) (*UpdateNamingPresentInfoResp, error) {
	out := new(UpdateNamingPresentInfoResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/UpdateNamingPresentInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) DelNamingPresentInfo(ctx context.Context, in *DelNamingPresentInfoReq, opts ...grpc.CallOption) (*DelNamingPresentInfoResp, error) {
	out := new(DelNamingPresentInfoResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/DelNamingPresentInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetNamingPresentInfoList(ctx context.Context, in *GetNamingPresentInfoListReq, opts ...grpc.CallOption) (*GetNamingPresentInfoListResp, error) {
	out := new(GetNamingPresentInfoListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetNamingPresentInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetValidNamingPresentInfos(ctx context.Context, in *GetValidNamingPresentInfosReq, opts ...grpc.CallOption) (*GetValidNamingPresentInfosResp, error) {
	out := new(GetValidNamingPresentInfosResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetValidNamingPresentInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) SendPresent(ctx context.Context, in *SendPresentReq, opts ...grpc.CallOption) (*SendPresentResp, error) {
	out := new(SendPresentResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/SendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) BatchSendPresent(ctx context.Context, in *BatchSendPresentReq, opts ...grpc.CallOption) (*BatchSendPresentResp, error) {
	out := new(BatchSendPresentResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/BatchSendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetUserPresentDetailList(ctx context.Context, in *GetUserPresentDetailListReq, opts ...grpc.CallOption) (*GetUserPresentDetailListResp, error) {
	out := new(GetUserPresentDetailListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetUserPresentDetailList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetUserPresentSendDetailList(ctx context.Context, in *GetUserPresentSendDetailListReq, opts ...grpc.CallOption) (*GetUserPresentSendDetailListResp, error) {
	out := new(GetUserPresentSendDetailListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetUserPresentSendDetailList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetUserPresentSummary(ctx context.Context, in *GetUserPresentSummaryReq, opts ...grpc.CallOption) (*GetUserPresentSummaryResp, error) {
	out := new(GetUserPresentSummaryResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetUserPresentSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetUserPresentSummaryByItemList(ctx context.Context, in *GetUserPresentSummaryByItemListReq, opts ...grpc.CallOption) (*GetUserPresentSummaryByItemListResp, error) {
	out := new(GetUserPresentSummaryByItemListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetUserPresentSummaryByItemList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentOrderStatus(ctx context.Context, in *GetPresentOrderStatusReq, opts ...grpc.CallOption) (*GetPresentOrderStatusResp, error) {
	out := new(GetPresentOrderStatusResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentOrderStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetOrderLogByOrderIds(ctx context.Context, in *GetOrderLogByOrderIdsReq, opts ...grpc.CallOption) (*GetOrderLogByOrderIdsResp, error) {
	out := new(GetOrderLogByOrderIdsResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetOrderLogByOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentFlowConfigById(ctx context.Context, in *GetPresentFlowConfigByIdReq, opts ...grpc.CallOption) (*GetPresentFlowConfigByIdResp, error) {
	out := new(GetPresentFlowConfigByIdResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentFlowConfigById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentFlowConfigList(ctx context.Context, in *GetPresentFlowConfigListReq, opts ...grpc.CallOption) (*GetPresentFlowConfigListResp, error) {
	out := new(GetPresentFlowConfigListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentFlowConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentFlowConfigUpdateTime(ctx context.Context, in *GetPresentFlowConfigUpdateTimeReq, opts ...grpc.CallOption) (*GetPresentFlowConfigUpdateTimeResp, error) {
	out := new(GetPresentFlowConfigUpdateTimeResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentFlowConfigUpdateTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) AddPresentFlowConfig(ctx context.Context, in *AddPresentFlowConfigReq, opts ...grpc.CallOption) (*AddPresentFlowConfigResp, error) {
	out := new(AddPresentFlowConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/AddPresentFlowConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) DelPresentFlowConfig(ctx context.Context, in *DelPresentFlowConfigReq, opts ...grpc.CallOption) (*DelPresentFlowConfigResp, error) {
	out := new(DelPresentFlowConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/DelPresentFlowConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) UpdatePresentFlowConfig(ctx context.Context, in *UpdatePresentFlowConfigReq, opts ...grpc.CallOption) (*UpdatePresentFlowConfigResp, error) {
	out := new(UpdatePresentFlowConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/UpdatePresentFlowConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) AddDynamicEffectTemplate(ctx context.Context, in *AddDynamicEffectTemplateReq, opts ...grpc.CallOption) (*AddDynamicEffectTemplateResp, error) {
	out := new(AddDynamicEffectTemplateResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/AddDynamicEffectTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) UpdateDynamicEffectTemplate(ctx context.Context, in *UpdateDynamicEffectTemplateReq, opts ...grpc.CallOption) (*UpdateDynamicEffectTemplateResp, error) {
	out := new(UpdateDynamicEffectTemplateResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/UpdateDynamicEffectTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) DelDynamicEffectTemplate(ctx context.Context, in *DelDynamicEffectTemplateReq, opts ...grpc.CallOption) (*DelDynamicEffectTemplateResp, error) {
	out := new(DelDynamicEffectTemplateResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/DelDynamicEffectTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetDynamicEffectTemplateList(ctx context.Context, in *GetDynamicEffectTemplateListReq, opts ...grpc.CallOption) (*GetDynamicEffectTemplateListResp, error) {
	out := new(GetDynamicEffectTemplateListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetDynamicEffectTemplateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) AddPresentEffectTemplateConfig(ctx context.Context, in *AddPresentEffectTemplateConfigReq, opts ...grpc.CallOption) (*AddPresentEffectTemplateConfigResp, error) {
	out := new(AddPresentEffectTemplateConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/AddPresentEffectTemplateConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) UpdatePresentEffectTemplateConfig(ctx context.Context, in *UpdatePresentEffectTemplateConfigReq, opts ...grpc.CallOption) (*UpdatePresentEffectTemplateConfigResp, error) {
	out := new(UpdatePresentEffectTemplateConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/UpdatePresentEffectTemplateConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) DelPresentEffectTemplateConfig(ctx context.Context, in *DelPresentEffectTemplateConfigReq, opts ...grpc.CallOption) (*DelPresentEffectTemplateConfigResp, error) {
	out := new(DelPresentEffectTemplateConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/DelPresentEffectTemplateConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentEffectTemplateConfigList(ctx context.Context, in *GetPresentEffectTemplateConfigListReq, opts ...grpc.CallOption) (*GetPresentEffectTemplateConfigListResp, error) {
	out := new(GetPresentEffectTemplateConfigListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentEffectTemplateConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentDynamicEffectTemplateConfig(ctx context.Context, in *GetPresentDynamicEffectTemplateConfigReq, opts ...grpc.CallOption) (*GetPresentDynamicEffectTemplateConfigResp, error) {
	out := new(GetPresentDynamicEffectTemplateConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentDynamicEffectTemplateConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentDynamicTemplateConfUpdateTime(ctx context.Context, in *GetPresentDynamicTemplateConfUpdateTimeReq, opts ...grpc.CallOption) (*GetPresentDynamicTemplateConfUpdateTimeResp, error) {
	out := new(GetPresentDynamicTemplateConfUpdateTimeResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentDynamicTemplateConfUpdateTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentDETConfigById(ctx context.Context, in *GetPresentDETConfigByIdReq, opts ...grpc.CallOption) (*GetPresentDETConfigByIdResp, error) {
	out := new(GetPresentDETConfigByIdResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentDETConfigById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) AddChanceItemSource(ctx context.Context, in *AddChanceItemSourceReq, opts ...grpc.CallOption) (*AddChanceItemSourceResp, error) {
	out := new(AddChanceItemSourceResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/AddChanceItemSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) DelChanceItemSource(ctx context.Context, in *DelChanceItemSourceReq, opts ...grpc.CallOption) (*DelChanceItemSourceResp, error) {
	out := new(DelChanceItemSourceResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/DelChanceItemSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) AddPresentMarkConfig(ctx context.Context, in *AddPresentMarkConfigReq, opts ...grpc.CallOption) (*AddPresentMarkConfigResp, error) {
	out := new(AddPresentMarkConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/AddPresentMarkConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) BatchAddPresentMarkConfig(ctx context.Context, in *BatchAddPresentMarkConfigReq, opts ...grpc.CallOption) (*BatchAddPresentMarkConfigResp, error) {
	out := new(BatchAddPresentMarkConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/BatchAddPresentMarkConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) DelPresentMarkConfig(ctx context.Context, in *DelPresentMarkConfigReq, opts ...grpc.CallOption) (*DelPresentMarkConfigResp, error) {
	out := new(DelPresentMarkConfigResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/DelPresentMarkConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentMarkConfigList(ctx context.Context, in *GetPresentMarkConfigListReq, opts ...grpc.CallOption) (*GetPresentMarkConfigListResp, error) {
	out := new(GetPresentMarkConfigListResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentMarkConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentMarkIconByPresentId(ctx context.Context, in *GetPresentMarkIconByPresentIdReq, opts ...grpc.CallOption) (*GetPresentMarkIconByPresentIdResp, error) {
	out := new(GetPresentMarkIconByPresentIdResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentMarkIconByPresentId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentConfigListBackend(ctx context.Context, in *GetPresentConfigListBackendReq, opts ...grpc.CallOption) (*GetPresentConfigListBackendResp, error) {
	out := new(GetPresentConfigListBackendResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigListBackend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetPresentConfigByIdBackend(ctx context.Context, in *GetPresentConfigByIdBackendReq, opts ...grpc.CallOption) (*GetPresentConfigByIdBackendResp, error) {
	out := new(GetPresentConfigByIdBackendResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetPresentConfigByIdBackend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) UpdatePresentConfigBackend(ctx context.Context, in *UpdatePresentConfigBackendReq, opts ...grpc.CallOption) (*UpdatePresentConfigBackendResp, error) {
	out := new(UpdatePresentConfigBackendResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/UpdatePresentConfigBackend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) DelPresentConfigBackend(ctx context.Context, in *DelPresentConfigBackendReq, opts ...grpc.CallOption) (*DelPresentConfigBackendResp, error) {
	out := new(DelPresentConfigBackendResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/DelPresentConfigBackend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) AddPresentConfigBackend(ctx context.Context, in *AddPresentConfigBackendReq, opts ...grpc.CallOption) (*AddPresentConfigBackendResp, error) {
	out := new(AddPresentConfigBackendResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/AddPresentConfigBackend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetUserPresentSend(ctx context.Context, in *GetUserPresentSendReq, opts ...grpc.CallOption) (*GetUserPresentSendResp, error) {
	out := new(GetUserPresentSendResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetUserPresentSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetUserPresentReceive(ctx context.Context, in *GetUserPresentReceiveReq, opts ...grpc.CallOption) (*GetUserPresentReceiveResp, error) {
	out := new(GetUserPresentReceiveResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetUserPresentReceive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPresentGOClient) GetAllFellowPresent(ctx context.Context, in *GetAllFellowPresentReq, opts ...grpc.CallOption) (*GetAllFellowPresentResp, error) {
	out := new(GetAllFellowPresentResp)
	err := c.cc.Invoke(ctx, "/userpresent_go.UserPresentGO/GetAllFellowPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserPresentGOServer is the server API for UserPresentGO service.
type UserPresentGOServer interface {
	// 礼物列表 - 旧版
	GetPresentConfigList(context.Context, *GetPresentConfigListReq) (*GetPresentConfigListResp, error)
	// 增删查改
	AddPresentConfig(context.Context, *AddPresentConfigReq) (*AddPresentConfigResp, error)
	UpdatePresentConfig(context.Context, *UpdatePresentConfigReq) (*UpdatePresentConfigResp, error)
	DelPresentConfig(context.Context, *DelPresentConfigReq) (*DelPresentConfigResp, error)
	// 查询礼物列表 - 新版 - V2旧的userpresent协议用过了
	GetPresentConfigListV3(context.Context, *GetPresentConfigListV3Req) (*GetPresentConfigListV3Resp, error)
	GetPresentConfigById(context.Context, *GetPresentConfigByIdReq) (*GetPresentConfigByIdResp, error)
	GetPresentConfigListByIdList(context.Context, *GetPresentConfigListByIdListReq) (*GetPresentConfigListByIdListResp, error)
	GetPresentConfigUpdateTime(context.Context, *GetPresentConfigUpdateTimeReq) (*GetPresentConfigUpdateTimeResp, error)
	// 获取虚拟直播间主播某时间段收礼流水
	GetLivePresentOrderList(context.Context, *GetLivePresentOrderListReq) (*GetLivePresentOrderListResp, error)
	// 场景礼物
	RecordSceneSendPresent(context.Context, *RecordSceneSendPresentReq) (*RecordSceneSendPresentResp, error)
	GetScenePresentSummary(context.Context, *GetScenePresentSummaryReq) (*GetScenePresentSummaryResp, error)
	ClearScenePresent(context.Context, *ClearScenePresentReq) (*ClearScenePresentResp, error)
	GetScenePresentDetailList(context.Context, *GetScenePresentDetailListReq) (*GetScenePresentDetailListResp, error)
	// 冠名礼物
	AddNamingPresentInfo(context.Context, *AddNamingPresentInfoReq) (*AddNamingPresentInfoResp, error)
	UpdateNamingPresentInfo(context.Context, *UpdateNamingPresentInfoReq) (*UpdateNamingPresentInfoResp, error)
	DelNamingPresentInfo(context.Context, *DelNamingPresentInfoReq) (*DelNamingPresentInfoResp, error)
	GetNamingPresentInfoList(context.Context, *GetNamingPresentInfoListReq) (*GetNamingPresentInfoListResp, error)
	GetValidNamingPresentInfos(context.Context, *GetValidNamingPresentInfosReq) (*GetValidNamingPresentInfosResp, error)
	// 送礼
	SendPresent(context.Context, *SendPresentReq) (*SendPresentResp, error)
	BatchSendPresent(context.Context, *BatchSendPresentReq) (*BatchSendPresentResp, error)
	// 查询用户送礼
	GetUserPresentDetailList(context.Context, *GetUserPresentDetailListReq) (*GetUserPresentDetailListResp, error)
	GetUserPresentSendDetailList(context.Context, *GetUserPresentSendDetailListReq) (*GetUserPresentSendDetailListResp, error)
	// 礼物汇总
	GetUserPresentSummary(context.Context, *GetUserPresentSummaryReq) (*GetUserPresentSummaryResp, error)
	GetUserPresentSummaryByItemList(context.Context, *GetUserPresentSummaryByItemListReq) (*GetUserPresentSummaryByItemListResp, error)
	// 对账相关
	GetPresentOrderStatus(context.Context, *GetPresentOrderStatusReq) (*GetPresentOrderStatusResp, error)
	GetOrderLogByOrderIds(context.Context, *GetOrderLogByOrderIdsReq) (*GetOrderLogByOrderIdsResp, error)
	// 流光
	GetPresentFlowConfigById(context.Context, *GetPresentFlowConfigByIdReq) (*GetPresentFlowConfigByIdResp, error)
	GetPresentFlowConfigList(context.Context, *GetPresentFlowConfigListReq) (*GetPresentFlowConfigListResp, error)
	GetPresentFlowConfigUpdateTime(context.Context, *GetPresentFlowConfigUpdateTimeReq) (*GetPresentFlowConfigUpdateTimeResp, error)
	AddPresentFlowConfig(context.Context, *AddPresentFlowConfigReq) (*AddPresentFlowConfigResp, error)
	DelPresentFlowConfig(context.Context, *DelPresentFlowConfigReq) (*DelPresentFlowConfigResp, error)
	UpdatePresentFlowConfig(context.Context, *UpdatePresentFlowConfigReq) (*UpdatePresentFlowConfigResp, error)
	// 动效模版
	AddDynamicEffectTemplate(context.Context, *AddDynamicEffectTemplateReq) (*AddDynamicEffectTemplateResp, error)
	UpdateDynamicEffectTemplate(context.Context, *UpdateDynamicEffectTemplateReq) (*UpdateDynamicEffectTemplateResp, error)
	DelDynamicEffectTemplate(context.Context, *DelDynamicEffectTemplateReq) (*DelDynamicEffectTemplateResp, error)
	GetDynamicEffectTemplateList(context.Context, *GetDynamicEffectTemplateListReq) (*GetDynamicEffectTemplateListResp, error)
	AddPresentEffectTemplateConfig(context.Context, *AddPresentEffectTemplateConfigReq) (*AddPresentEffectTemplateConfigResp, error)
	UpdatePresentEffectTemplateConfig(context.Context, *UpdatePresentEffectTemplateConfigReq) (*UpdatePresentEffectTemplateConfigResp, error)
	DelPresentEffectTemplateConfig(context.Context, *DelPresentEffectTemplateConfigReq) (*DelPresentEffectTemplateConfigResp, error)
	GetPresentEffectTemplateConfigList(context.Context, *GetPresentEffectTemplateConfigListReq) (*GetPresentEffectTemplateConfigListResp, error)
	GetPresentDynamicEffectTemplateConfig(context.Context, *GetPresentDynamicEffectTemplateConfigReq) (*GetPresentDynamicEffectTemplateConfigResp, error)
	GetPresentDynamicTemplateConfUpdateTime(context.Context, *GetPresentDynamicTemplateConfUpdateTimeReq) (*GetPresentDynamicTemplateConfUpdateTimeResp, error)
	GetPresentDETConfigById(context.Context, *GetPresentDETConfigByIdReq) (*GetPresentDETConfigByIdResp, error)
	// 娱乐玩法来源
	AddChanceItemSource(context.Context, *AddChanceItemSourceReq) (*AddChanceItemSourceResp, error)
	DelChanceItemSource(context.Context, *DelChanceItemSourceReq) (*DelChanceItemSourceResp, error)
	// 礼物角标
	AddPresentMarkConfig(context.Context, *AddPresentMarkConfigReq) (*AddPresentMarkConfigResp, error)
	BatchAddPresentMarkConfig(context.Context, *BatchAddPresentMarkConfigReq) (*BatchAddPresentMarkConfigResp, error)
	DelPresentMarkConfig(context.Context, *DelPresentMarkConfigReq) (*DelPresentMarkConfigResp, error)
	GetPresentMarkConfigList(context.Context, *GetPresentMarkConfigListReq) (*GetPresentMarkConfigListResp, error)
	GetPresentMarkIconByPresentId(context.Context, *GetPresentMarkIconByPresentIdReq) (*GetPresentMarkIconByPresentIdResp, error)
	// 后台专用
	GetPresentConfigListBackend(context.Context, *GetPresentConfigListBackendReq) (*GetPresentConfigListBackendResp, error)
	GetPresentConfigByIdBackend(context.Context, *GetPresentConfigByIdBackendReq) (*GetPresentConfigByIdBackendResp, error)
	UpdatePresentConfigBackend(context.Context, *UpdatePresentConfigBackendReq) (*UpdatePresentConfigBackendResp, error)
	DelPresentConfigBackend(context.Context, *DelPresentConfigBackendReq) (*DelPresentConfigBackendResp, error)
	AddPresentConfigBackend(context.Context, *AddPresentConfigBackendReq) (*AddPresentConfigBackendResp, error)
	GetUserPresentSend(context.Context, *GetUserPresentSendReq) (*GetUserPresentSendResp, error)
	GetUserPresentReceive(context.Context, *GetUserPresentReceiveReq) (*GetUserPresentReceiveResp, error)
	GetAllFellowPresent(context.Context, *GetAllFellowPresentReq) (*GetAllFellowPresentResp, error)
}

func RegisterUserPresentGOServer(s *grpc.Server, srv UserPresentGOServer) {
	s.RegisterService(&_UserPresentGO_serviceDesc, srv)
}

func _UserPresentGO_GetPresentConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigList(ctx, req.(*GetPresentConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_AddPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).AddPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/AddPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).AddPresentConfig(ctx, req.(*AddPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_UpdatePresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).UpdatePresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/UpdatePresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).UpdatePresentConfig(ctx, req.(*UpdatePresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_DelPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).DelPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/DelPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).DelPresentConfig(ctx, req.(*DelPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentConfigListV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigListV3Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigListV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigListV3",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigListV3(ctx, req.(*GetPresentConfigListV3Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentConfigById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigById(ctx, req.(*GetPresentConfigByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentConfigListByIdList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigListByIdListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigListByIdList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigListByIdList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigListByIdList(ctx, req.(*GetPresentConfigListByIdListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentConfigUpdateTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigUpdateTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigUpdateTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigUpdateTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigUpdateTime(ctx, req.(*GetPresentConfigUpdateTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetLivePresentOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLivePresentOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetLivePresentOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetLivePresentOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetLivePresentOrderList(ctx, req.(*GetLivePresentOrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_RecordSceneSendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordSceneSendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).RecordSceneSendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/RecordSceneSendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).RecordSceneSendPresent(ctx, req.(*RecordSceneSendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetScenePresentSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScenePresentSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetScenePresentSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetScenePresentSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetScenePresentSummary(ctx, req.(*GetScenePresentSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_ClearScenePresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearScenePresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).ClearScenePresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/ClearScenePresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).ClearScenePresent(ctx, req.(*ClearScenePresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetScenePresentDetailList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScenePresentDetailListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetScenePresentDetailList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetScenePresentDetailList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetScenePresentDetailList(ctx, req.(*GetScenePresentDetailListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_AddNamingPresentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddNamingPresentInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).AddNamingPresentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/AddNamingPresentInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).AddNamingPresentInfo(ctx, req.(*AddNamingPresentInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_UpdateNamingPresentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNamingPresentInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).UpdateNamingPresentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/UpdateNamingPresentInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).UpdateNamingPresentInfo(ctx, req.(*UpdateNamingPresentInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_DelNamingPresentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelNamingPresentInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).DelNamingPresentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/DelNamingPresentInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).DelNamingPresentInfo(ctx, req.(*DelNamingPresentInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetNamingPresentInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNamingPresentInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetNamingPresentInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetNamingPresentInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetNamingPresentInfoList(ctx, req.(*GetNamingPresentInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetValidNamingPresentInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetValidNamingPresentInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetValidNamingPresentInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetValidNamingPresentInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetValidNamingPresentInfos(ctx, req.(*GetValidNamingPresentInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_SendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).SendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/SendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).SendPresent(ctx, req.(*SendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_BatchSendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSendPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).BatchSendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/BatchSendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).BatchSendPresent(ctx, req.(*BatchSendPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetUserPresentDetailList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresentDetailListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetUserPresentDetailList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetUserPresentDetailList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetUserPresentDetailList(ctx, req.(*GetUserPresentDetailListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetUserPresentSendDetailList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresentSendDetailListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetUserPresentSendDetailList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetUserPresentSendDetailList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetUserPresentSendDetailList(ctx, req.(*GetUserPresentSendDetailListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetUserPresentSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresentSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetUserPresentSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetUserPresentSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetUserPresentSummary(ctx, req.(*GetUserPresentSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetUserPresentSummaryByItemList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresentSummaryByItemListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetUserPresentSummaryByItemList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetUserPresentSummaryByItemList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetUserPresentSummaryByItemList(ctx, req.(*GetUserPresentSummaryByItemListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentOrderStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentOrderStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentOrderStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentOrderStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentOrderStatus(ctx, req.(*GetPresentOrderStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetOrderLogByOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderLogByOrderIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetOrderLogByOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetOrderLogByOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetOrderLogByOrderIds(ctx, req.(*GetOrderLogByOrderIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentFlowConfigById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentFlowConfigByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentFlowConfigById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentFlowConfigById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentFlowConfigById(ctx, req.(*GetPresentFlowConfigByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentFlowConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentFlowConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentFlowConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentFlowConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentFlowConfigList(ctx, req.(*GetPresentFlowConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentFlowConfigUpdateTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentFlowConfigUpdateTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentFlowConfigUpdateTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentFlowConfigUpdateTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentFlowConfigUpdateTime(ctx, req.(*GetPresentFlowConfigUpdateTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_AddPresentFlowConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPresentFlowConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).AddPresentFlowConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/AddPresentFlowConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).AddPresentFlowConfig(ctx, req.(*AddPresentFlowConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_DelPresentFlowConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPresentFlowConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).DelPresentFlowConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/DelPresentFlowConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).DelPresentFlowConfig(ctx, req.(*DelPresentFlowConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_UpdatePresentFlowConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresentFlowConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).UpdatePresentFlowConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/UpdatePresentFlowConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).UpdatePresentFlowConfig(ctx, req.(*UpdatePresentFlowConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_AddDynamicEffectTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDynamicEffectTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).AddDynamicEffectTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/AddDynamicEffectTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).AddDynamicEffectTemplate(ctx, req.(*AddDynamicEffectTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_UpdateDynamicEffectTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDynamicEffectTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).UpdateDynamicEffectTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/UpdateDynamicEffectTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).UpdateDynamicEffectTemplate(ctx, req.(*UpdateDynamicEffectTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_DelDynamicEffectTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelDynamicEffectTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).DelDynamicEffectTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/DelDynamicEffectTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).DelDynamicEffectTemplate(ctx, req.(*DelDynamicEffectTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetDynamicEffectTemplateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDynamicEffectTemplateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetDynamicEffectTemplateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetDynamicEffectTemplateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetDynamicEffectTemplateList(ctx, req.(*GetDynamicEffectTemplateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_AddPresentEffectTemplateConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPresentEffectTemplateConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).AddPresentEffectTemplateConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/AddPresentEffectTemplateConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).AddPresentEffectTemplateConfig(ctx, req.(*AddPresentEffectTemplateConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_UpdatePresentEffectTemplateConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresentEffectTemplateConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).UpdatePresentEffectTemplateConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/UpdatePresentEffectTemplateConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).UpdatePresentEffectTemplateConfig(ctx, req.(*UpdatePresentEffectTemplateConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_DelPresentEffectTemplateConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPresentEffectTemplateConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).DelPresentEffectTemplateConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/DelPresentEffectTemplateConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).DelPresentEffectTemplateConfig(ctx, req.(*DelPresentEffectTemplateConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentEffectTemplateConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentEffectTemplateConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentEffectTemplateConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentEffectTemplateConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentEffectTemplateConfigList(ctx, req.(*GetPresentEffectTemplateConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentDynamicEffectTemplateConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentDynamicEffectTemplateConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentDynamicEffectTemplateConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentDynamicEffectTemplateConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentDynamicEffectTemplateConfig(ctx, req.(*GetPresentDynamicEffectTemplateConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentDynamicTemplateConfUpdateTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentDynamicTemplateConfUpdateTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentDynamicTemplateConfUpdateTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentDynamicTemplateConfUpdateTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentDynamicTemplateConfUpdateTime(ctx, req.(*GetPresentDynamicTemplateConfUpdateTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentDETConfigById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentDETConfigByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentDETConfigById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentDETConfigById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentDETConfigById(ctx, req.(*GetPresentDETConfigByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_AddChanceItemSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChanceItemSourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).AddChanceItemSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/AddChanceItemSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).AddChanceItemSource(ctx, req.(*AddChanceItemSourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_DelChanceItemSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelChanceItemSourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).DelChanceItemSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/DelChanceItemSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).DelChanceItemSource(ctx, req.(*DelChanceItemSourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_AddPresentMarkConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPresentMarkConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).AddPresentMarkConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/AddPresentMarkConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).AddPresentMarkConfig(ctx, req.(*AddPresentMarkConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_BatchAddPresentMarkConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddPresentMarkConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).BatchAddPresentMarkConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/BatchAddPresentMarkConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).BatchAddPresentMarkConfig(ctx, req.(*BatchAddPresentMarkConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_DelPresentMarkConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPresentMarkConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).DelPresentMarkConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/DelPresentMarkConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).DelPresentMarkConfig(ctx, req.(*DelPresentMarkConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentMarkConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentMarkConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentMarkConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentMarkConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentMarkConfigList(ctx, req.(*GetPresentMarkConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentMarkIconByPresentId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentMarkIconByPresentIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentMarkIconByPresentId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentMarkIconByPresentId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentMarkIconByPresentId(ctx, req.(*GetPresentMarkIconByPresentIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentConfigListBackend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigListBackendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigListBackend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigListBackend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigListBackend(ctx, req.(*GetPresentConfigListBackendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetPresentConfigByIdBackend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentConfigByIdBackendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetPresentConfigByIdBackend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetPresentConfigByIdBackend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetPresentConfigByIdBackend(ctx, req.(*GetPresentConfigByIdBackendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_UpdatePresentConfigBackend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresentConfigBackendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).UpdatePresentConfigBackend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/UpdatePresentConfigBackend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).UpdatePresentConfigBackend(ctx, req.(*UpdatePresentConfigBackendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_DelPresentConfigBackend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPresentConfigBackendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).DelPresentConfigBackend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/DelPresentConfigBackend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).DelPresentConfigBackend(ctx, req.(*DelPresentConfigBackendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_AddPresentConfigBackend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPresentConfigBackendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).AddPresentConfigBackend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/AddPresentConfigBackend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).AddPresentConfigBackend(ctx, req.(*AddPresentConfigBackendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetUserPresentSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresentSendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetUserPresentSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetUserPresentSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetUserPresentSend(ctx, req.(*GetUserPresentSendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetUserPresentReceive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPresentReceiveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetUserPresentReceive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetUserPresentReceive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetUserPresentReceive(ctx, req.(*GetUserPresentReceiveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPresentGO_GetAllFellowPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllFellowPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPresentGOServer).GetAllFellowPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userpresent_go.UserPresentGO/GetAllFellowPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPresentGOServer).GetAllFellowPresent(ctx, req.(*GetAllFellowPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserPresentGO_serviceDesc = grpc.ServiceDesc{
	ServiceName: "userpresent_go.UserPresentGO",
	HandlerType: (*UserPresentGOServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPresentConfigList",
			Handler:    _UserPresentGO_GetPresentConfigList_Handler,
		},
		{
			MethodName: "AddPresentConfig",
			Handler:    _UserPresentGO_AddPresentConfig_Handler,
		},
		{
			MethodName: "UpdatePresentConfig",
			Handler:    _UserPresentGO_UpdatePresentConfig_Handler,
		},
		{
			MethodName: "DelPresentConfig",
			Handler:    _UserPresentGO_DelPresentConfig_Handler,
		},
		{
			MethodName: "GetPresentConfigListV3",
			Handler:    _UserPresentGO_GetPresentConfigListV3_Handler,
		},
		{
			MethodName: "GetPresentConfigById",
			Handler:    _UserPresentGO_GetPresentConfigById_Handler,
		},
		{
			MethodName: "GetPresentConfigListByIdList",
			Handler:    _UserPresentGO_GetPresentConfigListByIdList_Handler,
		},
		{
			MethodName: "GetPresentConfigUpdateTime",
			Handler:    _UserPresentGO_GetPresentConfigUpdateTime_Handler,
		},
		{
			MethodName: "GetLivePresentOrderList",
			Handler:    _UserPresentGO_GetLivePresentOrderList_Handler,
		},
		{
			MethodName: "RecordSceneSendPresent",
			Handler:    _UserPresentGO_RecordSceneSendPresent_Handler,
		},
		{
			MethodName: "GetScenePresentSummary",
			Handler:    _UserPresentGO_GetScenePresentSummary_Handler,
		},
		{
			MethodName: "ClearScenePresent",
			Handler:    _UserPresentGO_ClearScenePresent_Handler,
		},
		{
			MethodName: "GetScenePresentDetailList",
			Handler:    _UserPresentGO_GetScenePresentDetailList_Handler,
		},
		{
			MethodName: "AddNamingPresentInfo",
			Handler:    _UserPresentGO_AddNamingPresentInfo_Handler,
		},
		{
			MethodName: "UpdateNamingPresentInfo",
			Handler:    _UserPresentGO_UpdateNamingPresentInfo_Handler,
		},
		{
			MethodName: "DelNamingPresentInfo",
			Handler:    _UserPresentGO_DelNamingPresentInfo_Handler,
		},
		{
			MethodName: "GetNamingPresentInfoList",
			Handler:    _UserPresentGO_GetNamingPresentInfoList_Handler,
		},
		{
			MethodName: "GetValidNamingPresentInfos",
			Handler:    _UserPresentGO_GetValidNamingPresentInfos_Handler,
		},
		{
			MethodName: "SendPresent",
			Handler:    _UserPresentGO_SendPresent_Handler,
		},
		{
			MethodName: "BatchSendPresent",
			Handler:    _UserPresentGO_BatchSendPresent_Handler,
		},
		{
			MethodName: "GetUserPresentDetailList",
			Handler:    _UserPresentGO_GetUserPresentDetailList_Handler,
		},
		{
			MethodName: "GetUserPresentSendDetailList",
			Handler:    _UserPresentGO_GetUserPresentSendDetailList_Handler,
		},
		{
			MethodName: "GetUserPresentSummary",
			Handler:    _UserPresentGO_GetUserPresentSummary_Handler,
		},
		{
			MethodName: "GetUserPresentSummaryByItemList",
			Handler:    _UserPresentGO_GetUserPresentSummaryByItemList_Handler,
		},
		{
			MethodName: "GetPresentOrderStatus",
			Handler:    _UserPresentGO_GetPresentOrderStatus_Handler,
		},
		{
			MethodName: "GetOrderLogByOrderIds",
			Handler:    _UserPresentGO_GetOrderLogByOrderIds_Handler,
		},
		{
			MethodName: "GetPresentFlowConfigById",
			Handler:    _UserPresentGO_GetPresentFlowConfigById_Handler,
		},
		{
			MethodName: "GetPresentFlowConfigList",
			Handler:    _UserPresentGO_GetPresentFlowConfigList_Handler,
		},
		{
			MethodName: "GetPresentFlowConfigUpdateTime",
			Handler:    _UserPresentGO_GetPresentFlowConfigUpdateTime_Handler,
		},
		{
			MethodName: "AddPresentFlowConfig",
			Handler:    _UserPresentGO_AddPresentFlowConfig_Handler,
		},
		{
			MethodName: "DelPresentFlowConfig",
			Handler:    _UserPresentGO_DelPresentFlowConfig_Handler,
		},
		{
			MethodName: "UpdatePresentFlowConfig",
			Handler:    _UserPresentGO_UpdatePresentFlowConfig_Handler,
		},
		{
			MethodName: "AddDynamicEffectTemplate",
			Handler:    _UserPresentGO_AddDynamicEffectTemplate_Handler,
		},
		{
			MethodName: "UpdateDynamicEffectTemplate",
			Handler:    _UserPresentGO_UpdateDynamicEffectTemplate_Handler,
		},
		{
			MethodName: "DelDynamicEffectTemplate",
			Handler:    _UserPresentGO_DelDynamicEffectTemplate_Handler,
		},
		{
			MethodName: "GetDynamicEffectTemplateList",
			Handler:    _UserPresentGO_GetDynamicEffectTemplateList_Handler,
		},
		{
			MethodName: "AddPresentEffectTemplateConfig",
			Handler:    _UserPresentGO_AddPresentEffectTemplateConfig_Handler,
		},
		{
			MethodName: "UpdatePresentEffectTemplateConfig",
			Handler:    _UserPresentGO_UpdatePresentEffectTemplateConfig_Handler,
		},
		{
			MethodName: "DelPresentEffectTemplateConfig",
			Handler:    _UserPresentGO_DelPresentEffectTemplateConfig_Handler,
		},
		{
			MethodName: "GetPresentEffectTemplateConfigList",
			Handler:    _UserPresentGO_GetPresentEffectTemplateConfigList_Handler,
		},
		{
			MethodName: "GetPresentDynamicEffectTemplateConfig",
			Handler:    _UserPresentGO_GetPresentDynamicEffectTemplateConfig_Handler,
		},
		{
			MethodName: "GetPresentDynamicTemplateConfUpdateTime",
			Handler:    _UserPresentGO_GetPresentDynamicTemplateConfUpdateTime_Handler,
		},
		{
			MethodName: "GetPresentDETConfigById",
			Handler:    _UserPresentGO_GetPresentDETConfigById_Handler,
		},
		{
			MethodName: "AddChanceItemSource",
			Handler:    _UserPresentGO_AddChanceItemSource_Handler,
		},
		{
			MethodName: "DelChanceItemSource",
			Handler:    _UserPresentGO_DelChanceItemSource_Handler,
		},
		{
			MethodName: "AddPresentMarkConfig",
			Handler:    _UserPresentGO_AddPresentMarkConfig_Handler,
		},
		{
			MethodName: "BatchAddPresentMarkConfig",
			Handler:    _UserPresentGO_BatchAddPresentMarkConfig_Handler,
		},
		{
			MethodName: "DelPresentMarkConfig",
			Handler:    _UserPresentGO_DelPresentMarkConfig_Handler,
		},
		{
			MethodName: "GetPresentMarkConfigList",
			Handler:    _UserPresentGO_GetPresentMarkConfigList_Handler,
		},
		{
			MethodName: "GetPresentMarkIconByPresentId",
			Handler:    _UserPresentGO_GetPresentMarkIconByPresentId_Handler,
		},
		{
			MethodName: "GetPresentConfigListBackend",
			Handler:    _UserPresentGO_GetPresentConfigListBackend_Handler,
		},
		{
			MethodName: "GetPresentConfigByIdBackend",
			Handler:    _UserPresentGO_GetPresentConfigByIdBackend_Handler,
		},
		{
			MethodName: "UpdatePresentConfigBackend",
			Handler:    _UserPresentGO_UpdatePresentConfigBackend_Handler,
		},
		{
			MethodName: "DelPresentConfigBackend",
			Handler:    _UserPresentGO_DelPresentConfigBackend_Handler,
		},
		{
			MethodName: "AddPresentConfigBackend",
			Handler:    _UserPresentGO_AddPresentConfigBackend_Handler,
		},
		{
			MethodName: "GetUserPresentSend",
			Handler:    _UserPresentGO_GetUserPresentSend_Handler,
		},
		{
			MethodName: "GetUserPresentReceive",
			Handler:    _UserPresentGO_GetUserPresentReceive_Handler,
		},
		{
			MethodName: "GetAllFellowPresent",
			Handler:    _UserPresentGO_GetAllFellowPresent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/userpresent-go/userpresent-go.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/userpresent-go/userpresent-go.proto", fileDescriptor_userpresent_go_dc34c46671c2d6c2)
}

var fileDescriptor_userpresent_go_dc34c46671c2d6c2 = []byte{
	// 6277 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x7d, 0xdd, 0x6f, 0x1c, 0xc9,
	0x71, 0x38, 0x77, 0x97, 0x1f, 0xbb, 0x45, 0x2e, 0xb9, 0x1c, 0x52, 0xe2, 0x72, 0x79, 0x92, 0xa8,
	0xd1, 0x17, 0x25, 0xdd, 0x49, 0x67, 0xde, 0x49, 0xbf, 0x9f, 0x7d, 0xf0, 0x07, 0x29, 0x52, 0x3a,
	0xda, 0x12, 0x25, 0x0c, 0x29, 0x9d, 0x73, 0x8e, 0x3d, 0x1e, 0xee, 0x0e, 0x57, 0x63, 0xce, 0xce,
	0xcc, 0x6d, 0xcf, 0x4a, 0xa2, 0x01, 0x07, 0x01, 0x82, 0x04, 0x09, 0xe0, 0x97, 0x04, 0x71, 0x1e,
	0x12, 0x23, 0x2f, 0x76, 0x02, 0x3f, 0x26, 0x81, 0x1f, 0xfd, 0x92, 0xe4, 0x21, 0x40, 0x90, 0xe7,
	0xfc, 0x05, 0x01, 0xf2, 0x66, 0x03, 0x41, 0x80, 0xbc, 0x06, 0x5d, 0xdd, 0x33, 0xd3, 0x33, 0xdd,
	0x33, 0xbb, 0x94, 0x78, 0x36, 0x1c, 0xe8, 0x8d, 0x5b, 0x5d, 0xdd, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d,
	0x55, 0x5d, 0x35, 0x84, 0xf5, 0x30, 0xbc, 0xfd, 0xd9, 0xc0, 0x69, 0x1f, 0x11, 0xc7, 0x7d, 0x61,
	0xf7, 0x6f, 0x0f, 0x88, 0xdd, 0x0f, 0xfa, 0x36, 0xb1, 0xbd, 0xf0, 0xbd, 0xae, 0x9f, 0xf9, 0x79,
	0x2b, 0xe8, 0xfb, 0xa1, 0xaf, 0xcd, 0x0a, 0x50, 0xb3, 0xeb, 0xeb, 0x5f, 0x86, 0xf9, 0xbd, 0xf0,
	0x9e, 0xef, 0x1d, 0x3a, 0xdd, 0x1d, 0x9f, 0x6c, 0xbf, 0x0a, 0x6d, 0xaf, 0xa3, 0xad, 0x41, 0xe3,
	0x85, 0xd3, 0xb1, 0x7d, 0xd3, 0x3e, 0x3c, 0xb4, 0xdb, 0xa1, 0x39, 0xe8, 0xbb, 0xcd, 0xd2, 0x6a,
	0x69, 0x6d, 0xc6, 0x98, 0x45, 0xf8, 0x36, 0x82, 0x9f, 0xf6, 0x5d, 0xfd, 0x9f, 0xa7, 0x60, 0x79,
	0x2f, 0x7c, 0xc2, 0xc6, 0xdb, 0x09, 0xed, 0x1e, 0x1b, 0x8a, 0x8f, 0xb3, 0x04, 0x53, 0x4e, 0x68,
	0xf7, 0x4c, 0xa7, 0x83, 0xdd, 0xeb, 0xc6, 0x24, 0xfd, 0xb9, 0xa3, 0x9e, 0xa0, 0xac, 0x9a, 0x40,
	0xbb, 0x00, 0xd3, 0xe4, 0xb9, 0xff, 0x92, 0x23, 0x36, 0x2b, 0x38, 0x0c, 0x50, 0x10, 0xc3, 0xd1,
	0x6e, 0xc1, 0xc2, 0xc0, 0x43, 0x94, 0x03, 0x2b, 0x6c, 0x3f, 0x37, 0xfd, 0x20, 0x74, 0x7c, 0xaf,
	0x39, 0xbe, 0x5a, 0x5a, 0xab, 0x1a, 0xf3, 0xac, 0x69, 0x93, 0xb6, 0x3c, 0xc6, 0x06, 0xa4, 0x89,
	0x98, 0xa1, 0x4d, 0xc2, 0xe6, 0x04, 0xe2, 0x4c, 0x3a, 0x64, 0xdf, 0x26, 0x21, 0x6d, 0x38, 0x74,
	0xfd, 0x97, 0x94, 0xd8, 0x49, 0x46, 0x2c, 0xfd, 0xb9, 0xd3, 0xd1, 0xbe, 0x06, 0xe0, 0xf8, 0xc4,
	0xb4, 0x71, 0x4d, 0xcd, 0xa9, 0xd5, 0xd2, 0xda, 0xf4, 0xfa, 0xc5, 0x5b, 0x69, 0x3e, 0xde, 0x92,
	0x98, 0x68, 0xd4, 0x9c, 0x98, 0x9f, 0xe7, 0x00, 0x3c, 0x3f, 0x74, 0x0e, 0x8f, 0x4d, 0xcb, 0x75,
	0x9b, 0x55, 0x9c, 0xb6, 0xc6, 0x20, 0x1b, 0xae, 0xab, 0x35, 0xa0, 0x12, 0x5a, 0xdd, 0x66, 0x0d,
	0x67, 0xa5, 0x7f, 0x6a, 0x57, 0x60, 0xf6, 0xd0, 0xef, 0xb7, 0x6d, 0x93, 0xd8, 0x5e, 0xc7, 0x3a,
	0x70, 0xed, 0x26, 0x60, 0xa7, 0x3a, 0x42, 0xf7, 0x38, 0x90, 0xa2, 0x79, 0xfe, 0x81, 0xe3, 0x3a,
	0xe1, 0xb1, 0xe9, 0xda, 0x2f, 0x6c, 0xb7, 0x39, 0x8d, 0x63, 0xd4, 0x23, 0xe8, 0x43, 0x0a, 0xd4,
	0xde, 0x87, 0x45, 0xce, 0xa2, 0x88, 0x60, 0xf2, 0xdc, 0x76, 0x0f, 0x9b, 0x33, 0x38, 0xa6, 0xc6,
	0xda, 0xf8, 0x1e, 0xee, 0xd1, 0x16, 0xed, 0x2a, 0xcc, 0x09, 0x5c, 0x37, 0xe9, 0xba, 0xeb, 0x8c,
	0x80, 0x84, 0xf3, 0xdb, 0x4c, 0x50, 0x12, 0x14, 0xb3, 0x63, 0xbb, 0xd6, 0x71, 0x73, 0x16, 0x11,
	0x67, 0xed, 0x08, 0x69, 0x8b, 0x42, 0xb5, 0x8f, 0x60, 0xba, 0x3d, 0x20, 0xa1, 0xdf, 0x33, 0x43,
	0xfb, 0x55, 0xd8, 0x9c, 0x5b, 0xad, 0xac, 0x4d, 0xaf, 0xb7, 0xb2, 0x5c, 0xbc, 0x87, 0x28, 0xfb,
	0xf6, 0xab, 0xd0, 0x80, 0x76, 0xfc, 0xb7, 0xa6, 0x43, 0x9d, 0xf4, 0x2c, 0xd7, 0x35, 0x5f, 0x58,
	0x01, 0xca, 0x4a, 0x63, 0xb5, 0xb4, 0x56, 0x33, 0xa6, 0x11, 0xf8, 0xcc, 0x0a, 0xa8, 0xa0, 0xa4,
	0x70, 0x7a, 0x9d, 0x3b, 0xcd, 0xf9, 0x34, 0xce, 0xa3, 0xce, 0x1d, 0xed, 0x32, 0xcc, 0xf6, 0x9c,
	0xb6, 0x28, 0x74, 0x1a, 0x22, 0xcd, 0xf4, 0x9c, 0x76, 0x22, 0x72, 0x69, 0x2c, 0x3a, 0xd4, 0x42,
	0x06, 0x8b, 0x8e, 0x45, 0xb7, 0x68, 0x40, 0x1c, 0xdf, 0x8b, 0x98, 0xda, 0x5c, 0xe4, 0x5b, 0x84,
	0x50, 0xce, 0x4e, 0xca, 0x49, 0x87, 0x98, 0x07, 0xfe, 0x2b, 0xf3, 0xa0, 0x6f, 0x5b, 0x47, 0x8e,
	0xd7, 0x6d, 0x9e, 0x61, 0x78, 0x0e, 0xd9, 0xf4, 0x5f, 0x6d, 0x72, 0x20, 0x15, 0x91, 0x43, 0xcb,
	0x23, 0x7c, 0x1b, 0xcf, 0xe2, 0x36, 0xd6, 0x28, 0x84, 0x6d, 0xe1, 0x55, 0x98, 0xf3, 0xfb, 0x4e,
	0xd7, 0xf1, 0x4c, 0xa7, 0xed, 0x7b, 0x48, 0xfa, 0x12, 0x12, 0x55, 0x67, 0xe0, 0x9d, 0xb6, 0xef,
	0x51, 0xda, 0x97, 0x60, 0xaa, 0x67, 0xf5, 0x8f, 0xa8, 0x10, 0x37, 0x57, 0x4b, 0x6b, 0xe3, 0xc6,
	0x24, 0xfd, 0xb9, 0xd3, 0xd1, 0x56, 0xa0, 0x86, 0x0d, 0x9e, 0xd5, 0xb3, 0x9b, 0xcb, 0xd8, 0xb5,
	0x4a, 0x01, 0xbb, 0x56, 0xcf, 0xd6, 0xd7, 0x01, 0x12, 0xce, 0x53, 0x71, 0x3c, 0xb2, 0x8f, 0xf1,
	0xc4, 0xd6, 0x0c, 0xfa, 0xa7, 0xa6, 0xc1, 0x38, 0xee, 0x5a, 0x79, 0xb5, 0xb2, 0x56, 0x33, 0xf0,
	0x6f, 0xfd, 0x97, 0x15, 0x58, 0x50, 0x9c, 0xfc, 0xfc, 0x33, 0xaf, 0xc1, 0x38, 0x4e, 0x5e, 0xc6,
	0x71, 0xf1, 0x6f, 0x6d, 0x19, 0xaa, 0xf1, 0x7a, 0x2a, 0x08, 0x9f, 0x72, 0xf8, 0x4a, 0x16, 0x61,
	0x22, 0xe8, 0x3b, 0x6d, 0x1b, 0x4f, 0x72, 0xdd, 0x60, 0x3f, 0x28, 0x94, 0xb4, 0xfd, 0xbe, 0x8d,
	0x67, 0xb7, 0x6e, 0xb0, 0x1f, 0x14, 0xda, 0x7e, 0x6e, 0xf5, 0x7b, 0xfc, 0xe0, 0xb2, 0x1f, 0x74,
	0xc2, 0xbe, 0xe5, 0x1d, 0xe1, 0x89, 0xad, 0x1b, 0xf8, 0xb7, 0x76, 0x11, 0x66, 0xf8, 0xbe, 0x1e,
	0xd8, 0x5d, 0xc7, 0xc3, 0xb3, 0x58, 0x37, 0xa6, 0x19, 0x6c, 0x93, 0x82, 0xe8, 0x4e, 0x08, 0x62,
	0xcf, 0x0e, 0x65, 0x2d, 0x96, 0x66, 0xaa, 0x90, 0x06, 0x41, 0xc7, 0x0a, 0x6d, 0x33, 0x74, 0x7a,
	0xec, 0x5c, 0xd6, 0x0d, 0x60, 0xa0, 0x7d, 0xa7, 0x67, 0x53, 0x84, 0x76, 0xdf, 0x8e, 0x11, 0xd8,
	0x89, 0x04, 0x06, 0x42, 0x84, 0x33, 0x30, 0xe9, 0x10, 0x7a, 0x58, 0xf8, 0x01, 0x9c, 0x70, 0xc8,
	0x96, 0xed, 0xd2, 0x79, 0x71, 0x8d, 0x66, 0x78, 0x1c, 0xd8, 0x78, 0xdc, 0xea, 0x46, 0x0d, 0x21,
	0xfb, 0xc7, 0x81, 0x4d, 0x9b, 0xfb, 0x4e, 0xfb, 0xb9, 0xf9, 0xc2, 0x72, 0x07, 0x36, 0x1e, 0xb2,
	0xba, 0x51, 0xa3, 0x90, 0x67, 0x14, 0xa0, 0x6d, 0xc0, 0x24, 0x57, 0x50, 0x73, 0xa8, 0xa0, 0xae,
	0xcb, 0x0a, 0x2a, 0x47, 0x4b, 0x1b, 0xbc, 0x23, 0xce, 0x60, 0x79, 0x47, 0xe6, 0xa1, 0xeb, 0x5b,
	0x21, 0x1e, 0xb1, 0xb2, 0x51, 0xa3, 0x90, 0xfb, 0x14, 0xa0, 0x7f, 0x02, 0x8b, 0x7b, 0xe1, 0x53,
	0x62, 0xf7, 0x23, 0x4d, 0x31, 0xe8, 0xf5, 0xac, 0xfe, 0x31, 0x15, 0x97, 0x41, 0xbc, 0xd9, 0xf4,
	0x4f, 0x51, 0x04, 0xca, 0x29, 0x11, 0xa0, 0xfb, 0xe4, 0x0f, 0xbc, 0x48, 0x8d, 0xb3, 0x1f, 0xfa,
	0x3f, 0xa0, 0x24, 0x09, 0x23, 0x6f, 0xd9, 0xa1, 0xe5, 0xb8, 0x54, 0x38, 0x0e, 0xfb, 0x7e, 0xcf,
	0x4c, 0x46, 0x9f, 0xa2, 0xbf, 0x9f, 0x3a, 0x48, 0x6a, 0x68, 0xf5, 0xbb, 0x76, 0x88, 0x8d, 0x6c,
	0x92, 0x1a, 0x83, 0xd0, 0xe6, 0x2d, 0x98, 0x46, 0x02, 0xda, 0xb8, 0x4c, 0x9c, 0x6d, 0x7a, 0xfd,
	0xd2, 0x08, 0x1c, 0x31, 0xc0, 0x49, 0x24, 0xf9, 0x22, 0xcc, 0xf4, 0xed, 0xb6, 0xed, 0xbc, 0xe0,
	0x3b, 0xc9, 0x04, 0x71, 0x9a, 0xc3, 0x70, 0x2b, 0xcf, 0x01, 0xf0, 0x89, 0xe8, 0xaa, 0x98, 0x4c,
	0xd6, 0xd8, 0x10, 0x03, 0x2f, 0xa4, 0x87, 0xce, 0xea, 0x74, 0x4c, 0x51, 0x36, 0xab, 0x56, 0xa7,
	0x73, 0x0f, 0xc5, 0x73, 0x19, 0xaa, 0x7e, 0xbf, 0x63, 0xf7, 0x29, 0x9b, 0xa6, 0x98, 0xec, 0xe3,
	0xef, 0x9d, 0x14, 0x03, 0xab, 0x59, 0x06, 0x32, 0xf1, 0xaf, 0x89, 0xe2, 0x4f, 0xef, 0x48, 0xaa,
	0x7f, 0x89, 0x3f, 0xe8, 0xb7, 0x63, 0x91, 0xa4, 0xa0, 0x3d, 0x84, 0xc4, 0x08, 0x3d, 0x3b, 0x7c,
	0xee, 0x77, 0x22, 0x91, 0xa4, 0xa0, 0x47, 0x08, 0xa1, 0xb4, 0x50, 0x42, 0xa9, 0x38, 0xa1, 0x50,
	0xd6, 0x8d, 0x29, 0xab, 0xd3, 0x31, 0x9c, 0xf6, 0x73, 0x2e, 0xad, 0x83, 0xa3, 0x97, 0xfc, 0x06,
	0x98, 0x70, 0xc8, 0xd3, 0xa3, 0x97, 0xfa, 0xb7, 0x60, 0xe9, 0x81, 0x1d, 0x31, 0x90, 0x31, 0xec,
	0xa1, 0x43, 0x42, 0xc3, 0xfe, 0x8c, 0xce, 0x46, 0x45, 0xd8, 0x3c, 0x70, 0xc2, 0x9e, 0x15, 0xf0,
	0xad, 0x03, 0x0a, 0xda, 0x44, 0x48, 0xf6, 0x08, 0x95, 0xb3, 0x47, 0x48, 0xff, 0x01, 0x34, 0xd5,
	0x83, 0x93, 0x40, 0xfb, 0x1a, 0x20, 0x83, 0x4d, 0xd7, 0x21, 0x61, 0xb3, 0x84, 0xd7, 0xc8, 0x48,
	0x3b, 0x5b, 0xa5, 0xbd, 0xe8, 0x28, 0xc3, 0xa7, 0xff, 0x36, 0x2c, 0xab, 0xa6, 0x7f, 0xf6, 0xc1,
	0xe9, 0xac, 0xee, 0xdf, 0x4b, 0xd0, 0xca, 0x1b, 0x9f, 0x04, 0xda, 0x97, 0xe5, 0x05, 0xae, 0x66,
	0x17, 0x98, 0xea, 0xbb, 0x6b, 0xbf, 0x14, 0x56, 0xb7, 0x06, 0x0d, 0xd7, 0x22, 0xa1, 0x29, 0xd3,
	0x30, 0x4b, 0xe1, 0x4f, 0x13, 0x45, 0xf5, 0x18, 0x1a, 0xb6, 0x17, 0xda, 0x7d, 0xf3, 0xc0, 0xb5,
	0xda, 0x47, 0x6c, 0xbe, 0x0a, 0xce, 0x77, 0x25, 0x67, 0xbe, 0x6d, 0x8a, 0xbe, 0x49, 0xb1, 0x29,
	0xb2, 0x31, 0x6b, 0xc7, 0xbf, 0xe9, 0xd4, 0xfa, 0xdf, 0x97, 0x61, 0x61, 0xa3, 0xd3, 0x49, 0x11,
	0x47, 0x59, 0x16, 0x69, 0xfe, 0x52, 0x8e, 0xe6, 0x2f, 0xe7, 0x68, 0xfe, 0x8a, 0xa8, 0xf9, 0xb3,
	0x9a, 0x7b, 0x7c, 0x98, 0xe6, 0x9e, 0xc8, 0x6a, 0xee, 0xe8, 0x3e, 0x98, 0x14, 0xee, 0x83, 0xb4,
	0xd2, 0x9d, 0xca, 0x2a, 0xdd, 0x44, 0xab, 0x56, 0x4f, 0x47, 0xab, 0xd6, 0xb2, 0x5a, 0xf5, 0x77,
	0x61, 0x51, 0x66, 0x19, 0x09, 0xb2, 0x2a, 0xac, 0xf4, 0x5a, 0x2a, 0x4c, 0xbf, 0x05, 0x0b, 0x5b,
	0xb6, 0x2b, 0x6d, 0x48, 0xde, 0x1d, 0xad, 0x9f, 0x85, 0x45, 0x19, 0x9f, 0x04, 0xfa, 0x77, 0xe0,
	0x2c, 0x13, 0x1c, 0x69, 0xa8, 0xd3, 0xa1, 0x73, 0x19, 0x96, 0x94, 0xe3, 0x93, 0x40, 0x5f, 0x97,
	0x15, 0xcd, 0xe6, 0xf1, 0x4e, 0xa7, 0x70, 0x19, 0xdf, 0x96, 0xf5, 0x07, 0xeb, 0x43, 0x02, 0x6d,
	0x43, 0x45, 0xf0, 0xf0, 0x03, 0x26, 0x52, 0xfb, 0x25, 0xb8, 0xa0, 0x3a, 0xbf, 0x74, 0x8a, 0x48,
	0x07, 0xa6, 0x48, 0xab, 0x08, 0xa4, 0xd9, 0xb0, 0x5a, 0xdc, 0x57, 0x45, 0x62, 0xe5, 0xc4, 0x24,
	0xb6, 0x61, 0x45, 0xc5, 0x81, 0xc7, 0x6e, 0xe7, 0x14, 0xa5, 0xeb, 0xbb, 0xf0, 0x8e, 0x6a, 0x92,
	0xd3, 0x53, 0xd5, 0xfa, 0x7f, 0x96, 0xa0, 0x91, 0x5d, 0xa7, 0xb6, 0x09, 0xd3, 0x07, 0x16, 0xb1,
	0xd3, 0xc4, 0x5f, 0xcc, 0x61, 0xcf, 0xa6, 0x45, 0xec, 0x88, 0xf4, 0x83, 0xf8, 0x6f, 0xed, 0x63,
	0xa8, 0x73, 0x55, 0xc1, 0x47, 0x29, 0xab, 0x59, 0x10, 0x29, 0x3e, 0xc4, 0xe5, 0xe3, 0x70, 0x3d,
	0xc4, 0x47, 0xda, 0x86, 0x19, 0xa6, 0x45, 0x53, 0xc6, 0x86, 0x5e, 0xa4, 0x41, 0xf9, 0x38, 0xd3,
	0x76, 0xf2, 0x43, 0xff, 0xef, 0x32, 0xcc, 0x4b, 0x24, 0x7f, 0xce, 0xc6, 0x74, 0x62, 0x88, 0x4e,
	0xe4, 0x1b, 0xa2, 0x93, 0xc5, 0x86, 0xe8, 0x54, 0xd6, 0x10, 0x8d, 0x4d, 0x94, 0xaa, 0xd2, 0x42,
	0xaf, 0x89, 0x16, 0xfa, 0x88, 0x6e, 0x6e, 0xe6, 0x46, 0x9d, 0x1e, 0x66, 0x72, 0xcf, 0x48, 0x26,
	0xb7, 0xe0, 0xf4, 0xd7, 0x45, 0xa7, 0x5f, 0xff, 0xbb, 0x0a, 0x68, 0xf2, 0xd6, 0xe4, 0xf3, 0x3d,
	0x27, 0xda, 0x50, 0xce, 0x8b, 0x36, 0x44, 0x77, 0x4e, 0x05, 0xf5, 0xbe, 0xda, 0x07, 0x39, 0xf9,
	0x4d, 0x26, 0xfb, 0xfd, 0x93, 0x27, 0xf1, 0xfb, 0xa7, 0x4e, 0xe2, 0xf7, 0x57, 0x47, 0xf5, 0xfb,
	0x6b, 0x4a, 0xbf, 0x7f, 0xa8, 0xbb, 0xc4, 0x83, 0x1f, 0xd3, 0x49, 0xf0, 0x23, 0xed, 0x0a, 0xcf,
	0x64, 0x5c, 0x61, 0xfd, 0x27, 0xe3, 0xb0, 0xa0, 0x38, 0x96, 0xbf, 0x96, 0x60, 0x93, 0x10, 0x23,
	0x1a, 0x4f, 0xc5, 0x88, 0xd2, 0x11, 0x9e, 0x89, 0x6c, 0x84, 0x27, 0x13, 0xfd, 0x98, 0x7c, 0xb3,
	0xe8, 0xc7, 0xd4, 0x08, 0xd1, 0x8f, 0xea, 0x28, 0xd1, 0x8f, 0xda, 0x48, 0xd1, 0x0f, 0x50, 0x44,
	0x3f, 0x86, 0x1e, 0x49, 0x45, 0xdc, 0x63, 0x46, 0x15, 0xf7, 0x90, 0xc3, 0x28, 0x75, 0x55, 0x18,
	0xe5, 0x36, 0x2c, 0x3a, 0x3e, 0x31, 0xa5, 0x7d, 0x9c, 0xc5, 0x7d, 0x9c, 0x77, 0x7c, 0xf2, 0x2c,
	0x1d, 0x98, 0xfc, 0x3d, 0x38, 0xa3, 0x34, 0x5a, 0xb5, 0xaf, 0x02, 0x30, 0x7d, 0x8d, 0xda, 0x8d,
	0x4a, 0xca, 0x6c, 0xee, 0xdd, 0x8a, 0x5d, 0xa9, 0xd2, 0x33, 0x6a, 0x76, 0xf4, 0x27, 0x65, 0x50,
	0xd7, 0x39, 0x0c, 0xcd, 0xe4, 0x6a, 0x2b, 0xe3, 0x0d, 0x3f, 0x43, 0xa1, 0x3b, 0xd1, 0xcd, 0xf5,
	0x43, 0x66, 0xe4, 0x3f, 0x74, 0x5e, 0x44, 0x36, 0xcd, 0x63, 0xea, 0xdc, 0x45, 0xf6, 0xc1, 0x19,
	0x98, 0x0c, 0x7d, 0xc1, 0xb3, 0x9d, 0x08, 0x7d, 0xee, 0xd7, 0xb6, 0x9f, 0x5b, 0x9e, 0x67, 0xbb,
	0x89, 0xf3, 0x5c, 0xe3, 0x10, 0x26, 0x65, 0xa8, 0x32, 0x18, 0xd3, 0x99, 0x78, 0xd6, 0x10, 0x82,
	0x3c, 0x5f, 0x86, 0x2a, 0x3d, 0x8e, 0x82, 0xb3, 0x3a, 0x65, 0x7b, 0x1d, 0xf4, 0x39, 0xbe, 0x83,
	0xf6, 0x80, 0x9a, 0x1a, 0x12, 0x50, 0xa6, 0x30, 0x5f, 0xb4, 0xc8, 0xe9, 0xc8, 0xf6, 0x36, 0x6a,
	0x7e, 0x34, 0x88, 0xfe, 0xe7, 0x25, 0x68, 0x64, 0xdb, 0x8b, 0x1c, 0xf8, 0x93, 0x85, 0x08, 0x04,
	0x76, 0x8d, 0x8b, 0xec, 0xca, 0xe8, 0xfd, 0x89, 0xac, 0xde, 0xd7, 0x1d, 0x98, 0xde, 0x0b, 0xf7,
	0xda, 0xb6, 0x67, 0xef, 0x78, 0x87, 0x3e, 0x25, 0x88, 0xd0, 0x1f, 0x89, 0x8e, 0x98, 0xc2, 0xdf,
	0x8c, 0xb5, 0xac, 0x09, 0xc5, 0x82, 0x73, 0x1e, 0x21, 0xb8, 0xe9, 0xab, 0x30, 0x43, 0x06, 0x07,
	0x66, 0xdc, 0x3b, 0x52, 0x0d, 0x83, 0x03, 0x36, 0x7a, 0x47, 0xff, 0x45, 0x09, 0xce, 0xf0, 0xb9,
	0x32, 0x01, 0x92, 0xd7, 0x9f, 0x55, 0xe0, 0x52, 0x45, 0xcd, 0xa5, 0x71, 0x91, 0x4b, 0x59, 0x22,
	0x27, 0xb2, 0x44, 0x6a, 0x4d, 0x98, 0x42, 0xd4, 0xbb, 0x1f, 0xe2, 0x8d, 0x31, 0x6e, 0x44, 0x3f,
	0xf5, 0x9f, 0x97, 0x61, 0x31, 0x4d, 0x7e, 0x12, 0x85, 0x79, 0x4d, 0xea, 0xc5, 0xed, 0xaf, 0xa4,
	0xb7, 0x3f, 0x67, 0x3f, 0x33, 0x66, 0xe9, 0xc4, 0xeb, 0xc5, 0x6d, 0x62, 0xe6, 0x4c, 0x8a, 0xcc,
	0x29, 0x08, 0xb7, 0x64, 0xc4, 0xa8, 0x2a, 0x99, 0x0f, 0x02, 0xdb, 0x6a, 0x69, 0xb6, 0xfd, 0x4d,
	0x19, 0x96, 0x0d, 0xbb, 0xed, 0xf7, 0x3b, 0xc8, 0x3a, 0x6a, 0xb2, 0x70, 0xe2, 0xe8, 0x29, 0x97,
	0x43, 0x63, 0x43, 0x02, 0x57, 0x22, 0x91, 0x95, 0x34, 0x91, 0x19, 0xde, 0x8c, 0xbf, 0x1e, 0x6f,
	0xbe, 0x14, 0x6d, 0x19, 0x1e, 0xf4, 0x09, 0x3c, 0xe8, 0x2b, 0xf2, 0x20, 0xf1, 0x91, 0xe1, 0xfb,
	0x89, 0x91, 0x85, 0x74, 0xb0, 0x6b, 0x52, 0x11, 0xec, 0x22, 0xb1, 0xfa, 0x61, 0x66, 0x61, 0x95,
	0x44, 0xfa, 0xe7, 0x13, 0x0c, 0xa9, 0x28, 0x4e, 0x07, 0x65, 0x53, 0x4c, 0x94, 0xe3, 0x1d, 0xfa,
	0xdc, 0x9e, 0x1f, 0x81, 0x28, 0xfa, 0xa7, 0xfe, 0x5f, 0x4c, 0xcf, 0x2a, 0x47, 0x26, 0x81, 0xf6,
	0x31, 0x3d, 0x12, 0xf8, 0x53, 0x54, 0x6d, 0x57, 0x72, 0x06, 0xcf, 0x0c, 0x30, 0xcd, 0xbb, 0x46,
	0x51, 0xa3, 0xd0, 0x0f, 0x2d, 0x97, 0xdb, 0xbd, 0x3c, 0xac, 0x83, 0x20, 0x66, 0xf8, 0xc6, 0x08,
	0xa2, 0xfe, 0x62, 0x08, 0x8c, 0x41, 0x97, 0xa0, 0x2e, 0x20, 0xdc, 0xfd, 0x10, 0xf7, 0x70, 0xdc,
	0x98, 0x49, 0x50, 0xee, 0x7e, 0x98, 0x20, 0xe1, 0x34, 0x77, 0x3f, 0xc4, 0x43, 0x10, 0x21, 0x3d,
	0x63, 0x30, 0xdd, 0x80, 0xc5, 0x7b, 0xae, 0x6d, 0xf5, 0x45, 0xa2, 0xdf, 0x94, 0x91, 0xff, 0x54,
	0x42, 0x6f, 0x4e, 0xd6, 0x00, 0xd1, 0x95, 0xf5, 0x06, 0x83, 0x67, 0x2e, 0xae, 0x72, 0xd1, 0xc5,
	0x55, 0x49, 0x5d, 0x5c, 0xda, 0x59, 0x98, 0xf4, 0x0f, 0x0f, 0x89, 0x1d, 0xa9, 0x3a, 0xfe, 0x8b,
	0x1e, 0x72, 0xd7, 0xe9, 0x39, 0x51, 0xd0, 0x95, 0xfd, 0xd0, 0x0f, 0xe1, 0x5c, 0xc1, 0x1a, 0x48,
	0xa0, 0x6d, 0xc3, 0x74, 0x07, 0x21, 0xa2, 0x38, 0x5c, 0x2e, 0x16, 0x07, 0x36, 0x84, 0x01, 0x9d,
	0x78, 0x28, 0xfd, 0x1d, 0x68, 0xe5, 0x9d, 0x7a, 0x12, 0xe8, 0x4b, 0x70, 0x46, 0xb1, 0x3d, 0x24,
	0xd0, 0xff, 0xb1, 0x04, 0xf3, 0xbb, 0x56, 0xcf, 0xf1, 0xba, 0xd1, 0x29, 0xa5, 0xcc, 0x99, 0x85,
	0x72, 0xac, 0x24, 0xca, 0x4e, 0x27, 0xd2, 0x1a, 0xe5, 0x54, 0x40, 0x9d, 0x99, 0x1c, 0xf1, 0x3d,
	0x80, 0xb6, 0x06, 0x73, 0x04, 0x70, 0x3c, 0xaa, 0x16, 0x42, 0x9b, 0x5f, 0x08, 0x35, 0xa3, 0xce,
	0xa0, 0xf7, 0x18, 0x90, 0xf2, 0x97, 0xb3, 0x9f, 0x70, 0x7e, 0x4d, 0x31, 0xe6, 0x13, 0xaa, 0x89,
	0x91, 0xf5, 0x24, 0xd2, 0x96, 0x94, 0xf1, 0x84, 0x6a, 0x3c, 0xab, 0xcd, 0x04, 0x99, 0x2b, 0x4b,
	0xfe, 0x53, 0x7f, 0x02, 0x4b, 0x1b, 0x9d, 0x8e, 0xb4, 0x0a, 0x2a, 0x21, 0x77, 0x60, 0x5c, 0x90,
	0x0d, 0xc9, 0x23, 0x97, 0xfb, 0x20, 0xba, 0xde, 0x82, 0xa6, 0x7a, 0x44, 0x12, 0xe8, 0x7b, 0xd0,
	0x62, 0x81, 0xa1, 0xd3, 0x9c, 0xf0, 0x1c, 0xac, 0xe4, 0x0e, 0x4a, 0x02, 0xfd, 0x3a, 0x2c, 0x6d,
	0xd9, 0xae, 0x72, 0xc2, 0xcc, 0x56, 0x51, 0xd2, 0xd5, 0xa8, 0x24, 0xd0, 0x7f, 0x56, 0x42, 0x9b,
	0x4b, 0x6a, 0x8c, 0xce, 0x53, 0x22, 0xd9, 0x25, 0xb5, 0x64, 0x97, 0x05, 0xc9, 0x8e, 0x84, 0xa2,
	0xa2, 0x14, 0x8a, 0xf1, 0x94, 0x50, 0x9c, 0x78, 0xb7, 0xf5, 0x1f, 0xb3, 0xb3, 0x9f, 0x43, 0x2a,
	0x09, 0xb4, 0xaf, 0x40, 0x8d, 0x72, 0x4e, 0x3c, 0x34, 0x23, 0x70, 0xbb, 0xea, 0xf0, 0x31, 0xa8,
	0x6e, 0xf4, 0xec, 0x57, 0xa1, 0xc9, 0x17, 0xcc, 0x95, 0x27, 0x05, 0x3d, 0x66, 0x8b, 0x5e, 0x81,
	0x1a, 0xd7, 0x8d, 0xb1, 0xea, 0xac, 0x32, 0xbd, 0xe8, 0x85, 0xfa, 0x05, 0x3c, 0xd5, 0xcf, 0x2c,
	0xd7, 0x91, 0xa5, 0x84, 0x18, 0xf6, 0x67, 0xfa, 0x77, 0xe1, 0x7c, 0x11, 0xc2, 0x9b, 0x2f, 0x40,
	0xff, 0x65, 0x0d, 0x66, 0x3f, 0xc7, 0xcb, 0x5d, 0x30, 0xf4, 0xc6, 0x53, 0x86, 0x5e, 0xda, 0x21,
	0x98, 0xc8, 0x3a, 0x04, 0xcb, 0x50, 0xed, 0x0e, 0x1c, 0xb7, 0x93, 0x24, 0x2d, 0x4c, 0xe1, 0x6f,
	0xd6, 0x53, 0xb8, 0xad, 0xa7, 0x0a, 0x9f, 0xa6, 0xaa, 0x99, 0xa7, 0xa9, 0xd4, 0x55, 0x5e, 0x4b,
	0x5f, 0xe5, 0x59, 0x43, 0x04, 0x5e, 0xcf, 0x10, 0xb9, 0x00, 0xd3, 0x7e, 0x10, 0x9a, 0x8e, 0xf7,
	0x82, 0xee, 0x1a, 0x3a, 0x90, 0x55, 0x03, 0xfc, 0x20, 0xdc, 0x61, 0x10, 0x4a, 0xbf, 0x45, 0x8e,
	0xbd, 0xb6, 0x79, 0xe8, 0x5a, 0x91, 0xef, 0x58, 0x43, 0xc8, 0x7d, 0xd7, 0xea, 0x52, 0x5b, 0x97,
	0xce, 0x68, 0xa2, 0x85, 0xe9, 0x04, 0xe8, 0x35, 0xd6, 0x0c, 0xa0, 0xb0, 0xfb, 0x7d, 0xbf, 0xb7,
	0x13, 0x68, 0x17, 0x61, 0x26, 0x62, 0x1d, 0xda, 0xa7, 0xec, 0xc9, 0x74, 0x9a, 0xc3, 0xd0, 0x42,
	0xbd, 0xc0, 0x97, 0xc2, 0x1f, 0xce, 0xe6, 0x98, 0x58, 0x52, 0x10, 0x7f, 0x38, 0x13, 0xc6, 0xc0,
	0x70, 0x1b, 0xcf, 0x3b, 0xe0, 0xb0, 0x5d, 0xab, 0x67, 0x6b, 0xef, 0x82, 0x16, 0xa1, 0x74, 0x1c,
	0x12, 0xb8, 0xd6, 0x31, 0xdd, 0x8c, 0x79, 0x1c, 0xaa, 0xc1, 0x5b, 0xb6, 0x58, 0x03, 0x33, 0x35,
	0xc5, 0xa7, 0x3a, 0x4d, 0x7a, 0xaa, 0xbb, 0x04, 0x75, 0x44, 0x08, 0x5c, 0x2b, 0x3c, 0xf4, 0xfb,
	0x3d, 0xcc, 0x3d, 0xa8, 0x1b, 0x33, 0x14, 0xf8, 0x84, 0xc3, 0xf0, 0x3a, 0xc5, 0xf0, 0x13, 0x2e,
	0x6c, 0x91, 0x5f, 0xa7, 0x14, 0x82, 0xcb, 0x3a, 0x03, 0x93, 0x56, 0x10, 0x50, 0x32, 0xce, 0xb0,
	0x53, 0x6e, 0x05, 0x41, 0x92, 0x02, 0x60, 0xa3, 0xca, 0x60, 0x19, 0x06, 0x55, 0x06, 0xd8, 0xe9,
	0x68, 0x37, 0x60, 0x9e, 0x3f, 0x6c, 0xf6, 0xcd, 0x58, 0xa4, 0x96, 0x10, 0x69, 0x2e, 0x6a, 0x78,
	0xc0, 0x45, 0x0b, 0x3d, 0xe0, 0x14, 0x62, 0x93, 0x11, 0xd9, 0x15, 0xb1, 0xc4, 0x37, 0xc5, 0xe5,
	0xf4, 0x9b, 0x62, 0xe6, 0x3d, 0xb2, 0x25, 0xbd, 0x47, 0x5e, 0x85, 0xb9, 0x03, 0xc7, 0x43, 0xf1,
	0x8c, 0x64, 0x7f, 0x85, 0x45, 0xb8, 0x28, 0xf8, 0x9e, 0xe8, 0x10, 0x77, 0x6c, 0xcb, 0x35, 0x43,
	0xff, 0xc8, 0xf6, 0x9a, 0xef, 0xe0, 0xee, 0xd4, 0x28, 0x64, 0x9f, 0x02, 0xb4, 0x35, 0x68, 0x30,
	0x0f, 0xe4, 0xe8, 0xa5, 0x19, 0x5d, 0x67, 0xe7, 0x10, 0x69, 0x16, 0x3d, 0x91, 0xa3, 0x97, 0x1b,
	0x0c, 0x4a, 0x97, 0x1f, 0x63, 0x7a, 0x4e, 0xfb, 0x08, 0x77, 0xfb, 0x3c, 0xa2, 0xce, 0x71, 0xd4,
	0x5d, 0x0e, 0xa6, 0xcb, 0xa7, 0xce, 0x8b, 0x30, 0xe6, 0x05, 0x16, 0x21, 0x09, 0x7d, 0x61, 0xc4,
	0xab, 0x30, 0xc7, 0xb1, 0xe2, 0xf1, 0x56, 0xd9, 0xdd, 0x8c, 0x68, 0xf1, 0x68, 0x57, 0x61, 0x2e,
	0x5a, 0x65, 0xd7, 0xea, 0xa1, 0x9b, 0x75, 0x91, 0x2d, 0x95, 0x83, 0x1f, 0x58, 0x3d, 0xea, 0x6c,
	0xb1, 0x80, 0xca, 0x0b, 0xa7, 0x1f, 0x0e, 0x2c, 0x6a, 0xbd, 0xbc, 0xb0, 0x9b, 0x7a, 0x14, 0x50,
	0x79, 0xc6, 0xa0, 0xd4, 0x01, 0x67, 0x4e, 0x99, 0xdf, 0xe7, 0x4e, 0xd9, 0xa5, 0xc8, 0x29, 0xf3,
	0xfb, 0xcc, 0x29, 0xbb, 0x0a, 0x73, 0xc8, 0x7a, 0x81, 0xb3, 0x97, 0xd9, 0x74, 0x14, 0x1c, 0x73,
	0x56, 0x9f, 0x87, 0xb9, 0xac, 0x59, 0x73, 0x1b, 0xef, 0x33, 0xe9, 0x9d, 0x3e, 0xba, 0xcf, 0x24,
	0x7d, 0xa8, 0x77, 0xf0, 0x56, 0xc9, 0xe9, 0xc0, 0x5e, 0x21, 0x64, 0x63, 0x4c, 0xa1, 0x49, 0xa4,
	0x11, 0x52, 0xb6, 0xd8, 0x07, 0xf8, 0x1a, 0x23, 0x26, 0x26, 0xd8, 0x5e, 0x67, 0x18, 0x69, 0xcf,
	0xf1, 0x19, 0xa6, 0xa0, 0xd3, 0xa9, 0x91, 0xb7, 0x8d, 0x6f, 0x51, 0x72, 0xde, 0x84, 0xfa, 0x0a,
	0x61, 0x81, 0x6a, 0xba, 0x15, 0x3c, 0xa6, 0x3c, 0xe9, 0x10, 0x4a, 0x9a, 0xfe, 0xb7, 0x25, 0xf4,
	0xa0, 0x54, 0xe3, 0x90, 0x40, 0x7b, 0xa0, 0x74, 0x73, 0x2e, 0x17, 0xd2, 0xfa, 0xf9, 0x78, 0x39,
	0xba, 0x0b, 0xba, 0x92, 0xce, 0xcd, 0xe3, 0x28, 0x36, 0x76, 0xb2, 0x95, 0x53, 0xb5, 0x95, 0x84,
	0xda, 0x2a, 0x18, 0x6a, 0x4b, 0x1e, 0x88, 0x3c, 0xb8, 0x34, 0x74, 0xb6, 0x53, 0xe4, 0x0f, 0x37,
	0x45, 0x52, 0x4f, 0x52, 0xc9, 0x8b, 0x3a, 0x35, 0x45, 0x36, 0xd0, 0x14, 0xc9, 0x45, 0x20, 0x52,
	0x7e, 0x40, 0x49, 0xca, 0x0f, 0x78, 0x20, 0xbe, 0x5e, 0x62, 0x24, 0x6d, 0x2f, 0xb4, 0xc2, 0x01,
	0x51, 0xf3, 0x4d, 0xb4, 0x2a, 0xca, 0x29, 0xab, 0x42, 0xff, 0x8a, 0x98, 0xc7, 0x90, 0x1a, 0x88,
	0xe0, 0xf5, 0xc8, 0xfa, 0x11, 0x84, 0xf1, 0x21, 0xa7, 0xfd, 0x04, 0x4d, 0xff, 0xd7, 0x12, 0x9c,
	0xd9, 0x3a, 0xa6, 0xae, 0x04, 0x8f, 0xfc, 0xee, 0xdb, 0x3d, 0x7a, 0x2f, 0xd9, 0x92, 0xcb, 0xa2,
	0x7a, 0x8e, 0xd2, 0x60, 0xdc, 0x69, 0xfb, 0x1e, 0x37, 0x75, 0xf0, 0x6f, 0x24, 0xbf, 0xef, 0x72,
	0x27, 0x85, 0xfe, 0x49, 0x21, 0xbd, 0xce, 0x1d, 0xb4, 0x6c, 0x6a, 0x06, 0xfd, 0x93, 0x2a, 0x30,
	0x3f, 0x88, 0x55, 0xeb, 0x24, 0xd3, 0xe9, 0x7e, 0x10, 0xe9, 0xd5, 0x15, 0xa8, 0x45, 0xec, 0x23,
	0x51, 0x98, 0x81, 0x33, 0x8f, 0xa0, 0xe9, 0x4b, 0x7d, 0x21, 0xd7, 0xef, 0xf3, 0x08, 0xf8, 0xd4,
	0x41, 0xf7, 0x1e, 0xfd, 0xa9, 0x3b, 0xb0, 0xb2, 0xd1, 0xe9, 0x28, 0x97, 0x43, 0x19, 0xfb, 0x75,
	0xa8, 0x87, 0xfc, 0xa7, 0xe8, 0xe0, 0x4a, 0x91, 0x02, 0xf5, 0x00, 0x33, 0x51, 0x5f, 0x74, 0xa5,
	0xcf, 0xc3, 0x3b, 0xf9, 0x53, 0x91, 0x40, 0x77, 0xe1, 0x3c, 0x93, 0x89, 0x5f, 0x0b, 0x35, 0x17,
	0xe1, 0x42, 0xe1, 0x6c, 0x24, 0xd0, 0xdf, 0x83, 0x95, 0x2d, 0xdb, 0xcd, 0xa5, 0x26, 0xeb, 0xf5,
	0x9c, 0x87, 0x77, 0xf2, 0xd1, 0x49, 0xa0, 0xb7, 0x51, 0x23, 0x2b, 0xdb, 0xa3, 0xf3, 0xaf, 0x4a,
	0x09, 0x49, 0x1c, 0xa2, 0xb2, 0xda, 0x21, 0xaa, 0x88, 0xae, 0xfe, 0x4f, 0x4b, 0xa8, 0xc2, 0x0b,
	0x66, 0x21, 0x81, 0xb6, 0x29, 0x9b, 0xfd, 0x23, 0xf2, 0x30, 0xf1, 0x5d, 0x52, 0xae, 0x49, 0x39,
	0xed, 0x9a, 0x64, 0x1d, 0x9b, 0x4a, 0xd6, 0xb1, 0xa1, 0x67, 0x68, 0x25, 0xf5, 0x5a, 0x15, 0xcd,
	0xc0, 0xed, 0xe0, 0xec, 0x49, 0xc2, 0xc7, 0x57, 0x46, 0x5b, 0xe2, 0x43, 0x70, 0x08, 0xb3, 0x1f,
	0xa3, 0xe6, 0xc4, 0x53, 0x8a, 0x7a, 0x70, 0x82, 0x12, 0xc9, 0x89, 0xbc, 0x09, 0x88, 0x05, 0xa2,
	0x93, 0x39, 0x5e, 0x13, 0x85, 0xc7, 0x6b, 0x32, 0x7d, 0xbc, 0xf4, 0x0e, 0x5c, 0x4c, 0x92, 0x55,
	0x54, 0xcb, 0xa1, 0x5b, 0xfb, 0xd5, 0x94, 0x53, 0x7e, 0xb3, 0xf0, 0x45, 0x3d, 0xd3, 0x9b, 0xb9,
	0xe7, 0x97, 0x41, 0x1f, 0x36, 0x0b, 0x09, 0xf4, 0x2e, 0x5c, 0x4e, 0xa5, 0x8c, 0x7c, 0x6e, 0xe4,
	0x5c, 0x83, 0x2b, 0x23, 0x4c, 0x44, 0x02, 0xfd, 0x03, 0xb8, 0x98, 0x24, 0xcf, 0xe4, 0x91, 0x93,
	0x3d, 0x4b, 0x97, 0x41, 0x1f, 0xd6, 0x89, 0x04, 0xfa, 0x8f, 0x4a, 0x70, 0x25, 0x51, 0xe5, 0x2a,
	0xb4, 0xe8, 0x60, 0xa5, 0xe5, 0xa7, 0xa4, 0x90, 0x1f, 0x51, 0x3c, 0xca, 0x92, 0x78, 0x24, 0x87,
	0xb0, 0xa2, 0x3e, 0x84, 0xe3, 0xe2, 0x21, 0xfc, 0x79, 0x09, 0xae, 0x8e, 0x42, 0x17, 0x09, 0xb4,
	0x87, 0x30, 0xcd, 0x3c, 0x46, 0xf1, 0x30, 0x9e, 0x68, 0x3b, 0xa0, 0x1d, 0x8f, 0xf8, 0x86, 0x87,
	0xf2, 0x06, 0xac, 0x25, 0x54, 0x2b, 0xcf, 0x7f, 0xbc, 0x61, 0xfa, 0xff, 0x94, 0xe0, 0xfa, 0x88,
	0xc8, 0x24, 0x48, 0x29, 0xee, 0x93, 0x2b, 0x9d, 0x58, 0x71, 0xe3, 0x1a, 0xbf, 0x05, 0x0b, 0x51,
	0x0f, 0xfe, 0xe2, 0x19, 0xbf, 0x36, 0x9e, 0x90, 0x73, 0xf3, 0x81, 0xd8, 0x88, 0x83, 0x5f, 0x84,
	0x19, 0xd4, 0x8c, 0x2f, 0xec, 0x3e, 0x71, 0xf8, 0x2d, 0x5d, 0x37, 0xa6, 0x29, 0xec, 0x19, 0x03,
	0xe9, 0xef, 0xc2, 0x0d, 0x69, 0xe1, 0xe2, 0xc0, 0x69, 0xc3, 0xe7, 0xeb, 0x70, 0x73, 0x64, 0x6c,
	0x12, 0xa4, 0xf5, 0x4c, 0x29, 0xa3, 0x67, 0x3e, 0x12, 0x13, 0x24, 0xb7, 0xb6, 0xf7, 0xd3, 0x69,
	0x5f, 0xc5, 0x22, 0xae, 0x7f, 0x5f, 0x4c, 0x7d, 0xca, 0x74, 0x26, 0x41, 0x1e, 0x57, 0x4b, 0xa7,
	0xc1, 0x55, 0xfd, 0xae, 0x38, 0xf7, 0x7d, 0xd7, 0x7f, 0x29, 0x25, 0xac, 0x45, 0xe9, 0x03, 0x25,
	0x31, 0x7d, 0x80, 0x7b, 0x4a, 0x39, 0xfd, 0x98, 0x2b, 0x82, 0x1d, 0x47, 0xcc, 0xd7, 0x4a, 0x46,
	0x30, 0xe0, 0x30, 0xfe, 0x5b, 0x3f, 0xa7, 0xa6, 0x8e, 0xab, 0x8e, 0x74, 0x3a, 0x57, 0xb6, 0x99,
	0xa5, 0x73, 0x21, 0x11, 0x23, 0xa5, 0x73, 0x09, 0x24, 0x54, 0x69, 0x2f, 0x64, 0xcf, 0x25, 0xb8,
	0xa8, 0x9a, 0x21, 0x2d, 0x48, 0xdb, 0xe8, 0x40, 0x14, 0x22, 0x8d, 0x62, 0x45, 0xbb, 0x18, 0xa7,
	0x96, 0xa9, 0xb1, 0x3f, 0xc3, 0x87, 0x49, 0xba, 0x90, 0xa8, 0xac, 0xa9, 0x66, 0xe0, 0xb6, 0x3c,
	0xed, 0xbb, 0x71, 0x13, 0xb5, 0x49, 0xcb, 0x49, 0xd3, 0xa3, 0xce, 0x1d, 0x2a, 0xb1, 0xd8, 0xd4,
	0xb1, 0x49, 0x9b, 0x1b, 0xb5, 0x88, 0xbb, 0x65, 0x93, 0xb6, 0xbe, 0x8e, 0x31, 0x63, 0xe5, 0x6c,
	0xb9, 0x9b, 0xfe, 0xc7, 0xa5, 0x28, 0xb8, 0x7d, 0xa2, 0x7e, 0x29, 0xf2, 0xcb, 0xf9, 0xe4, 0x57,
	0x0a, 0xc8, 0x1f, 0xcf, 0x90, 0xcf, 0x42, 0xf0, 0x0a, 0x32, 0x48, 0xc0, 0x63, 0xdc, 0xea, 0xb6,
	0x38, 0x92, 0xae, 0x6e, 0xfe, 0x59, 0x49, 0xa8, 0x11, 0x49, 0xda, 0xf2, 0x97, 0xc6, 0xfd, 0x83,
	0xb2, 0xe4, 0x1f, 0x54, 0x12, 0xff, 0x20, 0xb3, 0xf3, 0xe3, 0xc3, 0xb2, 0xc1, 0xa4, 0xac, 0x00,
	0x6a, 0x7c, 0x22, 0x17, 0x98, 0x6f, 0x81, 0x7f, 0xeb, 0xcf, 0xe0, 0xec, 0x06, 0x86, 0x3f, 0xbd,
	0xb6, 0xbd, 0x13, 0x07, 0x00, 0x79, 0xc2, 0x37, 0x8b, 0xd6, 0x25, 0x19, 0x23, 0x75, 0x03, 0x18,
	0x08, 0x43, 0x2a, 0x2b, 0x50, 0xc3, 0xb0, 0x9f, 0xf0, 0x0a, 0x5e, 0xa5, 0x00, 0xda, 0xa8, 0x2f,
	0xa3, 0x18, 0xca, 0xe3, 0x92, 0x40, 0xff, 0x22, 0x9c, 0xdd, 0xb2, 0xdd, 0xd7, 0x99, 0x92, 0x8e,
	0xaa, 0xec, 0x4a, 0x02, 0xfd, 0xff, 0xa1, 0xf7, 0xc8, 0xd2, 0x3b, 0xfc, 0xee, 0xe6, 0xf1, 0x63,
	0xe6, 0x0c, 0xa2, 0xf7, 0xb8, 0x02, 0xb5, 0xc8, 0x57, 0x24, 0x78, 0x82, 0x6b, 0x46, 0x95, 0x3b,
	0x8b, 0x44, 0x7f, 0x8e, 0xde, 0xa2, 0xaa, 0x23, 0x09, 0xb4, 0x6f, 0xc0, 0x2c, 0x4f, 0x10, 0xf1,
	0xbb, 0x43, 0x5e, 0x52, 0x05, 0x17, 0x3a, 0x1a, 0xcc, 0x60, 0xae, 0xe6, 0x43, 0xbf, 0xcb, 0x82,
	0xe9, 0x98, 0x2a, 0xa1, 0xc0, 0x7b, 0x83, 0x92, 0x0f, 0x1e, 0xa9, 0xed, 0xda, 0x26, 0xcb, 0x3e,
	0xac, 0x24, 0xd1, 0xde, 0xae, 0xbd, 0x87, 0x39, 0x88, 0xa2, 0xa7, 0x3c, 0x5e, 0x98, 0x01, 0x20,
	0x8b, 0x4c, 0x3a, 0xd0, 0x38, 0x99, 0x0d, 0x34, 0xa6, 0x83, 0x6e, 0x53, 0x99, 0xa0, 0x9b, 0xfe,
	0x3b, 0xb0, 0x80, 0x49, 0x81, 0x99, 0x17, 0x84, 0x4d, 0x98, 0x89, 0x38, 0x27, 0xb0, 0xf4, 0x82,
	0xc4, 0xd2, 0xa4, 0x17, 0xdd, 0x6b, 0x23, 0xf2, 0x00, 0x90, 0x97, 0x67, 0x61, 0x51, 0x1e, 0x9a,
	0x04, 0xfa, 0xbf, 0xd4, 0x52, 0x01, 0x3c, 0xda, 0xf1, 0xed, 0x8b, 0xc5, 0xdb, 0x17, 0x8b, 0xb7,
	0x2f, 0x16, 0x6f, 0x5f, 0x2c, 0x7e, 0xbb, 0x5e, 0x2c, 0xf4, 0x8f, 0x45, 0x43, 0xee, 0x91, 0xd5,
	0x3f, 0x1a, 0x5a, 0x58, 0x84, 0x85, 0xae, 0x82, 0x75, 0x44, 0x7f, 0x3f, 0xed, 0xbb, 0x69, 0x2b,
	0x47, 0x1c, 0x89, 0x04, 0xfa, 0x01, 0xbc, 0x83, 0x7a, 0x34, 0x6f, 0xaa, 0x4d, 0x5e, 0x3f, 0x5b,
	0x74, 0xf7, 0x49, 0x1d, 0x51, 0x5d, 0x23, 0x39, 0xa8, 0xab, 0xef, 0xc7, 0x39, 0xa9, 0x69, 0x94,
	0x93, 0xae, 0xe3, 0x02, 0x9c, 0x2b, 0xa0, 0x95, 0x99, 0x65, 0x89, 0x09, 0x9d, 0xb4, 0x29, 0x0d,
	0xfd, 0x6c, 0x33, 0x33, 0xf4, 0xb3, 0x6b, 0xcd, 0xd7, 0x96, 0xc2, 0xd4, 0xc9, 0x4a, 0xaf, 0x8b,
	0xe6, 0x70, 0x9a, 0x91, 0xea, 0x14, 0x0a, 0xf5, 0x3a, 0x3e, 0x12, 0x8b, 0x65, 0x68, 0xdb, 0x4e,
	0xdb, 0xf7, 0x36, 0x8f, 0x23, 0x3d, 0x5d, 0x5c, 0x04, 0xf4, 0xa7, 0x25, 0xd1, 0xdb, 0x50, 0xf6,
	0x26, 0x81, 0xaa, 0xb0, 0xba, 0xa4, 0x2a, 0xac, 0xde, 0x82, 0x69, 0xe4, 0x49, 0x71, 0xb9, 0x88,
	0x8a, 0x2b, 0xd0, 0x8b, 0xff, 0xd6, 0x07, 0x82, 0x3d, 0x9c, 0xa0, 0x8c, 0x14, 0x4e, 0x17, 0xe5,
	0xa1, 0x92, 0x92, 0x07, 0x95, 0x45, 0x3c, 0x9e, 0xf2, 0x85, 0xfe, 0xa2, 0x02, 0x0b, 0xf7, 0x6d,
	0xd7, 0x8d, 0xb3, 0xfd, 0xf9, 0xbc, 0xeb, 0x70, 0x66, 0xe0, 0x39, 0x9f, 0x0d, 0x6c, 0xf3, 0xc0,
	0x6a, 0x1f, 0x75, 0xfb, 0xfe, 0xc0, 0xeb, 0x08, 0x2c, 0x58, 0x60, 0x8d, 0x9b, 0x71, 0x1b, 0x9d,
	0xec, 0x5d, 0xd0, 0x78, 0x1f, 0xd1, 0x44, 0x65, 0xa6, 0x43, 0x83, 0xb5, 0xec, 0x25, 0xb6, 0xf1,
	0x39, 0x00, 0x8e, 0x9d, 0x58, 0xf1, 0x35, 0x06, 0xa1, 0x4e, 0xc9, 0xfb, 0xb0, 0xd8, 0x1b, 0xb8,
	0xa1, 0x93, 0x9d, 0x9f, 0x99, 0x67, 0x1a, 0xb6, 0xa5, 0xa7, 0xbf, 0x01, 0xf3, 0xac, 0x87, 0x38,
	0x3b, 0xb3, 0x32, 0xe6, 0xb0, 0x61, 0x2f, 0x65, 0x98, 0x33, 0x5c, 0x3a, 0xf7, 0x24, 0xaf, 0x79,
	0xa7, 0x00, 0x3a, 0xb5, 0x72, 0xed, 0x4e, 0xaf, 0xcb, 0xf3, 0x9d, 0xa4, 0xb5, 0xef, 0xf4, 0xba,
	0x4a, 0x72, 0x69, 0x97, 0xaa, 0x92, 0x5c, 0xda, 0x83, 0xea, 0x36, 0x46, 0xe8, 0xf7, 0x9d, 0x80,
	0xe7, 0xdb, 0xd7, 0x18, 0xe4, 0x53, 0x87, 0xba, 0x00, 0xe7, 0x55, 0x15, 0x54, 0x74, 0x0c, 0xdb,
	0x2b, 0x16, 0xef, 0x1f, 0x97, 0xe4, 0x2a, 0xb4, 0x54, 0x5f, 0xb4, 0xda, 0x15, 0x65, 0x5e, 0x37,
	0x46, 0x30, 0x7c, 0xa2, 0x41, 0x44, 0xfb, 0xe7, 0x5d, 0xd0, 0x42, 0x9b, 0x84, 0x8e, 0xd7, 0x35,
	0xa5, 0x08, 0x75, 0x83, 0xb7, 0xc4, 0x67, 0x4b, 0xff, 0x54, 0x5e, 0x19, 0xd6, 0xb9, 0x25, 0x2b,
	0x1b, 0x5a, 0x48, 0xbb, 0x04, 0x53, 0xd4, 0x32, 0xb5, 0x5c, 0x37, 0x7a, 0x11, 0xec, 0xda, 0xe1,
	0x86, 0xeb, 0xea, 0xdf, 0xcb, 0xa9, 0xbf, 0x13, 0x56, 0xfe, 0x40, 0x2e, 0x3d, 0x3b, 0xc9, 0xba,
	0x93, 0x07, 0xc6, 0x5f, 0x55, 0xa0, 0x95, 0x2d, 0xd0, 0x14, 0x16, 0xf1, 0xdb, 0x5b, 0xda, 0xfa,
	0x71, 0xa6, 0xb4, 0xf5, 0xfd, 0x91, 0x4b, 0x5b, 0xa3, 0xe5, 0x47, 0x15, 0xae, 0x1f, 0xc1, 0xe4,
	0x21, 0x2a, 0x17, 0x94, 0x6f, 0x85, 0x56, 0x54, 0xa8, 0x1e, 0x83, 0x77, 0xd1, 0xee, 0x51, 0x7b,
	0x08, 0x4d, 0x50, 0xef, 0xd0, 0x6f, 0x82, 0xfa, 0x5d, 0x96, 0x45, 0xd8, 0xb0, 0xa0, 0x08, 0x6b,
	0x80, 0x58, 0x7a, 0x29, 0xf6, 0x8b, 0xd2, 0x4b, 0x85, 0x1a, 0xdb, 0xe9, 0x4c, 0x8d, 0x6d, 0x8e,
	0xe4, 0xce, 0xe4, 0x48, 0xee, 0x2f, 0x4a, 0xb0, 0xa8, 0x9a, 0x10, 0x43, 0xe0, 0x58, 0x92, 0xc4,
	0x4b, 0x36, 0xf0, 0x07, 0x9e, 0x70, 0x4c, 0xa8, 0x40, 0xbb, 0x2a, 0x4a, 0x82, 0xb7, 0xbd, 0x4e,
	0xec, 0xb5, 0x74, 0xac, 0xe3, 0xd4, 0x6b, 0x79, 0xb5, 0x63, 0x1d, 0xb3, 0xc6, 0x35, 0x68, 0xd8,
	0xaf, 0x02, 0xa7, 0x6f, 0x9b, 0x09, 0x0e, 0xdb, 0xfc, 0x59, 0x06, 0xdf, 0x12, 0x30, 0x3d, 0x3f,
	0xa4, 0xbb, 0x99, 0x60, 0x32, 0x29, 0x98, 0x65, 0xf0, 0x08, 0x53, 0xff, 0x1e, 0xbe, 0x73, 0xaa,
	0xe5, 0xf5, 0x94, 0x55, 0x82, 0x7e, 0x07, 0x5a, 0xd9, 0x72, 0xe1, 0x51, 0x54, 0xd7, 0x39, 0x7c,
	0x6e, 0xcc, 0x23, 0x51, 0xff, 0xcb, 0x12, 0x9c, 0x53, 0x54, 0x03, 0x0b, 0x23, 0xff, 0x06, 0xf5,
	0xda, 0x6a, 0xf4, 0x76, 0x9b, 0x4b, 0xfe, 0xaf, 0x4a, 0x30, 0x97, 0x49, 0x2c, 0xc9, 0x2b, 0xf7,
	0x59, 0x82, 0xa9, 0xd0, 0x37, 0xc3, 0x30, 0xa9, 0x82, 0x09, 0xfd, 0xfd, 0x90, 0x15, 0xb6, 0x84,
	0x7e, 0x62, 0x86, 0xb3, 0x7b, 0x13, 0x42, 0x3f, 0xb6, 0xc1, 0x73, 0xdd, 0xef, 0x28, 0x5b, 0x02,
	0xfb, 0xb1, 0xd7, 0x3d, 0x54, 0x66, 0xe8, 0xf9, 0x0d, 0xc9, 0xe0, 0x17, 0xfd, 0x9f, 0xa9, 0xb4,
	0xff, 0x93, 0xf2, 0xaf, 0xab, 0x99, 0xe4, 0xfe, 0x9f, 0x94, 0x41, 0x13, 0x56, 0x6c, 0x30, 0xe7,
	0xab, 0x28, 0x98, 0xb3, 0x02, 0x35, 0x6c, 0x12, 0x96, 0x8e, 0xb8, 0xb8, 0xf8, 0x4b, 0x50, 0xc7,
	0xc6, 0xcc, 0xf2, 0x67, 0x28, 0xf0, 0xf3, 0x65, 0x40, 0x2a, 0xc4, 0x30, 0x25, 0x87, 0x18, 0x68,
	0xa3, 0x58, 0xdc, 0x4a, 0x1b, 0x59, 0x6c, 0x29, 0xfb, 0xad, 0x90, 0x9a, 0xf4, 0xad, 0x10, 0xfd,
	0x5b, 0x70, 0x46, 0x4e, 0x39, 0xca, 0xcd, 0xe9, 0x88, 0x33, 0x78, 0xcb, 0x79, 0x19, 0xbc, 0x15,
	0x31, 0x83, 0xd7, 0x81, 0xb3, 0xaa, 0xc1, 0x49, 0xa0, 0x3d, 0x4e, 0xde, 0x3b, 0x08, 0x2b, 0xd6,
	0x0c, 0x2d, 0xc7, 0xcd, 0x8b, 0x35, 0x65, 0x47, 0x88, 0xde, 0x38, 0x92, 0xf4, 0x28, 0xfd, 0x3b,
	0xd9, 0x84, 0x26, 0xbe, 0xdf, 0xa7, 0xb5, 0x94, 0x41, 0x36, 0xd1, 0x29, 0x1e, 0x9f, 0x04, 0xda,
	0x37, 0xe1, 0x6c, 0x44, 0x6d, 0xc4, 0xef, 0xd4, 0x82, 0xf4, 0x82, 0x05, 0x45, 0xe3, 0x2c, 0x06,
	0xa9, 0xdf, 0x7c, 0x59, 0x4d, 0xe4, 0xe0, 0x86, 0xeb, 0xa6, 0x6e, 0x2b, 0xea, 0x2e, 0x0d, 0xf0,
	0x0b, 0x04, 0x72, 0x0b, 0x09, 0xb4, 0x4f, 0x61, 0x81, 0xdd, 0x63, 0xa6, 0x22, 0x90, 0x77, 0x12,
	0x85, 0x34, 0x7f, 0x28, 0x8e, 0x8d, 0x96, 0xc7, 0x5f, 0x4d, 0x40, 0x2b, 0xbf, 0xc7, 0xdb, 0xef,
	0x2c, 0xfd, 0x66, 0xbe, 0xb3, 0xf4, 0x71, 0xe6, 0x3b, 0x4b, 0xa7, 0x61, 0x36, 0x35, 0xde, 0xd4,
	0x6c, 0x9a, 0x7f, 0x3d, 0xb3, 0x89, 0xaa, 0x4a, 0x62, 0x1e, 0x58, 0x9e, 0x67, 0x77, 0x30, 0xb0,
	0x57, 0x35, 0xaa, 0x0e, 0xd9, 0xc4, 0xdf, 0x19, 0x9b, 0x6a, 0x21, 0xfb, 0xdd, 0x92, 0xff, 0x98,
	0x82, 0xd5, 0x61, 0x4b, 0x7d, 0xfb, 0xfd, 0xbf, 0xb7, 0xdf, 0xff, 0x3b, 0x95, 0xef, 0xff, 0xc9,
	0x95, 0xeb, 0x8d, 0x91, 0x2a, 0xd7, 0xe7, 0x15, 0x95, 0xeb, 0xb7, 0x61, 0x31, 0xa9, 0x94, 0x97,
	0xbe, 0x04, 0x38, 0x1f, 0x15, 0xcc, 0x27, 0xc3, 0xaa, 0x3a, 0x24, 0x1f, 0x05, 0xcc, 0x74, 0xf8,
	0x3f, 0xf6, 0x65, 0xc0, 0x1b, 0x76, 0xfc, 0xfd, 0x95, 0x27, 0xb1, 0x86, 0x5c, 0x86, 0x33, 0x4f,
	0x8c, 0xed, 0xbd, 0xed, 0xdd, 0x7d, 0xf3, 0x89, 0xb1, 0x73, 0x6f, 0xdb, 0x7c, 0xba, 0xfb, 0x8d,
	0xdd, 0xc7, 0x9f, 0xec, 0x36, 0xc6, 0xb4, 0x73, 0xb0, 0x9c, 0x6e, 0x32, 0xb6, 0xb7, 0xcc, 0xad,
	0x9d, 0x8d, 0x47, 0x8f, 0x77, 0xb7, 0x1a, 0x25, 0x6d, 0x09, 0x16, 0xd2, 0xcd, 0xfb, 0x9b, 0xdb,
	0x1b, 0xbb, 0x8d, 0xf2, 0x8d, 0x4f, 0x61, 0x31, 0xf1, 0xe3, 0xf7, 0x59, 0x08, 0xe0, 0x91, 0x15,
	0x68, 0x1a, 0xcc, 0xde, 0x7b, 0xbc, 0x7b, 0x7f, 0xe7, 0x81, 0xf9, 0x74, 0xf7, 0xe1, 0xce, 0xa3,
	0x9d, 0xfd, 0xc6, 0x98, 0x76, 0x16, 0x34, 0x0e, 0xdb, 0x7d, 0xbc, 0x6f, 0x6e, 0x7f, 0xf3, 0xc9,
	0x8e, 0xb1, 0x4d, 0x07, 0x4f, 0xc3, 0xb7, 0xb6, 0x1f, 0x6e, 0xef, 0x6f, 0x6f, 0x35, 0xca, 0x37,
	0xfe, 0x30, 0xf9, 0x86, 0x4c, 0x5c, 0xcf, 0xaf, 0xad, 0xc0, 0x52, 0x16, 0xf6, 0xd4, 0x3b, 0xf2,
	0xfc, 0x97, 0x5e, 0x63, 0x0c, 0x17, 0x98, 0x69, 0xc4, 0x63, 0xd1, 0x28, 0xa9, 0xfa, 0x3d, 0xf4,
	0xc3, 0xd0, 0xee, 0x1f, 0x37, 0xca, 0xda, 0x3b, 0xd0, 0xcc, 0x36, 0x7e, 0xe2, 0x90, 0xe7, 0x74,
	0x4d, 0x8d, 0xca, 0x0d, 0x2b, 0x26, 0x63, 0x2f, 0x2e, 0x91, 0x5e, 0x80, 0x39, 0xce, 0x3c, 0x93,
	0x33, 0xa6, 0x31, 0x46, 0x81, 0xf7, 0x3e, 0xde, 0xd8, 0xdd, 0xdd, 0x7e, 0x18, 0x03, 0x4b, 0xda,
	0x3c, 0xd4, 0x1f, 0x3c, 0xdd, 0x79, 0xb8, 0x15, 0x83, 0xca, 0x5a, 0x03, 0x66, 0x9e, 0xee, 0x6d,
	0x1b, 0x31, 0xa4, 0xb2, 0xfe, 0x6f, 0x77, 0xa1, 0x2e, 0x18, 0x3c, 0x0f, 0x1e, 0x6b, 0x47, 0xb0,
	0xa8, 0x0a, 0x95, 0x68, 0xd7, 0xb2, 0x27, 0x2f, 0xe7, 0x63, 0x6e, 0xad, 0xb5, 0xd1, 0x10, 0x49,
	0xa0, 0x8f, 0x69, 0x26, 0x34, 0xb2, 0xae, 0xa7, 0x26, 0xdd, 0x6a, 0x8a, 0x0f, 0x84, 0xb5, 0x2e,
	0x0f, 0x47, 0xc2, 0x09, 0x9e, 0xc3, 0x82, 0xc2, 0xf9, 0xd2, 0xae, 0x4a, 0x46, 0x9f, 0xf2, 0x5b,
	0x55, 0xad, 0x6b, 0x23, 0xe1, 0x45, 0x4b, 0xc9, 0xba, 0xa8, 0xf2, 0x52, 0x14, 0x9f, 0xd6, 0x92,
	0x97, 0xa2, 0xfc, 0x9e, 0xd6, 0x98, 0xf6, 0x19, 0x9a, 0x9b, 0x8a, 0x6f, 0xc0, 0x69, 0xd7, 0x47,
	0xe1, 0x38, 0x7e, 0x8b, 0xae, 0x75, 0x63, 0x54, 0x54, 0x9c, 0x52, 0x21, 0x0b, 0x9b, 0xc7, 0x3b,
	0x9d, 0xe1, 0xb2, 0xc0, 0xd3, 0x97, 0x86, 0xcb, 0x42, 0x94, 0xaf, 0xa4, 0x8f, 0x69, 0x7f, 0x50,
	0x92, 0x3f, 0x0e, 0x25, 0x7e, 0xe8, 0x4a, 0xbb, 0x3d, 0x0a, 0xed, 0xc2, 0x27, 0xb5, 0x5a, 0xef,
	0x9f, 0xac, 0x03, 0x52, 0xf1, 0x03, 0xf9, 0x4b, 0x7b, 0xc2, 0x07, 0xf0, 0xde, 0x1b, 0x36, 0x62,
	0x2a, 0x31, 0xa9, 0x75, 0xeb, 0x24, 0xe8, 0x38, 0x7d, 0x88, 0x9e, 0x83, 0xea, 0xab, 0x1b, 0x9a,
	0x6a, 0xeb, 0x72, 0x3e, 0x16, 0xd2, 0xba, 0x39, 0x32, 0x6e, 0x24, 0x5a, 0xea, 0xe2, 0x64, 0x59,
	0xb4, 0x72, 0x3f, 0x5d, 0x20, 0x8b, 0x56, 0x41, 0xbd, 0x73, 0x24, 0xcd, 0xaa, 0x8f, 0x5f, 0xa8,
	0xa4, 0x59, 0xfd, 0x19, 0x00, 0xa5, 0x34, 0xe7, 0xd4, 0xf5, 0xeb, 0x63, 0xda, 0x01, 0xcc, 0x4b,
	0x45, 0xd6, 0x9a, 0x74, 0xfa, 0x54, 0x65, 0xf2, 0xad, 0x2b, 0x23, 0x60, 0xe1, 0x1c, 0xdf, 0x97,
	0xbe, 0x5a, 0x90, 0x94, 0x08, 0x69, 0xef, 0x0e, 0x21, 0x37, 0x55, 0x82, 0xd4, 0x7a, 0xef, 0x04,
	0xd8, 0xd1, 0x69, 0x55, 0x55, 0x45, 0xcb, 0xa7, 0x35, 0xa7, 0x1a, 0x5b, 0x3e, 0xad, 0xb9, 0x45,
	0xd6, 0x28, 0xa8, 0x39, 0x15, 0xd1, 0xb2, 0xa0, 0xe6, 0xd7, 0x63, 0xcb, 0x82, 0x5a, 0x54, 0x66,
	0x8d, 0x4b, 0x54, 0x55, 0x4f, 0xcb, 0x4b, 0xcc, 0x29, 0xc7, 0x96, 0x97, 0x98, 0x5b, 0x8c, 0x3d,
	0xa6, 0xbd, 0xc4, 0xb0, 0x85, 0xb2, 0xc4, 0x59, 0x53, 0x1d, 0xb0, 0xbc, 0xba, 0xed, 0xd6, 0xbb,
	0xa3, 0x23, 0x0b, 0x3a, 0x28, 0xa7, 0x38, 0x59, 0xa9, 0x83, 0xf2, 0x2b, 0x9d, 0x95, 0x3a, 0xa8,
	0xa0, 0xee, 0x59, 0x1f, 0xd3, 0x9e, 0xc0, 0xb4, 0xa8, 0x02, 0xce, 0x17, 0x64, 0x17, 0xd1, 0x09,
	0x2e, 0x14, 0xb6, 0x47, 0x77, 0x63, 0x36, 0xe5, 0x48, 0xbe, 0x1b, 0x15, 0xf9, 0x4e, 0xf2, 0xdd,
	0xa8, 0xcc, 0x5c, 0x8a, 0xb6, 0x4a, 0x59, 0x37, 0xa8, 0xdc, 0xaa, 0xbc, 0x92, 0x44, 0xe5, 0x56,
	0xe5, 0x96, 0x23, 0x26, 0x97, 0x56, 0x6e, 0x59, 0xa0, 0xf2, 0xd2, 0x2a, 0xaa, 0x3c, 0x54, 0x5e,
	0x5a, 0x85, 0x55, 0x87, 0xfa, 0x98, 0xe6, 0x49, 0x81, 0x42, 0xae, 0x4b, 0xd7, 0x86, 0x0c, 0x96,
	0xa8, 0xd2, 0xeb, 0x23, 0x62, 0xe2, 0x7c, 0x3f, 0x2c, 0x49, 0x15, 0x94, 0xd9, 0x22, 0x3a, 0x6d,
	0x7d, 0xa4, 0x01, 0x53, 0x35, 0x7e, 0xad, 0x0f, 0x4e, 0xdc, 0x47, 0x58, 0xbe, 0x5c, 0xb5, 0xa6,
	0x15, 0x98, 0x1f, 0xe9, 0x2a, 0xb9, 0xd6, 0xf5, 0x11, 0x31, 0x85, 0xf9, 0xe4, 0xbc, 0x47, 0xe5,
	0x7c, 0xca, 0xbc, 0x4a, 0xe5, 0x7c, 0xea, 0x44, 0xca, 0x58, 0xba, 0x95, 0xb9, 0xde, 0x4a, 0xe9,
	0xce, 0xcb, 0x26, 0x57, 0x4a, 0x77, 0x6e, 0x0a, 0x79, 0xfe, 0xc4, 0xb9, 0xc7, 0x2a, 0x2f, 0x51,
	0x7c, 0xb4, 0x89, 0x85, 0x1d, 0xfd, 0x93, 0x92, 0xf8, 0x18, 0xac, 0x4a, 0xe9, 0xd6, 0xbe, 0x30,
	0xca, 0x90, 0x69, 0x73, 0x6c, 0xfd, 0xa4, 0x5d, 0x84, 0x6b, 0x55, 0x4e, 0x49, 0xbe, 0x96, 0xef,
	0x82, 0xa4, 0xd2, 0xb2, 0x95, 0xd7, 0xaa, 0x3a, 0xfb, 0x39, 0xba, 0xe0, 0x46, 0x98, 0x2c, 0x27,
	0x77, 0x5c, 0x79, 0xc1, 0xe5, 0x4d, 0x16, 0x66, 0xbe, 0xa1, 0x2b, 0xcc, 0x77, 0xa3, 0xd0, 0xf1,
	0x49, 0x4f, 0x79, 0x73, 0x64, 0xdc, 0x48, 0xa8, 0xf2, 0x6a, 0x1d, 0x65, 0xa1, 0x2a, 0x28, 0xc0,
	0x94, 0x85, 0xaa, 0xb0, 0x84, 0x72, 0x4c, 0xfb, 0xfd, 0x52, 0x94, 0x7b, 0xae, 0x9e, 0xfc, 0x96,
	0x7a, 0x1d, 0xb9, 0xf3, 0xdf, 0x3e, 0x11, 0x7e, 0xb4, 0xf6, 0xbc, 0x3a, 0x48, 0x79, 0xed, 0x05,
	0x05, 0x96, 0xf2, 0xda, 0x0b, 0xcb, 0x2b, 0xe3, 0x7b, 0x2a, 0xb7, 0xf6, 0x51, 0x79, 0x4f, 0x15,
	0xd5, 0x63, 0x2a, 0xef, 0xa9, 0xc2, 0xd2, 0x4a, 0x7e, 0xac, 0x8b, 0x0b, 0xf5, 0xe4, 0x63, 0x3d,
	0xb4, 0x7c, 0x50, 0x3e, 0xd6, 0x23, 0xd4, 0x02, 0x8e, 0x69, 0x7f, 0x56, 0x82, 0x8b, 0x43, 0xab,
	0xf4, 0xb4, 0x0f, 0x0b, 0x65, 0x3b, 0x8f, 0xa2, 0x3b, 0xaf, 0xd1, 0x2b, 0x66, 0x50, 0x71, 0x71,
	0x9f, 0xcc, 0xa0, 0xa1, 0x15, 0x84, 0x32, 0x83, 0x46, 0xa8, 0x1f, 0x1c, 0xd3, 0x7e, 0x54, 0x12,
	0xcb, 0x6a, 0xf2, 0x2a, 0xf5, 0xb4, 0x3b, 0xf9, 0x4a, 0xb5, 0xa0, 0xea, 0xb0, 0x75, 0xf7, 0x75,
	0xba, 0x21, 0x5d, 0x7f, 0x9d, 0xaa, 0x6c, 0x2c, 0x28, 0xaf, 0xd3, 0xfe, 0x7f, 0xfe, 0x1c, 0xc5,
	0x25, 0x7c, 0xad, 0x2f, 0xbe, 0x66, 0x4f, 0x24, 0xf0, 0xa7, 0x25, 0xb8, 0x36, 0x62, 0x61, 0x9b,
	0xf6, 0xa5, 0xa1, 0x13, 0xe5, 0xd6, 0xcf, 0xb5, 0x3e, 0x7a, 0xed, 0xbe, 0x42, 0xa8, 0x41, 0x55,
	0xf5, 0xa6, 0x15, 0x44, 0x89, 0xb2, 0xb5, 0x75, 0xad, 0x9b, 0x23, 0xe3, 0x46, 0x01, 0x39, 0x45,
	0x75, 0x8b, 0x1c, 0x90, 0x53, 0x97, 0xd6, 0xb4, 0xae, 0x8d, 0x84, 0x17, 0xcd, 0xa4, 0xa8, 0x78,
	0x91, 0x67, 0x52, 0x57, 0xd4, 0xb4, 0xae, 0x8d, 0x84, 0x27, 0x5b, 0x08, 0x42, 0x92, 0x66, 0x81,
	0x85, 0x90, 0xca, 0x70, 0x2d, 0xb2, 0x10, 0x32, 0xf9, 0xad, 0x18, 0x61, 0xc8, 0x4d, 0xe5, 0x95,
	0x23, 0x0c, 0x45, 0x19, 0xca, 0x72, 0x84, 0xa1, 0x38, 0x47, 0x38, 0x63, 0x9d, 0x14, 0x2d, 0x34,
	0x27, 0x95, 0xb7, 0xc8, 0x3a, 0x91, 0x26, 0x4b, 0x19, 0x9f, 0xe9, 0x9c, 0xe3, 0x22, 0xe3, 0x53,
	0x4a, 0x5e, 0x2e, 0x32, 0x3e, 0xe5, 0x54, 0x66, 0x7d, 0x4c, 0xfb, 0xa3, 0x92, 0xf8, 0xc9, 0x0e,
	0x45, 0x1a, 0xb0, 0xf6, 0x7e, 0xf1, 0x88, 0x72, 0xce, 0x71, 0xeb, 0x0b, 0x27, 0xec, 0x11, 0x1b,
	0x2c, 0x05, 0x69, 0x8b, 0xda, 0xad, 0x91, 0xe2, 0x9b, 0x71, 0x12, 0x54, 0xeb, 0xf6, 0x89, 0xf0,
	0x73, 0x49, 0x10, 0x72, 0x46, 0x87, 0x93, 0x90, 0x4e, 0x4e, 0x1d, 0x4e, 0x42, 0x26, 0x21, 0x95,
	0x45, 0x43, 0xf2, 0xf3, 0xa7, 0xe4, 0x68, 0x48, 0x61, 0x1e, 0x58, 0xeb, 0xd6, 0x49, 0xd0, 0x23,
	0x35, 0x99, 0x93, 0x7a, 0x26, 0xab, 0xc9, 0xfc, 0xd4, 0xb6, 0xd6, 0xcd, 0x91, 0x71, 0xa3, 0x59,
	0x73, 0x72, 0xf2, 0xe4, 0x59, 0xf3, 0x93, 0x4d, 0x5b, 0x37, 0x47, 0xc6, 0xc5, 0x59, 0x6d, 0xd0,
	0xe4, 0x68, 0x83, 0x76, 0x65, 0x78, 0x44, 0x82, 0xce, 0x75, 0x75, 0x14, 0x34, 0x75, 0xb8, 0x22,
	0xca, 0xff, 0x1a, 0x12, 0xae, 0x48, 0xd2, 0x86, 0x86, 0x85, 0x2b, 0x84, 0x04, 0x20, 0x76, 0x13,
	0x28, 0xd2, 0x71, 0x34, 0x15, 0xc1, 0x8a, 0x6c, 0x9e, 0xd6, 0xb5, 0x91, 0xf0, 0xe8, 0x4c, 0x9b,
	0xeb, 0x9f, 0xbe, 0xdf, 0xf5, 0x5d, 0xcb, 0xeb, 0xde, 0xba, 0xb3, 0x1e, 0x86, 0xb7, 0xda, 0x7e,
	0xef, 0x36, 0xfe, 0x13, 0xc5, 0xb6, 0xef, 0xde, 0x26, 0x76, 0xff, 0x85, 0xd3, 0xb6, 0x49, 0xe6,
	0xbf, 0x2c, 0x1e, 0x4c, 0x22, 0xc6, 0x07, 0xff, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xda, 0x8d, 0xe2,
	0x6a, 0x9c, 0x71, 0x00, 0x00,
}
