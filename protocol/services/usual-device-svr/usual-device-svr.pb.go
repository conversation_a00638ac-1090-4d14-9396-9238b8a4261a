// Code generated by protoc-gen-go. DO NOT EDIT.
// source: usual-device/usual-device-svr.proto

package usual_device_svr // import "golang.52tt.com/protocol/services/usual-device-svr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CheckType int32

const (
	CheckType_ENUM_CHECK_TYPE_UNKNOWN CheckType = 0
	CheckType_ENUM_CHECK_TYPE_CONSUME CheckType = 1
	CheckType_ENUM_CHECK_TYPE_AUTH    CheckType = 2
	CheckType_ENUM_CHECK_TYPE_REBOUND CheckType = 3
	CheckType_ENUM_CHECK_TYPE_CAPTCHA CheckType = 4
	CheckType_ENUM_CHECK_TYPE_SMS     CheckType = 5
)

var CheckType_name = map[int32]string{
	0: "ENUM_CHECK_TYPE_UNKNOWN",
	1: "ENUM_CHECK_TYPE_CONSUME",
	2: "ENUM_CHECK_TYPE_AUTH",
	3: "ENUM_CHECK_TYPE_REBOUND",
	4: "ENUM_CHECK_TYPE_CAPTCHA",
	5: "ENUM_CHECK_TYPE_SMS",
}
var CheckType_value = map[string]int32{
	"ENUM_CHECK_TYPE_UNKNOWN": 0,
	"ENUM_CHECK_TYPE_CONSUME": 1,
	"ENUM_CHECK_TYPE_AUTH":    2,
	"ENUM_CHECK_TYPE_REBOUND": 3,
	"ENUM_CHECK_TYPE_CAPTCHA": 4,
	"ENUM_CHECK_TYPE_SMS":     5,
}

func (x CheckType) String() string {
	return proto.EnumName(CheckType_name, int32(x))
}
func (CheckType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{0}
}

// 检查常用设备
type CheckUsualDeviceReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	CheckType            uint32   `protobuf:"varint,3,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	DeviceType           uint32   `protobuf:"varint,4,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUsualDeviceReq) Reset()         { *m = CheckUsualDeviceReq{} }
func (m *CheckUsualDeviceReq) String() string { return proto.CompactTextString(m) }
func (*CheckUsualDeviceReq) ProtoMessage()    {}
func (*CheckUsualDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{0}
}
func (m *CheckUsualDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUsualDeviceReq.Unmarshal(m, b)
}
func (m *CheckUsualDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUsualDeviceReq.Marshal(b, m, deterministic)
}
func (dst *CheckUsualDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUsualDeviceReq.Merge(dst, src)
}
func (m *CheckUsualDeviceReq) XXX_Size() int {
	return xxx_messageInfo_CheckUsualDeviceReq.Size(m)
}
func (m *CheckUsualDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUsualDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUsualDeviceReq proto.InternalMessageInfo

func (m *CheckUsualDeviceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUsualDeviceReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *CheckUsualDeviceReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

func (m *CheckUsualDeviceReq) GetDeviceType() uint32 {
	if m != nil {
		return m.DeviceType
	}
	return 0
}

type CheckUsualDeviceResp struct {
	Result               bool     `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUsualDeviceResp) Reset()         { *m = CheckUsualDeviceResp{} }
func (m *CheckUsualDeviceResp) String() string { return proto.CompactTextString(m) }
func (*CheckUsualDeviceResp) ProtoMessage()    {}
func (*CheckUsualDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{1}
}
func (m *CheckUsualDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUsualDeviceResp.Unmarshal(m, b)
}
func (m *CheckUsualDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUsualDeviceResp.Marshal(b, m, deterministic)
}
func (dst *CheckUsualDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUsualDeviceResp.Merge(dst, src)
}
func (m *CheckUsualDeviceResp) XXX_Size() int {
	return xxx_messageInfo_CheckUsualDeviceResp.Size(m)
}
func (m *CheckUsualDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUsualDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUsualDeviceResp proto.InternalMessageInfo

func (m *CheckUsualDeviceResp) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

// 更新常用设备
type UpdateUsualDeviceReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	CheckType            uint32   `protobuf:"varint,3,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUsualDeviceReq) Reset()         { *m = UpdateUsualDeviceReq{} }
func (m *UpdateUsualDeviceReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUsualDeviceReq) ProtoMessage()    {}
func (*UpdateUsualDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{2}
}
func (m *UpdateUsualDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUsualDeviceReq.Unmarshal(m, b)
}
func (m *UpdateUsualDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUsualDeviceReq.Marshal(b, m, deterministic)
}
func (dst *UpdateUsualDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUsualDeviceReq.Merge(dst, src)
}
func (m *UpdateUsualDeviceReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUsualDeviceReq.Size(m)
}
func (m *UpdateUsualDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUsualDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUsualDeviceReq proto.InternalMessageInfo

func (m *UpdateUsualDeviceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUsualDeviceReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *UpdateUsualDeviceReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

type UpdateUsualDeviceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUsualDeviceResp) Reset()         { *m = UpdateUsualDeviceResp{} }
func (m *UpdateUsualDeviceResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUsualDeviceResp) ProtoMessage()    {}
func (*UpdateUsualDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{3}
}
func (m *UpdateUsualDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUsualDeviceResp.Unmarshal(m, b)
}
func (m *UpdateUsualDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUsualDeviceResp.Marshal(b, m, deterministic)
}
func (dst *UpdateUsualDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUsualDeviceResp.Merge(dst, src)
}
func (m *UpdateUsualDeviceResp) XXX_Size() int {
	return xxx_messageInfo_UpdateUsualDeviceResp.Size(m)
}
func (m *UpdateUsualDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUsualDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUsualDeviceResp proto.InternalMessageInfo

// 记录短信验证发起
type RecordMessageCheckReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	CheckType            uint32   `protobuf:"varint,2,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	MessageContent       string   `protobuf:"bytes,3,opt,name=message_content,json=messageContent,proto3" json:"message_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordMessageCheckReq) Reset()         { *m = RecordMessageCheckReq{} }
func (m *RecordMessageCheckReq) String() string { return proto.CompactTextString(m) }
func (*RecordMessageCheckReq) ProtoMessage()    {}
func (*RecordMessageCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{4}
}
func (m *RecordMessageCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordMessageCheckReq.Unmarshal(m, b)
}
func (m *RecordMessageCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordMessageCheckReq.Marshal(b, m, deterministic)
}
func (dst *RecordMessageCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordMessageCheckReq.Merge(dst, src)
}
func (m *RecordMessageCheckReq) XXX_Size() int {
	return xxx_messageInfo_RecordMessageCheckReq.Size(m)
}
func (m *RecordMessageCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordMessageCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordMessageCheckReq proto.InternalMessageInfo

func (m *RecordMessageCheckReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RecordMessageCheckReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

func (m *RecordMessageCheckReq) GetMessageContent() string {
	if m != nil {
		return m.MessageContent
	}
	return ""
}

type RecordMessageCheckResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordMessageCheckResp) Reset()         { *m = RecordMessageCheckResp{} }
func (m *RecordMessageCheckResp) String() string { return proto.CompactTextString(m) }
func (*RecordMessageCheckResp) ProtoMessage()    {}
func (*RecordMessageCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{5}
}
func (m *RecordMessageCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordMessageCheckResp.Unmarshal(m, b)
}
func (m *RecordMessageCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordMessageCheckResp.Marshal(b, m, deterministic)
}
func (dst *RecordMessageCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordMessageCheckResp.Merge(dst, src)
}
func (m *RecordMessageCheckResp) XXX_Size() int {
	return xxx_messageInfo_RecordMessageCheckResp.Size(m)
}
func (m *RecordMessageCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordMessageCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordMessageCheckResp proto.InternalMessageInfo

// 验证短信
type CheckMessageReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	CheckType            uint32   `protobuf:"varint,2,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	MessageContent       string   `protobuf:"bytes,3,opt,name=message_content,json=messageContent,proto3" json:"message_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckMessageReq) Reset()         { *m = CheckMessageReq{} }
func (m *CheckMessageReq) String() string { return proto.CompactTextString(m) }
func (*CheckMessageReq) ProtoMessage()    {}
func (*CheckMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{6}
}
func (m *CheckMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckMessageReq.Unmarshal(m, b)
}
func (m *CheckMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckMessageReq.Marshal(b, m, deterministic)
}
func (dst *CheckMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckMessageReq.Merge(dst, src)
}
func (m *CheckMessageReq) XXX_Size() int {
	return xxx_messageInfo_CheckMessageReq.Size(m)
}
func (m *CheckMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckMessageReq proto.InternalMessageInfo

func (m *CheckMessageReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *CheckMessageReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

func (m *CheckMessageReq) GetMessageContent() string {
	if m != nil {
		return m.MessageContent
	}
	return ""
}

type CheckMessageResp struct {
	Result               bool     `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckMessageResp) Reset()         { *m = CheckMessageResp{} }
func (m *CheckMessageResp) String() string { return proto.CompactTextString(m) }
func (*CheckMessageResp) ProtoMessage()    {}
func (*CheckMessageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{7}
}
func (m *CheckMessageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckMessageResp.Unmarshal(m, b)
}
func (m *CheckMessageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckMessageResp.Marshal(b, m, deterministic)
}
func (dst *CheckMessageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckMessageResp.Merge(dst, src)
}
func (m *CheckMessageResp) XXX_Size() int {
	return xxx_messageInfo_CheckMessageResp.Size(m)
}
func (m *CheckMessageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckMessageResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckMessageResp proto.InternalMessageInfo

func (m *CheckMessageResp) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

// 删除常用设备
type DelUsualDeviceReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	CheckType            uint32   `protobuf:"varint,3,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUsualDeviceReq) Reset()         { *m = DelUsualDeviceReq{} }
func (m *DelUsualDeviceReq) String() string { return proto.CompactTextString(m) }
func (*DelUsualDeviceReq) ProtoMessage()    {}
func (*DelUsualDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{8}
}
func (m *DelUsualDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUsualDeviceReq.Unmarshal(m, b)
}
func (m *DelUsualDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUsualDeviceReq.Marshal(b, m, deterministic)
}
func (dst *DelUsualDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUsualDeviceReq.Merge(dst, src)
}
func (m *DelUsualDeviceReq) XXX_Size() int {
	return xxx_messageInfo_DelUsualDeviceReq.Size(m)
}
func (m *DelUsualDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUsualDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUsualDeviceReq proto.InternalMessageInfo

func (m *DelUsualDeviceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelUsualDeviceReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *DelUsualDeviceReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

type DelUsualDeviceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUsualDeviceResp) Reset()         { *m = DelUsualDeviceResp{} }
func (m *DelUsualDeviceResp) String() string { return proto.CompactTextString(m) }
func (*DelUsualDeviceResp) ProtoMessage()    {}
func (*DelUsualDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{9}
}
func (m *DelUsualDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUsualDeviceResp.Unmarshal(m, b)
}
func (m *DelUsualDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUsualDeviceResp.Marshal(b, m, deterministic)
}
func (dst *DelUsualDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUsualDeviceResp.Merge(dst, src)
}
func (m *DelUsualDeviceResp) XXX_Size() int {
	return xxx_messageInfo_DelUsualDeviceResp.Size(m)
}
func (m *DelUsualDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUsualDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelUsualDeviceResp proto.InternalMessageInfo

// 记录短信验证随机字符
type RecordRandStringReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	CheckType            uint32   `protobuf:"varint,2,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	RandString           string   `protobuf:"bytes,3,opt,name=rand_string,json=randString,proto3" json:"rand_string,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordRandStringReq) Reset()         { *m = RecordRandStringReq{} }
func (m *RecordRandStringReq) String() string { return proto.CompactTextString(m) }
func (*RecordRandStringReq) ProtoMessage()    {}
func (*RecordRandStringReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{10}
}
func (m *RecordRandStringReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordRandStringReq.Unmarshal(m, b)
}
func (m *RecordRandStringReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordRandStringReq.Marshal(b, m, deterministic)
}
func (dst *RecordRandStringReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordRandStringReq.Merge(dst, src)
}
func (m *RecordRandStringReq) XXX_Size() int {
	return xxx_messageInfo_RecordRandStringReq.Size(m)
}
func (m *RecordRandStringReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordRandStringReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordRandStringReq proto.InternalMessageInfo

func (m *RecordRandStringReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RecordRandStringReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

func (m *RecordRandStringReq) GetRandString() string {
	if m != nil {
		return m.RandString
	}
	return ""
}

type RecordRandStringResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordRandStringResp) Reset()         { *m = RecordRandStringResp{} }
func (m *RecordRandStringResp) String() string { return proto.CompactTextString(m) }
func (*RecordRandStringResp) ProtoMessage()    {}
func (*RecordRandStringResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{11}
}
func (m *RecordRandStringResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordRandStringResp.Unmarshal(m, b)
}
func (m *RecordRandStringResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordRandStringResp.Marshal(b, m, deterministic)
}
func (dst *RecordRandStringResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordRandStringResp.Merge(dst, src)
}
func (m *RecordRandStringResp) XXX_Size() int {
	return xxx_messageInfo_RecordRandStringResp.Size(m)
}
func (m *RecordRandStringResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordRandStringResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordRandStringResp proto.InternalMessageInfo

// 获取短信验证随机字符
type GetRandStringReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	CheckType            uint32   `protobuf:"varint,2,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRandStringReq) Reset()         { *m = GetRandStringReq{} }
func (m *GetRandStringReq) String() string { return proto.CompactTextString(m) }
func (*GetRandStringReq) ProtoMessage()    {}
func (*GetRandStringReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{12}
}
func (m *GetRandStringReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRandStringReq.Unmarshal(m, b)
}
func (m *GetRandStringReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRandStringReq.Marshal(b, m, deterministic)
}
func (dst *GetRandStringReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRandStringReq.Merge(dst, src)
}
func (m *GetRandStringReq) XXX_Size() int {
	return xxx_messageInfo_GetRandStringReq.Size(m)
}
func (m *GetRandStringReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRandStringReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRandStringReq proto.InternalMessageInfo

func (m *GetRandStringReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *GetRandStringReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

type GetRandStringResp struct {
	RandString           string   `protobuf:"bytes,1,opt,name=rand_string,json=randString,proto3" json:"rand_string,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRandStringResp) Reset()         { *m = GetRandStringResp{} }
func (m *GetRandStringResp) String() string { return proto.CompactTextString(m) }
func (*GetRandStringResp) ProtoMessage()    {}
func (*GetRandStringResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{13}
}
func (m *GetRandStringResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRandStringResp.Unmarshal(m, b)
}
func (m *GetRandStringResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRandStringResp.Marshal(b, m, deterministic)
}
func (dst *GetRandStringResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRandStringResp.Merge(dst, src)
}
func (m *GetRandStringResp) XXX_Size() int {
	return xxx_messageInfo_GetRandStringResp.Size(m)
}
func (m *GetRandStringResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRandStringResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRandStringResp proto.InternalMessageInfo

func (m *GetRandStringResp) GetRandString() string {
	if m != nil {
		return m.RandString
	}
	return ""
}

// 记录验证请求
type RecordDeviceCheckReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	CheckType            uint32   `protobuf:"varint,3,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordDeviceCheckReq) Reset()         { *m = RecordDeviceCheckReq{} }
func (m *RecordDeviceCheckReq) String() string { return proto.CompactTextString(m) }
func (*RecordDeviceCheckReq) ProtoMessage()    {}
func (*RecordDeviceCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{14}
}
func (m *RecordDeviceCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordDeviceCheckReq.Unmarshal(m, b)
}
func (m *RecordDeviceCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordDeviceCheckReq.Marshal(b, m, deterministic)
}
func (dst *RecordDeviceCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordDeviceCheckReq.Merge(dst, src)
}
func (m *RecordDeviceCheckReq) XXX_Size() int {
	return xxx_messageInfo_RecordDeviceCheckReq.Size(m)
}
func (m *RecordDeviceCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordDeviceCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordDeviceCheckReq proto.InternalMessageInfo

func (m *RecordDeviceCheckReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordDeviceCheckReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *RecordDeviceCheckReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

type RecordDeviceCheckResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordDeviceCheckResp) Reset()         { *m = RecordDeviceCheckResp{} }
func (m *RecordDeviceCheckResp) String() string { return proto.CompactTextString(m) }
func (*RecordDeviceCheckResp) ProtoMessage()    {}
func (*RecordDeviceCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{15}
}
func (m *RecordDeviceCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordDeviceCheckResp.Unmarshal(m, b)
}
func (m *RecordDeviceCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordDeviceCheckResp.Marshal(b, m, deterministic)
}
func (dst *RecordDeviceCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordDeviceCheckResp.Merge(dst, src)
}
func (m *RecordDeviceCheckResp) XXX_Size() int {
	return xxx_messageInfo_RecordDeviceCheckResp.Size(m)
}
func (m *RecordDeviceCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordDeviceCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordDeviceCheckResp proto.InternalMessageInfo

// 记录成功验证
type RecordCheckResultReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	CheckType            uint32   `protobuf:"varint,3,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordCheckResultReq) Reset()         { *m = RecordCheckResultReq{} }
func (m *RecordCheckResultReq) String() string { return proto.CompactTextString(m) }
func (*RecordCheckResultReq) ProtoMessage()    {}
func (*RecordCheckResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{16}
}
func (m *RecordCheckResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordCheckResultReq.Unmarshal(m, b)
}
func (m *RecordCheckResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordCheckResultReq.Marshal(b, m, deterministic)
}
func (dst *RecordCheckResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordCheckResultReq.Merge(dst, src)
}
func (m *RecordCheckResultReq) XXX_Size() int {
	return xxx_messageInfo_RecordCheckResultReq.Size(m)
}
func (m *RecordCheckResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordCheckResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordCheckResultReq proto.InternalMessageInfo

func (m *RecordCheckResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordCheckResultReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *RecordCheckResultReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

type RecordCheckResultResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordCheckResultResp) Reset()         { *m = RecordCheckResultResp{} }
func (m *RecordCheckResultResp) String() string { return proto.CompactTextString(m) }
func (*RecordCheckResultResp) ProtoMessage()    {}
func (*RecordCheckResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{17}
}
func (m *RecordCheckResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordCheckResultResp.Unmarshal(m, b)
}
func (m *RecordCheckResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordCheckResultResp.Marshal(b, m, deterministic)
}
func (dst *RecordCheckResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordCheckResultResp.Merge(dst, src)
}
func (m *RecordCheckResultResp) XXX_Size() int {
	return xxx_messageInfo_RecordCheckResultResp.Size(m)
}
func (m *RecordCheckResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordCheckResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordCheckResultResp proto.InternalMessageInfo

// 获取短信验证完整信息
type GetMsgContentReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	CheckType            uint32   `protobuf:"varint,3,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMsgContentReq) Reset()         { *m = GetMsgContentReq{} }
func (m *GetMsgContentReq) String() string { return proto.CompactTextString(m) }
func (*GetMsgContentReq) ProtoMessage()    {}
func (*GetMsgContentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{18}
}
func (m *GetMsgContentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMsgContentReq.Unmarshal(m, b)
}
func (m *GetMsgContentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMsgContentReq.Marshal(b, m, deterministic)
}
func (dst *GetMsgContentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMsgContentReq.Merge(dst, src)
}
func (m *GetMsgContentReq) XXX_Size() int {
	return xxx_messageInfo_GetMsgContentReq.Size(m)
}
func (m *GetMsgContentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMsgContentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMsgContentReq proto.InternalMessageInfo

func (m *GetMsgContentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMsgContentReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *GetMsgContentReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

type GetMsgContentResp struct {
	MessageContent       string   `protobuf:"bytes,1,opt,name=message_content,json=messageContent,proto3" json:"message_content,omitempty"`
	BindPhone            string   `protobuf:"bytes,2,opt,name=bind_phone,json=bindPhone,proto3" json:"bind_phone,omitempty"`
	MessageTarget        string   `protobuf:"bytes,3,opt,name=message_target,json=messageTarget,proto3" json:"message_target,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMsgContentResp) Reset()         { *m = GetMsgContentResp{} }
func (m *GetMsgContentResp) String() string { return proto.CompactTextString(m) }
func (*GetMsgContentResp) ProtoMessage()    {}
func (*GetMsgContentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{19}
}
func (m *GetMsgContentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMsgContentResp.Unmarshal(m, b)
}
func (m *GetMsgContentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMsgContentResp.Marshal(b, m, deterministic)
}
func (dst *GetMsgContentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMsgContentResp.Merge(dst, src)
}
func (m *GetMsgContentResp) XXX_Size() int {
	return xxx_messageInfo_GetMsgContentResp.Size(m)
}
func (m *GetMsgContentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMsgContentResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMsgContentResp proto.InternalMessageInfo

func (m *GetMsgContentResp) GetMessageContent() string {
	if m != nil {
		return m.MessageContent
	}
	return ""
}

func (m *GetMsgContentResp) GetBindPhone() string {
	if m != nil {
		return m.BindPhone
	}
	return ""
}

func (m *GetMsgContentResp) GetMessageTarget() string {
	if m != nil {
		return m.MessageTarget
	}
	return ""
}

// 获取短信验证完整信息
type CheckVerifyMessageReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	CheckType            uint32   `protobuf:"varint,3,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckVerifyMessageReq) Reset()         { *m = CheckVerifyMessageReq{} }
func (m *CheckVerifyMessageReq) String() string { return proto.CompactTextString(m) }
func (*CheckVerifyMessageReq) ProtoMessage()    {}
func (*CheckVerifyMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{20}
}
func (m *CheckVerifyMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckVerifyMessageReq.Unmarshal(m, b)
}
func (m *CheckVerifyMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckVerifyMessageReq.Marshal(b, m, deterministic)
}
func (dst *CheckVerifyMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckVerifyMessageReq.Merge(dst, src)
}
func (m *CheckVerifyMessageReq) XXX_Size() int {
	return xxx_messageInfo_CheckVerifyMessageReq.Size(m)
}
func (m *CheckVerifyMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckVerifyMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckVerifyMessageReq proto.InternalMessageInfo

func (m *CheckVerifyMessageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckVerifyMessageReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *CheckVerifyMessageReq) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

type CheckVerifyMessageResp struct {
	CheckResult          bool     `protobuf:"varint,1,opt,name=check_result,json=checkResult,proto3" json:"check_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckVerifyMessageResp) Reset()         { *m = CheckVerifyMessageResp{} }
func (m *CheckVerifyMessageResp) String() string { return proto.CompactTextString(m) }
func (*CheckVerifyMessageResp) ProtoMessage()    {}
func (*CheckVerifyMessageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{21}
}
func (m *CheckVerifyMessageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckVerifyMessageResp.Unmarshal(m, b)
}
func (m *CheckVerifyMessageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckVerifyMessageResp.Marshal(b, m, deterministic)
}
func (dst *CheckVerifyMessageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckVerifyMessageResp.Merge(dst, src)
}
func (m *CheckVerifyMessageResp) XXX_Size() int {
	return xxx_messageInfo_CheckVerifyMessageResp.Size(m)
}
func (m *CheckVerifyMessageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckVerifyMessageResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckVerifyMessageResp proto.InternalMessageInfo

func (m *CheckVerifyMessageResp) GetCheckResult() bool {
	if m != nil {
		return m.CheckResult
	}
	return false
}

type UpdateConsumeVerifyUidListReq struct {
	IsDel                bool     `protobuf:"varint,1,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	UidList              []uint64 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	Reason               string   `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	UpdateAt             uint64   `protobuf:"varint,5,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateConsumeVerifyUidListReq) Reset()         { *m = UpdateConsumeVerifyUidListReq{} }
func (m *UpdateConsumeVerifyUidListReq) String() string { return proto.CompactTextString(m) }
func (*UpdateConsumeVerifyUidListReq) ProtoMessage()    {}
func (*UpdateConsumeVerifyUidListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{22}
}
func (m *UpdateConsumeVerifyUidListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateConsumeVerifyUidListReq.Unmarshal(m, b)
}
func (m *UpdateConsumeVerifyUidListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateConsumeVerifyUidListReq.Marshal(b, m, deterministic)
}
func (dst *UpdateConsumeVerifyUidListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateConsumeVerifyUidListReq.Merge(dst, src)
}
func (m *UpdateConsumeVerifyUidListReq) XXX_Size() int {
	return xxx_messageInfo_UpdateConsumeVerifyUidListReq.Size(m)
}
func (m *UpdateConsumeVerifyUidListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateConsumeVerifyUidListReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateConsumeVerifyUidListReq proto.InternalMessageInfo

func (m *UpdateConsumeVerifyUidListReq) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

func (m *UpdateConsumeVerifyUidListReq) GetUidList() []uint64 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *UpdateConsumeVerifyUidListReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *UpdateConsumeVerifyUidListReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *UpdateConsumeVerifyUidListReq) GetUpdateAt() uint64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

type UpdateConsumeVerifyUidListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateConsumeVerifyUidListResp) Reset()         { *m = UpdateConsumeVerifyUidListResp{} }
func (m *UpdateConsumeVerifyUidListResp) String() string { return proto.CompactTextString(m) }
func (*UpdateConsumeVerifyUidListResp) ProtoMessage()    {}
func (*UpdateConsumeVerifyUidListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{23}
}
func (m *UpdateConsumeVerifyUidListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateConsumeVerifyUidListResp.Unmarshal(m, b)
}
func (m *UpdateConsumeVerifyUidListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateConsumeVerifyUidListResp.Marshal(b, m, deterministic)
}
func (dst *UpdateConsumeVerifyUidListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateConsumeVerifyUidListResp.Merge(dst, src)
}
func (m *UpdateConsumeVerifyUidListResp) XXX_Size() int {
	return xxx_messageInfo_UpdateConsumeVerifyUidListResp.Size(m)
}
func (m *UpdateConsumeVerifyUidListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateConsumeVerifyUidListResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateConsumeVerifyUidListResp proto.InternalMessageInfo

type InConsumeVerifyUidListReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InConsumeVerifyUidListReq) Reset()         { *m = InConsumeVerifyUidListReq{} }
func (m *InConsumeVerifyUidListReq) String() string { return proto.CompactTextString(m) }
func (*InConsumeVerifyUidListReq) ProtoMessage()    {}
func (*InConsumeVerifyUidListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{24}
}
func (m *InConsumeVerifyUidListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InConsumeVerifyUidListReq.Unmarshal(m, b)
}
func (m *InConsumeVerifyUidListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InConsumeVerifyUidListReq.Marshal(b, m, deterministic)
}
func (dst *InConsumeVerifyUidListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InConsumeVerifyUidListReq.Merge(dst, src)
}
func (m *InConsumeVerifyUidListReq) XXX_Size() int {
	return xxx_messageInfo_InConsumeVerifyUidListReq.Size(m)
}
func (m *InConsumeVerifyUidListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InConsumeVerifyUidListReq.DiscardUnknown(m)
}

var xxx_messageInfo_InConsumeVerifyUidListReq proto.InternalMessageInfo

func (m *InConsumeVerifyUidListReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type InConsumeVerifyUidListResp struct {
	InList               bool     `protobuf:"varint,1,opt,name=in_list,json=inList,proto3" json:"in_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InConsumeVerifyUidListResp) Reset()         { *m = InConsumeVerifyUidListResp{} }
func (m *InConsumeVerifyUidListResp) String() string { return proto.CompactTextString(m) }
func (*InConsumeVerifyUidListResp) ProtoMessage()    {}
func (*InConsumeVerifyUidListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{25}
}
func (m *InConsumeVerifyUidListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InConsumeVerifyUidListResp.Unmarshal(m, b)
}
func (m *InConsumeVerifyUidListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InConsumeVerifyUidListResp.Marshal(b, m, deterministic)
}
func (dst *InConsumeVerifyUidListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InConsumeVerifyUidListResp.Merge(dst, src)
}
func (m *InConsumeVerifyUidListResp) XXX_Size() int {
	return xxx_messageInfo_InConsumeVerifyUidListResp.Size(m)
}
func (m *InConsumeVerifyUidListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InConsumeVerifyUidListResp.DiscardUnknown(m)
}

var xxx_messageInfo_InConsumeVerifyUidListResp proto.InternalMessageInfo

func (m *InConsumeVerifyUidListResp) GetInList() bool {
	if m != nil {
		return m.InList
	}
	return false
}

type GetDeviceAuthErrorReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,2,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDeviceAuthErrorReq) Reset()         { *m = GetDeviceAuthErrorReq{} }
func (m *GetDeviceAuthErrorReq) String() string { return proto.CompactTextString(m) }
func (*GetDeviceAuthErrorReq) ProtoMessage()    {}
func (*GetDeviceAuthErrorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{26}
}
func (m *GetDeviceAuthErrorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeviceAuthErrorReq.Unmarshal(m, b)
}
func (m *GetDeviceAuthErrorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeviceAuthErrorReq.Marshal(b, m, deterministic)
}
func (dst *GetDeviceAuthErrorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeviceAuthErrorReq.Merge(dst, src)
}
func (m *GetDeviceAuthErrorReq) XXX_Size() int {
	return xxx_messageInfo_GetDeviceAuthErrorReq.Size(m)
}
func (m *GetDeviceAuthErrorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeviceAuthErrorReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeviceAuthErrorReq proto.InternalMessageInfo

func (m *GetDeviceAuthErrorReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetDeviceAuthErrorReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetDeviceAuthErrorReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetDeviceAuthErrorResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDeviceAuthErrorResp) Reset()         { *m = GetDeviceAuthErrorResp{} }
func (m *GetDeviceAuthErrorResp) String() string { return proto.CompactTextString(m) }
func (*GetDeviceAuthErrorResp) ProtoMessage()    {}
func (*GetDeviceAuthErrorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{27}
}
func (m *GetDeviceAuthErrorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeviceAuthErrorResp.Unmarshal(m, b)
}
func (m *GetDeviceAuthErrorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeviceAuthErrorResp.Marshal(b, m, deterministic)
}
func (dst *GetDeviceAuthErrorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeviceAuthErrorResp.Merge(dst, src)
}
func (m *GetDeviceAuthErrorResp) XXX_Size() int {
	return xxx_messageInfo_GetDeviceAuthErrorResp.Size(m)
}
func (m *GetDeviceAuthErrorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeviceAuthErrorResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeviceAuthErrorResp proto.InternalMessageInfo

type UserLoginDevice struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	LoginAt              int64    `protobuf:"varint,2,opt,name=login_at,json=loginAt,proto3" json:"login_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLoginDevice) Reset()         { *m = UserLoginDevice{} }
func (m *UserLoginDevice) String() string { return proto.CompactTextString(m) }
func (*UserLoginDevice) ProtoMessage()    {}
func (*UserLoginDevice) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{28}
}
func (m *UserLoginDevice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLoginDevice.Unmarshal(m, b)
}
func (m *UserLoginDevice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLoginDevice.Marshal(b, m, deterministic)
}
func (dst *UserLoginDevice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLoginDevice.Merge(dst, src)
}
func (m *UserLoginDevice) XXX_Size() int {
	return xxx_messageInfo_UserLoginDevice.Size(m)
}
func (m *UserLoginDevice) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLoginDevice.DiscardUnknown(m)
}

var xxx_messageInfo_UserLoginDevice proto.InternalMessageInfo

func (m *UserLoginDevice) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserLoginDevice) GetLoginAt() int64 {
	if m != nil {
		return m.LoginAt
	}
	return 0
}

type GetUserUsualDeviceReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            int64    `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Limit                int32    `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserUsualDeviceReq) Reset()         { *m = GetUserUsualDeviceReq{} }
func (m *GetUserUsualDeviceReq) String() string { return proto.CompactTextString(m) }
func (*GetUserUsualDeviceReq) ProtoMessage()    {}
func (*GetUserUsualDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{29}
}
func (m *GetUserUsualDeviceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserUsualDeviceReq.Unmarshal(m, b)
}
func (m *GetUserUsualDeviceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserUsualDeviceReq.Marshal(b, m, deterministic)
}
func (dst *GetUserUsualDeviceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserUsualDeviceReq.Merge(dst, src)
}
func (m *GetUserUsualDeviceReq) XXX_Size() int {
	return xxx_messageInfo_GetUserUsualDeviceReq.Size(m)
}
func (m *GetUserUsualDeviceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserUsualDeviceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserUsualDeviceReq proto.InternalMessageInfo

func (m *GetUserUsualDeviceReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserUsualDeviceReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetUserUsualDeviceReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetUserUsualDeviceReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserUsualDeviceResp struct {
	Uid                  uint64             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LoginDevices         []*UserLoginDevice `protobuf:"bytes,2,rep,name=login_devices,json=loginDevices,proto3" json:"login_devices,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserUsualDeviceResp) Reset()         { *m = GetUserUsualDeviceResp{} }
func (m *GetUserUsualDeviceResp) String() string { return proto.CompactTextString(m) }
func (*GetUserUsualDeviceResp) ProtoMessage()    {}
func (*GetUserUsualDeviceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_usual_device_svr_aa366f201bbbf6b8, []int{30}
}
func (m *GetUserUsualDeviceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserUsualDeviceResp.Unmarshal(m, b)
}
func (m *GetUserUsualDeviceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserUsualDeviceResp.Marshal(b, m, deterministic)
}
func (dst *GetUserUsualDeviceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserUsualDeviceResp.Merge(dst, src)
}
func (m *GetUserUsualDeviceResp) XXX_Size() int {
	return xxx_messageInfo_GetUserUsualDeviceResp.Size(m)
}
func (m *GetUserUsualDeviceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserUsualDeviceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserUsualDeviceResp proto.InternalMessageInfo

func (m *GetUserUsualDeviceResp) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserUsualDeviceResp) GetLoginDevices() []*UserLoginDevice {
	if m != nil {
		return m.LoginDevices
	}
	return nil
}

func init() {
	proto.RegisterType((*CheckUsualDeviceReq)(nil), "usual_device_svr.CheckUsualDeviceReq")
	proto.RegisterType((*CheckUsualDeviceResp)(nil), "usual_device_svr.CheckUsualDeviceResp")
	proto.RegisterType((*UpdateUsualDeviceReq)(nil), "usual_device_svr.UpdateUsualDeviceReq")
	proto.RegisterType((*UpdateUsualDeviceResp)(nil), "usual_device_svr.UpdateUsualDeviceResp")
	proto.RegisterType((*RecordMessageCheckReq)(nil), "usual_device_svr.RecordMessageCheckReq")
	proto.RegisterType((*RecordMessageCheckResp)(nil), "usual_device_svr.RecordMessageCheckResp")
	proto.RegisterType((*CheckMessageReq)(nil), "usual_device_svr.CheckMessageReq")
	proto.RegisterType((*CheckMessageResp)(nil), "usual_device_svr.CheckMessageResp")
	proto.RegisterType((*DelUsualDeviceReq)(nil), "usual_device_svr.DelUsualDeviceReq")
	proto.RegisterType((*DelUsualDeviceResp)(nil), "usual_device_svr.DelUsualDeviceResp")
	proto.RegisterType((*RecordRandStringReq)(nil), "usual_device_svr.RecordRandStringReq")
	proto.RegisterType((*RecordRandStringResp)(nil), "usual_device_svr.RecordRandStringResp")
	proto.RegisterType((*GetRandStringReq)(nil), "usual_device_svr.GetRandStringReq")
	proto.RegisterType((*GetRandStringResp)(nil), "usual_device_svr.GetRandStringResp")
	proto.RegisterType((*RecordDeviceCheckReq)(nil), "usual_device_svr.RecordDeviceCheckReq")
	proto.RegisterType((*RecordDeviceCheckResp)(nil), "usual_device_svr.RecordDeviceCheckResp")
	proto.RegisterType((*RecordCheckResultReq)(nil), "usual_device_svr.RecordCheckResultReq")
	proto.RegisterType((*RecordCheckResultResp)(nil), "usual_device_svr.RecordCheckResultResp")
	proto.RegisterType((*GetMsgContentReq)(nil), "usual_device_svr.GetMsgContentReq")
	proto.RegisterType((*GetMsgContentResp)(nil), "usual_device_svr.GetMsgContentResp")
	proto.RegisterType((*CheckVerifyMessageReq)(nil), "usual_device_svr.CheckVerifyMessageReq")
	proto.RegisterType((*CheckVerifyMessageResp)(nil), "usual_device_svr.CheckVerifyMessageResp")
	proto.RegisterType((*UpdateConsumeVerifyUidListReq)(nil), "usual_device_svr.UpdateConsumeVerifyUidListReq")
	proto.RegisterType((*UpdateConsumeVerifyUidListResp)(nil), "usual_device_svr.UpdateConsumeVerifyUidListResp")
	proto.RegisterType((*InConsumeVerifyUidListReq)(nil), "usual_device_svr.InConsumeVerifyUidListReq")
	proto.RegisterType((*InConsumeVerifyUidListResp)(nil), "usual_device_svr.InConsumeVerifyUidListResp")
	proto.RegisterType((*GetDeviceAuthErrorReq)(nil), "usual_device_svr.GetDeviceAuthErrorReq")
	proto.RegisterType((*GetDeviceAuthErrorResp)(nil), "usual_device_svr.GetDeviceAuthErrorResp")
	proto.RegisterType((*UserLoginDevice)(nil), "usual_device_svr.UserLoginDevice")
	proto.RegisterType((*GetUserUsualDeviceReq)(nil), "usual_device_svr.GetUserUsualDeviceReq")
	proto.RegisterType((*GetUserUsualDeviceResp)(nil), "usual_device_svr.GetUserUsualDeviceResp")
	proto.RegisterEnum("usual_device_svr.CheckType", CheckType_name, CheckType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UsualDeviceSvrClient is the client API for UsualDeviceSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UsualDeviceSvrClient interface {
	CheckUsualDevice(ctx context.Context, in *CheckUsualDeviceReq, opts ...grpc.CallOption) (*CheckUsualDeviceResp, error)
	UpdateUsualDevice(ctx context.Context, in *UpdateUsualDeviceReq, opts ...grpc.CallOption) (*UpdateUsualDeviceResp, error)
	RecordMessageCheck(ctx context.Context, in *RecordMessageCheckReq, opts ...grpc.CallOption) (*RecordMessageCheckResp, error)
	CheckMessage(ctx context.Context, in *CheckMessageReq, opts ...grpc.CallOption) (*CheckMessageResp, error)
	DelUsualDevice(ctx context.Context, in *DelUsualDeviceReq, opts ...grpc.CallOption) (*DelUsualDeviceResp, error)
	RecordRandString(ctx context.Context, in *RecordRandStringReq, opts ...grpc.CallOption) (*RecordRandStringResp, error)
	GetRandString(ctx context.Context, in *GetRandStringReq, opts ...grpc.CallOption) (*GetRandStringResp, error)
	RecordDeviceCheck(ctx context.Context, in *RecordDeviceCheckReq, opts ...grpc.CallOption) (*RecordDeviceCheckResp, error)
	RecordCheckResult(ctx context.Context, in *RecordCheckResultReq, opts ...grpc.CallOption) (*RecordCheckResultResp, error)
	GetMsgContent(ctx context.Context, in *GetMsgContentReq, opts ...grpc.CallOption) (*GetMsgContentResp, error)
	CheckVerifyMessage(ctx context.Context, in *CheckVerifyMessageReq, opts ...grpc.CallOption) (*CheckVerifyMessageResp, error)
	GetUserUsualDevice(ctx context.Context, in *GetUserUsualDeviceReq, opts ...grpc.CallOption) (*GetUserUsualDeviceResp, error)
	// 此列表的用户，开放【人脸识别】、【短信编辑】验证方式
	UpdateConsumeVerifyUidList(ctx context.Context, in *UpdateConsumeVerifyUidListReq, opts ...grpc.CallOption) (*UpdateConsumeVerifyUidListResp, error)
	InConsumeVerifyUidList(ctx context.Context, in *InConsumeVerifyUidListReq, opts ...grpc.CallOption) (*InConsumeVerifyUidListResp, error)
	GetDeviceAuthError(ctx context.Context, in *GetDeviceAuthErrorReq, opts ...grpc.CallOption) (*GetDeviceAuthErrorResp, error)
}

type usualDeviceSvrClient struct {
	cc *grpc.ClientConn
}

func NewUsualDeviceSvrClient(cc *grpc.ClientConn) UsualDeviceSvrClient {
	return &usualDeviceSvrClient{cc}
}

func (c *usualDeviceSvrClient) CheckUsualDevice(ctx context.Context, in *CheckUsualDeviceReq, opts ...grpc.CallOption) (*CheckUsualDeviceResp, error) {
	out := new(CheckUsualDeviceResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/CheckUsualDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) UpdateUsualDevice(ctx context.Context, in *UpdateUsualDeviceReq, opts ...grpc.CallOption) (*UpdateUsualDeviceResp, error) {
	out := new(UpdateUsualDeviceResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/UpdateUsualDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) RecordMessageCheck(ctx context.Context, in *RecordMessageCheckReq, opts ...grpc.CallOption) (*RecordMessageCheckResp, error) {
	out := new(RecordMessageCheckResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/RecordMessageCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) CheckMessage(ctx context.Context, in *CheckMessageReq, opts ...grpc.CallOption) (*CheckMessageResp, error) {
	out := new(CheckMessageResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/CheckMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) DelUsualDevice(ctx context.Context, in *DelUsualDeviceReq, opts ...grpc.CallOption) (*DelUsualDeviceResp, error) {
	out := new(DelUsualDeviceResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/DelUsualDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) RecordRandString(ctx context.Context, in *RecordRandStringReq, opts ...grpc.CallOption) (*RecordRandStringResp, error) {
	out := new(RecordRandStringResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/RecordRandString", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) GetRandString(ctx context.Context, in *GetRandStringReq, opts ...grpc.CallOption) (*GetRandStringResp, error) {
	out := new(GetRandStringResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/GetRandString", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) RecordDeviceCheck(ctx context.Context, in *RecordDeviceCheckReq, opts ...grpc.CallOption) (*RecordDeviceCheckResp, error) {
	out := new(RecordDeviceCheckResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/RecordDeviceCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) RecordCheckResult(ctx context.Context, in *RecordCheckResultReq, opts ...grpc.CallOption) (*RecordCheckResultResp, error) {
	out := new(RecordCheckResultResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/RecordCheckResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) GetMsgContent(ctx context.Context, in *GetMsgContentReq, opts ...grpc.CallOption) (*GetMsgContentResp, error) {
	out := new(GetMsgContentResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/GetMsgContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) CheckVerifyMessage(ctx context.Context, in *CheckVerifyMessageReq, opts ...grpc.CallOption) (*CheckVerifyMessageResp, error) {
	out := new(CheckVerifyMessageResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/CheckVerifyMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) GetUserUsualDevice(ctx context.Context, in *GetUserUsualDeviceReq, opts ...grpc.CallOption) (*GetUserUsualDeviceResp, error) {
	out := new(GetUserUsualDeviceResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/GetUserUsualDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) UpdateConsumeVerifyUidList(ctx context.Context, in *UpdateConsumeVerifyUidListReq, opts ...grpc.CallOption) (*UpdateConsumeVerifyUidListResp, error) {
	out := new(UpdateConsumeVerifyUidListResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/UpdateConsumeVerifyUidList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) InConsumeVerifyUidList(ctx context.Context, in *InConsumeVerifyUidListReq, opts ...grpc.CallOption) (*InConsumeVerifyUidListResp, error) {
	out := new(InConsumeVerifyUidListResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/InConsumeVerifyUidList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *usualDeviceSvrClient) GetDeviceAuthError(ctx context.Context, in *GetDeviceAuthErrorReq, opts ...grpc.CallOption) (*GetDeviceAuthErrorResp, error) {
	out := new(GetDeviceAuthErrorResp)
	err := c.cc.Invoke(ctx, "/usual_device_svr.UsualDeviceSvr/GetDeviceAuthError", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UsualDeviceSvrServer is the server API for UsualDeviceSvr service.
type UsualDeviceSvrServer interface {
	CheckUsualDevice(context.Context, *CheckUsualDeviceReq) (*CheckUsualDeviceResp, error)
	UpdateUsualDevice(context.Context, *UpdateUsualDeviceReq) (*UpdateUsualDeviceResp, error)
	RecordMessageCheck(context.Context, *RecordMessageCheckReq) (*RecordMessageCheckResp, error)
	CheckMessage(context.Context, *CheckMessageReq) (*CheckMessageResp, error)
	DelUsualDevice(context.Context, *DelUsualDeviceReq) (*DelUsualDeviceResp, error)
	RecordRandString(context.Context, *RecordRandStringReq) (*RecordRandStringResp, error)
	GetRandString(context.Context, *GetRandStringReq) (*GetRandStringResp, error)
	RecordDeviceCheck(context.Context, *RecordDeviceCheckReq) (*RecordDeviceCheckResp, error)
	RecordCheckResult(context.Context, *RecordCheckResultReq) (*RecordCheckResultResp, error)
	GetMsgContent(context.Context, *GetMsgContentReq) (*GetMsgContentResp, error)
	CheckVerifyMessage(context.Context, *CheckVerifyMessageReq) (*CheckVerifyMessageResp, error)
	GetUserUsualDevice(context.Context, *GetUserUsualDeviceReq) (*GetUserUsualDeviceResp, error)
	// 此列表的用户，开放【人脸识别】、【短信编辑】验证方式
	UpdateConsumeVerifyUidList(context.Context, *UpdateConsumeVerifyUidListReq) (*UpdateConsumeVerifyUidListResp, error)
	InConsumeVerifyUidList(context.Context, *InConsumeVerifyUidListReq) (*InConsumeVerifyUidListResp, error)
	GetDeviceAuthError(context.Context, *GetDeviceAuthErrorReq) (*GetDeviceAuthErrorResp, error)
}

func RegisterUsualDeviceSvrServer(s *grpc.Server, srv UsualDeviceSvrServer) {
	s.RegisterService(&_UsualDeviceSvr_serviceDesc, srv)
}

func _UsualDeviceSvr_CheckUsualDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUsualDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).CheckUsualDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/CheckUsualDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).CheckUsualDevice(ctx, req.(*CheckUsualDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_UpdateUsualDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUsualDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).UpdateUsualDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/UpdateUsualDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).UpdateUsualDevice(ctx, req.(*UpdateUsualDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_RecordMessageCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordMessageCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).RecordMessageCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/RecordMessageCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).RecordMessageCheck(ctx, req.(*RecordMessageCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_CheckMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).CheckMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/CheckMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).CheckMessage(ctx, req.(*CheckMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_DelUsualDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUsualDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).DelUsualDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/DelUsualDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).DelUsualDevice(ctx, req.(*DelUsualDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_RecordRandString_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordRandStringReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).RecordRandString(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/RecordRandString",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).RecordRandString(ctx, req.(*RecordRandStringReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_GetRandString_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRandStringReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).GetRandString(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/GetRandString",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).GetRandString(ctx, req.(*GetRandStringReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_RecordDeviceCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordDeviceCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).RecordDeviceCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/RecordDeviceCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).RecordDeviceCheck(ctx, req.(*RecordDeviceCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_RecordCheckResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordCheckResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).RecordCheckResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/RecordCheckResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).RecordCheckResult(ctx, req.(*RecordCheckResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_GetMsgContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMsgContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).GetMsgContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/GetMsgContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).GetMsgContent(ctx, req.(*GetMsgContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_CheckVerifyMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVerifyMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).CheckVerifyMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/CheckVerifyMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).CheckVerifyMessage(ctx, req.(*CheckVerifyMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_GetUserUsualDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserUsualDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).GetUserUsualDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/GetUserUsualDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).GetUserUsualDevice(ctx, req.(*GetUserUsualDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_UpdateConsumeVerifyUidList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateConsumeVerifyUidListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).UpdateConsumeVerifyUidList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/UpdateConsumeVerifyUidList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).UpdateConsumeVerifyUidList(ctx, req.(*UpdateConsumeVerifyUidListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_InConsumeVerifyUidList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InConsumeVerifyUidListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).InConsumeVerifyUidList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/InConsumeVerifyUidList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).InConsumeVerifyUidList(ctx, req.(*InConsumeVerifyUidListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UsualDeviceSvr_GetDeviceAuthError_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceAuthErrorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UsualDeviceSvrServer).GetDeviceAuthError(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usual_device_svr.UsualDeviceSvr/GetDeviceAuthError",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UsualDeviceSvrServer).GetDeviceAuthError(ctx, req.(*GetDeviceAuthErrorReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UsualDeviceSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "usual_device_svr.UsualDeviceSvr",
	HandlerType: (*UsualDeviceSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckUsualDevice",
			Handler:    _UsualDeviceSvr_CheckUsualDevice_Handler,
		},
		{
			MethodName: "UpdateUsualDevice",
			Handler:    _UsualDeviceSvr_UpdateUsualDevice_Handler,
		},
		{
			MethodName: "RecordMessageCheck",
			Handler:    _UsualDeviceSvr_RecordMessageCheck_Handler,
		},
		{
			MethodName: "CheckMessage",
			Handler:    _UsualDeviceSvr_CheckMessage_Handler,
		},
		{
			MethodName: "DelUsualDevice",
			Handler:    _UsualDeviceSvr_DelUsualDevice_Handler,
		},
		{
			MethodName: "RecordRandString",
			Handler:    _UsualDeviceSvr_RecordRandString_Handler,
		},
		{
			MethodName: "GetRandString",
			Handler:    _UsualDeviceSvr_GetRandString_Handler,
		},
		{
			MethodName: "RecordDeviceCheck",
			Handler:    _UsualDeviceSvr_RecordDeviceCheck_Handler,
		},
		{
			MethodName: "RecordCheckResult",
			Handler:    _UsualDeviceSvr_RecordCheckResult_Handler,
		},
		{
			MethodName: "GetMsgContent",
			Handler:    _UsualDeviceSvr_GetMsgContent_Handler,
		},
		{
			MethodName: "CheckVerifyMessage",
			Handler:    _UsualDeviceSvr_CheckVerifyMessage_Handler,
		},
		{
			MethodName: "GetUserUsualDevice",
			Handler:    _UsualDeviceSvr_GetUserUsualDevice_Handler,
		},
		{
			MethodName: "UpdateConsumeVerifyUidList",
			Handler:    _UsualDeviceSvr_UpdateConsumeVerifyUidList_Handler,
		},
		{
			MethodName: "InConsumeVerifyUidList",
			Handler:    _UsualDeviceSvr_InConsumeVerifyUidList_Handler,
		},
		{
			MethodName: "GetDeviceAuthError",
			Handler:    _UsualDeviceSvr_GetDeviceAuthError_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "usual-device/usual-device-svr.proto",
}

func init() {
	proto.RegisterFile("usual-device/usual-device-svr.proto", fileDescriptor_usual_device_svr_aa366f201bbbf6b8)
}

var fileDescriptor_usual_device_svr_aa366f201bbbf6b8 = []byte{
	// 1165 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0x4f, 0x53, 0xdb, 0x46,
	0x14, 0x47, 0x18, 0x27, 0xf6, 0x03, 0x82, 0x59, 0x8c, 0x31, 0xca, 0xa4, 0x81, 0xa5, 0x29, 0x4c,
	0x5a, 0x4c, 0x87, 0x26, 0xa7, 0x9e, 0x1c, 0xe3, 0x02, 0x93, 0x60, 0x18, 0x61, 0x27, 0xd3, 0xcc,
	0x74, 0x54, 0x45, 0xda, 0x98, 0x9d, 0xc8, 0x92, 0xa2, 0x5d, 0x31, 0xc3, 0xb1, 0x9d, 0x7e, 0x92,
	0xde, 0x7a, 0xed, 0x27, 0xec, 0x68, 0x57, 0xc6, 0x96, 0xb4, 0x0a, 0xb4, 0x85, 0x9b, 0xf6, 0xfd,
	0xfb, 0xbd, 0x7d, 0xbb, 0xfb, 0xde, 0x6f, 0x04, 0x5b, 0x11, 0x8b, 0x2c, 0x77, 0xd7, 0x21, 0x97,
	0xd4, 0x26, 0x7b, 0xd3, 0x8b, 0x5d, 0x76, 0x19, 0xb6, 0x82, 0xd0, 0xe7, 0x3e, 0xaa, 0x09, 0xb9,
	0x29, 0xe5, 0x26, 0xbb, 0x0c, 0xf1, 0x1f, 0x1a, 0xac, 0x74, 0x2e, 0x88, 0xfd, 0x69, 0x10, 0x6b,
	0x0e, 0x84, 0xc2, 0x20, 0x9f, 0x51, 0x0d, 0x4a, 0x11, 0x75, 0x9a, 0xda, 0x86, 0xb6, 0xb3, 0x68,
	0xc4, 0x9f, 0xe8, 0x31, 0x54, 0x13, 0x3f, 0xea, 0x34, 0x67, 0x37, 0xb4, 0x9d, 0x05, 0xa3, 0x22,
	0x05, 0xc7, 0x0e, 0x7a, 0x02, 0x60, 0xc7, 0x51, 0x4c, 0x7e, 0x15, 0x90, 0x66, 0x49, 0x78, 0x55,
	0x85, 0xa4, 0x7f, 0x15, 0x10, 0xf4, 0x14, 0xe6, 0x13, 0x5f, 0xa1, 0x9f, 0x13, 0x7a, 0x90, 0xa2,
	0xd8, 0x00, 0xb7, 0xa0, 0x9e, 0xcf, 0x82, 0x05, 0xa8, 0x01, 0x0f, 0x42, 0xc2, 0x22, 0x97, 0x8b,
	0x4c, 0x2a, 0x46, 0xb2, 0xc2, 0x0e, 0xd4, 0x07, 0x81, 0x63, 0x71, 0x72, 0x9f, 0x69, 0xe3, 0x35,
	0x58, 0x55, 0xa0, 0xb0, 0x00, 0x47, 0xb0, 0x6a, 0x10, 0xdb, 0x0f, 0x9d, 0x13, 0xc2, 0x98, 0x35,
	0x24, 0x22, 0xf7, 0x18, 0xbf, 0x0e, 0xe5, 0xe0, 0xc2, 0xf7, 0x88, 0xc8, 0xa0, 0x6a, 0xc8, 0x45,
	0x06, 0x66, 0x36, 0x5b, 0x9d, 0x6d, 0x58, 0x1a, 0xc9, 0x38, 0xa6, 0xed, 0x7b, 0x9c, 0x78, 0x5c,
	0xa4, 0x52, 0x35, 0x1e, 0x25, 0xe2, 0x8e, 0x94, 0xe2, 0x26, 0x34, 0x54, 0xb0, 0x2c, 0xc0, 0x3e,
	0x2c, 0x89, 0x45, 0xa2, 0xb8, 0xff, 0x54, 0x9e, 0x43, 0x2d, 0x0d, 0xf8, 0x85, 0xc3, 0xb2, 0x60,
	0xf9, 0x80, 0xb8, 0xf7, 0x7a, 0x52, 0x75, 0x40, 0x59, 0x08, 0x16, 0xe0, 0x4f, 0xb0, 0x22, 0xeb,
	0x65, 0x58, 0x9e, 0x73, 0xce, 0x43, 0xea, 0x0d, 0xff, 0x73, 0x65, 0x9e, 0xc2, 0x7c, 0x68, 0x79,
	0x8e, 0xc9, 0x44, 0x98, 0xa4, 0x2a, 0x10, 0x5e, 0x07, 0xc6, 0x0d, 0xa8, 0xe7, 0xc1, 0x58, 0x80,
	0x0f, 0xa1, 0x76, 0x48, 0xf8, 0xff, 0xcf, 0x00, 0xbf, 0x80, 0xe5, 0x4c, 0x20, 0x16, 0x64, 0xd3,
	0xd2, 0x72, 0x69, 0x39, 0xe3, 0xb4, 0x64, 0x5d, 0xae, 0x6f, 0xea, 0x9d, 0xbf, 0x14, 0x05, 0x0a,
	0x0b, 0x26, 0xf0, 0x63, 0x51, 0xe4, 0xf2, 0x7b, 0x84, 0x4f, 0xa1, 0xb0, 0x00, 0xff, 0x2a, 0x8a,
	0x7f, 0xc2, 0x86, 0xc9, 0xbd, 0xbd, 0x7b, 0xe8, 0xdf, 0x35, 0x71, 0x2c, 0xd3, 0x10, 0x2c, 0x50,
	0xbd, 0x23, 0x4d, 0xf5, 0x8e, 0xe2, 0xe8, 0x1f, 0xa8, 0xe7, 0x98, 0xf2, 0x3a, 0xcc, 0x0a, 0x9b,
	0x6a, 0x2c, 0x39, 0x13, 0x57, 0xe2, 0x19, 0x8c, 0x1d, 0x4c, 0x6e, 0x85, 0x43, 0x32, 0x7e, 0x8e,
	0x8b, 0x89, 0xb4, 0x2f, 0x84, 0x98, 0xc0, 0xaa, 0xd8, 0xf9, 0x5b, 0x12, 0xd2, 0x8f, 0x57, 0x53,
	0x4d, 0xe0, 0x6e, 0xf7, 0xfa, 0x23, 0x34, 0x54, 0x30, 0x2c, 0x40, 0x9b, 0xb0, 0x20, 0x1d, 0x53,
	0x0d, 0x60, 0xde, 0x9e, 0x1c, 0x07, 0xfe, 0x53, 0x83, 0x27, 0xb2, 0x9b, 0x76, 0x7c, 0x8f, 0x45,
	0x23, 0x22, 0xa3, 0x0c, 0xa8, 0xf3, 0x86, 0x32, 0x71, 0x30, 0xab, 0xf0, 0x80, 0x32, 0xd3, 0x21,
	0x6e, 0xe2, 0x5e, 0xa6, 0xec, 0x80, 0xb8, 0x68, 0x1d, 0x2a, 0x11, 0x75, 0x4c, 0x97, 0x32, 0xde,
	0x9c, 0xdd, 0x28, 0xed, 0xcc, 0x19, 0x0f, 0x23, 0xe9, 0x84, 0x74, 0xa8, 0xf8, 0x01, 0x09, 0x2d,
	0xee, 0x87, 0x49, 0x61, 0xae, 0xd7, 0xb2, 0x1b, 0x59, 0xcc, 0xf7, 0xc4, 0xb8, 0xa9, 0x1a, 0xc9,
	0x2a, 0x2e, 0x40, 0x24, 0xd2, 0x30, 0x2d, 0xde, 0x2c, 0x6f, 0x68, 0x3b, 0x73, 0x46, 0x45, 0x0a,
	0xda, 0x1c, 0x6f, 0xc0, 0x57, 0x5f, 0xca, 0x91, 0x05, 0x78, 0x17, 0xd6, 0x8f, 0xbd, 0xa2, 0x1d,
	0x4c, 0x95, 0x7b, 0x4e, 0x94, 0x1b, 0xbf, 0x04, 0xbd, 0xc8, 0x9c, 0x05, 0x68, 0x0d, 0x1e, 0x52,
	0x4f, 0xee, 0x2c, 0x69, 0x99, 0xd4, 0x8b, 0x95, 0xf8, 0x33, 0xac, 0x1e, 0x12, 0x2e, 0x1f, 0x53,
	0x3b, 0xe2, 0x17, 0xdd, 0x30, 0xf4, 0x43, 0x25, 0x42, 0x7c, 0x45, 0x6c, 0x97, 0x12, 0x8f, 0x9b,
	0x97, 0x24, 0x64, 0xd4, 0xf7, 0x92, 0xce, 0xb1, 0x28, 0xa5, 0x6f, 0xa5, 0x30, 0x6e, 0x14, 0x89,
	0xd9, 0xd4, 0xd9, 0x82, 0x14, 0x89, 0xc3, 0x6d, 0x42, 0x43, 0x05, 0xc9, 0x02, 0x7c, 0x0c, 0x4b,
	0x03, 0x46, 0xc2, 0x37, 0xfe, 0x90, 0x7a, 0x52, 0x9f, 0xbe, 0x45, 0xf2, 0x66, 0x4f, 0x6e, 0xd1,
	0x3a, 0x54, 0xdc, 0xd8, 0x36, 0x2e, 0x70, 0x9c, 0x4b, 0xc9, 0x78, 0x28, 0xd6, 0x6d, 0x8e, 0xaf,
	0xc4, 0xbe, 0xe2, 0x68, 0xc5, 0xe3, 0x20, 0xd9, 0x57, 0xfc, 0x32, 0x48, 0x1c, 0x85, 0xd3, 0x11,
	0x49, 0xe2, 0x54, 0x85, 0xa4, 0x4f, 0x47, 0x24, 0x06, 0x21, 0x9e, 0x23, 0x95, 0x25, 0x09, 0x42,
	0x3c, 0x47, 0xa8, 0xea, 0x50, 0x76, 0xe9, 0x88, 0x72, 0x71, 0xf0, 0x65, 0x43, 0x2e, 0x70, 0x28,
	0xf6, 0x97, 0x83, 0x66, 0x81, 0x02, 0xfb, 0x27, 0x58, 0x94, 0x3b, 0x90, 0x7b, 0x62, 0xe2, 0xde,
	0xcd, 0xef, 0x6f, 0xb6, 0xb2, 0xfc, 0xa9, 0x95, 0x29, 0x8c, 0xb1, 0xe0, 0x4e, 0x16, 0xec, 0xf9,
	0xdf, 0x1a, 0x54, 0x3b, 0xd7, 0x23, 0xe4, 0x31, 0xac, 0x75, 0x7b, 0x83, 0x13, 0xb3, 0x73, 0xd4,
	0xed, 0xbc, 0x36, 0xfb, 0x3f, 0x9f, 0x75, 0xcd, 0x41, 0xef, 0x75, 0xef, 0xf4, 0x5d, 0xaf, 0x36,
	0xa3, 0x52, 0x76, 0x4e, 0x7b, 0xe7, 0x83, 0x93, 0x6e, 0x4d, 0x43, 0x4d, 0xa8, 0x67, 0x95, 0xed,
	0x41, 0xff, 0xa8, 0x36, 0xab, 0x72, 0x33, 0xba, 0xaf, 0x4e, 0x07, 0xbd, 0x83, 0x5a, 0x49, 0x19,
	0xb3, 0x7d, 0xd6, 0xef, 0x1c, 0xb5, 0x6b, 0x73, 0x68, 0x0d, 0x56, 0xb2, 0xca, 0xf3, 0x93, 0xf3,
	0x5a, 0x79, 0xff, 0xaf, 0x05, 0x78, 0x34, 0x55, 0xa2, 0xf3, 0xcb, 0x10, 0xd9, 0xc9, 0xb4, 0x9f,
	0x12, 0xa3, 0x67, 0xf9, 0x62, 0x28, 0x88, 0xa4, 0xfe, 0xcd, 0x6d, 0xcc, 0x58, 0x80, 0x67, 0xd0,
	0x47, 0x58, 0xce, 0xb1, 0x2d, 0xa4, 0x70, 0x57, 0x11, 0x3f, 0x7d, 0xfb, 0x56, 0x76, 0x02, 0x87,
	0x02, 0xca, 0xb3, 0x28, 0xa4, 0x08, 0xa0, 0xa4, 0x78, 0xfa, 0xce, 0xed, 0x0c, 0x05, 0xd4, 0x3b,
	0x58, 0x98, 0x66, 0x49, 0x68, 0xb3, 0xa0, 0x18, 0x93, 0x8e, 0xad, 0xe3, 0x9b, 0x4c, 0x44, 0xe0,
	0x5f, 0xe0, 0x51, 0x9a, 0xef, 0xa0, 0xad, 0xbc, 0x5f, 0x8e, 0x74, 0xe9, 0x5f, 0xdf, 0x6c, 0x24,
	0xc2, 0xdb, 0x50, 0xcb, 0x72, 0x19, 0xd5, 0x79, 0x2b, 0xc8, 0x95, 0xea, 0xbc, 0x95, 0xb4, 0x68,
	0x06, 0xbd, 0x87, 0xc5, 0x14, 0x9f, 0x41, 0x8a, 0xad, 0x67, 0x99, 0x93, 0xbe, 0x75, 0xa3, 0xcd,
	0xf8, 0x2e, 0xe5, 0xf8, 0x08, 0x2a, 0x4c, 0x2d, 0x4d, 0x8d, 0xf4, 0xed, 0x5b, 0xd9, 0xa5, 0x71,
	0xa6, 0x88, 0x47, 0x31, 0x4e, 0x9a, 0x03, 0x15, 0xe3, 0x64, 0x59, 0xcc, 0xb8, 0x56, 0x13, 0x92,
	0x51, 0x50, 0xab, 0x14, 0xd1, 0x29, 0xa8, 0x55, 0x9a, 0xa9, 0xc8, 0xf7, 0x90, 0x9f, 0xea, 0xaa,
	0xf7, 0xa0, 0xa4, 0x18, 0xaa, 0xf7, 0xa0, 0x26, 0x09, 0x12, 0x2a, 0xdf, 0x83, 0x55, 0x50, 0xca,
	0x21, 0xa1, 0x82, 0x52, 0xb7, 0x74, 0x3c, 0x83, 0x7e, 0xd3, 0x40, 0x2f, 0x1e, 0xe5, 0x68, 0xaf,
	0xa8, 0x5f, 0x14, 0x8c, 0x76, 0xfd, 0xfb, 0x7f, 0xe7, 0x20, 0x72, 0x88, 0xa0, 0xa1, 0x1e, 0xfe,
	0xe8, 0xdb, 0x7c, 0xb4, 0x42, 0x56, 0xa1, 0x7f, 0x77, 0x7b, 0xe3, 0xa9, 0x2a, 0x67, 0x26, 0x79,
	0x41, 0x95, 0xf3, 0x14, 0xa3, 0xa0, 0xca, 0x2a, 0x62, 0x30, 0xf3, 0xea, 0xc5, 0xfb, 0xfd, 0xa1,
	0xef, 0x5a, 0xde, 0xb0, 0xf5, 0x72, 0x9f, 0xf3, 0x96, 0xed, 0x8f, 0xf6, 0xc4, 0x9f, 0x06, 0xdb,
	0x77, 0xf7, 0x18, 0x09, 0xc5, 0x14, 0xcc, 0xfd, 0x8c, 0xf8, 0xf0, 0x40, 0xd8, 0xfc, 0xf0, 0x4f,
	0x00, 0x00, 0x00, 0xff, 0xff, 0x17, 0xd6, 0x5a, 0xdc, 0xb4, 0x10, 0x00, 0x00,
}
