// Code generated by protoc-gen-go. DO NOT EDIT.
// source: super-player-privilege/privilege.proto

package privilege // import "golang.52tt.com/protocol/services/privilege"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetUserSpecialConcernReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSpecialConcernReq) Reset()         { *m = GetUserSpecialConcernReq{} }
func (m *GetUserSpecialConcernReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSpecialConcernReq) ProtoMessage()    {}
func (*GetUserSpecialConcernReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_privilege_4c04f706f38dffe8, []int{0}
}
func (m *GetUserSpecialConcernReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSpecialConcernReq.Unmarshal(m, b)
}
func (m *GetUserSpecialConcernReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSpecialConcernReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSpecialConcernReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSpecialConcernReq.Merge(dst, src)
}
func (m *GetUserSpecialConcernReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSpecialConcernReq.Size(m)
}
func (m *GetUserSpecialConcernReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSpecialConcernReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSpecialConcernReq proto.InternalMessageInfo

func (m *GetUserSpecialConcernReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserSpecialConcernResp struct {
	SpecialConcernList   []uint32 `protobuf:"varint,1,rep,packed,name=special_concern_list,json=specialConcernList,proto3" json:"special_concern_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSpecialConcernResp) Reset()         { *m = GetUserSpecialConcernResp{} }
func (m *GetUserSpecialConcernResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSpecialConcernResp) ProtoMessage()    {}
func (*GetUserSpecialConcernResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_privilege_4c04f706f38dffe8, []int{1}
}
func (m *GetUserSpecialConcernResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSpecialConcernResp.Unmarshal(m, b)
}
func (m *GetUserSpecialConcernResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSpecialConcernResp.Marshal(b, m, deterministic)
}
func (dst *GetUserSpecialConcernResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSpecialConcernResp.Merge(dst, src)
}
func (m *GetUserSpecialConcernResp) XXX_Size() int {
	return xxx_messageInfo_GetUserSpecialConcernResp.Size(m)
}
func (m *GetUserSpecialConcernResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSpecialConcernResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSpecialConcernResp proto.InternalMessageInfo

func (m *GetUserSpecialConcernResp) GetSpecialConcernList() []uint32 {
	if m != nil {
		return m.SpecialConcernList
	}
	return nil
}

type GetUserBeSpecialConcernReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBeSpecialConcernReq) Reset()         { *m = GetUserBeSpecialConcernReq{} }
func (m *GetUserBeSpecialConcernReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBeSpecialConcernReq) ProtoMessage()    {}
func (*GetUserBeSpecialConcernReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_privilege_4c04f706f38dffe8, []int{2}
}
func (m *GetUserBeSpecialConcernReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBeSpecialConcernReq.Unmarshal(m, b)
}
func (m *GetUserBeSpecialConcernReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBeSpecialConcernReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBeSpecialConcernReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBeSpecialConcernReq.Merge(dst, src)
}
func (m *GetUserBeSpecialConcernReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBeSpecialConcernReq.Size(m)
}
func (m *GetUserBeSpecialConcernReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBeSpecialConcernReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBeSpecialConcernReq proto.InternalMessageInfo

func (m *GetUserBeSpecialConcernReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserBeSpecialConcernResp struct {
	BeSpecialConcernList []uint32 `protobuf:"varint,1,rep,packed,name=be_special_concern_list,json=beSpecialConcernList,proto3" json:"be_special_concern_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBeSpecialConcernResp) Reset()         { *m = GetUserBeSpecialConcernResp{} }
func (m *GetUserBeSpecialConcernResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBeSpecialConcernResp) ProtoMessage()    {}
func (*GetUserBeSpecialConcernResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_privilege_4c04f706f38dffe8, []int{3}
}
func (m *GetUserBeSpecialConcernResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBeSpecialConcernResp.Unmarshal(m, b)
}
func (m *GetUserBeSpecialConcernResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBeSpecialConcernResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBeSpecialConcernResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBeSpecialConcernResp.Merge(dst, src)
}
func (m *GetUserBeSpecialConcernResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBeSpecialConcernResp.Size(m)
}
func (m *GetUserBeSpecialConcernResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBeSpecialConcernResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBeSpecialConcernResp proto.InternalMessageInfo

func (m *GetUserBeSpecialConcernResp) GetBeSpecialConcernList() []uint32 {
	if m != nil {
		return m.BeSpecialConcernList
	}
	return nil
}

type AddUserSpecialConcernReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserSpecialConcernReq) Reset()         { *m = AddUserSpecialConcernReq{} }
func (m *AddUserSpecialConcernReq) String() string { return proto.CompactTextString(m) }
func (*AddUserSpecialConcernReq) ProtoMessage()    {}
func (*AddUserSpecialConcernReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_privilege_4c04f706f38dffe8, []int{4}
}
func (m *AddUserSpecialConcernReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserSpecialConcernReq.Unmarshal(m, b)
}
func (m *AddUserSpecialConcernReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserSpecialConcernReq.Marshal(b, m, deterministic)
}
func (dst *AddUserSpecialConcernReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserSpecialConcernReq.Merge(dst, src)
}
func (m *AddUserSpecialConcernReq) XXX_Size() int {
	return xxx_messageInfo_AddUserSpecialConcernReq.Size(m)
}
func (m *AddUserSpecialConcernReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserSpecialConcernReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserSpecialConcernReq proto.InternalMessageInfo

func (m *AddUserSpecialConcernReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserSpecialConcernReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type AddUserSpecialConcernResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserSpecialConcernResp) Reset()         { *m = AddUserSpecialConcernResp{} }
func (m *AddUserSpecialConcernResp) String() string { return proto.CompactTextString(m) }
func (*AddUserSpecialConcernResp) ProtoMessage()    {}
func (*AddUserSpecialConcernResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_privilege_4c04f706f38dffe8, []int{5}
}
func (m *AddUserSpecialConcernResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserSpecialConcernResp.Unmarshal(m, b)
}
func (m *AddUserSpecialConcernResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserSpecialConcernResp.Marshal(b, m, deterministic)
}
func (dst *AddUserSpecialConcernResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserSpecialConcernResp.Merge(dst, src)
}
func (m *AddUserSpecialConcernResp) XXX_Size() int {
	return xxx_messageInfo_AddUserSpecialConcernResp.Size(m)
}
func (m *AddUserSpecialConcernResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserSpecialConcernResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserSpecialConcernResp proto.InternalMessageInfo

type DelUserSpecialConcernReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserSpecialConcernReq) Reset()         { *m = DelUserSpecialConcernReq{} }
func (m *DelUserSpecialConcernReq) String() string { return proto.CompactTextString(m) }
func (*DelUserSpecialConcernReq) ProtoMessage()    {}
func (*DelUserSpecialConcernReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_privilege_4c04f706f38dffe8, []int{6}
}
func (m *DelUserSpecialConcernReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserSpecialConcernReq.Unmarshal(m, b)
}
func (m *DelUserSpecialConcernReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserSpecialConcernReq.Marshal(b, m, deterministic)
}
func (dst *DelUserSpecialConcernReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserSpecialConcernReq.Merge(dst, src)
}
func (m *DelUserSpecialConcernReq) XXX_Size() int {
	return xxx_messageInfo_DelUserSpecialConcernReq.Size(m)
}
func (m *DelUserSpecialConcernReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserSpecialConcernReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserSpecialConcernReq proto.InternalMessageInfo

func (m *DelUserSpecialConcernReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelUserSpecialConcernReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type DelUserSpecialConcernResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserSpecialConcernResp) Reset()         { *m = DelUserSpecialConcernResp{} }
func (m *DelUserSpecialConcernResp) String() string { return proto.CompactTextString(m) }
func (*DelUserSpecialConcernResp) ProtoMessage()    {}
func (*DelUserSpecialConcernResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_privilege_4c04f706f38dffe8, []int{7}
}
func (m *DelUserSpecialConcernResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserSpecialConcernResp.Unmarshal(m, b)
}
func (m *DelUserSpecialConcernResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserSpecialConcernResp.Marshal(b, m, deterministic)
}
func (dst *DelUserSpecialConcernResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserSpecialConcernResp.Merge(dst, src)
}
func (m *DelUserSpecialConcernResp) XXX_Size() int {
	return xxx_messageInfo_DelUserSpecialConcernResp.Size(m)
}
func (m *DelUserSpecialConcernResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserSpecialConcernResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserSpecialConcernResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetUserSpecialConcernReq)(nil), "privilege.GetUserSpecialConcernReq")
	proto.RegisterType((*GetUserSpecialConcernResp)(nil), "privilege.GetUserSpecialConcernResp")
	proto.RegisterType((*GetUserBeSpecialConcernReq)(nil), "privilege.GetUserBeSpecialConcernReq")
	proto.RegisterType((*GetUserBeSpecialConcernResp)(nil), "privilege.GetUserBeSpecialConcernResp")
	proto.RegisterType((*AddUserSpecialConcernReq)(nil), "privilege.AddUserSpecialConcernReq")
	proto.RegisterType((*AddUserSpecialConcernResp)(nil), "privilege.AddUserSpecialConcernResp")
	proto.RegisterType((*DelUserSpecialConcernReq)(nil), "privilege.DelUserSpecialConcernReq")
	proto.RegisterType((*DelUserSpecialConcernResp)(nil), "privilege.DelUserSpecialConcernResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PrivilegeClient is the client API for Privilege service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrivilegeClient interface {
	GetUserSpecialConcern(ctx context.Context, in *GetUserSpecialConcernReq, opts ...grpc.CallOption) (*GetUserSpecialConcernResp, error)
	GetUserBeSpecialConcern(ctx context.Context, in *GetUserBeSpecialConcernReq, opts ...grpc.CallOption) (*GetUserBeSpecialConcernResp, error)
	AddUserSpecialConcern(ctx context.Context, in *AddUserSpecialConcernReq, opts ...grpc.CallOption) (*AddUserSpecialConcernResp, error)
	DelUserSpecialConcern(ctx context.Context, in *DelUserSpecialConcernReq, opts ...grpc.CallOption) (*DelUserSpecialConcernResp, error)
}

type privilegeClient struct {
	cc *grpc.ClientConn
}

func NewPrivilegeClient(cc *grpc.ClientConn) PrivilegeClient {
	return &privilegeClient{cc}
}

func (c *privilegeClient) GetUserSpecialConcern(ctx context.Context, in *GetUserSpecialConcernReq, opts ...grpc.CallOption) (*GetUserSpecialConcernResp, error) {
	out := new(GetUserSpecialConcernResp)
	err := c.cc.Invoke(ctx, "/privilege.Privilege/GetUserSpecialConcern", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *privilegeClient) GetUserBeSpecialConcern(ctx context.Context, in *GetUserBeSpecialConcernReq, opts ...grpc.CallOption) (*GetUserBeSpecialConcernResp, error) {
	out := new(GetUserBeSpecialConcernResp)
	err := c.cc.Invoke(ctx, "/privilege.Privilege/GetUserBeSpecialConcern", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *privilegeClient) AddUserSpecialConcern(ctx context.Context, in *AddUserSpecialConcernReq, opts ...grpc.CallOption) (*AddUserSpecialConcernResp, error) {
	out := new(AddUserSpecialConcernResp)
	err := c.cc.Invoke(ctx, "/privilege.Privilege/AddUserSpecialConcern", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *privilegeClient) DelUserSpecialConcern(ctx context.Context, in *DelUserSpecialConcernReq, opts ...grpc.CallOption) (*DelUserSpecialConcernResp, error) {
	out := new(DelUserSpecialConcernResp)
	err := c.cc.Invoke(ctx, "/privilege.Privilege/DelUserSpecialConcern", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PrivilegeServer is the server API for Privilege service.
type PrivilegeServer interface {
	GetUserSpecialConcern(context.Context, *GetUserSpecialConcernReq) (*GetUserSpecialConcernResp, error)
	GetUserBeSpecialConcern(context.Context, *GetUserBeSpecialConcernReq) (*GetUserBeSpecialConcernResp, error)
	AddUserSpecialConcern(context.Context, *AddUserSpecialConcernReq) (*AddUserSpecialConcernResp, error)
	DelUserSpecialConcern(context.Context, *DelUserSpecialConcernReq) (*DelUserSpecialConcernResp, error)
}

func RegisterPrivilegeServer(s *grpc.Server, srv PrivilegeServer) {
	s.RegisterService(&_Privilege_serviceDesc, srv)
}

func _Privilege_GetUserSpecialConcern_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSpecialConcernReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrivilegeServer).GetUserSpecialConcern(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/privilege.Privilege/GetUserSpecialConcern",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrivilegeServer).GetUserSpecialConcern(ctx, req.(*GetUserSpecialConcernReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Privilege_GetUserBeSpecialConcern_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBeSpecialConcernReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrivilegeServer).GetUserBeSpecialConcern(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/privilege.Privilege/GetUserBeSpecialConcern",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrivilegeServer).GetUserBeSpecialConcern(ctx, req.(*GetUserBeSpecialConcernReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Privilege_AddUserSpecialConcern_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserSpecialConcernReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrivilegeServer).AddUserSpecialConcern(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/privilege.Privilege/AddUserSpecialConcern",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrivilegeServer).AddUserSpecialConcern(ctx, req.(*AddUserSpecialConcernReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Privilege_DelUserSpecialConcern_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserSpecialConcernReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrivilegeServer).DelUserSpecialConcern(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/privilege.Privilege/DelUserSpecialConcern",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrivilegeServer).DelUserSpecialConcern(ctx, req.(*DelUserSpecialConcernReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Privilege_serviceDesc = grpc.ServiceDesc{
	ServiceName: "privilege.Privilege",
	HandlerType: (*PrivilegeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserSpecialConcern",
			Handler:    _Privilege_GetUserSpecialConcern_Handler,
		},
		{
			MethodName: "GetUserBeSpecialConcern",
			Handler:    _Privilege_GetUserBeSpecialConcern_Handler,
		},
		{
			MethodName: "AddUserSpecialConcern",
			Handler:    _Privilege_AddUserSpecialConcern_Handler,
		},
		{
			MethodName: "DelUserSpecialConcern",
			Handler:    _Privilege_DelUserSpecialConcern_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "super-player-privilege/privilege.proto",
}

func init() {
	proto.RegisterFile("super-player-privilege/privilege.proto", fileDescriptor_privilege_4c04f706f38dffe8)
}

var fileDescriptor_privilege_4c04f706f38dffe8 = []byte{
	// 343 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x93, 0xd1, 0x4b, 0x3a, 0x41,
	0x10, 0xc7, 0xf5, 0x27, 0xfc, 0xc0, 0x01, 0x21, 0x16, 0xc5, 0x53, 0x09, 0xe4, 0x2a, 0x11, 0xca,
	0x33, 0x0c, 0xff, 0x80, 0x2c, 0xe8, 0xa1, 0x82, 0xb0, 0x7c, 0xe9, 0xe5, 0x38, 0xef, 0x86, 0x63,
	0x63, 0x73, 0xb7, 0x9d, 0x55, 0xe8, 0x4f, 0xea, 0xbf, 0x8c, 0x5b, 0x4b, 0xb9, 0xba, 0x2d, 0x1f,
	0x7a, 0xda, 0x65, 0x66, 0xf6, 0xfb, 0xdd, 0x9d, 0xcf, 0x0e, 0xf4, 0x68, 0xa9, 0x50, 0x0f, 0x94,
	0x88, 0x5e, 0xb3, 0x45, 0xf3, 0x15, 0x17, 0x98, 0xe2, 0x70, 0xb3, 0x0b, 0x94, 0x96, 0x46, 0xb2,
	0xea, 0x26, 0xe0, 0x9f, 0x80, 0x77, 0x85, 0x66, 0x46, 0xa8, 0xef, 0x15, 0xc6, 0x3c, 0x12, 0x17,
	0x72, 0x11, 0xa3, 0x5e, 0x4c, 0xf1, 0x85, 0xed, 0x41, 0x65, 0xc9, 0x13, 0xaf, 0xdc, 0x2d, 0xf7,
	0x6b, 0xd3, 0x6c, 0xeb, 0xdf, 0x42, 0xcb, 0x51, 0x4d, 0x8a, 0x9d, 0x42, 0x9d, 0xd6, 0xd1, 0x30,
	0x5e, 0x87, 0x43, 0xc1, 0xc9, 0x78, 0xe5, 0x6e, 0xa5, 0x5f, 0x9b, 0x32, 0xca, 0x9d, 0xb8, 0xe1,
	0x64, 0xfc, 0x00, 0xda, 0x1f, 0x72, 0x13, 0xdc, 0xc5, 0xfe, 0x01, 0x3a, 0xce, 0x7a, 0x52, 0x6c,
	0x0c, 0xcd, 0x39, 0x86, 0x3f, 0xdc, 0xa1, 0x3e, 0xff, 0x72, 0xcc, 0xde, 0xe2, 0x1a, 0xbc, 0xf3,
	0x24, 0xd9, 0xb1, 0x05, 0x6c, 0x1f, 0xc0, 0x44, 0x3a, 0x45, 0x13, 0x66, 0x89, 0x7f, 0x36, 0x51,
	0x5d, 0x47, 0x66, 0x3c, 0xf1, 0x3b, 0xd0, 0x72, 0x88, 0x91, 0xca, 0x9c, 0x2e, 0x51, 0xfc, 0x9d,
	0x93, 0x43, 0x8c, 0xd4, 0xe8, 0xad, 0x02, 0xd5, 0xbb, 0x4f, 0xc8, 0x2c, 0x81, 0x46, 0x21, 0x36,
	0x76, 0x10, 0x6c, 0xbf, 0x86, 0xeb, 0x1b, 0xb4, 0x0f, 0x7f, 0x2f, 0x22, 0xe5, 0x97, 0xd8, 0x13,
	0x34, 0x1d, 0x74, 0xd8, 0xd1, 0x77, 0x89, 0x02, 0xe2, 0xed, 0xde, 0x2e, 0x65, 0xd6, 0x2b, 0x81,
	0x46, 0x61, 0x9b, 0x73, 0x2f, 0x72, 0x51, 0xcd, 0xbd, 0xc8, 0x4d, 0xcb, 0xba, 0x14, 0xb6, 0x38,
	0xe7, 0xe2, 0x22, 0x9a, 0x73, 0x71, 0x92, 0xf2, 0x4b, 0x93, 0xc1, 0xe3, 0x71, 0x2a, 0x45, 0xb4,
	0x48, 0x83, 0xf1, 0xc8, 0x98, 0x20, 0x96, 0xcf, 0x43, 0x3b, 0xa6, 0xb1, 0x14, 0x43, 0x42, 0xbd,
	0xe2, 0x31, 0xd2, 0x76, 0x84, 0xe7, 0xff, 0x6d, 0xf2, 0xec, 0x3d, 0x00, 0x00, 0xff, 0xff, 0xd6,
	0x8d, 0x47, 0xa5, 0xed, 0x03, 0x00, 0x00,
}
