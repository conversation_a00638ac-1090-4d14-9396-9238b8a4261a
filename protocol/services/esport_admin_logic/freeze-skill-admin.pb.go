// Code generated by protoc-gen-go. DO NOT EDIT.
// source: esport-admin-logic/freeze-skill-admin.proto

package esport_admin_logic // import "golang.52tt.com/protocol/services/esport_admin_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FreezeType int32

const (
	FreezeType_FREEZE_TYPE_UNFREEZE FreezeType = 0
	FreezeType_FREEZE_TYPE_FOREVER  FreezeType = 1
	FreezeType_FREEZE_TYPE_TO_TIME  FreezeType = 2
)

var FreezeType_name = map[int32]string{
	0: "FREEZE_TYPE_UNFREEZE",
	1: "FREEZE_TYPE_FOREVER",
	2: "FREEZE_TYPE_TO_TIME",
}
var FreezeType_value = map[string]int32{
	"FREEZE_TYPE_UNFREEZE": 0,
	"FREEZE_TYPE_FOREVER":  1,
	"FREEZE_TYPE_TO_TIME":  2,
}

func (x FreezeType) String() string {
	return proto.EnumName(FreezeType_name, int32(x))
}
func (FreezeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{0}
}

type QueryType int32

const (
	QueryType_QUERY_TYPE_BY_UID      QueryType = 0
	QueryType_QUERY_TYPE_BY_GUILD_ID QueryType = 1
	QueryType_QUERY_TYPE_BY_ALL      QueryType = 2
)

var QueryType_name = map[int32]string{
	0: "QUERY_TYPE_BY_UID",
	1: "QUERY_TYPE_BY_GUILD_ID",
	2: "QUERY_TYPE_BY_ALL",
}
var QueryType_value = map[string]int32{
	"QUERY_TYPE_BY_UID":      0,
	"QUERY_TYPE_BY_GUILD_ID": 1,
	"QUERY_TYPE_BY_ALL":      2,
}

func (x QueryType) String() string {
	return proto.EnumName(QueryType_name, int32(x))
}
func (QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{1}
}

type CoachDetail struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	EsportErType         uint32   `protobuf:"varint,4,opt,name=esport_er_type,json=esportErType,proto3" json:"esport_er_type,omitempty"`
	GuildId              uint32   `protobuf:"varint,5,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildName            string   `protobuf:"bytes,6,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	ObtainRoleTs         uint32   `protobuf:"varint,7,opt,name=obtain_role_ts,json=obtainRoleTs,proto3" json:"obtain_role_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachDetail) Reset()         { *m = CoachDetail{} }
func (m *CoachDetail) String() string { return proto.CompactTextString(m) }
func (*CoachDetail) ProtoMessage()    {}
func (*CoachDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{0}
}
func (m *CoachDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachDetail.Unmarshal(m, b)
}
func (m *CoachDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachDetail.Marshal(b, m, deterministic)
}
func (dst *CoachDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachDetail.Merge(dst, src)
}
func (m *CoachDetail) XXX_Size() int {
	return xxx_messageInfo_CoachDetail.Size(m)
}
func (m *CoachDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CoachDetail proto.InternalMessageInfo

func (m *CoachDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CoachDetail) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *CoachDetail) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CoachDetail) GetEsportErType() uint32 {
	if m != nil {
		return m.EsportErType
	}
	return 0
}

func (m *CoachDetail) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CoachDetail) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *CoachDetail) GetObtainRoleTs() uint32 {
	if m != nil {
		return m.ObtainRoleTs
	}
	return 0
}

type SkillDetail struct {
	GameId               uint32     `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameName             string     `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	FreezeType           FreezeType `protobuf:"varint,3,opt,name=freeze_type,json=freezeType,proto3,enum=esport_admin_logic.FreezeType" json:"freeze_type,omitempty"`
	FreezeStopTs         int64      `protobuf:"varint,4,opt,name=freeze_stop_ts,json=freezeStopTs,proto3" json:"freeze_stop_ts,omitempty"`
	IsDeleted            bool       `protobuf:"varint,5,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	Rank                 float32    `protobuf:"fixed32,6,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SkillDetail) Reset()         { *m = SkillDetail{} }
func (m *SkillDetail) String() string { return proto.CompactTextString(m) }
func (*SkillDetail) ProtoMessage()    {}
func (*SkillDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{1}
}
func (m *SkillDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkillDetail.Unmarshal(m, b)
}
func (m *SkillDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkillDetail.Marshal(b, m, deterministic)
}
func (dst *SkillDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkillDetail.Merge(dst, src)
}
func (m *SkillDetail) XXX_Size() int {
	return xxx_messageInfo_SkillDetail.Size(m)
}
func (m *SkillDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_SkillDetail.DiscardUnknown(m)
}

var xxx_messageInfo_SkillDetail proto.InternalMessageInfo

func (m *SkillDetail) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SkillDetail) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *SkillDetail) GetFreezeType() FreezeType {
	if m != nil {
		return m.FreezeType
	}
	return FreezeType_FREEZE_TYPE_UNFREEZE
}

func (m *SkillDetail) GetFreezeStopTs() int64 {
	if m != nil {
		return m.FreezeStopTs
	}
	return 0
}

func (m *SkillDetail) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

func (m *SkillDetail) GetRank() float32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type GetCoachSkillInfoRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsFreeze             bool     `protobuf:"varint,2,opt,name=is_freeze,json=isFreeze,proto3" json:"is_freeze,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachSkillInfoRequest) Reset()         { *m = GetCoachSkillInfoRequest{} }
func (m *GetCoachSkillInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachSkillInfoRequest) ProtoMessage()    {}
func (*GetCoachSkillInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{2}
}
func (m *GetCoachSkillInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachSkillInfoRequest.Unmarshal(m, b)
}
func (m *GetCoachSkillInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachSkillInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachSkillInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachSkillInfoRequest.Merge(dst, src)
}
func (m *GetCoachSkillInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachSkillInfoRequest.Size(m)
}
func (m *GetCoachSkillInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachSkillInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachSkillInfoRequest proto.InternalMessageInfo

func (m *GetCoachSkillInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCoachSkillInfoRequest) GetIsFreeze() bool {
	if m != nil {
		return m.IsFreeze
	}
	return false
}

type GetCoachSkillInfoResponse struct {
	SkillList            []*SkillDetail `protobuf:"bytes,1,rep,name=skill_list,json=skillList,proto3" json:"skill_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetCoachSkillInfoResponse) Reset()         { *m = GetCoachSkillInfoResponse{} }
func (m *GetCoachSkillInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachSkillInfoResponse) ProtoMessage()    {}
func (*GetCoachSkillInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{3}
}
func (m *GetCoachSkillInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachSkillInfoResponse.Unmarshal(m, b)
}
func (m *GetCoachSkillInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachSkillInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachSkillInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachSkillInfoResponse.Merge(dst, src)
}
func (m *GetCoachSkillInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachSkillInfoResponse.Size(m)
}
func (m *GetCoachSkillInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachSkillInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachSkillInfoResponse proto.InternalMessageInfo

func (m *GetCoachSkillInfoResponse) GetSkillList() []*SkillDetail {
	if m != nil {
		return m.SkillList
	}
	return nil
}

type FreezeCoachSkill struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameIdList           []uint32 `protobuf:"varint,2,rep,packed,name=game_id_list,json=gameIdList,proto3" json:"game_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeCoachSkill) Reset()         { *m = FreezeCoachSkill{} }
func (m *FreezeCoachSkill) String() string { return proto.CompactTextString(m) }
func (*FreezeCoachSkill) ProtoMessage()    {}
func (*FreezeCoachSkill) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{4}
}
func (m *FreezeCoachSkill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeCoachSkill.Unmarshal(m, b)
}
func (m *FreezeCoachSkill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeCoachSkill.Marshal(b, m, deterministic)
}
func (dst *FreezeCoachSkill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeCoachSkill.Merge(dst, src)
}
func (m *FreezeCoachSkill) XXX_Size() int {
	return xxx_messageInfo_FreezeCoachSkill.Size(m)
}
func (m *FreezeCoachSkill) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeCoachSkill.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeCoachSkill proto.InternalMessageInfo

func (m *FreezeCoachSkill) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FreezeCoachSkill) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

type BatFreezeCoachSkillRequest struct {
	FreezeList           []*FreezeCoachSkill `protobuf:"bytes,1,rep,name=freeze_list,json=freezeList,proto3" json:"freeze_list,omitempty"`
	FreezeType           FreezeType          `protobuf:"varint,2,opt,name=freeze_type,json=freezeType,proto3,enum=esport_admin_logic.FreezeType" json:"freeze_type,omitempty"`
	FreezeStopTs         int64               `protobuf:"varint,3,opt,name=freeze_stop_ts,json=freezeStopTs,proto3" json:"freeze_stop_ts,omitempty"`
	FreezeReason         string              `protobuf:"bytes,4,opt,name=freeze_reason,json=freezeReason,proto3" json:"freeze_reason,omitempty"`
	OpUser               string              `protobuf:"bytes,5,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatFreezeCoachSkillRequest) Reset()         { *m = BatFreezeCoachSkillRequest{} }
func (m *BatFreezeCoachSkillRequest) String() string { return proto.CompactTextString(m) }
func (*BatFreezeCoachSkillRequest) ProtoMessage()    {}
func (*BatFreezeCoachSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{5}
}
func (m *BatFreezeCoachSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatFreezeCoachSkillRequest.Unmarshal(m, b)
}
func (m *BatFreezeCoachSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatFreezeCoachSkillRequest.Marshal(b, m, deterministic)
}
func (dst *BatFreezeCoachSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatFreezeCoachSkillRequest.Merge(dst, src)
}
func (m *BatFreezeCoachSkillRequest) XXX_Size() int {
	return xxx_messageInfo_BatFreezeCoachSkillRequest.Size(m)
}
func (m *BatFreezeCoachSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatFreezeCoachSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatFreezeCoachSkillRequest proto.InternalMessageInfo

func (m *BatFreezeCoachSkillRequest) GetFreezeList() []*FreezeCoachSkill {
	if m != nil {
		return m.FreezeList
	}
	return nil
}

func (m *BatFreezeCoachSkillRequest) GetFreezeType() FreezeType {
	if m != nil {
		return m.FreezeType
	}
	return FreezeType_FREEZE_TYPE_UNFREEZE
}

func (m *BatFreezeCoachSkillRequest) GetFreezeStopTs() int64 {
	if m != nil {
		return m.FreezeStopTs
	}
	return 0
}

func (m *BatFreezeCoachSkillRequest) GetFreezeReason() string {
	if m != nil {
		return m.FreezeReason
	}
	return ""
}

func (m *BatFreezeCoachSkillRequest) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

type BatFreezeCoachSkillResponse struct {
	// freeze_list[0] 请求成功，则 err_list[0] = ''
	// freeze_list[1] 请求失败，则 err_list[1] = '报错信息'
	ErrList              []string `protobuf:"bytes,1,rep,name=err_list,json=errList,proto3" json:"err_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatFreezeCoachSkillResponse) Reset()         { *m = BatFreezeCoachSkillResponse{} }
func (m *BatFreezeCoachSkillResponse) String() string { return proto.CompactTextString(m) }
func (*BatFreezeCoachSkillResponse) ProtoMessage()    {}
func (*BatFreezeCoachSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{6}
}
func (m *BatFreezeCoachSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatFreezeCoachSkillResponse.Unmarshal(m, b)
}
func (m *BatFreezeCoachSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatFreezeCoachSkillResponse.Marshal(b, m, deterministic)
}
func (dst *BatFreezeCoachSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatFreezeCoachSkillResponse.Merge(dst, src)
}
func (m *BatFreezeCoachSkillResponse) XXX_Size() int {
	return xxx_messageInfo_BatFreezeCoachSkillResponse.Size(m)
}
func (m *BatFreezeCoachSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatFreezeCoachSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatFreezeCoachSkillResponse proto.InternalMessageInfo

func (m *BatFreezeCoachSkillResponse) GetErrList() []string {
	if m != nil {
		return m.ErrList
	}
	return nil
}

type UnfreezeCoachSkillRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameIdList           []uint32 `protobuf:"varint,2,rep,packed,name=game_id_list,json=gameIdList,proto3" json:"game_id_list,omitempty"`
	OpUser               string   `protobuf:"bytes,3,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeCoachSkillRequest) Reset()         { *m = UnfreezeCoachSkillRequest{} }
func (m *UnfreezeCoachSkillRequest) String() string { return proto.CompactTextString(m) }
func (*UnfreezeCoachSkillRequest) ProtoMessage()    {}
func (*UnfreezeCoachSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{7}
}
func (m *UnfreezeCoachSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeCoachSkillRequest.Unmarshal(m, b)
}
func (m *UnfreezeCoachSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeCoachSkillRequest.Marshal(b, m, deterministic)
}
func (dst *UnfreezeCoachSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeCoachSkillRequest.Merge(dst, src)
}
func (m *UnfreezeCoachSkillRequest) XXX_Size() int {
	return xxx_messageInfo_UnfreezeCoachSkillRequest.Size(m)
}
func (m *UnfreezeCoachSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeCoachSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeCoachSkillRequest proto.InternalMessageInfo

func (m *UnfreezeCoachSkillRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnfreezeCoachSkillRequest) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

func (m *UnfreezeCoachSkillRequest) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

type UnfreezeCoachSkillResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnfreezeCoachSkillResponse) Reset()         { *m = UnfreezeCoachSkillResponse{} }
func (m *UnfreezeCoachSkillResponse) String() string { return proto.CompactTextString(m) }
func (*UnfreezeCoachSkillResponse) ProtoMessage()    {}
func (*UnfreezeCoachSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{8}
}
func (m *UnfreezeCoachSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnfreezeCoachSkillResponse.Unmarshal(m, b)
}
func (m *UnfreezeCoachSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnfreezeCoachSkillResponse.Marshal(b, m, deterministic)
}
func (dst *UnfreezeCoachSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnfreezeCoachSkillResponse.Merge(dst, src)
}
func (m *UnfreezeCoachSkillResponse) XXX_Size() int {
	return xxx_messageInfo_UnfreezeCoachSkillResponse.Size(m)
}
func (m *UnfreezeCoachSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UnfreezeCoachSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UnfreezeCoachSkillResponse proto.InternalMessageInfo

type GetCoachSkillInfoListRequest struct {
	Page                 int32    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GuildId              uint32   `protobuf:"varint,4,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	QueryType            uint32   `protobuf:"varint,5,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachSkillInfoListRequest) Reset()         { *m = GetCoachSkillInfoListRequest{} }
func (m *GetCoachSkillInfoListRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachSkillInfoListRequest) ProtoMessage()    {}
func (*GetCoachSkillInfoListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{9}
}
func (m *GetCoachSkillInfoListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachSkillInfoListRequest.Unmarshal(m, b)
}
func (m *GetCoachSkillInfoListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachSkillInfoListRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachSkillInfoListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachSkillInfoListRequest.Merge(dst, src)
}
func (m *GetCoachSkillInfoListRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachSkillInfoListRequest.Size(m)
}
func (m *GetCoachSkillInfoListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachSkillInfoListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachSkillInfoListRequest proto.InternalMessageInfo

func (m *GetCoachSkillInfoListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetCoachSkillInfoListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetCoachSkillInfoListRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetCoachSkillInfoListRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCoachSkillInfoListRequest) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

type GetCoachSkillInfoListResponse struct {
	CoachSkillInfoList   []*CoachSkillInfo `protobuf:"bytes,1,rep,name=coach_skill_info_list,json=coachSkillInfoList,proto3" json:"coach_skill_info_list,omitempty"`
	Total                int32             `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetCoachSkillInfoListResponse) Reset()         { *m = GetCoachSkillInfoListResponse{} }
func (m *GetCoachSkillInfoListResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachSkillInfoListResponse) ProtoMessage()    {}
func (*GetCoachSkillInfoListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{10}
}
func (m *GetCoachSkillInfoListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachSkillInfoListResponse.Unmarshal(m, b)
}
func (m *GetCoachSkillInfoListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachSkillInfoListResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachSkillInfoListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachSkillInfoListResponse.Merge(dst, src)
}
func (m *GetCoachSkillInfoListResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachSkillInfoListResponse.Size(m)
}
func (m *GetCoachSkillInfoListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachSkillInfoListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachSkillInfoListResponse proto.InternalMessageInfo

func (m *GetCoachSkillInfoListResponse) GetCoachSkillInfoList() []*CoachSkillInfo {
	if m != nil {
		return m.CoachSkillInfoList
	}
	return nil
}

func (m *GetCoachSkillInfoListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CoachSkillInfo struct {
	CoachDetail          *CoachDetail   `protobuf:"bytes,1,opt,name=coach_detail,json=coachDetail,proto3" json:"coach_detail,omitempty"`
	SkillList            []*SkillDetail `protobuf:"bytes,2,rep,name=skill_list,json=skillList,proto3" json:"skill_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CoachSkillInfo) Reset()         { *m = CoachSkillInfo{} }
func (m *CoachSkillInfo) String() string { return proto.CompactTextString(m) }
func (*CoachSkillInfo) ProtoMessage()    {}
func (*CoachSkillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{11}
}
func (m *CoachSkillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachSkillInfo.Unmarshal(m, b)
}
func (m *CoachSkillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachSkillInfo.Marshal(b, m, deterministic)
}
func (dst *CoachSkillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachSkillInfo.Merge(dst, src)
}
func (m *CoachSkillInfo) XXX_Size() int {
	return xxx_messageInfo_CoachSkillInfo.Size(m)
}
func (m *CoachSkillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachSkillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CoachSkillInfo proto.InternalMessageInfo

func (m *CoachSkillInfo) GetCoachDetail() *CoachDetail {
	if m != nil {
		return m.CoachDetail
	}
	return nil
}

func (m *CoachSkillInfo) GetSkillList() []*SkillDetail {
	if m != nil {
		return m.SkillList
	}
	return nil
}

type GetSkillFreezeOperationListRequest struct {
	Page                 int32    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GuildId              uint32   `protobuf:"varint,4,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GameId               uint32   `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	QueryType            uint32   `protobuf:"varint,6,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSkillFreezeOperationListRequest) Reset()         { *m = GetSkillFreezeOperationListRequest{} }
func (m *GetSkillFreezeOperationListRequest) String() string { return proto.CompactTextString(m) }
func (*GetSkillFreezeOperationListRequest) ProtoMessage()    {}
func (*GetSkillFreezeOperationListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{12}
}
func (m *GetSkillFreezeOperationListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSkillFreezeOperationListRequest.Unmarshal(m, b)
}
func (m *GetSkillFreezeOperationListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSkillFreezeOperationListRequest.Marshal(b, m, deterministic)
}
func (dst *GetSkillFreezeOperationListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSkillFreezeOperationListRequest.Merge(dst, src)
}
func (m *GetSkillFreezeOperationListRequest) XXX_Size() int {
	return xxx_messageInfo_GetSkillFreezeOperationListRequest.Size(m)
}
func (m *GetSkillFreezeOperationListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSkillFreezeOperationListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSkillFreezeOperationListRequest proto.InternalMessageInfo

func (m *GetSkillFreezeOperationListRequest) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetSkillFreezeOperationListRequest) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetSkillFreezeOperationListRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetSkillFreezeOperationListRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetSkillFreezeOperationListRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetSkillFreezeOperationListRequest) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

type GetSkillFreezeOperationListResponse struct {
	OperationList        []*SkillFreezeOperation `protobuf:"bytes,1,rep,name=operation_list,json=operationList,proto3" json:"operation_list,omitempty"`
	Total                int32                   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetSkillFreezeOperationListResponse) Reset()         { *m = GetSkillFreezeOperationListResponse{} }
func (m *GetSkillFreezeOperationListResponse) String() string { return proto.CompactTextString(m) }
func (*GetSkillFreezeOperationListResponse) ProtoMessage()    {}
func (*GetSkillFreezeOperationListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{13}
}
func (m *GetSkillFreezeOperationListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSkillFreezeOperationListResponse.Unmarshal(m, b)
}
func (m *GetSkillFreezeOperationListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSkillFreezeOperationListResponse.Marshal(b, m, deterministic)
}
func (dst *GetSkillFreezeOperationListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSkillFreezeOperationListResponse.Merge(dst, src)
}
func (m *GetSkillFreezeOperationListResponse) XXX_Size() int {
	return xxx_messageInfo_GetSkillFreezeOperationListResponse.Size(m)
}
func (m *GetSkillFreezeOperationListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSkillFreezeOperationListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSkillFreezeOperationListResponse proto.InternalMessageInfo

func (m *GetSkillFreezeOperationListResponse) GetOperationList() []*SkillFreezeOperation {
	if m != nil {
		return m.OperationList
	}
	return nil
}

func (m *GetSkillFreezeOperationListResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type SkillFreezeOperation struct {
	CoachDetail          *CoachDetail   `protobuf:"bytes,1,opt,name=coach_detail,json=coachDetail,proto3" json:"coach_detail,omitempty"`
	SkillList            []*SkillDetail `protobuf:"bytes,2,rep,name=skill_list,json=skillList,proto3" json:"skill_list,omitempty"`
	FreezeType           FreezeType     `protobuf:"varint,3,opt,name=freeze_type,json=freezeType,proto3,enum=esport_admin_logic.FreezeType" json:"freeze_type,omitempty"`
	FreezeStopTs         int64          `protobuf:"varint,4,opt,name=freeze_stop_ts,json=freezeStopTs,proto3" json:"freeze_stop_ts,omitempty"`
	FreezeReason         string         `protobuf:"bytes,5,opt,name=freeze_reason,json=freezeReason,proto3" json:"freeze_reason,omitempty"`
	OpUser               string         `protobuf:"bytes,6,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	OpTs                 int64          `protobuf:"varint,7,opt,name=op_ts,json=opTs,proto3" json:"op_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SkillFreezeOperation) Reset()         { *m = SkillFreezeOperation{} }
func (m *SkillFreezeOperation) String() string { return proto.CompactTextString(m) }
func (*SkillFreezeOperation) ProtoMessage()    {}
func (*SkillFreezeOperation) Descriptor() ([]byte, []int) {
	return fileDescriptor_freeze_skill_admin_185f50704297e4f1, []int{14}
}
func (m *SkillFreezeOperation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkillFreezeOperation.Unmarshal(m, b)
}
func (m *SkillFreezeOperation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkillFreezeOperation.Marshal(b, m, deterministic)
}
func (dst *SkillFreezeOperation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkillFreezeOperation.Merge(dst, src)
}
func (m *SkillFreezeOperation) XXX_Size() int {
	return xxx_messageInfo_SkillFreezeOperation.Size(m)
}
func (m *SkillFreezeOperation) XXX_DiscardUnknown() {
	xxx_messageInfo_SkillFreezeOperation.DiscardUnknown(m)
}

var xxx_messageInfo_SkillFreezeOperation proto.InternalMessageInfo

func (m *SkillFreezeOperation) GetCoachDetail() *CoachDetail {
	if m != nil {
		return m.CoachDetail
	}
	return nil
}

func (m *SkillFreezeOperation) GetSkillList() []*SkillDetail {
	if m != nil {
		return m.SkillList
	}
	return nil
}

func (m *SkillFreezeOperation) GetFreezeType() FreezeType {
	if m != nil {
		return m.FreezeType
	}
	return FreezeType_FREEZE_TYPE_UNFREEZE
}

func (m *SkillFreezeOperation) GetFreezeStopTs() int64 {
	if m != nil {
		return m.FreezeStopTs
	}
	return 0
}

func (m *SkillFreezeOperation) GetFreezeReason() string {
	if m != nil {
		return m.FreezeReason
	}
	return ""
}

func (m *SkillFreezeOperation) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *SkillFreezeOperation) GetOpTs() int64 {
	if m != nil {
		return m.OpTs
	}
	return 0
}

func init() {
	proto.RegisterType((*CoachDetail)(nil), "esport_admin_logic.CoachDetail")
	proto.RegisterType((*SkillDetail)(nil), "esport_admin_logic.SkillDetail")
	proto.RegisterType((*GetCoachSkillInfoRequest)(nil), "esport_admin_logic.GetCoachSkillInfoRequest")
	proto.RegisterType((*GetCoachSkillInfoResponse)(nil), "esport_admin_logic.GetCoachSkillInfoResponse")
	proto.RegisterType((*FreezeCoachSkill)(nil), "esport_admin_logic.FreezeCoachSkill")
	proto.RegisterType((*BatFreezeCoachSkillRequest)(nil), "esport_admin_logic.BatFreezeCoachSkillRequest")
	proto.RegisterType((*BatFreezeCoachSkillResponse)(nil), "esport_admin_logic.BatFreezeCoachSkillResponse")
	proto.RegisterType((*UnfreezeCoachSkillRequest)(nil), "esport_admin_logic.UnfreezeCoachSkillRequest")
	proto.RegisterType((*UnfreezeCoachSkillResponse)(nil), "esport_admin_logic.UnfreezeCoachSkillResponse")
	proto.RegisterType((*GetCoachSkillInfoListRequest)(nil), "esport_admin_logic.GetCoachSkillInfoListRequest")
	proto.RegisterType((*GetCoachSkillInfoListResponse)(nil), "esport_admin_logic.GetCoachSkillInfoListResponse")
	proto.RegisterType((*CoachSkillInfo)(nil), "esport_admin_logic.CoachSkillInfo")
	proto.RegisterType((*GetSkillFreezeOperationListRequest)(nil), "esport_admin_logic.GetSkillFreezeOperationListRequest")
	proto.RegisterType((*GetSkillFreezeOperationListResponse)(nil), "esport_admin_logic.GetSkillFreezeOperationListResponse")
	proto.RegisterType((*SkillFreezeOperation)(nil), "esport_admin_logic.SkillFreezeOperation")
	proto.RegisterEnum("esport_admin_logic.FreezeType", FreezeType_name, FreezeType_value)
	proto.RegisterEnum("esport_admin_logic.QueryType", QueryType_name, QueryType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// FreezeSkillAdminClient is the client API for FreezeSkillAdmin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FreezeSkillAdminClient interface {
	// 获取大神技能信息
	GetCoachSkillInfo(ctx context.Context, in *GetCoachSkillInfoRequest, opts ...grpc.CallOption) (*GetCoachSkillInfoResponse, error)
	// 批量冻结大神技能
	BatFreezeCoachSkill(ctx context.Context, in *BatFreezeCoachSkillRequest, opts ...grpc.CallOption) (*BatFreezeCoachSkillResponse, error)
	// 解冻大神技能
	UnfreezeCoachSkill(ctx context.Context, in *UnfreezeCoachSkillRequest, opts ...grpc.CallOption) (*UnfreezeCoachSkillResponse, error)
	// 获取大神技能信息列表
	GetCoachSkillInfoList(ctx context.Context, in *GetCoachSkillInfoListRequest, opts ...grpc.CallOption) (*GetCoachSkillInfoListResponse, error)
	// 获取技能冻结操作列表
	GetSkillFreezeOperationList(ctx context.Context, in *GetSkillFreezeOperationListRequest, opts ...grpc.CallOption) (*GetSkillFreezeOperationListResponse, error)
}

type freezeSkillAdminClient struct {
	cc *grpc.ClientConn
}

func NewFreezeSkillAdminClient(cc *grpc.ClientConn) FreezeSkillAdminClient {
	return &freezeSkillAdminClient{cc}
}

func (c *freezeSkillAdminClient) GetCoachSkillInfo(ctx context.Context, in *GetCoachSkillInfoRequest, opts ...grpc.CallOption) (*GetCoachSkillInfoResponse, error) {
	out := new(GetCoachSkillInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.FreezeSkillAdmin/GetCoachSkillInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *freezeSkillAdminClient) BatFreezeCoachSkill(ctx context.Context, in *BatFreezeCoachSkillRequest, opts ...grpc.CallOption) (*BatFreezeCoachSkillResponse, error) {
	out := new(BatFreezeCoachSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.FreezeSkillAdmin/BatFreezeCoachSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *freezeSkillAdminClient) UnfreezeCoachSkill(ctx context.Context, in *UnfreezeCoachSkillRequest, opts ...grpc.CallOption) (*UnfreezeCoachSkillResponse, error) {
	out := new(UnfreezeCoachSkillResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.FreezeSkillAdmin/UnfreezeCoachSkill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *freezeSkillAdminClient) GetCoachSkillInfoList(ctx context.Context, in *GetCoachSkillInfoListRequest, opts ...grpc.CallOption) (*GetCoachSkillInfoListResponse, error) {
	out := new(GetCoachSkillInfoListResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.FreezeSkillAdmin/GetCoachSkillInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *freezeSkillAdminClient) GetSkillFreezeOperationList(ctx context.Context, in *GetSkillFreezeOperationListRequest, opts ...grpc.CallOption) (*GetSkillFreezeOperationListResponse, error) {
	out := new(GetSkillFreezeOperationListResponse)
	err := c.cc.Invoke(ctx, "/esport_admin_logic.FreezeSkillAdmin/GetSkillFreezeOperationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FreezeSkillAdminServer is the server API for FreezeSkillAdmin service.
type FreezeSkillAdminServer interface {
	// 获取大神技能信息
	GetCoachSkillInfo(context.Context, *GetCoachSkillInfoRequest) (*GetCoachSkillInfoResponse, error)
	// 批量冻结大神技能
	BatFreezeCoachSkill(context.Context, *BatFreezeCoachSkillRequest) (*BatFreezeCoachSkillResponse, error)
	// 解冻大神技能
	UnfreezeCoachSkill(context.Context, *UnfreezeCoachSkillRequest) (*UnfreezeCoachSkillResponse, error)
	// 获取大神技能信息列表
	GetCoachSkillInfoList(context.Context, *GetCoachSkillInfoListRequest) (*GetCoachSkillInfoListResponse, error)
	// 获取技能冻结操作列表
	GetSkillFreezeOperationList(context.Context, *GetSkillFreezeOperationListRequest) (*GetSkillFreezeOperationListResponse, error)
}

func RegisterFreezeSkillAdminServer(s *grpc.Server, srv FreezeSkillAdminServer) {
	s.RegisterService(&_FreezeSkillAdmin_serviceDesc, srv)
}

func _FreezeSkillAdmin_GetCoachSkillInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachSkillInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FreezeSkillAdminServer).GetCoachSkillInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.FreezeSkillAdmin/GetCoachSkillInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FreezeSkillAdminServer).GetCoachSkillInfo(ctx, req.(*GetCoachSkillInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FreezeSkillAdmin_BatFreezeCoachSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatFreezeCoachSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FreezeSkillAdminServer).BatFreezeCoachSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.FreezeSkillAdmin/BatFreezeCoachSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FreezeSkillAdminServer).BatFreezeCoachSkill(ctx, req.(*BatFreezeCoachSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FreezeSkillAdmin_UnfreezeCoachSkill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfreezeCoachSkillRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FreezeSkillAdminServer).UnfreezeCoachSkill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.FreezeSkillAdmin/UnfreezeCoachSkill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FreezeSkillAdminServer).UnfreezeCoachSkill(ctx, req.(*UnfreezeCoachSkillRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FreezeSkillAdmin_GetCoachSkillInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachSkillInfoListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FreezeSkillAdminServer).GetCoachSkillInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.FreezeSkillAdmin/GetCoachSkillInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FreezeSkillAdminServer).GetCoachSkillInfoList(ctx, req.(*GetCoachSkillInfoListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FreezeSkillAdmin_GetSkillFreezeOperationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSkillFreezeOperationListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FreezeSkillAdminServer).GetSkillFreezeOperationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_admin_logic.FreezeSkillAdmin/GetSkillFreezeOperationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FreezeSkillAdminServer).GetSkillFreezeOperationList(ctx, req.(*GetSkillFreezeOperationListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _FreezeSkillAdmin_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_admin_logic.FreezeSkillAdmin",
	HandlerType: (*FreezeSkillAdminServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCoachSkillInfo",
			Handler:    _FreezeSkillAdmin_GetCoachSkillInfo_Handler,
		},
		{
			MethodName: "BatFreezeCoachSkill",
			Handler:    _FreezeSkillAdmin_BatFreezeCoachSkill_Handler,
		},
		{
			MethodName: "UnfreezeCoachSkill",
			Handler:    _FreezeSkillAdmin_UnfreezeCoachSkill_Handler,
		},
		{
			MethodName: "GetCoachSkillInfoList",
			Handler:    _FreezeSkillAdmin_GetCoachSkillInfoList_Handler,
		},
		{
			MethodName: "GetSkillFreezeOperationList",
			Handler:    _FreezeSkillAdmin_GetSkillFreezeOperationList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "esport-admin-logic/freeze-skill-admin.proto",
}

func init() {
	proto.RegisterFile("esport-admin-logic/freeze-skill-admin.proto", fileDescriptor_freeze_skill_admin_185f50704297e4f1)
}

var fileDescriptor_freeze_skill_admin_185f50704297e4f1 = []byte{
	// 1031 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0xcd, 0x72, 0x1b, 0x45,
	0x10, 0xce, 0x4a, 0xd6, 0xcf, 0xb6, 0x7f, 0x4a, 0x99, 0xd8, 0x44, 0x96, 0x13, 0x70, 0x6d, 0x72,
	0x50, 0x05, 0x2c, 0x81, 0x81, 0xc0, 0x09, 0x2a, 0x46, 0x6b, 0x97, 0xaa, 0x4c, 0x4c, 0xc6, 0x12,
	0x85, 0xc3, 0x61, 0x6b, 0x23, 0x8d, 0x94, 0x29, 0xaf, 0x77, 0x36, 0x3b, 0xa3, 0x54, 0x39, 0xc5,
	0x81, 0x23, 0x07, 0x8a, 0x13, 0x8f, 0xc0, 0x9b, 0xf0, 0x0a, 0x70, 0xe4, 0x5d, 0xa8, 0xe9, 0x59,
	0xd9, 0x2b, 0x69, 0xd7, 0xd8, 0x14, 0x05, 0x9c, 0xbc, 0xd3, 0xd3, 0xf3, 0x75, 0xf7, 0xf7, 0xf5,
	0xf4, 0x58, 0xf0, 0x2e, 0x93, 0x91, 0x88, 0xd5, 0x8e, 0x3f, 0x3c, 0xe3, 0xe1, 0x4e, 0x20, 0xc6,
	0x7c, 0xd0, 0x1e, 0xc5, 0x8c, 0xbd, 0x61, 0x3b, 0xf2, 0x94, 0x07, 0x81, 0xd9, 0x68, 0x45, 0xb1,
	0x50, 0x82, 0x10, 0xe3, 0xec, 0xa1, 0xcd, 0x43, 0x67, 0xe7, 0x77, 0x0b, 0x96, 0xbf, 0x10, 0xfe,
	0xe0, 0x65, 0x87, 0x29, 0x9f, 0x07, 0xa4, 0x06, 0xc5, 0x09, 0x1f, 0xd6, 0xad, 0x6d, 0xab, 0xb9,
	0x4a, 0xf5, 0x27, 0x21, 0xb0, 0xa4, 0x14, 0x1f, 0xd6, 0x0b, 0xdb, 0x56, 0xd3, 0xa6, 0xf8, 0x4d,
	0x1a, 0x50, 0x0d, 0xf9, 0xe0, 0x34, 0xf4, 0xcf, 0x58, 0xbd, 0x88, 0xf6, 0x8b, 0x35, 0x79, 0x08,
	0x6b, 0x49, 0x1c, 0x16, 0x7b, 0xea, 0x3c, 0x62, 0xf5, 0x25, 0x04, 0x5b, 0x31, 0x56, 0x37, 0xee,
	0x9d, 0x47, 0x8c, 0x6c, 0x42, 0x75, 0x3c, 0xe1, 0xc1, 0xd0, 0xe3, 0xc3, 0x7a, 0x09, 0xf7, 0x2b,
	0xb8, 0xee, 0x0e, 0xc9, 0x7d, 0x00, 0xb3, 0x85, 0xf0, 0x65, 0x84, 0xb7, 0xd1, 0xf2, 0x34, 0xc1,
	0x17, 0x2f, 0x94, 0xcf, 0x43, 0x2f, 0x16, 0x01, 0xf3, 0x94, 0xac, 0x57, 0x0c, 0xbe, 0xb1, 0x52,
	0x11, 0xb0, 0x9e, 0x74, 0xfe, 0xb0, 0x60, 0xf9, 0x58, 0x33, 0x90, 0xd4, 0x75, 0x17, 0x2a, 0x63,
	0xff, 0x8c, 0x79, 0x17, 0xb5, 0x95, 0xf5, 0xb2, 0x3b, 0x24, 0x5b, 0x60, 0xe3, 0x06, 0x06, 0x33,
	0x35, 0x56, 0xb5, 0x01, 0x63, 0x7d, 0x0e, 0xcb, 0x86, 0x4d, 0x53, 0x88, 0x2e, 0x75, 0x6d, 0xf7,
	0xed, 0xd6, 0x22, 0x8f, 0xad, 0x7d, 0x74, 0xd3, 0xa5, 0x51, 0x18, 0x5d, 0x7c, 0xeb, 0x64, 0x13,
	0x00, 0xa9, 0x44, 0xa4, 0x93, 0xd5, 0x64, 0x14, 0xe9, 0x8a, 0xb1, 0x1e, 0x2b, 0x11, 0xf5, 0xa4,
	0xae, 0x98, 0x4b, 0x6f, 0xc8, 0x02, 0xa6, 0x98, 0xa1, 0xa3, 0x4a, 0x6d, 0x2e, 0x3b, 0xc6, 0xa0,
	0x15, 0x88, 0xfd, 0xf0, 0x14, 0xa9, 0x28, 0x50, 0xfc, 0x76, 0xba, 0x50, 0x3f, 0x60, 0x0a, 0x95,
	0xc3, 0x32, 0xbb, 0xe1, 0x48, 0x50, 0xf6, 0x6a, 0xc2, 0xa4, 0xca, 0xd0, 0x70, 0x0b, 0x6c, 0x2e,
	0x3d, 0x13, 0x13, 0x8b, 0xac, 0xd2, 0x2a, 0x97, 0x26, 0x67, 0xe7, 0x5b, 0xd8, 0xcc, 0x80, 0x92,
	0x91, 0x08, 0x25, 0x23, 0x9f, 0x01, 0x60, 0x23, 0x79, 0x01, 0x97, 0xaa, 0x6e, 0x6d, 0x17, 0x9b,
	0xcb, 0xbb, 0xef, 0x64, 0x11, 0x90, 0x22, 0x9b, 0xda, 0x78, 0xe4, 0x90, 0x4b, 0xe5, 0xec, 0x43,
	0xcd, 0x84, 0xb9, 0xc4, 0xcf, 0xc8, 0x6f, 0x1b, 0x56, 0x12, 0x75, 0x4c, 0x9c, 0xc2, 0x76, 0xb1,
	0xb9, 0x4a, 0xc1, 0x48, 0x84, 0x38, 0x3f, 0x14, 0xa0, 0xb1, 0xe7, 0xab, 0x79, 0xac, 0x69, 0xc9,
	0xee, 0x85, 0x50, 0xa9, 0x3c, 0x1f, 0xe6, 0x0b, 0x95, 0x42, 0x48, 0xe4, 0xd2, 0x51, 0xe6, 0xf5,
	0x2e, 0xfc, 0x03, 0x7a, 0x17, 0x33, 0xf4, 0x7e, 0x00, 0xab, 0x89, 0x57, 0xcc, 0x7c, 0x29, 0x42,
	0x6c, 0x0a, 0x7b, 0xea, 0x44, 0xd1, 0xa6, 0x3b, 0x56, 0x44, 0xde, 0x44, 0xb2, 0x18, 0x3b, 0xc2,
	0xa6, 0x65, 0x11, 0xf5, 0x25, 0x8b, 0x9d, 0x4f, 0x61, 0x2b, 0x93, 0x89, 0x44, 0xb1, 0x4d, 0xa8,
	0xb2, 0x38, 0xbe, 0xe4, 0xc1, 0xa6, 0x15, 0x16, 0xc7, 0x48, 0xe2, 0x4b, 0xd8, 0xec, 0x87, 0xa3,
	0x1c, 0x0a, 0xff, 0x86, 0x2a, 0xe9, 0x1c, 0x8b, 0x33, 0x39, 0xde, 0x83, 0x46, 0x56, 0x24, 0x93,
	0xa2, 0xf3, 0x8b, 0x05, 0xf7, 0x16, 0x5a, 0x4e, 0x03, 0x4e, 0x73, 0x21, 0xb0, 0x14, 0xf9, 0x63,
	0x86, 0xc9, 0x94, 0x28, 0x7e, 0xeb, 0x1e, 0xd6, 0x7f, 0x3d, 0xc9, 0x93, 0x1e, 0x2e, 0xd1, 0xaa,
	0x36, 0x1c, 0xf3, 0x37, 0x58, 0xf4, 0x64, 0x9a, 0x66, 0x11, 0xd3, 0xac, 0x4c, 0xb8, 0xc9, 0x31,
	0x3d, 0x69, 0x96, 0x16, 0x26, 0xcd, 0xab, 0x09, 0x8b, 0xcf, 0x8d, 0xda, 0x66, 0x0c, 0xd9, 0x68,
	0xd1, 0x62, 0x3a, 0x3f, 0x5a, 0x70, 0x3f, 0x27, 0xcd, 0x84, 0xeb, 0x3e, 0x6c, 0x0c, 0xf4, 0xae,
	0x67, 0xee, 0x08, 0x0f, 0x47, 0x22, 0xdd, 0x80, 0x4e, 0x56, 0xe7, 0xcc, 0x5d, 0x34, 0x32, 0x58,
	0x80, 0x27, 0xeb, 0x50, 0x52, 0x42, 0xf9, 0x41, 0x52, 0xa6, 0x59, 0x38, 0x3f, 0x5b, 0xb0, 0x36,
	0x7b, 0x98, 0xec, 0xc1, 0x8a, 0x89, 0x3f, 0xc4, 0x8b, 0x87, 0x7c, 0xe5, 0xdc, 0xcf, 0xd4, 0x90,
	0xa7, 0xcb, 0x83, 0xd4, 0xc4, 0x9f, 0xbd, 0xe1, 0x85, 0x1b, 0xdf, 0xf0, 0x5f, 0x2d, 0x70, 0x0e,
	0x98, 0xc2, 0x5d, 0xd3, 0x94, 0x47, 0x11, 0x8b, 0x7d, 0xc5, 0x45, 0xf8, 0x1f, 0x48, 0x9a, 0x9a,
	0xf3, 0xa5, 0x99, 0x39, 0x3f, 0xab, 0x75, 0x39, 0x43, 0xeb, 0x07, 0x57, 0x56, 0x91, 0x28, 0x7e,
	0x04, 0x6b, 0x62, 0xba, 0x91, 0x96, 0xba, 0x99, 0xcb, 0xd8, 0x1c, 0x1a, 0x5d, 0x15, 0x69, 0xe0,
	0x1c, 0xad, 0x7f, 0x2b, 0xc0, 0x7a, 0xd6, 0xe9, 0xff, 0x83, 0xe2, 0xff, 0xd6, 0xab, 0xb8, 0x30,
	0x25, 0x4b, 0x57, 0x4f, 0xc9, 0x72, 0x7a, 0x02, 0x91, 0x3b, 0x50, 0x32, 0xd0, 0x15, 0x84, 0x5e,
	0xd2, 0x90, 0x8f, 0xbe, 0x01, 0xb8, 0x4c, 0x89, 0xd4, 0x61, 0x7d, 0x9f, 0xba, 0xee, 0x73, 0xd7,
	0xeb, 0x9d, 0x7c, 0xe5, 0x7a, 0xfd, 0xa7, 0x66, 0x55, 0xbb, 0x45, 0xee, 0xc2, 0x9d, 0xf4, 0xce,
	0xfe, 0x11, 0x75, 0xbf, 0x76, 0x69, 0xcd, 0x9a, 0xdf, 0xe8, 0x1d, 0x79, 0xbd, 0xee, 0x97, 0x6e,
	0xad, 0xf0, 0xa8, 0x0f, 0xf6, 0xb3, 0x69, 0x33, 0x91, 0x0d, 0xb8, 0xfd, 0xac, 0xef, 0xd2, 0x13,
	0xe3, 0xb4, 0x77, 0xe2, 0xf5, 0xbb, 0x9d, 0xda, 0x2d, 0xd2, 0x80, 0xb7, 0x66, 0xcd, 0x07, 0xfd,
	0xee, 0x61, 0xc7, 0xeb, 0x76, 0x6a, 0xd6, 0xe2, 0x91, 0x27, 0x87, 0x87, 0xb5, 0xc2, 0xee, 0xf7,
	0xa5, 0xe9, 0xfb, 0x89, 0x5a, 0x3c, 0xd1, 0xdc, 0x92, 0x08, 0x6e, 0x2f, 0x8c, 0x25, 0xf2, 0x5e,
	0x16, 0xff, 0x79, 0xff, 0x22, 0x34, 0x76, 0xae, 0xe9, 0x9d, 0x74, 0xfd, 0x6b, 0xb8, 0x93, 0xf1,
	0xe4, 0x90, 0x56, 0x16, 0x4a, 0xfe, 0x2b, 0xdd, 0x68, 0x5f, 0xdb, 0x3f, 0x89, 0x2b, 0x81, 0x2c,
	0x3e, 0x23, 0x24, 0x33, 0xf9, 0xdc, 0x87, 0xad, 0xd1, 0xba, 0xae, 0x7b, 0x12, 0xf4, 0x3b, 0xd8,
	0xc8, 0x9c, 0xfa, 0xe4, 0xfd, 0x6b, 0x91, 0x96, 0x1a, 0x7a, 0x8d, 0x0f, 0x6e, 0x70, 0x22, 0x89,
	0xfe, 0x93, 0x05, 0x5b, 0x57, 0x0c, 0x22, 0xf2, 0x38, 0x07, 0xf2, 0x2f, 0xe6, 0x6f, 0xe3, 0x93,
	0x1b, 0x9f, 0x33, 0x09, 0xed, 0x3d, 0x7e, 0xfe, 0xd1, 0x58, 0x04, 0x7e, 0x38, 0x6e, 0x7d, 0xbc,
	0xab, 0x54, 0x6b, 0x20, 0xce, 0xda, 0xf8, 0x73, 0x62, 0x20, 0x82, 0xb6, 0x64, 0xf1, 0x6b, 0x3e,
	0x60, 0xb2, 0xbd, 0x88, 0xfd, 0xa2, 0x8c, 0x5e, 0x1f, 0xfe, 0x19, 0x00, 0x00, 0xff, 0xff, 0xe0,
	0x67, 0x89, 0x7d, 0xa3, 0x0c, 0x00, 0x00,
}
