// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channelpersonalization/channel_personalization.proto

package channel_personalization

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import empty "github.com/golang/protobuf/ptypes/empty"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type DecorationType int32

const (
	DecorationType_INVALID                      DecorationType = 0
	DecorationType_CHANNEL_ENTER_SPECIAL_EFFECT DecorationType = 1
	DecorationType_CHANNEL_ENTER_FUN_MESSAGE    DecorationType = 2
)

var DecorationType_name = map[int32]string{
	0: "INVALID",
	1: "CHANNEL_ENTER_SPECIAL_EFFECT",
	2: "CHANNEL_ENTER_FUN_MESSAGE",
}
var DecorationType_value = map[string]int32{
	"INVALID":                      0,
	"CHANNEL_ENTER_SPECIAL_EFFECT": 1,
	"CHANNEL_ENTER_FUN_MESSAGE":    2,
}

func (x DecorationType) String() string {
	return proto.EnumName(DecorationType_name, int32(x))
}
func (DecorationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{0}
}

// 特效类型，0普通，1财富值特效，2贵族特效
type EffectType int32

const (
	EffectType_NORMAL   EffectType = 0
	EffectType_RICH     EffectType = 1
	EffectType_NOBILITY EffectType = 2
)

var EffectType_name = map[int32]string{
	0: "NORMAL",
	1: "RICH",
	2: "NOBILITY",
}
var EffectType_value = map[string]int32{
	"NORMAL":   0,
	"RICH":     1,
	"NOBILITY": 2,
}

func (x EffectType) String() string {
	return proto.EnumName(EffectType_name, int32(x))
}
func (EffectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{1}
}

// 特效类型，0普通进房特效，1座骑进房特效
type EnterEffectType int32

const (
	EnterEffectType_ENTER_NORMAL EnterEffectType = 0
	EnterEffectType_ENTER_PET    EnterEffectType = 1
)

var EnterEffectType_name = map[int32]string{
	0: "ENTER_NORMAL",
	1: "ENTER_PET",
}
var EnterEffectType_value = map[string]int32{
	"ENTER_NORMAL": 0,
	"ENTER_PET":    1,
}

func (x EnterEffectType) String() string {
	return proto.EnumName(EnterEffectType_name, int32(x))
}
func (EnterEffectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{2}
}

type EffectCustomType int32

const (
	EffectCustomType_EffectNone       EffectCustomType = 0
	EffectCustomType_EffectCustomText EffectCustomType = 1
	EffectCustomType_EffectFusion     EffectCustomType = 2
)

var EffectCustomType_name = map[int32]string{
	0: "EffectNone",
	1: "EffectCustomText",
	2: "EffectFusion",
}
var EffectCustomType_value = map[string]int32{
	"EffectNone":       0,
	"EffectCustomText": 1,
	"EffectFusion":     2,
}

func (x EffectCustomType) String() string {
	return proto.EnumName(EffectCustomType_name, int32(x))
}
func (EffectCustomType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{3}
}

// 资源类型
type EffectSourceType int32

const (
	EffectSourceType_EFFECT_SOURCE_NONE           EffectSourceType = 0
	EffectSourceType_EFFECT_SOURCE_NORMAL_MP4     EffectSourceType = 1
	EffectSourceType_EFFECT_SOURCE_NORMAL_STATIC  EffectSourceType = 2
	EffectSourceType_EFFECT_SOURCE_NORMAL_LOTTIE  EffectSourceType = 3
	EffectSourceType_EFFECT_SOURCE_PET_MP4        EffectSourceType = 4
	EffectSourceType_EFFECT_SOURCE_PET_FULLSCREEN EffectSourceType = 5
	EffectSourceType_EFFECT_SOURCE_PET_LOTTIE     EffectSourceType = 6
)

var EffectSourceType_name = map[int32]string{
	0: "EFFECT_SOURCE_NONE",
	1: "EFFECT_SOURCE_NORMAL_MP4",
	2: "EFFECT_SOURCE_NORMAL_STATIC",
	3: "EFFECT_SOURCE_NORMAL_LOTTIE",
	4: "EFFECT_SOURCE_PET_MP4",
	5: "EFFECT_SOURCE_PET_FULLSCREEN",
	6: "EFFECT_SOURCE_PET_LOTTIE",
}
var EffectSourceType_value = map[string]int32{
	"EFFECT_SOURCE_NONE":           0,
	"EFFECT_SOURCE_NORMAL_MP4":     1,
	"EFFECT_SOURCE_NORMAL_STATIC":  2,
	"EFFECT_SOURCE_NORMAL_LOTTIE":  3,
	"EFFECT_SOURCE_PET_MP4":        4,
	"EFFECT_SOURCE_PET_FULLSCREEN": 5,
	"EFFECT_SOURCE_PET_LOTTIE":     6,
}

func (x EffectSourceType) String() string {
	return proto.EnumName(EffectSourceType_name, int32(x))
}
func (EffectSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{4}
}

// 资源类型
type EffectSpecialType int32

const (
	EffectSpecialType_EFFECT_SPECIAL_NONE      EffectSpecialType = 0
	EffectSpecialType_EFFECT_SPECIAL_PLATE_TOP EffectSpecialType = 1
)

var EffectSpecialType_name = map[int32]string{
	0: "EFFECT_SPECIAL_NONE",
	1: "EFFECT_SPECIAL_PLATE_TOP",
}
var EffectSpecialType_value = map[string]int32{
	"EFFECT_SPECIAL_NONE":      0,
	"EFFECT_SPECIAL_PLATE_TOP": 1,
}

func (x EffectSpecialType) String() string {
	return proto.EnumName(EffectSpecialType_name, int32(x))
}
func (EffectSpecialType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{5}
}

type UserDecoration_TextStatus int32

const (
	UserDecoration_Default     UserDecoration_TextStatus = 0
	UserDecoration_UnderReview UserDecoration_TextStatus = 1
)

var UserDecoration_TextStatus_name = map[int32]string{
	0: "Default",
	1: "UnderReview",
}
var UserDecoration_TextStatus_value = map[string]int32{
	"Default":     0,
	"UnderReview": 1,
}

func (x UserDecoration_TextStatus) String() string {
	return proto.EnumName(UserDecoration_TextStatus_name, int32(x))
}
func (UserDecoration_TextStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{5, 0}
}

type ChangeDecorationCustomTextReq_DecorationType int32

const (
	ChangeDecorationCustomTextReq_Unknown                ChangeDecorationCustomTextReq_DecorationType = 0
	ChangeDecorationCustomTextReq_HeadWear               ChangeDecorationCustomTextReq_DecorationType = 1
	ChangeDecorationCustomTextReq_ChannelPersonalization ChangeDecorationCustomTextReq_DecorationType = 2
)

var ChangeDecorationCustomTextReq_DecorationType_name = map[int32]string{
	0: "Unknown",
	1: "HeadWear",
	2: "ChannelPersonalization",
}
var ChangeDecorationCustomTextReq_DecorationType_value = map[string]int32{
	"Unknown":                0,
	"HeadWear":               1,
	"ChannelPersonalization": 2,
}

func (x ChangeDecorationCustomTextReq_DecorationType) String() string {
	return proto.EnumName(ChangeDecorationCustomTextReq_DecorationType_name, int32(x))
}
func (ChangeDecorationCustomTextReq_DecorationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{27, 0}
}

type ChannelEnterSpecialEffect struct {
	Name                 string     `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string     `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	PreviewUrl           string     `protobuf:"bytes,3,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`
	MinLevel             uint32     `protobuf:"varint,4,opt,name=min_level,json=minLevel,proto3" json:"min_level,omitempty"`
	EffectType           EffectType `protobuf:"varint,5,opt,name=effect_type,json=effectType,proto3,enum=channel.personalization.EffectType" json:"effect_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ChannelEnterSpecialEffect) Reset()         { *m = ChannelEnterSpecialEffect{} }
func (m *ChannelEnterSpecialEffect) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterSpecialEffect) ProtoMessage()    {}
func (*ChannelEnterSpecialEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{0}
}
func (m *ChannelEnterSpecialEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterSpecialEffect.Unmarshal(m, b)
}
func (m *ChannelEnterSpecialEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterSpecialEffect.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterSpecialEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterSpecialEffect.Merge(dst, src)
}
func (m *ChannelEnterSpecialEffect) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterSpecialEffect.Size(m)
}
func (m *ChannelEnterSpecialEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterSpecialEffect.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterSpecialEffect proto.InternalMessageInfo

func (m *ChannelEnterSpecialEffect) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChannelEnterSpecialEffect) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ChannelEnterSpecialEffect) GetPreviewUrl() string {
	if m != nil {
		return m.PreviewUrl
	}
	return ""
}

func (m *ChannelEnterSpecialEffect) GetMinLevel() uint32 {
	if m != nil {
		return m.MinLevel
	}
	return 0
}

func (m *ChannelEnterSpecialEffect) GetEffectType() EffectType {
	if m != nil {
		return m.EffectType
	}
	return EffectType_NORMAL
}

type ChannelEnterSpecialEffectList struct {
	SpecialEffects       []*ChannelEnterSpecialEffect `protobuf:"bytes,1,rep,name=special_effects,json=specialEffects,proto3" json:"special_effects,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ChannelEnterSpecialEffectList) Reset()         { *m = ChannelEnterSpecialEffectList{} }
func (m *ChannelEnterSpecialEffectList) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterSpecialEffectList) ProtoMessage()    {}
func (*ChannelEnterSpecialEffectList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{1}
}
func (m *ChannelEnterSpecialEffectList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterSpecialEffectList.Unmarshal(m, b)
}
func (m *ChannelEnterSpecialEffectList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterSpecialEffectList.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterSpecialEffectList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterSpecialEffectList.Merge(dst, src)
}
func (m *ChannelEnterSpecialEffectList) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterSpecialEffectList.Size(m)
}
func (m *ChannelEnterSpecialEffectList) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterSpecialEffectList.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterSpecialEffectList proto.InternalMessageInfo

func (m *ChannelEnterSpecialEffectList) GetSpecialEffects() []*ChannelEnterSpecialEffect {
	if m != nil {
		return m.SpecialEffects
	}
	return nil
}

type ChannelEnterFunMessage struct {
	Random               bool     `protobuf:"varint,1,opt,name=random,proto3" json:"random,omitempty"`
	FixedText            string   `protobuf:"bytes,2,opt,name=fixed_text,json=fixedText,proto3" json:"fixed_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelEnterFunMessage) Reset()         { *m = ChannelEnterFunMessage{} }
func (m *ChannelEnterFunMessage) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterFunMessage) ProtoMessage()    {}
func (*ChannelEnterFunMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{2}
}
func (m *ChannelEnterFunMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterFunMessage.Unmarshal(m, b)
}
func (m *ChannelEnterFunMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterFunMessage.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterFunMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterFunMessage.Merge(dst, src)
}
func (m *ChannelEnterFunMessage) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterFunMessage.Size(m)
}
func (m *ChannelEnterFunMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterFunMessage.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterFunMessage proto.InternalMessageInfo

func (m *ChannelEnterFunMessage) GetRandom() bool {
	if m != nil {
		return m.Random
	}
	return false
}

func (m *ChannelEnterFunMessage) GetFixedText() string {
	if m != nil {
		return m.FixedText
	}
	return ""
}

// 个性化装饰
// NOTE: 一个用户拥有多个分类的个性化装饰，每个类别下都按照<uid, decoration_id>作为唯一索引
type Decoration struct {
	Id   string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type DecorationType `protobuf:"varint,2,opt,name=type,proto3,enum=channel.personalization.DecorationType" json:"type,omitempty"`
	Ver  string         `protobuf:"bytes,3,opt,name=ver,proto3" json:"ver,omitempty"`
	// 以下字段发放的时候不用填写
	Detail               *DecorationDetail `protobuf:"bytes,4,opt,name=detail,proto3" json:"detail,omitempty"`
	OperatorInfo         string            `protobuf:"bytes,5,opt,name=operator_info,json=operatorInfo,proto3" json:"operator_info,omitempty"`
	Notice               string            `protobuf:"bytes,6,opt,name=notice,proto3" json:"notice,omitempty"`
	AwardTime            uint32            `protobuf:"varint,7,opt,name=award_time,json=awardTime,proto3" json:"award_time,omitempty"`
	EnterEffectType      EnterEffectType   `protobuf:"varint,8,opt,name=enter_effect_type,json=enterEffectType,proto3,enum=channel.personalization.EnterEffectType" json:"enter_effect_type,omitempty"`
	SourceUrl            string            `protobuf:"bytes,9,opt,name=source_url,json=sourceUrl,proto3" json:"source_url,omitempty"`
	PreviewUrl           string            `protobuf:"bytes,10,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`
	CreateTime           uint32            `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32            `protobuf:"varint,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CustomType           EffectCustomType  `protobuf:"varint,13,opt,name=custom_type,json=customType,proto3,enum=channel.personalization.EffectCustomType" json:"custom_type,omitempty"`
	CustomColor          string            `protobuf:"bytes,14,opt,name=custom_color,json=customColor,proto3" json:"custom_color,omitempty"`
	SourceType           EffectSourceType  `protobuf:"varint,15,opt,name=source_type,json=sourceType,proto3,enum=channel.personalization.EffectSourceType" json:"source_type,omitempty"`
	SpecialType          EffectSpecialType `protobuf:"varint,16,opt,name=special_type,json=specialType,proto3,enum=channel.personalization.EffectSpecialType" json:"special_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Decoration) Reset()         { *m = Decoration{} }
func (m *Decoration) String() string { return proto.CompactTextString(m) }
func (*Decoration) ProtoMessage()    {}
func (*Decoration) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{3}
}
func (m *Decoration) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Decoration.Unmarshal(m, b)
}
func (m *Decoration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Decoration.Marshal(b, m, deterministic)
}
func (dst *Decoration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Decoration.Merge(dst, src)
}
func (m *Decoration) XXX_Size() int {
	return xxx_messageInfo_Decoration.Size(m)
}
func (m *Decoration) XXX_DiscardUnknown() {
	xxx_messageInfo_Decoration.DiscardUnknown(m)
}

var xxx_messageInfo_Decoration proto.InternalMessageInfo

func (m *Decoration) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Decoration) GetType() DecorationType {
	if m != nil {
		return m.Type
	}
	return DecorationType_INVALID
}

func (m *Decoration) GetVer() string {
	if m != nil {
		return m.Ver
	}
	return ""
}

func (m *Decoration) GetDetail() *DecorationDetail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *Decoration) GetOperatorInfo() string {
	if m != nil {
		return m.OperatorInfo
	}
	return ""
}

func (m *Decoration) GetNotice() string {
	if m != nil {
		return m.Notice
	}
	return ""
}

func (m *Decoration) GetAwardTime() uint32 {
	if m != nil {
		return m.AwardTime
	}
	return 0
}

func (m *Decoration) GetEnterEffectType() EnterEffectType {
	if m != nil {
		return m.EnterEffectType
	}
	return EnterEffectType_ENTER_NORMAL
}

func (m *Decoration) GetSourceUrl() string {
	if m != nil {
		return m.SourceUrl
	}
	return ""
}

func (m *Decoration) GetPreviewUrl() string {
	if m != nil {
		return m.PreviewUrl
	}
	return ""
}

func (m *Decoration) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Decoration) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Decoration) GetCustomType() EffectCustomType {
	if m != nil {
		return m.CustomType
	}
	return EffectCustomType_EffectNone
}

func (m *Decoration) GetCustomColor() string {
	if m != nil {
		return m.CustomColor
	}
	return ""
}

func (m *Decoration) GetSourceType() EffectSourceType {
	if m != nil {
		return m.SourceType
	}
	return EffectSourceType_EFFECT_SOURCE_NONE
}

func (m *Decoration) GetSpecialType() EffectSpecialType {
	if m != nil {
		return m.SpecialType
	}
	return EffectSpecialType_EFFECT_SPECIAL_NONE
}

type DecorationDetail struct {
	// 用oneof更合适，但是想直接把pb的结构存进mongo，序列化成问题
	// 根据DecorationType决定哪个字段填值
	ChannelEnterSpecialEffect *ChannelEnterSpecialEffect `protobuf:"bytes,1,opt,name=channel_enter_special_effect,json=channelEnterSpecialEffect,proto3" json:"channel_enter_special_effect,omitempty"`
	ChannelEnterFunMessage    *ChannelEnterFunMessage    `protobuf:"bytes,2,opt,name=channel_enter_fun_message,json=channelEnterFunMessage,proto3" json:"channel_enter_fun_message,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}                   `json:"-"`
	XXX_unrecognized          []byte                     `json:"-"`
	XXX_sizecache             int32                      `json:"-"`
}

func (m *DecorationDetail) Reset()         { *m = DecorationDetail{} }
func (m *DecorationDetail) String() string { return proto.CompactTextString(m) }
func (*DecorationDetail) ProtoMessage()    {}
func (*DecorationDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{4}
}
func (m *DecorationDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecorationDetail.Unmarshal(m, b)
}
func (m *DecorationDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecorationDetail.Marshal(b, m, deterministic)
}
func (dst *DecorationDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecorationDetail.Merge(dst, src)
}
func (m *DecorationDetail) XXX_Size() int {
	return xxx_messageInfo_DecorationDetail.Size(m)
}
func (m *DecorationDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DecorationDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DecorationDetail proto.InternalMessageInfo

func (m *DecorationDetail) GetChannelEnterSpecialEffect() *ChannelEnterSpecialEffect {
	if m != nil {
		return m.ChannelEnterSpecialEffect
	}
	return nil
}

func (m *DecorationDetail) GetChannelEnterFunMessage() *ChannelEnterFunMessage {
	if m != nil {
		return m.ChannelEnterFunMessage
	}
	return nil
}

type UserDecoration struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Decoration           *Decoration               `protobuf:"bytes,2,opt,name=decoration,proto3" json:"decoration,omitempty"`
	EffectBegin          uint64                    `protobuf:"varint,3,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint64                    `protobuf:"varint,4,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	GrantAt              uint64                    `protobuf:"varint,5,opt,name=grant_at,json=grantAt,proto3" json:"grant_at,omitempty"`
	GrantReason          string                    `protobuf:"bytes,6,opt,name=grant_reason,json=grantReason,proto3" json:"grant_reason,omitempty"`
	GrantOperator        string                    `protobuf:"bytes,7,opt,name=grant_operator,json=grantOperator,proto3" json:"grant_operator,omitempty"`
	Actived              bool                      `protobuf:"varint,8,opt,name=actived,proto3" json:"actived,omitempty"`
	OrderId              string                    `protobuf:"bytes,9,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	CustomText           string                    `protobuf:"bytes,10,opt,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	FusionTtid           string                    `protobuf:"bytes,11,opt,name=fusion_ttid,json=fusionTtid,proto3" json:"fusion_ttid,omitempty"`
	ExtendJson           string                    `protobuf:"bytes,12,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	TextStatus           UserDecoration_TextStatus `protobuf:"varint,13,opt,name=text_status,json=textStatus,proto3,enum=channel.personalization.UserDecoration_TextStatus" json:"text_status,omitempty"`
	ShowCustomText       string                    `protobuf:"bytes,14,opt,name=show_custom_text,json=showCustomText,proto3" json:"show_custom_text,omitempty"`
	RemainChangeCount    uint32                    `protobuf:"varint,15,opt,name=remain_change_count,json=remainChangeCount,proto3" json:"remain_change_count,omitempty"`
	FusionNickname       string                    `protobuf:"bytes,16,opt,name=fusion_nickname,json=fusionNickname,proto3" json:"fusion_nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UserDecoration) Reset()         { *m = UserDecoration{} }
func (m *UserDecoration) String() string { return proto.CompactTextString(m) }
func (*UserDecoration) ProtoMessage()    {}
func (*UserDecoration) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{5}
}
func (m *UserDecoration) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDecoration.Unmarshal(m, b)
}
func (m *UserDecoration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDecoration.Marshal(b, m, deterministic)
}
func (dst *UserDecoration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDecoration.Merge(dst, src)
}
func (m *UserDecoration) XXX_Size() int {
	return xxx_messageInfo_UserDecoration.Size(m)
}
func (m *UserDecoration) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDecoration.DiscardUnknown(m)
}

var xxx_messageInfo_UserDecoration proto.InternalMessageInfo

func (m *UserDecoration) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDecoration) GetDecoration() *Decoration {
	if m != nil {
		return m.Decoration
	}
	return nil
}

func (m *UserDecoration) GetEffectBegin() uint64 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *UserDecoration) GetEffectEnd() uint64 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *UserDecoration) GetGrantAt() uint64 {
	if m != nil {
		return m.GrantAt
	}
	return 0
}

func (m *UserDecoration) GetGrantReason() string {
	if m != nil {
		return m.GrantReason
	}
	return ""
}

func (m *UserDecoration) GetGrantOperator() string {
	if m != nil {
		return m.GrantOperator
	}
	return ""
}

func (m *UserDecoration) GetActived() bool {
	if m != nil {
		return m.Actived
	}
	return false
}

func (m *UserDecoration) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UserDecoration) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

func (m *UserDecoration) GetFusionTtid() string {
	if m != nil {
		return m.FusionTtid
	}
	return ""
}

func (m *UserDecoration) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

func (m *UserDecoration) GetTextStatus() UserDecoration_TextStatus {
	if m != nil {
		return m.TextStatus
	}
	return UserDecoration_Default
}

func (m *UserDecoration) GetShowCustomText() string {
	if m != nil {
		return m.ShowCustomText
	}
	return ""
}

func (m *UserDecoration) GetRemainChangeCount() uint32 {
	if m != nil {
		return m.RemainChangeCount
	}
	return 0
}

func (m *UserDecoration) GetFusionNickname() string {
	if m != nil {
		return m.FusionNickname
	}
	return ""
}

type UserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RichLevel            uint32   `protobuf:"varint,2,opt,name=rich_level,json=richLevel,proto3" json:"rich_level,omitempty"`
	NobilityLevel        uint32   `protobuf:"varint,3,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{6}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfo) GetRichLevel() uint32 {
	if m != nil {
		return m.RichLevel
	}
	return 0
}

func (m *UserInfo) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

type GetUserDecorationsReq struct {
	UserInfo             *UserInfo      `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	OnlyEffective        bool           `protobuf:"varint,2,opt,name=only_effective,json=onlyEffective,proto3" json:"only_effective,omitempty"`
	DecorationType       DecorationType `protobuf:"varint,3,opt,name=decoration_type,json=decorationType,proto3,enum=channel.personalization.DecorationType" json:"decoration_type,omitempty"`
	NeedUnderViewInfo    bool           `protobuf:"varint,4,opt,name=need_under_view_info,json=needUnderViewInfo,proto3" json:"need_under_view_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserDecorationsReq) Reset()         { *m = GetUserDecorationsReq{} }
func (m *GetUserDecorationsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDecorationsReq) ProtoMessage()    {}
func (*GetUserDecorationsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{7}
}
func (m *GetUserDecorationsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDecorationsReq.Unmarshal(m, b)
}
func (m *GetUserDecorationsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDecorationsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDecorationsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDecorationsReq.Merge(dst, src)
}
func (m *GetUserDecorationsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDecorationsReq.Size(m)
}
func (m *GetUserDecorationsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDecorationsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDecorationsReq proto.InternalMessageInfo

func (m *GetUserDecorationsReq) GetUserInfo() *UserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GetUserDecorationsReq) GetOnlyEffective() bool {
	if m != nil {
		return m.OnlyEffective
	}
	return false
}

func (m *GetUserDecorationsReq) GetDecorationType() DecorationType {
	if m != nil {
		return m.DecorationType
	}
	return DecorationType_INVALID
}

func (m *GetUserDecorationsReq) GetNeedUnderViewInfo() bool {
	if m != nil {
		return m.NeedUnderViewInfo
	}
	return false
}

type GetUserDecorationsResp struct {
	UserDecorations      []*UserDecoration `protobuf:"bytes,1,rep,name=user_decorations,json=userDecorations,proto3" json:"user_decorations,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserDecorationsResp) Reset()         { *m = GetUserDecorationsResp{} }
func (m *GetUserDecorationsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDecorationsResp) ProtoMessage()    {}
func (*GetUserDecorationsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{8}
}
func (m *GetUserDecorationsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDecorationsResp.Unmarshal(m, b)
}
func (m *GetUserDecorationsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDecorationsResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDecorationsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDecorationsResp.Merge(dst, src)
}
func (m *GetUserDecorationsResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDecorationsResp.Size(m)
}
func (m *GetUserDecorationsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDecorationsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDecorationsResp proto.InternalMessageInfo

func (m *GetUserDecorationsResp) GetUserDecorations() []*UserDecoration {
	if m != nil {
		return m.UserDecorations
	}
	return nil
}

type GrantDecorationToUserReq struct {
	UserDecoration       *UserDecoration `protobuf:"bytes,1,opt,name=user_decoration,json=userDecoration,proto3" json:"user_decoration,omitempty"`
	AddTtlForExisting    bool            `protobuf:"varint,2,opt,name=add_ttl_for_existing,json=addTtlForExisting,proto3" json:"add_ttl_for_existing,omitempty"`
	OutsideTime          int64           `protobuf:"varint,3,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	SourceType           uint32          `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GrantDecorationToUserReq) Reset()         { *m = GrantDecorationToUserReq{} }
func (m *GrantDecorationToUserReq) String() string { return proto.CompactTextString(m) }
func (*GrantDecorationToUserReq) ProtoMessage()    {}
func (*GrantDecorationToUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{9}
}
func (m *GrantDecorationToUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantDecorationToUserReq.Unmarshal(m, b)
}
func (m *GrantDecorationToUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantDecorationToUserReq.Marshal(b, m, deterministic)
}
func (dst *GrantDecorationToUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantDecorationToUserReq.Merge(dst, src)
}
func (m *GrantDecorationToUserReq) XXX_Size() int {
	return xxx_messageInfo_GrantDecorationToUserReq.Size(m)
}
func (m *GrantDecorationToUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantDecorationToUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_GrantDecorationToUserReq proto.InternalMessageInfo

func (m *GrantDecorationToUserReq) GetUserDecoration() *UserDecoration {
	if m != nil {
		return m.UserDecoration
	}
	return nil
}

func (m *GrantDecorationToUserReq) GetAddTtlForExisting() bool {
	if m != nil {
		return m.AddTtlForExisting
	}
	return false
}

func (m *GrantDecorationToUserReq) GetOutsideTime() int64 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

func (m *GrantDecorationToUserReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

type GrantDecorationToUserResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantDecorationToUserResp) Reset()         { *m = GrantDecorationToUserResp{} }
func (m *GrantDecorationToUserResp) String() string { return proto.CompactTextString(m) }
func (*GrantDecorationToUserResp) ProtoMessage()    {}
func (*GrantDecorationToUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{10}
}
func (m *GrantDecorationToUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantDecorationToUserResp.Unmarshal(m, b)
}
func (m *GrantDecorationToUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantDecorationToUserResp.Marshal(b, m, deterministic)
}
func (dst *GrantDecorationToUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantDecorationToUserResp.Merge(dst, src)
}
func (m *GrantDecorationToUserResp) XXX_Size() int {
	return xxx_messageInfo_GrantDecorationToUserResp.Size(m)
}
func (m *GrantDecorationToUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantDecorationToUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_GrantDecorationToUserResp proto.InternalMessageInfo

type ActivateUserDecorationReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DecorationType       DecorationType `protobuf:"varint,2,opt,name=decoration_type,json=decorationType,proto3,enum=channel.personalization.DecorationType" json:"decoration_type,omitempty"`
	DecorationId         string         `protobuf:"bytes,3,opt,name=decoration_id,json=decorationId,proto3" json:"decoration_id,omitempty"`
	CustomText           string         `protobuf:"bytes,4,opt,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	FusionTtid           string         `protobuf:"bytes,5,opt,name=fusion_ttid,json=fusionTtid,proto3" json:"fusion_ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ActivateUserDecorationReq) Reset()         { *m = ActivateUserDecorationReq{} }
func (m *ActivateUserDecorationReq) String() string { return proto.CompactTextString(m) }
func (*ActivateUserDecorationReq) ProtoMessage()    {}
func (*ActivateUserDecorationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{11}
}
func (m *ActivateUserDecorationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivateUserDecorationReq.Unmarshal(m, b)
}
func (m *ActivateUserDecorationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivateUserDecorationReq.Marshal(b, m, deterministic)
}
func (dst *ActivateUserDecorationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivateUserDecorationReq.Merge(dst, src)
}
func (m *ActivateUserDecorationReq) XXX_Size() int {
	return xxx_messageInfo_ActivateUserDecorationReq.Size(m)
}
func (m *ActivateUserDecorationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivateUserDecorationReq.DiscardUnknown(m)
}

var xxx_messageInfo_ActivateUserDecorationReq proto.InternalMessageInfo

func (m *ActivateUserDecorationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ActivateUserDecorationReq) GetDecorationType() DecorationType {
	if m != nil {
		return m.DecorationType
	}
	return DecorationType_INVALID
}

func (m *ActivateUserDecorationReq) GetDecorationId() string {
	if m != nil {
		return m.DecorationId
	}
	return ""
}

func (m *ActivateUserDecorationReq) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

func (m *ActivateUserDecorationReq) GetFusionTtid() string {
	if m != nil {
		return m.FusionTtid
	}
	return ""
}

type ActivateUserDecorationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivateUserDecorationResp) Reset()         { *m = ActivateUserDecorationResp{} }
func (m *ActivateUserDecorationResp) String() string { return proto.CompactTextString(m) }
func (*ActivateUserDecorationResp) ProtoMessage()    {}
func (*ActivateUserDecorationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{12}
}
func (m *ActivateUserDecorationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivateUserDecorationResp.Unmarshal(m, b)
}
func (m *ActivateUserDecorationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivateUserDecorationResp.Marshal(b, m, deterministic)
}
func (dst *ActivateUserDecorationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivateUserDecorationResp.Merge(dst, src)
}
func (m *ActivateUserDecorationResp) XXX_Size() int {
	return xxx_messageInfo_ActivateUserDecorationResp.Size(m)
}
func (m *ActivateUserDecorationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivateUserDecorationResp.DiscardUnknown(m)
}

var xxx_messageInfo_ActivateUserDecorationResp proto.InternalMessageInfo

type AddUserDecorationConfigReq struct {
	Decoration           *Decoration `protobuf:"bytes,1,opt,name=decoration,proto3" json:"decoration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AddUserDecorationConfigReq) Reset()         { *m = AddUserDecorationConfigReq{} }
func (m *AddUserDecorationConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddUserDecorationConfigReq) ProtoMessage()    {}
func (*AddUserDecorationConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{13}
}
func (m *AddUserDecorationConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserDecorationConfigReq.Unmarshal(m, b)
}
func (m *AddUserDecorationConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserDecorationConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddUserDecorationConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserDecorationConfigReq.Merge(dst, src)
}
func (m *AddUserDecorationConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddUserDecorationConfigReq.Size(m)
}
func (m *AddUserDecorationConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserDecorationConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserDecorationConfigReq proto.InternalMessageInfo

func (m *AddUserDecorationConfigReq) GetDecoration() *Decoration {
	if m != nil {
		return m.Decoration
	}
	return nil
}

type AddUserDecorationConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserDecorationConfigResp) Reset()         { *m = AddUserDecorationConfigResp{} }
func (m *AddUserDecorationConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddUserDecorationConfigResp) ProtoMessage()    {}
func (*AddUserDecorationConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{14}
}
func (m *AddUserDecorationConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserDecorationConfigResp.Unmarshal(m, b)
}
func (m *AddUserDecorationConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserDecorationConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddUserDecorationConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserDecorationConfigResp.Merge(dst, src)
}
func (m *AddUserDecorationConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddUserDecorationConfigResp.Size(m)
}
func (m *AddUserDecorationConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserDecorationConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserDecorationConfigResp proto.InternalMessageInfo

type DelUserDecorationConfigReq struct {
	Id                   string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 DecorationType `protobuf:"varint,2,opt,name=type,proto3,enum=channel.personalization.DecorationType" json:"type,omitempty"`
	CustomText           string         `protobuf:"bytes,3,opt,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	FusionTtid           string         `protobuf:"bytes,4,opt,name=fusion_ttid,json=fusionTtid,proto3" json:"fusion_ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *DelUserDecorationConfigReq) Reset()         { *m = DelUserDecorationConfigReq{} }
func (m *DelUserDecorationConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelUserDecorationConfigReq) ProtoMessage()    {}
func (*DelUserDecorationConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{15}
}
func (m *DelUserDecorationConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserDecorationConfigReq.Unmarshal(m, b)
}
func (m *DelUserDecorationConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserDecorationConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelUserDecorationConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserDecorationConfigReq.Merge(dst, src)
}
func (m *DelUserDecorationConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelUserDecorationConfigReq.Size(m)
}
func (m *DelUserDecorationConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserDecorationConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserDecorationConfigReq proto.InternalMessageInfo

func (m *DelUserDecorationConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *DelUserDecorationConfigReq) GetType() DecorationType {
	if m != nil {
		return m.Type
	}
	return DecorationType_INVALID
}

func (m *DelUserDecorationConfigReq) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

func (m *DelUserDecorationConfigReq) GetFusionTtid() string {
	if m != nil {
		return m.FusionTtid
	}
	return ""
}

type DelUserDecorationConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserDecorationConfigResp) Reset()         { *m = DelUserDecorationConfigResp{} }
func (m *DelUserDecorationConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelUserDecorationConfigResp) ProtoMessage()    {}
func (*DelUserDecorationConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{16}
}
func (m *DelUserDecorationConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserDecorationConfigResp.Unmarshal(m, b)
}
func (m *DelUserDecorationConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserDecorationConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelUserDecorationConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserDecorationConfigResp.Merge(dst, src)
}
func (m *DelUserDecorationConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelUserDecorationConfigResp.Size(m)
}
func (m *DelUserDecorationConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserDecorationConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserDecorationConfigResp proto.InternalMessageInfo

type UpdateUserDecorationConfigReq struct {
	Decoration           *Decoration `protobuf:"bytes,1,opt,name=decoration,proto3" json:"decoration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateUserDecorationConfigReq) Reset()         { *m = UpdateUserDecorationConfigReq{} }
func (m *UpdateUserDecorationConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserDecorationConfigReq) ProtoMessage()    {}
func (*UpdateUserDecorationConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{17}
}
func (m *UpdateUserDecorationConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserDecorationConfigReq.Unmarshal(m, b)
}
func (m *UpdateUserDecorationConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserDecorationConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateUserDecorationConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserDecorationConfigReq.Merge(dst, src)
}
func (m *UpdateUserDecorationConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUserDecorationConfigReq.Size(m)
}
func (m *UpdateUserDecorationConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserDecorationConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserDecorationConfigReq proto.InternalMessageInfo

func (m *UpdateUserDecorationConfigReq) GetDecoration() *Decoration {
	if m != nil {
		return m.Decoration
	}
	return nil
}

type UpdateUserDecorationConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserDecorationConfigResp) Reset()         { *m = UpdateUserDecorationConfigResp{} }
func (m *UpdateUserDecorationConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserDecorationConfigResp) ProtoMessage()    {}
func (*UpdateUserDecorationConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{18}
}
func (m *UpdateUserDecorationConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserDecorationConfigResp.Unmarshal(m, b)
}
func (m *UpdateUserDecorationConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserDecorationConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateUserDecorationConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserDecorationConfigResp.Merge(dst, src)
}
func (m *UpdateUserDecorationConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateUserDecorationConfigResp.Size(m)
}
func (m *UpdateUserDecorationConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserDecorationConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserDecorationConfigResp proto.InternalMessageInfo

// 财富等级特效
type RichLevelSpecialEffect struct {
	MinLevel             uint32      `protobuf:"varint,1,opt,name=min_level,json=minLevel,proto3" json:"min_level,omitempty"`
	EffectInfo           *Decoration `protobuf:"bytes,2,opt,name=effect_info,json=effectInfo,proto3" json:"effect_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *RichLevelSpecialEffect) Reset()         { *m = RichLevelSpecialEffect{} }
func (m *RichLevelSpecialEffect) String() string { return proto.CompactTextString(m) }
func (*RichLevelSpecialEffect) ProtoMessage()    {}
func (*RichLevelSpecialEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{19}
}
func (m *RichLevelSpecialEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RichLevelSpecialEffect.Unmarshal(m, b)
}
func (m *RichLevelSpecialEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RichLevelSpecialEffect.Marshal(b, m, deterministic)
}
func (dst *RichLevelSpecialEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RichLevelSpecialEffect.Merge(dst, src)
}
func (m *RichLevelSpecialEffect) XXX_Size() int {
	return xxx_messageInfo_RichLevelSpecialEffect.Size(m)
}
func (m *RichLevelSpecialEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_RichLevelSpecialEffect.DiscardUnknown(m)
}

var xxx_messageInfo_RichLevelSpecialEffect proto.InternalMessageInfo

func (m *RichLevelSpecialEffect) GetMinLevel() uint32 {
	if m != nil {
		return m.MinLevel
	}
	return 0
}

func (m *RichLevelSpecialEffect) GetEffectInfo() *Decoration {
	if m != nil {
		return m.EffectInfo
	}
	return nil
}

// 贵族等级特效
type NobilityLevelSpecialEffect struct {
	MinLevel             uint32      `protobuf:"varint,1,opt,name=min_level,json=minLevel,proto3" json:"min_level,omitempty"`
	EffectInfo           *Decoration `protobuf:"bytes,2,opt,name=effect_info,json=effectInfo,proto3" json:"effect_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *NobilityLevelSpecialEffect) Reset()         { *m = NobilityLevelSpecialEffect{} }
func (m *NobilityLevelSpecialEffect) String() string { return proto.CompactTextString(m) }
func (*NobilityLevelSpecialEffect) ProtoMessage()    {}
func (*NobilityLevelSpecialEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{20}
}
func (m *NobilityLevelSpecialEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NobilityLevelSpecialEffect.Unmarshal(m, b)
}
func (m *NobilityLevelSpecialEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NobilityLevelSpecialEffect.Marshal(b, m, deterministic)
}
func (dst *NobilityLevelSpecialEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NobilityLevelSpecialEffect.Merge(dst, src)
}
func (m *NobilityLevelSpecialEffect) XXX_Size() int {
	return xxx_messageInfo_NobilityLevelSpecialEffect.Size(m)
}
func (m *NobilityLevelSpecialEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_NobilityLevelSpecialEffect.DiscardUnknown(m)
}

var xxx_messageInfo_NobilityLevelSpecialEffect proto.InternalMessageInfo

func (m *NobilityLevelSpecialEffect) GetMinLevel() uint32 {
	if m != nil {
		return m.MinLevel
	}
	return 0
}

func (m *NobilityLevelSpecialEffect) GetEffectInfo() *Decoration {
	if m != nil {
		return m.EffectInfo
	}
	return nil
}

type ChannelEnterSpecialEffectConfig struct {
	RichLevelConfig         []*RichLevelSpecialEffect     `protobuf:"bytes,1,rep,name=rich_level_config,json=richLevelConfig,proto3" json:"rich_level_config,omitempty"`
	NonRichLevelDecorations map[string]*Decoration        `protobuf:"bytes,2,rep,name=non_rich_level_decorations,json=nonRichLevelDecorations,proto3" json:"non_rich_level_decorations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	NobilityLevelConfig     []*NobilityLevelSpecialEffect `protobuf:"bytes,3,rep,name=nobility_level_config,json=nobilityLevelConfig,proto3" json:"nobility_level_config,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                      `json:"-"`
	XXX_unrecognized        []byte                        `json:"-"`
	XXX_sizecache           int32                         `json:"-"`
}

func (m *ChannelEnterSpecialEffectConfig) Reset()         { *m = ChannelEnterSpecialEffectConfig{} }
func (m *ChannelEnterSpecialEffectConfig) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterSpecialEffectConfig) ProtoMessage()    {}
func (*ChannelEnterSpecialEffectConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{21}
}
func (m *ChannelEnterSpecialEffectConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterSpecialEffectConfig.Unmarshal(m, b)
}
func (m *ChannelEnterSpecialEffectConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterSpecialEffectConfig.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterSpecialEffectConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterSpecialEffectConfig.Merge(dst, src)
}
func (m *ChannelEnterSpecialEffectConfig) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterSpecialEffectConfig.Size(m)
}
func (m *ChannelEnterSpecialEffectConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterSpecialEffectConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterSpecialEffectConfig proto.InternalMessageInfo

func (m *ChannelEnterSpecialEffectConfig) GetRichLevelConfig() []*RichLevelSpecialEffect {
	if m != nil {
		return m.RichLevelConfig
	}
	return nil
}

func (m *ChannelEnterSpecialEffectConfig) GetNonRichLevelDecorations() map[string]*Decoration {
	if m != nil {
		return m.NonRichLevelDecorations
	}
	return nil
}

func (m *ChannelEnterSpecialEffectConfig) GetNobilityLevelConfig() []*NobilityLevelSpecialEffect {
	if m != nil {
		return m.NobilityLevelConfig
	}
	return nil
}

type SetUserDecorationSpecialLevelReq struct {
	Id                   string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 DecorationType `protobuf:"varint,2,opt,name=type,proto3,enum=channel.personalization.DecorationType" json:"type,omitempty"`
	Ver                  string         `protobuf:"bytes,3,opt,name=ver,proto3" json:"ver,omitempty"`
	EffectType           EffectType     `protobuf:"varint,4,opt,name=effect_type,json=effectType,proto3,enum=channel.personalization.EffectType" json:"effect_type,omitempty"`
	MinLevel             uint32         `protobuf:"varint,5,opt,name=min_level,json=minLevel,proto3" json:"min_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetUserDecorationSpecialLevelReq) Reset()         { *m = SetUserDecorationSpecialLevelReq{} }
func (m *SetUserDecorationSpecialLevelReq) String() string { return proto.CompactTextString(m) }
func (*SetUserDecorationSpecialLevelReq) ProtoMessage()    {}
func (*SetUserDecorationSpecialLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{22}
}
func (m *SetUserDecorationSpecialLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserDecorationSpecialLevelReq.Unmarshal(m, b)
}
func (m *SetUserDecorationSpecialLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserDecorationSpecialLevelReq.Marshal(b, m, deterministic)
}
func (dst *SetUserDecorationSpecialLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserDecorationSpecialLevelReq.Merge(dst, src)
}
func (m *SetUserDecorationSpecialLevelReq) XXX_Size() int {
	return xxx_messageInfo_SetUserDecorationSpecialLevelReq.Size(m)
}
func (m *SetUserDecorationSpecialLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserDecorationSpecialLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserDecorationSpecialLevelReq proto.InternalMessageInfo

func (m *SetUserDecorationSpecialLevelReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SetUserDecorationSpecialLevelReq) GetType() DecorationType {
	if m != nil {
		return m.Type
	}
	return DecorationType_INVALID
}

func (m *SetUserDecorationSpecialLevelReq) GetVer() string {
	if m != nil {
		return m.Ver
	}
	return ""
}

func (m *SetUserDecorationSpecialLevelReq) GetEffectType() EffectType {
	if m != nil {
		return m.EffectType
	}
	return EffectType_NORMAL
}

func (m *SetUserDecorationSpecialLevelReq) GetMinLevel() uint32 {
	if m != nil {
		return m.MinLevel
	}
	return 0
}

type SetUserDecorationSpecialLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserDecorationSpecialLevelResp) Reset()         { *m = SetUserDecorationSpecialLevelResp{} }
func (m *SetUserDecorationSpecialLevelResp) String() string { return proto.CompactTextString(m) }
func (*SetUserDecorationSpecialLevelResp) ProtoMessage()    {}
func (*SetUserDecorationSpecialLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{23}
}
func (m *SetUserDecorationSpecialLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserDecorationSpecialLevelResp.Unmarshal(m, b)
}
func (m *SetUserDecorationSpecialLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserDecorationSpecialLevelResp.Marshal(b, m, deterministic)
}
func (dst *SetUserDecorationSpecialLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserDecorationSpecialLevelResp.Merge(dst, src)
}
func (m *SetUserDecorationSpecialLevelResp) XXX_Size() int {
	return xxx_messageInfo_SetUserDecorationSpecialLevelResp.Size(m)
}
func (m *SetUserDecorationSpecialLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserDecorationSpecialLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserDecorationSpecialLevelResp proto.InternalMessageInfo

type FailedUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FailedUserInfo) Reset()         { *m = FailedUserInfo{} }
func (m *FailedUserInfo) String() string { return proto.CompactTextString(m) }
func (*FailedUserInfo) ProtoMessage()    {}
func (*FailedUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{24}
}
func (m *FailedUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FailedUserInfo.Unmarshal(m, b)
}
func (m *FailedUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FailedUserInfo.Marshal(b, m, deterministic)
}
func (dst *FailedUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FailedUserInfo.Merge(dst, src)
}
func (m *FailedUserInfo) XXX_Size() int {
	return xxx_messageInfo_FailedUserInfo.Size(m)
}
func (m *FailedUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FailedUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FailedUserInfo proto.InternalMessageInfo

func (m *FailedUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FailedUserInfo) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type BatchGrantDecorationToUserReq struct {
	// 批量发放不用填uid和ttid 填在后面的list里
	UserDecoration       []*UserDecoration `protobuf:"bytes,1,rep,name=user_decoration,json=userDecoration,proto3" json:"user_decoration,omitempty"`
	AddTtlForExisting    bool              `protobuf:"varint,2,opt,name=add_ttl_for_existing,json=addTtlForExisting,proto3" json:"add_ttl_for_existing,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGrantDecorationToUserReq) Reset()         { *m = BatchGrantDecorationToUserReq{} }
func (m *BatchGrantDecorationToUserReq) String() string { return proto.CompactTextString(m) }
func (*BatchGrantDecorationToUserReq) ProtoMessage()    {}
func (*BatchGrantDecorationToUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{25}
}
func (m *BatchGrantDecorationToUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGrantDecorationToUserReq.Unmarshal(m, b)
}
func (m *BatchGrantDecorationToUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGrantDecorationToUserReq.Marshal(b, m, deterministic)
}
func (dst *BatchGrantDecorationToUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGrantDecorationToUserReq.Merge(dst, src)
}
func (m *BatchGrantDecorationToUserReq) XXX_Size() int {
	return xxx_messageInfo_BatchGrantDecorationToUserReq.Size(m)
}
func (m *BatchGrantDecorationToUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGrantDecorationToUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGrantDecorationToUserReq proto.InternalMessageInfo

func (m *BatchGrantDecorationToUserReq) GetUserDecoration() []*UserDecoration {
	if m != nil {
		return m.UserDecoration
	}
	return nil
}

func (m *BatchGrantDecorationToUserReq) GetAddTtlForExisting() bool {
	if m != nil {
		return m.AddTtlForExisting
	}
	return false
}

type BatchGrantDecorationToUserResp struct {
	FailList             []*FailedUserInfo `protobuf:"bytes,1,rep,name=fail_list,json=failList,proto3" json:"fail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGrantDecorationToUserResp) Reset()         { *m = BatchGrantDecorationToUserResp{} }
func (m *BatchGrantDecorationToUserResp) String() string { return proto.CompactTextString(m) }
func (*BatchGrantDecorationToUserResp) ProtoMessage()    {}
func (*BatchGrantDecorationToUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{26}
}
func (m *BatchGrantDecorationToUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGrantDecorationToUserResp.Unmarshal(m, b)
}
func (m *BatchGrantDecorationToUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGrantDecorationToUserResp.Marshal(b, m, deterministic)
}
func (dst *BatchGrantDecorationToUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGrantDecorationToUserResp.Merge(dst, src)
}
func (m *BatchGrantDecorationToUserResp) XXX_Size() int {
	return xxx_messageInfo_BatchGrantDecorationToUserResp.Size(m)
}
func (m *BatchGrantDecorationToUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGrantDecorationToUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGrantDecorationToUserResp proto.InternalMessageInfo

func (m *BatchGrantDecorationToUserResp) GetFailList() []*FailedUserInfo {
	if m != nil {
		return m.FailList
	}
	return nil
}

type ChangeDecorationCustomTextReq struct {
	Uid                  uint32                                       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DecorationType       ChangeDecorationCustomTextReq_DecorationType `protobuf:"varint,2,opt,name=decoration_type,json=decorationType,proto3,enum=channel.personalization.ChangeDecorationCustomTextReq_DecorationType" json:"decoration_type,omitempty"`
	DecorationId         string                                       `protobuf:"bytes,3,opt,name=decoration_id,json=decorationId,proto3" json:"decoration_id,omitempty"`
	Version              string                                       `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	CustomText           string                                       `protobuf:"bytes,5,opt,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	NewCustomText        string                                       `protobuf:"bytes,6,opt,name=new_custom_text,json=newCustomText,proto3" json:"new_custom_text,omitempty"`
	CpUid                uint32                                       `protobuf:"varint,7,opt,name=cp_uid,json=cpUid,proto3" json:"cp_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *ChangeDecorationCustomTextReq) Reset()         { *m = ChangeDecorationCustomTextReq{} }
func (m *ChangeDecorationCustomTextReq) String() string { return proto.CompactTextString(m) }
func (*ChangeDecorationCustomTextReq) ProtoMessage()    {}
func (*ChangeDecorationCustomTextReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{27}
}
func (m *ChangeDecorationCustomTextReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeDecorationCustomTextReq.Unmarshal(m, b)
}
func (m *ChangeDecorationCustomTextReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeDecorationCustomTextReq.Marshal(b, m, deterministic)
}
func (dst *ChangeDecorationCustomTextReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDecorationCustomTextReq.Merge(dst, src)
}
func (m *ChangeDecorationCustomTextReq) XXX_Size() int {
	return xxx_messageInfo_ChangeDecorationCustomTextReq.Size(m)
}
func (m *ChangeDecorationCustomTextReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDecorationCustomTextReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDecorationCustomTextReq proto.InternalMessageInfo

func (m *ChangeDecorationCustomTextReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeDecorationCustomTextReq) GetDecorationType() ChangeDecorationCustomTextReq_DecorationType {
	if m != nil {
		return m.DecorationType
	}
	return ChangeDecorationCustomTextReq_Unknown
}

func (m *ChangeDecorationCustomTextReq) GetDecorationId() string {
	if m != nil {
		return m.DecorationId
	}
	return ""
}

func (m *ChangeDecorationCustomTextReq) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *ChangeDecorationCustomTextReq) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

func (m *ChangeDecorationCustomTextReq) GetNewCustomText() string {
	if m != nil {
		return m.NewCustomText
	}
	return ""
}

func (m *ChangeDecorationCustomTextReq) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

type ChangeDecorationCustomTextResp struct {
	ExtendJson           string   `protobuf:"bytes,1,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeDecorationCustomTextResp) Reset()         { *m = ChangeDecorationCustomTextResp{} }
func (m *ChangeDecorationCustomTextResp) String() string { return proto.CompactTextString(m) }
func (*ChangeDecorationCustomTextResp) ProtoMessage()    {}
func (*ChangeDecorationCustomTextResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{28}
}
func (m *ChangeDecorationCustomTextResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeDecorationCustomTextResp.Unmarshal(m, b)
}
func (m *ChangeDecorationCustomTextResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeDecorationCustomTextResp.Marshal(b, m, deterministic)
}
func (dst *ChangeDecorationCustomTextResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDecorationCustomTextResp.Merge(dst, src)
}
func (m *ChangeDecorationCustomTextResp) XXX_Size() int {
	return xxx_messageInfo_ChangeDecorationCustomTextResp.Size(m)
}
func (m *ChangeDecorationCustomTextResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDecorationCustomTextResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDecorationCustomTextResp proto.InternalMessageInfo

func (m *ChangeDecorationCustomTextResp) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

// 自动取对应id最新的版本
type GetChannelEnterSpecialEffectConfigByIdReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelEnterSpecialEffectConfigByIdReq) Reset() {
	*m = GetChannelEnterSpecialEffectConfigByIdReq{}
}
func (m *GetChannelEnterSpecialEffectConfigByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelEnterSpecialEffectConfigByIdReq) ProtoMessage()    {}
func (*GetChannelEnterSpecialEffectConfigByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{29}
}
func (m *GetChannelEnterSpecialEffectConfigByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdReq.Unmarshal(m, b)
}
func (m *GetChannelEnterSpecialEffectConfigByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelEnterSpecialEffectConfigByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdReq.Merge(dst, src)
}
func (m *GetChannelEnterSpecialEffectConfigByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdReq.Size(m)
}
func (m *GetChannelEnterSpecialEffectConfigByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdReq proto.InternalMessageInfo

func (m *GetChannelEnterSpecialEffectConfigByIdReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetChannelEnterSpecialEffectConfigByIdResp struct {
	Decoration           *Decoration `protobuf:"bytes,1,opt,name=decoration,proto3" json:"decoration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetChannelEnterSpecialEffectConfigByIdResp) Reset() {
	*m = GetChannelEnterSpecialEffectConfigByIdResp{}
}
func (m *GetChannelEnterSpecialEffectConfigByIdResp) String() string {
	return proto.CompactTextString(m)
}
func (*GetChannelEnterSpecialEffectConfigByIdResp) ProtoMessage() {}
func (*GetChannelEnterSpecialEffectConfigByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_personalization_f1abc666c74aea3a, []int{30}
}
func (m *GetChannelEnterSpecialEffectConfigByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdResp.Unmarshal(m, b)
}
func (m *GetChannelEnterSpecialEffectConfigByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelEnterSpecialEffectConfigByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdResp.Merge(dst, src)
}
func (m *GetChannelEnterSpecialEffectConfigByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdResp.Size(m)
}
func (m *GetChannelEnterSpecialEffectConfigByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelEnterSpecialEffectConfigByIdResp proto.InternalMessageInfo

func (m *GetChannelEnterSpecialEffectConfigByIdResp) GetDecoration() *Decoration {
	if m != nil {
		return m.Decoration
	}
	return nil
}

func init() {
	proto.RegisterType((*ChannelEnterSpecialEffect)(nil), "channel.personalization.ChannelEnterSpecialEffect")
	proto.RegisterType((*ChannelEnterSpecialEffectList)(nil), "channel.personalization.ChannelEnterSpecialEffectList")
	proto.RegisterType((*ChannelEnterFunMessage)(nil), "channel.personalization.ChannelEnterFunMessage")
	proto.RegisterType((*Decoration)(nil), "channel.personalization.Decoration")
	proto.RegisterType((*DecorationDetail)(nil), "channel.personalization.DecorationDetail")
	proto.RegisterType((*UserDecoration)(nil), "channel.personalization.UserDecoration")
	proto.RegisterType((*UserInfo)(nil), "channel.personalization.UserInfo")
	proto.RegisterType((*GetUserDecorationsReq)(nil), "channel.personalization.GetUserDecorationsReq")
	proto.RegisterType((*GetUserDecorationsResp)(nil), "channel.personalization.GetUserDecorationsResp")
	proto.RegisterType((*GrantDecorationToUserReq)(nil), "channel.personalization.GrantDecorationToUserReq")
	proto.RegisterType((*GrantDecorationToUserResp)(nil), "channel.personalization.GrantDecorationToUserResp")
	proto.RegisterType((*ActivateUserDecorationReq)(nil), "channel.personalization.ActivateUserDecorationReq")
	proto.RegisterType((*ActivateUserDecorationResp)(nil), "channel.personalization.ActivateUserDecorationResp")
	proto.RegisterType((*AddUserDecorationConfigReq)(nil), "channel.personalization.AddUserDecorationConfigReq")
	proto.RegisterType((*AddUserDecorationConfigResp)(nil), "channel.personalization.AddUserDecorationConfigResp")
	proto.RegisterType((*DelUserDecorationConfigReq)(nil), "channel.personalization.DelUserDecorationConfigReq")
	proto.RegisterType((*DelUserDecorationConfigResp)(nil), "channel.personalization.DelUserDecorationConfigResp")
	proto.RegisterType((*UpdateUserDecorationConfigReq)(nil), "channel.personalization.UpdateUserDecorationConfigReq")
	proto.RegisterType((*UpdateUserDecorationConfigResp)(nil), "channel.personalization.UpdateUserDecorationConfigResp")
	proto.RegisterType((*RichLevelSpecialEffect)(nil), "channel.personalization.RichLevelSpecialEffect")
	proto.RegisterType((*NobilityLevelSpecialEffect)(nil), "channel.personalization.NobilityLevelSpecialEffect")
	proto.RegisterType((*ChannelEnterSpecialEffectConfig)(nil), "channel.personalization.ChannelEnterSpecialEffectConfig")
	proto.RegisterMapType((map[string]*Decoration)(nil), "channel.personalization.ChannelEnterSpecialEffectConfig.NonRichLevelDecorationsEntry")
	proto.RegisterType((*SetUserDecorationSpecialLevelReq)(nil), "channel.personalization.SetUserDecorationSpecialLevelReq")
	proto.RegisterType((*SetUserDecorationSpecialLevelResp)(nil), "channel.personalization.SetUserDecorationSpecialLevelResp")
	proto.RegisterType((*FailedUserInfo)(nil), "channel.personalization.FailedUserInfo")
	proto.RegisterType((*BatchGrantDecorationToUserReq)(nil), "channel.personalization.BatchGrantDecorationToUserReq")
	proto.RegisterType((*BatchGrantDecorationToUserResp)(nil), "channel.personalization.BatchGrantDecorationToUserResp")
	proto.RegisterType((*ChangeDecorationCustomTextReq)(nil), "channel.personalization.ChangeDecorationCustomTextReq")
	proto.RegisterType((*ChangeDecorationCustomTextResp)(nil), "channel.personalization.ChangeDecorationCustomTextResp")
	proto.RegisterType((*GetChannelEnterSpecialEffectConfigByIdReq)(nil), "channel.personalization.GetChannelEnterSpecialEffectConfigByIdReq")
	proto.RegisterType((*GetChannelEnterSpecialEffectConfigByIdResp)(nil), "channel.personalization.GetChannelEnterSpecialEffectConfigByIdResp")
	proto.RegisterEnum("channel.personalization.DecorationType", DecorationType_name, DecorationType_value)
	proto.RegisterEnum("channel.personalization.EffectType", EffectType_name, EffectType_value)
	proto.RegisterEnum("channel.personalization.EnterEffectType", EnterEffectType_name, EnterEffectType_value)
	proto.RegisterEnum("channel.personalization.EffectCustomType", EffectCustomType_name, EffectCustomType_value)
	proto.RegisterEnum("channel.personalization.EffectSourceType", EffectSourceType_name, EffectSourceType_value)
	proto.RegisterEnum("channel.personalization.EffectSpecialType", EffectSpecialType_name, EffectSpecialType_value)
	proto.RegisterEnum("channel.personalization.UserDecoration_TextStatus", UserDecoration_TextStatus_name, UserDecoration_TextStatus_value)
	proto.RegisterEnum("channel.personalization.ChangeDecorationCustomTextReq_DecorationType", ChangeDecorationCustomTextReq_DecorationType_name, ChangeDecorationCustomTextReq_DecorationType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelPersonalizationClient is the client API for ChannelPersonalization service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelPersonalizationClient interface {
	// Common API for all decoration types
	GetUserDecorations(ctx context.Context, in *GetUserDecorationsReq, opts ...grpc.CallOption) (*GetUserDecorationsResp, error)
	GrantDecorationToUser(ctx context.Context, in *GrantDecorationToUserReq, opts ...grpc.CallOption) (*GrantDecorationToUserResp, error)
	ActivateUserDecoration(ctx context.Context, in *ActivateUserDecorationReq, opts ...grpc.CallOption) (*ActivateUserDecorationResp, error)
	// 添加座骑配置
	AddUserDecorationConfig(ctx context.Context, in *AddUserDecorationConfigReq, opts ...grpc.CallOption) (*AddUserDecorationConfigResp, error)
	// 删除座骑配置
	DelUserDecorationConfig(ctx context.Context, in *DelUserDecorationConfigReq, opts ...grpc.CallOption) (*DelUserDecorationConfigResp, error)
	// 更新座骑配置
	UpdateUserDecorationConfig(ctx context.Context, in *UpdateUserDecorationConfigReq, opts ...grpc.CallOption) (*UpdateUserDecorationConfigResp, error)
	// 获取所有座骑配置
	GetChannelEnterSpecialEffectConfig(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*ChannelEnterSpecialEffectConfig, error)
	// 配置财富/贵族等级特定座骑
	SetUserDecorationSpecialLevel(ctx context.Context, in *SetUserDecorationSpecialLevelReq, opts ...grpc.CallOption) (*SetUserDecorationSpecialLevelResp, error)
	// 发放座骑
	GrantDecorationToUserV2(ctx context.Context, in *GrantDecorationToUserReq, opts ...grpc.CallOption) (*GrantDecorationToUserResp, error)
	// 批量发放座骑
	BatchGrantDecorationToUser(ctx context.Context, in *BatchGrantDecorationToUserReq, opts ...grpc.CallOption) (*BatchGrantDecorationToUserResp, error)
	// 获取单个坐骑配置
	GetChannelEnterSpecialEffectConfigById(ctx context.Context, in *GetChannelEnterSpecialEffectConfigByIdReq, opts ...grpc.CallOption) (*GetChannelEnterSpecialEffectConfigByIdResp, error)
	ChangeDecorationCustomText(ctx context.Context, in *ChangeDecorationCustomTextReq, opts ...grpc.CallOption) (*ChangeDecorationCustomTextResp, error)
	// 获取时间范围内的背包订单数量和物品数量
	GetBackpackItemUseCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 获取时间范围内的背包订单列表
	GetBackpackItemUseOrder(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
}

type channelPersonalizationClient struct {
	cc *grpc.ClientConn
}

func NewChannelPersonalizationClient(cc *grpc.ClientConn) ChannelPersonalizationClient {
	return &channelPersonalizationClient{cc}
}

func (c *channelPersonalizationClient) GetUserDecorations(ctx context.Context, in *GetUserDecorationsReq, opts ...grpc.CallOption) (*GetUserDecorationsResp, error) {
	out := new(GetUserDecorationsResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/GetUserDecorations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) GrantDecorationToUser(ctx context.Context, in *GrantDecorationToUserReq, opts ...grpc.CallOption) (*GrantDecorationToUserResp, error) {
	out := new(GrantDecorationToUserResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/GrantDecorationToUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) ActivateUserDecoration(ctx context.Context, in *ActivateUserDecorationReq, opts ...grpc.CallOption) (*ActivateUserDecorationResp, error) {
	out := new(ActivateUserDecorationResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/ActivateUserDecoration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) AddUserDecorationConfig(ctx context.Context, in *AddUserDecorationConfigReq, opts ...grpc.CallOption) (*AddUserDecorationConfigResp, error) {
	out := new(AddUserDecorationConfigResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/AddUserDecorationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) DelUserDecorationConfig(ctx context.Context, in *DelUserDecorationConfigReq, opts ...grpc.CallOption) (*DelUserDecorationConfigResp, error) {
	out := new(DelUserDecorationConfigResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/DelUserDecorationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) UpdateUserDecorationConfig(ctx context.Context, in *UpdateUserDecorationConfigReq, opts ...grpc.CallOption) (*UpdateUserDecorationConfigResp, error) {
	out := new(UpdateUserDecorationConfigResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/UpdateUserDecorationConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) GetChannelEnterSpecialEffectConfig(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*ChannelEnterSpecialEffectConfig, error) {
	out := new(ChannelEnterSpecialEffectConfig)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/GetChannelEnterSpecialEffectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) SetUserDecorationSpecialLevel(ctx context.Context, in *SetUserDecorationSpecialLevelReq, opts ...grpc.CallOption) (*SetUserDecorationSpecialLevelResp, error) {
	out := new(SetUserDecorationSpecialLevelResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/SetUserDecorationSpecialLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) GrantDecorationToUserV2(ctx context.Context, in *GrantDecorationToUserReq, opts ...grpc.CallOption) (*GrantDecorationToUserResp, error) {
	out := new(GrantDecorationToUserResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/GrantDecorationToUserV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) BatchGrantDecorationToUser(ctx context.Context, in *BatchGrantDecorationToUserReq, opts ...grpc.CallOption) (*BatchGrantDecorationToUserResp, error) {
	out := new(BatchGrantDecorationToUserResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/BatchGrantDecorationToUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) GetChannelEnterSpecialEffectConfigById(ctx context.Context, in *GetChannelEnterSpecialEffectConfigByIdReq, opts ...grpc.CallOption) (*GetChannelEnterSpecialEffectConfigByIdResp, error) {
	out := new(GetChannelEnterSpecialEffectConfigByIdResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/GetChannelEnterSpecialEffectConfigById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) ChangeDecorationCustomText(ctx context.Context, in *ChangeDecorationCustomTextReq, opts ...grpc.CallOption) (*ChangeDecorationCustomTextResp, error) {
	out := new(ChangeDecorationCustomTextResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/ChangeDecorationCustomText", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) GetBackpackItemUseCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/GetBackpackItemUseCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPersonalizationClient) GetBackpackItemUseOrder(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel.personalization.ChannelPersonalization/GetBackpackItemUseOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelPersonalizationServer is the server API for ChannelPersonalization service.
type ChannelPersonalizationServer interface {
	// Common API for all decoration types
	GetUserDecorations(context.Context, *GetUserDecorationsReq) (*GetUserDecorationsResp, error)
	GrantDecorationToUser(context.Context, *GrantDecorationToUserReq) (*GrantDecorationToUserResp, error)
	ActivateUserDecoration(context.Context, *ActivateUserDecorationReq) (*ActivateUserDecorationResp, error)
	// 添加座骑配置
	AddUserDecorationConfig(context.Context, *AddUserDecorationConfigReq) (*AddUserDecorationConfigResp, error)
	// 删除座骑配置
	DelUserDecorationConfig(context.Context, *DelUserDecorationConfigReq) (*DelUserDecorationConfigResp, error)
	// 更新座骑配置
	UpdateUserDecorationConfig(context.Context, *UpdateUserDecorationConfigReq) (*UpdateUserDecorationConfigResp, error)
	// 获取所有座骑配置
	GetChannelEnterSpecialEffectConfig(context.Context, *empty.Empty) (*ChannelEnterSpecialEffectConfig, error)
	// 配置财富/贵族等级特定座骑
	SetUserDecorationSpecialLevel(context.Context, *SetUserDecorationSpecialLevelReq) (*SetUserDecorationSpecialLevelResp, error)
	// 发放座骑
	GrantDecorationToUserV2(context.Context, *GrantDecorationToUserReq) (*GrantDecorationToUserResp, error)
	// 批量发放座骑
	BatchGrantDecorationToUser(context.Context, *BatchGrantDecorationToUserReq) (*BatchGrantDecorationToUserResp, error)
	// 获取单个坐骑配置
	GetChannelEnterSpecialEffectConfigById(context.Context, *GetChannelEnterSpecialEffectConfigByIdReq) (*GetChannelEnterSpecialEffectConfigByIdResp, error)
	ChangeDecorationCustomText(context.Context, *ChangeDecorationCustomTextReq) (*ChangeDecorationCustomTextResp, error)
	// 获取时间范围内的背包订单数量和物品数量
	GetBackpackItemUseCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 获取时间范围内的背包订单列表
	GetBackpackItemUseOrder(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
}

func RegisterChannelPersonalizationServer(s *grpc.Server, srv ChannelPersonalizationServer) {
	s.RegisterService(&_ChannelPersonalization_serviceDesc, srv)
}

func _ChannelPersonalization_GetUserDecorations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDecorationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).GetUserDecorations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/GetUserDecorations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).GetUserDecorations(ctx, req.(*GetUserDecorationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_GrantDecorationToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantDecorationToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).GrantDecorationToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/GrantDecorationToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).GrantDecorationToUser(ctx, req.(*GrantDecorationToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_ActivateUserDecoration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateUserDecorationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).ActivateUserDecoration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/ActivateUserDecoration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).ActivateUserDecoration(ctx, req.(*ActivateUserDecorationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_AddUserDecorationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserDecorationConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).AddUserDecorationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/AddUserDecorationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).AddUserDecorationConfig(ctx, req.(*AddUserDecorationConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_DelUserDecorationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserDecorationConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).DelUserDecorationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/DelUserDecorationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).DelUserDecorationConfig(ctx, req.(*DelUserDecorationConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_UpdateUserDecorationConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserDecorationConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).UpdateUserDecorationConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/UpdateUserDecorationConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).UpdateUserDecorationConfig(ctx, req.(*UpdateUserDecorationConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_GetChannelEnterSpecialEffectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(empty.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).GetChannelEnterSpecialEffectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/GetChannelEnterSpecialEffectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).GetChannelEnterSpecialEffectConfig(ctx, req.(*empty.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_SetUserDecorationSpecialLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserDecorationSpecialLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).SetUserDecorationSpecialLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/SetUserDecorationSpecialLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).SetUserDecorationSpecialLevel(ctx, req.(*SetUserDecorationSpecialLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_GrantDecorationToUserV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantDecorationToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).GrantDecorationToUserV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/GrantDecorationToUserV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).GrantDecorationToUserV2(ctx, req.(*GrantDecorationToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_BatchGrantDecorationToUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGrantDecorationToUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).BatchGrantDecorationToUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/BatchGrantDecorationToUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).BatchGrantDecorationToUser(ctx, req.(*BatchGrantDecorationToUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_GetChannelEnterSpecialEffectConfigById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelEnterSpecialEffectConfigByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).GetChannelEnterSpecialEffectConfigById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/GetChannelEnterSpecialEffectConfigById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).GetChannelEnterSpecialEffectConfigById(ctx, req.(*GetChannelEnterSpecialEffectConfigByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_ChangeDecorationCustomText_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeDecorationCustomTextReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).ChangeDecorationCustomText(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/ChangeDecorationCustomText",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).ChangeDecorationCustomText(ctx, req.(*ChangeDecorationCustomTextReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_GetBackpackItemUseCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).GetBackpackItemUseCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/GetBackpackItemUseCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).GetBackpackItemUseCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPersonalization_GetBackpackItemUseOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPersonalizationServer).GetBackpackItemUseOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.personalization.ChannelPersonalization/GetBackpackItemUseOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPersonalizationServer).GetBackpackItemUseOrder(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelPersonalization_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel.personalization.ChannelPersonalization",
	HandlerType: (*ChannelPersonalizationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserDecorations",
			Handler:    _ChannelPersonalization_GetUserDecorations_Handler,
		},
		{
			MethodName: "GrantDecorationToUser",
			Handler:    _ChannelPersonalization_GrantDecorationToUser_Handler,
		},
		{
			MethodName: "ActivateUserDecoration",
			Handler:    _ChannelPersonalization_ActivateUserDecoration_Handler,
		},
		{
			MethodName: "AddUserDecorationConfig",
			Handler:    _ChannelPersonalization_AddUserDecorationConfig_Handler,
		},
		{
			MethodName: "DelUserDecorationConfig",
			Handler:    _ChannelPersonalization_DelUserDecorationConfig_Handler,
		},
		{
			MethodName: "UpdateUserDecorationConfig",
			Handler:    _ChannelPersonalization_UpdateUserDecorationConfig_Handler,
		},
		{
			MethodName: "GetChannelEnterSpecialEffectConfig",
			Handler:    _ChannelPersonalization_GetChannelEnterSpecialEffectConfig_Handler,
		},
		{
			MethodName: "SetUserDecorationSpecialLevel",
			Handler:    _ChannelPersonalization_SetUserDecorationSpecialLevel_Handler,
		},
		{
			MethodName: "GrantDecorationToUserV2",
			Handler:    _ChannelPersonalization_GrantDecorationToUserV2_Handler,
		},
		{
			MethodName: "BatchGrantDecorationToUser",
			Handler:    _ChannelPersonalization_BatchGrantDecorationToUser_Handler,
		},
		{
			MethodName: "GetChannelEnterSpecialEffectConfigById",
			Handler:    _ChannelPersonalization_GetChannelEnterSpecialEffectConfigById_Handler,
		},
		{
			MethodName: "ChangeDecorationCustomText",
			Handler:    _ChannelPersonalization_ChangeDecorationCustomText_Handler,
		},
		{
			MethodName: "GetBackpackItemUseCount",
			Handler:    _ChannelPersonalization_GetBackpackItemUseCount_Handler,
		},
		{
			MethodName: "GetBackpackItemUseOrder",
			Handler:    _ChannelPersonalization_GetBackpackItemUseOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channelpersonalization/channel_personalization.proto",
}

func init() {
	proto.RegisterFile("channelpersonalization/channel_personalization.proto", fileDescriptor_channel_personalization_f1abc666c74aea3a)
}

var fileDescriptor_channel_personalization_f1abc666c74aea3a = []byte{
	// 2318 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x19, 0x4d, 0x6f, 0xdb, 0xc8,
	0xd5, 0x94, 0xe4, 0x0f, 0x3d, 0xd9, 0x12, 0x3d, 0xbb, 0xb1, 0x65, 0x39, 0x8e, 0x1d, 0x06, 0xd9,
	0x38, 0x06, 0x2a, 0xb7, 0x4a, 0xd0, 0xee, 0x66, 0x81, 0x02, 0xb2, 0x4c, 0x3b, 0x0a, 0x14, 0xd9,
	0xa5, 0xa4, 0x14, 0xc5, 0x02, 0x25, 0x18, 0x72, 0xe4, 0x70, 0x23, 0x91, 0x0c, 0x67, 0xe4, 0xd8,
	0x6d, 0xd1, 0x76, 0x81, 0x5e, 0x7a, 0xe8, 0xa5, 0x87, 0xfe, 0x80, 0x1e, 0xdb, 0x53, 0xef, 0xbd,
	0xf7, 0x77, 0x14, 0x68, 0x7b, 0x6a, 0x4f, 0xfd, 0x03, 0xc5, 0x7c, 0x48, 0x16, 0x25, 0x51, 0xb2,
	0xbd, 0x69, 0x6e, 0x9c, 0x37, 0xef, 0x6b, 0xde, 0xbc, 0xcf, 0x21, 0x3c, 0xb5, 0xdf, 0x58, 0x9e,
	0x87, 0x3b, 0x01, 0x0e, 0x89, 0xef, 0x59, 0x1d, 0xf7, 0x67, 0x16, 0x75, 0x7d, 0x6f, 0x5f, 0x82,
	0xcd, 0x11, 0x78, 0x31, 0x08, 0x7d, 0xea, 0xa3, 0x75, 0xb9, 0x5d, 0x1c, 0xd9, 0x2e, 0x6c, 0x9e,
	0xf9, 0xfe, 0x59, 0x07, 0xef, 0x73, 0xb4, 0xd7, 0xbd, 0xf6, 0x3e, 0xee, 0x06, 0xf4, 0x52, 0x50,
	0x15, 0xb6, 0x43, 0x6c, 0xfb, 0x9e, 0xed, 0x76, 0xf0, 0x77, 0xce, 0x4b, 0xfb, 0xc3, 0x0b, 0x81,
	0xa0, 0xfd, 0x4d, 0x81, 0x8d, 0x8a, 0xe0, 0xac, 0x7b, 0x14, 0x87, 0x8d, 0x00, 0xdb, 0xae, 0xd5,
	0xd1, 0xdb, 0x6d, 0x6c, 0x53, 0x84, 0x20, 0xe5, 0x59, 0x5d, 0x9c, 0x57, 0x76, 0x94, 0xdd, 0xb4,
	0xc1, 0xbf, 0x19, 0xcc, 0xc1, 0xc4, 0xce, 0x27, 0x04, 0x8c, 0x7d, 0xa3, 0x6d, 0xc8, 0x04, 0x21,
	0x3e, 0x77, 0xf1, 0x7b, 0xb3, 0x17, 0x76, 0xf2, 0x49, 0xbe, 0x05, 0x12, 0xd4, 0x0a, 0x3b, 0x68,
	0x13, 0xd2, 0x5d, 0xd7, 0x33, 0x3b, 0xf8, 0x1c, 0x77, 0xf2, 0xa9, 0x1d, 0x65, 0x77, 0xc5, 0x58,
	0xea, 0xba, 0x5e, 0x8d, 0xad, 0xd1, 0x21, 0x64, 0x30, 0x97, 0x67, 0xd2, 0xcb, 0x00, 0xe7, 0xe7,
	0x77, 0x94, 0xdd, 0x6c, 0xe9, 0x41, 0x31, 0xe6, 0xc0, 0x45, 0xa1, 0x5b, 0xf3, 0x32, 0xc0, 0x06,
	0xe0, 0xc1, 0xb7, 0xf6, 0x0b, 0xd8, 0x8a, 0x3d, 0x48, 0xcd, 0x25, 0x14, 0x7d, 0x05, 0x39, 0x22,
	0x80, 0xa6, 0x20, 0x23, 0x79, 0x65, 0x27, 0xb9, 0x9b, 0x29, 0x95, 0x62, 0x45, 0xc5, 0x32, 0x34,
	0xb2, 0x64, 0x78, 0x49, 0xb4, 0x13, 0x58, 0x1b, 0x46, 0x3e, 0xea, 0x79, 0x2f, 0x31, 0x21, 0xd6,
	0x19, 0x46, 0x6b, 0xb0, 0x10, 0x5a, 0x9e, 0xe3, 0x77, 0xb9, 0x15, 0x97, 0x0c, 0xb9, 0x42, 0x5b,
	0x00, 0x6d, 0xf7, 0x02, 0x3b, 0x26, 0xc5, 0x17, 0x54, 0x5a, 0x33, 0xcd, 0x21, 0x4d, 0x7c, 0x41,
	0xb5, 0x7f, 0xcf, 0x03, 0x1c, 0x62, 0xdb, 0x0f, 0xb9, 0x26, 0x28, 0x0b, 0x09, 0xd7, 0x91, 0xf7,
	0x90, 0x70, 0x1d, 0xf4, 0x25, 0xa4, 0xb8, 0xb1, 0x12, 0xdc, 0x58, 0x8f, 0x62, 0x4f, 0x70, 0xc5,
	0x82, 0x1b, 0x8c, 0x13, 0x21, 0x15, 0x92, 0xe7, 0x38, 0x94, 0xd7, 0xc4, 0x3e, 0x51, 0x19, 0x16,
	0x1c, 0x4c, 0x2d, 0x57, 0x5c, 0x4e, 0xa6, 0xf4, 0xf8, 0x1a, 0x0c, 0x0f, 0x39, 0x81, 0x21, 0x09,
	0xd1, 0x03, 0x58, 0xf1, 0x03, 0x1c, 0x5a, 0xd4, 0x0f, 0x4d, 0xd7, 0x6b, 0xfb, 0xfc, 0x1e, 0xd3,
	0xc6, 0x72, 0x1f, 0x58, 0xf5, 0xda, 0x3e, 0x33, 0x86, 0xe7, 0x53, 0xd7, 0xc6, 0xf9, 0x05, 0xbe,
	0x2b, 0x57, 0xcc, 0x18, 0xd6, 0x7b, 0x2b, 0x74, 0x4c, 0xea, 0x76, 0x71, 0x7e, 0x91, 0x3b, 0x48,
	0x9a, 0x43, 0x9a, 0x6e, 0x17, 0xa3, 0x26, 0xac, 0x62, 0x66, 0x56, 0x73, 0xd8, 0x4f, 0x96, 0xf8,
	0xd1, 0x77, 0xe3, 0xfd, 0x84, 0x51, 0x0c, 0x39, 0x4b, 0x0e, 0x47, 0x01, 0x4c, 0x28, 0xf1, 0x7b,
	0xa1, 0x8d, 0xb9, 0xd3, 0xa6, 0xc5, 0x0d, 0x08, 0x08, 0xf3, 0xd9, 0x11, 0xa7, 0x86, 0x31, 0xa7,
	0xde, 0x86, 0x8c, 0x1d, 0x62, 0x8b, 0x62, 0xa1, 0x75, 0x86, 0x6b, 0x0d, 0x02, 0xc4, 0xd5, 0xde,
	0x86, 0x4c, 0x2f, 0x70, 0x06, 0x08, 0xcb, 0x02, 0x41, 0x80, 0x38, 0xc2, 0x0b, 0xc8, 0xd8, 0x3d,
	0x42, 0xfd, 0xae, 0x38, 0xd1, 0x0a, 0x3f, 0xd1, 0xe3, 0x19, 0x9e, 0x5f, 0xe1, 0x14, 0xc2, 0xff,
	0xed, 0xc1, 0x37, 0xba, 0x0f, 0xcb, 0x92, 0x97, 0xed, 0x77, 0xfc, 0x30, 0x9f, 0xe5, 0xfa, 0x4a,
	0xfe, 0x15, 0x06, 0x62, 0xe2, 0xe4, 0x81, 0xb9, 0xb8, 0xdc, 0xb5, 0xc4, 0x35, 0x38, 0x85, 0x10,
	0x47, 0x06, 0xdf, 0xe8, 0x25, 0x2c, 0xf7, 0xa3, 0x89, 0x33, 0x53, 0x39, 0xb3, 0xbd, 0x59, 0xcc,
	0x04, 0x09, 0xe7, 0x96, 0x21, 0x57, 0x0b, 0xed, 0xbf, 0x0a, 0xa8, 0xa3, 0xae, 0x85, 0x08, 0xdc,
	0xed, 0x27, 0x45, 0x71, 0xfd, 0xd1, 0xf8, 0xe5, 0xe1, 0x70, 0xbb, 0xf0, 0xdd, 0xb0, 0x63, 0x73,
	0xde, 0xd7, 0xb0, 0x11, 0x15, 0xda, 0xee, 0x79, 0x66, 0x57, 0x04, 0x33, 0x0f, 0xb7, 0x4c, 0x69,
	0xff, 0x5a, 0x12, 0xaf, 0x72, 0x80, 0xb1, 0x66, 0x4f, 0x84, 0x6b, 0x7f, 0x9a, 0x87, 0x6c, 0x8b,
	0xe0, 0x70, 0x28, 0xd0, 0x55, 0x48, 0xf6, 0x64, 0xa4, 0xaf, 0x18, 0xec, 0x13, 0x55, 0x00, 0x9c,
	0xc1, 0xbe, 0xd4, 0xe0, 0xc1, 0x35, 0xe2, 0xd3, 0x18, 0x22, 0x63, 0xde, 0x21, 0x63, 0xe7, 0x35,
	0x3e, 0x73, 0x3d, 0x1e, 0xfb, 0x29, 0x43, 0xe6, 0xdd, 0x03, 0x06, 0x62, 0xe1, 0x20, 0x51, 0xb0,
	0xe7, 0xf0, 0x3c, 0x90, 0x32, 0xd2, 0x02, 0xa2, 0x7b, 0x0e, 0xda, 0x80, 0xa5, 0xb3, 0xd0, 0xf2,
	0xa8, 0x69, 0x51, 0x1e, 0xda, 0x29, 0x63, 0x91, 0xaf, 0xcb, 0x94, 0x31, 0x17, 0x5b, 0x21, 0xb6,
	0x88, 0xef, 0xc9, 0xd8, 0xce, 0x70, 0x98, 0xc1, 0x41, 0xe8, 0x21, 0x64, 0x05, 0x4a, 0x3f, 0x1d,
	0xf0, 0x20, 0x4f, 0x1b, 0x2b, 0x1c, 0x7a, 0x22, 0x81, 0x28, 0x0f, 0x8b, 0x96, 0x4d, 0xdd, 0x73,
	0xec, 0xf0, 0xf0, 0x5e, 0x32, 0xfa, 0x4b, 0x26, 0xde, 0x0f, 0x1d, 0x1c, 0x9a, 0xae, 0x23, 0x43,
	0x75, 0x91, 0xaf, 0xab, 0x0e, 0x8f, 0x43, 0x19, 0x45, 0x2c, 0x95, 0xca, 0x40, 0x95, 0xa1, 0x81,
	0x2f, 0x28, 0x43, 0x68, 0xf7, 0x88, 0xeb, 0x7b, 0x26, 0xa5, 0xae, 0xc3, 0x03, 0x35, 0x6d, 0x80,
	0x00, 0x35, 0xa9, 0xcb, 0x39, 0xe0, 0x0b, 0x8a, 0x3d, 0xc7, 0xfc, 0x9a, 0xe9, 0xbf, 0x2c, 0x10,
	0x04, 0xe8, 0x05, 0x53, 0xbf, 0x01, 0x19, 0xc6, 0xdb, 0x24, 0xd4, 0xa2, 0x3d, 0x22, 0x03, 0x35,
	0xde, 0xf1, 0xa2, 0x77, 0x5a, 0x64, 0x4a, 0x34, 0x38, 0xa5, 0x01, 0x74, 0xf0, 0x8d, 0x76, 0x41,
	0x25, 0x6f, 0xfc, 0xf7, 0xe6, 0xb0, 0xf2, 0x22, 0x6a, 0xb3, 0x0c, 0x5e, 0xb9, 0x3a, 0x40, 0x11,
	0x3e, 0x09, 0x71, 0xd7, 0x72, 0x3d, 0x93, 0x49, 0x3c, 0xc3, 0xa6, 0xed, 0xf7, 0x3c, 0xca, 0x03,
	0x78, 0xc5, 0x58, 0x15, 0x5b, 0x15, 0xbe, 0x53, 0x61, 0x1b, 0xe8, 0x11, 0xe4, 0xe4, 0x81, 0x3d,
	0xd7, 0x7e, 0xcb, 0x4b, 0xb8, 0x2a, 0x18, 0x0b, 0x70, 0x5d, 0x42, 0xb5, 0x3d, 0x80, 0x2b, 0xe5,
	0x50, 0x06, 0x16, 0x0f, 0x71, 0xdb, 0xea, 0x75, 0xa8, 0x3a, 0x87, 0x72, 0x90, 0x69, 0x79, 0x0e,
	0x0e, 0x0d, 0x9e, 0xef, 0x54, 0x45, 0x7b, 0x0d, 0x4b, 0xec, 0x5c, 0x3c, 0x8f, 0x8f, 0x7b, 0xe9,
	0x16, 0x40, 0xe8, 0xda, 0x6f, 0x64, 0x89, 0x4f, 0x88, 0x0c, 0xce, 0x20, 0xa2, 0xc6, 0x3f, 0x84,
	0xac, 0xe7, 0xbf, 0x76, 0x3b, 0x2e, 0xbd, 0x94, 0x28, 0x49, 0x8e, 0xb2, 0xd2, 0x87, 0x72, 0x34,
	0xed, 0x9b, 0x04, 0xdc, 0x39, 0xc6, 0x34, 0x6a, 0x3f, 0x62, 0xe0, 0x77, 0xe8, 0x87, 0x90, 0xee,
	0x11, 0x2c, 0x4b, 0x8b, 0x08, 0xfc, 0xfb, 0x53, 0xed, 0xcf, 0xf4, 0x34, 0x96, 0x7a, 0x7d, 0x8d,
	0x1f, 0x42, 0xd6, 0xf7, 0x3a, 0x97, 0x32, 0x75, 0xb8, 0xe7, 0x22, 0x96, 0x97, 0x8c, 0x15, 0x06,
	0xd5, 0xfb, 0x40, 0x74, 0x0a, 0xb9, 0xab, 0xa8, 0x11, 0x99, 0x2d, 0x79, 0xb3, 0x12, 0x9b, 0x75,
	0x22, 0x6b, 0xb4, 0x0f, 0x9f, 0x7a, 0x18, 0x3b, 0x66, 0x8f, 0x19, 0xd3, 0xe4, 0xe5, 0x84, 0x9f,
	0x21, 0xc5, 0xc5, 0xaf, 0xb2, 0x3d, 0x6e, 0xe7, 0x57, 0x2e, 0x7e, 0xcf, 0x34, 0xd5, 0x3a, 0xb0,
	0x36, 0xc9, 0x04, 0x24, 0x40, 0x06, 0xa8, 0xdc, 0x06, 0x57, 0x12, 0xfa, 0x2d, 0xcc, 0xa3, 0x6b,
	0xba, 0xa2, 0x91, 0xeb, 0x45, 0xf9, 0x6a, 0x7f, 0x57, 0x20, 0x7f, 0xcc, 0x62, 0x70, 0xe8, 0x18,
	0x3e, 0x23, 0x62, 0x46, 0x3f, 0x85, 0xdc, 0x88, 0x40, 0x69, 0xfa, 0x6b, 0xcb, 0xcb, 0x46, 0xe5,
	0x31, 0x6b, 0x58, 0x8e, 0x63, 0x52, 0xda, 0x31, 0xdb, 0x7e, 0x68, 0xe2, 0x0b, 0x97, 0x50, 0xd7,
	0x3b, 0x93, 0x97, 0xb1, 0x6a, 0x39, 0x4e, 0x93, 0x76, 0x8e, 0xfc, 0x50, 0x97, 0x1b, 0x2c, 0xb7,
	0xf8, 0x3d, 0x4a, 0x5c, 0x47, 0x16, 0x51, 0x76, 0x1b, 0x49, 0x23, 0x23, 0x61, 0xfd, 0x32, 0x3b,
	0x5c, 0xd6, 0x44, 0x7b, 0x39, 0x54, 0xab, 0xb4, 0x4d, 0xd8, 0x88, 0x39, 0x22, 0x09, 0xb4, 0x7f,
	0x29, 0xb0, 0x51, 0x66, 0x97, 0x6f, 0x51, 0x3c, 0xa2, 0x3c, 0x7e, 0x37, 0xc1, 0xd1, 0x27, 0x78,
	0x48, 0xe2, 0xdb, 0x79, 0xc8, 0x03, 0x58, 0x19, 0xe2, 0xe8, 0x3a, 0xb2, 0x31, 0x5b, 0xbe, 0x02,
	0x8e, 0x27, 0xb9, 0xd4, 0xac, 0x24, 0x37, 0x3f, 0x9a, 0xe4, 0xb4, 0xbb, 0x50, 0x88, 0x3b, 0x27,
	0x09, 0x34, 0x0b, 0x0a, 0x65, 0xc7, 0x89, 0x6e, 0x54, 0x7c, 0xaf, 0xed, 0x9e, 0x31, 0x33, 0x44,
	0x6b, 0x90, 0x72, 0xab, 0x1a, 0xa4, 0x6d, 0xc1, 0x66, 0xac, 0x08, 0x12, 0x68, 0x7f, 0x56, 0xa0,
	0x70, 0x88, 0x3b, 0x71, 0x2a, 0x7c, 0xd0, 0x0e, 0x78, 0xc4, 0x9a, 0xc9, 0x59, 0xd6, 0x4c, 0x8d,
	0x59, 0x73, 0x0b, 0x36, 0x63, 0x95, 0x25, 0x81, 0xe6, 0xc0, 0x56, 0x8b, 0xf7, 0x79, 0xff, 0x57,
	0x8b, 0xee, 0xc0, 0xbd, 0x69, 0x52, 0x48, 0xa0, 0xfd, 0x1c, 0xd6, 0x8c, 0x7e, 0x12, 0x8e, 0xf6,
	0x39, 0x91, 0x91, 0x4c, 0x89, 0x1d, 0xc9, 0x78, 0xae, 0xba, 0x49, 0xd3, 0x21, 0xe8, 0x78, 0x26,
	0xfb, 0x15, 0x14, 0xea, 0xc3, 0xe9, 0xfd, 0xa3, 0x2b, 0xf0, 0x9f, 0x24, 0x6c, 0xc7, 0x36, 0x81,
	0xc2, 0x4a, 0xe8, 0x2b, 0x58, 0xbd, 0x2a, 0x5c, 0xa6, 0xcd, 0x81, 0x32, 0xab, 0xc6, 0xf7, 0x79,
	0x93, 0x6d, 0x6a, 0xe4, 0x06, 0x05, 0x4f, 0x32, 0xff, 0xbd, 0x02, 0x05, 0xcf, 0xf7, 0xcc, 0x21,
	0x09, 0xc3, 0xc9, 0x3b, 0xc1, 0xc5, 0xb4, 0x6e, 0xde, 0xc0, 0x0a, 0xf6, 0xc5, 0xba, 0xef, 0x0d,
	0x34, 0x19, 0x4a, 0xea, 0xba, 0x47, 0xc3, 0x4b, 0x63, 0xdd, 0x9b, 0xbc, 0x8b, 0xce, 0xe0, 0x4e,
	0xb4, 0x16, 0xf7, 0x4f, 0x9d, 0xe4, 0xea, 0x3c, 0x89, 0x55, 0x27, 0xfe, 0x32, 0x8d, 0x4f, 0x22,
	0x75, 0x5c, 0xa8, 0x57, 0xf0, 0xe1, 0xee, 0x34, 0x0d, 0x59, 0x72, 0x7d, 0x8b, 0x2f, 0x65, 0x4c,
	0xb3, 0x4f, 0xf4, 0x05, 0xcc, 0x9f, 0x5b, 0x9d, 0x1e, 0xbe, 0xc9, 0x85, 0x0b, 0x8a, 0x67, 0x89,
	0xcf, 0x15, 0xed, 0x1f, 0x0a, 0xec, 0x34, 0x46, 0x6b, 0xa7, 0x54, 0x94, 0xab, 0xf0, 0xc1, 0x13,
	0xc9, 0xf8, 0x28, 0x3d, 0xf2, 0x9a, 0x91, 0xba, 0xd5, 0x6b, 0x46, 0x34, 0x38, 0xe6, 0xa3, 0xc1,
	0xa1, 0x3d, 0x80, 0xfb, 0x33, 0x4e, 0x49, 0x02, 0xed, 0x19, 0x64, 0x8f, 0x2c, 0xb7, 0x83, 0x9d,
	0x29, 0x4d, 0xdb, 0x1a, 0x2c, 0xc8, 0x96, 0x5d, 0xbc, 0x3f, 0xc8, 0x95, 0xf6, 0x47, 0x05, 0xb6,
	0x0e, 0x2c, 0x6a, 0xbf, 0xb9, 0x59, 0x67, 0x90, 0xfc, 0x98, 0x9d, 0x81, 0xd6, 0x86, 0x7b, 0xd3,
	0x74, 0x24, 0x01, 0x3a, 0x84, 0x74, 0xdb, 0x72, 0x3b, 0x66, 0xc7, 0x25, 0x74, 0xa6, 0x7a, 0x51,
	0x63, 0x19, 0x4b, 0x8c, 0xb2, 0xe6, 0x12, 0xaa, 0xfd, 0x36, 0x29, 0x5e, 0x96, 0xce, 0xf0, 0x50,
	0x86, 0x1d, 0x54, 0x8a, 0xc9, 0x4d, 0x82, 0x17, 0xd7, 0x24, 0xe8, 0x53, 0x63, 0x3d, 0x56, 0xc4,
	0x07, 0x69, 0x21, 0xf2, 0xb0, 0x78, 0x8e, 0x43, 0x56, 0xc1, 0x64, 0x3d, 0xeb, 0x2f, 0x47, 0xcb,
	0xe1, 0xfc, 0x58, 0x39, 0xfc, 0x0c, 0x72, 0x1e, 0x8e, 0x4e, 0x2a, 0x62, 0xc8, 0x5b, 0xf1, 0xf0,
	0xf0, 0xa0, 0x72, 0x07, 0x16, 0xec, 0xc0, 0x64, 0xc6, 0x10, 0x6f, 0x38, 0xf3, 0x76, 0xd0, 0x72,
	0x1d, 0xed, 0x18, 0xb2, 0xd1, 0x03, 0xb0, 0x51, 0xa3, 0xe5, 0xbd, 0xf5, 0xfc, 0xf7, 0x9e, 0x3a,
	0x87, 0x96, 0x61, 0xe9, 0x39, 0xb6, 0x9c, 0x1f, 0x63, 0x2b, 0x54, 0x15, 0x54, 0x18, 0x3c, 0xa5,
	0x9d, 0x46, 0x4d, 0xa4, 0x26, 0xb4, 0x32, 0xdc, 0x9b, 0x66, 0x27, 0x12, 0x8c, 0x8e, 0x72, 0xca,
	0xe8, 0x28, 0xa7, 0x7d, 0x09, 0x8f, 0x8f, 0x31, 0x9d, 0x91, 0x59, 0x0f, 0x2e, 0xab, 0xce, 0x84,
	0x5c, 0xa1, 0xbd, 0x83, 0xbd, 0xeb, 0x12, 0x93, 0xe0, 0x83, 0xd4, 0xf8, 0xbd, 0x9f, 0x4e, 0xb2,
	0x5d, 0xb5, 0xfe, 0xaa, 0x5c, 0xab, 0x1e, 0xaa, 0x73, 0x68, 0x07, 0xee, 0x56, 0x9e, 0x97, 0xeb,
	0x75, 0xbd, 0x66, 0xea, 0xf5, 0xa6, 0x6e, 0x98, 0x8d, 0x53, 0xbd, 0x52, 0x2d, 0xd7, 0x4c, 0xfd,
	0xe8, 0x48, 0xaf, 0x34, 0x55, 0x05, 0x6d, 0xc1, 0x46, 0x14, 0xe3, 0xa8, 0x55, 0x37, 0x5f, 0xea,
	0x8d, 0x46, 0xf9, 0x58, 0x57, 0x13, 0x7b, 0xdf, 0x05, 0x18, 0x7a, 0x13, 0x03, 0x58, 0xa8, 0x9f,
	0x18, 0x2f, 0xcb, 0x35, 0x75, 0x0e, 0x2d, 0x41, 0xca, 0xa8, 0x56, 0x9e, 0xab, 0x0a, 0xbb, 0xa0,
	0xfa, 0xc9, 0x41, 0xb5, 0x56, 0x6d, 0xfe, 0x44, 0x4d, 0xec, 0x95, 0x20, 0x37, 0xf2, 0xb6, 0x86,
	0x54, 0x58, 0x16, 0xbc, 0x07, 0xc4, 0x2b, 0x90, 0x16, 0x90, 0x53, 0xbd, 0xa9, 0x2a, 0x7b, 0x2f,
	0x40, 0x1d, 0x7d, 0xbd, 0x42, 0xd9, 0xbe, 0xe4, 0xba, 0xef, 0x61, 0x75, 0x0e, 0x7d, 0x3a, 0x82,
	0x83, 0x2f, 0xa8, 0xaa, 0x70, 0xd6, 0x1c, 0x7a, 0xc4, 0x9b, 0x2f, 0x35, 0xb1, 0xf7, 0x4f, 0xa5,
	0x8f, 0x78, 0xf5, 0x36, 0x85, 0xd6, 0x00, 0x89, 0x13, 0x9b, 0x8d, 0x93, 0x96, 0x51, 0xd1, 0xcd,
	0xfa, 0x49, 0x5d, 0x57, 0xe7, 0xd0, 0x5d, 0xc8, 0x8f, 0xc2, 0x99, 0x86, 0xe6, 0xcb, 0xd3, 0xa7,
	0xaa, 0x82, 0xb6, 0x61, 0x73, 0xe2, 0x6e, 0xa3, 0x59, 0x6e, 0x56, 0x2b, 0x6a, 0x22, 0x16, 0xa1,
	0x76, 0xd2, 0x6c, 0x56, 0x75, 0x35, 0x89, 0x36, 0xe0, 0x4e, 0x14, 0xe1, 0x54, 0x6f, 0x72, 0xe6,
	0x29, 0x76, 0x35, 0xe3, 0x5b, 0x47, 0xad, 0x5a, 0xad, 0x51, 0x31, 0x74, 0xbd, 0xae, 0xce, 0x8f,
	0x2b, 0xc7, 0x30, 0x24, 0xeb, 0x85, 0xbd, 0x17, 0xb0, 0x3a, 0xf6, 0x6a, 0x86, 0xd6, 0xe1, 0x93,
	0x3e, 0x89, 0xbc, 0xe8, 0xf1, 0x83, 0xca, 0x8d, 0xd3, 0x5a, 0xb9, 0xa9, 0x9b, 0xcd, 0x93, 0x53,
	0x55, 0x29, 0xfd, 0x35, 0x1b, 0x17, 0x55, 0xa8, 0x07, 0x68, 0x7c, 0xde, 0x44, 0xc5, 0x58, 0x3f,
	0x9d, 0x38, 0x9f, 0x17, 0xf6, 0x6f, 0x84, 0x4f, 0x02, 0xf4, 0x4b, 0xb8, 0x33, 0x31, 0x73, 0xa3,
	0xef, 0xc5, 0x73, 0x8a, 0xa9, 0x46, 0x85, 0xd2, 0x4d, 0x49, 0x48, 0x80, 0xbe, 0x51, 0x60, 0x6d,
	0xf2, 0x3c, 0x84, 0xe2, 0xd9, 0xc5, 0x0e, 0x8a, 0x85, 0x27, 0x37, 0xa6, 0x21, 0x01, 0xfa, 0x8d,
	0x02, 0xeb, 0x31, 0x23, 0x11, 0x9a, 0xc2, 0x30, 0x76, 0x4e, 0x2b, 0x3c, 0xbd, 0x39, 0x91, 0x54,
	0x23, 0x66, 0x98, 0x99, 0xa2, 0x46, 0xfc, 0xac, 0x36, 0x45, 0x8d, 0x29, 0x33, 0x13, 0xfa, 0x9d,
	0x02, 0x85, 0xf8, 0x71, 0x06, 0x7d, 0x3f, 0xbe, 0xb3, 0x98, 0x36, 0x69, 0x15, 0x7e, 0x70, 0x2b,
	0x3a, 0x12, 0xa0, 0x73, 0xd0, 0x66, 0x27, 0x7b, 0xb4, 0x56, 0x14, 0x3f, 0xe0, 0x8a, 0xfd, 0x1f,
	0x70, 0x45, 0xbd, 0x1b, 0xd0, 0xcb, 0xc2, 0xe7, 0xb7, 0xed, 0xea, 0xd1, 0x1f, 0x14, 0xd8, 0x9a,
	0xda, 0xdf, 0xa1, 0x2f, 0x62, 0x79, 0xcf, 0xea, 0x7e, 0x0b, 0xcf, 0x6e, 0x4b, 0x4a, 0x02, 0xf4,
	0x6b, 0x05, 0xd6, 0x27, 0x06, 0xd4, 0xab, 0xd2, 0xc7, 0x8a, 0x5a, 0xe6, 0x23, 0xf1, 0x5d, 0xdf,
	0x14, 0x1f, 0x99, 0xda, 0xce, 0x4e, 0xf1, 0x91, 0x19, 0x2d, 0xe6, 0x5f, 0x14, 0xf8, 0xec, 0x7a,
	0x1d, 0x01, 0x3a, 0x98, 0x96, 0x21, 0xaf, 0xd7, 0x8f, 0x14, 0x2a, 0xdf, 0x9a, 0x87, 0xb4, 0x61,
	0x7c, 0x17, 0x35, 0xc5, 0x86, 0x53, 0x5b, 0xd4, 0x29, 0x36, 0x9c, 0xd1, 0xb2, 0xd5, 0x61, 0xfd,
	0x18, 0xd3, 0x03, 0xcb, 0x7e, 0x1b, 0x58, 0xf6, 0xdb, 0x2a, 0xc5, 0xdd, 0x16, 0x91, 0x0f, 0xd9,
	0x1b, 0x45, 0xa3, 0xff, 0xcf, 0xfa, 0x55, 0xa9, 0xd8, 0x74, 0xbb, 0xd8, 0x60, 0xac, 0x98, 0xb8,
	0xb5, 0xc8, 0x16, 0x47, 0xe7, 0x73, 0xcf, 0x1c, 0xfa, 0xd1, 0x24, 0x7e, 0x27, 0xa1, 0x83, 0xc3,
	0x69, 0xfc, 0xa2, 0x5b, 0x27, 0xe2, 0xdf, 0x02, 0x11, 0x2c, 0x5f, 0x2f, 0xf0, 0xe0, 0x7e, 0xf2,
	0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xa8, 0x84, 0xa8, 0xaf, 0xbc, 0x1f, 0x00, 0x00,
}
