// Code generated by protoc-gen-gogo.
// source: services/giftpkg/giftpkg.proto
// DO NOT EDIT!

/*
	Package Giftpkg is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/giftpkg/giftpkg.proto

	It has these top-level messages:
		UserFetchGiftPkgReq
		UserFetchGiftPkgResp
		UserFetchActivityGiftPkgReq
		UserFetchActivityGiftPkgResp
		CheckActivityGiftPkgFetchStatusReq
		CheckActivityGiftPkgFetchStatusResp
		GetUserGiftPkgSerialReq
		GetUserGiftPkgSerialResp
		StGiftPkg
		StGiftPkgSerial
		GetGiftPkgListReq
		GetGiftPkgListResp
		GetGiftPkgReq
		GetGiftPkgResp
		GetMyGiftPkgSerialListReq
		GetMyGiftPkgSerialListResp
		GuildApplyGiftPkgReq
		GuildApplyGiftPkgResp
		GetGiftPkgApplyListReq
		StGiftPkgApply
		GetGiftPkgApplyListResp
		GetGuildApplyingListReq
		GetGuildApplyingListResp
		TypeGiftPkgReq
		TypeGiftPkgResp
		UpdateGiftPkgReq
		UpdateGiftPkgShowStatusReq
		TypeGiftPkgSerialReq
		TypeGiftPkgSerialResp
		TypeGiftPkgSerialToGuildReq
		TypeGiftPkgSerialToGuildResp
		BatchGetGiftPkgReq
		BatchGetGiftPkgResp
		RedPkgFetchRecord
		StGuildGiftPkg
		GetGuildGiftPkgListReq
		GetGuildGiftPkgListResp
		GetGuildPkgListByGameIdReq
		GetGuildPkgListByGameIdResp
		PassGiftPkgApplyReq
		PassGiftPkgApplyResp
		RejectGiftPkgApplyReq
		RejectGiftPkgApplyResp
		GetGuildPkgStatusReq
		GetGuildPkgStatusResp
		BatchGetGuildPkgStatusReq
		BatchGetGuildPkgStatusResp
		CheckGuildPkgApplyingReq
		CheckGuildPkgApplyingResp
		GetGuildApplyHistoryReq
		GetGuildApplyHistoryResp
		TaohaoReq
		TaohaoResp
		TaohaoAddUsedCountReq
		TaohaoAddUsedCountResp
		CreateRedpkgReq
		CreateRedpkgResp
		FetchRedPkgReq
		FetchRedPkgResp
		GetRedPkgHistoryReq
		GetRedPkgHistoryResp
		GetRedPkgDetailReq
		RedPkgDetail
		GetRedPkgDetailResp
		GiveGiftPkgToGuildReq
		CheckIfFetchRedpkgReq
		CheckIfFetchRedpkgResp
		StGuildApplyTimePkg
		GetApplyingGuildListReq
		GetApplyingGuildListResp
		GetApplyedGuildListReq
		GetApplyedGuildListResp
		StGuildGiftPkgStatInfo
		StGuildGameGiftPkgStatInfo
		GetGuildGiftPkgStatInfoReq
		GetGuildGiftPkgStatInfoResp
		CountRestGiftPkgSerialReq
		CountRestGiftPkgSerialResp
		GetApplyReq
		GetApplyResp
		GetGiftpkgOpHistoryReq
		GiftpkgOpRecord
		GetGiftpkgOpHistoryResp
		GetDeviceLastFetchReq
		GetDeviceLastFetchResp
		GetOfficalLastOpTimeReq
		GetOfficalLastOpTimeResp
		DeleteGuildSourceGiftPkgReq
		DeleteGuildSourceGiftPkgResp
		LockSerialForUserToPayReq
		LockSerialForUserToPayResp
		RealFetchSerialForUserReq
		RealFetchSerialForUserResp
		ReleaseSerialForGuildReq
		ReleaseSerialForGuildResp
		SetGuildPkgRedDiamondPriceReq
		SetGuildPkgRedDiamondPriceResp
		GetUserPkgSerialListNewByIdReq
		GetUserPkgSerialListNewByIdResp
		BatchGetGiftPackageStatInfoReq
		StGuildGameGiftPackageStatInfo
		BatchGetGiftPackageStatInfoResp
		FetchActGiftPkgCollectionReq
		FetchActGiftPkgCollectionResp
		GetActGiftCollcetionFetStatusReq
		GetActGiftCollcetionFetStatusResp
		GetActGiftPkgCollectionConfReq
		GetActGiftPkgCollectionConfResp
*/
package Giftpkg

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type CHANNEL int32

const (
	CHANNEL_UNSPECIFIED CHANNEL = 0
	CHANNEL_UNIVERSAL   CHANNEL = 1
	CHANNEL_BAIDU       CHANNEL = 2
	CHANNEL_QIHOO       CHANNEL = 3
	CHANNEL_SY8         CHANNEL = 4
	CHANNEL_UC          CHANNEL = 5
	CHANNEL_XIAOMI      CHANNEL = 6
)

var CHANNEL_name = map[int32]string{
	0: "UNSPECIFIED",
	1: "UNIVERSAL",
	2: "BAIDU",
	3: "QIHOO",
	4: "SY8",
	5: "UC",
	6: "XIAOMI",
}
var CHANNEL_value = map[string]int32{
	"UNSPECIFIED": 0,
	"UNIVERSAL":   1,
	"BAIDU":       2,
	"QIHOO":       3,
	"SY8":         4,
	"UC":          5,
	"XIAOMI":      6,
}

func (x CHANNEL) Enum() *CHANNEL {
	p := new(CHANNEL)
	*p = x
	return p
}
func (x CHANNEL) String() string {
	return proto.EnumName(CHANNEL_name, int32(x))
}
func (x *CHANNEL) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CHANNEL_value, data, "CHANNEL")
	if err != nil {
		return err
	}
	*x = CHANNEL(value)
	return nil
}
func (CHANNEL) EnumDescriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{0} }

type StGiftPkgSerial_STATUS int32

const (
	StGiftPkgSerial_NONE  StGiftPkgSerial_STATUS = 0
	StGiftPkgSerial_FETCH StGiftPkgSerial_STATUS = 1
	StGiftPkgSerial_RAND  StGiftPkgSerial_STATUS = 2
)

var StGiftPkgSerial_STATUS_name = map[int32]string{
	0: "NONE",
	1: "FETCH",
	2: "RAND",
}
var StGiftPkgSerial_STATUS_value = map[string]int32{
	"NONE":  0,
	"FETCH": 1,
	"RAND":  2,
}

func (x StGiftPkgSerial_STATUS) Enum() *StGiftPkgSerial_STATUS {
	p := new(StGiftPkgSerial_STATUS)
	*p = x
	return p
}
func (x StGiftPkgSerial_STATUS) String() string {
	return proto.EnumName(StGiftPkgSerial_STATUS_name, int32(x))
}
func (x *StGiftPkgSerial_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(StGiftPkgSerial_STATUS_value, data, "StGiftPkgSerial_STATUS")
	if err != nil {
		return err
	}
	*x = StGiftPkgSerial_STATUS(value)
	return nil
}
func (StGiftPkgSerial_STATUS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{9, 0}
}

type StGiftPkgApply_STATUS int32

const (
	StGiftPkgApply_APPLY  StGiftPkgApply_STATUS = 0
	StGiftPkgApply_ACCEPT StGiftPkgApply_STATUS = 1
	StGiftPkgApply_REJECT StGiftPkgApply_STATUS = 2
)

var StGiftPkgApply_STATUS_name = map[int32]string{
	0: "APPLY",
	1: "ACCEPT",
	2: "REJECT",
}
var StGiftPkgApply_STATUS_value = map[string]int32{
	"APPLY":  0,
	"ACCEPT": 1,
	"REJECT": 2,
}

func (x StGiftPkgApply_STATUS) Enum() *StGiftPkgApply_STATUS {
	p := new(StGiftPkgApply_STATUS)
	*p = x
	return p
}
func (x StGiftPkgApply_STATUS) String() string {
	return proto.EnumName(StGiftPkgApply_STATUS_name, int32(x))
}
func (x *StGiftPkgApply_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(StGiftPkgApply_STATUS_value, data, "StGiftPkgApply_STATUS")
	if err != nil {
		return err
	}
	*x = StGiftPkgApply_STATUS(value)
	return nil
}
func (StGiftPkgApply_STATUS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{19, 0}
}

// ================================================================
// 用户获取礼包
// ================================================================
type UserFetchGiftPkgReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GiftPkgId uint32 `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	FetchTime uint32 `protobuf:"varint,3,req,name=fetch_time,json=fetchTime" json:"fetch_time"`
	DeviceId  string `protobuf:"bytes,4,req,name=device_id,json=deviceId" json:"device_id"`
}

func (m *UserFetchGiftPkgReq) Reset()                    { *m = UserFetchGiftPkgReq{} }
func (m *UserFetchGiftPkgReq) String() string            { return proto.CompactTextString(m) }
func (*UserFetchGiftPkgReq) ProtoMessage()               {}
func (*UserFetchGiftPkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{0} }

func (m *UserFetchGiftPkgReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UserFetchGiftPkgReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *UserFetchGiftPkgReq) GetFetchTime() uint32 {
	if m != nil {
		return m.FetchTime
	}
	return 0
}

func (m *UserFetchGiftPkgReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type UserFetchGiftPkgResp struct {
	GiftPkgSerial string `protobuf:"bytes,1,req,name=gift_pkg_serial,json=giftPkgSerial" json:"gift_pkg_serial"`
}

func (m *UserFetchGiftPkgResp) Reset()                    { *m = UserFetchGiftPkgResp{} }
func (m *UserFetchGiftPkgResp) String() string            { return proto.CompactTextString(m) }
func (*UserFetchGiftPkgResp) ProtoMessage()               {}
func (*UserFetchGiftPkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{1} }

func (m *UserFetchGiftPkgResp) GetGiftPkgSerial() string {
	if m != nil {
		return m.GiftPkgSerial
	}
	return ""
}

// 获取活动礼包
type UserFetchActivityGiftPkgReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GiftPkgId   uint32 `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	FetchTime   uint32 `protobuf:"varint,3,req,name=fetch_time,json=fetchTime" json:"fetch_time"`
	DeviceIdHex string `protobuf:"bytes,4,req,name=device_id_hex,json=deviceIdHex" json:"device_id_hex"`
}

func (m *UserFetchActivityGiftPkgReq) Reset()         { *m = UserFetchActivityGiftPkgReq{} }
func (m *UserFetchActivityGiftPkgReq) String() string { return proto.CompactTextString(m) }
func (*UserFetchActivityGiftPkgReq) ProtoMessage()    {}
func (*UserFetchActivityGiftPkgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{2}
}

func (m *UserFetchActivityGiftPkgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserFetchActivityGiftPkgReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *UserFetchActivityGiftPkgReq) GetFetchTime() uint32 {
	if m != nil {
		return m.FetchTime
	}
	return 0
}

func (m *UserFetchActivityGiftPkgReq) GetDeviceIdHex() string {
	if m != nil {
		return m.DeviceIdHex
	}
	return ""
}

type UserFetchActivityGiftPkgResp struct {
	GiftPkgSerial string `protobuf:"bytes,1,req,name=gift_pkg_serial,json=giftPkgSerial" json:"gift_pkg_serial"`
}

func (m *UserFetchActivityGiftPkgResp) Reset()         { *m = UserFetchActivityGiftPkgResp{} }
func (m *UserFetchActivityGiftPkgResp) String() string { return proto.CompactTextString(m) }
func (*UserFetchActivityGiftPkgResp) ProtoMessage()    {}
func (*UserFetchActivityGiftPkgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{3}
}

func (m *UserFetchActivityGiftPkgResp) GetGiftPkgSerial() string {
	if m != nil {
		return m.GiftPkgSerial
	}
	return ""
}

type CheckActivityGiftPkgFetchStatusReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GiftPkgId   uint32 `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	DeviceIdHex string `protobuf:"bytes,3,req,name=device_id_hex,json=deviceIdHex" json:"device_id_hex"`
}

func (m *CheckActivityGiftPkgFetchStatusReq) Reset()         { *m = CheckActivityGiftPkgFetchStatusReq{} }
func (m *CheckActivityGiftPkgFetchStatusReq) String() string { return proto.CompactTextString(m) }
func (*CheckActivityGiftPkgFetchStatusReq) ProtoMessage()    {}
func (*CheckActivityGiftPkgFetchStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{4}
}

func (m *CheckActivityGiftPkgFetchStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckActivityGiftPkgFetchStatusReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *CheckActivityGiftPkgFetchStatusReq) GetDeviceIdHex() string {
	if m != nil {
		return m.DeviceIdHex
	}
	return ""
}

type CheckActivityGiftPkgFetchStatusResp struct {
	UidFetched    bool   `protobuf:"varint,1,req,name=uid_fetched,json=uidFetched" json:"uid_fetched"`
	DeviceFetched bool   `protobuf:"varint,2,req,name=device_fetched,json=deviceFetched" json:"device_fetched"`
	FetchTime     uint32 `protobuf:"varint,3,opt,name=fetch_time,json=fetchTime" json:"fetch_time"`
}

func (m *CheckActivityGiftPkgFetchStatusResp) Reset()         { *m = CheckActivityGiftPkgFetchStatusResp{} }
func (m *CheckActivityGiftPkgFetchStatusResp) String() string { return proto.CompactTextString(m) }
func (*CheckActivityGiftPkgFetchStatusResp) ProtoMessage()    {}
func (*CheckActivityGiftPkgFetchStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{5}
}

func (m *CheckActivityGiftPkgFetchStatusResp) GetUidFetched() bool {
	if m != nil {
		return m.UidFetched
	}
	return false
}

func (m *CheckActivityGiftPkgFetchStatusResp) GetDeviceFetched() bool {
	if m != nil {
		return m.DeviceFetched
	}
	return false
}

func (m *CheckActivityGiftPkgFetchStatusResp) GetFetchTime() uint32 {
	if m != nil {
		return m.FetchTime
	}
	return 0
}

// ================================================================
// 用户查看已经获取到的礼包序列号
// ================================================================
type GetUserGiftPkgSerialReq struct {
	GiftPkgId uint32 `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *GetUserGiftPkgSerialReq) Reset()                    { *m = GetUserGiftPkgSerialReq{} }
func (m *GetUserGiftPkgSerialReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserGiftPkgSerialReq) ProtoMessage()               {}
func (*GetUserGiftPkgSerialReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{6} }

func (m *GetUserGiftPkgSerialReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

type GetUserGiftPkgSerialResp struct {
	Status  uint32 `protobuf:"varint,1,req,name=status" json:"status"`
	OpTime  uint32 `protobuf:"varint,2,req,name=op_time,json=opTime" json:"op_time"`
	Serial  string `protobuf:"bytes,3,req,name=serial" json:"serial"`
	GuildId uint32 `protobuf:"varint,4,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetUserGiftPkgSerialResp) Reset()                    { *m = GetUserGiftPkgSerialResp{} }
func (m *GetUserGiftPkgSerialResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserGiftPkgSerialResp) ProtoMessage()               {}
func (*GetUserGiftPkgSerialResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{7} }

func (m *GetUserGiftPkgSerialResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetUserGiftPkgSerialResp) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

func (m *GetUserGiftPkgSerialResp) GetSerial() string {
	if m != nil {
		return m.Serial
	}
	return ""
}

func (m *GetUserGiftPkgSerialResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

// 礼包结构
type StGiftPkg struct {
	GiftPkgId         uint32 `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	ExchangeBegintime uint32 `protobuf:"varint,2,req,name=exchange_begintime,json=exchangeBegintime" json:"exchange_begintime"`
	ExchangeEndtime   uint32 `protobuf:"varint,3,req,name=exchange_endtime,json=exchangeEndtime" json:"exchange_endtime"`
	GameId            uint32 `protobuf:"varint,4,req,name=game_id,json=gameId" json:"game_id"`
	Intro             string `protobuf:"bytes,5,req,name=intro" json:"intro"`
	Name              string `protobuf:"bytes,6,req,name=name" json:"name"`
	Content           string `protobuf:"bytes,7,req,name=content" json:"content"`
	Usage             string `protobuf:"bytes,8,req,name=usage" json:"usage"`
	SerialTotalNum    uint32 `protobuf:"varint,9,req,name=serial_total_num,json=serialTotalNum" json:"serial_total_num"`
	OfferedNum        uint32 `protobuf:"varint,10,req,name=offered_num,json=offeredNum" json:"offered_num"`
	IsShow            bool   `protobuf:"varint,11,req,name=is_show,json=isShow" json:"is_show"`
	SourceGuild       uint32 `protobuf:"varint,12,opt,name=source_guild,json=sourceGuild" json:"source_guild"`
	ChannelId         uint32 `protobuf:"varint,13,opt,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *StGiftPkg) Reset()                    { *m = StGiftPkg{} }
func (m *StGiftPkg) String() string            { return proto.CompactTextString(m) }
func (*StGiftPkg) ProtoMessage()               {}
func (*StGiftPkg) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{8} }

func (m *StGiftPkg) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *StGiftPkg) GetExchangeBegintime() uint32 {
	if m != nil {
		return m.ExchangeBegintime
	}
	return 0
}

func (m *StGiftPkg) GetExchangeEndtime() uint32 {
	if m != nil {
		return m.ExchangeEndtime
	}
	return 0
}

func (m *StGiftPkg) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *StGiftPkg) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *StGiftPkg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StGiftPkg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *StGiftPkg) GetUsage() string {
	if m != nil {
		return m.Usage
	}
	return ""
}

func (m *StGiftPkg) GetSerialTotalNum() uint32 {
	if m != nil {
		return m.SerialTotalNum
	}
	return 0
}

func (m *StGiftPkg) GetOfferedNum() uint32 {
	if m != nil {
		return m.OfferedNum
	}
	return 0
}

func (m *StGiftPkg) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

func (m *StGiftPkg) GetSourceGuild() uint32 {
	if m != nil {
		return m.SourceGuild
	}
	return 0
}

func (m *StGiftPkg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 礼包序列码结构
type StGiftPkgSerial struct {
	GiftPkg *StGiftPkg `protobuf:"bytes,1,req,name=gift_pkg,json=giftPkg" json:"gift_pkg,omitempty"`
	Status  uint32     `protobuf:"varint,2,req,name=status" json:"status"`
	OpTime  uint32     `protobuf:"varint,3,req,name=op_time,json=opTime" json:"op_time"`
	Serial  string     `protobuf:"bytes,4,req,name=serial" json:"serial"`
	GuildId uint32     `protobuf:"varint,5,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *StGiftPkgSerial) Reset()                    { *m = StGiftPkgSerial{} }
func (m *StGiftPkgSerial) String() string            { return proto.CompactTextString(m) }
func (*StGiftPkgSerial) ProtoMessage()               {}
func (*StGiftPkgSerial) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{9} }

func (m *StGiftPkgSerial) GetGiftPkg() *StGiftPkg {
	if m != nil {
		return m.GiftPkg
	}
	return nil
}

func (m *StGiftPkgSerial) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StGiftPkgSerial) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

func (m *StGiftPkgSerial) GetSerial() string {
	if m != nil {
		return m.Serial
	}
	return ""
}

func (m *StGiftPkgSerial) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

// ================================================================
// 查看礼包列表
// ================================================================
type GetGiftPkgListReq struct {
	Offset        uint32   `protobuf:"varint,1,req,name=offset" json:"offset"`
	Size_         uint32   `protobuf:"varint,2,req,name=size" json:"size"`
	SourceGuild   uint32   `protobuf:"varint,3,opt,name=source_guild,json=sourceGuild" json:"source_guild"`
	ChannelIdList []uint32 `protobuf:"varint,4,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	GameId        uint32   `protobuf:"varint,5,opt,name=game_id,json=gameId" json:"game_id"`
	OnlyVisible   bool     `protobuf:"varint,6,opt,name=only_visible,json=onlyVisible" json:"only_visible"`
}

func (m *GetGiftPkgListReq) Reset()                    { *m = GetGiftPkgListReq{} }
func (m *GetGiftPkgListReq) String() string            { return proto.CompactTextString(m) }
func (*GetGiftPkgListReq) ProtoMessage()               {}
func (*GetGiftPkgListReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{10} }

func (m *GetGiftPkgListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGiftPkgListReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

func (m *GetGiftPkgListReq) GetSourceGuild() uint32 {
	if m != nil {
		return m.SourceGuild
	}
	return 0
}

func (m *GetGiftPkgListReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetGiftPkgListReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGiftPkgListReq) GetOnlyVisible() bool {
	if m != nil {
		return m.OnlyVisible
	}
	return false
}

type GetGiftPkgListResp struct {
	PkgList []*StGiftPkg `protobuf:"bytes,1,rep,name=pkg_list,json=pkgList" json:"pkg_list,omitempty"`
	Total   uint32       `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetGiftPkgListResp) Reset()                    { *m = GetGiftPkgListResp{} }
func (m *GetGiftPkgListResp) String() string            { return proto.CompactTextString(m) }
func (*GetGiftPkgListResp) ProtoMessage()               {}
func (*GetGiftPkgListResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{11} }

func (m *GetGiftPkgListResp) GetPkgList() []*StGiftPkg {
	if m != nil {
		return m.PkgList
	}
	return nil
}

func (m *GetGiftPkgListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// ================================================================
// 单查某个礼包
// ================================================================
type GetGiftPkgReq struct {
	GiftPkgId uint32 `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *GetGiftPkgReq) Reset()                    { *m = GetGiftPkgReq{} }
func (m *GetGiftPkgReq) String() string            { return proto.CompactTextString(m) }
func (*GetGiftPkgReq) ProtoMessage()               {}
func (*GetGiftPkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{12} }

func (m *GetGiftPkgReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

type GetGiftPkgResp struct {
	Pkg *StGiftPkg `protobuf:"bytes,1,req,name=pkg" json:"pkg,omitempty"`
}

func (m *GetGiftPkgResp) Reset()                    { *m = GetGiftPkgResp{} }
func (m *GetGiftPkgResp) String() string            { return proto.CompactTextString(m) }
func (*GetGiftPkgResp) ProtoMessage()               {}
func (*GetGiftPkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{13} }

func (m *GetGiftPkgResp) GetPkg() *StGiftPkg {
	if m != nil {
		return m.Pkg
	}
	return nil
}

// ================================================================
// 用户查看自己的礼包列表
// ================================================================
type GetMyGiftPkgSerialListReq struct {
	Offset uint32 `protobuf:"varint,1,req,name=offset" json:"offset"`
	Size_  uint32 `protobuf:"varint,2,req,name=size" json:"size"`
}

func (m *GetMyGiftPkgSerialListReq) Reset()         { *m = GetMyGiftPkgSerialListReq{} }
func (m *GetMyGiftPkgSerialListReq) String() string { return proto.CompactTextString(m) }
func (*GetMyGiftPkgSerialListReq) ProtoMessage()    {}
func (*GetMyGiftPkgSerialListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{14}
}

func (m *GetMyGiftPkgSerialListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMyGiftPkgSerialListReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

type GetMyGiftPkgSerialListResp struct {
	PkgList []*StGiftPkgSerial `protobuf:"bytes,1,rep,name=pkg_list,json=pkgList" json:"pkg_list,omitempty"`
}

func (m *GetMyGiftPkgSerialListResp) Reset()         { *m = GetMyGiftPkgSerialListResp{} }
func (m *GetMyGiftPkgSerialListResp) String() string { return proto.CompactTextString(m) }
func (*GetMyGiftPkgSerialListResp) ProtoMessage()    {}
func (*GetMyGiftPkgSerialListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{15}
}

func (m *GetMyGiftPkgSerialListResp) GetPkgList() []*StGiftPkgSerial {
	if m != nil {
		return m.PkgList
	}
	return nil
}

// ================================================================
// 公会申请礼包
// ================================================================
type GuildApplyGiftPkgReq struct {
	GuildId     uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	PkgId       uint32 `protobuf:"varint,2,req,name=pkg_id,json=pkgId" json:"pkg_id"`
	RequiredNum uint32 `protobuf:"varint,3,req,name=required_num,json=requiredNum" json:"required_num"`
	ApplyMsg    string `protobuf:"bytes,4,req,name=apply_msg,json=applyMsg" json:"apply_msg"`
}

func (m *GuildApplyGiftPkgReq) Reset()                    { *m = GuildApplyGiftPkgReq{} }
func (m *GuildApplyGiftPkgReq) String() string            { return proto.CompactTextString(m) }
func (*GuildApplyGiftPkgReq) ProtoMessage()               {}
func (*GuildApplyGiftPkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{16} }

func (m *GuildApplyGiftPkgReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildApplyGiftPkgReq) GetPkgId() uint32 {
	if m != nil {
		return m.PkgId
	}
	return 0
}

func (m *GuildApplyGiftPkgReq) GetRequiredNum() uint32 {
	if m != nil {
		return m.RequiredNum
	}
	return 0
}

func (m *GuildApplyGiftPkgReq) GetApplyMsg() string {
	if m != nil {
		return m.ApplyMsg
	}
	return ""
}

type GuildApplyGiftPkgResp struct {
}

func (m *GuildApplyGiftPkgResp) Reset()                    { *m = GuildApplyGiftPkgResp{} }
func (m *GuildApplyGiftPkgResp) String() string            { return proto.CompactTextString(m) }
func (*GuildApplyGiftPkgResp) ProtoMessage()               {}
func (*GuildApplyGiftPkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{17} }

// ================================================================
// 查礼包申请列表
// ================================================================
type GetGiftPkgApplyListReq struct {
	Offset uint32 `protobuf:"varint,1,req,name=offset" json:"offset"`
	Size_  uint32 `protobuf:"varint,2,req,name=size" json:"size"`
}

func (m *GetGiftPkgApplyListReq) Reset()                    { *m = GetGiftPkgApplyListReq{} }
func (m *GetGiftPkgApplyListReq) String() string            { return proto.CompactTextString(m) }
func (*GetGiftPkgApplyListReq) ProtoMessage()               {}
func (*GetGiftPkgApplyListReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{18} }

func (m *GetGiftPkgApplyListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGiftPkgApplyListReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

type StGiftPkgApply struct {
	GuildId       uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ApplyId       uint32 `protobuf:"varint,2,req,name=apply_id,json=applyId" json:"apply_id"`
	PkgId         uint32 `protobuf:"varint,3,req,name=pkg_id,json=pkgId" json:"pkg_id"`
	ApplyTime     uint32 `protobuf:"varint,4,req,name=apply_time,json=applyTime" json:"apply_time"`
	Status        uint32 `protobuf:"varint,5,req,name=status" json:"status"`
	ApplyMsg      string `protobuf:"bytes,6,req,name=apply_msg,json=applyMsg" json:"apply_msg"`
	OfficalOpTime uint32 `protobuf:"varint,7,req,name=offical_op_time,json=officalOpTime" json:"offical_op_time"`
	OfficalMsg    string `protobuf:"bytes,8,req,name=offical_msg,json=officalMsg" json:"offical_msg"`
	WantNum       uint32 `protobuf:"varint,9,req,name=want_num,json=wantNum" json:"want_num"`
	OfferNum      uint32 `protobuf:"varint,10,req,name=offer_num,json=offerNum" json:"offer_num"`
	OpUid         uint32 `protobuf:"varint,11,req,name=op_uid,json=opUid" json:"op_uid"`
}

func (m *StGiftPkgApply) Reset()                    { *m = StGiftPkgApply{} }
func (m *StGiftPkgApply) String() string            { return proto.CompactTextString(m) }
func (*StGiftPkgApply) ProtoMessage()               {}
func (*StGiftPkgApply) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{19} }

func (m *StGiftPkgApply) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StGiftPkgApply) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *StGiftPkgApply) GetPkgId() uint32 {
	if m != nil {
		return m.PkgId
	}
	return 0
}

func (m *StGiftPkgApply) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *StGiftPkgApply) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StGiftPkgApply) GetApplyMsg() string {
	if m != nil {
		return m.ApplyMsg
	}
	return ""
}

func (m *StGiftPkgApply) GetOfficalOpTime() uint32 {
	if m != nil {
		return m.OfficalOpTime
	}
	return 0
}

func (m *StGiftPkgApply) GetOfficalMsg() string {
	if m != nil {
		return m.OfficalMsg
	}
	return ""
}

func (m *StGiftPkgApply) GetWantNum() uint32 {
	if m != nil {
		return m.WantNum
	}
	return 0
}

func (m *StGiftPkgApply) GetOfferNum() uint32 {
	if m != nil {
		return m.OfferNum
	}
	return 0
}

func (m *StGiftPkgApply) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type GetGiftPkgApplyListResp struct {
	ApplyList []*StGiftPkgApply `protobuf:"bytes,1,rep,name=apply_list,json=applyList" json:"apply_list,omitempty"`
	Total     uint32            `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetGiftPkgApplyListResp) Reset()                    { *m = GetGiftPkgApplyListResp{} }
func (m *GetGiftPkgApplyListResp) String() string            { return proto.CompactTextString(m) }
func (*GetGiftPkgApplyListResp) ProtoMessage()               {}
func (*GetGiftPkgApplyListResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{20} }

func (m *GetGiftPkgApplyListResp) GetApplyList() []*StGiftPkgApply {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

func (m *GetGiftPkgApplyListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// ================================================================
// 查礼包申请列表
// ================================================================
type GetGuildApplyingListReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetGuildApplyingListReq) Reset()                    { *m = GetGuildApplyingListReq{} }
func (m *GetGuildApplyingListReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildApplyingListReq) ProtoMessage()               {}
func (*GetGuildApplyingListReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{21} }

func (m *GetGuildApplyingListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildApplyingListResp struct {
	ApplyList []*StGiftPkgApply `protobuf:"bytes,1,rep,name=apply_list,json=applyList" json:"apply_list,omitempty"`
}

func (m *GetGuildApplyingListResp) Reset()                    { *m = GetGuildApplyingListResp{} }
func (m *GetGuildApplyingListResp) String() string            { return proto.CompactTextString(m) }
func (*GetGuildApplyingListResp) ProtoMessage()               {}
func (*GetGuildApplyingListResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{22} }

func (m *GetGuildApplyingListResp) GetApplyList() []*StGiftPkgApply {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

// ================================================================
// 录入礼包
// ================================================================
type TypeGiftPkgReq struct {
	Pkg       *StGiftPkg `protobuf:"bytes,1,req,name=pkg" json:"pkg,omitempty"`
	OpAccount string     `protobuf:"bytes,2,req,name=op_account,json=opAccount" json:"op_account"`
}

func (m *TypeGiftPkgReq) Reset()                    { *m = TypeGiftPkgReq{} }
func (m *TypeGiftPkgReq) String() string            { return proto.CompactTextString(m) }
func (*TypeGiftPkgReq) ProtoMessage()               {}
func (*TypeGiftPkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{23} }

func (m *TypeGiftPkgReq) GetPkg() *StGiftPkg {
	if m != nil {
		return m.Pkg
	}
	return nil
}

func (m *TypeGiftPkgReq) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

type TypeGiftPkgResp struct {
	GiftPkgId uint32 `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *TypeGiftPkgResp) Reset()                    { *m = TypeGiftPkgResp{} }
func (m *TypeGiftPkgResp) String() string            { return proto.CompactTextString(m) }
func (*TypeGiftPkgResp) ProtoMessage()               {}
func (*TypeGiftPkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{24} }

func (m *TypeGiftPkgResp) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

// ===============================================================
// 更新礼包
// ===============================================================
type UpdateGiftPkgReq struct {
	Pkg       *StGiftPkg `protobuf:"bytes,1,req,name=pkg" json:"pkg,omitempty"`
	OpAccount string     `protobuf:"bytes,2,req,name=op_account,json=opAccount" json:"op_account"`
}

func (m *UpdateGiftPkgReq) Reset()                    { *m = UpdateGiftPkgReq{} }
func (m *UpdateGiftPkgReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateGiftPkgReq) ProtoMessage()               {}
func (*UpdateGiftPkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{25} }

func (m *UpdateGiftPkgReq) GetPkg() *StGiftPkg {
	if m != nil {
		return m.Pkg
	}
	return nil
}

func (m *UpdateGiftPkgReq) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

// ===============================================================
// 上/下架礼包
// ===============================================================
type UpdateGiftPkgShowStatusReq struct {
	GiftPkgId uint32 `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	IsShow    bool   `protobuf:"varint,2,req,name=is_show,json=isShow" json:"is_show"`
	OpAccount string `protobuf:"bytes,3,req,name=op_account,json=opAccount" json:"op_account"`
}

func (m *UpdateGiftPkgShowStatusReq) Reset()         { *m = UpdateGiftPkgShowStatusReq{} }
func (m *UpdateGiftPkgShowStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGiftPkgShowStatusReq) ProtoMessage()    {}
func (*UpdateGiftPkgShowStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{26}
}

func (m *UpdateGiftPkgShowStatusReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *UpdateGiftPkgShowStatusReq) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

func (m *UpdateGiftPkgShowStatusReq) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

// ================================================================
// 录入序列号
// ================================================================
type TypeGiftPkgSerialReq struct {
	GiftPkgId  uint32   `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	SerialList []string `protobuf:"bytes,2,rep,name=serial_list,json=serialList" json:"serial_list,omitempty"`
	OpAccount  string   `protobuf:"bytes,3,req,name=op_account,json=opAccount" json:"op_account"`
}

func (m *TypeGiftPkgSerialReq) Reset()                    { *m = TypeGiftPkgSerialReq{} }
func (m *TypeGiftPkgSerialReq) String() string            { return proto.CompactTextString(m) }
func (*TypeGiftPkgSerialReq) ProtoMessage()               {}
func (*TypeGiftPkgSerialReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{27} }

func (m *TypeGiftPkgSerialReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *TypeGiftPkgSerialReq) GetSerialList() []string {
	if m != nil {
		return m.SerialList
	}
	return nil
}

func (m *TypeGiftPkgSerialReq) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

type TypeGiftPkgSerialResp struct {
	AddCount uint32 `protobuf:"varint,1,req,name=add_count,json=addCount" json:"add_count"`
}

func (m *TypeGiftPkgSerialResp) Reset()                    { *m = TypeGiftPkgSerialResp{} }
func (m *TypeGiftPkgSerialResp) String() string            { return proto.CompactTextString(m) }
func (*TypeGiftPkgSerialResp) ProtoMessage()               {}
func (*TypeGiftPkgSerialResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{28} }

func (m *TypeGiftPkgSerialResp) GetAddCount() uint32 {
	if m != nil {
		return m.AddCount
	}
	return 0
}

// 录入序列号并直接发给公会, 要求礼包的source必须是同一公会
type TypeGiftPkgSerialToGuildReq struct {
	GiftPkgId  uint32   `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	GuildId    uint32   `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	SerialList []string `protobuf:"bytes,3,rep,name=serial_list,json=serialList" json:"serial_list,omitempty"`
	OpAccount  string   `protobuf:"bytes,4,req,name=op_account,json=opAccount" json:"op_account"`
}

func (m *TypeGiftPkgSerialToGuildReq) Reset()         { *m = TypeGiftPkgSerialToGuildReq{} }
func (m *TypeGiftPkgSerialToGuildReq) String() string { return proto.CompactTextString(m) }
func (*TypeGiftPkgSerialToGuildReq) ProtoMessage()    {}
func (*TypeGiftPkgSerialToGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{29}
}

func (m *TypeGiftPkgSerialToGuildReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *TypeGiftPkgSerialToGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *TypeGiftPkgSerialToGuildReq) GetSerialList() []string {
	if m != nil {
		return m.SerialList
	}
	return nil
}

func (m *TypeGiftPkgSerialToGuildReq) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

type TypeGiftPkgSerialToGuildResp struct {
	AddCount uint32 `protobuf:"varint,1,req,name=add_count,json=addCount" json:"add_count"`
}

func (m *TypeGiftPkgSerialToGuildResp) Reset()         { *m = TypeGiftPkgSerialToGuildResp{} }
func (m *TypeGiftPkgSerialToGuildResp) String() string { return proto.CompactTextString(m) }
func (*TypeGiftPkgSerialToGuildResp) ProtoMessage()    {}
func (*TypeGiftPkgSerialToGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{30}
}

func (m *TypeGiftPkgSerialToGuildResp) GetAddCount() uint32 {
	if m != nil {
		return m.AddCount
	}
	return 0
}

// ================================================================
// 批量查礼包信息
// ================================================================
type BatchGetGiftPkgReq struct {
	GiftPkgId []uint32 `protobuf:"varint,1,rep,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id,omitempty"`
}

func (m *BatchGetGiftPkgReq) Reset()                    { *m = BatchGetGiftPkgReq{} }
func (m *BatchGetGiftPkgReq) String() string            { return proto.CompactTextString(m) }
func (*BatchGetGiftPkgReq) ProtoMessage()               {}
func (*BatchGetGiftPkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{31} }

func (m *BatchGetGiftPkgReq) GetGiftPkgId() []uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return nil
}

type BatchGetGiftPkgResp struct {
	Pkg []*StGiftPkg `protobuf:"bytes,1,rep,name=pkg" json:"pkg,omitempty"`
}

func (m *BatchGetGiftPkgResp) Reset()                    { *m = BatchGetGiftPkgResp{} }
func (m *BatchGetGiftPkgResp) String() string            { return proto.CompactTextString(m) }
func (*BatchGetGiftPkgResp) ProtoMessage()               {}
func (*BatchGetGiftPkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{32} }

func (m *BatchGetGiftPkgResp) GetPkg() []*StGiftPkg {
	if m != nil {
		return m.Pkg
	}
	return nil
}

// 红包领取流水
type RedPkgFetchRecord struct {
	Id        uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	RedPkgId  uint32 `protobuf:"varint,3,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
	FetchUid  uint32 `protobuf:"varint,4,req,name=fetch_uid,json=fetchUid" json:"fetch_uid"`
	FetchTime uint32 `protobuf:"varint,5,req,name=fetch_time,json=fetchTime" json:"fetch_time"`
}

func (m *RedPkgFetchRecord) Reset()                    { *m = RedPkgFetchRecord{} }
func (m *RedPkgFetchRecord) String() string            { return proto.CompactTextString(m) }
func (*RedPkgFetchRecord) ProtoMessage()               {}
func (*RedPkgFetchRecord) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{33} }

func (m *RedPkgFetchRecord) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RedPkgFetchRecord) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RedPkgFetchRecord) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

func (m *RedPkgFetchRecord) GetFetchUid() uint32 {
	if m != nil {
		return m.FetchUid
	}
	return 0
}

func (m *RedPkgFetchRecord) GetFetchTime() uint32 {
	if m != nil {
		return m.FetchTime
	}
	return 0
}

// 公会礼包结构
type StGuildGiftPkg struct {
	GiftPkg         *StGiftPkg `protobuf:"bytes,1,req,name=gift_pkg,json=giftPkg" json:"gift_pkg,omitempty"`
	TotalFetchNum   uint32     `protobuf:"varint,2,req,name=total_fetch_num,json=totalFetchNum" json:"total_fetch_num"`
	TotalNum        uint32     `protobuf:"varint,3,req,name=total_num,json=totalNum" json:"total_num"`
	StorageFetchNum uint32     `protobuf:"varint,4,req,name=storage_fetch_num,json=storageFetchNum" json:"storage_fetch_num"`
	StorageTotalNum uint32     `protobuf:"varint,5,req,name=storage_total_num,json=storageTotalNum" json:"storage_total_num"`
	PublicFetchNum  uint32     `protobuf:"varint,6,req,name=public_fetch_num,json=publicFetchNum" json:"public_fetch_num"`
	PublicTotalNum  uint32     `protobuf:"varint,7,req,name=public_total_num,json=publicTotalNum" json:"public_total_num"`
	TaohaoCount     uint32     `protobuf:"varint,8,req,name=taohao_count,json=taohaoCount" json:"taohao_count"`
	ApplyPassTime   uint32     `protobuf:"varint,9,req,name=apply_pass_time,json=applyPassTime" json:"apply_pass_time"`
	RedDiamondPrice uint32     `protobuf:"varint,10,opt,name=red_diamond_price,json=redDiamondPrice" json:"red_diamond_price"`
}

func (m *StGuildGiftPkg) Reset()                    { *m = StGuildGiftPkg{} }
func (m *StGuildGiftPkg) String() string            { return proto.CompactTextString(m) }
func (*StGuildGiftPkg) ProtoMessage()               {}
func (*StGuildGiftPkg) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{34} }

func (m *StGuildGiftPkg) GetGiftPkg() *StGiftPkg {
	if m != nil {
		return m.GiftPkg
	}
	return nil
}

func (m *StGuildGiftPkg) GetTotalFetchNum() uint32 {
	if m != nil {
		return m.TotalFetchNum
	}
	return 0
}

func (m *StGuildGiftPkg) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

func (m *StGuildGiftPkg) GetStorageFetchNum() uint32 {
	if m != nil {
		return m.StorageFetchNum
	}
	return 0
}

func (m *StGuildGiftPkg) GetStorageTotalNum() uint32 {
	if m != nil {
		return m.StorageTotalNum
	}
	return 0
}

func (m *StGuildGiftPkg) GetPublicFetchNum() uint32 {
	if m != nil {
		return m.PublicFetchNum
	}
	return 0
}

func (m *StGuildGiftPkg) GetPublicTotalNum() uint32 {
	if m != nil {
		return m.PublicTotalNum
	}
	return 0
}

func (m *StGuildGiftPkg) GetTaohaoCount() uint32 {
	if m != nil {
		return m.TaohaoCount
	}
	return 0
}

func (m *StGuildGiftPkg) GetApplyPassTime() uint32 {
	if m != nil {
		return m.ApplyPassTime
	}
	return 0
}

func (m *StGuildGiftPkg) GetRedDiamondPrice() uint32 {
	if m != nil {
		return m.RedDiamondPrice
	}
	return 0
}

// ================================================================
// 查看公会礼包列表
// ================================================================
type GetGuildGiftPkgListReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Offset    uint32 `protobuf:"varint,2,req,name=offset" json:"offset"`
	Size_     uint32 `protobuf:"varint,3,req,name=size" json:"size"`
	OrderType uint32 `protobuf:"varint,4,opt,name=order_type,json=orderType" json:"order_type"`
}

func (m *GetGuildGiftPkgListReq) Reset()                    { *m = GetGuildGiftPkgListReq{} }
func (m *GetGuildGiftPkgListReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildGiftPkgListReq) ProtoMessage()               {}
func (*GetGuildGiftPkgListReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{35} }

func (m *GetGuildGiftPkgListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildGiftPkgListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildGiftPkgListReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

func (m *GetGuildGiftPkgListReq) GetOrderType() uint32 {
	if m != nil {
		return m.OrderType
	}
	return 0
}

type GetGuildGiftPkgListResp struct {
	PkgList []*StGuildGiftPkg `protobuf:"bytes,1,rep,name=pkg_list,json=pkgList" json:"pkg_list,omitempty"`
	Total   uint32            `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetGuildGiftPkgListResp) Reset()                    { *m = GetGuildGiftPkgListResp{} }
func (m *GetGuildGiftPkgListResp) String() string            { return proto.CompactTextString(m) }
func (*GetGuildGiftPkgListResp) ProtoMessage()               {}
func (*GetGuildGiftPkgListResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{36} }

func (m *GetGuildGiftPkgListResp) GetPkgList() []*StGuildGiftPkg {
	if m != nil {
		return m.PkgList
	}
	return nil
}

func (m *GetGuildGiftPkgListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// ================================================================
// 根据游戏id查看公会礼包列表
// ================================================================
type GetGuildPkgListByGameIdReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GameId  uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	Offset  uint32 `protobuf:"varint,3,req,name=offset" json:"offset"`
	Size_   uint32 `protobuf:"varint,4,req,name=size" json:"size"`
}

func (m *GetGuildPkgListByGameIdReq) Reset()         { *m = GetGuildPkgListByGameIdReq{} }
func (m *GetGuildPkgListByGameIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildPkgListByGameIdReq) ProtoMessage()    {}
func (*GetGuildPkgListByGameIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{37}
}

func (m *GetGuildPkgListByGameIdReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildPkgListByGameIdReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGuildPkgListByGameIdReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildPkgListByGameIdReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

type GetGuildPkgListByGameIdResp struct {
	PkgList []*StGuildGiftPkg `protobuf:"bytes,1,rep,name=pkg_list,json=pkgList" json:"pkg_list,omitempty"`
	Total   uint32            `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetGuildPkgListByGameIdResp) Reset()         { *m = GetGuildPkgListByGameIdResp{} }
func (m *GetGuildPkgListByGameIdResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildPkgListByGameIdResp) ProtoMessage()    {}
func (*GetGuildPkgListByGameIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{38}
}

func (m *GetGuildPkgListByGameIdResp) GetPkgList() []*StGuildGiftPkg {
	if m != nil {
		return m.PkgList
	}
	return nil
}

func (m *GetGuildPkgListByGameIdResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// ================================================================
// 运营通过申请
// ================================================================
type PassGiftPkgApplyReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ApplyId   uint32 `protobuf:"varint,2,req,name=apply_id,json=applyId" json:"apply_id"`
	OfferNum  uint32 `protobuf:"varint,3,req,name=offer_num,json=offerNum" json:"offer_num"`
	OpAccount string `protobuf:"bytes,4,req,name=op_account,json=opAccount" json:"op_account"`
	GuildName string `protobuf:"bytes,5,opt,name=guild_name,json=guildName" json:"guild_name"`
}

func (m *PassGiftPkgApplyReq) Reset()                    { *m = PassGiftPkgApplyReq{} }
func (m *PassGiftPkgApplyReq) String() string            { return proto.CompactTextString(m) }
func (*PassGiftPkgApplyReq) ProtoMessage()               {}
func (*PassGiftPkgApplyReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{39} }

func (m *PassGiftPkgApplyReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *PassGiftPkgApplyReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *PassGiftPkgApplyReq) GetOfferNum() uint32 {
	if m != nil {
		return m.OfferNum
	}
	return 0
}

func (m *PassGiftPkgApplyReq) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

func (m *PassGiftPkgApplyReq) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

type PassGiftPkgApplyResp struct {
}

func (m *PassGiftPkgApplyResp) Reset()                    { *m = PassGiftPkgApplyResp{} }
func (m *PassGiftPkgApplyResp) String() string            { return proto.CompactTextString(m) }
func (*PassGiftPkgApplyResp) ProtoMessage()               {}
func (*PassGiftPkgApplyResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{40} }

// ================================================================
// 运营拒绝申请
// ================================================================
type RejectGiftPkgApplyReq struct {
	GuildId      uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	ApplyId      uint32 `protobuf:"varint,2,req,name=apply_id,json=applyId" json:"apply_id"`
	RejectReason string `protobuf:"bytes,3,req,name=reject_reason,json=rejectReason" json:"reject_reason"`
	OpAccount    string `protobuf:"bytes,4,req,name=op_account,json=opAccount" json:"op_account"`
	GuildName    string `protobuf:"bytes,5,opt,name=guild_name,json=guildName" json:"guild_name"`
}

func (m *RejectGiftPkgApplyReq) Reset()                    { *m = RejectGiftPkgApplyReq{} }
func (m *RejectGiftPkgApplyReq) String() string            { return proto.CompactTextString(m) }
func (*RejectGiftPkgApplyReq) ProtoMessage()               {}
func (*RejectGiftPkgApplyReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{41} }

func (m *RejectGiftPkgApplyReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RejectGiftPkgApplyReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *RejectGiftPkgApplyReq) GetRejectReason() string {
	if m != nil {
		return m.RejectReason
	}
	return ""
}

func (m *RejectGiftPkgApplyReq) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

func (m *RejectGiftPkgApplyReq) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

type RejectGiftPkgApplyResp struct {
}

func (m *RejectGiftPkgApplyResp) Reset()                    { *m = RejectGiftPkgApplyResp{} }
func (m *RejectGiftPkgApplyResp) String() string            { return proto.CompactTextString(m) }
func (*RejectGiftPkgApplyResp) ProtoMessage()               {}
func (*RejectGiftPkgApplyResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{42} }

// ================================================================
// 查公会某一种礼包
// ================================================================
type GetGuildPkgStatusReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	PkgId   uint32 `protobuf:"varint,2,req,name=pkg_id,json=pkgId" json:"pkg_id"`
}

func (m *GetGuildPkgStatusReq) Reset()                    { *m = GetGuildPkgStatusReq{} }
func (m *GetGuildPkgStatusReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildPkgStatusReq) ProtoMessage()               {}
func (*GetGuildPkgStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{43} }

func (m *GetGuildPkgStatusReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildPkgStatusReq) GetPkgId() uint32 {
	if m != nil {
		return m.PkgId
	}
	return 0
}

type GetGuildPkgStatusResp struct {
	Pkg *StGuildGiftPkg `protobuf:"bytes,1,req,name=pkg" json:"pkg,omitempty"`
}

func (m *GetGuildPkgStatusResp) Reset()                    { *m = GetGuildPkgStatusResp{} }
func (m *GetGuildPkgStatusResp) String() string            { return proto.CompactTextString(m) }
func (*GetGuildPkgStatusResp) ProtoMessage()               {}
func (*GetGuildPkgStatusResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{44} }

func (m *GetGuildPkgStatusResp) GetPkg() *StGuildGiftPkg {
	if m != nil {
		return m.Pkg
	}
	return nil
}

// 批量接口
type BatchGetGuildPkgStatusReq struct {
	GuildId   uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	PkgIdList []uint32 `protobuf:"varint,2,rep,name=pkg_id_list,json=pkgIdList" json:"pkg_id_list,omitempty"`
}

func (m *BatchGetGuildPkgStatusReq) Reset()         { *m = BatchGetGuildPkgStatusReq{} }
func (m *BatchGetGuildPkgStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGuildPkgStatusReq) ProtoMessage()    {}
func (*BatchGetGuildPkgStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{45}
}

func (m *BatchGetGuildPkgStatusReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetGuildPkgStatusReq) GetPkgIdList() []uint32 {
	if m != nil {
		return m.PkgIdList
	}
	return nil
}

type BatchGetGuildPkgStatusResp struct {
	PkgList []*StGuildGiftPkg `protobuf:"bytes,1,rep,name=pkg_list,json=pkgList" json:"pkg_list,omitempty"`
}

func (m *BatchGetGuildPkgStatusResp) Reset()         { *m = BatchGetGuildPkgStatusResp{} }
func (m *BatchGetGuildPkgStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGuildPkgStatusResp) ProtoMessage()    {}
func (*BatchGetGuildPkgStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{46}
}

func (m *BatchGetGuildPkgStatusResp) GetPkgList() []*StGuildGiftPkg {
	if m != nil {
		return m.PkgList
	}
	return nil
}

// ================================================================
// 判断公会是否正在申请某种礼包
// ================================================================
type CheckGuildPkgApplyingReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GiftPkgId uint32 `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *CheckGuildPkgApplyingReq) Reset()                    { *m = CheckGuildPkgApplyingReq{} }
func (m *CheckGuildPkgApplyingReq) String() string            { return proto.CompactTextString(m) }
func (*CheckGuildPkgApplyingReq) ProtoMessage()               {}
func (*CheckGuildPkgApplyingReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{47} }

func (m *CheckGuildPkgApplyingReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CheckGuildPkgApplyingReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

type CheckGuildPkgApplyingResp struct {
	IsApplying uint32 `protobuf:"varint,1,req,name=is_applying,json=isApplying" json:"is_applying"`
}

func (m *CheckGuildPkgApplyingResp) Reset()         { *m = CheckGuildPkgApplyingResp{} }
func (m *CheckGuildPkgApplyingResp) String() string { return proto.CompactTextString(m) }
func (*CheckGuildPkgApplyingResp) ProtoMessage()    {}
func (*CheckGuildPkgApplyingResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{48}
}

func (m *CheckGuildPkgApplyingResp) GetIsApplying() uint32 {
	if m != nil {
		return m.IsApplying
	}
	return 0
}

// ================================================================
// 判断公会是否正在申请某种礼包
// ================================================================
type GetGuildApplyHistoryReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Offset  uint32 `protobuf:"varint,2,req,name=offset" json:"offset"`
	Size_   uint32 `protobuf:"varint,3,req,name=size" json:"size"`
}

func (m *GetGuildApplyHistoryReq) Reset()                    { *m = GetGuildApplyHistoryReq{} }
func (m *GetGuildApplyHistoryReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildApplyHistoryReq) ProtoMessage()               {}
func (*GetGuildApplyHistoryReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{49} }

func (m *GetGuildApplyHistoryReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildApplyHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildApplyHistoryReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

type GetGuildApplyHistoryResp struct {
	ApplyList []*StGiftPkgApply `protobuf:"bytes,1,rep,name=apply_list,json=applyList" json:"apply_list,omitempty"`
	Total     uint32            `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetGuildApplyHistoryResp) Reset()                    { *m = GetGuildApplyHistoryResp{} }
func (m *GetGuildApplyHistoryResp) String() string            { return proto.CompactTextString(m) }
func (*GetGuildApplyHistoryResp) ProtoMessage()               {}
func (*GetGuildApplyHistoryResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{50} }

func (m *GetGuildApplyHistoryResp) GetApplyList() []*StGiftPkgApply {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

func (m *GetGuildApplyHistoryResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// ================================================================
// 淘号
// ================================================================
type TaohaoReq struct {
	GiftPkgId uint32 `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *TaohaoReq) Reset()                    { *m = TaohaoReq{} }
func (m *TaohaoReq) String() string            { return proto.CompactTextString(m) }
func (*TaohaoReq) ProtoMessage()               {}
func (*TaohaoReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{51} }

func (m *TaohaoReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *TaohaoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type TaohaoResp struct {
	SerialNum string `protobuf:"bytes,1,req,name=serial_num,json=serialNum" json:"serial_num"`
}

func (m *TaohaoResp) Reset()                    { *m = TaohaoResp{} }
func (m *TaohaoResp) String() string            { return proto.CompactTextString(m) }
func (*TaohaoResp) ProtoMessage()               {}
func (*TaohaoResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{52} }

func (m *TaohaoResp) GetSerialNum() string {
	if m != nil {
		return m.SerialNum
	}
	return ""
}

// ================================================================
// 增加淘号次数
// ================================================================
type TaohaoAddUsedCountReq struct {
	GiftPkgId uint32 `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	SerialNum string `protobuf:"bytes,3,req,name=serial_num,json=serialNum" json:"serial_num"`
}

func (m *TaohaoAddUsedCountReq) Reset()                    { *m = TaohaoAddUsedCountReq{} }
func (m *TaohaoAddUsedCountReq) String() string            { return proto.CompactTextString(m) }
func (*TaohaoAddUsedCountReq) ProtoMessage()               {}
func (*TaohaoAddUsedCountReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{53} }

func (m *TaohaoAddUsedCountReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *TaohaoAddUsedCountReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *TaohaoAddUsedCountReq) GetSerialNum() string {
	if m != nil {
		return m.SerialNum
	}
	return ""
}

type TaohaoAddUsedCountResp struct {
}

func (m *TaohaoAddUsedCountResp) Reset()                    { *m = TaohaoAddUsedCountResp{} }
func (m *TaohaoAddUsedCountResp) String() string            { return proto.CompactTextString(m) }
func (*TaohaoAddUsedCountResp) ProtoMessage()               {}
func (*TaohaoAddUsedCountResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{54} }

// ================================================================
// 生成红包
// ================================================================
type CreateRedpkgReq struct {
	GuildId        uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GiftPkgId      uint32 `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	SerialTotalNum uint32 `protobuf:"varint,3,req,name=serial_total_num,json=serialTotalNum" json:"serial_total_num"`
}

func (m *CreateRedpkgReq) Reset()                    { *m = CreateRedpkgReq{} }
func (m *CreateRedpkgReq) String() string            { return proto.CompactTextString(m) }
func (*CreateRedpkgReq) ProtoMessage()               {}
func (*CreateRedpkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{55} }

func (m *CreateRedpkgReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CreateRedpkgReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *CreateRedpkgReq) GetSerialTotalNum() uint32 {
	if m != nil {
		return m.SerialTotalNum
	}
	return 0
}

type CreateRedpkgResp struct {
	RedpkgId uint32 `protobuf:"varint,1,req,name=redpkg_id,json=redpkgId" json:"redpkg_id"`
}

func (m *CreateRedpkgResp) Reset()                    { *m = CreateRedpkgResp{} }
func (m *CreateRedpkgResp) String() string            { return proto.CompactTextString(m) }
func (*CreateRedpkgResp) ProtoMessage()               {}
func (*CreateRedpkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{56} }

func (m *CreateRedpkgResp) GetRedpkgId() uint32 {
	if m != nil {
		return m.RedpkgId
	}
	return 0
}

// ================================================================
// 领取红包
// ================================================================
type FetchRedPkgReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	RedPkgId  uint32 `protobuf:"varint,2,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
	GiftPkgId uint32 `protobuf:"varint,3,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *FetchRedPkgReq) Reset()                    { *m = FetchRedPkgReq{} }
func (m *FetchRedPkgReq) String() string            { return proto.CompactTextString(m) }
func (*FetchRedPkgReq) ProtoMessage()               {}
func (*FetchRedPkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{57} }

func (m *FetchRedPkgReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *FetchRedPkgReq) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

func (m *FetchRedPkgReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

type FetchRedPkgResp struct {
	SerialNum string `protobuf:"bytes,1,req,name=serial_num,json=serialNum" json:"serial_num"`
}

func (m *FetchRedPkgResp) Reset()                    { *m = FetchRedPkgResp{} }
func (m *FetchRedPkgResp) String() string            { return proto.CompactTextString(m) }
func (*FetchRedPkgResp) ProtoMessage()               {}
func (*FetchRedPkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{58} }

func (m *FetchRedPkgResp) GetSerialNum() string {
	if m != nil {
		return m.SerialNum
	}
	return ""
}

// ================================================================
// 查红包领取记录
// ================================================================
type GetRedPkgHistoryReq struct {
	GuildId  uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	RedPkgId uint32 `protobuf:"varint,2,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
	Offset   uint32 `protobuf:"varint,3,req,name=offset" json:"offset"`
	Size_    uint32 `protobuf:"varint,4,req,name=size" json:"size"`
}

func (m *GetRedPkgHistoryReq) Reset()                    { *m = GetRedPkgHistoryReq{} }
func (m *GetRedPkgHistoryReq) String() string            { return proto.CompactTextString(m) }
func (*GetRedPkgHistoryReq) ProtoMessage()               {}
func (*GetRedPkgHistoryReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{59} }

func (m *GetRedPkgHistoryReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetRedPkgHistoryReq) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

func (m *GetRedPkgHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetRedPkgHistoryReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

type GetRedPkgHistoryResp struct {
	RecordList []*RedPkgFetchRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList" json:"record_list,omitempty"`
}

func (m *GetRedPkgHistoryResp) Reset()                    { *m = GetRedPkgHistoryResp{} }
func (m *GetRedPkgHistoryResp) String() string            { return proto.CompactTextString(m) }
func (*GetRedPkgHistoryResp) ProtoMessage()               {}
func (*GetRedPkgHistoryResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{60} }

func (m *GetRedPkgHistoryResp) GetRecordList() []*RedPkgFetchRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

// ================================================================
// 查红包明细
// ================================================================
type GetRedPkgDetailReq struct {
	GuildId  uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	RedPkgId uint32 `protobuf:"varint,2,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
}

func (m *GetRedPkgDetailReq) Reset()                    { *m = GetRedPkgDetailReq{} }
func (m *GetRedPkgDetailReq) String() string            { return proto.CompactTextString(m) }
func (*GetRedPkgDetailReq) ProtoMessage()               {}
func (*GetRedPkgDetailReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{61} }

func (m *GetRedPkgDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetRedPkgDetailReq) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

type RedPkgDetail struct {
	RedPkgId       uint32 `protobuf:"varint,1,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
	GuildId        uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	GiftPkgId      uint32 `protobuf:"varint,3,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	CreatorUid     uint32 `protobuf:"varint,4,req,name=creator_uid,json=creatorUid" json:"creator_uid"`
	SerialFetchNum uint32 `protobuf:"varint,5,req,name=serial_fetch_num,json=serialFetchNum" json:"serial_fetch_num"`
	SerialTotalNum uint32 `protobuf:"varint,6,req,name=serial_total_num,json=serialTotalNum" json:"serial_total_num"`
	CreateTime     uint32 `protobuf:"varint,7,req,name=create_time,json=createTime" json:"create_time"`
	UpdateTime     uint32 `protobuf:"varint,8,req,name=update_time,json=updateTime" json:"update_time"`
}

func (m *RedPkgDetail) Reset()                    { *m = RedPkgDetail{} }
func (m *RedPkgDetail) String() string            { return proto.CompactTextString(m) }
func (*RedPkgDetail) ProtoMessage()               {}
func (*RedPkgDetail) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{62} }

func (m *RedPkgDetail) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

func (m *RedPkgDetail) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RedPkgDetail) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *RedPkgDetail) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *RedPkgDetail) GetSerialFetchNum() uint32 {
	if m != nil {
		return m.SerialFetchNum
	}
	return 0
}

func (m *RedPkgDetail) GetSerialTotalNum() uint32 {
	if m != nil {
		return m.SerialTotalNum
	}
	return 0
}

func (m *RedPkgDetail) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *RedPkgDetail) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetRedPkgDetailResp struct {
	RedPkgDetail *RedPkgDetail `protobuf:"bytes,1,req,name=red_pkg_detail,json=redPkgDetail" json:"red_pkg_detail,omitempty"`
}

func (m *GetRedPkgDetailResp) Reset()                    { *m = GetRedPkgDetailResp{} }
func (m *GetRedPkgDetailResp) String() string            { return proto.CompactTextString(m) }
func (*GetRedPkgDetailResp) ProtoMessage()               {}
func (*GetRedPkgDetailResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{63} }

func (m *GetRedPkgDetailResp) GetRedPkgDetail() *RedPkgDetail {
	if m != nil {
		return m.RedPkgDetail
	}
	return nil
}

// ======================================================================
// 给会公分发礼包
// ======================================================================
type GiveGiftPkgToGuildReq struct {
	GiftPkgId   uint32 `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	GuildId     uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	OfferNumber uint32 `protobuf:"varint,3,req,name=offer_number,json=offerNumber" json:"offer_number"`
	OpAccount   string `protobuf:"bytes,4,req,name=op_account,json=opAccount" json:"op_account"`
	GuildName   string `protobuf:"bytes,5,req,name=guild_name,json=guildName" json:"guild_name"`
}

func (m *GiveGiftPkgToGuildReq) Reset()                    { *m = GiveGiftPkgToGuildReq{} }
func (m *GiveGiftPkgToGuildReq) String() string            { return proto.CompactTextString(m) }
func (*GiveGiftPkgToGuildReq) ProtoMessage()               {}
func (*GiveGiftPkgToGuildReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{64} }

func (m *GiveGiftPkgToGuildReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *GiveGiftPkgToGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GiveGiftPkgToGuildReq) GetOfferNumber() uint32 {
	if m != nil {
		return m.OfferNumber
	}
	return 0
}

func (m *GiveGiftPkgToGuildReq) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

func (m *GiveGiftPkgToGuildReq) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

// ================================================================
// 查用户是否领取过某个红包
// ================================================================
type CheckIfFetchRedpkgReq struct {
	GuildId  uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	RedPkgId uint32 `protobuf:"varint,2,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
}

func (m *CheckIfFetchRedpkgReq) Reset()                    { *m = CheckIfFetchRedpkgReq{} }
func (m *CheckIfFetchRedpkgReq) String() string            { return proto.CompactTextString(m) }
func (*CheckIfFetchRedpkgReq) ProtoMessage()               {}
func (*CheckIfFetchRedpkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{65} }

func (m *CheckIfFetchRedpkgReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CheckIfFetchRedpkgReq) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

type CheckIfFetchRedpkgResp struct {
	IsFetch bool `protobuf:"varint,1,req,name=is_fetch,json=isFetch" json:"is_fetch"`
}

func (m *CheckIfFetchRedpkgResp) Reset()                    { *m = CheckIfFetchRedpkgResp{} }
func (m *CheckIfFetchRedpkgResp) String() string            { return proto.CompactTextString(m) }
func (*CheckIfFetchRedpkgResp) ProtoMessage()               {}
func (*CheckIfFetchRedpkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{66} }

func (m *CheckIfFetchRedpkgResp) GetIsFetch() bool {
	if m != nil {
		return m.IsFetch
	}
	return false
}

// ==================================================================
// 申请礼包包含未处理记录的公会列表
// ==================================================================
type StGuildApplyTimePkg struct {
	GuildId       uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	LastApplyTime uint32 `protobuf:"varint,2,req,name=last_apply_time,json=lastApplyTime" json:"last_apply_time"`
}

func (m *StGuildApplyTimePkg) Reset()                    { *m = StGuildApplyTimePkg{} }
func (m *StGuildApplyTimePkg) String() string            { return proto.CompactTextString(m) }
func (*StGuildApplyTimePkg) ProtoMessage()               {}
func (*StGuildApplyTimePkg) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{67} }

func (m *StGuildApplyTimePkg) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StGuildApplyTimePkg) GetLastApplyTime() uint32 {
	if m != nil {
		return m.LastApplyTime
	}
	return 0
}

type GetApplyingGuildListReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetApplyingGuildListReq) Reset()                    { *m = GetApplyingGuildListReq{} }
func (m *GetApplyingGuildListReq) String() string            { return proto.CompactTextString(m) }
func (*GetApplyingGuildListReq) ProtoMessage()               {}
func (*GetApplyingGuildListReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{68} }

func (m *GetApplyingGuildListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetApplyingGuildListResp struct {
	GuildApplyTimeList []*StGuildApplyTimePkg `protobuf:"bytes,1,rep,name=guild_apply_time_list,json=guildApplyTimeList" json:"guild_apply_time_list,omitempty"`
}

func (m *GetApplyingGuildListResp) Reset()                    { *m = GetApplyingGuildListResp{} }
func (m *GetApplyingGuildListResp) String() string            { return proto.CompactTextString(m) }
func (*GetApplyingGuildListResp) ProtoMessage()               {}
func (*GetApplyingGuildListResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{69} }

func (m *GetApplyingGuildListResp) GetGuildApplyTimeList() []*StGuildApplyTimePkg {
	if m != nil {
		return m.GuildApplyTimeList
	}
	return nil
}

type GetApplyedGuildListReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetApplyedGuildListReq) Reset()                    { *m = GetApplyedGuildListReq{} }
func (m *GetApplyedGuildListReq) String() string            { return proto.CompactTextString(m) }
func (*GetApplyedGuildListReq) ProtoMessage()               {}
func (*GetApplyedGuildListReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{70} }

func (m *GetApplyedGuildListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetApplyedGuildListResp struct {
	GuildApplyTimeList []*StGuildApplyTimePkg `protobuf:"bytes,1,rep,name=guild_apply_time_list,json=guildApplyTimeList" json:"guild_apply_time_list,omitempty"`
}

func (m *GetApplyedGuildListResp) Reset()                    { *m = GetApplyedGuildListResp{} }
func (m *GetApplyedGuildListResp) String() string            { return proto.CompactTextString(m) }
func (*GetApplyedGuildListResp) ProtoMessage()               {}
func (*GetApplyedGuildListResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{71} }

func (m *GetApplyedGuildListResp) GetGuildApplyTimeList() []*StGuildApplyTimePkg {
	if m != nil {
		return m.GuildApplyTimeList
	}
	return nil
}

type StGuildGiftPkgStatInfo struct {
	TotalCount        uint32 `protobuf:"varint,1,req,name=total_count,json=totalCount" json:"total_count"`
	TotalFetchedCount uint32 `protobuf:"varint,2,req,name=total_fetched_count,json=totalFetchedCount" json:"total_fetched_count"`
	StorageFetchNum   uint32 `protobuf:"varint,3,req,name=storage_fetch_num,json=storageFetchNum" json:"storage_fetch_num"`
	StorageTotalNum   uint32 `protobuf:"varint,4,req,name=storage_total_num,json=storageTotalNum" json:"storage_total_num"`
	PublicFetchNum    uint32 `protobuf:"varint,5,req,name=public_fetch_num,json=publicFetchNum" json:"public_fetch_num"`
	PublicTotalNum    uint32 `protobuf:"varint,6,req,name=public_total_num,json=publicTotalNum" json:"public_total_num"`
}

func (m *StGuildGiftPkgStatInfo) Reset()                    { *m = StGuildGiftPkgStatInfo{} }
func (m *StGuildGiftPkgStatInfo) String() string            { return proto.CompactTextString(m) }
func (*StGuildGiftPkgStatInfo) ProtoMessage()               {}
func (*StGuildGiftPkgStatInfo) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{72} }

func (m *StGuildGiftPkgStatInfo) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *StGuildGiftPkgStatInfo) GetTotalFetchedCount() uint32 {
	if m != nil {
		return m.TotalFetchedCount
	}
	return 0
}

func (m *StGuildGiftPkgStatInfo) GetStorageFetchNum() uint32 {
	if m != nil {
		return m.StorageFetchNum
	}
	return 0
}

func (m *StGuildGiftPkgStatInfo) GetStorageTotalNum() uint32 {
	if m != nil {
		return m.StorageTotalNum
	}
	return 0
}

func (m *StGuildGiftPkgStatInfo) GetPublicFetchNum() uint32 {
	if m != nil {
		return m.PublicFetchNum
	}
	return 0
}

func (m *StGuildGiftPkgStatInfo) GetPublicTotalNum() uint32 {
	if m != nil {
		return m.PublicTotalNum
	}
	return 0
}

// 某个公会的礼包统计数据(按游戏进行分组)
type StGuildGameGiftPkgStatInfo struct {
	GameId            uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	TotalCount        uint32 `protobuf:"varint,2,req,name=total_count,json=totalCount" json:"total_count"`
	TotalFetchedCount uint32 `protobuf:"varint,3,req,name=total_fetched_count,json=totalFetchedCount" json:"total_fetched_count"`
	PublicFetchNum    uint32 `protobuf:"varint,4,opt,name=public_fetch_num,json=publicFetchNum" json:"public_fetch_num"`
	PublicTotalNum    uint32 `protobuf:"varint,5,opt,name=public_total_num,json=publicTotalNum" json:"public_total_num"`
}

func (m *StGuildGameGiftPkgStatInfo) Reset()         { *m = StGuildGameGiftPkgStatInfo{} }
func (m *StGuildGameGiftPkgStatInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildGameGiftPkgStatInfo) ProtoMessage()    {}
func (*StGuildGameGiftPkgStatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{73}
}

func (m *StGuildGameGiftPkgStatInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *StGuildGameGiftPkgStatInfo) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *StGuildGameGiftPkgStatInfo) GetTotalFetchedCount() uint32 {
	if m != nil {
		return m.TotalFetchedCount
	}
	return 0
}

func (m *StGuildGameGiftPkgStatInfo) GetPublicFetchNum() uint32 {
	if m != nil {
		return m.PublicFetchNum
	}
	return 0
}

func (m *StGuildGameGiftPkgStatInfo) GetPublicTotalNum() uint32 {
	if m != nil {
		return m.PublicTotalNum
	}
	return 0
}

type GetGuildGiftPkgStatInfoReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetGuildGiftPkgStatInfoReq) Reset()         { *m = GetGuildGiftPkgStatInfoReq{} }
func (m *GetGuildGiftPkgStatInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftPkgStatInfoReq) ProtoMessage()    {}
func (*GetGuildGiftPkgStatInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{74}
}

func (m *GetGuildGiftPkgStatInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildGiftPkgStatInfoResp struct {
	StatInfo     *StGuildGiftPkgStatInfo       `protobuf:"bytes,1,req,name=stat_info,json=statInfo" json:"stat_info,omitempty"`
	GameStatList []*StGuildGameGiftPkgStatInfo `protobuf:"bytes,2,rep,name=game_stat_list,json=gameStatList" json:"game_stat_list,omitempty"`
}

func (m *GetGuildGiftPkgStatInfoResp) Reset()         { *m = GetGuildGiftPkgStatInfoResp{} }
func (m *GetGuildGiftPkgStatInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftPkgStatInfoResp) ProtoMessage()    {}
func (*GetGuildGiftPkgStatInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{75}
}

func (m *GetGuildGiftPkgStatInfoResp) GetStatInfo() *StGuildGiftPkgStatInfo {
	if m != nil {
		return m.StatInfo
	}
	return nil
}

func (m *GetGuildGiftPkgStatInfoResp) GetGameStatList() []*StGuildGameGiftPkgStatInfo {
	if m != nil {
		return m.GameStatList
	}
	return nil
}

// =================================================================
// 未分配给公会的礼包序列号数量
// =================================================================
type CountRestGiftPkgSerialReq struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *CountRestGiftPkgSerialReq) Reset()         { *m = CountRestGiftPkgSerialReq{} }
func (m *CountRestGiftPkgSerialReq) String() string { return proto.CompactTextString(m) }
func (*CountRestGiftPkgSerialReq) ProtoMessage()    {}
func (*CountRestGiftPkgSerialReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{76}
}

func (m *CountRestGiftPkgSerialReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type CountRestGiftPkgSerialResp struct {
	Total uint32 `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *CountRestGiftPkgSerialResp) Reset()         { *m = CountRestGiftPkgSerialResp{} }
func (m *CountRestGiftPkgSerialResp) String() string { return proto.CompactTextString(m) }
func (*CountRestGiftPkgSerialResp) ProtoMessage()    {}
func (*CountRestGiftPkgSerialResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{77}
}

func (m *CountRestGiftPkgSerialResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// =================================================================
// 根据申请ID，查申请记录
// =================================================================
type GetApplyReq struct {
	ApplyId uint32 `protobuf:"varint,1,req,name=apply_id,json=applyId" json:"apply_id"`
}

func (m *GetApplyReq) Reset()                    { *m = GetApplyReq{} }
func (m *GetApplyReq) String() string            { return proto.CompactTextString(m) }
func (*GetApplyReq) ProtoMessage()               {}
func (*GetApplyReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{78} }

func (m *GetApplyReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

type GetApplyResp struct {
	Apply *StGiftPkgApply `protobuf:"bytes,1,req,name=apply" json:"apply,omitempty"`
}

func (m *GetApplyResp) Reset()                    { *m = GetApplyResp{} }
func (m *GetApplyResp) String() string            { return proto.CompactTextString(m) }
func (*GetApplyResp) ProtoMessage()               {}
func (*GetApplyResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{79} }

func (m *GetApplyResp) GetApply() *StGiftPkgApply {
	if m != nil {
		return m.Apply
	}
	return nil
}

// =================================================================
// 查后台操作历史记录
// =================================================================
type GetGiftpkgOpHistoryReq struct {
	RecordBegintime uint32 `protobuf:"varint,1,req,name=record_begintime,json=recordBegintime" json:"record_begintime"`
	RecordEndtime   uint32 `protobuf:"varint,2,req,name=record_endtime,json=recordEndtime" json:"record_endtime"`
	Offset          uint32 `protobuf:"varint,3,req,name=offset" json:"offset"`
	Size_           uint32 `protobuf:"varint,4,req,name=size" json:"size"`
	OpAccount       string `protobuf:"bytes,5,req,name=op_account,json=opAccount" json:"op_account"`
}

func (m *GetGiftpkgOpHistoryReq) Reset()                    { *m = GetGiftpkgOpHistoryReq{} }
func (m *GetGiftpkgOpHistoryReq) String() string            { return proto.CompactTextString(m) }
func (*GetGiftpkgOpHistoryReq) ProtoMessage()               {}
func (*GetGiftpkgOpHistoryReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{80} }

func (m *GetGiftpkgOpHistoryReq) GetRecordBegintime() uint32 {
	if m != nil {
		return m.RecordBegintime
	}
	return 0
}

func (m *GetGiftpkgOpHistoryReq) GetRecordEndtime() uint32 {
	if m != nil {
		return m.RecordEndtime
	}
	return 0
}

func (m *GetGiftpkgOpHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGiftpkgOpHistoryReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

func (m *GetGiftpkgOpHistoryReq) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

// 礼包操作记录
type GiftpkgOpRecord struct {
	OpTime    uint32 `protobuf:"varint,1,req,name=op_time,json=opTime" json:"op_time"`
	OpAccount string `protobuf:"bytes,2,req,name=op_account,json=opAccount" json:"op_account"`
	OpContent string `protobuf:"bytes,3,req,name=op_content,json=opContent" json:"op_content"`
	OpType    uint32 `protobuf:"varint,4,req,name=op_type,json=opType" json:"op_type"`
}

func (m *GiftpkgOpRecord) Reset()                    { *m = GiftpkgOpRecord{} }
func (m *GiftpkgOpRecord) String() string            { return proto.CompactTextString(m) }
func (*GiftpkgOpRecord) ProtoMessage()               {}
func (*GiftpkgOpRecord) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{81} }

func (m *GiftpkgOpRecord) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

func (m *GiftpkgOpRecord) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

func (m *GiftpkgOpRecord) GetOpContent() string {
	if m != nil {
		return m.OpContent
	}
	return ""
}

func (m *GiftpkgOpRecord) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

type GetGiftpkgOpHistoryResp struct {
	Records []*GiftpkgOpRecord `protobuf:"bytes,1,rep,name=records" json:"records,omitempty"`
	Total   uint32             `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetGiftpkgOpHistoryResp) Reset()                    { *m = GetGiftpkgOpHistoryResp{} }
func (m *GetGiftpkgOpHistoryResp) String() string            { return proto.CompactTextString(m) }
func (*GetGiftpkgOpHistoryResp) ProtoMessage()               {}
func (*GetGiftpkgOpHistoryResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{82} }

func (m *GetGiftpkgOpHistoryResp) GetRecords() []*GiftpkgOpRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

func (m *GetGiftpkgOpHistoryResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// ==================================================================
// 申请礼包包含未处理记录的公会列表
// ==================================================================
type GetDeviceLastFetchReq struct {
	DeviceId string `protobuf:"bytes,1,req,name=device_id,json=deviceId" json:"device_id"`
}

func (m *GetDeviceLastFetchReq) Reset()                    { *m = GetDeviceLastFetchReq{} }
func (m *GetDeviceLastFetchReq) String() string            { return proto.CompactTextString(m) }
func (*GetDeviceLastFetchReq) ProtoMessage()               {}
func (*GetDeviceLastFetchReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{83} }

func (m *GetDeviceLastFetchReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetDeviceLastFetchResp struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LastFetchTime uint32 `protobuf:"varint,2,req,name=last_fetch_time,json=lastFetchTime" json:"last_fetch_time"`
	GiftPkgId     uint32 `protobuf:"varint,3,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *GetDeviceLastFetchResp) Reset()                    { *m = GetDeviceLastFetchResp{} }
func (m *GetDeviceLastFetchResp) String() string            { return proto.CompactTextString(m) }
func (*GetDeviceLastFetchResp) ProtoMessage()               {}
func (*GetDeviceLastFetchResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{84} }

func (m *GetDeviceLastFetchResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetDeviceLastFetchResp) GetLastFetchTime() uint32 {
	if m != nil {
		return m.LastFetchTime
	}
	return 0
}

func (m *GetDeviceLastFetchResp) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

// ==================================================================
// 查看公会最后申请某个礼包官方的处理时间
// ==================================================================
type GetOfficalLastOpTimeReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GiftpkgId uint32 `protobuf:"varint,2,req,name=giftpkg_id,json=giftpkgId" json:"giftpkg_id"`
}

func (m *GetOfficalLastOpTimeReq) Reset()                    { *m = GetOfficalLastOpTimeReq{} }
func (m *GetOfficalLastOpTimeReq) String() string            { return proto.CompactTextString(m) }
func (*GetOfficalLastOpTimeReq) ProtoMessage()               {}
func (*GetOfficalLastOpTimeReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{85} }

func (m *GetOfficalLastOpTimeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetOfficalLastOpTimeReq) GetGiftpkgId() uint32 {
	if m != nil {
		return m.GiftpkgId
	}
	return 0
}

type GetOfficalLastOpTimeResp struct {
	LastOpTime uint32 `protobuf:"varint,1,req,name=last_op_time,json=lastOpTime" json:"last_op_time"`
}

func (m *GetOfficalLastOpTimeResp) Reset()                    { *m = GetOfficalLastOpTimeResp{} }
func (m *GetOfficalLastOpTimeResp) String() string            { return proto.CompactTextString(m) }
func (*GetOfficalLastOpTimeResp) ProtoMessage()               {}
func (*GetOfficalLastOpTimeResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{86} }

func (m *GetOfficalLastOpTimeResp) GetLastOpTime() uint32 {
	if m != nil {
		return m.LastOpTime
	}
	return 0
}

// ==================================================================
// 礼包删除
// ==================================================================
type DeleteGuildSourceGiftPkgReq struct {
	GiftpkgId uint32 `protobuf:"varint,1,req,name=giftpkg_id,json=giftpkgId" json:"giftpkg_id"`
	GuildId   uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	OpAccount string `protobuf:"bytes,3,req,name=op_account,json=opAccount" json:"op_account"`
}

func (m *DeleteGuildSourceGiftPkgReq) Reset()         { *m = DeleteGuildSourceGiftPkgReq{} }
func (m *DeleteGuildSourceGiftPkgReq) String() string { return proto.CompactTextString(m) }
func (*DeleteGuildSourceGiftPkgReq) ProtoMessage()    {}
func (*DeleteGuildSourceGiftPkgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{87}
}

func (m *DeleteGuildSourceGiftPkgReq) GetGiftpkgId() uint32 {
	if m != nil {
		return m.GiftpkgId
	}
	return 0
}

func (m *DeleteGuildSourceGiftPkgReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DeleteGuildSourceGiftPkgReq) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

type DeleteGuildSourceGiftPkgResp struct {
}

func (m *DeleteGuildSourceGiftPkgResp) Reset()         { *m = DeleteGuildSourceGiftPkgResp{} }
func (m *DeleteGuildSourceGiftPkgResp) String() string { return proto.CompactTextString(m) }
func (*DeleteGuildSourceGiftPkgResp) ProtoMessage()    {}
func (*DeleteGuildSourceGiftPkgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{88}
}

// ==================================================================
// 锁定分配礼包序列号给用户
// ==================================================================
type LockSerialForUserToPayReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GiftpkgId uint32 `protobuf:"varint,2,req,name=giftpkg_id,json=giftpkgId" json:"giftpkg_id"`
	Orderid   string `protobuf:"bytes,3,req,name=orderid" json:"orderid"`
}

func (m *LockSerialForUserToPayReq) Reset()         { *m = LockSerialForUserToPayReq{} }
func (m *LockSerialForUserToPayReq) String() string { return proto.CompactTextString(m) }
func (*LockSerialForUserToPayReq) ProtoMessage()    {}
func (*LockSerialForUserToPayReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{89}
}

func (m *LockSerialForUserToPayReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *LockSerialForUserToPayReq) GetGiftpkgId() uint32 {
	if m != nil {
		return m.GiftpkgId
	}
	return 0
}

func (m *LockSerialForUserToPayReq) GetOrderid() string {
	if m != nil {
		return m.Orderid
	}
	return ""
}

type LockSerialForUserToPayResp struct {
}

func (m *LockSerialForUserToPayResp) Reset()         { *m = LockSerialForUserToPayResp{} }
func (m *LockSerialForUserToPayResp) String() string { return proto.CompactTextString(m) }
func (*LockSerialForUserToPayResp) ProtoMessage()    {}
func (*LockSerialForUserToPayResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{90}
}

// ==================================================================
// 扣费成功，分配序列号给用户
// ==================================================================
type RealFetchSerialForUserReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GiftpkgId uint32 `protobuf:"varint,2,req,name=giftpkg_id,json=giftpkgId" json:"giftpkg_id"`
	Orderid   string `protobuf:"bytes,3,req,name=orderid" json:"orderid"`
}

func (m *RealFetchSerialForUserReq) Reset()         { *m = RealFetchSerialForUserReq{} }
func (m *RealFetchSerialForUserReq) String() string { return proto.CompactTextString(m) }
func (*RealFetchSerialForUserReq) ProtoMessage()    {}
func (*RealFetchSerialForUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{91}
}

func (m *RealFetchSerialForUserReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RealFetchSerialForUserReq) GetGiftpkgId() uint32 {
	if m != nil {
		return m.GiftpkgId
	}
	return 0
}

func (m *RealFetchSerialForUserReq) GetOrderid() string {
	if m != nil {
		return m.Orderid
	}
	return ""
}

type RealFetchSerialForUserResp struct {
	SerialNum string `protobuf:"bytes,1,req,name=serial_num,json=serialNum" json:"serial_num"`
}

func (m *RealFetchSerialForUserResp) Reset()         { *m = RealFetchSerialForUserResp{} }
func (m *RealFetchSerialForUserResp) String() string { return proto.CompactTextString(m) }
func (*RealFetchSerialForUserResp) ProtoMessage()    {}
func (*RealFetchSerialForUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{92}
}

func (m *RealFetchSerialForUserResp) GetSerialNum() string {
	if m != nil {
		return m.SerialNum
	}
	return ""
}

// ==================================================================
// 扣费失败，释放序列号，回复成公会可领取的状态
// ==================================================================
type ReleaseSerialForGuildReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GiftpkgId uint32 `protobuf:"varint,2,req,name=giftpkg_id,json=giftpkgId" json:"giftpkg_id"`
	Orderid   string `protobuf:"bytes,3,req,name=orderid" json:"orderid"`
}

func (m *ReleaseSerialForGuildReq) Reset()                    { *m = ReleaseSerialForGuildReq{} }
func (m *ReleaseSerialForGuildReq) String() string            { return proto.CompactTextString(m) }
func (*ReleaseSerialForGuildReq) ProtoMessage()               {}
func (*ReleaseSerialForGuildReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg, []int{93} }

func (m *ReleaseSerialForGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ReleaseSerialForGuildReq) GetGiftpkgId() uint32 {
	if m != nil {
		return m.GiftpkgId
	}
	return 0
}

func (m *ReleaseSerialForGuildReq) GetOrderid() string {
	if m != nil {
		return m.Orderid
	}
	return ""
}

type ReleaseSerialForGuildResp struct {
}

func (m *ReleaseSerialForGuildResp) Reset()         { *m = ReleaseSerialForGuildResp{} }
func (m *ReleaseSerialForGuildResp) String() string { return proto.CompactTextString(m) }
func (*ReleaseSerialForGuildResp) ProtoMessage()    {}
func (*ReleaseSerialForGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{94}
}

// ==================================================================
// 设置公会礼包的价格
// ==================================================================
type SetGuildPkgRedDiamondPriceReq struct {
	GuildId        uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GiftpkgId      uint32 `protobuf:"varint,2,req,name=giftpkg_id,json=giftpkgId" json:"giftpkg_id"`
	RedDiamondCost uint32 `protobuf:"varint,3,req,name=red_diamond_cost,json=redDiamondCost" json:"red_diamond_cost"`
}

func (m *SetGuildPkgRedDiamondPriceReq) Reset()         { *m = SetGuildPkgRedDiamondPriceReq{} }
func (m *SetGuildPkgRedDiamondPriceReq) String() string { return proto.CompactTextString(m) }
func (*SetGuildPkgRedDiamondPriceReq) ProtoMessage()    {}
func (*SetGuildPkgRedDiamondPriceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{95}
}

func (m *SetGuildPkgRedDiamondPriceReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetGuildPkgRedDiamondPriceReq) GetGiftpkgId() uint32 {
	if m != nil {
		return m.GiftpkgId
	}
	return 0
}

func (m *SetGuildPkgRedDiamondPriceReq) GetRedDiamondCost() uint32 {
	if m != nil {
		return m.RedDiamondCost
	}
	return 0
}

type SetGuildPkgRedDiamondPriceResp struct {
}

func (m *SetGuildPkgRedDiamondPriceResp) Reset()         { *m = SetGuildPkgRedDiamondPriceResp{} }
func (m *SetGuildPkgRedDiamondPriceResp) String() string { return proto.CompactTextString(m) }
func (*SetGuildPkgRedDiamondPriceResp) ProtoMessage()    {}
func (*SetGuildPkgRedDiamondPriceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{96}
}

// ================================================================
// 用户查看已经获取到的礼包序列号
// ================================================================
type GetUserPkgSerialListNewByIdReq struct {
	GiftPkgId uint32 `protobuf:"varint,1,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *GetUserPkgSerialListNewByIdReq) Reset()         { *m = GetUserPkgSerialListNewByIdReq{} }
func (m *GetUserPkgSerialListNewByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPkgSerialListNewByIdReq) ProtoMessage()    {}
func (*GetUserPkgSerialListNewByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{97}
}

func (m *GetUserPkgSerialListNewByIdReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

type GetUserPkgSerialListNewByIdResp struct {
	SerialList []*StGiftPkgSerial `protobuf:"bytes,1,rep,name=serial_list,json=serialList" json:"serial_list,omitempty"`
}

func (m *GetUserPkgSerialListNewByIdResp) Reset()         { *m = GetUserPkgSerialListNewByIdResp{} }
func (m *GetUserPkgSerialListNewByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPkgSerialListNewByIdResp) ProtoMessage()    {}
func (*GetUserPkgSerialListNewByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{98}
}

func (m *GetUserPkgSerialListNewByIdResp) GetSerialList() []*StGiftPkgSerial {
	if m != nil {
		return m.SerialList
	}
	return nil
}

// ================================================================
// 根据 公会(可同时查多个) 查 某些游戏或所有游戏 的礼包总数
// ================================================================
type BatchGetGiftPackageStatInfoReq struct {
	GuildIdList []uint32 `protobuf:"varint,1,rep,name=guild_id_list,json=guildIdList" json:"guild_id_list,omitempty"`
	GameIdList  []uint32 `protobuf:"varint,2,rep,name=game_id_list,json=gameIdList" json:"game_id_list,omitempty"`
}

func (m *BatchGetGiftPackageStatInfoReq) Reset()         { *m = BatchGetGiftPackageStatInfoReq{} }
func (m *BatchGetGiftPackageStatInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGiftPackageStatInfoReq) ProtoMessage()    {}
func (*BatchGetGiftPackageStatInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{99}
}

func (m *BatchGetGiftPackageStatInfoReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

func (m *BatchGetGiftPackageStatInfoReq) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

type StGuildGameGiftPackageStatInfo struct {
	GuildId  uint32                  `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GameId   uint32                  `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	StatInfo *StGuildGiftPkgStatInfo `protobuf:"bytes,3,req,name=stat_info,json=statInfo" json:"stat_info,omitempty"`
}

func (m *StGuildGameGiftPackageStatInfo) Reset()         { *m = StGuildGameGiftPackageStatInfo{} }
func (m *StGuildGameGiftPackageStatInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildGameGiftPackageStatInfo) ProtoMessage()    {}
func (*StGuildGameGiftPackageStatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{100}
}

func (m *StGuildGameGiftPackageStatInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StGuildGameGiftPackageStatInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *StGuildGameGiftPackageStatInfo) GetStatInfo() *StGuildGiftPkgStatInfo {
	if m != nil {
		return m.StatInfo
	}
	return nil
}

type BatchGetGiftPackageStatInfoResp struct {
	StatList []*StGuildGameGiftPackageStatInfo `protobuf:"bytes,1,rep,name=stat_list,json=statList" json:"stat_list,omitempty"`
}

func (m *BatchGetGiftPackageStatInfoResp) Reset()         { *m = BatchGetGiftPackageStatInfoResp{} }
func (m *BatchGetGiftPackageStatInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGiftPackageStatInfoResp) ProtoMessage()    {}
func (*BatchGetGiftPackageStatInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{101}
}

func (m *BatchGetGiftPackageStatInfoResp) GetStatList() []*StGuildGameGiftPackageStatInfo {
	if m != nil {
		return m.StatList
	}
	return nil
}

type FetchActGiftPkgCollectionReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId   uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	DeviceId string `protobuf:"bytes,3,req,name=device_id,json=deviceId" json:"device_id"`
}

func (m *FetchActGiftPkgCollectionReq) Reset()         { *m = FetchActGiftPkgCollectionReq{} }
func (m *FetchActGiftPkgCollectionReq) String() string { return proto.CompactTextString(m) }
func (*FetchActGiftPkgCollectionReq) ProtoMessage()    {}
func (*FetchActGiftPkgCollectionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{102}
}

func (m *FetchActGiftPkgCollectionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FetchActGiftPkgCollectionReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *FetchActGiftPkgCollectionReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type FetchActGiftPkgCollectionResp struct {
	GiftPkgSerial []string `protobuf:"bytes,1,rep,name=gift_pkg_serial,json=giftPkgSerial" json:"gift_pkg_serial,omitempty"`
}

func (m *FetchActGiftPkgCollectionResp) Reset()         { *m = FetchActGiftPkgCollectionResp{} }
func (m *FetchActGiftPkgCollectionResp) String() string { return proto.CompactTextString(m) }
func (*FetchActGiftPkgCollectionResp) ProtoMessage()    {}
func (*FetchActGiftPkgCollectionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{103}
}

func (m *FetchActGiftPkgCollectionResp) GetGiftPkgSerial() []string {
	if m != nil {
		return m.GiftPkgSerial
	}
	return nil
}

type GetActGiftCollcetionFetStatusReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId   uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	DeviceId string `protobuf:"bytes,3,req,name=device_id,json=deviceId" json:"device_id"`
}

func (m *GetActGiftCollcetionFetStatusReq) Reset()         { *m = GetActGiftCollcetionFetStatusReq{} }
func (m *GetActGiftCollcetionFetStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetActGiftCollcetionFetStatusReq) ProtoMessage()    {}
func (*GetActGiftCollcetionFetStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{104}
}

func (m *GetActGiftCollcetionFetStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetActGiftCollcetionFetStatusReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetActGiftCollcetionFetStatusReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetActGiftCollcetionFetStatusResp struct {
	FetchStatus uint32 `protobuf:"varint,1,req,name=fetch_status,json=fetchStatus" json:"fetch_status"`
}

func (m *GetActGiftCollcetionFetStatusResp) Reset()         { *m = GetActGiftCollcetionFetStatusResp{} }
func (m *GetActGiftCollcetionFetStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetActGiftCollcetionFetStatusResp) ProtoMessage()    {}
func (*GetActGiftCollcetionFetStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{105}
}

func (m *GetActGiftCollcetionFetStatusResp) GetFetchStatus() uint32 {
	if m != nil {
		return m.FetchStatus
	}
	return 0
}

type GetActGiftPkgCollectionConfReq struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *GetActGiftPkgCollectionConfReq) Reset()         { *m = GetActGiftPkgCollectionConfReq{} }
func (m *GetActGiftPkgCollectionConfReq) String() string { return proto.CompactTextString(m) }
func (*GetActGiftPkgCollectionConfReq) ProtoMessage()    {}
func (*GetActGiftPkgCollectionConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{106}
}

func (m *GetActGiftPkgCollectionConfReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetActGiftPkgCollectionConfResp struct {
	PkgIdList string `protobuf:"bytes,1,req,name=pkg_id_list,json=pkgIdList" json:"pkg_id_list"`
	PicUrl    string `protobuf:"bytes,2,req,name=pic_url,json=picUrl" json:"pic_url"`
}

func (m *GetActGiftPkgCollectionConfResp) Reset()         { *m = GetActGiftPkgCollectionConfResp{} }
func (m *GetActGiftPkgCollectionConfResp) String() string { return proto.CompactTextString(m) }
func (*GetActGiftPkgCollectionConfResp) ProtoMessage()    {}
func (*GetActGiftPkgCollectionConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg, []int{107}
}

func (m *GetActGiftPkgCollectionConfResp) GetPkgIdList() string {
	if m != nil {
		return m.PkgIdList
	}
	return ""
}

func (m *GetActGiftPkgCollectionConfResp) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func init() {
	proto.RegisterType((*UserFetchGiftPkgReq)(nil), "Giftpkg.userFetchGiftPkgReq")
	proto.RegisterType((*UserFetchGiftPkgResp)(nil), "Giftpkg.userFetchGiftPkgResp")
	proto.RegisterType((*UserFetchActivityGiftPkgReq)(nil), "Giftpkg.UserFetchActivityGiftPkgReq")
	proto.RegisterType((*UserFetchActivityGiftPkgResp)(nil), "Giftpkg.UserFetchActivityGiftPkgResp")
	proto.RegisterType((*CheckActivityGiftPkgFetchStatusReq)(nil), "Giftpkg.CheckActivityGiftPkgFetchStatusReq")
	proto.RegisterType((*CheckActivityGiftPkgFetchStatusResp)(nil), "Giftpkg.CheckActivityGiftPkgFetchStatusResp")
	proto.RegisterType((*GetUserGiftPkgSerialReq)(nil), "Giftpkg.GetUserGiftPkgSerialReq")
	proto.RegisterType((*GetUserGiftPkgSerialResp)(nil), "Giftpkg.GetUserGiftPkgSerialResp")
	proto.RegisterType((*StGiftPkg)(nil), "Giftpkg.stGiftPkg")
	proto.RegisterType((*StGiftPkgSerial)(nil), "Giftpkg.stGiftPkgSerial")
	proto.RegisterType((*GetGiftPkgListReq)(nil), "Giftpkg.GetGiftPkgListReq")
	proto.RegisterType((*GetGiftPkgListResp)(nil), "Giftpkg.GetGiftPkgListResp")
	proto.RegisterType((*GetGiftPkgReq)(nil), "Giftpkg.GetGiftPkgReq")
	proto.RegisterType((*GetGiftPkgResp)(nil), "Giftpkg.GetGiftPkgResp")
	proto.RegisterType((*GetMyGiftPkgSerialListReq)(nil), "Giftpkg.GetMyGiftPkgSerialListReq")
	proto.RegisterType((*GetMyGiftPkgSerialListResp)(nil), "Giftpkg.GetMyGiftPkgSerialListResp")
	proto.RegisterType((*GuildApplyGiftPkgReq)(nil), "Giftpkg.GuildApplyGiftPkgReq")
	proto.RegisterType((*GuildApplyGiftPkgResp)(nil), "Giftpkg.GuildApplyGiftPkgResp")
	proto.RegisterType((*GetGiftPkgApplyListReq)(nil), "Giftpkg.GetGiftPkgApplyListReq")
	proto.RegisterType((*StGiftPkgApply)(nil), "Giftpkg.stGiftPkgApply")
	proto.RegisterType((*GetGiftPkgApplyListResp)(nil), "Giftpkg.GetGiftPkgApplyListResp")
	proto.RegisterType((*GetGuildApplyingListReq)(nil), "Giftpkg.GetGuildApplyingListReq")
	proto.RegisterType((*GetGuildApplyingListResp)(nil), "Giftpkg.GetGuildApplyingListResp")
	proto.RegisterType((*TypeGiftPkgReq)(nil), "Giftpkg.TypeGiftPkgReq")
	proto.RegisterType((*TypeGiftPkgResp)(nil), "Giftpkg.TypeGiftPkgResp")
	proto.RegisterType((*UpdateGiftPkgReq)(nil), "Giftpkg.UpdateGiftPkgReq")
	proto.RegisterType((*UpdateGiftPkgShowStatusReq)(nil), "Giftpkg.UpdateGiftPkgShowStatusReq")
	proto.RegisterType((*TypeGiftPkgSerialReq)(nil), "Giftpkg.TypeGiftPkgSerialReq")
	proto.RegisterType((*TypeGiftPkgSerialResp)(nil), "Giftpkg.TypeGiftPkgSerialResp")
	proto.RegisterType((*TypeGiftPkgSerialToGuildReq)(nil), "Giftpkg.TypeGiftPkgSerialToGuildReq")
	proto.RegisterType((*TypeGiftPkgSerialToGuildResp)(nil), "Giftpkg.TypeGiftPkgSerialToGuildResp")
	proto.RegisterType((*BatchGetGiftPkgReq)(nil), "Giftpkg.BatchGetGiftPkgReq")
	proto.RegisterType((*BatchGetGiftPkgResp)(nil), "Giftpkg.BatchGetGiftPkgResp")
	proto.RegisterType((*RedPkgFetchRecord)(nil), "Giftpkg.RedPkgFetchRecord")
	proto.RegisterType((*StGuildGiftPkg)(nil), "Giftpkg.stGuildGiftPkg")
	proto.RegisterType((*GetGuildGiftPkgListReq)(nil), "Giftpkg.GetGuildGiftPkgListReq")
	proto.RegisterType((*GetGuildGiftPkgListResp)(nil), "Giftpkg.GetGuildGiftPkgListResp")
	proto.RegisterType((*GetGuildPkgListByGameIdReq)(nil), "Giftpkg.GetGuildPkgListByGameIdReq")
	proto.RegisterType((*GetGuildPkgListByGameIdResp)(nil), "Giftpkg.GetGuildPkgListByGameIdResp")
	proto.RegisterType((*PassGiftPkgApplyReq)(nil), "Giftpkg.PassGiftPkgApplyReq")
	proto.RegisterType((*PassGiftPkgApplyResp)(nil), "Giftpkg.PassGiftPkgApplyResp")
	proto.RegisterType((*RejectGiftPkgApplyReq)(nil), "Giftpkg.RejectGiftPkgApplyReq")
	proto.RegisterType((*RejectGiftPkgApplyResp)(nil), "Giftpkg.RejectGiftPkgApplyResp")
	proto.RegisterType((*GetGuildPkgStatusReq)(nil), "Giftpkg.GetGuildPkgStatusReq")
	proto.RegisterType((*GetGuildPkgStatusResp)(nil), "Giftpkg.GetGuildPkgStatusResp")
	proto.RegisterType((*BatchGetGuildPkgStatusReq)(nil), "Giftpkg.BatchGetGuildPkgStatusReq")
	proto.RegisterType((*BatchGetGuildPkgStatusResp)(nil), "Giftpkg.BatchGetGuildPkgStatusResp")
	proto.RegisterType((*CheckGuildPkgApplyingReq)(nil), "Giftpkg.CheckGuildPkgApplyingReq")
	proto.RegisterType((*CheckGuildPkgApplyingResp)(nil), "Giftpkg.CheckGuildPkgApplyingResp")
	proto.RegisterType((*GetGuildApplyHistoryReq)(nil), "Giftpkg.GetGuildApplyHistoryReq")
	proto.RegisterType((*GetGuildApplyHistoryResp)(nil), "Giftpkg.GetGuildApplyHistoryResp")
	proto.RegisterType((*TaohaoReq)(nil), "Giftpkg.TaohaoReq")
	proto.RegisterType((*TaohaoResp)(nil), "Giftpkg.TaohaoResp")
	proto.RegisterType((*TaohaoAddUsedCountReq)(nil), "Giftpkg.TaohaoAddUsedCountReq")
	proto.RegisterType((*TaohaoAddUsedCountResp)(nil), "Giftpkg.TaohaoAddUsedCountResp")
	proto.RegisterType((*CreateRedpkgReq)(nil), "Giftpkg.CreateRedpkgReq")
	proto.RegisterType((*CreateRedpkgResp)(nil), "Giftpkg.CreateRedpkgResp")
	proto.RegisterType((*FetchRedPkgReq)(nil), "Giftpkg.FetchRedPkgReq")
	proto.RegisterType((*FetchRedPkgResp)(nil), "Giftpkg.FetchRedPkgResp")
	proto.RegisterType((*GetRedPkgHistoryReq)(nil), "Giftpkg.GetRedPkgHistoryReq")
	proto.RegisterType((*GetRedPkgHistoryResp)(nil), "Giftpkg.GetRedPkgHistoryResp")
	proto.RegisterType((*GetRedPkgDetailReq)(nil), "Giftpkg.GetRedPkgDetailReq")
	proto.RegisterType((*RedPkgDetail)(nil), "Giftpkg.RedPkgDetail")
	proto.RegisterType((*GetRedPkgDetailResp)(nil), "Giftpkg.GetRedPkgDetailResp")
	proto.RegisterType((*GiveGiftPkgToGuildReq)(nil), "Giftpkg.GiveGiftPkgToGuildReq")
	proto.RegisterType((*CheckIfFetchRedpkgReq)(nil), "Giftpkg.CheckIfFetchRedpkgReq")
	proto.RegisterType((*CheckIfFetchRedpkgResp)(nil), "Giftpkg.CheckIfFetchRedpkgResp")
	proto.RegisterType((*StGuildApplyTimePkg)(nil), "Giftpkg.stGuildApplyTimePkg")
	proto.RegisterType((*GetApplyingGuildListReq)(nil), "Giftpkg.GetApplyingGuildListReq")
	proto.RegisterType((*GetApplyingGuildListResp)(nil), "Giftpkg.GetApplyingGuildListResp")
	proto.RegisterType((*GetApplyedGuildListReq)(nil), "Giftpkg.GetApplyedGuildListReq")
	proto.RegisterType((*GetApplyedGuildListResp)(nil), "Giftpkg.GetApplyedGuildListResp")
	proto.RegisterType((*StGuildGiftPkgStatInfo)(nil), "Giftpkg.stGuildGiftPkgStatInfo")
	proto.RegisterType((*StGuildGameGiftPkgStatInfo)(nil), "Giftpkg.stGuildGameGiftPkgStatInfo")
	proto.RegisterType((*GetGuildGiftPkgStatInfoReq)(nil), "Giftpkg.GetGuildGiftPkgStatInfoReq")
	proto.RegisterType((*GetGuildGiftPkgStatInfoResp)(nil), "Giftpkg.GetGuildGiftPkgStatInfoResp")
	proto.RegisterType((*CountRestGiftPkgSerialReq)(nil), "Giftpkg.CountRestGiftPkgSerialReq")
	proto.RegisterType((*CountRestGiftPkgSerialResp)(nil), "Giftpkg.CountRestGiftPkgSerialResp")
	proto.RegisterType((*GetApplyReq)(nil), "Giftpkg.GetApplyReq")
	proto.RegisterType((*GetApplyResp)(nil), "Giftpkg.GetApplyResp")
	proto.RegisterType((*GetGiftpkgOpHistoryReq)(nil), "Giftpkg.GetGiftpkgOpHistoryReq")
	proto.RegisterType((*GiftpkgOpRecord)(nil), "Giftpkg.GiftpkgOpRecord")
	proto.RegisterType((*GetGiftpkgOpHistoryResp)(nil), "Giftpkg.GetGiftpkgOpHistoryResp")
	proto.RegisterType((*GetDeviceLastFetchReq)(nil), "Giftpkg.GetDeviceLastFetchReq")
	proto.RegisterType((*GetDeviceLastFetchResp)(nil), "Giftpkg.GetDeviceLastFetchResp")
	proto.RegisterType((*GetOfficalLastOpTimeReq)(nil), "Giftpkg.GetOfficalLastOpTimeReq")
	proto.RegisterType((*GetOfficalLastOpTimeResp)(nil), "Giftpkg.GetOfficalLastOpTimeResp")
	proto.RegisterType((*DeleteGuildSourceGiftPkgReq)(nil), "Giftpkg.DeleteGuildSourceGiftPkgReq")
	proto.RegisterType((*DeleteGuildSourceGiftPkgResp)(nil), "Giftpkg.DeleteGuildSourceGiftPkgResp")
	proto.RegisterType((*LockSerialForUserToPayReq)(nil), "Giftpkg.LockSerialForUserToPayReq")
	proto.RegisterType((*LockSerialForUserToPayResp)(nil), "Giftpkg.LockSerialForUserToPayResp")
	proto.RegisterType((*RealFetchSerialForUserReq)(nil), "Giftpkg.RealFetchSerialForUserReq")
	proto.RegisterType((*RealFetchSerialForUserResp)(nil), "Giftpkg.RealFetchSerialForUserResp")
	proto.RegisterType((*ReleaseSerialForGuildReq)(nil), "Giftpkg.ReleaseSerialForGuildReq")
	proto.RegisterType((*ReleaseSerialForGuildResp)(nil), "Giftpkg.ReleaseSerialForGuildResp")
	proto.RegisterType((*SetGuildPkgRedDiamondPriceReq)(nil), "Giftpkg.SetGuildPkgRedDiamondPriceReq")
	proto.RegisterType((*SetGuildPkgRedDiamondPriceResp)(nil), "Giftpkg.SetGuildPkgRedDiamondPriceResp")
	proto.RegisterType((*GetUserPkgSerialListNewByIdReq)(nil), "Giftpkg.GetUserPkgSerialListNewByIdReq")
	proto.RegisterType((*GetUserPkgSerialListNewByIdResp)(nil), "Giftpkg.GetUserPkgSerialListNewByIdResp")
	proto.RegisterType((*BatchGetGiftPackageStatInfoReq)(nil), "Giftpkg.BatchGetGiftPackageStatInfoReq")
	proto.RegisterType((*StGuildGameGiftPackageStatInfo)(nil), "Giftpkg.stGuildGameGiftPackageStatInfo")
	proto.RegisterType((*BatchGetGiftPackageStatInfoResp)(nil), "Giftpkg.BatchGetGiftPackageStatInfoResp")
	proto.RegisterType((*FetchActGiftPkgCollectionReq)(nil), "Giftpkg.FetchActGiftPkgCollectionReq")
	proto.RegisterType((*FetchActGiftPkgCollectionResp)(nil), "Giftpkg.FetchActGiftPkgCollectionResp")
	proto.RegisterType((*GetActGiftCollcetionFetStatusReq)(nil), "Giftpkg.GetActGiftCollcetionFetStatusReq")
	proto.RegisterType((*GetActGiftCollcetionFetStatusResp)(nil), "Giftpkg.GetActGiftCollcetionFetStatusResp")
	proto.RegisterType((*GetActGiftPkgCollectionConfReq)(nil), "Giftpkg.GetActGiftPkgCollectionConfReq")
	proto.RegisterType((*GetActGiftPkgCollectionConfResp)(nil), "Giftpkg.GetActGiftPkgCollectionConfResp")
	proto.RegisterEnum("Giftpkg.CHANNEL", CHANNEL_name, CHANNEL_value)
	proto.RegisterEnum("Giftpkg.StGiftPkgSerial_STATUS", StGiftPkgSerial_STATUS_name, StGiftPkgSerial_STATUS_value)
	proto.RegisterEnum("Giftpkg.StGiftPkgApply_STATUS", StGiftPkgApply_STATUS_name, StGiftPkgApply_STATUS_value)
}
func (m *UserFetchGiftPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserFetchGiftPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.FetchTime))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	return i, nil
}

func (m *UserFetchGiftPkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserFetchGiftPkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.GiftPkgSerial)))
	i += copy(dAtA[i:], m.GiftPkgSerial)
	return i, nil
}

func (m *UserFetchActivityGiftPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserFetchActivityGiftPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.FetchTime))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.DeviceIdHex)))
	i += copy(dAtA[i:], m.DeviceIdHex)
	return i, nil
}

func (m *UserFetchActivityGiftPkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserFetchActivityGiftPkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.GiftPkgSerial)))
	i += copy(dAtA[i:], m.GiftPkgSerial)
	return i, nil
}

func (m *CheckActivityGiftPkgFetchStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckActivityGiftPkgFetchStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.DeviceIdHex)))
	i += copy(dAtA[i:], m.DeviceIdHex)
	return i, nil
}

func (m *CheckActivityGiftPkgFetchStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckActivityGiftPkgFetchStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.UidFetched {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x10
	i++
	if m.DeviceFetched {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.FetchTime))
	return i, nil
}

func (m *GetUserGiftPkgSerialReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserGiftPkgSerialReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *GetUserGiftPkgSerialResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserGiftPkgSerialResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OpTime))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.Serial)))
	i += copy(dAtA[i:], m.Serial)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *StGiftPkg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGiftPkg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.ExchangeBegintime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.ExchangeEndtime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.Intro)))
	i += copy(dAtA[i:], m.Intro)
	dAtA[i] = 0x32
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x42
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.Usage)))
	i += copy(dAtA[i:], m.Usage)
	dAtA[i] = 0x48
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.SerialTotalNum))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OfferedNum))
	dAtA[i] = 0x58
	i++
	if m.IsShow {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x60
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.SourceGuild))
	dAtA[i] = 0x68
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *StGiftPkgSerial) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGiftPkgSerial) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GiftPkg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gift_pkg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkg.Size()))
		n1, err := m.GiftPkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OpTime))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.Serial)))
	i += copy(dAtA[i:], m.Serial)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGiftPkgListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftPkgListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Size_))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.SourceGuild))
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x20
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x30
	i++
	if m.OnlyVisible {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetGiftPkgListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftPkgListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PkgList) > 0 {
		for _, msg := range m.PkgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *GetGiftPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *GetGiftPkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftPkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Pkg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("pkg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg(dAtA, i, uint64(m.Pkg.Size()))
		n2, err := m.Pkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *GetMyGiftPkgSerialListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMyGiftPkgSerialListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Size_))
	return i, nil
}

func (m *GetMyGiftPkgSerialListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMyGiftPkgSerialListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PkgList) > 0 {
		for _, msg := range m.PkgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GuildApplyGiftPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildApplyGiftPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.PkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RequiredNum))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.ApplyMsg)))
	i += copy(dAtA[i:], m.ApplyMsg)
	return i, nil
}

func (m *GuildApplyGiftPkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildApplyGiftPkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetGiftPkgApplyListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftPkgApplyListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Size_))
	return i, nil
}

func (m *StGiftPkgApply) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGiftPkgApply) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.ApplyId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.PkgId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.ApplyTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.ApplyMsg)))
	i += copy(dAtA[i:], m.ApplyMsg)
	dAtA[i] = 0x38
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OfficalOpTime))
	dAtA[i] = 0x42
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OfficalMsg)))
	i += copy(dAtA[i:], m.OfficalMsg)
	dAtA[i] = 0x48
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.WantNum))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OfferNum))
	dAtA[i] = 0x58
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OpUid))
	return i, nil
}

func (m *GetGiftPkgApplyListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftPkgApplyListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ApplyList) > 0 {
		for _, msg := range m.ApplyList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *GetGuildApplyingListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildApplyingListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildApplyingListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildApplyingListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ApplyList) > 0 {
		for _, msg := range m.ApplyList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *TypeGiftPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TypeGiftPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Pkg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("pkg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg(dAtA, i, uint64(m.Pkg.Size()))
		n3, err := m.Pkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	return i, nil
}

func (m *TypeGiftPkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TypeGiftPkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *UpdateGiftPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGiftPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Pkg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("pkg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg(dAtA, i, uint64(m.Pkg.Size()))
		n4, err := m.Pkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	return i, nil
}

func (m *UpdateGiftPkgShowStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGiftPkgShowStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x10
	i++
	if m.IsShow {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	return i, nil
}

func (m *TypeGiftPkgSerialReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TypeGiftPkgSerialReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	if len(m.SerialList) > 0 {
		for _, s := range m.SerialList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	return i, nil
}

func (m *TypeGiftPkgSerialResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TypeGiftPkgSerialResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.AddCount))
	return i, nil
}

func (m *TypeGiftPkgSerialToGuildReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TypeGiftPkgSerialToGuildReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	if len(m.SerialList) > 0 {
		for _, s := range m.SerialList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	return i, nil
}

func (m *TypeGiftPkgSerialToGuildResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TypeGiftPkgSerialToGuildResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.AddCount))
	return i, nil
}

func (m *BatchGetGiftPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetGiftPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GiftPkgId) > 0 {
		for _, num := range m.GiftPkgId {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetGiftPkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetGiftPkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Pkg) > 0 {
		for _, msg := range m.Pkg {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RedPkgFetchRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPkgFetchRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RedPkgId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.FetchUid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.FetchTime))
	return i, nil
}

func (m *StGuildGiftPkg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildGiftPkg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GiftPkg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gift_pkg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkg.Size()))
		n5, err := m.GiftPkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.TotalFetchNum))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.TotalNum))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.StorageFetchNum))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.StorageTotalNum))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.PublicFetchNum))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.PublicTotalNum))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.TaohaoCount))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.ApplyPassTime))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RedDiamondPrice))
	return i, nil
}

func (m *GetGuildGiftPkgListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftPkgListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Size_))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OrderType))
	return i, nil
}

func (m *GetGuildGiftPkgListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftPkgListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PkgList) > 0 {
		for _, msg := range m.PkgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *GetGuildPkgListByGameIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildPkgListByGameIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Size_))
	return i, nil
}

func (m *GetGuildPkgListByGameIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildPkgListByGameIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PkgList) > 0 {
		for _, msg := range m.PkgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *PassGiftPkgApplyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PassGiftPkgApplyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.ApplyId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OfferNum))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.GuildName)))
	i += copy(dAtA[i:], m.GuildName)
	return i, nil
}

func (m *PassGiftPkgApplyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PassGiftPkgApplyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RejectGiftPkgApplyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RejectGiftPkgApplyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.ApplyId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.RejectReason)))
	i += copy(dAtA[i:], m.RejectReason)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.GuildName)))
	i += copy(dAtA[i:], m.GuildName)
	return i, nil
}

func (m *RejectGiftPkgApplyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RejectGiftPkgApplyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetGuildPkgStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildPkgStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.PkgId))
	return i, nil
}

func (m *GetGuildPkgStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildPkgStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Pkg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("pkg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg(dAtA, i, uint64(m.Pkg.Size()))
		n6, err := m.Pkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *BatchGetGuildPkgStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetGuildPkgStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	if len(m.PkgIdList) > 0 {
		for _, num := range m.PkgIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetGuildPkgStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetGuildPkgStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PkgList) > 0 {
		for _, msg := range m.PkgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CheckGuildPkgApplyingReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckGuildPkgApplyingReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *CheckGuildPkgApplyingResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckGuildPkgApplyingResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.IsApplying))
	return i, nil
}

func (m *GetGuildApplyHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildApplyHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Size_))
	return i, nil
}

func (m *GetGuildApplyHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildApplyHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ApplyList) > 0 {
		for _, msg := range m.ApplyList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *TaohaoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TaohaoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *TaohaoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TaohaoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.SerialNum)))
	i += copy(dAtA[i:], m.SerialNum)
	return i, nil
}

func (m *TaohaoAddUsedCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TaohaoAddUsedCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.SerialNum)))
	i += copy(dAtA[i:], m.SerialNum)
	return i, nil
}

func (m *TaohaoAddUsedCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TaohaoAddUsedCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CreateRedpkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateRedpkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.SerialTotalNum))
	return i, nil
}

func (m *CreateRedpkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateRedpkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RedpkgId))
	return i, nil
}

func (m *FetchRedPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchRedPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RedPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *FetchRedPkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchRedPkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.SerialNum)))
	i += copy(dAtA[i:], m.SerialNum)
	return i, nil
}

func (m *GetRedPkgHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRedPkgHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RedPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Size_))
	return i, nil
}

func (m *GetRedPkgHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRedPkgHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecordList) > 0 {
		for _, msg := range m.RecordList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetRedPkgDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRedPkgDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RedPkgId))
	return i, nil
}

func (m *RedPkgDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPkgDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RedPkgId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.CreatorUid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.SerialFetchNum))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.SerialTotalNum))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.UpdateTime))
	return i, nil
}

func (m *GetRedPkgDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRedPkgDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RedPkgDetail == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("red_pkg_detail")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg(dAtA, i, uint64(m.RedPkgDetail.Size()))
		n7, err := m.RedPkgDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *GiveGiftPkgToGuildReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GiveGiftPkgToGuildReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OfferNumber))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.GuildName)))
	i += copy(dAtA[i:], m.GuildName)
	return i, nil
}

func (m *CheckIfFetchRedpkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckIfFetchRedpkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RedPkgId))
	return i, nil
}

func (m *CheckIfFetchRedpkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckIfFetchRedpkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsFetch {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *StGuildApplyTimePkg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildApplyTimePkg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.LastApplyTime))
	return i, nil
}

func (m *GetApplyingGuildListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetApplyingGuildListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetApplyingGuildListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetApplyingGuildListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildApplyTimeList) > 0 {
		for _, msg := range m.GuildApplyTimeList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetApplyedGuildListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetApplyedGuildListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetApplyedGuildListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetApplyedGuildListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildApplyTimeList) > 0 {
		for _, msg := range m.GuildApplyTimeList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StGuildGiftPkgStatInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildGiftPkgStatInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.TotalCount))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.TotalFetchedCount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.StorageFetchNum))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.StorageTotalNum))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.PublicFetchNum))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.PublicTotalNum))
	return i, nil
}

func (m *StGuildGameGiftPkgStatInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildGameGiftPkgStatInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.TotalCount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.TotalFetchedCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.PublicFetchNum))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.PublicTotalNum))
	return i, nil
}

func (m *GetGuildGiftPkgStatInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftPkgStatInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildGiftPkgStatInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftPkgStatInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.StatInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("stat_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg(dAtA, i, uint64(m.StatInfo.Size()))
		n8, err := m.StatInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if len(m.GameStatList) > 0 {
		for _, msg := range m.GameStatList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CountRestGiftPkgSerialReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CountRestGiftPkgSerialReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GameId))
	return i, nil
}

func (m *CountRestGiftPkgSerialResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CountRestGiftPkgSerialResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *GetApplyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetApplyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.ApplyId))
	return i, nil
}

func (m *GetApplyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetApplyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Apply == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("apply")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg(dAtA, i, uint64(m.Apply.Size()))
		n9, err := m.Apply.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *GetGiftpkgOpHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftpkgOpHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RecordBegintime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RecordEndtime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Size_))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	return i, nil
}

func (m *GiftpkgOpRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GiftpkgOpRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OpTime))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpContent)))
	i += copy(dAtA[i:], m.OpContent)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.OpType))
	return i, nil
}

func (m *GetGiftpkgOpHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftpkgOpHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Records) > 0 {
		for _, msg := range m.Records {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *GetDeviceLastFetchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDeviceLastFetchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	return i, nil
}

func (m *GetDeviceLastFetchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDeviceLastFetchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.LastFetchTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *GetOfficalLastOpTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOfficalLastOpTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftpkgId))
	return i, nil
}

func (m *GetOfficalLastOpTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOfficalLastOpTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.LastOpTime))
	return i, nil
}

func (m *DeleteGuildSourceGiftPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteGuildSourceGiftPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftpkgId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	return i, nil
}

func (m *DeleteGuildSourceGiftPkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteGuildSourceGiftPkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *LockSerialForUserToPayReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LockSerialForUserToPayReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftpkgId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.Orderid)))
	i += copy(dAtA[i:], m.Orderid)
	return i, nil
}

func (m *LockSerialForUserToPayResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LockSerialForUserToPayResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RealFetchSerialForUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RealFetchSerialForUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftpkgId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.Orderid)))
	i += copy(dAtA[i:], m.Orderid)
	return i, nil
}

func (m *RealFetchSerialForUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RealFetchSerialForUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.SerialNum)))
	i += copy(dAtA[i:], m.SerialNum)
	return i, nil
}

func (m *ReleaseSerialForGuildReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReleaseSerialForGuildReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftpkgId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.Orderid)))
	i += copy(dAtA[i:], m.Orderid)
	return i, nil
}

func (m *ReleaseSerialForGuildResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReleaseSerialForGuildResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetGuildPkgRedDiamondPriceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGuildPkgRedDiamondPriceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftpkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.RedDiamondCost))
	return i, nil
}

func (m *SetGuildPkgRedDiamondPriceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGuildPkgRedDiamondPriceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserPkgSerialListNewByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserPkgSerialListNewByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *GetUserPkgSerialListNewByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserPkgSerialListNewByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SerialList) > 0 {
		for _, msg := range m.SerialList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetGiftPackageStatInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetGiftPackageStatInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, num := range m.GuildIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(num))
		}
	}
	if len(m.GameIdList) > 0 {
		for _, num := range m.GameIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *StGuildGameGiftPackageStatInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildGameGiftPackageStatInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GameId))
	if m.StatInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("stat_info")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGiftpkg(dAtA, i, uint64(m.StatInfo.Size()))
		n10, err := m.StatInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *BatchGetGiftPackageStatInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetGiftPackageStatInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.StatList) > 0 {
		for _, msg := range m.StatList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGiftpkg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *FetchActGiftPkgCollectionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchActGiftPkgCollectionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	return i, nil
}

func (m *FetchActGiftPkgCollectionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchActGiftPkgCollectionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GiftPkgSerial) > 0 {
		for _, s := range m.GiftPkgSerial {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GetActGiftCollcetionFetStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActGiftCollcetionFetStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	return i, nil
}

func (m *GetActGiftCollcetionFetStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActGiftCollcetionFetStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.FetchStatus))
	return i, nil
}

func (m *GetActGiftPkgCollectionConfReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActGiftPkgCollectionConfReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(m.GameId))
	return i, nil
}

func (m *GetActGiftPkgCollectionConfResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActGiftPkgCollectionConfResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.PkgIdList)))
	i += copy(dAtA[i:], m.PkgIdList)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGiftpkg(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	return i, nil
}

func encodeFixed64Giftpkg(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Giftpkg(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGiftpkg(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UserFetchGiftPkgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg(uint64(m.FetchTime))
	l = len(m.DeviceId)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *UserFetchGiftPkgResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.GiftPkgSerial)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *UserFetchActivityGiftPkgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Uid))
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg(uint64(m.FetchTime))
	l = len(m.DeviceIdHex)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *UserFetchActivityGiftPkgResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.GiftPkgSerial)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *CheckActivityGiftPkgFetchStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Uid))
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	l = len(m.DeviceIdHex)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *CheckActivityGiftPkgFetchStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	n += 2
	n += 1 + sovGiftpkg(uint64(m.FetchTime))
	return n
}

func (m *GetUserGiftPkgSerialReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	return n
}

func (m *GetUserGiftPkgSerialResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Status))
	n += 1 + sovGiftpkg(uint64(m.OpTime))
	l = len(m.Serial)
	n += 1 + l + sovGiftpkg(uint64(l))
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	return n
}

func (m *StGiftPkg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg(uint64(m.ExchangeBegintime))
	n += 1 + sovGiftpkg(uint64(m.ExchangeEndtime))
	n += 1 + sovGiftpkg(uint64(m.GameId))
	l = len(m.Intro)
	n += 1 + l + sovGiftpkg(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovGiftpkg(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovGiftpkg(uint64(l))
	l = len(m.Usage)
	n += 1 + l + sovGiftpkg(uint64(l))
	n += 1 + sovGiftpkg(uint64(m.SerialTotalNum))
	n += 1 + sovGiftpkg(uint64(m.OfferedNum))
	n += 2
	n += 1 + sovGiftpkg(uint64(m.SourceGuild))
	n += 1 + sovGiftpkg(uint64(m.ChannelId))
	return n
}

func (m *StGiftPkgSerial) Size() (n int) {
	var l int
	_ = l
	if m.GiftPkg != nil {
		l = m.GiftPkg.Size()
		n += 1 + l + sovGiftpkg(uint64(l))
	}
	n += 1 + sovGiftpkg(uint64(m.Status))
	n += 1 + sovGiftpkg(uint64(m.OpTime))
	l = len(m.Serial)
	n += 1 + l + sovGiftpkg(uint64(l))
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	return n
}

func (m *GetGiftPkgListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Offset))
	n += 1 + sovGiftpkg(uint64(m.Size_))
	n += 1 + sovGiftpkg(uint64(m.SourceGuild))
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovGiftpkg(uint64(e))
		}
	}
	n += 1 + sovGiftpkg(uint64(m.GameId))
	n += 2
	return n
}

func (m *GetGiftPkgListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PkgList) > 0 {
		for _, e := range m.PkgList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	n += 1 + sovGiftpkg(uint64(m.Total))
	return n
}

func (m *GetGiftPkgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	return n
}

func (m *GetGiftPkgResp) Size() (n int) {
	var l int
	_ = l
	if m.Pkg != nil {
		l = m.Pkg.Size()
		n += 1 + l + sovGiftpkg(uint64(l))
	}
	return n
}

func (m *GetMyGiftPkgSerialListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Offset))
	n += 1 + sovGiftpkg(uint64(m.Size_))
	return n
}

func (m *GetMyGiftPkgSerialListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PkgList) > 0 {
		for _, e := range m.PkgList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *GuildApplyGiftPkgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.PkgId))
	n += 1 + sovGiftpkg(uint64(m.RequiredNum))
	l = len(m.ApplyMsg)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *GuildApplyGiftPkgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetGiftPkgApplyListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Offset))
	n += 1 + sovGiftpkg(uint64(m.Size_))
	return n
}

func (m *StGiftPkgApply) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.ApplyId))
	n += 1 + sovGiftpkg(uint64(m.PkgId))
	n += 1 + sovGiftpkg(uint64(m.ApplyTime))
	n += 1 + sovGiftpkg(uint64(m.Status))
	l = len(m.ApplyMsg)
	n += 1 + l + sovGiftpkg(uint64(l))
	n += 1 + sovGiftpkg(uint64(m.OfficalOpTime))
	l = len(m.OfficalMsg)
	n += 1 + l + sovGiftpkg(uint64(l))
	n += 1 + sovGiftpkg(uint64(m.WantNum))
	n += 1 + sovGiftpkg(uint64(m.OfferNum))
	n += 1 + sovGiftpkg(uint64(m.OpUid))
	return n
}

func (m *GetGiftPkgApplyListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ApplyList) > 0 {
		for _, e := range m.ApplyList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	n += 1 + sovGiftpkg(uint64(m.Total))
	return n
}

func (m *GetGuildApplyingListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	return n
}

func (m *GetGuildApplyingListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ApplyList) > 0 {
		for _, e := range m.ApplyList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *TypeGiftPkgReq) Size() (n int) {
	var l int
	_ = l
	if m.Pkg != nil {
		l = m.Pkg.Size()
		n += 1 + l + sovGiftpkg(uint64(l))
	}
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *TypeGiftPkgResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	return n
}

func (m *UpdateGiftPkgReq) Size() (n int) {
	var l int
	_ = l
	if m.Pkg != nil {
		l = m.Pkg.Size()
		n += 1 + l + sovGiftpkg(uint64(l))
	}
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *UpdateGiftPkgShowStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	n += 2
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *TypeGiftPkgSerialReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	if len(m.SerialList) > 0 {
		for _, s := range m.SerialList {
			l = len(s)
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *TypeGiftPkgSerialResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.AddCount))
	return n
}

func (m *TypeGiftPkgSerialToGuildReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	if len(m.SerialList) > 0 {
		for _, s := range m.SerialList {
			l = len(s)
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *TypeGiftPkgSerialToGuildResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.AddCount))
	return n
}

func (m *BatchGetGiftPkgReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GiftPkgId) > 0 {
		for _, e := range m.GiftPkgId {
			n += 1 + sovGiftpkg(uint64(e))
		}
	}
	return n
}

func (m *BatchGetGiftPkgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.Pkg) > 0 {
		for _, e := range m.Pkg {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *RedPkgFetchRecord) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Id))
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.RedPkgId))
	n += 1 + sovGiftpkg(uint64(m.FetchUid))
	n += 1 + sovGiftpkg(uint64(m.FetchTime))
	return n
}

func (m *StGuildGiftPkg) Size() (n int) {
	var l int
	_ = l
	if m.GiftPkg != nil {
		l = m.GiftPkg.Size()
		n += 1 + l + sovGiftpkg(uint64(l))
	}
	n += 1 + sovGiftpkg(uint64(m.TotalFetchNum))
	n += 1 + sovGiftpkg(uint64(m.TotalNum))
	n += 1 + sovGiftpkg(uint64(m.StorageFetchNum))
	n += 1 + sovGiftpkg(uint64(m.StorageTotalNum))
	n += 1 + sovGiftpkg(uint64(m.PublicFetchNum))
	n += 1 + sovGiftpkg(uint64(m.PublicTotalNum))
	n += 1 + sovGiftpkg(uint64(m.TaohaoCount))
	n += 1 + sovGiftpkg(uint64(m.ApplyPassTime))
	n += 1 + sovGiftpkg(uint64(m.RedDiamondPrice))
	return n
}

func (m *GetGuildGiftPkgListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.Offset))
	n += 1 + sovGiftpkg(uint64(m.Size_))
	n += 1 + sovGiftpkg(uint64(m.OrderType))
	return n
}

func (m *GetGuildGiftPkgListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PkgList) > 0 {
		for _, e := range m.PkgList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	n += 1 + sovGiftpkg(uint64(m.Total))
	return n
}

func (m *GetGuildPkgListByGameIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GameId))
	n += 1 + sovGiftpkg(uint64(m.Offset))
	n += 1 + sovGiftpkg(uint64(m.Size_))
	return n
}

func (m *GetGuildPkgListByGameIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PkgList) > 0 {
		for _, e := range m.PkgList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	n += 1 + sovGiftpkg(uint64(m.Total))
	return n
}

func (m *PassGiftPkgApplyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.ApplyId))
	n += 1 + sovGiftpkg(uint64(m.OfferNum))
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	l = len(m.GuildName)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *PassGiftPkgApplyResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RejectGiftPkgApplyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.ApplyId))
	l = len(m.RejectReason)
	n += 1 + l + sovGiftpkg(uint64(l))
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	l = len(m.GuildName)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *RejectGiftPkgApplyResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetGuildPkgStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.PkgId))
	return n
}

func (m *GetGuildPkgStatusResp) Size() (n int) {
	var l int
	_ = l
	if m.Pkg != nil {
		l = m.Pkg.Size()
		n += 1 + l + sovGiftpkg(uint64(l))
	}
	return n
}

func (m *BatchGetGuildPkgStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	if len(m.PkgIdList) > 0 {
		for _, e := range m.PkgIdList {
			n += 1 + sovGiftpkg(uint64(e))
		}
	}
	return n
}

func (m *BatchGetGuildPkgStatusResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PkgList) > 0 {
		for _, e := range m.PkgList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *CheckGuildPkgApplyingReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	return n
}

func (m *CheckGuildPkgApplyingResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.IsApplying))
	return n
}

func (m *GetGuildApplyHistoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.Offset))
	n += 1 + sovGiftpkg(uint64(m.Size_))
	return n
}

func (m *GetGuildApplyHistoryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ApplyList) > 0 {
		for _, e := range m.ApplyList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	n += 1 + sovGiftpkg(uint64(m.Total))
	return n
}

func (m *TaohaoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	return n
}

func (m *TaohaoResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.SerialNum)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *TaohaoAddUsedCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	l = len(m.SerialNum)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *TaohaoAddUsedCountResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CreateRedpkgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg(uint64(m.SerialTotalNum))
	return n
}

func (m *CreateRedpkgResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.RedpkgId))
	return n
}

func (m *FetchRedPkgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.RedPkgId))
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	return n
}

func (m *FetchRedPkgResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.SerialNum)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *GetRedPkgHistoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.RedPkgId))
	n += 1 + sovGiftpkg(uint64(m.Offset))
	n += 1 + sovGiftpkg(uint64(m.Size_))
	return n
}

func (m *GetRedPkgHistoryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecordList) > 0 {
		for _, e := range m.RecordList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *GetRedPkgDetailReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.RedPkgId))
	return n
}

func (m *RedPkgDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.RedPkgId))
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg(uint64(m.CreatorUid))
	n += 1 + sovGiftpkg(uint64(m.SerialFetchNum))
	n += 1 + sovGiftpkg(uint64(m.SerialTotalNum))
	n += 1 + sovGiftpkg(uint64(m.CreateTime))
	n += 1 + sovGiftpkg(uint64(m.UpdateTime))
	return n
}

func (m *GetRedPkgDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.RedPkgDetail != nil {
		l = m.RedPkgDetail.Size()
		n += 1 + l + sovGiftpkg(uint64(l))
	}
	return n
}

func (m *GiveGiftPkgToGuildReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.OfferNumber))
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	l = len(m.GuildName)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *CheckIfFetchRedpkgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.RedPkgId))
	return n
}

func (m *CheckIfFetchRedpkgResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *StGuildApplyTimePkg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.LastApplyTime))
	return n
}

func (m *GetApplyingGuildListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	return n
}

func (m *GetApplyingGuildListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildApplyTimeList) > 0 {
		for _, e := range m.GuildApplyTimeList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *GetApplyedGuildListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	return n
}

func (m *GetApplyedGuildListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildApplyTimeList) > 0 {
		for _, e := range m.GuildApplyTimeList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *StGuildGiftPkgStatInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.TotalCount))
	n += 1 + sovGiftpkg(uint64(m.TotalFetchedCount))
	n += 1 + sovGiftpkg(uint64(m.StorageFetchNum))
	n += 1 + sovGiftpkg(uint64(m.StorageTotalNum))
	n += 1 + sovGiftpkg(uint64(m.PublicFetchNum))
	n += 1 + sovGiftpkg(uint64(m.PublicTotalNum))
	return n
}

func (m *StGuildGameGiftPkgStatInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GameId))
	n += 1 + sovGiftpkg(uint64(m.TotalCount))
	n += 1 + sovGiftpkg(uint64(m.TotalFetchedCount))
	n += 1 + sovGiftpkg(uint64(m.PublicFetchNum))
	n += 1 + sovGiftpkg(uint64(m.PublicTotalNum))
	return n
}

func (m *GetGuildGiftPkgStatInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	return n
}

func (m *GetGuildGiftPkgStatInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.StatInfo != nil {
		l = m.StatInfo.Size()
		n += 1 + l + sovGiftpkg(uint64(l))
	}
	if len(m.GameStatList) > 0 {
		for _, e := range m.GameStatList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *CountRestGiftPkgSerialReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GameId))
	return n
}

func (m *CountRestGiftPkgSerialResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Total))
	return n
}

func (m *GetApplyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.ApplyId))
	return n
}

func (m *GetApplyResp) Size() (n int) {
	var l int
	_ = l
	if m.Apply != nil {
		l = m.Apply.Size()
		n += 1 + l + sovGiftpkg(uint64(l))
	}
	return n
}

func (m *GetGiftpkgOpHistoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.RecordBegintime))
	n += 1 + sovGiftpkg(uint64(m.RecordEndtime))
	n += 1 + sovGiftpkg(uint64(m.Offset))
	n += 1 + sovGiftpkg(uint64(m.Size_))
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *GiftpkgOpRecord) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.OpTime))
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	l = len(m.OpContent)
	n += 1 + l + sovGiftpkg(uint64(l))
	n += 1 + sovGiftpkg(uint64(m.OpType))
	return n
}

func (m *GetGiftpkgOpHistoryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.Records) > 0 {
		for _, e := range m.Records {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	n += 1 + sovGiftpkg(uint64(m.Total))
	return n
}

func (m *GetDeviceLastFetchReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.DeviceId)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *GetDeviceLastFetchResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Uid))
	n += 1 + sovGiftpkg(uint64(m.LastFetchTime))
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	return n
}

func (m *GetOfficalLastOpTimeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GiftpkgId))
	return n
}

func (m *GetOfficalLastOpTimeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.LastOpTime))
	return n
}

func (m *DeleteGuildSourceGiftPkgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftpkgId))
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	l = len(m.OpAccount)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *DeleteGuildSourceGiftPkgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *LockSerialForUserToPayReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GiftpkgId))
	l = len(m.Orderid)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *LockSerialForUserToPayResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RealFetchSerialForUserReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GiftpkgId))
	l = len(m.Orderid)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *RealFetchSerialForUserResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.SerialNum)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *ReleaseSerialForGuildReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GiftpkgId))
	l = len(m.Orderid)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *ReleaseSerialForGuildResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetGuildPkgRedDiamondPriceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GiftpkgId))
	n += 1 + sovGiftpkg(uint64(m.RedDiamondCost))
	return n
}

func (m *SetGuildPkgRedDiamondPriceResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserPkgSerialListNewByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GiftPkgId))
	return n
}

func (m *GetUserPkgSerialListNewByIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SerialList) > 0 {
		for _, e := range m.SerialList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *BatchGetGiftPackageStatInfoReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, e := range m.GuildIdList {
			n += 1 + sovGiftpkg(uint64(e))
		}
	}
	if len(m.GameIdList) > 0 {
		for _, e := range m.GameIdList {
			n += 1 + sovGiftpkg(uint64(e))
		}
	}
	return n
}

func (m *StGuildGameGiftPackageStatInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GuildId))
	n += 1 + sovGiftpkg(uint64(m.GameId))
	if m.StatInfo != nil {
		l = m.StatInfo.Size()
		n += 1 + l + sovGiftpkg(uint64(l))
	}
	return n
}

func (m *BatchGetGiftPackageStatInfoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.StatList) > 0 {
		for _, e := range m.StatList {
			l = e.Size()
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *FetchActGiftPkgCollectionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Uid))
	n += 1 + sovGiftpkg(uint64(m.GameId))
	l = len(m.DeviceId)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *FetchActGiftPkgCollectionResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GiftPkgSerial) > 0 {
		for _, s := range m.GiftPkgSerial {
			l = len(s)
			n += 1 + l + sovGiftpkg(uint64(l))
		}
	}
	return n
}

func (m *GetActGiftCollcetionFetStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.Uid))
	n += 1 + sovGiftpkg(uint64(m.GameId))
	l = len(m.DeviceId)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func (m *GetActGiftCollcetionFetStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.FetchStatus))
	return n
}

func (m *GetActGiftPkgCollectionConfReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGiftpkg(uint64(m.GameId))
	return n
}

func (m *GetActGiftPkgCollectionConfResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.PkgIdList)
	n += 1 + l + sovGiftpkg(uint64(l))
	l = len(m.PicUrl)
	n += 1 + l + sovGiftpkg(uint64(l))
	return n
}

func sovGiftpkg(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGiftpkg(x uint64) (n int) {
	return sovGiftpkg(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *UserFetchGiftPkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: userFetchGiftPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: userFetchGiftPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchTime", wireType)
			}
			m.FetchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fetch_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserFetchGiftPkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: userFetchGiftPkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: userFetchGiftPkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgSerial", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftPkgSerial = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_serial")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserFetchActivityGiftPkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserFetchActivityGiftPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserFetchActivityGiftPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchTime", wireType)
			}
			m.FetchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceIdHex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceIdHex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fetch_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id_hex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserFetchActivityGiftPkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserFetchActivityGiftPkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserFetchActivityGiftPkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgSerial", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftPkgSerial = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_serial")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckActivityGiftPkgFetchStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckActivityGiftPkgFetchStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckActivityGiftPkgFetchStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceIdHex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceIdHex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id_hex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckActivityGiftPkgFetchStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckActivityGiftPkgFetchStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckActivityGiftPkgFetchStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidFetched", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UidFetched = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceFetched", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DeviceFetched = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchTime", wireType)
			}
			m.FetchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid_fetched")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_fetched")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserGiftPkgSerialReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserGiftPkgSerialReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserGiftPkgSerialReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserGiftPkgSerialResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserGiftPkgSerialResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserGiftPkgSerialResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			m.OpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Serial", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Serial = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("serial")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGiftPkg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGiftPkg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGiftPkg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeBegintime", wireType)
			}
			m.ExchangeBegintime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeBegintime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeEndtime", wireType)
			}
			m.ExchangeEndtime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeEndtime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Intro", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Intro = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Usage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Usage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialTotalNum", wireType)
			}
			m.SerialTotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SerialTotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferedNum", wireType)
			}
			m.OfferedNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfferedNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsShow", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShow = bool(v != 0)
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceGuild", wireType)
			}
			m.SourceGuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceGuild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exchange_begintime")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exchange_endtime")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("intro")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("usage")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("serial_total_num")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offered_num")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_show")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGiftPkgSerial) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGiftPkgSerial: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGiftPkgSerial: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiftPkg == nil {
				m.GiftPkg = &StGiftPkg{}
			}
			if err := m.GiftPkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			m.OpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Serial", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Serial = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("serial")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftPkgListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftPkgListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftPkgListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceGuild", wireType)
			}
			m.SourceGuild = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceGuild |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGiftpkg
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGiftpkg
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlyVisible", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OnlyVisible = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftPkgListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftPkgListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftPkgListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PkgList = append(m.PkgList, &StGiftPkg{})
			if err := m.PkgList[len(m.PkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftPkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftPkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftPkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftPkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Pkg == nil {
				m.Pkg = &StGiftPkg{}
			}
			if err := m.Pkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMyGiftPkgSerialListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMyGiftPkgSerialListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMyGiftPkgSerialListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMyGiftPkgSerialListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMyGiftPkgSerialListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMyGiftPkgSerialListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PkgList = append(m.PkgList, &StGiftPkgSerial{})
			if err := m.PkgList[len(m.PkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildApplyGiftPkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildApplyGiftPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildApplyGiftPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgId", wireType)
			}
			m.PkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequiredNum", wireType)
			}
			m.RequiredNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequiredNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ApplyMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("required_num")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildApplyGiftPkgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildApplyGiftPkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildApplyGiftPkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftPkgApplyListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftPkgApplyListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftPkgApplyListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGiftPkgApply) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGiftPkgApply: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGiftPkgApply: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyId", wireType)
			}
			m.ApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgId", wireType)
			}
			m.PkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyTime", wireType)
			}
			m.ApplyTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ApplyMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficalOpTime", wireType)
			}
			m.OfficalOpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficalOpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficalMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OfficalMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WantNum", wireType)
			}
			m.WantNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WantNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferNum", wireType)
			}
			m.OfferNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfferNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_msg")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offical_op_time")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offical_msg")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("want_num")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offer_num")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftPkgApplyListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftPkgApplyListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftPkgApplyListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ApplyList = append(m.ApplyList, &StGiftPkgApply{})
			if err := m.ApplyList[len(m.ApplyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildApplyingListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildApplyingListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildApplyingListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildApplyingListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildApplyingListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildApplyingListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ApplyList = append(m.ApplyList, &StGiftPkgApply{})
			if err := m.ApplyList[len(m.ApplyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TypeGiftPkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TypeGiftPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TypeGiftPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Pkg == nil {
				m.Pkg = &StGiftPkg{}
			}
			if err := m.Pkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TypeGiftPkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TypeGiftPkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TypeGiftPkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGiftPkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGiftPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGiftPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Pkg == nil {
				m.Pkg = &StGiftPkg{}
			}
			if err := m.Pkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGiftPkgShowStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGiftPkgShowStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGiftPkgShowStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsShow", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShow = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_show")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TypeGiftPkgSerialReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TypeGiftPkgSerialReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TypeGiftPkgSerialReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialList = append(m.SerialList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TypeGiftPkgSerialResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TypeGiftPkgSerialResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TypeGiftPkgSerialResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddCount", wireType)
			}
			m.AddCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("add_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TypeGiftPkgSerialToGuildReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TypeGiftPkgSerialToGuildReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TypeGiftPkgSerialToGuildReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialList = append(m.SerialList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TypeGiftPkgSerialToGuildResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TypeGiftPkgSerialToGuildResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TypeGiftPkgSerialToGuildResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddCount", wireType)
			}
			m.AddCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("add_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetGiftPkgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetGiftPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetGiftPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GiftPkgId = append(m.GiftPkgId, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGiftpkg
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGiftpkg
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GiftPkgId = append(m.GiftPkgId, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetGiftPkgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetGiftPkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetGiftPkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Pkg = append(m.Pkg, &StGiftPkg{})
			if err := m.Pkg[len(m.Pkg)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPkgFetchRecord) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPkgFetchRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPkgFetchRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchUid", wireType)
			}
			m.FetchUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchTime", wireType)
			}
			m.FetchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("red_pkg_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fetch_uid")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fetch_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildGiftPkg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildGiftPkg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildGiftPkg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GiftPkg == nil {
				m.GiftPkg = &StGiftPkg{}
			}
			if err := m.GiftPkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalFetchNum", wireType)
			}
			m.TotalFetchNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalFetchNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalNum", wireType)
			}
			m.TotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StorageFetchNum", wireType)
			}
			m.StorageFetchNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StorageFetchNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StorageTotalNum", wireType)
			}
			m.StorageTotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StorageTotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicFetchNum", wireType)
			}
			m.PublicFetchNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicFetchNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicTotalNum", wireType)
			}
			m.PublicTotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicTotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TaohaoCount", wireType)
			}
			m.TaohaoCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TaohaoCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyPassTime", wireType)
			}
			m.ApplyPassTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyPassTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamondPrice", wireType)
			}
			m.RedDiamondPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamondPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_fetch_num")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_num")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("storage_fetch_num")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("storage_total_num")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("public_fetch_num")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("public_total_num")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("taohao_count")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_pass_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftPkgListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftPkgListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftPkgListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderType", wireType)
			}
			m.OrderType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OrderType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftPkgListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftPkgListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftPkgListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PkgList = append(m.PkgList, &StGuildGiftPkg{})
			if err := m.PkgList[len(m.PkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildPkgListByGameIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildPkgListByGameIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildPkgListByGameIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildPkgListByGameIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildPkgListByGameIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildPkgListByGameIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PkgList = append(m.PkgList, &StGuildGiftPkg{})
			if err := m.PkgList[len(m.PkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PassGiftPkgApplyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PassGiftPkgApplyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PassGiftPkgApplyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyId", wireType)
			}
			m.ApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferNum", wireType)
			}
			m.OfferNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfferNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offer_num")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PassGiftPkgApplyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PassGiftPkgApplyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PassGiftPkgApplyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RejectGiftPkgApplyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RejectGiftPkgApplyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RejectGiftPkgApplyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyId", wireType)
			}
			m.ApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RejectReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RejectReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reject_reason")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RejectGiftPkgApplyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RejectGiftPkgApplyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RejectGiftPkgApplyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildPkgStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildPkgStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildPkgStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgId", wireType)
			}
			m.PkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildPkgStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildPkgStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildPkgStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Pkg == nil {
				m.Pkg = &StGuildGiftPkg{}
			}
			if err := m.Pkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetGuildPkgStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetGuildPkgStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetGuildPkgStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.PkgIdList = append(m.PkgIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGiftpkg
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGiftpkg
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.PkgIdList = append(m.PkgIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetGuildPkgStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetGuildPkgStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetGuildPkgStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PkgList = append(m.PkgList, &StGuildGiftPkg{})
			if err := m.PkgList[len(m.PkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckGuildPkgApplyingReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckGuildPkgApplyingReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckGuildPkgApplyingReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckGuildPkgApplyingResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckGuildPkgApplyingResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckGuildPkgApplyingResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsApplying", wireType)
			}
			m.IsApplying = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsApplying |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_applying")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildApplyHistoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildApplyHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildApplyHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildApplyHistoryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildApplyHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildApplyHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ApplyList = append(m.ApplyList, &StGiftPkgApply{})
			if err := m.ApplyList[len(m.ApplyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TaohaoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TaohaoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TaohaoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TaohaoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TaohaoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TaohaoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("serial_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TaohaoAddUsedCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TaohaoAddUsedCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TaohaoAddUsedCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("serial_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TaohaoAddUsedCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TaohaoAddUsedCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TaohaoAddUsedCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateRedpkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateRedpkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateRedpkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialTotalNum", wireType)
			}
			m.SerialTotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SerialTotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("serial_total_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateRedpkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateRedpkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateRedpkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedpkgId", wireType)
			}
			m.RedpkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedpkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("redpkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchRedPkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchRedPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchRedPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("red_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchRedPkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchRedPkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchRedPkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("serial_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRedPkgHistoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRedPkgHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRedPkgHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("red_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRedPkgHistoryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRedPkgHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRedPkgHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecordList = append(m.RecordList, &RedPkgFetchRecord{})
			if err := m.RecordList[len(m.RecordList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRedPkgDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRedPkgDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRedPkgDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("red_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPkgDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPkgDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPkgDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreatorUid", wireType)
			}
			m.CreatorUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreatorUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialFetchNum", wireType)
			}
			m.SerialFetchNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SerialFetchNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialTotalNum", wireType)
			}
			m.SerialTotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SerialTotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("red_pkg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator_uid")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("serial_fetch_num")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("serial_total_num")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRedPkgDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRedPkgDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRedPkgDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RedPkgDetail == nil {
				m.RedPkgDetail = &RedPkgDetail{}
			}
			if err := m.RedPkgDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("red_pkg_detail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GiveGiftPkgToGuildReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GiveGiftPkgToGuildReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GiveGiftPkgToGuildReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfferNumber", wireType)
			}
			m.OfferNumber = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfferNumber |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offer_number")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckIfFetchRedpkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckIfFetchRedpkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckIfFetchRedpkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("red_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckIfFetchRedpkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckIfFetchRedpkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckIfFetchRedpkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsFetch", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsFetch = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_fetch")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildApplyTimePkg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildApplyTimePkg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildApplyTimePkg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastApplyTime", wireType)
			}
			m.LastApplyTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastApplyTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_apply_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetApplyingGuildListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetApplyingGuildListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetApplyingGuildListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetApplyingGuildListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetApplyingGuildListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetApplyingGuildListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildApplyTimeList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildApplyTimeList = append(m.GuildApplyTimeList, &StGuildApplyTimePkg{})
			if err := m.GuildApplyTimeList[len(m.GuildApplyTimeList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetApplyedGuildListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetApplyedGuildListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetApplyedGuildListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetApplyedGuildListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetApplyedGuildListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetApplyedGuildListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildApplyTimeList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildApplyTimeList = append(m.GuildApplyTimeList, &StGuildApplyTimePkg{})
			if err := m.GuildApplyTimeList[len(m.GuildApplyTimeList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildGiftPkgStatInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildGiftPkgStatInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildGiftPkgStatInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCount", wireType)
			}
			m.TotalCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalFetchedCount", wireType)
			}
			m.TotalFetchedCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalFetchedCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StorageFetchNum", wireType)
			}
			m.StorageFetchNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StorageFetchNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StorageTotalNum", wireType)
			}
			m.StorageTotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StorageTotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicFetchNum", wireType)
			}
			m.PublicFetchNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicFetchNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicTotalNum", wireType)
			}
			m.PublicTotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicTotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_count")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_fetched_count")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("storage_fetch_num")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("storage_total_num")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("public_fetch_num")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("public_total_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildGameGiftPkgStatInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildGameGiftPkgStatInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildGameGiftPkgStatInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCount", wireType)
			}
			m.TotalCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalFetchedCount", wireType)
			}
			m.TotalFetchedCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalFetchedCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicFetchNum", wireType)
			}
			m.PublicFetchNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicFetchNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicTotalNum", wireType)
			}
			m.PublicTotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicTotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_count")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_fetched_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftPkgStatInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftPkgStatInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftPkgStatInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftPkgStatInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftPkgStatInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftPkgStatInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StatInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.StatInfo == nil {
				m.StatInfo = &StGuildGiftPkgStatInfo{}
			}
			if err := m.StatInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameStatList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameStatList = append(m.GameStatList, &StGuildGameGiftPkgStatInfo{})
			if err := m.GameStatList[len(m.GameStatList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stat_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CountRestGiftPkgSerialReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CountRestGiftPkgSerialReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CountRestGiftPkgSerialReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CountRestGiftPkgSerialResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CountRestGiftPkgSerialResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CountRestGiftPkgSerialResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetApplyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetApplyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetApplyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyId", wireType)
			}
			m.ApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetApplyResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetApplyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetApplyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Apply", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Apply == nil {
				m.Apply = &StGiftPkgApply{}
			}
			if err := m.Apply.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftpkgOpHistoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftpkgOpHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftpkgOpHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordBegintime", wireType)
			}
			m.RecordBegintime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecordBegintime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordEndtime", wireType)
			}
			m.RecordEndtime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecordEndtime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("record_begintime")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("record_endtime")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("size")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GiftpkgOpRecord) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GiftpkgOpRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GiftpkgOpRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			m.OpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_content")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftpkgOpHistoryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftpkgOpHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftpkgOpHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Records", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Records = append(m.Records, &GiftpkgOpRecord{})
			if err := m.Records[len(m.Records)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDeviceLastFetchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDeviceLastFetchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDeviceLastFetchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDeviceLastFetchResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDeviceLastFetchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDeviceLastFetchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastFetchTime", wireType)
			}
			m.LastFetchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastFetchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_fetch_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOfficalLastOpTimeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOfficalLastOpTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOfficalLastOpTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftpkgId", wireType)
			}
			m.GiftpkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftpkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giftpkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOfficalLastOpTimeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOfficalLastOpTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOfficalLastOpTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastOpTime", wireType)
			}
			m.LastOpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastOpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_op_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteGuildSourceGiftPkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteGuildSourceGiftPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteGuildSourceGiftPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftpkgId", wireType)
			}
			m.GiftpkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftpkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giftpkg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteGuildSourceGiftPkgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteGuildSourceGiftPkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteGuildSourceGiftPkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LockSerialForUserToPayReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LockSerialForUserToPayReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LockSerialForUserToPayReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftpkgId", wireType)
			}
			m.GiftpkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftpkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Orderid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Orderid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giftpkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("orderid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LockSerialForUserToPayResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LockSerialForUserToPayResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LockSerialForUserToPayResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RealFetchSerialForUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RealFetchSerialForUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RealFetchSerialForUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftpkgId", wireType)
			}
			m.GiftpkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftpkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Orderid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Orderid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giftpkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("orderid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RealFetchSerialForUserResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RealFetchSerialForUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RealFetchSerialForUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("serial_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReleaseSerialForGuildReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReleaseSerialForGuildReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReleaseSerialForGuildReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftpkgId", wireType)
			}
			m.GiftpkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftpkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Orderid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Orderid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giftpkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("orderid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReleaseSerialForGuildResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReleaseSerialForGuildResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReleaseSerialForGuildResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGuildPkgRedDiamondPriceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGuildPkgRedDiamondPriceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGuildPkgRedDiamondPriceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftpkgId", wireType)
			}
			m.GiftpkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftpkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamondCost", wireType)
			}
			m.RedDiamondCost = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamondCost |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("giftpkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("red_diamond_cost")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGuildPkgRedDiamondPriceResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGuildPkgRedDiamondPriceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGuildPkgRedDiamondPriceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserPkgSerialListNewByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserPkgSerialListNewByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserPkgSerialListNewByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserPkgSerialListNewByIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserPkgSerialListNewByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserPkgSerialListNewByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialList = append(m.SerialList, &StGiftPkgSerial{})
			if err := m.SerialList[len(m.SerialList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetGiftPackageStatInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetGiftPackageStatInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetGiftPackageStatInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GuildIdList = append(m.GuildIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGiftpkg
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGiftpkg
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GuildIdList = append(m.GuildIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildIdList", wireType)
			}
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameIdList = append(m.GameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGiftpkg
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGiftpkg
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameIdList = append(m.GameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildGameGiftPackageStatInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildGameGiftPackageStatInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildGameGiftPackageStatInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StatInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.StatInfo == nil {
				m.StatInfo = &StGuildGiftPkgStatInfo{}
			}
			if err := m.StatInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("stat_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetGiftPackageStatInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetGiftPackageStatInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetGiftPackageStatInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StatList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StatList = append(m.StatList, &StGuildGameGiftPackageStatInfo{})
			if err := m.StatList[len(m.StatList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchActGiftPkgCollectionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchActGiftPkgCollectionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchActGiftPkgCollectionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchActGiftPkgCollectionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchActGiftPkgCollectionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchActGiftPkgCollectionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgSerial", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftPkgSerial = append(m.GiftPkgSerial, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActGiftCollcetionFetStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActGiftCollcetionFetStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActGiftCollcetionFetStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActGiftCollcetionFetStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActGiftCollcetionFetStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActGiftCollcetionFetStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchStatus", wireType)
			}
			m.FetchStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fetch_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActGiftPkgCollectionConfReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActGiftPkgCollectionConfReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActGiftPkgCollectionConfReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActGiftPkgCollectionConfResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActGiftPkgCollectionConfResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActGiftPkgCollectionConfResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PkgIdList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PkgIdList = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg_id_list")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pic_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGiftpkg(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGiftpkg
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGiftpkg
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGiftpkg
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGiftpkg
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGiftpkg(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGiftpkg = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGiftpkg   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/giftpkg/giftpkg.proto", fileDescriptorGiftpkg) }

var fileDescriptorGiftpkg = []byte{
	// 4798 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x7c, 0x4b, 0x6c, 0x1c, 0xc9,
	0x79, 0xb0, 0x7a, 0x86, 0x1c, 0x92, 0xdf, 0xf0, 0x31, 0x2a, 0x89, 0xd4, 0x70, 0xc4, 0xc7, 0xa8,
	0xa4, 0x15, 0xa5, 0x95, 0x44, 0xed, 0x6a, 0x1f, 0xde, 0xe5, 0xd2, 0x34, 0xf8, 0x12, 0x45, 0xff,
	0x5a, 0x8a, 0x3b, 0x24, 0xfd, 0x67, 0xe1, 0x35, 0xc6, 0xad, 0xe9, 0xe2, 0xa8, 0xcd, 0x99, 0xe9,
	0xde, 0xa9, 0x1e, 0x49, 0xb4, 0x8d, 0x40, 0x4e, 0x0c, 0xc3, 0x09, 0x0c, 0x24, 0x58, 0x64, 0x03,
	0x04, 0x09, 0x8c, 0x20, 0x90, 0x03, 0x24, 0x70, 0x10, 0xf8, 0x12, 0x24, 0x39, 0xe7, 0xe0, 0x93,
	0x91, 0x20, 0xe7, 0x04, 0xc1, 0x26, 0x01, 0xf6, 0x90, 0xdc, 0x83, 0x9c, 0x82, 0xaa, 0xea, 0x47,
	0x75, 0x77, 0x75, 0x4f, 0x53, 0xa2, 0x7c, 0x92, 0x58, 0x8f, 0xef, 0x55, 0x5f, 0x7d, 0x8f, 0xfa,
	0xbe, 0x1e, 0x98, 0xa3, 0xa4, 0xfb, 0xd8, 0x6c, 0x10, 0x7a, 0xbb, 0x69, 0x1e, 0x3a, 0xf6, 0x51,
	0xd3, 0xfb, 0x77, 0xd1, 0xee, 0x5a, 0x8e, 0x85, 0x86, 0xb6, 0xc4, 0x9f, 0x95, 0x2b, 0x0d, 0xab,
	0xdd, 0xb6, 0x3a, 0xb7, 0x9d, 0xd6, 0x63, 0xdb, 0x6c, 0x1c, 0xb5, 0xc8, 0x6d, 0x7a, 0xf4, 0xb0,
	0x67, 0xb6, 0x1c, 0xb3, 0xe3, 0x1c, 0xdb, 0x44, 0x2c, 0xc7, 0xcf, 0x35, 0x38, 0xd7, 0xa3, 0xa4,
	0x7b, 0x97, 0x38, 0x8d, 0x47, 0x6c, 0xeb, 0xee, 0x51, 0xb3, 0x46, 0x3e, 0x45, 0xf3, 0x30, 0xdc,
	0xec, 0x99, 0x2d, 0xa3, 0x6e, 0x1a, 0x65, 0xad, 0x9a, 0xbb, 0x36, 0xb6, 0x36, 0xf0, 0xcb, 0x7f,
	0x9d, 0x3f, 0x53, 0x1b, 0xe2, 0xa3, 0xdb, 0x06, 0xba, 0x02, 0x45, 0x86, 0xb8, 0x6e, 0x1f, 0x35,
	0xd9, 0x9a, 0x9c, 0xb4, 0x66, 0xa4, 0x29, 0xe0, 0x6c, 0x1b, 0xe8, 0x32, 0xc0, 0x21, 0x83, 0x5c,
	0x77, 0xcc, 0x36, 0x29, 0xe7, 0xe5, 0x45, 0x7c, 0x7c, 0xdf, 0x6c, 0x13, 0x74, 0x09, 0x46, 0x0c,
	0xc2, 0x78, 0x62, 0x80, 0x06, 0xaa, 0xb9, 0x6b, 0x23, 0xee, 0x9a, 0x61, 0x31, 0xbc, 0x6d, 0xe0,
	0x0d, 0x38, 0x1f, 0xa7, 0x92, 0xda, 0xe8, 0x26, 0x4c, 0xf8, 0x54, 0x50, 0xd2, 0x35, 0xf5, 0x16,
	0xa7, 0xd6, 0x03, 0x30, 0xe6, 0x52, 0xb2, 0xc7, 0xa7, 0xf0, 0x5f, 0x6a, 0x70, 0xf1, 0xc0, 0x03,
	0xb3, 0xda, 0x70, 0xcc, 0xc7, 0xa6, 0x73, 0x2c, 0x31, 0x3d, 0x05, 0xf9, 0x5e, 0x84, 0x5f, 0x36,
	0x70, 0x9a, 0xbc, 0x5e, 0x83, 0x31, 0x9f, 0xd7, 0xfa, 0x23, 0xf2, 0x34, 0xc4, 0x6f, 0xd1, 0xe3,
	0xf7, 0x1e, 0x79, 0x8a, 0xef, 0xc3, 0x4c, 0x32, 0xad, 0x27, 0x66, 0xfd, 0x27, 0x1a, 0xe0, 0xf5,
	0x47, 0xa4, 0x71, 0x14, 0x01, 0xc5, 0xc1, 0xef, 0x39, 0xba, 0xd3, 0xa3, 0x2f, 0x2f, 0x81, 0x18,
	0x73, 0xf9, 0x24, 0xe6, 0x7e, 0xaa, 0xc1, 0xe5, 0xbe, 0xe4, 0x50, 0x1b, 0xbd, 0x06, 0xc5, 0x9e,
	0x69, 0xd4, 0xb9, 0xfc, 0x88, 0xa0, 0x6b, 0xd8, 0x85, 0x07, 0x3d, 0xd3, 0xb8, 0x2b, 0xc6, 0xd1,
	0x0d, 0x18, 0x77, 0x11, 0x7b, 0x2b, 0x73, 0xd2, 0x4a, 0x97, 0x28, 0x6f, 0x71, 0xf4, 0x9c, 0x34,
	0xc5, 0x39, 0xe1, 0xaf, 0xc1, 0x85, 0x2d, 0xe2, 0xb0, 0x03, 0xd8, 0x92, 0xe5, 0xc8, 0x64, 0x14,
	0x91, 0x85, 0xa6, 0x94, 0x05, 0xfe, 0x43, 0x0d, 0xca, 0x6a, 0x08, 0xd4, 0x46, 0x33, 0x50, 0xa0,
	0x9c, 0xc9, 0xd0, 0x6e, 0x77, 0x0c, 0xcd, 0xc2, 0x90, 0x65, 0x0b, 0xea, 0x64, 0x41, 0x17, 0x2c,
	0x9b, 0xab, 0x10, 0xdb, 0x2c, 0xce, 0x5b, 0x16, 0xaf, 0x3b, 0x16, 0xba, 0xb8, 0x03, 0x8a, 0x8b,
	0x8b, 0xff, 0x2b, 0x0f, 0x23, 0xd4, 0x71, 0x69, 0xca, 0xc6, 0x0c, 0x7a, 0x0b, 0x10, 0x79, 0xda,
	0x78, 0xa4, 0x77, 0x9a, 0xa4, 0xfe, 0x90, 0x34, 0xcd, 0x4e, 0x8c, 0xb8, 0xb3, 0xde, 0xfc, 0x9a,
	0x37, 0x8d, 0x6e, 0x43, 0xc9, 0xdf, 0x44, 0x3a, 0x46, 0xec, 0x56, 0x4c, 0x78, 0xb3, 0x9b, 0x62,
	0x92, 0xf1, 0xdd, 0xd4, 0xdb, 0x24, 0x4a, 0x79, 0x81, 0x0d, 0x6e, 0x1b, 0xa8, 0x02, 0x83, 0x66,
	0xc7, 0xe9, 0x5a, 0xe5, 0x41, 0x89, 0x6d, 0x31, 0x84, 0xca, 0x30, 0xd0, 0xd1, 0xdb, 0xa4, 0x5c,
	0x90, 0xa6, 0xf8, 0x08, 0x9a, 0x83, 0xa1, 0x86, 0xd5, 0x71, 0x48, 0xc7, 0x29, 0x0f, 0x49, 0x93,
	0xde, 0x20, 0x83, 0xda, 0xa3, 0x7a, 0x93, 0x94, 0x87, 0x65, 0xa8, 0x7c, 0x08, 0x2d, 0x42, 0x49,
	0x48, 0xb5, 0xee, 0x58, 0x8e, 0xde, 0xaa, 0x77, 0x7a, 0xed, 0xf2, 0x88, 0x44, 0xd9, 0xb8, 0x98,
	0xdd, 0x67, 0x93, 0x3b, 0xbd, 0x36, 0xd3, 0x56, 0xeb, 0xf0, 0x90, 0x74, 0x89, 0xc1, 0x97, 0x82,
	0xb4, 0x14, 0xdc, 0x09, 0xb6, 0x6c, 0x16, 0x86, 0x4c, 0x5a, 0xa7, 0x8f, 0xac, 0x27, 0xe5, 0xa2,
	0xa4, 0xa6, 0x05, 0x93, 0xee, 0x3d, 0xb2, 0x9e, 0xa0, 0x05, 0x18, 0xa5, 0x56, 0xaf, 0xdb, 0x20,
	0x75, 0x7e, 0x64, 0xe5, 0x51, 0x49, 0x43, 0x8b, 0x62, 0x66, 0x8b, 0x4d, 0x30, 0x45, 0x66, 0x02,
	0xec, 0x90, 0x16, 0x13, 0xd9, 0x98, 0xac, 0xc8, 0xee, 0xf8, 0xb6, 0x81, 0xff, 0x53, 0x83, 0x09,
	0xff, 0xb8, 0x85, 0x0a, 0xa2, 0x5b, 0x30, 0xec, 0x1d, 0x3a, 0x3f, 0xf1, 0xe2, 0x1d, 0xb4, 0xe8,
	0xba, 0x8d, 0x45, 0x7f, 0x6d, 0x6d, 0xc8, 0x3d, 0x7f, 0x49, 0x5b, 0x73, 0xe9, 0xda, 0x9a, 0x4f,
	0xd5, 0xd6, 0x81, 0x3e, 0xda, 0x3a, 0xa8, 0xd2, 0xd6, 0x05, 0x28, 0xec, 0xed, 0xaf, 0xee, 0x1f,
	0xec, 0xa1, 0x61, 0x18, 0xd8, 0x79, 0xb0, 0xb3, 0x59, 0x3a, 0x83, 0x46, 0x60, 0xf0, 0xee, 0xe6,
	0xfe, 0xfa, 0xbd, 0x92, 0xc6, 0x06, 0x6b, 0xab, 0x3b, 0x1b, 0xa5, 0x1c, 0xfe, 0x0f, 0x0d, 0xce,
	0x6e, 0x11, 0x8f, 0xf8, 0xfb, 0x26, 0x75, 0xd8, 0x5d, 0x9d, 0x81, 0x82, 0x75, 0x78, 0x48, 0x89,
	0x13, 0xbe, 0x68, 0x62, 0x8c, 0x69, 0x0d, 0x35, 0xbf, 0x1b, 0x56, 0x64, 0x3e, 0x12, 0x3b, 0x83,
	0x7c, 0xd2, 0x19, 0x5c, 0x85, 0x89, 0xe0, 0x0c, 0xea, 0x2d, 0x93, 0x3a, 0xe5, 0x81, 0x6a, 0xfe,
	0xda, 0x58, 0x6d, 0xcc, 0x3f, 0x02, 0x46, 0x8b, 0xac, 0xdb, 0x83, 0x12, 0x2c, 0x4f, 0xb7, 0x17,
	0x60, 0xd4, 0xea, 0xb4, 0x8e, 0xeb, 0x8f, 0x4d, 0x6a, 0x3e, 0x6c, 0x31, 0x3d, 0xd6, 0x7c, 0xbd,
	0x28, 0xb2, 0x99, 0x6f, 0x88, 0x09, 0x5c, 0x07, 0x14, 0xe5, 0x92, 0xda, 0xec, 0x40, 0xd9, 0x05,
	0xe6, 0xe8, 0xb5, 0x6a, 0x3e, 0xe9, 0x40, 0x6d, 0xb1, 0x85, 0xe9, 0x3c, 0x57, 0xe8, 0x10, 0xe3,
	0x62, 0x08, 0xbf, 0x03, 0x63, 0x01, 0x82, 0xec, 0xe6, 0xee, 0x5d, 0x18, 0x97, 0xb7, 0x51, 0x1b,
	0x5d, 0x81, 0x7c, 0xba, 0x7e, 0xb1, 0x69, 0xbc, 0x07, 0xd3, 0x5b, 0xc4, 0xf9, 0xf0, 0x38, 0xa4,
	0xa0, 0x2f, 0x79, 0x7a, 0xf8, 0x23, 0xa8, 0x24, 0x01, 0xa5, 0x36, 0x7a, 0x2b, 0x26, 0xac, 0x72,
	0x9c, 0x3a, 0xd7, 0x58, 0x7b, 0x22, 0xc3, 0x7f, 0xaa, 0xc1, 0x79, 0x7e, 0xe2, 0xab, 0xb6, 0xdd,
	0x3a, 0x3e, 0x49, 0xa0, 0x74, 0x11, 0x0a, 0x0a, 0xaf, 0x39, 0x68, 0x73, 0xc3, 0xba, 0x00, 0xa3,
	0x5d, 0xf2, 0x69, 0xcf, 0xf4, 0x4c, 0x86, 0x7c, 0x83, 0x8a, 0xde, 0x0c, 0xb3, 0x19, 0x97, 0x60,
	0x44, 0x67, 0x98, 0xeb, 0x6d, 0xda, 0x0c, 0xc7, 0x48, 0x7c, 0xf8, 0x43, 0xda, 0xc4, 0x17, 0x60,
	0x52, 0x41, 0x21, 0xb5, 0xf1, 0x2e, 0x4c, 0x05, 0x67, 0xc3, 0x67, 0x5f, 0x56, 0xc0, 0x7f, 0x9b,
	0x87, 0x71, 0x1a, 0x82, 0xd8, 0x5f, 0x0e, 0xf3, 0x20, 0x48, 0x8d, 0x4a, 0x62, 0x88, 0x8f, 0x86,
	0x04, 0x95, 0x8f, 0x0b, 0xea, 0x32, 0x80, 0xd8, 0xcd, 0x0d, 0x8d, 0xec, 0x1e, 0x84, 0x5c, 0x7c,
	0x5b, 0x23, 0x0c, 0xd5, 0xa0, 0xc2, 0x50, 0x85, 0x44, 0x58, 0x50, 0x89, 0x90, 0xc5, 0x54, 0xd6,
	0xe1, 0xa1, 0xd9, 0xd0, 0x5b, 0x75, 0xcf, 0xa6, 0x0d, 0x49, 0x90, 0xc6, 0xdc, 0xc9, 0x07, 0xc2,
	0xb4, 0x09, 0x73, 0xcf, 0x57, 0x33, 0x90, 0xb2, 0x03, 0x01, 0x77, 0x82, 0x01, 0x9d, 0x87, 0xe1,
	0x27, 0x7a, 0xc7, 0x89, 0x79, 0x8f, 0x21, 0x36, 0xea, 0x9e, 0x2d, 0xf7, 0x0e, 0x31, 0xa7, 0x31,
	0xcc, 0x87, 0xd9, 0x92, 0x8b, 0x50, 0xb0, 0xec, 0x3a, 0x0b, 0xcd, 0x8a, 0xb2, 0x6c, 0x2c, 0xfb,
	0xc0, 0x34, 0xf0, 0x0d, 0xdf, 0x46, 0x8e, 0xc0, 0xe0, 0xea, 0xee, 0xee, 0xfd, 0x8f, 0x4b, 0x67,
	0x10, 0x40, 0x61, 0x75, 0x7d, 0x7d, 0x73, 0x77, 0xbf, 0xa4, 0xb1, 0xff, 0xd7, 0x36, 0xbf, 0xbe,
	0xb9, 0xbe, 0x5f, 0xca, 0xe1, 0x36, 0x0f, 0x6c, 0xe2, 0xca, 0x40, 0x6d, 0xf4, 0xae, 0x27, 0x63,
	0xe9, 0x6a, 0x5c, 0x88, 0x5f, 0x0d, 0xbe, 0xc9, 0x15, 0x7b, 0x5f, 0x73, 0xb2, 0x24, 0xd0, 0xf9,
	0x7a, 0x69, 0x76, 0x7c, 0xdb, 0xdc, 0x4f, 0x63, 0x70, 0x8d, 0x47, 0x50, 0x8a, 0xbd, 0x2f, 0x4e,
	0x2b, 0xfe, 0x26, 0x8c, 0xef, 0x1f, 0xdb, 0x24, 0x64, 0xdf, 0x32, 0xd8, 0x29, 0xa6, 0x7f, 0x96,
	0x5d, 0xd7, 0x1b, 0x0d, 0xab, 0xd7, 0x71, 0x38, 0xa3, 0xde, 0x51, 0x8f, 0x58, 0xf6, 0xaa, 0x18,
	0xc6, 0x5f, 0x81, 0x89, 0x10, 0x70, 0x6e, 0x05, 0xb3, 0x58, 0xcf, 0x6f, 0x41, 0xe9, 0xc0, 0x36,
	0x74, 0xe7, 0x15, 0xd1, 0xf5, 0x23, 0x0d, 0x2a, 0x21, 0xf8, 0x2c, 0xce, 0x08, 0x82, 0xfe, 0x6c,
	0x31, 0xa0, 0x14, 0xb5, 0xe4, 0x14, 0x51, 0x4b, 0x98, 0x90, 0xbc, 0x9a, 0x90, 0xdf, 0xd2, 0xe0,
	0xbc, 0x24, 0xa1, 0x13, 0xc6, 0xd4, 0x68, 0x1e, 0x8a, 0x6e, 0x3c, 0xc6, 0x4f, 0x3d, 0x57, 0xcd,
	0x5f, 0x1b, 0xa9, 0x01, 0xf5, 0xcd, 0x7b, 0x36, 0x22, 0x96, 0x60, 0x52, 0x41, 0x03, 0xb5, 0xb9,
	0x81, 0x30, 0x8c, 0xba, 0xd8, 0x2c, 0x93, 0x30, 0xac, 0x1b, 0xc6, 0x3a, 0xdf, 0xcb, 0x32, 0xc8,
	0xd8, 0xe6, 0x7d, 0x8b, 0xeb, 0xe8, 0x49, 0xf8, 0x08, 0x34, 0x3f, 0xa7, 0xb6, 0x95, 0x21, 0x46,
	0xf3, 0x7d, 0x18, 0x1d, 0x50, 0x33, 0xba, 0x0a, 0x33, 0xc9, 0xb4, 0x66, 0xe3, 0xf7, 0x6d, 0x40,
	0x6b, 0x3a, 0xcb, 0xb9, 0x43, 0x21, 0xc1, 0x5c, 0x94, 0x4b, 0x16, 0xf0, 0x48, 0xea, 0xfc, 0x01,
	0x9c, 0x8b, 0xed, 0x92, 0x23, 0x82, 0x7c, 0x5a, 0x44, 0xf0, 0x37, 0x1a, 0x9c, 0xad, 0x11, 0xc3,
	0x4b, 0x06, 0x6b, 0xa4, 0x61, 0x75, 0x0d, 0x74, 0x1e, 0x72, 0x11, 0x79, 0xe6, 0xcc, 0x0c, 0x82,
	0xc4, 0x00, 0xcc, 0xb5, 0x2a, 0xfc, 0xca, 0x70, 0x97, 0xe3, 0xd8, 0x36, 0x98, 0x18, 0x44, 0x3e,
	0xd8, 0x8b, 0x24, 0x1e, 0xc3, 0x7c, 0xf8, 0xc0, 0x8c, 0xa6, 0x8c, 0x83, 0xca, 0xd4, 0x1e, 0xff,
	0x8b, 0x70, 0x8a, 0x0c, 0xb3, 0x97, 0x5d, 0x9d, 0x30, 0xd0, 0xbe, 0x09, 0x13, 0x22, 0xd1, 0x10,
	0xc8, 0x98, 0x3b, 0x90, 0xb9, 0x1a, 0xe3, 0x93, 0x5c, 0x2a, 0xae, 0xdb, 0x08, 0xd2, 0x92, 0x10,
	0x6b, 0x8e, 0x97, 0x90, 0xbc, 0x01, 0x67, 0xa9, 0x63, 0x75, 0xf5, 0x26, 0x91, 0x40, 0xca, 0x2c,
	0x4e, 0xb8, 0xd3, 0x3e, 0x50, 0x69, 0x47, 0x00, 0x7c, 0x50, 0xb1, 0xc3, 0x4f, 0x7a, 0x16, 0xa1,
	0x64, 0xf7, 0x1e, 0xb6, 0xcc, 0x86, 0x84, 0xa2, 0x20, 0x27, 0x49, 0x62, 0xd6, 0xc7, 0x10, 0xac,
	0x0f, 0x10, 0x0c, 0xc5, 0xd7, 0xfb, 0xf0, 0x17, 0x60, 0xd4, 0xd1, 0xad, 0x47, 0xba, 0xe5, 0x2a,
	0xea, 0xb0, 0x1c, 0x22, 0x89, 0x19, 0xae, 0xab, 0x4c, 0x7a, 0xc2, 0x25, 0xd8, 0x3a, 0xa5, 0xe2,
	0xa4, 0x64, 0x77, 0x3b, 0xc6, 0x27, 0x77, 0x75, 0x4a, 0xb9, 0xf3, 0x7e, 0x03, 0xce, 0x32, 0xcd,
	0x30, 0x4c, 0xbd, 0x6d, 0x75, 0x8c, 0xba, 0xdd, 0x35, 0x1b, 0xa4, 0x0c, 0x52, 0x68, 0x3e, 0xd1,
	0x25, 0xc6, 0x86, 0x98, 0xdd, 0x65, 0x93, 0x2c, 0xa3, 0x9f, 0xf2, 0xfc, 0x51, 0x24, 0xcd, 0xe8,
	0x1b, 0xfc, 0x04, 0x81, 0x56, 0x2e, 0x25, 0xd0, 0xca, 0xc7, 0xf2, 0x10, 0x76, 0xcf, 0xbb, 0x06,
	0xe9, 0xd6, 0x9d, 0x63, 0x9b, 0x85, 0x3d, 0x52, 0x8a, 0xc7, 0xc7, 0xd9, 0xfd, 0xc6, 0x66, 0xe0,
	0x63, 0xa3, 0x89, 0xc1, 0x9d, 0x58, 0xac, 0x1b, 0x72, 0x92, 0xd2, 0x96, 0x6c, 0xd9, 0xc1, 0x1f,
	0x68, 0x3c, 0xb4, 0xe6, 0x1b, 0x5d, 0x3c, 0x6b, 0xc7, 0x5b, 0x3c, 0x87, 0xc9, 0x24, 0x07, 0x29,
	0x0d, 0xca, 0x29, 0x52, 0xfc, 0x40, 0x4c, 0xf9, 0x14, 0x31, 0x0d, 0xc4, 0xe2, 0xd1, 0x36, 0x5c,
	0x4c, 0xa4, 0xea, 0x15, 0x48, 0xe1, 0x1f, 0x34, 0x38, 0xc7, 0x14, 0x29, 0x14, 0x64, 0x64, 0x61,
	0xbf, 0x6f, 0x0c, 0x1c, 0x0a, 0x05, 0xf3, 0xca, 0x50, 0x30, 0x8b, 0xe9, 0x67, 0x8b, 0x04, 0x25,
	0xfc, 0x55, 0x84, 0x65, 0x9c, 0xfe, 0x22, 0x3e, 0xbe, 0xa3, 0xb7, 0x09, 0x9e, 0x82, 0xf3, 0x71,
	0x2e, 0xa8, 0x8d, 0x7f, 0xa5, 0xc1, 0x64, 0x8d, 0x7c, 0x87, 0x34, 0x9c, 0xd3, 0x67, 0xf0, 0x3a,
	0x8c, 0x75, 0x39, 0xe8, 0x7a, 0x97, 0xe8, 0xd4, 0xea, 0x84, 0x9c, 0xf4, 0xa8, 0x98, 0xaa, 0xf1,
	0x99, 0x53, 0x64, 0xb4, 0x0c, 0x53, 0x2a, 0x7e, 0xa8, 0x8d, 0xf7, 0xe1, 0xbc, 0xa4, 0x38, 0x41,
	0x48, 0xf4, 0x52, 0x59, 0x1d, 0x5e, 0x83, 0x49, 0x05, 0x54, 0x6a, 0xa3, 0xeb, 0x72, 0x4c, 0x97,
	0xa8, 0x83, 0xdc, 0x0d, 0x7e, 0x02, 0xd3, 0xbe, 0x0f, 0x3d, 0x39, 0x79, 0x73, 0x50, 0x14, 0xe4,
	0x05, 0x91, 0xd2, 0x58, 0x6d, 0x84, 0x53, 0xc7, 0xc3, 0xe0, 0x5d, 0xa8, 0x24, 0x41, 0x7f, 0xb1,
	0xfb, 0x82, 0x75, 0x28, 0xf3, 0x07, 0x5d, 0x0f, 0x9c, 0x17, 0xb1, 0x9f, 0x5e, 0x31, 0x01, 0xaf,
	0xc1, 0x74, 0x02, 0x0a, 0xf1, 0x52, 0x6c, 0xd2, 0xba, 0xee, 0x0e, 0x85, 0xd0, 0x80, 0x49, 0xbd,
	0xa5, 0xb8, 0x1b, 0xc9, 0x47, 0xee, 0x99, 0xcc, 0x9f, 0x1d, 0xbf, 0x4a, 0x23, 0x8e, 0x3b, 0x91,
	0x3c, 0xc6, 0xc7, 0xf9, 0x8a, 0x72, 0xae, 0x1a, 0x8c, 0xec, 0x73, 0xbf, 0x78, 0x7a, 0x11, 0x29,
	0x7e, 0x13, 0xc0, 0x83, 0x49, 0x6d, 0x76, 0xeb, 0xdc, 0xf8, 0x94, 0xd9, 0x29, 0xb9, 0xec, 0x30,
	0x22, 0xc6, 0x77, 0x7a, 0x6d, 0xfc, 0xdb, 0x1a, 0x4c, 0x8a, 0x3d, 0xab, 0x86, 0x71, 0x40, 0x89,
	0x08, 0x29, 0x4f, 0x31, 0x4a, 0x0e, 0x53, 0x91, 0x57, 0x53, 0x51, 0x86, 0x29, 0x15, 0x11, 0xd4,
	0xc6, 0x3f, 0xd6, 0x60, 0x62, 0xbd, 0x4b, 0x74, 0x87, 0xd4, 0x88, 0x61, 0x9f, 0x6a, 0xd9, 0x4b,
	0xf5, 0x70, 0x9c, 0x4f, 0x7e, 0x38, 0xc6, 0xef, 0x40, 0x29, 0x4c, 0x89, 0x88, 0xce, 0xbb, 0xfc,
	0xaf, 0x28, 0x2d, 0xc3, 0x62, 0x78, 0xdb, 0xc0, 0xdf, 0x83, 0x71, 0x37, 0x46, 0x36, 0xb2, 0xbe,
	0x46, 0x85, 0x03, 0xe2, 0x9c, 0x32, 0x20, 0x8e, 0xf0, 0x98, 0x4f, 0x7a, 0xf1, 0x9b, 0x08, 0x21,
	0xcf, 0xaa, 0x16, 0x9f, 0x69, 0x70, 0x6e, 0x8b, 0x38, 0x62, 0xdb, 0x49, 0xae, 0x5f, 0x16, 0xd2,
	0x5f, 0x34, 0x80, 0xd8, 0xe3, 0x7e, 0x20, 0x42, 0x13, 0xb5, 0xd1, 0x07, 0x50, 0xec, 0xf2, 0x04,
	0x44, 0xbe, 0x9f, 0x15, 0xff, 0x7e, 0xc6, 0xf2, 0x94, 0x1a, 0x88, 0xe5, 0xdc, 0x24, 0x7e, 0xcc,
	0xdf, 0x6a, 0xc5, 0x9a, 0x0d, 0xe2, 0xe8, 0x66, 0xeb, 0xb4, 0xf8, 0xc4, 0xbf, 0xca, 0xc1, 0xa8,
	0x0c, 0x38, 0xb2, 0x49, 0x53, 0x0a, 0xa7, 0xef, 0x85, 0xca, 0x74, 0xf0, 0xcc, 0xd2, 0x36, 0x98,
	0xb2, 0x5a, 0xdd, 0x58, 0xc6, 0x04, 0xee, 0x04, 0xcb, 0x99, 0x82, 0x3b, 0x10, 0xe4, 0x05, 0x83,
	0xf1, 0x3b, 0x20, 0xe7, 0x05, 0xb1, 0x3b, 0x53, 0x48, 0x2f, 0xb6, 0x70, 0x6c, 0x24, 0xfe, 0x4e,
	0x27, 0xc8, 0x20, 0xde, 0x23, 0x5d, 0x8f, 0x3f, 0x7d, 0x88, 0x65, 0x72, 0xf6, 0x00, 0x62, 0x82,
	0x27, 0x6f, 0x35, 0x49, 0x29, 0xbd, 0xb3, 0xe2, 0xe7, 0x3f, 0xee, 0x89, 0xd5, 0xe0, 0xa3, 0xae,
	0xef, 0x9e, 0x8c, 0xa8, 0x80, 0xbb, 0x65, 0xb4, 0x2b, 0xfd, 0x85, 0xff, 0x49, 0x83, 0xc9, 0x2d,
	0xf3, 0xb1, 0x97, 0x80, 0x9f, 0xfe, 0x33, 0xc1, 0x02, 0x8c, 0xfa, 0xd1, 0xe2, 0x43, 0xd2, 0x0d,
	0xbf, 0x1e, 0x7b, 0x01, 0xe3, 0x43, 0xd2, 0x7d, 0xb1, 0x50, 0x2a, 0xa7, 0x0a, 0xa5, 0x3e, 0x81,
	0x49, 0xee, 0x83, 0xb7, 0x0f, 0xbd, 0xcb, 0x6f, 0x9f, 0x9e, 0xe5, 0xc1, 0xef, 0xc3, 0x94, 0x0a,
	0x3a, 0xb5, 0x19, 0x78, 0x93, 0x0a, 0x4d, 0x0a, 0x55, 0x81, 0x87, 0x4c, 0xca, 0x97, 0x62, 0x03,
	0xce, 0x51, 0xc9, 0xc7, 0xb2, 0x43, 0x65, 0x29, 0x75, 0x5f, 0xb2, 0x6e, 0xc2, 0x44, 0x4b, 0xa7,
	0x4e, 0x5d, 0x7a, 0x5d, 0x0e, 0xe5, 0xdc, 0x6c, 0xd2, 0x87, 0xe8, 0x3e, 0x67, 0x7a, 0xd1, 0x04,
	0x47, 0x97, 0xf9, 0x39, 0xf3, 0x88, 0x87, 0x01, 0x8a, 0xbd, 0xd4, 0x46, 0x0f, 0x60, 0x52, 0x6c,
	0x0e, 0xc8, 0x90, 0x2d, 0xce, 0x4c, 0x34, 0xfc, 0x92, 0x79, 0xac, 0xa1, 0x66, 0x68, 0x88, 0xdb,
	0x9e, 0xf7, 0x79, 0xae, 0xca, 0xc7, 0x88, 0x71, 0x32, 0x3a, 0xbf, 0x13, 0xf0, 0x18, 0xda, 0xfa,
	0x2a, 0xc8, 0xfc, 0xbb, 0x1c, 0x4c, 0x85, 0x23, 0x4a, 0x16, 0x86, 0x6e, 0x77, 0x0e, 0x2d, 0x76,
	0x71, 0x85, 0x21, 0x88, 0xbf, 0x4f, 0x01, 0x9f, 0x10, 0x59, 0xff, 0xdb, 0x70, 0x4e, 0x7a, 0x33,
	0x21, 0xde, 0x73, 0x56, 0xa8, 0x36, 0x1d, 0xbc, 0x9b, 0xb8, 0xfe, 0x5f, 0xfd, 0x30, 0x92, 0x3f,
	0xf1, 0xc3, 0xc8, 0xc0, 0x49, 0x1f, 0x46, 0x06, 0x4f, 0xf8, 0x30, 0x52, 0x48, 0x7e, 0x18, 0xc1,
	0xff, 0xa3, 0x41, 0xc5, 0x93, 0x9d, 0xde, 0x26, 0x51, 0xf9, 0x49, 0xa9, 0xb6, 0xa6, 0x48, 0xb5,
	0x23, 0xe2, 0xcd, 0x9d, 0x4c, 0xbc, 0xf9, 0x74, 0xf1, 0xaa, 0x58, 0x97, 0x1f, 0x2f, 0xb2, 0xb0,
	0x3e, 0x18, 0x5f, 0xef, 0xb3, 0xfe, 0xd5, 0xe0, 0x15, 0x22, 0xc2, 0x76, 0x26, 0x0d, 0xff, 0x73,
	0x2d, 0x78, 0x2f, 0x88, 0xed, 0xa7, 0x36, 0x5a, 0x86, 0x11, 0xea, 0xe8, 0x4e, 0xdd, 0xec, 0x1c,
	0x5a, 0xae, 0xc1, 0x9f, 0x4f, 0x48, 0x80, 0xfc, 0x7d, 0xc3, 0xd4, 0x13, 0xfc, 0x36, 0x8c, 0x73,
	0xc1, 0x73, 0x10, 0x7e, 0xfa, 0x55, 0xbc, 0x73, 0x39, 0x06, 0x22, 0x7e, 0x6a, 0xb5, 0x51, 0xb6,
	0x95, 0xfd, 0xc5, 0xaf, 0xc7, 0x12, 0x4c, 0x7b, 0xf1, 0xaa, 0x13, 0x7b, 0x33, 0x4f, 0x3f, 0x60,
	0xfc, 0x1e, 0x54, 0x92, 0xf6, 0x52, 0x3b, 0x35, 0x7f, 0x58, 0x84, 0xa2, 0x67, 0x00, 0x5c, 0x71,
	0xfa, 0x39, 0xbd, 0xa6, 0xc8, 0xe9, 0xf1, 0x57, 0x61, 0x34, 0x58, 0xcf, 0xab, 0xd1, 0x83, 0x7c,
	0x4a, 0x95, 0xe7, 0xca, 0xe9, 0x8c, 0x58, 0x85, 0xff, 0x59, 0xf3, 0xeb, 0x93, 0xf6, 0x51, 0xf3,
	0x81, 0x2d, 0xc5, 0x84, 0xb7, 0xa1, 0xe4, 0x86, 0x5f, 0x41, 0xd7, 0x89, 0x4c, 0xc2, 0x84, 0x98,
	0x0d, 0x7a, 0x4e, 0x6e, 0x30, 0x7f, 0xcd, 0x37, 0x78, 0x1d, 0x27, 0x21, 0x63, 0x2e, 0xe6, 0xbc,
	0x7e, 0x93, 0x17, 0x0c, 0x16, 0x23, 0xde, 0x74, 0x50, 0xfd, 0xf8, 0xfe, 0x47, 0x1a, 0x4c, 0xf8,
	0x2c, 0xb9, 0x8f, 0xd8, 0x52, 0xab, 0x84, 0xa6, 0x68, 0x95, 0xc8, 0x52, 0xcb, 0x71, 0x17, 0x79,
	0x2d, 0x2d, 0x91, 0x12, 0xc7, 0xba, 0xdb, 0xd4, 0xe2, 0x22, 0x12, 0x6f, 0x86, 0x61, 0x44, 0xd2,
	0x83, 0x61, 0x4c, 0xe0, 0x3c, 0xf5, 0x1f, 0x12, 0x42, 0xa2, 0xb1, 0xda, 0x78, 0x84, 0x9b, 0x9a,
	0xb7, 0xb0, 0x4f, 0xfd, 0x6f, 0x72, 0x8b, 0x38, 0x1b, 0xbc, 0x01, 0xeb, 0xbe, 0x4e, 0x1d, 0xd7,
	0xaf, 0x7f, 0x1a, 0x6e, 0xfa, 0xd3, 0x94, 0x4d, 0x7f, 0x3f, 0x14, 0x8a, 0x11, 0xdb, 0x4c, 0xed,
	0xc4, 0x3e, 0x35, 0xcf, 0x9b, 0x4b, 0xaf, 0xf5, 0x31, 0x6f, 0x7e, 0xd7, 0x6f, 0xc6, 0xcb, 0x96,
	0xe8, 0xd4, 0xb9, 0xb4, 0x1e, 0x88, 0x82, 0x2e, 0x23, 0x43, 0x94, 0x7f, 0x33, 0x05, 0x3d, 0x2c,
	0xa6, 0x12, 0x62, 0x53, 0x66, 0x8b, 0xde, 0x73, 0x51, 0x59, 0x8d, 0x80, 0xda, 0xe8, 0x2a, 0x8c,
	0x72, 0x86, 0x54, 0x7a, 0x03, 0x2d, 0x7f, 0x2d, 0x4b, 0x66, 0x2f, 0x6e, 0x90, 0x16, 0x71, 0x44,
	0x5f, 0xca, 0x9e, 0x68, 0x51, 0x09, 0x4a, 0x36, 0x61, 0x42, 0x34, 0x25, 0x21, 0x99, 0x32, 0xee,
	0xfe, 0xf5, 0xb5, 0x39, 0x98, 0x49, 0xa6, 0x84, 0xda, 0xf8, 0x07, 0x1a, 0x4c, 0xdf, 0xb7, 0x1a,
	0x47, 0xc2, 0x1a, 0xdd, 0xb5, 0xba, 0x07, 0x94, 0x74, 0xf7, 0xad, 0x5d, 0xfd, 0xf8, 0xd4, 0x44,
	0x8a, 0xe6, 0x60, 0x88, 0xbf, 0x8f, 0xbb, 0xa7, 0xea, 0x77, 0x7d, 0xb9, 0x83, 0x78, 0x06, 0x2a,
	0x49, 0x24, 0xb8, 0x14, 0xd6, 0x88, 0xeb, 0xd3, 0x42, 0x6b, 0x7e, 0x7d, 0x14, 0xae, 0x42, 0x25,
	0x89, 0x84, 0xac, 0x99, 0xf6, 0x33, 0x0d, 0xca, 0x35, 0xd2, 0x22, 0x3a, 0x25, 0x3e, 0x04, 0x3f,
	0x05, 0xf9, 0xf5, 0x70, 0x71, 0x91, 0x09, 0x52, 0x49, 0x01, 0xb5, 0xf1, 0xe7, 0x1a, 0xcc, 0xee,
	0x05, 0x0f, 0x90, 0xb5, 0x70, 0xbd, 0xe5, 0xf4, 0x88, 0x5c, 0x64, 0x5e, 0x24, 0x28, 0xf5, 0x34,
	0x2c, 0x1a, 0xb6, 0xf8, 0xe3, 0x41, 0xa5, 0x67, 0xdd, 0xa2, 0x0e, 0xae, 0xc2, 0x5c, 0x1a, 0x59,
	0xd4, 0xc6, 0x77, 0x61, 0xce, 0xed, 0xed, 0x0c, 0xb5, 0x17, 0xed, 0x90, 0x27, 0x6b, 0xc7, 0xdb,
	0xd9, 0x33, 0x3c, 0xfc, 0x09, 0xcc, 0xa7, 0xc2, 0xa1, 0x36, 0x7a, 0x3f, 0x5c, 0x0a, 0xee, 0xd7,
	0xb0, 0x24, 0x15, 0x89, 0xf1, 0x21, 0xcc, 0x85, 0xca, 0xb0, 0x7a, 0xe3, 0x48, 0x6f, 0x12, 0x39,
	0x52, 0xc2, 0x30, 0xe6, 0xc9, 0x37, 0x00, 0x3f, 0x56, 0x2b, 0xba, 0xe2, 0xe5, 0x2f, 0x8d, 0x55,
	0x18, 0x75, 0xc3, 0x0c, 0xf9, 0x2d, 0x19, 0x44, 0x94, 0xc1, 0xf1, 0xfc, 0x54, 0x83, 0xb9, 0x68,
	0x48, 0x13, 0xc6, 0xf5, 0xd2, 0x85, 0xa1, 0x50, 0x44, 0x96, 0x3f, 0x61, 0x44, 0x86, 0x9b, 0x30,
	0x9f, 0x2a, 0x08, 0x6a, 0xa3, 0x0d, 0x17, 0x81, 0x24, 0xe4, 0x85, 0xc4, 0x78, 0x2d, 0xb2, 0x9f,
	0x23, 0xe2, 0x92, 0x78, 0x0a, 0x33, 0x5e, 0xbf, 0xb6, 0x4b, 0xcd, 0xba, 0xd5, 0x6a, 0x91, 0x86,
	0x63, 0x5a, 0x9d, 0xb4, 0xf6, 0xea, 0x3e, 0xdc, 0x87, 0x7c, 0x65, 0x5e, 0xe9, 0x2b, 0xb7, 0x60,
	0x36, 0x05, 0x33, 0x77, 0x24, 0x8a, 0x76, 0xf1, 0xfc, 0xb5, 0x91, 0x68, 0xa3, 0xf8, 0xf7, 0xa1,
	0xca, 0x82, 0x39, 0x01, 0x86, 0xc1, 0x68, 0x10, 0x06, 0xe3, 0x2e, 0x71, 0xfa, 0x77, 0x89, 0xbf,
	0x3c, 0x1b, 0xf7, 0xe1, 0x52, 0x1f, 0xec, 0xd4, 0x46, 0x0b, 0x30, 0x2a, 0xfc, 0xbb, 0xa2, 0x87,
	0xba, 0x78, 0x18, 0x74, 0x90, 0xe3, 0xaf, 0xf1, 0x6b, 0xaa, 0x12, 0xc9, 0xba, 0xd5, 0x39, 0xcc,
	0x10, 0x43, 0x1f, 0xf2, 0xfb, 0x99, 0x0c, 0x40, 0x34, 0xf8, 0xc8, 0x95, 0x96, 0x90, 0x29, 0xf6,
	0xeb, 0x2d, 0x0c, 0x8f, 0x6d, 0x36, 0xea, 0xbd, 0x6e, 0x2b, 0x14, 0xd7, 0x15, 0x6c, 0xb3, 0x71,
	0xd0, 0x6d, 0xbd, 0xfe, 0x09, 0x0c, 0xad, 0xdf, 0x5b, 0xdd, 0xd9, 0xd9, 0xbc, 0x8f, 0x26, 0xa0,
	0x78, 0xb0, 0xb3, 0xb7, 0xbb, 0xb9, 0xbe, 0x7d, 0x77, 0x7b, 0x73, 0xa3, 0x74, 0x06, 0x8d, 0xc1,
	0xc8, 0xc1, 0xce, 0xf6, 0x37, 0x36, 0x6b, 0x7b, 0xab, 0xf7, 0x4b, 0x1a, 0x1a, 0x81, 0xc1, 0xb5,
	0xd5, 0xed, 0x8d, 0x83, 0x52, 0x8e, 0xfd, 0xf7, 0xa3, 0xed, 0x7b, 0x0f, 0x1e, 0x94, 0xf2, 0x68,
	0x08, 0xf2, 0x7b, 0x1f, 0xbf, 0x57, 0x1a, 0x40, 0x05, 0xc8, 0x1d, 0xac, 0x97, 0x06, 0x11, 0x40,
	0xe1, 0x37, 0xb6, 0x57, 0x1f, 0x7c, 0xb8, 0x5d, 0x2a, 0xdc, 0xf9, 0xef, 0x77, 0xc1, 0xfb, 0x2a,
	0x04, 0xfd, 0x48, 0x83, 0x52, 0xf4, 0x4b, 0x0a, 0x14, 0xe4, 0xed, 0x8a, 0x4f, 0x41, 0x2a, 0xb3,
	0x29, 0xb3, 0xd4, 0xc6, 0xef, 0x3c, 0x7b, 0xfe, 0x65, 0x5e, 0xfb, 0xdd, 0xe7, 0x5f, 0xe6, 0x0b,
	0xbd, 0xa5, 0xe6, 0x92, 0xbd, 0xf4, 0xd9, 0xf3, 0x2f, 0xf3, 0xd5, 0x5b, 0xbd, 0xea, 0x72, 0xcf,
	0x34, 0x56, 0xaa, 0xb7, 0x9a, 0xd5, 0x65, 0x7e, 0xc9, 0xf9, 0x1f, 0xf6, 0xb2, 0x10, 0xd6, 0x0a,
	0xfa, 0x3e, 0x8f, 0xed, 0x14, 0x3d, 0x9a, 0x08, 0x07, 0x11, 0x67, 0x52, 0x67, 0x68, 0xe5, 0x72,
	0xdf, 0x35, 0xd4, 0xc6, 0xd3, 0x8c, 0xb2, 0x1c, 0xa3, 0x2c, 0xd7, 0xe3, 0x54, 0x0d, 0x7b, 0x54,
	0xa1, 0xbf, 0xd6, 0xe0, 0x6c, 0xac, 0x59, 0x12, 0x05, 0x9c, 0xaa, 0x5a, 0x3d, 0x2b, 0x73, 0x69,
	0xd3, 0xd4, 0xc6, 0xdf, 0x64, 0xf8, 0xf2, 0x0c, 0x1f, 0x08, 0x49, 0x74, 0x97, 0x74, 0x8e, 0x77,
	0x2d, 0x49, 0x1a, 0x55, 0x4f, 0x1c, 0xd5, 0x5b, 0xdd, 0xea, 0xb2, 0xdc, 0x09, 0xba, 0x52, 0xbd,
	0xa5, 0x57, 0x97, 0xfd, 0x7e, 0xc5, 0x15, 0x64, 0xf2, 0x07, 0xca, 0x68, 0xdf, 0x1e, 0x9a, 0x97,
	0x05, 0xa1, 0x68, 0xf1, 0xac, 0x54, 0xd3, 0x17, 0x50, 0x1b, 0x4f, 0x30, 0xb2, 0x07, 0x18, 0xd9,
	0x67, 0x18, 0xb1, 0x67, 0xd0, 0x47, 0x50, 0x94, 0xfa, 0x86, 0x50, 0x90, 0xbf, 0x85, 0x3b, 0xe7,
	0x2a, 0x65, 0xf5, 0x84, 0x07, 0x72, 0x50, 0x02, 0xe9, 0x52, 0x1f, 0x69, 0x51, 0x88, 0x50, 0x1f,
	0x6f, 0xac, 0x88, 0x50, 0xaf, 0xe8, 0x70, 0x10, 0xa8, 0x0a, 0x12, 0xaa, 0x6f, 0x43, 0x29, 0x5a,
	0xd5, 0x96, 0x14, 0x5c, 0x51, 0xb6, 0x97, 0x14, 0x5c, 0x59, 0x0e, 0xe7, 0x18, 0x86, 0x24, 0x0c,
	0x87, 0x80, 0xe2, 0xe5, 0x64, 0x34, 0x27, 0x3d, 0x09, 0x2b, 0x6a, 0xe7, 0x95, 0xf9, 0xd4, 0x79,
	0x0f, 0xcf, 0xb0, 0x84, 0xa7, 0x01, 0x67, 0x63, 0xfd, 0x5b, 0x92, 0x8e, 0xaa, 0x1a, 0xe9, 0x24,
	0x1d, 0x55, 0xf6, 0xb8, 0x09, 0x24, 0x23, 0x12, 0x92, 0x5f, 0x68, 0x72, 0xe7, 0xb6, 0xa8, 0x2d,
	0x2a, 0x54, 0xc6, 0x3b, 0x90, 0x8b, 0x89, 0x73, 0xd4, 0xc6, 0x87, 0x0c, 0x38, 0xf0, 0x0b, 0xd0,
	0x5c, 0xb2, 0x96, 0x5a, 0x4b, 0x74, 0x89, 0xf0, 0x0b, 0xf0, 0x21, 0xd7, 0x7b, 0xbd, 0x4d, 0xaa,
	0x5c, 0xd7, 0xad, 0xea, 0xb2, 0x48, 0xb2, 0x57, 0xaa, 0xb7, 0x5a, 0xd5, 0xe5, 0x96, 0xd9, 0x36,
	0xd9, 0x7f, 0x69, 0x75, 0x59, 0x6e, 0xba, 0x17, 0xf7, 0x82, 0x54, 0x97, 0x23, 0x0d, 0xf6, 0x2b,
	0xe8, 0x63, 0x18, 0x0b, 0xb5, 0x33, 0xa2, 0x69, 0x9f, 0xaa, 0x68, 0x1b, 0x65, 0x65, 0x66, 0xd1,
	0xff, 0x00, 0x6e, 0x71, 0xef, 0xff, 0xad, 0x89, 0x0f, 0xe0, 0x36, 0xdb, 0xb6, 0x73, 0x5c, 0xdf,
	0x5d, 0x13, 0xe2, 0x28, 0x4a, 0xe2, 0xf8, 0x5e, 0xd0, 0x10, 0x20, 0xf7, 0x9c, 0xa2, 0xb8, 0x22,
	0x46, 0xda, 0x59, 0x2b, 0x97, 0xfa, 0xac, 0xa0, 0x36, 0x9e, 0x63, 0xd8, 0x46, 0xb9, 0x41, 0x6a,
	0x72, 0xb9, 0x8c, 0xf9, 0xf6, 0x80, 0xdb, 0xc4, 0x6f, 0x03, 0x04, 0x52, 0x45, 0x53, 0x0a, 0x51,
	0x33, 0x44, 0x17, 0x94, 0xe3, 0xd4, 0xc6, 0x97, 0x18, 0xf8, 0x31, 0x09, 0x7c, 0x89, 0x83, 0x0f,
	0xc2, 0xd0, 0x15, 0xf4, 0xcc, 0xfd, 0x4a, 0x22, 0x54, 0xf3, 0x97, 0xed, 0x9e, 0xa2, 0xdb, 0x40,
	0xb6, 0x7b, 0xaa, 0x76, 0x01, 0x7c, 0x93, 0xe1, 0x1d, 0x67, 0x78, 0x07, 0x3c, 0xfb, 0x3f, 0x1d,
	0x62, 0x4c, 0xb6, 0x74, 0xe8, 0x98, 0x4b, 0x38, 0xf6, 0x5d, 0x54, 0x58, 0xc2, 0xaa, 0x0f, 0xaf,
	0xc2, 0x12, 0x56, 0x7e, 0x58, 0x85, 0x67, 0x18, 0x29, 0x13, 0x5c, 0x04, 0x82, 0x90, 0xa2, 0x8c,
	0xfa, 0x27, 0x9a, 0x5b, 0xbd, 0x88, 0x76, 0x10, 0xa0, 0x00, 0x74, 0x52, 0x13, 0x43, 0x05, 0xf7,
	0x5b, 0xe2, 0x49, 0xa2, 0x94, 0x55, 0x12, 0x9f, 0x6b, 0x11, 0x65, 0x73, 0x1f, 0x62, 0x92, 0x94,
	0x2d, 0x78, 0x18, 0x4b, 0x52, 0x36, 0xe9, 0x25, 0x07, 0xbf, 0xc7, 0x68, 0x39, 0xcb, 0xfd, 0x32,
	0xbb, 0x8c, 0x94, 0x53, 0x73, 0x39, 0x42, 0x8d, 0x7c, 0x13, 0xd9, 0xf5, 0x33, 0xbf, 0x4b, 0x56,
	0xd0, 0xb7, 0x60, 0x22, 0xd2, 0xbe, 0x89, 0x82, 0x6b, 0x1f, 0x6f, 0x07, 0xad, 0xcc, 0x24, 0x4f,
	0x7a, 0x16, 0x07, 0x49, 0x57, 0xac, 0x0e, 0x05, 0x51, 0x91, 0x47, 0x41, 0x33, 0xa4, 0xdf, 0xaf,
	0x50, 0x39, 0x17, 0x1b, 0xf3, 0xe4, 0x7a, 0x2e, 0xab, 0x5c, 0x7f, 0x47, 0x83, 0x51, 0xb9, 0x9c,
	0x8e, 0x02, 0x47, 0x15, 0xa9, 0xf7, 0x57, 0xa6, 0x13, 0x66, 0xa8, 0x8d, 0x37, 0x18, 0xce, 0xf3,
	0xae, 0xfc, 0x6c, 0x57, 0x7e, 0xb7, 0x13, 0xb1, 0x0a, 0xf9, 0x45, 0x0a, 0x98, 0x2b, 0xa8, 0x03,
	0x45, 0xa9, 0x4a, 0x2e, 0xf9, 0xd2, 0x70, 0xe1, 0x5e, 0xf2, 0xa5, 0x91, 0xa2, 0xba, 0xe0, 0x7d,
	0x32, 0x2b, 0xef, 0x7f, 0xac, 0x41, 0x29, 0x5a, 0xc9, 0x96, 0xdc, 0x9f, 0xa2, 0xf0, 0x5e, 0x99,
	0x4d, 0x99, 0xa5, 0x36, 0xfe, 0x3a, 0xc3, 0x3f, 0xc5, 0xf0, 0x0f, 0x37, 0x97, 0xba, 0xbe, 0x26,
	0xbd, 0x15, 0xa1, 0x81, 0xc7, 0x2f, 0x5e, 0x79, 0x2f, 0x49, 0xb3, 0x3e, 0xd7, 0x00, 0xc5, 0xbb,
	0x31, 0x24, 0xd7, 0xa9, 0xec, 0x17, 0x91, 0x5c, 0x67, 0x42, 0x2b, 0xc7, 0x0a, 0xa3, 0xf1, 0x02,
	0x3f, 0x2b, 0x7b, 0xa9, 0xe9, 0x52, 0x78, 0x3d, 0xd3, 0x59, 0xf1, 0x53, 0x7a, 0xa6, 0xc1, 0x44,
	0xa4, 0xfc, 0x8b, 0x2e, 0xc6, 0xc5, 0xe2, 0x17, 0xf1, 0x2b, 0x33, 0xc9, 0x93, 0xd4, 0xc6, 0x77,
	0x18, 0x39, 0xe5, 0x08, 0x39, 0xb3, 0xa9, 0x02, 0x43, 0x9f, 0x69, 0x70, 0x21, 0xa1, 0x47, 0x1f,
	0x5d, 0x56, 0xbb, 0xb7, 0x50, 0x17, 0x7f, 0x1f, 0x47, 0xf7, 0x36, 0x23, 0x69, 0x9a, 0x6b, 0x91,
	0xa7, 0xcb, 0x97, 0x98, 0x44, 0x98, 0x77, 0xa8, 0xda, 0x22, 0xa1, 0xad, 0x7a, 0xa2, 0x31, 0x69,
	0x95, 0x3e, 0xb2, 0x9e, 0xac, 0xa0, 0x06, 0xa0, 0x78, 0x01, 0x5b, 0x3a, 0x2e, 0x65, 0x75, 0x3b,
	0x8b, 0xcb, 0xad, 0x48, 0xf6, 0xe0, 0x33, 0x0d, 0x50, 0xbc, 0xea, 0x2b, 0x61, 0x51, 0x16, 0x9c,
	0x25, 0xa5, 0x50, 0x97, 0x8c, 0xf1, 0x07, 0x0c, 0xd1, 0x8c, 0x9f, 0x98, 0x74, 0x39, 0xd3, 0x57,
	0xe3, 0xa1, 0xb8, 0xf2, 0x38, 0x5a, 0xdc, 0x34, 0xc7, 0x8a, 0xb5, 0x61, 0xd3, 0xac, 0xaa, 0x03,
	0x87, 0x4d, 0xb3, 0xb2, 0xda, 0x2b, 0x44, 0x30, 0x2b, 0x89, 0xe0, 0x29, 0x0f, 0x8f, 0xa3, 0x25,
	0xd7, 0x70, 0x78, 0xac, 0xa8, 0xe5, 0x56, 0xaa, 0xe9, 0x0b, 0xbc, 0x90, 0x63, 0x2e, 0x39, 0xe4,
	0xf8, 0xa1, 0x16, 0x6b, 0x1e, 0xf6, 0x1f, 0x6d, 0x2e, 0x27, 0x05, 0xdf, 0xd2, 0x13, 0x52, 0xe5,
	0x4a, 0xff, 0x45, 0x1e, 0x19, 0xf3, 0xc9, 0x64, 0x3c, 0xd3, 0x60, 0x4a, 0x5d, 0xad, 0x92, 0xd2,
	0xc1, 0xc4, 0x52, 0x98, 0x94, 0x0e, 0x26, 0x97, 0xbc, 0xf0, 0x2c, 0xa3, 0xa1, 0x2a, 0xd1, 0x30,
	0x2a, 0x47, 0xa5, 0xe8, 0xff, 0xc3, 0xb0, 0x27, 0x44, 0x74, 0x3e, 0x26, 0x57, 0x86, 0x65, 0x52,
	0x31, 0xea, 0xf1, 0x76, 0x89, 0xc3, 0xd5, 0x5d, 0xde, 0xbc, 0xd4, 0x8d, 0x03, 0xfe, 0x13, 0xcd,
	0x4f, 0xdd, 0xe4, 0x72, 0x4b, 0x3c, 0x75, 0x8b, 0x54, 0xbf, 0xe2, 0xa9, 0x5b, 0xb4, 0x5a, 0x83,
	0xd7, 0x18, 0x6a, 0x2c, 0xa1, 0x7e, 0xf3, 0xd6, 0xc3, 0xea, 0xb2, 0x5f, 0x2b, 0x13, 0xe1, 0xb3,
	0x5b, 0x09, 0x4b, 0xb2, 0xc9, 0x8f, 0x79, 0x97, 0x52, 0xa4, 0xc8, 0x82, 0x42, 0x31, 0x5f, 0xbc,
	0x7c, 0x53, 0x99, 0x4f, 0x9d, 0xa7, 0x36, 0x9e, 0x67, 0xa4, 0x5d, 0xe6, 0xa4, 0x19, 0x9c, 0xb4,
	0xf1, 0x5b, 0x46, 0x75, 0xd9, 0x7f, 0xff, 0xe1, 0x5e, 0xfa, 0xbc, 0xaa, 0xec, 0x11, 0xbe, 0x62,
	0xaa, 0xb2, 0x4b, 0xf8, 0x8a, 0x29, 0xeb, 0x26, 0xc2, 0x6b, 0x5e, 0xc9, 0xea, 0x35, 0xff, 0x4c,
	0x83, 0x72, 0xd2, 0xa7, 0x32, 0xe8, 0x4a, 0x72, 0x4a, 0x25, 0x19, 0xbd, 0xd7, 0x32, 0xac, 0xa2,
	0x36, 0x7e, 0x9f, 0xd1, 0xf5, 0x5a, 0x24, 0xaa, 0xb8, 0xe2, 0x53, 0x16, 0x77, 0x53, 0x0d, 0xe3,
	0xa8, 0x7e, 0x68, 0xb6, 0x08, 0xd7, 0xa3, 0x29, 0x75, 0xd3, 0xae, 0x74, 0x47, 0x12, 0x7b, 0x86,
	0xa5, 0x3b, 0x92, 0xdc, 0xf9, 0x2b, 0xc8, 0xbb, 0x1a, 0x12, 0xdb, 0xd5, 0x24, 0xb1, 0xbd, 0x79,
	0x53, 0xfc, 0x7b, 0xe7, 0xe6, 0xe2, 0xe2, 0x22, 0xf7, 0xed, 0xe5, 0xa4, 0xba, 0x8f, 0x24, 0xc3,
	0x94, 0x22, 0x95, 0x24, 0xc3, 0xd4, 0x02, 0x12, 0x3f, 0xdb, 0x85, 0xac, 0x67, 0xdb, 0x85, 0x29,
	0x75, 0xa9, 0x47, 0x92, 0x5a, 0x62, 0x39, 0x4a, 0x92, 0x5a, 0x4a, 0xbd, 0x88, 0xdb, 0xf3, 0x6b,
	0x92, 0x3d, 0xef, 0xc2, 0x94, 0xba, 0x78, 0x23, 0xe1, 0x4c, 0x2c, 0x30, 0x49, 0x38, 0x93, 0x2b,
	0x40, 0x02, 0xe7, 0x75, 0x09, 0xa7, 0x05, 0x93, 0xca, 0x52, 0x8b, 0x94, 0xdb, 0x24, 0x15, 0x83,
	0x2a, 0xb8, 0xdf, 0x12, 0x0f, 0xe1, 0xeb, 0x12, 0xc2, 0xdf, 0x84, 0x4a, 0x72, 0x99, 0x04, 0x5d,
	0xf5, 0x41, 0xa6, 0x96, 0x78, 0x2a, 0x0b, 0x99, 0xd6, 0x79, 0xf8, 0x6f, 0x48, 0xf8, 0x7f, 0x20,
	0xba, 0x38, 0x92, 0xaa, 0x27, 0x68, 0x21, 0x9a, 0x2e, 0x26, 0xd4, 0x6a, 0x2a, 0xd7, 0xb2, 0x2d,
	0xf4, 0x68, 0xb8, 0x29, 0xd1, 0xf0, 0x57, 0x92, 0xfb, 0x8c, 0x7c, 0x79, 0xa2, 0x70, 0x9f, 0xf1,
	0x2f, 0x66, 0x14, 0xee, 0x53, 0xf1, 0x01, 0x0b, 0xde, 0x62, 0x78, 0x6f, 0xb9, 0x31, 0xb8, 0xee,
	0xc7, 0xe0, 0x6f, 0x44, 0xb4, 0x5e, 0x17, 0xee, 0x2c, 0x25, 0x00, 0xff, 0x0b, 0x0d, 0x2e, 0xa6,
	0x94, 0x42, 0x24, 0x99, 0xa5, 0x57, 0x8e, 0x24, 0x99, 0xf5, 0xa9, 0xac, 0xe0, 0xaf, 0x30, 0xda,
	0x17, 0xdd, 0xdb, 0xaa, 0x47, 0xec, 0x9d, 0xff, 0xa6, 0x23, 0x13, 0xef, 0xbe, 0xf2, 0xfc, 0x4c,
	0x83, 0x72, 0xd2, 0x2f, 0xe0, 0x48, 0x06, 0x25, 0xe5, 0x07, 0x7d, 0x24, 0x83, 0x92, 0xf6, 0x53,
	0x3a, 0x78, 0x99, 0x91, 0x78, 0xdb, 0x8f, 0x14, 0x85, 0xbf, 0x5a, 0x08, 0x47, 0x8a, 0x7e, 0x95,
	0x71, 0xa5, 0x1a, 0x71, 0x64, 0x7f, 0xaf, 0xc1, 0x7c, 0x9f, 0xdf, 0xb2, 0x41, 0x37, 0xc2, 0xc1,
	0x6a, 0xea, 0x8f, 0xf0, 0x54, 0x6e, 0x66, 0x5f, 0xec, 0x11, 0xff, 0xc6, 0x8b, 0x12, 0xff, 0x33,
	0x0d, 0xa6, 0x13, 0x0b, 0x47, 0xe8, 0xb5, 0x70, 0x56, 0x9a, 0x50, 0xd6, 0xaa, 0x5c, 0xcd, 0xb2,
	0xcc, 0xf3, 0x2e, 0x6f, 0x46, 0x48, 0xbd, 0x12, 0x26, 0xd5, 0xd7, 0xdf, 0x30, 0x9d, 0xbf, 0xd0,
	0x60, 0x36, 0xb5, 0x32, 0x84, 0xae, 0x87, 0xa2, 0xb3, 0xb4, 0xfa, 0x55, 0xe5, 0xf5, 0xac, 0x4b,
	0x3d, 0x9a, 0xef, 0xbc, 0x10, 0xcd, 0xbf, 0x27, 0x0c, 0x54, 0x52, 0xf9, 0x28, 0x6c, 0xa0, 0x52,
	0xaa, 0x54, 0x61, 0x03, 0x95, 0x56, 0x8d, 0x12, 0x31, 0xee, 0x5b, 0xaa, 0x18, 0x97, 0x53, 0xf4,
	0x73, 0x0d, 0xe6, 0x93, 0x6e, 0xc2, 0x8e, 0x75, 0xdf, 0x6c, 0x9b, 0xce, 0x2b, 0xb8, 0x59, 0x6f,
	0xbf, 0xa8, 0x72, 0xfe, 0x5c, 0x83, 0x6a, 0xa2, 0x46, 0x79, 0xf4, 0xbe, 0x0a, 0x1d, 0x7d, 0xe7,
	0x45, 0xce, 0xbb, 0x52, 0xf8, 0xf1, 0xf3, 0x2f, 0xf3, 0xff, 0xeb, 0xac, 0x95, 0x7e, 0xf9, 0xc5,
	0x9c, 0xf6, 0x8f, 0x5f, 0xcc, 0x69, 0xff, 0xf6, 0xc5, 0x9c, 0xf6, 0xfb, 0xff, 0x3e, 0x77, 0xe6,
	0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0x10, 0xb8, 0xd1, 0x26, 0xb5, 0x4d, 0x00, 0x00,
}
