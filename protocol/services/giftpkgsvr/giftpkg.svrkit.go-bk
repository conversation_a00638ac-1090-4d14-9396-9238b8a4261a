package Giftpkg

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/giftpkg/giftpkg.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for Giftpkg service
const GiftpkgMagic = uint16(14970)

// Client API for Giftpkg service

type GiftpkgClientInterface interface {
	UserFetchGiftPkg(ctx context.Context, uin uint32, in *UserFetchGiftPkgReq, opts ...svrkit.CallOption) (*UserFetchGiftPkgResp, error)
	GetMyGiftPkgSerialList(ctx context.Context, uin uint32, in *GetMyGiftPkgSerialListReq, opts ...svrkit.CallOption) (*GetMyGiftPkgSerialListResp, error)
	GuildApplyGiftPkg(ctx context.Context, uin uint32, in *GuildApplyGiftPkgReq, opts ...svrkit.CallOption) (*GuildApplyGiftPkgResp, error)
	GetGiftPkgApplyList(ctx context.Context, uin uint32, in *GetGiftPkgApplyListReq, opts ...svrkit.CallOption) (*GetGiftPkgApplyListResp, error)
	TypeGiftPkg(ctx context.Context, uin uint32, in *TypeGiftPkgReq, opts ...svrkit.CallOption) (*TypeGiftPkgResp, error)
	GetGuildGiftPkgList(ctx context.Context, uin uint32, in *GetGuildGiftPkgListReq, opts ...svrkit.CallOption) (*GetGuildGiftPkgListResp, error)
	PassGiftPkgApply(ctx context.Context, uin uint32, in *PassGiftPkgApplyReq, opts ...svrkit.CallOption) (*PassGiftPkgApplyResp, error)
	RejectGiftPkgApply(ctx context.Context, uin uint32, in *RejectGiftPkgApplyReq, opts ...svrkit.CallOption) (*RejectGiftPkgApplyResp, error)
	TypeGiftPkgSerial(ctx context.Context, uin uint32, in *TypeGiftPkgSerialReq, opts ...svrkit.CallOption) (*TypeGiftPkgSerialResp, error)
	GetGiftPkgList(ctx context.Context, uin uint32, in *GetGiftPkgListReq, opts ...svrkit.CallOption) (*GetGiftPkgListResp, error)
	UpdateGiftPkg(ctx context.Context, uin uint32, in *UpdateGiftPkgReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildApplyingList(ctx context.Context, uin uint32, in *GetGuildApplyingListReq, opts ...svrkit.CallOption) (*GetGuildApplyingListResp, error)
	GetGiftPkg(ctx context.Context, uin uint32, in *GetGiftPkgReq, opts ...svrkit.CallOption) (*GetGiftPkgResp, error)
	GetGuildPkgStatus(ctx context.Context, uin uint32, in *GetGuildPkgStatusReq, opts ...svrkit.CallOption) (*GetGuildPkgStatusResp, error)
	GetUserGiftPkgSerial(ctx context.Context, uin uint32, in *GetUserGiftPkgSerialReq, opts ...svrkit.CallOption) (*GetUserGiftPkgSerialResp, error)
	CheckGuildPkgApplying(ctx context.Context, uin uint32, in *CheckGuildPkgApplyingReq, opts ...svrkit.CallOption) (*CheckGuildPkgApplyingResp, error)
	GetGuildApplyHistory(ctx context.Context, uin uint32, in *GetGuildApplyHistoryReq, opts ...svrkit.CallOption) (*GetGuildApplyHistoryResp, error)
	BatchGetGiftPkg(ctx context.Context, uin uint32, in *BatchGetGiftPkgReq, opts ...svrkit.CallOption) (*BatchGetGiftPkgResp, error)
	Taohao(ctx context.Context, uin uint32, in *TaohaoReq, opts ...svrkit.CallOption) (*TaohaoResp, error)
	CreateRedpkg(ctx context.Context, uin uint32, in *CreateRedpkgReq, opts ...svrkit.CallOption) (*CreateRedpkgResp, error)
	FetchRedPkg(ctx context.Context, uin uint32, in *FetchRedPkgReq, opts ...svrkit.CallOption) (*FetchRedPkgResp, error)
	GetRedPkgHistory(ctx context.Context, uin uint32, in *GetRedPkgHistoryReq, opts ...svrkit.CallOption) (*GetRedPkgHistoryResp, error)
	TaohaoAddUsedCount(ctx context.Context, uin uint32, in *TaohaoAddUsedCountReq, opts ...svrkit.CallOption) (*TaohaoAddUsedCountResp, error)
	GetRedPkgDetail(ctx context.Context, uin uint32, in *GetRedPkgDetailReq, opts ...svrkit.CallOption) (*GetRedPkgDetailResp, error)
	UpdateGiftPkgShowStatus(ctx context.Context, uin uint32, in *UpdateGiftPkgShowStatusReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GiveGiftPkgToGuild(ctx context.Context, uin uint32, in *GiveGiftPkgToGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	CheckIfFetchRedpkg(ctx context.Context, uin uint32, in *CheckIfFetchRedpkgReq, opts ...svrkit.CallOption) (*CheckIfFetchRedpkgResp, error)
	GetApplyingGuildList(ctx context.Context, uin uint32, in *GetApplyingGuildListReq, opts ...svrkit.CallOption) (*GetApplyingGuildListResp, error)
	GetApplyedGuildList(ctx context.Context, uin uint32, in *GetApplyedGuildListReq, opts ...svrkit.CallOption) (*GetApplyedGuildListResp, error)
	GetGuildGiftPkgStatInfo(ctx context.Context, uin uint32, in *GetGuildGiftPkgStatInfoReq, opts ...svrkit.CallOption) (*GetGuildGiftPkgStatInfoResp, error)
	CountRestGiftPkgSerial(ctx context.Context, uin uint32, in *CountRestGiftPkgSerialReq, opts ...svrkit.CallOption) (*CountRestGiftPkgSerialResp, error)
	GetApply(ctx context.Context, uin uint32, in *GetApplyReq, opts ...svrkit.CallOption) (*GetApplyResp, error)
	GetGiftpkgOpHistory(ctx context.Context, uin uint32, in *GetGiftpkgOpHistoryReq, opts ...svrkit.CallOption) (*GetGiftpkgOpHistoryResp, error)
	GetDeviceLastFetch(ctx context.Context, uin uint32, in *GetDeviceLastFetchReq, opts ...svrkit.CallOption) (*GetDeviceLastFetchResp, error)
	GetOfficalLastOpTime(ctx context.Context, uin uint32, in *GetOfficalLastOpTimeReq, opts ...svrkit.CallOption) (*GetOfficalLastOpTimeResp, error)
	TypeGiftPkgSerialToGuild(ctx context.Context, uin uint32, in *TypeGiftPkgSerialToGuildReq, opts ...svrkit.CallOption) (*TypeGiftPkgSerialToGuildResp, error)
	BatchGetGuildPkgStatus(ctx context.Context, uin uint32, in *BatchGetGuildPkgStatusReq, opts ...svrkit.CallOption) (*BatchGetGuildPkgStatusResp, error)
	DeleteGuildSourceGiftPkg(ctx context.Context, uin uint32, in *DeleteGuildSourceGiftPkgReq, opts ...svrkit.CallOption) (*DeleteGuildSourceGiftPkgResp, error)
	LockSerialForUserToPay(ctx context.Context, uin uint32, in *LockSerialForUserToPayReq, opts ...svrkit.CallOption) (*LockSerialForUserToPayResp, error)
	RealFetchSerialForUser(ctx context.Context, uin uint32, in *RealFetchSerialForUserReq, opts ...svrkit.CallOption) (*RealFetchSerialForUserResp, error)
	ReleaseSerialForGuild(ctx context.Context, uin uint32, in *ReleaseSerialForGuildReq, opts ...svrkit.CallOption) (*ReleaseSerialForGuildResp, error)
	SetGuildPkgRedDiamondPrice(ctx context.Context, uin uint32, in *SetGuildPkgRedDiamondPriceReq, opts ...svrkit.CallOption) (*SetGuildPkgRedDiamondPriceResp, error)
	GetUserPkgSerialListNewById(ctx context.Context, uin uint32, in *GetUserPkgSerialListNewByIdReq, opts ...svrkit.CallOption) (*GetUserPkgSerialListNewByIdResp, error)
	GetGuildPkgListByGameId(ctx context.Context, uin uint32, in *GetGuildPkgListByGameIdReq, opts ...svrkit.CallOption) (*GetGuildPkgListByGameIdResp, error)
	BatchGetGiftPackageStatInfo(ctx context.Context, uin uint32, in *BatchGetGiftPackageStatInfoReq, opts ...svrkit.CallOption) (*BatchGetGiftPackageStatInfoResp, error)
	UserFetchActivityGiftPkg(ctx context.Context, uin uint32, in *UserFetchActivityGiftPkgReq, opts ...svrkit.CallOption) (*UserFetchActivityGiftPkgResp, error)
	CheckActivityGiftPkgFetchStatus(ctx context.Context, uin uint32, in *CheckActivityGiftPkgFetchStatusReq, opts ...svrkit.CallOption) (*CheckActivityGiftPkgFetchStatusResp, error)
	FetchActGiftPkgCollection(ctx context.Context, uin uint32, in *FetchActGiftPkgCollectionReq, opts ...svrkit.CallOption) (*FetchActGiftPkgCollectionResp, error)
	GetActGiftCollcetionFetStatus(ctx context.Context, uin uint32, in *GetActGiftCollcetionFetStatusReq, opts ...svrkit.CallOption) (*GetActGiftCollcetionFetStatusResp, error)
	GetActGiftPkgCollectionConf(ctx context.Context, uin uint32, in *GetActGiftPkgCollectionConfReq, opts ...svrkit.CallOption) (*GetActGiftPkgCollectionConfResp, error)
	UserFetchActivityGiftPkgNoLimit(ctx context.Context, uin uint32, in *UserFetchActivityGiftPkgReq, opts ...svrkit.CallOption) (*UserFetchActivityGiftPkgResp, error)
	FetchActGiftPkgCollectionNoLimit(ctx context.Context, uin uint32, in *FetchActGiftPkgCollectionReq, opts ...svrkit.CallOption) (*FetchActGiftPkgCollectionResp, error)
}

type GiftpkgClient struct {
	cc *svrkit.ClientConn
}

func NewGiftpkgClient(cc *svrkit.ClientConn) GiftpkgClientInterface {
	return &GiftpkgClient{cc}
}

const (
	commandGiftpkgGetSelfSvnInfo                   = 9995
	commandGiftpkgEcho                             = 9999
	commandGiftpkguserFetchGiftPkg                 = 1
	commandGiftpkgGetMyGiftPkgSerialList           = 2
	commandGiftpkgGuildApplyGiftPkg                = 3
	commandGiftpkgGetGiftPkgApplyList              = 4
	commandGiftpkgTypeGiftPkg                      = 5
	commandGiftpkgGetGuildGiftPkgList              = 6
	commandGiftpkgPassGiftPkgApply                 = 7
	commandGiftpkgRejectGiftPkgApply               = 8
	commandGiftpkgTypeGiftPkgSerial                = 9
	commandGiftpkgGetGiftPkgList                   = 10
	commandGiftpkgUpdateGiftPkg                    = 11
	commandGiftpkgGetGuildApplyingList             = 12
	commandGiftpkgGetGiftPkg                       = 13
	commandGiftpkgGetGuildPkgStatus                = 14
	commandGiftpkgGetUserGiftPkgSerial             = 15
	commandGiftpkgCheckGuildPkgApplying            = 16
	commandGiftpkgGetGuildApplyHistory             = 17
	commandGiftpkgBatchGetGiftPkg                  = 18
	commandGiftpkgTaohao                           = 19
	commandGiftpkgCreateRedpkg                     = 20
	commandGiftpkgFetchRedPkg                      = 21
	commandGiftpkgGetRedPkgHistory                 = 22
	commandGiftpkgTaohaoAddUsedCount               = 23
	commandGiftpkgGetRedPkgDetail                  = 24
	commandGiftpkgUpdateGiftPkgShowStatus          = 25
	commandGiftpkgGiveGiftPkgToGuild               = 26
	commandGiftpkgCheckIfFetchRedpkg               = 28
	commandGiftpkgGetApplyingGuildList             = 29
	commandGiftpkgGetApplyedGuildList              = 30
	commandGiftpkgGetGuildGiftPkgStatInfo          = 31
	commandGiftpkgCountRestGiftPkgSerial           = 32
	commandGiftpkgGetApply                         = 33
	commandGiftpkgGetGiftpkgOpHistory              = 34
	commandGiftpkgGetDeviceLastFetch               = 35
	commandGiftpkgGetOfficalLastOpTime             = 36
	commandGiftpkgTypeGiftPkgSerialToGuild         = 37
	commandGiftpkgBatchGetGuildPkgStatus           = 38
	commandGiftpkgDeleteGuildSourceGiftPkg         = 39
	commandGiftpkgLockSerialForUserToPay           = 40
	commandGiftpkgRealFetchSerialForUser           = 41
	commandGiftpkgReleaseSerialForGuild            = 42
	commandGiftpkgSetGuildPkgRedDiamondPrice       = 43
	commandGiftpkgGetUserPkgSerialListNewById      = 44
	commandGiftpkgGetGuildPkgListByGameId          = 45
	commandGiftpkgBatchGetGiftPackageStatInfo      = 46
	commandGiftpkgUserFetchActivityGiftPkg         = 47
	commandGiftpkgCheckActivityGiftPkgFetchStatus  = 48
	commandGiftpkgFetchActGiftPkgCollection        = 49
	commandGiftpkgGetActGiftCollcetionFetStatus    = 50
	commandGiftpkgGetActGiftPkgCollectionConf      = 51
	commandGiftpkgUserFetchActivityGiftPkgNoLimit  = 52
	commandGiftpkgFetchActGiftPkgCollectionNoLimit = 53
)

func (c *GiftpkgClient) UserFetchGiftPkg(ctx context.Context, uin uint32, in *UserFetchGiftPkgReq, opts ...svrkit.CallOption) (*UserFetchGiftPkgResp, error) {
	out := new(UserFetchGiftPkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkguserFetchGiftPkg, "/Giftpkg.Giftpkg/UserFetchGiftPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetMyGiftPkgSerialList(ctx context.Context, uin uint32, in *GetMyGiftPkgSerialListReq, opts ...svrkit.CallOption) (*GetMyGiftPkgSerialListResp, error) {
	out := new(GetMyGiftPkgSerialListResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetMyGiftPkgSerialList, "/Giftpkg.Giftpkg/GetMyGiftPkgSerialList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GuildApplyGiftPkg(ctx context.Context, uin uint32, in *GuildApplyGiftPkgReq, opts ...svrkit.CallOption) (*GuildApplyGiftPkgResp, error) {
	out := new(GuildApplyGiftPkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGuildApplyGiftPkg, "/Giftpkg.Giftpkg/GuildApplyGiftPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetGiftPkgApplyList(ctx context.Context, uin uint32, in *GetGiftPkgApplyListReq, opts ...svrkit.CallOption) (*GetGiftPkgApplyListResp, error) {
	out := new(GetGiftPkgApplyListResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetGiftPkgApplyList, "/Giftpkg.Giftpkg/GetGiftPkgApplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) TypeGiftPkg(ctx context.Context, uin uint32, in *TypeGiftPkgReq, opts ...svrkit.CallOption) (*TypeGiftPkgResp, error) {
	out := new(TypeGiftPkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgTypeGiftPkg, "/Giftpkg.Giftpkg/TypeGiftPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetGuildGiftPkgList(ctx context.Context, uin uint32, in *GetGuildGiftPkgListReq, opts ...svrkit.CallOption) (*GetGuildGiftPkgListResp, error) {
	out := new(GetGuildGiftPkgListResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetGuildGiftPkgList, "/Giftpkg.Giftpkg/GetGuildGiftPkgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) PassGiftPkgApply(ctx context.Context, uin uint32, in *PassGiftPkgApplyReq, opts ...svrkit.CallOption) (*PassGiftPkgApplyResp, error) {
	out := new(PassGiftPkgApplyResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgPassGiftPkgApply, "/Giftpkg.Giftpkg/PassGiftPkgApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) RejectGiftPkgApply(ctx context.Context, uin uint32, in *RejectGiftPkgApplyReq, opts ...svrkit.CallOption) (*RejectGiftPkgApplyResp, error) {
	out := new(RejectGiftPkgApplyResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgRejectGiftPkgApply, "/Giftpkg.Giftpkg/RejectGiftPkgApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) TypeGiftPkgSerial(ctx context.Context, uin uint32, in *TypeGiftPkgSerialReq, opts ...svrkit.CallOption) (*TypeGiftPkgSerialResp, error) {
	out := new(TypeGiftPkgSerialResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgTypeGiftPkgSerial, "/Giftpkg.Giftpkg/TypeGiftPkgSerial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetGiftPkgList(ctx context.Context, uin uint32, in *GetGiftPkgListReq, opts ...svrkit.CallOption) (*GetGiftPkgListResp, error) {
	out := new(GetGiftPkgListResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetGiftPkgList, "/Giftpkg.Giftpkg/GetGiftPkgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) UpdateGiftPkg(ctx context.Context, uin uint32, in *UpdateGiftPkgReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgUpdateGiftPkg, "/Giftpkg.Giftpkg/UpdateGiftPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetGuildApplyingList(ctx context.Context, uin uint32, in *GetGuildApplyingListReq, opts ...svrkit.CallOption) (*GetGuildApplyingListResp, error) {
	out := new(GetGuildApplyingListResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetGuildApplyingList, "/Giftpkg.Giftpkg/GetGuildApplyingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetGiftPkg(ctx context.Context, uin uint32, in *GetGiftPkgReq, opts ...svrkit.CallOption) (*GetGiftPkgResp, error) {
	out := new(GetGiftPkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetGiftPkg, "/Giftpkg.Giftpkg/GetGiftPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetGuildPkgStatus(ctx context.Context, uin uint32, in *GetGuildPkgStatusReq, opts ...svrkit.CallOption) (*GetGuildPkgStatusResp, error) {
	out := new(GetGuildPkgStatusResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetGuildPkgStatus, "/Giftpkg.Giftpkg/GetGuildPkgStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetUserGiftPkgSerial(ctx context.Context, uin uint32, in *GetUserGiftPkgSerialReq, opts ...svrkit.CallOption) (*GetUserGiftPkgSerialResp, error) {
	out := new(GetUserGiftPkgSerialResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetUserGiftPkgSerial, "/Giftpkg.Giftpkg/GetUserGiftPkgSerial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) CheckGuildPkgApplying(ctx context.Context, uin uint32, in *CheckGuildPkgApplyingReq, opts ...svrkit.CallOption) (*CheckGuildPkgApplyingResp, error) {
	out := new(CheckGuildPkgApplyingResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgCheckGuildPkgApplying, "/Giftpkg.Giftpkg/CheckGuildPkgApplying", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetGuildApplyHistory(ctx context.Context, uin uint32, in *GetGuildApplyHistoryReq, opts ...svrkit.CallOption) (*GetGuildApplyHistoryResp, error) {
	out := new(GetGuildApplyHistoryResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetGuildApplyHistory, "/Giftpkg.Giftpkg/GetGuildApplyHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) BatchGetGiftPkg(ctx context.Context, uin uint32, in *BatchGetGiftPkgReq, opts ...svrkit.CallOption) (*BatchGetGiftPkgResp, error) {
	out := new(BatchGetGiftPkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgBatchGetGiftPkg, "/Giftpkg.Giftpkg/BatchGetGiftPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) Taohao(ctx context.Context, uin uint32, in *TaohaoReq, opts ...svrkit.CallOption) (*TaohaoResp, error) {
	out := new(TaohaoResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgTaohao, "/Giftpkg.Giftpkg/Taohao", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) CreateRedpkg(ctx context.Context, uin uint32, in *CreateRedpkgReq, opts ...svrkit.CallOption) (*CreateRedpkgResp, error) {
	out := new(CreateRedpkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgCreateRedpkg, "/Giftpkg.Giftpkg/CreateRedpkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) FetchRedPkg(ctx context.Context, uin uint32, in *FetchRedPkgReq, opts ...svrkit.CallOption) (*FetchRedPkgResp, error) {
	out := new(FetchRedPkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgFetchRedPkg, "/Giftpkg.Giftpkg/FetchRedPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetRedPkgHistory(ctx context.Context, uin uint32, in *GetRedPkgHistoryReq, opts ...svrkit.CallOption) (*GetRedPkgHistoryResp, error) {
	out := new(GetRedPkgHistoryResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetRedPkgHistory, "/Giftpkg.Giftpkg/GetRedPkgHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) TaohaoAddUsedCount(ctx context.Context, uin uint32, in *TaohaoAddUsedCountReq, opts ...svrkit.CallOption) (*TaohaoAddUsedCountResp, error) {
	out := new(TaohaoAddUsedCountResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgTaohaoAddUsedCount, "/Giftpkg.Giftpkg/TaohaoAddUsedCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetRedPkgDetail(ctx context.Context, uin uint32, in *GetRedPkgDetailReq, opts ...svrkit.CallOption) (*GetRedPkgDetailResp, error) {
	out := new(GetRedPkgDetailResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetRedPkgDetail, "/Giftpkg.Giftpkg/GetRedPkgDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) UpdateGiftPkgShowStatus(ctx context.Context, uin uint32, in *UpdateGiftPkgShowStatusReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgUpdateGiftPkgShowStatus, "/Giftpkg.Giftpkg/UpdateGiftPkgShowStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GiveGiftPkgToGuild(ctx context.Context, uin uint32, in *GiveGiftPkgToGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGiveGiftPkgToGuild, "/Giftpkg.Giftpkg/GiveGiftPkgToGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) CheckIfFetchRedpkg(ctx context.Context, uin uint32, in *CheckIfFetchRedpkgReq, opts ...svrkit.CallOption) (*CheckIfFetchRedpkgResp, error) {
	out := new(CheckIfFetchRedpkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgCheckIfFetchRedpkg, "/Giftpkg.Giftpkg/CheckIfFetchRedpkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetApplyingGuildList(ctx context.Context, uin uint32, in *GetApplyingGuildListReq, opts ...svrkit.CallOption) (*GetApplyingGuildListResp, error) {
	out := new(GetApplyingGuildListResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetApplyingGuildList, "/Giftpkg.Giftpkg/GetApplyingGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetApplyedGuildList(ctx context.Context, uin uint32, in *GetApplyedGuildListReq, opts ...svrkit.CallOption) (*GetApplyedGuildListResp, error) {
	out := new(GetApplyedGuildListResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetApplyedGuildList, "/Giftpkg.Giftpkg/GetApplyedGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetGuildGiftPkgStatInfo(ctx context.Context, uin uint32, in *GetGuildGiftPkgStatInfoReq, opts ...svrkit.CallOption) (*GetGuildGiftPkgStatInfoResp, error) {
	out := new(GetGuildGiftPkgStatInfoResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetGuildGiftPkgStatInfo, "/Giftpkg.Giftpkg/GetGuildGiftPkgStatInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) CountRestGiftPkgSerial(ctx context.Context, uin uint32, in *CountRestGiftPkgSerialReq, opts ...svrkit.CallOption) (*CountRestGiftPkgSerialResp, error) {
	out := new(CountRestGiftPkgSerialResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgCountRestGiftPkgSerial, "/Giftpkg.Giftpkg/CountRestGiftPkgSerial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetApply(ctx context.Context, uin uint32, in *GetApplyReq, opts ...svrkit.CallOption) (*GetApplyResp, error) {
	out := new(GetApplyResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetApply, "/Giftpkg.Giftpkg/GetApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetGiftpkgOpHistory(ctx context.Context, uin uint32, in *GetGiftpkgOpHistoryReq, opts ...svrkit.CallOption) (*GetGiftpkgOpHistoryResp, error) {
	out := new(GetGiftpkgOpHistoryResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetGiftpkgOpHistory, "/Giftpkg.Giftpkg/GetGiftpkgOpHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetDeviceLastFetch(ctx context.Context, uin uint32, in *GetDeviceLastFetchReq, opts ...svrkit.CallOption) (*GetDeviceLastFetchResp, error) {
	out := new(GetDeviceLastFetchResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetDeviceLastFetch, "/Giftpkg.Giftpkg/GetDeviceLastFetch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetOfficalLastOpTime(ctx context.Context, uin uint32, in *GetOfficalLastOpTimeReq, opts ...svrkit.CallOption) (*GetOfficalLastOpTimeResp, error) {
	out := new(GetOfficalLastOpTimeResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetOfficalLastOpTime, "/Giftpkg.Giftpkg/GetOfficalLastOpTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) TypeGiftPkgSerialToGuild(ctx context.Context, uin uint32, in *TypeGiftPkgSerialToGuildReq, opts ...svrkit.CallOption) (*TypeGiftPkgSerialToGuildResp, error) {
	out := new(TypeGiftPkgSerialToGuildResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgTypeGiftPkgSerialToGuild, "/Giftpkg.Giftpkg/TypeGiftPkgSerialToGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) BatchGetGuildPkgStatus(ctx context.Context, uin uint32, in *BatchGetGuildPkgStatusReq, opts ...svrkit.CallOption) (*BatchGetGuildPkgStatusResp, error) {
	out := new(BatchGetGuildPkgStatusResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgBatchGetGuildPkgStatus, "/Giftpkg.Giftpkg/BatchGetGuildPkgStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) DeleteGuildSourceGiftPkg(ctx context.Context, uin uint32, in *DeleteGuildSourceGiftPkgReq, opts ...svrkit.CallOption) (*DeleteGuildSourceGiftPkgResp, error) {
	out := new(DeleteGuildSourceGiftPkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgDeleteGuildSourceGiftPkg, "/Giftpkg.Giftpkg/DeleteGuildSourceGiftPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) LockSerialForUserToPay(ctx context.Context, uin uint32, in *LockSerialForUserToPayReq, opts ...svrkit.CallOption) (*LockSerialForUserToPayResp, error) {
	out := new(LockSerialForUserToPayResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgLockSerialForUserToPay, "/Giftpkg.Giftpkg/LockSerialForUserToPay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) RealFetchSerialForUser(ctx context.Context, uin uint32, in *RealFetchSerialForUserReq, opts ...svrkit.CallOption) (*RealFetchSerialForUserResp, error) {
	out := new(RealFetchSerialForUserResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgRealFetchSerialForUser, "/Giftpkg.Giftpkg/RealFetchSerialForUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) ReleaseSerialForGuild(ctx context.Context, uin uint32, in *ReleaseSerialForGuildReq, opts ...svrkit.CallOption) (*ReleaseSerialForGuildResp, error) {
	out := new(ReleaseSerialForGuildResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgReleaseSerialForGuild, "/Giftpkg.Giftpkg/ReleaseSerialForGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) SetGuildPkgRedDiamondPrice(ctx context.Context, uin uint32, in *SetGuildPkgRedDiamondPriceReq, opts ...svrkit.CallOption) (*SetGuildPkgRedDiamondPriceResp, error) {
	out := new(SetGuildPkgRedDiamondPriceResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgSetGuildPkgRedDiamondPrice, "/Giftpkg.Giftpkg/SetGuildPkgRedDiamondPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetUserPkgSerialListNewById(ctx context.Context, uin uint32, in *GetUserPkgSerialListNewByIdReq, opts ...svrkit.CallOption) (*GetUserPkgSerialListNewByIdResp, error) {
	out := new(GetUserPkgSerialListNewByIdResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetUserPkgSerialListNewById, "/Giftpkg.Giftpkg/GetUserPkgSerialListNewById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetGuildPkgListByGameId(ctx context.Context, uin uint32, in *GetGuildPkgListByGameIdReq, opts ...svrkit.CallOption) (*GetGuildPkgListByGameIdResp, error) {
	out := new(GetGuildPkgListByGameIdResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetGuildPkgListByGameId, "/Giftpkg.Giftpkg/GetGuildPkgListByGameId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) BatchGetGiftPackageStatInfo(ctx context.Context, uin uint32, in *BatchGetGiftPackageStatInfoReq, opts ...svrkit.CallOption) (*BatchGetGiftPackageStatInfoResp, error) {
	out := new(BatchGetGiftPackageStatInfoResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgBatchGetGiftPackageStatInfo, "/Giftpkg.Giftpkg/BatchGetGiftPackageStatInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) UserFetchActivityGiftPkg(ctx context.Context, uin uint32, in *UserFetchActivityGiftPkgReq, opts ...svrkit.CallOption) (*UserFetchActivityGiftPkgResp, error) {
	out := new(UserFetchActivityGiftPkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgUserFetchActivityGiftPkg, "/Giftpkg.Giftpkg/UserFetchActivityGiftPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) CheckActivityGiftPkgFetchStatus(ctx context.Context, uin uint32, in *CheckActivityGiftPkgFetchStatusReq, opts ...svrkit.CallOption) (*CheckActivityGiftPkgFetchStatusResp, error) {
	out := new(CheckActivityGiftPkgFetchStatusResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgCheckActivityGiftPkgFetchStatus, "/Giftpkg.Giftpkg/CheckActivityGiftPkgFetchStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) FetchActGiftPkgCollection(ctx context.Context, uin uint32, in *FetchActGiftPkgCollectionReq, opts ...svrkit.CallOption) (*FetchActGiftPkgCollectionResp, error) {
	out := new(FetchActGiftPkgCollectionResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgFetchActGiftPkgCollection, "/Giftpkg.Giftpkg/FetchActGiftPkgCollection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetActGiftCollcetionFetStatus(ctx context.Context, uin uint32, in *GetActGiftCollcetionFetStatusReq, opts ...svrkit.CallOption) (*GetActGiftCollcetionFetStatusResp, error) {
	out := new(GetActGiftCollcetionFetStatusResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetActGiftCollcetionFetStatus, "/Giftpkg.Giftpkg/GetActGiftCollcetionFetStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) GetActGiftPkgCollectionConf(ctx context.Context, uin uint32, in *GetActGiftPkgCollectionConfReq, opts ...svrkit.CallOption) (*GetActGiftPkgCollectionConfResp, error) {
	out := new(GetActGiftPkgCollectionConfResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgGetActGiftPkgCollectionConf, "/Giftpkg.Giftpkg/GetActGiftPkgCollectionConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) UserFetchActivityGiftPkgNoLimit(ctx context.Context, uin uint32, in *UserFetchActivityGiftPkgReq, opts ...svrkit.CallOption) (*UserFetchActivityGiftPkgResp, error) {
	out := new(UserFetchActivityGiftPkgResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgUserFetchActivityGiftPkgNoLimit, "/Giftpkg.Giftpkg/UserFetchActivityGiftPkgNoLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *GiftpkgClient) FetchActGiftPkgCollectionNoLimit(ctx context.Context, uin uint32, in *FetchActGiftPkgCollectionReq, opts ...svrkit.CallOption) (*FetchActGiftPkgCollectionResp, error) {
	out := new(FetchActGiftPkgCollectionResp)
	err := c.cc.Invoke(ctx, uin, commandGiftpkgFetchActGiftPkgCollectionNoLimit, "/Giftpkg.Giftpkg/FetchActGiftPkgCollectionNoLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
