// Code generated by protoc-gen-gogo.
// source: src/guildcirclesvr/guildcircle.proto
// DO NOT EDIT!

/*
	Package guildcircle is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/guildcirclesvr/guildcircle.proto

	It has these top-level messages:
		CircleBaseInfo
		GuildTopicBase
		GuildTopicBrief
		GuildTopicDetail
		CommentBase
		GuildCommentWithRef
		CircleImageAttr
		CreateGuildCircleReq
		CreateTopicReq
		CreateTopicResp
		DeleteTopicReq
		DeleteTopicResp
		CreateTopicCommentReq
		CreateTopicCommentResp
		DeleteTopicCommentReq
		DeleteTopicCommentResp
		LikeReq
		LikeResp
		CancelLikeReq
		CancelLikeResp
		GetTopicsReq
		GetTopicsResp
		GetTopicDetailReq
		GetTopicDetailResp
		GetTopicLikersReq
		GetTopicLikersResp
		GetTopicReferencedCommentCreatorsReq
		GetTopicReferencedCommentCreatorsResp
		CheckLikeResp
		ReportTopicReq
		ReportTopicResp
		GetDeletedTopicIdListReq
		GetDeletedTopicIdListResp
		GetCircleReq
		AddTopicTagReq
		AddTopicTagResp
		CheckCircleTopicsLikeReq
		CheckCircleTopicsLikeResp
		GetTopicLikedUsersReq
		GetTopicLikedUsersResp
		GetTopicCommentsReq
		GetTopicCommentsResp
		GuildCircleSimpleCommentInfo
		GuildCircleCommentWithSimpleRefInfo
		GuildCircleCommentWithReply
		GetCommentsReq
		GetCommentsResp
		GetCommentListReq
		GetCommentListResp
		GetCommentReplyListReq
		GetCommentReplyListResp
		GetTopicCountReq
		GetTopicCountResp
*/
package guildcircle

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type CIRCLE_TOPIC_TAG int32

const (
	CIRCLE_TOPIC_TAG_HIGHLIGHT         CIRCLE_TOPIC_TAG = 1
	CIRCLE_TOPIC_TAG_TOP               CIRCLE_TOPIC_TAG = 2
	CIRCLE_TOPIC_TAG_ACTIVITY          CIRCLE_TOPIC_TAG = 4
	CIRCLE_TOPIC_TAG_GAME_DOWNLOAD     CIRCLE_TOPIC_TAG = 8
	CIRCLE_TOPIC_TAG_SHIELD_ON_ANDROID CIRCLE_TOPIC_TAG = 16
	CIRCLE_TOPIC_TAG_SHIELD_ON_IOS     CIRCLE_TOPIC_TAG = 32
)

var CIRCLE_TOPIC_TAG_name = map[int32]string{
	1:  "HIGHLIGHT",
	2:  "TOP",
	4:  "ACTIVITY",
	8:  "GAME_DOWNLOAD",
	16: "SHIELD_ON_ANDROID",
	32: "SHIELD_ON_IOS",
}
var CIRCLE_TOPIC_TAG_value = map[string]int32{
	"HIGHLIGHT":         1,
	"TOP":               2,
	"ACTIVITY":          4,
	"GAME_DOWNLOAD":     8,
	"SHIELD_ON_ANDROID": 16,
	"SHIELD_ON_IOS":     32,
}

func (x CIRCLE_TOPIC_TAG) Enum() *CIRCLE_TOPIC_TAG {
	p := new(CIRCLE_TOPIC_TAG)
	*p = x
	return p
}
func (x CIRCLE_TOPIC_TAG) String() string {
	return proto.EnumName(CIRCLE_TOPIC_TAG_name, int32(x))
}
func (x *CIRCLE_TOPIC_TAG) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CIRCLE_TOPIC_TAG_value, data, "CIRCLE_TOPIC_TAG")
	if err != nil {
		return err
	}
	*x = CIRCLE_TOPIC_TAG(value)
	return nil
}
func (CIRCLE_TOPIC_TAG) EnumDescriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{0} }

type GuildTopicBase_TOPIC_STATUS int32

const (
	GuildTopicBase_NORMAL  GuildTopicBase_TOPIC_STATUS = 0
	GuildTopicBase_DELETED GuildTopicBase_TOPIC_STATUS = 1
)

var GuildTopicBase_TOPIC_STATUS_name = map[int32]string{
	0: "NORMAL",
	1: "DELETED",
}
var GuildTopicBase_TOPIC_STATUS_value = map[string]int32{
	"NORMAL":  0,
	"DELETED": 1,
}

func (x GuildTopicBase_TOPIC_STATUS) Enum() *GuildTopicBase_TOPIC_STATUS {
	p := new(GuildTopicBase_TOPIC_STATUS)
	*p = x
	return p
}
func (x GuildTopicBase_TOPIC_STATUS) String() string {
	return proto.EnumName(GuildTopicBase_TOPIC_STATUS_name, int32(x))
}
func (x *GuildTopicBase_TOPIC_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GuildTopicBase_TOPIC_STATUS_value, data, "GuildTopicBase_TOPIC_STATUS")
	if err != nil {
		return err
	}
	*x = GuildTopicBase_TOPIC_STATUS(value)
	return nil
}
func (GuildTopicBase_TOPIC_STATUS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{1, 0}
}

type CommentBase_COMMENT_STATUS int32

const (
	CommentBase_NORMAL  CommentBase_COMMENT_STATUS = 0
	CommentBase_DELETED CommentBase_COMMENT_STATUS = 1
)

var CommentBase_COMMENT_STATUS_name = map[int32]string{
	0: "NORMAL",
	1: "DELETED",
}
var CommentBase_COMMENT_STATUS_value = map[string]int32{
	"NORMAL":  0,
	"DELETED": 1,
}

func (x CommentBase_COMMENT_STATUS) Enum() *CommentBase_COMMENT_STATUS {
	p := new(CommentBase_COMMENT_STATUS)
	*p = x
	return p
}
func (x CommentBase_COMMENT_STATUS) String() string {
	return proto.EnumName(CommentBase_COMMENT_STATUS_name, int32(x))
}
func (x *CommentBase_COMMENT_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CommentBase_COMMENT_STATUS_value, data, "CommentBase_COMMENT_STATUS")
	if err != nil {
		return err
	}
	*x = CommentBase_COMMENT_STATUS(value)
	return nil
}
func (CommentBase_COMMENT_STATUS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{4, 0}
}

// *
// 	公会圈子
type CircleBaseInfo struct {
	GuildId         uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TodayTopicCount uint32 `protobuf:"varint,2,req,name=today_topic_count,json=todayTopicCount" json:"today_topic_count"`
	TopicCount      uint32 `protobuf:"varint,3,opt,name=topic_count,json=topicCount" json:"topic_count"`
}

func (m *CircleBaseInfo) Reset()                    { *m = CircleBaseInfo{} }
func (m *CircleBaseInfo) String() string            { return proto.CompactTextString(m) }
func (*CircleBaseInfo) ProtoMessage()               {}
func (*CircleBaseInfo) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{0} }

func (m *CircleBaseInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CircleBaseInfo) GetTodayTopicCount() uint32 {
	if m != nil {
		return m.TodayTopicCount
	}
	return 0
}

func (m *CircleBaseInfo) GetTopicCount() uint32 {
	if m != nil {
		return m.TopicCount
	}
	return 0
}

// 主题基本信息
type GuildTopicBase struct {
	TopicId       uint32   `protobuf:"varint,1,req,name=topic_id,json=topicId" json:"topic_id"`
	GuildId       uint32   `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	Title         string   `protobuf:"bytes,3,req,name=title" json:"title"`
	Creator       uint32   `protobuf:"varint,4,req,name=creator" json:"creator"`
	CreateTime    uint32   `protobuf:"varint,5,req,name=create_time,json=createTime" json:"create_time"`
	LikeCount     uint32   `protobuf:"varint,6,req,name=like_count,json=likeCount" json:"like_count"`
	CommentCount  uint32   `protobuf:"varint,7,req,name=comment_count,json=commentCount" json:"comment_count"`
	MsgSeqId      uint32   `protobuf:"varint,8,req,name=msg_seq_id,json=msgSeqId" json:"msg_seq_id"`
	Status        uint32   `protobuf:"varint,9,req,name=status" json:"status"`
	ImgList       []string `protobuf:"bytes,10,rep,name=img_list,json=imgList" json:"img_list,omitempty"`
	ReportCount   uint32   `protobuf:"varint,11,opt,name=report_count,json=reportCount" json:"report_count"`
	OfficalReport uint32   `protobuf:"varint,12,opt,name=offical_report,json=officalReport" json:"offical_report"`
	Tag           uint32   `protobuf:"varint,13,opt,name=tag" json:"tag"`
	LastTime      uint32   `protobuf:"varint,14,opt,name=last_time,json=lastTime" json:"last_time"`
}

func (m *GuildTopicBase) Reset()                    { *m = GuildTopicBase{} }
func (m *GuildTopicBase) String() string            { return proto.CompactTextString(m) }
func (*GuildTopicBase) ProtoMessage()               {}
func (*GuildTopicBase) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{1} }

func (m *GuildTopicBase) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildTopicBase) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildTopicBase) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GuildTopicBase) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *GuildTopicBase) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GuildTopicBase) GetLikeCount() uint32 {
	if m != nil {
		return m.LikeCount
	}
	return 0
}

func (m *GuildTopicBase) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *GuildTopicBase) GetMsgSeqId() uint32 {
	if m != nil {
		return m.MsgSeqId
	}
	return 0
}

func (m *GuildTopicBase) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GuildTopicBase) GetImgList() []string {
	if m != nil {
		return m.ImgList
	}
	return nil
}

func (m *GuildTopicBase) GetReportCount() uint32 {
	if m != nil {
		return m.ReportCount
	}
	return 0
}

func (m *GuildTopicBase) GetOfficalReport() uint32 {
	if m != nil {
		return m.OfficalReport
	}
	return 0
}

func (m *GuildTopicBase) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *GuildTopicBase) GetLastTime() uint32 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

// 主题简要信息
type GuildTopicBrief struct {
	Base           *GuildTopicBase `protobuf:"bytes,1,req,name=base" json:"base,omitempty"`
	ContentPreview string          `protobuf:"bytes,2,req,name=content_preview,json=contentPreview" json:"content_preview"`
}

func (m *GuildTopicBrief) Reset()                    { *m = GuildTopicBrief{} }
func (m *GuildTopicBrief) String() string            { return proto.CompactTextString(m) }
func (*GuildTopicBrief) ProtoMessage()               {}
func (*GuildTopicBrief) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{2} }

func (m *GuildTopicBrief) GetBase() *GuildTopicBase {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *GuildTopicBrief) GetContentPreview() string {
	if m != nil {
		return m.ContentPreview
	}
	return ""
}

// 主题详细信息
type GuildTopicDetail struct {
	Base    *GuildTopicBase `protobuf:"bytes,1,req,name=base" json:"base,omitempty"`
	Content string          `protobuf:"bytes,2,req,name=content" json:"content"`
}

func (m *GuildTopicDetail) Reset()                    { *m = GuildTopicDetail{} }
func (m *GuildTopicDetail) String() string            { return proto.CompactTextString(m) }
func (*GuildTopicDetail) ProtoMessage()               {}
func (*GuildTopicDetail) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{3} }

func (m *GuildTopicDetail) GetBase() *GuildTopicBase {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *GuildTopicDetail) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// *
// 	评论
type CommentBase struct {
	CommentId       uint32   `protobuf:"varint,1,req,name=comment_id,json=commentId" json:"comment_id"`
	GuildId         uint32   `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId         uint32   `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Content         string   `protobuf:"bytes,4,req,name=content" json:"content"`
	Creator         uint32   `protobuf:"varint,5,req,name=creator" json:"creator"`
	CreateTime      uint32   `protobuf:"varint,6,req,name=create_time,json=createTime" json:"create_time"`
	Status          uint32   `protobuf:"varint,7,req,name=status" json:"status"`
	MsgSeqId        uint32   `protobuf:"varint,8,req,name=msg_seq_id,json=msgSeqId" json:"msg_seq_id"`
	RefCommentId    uint32   `protobuf:"varint,9,req,name=ref_comment_id,json=refCommentId" json:"ref_comment_id"`
	LikeCount       uint32   `protobuf:"varint,10,req,name=like_count,json=likeCount" json:"like_count"`
	ReportCount     uint32   `protobuf:"varint,11,opt,name=report_count,json=reportCount" json:"report_count"`
	OfficalReport   uint32   `protobuf:"varint,12,opt,name=offical_report,json=officalReport" json:"offical_report"`
	ImgList         []string `protobuf:"bytes,13,rep,name=img_list,json=imgList" json:"img_list,omitempty"`
	Floor           int32    `protobuf:"varint,14,opt,name=floor" json:"floor"`
	ParentCommentId uint32   `protobuf:"varint,15,opt,name=parent_comment_id,json=parentCommentId" json:"parent_comment_id"`
	CommentType     uint32   `protobuf:"varint,16,opt,name=comment_type,json=commentType" json:"comment_type"`
}

func (m *CommentBase) Reset()                    { *m = CommentBase{} }
func (m *CommentBase) String() string            { return proto.CompactTextString(m) }
func (*CommentBase) ProtoMessage()               {}
func (*CommentBase) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{4} }

func (m *CommentBase) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *CommentBase) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CommentBase) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CommentBase) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CommentBase) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *CommentBase) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *CommentBase) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CommentBase) GetMsgSeqId() uint32 {
	if m != nil {
		return m.MsgSeqId
	}
	return 0
}

func (m *CommentBase) GetRefCommentId() uint32 {
	if m != nil {
		return m.RefCommentId
	}
	return 0
}

func (m *CommentBase) GetLikeCount() uint32 {
	if m != nil {
		return m.LikeCount
	}
	return 0
}

func (m *CommentBase) GetReportCount() uint32 {
	if m != nil {
		return m.ReportCount
	}
	return 0
}

func (m *CommentBase) GetOfficalReport() uint32 {
	if m != nil {
		return m.OfficalReport
	}
	return 0
}

func (m *CommentBase) GetImgList() []string {
	if m != nil {
		return m.ImgList
	}
	return nil
}

func (m *CommentBase) GetFloor() int32 {
	if m != nil {
		return m.Floor
	}
	return 0
}

func (m *CommentBase) GetParentCommentId() uint32 {
	if m != nil {
		return m.ParentCommentId
	}
	return 0
}

func (m *CommentBase) GetCommentType() uint32 {
	if m != nil {
		return m.CommentType
	}
	return 0
}

type GuildCommentWithRef struct {
	Comment    *CommentBase `protobuf:"bytes,1,req,name=comment" json:"comment,omitempty"`
	RefComment *CommentBase `protobuf:"bytes,2,opt,name=ref_comment,json=refComment" json:"ref_comment,omitempty"`
}

func (m *GuildCommentWithRef) Reset()                    { *m = GuildCommentWithRef{} }
func (m *GuildCommentWithRef) String() string            { return proto.CompactTextString(m) }
func (*GuildCommentWithRef) ProtoMessage()               {}
func (*GuildCommentWithRef) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{5} }

func (m *GuildCommentWithRef) GetComment() *CommentBase {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *GuildCommentWithRef) GetRefComment() *CommentBase {
	if m != nil {
		return m.RefComment
	}
	return nil
}

type CircleImageAttr struct {
	Key         string `protobuf:"bytes,1,req,name=key" json:"key"`
	Width       uint32 `protobuf:"varint,2,req,name=width" json:"width"`
	Height      uint32 `protobuf:"varint,3,req,name=height" json:"height"`
	Format      string `protobuf:"bytes,4,opt,name=format" json:"format"`
	ColorModel  string `protobuf:"bytes,5,opt,name=color_model,json=colorModel" json:"color_model"`
	FrameNumber uint32 `protobuf:"varint,6,opt,name=frame_number,json=frameNumber" json:"frame_number"`
}

func (m *CircleImageAttr) Reset()                    { *m = CircleImageAttr{} }
func (m *CircleImageAttr) String() string            { return proto.CompactTextString(m) }
func (*CircleImageAttr) ProtoMessage()               {}
func (*CircleImageAttr) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{6} }

func (m *CircleImageAttr) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CircleImageAttr) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *CircleImageAttr) GetHeight() uint32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *CircleImageAttr) GetFormat() string {
	if m != nil {
		return m.Format
	}
	return ""
}

func (m *CircleImageAttr) GetColorModel() string {
	if m != nil {
		return m.ColorModel
	}
	return ""
}

func (m *CircleImageAttr) GetFrameNumber() uint32 {
	if m != nil {
		return m.FrameNumber
	}
	return 0
}

// 创建一个公会圈子
type CreateGuildCircleReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *CreateGuildCircleReq) Reset()                    { *m = CreateGuildCircleReq{} }
func (m *CreateGuildCircleReq) String() string            { return proto.CompactTextString(m) }
func (*CreateGuildCircleReq) ProtoMessage()               {}
func (*CreateGuildCircleReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{7} }

func (m *CreateGuildCircleReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

// 发表主题
type CreateTopicReq struct {
	GuildId   uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Title     string   `protobuf:"bytes,2,req,name=title" json:"title"`
	Content   string   `protobuf:"bytes,3,req,name=content" json:"content"`
	MsgSeqId  uint32   `protobuf:"varint,4,req,name=msg_seq_id,json=msgSeqId" json:"msg_seq_id"`
	ImgList   []string `protobuf:"bytes,5,rep,name=img_list,json=imgList" json:"img_list,omitempty"`
	Highlight bool     `protobuf:"varint,6,opt,name=highlight" json:"highlight"`
}

func (m *CreateTopicReq) Reset()                    { *m = CreateTopicReq{} }
func (m *CreateTopicReq) String() string            { return proto.CompactTextString(m) }
func (*CreateTopicReq) ProtoMessage()               {}
func (*CreateTopicReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{8} }

func (m *CreateTopicReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CreateTopicReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CreateTopicReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CreateTopicReq) GetMsgSeqId() uint32 {
	if m != nil {
		return m.MsgSeqId
	}
	return 0
}

func (m *CreateTopicReq) GetImgList() []string {
	if m != nil {
		return m.ImgList
	}
	return nil
}

func (m *CreateTopicReq) GetHighlight() bool {
	if m != nil {
		return m.Highlight
	}
	return false
}

type CreateTopicResp struct {
	TopicId        uint32 `protobuf:"varint,1,req,name=topic_id,json=topicId" json:"topic_id"`
	RemainCooldown uint32 `protobuf:"varint,2,opt,name=remain_cooldown,json=remainCooldown" json:"remain_cooldown"`
}

func (m *CreateTopicResp) Reset()                    { *m = CreateTopicResp{} }
func (m *CreateTopicResp) String() string            { return proto.CompactTextString(m) }
func (*CreateTopicResp) ProtoMessage()               {}
func (*CreateTopicResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{9} }

func (m *CreateTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CreateTopicResp) GetRemainCooldown() uint32 {
	if m != nil {
		return m.RemainCooldown
	}
	return 0
}

// 删除主题
type DeleteTopicReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *DeleteTopicReq) Reset()                    { *m = DeleteTopicReq{} }
func (m *DeleteTopicReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteTopicReq) ProtoMessage()               {}
func (*DeleteTopicReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{10} }

func (m *DeleteTopicReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DeleteTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type DeleteTopicResp struct {
}

func (m *DeleteTopicResp) Reset()                    { *m = DeleteTopicResp{} }
func (m *DeleteTopicResp) String() string            { return proto.CompactTextString(m) }
func (*DeleteTopicResp) ProtoMessage()               {}
func (*DeleteTopicResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{11} }

// 发表评论
type CreateTopicCommentReq struct {
	GuildId      uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId      uint32   `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	Content      string   `protobuf:"bytes,3,req,name=content" json:"content"`
	MsgSeqId     uint32   `protobuf:"varint,4,req,name=msg_seq_id,json=msgSeqId" json:"msg_seq_id"`
	RefCommentId uint32   `protobuf:"varint,5,opt,name=ref_comment_id,json=refCommentId" json:"ref_comment_id"`
	UserType     uint32   `protobuf:"varint,6,opt,name=user_type,json=userType" json:"user_type"`
	UserName     string   `protobuf:"bytes,7,opt,name=user_name,json=userName" json:"user_name"`
	ImgList      []string `protobuf:"bytes,8,rep,name=img_list,json=imgList" json:"img_list,omitempty"`
	AntiAdFlag   uint32   `protobuf:"varint,9,opt,name=anti_ad_flag,json=antiAdFlag" json:"anti_ad_flag"`
}

func (m *CreateTopicCommentReq) Reset()         { *m = CreateTopicCommentReq{} }
func (m *CreateTopicCommentReq) String() string { return proto.CompactTextString(m) }
func (*CreateTopicCommentReq) ProtoMessage()    {}
func (*CreateTopicCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{12}
}

func (m *CreateTopicCommentReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CreateTopicCommentReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CreateTopicCommentReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CreateTopicCommentReq) GetMsgSeqId() uint32 {
	if m != nil {
		return m.MsgSeqId
	}
	return 0
}

func (m *CreateTopicCommentReq) GetRefCommentId() uint32 {
	if m != nil {
		return m.RefCommentId
	}
	return 0
}

func (m *CreateTopicCommentReq) GetUserType() uint32 {
	if m != nil {
		return m.UserType
	}
	return 0
}

func (m *CreateTopicCommentReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *CreateTopicCommentReq) GetImgList() []string {
	if m != nil {
		return m.ImgList
	}
	return nil
}

func (m *CreateTopicCommentReq) GetAntiAdFlag() uint32 {
	if m != nil {
		return m.AntiAdFlag
	}
	return 0
}

type CreateTopicCommentResp struct {
	CommentId      uint32               `protobuf:"varint,1,req,name=comment_id,json=commentId" json:"comment_id"`
	RemainCooldown uint32               `protobuf:"varint,2,opt,name=remain_cooldown,json=remainCooldown" json:"remain_cooldown"`
	Comment        *GuildCommentWithRef `protobuf:"bytes,3,opt,name=comment" json:"comment,omitempty"`
}

func (m *CreateTopicCommentResp) Reset()         { *m = CreateTopicCommentResp{} }
func (m *CreateTopicCommentResp) String() string { return proto.CompactTextString(m) }
func (*CreateTopicCommentResp) ProtoMessage()    {}
func (*CreateTopicCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{13}
}

func (m *CreateTopicCommentResp) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *CreateTopicCommentResp) GetRemainCooldown() uint32 {
	if m != nil {
		return m.RemainCooldown
	}
	return 0
}

func (m *CreateTopicCommentResp) GetComment() *GuildCommentWithRef {
	if m != nil {
		return m.Comment
	}
	return nil
}

// 删除评论
type DeleteTopicCommentReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId   uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentId uint32 `protobuf:"varint,3,req,name=comment_id,json=commentId" json:"comment_id"`
}

func (m *DeleteTopicCommentReq) Reset()         { *m = DeleteTopicCommentReq{} }
func (m *DeleteTopicCommentReq) String() string { return proto.CompactTextString(m) }
func (*DeleteTopicCommentReq) ProtoMessage()    {}
func (*DeleteTopicCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{14}
}

func (m *DeleteTopicCommentReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DeleteTopicCommentReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *DeleteTopicCommentReq) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

type DeleteTopicCommentResp struct {
}

func (m *DeleteTopicCommentResp) Reset()         { *m = DeleteTopicCommentResp{} }
func (m *DeleteTopicCommentResp) String() string { return proto.CompactTextString(m) }
func (*DeleteTopicCommentResp) ProtoMessage()    {}
func (*DeleteTopicCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{15}
}

// 点赞
type LikeReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId   uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentId uint32 `protobuf:"varint,3,opt,name=comment_id,json=commentId" json:"comment_id"`
}

func (m *LikeReq) Reset()                    { *m = LikeReq{} }
func (m *LikeReq) String() string            { return proto.CompactTextString(m) }
func (*LikeReq) ProtoMessage()               {}
func (*LikeReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{16} }

func (m *LikeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *LikeReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *LikeReq) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

type LikeResp struct {
}

func (m *LikeResp) Reset()                    { *m = LikeResp{} }
func (m *LikeResp) String() string            { return proto.CompactTextString(m) }
func (*LikeResp) ProtoMessage()               {}
func (*LikeResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{17} }

// 取消赞
type CancelLikeReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId   uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentId uint32 `protobuf:"varint,3,opt,name=comment_id,json=commentId" json:"comment_id"`
}

func (m *CancelLikeReq) Reset()                    { *m = CancelLikeReq{} }
func (m *CancelLikeReq) String() string            { return proto.CompactTextString(m) }
func (*CancelLikeReq) ProtoMessage()               {}
func (*CancelLikeReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{18} }

func (m *CancelLikeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CancelLikeReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CancelLikeReq) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

type CancelLikeResp struct {
}

func (m *CancelLikeResp) Reset()                    { *m = CancelLikeResp{} }
func (m *CancelLikeResp) String() string            { return proto.CompactTextString(m) }
func (*CancelLikeResp) ProtoMessage()               {}
func (*CancelLikeResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{19} }

// 获取主题列表
type GetTopicsReq struct {
	GuildId      uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	StartTopicId uint32 `protobuf:"varint,2,req,name=start_topic_id,json=startTopicId" json:"start_topic_id"`
	Count        uint32 `protobuf:"varint,3,req,name=count" json:"count"`
	Tag          uint32 `protobuf:"varint,4,opt,name=tag" json:"tag"`
	Sorttype     uint32 `protobuf:"varint,5,opt,name=sorttype" json:"sorttype"`
	Userfrom     uint32 `protobuf:"varint,6,opt,name=userfrom" json:"userfrom"`
	ClientType   uint32 `protobuf:"varint,7,opt,name=client_type,json=clientType" json:"client_type"`
}

func (m *GetTopicsReq) Reset()                    { *m = GetTopicsReq{} }
func (m *GetTopicsReq) String() string            { return proto.CompactTextString(m) }
func (*GetTopicsReq) ProtoMessage()               {}
func (*GetTopicsReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{20} }

func (m *GetTopicsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetTopicsReq) GetStartTopicId() uint32 {
	if m != nil {
		return m.StartTopicId
	}
	return 0
}

func (m *GetTopicsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetTopicsReq) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *GetTopicsReq) GetSorttype() uint32 {
	if m != nil {
		return m.Sorttype
	}
	return 0
}

func (m *GetTopicsReq) GetUserfrom() uint32 {
	if m != nil {
		return m.Userfrom
	}
	return 0
}

func (m *GetTopicsReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type GetTopicsResp struct {
	TopicBriefList []*GuildTopicBrief `protobuf:"bytes,1,rep,name=topic_brief_list,json=topicBriefList" json:"topic_brief_list,omitempty"`
	ImageAttrList  []*CircleImageAttr `protobuf:"bytes,2,rep,name=image_attr_list,json=imageAttrList" json:"image_attr_list,omitempty"`
}

func (m *GetTopicsResp) Reset()                    { *m = GetTopicsResp{} }
func (m *GetTopicsResp) String() string            { return proto.CompactTextString(m) }
func (*GetTopicsResp) ProtoMessage()               {}
func (*GetTopicsResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{21} }

func (m *GetTopicsResp) GetTopicBriefList() []*GuildTopicBrief {
	if m != nil {
		return m.TopicBriefList
	}
	return nil
}

func (m *GetTopicsResp) GetImageAttrList() []*CircleImageAttr {
	if m != nil {
		return m.ImageAttrList
	}
	return nil
}

// 查询主题详情
type GetTopicDetailReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *GetTopicDetailReq) Reset()                    { *m = GetTopicDetailReq{} }
func (m *GetTopicDetailReq) String() string            { return proto.CompactTextString(m) }
func (*GetTopicDetailReq) ProtoMessage()               {}
func (*GetTopicDetailReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{22} }

func (m *GetTopicDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetTopicDetailReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type GetTopicDetailResp struct {
	TopicDetail   *GuildTopicDetail  `protobuf:"bytes,1,req,name=topic_detail,json=topicDetail" json:"topic_detail,omitempty"`
	ImageAttrList []*CircleImageAttr `protobuf:"bytes,2,rep,name=image_attr_list,json=imageAttrList" json:"image_attr_list,omitempty"`
}

func (m *GetTopicDetailResp) Reset()                    { *m = GetTopicDetailResp{} }
func (m *GetTopicDetailResp) String() string            { return proto.CompactTextString(m) }
func (*GetTopicDetailResp) ProtoMessage()               {}
func (*GetTopicDetailResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{23} }

func (m *GetTopicDetailResp) GetTopicDetail() *GuildTopicDetail {
	if m != nil {
		return m.TopicDetail
	}
	return nil
}

func (m *GetTopicDetailResp) GetImageAttrList() []*CircleImageAttr {
	if m != nil {
		return m.ImageAttrList
	}
	return nil
}

// 获取主题的点赞列表
type GetTopicLikersReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	Offset  uint32 `protobuf:"varint,3,req,name=offset" json:"offset"`
	Limit   uint32 `protobuf:"varint,4,req,name=limit" json:"limit"`
}

func (m *GetTopicLikersReq) Reset()                    { *m = GetTopicLikersReq{} }
func (m *GetTopicLikersReq) String() string            { return proto.CompactTextString(m) }
func (*GetTopicLikersReq) ProtoMessage()               {}
func (*GetTopicLikersReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{24} }

func (m *GetTopicLikersReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetTopicLikersReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GetTopicLikersReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetTopicLikersReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetTopicLikersResp struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *GetTopicLikersResp) Reset()                    { *m = GetTopicLikersResp{} }
func (m *GetTopicLikersResp) String() string            { return proto.CompactTextString(m) }
func (*GetTopicLikersResp) ProtoMessage()               {}
func (*GetTopicLikersResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{25} }

func (m *GetTopicLikersResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 获取某主题中所有被引用评论的作者列表
type GetTopicReferencedCommentCreatorsReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *GetTopicReferencedCommentCreatorsReq) Reset()         { *m = GetTopicReferencedCommentCreatorsReq{} }
func (m *GetTopicReferencedCommentCreatorsReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicReferencedCommentCreatorsReq) ProtoMessage()    {}
func (*GetTopicReferencedCommentCreatorsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{26}
}

func (m *GetTopicReferencedCommentCreatorsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetTopicReferencedCommentCreatorsReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type GetTopicReferencedCommentCreatorsResp struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *GetTopicReferencedCommentCreatorsResp) Reset()         { *m = GetTopicReferencedCommentCreatorsResp{} }
func (m *GetTopicReferencedCommentCreatorsResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicReferencedCommentCreatorsResp) ProtoMessage()    {}
func (*GetTopicReferencedCommentCreatorsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{27}
}

func (m *GetTopicReferencedCommentCreatorsResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type CheckLikeResp struct {
	LikeStatus uint32 `protobuf:"varint,1,req,name=like_status,json=likeStatus" json:"like_status"`
}

func (m *CheckLikeResp) Reset()                    { *m = CheckLikeResp{} }
func (m *CheckLikeResp) String() string            { return proto.CompactTextString(m) }
func (*CheckLikeResp) ProtoMessage()               {}
func (*CheckLikeResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{28} }

func (m *CheckLikeResp) GetLikeStatus() uint32 {
	if m != nil {
		return m.LikeStatus
	}
	return 0
}

// 举报某条主题
type ReportTopicReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *ReportTopicReq) Reset()                    { *m = ReportTopicReq{} }
func (m *ReportTopicReq) String() string            { return proto.CompactTextString(m) }
func (*ReportTopicReq) ProtoMessage()               {}
func (*ReportTopicReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{29} }

func (m *ReportTopicReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ReportTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type ReportTopicResp struct {
}

func (m *ReportTopicResp) Reset()                    { *m = ReportTopicResp{} }
func (m *ReportTopicResp) String() string            { return proto.CompactTextString(m) }
func (*ReportTopicResp) ProtoMessage()               {}
func (*ReportTopicResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{30} }

// 获取一个圈子下, 指定区间中被删除的主题id列表
type GetDeletedTopicIdListReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	MinTopicId uint32 `protobuf:"varint,2,req,name=min_topic_id,json=minTopicId" json:"min_topic_id"`
	MaxTopicId uint32 `protobuf:"varint,3,req,name=max_topic_id,json=maxTopicId" json:"max_topic_id"`
}

func (m *GetDeletedTopicIdListReq) Reset()         { *m = GetDeletedTopicIdListReq{} }
func (m *GetDeletedTopicIdListReq) String() string { return proto.CompactTextString(m) }
func (*GetDeletedTopicIdListReq) ProtoMessage()    {}
func (*GetDeletedTopicIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{31}
}

func (m *GetDeletedTopicIdListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetDeletedTopicIdListReq) GetMinTopicId() uint32 {
	if m != nil {
		return m.MinTopicId
	}
	return 0
}

func (m *GetDeletedTopicIdListReq) GetMaxTopicId() uint32 {
	if m != nil {
		return m.MaxTopicId
	}
	return 0
}

type GetDeletedTopicIdListResp struct {
	DeletedTopicIdList []uint32 `protobuf:"varint,3,rep,name=deleted_topic_id_list,json=deletedTopicIdList" json:"deleted_topic_id_list,omitempty"`
}

func (m *GetDeletedTopicIdListResp) Reset()         { *m = GetDeletedTopicIdListResp{} }
func (m *GetDeletedTopicIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetDeletedTopicIdListResp) ProtoMessage()    {}
func (*GetDeletedTopicIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{32}
}

func (m *GetDeletedTopicIdListResp) GetDeletedTopicIdList() []uint32 {
	if m != nil {
		return m.DeletedTopicIdList
	}
	return nil
}

type GetCircleReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetCircleReq) Reset()                    { *m = GetCircleReq{} }
func (m *GetCircleReq) String() string            { return proto.CompactTextString(m) }
func (*GetCircleReq) ProtoMessage()               {}
func (*GetCircleReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{33} }

func (m *GetCircleReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type AddTopicTagReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	Tag     uint32 `protobuf:"varint,3,req,name=tag" json:"tag"`
	Add     bool   `protobuf:"varint,4,req,name=add" json:"add"`
}

func (m *AddTopicTagReq) Reset()                    { *m = AddTopicTagReq{} }
func (m *AddTopicTagReq) String() string            { return proto.CompactTextString(m) }
func (*AddTopicTagReq) ProtoMessage()               {}
func (*AddTopicTagReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{34} }

func (m *AddTopicTagReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddTopicTagReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *AddTopicTagReq) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *AddTopicTagReq) GetAdd() bool {
	if m != nil {
		return m.Add
	}
	return false
}

type AddTopicTagResp struct {
}

func (m *AddTopicTagResp) Reset()                    { *m = AddTopicTagResp{} }
func (m *AddTopicTagResp) String() string            { return proto.CompactTextString(m) }
func (*AddTopicTagResp) ProtoMessage()               {}
func (*AddTopicTagResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{35} }

// 用过户点赞过的topic_id
type CheckCircleTopicsLikeReq struct {
	GuildId     uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicIdList []uint32 `protobuf:"varint,2,rep,name=topic_id_list,json=topicIdList" json:"topic_id_list,omitempty"`
}

func (m *CheckCircleTopicsLikeReq) Reset()         { *m = CheckCircleTopicsLikeReq{} }
func (m *CheckCircleTopicsLikeReq) String() string { return proto.CompactTextString(m) }
func (*CheckCircleTopicsLikeReq) ProtoMessage()    {}
func (*CheckCircleTopicsLikeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{36}
}

func (m *CheckCircleTopicsLikeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CheckCircleTopicsLikeReq) GetTopicIdList() []uint32 {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

type CheckCircleTopicsLikeResp struct {
	TopicLikedList []uint32 `protobuf:"varint,1,rep,name=topic_liked_list,json=topicLikedList" json:"topic_liked_list,omitempty"`
}

func (m *CheckCircleTopicsLikeResp) Reset()         { *m = CheckCircleTopicsLikeResp{} }
func (m *CheckCircleTopicsLikeResp) String() string { return proto.CompactTextString(m) }
func (*CheckCircleTopicsLikeResp) ProtoMessage()    {}
func (*CheckCircleTopicsLikeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{37}
}

func (m *CheckCircleTopicsLikeResp) GetTopicLikedList() []uint32 {
	if m != nil {
		return m.TopicLikedList
	}
	return nil
}

type GetTopicLikedUsersReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	Start   uint32 `protobuf:"varint,3,req,name=start" json:"start"`
	Count   uint32 `protobuf:"varint,4,req,name=count" json:"count"`
}

func (m *GetTopicLikedUsersReq) Reset()         { *m = GetTopicLikedUsersReq{} }
func (m *GetTopicLikedUsersReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicLikedUsersReq) ProtoMessage()    {}
func (*GetTopicLikedUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{38}
}

func (m *GetTopicLikedUsersReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetTopicLikedUsersReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GetTopicLikedUsersReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetTopicLikedUsersReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetTopicLikedUsersResp struct {
	TopicLikedUserList []uint32 `protobuf:"varint,1,rep,name=topic_liked_user_list,json=topicLikedUserList" json:"topic_liked_user_list,omitempty"`
}

func (m *GetTopicLikedUsersResp) Reset()         { *m = GetTopicLikedUsersResp{} }
func (m *GetTopicLikedUsersResp) String() string { return proto.CompactTextString(m) }
func (*GetTopicLikedUsersResp) ProtoMessage()    {}
func (*GetTopicLikedUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{39}
}

func (m *GetTopicLikedUsersResp) GetTopicLikedUserList() []uint32 {
	if m != nil {
		return m.TopicLikedUserList
	}
	return nil
}

type GetTopicCommentsReq struct {
	GuildId        uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId        uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	StartCommentId uint32 `protobuf:"varint,3,req,name=start_comment_id,json=startCommentId" json:"start_comment_id"`
	Count          uint32 `protobuf:"varint,4,req,name=count" json:"count"`
}

func (m *GetTopicCommentsReq) Reset()                    { *m = GetTopicCommentsReq{} }
func (m *GetTopicCommentsReq) String() string            { return proto.CompactTextString(m) }
func (*GetTopicCommentsReq) ProtoMessage()               {}
func (*GetTopicCommentsReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{40} }

func (m *GetTopicCommentsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetTopicCommentsReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GetTopicCommentsReq) GetStartCommentId() uint32 {
	if m != nil {
		return m.StartCommentId
	}
	return 0
}

func (m *GetTopicCommentsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetTopicCommentsResp struct {
	CommentList []*GuildCommentWithRef `protobuf:"bytes,1,rep,name=comment_list,json=commentList" json:"comment_list,omitempty"`
}

func (m *GetTopicCommentsResp) Reset()                    { *m = GetTopicCommentsResp{} }
func (m *GetTopicCommentsResp) String() string            { return proto.CompactTextString(m) }
func (*GetTopicCommentsResp) ProtoMessage()               {}
func (*GetTopicCommentsResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{41} }

func (m *GetTopicCommentsResp) GetCommentList() []*GuildCommentWithRef {
	if m != nil {
		return m.CommentList
	}
	return nil
}

type GuildCircleSimpleCommentInfo struct {
	CommentId       uint32 `protobuf:"varint,1,req,name=comment_id,json=commentId" json:"comment_id"`
	GuildId         uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId         uint32 `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Creator         uint32 `protobuf:"varint,4,req,name=creator" json:"creator"`
	CreateTime      uint32 `protobuf:"varint,5,req,name=create_time,json=createTime" json:"create_time"`
	Status          uint32 `protobuf:"varint,6,req,name=status" json:"status"`
	MsgSeqId        uint32 `protobuf:"varint,7,req,name=msg_seq_id,json=msgSeqId" json:"msg_seq_id"`
	RefCommentId    uint32 `protobuf:"varint,8,req,name=ref_comment_id,json=refCommentId" json:"ref_comment_id"`
	OfficalReport   uint32 `protobuf:"varint,9,opt,name=offical_report,json=officalReport" json:"offical_report"`
	ParentCommentId uint32 `protobuf:"varint,10,req,name=parent_comment_id,json=parentCommentId" json:"parent_comment_id"`
	CommentType     uint32 `protobuf:"varint,11,req,name=comment_type,json=commentType" json:"comment_type"`
}

func (m *GuildCircleSimpleCommentInfo) Reset()         { *m = GuildCircleSimpleCommentInfo{} }
func (m *GuildCircleSimpleCommentInfo) String() string { return proto.CompactTextString(m) }
func (*GuildCircleSimpleCommentInfo) ProtoMessage()    {}
func (*GuildCircleSimpleCommentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{42}
}

func (m *GuildCircleSimpleCommentInfo) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *GuildCircleSimpleCommentInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleSimpleCommentInfo) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleSimpleCommentInfo) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *GuildCircleSimpleCommentInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GuildCircleSimpleCommentInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GuildCircleSimpleCommentInfo) GetMsgSeqId() uint32 {
	if m != nil {
		return m.MsgSeqId
	}
	return 0
}

func (m *GuildCircleSimpleCommentInfo) GetRefCommentId() uint32 {
	if m != nil {
		return m.RefCommentId
	}
	return 0
}

func (m *GuildCircleSimpleCommentInfo) GetOfficalReport() uint32 {
	if m != nil {
		return m.OfficalReport
	}
	return 0
}

func (m *GuildCircleSimpleCommentInfo) GetParentCommentId() uint32 {
	if m != nil {
		return m.ParentCommentId
	}
	return 0
}

func (m *GuildCircleSimpleCommentInfo) GetCommentType() uint32 {
	if m != nil {
		return m.CommentType
	}
	return 0
}

type GuildCircleCommentWithSimpleRefInfo struct {
	Comment        *CommentBase                  `protobuf:"bytes,1,req,name=comment" json:"comment,omitempty"`
	RefCommentInfo *GuildCircleSimpleCommentInfo `protobuf:"bytes,2,opt,name=ref_comment_info,json=refCommentInfo" json:"ref_comment_info,omitempty"`
}

func (m *GuildCircleCommentWithSimpleRefInfo) Reset()         { *m = GuildCircleCommentWithSimpleRefInfo{} }
func (m *GuildCircleCommentWithSimpleRefInfo) String() string { return proto.CompactTextString(m) }
func (*GuildCircleCommentWithSimpleRefInfo) ProtoMessage()    {}
func (*GuildCircleCommentWithSimpleRefInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{43}
}

func (m *GuildCircleCommentWithSimpleRefInfo) GetComment() *CommentBase {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *GuildCircleCommentWithSimpleRefInfo) GetRefCommentInfo() *GuildCircleSimpleCommentInfo {
	if m != nil {
		return m.RefCommentInfo
	}
	return nil
}

type GuildCircleCommentWithReply struct {
	Comment              *GuildCircleCommentWithSimpleRefInfo   `protobuf:"bytes,1,req,name=comment" json:"comment,omitempty"`
	ReplyCommentTotalCnt uint32                                 `protobuf:"varint,2,req,name=reply_comment_total_cnt,json=replyCommentTotalCnt" json:"reply_comment_total_cnt"`
	ReplyCommentList     []*GuildCircleCommentWithSimpleRefInfo `protobuf:"bytes,3,rep,name=reply_comment_list,json=replyCommentList" json:"reply_comment_list,omitempty"`
}

func (m *GuildCircleCommentWithReply) Reset()         { *m = GuildCircleCommentWithReply{} }
func (m *GuildCircleCommentWithReply) String() string { return proto.CompactTextString(m) }
func (*GuildCircleCommentWithReply) ProtoMessage()    {}
func (*GuildCircleCommentWithReply) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{44}
}

func (m *GuildCircleCommentWithReply) GetComment() *GuildCircleCommentWithSimpleRefInfo {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *GuildCircleCommentWithReply) GetReplyCommentTotalCnt() uint32 {
	if m != nil {
		return m.ReplyCommentTotalCnt
	}
	return 0
}

func (m *GuildCircleCommentWithReply) GetReplyCommentList() []*GuildCircleCommentWithSimpleRefInfo {
	if m != nil {
		return m.ReplyCommentList
	}
	return nil
}

type GetCommentsReq struct {
	GuildId       uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId       uint32   `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentIdList []uint32 `protobuf:"varint,3,rep,name=comment_id_list,json=commentIdList" json:"comment_id_list,omitempty"`
}

func (m *GetCommentsReq) Reset()                    { *m = GetCommentsReq{} }
func (m *GetCommentsReq) String() string            { return proto.CompactTextString(m) }
func (*GetCommentsReq) ProtoMessage()               {}
func (*GetCommentsReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{45} }

func (m *GetCommentsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCommentsReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GetCommentsReq) GetCommentIdList() []uint32 {
	if m != nil {
		return m.CommentIdList
	}
	return nil
}

type GetCommentsResp struct {
	CommentBaseList []*CommentBase `protobuf:"bytes,1,rep,name=comment_base_list,json=commentBaseList" json:"comment_base_list,omitempty"`
}

func (m *GetCommentsResp) Reset()                    { *m = GetCommentsResp{} }
func (m *GetCommentsResp) String() string            { return proto.CompactTextString(m) }
func (*GetCommentsResp) ProtoMessage()               {}
func (*GetCommentsResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{46} }

func (m *GetCommentsResp) GetCommentBaseList() []*CommentBase {
	if m != nil {
		return m.CommentBaseList
	}
	return nil
}

// 获取评论列表 2016-9-7 Add
type GetCommentListReq struct {
	GuildId        uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId        uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	StartCommentId uint32 `protobuf:"varint,3,req,name=start_comment_id,json=startCommentId" json:"start_comment_id"`
	Count          uint32 `protobuf:"varint,4,req,name=count" json:"count"`
	IncludeStartId bool   `protobuf:"varint,5,opt,name=include_start_id,json=includeStartId" json:"include_start_id"`
}

func (m *GetCommentListReq) Reset()                    { *m = GetCommentListReq{} }
func (m *GetCommentListReq) String() string            { return proto.CompactTextString(m) }
func (*GetCommentListReq) ProtoMessage()               {}
func (*GetCommentListReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{47} }

func (m *GetCommentListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCommentListReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GetCommentListReq) GetStartCommentId() uint32 {
	if m != nil {
		return m.StartCommentId
	}
	return 0
}

func (m *GetCommentListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetCommentListReq) GetIncludeStartId() bool {
	if m != nil {
		return m.IncludeStartId
	}
	return false
}

type GetCommentListResp struct {
	CommentList         []*GuildCircleCommentWithReply `protobuf:"bytes,1,rep,name=comment_list,json=commentList" json:"comment_list,omitempty"`
	NomalCommentLeftCnt uint32                         `protobuf:"varint,2,opt,name=nomal_comment_left_cnt,json=nomalCommentLeftCnt" json:"nomal_comment_left_cnt"`
}

func (m *GetCommentListResp) Reset()                    { *m = GetCommentListResp{} }
func (m *GetCommentListResp) String() string            { return proto.CompactTextString(m) }
func (*GetCommentListResp) ProtoMessage()               {}
func (*GetCommentListResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{48} }

func (m *GetCommentListResp) GetCommentList() []*GuildCircleCommentWithReply {
	if m != nil {
		return m.CommentList
	}
	return nil
}

func (m *GetCommentListResp) GetNomalCommentLeftCnt() uint32 {
	if m != nil {
		return m.NomalCommentLeftCnt
	}
	return 0
}

// 获取指定评论的回复列表 2016-9-7 Add
type GetCommentReplyListReq struct {
	GuildId         uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId         uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	ParentCommentId uint32 `protobuf:"varint,3,req,name=parent_comment_id,json=parentCommentId" json:"parent_comment_id"`
	StartCommentId  uint32 `protobuf:"varint,4,req,name=start_comment_id,json=startCommentId" json:"start_comment_id"`
	Count           uint32 `protobuf:"varint,5,req,name=count" json:"count"`
}

func (m *GetCommentReplyListReq) Reset()         { *m = GetCommentReplyListReq{} }
func (m *GetCommentReplyListReq) String() string { return proto.CompactTextString(m) }
func (*GetCommentReplyListReq) ProtoMessage()    {}
func (*GetCommentReplyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{49}
}

func (m *GetCommentReplyListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCommentReplyListReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GetCommentReplyListReq) GetParentCommentId() uint32 {
	if m != nil {
		return m.ParentCommentId
	}
	return 0
}

func (m *GetCommentReplyListReq) GetStartCommentId() uint32 {
	if m != nil {
		return m.StartCommentId
	}
	return 0
}

func (m *GetCommentReplyListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetCommentReplyListResp struct {
	ReplyCommentList     []*GuildCircleCommentWithSimpleRefInfo `protobuf:"bytes,1,rep,name=reply_comment_list,json=replyCommentList" json:"reply_comment_list,omitempty"`
	ReplyCommentTotalCnt uint32                                 `protobuf:"varint,2,req,name=reply_comment_total_cnt,json=replyCommentTotalCnt" json:"reply_comment_total_cnt"`
	ReplyCommentLeftCnt  uint32                                 `protobuf:"varint,3,opt,name=reply_comment_left_cnt,json=replyCommentLeftCnt" json:"reply_comment_left_cnt"`
}

func (m *GetCommentReplyListResp) Reset()         { *m = GetCommentReplyListResp{} }
func (m *GetCommentReplyListResp) String() string { return proto.CompactTextString(m) }
func (*GetCommentReplyListResp) ProtoMessage()    {}
func (*GetCommentReplyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle, []int{50}
}

func (m *GetCommentReplyListResp) GetReplyCommentList() []*GuildCircleCommentWithSimpleRefInfo {
	if m != nil {
		return m.ReplyCommentList
	}
	return nil
}

func (m *GetCommentReplyListResp) GetReplyCommentTotalCnt() uint32 {
	if m != nil {
		return m.ReplyCommentTotalCnt
	}
	return 0
}

func (m *GetCommentReplyListResp) GetReplyCommentLeftCnt() uint32 {
	if m != nil {
		return m.ReplyCommentLeftCnt
	}
	return 0
}

type GetTopicCountReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetTopicCountReq) Reset()                    { *m = GetTopicCountReq{} }
func (m *GetTopicCountReq) String() string            { return proto.CompactTextString(m) }
func (*GetTopicCountReq) ProtoMessage()               {}
func (*GetTopicCountReq) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{51} }

func (m *GetTopicCountReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetTopicCountResp struct {
	TopicCount uint32 `protobuf:"varint,1,req,name=topic_count,json=topicCount" json:"topic_count"`
}

func (m *GetTopicCountResp) Reset()                    { *m = GetTopicCountResp{} }
func (m *GetTopicCountResp) String() string            { return proto.CompactTextString(m) }
func (*GetTopicCountResp) ProtoMessage()               {}
func (*GetTopicCountResp) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle, []int{52} }

func (m *GetTopicCountResp) GetTopicCount() uint32 {
	if m != nil {
		return m.TopicCount
	}
	return 0
}

func init() {
	proto.RegisterType((*CircleBaseInfo)(nil), "guildcircle.CircleBaseInfo")
	proto.RegisterType((*GuildTopicBase)(nil), "guildcircle.GuildTopicBase")
	proto.RegisterType((*GuildTopicBrief)(nil), "guildcircle.GuildTopicBrief")
	proto.RegisterType((*GuildTopicDetail)(nil), "guildcircle.GuildTopicDetail")
	proto.RegisterType((*CommentBase)(nil), "guildcircle.CommentBase")
	proto.RegisterType((*GuildCommentWithRef)(nil), "guildcircle.GuildCommentWithRef")
	proto.RegisterType((*CircleImageAttr)(nil), "guildcircle.CircleImageAttr")
	proto.RegisterType((*CreateGuildCircleReq)(nil), "guildcircle.CreateGuildCircleReq")
	proto.RegisterType((*CreateTopicReq)(nil), "guildcircle.CreateTopicReq")
	proto.RegisterType((*CreateTopicResp)(nil), "guildcircle.CreateTopicResp")
	proto.RegisterType((*DeleteTopicReq)(nil), "guildcircle.DeleteTopicReq")
	proto.RegisterType((*DeleteTopicResp)(nil), "guildcircle.DeleteTopicResp")
	proto.RegisterType((*CreateTopicCommentReq)(nil), "guildcircle.CreateTopicCommentReq")
	proto.RegisterType((*CreateTopicCommentResp)(nil), "guildcircle.CreateTopicCommentResp")
	proto.RegisterType((*DeleteTopicCommentReq)(nil), "guildcircle.DeleteTopicCommentReq")
	proto.RegisterType((*DeleteTopicCommentResp)(nil), "guildcircle.DeleteTopicCommentResp")
	proto.RegisterType((*LikeReq)(nil), "guildcircle.LikeReq")
	proto.RegisterType((*LikeResp)(nil), "guildcircle.LikeResp")
	proto.RegisterType((*CancelLikeReq)(nil), "guildcircle.CancelLikeReq")
	proto.RegisterType((*CancelLikeResp)(nil), "guildcircle.CancelLikeResp")
	proto.RegisterType((*GetTopicsReq)(nil), "guildcircle.GetTopicsReq")
	proto.RegisterType((*GetTopicsResp)(nil), "guildcircle.GetTopicsResp")
	proto.RegisterType((*GetTopicDetailReq)(nil), "guildcircle.GetTopicDetailReq")
	proto.RegisterType((*GetTopicDetailResp)(nil), "guildcircle.GetTopicDetailResp")
	proto.RegisterType((*GetTopicLikersReq)(nil), "guildcircle.GetTopicLikersReq")
	proto.RegisterType((*GetTopicLikersResp)(nil), "guildcircle.GetTopicLikersResp")
	proto.RegisterType((*GetTopicReferencedCommentCreatorsReq)(nil), "guildcircle.GetTopicReferencedCommentCreatorsReq")
	proto.RegisterType((*GetTopicReferencedCommentCreatorsResp)(nil), "guildcircle.GetTopicReferencedCommentCreatorsResp")
	proto.RegisterType((*CheckLikeResp)(nil), "guildcircle.CheckLikeResp")
	proto.RegisterType((*ReportTopicReq)(nil), "guildcircle.ReportTopicReq")
	proto.RegisterType((*ReportTopicResp)(nil), "guildcircle.ReportTopicResp")
	proto.RegisterType((*GetDeletedTopicIdListReq)(nil), "guildcircle.GetDeletedTopicIdListReq")
	proto.RegisterType((*GetDeletedTopicIdListResp)(nil), "guildcircle.GetDeletedTopicIdListResp")
	proto.RegisterType((*GetCircleReq)(nil), "guildcircle.GetCircleReq")
	proto.RegisterType((*AddTopicTagReq)(nil), "guildcircle.AddTopicTagReq")
	proto.RegisterType((*AddTopicTagResp)(nil), "guildcircle.AddTopicTagResp")
	proto.RegisterType((*CheckCircleTopicsLikeReq)(nil), "guildcircle.CheckCircleTopicsLikeReq")
	proto.RegisterType((*CheckCircleTopicsLikeResp)(nil), "guildcircle.CheckCircleTopicsLikeResp")
	proto.RegisterType((*GetTopicLikedUsersReq)(nil), "guildcircle.GetTopicLikedUsersReq")
	proto.RegisterType((*GetTopicLikedUsersResp)(nil), "guildcircle.GetTopicLikedUsersResp")
	proto.RegisterType((*GetTopicCommentsReq)(nil), "guildcircle.GetTopicCommentsReq")
	proto.RegisterType((*GetTopicCommentsResp)(nil), "guildcircle.GetTopicCommentsResp")
	proto.RegisterType((*GuildCircleSimpleCommentInfo)(nil), "guildcircle.GuildCircleSimpleCommentInfo")
	proto.RegisterType((*GuildCircleCommentWithSimpleRefInfo)(nil), "guildcircle.GuildCircleCommentWithSimpleRefInfo")
	proto.RegisterType((*GuildCircleCommentWithReply)(nil), "guildcircle.GuildCircleCommentWithReply")
	proto.RegisterType((*GetCommentsReq)(nil), "guildcircle.GetCommentsReq")
	proto.RegisterType((*GetCommentsResp)(nil), "guildcircle.GetCommentsResp")
	proto.RegisterType((*GetCommentListReq)(nil), "guildcircle.GetCommentListReq")
	proto.RegisterType((*GetCommentListResp)(nil), "guildcircle.GetCommentListResp")
	proto.RegisterType((*GetCommentReplyListReq)(nil), "guildcircle.GetCommentReplyListReq")
	proto.RegisterType((*GetCommentReplyListResp)(nil), "guildcircle.GetCommentReplyListResp")
	proto.RegisterType((*GetTopicCountReq)(nil), "guildcircle.GetTopicCountReq")
	proto.RegisterType((*GetTopicCountResp)(nil), "guildcircle.GetTopicCountResp")
	proto.RegisterEnum("guildcircle.CIRCLE_TOPIC_TAG", CIRCLE_TOPIC_TAG_name, CIRCLE_TOPIC_TAG_value)
	proto.RegisterEnum("guildcircle.GuildTopicBase_TOPIC_STATUS", GuildTopicBase_TOPIC_STATUS_name, GuildTopicBase_TOPIC_STATUS_value)
	proto.RegisterEnum("guildcircle.CommentBase_COMMENT_STATUS", CommentBase_COMMENT_STATUS_name, CommentBase_COMMENT_STATUS_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for GuildCircle service

type GuildCircleClient interface {
	CreateGuildCircle(ctx context.Context, in *CreateGuildCircleReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// *
	// 	创建主题
	CreateTopic(ctx context.Context, in *CreateTopicReq, opts ...grpc.CallOption) (*CreateTopicResp, error)
	// *
	// 	删除主题
	DeleteTopic(ctx context.Context, in *DeleteTopicReq, opts ...grpc.CallOption) (*DeleteTopicResp, error)
	// *
	// 	创建评论
	CreateTopicComment(ctx context.Context, in *CreateTopicCommentReq, opts ...grpc.CallOption) (*CreateTopicCommentResp, error)
	// *
	// 	删除评论
	DeleteTopicComment(ctx context.Context, in *DeleteTopicCommentReq, opts ...grpc.CallOption) (*DeleteTopicCommentResp, error)
	// *
	// 	点了个赞
	Like(ctx context.Context, in *LikeReq, opts ...grpc.CallOption) (*LikeResp, error)
	// *
	// 	取消赞
	CancelLike(ctx context.Context, in *CancelLikeReq, opts ...grpc.CallOption) (*CancelLikeResp, error)
	// *
	// 	获取圈子的主题列表
	GetTopics(ctx context.Context, in *GetTopicsReq, opts ...grpc.CallOption) (*GetTopicsResp, error)
	// *
	// 	获取特定主题的详细信息
	GetTopicDetail(ctx context.Context, in *GetTopicDetailReq, opts ...grpc.CallOption) (*GetTopicDetailResp, error)
	// *
	// 	帖子加精、取消
	AddTopicTag(ctx context.Context, in *AddTopicTagReq, opts ...grpc.CallOption) (*AddTopicTagResp, error)
	// *
	// 	用户点过的赞
	CheckCircleTopicsLike(ctx context.Context, in *CheckCircleTopicsLikeReq, opts ...grpc.CallOption) (*CheckCircleTopicsLikeResp, error)
	// *
	// 	帖子点赞的用户
	GetTopicLikedUsers(ctx context.Context, in *GetTopicLikedUsersReq, opts ...grpc.CallOption) (*GetTopicLikedUsersResp, error)
	// *
	// 	评论
	GetTopicComments(ctx context.Context, in *GetTopicCommentsReq, opts ...grpc.CallOption) (*GetTopicCommentsResp, error)
	// *
	// 	一级评论
	GetCommentList(ctx context.Context, in *GetCommentListReq, opts ...grpc.CallOption) (*GetCommentListResp, error)
	// *
	// 评论的回复
	GetCommentReplyList(ctx context.Context, in *GetCommentReplyListReq, opts ...grpc.CallOption) (*GetCommentReplyListResp, error)
	// *
	// 获取指定的评论
	GetComments(ctx context.Context, in *GetCommentsReq, opts ...grpc.CallOption) (*GetCommentsResp, error)
	// 话题数量
	GetTopicCount(ctx context.Context, in *GetTopicCountReq, opts ...grpc.CallOption) (*GetTopicCountResp, error)
}

type guildCircleClient struct {
	cc *grpc.ClientConn
}

func NewGuildCircleClient(cc *grpc.ClientConn) GuildCircleClient {
	return &guildCircleClient{cc}
}

func (c *guildCircleClient) CreateGuildCircle(ctx context.Context, in *CreateGuildCircleReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/CreateGuildCircle", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) CreateTopic(ctx context.Context, in *CreateTopicReq, opts ...grpc.CallOption) (*CreateTopicResp, error) {
	out := new(CreateTopicResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/CreateTopic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) DeleteTopic(ctx context.Context, in *DeleteTopicReq, opts ...grpc.CallOption) (*DeleteTopicResp, error) {
	out := new(DeleteTopicResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/DeleteTopic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) CreateTopicComment(ctx context.Context, in *CreateTopicCommentReq, opts ...grpc.CallOption) (*CreateTopicCommentResp, error) {
	out := new(CreateTopicCommentResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/CreateTopicComment", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) DeleteTopicComment(ctx context.Context, in *DeleteTopicCommentReq, opts ...grpc.CallOption) (*DeleteTopicCommentResp, error) {
	out := new(DeleteTopicCommentResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/DeleteTopicComment", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) Like(ctx context.Context, in *LikeReq, opts ...grpc.CallOption) (*LikeResp, error) {
	out := new(LikeResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/Like", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) CancelLike(ctx context.Context, in *CancelLikeReq, opts ...grpc.CallOption) (*CancelLikeResp, error) {
	out := new(CancelLikeResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/CancelLike", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) GetTopics(ctx context.Context, in *GetTopicsReq, opts ...grpc.CallOption) (*GetTopicsResp, error) {
	out := new(GetTopicsResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/GetTopics", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) GetTopicDetail(ctx context.Context, in *GetTopicDetailReq, opts ...grpc.CallOption) (*GetTopicDetailResp, error) {
	out := new(GetTopicDetailResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/GetTopicDetail", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) AddTopicTag(ctx context.Context, in *AddTopicTagReq, opts ...grpc.CallOption) (*AddTopicTagResp, error) {
	out := new(AddTopicTagResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/AddTopicTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) CheckCircleTopicsLike(ctx context.Context, in *CheckCircleTopicsLikeReq, opts ...grpc.CallOption) (*CheckCircleTopicsLikeResp, error) {
	out := new(CheckCircleTopicsLikeResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/CheckCircleTopicsLike", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) GetTopicLikedUsers(ctx context.Context, in *GetTopicLikedUsersReq, opts ...grpc.CallOption) (*GetTopicLikedUsersResp, error) {
	out := new(GetTopicLikedUsersResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/GetTopicLikedUsers", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) GetTopicComments(ctx context.Context, in *GetTopicCommentsReq, opts ...grpc.CallOption) (*GetTopicCommentsResp, error) {
	out := new(GetTopicCommentsResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/GetTopicComments", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) GetCommentList(ctx context.Context, in *GetCommentListReq, opts ...grpc.CallOption) (*GetCommentListResp, error) {
	out := new(GetCommentListResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/GetCommentList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) GetCommentReplyList(ctx context.Context, in *GetCommentReplyListReq, opts ...grpc.CallOption) (*GetCommentReplyListResp, error) {
	out := new(GetCommentReplyListResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/GetCommentReplyList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) GetComments(ctx context.Context, in *GetCommentsReq, opts ...grpc.CallOption) (*GetCommentsResp, error) {
	out := new(GetCommentsResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/GetComments", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildCircleClient) GetTopicCount(ctx context.Context, in *GetTopicCountReq, opts ...grpc.CallOption) (*GetTopicCountResp, error) {
	out := new(GetTopicCountResp)
	err := grpc.Invoke(ctx, "/guildcircle.GuildCircle/GetTopicCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for GuildCircle service

type GuildCircleServer interface {
	CreateGuildCircle(context.Context, *CreateGuildCircleReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// *
	// 	创建主题
	CreateTopic(context.Context, *CreateTopicReq) (*CreateTopicResp, error)
	// *
	// 	删除主题
	DeleteTopic(context.Context, *DeleteTopicReq) (*DeleteTopicResp, error)
	// *
	// 	创建评论
	CreateTopicComment(context.Context, *CreateTopicCommentReq) (*CreateTopicCommentResp, error)
	// *
	// 	删除评论
	DeleteTopicComment(context.Context, *DeleteTopicCommentReq) (*DeleteTopicCommentResp, error)
	// *
	// 	点了个赞
	Like(context.Context, *LikeReq) (*LikeResp, error)
	// *
	// 	取消赞
	CancelLike(context.Context, *CancelLikeReq) (*CancelLikeResp, error)
	// *
	// 	获取圈子的主题列表
	GetTopics(context.Context, *GetTopicsReq) (*GetTopicsResp, error)
	// *
	// 	获取特定主题的详细信息
	GetTopicDetail(context.Context, *GetTopicDetailReq) (*GetTopicDetailResp, error)
	// *
	// 	帖子加精、取消
	AddTopicTag(context.Context, *AddTopicTagReq) (*AddTopicTagResp, error)
	// *
	// 	用户点过的赞
	CheckCircleTopicsLike(context.Context, *CheckCircleTopicsLikeReq) (*CheckCircleTopicsLikeResp, error)
	// *
	// 	帖子点赞的用户
	GetTopicLikedUsers(context.Context, *GetTopicLikedUsersReq) (*GetTopicLikedUsersResp, error)
	// *
	// 	评论
	GetTopicComments(context.Context, *GetTopicCommentsReq) (*GetTopicCommentsResp, error)
	// *
	// 	一级评论
	GetCommentList(context.Context, *GetCommentListReq) (*GetCommentListResp, error)
	// *
	// 评论的回复
	GetCommentReplyList(context.Context, *GetCommentReplyListReq) (*GetCommentReplyListResp, error)
	// *
	// 获取指定的评论
	GetComments(context.Context, *GetCommentsReq) (*GetCommentsResp, error)
	// 话题数量
	GetTopicCount(context.Context, *GetTopicCountReq) (*GetTopicCountResp, error)
}

func RegisterGuildCircleServer(s *grpc.Server, srv GuildCircleServer) {
	s.RegisterService(&_GuildCircle_serviceDesc, srv)
}

func _GuildCircle_CreateGuildCircle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGuildCircleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).CreateGuildCircle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/CreateGuildCircle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).CreateGuildCircle(ctx, req.(*CreateGuildCircleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_CreateTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).CreateTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/CreateTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).CreateTopic(ctx, req.(*CreateTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_DeleteTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).DeleteTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/DeleteTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).DeleteTopic(ctx, req.(*DeleteTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_CreateTopicComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTopicCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).CreateTopicComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/CreateTopicComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).CreateTopicComment(ctx, req.(*CreateTopicCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_DeleteTopicComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTopicCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).DeleteTopicComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/DeleteTopicComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).DeleteTopicComment(ctx, req.(*DeleteTopicCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_Like_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LikeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).Like(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/Like",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).Like(ctx, req.(*LikeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_CancelLike_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelLikeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).CancelLike(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/CancelLike",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).CancelLike(ctx, req.(*CancelLikeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_GetTopics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).GetTopics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/GetTopics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).GetTopics(ctx, req.(*GetTopicsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_GetTopicDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).GetTopicDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/GetTopicDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).GetTopicDetail(ctx, req.(*GetTopicDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_AddTopicTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTopicTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).AddTopicTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/AddTopicTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).AddTopicTag(ctx, req.(*AddTopicTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_CheckCircleTopicsLike_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCircleTopicsLikeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).CheckCircleTopicsLike(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/CheckCircleTopicsLike",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).CheckCircleTopicsLike(ctx, req.(*CheckCircleTopicsLikeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_GetTopicLikedUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicLikedUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).GetTopicLikedUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/GetTopicLikedUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).GetTopicLikedUsers(ctx, req.(*GetTopicLikedUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_GetTopicComments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicCommentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).GetTopicComments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/GetTopicComments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).GetTopicComments(ctx, req.(*GetTopicCommentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_GetCommentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).GetCommentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/GetCommentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).GetCommentList(ctx, req.(*GetCommentListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_GetCommentReplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentReplyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).GetCommentReplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/GetCommentReplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).GetCommentReplyList(ctx, req.(*GetCommentReplyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_GetComments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).GetComments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/GetComments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).GetComments(ctx, req.(*GetCommentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildCircle_GetTopicCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildCircleServer).GetTopicCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildcircle.GuildCircle/GetTopicCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildCircleServer).GetTopicCount(ctx, req.(*GetTopicCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GuildCircle_serviceDesc = grpc.ServiceDesc{
	ServiceName: "guildcircle.GuildCircle",
	HandlerType: (*GuildCircleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGuildCircle",
			Handler:    _GuildCircle_CreateGuildCircle_Handler,
		},
		{
			MethodName: "CreateTopic",
			Handler:    _GuildCircle_CreateTopic_Handler,
		},
		{
			MethodName: "DeleteTopic",
			Handler:    _GuildCircle_DeleteTopic_Handler,
		},
		{
			MethodName: "CreateTopicComment",
			Handler:    _GuildCircle_CreateTopicComment_Handler,
		},
		{
			MethodName: "DeleteTopicComment",
			Handler:    _GuildCircle_DeleteTopicComment_Handler,
		},
		{
			MethodName: "Like",
			Handler:    _GuildCircle_Like_Handler,
		},
		{
			MethodName: "CancelLike",
			Handler:    _GuildCircle_CancelLike_Handler,
		},
		{
			MethodName: "GetTopics",
			Handler:    _GuildCircle_GetTopics_Handler,
		},
		{
			MethodName: "GetTopicDetail",
			Handler:    _GuildCircle_GetTopicDetail_Handler,
		},
		{
			MethodName: "AddTopicTag",
			Handler:    _GuildCircle_AddTopicTag_Handler,
		},
		{
			MethodName: "CheckCircleTopicsLike",
			Handler:    _GuildCircle_CheckCircleTopicsLike_Handler,
		},
		{
			MethodName: "GetTopicLikedUsers",
			Handler:    _GuildCircle_GetTopicLikedUsers_Handler,
		},
		{
			MethodName: "GetTopicComments",
			Handler:    _GuildCircle_GetTopicComments_Handler,
		},
		{
			MethodName: "GetCommentList",
			Handler:    _GuildCircle_GetCommentList_Handler,
		},
		{
			MethodName: "GetCommentReplyList",
			Handler:    _GuildCircle_GetCommentReplyList_Handler,
		},
		{
			MethodName: "GetComments",
			Handler:    _GuildCircle_GetComments_Handler,
		},
		{
			MethodName: "GetTopicCount",
			Handler:    _GuildCircle_GetTopicCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/guildcirclesvr/guildcircle.proto",
}

func (m *CircleBaseInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleBaseInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TodayTopicCount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicCount))
	return i, nil
}

func (m *GuildTopicBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildTopicBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Creator))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.LikeCount))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CommentCount))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.MsgSeqId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Status))
	if len(m.ImgList) > 0 {
		for _, s := range m.ImgList {
			dAtA[i] = 0x52
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x58
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.ReportCount))
	dAtA[i] = 0x60
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.OfficalReport))
	dAtA[i] = 0x68
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Tag))
	dAtA[i] = 0x70
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.LastTime))
	return i, nil
}

func (m *GuildTopicBrief) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildTopicBrief) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Base == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("base")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle(dAtA, i, uint64(m.Base.Size()))
		n1, err := m.Base.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.ContentPreview)))
	i += copy(dAtA[i:], m.ContentPreview)
	return i, nil
}

func (m *GuildTopicDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildTopicDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Base == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("base")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle(dAtA, i, uint64(m.Base.Size()))
		n2, err := m.Base.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	return i, nil
}

func (m *CommentBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommentBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CommentId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Creator))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.MsgSeqId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.RefCommentId))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.LikeCount))
	dAtA[i] = 0x58
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.ReportCount))
	dAtA[i] = 0x60
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.OfficalReport))
	if len(m.ImgList) > 0 {
		for _, s := range m.ImgList {
			dAtA[i] = 0x6a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x70
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Floor))
	dAtA[i] = 0x78
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.ParentCommentId))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CommentType))
	return i, nil
}

func (m *GuildCommentWithRef) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCommentWithRef) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Comment == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("comment")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle(dAtA, i, uint64(m.Comment.Size()))
		n3, err := m.Comment.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.RefComment != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildcircle(dAtA, i, uint64(m.RefComment.Size()))
		n4, err := m.RefComment.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *CircleImageAttr) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleImageAttr) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Width))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Height))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.Format)))
	i += copy(dAtA[i:], m.Format)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.ColorModel)))
	i += copy(dAtA[i:], m.ColorModel)
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.FrameNumber))
	return i, nil
}

func (m *CreateGuildCircleReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateGuildCircleReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *CreateTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.MsgSeqId))
	if len(m.ImgList) > 0 {
		for _, s := range m.ImgList {
			dAtA[i] = 0x2a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x30
	i++
	if m.Highlight {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CreateTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.RemainCooldown))
	return i, nil
}

func (m *DeleteTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *DeleteTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CreateTopicCommentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTopicCommentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.MsgSeqId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.RefCommentId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.UserType))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(len(m.UserName)))
	i += copy(dAtA[i:], m.UserName)
	if len(m.ImgList) > 0 {
		for _, s := range m.ImgList {
			dAtA[i] = 0x42
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x48
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.AntiAdFlag))
	return i, nil
}

func (m *CreateTopicCommentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTopicCommentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CommentId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.RemainCooldown))
	if m.Comment != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGuildcircle(dAtA, i, uint64(m.Comment.Size()))
		n5, err := m.Comment.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *DeleteTopicCommentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteTopicCommentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CommentId))
	return i, nil
}

func (m *DeleteTopicCommentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteTopicCommentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *LikeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LikeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CommentId))
	return i, nil
}

func (m *LikeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LikeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CancelLikeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelLikeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CommentId))
	return i, nil
}

func (m *CancelLikeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelLikeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetTopicsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.StartTopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Tag))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Sorttype))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Userfrom))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.ClientType))
	return i, nil
}

func (m *GetTopicsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TopicBriefList) > 0 {
		for _, msg := range m.TopicBriefList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ImageAttrList) > 0 {
		for _, msg := range m.ImageAttrList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetTopicDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *GetTopicDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TopicDetail == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("topic_detail")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicDetail.Size()))
		n6, err := m.TopicDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if len(m.ImageAttrList) > 0 {
		for _, msg := range m.ImageAttrList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetTopicLikersReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicLikersReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetTopicLikersResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicLikersResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetTopicReferencedCommentCreatorsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicReferencedCommentCreatorsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *GetTopicReferencedCommentCreatorsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicReferencedCommentCreatorsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CheckLikeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckLikeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.LikeStatus))
	return i, nil
}

func (m *ReportTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *ReportTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetDeletedTopicIdListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDeletedTopicIdListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.MinTopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.MaxTopicId))
	return i, nil
}

func (m *GetDeletedTopicIdListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDeletedTopicIdListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DeletedTopicIdList) > 0 {
		for _, num := range m.DeletedTopicIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetCircleReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCircleReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *AddTopicTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddTopicTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Tag))
	dAtA[i] = 0x20
	i++
	if m.Add {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *AddTopicTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddTopicTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CheckCircleTopicsLikeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckCircleTopicsLikeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	if len(m.TopicIdList) > 0 {
		for _, num := range m.TopicIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CheckCircleTopicsLikeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckCircleTopicsLikeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TopicLikedList) > 0 {
		for _, num := range m.TopicLikedList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetTopicLikedUsersReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicLikedUsersReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Start))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetTopicLikedUsersResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicLikedUsersResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TopicLikedUserList) > 0 {
		for _, num := range m.TopicLikedUserList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetTopicCommentsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicCommentsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.StartCommentId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetTopicCommentsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicCommentsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CommentList) > 0 {
		for _, msg := range m.CommentList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GuildCircleSimpleCommentInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleSimpleCommentInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CommentId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Creator))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.MsgSeqId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.RefCommentId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.OfficalReport))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.ParentCommentId))
	dAtA[i] = 0x58
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.CommentType))
	return i, nil
}

func (m *GuildCircleCommentWithSimpleRefInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleCommentWithSimpleRefInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Comment == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("comment")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle(dAtA, i, uint64(m.Comment.Size()))
		n7, err := m.Comment.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.RefCommentInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildcircle(dAtA, i, uint64(m.RefCommentInfo.Size()))
		n8, err := m.RefCommentInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *GuildCircleCommentWithReply) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleCommentWithReply) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Comment == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("comment")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle(dAtA, i, uint64(m.Comment.Size()))
		n9, err := m.Comment.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.ReplyCommentTotalCnt))
	if len(m.ReplyCommentList) > 0 {
		for _, msg := range m.ReplyCommentList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetCommentsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommentsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	if len(m.CommentIdList) > 0 {
		for _, num := range m.CommentIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetCommentsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommentsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CommentBaseList) > 0 {
		for _, msg := range m.CommentBaseList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetCommentListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommentListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.StartCommentId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x28
	i++
	if m.IncludeStartId {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetCommentListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommentListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CommentList) > 0 {
		for _, msg := range m.CommentList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.NomalCommentLeftCnt))
	return i, nil
}

func (m *GetCommentReplyListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommentReplyListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.ParentCommentId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.StartCommentId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetCommentReplyListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommentReplyListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ReplyCommentList) > 0 {
		for _, msg := range m.ReplyCommentList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildcircle(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.ReplyCommentTotalCnt))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.ReplyCommentLeftCnt))
	return i, nil
}

func (m *GetTopicCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetTopicCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTopicCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle(dAtA, i, uint64(m.TopicCount))
	return i, nil
}

func encodeFixed64Guildcircle(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Guildcircle(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGuildcircle(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CircleBaseInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TodayTopicCount))
	n += 1 + sovGuildcircle(uint64(m.TopicCount))
	return n
}

func (m *GuildTopicBase) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	l = len(m.Title)
	n += 1 + l + sovGuildcircle(uint64(l))
	n += 1 + sovGuildcircle(uint64(m.Creator))
	n += 1 + sovGuildcircle(uint64(m.CreateTime))
	n += 1 + sovGuildcircle(uint64(m.LikeCount))
	n += 1 + sovGuildcircle(uint64(m.CommentCount))
	n += 1 + sovGuildcircle(uint64(m.MsgSeqId))
	n += 1 + sovGuildcircle(uint64(m.Status))
	if len(m.ImgList) > 0 {
		for _, s := range m.ImgList {
			l = len(s)
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	n += 1 + sovGuildcircle(uint64(m.ReportCount))
	n += 1 + sovGuildcircle(uint64(m.OfficalReport))
	n += 1 + sovGuildcircle(uint64(m.Tag))
	n += 1 + sovGuildcircle(uint64(m.LastTime))
	return n
}

func (m *GuildTopicBrief) Size() (n int) {
	var l int
	_ = l
	if m.Base != nil {
		l = m.Base.Size()
		n += 1 + l + sovGuildcircle(uint64(l))
	}
	l = len(m.ContentPreview)
	n += 1 + l + sovGuildcircle(uint64(l))
	return n
}

func (m *GuildTopicDetail) Size() (n int) {
	var l int
	_ = l
	if m.Base != nil {
		l = m.Base.Size()
		n += 1 + l + sovGuildcircle(uint64(l))
	}
	l = len(m.Content)
	n += 1 + l + sovGuildcircle(uint64(l))
	return n
}

func (m *CommentBase) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.CommentId))
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	l = len(m.Content)
	n += 1 + l + sovGuildcircle(uint64(l))
	n += 1 + sovGuildcircle(uint64(m.Creator))
	n += 1 + sovGuildcircle(uint64(m.CreateTime))
	n += 1 + sovGuildcircle(uint64(m.Status))
	n += 1 + sovGuildcircle(uint64(m.MsgSeqId))
	n += 1 + sovGuildcircle(uint64(m.RefCommentId))
	n += 1 + sovGuildcircle(uint64(m.LikeCount))
	n += 1 + sovGuildcircle(uint64(m.ReportCount))
	n += 1 + sovGuildcircle(uint64(m.OfficalReport))
	if len(m.ImgList) > 0 {
		for _, s := range m.ImgList {
			l = len(s)
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	n += 1 + sovGuildcircle(uint64(m.Floor))
	n += 1 + sovGuildcircle(uint64(m.ParentCommentId))
	n += 2 + sovGuildcircle(uint64(m.CommentType))
	return n
}

func (m *GuildCommentWithRef) Size() (n int) {
	var l int
	_ = l
	if m.Comment != nil {
		l = m.Comment.Size()
		n += 1 + l + sovGuildcircle(uint64(l))
	}
	if m.RefComment != nil {
		l = m.RefComment.Size()
		n += 1 + l + sovGuildcircle(uint64(l))
	}
	return n
}

func (m *CircleImageAttr) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovGuildcircle(uint64(l))
	n += 1 + sovGuildcircle(uint64(m.Width))
	n += 1 + sovGuildcircle(uint64(m.Height))
	l = len(m.Format)
	n += 1 + l + sovGuildcircle(uint64(l))
	l = len(m.ColorModel)
	n += 1 + l + sovGuildcircle(uint64(l))
	n += 1 + sovGuildcircle(uint64(m.FrameNumber))
	return n
}

func (m *CreateGuildCircleReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	return n
}

func (m *CreateTopicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	l = len(m.Title)
	n += 1 + l + sovGuildcircle(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovGuildcircle(uint64(l))
	n += 1 + sovGuildcircle(uint64(m.MsgSeqId))
	if len(m.ImgList) > 0 {
		for _, s := range m.ImgList {
			l = len(s)
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	n += 2
	return n
}

func (m *CreateTopicResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.RemainCooldown))
	return n
}

func (m *DeleteTopicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	return n
}

func (m *DeleteTopicResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CreateTopicCommentReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	l = len(m.Content)
	n += 1 + l + sovGuildcircle(uint64(l))
	n += 1 + sovGuildcircle(uint64(m.MsgSeqId))
	n += 1 + sovGuildcircle(uint64(m.RefCommentId))
	n += 1 + sovGuildcircle(uint64(m.UserType))
	l = len(m.UserName)
	n += 1 + l + sovGuildcircle(uint64(l))
	if len(m.ImgList) > 0 {
		for _, s := range m.ImgList {
			l = len(s)
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	n += 1 + sovGuildcircle(uint64(m.AntiAdFlag))
	return n
}

func (m *CreateTopicCommentResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.CommentId))
	n += 1 + sovGuildcircle(uint64(m.RemainCooldown))
	if m.Comment != nil {
		l = m.Comment.Size()
		n += 1 + l + sovGuildcircle(uint64(l))
	}
	return n
}

func (m *DeleteTopicCommentReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.CommentId))
	return n
}

func (m *DeleteTopicCommentResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *LikeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.CommentId))
	return n
}

func (m *LikeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CancelLikeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.CommentId))
	return n
}

func (m *CancelLikeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetTopicsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.StartTopicId))
	n += 1 + sovGuildcircle(uint64(m.Count))
	n += 1 + sovGuildcircle(uint64(m.Tag))
	n += 1 + sovGuildcircle(uint64(m.Sorttype))
	n += 1 + sovGuildcircle(uint64(m.Userfrom))
	n += 1 + sovGuildcircle(uint64(m.ClientType))
	return n
}

func (m *GetTopicsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TopicBriefList) > 0 {
		for _, e := range m.TopicBriefList {
			l = e.Size()
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	if len(m.ImageAttrList) > 0 {
		for _, e := range m.ImageAttrList {
			l = e.Size()
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	return n
}

func (m *GetTopicDetailReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	return n
}

func (m *GetTopicDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.TopicDetail != nil {
		l = m.TopicDetail.Size()
		n += 1 + l + sovGuildcircle(uint64(l))
	}
	if len(m.ImageAttrList) > 0 {
		for _, e := range m.ImageAttrList {
			l = e.Size()
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	return n
}

func (m *GetTopicLikersReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.Offset))
	n += 1 + sovGuildcircle(uint64(m.Limit))
	return n
}

func (m *GetTopicLikersResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovGuildcircle(uint64(e))
		}
	}
	return n
}

func (m *GetTopicReferencedCommentCreatorsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	return n
}

func (m *GetTopicReferencedCommentCreatorsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovGuildcircle(uint64(e))
		}
	}
	return n
}

func (m *CheckLikeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.LikeStatus))
	return n
}

func (m *ReportTopicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	return n
}

func (m *ReportTopicResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetDeletedTopicIdListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.MinTopicId))
	n += 1 + sovGuildcircle(uint64(m.MaxTopicId))
	return n
}

func (m *GetDeletedTopicIdListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DeletedTopicIdList) > 0 {
		for _, e := range m.DeletedTopicIdList {
			n += 1 + sovGuildcircle(uint64(e))
		}
	}
	return n
}

func (m *GetCircleReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	return n
}

func (m *AddTopicTagReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.Tag))
	n += 2
	return n
}

func (m *AddTopicTagResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CheckCircleTopicsLikeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	if len(m.TopicIdList) > 0 {
		for _, e := range m.TopicIdList {
			n += 1 + sovGuildcircle(uint64(e))
		}
	}
	return n
}

func (m *CheckCircleTopicsLikeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TopicLikedList) > 0 {
		for _, e := range m.TopicLikedList {
			n += 1 + sovGuildcircle(uint64(e))
		}
	}
	return n
}

func (m *GetTopicLikedUsersReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.Start))
	n += 1 + sovGuildcircle(uint64(m.Count))
	return n
}

func (m *GetTopicLikedUsersResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TopicLikedUserList) > 0 {
		for _, e := range m.TopicLikedUserList {
			n += 1 + sovGuildcircle(uint64(e))
		}
	}
	return n
}

func (m *GetTopicCommentsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.StartCommentId))
	n += 1 + sovGuildcircle(uint64(m.Count))
	return n
}

func (m *GetTopicCommentsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CommentList) > 0 {
		for _, e := range m.CommentList {
			l = e.Size()
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	return n
}

func (m *GuildCircleSimpleCommentInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.CommentId))
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.Creator))
	n += 1 + sovGuildcircle(uint64(m.CreateTime))
	n += 1 + sovGuildcircle(uint64(m.Status))
	n += 1 + sovGuildcircle(uint64(m.MsgSeqId))
	n += 1 + sovGuildcircle(uint64(m.RefCommentId))
	n += 1 + sovGuildcircle(uint64(m.OfficalReport))
	n += 1 + sovGuildcircle(uint64(m.ParentCommentId))
	n += 1 + sovGuildcircle(uint64(m.CommentType))
	return n
}

func (m *GuildCircleCommentWithSimpleRefInfo) Size() (n int) {
	var l int
	_ = l
	if m.Comment != nil {
		l = m.Comment.Size()
		n += 1 + l + sovGuildcircle(uint64(l))
	}
	if m.RefCommentInfo != nil {
		l = m.RefCommentInfo.Size()
		n += 1 + l + sovGuildcircle(uint64(l))
	}
	return n
}

func (m *GuildCircleCommentWithReply) Size() (n int) {
	var l int
	_ = l
	if m.Comment != nil {
		l = m.Comment.Size()
		n += 1 + l + sovGuildcircle(uint64(l))
	}
	n += 1 + sovGuildcircle(uint64(m.ReplyCommentTotalCnt))
	if len(m.ReplyCommentList) > 0 {
		for _, e := range m.ReplyCommentList {
			l = e.Size()
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	return n
}

func (m *GetCommentsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	if len(m.CommentIdList) > 0 {
		for _, e := range m.CommentIdList {
			n += 1 + sovGuildcircle(uint64(e))
		}
	}
	return n
}

func (m *GetCommentsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CommentBaseList) > 0 {
		for _, e := range m.CommentBaseList {
			l = e.Size()
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	return n
}

func (m *GetCommentListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.StartCommentId))
	n += 1 + sovGuildcircle(uint64(m.Count))
	n += 2
	return n
}

func (m *GetCommentListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CommentList) > 0 {
		for _, e := range m.CommentList {
			l = e.Size()
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	n += 1 + sovGuildcircle(uint64(m.NomalCommentLeftCnt))
	return n
}

func (m *GetCommentReplyListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	n += 1 + sovGuildcircle(uint64(m.TopicId))
	n += 1 + sovGuildcircle(uint64(m.ParentCommentId))
	n += 1 + sovGuildcircle(uint64(m.StartCommentId))
	n += 1 + sovGuildcircle(uint64(m.Count))
	return n
}

func (m *GetCommentReplyListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ReplyCommentList) > 0 {
		for _, e := range m.ReplyCommentList {
			l = e.Size()
			n += 1 + l + sovGuildcircle(uint64(l))
		}
	}
	n += 1 + sovGuildcircle(uint64(m.ReplyCommentTotalCnt))
	n += 1 + sovGuildcircle(uint64(m.ReplyCommentLeftCnt))
	return n
}

func (m *GetTopicCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.GuildId))
	return n
}

func (m *GetTopicCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle(uint64(m.TopicCount))
	return n
}

func sovGuildcircle(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGuildcircle(x uint64) (n int) {
	return sovGuildcircle(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *CircleBaseInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleBaseInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleBaseInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TodayTopicCount", wireType)
			}
			m.TodayTopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TodayTopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicCount", wireType)
			}
			m.TopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("today_topic_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildTopicBase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildTopicBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildTopicBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			m.Creator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Creator |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeCount", wireType)
			}
			m.LikeCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentCount", wireType)
			}
			m.CommentCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgSeqId", wireType)
			}
			m.MsgSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgList = append(m.ImgList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportCount", wireType)
			}
			m.ReportCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficalReport", wireType)
			}
			m.OfficalReport = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficalReport |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			m.Tag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastTime", wireType)
			}
			m.LastTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("like_count")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_count")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_seq_id")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildTopicBrief) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildTopicBrief: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildTopicBrief: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Base", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Base == nil {
				m.Base = &GuildTopicBase{}
			}
			if err := m.Base.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContentPreview", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ContentPreview = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content_preview")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildTopicDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildTopicDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildTopicDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Base", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Base == nil {
				m.Base = &GuildTopicBase{}
			}
			if err := m.Base.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommentBase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CommentBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CommentBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			m.Creator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Creator |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgSeqId", wireType)
			}
			m.MsgSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefCommentId", wireType)
			}
			m.RefCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RefCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeCount", wireType)
			}
			m.LikeCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportCount", wireType)
			}
			m.ReportCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficalReport", wireType)
			}
			m.OfficalReport = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficalReport |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgList = append(m.ImgList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Floor", wireType)
			}
			m.Floor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Floor |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentCommentId", wireType)
			}
			m.ParentCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentType", wireType)
			}
			m.CommentType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_seq_id")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ref_comment_id")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("like_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCommentWithRef) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCommentWithRef: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCommentWithRef: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Comment", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Comment == nil {
				m.Comment = &CommentBase{}
			}
			if err := m.Comment.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefComment", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RefComment == nil {
				m.RefComment = &CommentBase{}
			}
			if err := m.RefComment.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleImageAttr) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleImageAttr: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleImageAttr: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Width", wireType)
			}
			m.Width = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Width |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			m.Height = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Height |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Format", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Format = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ColorModel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ColorModel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FrameNumber", wireType)
			}
			m.FrameNumber = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FrameNumber |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("width")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("height")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateGuildCircleReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateGuildCircleReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateGuildCircleReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgSeqId", wireType)
			}
			m.MsgSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgList = append(m.ImgList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Highlight", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Highlight = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainCooldown", wireType)
			}
			m.RemainCooldown = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainCooldown |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteTopicResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTopicCommentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateTopicCommentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateTopicCommentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgSeqId", wireType)
			}
			m.MsgSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefCommentId", wireType)
			}
			m.RefCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RefCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserType", wireType)
			}
			m.UserType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgList = append(m.ImgList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AntiAdFlag", wireType)
			}
			m.AntiAdFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AntiAdFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTopicCommentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateTopicCommentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateTopicCommentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainCooldown", wireType)
			}
			m.RemainCooldown = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainCooldown |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Comment", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Comment == nil {
				m.Comment = &GuildCommentWithRef{}
			}
			if err := m.Comment.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteTopicCommentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteTopicCommentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteTopicCommentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteTopicCommentResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteTopicCommentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteTopicCommentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LikeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LikeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LikeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LikeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LikeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LikeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelLikeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CancelLikeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CancelLikeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelLikeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CancelLikeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CancelLikeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTopicId", wireType)
			}
			m.StartTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			m.Tag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sorttype", wireType)
			}
			m.Sorttype = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sorttype |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Userfrom", wireType)
			}
			m.Userfrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Userfrom |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicBriefList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicBriefList = append(m.TopicBriefList, &GuildTopicBrief{})
			if err := m.TopicBriefList[len(m.TopicBriefList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageAttrList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageAttrList = append(m.ImageAttrList, &CircleImageAttr{})
			if err := m.ImageAttrList[len(m.ImageAttrList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TopicDetail == nil {
				m.TopicDetail = &GuildTopicDetail{}
			}
			if err := m.TopicDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageAttrList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageAttrList = append(m.ImageAttrList, &CircleImageAttr{})
			if err := m.ImageAttrList[len(m.ImageAttrList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_detail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicLikersReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicLikersReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicLikersReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicLikersResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicLikersResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicLikersResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildcircle
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildcircle
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicReferencedCommentCreatorsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicReferencedCommentCreatorsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicReferencedCommentCreatorsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicReferencedCommentCreatorsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicReferencedCommentCreatorsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicReferencedCommentCreatorsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildcircle
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildcircle
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckLikeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckLikeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckLikeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeStatus", wireType)
			}
			m.LikeStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("like_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportTopicResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDeletedTopicIdListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDeletedTopicIdListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDeletedTopicIdListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MinTopicId", wireType)
			}
			m.MinTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxTopicId", wireType)
			}
			m.MaxTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("min_topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("max_topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDeletedTopicIdListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDeletedTopicIdListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDeletedTopicIdListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.DeletedTopicIdList = append(m.DeletedTopicIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildcircle
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildcircle
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.DeletedTopicIdList = append(m.DeletedTopicIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeletedTopicIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCircleReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCircleReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCircleReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddTopicTagReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddTopicTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddTopicTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			m.Tag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Add", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Add = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("add")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddTopicTagResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddTopicTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddTopicTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckCircleTopicsLikeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckCircleTopicsLikeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckCircleTopicsLikeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TopicIdList = append(m.TopicIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildcircle
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildcircle
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TopicIdList = append(m.TopicIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckCircleTopicsLikeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckCircleTopicsLikeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckCircleTopicsLikeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TopicLikedList = append(m.TopicLikedList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildcircle
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildcircle
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TopicLikedList = append(m.TopicLikedList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicLikedList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicLikedUsersReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicLikedUsersReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicLikedUsersReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicLikedUsersResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicLikedUsersResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicLikedUsersResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TopicLikedUserList = append(m.TopicLikedUserList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildcircle
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildcircle
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TopicLikedUserList = append(m.TopicLikedUserList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicLikedUserList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicCommentsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicCommentsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicCommentsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCommentId", wireType)
			}
			m.StartCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_comment_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicCommentsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicCommentsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicCommentsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CommentList = append(m.CommentList, &GuildCommentWithRef{})
			if err := m.CommentList[len(m.CommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleSimpleCommentInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleSimpleCommentInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleSimpleCommentInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			m.Creator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Creator |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgSeqId", wireType)
			}
			m.MsgSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefCommentId", wireType)
			}
			m.RefCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RefCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficalReport", wireType)
			}
			m.OfficalReport = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficalReport |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentCommentId", wireType)
			}
			m.ParentCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentType", wireType)
			}
			m.CommentType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_seq_id")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ref_comment_id")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("parent_comment_id")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleCommentWithSimpleRefInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleCommentWithSimpleRefInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleCommentWithSimpleRefInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Comment", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Comment == nil {
				m.Comment = &CommentBase{}
			}
			if err := m.Comment.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefCommentInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RefCommentInfo == nil {
				m.RefCommentInfo = &GuildCircleSimpleCommentInfo{}
			}
			if err := m.RefCommentInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleCommentWithReply) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleCommentWithReply: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleCommentWithReply: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Comment", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Comment == nil {
				m.Comment = &GuildCircleCommentWithSimpleRefInfo{}
			}
			if err := m.Comment.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyCommentTotalCnt", wireType)
			}
			m.ReplyCommentTotalCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReplyCommentTotalCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyCommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReplyCommentList = append(m.ReplyCommentList, &GuildCircleCommentWithSimpleRefInfo{})
			if err := m.ReplyCommentList[len(m.ReplyCommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reply_comment_total_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommentsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommentsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommentsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CommentIdList = append(m.CommentIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildcircle
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildcircle
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CommentIdList = append(m.CommentIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommentsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommentsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommentsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentBaseList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CommentBaseList = append(m.CommentBaseList, &CommentBase{})
			if err := m.CommentBaseList[len(m.CommentBaseList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommentListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommentListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommentListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCommentId", wireType)
			}
			m.StartCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncludeStartId", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IncludeStartId = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_comment_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommentListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommentListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommentListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CommentList = append(m.CommentList, &GuildCircleCommentWithReply{})
			if err := m.CommentList[len(m.CommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NomalCommentLeftCnt", wireType)
			}
			m.NomalCommentLeftCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NomalCommentLeftCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommentReplyListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommentReplyListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommentReplyListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentCommentId", wireType)
			}
			m.ParentCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCommentId", wireType)
			}
			m.StartCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("parent_comment_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommentReplyListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommentReplyListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommentReplyListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyCommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReplyCommentList = append(m.ReplyCommentList, &GuildCircleCommentWithSimpleRefInfo{})
			if err := m.ReplyCommentList[len(m.ReplyCommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyCommentTotalCnt", wireType)
			}
			m.ReplyCommentTotalCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReplyCommentTotalCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyCommentLeftCnt", wireType)
			}
			m.ReplyCommentLeftCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReplyCommentLeftCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reply_comment_total_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTopicCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTopicCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTopicCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicCount", wireType)
			}
			m.TopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGuildcircle(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGuildcircle
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildcircle
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGuildcircle
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGuildcircle
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGuildcircle(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGuildcircle = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGuildcircle   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/guildcirclesvr/guildcircle.proto", fileDescriptorGuildcircle) }

var fileDescriptorGuildcircle = []byte{
	// 2833 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0x4d, 0x6c, 0x1b, 0xc7,
	0x15, 0xce, 0x92, 0x92, 0x48, 0x3e, 0x8a, 0x3f, 0x1a, 0x5b, 0x0a, 0x4d, 0x3b, 0x0a, 0xbd, 0x76,
	0x1c, 0xc7, 0x81, 0xac, 0xc4, 0x2d, 0xda, 0x86, 0x15, 0xd4, 0x48, 0x94, 0x22, 0xb3, 0x96, 0x25,
	0x83, 0x62, 0x1a, 0x14, 0x05, 0xb2, 0x58, 0x93, 0x43, 0x6a, 0xe1, 0xfd, 0xf3, 0xce, 0xd0, 0x96,
	0x0a, 0xb4, 0x48, 0x8a, 0xa2, 0x68, 0xd3, 0x00, 0x6d, 0x73, 0x2b, 0x90, 0xa2, 0x05, 0xea, 0x02,
	0x3d, 0xe5, 0xde, 0x5b, 0xd1, 0x53, 0x0e, 0x3d, 0x24, 0x3d, 0xf5, 0x50, 0x14, 0x45, 0x7a, 0xf1,
	0xb5, 0xd7, 0x9e, 0x8a, 0x99, 0xd9, 0xe5, 0xce, 0x2e, 0x97, 0x12, 0x1d, 0x33, 0x41, 0x6f, 0xd6,
	0x7b, 0x6f, 0x67, 0xbe, 0xf7, 0xe6, 0x7b, 0x6f, 0xde, 0x3c, 0x1a, 0x2e, 0x13, 0xaf, 0xb3, 0xda,
	0x1f, 0x18, 0x66, 0xb7, 0x63, 0x78, 0x1d, 0x13, 0x93, 0x07, 0x9e, 0xfc, 0xe7, 0x75, 0xd7, 0x73,
	0xa8, 0x83, 0xf2, 0x92, 0xa8, 0x7a, 0xb9, 0xe3, 0x58, 0x96, 0x63, 0xaf, 0x52, 0xf3, 0x81, 0x6b,
	0x74, 0xee, 0x99, 0x78, 0x95, 0xdc, 0xbb, 0x3b, 0x30, 0x4c, 0x6a, 0xd8, 0xf4, 0xd8, 0xf5, 0x3f,
	0x51, 0xdf, 0x53, 0xa0, 0xd8, 0xe0, 0x1f, 0x6c, 0xea, 0x04, 0x37, 0xed, 0x9e, 0x83, 0x9e, 0x87,
	0x2c, 0x5f, 0x47, 0x33, 0xba, 0x15, 0xa5, 0x96, 0xba, 0x5a, 0xd8, 0x9c, 0xf9, 0xf8, 0x9f, 0xcf,
	0x3f, 0xd3, 0xca, 0x70, 0x69, 0xb3, 0x8b, 0x5e, 0x81, 0x05, 0xea, 0x74, 0xf5, 0x63, 0x8d, 0x3a,
	0xae, 0xd1, 0xd1, 0x3a, 0xce, 0xc0, 0xa6, 0x95, 0x94, 0x64, 0x59, 0xe2, 0xea, 0x36, 0xd3, 0x36,
	0x98, 0x12, 0xbd, 0x00, 0x79, 0xd9, 0x36, 0x5d, 0x53, 0x86, 0xb6, 0x40, 0x87, 0x66, 0xea, 0x87,
	0x33, 0x50, 0xdc, 0x61, 0x9b, 0xf0, 0x4f, 0x19, 0x20, 0x06, 0x46, 0x7c, 0x19, 0x07, 0xc3, 0xa5,
	0xcd, 0x6e, 0x04, 0x6d, 0x2a, 0x09, 0x6d, 0x15, 0x66, 0xa9, 0x41, 0x4d, 0x5c, 0x49, 0xd7, 0x52,
	0x57, 0x73, 0xbe, 0x56, 0x88, 0xd0, 0x32, 0x64, 0x3a, 0x1e, 0xd6, 0xa9, 0xe3, 0x55, 0x66, 0xe4,
	0x6f, 0x7d, 0x21, 0xc3, 0xcd, 0xff, 0x89, 0x35, 0x6a, 0x58, 0xb8, 0x32, 0x2b, 0xd9, 0x80, 0x50,
	0xb4, 0x0d, 0x0b, 0xa3, 0x4b, 0x00, 0xa6, 0x71, 0x0f, 0xfb, 0xde, 0xcd, 0x49, 0x56, 0x39, 0x26,
	0x17, 0x31, 0x78, 0x09, 0x0a, 0xec, 0x44, 0xb0, 0x4d, 0x7d, 0xbb, 0x8c, 0x64, 0x37, 0xef, 0xab,
	0x84, 0xa9, 0x0a, 0x60, 0x91, 0xbe, 0x46, 0xf0, 0x7d, 0xe6, 0x55, 0x56, 0xb2, 0xcb, 0x5a, 0xa4,
	0x7f, 0x80, 0xef, 0x37, 0xbb, 0xe8, 0x02, 0xcc, 0x11, 0xaa, 0xd3, 0x01, 0xa9, 0xe4, 0x24, 0xbd,
	0x2f, 0x43, 0xe7, 0x20, 0x6b, 0x58, 0x7d, 0xcd, 0x34, 0x08, 0xad, 0x40, 0x2d, 0x7d, 0x35, 0xd7,
	0xca, 0x18, 0x56, 0x7f, 0xd7, 0x20, 0x14, 0xbd, 0x08, 0xf3, 0x1e, 0x76, 0x1d, 0x2f, 0x80, 0x91,
	0x97, 0x0e, 0x23, 0x2f, 0x34, 0x02, 0xc5, 0xcb, 0x50, 0x74, 0x7a, 0x3d, 0xa3, 0xa3, 0x9b, 0x9a,
	0x10, 0x57, 0xe6, 0x25, 0xd3, 0x82, 0xaf, 0x6b, 0x71, 0x15, 0x5a, 0x82, 0x34, 0xd5, 0xfb, 0x95,
	0x82, 0x64, 0xc1, 0x04, 0xe8, 0x22, 0xe4, 0x4c, 0x9d, 0x50, 0x11, 0xbf, 0xa2, 0xa4, 0xcd, 0x32,
	0x31, 0x8b, 0x9e, 0xfa, 0x22, 0xcc, 0xb7, 0xf7, 0xef, 0x34, 0x1b, 0xda, 0x41, 0x7b, 0xa3, 0xfd,
	0xe6, 0x01, 0x02, 0x98, 0xdb, 0xdb, 0x6f, 0xdd, 0xde, 0xd8, 0x2d, 0x3f, 0x83, 0xf2, 0x90, 0xd9,
	0xda, 0xde, 0xdd, 0x6e, 0x6f, 0x6f, 0x95, 0x15, 0xf5, 0x3e, 0x94, 0x24, 0x76, 0x78, 0x06, 0xee,
	0xa1, 0x55, 0x98, 0xb9, 0xab, 0x13, 0xcc, 0xa9, 0x91, 0xbf, 0x71, 0xfe, 0xba, 0x9c, 0x13, 0x51,
	0x26, 0xb5, 0xb8, 0x21, 0x5a, 0x81, 0x52, 0xc7, 0xb1, 0x29, 0x3b, 0x05, 0xd7, 0xc3, 0x0f, 0x0c,
	0xfc, 0x90, 0xb3, 0x26, 0xe0, 0x45, 0xd1, 0x57, 0xde, 0x11, 0x3a, 0xb5, 0x03, 0xe5, 0x70, 0x99,
	0x2d, 0x4c, 0x75, 0xc3, 0x7c, 0xf2, 0x3d, 0x19, 0xcb, 0xc4, 0xb2, 0x91, 0xbd, 0x02, 0xa1, 0xfa,
	0xee, 0x2c, 0xe4, 0x1b, 0xe2, 0xfc, 0x39, 0xe7, 0x2f, 0x01, 0x04, 0x4c, 0x89, 0xb1, 0x3e, 0xe7,
	0xcb, 0x27, 0xe1, 0xbd, 0x9c, 0x39, 0xe9, 0xa4, 0xcc, 0x91, 0x60, 0xcd, 0x24, 0xc0, 0x92, 0x93,
	0x63, 0x76, 0x82, 0xe4, 0x98, 0x1b, 0x93, 0x1c, 0x21, 0x51, 0x33, 0x09, 0x44, 0x9d, 0x84, 0xea,
	0xd7, 0xa0, 0xe8, 0xe1, 0x9e, 0x26, 0xc5, 0x44, 0xa6, 0xfc, 0xbc, 0x87, 0x7b, 0x8d, 0x61, 0x58,
	0xa2, 0xa9, 0x08, 0xc9, 0xa9, 0xf8, 0xc5, 0xa4, 0x80, 0x9c, 0x73, 0x85, 0x68, 0xce, 0x55, 0x61,
	0xb6, 0x67, 0x3a, 0x8e, 0xc7, 0x33, 0x60, 0x36, 0xa8, 0x41, 0x5c, 0xc4, 0xaa, 0xa9, 0xab, 0x7b,
	0xa2, 0x2c, 0x0c, 0x1d, 0x2c, 0x49, 0xdb, 0x94, 0x84, 0x3a, 0xf4, 0xf1, 0x45, 0x08, 0xca, 0x85,
	0xc6, 0x2a, 0x79, 0xa5, 0x2c, 0xc3, 0xf7, 0x35, 0xed, 0x63, 0x17, 0xab, 0x2f, 0x41, 0xb1, 0xb1,
	0x7f, 0xfb, 0xf6, 0xf6, 0x5e, 0xfb, 0xd4, 0xdc, 0xfa, 0xb1, 0x02, 0x67, 0x38, 0x79, 0xfd, 0x6d,
	0xde, 0x32, 0xe8, 0x61, 0x0b, 0xf7, 0xd0, 0x0d, 0x46, 0x12, 0x2e, 0xf1, 0xf9, 0x5e, 0x89, 0xf0,
	0x5d, 0xa2, 0x6d, 0x2b, 0x30, 0x44, 0xaf, 0x41, 0x5e, 0x3a, 0xaf, 0x4a, 0xaa, 0xa6, 0x9c, 0xf8,
	0x1d, 0x84, 0x07, 0xa8, 0x7e, 0xaa, 0x40, 0x49, 0x5c, 0x47, 0x4d, 0x4b, 0xef, 0xe3, 0x0d, 0x4a,
	0x3d, 0x56, 0x5a, 0xee, 0xe1, 0x63, 0xbe, 0x7d, 0xc0, 0x51, 0x26, 0x60, 0x41, 0x7d, 0x68, 0x74,
	0xe9, 0x61, 0x84, 0xfe, 0x42, 0xc4, 0x48, 0x77, 0x88, 0x8d, 0xfe, 0x21, 0x8d, 0x50, 0xdf, 0x97,
	0x31, 0x6d, 0xcf, 0xf1, 0x2c, 0x9d, 0x11, 0x5f, 0x19, 0x2e, 0xea, 0xcb, 0x38, 0xaf, 0x1d, 0xd3,
	0xf1, 0x34, 0xcb, 0xe9, 0x62, 0xb3, 0x32, 0x2b, 0x99, 0x00, 0x57, 0xdc, 0x66, 0x72, 0x76, 0x0a,
	0x3d, 0x4f, 0xb7, 0xb0, 0x66, 0x0f, 0xac, 0xbb, 0xd8, 0xab, 0xcc, 0xc9, 0xa7, 0xc0, 0x35, 0x7b,
	0x5c, 0xa1, 0x7e, 0x1d, 0xce, 0x36, 0x78, 0x3a, 0x88, 0xf8, 0x72, 0xef, 0x5a, 0xf8, 0xfe, 0xa9,
	0xf7, 0xac, 0xfa, 0x09, 0xbb, 0x9b, 0x45, 0x22, 0xb1, 0x94, 0x9d, 0xe4, 0x9b, 0xf0, 0xb6, 0x4b,
	0x25, 0xdf, 0x76, 0x7e, 0xc2, 0xa7, 0x93, 0x12, 0x3e, 0x9a, 0x8b, 0x33, 0x89, 0xb9, 0x28, 0x93,
	0x7c, 0x36, 0x4a, 0x72, 0x15, 0x72, 0x87, 0x46, 0xff, 0xd0, 0xe4, 0x61, 0x67, 0xd1, 0xc8, 0x06,
	0x99, 0x37, 0x14, 0xab, 0x3a, 0x94, 0x22, 0x1e, 0x11, 0xf7, 0xf4, 0x1b, 0x7e, 0x05, 0x4a, 0x1e,
	0xb6, 0x74, 0xc3, 0xd6, 0x3a, 0x8e, 0x63, 0x76, 0x9d, 0x87, 0x36, 0xa7, 0x54, 0x60, 0x57, 0x14,
	0xca, 0x86, 0xaf, 0x53, 0x5b, 0x50, 0xdc, 0xc2, 0x26, 0x7e, 0x92, 0xa0, 0xc9, 0x10, 0x52, 0x09,
	0x10, 0xd4, 0x05, 0x28, 0x45, 0xd6, 0x24, 0xae, 0xfa, 0xb7, 0x14, 0x2c, 0x4a, 0xae, 0xf8, 0x04,
	0x9e, 0xca, 0x76, 0x53, 0x39, 0xa8, 0xd1, 0xa2, 0x39, 0x2b, 0x05, 0x2d, 0x5a, 0x34, 0x2f, 0x42,
	0x6e, 0x40, 0xb0, 0x27, 0xaa, 0x89, 0xcc, 0xe3, 0x2c, 0x13, 0xb3, 0x52, 0x32, 0x34, 0xb1, 0x75,
	0x0b, 0x57, 0x32, 0x52, 0x4a, 0x70, 0x93, 0x3d, 0xdd, 0xc2, 0x11, 0x6a, 0x64, 0xa3, 0xd4, 0xb8,
	0x02, 0xf3, 0xba, 0x4d, 0x0d, 0x4d, 0xef, 0x6a, 0x3d, 0x53, 0xef, 0x57, 0x72, 0x72, 0x03, 0xc8,
	0x34, 0x1b, 0xdd, 0x37, 0x4c, 0xbd, 0xaf, 0xfe, 0x51, 0x81, 0xa5, 0xa4, 0xa0, 0x12, 0x77, 0xb2,
	0x4b, 0xf1, 0xc9, 0xa8, 0x82, 0xea, 0x61, 0x71, 0x4b, 0xf3, 0x22, 0x55, 0x1b, 0xbd, 0xcc, 0xa3,
	0xf5, 0x70, 0x58, 0xe4, 0xd4, 0x1f, 0xc2, 0xa2, 0x44, 0x89, 0xa9, 0x1e, 0x7f, 0xd4, 0xd5, 0x74,
	0xa2, 0xab, 0x6a, 0x05, 0x96, 0x92, 0xf6, 0x27, 0xae, 0xea, 0x42, 0x66, 0xd7, 0xb8, 0x87, 0xbf,
	0x18, 0x2c, 0x4a, 0x12, 0x16, 0x80, 0xac, 0xd8, 0x91, 0xb8, 0xea, 0x03, 0x28, 0x34, 0x74, 0xbb,
	0x83, 0xcd, 0x2f, 0x19, 0x43, 0x19, 0x8a, 0xf2, 0xbe, 0xc4, 0x55, 0xff, 0xab, 0xc0, 0xfc, 0x0e,
	0xa6, 0x3c, 0x3e, 0x64, 0x22, 0x24, 0xd7, 0xa0, 0x48, 0xa8, 0xee, 0x51, 0x2d, 0x11, 0xcf, 0x3c,
	0xd7, 0xb5, 0x7d, 0x50, 0x55, 0x98, 0x0d, 0x1e, 0x33, 0xd2, 0xed, 0xc3, 0x45, 0x41, 0x33, 0x3c,
	0x13, 0x6f, 0x86, 0x6b, 0x90, 0x25, 0x8e, 0x47, 0x79, 0x9a, 0xc9, 0xd9, 0x38, 0x94, 0x32, 0x0b,
	0x96, 0x4f, 0x3d, 0xcf, 0xb1, 0x46, 0x13, 0x91, 0x49, 0xf9, 0xed, 0x64, 0x1a, 0xc3, 0xbb, 0x3f,
	0x23, 0x67, 0x92, 0x50, 0xf0, 0xab, 0xff, 0x37, 0x0a, 0x14, 0x24, 0xe7, 0x89, 0x8b, 0xde, 0x80,
	0xb2, 0x70, 0xeb, 0x2e, 0xeb, 0x9c, 0x45, 0x9a, 0x2a, 0xb5, 0xf4, 0xd5, 0xfc, 0x8d, 0x0b, 0xe3,
	0x5a, 0x58, 0x66, 0xd8, 0x2a, 0xd2, 0xe1, 0xbf, 0x79, 0x2e, 0x6f, 0x41, 0xc9, 0x60, 0x77, 0xb3,
	0xa6, 0x53, 0xea, 0x89, 0x65, 0x52, 0x09, 0xcb, 0xc4, 0x6e, 0xf1, 0x56, 0xc1, 0x08, 0xfe, 0xc9,
	0x56, 0x51, 0xdf, 0x84, 0x85, 0x00, 0x9e, 0x68, 0xab, 0xa7, 0x53, 0xa8, 0x3f, 0x54, 0x00, 0xc5,
	0xd7, 0x25, 0x2e, 0x7a, 0x1d, 0xe6, 0xc5, 0x77, 0x5d, 0x2e, 0xf3, 0x5b, 0x99, 0xe7, 0xc6, 0xf8,
	0xed, 0x7f, 0x28, 0x9e, 0xac, 0x7e, 0xd3, 0x3f, 0x1d, 0xaf, 0x7f, 0xa1, 0x84, 0x6e, 0x33, 0x9e,
	0x7a, 0x64, 0x3a, 0x19, 0x72, 0x01, 0xe6, 0x9c, 0x5e, 0x8f, 0xe0, 0x58, 0xbb, 0x23, 0x64, 0x8c,
	0xaa, 0xa6, 0x61, 0x19, 0x34, 0x72, 0x53, 0x08, 0x91, 0xba, 0x1a, 0xc6, 0x2b, 0x00, 0x44, 0x5c,
	0x56, 0xca, 0x07, 0x46, 0x37, 0xe4, 0x48, 0xa1, 0x95, 0x19, 0x18, 0x5d, 0xee, 0xc2, 0x21, 0x5c,
	0x0e, 0x3e, 0x68, 0xe1, 0x1e, 0xf6, 0xb0, 0xdd, 0xc1, 0x41, 0x91, 0x6c, 0x88, 0x97, 0xc1, 0x74,
	0x9c, 0x52, 0x37, 0xe1, 0x85, 0x09, 0x76, 0x3a, 0x19, 0xed, 0xd7, 0xa0, 0xd0, 0x38, 0xc4, 0x9d,
	0x7b, 0x41, 0x51, 0x60, 0xe9, 0xc3, 0xdf, 0x07, 0xfe, 0x93, 0x44, 0x46, 0xc6, 0x1f, 0x0e, 0x07,
	0x5c, 0xce, 0x9a, 0x08, 0xd1, 0xd5, 0x4f, 0xb7, 0x89, 0x88, 0xac, 0x49, 0x5c, 0xf5, 0xe7, 0x0a,
	0x54, 0x76, 0x30, 0x15, 0x85, 0xbc, 0xeb, 0x97, 0x16, 0x06, 0x7c, 0xa2, 0x1d, 0xaf, 0xc0, 0xbc,
	0x65, 0xd8, 0xc9, 0xc5, 0x0a, 0x2c, 0xc3, 0x0e, 0x4a, 0x15, 0xb3, 0xd3, 0x8f, 0xb4, 0xc4, 0xd7,
	0x20, 0x58, 0xfa, 0x91, 0x6f, 0xa7, 0xee, 0xc1, 0xb9, 0x31, 0x60, 0x88, 0x8b, 0x5e, 0x85, 0xc5,
	0xae, 0xd0, 0x0c, 0x17, 0x12, 0x11, 0x4f, 0xf3, 0x88, 0xa3, 0xee, 0xc8, 0x67, 0xea, 0x2a, 0xaf,
	0xbf, 0x4f, 0xd0, 0xf0, 0xfe, 0x48, 0x81, 0xe2, 0x46, 0x57, 0xac, 0xd1, 0xd6, 0xfb, 0xd3, 0xc9,
	0x0d, 0xbf, 0x18, 0xcb, 0x4e, 0xf3, 0x62, 0xbc, 0x04, 0x69, 0xbd, 0x2b, 0xba, 0xa7, 0xa0, 0x51,
	0x65, 0x02, 0x76, 0x4c, 0x11, 0x0c, 0xc4, 0x55, 0x35, 0xa8, 0x70, 0x16, 0x09, 0x57, 0x44, 0x4d,
	0x9d, 0xf8, 0x7a, 0x53, 0xa1, 0x10, 0x0d, 0x58, 0x8a, 0x07, 0x2c, 0x4f, 0xa5, 0x48, 0x6d, 0xc3,
	0xb9, 0x31, 0x1b, 0x10, 0x17, 0x5d, 0x0d, 0x0a, 0x37, 0xe3, 0x67, 0x84, 0xe6, 0xa2, 0x34, 0x33,
	0x43, 0xb1, 0xcc, 0xaf, 0x14, 0x58, 0x94, 0xb3, 0xb9, 0xfb, 0x26, 0x99, 0x5a, 0x89, 0xa9, 0xc2,
	0x2c, 0xbf, 0xff, 0xa2, 0xf7, 0x1d, 0x17, 0x85, 0x77, 0xe1, 0xcc, 0xc8, 0x5d, 0xa8, 0xde, 0x82,
	0xa5, 0x24, 0x48, 0x82, 0x51, 0xb2, 0x5f, 0xbc, 0xbd, 0x94, 0x9c, 0x43, 0x34, 0xf2, 0x0d, 0x77,
	0xf0, 0xf7, 0xec, 0x95, 0xea, 0xaf, 0xe6, 0x57, 0x82, 0x29, 0xb9, 0x77, 0x1d, 0xca, 0xe2, 0xea,
	0x1f, 0xd3, 0x79, 0x89, 0xc6, 0x20, 0x6c, 0x99, 0x4f, 0x72, 0xf9, 0x7b, 0x70, 0x76, 0x14, 0x24,
	0x71, 0x51, 0x23, 0x7c, 0xb7, 0x4b, 0xb7, 0xef, 0xe9, 0x3d, 0x67, 0xf0, 0xa6, 0xe7, 0x21, 0xf8,
	0x4b, 0x1a, 0x2e, 0x48, 0x0f, 0xc9, 0x03, 0xc3, 0x72, 0x4d, 0x1c, 0xe0, 0xb2, 0x7b, 0xce, 0x97,
	0x39, 0x3d, 0x9a, 0xc2, 0xe8, 0x34, 0x9c, 0x0e, 0xcd, 0x9d, 0x3a, 0x1d, 0xca, 0x4c, 0xf8, 0xd0,
	0xc9, 0x8e, 0x9d, 0x0e, 0x8d, 0xce, 0x73, 0x72, 0xe3, 0xe7, 0x39, 0x89, 0x83, 0x19, 0x79, 0xa2,
	0x74, 0xea, 0x60, 0x26, 0x2f, 0x19, 0x47, 0x06, 0x33, 0x1f, 0x29, 0x70, 0x49, 0x3a, 0x44, 0xe9,
	0xbc, 0xc5, 0x79, 0xb6, 0x70, 0x8f, 0x9f, 0xe5, 0xe7, 0x99, 0xbe, 0x1c, 0x40, 0x39, 0x12, 0x0f,
	0xbb, 0xe7, 0xf8, 0x23, 0x98, 0x97, 0x12, 0x98, 0x96, 0x4c, 0x22, 0xf6, 0x52, 0xea, 0x49, 0x7f,
	0xab, 0x3f, 0x49, 0xc1, 0xf9, 0x64, 0xc0, 0x2d, 0xec, 0x9a, 0xc7, 0xe8, 0xdb, 0x71, 0xa0, 0xaf,
	0x8c, 0xdb, 0x6b, 0x9c, 0xaf, 0xa1, 0x03, 0xdf, 0x84, 0x67, 0x3d, 0xb6, 0xe8, 0xd0, 0x05, 0xea,
	0x50, 0xdd, 0xd4, 0x3a, 0xb1, 0x1f, 0x19, 0xce, 0x72, 0x23, 0x7f, 0xb9, 0x36, 0x33, 0x69, 0xd8,
	0x14, 0xbd, 0x0d, 0x28, 0xfa, 0xf1, 0xf0, 0x8e, 0xfa, 0x3c, 0x98, 0xca, 0xf2, 0x1e, 0x3c, 0xfd,
	0xbe, 0x0f, 0x45, 0x76, 0xa7, 0x4d, 0xb5, 0xf6, 0x5c, 0x81, 0x52, 0xc8, 0x30, 0xf9, 0x52, 0x2d,
	0x0c, 0x13, 0x96, 0xef, 0xfd, 0x16, 0x94, 0x22, 0x7b, 0x13, 0x17, 0x6d, 0xc1, 0x42, 0xf0, 0xe9,
	0x5d, 0x9d, 0x60, 0xb9, 0xae, 0x8c, 0xa7, 0x4a, 0xb0, 0x1b, 0xfb, 0x83, 0x2f, 0xfc, 0xa9, 0x68,
	0x4b, 0x25, 0x3f, 0xff, 0xef, 0x8a, 0x2a, 0x5b, 0xcb, 0xb0, 0x3b, 0xe6, 0xa0, 0xcb, 0x7b, 0x37,
	0x6f, 0x38, 0xd1, 0x08, 0xee, 0xee, 0xa2, 0xaf, 0x3d, 0x60, 0xca, 0xf0, 0x25, 0x10, 0xf1, 0x89,
	0xb8, 0xe8, 0x56, 0x62, 0x0d, 0xbe, 0x3a, 0x01, 0x33, 0x38, 0xd1, 0x23, 0xb5, 0x18, 0xbd, 0x06,
	0x4b, 0xb6, 0x63, 0x31, 0x6e, 0x06, 0x4b, 0xe2, 0x1e, 0xf5, 0x89, 0x1a, 0x96, 0x95, 0x33, 0xdc,
	0x26, 0x00, 0x82, 0x7b, 0xb4, 0x61, 0x53, 0xf5, 0xef, 0x0a, 0xbf, 0x17, 0x87, 0xef, 0x76, 0xd7,
	0x3c, 0x9e, 0x5e, 0xdc, 0x13, 0x2b, 0x57, 0xfa, 0xa4, 0xca, 0x95, 0x74, 0x52, 0x33, 0x93, 0x9c,
	0xd4, 0xec, 0xe8, 0xf5, 0xf7, 0x1f, 0x05, 0x9e, 0x4d, 0x74, 0x8d, 0xb8, 0x63, 0xd2, 0x53, 0x99,
	0x56, 0x7a, 0x3e, 0x5d, 0xed, 0x78, 0x0d, 0x96, 0x62, 0xe0, 0x82, 0xe3, 0x94, 0x67, 0x0e, 0x67,
	0x22, 0x9b, 0xfa, 0xc7, 0xf9, 0x15, 0x28, 0x87, 0x57, 0xfe, 0x60, 0xb2, 0x41, 0x90, 0x5a, 0x0f,
	0x1f, 0x83, 0xfe, 0x47, 0xe2, 0x81, 0x22, 0xff, 0x54, 0x1a, 0x79, 0xa0, 0x84, 0x3f, 0x95, 0x5e,
	0x1b, 0x40, 0xb9, 0xd1, 0x6c, 0x35, 0x76, 0xb7, 0x35, 0xf1, 0xdb, 0x59, 0x7b, 0x63, 0x07, 0x15,
	0x20, 0x77, 0xb3, 0xb9, 0x73, 0x73, 0xb7, 0xb9, 0x73, 0xb3, 0x5d, 0x56, 0x50, 0x06, 0xd2, 0xed,
	0xfd, 0x3b, 0xe5, 0x14, 0x9a, 0x87, 0xec, 0x46, 0xa3, 0xdd, 0xfc, 0x4e, 0xb3, 0xfd, 0xdd, 0xf2,
	0x0c, 0x5a, 0x80, 0xc2, 0xce, 0xc6, 0xed, 0x6d, 0x6d, 0x6b, 0xff, 0xad, 0xbd, 0xdd, 0xfd, 0x8d,
	0xad, 0x72, 0x16, 0x2d, 0xc2, 0xc2, 0xc1, 0xcd, 0xe6, 0xf6, 0xee, 0x96, 0xb6, 0xbf, 0xa7, 0x6d,
	0xec, 0x6d, 0xb5, 0xf6, 0x9b, 0x5b, 0xe5, 0x32, 0xb3, 0x0c, 0xc5, 0xcd, 0xfd, 0x83, 0x72, 0xed,
	0xc6, 0x9f, 0xce, 0x42, 0x5e, 0x3a, 0x19, 0x44, 0x61, 0x61, 0x64, 0xb6, 0x8d, 0x2e, 0x46, 0x2b,
	0x4f, 0xc2, 0xec, 0xbb, 0x7a, 0xe1, 0xfa, 0xf0, 0x67, 0xe9, 0xeb, 0x07, 0xb7, 0x36, 0xc5, 0xcf,
	0xd2, 0xdb, 0x96, 0x4b, 0x8f, 0xb5, 0x3b, 0x9b, 0xea, 0xf2, 0x3b, 0x8f, 0x1e, 0xa7, 0x95, 0xf7,
	0x1e, 0x3d, 0x4e, 0xa7, 0xfa, 0xf5, 0x0f, 0x1e, 0x3d, 0x4e, 0x17, 0x56, 0xfa, 0xb5, 0xb5, 0x20,
	0x98, 0xeb, 0xe8, 0x23, 0x05, 0xf2, 0xd2, 0x98, 0x10, 0x9d, 0x4f, 0xd8, 0x30, 0x78, 0xb8, 0x55,
	0x2f, 0x8c, 0x57, 0x12, 0x57, 0xed, 0xb2, 0xad, 0xe6, 0xd8, 0x56, 0xc5, 0x41, 0xfd, 0xa8, 0x4e,
	0xeb, 0xb8, 0x4e, 0xea, 0x6e, 0xdd, 0xe0, 0xdb, 0xee, 0xac, 0x0c, 0x6a, 0x6b, 0x03, 0xa3, 0xbb,
	0x5e, 0x5b, 0x39, 0x92, 0xf6, 0xaf, 0xad, 0xd0, 0xda, 0x1a, 0x1f, 0xa9, 0xaf, 0xd7, 0x56, 0x70,
	0x6d, 0xcd, 0x1f, 0xcb, 0xae, 0xd7, 0x56, 0x48, 0x6d, 0x4d, 0xf4, 0x2a, 0xeb, 0xb5, 0x15, 0xa3,
	0xb6, 0x66, 0x58, 0x7d, 0x6d, 0xe0, 0x99, 0xeb, 0xe8, 0x08, 0xf2, 0xd2, 0xb0, 0x2e, 0x86, 0x37,
	0x3a, 0xad, 0x8e, 0xe1, 0x8d, 0x8f, 0x9d, 0xaf, 0x33, 0xbc, 0x19, 0x86, 0x77, 0x86, 0xa1, 0x65,
	0x28, 0xcf, 0x27, 0x80, 0xf3, 0xcb, 0xc3, 0x3a, 0xfa, 0x87, 0x02, 0x68, 0x74, 0xa2, 0x8a, 0xd4,
	0x71, 0x41, 0x09, 0x07, 0x99, 0xd5, 0x4b, 0xa7, 0xda, 0x10, 0x57, 0xfd, 0x01, 0xc3, 0x93, 0x65,
	0x78, 0xca, 0x22, 0x7e, 0xa4, 0x6e, 0xd7, 0xbd, 0x61, 0x04, 0xdf, 0x3e, 0x29, 0x82, 0x01, 0xc8,
	0x68, 0xdc, 0x6c, 0x39, 0xa0, 0x5e, 0x6d, 0x2d, 0xda, 0xdc, 0xc5, 0x02, 0xfb, 0x5b, 0x05, 0xd0,
	0xe8, 0x18, 0x34, 0xe6, 0x5e, 0xe2, 0x9c, 0x36, 0xe6, 0xde, 0x98, 0x59, 0xea, 0xeb, 0xcc, 0xbd,
	0x1c, 0x73, 0x6f, 0x8e, 0x39, 0x67, 0x73, 0xa7, 0x5e, 0x3e, 0xd1, 0x17, 0x8e, 0x7f, 0x08, 0x17,
	0xbd, 0xa3, 0xc0, 0x0c, 0x7b, 0xc4, 0xa0, 0xb3, 0x91, 0xfd, 0xfc, 0xe7, 0x63, 0x75, 0x31, 0x41,
	0x4a, 0x5c, 0xf5, 0x16, 0xdb, 0x17, 0xd8, 0xbe, 0x59, 0x11, 0x56, 0xb1, 0xf3, 0x57, 0x27, 0x0b,
	0x67, 0x0c, 0xc2, 0x07, 0x0a, 0x40, 0x38, 0x1b, 0x45, 0xd5, 0xe8, 0xb9, 0xca, 0xc3, 0xda, 0xea,
	0xf9, 0xb1, 0xba, 0x00, 0x54, 0x7e, 0x4a, 0xa0, 0xde, 0x57, 0x20, 0x37, 0x1c, 0x50, 0xa2, 0x73,
	0xd1, 0xda, 0x2f, 0x4d, 0x6d, 0xab, 0xd5, 0x71, 0x2a, 0xe2, 0xaa, 0x37, 0x19, 0xa2, 0x79, 0x8e,
	0xe8, 0x88, 0x33, 0x4f, 0x64, 0xc4, 0xab, 0x31, 0x20, 0x8c, 0x5e, 0xfc, 0x76, 0x7b, 0x68, 0xd0,
	0xc3, 0x00, 0xca, 0x80, 0x13, 0x8c, 0x61, 0xd4, 0xfb, 0xeb, 0xe8, 0x5d, 0x85, 0x37, 0x76, 0xf2,
	0xef, 0xfc, 0xcb, 0x89, 0x1b, 0x0f, 0xa7, 0x95, 0xd5, 0xe7, 0x4f, 0xd4, 0x07, 0xb9, 0x5a, 0x98,
	0x3c, 0x57, 0xdf, 0x57, 0x20, 0x2f, 0x8d, 0x1e, 0x62, 0x65, 0x22, 0x3a, 0x18, 0x89, 0x95, 0x89,
	0xf8, 0xc4, 0x62, 0x93, 0x6d, 0x5d, 0xf4, 0x03, 0x43, 0xeb, 0xba, 0x9f, 0x8e, 0x27, 0x33, 0x57,
	0x5f, 0xd3, 0xbb, 0x3c, 0xc1, 0x44, 0x48, 0x7e, 0xa7, 0xc0, 0x62, 0xe2, 0x54, 0x02, 0xbd, 0x10,
	0x65, 0xc9, 0x98, 0xd1, 0x48, 0xf5, 0xca, 0x24, 0x66, 0xc4, 0x55, 0xbf, 0xc1, 0xc0, 0x96, 0x78,
	0x92, 0x09, 0x5e, 0x31, 0xa8, 0x97, 0x56, 0x06, 0xa7, 0x92, 0x0a, 0xfd, 0x41, 0x89, 0x8e, 0x2f,
	0xc5, 0x74, 0x21, 0x96, 0xfe, 0x89, 0x13, 0x91, 0x58, 0xfa, 0x27, 0x8f, 0x28, 0xd4, 0x37, 0x18,
	0xb2, 0xf2, 0x30, 0x8c, 0xc4, 0x67, 0xfc, 0xea, 0xc9, 0xc5, 0x4c, 0x90, 0x8d, 0xf1, 0xcc, 0xa7,
	0x19, 0x2b, 0x53, 0xe5, 0xf8, 0x48, 0x00, 0xd5, 0x12, 0x11, 0x48, 0x4f, 0x8b, 0xea, 0xc5, 0x53,
	0x2c, 0x02, 0x84, 0x0b, 0x4f, 0x8f, 0xf0, 0xd7, 0x8a, 0xfc, 0xb0, 0xe1, 0xbd, 0xd4, 0x08, 0xff,
	0xa3, 0xef, 0x83, 0x51, 0xfe, 0xc7, 0x7a, 0x6d, 0x81, 0x0d, 0x3d, 0x3d, 0xb6, 0x3f, 0x8b, 0xa9,
	0x4f, 0xbc, 0xa1, 0x44, 0x97, 0xc6, 0x00, 0x90, 0xbb, 0xe9, 0xea, 0xe5, 0xd3, 0x8d, 0x88, 0xab,
	0xbe, 0xcd, 0xa0, 0x9e, 0x61, 0x50, 0x21, 0x80, 0xea, 0x72, 0xb0, 0xdb, 0x4f, 0x08, 0xb6, 0xb6,
	0xe2, 0xae, 0x8d, 0xf4, 0xde, 0xeb, 0xe8, 0x67, 0x0a, 0xe4, 0xa5, 0xb7, 0x5b, 0x2c, 0xb5, 0xa3,
	0x2f, 0xca, 0x58, 0x6a, 0xc7, 0x9e, 0x7c, 0xea, 0xb7, 0x18, 0xd4, 0xb3, 0xc3, 0x2b, 0xa9, 0xc3,
	0x61, 0x5e, 0x3b, 0x11, 0x66, 0x27, 0x52, 0x79, 0xed, 0xf0, 0x97, 0x21, 0xf1, 0xbf, 0x5c, 0x9e,
	0x1b, 0x43, 0x33, 0xd1, 0xc7, 0x56, 0x97, 0x4f, 0x52, 0x13, 0x57, 0x74, 0x6b, 0x8b, 0xbc, 0x5b,
	0x3b, 0xf2, 0xbb, 0x35, 0x19, 0x4c, 0x75, 0xee, 0xa7, 0x8f, 0x1e, 0xa7, 0xff, 0xfa, 0x70, 0xb3,
	0xfc, 0xf1, 0x67, 0xcb, 0xca, 0x27, 0x9f, 0x2d, 0x2b, 0xff, 0xfa, 0x6c, 0x59, 0xf9, 0xe5, 0xbf,
	0x97, 0x9f, 0xf9, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x09, 0xd0, 0xf6, 0x75, 0xd6, 0x28, 0x00,
	0x00,
}
