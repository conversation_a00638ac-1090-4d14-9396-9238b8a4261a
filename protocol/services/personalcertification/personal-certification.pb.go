// Code generated by protoc-gen-go. DO NOT EDIT.
// source: personal-certification/personal-certification.proto

package personalcertification // import "golang.52tt.com/protocol/services/personalcertification"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 运营后台配置
type CertType int32

const (
	CertType_UNKNOWN       CertType = 0
	CertType_PERSONAL_CERT CertType = 1
)

var CertType_name = map[int32]string{
	0: "UNKNOWN",
	1: "PERSONAL_CERT",
}
var CertType_value = map[string]int32{
	"UNKNOWN":       0,
	"PERSONAL_CERT": 1,
}

func (x CertType) String() string {
	return proto.EnumName(CertType_name, int32(x))
}
func (CertType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{0}
}

type PresentPosition int32

const (
	PresentPosition_VALID_POSITION     PresentPosition = 0
	PresentPosition_PERSONAL_HOME_PAGE PresentPosition = 1
	PresentPosition_INFORMATION_CARD   PresentPosition = 2
	PresentPosition_RCMD_CARD          PresentPosition = 3
	PresentPosition_UGC_POST           PresentPosition = 4
	PresentPosition_IM_FOLLOW          PresentPosition = 5
	PresentPosition_CHANNEL_NICKNAME   PresentPosition = 6
)

var PresentPosition_name = map[int32]string{
	0: "VALID_POSITION",
	1: "PERSONAL_HOME_PAGE",
	2: "INFORMATION_CARD",
	3: "RCMD_CARD",
	4: "UGC_POST",
	5: "IM_FOLLOW",
	6: "CHANNEL_NICKNAME",
}
var PresentPosition_value = map[string]int32{
	"VALID_POSITION":     0,
	"PERSONAL_HOME_PAGE": 1,
	"INFORMATION_CARD":   2,
	"RCMD_CARD":          3,
	"UGC_POST":           4,
	"IM_FOLLOW":          5,
	"CHANNEL_NICKNAME":   6,
}

func (x PresentPosition) String() string {
	return proto.EnumName(PresentPosition_name, int32(x))
}
func (PresentPosition) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{1}
}

type CertStatusType int32

const (
	CertStatusType_Unknown  CertStatusType = 0
	CertStatusType_Valid    CertStatusType = 1
	CertStatusType_Expired  CertStatusType = 2
	CertStatusType_NotYet   CertStatusType = 3
	CertStatusType_Replaced CertStatusType = 4
)

var CertStatusType_name = map[int32]string{
	0: "Unknown",
	1: "Valid",
	2: "Expired",
	3: "NotYet",
	4: "Replaced",
}
var CertStatusType_value = map[string]int32{
	"Unknown":  0,
	"Valid":    1,
	"Expired":  2,
	"NotYet":   3,
	"Replaced": 4,
}

func (x CertStatusType) String() string {
	return proto.EnumName(CertStatusType_name, int32(x))
}
func (CertStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{2}
}

type GetCertTypeByIdReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCertTypeByIdReq) Reset()         { *m = GetCertTypeByIdReq{} }
func (m *GetCertTypeByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetCertTypeByIdReq) ProtoMessage()    {}
func (*GetCertTypeByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{0}
}
func (m *GetCertTypeByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCertTypeByIdReq.Unmarshal(m, b)
}
func (m *GetCertTypeByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCertTypeByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetCertTypeByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCertTypeByIdReq.Merge(dst, src)
}
func (m *GetCertTypeByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetCertTypeByIdReq.Size(m)
}
func (m *GetCertTypeByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCertTypeByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCertTypeByIdReq proto.InternalMessageInfo

func (m *GetCertTypeByIdReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetCertTypeByIdResp struct {
	Cert                 *CertTypeInfo `protobuf:"bytes,1,opt,name=cert,proto3" json:"cert,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetCertTypeByIdResp) Reset()         { *m = GetCertTypeByIdResp{} }
func (m *GetCertTypeByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetCertTypeByIdResp) ProtoMessage()    {}
func (*GetCertTypeByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{1}
}
func (m *GetCertTypeByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCertTypeByIdResp.Unmarshal(m, b)
}
func (m *GetCertTypeByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCertTypeByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetCertTypeByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCertTypeByIdResp.Merge(dst, src)
}
func (m *GetCertTypeByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetCertTypeByIdResp.Size(m)
}
func (m *GetCertTypeByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCertTypeByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCertTypeByIdResp proto.InternalMessageInfo

func (m *GetCertTypeByIdResp) GetCert() *CertTypeInfo {
	if m != nil {
		return m.Cert
	}
	return nil
}

type PersonalCertificationCommon struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CertType             uint32   `protobuf:"varint,2,opt,name=cert_type,json=certType,proto3" json:"cert_type,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Text                 string   `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	BeginTime            uint32   `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Operator             string   `protobuf:"bytes,8,opt,name=operator,proto3" json:"operator,omitempty"`
	PresentPositionList  []uint32 `protobuf:"varint,9,rep,packed,name=present_position_list,json=presentPositionList,proto3" json:"present_position_list,omitempty"`
	Color                []string `protobuf:"bytes,10,rep,name=color,proto3" json:"color,omitempty"`
	TextShadowColor      string   `protobuf:"bytes,11,opt,name=text_shadow_color,json=textShadowColor,proto3" json:"text_shadow_color,omitempty"`
	ShortText            string   `protobuf:"bytes,12,opt,name=short_text,json=shortText,proto3" json:"short_text,omitempty"`
	Introduce            string   `protobuf:"bytes,13,opt,name=introduce,proto3" json:"introduce,omitempty"`
	JumpUrl              string   `protobuf:"bytes,14,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	CertTypeName         string   `protobuf:"bytes,15,opt,name=cert_type_name,json=certTypeName,proto3" json:"cert_type_name,omitempty"`
	PriorityDisplayBit   int32    `protobuf:"varint,16,opt,name=priority_display_bit,json=priorityDisplayBit,proto3" json:"priority_display_bit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PersonalCertificationCommon) Reset()         { *m = PersonalCertificationCommon{} }
func (m *PersonalCertificationCommon) String() string { return proto.CompactTextString(m) }
func (*PersonalCertificationCommon) ProtoMessage()    {}
func (*PersonalCertificationCommon) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{2}
}
func (m *PersonalCertificationCommon) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PersonalCertificationCommon.Unmarshal(m, b)
}
func (m *PersonalCertificationCommon) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PersonalCertificationCommon.Marshal(b, m, deterministic)
}
func (dst *PersonalCertificationCommon) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PersonalCertificationCommon.Merge(dst, src)
}
func (m *PersonalCertificationCommon) XXX_Size() int {
	return xxx_messageInfo_PersonalCertificationCommon.Size(m)
}
func (m *PersonalCertificationCommon) XXX_DiscardUnknown() {
	xxx_messageInfo_PersonalCertificationCommon.DiscardUnknown(m)
}

var xxx_messageInfo_PersonalCertificationCommon proto.InternalMessageInfo

func (m *PersonalCertificationCommon) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PersonalCertificationCommon) GetCertType() uint32 {
	if m != nil {
		return m.CertType
	}
	return 0
}

func (m *PersonalCertificationCommon) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *PersonalCertificationCommon) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *PersonalCertificationCommon) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *PersonalCertificationCommon) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *PersonalCertificationCommon) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *PersonalCertificationCommon) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *PersonalCertificationCommon) GetPresentPositionList() []uint32 {
	if m != nil {
		return m.PresentPositionList
	}
	return nil
}

func (m *PersonalCertificationCommon) GetColor() []string {
	if m != nil {
		return m.Color
	}
	return nil
}

func (m *PersonalCertificationCommon) GetTextShadowColor() string {
	if m != nil {
		return m.TextShadowColor
	}
	return ""
}

func (m *PersonalCertificationCommon) GetShortText() string {
	if m != nil {
		return m.ShortText
	}
	return ""
}

func (m *PersonalCertificationCommon) GetIntroduce() string {
	if m != nil {
		return m.Introduce
	}
	return ""
}

func (m *PersonalCertificationCommon) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *PersonalCertificationCommon) GetCertTypeName() string {
	if m != nil {
		return m.CertTypeName
	}
	return ""
}

func (m *PersonalCertificationCommon) GetPriorityDisplayBit() int32 {
	if m != nil {
		return m.PriorityDisplayBit
	}
	return 0
}

type PersonalCertificationInfo struct {
	Id                   string                       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Common               *PersonalCertificationCommon `protobuf:"bytes,2,opt,name=common,proto3" json:"common,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *PersonalCertificationInfo) Reset()         { *m = PersonalCertificationInfo{} }
func (m *PersonalCertificationInfo) String() string { return proto.CompactTextString(m) }
func (*PersonalCertificationInfo) ProtoMessage()    {}
func (*PersonalCertificationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{3}
}
func (m *PersonalCertificationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PersonalCertificationInfo.Unmarshal(m, b)
}
func (m *PersonalCertificationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PersonalCertificationInfo.Marshal(b, m, deterministic)
}
func (dst *PersonalCertificationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PersonalCertificationInfo.Merge(dst, src)
}
func (m *PersonalCertificationInfo) XXX_Size() int {
	return xxx_messageInfo_PersonalCertificationInfo.Size(m)
}
func (m *PersonalCertificationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PersonalCertificationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PersonalCertificationInfo proto.InternalMessageInfo

func (m *PersonalCertificationInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PersonalCertificationInfo) GetCommon() *PersonalCertificationCommon {
	if m != nil {
		return m.Common
	}
	return nil
}

type SetPersonalCertificationReq struct {
	InfoList             []*PersonalCertificationInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *SetPersonalCertificationReq) Reset()         { *m = SetPersonalCertificationReq{} }
func (m *SetPersonalCertificationReq) String() string { return proto.CompactTextString(m) }
func (*SetPersonalCertificationReq) ProtoMessage()    {}
func (*SetPersonalCertificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{4}
}
func (m *SetPersonalCertificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPersonalCertificationReq.Unmarshal(m, b)
}
func (m *SetPersonalCertificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPersonalCertificationReq.Marshal(b, m, deterministic)
}
func (dst *SetPersonalCertificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPersonalCertificationReq.Merge(dst, src)
}
func (m *SetPersonalCertificationReq) XXX_Size() int {
	return xxx_messageInfo_SetPersonalCertificationReq.Size(m)
}
func (m *SetPersonalCertificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPersonalCertificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPersonalCertificationReq proto.InternalMessageInfo

func (m *SetPersonalCertificationReq) GetInfoList() []*PersonalCertificationInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type SetPersonalCertificationResp struct {
	FailedUidList        []uint32 `protobuf:"varint,1,rep,packed,name=failed_uid_list,json=failedUidList,proto3" json:"failed_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPersonalCertificationResp) Reset()         { *m = SetPersonalCertificationResp{} }
func (m *SetPersonalCertificationResp) String() string { return proto.CompactTextString(m) }
func (*SetPersonalCertificationResp) ProtoMessage()    {}
func (*SetPersonalCertificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{5}
}
func (m *SetPersonalCertificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPersonalCertificationResp.Unmarshal(m, b)
}
func (m *SetPersonalCertificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPersonalCertificationResp.Marshal(b, m, deterministic)
}
func (dst *SetPersonalCertificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPersonalCertificationResp.Merge(dst, src)
}
func (m *SetPersonalCertificationResp) XXX_Size() int {
	return xxx_messageInfo_SetPersonalCertificationResp.Size(m)
}
func (m *SetPersonalCertificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPersonalCertificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPersonalCertificationResp proto.InternalMessageInfo

func (m *SetPersonalCertificationResp) GetFailedUidList() []uint32 {
	if m != nil {
		return m.FailedUidList
	}
	return nil
}

type GetPersonalCertificationReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	CertType             uint32   `protobuf:"varint,4,opt,name=cert_type,json=certType,proto3" json:"cert_type,omitempty"`
	PresentPosition      uint32   `protobuf:"varint,5,opt,name=present_position,json=presentPosition,proto3" json:"present_position,omitempty"`
	Offset               uint32   `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	Valid                bool     `protobuf:"varint,8,opt,name=valid,proto3" json:"valid,omitempty"`
	NotUseCache          bool     `protobuf:"varint,9,opt,name=not_use_cache,json=notUseCache,proto3" json:"not_use_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPersonalCertificationReq) Reset()         { *m = GetPersonalCertificationReq{} }
func (m *GetPersonalCertificationReq) String() string { return proto.CompactTextString(m) }
func (*GetPersonalCertificationReq) ProtoMessage()    {}
func (*GetPersonalCertificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{6}
}
func (m *GetPersonalCertificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPersonalCertificationReq.Unmarshal(m, b)
}
func (m *GetPersonalCertificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPersonalCertificationReq.Marshal(b, m, deterministic)
}
func (dst *GetPersonalCertificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPersonalCertificationReq.Merge(dst, src)
}
func (m *GetPersonalCertificationReq) XXX_Size() int {
	return xxx_messageInfo_GetPersonalCertificationReq.Size(m)
}
func (m *GetPersonalCertificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPersonalCertificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPersonalCertificationReq proto.InternalMessageInfo

func (m *GetPersonalCertificationReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetPersonalCertificationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPersonalCertificationReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *GetPersonalCertificationReq) GetCertType() uint32 {
	if m != nil {
		return m.CertType
	}
	return 0
}

func (m *GetPersonalCertificationReq) GetPresentPosition() uint32 {
	if m != nil {
		return m.PresentPosition
	}
	return 0
}

func (m *GetPersonalCertificationReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPersonalCertificationReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetPersonalCertificationReq) GetValid() bool {
	if m != nil {
		return m.Valid
	}
	return false
}

func (m *GetPersonalCertificationReq) GetNotUseCache() bool {
	if m != nil {
		return m.NotUseCache
	}
	return false
}

type GetPersonalCertificationResp struct {
	InfoList             []*PersonalCertificationInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Total                uint32                       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetPersonalCertificationResp) Reset()         { *m = GetPersonalCertificationResp{} }
func (m *GetPersonalCertificationResp) String() string { return proto.CompactTextString(m) }
func (*GetPersonalCertificationResp) ProtoMessage()    {}
func (*GetPersonalCertificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{7}
}
func (m *GetPersonalCertificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPersonalCertificationResp.Unmarshal(m, b)
}
func (m *GetPersonalCertificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPersonalCertificationResp.Marshal(b, m, deterministic)
}
func (dst *GetPersonalCertificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPersonalCertificationResp.Merge(dst, src)
}
func (m *GetPersonalCertificationResp) XXX_Size() int {
	return xxx_messageInfo_GetPersonalCertificationResp.Size(m)
}
func (m *GetPersonalCertificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPersonalCertificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPersonalCertificationResp proto.InternalMessageInfo

func (m *GetPersonalCertificationResp) GetInfoList() []*PersonalCertificationInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetPersonalCertificationResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type StopPersonalCertificationReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopPersonalCertificationReq) Reset()         { *m = StopPersonalCertificationReq{} }
func (m *StopPersonalCertificationReq) String() string { return proto.CompactTextString(m) }
func (*StopPersonalCertificationReq) ProtoMessage()    {}
func (*StopPersonalCertificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{8}
}
func (m *StopPersonalCertificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopPersonalCertificationReq.Unmarshal(m, b)
}
func (m *StopPersonalCertificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopPersonalCertificationReq.Marshal(b, m, deterministic)
}
func (dst *StopPersonalCertificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopPersonalCertificationReq.Merge(dst, src)
}
func (m *StopPersonalCertificationReq) XXX_Size() int {
	return xxx_messageInfo_StopPersonalCertificationReq.Size(m)
}
func (m *StopPersonalCertificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopPersonalCertificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopPersonalCertificationReq proto.InternalMessageInfo

func (m *StopPersonalCertificationReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type StopPersonalCertificationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopPersonalCertificationResp) Reset()         { *m = StopPersonalCertificationResp{} }
func (m *StopPersonalCertificationResp) String() string { return proto.CompactTextString(m) }
func (*StopPersonalCertificationResp) ProtoMessage()    {}
func (*StopPersonalCertificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{9}
}
func (m *StopPersonalCertificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopPersonalCertificationResp.Unmarshal(m, b)
}
func (m *StopPersonalCertificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopPersonalCertificationResp.Marshal(b, m, deterministic)
}
func (dst *StopPersonalCertificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopPersonalCertificationResp.Merge(dst, src)
}
func (m *StopPersonalCertificationResp) XXX_Size() int {
	return xxx_messageInfo_StopPersonalCertificationResp.Size(m)
}
func (m *StopPersonalCertificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopPersonalCertificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopPersonalCertificationResp proto.InternalMessageInfo

type BatGetPersonalCertificationReq struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	PresentPosition      uint32   `protobuf:"varint,2,opt,name=present_position,json=presentPosition,proto3" json:"present_position,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetPersonalCertificationReq) Reset()         { *m = BatGetPersonalCertificationReq{} }
func (m *BatGetPersonalCertificationReq) String() string { return proto.CompactTextString(m) }
func (*BatGetPersonalCertificationReq) ProtoMessage()    {}
func (*BatGetPersonalCertificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{10}
}
func (m *BatGetPersonalCertificationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetPersonalCertificationReq.Unmarshal(m, b)
}
func (m *BatGetPersonalCertificationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetPersonalCertificationReq.Marshal(b, m, deterministic)
}
func (dst *BatGetPersonalCertificationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetPersonalCertificationReq.Merge(dst, src)
}
func (m *BatGetPersonalCertificationReq) XXX_Size() int {
	return xxx_messageInfo_BatGetPersonalCertificationReq.Size(m)
}
func (m *BatGetPersonalCertificationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetPersonalCertificationReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetPersonalCertificationReq proto.InternalMessageInfo

func (m *BatGetPersonalCertificationReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *BatGetPersonalCertificationReq) GetPresentPosition() uint32 {
	if m != nil {
		return m.PresentPosition
	}
	return 0
}

type PersonalCertificationInfoList struct {
	InfoList             []*PersonalCertificationInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *PersonalCertificationInfoList) Reset()         { *m = PersonalCertificationInfoList{} }
func (m *PersonalCertificationInfoList) String() string { return proto.CompactTextString(m) }
func (*PersonalCertificationInfoList) ProtoMessage()    {}
func (*PersonalCertificationInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{11}
}
func (m *PersonalCertificationInfoList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PersonalCertificationInfoList.Unmarshal(m, b)
}
func (m *PersonalCertificationInfoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PersonalCertificationInfoList.Marshal(b, m, deterministic)
}
func (dst *PersonalCertificationInfoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PersonalCertificationInfoList.Merge(dst, src)
}
func (m *PersonalCertificationInfoList) XXX_Size() int {
	return xxx_messageInfo_PersonalCertificationInfoList.Size(m)
}
func (m *PersonalCertificationInfoList) XXX_DiscardUnknown() {
	xxx_messageInfo_PersonalCertificationInfoList.DiscardUnknown(m)
}

var xxx_messageInfo_PersonalCertificationInfoList proto.InternalMessageInfo

func (m *PersonalCertificationInfoList) GetInfoList() []*PersonalCertificationInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BatGetPersonalCertificationResp struct {
	UserCertMap          map[uint32]*PersonalCertificationInfoList `protobuf:"bytes,1,rep,name=user_cert_map,json=userCertMap,proto3" json:"user_cert_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *BatGetPersonalCertificationResp) Reset()         { *m = BatGetPersonalCertificationResp{} }
func (m *BatGetPersonalCertificationResp) String() string { return proto.CompactTextString(m) }
func (*BatGetPersonalCertificationResp) ProtoMessage()    {}
func (*BatGetPersonalCertificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{12}
}
func (m *BatGetPersonalCertificationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetPersonalCertificationResp.Unmarshal(m, b)
}
func (m *BatGetPersonalCertificationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetPersonalCertificationResp.Marshal(b, m, deterministic)
}
func (dst *BatGetPersonalCertificationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetPersonalCertificationResp.Merge(dst, src)
}
func (m *BatGetPersonalCertificationResp) XXX_Size() int {
	return xxx_messageInfo_BatGetPersonalCertificationResp.Size(m)
}
func (m *BatGetPersonalCertificationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetPersonalCertificationResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetPersonalCertificationResp proto.InternalMessageInfo

func (m *BatGetPersonalCertificationResp) GetUserCertMap() map[uint32]*PersonalCertificationInfoList {
	if m != nil {
		return m.UserCertMap
	}
	return nil
}

// --------------------- 新版本 类型+给用户发标 ---------------------
type CertTypeInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Color                []string `protobuf:"bytes,4,rep,name=color,proto3" json:"color,omitempty"`
	TextShadowColor      string   `protobuf:"bytes,5,opt,name=text_shadow_color,json=textShadowColor,proto3" json:"text_shadow_color,omitempty"`
	PresentPositionList  []uint32 `protobuf:"varint,6,rep,packed,name=present_position_list,json=presentPositionList,proto3" json:"present_position_list,omitempty"`
	Operator             string   `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Introduce            string   `protobuf:"bytes,9,opt,name=introduce,proto3" json:"introduce,omitempty"`
	JumpUrl              string   `protobuf:"bytes,10,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CertTypeInfo) Reset()         { *m = CertTypeInfo{} }
func (m *CertTypeInfo) String() string { return proto.CompactTextString(m) }
func (*CertTypeInfo) ProtoMessage()    {}
func (*CertTypeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{13}
}
func (m *CertTypeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CertTypeInfo.Unmarshal(m, b)
}
func (m *CertTypeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CertTypeInfo.Marshal(b, m, deterministic)
}
func (dst *CertTypeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CertTypeInfo.Merge(dst, src)
}
func (m *CertTypeInfo) XXX_Size() int {
	return xxx_messageInfo_CertTypeInfo.Size(m)
}
func (m *CertTypeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CertTypeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CertTypeInfo proto.InternalMessageInfo

func (m *CertTypeInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CertTypeInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CertTypeInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *CertTypeInfo) GetColor() []string {
	if m != nil {
		return m.Color
	}
	return nil
}

func (m *CertTypeInfo) GetTextShadowColor() string {
	if m != nil {
		return m.TextShadowColor
	}
	return ""
}

func (m *CertTypeInfo) GetPresentPositionList() []uint32 {
	if m != nil {
		return m.PresentPositionList
	}
	return nil
}

func (m *CertTypeInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CertTypeInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *CertTypeInfo) GetIntroduce() string {
	if m != nil {
		return m.Introduce
	}
	return ""
}

func (m *CertTypeInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type SetCertTypeReq struct {
	Info                 *CertTypeInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetCertTypeReq) Reset()         { *m = SetCertTypeReq{} }
func (m *SetCertTypeReq) String() string { return proto.CompactTextString(m) }
func (*SetCertTypeReq) ProtoMessage()    {}
func (*SetCertTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{14}
}
func (m *SetCertTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCertTypeReq.Unmarshal(m, b)
}
func (m *SetCertTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCertTypeReq.Marshal(b, m, deterministic)
}
func (dst *SetCertTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCertTypeReq.Merge(dst, src)
}
func (m *SetCertTypeReq) XXX_Size() int {
	return xxx_messageInfo_SetCertTypeReq.Size(m)
}
func (m *SetCertTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCertTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCertTypeReq proto.InternalMessageInfo

func (m *SetCertTypeReq) GetInfo() *CertTypeInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetCertTypeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCertTypeResp) Reset()         { *m = SetCertTypeResp{} }
func (m *SetCertTypeResp) String() string { return proto.CompactTextString(m) }
func (*SetCertTypeResp) ProtoMessage()    {}
func (*SetCertTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{15}
}
func (m *SetCertTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCertTypeResp.Unmarshal(m, b)
}
func (m *SetCertTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCertTypeResp.Marshal(b, m, deterministic)
}
func (dst *SetCertTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCertTypeResp.Merge(dst, src)
}
func (m *SetCertTypeResp) XXX_Size() int {
	return xxx_messageInfo_SetCertTypeResp.Size(m)
}
func (m *SetCertTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCertTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCertTypeResp proto.InternalMessageInfo

type DelCertTypeReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCertTypeReq) Reset()         { *m = DelCertTypeReq{} }
func (m *DelCertTypeReq) String() string { return proto.CompactTextString(m) }
func (*DelCertTypeReq) ProtoMessage()    {}
func (*DelCertTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{16}
}
func (m *DelCertTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCertTypeReq.Unmarshal(m, b)
}
func (m *DelCertTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCertTypeReq.Marshal(b, m, deterministic)
}
func (dst *DelCertTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCertTypeReq.Merge(dst, src)
}
func (m *DelCertTypeReq) XXX_Size() int {
	return xxx_messageInfo_DelCertTypeReq.Size(m)
}
func (m *DelCertTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCertTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelCertTypeReq proto.InternalMessageInfo

func (m *DelCertTypeReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelCertTypeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCertTypeResp) Reset()         { *m = DelCertTypeResp{} }
func (m *DelCertTypeResp) String() string { return proto.CompactTextString(m) }
func (*DelCertTypeResp) ProtoMessage()    {}
func (*DelCertTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{17}
}
func (m *DelCertTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCertTypeResp.Unmarshal(m, b)
}
func (m *DelCertTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCertTypeResp.Marshal(b, m, deterministic)
}
func (dst *DelCertTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCertTypeResp.Merge(dst, src)
}
func (m *DelCertTypeResp) XXX_Size() int {
	return xxx_messageInfo_DelCertTypeResp.Size(m)
}
func (m *DelCertTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCertTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelCertTypeResp proto.InternalMessageInfo

type GetCertTypeReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCertTypeReq) Reset()         { *m = GetCertTypeReq{} }
func (m *GetCertTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetCertTypeReq) ProtoMessage()    {}
func (*GetCertTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{18}
}
func (m *GetCertTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCertTypeReq.Unmarshal(m, b)
}
func (m *GetCertTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCertTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetCertTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCertTypeReq.Merge(dst, src)
}
func (m *GetCertTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetCertTypeReq.Size(m)
}
func (m *GetCertTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCertTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCertTypeReq proto.InternalMessageInfo

func (m *GetCertTypeReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetCertTypeReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCertTypeReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetCertTypeResp struct {
	CertList             []*CertTypeInfo `protobuf:"bytes,1,rep,name=cert_list,json=certList,proto3" json:"cert_list,omitempty"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCertTypeResp) Reset()         { *m = GetCertTypeResp{} }
func (m *GetCertTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetCertTypeResp) ProtoMessage()    {}
func (*GetCertTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{19}
}
func (m *GetCertTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCertTypeResp.Unmarshal(m, b)
}
func (m *GetCertTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCertTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetCertTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCertTypeResp.Merge(dst, src)
}
func (m *GetCertTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetCertTypeResp.Size(m)
}
func (m *GetCertTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCertTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCertTypeResp proto.InternalMessageInfo

func (m *GetCertTypeResp) GetCertList() []*CertTypeInfo {
	if m != nil {
		return m.CertList
	}
	return nil
}

func (m *GetCertTypeResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 模糊搜索
type GetCertTypeFuzzySearchReq struct {
	Str                  string   `protobuf:"bytes,1,opt,name=str,proto3" json:"str,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCertTypeFuzzySearchReq) Reset()         { *m = GetCertTypeFuzzySearchReq{} }
func (m *GetCertTypeFuzzySearchReq) String() string { return proto.CompactTextString(m) }
func (*GetCertTypeFuzzySearchReq) ProtoMessage()    {}
func (*GetCertTypeFuzzySearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{20}
}
func (m *GetCertTypeFuzzySearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCertTypeFuzzySearchReq.Unmarshal(m, b)
}
func (m *GetCertTypeFuzzySearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCertTypeFuzzySearchReq.Marshal(b, m, deterministic)
}
func (dst *GetCertTypeFuzzySearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCertTypeFuzzySearchReq.Merge(dst, src)
}
func (m *GetCertTypeFuzzySearchReq) XXX_Size() int {
	return xxx_messageInfo_GetCertTypeFuzzySearchReq.Size(m)
}
func (m *GetCertTypeFuzzySearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCertTypeFuzzySearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCertTypeFuzzySearchReq proto.InternalMessageInfo

func (m *GetCertTypeFuzzySearchReq) GetStr() string {
	if m != nil {
		return m.Str
	}
	return ""
}

type CertTypeIdInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CertTypeIdInfo) Reset()         { *m = CertTypeIdInfo{} }
func (m *CertTypeIdInfo) String() string { return proto.CompactTextString(m) }
func (*CertTypeIdInfo) ProtoMessage()    {}
func (*CertTypeIdInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{21}
}
func (m *CertTypeIdInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CertTypeIdInfo.Unmarshal(m, b)
}
func (m *CertTypeIdInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CertTypeIdInfo.Marshal(b, m, deterministic)
}
func (dst *CertTypeIdInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CertTypeIdInfo.Merge(dst, src)
}
func (m *CertTypeIdInfo) XXX_Size() int {
	return xxx_messageInfo_CertTypeIdInfo.Size(m)
}
func (m *CertTypeIdInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CertTypeIdInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CertTypeIdInfo proto.InternalMessageInfo

func (m *CertTypeIdInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CertTypeIdInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type GetCertTypeFuzzySearchResp struct {
	CertIdNameList       []*CertTypeIdInfo `protobuf:"bytes,1,rep,name=cert_id_name_list,json=certIdNameList,proto3" json:"cert_id_name_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetCertTypeFuzzySearchResp) Reset()         { *m = GetCertTypeFuzzySearchResp{} }
func (m *GetCertTypeFuzzySearchResp) String() string { return proto.CompactTextString(m) }
func (*GetCertTypeFuzzySearchResp) ProtoMessage()    {}
func (*GetCertTypeFuzzySearchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{22}
}
func (m *GetCertTypeFuzzySearchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCertTypeFuzzySearchResp.Unmarshal(m, b)
}
func (m *GetCertTypeFuzzySearchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCertTypeFuzzySearchResp.Marshal(b, m, deterministic)
}
func (dst *GetCertTypeFuzzySearchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCertTypeFuzzySearchResp.Merge(dst, src)
}
func (m *GetCertTypeFuzzySearchResp) XXX_Size() int {
	return xxx_messageInfo_GetCertTypeFuzzySearchResp.Size(m)
}
func (m *GetCertTypeFuzzySearchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCertTypeFuzzySearchResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCertTypeFuzzySearchResp proto.InternalMessageInfo

func (m *GetCertTypeFuzzySearchResp) GetCertIdNameList() []*CertTypeIdInfo {
	if m != nil {
		return m.CertIdNameList
	}
	return nil
}

type UserCertInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	CertTypeId           string   `protobuf:"bytes,3,opt,name=cert_type_id,json=certTypeId,proto3" json:"cert_type_id,omitempty"`
	CertTypeName         string   `protobuf:"bytes,4,opt,name=cert_type_name,json=certTypeName,proto3" json:"cert_type_name,omitempty"`
	Text                 string   `protobuf:"bytes,5,opt,name=text,proto3" json:"text,omitempty"`
	ShortText            string   `protobuf:"bytes,6,opt,name=short_text,json=shortText,proto3" json:"short_text,omitempty"`
	BeginTime            uint32   `protobuf:"varint,7,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Status               uint32   `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Operator             string   `protobuf:"bytes,11,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserCertInfo) Reset()         { *m = UserCertInfo{} }
func (m *UserCertInfo) String() string { return proto.CompactTextString(m) }
func (*UserCertInfo) ProtoMessage()    {}
func (*UserCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{23}
}
func (m *UserCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCertInfo.Unmarshal(m, b)
}
func (m *UserCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCertInfo.Marshal(b, m, deterministic)
}
func (dst *UserCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCertInfo.Merge(dst, src)
}
func (m *UserCertInfo) XXX_Size() int {
	return xxx_messageInfo_UserCertInfo.Size(m)
}
func (m *UserCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserCertInfo proto.InternalMessageInfo

func (m *UserCertInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UserCertInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserCertInfo) GetCertTypeId() string {
	if m != nil {
		return m.CertTypeId
	}
	return ""
}

func (m *UserCertInfo) GetCertTypeName() string {
	if m != nil {
		return m.CertTypeName
	}
	return ""
}

func (m *UserCertInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *UserCertInfo) GetShortText() string {
	if m != nil {
		return m.ShortText
	}
	return ""
}

func (m *UserCertInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *UserCertInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *UserCertInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UserCertInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *UserCertInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SetUserCertReq struct {
	CertList             []*UserCertInfo `protobuf:"bytes,1,rep,name=cert_list,json=certList,proto3" json:"cert_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetUserCertReq) Reset()         { *m = SetUserCertReq{} }
func (m *SetUserCertReq) String() string { return proto.CompactTextString(m) }
func (*SetUserCertReq) ProtoMessage()    {}
func (*SetUserCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{24}
}
func (m *SetUserCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserCertReq.Unmarshal(m, b)
}
func (m *SetUserCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserCertReq.Marshal(b, m, deterministic)
}
func (dst *SetUserCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserCertReq.Merge(dst, src)
}
func (m *SetUserCertReq) XXX_Size() int {
	return xxx_messageInfo_SetUserCertReq.Size(m)
}
func (m *SetUserCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserCertReq proto.InternalMessageInfo

func (m *SetUserCertReq) GetCertList() []*UserCertInfo {
	if m != nil {
		return m.CertList
	}
	return nil
}

type SetUserCertResp struct {
	InvalidCertTypeList  []string `protobuf:"bytes,1,rep,name=invalid_cert_type_list,json=invalidCertTypeList,proto3" json:"invalid_cert_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserCertResp) Reset()         { *m = SetUserCertResp{} }
func (m *SetUserCertResp) String() string { return proto.CompactTextString(m) }
func (*SetUserCertResp) ProtoMessage()    {}
func (*SetUserCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{25}
}
func (m *SetUserCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserCertResp.Unmarshal(m, b)
}
func (m *SetUserCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserCertResp.Marshal(b, m, deterministic)
}
func (dst *SetUserCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserCertResp.Merge(dst, src)
}
func (m *SetUserCertResp) XXX_Size() int {
	return xxx_messageInfo_SetUserCertResp.Size(m)
}
func (m *SetUserCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserCertResp proto.InternalMessageInfo

func (m *SetUserCertResp) GetInvalidCertTypeList() []string {
	if m != nil {
		return m.InvalidCertTypeList
	}
	return nil
}

// 运营后台 无缓存
type GetUserCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CertTypeId           string   `protobuf:"bytes,2,opt,name=cert_type_id,json=certTypeId,proto3" json:"cert_type_id,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	Offset               uint32   `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCertReq) Reset()         { *m = GetUserCertReq{} }
func (m *GetUserCertReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCertReq) ProtoMessage()    {}
func (*GetUserCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{26}
}
func (m *GetUserCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCertReq.Unmarshal(m, b)
}
func (m *GetUserCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCertReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCertReq.Merge(dst, src)
}
func (m *GetUserCertReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCertReq.Size(m)
}
func (m *GetUserCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCertReq proto.InternalMessageInfo

func (m *GetUserCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCertReq) GetCertTypeId() string {
	if m != nil {
		return m.CertTypeId
	}
	return ""
}

func (m *GetUserCertReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GetUserCertReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *GetUserCertReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetUserCertReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetUserCertReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetUserCertResp struct {
	CertList             []*UserCertInfo `protobuf:"bytes,1,rep,name=cert_list,json=certList,proto3" json:"cert_list,omitempty"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserCertResp) Reset()         { *m = GetUserCertResp{} }
func (m *GetUserCertResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCertResp) ProtoMessage()    {}
func (*GetUserCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{27}
}
func (m *GetUserCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCertResp.Unmarshal(m, b)
}
func (m *GetUserCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCertResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCertResp.Merge(dst, src)
}
func (m *GetUserCertResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCertResp.Size(m)
}
func (m *GetUserCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCertResp proto.InternalMessageInfo

func (m *GetUserCertResp) GetCertList() []*UserCertInfo {
	if m != nil {
		return m.CertList
	}
	return nil
}

func (m *GetUserCertResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DelUserCertByIDReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	CertTypeId           string   `protobuf:"bytes,3,opt,name=cert_type_id,json=certTypeId,proto3" json:"cert_type_id,omitempty"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserCertByIDReq) Reset()         { *m = DelUserCertByIDReq{} }
func (m *DelUserCertByIDReq) String() string { return proto.CompactTextString(m) }
func (*DelUserCertByIDReq) ProtoMessage()    {}
func (*DelUserCertByIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{28}
}
func (m *DelUserCertByIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserCertByIDReq.Unmarshal(m, b)
}
func (m *DelUserCertByIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserCertByIDReq.Marshal(b, m, deterministic)
}
func (dst *DelUserCertByIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserCertByIDReq.Merge(dst, src)
}
func (m *DelUserCertByIDReq) XXX_Size() int {
	return xxx_messageInfo_DelUserCertByIDReq.Size(m)
}
func (m *DelUserCertByIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserCertByIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserCertByIDReq proto.InternalMessageInfo

func (m *DelUserCertByIDReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *DelUserCertByIDReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelUserCertByIDReq) GetCertTypeId() string {
	if m != nil {
		return m.CertTypeId
	}
	return ""
}

func (m *DelUserCertByIDReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type DelUserCertByIDResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserCertByIDResp) Reset()         { *m = DelUserCertByIDResp{} }
func (m *DelUserCertByIDResp) String() string { return proto.CompactTextString(m) }
func (*DelUserCertByIDResp) ProtoMessage()    {}
func (*DelUserCertByIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{29}
}
func (m *DelUserCertByIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserCertByIDResp.Unmarshal(m, b)
}
func (m *DelUserCertByIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserCertByIDResp.Marshal(b, m, deterministic)
}
func (dst *DelUserCertByIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserCertByIDResp.Merge(dst, src)
}
func (m *DelUserCertByIDResp) XXX_Size() int {
	return xxx_messageInfo_DelUserCertByIDResp.Size(m)
}
func (m *DelUserCertByIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserCertByIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserCertByIDResp proto.InternalMessageInfo

type DelUserCertInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CertTypeId           string   `protobuf:"bytes,2,opt,name=cert_type_id,json=certTypeId,proto3" json:"cert_type_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserCertInfo) Reset()         { *m = DelUserCertInfo{} }
func (m *DelUserCertInfo) String() string { return proto.CompactTextString(m) }
func (*DelUserCertInfo) ProtoMessage()    {}
func (*DelUserCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{30}
}
func (m *DelUserCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserCertInfo.Unmarshal(m, b)
}
func (m *DelUserCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserCertInfo.Marshal(b, m, deterministic)
}
func (dst *DelUserCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserCertInfo.Merge(dst, src)
}
func (m *DelUserCertInfo) XXX_Size() int {
	return xxx_messageInfo_DelUserCertInfo.Size(m)
}
func (m *DelUserCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserCertInfo proto.InternalMessageInfo

func (m *DelUserCertInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelUserCertInfo) GetCertTypeId() string {
	if m != nil {
		return m.CertTypeId
	}
	return ""
}

type BatDelUserCertReq struct {
	DelInfo              []*DelUserCertInfo `protobuf:"bytes,1,rep,name=del_info,json=delInfo,proto3" json:"del_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatDelUserCertReq) Reset()         { *m = BatDelUserCertReq{} }
func (m *BatDelUserCertReq) String() string { return proto.CompactTextString(m) }
func (*BatDelUserCertReq) ProtoMessage()    {}
func (*BatDelUserCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{31}
}
func (m *BatDelUserCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelUserCertReq.Unmarshal(m, b)
}
func (m *BatDelUserCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelUserCertReq.Marshal(b, m, deterministic)
}
func (dst *BatDelUserCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelUserCertReq.Merge(dst, src)
}
func (m *BatDelUserCertReq) XXX_Size() int {
	return xxx_messageInfo_BatDelUserCertReq.Size(m)
}
func (m *BatDelUserCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelUserCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelUserCertReq proto.InternalMessageInfo

func (m *BatDelUserCertReq) GetDelInfo() []*DelUserCertInfo {
	if m != nil {
		return m.DelInfo
	}
	return nil
}

type BatDelUserCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelUserCertResp) Reset()         { *m = BatDelUserCertResp{} }
func (m *BatDelUserCertResp) String() string { return proto.CompactTextString(m) }
func (*BatDelUserCertResp) ProtoMessage()    {}
func (*BatDelUserCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{32}
}
func (m *BatDelUserCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelUserCertResp.Unmarshal(m, b)
}
func (m *BatDelUserCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelUserCertResp.Marshal(b, m, deterministic)
}
func (dst *BatDelUserCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelUserCertResp.Merge(dst, src)
}
func (m *BatDelUserCertResp) XXX_Size() int {
	return xxx_messageInfo_BatDelUserCertResp.Size(m)
}
func (m *BatDelUserCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelUserCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelUserCertResp proto.InternalMessageInfo

// 提供客户端的接口 生效中
type ValidUserCertTypeInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Text                 string   `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	ShortText            string   `protobuf:"bytes,5,opt,name=short_text,json=shortText,proto3" json:"short_text,omitempty"`
	Color                []string `protobuf:"bytes,6,rep,name=color,proto3" json:"color,omitempty"`
	TextShadowColor      string   `protobuf:"bytes,7,opt,name=text_shadow_color,json=textShadowColor,proto3" json:"text_shadow_color,omitempty"`
	PresentPositionList  []uint32 `protobuf:"varint,8,rep,packed,name=present_position_list,json=presentPositionList,proto3" json:"present_position_list,omitempty"`
	BeginTime            uint32   `protobuf:"varint,9,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Introduce            string   `protobuf:"bytes,11,opt,name=introduce,proto3" json:"introduce,omitempty"`
	JumpUrl              string   `protobuf:"bytes,12,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	CertTypeName         string   `protobuf:"bytes,13,opt,name=cert_type_name,json=certTypeName,proto3" json:"cert_type_name,omitempty"`
	PriorityDisplayBit   int32    `protobuf:"varint,14,opt,name=priority_display_bit,json=priorityDisplayBit,proto3" json:"priority_display_bit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ValidUserCertTypeInfo) Reset()         { *m = ValidUserCertTypeInfo{} }
func (m *ValidUserCertTypeInfo) String() string { return proto.CompactTextString(m) }
func (*ValidUserCertTypeInfo) ProtoMessage()    {}
func (*ValidUserCertTypeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{33}
}
func (m *ValidUserCertTypeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidUserCertTypeInfo.Unmarshal(m, b)
}
func (m *ValidUserCertTypeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidUserCertTypeInfo.Marshal(b, m, deterministic)
}
func (dst *ValidUserCertTypeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidUserCertTypeInfo.Merge(dst, src)
}
func (m *ValidUserCertTypeInfo) XXX_Size() int {
	return xxx_messageInfo_ValidUserCertTypeInfo.Size(m)
}
func (m *ValidUserCertTypeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidUserCertTypeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ValidUserCertTypeInfo proto.InternalMessageInfo

func (m *ValidUserCertTypeInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ValidUserCertTypeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ValidUserCertTypeInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ValidUserCertTypeInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ValidUserCertTypeInfo) GetShortText() string {
	if m != nil {
		return m.ShortText
	}
	return ""
}

func (m *ValidUserCertTypeInfo) GetColor() []string {
	if m != nil {
		return m.Color
	}
	return nil
}

func (m *ValidUserCertTypeInfo) GetTextShadowColor() string {
	if m != nil {
		return m.TextShadowColor
	}
	return ""
}

func (m *ValidUserCertTypeInfo) GetPresentPositionList() []uint32 {
	if m != nil {
		return m.PresentPositionList
	}
	return nil
}

func (m *ValidUserCertTypeInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ValidUserCertTypeInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ValidUserCertTypeInfo) GetIntroduce() string {
	if m != nil {
		return m.Introduce
	}
	return ""
}

func (m *ValidUserCertTypeInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *ValidUserCertTypeInfo) GetCertTypeName() string {
	if m != nil {
		return m.CertTypeName
	}
	return ""
}

func (m *ValidUserCertTypeInfo) GetPriorityDisplayBit() int32 {
	if m != nil {
		return m.PriorityDisplayBit
	}
	return 0
}

type GetUserCertByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PresentPosition      uint32   `protobuf:"varint,5,opt,name=present_position,json=presentPosition,proto3" json:"present_position,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCertByUidReq) Reset()         { *m = GetUserCertByUidReq{} }
func (m *GetUserCertByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCertByUidReq) ProtoMessage()    {}
func (*GetUserCertByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{34}
}
func (m *GetUserCertByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCertByUidReq.Unmarshal(m, b)
}
func (m *GetUserCertByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCertByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCertByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCertByUidReq.Merge(dst, src)
}
func (m *GetUserCertByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCertByUidReq.Size(m)
}
func (m *GetUserCertByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCertByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCertByUidReq proto.InternalMessageInfo

func (m *GetUserCertByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCertByUidReq) GetPresentPosition() uint32 {
	if m != nil {
		return m.PresentPosition
	}
	return 0
}

type GetUserCertByUidResp struct {
	Cert                 []*ValidUserCertTypeInfo `protobuf:"bytes,1,rep,name=cert,proto3" json:"cert,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetUserCertByUidResp) Reset()         { *m = GetUserCertByUidResp{} }
func (m *GetUserCertByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCertByUidResp) ProtoMessage()    {}
func (*GetUserCertByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{35}
}
func (m *GetUserCertByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCertByUidResp.Unmarshal(m, b)
}
func (m *GetUserCertByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCertByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCertByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCertByUidResp.Merge(dst, src)
}
func (m *GetUserCertByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCertByUidResp.Size(m)
}
func (m *GetUserCertByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCertByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCertByUidResp proto.InternalMessageInfo

func (m *GetUserCertByUidResp) GetCert() []*ValidUserCertTypeInfo {
	if m != nil {
		return m.Cert
	}
	return nil
}

// 设置优先展示的认证标
type SetPriorityDisplayCertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CertId               string   `protobuf:"bytes,2,opt,name=cert_id,json=certId,proto3" json:"cert_id,omitempty"`
	PresentPosition      uint32   `protobuf:"varint,3,opt,name=present_position,json=presentPosition,proto3" json:"present_position,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPriorityDisplayCertReq) Reset()         { *m = SetPriorityDisplayCertReq{} }
func (m *SetPriorityDisplayCertReq) String() string { return proto.CompactTextString(m) }
func (*SetPriorityDisplayCertReq) ProtoMessage()    {}
func (*SetPriorityDisplayCertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{36}
}
func (m *SetPriorityDisplayCertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPriorityDisplayCertReq.Unmarshal(m, b)
}
func (m *SetPriorityDisplayCertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPriorityDisplayCertReq.Marshal(b, m, deterministic)
}
func (dst *SetPriorityDisplayCertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPriorityDisplayCertReq.Merge(dst, src)
}
func (m *SetPriorityDisplayCertReq) XXX_Size() int {
	return xxx_messageInfo_SetPriorityDisplayCertReq.Size(m)
}
func (m *SetPriorityDisplayCertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPriorityDisplayCertReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPriorityDisplayCertReq proto.InternalMessageInfo

func (m *SetPriorityDisplayCertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetPriorityDisplayCertReq) GetCertId() string {
	if m != nil {
		return m.CertId
	}
	return ""
}

func (m *SetPriorityDisplayCertReq) GetPresentPosition() uint32 {
	if m != nil {
		return m.PresentPosition
	}
	return 0
}

func (m *SetPriorityDisplayCertReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SetPriorityDisplayCertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPriorityDisplayCertResp) Reset()         { *m = SetPriorityDisplayCertResp{} }
func (m *SetPriorityDisplayCertResp) String() string { return proto.CompactTextString(m) }
func (*SetPriorityDisplayCertResp) ProtoMessage()    {}
func (*SetPriorityDisplayCertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_personal_certification_ba42dfee555ece6b, []int{37}
}
func (m *SetPriorityDisplayCertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPriorityDisplayCertResp.Unmarshal(m, b)
}
func (m *SetPriorityDisplayCertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPriorityDisplayCertResp.Marshal(b, m, deterministic)
}
func (dst *SetPriorityDisplayCertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPriorityDisplayCertResp.Merge(dst, src)
}
func (m *SetPriorityDisplayCertResp) XXX_Size() int {
	return xxx_messageInfo_SetPriorityDisplayCertResp.Size(m)
}
func (m *SetPriorityDisplayCertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPriorityDisplayCertResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPriorityDisplayCertResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetCertTypeByIdReq)(nil), "personalcertification.GetCertTypeByIdReq")
	proto.RegisterType((*GetCertTypeByIdResp)(nil), "personalcertification.GetCertTypeByIdResp")
	proto.RegisterType((*PersonalCertificationCommon)(nil), "personalcertification.PersonalCertificationCommon")
	proto.RegisterType((*PersonalCertificationInfo)(nil), "personalcertification.PersonalCertificationInfo")
	proto.RegisterType((*SetPersonalCertificationReq)(nil), "personalcertification.SetPersonalCertificationReq")
	proto.RegisterType((*SetPersonalCertificationResp)(nil), "personalcertification.SetPersonalCertificationResp")
	proto.RegisterType((*GetPersonalCertificationReq)(nil), "personalcertification.GetPersonalCertificationReq")
	proto.RegisterType((*GetPersonalCertificationResp)(nil), "personalcertification.GetPersonalCertificationResp")
	proto.RegisterType((*StopPersonalCertificationReq)(nil), "personalcertification.StopPersonalCertificationReq")
	proto.RegisterType((*StopPersonalCertificationResp)(nil), "personalcertification.StopPersonalCertificationResp")
	proto.RegisterType((*BatGetPersonalCertificationReq)(nil), "personalcertification.BatGetPersonalCertificationReq")
	proto.RegisterType((*PersonalCertificationInfoList)(nil), "personalcertification.PersonalCertificationInfoList")
	proto.RegisterType((*BatGetPersonalCertificationResp)(nil), "personalcertification.BatGetPersonalCertificationResp")
	proto.RegisterMapType((map[uint32]*PersonalCertificationInfoList)(nil), "personalcertification.BatGetPersonalCertificationResp.UserCertMapEntry")
	proto.RegisterType((*CertTypeInfo)(nil), "personalcertification.CertTypeInfo")
	proto.RegisterType((*SetCertTypeReq)(nil), "personalcertification.SetCertTypeReq")
	proto.RegisterType((*SetCertTypeResp)(nil), "personalcertification.SetCertTypeResp")
	proto.RegisterType((*DelCertTypeReq)(nil), "personalcertification.DelCertTypeReq")
	proto.RegisterType((*DelCertTypeResp)(nil), "personalcertification.DelCertTypeResp")
	proto.RegisterType((*GetCertTypeReq)(nil), "personalcertification.GetCertTypeReq")
	proto.RegisterType((*GetCertTypeResp)(nil), "personalcertification.GetCertTypeResp")
	proto.RegisterType((*GetCertTypeFuzzySearchReq)(nil), "personalcertification.GetCertTypeFuzzySearchReq")
	proto.RegisterType((*CertTypeIdInfo)(nil), "personalcertification.CertTypeIdInfo")
	proto.RegisterType((*GetCertTypeFuzzySearchResp)(nil), "personalcertification.GetCertTypeFuzzySearchResp")
	proto.RegisterType((*UserCertInfo)(nil), "personalcertification.UserCertInfo")
	proto.RegisterType((*SetUserCertReq)(nil), "personalcertification.SetUserCertReq")
	proto.RegisterType((*SetUserCertResp)(nil), "personalcertification.SetUserCertResp")
	proto.RegisterType((*GetUserCertReq)(nil), "personalcertification.GetUserCertReq")
	proto.RegisterType((*GetUserCertResp)(nil), "personalcertification.GetUserCertResp")
	proto.RegisterType((*DelUserCertByIDReq)(nil), "personalcertification.DelUserCertByIDReq")
	proto.RegisterType((*DelUserCertByIDResp)(nil), "personalcertification.DelUserCertByIDResp")
	proto.RegisterType((*DelUserCertInfo)(nil), "personalcertification.DelUserCertInfo")
	proto.RegisterType((*BatDelUserCertReq)(nil), "personalcertification.BatDelUserCertReq")
	proto.RegisterType((*BatDelUserCertResp)(nil), "personalcertification.BatDelUserCertResp")
	proto.RegisterType((*ValidUserCertTypeInfo)(nil), "personalcertification.ValidUserCertTypeInfo")
	proto.RegisterType((*GetUserCertByUidReq)(nil), "personalcertification.GetUserCertByUidReq")
	proto.RegisterType((*GetUserCertByUidResp)(nil), "personalcertification.GetUserCertByUidResp")
	proto.RegisterType((*SetPriorityDisplayCertReq)(nil), "personalcertification.SetPriorityDisplayCertReq")
	proto.RegisterType((*SetPriorityDisplayCertResp)(nil), "personalcertification.SetPriorityDisplayCertResp")
	proto.RegisterEnum("personalcertification.CertType", CertType_name, CertType_value)
	proto.RegisterEnum("personalcertification.PresentPosition", PresentPosition_name, PresentPosition_value)
	proto.RegisterEnum("personalcertification.CertStatusType", CertStatusType_name, CertStatusType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PersonalCertificationClient is the client API for PersonalCertification service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PersonalCertificationClient interface {
	SetPersonalCertification(ctx context.Context, in *SetPersonalCertificationReq, opts ...grpc.CallOption) (*SetPersonalCertificationResp, error)
	GetPersonalCertification(ctx context.Context, in *GetPersonalCertificationReq, opts ...grpc.CallOption) (*GetPersonalCertificationResp, error)
	BatGetPersonalCertification(ctx context.Context, in *BatGetPersonalCertificationReq, opts ...grpc.CallOption) (*BatGetPersonalCertificationResp, error)
	StopPersonalCertification(ctx context.Context, in *StopPersonalCertificationReq, opts ...grpc.CallOption) (*StopPersonalCertificationResp, error)
	SetCertType(ctx context.Context, in *SetCertTypeReq, opts ...grpc.CallOption) (*SetCertTypeResp, error)
	DelCertType(ctx context.Context, in *DelCertTypeReq, opts ...grpc.CallOption) (*DelCertTypeResp, error)
	GetCertType(ctx context.Context, in *GetCertTypeReq, opts ...grpc.CallOption) (*GetCertTypeResp, error)
	GetCertTypeById(ctx context.Context, in *GetCertTypeByIdReq, opts ...grpc.CallOption) (*GetCertTypeByIdResp, error)
	// 模糊搜索
	GetCertTypeFuzzySearch(ctx context.Context, in *GetCertTypeFuzzySearchReq, opts ...grpc.CallOption) (*GetCertTypeFuzzySearchResp, error)
	SetUserCert(ctx context.Context, in *SetUserCertReq, opts ...grpc.CallOption) (*SetUserCertResp, error)
	GetUserCert(ctx context.Context, in *GetUserCertReq, opts ...grpc.CallOption) (*GetUserCertResp, error)
	DelUserCertByID(ctx context.Context, in *DelUserCertByIDReq, opts ...grpc.CallOption) (*DelUserCertByIDResp, error)
	BatDelUserCert(ctx context.Context, in *BatDelUserCertReq, opts ...grpc.CallOption) (*BatDelUserCertResp, error)
	// 提供客户端的接口 生效中
	GetUserCertByUid(ctx context.Context, in *GetUserCertByUidReq, opts ...grpc.CallOption) (*GetUserCertByUidResp, error)
	// 设置优先展示的认证标
	SetPriorityDisplayCert(ctx context.Context, in *SetPriorityDisplayCertReq, opts ...grpc.CallOption) (*SetPriorityDisplayCertResp, error)
}

type personalCertificationClient struct {
	cc *grpc.ClientConn
}

func NewPersonalCertificationClient(cc *grpc.ClientConn) PersonalCertificationClient {
	return &personalCertificationClient{cc}
}

func (c *personalCertificationClient) SetPersonalCertification(ctx context.Context, in *SetPersonalCertificationReq, opts ...grpc.CallOption) (*SetPersonalCertificationResp, error) {
	out := new(SetPersonalCertificationResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/SetPersonalCertification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) GetPersonalCertification(ctx context.Context, in *GetPersonalCertificationReq, opts ...grpc.CallOption) (*GetPersonalCertificationResp, error) {
	out := new(GetPersonalCertificationResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/GetPersonalCertification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) BatGetPersonalCertification(ctx context.Context, in *BatGetPersonalCertificationReq, opts ...grpc.CallOption) (*BatGetPersonalCertificationResp, error) {
	out := new(BatGetPersonalCertificationResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/BatGetPersonalCertification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) StopPersonalCertification(ctx context.Context, in *StopPersonalCertificationReq, opts ...grpc.CallOption) (*StopPersonalCertificationResp, error) {
	out := new(StopPersonalCertificationResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/StopPersonalCertification", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) SetCertType(ctx context.Context, in *SetCertTypeReq, opts ...grpc.CallOption) (*SetCertTypeResp, error) {
	out := new(SetCertTypeResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/SetCertType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) DelCertType(ctx context.Context, in *DelCertTypeReq, opts ...grpc.CallOption) (*DelCertTypeResp, error) {
	out := new(DelCertTypeResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/DelCertType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) GetCertType(ctx context.Context, in *GetCertTypeReq, opts ...grpc.CallOption) (*GetCertTypeResp, error) {
	out := new(GetCertTypeResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/GetCertType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) GetCertTypeById(ctx context.Context, in *GetCertTypeByIdReq, opts ...grpc.CallOption) (*GetCertTypeByIdResp, error) {
	out := new(GetCertTypeByIdResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/GetCertTypeById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) GetCertTypeFuzzySearch(ctx context.Context, in *GetCertTypeFuzzySearchReq, opts ...grpc.CallOption) (*GetCertTypeFuzzySearchResp, error) {
	out := new(GetCertTypeFuzzySearchResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/GetCertTypeFuzzySearch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) SetUserCert(ctx context.Context, in *SetUserCertReq, opts ...grpc.CallOption) (*SetUserCertResp, error) {
	out := new(SetUserCertResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/SetUserCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) GetUserCert(ctx context.Context, in *GetUserCertReq, opts ...grpc.CallOption) (*GetUserCertResp, error) {
	out := new(GetUserCertResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/GetUserCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) DelUserCertByID(ctx context.Context, in *DelUserCertByIDReq, opts ...grpc.CallOption) (*DelUserCertByIDResp, error) {
	out := new(DelUserCertByIDResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/DelUserCertByID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) BatDelUserCert(ctx context.Context, in *BatDelUserCertReq, opts ...grpc.CallOption) (*BatDelUserCertResp, error) {
	out := new(BatDelUserCertResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/BatDelUserCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) GetUserCertByUid(ctx context.Context, in *GetUserCertByUidReq, opts ...grpc.CallOption) (*GetUserCertByUidResp, error) {
	out := new(GetUserCertByUidResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/GetUserCertByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *personalCertificationClient) SetPriorityDisplayCert(ctx context.Context, in *SetPriorityDisplayCertReq, opts ...grpc.CallOption) (*SetPriorityDisplayCertResp, error) {
	out := new(SetPriorityDisplayCertResp)
	err := c.cc.Invoke(ctx, "/personalcertification.PersonalCertification/SetPriorityDisplayCert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PersonalCertificationServer is the server API for PersonalCertification service.
type PersonalCertificationServer interface {
	SetPersonalCertification(context.Context, *SetPersonalCertificationReq) (*SetPersonalCertificationResp, error)
	GetPersonalCertification(context.Context, *GetPersonalCertificationReq) (*GetPersonalCertificationResp, error)
	BatGetPersonalCertification(context.Context, *BatGetPersonalCertificationReq) (*BatGetPersonalCertificationResp, error)
	StopPersonalCertification(context.Context, *StopPersonalCertificationReq) (*StopPersonalCertificationResp, error)
	SetCertType(context.Context, *SetCertTypeReq) (*SetCertTypeResp, error)
	DelCertType(context.Context, *DelCertTypeReq) (*DelCertTypeResp, error)
	GetCertType(context.Context, *GetCertTypeReq) (*GetCertTypeResp, error)
	GetCertTypeById(context.Context, *GetCertTypeByIdReq) (*GetCertTypeByIdResp, error)
	// 模糊搜索
	GetCertTypeFuzzySearch(context.Context, *GetCertTypeFuzzySearchReq) (*GetCertTypeFuzzySearchResp, error)
	SetUserCert(context.Context, *SetUserCertReq) (*SetUserCertResp, error)
	GetUserCert(context.Context, *GetUserCertReq) (*GetUserCertResp, error)
	DelUserCertByID(context.Context, *DelUserCertByIDReq) (*DelUserCertByIDResp, error)
	BatDelUserCert(context.Context, *BatDelUserCertReq) (*BatDelUserCertResp, error)
	// 提供客户端的接口 生效中
	GetUserCertByUid(context.Context, *GetUserCertByUidReq) (*GetUserCertByUidResp, error)
	// 设置优先展示的认证标
	SetPriorityDisplayCert(context.Context, *SetPriorityDisplayCertReq) (*SetPriorityDisplayCertResp, error)
}

func RegisterPersonalCertificationServer(s *grpc.Server, srv PersonalCertificationServer) {
	s.RegisterService(&_PersonalCertification_serviceDesc, srv)
}

func _PersonalCertification_SetPersonalCertification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPersonalCertificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).SetPersonalCertification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/SetPersonalCertification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).SetPersonalCertification(ctx, req.(*SetPersonalCertificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_GetPersonalCertification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalCertificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).GetPersonalCertification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/GetPersonalCertification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).GetPersonalCertification(ctx, req.(*GetPersonalCertificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_BatGetPersonalCertification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetPersonalCertificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).BatGetPersonalCertification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/BatGetPersonalCertification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).BatGetPersonalCertification(ctx, req.(*BatGetPersonalCertificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_StopPersonalCertification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopPersonalCertificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).StopPersonalCertification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/StopPersonalCertification",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).StopPersonalCertification(ctx, req.(*StopPersonalCertificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_SetCertType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCertTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).SetCertType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/SetCertType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).SetCertType(ctx, req.(*SetCertTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_DelCertType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCertTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).DelCertType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/DelCertType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).DelCertType(ctx, req.(*DelCertTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_GetCertType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCertTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).GetCertType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/GetCertType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).GetCertType(ctx, req.(*GetCertTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_GetCertTypeById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCertTypeByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).GetCertTypeById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/GetCertTypeById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).GetCertTypeById(ctx, req.(*GetCertTypeByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_GetCertTypeFuzzySearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCertTypeFuzzySearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).GetCertTypeFuzzySearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/GetCertTypeFuzzySearch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).GetCertTypeFuzzySearch(ctx, req.(*GetCertTypeFuzzySearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_SetUserCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).SetUserCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/SetUserCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).SetUserCert(ctx, req.(*SetUserCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_GetUserCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).GetUserCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/GetUserCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).GetUserCert(ctx, req.(*GetUserCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_DelUserCertByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserCertByIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).DelUserCertByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/DelUserCertByID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).DelUserCertByID(ctx, req.(*DelUserCertByIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_BatDelUserCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatDelUserCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).BatDelUserCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/BatDelUserCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).BatDelUserCert(ctx, req.(*BatDelUserCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_GetUserCertByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCertByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).GetUserCertByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/GetUserCertByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).GetUserCertByUid(ctx, req.(*GetUserCertByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PersonalCertification_SetPriorityDisplayCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPriorityDisplayCertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PersonalCertificationServer).SetPriorityDisplayCert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/personalcertification.PersonalCertification/SetPriorityDisplayCert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PersonalCertificationServer).SetPriorityDisplayCert(ctx, req.(*SetPriorityDisplayCertReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PersonalCertification_serviceDesc = grpc.ServiceDesc{
	ServiceName: "personalcertification.PersonalCertification",
	HandlerType: (*PersonalCertificationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetPersonalCertification",
			Handler:    _PersonalCertification_SetPersonalCertification_Handler,
		},
		{
			MethodName: "GetPersonalCertification",
			Handler:    _PersonalCertification_GetPersonalCertification_Handler,
		},
		{
			MethodName: "BatGetPersonalCertification",
			Handler:    _PersonalCertification_BatGetPersonalCertification_Handler,
		},
		{
			MethodName: "StopPersonalCertification",
			Handler:    _PersonalCertification_StopPersonalCertification_Handler,
		},
		{
			MethodName: "SetCertType",
			Handler:    _PersonalCertification_SetCertType_Handler,
		},
		{
			MethodName: "DelCertType",
			Handler:    _PersonalCertification_DelCertType_Handler,
		},
		{
			MethodName: "GetCertType",
			Handler:    _PersonalCertification_GetCertType_Handler,
		},
		{
			MethodName: "GetCertTypeById",
			Handler:    _PersonalCertification_GetCertTypeById_Handler,
		},
		{
			MethodName: "GetCertTypeFuzzySearch",
			Handler:    _PersonalCertification_GetCertTypeFuzzySearch_Handler,
		},
		{
			MethodName: "SetUserCert",
			Handler:    _PersonalCertification_SetUserCert_Handler,
		},
		{
			MethodName: "GetUserCert",
			Handler:    _PersonalCertification_GetUserCert_Handler,
		},
		{
			MethodName: "DelUserCertByID",
			Handler:    _PersonalCertification_DelUserCertByID_Handler,
		},
		{
			MethodName: "BatDelUserCert",
			Handler:    _PersonalCertification_BatDelUserCert_Handler,
		},
		{
			MethodName: "GetUserCertByUid",
			Handler:    _PersonalCertification_GetUserCertByUid_Handler,
		},
		{
			MethodName: "SetPriorityDisplayCert",
			Handler:    _PersonalCertification_SetPriorityDisplayCert_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "personal-certification/personal-certification.proto",
}

func init() {
	proto.RegisterFile("personal-certification/personal-certification.proto", fileDescriptor_personal_certification_ba42dfee555ece6b)
}

var fileDescriptor_personal_certification_ba42dfee555ece6b = []byte{
	// 1874 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x19, 0x5d, 0x73, 0xe3, 0x48,
	0x31, 0xfe, 0xb6, 0xdb, 0xb1, 0xa3, 0xcc, 0x26, 0xc1, 0xeb, 0x24, 0x6c, 0x6a, 0xb8, 0xdb, 0xca,
	0x05, 0x2e, 0xbb, 0x64, 0xf7, 0x58, 0xe0, 0x89, 0xc4, 0x71, 0x74, 0xbe, 0x4b, 0xec, 0x94, 0x9c,
	0xec, 0x01, 0x55, 0xa0, 0xd2, 0x4a, 0x93, 0x8d, 0x76, 0x65, 0x49, 0x27, 0x8d, 0x6f, 0xcf, 0xf7,
	0xc2, 0x03, 0x50, 0x45, 0x15, 0x55, 0x14, 0x3c, 0xf0, 0x47, 0x78, 0xe4, 0x8d, 0x1f, 0xc1, 0xff,
	0xa1, 0x66, 0x24, 0xd9, 0x92, 0x2c, 0xc9, 0x76, 0xb8, 0x37, 0x4d, 0x77, 0x4f, 0x77, 0x4f, 0x7f,
	0xb7, 0x0d, 0x2f, 0x6c, 0xe2, 0xb8, 0x96, 0xa9, 0x18, 0x9f, 0xaa, 0xc4, 0xa1, 0xfa, 0x9d, 0xae,
	0x2a, 0x54, 0xb7, 0xcc, 0x67, 0xc9, 0xe0, 0x63, 0xdb, 0xb1, 0xa8, 0x85, 0xb6, 0x03, 0x6c, 0x04,
	0x89, 0x3f, 0x02, 0x24, 0x12, 0xda, 0x21, 0x0e, 0xbd, 0x99, 0xd8, 0xe4, 0x6c, 0xd2, 0xd3, 0x24,
	0xf2, 0x35, 0x6a, 0x42, 0x5e, 0xd7, 0x5a, 0xb9, 0x83, 0xdc, 0x61, 0x4d, 0xca, 0xeb, 0x1a, 0xee,
	0xc3, 0xa3, 0x39, 0x2a, 0xd7, 0x46, 0xaf, 0xa0, 0xc8, 0xb8, 0x71, 0xc2, 0xfa, 0xc9, 0x8f, 0x8e,
	0x13, 0x45, 0x1c, 0x07, 0xd7, 0x7a, 0xe6, 0x9d, 0x25, 0xf1, 0x0b, 0xf8, 0x1f, 0x45, 0xd8, 0xbd,
	0xf6, 0x89, 0x3b, 0x61, 0xe2, 0x8e, 0x35, 0x1a, 0x59, 0x26, 0x12, 0xa0, 0x30, 0xf6, 0x15, 0x68,
	0x48, 0xec, 0x13, 0xed, 0x42, 0x8d, 0xdd, 0x94, 0xe9, 0xc4, 0x26, 0xad, 0x3c, 0x87, 0x57, 0x55,
	0x9f, 0x31, 0x42, 0x50, 0xd4, 0x55, 0xcb, 0x6c, 0x15, 0xb8, 0xc2, 0xfc, 0x9b, 0xc1, 0x28, 0xf9,
	0x96, 0xb6, 0x8a, 0x1e, 0x8c, 0x7d, 0xa3, 0x7d, 0x80, 0x37, 0xe4, 0xad, 0x6e, 0xca, 0x54, 0x1f,
	0x91, 0x56, 0x89, 0x73, 0xa9, 0x71, 0xc8, 0x8d, 0x3e, 0x22, 0xe8, 0x31, 0x54, 0x89, 0xa9, 0x79,
	0xc8, 0x32, 0x47, 0x56, 0x88, 0xa9, 0x71, 0xd4, 0x13, 0xa8, 0x8f, 0x6d, 0x4d, 0xa1, 0xc4, 0xc3,
	0x56, 0x38, 0x16, 0x3c, 0x10, 0x27, 0x68, 0x43, 0xd5, 0xb2, 0x89, 0xa3, 0x50, 0xcb, 0x69, 0x55,
	0xb9, 0xc8, 0xe9, 0x19, 0x9d, 0xc0, 0xb6, 0xed, 0x10, 0x97, 0x98, 0x54, 0xb6, 0x2d, 0x57, 0x67,
	0xef, 0x94, 0x0d, 0xdd, 0xa5, 0xad, 0xda, 0x41, 0xe1, 0xb0, 0x21, 0x3d, 0xf2, 0x91, 0xd7, 0x3e,
	0xee, 0x52, 0x77, 0x29, 0xda, 0x82, 0x92, 0x6a, 0x19, 0x96, 0xd3, 0x82, 0x83, 0xc2, 0x61, 0x4d,
	0xf2, 0x0e, 0xe8, 0x08, 0x36, 0xd9, 0x43, 0x64, 0xf7, 0x5e, 0xd1, 0xac, 0x0f, 0xb2, 0x47, 0x51,
	0xe7, 0xe2, 0x36, 0x18, 0x62, 0xc8, 0xe1, 0x1d, 0x4e, 0xbb, 0x0f, 0xe0, 0xde, 0x5b, 0xcc, 0x64,
	0xcc, 0x0c, 0xeb, 0x9c, 0xa8, 0xc6, 0x21, 0x37, 0xcc, 0x16, 0x7b, 0x50, 0xd3, 0x4d, 0xea, 0x58,
	0xda, 0x58, 0x25, 0xad, 0x86, 0x87, 0x9d, 0x02, 0x98, 0x29, 0xde, 0x8d, 0x47, 0xb6, 0x3c, 0x76,
	0x8c, 0x56, 0x93, 0x23, 0x2b, 0xec, 0x7c, 0xeb, 0x18, 0xe8, 0x23, 0x68, 0x4e, 0x3d, 0x21, 0x9b,
	0xca, 0x88, 0xb4, 0x36, 0x38, 0xc1, 0x7a, 0xe0, 0x8e, 0xbe, 0x32, 0x22, 0xe8, 0x39, 0x6c, 0xd9,
	0x8e, 0x6e, 0x39, 0x3a, 0x9d, 0xc8, 0x9a, 0xee, 0xda, 0x86, 0x32, 0x91, 0xdf, 0xe8, 0xb4, 0x25,
	0x1c, 0xe4, 0x0e, 0x4b, 0x12, 0x0a, 0x70, 0xe7, 0x1e, 0xea, 0x4c, 0xa7, 0xf8, 0x03, 0x3c, 0x4e,
	0x0c, 0x09, 0x16, 0x36, 0xf1, 0x80, 0x44, 0x5f, 0x40, 0x59, 0xe5, 0xa1, 0xc2, 0x63, 0xa1, 0x7e,
	0x72, 0x92, 0x12, 0x7b, 0x19, 0x41, 0x26, 0xf9, 0x1c, 0xb0, 0x01, 0xbb, 0x43, 0x42, 0x13, 0x29,
	0x59, 0x2e, 0x5c, 0x31, 0x43, 0xdd, 0x59, 0x9e, 0xc7, 0x72, 0x07, 0x85, 0xc3, 0xfa, 0xc9, 0xf3,
	0x55, 0xa4, 0xf1, 0xb0, 0xaf, 0x32, 0x16, 0xcc, 0xb1, 0xf8, 0x02, 0xf6, 0xd2, 0xa5, 0xb9, 0x36,
	0x7a, 0x0a, 0x1b, 0x77, 0x8a, 0x6e, 0x10, 0x4d, 0x1e, 0xeb, 0xda, 0x4c, 0x68, 0x43, 0x6a, 0x78,
	0xe0, 0x5b, 0x5d, 0xe3, 0x7c, 0xfe, 0x9a, 0x87, 0x5d, 0x31, 0x43, 0xed, 0xb8, 0xc5, 0xfc, 0x94,
	0xca, 0xcf, 0x52, 0x2a, 0x1c, 0xb2, 0x85, 0x58, 0xc8, 0x46, 0xd2, 0xad, 0x18, 0x4b, 0xb7, 0x4f,
	0x40, 0x88, 0xc7, 0xb3, 0x9f, 0x4c, 0x1b, 0xb1, 0x50, 0x46, 0x3b, 0x50, 0xb6, 0xee, 0xee, 0x5c,
	0x42, 0xfd, 0x84, 0xf2, 0x4f, 0x2c, 0xbc, 0x0d, 0x7d, 0xa4, 0x53, 0x3f, 0x93, 0xbc, 0x03, 0x83,
	0x7e, 0xa3, 0x18, 0xba, 0xc6, 0x33, 0xa8, 0x2a, 0x79, 0x07, 0x84, 0xa1, 0x61, 0x5a, 0x54, 0x1e,
	0xbb, 0x44, 0x56, 0x15, 0xf5, 0x9e, 0xb4, 0x6a, 0x1c, 0x5b, 0x37, 0x2d, 0x7a, 0xeb, 0x92, 0x0e,
	0x03, 0xe1, 0x3f, 0xe6, 0x60, 0x4f, 0xcc, 0x32, 0xeb, 0xf7, 0xeb, 0x45, 0xa6, 0x29, 0xb5, 0xa8,
	0x62, 0xf8, 0xf6, 0xf4, 0x0e, 0xf8, 0x18, 0xf6, 0x86, 0xd4, 0xb2, 0x97, 0xf5, 0x09, 0x7e, 0x02,
	0xfb, 0x19, 0xf4, 0xae, 0x8d, 0x7f, 0x07, 0x3f, 0x3c, 0x53, 0x68, 0x96, 0x9b, 0xa7, 0x95, 0xb2,
	0x10, 0xb8, 0x35, 0xc9, 0x3b, 0xf9, 0x44, 0xef, 0x60, 0x13, 0xf6, 0x53, 0x1f, 0xcb, 0x9f, 0xf9,
	0x3d, 0xc7, 0xfe, 0x9f, 0xf2, 0xf0, 0x24, 0xf3, 0x3d, 0xae, 0x8d, 0xde, 0x43, 0x63, 0xec, 0x12,
	0x47, 0xe6, 0xe1, 0x37, 0x52, 0x6c, 0x5f, 0xac, 0x98, 0x22, 0x76, 0x01, 0xbb, 0xe3, 0x5b, 0x97,
	0x38, 0x0c, 0x7a, 0xa5, 0xd8, 0x5d, 0x93, 0x3a, 0x13, 0xa9, 0x3e, 0x9e, 0x41, 0xda, 0x14, 0x84,
	0x38, 0x01, 0xb3, 0xe8, 0x7b, 0x32, 0x09, 0x7a, 0xcf, 0x7b, 0x32, 0x41, 0x5f, 0xf0, 0xb0, 0x1c,
	0x13, 0xbf, 0xd6, 0xbc, 0x5c, 0xd5, 0x02, 0xec, 0xed, 0x92, 0xc7, 0xe2, 0x97, 0xf9, 0x9f, 0xe7,
	0xf0, 0xbf, 0xf2, 0xb0, 0x1e, 0x6e, 0x8a, 0x73, 0xb9, 0x8a, 0xa0, 0xc8, 0x0b, 0x6b, 0xde, 0xeb,
	0x5d, 0xec, 0x3b, 0xb1, 0xc7, 0x4d, 0x9b, 0x44, 0x71, 0x61, 0x93, 0x28, 0x25, 0x37, 0x89, 0xd4,
	0xd6, 0x54, 0x4e, 0x6f, 0x4d, 0xe1, 0xba, 0x51, 0x89, 0xd5, 0x8d, 0x58, 0x9f, 0xac, 0xce, 0xf5,
	0xc9, 0x48, 0xdb, 0xa9, 0x65, 0xb5, 0x1d, 0x88, 0xb4, 0x1d, 0xdc, 0x83, 0xe6, 0x70, 0x36, 0x82,
	0xb0, 0xd0, 0x7f, 0x05, 0x45, 0x16, 0x59, 0x2b, 0x4d, 0x1f, 0xec, 0x02, 0xde, 0x84, 0x8d, 0x08,
	0x2b, 0xd7, 0xc6, 0x07, 0xd0, 0x3c, 0x27, 0x46, 0x98, 0x7b, 0x3c, 0x57, 0x37, 0x61, 0x23, 0x42,
	0xe1, 0xda, 0xb8, 0x0f, 0x4d, 0x31, 0xaa, 0x52, 0xdc, 0x91, 0xb3, 0xf2, 0x97, 0x4f, 0x2e, 0x7f,
	0x85, 0x50, 0xf9, 0xc3, 0x3a, 0x6c, 0x88, 0x51, 0xbd, 0xd0, 0xaf, 0xfc, 0x3a, 0x1c, 0x4a, 0xc0,
	0xa5, 0x1e, 0xca, 0x8b, 0x75, 0x46, 0xa5, 0xfa, 0x14, 0x1e, 0x87, 0x44, 0x5d, 0x8c, 0xbf, 0xfb,
	0x6e, 0x32, 0x24, 0x8a, 0xa3, 0xde, 0xfb, 0x35, 0xc5, 0xa5, 0x8e, 0xff, 0x0c, 0xf6, 0x89, 0x5f,
	0x42, 0x73, 0xca, 0x5e, 0x5b, 0x36, 0x64, 0xb1, 0x09, 0xed, 0x34, 0x21, 0xae, 0x8d, 0xae, 0x61,
	0x93, 0x3f, 0x4d, 0xd7, 0xf8, 0x14, 0x11, 0x7e, 0xe2, 0xc7, 0x8b, 0x9e, 0xc8, 0x75, 0x90, 0xf8,
	0x1c, 0xd2, 0xd3, 0xd8, 0xbc, 0xc1, 0xcb, 0xcb, 0xbf, 0xf3, 0xb0, 0x1e, 0xa4, 0x73, 0xa2, 0x92,
	0xf3, 0x3d, 0xf0, 0x00, 0xd6, 0x67, 0xc3, 0x8c, 0xae, 0xf9, 0xd9, 0x05, 0xea, 0x54, 0x50, 0xc2,
	0xb8, 0x53, 0x4c, 0x18, 0x77, 0x82, 0x69, 0xb3, 0x14, 0x9d, 0x36, 0x43, 0x03, 0x58, 0x39, 0x3e,
	0x80, 0x45, 0x87, 0xd1, 0x4a, 0xd6, 0x30, 0x5a, 0x8d, 0x0e, 0xa3, 0x3b, 0x50, 0x76, 0xa9, 0x42,
	0xc7, 0x2e, 0x4f, 0xa0, 0x86, 0xe4, 0x9f, 0xe2, 0xc9, 0x07, 0x99, 0x43, 0x6a, 0x3d, 0x9a, 0xb9,
	0x58, 0xe2, 0xf9, 0x15, 0x98, 0x8f, 0x85, 0xc1, 0x0a, 0xb1, 0x17, 0xb6, 0xfa, 0x2c, 0xf6, 0xf0,
	0x05, 0x4f, 0xb4, 0x19, 0x4f, 0xd7, 0x46, 0x2f, 0x60, 0x47, 0x37, 0x79, 0x5f, 0x97, 0x67, 0x66,
	0x9d, 0x4a, 0xa8, 0x49, 0x8f, 0x7c, 0x6c, 0xe0, 0x6a, 0xcf, 0xb1, 0x39, 0x9e, 0x69, 0x61, 0xe5,
	0xe6, 0x37, 0x84, 0xb8, 0x2b, 0xf3, 0x73, 0xae, 0x0c, 0x9c, 0x54, 0x08, 0x39, 0x29, 0x6c, 0x92,
	0x62, 0xac, 0x98, 0xcd, 0xec, 0x5c, 0x8a, 0xd8, 0x79, 0xa5, 0xa1, 0xc6, 0xcf, 0xea, 0x88, 0x11,
	0xfe, 0x6f, 0xcb, 0xa6, 0x64, 0xb5, 0x01, 0xe8, 0x9c, 0x18, 0xc1, 0x95, 0xb3, 0x49, 0xef, 0x7c,
	0xb9, 0x49, 0x70, 0x71, 0x16, 0x20, 0x28, 0x6a, 0xc4, 0x55, 0x83, 0x6d, 0x8a, 0x7d, 0xe3, 0x6d,
	0x78, 0x34, 0x27, 0xcd, 0xb5, 0x71, 0x97, 0x17, 0xca, 0x48, 0x1e, 0x3e, 0xc0, 0x59, 0xf8, 0x35,
	0x6c, 0x9e, 0x29, 0x34, 0xc4, 0x89, 0x3d, 0xe5, 0x14, 0xaa, 0x1a, 0x31, 0x64, 0xbf, 0xec, 0x33,
	0xbb, 0x3d, 0x4d, 0xb1, 0x5b, 0x4c, 0x05, 0xa9, 0xa2, 0x11, 0x83, 0x7d, 0xe0, 0x2d, 0x40, 0x71,
	0xbe, 0xae, 0x8d, 0xff, 0x53, 0x80, 0xed, 0xd7, 0x2c, 0xee, 0x02, 0x68, 0x6a, 0x6f, 0x9e, 0xb7,
	0xde, 0x0a, 0xdb, 0x67, 0xa8, 0x1e, 0x94, 0xe2, 0xf5, 0x60, 0xda, 0xcc, 0xcb, 0x0b, 0x9b, 0x79,
	0x65, 0xc5, 0x66, 0x5e, 0x4d, 0x6f, 0xe6, 0xd1, 0x2a, 0x54, 0xcb, 0xaa, 0x42, 0x10, 0xad, 0x42,
	0x91, 0x4e, 0x5e, 0xcf, 0xea, 0xe4, 0xeb, 0x8b, 0x16, 0xc8, 0xc6, 0x0a, 0x0b, 0x64, 0x33, 0x75,
	0x81, 0x94, 0xf8, 0x8f, 0x14, 0xb3, 0x78, 0xbc, 0xd5, 0xb5, 0xe4, 0x4a, 0xb1, 0xfc, 0xfe, 0x82,
	0x7f, 0x0d, 0x5b, 0xf3, 0x3c, 0x79, 0x06, 0x07, 0xbf, 0x7c, 0xb0, 0x20, 0xfc, 0x49, 0x4a, 0x10,
	0x26, 0x46, 0x94, 0xff, 0x13, 0xc8, 0xdf, 0x73, 0xf0, 0x98, 0x2d, 0x82, 0xd1, 0x77, 0xa4, 0x97,
	0xb7, 0x1f, 0x40, 0xc5, 0x6f, 0x97, 0x7e, 0xb2, 0x94, 0xbd, 0xee, 0x97, 0xf8, 0x9a, 0x42, 0xf2,
	0x36, 0xb6, 0x0f, 0xa0, 0xde, 0x2b, 0xa6, 0xc9, 0x52, 0x48, 0xf3, 0xd7, 0xba, 0x9a, 0x0f, 0xe9,
	0x69, 0x78, 0x0f, 0xda, 0x69, 0x1a, 0xb9, 0xf6, 0xd1, 0x11, 0x54, 0x83, 0x67, 0xa0, 0x3a, 0x54,
	0x6e, 0xfb, 0x5f, 0xf6, 0x07, 0x5f, 0xf5, 0x85, 0x35, 0xb4, 0x09, 0x8d, 0xeb, 0xae, 0x34, 0x1c,
	0xf4, 0x4f, 0x2f, 0xe5, 0x4e, 0x57, 0xba, 0x11, 0x72, 0x47, 0xff, 0xcc, 0xc1, 0xc6, 0x75, 0x4c,
	0x38, 0x82, 0xe6, 0xeb, 0xd3, 0xcb, 0xde, 0xb9, 0x7c, 0x3d, 0x18, 0xf6, 0x6e, 0x7a, 0x03, 0x76,
	0x75, 0x07, 0xd0, 0xf4, 0xea, 0xe7, 0x83, 0xab, 0xae, 0x7c, 0x7d, 0x2a, 0x76, 0x85, 0x1c, 0xda,
	0x02, 0xa1, 0xd7, 0xbf, 0x18, 0x48, 0x57, 0xa7, 0x8c, 0x50, 0xee, 0x9c, 0x4a, 0xe7, 0x42, 0x1e,
	0x35, 0xa0, 0x26, 0x75, 0xae, 0xce, 0xbd, 0x63, 0x01, 0xad, 0x43, 0xf5, 0x56, 0xec, 0x30, 0x76,
	0x37, 0x42, 0x91, 0x21, 0x7b, 0x57, 0xf2, 0xc5, 0xe0, 0xf2, 0x72, 0xf0, 0x95, 0x50, 0x62, 0x1c,
	0x3a, 0x9f, 0x9f, 0xf6, 0xfb, 0xdd, 0x4b, 0xb9, 0xdf, 0xeb, 0x7c, 0xd9, 0x3f, 0xbd, 0xea, 0x0a,
	0xe5, 0xa3, 0x81, 0x37, 0xc7, 0x0c, 0x79, 0x1d, 0x9f, 0xbe, 0xc4, 0x7c, 0x6f, 0x5a, 0x1f, 0x4c,
	0x61, 0x0d, 0xd5, 0xa0, 0xc4, 0x5d, 0x26, 0xe4, 0x18, 0xbc, 0xfb, 0xad, 0xad, 0x3b, 0x44, 0x13,
	0xf2, 0x08, 0xa0, 0xdc, 0xb7, 0xe8, 0x6f, 0x08, 0xf5, 0xa4, 0x4a, 0xc4, 0x36, 0x14, 0x95, 0x68,
	0x42, 0xf1, 0xe4, 0xbf, 0x0d, 0xd8, 0x4e, 0x9c, 0xfb, 0xd1, 0x9f, 0x73, 0xd0, 0x4a, 0x5b, 0xf4,
	0x51, 0xda, 0xcf, 0x15, 0x19, 0xbf, 0x43, 0xb4, 0x5f, 0xac, 0x7c, 0xc7, 0xb5, 0xf1, 0x1a, 0xd7,
	0x43, 0x5c, 0x55, 0x0f, 0xf1, 0x01, 0x7a, 0x88, 0xd9, 0x7a, 0xfc, 0x2d, 0x07, 0xbb, 0x19, 0xcb,
	0x1a, 0xfa, 0xec, 0x21, 0x0b, 0xde, 0xd7, 0xed, 0x9f, 0x3d, 0x6c, 0x2f, 0xc4, 0x6b, 0xe8, 0x2f,
	0x2c, 0x01, 0xd3, 0xb6, 0x6f, 0x94, 0x6a, 0xed, 0x8c, 0xfd, 0xbe, 0xfd, 0x72, 0xf5, 0x4b, 0x5c,
	0x95, 0xdf, 0x43, 0x3d, 0xb4, 0x90, 0xa0, 0x8f, 0xd3, 0x3d, 0x1d, 0x5a, 0x36, 0xda, 0x4f, 0x97,
	0x21, 0x0b, 0xf8, 0x87, 0x76, 0x97, 0x54, 0xfe, 0xd1, 0x0d, 0xa8, 0xfd, 0x74, 0x19, 0xb2, 0x80,
	0xbf, 0xb8, 0x84, 0xfe, 0xe2, 0x72, 0xfa, 0x8b, 0x73, 0xfa, 0xbf, 0x8b, 0x2c, 0x46, 0x67, 0x13,
	0x56, 0xf5, 0x16, 0x5f, 0xf6, 0x7f, 0xcc, 0x6e, 0x1f, 0x2d, 0x4b, 0xca, 0x65, 0xfd, 0x01, 0x76,
	0x92, 0x97, 0x16, 0xf4, 0x7c, 0x31, 0x9f, 0xe8, 0x22, 0xd5, 0xfe, 0xe9, 0x8a, 0x37, 0x42, 0xc1,
	0x10, 0x74, 0x8d, 0xac, 0x60, 0x08, 0x4d, 0x46, 0x59, 0xc1, 0x10, 0x19, 0x74, 0x02, 0x67, 0x2d,
	0xe4, 0x2f, 0x2e, 0xc7, 0x5f, 0x9c, 0xe3, 0xff, 0x2e, 0x32, 0xff, 0xb1, 0xb1, 0x30, 0xd5, 0x59,
	0xf3, 0xc3, 0x6a, 0xaa, 0xb3, 0x92, 0x26, 0xcd, 0x35, 0xf4, 0x16, 0x9a, 0xd1, 0x61, 0x0e, 0x1d,
	0xa6, 0xd7, 0x83, 0xe8, 0x2c, 0xd9, 0xfe, 0x64, 0x49, 0x4a, 0x2e, 0x68, 0x04, 0x42, 0x7c, 0x0e,
	0x40, 0x47, 0x8b, 0x4d, 0x12, 0x0c, 0x21, 0xed, 0x1f, 0x2f, 0x4d, 0x1b, 0x04, 0x61, 0x72, 0x27,
	0x4e, 0x0d, 0xc2, 0xd4, 0x51, 0x22, 0x35, 0x08, 0xd3, 0x5b, 0x3d, 0x5e, 0x3b, 0xfb, 0xc5, 0x6f,
	0x5f, 0xbd, 0xb5, 0x0c, 0xc5, 0x7c, 0x7b, 0xfc, 0xd9, 0x09, 0xa5, 0xc7, 0xaa, 0x35, 0x7a, 0xc6,
	0xff, 0x46, 0x52, 0x2d, 0xe3, 0x99, 0x4b, 0x9c, 0x6f, 0x74, 0x95, 0xb8, 0xcf, 0x12, 0xf9, 0xbe,
	0x29, 0x73, 0xc2, 0x17, 0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0xb0, 0x6d, 0x53, 0x8d, 0xa6, 0x1a,
	0x00, 0x00,
}
