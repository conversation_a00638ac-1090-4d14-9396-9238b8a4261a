// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/magic-spirit/magic-spirit.proto

package magic_spirit // import "golang.52tt.com/protocol/services/magic-spirit"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import present_middleware "golang.52tt.com/protocol/services/present-middleware"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PrizeLeverConst int32

const (
	PrizeLeverConst_NONE_PRIZE        PrizeLeverConst = 0
	PrizeLeverConst_SMALL_PRIZE       PrizeLeverConst = 1
	PrizeLeverConst_NORMAL_PRIZE      PrizeLeverConst = 2
	PrizeLeverConst_BIG_PRIZE         PrizeLeverConst = 3
	PrizeLeverConst_FULL_SERVER_PRIZE PrizeLeverConst = 4
)

var PrizeLeverConst_name = map[int32]string{
	0: "NONE_PRIZE",
	1: "SMALL_PRIZE",
	2: "NORMAL_PRIZE",
	3: "BIG_PRIZE",
	4: "FULL_SERVER_PRIZE",
}
var PrizeLeverConst_value = map[string]int32{
	"NONE_PRIZE":        0,
	"SMALL_PRIZE":       1,
	"NORMAL_PRIZE":      2,
	"BIG_PRIZE":         3,
	"FULL_SERVER_PRIZE": 4,
}

func (x PrizeLeverConst) String() string {
	return proto.EnumName(PrizeLeverConst_name, int32(x))
}
func (PrizeLeverConst) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{0}
}

type CommonConfType int32

const (
	CommonConfType_NONE                         CommonConfType = 0
	CommonConfType_DAILY_SEND_MONEY_LIMIT       CommonConfType = 1
	CommonConfType_DAILY_PREVENT_EXCHANGE_LIMIT CommonConfType = 2
	CommonConfType_PER_ORDER_COUNT_LIMIT        CommonConfType = 3
)

var CommonConfType_name = map[int32]string{
	0: "NONE",
	1: "DAILY_SEND_MONEY_LIMIT",
	2: "DAILY_PREVENT_EXCHANGE_LIMIT",
	3: "PER_ORDER_COUNT_LIMIT",
}
var CommonConfType_value = map[string]int32{
	"NONE": 0,
	"DAILY_SEND_MONEY_LIMIT":       1,
	"DAILY_PREVENT_EXCHANGE_LIMIT": 2,
	"PER_ORDER_COUNT_LIMIT":        3,
}

func (x CommonConfType) String() string {
	return proto.EnumName(CommonConfType_name, int32(x))
}
func (CommonConfType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{1}
}

// 魔法精灵中奖类型
type MagicSpiritEffectType int32

const (
	MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_NONE         MagicSpiritEffectType = 0
	MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV1          MagicSpiritEffectType = 1
	MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV2          MagicSpiritEffectType = 2
	MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV3          MagicSpiritEffectType = 3
	MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV4          MagicSpiritEffectType = 4
	MagicSpiritEffectType_MAGIC_SPIRIT_EFFECT_LV3_MORE_500 MagicSpiritEffectType = 5
)

var MagicSpiritEffectType_name = map[int32]string{
	0: "MAGIC_SPIRIT_EFFECT_NONE",
	1: "MAGIC_SPIRIT_EFFECT_LV1",
	2: "MAGIC_SPIRIT_EFFECT_LV2",
	3: "MAGIC_SPIRIT_EFFECT_LV3",
	4: "MAGIC_SPIRIT_EFFECT_LV4",
	5: "MAGIC_SPIRIT_EFFECT_LV3_MORE_500",
}
var MagicSpiritEffectType_value = map[string]int32{
	"MAGIC_SPIRIT_EFFECT_NONE":         0,
	"MAGIC_SPIRIT_EFFECT_LV1":          1,
	"MAGIC_SPIRIT_EFFECT_LV2":          2,
	"MAGIC_SPIRIT_EFFECT_LV3":          3,
	"MAGIC_SPIRIT_EFFECT_LV4":          4,
	"MAGIC_SPIRIT_EFFECT_LV3_MORE_500": 5,
}

func (x MagicSpiritEffectType) String() string {
	return proto.EnumName(MagicSpiritEffectType_name, int32(x))
}
func (MagicSpiritEffectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{2}
}

type SendMagicSource int32

const (
	SendMagicSource_Common         SendMagicSource = 0
	SendMagicSource_ChannelLottery SendMagicSource = 1
	SendMagicSource_GrabChairGame  SendMagicSource = 2
)

var SendMagicSource_name = map[int32]string{
	0: "Common",
	1: "ChannelLottery",
	2: "GrabChairGame",
}
var SendMagicSource_value = map[string]int32{
	"Common":         0,
	"ChannelLottery": 1,
	"GrabChairGame":  2,
}

func (x SendMagicSource) String() string {
	return proto.EnumName(SendMagicSource_name, int32(x))
}
func (SendMagicSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{3}
}

type MagicSpirit struct {
	MagicSpiritId    uint32  `protobuf:"varint,1,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	Name             string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl          string  `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price            uint32  `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Rank             uint32  `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`
	EffectBegin      uint32  `protobuf:"varint,6,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd        uint32  `protobuf:"varint,7,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	DescribeImageUrl string  `protobuf:"bytes,8,opt,name=describe_image_url,json=describeImageUrl,proto3" json:"describe_image_url,omitempty"`
	Describe         string  `protobuf:"bytes,9,opt,name=describe,proto3" json:"describe,omitempty"`
	JuniorLighting   uint32  `protobuf:"varint,10,opt,name=junior_lighting,json=juniorLighting,proto3" json:"junior_lighting,omitempty"`
	MiddleLighting   uint32  `protobuf:"varint,11,opt,name=middle_lighting,json=middleLighting,proto3" json:"middle_lighting,omitempty"`
	VfxResource      string  `protobuf:"bytes,12,opt,name=vfx_resource,json=vfxResource,proto3" json:"vfx_resource,omitempty"`
	VfxResourceMd5   string  `protobuf:"bytes,13,opt,name=vfx_resource_md5,json=vfxResourceMd5,proto3" json:"vfx_resource_md5,omitempty"`
	UpdateTime       uint32  `protobuf:"varint,14,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	RankFloat        float32 `protobuf:"fixed32,15,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	// 6.36.0 增加活动链接字段
	DescActivityUrl string   `protobuf:"bytes,16,opt,name=desc_activity_url,json=descActivityUrl,proto3" json:"desc_activity_url,omitempty"`
	ChannelTypeList []uint32 `protobuf:"varint,17,rep,packed,name=channel_type_list,json=channelTypeList,proto3" json:"channel_type_list,omitempty"`
	// 6.56.3 新增
	ShowEffectEnd        bool                    `protobuf:"varint,18,opt,name=show_effect_end,json=showEffectEnd,proto3" json:"show_effect_end,omitempty"`
	ActivityCfg          *MagicSpiritActivityCfg `protobuf:"bytes,19,opt,name=activity_cfg,json=activityCfg,proto3" json:"activity_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *MagicSpirit) Reset()         { *m = MagicSpirit{} }
func (m *MagicSpirit) String() string { return proto.CompactTextString(m) }
func (*MagicSpirit) ProtoMessage()    {}
func (*MagicSpirit) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{0}
}
func (m *MagicSpirit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpirit.Unmarshal(m, b)
}
func (m *MagicSpirit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpirit.Marshal(b, m, deterministic)
}
func (dst *MagicSpirit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpirit.Merge(dst, src)
}
func (m *MagicSpirit) XXX_Size() int {
	return xxx_messageInfo_MagicSpirit.Size(m)
}
func (m *MagicSpirit) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpirit.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpirit proto.InternalMessageInfo

func (m *MagicSpirit) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *MagicSpirit) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MagicSpirit) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *MagicSpirit) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *MagicSpirit) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *MagicSpirit) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *MagicSpirit) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *MagicSpirit) GetDescribeImageUrl() string {
	if m != nil {
		return m.DescribeImageUrl
	}
	return ""
}

func (m *MagicSpirit) GetDescribe() string {
	if m != nil {
		return m.Describe
	}
	return ""
}

func (m *MagicSpirit) GetJuniorLighting() uint32 {
	if m != nil {
		return m.JuniorLighting
	}
	return 0
}

func (m *MagicSpirit) GetMiddleLighting() uint32 {
	if m != nil {
		return m.MiddleLighting
	}
	return 0
}

func (m *MagicSpirit) GetVfxResource() string {
	if m != nil {
		return m.VfxResource
	}
	return ""
}

func (m *MagicSpirit) GetVfxResourceMd5() string {
	if m != nil {
		return m.VfxResourceMd5
	}
	return ""
}

func (m *MagicSpirit) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *MagicSpirit) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

func (m *MagicSpirit) GetDescActivityUrl() string {
	if m != nil {
		return m.DescActivityUrl
	}
	return ""
}

func (m *MagicSpirit) GetChannelTypeList() []uint32 {
	if m != nil {
		return m.ChannelTypeList
	}
	return nil
}

func (m *MagicSpirit) GetShowEffectEnd() bool {
	if m != nil {
		return m.ShowEffectEnd
	}
	return false
}

func (m *MagicSpirit) GetActivityCfg() *MagicSpiritActivityCfg {
	if m != nil {
		return m.ActivityCfg
	}
	return nil
}

type MagicSpiritActivityCfg struct {
	ActivityName         string   `protobuf:"bytes,1,opt,name=activity_name,json=activityName,proto3" json:"activity_name,omitempty"`
	BeginTime            int64    `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ImageUrl             string   `protobuf:"bytes,4,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	JumpUrlTt            string   `protobuf:"bytes,5,opt,name=jump_url_tt,json=jumpUrlTt,proto3" json:"jump_url_tt,omitempty"`
	JumpUrlHcAndroid     string   `protobuf:"bytes,6,opt,name=jump_url_hc_android,json=jumpUrlHcAndroid,proto3" json:"jump_url_hc_android,omitempty"`
	JumpUrlHcIos         string   `protobuf:"bytes,7,opt,name=jump_url_hc_ios,json=jumpUrlHcIos,proto3" json:"jump_url_hc_ios,omitempty"`
	JumpUrlMikeAndroid   string   `protobuf:"bytes,8,opt,name=jump_url_mike_android,json=jumpUrlMikeAndroid,proto3" json:"jump_url_mike_android,omitempty"`
	JumpUrlMikeIos       string   `protobuf:"bytes,9,opt,name=jump_url_mike_ios,json=jumpUrlMikeIos,proto3" json:"jump_url_mike_ios,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MagicSpiritActivityCfg) Reset()         { *m = MagicSpiritActivityCfg{} }
func (m *MagicSpiritActivityCfg) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritActivityCfg) ProtoMessage()    {}
func (*MagicSpiritActivityCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{1}
}
func (m *MagicSpiritActivityCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritActivityCfg.Unmarshal(m, b)
}
func (m *MagicSpiritActivityCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritActivityCfg.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritActivityCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritActivityCfg.Merge(dst, src)
}
func (m *MagicSpiritActivityCfg) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritActivityCfg.Size(m)
}
func (m *MagicSpiritActivityCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritActivityCfg.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritActivityCfg proto.InternalMessageInfo

func (m *MagicSpiritActivityCfg) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *MagicSpiritActivityCfg) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *MagicSpiritActivityCfg) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *MagicSpiritActivityCfg) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *MagicSpiritActivityCfg) GetJumpUrlTt() string {
	if m != nil {
		return m.JumpUrlTt
	}
	return ""
}

func (m *MagicSpiritActivityCfg) GetJumpUrlHcAndroid() string {
	if m != nil {
		return m.JumpUrlHcAndroid
	}
	return ""
}

func (m *MagicSpiritActivityCfg) GetJumpUrlHcIos() string {
	if m != nil {
		return m.JumpUrlHcIos
	}
	return ""
}

func (m *MagicSpiritActivityCfg) GetJumpUrlMikeAndroid() string {
	if m != nil {
		return m.JumpUrlMikeAndroid
	}
	return ""
}

func (m *MagicSpiritActivityCfg) GetJumpUrlMikeIos() string {
	if m != nil {
		return m.JumpUrlMikeIos
	}
	return ""
}

// 更新幸运礼物配置
type MagicSpiritTmp struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	EffectTime           uint32       `protobuf:"varint,2,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	Conf                 *MagicSpirit `protobuf:"bytes,3,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MagicSpiritTmp) Reset()         { *m = MagicSpiritTmp{} }
func (m *MagicSpiritTmp) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritTmp) ProtoMessage()    {}
func (*MagicSpiritTmp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{2}
}
func (m *MagicSpiritTmp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritTmp.Unmarshal(m, b)
}
func (m *MagicSpiritTmp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritTmp.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritTmp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritTmp.Merge(dst, src)
}
func (m *MagicSpiritTmp) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritTmp.Size(m)
}
func (m *MagicSpiritTmp) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritTmp.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritTmp proto.InternalMessageInfo

func (m *MagicSpiritTmp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MagicSpiritTmp) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

func (m *MagicSpiritTmp) GetConf() *MagicSpirit {
	if m != nil {
		return m.Conf
	}
	return nil
}

type AddMagicSpiritReq struct {
	MagicSpirit          []*MagicSpirit `protobuf:"bytes,1,rep,name=magic_spirit,json=magicSpirit,proto3" json:"magic_spirit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AddMagicSpiritReq) Reset()         { *m = AddMagicSpiritReq{} }
func (m *AddMagicSpiritReq) String() string { return proto.CompactTextString(m) }
func (*AddMagicSpiritReq) ProtoMessage()    {}
func (*AddMagicSpiritReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{3}
}
func (m *AddMagicSpiritReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMagicSpiritReq.Unmarshal(m, b)
}
func (m *AddMagicSpiritReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMagicSpiritReq.Marshal(b, m, deterministic)
}
func (dst *AddMagicSpiritReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMagicSpiritReq.Merge(dst, src)
}
func (m *AddMagicSpiritReq) XXX_Size() int {
	return xxx_messageInfo_AddMagicSpiritReq.Size(m)
}
func (m *AddMagicSpiritReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMagicSpiritReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMagicSpiritReq proto.InternalMessageInfo

func (m *AddMagicSpiritReq) GetMagicSpirit() []*MagicSpirit {
	if m != nil {
		return m.MagicSpirit
	}
	return nil
}

type AddMagicSpiritResp struct {
	MagicSpiritId        []uint32 `protobuf:"varint,1,rep,packed,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMagicSpiritResp) Reset()         { *m = AddMagicSpiritResp{} }
func (m *AddMagicSpiritResp) String() string { return proto.CompactTextString(m) }
func (*AddMagicSpiritResp) ProtoMessage()    {}
func (*AddMagicSpiritResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{4}
}
func (m *AddMagicSpiritResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMagicSpiritResp.Unmarshal(m, b)
}
func (m *AddMagicSpiritResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMagicSpiritResp.Marshal(b, m, deterministic)
}
func (dst *AddMagicSpiritResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMagicSpiritResp.Merge(dst, src)
}
func (m *AddMagicSpiritResp) XXX_Size() int {
	return xxx_messageInfo_AddMagicSpiritResp.Size(m)
}
func (m *AddMagicSpiritResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMagicSpiritResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMagicSpiritResp proto.InternalMessageInfo

func (m *AddMagicSpiritResp) GetMagicSpiritId() []uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return nil
}

type DelMagicSpiritReq struct {
	MagicSpiritIds       []uint32 `protobuf:"varint,1,rep,packed,name=magic_spirit_ids,json=magicSpiritIds,proto3" json:"magic_spirit_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMagicSpiritReq) Reset()         { *m = DelMagicSpiritReq{} }
func (m *DelMagicSpiritReq) String() string { return proto.CompactTextString(m) }
func (*DelMagicSpiritReq) ProtoMessage()    {}
func (*DelMagicSpiritReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{5}
}
func (m *DelMagicSpiritReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMagicSpiritReq.Unmarshal(m, b)
}
func (m *DelMagicSpiritReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMagicSpiritReq.Marshal(b, m, deterministic)
}
func (dst *DelMagicSpiritReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMagicSpiritReq.Merge(dst, src)
}
func (m *DelMagicSpiritReq) XXX_Size() int {
	return xxx_messageInfo_DelMagicSpiritReq.Size(m)
}
func (m *DelMagicSpiritReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMagicSpiritReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelMagicSpiritReq proto.InternalMessageInfo

func (m *DelMagicSpiritReq) GetMagicSpiritIds() []uint32 {
	if m != nil {
		return m.MagicSpiritIds
	}
	return nil
}

type DelMagicSpiritResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMagicSpiritResp) Reset()         { *m = DelMagicSpiritResp{} }
func (m *DelMagicSpiritResp) String() string { return proto.CompactTextString(m) }
func (*DelMagicSpiritResp) ProtoMessage()    {}
func (*DelMagicSpiritResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{6}
}
func (m *DelMagicSpiritResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMagicSpiritResp.Unmarshal(m, b)
}
func (m *DelMagicSpiritResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMagicSpiritResp.Marshal(b, m, deterministic)
}
func (dst *DelMagicSpiritResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMagicSpiritResp.Merge(dst, src)
}
func (m *DelMagicSpiritResp) XXX_Size() int {
	return xxx_messageInfo_DelMagicSpiritResp.Size(m)
}
func (m *DelMagicSpiritResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMagicSpiritResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelMagicSpiritResp proto.InternalMessageInfo

type GetMagicSpiritReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritReq) Reset()         { *m = GetMagicSpiritReq{} }
func (m *GetMagicSpiritReq) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritReq) ProtoMessage()    {}
func (*GetMagicSpiritReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{7}
}
func (m *GetMagicSpiritReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritReq.Unmarshal(m, b)
}
func (m *GetMagicSpiritReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritReq.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritReq.Merge(dst, src)
}
func (m *GetMagicSpiritReq) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritReq.Size(m)
}
func (m *GetMagicSpiritReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritReq proto.InternalMessageInfo

type GetMagicSpiritResp struct {
	MagicSpirit          []*MagicSpirit `protobuf:"bytes,1,rep,name=magic_spirit,json=magicSpirit,proto3" json:"magic_spirit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetMagicSpiritResp) Reset()         { *m = GetMagicSpiritResp{} }
func (m *GetMagicSpiritResp) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritResp) ProtoMessage()    {}
func (*GetMagicSpiritResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{8}
}
func (m *GetMagicSpiritResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritResp.Unmarshal(m, b)
}
func (m *GetMagicSpiritResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritResp.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritResp.Merge(dst, src)
}
func (m *GetMagicSpiritResp) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritResp.Size(m)
}
func (m *GetMagicSpiritResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritResp proto.InternalMessageInfo

func (m *GetMagicSpiritResp) GetMagicSpirit() []*MagicSpirit {
	if m != nil {
		return m.MagicSpirit
	}
	return nil
}

type UpdateMagicSpiritReq struct {
	MagicSpirit          []*MagicSpiritTmp `protobuf:"bytes,1,rep,name=magic_spirit,json=magicSpirit,proto3" json:"magic_spirit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateMagicSpiritReq) Reset()         { *m = UpdateMagicSpiritReq{} }
func (m *UpdateMagicSpiritReq) String() string { return proto.CompactTextString(m) }
func (*UpdateMagicSpiritReq) ProtoMessage()    {}
func (*UpdateMagicSpiritReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{9}
}
func (m *UpdateMagicSpiritReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMagicSpiritReq.Unmarshal(m, b)
}
func (m *UpdateMagicSpiritReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMagicSpiritReq.Marshal(b, m, deterministic)
}
func (dst *UpdateMagicSpiritReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMagicSpiritReq.Merge(dst, src)
}
func (m *UpdateMagicSpiritReq) XXX_Size() int {
	return xxx_messageInfo_UpdateMagicSpiritReq.Size(m)
}
func (m *UpdateMagicSpiritReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMagicSpiritReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMagicSpiritReq proto.InternalMessageInfo

func (m *UpdateMagicSpiritReq) GetMagicSpirit() []*MagicSpiritTmp {
	if m != nil {
		return m.MagicSpirit
	}
	return nil
}

type UpdateMagicSpiritResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateMagicSpiritResp) Reset()         { *m = UpdateMagicSpiritResp{} }
func (m *UpdateMagicSpiritResp) String() string { return proto.CompactTextString(m) }
func (*UpdateMagicSpiritResp) ProtoMessage()    {}
func (*UpdateMagicSpiritResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{10}
}
func (m *UpdateMagicSpiritResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMagicSpiritResp.Unmarshal(m, b)
}
func (m *UpdateMagicSpiritResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMagicSpiritResp.Marshal(b, m, deterministic)
}
func (dst *UpdateMagicSpiritResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMagicSpiritResp.Merge(dst, src)
}
func (m *UpdateMagicSpiritResp) XXX_Size() int {
	return xxx_messageInfo_UpdateMagicSpiritResp.Size(m)
}
func (m *UpdateMagicSpiritResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMagicSpiritResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMagicSpiritResp proto.InternalMessageInfo

type MagicSpiritPondItem struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	MagicSpiritId        uint32   `protobuf:"varint,2,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	PresentId            uint32   `protobuf:"varint,3,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	Weight               uint32   `protobuf:"varint,4,opt,name=weight,proto3" json:"weight,omitempty"`
	PrizeLevel           uint32   `protobuf:"varint,5,opt,name=prize_level,json=prizeLevel,proto3" json:"prize_level,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	EffectTime           uint32   `protobuf:"varint,9,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	BreakingNewId        uint32   `protobuf:"varint,10,opt,name=breaking_new_id,json=breakingNewId,proto3" json:"breaking_new_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MagicSpiritPondItem) Reset()         { *m = MagicSpiritPondItem{} }
func (m *MagicSpiritPondItem) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritPondItem) ProtoMessage()    {}
func (*MagicSpiritPondItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{11}
}
func (m *MagicSpiritPondItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritPondItem.Unmarshal(m, b)
}
func (m *MagicSpiritPondItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritPondItem.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritPondItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritPondItem.Merge(dst, src)
}
func (m *MagicSpiritPondItem) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritPondItem.Size(m)
}
func (m *MagicSpiritPondItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritPondItem.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritPondItem proto.InternalMessageInfo

func (m *MagicSpiritPondItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *MagicSpiritPondItem) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *MagicSpiritPondItem) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *MagicSpiritPondItem) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *MagicSpiritPondItem) GetPrizeLevel() uint32 {
	if m != nil {
		return m.PrizeLevel
	}
	return 0
}

func (m *MagicSpiritPondItem) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *MagicSpiritPondItem) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

func (m *MagicSpiritPondItem) GetBreakingNewId() uint32 {
	if m != nil {
		return m.BreakingNewId
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type AddMagicSpiritPondReq struct {
	MagicSpiritPondItems []*MagicSpiritPondItem `protobuf:"bytes,1,rep,name=MagicSpiritPondItems,proto3" json:"MagicSpiritPondItems,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AddMagicSpiritPondReq) Reset()         { *m = AddMagicSpiritPondReq{} }
func (m *AddMagicSpiritPondReq) String() string { return proto.CompactTextString(m) }
func (*AddMagicSpiritPondReq) ProtoMessage()    {}
func (*AddMagicSpiritPondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{12}
}
func (m *AddMagicSpiritPondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMagicSpiritPondReq.Unmarshal(m, b)
}
func (m *AddMagicSpiritPondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMagicSpiritPondReq.Marshal(b, m, deterministic)
}
func (dst *AddMagicSpiritPondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMagicSpiritPondReq.Merge(dst, src)
}
func (m *AddMagicSpiritPondReq) XXX_Size() int {
	return xxx_messageInfo_AddMagicSpiritPondReq.Size(m)
}
func (m *AddMagicSpiritPondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMagicSpiritPondReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMagicSpiritPondReq proto.InternalMessageInfo

func (m *AddMagicSpiritPondReq) GetMagicSpiritPondItems() []*MagicSpiritPondItem {
	if m != nil {
		return m.MagicSpiritPondItems
	}
	return nil
}

type AddMagicSpiritPondResp struct {
	MagicSpiritIds       []uint32 `protobuf:"varint,1,rep,packed,name=magic_spirit_ids,json=magicSpiritIds,proto3" json:"magic_spirit_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMagicSpiritPondResp) Reset()         { *m = AddMagicSpiritPondResp{} }
func (m *AddMagicSpiritPondResp) String() string { return proto.CompactTextString(m) }
func (*AddMagicSpiritPondResp) ProtoMessage()    {}
func (*AddMagicSpiritPondResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{13}
}
func (m *AddMagicSpiritPondResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMagicSpiritPondResp.Unmarshal(m, b)
}
func (m *AddMagicSpiritPondResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMagicSpiritPondResp.Marshal(b, m, deterministic)
}
func (dst *AddMagicSpiritPondResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMagicSpiritPondResp.Merge(dst, src)
}
func (m *AddMagicSpiritPondResp) XXX_Size() int {
	return xxx_messageInfo_AddMagicSpiritPondResp.Size(m)
}
func (m *AddMagicSpiritPondResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMagicSpiritPondResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMagicSpiritPondResp proto.InternalMessageInfo

func (m *AddMagicSpiritPondResp) GetMagicSpiritIds() []uint32 {
	if m != nil {
		return m.MagicSpiritIds
	}
	return nil
}

type UpdateMagicSpiritPondReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	PresentId            uint32   `protobuf:"varint,2,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	Weight               uint32   `protobuf:"varint,3,opt,name=weight,proto3" json:"weight,omitempty"`
	PrizeLevel           uint32   `protobuf:"varint,5,opt,name=prize_level,json=prizeLevel,proto3" json:"prize_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateMagicSpiritPondReq) Reset()         { *m = UpdateMagicSpiritPondReq{} }
func (m *UpdateMagicSpiritPondReq) String() string { return proto.CompactTextString(m) }
func (*UpdateMagicSpiritPondReq) ProtoMessage()    {}
func (*UpdateMagicSpiritPondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{14}
}
func (m *UpdateMagicSpiritPondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMagicSpiritPondReq.Unmarshal(m, b)
}
func (m *UpdateMagicSpiritPondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMagicSpiritPondReq.Marshal(b, m, deterministic)
}
func (dst *UpdateMagicSpiritPondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMagicSpiritPondReq.Merge(dst, src)
}
func (m *UpdateMagicSpiritPondReq) XXX_Size() int {
	return xxx_messageInfo_UpdateMagicSpiritPondReq.Size(m)
}
func (m *UpdateMagicSpiritPondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMagicSpiritPondReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMagicSpiritPondReq proto.InternalMessageInfo

func (m *UpdateMagicSpiritPondReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UpdateMagicSpiritPondReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *UpdateMagicSpiritPondReq) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *UpdateMagicSpiritPondReq) GetPrizeLevel() uint32 {
	if m != nil {
		return m.PrizeLevel
	}
	return 0
}

type UpdateMagicSpiritPondResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateMagicSpiritPondResp) Reset()         { *m = UpdateMagicSpiritPondResp{} }
func (m *UpdateMagicSpiritPondResp) String() string { return proto.CompactTextString(m) }
func (*UpdateMagicSpiritPondResp) ProtoMessage()    {}
func (*UpdateMagicSpiritPondResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{15}
}
func (m *UpdateMagicSpiritPondResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMagicSpiritPondResp.Unmarshal(m, b)
}
func (m *UpdateMagicSpiritPondResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMagicSpiritPondResp.Marshal(b, m, deterministic)
}
func (dst *UpdateMagicSpiritPondResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMagicSpiritPondResp.Merge(dst, src)
}
func (m *UpdateMagicSpiritPondResp) XXX_Size() int {
	return xxx_messageInfo_UpdateMagicSpiritPondResp.Size(m)
}
func (m *UpdateMagicSpiritPondResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMagicSpiritPondResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMagicSpiritPondResp proto.InternalMessageInfo

type GetMagicSpiritPondReq struct {
	MagicSpiritId        uint32   `protobuf:"varint,1,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritPondReq) Reset()         { *m = GetMagicSpiritPondReq{} }
func (m *GetMagicSpiritPondReq) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritPondReq) ProtoMessage()    {}
func (*GetMagicSpiritPondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{16}
}
func (m *GetMagicSpiritPondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritPondReq.Unmarshal(m, b)
}
func (m *GetMagicSpiritPondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritPondReq.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritPondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritPondReq.Merge(dst, src)
}
func (m *GetMagicSpiritPondReq) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritPondReq.Size(m)
}
func (m *GetMagicSpiritPondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritPondReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritPondReq proto.InternalMessageInfo

func (m *GetMagicSpiritPondReq) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

type GetMagicSpiritPondResp struct {
	MagicSpiritPond      []*MagicSpiritPondItem `protobuf:"bytes,1,rep,name=magic_spirit_pond,json=magicSpiritPond,proto3" json:"magic_spirit_pond,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetMagicSpiritPondResp) Reset()         { *m = GetMagicSpiritPondResp{} }
func (m *GetMagicSpiritPondResp) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritPondResp) ProtoMessage()    {}
func (*GetMagicSpiritPondResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{17}
}
func (m *GetMagicSpiritPondResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritPondResp.Unmarshal(m, b)
}
func (m *GetMagicSpiritPondResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritPondResp.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritPondResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritPondResp.Merge(dst, src)
}
func (m *GetMagicSpiritPondResp) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritPondResp.Size(m)
}
func (m *GetMagicSpiritPondResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritPondResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritPondResp proto.InternalMessageInfo

func (m *GetMagicSpiritPondResp) GetMagicSpiritPond() []*MagicSpiritPondItem {
	if m != nil {
		return m.MagicSpiritPond
	}
	return nil
}

type DelMagicSpiritPondReq struct {
	MagicSpiritId        uint32   `protobuf:"varint,1,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	ItemIds              []uint32 `protobuf:"varint,2,rep,packed,name=item_ids,json=itemIds,proto3" json:"item_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMagicSpiritPondReq) Reset()         { *m = DelMagicSpiritPondReq{} }
func (m *DelMagicSpiritPondReq) String() string { return proto.CompactTextString(m) }
func (*DelMagicSpiritPondReq) ProtoMessage()    {}
func (*DelMagicSpiritPondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{18}
}
func (m *DelMagicSpiritPondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMagicSpiritPondReq.Unmarshal(m, b)
}
func (m *DelMagicSpiritPondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMagicSpiritPondReq.Marshal(b, m, deterministic)
}
func (dst *DelMagicSpiritPondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMagicSpiritPondReq.Merge(dst, src)
}
func (m *DelMagicSpiritPondReq) XXX_Size() int {
	return xxx_messageInfo_DelMagicSpiritPondReq.Size(m)
}
func (m *DelMagicSpiritPondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMagicSpiritPondReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelMagicSpiritPondReq proto.InternalMessageInfo

func (m *DelMagicSpiritPondReq) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *DelMagicSpiritPondReq) GetItemIds() []uint32 {
	if m != nil {
		return m.ItemIds
	}
	return nil
}

type DelMagicSpiritPondResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMagicSpiritPondResp) Reset()         { *m = DelMagicSpiritPondResp{} }
func (m *DelMagicSpiritPondResp) String() string { return proto.CompactTextString(m) }
func (*DelMagicSpiritPondResp) ProtoMessage()    {}
func (*DelMagicSpiritPondResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{19}
}
func (m *DelMagicSpiritPondResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMagicSpiritPondResp.Unmarshal(m, b)
}
func (m *DelMagicSpiritPondResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMagicSpiritPondResp.Marshal(b, m, deterministic)
}
func (dst *DelMagicSpiritPondResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMagicSpiritPondResp.Merge(dst, src)
}
func (m *DelMagicSpiritPondResp) XXX_Size() int {
	return xxx_messageInfo_DelMagicSpiritPondResp.Size(m)
}
func (m *DelMagicSpiritPondResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMagicSpiritPondResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelMagicSpiritPondResp proto.InternalMessageInfo

// ============= 新增未开始tab ====================
type GetMagicSpiritConfTmpReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritConfTmpReq) Reset()         { *m = GetMagicSpiritConfTmpReq{} }
func (m *GetMagicSpiritConfTmpReq) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritConfTmpReq) ProtoMessage()    {}
func (*GetMagicSpiritConfTmpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{20}
}
func (m *GetMagicSpiritConfTmpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritConfTmpReq.Unmarshal(m, b)
}
func (m *GetMagicSpiritConfTmpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritConfTmpReq.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritConfTmpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritConfTmpReq.Merge(dst, src)
}
func (m *GetMagicSpiritConfTmpReq) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritConfTmpReq.Size(m)
}
func (m *GetMagicSpiritConfTmpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritConfTmpReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritConfTmpReq proto.InternalMessageInfo

type MagicSpiritConfTmp struct {
	MagicSpiritTmp       *MagicSpiritTmp        `protobuf:"bytes,1,opt,name=magic_spirit_tmp,json=magicSpiritTmp,proto3" json:"magic_spirit_tmp,omitempty"`
	HasPool              bool                   `protobuf:"varint,2,opt,name=has_pool,json=hasPool,proto3" json:"has_pool,omitempty"`
	Pool                 []*MagicSpiritPondItem `protobuf:"bytes,3,rep,name=pool,proto3" json:"pool,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *MagicSpiritConfTmp) Reset()         { *m = MagicSpiritConfTmp{} }
func (m *MagicSpiritConfTmp) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritConfTmp) ProtoMessage()    {}
func (*MagicSpiritConfTmp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{21}
}
func (m *MagicSpiritConfTmp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritConfTmp.Unmarshal(m, b)
}
func (m *MagicSpiritConfTmp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritConfTmp.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritConfTmp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritConfTmp.Merge(dst, src)
}
func (m *MagicSpiritConfTmp) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritConfTmp.Size(m)
}
func (m *MagicSpiritConfTmp) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritConfTmp.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritConfTmp proto.InternalMessageInfo

func (m *MagicSpiritConfTmp) GetMagicSpiritTmp() *MagicSpiritTmp {
	if m != nil {
		return m.MagicSpiritTmp
	}
	return nil
}

func (m *MagicSpiritConfTmp) GetHasPool() bool {
	if m != nil {
		return m.HasPool
	}
	return false
}

func (m *MagicSpiritConfTmp) GetPool() []*MagicSpiritPondItem {
	if m != nil {
		return m.Pool
	}
	return nil
}

type GetMagicSpiritConfTmpResp struct {
	ConfList             []*MagicSpiritConfTmp `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetMagicSpiritConfTmpResp) Reset()         { *m = GetMagicSpiritConfTmpResp{} }
func (m *GetMagicSpiritConfTmpResp) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritConfTmpResp) ProtoMessage()    {}
func (*GetMagicSpiritConfTmpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{22}
}
func (m *GetMagicSpiritConfTmpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritConfTmpResp.Unmarshal(m, b)
}
func (m *GetMagicSpiritConfTmpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritConfTmpResp.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritConfTmpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritConfTmpResp.Merge(dst, src)
}
func (m *GetMagicSpiritConfTmpResp) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritConfTmpResp.Size(m)
}
func (m *GetMagicSpiritConfTmpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritConfTmpResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritConfTmpResp proto.InternalMessageInfo

func (m *GetMagicSpiritConfTmpResp) GetConfList() []*MagicSpiritConfTmp {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type MagicSpiritBlacklist struct {
	BlacklistId          uint32   `protobuf:"varint,1,opt,name=blacklist_id,json=blacklistId,proto3" json:"blacklist_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoomId               uint32   `protobuf:"varint,3,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	RoomName             string   `protobuf:"bytes,4,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	Ttid                 uint32   `protobuf:"varint,5,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Owner                string   `protobuf:"bytes,6,opt,name=owner,proto3" json:"owner,omitempty"`
	CreateTime           uint32   `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MagicSpiritBlacklist) Reset()         { *m = MagicSpiritBlacklist{} }
func (m *MagicSpiritBlacklist) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritBlacklist) ProtoMessage()    {}
func (*MagicSpiritBlacklist) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{23}
}
func (m *MagicSpiritBlacklist) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritBlacklist.Unmarshal(m, b)
}
func (m *MagicSpiritBlacklist) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritBlacklist.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritBlacklist) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritBlacklist.Merge(dst, src)
}
func (m *MagicSpiritBlacklist) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritBlacklist.Size(m)
}
func (m *MagicSpiritBlacklist) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritBlacklist.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritBlacklist proto.InternalMessageInfo

func (m *MagicSpiritBlacklist) GetBlacklistId() uint32 {
	if m != nil {
		return m.BlacklistId
	}
	return 0
}

func (m *MagicSpiritBlacklist) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MagicSpiritBlacklist) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *MagicSpiritBlacklist) GetRoomName() string {
	if m != nil {
		return m.RoomName
	}
	return ""
}

func (m *MagicSpiritBlacklist) GetTtid() uint32 {
	if m != nil {
		return m.Ttid
	}
	return 0
}

func (m *MagicSpiritBlacklist) GetOwner() string {
	if m != nil {
		return m.Owner
	}
	return ""
}

func (m *MagicSpiritBlacklist) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type AddMagicSpiritBlacklistReq struct {
	Blacklist            []*MagicSpiritBlacklist `protobuf:"bytes,1,rep,name=blacklist,proto3" json:"blacklist,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AddMagicSpiritBlacklistReq) Reset()         { *m = AddMagicSpiritBlacklistReq{} }
func (m *AddMagicSpiritBlacklistReq) String() string { return proto.CompactTextString(m) }
func (*AddMagicSpiritBlacklistReq) ProtoMessage()    {}
func (*AddMagicSpiritBlacklistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{24}
}
func (m *AddMagicSpiritBlacklistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMagicSpiritBlacklistReq.Unmarshal(m, b)
}
func (m *AddMagicSpiritBlacklistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMagicSpiritBlacklistReq.Marshal(b, m, deterministic)
}
func (dst *AddMagicSpiritBlacklistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMagicSpiritBlacklistReq.Merge(dst, src)
}
func (m *AddMagicSpiritBlacklistReq) XXX_Size() int {
	return xxx_messageInfo_AddMagicSpiritBlacklistReq.Size(m)
}
func (m *AddMagicSpiritBlacklistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMagicSpiritBlacklistReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMagicSpiritBlacklistReq proto.InternalMessageInfo

func (m *AddMagicSpiritBlacklistReq) GetBlacklist() []*MagicSpiritBlacklist {
	if m != nil {
		return m.Blacklist
	}
	return nil
}

type AddMagicSpiritBlacklistResp struct {
	BlacklistId          []uint32 `protobuf:"varint,1,rep,packed,name=blacklist_id,json=blacklistId,proto3" json:"blacklist_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMagicSpiritBlacklistResp) Reset()         { *m = AddMagicSpiritBlacklistResp{} }
func (m *AddMagicSpiritBlacklistResp) String() string { return proto.CompactTextString(m) }
func (*AddMagicSpiritBlacklistResp) ProtoMessage()    {}
func (*AddMagicSpiritBlacklistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{25}
}
func (m *AddMagicSpiritBlacklistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMagicSpiritBlacklistResp.Unmarshal(m, b)
}
func (m *AddMagicSpiritBlacklistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMagicSpiritBlacklistResp.Marshal(b, m, deterministic)
}
func (dst *AddMagicSpiritBlacklistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMagicSpiritBlacklistResp.Merge(dst, src)
}
func (m *AddMagicSpiritBlacklistResp) XXX_Size() int {
	return xxx_messageInfo_AddMagicSpiritBlacklistResp.Size(m)
}
func (m *AddMagicSpiritBlacklistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMagicSpiritBlacklistResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMagicSpiritBlacklistResp proto.InternalMessageInfo

func (m *AddMagicSpiritBlacklistResp) GetBlacklistId() []uint32 {
	if m != nil {
		return m.BlacklistId
	}
	return nil
}

type GetMagicSpiritBlackListReq struct {
	PageNum              uint32   `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritBlackListReq) Reset()         { *m = GetMagicSpiritBlackListReq{} }
func (m *GetMagicSpiritBlackListReq) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritBlackListReq) ProtoMessage()    {}
func (*GetMagicSpiritBlackListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{26}
}
func (m *GetMagicSpiritBlackListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritBlackListReq.Unmarshal(m, b)
}
func (m *GetMagicSpiritBlackListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritBlackListReq.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritBlackListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritBlackListReq.Merge(dst, src)
}
func (m *GetMagicSpiritBlackListReq) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritBlackListReq.Size(m)
}
func (m *GetMagicSpiritBlackListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritBlackListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritBlackListReq proto.InternalMessageInfo

func (m *GetMagicSpiritBlackListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetMagicSpiritBlackListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetMagicSpiritBlackListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMagicSpiritBlackListResp struct {
	Total                uint32                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Blacklist            []*MagicSpiritBlacklist `protobuf:"bytes,2,rep,name=blacklist,proto3" json:"blacklist,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetMagicSpiritBlackListResp) Reset()         { *m = GetMagicSpiritBlackListResp{} }
func (m *GetMagicSpiritBlackListResp) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritBlackListResp) ProtoMessage()    {}
func (*GetMagicSpiritBlackListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{27}
}
func (m *GetMagicSpiritBlackListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritBlackListResp.Unmarshal(m, b)
}
func (m *GetMagicSpiritBlackListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritBlackListResp.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritBlackListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritBlackListResp.Merge(dst, src)
}
func (m *GetMagicSpiritBlackListResp) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritBlackListResp.Size(m)
}
func (m *GetMagicSpiritBlackListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritBlackListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritBlackListResp proto.InternalMessageInfo

func (m *GetMagicSpiritBlackListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetMagicSpiritBlackListResp) GetBlacklist() []*MagicSpiritBlacklist {
	if m != nil {
		return m.Blacklist
	}
	return nil
}

type DelMagicSpiritBlacklistReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMagicSpiritBlacklistReq) Reset()         { *m = DelMagicSpiritBlacklistReq{} }
func (m *DelMagicSpiritBlacklistReq) String() string { return proto.CompactTextString(m) }
func (*DelMagicSpiritBlacklistReq) ProtoMessage()    {}
func (*DelMagicSpiritBlacklistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{28}
}
func (m *DelMagicSpiritBlacklistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMagicSpiritBlacklistReq.Unmarshal(m, b)
}
func (m *DelMagicSpiritBlacklistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMagicSpiritBlacklistReq.Marshal(b, m, deterministic)
}
func (dst *DelMagicSpiritBlacklistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMagicSpiritBlacklistReq.Merge(dst, src)
}
func (m *DelMagicSpiritBlacklistReq) XXX_Size() int {
	return xxx_messageInfo_DelMagicSpiritBlacklistReq.Size(m)
}
func (m *DelMagicSpiritBlacklistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMagicSpiritBlacklistReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelMagicSpiritBlacklistReq proto.InternalMessageInfo

func (m *DelMagicSpiritBlacklistReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type DelMagicSpiritBlacklistResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMagicSpiritBlacklistResp) Reset()         { *m = DelMagicSpiritBlacklistResp{} }
func (m *DelMagicSpiritBlacklistResp) String() string { return proto.CompactTextString(m) }
func (*DelMagicSpiritBlacklistResp) ProtoMessage()    {}
func (*DelMagicSpiritBlacklistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{29}
}
func (m *DelMagicSpiritBlacklistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMagicSpiritBlacklistResp.Unmarshal(m, b)
}
func (m *DelMagicSpiritBlacklistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMagicSpiritBlacklistResp.Marshal(b, m, deterministic)
}
func (dst *DelMagicSpiritBlacklistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMagicSpiritBlacklistResp.Merge(dst, src)
}
func (m *DelMagicSpiritBlacklistResp) XXX_Size() int {
	return xxx_messageInfo_DelMagicSpiritBlacklistResp.Size(m)
}
func (m *DelMagicSpiritBlacklistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMagicSpiritBlacklistResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelMagicSpiritBlacklistResp proto.InternalMessageInfo

type CommonConf struct {
	ConfType             uint32   `protobuf:"varint,1,opt,name=conf_type,json=confType,proto3" json:"conf_type,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonConf) Reset()         { *m = CommonConf{} }
func (m *CommonConf) String() string { return proto.CompactTextString(m) }
func (*CommonConf) ProtoMessage()    {}
func (*CommonConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{30}
}
func (m *CommonConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonConf.Unmarshal(m, b)
}
func (m *CommonConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonConf.Marshal(b, m, deterministic)
}
func (dst *CommonConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonConf.Merge(dst, src)
}
func (m *CommonConf) XXX_Size() int {
	return xxx_messageInfo_CommonConf.Size(m)
}
func (m *CommonConf) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonConf.DiscardUnknown(m)
}

var xxx_messageInfo_CommonConf proto.InternalMessageInfo

func (m *CommonConf) GetConfType() uint32 {
	if m != nil {
		return m.ConfType
	}
	return 0
}

func (m *CommonConf) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type GetCommonConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCommonConfReq) Reset()         { *m = GetCommonConfReq{} }
func (m *GetCommonConfReq) String() string { return proto.CompactTextString(m) }
func (*GetCommonConfReq) ProtoMessage()    {}
func (*GetCommonConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{31}
}
func (m *GetCommonConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommonConfReq.Unmarshal(m, b)
}
func (m *GetCommonConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommonConfReq.Marshal(b, m, deterministic)
}
func (dst *GetCommonConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommonConfReq.Merge(dst, src)
}
func (m *GetCommonConfReq) XXX_Size() int {
	return xxx_messageInfo_GetCommonConfReq.Size(m)
}
func (m *GetCommonConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommonConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommonConfReq proto.InternalMessageInfo

type GetCommonConfResp struct {
	CommonConf           []*CommonConf `protobuf:"bytes,1,rep,name=common_conf,json=commonConf,proto3" json:"common_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetCommonConfResp) Reset()         { *m = GetCommonConfResp{} }
func (m *GetCommonConfResp) String() string { return proto.CompactTextString(m) }
func (*GetCommonConfResp) ProtoMessage()    {}
func (*GetCommonConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{32}
}
func (m *GetCommonConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommonConfResp.Unmarshal(m, b)
}
func (m *GetCommonConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommonConfResp.Marshal(b, m, deterministic)
}
func (dst *GetCommonConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommonConfResp.Merge(dst, src)
}
func (m *GetCommonConfResp) XXX_Size() int {
	return xxx_messageInfo_GetCommonConfResp.Size(m)
}
func (m *GetCommonConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommonConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommonConfResp proto.InternalMessageInfo

func (m *GetCommonConfResp) GetCommonConf() []*CommonConf {
	if m != nil {
		return m.CommonConf
	}
	return nil
}

type SetCommonConfReq struct {
	CommonConf           []*CommonConf `protobuf:"bytes,1,rep,name=common_conf,json=commonConf,proto3" json:"common_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetCommonConfReq) Reset()         { *m = SetCommonConfReq{} }
func (m *SetCommonConfReq) String() string { return proto.CompactTextString(m) }
func (*SetCommonConfReq) ProtoMessage()    {}
func (*SetCommonConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{33}
}
func (m *SetCommonConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCommonConfReq.Unmarshal(m, b)
}
func (m *SetCommonConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCommonConfReq.Marshal(b, m, deterministic)
}
func (dst *SetCommonConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCommonConfReq.Merge(dst, src)
}
func (m *SetCommonConfReq) XXX_Size() int {
	return xxx_messageInfo_SetCommonConfReq.Size(m)
}
func (m *SetCommonConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCommonConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCommonConfReq proto.InternalMessageInfo

func (m *SetCommonConfReq) GetCommonConf() []*CommonConf {
	if m != nil {
		return m.CommonConf
	}
	return nil
}

type SetCommonConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCommonConfResp) Reset()         { *m = SetCommonConfResp{} }
func (m *SetCommonConfResp) String() string { return proto.CompactTextString(m) }
func (*SetCommonConfResp) ProtoMessage()    {}
func (*SetCommonConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{34}
}
func (m *SetCommonConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCommonConfResp.Unmarshal(m, b)
}
func (m *SetCommonConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCommonConfResp.Marshal(b, m, deterministic)
}
func (dst *SetCommonConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCommonConfResp.Merge(dst, src)
}
func (m *SetCommonConfResp) XXX_Size() int {
	return xxx_messageInfo_SetCommonConfResp.Size(m)
}
func (m *SetCommonConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCommonConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCommonConfResp proto.InternalMessageInfo

// 连击信息
type CombInfo struct {
	Duration             uint32   `protobuf:"varint,1,opt,name=duration,proto3" json:"duration,omitempty"`
	CombLevel            uint32   `protobuf:"varint,2,opt,name=comb_level,json=combLevel,proto3" json:"comb_level,omitempty"`
	LevelName            string   `protobuf:"bytes,3,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	LevelUp              bool     `protobuf:"varint,4,opt,name=level_up,json=levelUp,proto3" json:"level_up,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CombInfo) Reset()         { *m = CombInfo{} }
func (m *CombInfo) String() string { return proto.CompactTextString(m) }
func (*CombInfo) ProtoMessage()    {}
func (*CombInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{35}
}
func (m *CombInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CombInfo.Unmarshal(m, b)
}
func (m *CombInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CombInfo.Marshal(b, m, deterministic)
}
func (dst *CombInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CombInfo.Merge(dst, src)
}
func (m *CombInfo) XXX_Size() int {
	return xxx_messageInfo_CombInfo.Size(m)
}
func (m *CombInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CombInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CombInfo proto.InternalMessageInfo

func (m *CombInfo) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *CombInfo) GetCombLevel() uint32 {
	if m != nil {
		return m.CombLevel
	}
	return 0
}

func (m *CombInfo) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *CombInfo) GetLevelUp() bool {
	if m != nil {
		return m.LevelUp
	}
	return false
}

// 实际送礼信息
type PresentSendInfo struct {
	PresentOrderId       string   `protobuf:"bytes,1,opt,name=present_order_id,json=presentOrderId,proto3" json:"present_order_id,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	GiftId               uint32   `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Cnt                  uint32   `protobuf:"varint,4,opt,name=cnt,proto3" json:"cnt,omitempty"`
	AwardEffect          uint32   `protobuf:"varint,5,opt,name=award_effect,json=awardEffect,proto3" json:"award_effect,omitempty"`
	TotalPrice           uint32   `protobuf:"varint,6,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	DealToken            string   `protobuf:"bytes,7,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentSendInfo) Reset()         { *m = PresentSendInfo{} }
func (m *PresentSendInfo) String() string { return proto.CompactTextString(m) }
func (*PresentSendInfo) ProtoMessage()    {}
func (*PresentSendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{36}
}
func (m *PresentSendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSendInfo.Unmarshal(m, b)
}
func (m *PresentSendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSendInfo.Marshal(b, m, deterministic)
}
func (dst *PresentSendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSendInfo.Merge(dst, src)
}
func (m *PresentSendInfo) XXX_Size() int {
	return xxx_messageInfo_PresentSendInfo.Size(m)
}
func (m *PresentSendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSendInfo proto.InternalMessageInfo

func (m *PresentSendInfo) GetPresentOrderId() string {
	if m != nil {
		return m.PresentOrderId
	}
	return ""
}

func (m *PresentSendInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *PresentSendInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentSendInfo) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *PresentSendInfo) GetAwardEffect() uint32 {
	if m != nil {
		return m.AwardEffect
	}
	return 0
}

func (m *PresentSendInfo) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *PresentSendInfo) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type SendMagicSpiritReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MagicSpiritId        uint32   `protobuf:"varint,2,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	TargetUidList        []uint32 `protobuf:"varint,3,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	AverageCnt           uint32   `protobuf:"varint,4,opt,name=average_cnt,json=averageCnt,proto3" json:"average_cnt,omitempty"`
	ChannelId            uint32   `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CombCnt              uint32   `protobuf:"varint,6,opt,name=comb_cnt,json=combCnt,proto3" json:"comb_cnt,omitempty"`
	SeparateUnpackGift   bool     `protobuf:"varint,7,opt,name=separate_unpack_gift,json=separateUnpackGift,proto3" json:"separate_unpack_gift,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendMagicSpiritReq) Reset()         { *m = SendMagicSpiritReq{} }
func (m *SendMagicSpiritReq) String() string { return proto.CompactTextString(m) }
func (*SendMagicSpiritReq) ProtoMessage()    {}
func (*SendMagicSpiritReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{37}
}
func (m *SendMagicSpiritReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendMagicSpiritReq.Unmarshal(m, b)
}
func (m *SendMagicSpiritReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendMagicSpiritReq.Marshal(b, m, deterministic)
}
func (dst *SendMagicSpiritReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendMagicSpiritReq.Merge(dst, src)
}
func (m *SendMagicSpiritReq) XXX_Size() int {
	return xxx_messageInfo_SendMagicSpiritReq.Size(m)
}
func (m *SendMagicSpiritReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendMagicSpiritReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendMagicSpiritReq proto.InternalMessageInfo

func (m *SendMagicSpiritReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendMagicSpiritReq) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *SendMagicSpiritReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *SendMagicSpiritReq) GetAverageCnt() uint32 {
	if m != nil {
		return m.AverageCnt
	}
	return 0
}

func (m *SendMagicSpiritReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendMagicSpiritReq) GetCombCnt() uint32 {
	if m != nil {
		return m.CombCnt
	}
	return 0
}

func (m *SendMagicSpiritReq) GetSeparateUnpackGift() bool {
	if m != nil {
		return m.SeparateUnpackGift
	}
	return false
}

type SendMagicSpiritResp struct {
	SendInfoList         []*PresentSendInfo `protobuf:"bytes,1,rep,name=send_info_list,json=sendInfoList,proto3" json:"send_info_list,omitempty"`
	CombInfo             *CombInfo          `protobuf:"bytes,2,opt,name=comb_info,json=combInfo,proto3" json:"comb_info,omitempty"`
	SendTime             uint32             `protobuf:"varint,3,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	MagicOrderId         string             `protobuf:"bytes,4,opt,name=magic_order_id,json=magicOrderId,proto3" json:"magic_order_id,omitempty"`
	MagicConf            *MagicSpirit       `protobuf:"bytes,5,opt,name=magic_conf,json=magicConf,proto3" json:"magic_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SendMagicSpiritResp) Reset()         { *m = SendMagicSpiritResp{} }
func (m *SendMagicSpiritResp) String() string { return proto.CompactTextString(m) }
func (*SendMagicSpiritResp) ProtoMessage()    {}
func (*SendMagicSpiritResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{38}
}
func (m *SendMagicSpiritResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendMagicSpiritResp.Unmarshal(m, b)
}
func (m *SendMagicSpiritResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendMagicSpiritResp.Marshal(b, m, deterministic)
}
func (dst *SendMagicSpiritResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendMagicSpiritResp.Merge(dst, src)
}
func (m *SendMagicSpiritResp) XXX_Size() int {
	return xxx_messageInfo_SendMagicSpiritResp.Size(m)
}
func (m *SendMagicSpiritResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendMagicSpiritResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendMagicSpiritResp proto.InternalMessageInfo

func (m *SendMagicSpiritResp) GetSendInfoList() []*PresentSendInfo {
	if m != nil {
		return m.SendInfoList
	}
	return nil
}

func (m *SendMagicSpiritResp) GetCombInfo() *CombInfo {
	if m != nil {
		return m.CombInfo
	}
	return nil
}

func (m *SendMagicSpiritResp) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *SendMagicSpiritResp) GetMagicOrderId() string {
	if m != nil {
		return m.MagicOrderId
	}
	return ""
}

func (m *SendMagicSpiritResp) GetMagicConf() *MagicSpirit {
	if m != nil {
		return m.MagicConf
	}
	return nil
}

type CheckIfSendMagicWithSourceReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MagicSpiritId        uint32   `protobuf:"varint,3,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	Amount               uint32   `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	Source               uint32   `protobuf:"varint,5,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfSendMagicWithSourceReq) Reset()         { *m = CheckIfSendMagicWithSourceReq{} }
func (m *CheckIfSendMagicWithSourceReq) String() string { return proto.CompactTextString(m) }
func (*CheckIfSendMagicWithSourceReq) ProtoMessage()    {}
func (*CheckIfSendMagicWithSourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{39}
}
func (m *CheckIfSendMagicWithSourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfSendMagicWithSourceReq.Unmarshal(m, b)
}
func (m *CheckIfSendMagicWithSourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfSendMagicWithSourceReq.Marshal(b, m, deterministic)
}
func (dst *CheckIfSendMagicWithSourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfSendMagicWithSourceReq.Merge(dst, src)
}
func (m *CheckIfSendMagicWithSourceReq) XXX_Size() int {
	return xxx_messageInfo_CheckIfSendMagicWithSourceReq.Size(m)
}
func (m *CheckIfSendMagicWithSourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfSendMagicWithSourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfSendMagicWithSourceReq proto.InternalMessageInfo

func (m *CheckIfSendMagicWithSourceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckIfSendMagicWithSourceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckIfSendMagicWithSourceReq) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *CheckIfSendMagicWithSourceReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *CheckIfSendMagicWithSourceReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type CheckIfSendMagicWithSourceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfSendMagicWithSourceResp) Reset()         { *m = CheckIfSendMagicWithSourceResp{} }
func (m *CheckIfSendMagicWithSourceResp) String() string { return proto.CompactTextString(m) }
func (*CheckIfSendMagicWithSourceResp) ProtoMessage()    {}
func (*CheckIfSendMagicWithSourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{40}
}
func (m *CheckIfSendMagicWithSourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfSendMagicWithSourceResp.Unmarshal(m, b)
}
func (m *CheckIfSendMagicWithSourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfSendMagicWithSourceResp.Marshal(b, m, deterministic)
}
func (dst *CheckIfSendMagicWithSourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfSendMagicWithSourceResp.Merge(dst, src)
}
func (m *CheckIfSendMagicWithSourceResp) XXX_Size() int {
	return xxx_messageInfo_CheckIfSendMagicWithSourceResp.Size(m)
}
func (m *CheckIfSendMagicWithSourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfSendMagicWithSourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfSendMagicWithSourceResp proto.InternalMessageInfo

type SendMagicWithSourceReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MagicSpiritId        uint32   `protobuf:"varint,2,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	TargetUidList        []uint32 `protobuf:"varint,3,rep,packed,name=target_uid_list,json=targetUidList,proto3" json:"target_uid_list,omitempty"`
	AverageCnt           uint32   `protobuf:"varint,4,opt,name=average_cnt,json=averageCnt,proto3" json:"average_cnt,omitempty"`
	ChannelId            uint32   `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PayOrderId           string   `protobuf:"bytes,6,opt,name=pay_order_id,json=payOrderId,proto3" json:"pay_order_id,omitempty"`
	DealToken            string   `protobuf:"bytes,7,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	Source               uint32   `protobuf:"varint,8,opt,name=source,proto3" json:"source,omitempty"`
	OutsideTime          int64    `protobuf:"varint,9,opt,name=outside_time,json=outsideTime,proto3" json:"outside_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendMagicWithSourceReq) Reset()         { *m = SendMagicWithSourceReq{} }
func (m *SendMagicWithSourceReq) String() string { return proto.CompactTextString(m) }
func (*SendMagicWithSourceReq) ProtoMessage()    {}
func (*SendMagicWithSourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{41}
}
func (m *SendMagicWithSourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendMagicWithSourceReq.Unmarshal(m, b)
}
func (m *SendMagicWithSourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendMagicWithSourceReq.Marshal(b, m, deterministic)
}
func (dst *SendMagicWithSourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendMagicWithSourceReq.Merge(dst, src)
}
func (m *SendMagicWithSourceReq) XXX_Size() int {
	return xxx_messageInfo_SendMagicWithSourceReq.Size(m)
}
func (m *SendMagicWithSourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendMagicWithSourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendMagicWithSourceReq proto.InternalMessageInfo

func (m *SendMagicWithSourceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendMagicWithSourceReq) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *SendMagicWithSourceReq) GetTargetUidList() []uint32 {
	if m != nil {
		return m.TargetUidList
	}
	return nil
}

func (m *SendMagicWithSourceReq) GetAverageCnt() uint32 {
	if m != nil {
		return m.AverageCnt
	}
	return 0
}

func (m *SendMagicWithSourceReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendMagicWithSourceReq) GetPayOrderId() string {
	if m != nil {
		return m.PayOrderId
	}
	return ""
}

func (m *SendMagicWithSourceReq) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *SendMagicWithSourceReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *SendMagicWithSourceReq) GetOutsideTime() int64 {
	if m != nil {
		return m.OutsideTime
	}
	return 0
}

type SendMagicWithSourceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendMagicWithSourceResp) Reset()         { *m = SendMagicWithSourceResp{} }
func (m *SendMagicWithSourceResp) String() string { return proto.CompactTextString(m) }
func (*SendMagicWithSourceResp) ProtoMessage()    {}
func (*SendMagicWithSourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{42}
}
func (m *SendMagicWithSourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendMagicWithSourceResp.Unmarshal(m, b)
}
func (m *SendMagicWithSourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendMagicWithSourceResp.Marshal(b, m, deterministic)
}
func (dst *SendMagicWithSourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendMagicWithSourceResp.Merge(dst, src)
}
func (m *SendMagicWithSourceResp) XXX_Size() int {
	return xxx_messageInfo_SendMagicWithSourceResp.Size(m)
}
func (m *SendMagicWithSourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendMagicWithSourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendMagicWithSourceResp proto.InternalMessageInfo

// 开箱
type SendUnpackGiftReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ItemOrderId          string   `protobuf:"bytes,3,opt,name=item_order_id,json=itemOrderId,proto3" json:"item_order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendUnpackGiftReq) Reset()         { *m = SendUnpackGiftReq{} }
func (m *SendUnpackGiftReq) String() string { return proto.CompactTextString(m) }
func (*SendUnpackGiftReq) ProtoMessage()    {}
func (*SendUnpackGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{43}
}
func (m *SendUnpackGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendUnpackGiftReq.Unmarshal(m, b)
}
func (m *SendUnpackGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendUnpackGiftReq.Marshal(b, m, deterministic)
}
func (dst *SendUnpackGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendUnpackGiftReq.Merge(dst, src)
}
func (m *SendUnpackGiftReq) XXX_Size() int {
	return xxx_messageInfo_SendUnpackGiftReq.Size(m)
}
func (m *SendUnpackGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendUnpackGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendUnpackGiftReq proto.InternalMessageInfo

func (m *SendUnpackGiftReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendUnpackGiftReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendUnpackGiftReq) GetItemOrderId() string {
	if m != nil {
		return m.ItemOrderId
	}
	return ""
}

type SendUnpackGiftResp struct {
	SendOpt              *present_middleware.SendMagicSpiritOpt `protobuf:"bytes,1,opt,name=send_opt,json=sendOpt,proto3" json:"send_opt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *SendUnpackGiftResp) Reset()         { *m = SendUnpackGiftResp{} }
func (m *SendUnpackGiftResp) String() string { return proto.CompactTextString(m) }
func (*SendUnpackGiftResp) ProtoMessage()    {}
func (*SendUnpackGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{44}
}
func (m *SendUnpackGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendUnpackGiftResp.Unmarshal(m, b)
}
func (m *SendUnpackGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendUnpackGiftResp.Marshal(b, m, deterministic)
}
func (dst *SendUnpackGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendUnpackGiftResp.Merge(dst, src)
}
func (m *SendUnpackGiftResp) XXX_Size() int {
	return xxx_messageInfo_SendUnpackGiftResp.Size(m)
}
func (m *SendUnpackGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendUnpackGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendUnpackGiftResp proto.InternalMessageInfo

func (m *SendUnpackGiftResp) GetSendOpt() *present_middleware.SendMagicSpiritOpt {
	if m != nil {
		return m.SendOpt
	}
	return nil
}

type MagicSpiritForCli struct {
	MagicSpiritId    uint32   `protobuf:"varint,1,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	Name             string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconUrl          string   `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Price            uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Rank             uint32   `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`
	EffectBegin      uint32   `protobuf:"varint,6,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd        uint32   `protobuf:"varint,7,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	DescribeImageUrl string   `protobuf:"bytes,8,opt,name=describe_image_url,json=describeImageUrl,proto3" json:"describe_image_url,omitempty"`
	DescribeJumpUrl  string   `protobuf:"bytes,9,opt,name=describe_jump_url,json=describeJumpUrl,proto3" json:"describe_jump_url,omitempty"`
	VfxResource      string   `protobuf:"bytes,10,opt,name=vfx_resource,json=vfxResource,proto3" json:"vfx_resource,omitempty"`
	VfxResourceMd5   string   `protobuf:"bytes,11,opt,name=vfx_resource_md5,json=vfxResourceMd5,proto3" json:"vfx_resource_md5,omitempty"`
	PresentIds       []uint32 `protobuf:"varint,12,rep,packed,name=present_ids,json=presentIds,proto3" json:"present_ids,omitempty"`
	RankFloat        float32  `protobuf:"fixed32,13,opt,name=rank_float,json=rankFloat,proto3" json:"rank_float,omitempty"`
	// 6.36.0 增加活动链接字段
	DescActivityUrl string `protobuf:"bytes,14,opt,name=desc_activity_url,json=descActivityUrl,proto3" json:"desc_activity_url,omitempty"`
	// 6.56.3 新增
	ShowEffectEnd        bool                    `protobuf:"varint,15,opt,name=show_effect_end,json=showEffectEnd,proto3" json:"show_effect_end,omitempty"`
	ActivityCfg          *MagicSpiritActivityCfg `protobuf:"bytes,16,opt,name=activity_cfg,json=activityCfg,proto3" json:"activity_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *MagicSpiritForCli) Reset()         { *m = MagicSpiritForCli{} }
func (m *MagicSpiritForCli) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritForCli) ProtoMessage()    {}
func (*MagicSpiritForCli) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{45}
}
func (m *MagicSpiritForCli) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritForCli.Unmarshal(m, b)
}
func (m *MagicSpiritForCli) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritForCli.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritForCli) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritForCli.Merge(dst, src)
}
func (m *MagicSpiritForCli) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritForCli.Size(m)
}
func (m *MagicSpiritForCli) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritForCli.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritForCli proto.InternalMessageInfo

func (m *MagicSpiritForCli) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *MagicSpiritForCli) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MagicSpiritForCli) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *MagicSpiritForCli) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *MagicSpiritForCli) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *MagicSpiritForCli) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *MagicSpiritForCli) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *MagicSpiritForCli) GetDescribeImageUrl() string {
	if m != nil {
		return m.DescribeImageUrl
	}
	return ""
}

func (m *MagicSpiritForCli) GetDescribeJumpUrl() string {
	if m != nil {
		return m.DescribeJumpUrl
	}
	return ""
}

func (m *MagicSpiritForCli) GetVfxResource() string {
	if m != nil {
		return m.VfxResource
	}
	return ""
}

func (m *MagicSpiritForCli) GetVfxResourceMd5() string {
	if m != nil {
		return m.VfxResourceMd5
	}
	return ""
}

func (m *MagicSpiritForCli) GetPresentIds() []uint32 {
	if m != nil {
		return m.PresentIds
	}
	return nil
}

func (m *MagicSpiritForCli) GetRankFloat() float32 {
	if m != nil {
		return m.RankFloat
	}
	return 0
}

func (m *MagicSpiritForCli) GetDescActivityUrl() string {
	if m != nil {
		return m.DescActivityUrl
	}
	return ""
}

func (m *MagicSpiritForCli) GetShowEffectEnd() bool {
	if m != nil {
		return m.ShowEffectEnd
	}
	return false
}

func (m *MagicSpiritForCli) GetActivityCfg() *MagicSpiritActivityCfg {
	if m != nil {
		return m.ActivityCfg
	}
	return nil
}

type GetMagicSpiritForCliReq struct {
	CurrentVersion       uint32   `protobuf:"varint,1,opt,name=current_version,json=currentVersion,proto3" json:"current_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritForCliReq) Reset()         { *m = GetMagicSpiritForCliReq{} }
func (m *GetMagicSpiritForCliReq) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritForCliReq) ProtoMessage()    {}
func (*GetMagicSpiritForCliReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{46}
}
func (m *GetMagicSpiritForCliReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritForCliReq.Unmarshal(m, b)
}
func (m *GetMagicSpiritForCliReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritForCliReq.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritForCliReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritForCliReq.Merge(dst, src)
}
func (m *GetMagicSpiritForCliReq) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritForCliReq.Size(m)
}
func (m *GetMagicSpiritForCliReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritForCliReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritForCliReq proto.InternalMessageInfo

func (m *GetMagicSpiritForCliReq) GetCurrentVersion() uint32 {
	if m != nil {
		return m.CurrentVersion
	}
	return 0
}

type GetMagicSpiritForCliResp struct {
	MagicSpirits         []*MagicSpiritForCli `protobuf:"bytes,1,rep,name=magic_spirits,json=magicSpirits,proto3" json:"magic_spirits,omitempty"`
	CurrentVersion       uint32               `protobuf:"varint,2,opt,name=current_version,json=currentVersion,proto3" json:"current_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetMagicSpiritForCliResp) Reset()         { *m = GetMagicSpiritForCliResp{} }
func (m *GetMagicSpiritForCliResp) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritForCliResp) ProtoMessage()    {}
func (*GetMagicSpiritForCliResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{47}
}
func (m *GetMagicSpiritForCliResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritForCliResp.Unmarshal(m, b)
}
func (m *GetMagicSpiritForCliResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritForCliResp.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritForCliResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritForCliResp.Merge(dst, src)
}
func (m *GetMagicSpiritForCliResp) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritForCliResp.Size(m)
}
func (m *GetMagicSpiritForCliResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritForCliResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritForCliResp proto.InternalMessageInfo

func (m *GetMagicSpiritForCliResp) GetMagicSpirits() []*MagicSpiritForCli {
	if m != nil {
		return m.MagicSpirits
	}
	return nil
}

func (m *GetMagicSpiritForCliResp) GetCurrentVersion() uint32 {
	if m != nil {
		return m.CurrentVersion
	}
	return 0
}

type GetMagicSpiritUsableReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	OldVersion           bool     `protobuf:"varint,3,opt,name=old_version,json=oldVersion,proto3" json:"old_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritUsableReq) Reset()         { *m = GetMagicSpiritUsableReq{} }
func (m *GetMagicSpiritUsableReq) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritUsableReq) ProtoMessage()    {}
func (*GetMagicSpiritUsableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{48}
}
func (m *GetMagicSpiritUsableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritUsableReq.Unmarshal(m, b)
}
func (m *GetMagicSpiritUsableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritUsableReq.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritUsableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritUsableReq.Merge(dst, src)
}
func (m *GetMagicSpiritUsableReq) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritUsableReq.Size(m)
}
func (m *GetMagicSpiritUsableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritUsableReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritUsableReq proto.InternalMessageInfo

func (m *GetMagicSpiritUsableReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMagicSpiritUsableReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMagicSpiritUsableReq) GetOldVersion() bool {
	if m != nil {
		return m.OldVersion
	}
	return false
}

type GetMagicSpiritUsableResp struct {
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	//  uint32 exp_level    = 2; // 使用这个礼物的最低经验等级
	//  uint32 wealth       = 3; // 使用这个礼物的最低财富值
	//  uint32 charm        = 4; // 使用这个礼物的最低魅力值
	//  uint32 pupil_usable = 5; // 未成年人是否可以使用这个礼物
	Usable               uint32   `protobuf:"varint,6,opt,name=usable,proto3" json:"usable,omitempty"`
	CheckIntervalSec     uint32   `protobuf:"varint,7,opt,name=check_interval_sec,json=checkIntervalSec,proto3" json:"check_interval_sec,omitempty"`
	MagicSpiritIds       []uint32 `protobuf:"varint,8,rep,packed,name=magic_spirit_ids,json=magicSpiritIds,proto3" json:"magic_spirit_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritUsableResp) Reset()         { *m = GetMagicSpiritUsableResp{} }
func (m *GetMagicSpiritUsableResp) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritUsableResp) ProtoMessage()    {}
func (*GetMagicSpiritUsableResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{49}
}
func (m *GetMagicSpiritUsableResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritUsableResp.Unmarshal(m, b)
}
func (m *GetMagicSpiritUsableResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritUsableResp.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritUsableResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritUsableResp.Merge(dst, src)
}
func (m *GetMagicSpiritUsableResp) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritUsableResp.Size(m)
}
func (m *GetMagicSpiritUsableResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritUsableResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritUsableResp proto.InternalMessageInfo

func (m *GetMagicSpiritUsableResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMagicSpiritUsableResp) GetUsable() uint32 {
	if m != nil {
		return m.Usable
	}
	return 0
}

func (m *GetMagicSpiritUsableResp) GetCheckIntervalSec() uint32 {
	if m != nil {
		return m.CheckIntervalSec
	}
	return 0
}

func (m *GetMagicSpiritUsableResp) GetMagicSpiritIds() []uint32 {
	if m != nil {
		return m.MagicSpiritIds
	}
	return nil
}

type GetMagicSpiritOrderTotalReq struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritOrderTotalReq) Reset()         { *m = GetMagicSpiritOrderTotalReq{} }
func (m *GetMagicSpiritOrderTotalReq) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritOrderTotalReq) ProtoMessage()    {}
func (*GetMagicSpiritOrderTotalReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{50}
}
func (m *GetMagicSpiritOrderTotalReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritOrderTotalReq.Unmarshal(m, b)
}
func (m *GetMagicSpiritOrderTotalReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritOrderTotalReq.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritOrderTotalReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritOrderTotalReq.Merge(dst, src)
}
func (m *GetMagicSpiritOrderTotalReq) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritOrderTotalReq.Size(m)
}
func (m *GetMagicSpiritOrderTotalReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritOrderTotalReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritOrderTotalReq proto.InternalMessageInfo

func (m *GetMagicSpiritOrderTotalReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetMagicSpiritOrderTotalReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetMagicSpiritOrderTotalResp struct {
	OrderCnt             uint32   `protobuf:"varint,1,opt,name=order_cnt,json=orderCnt,proto3" json:"order_cnt,omitempty"`
	TotalPrice           uint64   `protobuf:"varint,2,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	TotalNum             uint64   `protobuf:"varint,3,opt,name=total_num,json=totalNum,proto3" json:"total_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritOrderTotalResp) Reset()         { *m = GetMagicSpiritOrderTotalResp{} }
func (m *GetMagicSpiritOrderTotalResp) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritOrderTotalResp) ProtoMessage()    {}
func (*GetMagicSpiritOrderTotalResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{51}
}
func (m *GetMagicSpiritOrderTotalResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritOrderTotalResp.Unmarshal(m, b)
}
func (m *GetMagicSpiritOrderTotalResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritOrderTotalResp.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritOrderTotalResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritOrderTotalResp.Merge(dst, src)
}
func (m *GetMagicSpiritOrderTotalResp) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritOrderTotalResp.Size(m)
}
func (m *GetMagicSpiritOrderTotalResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritOrderTotalResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritOrderTotalResp proto.InternalMessageInfo

func (m *GetMagicSpiritOrderTotalResp) GetOrderCnt() uint32 {
	if m != nil {
		return m.OrderCnt
	}
	return 0
}

func (m *GetMagicSpiritOrderTotalResp) GetTotalPrice() uint64 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *GetMagicSpiritOrderTotalResp) GetTotalNum() uint64 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

type GetMagicSpiritAwardTotalReq struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritAwardTotalReq) Reset()         { *m = GetMagicSpiritAwardTotalReq{} }
func (m *GetMagicSpiritAwardTotalReq) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritAwardTotalReq) ProtoMessage()    {}
func (*GetMagicSpiritAwardTotalReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{52}
}
func (m *GetMagicSpiritAwardTotalReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritAwardTotalReq.Unmarshal(m, b)
}
func (m *GetMagicSpiritAwardTotalReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritAwardTotalReq.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritAwardTotalReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritAwardTotalReq.Merge(dst, src)
}
func (m *GetMagicSpiritAwardTotalReq) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritAwardTotalReq.Size(m)
}
func (m *GetMagicSpiritAwardTotalReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritAwardTotalReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritAwardTotalReq proto.InternalMessageInfo

func (m *GetMagicSpiritAwardTotalReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetMagicSpiritAwardTotalReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetMagicSpiritAwardTotalResp struct {
	OrderCnt             uint32   `protobuf:"varint,1,opt,name=order_cnt,json=orderCnt,proto3" json:"order_cnt,omitempty"`
	TotalPrice           uint64   `protobuf:"varint,2,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	TotalNum             uint64   `protobuf:"varint,3,opt,name=total_num,json=totalNum,proto3" json:"total_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritAwardTotalResp) Reset()         { *m = GetMagicSpiritAwardTotalResp{} }
func (m *GetMagicSpiritAwardTotalResp) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritAwardTotalResp) ProtoMessage()    {}
func (*GetMagicSpiritAwardTotalResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{53}
}
func (m *GetMagicSpiritAwardTotalResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritAwardTotalResp.Unmarshal(m, b)
}
func (m *GetMagicSpiritAwardTotalResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritAwardTotalResp.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritAwardTotalResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritAwardTotalResp.Merge(dst, src)
}
func (m *GetMagicSpiritAwardTotalResp) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritAwardTotalResp.Size(m)
}
func (m *GetMagicSpiritAwardTotalResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritAwardTotalResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritAwardTotalResp proto.InternalMessageInfo

func (m *GetMagicSpiritAwardTotalResp) GetOrderCnt() uint32 {
	if m != nil {
		return m.OrderCnt
	}
	return 0
}

func (m *GetMagicSpiritAwardTotalResp) GetTotalPrice() uint64 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *GetMagicSpiritAwardTotalResp) GetTotalNum() uint64 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

// 待开箱礼物信息
type UnpackGiftInfo struct {
	ItemOrderId          string   `protobuf:"bytes,1,opt,name=item_order_id,json=itemOrderId,proto3" json:"item_order_id,omitempty"`
	MagicSpiritId        uint32   `protobuf:"varint,2,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	ItemId               uint32   `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SendUid              uint32   `protobuf:"varint,5,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,6,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	EndTs                uint32   `protobuf:"varint,7,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnpackGiftInfo) Reset()         { *m = UnpackGiftInfo{} }
func (m *UnpackGiftInfo) String() string { return proto.CompactTextString(m) }
func (*UnpackGiftInfo) ProtoMessage()    {}
func (*UnpackGiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{54}
}
func (m *UnpackGiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnpackGiftInfo.Unmarshal(m, b)
}
func (m *UnpackGiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnpackGiftInfo.Marshal(b, m, deterministic)
}
func (dst *UnpackGiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnpackGiftInfo.Merge(dst, src)
}
func (m *UnpackGiftInfo) XXX_Size() int {
	return xxx_messageInfo_UnpackGiftInfo.Size(m)
}
func (m *UnpackGiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UnpackGiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UnpackGiftInfo proto.InternalMessageInfo

func (m *UnpackGiftInfo) GetItemOrderId() string {
	if m != nil {
		return m.ItemOrderId
	}
	return ""
}

func (m *UnpackGiftInfo) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *UnpackGiftInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UnpackGiftInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UnpackGiftInfo) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *UnpackGiftInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *UnpackGiftInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type UnpackGiftSimple struct {
	ItemOrderId          string   `protobuf:"bytes,1,opt,name=item_order_id,json=itemOrderId,proto3" json:"item_order_id,omitempty"`
	EndTs                uint32   `protobuf:"varint,2,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnpackGiftSimple) Reset()         { *m = UnpackGiftSimple{} }
func (m *UnpackGiftSimple) String() string { return proto.CompactTextString(m) }
func (*UnpackGiftSimple) ProtoMessage()    {}
func (*UnpackGiftSimple) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{55}
}
func (m *UnpackGiftSimple) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnpackGiftSimple.Unmarshal(m, b)
}
func (m *UnpackGiftSimple) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnpackGiftSimple.Marshal(b, m, deterministic)
}
func (dst *UnpackGiftSimple) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnpackGiftSimple.Merge(dst, src)
}
func (m *UnpackGiftSimple) XXX_Size() int {
	return xxx_messageInfo_UnpackGiftSimple.Size(m)
}
func (m *UnpackGiftSimple) XXX_DiscardUnknown() {
	xxx_messageInfo_UnpackGiftSimple.DiscardUnknown(m)
}

var xxx_messageInfo_UnpackGiftSimple proto.InternalMessageInfo

func (m *UnpackGiftSimple) GetItemOrderId() string {
	if m != nil {
		return m.ItemOrderId
	}
	return ""
}

func (m *UnpackGiftSimple) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

// 用户待开箱列表推送 opt
type UserUnpackGiftList struct {
	ChannelId            uint32              `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32              `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	List                 []*UnpackGiftSimple `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserUnpackGiftList) Reset()         { *m = UserUnpackGiftList{} }
func (m *UserUnpackGiftList) String() string { return proto.CompactTextString(m) }
func (*UserUnpackGiftList) ProtoMessage()    {}
func (*UserUnpackGiftList) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{56}
}
func (m *UserUnpackGiftList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserUnpackGiftList.Unmarshal(m, b)
}
func (m *UserUnpackGiftList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserUnpackGiftList.Marshal(b, m, deterministic)
}
func (dst *UserUnpackGiftList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserUnpackGiftList.Merge(dst, src)
}
func (m *UserUnpackGiftList) XXX_Size() int {
	return xxx_messageInfo_UserUnpackGiftList.Size(m)
}
func (m *UserUnpackGiftList) XXX_DiscardUnknown() {
	xxx_messageInfo_UserUnpackGiftList.DiscardUnknown(m)
}

var xxx_messageInfo_UserUnpackGiftList proto.InternalMessageInfo

func (m *UserUnpackGiftList) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserUnpackGiftList) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserUnpackGiftList) GetList() []*UnpackGiftSimple {
	if m != nil {
		return m.List
	}
	return nil
}

// 获取房间当前待开箱礼物列表
type GetChannelAllUnpackGiftReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelAllUnpackGiftReq) Reset()         { *m = GetChannelAllUnpackGiftReq{} }
func (m *GetChannelAllUnpackGiftReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelAllUnpackGiftReq) ProtoMessage()    {}
func (*GetChannelAllUnpackGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{57}
}
func (m *GetChannelAllUnpackGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelAllUnpackGiftReq.Unmarshal(m, b)
}
func (m *GetChannelAllUnpackGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelAllUnpackGiftReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelAllUnpackGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelAllUnpackGiftReq.Merge(dst, src)
}
func (m *GetChannelAllUnpackGiftReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelAllUnpackGiftReq.Size(m)
}
func (m *GetChannelAllUnpackGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelAllUnpackGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelAllUnpackGiftReq proto.InternalMessageInfo

func (m *GetChannelAllUnpackGiftReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelAllUnpackGiftReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelAllUnpackGiftResp struct {
	List                 []*UnpackGiftInfo   `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	UserUnpackOpt        *UserUnpackGiftList `protobuf:"bytes,2,opt,name=user_unpack_opt,json=userUnpackOpt,proto3" json:"user_unpack_opt,omitempty"`
	ServerTs             uint32              `protobuf:"varint,3,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetChannelAllUnpackGiftResp) Reset()         { *m = GetChannelAllUnpackGiftResp{} }
func (m *GetChannelAllUnpackGiftResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelAllUnpackGiftResp) ProtoMessage()    {}
func (*GetChannelAllUnpackGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{58}
}
func (m *GetChannelAllUnpackGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelAllUnpackGiftResp.Unmarshal(m, b)
}
func (m *GetChannelAllUnpackGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelAllUnpackGiftResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelAllUnpackGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelAllUnpackGiftResp.Merge(dst, src)
}
func (m *GetChannelAllUnpackGiftResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelAllUnpackGiftResp.Size(m)
}
func (m *GetChannelAllUnpackGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelAllUnpackGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelAllUnpackGiftResp proto.InternalMessageInfo

func (m *GetChannelAllUnpackGiftResp) GetList() []*UnpackGiftInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetChannelAllUnpackGiftResp) GetUserUnpackOpt() *UserUnpackGiftList {
	if m != nil {
		return m.UserUnpackOpt
	}
	return nil
}

func (m *GetChannelAllUnpackGiftResp) GetServerTs() uint32 {
	if m != nil {
		return m.ServerTs
	}
	return 0
}

type GetMagicSpiritExemptValueReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MagicSpiritId        []uint32 `protobuf:"varint,2,rep,packed,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritExemptValueReq) Reset()         { *m = GetMagicSpiritExemptValueReq{} }
func (m *GetMagicSpiritExemptValueReq) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritExemptValueReq) ProtoMessage()    {}
func (*GetMagicSpiritExemptValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{59}
}
func (m *GetMagicSpiritExemptValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritExemptValueReq.Unmarshal(m, b)
}
func (m *GetMagicSpiritExemptValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritExemptValueReq.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritExemptValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritExemptValueReq.Merge(dst, src)
}
func (m *GetMagicSpiritExemptValueReq) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritExemptValueReq.Size(m)
}
func (m *GetMagicSpiritExemptValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritExemptValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritExemptValueReq proto.InternalMessageInfo

func (m *GetMagicSpiritExemptValueReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMagicSpiritExemptValueReq) GetMagicSpiritId() []uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return nil
}

type MagicSpiritExemptValue struct {
	MagicSpiritId        uint32   `protobuf:"varint,1,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	SendFlag             bool     `protobuf:"varint,2,opt,name=send_flag,json=sendFlag,proto3" json:"send_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MagicSpiritExemptValue) Reset()         { *m = MagicSpiritExemptValue{} }
func (m *MagicSpiritExemptValue) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritExemptValue) ProtoMessage()    {}
func (*MagicSpiritExemptValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{60}
}
func (m *MagicSpiritExemptValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritExemptValue.Unmarshal(m, b)
}
func (m *MagicSpiritExemptValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritExemptValue.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritExemptValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritExemptValue.Merge(dst, src)
}
func (m *MagicSpiritExemptValue) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritExemptValue.Size(m)
}
func (m *MagicSpiritExemptValue) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritExemptValue.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritExemptValue proto.InternalMessageInfo

func (m *MagicSpiritExemptValue) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *MagicSpiritExemptValue) GetSendFlag() bool {
	if m != nil {
		return m.SendFlag
	}
	return false
}

type GetMagicSpiritExemptValueResp struct {
	ValueList            []*MagicSpiritExemptValue `protobuf:"bytes,1,rep,name=value_list,json=valueList,proto3" json:"value_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetMagicSpiritExemptValueResp) Reset()         { *m = GetMagicSpiritExemptValueResp{} }
func (m *GetMagicSpiritExemptValueResp) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritExemptValueResp) ProtoMessage()    {}
func (*GetMagicSpiritExemptValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_magic_spirit_a44b0c8bce7d6b51, []int{61}
}
func (m *GetMagicSpiritExemptValueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritExemptValueResp.Unmarshal(m, b)
}
func (m *GetMagicSpiritExemptValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritExemptValueResp.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritExemptValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritExemptValueResp.Merge(dst, src)
}
func (m *GetMagicSpiritExemptValueResp) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritExemptValueResp.Size(m)
}
func (m *GetMagicSpiritExemptValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritExemptValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritExemptValueResp proto.InternalMessageInfo

func (m *GetMagicSpiritExemptValueResp) GetValueList() []*MagicSpiritExemptValue {
	if m != nil {
		return m.ValueList
	}
	return nil
}

func init() {
	proto.RegisterType((*MagicSpirit)(nil), "magic_spirit.MagicSpirit")
	proto.RegisterType((*MagicSpiritActivityCfg)(nil), "magic_spirit.MagicSpiritActivityCfg")
	proto.RegisterType((*MagicSpiritTmp)(nil), "magic_spirit.MagicSpiritTmp")
	proto.RegisterType((*AddMagicSpiritReq)(nil), "magic_spirit.AddMagicSpiritReq")
	proto.RegisterType((*AddMagicSpiritResp)(nil), "magic_spirit.AddMagicSpiritResp")
	proto.RegisterType((*DelMagicSpiritReq)(nil), "magic_spirit.DelMagicSpiritReq")
	proto.RegisterType((*DelMagicSpiritResp)(nil), "magic_spirit.DelMagicSpiritResp")
	proto.RegisterType((*GetMagicSpiritReq)(nil), "magic_spirit.GetMagicSpiritReq")
	proto.RegisterType((*GetMagicSpiritResp)(nil), "magic_spirit.GetMagicSpiritResp")
	proto.RegisterType((*UpdateMagicSpiritReq)(nil), "magic_spirit.UpdateMagicSpiritReq")
	proto.RegisterType((*UpdateMagicSpiritResp)(nil), "magic_spirit.UpdateMagicSpiritResp")
	proto.RegisterType((*MagicSpiritPondItem)(nil), "magic_spirit.MagicSpiritPondItem")
	proto.RegisterType((*AddMagicSpiritPondReq)(nil), "magic_spirit.AddMagicSpiritPondReq")
	proto.RegisterType((*AddMagicSpiritPondResp)(nil), "magic_spirit.AddMagicSpiritPondResp")
	proto.RegisterType((*UpdateMagicSpiritPondReq)(nil), "magic_spirit.UpdateMagicSpiritPondReq")
	proto.RegisterType((*UpdateMagicSpiritPondResp)(nil), "magic_spirit.UpdateMagicSpiritPondResp")
	proto.RegisterType((*GetMagicSpiritPondReq)(nil), "magic_spirit.GetMagicSpiritPondReq")
	proto.RegisterType((*GetMagicSpiritPondResp)(nil), "magic_spirit.GetMagicSpiritPondResp")
	proto.RegisterType((*DelMagicSpiritPondReq)(nil), "magic_spirit.DelMagicSpiritPondReq")
	proto.RegisterType((*DelMagicSpiritPondResp)(nil), "magic_spirit.DelMagicSpiritPondResp")
	proto.RegisterType((*GetMagicSpiritConfTmpReq)(nil), "magic_spirit.GetMagicSpiritConfTmpReq")
	proto.RegisterType((*MagicSpiritConfTmp)(nil), "magic_spirit.MagicSpiritConfTmp")
	proto.RegisterType((*GetMagicSpiritConfTmpResp)(nil), "magic_spirit.GetMagicSpiritConfTmpResp")
	proto.RegisterType((*MagicSpiritBlacklist)(nil), "magic_spirit.MagicSpiritBlacklist")
	proto.RegisterType((*AddMagicSpiritBlacklistReq)(nil), "magic_spirit.AddMagicSpiritBlacklistReq")
	proto.RegisterType((*AddMagicSpiritBlacklistResp)(nil), "magic_spirit.AddMagicSpiritBlacklistResp")
	proto.RegisterType((*GetMagicSpiritBlackListReq)(nil), "magic_spirit.GetMagicSpiritBlackListReq")
	proto.RegisterType((*GetMagicSpiritBlackListResp)(nil), "magic_spirit.GetMagicSpiritBlackListResp")
	proto.RegisterType((*DelMagicSpiritBlacklistReq)(nil), "magic_spirit.DelMagicSpiritBlacklistReq")
	proto.RegisterType((*DelMagicSpiritBlacklistResp)(nil), "magic_spirit.DelMagicSpiritBlacklistResp")
	proto.RegisterType((*CommonConf)(nil), "magic_spirit.CommonConf")
	proto.RegisterType((*GetCommonConfReq)(nil), "magic_spirit.GetCommonConfReq")
	proto.RegisterType((*GetCommonConfResp)(nil), "magic_spirit.GetCommonConfResp")
	proto.RegisterType((*SetCommonConfReq)(nil), "magic_spirit.SetCommonConfReq")
	proto.RegisterType((*SetCommonConfResp)(nil), "magic_spirit.SetCommonConfResp")
	proto.RegisterType((*CombInfo)(nil), "magic_spirit.CombInfo")
	proto.RegisterType((*PresentSendInfo)(nil), "magic_spirit.PresentSendInfo")
	proto.RegisterType((*SendMagicSpiritReq)(nil), "magic_spirit.SendMagicSpiritReq")
	proto.RegisterType((*SendMagicSpiritResp)(nil), "magic_spirit.SendMagicSpiritResp")
	proto.RegisterType((*CheckIfSendMagicWithSourceReq)(nil), "magic_spirit.CheckIfSendMagicWithSourceReq")
	proto.RegisterType((*CheckIfSendMagicWithSourceResp)(nil), "magic_spirit.CheckIfSendMagicWithSourceResp")
	proto.RegisterType((*SendMagicWithSourceReq)(nil), "magic_spirit.SendMagicWithSourceReq")
	proto.RegisterType((*SendMagicWithSourceResp)(nil), "magic_spirit.SendMagicWithSourceResp")
	proto.RegisterType((*SendUnpackGiftReq)(nil), "magic_spirit.SendUnpackGiftReq")
	proto.RegisterType((*SendUnpackGiftResp)(nil), "magic_spirit.SendUnpackGiftResp")
	proto.RegisterType((*MagicSpiritForCli)(nil), "magic_spirit.MagicSpiritForCli")
	proto.RegisterType((*GetMagicSpiritForCliReq)(nil), "magic_spirit.GetMagicSpiritForCliReq")
	proto.RegisterType((*GetMagicSpiritForCliResp)(nil), "magic_spirit.GetMagicSpiritForCliResp")
	proto.RegisterType((*GetMagicSpiritUsableReq)(nil), "magic_spirit.GetMagicSpiritUsableReq")
	proto.RegisterType((*GetMagicSpiritUsableResp)(nil), "magic_spirit.GetMagicSpiritUsableResp")
	proto.RegisterType((*GetMagicSpiritOrderTotalReq)(nil), "magic_spirit.GetMagicSpiritOrderTotalReq")
	proto.RegisterType((*GetMagicSpiritOrderTotalResp)(nil), "magic_spirit.GetMagicSpiritOrderTotalResp")
	proto.RegisterType((*GetMagicSpiritAwardTotalReq)(nil), "magic_spirit.GetMagicSpiritAwardTotalReq")
	proto.RegisterType((*GetMagicSpiritAwardTotalResp)(nil), "magic_spirit.GetMagicSpiritAwardTotalResp")
	proto.RegisterType((*UnpackGiftInfo)(nil), "magic_spirit.UnpackGiftInfo")
	proto.RegisterType((*UnpackGiftSimple)(nil), "magic_spirit.UnpackGiftSimple")
	proto.RegisterType((*UserUnpackGiftList)(nil), "magic_spirit.UserUnpackGiftList")
	proto.RegisterType((*GetChannelAllUnpackGiftReq)(nil), "magic_spirit.GetChannelAllUnpackGiftReq")
	proto.RegisterType((*GetChannelAllUnpackGiftResp)(nil), "magic_spirit.GetChannelAllUnpackGiftResp")
	proto.RegisterType((*GetMagicSpiritExemptValueReq)(nil), "magic_spirit.GetMagicSpiritExemptValueReq")
	proto.RegisterType((*MagicSpiritExemptValue)(nil), "magic_spirit.MagicSpiritExemptValue")
	proto.RegisterType((*GetMagicSpiritExemptValueResp)(nil), "magic_spirit.GetMagicSpiritExemptValueResp")
	proto.RegisterEnum("magic_spirit.PrizeLeverConst", PrizeLeverConst_name, PrizeLeverConst_value)
	proto.RegisterEnum("magic_spirit.CommonConfType", CommonConfType_name, CommonConfType_value)
	proto.RegisterEnum("magic_spirit.MagicSpiritEffectType", MagicSpiritEffectType_name, MagicSpiritEffectType_value)
	proto.RegisterEnum("magic_spirit.SendMagicSource", SendMagicSource_name, SendMagicSource_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MagicSpiritBackendClient is the client API for MagicSpiritBackend service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MagicSpiritBackendClient interface {
	// 添加幸运礼物
	AddMagicSpirit(ctx context.Context, in *AddMagicSpiritReq, opts ...grpc.CallOption) (*AddMagicSpiritResp, error)
	// 删除幸运礼物
	DelMagicSpirit(ctx context.Context, in *DelMagicSpiritReq, opts ...grpc.CallOption) (*DelMagicSpiritResp, error)
	// 查询幸运礼物
	GetMagicSpirit(ctx context.Context, in *GetMagicSpiritReq, opts ...grpc.CallOption) (*GetMagicSpiritResp, error)
	// 修改幸运礼物
	UpdateMagicSpirit(ctx context.Context, in *UpdateMagicSpiritReq, opts ...grpc.CallOption) (*UpdateMagicSpiritResp, error)
	// 添加奖池礼物
	AddMagicSpiritPond(ctx context.Context, in *AddMagicSpiritPondReq, opts ...grpc.CallOption) (*AddMagicSpiritPondResp, error)
	// 获取礼物池列表
	GetMagicSpiritPond(ctx context.Context, in *GetMagicSpiritPondReq, opts ...grpc.CallOption) (*GetMagicSpiritPondResp, error)
	// 添加黑名单
	AddMagicSpiritBlacklist(ctx context.Context, in *AddMagicSpiritBlacklistReq, opts ...grpc.CallOption) (*AddMagicSpiritBlacklistResp, error)
	// 获取黑名单
	GetMagicSpiritBlacklist(ctx context.Context, in *GetMagicSpiritBlackListReq, opts ...grpc.CallOption) (*GetMagicSpiritBlackListResp, error)
	// 删除黑名单
	DelMagicSpiritBlacklist(ctx context.Context, in *DelMagicSpiritBlacklistReq, opts ...grpc.CallOption) (*DelMagicSpiritBlacklistResp, error)
	// 设置通用配置, 单账号送礼数限制, 每日金额限制, 单笔礼物数目限制
	SetCommonConf(ctx context.Context, in *SetCommonConfReq, opts ...grpc.CallOption) (*SetCommonConfResp, error)
	// 获取通用配置
	GetCommonConf(ctx context.Context, in *GetCommonConfReq, opts ...grpc.CallOption) (*GetCommonConfResp, error)
	// 赠送送魔法精灵
	SendMagicSpirit(ctx context.Context, in *SendMagicSpiritReq, opts ...grpc.CallOption) (*SendMagicSpiritResp, error)
	// 开箱礼物
	SendUnpackGift(ctx context.Context, in *SendUnpackGiftReq, opts ...grpc.CallOption) (*SendUnpackGiftResp, error)
	// 获取房间带开箱礼物信息列表
	GetChannelAllUnpackGift(ctx context.Context, in *GetChannelAllUnpackGiftReq, opts ...grpc.CallOption) (*GetChannelAllUnpackGiftResp, error)
	// 客户端幸运礼物列表
	GetMagicSpiritForCli(ctx context.Context, in *GetMagicSpiritForCliReq, opts ...grpc.CallOption) (*GetMagicSpiritForCliResp, error)
	// 是否可玩幸运礼物
	GetMagicSpiritUsable(ctx context.Context, in *GetMagicSpiritUsableReq, opts ...grpc.CallOption) (*GetMagicSpiritUsableResp, error)
	// 获取订单汇总信息(对账用)
	GetMagicSpiritOrderTotal(ctx context.Context, in *GetMagicSpiritOrderTotalReq, opts ...grpc.CallOption) (*GetMagicSpiritOrderTotalResp, error)
	// 获取奖励汇总信息(对账用)
	GetMagicSpiritAwardTotal(ctx context.Context, in *GetMagicSpiritAwardTotalReq, opts ...grpc.CallOption) (*GetMagicSpiritAwardTotalResp, error)
	// 获取未开始的幸运礼物/奖池配置
	GetMagicSpiritConfTmp(ctx context.Context, in *GetMagicSpiritConfTmpReq, opts ...grpc.CallOption) (*GetMagicSpiritConfTmpResp, error)
	// 检查是否可以发特殊来源的幸运礼物
	CheckIfSendMagicWithSource(ctx context.Context, in *CheckIfSendMagicWithSourceReq, opts ...grpc.CallOption) (*CheckIfSendMagicWithSourceResp, error)
	// 发特殊来源的幸运礼物
	SendMagicWithSource(ctx context.Context, in *SendMagicWithSourceReq, opts ...grpc.CallOption) (*SendMagicWithSourceResp, error)
	// 豁免条件值
	GetMagicSpiritExemptValue(ctx context.Context, in *GetMagicSpiritExemptValueReq, opts ...grpc.CallOption) (*GetMagicSpiritExemptValueResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	ReissueMagicOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error)
}

type magicSpiritBackendClient struct {
	cc *grpc.ClientConn
}

func NewMagicSpiritBackendClient(cc *grpc.ClientConn) MagicSpiritBackendClient {
	return &magicSpiritBackendClient{cc}
}

func (c *magicSpiritBackendClient) AddMagicSpirit(ctx context.Context, in *AddMagicSpiritReq, opts ...grpc.CallOption) (*AddMagicSpiritResp, error) {
	out := new(AddMagicSpiritResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/AddMagicSpirit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) DelMagicSpirit(ctx context.Context, in *DelMagicSpiritReq, opts ...grpc.CallOption) (*DelMagicSpiritResp, error) {
	out := new(DelMagicSpiritResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/DelMagicSpirit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetMagicSpirit(ctx context.Context, in *GetMagicSpiritReq, opts ...grpc.CallOption) (*GetMagicSpiritResp, error) {
	out := new(GetMagicSpiritResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetMagicSpirit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) UpdateMagicSpirit(ctx context.Context, in *UpdateMagicSpiritReq, opts ...grpc.CallOption) (*UpdateMagicSpiritResp, error) {
	out := new(UpdateMagicSpiritResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/UpdateMagicSpirit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) AddMagicSpiritPond(ctx context.Context, in *AddMagicSpiritPondReq, opts ...grpc.CallOption) (*AddMagicSpiritPondResp, error) {
	out := new(AddMagicSpiritPondResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/AddMagicSpiritPond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetMagicSpiritPond(ctx context.Context, in *GetMagicSpiritPondReq, opts ...grpc.CallOption) (*GetMagicSpiritPondResp, error) {
	out := new(GetMagicSpiritPondResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetMagicSpiritPond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) AddMagicSpiritBlacklist(ctx context.Context, in *AddMagicSpiritBlacklistReq, opts ...grpc.CallOption) (*AddMagicSpiritBlacklistResp, error) {
	out := new(AddMagicSpiritBlacklistResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/AddMagicSpiritBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetMagicSpiritBlacklist(ctx context.Context, in *GetMagicSpiritBlackListReq, opts ...grpc.CallOption) (*GetMagicSpiritBlackListResp, error) {
	out := new(GetMagicSpiritBlackListResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetMagicSpiritBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) DelMagicSpiritBlacklist(ctx context.Context, in *DelMagicSpiritBlacklistReq, opts ...grpc.CallOption) (*DelMagicSpiritBlacklistResp, error) {
	out := new(DelMagicSpiritBlacklistResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/DelMagicSpiritBlacklist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) SetCommonConf(ctx context.Context, in *SetCommonConfReq, opts ...grpc.CallOption) (*SetCommonConfResp, error) {
	out := new(SetCommonConfResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/SetCommonConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetCommonConf(ctx context.Context, in *GetCommonConfReq, opts ...grpc.CallOption) (*GetCommonConfResp, error) {
	out := new(GetCommonConfResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetCommonConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) SendMagicSpirit(ctx context.Context, in *SendMagicSpiritReq, opts ...grpc.CallOption) (*SendMagicSpiritResp, error) {
	out := new(SendMagicSpiritResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/SendMagicSpirit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) SendUnpackGift(ctx context.Context, in *SendUnpackGiftReq, opts ...grpc.CallOption) (*SendUnpackGiftResp, error) {
	out := new(SendUnpackGiftResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/SendUnpackGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetChannelAllUnpackGift(ctx context.Context, in *GetChannelAllUnpackGiftReq, opts ...grpc.CallOption) (*GetChannelAllUnpackGiftResp, error) {
	out := new(GetChannelAllUnpackGiftResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetChannelAllUnpackGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetMagicSpiritForCli(ctx context.Context, in *GetMagicSpiritForCliReq, opts ...grpc.CallOption) (*GetMagicSpiritForCliResp, error) {
	out := new(GetMagicSpiritForCliResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetMagicSpiritForCli", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetMagicSpiritUsable(ctx context.Context, in *GetMagicSpiritUsableReq, opts ...grpc.CallOption) (*GetMagicSpiritUsableResp, error) {
	out := new(GetMagicSpiritUsableResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetMagicSpiritUsable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetMagicSpiritOrderTotal(ctx context.Context, in *GetMagicSpiritOrderTotalReq, opts ...grpc.CallOption) (*GetMagicSpiritOrderTotalResp, error) {
	out := new(GetMagicSpiritOrderTotalResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetMagicSpiritOrderTotal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetMagicSpiritAwardTotal(ctx context.Context, in *GetMagicSpiritAwardTotalReq, opts ...grpc.CallOption) (*GetMagicSpiritAwardTotalResp, error) {
	out := new(GetMagicSpiritAwardTotalResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetMagicSpiritAwardTotal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetMagicSpiritConfTmp(ctx context.Context, in *GetMagicSpiritConfTmpReq, opts ...grpc.CallOption) (*GetMagicSpiritConfTmpResp, error) {
	out := new(GetMagicSpiritConfTmpResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetMagicSpiritConfTmp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) CheckIfSendMagicWithSource(ctx context.Context, in *CheckIfSendMagicWithSourceReq, opts ...grpc.CallOption) (*CheckIfSendMagicWithSourceResp, error) {
	out := new(CheckIfSendMagicWithSourceResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/CheckIfSendMagicWithSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) SendMagicWithSource(ctx context.Context, in *SendMagicWithSourceReq, opts ...grpc.CallOption) (*SendMagicWithSourceResp, error) {
	out := new(SendMagicWithSourceResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/SendMagicWithSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetMagicSpiritExemptValue(ctx context.Context, in *GetMagicSpiritExemptValueReq, opts ...grpc.CallOption) (*GetMagicSpiritExemptValueResp, error) {
	out := new(GetMagicSpiritExemptValueResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetMagicSpiritExemptValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetAwardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetAwardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetConsumeTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GetConsumeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) ReissueMagicOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/ReissueMagicOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *magicSpiritBackendClient) GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error) {
	out := new(reconcile_v2.GenFinancialFileResp)
	err := c.cc.Invoke(ctx, "/magic_spirit.MagicSpiritBackend/GenFinancialFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MagicSpiritBackendServer is the server API for MagicSpiritBackend service.
type MagicSpiritBackendServer interface {
	// 添加幸运礼物
	AddMagicSpirit(context.Context, *AddMagicSpiritReq) (*AddMagicSpiritResp, error)
	// 删除幸运礼物
	DelMagicSpirit(context.Context, *DelMagicSpiritReq) (*DelMagicSpiritResp, error)
	// 查询幸运礼物
	GetMagicSpirit(context.Context, *GetMagicSpiritReq) (*GetMagicSpiritResp, error)
	// 修改幸运礼物
	UpdateMagicSpirit(context.Context, *UpdateMagicSpiritReq) (*UpdateMagicSpiritResp, error)
	// 添加奖池礼物
	AddMagicSpiritPond(context.Context, *AddMagicSpiritPondReq) (*AddMagicSpiritPondResp, error)
	// 获取礼物池列表
	GetMagicSpiritPond(context.Context, *GetMagicSpiritPondReq) (*GetMagicSpiritPondResp, error)
	// 添加黑名单
	AddMagicSpiritBlacklist(context.Context, *AddMagicSpiritBlacklistReq) (*AddMagicSpiritBlacklistResp, error)
	// 获取黑名单
	GetMagicSpiritBlacklist(context.Context, *GetMagicSpiritBlackListReq) (*GetMagicSpiritBlackListResp, error)
	// 删除黑名单
	DelMagicSpiritBlacklist(context.Context, *DelMagicSpiritBlacklistReq) (*DelMagicSpiritBlacklistResp, error)
	// 设置通用配置, 单账号送礼数限制, 每日金额限制, 单笔礼物数目限制
	SetCommonConf(context.Context, *SetCommonConfReq) (*SetCommonConfResp, error)
	// 获取通用配置
	GetCommonConf(context.Context, *GetCommonConfReq) (*GetCommonConfResp, error)
	// 赠送送魔法精灵
	SendMagicSpirit(context.Context, *SendMagicSpiritReq) (*SendMagicSpiritResp, error)
	// 开箱礼物
	SendUnpackGift(context.Context, *SendUnpackGiftReq) (*SendUnpackGiftResp, error)
	// 获取房间带开箱礼物信息列表
	GetChannelAllUnpackGift(context.Context, *GetChannelAllUnpackGiftReq) (*GetChannelAllUnpackGiftResp, error)
	// 客户端幸运礼物列表
	GetMagicSpiritForCli(context.Context, *GetMagicSpiritForCliReq) (*GetMagicSpiritForCliResp, error)
	// 是否可玩幸运礼物
	GetMagicSpiritUsable(context.Context, *GetMagicSpiritUsableReq) (*GetMagicSpiritUsableResp, error)
	// 获取订单汇总信息(对账用)
	GetMagicSpiritOrderTotal(context.Context, *GetMagicSpiritOrderTotalReq) (*GetMagicSpiritOrderTotalResp, error)
	// 获取奖励汇总信息(对账用)
	GetMagicSpiritAwardTotal(context.Context, *GetMagicSpiritAwardTotalReq) (*GetMagicSpiritAwardTotalResp, error)
	// 获取未开始的幸运礼物/奖池配置
	GetMagicSpiritConfTmp(context.Context, *GetMagicSpiritConfTmpReq) (*GetMagicSpiritConfTmpResp, error)
	// 检查是否可以发特殊来源的幸运礼物
	CheckIfSendMagicWithSource(context.Context, *CheckIfSendMagicWithSourceReq) (*CheckIfSendMagicWithSourceResp, error)
	// 发特殊来源的幸运礼物
	SendMagicWithSource(context.Context, *SendMagicWithSourceReq) (*SendMagicWithSourceResp, error)
	// 豁免条件值
	GetMagicSpiritExemptValue(context.Context, *GetMagicSpiritExemptValueReq) (*GetMagicSpiritExemptValueResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	ReissueMagicOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	GenFinancialFile(context.Context, *reconcile_v2.GenFinancialFileReq) (*reconcile_v2.GenFinancialFileResp, error)
}

func RegisterMagicSpiritBackendServer(s *grpc.Server, srv MagicSpiritBackendServer) {
	s.RegisterService(&_MagicSpiritBackend_serviceDesc, srv)
}

func _MagicSpiritBackend_AddMagicSpirit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMagicSpiritReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).AddMagicSpirit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/AddMagicSpirit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).AddMagicSpirit(ctx, req.(*AddMagicSpiritReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_DelMagicSpirit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelMagicSpiritReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).DelMagicSpirit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/DelMagicSpirit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).DelMagicSpirit(ctx, req.(*DelMagicSpiritReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetMagicSpirit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetMagicSpirit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetMagicSpirit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetMagicSpirit(ctx, req.(*GetMagicSpiritReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_UpdateMagicSpirit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMagicSpiritReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).UpdateMagicSpirit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/UpdateMagicSpirit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).UpdateMagicSpirit(ctx, req.(*UpdateMagicSpiritReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_AddMagicSpiritPond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMagicSpiritPondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).AddMagicSpiritPond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/AddMagicSpiritPond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).AddMagicSpiritPond(ctx, req.(*AddMagicSpiritPondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetMagicSpiritPond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritPondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritPond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetMagicSpiritPond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritPond(ctx, req.(*GetMagicSpiritPondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_AddMagicSpiritBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMagicSpiritBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).AddMagicSpiritBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/AddMagicSpiritBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).AddMagicSpiritBlacklist(ctx, req.(*AddMagicSpiritBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetMagicSpiritBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetMagicSpiritBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritBlacklist(ctx, req.(*GetMagicSpiritBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_DelMagicSpiritBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelMagicSpiritBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).DelMagicSpiritBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/DelMagicSpiritBlacklist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).DelMagicSpiritBlacklist(ctx, req.(*DelMagicSpiritBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_SetCommonConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCommonConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).SetCommonConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/SetCommonConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).SetCommonConf(ctx, req.(*SetCommonConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetCommonConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommonConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetCommonConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetCommonConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetCommonConf(ctx, req.(*GetCommonConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_SendMagicSpirit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMagicSpiritReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).SendMagicSpirit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/SendMagicSpirit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).SendMagicSpirit(ctx, req.(*SendMagicSpiritReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_SendUnpackGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendUnpackGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).SendUnpackGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/SendUnpackGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).SendUnpackGift(ctx, req.(*SendUnpackGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetChannelAllUnpackGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelAllUnpackGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetChannelAllUnpackGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetChannelAllUnpackGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetChannelAllUnpackGift(ctx, req.(*GetChannelAllUnpackGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetMagicSpiritForCli_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritForCliReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritForCli(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetMagicSpiritForCli",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritForCli(ctx, req.(*GetMagicSpiritForCliReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetMagicSpiritUsable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritUsableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritUsable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetMagicSpiritUsable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritUsable(ctx, req.(*GetMagicSpiritUsableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetMagicSpiritOrderTotal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritOrderTotalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritOrderTotal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetMagicSpiritOrderTotal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritOrderTotal(ctx, req.(*GetMagicSpiritOrderTotalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetMagicSpiritAwardTotal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritAwardTotalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritAwardTotal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetMagicSpiritAwardTotal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritAwardTotal(ctx, req.(*GetMagicSpiritAwardTotalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetMagicSpiritConfTmp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritConfTmpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritConfTmp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetMagicSpiritConfTmp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritConfTmp(ctx, req.(*GetMagicSpiritConfTmpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_CheckIfSendMagicWithSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIfSendMagicWithSourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).CheckIfSendMagicWithSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/CheckIfSendMagicWithSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).CheckIfSendMagicWithSource(ctx, req.(*CheckIfSendMagicWithSourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_SendMagicWithSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMagicWithSourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).SendMagicWithSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/SendMagicWithSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).SendMagicWithSource(ctx, req.(*SendMagicWithSourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetMagicSpiritExemptValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritExemptValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritExemptValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetMagicSpiritExemptValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetMagicSpiritExemptValue(ctx, req.(*GetMagicSpiritExemptValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetAwardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetAwardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetAwardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetAwardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetAwardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetAwardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetAwardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetAwardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetConsumeTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetConsumeTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetConsumeTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetConsumeTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GetConsumeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GetConsumeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GetConsumeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GetConsumeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_ReissueMagicOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).ReissueMagicOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/ReissueMagicOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).ReissueMagicOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MagicSpiritBackend_GenFinancialFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.GenFinancialFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MagicSpiritBackendServer).GenFinancialFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/magic_spirit.MagicSpiritBackend/GenFinancialFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MagicSpiritBackendServer).GenFinancialFile(ctx, req.(*reconcile_v2.GenFinancialFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MagicSpiritBackend_serviceDesc = grpc.ServiceDesc{
	ServiceName: "magic_spirit.MagicSpiritBackend",
	HandlerType: (*MagicSpiritBackendServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddMagicSpirit",
			Handler:    _MagicSpiritBackend_AddMagicSpirit_Handler,
		},
		{
			MethodName: "DelMagicSpirit",
			Handler:    _MagicSpiritBackend_DelMagicSpirit_Handler,
		},
		{
			MethodName: "GetMagicSpirit",
			Handler:    _MagicSpiritBackend_GetMagicSpirit_Handler,
		},
		{
			MethodName: "UpdateMagicSpirit",
			Handler:    _MagicSpiritBackend_UpdateMagicSpirit_Handler,
		},
		{
			MethodName: "AddMagicSpiritPond",
			Handler:    _MagicSpiritBackend_AddMagicSpiritPond_Handler,
		},
		{
			MethodName: "GetMagicSpiritPond",
			Handler:    _MagicSpiritBackend_GetMagicSpiritPond_Handler,
		},
		{
			MethodName: "AddMagicSpiritBlacklist",
			Handler:    _MagicSpiritBackend_AddMagicSpiritBlacklist_Handler,
		},
		{
			MethodName: "GetMagicSpiritBlacklist",
			Handler:    _MagicSpiritBackend_GetMagicSpiritBlacklist_Handler,
		},
		{
			MethodName: "DelMagicSpiritBlacklist",
			Handler:    _MagicSpiritBackend_DelMagicSpiritBlacklist_Handler,
		},
		{
			MethodName: "SetCommonConf",
			Handler:    _MagicSpiritBackend_SetCommonConf_Handler,
		},
		{
			MethodName: "GetCommonConf",
			Handler:    _MagicSpiritBackend_GetCommonConf_Handler,
		},
		{
			MethodName: "SendMagicSpirit",
			Handler:    _MagicSpiritBackend_SendMagicSpirit_Handler,
		},
		{
			MethodName: "SendUnpackGift",
			Handler:    _MagicSpiritBackend_SendUnpackGift_Handler,
		},
		{
			MethodName: "GetChannelAllUnpackGift",
			Handler:    _MagicSpiritBackend_GetChannelAllUnpackGift_Handler,
		},
		{
			MethodName: "GetMagicSpiritForCli",
			Handler:    _MagicSpiritBackend_GetMagicSpiritForCli_Handler,
		},
		{
			MethodName: "GetMagicSpiritUsable",
			Handler:    _MagicSpiritBackend_GetMagicSpiritUsable_Handler,
		},
		{
			MethodName: "GetMagicSpiritOrderTotal",
			Handler:    _MagicSpiritBackend_GetMagicSpiritOrderTotal_Handler,
		},
		{
			MethodName: "GetMagicSpiritAwardTotal",
			Handler:    _MagicSpiritBackend_GetMagicSpiritAwardTotal_Handler,
		},
		{
			MethodName: "GetMagicSpiritConfTmp",
			Handler:    _MagicSpiritBackend_GetMagicSpiritConfTmp_Handler,
		},
		{
			MethodName: "CheckIfSendMagicWithSource",
			Handler:    _MagicSpiritBackend_CheckIfSendMagicWithSource_Handler,
		},
		{
			MethodName: "SendMagicWithSource",
			Handler:    _MagicSpiritBackend_SendMagicWithSource_Handler,
		},
		{
			MethodName: "GetMagicSpiritExemptValue",
			Handler:    _MagicSpiritBackend_GetMagicSpiritExemptValue_Handler,
		},
		{
			MethodName: "GetAwardTotalCount",
			Handler:    _MagicSpiritBackend_GetAwardTotalCount_Handler,
		},
		{
			MethodName: "GetAwardOrderIds",
			Handler:    _MagicSpiritBackend_GetAwardOrderIds_Handler,
		},
		{
			MethodName: "GetConsumeTotalCount",
			Handler:    _MagicSpiritBackend_GetConsumeTotalCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderIds",
			Handler:    _MagicSpiritBackend_GetConsumeOrderIds_Handler,
		},
		{
			MethodName: "ReissueMagicOrder",
			Handler:    _MagicSpiritBackend_ReissueMagicOrder_Handler,
		},
		{
			MethodName: "GenFinancialFile",
			Handler:    _MagicSpiritBackend_GenFinancialFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/magic-spirit/magic-spirit.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/magic-spirit/magic-spirit.proto", fileDescriptor_magic_spirit_a44b0c8bce7d6b51)
}

var fileDescriptor_magic_spirit_a44b0c8bce7d6b51 = []byte{
	// 3332 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x1a, 0xcb, 0x72, 0x1b, 0xc7,
	0x91, 0x00, 0x28, 0x12, 0x68, 0x10, 0x0f, 0x8e, 0x44, 0x0a, 0x04, 0x45, 0x0b, 0x5a, 0xcb, 0x32,
	0x2d, 0x5b, 0x94, 0x4c, 0x47, 0x55, 0x49, 0xca, 0x2e, 0x9b, 0x82, 0x40, 0x1a, 0x0e, 0x5f, 0x59,
	0x92, 0x92, 0xad, 0xaa, 0x78, 0x6b, 0xb9, 0x3b, 0x00, 0x57, 0xdc, 0x97, 0x77, 0x16, 0x94, 0xe9,
	0x4b, 0x0e, 0x39, 0xa5, 0x92, 0x0f, 0xc8, 0x27, 0x24, 0x97, 0x1c, 0x7d, 0xc9, 0x2d, 0x3f, 0x90,
	0x73, 0x72, 0x4f, 0x95, 0x3f, 0x23, 0x35, 0x8f, 0x7d, 0x62, 0x17, 0x84, 0xac, 0xb8, 0x2a, 0x55,
	0xb9, 0x61, 0xba, 0x7b, 0xba, 0x7b, 0x7a, 0xba, 0x7b, 0xba, 0x7b, 0x01, 0x1b, 0xbe, 0xff, 0xf0,
	0x9b, 0x91, 0xa1, 0x9d, 0x13, 0xc3, 0xbc, 0xc0, 0xde, 0x43, 0x4b, 0x1d, 0x1a, 0xda, 0x03, 0xe2,
	0x1a, 0x9e, 0xe1, 0x27, 0x16, 0x1b, 0xae, 0xe7, 0xf8, 0x0e, 0x5a, 0x60, 0x30, 0x85, 0xc3, 0xda,
	0xbf, 0x4c, 0xed, 0x76, 0x3d, 0x4c, 0xb0, 0xed, 0x3f, 0xb0, 0x0c, 0x5d, 0x37, 0xf1, 0x2b, 0xd5,
	0xc3, 0x19, 0x20, 0xce, 0xa9, 0x9d, 0x96, 0xec, 0x61, 0xcd, 0xb1, 0x35, 0xc3, 0xc4, 0x0f, 0x2e,
	0x36, 0x13, 0x0b, 0x4e, 0x2f, 0xfd, 0xfd, 0x1a, 0x54, 0xf7, 0xa8, 0xf0, 0x23, 0x26, 0x1b, 0xdd,
	0x83, 0x46, 0x5c, 0x17, 0xc5, 0xd0, 0x5b, 0x85, 0x4e, 0x61, 0xbd, 0x26, 0xd7, 0xac, 0x88, 0xaa,
	0xaf, 0x23, 0x04, 0xb3, 0xb6, 0x6a, 0xe1, 0x56, 0xb1, 0x53, 0x58, 0xaf, 0xc8, 0xec, 0x37, 0x5a,
	0x81, 0xb2, 0xa1, 0x39, 0xb6, 0x32, 0xf2, 0xcc, 0x56, 0x89, 0xc1, 0xe7, 0xe9, 0xfa, 0xc4, 0x33,
	0xd1, 0x0d, 0xb8, 0xe6, 0x7a, 0x86, 0x86, 0x5b, 0xb3, 0x8c, 0x19, 0x5f, 0x50, 0x26, 0x9e, 0x6a,
	0x9f, 0xb7, 0xae, 0x31, 0x20, 0xfb, 0x8d, 0xee, 0xc0, 0x02, 0x1e, 0x0c, 0xb0, 0xe6, 0x2b, 0xa7,
	0x78, 0x68, 0xd8, 0xad, 0x39, 0x86, 0xab, 0x72, 0xd8, 0x13, 0x0a, 0x42, 0x6b, 0x00, 0x82, 0x04,
	0xdb, 0x7a, 0x6b, 0x9e, 0x11, 0x54, 0x38, 0xa4, 0x67, 0xeb, 0xe8, 0x03, 0x40, 0x3a, 0x26, 0x9a,
	0x67, 0x9c, 0x62, 0xc5, 0xb0, 0xd4, 0x21, 0x66, 0x0a, 0x95, 0x99, 0x42, 0xcd, 0x00, 0xd3, 0xa7,
	0x08, 0xaa, 0x59, 0x1b, 0xca, 0x01, 0xac, 0x55, 0x61, 0x34, 0xe1, 0x1a, 0xbd, 0x0b, 0x8d, 0x97,
	0x23, 0xdb, 0x70, 0x3c, 0xc5, 0x34, 0x86, 0x67, 0xbe, 0x61, 0x0f, 0x5b, 0xc0, 0xa4, 0xd5, 0x39,
	0x78, 0x57, 0x40, 0x29, 0x21, 0xbf, 0x89, 0x88, 0xb0, 0xca, 0x09, 0x39, 0x38, 0x24, 0xbc, 0x03,
	0x0b, 0x17, 0x83, 0x6f, 0x15, 0x0f, 0x13, 0x67, 0xe4, 0x69, 0xb8, 0xb5, 0xc0, 0x24, 0x56, 0x2f,
	0x06, 0xdf, 0xca, 0x02, 0x84, 0xd6, 0xa1, 0x19, 0x27, 0x51, 0x2c, 0xfd, 0x71, 0xab, 0xc6, 0xc8,
	0xea, 0x31, 0xb2, 0x3d, 0xfd, 0x31, 0xba, 0x0d, 0xd5, 0x91, 0xab, 0xab, 0x3e, 0x56, 0x7c, 0xc3,
	0xc2, 0xad, 0x3a, 0x93, 0x08, 0x1c, 0x74, 0x6c, 0x58, 0x98, 0x1a, 0x8a, 0xda, 0x54, 0x19, 0x98,
	0x8e, 0xea, 0xb7, 0x1a, 0x9d, 0xc2, 0x7a, 0x51, 0xae, 0x50, 0xc8, 0x36, 0x05, 0xa0, 0xfb, 0xb0,
	0x48, 0x8f, 0xaa, 0xa8, 0x9a, 0x6f, 0x5c, 0x18, 0xfe, 0x25, 0xb3, 0x53, 0x93, 0x89, 0x6a, 0x50,
	0xc4, 0x96, 0x80, 0x53, 0x33, 0xdd, 0x87, 0x45, 0xed, 0x4c, 0xb5, 0x6d, 0x6c, 0x2a, 0xfe, 0xa5,
	0x4b, 0xcf, 0x49, 0xfc, 0xd6, 0x62, 0xa7, 0xb4, 0x5e, 0x93, 0x1b, 0x02, 0x71, 0x7c, 0xe9, 0xe2,
	0x5d, 0x83, 0x30, 0x1f, 0x22, 0x67, 0xce, 0x2b, 0x25, 0x76, 0x49, 0xa8, 0x53, 0x58, 0x2f, 0xcb,
	0x35, 0x0a, 0xee, 0x85, 0x17, 0xb5, 0x03, 0x0b, 0xa1, 0x68, 0x6d, 0x30, 0x6c, 0x5d, 0xef, 0x14,
	0xd6, 0xab, 0x9b, 0x77, 0x37, 0xe2, 0x0e, 0xb8, 0x11, 0x73, 0xce, 0x40, 0x9f, 0xee, 0x60, 0x28,
	0x57, 0xd5, 0x68, 0x21, 0xfd, 0x50, 0x84, 0xe5, 0x6c, 0x3a, 0xf4, 0x36, 0xd4, 0x42, 0x19, 0xcc,
	0x61, 0x0b, 0xec, 0x7c, 0xa1, 0xe0, 0x7d, 0x95, 0xdb, 0x89, 0x39, 0x1b, 0xb7, 0x23, 0x75, 0xe9,
	0x92, 0x5c, 0x61, 0x10, 0x66, 0xc6, 0x15, 0x28, 0x63, 0x5b, 0xe7, 0xc8, 0x12, 0x43, 0xce, 0x63,
	0x5b, 0x67, 0xa8, 0x55, 0xa8, 0x44, 0x2e, 0x36, 0xcb, 0xdd, 0xc7, 0x08, 0x5c, 0xeb, 0x2d, 0xa8,
	0xbe, 0x1c, 0x59, 0x2e, 0xc5, 0x29, 0xbe, 0xcf, 0xbc, 0xbc, 0x22, 0x57, 0x28, 0xe8, 0xc4, 0x33,
	0x8f, 0x7d, 0xf4, 0x00, 0xae, 0x87, 0xf8, 0x33, 0x4d, 0x51, 0x6d, 0xdd, 0x73, 0x0c, 0x9d, 0x79,
	0x7c, 0x45, 0x6e, 0x0a, 0xba, 0xcf, 0xb5, 0x2d, 0x0e, 0x47, 0xef, 0x50, 0x6f, 0x8c, 0xc8, 0x0d,
	0x87, 0x30, 0xdf, 0xaf, 0xc8, 0x0b, 0x21, 0x69, 0xdf, 0x21, 0xe8, 0x43, 0x58, 0x0a, 0xc9, 0x2c,
	0xe3, 0x1c, 0x87, 0x7c, 0x79, 0x04, 0x20, 0x41, 0xbc, 0x67, 0x9c, 0xe3, 0x80, 0xf3, 0x7b, 0xb0,
	0x98, 0xdc, 0x42, 0x79, 0xf3, 0x60, 0xa8, 0xc7, 0xc8, 0xfb, 0x0e, 0x91, 0x5c, 0xa8, 0xc7, 0x2c,
	0x7d, 0x6c, 0xb9, 0xa8, 0x0e, 0xc5, 0x30, 0x49, 0x14, 0x0d, 0x9d, 0x7a, 0xa5, 0xb8, 0xf8, 0xd0,
	0x9a, 0x35, 0x59, 0x04, 0x2c, 0xb3, 0xd9, 0x03, 0x98, 0xd5, 0x1c, 0x7b, 0xc0, 0x4c, 0x59, 0xdd,
	0x5c, 0xc9, 0xbd, 0x6e, 0x99, 0x91, 0x49, 0xbf, 0x86, 0xc5, 0x2d, 0x5d, 0x8f, 0xc3, 0xf1, 0x37,
	0xe8, 0x63, 0x48, 0xa4, 0xcc, 0x56, 0xa1, 0x53, 0x9a, 0xcc, 0xab, 0x1a, 0x4b, 0x5f, 0xd2, 0xc7,
	0x80, 0xd2, 0x2c, 0x89, 0x9b, 0x9d, 0xfa, 0x4a, 0x63, 0xa9, 0x4f, 0xfa, 0x04, 0x16, 0x9f, 0x62,
	0x33, 0xa5, 0xd0, 0x3a, 0x34, 0x53, 0x9b, 0x89, 0xd8, 0x5d, 0x4f, 0xec, 0x26, 0xd2, 0x0d, 0x40,
	0xe9, 0xed, 0xc4, 0x95, 0xae, 0xc3, 0xe2, 0x0e, 0xf6, 0x93, 0x4c, 0x25, 0x19, 0x50, 0x1a, 0x48,
	0xdc, 0x37, 0x3c, 0xfb, 0x73, 0xb8, 0x71, 0xc2, 0x32, 0x44, 0xea, 0x00, 0x9f, 0x66, 0x72, 0xbd,
	0x95, 0xcb, 0xf5, 0xd8, 0x72, 0x93, 0x8c, 0x6f, 0xc2, 0x52, 0x06, 0x63, 0xe2, 0x4a, 0x7f, 0x2a,
	0xc2, 0xf5, 0x18, 0xec, 0xd0, 0xb1, 0xf5, 0xbe, 0x8f, 0x2d, 0x74, 0x13, 0xe6, 0x0d, 0x1f, 0x5b,
	0xd1, 0x13, 0x33, 0x47, 0x97, 0x7d, 0x3d, 0xeb, 0x22, 0x8a, 0x59, 0x6f, 0xd0, 0x1a, 0x80, 0x78,
	0x07, 0x29, 0x49, 0x89, 0xbf, 0x03, 0x02, 0xd2, 0xd7, 0xd1, 0x32, 0xcc, 0xbd, 0xc2, 0x34, 0xf1,
	0x8a, 0x47, 0x47, 0xac, 0xa8, 0x83, 0xba, 0x9e, 0xf1, 0x1d, 0x56, 0x4c, 0x7c, 0x81, 0x4d, 0xf1,
	0xf8, 0x00, 0x03, 0xed, 0x52, 0x48, 0x3a, 0xaf, 0x96, 0xc7, 0xf2, 0x6a, 0xca, 0xc5, 0x2b, 0x63,
	0x2e, 0x7e, 0x0f, 0x1a, 0xa7, 0x1e, 0x56, 0xcf, 0x0d, 0x7b, 0xa8, 0xd8, 0xf8, 0x15, 0x55, 0x8f,
	0x3f, 0x1c, 0xb5, 0x00, 0xbc, 0x8f, 0x5f, 0xf5, 0x75, 0xc9, 0x86, 0xa5, 0xa4, 0x23, 0x52, 0xe3,
	0xd0, 0xdb, 0x38, 0x81, 0x1b, 0x19, 0x26, 0x23, 0xe2, 0x56, 0xee, 0xe4, 0xde, 0x4a, 0x40, 0x29,
	0x67, 0x6e, 0x97, 0x9e, 0xc0, 0x72, 0x96, 0x3c, 0xe2, 0xbe, 0x86, 0xff, 0xfe, 0xa1, 0x00, 0xad,
	0xb1, 0x8b, 0x0e, 0xf4, 0xce, 0xbd, 0xd3, 0xe4, 0x5d, 0x15, 0xf3, 0xef, 0xaa, 0xf4, 0x5a, 0x77,
	0x25, 0xad, 0xc2, 0x4a, 0x8e, 0x32, 0xc4, 0x95, 0x3e, 0x85, 0xa5, 0x64, 0xfc, 0x04, 0x6a, 0x4e,
	0x59, 0xe5, 0x48, 0x43, 0x58, 0xce, 0x62, 0x40, 0x5c, 0xb4, 0x07, 0x8b, 0x09, 0x0e, 0xae, 0x63,
	0xeb, 0xd3, 0xdf, 0x4e, 0xc3, 0x4a, 0x02, 0xa5, 0x17, 0xb0, 0x94, 0x4c, 0x0a, 0xaf, 0xa9, 0x29,
	0xab, 0xbd, 0xb8, 0xe1, 0x49, 0xab, 0xc8, 0xee, 0x6d, 0x9e, 0x5b, 0x9e, 0x48, 0x2d, 0x58, 0xce,
	0xe2, 0x4d, 0x5c, 0xa9, 0x0d, 0xad, 0xe4, 0xf1, 0xba, 0x8e, 0x3d, 0xa0, 0x81, 0x8d, 0xbf, 0x91,
	0xfe, 0x5a, 0x00, 0x34, 0x8e, 0x41, 0xdb, 0x29, 0x3f, 0xf1, 0x2d, 0x97, 0x29, 0x74, 0x55, 0xaa,
	0x88, 0x7b, 0x11, 0xe5, 0xb3, 0x02, 0xe5, 0x33, 0x95, 0x28, 0xae, 0xe3, 0x98, 0xcc, 0x1b, 0xca,
	0xf2, 0xfc, 0x99, 0x4a, 0x0e, 0x1d, 0xc7, 0x44, 0x8f, 0x61, 0x96, 0x81, 0x4b, 0xd3, 0x5a, 0x93,
	0x91, 0x4b, 0x2f, 0x60, 0x25, 0xe7, 0x30, 0xc4, 0x45, 0x9f, 0x40, 0x85, 0x3e, 0x26, 0xbc, 0x6c,
	0xe1, 0xd7, 0xd4, 0xc9, 0x65, 0x1c, 0x6c, 0x2c, 0xd3, 0x2d, 0xb4, 0xa2, 0x91, 0xfe, 0x59, 0x48,
	0xc4, 0xe3, 0x13, 0x53, 0xd5, 0xce, 0x29, 0x2b, 0x5a, 0xcf, 0x9d, 0x06, 0x8b, 0xe8, 0x6e, 0xaa,
	0x21, 0x8c, 0x7b, 0x7e, 0x50, 0x39, 0x45, 0x9e, 0x2f, 0x20, 0x7d, 0x9d, 0x46, 0x8c, 0xe7, 0x38,
	0x56, 0x94, 0xc1, 0xe6, 0xe8, 0xb2, 0xaf, 0xd3, 0xd2, 0x82, 0x21, 0x58, 0xd5, 0x22, 0x4a, 0x0b,
	0x0a, 0x60, 0x15, 0x0b, 0x82, 0x59, 0xdf, 0x37, 0xf4, 0xa0, 0x72, 0xa6, 0xbf, 0x69, 0x8d, 0xed,
	0xbc, 0xb2, 0xb1, 0x27, 0x0a, 0x08, 0xbe, 0xa0, 0x11, 0xa4, 0x79, 0x38, 0x4c, 0x66, 0xbc, 0x5a,
	0x06, 0x0e, 0xa2, 0xb9, 0x4a, 0xfa, 0x1a, 0xda, 0xc9, 0x9c, 0x10, 0x9e, 0x8e, 0xfa, 0xdf, 0x67,
	0x50, 0x09, 0x0f, 0x23, 0x0c, 0x27, 0xe5, 0x1a, 0x2e, 0xda, 0x19, 0x6d, 0x92, 0x3e, 0x83, 0xd5,
	0x5c, 0xfe, 0xc4, 0xcd, 0xb0, 0x60, 0x29, 0x65, 0x41, 0x89, 0x40, 0x3b, 0x79, 0xb3, 0x8c, 0xc3,
	0xae, 0xd0, 0x70, 0x05, 0xca, 0x2e, 0xad, 0xc0, 0xec, 0x91, 0x25, 0xcc, 0x3f, 0x4f, 0xd7, 0xfb,
	0x23, 0x8b, 0x9a, 0x90, 0xa1, 0x88, 0xf1, 0x5d, 0x50, 0x88, 0x30, 0xda, 0x23, 0xe3, 0x3b, 0x9c,
	0xba, 0x97, 0x52, 0xea, 0x5e, 0xa4, 0x11, 0xac, 0xe6, 0x0a, 0x25, 0x2e, 0x35, 0xb6, 0xef, 0xf8,
	0xaa, 0x29, 0x44, 0xf2, 0x45, 0xd2, 0x5a, 0xc5, 0x1f, 0x63, 0xad, 0x4f, 0xa0, 0x9d, 0x0c, 0xd6,
	0xc4, 0x6d, 0xd0, 0xcb, 0x0c, 0x75, 0x0e, 0x12, 0x34, 0x84, 0x4a, 0x13, 0x69, 0x0d, 0x56, 0x73,
	0xb7, 0xb3, 0x84, 0x08, 0x5d, 0xc7, 0xb2, 0x1c, 0x9b, 0xba, 0x38, 0x35, 0x0f, 0x0b, 0x0a, 0x5a,
	0xd0, 0x8b, 0x73, 0x30, 0x97, 0xa7, 0x85, 0x3c, 0x3d, 0xe0, 0x85, 0x6a, 0x8e, 0x02, 0xbb, 0xf1,
	0x85, 0x84, 0xa0, 0xb9, 0x83, 0xfd, 0x88, 0x07, 0xcd, 0x14, 0xfb, 0xac, 0x74, 0x89, 0xc3, 0x88,
	0x8b, 0x7e, 0x01, 0x55, 0x8d, 0x41, 0x14, 0x56, 0xeb, 0x71, 0xcf, 0x69, 0x25, 0x6d, 0x11, 0xdb,
	0x02, 0x5a, 0xf8, 0x5b, 0xda, 0x83, 0xe6, 0x51, 0x4a, 0xc6, 0x9b, 0xb0, 0xbb, 0x0e, 0x8b, 0x47,
	0x69, 0xf5, 0xa4, 0xdf, 0x42, 0xb9, 0xeb, 0x58, 0xa7, 0x7d, 0x7b, 0xe0, 0xb0, 0x0e, 0x70, 0xe4,
	0xa9, 0xbe, 0xe1, 0xd8, 0x81, 0x15, 0x82, 0x35, 0x73, 0x12, 0xc7, 0x3a, 0x15, 0xcf, 0x4f, 0x10,
	0xbc, 0x8e, 0x75, 0xca, 0x2b, 0x85, 0x35, 0x00, 0x86, 0xe1, 0x41, 0xca, 0x7b, 0xde, 0x0a, 0x83,
	0xec, 0x8b, 0x86, 0x98, 0xa3, 0x47, 0x2e, 0x8b, 0xe0, 0xb2, 0x3c, 0xcf, 0xd6, 0x27, 0xae, 0xf4,
	0x43, 0x01, 0x1a, 0x87, 0xfc, 0xf9, 0x3b, 0xc2, 0xb6, 0xce, 0x14, 0x59, 0x87, 0x66, 0xf0, 0x46,
	0x3a, 0x9e, 0x8e, 0xbd, 0x20, 0xa1, 0x54, 0xe4, 0xba, 0x80, 0x1f, 0x50, 0x30, 0xcf, 0x29, 0xbe,
	0xea, 0x0d, 0xb1, 0xaf, 0x8c, 0xa2, 0x9c, 0xc2, 0x21, 0x27, 0x06, 0xcb, 0x29, 0x43, 0x63, 0x10,
	0xab, 0x8a, 0xe6, 0xe8, 0xb2, 0xaf, 0xa3, 0x26, 0x94, 0x34, 0x3b, 0xa8, 0x87, 0xe8, 0x4f, 0x1a,
	0x7e, 0xea, 0x2b, 0xd5, 0xd3, 0x45, 0xb3, 0x26, 0x12, 0x4a, 0x95, 0xc1, 0x78, 0xa7, 0x46, 0x9d,
	0x8e, 0x79, 0xb7, 0xc2, 0x3b, 0x78, 0xde, 0x90, 0x03, 0x03, 0x1d, 0xb2, 0x36, 0x7e, 0x0d, 0x40,
	0xc7, 0xaa, 0xa9, 0xf8, 0xce, 0x39, 0xb6, 0x45, 0x4f, 0x52, 0xa1, 0x90, 0x63, 0x0a, 0x90, 0x7e,
	0x57, 0x04, 0x44, 0xcf, 0x98, 0x2a, 0x38, 0x9b, 0x50, 0x1a, 0x85, 0x19, 0x93, 0xfe, 0x9c, 0xba,
	0xee, 0xbb, 0x07, 0x8d, 0xe8, 0xf4, 0x3c, 0xa5, 0x97, 0x78, 0xa1, 0x1e, 0x9a, 0x80, 0xf5, 0xa1,
	0xb7, 0xa1, 0xaa, 0x5e, 0x60, 0x8f, 0x66, 0x80, 0xe8, 0xd4, 0x20, 0x40, 0x5d, 0xdb, 0x4f, 0xa5,
	0x80, 0x6b, 0xe9, 0xd4, 0xbc, 0x02, 0x65, 0x76, 0xf9, 0x74, 0x33, 0x3f, 0xf5, 0x3c, 0x5d, 0xd3,
	0x9d, 0x8f, 0xe0, 0x06, 0xc1, 0xae, 0xea, 0xd1, 0xbc, 0x3a, 0xb2, 0x5d, 0x55, 0x3b, 0x57, 0xa8,
	0x89, 0xd9, 0xe1, 0xcb, 0x32, 0x0a, 0x70, 0x27, 0x0c, 0xb5, 0x63, 0x0c, 0x7c, 0xe9, 0x8f, 0x45,
	0xb8, 0x3e, 0x66, 0x05, 0xe2, 0xa2, 0x2e, 0xd4, 0x09, 0xed, 0x2e, 0x0d, 0x7b, 0xe0, 0xc4, 0x9f,
	0xa7, 0xb5, 0xa4, 0x73, 0xa7, 0x7c, 0x45, 0x5e, 0x20, 0xe2, 0x17, 0x3b, 0xe9, 0x47, 0xc0, 0x9c,
	0x92, 0x31, 0x61, 0x36, 0xab, 0x6e, 0x2e, 0x8f, 0x05, 0x07, 0xf3, 0x76, 0x99, 0x1d, 0x89, 0xb9,
	0xdb, 0x2a, 0x54, 0x48, 0xa2, 0xaf, 0xad, 0xc9, 0x65, 0x12, 0x34, 0xb6, 0x77, 0x81, 0xbf, 0xd8,
	0x91, 0x27, 0xf2, 0x27, 0x88, 0x37, 0x09, 0x81, 0x1f, 0xfe, 0x1c, 0x80, 0x53, 0xb1, 0xa8, 0xbc,
	0x76, 0x55, 0x43, 0x57, 0x61, 0x18, 0x16, 0x95, 0x7f, 0x2e, 0xc0, 0x5a, 0xf7, 0x0c, 0x6b, 0xe7,
	0xfd, 0x41, 0x68, 0x95, 0xe7, 0x86, 0x7f, 0x76, 0xc4, 0xa6, 0x1b, 0xd9, 0xfe, 0x71, 0xc5, 0x4b,
	0x9a, 0xe1, 0x3e, 0xa5, 0x2c, 0xf7, 0x59, 0x86, 0x39, 0xd5, 0x72, 0x46, 0xa1, 0x47, 0x88, 0x15,
	0x85, 0x8b, 0xa9, 0x0c, 0xf7, 0x04, 0xb1, 0x92, 0x3a, 0xf0, 0xd6, 0x24, 0x4d, 0x89, 0x2b, 0x7d,
	0x5f, 0x84, 0xe5, 0xa9, 0x4f, 0xf1, 0xbf, 0xe6, 0xe5, 0x1d, 0x58, 0x70, 0xd5, 0xcb, 0xe8, 0x9e,
	0x79, 0xf5, 0x00, 0xae, 0x7a, 0x19, 0xcb, 0x36, 0x13, 0xe2, 0x3b, 0x66, 0xb7, 0x72, 0xdc, 0x6e,
	0x34, 0xb5, 0x38, 0x23, 0x9f, 0x18, 0x3a, 0x8e, 0xda, 0xa4, 0x92, 0x5c, 0x15, 0x30, 0x56, 0x7b,
	0xac, 0xc0, 0xcd, 0x3c, 0x9b, 0x9e, 0xd1, 0xb4, 0x6d, 0xeb, 0x51, 0x04, 0xfd, 0x28, 0x9f, 0x90,
	0xa0, 0xc6, 0xca, 0xe2, 0xf0, 0x74, 0x3c, 0x47, 0x57, 0x29, 0x50, 0x1c, 0x4f, 0x7a, 0xce, 0xd3,
	0x53, 0x5c, 0x12, 0x71, 0xd1, 0x16, 0xb0, 0x60, 0x50, 0x1c, 0xd7, 0x17, 0x05, 0xee, 0xbd, 0x8d,
	0x20, 0x3b, 0xc7, 0xa6, 0xae, 0xa9, 0x90, 0x3e, 0x70, 0x7d, 0x79, 0x9e, 0xee, 0x3b, 0x70, 0x7d,
	0xe9, 0x5f, 0xb3, 0xb0, 0x18, 0xc3, 0x6d, 0x3b, 0x5e, 0xd7, 0x34, 0xfe, 0xff, 0x26, 0xac, 0x62,
	0xcc, 0xc8, 0xa8, 0x83, 0x31, 0x93, 0x98, 0x2e, 0x35, 0x02, 0xc4, 0x17, 0x7c, 0xca, 0x34, 0x36,
	0x1f, 0x85, 0xe9, 0xe6, 0xa3, 0xd5, 0xbc, 0xf9, 0x68, 0xd4, 0x73, 0x92, 0xd6, 0x02, 0xaf, 0x96,
	0xc2, 0xa6, 0x93, 0xa4, 0xe6, 0xa3, 0xb5, 0xa9, 0xe6, 0xa3, 0xf5, 0xec, 0xf9, 0x68, 0xc6, 0xcc,
	0xb3, 0x31, 0xcd, 0xcc, 0xb3, 0xf9, 0x63, 0x67, 0x9e, 0x4f, 0xe0, 0x66, 0xb2, 0x3e, 0xe5, 0xee,
	0x45, 0xa3, 0xe4, 0x5d, 0x68, 0x68, 0x23, 0xcf, 0xa3, 0xe7, 0xbe, 0xc0, 0x1e, 0x89, 0xea, 0x9a,
	0xba, 0x00, 0x3f, 0xe3, 0x50, 0xe9, 0xf7, 0x85, 0x74, 0x03, 0x18, 0x30, 0x21, 0x2e, 0x7a, 0x0a,
	0xb5, 0xb8, 0x52, 0xc1, 0xec, 0xe1, 0x76, 0xae, 0xaa, 0x62, 0xef, 0x42, 0xcc, 0x8d, 0x49, 0x96,
	0x2e, 0xc5, 0x4c, 0x5d, 0xce, 0xd3, 0xe7, 0x39, 0x21, 0xea, 0xa9, 0xc9, 0x72, 0x68, 0x32, 0xc6,
	0x0b, 0xe9, 0x18, 0x17, 0x49, 0xa1, 0x18, 0x25, 0x85, 0xdb, 0x50, 0x75, 0x4c, 0x3d, 0x14, 0x58,
	0x62, 0x17, 0x01, 0x8e, 0xa9, 0x07, 0xc2, 0xfe, 0x32, 0x76, 0xf0, 0x40, 0x1a, 0x71, 0xaf, 0x12,
	0xb7, 0x0c, 0x73, 0x23, 0x46, 0x2c, 0x02, 0x47, 0xac, 0x68, 0x50, 0x68, 0xf4, 0x99, 0x50, 0x0c,
	0xdb, 0xc7, 0xde, 0x85, 0x6a, 0x2a, 0x04, 0x6b, 0x22, 0x76, 0x9a, 0x0c, 0xd3, 0x17, 0x88, 0x23,
	0xac, 0x65, 0xce, 0x5b, 0xca, 0x99, 0xf3, 0x96, 0xe7, 0xe9, 0x46, 0x84, 0xe5, 0xad, 0x63, 0x5a,
	0x7e, 0x09, 0xe3, 0xc4, 0x66, 0xd7, 0x42, 0xdb, 0xec, 0xd9, 0x35, 0xb7, 0x50, 0x30, 0xbb, 0x96,
	0x2e, 0xe1, 0x56, 0x3e, 0x63, 0xe2, 0xd2, 0xfa, 0x80, 0xa7, 0x4d, 0xfa, 0xac, 0x88, 0xc2, 0x98,
	0x01, 0xe8, 0xa3, 0x92, 0x2a, 0x0a, 0x29, 0xeb, 0xd9, 0x44, 0x51, 0xb8, 0x0a, 0x15, 0x4e, 0x40,
	0xfb, 0xb2, 0x12, 0x43, 0x97, 0x19, 0x60, 0x7f, 0x64, 0x8d, 0x9f, 0x69, 0x8b, 0xd6, 0x9b, 0x3f,
	0xc5, 0x99, 0xe2, 0x8c, 0x7f, 0xda, 0x33, 0xfd, 0xbb, 0x00, 0xf5, 0xe8, 0x0d, 0x61, 0x15, 0xd6,
	0xd8, 0xeb, 0x53, 0x18, 0x7b, 0x7d, 0xa6, 0x2e, 0x07, 0x62, 0x93, 0xb5, 0x52, 0x7a, 0xb2, 0x16,
	0x73, 0xd7, 0xd9, 0x8c, 0x22, 0x96, 0xbd, 0x63, 0xa3, 0xf0, 0xed, 0x67, 0xef, 0xd3, 0x89, 0x91,
	0xee, 0x22, 0xe6, 0xd2, 0x5d, 0xc4, 0x12, 0xcc, 0x31, 0x33, 0x13, 0xe1, 0xc4, 0xd7, 0xa8, 0x91,
	0x09, 0x6d, 0xcf, 0xa2, 0x63, 0x1e, 0x19, 0x96, 0x6b, 0xe2, 0xa9, 0x0e, 0x1a, 0xb1, 0x2b, 0xc6,
	0xd9, 0x5d, 0x02, 0x3a, 0x21, 0xd8, 0x8b, 0x58, 0xb2, 0xa2, 0xe6, 0xb5, 0x43, 0x7e, 0x13, 0x66,
	0xc3, 0x12, 0xa9, 0xba, 0xf9, 0x56, 0x32, 0x49, 0xa5, 0xf5, 0x95, 0x19, 0xad, 0xb4, 0xc7, 0xe6,
	0x0a, 0x5d, 0xce, 0x75, 0xcb, 0x34, 0xdf, 0xac, 0xd6, 0x90, 0xbe, 0x2f, 0x30, 0xaf, 0xce, 0xe6,
	0x47, 0x5c, 0xf4, 0x48, 0xa8, 0x98, 0x39, 0x59, 0x4f, 0x7a, 0x0e, 0x57, 0x10, 0x7d, 0x0e, 0x8d,
	0x11, 0xc1, 0x5e, 0xd0, 0x61, 0xd0, 0x52, 0x84, 0x17, 0xf7, 0xa9, 0xd9, 0xd5, 0xb8, 0x01, 0xe5,
	0xda, 0x28, 0x84, 0x1d, 0xb8, 0x3e, 0xaf, 0xf5, 0xbd, 0x0b, 0xec, 0x51, 0xfb, 0x87, 0xb5, 0x3e,
	0x05, 0x1c, 0x13, 0xe9, 0xcb, 0x74, 0xd0, 0xf4, 0xbe, 0xc5, 0x96, 0xeb, 0x3f, 0xa3, 0x1d, 0xff,
	0x6b, 0xd4, 0xb0, 0x19, 0x9f, 0x4a, 0x7e, 0x93, 0xf8, 0x2e, 0x17, 0x63, 0x3b, 0x75, 0x15, 0x14,
	0x34, 0x29, 0x03, 0x53, 0x1d, 0x8a, 0x41, 0x21, 0xf3, 0xe7, 0x6d, 0x53, 0x1d, 0x4a, 0x3a, 0xac,
	0x4d, 0x50, 0x9c, 0x35, 0x57, 0xc0, 0xe6, 0x16, 0xf1, 0xc6, 0x2a, 0xff, 0xad, 0x8d, 0xef, 0xae,
	0xb0, 0x7d, 0xd4, 0x94, 0xf7, 0x5f, 0xd2, 0x4e, 0x5d, 0x0c, 0x9c, 0xbd, 0xae, 0x63, 0x13, 0x1f,
	0xd5, 0x01, 0xf6, 0x0f, 0xf6, 0x7b, 0xca, 0xa1, 0xdc, 0x7f, 0xd1, 0x6b, 0xce, 0xa0, 0x06, 0x54,
	0x8f, 0xf6, 0xb6, 0x76, 0x77, 0x05, 0xa0, 0x80, 0x9a, 0xb0, 0xb0, 0x7f, 0x20, 0xef, 0x6d, 0x05,
	0x90, 0x22, 0xaa, 0x41, 0xe5, 0x49, 0x7f, 0x47, 0x2c, 0x4b, 0x68, 0x09, 0x16, 0xb7, 0x4f, 0x76,
	0x77, 0x95, 0xa3, 0x9e, 0xfc, 0xac, 0x27, 0x0b, 0xf0, 0xec, 0x7d, 0x02, 0xf5, 0x68, 0x52, 0xc1,
	0xe6, 0x30, 0x65, 0x98, 0xa5, 0xa2, 0x9a, 0x33, 0xa8, 0x0d, 0xcb, 0x4f, 0xb7, 0xfa, 0xbb, 0x5f,
	0x29, 0x47, 0xbd, 0xfd, 0xa7, 0xca, 0xde, 0xc1, 0x7e, 0xef, 0x2b, 0x65, 0xb7, 0xbf, 0xd7, 0x3f,
	0x6e, 0x16, 0x50, 0x07, 0x6e, 0x71, 0xdc, 0xa1, 0xdc, 0x7b, 0xd6, 0xdb, 0x3f, 0x56, 0x7a, 0x5f,
	0x76, 0x3f, 0xdf, 0xda, 0xdf, 0xe9, 0x09, 0x8a, 0x22, 0x5a, 0x81, 0xa5, 0xc3, 0x9e, 0xac, 0x1c,
	0xc8, 0x4f, 0x7b, 0xb2, 0xd2, 0x3d, 0x38, 0xd9, 0x3f, 0x16, 0xa8, 0xd2, 0xfd, 0x7f, 0x14, 0x60,
	0x29, 0x6e, 0x06, 0xfe, 0x1d, 0x83, 0x0a, 0xbf, 0x05, 0xad, 0xbd, 0xad, 0x9d, 0x7e, 0x57, 0x39,
	0x3a, 0xec, 0xcb, 0xfd, 0x63, 0xa5, 0xb7, 0xbd, 0xdd, 0xeb, 0x1e, 0x2b, 0x42, 0xa1, 0x55, 0xb8,
	0x99, 0x85, 0xdd, 0x7d, 0xf6, 0x61, 0xb3, 0x90, 0x8f, 0xdc, 0x6c, 0x16, 0xf3, 0x91, 0x1f, 0x35,
	0x4b, 0xf9, 0xc8, 0x9f, 0x35, 0x67, 0xd1, 0x5d, 0xe8, 0xe4, 0xec, 0x54, 0xf6, 0x0e, 0xe4, 0x9e,
	0xf2, 0xf8, 0xd1, 0xa3, 0xe6, 0xb5, 0xfb, 0x4f, 0xa1, 0x11, 0x15, 0xe6, 0xbc, 0x6c, 0x04, 0x98,
	0xe3, 0x96, 0x6d, 0xce, 0x20, 0x04, 0x75, 0x11, 0xa5, 0xbb, 0x8e, 0xef, 0x63, 0xef, 0xb2, 0x59,
	0x40, 0x8b, 0x50, 0xdb, 0xf1, 0xd4, 0xd3, 0xee, 0x99, 0x6a, 0x78, 0x3b, 0xaa, 0x85, 0x9b, 0xc5,
	0xcd, 0xbf, 0x2d, 0x25, 0x46, 0xe0, 0x4f, 0x54, 0xed, 0x1c, 0xdb, 0x3a, 0x3a, 0x81, 0x7a, 0x72,
	0xa0, 0x89, 0x52, 0x35, 0xd1, 0xd8, 0xe7, 0xca, 0x76, 0x67, 0x32, 0x01, 0x71, 0xa5, 0x19, 0xca,
	0x36, 0x39, 0xba, 0x4b, 0xb3, 0x1d, 0xfb, 0xe8, 0x98, 0x66, 0x9b, 0xf1, 0x59, 0x91, 0xb1, 0x4d,
	0xc6, 0x48, 0x9a, 0xed, 0xd8, 0x67, 0xc7, 0x34, 0xdb, 0xf1, 0x4f, 0x90, 0xd2, 0x0c, 0xfa, 0x1a,
	0x16, 0xc7, 0xbe, 0xbb, 0xa0, 0xd4, 0xac, 0x33, 0xeb, 0x3b, 0x63, 0xfb, 0xed, 0x2b, 0x69, 0x18,
	0x7f, 0x35, 0xfd, 0x89, 0xf6, 0xd0, 0xb1, 0x75, 0xf4, 0xf6, 0x24, 0x3b, 0x8a, 0x4f, 0x26, 0xed,
	0xbb, 0x57, 0x13, 0x05, 0x22, 0xc6, 0x3f, 0xee, 0xa4, 0x45, 0x64, 0x7e, 0x3f, 0x4a, 0x8b, 0xc8,
	0xfe, 0x46, 0x24, 0xcd, 0x20, 0x17, 0x6e, 0xe6, 0xcc, 0xbe, 0xd1, 0xfa, 0x24, 0x2d, 0xe3, 0x43,
	0xdf, 0xf6, 0x7b, 0x53, 0x52, 0x06, 0x12, 0x33, 0xc6, 0xd6, 0x59, 0x12, 0xf3, 0x47, 0xea, 0x69,
	0x89, 0x13, 0xe6, 0xe0, 0x5c, 0x62, 0xce, 0xc8, 0x39, 0x2d, 0x31, 0x7f, 0xb0, 0x9d, 0x96, 0x38,
	0x69, 0x86, 0x3d, 0x83, 0x64, 0xa8, 0x25, 0x26, 0xba, 0x28, 0xf5, 0xdc, 0xa7, 0xa7, 0xc7, 0xed,
	0xdb, 0x13, 0xf1, 0x01, 0xcf, 0x9d, 0x49, 0x3c, 0x77, 0xae, 0xe0, 0xb9, 0x93, 0xc1, 0xf3, 0xcb,
	0x78, 0x16, 0xe2, 0x11, 0xd2, 0x49, 0x6b, 0x92, 0x1e, 0x8b, 0xb6, 0xef, 0x5c, 0x41, 0x11, 0x04,
	0x75, 0x72, 0x64, 0x81, 0x6e, 0x8f, 0x6f, 0x4b, 0x94, 0x33, 0xed, 0xce, 0x64, 0x82, 0x98, 0xf3,
	0x64, 0x15, 0x30, 0x19, 0xce, 0x93, 0x53, 0x37, 0x65, 0x38, 0x4f, 0x5e, 0x45, 0x24, 0xcd, 0xa0,
	0x21, 0xdc, 0xc8, 0x6a, 0x40, 0xd1, 0x3b, 0x93, 0x3c, 0x30, 0xec, 0x74, 0xdb, 0xf7, 0xa6, 0x21,
	0xcb, 0x16, 0xc4, 0x1b, 0xbe, 0xc9, 0x82, 0xc2, 0x16, 0x74, 0xb2, 0xa0, 0xa8, 0x77, 0x94, 0x66,
	0x10, 0x49, 0x77, 0x96, 0x51, 0x57, 0x85, 0x26, 0xc6, 0x55, 0xa2, 0xad, 0x6b, 0xdf, 0x9f, 0x96,
	0x34, 0x5b, 0x68, 0xd4, 0xf6, 0x4c, 0x16, 0x9a, 0xe8, 0xbb, 0x26, 0x0b, 0x4d, 0x76, 0x52, 0xd2,
	0x0c, 0x7a, 0x99, 0xfe, 0xba, 0x1e, 0x7c, 0x23, 0x9e, 0x68, 0xac, 0xe8, 0x13, 0x73, 0xfb, 0xdd,
	0xa9, 0xe8, 0x98, 0xac, 0x4b, 0x68, 0xe7, 0xcf, 0x60, 0xd1, 0xfb, 0xa9, 0x59, 0xf7, 0xa4, 0xb9,
	0x72, 0xfb, 0x83, 0xe9, 0x89, 0x99, 0x68, 0x3d, 0x36, 0xb7, 0x8f, 0xc9, 0xbc, 0x9b, 0x13, 0xa7,
	0x49, 0x61, 0xef, 0x4c, 0x41, 0xc5, 0xa4, 0x5c, 0xa4, 0xbf, 0x5e, 0xc7, 0x8b, 0xe5, 0x89, 0xf7,
	0x92, 0x2c, 0xd6, 0xdb, 0xef, 0x4f, 0x4d, 0xcb, 0xe4, 0xf6, 0xd9, 0x23, 0x18, 0xdd, 0x6d, 0x97,
	0x8d, 0xc2, 0x57, 0x36, 0xe4, 0xe0, 0xaf, 0xa2, 0xcf, 0x36, 0x37, 0x68, 0x67, 0x2d, 0xab, 0xf6,
	0x90, 0xf1, 0x5f, 0x4e, 0xa0, 0x18, 0xb9, 0x60, 0xf5, 0x05, 0xfb, 0x36, 0xc8, 0x58, 0x89, 0x9e,
	0x8f, 0x4c, 0x62, 0x94, 0x44, 0x05, 0x3b, 0x04, 0xaf, 0x5f, 0xb1, 0x70, 0xa5, 0xc5, 0xf6, 0xc8,
	0xc2, 0x6f, 0xaa, 0xd8, 0x2e, 0x3b, 0xa3, 0x60, 0xf6, 0x5f, 0x50, 0x6d, 0x51, 0xc6, 0x06, 0x21,
	0x23, 0x5e, 0xb5, 0x30, 0x2c, 0xba, 0x95, 0xd8, 0x21, 0x63, 0xd7, 0x54, 0x35, 0x2e, 0x6a, 0x5c,
	0xb5, 0x9e, 0xe5, 0xfa, 0x97, 0x82, 0xd9, 0x57, 0xd4, 0x66, 0xf6, 0xb6, 0x61, 0xab, 0xb6, 0x66,
	0xa8, 0xe6, 0xb6, 0x61, 0x62, 0xd4, 0x49, 0x50, 0xa7, 0xd1, 0xfc, 0x8d, 0x98, 0x4c, 0x41, 0x59,
	0x3f, 0x79, 0xf4, 0x62, 0x63, 0xe8, 0x98, 0xaa, 0x3d, 0xdc, 0x78, 0xbc, 0xe9, 0xfb, 0x1b, 0x9a,
	0x63, 0x3d, 0x64, 0x7f, 0xf9, 0xd5, 0x1c, 0xf3, 0x21, 0x6d, 0xfd, 0x0c, 0x0d, 0x93, 0xc4, 0x7f,
	0x91, 0x4f, 0xe7, 0x18, 0xfe, 0xa3, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0x29, 0x7e, 0x93, 0xd5,
	0xbe, 0x2c, 0x00, 0x00,
}
