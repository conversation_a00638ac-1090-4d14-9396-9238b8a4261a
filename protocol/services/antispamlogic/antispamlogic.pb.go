// Code generated by protoc-gen-gogo.
// source: src/antispamlogic/antispamlogic.proto
// DO NOT EDIT!

/*
	Package antispamlogic is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/antispamlogic/antispamlogic.proto

	It has these top-level messages:
		UserBehaviorCheckReq
		UserBehaviorCheckResp
		ShumeiDeviceInfo
		AntispamUserInfo
		AntispamChanInfo
		AntispamGroupInfo
		SubTextInfo
		TextCheckReq
		TextCheckResp
		DVCommCheckReq
		DVLoginCheckReq
		DVCheckResp
		AntispamMissionInfo
		AntispamExchangeInfo
		AntispamTaskEventCheckReq
		AntispamExchangeEventCheckReq
		AntispamEventCheckResp
		UserBehaviorResultReq
		UserBehaviorResultResp
		BatchUserBehaviorCheckReq
		UserBehaviorInfo
		BatchUserBehaviorCheckResp
		AntispamEventCheckReq
*/
package antispamlogic

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import math3 "math"

import io1 "io"
import math4 "math"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type RiskType int32

const (
	RiskType_RESULT_DEFAULT           RiskType = 0
	RiskType_RESULT_HIGH_RISK_ACCOUNT RiskType = 1
)

var RiskType_name = map[int32]string{
	0: "RESULT_DEFAULT",
	1: "RESULT_HIGH_RISK_ACCOUNT",
}
var RiskType_value = map[string]int32{
	"RESULT_DEFAULT":           0,
	"RESULT_HIGH_RISK_ACCOUNT": 1,
}

func (x RiskType) Enum() *RiskType {
	p := new(RiskType)
	*p = x
	return p
}
func (x RiskType) String() string {
	return proto.EnumName(RiskType_name, int32(x))
}
func (x *RiskType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RiskType_value, data, "RiskType")
	if err != nil {
		return err
	}
	*x = RiskType(value)
	return nil
}
func (RiskType) EnumDescriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{0} }

type ShumeiOSType int32

const (
	ShumeiOSType_none    ShumeiOSType = 0
	ShumeiOSType_android ShumeiOSType = 1
	ShumeiOSType_ios     ShumeiOSType = 2
	ShumeiOSType_weapp   ShumeiOSType = 3
	ShumeiOSType_web     ShumeiOSType = 4
)

var ShumeiOSType_name = map[int32]string{
	0: "none",
	1: "android",
	2: "ios",
	3: "weapp",
	4: "web",
}
var ShumeiOSType_value = map[string]int32{
	"none":    0,
	"android": 1,
	"ios":     2,
	"weapp":   3,
	"web":     4,
}

func (x ShumeiOSType) Enum() *ShumeiOSType {
	p := new(ShumeiOSType)
	*p = x
	return p
}
func (x ShumeiOSType) String() string {
	return proto.EnumName(ShumeiOSType_name, int32(x))
}
func (x *ShumeiOSType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ShumeiOSType_value, data, "ShumeiOSType")
	if err != nil {
		return err
	}
	*x = ShumeiOSType(value)
	return nil
}
func (ShumeiOSType) EnumDescriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{1} }

type UserBehaviorCheckReq_BehaviorType int32

const (
	UserBehaviorCheckReq_friend_match      UserBehaviorCheckReq_BehaviorType = 1
	UserBehaviorCheckReq_ugc_follow        UserBehaviorCheckReq_BehaviorType = 2
	UserBehaviorCheckReq_im_stranger       UserBehaviorCheckReq_BehaviorType = 3
	UserBehaviorCheckReq_im_send_msg       UserBehaviorCheckReq_BehaviorType = 4
	UserBehaviorCheckReq_im_send_text      UserBehaviorCheckReq_BehaviorType = 5
	UserBehaviorCheckReq_ugc_been_followed UserBehaviorCheckReq_BehaviorType = 6
	UserBehaviorCheckReq_ugc_post          UserBehaviorCheckReq_BehaviorType = 7
	UserBehaviorCheckReq_ugc_comment_text  UserBehaviorCheckReq_BehaviorType = 8
	UserBehaviorCheckReq_ugc_comment_image UserBehaviorCheckReq_BehaviorType = 9
	UserBehaviorCheckReq_ugc_attitude      UserBehaviorCheckReq_BehaviorType = 10
	UserBehaviorCheckReq_game_match        UserBehaviorCheckReq_BehaviorType = 11
	UserBehaviorCheckReq_hold_mic          UserBehaviorCheckReq_BehaviorType = 12
	UserBehaviorCheckReq_guild_create      UserBehaviorCheckReq_BehaviorType = 13
	UserBehaviorCheckReq_guild_join        UserBehaviorCheckReq_BehaviorType = 14
	UserBehaviorCheckReq_near_user         UserBehaviorCheckReq_BehaviorType = 15
	UserBehaviorCheckReq_room_text_msg     UserBehaviorCheckReq_BehaviorType = 16
	UserBehaviorCheckReq_room_atta_msg     UserBehaviorCheckReq_BehaviorType = 17
	UserBehaviorCheckReq_draw_game         UserBehaviorCheckReq_BehaviorType = 18
	UserBehaviorCheckReq_apply_mic         UserBehaviorCheckReq_BehaviorType = 19
	UserBehaviorCheckReq_usertag_match     UserBehaviorCheckReq_BehaviorType = 20
	UserBehaviorCheckReq_black_list        UserBehaviorCheckReq_BehaviorType = 21
)

var UserBehaviorCheckReq_BehaviorType_name = map[int32]string{
	1:  "friend_match",
	2:  "ugc_follow",
	3:  "im_stranger",
	4:  "im_send_msg",
	5:  "im_send_text",
	6:  "ugc_been_followed",
	7:  "ugc_post",
	8:  "ugc_comment_text",
	9:  "ugc_comment_image",
	10: "ugc_attitude",
	11: "game_match",
	12: "hold_mic",
	13: "guild_create",
	14: "guild_join",
	15: "near_user",
	16: "room_text_msg",
	17: "room_atta_msg",
	18: "draw_game",
	19: "apply_mic",
	20: "usertag_match",
	21: "black_list",
}
var UserBehaviorCheckReq_BehaviorType_value = map[string]int32{
	"friend_match":      1,
	"ugc_follow":        2,
	"im_stranger":       3,
	"im_send_msg":       4,
	"im_send_text":      5,
	"ugc_been_followed": 6,
	"ugc_post":          7,
	"ugc_comment_text":  8,
	"ugc_comment_image": 9,
	"ugc_attitude":      10,
	"game_match":        11,
	"hold_mic":          12,
	"guild_create":      13,
	"guild_join":        14,
	"near_user":         15,
	"room_text_msg":     16,
	"room_atta_msg":     17,
	"draw_game":         18,
	"apply_mic":         19,
	"usertag_match":     20,
	"black_list":        21,
}

func (x UserBehaviorCheckReq_BehaviorType) Enum() *UserBehaviorCheckReq_BehaviorType {
	p := new(UserBehaviorCheckReq_BehaviorType)
	*p = x
	return p
}
func (x UserBehaviorCheckReq_BehaviorType) String() string {
	return proto.EnumName(UserBehaviorCheckReq_BehaviorType_name, int32(x))
}
func (x *UserBehaviorCheckReq_BehaviorType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserBehaviorCheckReq_BehaviorType_value, data, "UserBehaviorCheckReq_BehaviorType")
	if err != nil {
		return err
	}
	*x = UserBehaviorCheckReq_BehaviorType(value)
	return nil
}
func (UserBehaviorCheckReq_BehaviorType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{0, 0}
}

type UserBehaviorCheckResp_ResultType int32

const (
	UserBehaviorCheckResp_NORMAL    UserBehaviorCheckResp_ResultType = 0
	UserBehaviorCheckResp_SUSPICION UserBehaviorCheckResp_ResultType = 1
	UserBehaviorCheckResp_BLACK     UserBehaviorCheckResp_ResultType = 2
)

var UserBehaviorCheckResp_ResultType_name = map[int32]string{
	0: "NORMAL",
	1: "SUSPICION",
	2: "BLACK",
}
var UserBehaviorCheckResp_ResultType_value = map[string]int32{
	"NORMAL":    0,
	"SUSPICION": 1,
	"BLACK":     2,
}

func (x UserBehaviorCheckResp_ResultType) Enum() *UserBehaviorCheckResp_ResultType {
	p := new(UserBehaviorCheckResp_ResultType)
	*p = x
	return p
}
func (x UserBehaviorCheckResp_ResultType) String() string {
	return proto.EnumName(UserBehaviorCheckResp_ResultType_name, int32(x))
}
func (x *UserBehaviorCheckResp_ResultType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserBehaviorCheckResp_ResultType_value, data, "UserBehaviorCheckResp_ResultType")
	if err != nil {
		return err
	}
	*x = UserBehaviorCheckResp_ResultType(value)
	return nil
}
func (UserBehaviorCheckResp_ResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{1, 0}
}

type TextCheckResp_RESULT int32

const (
	TextCheckResp_PASS      TextCheckResp_RESULT = 0
	TextCheckResp_SUSPICION TextCheckResp_RESULT = 1
	TextCheckResp_NOT_PASS  TextCheckResp_RESULT = 2
)

var TextCheckResp_RESULT_name = map[int32]string{
	0: "PASS",
	1: "SUSPICION",
	2: "NOT_PASS",
}
var TextCheckResp_RESULT_value = map[string]int32{
	"PASS":      0,
	"SUSPICION": 1,
	"NOT_PASS":  2,
}

func (x TextCheckResp_RESULT) Enum() *TextCheckResp_RESULT {
	p := new(TextCheckResp_RESULT)
	*p = x
	return p
}
func (x TextCheckResp_RESULT) String() string {
	return proto.EnumName(TextCheckResp_RESULT_name, int32(x))
}
func (x *TextCheckResp_RESULT) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(TextCheckResp_RESULT_value, data, "TextCheckResp_RESULT")
	if err != nil {
		return err
	}
	*x = TextCheckResp_RESULT(value)
	return nil
}
func (TextCheckResp_RESULT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{8, 0}
}

type UserBehaviorResultReq_BehaviorResult int32

const (
	UserBehaviorResultReq_PASS     UserBehaviorResultReq_BehaviorResult = 0
	UserBehaviorResultReq_NOT_PASS UserBehaviorResultReq_BehaviorResult = 1
)

var UserBehaviorResultReq_BehaviorResult_name = map[int32]string{
	0: "PASS",
	1: "NOT_PASS",
}
var UserBehaviorResultReq_BehaviorResult_value = map[string]int32{
	"PASS":     0,
	"NOT_PASS": 1,
}

func (x UserBehaviorResultReq_BehaviorResult) Enum() *UserBehaviorResultReq_BehaviorResult {
	p := new(UserBehaviorResultReq_BehaviorResult)
	*p = x
	return p
}
func (x UserBehaviorResultReq_BehaviorResult) String() string {
	return proto.EnumName(UserBehaviorResultReq_BehaviorResult_name, int32(x))
}
func (x *UserBehaviorResultReq_BehaviorResult) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserBehaviorResultReq_BehaviorResult_value, data, "UserBehaviorResultReq_BehaviorResult")
	if err != nil {
		return err
	}
	*x = UserBehaviorResultReq_BehaviorResult(value)
	return nil
}
func (UserBehaviorResultReq_BehaviorResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{17, 0}
}

type AntispamEventCheckReqCheckEventType int32

const (
	AntispamEventCheckReq_undefined AntispamEventCheckReqCheckEventType = 0
	AntispamEventCheckReq_fission   AntispamEventCheckReqCheckEventType = 1
	AntispamEventCheckReq_coupon    AntispamEventCheckReqCheckEventType = 2
	AntispamEventCheckReq_like      AntispamEventCheckReqCheckEventType = 3
)

var AntispamEventCheckReqCheckEventType_name = map[int32]string{
	0: "undefined",
	1: "fission",
	2: "coupon",
	3: "like",
}
var AntispamEventCheckReqCheckEventType_value = map[string]int32{
	"undefined": 0,
	"fission":   1,
	"coupon":    2,
	"like":      3,
}

func (x AntispamEventCheckReqCheckEventType) Enum() *AntispamEventCheckReqCheckEventType {
	p := new(AntispamEventCheckReqCheckEventType)
	*p = x
	return p
}
func (x AntispamEventCheckReqCheckEventType) String() string {
	return proto.EnumName(AntispamEventCheckReqCheckEventType_name, int32(x))
}
func (x *AntispamEventCheckReqCheckEventType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(AntispamEventCheckReqCheckEventType_value, data, "AntispamEventCheckReqCheckEventType")
	if err != nil {
		return err
	}
	*x = AntispamEventCheckReqCheckEventType(value)
	return nil
}
func (AntispamEventCheckReqCheckEventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{22, 0}
}

type UserBehaviorCheckReq struct {
	Uid              uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	BehaviorTypeList []uint32 `protobuf:"varint,2,rep,name=behavior_type_list,json=behaviorTypeList" json:"behavior_type_list,omitempty"`
	ToUid            uint32   `protobuf:"varint,3,opt,name=to_uid,json=toUid" json:"to_uid"`
}

func (m *UserBehaviorCheckReq) Reset()         { *m = UserBehaviorCheckReq{} }
func (m *UserBehaviorCheckReq) String() string { return proto.CompactTextString(m) }
func (*UserBehaviorCheckReq) ProtoMessage()    {}
func (*UserBehaviorCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{0}
}

func (m *UserBehaviorCheckReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBehaviorCheckReq) GetBehaviorTypeList() []uint32 {
	if m != nil {
		return m.BehaviorTypeList
	}
	return nil
}

func (m *UserBehaviorCheckReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type UserBehaviorCheckResp struct {
	HitUid          uint32 `protobuf:"varint,1,opt,name=hit_uid,json=hitUid" json:"hit_uid"`
	HitBehaviorType uint32 `protobuf:"varint,2,opt,name=hit_behavior_type,json=hitBehaviorType" json:"hit_behavior_type"`
	ResultType      uint32 `protobuf:"varint,3,opt,name=result_type,json=resultType" json:"result_type"`
}

func (m *UserBehaviorCheckResp) Reset()         { *m = UserBehaviorCheckResp{} }
func (m *UserBehaviorCheckResp) String() string { return proto.CompactTextString(m) }
func (*UserBehaviorCheckResp) ProtoMessage()    {}
func (*UserBehaviorCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{1}
}

func (m *UserBehaviorCheckResp) GetHitUid() uint32 {
	if m != nil {
		return m.HitUid
	}
	return 0
}

func (m *UserBehaviorCheckResp) GetHitBehaviorType() uint32 {
	if m != nil {
		return m.HitBehaviorType
	}
	return 0
}

func (m *UserBehaviorCheckResp) GetResultType() uint32 {
	if m != nil {
		return m.ResultType
	}
	return 0
}

type ShumeiDeviceInfo struct {
	SmDeviceId string `protobuf:"bytes,1,opt,name=sm_device_id,json=smDeviceId" json:"sm_device_id"`
}

func (m *ShumeiDeviceInfo) Reset()                    { *m = ShumeiDeviceInfo{} }
func (m *ShumeiDeviceInfo) String() string            { return proto.CompactTextString(m) }
func (*ShumeiDeviceInfo) ProtoMessage()               {}
func (*ShumeiDeviceInfo) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{2} }

func (m *ShumeiDeviceInfo) GetSmDeviceId() string {
	if m != nil {
		return m.SmDeviceId
	}
	return ""
}

type AntispamUserInfo struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Username    string `protobuf:"bytes,2,opt,name=username" json:"username"`
	Alias       string `protobuf:"bytes,3,opt,name=alias" json:"alias"`
	Nickname    string `protobuf:"bytes,4,opt,name=nickname" json:"nickname"`
	Tokenid     string `protobuf:"bytes,5,opt,name=tokenid" json:"tokenid"`
	Gender      uint32 `protobuf:"varint,6,opt,name=gender" json:"gender"`
	PhoneNumber string `protobuf:"bytes,7,opt,name=phone_number,json=phoneNumber" json:"phone_number"`
}

func (m *AntispamUserInfo) Reset()                    { *m = AntispamUserInfo{} }
func (m *AntispamUserInfo) String() string            { return proto.CompactTextString(m) }
func (*AntispamUserInfo) ProtoMessage()               {}
func (*AntispamUserInfo) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{3} }

func (m *AntispamUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AntispamUserInfo) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *AntispamUserInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *AntispamUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AntispamUserInfo) GetTokenid() string {
	if m != nil {
		return m.Tokenid
	}
	return ""
}

func (m *AntispamUserInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *AntispamUserInfo) GetPhoneNumber() string {
	if m != nil {
		return m.PhoneNumber
	}
	return ""
}

type AntispamChanInfo struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	DisplayId uint32 `protobuf:"varint,2,opt,name=display_id,json=displayId" json:"display_id"`
	Name      string `protobuf:"bytes,3,opt,name=name" json:"name"`
}

func (m *AntispamChanInfo) Reset()                    { *m = AntispamChanInfo{} }
func (m *AntispamChanInfo) String() string            { return proto.CompactTextString(m) }
func (*AntispamChanInfo) ProtoMessage()               {}
func (*AntispamChanInfo) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{4} }

func (m *AntispamChanInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AntispamChanInfo) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *AntispamChanInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type AntispamGroupInfo struct {
	GroupId uint32 `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
}

func (m *AntispamGroupInfo) Reset()                    { *m = AntispamGroupInfo{} }
func (m *AntispamGroupInfo) String() string            { return proto.CompactTextString(m) }
func (*AntispamGroupInfo) ProtoMessage()               {}
func (*AntispamGroupInfo) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{5} }

func (m *AntispamGroupInfo) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type SubTextInfo struct {
	Text                string `protobuf:"bytes,1,opt,name=text" json:"text"`
	SubTextCheckContext []byte `protobuf:"bytes,2,opt,name=sub_text_check_context,json=subTextCheckContext" json:"sub_text_check_context"`
	DataType            uint32 `protobuf:"varint,3,opt,name=data_type,json=dataType" json:"data_type"`
	DataSubScene        string `protobuf:"bytes,4,opt,name=data_sub_scene,json=dataSubScene" json:"data_sub_scene"`
}

func (m *SubTextInfo) Reset()                    { *m = SubTextInfo{} }
func (m *SubTextInfo) String() string            { return proto.CompactTextString(m) }
func (*SubTextInfo) ProtoMessage()               {}
func (*SubTextInfo) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{6} }

func (m *SubTextInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SubTextInfo) GetSubTextCheckContext() []byte {
	if m != nil {
		return m.SubTextCheckContext
	}
	return nil
}

func (m *SubTextInfo) GetDataType() uint32 {
	if m != nil {
		return m.DataType
	}
	return 0
}

func (m *SubTextInfo) GetDataSubScene() string {
	if m != nil {
		return m.DataSubScene
	}
	return ""
}

type TextCheckReq struct {
	Text             string             `protobuf:"bytes,1,req,name=text" json:"text"`
	UserInfo         *AntispamUserInfo  `protobuf:"bytes,2,opt,name=user_info,json=userInfo" json:"user_info,omitempty"`
	ToUserInfo       *AntispamUserInfo  `protobuf:"bytes,3,opt,name=to_user_info,json=toUserInfo" json:"to_user_info,omitempty"`
	Ip               string             `protobuf:"bytes,4,opt,name=ip" json:"ip"`
	DeviceIdBase64   string             `protobuf:"bytes,5,opt,name=device_id_base64,json=deviceIdBase64" json:"device_id_base64"`
	DataType         uint32             `protobuf:"varint,6,opt,name=data_type,json=dataType" json:"data_type"`
	DataSubScene     string             `protobuf:"bytes,7,opt,name=data_sub_scene,json=dataSubScene" json:"data_sub_scene"`
	SmDeviceInfo     *ShumeiDeviceInfo  `protobuf:"bytes,8,opt,name=sm_device_info,json=smDeviceInfo" json:"sm_device_info,omitempty"`
	ChannelInfo      *AntispamChanInfo  `protobuf:"bytes,9,opt,name=channel_info,json=channelInfo" json:"channel_info,omitempty"`
	GroupInfo        *AntispamGroupInfo `protobuf:"bytes,10,opt,name=group_info,json=groupInfo" json:"group_info,omitempty"`
	TextCheckContext []byte             `protobuf:"bytes,11,opt,name=text_check_context,json=textCheckContext" json:"text_check_context"`
	AsyncStatus      bool               `protobuf:"varint,12,opt,name=async_status,json=asyncStatus" json:"async_status"`
	ProduceTime      uint32             `protobuf:"varint,13,opt,name=produce_time,json=produceTime" json:"produce_time"`
	SubTextInfoList  []*SubTextInfo     `protobuf:"bytes,14,rep,name=sub_text_info_list,json=subTextInfoList" json:"sub_text_info_list,omitempty"`
}

func (m *TextCheckReq) Reset()                    { *m = TextCheckReq{} }
func (m *TextCheckReq) String() string            { return proto.CompactTextString(m) }
func (*TextCheckReq) ProtoMessage()               {}
func (*TextCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{7} }

func (m *TextCheckReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *TextCheckReq) GetUserInfo() *AntispamUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *TextCheckReq) GetToUserInfo() *AntispamUserInfo {
	if m != nil {
		return m.ToUserInfo
	}
	return nil
}

func (m *TextCheckReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *TextCheckReq) GetDeviceIdBase64() string {
	if m != nil {
		return m.DeviceIdBase64
	}
	return ""
}

func (m *TextCheckReq) GetDataType() uint32 {
	if m != nil {
		return m.DataType
	}
	return 0
}

func (m *TextCheckReq) GetDataSubScene() string {
	if m != nil {
		return m.DataSubScene
	}
	return ""
}

func (m *TextCheckReq) GetSmDeviceInfo() *ShumeiDeviceInfo {
	if m != nil {
		return m.SmDeviceInfo
	}
	return nil
}

func (m *TextCheckReq) GetChannelInfo() *AntispamChanInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *TextCheckReq) GetGroupInfo() *AntispamGroupInfo {
	if m != nil {
		return m.GroupInfo
	}
	return nil
}

func (m *TextCheckReq) GetTextCheckContext() []byte {
	if m != nil {
		return m.TextCheckContext
	}
	return nil
}

func (m *TextCheckReq) GetAsyncStatus() bool {
	if m != nil {
		return m.AsyncStatus
	}
	return false
}

func (m *TextCheckReq) GetProduceTime() uint32 {
	if m != nil {
		return m.ProduceTime
	}
	return 0
}

func (m *TextCheckReq) GetSubTextInfoList() []*SubTextInfo {
	if m != nil {
		return m.SubTextInfoList
	}
	return nil
}

type TextCheckResp struct {
	Result    uint32 `protobuf:"varint,1,req,name=result" json:"result"`
	LabelInfo string `protobuf:"bytes,2,opt,name=label_info,json=labelInfo" json:"label_info"`
	RequestId string `protobuf:"bytes,3,opt,name=requestId" json:"requestId"`
	RiskType  uint32 `protobuf:"varint,4,opt,name=risk_type,json=riskType" json:"risk_type"`
}

func (m *TextCheckResp) Reset()                    { *m = TextCheckResp{} }
func (m *TextCheckResp) String() string            { return proto.CompactTextString(m) }
func (*TextCheckResp) ProtoMessage()               {}
func (*TextCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{8} }

func (m *TextCheckResp) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *TextCheckResp) GetLabelInfo() string {
	if m != nil {
		return m.LabelInfo
	}
	return ""
}

func (m *TextCheckResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *TextCheckResp) GetRiskType() uint32 {
	if m != nil {
		return m.RiskType
	}
	return 0
}

type DVCommCheckReq struct {
	UserInfo   *AntispamUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo" json:"user_info,omitempty"`
	ToUserInfo *AntispamUserInfo `protobuf:"bytes,2,opt,name=to_user_info,json=toUserInfo" json:"to_user_info,omitempty"`
	EventType  string            `protobuf:"bytes,3,opt,name=event_type,json=eventType" json:"event_type"`
	EventTime  string            `protobuf:"bytes,4,opt,name=event_time,json=eventTime" json:"event_time"`
	ExtInfo    string            `protobuf:"bytes,5,opt,name=ext_info,json=extInfo" json:"ext_info"`
}

func (m *DVCommCheckReq) Reset()                    { *m = DVCommCheckReq{} }
func (m *DVCommCheckReq) String() string            { return proto.CompactTextString(m) }
func (*DVCommCheckReq) ProtoMessage()               {}
func (*DVCommCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{9} }

func (m *DVCommCheckReq) GetUserInfo() *AntispamUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *DVCommCheckReq) GetToUserInfo() *AntispamUserInfo {
	if m != nil {
		return m.ToUserInfo
	}
	return nil
}

func (m *DVCommCheckReq) GetEventType() string {
	if m != nil {
		return m.EventType
	}
	return ""
}

func (m *DVCommCheckReq) GetEventTime() string {
	if m != nil {
		return m.EventTime
	}
	return ""
}

func (m *DVCommCheckReq) GetExtInfo() string {
	if m != nil {
		return m.ExtInfo
	}
	return ""
}

type DVLoginCheckReq struct {
	UserInfo      *AntispamUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo" json:"user_info,omitempty"`
	EventType     string            `protobuf:"bytes,2,opt,name=event_type,json=eventType" json:"event_type"`
	Ip            string            `protobuf:"bytes,3,opt,name=ip" json:"ip"`
	ClientVersion uint32            `protobuf:"varint,4,opt,name=client_version,json=clientVersion" json:"client_version"`
	EventTime     string            `protobuf:"bytes,5,opt,name=event_time,json=eventTime" json:"event_time"`
	DeviceId      string            `protobuf:"bytes,6,opt,name=device_id,json=deviceId" json:"device_id"`
	OsVer         string            `protobuf:"bytes,7,opt,name=os_ver,json=osVer" json:"os_ver"`
	Token         string            `protobuf:"bytes,8,opt,name=token" json:"token"`
}

func (m *DVLoginCheckReq) Reset()                    { *m = DVLoginCheckReq{} }
func (m *DVLoginCheckReq) String() string            { return proto.CompactTextString(m) }
func (*DVLoginCheckReq) ProtoMessage()               {}
func (*DVLoginCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{10} }

func (m *DVLoginCheckReq) GetUserInfo() *AntispamUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *DVLoginCheckReq) GetEventType() string {
	if m != nil {
		return m.EventType
	}
	return ""
}

func (m *DVLoginCheckReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *DVLoginCheckReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *DVLoginCheckReq) GetEventTime() string {
	if m != nil {
		return m.EventTime
	}
	return ""
}

func (m *DVLoginCheckReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *DVLoginCheckReq) GetOsVer() string {
	if m != nil {
		return m.OsVer
	}
	return ""
}

func (m *DVLoginCheckReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type DVCheckResp struct {
	ResponseType string  `protobuf:"bytes,1,opt,name=response_type,json=responseType" json:"response_type"`
	CustNo       string  `protobuf:"bytes,2,opt,name=cust_no,json=custNo" json:"cust_no"`
	ApplyNo      string  `protobuf:"bytes,3,opt,name=apply_no,json=applyNo" json:"apply_no"`
	Score        float64 `protobuf:"fixed64,4,opt,name=score" json:"score"`
	ErrorType    string  `protobuf:"bytes,5,opt,name=error_type,json=errorType" json:"error_type"`
	ErrorDetail  string  `protobuf:"bytes,6,opt,name=error_detail,json=errorDetail" json:"error_detail"`
}

func (m *DVCheckResp) Reset()                    { *m = DVCheckResp{} }
func (m *DVCheckResp) String() string            { return proto.CompactTextString(m) }
func (*DVCheckResp) ProtoMessage()               {}
func (*DVCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{11} }

func (m *DVCheckResp) GetResponseType() string {
	if m != nil {
		return m.ResponseType
	}
	return ""
}

func (m *DVCheckResp) GetCustNo() string {
	if m != nil {
		return m.CustNo
	}
	return ""
}

func (m *DVCheckResp) GetApplyNo() string {
	if m != nil {
		return m.ApplyNo
	}
	return ""
}

func (m *DVCheckResp) GetScore() float64 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *DVCheckResp) GetErrorType() string {
	if m != nil {
		return m.ErrorType
	}
	return ""
}

func (m *DVCheckResp) GetErrorDetail() string {
	if m != nil {
		return m.ErrorDetail
	}
	return ""
}

type AntispamMissionInfo struct {
	GetCoupon    int32  `protobuf:"varint,1,opt,name=get_coupon,json=getCoupon" json:"get_coupon"`
	EventName    string `protobuf:"bytes,2,opt,name=event_name,json=eventName" json:"event_name"`
	TaskCostTime int64  `protobuf:"varint,3,opt,name=task_cost_time,json=taskCostTime" json:"task_cost_time"`
	TaskAmount   int64  `protobuf:"varint,4,opt,name=task_amount,json=taskAmount" json:"task_amount"`
}

func (m *AntispamMissionInfo) Reset()         { *m = AntispamMissionInfo{} }
func (m *AntispamMissionInfo) String() string { return proto.CompactTextString(m) }
func (*AntispamMissionInfo) ProtoMessage()    {}
func (*AntispamMissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{12}
}

func (m *AntispamMissionInfo) GetGetCoupon() int32 {
	if m != nil {
		return m.GetCoupon
	}
	return 0
}

func (m *AntispamMissionInfo) GetEventName() string {
	if m != nil {
		return m.EventName
	}
	return ""
}

func (m *AntispamMissionInfo) GetTaskCostTime() int64 {
	if m != nil {
		return m.TaskCostTime
	}
	return 0
}

func (m *AntispamMissionInfo) GetTaskAmount() int64 {
	if m != nil {
		return m.TaskAmount
	}
	return 0
}

type AntispamExchangeInfo struct {
	Interval     int64  `protobuf:"varint,1,opt,name=interval" json:"interval"`
	Price        string `protobuf:"bytes,2,opt,name=price" json:"price"`
	Discount     string `protobuf:"bytes,3,opt,name=discount" json:"discount"`
	Account      string `protobuf:"bytes,4,opt,name=account" json:"account"`
	DiscountType string `protobuf:"bytes,5,opt,name=discount_type,json=discountType" json:"discount_type"`
}

func (m *AntispamExchangeInfo) Reset()         { *m = AntispamExchangeInfo{} }
func (m *AntispamExchangeInfo) String() string { return proto.CompactTextString(m) }
func (*AntispamExchangeInfo) ProtoMessage()    {}
func (*AntispamExchangeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{13}
}

func (m *AntispamExchangeInfo) GetInterval() int64 {
	if m != nil {
		return m.Interval
	}
	return 0
}

func (m *AntispamExchangeInfo) GetPrice() string {
	if m != nil {
		return m.Price
	}
	return ""
}

func (m *AntispamExchangeInfo) GetDiscount() string {
	if m != nil {
		return m.Discount
	}
	return ""
}

func (m *AntispamExchangeInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AntispamExchangeInfo) GetDiscountType() string {
	if m != nil {
		return m.DiscountType
	}
	return ""
}

// 数美做任务检测
type AntispamTaskEventCheckReq struct {
	AppId       string               `protobuf:"bytes,1,opt,name=app_id,json=appId" json:"app_id"`
	Uid         uint32               `protobuf:"varint,2,req,name=uid" json:"uid"`
	ClientIp    string               `protobuf:"bytes,3,req,name=client_ip,json=clientIp" json:"client_ip"`
	UserLevel   int32                `protobuf:"varint,4,opt,name=user_level,json=userLevel" json:"user_level"`
	TaskId      string               `protobuf:"bytes,5,opt,name=task_id,json=taskId" json:"task_id"`
	Deviceid    string               `protobuf:"bytes,6,opt,name=deviceid" json:"deviceid"`
	Phone       string               `protobuf:"bytes,7,opt,name=phone" json:"phone"`
	Os          int32                `protobuf:"varint,8,opt,name=os" json:"os"`
	AppVersion  int32                `protobuf:"varint,9,opt,name=app_version,json=appVersion" json:"app_version"`
	RoleId      string               `protobuf:"bytes,10,opt,name=role_id,json=roleId" json:"role_id"`
	MissionInfo *AntispamMissionInfo `protobuf:"bytes,11,opt,name=mission_info,json=missionInfo" json:"mission_info,omitempty"`
	Nickname    string               `protobuf:"bytes,12,opt,name=nickname" json:"nickname"`
}

func (m *AntispamTaskEventCheckReq) Reset()         { *m = AntispamTaskEventCheckReq{} }
func (m *AntispamTaskEventCheckReq) String() string { return proto.CompactTextString(m) }
func (*AntispamTaskEventCheckReq) ProtoMessage()    {}
func (*AntispamTaskEventCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{14}
}

func (m *AntispamTaskEventCheckReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *AntispamTaskEventCheckReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AntispamTaskEventCheckReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *AntispamTaskEventCheckReq) GetUserLevel() int32 {
	if m != nil {
		return m.UserLevel
	}
	return 0
}

func (m *AntispamTaskEventCheckReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *AntispamTaskEventCheckReq) GetDeviceid() string {
	if m != nil {
		return m.Deviceid
	}
	return ""
}

func (m *AntispamTaskEventCheckReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *AntispamTaskEventCheckReq) GetOs() int32 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *AntispamTaskEventCheckReq) GetAppVersion() int32 {
	if m != nil {
		return m.AppVersion
	}
	return 0
}

func (m *AntispamTaskEventCheckReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *AntispamTaskEventCheckReq) GetMissionInfo() *AntispamMissionInfo {
	if m != nil {
		return m.MissionInfo
	}
	return nil
}

func (m *AntispamTaskEventCheckReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

// 数美兑换检测
type AntispamExchangeEventCheckReq struct {
	AppId        string                `protobuf:"bytes,1,opt,name=app_id,json=appId" json:"app_id"`
	Uid          uint32                `protobuf:"varint,2,req,name=uid" json:"uid"`
	ClientIp     string                `protobuf:"bytes,3,req,name=client_ip,json=clientIp" json:"client_ip"`
	UserLevel    int32                 `protobuf:"varint,4,opt,name=user_level,json=userLevel" json:"user_level"`
	Product      string                `protobuf:"bytes,5,opt,name=product" json:"product"`
	Deviceid     string                `protobuf:"bytes,6,opt,name=deviceid" json:"deviceid"`
	Phone        string                `protobuf:"bytes,7,opt,name=phone" json:"phone"`
	Os           int32                 `protobuf:"varint,8,opt,name=os" json:"os"`
	AppVersion   int32                 `protobuf:"varint,9,opt,name=app_version,json=appVersion" json:"app_version"`
	RoleId       string                `protobuf:"bytes,10,opt,name=role_id,json=roleId" json:"role_id"`
	ExchangeInfo *AntispamExchangeInfo `protobuf:"bytes,11,opt,name=exchange_info,json=exchangeInfo" json:"exchange_info,omitempty"`
	Nickname     string                `protobuf:"bytes,12,opt,name=nickname" json:"nickname"`
}

func (m *AntispamExchangeEventCheckReq) Reset()         { *m = AntispamExchangeEventCheckReq{} }
func (m *AntispamExchangeEventCheckReq) String() string { return proto.CompactTextString(m) }
func (*AntispamExchangeEventCheckReq) ProtoMessage()    {}
func (*AntispamExchangeEventCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{15}
}

func (m *AntispamExchangeEventCheckReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *AntispamExchangeEventCheckReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AntispamExchangeEventCheckReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *AntispamExchangeEventCheckReq) GetUserLevel() int32 {
	if m != nil {
		return m.UserLevel
	}
	return 0
}

func (m *AntispamExchangeEventCheckReq) GetProduct() string {
	if m != nil {
		return m.Product
	}
	return ""
}

func (m *AntispamExchangeEventCheckReq) GetDeviceid() string {
	if m != nil {
		return m.Deviceid
	}
	return ""
}

func (m *AntispamExchangeEventCheckReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *AntispamExchangeEventCheckReq) GetOs() int32 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *AntispamExchangeEventCheckReq) GetAppVersion() int32 {
	if m != nil {
		return m.AppVersion
	}
	return 0
}

func (m *AntispamExchangeEventCheckReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *AntispamExchangeEventCheckReq) GetExchangeInfo() *AntispamExchangeInfo {
	if m != nil {
		return m.ExchangeInfo
	}
	return nil
}

func (m *AntispamExchangeEventCheckReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

type AntispamEventCheckResp struct {
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId" json:"request_id"`
	Code      int32  `protobuf:"varint,2,opt,name=code" json:"code"`
	Message   string `protobuf:"bytes,3,opt,name=message" json:"message"`
	RiskLevel string `protobuf:"bytes,4,opt,name=risk_level,json=riskLevel" json:"risk_level"`
	Score     int32  `protobuf:"varint,5,opt,name=score" json:"score"`
}

func (m *AntispamEventCheckResp) Reset()         { *m = AntispamEventCheckResp{} }
func (m *AntispamEventCheckResp) String() string { return proto.CompactTextString(m) }
func (*AntispamEventCheckResp) ProtoMessage()    {}
func (*AntispamEventCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{16}
}

func (m *AntispamEventCheckResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *AntispamEventCheckResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AntispamEventCheckResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *AntispamEventCheckResp) GetRiskLevel() string {
	if m != nil {
		return m.RiskLevel
	}
	return ""
}

func (m *AntispamEventCheckResp) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type UserBehaviorResultReq struct {
	Uid    uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Result uint32 `protobuf:"varint,2,req,name=result" json:"result"`
}

func (m *UserBehaviorResultReq) Reset()         { *m = UserBehaviorResultReq{} }
func (m *UserBehaviorResultReq) String() string { return proto.CompactTextString(m) }
func (*UserBehaviorResultReq) ProtoMessage()    {}
func (*UserBehaviorResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{17}
}

func (m *UserBehaviorResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBehaviorResultReq) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

type UserBehaviorResultResp struct {
}

func (m *UserBehaviorResultResp) Reset()         { *m = UserBehaviorResultResp{} }
func (m *UserBehaviorResultResp) String() string { return proto.CompactTextString(m) }
func (*UserBehaviorResultResp) ProtoMessage()    {}
func (*UserBehaviorResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{18}
}

type BatchUserBehaviorCheckReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchUserBehaviorCheckReq) Reset()         { *m = BatchUserBehaviorCheckReq{} }
func (m *BatchUserBehaviorCheckReq) String() string { return proto.CompactTextString(m) }
func (*BatchUserBehaviorCheckReq) ProtoMessage()    {}
func (*BatchUserBehaviorCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{19}
}

func (m *BatchUserBehaviorCheckReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type UserBehaviorInfo struct {
	Uid    uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Status bool   `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *UserBehaviorInfo) Reset()                    { *m = UserBehaviorInfo{} }
func (m *UserBehaviorInfo) String() string            { return proto.CompactTextString(m) }
func (*UserBehaviorInfo) ProtoMessage()               {}
func (*UserBehaviorInfo) Descriptor() ([]byte, []int) { return fileDescriptorAntispamlogic, []int{20} }

func (m *UserBehaviorInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBehaviorInfo) GetStatus() bool {
	if m != nil {
		return m.Status
	}
	return false
}

type BatchUserBehaviorCheckResp struct {
	UidBehaviorList []*UserBehaviorInfo `protobuf:"bytes,1,rep,name=uid_behavior_list,json=uidBehaviorList" json:"uid_behavior_list,omitempty"`
}

func (m *BatchUserBehaviorCheckResp) Reset()         { *m = BatchUserBehaviorCheckResp{} }
func (m *BatchUserBehaviorCheckResp) String() string { return proto.CompactTextString(m) }
func (*BatchUserBehaviorCheckResp) ProtoMessage()    {}
func (*BatchUserBehaviorCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{21}
}

func (m *BatchUserBehaviorCheckResp) GetUidBehaviorList() []*UserBehaviorInfo {
	if m != nil {
		return m.UidBehaviorList
	}
	return nil
}

// 数美检测事件请求：通用
type AntispamEventCheckReq struct {
	AppId         string                              `protobuf:"bytes,1,opt,name=app_id,json=appId" json:"app_id"`
	Uid           uint32                              `protobuf:"varint,2,req,name=uid" json:"uid"`
	Ip            string                              `protobuf:"bytes,3,req,name=ip" json:"ip"`
	EventType     AntispamEventCheckReqCheckEventType `protobuf:"varint,4,req,name=event_type,json=eventType,enum=antispamlogic.AntispamEventCheckReqCheckEventType" json:"event_type"`
	TokenSeperate int32                               `protobuf:"varint,5,opt,name=token_seperate,json=tokenSeperate" json:"token_seperate"`
	Nickname      string                              `protobuf:"bytes,6,opt,name=nickname" json:"nickname"`
	Deviceid      string                              `protobuf:"bytes,7,opt,name=deviceid" json:"deviceid"`
	UserLevel     int32                               `protobuf:"varint,8,opt,name=user_level,json=userLevel" json:"user_level"`
	Phone         string                              `protobuf:"bytes,9,opt,name=phone" json:"phone"`
	// fission事件
	FissionInviteuid uint32 `protobuf:"varint,10,opt,name=fission_inviteuid,json=fissionInviteuid" json:"fission_inviteuid"`
	// 点赞事件新增
	Os         ShumeiOSType `protobuf:"varint,11,opt,name=os,enum=antispamlogic.ShumeiOSType" json:"os"`
	AppVersion int32        `protobuf:"varint,12,opt,name=app_version,json=appVersion" json:"app_version"`
	ContentId  string       `protobuf:"bytes,13,opt,name=content_id,json=contentId" json:"content_id"`
}

func (m *AntispamEventCheckReq) Reset()         { *m = AntispamEventCheckReq{} }
func (m *AntispamEventCheckReq) String() string { return proto.CompactTextString(m) }
func (*AntispamEventCheckReq) ProtoMessage()    {}
func (*AntispamEventCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAntispamlogic, []int{22}
}

func (m *AntispamEventCheckReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *AntispamEventCheckReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AntispamEventCheckReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *AntispamEventCheckReq) GetEventType() AntispamEventCheckReqCheckEventType {
	if m != nil {
		return m.EventType
	}
	return AntispamEventCheckReq_undefined
}

func (m *AntispamEventCheckReq) GetTokenSeperate() int32 {
	if m != nil {
		return m.TokenSeperate
	}
	return 0
}

func (m *AntispamEventCheckReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AntispamEventCheckReq) GetDeviceid() string {
	if m != nil {
		return m.Deviceid
	}
	return ""
}

func (m *AntispamEventCheckReq) GetUserLevel() int32 {
	if m != nil {
		return m.UserLevel
	}
	return 0
}

func (m *AntispamEventCheckReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *AntispamEventCheckReq) GetFissionInviteuid() uint32 {
	if m != nil {
		return m.FissionInviteuid
	}
	return 0
}

func (m *AntispamEventCheckReq) GetOs() ShumeiOSType {
	if m != nil {
		return m.Os
	}
	return ShumeiOSType_none
}

func (m *AntispamEventCheckReq) GetAppVersion() int32 {
	if m != nil {
		return m.AppVersion
	}
	return 0
}

func (m *AntispamEventCheckReq) GetContentId() string {
	if m != nil {
		return m.ContentId
	}
	return ""
}

func init() {
	proto.RegisterType((*UserBehaviorCheckReq)(nil), "antispamlogic.UserBehaviorCheckReq")
	proto.RegisterType((*UserBehaviorCheckResp)(nil), "antispamlogic.UserBehaviorCheckResp")
	proto.RegisterType((*ShumeiDeviceInfo)(nil), "antispamlogic.ShumeiDeviceInfo")
	proto.RegisterType((*AntispamUserInfo)(nil), "antispamlogic.AntispamUserInfo")
	proto.RegisterType((*AntispamChanInfo)(nil), "antispamlogic.AntispamChanInfo")
	proto.RegisterType((*AntispamGroupInfo)(nil), "antispamlogic.AntispamGroupInfo")
	proto.RegisterType((*SubTextInfo)(nil), "antispamlogic.SubTextInfo")
	proto.RegisterType((*TextCheckReq)(nil), "antispamlogic.TextCheckReq")
	proto.RegisterType((*TextCheckResp)(nil), "antispamlogic.TextCheckResp")
	proto.RegisterType((*DVCommCheckReq)(nil), "antispamlogic.DVCommCheckReq")
	proto.RegisterType((*DVLoginCheckReq)(nil), "antispamlogic.DVLoginCheckReq")
	proto.RegisterType((*DVCheckResp)(nil), "antispamlogic.DVCheckResp")
	proto.RegisterType((*AntispamMissionInfo)(nil), "antispamlogic.antispamMissionInfo")
	proto.RegisterType((*AntispamExchangeInfo)(nil), "antispamlogic.antispamExchangeInfo")
	proto.RegisterType((*AntispamTaskEventCheckReq)(nil), "antispamlogic.AntispamTaskEventCheckReq")
	proto.RegisterType((*AntispamExchangeEventCheckReq)(nil), "antispamlogic.AntispamExchangeEventCheckReq")
	proto.RegisterType((*AntispamEventCheckResp)(nil), "antispamlogic.AntispamEventCheckResp")
	proto.RegisterType((*UserBehaviorResultReq)(nil), "antispamlogic.UserBehaviorResultReq")
	proto.RegisterType((*UserBehaviorResultResp)(nil), "antispamlogic.UserBehaviorResultResp")
	proto.RegisterType((*BatchUserBehaviorCheckReq)(nil), "antispamlogic.BatchUserBehaviorCheckReq")
	proto.RegisterType((*UserBehaviorInfo)(nil), "antispamlogic.UserBehaviorInfo")
	proto.RegisterType((*BatchUserBehaviorCheckResp)(nil), "antispamlogic.BatchUserBehaviorCheckResp")
	proto.RegisterType((*AntispamEventCheckReq)(nil), "antispamlogic.AntispamEventCheckReq")
	proto.RegisterEnum("antispamlogic.RiskType", RiskType_name, RiskType_value)
	proto.RegisterEnum("antispamlogic.ShumeiOSType", ShumeiOSType_name, ShumeiOSType_value)
	proto.RegisterEnum("antispamlogic.UserBehaviorCheckReq_BehaviorType", UserBehaviorCheckReq_BehaviorType_name, UserBehaviorCheckReq_BehaviorType_value)
	proto.RegisterEnum("antispamlogic.UserBehaviorCheckResp_ResultType", UserBehaviorCheckResp_ResultType_name, UserBehaviorCheckResp_ResultType_value)
	proto.RegisterEnum("antispamlogic.TextCheckResp_RESULT", TextCheckResp_RESULT_name, TextCheckResp_RESULT_value)
	proto.RegisterEnum("antispamlogic.UserBehaviorResultReq_BehaviorResult", UserBehaviorResultReq_BehaviorResult_name, UserBehaviorResultReq_BehaviorResult_value)
	proto.RegisterEnum("antispamlogic.AntispamEventCheckReqCheckEventType", AntispamEventCheckReqCheckEventType_name, AntispamEventCheckReqCheckEventType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for AntispamLogic service

type AntispamLogicClient interface {
	UserBehaviorCheck(ctx context.Context, in *UserBehaviorCheckReq, opts ...grpc.CallOption) (*UserBehaviorCheckResp, error)
	TextCheck(ctx context.Context, in *TextCheckReq, opts ...grpc.CallOption) (*TextCheckResp, error)
	DVLoginCheck(ctx context.Context, in *DVLoginCheckReq, opts ...grpc.CallOption) (*DVCheckResp, error)
	DVCommCheck(ctx context.Context, in *DVCommCheckReq, opts ...grpc.CallOption) (*DVCheckResp, error)
	TaskEventCheck(ctx context.Context, in *AntispamTaskEventCheckReq, opts ...grpc.CallOption) (*AntispamEventCheckResp, error)
	ExchangeEventCheck(ctx context.Context, in *AntispamExchangeEventCheckReq, opts ...grpc.CallOption) (*AntispamEventCheckResp, error)
	UpdateUserBehaviorResult(ctx context.Context, in *UserBehaviorResultReq, opts ...grpc.CallOption) (*UserBehaviorResultResp, error)
	BathUserBehaviorCheck(ctx context.Context, in *BatchUserBehaviorCheckReq, opts ...grpc.CallOption) (*BatchUserBehaviorCheckResp, error)
	SMCommEventCheck(ctx context.Context, in *AntispamEventCheckReq, opts ...grpc.CallOption) (*AntispamEventCheckResp, error)
}

type antispamLogicClient struct {
	cc *grpc.ClientConn
}

func NewAntispamLogicClient(cc *grpc.ClientConn) AntispamLogicClient {
	return &antispamLogicClient{cc}
}

func (c *antispamLogicClient) UserBehaviorCheck(ctx context.Context, in *UserBehaviorCheckReq, opts ...grpc.CallOption) (*UserBehaviorCheckResp, error) {
	out := new(UserBehaviorCheckResp)
	err := grpc.Invoke(ctx, "/antispamlogic.AntispamLogic/UserBehaviorCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamLogicClient) TextCheck(ctx context.Context, in *TextCheckReq, opts ...grpc.CallOption) (*TextCheckResp, error) {
	out := new(TextCheckResp)
	err := grpc.Invoke(ctx, "/antispamlogic.AntispamLogic/TextCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamLogicClient) DVLoginCheck(ctx context.Context, in *DVLoginCheckReq, opts ...grpc.CallOption) (*DVCheckResp, error) {
	out := new(DVCheckResp)
	err := grpc.Invoke(ctx, "/antispamlogic.AntispamLogic/DVLoginCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamLogicClient) DVCommCheck(ctx context.Context, in *DVCommCheckReq, opts ...grpc.CallOption) (*DVCheckResp, error) {
	out := new(DVCheckResp)
	err := grpc.Invoke(ctx, "/antispamlogic.AntispamLogic/DVCommCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamLogicClient) TaskEventCheck(ctx context.Context, in *AntispamTaskEventCheckReq, opts ...grpc.CallOption) (*AntispamEventCheckResp, error) {
	out := new(AntispamEventCheckResp)
	err := grpc.Invoke(ctx, "/antispamlogic.AntispamLogic/TaskEventCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamLogicClient) ExchangeEventCheck(ctx context.Context, in *AntispamExchangeEventCheckReq, opts ...grpc.CallOption) (*AntispamEventCheckResp, error) {
	out := new(AntispamEventCheckResp)
	err := grpc.Invoke(ctx, "/antispamlogic.AntispamLogic/ExchangeEventCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamLogicClient) UpdateUserBehaviorResult(ctx context.Context, in *UserBehaviorResultReq, opts ...grpc.CallOption) (*UserBehaviorResultResp, error) {
	out := new(UserBehaviorResultResp)
	err := grpc.Invoke(ctx, "/antispamlogic.AntispamLogic/UpdateUserBehaviorResult", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamLogicClient) BathUserBehaviorCheck(ctx context.Context, in *BatchUserBehaviorCheckReq, opts ...grpc.CallOption) (*BatchUserBehaviorCheckResp, error) {
	out := new(BatchUserBehaviorCheckResp)
	err := grpc.Invoke(ctx, "/antispamlogic.AntispamLogic/BathUserBehaviorCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamLogicClient) SMCommEventCheck(ctx context.Context, in *AntispamEventCheckReq, opts ...grpc.CallOption) (*AntispamEventCheckResp, error) {
	out := new(AntispamEventCheckResp)
	err := grpc.Invoke(ctx, "/antispamlogic.AntispamLogic/SMCommEventCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for AntispamLogic service

type AntispamLogicServer interface {
	UserBehaviorCheck(context.Context, *UserBehaviorCheckReq) (*UserBehaviorCheckResp, error)
	TextCheck(context.Context, *TextCheckReq) (*TextCheckResp, error)
	DVLoginCheck(context.Context, *DVLoginCheckReq) (*DVCheckResp, error)
	DVCommCheck(context.Context, *DVCommCheckReq) (*DVCheckResp, error)
	TaskEventCheck(context.Context, *AntispamTaskEventCheckReq) (*AntispamEventCheckResp, error)
	ExchangeEventCheck(context.Context, *AntispamExchangeEventCheckReq) (*AntispamEventCheckResp, error)
	UpdateUserBehaviorResult(context.Context, *UserBehaviorResultReq) (*UserBehaviorResultResp, error)
	BathUserBehaviorCheck(context.Context, *BatchUserBehaviorCheckReq) (*BatchUserBehaviorCheckResp, error)
	SMCommEventCheck(context.Context, *AntispamEventCheckReq) (*AntispamEventCheckResp, error)
}

func RegisterAntispamLogicServer(s *grpc.Server, srv AntispamLogicServer) {
	s.RegisterService(&_AntispamLogic_serviceDesc, srv)
}

func _AntispamLogic_UserBehaviorCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserBehaviorCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamLogicServer).UserBehaviorCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/antispamlogic.AntispamLogic/UserBehaviorCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamLogicServer).UserBehaviorCheck(ctx, req.(*UserBehaviorCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AntispamLogic_TextCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TextCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamLogicServer).TextCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/antispamlogic.AntispamLogic/TextCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamLogicServer).TextCheck(ctx, req.(*TextCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AntispamLogic_DVLoginCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DVLoginCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamLogicServer).DVLoginCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/antispamlogic.AntispamLogic/DVLoginCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamLogicServer).DVLoginCheck(ctx, req.(*DVLoginCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AntispamLogic_DVCommCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DVCommCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamLogicServer).DVCommCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/antispamlogic.AntispamLogic/DVCommCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamLogicServer).DVCommCheck(ctx, req.(*DVCommCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AntispamLogic_TaskEventCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AntispamTaskEventCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamLogicServer).TaskEventCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/antispamlogic.AntispamLogic/TaskEventCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamLogicServer).TaskEventCheck(ctx, req.(*AntispamTaskEventCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AntispamLogic_ExchangeEventCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AntispamExchangeEventCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamLogicServer).ExchangeEventCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/antispamlogic.AntispamLogic/ExchangeEventCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamLogicServer).ExchangeEventCheck(ctx, req.(*AntispamExchangeEventCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AntispamLogic_UpdateUserBehaviorResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserBehaviorResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamLogicServer).UpdateUserBehaviorResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/antispamlogic.AntispamLogic/UpdateUserBehaviorResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamLogicServer).UpdateUserBehaviorResult(ctx, req.(*UserBehaviorResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AntispamLogic_BathUserBehaviorCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUserBehaviorCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamLogicServer).BathUserBehaviorCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/antispamlogic.AntispamLogic/BathUserBehaviorCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamLogicServer).BathUserBehaviorCheck(ctx, req.(*BatchUserBehaviorCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AntispamLogic_SMCommEventCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AntispamEventCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamLogicServer).SMCommEventCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/antispamlogic.AntispamLogic/SMCommEventCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamLogicServer).SMCommEventCheck(ctx, req.(*AntispamEventCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AntispamLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "antispamlogic.AntispamLogic",
	HandlerType: (*AntispamLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UserBehaviorCheck",
			Handler:    _AntispamLogic_UserBehaviorCheck_Handler,
		},
		{
			MethodName: "TextCheck",
			Handler:    _AntispamLogic_TextCheck_Handler,
		},
		{
			MethodName: "DVLoginCheck",
			Handler:    _AntispamLogic_DVLoginCheck_Handler,
		},
		{
			MethodName: "DVCommCheck",
			Handler:    _AntispamLogic_DVCommCheck_Handler,
		},
		{
			MethodName: "TaskEventCheck",
			Handler:    _AntispamLogic_TaskEventCheck_Handler,
		},
		{
			MethodName: "ExchangeEventCheck",
			Handler:    _AntispamLogic_ExchangeEventCheck_Handler,
		},
		{
			MethodName: "UpdateUserBehaviorResult",
			Handler:    _AntispamLogic_UpdateUserBehaviorResult_Handler,
		},
		{
			MethodName: "BathUserBehaviorCheck",
			Handler:    _AntispamLogic_BathUserBehaviorCheck_Handler,
		},
		{
			MethodName: "SMCommEventCheck",
			Handler:    _AntispamLogic_SMCommEventCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/antispamlogic/antispamlogic.proto",
}

func (m *UserBehaviorCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserBehaviorCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Uid))
	if len(m.BehaviorTypeList) > 0 {
		for _, num := range m.BehaviorTypeList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintAntispamlogic(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.ToUid))
	return i, nil
}

func (m *UserBehaviorCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserBehaviorCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.HitUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.HitBehaviorType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.ResultType))
	return i, nil
}

func (m *ShumeiDeviceInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShumeiDeviceInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.SmDeviceId)))
	i += copy(dAtA[i:], m.SmDeviceId)
	return i, nil
}

func (m *AntispamUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AntispamUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Username)))
	i += copy(dAtA[i:], m.Username)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Alias)))
	i += copy(dAtA[i:], m.Alias)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Tokenid)))
	i += copy(dAtA[i:], m.Tokenid)
	dAtA[i] = 0x30
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Gender))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.PhoneNumber)))
	i += copy(dAtA[i:], m.PhoneNumber)
	return i, nil
}

func (m *AntispamChanInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AntispamChanInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.DisplayId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	return i, nil
}

func (m *AntispamGroupInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AntispamGroupInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.GroupId))
	return i, nil
}

func (m *SubTextInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SubTextInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	if m.SubTextCheckContext != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.SubTextCheckContext)))
		i += copy(dAtA[i:], m.SubTextCheckContext)
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.DataType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.DataSubScene)))
	i += copy(dAtA[i:], m.DataSubScene)
	return i, nil
}

func (m *TextCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TextCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	if m.UserInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(m.UserInfo.Size()))
		n1, err := m.UserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.ToUserInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(m.ToUserInfo.Size()))
		n2, err := m.ToUserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.DeviceIdBase64)))
	i += copy(dAtA[i:], m.DeviceIdBase64)
	dAtA[i] = 0x30
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.DataType))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.DataSubScene)))
	i += copy(dAtA[i:], m.DataSubScene)
	if m.SmDeviceInfo != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(m.SmDeviceInfo.Size()))
		n3, err := m.SmDeviceInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.ChannelInfo != nil {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(m.ChannelInfo.Size()))
		n4, err := m.ChannelInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.GroupInfo != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(m.GroupInfo.Size()))
		n5, err := m.GroupInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.TextCheckContext != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.TextCheckContext)))
		i += copy(dAtA[i:], m.TextCheckContext)
	}
	dAtA[i] = 0x60
	i++
	if m.AsyncStatus {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x68
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.ProduceTime))
	if len(m.SubTextInfoList) > 0 {
		for _, msg := range m.SubTextInfoList {
			dAtA[i] = 0x72
			i++
			i = encodeVarintAntispamlogic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *TextCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TextCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.LabelInfo)))
	i += copy(dAtA[i:], m.LabelInfo)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.RiskType))
	return i, nil
}

func (m *DVCommCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DVCommCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(m.UserInfo.Size()))
		n6, err := m.UserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.ToUserInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(m.ToUserInfo.Size()))
		n7, err := m.ToUserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.EventType)))
	i += copy(dAtA[i:], m.EventType)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.EventTime)))
	i += copy(dAtA[i:], m.EventTime)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.ExtInfo)))
	i += copy(dAtA[i:], m.ExtInfo)
	return i, nil
}

func (m *DVLoginCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DVLoginCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(m.UserInfo.Size()))
		n8, err := m.UserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.EventType)))
	i += copy(dAtA[i:], m.EventType)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.ClientVersion))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.EventTime)))
	i += copy(dAtA[i:], m.EventTime)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.OsVer)))
	i += copy(dAtA[i:], m.OsVer)
	dAtA[i] = 0x42
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	return i, nil
}

func (m *DVCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DVCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.ResponseType)))
	i += copy(dAtA[i:], m.ResponseType)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.CustNo)))
	i += copy(dAtA[i:], m.CustNo)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.ApplyNo)))
	i += copy(dAtA[i:], m.ApplyNo)
	dAtA[i] = 0x21
	i++
	i = encodeFixed64Antispamlogic(dAtA, i, uint64(math3.Float64bits(float64(m.Score))))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.ErrorType)))
	i += copy(dAtA[i:], m.ErrorType)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.ErrorDetail)))
	i += copy(dAtA[i:], m.ErrorDetail)
	return i, nil
}

func (m *AntispamMissionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AntispamMissionInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.GetCoupon))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.EventName)))
	i += copy(dAtA[i:], m.EventName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.TaskCostTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.TaskAmount))
	return i, nil
}

func (m *AntispamExchangeInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AntispamExchangeInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Interval))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Price)))
	i += copy(dAtA[i:], m.Price)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Discount)))
	i += copy(dAtA[i:], m.Discount)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.DiscountType)))
	i += copy(dAtA[i:], m.DiscountType)
	return i, nil
}

func (m *AntispamTaskEventCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AntispamTaskEventCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.UserLevel))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.TaskId)))
	i += copy(dAtA[i:], m.TaskId)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Deviceid)))
	i += copy(dAtA[i:], m.Deviceid)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x40
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Os))
	dAtA[i] = 0x48
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.AppVersion))
	dAtA[i] = 0x52
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.RoleId)))
	i += copy(dAtA[i:], m.RoleId)
	if m.MissionInfo != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(m.MissionInfo.Size()))
		n9, err := m.MissionInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x62
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	return i, nil
}

func (m *AntispamExchangeEventCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AntispamExchangeEventCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.UserLevel))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Product)))
	i += copy(dAtA[i:], m.Product)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Deviceid)))
	i += copy(dAtA[i:], m.Deviceid)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x40
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Os))
	dAtA[i] = 0x48
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.AppVersion))
	dAtA[i] = 0x52
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.RoleId)))
	i += copy(dAtA[i:], m.RoleId)
	if m.ExchangeInfo != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintAntispamlogic(dAtA, i, uint64(m.ExchangeInfo.Size()))
		n10, err := m.ExchangeInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x62
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	return i, nil
}

func (m *AntispamEventCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AntispamEventCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Code))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Message)))
	i += copy(dAtA[i:], m.Message)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.RiskLevel)))
	i += copy(dAtA[i:], m.RiskLevel)
	dAtA[i] = 0x28
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Score))
	return i, nil
}

func (m *UserBehaviorResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserBehaviorResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Result))
	return i, nil
}

func (m *UserBehaviorResultResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserBehaviorResultResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatchUserBehaviorCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchUserBehaviorCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintAntispamlogic(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *UserBehaviorInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserBehaviorInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.Status {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *BatchUserBehaviorCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchUserBehaviorCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidBehaviorList) > 0 {
		for _, msg := range m.UidBehaviorList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAntispamlogic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AntispamEventCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AntispamEventCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.EventType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.TokenSeperate))
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Deviceid)))
	i += copy(dAtA[i:], m.Deviceid)
	dAtA[i] = 0x40
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.UserLevel))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x50
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.FissionInviteuid))
	dAtA[i] = 0x58
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.Os))
	dAtA[i] = 0x60
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(m.AppVersion))
	dAtA[i] = 0x6a
	i++
	i = encodeVarintAntispamlogic(dAtA, i, uint64(len(m.ContentId)))
	i += copy(dAtA[i:], m.ContentId)
	return i, nil
}

func encodeFixed64Antispamlogic(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Antispamlogic(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAntispamlogic(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UserBehaviorCheckReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispamlogic(uint64(m.Uid))
	if len(m.BehaviorTypeList) > 0 {
		for _, e := range m.BehaviorTypeList {
			n += 1 + sovAntispamlogic(uint64(e))
		}
	}
	n += 1 + sovAntispamlogic(uint64(m.ToUid))
	return n
}

func (m *UserBehaviorCheckResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispamlogic(uint64(m.HitUid))
	n += 1 + sovAntispamlogic(uint64(m.HitBehaviorType))
	n += 1 + sovAntispamlogic(uint64(m.ResultType))
	return n
}

func (m *ShumeiDeviceInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.SmDeviceId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func (m *AntispamUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispamlogic(uint64(m.Uid))
	l = len(m.Username)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Alias)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Tokenid)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.Gender))
	l = len(m.PhoneNumber)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func (m *AntispamChanInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispamlogic(uint64(m.ChannelId))
	n += 1 + sovAntispamlogic(uint64(m.DisplayId))
	l = len(m.Name)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func (m *AntispamGroupInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispamlogic(uint64(m.GroupId))
	return n
}

func (m *SubTextInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Text)
	n += 1 + l + sovAntispamlogic(uint64(l))
	if m.SubTextCheckContext != nil {
		l = len(m.SubTextCheckContext)
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	n += 1 + sovAntispamlogic(uint64(m.DataType))
	l = len(m.DataSubScene)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func (m *TextCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Text)
	n += 1 + l + sovAntispamlogic(uint64(l))
	if m.UserInfo != nil {
		l = m.UserInfo.Size()
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	if m.ToUserInfo != nil {
		l = m.ToUserInfo.Size()
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	l = len(m.Ip)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.DeviceIdBase64)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.DataType))
	l = len(m.DataSubScene)
	n += 1 + l + sovAntispamlogic(uint64(l))
	if m.SmDeviceInfo != nil {
		l = m.SmDeviceInfo.Size()
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	if m.ChannelInfo != nil {
		l = m.ChannelInfo.Size()
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	if m.GroupInfo != nil {
		l = m.GroupInfo.Size()
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	if m.TextCheckContext != nil {
		l = len(m.TextCheckContext)
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	n += 2
	n += 1 + sovAntispamlogic(uint64(m.ProduceTime))
	if len(m.SubTextInfoList) > 0 {
		for _, e := range m.SubTextInfoList {
			l = e.Size()
			n += 1 + l + sovAntispamlogic(uint64(l))
		}
	}
	return n
}

func (m *TextCheckResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispamlogic(uint64(m.Result))
	l = len(m.LabelInfo)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.RequestId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.RiskType))
	return n
}

func (m *DVCommCheckReq) Size() (n int) {
	var l int
	_ = l
	if m.UserInfo != nil {
		l = m.UserInfo.Size()
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	if m.ToUserInfo != nil {
		l = m.ToUserInfo.Size()
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	l = len(m.EventType)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.EventTime)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.ExtInfo)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func (m *DVLoginCheckReq) Size() (n int) {
	var l int
	_ = l
	if m.UserInfo != nil {
		l = m.UserInfo.Size()
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	l = len(m.EventType)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.ClientVersion))
	l = len(m.EventTime)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.DeviceId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.OsVer)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Token)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func (m *DVCheckResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.ResponseType)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.CustNo)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.ApplyNo)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 9
	l = len(m.ErrorType)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.ErrorDetail)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func (m *AntispamMissionInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispamlogic(uint64(m.GetCoupon))
	l = len(m.EventName)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.TaskCostTime))
	n += 1 + sovAntispamlogic(uint64(m.TaskAmount))
	return n
}

func (m *AntispamExchangeInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispamlogic(uint64(m.Interval))
	l = len(m.Price)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Discount)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.DiscountType)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func (m *AntispamTaskEventCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.Uid))
	l = len(m.ClientIp)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.UserLevel))
	l = len(m.TaskId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Deviceid)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.Os))
	n += 1 + sovAntispamlogic(uint64(m.AppVersion))
	l = len(m.RoleId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	if m.MissionInfo != nil {
		l = m.MissionInfo.Size()
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	l = len(m.Nickname)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func (m *AntispamExchangeEventCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.Uid))
	l = len(m.ClientIp)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.UserLevel))
	l = len(m.Product)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Deviceid)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.Os))
	n += 1 + sovAntispamlogic(uint64(m.AppVersion))
	l = len(m.RoleId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	if m.ExchangeInfo != nil {
		l = m.ExchangeInfo.Size()
		n += 1 + l + sovAntispamlogic(uint64(l))
	}
	l = len(m.Nickname)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func (m *AntispamEventCheckResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.RequestId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.Code))
	l = len(m.Message)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.RiskLevel)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.Score))
	return n
}

func (m *UserBehaviorResultReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispamlogic(uint64(m.Uid))
	n += 1 + sovAntispamlogic(uint64(m.Result))
	return n
}

func (m *UserBehaviorResultResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatchUserBehaviorCheckReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovAntispamlogic(uint64(e))
		}
	}
	return n
}

func (m *UserBehaviorInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispamlogic(uint64(m.Uid))
	n += 2
	return n
}

func (m *BatchUserBehaviorCheckResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidBehaviorList) > 0 {
		for _, e := range m.UidBehaviorList {
			l = e.Size()
			n += 1 + l + sovAntispamlogic(uint64(l))
		}
	}
	return n
}

func (m *AntispamEventCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.Uid))
	l = len(m.Ip)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.EventType))
	n += 1 + sovAntispamlogic(uint64(m.TokenSeperate))
	l = len(m.Nickname)
	n += 1 + l + sovAntispamlogic(uint64(l))
	l = len(m.Deviceid)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.UserLevel))
	l = len(m.Phone)
	n += 1 + l + sovAntispamlogic(uint64(l))
	n += 1 + sovAntispamlogic(uint64(m.FissionInviteuid))
	n += 1 + sovAntispamlogic(uint64(m.Os))
	n += 1 + sovAntispamlogic(uint64(m.AppVersion))
	l = len(m.ContentId)
	n += 1 + l + sovAntispamlogic(uint64(l))
	return n
}

func sovAntispamlogic(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAntispamlogic(x uint64) (n int) {
	return sovAntispamlogic(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *UserBehaviorCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserBehaviorCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserBehaviorCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAntispamlogic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.BehaviorTypeList = append(m.BehaviorTypeList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAntispamlogic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAntispamlogic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAntispamlogic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.BehaviorTypeList = append(m.BehaviorTypeList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field BehaviorTypeList", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUid", wireType)
			}
			m.ToUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserBehaviorCheckResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserBehaviorCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserBehaviorCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HitUid", wireType)
			}
			m.HitUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HitUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HitBehaviorType", wireType)
			}
			m.HitBehaviorType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HitBehaviorType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultType", wireType)
			}
			m.ResultType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResultType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShumeiDeviceInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ShumeiDeviceInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ShumeiDeviceInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SmDeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SmDeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AntispamUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AntispamUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AntispamUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Username", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Username = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Alias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Alias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tokenid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Tokenid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Gender", wireType)
			}
			m.Gender = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Gender |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhoneNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AntispamChanInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AntispamChanInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AntispamChanInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisplayId", wireType)
			}
			m.DisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AntispamGroupInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AntispamGroupInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AntispamGroupInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SubTextInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SubTextInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SubTextInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubTextCheckContext", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubTextCheckContext = append(m.SubTextCheckContext[:0], dAtA[iNdEx:postIndex]...)
			if m.SubTextCheckContext == nil {
				m.SubTextCheckContext = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataType", wireType)
			}
			m.DataType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DataType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataSubScene", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DataSubScene = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TextCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TextCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TextCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserInfo == nil {
				m.UserInfo = &AntispamUserInfo{}
			}
			if err := m.UserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ToUserInfo == nil {
				m.ToUserInfo = &AntispamUserInfo{}
			}
			if err := m.ToUserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceIdBase64", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceIdBase64 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataType", wireType)
			}
			m.DataType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DataType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataSubScene", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DataSubScene = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SmDeviceInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SmDeviceInfo == nil {
				m.SmDeviceInfo = &ShumeiDeviceInfo{}
			}
			if err := m.SmDeviceInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ChannelInfo == nil {
				m.ChannelInfo = &AntispamChanInfo{}
			}
			if err := m.ChannelInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GroupInfo == nil {
				m.GroupInfo = &AntispamGroupInfo{}
			}
			if err := m.GroupInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TextCheckContext", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TextCheckContext = append(m.TextCheckContext[:0], dAtA[iNdEx:postIndex]...)
			if m.TextCheckContext == nil {
				m.TextCheckContext = []byte{}
			}
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AsyncStatus", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AsyncStatus = bool(v != 0)
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProduceTime", wireType)
			}
			m.ProduceTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProduceTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubTextInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubTextInfoList = append(m.SubTextInfoList, &SubTextInfo{})
			if err := m.SubTextInfoList[len(m.SubTextInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("text")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TextCheckResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TextCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TextCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LabelInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LabelInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RiskType", wireType)
			}
			m.RiskType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RiskType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DVCommCheckReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DVCommCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DVCommCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserInfo == nil {
				m.UserInfo = &AntispamUserInfo{}
			}
			if err := m.UserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ToUserInfo == nil {
				m.ToUserInfo = &AntispamUserInfo{}
			}
			if err := m.ToUserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EventType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EventTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DVLoginCheckReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DVLoginCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DVLoginCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserInfo == nil {
				m.UserInfo = &AntispamUserInfo{}
			}
			if err := m.UserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EventType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientVersion", wireType)
			}
			m.ClientVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EventTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OsVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OsVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DVCheckResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DVCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DVCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResponseType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResponseType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CustNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CustNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ApplyNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Score = float64(math4.Float64frombits(v))
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrorType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ErrorType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrorDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ErrorDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AntispamMissionInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: antispamMissionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: antispamMissionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetCoupon", wireType)
			}
			m.GetCoupon = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GetCoupon |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EventName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TaskCostTime", wireType)
			}
			m.TaskCostTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TaskCostTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TaskAmount", wireType)
			}
			m.TaskAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TaskAmount |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AntispamExchangeInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: antispamExchangeInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: antispamExchangeInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Interval", wireType)
			}
			m.Interval = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Interval |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Price = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Discount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Discount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DiscountType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DiscountType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AntispamTaskEventCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AntispamTaskEventCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AntispamTaskEventCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserLevel", wireType)
			}
			m.UserLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserLevel |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TaskId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Deviceid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Deviceid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Os", wireType)
			}
			m.Os = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Os |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			m.AppVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppVersion |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoleId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RoleId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MissionInfo == nil {
				m.MissionInfo = &AntispamMissionInfo{}
			}
			if err := m.MissionInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("client_ip")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AntispamExchangeEventCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AntispamExchangeEventCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AntispamExchangeEventCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserLevel", wireType)
			}
			m.UserLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserLevel |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Product", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Product = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Deviceid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Deviceid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Os", wireType)
			}
			m.Os = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Os |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			m.AppVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppVersion |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoleId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RoleId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ExchangeInfo == nil {
				m.ExchangeInfo = &AntispamExchangeInfo{}
			}
			if err := m.ExchangeInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("client_ip")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AntispamEventCheckResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AntispamEventCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AntispamEventCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RiskLevel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RiskLevel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserBehaviorResultReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserBehaviorResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserBehaviorResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserBehaviorResultResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserBehaviorResultResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserBehaviorResultResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchUserBehaviorCheckReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchUserBehaviorCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchUserBehaviorCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAntispamlogic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAntispamlogic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAntispamlogic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAntispamlogic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserBehaviorInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserBehaviorInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserBehaviorInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Status = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchUserBehaviorCheckResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchUserBehaviorCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchUserBehaviorCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidBehaviorList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UidBehaviorList = append(m.UidBehaviorList, &UserBehaviorInfo{})
			if err := m.UidBehaviorList[len(m.UidBehaviorList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AntispamEventCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AntispamEventCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AntispamEventCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			m.EventType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EventType |= (AntispamEventCheckReqCheckEventType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TokenSeperate", wireType)
			}
			m.TokenSeperate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TokenSeperate |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Deviceid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Deviceid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserLevel", wireType)
			}
			m.UserLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserLevel |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FissionInviteuid", wireType)
			}
			m.FissionInviteuid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FissionInviteuid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Os", wireType)
			}
			m.Os = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Os |= (ShumeiOSType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			m.AppVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppVersion |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContentId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ContentId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispamlogic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispamlogic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ip")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("event_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAntispamlogic(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAntispamlogic
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAntispamlogic
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAntispamlogic
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAntispamlogic
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAntispamlogic(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAntispamlogic = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAntispamlogic   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/antispamlogic/antispamlogic.proto", fileDescriptorAntispamlogic) }

var fileDescriptorAntispamlogic = []byte{
	// 2476 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x59, 0xcb, 0x6f, 0xdc, 0xc6,
	0x19, 0x17, 0xf7, 0xbd, 0xdf, 0x72, 0x57, 0xd4, 0xc4, 0x36, 0xd6, 0x8a, 0x2d, 0x6f, 0xe8, 0xb8,
	0x51, 0x9c, 0xc4, 0x4a, 0x94, 0x34, 0x40, 0x03, 0x23, 0x85, 0x24, 0xab, 0xf1, 0x22, 0x8a, 0x1c,
	0xec, 0x4a, 0x46, 0x1f, 0x07, 0x82, 0x4b, 0x8e, 0x56, 0x53, 0x71, 0x49, 0x86, 0x33, 0x94, 0xa3,
	0xa2, 0x87, 0x1e, 0x8b, 0x9e, 0x8a, 0xf6, 0x0f, 0xe8, 0xc5, 0xff, 0x40, 0x7b, 0x0a, 0x7a, 0x6b,
	0x7b, 0x68, 0xd0, 0x53, 0xaf, 0xbd, 0x14, 0x85, 0x9b, 0x83, 0xaf, 0xbd, 0x16, 0x45, 0x51, 0x7c,
	0x43, 0x0e, 0x97, 0xdc, 0x87, 0xe3, 0xb4, 0x3d, 0xe4, 0x60, 0xc0, 0xfb, 0xfb, 0x66, 0xbe, 0xf7,
	0x6b, 0x28, 0xb8, 0xc5, 0x23, 0x67, 0xcb, 0xf6, 0x05, 0xe3, 0xa1, 0x3d, 0xf1, 0x82, 0x31, 0x9b,
	0xf9, 0x75, 0x27, 0x8c, 0x02, 0x11, 0x90, 0x76, 0x01, 0x5c, 0x7f, 0xd9, 0x09, 0x26, 0x93, 0xc0,
	0xdf, 0x12, 0xde, 0x79, 0xc8, 0x9c, 0x33, 0x8f, 0x6e, 0xf1, 0xb3, 0x51, 0xcc, 0x3c, 0xc1, 0x7c,
	0x71, 0x11, 0xd2, 0xe4, 0x92, 0xf9, 0xcf, 0x32, 0x5c, 0x3a, 0xe6, 0x34, 0xda, 0xa5, 0xa7, 0xf6,
	0x39, 0x0b, 0xa2, 0xbd, 0x53, 0xea, 0x9c, 0x0d, 0xe8, 0x27, 0xe4, 0x0a, 0x94, 0x63, 0xe6, 0x76,
	0xb5, 0x5e, 0x69, 0xb3, 0xbd, 0x5b, 0xf9, 0xfc, 0xaf, 0x37, 0x56, 0x06, 0x08, 0x90, 0xd7, 0x81,
	0x8c, 0xd2, 0xb3, 0x16, 0xf2, 0xb1, 0x3c, 0xc6, 0x45, 0xb7, 0xd4, 0x2b, 0x6f, 0xb6, 0x07, 0x86,
	0xa2, 0x1c, 0x5d, 0x84, 0xf4, 0x80, 0x71, 0x41, 0x5e, 0x84, 0x9a, 0x08, 0x2c, 0x64, 0x54, 0xee,
	0x69, 0x19, 0xa3, 0xaa, 0x08, 0x8e, 0x99, 0x6b, 0xfe, 0xbb, 0x04, 0xfa, 0x6e, 0xee, 0x06, 0x31,
	0x40, 0x3f, 0x89, 0x18, 0xf5, 0x5d, 0x6b, 0x62, 0x0b, 0xe7, 0xd4, 0xd0, 0x48, 0x07, 0x20, 0x1e,
	0x3b, 0xd6, 0x49, 0xe0, 0x79, 0xc1, 0x23, 0xa3, 0x44, 0x56, 0xa1, 0xc5, 0x26, 0x16, 0x17, 0x91,
	0xed, 0x8f, 0x69, 0x64, 0x94, 0x15, 0x20, 0xef, 0xf0, 0xb1, 0x51, 0x41, 0x1e, 0x0a, 0x10, 0xf4,
	0x53, 0x61, 0x54, 0xc9, 0x65, 0x58, 0x43, 0x1e, 0x23, 0x4a, 0xfd, 0x94, 0x11, 0x75, 0x8d, 0x1a,
	0xd1, 0xa1, 0x81, 0x70, 0x18, 0x70, 0x61, 0xd4, 0xc9, 0x25, 0x30, 0xf0, 0x17, 0xfa, 0x8c, 0xfa,
	0x22, 0xb9, 0xda, 0x50, 0x57, 0x15, 0xca, 0x26, 0xf6, 0x98, 0x1a, 0x4d, 0x94, 0x81, 0xb0, 0x2d,
	0x04, 0x13, 0xb1, 0x4b, 0x0d, 0x40, 0x3d, 0xc7, 0xf6, 0x84, 0xa6, 0x7a, 0xb7, 0x90, 0xf9, 0x69,
	0xe0, 0xb9, 0xd6, 0x84, 0x39, 0x86, 0x8e, 0xe7, 0xc7, 0x31, 0xf3, 0x5c, 0xcb, 0x89, 0xa8, 0x2d,
	0xa8, 0xd1, 0x96, 0xe7, 0x25, 0xf2, 0xc3, 0x80, 0xf9, 0x46, 0x87, 0xb4, 0xa1, 0xe9, 0x53, 0x3b,
	0xb2, 0x62, 0x4e, 0x23, 0x63, 0x95, 0xac, 0x41, 0x3b, 0x0a, 0x82, 0x89, 0x54, 0x43, 0xda, 0x65,
	0x64, 0x90, 0x2d, 0x84, 0x2d, 0xa1, 0x35, 0xbc, 0xe4, 0x46, 0xf6, 0x23, 0x0b, 0x25, 0x1b, 0x04,
	0x7f, 0xda, 0x61, 0xe8, 0x5d, 0x48, 0xa1, 0x2f, 0xe0, 0x05, 0xe4, 0x26, 0xec, 0x71, 0xaa, 0xd5,
	0x25, 0x94, 0x3a, 0xf2, 0x6c, 0xe7, 0x4c, 0xc6, 0xcc, 0xb8, 0x6c, 0xfe, 0x41, 0x83, 0xcb, 0x0b,
	0x82, 0xcf, 0x43, 0x72, 0x1d, 0xea, 0xa7, 0x4c, 0x58, 0x49, 0x06, 0x4c, 0x03, 0x57, 0x3b, 0x65,
	0xe2, 0x98, 0xb9, 0xe4, 0x4d, 0x58, 0x43, 0x72, 0x21, 0x11, 0xba, 0xa5, 0xdc, 0xc1, 0xd5, 0x53,
	0x26, 0x0a, 0xa1, 0xbd, 0x05, 0xad, 0x88, 0xf2, 0xd8, 0x13, 0xc9, 0xd9, 0x7c, 0x36, 0x40, 0x42,
	0xc0, 0x63, 0xe6, 0x36, 0xc0, 0x20, 0xfb, 0x45, 0x00, 0x6a, 0x87, 0x0f, 0x06, 0x1f, 0xed, 0x1c,
	0x18, 0x2b, 0x68, 0xdd, 0xf0, 0x78, 0xf8, 0x71, 0x7f, 0xaf, 0xff, 0xe0, 0xd0, 0xd0, 0x48, 0x13,
	0xaa, 0xbb, 0x07, 0x3b, 0x7b, 0x1f, 0x1a, 0x25, 0xf3, 0x3d, 0x30, 0x86, 0xa7, 0xf1, 0x84, 0xb2,
	0x7b, 0xf4, 0x9c, 0x39, 0xb4, 0xef, 0x9f, 0x04, 0xe4, 0x1b, 0xa0, 0xf3, 0x89, 0xe5, 0x4a, 0xc0,
	0x4a, 0x8d, 0x68, 0x2a, 0x79, 0x7c, 0x92, 0x9e, 0x74, 0xcd, 0x7f, 0x68, 0x60, 0xec, 0xa4, 0x65,
	0x83, 0x9e, 0x90, 0x97, 0x97, 0xa5, 0x7e, 0x0f, 0x1a, 0xe8, 0x51, 0xdf, 0x9e, 0x24, 0xc6, 0x2a,
	0x86, 0x19, 0x4a, 0xd6, 0xa1, 0x6a, 0x7b, 0xcc, 0xe6, 0xd2, 0x3e, 0x45, 0x4e, 0x20, 0xbc, 0xed,
	0x33, 0xe7, 0x4c, 0xde, 0xae, 0xe4, 0x6f, 0x2b, 0x94, 0x6c, 0x40, 0x5d, 0x04, 0x67, 0xd4, 0x67,
	0x6e, 0xb7, 0x9a, 0x3b, 0xa0, 0x40, 0x72, 0x0d, 0x6a, 0x63, 0xea, 0xbb, 0x34, 0xea, 0xd6, 0xf2,
	0x31, 0x49, 0x30, 0xf2, 0x0a, 0xe8, 0xe1, 0x69, 0xe0, 0x53, 0xcb, 0x8f, 0x27, 0x23, 0x1a, 0x75,
	0xeb, 0x39, 0x16, 0x2d, 0x49, 0x39, 0x94, 0x04, 0xf3, 0x7c, 0x6a, 0xf2, 0xde, 0xa9, 0xed, 0x4b,
	0x93, 0x6f, 0x02, 0x38, 0xa7, 0xb6, 0xef, 0x53, 0xcf, 0x9a, 0xb1, 0xbc, 0x99, 0xe2, 0x7d, 0x17,
	0x0f, 0xb9, 0x8c, 0x87, 0x9e, 0x7d, 0x81, 0x87, 0xf2, 0xe1, 0x6e, 0xa6, 0x78, 0xdf, 0x25, 0x5d,
	0xa8, 0x48, 0x13, 0xf3, 0x1e, 0x90, 0x88, 0xf9, 0x0e, 0xac, 0x29, 0xb9, 0x1f, 0x44, 0x41, 0x1c,
	0x4a, 0xc1, 0x37, 0xa0, 0x31, 0xc6, 0x1f, 0xb3, 0x62, 0xeb, 0x12, 0xed, 0xbb, 0xe6, 0xaf, 0x35,
	0x68, 0x0d, 0xe3, 0xd1, 0x11, 0xfd, 0x54, 0xc8, 0x0b, 0x5d, 0xa8, 0x60, 0x55, 0x14, 0x22, 0x2a,
	0x11, 0xf2, 0x2d, 0xb8, 0xc2, 0xe3, 0x51, 0x52, 0x33, 0x0e, 0x66, 0xb2, 0xe5, 0x04, 0xbe, 0x3c,
	0x8b, 0xaa, 0xea, 0xe9, 0xd9, 0x17, 0x78, 0xc2, 0x4c, 0xe6, 0xfa, 0x5e, 0x72, 0x80, 0xbc, 0x04,
	0x4d, 0xd7, 0x16, 0xf6, 0x7c, 0x6e, 0x36, 0x10, 0x96, 0xb9, 0x78, 0x1b, 0x3a, 0xf2, 0x08, 0x8a,
	0xe0, 0x0e, 0xf5, 0x8b, 0x41, 0xd4, 0x91, 0x36, 0x8c, 0x47, 0x43, 0xa4, 0x98, 0x7f, 0xa9, 0x82,
	0x9e, 0xc9, 0xc0, 0x66, 0x3a, 0x55, 0xba, 0x34, 0xa3, 0xf4, 0x5d, 0x68, 0x62, 0xf6, 0x58, 0xcc,
	0x3f, 0x09, 0xa4, 0x9e, 0xad, 0xed, 0x1b, 0x77, 0x8a, 0xdd, 0x7d, 0x36, 0x3f, 0x93, 0x7c, 0x93,
	0xce, 0xd8, 0x01, 0x1d, 0xdb, 0x6b, 0xc6, 0xa0, 0xfc, 0x7c, 0x0c, 0x40, 0x04, 0x59, 0xb2, 0x5f,
	0x82, 0x12, 0x0b, 0x0b, 0xb6, 0x94, 0x58, 0x48, 0xee, 0x80, 0x91, 0x15, 0x8f, 0x35, 0xb2, 0x39,
	0x7d, 0xf7, 0x9d, 0x42, 0x4e, 0x76, 0xdc, 0xb4, 0x82, 0x76, 0x25, 0xad, 0xe8, 0xc0, 0xda, 0x73,
	0x3a, 0xb0, 0xbe, 0xcc, 0x81, 0x64, 0x1f, 0x3a, 0xb9, 0xf2, 0x45, 0xcb, 0x1a, 0x0b, 0x2d, 0x9b,
	0xad, 0xfb, 0x81, 0x9e, 0xd5, 0x36, 0xda, 0xb6, 0x0b, 0x7a, 0x96, 0xd5, 0xc8, 0xa4, 0xf9, 0x4c,
	0xf7, 0xa8, 0x62, 0x18, 0xb4, 0x54, 0xca, 0x23, 0x8f, 0x6f, 0x03, 0xa4, 0x09, 0x8a, 0x1c, 0x40,
	0x72, 0xe8, 0x2d, 0xe1, 0x90, 0xa5, 0xf5, 0xa0, 0x39, 0xce, 0x32, 0x7c, 0x1b, 0xc8, 0x82, 0x94,
	0x6c, 0xe5, 0x52, 0xd2, 0x10, 0xb3, 0xf9, 0xf8, 0x0a, 0xe8, 0x36, 0xbf, 0xf0, 0x1d, 0x8b, 0x0b,
	0x5b, 0xc4, 0xbc, 0xab, 0xf7, 0xb4, 0xcd, 0x86, 0xaa, 0x65, 0x49, 0x19, 0x4a, 0x82, 0x2c, 0xfa,
	0x28, 0x70, 0x63, 0x87, 0x5a, 0x82, 0x4d, 0x68, 0xb7, 0x9d, 0x73, 0x7d, 0x2b, 0xa5, 0x1c, 0xb1,
	0x09, 0x25, 0x1f, 0x00, 0xc9, 0x8a, 0x03, 0x2d, 0x49, 0xc6, 0x76, 0xa7, 0x57, 0xde, 0x6c, 0x6d,
	0xaf, 0xcf, 0x7a, 0x75, 0x5a, 0x6e, 0x83, 0x55, 0x3e, 0xfd, 0x81, 0x13, 0xdd, 0xfc, 0x93, 0x06,
	0xed, 0x5c, 0x6e, 0xf3, 0x10, 0xdb, 0x52, 0xd2, 0xc1, 0x0b, 0x05, 0x9c, 0x62, 0xd8, 0x34, 0x3c,
	0x7b, 0xa4, 0x22, 0x90, 0x6f, 0x9b, 0x4d, 0x89, 0x4b, 0x1f, 0x99, 0xd0, 0x8c, 0xe8, 0x27, 0x31,
	0xe5, 0xa2, 0xef, 0x16, 0x3a, 0xc7, 0x14, 0xc6, 0x14, 0x8b, 0x18, 0x3f, 0x4b, 0x52, 0xac, 0x92,
	0x4f, 0x31, 0x84, 0xe5, 0xf4, 0xd8, 0x82, 0xda, 0x60, 0x7f, 0x78, 0x7c, 0x70, 0x44, 0x1a, 0x50,
	0xf9, 0x78, 0x67, 0x38, 0x9c, 0x9f, 0x1b, 0x3a, 0x34, 0x0e, 0x1f, 0x1c, 0x59, 0x92, 0x58, 0x32,
	0xff, 0xa5, 0x41, 0xe7, 0xde, 0xc3, 0xbd, 0x60, 0x32, 0xc9, 0x4a, 0xb5, 0x50, 0x90, 0xda, 0xff,
	0x5a, 0x90, 0xa5, 0xaf, 0x5e, 0x90, 0x37, 0x01, 0xe8, 0xb9, 0xdc, 0x41, 0x54, 0x33, 0xca, 0x9c,
	0x21, 0x71, 0x59, 0x4c, 0xd3, 0x43, 0x6c, 0x66, 0x9c, 0xa4, 0x87, 0x30, 0xe6, 0x37, 0xa0, 0xa1,
	0xc2, 0x5d, 0x1c, 0x28, 0x69, 0x3c, 0xcd, 0xcf, 0x4a, 0xb0, 0x7a, 0xef, 0xe1, 0x41, 0x30, 0x66,
	0xfe, 0xff, 0xc9, 0xfe, 0xa2, 0xf2, 0xa5, 0xc5, 0xca, 0x27, 0x2d, 0xa7, 0x3c, 0xd3, 0x72, 0x5e,
	0x83, 0x8e, 0xe3, 0x31, 0xbc, 0x7b, 0x4e, 0x23, 0xce, 0x02, 0xbf, 0x10, 0xe4, 0x76, 0x42, 0x7b,
	0x98, 0x90, 0x66, 0xec, 0xaf, 0x2e, 0xb6, 0x1f, 0x9b, 0x52, 0xb6, 0x01, 0xd4, 0xf2, 0x23, 0x57,
	0x75, 0x2f, 0xdc, 0x4f, 0x03, 0x8e, 0x02, 0x0b, 0xcd, 0xa8, 0x1a, 0xf0, 0x87, 0x34, 0xc2, 0x69,
	0x2e, 0x47, 0xaf, 0x6c, 0x3e, 0xcd, 0xe9, 0xee, 0x7a, 0x46, 0x7d, 0xf3, 0x89, 0x06, 0xad, 0x7b,
	0x0f, 0xa7, 0x45, 0xf0, 0x2a, 0xb4, 0x23, 0xca, 0xc3, 0xc0, 0xe7, 0x34, 0xb1, 0x3d, 0x3f, 0x9f,
	0x74, 0x45, 0x92, 0xe6, 0x5f, 0x87, 0xba, 0x13, 0x73, 0x61, 0xf9, 0xc5, 0x72, 0xa8, 0x21, 0x78,
	0x28, 0x27, 0x62, 0xb2, 0xc6, 0xf9, 0x41, 0xc1, 0x47, 0x75, 0x89, 0x1e, 0x06, 0xa8, 0x16, 0x77,
	0x82, 0x28, 0x09, 0xbb, 0xa6, 0xd4, 0x92, 0x90, 0xf4, 0x4b, 0x14, 0xa9, 0x8d, 0xac, 0xe8, 0x17,
	0xc4, 0xa5, 0x02, 0xaf, 0x80, 0x9e, 0x1c, 0x72, 0xa9, 0xb0, 0x99, 0x57, 0x70, 0x4d, 0x4b, 0x52,
	0xee, 0x49, 0x82, 0xf9, 0x1b, 0x0d, 0x5e, 0x50, 0xa1, 0xff, 0x88, 0x71, 0xf4, 0xbc, 0x8a, 0xf2,
	0x98, 0x0a, 0xcb, 0x09, 0xe2, 0x30, 0xf0, 0xa5, 0xa5, 0x55, 0x25, 0x65, 0x4c, 0xc5, 0x9e, 0x84,
	0xa7, 0x21, 0x9a, 0xdb, 0x97, 0x92, 0x10, 0x1d, 0xe2, 0xca, 0x73, 0x1b, 0x3a, 0xc2, 0xe6, 0xd8,
	0x16, 0x79, 0x1a, 0x4b, 0x34, 0xb9, 0xac, 0xfc, 0x86, 0xb4, 0xbd, 0x80, 0x27, 0xe1, 0xbc, 0x05,
	0x2d, 0x79, 0xd6, 0x9e, 0x04, 0xb1, 0x2f, 0xa4, 0xf5, 0xea, 0x20, 0x20, 0x61, 0x47, 0xe2, 0xe6,
	0xef, 0x34, 0xb8, 0xa4, 0x94, 0xde, 0xff, 0x14, 0x5b, 0xf9, 0x38, 0x99, 0x06, 0x3d, 0x68, 0x30,
	0x5f, 0xd0, 0xe8, 0xdc, 0xf6, 0xa4, 0xce, 0xea, 0x72, 0x86, 0xa2, 0x67, 0xc3, 0x88, 0x39, 0x45,
	0x6d, 0x13, 0x08, 0x6f, 0xbb, 0x8c, 0x3b, 0x52, 0x74, 0xb9, 0x90, 0x4b, 0x29, 0x8a, 0xeb, 0x9b,
	0xed, 0x38, 0x99, 0x6e, 0xd3, 0xb8, 0x25, 0x20, 0xa6, 0x88, 0x3a, 0x3b, 0x1f, 0x1e, 0x5d, 0x91,
	0x64, 0x23, 0xfb, 0x6d, 0x19, 0xae, 0xaa, 0x2a, 0x3b, 0xb2, 0xf9, 0xd9, 0x3e, 0x3a, 0x2c, 0x2b,
	0xd1, 0x17, 0xa1, 0x66, 0x87, 0xe1, 0xec, 0x5a, 0x5b, 0xb5, 0xc3, 0xb0, 0xef, 0xaa, 0xe5, 0xb5,
	0x34, 0xbb, 0xbc, 0xbe, 0x04, 0xcd, 0xb4, 0xbc, 0x64, 0xed, 0x4d, 0xf7, 0x90, 0x46, 0x02, 0xf7,
	0x43, 0x8c, 0x98, 0x2c, 0x7d, 0x8f, 0x9e, 0x53, 0x4f, 0xda, 0x90, 0x85, 0x15, 0xf1, 0x03, 0x84,
	0x31, 0x7b, 0x65, 0x14, 0x66, 0x96, 0xd4, 0x1a, 0x82, 0x7d, 0xb9, 0x23, 0x27, 0xc5, 0xb5, 0xb8,
	0xe4, 0x98, 0x2b, 0x9d, 0x8c, 0xdb, 0x68, 0xb1, 0xe2, 0x24, 0x84, 0x9d, 0x21, 0xe0, 0xb2, 0xdc,
	0x94, 0xe4, 0x52, 0xc0, 0x31, 0xf0, 0x68, 0xaf, 0x6a, 0x0b, 0xcd, 0x1c, 0x19, 0xec, 0x30, 0x54,
	0x3d, 0xe1, 0x3a, 0xd4, 0xa3, 0xc0, 0x93, 0xc5, 0x0e, 0x79, 0xcd, 0x10, 0xec, 0xbb, 0x64, 0x1f,
	0xf4, 0x49, 0x92, 0xc3, 0x49, 0x6f, 0x6b, 0xc9, 0xde, 0x66, 0xce, 0xf4, 0xb6, 0x05, 0xe9, 0x3e,
	0x68, 0x4d, 0x72, 0xb9, 0x9f, 0x5f, 0xe3, 0xf5, 0x45, 0x6b, 0xbc, 0xf9, 0xfb, 0x32, 0x5c, 0xdf,
	0x99, 0x49, 0xc0, 0xaf, 0x51, 0x00, 0x37, 0xa0, 0x9e, 0x2c, 0x06, 0xa2, 0x38, 0x14, 0x52, 0xf0,
	0x6b, 0x1c, 0xc1, 0xfb, 0xd0, 0xa6, 0xa9, 0x3f, 0xf3, 0x21, 0xbc, 0xb9, 0x24, 0x84, 0xf9, 0xe2,
	0x1f, 0xe8, 0x74, 0xa6, 0x15, 0x7c, 0x49, 0x10, 0x3f, 0xd3, 0xe0, 0x4a, 0x16, 0xc4, 0x5c, 0xf0,
	0xb8, 0xf4, 0x72, 0xba, 0x95, 0xcc, 0x46, 0x30, 0xb7, 0xad, 0x74, 0xa1, 0xe2, 0x04, 0x6e, 0xd2,
	0x49, 0x94, 0xa9, 0x12, 0x41, 0xff, 0x4f, 0x28, 0xe7, 0xf6, 0xb8, 0x38, 0xdc, 0x15, 0x28, 0xd9,
	0xe3, 0x9e, 0x33, 0x0d, 0xe2, 0x94, 0x3d, 0xe3, 0x67, 0x49, 0x10, 0xb3, 0x19, 0x50, 0xcd, 0xf1,
	0x4f, 0x20, 0xf3, 0x51, 0xf1, 0x51, 0x9f, 0xbc, 0xa7, 0x9f, 0xf5, 0x49, 0x67, 0xba, 0xc0, 0x95,
	0xe6, 0x17, 0x38, 0x73, 0x13, 0x3a, 0x45, 0x56, 0xb9, 0xe5, 0x2a, 0xbf, 0x4d, 0x69, 0x66, 0x17,
	0xae, 0x2c, 0x12, 0xcc, 0x43, 0xf3, 0x5d, 0xb8, 0xba, 0x6b, 0x0b, 0xe7, 0x74, 0xe1, 0x97, 0xa6,
	0xab, 0xd0, 0x88, 0x99, 0x9b, 0x2c, 0xa4, 0x9a, 0xfc, 0x8e, 0x54, 0x8f, 0x99, 0x2b, 0x97, 0xcd,
	0xfb, 0x60, 0xe4, 0xaf, 0x3c, 0xf3, 0x75, 0x7e, 0x0d, 0x6a, 0xe9, 0xb6, 0x8c, 0x56, 0xa8, 0x6d,
	0x39, 0xc5, 0x4c, 0x06, 0xeb, 0xcb, 0x34, 0xe0, 0x21, 0xf9, 0x10, 0xd6, 0x50, 0x85, 0xec, 0x7b,
	0x46, 0xa6, 0xcb, 0xfc, 0xf2, 0x33, 0xab, 0xcf, 0x60, 0x35, 0x66, 0xae, 0x02, 0xa4, 0xd2, 0x5f,
	0x54, 0xe0, 0xf2, 0xa2, 0xd4, 0xf9, 0x2f, 0xeb, 0x5e, 0x6d, 0x4b, 0xa5, 0xc2, 0xb6, 0xf4, 0xdd,
	0xc2, 0xa2, 0x55, 0xe9, 0x95, 0x36, 0x3b, 0xdb, 0x6f, 0x2f, 0xd9, 0xd3, 0x0a, 0x4a, 0xdc, 0x91,
	0xef, 0x90, 0x7d, 0xb5, 0x8c, 0xcd, 0x6f, 0x67, 0xaf, 0x41, 0x47, 0xae, 0x38, 0x16, 0xa7, 0x21,
	0x8d, 0x6c, 0x51, 0xcc, 0xb1, 0xb6, 0xa4, 0x0d, 0x53, 0x52, 0xa1, 0x90, 0x6a, 0x0b, 0x3f, 0x6a,
	0xe4, 0xdb, 0x49, 0x7d, 0x61, 0x3b, 0x29, 0x76, 0xad, 0xc6, 0xe2, 0xae, 0x95, 0xf5, 0x9c, 0xe6,
	0x7c, 0xcf, 0x79, 0x0b, 0xd6, 0x4e, 0xb2, 0xce, 0x7e, 0xce, 0x04, 0x8d, 0xd3, 0x06, 0xa2, 0xfc,
	0x68, 0x9c, 0xa8, 0x06, 0x9e, 0x52, 0xc9, 0x5b, 0xb2, 0x4d, 0x61, 0xff, 0xe8, 0x6c, 0xbf, 0xb8,
	0xf0, 0x51, 0xf9, 0x60, 0x98, 0x73, 0xcf, 0x82, 0x1e, 0xa6, 0x2f, 0xe9, 0x61, 0x37, 0x01, 0xe4,
	0x1b, 0xcf, 0x97, 0xdd, 0xa1, 0x9d, 0x2f, 0xdf, 0x14, 0xef, 0xbb, 0xe6, 0x2e, 0x74, 0x8a, 0x61,
	0xc0, 0x67, 0x4a, 0xec, 0xbb, 0xf4, 0x84, 0xf9, 0xd4, 0x35, 0x56, 0x48, 0x0b, 0xea, 0xa9, 0xce,
	0x86, 0x46, 0x00, 0x6a, 0xc9, 0xaa, 0x65, 0x94, 0xb0, 0xf6, 0x3c, 0x76, 0x46, 0x8d, 0xf2, 0xed,
	0xbb, 0xd0, 0x18, 0xa4, 0x0f, 0x1f, 0x42, 0xa0, 0x93, 0x3c, 0x7c, 0xac, 0x7b, 0xfb, 0xdf, 0xd9,
	0x39, 0x3e, 0x38, 0x32, 0x56, 0xc8, 0x35, 0xe8, 0xa6, 0xd8, 0xfd, 0xfe, 0x07, 0xf7, 0xad, 0x41,
	0x7f, 0xf8, 0xa1, 0xb5, 0xb3, 0xb7, 0xf7, 0xe0, 0xf8, 0xf0, 0xc8, 0xd0, 0x6e, 0xef, 0x82, 0x9e,
	0xb7, 0x13, 0xf9, 0xfa, 0x81, 0x4f, 0x13, 0xd1, 0xb6, 0xef, 0x46, 0x01, 0x73, 0x0d, 0x8d, 0xd4,
	0xa1, 0xcc, 0x02, 0x6e, 0x94, 0x48, 0x13, 0xaa, 0x8f, 0xa8, 0x1d, 0x86, 0x46, 0x19, 0xb1, 0x47,
	0x74, 0x64, 0x54, 0xb6, 0xff, 0xd8, 0x80, 0xb6, 0xca, 0x31, 0x7c, 0x44, 0x38, 0xe4, 0x02, 0xd6,
	0xe6, 0x0a, 0x8c, 0xdc, 0x7c, 0x46, 0x05, 0xa9, 0xac, 0x5c, 0x7f, 0xf9, 0xcb, 0x0f, 0xf1, 0xd0,
	0xbc, 0xfa, 0x93, 0xc7, 0x4f, 0xcb, 0xda, 0xcf, 0x1e, 0x3f, 0x2d, 0x97, 0xe2, 0xf7, 0x7e, 0xf1,
	0xf8, 0x69, 0xb9, 0xf1, 0x46, 0xdc, 0xbb, 0x1b, 0x33, 0xf7, 0x7d, 0x62, 0x43, 0x33, 0x7b, 0x96,
	0x92, 0xd9, 0x90, 0xe6, 0x3f, 0xc6, 0xac, 0x5f, 0x5b, 0x4e, 0x54, 0x22, 0x4a, 0x0b, 0x45, 0xfc,
	0x00, 0xf4, 0xfc, 0x6b, 0x89, 0x6c, 0xcc, 0x30, 0x9a, 0x79, 0x4a, 0xad, 0xaf, 0xcf, 0xd1, 0xa7,
	0x62, 0x56, 0x51, 0x4c, 0x19, 0xc5, 0xac, 0xa0, 0x90, 0x15, 0xf2, 0x3d, 0xf9, 0x9e, 0x50, 0x2f,
	0x51, 0x72, 0x7d, 0xfe, 0x6e, 0xee, 0x95, 0xfa, 0xe5, 0xac, 0x2b, 0x39, 0xd6, 0x11, 0x74, 0x8a,
	0x4b, 0x24, 0xd9, 0x5c, 0xd2, 0x29, 0xe6, 0x76, 0xcd, 0xf5, 0x5b, 0xcf, 0xd1, 0x53, 0x94, 0xcc,
	0x6a, 0x4e, 0xe6, 0x8f, 0x80, 0xcc, 0xef, 0x3e, 0xe4, 0xf5, 0x65, 0xdc, 0x16, 0xad, 0x49, 0x5f,
	0x49, 0x76, 0x2d, 0x27, 0xfb, 0x57, 0x1a, 0x74, 0x8f, 0x43, 0xd7, 0x16, 0x74, 0x7e, 0x1c, 0x91,
	0x67, 0x25, 0x5a, 0x36, 0x2a, 0xe7, 0x44, 0x2f, 0x9b, 0x6b, 0x28, 0xba, 0x8e, 0xa2, 0x2b, 0xf1,
	0x7b, 0x23, 0x99, 0x2e, 0x37, 0x55, 0xba, 0xf4, 0xde, 0x18, 0xf5, 0xee, 0xfa, 0x81, 0xe8, 0xc9,
	0x4f, 0xee, 0xbd, 0x37, 0x7b, 0x3f, 0x4e, 0xff, 0xf7, 0xd6, 0xfb, 0xe4, 0x97, 0x1a, 0x5c, 0xde,
	0xb5, 0xc5, 0xfc, 0x34, 0x9a, 0x8b, 0xcc, 0xd2, 0xb1, 0xb9, 0xfe, 0xea, 0x73, 0x9e, 0xe4, 0xa1,
	0x79, 0x0b, 0xd5, 0x6c, 0xe4, 0x72, 0xfa, 0x52, 0xaa, 0xe4, 0xeb, 0xb9, 0x7f, 0xef, 0x93, 0x10,
	0x8c, 0xe1, 0x47, 0x98, 0x66, 0xb9, 0x88, 0xbd, 0xfc, 0x3c, 0x33, 0xe5, 0x2b, 0x45, 0xaa, 0x39,
	0x8d, 0xd4, 0x7a, 0xed, 0xa7, 0x8f, 0x9f, 0x96, 0xbf, 0x88, 0x77, 0xf7, 0x3f, 0x7f, 0xb2, 0xa1,
	0xfd, 0xf9, 0xc9, 0x86, 0xf6, 0xb7, 0x27, 0x1b, 0xda, 0xcf, 0xff, 0xbe, 0xb1, 0xf2, 0xfd, 0xb7,
	0xc7, 0x81, 0x67, 0xfb, 0xe3, 0x3b, 0xdf, 0xdc, 0x16, 0xe2, 0x8e, 0x13, 0x4c, 0xb6, 0xe4, 0x9f,
	0xab, 0x9c, 0xc0, 0xdb, 0xe2, 0x34, 0xc2, 0xc1, 0xc1, 0xb7, 0xe6, 0xfe, 0x2a, 0xf6, 0x9f, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x45, 0xba, 0x15, 0x10, 0x29, 0x1b, 0x00, 0x00,
}
