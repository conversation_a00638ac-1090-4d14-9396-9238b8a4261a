// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-game-player/channel_game_player.proto

package channel_game_player // import "golang.52tt.com/protocol/services/channel-game-player"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 游戏模式信息
type ChannelGameModeInfo struct {
	ModeKey              string   `protobuf:"bytes,1,opt,name=mode_key,json=modeKey,proto3" json:"mode_key,omitempty"`
	GameParam            string   `protobuf:"bytes,2,opt,name=game_param,json=gameParam,proto3" json:"game_param,omitempty"`
	PlayerLimit          []uint32 `protobuf:"varint,3,rep,packed,name=player_limit,json=playerLimit,proto3" json:"player_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGameModeInfo) Reset()         { *m = ChannelGameModeInfo{} }
func (m *ChannelGameModeInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelGameModeInfo) ProtoMessage()    {}
func (*ChannelGameModeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{0}
}
func (m *ChannelGameModeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGameModeInfo.Unmarshal(m, b)
}
func (m *ChannelGameModeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGameModeInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelGameModeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGameModeInfo.Merge(dst, src)
}
func (m *ChannelGameModeInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelGameModeInfo.Size(m)
}
func (m *ChannelGameModeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGameModeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGameModeInfo proto.InternalMessageInfo

func (m *ChannelGameModeInfo) GetModeKey() string {
	if m != nil {
		return m.ModeKey
	}
	return ""
}

func (m *ChannelGameModeInfo) GetGameParam() string {
	if m != nil {
		return m.GameParam
	}
	return ""
}

func (m *ChannelGameModeInfo) GetPlayerLimit() []uint32 {
	if m != nil {
		return m.PlayerLimit
	}
	return nil
}

// 设置房间游戏
type SetChannelGameReq struct {
	GameId      uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameVersion string `protobuf:"bytes,2,opt,name=game_version,json=gameVersion,proto3" json:"game_version,omitempty"`
	// datacenter需要获取返回
	GameName             string               `protobuf:"bytes,3,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameMemberCntLimit   uint32               `protobuf:"varint,4,opt,name=game_member_cnt_limit,json=gameMemberCntLimit,proto3" json:"game_member_cnt_limit,omitempty"`
	CpId                 uint32               `protobuf:"varint,5,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	Uid                  uint32               `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32               `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameMode             *ChannelGameModeInfo `protobuf:"bytes,8,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SetChannelGameReq) Reset()         { *m = SetChannelGameReq{} }
func (m *SetChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameReq) ProtoMessage()    {}
func (*SetChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{1}
}
func (m *SetChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameReq.Unmarshal(m, b)
}
func (m *SetChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameReq.Merge(dst, src)
}
func (m *SetChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameReq.Size(m)
}
func (m *SetChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameReq proto.InternalMessageInfo

func (m *SetChannelGameReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGameReq) GetGameVersion() string {
	if m != nil {
		return m.GameVersion
	}
	return ""
}

func (m *SetChannelGameReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *SetChannelGameReq) GetGameMemberCntLimit() uint32 {
	if m != nil {
		return m.GameMemberCntLimit
	}
	return 0
}

func (m *SetChannelGameReq) GetCpId() uint32 {
	if m != nil {
		return m.CpId
	}
	return 0
}

func (m *SetChannelGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGameReq) GetGameMode() *ChannelGameModeInfo {
	if m != nil {
		return m.GameMode
	}
	return nil
}

type SetChannelGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameResp) Reset()         { *m = SetChannelGameResp{} }
func (m *SetChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameResp) ProtoMessage()    {}
func (*SetChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{2}
}
func (m *SetChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameResp.Unmarshal(m, b)
}
func (m *SetChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameResp.Merge(dst, src)
}
func (m *SetChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameResp.Size(m)
}
func (m *SetChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameResp proto.InternalMessageInfo

// 修改游戏附加信息
type SetChannelGameModeInfoReq struct {
	ChannelId            uint32               `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32               `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Uid                  uint32               `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	GameMode             *ChannelGameModeInfo `protobuf:"bytes,4,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SetChannelGameModeInfoReq) Reset()         { *m = SetChannelGameModeInfoReq{} }
func (m *SetChannelGameModeInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameModeInfoReq) ProtoMessage()    {}
func (*SetChannelGameModeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{3}
}
func (m *SetChannelGameModeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameModeInfoReq.Unmarshal(m, b)
}
func (m *SetChannelGameModeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameModeInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameModeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameModeInfoReq.Merge(dst, src)
}
func (m *SetChannelGameModeInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameModeInfoReq.Size(m)
}
func (m *SetChannelGameModeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameModeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameModeInfoReq proto.InternalMessageInfo

func (m *SetChannelGameModeInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGameModeInfoReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGameModeInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelGameModeInfoReq) GetGameMode() *ChannelGameModeInfo {
	if m != nil {
		return m.GameMode
	}
	return nil
}

type SetChannelGameModeInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameModeInfoResp) Reset()         { *m = SetChannelGameModeInfoResp{} }
func (m *SetChannelGameModeInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameModeInfoResp) ProtoMessage()    {}
func (*SetChannelGameModeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{4}
}
func (m *SetChannelGameModeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameModeInfoResp.Unmarshal(m, b)
}
func (m *SetChannelGameModeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameModeInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameModeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameModeInfoResp.Merge(dst, src)
}
func (m *SetChannelGameModeInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameModeInfoResp.Size(m)
}
func (m *SetChannelGameModeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameModeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameModeInfoResp proto.InternalMessageInfo

// 检查用户是否主持人
type GetMasterReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMasterReq) Reset()         { *m = GetMasterReq{} }
func (m *GetMasterReq) String() string { return proto.CompactTextString(m) }
func (*GetMasterReq) ProtoMessage()    {}
func (*GetMasterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{5}
}
func (m *GetMasterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMasterReq.Unmarshal(m, b)
}
func (m *GetMasterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMasterReq.Marshal(b, m, deterministic)
}
func (dst *GetMasterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMasterReq.Merge(dst, src)
}
func (m *GetMasterReq) XXX_Size() int {
	return xxx_messageInfo_GetMasterReq.Size(m)
}
func (m *GetMasterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMasterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMasterReq proto.InternalMessageInfo

func (m *GetMasterReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMasterReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetMasterResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMasterResp) Reset()         { *m = GetMasterResp{} }
func (m *GetMasterResp) String() string { return proto.CompactTextString(m) }
func (*GetMasterResp) ProtoMessage()    {}
func (*GetMasterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{6}
}
func (m *GetMasterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMasterResp.Unmarshal(m, b)
}
func (m *GetMasterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMasterResp.Marshal(b, m, deterministic)
}
func (dst *GetMasterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMasterResp.Merge(dst, src)
}
func (m *GetMasterResp) XXX_Size() int {
	return xxx_messageInfo_GetMasterResp.Size(m)
}
func (m *GetMasterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMasterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMasterResp proto.InternalMessageInfo

func (m *GetMasterResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 游戏用户信息
type ChannelGamePlayerInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Username             string   `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Openid               string   `protobuf:"bytes,6,opt,name=openid,proto3" json:"openid,omitempty"`
	Seq                  uint32   `protobuf:"varint,7,opt,name=seq,proto3" json:"seq,omitempty"`
	PermissionCode       uint32   `protobuf:"varint,8,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
	Face                 string   `protobuf:"bytes,9,opt,name=face,proto3" json:"face,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGamePlayerInfo) Reset()         { *m = ChannelGamePlayerInfo{} }
func (m *ChannelGamePlayerInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelGamePlayerInfo) ProtoMessage()    {}
func (*ChannelGamePlayerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{7}
}
func (m *ChannelGamePlayerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGamePlayerInfo.Unmarshal(m, b)
}
func (m *ChannelGamePlayerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGamePlayerInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelGamePlayerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGamePlayerInfo.Merge(dst, src)
}
func (m *ChannelGamePlayerInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelGamePlayerInfo.Size(m)
}
func (m *ChannelGamePlayerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGamePlayerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGamePlayerInfo proto.InternalMessageInfo

func (m *ChannelGamePlayerInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *ChannelGamePlayerInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChannelGamePlayerInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *ChannelGamePlayerInfo) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetPermissionCode() uint32 {
	if m != nil {
		return m.PermissionCode
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetFace() string {
	if m != nil {
		return m.Face
	}
	return ""
}

// 查询用户列表
type GetPlayersReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlayersReq) Reset()         { *m = GetPlayersReq{} }
func (m *GetPlayersReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayersReq) ProtoMessage()    {}
func (*GetPlayersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{8}
}
func (m *GetPlayersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayersReq.Unmarshal(m, b)
}
func (m *GetPlayersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayersReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayersReq.Merge(dst, src)
}
func (m *GetPlayersReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayersReq.Size(m)
}
func (m *GetPlayersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayersReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayersReq proto.InternalMessageInfo

func (m *GetPlayersReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPlayersReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetPlayersResp struct {
	GamePlayers          []*ChannelGamePlayerInfo `protobuf:"bytes,1,rep,name=game_players,json=gamePlayers,proto3" json:"game_players,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetPlayersResp) Reset()         { *m = GetPlayersResp{} }
func (m *GetPlayersResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayersResp) ProtoMessage()    {}
func (*GetPlayersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{9}
}
func (m *GetPlayersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayersResp.Unmarshal(m, b)
}
func (m *GetPlayersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayersResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayersResp.Merge(dst, src)
}
func (m *GetPlayersResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayersResp.Size(m)
}
func (m *GetPlayersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayersResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayersResp proto.InternalMessageInfo

func (m *GetPlayersResp) GetGamePlayers() []*ChannelGamePlayerInfo {
	if m != nil {
		return m.GamePlayers
	}
	return nil
}

// 游戏加载完，登录获取openid
type GetOpenidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	AppId                uint32   `protobuf:"varint,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOpenidReq) Reset()         { *m = GetOpenidReq{} }
func (m *GetOpenidReq) String() string { return proto.CompactTextString(m) }
func (*GetOpenidReq) ProtoMessage()    {}
func (*GetOpenidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{10}
}
func (m *GetOpenidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenidReq.Unmarshal(m, b)
}
func (m *GetOpenidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenidReq.Marshal(b, m, deterministic)
}
func (dst *GetOpenidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenidReq.Merge(dst, src)
}
func (m *GetOpenidReq) XXX_Size() int {
	return xxx_messageInfo_GetOpenidReq.Size(m)
}
func (m *GetOpenidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenidReq proto.InternalMessageInfo

func (m *GetOpenidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetOpenidReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetOpenidReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetOpenidReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

type GetOpenidResp struct {
	OpenId               string   `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	Code                 int32    `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOpenidResp) Reset()         { *m = GetOpenidResp{} }
func (m *GetOpenidResp) String() string { return proto.CompactTextString(m) }
func (*GetOpenidResp) ProtoMessage()    {}
func (*GetOpenidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{11}
}
func (m *GetOpenidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenidResp.Unmarshal(m, b)
}
func (m *GetOpenidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenidResp.Marshal(b, m, deterministic)
}
func (dst *GetOpenidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenidResp.Merge(dst, src)
}
func (m *GetOpenidResp) XXX_Size() int {
	return xxx_messageInfo_GetOpenidResp.Size(m)
}
func (m *GetOpenidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenidResp proto.InternalMessageInfo

func (m *GetOpenidResp) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *GetOpenidResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

// 加入游戏
type JoinChannelGameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	PermissionsCode      uint32   `protobuf:"varint,4,opt,name=permissions_code,json=permissionsCode,proto3" json:"permissions_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinChannelGameReq) Reset()         { *m = JoinChannelGameReq{} }
func (m *JoinChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*JoinChannelGameReq) ProtoMessage()    {}
func (*JoinChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{12}
}
func (m *JoinChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelGameReq.Unmarshal(m, b)
}
func (m *JoinChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *JoinChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelGameReq.Merge(dst, src)
}
func (m *JoinChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_JoinChannelGameReq.Size(m)
}
func (m *JoinChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelGameReq proto.InternalMessageInfo

func (m *JoinChannelGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinChannelGameReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *JoinChannelGameReq) GetPermissionsCode() uint32 {
	if m != nil {
		return m.PermissionsCode
	}
	return 0
}

type JoinChannelGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinChannelGameResp) Reset()         { *m = JoinChannelGameResp{} }
func (m *JoinChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*JoinChannelGameResp) ProtoMessage()    {}
func (*JoinChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{13}
}
func (m *JoinChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelGameResp.Unmarshal(m, b)
}
func (m *JoinChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *JoinChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelGameResp.Merge(dst, src)
}
func (m *JoinChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_JoinChannelGameResp.Size(m)
}
func (m *JoinChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelGameResp proto.InternalMessageInfo

// 取消加入
type QuitChannelGameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuitChannelGameReq) Reset()         { *m = QuitChannelGameReq{} }
func (m *QuitChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*QuitChannelGameReq) ProtoMessage()    {}
func (*QuitChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{14}
}
func (m *QuitChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitChannelGameReq.Unmarshal(m, b)
}
func (m *QuitChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *QuitChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitChannelGameReq.Merge(dst, src)
}
func (m *QuitChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_QuitChannelGameReq.Size(m)
}
func (m *QuitChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuitChannelGameReq proto.InternalMessageInfo

func (m *QuitChannelGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *QuitChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *QuitChannelGameReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type QuitChannelGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QuitChannelGameResp) Reset()         { *m = QuitChannelGameResp{} }
func (m *QuitChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*QuitChannelGameResp) ProtoMessage()    {}
func (*QuitChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{15}
}
func (m *QuitChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitChannelGameResp.Unmarshal(m, b)
}
func (m *QuitChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *QuitChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitChannelGameResp.Merge(dst, src)
}
func (m *QuitChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_QuitChannelGameResp.Size(m)
}
func (m *QuitChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_QuitChannelGameResp proto.InternalMessageInfo

// 准备游戏
type ReadyChannelGameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadyChannelGameReq) Reset()         { *m = ReadyChannelGameReq{} }
func (m *ReadyChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*ReadyChannelGameReq) ProtoMessage()    {}
func (*ReadyChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{16}
}
func (m *ReadyChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadyChannelGameReq.Unmarshal(m, b)
}
func (m *ReadyChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadyChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *ReadyChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadyChannelGameReq.Merge(dst, src)
}
func (m *ReadyChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_ReadyChannelGameReq.Size(m)
}
func (m *ReadyChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadyChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReadyChannelGameReq proto.InternalMessageInfo

func (m *ReadyChannelGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReadyChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReadyChannelGameReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ReadyChannelGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReadyChannelGameResp) Reset()         { *m = ReadyChannelGameResp{} }
func (m *ReadyChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*ReadyChannelGameResp) ProtoMessage()    {}
func (*ReadyChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{17}
}
func (m *ReadyChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadyChannelGameResp.Unmarshal(m, b)
}
func (m *ReadyChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadyChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *ReadyChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadyChannelGameResp.Merge(dst, src)
}
func (m *ReadyChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_ReadyChannelGameResp.Size(m)
}
func (m *ReadyChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadyChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReadyChannelGameResp proto.InternalMessageInfo

// 取消准备
type UnReadyChannelGameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnReadyChannelGameReq) Reset()         { *m = UnReadyChannelGameReq{} }
func (m *UnReadyChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*UnReadyChannelGameReq) ProtoMessage()    {}
func (*UnReadyChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{18}
}
func (m *UnReadyChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnReadyChannelGameReq.Unmarshal(m, b)
}
func (m *UnReadyChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnReadyChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *UnReadyChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnReadyChannelGameReq.Merge(dst, src)
}
func (m *UnReadyChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_UnReadyChannelGameReq.Size(m)
}
func (m *UnReadyChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnReadyChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnReadyChannelGameReq proto.InternalMessageInfo

func (m *UnReadyChannelGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnReadyChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UnReadyChannelGameReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type UnReadyChannelGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnReadyChannelGameResp) Reset()         { *m = UnReadyChannelGameResp{} }
func (m *UnReadyChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*UnReadyChannelGameResp) ProtoMessage()    {}
func (*UnReadyChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{19}
}
func (m *UnReadyChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnReadyChannelGameResp.Unmarshal(m, b)
}
func (m *UnReadyChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnReadyChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *UnReadyChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnReadyChannelGameResp.Merge(dst, src)
}
func (m *UnReadyChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_UnReadyChannelGameResp.Size(m)
}
func (m *UnReadyChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnReadyChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnReadyChannelGameResp proto.InternalMessageInfo

// 更改权限值
type SetChannelGamePlayerPermissionCodeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	PermissionUid        uint32   `protobuf:"varint,4,opt,name=permission_uid,json=permissionUid,proto3" json:"permission_uid,omitempty"`
	PermissionCode       uint32   `protobuf:"varint,5,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGamePlayerPermissionCodeReq) Reset()         { *m = SetChannelGamePlayerPermissionCodeReq{} }
func (m *SetChannelGamePlayerPermissionCodeReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGamePlayerPermissionCodeReq) ProtoMessage()    {}
func (*SetChannelGamePlayerPermissionCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{20}
}
func (m *SetChannelGamePlayerPermissionCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq.Unmarshal(m, b)
}
func (m *SetChannelGamePlayerPermissionCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGamePlayerPermissionCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq.Merge(dst, src)
}
func (m *SetChannelGamePlayerPermissionCodeReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq.Size(m)
}
func (m *SetChannelGamePlayerPermissionCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq proto.InternalMessageInfo

func (m *SetChannelGamePlayerPermissionCodeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelGamePlayerPermissionCodeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGamePlayerPermissionCodeReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGamePlayerPermissionCodeReq) GetPermissionUid() uint32 {
	if m != nil {
		return m.PermissionUid
	}
	return 0
}

func (m *SetChannelGamePlayerPermissionCodeReq) GetPermissionCode() uint32 {
	if m != nil {
		return m.PermissionCode
	}
	return 0
}

type SetChannelGamePlayerPermissionCodeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGamePlayerPermissionCodeResp) Reset() {
	*m = SetChannelGamePlayerPermissionCodeResp{}
}
func (m *SetChannelGamePlayerPermissionCodeResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGamePlayerPermissionCodeResp) ProtoMessage()    {}
func (*SetChannelGamePlayerPermissionCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{21}
}
func (m *SetChannelGamePlayerPermissionCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp.Unmarshal(m, b)
}
func (m *SetChannelGamePlayerPermissionCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGamePlayerPermissionCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp.Merge(dst, src)
}
func (m *SetChannelGamePlayerPermissionCodeResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp.Size(m)
}
func (m *SetChannelGamePlayerPermissionCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp proto.InternalMessageInfo

// 退出游戏
type ExitChannelGameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitChannelGameReq) Reset()         { *m = ExitChannelGameReq{} }
func (m *ExitChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*ExitChannelGameReq) ProtoMessage()    {}
func (*ExitChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{22}
}
func (m *ExitChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitChannelGameReq.Unmarshal(m, b)
}
func (m *ExitChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *ExitChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitChannelGameReq.Merge(dst, src)
}
func (m *ExitChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_ExitChannelGameReq.Size(m)
}
func (m *ExitChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExitChannelGameReq proto.InternalMessageInfo

func (m *ExitChannelGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExitChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ExitChannelGameReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ExitChannelGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExitChannelGameResp) Reset()         { *m = ExitChannelGameResp{} }
func (m *ExitChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*ExitChannelGameResp) ProtoMessage()    {}
func (*ExitChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{23}
}
func (m *ExitChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitChannelGameResp.Unmarshal(m, b)
}
func (m *ExitChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *ExitChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitChannelGameResp.Merge(dst, src)
}
func (m *ExitChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_ExitChannelGameResp.Size(m)
}
func (m *ExitChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExitChannelGameResp proto.InternalMessageInfo

// 开始游戏
type StartChannelGameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	AppId                uint32   `protobuf:"varint,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartChannelGameReq) Reset()         { *m = StartChannelGameReq{} }
func (m *StartChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*StartChannelGameReq) ProtoMessage()    {}
func (*StartChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{24}
}
func (m *StartChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChannelGameReq.Unmarshal(m, b)
}
func (m *StartChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *StartChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChannelGameReq.Merge(dst, src)
}
func (m *StartChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_StartChannelGameReq.Size(m)
}
func (m *StartChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartChannelGameReq proto.InternalMessageInfo

func (m *StartChannelGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartChannelGameReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *StartChannelGameReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

type StartChannelGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartChannelGameResp) Reset()         { *m = StartChannelGameResp{} }
func (m *StartChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*StartChannelGameResp) ProtoMessage()    {}
func (*StartChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_d83b848a896ad79f, []int{25}
}
func (m *StartChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChannelGameResp.Unmarshal(m, b)
}
func (m *StartChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *StartChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChannelGameResp.Merge(dst, src)
}
func (m *StartChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_StartChannelGameResp.Size(m)
}
func (m *StartChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartChannelGameResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ChannelGameModeInfo)(nil), "channel_game_player.ChannelGameModeInfo")
	proto.RegisterType((*SetChannelGameReq)(nil), "channel_game_player.SetChannelGameReq")
	proto.RegisterType((*SetChannelGameResp)(nil), "channel_game_player.SetChannelGameResp")
	proto.RegisterType((*SetChannelGameModeInfoReq)(nil), "channel_game_player.SetChannelGameModeInfoReq")
	proto.RegisterType((*SetChannelGameModeInfoResp)(nil), "channel_game_player.SetChannelGameModeInfoResp")
	proto.RegisterType((*GetMasterReq)(nil), "channel_game_player.GetMasterReq")
	proto.RegisterType((*GetMasterResp)(nil), "channel_game_player.GetMasterResp")
	proto.RegisterType((*ChannelGamePlayerInfo)(nil), "channel_game_player.ChannelGamePlayerInfo")
	proto.RegisterType((*GetPlayersReq)(nil), "channel_game_player.GetPlayersReq")
	proto.RegisterType((*GetPlayersResp)(nil), "channel_game_player.GetPlayersResp")
	proto.RegisterType((*GetOpenidReq)(nil), "channel_game_player.GetOpenidReq")
	proto.RegisterType((*GetOpenidResp)(nil), "channel_game_player.GetOpenidResp")
	proto.RegisterType((*JoinChannelGameReq)(nil), "channel_game_player.JoinChannelGameReq")
	proto.RegisterType((*JoinChannelGameResp)(nil), "channel_game_player.JoinChannelGameResp")
	proto.RegisterType((*QuitChannelGameReq)(nil), "channel_game_player.QuitChannelGameReq")
	proto.RegisterType((*QuitChannelGameResp)(nil), "channel_game_player.QuitChannelGameResp")
	proto.RegisterType((*ReadyChannelGameReq)(nil), "channel_game_player.ReadyChannelGameReq")
	proto.RegisterType((*ReadyChannelGameResp)(nil), "channel_game_player.ReadyChannelGameResp")
	proto.RegisterType((*UnReadyChannelGameReq)(nil), "channel_game_player.UnReadyChannelGameReq")
	proto.RegisterType((*UnReadyChannelGameResp)(nil), "channel_game_player.UnReadyChannelGameResp")
	proto.RegisterType((*SetChannelGamePlayerPermissionCodeReq)(nil), "channel_game_player.SetChannelGamePlayerPermissionCodeReq")
	proto.RegisterType((*SetChannelGamePlayerPermissionCodeResp)(nil), "channel_game_player.SetChannelGamePlayerPermissionCodeResp")
	proto.RegisterType((*ExitChannelGameReq)(nil), "channel_game_player.ExitChannelGameReq")
	proto.RegisterType((*ExitChannelGameResp)(nil), "channel_game_player.ExitChannelGameResp")
	proto.RegisterType((*StartChannelGameReq)(nil), "channel_game_player.StartChannelGameReq")
	proto.RegisterType((*StartChannelGameResp)(nil), "channel_game_player.StartChannelGameResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelGamePlayerClient is the client API for ChannelGamePlayer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelGamePlayerClient interface {
	// ===== channel-open-game =====
	// 游戏用户信息限制信息同步
	SetChannelGame(ctx context.Context, in *SetChannelGameReq, opts ...grpc.CallOption) (*SetChannelGameResp, error)
	// 游戏模式信息同步
	SetChannelGameModeInfo(ctx context.Context, in *SetChannelGameModeInfoReq, opts ...grpc.CallOption) (*SetChannelGameModeInfoResp, error)
	// ===== channel-open-game-logic =====
	// 检查用户是否主持人
	GetMaster(ctx context.Context, in *GetMasterReq, opts ...grpc.CallOption) (*GetMasterResp, error)
	// 查询用户列表
	GetPlayers(ctx context.Context, in *GetPlayersReq, opts ...grpc.CallOption) (*GetPlayersResp, error)
	// ===== channel-game-player-logic =====
	GetOpenid(ctx context.Context, in *GetOpenidReq, opts ...grpc.CallOption) (*GetOpenidResp, error)
	JoinChannelGame(ctx context.Context, in *JoinChannelGameReq, opts ...grpc.CallOption) (*JoinChannelGameResp, error)
	QuitChannelGame(ctx context.Context, in *QuitChannelGameReq, opts ...grpc.CallOption) (*QuitChannelGameResp, error)
	ReadyChannelGame(ctx context.Context, in *ReadyChannelGameReq, opts ...grpc.CallOption) (*ReadyChannelGameResp, error)
	UnReadyChannelGame(ctx context.Context, in *UnReadyChannelGameReq, opts ...grpc.CallOption) (*UnReadyChannelGameResp, error)
	SetChannelGamePlayerPermissionCode(ctx context.Context, in *SetChannelGamePlayerPermissionCodeReq, opts ...grpc.CallOption) (*SetChannelGamePlayerPermissionCodeResp, error)
	// 有用户退出房间
	ExitChannelGame(ctx context.Context, in *ExitChannelGameReq, opts ...grpc.CallOption) (*ExitChannelGameResp, error)
	// 开始游戏
	StartChannelGame(ctx context.Context, in *StartChannelGameReq, opts ...grpc.CallOption) (*StartChannelGameResp, error)
}

type channelGamePlayerClient struct {
	cc *grpc.ClientConn
}

func NewChannelGamePlayerClient(cc *grpc.ClientConn) ChannelGamePlayerClient {
	return &channelGamePlayerClient{cc}
}

func (c *channelGamePlayerClient) SetChannelGame(ctx context.Context, in *SetChannelGameReq, opts ...grpc.CallOption) (*SetChannelGameResp, error) {
	out := new(SetChannelGameResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/SetChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) SetChannelGameModeInfo(ctx context.Context, in *SetChannelGameModeInfoReq, opts ...grpc.CallOption) (*SetChannelGameModeInfoResp, error) {
	out := new(SetChannelGameModeInfoResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/SetChannelGameModeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) GetMaster(ctx context.Context, in *GetMasterReq, opts ...grpc.CallOption) (*GetMasterResp, error) {
	out := new(GetMasterResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/GetMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) GetPlayers(ctx context.Context, in *GetPlayersReq, opts ...grpc.CallOption) (*GetPlayersResp, error) {
	out := new(GetPlayersResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/GetPlayers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) GetOpenid(ctx context.Context, in *GetOpenidReq, opts ...grpc.CallOption) (*GetOpenidResp, error) {
	out := new(GetOpenidResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/GetOpenid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) JoinChannelGame(ctx context.Context, in *JoinChannelGameReq, opts ...grpc.CallOption) (*JoinChannelGameResp, error) {
	out := new(JoinChannelGameResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/JoinChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) QuitChannelGame(ctx context.Context, in *QuitChannelGameReq, opts ...grpc.CallOption) (*QuitChannelGameResp, error) {
	out := new(QuitChannelGameResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/QuitChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) ReadyChannelGame(ctx context.Context, in *ReadyChannelGameReq, opts ...grpc.CallOption) (*ReadyChannelGameResp, error) {
	out := new(ReadyChannelGameResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/ReadyChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) UnReadyChannelGame(ctx context.Context, in *UnReadyChannelGameReq, opts ...grpc.CallOption) (*UnReadyChannelGameResp, error) {
	out := new(UnReadyChannelGameResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/UnReadyChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) SetChannelGamePlayerPermissionCode(ctx context.Context, in *SetChannelGamePlayerPermissionCodeReq, opts ...grpc.CallOption) (*SetChannelGamePlayerPermissionCodeResp, error) {
	out := new(SetChannelGamePlayerPermissionCodeResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/SetChannelGamePlayerPermissionCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) ExitChannelGame(ctx context.Context, in *ExitChannelGameReq, opts ...grpc.CallOption) (*ExitChannelGameResp, error) {
	out := new(ExitChannelGameResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/ExitChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelGamePlayerClient) StartChannelGame(ctx context.Context, in *StartChannelGameReq, opts ...grpc.CallOption) (*StartChannelGameResp, error) {
	out := new(StartChannelGameResp)
	err := c.cc.Invoke(ctx, "/channel_game_player.ChannelGamePlayer/StartChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelGamePlayerServer is the server API for ChannelGamePlayer service.
type ChannelGamePlayerServer interface {
	// ===== channel-open-game =====
	// 游戏用户信息限制信息同步
	SetChannelGame(context.Context, *SetChannelGameReq) (*SetChannelGameResp, error)
	// 游戏模式信息同步
	SetChannelGameModeInfo(context.Context, *SetChannelGameModeInfoReq) (*SetChannelGameModeInfoResp, error)
	// ===== channel-open-game-logic =====
	// 检查用户是否主持人
	GetMaster(context.Context, *GetMasterReq) (*GetMasterResp, error)
	// 查询用户列表
	GetPlayers(context.Context, *GetPlayersReq) (*GetPlayersResp, error)
	// ===== channel-game-player-logic =====
	GetOpenid(context.Context, *GetOpenidReq) (*GetOpenidResp, error)
	JoinChannelGame(context.Context, *JoinChannelGameReq) (*JoinChannelGameResp, error)
	QuitChannelGame(context.Context, *QuitChannelGameReq) (*QuitChannelGameResp, error)
	ReadyChannelGame(context.Context, *ReadyChannelGameReq) (*ReadyChannelGameResp, error)
	UnReadyChannelGame(context.Context, *UnReadyChannelGameReq) (*UnReadyChannelGameResp, error)
	SetChannelGamePlayerPermissionCode(context.Context, *SetChannelGamePlayerPermissionCodeReq) (*SetChannelGamePlayerPermissionCodeResp, error)
	// 有用户退出房间
	ExitChannelGame(context.Context, *ExitChannelGameReq) (*ExitChannelGameResp, error)
	// 开始游戏
	StartChannelGame(context.Context, *StartChannelGameReq) (*StartChannelGameResp, error)
}

func RegisterChannelGamePlayerServer(s *grpc.Server, srv ChannelGamePlayerServer) {
	s.RegisterService(&_ChannelGamePlayer_serviceDesc, srv)
}

func _ChannelGamePlayer_SetChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).SetChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/SetChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).SetChannelGame(ctx, req.(*SetChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_SetChannelGameModeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGameModeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).SetChannelGameModeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/SetChannelGameModeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).SetChannelGameModeInfo(ctx, req.(*SetChannelGameModeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_GetMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).GetMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/GetMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).GetMaster(ctx, req.(*GetMasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_GetPlayers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).GetPlayers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/GetPlayers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).GetPlayers(ctx, req.(*GetPlayersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_GetOpenid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOpenidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).GetOpenid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/GetOpenid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).GetOpenid(ctx, req.(*GetOpenidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_JoinChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).JoinChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/JoinChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).JoinChannelGame(ctx, req.(*JoinChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_QuitChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuitChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).QuitChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/QuitChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).QuitChannelGame(ctx, req.(*QuitChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_ReadyChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadyChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).ReadyChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/ReadyChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).ReadyChannelGame(ctx, req.(*ReadyChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_UnReadyChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnReadyChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).UnReadyChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/UnReadyChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).UnReadyChannelGame(ctx, req.(*UnReadyChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_SetChannelGamePlayerPermissionCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGamePlayerPermissionCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).SetChannelGamePlayerPermissionCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/SetChannelGamePlayerPermissionCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).SetChannelGamePlayerPermissionCode(ctx, req.(*SetChannelGamePlayerPermissionCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_ExitChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExitChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).ExitChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/ExitChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).ExitChannelGame(ctx, req.(*ExitChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelGamePlayer_StartChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelGamePlayerServer).StartChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_game_player.ChannelGamePlayer/StartChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelGamePlayerServer).StartChannelGame(ctx, req.(*StartChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelGamePlayer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_game_player.ChannelGamePlayer",
	HandlerType: (*ChannelGamePlayerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetChannelGame",
			Handler:    _ChannelGamePlayer_SetChannelGame_Handler,
		},
		{
			MethodName: "SetChannelGameModeInfo",
			Handler:    _ChannelGamePlayer_SetChannelGameModeInfo_Handler,
		},
		{
			MethodName: "GetMaster",
			Handler:    _ChannelGamePlayer_GetMaster_Handler,
		},
		{
			MethodName: "GetPlayers",
			Handler:    _ChannelGamePlayer_GetPlayers_Handler,
		},
		{
			MethodName: "GetOpenid",
			Handler:    _ChannelGamePlayer_GetOpenid_Handler,
		},
		{
			MethodName: "JoinChannelGame",
			Handler:    _ChannelGamePlayer_JoinChannelGame_Handler,
		},
		{
			MethodName: "QuitChannelGame",
			Handler:    _ChannelGamePlayer_QuitChannelGame_Handler,
		},
		{
			MethodName: "ReadyChannelGame",
			Handler:    _ChannelGamePlayer_ReadyChannelGame_Handler,
		},
		{
			MethodName: "UnReadyChannelGame",
			Handler:    _ChannelGamePlayer_UnReadyChannelGame_Handler,
		},
		{
			MethodName: "SetChannelGamePlayerPermissionCode",
			Handler:    _ChannelGamePlayer_SetChannelGamePlayerPermissionCode_Handler,
		},
		{
			MethodName: "ExitChannelGame",
			Handler:    _ChannelGamePlayer_ExitChannelGame_Handler,
		},
		{
			MethodName: "StartChannelGame",
			Handler:    _ChannelGamePlayer_StartChannelGame_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-game-player/channel_game_player.proto",
}

func init() {
	proto.RegisterFile("channel-game-player/channel_game_player.proto", fileDescriptor_channel_game_player_d83b848a896ad79f)
}

var fileDescriptor_channel_game_player_d83b848a896ad79f = []byte{
	// 991 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x57, 0xef, 0x72, 0xdb, 0x44,
	0x10, 0x8f, 0xe2, 0x3f, 0x89, 0x37, 0x75, 0x9a, 0xae, 0x63, 0xa3, 0x0a, 0x98, 0x49, 0x8e, 0x69,
	0xeb, 0xc2, 0xc4, 0x1e, 0xc2, 0x74, 0x98, 0x01, 0x3e, 0x91, 0x29, 0x19, 0x03, 0x81, 0xe0, 0x52,
	0x98, 0xe1, 0x03, 0x1a, 0x55, 0xba, 0x04, 0x4d, 0x2d, 0xe9, 0xa2, 0x93, 0x33, 0xcd, 0xf0, 0x04,
	0x3c, 0x03, 0x1f, 0x78, 0x81, 0xbe, 0x04, 0xaf, 0xc4, 0x13, 0x30, 0x77, 0x27, 0xd9, 0xb2, 0x74,
	0xc2, 0xa6, 0x8d, 0xbf, 0xe9, 0x76, 0xf7, 0xf6, 0xf7, 0xbb, 0xdd, 0xbd, 0xdf, 0xd9, 0x70, 0xe4,
	0xfe, 0xe6, 0x84, 0x21, 0x9d, 0x1c, 0x5d, 0x3a, 0x01, 0x3d, 0x62, 0x13, 0xe7, 0x86, 0xc6, 0xc3,
	0xd4, 0x66, 0x0b, 0x9b, 0xad, 0x6c, 0x03, 0x16, 0x47, 0x49, 0x84, 0x1d, 0x8d, 0x8b, 0xc4, 0xd0,
	0x39, 0x51, 0xe6, 0x53, 0x27, 0xa0, 0x67, 0x91, 0x47, 0x47, 0xe1, 0x45, 0x84, 0xf7, 0x61, 0x3b,
	0x88, 0x3c, 0x6a, 0xbf, 0xa4, 0x37, 0xa6, 0x71, 0x60, 0xf4, 0x5b, 0xe3, 0x2d, 0xb1, 0xfe, 0x86,
	0xde, 0xe0, 0xfb, 0x00, 0x2a, 0x81, 0x13, 0x3b, 0x81, 0xb9, 0x29, 0x9d, 0x2d, 0x61, 0x39, 0x17,
	0x06, 0x3c, 0x84, 0x3b, 0x2a, 0xb5, 0x3d, 0xf1, 0x03, 0x3f, 0x31, 0x6b, 0x07, 0xb5, 0x7e, 0x7b,
	0xbc, 0xa3, 0x6c, 0xdf, 0x0a, 0x13, 0x79, 0xbd, 0x09, 0xf7, 0x9e, 0xd1, 0x24, 0x87, 0x3b, 0xa6,
	0x57, 0xf8, 0x0e, 0x6c, 0xc9, 0xbc, 0xbe, 0x27, 0x11, 0xdb, 0xe3, 0xa6, 0x58, 0x8e, 0x3c, 0x91,
	0x51, 0x3a, 0xae, 0x69, 0xcc, 0xfd, 0x28, 0x4c, 0x21, 0x77, 0x84, 0xed, 0x27, 0x65, 0xc2, 0x77,
	0x41, 0x32, 0xb0, 0x43, 0x27, 0xa0, 0x66, 0x4d, 0xfa, 0xb7, 0x85, 0xe1, 0x3b, 0x27, 0xa0, 0xf8,
	0x31, 0x74, 0xa5, 0x33, 0xa0, 0xc1, 0x0b, 0x1a, 0xdb, 0x6e, 0x98, 0xa4, 0xd4, 0xea, 0x12, 0x06,
	0x85, 0xf3, 0x4c, 0xfa, 0x4e, 0xc2, 0x44, 0x32, 0xc4, 0x0e, 0x34, 0x5c, 0x26, 0x98, 0x34, 0x64,
	0x48, 0xdd, 0x65, 0x23, 0x0f, 0xf7, 0xa0, 0x36, 0xf5, 0x3d, 0xb3, 0x29, 0x4d, 0xe2, 0x53, 0x94,
	0x22, 0xab, 0xa9, 0xef, 0x99, 0x5b, 0xd2, 0xd1, 0x4a, 0x2d, 0x23, 0x0f, 0x9f, 0xa6, 0xac, 0x44,
	0xe5, 0xcc, 0xed, 0x03, 0xa3, 0xbf, 0x73, 0xdc, 0x1f, 0xe8, 0xfa, 0xa3, 0xe9, 0x80, 0xe2, 0x2f,
	0x56, 0x64, 0x1f, 0xb0, 0x58, 0x2d, 0xce, 0xc8, 0x6b, 0x03, 0xee, 0x2f, 0x9a, 0x67, 0x5b, 0xe9,
	0x55, 0x81, 0x99, 0x51, 0x64, 0x96, 0xab, 0xf5, 0xe6, 0x42, 0xad, 0xd3, 0x33, 0xd6, 0xe6, 0x67,
	0x5c, 0x38, 0x44, 0xfd, 0x8d, 0x0f, 0xf1, 0x1e, 0x58, 0x55, 0x6c, 0x39, 0x23, 0x5f, 0xc1, 0x9d,
	0x53, 0x9a, 0x9c, 0x39, 0x3c, 0xa1, 0xf1, 0x5b, 0xd0, 0x27, 0x87, 0xd0, 0xce, 0xe5, 0xe1, 0x2c,
	0x3b, 0x8f, 0x31, 0x3b, 0x0f, 0xf9, 0xc7, 0x80, 0x6e, 0x8e, 0xc6, 0xb9, 0x24, 0x2f, 0x67, 0xbe,
	0x14, 0x8b, 0x3d, 0x68, 0xf2, 0xc4, 0x49, 0xa6, 0x3c, 0x83, 0x51, 0x2b, 0xb4, 0x60, 0x7b, 0xca,
	0x69, 0x9c, 0x9f, 0xb6, 0x6c, 0x2d, 0x7c, 0xa1, 0xef, 0xbe, 0x94, 0xbe, 0xba, 0xf2, 0x65, 0x6b,
	0x81, 0xc0, 0xe9, 0xab, 0x74, 0xa8, 0xc4, 0xa7, 0x40, 0x88, 0x18, 0x0d, 0xd3, 0xb1, 0x6a, 0x8d,
	0xd3, 0x95, 0x8a, 0xbc, 0x4a, 0x47, 0x4a, 0x7c, 0xe2, 0x23, 0xb8, 0xcb, 0x68, 0x1c, 0xf8, 0x5c,
	0x0c, 0xbc, 0xed, 0x66, 0x23, 0xd5, 0x1e, 0xef, 0xce, 0xcd, 0x27, 0x91, 0x47, 0x11, 0xa1, 0x7e,
	0xe1, 0xb8, 0xd4, 0x6c, 0xc9, 0x84, 0xf2, 0x9b, 0x9c, 0xca, 0xba, 0xa8, 0xb3, 0xf2, 0xb7, 0x29,
	0xb0, 0x0d, 0xbb, 0xf9, 0x44, 0x9c, 0xe1, 0x59, 0x7a, 0x3b, 0xd5, 0x14, 0x70, 0xd3, 0x38, 0xa8,
	0xf5, 0x77, 0x8e, 0x3f, 0x5c, 0x36, 0x22, 0xf3, 0xba, 0xab, 0x9b, 0x9c, 0xa6, 0x24, 0x91, 0x9c,
	0x84, 0xef, 0x65, 0x15, 0x04, 0xd1, 0x72, 0x53, 0x16, 0xa9, 0x6f, 0xfe, 0x07, 0xf5, 0xda, 0xc2,
	0x68, 0x77, 0xa1, 0xe9, 0x30, 0x79, 0xa9, 0xd5, 0xbd, 0x6f, 0x38, 0x8c, 0x8d, 0x3c, 0xf2, 0x85,
	0x2c, 0x4d, 0x06, 0xc8, 0x99, 0x48, 0x20, 0x9a, 0x90, 0xd5, 0x25, 0xed, 0xc9, 0xc8, 0x13, 0x85,
	0x95, 0x65, 0x17, 0x90, 0x8d, 0xb1, 0xfc, 0x26, 0x7f, 0x18, 0x80, 0x5f, 0x47, 0x7e, 0x58, 0xd0,
	0xb2, 0xdb, 0x63, 0xfd, 0x18, 0xf6, 0xe6, 0xfd, 0xe5, 0xaa, 0xef, 0x8a, 0x7f, 0x6e, 0x1c, 0xb8,
	0x68, 0x3c, 0xe9, 0x42, 0xa7, 0x44, 0x85, 0x33, 0xf2, 0x2b, 0xe0, 0x0f, 0x53, 0x3f, 0x59, 0x17,
	0x43, 0x01, 0x5b, 0xca, 0xcf, 0x19, 0xb1, 0xa1, 0x33, 0xa6, 0x8e, 0x77, 0xb3, 0x36, 0xdc, 0x1e,
	0xec, 0x97, 0x01, 0x38, 0x23, 0x0e, 0x74, 0x9f, 0x87, 0xeb, 0x85, 0x36, 0xa1, 0xa7, 0x83, 0xe0,
	0x8c, 0xfc, 0x6d, 0xc0, 0x83, 0x45, 0x9d, 0x53, 0x83, 0x7d, 0xbe, 0x70, 0x47, 0x6f, 0x77, 0x44,
	0x1e, 0x40, 0x4e, 0x02, 0xec, 0xe9, 0x6c, 0xc0, 0xdb, 0x73, 0xeb, 0x73, 0xdf, 0xd3, 0x09, 0x48,
	0x43, 0x27, 0x20, 0xa4, 0x0f, 0x0f, 0x57, 0x39, 0x82, 0x1a, 0xad, 0xa7, 0xaf, 0xd6, 0x3b, 0x5a,
	0xa5, 0xfc, 0x9c, 0x91, 0x6b, 0xe8, 0x3c, 0x4b, 0x9c, 0x78, 0x6d, 0xb8, 0x55, 0x52, 0xd1, 0x83,
	0xfd, 0x32, 0x2e, 0x67, 0xc7, 0x7f, 0x02, 0xdc, 0x2b, 0x95, 0x0b, 0x5d, 0xd8, 0x5d, 0x2c, 0x23,
	0x3e, 0xd4, 0x8a, 0x62, 0xe9, 0x97, 0x90, 0xf5, 0x68, 0xa5, 0x38, 0xce, 0xc8, 0x06, 0xfe, 0x0e,
	0x3d, 0xfd, 0xb3, 0x8a, 0x83, 0x15, 0x92, 0xe4, 0x7e, 0x31, 0x58, 0xc3, 0xff, 0x15, 0x2f, 0xc1,
	0x7f, 0x84, 0xd6, 0xec, 0xb5, 0xc5, 0x43, 0xed, 0xfe, 0xfc, 0xab, 0x6e, 0x91, 0x65, 0x21, 0x32,
	0xeb, 0xcf, 0x00, 0xf3, 0x27, 0x06, 0x2b, 0xf7, 0xcc, 0x1f, 0x33, 0xeb, 0x83, 0xa5, 0x31, 0x39,
	0xba, 0x4a, 0xe9, 0xab, 0xe9, 0xce, 0x9e, 0x9e, 0x6a, 0xba, 0xf3, 0xc7, 0x82, 0x6c, 0xe0, 0x05,
	0xdc, 0x2d, 0xa8, 0x2e, 0xea, 0xfb, 0x57, 0x7e, 0x26, 0xac, 0xfe, 0x6a, 0x81, 0x19, 0x4e, 0x41,
	0x66, 0x2b, 0x70, 0xca, 0x62, 0x5f, 0x81, 0xa3, 0x53, 0xed, 0x0d, 0xf4, 0x61, 0xaf, 0xa8, 0x6c,
	0xa8, 0xdf, 0xaf, 0xd1, 0x58, 0xeb, 0xf1, 0x8a, 0x91, 0x12, 0x2a, 0x02, 0x2c, 0xcb, 0x28, 0xea,
	0x7f, 0x3a, 0x68, 0x25, 0xdd, 0xfa, 0x68, 0xe5, 0x58, 0x09, 0xf8, 0x97, 0x01, 0x64, 0xb9, 0xb4,
	0xe1, 0x67, 0x2b, 0x5c, 0x85, 0x0a, 0x59, 0xb7, 0x3e, 0x7f, 0xe3, 0xbd, 0x59, 0x97, 0x0b, 0x8a,
	0x57, 0xd1, 0xe5, 0xb2, 0xee, 0x56, 0x74, 0x59, 0x27, 0xa0, 0xb2, 0xcb, 0x45, 0x29, 0xab, 0xe8,
	0xb2, 0x46, 0x69, 0x2b, 0xba, 0xac, 0xd3, 0x46, 0xb2, 0xf1, 0xe5, 0xa7, 0xbf, 0x3c, 0xb9, 0x8c,
	0x26, 0x4e, 0x78, 0x39, 0x78, 0x72, 0x9c, 0x24, 0x03, 0x37, 0x0a, 0x86, 0xf2, 0xff, 0xa8, 0x1b,
	0x4d, 0x86, 0x9c, 0xc6, 0xd7, 0xbe, 0x4b, 0xf9, 0x50, 0xf3, 0x4f, 0xf6, 0x45, 0x53, 0x86, 0x7d,
	0xf2, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf2, 0x33, 0xb1, 0xf0, 0xe7, 0x0e, 0x00, 0x00,
}
