// Code generated by protoc-gen-go. DO NOT EDIT.
// source: present-count-go/present-count-go.proto

package present_count_go // import "golang.52tt.com/protocol/services/present-count-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PresentCountReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsOff                bool     `protobuf:"varint,2,opt,name=is_off,json=isOff,proto3" json:"is_off,omitempty"`
	MicrUsers            []uint32 `protobuf:"varint,3,rep,packed,name=micr_users,json=micrUsers,proto3" json:"micr_users,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelType          uint32   `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentCountReq) Reset()         { *m = PresentCountReq{} }
func (m *PresentCountReq) String() string { return proto.CompactTextString(m) }
func (*PresentCountReq) ProtoMessage()    {}
func (*PresentCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{0}
}
func (m *PresentCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentCountReq.Unmarshal(m, b)
}
func (m *PresentCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentCountReq.Marshal(b, m, deterministic)
}
func (dst *PresentCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentCountReq.Merge(dst, src)
}
func (m *PresentCountReq) XXX_Size() int {
	return xxx_messageInfo_PresentCountReq.Size(m)
}
func (m *PresentCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_PresentCountReq proto.InternalMessageInfo

func (m *PresentCountReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PresentCountReq) GetIsOff() bool {
	if m != nil {
		return m.IsOff
	}
	return false
}

func (m *PresentCountReq) GetMicrUsers() []uint32 {
	if m != nil {
		return m.MicrUsers
	}
	return nil
}

func (m *PresentCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentCountReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetPresentCountByIdReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentCountByIdReq) Reset()         { *m = GetPresentCountByIdReq{} }
func (m *GetPresentCountByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentCountByIdReq) ProtoMessage()    {}
func (*GetPresentCountByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{1}
}
func (m *GetPresentCountByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentCountByIdReq.Unmarshal(m, b)
}
func (m *GetPresentCountByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentCountByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentCountByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentCountByIdReq.Merge(dst, src)
}
func (m *GetPresentCountByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentCountByIdReq.Size(m)
}
func (m *GetPresentCountByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentCountByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentCountByIdReq proto.InternalMessageInfo

func (m *GetPresentCountByIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPresentCountByIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPresentCountByIdResp struct {
	Price                uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentCountByIdResp) Reset()         { *m = GetPresentCountByIdResp{} }
func (m *GetPresentCountByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentCountByIdResp) ProtoMessage()    {}
func (*GetPresentCountByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{2}
}
func (m *GetPresentCountByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentCountByIdResp.Unmarshal(m, b)
}
func (m *GetPresentCountByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentCountByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentCountByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentCountByIdResp.Merge(dst, src)
}
func (m *GetPresentCountByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentCountByIdResp.Size(m)
}
func (m *GetPresentCountByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentCountByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentCountByIdResp proto.InternalMessageInfo

func (m *GetPresentCountByIdResp) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

type PresentCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentCountResp) Reset()         { *m = PresentCountResp{} }
func (m *PresentCountResp) String() string { return proto.CompactTextString(m) }
func (*PresentCountResp) ProtoMessage()    {}
func (*PresentCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{3}
}
func (m *PresentCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentCountResp.Unmarshal(m, b)
}
func (m *PresentCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentCountResp.Marshal(b, m, deterministic)
}
func (dst *PresentCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentCountResp.Merge(dst, src)
}
func (m *PresentCountResp) XXX_Size() int {
	return xxx_messageInfo_PresentCountResp.Size(m)
}
func (m *PresentCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_PresentCountResp proto.InternalMessageInfo

// 获得房间送礼统计开关状态
type GetPresentCountStateReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentCountStateReq) Reset()         { *m = GetPresentCountStateReq{} }
func (m *GetPresentCountStateReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentCountStateReq) ProtoMessage()    {}
func (*GetPresentCountStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{4}
}
func (m *GetPresentCountStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentCountStateReq.Unmarshal(m, b)
}
func (m *GetPresentCountStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentCountStateReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentCountStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentCountStateReq.Merge(dst, src)
}
func (m *GetPresentCountStateReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentCountStateReq.Size(m)
}
func (m *GetPresentCountStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentCountStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentCountStateReq proto.InternalMessageInfo

func (m *GetPresentCountStateReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type MicsPresentCountInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Price                uint32   `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	BgSuffix             string   `protobuf:"bytes,3,opt,name=bg_suffix,json=bgSuffix,proto3" json:"bg_suffix,omitempty"`
	IcSuffic             string   `protobuf:"bytes,4,opt,name=ic_suffic,json=icSuffic,proto3" json:"ic_suffic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicsPresentCountInfo) Reset()         { *m = MicsPresentCountInfo{} }
func (m *MicsPresentCountInfo) String() string { return proto.CompactTextString(m) }
func (*MicsPresentCountInfo) ProtoMessage()    {}
func (*MicsPresentCountInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{5}
}
func (m *MicsPresentCountInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicsPresentCountInfo.Unmarshal(m, b)
}
func (m *MicsPresentCountInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicsPresentCountInfo.Marshal(b, m, deterministic)
}
func (dst *MicsPresentCountInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicsPresentCountInfo.Merge(dst, src)
}
func (m *MicsPresentCountInfo) XXX_Size() int {
	return xxx_messageInfo_MicsPresentCountInfo.Size(m)
}
func (m *MicsPresentCountInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicsPresentCountInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicsPresentCountInfo proto.InternalMessageInfo

func (m *MicsPresentCountInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MicsPresentCountInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *MicsPresentCountInfo) GetBgSuffix() string {
	if m != nil {
		return m.BgSuffix
	}
	return ""
}

func (m *MicsPresentCountInfo) GetIcSuffic() string {
	if m != nil {
		return m.IcSuffic
	}
	return ""
}

type GetPresentCountStateResp struct {
	State                bool                    `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
	MicsPresentCount     []*MicsPresentCountInfo `protobuf:"bytes,2,rep,name=mics_present_count,json=micsPresentCount,proto3" json:"mics_present_count,omitempty"`
	StylePrefix          string                  `protobuf:"bytes,3,opt,name=style_prefix,json=stylePrefix,proto3" json:"style_prefix,omitempty"`
	UncompiledPrefix     string                  `protobuf:"bytes,4,opt,name=uncompiled_prefix,json=uncompiledPrefix,proto3" json:"uncompiled_prefix,omitempty"`
	BgSuffix             string                  `protobuf:"bytes,5,opt,name=bg_suffix,json=bgSuffix,proto3" json:"bg_suffix,omitempty"`
	IcSuffic             string                  `protobuf:"bytes,6,opt,name=ic_suffic,json=icSuffic,proto3" json:"ic_suffic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetPresentCountStateResp) Reset()         { *m = GetPresentCountStateResp{} }
func (m *GetPresentCountStateResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentCountStateResp) ProtoMessage()    {}
func (*GetPresentCountStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{6}
}
func (m *GetPresentCountStateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentCountStateResp.Unmarshal(m, b)
}
func (m *GetPresentCountStateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentCountStateResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentCountStateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentCountStateResp.Merge(dst, src)
}
func (m *GetPresentCountStateResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentCountStateResp.Size(m)
}
func (m *GetPresentCountStateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentCountStateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentCountStateResp proto.InternalMessageInfo

func (m *GetPresentCountStateResp) GetState() bool {
	if m != nil {
		return m.State
	}
	return false
}

func (m *GetPresentCountStateResp) GetMicsPresentCount() []*MicsPresentCountInfo {
	if m != nil {
		return m.MicsPresentCount
	}
	return nil
}

func (m *GetPresentCountStateResp) GetStylePrefix() string {
	if m != nil {
		return m.StylePrefix
	}
	return ""
}

func (m *GetPresentCountStateResp) GetUncompiledPrefix() string {
	if m != nil {
		return m.UncompiledPrefix
	}
	return ""
}

func (m *GetPresentCountStateResp) GetBgSuffix() string {
	if m != nil {
		return m.BgSuffix
	}
	return ""
}

func (m *GetPresentCountStateResp) GetIcSuffic() string {
	if m != nil {
		return m.IcSuffic
	}
	return ""
}

type ClearUserPresentCountReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearUserPresentCountReq) Reset()         { *m = ClearUserPresentCountReq{} }
func (m *ClearUserPresentCountReq) String() string { return proto.CompactTextString(m) }
func (*ClearUserPresentCountReq) ProtoMessage()    {}
func (*ClearUserPresentCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{7}
}
func (m *ClearUserPresentCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearUserPresentCountReq.Unmarshal(m, b)
}
func (m *ClearUserPresentCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearUserPresentCountReq.Marshal(b, m, deterministic)
}
func (dst *ClearUserPresentCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearUserPresentCountReq.Merge(dst, src)
}
func (m *ClearUserPresentCountReq) XXX_Size() int {
	return xxx_messageInfo_ClearUserPresentCountReq.Size(m)
}
func (m *ClearUserPresentCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearUserPresentCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearUserPresentCountReq proto.InternalMessageInfo

func (m *ClearUserPresentCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ClearUserPresentCountReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ClearUserPresentCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearUserPresentCountResp) Reset()         { *m = ClearUserPresentCountResp{} }
func (m *ClearUserPresentCountResp) String() string { return proto.CompactTextString(m) }
func (*ClearUserPresentCountResp) ProtoMessage()    {}
func (*ClearUserPresentCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{8}
}
func (m *ClearUserPresentCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearUserPresentCountResp.Unmarshal(m, b)
}
func (m *ClearUserPresentCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearUserPresentCountResp.Marshal(b, m, deterministic)
}
func (dst *ClearUserPresentCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearUserPresentCountResp.Merge(dst, src)
}
func (m *ClearUserPresentCountResp) XXX_Size() int {
	return xxx_messageInfo_ClearUserPresentCountResp.Size(m)
}
func (m *ClearUserPresentCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearUserPresentCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearUserPresentCountResp proto.InternalMessageInfo

type PresentCountTargetUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Receive              uint32   `protobuf:"varint,2,opt,name=receive,proto3" json:"receive,omitempty"`
	NextLevelValue       uint32   `protobuf:"varint,3,opt,name=next_level_value,json=nextLevelValue,proto3" json:"next_level_value,omitempty"`
	NextLevelUrl         string   `protobuf:"bytes,4,opt,name=next_level_url,json=nextLevelUrl,proto3" json:"next_level_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentCountTargetUser) Reset()         { *m = PresentCountTargetUser{} }
func (m *PresentCountTargetUser) String() string { return proto.CompactTextString(m) }
func (*PresentCountTargetUser) ProtoMessage()    {}
func (*PresentCountTargetUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{9}
}
func (m *PresentCountTargetUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentCountTargetUser.Unmarshal(m, b)
}
func (m *PresentCountTargetUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentCountTargetUser.Marshal(b, m, deterministic)
}
func (dst *PresentCountTargetUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentCountTargetUser.Merge(dst, src)
}
func (m *PresentCountTargetUser) XXX_Size() int {
	return xxx_messageInfo_PresentCountTargetUser.Size(m)
}
func (m *PresentCountTargetUser) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentCountTargetUser.DiscardUnknown(m)
}

var xxx_messageInfo_PresentCountTargetUser proto.InternalMessageInfo

func (m *PresentCountTargetUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentCountTargetUser) GetReceive() uint32 {
	if m != nil {
		return m.Receive
	}
	return 0
}

func (m *PresentCountTargetUser) GetNextLevelValue() uint32 {
	if m != nil {
		return m.NextLevelValue
	}
	return 0
}

func (m *PresentCountTargetUser) GetNextLevelUrl() string {
	if m != nil {
		return m.NextLevelUrl
	}
	return ""
}

// 排行榜，首杀那些需要神秘人的当时状态信息，所以定义一个神秘人信息结构
type UkwInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	Medal                string   `protobuf:"bytes,5,opt,name=medal,proto3" json:"medal,omitempty"`
	HeadFrame            string   `protobuf:"bytes,6,opt,name=head_frame,json=headFrame,proto3" json:"head_frame,omitempty"`
	FakeUid              uint32   `protobuf:"varint,7,opt,name=fake_uid,json=fakeUid,proto3" json:"fake_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UkwInfo) Reset()         { *m = UkwInfo{} }
func (m *UkwInfo) String() string { return proto.CompactTextString(m) }
func (*UkwInfo) ProtoMessage()    {}
func (*UkwInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{10}
}
func (m *UkwInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UkwInfo.Unmarshal(m, b)
}
func (m *UkwInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UkwInfo.Marshal(b, m, deterministic)
}
func (dst *UkwInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UkwInfo.Merge(dst, src)
}
func (m *UkwInfo) XXX_Size() int {
	return xxx_messageInfo_UkwInfo.Size(m)
}
func (m *UkwInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UkwInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UkwInfo proto.InternalMessageInfo

func (m *UkwInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UkwInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UkwInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UkwInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *UkwInfo) GetMedal() string {
	if m != nil {
		return m.Medal
	}
	return ""
}

func (m *UkwInfo) GetHeadFrame() string {
	if m != nil {
		return m.HeadFrame
	}
	return ""
}

func (m *UkwInfo) GetFakeUid() uint32 {
	if m != nil {
		return m.FakeUid
	}
	return 0
}

type PresentCountRankUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SendValue            uint32   `protobuf:"varint,2,opt,name=send_value,json=sendValue,proto3" json:"send_value,omitempty"`
	Rank                 uint32   `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`
	DValue               uint32   `protobuf:"varint,4,opt,name=d_value,json=dValue,proto3" json:"d_value,omitempty"`
	UkwInfo              *UkwInfo `protobuf:"bytes,5,opt,name=ukw_info,json=ukwInfo,proto3" json:"ukw_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentCountRankUser) Reset()         { *m = PresentCountRankUser{} }
func (m *PresentCountRankUser) String() string { return proto.CompactTextString(m) }
func (*PresentCountRankUser) ProtoMessage()    {}
func (*PresentCountRankUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{11}
}
func (m *PresentCountRankUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentCountRankUser.Unmarshal(m, b)
}
func (m *PresentCountRankUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentCountRankUser.Marshal(b, m, deterministic)
}
func (dst *PresentCountRankUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentCountRankUser.Merge(dst, src)
}
func (m *PresentCountRankUser) XXX_Size() int {
	return xxx_messageInfo_PresentCountRankUser.Size(m)
}
func (m *PresentCountRankUser) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentCountRankUser.DiscardUnknown(m)
}

var xxx_messageInfo_PresentCountRankUser proto.InternalMessageInfo

func (m *PresentCountRankUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PresentCountRankUser) GetSendValue() uint32 {
	if m != nil {
		return m.SendValue
	}
	return 0
}

func (m *PresentCountRankUser) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *PresentCountRankUser) GetDValue() uint32 {
	if m != nil {
		return m.DValue
	}
	return 0
}

func (m *PresentCountRankUser) GetUkwInfo() *UkwInfo {
	if m != nil {
		return m.UkwInfo
	}
	return nil
}

type GetPresentCountRankReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentCountRankReq) Reset()         { *m = GetPresentCountRankReq{} }
func (m *GetPresentCountRankReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentCountRankReq) ProtoMessage()    {}
func (*GetPresentCountRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{12}
}
func (m *GetPresentCountRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentCountRankReq.Unmarshal(m, b)
}
func (m *GetPresentCountRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentCountRankReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentCountRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentCountRankReq.Merge(dst, src)
}
func (m *GetPresentCountRankReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentCountRankReq.Size(m)
}
func (m *GetPresentCountRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentCountRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentCountRankReq proto.InternalMessageInfo

func (m *GetPresentCountRankReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPresentCountRankReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetPresentCountRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPresentCountRankReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetPresentCountRankReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetPresentCountRankResp struct {
	ChannelId            uint32                  `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TargetUser           *PresentCountTargetUser `protobuf:"bytes,2,opt,name=target_user,json=targetUser,proto3" json:"target_user,omitempty"`
	MyInfo               *PresentCountRankUser   `protobuf:"bytes,3,opt,name=my_info,json=myInfo,proto3" json:"my_info,omitempty"`
	RankList             []*PresentCountRankUser `protobuf:"bytes,4,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	NextOffset           uint32                  `protobuf:"varint,5,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	ViewCnt              uint32                  `protobuf:"varint,6,opt,name=view_cnt,json=viewCnt,proto3" json:"view_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetPresentCountRankResp) Reset()         { *m = GetPresentCountRankResp{} }
func (m *GetPresentCountRankResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentCountRankResp) ProtoMessage()    {}
func (*GetPresentCountRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_count_go_ae220eca8bd1cc47, []int{13}
}
func (m *GetPresentCountRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentCountRankResp.Unmarshal(m, b)
}
func (m *GetPresentCountRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentCountRankResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentCountRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentCountRankResp.Merge(dst, src)
}
func (m *GetPresentCountRankResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentCountRankResp.Size(m)
}
func (m *GetPresentCountRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentCountRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentCountRankResp proto.InternalMessageInfo

func (m *GetPresentCountRankResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPresentCountRankResp) GetTargetUser() *PresentCountTargetUser {
	if m != nil {
		return m.TargetUser
	}
	return nil
}

func (m *GetPresentCountRankResp) GetMyInfo() *PresentCountRankUser {
	if m != nil {
		return m.MyInfo
	}
	return nil
}

func (m *GetPresentCountRankResp) GetRankList() []*PresentCountRankUser {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetPresentCountRankResp) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

func (m *GetPresentCountRankResp) GetViewCnt() uint32 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

func init() {
	proto.RegisterType((*PresentCountReq)(nil), "present_count_go.PresentCountReq")
	proto.RegisterType((*GetPresentCountByIdReq)(nil), "present_count_go.GetPresentCountByIdReq")
	proto.RegisterType((*GetPresentCountByIdResp)(nil), "present_count_go.GetPresentCountByIdResp")
	proto.RegisterType((*PresentCountResp)(nil), "present_count_go.PresentCountResp")
	proto.RegisterType((*GetPresentCountStateReq)(nil), "present_count_go.GetPresentCountStateReq")
	proto.RegisterType((*MicsPresentCountInfo)(nil), "present_count_go.MicsPresentCountInfo")
	proto.RegisterType((*GetPresentCountStateResp)(nil), "present_count_go.GetPresentCountStateResp")
	proto.RegisterType((*ClearUserPresentCountReq)(nil), "present_count_go.ClearUserPresentCountReq")
	proto.RegisterType((*ClearUserPresentCountResp)(nil), "present_count_go.ClearUserPresentCountResp")
	proto.RegisterType((*PresentCountTargetUser)(nil), "present_count_go.PresentCountTargetUser")
	proto.RegisterType((*UkwInfo)(nil), "present_count_go.UkwInfo")
	proto.RegisterType((*PresentCountRankUser)(nil), "present_count_go.PresentCountRankUser")
	proto.RegisterType((*GetPresentCountRankReq)(nil), "present_count_go.GetPresentCountRankReq")
	proto.RegisterType((*GetPresentCountRankResp)(nil), "present_count_go.GetPresentCountRankResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentCountGoClient is the client API for PresentCountGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentCountGoClient interface {
	SwitchPresentCount(ctx context.Context, in *PresentCountReq, opts ...grpc.CallOption) (*PresentCountResp, error)
	GetPresentCountById(ctx context.Context, in *GetPresentCountByIdReq, opts ...grpc.CallOption) (*GetPresentCountByIdResp, error)
	GetPresentCountState(ctx context.Context, in *GetPresentCountStateReq, opts ...grpc.CallOption) (*GetPresentCountStateResp, error)
	ClearUserPresentCount(ctx context.Context, in *ClearUserPresentCountReq, opts ...grpc.CallOption) (*ClearUserPresentCountResp, error)
	GetPresentCountRank(ctx context.Context, in *GetPresentCountRankReq, opts ...grpc.CallOption) (*GetPresentCountRankResp, error)
	// ===================== 礼物kafka对账 =========================
	// 礼物->图鉴对账
	TimeRangeCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	TimeRangeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 补单图鉴
	FixPresentCountOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
}

type presentCountGoClient struct {
	cc *grpc.ClientConn
}

func NewPresentCountGoClient(cc *grpc.ClientConn) PresentCountGoClient {
	return &presentCountGoClient{cc}
}

func (c *presentCountGoClient) SwitchPresentCount(ctx context.Context, in *PresentCountReq, opts ...grpc.CallOption) (*PresentCountResp, error) {
	out := new(PresentCountResp)
	err := c.cc.Invoke(ctx, "/present_count_go.PresentCountGo/SwitchPresentCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentCountGoClient) GetPresentCountById(ctx context.Context, in *GetPresentCountByIdReq, opts ...grpc.CallOption) (*GetPresentCountByIdResp, error) {
	out := new(GetPresentCountByIdResp)
	err := c.cc.Invoke(ctx, "/present_count_go.PresentCountGo/GetPresentCountById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentCountGoClient) GetPresentCountState(ctx context.Context, in *GetPresentCountStateReq, opts ...grpc.CallOption) (*GetPresentCountStateResp, error) {
	out := new(GetPresentCountStateResp)
	err := c.cc.Invoke(ctx, "/present_count_go.PresentCountGo/GetPresentCountState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentCountGoClient) ClearUserPresentCount(ctx context.Context, in *ClearUserPresentCountReq, opts ...grpc.CallOption) (*ClearUserPresentCountResp, error) {
	out := new(ClearUserPresentCountResp)
	err := c.cc.Invoke(ctx, "/present_count_go.PresentCountGo/ClearUserPresentCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentCountGoClient) GetPresentCountRank(ctx context.Context, in *GetPresentCountRankReq, opts ...grpc.CallOption) (*GetPresentCountRankResp, error) {
	out := new(GetPresentCountRankResp)
	err := c.cc.Invoke(ctx, "/present_count_go.PresentCountGo/GetPresentCountRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentCountGoClient) TimeRangeCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/present_count_go.PresentCountGo/TimeRangeCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentCountGoClient) TimeRangeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/present_count_go.PresentCountGo/TimeRangeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentCountGoClient) FixPresentCountOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/present_count_go.PresentCountGo/FixPresentCountOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentCountGoServer is the server API for PresentCountGo service.
type PresentCountGoServer interface {
	SwitchPresentCount(context.Context, *PresentCountReq) (*PresentCountResp, error)
	GetPresentCountById(context.Context, *GetPresentCountByIdReq) (*GetPresentCountByIdResp, error)
	GetPresentCountState(context.Context, *GetPresentCountStateReq) (*GetPresentCountStateResp, error)
	ClearUserPresentCount(context.Context, *ClearUserPresentCountReq) (*ClearUserPresentCountResp, error)
	GetPresentCountRank(context.Context, *GetPresentCountRankReq) (*GetPresentCountRankResp, error)
	// ===================== 礼物kafka对账 =========================
	// 礼物->图鉴对账
	TimeRangeCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	TimeRangeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 补单图鉴
	FixPresentCountOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
}

func RegisterPresentCountGoServer(s *grpc.Server, srv PresentCountGoServer) {
	s.RegisterService(&_PresentCountGo_serviceDesc, srv)
}

func _PresentCountGo_SwitchPresentCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PresentCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentCountGoServer).SwitchPresentCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_count_go.PresentCountGo/SwitchPresentCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentCountGoServer).SwitchPresentCount(ctx, req.(*PresentCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentCountGo_GetPresentCountById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentCountByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentCountGoServer).GetPresentCountById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_count_go.PresentCountGo/GetPresentCountById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentCountGoServer).GetPresentCountById(ctx, req.(*GetPresentCountByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentCountGo_GetPresentCountState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentCountStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentCountGoServer).GetPresentCountState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_count_go.PresentCountGo/GetPresentCountState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentCountGoServer).GetPresentCountState(ctx, req.(*GetPresentCountStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentCountGo_ClearUserPresentCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearUserPresentCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentCountGoServer).ClearUserPresentCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_count_go.PresentCountGo/ClearUserPresentCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentCountGoServer).ClearUserPresentCount(ctx, req.(*ClearUserPresentCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentCountGo_GetPresentCountRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentCountRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentCountGoServer).GetPresentCountRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_count_go.PresentCountGo/GetPresentCountRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentCountGoServer).GetPresentCountRank(ctx, req.(*GetPresentCountRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentCountGo_TimeRangeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentCountGoServer).TimeRangeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_count_go.PresentCountGo/TimeRangeCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentCountGoServer).TimeRangeCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentCountGo_TimeRangeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentCountGoServer).TimeRangeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_count_go.PresentCountGo/TimeRangeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentCountGoServer).TimeRangeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentCountGo_FixPresentCountOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentCountGoServer).FixPresentCountOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/present_count_go.PresentCountGo/FixPresentCountOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentCountGoServer).FixPresentCountOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentCountGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "present_count_go.PresentCountGo",
	HandlerType: (*PresentCountGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SwitchPresentCount",
			Handler:    _PresentCountGo_SwitchPresentCount_Handler,
		},
		{
			MethodName: "GetPresentCountById",
			Handler:    _PresentCountGo_GetPresentCountById_Handler,
		},
		{
			MethodName: "GetPresentCountState",
			Handler:    _PresentCountGo_GetPresentCountState_Handler,
		},
		{
			MethodName: "ClearUserPresentCount",
			Handler:    _PresentCountGo_ClearUserPresentCount_Handler,
		},
		{
			MethodName: "GetPresentCountRank",
			Handler:    _PresentCountGo_GetPresentCountRank_Handler,
		},
		{
			MethodName: "TimeRangeCount",
			Handler:    _PresentCountGo_TimeRangeCount_Handler,
		},
		{
			MethodName: "TimeRangeOrderIds",
			Handler:    _PresentCountGo_TimeRangeOrderIds_Handler,
		},
		{
			MethodName: "FixPresentCountOrder",
			Handler:    _PresentCountGo_FixPresentCountOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "present-count-go/present-count-go.proto",
}

func init() {
	proto.RegisterFile("present-count-go/present-count-go.proto", fileDescriptor_present_count_go_ae220eca8bd1cc47)
}

var fileDescriptor_present_count_go_ae220eca8bd1cc47 = []byte{
	// 1040 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x56, 0xdd, 0x6e, 0xdb, 0x36,
	0x14, 0xae, 0xec, 0xc6, 0x3f, 0xc7, 0x4d, 0xe6, 0x72, 0x6e, 0x2a, 0xbb, 0x2b, 0xe2, 0x09, 0x43,
	0xe7, 0xad, 0x88, 0x0d, 0x78, 0x1d, 0xb0, 0xbb, 0x01, 0x0d, 0xda, 0xc2, 0x68, 0xb7, 0x14, 0x4a,
	0xdc, 0x8b, 0x01, 0x83, 0xa0, 0x50, 0x94, 0x43, 0x58, 0xa2, 0x54, 0x91, 0x76, 0xec, 0xb7, 0xd8,
	0xc5, 0xb0, 0x9b, 0xbd, 0xc0, 0xb0, 0x17, 0xd8, 0x13, 0xec, 0x05, 0xf6, 0x42, 0x1b, 0x48, 0x4a,
	0x8e, 0x7f, 0xe4, 0xd8, 0xbb, 0xb2, 0xcf, 0xc7, 0xc3, 0x8f, 0x1f, 0xbf, 0x73, 0x48, 0x0a, 0xbe,
	0x8c, 0x13, 0xc2, 0x09, 0x13, 0xa7, 0x38, 0x9a, 0x30, 0x71, 0x3a, 0x8a, 0x7a, 0xeb, 0x40, 0x37,
	0x4e, 0x22, 0x11, 0xa1, 0x7a, 0x8a, 0x3b, 0x0a, 0x77, 0x46, 0x51, 0xeb, 0x84, 0xcc, 0x04, 0x61,
	0x9c, 0x46, 0xac, 0x17, 0xc5, 0x82, 0x46, 0x8c, 0x67, 0xbf, 0x7a, 0x4a, 0xeb, 0x24, 0x21, 0x38,
	0x62, 0x98, 0x06, 0xe4, 0x74, 0xda, 0xef, 0x2d, 0x07, 0x3a, 0xc1, 0xfa, 0xdd, 0x80, 0x4f, 0xde,
	0x6b, 0xda, 0x33, 0xc9, 0x6a, 0x93, 0x8f, 0xe8, 0x29, 0x00, 0xbe, 0x76, 0x19, 0x23, 0x81, 0x43,
	0x3d, 0xd3, 0x68, 0x1b, 0x9d, 0x43, 0xbb, 0x9a, 0x22, 0x03, 0x0f, 0x3d, 0x82, 0x12, 0xe5, 0x4e,
	0xe4, 0xfb, 0x66, 0xa1, 0x6d, 0x74, 0x2a, 0xf6, 0x01, 0xe5, 0xe7, 0xbe, 0x2f, 0x67, 0x85, 0x14,
	0x27, 0xce, 0x84, 0x93, 0x84, 0x9b, 0xc5, 0x76, 0x51, 0xce, 0x92, 0xc8, 0x50, 0x02, 0xa8, 0x0e,
	0xc5, 0x09, 0xf5, 0xcc, 0xfb, 0x8a, 0x4d, 0xfe, 0x45, 0x9f, 0xc3, 0x83, 0x6c, 0x19, 0x31, 0x8f,
	0x89, 0x79, 0xa0, 0x86, 0x6a, 0x29, 0x76, 0x39, 0x8f, 0x89, 0x35, 0x80, 0xe3, 0x37, 0x44, 0x2c,
	0xeb, 0x7b, 0x39, 0x1f, 0x78, 0x7b, 0x68, 0x4c, 0x57, 0x2b, 0x2c, 0x56, 0xb3, 0x7a, 0xf0, 0x38,
	0x97, 0x8a, 0xc7, 0xa8, 0x01, 0x07, 0x71, 0x42, 0x31, 0x49, 0x69, 0x74, 0x60, 0x21, 0xa8, 0xaf,
	0x1a, 0xc3, 0x63, 0xeb, 0xbb, 0x0d, 0x92, 0x0b, 0xe1, 0x0a, 0xb2, 0x5b, 0x90, 0x35, 0x83, 0xc6,
	0x0f, 0x14, 0xf3, 0xe5, 0xa9, 0x03, 0xe6, 0x47, 0x99, 0x50, 0xe3, 0xd6, 0x96, 0x85, 0x9a, 0xc2,
	0x92, 0x1a, 0xf4, 0x04, 0xaa, 0x57, 0x23, 0x87, 0x4f, 0x7c, 0x9f, 0xce, 0xcc, 0x62, 0xdb, 0xe8,
	0x54, 0xed, 0xca, 0xd5, 0xe8, 0x42, 0xc5, 0x72, 0x90, 0x62, 0x3d, 0x88, 0x95, 0xc3, 0x55, 0xbb,
	0x42, 0xb1, 0x1a, 0xc4, 0xd6, 0x2f, 0x05, 0x30, 0xf3, 0x45, 0xeb, 0xad, 0x73, 0x19, 0x28, 0x01,
	0x15, 0x5b, 0x07, 0xe8, 0x12, 0x50, 0x48, 0x31, 0x77, 0x56, 0xfa, 0xcd, 0x2c, 0xb4, 0x8b, 0x9d,
	0x5a, 0xff, 0x59, 0x77, 0xbd, 0x0b, 0xbb, 0x79, 0x1b, 0xb3, 0xeb, 0xe1, 0x1a, 0x2a, 0xeb, 0xcd,
	0xc5, 0x3c, 0x20, 0x92, 0xf6, 0x76, 0x17, 0x35, 0x85, 0xbd, 0x57, 0x10, 0x7a, 0x0e, 0x0f, 0x27,
	0x0c, 0x47, 0x61, 0x4c, 0x03, 0xe2, 0x65, 0x79, 0x7a, 0x43, 0xf5, 0xdb, 0x81, 0x34, 0x79, 0xc5,
	0x92, 0x83, 0xbb, 0x2c, 0x29, 0xad, 0x59, 0xf2, 0x16, 0xcc, 0xb3, 0x80, 0xb8, 0xaa, 0x33, 0xd7,
	0x9b, 0x7f, 0xb3, 0x20, 0xab, 0x95, 0x2d, 0xac, 0x57, 0xf6, 0x09, 0x34, 0xb7, 0x90, 0xf1, 0xd8,
	0xfa, 0xd5, 0x80, 0xe3, 0x65, 0xf0, 0xd2, 0x4d, 0x46, 0x44, 0xc8, 0xd4, 0x9c, 0x85, 0x4c, 0x28,
	0x27, 0x04, 0x13, 0x3a, 0xcd, 0x6a, 0x9f, 0x85, 0xa8, 0x03, 0x75, 0x46, 0x66, 0xc2, 0x09, 0xc8,
	0x94, 0x04, 0xce, 0xd4, 0x0d, 0x26, 0x44, 0xd9, 0x77, 0x68, 0x1f, 0x49, 0xfc, 0x9d, 0x84, 0x3f,
	0x48, 0x14, 0x7d, 0x01, 0x47, 0x4b, 0x99, 0x93, 0x24, 0x48, 0xed, 0x7b, 0xb0, 0xc8, 0x1b, 0x26,
	0x81, 0xf5, 0x97, 0x01, 0xe5, 0xe1, 0xf8, 0x66, 0x4b, 0x07, 0x9a, 0x50, 0x76, 0x71, 0x56, 0x73,
	0x39, 0x39, 0x0b, 0x51, 0x0b, 0x2a, 0x8c, 0xe2, 0x31, 0x73, 0x43, 0x92, 0x35, 0x61, 0x16, 0xcb,
	0x56, 0x52, 0x8b, 0xa6, 0x47, 0x5c, 0x07, 0x12, 0x0d, 0x89, 0xe7, 0x06, 0x69, 0x81, 0x74, 0x20,
	0x2d, 0xbd, 0x26, 0xae, 0xe7, 0xf8, 0x89, 0x64, 0xd2, 0xe5, 0xa9, 0x4a, 0xe4, 0xb5, 0x04, 0x50,
	0x13, 0x2a, 0xbe, 0x3b, 0x26, 0x8e, 0xd4, 0x55, 0xd6, 0x4e, 0xc8, 0x78, 0x48, 0x3d, 0xeb, 0x4f,
	0x03, 0x1a, 0x2b, 0x2e, 0xbb, 0x6c, 0xbc, 0xc5, 0xce, 0xa7, 0x00, 0x9c, 0x30, 0x2f, 0xb5, 0x2b,
	0xad, 0x9b, 0x44, 0xb4, 0x53, 0x08, 0xee, 0x27, 0x2e, 0x1b, 0xa7, 0x3e, 0xaa, 0xff, 0xe8, 0x31,
	0x94, 0xb3, 0x7c, 0xbd, 0x8b, 0x52, 0x9a, 0xfc, 0x02, 0x2a, 0x93, 0xf1, 0x8d, 0x43, 0x99, 0x1f,
	0xa9, 0x9d, 0xd4, 0xfa, 0xcd, 0xcd, 0x73, 0x90, 0x3a, 0x6a, 0x97, 0x27, 0xfa, 0x8f, 0xf5, 0x9b,
	0xb1, 0x71, 0x7f, 0x49, 0xbd, 0x5b, 0xdb, 0x4c, 0xa8, 0xee, 0x70, 0x6e, 0x6f, 0xae, 0xaa, 0x46,
	0x86, 0x1b, 0x5d, 0x58, 0x5c, 0xbf, 0xf0, 0x8e, 0xa1, 0x14, 0xf9, 0x3e, 0x27, 0x22, 0x13, 0xae,
	0x23, 0x55, 0x15, 0x1a, 0x52, 0x91, 0xde, 0xae, 0x3a, 0xb0, 0xfe, 0x2e, 0x6c, 0x5c, 0x64, 0x5a,
	0x18, 0x8f, 0x77, 0xdd, 0xac, 0x03, 0xa8, 0x65, 0x32, 0x39, 0x49, 0x94, 0xce, 0x5a, 0xbf, 0xb3,
	0x69, 0x46, 0x7e, 0xd7, 0xdb, 0xe9, 0x1e, 0x55, 0xc9, 0xbe, 0x87, 0x72, 0x38, 0xd7, 0x9e, 0x16,
	0x15, 0xcd, 0xb3, 0xbb, 0x69, 0xb2, 0x5a, 0xdb, 0xa5, 0x70, 0xae, 0x5a, 0xf7, 0x0c, 0xaa, 0xb2,
	0x6c, 0x4e, 0x40, 0xb9, 0xdc, 0x77, 0xf1, 0x7f, 0x50, 0x54, 0xe4, 0xc4, 0x77, 0x94, 0x0b, 0x74,
	0x02, 0x35, 0x75, 0x62, 0x52, 0xfb, 0xb4, 0x4f, 0x20, 0xa1, 0x73, 0x6d, 0x61, 0x13, 0x2a, 0x53,
	0x4a, 0x6e, 0x1c, 0xcc, 0x84, 0x6a, 0xd5, 0x43, 0xbb, 0x2c, 0xe3, 0x33, 0x26, 0xfa, 0x7f, 0x94,
	0xe0, 0x68, 0x99, 0xfe, 0x4d, 0x84, 0x7e, 0x06, 0x74, 0x71, 0x43, 0x05, 0xbe, 0x5e, 0xbd, 0xfb,
	0x76, 0xc8, 0x22, 0x1f, 0x5b, 0xd6, 0xae, 0x14, 0x1e, 0x5b, 0xf7, 0x50, 0x00, 0x9f, 0xe6, 0x3c,
	0x63, 0x28, 0xa7, 0x00, 0xf9, 0x0f, 0x67, 0xeb, 0xab, 0x3d, 0x33, 0xd5, 0x6a, 0x11, 0x34, 0xf2,
	0x9e, 0x0e, 0xb4, 0x9b, 0x24, 0x7b, 0x17, 0x5b, 0x5f, 0xef, 0x9b, 0xaa, 0x16, 0x4c, 0xe0, 0x51,
	0xee, 0x65, 0x8a, 0x72, 0x68, 0xb6, 0x5d, 0xe1, 0xad, 0xe7, 0x7b, 0xe7, 0x6e, 0xb1, 0x54, 0x76,
	0xc9, 0x1e, 0x96, 0xa6, 0x67, 0x79, 0x0f, 0x4b, 0xb3, 0xc3, 0x65, 0xdd, 0x43, 0xaf, 0xe0, 0xe8,
	0x92, 0x86, 0xc4, 0x76, 0xd9, 0x88, 0xe8, 0xad, 0x35, 0xbb, 0x76, 0xf6, 0x5d, 0xf6, 0xa1, 0xdf,
	0x5d, 0x0c, 0x4a, 0xe6, 0xe3, 0x95, 0xa1, 0x65, 0xd1, 0x6f, 0xe1, 0xe1, 0x22, 0xf3, 0x3c, 0xf1,
	0x48, 0x32, 0xf0, 0xf8, 0x5d, 0x4c, 0xab, 0x43, 0xd9, 0x8c, 0x94, 0xec, 0x47, 0x68, 0xbc, 0xa6,
	0xb3, 0x65, 0xc1, 0x2a, 0x01, 0x7d, 0xb6, 0x32, 0xc9, 0x26, 0x71, 0xe0, 0x62, 0xbd, 0xda, 0xa6,
	0xb8, 0x57, 0x61, 0x2c, 0xe6, 0x9a, 0xaf, 0x85, 0xfe, 0xf9, 0xf7, 0x64, 0xed, 0x5c, 0xbc, 0x7c,
	0xf1, 0x53, 0x7f, 0x14, 0x05, 0x2e, 0x1b, 0x75, 0xbf, 0xed, 0x0b, 0xd1, 0xc5, 0x51, 0xd8, 0x53,
	0x5f, 0xa0, 0x38, 0x0a, 0x7a, 0x9c, 0x24, 0x53, 0x8a, 0x09, 0xdf, 0xf8, 0xf0, 0xbd, 0x2a, 0xa9,
	0x9c, 0x6f, 0xfe, 0x0b, 0x00, 0x00, 0xff, 0xff, 0xd0, 0x1b, 0x31, 0x89, 0x24, 0x0b, 0x00, 0x00,
}
