// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/subscribe_topic.proto

package subscribe_topic // import "golang.52tt.com/protocol/services/ugc/subscribe_topic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Status int32

const (
	Status_NORMAL  Status = 0
	Status_DELETED Status = -1
)

var Status_name = map[int32]string{
	0:  "NORMAL",
	-1: "DELETED",
}
var Status_value = map[string]int32{
	"NORMAL":  0,
	"DELETED": -1,
}

func (x Status) String() string {
	return proto.EnumName(Status_name, int32(x))
}
func (Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{0}
}

type SubscribeItem struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Time                 int64    `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeItem) Reset()         { *m = SubscribeItem{} }
func (m *SubscribeItem) String() string { return proto.CompactTextString(m) }
func (*SubscribeItem) ProtoMessage()    {}
func (*SubscribeItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{0}
}
func (m *SubscribeItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeItem.Unmarshal(m, b)
}
func (m *SubscribeItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeItem.Marshal(b, m, deterministic)
}
func (dst *SubscribeItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeItem.Merge(dst, src)
}
func (m *SubscribeItem) XXX_Size() int {
	return xxx_messageInfo_SubscribeItem.Size(m)
}
func (m *SubscribeItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeItem.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeItem proto.InternalMessageInfo

func (m *SubscribeItem) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *SubscribeItem) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *SubscribeItem) GetTime() int64 {
	if m != nil {
		return m.Time
	}
	return 0
}

type SubscribeReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TopicIds             []string `protobuf:"bytes,2,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeReq) Reset()         { *m = SubscribeReq{} }
func (m *SubscribeReq) String() string { return proto.CompactTextString(m) }
func (*SubscribeReq) ProtoMessage()    {}
func (*SubscribeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{1}
}
func (m *SubscribeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeReq.Unmarshal(m, b)
}
func (m *SubscribeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeReq.Marshal(b, m, deterministic)
}
func (dst *SubscribeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeReq.Merge(dst, src)
}
func (m *SubscribeReq) XXX_Size() int {
	return xxx_messageInfo_SubscribeReq.Size(m)
}
func (m *SubscribeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeReq proto.InternalMessageInfo

func (m *SubscribeReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *SubscribeReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type SubscribeTopicResp struct {
	SuccessIds           []string `protobuf:"bytes,1,rep,name=success_ids,json=successIds,proto3" json:"success_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscribeTopicResp) Reset()         { *m = SubscribeTopicResp{} }
func (m *SubscribeTopicResp) String() string { return proto.CompactTextString(m) }
func (*SubscribeTopicResp) ProtoMessage()    {}
func (*SubscribeTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{2}
}
func (m *SubscribeTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscribeTopicResp.Unmarshal(m, b)
}
func (m *SubscribeTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscribeTopicResp.Marshal(b, m, deterministic)
}
func (dst *SubscribeTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeTopicResp.Merge(dst, src)
}
func (m *SubscribeTopicResp) XXX_Size() int {
	return xxx_messageInfo_SubscribeTopicResp.Size(m)
}
func (m *SubscribeTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeTopicResp proto.InternalMessageInfo

func (m *SubscribeTopicResp) GetSuccessIds() []string {
	if m != nil {
		return m.SuccessIds
	}
	return nil
}

type HadSubscribeTopicResp struct {
	Info                 map[string]bool `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *HadSubscribeTopicResp) Reset()         { *m = HadSubscribeTopicResp{} }
func (m *HadSubscribeTopicResp) String() string { return proto.CompactTextString(m) }
func (*HadSubscribeTopicResp) ProtoMessage()    {}
func (*HadSubscribeTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{3}
}
func (m *HadSubscribeTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HadSubscribeTopicResp.Unmarshal(m, b)
}
func (m *HadSubscribeTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HadSubscribeTopicResp.Marshal(b, m, deterministic)
}
func (dst *HadSubscribeTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HadSubscribeTopicResp.Merge(dst, src)
}
func (m *HadSubscribeTopicResp) XXX_Size() int {
	return xxx_messageInfo_HadSubscribeTopicResp.Size(m)
}
func (m *HadSubscribeTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HadSubscribeTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_HadSubscribeTopicResp proto.InternalMessageInfo

func (m *HadSubscribeTopicResp) GetInfo() map[string]bool {
	if m != nil {
		return m.Info
	}
	return nil
}

type ListSubscribeReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Skip                 int32    `protobuf:"varint,2,opt,name=skip,proto3" json:"skip,omitempty"`
	Limit                int32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListSubscribeReq) Reset()         { *m = ListSubscribeReq{} }
func (m *ListSubscribeReq) String() string { return proto.CompactTextString(m) }
func (*ListSubscribeReq) ProtoMessage()    {}
func (*ListSubscribeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{4}
}
func (m *ListSubscribeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSubscribeReq.Unmarshal(m, b)
}
func (m *ListSubscribeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSubscribeReq.Marshal(b, m, deterministic)
}
func (dst *ListSubscribeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSubscribeReq.Merge(dst, src)
}
func (m *ListSubscribeReq) XXX_Size() int {
	return xxx_messageInfo_ListSubscribeReq.Size(m)
}
func (m *ListSubscribeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSubscribeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListSubscribeReq proto.InternalMessageInfo

func (m *ListSubscribeReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *ListSubscribeReq) GetSkip() int32 {
	if m != nil {
		return m.Skip
	}
	return 0
}

func (m *ListSubscribeReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type ListSubscribeResp struct {
	Items                []*SubscribeItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total                int32            `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ListSubscribeResp) Reset()         { *m = ListSubscribeResp{} }
func (m *ListSubscribeResp) String() string { return proto.CompactTextString(m) }
func (*ListSubscribeResp) ProtoMessage()    {}
func (*ListSubscribeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{5}
}
func (m *ListSubscribeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSubscribeResp.Unmarshal(m, b)
}
func (m *ListSubscribeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSubscribeResp.Marshal(b, m, deterministic)
}
func (dst *ListSubscribeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSubscribeResp.Merge(dst, src)
}
func (m *ListSubscribeResp) XXX_Size() int {
	return xxx_messageInfo_ListSubscribeResp.Size(m)
}
func (m *ListSubscribeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSubscribeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListSubscribeResp proto.InternalMessageInfo

func (m *ListSubscribeResp) GetItems() []*SubscribeItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *ListSubscribeResp) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type FreeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreeResp) Reset()         { *m = FreeResp{} }
func (m *FreeResp) String() string { return proto.CompactTextString(m) }
func (*FreeResp) ProtoMessage()    {}
func (*FreeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{6}
}
func (m *FreeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreeResp.Unmarshal(m, b)
}
func (m *FreeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreeResp.Marshal(b, m, deterministic)
}
func (dst *FreeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreeResp.Merge(dst, src)
}
func (m *FreeResp) XXX_Size() int {
	return xxx_messageInfo_FreeResp.Size(m)
}
func (m *FreeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreeResp.DiscardUnknown(m)
}

var xxx_messageInfo_FreeResp proto.InternalMessageInfo

type ListRecentViewTopicReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Skip                 int32    `protobuf:"varint,2,opt,name=skip,proto3" json:"skip,omitempty"`
	Limit                int32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListRecentViewTopicReq) Reset()         { *m = ListRecentViewTopicReq{} }
func (m *ListRecentViewTopicReq) String() string { return proto.CompactTextString(m) }
func (*ListRecentViewTopicReq) ProtoMessage()    {}
func (*ListRecentViewTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{7}
}
func (m *ListRecentViewTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecentViewTopicReq.Unmarshal(m, b)
}
func (m *ListRecentViewTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecentViewTopicReq.Marshal(b, m, deterministic)
}
func (dst *ListRecentViewTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecentViewTopicReq.Merge(dst, src)
}
func (m *ListRecentViewTopicReq) XXX_Size() int {
	return xxx_messageInfo_ListRecentViewTopicReq.Size(m)
}
func (m *ListRecentViewTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecentViewTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecentViewTopicReq proto.InternalMessageInfo

func (m *ListRecentViewTopicReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *ListRecentViewTopicReq) GetSkip() int32 {
	if m != nil {
		return m.Skip
	}
	return 0
}

func (m *ListRecentViewTopicReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type ListRecentViewTopicRsp struct {
	TopicIds             []string `protobuf:"bytes,1,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListRecentViewTopicRsp) Reset()         { *m = ListRecentViewTopicRsp{} }
func (m *ListRecentViewTopicRsp) String() string { return proto.CompactTextString(m) }
func (*ListRecentViewTopicRsp) ProtoMessage()    {}
func (*ListRecentViewTopicRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{8}
}
func (m *ListRecentViewTopicRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecentViewTopicRsp.Unmarshal(m, b)
}
func (m *ListRecentViewTopicRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecentViewTopicRsp.Marshal(b, m, deterministic)
}
func (dst *ListRecentViewTopicRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecentViewTopicRsp.Merge(dst, src)
}
func (m *ListRecentViewTopicRsp) XXX_Size() int {
	return xxx_messageInfo_ListRecentViewTopicRsp.Size(m)
}
func (m *ListRecentViewTopicRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecentViewTopicRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecentViewTopicRsp proto.InternalMessageInfo

func (m *ListRecentViewTopicRsp) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type ReportRecentViewTopicReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportRecentViewTopicReq) Reset()         { *m = ReportRecentViewTopicReq{} }
func (m *ReportRecentViewTopicReq) String() string { return proto.CompactTextString(m) }
func (*ReportRecentViewTopicReq) ProtoMessage()    {}
func (*ReportRecentViewTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{9}
}
func (m *ReportRecentViewTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportRecentViewTopicReq.Unmarshal(m, b)
}
func (m *ReportRecentViewTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportRecentViewTopicReq.Marshal(b, m, deterministic)
}
func (dst *ReportRecentViewTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportRecentViewTopicReq.Merge(dst, src)
}
func (m *ReportRecentViewTopicReq) XXX_Size() int {
	return xxx_messageInfo_ReportRecentViewTopicReq.Size(m)
}
func (m *ReportRecentViewTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportRecentViewTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportRecentViewTopicReq proto.InternalMessageInfo

func (m *ReportRecentViewTopicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportRecentViewTopicReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type ReportRecentViewTopicRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportRecentViewTopicRsp) Reset()         { *m = ReportRecentViewTopicRsp{} }
func (m *ReportRecentViewTopicRsp) String() string { return proto.CompactTextString(m) }
func (*ReportRecentViewTopicRsp) ProtoMessage()    {}
func (*ReportRecentViewTopicRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{10}
}
func (m *ReportRecentViewTopicRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportRecentViewTopicRsp.Unmarshal(m, b)
}
func (m *ReportRecentViewTopicRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportRecentViewTopicRsp.Marshal(b, m, deterministic)
}
func (dst *ReportRecentViewTopicRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportRecentViewTopicRsp.Merge(dst, src)
}
func (m *ReportRecentViewTopicRsp) XXX_Size() int {
	return xxx_messageInfo_ReportRecentViewTopicRsp.Size(m)
}
func (m *ReportRecentViewTopicRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportRecentViewTopicRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportRecentViewTopicRsp proto.InternalMessageInfo

type AddPostViewedCountByTopicIDsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TopicIds             []string `protobuf:"bytes,2,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPostViewedCountByTopicIDsReq) Reset()         { *m = AddPostViewedCountByTopicIDsReq{} }
func (m *AddPostViewedCountByTopicIDsReq) String() string { return proto.CompactTextString(m) }
func (*AddPostViewedCountByTopicIDsReq) ProtoMessage()    {}
func (*AddPostViewedCountByTopicIDsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{11}
}
func (m *AddPostViewedCountByTopicIDsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostViewedCountByTopicIDsReq.Unmarshal(m, b)
}
func (m *AddPostViewedCountByTopicIDsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostViewedCountByTopicIDsReq.Marshal(b, m, deterministic)
}
func (dst *AddPostViewedCountByTopicIDsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostViewedCountByTopicIDsReq.Merge(dst, src)
}
func (m *AddPostViewedCountByTopicIDsReq) XXX_Size() int {
	return xxx_messageInfo_AddPostViewedCountByTopicIDsReq.Size(m)
}
func (m *AddPostViewedCountByTopicIDsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostViewedCountByTopicIDsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostViewedCountByTopicIDsReq proto.InternalMessageInfo

func (m *AddPostViewedCountByTopicIDsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddPostViewedCountByTopicIDsReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type AddPostViewedCountByTopicIDsRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPostViewedCountByTopicIDsRsp) Reset()         { *m = AddPostViewedCountByTopicIDsRsp{} }
func (m *AddPostViewedCountByTopicIDsRsp) String() string { return proto.CompactTextString(m) }
func (*AddPostViewedCountByTopicIDsRsp) ProtoMessage()    {}
func (*AddPostViewedCountByTopicIDsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{12}
}
func (m *AddPostViewedCountByTopicIDsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPostViewedCountByTopicIDsRsp.Unmarshal(m, b)
}
func (m *AddPostViewedCountByTopicIDsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPostViewedCountByTopicIDsRsp.Marshal(b, m, deterministic)
}
func (dst *AddPostViewedCountByTopicIDsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPostViewedCountByTopicIDsRsp.Merge(dst, src)
}
func (m *AddPostViewedCountByTopicIDsRsp) XXX_Size() int {
	return xxx_messageInfo_AddPostViewedCountByTopicIDsRsp.Size(m)
}
func (m *AddPostViewedCountByTopicIDsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPostViewedCountByTopicIDsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPostViewedCountByTopicIDsRsp proto.InternalMessageInfo

type ReportTopicViewCountReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TopicIds             []string `protobuf:"bytes,2,rep,name=topic_ids,json=topicIds,proto3" json:"topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportTopicViewCountReq) Reset()         { *m = ReportTopicViewCountReq{} }
func (m *ReportTopicViewCountReq) String() string { return proto.CompactTextString(m) }
func (*ReportTopicViewCountReq) ProtoMessage()    {}
func (*ReportTopicViewCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{13}
}
func (m *ReportTopicViewCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportTopicViewCountReq.Unmarshal(m, b)
}
func (m *ReportTopicViewCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportTopicViewCountReq.Marshal(b, m, deterministic)
}
func (dst *ReportTopicViewCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportTopicViewCountReq.Merge(dst, src)
}
func (m *ReportTopicViewCountReq) XXX_Size() int {
	return xxx_messageInfo_ReportTopicViewCountReq.Size(m)
}
func (m *ReportTopicViewCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportTopicViewCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportTopicViewCountReq proto.InternalMessageInfo

func (m *ReportTopicViewCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportTopicViewCountReq) GetTopicIds() []string {
	if m != nil {
		return m.TopicIds
	}
	return nil
}

type ReportTopicViewCountRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportTopicViewCountRsp) Reset()         { *m = ReportTopicViewCountRsp{} }
func (m *ReportTopicViewCountRsp) String() string { return proto.CompactTextString(m) }
func (*ReportTopicViewCountRsp) ProtoMessage()    {}
func (*ReportTopicViewCountRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{14}
}
func (m *ReportTopicViewCountRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportTopicViewCountRsp.Unmarshal(m, b)
}
func (m *ReportTopicViewCountRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportTopicViewCountRsp.Marshal(b, m, deterministic)
}
func (dst *ReportTopicViewCountRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportTopicViewCountRsp.Merge(dst, src)
}
func (m *ReportTopicViewCountRsp) XXX_Size() int {
	return xxx_messageInfo_ReportTopicViewCountRsp.Size(m)
}
func (m *ReportTopicViewCountRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportTopicViewCountRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportTopicViewCountRsp proto.InternalMessageInfo

type GetTopicViewCountReq struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicInitCount       uint32   `protobuf:"varint,2,opt,name=topic_init_count,json=topicInitCount,proto3" json:"topic_init_count,omitempty"`
	TopicInitTime        uint32   `protobuf:"varint,3,opt,name=topic_init_time,json=topicInitTime,proto3" json:"topic_init_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicViewCountReq) Reset()         { *m = GetTopicViewCountReq{} }
func (m *GetTopicViewCountReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicViewCountReq) ProtoMessage()    {}
func (*GetTopicViewCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{15}
}
func (m *GetTopicViewCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicViewCountReq.Unmarshal(m, b)
}
func (m *GetTopicViewCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicViewCountReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicViewCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicViewCountReq.Merge(dst, src)
}
func (m *GetTopicViewCountReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicViewCountReq.Size(m)
}
func (m *GetTopicViewCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicViewCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicViewCountReq proto.InternalMessageInfo

func (m *GetTopicViewCountReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetTopicViewCountReq) GetTopicInitCount() uint32 {
	if m != nil {
		return m.TopicInitCount
	}
	return 0
}

func (m *GetTopicViewCountReq) GetTopicInitTime() uint32 {
	if m != nil {
		return m.TopicInitTime
	}
	return 0
}

type GetTopicViewCountRsp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicViewCountRsp) Reset()         { *m = GetTopicViewCountRsp{} }
func (m *GetTopicViewCountRsp) String() string { return proto.CompactTextString(m) }
func (*GetTopicViewCountRsp) ProtoMessage()    {}
func (*GetTopicViewCountRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{16}
}
func (m *GetTopicViewCountRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicViewCountRsp.Unmarshal(m, b)
}
func (m *GetTopicViewCountRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicViewCountRsp.Marshal(b, m, deterministic)
}
func (dst *GetTopicViewCountRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicViewCountRsp.Merge(dst, src)
}
func (m *GetTopicViewCountRsp) XXX_Size() int {
	return xxx_messageInfo_GetTopicViewCountRsp.Size(m)
}
func (m *GetTopicViewCountRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicViewCountRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicViewCountRsp proto.InternalMessageInfo

func (m *GetTopicViewCountRsp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type BatGetTopicViewCountReq struct {
	TopicIdList          []string `protobuf:"bytes,1,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetTopicViewCountReq) Reset()         { *m = BatGetTopicViewCountReq{} }
func (m *BatGetTopicViewCountReq) String() string { return proto.CompactTextString(m) }
func (*BatGetTopicViewCountReq) ProtoMessage()    {}
func (*BatGetTopicViewCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{17}
}
func (m *BatGetTopicViewCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetTopicViewCountReq.Unmarshal(m, b)
}
func (m *BatGetTopicViewCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetTopicViewCountReq.Marshal(b, m, deterministic)
}
func (dst *BatGetTopicViewCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetTopicViewCountReq.Merge(dst, src)
}
func (m *BatGetTopicViewCountReq) XXX_Size() int {
	return xxx_messageInfo_BatGetTopicViewCountReq.Size(m)
}
func (m *BatGetTopicViewCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetTopicViewCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetTopicViewCountReq proto.InternalMessageInfo

func (m *BatGetTopicViewCountReq) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

type BatGetTopicViewCountRsp struct {
	CountMap             map[string]uint32 `protobuf:"bytes,1,rep,name=count_map,json=countMap,proto3" json:"count_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetTopicViewCountRsp) Reset()         { *m = BatGetTopicViewCountRsp{} }
func (m *BatGetTopicViewCountRsp) String() string { return proto.CompactTextString(m) }
func (*BatGetTopicViewCountRsp) ProtoMessage()    {}
func (*BatGetTopicViewCountRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_subscribe_topic_55437f7fdf035761, []int{18}
}
func (m *BatGetTopicViewCountRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetTopicViewCountRsp.Unmarshal(m, b)
}
func (m *BatGetTopicViewCountRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetTopicViewCountRsp.Marshal(b, m, deterministic)
}
func (dst *BatGetTopicViewCountRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetTopicViewCountRsp.Merge(dst, src)
}
func (m *BatGetTopicViewCountRsp) XXX_Size() int {
	return xxx_messageInfo_BatGetTopicViewCountRsp.Size(m)
}
func (m *BatGetTopicViewCountRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetTopicViewCountRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetTopicViewCountRsp proto.InternalMessageInfo

func (m *BatGetTopicViewCountRsp) GetCountMap() map[string]uint32 {
	if m != nil {
		return m.CountMap
	}
	return nil
}

func init() {
	proto.RegisterType((*SubscribeItem)(nil), "ugc.subscribe_topic.SubscribeItem")
	proto.RegisterType((*SubscribeReq)(nil), "ugc.subscribe_topic.SubscribeReq")
	proto.RegisterType((*SubscribeTopicResp)(nil), "ugc.subscribe_topic.SubscribeTopicResp")
	proto.RegisterType((*HadSubscribeTopicResp)(nil), "ugc.subscribe_topic.HadSubscribeTopicResp")
	proto.RegisterMapType((map[string]bool)(nil), "ugc.subscribe_topic.HadSubscribeTopicResp.InfoEntry")
	proto.RegisterType((*ListSubscribeReq)(nil), "ugc.subscribe_topic.ListSubscribeReq")
	proto.RegisterType((*ListSubscribeResp)(nil), "ugc.subscribe_topic.ListSubscribeResp")
	proto.RegisterType((*FreeResp)(nil), "ugc.subscribe_topic.FreeResp")
	proto.RegisterType((*ListRecentViewTopicReq)(nil), "ugc.subscribe_topic.ListRecentViewTopicReq")
	proto.RegisterType((*ListRecentViewTopicRsp)(nil), "ugc.subscribe_topic.ListRecentViewTopicRsp")
	proto.RegisterType((*ReportRecentViewTopicReq)(nil), "ugc.subscribe_topic.ReportRecentViewTopicReq")
	proto.RegisterType((*ReportRecentViewTopicRsp)(nil), "ugc.subscribe_topic.ReportRecentViewTopicRsp")
	proto.RegisterType((*AddPostViewedCountByTopicIDsReq)(nil), "ugc.subscribe_topic.AddPostViewedCountByTopicIDsReq")
	proto.RegisterType((*AddPostViewedCountByTopicIDsRsp)(nil), "ugc.subscribe_topic.AddPostViewedCountByTopicIDsRsp")
	proto.RegisterType((*ReportTopicViewCountReq)(nil), "ugc.subscribe_topic.ReportTopicViewCountReq")
	proto.RegisterType((*ReportTopicViewCountRsp)(nil), "ugc.subscribe_topic.ReportTopicViewCountRsp")
	proto.RegisterType((*GetTopicViewCountReq)(nil), "ugc.subscribe_topic.GetTopicViewCountReq")
	proto.RegisterType((*GetTopicViewCountRsp)(nil), "ugc.subscribe_topic.GetTopicViewCountRsp")
	proto.RegisterType((*BatGetTopicViewCountReq)(nil), "ugc.subscribe_topic.BatGetTopicViewCountReq")
	proto.RegisterType((*BatGetTopicViewCountRsp)(nil), "ugc.subscribe_topic.BatGetTopicViewCountRsp")
	proto.RegisterMapType((map[string]uint32)(nil), "ugc.subscribe_topic.BatGetTopicViewCountRsp.CountMapEntry")
	proto.RegisterEnum("ugc.subscribe_topic.Status", Status_name, Status_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SubscribeClient is the client API for Subscribe service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SubscribeClient interface {
	SubscribeTopic(ctx context.Context, in *SubscribeReq, opts ...grpc.CallOption) (*SubscribeTopicResp, error)
	UnsubscribeTopic(ctx context.Context, in *SubscribeReq, opts ...grpc.CallOption) (*SubscribeTopicResp, error)
	HadSubscribeTopic(ctx context.Context, in *SubscribeReq, opts ...grpc.CallOption) (*HadSubscribeTopicResp, error)
	ListSubscribeTopic(ctx context.Context, in *ListSubscribeReq, opts ...grpc.CallOption) (*ListSubscribeResp, error)
	// 获取LRU 最近查看列表
	ListRecentViewTopic(ctx context.Context, in *ListRecentViewTopicReq, opts ...grpc.CallOption) (*ListRecentViewTopicRsp, error)
	// 获取话题查看次数
	GetTopicViewCount(ctx context.Context, in *GetTopicViewCountReq, opts ...grpc.CallOption) (*GetTopicViewCountRsp, error)
	// 获取话题查看次数
	BatGetTopicViewCount(ctx context.Context, in *BatGetTopicViewCountReq, opts ...grpc.CallOption) (*BatGetTopicViewCountRsp, error)
	// 上报最近访问
	ReportRecentViewTopic(ctx context.Context, in *ReportRecentViewTopicReq, opts ...grpc.CallOption) (*ReportRecentViewTopicRsp, error)
	// 上报话题页的查看数量
	ReportTopicViewCount(ctx context.Context, in *ReportTopicViewCountReq, opts ...grpc.CallOption) (*ReportTopicViewCountRsp, error)
}

type subscribeClient struct {
	cc *grpc.ClientConn
}

func NewSubscribeClient(cc *grpc.ClientConn) SubscribeClient {
	return &subscribeClient{cc}
}

func (c *subscribeClient) SubscribeTopic(ctx context.Context, in *SubscribeReq, opts ...grpc.CallOption) (*SubscribeTopicResp, error) {
	out := new(SubscribeTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.subscribe_topic.Subscribe/SubscribeTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscribeClient) UnsubscribeTopic(ctx context.Context, in *SubscribeReq, opts ...grpc.CallOption) (*SubscribeTopicResp, error) {
	out := new(SubscribeTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.subscribe_topic.Subscribe/UnsubscribeTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscribeClient) HadSubscribeTopic(ctx context.Context, in *SubscribeReq, opts ...grpc.CallOption) (*HadSubscribeTopicResp, error) {
	out := new(HadSubscribeTopicResp)
	err := c.cc.Invoke(ctx, "/ugc.subscribe_topic.Subscribe/HadSubscribeTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscribeClient) ListSubscribeTopic(ctx context.Context, in *ListSubscribeReq, opts ...grpc.CallOption) (*ListSubscribeResp, error) {
	out := new(ListSubscribeResp)
	err := c.cc.Invoke(ctx, "/ugc.subscribe_topic.Subscribe/ListSubscribeTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscribeClient) ListRecentViewTopic(ctx context.Context, in *ListRecentViewTopicReq, opts ...grpc.CallOption) (*ListRecentViewTopicRsp, error) {
	out := new(ListRecentViewTopicRsp)
	err := c.cc.Invoke(ctx, "/ugc.subscribe_topic.Subscribe/ListRecentViewTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscribeClient) GetTopicViewCount(ctx context.Context, in *GetTopicViewCountReq, opts ...grpc.CallOption) (*GetTopicViewCountRsp, error) {
	out := new(GetTopicViewCountRsp)
	err := c.cc.Invoke(ctx, "/ugc.subscribe_topic.Subscribe/GetTopicViewCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscribeClient) BatGetTopicViewCount(ctx context.Context, in *BatGetTopicViewCountReq, opts ...grpc.CallOption) (*BatGetTopicViewCountRsp, error) {
	out := new(BatGetTopicViewCountRsp)
	err := c.cc.Invoke(ctx, "/ugc.subscribe_topic.Subscribe/BatGetTopicViewCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscribeClient) ReportRecentViewTopic(ctx context.Context, in *ReportRecentViewTopicReq, opts ...grpc.CallOption) (*ReportRecentViewTopicRsp, error) {
	out := new(ReportRecentViewTopicRsp)
	err := c.cc.Invoke(ctx, "/ugc.subscribe_topic.Subscribe/ReportRecentViewTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscribeClient) ReportTopicViewCount(ctx context.Context, in *ReportTopicViewCountReq, opts ...grpc.CallOption) (*ReportTopicViewCountRsp, error) {
	out := new(ReportTopicViewCountRsp)
	err := c.cc.Invoke(ctx, "/ugc.subscribe_topic.Subscribe/ReportTopicViewCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SubscribeServer is the server API for Subscribe service.
type SubscribeServer interface {
	SubscribeTopic(context.Context, *SubscribeReq) (*SubscribeTopicResp, error)
	UnsubscribeTopic(context.Context, *SubscribeReq) (*SubscribeTopicResp, error)
	HadSubscribeTopic(context.Context, *SubscribeReq) (*HadSubscribeTopicResp, error)
	ListSubscribeTopic(context.Context, *ListSubscribeReq) (*ListSubscribeResp, error)
	// 获取LRU 最近查看列表
	ListRecentViewTopic(context.Context, *ListRecentViewTopicReq) (*ListRecentViewTopicRsp, error)
	// 获取话题查看次数
	GetTopicViewCount(context.Context, *GetTopicViewCountReq) (*GetTopicViewCountRsp, error)
	// 获取话题查看次数
	BatGetTopicViewCount(context.Context, *BatGetTopicViewCountReq) (*BatGetTopicViewCountRsp, error)
	// 上报最近访问
	ReportRecentViewTopic(context.Context, *ReportRecentViewTopicReq) (*ReportRecentViewTopicRsp, error)
	// 上报话题页的查看数量
	ReportTopicViewCount(context.Context, *ReportTopicViewCountReq) (*ReportTopicViewCountRsp, error)
}

func RegisterSubscribeServer(s *grpc.Server, srv SubscribeServer) {
	s.RegisterService(&_Subscribe_serviceDesc, srv)
}

func _Subscribe_SubscribeTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeServer).SubscribeTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.subscribe_topic.Subscribe/SubscribeTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeServer).SubscribeTopic(ctx, req.(*SubscribeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subscribe_UnsubscribeTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeServer).UnsubscribeTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.subscribe_topic.Subscribe/UnsubscribeTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeServer).UnsubscribeTopic(ctx, req.(*SubscribeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subscribe_HadSubscribeTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeServer).HadSubscribeTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.subscribe_topic.Subscribe/HadSubscribeTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeServer).HadSubscribeTopic(ctx, req.(*SubscribeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subscribe_ListSubscribeTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSubscribeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeServer).ListSubscribeTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.subscribe_topic.Subscribe/ListSubscribeTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeServer).ListSubscribeTopic(ctx, req.(*ListSubscribeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subscribe_ListRecentViewTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRecentViewTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeServer).ListRecentViewTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.subscribe_topic.Subscribe/ListRecentViewTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeServer).ListRecentViewTopic(ctx, req.(*ListRecentViewTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subscribe_GetTopicViewCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicViewCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeServer).GetTopicViewCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.subscribe_topic.Subscribe/GetTopicViewCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeServer).GetTopicViewCount(ctx, req.(*GetTopicViewCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subscribe_BatGetTopicViewCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetTopicViewCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeServer).BatGetTopicViewCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.subscribe_topic.Subscribe/BatGetTopicViewCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeServer).BatGetTopicViewCount(ctx, req.(*BatGetTopicViewCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subscribe_ReportRecentViewTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportRecentViewTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeServer).ReportRecentViewTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.subscribe_topic.Subscribe/ReportRecentViewTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeServer).ReportRecentViewTopic(ctx, req.(*ReportRecentViewTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subscribe_ReportTopicViewCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportTopicViewCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscribeServer).ReportTopicViewCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.subscribe_topic.Subscribe/ReportTopicViewCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscribeServer).ReportTopicViewCount(ctx, req.(*ReportTopicViewCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Subscribe_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.subscribe_topic.Subscribe",
	HandlerType: (*SubscribeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubscribeTopic",
			Handler:    _Subscribe_SubscribeTopic_Handler,
		},
		{
			MethodName: "UnsubscribeTopic",
			Handler:    _Subscribe_UnsubscribeTopic_Handler,
		},
		{
			MethodName: "HadSubscribeTopic",
			Handler:    _Subscribe_HadSubscribeTopic_Handler,
		},
		{
			MethodName: "ListSubscribeTopic",
			Handler:    _Subscribe_ListSubscribeTopic_Handler,
		},
		{
			MethodName: "ListRecentViewTopic",
			Handler:    _Subscribe_ListRecentViewTopic_Handler,
		},
		{
			MethodName: "GetTopicViewCount",
			Handler:    _Subscribe_GetTopicViewCount_Handler,
		},
		{
			MethodName: "BatGetTopicViewCount",
			Handler:    _Subscribe_BatGetTopicViewCount_Handler,
		},
		{
			MethodName: "ReportRecentViewTopic",
			Handler:    _Subscribe_ReportRecentViewTopic_Handler,
		},
		{
			MethodName: "ReportTopicViewCount",
			Handler:    _Subscribe_ReportTopicViewCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/subscribe_topic.proto",
}

func init() {
	proto.RegisterFile("ugc/subscribe_topic.proto", fileDescriptor_subscribe_topic_55437f7fdf035761)
}

var fileDescriptor_subscribe_topic_55437f7fdf035761 = []byte{
	// 798 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x56, 0xef, 0x4e, 0xdb, 0x48,
	0x10, 0x8f, 0x09, 0xf9, 0x37, 0x9c, 0xb9, 0xb0, 0x84, 0x23, 0xf8, 0x3e, 0x10, 0x56, 0x3a, 0x2e,
	0xc7, 0x71, 0x8e, 0xc4, 0x35, 0x02, 0x51, 0xf5, 0x03, 0x34, 0x29, 0x44, 0x82, 0x16, 0x2d, 0x50,
	0xa4, 0x56, 0x6a, 0x6a, 0xec, 0x25, 0xb2, 0x48, 0x6c, 0x27, 0xbb, 0x06, 0xf1, 0xb9, 0x6f, 0xd1,
	0x97, 0xe8, 0x23, 0xf5, 0x51, 0x5a, 0x79, 0xed, 0x58, 0x24, 0xac, 0x43, 0xa8, 0xda, 0x7c, 0xda,
	0xd9, 0x9d, 0xf9, 0xfd, 0x66, 0x46, 0x33, 0xbf, 0x18, 0x56, 0xfc, 0x8e, 0x59, 0x63, 0xfe, 0x25,
	0x33, 0x07, 0xf6, 0x25, 0x6d, 0x73, 0xd7, 0xb3, 0x4d, 0xdd, 0x1b, 0xb8, 0xdc, 0x45, 0x8b, 0x7e,
	0xc7, 0xd4, 0xc7, 0x9e, 0xf0, 0x05, 0xa8, 0xa7, 0xc3, 0xab, 0x16, 0xa7, 0x3d, 0xb4, 0x0c, 0x39,
	0x9f, 0xd1, 0x41, 0xdb, 0xb6, 0xca, 0x4a, 0x45, 0xa9, 0xaa, 0x24, 0x1b, 0x98, 0x2d, 0x0b, 0xad,
	0x40, 0x5e, 0x84, 0x04, 0x2f, 0x33, 0x15, 0xa5, 0x5a, 0x20, 0x39, 0x61, 0xb7, 0x2c, 0x84, 0x60,
	0x96, 0xdb, 0x3d, 0x5a, 0x4e, 0x57, 0x94, 0x6a, 0x9a, 0x88, 0x33, 0x6e, 0xc0, 0x6f, 0x31, 0x30,
	0xa1, 0xfd, 0x64, 0xdc, 0x3f, 0xa1, 0x30, 0xc4, 0x65, 0xe5, 0x99, 0x4a, 0xba, 0x5a, 0x20, 0xf9,
	0x08, 0x98, 0xe1, 0x3a, 0xa0, 0x18, 0xe5, 0x2c, 0xb8, 0x24, 0x94, 0x79, 0x68, 0x15, 0xe6, 0x98,
	0x6f, 0x9a, 0x94, 0x31, 0x11, 0xa4, 0x88, 0x20, 0x88, 0xae, 0x82, 0xb0, 0xcf, 0x0a, 0x2c, 0x1d,
	0x1a, 0x96, 0x24, 0xf4, 0x10, 0x66, 0x6d, 0xe7, 0xca, 0x15, 0x31, 0x73, 0x5b, 0xcf, 0x74, 0x49,
	0x4f, 0x74, 0x69, 0xa4, 0xde, 0x72, 0xae, 0xdc, 0xa6, 0xc3, 0x07, 0x77, 0x44, 0x20, 0x68, 0xdb,
	0x50, 0x88, 0xaf, 0x50, 0x11, 0xd2, 0xd7, 0xf4, 0x4e, 0x54, 0x56, 0x20, 0xc1, 0x11, 0x95, 0x20,
	0x73, 0x63, 0x74, 0x7d, 0x2a, 0x7a, 0x95, 0x27, 0xa1, 0xb1, 0x3b, 0xb3, 0xa3, 0xe0, 0x73, 0x28,
	0x1e, 0xd9, 0x8c, 0x4f, 0xd7, 0x1d, 0x04, 0xb3, 0xec, 0xda, 0xf6, 0x04, 0x4a, 0x86, 0x88, 0x73,
	0x00, 0xdd, 0xb5, 0x7b, 0x36, 0x17, 0xfd, 0xce, 0x90, 0xd0, 0xc0, 0x26, 0x2c, 0x8c, 0xc1, 0x32,
	0x0f, 0xed, 0x40, 0xc6, 0xe6, 0xb4, 0xc7, 0xa2, 0x7a, 0xb1, 0xb4, 0xde, 0x91, 0x01, 0x20, 0x61,
	0x40, 0x40, 0xc2, 0x5d, 0x6e, 0x74, 0x23, 0xe6, 0xd0, 0xc0, 0x00, 0xf9, 0x57, 0x03, 0x2a, 0xb0,
	0xf1, 0x7b, 0xf8, 0x23, 0x20, 0x24, 0xd4, 0xa4, 0x0e, 0x7f, 0x6b, 0xd3, 0xdb, 0xa8, 0x57, 0x3f,
	0xa5, 0x9a, 0xba, 0x1c, 0x9c, 0x79, 0xa3, 0xf3, 0xa2, 0x8c, 0xcd, 0xcb, 0x01, 0x94, 0x09, 0xf5,
	0xdc, 0x81, 0x2c, 0xab, 0x22, 0xa4, 0xfd, 0x38, 0xa3, 0xe0, 0x38, 0x61, 0xa4, 0xb1, 0x96, 0x04,
	0xc4, 0x3c, 0x7c, 0x02, 0xab, 0x7b, 0x96, 0x75, 0xe2, 0x32, 0x71, 0x4d, 0xad, 0x97, 0xae, 0xef,
	0xf0, 0xfd, 0x3b, 0xf1, 0xde, 0x6a, 0x30, 0x39, 0xd7, 0xc4, 0x31, 0x5f, 0x7b, 0x04, 0x91, 0x79,
	0xf8, 0x10, 0x96, 0xc3, 0x84, 0xc4, 0x65, 0xe0, 0x26, 0x9c, 0x7e, 0x80, 0x6c, 0x25, 0x01, 0x89,
	0x79, 0xf8, 0x93, 0x02, 0xa5, 0x03, 0x2a, 0xa1, 0xb8, 0xdf, 0x29, 0x65, 0x74, 0xf9, 0xab, 0x50,
	0x8c, 0x9e, 0x1c, 0x9b, 0xb7, 0xcd, 0x20, 0x42, 0x34, 0x53, 0x25, 0xf3, 0xa1, 0x8b, 0x63, 0x73,
	0x81, 0x83, 0xd6, 0xe1, 0xf7, 0x7b, 0x9e, 0xb1, 0x62, 0xa8, 0x44, 0x8d, 0x1d, 0xcf, 0x02, 0xe9,
	0xd8, 0x94, 0x25, 0xc1, 0xc4, 0xa4, 0x84, 0xf0, 0x61, 0xa5, 0xa1, 0x81, 0x5f, 0xc0, 0xf2, 0xbe,
	0xc1, 0xa5, 0x59, 0x63, 0x50, 0x87, 0x59, 0xb7, 0xbb, 0x36, 0xe3, 0xd1, 0xb8, 0xcc, 0x45, 0xa9,
	0x07, 0x03, 0x86, 0xbf, 0x28, 0x09, 0xf1, 0xcc, 0x43, 0x17, 0x50, 0x10, 0x1c, 0xed, 0x9e, 0xe1,
	0x45, 0x1b, 0xb4, 0x2b, 0xdd, 0xa0, 0x04, 0x00, 0x5d, 0x1c, 0x8e, 0x0d, 0x2f, 0xd4, 0x8d, 0xbc,
	0x19, 0x99, 0xda, 0x73, 0x50, 0x47, 0x9e, 0x1e, 0xd3, 0x0f, 0xf5, 0x9e, 0x7e, 0x6c, 0x6c, 0x40,
	0xf6, 0x94, 0x1b, 0xdc, 0x67, 0x08, 0x20, 0xfb, 0xfa, 0x0d, 0x39, 0xde, 0x3b, 0x2a, 0xa6, 0x50,
	0x09, 0x72, 0x8d, 0xe6, 0x51, 0xf3, 0xac, 0xd9, 0x28, 0x7e, 0x1b, 0xfe, 0x94, 0xad, 0xaf, 0x39,
	0x28, 0xc4, 0xeb, 0x8d, 0x3e, 0xc0, 0xfc, 0xa8, 0xb0, 0xa1, 0xb5, 0xc9, 0x82, 0x40, 0x68, 0x5f,
	0xfb, 0x7b, 0xb2, 0x4b, 0x2c, 0x90, 0x38, 0x85, 0x3e, 0x42, 0xf1, 0xdc, 0x61, 0xbf, 0x92, 0xc1,
	0x82, 0x85, 0x07, 0xea, 0x3c, 0x0d, 0xc5, 0xc6, 0xf4, 0x42, 0x8f, 0x53, 0x88, 0x02, 0x1a, 0x91,
	0xd2, 0x90, 0xe6, 0x2f, 0x29, 0xc6, 0xb8, 0x94, 0x6b, 0xeb, 0xd3, 0xb8, 0x09, 0x9a, 0x3e, 0x2c,
	0x4a, 0x34, 0x0e, 0xfd, 0x9b, 0x08, 0xf0, 0x50, 0xd4, 0xb4, 0xe9, 0x9d, 0x05, 0xe5, 0x35, 0x2c,
	0x3c, 0x18, 0x54, 0xf4, 0x8f, 0x14, 0x43, 0xb6, 0x51, 0xda, 0xb4, 0xae, 0x82, 0x8c, 0x43, 0x49,
	0xb6, 0x18, 0x68, 0x73, 0xfa, 0x1d, 0xa2, 0x7d, 0x6d, 0xf3, 0x29, 0x1b, 0x87, 0x53, 0xe8, 0x16,
	0x96, 0xa4, 0xca, 0x8d, 0xfe, 0x93, 0x02, 0x25, 0xfd, 0x5d, 0x68, 0x4f, 0x71, 0x1f, 0x96, 0x2b,
	0xd3, 0xd5, 0x84, 0x72, 0x13, 0xc4, 0x5c, 0x7b, 0x82, 0x77, 0xc0, 0xba, 0xbf, 0xfd, 0xae, 0xde,
	0x71, 0xbb, 0x86, 0xd3, 0xd1, 0xeb, 0x5b, 0x9c, 0xeb, 0xa6, 0xdb, 0xab, 0x89, 0xcf, 0x3d, 0xd3,
	0xed, 0xd6, 0x18, 0x1d, 0xdc, 0xd8, 0x26, 0x65, 0x35, 0xc9, 0x47, 0xe1, 0x65, 0x56, 0xb8, 0xfd,
	0xff, 0x3d, 0x00, 0x00, 0xff, 0xff, 0xb1, 0x56, 0x7f, 0x31, 0x32, 0x0a, 0x00, 0x00,
}
