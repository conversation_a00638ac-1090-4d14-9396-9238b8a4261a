// Code generated by protoc-gen-go. DO NOT EDIT.
// source: comment_record.proto

/*
Package comment_record is a generated protocol buffer package.

It is generated from these files:
	comment_record.proto

It has these top-level messages:
	HadCommentStatusReq
	HadCommentStatusResp
	AddCommentItem
	AddCommentRecordReq
	AddCommentRecordResp
	DelCommentRecordReq
	DelCommentRecordResp
*/
package comment_record

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type HadCommentStatusReq struct {
	UserId  uint32   `protobuf:"varint,1,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	PostIds []string `protobuf:"bytes,2,rep,name=post_ids,json=postIds" json:"post_ids,omitempty"`
}

func (m *HadCommentStatusReq) Reset()                    { *m = HadCommentStatusReq{} }
func (m *HadCommentStatusReq) String() string            { return proto.CompactTextString(m) }
func (*HadCommentStatusReq) ProtoMessage()               {}
func (*HadCommentStatusReq) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

func (m *HadCommentStatusReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *HadCommentStatusReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type HadCommentStatusResp struct {
	Status map[string]bool `protobuf:"bytes,1,rep,name=status" json:"status,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
}

func (m *HadCommentStatusResp) Reset()                    { *m = HadCommentStatusResp{} }
func (m *HadCommentStatusResp) String() string            { return proto.CompactTextString(m) }
func (*HadCommentStatusResp) ProtoMessage()               {}
func (*HadCommentStatusResp) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1} }

func (m *HadCommentStatusResp) GetStatus() map[string]bool {
	if m != nil {
		return m.Status
	}
	return nil
}

type AddCommentItem struct {
	Id   string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Time int64  `protobuf:"varint,2,opt,name=time" json:"time,omitempty"`
}

func (m *AddCommentItem) Reset()                    { *m = AddCommentItem{} }
func (m *AddCommentItem) String() string            { return proto.CompactTextString(m) }
func (*AddCommentItem) ProtoMessage()               {}
func (*AddCommentItem) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{2} }

func (m *AddCommentItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *AddCommentItem) GetTime() int64 {
	if m != nil {
		return m.Time
	}
	return 0
}

type AddCommentRecordReq struct {
	UserId  uint32            `protobuf:"varint,1,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	PostId  string            `protobuf:"bytes,2,opt,name=post_id,json=postId" json:"post_id,omitempty"`
	Comment []*AddCommentItem `protobuf:"bytes,3,rep,name=comment" json:"comment,omitempty"`
}

func (m *AddCommentRecordReq) Reset()                    { *m = AddCommentRecordReq{} }
func (m *AddCommentRecordReq) String() string            { return proto.CompactTextString(m) }
func (*AddCommentRecordReq) ProtoMessage()               {}
func (*AddCommentRecordReq) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{3} }

func (m *AddCommentRecordReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddCommentRecordReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *AddCommentRecordReq) GetComment() []*AddCommentItem {
	if m != nil {
		return m.Comment
	}
	return nil
}

type AddCommentRecordResp struct {
}

func (m *AddCommentRecordResp) Reset()                    { *m = AddCommentRecordResp{} }
func (m *AddCommentRecordResp) String() string            { return proto.CompactTextString(m) }
func (*AddCommentRecordResp) ProtoMessage()               {}
func (*AddCommentRecordResp) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{4} }

type DelCommentRecordReq struct {
	UserId     uint32   `protobuf:"varint,1,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	PostId     string   `protobuf:"bytes,2,opt,name=post_id,json=postId" json:"post_id,omitempty"`
	CommentIds []string `protobuf:"bytes,3,rep,name=comment_ids,json=commentIds" json:"comment_ids,omitempty"`
}

func (m *DelCommentRecordReq) Reset()                    { *m = DelCommentRecordReq{} }
func (m *DelCommentRecordReq) String() string            { return proto.CompactTextString(m) }
func (*DelCommentRecordReq) ProtoMessage()               {}
func (*DelCommentRecordReq) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{5} }

func (m *DelCommentRecordReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *DelCommentRecordReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *DelCommentRecordReq) GetCommentIds() []string {
	if m != nil {
		return m.CommentIds
	}
	return nil
}

type DelCommentRecordResp struct {
}

func (m *DelCommentRecordResp) Reset()                    { *m = DelCommentRecordResp{} }
func (m *DelCommentRecordResp) String() string            { return proto.CompactTextString(m) }
func (*DelCommentRecordResp) ProtoMessage()               {}
func (*DelCommentRecordResp) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{6} }

func init() {
	proto.RegisterType((*HadCommentStatusReq)(nil), "ugc.comment_record.HadCommentStatusReq")
	proto.RegisterType((*HadCommentStatusResp)(nil), "ugc.comment_record.HadCommentStatusResp")
	proto.RegisterType((*AddCommentItem)(nil), "ugc.comment_record.AddCommentItem")
	proto.RegisterType((*AddCommentRecordReq)(nil), "ugc.comment_record.AddCommentRecordReq")
	proto.RegisterType((*AddCommentRecordResp)(nil), "ugc.comment_record.AddCommentRecordResp")
	proto.RegisterType((*DelCommentRecordReq)(nil), "ugc.comment_record.DelCommentRecordReq")
	proto.RegisterType((*DelCommentRecordResp)(nil), "ugc.comment_record.DelCommentRecordResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for CommentRecord service

type CommentRecordClient interface {
	HadCommentStatus(ctx context.Context, in *HadCommentStatusReq, opts ...grpc.CallOption) (*HadCommentStatusResp, error)
	AddCommentRecord(ctx context.Context, in *AddCommentRecordReq, opts ...grpc.CallOption) (*AddCommentRecordResp, error)
	DelCommentRecord(ctx context.Context, in *DelCommentRecordReq, opts ...grpc.CallOption) (*DelCommentRecordResp, error)
}

type commentRecordClient struct {
	cc *grpc.ClientConn
}

func NewCommentRecordClient(cc *grpc.ClientConn) CommentRecordClient {
	return &commentRecordClient{cc}
}

func (c *commentRecordClient) HadCommentStatus(ctx context.Context, in *HadCommentStatusReq, opts ...grpc.CallOption) (*HadCommentStatusResp, error) {
	out := new(HadCommentStatusResp)
	err := grpc.Invoke(ctx, "/ugc.comment_record.CommentRecord/HadCommentStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commentRecordClient) AddCommentRecord(ctx context.Context, in *AddCommentRecordReq, opts ...grpc.CallOption) (*AddCommentRecordResp, error) {
	out := new(AddCommentRecordResp)
	err := grpc.Invoke(ctx, "/ugc.comment_record.CommentRecord/AddCommentRecord", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commentRecordClient) DelCommentRecord(ctx context.Context, in *DelCommentRecordReq, opts ...grpc.CallOption) (*DelCommentRecordResp, error) {
	out := new(DelCommentRecordResp)
	err := grpc.Invoke(ctx, "/ugc.comment_record.CommentRecord/DelCommentRecord", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for CommentRecord service

type CommentRecordServer interface {
	HadCommentStatus(context.Context, *HadCommentStatusReq) (*HadCommentStatusResp, error)
	AddCommentRecord(context.Context, *AddCommentRecordReq) (*AddCommentRecordResp, error)
	DelCommentRecord(context.Context, *DelCommentRecordReq) (*DelCommentRecordResp, error)
}

func RegisterCommentRecordServer(s *grpc.Server, srv CommentRecordServer) {
	s.RegisterService(&_CommentRecord_serviceDesc, srv)
}

func _CommentRecord_HadCommentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HadCommentStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommentRecordServer).HadCommentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.comment_record.CommentRecord/HadCommentStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommentRecordServer).HadCommentStatus(ctx, req.(*HadCommentStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommentRecord_AddCommentRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCommentRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommentRecordServer).AddCommentRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.comment_record.CommentRecord/AddCommentRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommentRecordServer).AddCommentRecord(ctx, req.(*AddCommentRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommentRecord_DelCommentRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCommentRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommentRecordServer).DelCommentRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.comment_record.CommentRecord/DelCommentRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommentRecordServer).DelCommentRecord(ctx, req.(*DelCommentRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _CommentRecord_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.comment_record.CommentRecord",
	HandlerType: (*CommentRecordServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HadCommentStatus",
			Handler:    _CommentRecord_HadCommentStatus_Handler,
		},
		{
			MethodName: "AddCommentRecord",
			Handler:    _CommentRecord_AddCommentRecord_Handler,
		},
		{
			MethodName: "DelCommentRecord",
			Handler:    _CommentRecord_DelCommentRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "comment_record.proto",
}

func init() { proto.RegisterFile("comment_record.proto", fileDescriptor0) }

var fileDescriptor0 = []byte{
	// 406 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x53, 0x4d, 0x0f, 0x93, 0x40,
	0x10, 0x2d, 0xa0, 0x50, 0xa6, 0x69, 0xd3, 0x6c, 0x49, 0x8b, 0xbd, 0x48, 0xf6, 0x22, 0x27, 0x48,
	0x6a, 0x35, 0x6a, 0xbc, 0xf8, 0x95, 0x48, 0xe2, 0x69, 0xbd, 0x79, 0x69, 0x90, 0xdd, 0x20, 0x11,
	0x0a, 0x65, 0x97, 0x26, 0xfd, 0x01, 0xfe, 0x0e, 0xff, 0x8f, 0xbf, 0xca, 0xb0, 0xd0, 0x28, 0x65,
	0x63, 0x7a, 0xf0, 0x36, 0x33, 0xf0, 0xe6, 0xbd, 0x79, 0x0f, 0xc0, 0x49, 0xca, 0xa2, 0x60, 0x47,
	0x71, 0xa8, 0x59, 0x52, 0xd6, 0x34, 0xa8, 0xea, 0x52, 0x94, 0x08, 0x35, 0x69, 0x12, 0x0c, 0x9f,
	0xe0, 0x08, 0x56, 0x1f, 0x63, 0xfa, 0xae, 0x1b, 0x7e, 0x16, 0xb1, 0x68, 0x38, 0x61, 0x27, 0xb4,
	0x01, 0xab, 0xe1, 0xac, 0x3e, 0x64, 0xd4, 0xd5, 0x3c, 0xcd, 0x9f, 0x13, 0xb3, 0x6d, 0x23, 0x8a,
	0x1e, 0xc1, 0xb4, 0x2a, 0xb9, 0x38, 0x64, 0x94, 0xbb, 0xba, 0x67, 0xf8, 0x36, 0xb1, 0xda, 0x3e,
	0xa2, 0x1c, 0xff, 0xd4, 0xc0, 0x19, 0xef, 0xe2, 0x15, 0xfa, 0x04, 0x26, 0x97, 0x9d, 0xab, 0x79,
	0x86, 0x3f, 0xdb, 0xed, 0x83, 0xb1, 0x90, 0x40, 0x85, 0x0c, 0xba, 0xf2, 0xc3, 0x51, 0xd4, 0x17,
	0xd2, 0xef, 0xd8, 0xbe, 0x84, 0xd9, 0x5f, 0x63, 0xb4, 0x04, 0xe3, 0x3b, 0xbb, 0x48, 0x95, 0x36,
	0x69, 0x4b, 0xe4, 0xc0, 0xc3, 0x73, 0x9c, 0x37, 0xcc, 0xd5, 0x3d, 0xcd, 0x9f, 0x92, 0xae, 0x79,
	0xa5, 0xbf, 0xd0, 0xf0, 0x1e, 0x16, 0x6f, 0xe8, 0x95, 0x26, 0x12, 0xac, 0x40, 0x0b, 0xd0, 0xfb,
	0x13, 0x6d, 0xa2, 0x67, 0x14, 0x21, 0x78, 0x20, 0xb2, 0xa2, 0x83, 0x1a, 0x44, 0xd6, 0xf8, 0x87,
	0x06, 0xab, 0x3f, 0x30, 0x22, 0xe5, 0xfe, 0xd3, 0xa3, 0x0d, 0x58, 0xbd, 0x47, 0x72, 0x8f, 0x4d,
	0xcc, 0xce, 0x22, 0xf4, 0x1a, 0xac, 0xfe, 0x6a, 0xd7, 0x90, 0x4e, 0x60, 0x95, 0x13, 0x43, 0x89,
	0xe4, 0x0a, 0xc1, 0x6b, 0x70, 0xc6, 0x32, 0x78, 0x85, 0xbf, 0xc1, 0xea, 0x3d, 0xcb, 0xff, 0x83,
	0xbc, 0xc7, 0x30, 0xbb, 0x4a, 0x69, 0xe3, 0x35, 0x64, 0xbc, 0xd0, 0x8f, 0xda, 0x84, 0xd7, 0xe0,
	0x8c, 0x99, 0x78, 0xb5, 0xfb, 0xa5, 0xc3, 0x7c, 0x30, 0x45, 0x29, 0x2c, 0x6f, 0x03, 0x45, 0x4f,
	0xee, 0x8b, 0xfd, 0xb4, 0xf5, 0xef, 0xfd, 0x3e, 0xf0, 0xa4, 0x25, 0xba, 0x35, 0x45, 0x4d, 0xa4,
	0x48, 0x50, 0x4d, 0xa4, 0xf4, 0x58, 0x12, 0xdd, 0xde, 0xae, 0x26, 0x52, 0x64, 0xa1, 0x26, 0x52,
	0x59, 0x89, 0x27, 0x6f, 0x9f, 0x7f, 0xd9, 0xa7, 0x65, 0x1e, 0x1f, 0xd3, 0xe0, 0xd9, 0x4e, 0x88,
	0x16, 0x14, 0xca, 0xdf, 0x37, 0x29, 0xf3, 0x90, 0xb3, 0xfa, 0x9c, 0x25, 0x8c, 0x87, 0x4d, 0x9a,
	0x84, 0xc3, 0x75, 0x5f, 0x4d, 0xf9, 0xd6, 0xd3, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xd5, 0xc5,
	0xfe, 0x53, 0xfc, 0x03, 0x00, 0x00,
}
