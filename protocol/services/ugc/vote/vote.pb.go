// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/vote.proto

package vote // import "golang.52tt.com/protocol/services/ugc/vote"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Option_ContentT int32

const (
	Option_ContentTNone Option_ContentT = 0
	Option_ContentTText Option_ContentT = 1
)

var Option_ContentT_name = map[int32]string{
	0: "ContentTNone",
	1: "ContentTText",
}
var Option_ContentT_value = map[string]int32{
	"ContentTNone": 0,
	"ContentTText": 1,
}

func (x Option_ContentT) String() string {
	return proto.EnumName(Option_ContentT_name, int32(x))
}
func (Option_ContentT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{0, 0}
}

type Vote_ExpiryT int32

const (
	Vote_ExpiryTNever  Vote_ExpiryT = 0
	Vote_ExpiryTCustom Vote_ExpiryT = 1
)

var Vote_ExpiryT_name = map[int32]string{
	0: "ExpiryTNever",
	1: "ExpiryTCustom",
}
var Vote_ExpiryT_value = map[string]int32{
	"ExpiryTNever":  0,
	"ExpiryTCustom": 1,
}

func (x Vote_ExpiryT) String() string {
	return proto.EnumName(Vote_ExpiryT_name, int32(x))
}
func (Vote_ExpiryT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{1, 0}
}

type Vote_VoteT int32

const (
	Vote_VoteTNone Vote_VoteT = 0
	Vote_VoteTText Vote_VoteT = 1
)

var Vote_VoteT_name = map[int32]string{
	0: "VoteTNone",
	1: "VoteTText",
}
var Vote_VoteT_value = map[string]int32{
	"VoteTNone": 0,
	"VoteTText": 1,
}

func (x Vote_VoteT) String() string {
	return proto.EnumName(Vote_VoteT_name, int32(x))
}
func (Vote_VoteT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{1, 1}
}

// 投票选项
type Option struct {
	Id                   string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ContentT             Option_ContentT `protobuf:"varint,2,opt,name=contentT,proto3,enum=ugc.vote.Option_ContentT" json:"contentT,omitempty"`
	Content              string          `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	Votes                uint32          `protobuf:"varint,4,opt,name=votes,proto3" json:"votes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Option) Reset()         { *m = Option{} }
func (m *Option) String() string { return proto.CompactTextString(m) }
func (*Option) ProtoMessage()    {}
func (*Option) Descriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{0}
}
func (m *Option) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Option.Unmarshal(m, b)
}
func (m *Option) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Option.Marshal(b, m, deterministic)
}
func (dst *Option) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Option.Merge(dst, src)
}
func (m *Option) XXX_Size() int {
	return xxx_messageInfo_Option.Size(m)
}
func (m *Option) XXX_DiscardUnknown() {
	xxx_messageInfo_Option.DiscardUnknown(m)
}

var xxx_messageInfo_Option proto.InternalMessageInfo

func (m *Option) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Option) GetContentT() Option_ContentT {
	if m != nil {
		return m.ContentT
	}
	return Option_ContentTNone
}

func (m *Option) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *Option) GetVotes() uint32 {
	if m != nil {
		return m.Votes
	}
	return 0
}

// 投票详情
type Vote struct {
	PostId               string       `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ExpiryT              Vote_ExpiryT `protobuf:"varint,3,opt,name=expiry_t,json=expiryT,proto3,enum=ugc.vote.Vote_ExpiryT" json:"expiry_t,omitempty"`
	ExpiredAt            uint32       `protobuf:"varint,4,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	OptionId             string       `protobuf:"bytes,5,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	Options              []*Option    `protobuf:"bytes,6,rep,name=options,proto3" json:"options,omitempty"`
	Title                string       `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	VoteT                Vote_VoteT   `protobuf:"varint,8,opt,name=vote_t,json=voteT,proto3,enum=ugc.vote.Vote_VoteT" json:"vote_t,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *Vote) Reset()         { *m = Vote{} }
func (m *Vote) String() string { return proto.CompactTextString(m) }
func (*Vote) ProtoMessage()    {}
func (*Vote) Descriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{1}
}
func (m *Vote) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Vote.Unmarshal(m, b)
}
func (m *Vote) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Vote.Marshal(b, m, deterministic)
}
func (dst *Vote) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Vote.Merge(dst, src)
}
func (m *Vote) XXX_Size() int {
	return xxx_messageInfo_Vote.Size(m)
}
func (m *Vote) XXX_DiscardUnknown() {
	xxx_messageInfo_Vote.DiscardUnknown(m)
}

var xxx_messageInfo_Vote proto.InternalMessageInfo

func (m *Vote) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *Vote) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Vote) GetExpiryT() Vote_ExpiryT {
	if m != nil {
		return m.ExpiryT
	}
	return Vote_ExpiryTNever
}

func (m *Vote) GetExpiredAt() uint32 {
	if m != nil {
		return m.ExpiredAt
	}
	return 0
}

func (m *Vote) GetOptionId() string {
	if m != nil {
		return m.OptionId
	}
	return ""
}

func (m *Vote) GetOptions() []*Option {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *Vote) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Vote) GetVoteT() Vote_VoteT {
	if m != nil {
		return m.VoteT
	}
	return Vote_VoteTNone
}

type CreateVoteReq struct {
	Vote                 *Vote    `protobuf:"bytes,1,opt,name=vote,proto3" json:"vote,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateVoteReq) Reset()         { *m = CreateVoteReq{} }
func (m *CreateVoteReq) String() string { return proto.CompactTextString(m) }
func (*CreateVoteReq) ProtoMessage()    {}
func (*CreateVoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{2}
}
func (m *CreateVoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateVoteReq.Unmarshal(m, b)
}
func (m *CreateVoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateVoteReq.Marshal(b, m, deterministic)
}
func (dst *CreateVoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateVoteReq.Merge(dst, src)
}
func (m *CreateVoteReq) XXX_Size() int {
	return xxx_messageInfo_CreateVoteReq.Size(m)
}
func (m *CreateVoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateVoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateVoteReq proto.InternalMessageInfo

func (m *CreateVoteReq) GetVote() *Vote {
	if m != nil {
		return m.Vote
	}
	return nil
}

type CreateVoteRsp struct {
	Vote                 *Vote    `protobuf:"bytes,1,opt,name=vote,proto3" json:"vote,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateVoteRsp) Reset()         { *m = CreateVoteRsp{} }
func (m *CreateVoteRsp) String() string { return proto.CompactTextString(m) }
func (*CreateVoteRsp) ProtoMessage()    {}
func (*CreateVoteRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{3}
}
func (m *CreateVoteRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateVoteRsp.Unmarshal(m, b)
}
func (m *CreateVoteRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateVoteRsp.Marshal(b, m, deterministic)
}
func (dst *CreateVoteRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateVoteRsp.Merge(dst, src)
}
func (m *CreateVoteRsp) XXX_Size() int {
	return xxx_messageInfo_CreateVoteRsp.Size(m)
}
func (m *CreateVoteRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateVoteRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateVoteRsp proto.InternalMessageInfo

func (m *CreateVoteRsp) GetVote() *Vote {
	if m != nil {
		return m.Vote
	}
	return nil
}

type GetVoteListReq struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVoteListReq) Reset()         { *m = GetVoteListReq{} }
func (m *GetVoteListReq) String() string { return proto.CompactTextString(m) }
func (*GetVoteListReq) ProtoMessage()    {}
func (*GetVoteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{4}
}
func (m *GetVoteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoteListReq.Unmarshal(m, b)
}
func (m *GetVoteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoteListReq.Marshal(b, m, deterministic)
}
func (dst *GetVoteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoteListReq.Merge(dst, src)
}
func (m *GetVoteListReq) XXX_Size() int {
	return xxx_messageInfo_GetVoteListReq.Size(m)
}
func (m *GetVoteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoteListReq proto.InternalMessageInfo

func (m *GetVoteListReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func (m *GetVoteListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetVoteListRsp struct {
	Votes                []*Vote  `protobuf:"bytes,1,rep,name=votes,proto3" json:"votes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVoteListRsp) Reset()         { *m = GetVoteListRsp{} }
func (m *GetVoteListRsp) String() string { return proto.CompactTextString(m) }
func (*GetVoteListRsp) ProtoMessage()    {}
func (*GetVoteListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{5}
}
func (m *GetVoteListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoteListRsp.Unmarshal(m, b)
}
func (m *GetVoteListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoteListRsp.Marshal(b, m, deterministic)
}
func (dst *GetVoteListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoteListRsp.Merge(dst, src)
}
func (m *GetVoteListRsp) XXX_Size() int {
	return xxx_messageInfo_GetVoteListRsp.Size(m)
}
func (m *GetVoteListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoteListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoteListRsp proto.InternalMessageInfo

func (m *GetVoteListRsp) GetVotes() []*Vote {
	if m != nil {
		return m.Votes
	}
	return nil
}

type VoteOptionReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	OptionId             string   `protobuf:"bytes,3,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,4,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,5,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VoteOptionReq) Reset()         { *m = VoteOptionReq{} }
func (m *VoteOptionReq) String() string { return proto.CompactTextString(m) }
func (*VoteOptionReq) ProtoMessage()    {}
func (*VoteOptionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{6}
}
func (m *VoteOptionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoteOptionReq.Unmarshal(m, b)
}
func (m *VoteOptionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoteOptionReq.Marshal(b, m, deterministic)
}
func (dst *VoteOptionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoteOptionReq.Merge(dst, src)
}
func (m *VoteOptionReq) XXX_Size() int {
	return xxx_messageInfo_VoteOptionReq.Size(m)
}
func (m *VoteOptionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_VoteOptionReq.DiscardUnknown(m)
}

var xxx_messageInfo_VoteOptionReq proto.InternalMessageInfo

func (m *VoteOptionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VoteOptionReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *VoteOptionReq) GetOptionId() string {
	if m != nil {
		return m.OptionId
	}
	return ""
}

func (m *VoteOptionReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *VoteOptionReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type VoteOptionRsp struct {
	Vote                 *Vote    `protobuf:"bytes,1,opt,name=vote,proto3" json:"vote,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VoteOptionRsp) Reset()         { *m = VoteOptionRsp{} }
func (m *VoteOptionRsp) String() string { return proto.CompactTextString(m) }
func (*VoteOptionRsp) ProtoMessage()    {}
func (*VoteOptionRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{7}
}
func (m *VoteOptionRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoteOptionRsp.Unmarshal(m, b)
}
func (m *VoteOptionRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoteOptionRsp.Marshal(b, m, deterministic)
}
func (dst *VoteOptionRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoteOptionRsp.Merge(dst, src)
}
func (m *VoteOptionRsp) XXX_Size() int {
	return xxx_messageInfo_VoteOptionRsp.Size(m)
}
func (m *VoteOptionRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_VoteOptionRsp.DiscardUnknown(m)
}

var xxx_messageInfo_VoteOptionRsp proto.InternalMessageInfo

func (m *VoteOptionRsp) GetVote() *Vote {
	if m != nil {
		return m.Vote
	}
	return nil
}

type GetVoteReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVoteReq) Reset()         { *m = GetVoteReq{} }
func (m *GetVoteReq) String() string { return proto.CompactTextString(m) }
func (*GetVoteReq) ProtoMessage()    {}
func (*GetVoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{8}
}
func (m *GetVoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoteReq.Unmarshal(m, b)
}
func (m *GetVoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoteReq.Marshal(b, m, deterministic)
}
func (dst *GetVoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoteReq.Merge(dst, src)
}
func (m *GetVoteReq) XXX_Size() int {
	return xxx_messageInfo_GetVoteReq.Size(m)
}
func (m *GetVoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoteReq proto.InternalMessageInfo

func (m *GetVoteReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetVoteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetVoteRsp struct {
	Vote                 *Vote    `protobuf:"bytes,1,opt,name=vote,proto3" json:"vote,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVoteRsp) Reset()         { *m = GetVoteRsp{} }
func (m *GetVoteRsp) String() string { return proto.CompactTextString(m) }
func (*GetVoteRsp) ProtoMessage()    {}
func (*GetVoteRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_vote_cef44691e4d7f273, []int{9}
}
func (m *GetVoteRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoteRsp.Unmarshal(m, b)
}
func (m *GetVoteRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoteRsp.Marshal(b, m, deterministic)
}
func (dst *GetVoteRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoteRsp.Merge(dst, src)
}
func (m *GetVoteRsp) XXX_Size() int {
	return xxx_messageInfo_GetVoteRsp.Size(m)
}
func (m *GetVoteRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoteRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoteRsp proto.InternalMessageInfo

func (m *GetVoteRsp) GetVote() *Vote {
	if m != nil {
		return m.Vote
	}
	return nil
}

func init() {
	proto.RegisterType((*Option)(nil), "ugc.vote.Option")
	proto.RegisterType((*Vote)(nil), "ugc.vote.Vote")
	proto.RegisterType((*CreateVoteReq)(nil), "ugc.vote.CreateVoteReq")
	proto.RegisterType((*CreateVoteRsp)(nil), "ugc.vote.CreateVoteRsp")
	proto.RegisterType((*GetVoteListReq)(nil), "ugc.vote.GetVoteListReq")
	proto.RegisterType((*GetVoteListRsp)(nil), "ugc.vote.GetVoteListRsp")
	proto.RegisterType((*VoteOptionReq)(nil), "ugc.vote.VoteOptionReq")
	proto.RegisterType((*VoteOptionRsp)(nil), "ugc.vote.VoteOptionRsp")
	proto.RegisterType((*GetVoteReq)(nil), "ugc.vote.GetVoteReq")
	proto.RegisterType((*GetVoteRsp)(nil), "ugc.vote.GetVoteRsp")
	proto.RegisterEnum("ugc.vote.Option_ContentT", Option_ContentT_name, Option_ContentT_value)
	proto.RegisterEnum("ugc.vote.Vote_ExpiryT", Vote_ExpiryT_name, Vote_ExpiryT_value)
	proto.RegisterEnum("ugc.vote.Vote_VoteT", Vote_VoteT_name, Vote_VoteT_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UgcVoteClient is the client API for UgcVote service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UgcVoteClient interface {
	// 新增投票
	CreateVote(ctx context.Context, in *CreateVoteReq, opts ...grpc.CallOption) (*CreateVoteRsp, error)
	// 根据帖子id批量获取投票及选项
	GetVoteList(ctx context.Context, in *GetVoteListReq, opts ...grpc.CallOption) (*GetVoteListRsp, error)
	// 给帖子选项投票
	VoteOption(ctx context.Context, in *VoteOptionReq, opts ...grpc.CallOption) (*VoteOptionRsp, error)
	// 获取帖子投票信息
	GetVote(ctx context.Context, in *GetVoteReq, opts ...grpc.CallOption) (*GetVoteRsp, error)
}

type ugcVoteClient struct {
	cc *grpc.ClientConn
}

func NewUgcVoteClient(cc *grpc.ClientConn) UgcVoteClient {
	return &ugcVoteClient{cc}
}

func (c *ugcVoteClient) CreateVote(ctx context.Context, in *CreateVoteReq, opts ...grpc.CallOption) (*CreateVoteRsp, error) {
	out := new(CreateVoteRsp)
	err := c.cc.Invoke(ctx, "/ugc.vote.UgcVote/CreateVote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcVoteClient) GetVoteList(ctx context.Context, in *GetVoteListReq, opts ...grpc.CallOption) (*GetVoteListRsp, error) {
	out := new(GetVoteListRsp)
	err := c.cc.Invoke(ctx, "/ugc.vote.UgcVote/GetVoteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcVoteClient) VoteOption(ctx context.Context, in *VoteOptionReq, opts ...grpc.CallOption) (*VoteOptionRsp, error) {
	out := new(VoteOptionRsp)
	err := c.cc.Invoke(ctx, "/ugc.vote.UgcVote/VoteOption", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcVoteClient) GetVote(ctx context.Context, in *GetVoteReq, opts ...grpc.CallOption) (*GetVoteRsp, error) {
	out := new(GetVoteRsp)
	err := c.cc.Invoke(ctx, "/ugc.vote.UgcVote/GetVote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UgcVoteServer is the server API for UgcVote service.
type UgcVoteServer interface {
	// 新增投票
	CreateVote(context.Context, *CreateVoteReq) (*CreateVoteRsp, error)
	// 根据帖子id批量获取投票及选项
	GetVoteList(context.Context, *GetVoteListReq) (*GetVoteListRsp, error)
	// 给帖子选项投票
	VoteOption(context.Context, *VoteOptionReq) (*VoteOptionRsp, error)
	// 获取帖子投票信息
	GetVote(context.Context, *GetVoteReq) (*GetVoteRsp, error)
}

func RegisterUgcVoteServer(s *grpc.Server, srv UgcVoteServer) {
	s.RegisterService(&_UgcVote_serviceDesc, srv)
}

func _UgcVote_CreateVote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcVoteServer).CreateVote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.vote.UgcVote/CreateVote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcVoteServer).CreateVote(ctx, req.(*CreateVoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcVote_GetVoteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcVoteServer).GetVoteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.vote.UgcVote/GetVoteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcVoteServer).GetVoteList(ctx, req.(*GetVoteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcVote_VoteOption_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VoteOptionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcVoteServer).VoteOption(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.vote.UgcVote/VoteOption",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcVoteServer).VoteOption(ctx, req.(*VoteOptionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcVote_GetVote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcVoteServer).GetVote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.vote.UgcVote/GetVote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcVoteServer).GetVote(ctx, req.(*GetVoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UgcVote_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.vote.UgcVote",
	HandlerType: (*UgcVoteServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateVote",
			Handler:    _UgcVote_CreateVote_Handler,
		},
		{
			MethodName: "GetVoteList",
			Handler:    _UgcVote_GetVoteList_Handler,
		},
		{
			MethodName: "VoteOption",
			Handler:    _UgcVote_VoteOption_Handler,
		},
		{
			MethodName: "GetVote",
			Handler:    _UgcVote_GetVote_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/vote.proto",
}

func init() { proto.RegisterFile("ugc/vote.proto", fileDescriptor_vote_cef44691e4d7f273) }

var fileDescriptor_vote_cef44691e4d7f273 = []byte{
	// 615 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x54, 0xdd, 0x8a, 0xd3, 0x40,
	0x14, 0x6e, 0xd2, 0x9f, 0xa4, 0xa7, 0x26, 0xc4, 0xa1, 0xb8, 0xd9, 0x2e, 0x62, 0x09, 0x0a, 0x65,
	0x95, 0x54, 0xb3, 0xac, 0x7b, 0x25, 0xac, 0x16, 0x91, 0x82, 0xac, 0x10, 0xa2, 0x17, 0xde, 0x94,
	0x9a, 0x0c, 0x25, 0xd8, 0x66, 0xb2, 0x99, 0x69, 0xd9, 0x3e, 0x83, 0xd7, 0x3e, 0x87, 0x6f, 0xe5,
	0x73, 0xc8, 0xfc, 0x64, 0xd3, 0x74, 0x2b, 0xf4, 0x26, 0x9c, 0xbf, 0xef, 0x9c, 0x93, 0xef, 0x7c,
	0x09, 0xd8, 0xeb, 0x45, 0x3c, 0xde, 0x10, 0x86, 0xfd, 0xbc, 0x20, 0x8c, 0x20, 0x73, 0xbd, 0x88,
	0x7d, 0xee, 0x7b, 0x7f, 0x34, 0xe8, 0x7c, 0xc9, 0x59, 0x4a, 0x32, 0x64, 0x83, 0x9e, 0x26, 0xae,
	0x36, 0xd4, 0x46, 0xdd, 0x50, 0x4f, 0x13, 0x74, 0x09, 0x66, 0x4c, 0x32, 0x86, 0x33, 0x16, 0xb9,
	0xfa, 0x50, 0x1b, 0xd9, 0xc1, 0xa9, 0x5f, 0xe2, 0x7c, 0x89, 0xf1, 0x27, 0xaa, 0x20, 0xbc, 0x2f,
	0x45, 0x2e, 0x18, 0xca, 0x76, 0x9b, 0xa2, 0x57, 0xe9, 0xa2, 0x3e, 0xb4, 0x39, 0x96, 0xba, 0xad,
	0xa1, 0x36, 0xb2, 0x42, 0xe9, 0x78, 0x3e, 0x98, 0x65, 0x17, 0xe4, 0xc0, 0xa3, 0xd2, 0xbe, 0x21,
	0x19, 0x76, 0x1a, 0xbb, 0x91, 0x08, 0xdf, 0x31, 0x47, 0xf3, 0xfe, 0xea, 0xd0, 0xfa, 0x46, 0x18,
	0x46, 0x27, 0x60, 0xe4, 0x84, 0xb2, 0xd9, 0xfd, 0xd2, 0x1d, 0xee, 0x4e, 0x13, 0xe4, 0x40, 0x73,
	0x9d, 0x26, 0x62, 0x67, 0x2b, 0xe4, 0x26, 0x7a, 0x03, 0x26, 0xbe, 0xcb, 0xd3, 0x62, 0x3b, 0x93,
	0x4b, 0xd9, 0xc1, 0x93, 0xea, 0x55, 0x78, 0x33, 0xff, 0xa3, 0x48, 0x47, 0xa1, 0x21, 0xeb, 0x22,
	0xf4, 0x14, 0x40, 0x98, 0x38, 0x99, 0xcd, 0x99, 0xda, 0xb8, 0xab, 0x22, 0xef, 0x19, 0x3a, 0x83,
	0x2e, 0x11, 0x14, 0xf0, 0xf1, 0x6d, 0x31, 0xde, 0x94, 0x81, 0x69, 0x82, 0xce, 0xc1, 0x90, 0x36,
	0x75, 0x3b, 0xc3, 0xe6, 0xa8, 0x17, 0x38, 0xfb, 0xc4, 0x85, 0x65, 0x01, 0x27, 0x85, 0xa5, 0x6c,
	0x89, 0x5d, 0x43, 0x34, 0x91, 0x0e, 0x7a, 0x09, 0x1d, 0x5e, 0x3d, 0x63, 0xae, 0x29, 0xd6, 0xed,
	0xef, 0xad, 0xcb, 0x1f, 0x91, 0x64, 0x30, 0xf2, 0x7c, 0x30, 0xd4, 0xfa, 0x9c, 0x2e, 0x65, 0xde,
	0xe0, 0x0d, 0x2e, 0x9c, 0x06, 0x7a, 0x0c, 0x96, 0x8a, 0x4c, 0xd6, 0x94, 0x91, 0x95, 0xa3, 0x79,
	0x2f, 0xa0, 0x2d, 0xf0, 0xc8, 0x82, 0xae, 0x30, 0x14, 0xd7, 0xa5, 0xab, 0x88, 0xbe, 0x00, 0x6b,
	0x52, 0xe0, 0x39, 0xc3, 0x3c, 0x18, 0xe2, 0x5b, 0xe4, 0x41, 0x8b, 0x0f, 0x14, 0x6c, 0xf7, 0x02,
	0xbb, 0xbe, 0x52, 0x28, 0x72, 0x7b, 0x20, 0x9a, 0x1f, 0x05, 0x7a, 0x07, 0xf6, 0x27, 0xcc, 0x78,
	0xe0, 0x73, 0x4a, 0x19, 0x1f, 0x75, 0x0a, 0xa6, 0xba, 0x2d, 0x75, 0xb5, 0x61, 0x93, 0xab, 0x48,
	0x1e, 0x97, 0x3e, 0xbc, 0xae, 0xf7, 0xb6, 0x0e, 0xa7, 0x39, 0x7a, 0x5e, 0x2a, 0x4d, 0x13, 0xf4,
	0xef, 0x4f, 0x55, 0xca, 0xfb, 0xad, 0x81, 0xc5, 0x7d, 0x75, 0x12, 0x7c, 0x5b, 0xf6, 0xd6, 0x2a,
	0xe5, 0xec, 0x88, 0x4c, 0xaf, 0x89, 0xac, 0x26, 0x80, 0xe6, 0x9e, 0x00, 0xce, 0xa0, 0xbb, 0x9a,
	0x17, 0x3f, 0xb1, 0xc0, 0x49, 0xed, 0x98, 0x32, 0x30, 0x4d, 0xd0, 0x33, 0xe8, 0xc5, 0xcb, 0x14,
	0x67, 0x6c, 0xc6, 0xb6, 0x39, 0x16, 0xe2, 0xb1, 0x42, 0x90, 0xa1, 0x68, 0x9b, 0x0b, 0x0e, 0x77,
	0xd6, 0x3a, 0x92, 0xc3, 0x2b, 0x00, 0x45, 0x02, 0x7f, 0x91, 0xe3, 0xbf, 0x0d, 0xef, 0x75, 0x05,
	0x3c, 0x6e, 0x54, 0xf0, 0x4b, 0x07, 0xe3, 0xeb, 0x22, 0x16, 0x1f, 0xe1, 0x35, 0x40, 0x75, 0x6f,
	0x74, 0x52, 0xd5, 0xd7, 0xa4, 0x33, 0x38, 0x9c, 0xa0, 0xb9, 0xd7, 0x40, 0x13, 0xe8, 0xed, 0x5c,
	0x0f, 0xb9, 0x55, 0x65, 0x5d, 0x13, 0x83, 0xff, 0x64, 0x44, 0x93, 0x6b, 0x80, 0x8a, 0xb2, 0xdd,
	0x35, 0x6a, 0xf7, 0x1d, 0x1c, 0x4e, 0x88, 0x0e, 0x57, 0x60, 0xa8, 0xae, 0xa8, 0xff, 0x60, 0x10,
	0xc7, 0x1e, 0x88, 0x72, 0xe0, 0x87, 0x57, 0xdf, 0xcf, 0x17, 0x64, 0x39, 0xcf, 0x16, 0xfe, 0x65,
	0xc0, 0x98, 0x1f, 0x93, 0xd5, 0x58, 0xfc, 0x64, 0x63, 0xb2, 0x1c, 0x53, 0x5c, 0x6c, 0xd2, 0x18,
	0xd3, 0x71, 0xf9, 0xff, 0xfd, 0xd1, 0x11, 0xb9, 0x8b, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xce,
	0xf2, 0x72, 0xe6, 0x92, 0x05, 0x00, 0x00,
}
