// Code generated by protoc-gen-go. DO NOT EDIT.
// source: robot-commend/robot-commend.proto

package robotcommend // import "golang.52tt.com/protocol/services/ugc/robotcommend"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RobotCommendReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RobotCommendReq) Reset()         { *m = RobotCommendReq{} }
func (m *RobotCommendReq) String() string { return proto.CompactTextString(m) }
func (*RobotCommendReq) ProtoMessage()    {}
func (*RobotCommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_robot_commend_888a037fc59bcfae, []int{0}
}
func (m *RobotCommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RobotCommendReq.Unmarshal(m, b)
}
func (m *RobotCommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RobotCommendReq.Marshal(b, m, deterministic)
}
func (dst *RobotCommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RobotCommendReq.Merge(dst, src)
}
func (m *RobotCommendReq) XXX_Size() int {
	return xxx_messageInfo_RobotCommendReq.Size(m)
}
func (m *RobotCommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RobotCommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_RobotCommendReq proto.InternalMessageInfo

type RobotCommendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RobotCommendResp) Reset()         { *m = RobotCommendResp{} }
func (m *RobotCommendResp) String() string { return proto.CompactTextString(m) }
func (*RobotCommendResp) ProtoMessage()    {}
func (*RobotCommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_robot_commend_888a037fc59bcfae, []int{1}
}
func (m *RobotCommendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RobotCommendResp.Unmarshal(m, b)
}
func (m *RobotCommendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RobotCommendResp.Marshal(b, m, deterministic)
}
func (dst *RobotCommendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RobotCommendResp.Merge(dst, src)
}
func (m *RobotCommendResp) XXX_Size() int {
	return xxx_messageInfo_RobotCommendResp.Size(m)
}
func (m *RobotCommendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RobotCommendResp.DiscardUnknown(m)
}

var xxx_messageInfo_RobotCommendResp proto.InternalMessageInfo

type AddRobotCommendsReq struct {
	Commends             [][]byte `protobuf:"bytes,1,rep,name=commends,proto3" json:"commends,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRobotCommendsReq) Reset()         { *m = AddRobotCommendsReq{} }
func (m *AddRobotCommendsReq) String() string { return proto.CompactTextString(m) }
func (*AddRobotCommendsReq) ProtoMessage()    {}
func (*AddRobotCommendsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_robot_commend_888a037fc59bcfae, []int{2}
}
func (m *AddRobotCommendsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRobotCommendsReq.Unmarshal(m, b)
}
func (m *AddRobotCommendsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRobotCommendsReq.Marshal(b, m, deterministic)
}
func (dst *AddRobotCommendsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRobotCommendsReq.Merge(dst, src)
}
func (m *AddRobotCommendsReq) XXX_Size() int {
	return xxx_messageInfo_AddRobotCommendsReq.Size(m)
}
func (m *AddRobotCommendsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRobotCommendsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddRobotCommendsReq proto.InternalMessageInfo

func (m *AddRobotCommendsReq) GetCommends() [][]byte {
	if m != nil {
		return m.Commends
	}
	return nil
}

type AddRobotCommendsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRobotCommendsResp) Reset()         { *m = AddRobotCommendsResp{} }
func (m *AddRobotCommendsResp) String() string { return proto.CompactTextString(m) }
func (*AddRobotCommendsResp) ProtoMessage()    {}
func (*AddRobotCommendsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_robot_commend_888a037fc59bcfae, []int{3}
}
func (m *AddRobotCommendsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRobotCommendsResp.Unmarshal(m, b)
}
func (m *AddRobotCommendsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRobotCommendsResp.Marshal(b, m, deterministic)
}
func (dst *AddRobotCommendsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRobotCommendsResp.Merge(dst, src)
}
func (m *AddRobotCommendsResp) XXX_Size() int {
	return xxx_messageInfo_AddRobotCommendsResp.Size(m)
}
func (m *AddRobotCommendsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRobotCommendsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddRobotCommendsResp proto.InternalMessageInfo

type DelRobotCommendReq struct {
	Commend              []byte   `protobuf:"bytes,1,opt,name=commend,proto3" json:"commend,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelRobotCommendReq) Reset()         { *m = DelRobotCommendReq{} }
func (m *DelRobotCommendReq) String() string { return proto.CompactTextString(m) }
func (*DelRobotCommendReq) ProtoMessage()    {}
func (*DelRobotCommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_robot_commend_888a037fc59bcfae, []int{4}
}
func (m *DelRobotCommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelRobotCommendReq.Unmarshal(m, b)
}
func (m *DelRobotCommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelRobotCommendReq.Marshal(b, m, deterministic)
}
func (dst *DelRobotCommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelRobotCommendReq.Merge(dst, src)
}
func (m *DelRobotCommendReq) XXX_Size() int {
	return xxx_messageInfo_DelRobotCommendReq.Size(m)
}
func (m *DelRobotCommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelRobotCommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelRobotCommendReq proto.InternalMessageInfo

func (m *DelRobotCommendReq) GetCommend() []byte {
	if m != nil {
		return m.Commend
	}
	return nil
}

type DelRobotCommendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelRobotCommendResp) Reset()         { *m = DelRobotCommendResp{} }
func (m *DelRobotCommendResp) String() string { return proto.CompactTextString(m) }
func (*DelRobotCommendResp) ProtoMessage()    {}
func (*DelRobotCommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_robot_commend_888a037fc59bcfae, []int{5}
}
func (m *DelRobotCommendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelRobotCommendResp.Unmarshal(m, b)
}
func (m *DelRobotCommendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelRobotCommendResp.Marshal(b, m, deterministic)
}
func (dst *DelRobotCommendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelRobotCommendResp.Merge(dst, src)
}
func (m *DelRobotCommendResp) XXX_Size() int {
	return xxx_messageInfo_DelRobotCommendResp.Size(m)
}
func (m *DelRobotCommendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelRobotCommendResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelRobotCommendResp proto.InternalMessageInfo

type GetRobotCommendsReq struct {
	Off                  uint32   `protobuf:"varint,1,opt,name=off,proto3" json:"off,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRobotCommendsReq) Reset()         { *m = GetRobotCommendsReq{} }
func (m *GetRobotCommendsReq) String() string { return proto.CompactTextString(m) }
func (*GetRobotCommendsReq) ProtoMessage()    {}
func (*GetRobotCommendsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_robot_commend_888a037fc59bcfae, []int{6}
}
func (m *GetRobotCommendsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRobotCommendsReq.Unmarshal(m, b)
}
func (m *GetRobotCommendsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRobotCommendsReq.Marshal(b, m, deterministic)
}
func (dst *GetRobotCommendsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRobotCommendsReq.Merge(dst, src)
}
func (m *GetRobotCommendsReq) XXX_Size() int {
	return xxx_messageInfo_GetRobotCommendsReq.Size(m)
}
func (m *GetRobotCommendsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRobotCommendsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRobotCommendsReq proto.InternalMessageInfo

func (m *GetRobotCommendsReq) GetOff() uint32 {
	if m != nil {
		return m.Off
	}
	return 0
}

func (m *GetRobotCommendsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetRobotCommendsResp struct {
	Commends             [][]byte `protobuf:"bytes,1,rep,name=commends,proto3" json:"commends,omitempty"`
	Total                uint32   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRobotCommendsResp) Reset()         { *m = GetRobotCommendsResp{} }
func (m *GetRobotCommendsResp) String() string { return proto.CompactTextString(m) }
func (*GetRobotCommendsResp) ProtoMessage()    {}
func (*GetRobotCommendsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_robot_commend_888a037fc59bcfae, []int{7}
}
func (m *GetRobotCommendsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRobotCommendsResp.Unmarshal(m, b)
}
func (m *GetRobotCommendsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRobotCommendsResp.Marshal(b, m, deterministic)
}
func (dst *GetRobotCommendsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRobotCommendsResp.Merge(dst, src)
}
func (m *GetRobotCommendsResp) XXX_Size() int {
	return xxx_messageInfo_GetRobotCommendsResp.Size(m)
}
func (m *GetRobotCommendsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRobotCommendsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRobotCommendsResp proto.InternalMessageInfo

func (m *GetRobotCommendsResp) GetCommends() [][]byte {
	if m != nil {
		return m.Commends
	}
	return nil
}

func (m *GetRobotCommendsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func init() {
	proto.RegisterType((*RobotCommendReq)(nil), "robotcommend.RobotCommendReq")
	proto.RegisterType((*RobotCommendResp)(nil), "robotcommend.RobotCommendResp")
	proto.RegisterType((*AddRobotCommendsReq)(nil), "robotcommend.AddRobotCommendsReq")
	proto.RegisterType((*AddRobotCommendsResp)(nil), "robotcommend.AddRobotCommendsResp")
	proto.RegisterType((*DelRobotCommendReq)(nil), "robotcommend.DelRobotCommendReq")
	proto.RegisterType((*DelRobotCommendResp)(nil), "robotcommend.DelRobotCommendResp")
	proto.RegisterType((*GetRobotCommendsReq)(nil), "robotcommend.GetRobotCommendsReq")
	proto.RegisterType((*GetRobotCommendsResp)(nil), "robotcommend.GetRobotCommendsResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RobotCommendClient is the client API for RobotCommend service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RobotCommendClient interface {
	GetHello(ctx context.Context, in *RobotCommendReq, opts ...grpc.CallOption) (*RobotCommendResp, error)
	AddRobotCommends(ctx context.Context, in *AddRobotCommendsReq, opts ...grpc.CallOption) (*AddRobotCommendsResp, error)
	DelRobotCommend(ctx context.Context, in *DelRobotCommendReq, opts ...grpc.CallOption) (*DelRobotCommendResp, error)
	GetRobotCommends(ctx context.Context, in *GetRobotCommendsReq, opts ...grpc.CallOption) (*GetRobotCommendsResp, error)
}

type robotCommendClient struct {
	cc *grpc.ClientConn
}

func NewRobotCommendClient(cc *grpc.ClientConn) RobotCommendClient {
	return &robotCommendClient{cc}
}

func (c *robotCommendClient) GetHello(ctx context.Context, in *RobotCommendReq, opts ...grpc.CallOption) (*RobotCommendResp, error) {
	out := new(RobotCommendResp)
	err := c.cc.Invoke(ctx, "/robotcommend.RobotCommend/GetHello", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *robotCommendClient) AddRobotCommends(ctx context.Context, in *AddRobotCommendsReq, opts ...grpc.CallOption) (*AddRobotCommendsResp, error) {
	out := new(AddRobotCommendsResp)
	err := c.cc.Invoke(ctx, "/robotcommend.RobotCommend/AddRobotCommends", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *robotCommendClient) DelRobotCommend(ctx context.Context, in *DelRobotCommendReq, opts ...grpc.CallOption) (*DelRobotCommendResp, error) {
	out := new(DelRobotCommendResp)
	err := c.cc.Invoke(ctx, "/robotcommend.RobotCommend/DelRobotCommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *robotCommendClient) GetRobotCommends(ctx context.Context, in *GetRobotCommendsReq, opts ...grpc.CallOption) (*GetRobotCommendsResp, error) {
	out := new(GetRobotCommendsResp)
	err := c.cc.Invoke(ctx, "/robotcommend.RobotCommend/GetRobotCommends", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RobotCommendServer is the server API for RobotCommend service.
type RobotCommendServer interface {
	GetHello(context.Context, *RobotCommendReq) (*RobotCommendResp, error)
	AddRobotCommends(context.Context, *AddRobotCommendsReq) (*AddRobotCommendsResp, error)
	DelRobotCommend(context.Context, *DelRobotCommendReq) (*DelRobotCommendResp, error)
	GetRobotCommends(context.Context, *GetRobotCommendsReq) (*GetRobotCommendsResp, error)
}

func RegisterRobotCommendServer(s *grpc.Server, srv RobotCommendServer) {
	s.RegisterService(&_RobotCommend_serviceDesc, srv)
}

func _RobotCommend_GetHello_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RobotCommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RobotCommendServer).GetHello(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/robotcommend.RobotCommend/GetHello",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RobotCommendServer).GetHello(ctx, req.(*RobotCommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RobotCommend_AddRobotCommends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRobotCommendsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RobotCommendServer).AddRobotCommends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/robotcommend.RobotCommend/AddRobotCommends",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RobotCommendServer).AddRobotCommends(ctx, req.(*AddRobotCommendsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RobotCommend_DelRobotCommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelRobotCommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RobotCommendServer).DelRobotCommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/robotcommend.RobotCommend/DelRobotCommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RobotCommendServer).DelRobotCommend(ctx, req.(*DelRobotCommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RobotCommend_GetRobotCommends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRobotCommendsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RobotCommendServer).GetRobotCommends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/robotcommend.RobotCommend/GetRobotCommends",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RobotCommendServer).GetRobotCommends(ctx, req.(*GetRobotCommendsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RobotCommend_serviceDesc = grpc.ServiceDesc{
	ServiceName: "robotcommend.RobotCommend",
	HandlerType: (*RobotCommendServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetHello",
			Handler:    _RobotCommend_GetHello_Handler,
		},
		{
			MethodName: "AddRobotCommends",
			Handler:    _RobotCommend_AddRobotCommends_Handler,
		},
		{
			MethodName: "DelRobotCommend",
			Handler:    _RobotCommend_DelRobotCommend_Handler,
		},
		{
			MethodName: "GetRobotCommends",
			Handler:    _RobotCommend_GetRobotCommends_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "robot-commend/robot-commend.proto",
}

func init() {
	proto.RegisterFile("robot-commend/robot-commend.proto", fileDescriptor_robot_commend_888a037fc59bcfae)
}

var fileDescriptor_robot_commend_888a037fc59bcfae = []byte{
	// 323 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x52, 0x4d, 0x4f, 0xc2, 0x40,
	0x14, 0xe4, 0x23, 0x2a, 0x79, 0xa9, 0x01, 0x17, 0x34, 0x4d, 0x13, 0x0d, 0xec, 0x89, 0x8b, 0xdb,
	0x88, 0x7a, 0xf4, 0xe0, 0x47, 0x02, 0x89, 0xb7, 0x3d, 0x19, 0x3d, 0xc1, 0xb2, 0x10, 0x93, 0x85,
	0xb7, 0xb0, 0x8b, 0x7f, 0xdb, 0xbf, 0x60, 0xba, 0xb4, 0xa6, 0xdd, 0x36, 0x70, 0xeb, 0x4c, 0xdf,
	0x4c, 0xdf, 0x9b, 0x29, 0x0c, 0xb6, 0x38, 0x43, 0x7b, 0x2b, 0x70, 0xb5, 0x92, 0xeb, 0x79, 0x5c,
	0x40, 0x4c, 0x6f, 0xd1, 0x22, 0x09, 0x1c, 0x99, 0x72, 0xf4, 0x02, 0xda, 0x3c, 0xc1, 0xaf, 0x7b,
	0xcc, 0xe5, 0x86, 0x12, 0xe8, 0x14, 0x29, 0xa3, 0xe9, 0x1d, 0x74, 0x9f, 0xe7, 0xf3, 0x3c, 0x6d,
	0xb8, 0xdc, 0x90, 0x08, 0x5a, 0xa9, 0x91, 0x09, 0xeb, 0xfd, 0xe6, 0x30, 0xe0, 0xff, 0x98, 0x5e,
	0x41, 0xaf, 0x2c, 0x31, 0x9a, 0x32, 0x20, 0x6f, 0x52, 0x79, 0x1f, 0x25, 0x21, 0x9c, 0xa5, 0xca,
	0xb0, 0xde, 0xaf, 0x0f, 0x03, 0x9e, 0x41, 0x7a, 0x09, 0xdd, 0xd2, 0xbc, 0xd1, 0xf4, 0x09, 0xba,
	0x63, 0x69, 0x4b, 0x1b, 0x75, 0xa0, 0x89, 0x8b, 0x85, 0xf3, 0x38, 0xe7, 0xc9, 0x23, 0xe9, 0xc1,
	0x89, 0xc0, 0xdd, 0xda, 0x86, 0x0d, 0xc7, 0xed, 0x01, 0x9d, 0x40, 0xaf, 0x2c, 0x37, 0xfa, 0xd0,
	0x45, 0x89, 0x93, 0x45, 0x3b, 0x55, 0x99, 0x93, 0x03, 0xa3, 0xdf, 0x06, 0x04, 0x79, 0x1f, 0xf2,
	0x0e, 0xad, 0xb1, 0xb4, 0x13, 0xa9, 0x14, 0x92, 0x6b, 0x96, 0x4f, 0x9b, 0x79, 0x57, 0x47, 0x37,
	0x87, 0x5e, 0x1b, 0x4d, 0x6b, 0xe4, 0x0b, 0x3a, 0x7e, 0x8a, 0x64, 0x50, 0x54, 0x55, 0x14, 0x13,
	0xd1, 0x63, 0x23, 0xce, 0xfc, 0x03, 0xda, 0x5e, 0xb4, 0xa4, 0x5f, 0x14, 0x96, 0x9b, 0x8a, 0x06,
	0x47, 0x26, 0xb2, 0xb5, 0xfd, 0x78, 0xfd, 0xb5, 0x2b, 0xda, 0xf3, 0xd7, 0xae, 0x6a, 0x88, 0xd6,
	0x5e, 0x1e, 0x3e, 0x47, 0x4b, 0x54, 0xd3, 0xf5, 0x92, 0x3d, 0x8e, 0xac, 0x65, 0x02, 0x57, 0xb1,
	0xfb, 0xb5, 0x05, 0xaa, 0xd8, 0xc8, 0xed, 0xcf, 0xb7, 0x90, 0x26, 0xde, 0x2d, 0x45, 0x9c, 0x37,
	0x9b, 0x9d, 0xba, 0x99, 0xfb, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xb8, 0x4f, 0xd2, 0x56, 0x23,
	0x03, 0x00, 0x00,
}
