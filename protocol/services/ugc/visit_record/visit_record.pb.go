// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/visit_record.proto

package visit_record // import "golang.52tt.com/protocol/services/ugc/visit_record"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FollowType int32

const (
	FollowType_AuditType  FollowType = 0
	FollowType_ReportType FollowType = 1
)

var FollowType_name = map[int32]string{
	0: "AuditType",
	1: "ReportType",
}
var FollowType_value = map[string]int32{
	"AuditType":  0,
	"ReportType": 1,
}

func (x FollowType) String() string {
	return proto.EnumName(FollowType_name, int32(x))
}
func (FollowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{0}
}

type AddVisitRecordReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PostIds              []string `protobuf:"bytes,2,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	Type                 string   `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	NeedReturnData       bool     `protobuf:"varint,4,opt,name=need_return_data,json=needReturnData,proto3" json:"need_return_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddVisitRecordReq) Reset()         { *m = AddVisitRecordReq{} }
func (m *AddVisitRecordReq) String() string { return proto.CompactTextString(m) }
func (*AddVisitRecordReq) ProtoMessage()    {}
func (*AddVisitRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{0}
}
func (m *AddVisitRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddVisitRecordReq.Unmarshal(m, b)
}
func (m *AddVisitRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddVisitRecordReq.Marshal(b, m, deterministic)
}
func (dst *AddVisitRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddVisitRecordReq.Merge(dst, src)
}
func (m *AddVisitRecordReq) XXX_Size() int {
	return xxx_messageInfo_AddVisitRecordReq.Size(m)
}
func (m *AddVisitRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddVisitRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddVisitRecordReq proto.InternalMessageInfo

func (m *AddVisitRecordReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AddVisitRecordReq) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

func (m *AddVisitRecordReq) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *AddVisitRecordReq) GetNeedReturnData() bool {
	if m != nil {
		return m.NeedReturnData
	}
	return false
}

type AddVisitRecordResp struct {
	NewArray             []string `protobuf:"bytes,1,rep,name=new_array,json=newArray,proto3" json:"new_array,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddVisitRecordResp) Reset()         { *m = AddVisitRecordResp{} }
func (m *AddVisitRecordResp) String() string { return proto.CompactTextString(m) }
func (*AddVisitRecordResp) ProtoMessage()    {}
func (*AddVisitRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{1}
}
func (m *AddVisitRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddVisitRecordResp.Unmarshal(m, b)
}
func (m *AddVisitRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddVisitRecordResp.Marshal(b, m, deterministic)
}
func (dst *AddVisitRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddVisitRecordResp.Merge(dst, src)
}
func (m *AddVisitRecordResp) XXX_Size() int {
	return xxx_messageInfo_AddVisitRecordResp.Size(m)
}
func (m *AddVisitRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddVisitRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddVisitRecordResp proto.InternalMessageInfo

func (m *AddVisitRecordResp) GetNewArray() []string {
	if m != nil {
		return m.NewArray
	}
	return nil
}

type GetVisitRecordReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Type                 string   `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVisitRecordReq) Reset()         { *m = GetVisitRecordReq{} }
func (m *GetVisitRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetVisitRecordReq) ProtoMessage()    {}
func (*GetVisitRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{2}
}
func (m *GetVisitRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVisitRecordReq.Unmarshal(m, b)
}
func (m *GetVisitRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVisitRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetVisitRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVisitRecordReq.Merge(dst, src)
}
func (m *GetVisitRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetVisitRecordReq.Size(m)
}
func (m *GetVisitRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVisitRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVisitRecordReq proto.InternalMessageInfo

func (m *GetVisitRecordReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetVisitRecordReq) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

type GetVisitRecordResp struct {
	PostIds              []string `protobuf:"bytes,1,rep,name=post_ids,json=postIds,proto3" json:"post_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVisitRecordResp) Reset()         { *m = GetVisitRecordResp{} }
func (m *GetVisitRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetVisitRecordResp) ProtoMessage()    {}
func (*GetVisitRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{3}
}
func (m *GetVisitRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVisitRecordResp.Unmarshal(m, b)
}
func (m *GetVisitRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVisitRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetVisitRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVisitRecordResp.Merge(dst, src)
}
func (m *GetVisitRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetVisitRecordResp.Size(m)
}
func (m *GetVisitRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVisitRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVisitRecordResp proto.InternalMessageInfo

func (m *GetVisitRecordResp) GetPostIds() []string {
	if m != nil {
		return m.PostIds
	}
	return nil
}

type GetUserFollowPostReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FollowUids           []uint32 `protobuf:"varint,2,rep,packed,name=follow_uids,json=followUids,proto3" json:"follow_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserFollowPostReq) Reset()         { *m = GetUserFollowPostReq{} }
func (m *GetUserFollowPostReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFollowPostReq) ProtoMessage()    {}
func (*GetUserFollowPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{4}
}
func (m *GetUserFollowPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFollowPostReq.Unmarshal(m, b)
}
func (m *GetUserFollowPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFollowPostReq.Marshal(b, m, deterministic)
}
func (dst *GetUserFollowPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFollowPostReq.Merge(dst, src)
}
func (m *GetUserFollowPostReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFollowPostReq.Size(m)
}
func (m *GetUserFollowPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFollowPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFollowPostReq proto.InternalMessageInfo

func (m *GetUserFollowPostReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserFollowPostReq) GetFollowUids() []uint32 {
	if m != nil {
		return m.FollowUids
	}
	return nil
}

type GetUserFollowPostRsp struct {
	IsFollowUpdate       bool     `protobuf:"varint,1,opt,name=is_follow_update,json=isFollowUpdate,proto3" json:"is_follow_update,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostTime             int64    `protobuf:"varint,3,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserFollowPostRsp) Reset()         { *m = GetUserFollowPostRsp{} }
func (m *GetUserFollowPostRsp) String() string { return proto.CompactTextString(m) }
func (*GetUserFollowPostRsp) ProtoMessage()    {}
func (*GetUserFollowPostRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{5}
}
func (m *GetUserFollowPostRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFollowPostRsp.Unmarshal(m, b)
}
func (m *GetUserFollowPostRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFollowPostRsp.Marshal(b, m, deterministic)
}
func (dst *GetUserFollowPostRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFollowPostRsp.Merge(dst, src)
}
func (m *GetUserFollowPostRsp) XXX_Size() int {
	return xxx_messageInfo_GetUserFollowPostRsp.Size(m)
}
func (m *GetUserFollowPostRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFollowPostRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFollowPostRsp proto.InternalMessageInfo

func (m *GetUserFollowPostRsp) GetIsFollowUpdate() bool {
	if m != nil {
		return m.IsFollowUpdate
	}
	return false
}

func (m *GetUserFollowPostRsp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetUserFollowPostRsp) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *GetUserFollowPostRsp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ReportUserFollowPostReq struct {
	FollowType           FollowType `protobuf:"varint,1,opt,name=follow_type,json=followType,proto3,enum=ugc.visit_record.FollowType" json:"follow_type,omitempty"`
	Uid                  uint32     `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	PostTime             int64      `protobuf:"varint,3,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	PostId               string     `protobuf:"bytes,4,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ReportUserFollowPostReq) Reset()         { *m = ReportUserFollowPostReq{} }
func (m *ReportUserFollowPostReq) String() string { return proto.CompactTextString(m) }
func (*ReportUserFollowPostReq) ProtoMessage()    {}
func (*ReportUserFollowPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{6}
}
func (m *ReportUserFollowPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserFollowPostReq.Unmarshal(m, b)
}
func (m *ReportUserFollowPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserFollowPostReq.Marshal(b, m, deterministic)
}
func (dst *ReportUserFollowPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserFollowPostReq.Merge(dst, src)
}
func (m *ReportUserFollowPostReq) XXX_Size() int {
	return xxx_messageInfo_ReportUserFollowPostReq.Size(m)
}
func (m *ReportUserFollowPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserFollowPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserFollowPostReq proto.InternalMessageInfo

func (m *ReportUserFollowPostReq) GetFollowType() FollowType {
	if m != nil {
		return m.FollowType
	}
	return FollowType_AuditType
}

func (m *ReportUserFollowPostReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportUserFollowPostReq) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *ReportUserFollowPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type ReportUserFollowPostRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportUserFollowPostRsp) Reset()         { *m = ReportUserFollowPostRsp{} }
func (m *ReportUserFollowPostRsp) String() string { return proto.CompactTextString(m) }
func (*ReportUserFollowPostRsp) ProtoMessage()    {}
func (*ReportUserFollowPostRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{7}
}
func (m *ReportUserFollowPostRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserFollowPostRsp.Unmarshal(m, b)
}
func (m *ReportUserFollowPostRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserFollowPostRsp.Marshal(b, m, deterministic)
}
func (dst *ReportUserFollowPostRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserFollowPostRsp.Merge(dst, src)
}
func (m *ReportUserFollowPostRsp) XXX_Size() int {
	return xxx_messageInfo_ReportUserFollowPostRsp.Size(m)
}
func (m *ReportUserFollowPostRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserFollowPostRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserFollowPostRsp proto.InternalMessageInfo

type DelUserFollowPostReq struct {
	FollowType           FollowType `protobuf:"varint,1,opt,name=follow_type,json=followType,proto3,enum=ugc.visit_record.FollowType" json:"follow_type,omitempty"`
	Uid                  uint32     `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *DelUserFollowPostReq) Reset()         { *m = DelUserFollowPostReq{} }
func (m *DelUserFollowPostReq) String() string { return proto.CompactTextString(m) }
func (*DelUserFollowPostReq) ProtoMessage()    {}
func (*DelUserFollowPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{8}
}
func (m *DelUserFollowPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserFollowPostReq.Unmarshal(m, b)
}
func (m *DelUserFollowPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserFollowPostReq.Marshal(b, m, deterministic)
}
func (dst *DelUserFollowPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserFollowPostReq.Merge(dst, src)
}
func (m *DelUserFollowPostReq) XXX_Size() int {
	return xxx_messageInfo_DelUserFollowPostReq.Size(m)
}
func (m *DelUserFollowPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserFollowPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserFollowPostReq proto.InternalMessageInfo

func (m *DelUserFollowPostReq) GetFollowType() FollowType {
	if m != nil {
		return m.FollowType
	}
	return FollowType_AuditType
}

func (m *DelUserFollowPostReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelUserFollowPostRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserFollowPostRsp) Reset()         { *m = DelUserFollowPostRsp{} }
func (m *DelUserFollowPostRsp) String() string { return proto.CompactTextString(m) }
func (*DelUserFollowPostRsp) ProtoMessage()    {}
func (*DelUserFollowPostRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_visit_record_331f89e0d5b80250, []int{9}
}
func (m *DelUserFollowPostRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserFollowPostRsp.Unmarshal(m, b)
}
func (m *DelUserFollowPostRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserFollowPostRsp.Marshal(b, m, deterministic)
}
func (dst *DelUserFollowPostRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserFollowPostRsp.Merge(dst, src)
}
func (m *DelUserFollowPostRsp) XXX_Size() int {
	return xxx_messageInfo_DelUserFollowPostRsp.Size(m)
}
func (m *DelUserFollowPostRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserFollowPostRsp.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserFollowPostRsp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*AddVisitRecordReq)(nil), "ugc.visit_record.AddVisitRecordReq")
	proto.RegisterType((*AddVisitRecordResp)(nil), "ugc.visit_record.AddVisitRecordResp")
	proto.RegisterType((*GetVisitRecordReq)(nil), "ugc.visit_record.GetVisitRecordReq")
	proto.RegisterType((*GetVisitRecordResp)(nil), "ugc.visit_record.GetVisitRecordResp")
	proto.RegisterType((*GetUserFollowPostReq)(nil), "ugc.visit_record.GetUserFollowPostReq")
	proto.RegisterType((*GetUserFollowPostRsp)(nil), "ugc.visit_record.GetUserFollowPostRsp")
	proto.RegisterType((*ReportUserFollowPostReq)(nil), "ugc.visit_record.ReportUserFollowPostReq")
	proto.RegisterType((*ReportUserFollowPostRsp)(nil), "ugc.visit_record.ReportUserFollowPostRsp")
	proto.RegisterType((*DelUserFollowPostReq)(nil), "ugc.visit_record.DelUserFollowPostReq")
	proto.RegisterType((*DelUserFollowPostRsp)(nil), "ugc.visit_record.DelUserFollowPostRsp")
	proto.RegisterEnum("ugc.visit_record.FollowType", FollowType_name, FollowType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VisitRecordClient is the client API for VisitRecord service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VisitRecordClient interface {
	AddVisitRecord(ctx context.Context, in *AddVisitRecordReq, opts ...grpc.CallOption) (*AddVisitRecordResp, error)
	GetVisitRecord(ctx context.Context, in *GetVisitRecordReq, opts ...grpc.CallOption) (*GetVisitRecordResp, error)
	GetUserFollowPost(ctx context.Context, in *GetUserFollowPostReq, opts ...grpc.CallOption) (*GetUserFollowPostRsp, error)
	ReportUserFollowPost(ctx context.Context, in *ReportUserFollowPostReq, opts ...grpc.CallOption) (*ReportUserFollowPostRsp, error)
	DelUserFollowPost(ctx context.Context, in *DelUserFollowPostReq, opts ...grpc.CallOption) (*DelUserFollowPostRsp, error)
}

type visitRecordClient struct {
	cc *grpc.ClientConn
}

func NewVisitRecordClient(cc *grpc.ClientConn) VisitRecordClient {
	return &visitRecordClient{cc}
}

func (c *visitRecordClient) AddVisitRecord(ctx context.Context, in *AddVisitRecordReq, opts ...grpc.CallOption) (*AddVisitRecordResp, error) {
	out := new(AddVisitRecordResp)
	err := c.cc.Invoke(ctx, "/ugc.visit_record.VisitRecord/AddVisitRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visitRecordClient) GetVisitRecord(ctx context.Context, in *GetVisitRecordReq, opts ...grpc.CallOption) (*GetVisitRecordResp, error) {
	out := new(GetVisitRecordResp)
	err := c.cc.Invoke(ctx, "/ugc.visit_record.VisitRecord/GetVisitRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visitRecordClient) GetUserFollowPost(ctx context.Context, in *GetUserFollowPostReq, opts ...grpc.CallOption) (*GetUserFollowPostRsp, error) {
	out := new(GetUserFollowPostRsp)
	err := c.cc.Invoke(ctx, "/ugc.visit_record.VisitRecord/GetUserFollowPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visitRecordClient) ReportUserFollowPost(ctx context.Context, in *ReportUserFollowPostReq, opts ...grpc.CallOption) (*ReportUserFollowPostRsp, error) {
	out := new(ReportUserFollowPostRsp)
	err := c.cc.Invoke(ctx, "/ugc.visit_record.VisitRecord/ReportUserFollowPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *visitRecordClient) DelUserFollowPost(ctx context.Context, in *DelUserFollowPostReq, opts ...grpc.CallOption) (*DelUserFollowPostRsp, error) {
	out := new(DelUserFollowPostRsp)
	err := c.cc.Invoke(ctx, "/ugc.visit_record.VisitRecord/DelUserFollowPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VisitRecordServer is the server API for VisitRecord service.
type VisitRecordServer interface {
	AddVisitRecord(context.Context, *AddVisitRecordReq) (*AddVisitRecordResp, error)
	GetVisitRecord(context.Context, *GetVisitRecordReq) (*GetVisitRecordResp, error)
	GetUserFollowPost(context.Context, *GetUserFollowPostReq) (*GetUserFollowPostRsp, error)
	ReportUserFollowPost(context.Context, *ReportUserFollowPostReq) (*ReportUserFollowPostRsp, error)
	DelUserFollowPost(context.Context, *DelUserFollowPostReq) (*DelUserFollowPostRsp, error)
}

func RegisterVisitRecordServer(s *grpc.Server, srv VisitRecordServer) {
	s.RegisterService(&_VisitRecord_serviceDesc, srv)
}

func _VisitRecord_AddVisitRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddVisitRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisitRecordServer).AddVisitRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.visit_record.VisitRecord/AddVisitRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisitRecordServer).AddVisitRecord(ctx, req.(*AddVisitRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VisitRecord_GetVisitRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVisitRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisitRecordServer).GetVisitRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.visit_record.VisitRecord/GetVisitRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisitRecordServer).GetVisitRecord(ctx, req.(*GetVisitRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VisitRecord_GetUserFollowPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFollowPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisitRecordServer).GetUserFollowPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.visit_record.VisitRecord/GetUserFollowPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisitRecordServer).GetUserFollowPost(ctx, req.(*GetUserFollowPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VisitRecord_ReportUserFollowPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportUserFollowPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisitRecordServer).ReportUserFollowPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.visit_record.VisitRecord/ReportUserFollowPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisitRecordServer).ReportUserFollowPost(ctx, req.(*ReportUserFollowPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VisitRecord_DelUserFollowPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserFollowPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VisitRecordServer).DelUserFollowPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.visit_record.VisitRecord/DelUserFollowPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VisitRecordServer).DelUserFollowPost(ctx, req.(*DelUserFollowPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _VisitRecord_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.visit_record.VisitRecord",
	HandlerType: (*VisitRecordServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddVisitRecord",
			Handler:    _VisitRecord_AddVisitRecord_Handler,
		},
		{
			MethodName: "GetVisitRecord",
			Handler:    _VisitRecord_GetVisitRecord_Handler,
		},
		{
			MethodName: "GetUserFollowPost",
			Handler:    _VisitRecord_GetUserFollowPost_Handler,
		},
		{
			MethodName: "ReportUserFollowPost",
			Handler:    _VisitRecord_ReportUserFollowPost_Handler,
		},
		{
			MethodName: "DelUserFollowPost",
			Handler:    _VisitRecord_DelUserFollowPost_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/visit_record.proto",
}

func init() {
	proto.RegisterFile("ugc/visit_record.proto", fileDescriptor_visit_record_331f89e0d5b80250)
}

var fileDescriptor_visit_record_331f89e0d5b80250 = []byte{
	// 548 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x54, 0xdf, 0x8a, 0x13, 0x3d,
	0x14, 0xef, 0xb4, 0xa5, 0xdb, 0x9e, 0xa5, 0xa5, 0x1b, 0xca, 0xee, 0x6c, 0xbf, 0x0f, 0x2c, 0x51,
	0xa4, 0x2a, 0x4c, 0xb1, 0xea, 0xa5, 0x60, 0x65, 0x71, 0xe9, 0x9d, 0x84, 0xad, 0x17, 0x82, 0x0c,
	0xe3, 0x24, 0x96, 0x40, 0x3b, 0x89, 0x49, 0x66, 0x4b, 0x1f, 0x40, 0xd0, 0xb7, 0xf0, 0x51, 0x25,
	0x19, 0xa7, 0xdb, 0xe9, 0xcc, 0x6a, 0x6f, 0xbc, 0xcb, 0x39, 0x73, 0xf2, 0xfb, 0x73, 0x72, 0xce,
	0xc0, 0x79, 0xba, 0x8c, 0x27, 0xb7, 0x5c, 0x73, 0x13, 0x2a, 0x16, 0x0b, 0x45, 0x03, 0xa9, 0x84,
	0x11, 0xa8, 0x9f, 0x2e, 0xe3, 0x60, 0x3f, 0x8f, 0xbf, 0x79, 0x70, 0x36, 0xa3, 0xf4, 0x83, 0xcd,
	0x11, 0x97, 0x22, 0xec, 0x2b, 0xba, 0x80, 0x93, 0x54, 0x33, 0x15, 0x72, 0xea, 0x7b, 0x23, 0x6f,
	0xdc, 0x25, 0x2d, 0x1b, 0xce, 0x29, 0xba, 0x84, 0xb6, 0x14, 0xda, 0x84, 0x9c, 0x6a, 0xbf, 0x3e,
	0x6a, 0x8c, 0x3b, 0xe4, 0xc4, 0xc6, 0x73, 0xaa, 0x11, 0x82, 0xa6, 0xd9, 0x4a, 0xe6, 0x37, 0x46,
	0xde, 0xb8, 0x43, 0xdc, 0x19, 0x8d, 0xa1, 0x9f, 0x30, 0x46, 0x43, 0xc5, 0x4c, 0xaa, 0x92, 0x90,
	0x46, 0x26, 0xf2, 0x9b, 0x23, 0x6f, 0xdc, 0x26, 0x3d, 0x9b, 0x27, 0x2e, 0x7d, 0x15, 0x99, 0x08,
	0x3f, 0x07, 0x74, 0x28, 0x43, 0x4b, 0xf4, 0x1f, 0x74, 0x12, 0xb6, 0x09, 0x23, 0xa5, 0xa2, 0xad,
	0xef, 0x39, 0xbe, 0x76, 0xc2, 0x36, 0x33, 0x1b, 0xe3, 0x37, 0x70, 0x76, 0xcd, 0xcc, 0xb1, 0xca,
	0x2b, 0xe4, 0xe1, 0x09, 0xa0, 0x43, 0x04, 0x2d, 0x0b, 0x1e, 0xbd, 0x82, 0x47, 0x3c, 0x87, 0xc1,
	0x35, 0x33, 0x0b, 0xcd, 0xd4, 0x3b, 0xb1, 0x5a, 0x89, 0xcd, 0x7b, 0xa1, 0x8d, 0x65, 0xed, 0x43,
	0x23, 0xdd, 0x31, 0xda, 0x23, 0x7a, 0x00, 0xa7, 0x5f, 0x5c, 0x49, 0x98, 0xe6, 0xbd, 0xea, 0x12,
	0xc8, 0x52, 0x0b, 0x4e, 0x35, 0xfe, 0xee, 0x55, 0x61, 0x69, 0x69, 0x7b, 0xc6, 0x75, 0x98, 0x5f,
	0x96, 0x34, 0x32, 0xcc, 0x01, 0xb7, 0x49, 0x8f, 0xeb, 0xac, 0x74, 0xe1, 0xb2, 0xd6, 0xeb, 0x6f,
	0xa1, 0x7e, 0xdd, 0xb9, 0x6a, 0x65, 0x3a, 0x6d, 0xdb, 0xdc, 0x07, 0xc3, 0xd7, 0x99, 0xe1, 0x06,
	0x71, 0x96, 0x6e, 0xf8, 0x9a, 0xe5, 0x5a, 0x9b, 0x3b, 0xad, 0xf8, 0xa7, 0x07, 0x17, 0x84, 0x49,
	0xa1, 0x2a, 0x9c, 0xbd, 0xde, 0xf9, 0x70, 0xdd, 0xb3, 0x42, 0x7a, 0xd3, 0xff, 0x83, 0xc3, 0x39,
	0x0a, 0xb2, 0x5b, 0x37, 0x5b, 0xc9, 0x72, 0x97, 0xf6, 0x9c, 0x93, 0xd5, 0xef, 0x1a, 0xf3, 0x47,
	0x6d, 0x7b, 0x8e, 0x9a, 0xfb, 0x8e, 0xf0, 0xe5, 0x3d, 0x0a, 0xb5, 0xc4, 0x4b, 0x18, 0x5c, 0xb1,
	0xd5, 0xbf, 0x57, 0x8e, 0xcf, 0xab, 0x88, 0xb4, 0x7c, 0xfa, 0x0c, 0xe0, 0x0e, 0x03, 0x75, 0xa1,
	0x33, 0x4b, 0x29, 0x37, 0x36, 0xe8, 0xd7, 0x50, 0x0f, 0x20, 0x13, 0xee, 0x62, 0x6f, 0xfa, 0xa3,
	0x09, 0xa7, 0x7b, 0x03, 0x87, 0x3e, 0x41, 0xaf, 0x38, 0xf7, 0xe8, 0x61, 0x59, 0x62, 0x69, 0x41,
	0x87, 0x8f, 0xfe, 0x5e, 0xa4, 0x25, 0xae, 0x59, 0xf8, 0xe2, 0x84, 0x57, 0xc1, 0x97, 0xb6, 0xa8,
	0x0a, 0xbe, 0xbc, 0x28, 0xb8, 0x86, 0x98, 0x5b, 0xc1, 0x62, 0x4b, 0xd0, 0xe3, 0xca, 0xcb, 0xa5,
	0x07, 0x1a, 0x1e, 0x55, 0xe7, 0x68, 0x12, 0x18, 0x54, 0xbd, 0x3e, 0x7a, 0x52, 0x46, 0xb8, 0x67,
	0x8e, 0x87, 0xc7, 0x96, 0xe6, 0xb6, 0x4a, 0x2f, 0x5d, 0x65, 0xab, 0x6a, 0xee, 0x86, 0x47, 0xd5,
	0x59, 0x9a, 0xb7, 0x2f, 0x3f, 0x4e, 0x97, 0x62, 0x15, 0x25, 0xcb, 0xe0, 0xd5, 0xd4, 0x98, 0x20,
	0x16, 0xeb, 0x89, 0xfb, 0x4d, 0xc7, 0x62, 0x35, 0xd1, 0x4c, 0xdd, 0xf2, 0x98, 0xe9, 0xc9, 0xe1,
	0x9f, 0xfc, 0x73, 0xcb, 0xd5, 0xbc, 0xf8, 0x15, 0x00, 0x00, 0xff, 0xff, 0x7f, 0xeb, 0x46, 0x80,
	0xe4, 0x05, 0x00, 0x00,
}
