// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/event.proto

package ugc_event

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Action int32

const (
	Action_ACTION_UNDEFINED                 Action = 0
	Action_ACTION_BAN_WITH_NORMAL_STATUS    Action = 1
	Action_ACTION_DELETE_WITH_NORMAL_STATUS Action = 2
	Action_ACTION_CHANGE_PRIVACY_POLICY     Action = 3
	Action_ACTION_PUBLISH                   Action = 4
)

var Action_name = map[int32]string{
	0: "ACTION_UNDEFINED",
	1: "ACTION_BAN_WITH_NORMAL_STATUS",
	2: "ACTION_DELETE_WITH_NORMAL_STATUS",
	3: "ACTION_CHANGE_PRIVACY_POLICY",
	4: "ACTION_PUBLISH",
}
var Action_value = map[string]int32{
	"ACTION_UNDEFINED":                 0,
	"ACTION_BAN_WITH_NORMAL_STATUS":    1,
	"ACTION_DELETE_WITH_NORMAL_STATUS": 2,
	"ACTION_CHANGE_PRIVACY_POLICY":     3,
	"ACTION_PUBLISH":                   4,
}

func (x Action) String() string {
	return proto.EnumName(Action_name, int32(x))
}
func (Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{0}
}

type PostEvent_Origin int32

const (
	PostEvent_NORMAL  PostEvent_Origin = 0
	PostEvent_BACKEND PostEvent_Origin = 1
)

var PostEvent_Origin_name = map[int32]string{
	0: "NORMAL",
	1: "BACKEND",
}
var PostEvent_Origin_value = map[string]int32{
	"NORMAL":  0,
	"BACKEND": 1,
}

func (x PostEvent_Origin) String() string {
	return proto.EnumName(PostEvent_Origin_name, int32(x))
}
func (PostEvent_Origin) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{0, 0}
}

type FollowEvent_Source int32

const (
	FollowEvent_USER_OPERATION FollowEvent_Source = 0
	FollowEvent_SYNCHORNIZER   FollowEvent_Source = 1
	FollowEvent_FRIEND_VERIFY  FollowEvent_Source = 2
)

var FollowEvent_Source_name = map[int32]string{
	0: "USER_OPERATION",
	1: "SYNCHORNIZER",
	2: "FRIEND_VERIFY",
}
var FollowEvent_Source_value = map[string]int32{
	"USER_OPERATION": 0,
	"SYNCHORNIZER":   1,
	"FRIEND_VERIFY":  2,
}

func (x FollowEvent_Source) String() string {
	return proto.EnumName(FollowEvent_Source_name, int32(x))
}
func (FollowEvent_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{3, 0}
}

// 发帖
type PostEvent struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	UserId               uint32   `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CreateAt             uint64   `protobuf:"varint,5,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	PostType             uint32   `protobuf:"varint,6,opt,name=post_type,json=postType,proto3" json:"post_type,omitempty"`
	Origin               uint32   `protobuf:"varint,7,opt,name=origin,proto3" json:"origin,omitempty"`
	SubTopicId           string   `protobuf:"bytes,8,opt,name=sub_topic_id,json=subTopicId,proto3" json:"sub_topic_id,omitempty"`
	ContentType          int32    `protobuf:"varint,9,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	PostOrigin           uint32   `protobuf:"varint,10,opt,name=post_origin,json=postOrigin,proto3" json:"post_origin,omitempty"`
	IsSystem             uint32   `protobuf:"varint,11,opt,name=is_system,json=isSystem,proto3" json:"is_system,omitempty"`
	GeoTopicId           string   `protobuf:"bytes,12,opt,name=geo_topic_id,json=geoTopicId,proto3" json:"geo_topic_id,omitempty"`
	Content              string   `protobuf:"bytes,13,opt,name=content,proto3" json:"content,omitempty"`
	PrivacyPolicy        uint32   `protobuf:"varint,14,opt,name=privacy_policy,json=privacyPolicy,proto3" json:"privacy_policy,omitempty"`
	Action               Action   `protobuf:"varint,15,opt,name=action,proto3,enum=ugc.event.Action" json:"action,omitempty"`
	UpdateAt             int64    `protobuf:"varint,16,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	DiyTopicIds          []string `protobuf:"bytes,17,rep,name=diy_topic_ids,json=diyTopicIds,proto3" json:"diy_topic_ids,omitempty"`
	ClientIp             uint32   `protobuf:"varint,18,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostEvent) Reset()         { *m = PostEvent{} }
func (m *PostEvent) String() string { return proto.CompactTextString(m) }
func (*PostEvent) ProtoMessage()    {}
func (*PostEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{0}
}
func (m *PostEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostEvent.Unmarshal(m, b)
}
func (m *PostEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostEvent.Marshal(b, m, deterministic)
}
func (dst *PostEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostEvent.Merge(dst, src)
}
func (m *PostEvent) XXX_Size() int {
	return xxx_messageInfo_PostEvent.Size(m)
}
func (m *PostEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PostEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PostEvent proto.InternalMessageInfo

func (m *PostEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostEvent) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *PostEvent) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PostEvent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *PostEvent) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *PostEvent) GetPostType() uint32 {
	if m != nil {
		return m.PostType
	}
	return 0
}

func (m *PostEvent) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *PostEvent) GetSubTopicId() string {
	if m != nil {
		return m.SubTopicId
	}
	return ""
}

func (m *PostEvent) GetContentType() int32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *PostEvent) GetPostOrigin() uint32 {
	if m != nil {
		return m.PostOrigin
	}
	return 0
}

func (m *PostEvent) GetIsSystem() uint32 {
	if m != nil {
		return m.IsSystem
	}
	return 0
}

func (m *PostEvent) GetGeoTopicId() string {
	if m != nil {
		return m.GeoTopicId
	}
	return ""
}

func (m *PostEvent) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PostEvent) GetPrivacyPolicy() uint32 {
	if m != nil {
		return m.PrivacyPolicy
	}
	return 0
}

func (m *PostEvent) GetAction() Action {
	if m != nil {
		return m.Action
	}
	return Action_ACTION_UNDEFINED
}

func (m *PostEvent) GetUpdateAt() int64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *PostEvent) GetDiyTopicIds() []string {
	if m != nil {
		return m.DiyTopicIds
	}
	return nil
}

func (m *PostEvent) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

// 评论
type CommentEvent struct {
	PostId           string `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TopicId          string `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	CommentId        string `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	ReplyToCommentId string `protobuf:"bytes,4,opt,name=reply_to_comment_id,json=replyToCommentId,proto3" json:"reply_to_comment_id,omitempty"`
	ConversationId   string `protobuf:"bytes,5,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	FromUserId       uint32 `protobuf:"varint,6,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	// 回复哪个人, 可能和下面的reply_to_comment_owner_user_id, post_owner_user_id 一样
	ToUserId                  uint32   `protobuf:"varint,7,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	Status                    uint32   `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	CreateAt                  uint64   `protobuf:"varint,9,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	PostOwnerUserId           uint32   `protobuf:"varint,10,opt,name=post_owner_user_id,json=postOwnerUserId,proto3" json:"post_owner_user_id,omitempty"`
	ReplyToCommentOwnerUserId uint32   `protobuf:"varint,11,opt,name=reply_to_comment_owner_user_id,json=replyToCommentOwnerUserId,proto3" json:"reply_to_comment_owner_user_id,omitempty"`
	ContentType               int32    `protobuf:"varint,12,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	IsRobot                   bool     `protobuf:"varint,13,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	Source                    uint32   `protobuf:"varint,14,opt,name=source,proto3" json:"source,omitempty"`
	IsValidPosterInter        bool     `protobuf:"varint,15,opt,name=is_valid_poster_inter,json=isValidPosterInter,proto3" json:"is_valid_poster_inter,omitempty"`
	IsValidUserInter          bool     `protobuf:"varint,16,opt,name=is_valid_user_inter,json=isValidUserInter,proto3" json:"is_valid_user_inter,omitempty"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *CommentEvent) Reset()         { *m = CommentEvent{} }
func (m *CommentEvent) String() string { return proto.CompactTextString(m) }
func (*CommentEvent) ProtoMessage()    {}
func (*CommentEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{1}
}
func (m *CommentEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentEvent.Unmarshal(m, b)
}
func (m *CommentEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentEvent.Marshal(b, m, deterministic)
}
func (dst *CommentEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentEvent.Merge(dst, src)
}
func (m *CommentEvent) XXX_Size() int {
	return xxx_messageInfo_CommentEvent.Size(m)
}
func (m *CommentEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentEvent.DiscardUnknown(m)
}

var xxx_messageInfo_CommentEvent proto.InternalMessageInfo

func (m *CommentEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CommentEvent) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *CommentEvent) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *CommentEvent) GetReplyToCommentId() string {
	if m != nil {
		return m.ReplyToCommentId
	}
	return ""
}

func (m *CommentEvent) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *CommentEvent) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *CommentEvent) GetToUserId() uint32 {
	if m != nil {
		return m.ToUserId
	}
	return 0
}

func (m *CommentEvent) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CommentEvent) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *CommentEvent) GetPostOwnerUserId() uint32 {
	if m != nil {
		return m.PostOwnerUserId
	}
	return 0
}

func (m *CommentEvent) GetReplyToCommentOwnerUserId() uint32 {
	if m != nil {
		return m.ReplyToCommentOwnerUserId
	}
	return 0
}

func (m *CommentEvent) GetContentType() int32 {
	if m != nil {
		return m.ContentType
	}
	return 0
}

func (m *CommentEvent) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

func (m *CommentEvent) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *CommentEvent) GetIsValidPosterInter() bool {
	if m != nil {
		return m.IsValidPosterInter
	}
	return false
}

func (m *CommentEvent) GetIsValidUserInter() bool {
	if m != nil {
		return m.IsValidUserInter
	}
	return false
}

// 点赞
type AttitudeEvent struct {
	// Types that are valid to be assigned to Target:
	//	*AttitudeEvent_PostId
	//	*AttitudeEvent_CommentId
	Target               isAttitudeEvent_Target `protobuf_oneof:"target"`
	AttitudeType         uint32                 `protobuf:"varint,3,opt,name=attitude_type,json=attitudeType,proto3" json:"attitude_type,omitempty"`
	CreateAt             uint64                 `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	UserId               uint32                 `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TargetCreatorUid     uint32                 `protobuf:"varint,6,opt,name=target_creator_uid,json=targetCreatorUid,proto3" json:"target_creator_uid,omitempty"`
	IsFirstTime          bool                   `protobuf:"varint,7,opt,name=is_first_time,json=isFirstTime,proto3" json:"is_first_time,omitempty"`
	Source               uint32                 `protobuf:"varint,8,opt,name=source,proto3" json:"source,omitempty"`
	IsRobot              bool                   `protobuf:"varint,20,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AttitudeEvent) Reset()         { *m = AttitudeEvent{} }
func (m *AttitudeEvent) String() string { return proto.CompactTextString(m) }
func (*AttitudeEvent) ProtoMessage()    {}
func (*AttitudeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{2}
}
func (m *AttitudeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AttitudeEvent.Unmarshal(m, b)
}
func (m *AttitudeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AttitudeEvent.Marshal(b, m, deterministic)
}
func (dst *AttitudeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AttitudeEvent.Merge(dst, src)
}
func (m *AttitudeEvent) XXX_Size() int {
	return xxx_messageInfo_AttitudeEvent.Size(m)
}
func (m *AttitudeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_AttitudeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_AttitudeEvent proto.InternalMessageInfo

type isAttitudeEvent_Target interface {
	isAttitudeEvent_Target()
}

type AttitudeEvent_PostId struct {
	PostId string `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3,oneof"`
}

type AttitudeEvent_CommentId struct {
	CommentId string `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3,oneof"`
}

func (*AttitudeEvent_PostId) isAttitudeEvent_Target() {}

func (*AttitudeEvent_CommentId) isAttitudeEvent_Target() {}

func (m *AttitudeEvent) GetTarget() isAttitudeEvent_Target {
	if m != nil {
		return m.Target
	}
	return nil
}

func (m *AttitudeEvent) GetPostId() string {
	if x, ok := m.GetTarget().(*AttitudeEvent_PostId); ok {
		return x.PostId
	}
	return ""
}

func (m *AttitudeEvent) GetCommentId() string {
	if x, ok := m.GetTarget().(*AttitudeEvent_CommentId); ok {
		return x.CommentId
	}
	return ""
}

func (m *AttitudeEvent) GetAttitudeType() uint32 {
	if m != nil {
		return m.AttitudeType
	}
	return 0
}

func (m *AttitudeEvent) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *AttitudeEvent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *AttitudeEvent) GetTargetCreatorUid() uint32 {
	if m != nil {
		return m.TargetCreatorUid
	}
	return 0
}

func (m *AttitudeEvent) GetIsFirstTime() bool {
	if m != nil {
		return m.IsFirstTime
	}
	return false
}

func (m *AttitudeEvent) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AttitudeEvent) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*AttitudeEvent) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _AttitudeEvent_OneofMarshaler, _AttitudeEvent_OneofUnmarshaler, _AttitudeEvent_OneofSizer, []interface{}{
		(*AttitudeEvent_PostId)(nil),
		(*AttitudeEvent_CommentId)(nil),
	}
}

func _AttitudeEvent_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*AttitudeEvent)
	// target
	switch x := m.Target.(type) {
	case *AttitudeEvent_PostId:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.PostId)
	case *AttitudeEvent_CommentId:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.CommentId)
	case nil:
	default:
		return fmt.Errorf("AttitudeEvent.Target has unexpected type %T", x)
	}
	return nil
}

func _AttitudeEvent_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*AttitudeEvent)
	switch tag {
	case 1: // target.post_id
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Target = &AttitudeEvent_PostId{x}
		return true, err
	case 2: // target.comment_id
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Target = &AttitudeEvent_CommentId{x}
		return true, err
	default:
		return false, nil
	}
}

func _AttitudeEvent_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*AttitudeEvent)
	// target
	switch x := m.Target.(type) {
	case *AttitudeEvent_PostId:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.PostId)))
		n += len(x.PostId)
	case *AttitudeEvent_CommentId:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.CommentId)))
		n += len(x.CommentId)
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 关注 / 取消关注
type FollowEvent struct {
	FromUserId           uint32             `protobuf:"varint,1,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	ToUserId             uint32             `protobuf:"varint,2,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	IsFirstTime          bool               `protobuf:"varint,3,opt,name=is_first_time,json=isFirstTime,proto3" json:"is_first_time,omitempty"`
	CreateAt             uint64             `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	IsDeleted            bool               `protobuf:"varint,5,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	Source               FollowEvent_Source `protobuf:"varint,6,opt,name=source,proto3,enum=ugc.event.FollowEvent_Source" json:"source,omitempty"`
	ClientSource         uint32             `protobuf:"varint,7,opt,name=client_source,json=clientSource,proto3" json:"client_source,omitempty"`
	ClientCustomSource   string             `protobuf:"bytes,8,opt,name=client_custom_source,json=clientCustomSource,proto3" json:"client_custom_source,omitempty"`
	IsRobot              bool               `protobuf:"varint,9,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *FollowEvent) Reset()         { *m = FollowEvent{} }
func (m *FollowEvent) String() string { return proto.CompactTextString(m) }
func (*FollowEvent) ProtoMessage()    {}
func (*FollowEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{3}
}
func (m *FollowEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowEvent.Unmarshal(m, b)
}
func (m *FollowEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowEvent.Marshal(b, m, deterministic)
}
func (dst *FollowEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowEvent.Merge(dst, src)
}
func (m *FollowEvent) XXX_Size() int {
	return xxx_messageInfo_FollowEvent.Size(m)
}
func (m *FollowEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowEvent.DiscardUnknown(m)
}

var xxx_messageInfo_FollowEvent proto.InternalMessageInfo

func (m *FollowEvent) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *FollowEvent) GetToUserId() uint32 {
	if m != nil {
		return m.ToUserId
	}
	return 0
}

func (m *FollowEvent) GetIsFirstTime() bool {
	if m != nil {
		return m.IsFirstTime
	}
	return false
}

func (m *FollowEvent) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *FollowEvent) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

func (m *FollowEvent) GetSource() FollowEvent_Source {
	if m != nil {
		return m.Source
	}
	return FollowEvent_USER_OPERATION
}

func (m *FollowEvent) GetClientSource() uint32 {
	if m != nil {
		return m.ClientSource
	}
	return 0
}

func (m *FollowEvent) GetClientCustomSource() string {
	if m != nil {
		return m.ClientCustomSource
	}
	return ""
}

func (m *FollowEvent) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

//
type PostShareEvent struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	UserId               uint32   `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OwnerId              uint32   `protobuf:"varint,3,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	IsRobot              bool     `protobuf:"varint,10,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostShareEvent) Reset()         { *m = PostShareEvent{} }
func (m *PostShareEvent) String() string { return proto.CompactTextString(m) }
func (*PostShareEvent) ProtoMessage()    {}
func (*PostShareEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{4}
}
func (m *PostShareEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostShareEvent.Unmarshal(m, b)
}
func (m *PostShareEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostShareEvent.Marshal(b, m, deterministic)
}
func (dst *PostShareEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostShareEvent.Merge(dst, src)
}
func (m *PostShareEvent) XXX_Size() int {
	return xxx_messageInfo_PostShareEvent.Size(m)
}
func (m *PostShareEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PostShareEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PostShareEvent proto.InternalMessageInfo

func (m *PostShareEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostShareEvent) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *PostShareEvent) GetOwnerId() uint32 {
	if m != nil {
		return m.OwnerId
	}
	return 0
}

func (m *PostShareEvent) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

// 举报 帖子 / 评论
type UgcContentReport struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	CreateAt             uint64   `protobuf:"varint,3,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	ReportAt             uint64   `protobuf:"varint,4,opt,name=report_at,json=reportAt,proto3" json:"report_at,omitempty"`
	ReportType           uint32   `protobuf:"varint,5,opt,name=report_type,json=reportType,proto3" json:"report_type,omitempty"`
	OwnerUserId          uint32   `protobuf:"varint,6,opt,name=owner_user_id,json=ownerUserId,proto3" json:"owner_user_id,omitempty"`
	FromUserId           uint32   `protobuf:"varint,7,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	AttachmentKeyList    []string `protobuf:"bytes,8,rep,name=attachment_key_list,json=attachmentKeyList,proto3" json:"attachment_key_list,omitempty"`
	Content              string   `protobuf:"bytes,9,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UgcContentReport) Reset()         { *m = UgcContentReport{} }
func (m *UgcContentReport) String() string { return proto.CompactTextString(m) }
func (*UgcContentReport) ProtoMessage()    {}
func (*UgcContentReport) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{5}
}
func (m *UgcContentReport) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UgcContentReport.Unmarshal(m, b)
}
func (m *UgcContentReport) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UgcContentReport.Marshal(b, m, deterministic)
}
func (dst *UgcContentReport) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UgcContentReport.Merge(dst, src)
}
func (m *UgcContentReport) XXX_Size() int {
	return xxx_messageInfo_UgcContentReport.Size(m)
}
func (m *UgcContentReport) XXX_DiscardUnknown() {
	xxx_messageInfo_UgcContentReport.DiscardUnknown(m)
}

var xxx_messageInfo_UgcContentReport proto.InternalMessageInfo

func (m *UgcContentReport) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UgcContentReport) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UgcContentReport) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *UgcContentReport) GetReportAt() uint64 {
	if m != nil {
		return m.ReportAt
	}
	return 0
}

func (m *UgcContentReport) GetReportType() uint32 {
	if m != nil {
		return m.ReportType
	}
	return 0
}

func (m *UgcContentReport) GetOwnerUserId() uint32 {
	if m != nil {
		return m.OwnerUserId
	}
	return 0
}

func (m *UgcContentReport) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *UgcContentReport) GetAttachmentKeyList() []string {
	if m != nil {
		return m.AttachmentKeyList
	}
	return nil
}

func (m *UgcContentReport) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// 文本过滤拦截
type TextAntiReport struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	CreateAt             uint64   `protobuf:"varint,3,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	ReportAt             uint64   `protobuf:"varint,4,opt,name=report_at,json=reportAt,proto3" json:"report_at,omitempty"`
	ResultType           uint32   `protobuf:"varint,5,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	OwnerUserId          uint32   `protobuf:"varint,6,opt,name=owner_user_id,json=ownerUserId,proto3" json:"owner_user_id,omitempty"`
	LabelInfo            string   `protobuf:"bytes,7,opt,name=label_info,json=labelInfo,proto3" json:"label_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TextAntiReport) Reset()         { *m = TextAntiReport{} }
func (m *TextAntiReport) String() string { return proto.CompactTextString(m) }
func (*TextAntiReport) ProtoMessage()    {}
func (*TextAntiReport) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{6}
}
func (m *TextAntiReport) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TextAntiReport.Unmarshal(m, b)
}
func (m *TextAntiReport) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TextAntiReport.Marshal(b, m, deterministic)
}
func (dst *TextAntiReport) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TextAntiReport.Merge(dst, src)
}
func (m *TextAntiReport) XXX_Size() int {
	return xxx_messageInfo_TextAntiReport.Size(m)
}
func (m *TextAntiReport) XXX_DiscardUnknown() {
	xxx_messageInfo_TextAntiReport.DiscardUnknown(m)
}

var xxx_messageInfo_TextAntiReport proto.InternalMessageInfo

func (m *TextAntiReport) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *TextAntiReport) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *TextAntiReport) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *TextAntiReport) GetReportAt() uint64 {
	if m != nil {
		return m.ReportAt
	}
	return 0
}

func (m *TextAntiReport) GetResultType() uint32 {
	if m != nil {
		return m.ResultType
	}
	return 0
}

func (m *TextAntiReport) GetOwnerUserId() uint32 {
	if m != nil {
		return m.OwnerUserId
	}
	return 0
}

func (m *TextAntiReport) GetLabelInfo() string {
	if m != nil {
		return m.LabelInfo
	}
	return ""
}

type HotPostEvent struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Author               uint32   `protobuf:"varint,2,opt,name=author,proto3" json:"author,omitempty"`
	TopicId              string   `protobuf:"bytes,3,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HotPostEvent) Reset()         { *m = HotPostEvent{} }
func (m *HotPostEvent) String() string { return proto.CompactTextString(m) }
func (*HotPostEvent) ProtoMessage()    {}
func (*HotPostEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{7}
}
func (m *HotPostEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotPostEvent.Unmarshal(m, b)
}
func (m *HotPostEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotPostEvent.Marshal(b, m, deterministic)
}
func (dst *HotPostEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotPostEvent.Merge(dst, src)
}
func (m *HotPostEvent) XXX_Size() int {
	return xxx_messageInfo_HotPostEvent.Size(m)
}
func (m *HotPostEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_HotPostEvent.DiscardUnknown(m)
}

var xxx_messageInfo_HotPostEvent proto.InternalMessageInfo

func (m *HotPostEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *HotPostEvent) GetAuthor() uint32 {
	if m != nil {
		return m.Author
	}
	return 0
}

func (m *HotPostEvent) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type VisitEvent struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	FromUserId           uint32   `protobuf:"varint,2,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	Author               uint32   `protobuf:"varint,3,opt,name=author,proto3" json:"author,omitempty"`
	VisitAt              uint64   `protobuf:"varint,4,opt,name=visit_at,json=visitAt,proto3" json:"visit_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VisitEvent) Reset()         { *m = VisitEvent{} }
func (m *VisitEvent) String() string { return proto.CompactTextString(m) }
func (*VisitEvent) ProtoMessage()    {}
func (*VisitEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_event_78a9d07c39f30670, []int{8}
}
func (m *VisitEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VisitEvent.Unmarshal(m, b)
}
func (m *VisitEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VisitEvent.Marshal(b, m, deterministic)
}
func (dst *VisitEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VisitEvent.Merge(dst, src)
}
func (m *VisitEvent) XXX_Size() int {
	return xxx_messageInfo_VisitEvent.Size(m)
}
func (m *VisitEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_VisitEvent.DiscardUnknown(m)
}

var xxx_messageInfo_VisitEvent proto.InternalMessageInfo

func (m *VisitEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *VisitEvent) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *VisitEvent) GetAuthor() uint32 {
	if m != nil {
		return m.Author
	}
	return 0
}

func (m *VisitEvent) GetVisitAt() uint64 {
	if m != nil {
		return m.VisitAt
	}
	return 0
}

func init() {
	proto.RegisterType((*PostEvent)(nil), "ugc.event.PostEvent")
	proto.RegisterType((*CommentEvent)(nil), "ugc.event.CommentEvent")
	proto.RegisterType((*AttitudeEvent)(nil), "ugc.event.AttitudeEvent")
	proto.RegisterType((*FollowEvent)(nil), "ugc.event.FollowEvent")
	proto.RegisterType((*PostShareEvent)(nil), "ugc.event.PostShareEvent")
	proto.RegisterType((*UgcContentReport)(nil), "ugc.event.UgcContentReport")
	proto.RegisterType((*TextAntiReport)(nil), "ugc.event.TextAntiReport")
	proto.RegisterType((*HotPostEvent)(nil), "ugc.event.HotPostEvent")
	proto.RegisterType((*VisitEvent)(nil), "ugc.event.VisitEvent")
	proto.RegisterEnum("ugc.event.Action", Action_name, Action_value)
	proto.RegisterEnum("ugc.event.PostEvent_Origin", PostEvent_Origin_name, PostEvent_Origin_value)
	proto.RegisterEnum("ugc.event.FollowEvent_Source", FollowEvent_Source_name, FollowEvent_Source_value)
}

func init() { proto.RegisterFile("ugc/event.proto", fileDescriptor_event_78a9d07c39f30670) }

var fileDescriptor_event_78a9d07c39f30670 = []byte{
	// 1236 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x56, 0xdb, 0x6e, 0xdb, 0x46,
	0x13, 0xb6, 0x0e, 0x96, 0xc8, 0xd1, 0xc1, 0xf4, 0x26, 0x7f, 0x7e, 0x1a, 0x89, 0x1b, 0x45, 0x69,
	0x51, 0xf5, 0xe4, 0x9e, 0xd0, 0x07, 0xa0, 0x65, 0xba, 0x26, 0xe2, 0xca, 0x02, 0x25, 0xb9, 0x70,
	0x6e, 0x16, 0x34, 0xb9, 0x56, 0x16, 0x95, 0xb4, 0x02, 0x77, 0xe9, 0x44, 0x7d, 0x93, 0x5e, 0xf4,
	0x31, 0xf2, 0x26, 0x45, 0x1f, 0xa1, 0xcf, 0x51, 0xec, 0xc1, 0x32, 0x29, 0x15, 0x49, 0x80, 0x5e,
	0xf4, 0x72, 0xe6, 0x9b, 0xdd, 0x19, 0x7e, 0xf3, 0xcd, 0x2c, 0x61, 0x2f, 0x9b, 0xc6, 0x5f, 0x93,
	0x5b, 0xb2, 0x10, 0x47, 0xcb, 0x94, 0x09, 0x86, 0xec, 0x6c, 0x1a, 0x1f, 0x29, 0x47, 0xf7, 0x8f,
	0x2a, 0xd8, 0x43, 0xc6, 0x85, 0x2f, 0x2d, 0xf4, 0x7f, 0xa8, 0x2f, 0x19, 0x17, 0x98, 0x26, 0x6e,
	0xa9, 0x53, 0xea, 0xd9, 0x61, 0x4d, 0x9a, 0x41, 0x82, 0x0e, 0xc0, 0x12, 0x6c, 0x49, 0x63, 0x89,
	0x94, 0x15, 0x52, 0x57, 0x76, 0x90, 0xa0, 0x47, 0x50, 0xe3, 0x22, 0x12, 0x19, 0x77, 0x2b, 0x9d,
	0x52, 0xaf, 0x15, 0x1a, 0x4b, 0xde, 0x95, 0x71, 0x92, 0xca, 0x13, 0x55, 0x0d, 0x48, 0x33, 0x48,
	0xd0, 0x63, 0xb0, 0xe3, 0x94, 0x44, 0x82, 0xe0, 0x48, 0xb8, 0xbb, 0x9d, 0x52, 0xaf, 0x1a, 0x5a,
	0xda, 0xe1, 0x09, 0x09, 0xaa, 0x0a, 0xc4, 0x6a, 0x49, 0xdc, 0x9a, 0x3a, 0x67, 0x49, 0xc7, 0x78,
	0xb5, 0x24, 0x32, 0x15, 0x4b, 0xe9, 0x94, 0x2e, 0xdc, 0xba, 0xbe, 0x51, 0x5b, 0xa8, 0x03, 0x4d,
	0x9e, 0x5d, 0xe3, 0x75, 0x85, 0x96, 0xaa, 0x10, 0x78, 0x76, 0x3d, 0x36, 0x45, 0x3e, 0x83, 0x66,
	0xcc, 0x16, 0x82, 0x2c, 0xcc, 0xcd, 0x76, 0xa7, 0xd4, 0xdb, 0x0d, 0x1b, 0xc6, 0xa7, 0x2e, 0x7f,
	0x0a, 0x0d, 0x95, 0xd9, 0x64, 0x00, 0x95, 0x01, 0xa4, 0xeb, 0x42, 0x67, 0x79, 0x0c, 0x36, 0xe5,
	0x98, 0xaf, 0xb8, 0x20, 0x73, 0xb7, 0xa1, 0x4b, 0xa3, 0x7c, 0xa4, 0x6c, 0x59, 0xc2, 0x94, 0xb0,
	0xfb, 0x12, 0x9a, 0xba, 0x84, 0x29, 0x61, 0x77, 0x25, 0xb8, 0x50, 0x37, 0xe9, 0xdc, 0x96, 0x66,
	0xd0, 0x98, 0xe8, 0x13, 0x68, 0x2f, 0x53, 0x7a, 0x1b, 0xc5, 0x2b, 0xbc, 0x64, 0x33, 0x1a, 0xaf,
	0xdc, 0xb6, 0xba, 0xbd, 0x65, 0xbc, 0x43, 0xe5, 0x44, 0x9f, 0x41, 0x2d, 0x8a, 0x05, 0x65, 0x0b,
	0x77, 0xaf, 0x53, 0xea, 0xb5, 0xbf, 0xdb, 0x3f, 0x5a, 0xb7, 0xf1, 0xc8, 0x53, 0x40, 0x68, 0x02,
	0x64, 0xa9, 0xd9, 0x32, 0x31, 0x14, 0x3b, 0x9d, 0x52, 0xaf, 0x12, 0x5a, 0xda, 0xe1, 0x09, 0xd4,
	0x85, 0x56, 0x42, 0x57, 0xeb, 0x52, 0xb9, 0xbb, 0xdf, 0xa9, 0xf4, 0xec, 0xb0, 0x91, 0xd0, 0x95,
	0xa9, 0x95, 0xab, 0x1e, 0xcd, 0xa8, 0xa4, 0x8b, 0x2e, 0x5d, 0xa4, 0xbf, 0x55, 0x3b, 0x82, 0x65,
	0xf7, 0x19, 0xd4, 0x0c, 0x25, 0x00, 0xb5, 0xc1, 0x45, 0xf8, 0x93, 0x77, 0xee, 0xec, 0xa0, 0x06,
	0xd4, 0x8f, 0xbd, 0xfe, 0x0b, 0x7f, 0x70, 0xe2, 0x94, 0xba, 0x7f, 0x56, 0xa1, 0xd9, 0x67, 0xf3,
	0x39, 0x59, 0xfc, 0x0b, 0x65, 0x1d, 0x02, 0xc4, 0xfa, 0x0e, 0x09, 0x56, 0x14, 0x68, 0x1b, 0x4f,
	0x90, 0xa0, 0xaf, 0xe0, 0x41, 0x4a, 0x96, 0x33, 0xf9, 0x25, 0x38, 0x17, 0x57, 0x55, 0x71, 0x8e,
	0x82, 0xc6, 0xac, 0xbf, 0x0e, 0xff, 0x14, 0xf6, 0x62, 0xb6, 0xb8, 0x25, 0x29, 0x8f, 0x24, 0x47,
	0x32, 0x74, 0x57, 0x85, 0xb6, 0xf3, 0xee, 0x20, 0x91, 0xad, 0xbc, 0x49, 0xd9, 0x1c, 0xdf, 0xa9,
	0x57, 0xab, 0x10, 0xa4, 0x6f, 0xa2, 0x15, 0xfc, 0x04, 0x40, 0xb0, 0x35, 0xae, 0xb5, 0x68, 0x09,
	0x66, 0xd0, 0xfb, 0x81, 0xb0, 0x0a, 0x03, 0x51, 0xd0, 0xbd, 0xbd, 0xa1, 0xfb, 0x2f, 0x00, 0x69,
	0xf5, 0xbd, 0x5e, 0x90, 0x74, 0x7d, 0xb5, 0x16, 0xe1, 0x9e, 0x12, 0xa1, 0x04, 0x4c, 0x06, 0x0f,
	0x3e, 0xda, 0xfa, 0xf2, 0xe2, 0x41, 0x2d, 0xcf, 0x83, 0x22, 0x09, 0xf9, 0x2b, 0x36, 0x07, 0xa2,
	0xb9, 0x3d, 0x10, 0x07, 0x60, 0x51, 0x8e, 0x53, 0x76, 0xcd, 0xb4, 0x62, 0xad, 0xb0, 0x4e, 0x79,
	0x28, 0x4d, 0xf5, 0x89, 0x2c, 0x4b, 0x63, 0x62, 0x94, 0x6a, 0x2c, 0xf4, 0x2d, 0xfc, 0x8f, 0x72,
	0x7c, 0x1b, 0xcd, 0x68, 0x82, 0x65, 0xd1, 0xb2, 0x98, 0x85, 0x20, 0xa9, 0x52, 0xac, 0x15, 0x22,
	0xca, 0x2f, 0x25, 0x36, 0x54, 0x50, 0x20, 0x11, 0xd9, 0xc5, 0xf5, 0x11, 0x5d, 0xbd, 0x3a, 0xe0,
	0xa8, 0x03, 0x8e, 0x39, 0xa0, 0x8a, 0x96, 0xfe, 0xee, 0xdb, 0x32, 0xb4, 0x3c, 0x21, 0xa8, 0xc8,
	0x12, 0xa2, 0x95, 0x75, 0xb0, 0xa1, 0xac, 0xb3, 0x9d, 0xb5, 0xb6, 0x9e, 0x16, 0x04, 0x54, 0x36,
	0x68, 0x4e, 0x42, 0xcf, 0xa1, 0x15, 0x99, 0xcb, 0x34, 0x0d, 0x7a, 0x85, 0x35, 0xef, 0x9c, 0x8a,
	0x87, 0x42, 0xdf, 0xaa, 0x1b, 0x7d, 0xcb, 0x6d, 0xb9, 0xdd, 0xc2, 0x96, 0xfb, 0x12, 0x90, 0x88,
	0xd2, 0x29, 0x11, 0x58, 0xc5, 0xb2, 0x14, 0x67, 0x6b, 0x2d, 0x39, 0x1a, 0xe9, 0x6b, 0x60, 0x42,
	0x13, 0x39, 0x93, 0x94, 0xe3, 0x1b, 0x9a, 0xca, 0xd5, 0x47, 0xe7, 0x44, 0x89, 0xca, 0x0a, 0x1b,
	0x94, 0x9f, 0x4a, 0xdf, 0x98, 0xce, 0x49, 0x8e, 0x74, 0xab, 0x40, 0x7a, 0xbe, 0x4f, 0x0f, 0x0b,
	0x7d, 0x3a, 0xb6, 0xa0, 0xa6, 0x53, 0x75, 0x7f, 0xab, 0x40, 0xe3, 0x94, 0xcd, 0x66, 0xec, 0xb5,
	0x66, 0x6d, 0x53, 0xe4, 0xa5, 0xf7, 0x88, 0xbc, 0xbc, 0x21, 0xf2, 0xad, 0x82, 0x2b, 0xdb, 0x05,
	0xbf, 0x93, 0xb8, 0x43, 0x00, 0xca, 0x71, 0x42, 0x66, 0x44, 0x10, 0xcd, 0x9d, 0x15, 0xda, 0x94,
	0x9f, 0x68, 0x07, 0xfa, 0x61, 0xfd, 0xb1, 0x35, 0xb5, 0xec, 0x0e, 0x73, 0xcb, 0x2e, 0xf7, 0x1d,
	0x47, 0x23, 0x15, 0xb4, 0xe6, 0xe2, 0x39, 0xb4, 0xcc, 0xde, 0x32, 0xa7, 0xf5, 0x70, 0x36, 0xb5,
	0x53, 0x07, 0xa3, 0x6f, 0xe0, 0xa1, 0x09, 0x8a, 0x33, 0x2e, 0xd8, 0x1c, 0xe7, 0x68, 0xb5, 0x43,
	0xa4, 0xb1, 0xbe, 0x82, 0x46, 0xdb, 0x14, 0xdb, 0x05, 0x8a, 0xbb, 0x1e, 0xd4, 0x4c, 0x10, 0x82,
	0xf6, 0x64, 0xe4, 0x87, 0xf8, 0x62, 0xe8, 0x87, 0xde, 0x38, 0xb8, 0x18, 0x38, 0x3b, 0xc8, 0x81,
	0xe6, 0xe8, 0x6a, 0xd0, 0x3f, 0xbb, 0x08, 0x07, 0xc1, 0x4b, 0x3f, 0x74, 0x4a, 0x68, 0x1f, 0x5a,
	0xa7, 0x61, 0xe0, 0x0f, 0x4e, 0xf0, 0xa5, 0x1f, 0x06, 0xa7, 0x57, 0x4e, 0xb9, 0xfb, 0x06, 0xda,
	0x72, 0x22, 0x46, 0xaf, 0xa2, 0x94, 0xbc, 0x67, 0x5b, 0xe6, 0xe4, 0x56, 0x2e, 0xc8, 0xed, 0x00,
	0x2c, 0xbd, 0x01, 0xcc, 0xa6, 0x6c, 0x85, 0x75, 0x65, 0x6b, 0x68, 0x5d, 0x3c, 0x14, 0x8b, 0x7f,
	0x5b, 0x06, 0x67, 0x32, 0x8d, 0xfb, 0x7a, 0xea, 0x43, 0xb2, 0x64, 0xe9, 0x3b, 0x92, 0x1f, 0x6e,
	0x8f, 0x53, 0x7e, 0x98, 0x0a, 0xed, 0xae, 0x6c, 0xbf, 0xeb, 0xa9, 0xba, 0x3e, 0xa7, 0x05, 0xed,
	0xf0, 0x84, 0x7c, 0x7a, 0x0d, 0xa8, 0x86, 0x50, 0x0f, 0x12, 0x68, 0x97, 0x1a, 0xc1, 0x2e, 0xb4,
	0x8a, 0xfb, 0x4d, 0xcf, 0x51, 0x83, 0xe5, 0x36, 0xda, 0xa6, 0xa2, 0xeb, 0x5b, 0x8a, 0x3e, 0x82,
	0x07, 0x91, 0x10, 0x51, 0xfc, 0x4a, 0x7d, 0xc2, 0x2f, 0x64, 0x85, 0x67, 0x94, 0x0b, 0xd7, 0x52,
	0xcf, 0xdf, 0xfe, 0x3d, 0xf4, 0x82, 0xac, 0xce, 0x29, 0x17, 0xf9, 0x17, 0xdb, 0x2e, 0xbc, 0xd8,
	0xdd, 0xbf, 0x4a, 0xd0, 0x1e, 0x93, 0x37, 0xc2, 0x5b, 0x08, 0xfa, 0xdf, 0xb2, 0xc6, 0xb3, 0xd9,
	0x26, 0x6b, 0xd2, 0xf5, 0xc1, 0xac, 0x1d, 0x02, 0xcc, 0xa2, 0x6b, 0x32, 0xc3, 0x74, 0x71, 0xc3,
	0x14, 0x67, 0x76, 0x68, 0x2b, 0x4f, 0xb0, 0xb8, 0x61, 0xdd, 0x97, 0xd0, 0x3c, 0x63, 0xe2, 0x03,
	0x7e, 0x10, 0x1f, 0x41, 0x2d, 0xca, 0xc4, 0x2b, 0x96, 0xde, 0xe9, 0x52, 0x5b, 0x85, 0xe7, 0xbd,
	0x52, 0x78, 0xde, 0xbb, 0xbf, 0x02, 0x5c, 0x52, 0x4e, 0xdf, 0x77, 0xf3, 0x66, 0x5f, 0xcb, 0x5b,
	0x7d, 0xbd, 0xcf, 0x5d, 0xd9, 0xcc, 0x7d, 0x2b, 0x13, 0xdc, 0x93, 0x57, 0x57, 0xb6, 0x27, 0x3e,
	0xff, 0xbd, 0x04, 0x35, 0xfd, 0xcf, 0x84, 0x1e, 0x82, 0xe3, 0xf5, 0xe5, 0xb8, 0xe2, 0xc9, 0xe0,
	0xc4, 0x3f, 0x0d, 0x06, 0xfe, 0x89, 0xb3, 0x83, 0x9e, 0xc1, 0xa1, 0xf1, 0x1e, 0x7b, 0x03, 0xfc,
	0x73, 0x30, 0x3e, 0xc3, 0xfa, 0x4f, 0x07, 0x8f, 0xc6, 0xde, 0x78, 0x32, 0x72, 0x4a, 0xe8, 0x63,
	0xe8, 0x98, 0x90, 0x13, 0xff, 0xdc, 0x1f, 0xfb, 0xff, 0x14, 0x55, 0x46, 0x1d, 0x78, 0x62, 0xa2,
	0xfa, 0x67, 0xde, 0xe0, 0x47, 0x1f, 0x0f, 0xc3, 0xe0, 0xd2, 0xeb, 0x5f, 0xe1, 0xe1, 0xc5, 0x79,
	0xd0, 0xbf, 0x72, 0x2a, 0x72, 0x6f, 0x98, 0x88, 0xe1, 0xe4, 0xf8, 0x3c, 0x18, 0x9d, 0x39, 0xd5,
	0xeb, 0x9a, 0xfa, 0x51, 0xff, 0xfe, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0xd0, 0x27, 0xad, 0x13,
	0xbb, 0x0b, 0x00, 0x00,
}
