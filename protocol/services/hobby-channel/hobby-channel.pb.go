// Code generated by protoc-gen-go. DO NOT EDIT.
// source: hobby-channel/hobby-channel.proto

package hobby_channel // import "golang.52tt.com/protocol/services/hobby-channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type REGULATORY_LEVEL int32

const (
	REGULATORY_LEVEL_FREE         REGULATORY_LEVEL = 0
	REGULATORY_LEVEL_SIMPLE_MINOR REGULATORY_LEVEL = 1
)

var REGULATORY_LEVEL_name = map[int32]string{
	0: "FREE",
	1: "SIMPLE_MINOR",
}
var REGULATORY_LEVEL_value = map[string]int32{
	"FREE":         0,
	"SIMPLE_MINOR": 1,
}

func (x REGULATORY_LEVEL) String() string {
	return proto.EnumName(REGULATORY_LEVEL_name, int32(x))
}
func (REGULATORY_LEVEL) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{0}
}

type EntryType int32

const (
	EntryType_GameHomePageEntry    EntryType = 0
	EntryType_MysteryHomePageEntry EntryType = 1
)

var EntryType_name = map[int32]string{
	0: "GameHomePageEntry",
	1: "MysteryHomePageEntry",
}
var EntryType_value = map[string]int32{
	"GameHomePageEntry":    0,
	"MysteryHomePageEntry": 1,
}

func (x EntryType) String() string {
	return proto.EnumName(EntryType_name, int32(x))
}
func (EntryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{1}
}

type HighQualityChannelInfoStatusType int32

const (
	HighQualityChannelInfoStatusType_UNKNOWN    HighQualityChannelInfoStatusType = 0
	HighQualityChannelInfoStatusType_NOT_ACTIVE HighQualityChannelInfoStatusType = 1
	HighQualityChannelInfoStatusType_ACTIVE     HighQualityChannelInfoStatusType = 2
	HighQualityChannelInfoStatusType_INVALID    HighQualityChannelInfoStatusType = 3
)

var HighQualityChannelInfoStatusType_name = map[int32]string{
	0: "UNKNOWN",
	1: "NOT_ACTIVE",
	2: "ACTIVE",
	3: "INVALID",
}
var HighQualityChannelInfoStatusType_value = map[string]int32{
	"UNKNOWN":    0,
	"NOT_ACTIVE": 1,
	"ACTIVE":     2,
	"INVALID":    3,
}

func (x HighQualityChannelInfoStatusType) String() string {
	return proto.EnumName(HighQualityChannelInfoStatusType_name, int32(x))
}
func (HighQualityChannelInfoStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{2}
}

type BatchChannelOwnerDurationReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchChannelOwnerDurationReq) Reset()         { *m = BatchChannelOwnerDurationReq{} }
func (m *BatchChannelOwnerDurationReq) String() string { return proto.CompactTextString(m) }
func (*BatchChannelOwnerDurationReq) ProtoMessage()    {}
func (*BatchChannelOwnerDurationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{0}
}
func (m *BatchChannelOwnerDurationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchChannelOwnerDurationReq.Unmarshal(m, b)
}
func (m *BatchChannelOwnerDurationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchChannelOwnerDurationReq.Marshal(b, m, deterministic)
}
func (dst *BatchChannelOwnerDurationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchChannelOwnerDurationReq.Merge(dst, src)
}
func (m *BatchChannelOwnerDurationReq) XXX_Size() int {
	return xxx_messageInfo_BatchChannelOwnerDurationReq.Size(m)
}
func (m *BatchChannelOwnerDurationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchChannelOwnerDurationReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchChannelOwnerDurationReq proto.InternalMessageInfo

func (m *BatchChannelOwnerDurationReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type BatchChannelOwnerDurationResp struct {
	ChannelOwnerDurationMap map[uint32]int64 `protobuf:"bytes,1,rep,name=channel_owner_duration_map,json=channelOwnerDurationMap,proto3" json:"channel_owner_duration_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral    struct{}         `json:"-"`
	XXX_unrecognized        []byte           `json:"-"`
	XXX_sizecache           int32            `json:"-"`
}

func (m *BatchChannelOwnerDurationResp) Reset()         { *m = BatchChannelOwnerDurationResp{} }
func (m *BatchChannelOwnerDurationResp) String() string { return proto.CompactTextString(m) }
func (*BatchChannelOwnerDurationResp) ProtoMessage()    {}
func (*BatchChannelOwnerDurationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{1}
}
func (m *BatchChannelOwnerDurationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchChannelOwnerDurationResp.Unmarshal(m, b)
}
func (m *BatchChannelOwnerDurationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchChannelOwnerDurationResp.Marshal(b, m, deterministic)
}
func (dst *BatchChannelOwnerDurationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchChannelOwnerDurationResp.Merge(dst, src)
}
func (m *BatchChannelOwnerDurationResp) XXX_Size() int {
	return xxx_messageInfo_BatchChannelOwnerDurationResp.Size(m)
}
func (m *BatchChannelOwnerDurationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchChannelOwnerDurationResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchChannelOwnerDurationResp proto.InternalMessageInfo

func (m *BatchChannelOwnerDurationResp) GetChannelOwnerDurationMap() map[uint32]int64 {
	if m != nil {
		return m.ChannelOwnerDurationMap
	}
	return nil
}

type ListMatchElementConfigReq struct {
	Name                 []string `protobuf:"bytes,1,rep,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListMatchElementConfigReq) Reset()         { *m = ListMatchElementConfigReq{} }
func (m *ListMatchElementConfigReq) String() string { return proto.CompactTextString(m) }
func (*ListMatchElementConfigReq) ProtoMessage()    {}
func (*ListMatchElementConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{2}
}
func (m *ListMatchElementConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMatchElementConfigReq.Unmarshal(m, b)
}
func (m *ListMatchElementConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMatchElementConfigReq.Marshal(b, m, deterministic)
}
func (dst *ListMatchElementConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMatchElementConfigReq.Merge(dst, src)
}
func (m *ListMatchElementConfigReq) XXX_Size() int {
	return xxx_messageInfo_ListMatchElementConfigReq.Size(m)
}
func (m *ListMatchElementConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMatchElementConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListMatchElementConfigReq proto.InternalMessageInfo

func (m *ListMatchElementConfigReq) GetName() []string {
	if m != nil {
		return m.Name
	}
	return nil
}

type GetElementConfigResp struct {
	EleConfig            []*GetElementConfigResp_ElementConfig `protobuf:"bytes,1,rep,name=ele_config,json=eleConfig,proto3" json:"ele_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *GetElementConfigResp) Reset()         { *m = GetElementConfigResp{} }
func (m *GetElementConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetElementConfigResp) ProtoMessage()    {}
func (*GetElementConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{3}
}
func (m *GetElementConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetElementConfigResp.Unmarshal(m, b)
}
func (m *GetElementConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetElementConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetElementConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetElementConfigResp.Merge(dst, src)
}
func (m *GetElementConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetElementConfigResp.Size(m)
}
func (m *GetElementConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetElementConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetElementConfigResp proto.InternalMessageInfo

func (m *GetElementConfigResp) GetEleConfig() []*GetElementConfigResp_ElementConfig {
	if m != nil {
		return m.EleConfig
	}
	return nil
}

type GetElementConfigResp_ElementConfig struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ElementId            string   `protobuf:"bytes,2,opt,name=element_id,json=elementId,proto3" json:"element_id,omitempty"`
	ElementName          string   `protobuf:"bytes,3,opt,name=element_name,json=elementName,proto3" json:"element_name,omitempty"`
	VideoUrl             string   `protobuf:"bytes,4,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	VideoType            string   `protobuf:"bytes,5,opt,name=video_type,json=videoType,proto3" json:"video_type,omitempty"`
	ImgUrl               string   `protobuf:"bytes,6,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	TabId                uint32   `protobuf:"varint,7,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,8,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Uid                  uint32   `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
	UserName             string   `protobuf:"bytes,10,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	CreateAt             int64    `protobuf:"varint,11,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	UpdateAt             int64    `protobuf:"varint,12,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetElementConfigResp_ElementConfig) Reset()         { *m = GetElementConfigResp_ElementConfig{} }
func (m *GetElementConfigResp_ElementConfig) String() string { return proto.CompactTextString(m) }
func (*GetElementConfigResp_ElementConfig) ProtoMessage()    {}
func (*GetElementConfigResp_ElementConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{3, 0}
}
func (m *GetElementConfigResp_ElementConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetElementConfigResp_ElementConfig.Unmarshal(m, b)
}
func (m *GetElementConfigResp_ElementConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetElementConfigResp_ElementConfig.Marshal(b, m, deterministic)
}
func (dst *GetElementConfigResp_ElementConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetElementConfigResp_ElementConfig.Merge(dst, src)
}
func (m *GetElementConfigResp_ElementConfig) XXX_Size() int {
	return xxx_messageInfo_GetElementConfigResp_ElementConfig.Size(m)
}
func (m *GetElementConfigResp_ElementConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_GetElementConfigResp_ElementConfig.DiscardUnknown(m)
}

var xxx_messageInfo_GetElementConfigResp_ElementConfig proto.InternalMessageInfo

func (m *GetElementConfigResp_ElementConfig) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetElementConfigResp_ElementConfig) GetElementId() string {
	if m != nil {
		return m.ElementId
	}
	return ""
}

func (m *GetElementConfigResp_ElementConfig) GetElementName() string {
	if m != nil {
		return m.ElementName
	}
	return ""
}

func (m *GetElementConfigResp_ElementConfig) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *GetElementConfigResp_ElementConfig) GetVideoType() string {
	if m != nil {
		return m.VideoType
	}
	return ""
}

func (m *GetElementConfigResp_ElementConfig) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *GetElementConfigResp_ElementConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetElementConfigResp_ElementConfig) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GetElementConfigResp_ElementConfig) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetElementConfigResp_ElementConfig) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *GetElementConfigResp_ElementConfig) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *GetElementConfigResp_ElementConfig) GetUpdateAt() int64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

type UpsertElementConfigReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ElementId            string   `protobuf:"bytes,2,opt,name=element_id,json=elementId,proto3" json:"element_id,omitempty"`
	VideoUrl             string   `protobuf:"bytes,3,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	VideoType            string   `protobuf:"bytes,4,opt,name=video_type,json=videoType,proto3" json:"video_type,omitempty"`
	ImgUrl               string   `protobuf:"bytes,5,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	TabId                uint32   `protobuf:"varint,6,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`
	UserName             string   `protobuf:"bytes,8,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertElementConfigReq) Reset()         { *m = UpsertElementConfigReq{} }
func (m *UpsertElementConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertElementConfigReq) ProtoMessage()    {}
func (*UpsertElementConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{4}
}
func (m *UpsertElementConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertElementConfigReq.Unmarshal(m, b)
}
func (m *UpsertElementConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertElementConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertElementConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertElementConfigReq.Merge(dst, src)
}
func (m *UpsertElementConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertElementConfigReq.Size(m)
}
func (m *UpsertElementConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertElementConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertElementConfigReq proto.InternalMessageInfo

func (m *UpsertElementConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpsertElementConfigReq) GetElementId() string {
	if m != nil {
		return m.ElementId
	}
	return ""
}

func (m *UpsertElementConfigReq) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *UpsertElementConfigReq) GetVideoType() string {
	if m != nil {
		return m.VideoType
	}
	return ""
}

func (m *UpsertElementConfigReq) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *UpsertElementConfigReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UpsertElementConfigReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpsertElementConfigReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

type UpsertElementConfigResp struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertElementConfigResp) Reset()         { *m = UpsertElementConfigResp{} }
func (m *UpsertElementConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertElementConfigResp) ProtoMessage()    {}
func (*UpsertElementConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{5}
}
func (m *UpsertElementConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertElementConfigResp.Unmarshal(m, b)
}
func (m *UpsertElementConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertElementConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertElementConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertElementConfigResp.Merge(dst, src)
}
func (m *UpsertElementConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertElementConfigResp.Size(m)
}
func (m *UpsertElementConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertElementConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertElementConfigResp proto.InternalMessageInfo

func (m *UpsertElementConfigResp) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type ListElementConfigsReq struct {
	ElementId            string   `protobuf:"bytes,1,opt,name=element_id,json=elementId,proto3" json:"element_id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListElementConfigsReq) Reset()         { *m = ListElementConfigsReq{} }
func (m *ListElementConfigsReq) String() string { return proto.CompactTextString(m) }
func (*ListElementConfigsReq) ProtoMessage()    {}
func (*ListElementConfigsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{6}
}
func (m *ListElementConfigsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListElementConfigsReq.Unmarshal(m, b)
}
func (m *ListElementConfigsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListElementConfigsReq.Marshal(b, m, deterministic)
}
func (dst *ListElementConfigsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListElementConfigsReq.Merge(dst, src)
}
func (m *ListElementConfigsReq) XXX_Size() int {
	return xxx_messageInfo_ListElementConfigsReq.Size(m)
}
func (m *ListElementConfigsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListElementConfigsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListElementConfigsReq proto.InternalMessageInfo

func (m *ListElementConfigsReq) GetElementId() string {
	if m != nil {
		return m.ElementId
	}
	return ""
}

func (m *ListElementConfigsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type ListElementConfigsResp struct {
	Configs              []*ListElementConfigsResp_ElementConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *ListElementConfigsResp) Reset()         { *m = ListElementConfigsResp{} }
func (m *ListElementConfigsResp) String() string { return proto.CompactTextString(m) }
func (*ListElementConfigsResp) ProtoMessage()    {}
func (*ListElementConfigsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{7}
}
func (m *ListElementConfigsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListElementConfigsResp.Unmarshal(m, b)
}
func (m *ListElementConfigsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListElementConfigsResp.Marshal(b, m, deterministic)
}
func (dst *ListElementConfigsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListElementConfigsResp.Merge(dst, src)
}
func (m *ListElementConfigsResp) XXX_Size() int {
	return xxx_messageInfo_ListElementConfigsResp.Size(m)
}
func (m *ListElementConfigsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListElementConfigsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListElementConfigsResp proto.InternalMessageInfo

func (m *ListElementConfigsResp) GetConfigs() []*ListElementConfigsResp_ElementConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type ListElementConfigsResp_ElementConfig struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ElementId            string   `protobuf:"bytes,2,opt,name=element_id,json=elementId,proto3" json:"element_id,omitempty"`
	ElementName          string   `protobuf:"bytes,3,opt,name=element_name,json=elementName,proto3" json:"element_name,omitempty"`
	VideoUrl             string   `protobuf:"bytes,4,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	VideoType            string   `protobuf:"bytes,5,opt,name=video_type,json=videoType,proto3" json:"video_type,omitempty"`
	ImgUrl               string   `protobuf:"bytes,6,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	TabId                uint32   `protobuf:"varint,7,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,8,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Uid                  uint32   `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
	UserName             string   `protobuf:"bytes,10,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	CreateAt             int64    `protobuf:"varint,11,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	UpdateAt             int64    `protobuf:"varint,12,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListElementConfigsResp_ElementConfig) Reset()         { *m = ListElementConfigsResp_ElementConfig{} }
func (m *ListElementConfigsResp_ElementConfig) String() string { return proto.CompactTextString(m) }
func (*ListElementConfigsResp_ElementConfig) ProtoMessage()    {}
func (*ListElementConfigsResp_ElementConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{7, 0}
}
func (m *ListElementConfigsResp_ElementConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListElementConfigsResp_ElementConfig.Unmarshal(m, b)
}
func (m *ListElementConfigsResp_ElementConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListElementConfigsResp_ElementConfig.Marshal(b, m, deterministic)
}
func (dst *ListElementConfigsResp_ElementConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListElementConfigsResp_ElementConfig.Merge(dst, src)
}
func (m *ListElementConfigsResp_ElementConfig) XXX_Size() int {
	return xxx_messageInfo_ListElementConfigsResp_ElementConfig.Size(m)
}
func (m *ListElementConfigsResp_ElementConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ListElementConfigsResp_ElementConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ListElementConfigsResp_ElementConfig proto.InternalMessageInfo

func (m *ListElementConfigsResp_ElementConfig) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ListElementConfigsResp_ElementConfig) GetElementId() string {
	if m != nil {
		return m.ElementId
	}
	return ""
}

func (m *ListElementConfigsResp_ElementConfig) GetElementName() string {
	if m != nil {
		return m.ElementName
	}
	return ""
}

func (m *ListElementConfigsResp_ElementConfig) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *ListElementConfigsResp_ElementConfig) GetVideoType() string {
	if m != nil {
		return m.VideoType
	}
	return ""
}

func (m *ListElementConfigsResp_ElementConfig) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *ListElementConfigsResp_ElementConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ListElementConfigsResp_ElementConfig) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ListElementConfigsResp_ElementConfig) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListElementConfigsResp_ElementConfig) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *ListElementConfigsResp_ElementConfig) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *ListElementConfigsResp_ElementConfig) GetUpdateAt() int64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

type DelElementConfigReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelElementConfigReq) Reset()         { *m = DelElementConfigReq{} }
func (m *DelElementConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelElementConfigReq) ProtoMessage()    {}
func (*DelElementConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{8}
}
func (m *DelElementConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelElementConfigReq.Unmarshal(m, b)
}
func (m *DelElementConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelElementConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelElementConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelElementConfigReq.Merge(dst, src)
}
func (m *DelElementConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelElementConfigReq.Size(m)
}
func (m *DelElementConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelElementConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelElementConfigReq proto.InternalMessageInfo

func (m *DelElementConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelElementConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelElementConfigResp) Reset()         { *m = DelElementConfigResp{} }
func (m *DelElementConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelElementConfigResp) ProtoMessage()    {}
func (*DelElementConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{9}
}
func (m *DelElementConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelElementConfigResp.Unmarshal(m, b)
}
func (m *DelElementConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelElementConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelElementConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelElementConfigResp.Merge(dst, src)
}
func (m *DelElementConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelElementConfigResp.Size(m)
}
func (m *DelElementConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelElementConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelElementConfigResp proto.InternalMessageInfo

type UpsertElementReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Note                 string   `protobuf:"bytes,3,opt,name=note,proto3" json:"note,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	UserName             string   `protobuf:"bytes,5,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertElementReq) Reset()         { *m = UpsertElementReq{} }
func (m *UpsertElementReq) String() string { return proto.CompactTextString(m) }
func (*UpsertElementReq) ProtoMessage()    {}
func (*UpsertElementReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{10}
}
func (m *UpsertElementReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertElementReq.Unmarshal(m, b)
}
func (m *UpsertElementReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertElementReq.Marshal(b, m, deterministic)
}
func (dst *UpsertElementReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertElementReq.Merge(dst, src)
}
func (m *UpsertElementReq) XXX_Size() int {
	return xxx_messageInfo_UpsertElementReq.Size(m)
}
func (m *UpsertElementReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertElementReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertElementReq proto.InternalMessageInfo

func (m *UpsertElementReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpsertElementReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpsertElementReq) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *UpsertElementReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpsertElementReq) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

type UpsertElementResp struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertElementResp) Reset()         { *m = UpsertElementResp{} }
func (m *UpsertElementResp) String() string { return proto.CompactTextString(m) }
func (*UpsertElementResp) ProtoMessage()    {}
func (*UpsertElementResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{11}
}
func (m *UpsertElementResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertElementResp.Unmarshal(m, b)
}
func (m *UpsertElementResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertElementResp.Marshal(b, m, deterministic)
}
func (dst *UpsertElementResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertElementResp.Merge(dst, src)
}
func (m *UpsertElementResp) XXX_Size() int {
	return xxx_messageInfo_UpsertElementResp.Size(m)
}
func (m *UpsertElementResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertElementResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertElementResp proto.InternalMessageInfo

func (m *UpsertElementResp) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelElementReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelElementReq) Reset()         { *m = DelElementReq{} }
func (m *DelElementReq) String() string { return proto.CompactTextString(m) }
func (*DelElementReq) ProtoMessage()    {}
func (*DelElementReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{12}
}
func (m *DelElementReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelElementReq.Unmarshal(m, b)
}
func (m *DelElementReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelElementReq.Marshal(b, m, deterministic)
}
func (dst *DelElementReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelElementReq.Merge(dst, src)
}
func (m *DelElementReq) XXX_Size() int {
	return xxx_messageInfo_DelElementReq.Size(m)
}
func (m *DelElementReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelElementReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelElementReq proto.InternalMessageInfo

func (m *DelElementReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelElementResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelElementResp) Reset()         { *m = DelElementResp{} }
func (m *DelElementResp) String() string { return proto.CompactTextString(m) }
func (*DelElementResp) ProtoMessage()    {}
func (*DelElementResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{13}
}
func (m *DelElementResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelElementResp.Unmarshal(m, b)
}
func (m *DelElementResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelElementResp.Marshal(b, m, deterministic)
}
func (dst *DelElementResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelElementResp.Merge(dst, src)
}
func (m *DelElementResp) XXX_Size() int {
	return xxx_messageInfo_DelElementResp.Size(m)
}
func (m *DelElementResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelElementResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelElementResp proto.InternalMessageInfo

type ListElementsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListElementsReq) Reset()         { *m = ListElementsReq{} }
func (m *ListElementsReq) String() string { return proto.CompactTextString(m) }
func (*ListElementsReq) ProtoMessage()    {}
func (*ListElementsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{14}
}
func (m *ListElementsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListElementsReq.Unmarshal(m, b)
}
func (m *ListElementsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListElementsReq.Marshal(b, m, deterministic)
}
func (dst *ListElementsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListElementsReq.Merge(dst, src)
}
func (m *ListElementsReq) XXX_Size() int {
	return xxx_messageInfo_ListElementsReq.Size(m)
}
func (m *ListElementsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListElementsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListElementsReq proto.InternalMessageInfo

type ListElementsResp struct {
	Elements             []*ListElementsResp_Element `protobuf:"bytes,1,rep,name=elements,proto3" json:"elements,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *ListElementsResp) Reset()         { *m = ListElementsResp{} }
func (m *ListElementsResp) String() string { return proto.CompactTextString(m) }
func (*ListElementsResp) ProtoMessage()    {}
func (*ListElementsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{15}
}
func (m *ListElementsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListElementsResp.Unmarshal(m, b)
}
func (m *ListElementsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListElementsResp.Marshal(b, m, deterministic)
}
func (dst *ListElementsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListElementsResp.Merge(dst, src)
}
func (m *ListElementsResp) XXX_Size() int {
	return xxx_messageInfo_ListElementsResp.Size(m)
}
func (m *ListElementsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListElementsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListElementsResp proto.InternalMessageInfo

func (m *ListElementsResp) GetElements() []*ListElementsResp_Element {
	if m != nil {
		return m.Elements
	}
	return nil
}

type ListElementsResp_Element struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Note                 string   `protobuf:"bytes,3,opt,name=note,proto3" json:"note,omitempty"`
	CreateAt             int64    `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	UpdateAt             int64    `protobuf:"varint,5,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	Uid                  uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	UserName             string   `protobuf:"bytes,7,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListElementsResp_Element) Reset()         { *m = ListElementsResp_Element{} }
func (m *ListElementsResp_Element) String() string { return proto.CompactTextString(m) }
func (*ListElementsResp_Element) ProtoMessage()    {}
func (*ListElementsResp_Element) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{15, 0}
}
func (m *ListElementsResp_Element) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListElementsResp_Element.Unmarshal(m, b)
}
func (m *ListElementsResp_Element) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListElementsResp_Element.Marshal(b, m, deterministic)
}
func (dst *ListElementsResp_Element) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListElementsResp_Element.Merge(dst, src)
}
func (m *ListElementsResp_Element) XXX_Size() int {
	return xxx_messageInfo_ListElementsResp_Element.Size(m)
}
func (m *ListElementsResp_Element) XXX_DiscardUnknown() {
	xxx_messageInfo_ListElementsResp_Element.DiscardUnknown(m)
}

var xxx_messageInfo_ListElementsResp_Element proto.InternalMessageInfo

func (m *ListElementsResp_Element) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ListElementsResp_Element) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListElementsResp_Element) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *ListElementsResp_Element) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *ListElementsResp_Element) GetUpdateAt() int64 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

func (m *ListElementsResp_Element) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListElementsResp_Element) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

type ListTabsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListTabsReq) Reset()         { *m = ListTabsReq{} }
func (m *ListTabsReq) String() string { return proto.CompactTextString(m) }
func (*ListTabsReq) ProtoMessage()    {}
func (*ListTabsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{16}
}
func (m *ListTabsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListTabsReq.Unmarshal(m, b)
}
func (m *ListTabsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListTabsReq.Marshal(b, m, deterministic)
}
func (dst *ListTabsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListTabsReq.Merge(dst, src)
}
func (m *ListTabsReq) XXX_Size() int {
	return xxx_messageInfo_ListTabsReq.Size(m)
}
func (m *ListTabsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListTabsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListTabsReq proto.InternalMessageInfo

type ListTabsResp struct {
	Tabs                 []*ListTabsResp_Tab `protobuf:"bytes,1,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ListTabsResp) Reset()         { *m = ListTabsResp{} }
func (m *ListTabsResp) String() string { return proto.CompactTextString(m) }
func (*ListTabsResp) ProtoMessage()    {}
func (*ListTabsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{17}
}
func (m *ListTabsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListTabsResp.Unmarshal(m, b)
}
func (m *ListTabsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListTabsResp.Marshal(b, m, deterministic)
}
func (dst *ListTabsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListTabsResp.Merge(dst, src)
}
func (m *ListTabsResp) XXX_Size() int {
	return xxx_messageInfo_ListTabsResp.Size(m)
}
func (m *ListTabsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListTabsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListTabsResp proto.InternalMessageInfo

func (m *ListTabsResp) GetTabs() []*ListTabsResp_Tab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

type ListTabsResp_Tab struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListTabsResp_Tab) Reset()         { *m = ListTabsResp_Tab{} }
func (m *ListTabsResp_Tab) String() string { return proto.CompactTextString(m) }
func (*ListTabsResp_Tab) ProtoMessage()    {}
func (*ListTabsResp_Tab) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{17, 0}
}
func (m *ListTabsResp_Tab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListTabsResp_Tab.Unmarshal(m, b)
}
func (m *ListTabsResp_Tab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListTabsResp_Tab.Marshal(b, m, deterministic)
}
func (dst *ListTabsResp_Tab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListTabsResp_Tab.Merge(dst, src)
}
func (m *ListTabsResp_Tab) XXX_Size() int {
	return xxx_messageInfo_ListTabsResp_Tab.Size(m)
}
func (m *ListTabsResp_Tab) XXX_DiscardUnknown() {
	xxx_messageInfo_ListTabsResp_Tab.DiscardUnknown(m)
}

var xxx_messageInfo_ListTabsResp_Tab proto.InternalMessageInfo

func (m *ListTabsResp_Tab) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ListTabsResp_Tab) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type GetGameHomePageDIYFilterReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RegulatoryLevel      REGULATORY_LEVEL `protobuf:"varint,2,opt,name=regulatory_level,json=regulatoryLevel,proto3,enum=hobby_channel.REGULATORY_LEVEL" json:"regulatory_level,omitempty"`
	EntryType            EntryType        `protobuf:"varint,3,opt,name=entry_type,json=entryType,proto3,enum=hobby_channel.EntryType" json:"entry_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGameHomePageDIYFilterReq) Reset()         { *m = GetGameHomePageDIYFilterReq{} }
func (m *GetGameHomePageDIYFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetGameHomePageDIYFilterReq) ProtoMessage()    {}
func (*GetGameHomePageDIYFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{18}
}
func (m *GetGameHomePageDIYFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHomePageDIYFilterReq.Unmarshal(m, b)
}
func (m *GetGameHomePageDIYFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHomePageDIYFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetGameHomePageDIYFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHomePageDIYFilterReq.Merge(dst, src)
}
func (m *GetGameHomePageDIYFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetGameHomePageDIYFilterReq.Size(m)
}
func (m *GetGameHomePageDIYFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHomePageDIYFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHomePageDIYFilterReq proto.InternalMessageInfo

func (m *GetGameHomePageDIYFilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGameHomePageDIYFilterReq) GetRegulatoryLevel() REGULATORY_LEVEL {
	if m != nil {
		return m.RegulatoryLevel
	}
	return REGULATORY_LEVEL_FREE
}

func (m *GetGameHomePageDIYFilterReq) GetEntryType() EntryType {
	if m != nil {
		return m.EntryType
	}
	return EntryType_GameHomePageEntry
}

type GetGameHomePageDIYFilterResp struct {
	Items                []*GameHomePageFilterItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetGameHomePageDIYFilterResp) Reset()         { *m = GetGameHomePageDIYFilterResp{} }
func (m *GetGameHomePageDIYFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetGameHomePageDIYFilterResp) ProtoMessage()    {}
func (*GetGameHomePageDIYFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{19}
}
func (m *GetGameHomePageDIYFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameHomePageDIYFilterResp.Unmarshal(m, b)
}
func (m *GetGameHomePageDIYFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameHomePageDIYFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetGameHomePageDIYFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameHomePageDIYFilterResp.Merge(dst, src)
}
func (m *GetGameHomePageDIYFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetGameHomePageDIYFilterResp.Size(m)
}
func (m *GetGameHomePageDIYFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameHomePageDIYFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameHomePageDIYFilterResp proto.InternalMessageInfo

func (m *GetGameHomePageDIYFilterResp) GetItems() []*GameHomePageFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type GameHomePageFilterItem struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	CategoryId           uint32   `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameHomePageFilterItem) Reset()         { *m = GameHomePageFilterItem{} }
func (m *GameHomePageFilterItem) String() string { return proto.CompactTextString(m) }
func (*GameHomePageFilterItem) ProtoMessage()    {}
func (*GameHomePageFilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{20}
}
func (m *GameHomePageFilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameHomePageFilterItem.Unmarshal(m, b)
}
func (m *GameHomePageFilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameHomePageFilterItem.Marshal(b, m, deterministic)
}
func (dst *GameHomePageFilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameHomePageFilterItem.Merge(dst, src)
}
func (m *GameHomePageFilterItem) XXX_Size() int {
	return xxx_messageInfo_GameHomePageFilterItem.Size(m)
}
func (m *GameHomePageFilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameHomePageFilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameHomePageFilterItem proto.InternalMessageInfo

func (m *GameHomePageFilterItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GameHomePageFilterItem) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *GameHomePageFilterItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type SetGameHomePageDIYFilterReq struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Items                []*GameHomePageFilterItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	EntryType            EntryType                 `protobuf:"varint,3,opt,name=entry_type,json=entryType,proto3,enum=hobby_channel.EntryType" json:"entry_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SetGameHomePageDIYFilterReq) Reset()         { *m = SetGameHomePageDIYFilterReq{} }
func (m *SetGameHomePageDIYFilterReq) String() string { return proto.CompactTextString(m) }
func (*SetGameHomePageDIYFilterReq) ProtoMessage()    {}
func (*SetGameHomePageDIYFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{21}
}
func (m *SetGameHomePageDIYFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameHomePageDIYFilterReq.Unmarshal(m, b)
}
func (m *SetGameHomePageDIYFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameHomePageDIYFilterReq.Marshal(b, m, deterministic)
}
func (dst *SetGameHomePageDIYFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameHomePageDIYFilterReq.Merge(dst, src)
}
func (m *SetGameHomePageDIYFilterReq) XXX_Size() int {
	return xxx_messageInfo_SetGameHomePageDIYFilterReq.Size(m)
}
func (m *SetGameHomePageDIYFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameHomePageDIYFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameHomePageDIYFilterReq proto.InternalMessageInfo

func (m *SetGameHomePageDIYFilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetGameHomePageDIYFilterReq) GetItems() []*GameHomePageFilterItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *SetGameHomePageDIYFilterReq) GetEntryType() EntryType {
	if m != nil {
		return m.EntryType
	}
	return EntryType_GameHomePageEntry
}

type SetGameHomePageDIYFilterResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameHomePageDIYFilterResp) Reset()         { *m = SetGameHomePageDIYFilterResp{} }
func (m *SetGameHomePageDIYFilterResp) String() string { return proto.CompactTextString(m) }
func (*SetGameHomePageDIYFilterResp) ProtoMessage()    {}
func (*SetGameHomePageDIYFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{22}
}
func (m *SetGameHomePageDIYFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameHomePageDIYFilterResp.Unmarshal(m, b)
}
func (m *SetGameHomePageDIYFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameHomePageDIYFilterResp.Marshal(b, m, deterministic)
}
func (dst *SetGameHomePageDIYFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameHomePageDIYFilterResp.Merge(dst, src)
}
func (m *SetGameHomePageDIYFilterResp) XXX_Size() int {
	return xxx_messageInfo_SetGameHomePageDIYFilterResp.Size(m)
}
func (m *SetGameHomePageDIYFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameHomePageDIYFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameHomePageDIYFilterResp proto.InternalMessageInfo

// 查询优质房
type SearchHighQualityChannelsReq struct {
	Ttid                 uint32                           `protobuf:"varint,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Status               HighQualityChannelInfoStatusType `protobuf:"varint,2,opt,name=status,proto3,enum=hobby_channel.HighQualityChannelInfoStatusType" json:"status,omitempty"`
	Page                 uint32                           `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Limit                uint32                           `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *SearchHighQualityChannelsReq) Reset()         { *m = SearchHighQualityChannelsReq{} }
func (m *SearchHighQualityChannelsReq) String() string { return proto.CompactTextString(m) }
func (*SearchHighQualityChannelsReq) ProtoMessage()    {}
func (*SearchHighQualityChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{23}
}
func (m *SearchHighQualityChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchHighQualityChannelsReq.Unmarshal(m, b)
}
func (m *SearchHighQualityChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchHighQualityChannelsReq.Marshal(b, m, deterministic)
}
func (dst *SearchHighQualityChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchHighQualityChannelsReq.Merge(dst, src)
}
func (m *SearchHighQualityChannelsReq) XXX_Size() int {
	return xxx_messageInfo_SearchHighQualityChannelsReq.Size(m)
}
func (m *SearchHighQualityChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchHighQualityChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchHighQualityChannelsReq proto.InternalMessageInfo

func (m *SearchHighQualityChannelsReq) GetTtid() uint32 {
	if m != nil {
		return m.Ttid
	}
	return 0
}

func (m *SearchHighQualityChannelsReq) GetStatus() HighQualityChannelInfoStatusType {
	if m != nil {
		return m.Status
	}
	return HighQualityChannelInfoStatusType_UNKNOWN
}

func (m *SearchHighQualityChannelsReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *SearchHighQualityChannelsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type SearchHighQualityChannelsResp struct {
	InfoList             []*HighQualityChannelInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Count                uint32                    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SearchHighQualityChannelsResp) Reset()         { *m = SearchHighQualityChannelsResp{} }
func (m *SearchHighQualityChannelsResp) String() string { return proto.CompactTextString(m) }
func (*SearchHighQualityChannelsResp) ProtoMessage()    {}
func (*SearchHighQualityChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{24}
}
func (m *SearchHighQualityChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchHighQualityChannelsResp.Unmarshal(m, b)
}
func (m *SearchHighQualityChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchHighQualityChannelsResp.Marshal(b, m, deterministic)
}
func (dst *SearchHighQualityChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchHighQualityChannelsResp.Merge(dst, src)
}
func (m *SearchHighQualityChannelsResp) XXX_Size() int {
	return xxx_messageInfo_SearchHighQualityChannelsResp.Size(m)
}
func (m *SearchHighQualityChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchHighQualityChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchHighQualityChannelsResp proto.InternalMessageInfo

func (m *SearchHighQualityChannelsResp) GetInfoList() []*HighQualityChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *SearchHighQualityChannelsResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 更新优质房
type UpdateHighQualityChannelsReq struct {
	Info                 []*HighQualityChannelInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpdateHighQualityChannelsReq) Reset()         { *m = UpdateHighQualityChannelsReq{} }
func (m *UpdateHighQualityChannelsReq) String() string { return proto.CompactTextString(m) }
func (*UpdateHighQualityChannelsReq) ProtoMessage()    {}
func (*UpdateHighQualityChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{25}
}
func (m *UpdateHighQualityChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateHighQualityChannelsReq.Unmarshal(m, b)
}
func (m *UpdateHighQualityChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateHighQualityChannelsReq.Marshal(b, m, deterministic)
}
func (dst *UpdateHighQualityChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateHighQualityChannelsReq.Merge(dst, src)
}
func (m *UpdateHighQualityChannelsReq) XXX_Size() int {
	return xxx_messageInfo_UpdateHighQualityChannelsReq.Size(m)
}
func (m *UpdateHighQualityChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateHighQualityChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateHighQualityChannelsReq proto.InternalMessageInfo

func (m *UpdateHighQualityChannelsReq) GetInfo() []*HighQualityChannelInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateHighQualityChannelsResp struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateHighQualityChannelsResp) Reset()         { *m = UpdateHighQualityChannelsResp{} }
func (m *UpdateHighQualityChannelsResp) String() string { return proto.CompactTextString(m) }
func (*UpdateHighQualityChannelsResp) ProtoMessage()    {}
func (*UpdateHighQualityChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{26}
}
func (m *UpdateHighQualityChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateHighQualityChannelsResp.Unmarshal(m, b)
}
func (m *UpdateHighQualityChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateHighQualityChannelsResp.Marshal(b, m, deterministic)
}
func (dst *UpdateHighQualityChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateHighQualityChannelsResp.Merge(dst, src)
}
func (m *UpdateHighQualityChannelsResp) XXX_Size() int {
	return xxx_messageInfo_UpdateHighQualityChannelsResp.Size(m)
}
func (m *UpdateHighQualityChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateHighQualityChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateHighQualityChannelsResp proto.InternalMessageInfo

func (m *UpdateHighQualityChannelsResp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type HighQualityChannelInfo struct {
	Uid                  uint32                           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 uint32                           `protobuf:"varint,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Name                 string                           `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	DisplayId            uint32                           `protobuf:"varint,4,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelId            uint32                           `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeginTime            int64                            `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64                            `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	DurationTimeHour     uint32                           `protobuf:"varint,8,opt,name=duration_time_hour,json=durationTimeHour,proto3" json:"duration_time_hour,omitempty"`
	Status               HighQualityChannelInfoStatusType `protobuf:"varint,9,opt,name=status,proto3,enum=hobby_channel.HighQualityChannelInfoStatusType" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *HighQualityChannelInfo) Reset()         { *m = HighQualityChannelInfo{} }
func (m *HighQualityChannelInfo) String() string { return proto.CompactTextString(m) }
func (*HighQualityChannelInfo) ProtoMessage()    {}
func (*HighQualityChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{27}
}
func (m *HighQualityChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HighQualityChannelInfo.Unmarshal(m, b)
}
func (m *HighQualityChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HighQualityChannelInfo.Marshal(b, m, deterministic)
}
func (dst *HighQualityChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HighQualityChannelInfo.Merge(dst, src)
}
func (m *HighQualityChannelInfo) XXX_Size() int {
	return xxx_messageInfo_HighQualityChannelInfo.Size(m)
}
func (m *HighQualityChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HighQualityChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HighQualityChannelInfo proto.InternalMessageInfo

func (m *HighQualityChannelInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HighQualityChannelInfo) GetTtid() uint32 {
	if m != nil {
		return m.Ttid
	}
	return 0
}

func (m *HighQualityChannelInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *HighQualityChannelInfo) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *HighQualityChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HighQualityChannelInfo) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *HighQualityChannelInfo) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *HighQualityChannelInfo) GetDurationTimeHour() uint32 {
	if m != nil {
		return m.DurationTimeHour
	}
	return 0
}

func (m *HighQualityChannelInfo) GetStatus() HighQualityChannelInfoStatusType {
	if m != nil {
		return m.Status
	}
	return HighQualityChannelInfoStatusType_UNKNOWN
}

// batch check high quality channel
type CheckHighQualityChannelsReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckHighQualityChannelsReq) Reset()         { *m = CheckHighQualityChannelsReq{} }
func (m *CheckHighQualityChannelsReq) String() string { return proto.CompactTextString(m) }
func (*CheckHighQualityChannelsReq) ProtoMessage()    {}
func (*CheckHighQualityChannelsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{28}
}
func (m *CheckHighQualityChannelsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckHighQualityChannelsReq.Unmarshal(m, b)
}
func (m *CheckHighQualityChannelsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckHighQualityChannelsReq.Marshal(b, m, deterministic)
}
func (dst *CheckHighQualityChannelsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckHighQualityChannelsReq.Merge(dst, src)
}
func (m *CheckHighQualityChannelsReq) XXX_Size() int {
	return xxx_messageInfo_CheckHighQualityChannelsReq.Size(m)
}
func (m *CheckHighQualityChannelsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckHighQualityChannelsReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckHighQualityChannelsReq proto.InternalMessageInfo

func (m *CheckHighQualityChannelsReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type CheckHighQualityChannelsResp struct {
	Result               map[uint32]bool `protobuf:"bytes,1,rep,name=result,proto3" json:"result,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CheckHighQualityChannelsResp) Reset()         { *m = CheckHighQualityChannelsResp{} }
func (m *CheckHighQualityChannelsResp) String() string { return proto.CompactTextString(m) }
func (*CheckHighQualityChannelsResp) ProtoMessage()    {}
func (*CheckHighQualityChannelsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_hobby_channel_42db6ae53c8d161a, []int{29}
}
func (m *CheckHighQualityChannelsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckHighQualityChannelsResp.Unmarshal(m, b)
}
func (m *CheckHighQualityChannelsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckHighQualityChannelsResp.Marshal(b, m, deterministic)
}
func (dst *CheckHighQualityChannelsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckHighQualityChannelsResp.Merge(dst, src)
}
func (m *CheckHighQualityChannelsResp) XXX_Size() int {
	return xxx_messageInfo_CheckHighQualityChannelsResp.Size(m)
}
func (m *CheckHighQualityChannelsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckHighQualityChannelsResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckHighQualityChannelsResp proto.InternalMessageInfo

func (m *CheckHighQualityChannelsResp) GetResult() map[uint32]bool {
	if m != nil {
		return m.Result
	}
	return nil
}

func init() {
	proto.RegisterType((*BatchChannelOwnerDurationReq)(nil), "hobby_channel.BatchChannelOwnerDurationReq")
	proto.RegisterType((*BatchChannelOwnerDurationResp)(nil), "hobby_channel.BatchChannelOwnerDurationResp")
	proto.RegisterMapType((map[uint32]int64)(nil), "hobby_channel.BatchChannelOwnerDurationResp.ChannelOwnerDurationMapEntry")
	proto.RegisterType((*ListMatchElementConfigReq)(nil), "hobby_channel.ListMatchElementConfigReq")
	proto.RegisterType((*GetElementConfigResp)(nil), "hobby_channel.GetElementConfigResp")
	proto.RegisterType((*GetElementConfigResp_ElementConfig)(nil), "hobby_channel.GetElementConfigResp.ElementConfig")
	proto.RegisterType((*UpsertElementConfigReq)(nil), "hobby_channel.UpsertElementConfigReq")
	proto.RegisterType((*UpsertElementConfigResp)(nil), "hobby_channel.UpsertElementConfigResp")
	proto.RegisterType((*ListElementConfigsReq)(nil), "hobby_channel.ListElementConfigsReq")
	proto.RegisterType((*ListElementConfigsResp)(nil), "hobby_channel.ListElementConfigsResp")
	proto.RegisterType((*ListElementConfigsResp_ElementConfig)(nil), "hobby_channel.ListElementConfigsResp.ElementConfig")
	proto.RegisterType((*DelElementConfigReq)(nil), "hobby_channel.DelElementConfigReq")
	proto.RegisterType((*DelElementConfigResp)(nil), "hobby_channel.DelElementConfigResp")
	proto.RegisterType((*UpsertElementReq)(nil), "hobby_channel.UpsertElementReq")
	proto.RegisterType((*UpsertElementResp)(nil), "hobby_channel.UpsertElementResp")
	proto.RegisterType((*DelElementReq)(nil), "hobby_channel.DelElementReq")
	proto.RegisterType((*DelElementResp)(nil), "hobby_channel.DelElementResp")
	proto.RegisterType((*ListElementsReq)(nil), "hobby_channel.ListElementsReq")
	proto.RegisterType((*ListElementsResp)(nil), "hobby_channel.ListElementsResp")
	proto.RegisterType((*ListElementsResp_Element)(nil), "hobby_channel.ListElementsResp.Element")
	proto.RegisterType((*ListTabsReq)(nil), "hobby_channel.ListTabsReq")
	proto.RegisterType((*ListTabsResp)(nil), "hobby_channel.ListTabsResp")
	proto.RegisterType((*ListTabsResp_Tab)(nil), "hobby_channel.ListTabsResp.Tab")
	proto.RegisterType((*GetGameHomePageDIYFilterReq)(nil), "hobby_channel.GetGameHomePageDIYFilterReq")
	proto.RegisterType((*GetGameHomePageDIYFilterResp)(nil), "hobby_channel.GetGameHomePageDIYFilterResp")
	proto.RegisterType((*GameHomePageFilterItem)(nil), "hobby_channel.GameHomePageFilterItem")
	proto.RegisterType((*SetGameHomePageDIYFilterReq)(nil), "hobby_channel.SetGameHomePageDIYFilterReq")
	proto.RegisterType((*SetGameHomePageDIYFilterResp)(nil), "hobby_channel.SetGameHomePageDIYFilterResp")
	proto.RegisterType((*SearchHighQualityChannelsReq)(nil), "hobby_channel.SearchHighQualityChannelsReq")
	proto.RegisterType((*SearchHighQualityChannelsResp)(nil), "hobby_channel.SearchHighQualityChannelsResp")
	proto.RegisterType((*UpdateHighQualityChannelsReq)(nil), "hobby_channel.UpdateHighQualityChannelsReq")
	proto.RegisterType((*UpdateHighQualityChannelsResp)(nil), "hobby_channel.UpdateHighQualityChannelsResp")
	proto.RegisterType((*HighQualityChannelInfo)(nil), "hobby_channel.HighQualityChannelInfo")
	proto.RegisterType((*CheckHighQualityChannelsReq)(nil), "hobby_channel.CheckHighQualityChannelsReq")
	proto.RegisterType((*CheckHighQualityChannelsResp)(nil), "hobby_channel.CheckHighQualityChannelsResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "hobby_channel.CheckHighQualityChannelsResp.ResultEntry")
	proto.RegisterEnum("hobby_channel.REGULATORY_LEVEL", REGULATORY_LEVEL_name, REGULATORY_LEVEL_value)
	proto.RegisterEnum("hobby_channel.EntryType", EntryType_name, EntryType_value)
	proto.RegisterEnum("hobby_channel.HighQualityChannelInfoStatusType", HighQualityChannelInfoStatusType_name, HighQualityChannelInfoStatusType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// HobbyChannelClient is the client API for HobbyChannel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type HobbyChannelClient interface {
	// 开黑列表自定义筛选器
	GetGameHomePageDIYFilter(ctx context.Context, in *GetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*GetGameHomePageDIYFilterResp, error)
	SetGameHomePageDIYFilter(ctx context.Context, in *SetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*SetGameHomePageDIYFilterResp, error)
	ListElements(ctx context.Context, in *ListElementsReq, opts ...grpc.CallOption) (*ListElementsResp, error)
	UpsertElement(ctx context.Context, in *UpsertElementReq, opts ...grpc.CallOption) (*UpsertElementResp, error)
	DelElement(ctx context.Context, in *DelElementReq, opts ...grpc.CallOption) (*DelElementResp, error)
	ListTabs(ctx context.Context, in *ListTabsReq, opts ...grpc.CallOption) (*ListTabsResp, error)
	UpsertElementConfig(ctx context.Context, in *UpsertElementConfigReq, opts ...grpc.CallOption) (*UpsertElementConfigResp, error)
	ListElementConfigs(ctx context.Context, in *ListElementConfigsReq, opts ...grpc.CallOption) (*ListElementConfigsResp, error)
	DelElementConfig(ctx context.Context, in *DelElementConfigReq, opts ...grpc.CallOption) (*DelElementConfigResp, error)
	ListMatchElementConfig(ctx context.Context, in *ListMatchElementConfigReq, opts ...grpc.CallOption) (*ListElementConfigsResp, error)
	BatchChannelOwnerDuration(ctx context.Context, in *BatchChannelOwnerDurationReq, opts ...grpc.CallOption) (*BatchChannelOwnerDurationResp, error)
	SearchHighQualityChannels(ctx context.Context, in *SearchHighQualityChannelsReq, opts ...grpc.CallOption) (*SearchHighQualityChannelsResp, error)
	UpdateHighQualityChannels(ctx context.Context, in *UpdateHighQualityChannelsReq, opts ...grpc.CallOption) (*UpdateHighQualityChannelsResp, error)
	CheckHighQualityChannels(ctx context.Context, in *CheckHighQualityChannelsReq, opts ...grpc.CallOption) (*CheckHighQualityChannelsResp, error)
}

type hobbyChannelClient struct {
	cc *grpc.ClientConn
}

func NewHobbyChannelClient(cc *grpc.ClientConn) HobbyChannelClient {
	return &hobbyChannelClient{cc}
}

func (c *hobbyChannelClient) GetGameHomePageDIYFilter(ctx context.Context, in *GetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*GetGameHomePageDIYFilterResp, error) {
	out := new(GetGameHomePageDIYFilterResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/GetGameHomePageDIYFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) SetGameHomePageDIYFilter(ctx context.Context, in *SetGameHomePageDIYFilterReq, opts ...grpc.CallOption) (*SetGameHomePageDIYFilterResp, error) {
	out := new(SetGameHomePageDIYFilterResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/SetGameHomePageDIYFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) ListElements(ctx context.Context, in *ListElementsReq, opts ...grpc.CallOption) (*ListElementsResp, error) {
	out := new(ListElementsResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/ListElements", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) UpsertElement(ctx context.Context, in *UpsertElementReq, opts ...grpc.CallOption) (*UpsertElementResp, error) {
	out := new(UpsertElementResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/UpsertElement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) DelElement(ctx context.Context, in *DelElementReq, opts ...grpc.CallOption) (*DelElementResp, error) {
	out := new(DelElementResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/DelElement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) ListTabs(ctx context.Context, in *ListTabsReq, opts ...grpc.CallOption) (*ListTabsResp, error) {
	out := new(ListTabsResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/ListTabs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) UpsertElementConfig(ctx context.Context, in *UpsertElementConfigReq, opts ...grpc.CallOption) (*UpsertElementConfigResp, error) {
	out := new(UpsertElementConfigResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/UpsertElementConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) ListElementConfigs(ctx context.Context, in *ListElementConfigsReq, opts ...grpc.CallOption) (*ListElementConfigsResp, error) {
	out := new(ListElementConfigsResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/ListElementConfigs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) DelElementConfig(ctx context.Context, in *DelElementConfigReq, opts ...grpc.CallOption) (*DelElementConfigResp, error) {
	out := new(DelElementConfigResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/DelElementConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) ListMatchElementConfig(ctx context.Context, in *ListMatchElementConfigReq, opts ...grpc.CallOption) (*ListElementConfigsResp, error) {
	out := new(ListElementConfigsResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/ListMatchElementConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) BatchChannelOwnerDuration(ctx context.Context, in *BatchChannelOwnerDurationReq, opts ...grpc.CallOption) (*BatchChannelOwnerDurationResp, error) {
	out := new(BatchChannelOwnerDurationResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/BatchChannelOwnerDuration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) SearchHighQualityChannels(ctx context.Context, in *SearchHighQualityChannelsReq, opts ...grpc.CallOption) (*SearchHighQualityChannelsResp, error) {
	out := new(SearchHighQualityChannelsResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/SearchHighQualityChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) UpdateHighQualityChannels(ctx context.Context, in *UpdateHighQualityChannelsReq, opts ...grpc.CallOption) (*UpdateHighQualityChannelsResp, error) {
	out := new(UpdateHighQualityChannelsResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/UpdateHighQualityChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hobbyChannelClient) CheckHighQualityChannels(ctx context.Context, in *CheckHighQualityChannelsReq, opts ...grpc.CallOption) (*CheckHighQualityChannelsResp, error) {
	out := new(CheckHighQualityChannelsResp)
	err := c.cc.Invoke(ctx, "/hobby_channel.HobbyChannel/CheckHighQualityChannels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HobbyChannelServer is the server API for HobbyChannel service.
type HobbyChannelServer interface {
	// 开黑列表自定义筛选器
	GetGameHomePageDIYFilter(context.Context, *GetGameHomePageDIYFilterReq) (*GetGameHomePageDIYFilterResp, error)
	SetGameHomePageDIYFilter(context.Context, *SetGameHomePageDIYFilterReq) (*SetGameHomePageDIYFilterResp, error)
	ListElements(context.Context, *ListElementsReq) (*ListElementsResp, error)
	UpsertElement(context.Context, *UpsertElementReq) (*UpsertElementResp, error)
	DelElement(context.Context, *DelElementReq) (*DelElementResp, error)
	ListTabs(context.Context, *ListTabsReq) (*ListTabsResp, error)
	UpsertElementConfig(context.Context, *UpsertElementConfigReq) (*UpsertElementConfigResp, error)
	ListElementConfigs(context.Context, *ListElementConfigsReq) (*ListElementConfigsResp, error)
	DelElementConfig(context.Context, *DelElementConfigReq) (*DelElementConfigResp, error)
	ListMatchElementConfig(context.Context, *ListMatchElementConfigReq) (*ListElementConfigsResp, error)
	BatchChannelOwnerDuration(context.Context, *BatchChannelOwnerDurationReq) (*BatchChannelOwnerDurationResp, error)
	SearchHighQualityChannels(context.Context, *SearchHighQualityChannelsReq) (*SearchHighQualityChannelsResp, error)
	UpdateHighQualityChannels(context.Context, *UpdateHighQualityChannelsReq) (*UpdateHighQualityChannelsResp, error)
	CheckHighQualityChannels(context.Context, *CheckHighQualityChannelsReq) (*CheckHighQualityChannelsResp, error)
}

func RegisterHobbyChannelServer(s *grpc.Server, srv HobbyChannelServer) {
	s.RegisterService(&_HobbyChannel_serviceDesc, srv)
}

func _HobbyChannel_GetGameHomePageDIYFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameHomePageDIYFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).GetGameHomePageDIYFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/GetGameHomePageDIYFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).GetGameHomePageDIYFilter(ctx, req.(*GetGameHomePageDIYFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_SetGameHomePageDIYFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGameHomePageDIYFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).SetGameHomePageDIYFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/SetGameHomePageDIYFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).SetGameHomePageDIYFilter(ctx, req.(*SetGameHomePageDIYFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_ListElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListElementsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).ListElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/ListElements",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).ListElements(ctx, req.(*ListElementsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_UpsertElement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertElementReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).UpsertElement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/UpsertElement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).UpsertElement(ctx, req.(*UpsertElementReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_DelElement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelElementReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).DelElement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/DelElement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).DelElement(ctx, req.(*DelElementReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_ListTabs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTabsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).ListTabs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/ListTabs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).ListTabs(ctx, req.(*ListTabsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_UpsertElementConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertElementConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).UpsertElementConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/UpsertElementConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).UpsertElementConfig(ctx, req.(*UpsertElementConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_ListElementConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListElementConfigsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).ListElementConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/ListElementConfigs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).ListElementConfigs(ctx, req.(*ListElementConfigsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_DelElementConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelElementConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).DelElementConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/DelElementConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).DelElementConfig(ctx, req.(*DelElementConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_ListMatchElementConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMatchElementConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).ListMatchElementConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/ListMatchElementConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).ListMatchElementConfig(ctx, req.(*ListMatchElementConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_BatchChannelOwnerDuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchChannelOwnerDurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).BatchChannelOwnerDuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/BatchChannelOwnerDuration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).BatchChannelOwnerDuration(ctx, req.(*BatchChannelOwnerDurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_SearchHighQualityChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchHighQualityChannelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).SearchHighQualityChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/SearchHighQualityChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).SearchHighQualityChannels(ctx, req.(*SearchHighQualityChannelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_UpdateHighQualityChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHighQualityChannelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).UpdateHighQualityChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/UpdateHighQualityChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).UpdateHighQualityChannels(ctx, req.(*UpdateHighQualityChannelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HobbyChannel_CheckHighQualityChannels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckHighQualityChannelsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HobbyChannelServer).CheckHighQualityChannels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hobby_channel.HobbyChannel/CheckHighQualityChannels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HobbyChannelServer).CheckHighQualityChannels(ctx, req.(*CheckHighQualityChannelsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _HobbyChannel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "hobby_channel.HobbyChannel",
	HandlerType: (*HobbyChannelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGameHomePageDIYFilter",
			Handler:    _HobbyChannel_GetGameHomePageDIYFilter_Handler,
		},
		{
			MethodName: "SetGameHomePageDIYFilter",
			Handler:    _HobbyChannel_SetGameHomePageDIYFilter_Handler,
		},
		{
			MethodName: "ListElements",
			Handler:    _HobbyChannel_ListElements_Handler,
		},
		{
			MethodName: "UpsertElement",
			Handler:    _HobbyChannel_UpsertElement_Handler,
		},
		{
			MethodName: "DelElement",
			Handler:    _HobbyChannel_DelElement_Handler,
		},
		{
			MethodName: "ListTabs",
			Handler:    _HobbyChannel_ListTabs_Handler,
		},
		{
			MethodName: "UpsertElementConfig",
			Handler:    _HobbyChannel_UpsertElementConfig_Handler,
		},
		{
			MethodName: "ListElementConfigs",
			Handler:    _HobbyChannel_ListElementConfigs_Handler,
		},
		{
			MethodName: "DelElementConfig",
			Handler:    _HobbyChannel_DelElementConfig_Handler,
		},
		{
			MethodName: "ListMatchElementConfig",
			Handler:    _HobbyChannel_ListMatchElementConfig_Handler,
		},
		{
			MethodName: "BatchChannelOwnerDuration",
			Handler:    _HobbyChannel_BatchChannelOwnerDuration_Handler,
		},
		{
			MethodName: "SearchHighQualityChannels",
			Handler:    _HobbyChannel_SearchHighQualityChannels_Handler,
		},
		{
			MethodName: "UpdateHighQualityChannels",
			Handler:    _HobbyChannel_UpdateHighQualityChannels_Handler,
		},
		{
			MethodName: "CheckHighQualityChannels",
			Handler:    _HobbyChannel_CheckHighQualityChannels_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "hobby-channel/hobby-channel.proto",
}

func init() {
	proto.RegisterFile("hobby-channel/hobby-channel.proto", fileDescriptor_hobby_channel_42db6ae53c8d161a)
}

var fileDescriptor_hobby_channel_42db6ae53c8d161a = []byte{
	// 1622 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0xcd, 0x72, 0xdb, 0x46,
	0x12, 0x16, 0x48, 0x8a, 0x3f, 0x4d, 0x51, 0x86, 0xc7, 0xb2, 0x4c, 0x51, 0x92, 0x25, 0xc3, 0xab,
	0x5d, 0x59, 0xf6, 0x52, 0x65, 0xab, 0xb6, 0xbc, 0xde, 0xdd, 0xda, 0x2d, 0x59, 0xa2, 0x25, 0x78,
	0xf5, 0xe3, 0x85, 0x28, 0x6f, 0x1c, 0x1f, 0x50, 0x20, 0x31, 0xa2, 0x50, 0x06, 0x01, 0x08, 0x33,
	0x50, 0xc2, 0x7b, 0x2a, 0xef, 0x91, 0x43, 0x2e, 0x3e, 0xe7, 0x96, 0x5c, 0xf2, 0x08, 0x79, 0x85,
	0x3c, 0x43, 0x1e, 0x20, 0x35, 0x03, 0x80, 0x24, 0x40, 0x00, 0x92, 0x9c, 0xca, 0x2d, 0xb7, 0x41,
	0xff, 0x4d, 0xf7, 0x37, 0x3d, 0x3d, 0xdd, 0x80, 0x07, 0xe7, 0x76, 0xa7, 0x33, 0xf8, 0x6b, 0xf7,
	0x5c, 0xb3, 0x2c, 0x6c, 0x6e, 0x46, 0xbe, 0x9a, 0x8e, 0x6b, 0x53, 0x1b, 0xd5, 0x38, 0x51, 0x0d,
	0x88, 0xd2, 0x7f, 0x60, 0xe9, 0xa5, 0x46, 0xbb, 0xe7, 0x3b, 0xfe, 0xf7, 0xf1, 0x17, 0x16, 0x76,
	0x77, 0x3d, 0x57, 0xa3, 0x86, 0x6d, 0x29, 0xf8, 0x02, 0xad, 0x40, 0x35, 0x10, 0x55, 0x0d, 0x9d,
	0xd4, 0x85, 0xd5, 0xfc, 0x7a, 0x4d, 0x81, 0x80, 0x24, 0xeb, 0x44, 0xfa, 0x45, 0x80, 0xe5, 0x0c,
	0x0b, 0xc4, 0x41, 0x5f, 0x0b, 0xd0, 0x08, 0x6d, 0xd8, 0x8c, 0xab, 0xea, 0x01, 0x5b, 0xed, 0x6b,
	0x0e, 0x37, 0x59, 0x7d, 0x26, 0x37, 0x23, 0x7e, 0x35, 0x33, 0x4d, 0x36, 0x93, 0x18, 0x87, 0x9a,
	0xd3, 0xb2, 0xa8, 0x3b, 0x50, 0xee, 0x75, 0x93, 0xb9, 0x8d, 0xd7, 0xb0, 0x94, 0xa5, 0x88, 0x44,
	0xc8, 0x7f, 0xc0, 0x83, 0xba, 0xb0, 0x2a, 0xac, 0xd7, 0x14, 0xb6, 0x44, 0x73, 0x30, 0x7d, 0xa9,
	0x99, 0x1e, 0xae, 0xe7, 0x56, 0x85, 0xf5, 0xbc, 0xe2, 0x7f, 0xfc, 0x23, 0xf7, 0x77, 0x41, 0xda,
	0x84, 0x85, 0x03, 0x83, 0xd0, 0x43, 0xe6, 0x66, 0xcb, 0xc4, 0x7d, 0x6c, 0xd1, 0x1d, 0xdb, 0x3a,
	0x33, 0x7a, 0x0c, 0x34, 0x04, 0x05, 0x4b, 0xeb, 0x63, 0x1e, 0x5a, 0x45, 0xe1, 0x6b, 0xe9, 0x87,
	0x3c, 0xcc, 0xed, 0x61, 0x1a, 0x93, 0x25, 0x0e, 0x7a, 0x03, 0x80, 0x4d, 0xac, 0x76, 0x39, 0x25,
	0x40, 0xe3, 0x69, 0x0c, 0x8d, 0x24, 0xc5, 0x66, 0x94, 0x52, 0xc1, 0x26, 0xf6, 0x97, 0x8d, 0x9f,
	0x72, 0x50, 0x8b, 0x30, 0xd1, 0x2c, 0xe4, 0x0c, 0x9d, 0x07, 0x56, 0x51, 0x72, 0x86, 0x8e, 0x96,
	0xf9, 0x9e, 0x4c, 0x40, 0x35, 0x74, 0x1e, 0x5c, 0x85, 0x1b, 0x60, 0x14, 0x59, 0x47, 0x0f, 0x60,
	0x26, 0x64, 0xf3, 0x38, 0xf2, 0x5c, 0xa0, 0x1a, 0xd0, 0x8e, 0xb4, 0x3e, 0x46, 0x8b, 0x50, 0xb9,
	0x34, 0x74, 0x6c, 0xab, 0x9e, 0x6b, 0xd6, 0x0b, 0x9c, 0x5f, 0xe6, 0x84, 0x53, 0xd7, 0x64, 0xe6,
	0x7d, 0x26, 0x1d, 0x38, 0xb8, 0x3e, 0xed, 0x9b, 0xe7, 0x94, 0xf6, 0xc0, 0xc1, 0xe8, 0x1e, 0x94,
	0x8c, 0x7e, 0x8f, 0x6b, 0x16, 0x39, 0xaf, 0x68, 0xf4, 0x7b, 0x4c, 0xef, 0x2e, 0x14, 0xa9, 0xd6,
	0x61, 0x2e, 0x95, 0xf8, 0x19, 0x4c, 0x53, 0xad, 0x23, 0xeb, 0x68, 0x01, 0xca, 0x8c, 0xcc, 0x5d,
	0x29, 0x73, 0x85, 0x12, 0xd5, 0x3a, 0xdc, 0x0d, 0x11, 0xf2, 0x9e, 0xa1, 0xd7, 0x2b, 0xfe, 0x91,
	0x79, 0x86, 0xce, 0x1c, 0xf3, 0x08, 0x76, 0x7d, 0x69, 0xf0, 0x1d, 0x63, 0x84, 0xd0, 0xeb, 0xae,
	0x8b, 0x35, 0x8a, 0x55, 0x8d, 0xd6, 0xab, 0xfc, 0x4c, 0xcb, 0x3e, 0x61, 0x9b, 0x72, 0x4d, 0x47,
	0x0f, 0x98, 0x33, 0x3e, 0xd3, 0x27, 0x6c, 0x53, 0xe9, 0x67, 0x01, 0xe6, 0x4f, 0x1d, 0x82, 0x5d,
	0x3a, 0x71, 0xda, 0x37, 0x04, 0x37, 0x82, 0x5c, 0x3e, 0x13, 0xb9, 0x42, 0x06, 0x72, 0xd3, 0x29,
	0xc8, 0x15, 0xc7, 0x91, 0x0b, 0xe0, 0x29, 0xa5, 0xc0, 0x53, 0x8e, 0xc2, 0x23, 0x3d, 0x82, 0x7b,
	0x89, 0x31, 0x12, 0x27, 0x1e, 0xa4, 0x74, 0x08, 0x77, 0x59, 0xfe, 0x47, 0x04, 0x09, 0x43, 0x23,
	0x1a, 0xbd, 0x10, 0x8f, 0x7e, 0xe4, 0x68, 0x6e, 0xcc, 0x51, 0xe9, 0xfb, 0x3c, 0xcc, 0x27, 0xd9,
	0x23, 0x0e, 0x3a, 0x84, 0x92, 0x7f, 0x37, 0x48, 0x70, 0x39, 0xb6, 0x62, 0x97, 0x23, 0x59, 0x2f,
	0x76, 0x3d, 0x42, 0x1b, 0x7f, 0x5c, 0x8e, 0xdf, 0xe1, 0x72, 0xac, 0xc1, 0x9d, 0x5d, 0x6c, 0x5e,
	0x75, 0x31, 0xa4, 0x79, 0x98, 0x9b, 0x14, 0x23, 0x8e, 0x34, 0x00, 0x31, 0x92, 0x76, 0x49, 0x97,
	0x2a, 0x2c, 0xa9, 0xfe, 0x71, 0xf0, 0x35, 0xa7, 0xd9, 0x34, 0x3c, 0x01, 0xbe, 0x0e, 0x63, 0x2e,
	0xa4, 0xc4, 0x3c, 0x1d, 0xcb, 0xf8, 0x87, 0x70, 0x3b, 0xb6, 0x75, 0x42, 0xae, 0xaf, 0x40, 0x6d,
	0xe4, 0x77, 0x52, 0x60, 0x22, 0xcc, 0x8e, 0x0b, 0x10, 0x47, 0xba, 0x0d, 0xb7, 0xc6, 0xd2, 0x92,
	0x5d, 0x0c, 0xe9, 0xab, 0x1c, 0x88, 0x51, 0x1a, 0x71, 0xd0, 0x0e, 0x94, 0x83, 0xc4, 0x09, 0xb3,
	0xfb, 0x2f, 0xe9, 0xd9, 0x1d, 0xc9, 0x6b, 0x65, 0xa8, 0xd8, 0xf8, 0x28, 0x40, 0x29, 0xa0, 0x7e,
	0x32, 0x6e, 0x91, 0xc3, 0x2f, 0x64, 0x1d, 0xfe, 0x74, 0xf4, 0xf0, 0x43, 0xc4, 0x8b, 0x29, 0x88,
	0x97, 0x62, 0x88, 0xd7, 0xa0, 0xca, 0x42, 0x6a, 0x6b, 0x1d, 0x8e, 0x8a, 0x05, 0x33, 0xa3, 0x4f,
	0xe2, 0xa0, 0x2d, 0x28, 0x50, 0xad, 0x13, 0x82, 0xb1, 0x92, 0x00, 0x46, 0x28, 0xda, 0x6c, 0x6b,
	0x1d, 0x85, 0x0b, 0x37, 0x1e, 0x41, 0xbe, 0xad, 0x75, 0xc6, 0x62, 0xaf, 0xa5, 0xc5, 0x2e, 0x7d,
	0x27, 0xc0, 0xe2, 0x1e, 0xa6, 0x7b, 0x5a, 0x1f, 0xef, 0xdb, 0x7d, 0xfc, 0x46, 0xeb, 0xe1, 0x5d,
	0xf9, 0xdd, 0x2b, 0xc3, 0xa4, 0xd8, 0x65, 0x47, 0x1b, 0x44, 0x23, 0x8c, 0xa2, 0x79, 0x0d, 0xa2,
	0x8b, 0x7b, 0x9e, 0xa9, 0x51, 0xdb, 0x1d, 0xa8, 0x26, 0xbe, 0xc4, 0x26, 0xb7, 0x38, 0x3b, 0xe1,
	0x9d, 0xd2, 0xda, 0x3b, 0x3d, 0xd8, 0x6e, 0x1f, 0x2b, 0xef, 0xd4, 0x83, 0xd6, 0xdb, 0xd6, 0x81,
	0x72, 0x6b, 0xa4, 0x78, 0xc0, 0xf4, 0xd0, 0x73, 0x00, 0xcc, 0x5a, 0x0d, 0xff, 0xee, 0xe7, 0xb9,
	0x95, 0x7a, 0xcc, 0x0a, 0xef, 0x45, 0x58, 0x29, 0x50, 0x2a, 0x38, 0x5c, 0x4a, 0xef, 0x61, 0x29,
	0xdd, 0x6b, 0xe2, 0xa0, 0x7f, 0xc2, 0xb4, 0x41, 0x71, 0x3f, 0xc4, 0x6d, 0x2d, 0xde, 0x3f, 0x8c,
	0x29, 0xfa, 0x5a, 0x32, 0xc5, 0x7d, 0xc5, 0xd7, 0x91, 0xce, 0x60, 0x3e, 0x59, 0x60, 0xac, 0xe6,
	0x08, 0xe3, 0x35, 0x87, 0x35, 0x85, 0x1a, 0xc5, 0x3d, 0x06, 0xc8, 0xb0, 0x92, 0x43, 0x48, 0x92,
	0x75, 0xd6, 0x37, 0x51, 0x83, 0x9a, 0x61, 0x8a, 0xf9, 0x1f, 0xd2, 0xb7, 0x02, 0x2c, 0x9e, 0xdc,
	0x08, 0xfb, 0x61, 0x58, 0xb9, 0x9b, 0x87, 0xf5, 0xe9, 0x60, 0xdf, 0x87, 0xa5, 0x93, 0x0c, 0xb0,
	0x59, 0x1c, 0x4b, 0x27, 0x58, 0x73, 0xbb, 0xe7, 0xfb, 0x46, 0xef, 0xfc, 0x7f, 0x9e, 0x66, 0x1a,
	0x74, 0x10, 0x74, 0x96, 0x24, 0xe8, 0xff, 0x28, 0x1d, 0x46, 0xc2, 0xd7, 0x68, 0x0f, 0x8a, 0x84,
	0x6a, 0xd4, 0x23, 0x41, 0xf2, 0x6c, 0xc6, 0x3c, 0x99, 0x34, 0x25, 0x5b, 0x67, 0xf6, 0x09, 0x57,
	0xe1, 0x0e, 0x06, 0xea, 0xcc, 0xb8, 0xa3, 0xf5, 0xfc, 0x80, 0x6a, 0x0a, 0x5f, 0x33, 0xbc, 0x4d,
	0xa3, 0x6f, 0xd0, 0xa0, 0xee, 0xf9, 0x1f, 0xd2, 0x00, 0x96, 0x33, 0xdc, 0x24, 0x0e, 0x7a, 0x09,
	0x15, 0xc3, 0x3a, 0xb3, 0x55, 0xd3, 0x20, 0x34, 0x25, 0x73, 0x92, 0xdd, 0x52, 0xca, 0x4c, 0x8f,
	0xdd, 0x46, 0xb6, 0x75, 0xd7, 0xf6, 0x2c, 0x1a, 0xbe, 0xe7, 0xfc, 0x43, 0x7a, 0x07, 0x4b, 0xa7,
	0xbc, 0x40, 0xa4, 0x20, 0xf4, 0x02, 0x0a, 0xcc, 0xc2, 0xcd, 0x36, 0xe5, 0x2a, 0xd2, 0x16, 0x2c,
	0x67, 0x98, 0x26, 0x0e, 0x03, 0xc8, 0x1b, 0xcd, 0x2a, 0x7c, 0x2d, 0xfd, 0x98, 0x83, 0xf9, 0x64,
	0xab, 0x09, 0x59, 0x17, 0x1e, 0x5f, 0x6e, 0xec, 0xf8, 0xc2, 0x5a, 0x92, 0x1f, 0xab, 0xa3, 0xcb,
	0x00, 0xba, 0x41, 0x1c, 0x53, 0xe3, 0xb7, 0xc0, 0x87, 0xbe, 0x12, 0x50, 0x64, 0xde, 0x47, 0x8c,
	0x46, 0x27, 0x5e, 0x36, 0x6b, 0x4a, 0x65, 0x38, 0x39, 0x31, 0x76, 0x07, 0xf7, 0x0c, 0x4b, 0xa5,
	0x46, 0x1f, 0xf3, 0xf2, 0x99, 0x57, 0x2a, 0x9c, 0xd2, 0x36, 0xfa, 0x98, 0xbd, 0xeb, 0xd8, 0xd2,
	0x7d, 0x66, 0x89, 0x33, 0x4b, 0xd8, 0xd2, 0x39, 0xeb, 0x09, 0xa0, 0xe1, 0x04, 0xc5, 0xf8, 0xea,
	0xb9, 0xed, 0xb9, 0xfc, 0xf1, 0xaf, 0x29, 0x62, 0xc8, 0x61, 0x92, 0xfb, 0xb6, 0xe7, 0x8e, 0x25,
	0x5e, 0xe5, 0x37, 0x25, 0x9e, 0xf4, 0x6f, 0x58, 0xdc, 0x39, 0xc7, 0xdd, 0x0f, 0x29, 0x47, 0x7a,
	0xe5, 0xa4, 0xf8, 0x51, 0x60, 0xf3, 0x57, 0x9a, 0x01, 0xe2, 0xa0, 0x63, 0x28, 0xba, 0x98, 0x78,
	0x66, 0x98, 0x8b, 0xcf, 0x63, 0x9e, 0x66, 0x29, 0x37, 0x15, 0xae, 0xe9, 0x4f, 0x80, 0x81, 0x99,
	0xc6, 0x0b, 0xa8, 0x8e, 0x91, 0xaf, 0x9a, 0xef, 0xca, 0x63, 0xf3, 0xdd, 0x46, 0x13, 0xc4, 0x78,
	0x39, 0x47, 0x65, 0x28, 0xbc, 0x52, 0x5a, 0x2d, 0x71, 0x0a, 0x89, 0x30, 0x73, 0x22, 0x1f, 0xbe,
	0x39, 0x68, 0xa9, 0x87, 0xf2, 0xd1, 0xb1, 0x22, 0x0a, 0x1b, 0xff, 0x82, 0xca, 0xb0, 0x96, 0xa0,
	0xbb, 0x70, 0x7b, 0xbc, 0x7a, 0x70, 0x86, 0x38, 0x85, 0xea, 0x30, 0x77, 0x38, 0x20, 0x14, 0xbb,
	0x83, 0x28, 0x47, 0xd8, 0xf8, 0x0c, 0x56, 0xaf, 0x3a, 0x06, 0x54, 0x85, 0xd2, 0xe9, 0xd1, 0x7f,
	0x8f, 0x8e, 0xff, 0x7f, 0x24, 0x4e, 0xa1, 0x59, 0x80, 0xa3, 0xe3, 0xb6, 0xba, 0xbd, 0xd3, 0x96,
	0xdf, 0xb6, 0x44, 0x01, 0x01, 0x14, 0x83, 0x75, 0x8e, 0x09, 0xca, 0x47, 0x6f, 0xb7, 0x0f, 0xe4,
	0x5d, 0x31, 0xff, 0xec, 0x9b, 0x2a, 0xcc, 0xec, 0x33, 0x14, 0x03, 0xa3, 0x88, 0x40, 0x3d, 0xed,
	0x25, 0x41, 0x1b, 0x93, 0x63, 0x67, 0x5a, 0xb1, 0x6e, 0x3c, 0xbe, 0xb6, 0x2c, 0x71, 0xd8, 0xa6,
	0x27, 0xd7, 0xdd, 0xf4, 0xe4, 0x06, 0x9b, 0x66, 0x95, 0x69, 0x74, 0xec, 0xb7, 0x16, 0x61, 0xf3,
	0x84, 0xee, 0x67, 0x76, 0x56, 0x17, 0x8d, 0x95, 0x2b, 0x3a, 0x2f, 0xa4, 0x40, 0x2d, 0xd2, 0x2c,
	0xa2, 0xb8, 0x46, 0xbc, 0x8b, 0x6d, 0xac, 0x66, 0x0b, 0x10, 0x07, 0xc9, 0x00, 0xa3, 0xd6, 0x11,
	0x2d, 0xc5, 0xe4, 0x23, 0x6d, 0x67, 0x63, 0x39, 0x83, 0xeb, 0xf7, 0x92, 0x61, 0x7f, 0x84, 0x1a,
	0xa9, 0x8d, 0xd3, 0x45, 0x63, 0x31, 0xa3, 0xa9, 0x42, 0x3a, 0xdc, 0x49, 0x18, 0x01, 0xd1, 0x5a,
	0x56, 0x20, 0xc3, 0x8e, 0xbf, 0xf1, 0xe7, 0xeb, 0x88, 0x11, 0x07, 0x69, 0x80, 0x26, 0xa7, 0x36,
	0xf4, 0xa7, 0x6b, 0x0c, 0x76, 0x17, 0x8d, 0xb5, 0x6b, 0x8d, 0x7f, 0xe8, 0x3d, 0x88, 0xf1, 0x61,
	0x03, 0x49, 0xa9, 0x00, 0x8e, 0x42, 0x78, 0x78, 0xa5, 0x0c, 0x71, 0x90, 0xe1, 0x4f, 0xab, 0x93,
	0x7f, 0x7f, 0xd0, 0x7a, 0x82, 0x77, 0x89, 0x3f, 0x89, 0xae, 0x1b, 0xc7, 0x25, 0x2c, 0xa4, 0xfe,
	0x0b, 0x43, 0x8f, 0xaf, 0xff, 0xd7, 0xec, 0xa2, 0xf1, 0xe4, 0x26, 0xbf, 0xd8, 0xd0, 0x97, 0xb0,
	0x90, 0xda, 0x3c, 0xa0, 0xc9, 0x7b, 0x98, 0xde, 0x0d, 0x4d, 0xec, 0x9b, 0xd9, 0x93, 0x48, 0x53,
	0x6c, 0xe7, 0xd4, 0x07, 0x7e, 0x62, 0xe7, 0xac, 0x2e, 0x63, 0x62, 0xe7, 0xcc, 0xbe, 0x41, 0x9a,
	0x42, 0x1e, 0xd4, 0xd3, 0xde, 0x98, 0x89, 0x32, 0x95, 0xf1, 0x14, 0x4e, 0x94, 0xa9, 0xac, 0x87,
	0x4b, 0x9a, 0x7a, 0xf9, 0xf4, 0xf3, 0xcd, 0x9e, 0x6d, 0x6a, 0x56, 0xaf, 0xf9, 0xb7, 0x67, 0x94,
	0x36, 0xbb, 0x76, 0x7f, 0x93, 0xff, 0xab, 0xed, 0xda, 0xe6, 0x26, 0xc1, 0xee, 0xa5, 0xd1, 0xc5,
	0x24, 0xfa, 0x2f, 0xb7, 0x53, 0xe4, 0x02, 0x5b, 0xbf, 0x06, 0x00, 0x00, 0xff, 0xff, 0x89, 0x55,
	0x30, 0x83, 0xf1, 0x15, 0x00, 0x00,
}
