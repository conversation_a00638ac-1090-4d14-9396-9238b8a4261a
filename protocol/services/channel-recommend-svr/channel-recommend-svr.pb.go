// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-recommend-svr/channel-recommend-svr.proto

package channel_recommend_svr // import "golang.52tt.com/protocol/services/channel-recommend-svr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 推荐等级
type RecommendLevel int32

const (
	RecommendLevel_Recommend_Invalid RecommendLevel = 0
	RecommendLevel_Recommend_Level_S RecommendLevel = 1
	RecommendLevel_Recommend_Level_A RecommendLevel = 2
	RecommendLevel_Recommend_Level_B RecommendLevel = 3
)

var RecommendLevel_name = map[int32]string{
	0: "Recommend_Invalid",
	1: "Recommend_Level_S",
	2: "Recommend_Level_A",
	3: "Recommend_Level_B",
}
var RecommendLevel_value = map[string]int32{
	"Recommend_Invalid": 0,
	"Recommend_Level_S": 1,
	"Recommend_Level_A": 2,
	"Recommend_Level_B": 3,
}

func (x RecommendLevel) String() string {
	return proto.EnumName(RecommendLevel_name, int32(x))
}
func (RecommendLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{0}
}

// 发放类型
type FlowCardGrantType int32

const (
	FlowCardGrantType_GrantGuild  FlowCardGrantType = 0
	FlowCardGrantType_GrantAnchor FlowCardGrantType = 1
)

var FlowCardGrantType_name = map[int32]string{
	0: "GrantGuild",
	1: "GrantAnchor",
}
var FlowCardGrantType_value = map[string]int32{
	"GrantGuild":  0,
	"GrantAnchor": 1,
}

func (x FlowCardGrantType) String() string {
	return proto.EnumName(FlowCardGrantType_name, int32(x))
}
func (FlowCardGrantType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{1}
}

type UserCategory int32

const (
	UserCategory_NewUser_Type        UserCategory = 0
	UserCategory_OldUser_Type        UserCategory = 1
	UserCategory_BothUser_Type       UserCategory = 2
	UserCategory_QuickEnterUser_Type UserCategory = 3
)

var UserCategory_name = map[int32]string{
	0: "NewUser_Type",
	1: "OldUser_Type",
	2: "BothUser_Type",
	3: "QuickEnterUser_Type",
}
var UserCategory_value = map[string]int32{
	"NewUser_Type":        0,
	"OldUser_Type":        1,
	"BothUser_Type":       2,
	"QuickEnterUser_Type": 3,
}

func (x UserCategory) String() string {
	return proto.EnumName(UserCategory_name, int32(x))
}
func (UserCategory) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{2}
}

type ChannelCategory int32

const (
	ChannelCategory_Activity_BIG           ChannelCategory = 0
	ChannelCategory_Activity_SAMLL         ChannelCategory = 1
	ChannelCategory_Used_Enter             ChannelCategory = 2
	ChannelCategory_Normal_Level           ChannelCategory = 3
	ChannelCategory_Hot_Channel            ChannelCategory = 4
	ChannelCategory_Roi_Channel            ChannelCategory = 5
	ChannelCategory_Guild_Channel          ChannelCategory = 6
	ChannelCategory_Relationship_Channel   ChannelCategory = 7
	ChannelCategory_HighQualityPgc_Channel ChannelCategory = 8
)

var ChannelCategory_name = map[int32]string{
	0: "Activity_BIG",
	1: "Activity_SAMLL",
	2: "Used_Enter",
	3: "Normal_Level",
	4: "Hot_Channel",
	5: "Roi_Channel",
	6: "Guild_Channel",
	7: "Relationship_Channel",
	8: "HighQualityPgc_Channel",
}
var ChannelCategory_value = map[string]int32{
	"Activity_BIG":           0,
	"Activity_SAMLL":         1,
	"Used_Enter":             2,
	"Normal_Level":           3,
	"Hot_Channel":            4,
	"Roi_Channel":            5,
	"Guild_Channel":          6,
	"Relationship_Channel":   7,
	"HighQualityPgc_Channel": 8,
}

func (x ChannelCategory) String() string {
	return proto.EnumName(ChannelCategory_name, int32(x))
}
func (ChannelCategory) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{3}
}

type ChannelLevel int32

const (
	ChannelLevel_Channel_Invalid ChannelLevel = 0
	ChannelLevel_Channel_Level_S ChannelLevel = 1
	ChannelLevel_Channel_Level_A ChannelLevel = 2
	ChannelLevel_Channel_Level_B ChannelLevel = 3
	ChannelLevel_Channel_Level_C ChannelLevel = 4
	ChannelLevel_Channel_Level_D ChannelLevel = 5
	ChannelLevel_Channel_Level_E ChannelLevel = 6
	ChannelLevel_Channel_Level_F ChannelLevel = 7
	ChannelLevel_Channel_Level_G ChannelLevel = 8
)

var ChannelLevel_name = map[int32]string{
	0: "Channel_Invalid",
	1: "Channel_Level_S",
	2: "Channel_Level_A",
	3: "Channel_Level_B",
	4: "Channel_Level_C",
	5: "Channel_Level_D",
	6: "Channel_Level_E",
	7: "Channel_Level_F",
	8: "Channel_Level_G",
}
var ChannelLevel_value = map[string]int32{
	"Channel_Invalid": 0,
	"Channel_Level_S": 1,
	"Channel_Level_A": 2,
	"Channel_Level_B": 3,
	"Channel_Level_C": 4,
	"Channel_Level_D": 5,
	"Channel_Level_E": 6,
	"Channel_Level_F": 7,
	"Channel_Level_G": 8,
}

func (x ChannelLevel) String() string {
	return proto.EnumName(ChannelLevel_name, int32(x))
}
func (ChannelLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{4}
}

// 房间关系链类型
type RecommendRelationshipType int32

const (
	RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_NONE           RecommendRelationshipType = 0
	RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_MIC     RecommendRelationshipType = 1
	RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_CHANNEL RecommendRelationshipType = 2
	RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_COLLECT        RecommendRelationshipType = 3
	RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_FOLLOW         RecommendRelationshipType = 4
	RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_USED_ENTER     RecommendRelationshipType = 5
)

var RecommendRelationshipType_name = map[int32]string{
	0: "RECOMMMENT_RELATIONSHIP_TYPE_NONE",
	1: "RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_MIC",
	2: "RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_CHANNEL",
	3: "RECOMMMENT_RELATIONSHIP_TYPE_COLLECT",
	4: "RECOMMMENT_RELATIONSHIP_TYPE_FOLLOW",
	5: "RECOMMMENT_RELATIONSHIP_TYPE_USED_ENTER",
}
var RecommendRelationshipType_value = map[string]int32{
	"RECOMMMENT_RELATIONSHIP_TYPE_NONE":           0,
	"RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_MIC":     1,
	"RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_CHANNEL": 2,
	"RECOMMMENT_RELATIONSHIP_TYPE_COLLECT":        3,
	"RECOMMMENT_RELATIONSHIP_TYPE_FOLLOW":         4,
	"RECOMMMENT_RELATIONSHIP_TYPE_USED_ENTER":     5,
}

func (x RecommendRelationshipType) String() string {
	return proto.EnumName(RecommendRelationshipType_name, int32(x))
}
func (RecommendRelationshipType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{5}
}

type RoiChannelCertType int32

const (
	// roi承接房间标识定义，移位定义
	RoiChannelCertType_RoiChannelCertTypeNone    RoiChannelCertType = 0
	RoiChannelCertType_RoiChannelCertTypeFriend  RoiChannelCertType = 1
	RoiChannelCertType_RoiChannelCertTypeFollow  RoiChannelCertType = 2
	RoiChannelCertType_RoiChannelCertTypeCollect RoiChannelCertType = 4
	RoiChannelCertType_RoiChannelCertTypeNew     RoiChannelCertType = 8
)

var RoiChannelCertType_name = map[int32]string{
	0: "RoiChannelCertTypeNone",
	1: "RoiChannelCertTypeFriend",
	2: "RoiChannelCertTypeFollow",
	4: "RoiChannelCertTypeCollect",
	8: "RoiChannelCertTypeNew",
}
var RoiChannelCertType_value = map[string]int32{
	"RoiChannelCertTypeNone":    0,
	"RoiChannelCertTypeFriend":  1,
	"RoiChannelCertTypeFollow":  2,
	"RoiChannelCertTypeCollect": 4,
	"RoiChannelCertTypeNew":     8,
}

func (x RoiChannelCertType) String() string {
	return proto.EnumName(RoiChannelCertType_name, int32(x))
}
func (RoiChannelCertType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{6}
}

// 推荐库配置类型
type PrepareType int32

const (
	PrepareType_Prepare_Type_Invalid PrepareType = 0
	PrepareType_Prepare_Type_Manual  PrepareType = 1
	PrepareType_Prepare_Type_Auto    PrepareType = 2
)

var PrepareType_name = map[int32]string{
	0: "Prepare_Type_Invalid",
	1: "Prepare_Type_Manual",
	2: "Prepare_Type_Auto",
}
var PrepareType_value = map[string]int32{
	"Prepare_Type_Invalid": 0,
	"Prepare_Type_Manual":  1,
	"Prepare_Type_Auto":    2,
}

func (x PrepareType) String() string {
	return proto.EnumName(PrepareType_name, int32(x))
}
func (PrepareType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{7}
}

// 推荐库操作类型
type PrepareOperType int32

const (
	PrepareOperType_Prepare_Oper_Type_Invalid PrepareOperType = 0
	PrepareOperType_Prepare_Oper_Type_Add     PrepareOperType = 1
	PrepareOperType_Prepare_Oper_Type_Update  PrepareOperType = 2
	PrepareOperType_Prepare_Oper_Type_Del     PrepareOperType = 3
)

var PrepareOperType_name = map[int32]string{
	0: "Prepare_Oper_Type_Invalid",
	1: "Prepare_Oper_Type_Add",
	2: "Prepare_Oper_Type_Update",
	3: "Prepare_Oper_Type_Del",
}
var PrepareOperType_value = map[string]int32{
	"Prepare_Oper_Type_Invalid": 0,
	"Prepare_Oper_Type_Add":     1,
	"Prepare_Oper_Type_Update":  2,
	"Prepare_Oper_Type_Del":     3,
}

func (x PrepareOperType) String() string {
	return proto.EnumName(PrepareOperType_name, int32(x))
}
func (PrepareOperType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{8}
}

// 配置状态
type DisplaySceneStatusType int32

const (
	DisplaySceneStatusType_Display_Scene_Status_Invalid DisplaySceneStatusType = 0
	DisplaySceneStatusType_Display_Scene_Status_Future  DisplaySceneStatusType = 1
	DisplaySceneStatusType_Display_Scene_Status_Valid   DisplaySceneStatusType = 2
	DisplaySceneStatusType_Display_Scene_Status_Expire  DisplaySceneStatusType = 3
)

var DisplaySceneStatusType_name = map[int32]string{
	0: "Display_Scene_Status_Invalid",
	1: "Display_Scene_Status_Future",
	2: "Display_Scene_Status_Valid",
	3: "Display_Scene_Status_Expire",
}
var DisplaySceneStatusType_value = map[string]int32{
	"Display_Scene_Status_Invalid": 0,
	"Display_Scene_Status_Future":  1,
	"Display_Scene_Status_Valid":   2,
	"Display_Scene_Status_Expire":  3,
}

func (x DisplaySceneStatusType) String() string {
	return proto.EnumName(DisplaySceneStatusType_name, int32(x))
}
func (DisplaySceneStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{9}
}

// 配置时间类型
type DisplaySceneTimeType int32

const (
	DisplaySceneTimeType_Display_Scene_Time_Invalid DisplaySceneTimeType = 0
	DisplaySceneTimeType_Display_Scene_Time_Regular DisplaySceneTimeType = 1
	DisplaySceneTimeType_Display_Scene_Time_Forever DisplaySceneTimeType = 2
)

var DisplaySceneTimeType_name = map[int32]string{
	0: "Display_Scene_Time_Invalid",
	1: "Display_Scene_Time_Regular",
	2: "Display_Scene_Time_Forever",
}
var DisplaySceneTimeType_value = map[string]int32{
	"Display_Scene_Time_Invalid": 0,
	"Display_Scene_Time_Regular": 1,
	"Display_Scene_Time_Forever": 2,
}

func (x DisplaySceneTimeType) String() string {
	return proto.EnumName(DisplaySceneTimeType_name, int32(x))
}
func (DisplaySceneTimeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{10}
}

type QuickRecChannelType int32

const (
	QuickRecChannelType_Normal QuickRecChannelType = 0
	QuickRecChannelType_MicSex QuickRecChannelType = 1
)

var QuickRecChannelType_name = map[int32]string{
	0: "Normal",
	1: "MicSex",
}
var QuickRecChannelType_value = map[string]int32{
	"Normal": 0,
	"MicSex": 1,
}

func (x QuickRecChannelType) String() string {
	return proto.EnumName(QuickRecChannelType_name, int32(x))
}
func (QuickRecChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{11}
}

type GetFlowCardListByTypeReq_GetType int32

const (
	GetFlowCardListByTypeReq_NotUseAll GetFlowCardListByTypeReq_GetType = 0
)

var GetFlowCardListByTypeReq_GetType_name = map[int32]string{
	0: "NotUseAll",
}
var GetFlowCardListByTypeReq_GetType_value = map[string]int32{
	"NotUseAll": 0,
}

func (x GetFlowCardListByTypeReq_GetType) String() string {
	return proto.EnumName(GetFlowCardListByTypeReq_GetType_name, int32(x))
}
func (GetFlowCardListByTypeReq_GetType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{29, 0}
}

// 定时任务类型
type TriggerTimerReq_TimerType int32

const (
	TriggerTimerReq_Timer_Type_Invalid                TriggerTimerReq_TimerType = 0
	TriggerTimerReq_Timer_Type_AutoGenPgcPrepareLevel TriggerTimerReq_TimerType = 1
	TriggerTimerReq_Timer_Type_LoadPgcPrepareLevel    TriggerTimerReq_TimerType = 2
)

var TriggerTimerReq_TimerType_name = map[int32]string{
	0: "Timer_Type_Invalid",
	1: "Timer_Type_AutoGenPgcPrepareLevel",
	2: "Timer_Type_LoadPgcPrepareLevel",
}
var TriggerTimerReq_TimerType_value = map[string]int32{
	"Timer_Type_Invalid":                0,
	"Timer_Type_AutoGenPgcPrepareLevel": 1,
	"Timer_Type_LoadPgcPrepareLevel":    2,
}

func (x TriggerTimerReq_TimerType) String() string {
	return proto.EnumName(TriggerTimerReq_TimerType_name, int32(x))
}
func (TriggerTimerReq_TimerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{69, 0}
}

// 查询场景
type GetUserQualityInfoReq_QueryScene int32

const (
	GetUserQualityInfoReq_Query_Scene_Invalid GetUserQualityInfoReq_QueryScene = 0
	GetUserQualityInfoReq_Query_Scene_Login   GetUserQualityInfoReq_QueryScene = 1
	GetUserQualityInfoReq_Query_Scene_Channel GetUserQualityInfoReq_QueryScene = 2
)

var GetUserQualityInfoReq_QueryScene_name = map[int32]string{
	0: "Query_Scene_Invalid",
	1: "Query_Scene_Login",
	2: "Query_Scene_Channel",
}
var GetUserQualityInfoReq_QueryScene_value = map[string]int32{
	"Query_Scene_Invalid": 0,
	"Query_Scene_Login":   1,
	"Query_Scene_Channel": 2,
}

func (x GetUserQualityInfoReq_QueryScene) String() string {
	return proto.EnumName(GetUserQualityInfoReq_QueryScene_name, int32(x))
}
func (GetUserQualityInfoReq_QueryScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{101, 0}
}

// 流量卡限额配置
type LevelConf struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Cnt                  uint32   `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelConf) Reset()         { *m = LevelConf{} }
func (m *LevelConf) String() string { return proto.CompactTextString(m) }
func (*LevelConf) ProtoMessage()    {}
func (*LevelConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{0}
}
func (m *LevelConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConf.Unmarshal(m, b)
}
func (m *LevelConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConf.Marshal(b, m, deterministic)
}
func (dst *LevelConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConf.Merge(dst, src)
}
func (m *LevelConf) XXX_Size() int {
	return xxx_messageInfo_LevelConf.Size(m)
}
func (m *LevelConf) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConf.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConf proto.InternalMessageInfo

func (m *LevelConf) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelConf) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type LimitConf struct {
	HourCnt              uint32       `protobuf:"varint,1,opt,name=hourCnt,proto3" json:"hourCnt,omitempty"`
	ConfList             []*LevelConf `protobuf:"bytes,2,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LimitConf) Reset()         { *m = LimitConf{} }
func (m *LimitConf) String() string { return proto.CompactTextString(m) }
func (*LimitConf) ProtoMessage()    {}
func (*LimitConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{1}
}
func (m *LimitConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LimitConf.Unmarshal(m, b)
}
func (m *LimitConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LimitConf.Marshal(b, m, deterministic)
}
func (dst *LimitConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LimitConf.Merge(dst, src)
}
func (m *LimitConf) XXX_Size() int {
	return xxx_messageInfo_LimitConf.Size(m)
}
func (m *LimitConf) XXX_DiscardUnknown() {
	xxx_messageInfo_LimitConf.DiscardUnknown(m)
}

var xxx_messageInfo_LimitConf proto.InternalMessageInfo

func (m *LimitConf) GetHourCnt() uint32 {
	if m != nil {
		return m.HourCnt
	}
	return 0
}

func (m *LimitConf) GetConfList() []*LevelConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type FlowCardLimitConf struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BeginTs              uint32       `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32       `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	ConfList             []*LimitConf `protobuf:"bytes,4,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *FlowCardLimitConf) Reset()         { *m = FlowCardLimitConf{} }
func (m *FlowCardLimitConf) String() string { return proto.CompactTextString(m) }
func (*FlowCardLimitConf) ProtoMessage()    {}
func (*FlowCardLimitConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{2}
}
func (m *FlowCardLimitConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlowCardLimitConf.Unmarshal(m, b)
}
func (m *FlowCardLimitConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlowCardLimitConf.Marshal(b, m, deterministic)
}
func (dst *FlowCardLimitConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlowCardLimitConf.Merge(dst, src)
}
func (m *FlowCardLimitConf) XXX_Size() int {
	return xxx_messageInfo_FlowCardLimitConf.Size(m)
}
func (m *FlowCardLimitConf) XXX_DiscardUnknown() {
	xxx_messageInfo_FlowCardLimitConf.DiscardUnknown(m)
}

var xxx_messageInfo_FlowCardLimitConf proto.InternalMessageInfo

func (m *FlowCardLimitConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FlowCardLimitConf) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *FlowCardLimitConf) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *FlowCardLimitConf) GetConfList() []*LimitConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type GetFlowCardLimitConfListReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFlowCardLimitConfListReq) Reset()         { *m = GetFlowCardLimitConfListReq{} }
func (m *GetFlowCardLimitConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetFlowCardLimitConfListReq) ProtoMessage()    {}
func (*GetFlowCardLimitConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{3}
}
func (m *GetFlowCardLimitConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowCardLimitConfListReq.Unmarshal(m, b)
}
func (m *GetFlowCardLimitConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowCardLimitConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetFlowCardLimitConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowCardLimitConfListReq.Merge(dst, src)
}
func (m *GetFlowCardLimitConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetFlowCardLimitConfListReq.Size(m)
}
func (m *GetFlowCardLimitConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowCardLimitConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowCardLimitConfListReq proto.InternalMessageInfo

func (m *GetFlowCardLimitConfListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetFlowCardLimitConfListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetFlowCardLimitConfListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetFlowCardLimitConfListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetFlowCardLimitConfListResp struct {
	ConfList             []*FlowCardLimitConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	NextPage             uint32               `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetFlowCardLimitConfListResp) Reset()         { *m = GetFlowCardLimitConfListResp{} }
func (m *GetFlowCardLimitConfListResp) String() string { return proto.CompactTextString(m) }
func (*GetFlowCardLimitConfListResp) ProtoMessage()    {}
func (*GetFlowCardLimitConfListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{4}
}
func (m *GetFlowCardLimitConfListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowCardLimitConfListResp.Unmarshal(m, b)
}
func (m *GetFlowCardLimitConfListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowCardLimitConfListResp.Marshal(b, m, deterministic)
}
func (dst *GetFlowCardLimitConfListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowCardLimitConfListResp.Merge(dst, src)
}
func (m *GetFlowCardLimitConfListResp) XXX_Size() int {
	return xxx_messageInfo_GetFlowCardLimitConfListResp.Size(m)
}
func (m *GetFlowCardLimitConfListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowCardLimitConfListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowCardLimitConfListResp proto.InternalMessageInfo

func (m *GetFlowCardLimitConfListResp) GetConfList() []*FlowCardLimitConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *GetFlowCardLimitConfListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

func (m *GetFlowCardLimitConfListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 增加流量卡限额配置
type AddFlowCardLimitConfReq struct {
	Conf                 *FlowCardLimitConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddFlowCardLimitConfReq) Reset()         { *m = AddFlowCardLimitConfReq{} }
func (m *AddFlowCardLimitConfReq) String() string { return proto.CompactTextString(m) }
func (*AddFlowCardLimitConfReq) ProtoMessage()    {}
func (*AddFlowCardLimitConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{5}
}
func (m *AddFlowCardLimitConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFlowCardLimitConfReq.Unmarshal(m, b)
}
func (m *AddFlowCardLimitConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFlowCardLimitConfReq.Marshal(b, m, deterministic)
}
func (dst *AddFlowCardLimitConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFlowCardLimitConfReq.Merge(dst, src)
}
func (m *AddFlowCardLimitConfReq) XXX_Size() int {
	return xxx_messageInfo_AddFlowCardLimitConfReq.Size(m)
}
func (m *AddFlowCardLimitConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFlowCardLimitConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFlowCardLimitConfReq proto.InternalMessageInfo

func (m *AddFlowCardLimitConfReq) GetConf() *FlowCardLimitConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type AddFlowCardLimitConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFlowCardLimitConfResp) Reset()         { *m = AddFlowCardLimitConfResp{} }
func (m *AddFlowCardLimitConfResp) String() string { return proto.CompactTextString(m) }
func (*AddFlowCardLimitConfResp) ProtoMessage()    {}
func (*AddFlowCardLimitConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{6}
}
func (m *AddFlowCardLimitConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFlowCardLimitConfResp.Unmarshal(m, b)
}
func (m *AddFlowCardLimitConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFlowCardLimitConfResp.Marshal(b, m, deterministic)
}
func (dst *AddFlowCardLimitConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFlowCardLimitConfResp.Merge(dst, src)
}
func (m *AddFlowCardLimitConfResp) XXX_Size() int {
	return xxx_messageInfo_AddFlowCardLimitConfResp.Size(m)
}
func (m *AddFlowCardLimitConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFlowCardLimitConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddFlowCardLimitConfResp proto.InternalMessageInfo

// 更新流量卡限额配置
type UpdateFlowCardLimitConfReq struct {
	Conf                 *FlowCardLimitConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateFlowCardLimitConfReq) Reset()         { *m = UpdateFlowCardLimitConfReq{} }
func (m *UpdateFlowCardLimitConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFlowCardLimitConfReq) ProtoMessage()    {}
func (*UpdateFlowCardLimitConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{7}
}
func (m *UpdateFlowCardLimitConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFlowCardLimitConfReq.Unmarshal(m, b)
}
func (m *UpdateFlowCardLimitConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFlowCardLimitConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFlowCardLimitConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFlowCardLimitConfReq.Merge(dst, src)
}
func (m *UpdateFlowCardLimitConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFlowCardLimitConfReq.Size(m)
}
func (m *UpdateFlowCardLimitConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFlowCardLimitConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFlowCardLimitConfReq proto.InternalMessageInfo

func (m *UpdateFlowCardLimitConfReq) GetConf() *FlowCardLimitConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateFlowCardLimitConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFlowCardLimitConfResp) Reset()         { *m = UpdateFlowCardLimitConfResp{} }
func (m *UpdateFlowCardLimitConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFlowCardLimitConfResp) ProtoMessage()    {}
func (*UpdateFlowCardLimitConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{8}
}
func (m *UpdateFlowCardLimitConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFlowCardLimitConfResp.Unmarshal(m, b)
}
func (m *UpdateFlowCardLimitConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFlowCardLimitConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateFlowCardLimitConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFlowCardLimitConfResp.Merge(dst, src)
}
func (m *UpdateFlowCardLimitConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateFlowCardLimitConfResp.Size(m)
}
func (m *UpdateFlowCardLimitConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFlowCardLimitConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFlowCardLimitConfResp proto.InternalMessageInfo

// 删除流量卡限额配置
type DelFlowCardLimitConfReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFlowCardLimitConfReq) Reset()         { *m = DelFlowCardLimitConfReq{} }
func (m *DelFlowCardLimitConfReq) String() string { return proto.CompactTextString(m) }
func (*DelFlowCardLimitConfReq) ProtoMessage()    {}
func (*DelFlowCardLimitConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{9}
}
func (m *DelFlowCardLimitConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFlowCardLimitConfReq.Unmarshal(m, b)
}
func (m *DelFlowCardLimitConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFlowCardLimitConfReq.Marshal(b, m, deterministic)
}
func (dst *DelFlowCardLimitConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFlowCardLimitConfReq.Merge(dst, src)
}
func (m *DelFlowCardLimitConfReq) XXX_Size() int {
	return xxx_messageInfo_DelFlowCardLimitConfReq.Size(m)
}
func (m *DelFlowCardLimitConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFlowCardLimitConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelFlowCardLimitConfReq proto.InternalMessageInfo

func (m *DelFlowCardLimitConfReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelFlowCardLimitConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFlowCardLimitConfResp) Reset()         { *m = DelFlowCardLimitConfResp{} }
func (m *DelFlowCardLimitConfResp) String() string { return proto.CompactTextString(m) }
func (*DelFlowCardLimitConfResp) ProtoMessage()    {}
func (*DelFlowCardLimitConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{10}
}
func (m *DelFlowCardLimitConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFlowCardLimitConfResp.Unmarshal(m, b)
}
func (m *DelFlowCardLimitConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFlowCardLimitConfResp.Marshal(b, m, deterministic)
}
func (dst *DelFlowCardLimitConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFlowCardLimitConfResp.Merge(dst, src)
}
func (m *DelFlowCardLimitConfResp) XXX_Size() int {
	return xxx_messageInfo_DelFlowCardLimitConfResp.Size(m)
}
func (m *DelFlowCardLimitConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFlowCardLimitConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelFlowCardLimitConfResp proto.InternalMessageInfo

type FlowCardGrantInfo struct {
	GrantId              uint32   `protobuf:"varint,1,opt,name=grant_id,json=grantId,proto3" json:"grant_id,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	ExpirtTs             uint32   `protobuf:"varint,4,opt,name=expirt_ts,json=expirtTs,proto3" json:"expirt_ts,omitempty"`
	Cnt                  uint32   `protobuf:"varint,5,opt,name=cnt,proto3" json:"cnt,omitempty"`
	Remark               string   `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark,omitempty"`
	BanBeginTs           uint32   `protobuf:"varint,7,opt,name=ban_begin_ts,json=banBeginTs,proto3" json:"ban_begin_ts,omitempty"`
	BanEndTs             uint32   `protobuf:"varint,8,opt,name=ban_end_ts,json=banEndTs,proto3" json:"ban_end_ts,omitempty"`
	UsedCnt              uint32   `protobuf:"varint,9,opt,name=used_cnt,json=usedCnt,proto3" json:"used_cnt,omitempty"`
	UpdateTs             uint32   `protobuf:"varint,10,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	CreateTs             uint32   `protobuf:"varint,11,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	GuildGrantId         uint32   `protobuf:"varint,12,opt,name=guild_grant_id,json=guildGrantId,proto3" json:"guild_grant_id,omitempty"`
	OrderId              string   `protobuf:"bytes,13,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlowCardGrantInfo) Reset()         { *m = FlowCardGrantInfo{} }
func (m *FlowCardGrantInfo) String() string { return proto.CompactTextString(m) }
func (*FlowCardGrantInfo) ProtoMessage()    {}
func (*FlowCardGrantInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{11}
}
func (m *FlowCardGrantInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlowCardGrantInfo.Unmarshal(m, b)
}
func (m *FlowCardGrantInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlowCardGrantInfo.Marshal(b, m, deterministic)
}
func (dst *FlowCardGrantInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlowCardGrantInfo.Merge(dst, src)
}
func (m *FlowCardGrantInfo) XXX_Size() int {
	return xxx_messageInfo_FlowCardGrantInfo.Size(m)
}
func (m *FlowCardGrantInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FlowCardGrantInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FlowCardGrantInfo proto.InternalMessageInfo

func (m *FlowCardGrantInfo) GetGrantId() uint32 {
	if m != nil {
		return m.GrantId
	}
	return 0
}

func (m *FlowCardGrantInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FlowCardGrantInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *FlowCardGrantInfo) GetExpirtTs() uint32 {
	if m != nil {
		return m.ExpirtTs
	}
	return 0
}

func (m *FlowCardGrantInfo) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *FlowCardGrantInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *FlowCardGrantInfo) GetBanBeginTs() uint32 {
	if m != nil {
		return m.BanBeginTs
	}
	return 0
}

func (m *FlowCardGrantInfo) GetBanEndTs() uint32 {
	if m != nil {
		return m.BanEndTs
	}
	return 0
}

func (m *FlowCardGrantInfo) GetUsedCnt() uint32 {
	if m != nil {
		return m.UsedCnt
	}
	return 0
}

func (m *FlowCardGrantInfo) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *FlowCardGrantInfo) GetCreateTs() uint32 {
	if m != nil {
		return m.CreateTs
	}
	return 0
}

func (m *FlowCardGrantInfo) GetGuildGrantId() uint32 {
	if m != nil {
		return m.GuildGrantId
	}
	return 0
}

func (m *FlowCardGrantInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

// 获取流量卡列表
type GetGrantFlowCardListReq struct {
	GrantType            uint32   `protobuf:"varint,1,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	GuildGrantId         uint32   `protobuf:"varint,5,opt,name=guild_grant_id,json=guildGrantId,proto3" json:"guild_grant_id,omitempty"`
	Level                uint32   `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGrantFlowCardListReq) Reset()         { *m = GetGrantFlowCardListReq{} }
func (m *GetGrantFlowCardListReq) String() string { return proto.CompactTextString(m) }
func (*GetGrantFlowCardListReq) ProtoMessage()    {}
func (*GetGrantFlowCardListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{12}
}
func (m *GetGrantFlowCardListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGrantFlowCardListReq.Unmarshal(m, b)
}
func (m *GetGrantFlowCardListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGrantFlowCardListReq.Marshal(b, m, deterministic)
}
func (dst *GetGrantFlowCardListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGrantFlowCardListReq.Merge(dst, src)
}
func (m *GetGrantFlowCardListReq) XXX_Size() int {
	return xxx_messageInfo_GetGrantFlowCardListReq.Size(m)
}
func (m *GetGrantFlowCardListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGrantFlowCardListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGrantFlowCardListReq proto.InternalMessageInfo

func (m *GetGrantFlowCardListReq) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *GetGrantFlowCardListReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetGrantFlowCardListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGrantFlowCardListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetGrantFlowCardListReq) GetGuildGrantId() uint32 {
	if m != nil {
		return m.GuildGrantId
	}
	return 0
}

func (m *GetGrantFlowCardListReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type GetGrantFlowCardListResp struct {
	InfoList             []*FlowCardGrantInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	NextPage             uint32               `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetGrantFlowCardListResp) Reset()         { *m = GetGrantFlowCardListResp{} }
func (m *GetGrantFlowCardListResp) String() string { return proto.CompactTextString(m) }
func (*GetGrantFlowCardListResp) ProtoMessage()    {}
func (*GetGrantFlowCardListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{13}
}
func (m *GetGrantFlowCardListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGrantFlowCardListResp.Unmarshal(m, b)
}
func (m *GetGrantFlowCardListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGrantFlowCardListResp.Marshal(b, m, deterministic)
}
func (dst *GetGrantFlowCardListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGrantFlowCardListResp.Merge(dst, src)
}
func (m *GetGrantFlowCardListResp) XXX_Size() int {
	return xxx_messageInfo_GetGrantFlowCardListResp.Size(m)
}
func (m *GetGrantFlowCardListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGrantFlowCardListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGrantFlowCardListResp proto.InternalMessageInfo

func (m *GetGrantFlowCardListResp) GetInfoList() []*FlowCardGrantInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetGrantFlowCardListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

func (m *GetGrantFlowCardListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 发放流量卡
type GrantFlowCardReq struct {
	GrantType            uint32             `protobuf:"varint,1,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	Info                 *FlowCardGrantInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GrantFlowCardReq) Reset()         { *m = GrantFlowCardReq{} }
func (m *GrantFlowCardReq) String() string { return proto.CompactTextString(m) }
func (*GrantFlowCardReq) ProtoMessage()    {}
func (*GrantFlowCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{14}
}
func (m *GrantFlowCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantFlowCardReq.Unmarshal(m, b)
}
func (m *GrantFlowCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantFlowCardReq.Marshal(b, m, deterministic)
}
func (dst *GrantFlowCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantFlowCardReq.Merge(dst, src)
}
func (m *GrantFlowCardReq) XXX_Size() int {
	return xxx_messageInfo_GrantFlowCardReq.Size(m)
}
func (m *GrantFlowCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantFlowCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GrantFlowCardReq proto.InternalMessageInfo

func (m *GrantFlowCardReq) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *GrantFlowCardReq) GetInfo() *FlowCardGrantInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GrantFlowCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantFlowCardResp) Reset()         { *m = GrantFlowCardResp{} }
func (m *GrantFlowCardResp) String() string { return proto.CompactTextString(m) }
func (*GrantFlowCardResp) ProtoMessage()    {}
func (*GrantFlowCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{15}
}
func (m *GrantFlowCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantFlowCardResp.Unmarshal(m, b)
}
func (m *GrantFlowCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantFlowCardResp.Marshal(b, m, deterministic)
}
func (dst *GrantFlowCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantFlowCardResp.Merge(dst, src)
}
func (m *GrantFlowCardResp) XXX_Size() int {
	return xxx_messageInfo_GrantFlowCardResp.Size(m)
}
func (m *GrantFlowCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantFlowCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GrantFlowCardResp proto.InternalMessageInfo

type GrantErrorMsg struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ErrCode              int32    `protobuf:"varint,2,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,3,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	Id                   uint32   `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrantErrorMsg) Reset()         { *m = GrantErrorMsg{} }
func (m *GrantErrorMsg) String() string { return proto.CompactTextString(m) }
func (*GrantErrorMsg) ProtoMessage()    {}
func (*GrantErrorMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{16}
}
func (m *GrantErrorMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantErrorMsg.Unmarshal(m, b)
}
func (m *GrantErrorMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantErrorMsg.Marshal(b, m, deterministic)
}
func (dst *GrantErrorMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantErrorMsg.Merge(dst, src)
}
func (m *GrantErrorMsg) XXX_Size() int {
	return xxx_messageInfo_GrantErrorMsg.Size(m)
}
func (m *GrantErrorMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantErrorMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GrantErrorMsg proto.InternalMessageInfo

func (m *GrantErrorMsg) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GrantErrorMsg) GetErrCode() int32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GrantErrorMsg) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GrantErrorMsg) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 批量发放流量卡
type BatGrantFlowCardReq struct {
	GrantType            uint32               `protobuf:"varint,1,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	InfoList             []*FlowCardGrantInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatGrantFlowCardReq) Reset()         { *m = BatGrantFlowCardReq{} }
func (m *BatGrantFlowCardReq) String() string { return proto.CompactTextString(m) }
func (*BatGrantFlowCardReq) ProtoMessage()    {}
func (*BatGrantFlowCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{17}
}
func (m *BatGrantFlowCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGrantFlowCardReq.Unmarshal(m, b)
}
func (m *BatGrantFlowCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGrantFlowCardReq.Marshal(b, m, deterministic)
}
func (dst *BatGrantFlowCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGrantFlowCardReq.Merge(dst, src)
}
func (m *BatGrantFlowCardReq) XXX_Size() int {
	return xxx_messageInfo_BatGrantFlowCardReq.Size(m)
}
func (m *BatGrantFlowCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGrantFlowCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGrantFlowCardReq proto.InternalMessageInfo

func (m *BatGrantFlowCardReq) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *BatGrantFlowCardReq) GetInfoList() []*FlowCardGrantInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BatGrantFlowCardResp struct {
	ErrList              []*GrantErrorMsg `protobuf:"bytes,1,rep,name=err_list,json=errList,proto3" json:"err_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatGrantFlowCardResp) Reset()         { *m = BatGrantFlowCardResp{} }
func (m *BatGrantFlowCardResp) String() string { return proto.CompactTextString(m) }
func (*BatGrantFlowCardResp) ProtoMessage()    {}
func (*BatGrantFlowCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{18}
}
func (m *BatGrantFlowCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGrantFlowCardResp.Unmarshal(m, b)
}
func (m *BatGrantFlowCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGrantFlowCardResp.Marshal(b, m, deterministic)
}
func (dst *BatGrantFlowCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGrantFlowCardResp.Merge(dst, src)
}
func (m *BatGrantFlowCardResp) XXX_Size() int {
	return xxx_messageInfo_BatGrantFlowCardResp.Size(m)
}
func (m *BatGrantFlowCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGrantFlowCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGrantFlowCardResp proto.InternalMessageInfo

func (m *BatGrantFlowCardResp) GetErrList() []*GrantErrorMsg {
	if m != nil {
		return m.ErrList
	}
	return nil
}

// 回收流量卡
type ReclaimGrantedFlowCardReq struct {
	GrantType            uint32   `protobuf:"varint,1,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	GrantId              uint32   `protobuf:"varint,2,opt,name=grant_id,json=grantId,proto3" json:"grant_id,omitempty"`
	ReclaimCnt           uint32   `protobuf:"varint,3,opt,name=reclaim_cnt,json=reclaimCnt,proto3" json:"reclaim_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReclaimGrantedFlowCardReq) Reset()         { *m = ReclaimGrantedFlowCardReq{} }
func (m *ReclaimGrantedFlowCardReq) String() string { return proto.CompactTextString(m) }
func (*ReclaimGrantedFlowCardReq) ProtoMessage()    {}
func (*ReclaimGrantedFlowCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{19}
}
func (m *ReclaimGrantedFlowCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReclaimGrantedFlowCardReq.Unmarshal(m, b)
}
func (m *ReclaimGrantedFlowCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReclaimGrantedFlowCardReq.Marshal(b, m, deterministic)
}
func (dst *ReclaimGrantedFlowCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReclaimGrantedFlowCardReq.Merge(dst, src)
}
func (m *ReclaimGrantedFlowCardReq) XXX_Size() int {
	return xxx_messageInfo_ReclaimGrantedFlowCardReq.Size(m)
}
func (m *ReclaimGrantedFlowCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReclaimGrantedFlowCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReclaimGrantedFlowCardReq proto.InternalMessageInfo

func (m *ReclaimGrantedFlowCardReq) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *ReclaimGrantedFlowCardReq) GetGrantId() uint32 {
	if m != nil {
		return m.GrantId
	}
	return 0
}

func (m *ReclaimGrantedFlowCardReq) GetReclaimCnt() uint32 {
	if m != nil {
		return m.ReclaimCnt
	}
	return 0
}

type ReclaimGrantedFlowCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReclaimGrantedFlowCardResp) Reset()         { *m = ReclaimGrantedFlowCardResp{} }
func (m *ReclaimGrantedFlowCardResp) String() string { return proto.CompactTextString(m) }
func (*ReclaimGrantedFlowCardResp) ProtoMessage()    {}
func (*ReclaimGrantedFlowCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{20}
}
func (m *ReclaimGrantedFlowCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReclaimGrantedFlowCardResp.Unmarshal(m, b)
}
func (m *ReclaimGrantedFlowCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReclaimGrantedFlowCardResp.Marshal(b, m, deterministic)
}
func (dst *ReclaimGrantedFlowCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReclaimGrantedFlowCardResp.Merge(dst, src)
}
func (m *ReclaimGrantedFlowCardResp) XXX_Size() int {
	return xxx_messageInfo_ReclaimGrantedFlowCardResp.Size(m)
}
func (m *ReclaimGrantedFlowCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReclaimGrantedFlowCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReclaimGrantedFlowCardResp proto.InternalMessageInfo

// 禁用流量卡
type BanGrantedFlowCardReq struct {
	GrantType            uint32   `protobuf:"varint,1,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	GrantId              uint32   `protobuf:"varint,2,opt,name=grant_id,json=grantId,proto3" json:"grant_id,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanGrantedFlowCardReq) Reset()         { *m = BanGrantedFlowCardReq{} }
func (m *BanGrantedFlowCardReq) String() string { return proto.CompactTextString(m) }
func (*BanGrantedFlowCardReq) ProtoMessage()    {}
func (*BanGrantedFlowCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{21}
}
func (m *BanGrantedFlowCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanGrantedFlowCardReq.Unmarshal(m, b)
}
func (m *BanGrantedFlowCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanGrantedFlowCardReq.Marshal(b, m, deterministic)
}
func (dst *BanGrantedFlowCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanGrantedFlowCardReq.Merge(dst, src)
}
func (m *BanGrantedFlowCardReq) XXX_Size() int {
	return xxx_messageInfo_BanGrantedFlowCardReq.Size(m)
}
func (m *BanGrantedFlowCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BanGrantedFlowCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_BanGrantedFlowCardReq proto.InternalMessageInfo

func (m *BanGrantedFlowCardReq) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *BanGrantedFlowCardReq) GetGrantId() uint32 {
	if m != nil {
		return m.GrantId
	}
	return 0
}

func (m *BanGrantedFlowCardReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *BanGrantedFlowCardReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type BanGrantedFlowCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BanGrantedFlowCardResp) Reset()         { *m = BanGrantedFlowCardResp{} }
func (m *BanGrantedFlowCardResp) String() string { return proto.CompactTextString(m) }
func (*BanGrantedFlowCardResp) ProtoMessage()    {}
func (*BanGrantedFlowCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{22}
}
func (m *BanGrantedFlowCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BanGrantedFlowCardResp.Unmarshal(m, b)
}
func (m *BanGrantedFlowCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BanGrantedFlowCardResp.Marshal(b, m, deterministic)
}
func (dst *BanGrantedFlowCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BanGrantedFlowCardResp.Merge(dst, src)
}
func (m *BanGrantedFlowCardResp) XXX_Size() int {
	return xxx_messageInfo_BanGrantedFlowCardResp.Size(m)
}
func (m *BanGrantedFlowCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BanGrantedFlowCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_BanGrantedFlowCardResp proto.InternalMessageInfo

// 获取小时流量卡使用的剩余数量
type GetFlowCardHourRemainCntReq struct {
	Ts                   uint32   `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFlowCardHourRemainCntReq) Reset()         { *m = GetFlowCardHourRemainCntReq{} }
func (m *GetFlowCardHourRemainCntReq) String() string { return proto.CompactTextString(m) }
func (*GetFlowCardHourRemainCntReq) ProtoMessage()    {}
func (*GetFlowCardHourRemainCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{23}
}
func (m *GetFlowCardHourRemainCntReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowCardHourRemainCntReq.Unmarshal(m, b)
}
func (m *GetFlowCardHourRemainCntReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowCardHourRemainCntReq.Marshal(b, m, deterministic)
}
func (dst *GetFlowCardHourRemainCntReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowCardHourRemainCntReq.Merge(dst, src)
}
func (m *GetFlowCardHourRemainCntReq) XXX_Size() int {
	return xxx_messageInfo_GetFlowCardHourRemainCntReq.Size(m)
}
func (m *GetFlowCardHourRemainCntReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowCardHourRemainCntReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowCardHourRemainCntReq proto.InternalMessageInfo

func (m *GetFlowCardHourRemainCntReq) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *GetFlowCardHourRemainCntReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type GetFlowCardHourRemainCntResp struct {
	RemainCnt            uint32   `protobuf:"varint,1,opt,name=remain_cnt,json=remainCnt,proto3" json:"remain_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFlowCardHourRemainCntResp) Reset()         { *m = GetFlowCardHourRemainCntResp{} }
func (m *GetFlowCardHourRemainCntResp) String() string { return proto.CompactTextString(m) }
func (*GetFlowCardHourRemainCntResp) ProtoMessage()    {}
func (*GetFlowCardHourRemainCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{24}
}
func (m *GetFlowCardHourRemainCntResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowCardHourRemainCntResp.Unmarshal(m, b)
}
func (m *GetFlowCardHourRemainCntResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowCardHourRemainCntResp.Marshal(b, m, deterministic)
}
func (dst *GetFlowCardHourRemainCntResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowCardHourRemainCntResp.Merge(dst, src)
}
func (m *GetFlowCardHourRemainCntResp) XXX_Size() int {
	return xxx_messageInfo_GetFlowCardHourRemainCntResp.Size(m)
}
func (m *GetFlowCardHourRemainCntResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowCardHourRemainCntResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowCardHourRemainCntResp proto.InternalMessageInfo

func (m *GetFlowCardHourRemainCntResp) GetRemainCnt() uint32 {
	if m != nil {
		return m.RemainCnt
	}
	return 0
}

// 主播或者公会直接使用流量卡
type UseFlowCardReq struct {
	GrantId              uint32   `protobuf:"varint,1,opt,name=grant_id,json=grantId,proto3" json:"grant_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	GuildId              uint32   `protobuf:"varint,4,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UseFlowCardReq) Reset()         { *m = UseFlowCardReq{} }
func (m *UseFlowCardReq) String() string { return proto.CompactTextString(m) }
func (*UseFlowCardReq) ProtoMessage()    {}
func (*UseFlowCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{25}
}
func (m *UseFlowCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseFlowCardReq.Unmarshal(m, b)
}
func (m *UseFlowCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseFlowCardReq.Marshal(b, m, deterministic)
}
func (dst *UseFlowCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseFlowCardReq.Merge(dst, src)
}
func (m *UseFlowCardReq) XXX_Size() int {
	return xxx_messageInfo_UseFlowCardReq.Size(m)
}
func (m *UseFlowCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UseFlowCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_UseFlowCardReq proto.InternalMessageInfo

func (m *UseFlowCardReq) GetGrantId() uint32 {
	if m != nil {
		return m.GrantId
	}
	return 0
}

func (m *UseFlowCardReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *UseFlowCardReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *UseFlowCardReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type UseFlowCardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UseFlowCardResp) Reset()         { *m = UseFlowCardResp{} }
func (m *UseFlowCardResp) String() string { return proto.CompactTextString(m) }
func (*UseFlowCardResp) ProtoMessage()    {}
func (*UseFlowCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{26}
}
func (m *UseFlowCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseFlowCardResp.Unmarshal(m, b)
}
func (m *UseFlowCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseFlowCardResp.Marshal(b, m, deterministic)
}
func (dst *UseFlowCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseFlowCardResp.Merge(dst, src)
}
func (m *UseFlowCardResp) XXX_Size() int {
	return xxx_messageInfo_UseFlowCardResp.Size(m)
}
func (m *UseFlowCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UseFlowCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_UseFlowCardResp proto.InternalMessageInfo

// 公会发放主播流量卡
type GrantAnchorFlowCardByGuildReq struct {
	GuildId              uint32               `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GrantId              uint32               `protobuf:"varint,2,opt,name=grant_id,json=grantId,proto3" json:"grant_id,omitempty"`
	InfoList             []*FlowCardGrantInfo `protobuf:"bytes,3,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GrantAnchorFlowCardByGuildReq) Reset()         { *m = GrantAnchorFlowCardByGuildReq{} }
func (m *GrantAnchorFlowCardByGuildReq) String() string { return proto.CompactTextString(m) }
func (*GrantAnchorFlowCardByGuildReq) ProtoMessage()    {}
func (*GrantAnchorFlowCardByGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{27}
}
func (m *GrantAnchorFlowCardByGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantAnchorFlowCardByGuildReq.Unmarshal(m, b)
}
func (m *GrantAnchorFlowCardByGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantAnchorFlowCardByGuildReq.Marshal(b, m, deterministic)
}
func (dst *GrantAnchorFlowCardByGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantAnchorFlowCardByGuildReq.Merge(dst, src)
}
func (m *GrantAnchorFlowCardByGuildReq) XXX_Size() int {
	return xxx_messageInfo_GrantAnchorFlowCardByGuildReq.Size(m)
}
func (m *GrantAnchorFlowCardByGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantAnchorFlowCardByGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_GrantAnchorFlowCardByGuildReq proto.InternalMessageInfo

func (m *GrantAnchorFlowCardByGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GrantAnchorFlowCardByGuildReq) GetGrantId() uint32 {
	if m != nil {
		return m.GrantId
	}
	return 0
}

func (m *GrantAnchorFlowCardByGuildReq) GetInfoList() []*FlowCardGrantInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type GrantAnchorFlowCardByGuildResp struct {
	ErrList              []*GrantErrorMsg `protobuf:"bytes,1,rep,name=err_list,json=errList,proto3" json:"err_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GrantAnchorFlowCardByGuildResp) Reset()         { *m = GrantAnchorFlowCardByGuildResp{} }
func (m *GrantAnchorFlowCardByGuildResp) String() string { return proto.CompactTextString(m) }
func (*GrantAnchorFlowCardByGuildResp) ProtoMessage()    {}
func (*GrantAnchorFlowCardByGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{28}
}
func (m *GrantAnchorFlowCardByGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantAnchorFlowCardByGuildResp.Unmarshal(m, b)
}
func (m *GrantAnchorFlowCardByGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantAnchorFlowCardByGuildResp.Marshal(b, m, deterministic)
}
func (dst *GrantAnchorFlowCardByGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantAnchorFlowCardByGuildResp.Merge(dst, src)
}
func (m *GrantAnchorFlowCardByGuildResp) XXX_Size() int {
	return xxx_messageInfo_GrantAnchorFlowCardByGuildResp.Size(m)
}
func (m *GrantAnchorFlowCardByGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantAnchorFlowCardByGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_GrantAnchorFlowCardByGuildResp proto.InternalMessageInfo

func (m *GrantAnchorFlowCardByGuildResp) GetErrList() []*GrantErrorMsg {
	if m != nil {
		return m.ErrList
	}
	return nil
}

// 根据类型获取公会或者主播的流量卡列表, 现在只支持主播的
type GetFlowCardListByTypeReq struct {
	GrantType            uint32   `protobuf:"varint,1,opt,name=grant_type,json=grantType,proto3" json:"grant_type,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	Id                   uint32   `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFlowCardListByTypeReq) Reset()         { *m = GetFlowCardListByTypeReq{} }
func (m *GetFlowCardListByTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetFlowCardListByTypeReq) ProtoMessage()    {}
func (*GetFlowCardListByTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{29}
}
func (m *GetFlowCardListByTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowCardListByTypeReq.Unmarshal(m, b)
}
func (m *GetFlowCardListByTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowCardListByTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetFlowCardListByTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowCardListByTypeReq.Merge(dst, src)
}
func (m *GetFlowCardListByTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetFlowCardListByTypeReq.Size(m)
}
func (m *GetFlowCardListByTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowCardListByTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowCardListByTypeReq proto.InternalMessageInfo

func (m *GetFlowCardListByTypeReq) GetGrantType() uint32 {
	if m != nil {
		return m.GrantType
	}
	return 0
}

func (m *GetFlowCardListByTypeReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetFlowCardListByTypeReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetFlowCardListByTypeReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetFlowCardListByTypeResp struct {
	InfoList             []*FlowCardGrantInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetFlowCardListByTypeResp) Reset()         { *m = GetFlowCardListByTypeResp{} }
func (m *GetFlowCardListByTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetFlowCardListByTypeResp) ProtoMessage()    {}
func (*GetFlowCardListByTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{30}
}
func (m *GetFlowCardListByTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowCardListByTypeResp.Unmarshal(m, b)
}
func (m *GetFlowCardListByTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowCardListByTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetFlowCardListByTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowCardListByTypeResp.Merge(dst, src)
}
func (m *GetFlowCardListByTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetFlowCardListByTypeResp.Size(m)
}
func (m *GetFlowCardListByTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowCardListByTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowCardListByTypeResp proto.InternalMessageInfo

func (m *GetFlowCardListByTypeResp) GetInfoList() []*FlowCardGrantInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 查询所有使用流量卡的主播
type GetAllUseFlowCardAnchorReq struct {
	Ts                   uint32   `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllUseFlowCardAnchorReq) Reset()         { *m = GetAllUseFlowCardAnchorReq{} }
func (m *GetAllUseFlowCardAnchorReq) String() string { return proto.CompactTextString(m) }
func (*GetAllUseFlowCardAnchorReq) ProtoMessage()    {}
func (*GetAllUseFlowCardAnchorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{31}
}
func (m *GetAllUseFlowCardAnchorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllUseFlowCardAnchorReq.Unmarshal(m, b)
}
func (m *GetAllUseFlowCardAnchorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllUseFlowCardAnchorReq.Marshal(b, m, deterministic)
}
func (dst *GetAllUseFlowCardAnchorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllUseFlowCardAnchorReq.Merge(dst, src)
}
func (m *GetAllUseFlowCardAnchorReq) XXX_Size() int {
	return xxx_messageInfo_GetAllUseFlowCardAnchorReq.Size(m)
}
func (m *GetAllUseFlowCardAnchorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllUseFlowCardAnchorReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllUseFlowCardAnchorReq proto.InternalMessageInfo

func (m *GetAllUseFlowCardAnchorReq) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type GetAllUseFlowCardAnchorResp struct {
	MapCidLv             map[uint32]uint32 `protobuf:"bytes,1,rep,name=map_cid_lv,json=mapCidLv,proto3" json:"map_cid_lv,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAllUseFlowCardAnchorResp) Reset()         { *m = GetAllUseFlowCardAnchorResp{} }
func (m *GetAllUseFlowCardAnchorResp) String() string { return proto.CompactTextString(m) }
func (*GetAllUseFlowCardAnchorResp) ProtoMessage()    {}
func (*GetAllUseFlowCardAnchorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{32}
}
func (m *GetAllUseFlowCardAnchorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllUseFlowCardAnchorResp.Unmarshal(m, b)
}
func (m *GetAllUseFlowCardAnchorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllUseFlowCardAnchorResp.Marshal(b, m, deterministic)
}
func (dst *GetAllUseFlowCardAnchorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllUseFlowCardAnchorResp.Merge(dst, src)
}
func (m *GetAllUseFlowCardAnchorResp) XXX_Size() int {
	return xxx_messageInfo_GetAllUseFlowCardAnchorResp.Size(m)
}
func (m *GetAllUseFlowCardAnchorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllUseFlowCardAnchorResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllUseFlowCardAnchorResp proto.InternalMessageInfo

func (m *GetAllUseFlowCardAnchorResp) GetMapCidLv() map[uint32]uint32 {
	if m != nil {
		return m.MapCidLv
	}
	return nil
}

// 奖品信息
type GiftInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftIcon             string   `protobuf:"bytes,3,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,4,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	GiftPriceType        uint32   `protobuf:"varint,5,opt,name=gift_price_type,json=giftPriceType,proto3" json:"gift_price_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftInfo) Reset()         { *m = GiftInfo{} }
func (m *GiftInfo) String() string { return proto.CompactTextString(m) }
func (*GiftInfo) ProtoMessage()    {}
func (*GiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{33}
}
func (m *GiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftInfo.Unmarshal(m, b)
}
func (m *GiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftInfo.Marshal(b, m, deterministic)
}
func (dst *GiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftInfo.Merge(dst, src)
}
func (m *GiftInfo) XXX_Size() int {
	return xxx_messageInfo_GiftInfo.Size(m)
}
func (m *GiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GiftInfo proto.InternalMessageInfo

func (m *GiftInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *GiftInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *GiftInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *GiftInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *GiftInfo) GetGiftPriceType() uint32 {
	if m != nil {
		return m.GiftPriceType
	}
	return 0
}

// 推荐抽奖房间信息
type RecLotteryChInfo struct {
	ChannelId            uint32    `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LotteryEndTs         uint32    `protobuf:"varint,2,opt,name=lottery_end_ts,json=lotteryEndTs,proto3" json:"lottery_end_ts,omitempty"`
	AwardCnt             uint32    `protobuf:"varint,3,opt,name=award_cnt,json=awardCnt,proto3" json:"award_cnt,omitempty"`
	GiftInfo             *GiftInfo `protobuf:"bytes,4,opt,name=gift_info,json=giftInfo,proto3" json:"gift_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *RecLotteryChInfo) Reset()         { *m = RecLotteryChInfo{} }
func (m *RecLotteryChInfo) String() string { return proto.CompactTextString(m) }
func (*RecLotteryChInfo) ProtoMessage()    {}
func (*RecLotteryChInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{34}
}
func (m *RecLotteryChInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecLotteryChInfo.Unmarshal(m, b)
}
func (m *RecLotteryChInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecLotteryChInfo.Marshal(b, m, deterministic)
}
func (dst *RecLotteryChInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecLotteryChInfo.Merge(dst, src)
}
func (m *RecLotteryChInfo) XXX_Size() int {
	return xxx_messageInfo_RecLotteryChInfo.Size(m)
}
func (m *RecLotteryChInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecLotteryChInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecLotteryChInfo proto.InternalMessageInfo

func (m *RecLotteryChInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecLotteryChInfo) GetLotteryEndTs() uint32 {
	if m != nil {
		return m.LotteryEndTs
	}
	return 0
}

func (m *RecLotteryChInfo) GetAwardCnt() uint32 {
	if m != nil {
		return m.AwardCnt
	}
	return 0
}

func (m *RecLotteryChInfo) GetGiftInfo() *GiftInfo {
	if m != nil {
		return m.GiftInfo
	}
	return nil
}

// 获取抽奖房间推荐列表
type GetRecLotteryChListReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecLotteryChListReq) Reset()         { *m = GetRecLotteryChListReq{} }
func (m *GetRecLotteryChListReq) String() string { return proto.CompactTextString(m) }
func (*GetRecLotteryChListReq) ProtoMessage()    {}
func (*GetRecLotteryChListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{35}
}
func (m *GetRecLotteryChListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecLotteryChListReq.Unmarshal(m, b)
}
func (m *GetRecLotteryChListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecLotteryChListReq.Marshal(b, m, deterministic)
}
func (dst *GetRecLotteryChListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecLotteryChListReq.Merge(dst, src)
}
func (m *GetRecLotteryChListReq) XXX_Size() int {
	return xxx_messageInfo_GetRecLotteryChListReq.Size(m)
}
func (m *GetRecLotteryChListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecLotteryChListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecLotteryChListReq proto.InternalMessageInfo

func (m *GetRecLotteryChListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetRecLotteryChListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetRecLotteryChListResp struct {
	ChList               []*RecLotteryChInfo `protobuf:"bytes,1,rep,name=ch_list,json=chList,proto3" json:"ch_list,omitempty"`
	NextPage             uint32              `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetRecLotteryChListResp) Reset()         { *m = GetRecLotteryChListResp{} }
func (m *GetRecLotteryChListResp) String() string { return proto.CompactTextString(m) }
func (*GetRecLotteryChListResp) ProtoMessage()    {}
func (*GetRecLotteryChListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{36}
}
func (m *GetRecLotteryChListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecLotteryChListResp.Unmarshal(m, b)
}
func (m *GetRecLotteryChListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecLotteryChListResp.Marshal(b, m, deterministic)
}
func (dst *GetRecLotteryChListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecLotteryChListResp.Merge(dst, src)
}
func (m *GetRecLotteryChListResp) XXX_Size() int {
	return xxx_messageInfo_GetRecLotteryChListResp.Size(m)
}
func (m *GetRecLotteryChListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecLotteryChListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecLotteryChListResp proto.InternalMessageInfo

func (m *GetRecLotteryChListResp) GetChList() []*RecLotteryChInfo {
	if m != nil {
		return m.ChList
	}
	return nil
}

func (m *GetRecLotteryChListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

// 根据id获取抽奖房间推荐信息
type GetLotteryChannelRecInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLotteryChannelRecInfoReq) Reset()         { *m = GetLotteryChannelRecInfoReq{} }
func (m *GetLotteryChannelRecInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetLotteryChannelRecInfoReq) ProtoMessage()    {}
func (*GetLotteryChannelRecInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{37}
}
func (m *GetLotteryChannelRecInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLotteryChannelRecInfoReq.Unmarshal(m, b)
}
func (m *GetLotteryChannelRecInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLotteryChannelRecInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetLotteryChannelRecInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLotteryChannelRecInfoReq.Merge(dst, src)
}
func (m *GetLotteryChannelRecInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetLotteryChannelRecInfoReq.Size(m)
}
func (m *GetLotteryChannelRecInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLotteryChannelRecInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLotteryChannelRecInfoReq proto.InternalMessageInfo

func (m *GetLotteryChannelRecInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetLotteryChannelRecInfoResp struct {
	IsRec                bool     `protobuf:"varint,1,opt,name=is_rec,json=isRec,proto3" json:"is_rec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLotteryChannelRecInfoResp) Reset()         { *m = GetLotteryChannelRecInfoResp{} }
func (m *GetLotteryChannelRecInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetLotteryChannelRecInfoResp) ProtoMessage()    {}
func (*GetLotteryChannelRecInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{38}
}
func (m *GetLotteryChannelRecInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLotteryChannelRecInfoResp.Unmarshal(m, b)
}
func (m *GetLotteryChannelRecInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLotteryChannelRecInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetLotteryChannelRecInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLotteryChannelRecInfoResp.Merge(dst, src)
}
func (m *GetLotteryChannelRecInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetLotteryChannelRecInfoResp.Size(m)
}
func (m *GetLotteryChannelRecInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLotteryChannelRecInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLotteryChannelRecInfoResp proto.InternalMessageInfo

func (m *GetLotteryChannelRecInfoResp) GetIsRec() bool {
	if m != nil {
		return m.IsRec
	}
	return false
}

type TimeSection struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeSection) Reset()         { *m = TimeSection{} }
func (m *TimeSection) String() string { return proto.CompactTextString(m) }
func (*TimeSection) ProtoMessage()    {}
func (*TimeSection) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{39}
}
func (m *TimeSection) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeSection.Unmarshal(m, b)
}
func (m *TimeSection) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeSection.Marshal(b, m, deterministic)
}
func (dst *TimeSection) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeSection.Merge(dst, src)
}
func (m *TimeSection) XXX_Size() int {
	return xxx_messageInfo_TimeSection.Size(m)
}
func (m *TimeSection) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeSection.DiscardUnknown(m)
}

var xxx_messageInfo_TimeSection proto.InternalMessageInfo

func (m *TimeSection) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TimeSection) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 推荐流房间信息
type ChannelRecommendSimpleInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Category             uint32   `protobuf:"varint,2,opt,name=category,proto3" json:"category,omitempty"`
	TagId                uint32   `protobuf:"varint,3,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	SubTag               string   `protobuf:"bytes,4,opt,name=sub_tag,json=subTag,proto3" json:"sub_tag,omitempty"`
	ChannelLevel         uint32   `protobuf:"varint,5,opt,name=channel_level,json=channelLevel,proto3" json:"channel_level,omitempty"`
	Score                uint32   `protobuf:"varint,6,opt,name=score,proto3" json:"score,omitempty"`
	TagName              string   `protobuf:"bytes,7,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	Label                string   `protobuf:"bytes,8,opt,name=label,proto3" json:"label,omitempty"`
	RoiUrl               string   `protobuf:"bytes,9,opt,name=roi_url,json=roiUrl,proto3" json:"roi_url,omitempty"`
	RelationShip         uint32   `protobuf:"varint,10,opt,name=relation_ship,json=relationShip,proto3" json:"relation_ship,omitempty"`
	RoiUrlType           uint32   `protobuf:"varint,11,opt,name=roi_url_type,json=roiUrlType,proto3" json:"roi_url_type,omitempty"`
	RecPoorSource        uint32   `protobuf:"varint,12,opt,name=rec_poor_source,json=recPoorSource,proto3" json:"rec_poor_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRecommendSimpleInfo) Reset()         { *m = ChannelRecommendSimpleInfo{} }
func (m *ChannelRecommendSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelRecommendSimpleInfo) ProtoMessage()    {}
func (*ChannelRecommendSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{40}
}
func (m *ChannelRecommendSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRecommendSimpleInfo.Unmarshal(m, b)
}
func (m *ChannelRecommendSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRecommendSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelRecommendSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRecommendSimpleInfo.Merge(dst, src)
}
func (m *ChannelRecommendSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelRecommendSimpleInfo.Size(m)
}
func (m *ChannelRecommendSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRecommendSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRecommendSimpleInfo proto.InternalMessageInfo

func (m *ChannelRecommendSimpleInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetSubTag() string {
	if m != nil {
		return m.SubTag
	}
	return ""
}

func (m *ChannelRecommendSimpleInfo) GetChannelLevel() uint32 {
	if m != nil {
		return m.ChannelLevel
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *ChannelRecommendSimpleInfo) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *ChannelRecommendSimpleInfo) GetRoiUrl() string {
	if m != nil {
		return m.RoiUrl
	}
	return ""
}

func (m *ChannelRecommendSimpleInfo) GetRelationShip() uint32 {
	if m != nil {
		return m.RelationShip
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetRoiUrlType() uint32 {
	if m != nil {
		return m.RoiUrlType
	}
	return 0
}

func (m *ChannelRecommendSimpleInfo) GetRecPoorSource() uint32 {
	if m != nil {
		return m.RecPoorSource
	}
	return 0
}

type GetRecommendChannelReq struct {
	UserCategory         uint32   `protobuf:"varint,1,opt,name=user_category,json=userCategory,proto3" json:"user_category,omitempty"`
	Start                uint32   `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	HomeType             uint32   `protobuf:"varint,4,opt,name=home_type,json=homeType,proto3" json:"home_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             string   `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendChannelReq) Reset()         { *m = GetRecommendChannelReq{} }
func (m *GetRecommendChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelReq) ProtoMessage()    {}
func (*GetRecommendChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{41}
}
func (m *GetRecommendChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelReq.Unmarshal(m, b)
}
func (m *GetRecommendChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelReq.Merge(dst, src)
}
func (m *GetRecommendChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelReq.Size(m)
}
func (m *GetRecommendChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelReq proto.InternalMessageInfo

func (m *GetRecommendChannelReq) GetUserCategory() uint32 {
	if m != nil {
		return m.UserCategory
	}
	return 0
}

func (m *GetRecommendChannelReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetRecommendChannelReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetRecommendChannelReq) GetHomeType() uint32 {
	if m != nil {
		return m.HomeType
	}
	return 0
}

func (m *GetRecommendChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendChannelReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetRecommendChannelResp struct {
	ChannelList          []*ChannelRecommendSimpleInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	IsEnd                bool                          `protobuf:"varint,2,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetRecommendChannelResp) Reset()         { *m = GetRecommendChannelResp{} }
func (m *GetRecommendChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelResp) ProtoMessage()    {}
func (*GetRecommendChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{42}
}
func (m *GetRecommendChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelResp.Unmarshal(m, b)
}
func (m *GetRecommendChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelResp.Merge(dst, src)
}
func (m *GetRecommendChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelResp.Size(m)
}
func (m *GetRecommendChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelResp proto.InternalMessageInfo

func (m *GetRecommendChannelResp) GetChannelList() []*ChannelRecommendSimpleInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetRecommendChannelResp) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

type GetRecommendChannelV2Req struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserCategory         uint32   `protobuf:"varint,2,opt,name=user_category,json=userCategory,proto3" json:"user_category,omitempty"`
	Start                uint32   `protobuf:"varint,3,opt,name=start,proto3" json:"start,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	DeviceId             string   `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Sex                  int32    `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendChannelV2Req) Reset()         { *m = GetRecommendChannelV2Req{} }
func (m *GetRecommendChannelV2Req) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelV2Req) ProtoMessage()    {}
func (*GetRecommendChannelV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{43}
}
func (m *GetRecommendChannelV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelV2Req.Unmarshal(m, b)
}
func (m *GetRecommendChannelV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelV2Req.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelV2Req.Merge(dst, src)
}
func (m *GetRecommendChannelV2Req) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelV2Req.Size(m)
}
func (m *GetRecommendChannelV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelV2Req proto.InternalMessageInfo

func (m *GetRecommendChannelV2Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendChannelV2Req) GetUserCategory() uint32 {
	if m != nil {
		return m.UserCategory
	}
	return 0
}

func (m *GetRecommendChannelV2Req) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetRecommendChannelV2Req) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetRecommendChannelV2Req) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetRecommendChannelV2Req) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type GetRecommendChannelV2Resp struct {
	ChannelList          []*ChannelRecommendSimpleInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	IsEnd                bool                          `protobuf:"varint,2,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetRecommendChannelV2Resp) Reset()         { *m = GetRecommendChannelV2Resp{} }
func (m *GetRecommendChannelV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelV2Resp) ProtoMessage()    {}
func (*GetRecommendChannelV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{44}
}
func (m *GetRecommendChannelV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelV2Resp.Unmarshal(m, b)
}
func (m *GetRecommendChannelV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelV2Resp.Merge(dst, src)
}
func (m *GetRecommendChannelV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelV2Resp.Size(m)
}
func (m *GetRecommendChannelV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelV2Resp proto.InternalMessageInfo

func (m *GetRecommendChannelV2Resp) GetChannelList() []*ChannelRecommendSimpleInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetRecommendChannelV2Resp) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

// 推荐库信息
type PrepareChannelInfo struct {
	ChannelId            uint32       `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TagId                uint32       `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	NewLevel             uint32       `protobuf:"varint,3,opt,name=new_level,json=newLevel,proto3" json:"new_level,omitempty"`
	NewSection           *TimeSection `protobuf:"bytes,4,opt,name=new_section,json=newSection,proto3" json:"new_section,omitempty"`
	OldLevel             uint32       `protobuf:"varint,5,opt,name=old_level,json=oldLevel,proto3" json:"old_level,omitempty"`
	OldSection           *TimeSection `protobuf:"bytes,6,opt,name=old_section,json=oldSection,proto3" json:"old_section,omitempty"`
	QuickLevel           uint32       `protobuf:"varint,7,opt,name=quick_level,json=quickLevel,proto3" json:"quick_level,omitempty"`
	QuickSection         *TimeSection `protobuf:"bytes,8,opt,name=quick_section,json=quickSection,proto3" json:"quick_section,omitempty"`
	UpdateTs             uint32       `protobuf:"varint,9,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	PrepareType          uint32       `protobuf:"varint,10,opt,name=prepare_type,json=prepareType,proto3" json:"prepare_type,omitempty"`
	Id                   uint32       `protobuf:"varint,11,opt,name=id,proto3" json:"id,omitempty"`
	Operator             string       `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PrepareChannelInfo) Reset()         { *m = PrepareChannelInfo{} }
func (m *PrepareChannelInfo) String() string { return proto.CompactTextString(m) }
func (*PrepareChannelInfo) ProtoMessage()    {}
func (*PrepareChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{45}
}
func (m *PrepareChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrepareChannelInfo.Unmarshal(m, b)
}
func (m *PrepareChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrepareChannelInfo.Marshal(b, m, deterministic)
}
func (dst *PrepareChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrepareChannelInfo.Merge(dst, src)
}
func (m *PrepareChannelInfo) XXX_Size() int {
	return xxx_messageInfo_PrepareChannelInfo.Size(m)
}
func (m *PrepareChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PrepareChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PrepareChannelInfo proto.InternalMessageInfo

func (m *PrepareChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PrepareChannelInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *PrepareChannelInfo) GetNewLevel() uint32 {
	if m != nil {
		return m.NewLevel
	}
	return 0
}

func (m *PrepareChannelInfo) GetNewSection() *TimeSection {
	if m != nil {
		return m.NewSection
	}
	return nil
}

func (m *PrepareChannelInfo) GetOldLevel() uint32 {
	if m != nil {
		return m.OldLevel
	}
	return 0
}

func (m *PrepareChannelInfo) GetOldSection() *TimeSection {
	if m != nil {
		return m.OldSection
	}
	return nil
}

func (m *PrepareChannelInfo) GetQuickLevel() uint32 {
	if m != nil {
		return m.QuickLevel
	}
	return 0
}

func (m *PrepareChannelInfo) GetQuickSection() *TimeSection {
	if m != nil {
		return m.QuickSection
	}
	return nil
}

func (m *PrepareChannelInfo) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *PrepareChannelInfo) GetPrepareType() uint32 {
	if m != nil {
		return m.PrepareType
	}
	return 0
}

func (m *PrepareChannelInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PrepareChannelInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 获取推荐库列表
type GetPrepareChannelListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PrepareType          uint32   `protobuf:"varint,2,opt,name=prepare_type,json=prepareType,proto3" json:"prepare_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareChannelListReq) Reset()         { *m = GetPrepareChannelListReq{} }
func (m *GetPrepareChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetPrepareChannelListReq) ProtoMessage()    {}
func (*GetPrepareChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{46}
}
func (m *GetPrepareChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareChannelListReq.Unmarshal(m, b)
}
func (m *GetPrepareChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetPrepareChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareChannelListReq.Merge(dst, src)
}
func (m *GetPrepareChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetPrepareChannelListReq.Size(m)
}
func (m *GetPrepareChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareChannelListReq proto.InternalMessageInfo

func (m *GetPrepareChannelListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPrepareChannelListReq) GetPrepareType() uint32 {
	if m != nil {
		return m.PrepareType
	}
	return 0
}

type GetPrepareChannelListResp struct {
	PrepareChannelList   []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=prepare_channel_list,json=prepareChannelList,proto3" json:"prepare_channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPrepareChannelListResp) Reset()         { *m = GetPrepareChannelListResp{} }
func (m *GetPrepareChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetPrepareChannelListResp) ProtoMessage()    {}
func (*GetPrepareChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{47}
}
func (m *GetPrepareChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareChannelListResp.Unmarshal(m, b)
}
func (m *GetPrepareChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetPrepareChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareChannelListResp.Merge(dst, src)
}
func (m *GetPrepareChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetPrepareChannelListResp.Size(m)
}
func (m *GetPrepareChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareChannelListResp proto.InternalMessageInfo

func (m *GetPrepareChannelListResp) GetPrepareChannelList() []*PrepareChannelInfo {
	if m != nil {
		return m.PrepareChannelList
	}
	return nil
}

// 设置推荐库房间信息
type SetPrepareChannelReq struct {
	InfoList             []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Operator             string                `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SetPrepareChannelReq) Reset()         { *m = SetPrepareChannelReq{} }
func (m *SetPrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*SetPrepareChannelReq) ProtoMessage()    {}
func (*SetPrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{48}
}
func (m *SetPrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPrepareChannelReq.Unmarshal(m, b)
}
func (m *SetPrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *SetPrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPrepareChannelReq.Merge(dst, src)
}
func (m *SetPrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_SetPrepareChannelReq.Size(m)
}
func (m *SetPrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPrepareChannelReq proto.InternalMessageInfo

func (m *SetPrepareChannelReq) GetInfoList() []*PrepareChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *SetPrepareChannelReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SetPrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPrepareChannelResp) Reset()         { *m = SetPrepareChannelResp{} }
func (m *SetPrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*SetPrepareChannelResp) ProtoMessage()    {}
func (*SetPrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{49}
}
func (m *SetPrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPrepareChannelResp.Unmarshal(m, b)
}
func (m *SetPrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *SetPrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPrepareChannelResp.Merge(dst, src)
}
func (m *SetPrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_SetPrepareChannelResp.Size(m)
}
func (m *SetPrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPrepareChannelResp proto.InternalMessageInfo

// 删除推荐库房间信息
type DelPrepareChannelReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Operator             string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPrepareChannelReq) Reset()         { *m = DelPrepareChannelReq{} }
func (m *DelPrepareChannelReq) String() string { return proto.CompactTextString(m) }
func (*DelPrepareChannelReq) ProtoMessage()    {}
func (*DelPrepareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{50}
}
func (m *DelPrepareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPrepareChannelReq.Unmarshal(m, b)
}
func (m *DelPrepareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPrepareChannelReq.Marshal(b, m, deterministic)
}
func (dst *DelPrepareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPrepareChannelReq.Merge(dst, src)
}
func (m *DelPrepareChannelReq) XXX_Size() int {
	return xxx_messageInfo_DelPrepareChannelReq.Size(m)
}
func (m *DelPrepareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPrepareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPrepareChannelReq proto.InternalMessageInfo

func (m *DelPrepareChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DelPrepareChannelReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type DelPrepareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPrepareChannelResp) Reset()         { *m = DelPrepareChannelResp{} }
func (m *DelPrepareChannelResp) String() string { return proto.CompactTextString(m) }
func (*DelPrepareChannelResp) ProtoMessage()    {}
func (*DelPrepareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{51}
}
func (m *DelPrepareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPrepareChannelResp.Unmarshal(m, b)
}
func (m *DelPrepareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPrepareChannelResp.Marshal(b, m, deterministic)
}
func (dst *DelPrepareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPrepareChannelResp.Merge(dst, src)
}
func (m *DelPrepareChannelResp) XXX_Size() int {
	return xxx_messageInfo_DelPrepareChannelResp.Size(m)
}
func (m *DelPrepareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPrepareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPrepareChannelResp proto.InternalMessageInfo

// 获取推荐库备份列表
type GetPrepareBackupListReq struct {
	Version              int64    `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareBackupListReq) Reset()         { *m = GetPrepareBackupListReq{} }
func (m *GetPrepareBackupListReq) String() string { return proto.CompactTextString(m) }
func (*GetPrepareBackupListReq) ProtoMessage()    {}
func (*GetPrepareBackupListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{52}
}
func (m *GetPrepareBackupListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareBackupListReq.Unmarshal(m, b)
}
func (m *GetPrepareBackupListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareBackupListReq.Marshal(b, m, deterministic)
}
func (dst *GetPrepareBackupListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareBackupListReq.Merge(dst, src)
}
func (m *GetPrepareBackupListReq) XXX_Size() int {
	return xxx_messageInfo_GetPrepareBackupListReq.Size(m)
}
func (m *GetPrepareBackupListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareBackupListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareBackupListReq proto.InternalMessageInfo

func (m *GetPrepareBackupListReq) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

type GetPrepareBackupListResp struct {
	InfoList             []*PrepareChannelInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPrepareBackupListResp) Reset()         { *m = GetPrepareBackupListResp{} }
func (m *GetPrepareBackupListResp) String() string { return proto.CompactTextString(m) }
func (*GetPrepareBackupListResp) ProtoMessage()    {}
func (*GetPrepareBackupListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{53}
}
func (m *GetPrepareBackupListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareBackupListResp.Unmarshal(m, b)
}
func (m *GetPrepareBackupListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareBackupListResp.Marshal(b, m, deterministic)
}
func (dst *GetPrepareBackupListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareBackupListResp.Merge(dst, src)
}
func (m *GetPrepareBackupListResp) XXX_Size() int {
	return xxx_messageInfo_GetPrepareBackupListResp.Size(m)
}
func (m *GetPrepareBackupListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareBackupListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareBackupListResp proto.InternalMessageInfo

func (m *GetPrepareBackupListResp) GetInfoList() []*PrepareChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 房间推荐信息
type PrepareOperRecord struct {
	OperType             uint32              `protobuf:"varint,1,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`
	Info                 *PrepareChannelInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PrepareOperRecord) Reset()         { *m = PrepareOperRecord{} }
func (m *PrepareOperRecord) String() string { return proto.CompactTextString(m) }
func (*PrepareOperRecord) ProtoMessage()    {}
func (*PrepareOperRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{54}
}
func (m *PrepareOperRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrepareOperRecord.Unmarshal(m, b)
}
func (m *PrepareOperRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrepareOperRecord.Marshal(b, m, deterministic)
}
func (dst *PrepareOperRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrepareOperRecord.Merge(dst, src)
}
func (m *PrepareOperRecord) XXX_Size() int {
	return xxx_messageInfo_PrepareOperRecord.Size(m)
}
func (m *PrepareOperRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_PrepareOperRecord.DiscardUnknown(m)
}

var xxx_messageInfo_PrepareOperRecord proto.InternalMessageInfo

func (m *PrepareOperRecord) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *PrepareOperRecord) GetInfo() *PrepareChannelInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 获取操作记录列表
type GetPrepareOperRecordListReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepareOperRecordListReq) Reset()         { *m = GetPrepareOperRecordListReq{} }
func (m *GetPrepareOperRecordListReq) String() string { return proto.CompactTextString(m) }
func (*GetPrepareOperRecordListReq) ProtoMessage()    {}
func (*GetPrepareOperRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{55}
}
func (m *GetPrepareOperRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareOperRecordListReq.Unmarshal(m, b)
}
func (m *GetPrepareOperRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareOperRecordListReq.Marshal(b, m, deterministic)
}
func (dst *GetPrepareOperRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareOperRecordListReq.Merge(dst, src)
}
func (m *GetPrepareOperRecordListReq) XXX_Size() int {
	return xxx_messageInfo_GetPrepareOperRecordListReq.Size(m)
}
func (m *GetPrepareOperRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareOperRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareOperRecordListReq proto.InternalMessageInfo

func (m *GetPrepareOperRecordListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPrepareOperRecordListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetPrepareOperRecordListResp struct {
	RecordList           []*PrepareOperRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetPrepareOperRecordListResp) Reset()         { *m = GetPrepareOperRecordListResp{} }
func (m *GetPrepareOperRecordListResp) String() string { return proto.CompactTextString(m) }
func (*GetPrepareOperRecordListResp) ProtoMessage()    {}
func (*GetPrepareOperRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{56}
}
func (m *GetPrepareOperRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepareOperRecordListResp.Unmarshal(m, b)
}
func (m *GetPrepareOperRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepareOperRecordListResp.Marshal(b, m, deterministic)
}
func (dst *GetPrepareOperRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepareOperRecordListResp.Merge(dst, src)
}
func (m *GetPrepareOperRecordListResp) XXX_Size() int {
	return xxx_messageInfo_GetPrepareOperRecordListResp.Size(m)
}
func (m *GetPrepareOperRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepareOperRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepareOperRecordListResp proto.InternalMessageInfo

func (m *GetPrepareOperRecordListResp) GetRecordList() []*PrepareOperRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetPrepareOperRecordListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 快速进房豆腐块配置
type DisplaySceneInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	SceneId              uint32   `protobuf:"varint,4,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	MarketIdList         []uint32 `protobuf:"varint,5,rep,packed,name=market_id_list,json=marketIdList,proto3" json:"market_id_list,omitempty"`
	Platform             uint32   `protobuf:"varint,6,opt,name=platform,proto3" json:"platform,omitempty"`
	MinVersion           string   `protobuf:"bytes,7,opt,name=min_version,json=minVersion,proto3" json:"min_version,omitempty"`
	BeginTs              uint32   `protobuf:"varint,8,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,9,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	SceneType            uint32   `protobuf:"varint,10,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	TagId                uint32   `protobuf:"varint,11,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	JumpUrl              string   `protobuf:"bytes,12,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	Weight               uint32   `protobuf:"varint,13,opt,name=weight,proto3" json:"weight,omitempty"`
	BgImg                string   `protobuf:"bytes,14,opt,name=bg_img,json=bgImg,proto3" json:"bg_img,omitempty"`
	SmallImg             string   `protobuf:"bytes,15,opt,name=small_img,json=smallImg,proto3" json:"small_img,omitempty"`
	StaticImg            string   `protobuf:"bytes,16,opt,name=static_img,json=staticImg,proto3" json:"static_img,omitempty"`
	DynamicUrl           string   `protobuf:"bytes,17,opt,name=dynamic_url,json=dynamicUrl,proto3" json:"dynamic_url,omitempty"`
	DynamicMd5           string   `protobuf:"bytes,18,opt,name=dynamic_md5,json=dynamicMd5,proto3" json:"dynamic_md5,omitempty"`
	DynamicJsonKey       string   `protobuf:"bytes,19,opt,name=dynamic_json_key,json=dynamicJsonKey,proto3" json:"dynamic_json_key,omitempty"`
	Operator             string   `protobuf:"bytes,20,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdateTs             uint32   `protobuf:"varint,21,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Status               uint32   `protobuf:"varint,22,opt,name=status,proto3" json:"status,omitempty"`
	TimeType             uint32   `protobuf:"varint,23,opt,name=time_type,json=timeType,proto3" json:"time_type,omitempty"`
	UMinVersion          uint32   `protobuf:"varint,24,opt,name=u_min_version,json=uMinVersion,proto3" json:"u_min_version,omitempty"`
	SubTitles            []string `protobuf:"bytes,25,rep,name=sub_titles,json=subTitles,proto3" json:"sub_titles,omitempty"`
	DynamicType          uint32   `protobuf:"varint,26,opt,name=dynamic_type,json=dynamicType,proto3" json:"dynamic_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DisplaySceneInfo) Reset()         { *m = DisplaySceneInfo{} }
func (m *DisplaySceneInfo) String() string { return proto.CompactTextString(m) }
func (*DisplaySceneInfo) ProtoMessage()    {}
func (*DisplaySceneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{57}
}
func (m *DisplaySceneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DisplaySceneInfo.Unmarshal(m, b)
}
func (m *DisplaySceneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DisplaySceneInfo.Marshal(b, m, deterministic)
}
func (dst *DisplaySceneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisplaySceneInfo.Merge(dst, src)
}
func (m *DisplaySceneInfo) XXX_Size() int {
	return xxx_messageInfo_DisplaySceneInfo.Size(m)
}
func (m *DisplaySceneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DisplaySceneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DisplaySceneInfo proto.InternalMessageInfo

func (m *DisplaySceneInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DisplaySceneInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *DisplaySceneInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *DisplaySceneInfo) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *DisplaySceneInfo) GetMarketIdList() []uint32 {
	if m != nil {
		return m.MarketIdList
	}
	return nil
}

func (m *DisplaySceneInfo) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *DisplaySceneInfo) GetMinVersion() string {
	if m != nil {
		return m.MinVersion
	}
	return ""
}

func (m *DisplaySceneInfo) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *DisplaySceneInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *DisplaySceneInfo) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *DisplaySceneInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *DisplaySceneInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *DisplaySceneInfo) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *DisplaySceneInfo) GetBgImg() string {
	if m != nil {
		return m.BgImg
	}
	return ""
}

func (m *DisplaySceneInfo) GetSmallImg() string {
	if m != nil {
		return m.SmallImg
	}
	return ""
}

func (m *DisplaySceneInfo) GetStaticImg() string {
	if m != nil {
		return m.StaticImg
	}
	return ""
}

func (m *DisplaySceneInfo) GetDynamicUrl() string {
	if m != nil {
		return m.DynamicUrl
	}
	return ""
}

func (m *DisplaySceneInfo) GetDynamicMd5() string {
	if m != nil {
		return m.DynamicMd5
	}
	return ""
}

func (m *DisplaySceneInfo) GetDynamicJsonKey() string {
	if m != nil {
		return m.DynamicJsonKey
	}
	return ""
}

func (m *DisplaySceneInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *DisplaySceneInfo) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *DisplaySceneInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DisplaySceneInfo) GetTimeType() uint32 {
	if m != nil {
		return m.TimeType
	}
	return 0
}

func (m *DisplaySceneInfo) GetUMinVersion() uint32 {
	if m != nil {
		return m.UMinVersion
	}
	return 0
}

func (m *DisplaySceneInfo) GetSubTitles() []string {
	if m != nil {
		return m.SubTitles
	}
	return nil
}

func (m *DisplaySceneInfo) GetDynamicType() uint32 {
	if m != nil {
		return m.DynamicType
	}
	return 0
}

type QuickEntryConfig struct {
	SceneInfoList        []*DisplaySceneInfo `protobuf:"bytes,1,rep,name=scene_info_list,json=sceneInfoList,proto3" json:"scene_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *QuickEntryConfig) Reset()         { *m = QuickEntryConfig{} }
func (m *QuickEntryConfig) String() string { return proto.CompactTextString(m) }
func (*QuickEntryConfig) ProtoMessage()    {}
func (*QuickEntryConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{58}
}
func (m *QuickEntryConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickEntryConfig.Unmarshal(m, b)
}
func (m *QuickEntryConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickEntryConfig.Marshal(b, m, deterministic)
}
func (dst *QuickEntryConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickEntryConfig.Merge(dst, src)
}
func (m *QuickEntryConfig) XXX_Size() int {
	return xxx_messageInfo_QuickEntryConfig.Size(m)
}
func (m *QuickEntryConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickEntryConfig.DiscardUnknown(m)
}

var xxx_messageInfo_QuickEntryConfig proto.InternalMessageInfo

func (m *QuickEntryConfig) GetSceneInfoList() []*DisplaySceneInfo {
	if m != nil {
		return m.SceneInfoList
	}
	return nil
}

// 查询列表
type GetDisplaySceneInfoListReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	SceneId              uint32   `protobuf:"varint,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	BeginTs              uint32   `protobuf:"varint,5,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Page                 uint32   `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDisplaySceneInfoListReq) Reset()         { *m = GetDisplaySceneInfoListReq{} }
func (m *GetDisplaySceneInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetDisplaySceneInfoListReq) ProtoMessage()    {}
func (*GetDisplaySceneInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{59}
}
func (m *GetDisplaySceneInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDisplaySceneInfoListReq.Unmarshal(m, b)
}
func (m *GetDisplaySceneInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDisplaySceneInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetDisplaySceneInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDisplaySceneInfoListReq.Merge(dst, src)
}
func (m *GetDisplaySceneInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetDisplaySceneInfoListReq.Size(m)
}
func (m *GetDisplaySceneInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDisplaySceneInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDisplaySceneInfoListReq proto.InternalMessageInfo

func (m *GetDisplaySceneInfoListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetSceneId() uint32 {
	if m != nil {
		return m.SceneId
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetDisplaySceneInfoListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetDisplaySceneInfoListResp struct {
	InfoList             []*DisplaySceneInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	TotalCnt             uint32              `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetDisplaySceneInfoListResp) Reset()         { *m = GetDisplaySceneInfoListResp{} }
func (m *GetDisplaySceneInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetDisplaySceneInfoListResp) ProtoMessage()    {}
func (*GetDisplaySceneInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{60}
}
func (m *GetDisplaySceneInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDisplaySceneInfoListResp.Unmarshal(m, b)
}
func (m *GetDisplaySceneInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDisplaySceneInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetDisplaySceneInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDisplaySceneInfoListResp.Merge(dst, src)
}
func (m *GetDisplaySceneInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetDisplaySceneInfoListResp.Size(m)
}
func (m *GetDisplaySceneInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDisplaySceneInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDisplaySceneInfoListResp proto.InternalMessageInfo

func (m *GetDisplaySceneInfoListResp) GetInfoList() []*DisplaySceneInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetDisplaySceneInfoListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 增加配置
type AddDisplaySceneInfoReq struct {
	Info                 *DisplaySceneInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddDisplaySceneInfoReq) Reset()         { *m = AddDisplaySceneInfoReq{} }
func (m *AddDisplaySceneInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddDisplaySceneInfoReq) ProtoMessage()    {}
func (*AddDisplaySceneInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{61}
}
func (m *AddDisplaySceneInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDisplaySceneInfoReq.Unmarshal(m, b)
}
func (m *AddDisplaySceneInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDisplaySceneInfoReq.Marshal(b, m, deterministic)
}
func (dst *AddDisplaySceneInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDisplaySceneInfoReq.Merge(dst, src)
}
func (m *AddDisplaySceneInfoReq) XXX_Size() int {
	return xxx_messageInfo_AddDisplaySceneInfoReq.Size(m)
}
func (m *AddDisplaySceneInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDisplaySceneInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddDisplaySceneInfoReq proto.InternalMessageInfo

func (m *AddDisplaySceneInfoReq) GetInfo() *DisplaySceneInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AddDisplaySceneInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddDisplaySceneInfoResp) Reset()         { *m = AddDisplaySceneInfoResp{} }
func (m *AddDisplaySceneInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddDisplaySceneInfoResp) ProtoMessage()    {}
func (*AddDisplaySceneInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{62}
}
func (m *AddDisplaySceneInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddDisplaySceneInfoResp.Unmarshal(m, b)
}
func (m *AddDisplaySceneInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddDisplaySceneInfoResp.Marshal(b, m, deterministic)
}
func (dst *AddDisplaySceneInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddDisplaySceneInfoResp.Merge(dst, src)
}
func (m *AddDisplaySceneInfoResp) XXX_Size() int {
	return xxx_messageInfo_AddDisplaySceneInfoResp.Size(m)
}
func (m *AddDisplaySceneInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddDisplaySceneInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddDisplaySceneInfoResp proto.InternalMessageInfo

// 删除配置
type DelDisplaySceneInfoReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDisplaySceneInfoReq) Reset()         { *m = DelDisplaySceneInfoReq{} }
func (m *DelDisplaySceneInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelDisplaySceneInfoReq) ProtoMessage()    {}
func (*DelDisplaySceneInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{63}
}
func (m *DelDisplaySceneInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDisplaySceneInfoReq.Unmarshal(m, b)
}
func (m *DelDisplaySceneInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDisplaySceneInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelDisplaySceneInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDisplaySceneInfoReq.Merge(dst, src)
}
func (m *DelDisplaySceneInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelDisplaySceneInfoReq.Size(m)
}
func (m *DelDisplaySceneInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDisplaySceneInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelDisplaySceneInfoReq proto.InternalMessageInfo

func (m *DelDisplaySceneInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelDisplaySceneInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelDisplaySceneInfoResp) Reset()         { *m = DelDisplaySceneInfoResp{} }
func (m *DelDisplaySceneInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelDisplaySceneInfoResp) ProtoMessage()    {}
func (*DelDisplaySceneInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{64}
}
func (m *DelDisplaySceneInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelDisplaySceneInfoResp.Unmarshal(m, b)
}
func (m *DelDisplaySceneInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelDisplaySceneInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelDisplaySceneInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelDisplaySceneInfoResp.Merge(dst, src)
}
func (m *DelDisplaySceneInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelDisplaySceneInfoResp.Size(m)
}
func (m *DelDisplaySceneInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelDisplaySceneInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelDisplaySceneInfoResp proto.InternalMessageInfo

type GetQuickEntryConfigListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickEntryConfigListReq) Reset()         { *m = GetQuickEntryConfigListReq{} }
func (m *GetQuickEntryConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickEntryConfigListReq) ProtoMessage()    {}
func (*GetQuickEntryConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{65}
}
func (m *GetQuickEntryConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickEntryConfigListReq.Unmarshal(m, b)
}
func (m *GetQuickEntryConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickEntryConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetQuickEntryConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickEntryConfigListReq.Merge(dst, src)
}
func (m *GetQuickEntryConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetQuickEntryConfigListReq.Size(m)
}
func (m *GetQuickEntryConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickEntryConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickEntryConfigListReq proto.InternalMessageInfo

type GetQuickEntryConfigListResp struct {
	MapTypeConf          map[uint32]*QuickEntryConfig `protobuf:"bytes,1,rep,name=map_type_conf,json=mapTypeConf,proto3" json:"map_type_conf,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetQuickEntryConfigListResp) Reset()         { *m = GetQuickEntryConfigListResp{} }
func (m *GetQuickEntryConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickEntryConfigListResp) ProtoMessage()    {}
func (*GetQuickEntryConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{66}
}
func (m *GetQuickEntryConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickEntryConfigListResp.Unmarshal(m, b)
}
func (m *GetQuickEntryConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickEntryConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetQuickEntryConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickEntryConfigListResp.Merge(dst, src)
}
func (m *GetQuickEntryConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetQuickEntryConfigListResp.Size(m)
}
func (m *GetQuickEntryConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickEntryConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickEntryConfigListResp proto.InternalMessageInfo

func (m *GetQuickEntryConfigListResp) GetMapTypeConf() map[uint32]*QuickEntryConfig {
	if m != nil {
		return m.MapTypeConf
	}
	return nil
}

// 批量获取直播间的歌手标签
type BatGetChannelSoundLabelReq struct {
	CidList              []uint32 `protobuf:"varint,1,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetChannelSoundLabelReq) Reset()         { *m = BatGetChannelSoundLabelReq{} }
func (m *BatGetChannelSoundLabelReq) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelSoundLabelReq) ProtoMessage()    {}
func (*BatGetChannelSoundLabelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{67}
}
func (m *BatGetChannelSoundLabelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetChannelSoundLabelReq.Unmarshal(m, b)
}
func (m *BatGetChannelSoundLabelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetChannelSoundLabelReq.Marshal(b, m, deterministic)
}
func (dst *BatGetChannelSoundLabelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetChannelSoundLabelReq.Merge(dst, src)
}
func (m *BatGetChannelSoundLabelReq) XXX_Size() int {
	return xxx_messageInfo_BatGetChannelSoundLabelReq.Size(m)
}
func (m *BatGetChannelSoundLabelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetChannelSoundLabelReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetChannelSoundLabelReq proto.InternalMessageInfo

func (m *BatGetChannelSoundLabelReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

type BatGetChannelSoundLabelResp struct {
	MapCidLabel          map[uint32]string `protobuf:"bytes,1,rep,name=map_cid_label,json=mapCidLabel,proto3" json:"map_cid_label,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetChannelSoundLabelResp) Reset()         { *m = BatGetChannelSoundLabelResp{} }
func (m *BatGetChannelSoundLabelResp) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelSoundLabelResp) ProtoMessage()    {}
func (*BatGetChannelSoundLabelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{68}
}
func (m *BatGetChannelSoundLabelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetChannelSoundLabelResp.Unmarshal(m, b)
}
func (m *BatGetChannelSoundLabelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetChannelSoundLabelResp.Marshal(b, m, deterministic)
}
func (dst *BatGetChannelSoundLabelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetChannelSoundLabelResp.Merge(dst, src)
}
func (m *BatGetChannelSoundLabelResp) XXX_Size() int {
	return xxx_messageInfo_BatGetChannelSoundLabelResp.Size(m)
}
func (m *BatGetChannelSoundLabelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetChannelSoundLabelResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetChannelSoundLabelResp proto.InternalMessageInfo

func (m *BatGetChannelSoundLabelResp) GetMapCidLabel() map[uint32]string {
	if m != nil {
		return m.MapCidLabel
	}
	return nil
}

// 触发定时任务
type TriggerTimerReq struct {
	TimerType            TriggerTimerReq_TimerType `protobuf:"varint,1,opt,name=timer_type,json=timerType,proto3,enum=channelrecommend.TriggerTimerReq_TimerType" json:"timer_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *TriggerTimerReq) Reset()         { *m = TriggerTimerReq{} }
func (m *TriggerTimerReq) String() string { return proto.CompactTextString(m) }
func (*TriggerTimerReq) ProtoMessage()    {}
func (*TriggerTimerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{69}
}
func (m *TriggerTimerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerTimerReq.Unmarshal(m, b)
}
func (m *TriggerTimerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerTimerReq.Marshal(b, m, deterministic)
}
func (dst *TriggerTimerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerTimerReq.Merge(dst, src)
}
func (m *TriggerTimerReq) XXX_Size() int {
	return xxx_messageInfo_TriggerTimerReq.Size(m)
}
func (m *TriggerTimerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerTimerReq.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerTimerReq proto.InternalMessageInfo

func (m *TriggerTimerReq) GetTimerType() TriggerTimerReq_TimerType {
	if m != nil {
		return m.TimerType
	}
	return TriggerTimerReq_Timer_Type_Invalid
}

type TriggerTimerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TriggerTimerResp) Reset()         { *m = TriggerTimerResp{} }
func (m *TriggerTimerResp) String() string { return proto.CompactTextString(m) }
func (*TriggerTimerResp) ProtoMessage()    {}
func (*TriggerTimerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{70}
}
func (m *TriggerTimerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerTimerResp.Unmarshal(m, b)
}
func (m *TriggerTimerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerTimerResp.Marshal(b, m, deterministic)
}
func (dst *TriggerTimerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerTimerResp.Merge(dst, src)
}
func (m *TriggerTimerResp) XXX_Size() int {
	return xxx_messageInfo_TriggerTimerResp.Size(m)
}
func (m *TriggerTimerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerTimerResp.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerTimerResp proto.InternalMessageInfo

// 根据短链中的TagId获取快速进度推荐房间id
type GetQuickRecChannelByTagIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TagId                uint32   `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickRecChannelByTagIdReq) Reset()         { *m = GetQuickRecChannelByTagIdReq{} }
func (m *GetQuickRecChannelByTagIdReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickRecChannelByTagIdReq) ProtoMessage()    {}
func (*GetQuickRecChannelByTagIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{71}
}
func (m *GetQuickRecChannelByTagIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickRecChannelByTagIdReq.Unmarshal(m, b)
}
func (m *GetQuickRecChannelByTagIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickRecChannelByTagIdReq.Marshal(b, m, deterministic)
}
func (dst *GetQuickRecChannelByTagIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickRecChannelByTagIdReq.Merge(dst, src)
}
func (m *GetQuickRecChannelByTagIdReq) XXX_Size() int {
	return xxx_messageInfo_GetQuickRecChannelByTagIdReq.Size(m)
}
func (m *GetQuickRecChannelByTagIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickRecChannelByTagIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickRecChannelByTagIdReq proto.InternalMessageInfo

func (m *GetQuickRecChannelByTagIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetQuickRecChannelByTagIdReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type GetQuickRecChannelByTagIdResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickRecChannelByTagIdResp) Reset()         { *m = GetQuickRecChannelByTagIdResp{} }
func (m *GetQuickRecChannelByTagIdResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickRecChannelByTagIdResp) ProtoMessage()    {}
func (*GetQuickRecChannelByTagIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{72}
}
func (m *GetQuickRecChannelByTagIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickRecChannelByTagIdResp.Unmarshal(m, b)
}
func (m *GetQuickRecChannelByTagIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickRecChannelByTagIdResp.Marshal(b, m, deterministic)
}
func (dst *GetQuickRecChannelByTagIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickRecChannelByTagIdResp.Merge(dst, src)
}
func (m *GetQuickRecChannelByTagIdResp) XXX_Size() int {
	return xxx_messageInfo_GetQuickRecChannelByTagIdResp.Size(m)
}
func (m *GetQuickRecChannelByTagIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickRecChannelByTagIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickRecChannelByTagIdResp proto.InternalMessageInfo

func (m *GetQuickRecChannelByTagIdResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 获取用户roi信息
type GetUserRoiInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             string   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRoiInfoReq) Reset()         { *m = GetUserRoiInfoReq{} }
func (m *GetUserRoiInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRoiInfoReq) ProtoMessage()    {}
func (*GetUserRoiInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{73}
}
func (m *GetUserRoiInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRoiInfoReq.Unmarshal(m, b)
}
func (m *GetUserRoiInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRoiInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRoiInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRoiInfoReq.Merge(dst, src)
}
func (m *GetUserRoiInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRoiInfoReq.Size(m)
}
func (m *GetUserRoiInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRoiInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRoiInfoReq proto.InternalMessageInfo

func (m *GetUserRoiInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRoiInfoReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetUserRoiInfoResp struct {
	IsRoi                bool     `protobuf:"varint,1,opt,name=is_roi,json=isRoi,proto3" json:"is_roi,omitempty"`
	RoiHighCert          string   `protobuf:"bytes,2,opt,name=roi_high_cert,json=roiHighCert,proto3" json:"roi_high_cert,omitempty"`
	RoiHighPopUrl        string   `protobuf:"bytes,3,opt,name=roi_high_pop_url,json=roiHighPopUrl,proto3" json:"roi_high_pop_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRoiInfoResp) Reset()         { *m = GetUserRoiInfoResp{} }
func (m *GetUserRoiInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRoiInfoResp) ProtoMessage()    {}
func (*GetUserRoiInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{74}
}
func (m *GetUserRoiInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRoiInfoResp.Unmarshal(m, b)
}
func (m *GetUserRoiInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRoiInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRoiInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRoiInfoResp.Merge(dst, src)
}
func (m *GetUserRoiInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRoiInfoResp.Size(m)
}
func (m *GetUserRoiInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRoiInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRoiInfoResp proto.InternalMessageInfo

func (m *GetUserRoiInfoResp) GetIsRoi() bool {
	if m != nil {
		return m.IsRoi
	}
	return false
}

func (m *GetUserRoiInfoResp) GetRoiHighCert() string {
	if m != nil {
		return m.RoiHighCert
	}
	return ""
}

func (m *GetUserRoiInfoResp) GetRoiHighPopUrl() string {
	if m != nil {
		return m.RoiHighPopUrl
	}
	return ""
}

// 确认高潜付费用户弹窗显示
type ConfirmRoiHighPotentailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmRoiHighPotentailReq) Reset()         { *m = ConfirmRoiHighPotentailReq{} }
func (m *ConfirmRoiHighPotentailReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmRoiHighPotentailReq) ProtoMessage()    {}
func (*ConfirmRoiHighPotentailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{75}
}
func (m *ConfirmRoiHighPotentailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmRoiHighPotentailReq.Unmarshal(m, b)
}
func (m *ConfirmRoiHighPotentailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmRoiHighPotentailReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmRoiHighPotentailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmRoiHighPotentailReq.Merge(dst, src)
}
func (m *ConfirmRoiHighPotentailReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmRoiHighPotentailReq.Size(m)
}
func (m *ConfirmRoiHighPotentailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmRoiHighPotentailReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmRoiHighPotentailReq proto.InternalMessageInfo

func (m *ConfirmRoiHighPotentailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ConfirmRoiHighPotentailResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmRoiHighPotentailResp) Reset()         { *m = ConfirmRoiHighPotentailResp{} }
func (m *ConfirmRoiHighPotentailResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmRoiHighPotentailResp) ProtoMessage()    {}
func (*ConfirmRoiHighPotentailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{76}
}
func (m *ConfirmRoiHighPotentailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmRoiHighPotentailResp.Unmarshal(m, b)
}
func (m *ConfirmRoiHighPotentailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmRoiHighPotentailResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmRoiHighPotentailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmRoiHighPotentailResp.Merge(dst, src)
}
func (m *ConfirmRoiHighPotentailResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmRoiHighPotentailResp.Size(m)
}
func (m *ConfirmRoiHighPotentailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmRoiHighPotentailResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmRoiHighPotentailResp proto.InternalMessageInfo

type ChannelGroupResource struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	SubName              string   `protobuf:"bytes,3,opt,name=sub_name,json=subName,proto3" json:"sub_name,omitempty"`
	Background           string   `protobuf:"bytes,4,opt,name=background,proto3" json:"background,omitempty"`
	JumpUrl              string   `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	Rank                 float64  `protobuf:"fixed64,6,opt,name=rank,proto3" json:"rank,omitempty"`
	BeginTs              uint32   `protobuf:"varint,7,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	UpdateTs             uint32   `protobuf:"varint,8,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	Status               uint32   `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,10,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	EndTs                uint32   `protobuf:"varint,11,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	UrlType              uint32   `protobuf:"varint,12,opt,name=url_type,json=urlType,proto3" json:"url_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGroupResource) Reset()         { *m = ChannelGroupResource{} }
func (m *ChannelGroupResource) String() string { return proto.CompactTextString(m) }
func (*ChannelGroupResource) ProtoMessage()    {}
func (*ChannelGroupResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{77}
}
func (m *ChannelGroupResource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGroupResource.Unmarshal(m, b)
}
func (m *ChannelGroupResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGroupResource.Marshal(b, m, deterministic)
}
func (dst *ChannelGroupResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGroupResource.Merge(dst, src)
}
func (m *ChannelGroupResource) XXX_Size() int {
	return xxx_messageInfo_ChannelGroupResource.Size(m)
}
func (m *ChannelGroupResource) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGroupResource.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGroupResource proto.InternalMessageInfo

func (m *ChannelGroupResource) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChannelGroupResource) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChannelGroupResource) GetSubName() string {
	if m != nil {
		return m.SubName
	}
	return ""
}

func (m *ChannelGroupResource) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *ChannelGroupResource) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *ChannelGroupResource) GetRank() float64 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *ChannelGroupResource) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ChannelGroupResource) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *ChannelGroupResource) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelGroupResource) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *ChannelGroupResource) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ChannelGroupResource) GetUrlType() uint32 {
	if m != nil {
		return m.UrlType
	}
	return 0
}

// 房间组资源位配置列表请求
type GetChannelGroupResourceListReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Id                   uint32   `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelGroupResourceListReq) Reset()         { *m = GetChannelGroupResourceListReq{} }
func (m *GetChannelGroupResourceListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelGroupResourceListReq) ProtoMessage()    {}
func (*GetChannelGroupResourceListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{78}
}
func (m *GetChannelGroupResourceListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGroupResourceListReq.Unmarshal(m, b)
}
func (m *GetChannelGroupResourceListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGroupResourceListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelGroupResourceListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGroupResourceListReq.Merge(dst, src)
}
func (m *GetChannelGroupResourceListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelGroupResourceListReq.Size(m)
}
func (m *GetChannelGroupResourceListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGroupResourceListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGroupResourceListReq proto.InternalMessageInfo

func (m *GetChannelGroupResourceListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelGroupResourceListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetChannelGroupResourceListReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetChannelGroupResourceListReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetChannelGroupResourceListReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 房间组资源位配置列表响应
type GetChannelGroupResourceListResp struct {
	List                 []*ChannelGroupResource `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCnt             uint32                  `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetChannelGroupResourceListResp) Reset()         { *m = GetChannelGroupResourceListResp{} }
func (m *GetChannelGroupResourceListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelGroupResourceListResp) ProtoMessage()    {}
func (*GetChannelGroupResourceListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{79}
}
func (m *GetChannelGroupResourceListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGroupResourceListResp.Unmarshal(m, b)
}
func (m *GetChannelGroupResourceListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGroupResourceListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelGroupResourceListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGroupResourceListResp.Merge(dst, src)
}
func (m *GetChannelGroupResourceListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelGroupResourceListResp.Size(m)
}
func (m *GetChannelGroupResourceListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGroupResourceListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGroupResourceListResp proto.InternalMessageInfo

func (m *GetChannelGroupResourceListResp) GetList() []*ChannelGroupResource {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetChannelGroupResourceListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 房间组资源位配置添加请求
type AddChannelGroupResourceReq struct {
	Info                 *ChannelGroupResource `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *AddChannelGroupResourceReq) Reset()         { *m = AddChannelGroupResourceReq{} }
func (m *AddChannelGroupResourceReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelGroupResourceReq) ProtoMessage()    {}
func (*AddChannelGroupResourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{80}
}
func (m *AddChannelGroupResourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelGroupResourceReq.Unmarshal(m, b)
}
func (m *AddChannelGroupResourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelGroupResourceReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelGroupResourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelGroupResourceReq.Merge(dst, src)
}
func (m *AddChannelGroupResourceReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelGroupResourceReq.Size(m)
}
func (m *AddChannelGroupResourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelGroupResourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelGroupResourceReq proto.InternalMessageInfo

func (m *AddChannelGroupResourceReq) GetInfo() *ChannelGroupResource {
	if m != nil {
		return m.Info
	}
	return nil
}

// 房间组资源位配置添加响应
type AddChannelGroupResourceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelGroupResourceResp) Reset()         { *m = AddChannelGroupResourceResp{} }
func (m *AddChannelGroupResourceResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelGroupResourceResp) ProtoMessage()    {}
func (*AddChannelGroupResourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{81}
}
func (m *AddChannelGroupResourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelGroupResourceResp.Unmarshal(m, b)
}
func (m *AddChannelGroupResourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelGroupResourceResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelGroupResourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelGroupResourceResp.Merge(dst, src)
}
func (m *AddChannelGroupResourceResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelGroupResourceResp.Size(m)
}
func (m *AddChannelGroupResourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelGroupResourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelGroupResourceResp proto.InternalMessageInfo

// 房间组资源位配置更新请求
type UpdateChannelGroupResourceReq struct {
	Info                 *ChannelGroupResource `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UpdateChannelGroupResourceReq) Reset()         { *m = UpdateChannelGroupResourceReq{} }
func (m *UpdateChannelGroupResourceReq) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelGroupResourceReq) ProtoMessage()    {}
func (*UpdateChannelGroupResourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{82}
}
func (m *UpdateChannelGroupResourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelGroupResourceReq.Unmarshal(m, b)
}
func (m *UpdateChannelGroupResourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelGroupResourceReq.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelGroupResourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelGroupResourceReq.Merge(dst, src)
}
func (m *UpdateChannelGroupResourceReq) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelGroupResourceReq.Size(m)
}
func (m *UpdateChannelGroupResourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelGroupResourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelGroupResourceReq proto.InternalMessageInfo

func (m *UpdateChannelGroupResourceReq) GetInfo() *ChannelGroupResource {
	if m != nil {
		return m.Info
	}
	return nil
}

// 房间组资源位配置更新响应
type UpdateChannelGroupResourceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelGroupResourceResp) Reset()         { *m = UpdateChannelGroupResourceResp{} }
func (m *UpdateChannelGroupResourceResp) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelGroupResourceResp) ProtoMessage()    {}
func (*UpdateChannelGroupResourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{83}
}
func (m *UpdateChannelGroupResourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelGroupResourceResp.Unmarshal(m, b)
}
func (m *UpdateChannelGroupResourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelGroupResourceResp.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelGroupResourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelGroupResourceResp.Merge(dst, src)
}
func (m *UpdateChannelGroupResourceResp) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelGroupResourceResp.Size(m)
}
func (m *UpdateChannelGroupResourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelGroupResourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelGroupResourceResp proto.InternalMessageInfo

// 房间组资源位配置删除请求
type DelChannelGroupResourceReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelGroupResourceReq) Reset()         { *m = DelChannelGroupResourceReq{} }
func (m *DelChannelGroupResourceReq) String() string { return proto.CompactTextString(m) }
func (*DelChannelGroupResourceReq) ProtoMessage()    {}
func (*DelChannelGroupResourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{84}
}
func (m *DelChannelGroupResourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelGroupResourceReq.Unmarshal(m, b)
}
func (m *DelChannelGroupResourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelGroupResourceReq.Marshal(b, m, deterministic)
}
func (dst *DelChannelGroupResourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelGroupResourceReq.Merge(dst, src)
}
func (m *DelChannelGroupResourceReq) XXX_Size() int {
	return xxx_messageInfo_DelChannelGroupResourceReq.Size(m)
}
func (m *DelChannelGroupResourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelGroupResourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelGroupResourceReq proto.InternalMessageInfo

func (m *DelChannelGroupResourceReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 房间组资源位配置删除响应
type DelChannelGroupResourceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelGroupResourceResp) Reset()         { *m = DelChannelGroupResourceResp{} }
func (m *DelChannelGroupResourceResp) String() string { return proto.CompactTextString(m) }
func (*DelChannelGroupResourceResp) ProtoMessage()    {}
func (*DelChannelGroupResourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{85}
}
func (m *DelChannelGroupResourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelGroupResourceResp.Unmarshal(m, b)
}
func (m *DelChannelGroupResourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelGroupResourceResp.Marshal(b, m, deterministic)
}
func (dst *DelChannelGroupResourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelGroupResourceResp.Merge(dst, src)
}
func (m *DelChannelGroupResourceResp) XXX_Size() int {
	return xxx_messageInfo_DelChannelGroupResourceResp.Size(m)
}
func (m *DelChannelGroupResourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelGroupResourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelGroupResourceResp proto.InternalMessageInfo

// 检查房间组资源位配置有效性
type CheckChannelGroupResourceReq struct {
	Info                 *ChannelGroupResource `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CheckChannelGroupResourceReq) Reset()         { *m = CheckChannelGroupResourceReq{} }
func (m *CheckChannelGroupResourceReq) String() string { return proto.CompactTextString(m) }
func (*CheckChannelGroupResourceReq) ProtoMessage()    {}
func (*CheckChannelGroupResourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{86}
}
func (m *CheckChannelGroupResourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChannelGroupResourceReq.Unmarshal(m, b)
}
func (m *CheckChannelGroupResourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChannelGroupResourceReq.Marshal(b, m, deterministic)
}
func (dst *CheckChannelGroupResourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChannelGroupResourceReq.Merge(dst, src)
}
func (m *CheckChannelGroupResourceReq) XXX_Size() int {
	return xxx_messageInfo_CheckChannelGroupResourceReq.Size(m)
}
func (m *CheckChannelGroupResourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChannelGroupResourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChannelGroupResourceReq proto.InternalMessageInfo

func (m *CheckChannelGroupResourceReq) GetInfo() *ChannelGroupResource {
	if m != nil {
		return m.Info
	}
	return nil
}

// 检查房间组资源位配置有效性
type CheckChannelGroupResourceResp struct {
	IsValid              bool     `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckChannelGroupResourceResp) Reset()         { *m = CheckChannelGroupResourceResp{} }
func (m *CheckChannelGroupResourceResp) String() string { return proto.CompactTextString(m) }
func (*CheckChannelGroupResourceResp) ProtoMessage()    {}
func (*CheckChannelGroupResourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{87}
}
func (m *CheckChannelGroupResourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChannelGroupResourceResp.Unmarshal(m, b)
}
func (m *CheckChannelGroupResourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChannelGroupResourceResp.Marshal(b, m, deterministic)
}
func (dst *CheckChannelGroupResourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChannelGroupResourceResp.Merge(dst, src)
}
func (m *CheckChannelGroupResourceResp) XXX_Size() int {
	return xxx_messageInfo_CheckChannelGroupResourceResp.Size(m)
}
func (m *CheckChannelGroupResourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChannelGroupResourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChannelGroupResourceResp proto.InternalMessageInfo

func (m *CheckChannelGroupResourceResp) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

// 根据TAGID获取推荐房间
type GetChannelByTagIdReq struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Start                uint32   `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	UserCategory         uint32   `protobuf:"varint,4,opt,name=user_category,json=userCategory,proto3" json:"user_category,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelByTagIdReq) Reset()         { *m = GetChannelByTagIdReq{} }
func (m *GetChannelByTagIdReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelByTagIdReq) ProtoMessage()    {}
func (*GetChannelByTagIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{88}
}
func (m *GetChannelByTagIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelByTagIdReq.Unmarshal(m, b)
}
func (m *GetChannelByTagIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelByTagIdReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelByTagIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelByTagIdReq.Merge(dst, src)
}
func (m *GetChannelByTagIdReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelByTagIdReq.Size(m)
}
func (m *GetChannelByTagIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelByTagIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelByTagIdReq proto.InternalMessageInfo

func (m *GetChannelByTagIdReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetChannelByTagIdReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetChannelByTagIdReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetChannelByTagIdReq) GetUserCategory() uint32 {
	if m != nil {
		return m.UserCategory
	}
	return 0
}

func (m *GetChannelByTagIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetChannelByTagIdResp struct {
	ChannelList          []*ChannelRecommendSimpleInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	IsEnd                bool                          `protobuf:"varint,2,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetChannelByTagIdResp) Reset()         { *m = GetChannelByTagIdResp{} }
func (m *GetChannelByTagIdResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelByTagIdResp) ProtoMessage()    {}
func (*GetChannelByTagIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{89}
}
func (m *GetChannelByTagIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelByTagIdResp.Unmarshal(m, b)
}
func (m *GetChannelByTagIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelByTagIdResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelByTagIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelByTagIdResp.Merge(dst, src)
}
func (m *GetChannelByTagIdResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelByTagIdResp.Size(m)
}
func (m *GetChannelByTagIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelByTagIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelByTagIdResp proto.InternalMessageInfo

func (m *GetChannelByTagIdResp) GetChannelList() []*ChannelRecommendSimpleInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetChannelByTagIdResp) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

// 根据个性tagid请求房间数据
type GetRecChListByPerTagIdReq struct {
	PersonalityTagId     uint32   `protobuf:"varint,1,opt,name=personality_tag_id,json=personalityTagId,proto3" json:"personality_tag_id,omitempty"`
	Start                uint32   `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecChListByPerTagIdReq) Reset()         { *m = GetRecChListByPerTagIdReq{} }
func (m *GetRecChListByPerTagIdReq) String() string { return proto.CompactTextString(m) }
func (*GetRecChListByPerTagIdReq) ProtoMessage()    {}
func (*GetRecChListByPerTagIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{90}
}
func (m *GetRecChListByPerTagIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecChListByPerTagIdReq.Unmarshal(m, b)
}
func (m *GetRecChListByPerTagIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecChListByPerTagIdReq.Marshal(b, m, deterministic)
}
func (dst *GetRecChListByPerTagIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecChListByPerTagIdReq.Merge(dst, src)
}
func (m *GetRecChListByPerTagIdReq) XXX_Size() int {
	return xxx_messageInfo_GetRecChListByPerTagIdReq.Size(m)
}
func (m *GetRecChListByPerTagIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecChListByPerTagIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecChListByPerTagIdReq proto.InternalMessageInfo

func (m *GetRecChListByPerTagIdReq) GetPersonalityTagId() uint32 {
	if m != nil {
		return m.PersonalityTagId
	}
	return 0
}

func (m *GetRecChListByPerTagIdReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetRecChListByPerTagIdReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetRecChListByPerTagIdReq) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetRecChListByPerTagIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRecChListByPerTagIdResp struct {
	ChannelList          []*ChannelRecommendSimpleInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	IsEnd                bool                          `protobuf:"varint,2,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetRecChListByPerTagIdResp) Reset()         { *m = GetRecChListByPerTagIdResp{} }
func (m *GetRecChListByPerTagIdResp) String() string { return proto.CompactTextString(m) }
func (*GetRecChListByPerTagIdResp) ProtoMessage()    {}
func (*GetRecChListByPerTagIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{91}
}
func (m *GetRecChListByPerTagIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecChListByPerTagIdResp.Unmarshal(m, b)
}
func (m *GetRecChListByPerTagIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecChListByPerTagIdResp.Marshal(b, m, deterministic)
}
func (dst *GetRecChListByPerTagIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecChListByPerTagIdResp.Merge(dst, src)
}
func (m *GetRecChListByPerTagIdResp) XXX_Size() int {
	return xxx_messageInfo_GetRecChListByPerTagIdResp.Size(m)
}
func (m *GetRecChListByPerTagIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecChListByPerTagIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecChListByPerTagIdResp proto.InternalMessageInfo

func (m *GetRecChListByPerTagIdResp) GetChannelList() []*ChannelRecommendSimpleInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetRecChListByPerTagIdResp) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

// 批量获取房间的大礼物信息
type BatchGetChannelBigGiftInfoReq struct {
	CidList              []uint32 `protobuf:"varint,1,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelBigGiftInfoReq) Reset()         { *m = BatchGetChannelBigGiftInfoReq{} }
func (m *BatchGetChannelBigGiftInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelBigGiftInfoReq) ProtoMessage()    {}
func (*BatchGetChannelBigGiftInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{92}
}
func (m *BatchGetChannelBigGiftInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelBigGiftInfoReq.Unmarshal(m, b)
}
func (m *BatchGetChannelBigGiftInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelBigGiftInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelBigGiftInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelBigGiftInfoReq.Merge(dst, src)
}
func (m *BatchGetChannelBigGiftInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelBigGiftInfoReq.Size(m)
}
func (m *BatchGetChannelBigGiftInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelBigGiftInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelBigGiftInfoReq proto.InternalMessageInfo

func (m *BatchGetChannelBigGiftInfoReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

type BatchGetChannelBigGiftInfoResp struct {
	MapCidGift           map[uint32]*BigGiftInfo `protobuf:"bytes,1,rep,name=map_cid_gift,json=mapCidGift,proto3" json:"map_cid_gift,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetChannelBigGiftInfoResp) Reset()         { *m = BatchGetChannelBigGiftInfoResp{} }
func (m *BatchGetChannelBigGiftInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelBigGiftInfoResp) ProtoMessage()    {}
func (*BatchGetChannelBigGiftInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{93}
}
func (m *BatchGetChannelBigGiftInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelBigGiftInfoResp.Unmarshal(m, b)
}
func (m *BatchGetChannelBigGiftInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelBigGiftInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelBigGiftInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelBigGiftInfoResp.Merge(dst, src)
}
func (m *BatchGetChannelBigGiftInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelBigGiftInfoResp.Size(m)
}
func (m *BatchGetChannelBigGiftInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelBigGiftInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelBigGiftInfoResp proto.InternalMessageInfo

func (m *BatchGetChannelBigGiftInfoResp) GetMapCidGift() map[uint32]*BigGiftInfo {
	if m != nil {
		return m.MapCidGift
	}
	return nil
}

type BigGiftInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftIcon             string   `protobuf:"bytes,3,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BigGiftInfo) Reset()         { *m = BigGiftInfo{} }
func (m *BigGiftInfo) String() string { return proto.CompactTextString(m) }
func (*BigGiftInfo) ProtoMessage()    {}
func (*BigGiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{94}
}
func (m *BigGiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BigGiftInfo.Unmarshal(m, b)
}
func (m *BigGiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BigGiftInfo.Marshal(b, m, deterministic)
}
func (dst *BigGiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BigGiftInfo.Merge(dst, src)
}
func (m *BigGiftInfo) XXX_Size() int {
	return xxx_messageInfo_BigGiftInfo.Size(m)
}
func (m *BigGiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BigGiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BigGiftInfo proto.InternalMessageInfo

func (m *BigGiftInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *BigGiftInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *BigGiftInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

// 营收相关 开关合集
type BatchGetRevenueSwitchHubReq struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	SwitchType           uint32   `protobuf:"varint,2,opt,name=switch_type,json=switchType,proto3" json:"switch_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetRevenueSwitchHubReq) Reset()         { *m = BatchGetRevenueSwitchHubReq{} }
func (m *BatchGetRevenueSwitchHubReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetRevenueSwitchHubReq) ProtoMessage()    {}
func (*BatchGetRevenueSwitchHubReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{95}
}
func (m *BatchGetRevenueSwitchHubReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRevenueSwitchHubReq.Unmarshal(m, b)
}
func (m *BatchGetRevenueSwitchHubReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRevenueSwitchHubReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetRevenueSwitchHubReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRevenueSwitchHubReq.Merge(dst, src)
}
func (m *BatchGetRevenueSwitchHubReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetRevenueSwitchHubReq.Size(m)
}
func (m *BatchGetRevenueSwitchHubReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRevenueSwitchHubReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRevenueSwitchHubReq proto.InternalMessageInfo

func (m *BatchGetRevenueSwitchHubReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *BatchGetRevenueSwitchHubReq) GetSwitchType() uint32 {
	if m != nil {
		return m.SwitchType
	}
	return 0
}

type BatchGetRevenueSwitchHubResp struct {
	IsOpenMap            map[uint32]bool `protobuf:"bytes,2,rep,name=is_open_map,json=isOpenMap,proto3" json:"is_open_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetRevenueSwitchHubResp) Reset()         { *m = BatchGetRevenueSwitchHubResp{} }
func (m *BatchGetRevenueSwitchHubResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetRevenueSwitchHubResp) ProtoMessage()    {}
func (*BatchGetRevenueSwitchHubResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{96}
}
func (m *BatchGetRevenueSwitchHubResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetRevenueSwitchHubResp.Unmarshal(m, b)
}
func (m *BatchGetRevenueSwitchHubResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetRevenueSwitchHubResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetRevenueSwitchHubResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetRevenueSwitchHubResp.Merge(dst, src)
}
func (m *BatchGetRevenueSwitchHubResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetRevenueSwitchHubResp.Size(m)
}
func (m *BatchGetRevenueSwitchHubResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetRevenueSwitchHubResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetRevenueSwitchHubResp proto.InternalMessageInfo

func (m *BatchGetRevenueSwitchHubResp) GetIsOpenMap() map[uint32]bool {
	if m != nil {
		return m.IsOpenMap
	}
	return nil
}

type GetRevenueSwitchHubReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRevenueSwitchHubReq) Reset()         { *m = GetRevenueSwitchHubReq{} }
func (m *GetRevenueSwitchHubReq) String() string { return proto.CompactTextString(m) }
func (*GetRevenueSwitchHubReq) ProtoMessage()    {}
func (*GetRevenueSwitchHubReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{97}
}
func (m *GetRevenueSwitchHubReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueSwitchHubReq.Unmarshal(m, b)
}
func (m *GetRevenueSwitchHubReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueSwitchHubReq.Marshal(b, m, deterministic)
}
func (dst *GetRevenueSwitchHubReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueSwitchHubReq.Merge(dst, src)
}
func (m *GetRevenueSwitchHubReq) XXX_Size() int {
	return xxx_messageInfo_GetRevenueSwitchHubReq.Size(m)
}
func (m *GetRevenueSwitchHubReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueSwitchHubReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueSwitchHubReq proto.InternalMessageInfo

func (m *GetRevenueSwitchHubReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRevenueSwitchHubResp struct {
	IsOpenMap            map[uint32]bool `protobuf:"bytes,2,rep,name=is_open_map,json=isOpenMap,proto3" json:"is_open_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRevenueSwitchHubResp) Reset()         { *m = GetRevenueSwitchHubResp{} }
func (m *GetRevenueSwitchHubResp) String() string { return proto.CompactTextString(m) }
func (*GetRevenueSwitchHubResp) ProtoMessage()    {}
func (*GetRevenueSwitchHubResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{98}
}
func (m *GetRevenueSwitchHubResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueSwitchHubResp.Unmarshal(m, b)
}
func (m *GetRevenueSwitchHubResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueSwitchHubResp.Marshal(b, m, deterministic)
}
func (dst *GetRevenueSwitchHubResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueSwitchHubResp.Merge(dst, src)
}
func (m *GetRevenueSwitchHubResp) XXX_Size() int {
	return xxx_messageInfo_GetRevenueSwitchHubResp.Size(m)
}
func (m *GetRevenueSwitchHubResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueSwitchHubResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueSwitchHubResp proto.InternalMessageInfo

func (m *GetRevenueSwitchHubResp) GetIsOpenMap() map[uint32]bool {
	if m != nil {
		return m.IsOpenMap
	}
	return nil
}

// 设置营收开关
type SetRevenueSwitchHubReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SwitchType           uint32   `protobuf:"varint,2,opt,name=switch_type,json=switchType,proto3" json:"switch_type,omitempty"`
	IsOpen               bool     `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetRevenueSwitchHubReq) Reset()         { *m = SetRevenueSwitchHubReq{} }
func (m *SetRevenueSwitchHubReq) String() string { return proto.CompactTextString(m) }
func (*SetRevenueSwitchHubReq) ProtoMessage()    {}
func (*SetRevenueSwitchHubReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{99}
}
func (m *SetRevenueSwitchHubReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRevenueSwitchHubReq.Unmarshal(m, b)
}
func (m *SetRevenueSwitchHubReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRevenueSwitchHubReq.Marshal(b, m, deterministic)
}
func (dst *SetRevenueSwitchHubReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRevenueSwitchHubReq.Merge(dst, src)
}
func (m *SetRevenueSwitchHubReq) XXX_Size() int {
	return xxx_messageInfo_SetRevenueSwitchHubReq.Size(m)
}
func (m *SetRevenueSwitchHubReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRevenueSwitchHubReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetRevenueSwitchHubReq proto.InternalMessageInfo

func (m *SetRevenueSwitchHubReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetRevenueSwitchHubReq) GetSwitchType() uint32 {
	if m != nil {
		return m.SwitchType
	}
	return 0
}

func (m *SetRevenueSwitchHubReq) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetRevenueSwitchHubResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetRevenueSwitchHubResp) Reset()         { *m = SetRevenueSwitchHubResp{} }
func (m *SetRevenueSwitchHubResp) String() string { return proto.CompactTextString(m) }
func (*SetRevenueSwitchHubResp) ProtoMessage()    {}
func (*SetRevenueSwitchHubResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{100}
}
func (m *SetRevenueSwitchHubResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRevenueSwitchHubResp.Unmarshal(m, b)
}
func (m *SetRevenueSwitchHubResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRevenueSwitchHubResp.Marshal(b, m, deterministic)
}
func (dst *SetRevenueSwitchHubResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRevenueSwitchHubResp.Merge(dst, src)
}
func (m *SetRevenueSwitchHubResp) XXX_Size() int {
	return xxx_messageInfo_SetRevenueSwitchHubResp.Size(m)
}
func (m *SetRevenueSwitchHubResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRevenueSwitchHubResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetRevenueSwitchHubResp proto.InternalMessageInfo

// 获取优质用户信息
type GetUserQualityInfoReq struct {
	SceneType            uint32   `protobuf:"varint,1,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserQualityInfoReq) Reset()         { *m = GetUserQualityInfoReq{} }
func (m *GetUserQualityInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserQualityInfoReq) ProtoMessage()    {}
func (*GetUserQualityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{101}
}
func (m *GetUserQualityInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserQualityInfoReq.Unmarshal(m, b)
}
func (m *GetUserQualityInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserQualityInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserQualityInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserQualityInfoReq.Merge(dst, src)
}
func (m *GetUserQualityInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserQualityInfoReq.Size(m)
}
func (m *GetUserQualityInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserQualityInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserQualityInfoReq proto.InternalMessageInfo

func (m *GetUserQualityInfoReq) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *GetUserQualityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserQualityInfoResp struct {
	IsQuality            bool     `protobuf:"varint,1,opt,name=is_quality,json=isQuality,proto3" json:"is_quality,omitempty"`
	HighCertUrl          string   `protobuf:"bytes,2,opt,name=high_cert_url,json=highCertUrl,proto3" json:"high_cert_url,omitempty"`
	HignPopUrl           string   `protobuf:"bytes,3,opt,name=hign_pop_url,json=hignPopUrl,proto3" json:"hign_pop_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserQualityInfoResp) Reset()         { *m = GetUserQualityInfoResp{} }
func (m *GetUserQualityInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserQualityInfoResp) ProtoMessage()    {}
func (*GetUserQualityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{102}
}
func (m *GetUserQualityInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserQualityInfoResp.Unmarshal(m, b)
}
func (m *GetUserQualityInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserQualityInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserQualityInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserQualityInfoResp.Merge(dst, src)
}
func (m *GetUserQualityInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserQualityInfoResp.Size(m)
}
func (m *GetUserQualityInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserQualityInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserQualityInfoResp proto.InternalMessageInfo

func (m *GetUserQualityInfoResp) GetIsQuality() bool {
	if m != nil {
		return m.IsQuality
	}
	return false
}

func (m *GetUserQualityInfoResp) GetHighCertUrl() string {
	if m != nil {
		return m.HighCertUrl
	}
	return ""
}

func (m *GetUserQualityInfoResp) GetHignPopUrl() string {
	if m != nil {
		return m.HignPopUrl
	}
	return ""
}

// 确认壕用户弹窗显示
type ConfirmQualityHighPopReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmQualityHighPopReq) Reset()         { *m = ConfirmQualityHighPopReq{} }
func (m *ConfirmQualityHighPopReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmQualityHighPopReq) ProtoMessage()    {}
func (*ConfirmQualityHighPopReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{103}
}
func (m *ConfirmQualityHighPopReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmQualityHighPopReq.Unmarshal(m, b)
}
func (m *ConfirmQualityHighPopReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmQualityHighPopReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmQualityHighPopReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmQualityHighPopReq.Merge(dst, src)
}
func (m *ConfirmQualityHighPopReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmQualityHighPopReq.Size(m)
}
func (m *ConfirmQualityHighPopReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmQualityHighPopReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmQualityHighPopReq proto.InternalMessageInfo

func (m *ConfirmQualityHighPopReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ConfirmQualityHighPopResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmQualityHighPopResp) Reset()         { *m = ConfirmQualityHighPopResp{} }
func (m *ConfirmQualityHighPopResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmQualityHighPopResp) ProtoMessage()    {}
func (*ConfirmQualityHighPopResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{104}
}
func (m *ConfirmQualityHighPopResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmQualityHighPopResp.Unmarshal(m, b)
}
func (m *ConfirmQualityHighPopResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmQualityHighPopResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmQualityHighPopResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmQualityHighPopResp.Merge(dst, src)
}
func (m *ConfirmQualityHighPopResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmQualityHighPopResp.Size(m)
}
func (m *ConfirmQualityHighPopResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmQualityHighPopResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmQualityHighPopResp proto.InternalMessageInfo

// 获取顶部浮窗房间信息
type GetTopWinChannelInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopWinChannelInfoReq) Reset()         { *m = GetTopWinChannelInfoReq{} }
func (m *GetTopWinChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetTopWinChannelInfoReq) ProtoMessage()    {}
func (*GetTopWinChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{105}
}
func (m *GetTopWinChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopWinChannelInfoReq.Unmarshal(m, b)
}
func (m *GetTopWinChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopWinChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetTopWinChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopWinChannelInfoReq.Merge(dst, src)
}
func (m *GetTopWinChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetTopWinChannelInfoReq.Size(m)
}
func (m *GetTopWinChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopWinChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopWinChannelInfoReq proto.InternalMessageInfo

func (m *GetTopWinChannelInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type TopWinChannelInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TagId                uint32   `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string   `protobuf:"bytes,3,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	RecPoorSource        uint32   `protobuf:"varint,4,opt,name=rec_poor_source,json=recPoorSource,proto3" json:"rec_poor_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopWinChannelInfo) Reset()         { *m = TopWinChannelInfo{} }
func (m *TopWinChannelInfo) String() string { return proto.CompactTextString(m) }
func (*TopWinChannelInfo) ProtoMessage()    {}
func (*TopWinChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{106}
}
func (m *TopWinChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopWinChannelInfo.Unmarshal(m, b)
}
func (m *TopWinChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopWinChannelInfo.Marshal(b, m, deterministic)
}
func (dst *TopWinChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopWinChannelInfo.Merge(dst, src)
}
func (m *TopWinChannelInfo) XXX_Size() int {
	return xxx_messageInfo_TopWinChannelInfo.Size(m)
}
func (m *TopWinChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopWinChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopWinChannelInfo proto.InternalMessageInfo

func (m *TopWinChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TopWinChannelInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TopWinChannelInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *TopWinChannelInfo) GetRecPoorSource() uint32 {
	if m != nil {
		return m.RecPoorSource
	}
	return 0
}

type GetTopWinChannelInfoResp struct {
	Info                 *TopWinChannelInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	IsHitAbtest          bool               `protobuf:"varint,2,opt,name=is_hit_abtest,json=isHitAbtest,proto3" json:"is_hit_abtest,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetTopWinChannelInfoResp) Reset()         { *m = GetTopWinChannelInfoResp{} }
func (m *GetTopWinChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetTopWinChannelInfoResp) ProtoMessage()    {}
func (*GetTopWinChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{107}
}
func (m *GetTopWinChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopWinChannelInfoResp.Unmarshal(m, b)
}
func (m *GetTopWinChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopWinChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetTopWinChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopWinChannelInfoResp.Merge(dst, src)
}
func (m *GetTopWinChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetTopWinChannelInfoResp.Size(m)
}
func (m *GetTopWinChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopWinChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopWinChannelInfoResp proto.InternalMessageInfo

func (m *GetTopWinChannelInfoResp) GetInfo() *TopWinChannelInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetTopWinChannelInfoResp) GetIsHitAbtest() bool {
	if m != nil {
		return m.IsHitAbtest
	}
	return false
}

type CleanTopWinFilterReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CleanTopWinFilterReq) Reset()         { *m = CleanTopWinFilterReq{} }
func (m *CleanTopWinFilterReq) String() string { return proto.CompactTextString(m) }
func (*CleanTopWinFilterReq) ProtoMessage()    {}
func (*CleanTopWinFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{108}
}
func (m *CleanTopWinFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanTopWinFilterReq.Unmarshal(m, b)
}
func (m *CleanTopWinFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanTopWinFilterReq.Marshal(b, m, deterministic)
}
func (dst *CleanTopWinFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanTopWinFilterReq.Merge(dst, src)
}
func (m *CleanTopWinFilterReq) XXX_Size() int {
	return xxx_messageInfo_CleanTopWinFilterReq.Size(m)
}
func (m *CleanTopWinFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanTopWinFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_CleanTopWinFilterReq proto.InternalMessageInfo

func (m *CleanTopWinFilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CleanTopWinFilterResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CleanTopWinFilterResp) Reset()         { *m = CleanTopWinFilterResp{} }
func (m *CleanTopWinFilterResp) String() string { return proto.CompactTextString(m) }
func (*CleanTopWinFilterResp) ProtoMessage()    {}
func (*CleanTopWinFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{109}
}
func (m *CleanTopWinFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanTopWinFilterResp.Unmarshal(m, b)
}
func (m *CleanTopWinFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanTopWinFilterResp.Marshal(b, m, deterministic)
}
func (dst *CleanTopWinFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanTopWinFilterResp.Merge(dst, src)
}
func (m *CleanTopWinFilterResp) XXX_Size() int {
	return xxx_messageInfo_CleanTopWinFilterResp.Size(m)
}
func (m *CleanTopWinFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanTopWinFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_CleanTopWinFilterResp proto.InternalMessageInfo

// 获取顶部浮窗房间信息
type GetTopOverLayChannelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopOverLayChannelReq) Reset()         { *m = GetTopOverLayChannelReq{} }
func (m *GetTopOverLayChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetTopOverLayChannelReq) ProtoMessage()    {}
func (*GetTopOverLayChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{110}
}
func (m *GetTopOverLayChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopOverLayChannelReq.Unmarshal(m, b)
}
func (m *GetTopOverLayChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopOverLayChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetTopOverLayChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopOverLayChannelReq.Merge(dst, src)
}
func (m *GetTopOverLayChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetTopOverLayChannelReq.Size(m)
}
func (m *GetTopOverLayChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopOverLayChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopOverLayChannelReq proto.InternalMessageInfo

func (m *GetTopOverLayChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetTopOverLayChannelResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TagId                uint32   `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string   `protobuf:"bytes,3,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	RecommendPool        uint32   `protobuf:"varint,4,opt,name=recommend_pool,json=recommendPool,proto3" json:"recommend_pool,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopOverLayChannelResp) Reset()         { *m = GetTopOverLayChannelResp{} }
func (m *GetTopOverLayChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetTopOverLayChannelResp) ProtoMessage()    {}
func (*GetTopOverLayChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{111}
}
func (m *GetTopOverLayChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopOverLayChannelResp.Unmarshal(m, b)
}
func (m *GetTopOverLayChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopOverLayChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetTopOverLayChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopOverLayChannelResp.Merge(dst, src)
}
func (m *GetTopOverLayChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetTopOverLayChannelResp.Size(m)
}
func (m *GetTopOverLayChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopOverLayChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopOverLayChannelResp proto.InternalMessageInfo

func (m *GetTopOverLayChannelResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetTopOverLayChannelResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetTopOverLayChannelResp) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *GetTopOverLayChannelResp) GetRecommendPool() uint32 {
	if m != nil {
		return m.RecommendPool
	}
	return 0
}

// 快速进房权重配置
type QuickWeightConf struct {
	TagId                uint32            `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	MapLvWeight          map[uint32]uint32 `protobuf:"bytes,2,rep,name=map_lv_weight,json=mapLvWeight,proto3" json:"map_lv_weight,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *QuickWeightConf) Reset()         { *m = QuickWeightConf{} }
func (m *QuickWeightConf) String() string { return proto.CompactTextString(m) }
func (*QuickWeightConf) ProtoMessage()    {}
func (*QuickWeightConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{112}
}
func (m *QuickWeightConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuickWeightConf.Unmarshal(m, b)
}
func (m *QuickWeightConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuickWeightConf.Marshal(b, m, deterministic)
}
func (dst *QuickWeightConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuickWeightConf.Merge(dst, src)
}
func (m *QuickWeightConf) XXX_Size() int {
	return xxx_messageInfo_QuickWeightConf.Size(m)
}
func (m *QuickWeightConf) XXX_DiscardUnknown() {
	xxx_messageInfo_QuickWeightConf.DiscardUnknown(m)
}

var xxx_messageInfo_QuickWeightConf proto.InternalMessageInfo

func (m *QuickWeightConf) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *QuickWeightConf) GetMapLvWeight() map[uint32]uint32 {
	if m != nil {
		return m.MapLvWeight
	}
	return nil
}

// 获取快速进房权重配置列表
type GetQuickWeightConfListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickWeightConfListReq) Reset()         { *m = GetQuickWeightConfListReq{} }
func (m *GetQuickWeightConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickWeightConfListReq) ProtoMessage()    {}
func (*GetQuickWeightConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{113}
}
func (m *GetQuickWeightConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickWeightConfListReq.Unmarshal(m, b)
}
func (m *GetQuickWeightConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickWeightConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetQuickWeightConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickWeightConfListReq.Merge(dst, src)
}
func (m *GetQuickWeightConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetQuickWeightConfListReq.Size(m)
}
func (m *GetQuickWeightConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickWeightConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickWeightConfListReq proto.InternalMessageInfo

type GetQuickWeightConfListResp struct {
	ConfList             []*QuickWeightConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetQuickWeightConfListResp) Reset()         { *m = GetQuickWeightConfListResp{} }
func (m *GetQuickWeightConfListResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickWeightConfListResp) ProtoMessage()    {}
func (*GetQuickWeightConfListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{114}
}
func (m *GetQuickWeightConfListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickWeightConfListResp.Unmarshal(m, b)
}
func (m *GetQuickWeightConfListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickWeightConfListResp.Marshal(b, m, deterministic)
}
func (dst *GetQuickWeightConfListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickWeightConfListResp.Merge(dst, src)
}
func (m *GetQuickWeightConfListResp) XXX_Size() int {
	return xxx_messageInfo_GetQuickWeightConfListResp.Size(m)
}
func (m *GetQuickWeightConfListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickWeightConfListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickWeightConfListResp proto.InternalMessageInfo

func (m *GetQuickWeightConfListResp) GetConfList() []*QuickWeightConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

// 获取快速进房在线房间列表
type GetQuickRecChannelListReq struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickRecChannelListReq) Reset()         { *m = GetQuickRecChannelListReq{} }
func (m *GetQuickRecChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickRecChannelListReq) ProtoMessage()    {}
func (*GetQuickRecChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{115}
}
func (m *GetQuickRecChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickRecChannelListReq.Unmarshal(m, b)
}
func (m *GetQuickRecChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickRecChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetQuickRecChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickRecChannelListReq.Merge(dst, src)
}
func (m *GetQuickRecChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetQuickRecChannelListReq.Size(m)
}
func (m *GetQuickRecChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickRecChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickRecChannelListReq proto.InternalMessageInfo

func (m *GetQuickRecChannelListReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetQuickRecChannelListReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetQuickRecChannelListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetQuickRecChannelListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetQuickRecChannelListResp struct {
	CidList              []uint32 `protobuf:"varint,1,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickRecChannelListResp) Reset()         { *m = GetQuickRecChannelListResp{} }
func (m *GetQuickRecChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickRecChannelListResp) ProtoMessage()    {}
func (*GetQuickRecChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{116}
}
func (m *GetQuickRecChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickRecChannelListResp.Unmarshal(m, b)
}
func (m *GetQuickRecChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickRecChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetQuickRecChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickRecChannelListResp.Merge(dst, src)
}
func (m *GetQuickRecChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetQuickRecChannelListResp.Size(m)
}
func (m *GetQuickRecChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickRecChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickRecChannelListResp proto.InternalMessageInfo

func (m *GetQuickRecChannelListResp) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

// 二级标签配置
type SubTagConfig struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubTagConfig) Reset()         { *m = SubTagConfig{} }
func (m *SubTagConfig) String() string { return proto.CompactTextString(m) }
func (*SubTagConfig) ProtoMessage()    {}
func (*SubTagConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{117}
}
func (m *SubTagConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubTagConfig.Unmarshal(m, b)
}
func (m *SubTagConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubTagConfig.Marshal(b, m, deterministic)
}
func (dst *SubTagConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubTagConfig.Merge(dst, src)
}
func (m *SubTagConfig) XXX_Size() int {
	return xxx_messageInfo_SubTagConfig.Size(m)
}
func (m *SubTagConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SubTagConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SubTagConfig proto.InternalMessageInfo

func (m *SubTagConfig) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *SubTagConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type TagConfigInfo struct {
	RootTagName          string          `protobuf:"bytes,1,opt,name=root_tag_name,json=rootTagName,proto3" json:"root_tag_name,omitempty"`
	RootTagId            uint32          `protobuf:"varint,2,opt,name=root_tag_id,json=rootTagId,proto3" json:"root_tag_id,omitempty"`
	SubTagList           []*SubTagConfig `protobuf:"bytes,3,rep,name=sub_tag_list,json=subTagList,proto3" json:"sub_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *TagConfigInfo) Reset()         { *m = TagConfigInfo{} }
func (m *TagConfigInfo) String() string { return proto.CompactTextString(m) }
func (*TagConfigInfo) ProtoMessage()    {}
func (*TagConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{118}
}
func (m *TagConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagConfigInfo.Unmarshal(m, b)
}
func (m *TagConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagConfigInfo.Marshal(b, m, deterministic)
}
func (dst *TagConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagConfigInfo.Merge(dst, src)
}
func (m *TagConfigInfo) XXX_Size() int {
	return xxx_messageInfo_TagConfigInfo.Size(m)
}
func (m *TagConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TagConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TagConfigInfo proto.InternalMessageInfo

func (m *TagConfigInfo) GetRootTagName() string {
	if m != nil {
		return m.RootTagName
	}
	return ""
}

func (m *TagConfigInfo) GetRootTagId() uint32 {
	if m != nil {
		return m.RootTagId
	}
	return 0
}

func (m *TagConfigInfo) GetSubTagList() []*SubTagConfig {
	if m != nil {
		return m.SubTagList
	}
	return nil
}

// 获取标签配置列表
type GetTagConfigInfoListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTagConfigInfoListReq) Reset()         { *m = GetTagConfigInfoListReq{} }
func (m *GetTagConfigInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetTagConfigInfoListReq) ProtoMessage()    {}
func (*GetTagConfigInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{119}
}
func (m *GetTagConfigInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTagConfigInfoListReq.Unmarshal(m, b)
}
func (m *GetTagConfigInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTagConfigInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetTagConfigInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagConfigInfoListReq.Merge(dst, src)
}
func (m *GetTagConfigInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetTagConfigInfoListReq.Size(m)
}
func (m *GetTagConfigInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagConfigInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagConfigInfoListReq proto.InternalMessageInfo

type GetTagConfigInfoListResp struct {
	TagList              []*TagConfigInfo `protobuf:"bytes,1,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetTagConfigInfoListResp) Reset()         { *m = GetTagConfigInfoListResp{} }
func (m *GetTagConfigInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetTagConfigInfoListResp) ProtoMessage()    {}
func (*GetTagConfigInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{120}
}
func (m *GetTagConfigInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTagConfigInfoListResp.Unmarshal(m, b)
}
func (m *GetTagConfigInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTagConfigInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetTagConfigInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagConfigInfoListResp.Merge(dst, src)
}
func (m *GetTagConfigInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetTagConfigInfoListResp.Size(m)
}
func (m *GetTagConfigInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagConfigInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagConfigInfoListResp proto.InternalMessageInfo

func (m *GetTagConfigInfoListResp) GetTagList() []*TagConfigInfo {
	if m != nil {
		return m.TagList
	}
	return nil
}

// 获取推荐列表房间反馈配置
type GetRecFeedbackConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecFeedbackConfigReq) Reset()         { *m = GetRecFeedbackConfigReq{} }
func (m *GetRecFeedbackConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetRecFeedbackConfigReq) ProtoMessage()    {}
func (*GetRecFeedbackConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{121}
}
func (m *GetRecFeedbackConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecFeedbackConfigReq.Unmarshal(m, b)
}
func (m *GetRecFeedbackConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecFeedbackConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetRecFeedbackConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecFeedbackConfigReq.Merge(dst, src)
}
func (m *GetRecFeedbackConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetRecFeedbackConfigReq.Size(m)
}
func (m *GetRecFeedbackConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecFeedbackConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecFeedbackConfigReq proto.InternalMessageInfo

type GetRecFeedbackConfigResp struct {
	ReasonList           []string `protobuf:"bytes,1,rep,name=reason_list,json=reasonList,proto3" json:"reason_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecFeedbackConfigResp) Reset()         { *m = GetRecFeedbackConfigResp{} }
func (m *GetRecFeedbackConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetRecFeedbackConfigResp) ProtoMessage()    {}
func (*GetRecFeedbackConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{122}
}
func (m *GetRecFeedbackConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecFeedbackConfigResp.Unmarshal(m, b)
}
func (m *GetRecFeedbackConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecFeedbackConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetRecFeedbackConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecFeedbackConfigResp.Merge(dst, src)
}
func (m *GetRecFeedbackConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetRecFeedbackConfigResp.Size(m)
}
func (m *GetRecFeedbackConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecFeedbackConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecFeedbackConfigResp proto.InternalMessageInfo

func (m *GetRecFeedbackConfigResp) GetReasonList() []string {
	if m != nil {
		return m.ReasonList
	}
	return nil
}

// 推荐房间反馈
type DoRecFeedbackReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReasonList           []string `protobuf:"bytes,3,rep,name=reason_list,json=reasonList,proto3" json:"reason_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoRecFeedbackReq) Reset()         { *m = DoRecFeedbackReq{} }
func (m *DoRecFeedbackReq) String() string { return proto.CompactTextString(m) }
func (*DoRecFeedbackReq) ProtoMessage()    {}
func (*DoRecFeedbackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{123}
}
func (m *DoRecFeedbackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoRecFeedbackReq.Unmarshal(m, b)
}
func (m *DoRecFeedbackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoRecFeedbackReq.Marshal(b, m, deterministic)
}
func (dst *DoRecFeedbackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoRecFeedbackReq.Merge(dst, src)
}
func (m *DoRecFeedbackReq) XXX_Size() int {
	return xxx_messageInfo_DoRecFeedbackReq.Size(m)
}
func (m *DoRecFeedbackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DoRecFeedbackReq.DiscardUnknown(m)
}

var xxx_messageInfo_DoRecFeedbackReq proto.InternalMessageInfo

func (m *DoRecFeedbackReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DoRecFeedbackReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DoRecFeedbackReq) GetReasonList() []string {
	if m != nil {
		return m.ReasonList
	}
	return nil
}

type DoRecFeedbackResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DoRecFeedbackResp) Reset()         { *m = DoRecFeedbackResp{} }
func (m *DoRecFeedbackResp) String() string { return proto.CompactTextString(m) }
func (*DoRecFeedbackResp) ProtoMessage()    {}
func (*DoRecFeedbackResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{124}
}
func (m *DoRecFeedbackResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoRecFeedbackResp.Unmarshal(m, b)
}
func (m *DoRecFeedbackResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoRecFeedbackResp.Marshal(b, m, deterministic)
}
func (dst *DoRecFeedbackResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoRecFeedbackResp.Merge(dst, src)
}
func (m *DoRecFeedbackResp) XXX_Size() int {
	return xxx_messageInfo_DoRecFeedbackResp.Size(m)
}
func (m *DoRecFeedbackResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DoRecFeedbackResp.DiscardUnknown(m)
}

var xxx_messageInfo_DoRecFeedbackResp proto.InternalMessageInfo

// 获取婚礼房间列表
type CheckUidIsInWeddingGroupReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUidIsInWeddingGroupReq) Reset()         { *m = CheckUidIsInWeddingGroupReq{} }
func (m *CheckUidIsInWeddingGroupReq) String() string { return proto.CompactTextString(m) }
func (*CheckUidIsInWeddingGroupReq) ProtoMessage()    {}
func (*CheckUidIsInWeddingGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{125}
}
func (m *CheckUidIsInWeddingGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUidIsInWeddingGroupReq.Unmarshal(m, b)
}
func (m *CheckUidIsInWeddingGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUidIsInWeddingGroupReq.Marshal(b, m, deterministic)
}
func (dst *CheckUidIsInWeddingGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUidIsInWeddingGroupReq.Merge(dst, src)
}
func (m *CheckUidIsInWeddingGroupReq) XXX_Size() int {
	return xxx_messageInfo_CheckUidIsInWeddingGroupReq.Size(m)
}
func (m *CheckUidIsInWeddingGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUidIsInWeddingGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUidIsInWeddingGroupReq proto.InternalMessageInfo

func (m *CheckUidIsInWeddingGroupReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUidIsInWeddingGroupResp struct {
	IsInWeddingGroup     bool     `protobuf:"varint,1,opt,name=is_in_wedding_group,json=isInWeddingGroup,proto3" json:"is_in_wedding_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUidIsInWeddingGroupResp) Reset()         { *m = CheckUidIsInWeddingGroupResp{} }
func (m *CheckUidIsInWeddingGroupResp) String() string { return proto.CompactTextString(m) }
func (*CheckUidIsInWeddingGroupResp) ProtoMessage()    {}
func (*CheckUidIsInWeddingGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_svr_9183e74027c33df8, []int{126}
}
func (m *CheckUidIsInWeddingGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUidIsInWeddingGroupResp.Unmarshal(m, b)
}
func (m *CheckUidIsInWeddingGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUidIsInWeddingGroupResp.Marshal(b, m, deterministic)
}
func (dst *CheckUidIsInWeddingGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUidIsInWeddingGroupResp.Merge(dst, src)
}
func (m *CheckUidIsInWeddingGroupResp) XXX_Size() int {
	return xxx_messageInfo_CheckUidIsInWeddingGroupResp.Size(m)
}
func (m *CheckUidIsInWeddingGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUidIsInWeddingGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUidIsInWeddingGroupResp proto.InternalMessageInfo

func (m *CheckUidIsInWeddingGroupResp) GetIsInWeddingGroup() bool {
	if m != nil {
		return m.IsInWeddingGroup
	}
	return false
}

func init() {
	proto.RegisterType((*LevelConf)(nil), "channelrecommend.LevelConf")
	proto.RegisterType((*LimitConf)(nil), "channelrecommend.LimitConf")
	proto.RegisterType((*FlowCardLimitConf)(nil), "channelrecommend.FlowCardLimitConf")
	proto.RegisterType((*GetFlowCardLimitConfListReq)(nil), "channelrecommend.GetFlowCardLimitConfListReq")
	proto.RegisterType((*GetFlowCardLimitConfListResp)(nil), "channelrecommend.GetFlowCardLimitConfListResp")
	proto.RegisterType((*AddFlowCardLimitConfReq)(nil), "channelrecommend.AddFlowCardLimitConfReq")
	proto.RegisterType((*AddFlowCardLimitConfResp)(nil), "channelrecommend.AddFlowCardLimitConfResp")
	proto.RegisterType((*UpdateFlowCardLimitConfReq)(nil), "channelrecommend.UpdateFlowCardLimitConfReq")
	proto.RegisterType((*UpdateFlowCardLimitConfResp)(nil), "channelrecommend.UpdateFlowCardLimitConfResp")
	proto.RegisterType((*DelFlowCardLimitConfReq)(nil), "channelrecommend.DelFlowCardLimitConfReq")
	proto.RegisterType((*DelFlowCardLimitConfResp)(nil), "channelrecommend.DelFlowCardLimitConfResp")
	proto.RegisterType((*FlowCardGrantInfo)(nil), "channelrecommend.FlowCardGrantInfo")
	proto.RegisterType((*GetGrantFlowCardListReq)(nil), "channelrecommend.GetGrantFlowCardListReq")
	proto.RegisterType((*GetGrantFlowCardListResp)(nil), "channelrecommend.GetGrantFlowCardListResp")
	proto.RegisterType((*GrantFlowCardReq)(nil), "channelrecommend.GrantFlowCardReq")
	proto.RegisterType((*GrantFlowCardResp)(nil), "channelrecommend.GrantFlowCardResp")
	proto.RegisterType((*GrantErrorMsg)(nil), "channelrecommend.GrantErrorMsg")
	proto.RegisterType((*BatGrantFlowCardReq)(nil), "channelrecommend.BatGrantFlowCardReq")
	proto.RegisterType((*BatGrantFlowCardResp)(nil), "channelrecommend.BatGrantFlowCardResp")
	proto.RegisterType((*ReclaimGrantedFlowCardReq)(nil), "channelrecommend.ReclaimGrantedFlowCardReq")
	proto.RegisterType((*ReclaimGrantedFlowCardResp)(nil), "channelrecommend.ReclaimGrantedFlowCardResp")
	proto.RegisterType((*BanGrantedFlowCardReq)(nil), "channelrecommend.BanGrantedFlowCardReq")
	proto.RegisterType((*BanGrantedFlowCardResp)(nil), "channelrecommend.BanGrantedFlowCardResp")
	proto.RegisterType((*GetFlowCardHourRemainCntReq)(nil), "channelrecommend.GetFlowCardHourRemainCntReq")
	proto.RegisterType((*GetFlowCardHourRemainCntResp)(nil), "channelrecommend.GetFlowCardHourRemainCntResp")
	proto.RegisterType((*UseFlowCardReq)(nil), "channelrecommend.UseFlowCardReq")
	proto.RegisterType((*UseFlowCardResp)(nil), "channelrecommend.UseFlowCardResp")
	proto.RegisterType((*GrantAnchorFlowCardByGuildReq)(nil), "channelrecommend.GrantAnchorFlowCardByGuildReq")
	proto.RegisterType((*GrantAnchorFlowCardByGuildResp)(nil), "channelrecommend.GrantAnchorFlowCardByGuildResp")
	proto.RegisterType((*GetFlowCardListByTypeReq)(nil), "channelrecommend.GetFlowCardListByTypeReq")
	proto.RegisterType((*GetFlowCardListByTypeResp)(nil), "channelrecommend.GetFlowCardListByTypeResp")
	proto.RegisterType((*GetAllUseFlowCardAnchorReq)(nil), "channelrecommend.GetAllUseFlowCardAnchorReq")
	proto.RegisterType((*GetAllUseFlowCardAnchorResp)(nil), "channelrecommend.GetAllUseFlowCardAnchorResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channelrecommend.GetAllUseFlowCardAnchorResp.MapCidLvEntry")
	proto.RegisterType((*GiftInfo)(nil), "channelrecommend.GiftInfo")
	proto.RegisterType((*RecLotteryChInfo)(nil), "channelrecommend.RecLotteryChInfo")
	proto.RegisterType((*GetRecLotteryChListReq)(nil), "channelrecommend.GetRecLotteryChListReq")
	proto.RegisterType((*GetRecLotteryChListResp)(nil), "channelrecommend.GetRecLotteryChListResp")
	proto.RegisterType((*GetLotteryChannelRecInfoReq)(nil), "channelrecommend.GetLotteryChannelRecInfoReq")
	proto.RegisterType((*GetLotteryChannelRecInfoResp)(nil), "channelrecommend.GetLotteryChannelRecInfoResp")
	proto.RegisterType((*TimeSection)(nil), "channelrecommend.TimeSection")
	proto.RegisterType((*ChannelRecommendSimpleInfo)(nil), "channelrecommend.ChannelRecommendSimpleInfo")
	proto.RegisterType((*GetRecommendChannelReq)(nil), "channelrecommend.GetRecommendChannelReq")
	proto.RegisterType((*GetRecommendChannelResp)(nil), "channelrecommend.GetRecommendChannelResp")
	proto.RegisterType((*GetRecommendChannelV2Req)(nil), "channelrecommend.GetRecommendChannelV2Req")
	proto.RegisterType((*GetRecommendChannelV2Resp)(nil), "channelrecommend.GetRecommendChannelV2Resp")
	proto.RegisterType((*PrepareChannelInfo)(nil), "channelrecommend.PrepareChannelInfo")
	proto.RegisterType((*GetPrepareChannelListReq)(nil), "channelrecommend.GetPrepareChannelListReq")
	proto.RegisterType((*GetPrepareChannelListResp)(nil), "channelrecommend.GetPrepareChannelListResp")
	proto.RegisterType((*SetPrepareChannelReq)(nil), "channelrecommend.SetPrepareChannelReq")
	proto.RegisterType((*SetPrepareChannelResp)(nil), "channelrecommend.SetPrepareChannelResp")
	proto.RegisterType((*DelPrepareChannelReq)(nil), "channelrecommend.DelPrepareChannelReq")
	proto.RegisterType((*DelPrepareChannelResp)(nil), "channelrecommend.DelPrepareChannelResp")
	proto.RegisterType((*GetPrepareBackupListReq)(nil), "channelrecommend.GetPrepareBackupListReq")
	proto.RegisterType((*GetPrepareBackupListResp)(nil), "channelrecommend.GetPrepareBackupListResp")
	proto.RegisterType((*PrepareOperRecord)(nil), "channelrecommend.PrepareOperRecord")
	proto.RegisterType((*GetPrepareOperRecordListReq)(nil), "channelrecommend.GetPrepareOperRecordListReq")
	proto.RegisterType((*GetPrepareOperRecordListResp)(nil), "channelrecommend.GetPrepareOperRecordListResp")
	proto.RegisterType((*DisplaySceneInfo)(nil), "channelrecommend.DisplaySceneInfo")
	proto.RegisterType((*QuickEntryConfig)(nil), "channelrecommend.QuickEntryConfig")
	proto.RegisterType((*GetDisplaySceneInfoListReq)(nil), "channelrecommend.GetDisplaySceneInfoListReq")
	proto.RegisterType((*GetDisplaySceneInfoListResp)(nil), "channelrecommend.GetDisplaySceneInfoListResp")
	proto.RegisterType((*AddDisplaySceneInfoReq)(nil), "channelrecommend.AddDisplaySceneInfoReq")
	proto.RegisterType((*AddDisplaySceneInfoResp)(nil), "channelrecommend.AddDisplaySceneInfoResp")
	proto.RegisterType((*DelDisplaySceneInfoReq)(nil), "channelrecommend.DelDisplaySceneInfoReq")
	proto.RegisterType((*DelDisplaySceneInfoResp)(nil), "channelrecommend.DelDisplaySceneInfoResp")
	proto.RegisterType((*GetQuickEntryConfigListReq)(nil), "channelrecommend.GetQuickEntryConfigListReq")
	proto.RegisterType((*GetQuickEntryConfigListResp)(nil), "channelrecommend.GetQuickEntryConfigListResp")
	proto.RegisterMapType((map[uint32]*QuickEntryConfig)(nil), "channelrecommend.GetQuickEntryConfigListResp.MapTypeConfEntry")
	proto.RegisterType((*BatGetChannelSoundLabelReq)(nil), "channelrecommend.BatGetChannelSoundLabelReq")
	proto.RegisterType((*BatGetChannelSoundLabelResp)(nil), "channelrecommend.BatGetChannelSoundLabelResp")
	proto.RegisterMapType((map[uint32]string)(nil), "channelrecommend.BatGetChannelSoundLabelResp.MapCidLabelEntry")
	proto.RegisterType((*TriggerTimerReq)(nil), "channelrecommend.TriggerTimerReq")
	proto.RegisterType((*TriggerTimerResp)(nil), "channelrecommend.TriggerTimerResp")
	proto.RegisterType((*GetQuickRecChannelByTagIdReq)(nil), "channelrecommend.GetQuickRecChannelByTagIdReq")
	proto.RegisterType((*GetQuickRecChannelByTagIdResp)(nil), "channelrecommend.GetQuickRecChannelByTagIdResp")
	proto.RegisterType((*GetUserRoiInfoReq)(nil), "channelrecommend.GetUserRoiInfoReq")
	proto.RegisterType((*GetUserRoiInfoResp)(nil), "channelrecommend.GetUserRoiInfoResp")
	proto.RegisterType((*ConfirmRoiHighPotentailReq)(nil), "channelrecommend.ConfirmRoiHighPotentailReq")
	proto.RegisterType((*ConfirmRoiHighPotentailResp)(nil), "channelrecommend.ConfirmRoiHighPotentailResp")
	proto.RegisterType((*ChannelGroupResource)(nil), "channelrecommend.ChannelGroupResource")
	proto.RegisterType((*GetChannelGroupResourceListReq)(nil), "channelrecommend.GetChannelGroupResourceListReq")
	proto.RegisterType((*GetChannelGroupResourceListResp)(nil), "channelrecommend.GetChannelGroupResourceListResp")
	proto.RegisterType((*AddChannelGroupResourceReq)(nil), "channelrecommend.AddChannelGroupResourceReq")
	proto.RegisterType((*AddChannelGroupResourceResp)(nil), "channelrecommend.AddChannelGroupResourceResp")
	proto.RegisterType((*UpdateChannelGroupResourceReq)(nil), "channelrecommend.UpdateChannelGroupResourceReq")
	proto.RegisterType((*UpdateChannelGroupResourceResp)(nil), "channelrecommend.UpdateChannelGroupResourceResp")
	proto.RegisterType((*DelChannelGroupResourceReq)(nil), "channelrecommend.DelChannelGroupResourceReq")
	proto.RegisterType((*DelChannelGroupResourceResp)(nil), "channelrecommend.DelChannelGroupResourceResp")
	proto.RegisterType((*CheckChannelGroupResourceReq)(nil), "channelrecommend.CheckChannelGroupResourceReq")
	proto.RegisterType((*CheckChannelGroupResourceResp)(nil), "channelrecommend.CheckChannelGroupResourceResp")
	proto.RegisterType((*GetChannelByTagIdReq)(nil), "channelrecommend.GetChannelByTagIdReq")
	proto.RegisterType((*GetChannelByTagIdResp)(nil), "channelrecommend.GetChannelByTagIdResp")
	proto.RegisterType((*GetRecChListByPerTagIdReq)(nil), "channelrecommend.GetRecChListByPerTagIdReq")
	proto.RegisterType((*GetRecChListByPerTagIdResp)(nil), "channelrecommend.GetRecChListByPerTagIdResp")
	proto.RegisterType((*BatchGetChannelBigGiftInfoReq)(nil), "channelrecommend.BatchGetChannelBigGiftInfoReq")
	proto.RegisterType((*BatchGetChannelBigGiftInfoResp)(nil), "channelrecommend.BatchGetChannelBigGiftInfoResp")
	proto.RegisterMapType((map[uint32]*BigGiftInfo)(nil), "channelrecommend.BatchGetChannelBigGiftInfoResp.MapCidGiftEntry")
	proto.RegisterType((*BigGiftInfo)(nil), "channelrecommend.BigGiftInfo")
	proto.RegisterType((*BatchGetRevenueSwitchHubReq)(nil), "channelrecommend.BatchGetRevenueSwitchHubReq")
	proto.RegisterType((*BatchGetRevenueSwitchHubResp)(nil), "channelrecommend.BatchGetRevenueSwitchHubResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "channelrecommend.BatchGetRevenueSwitchHubResp.IsOpenMapEntry")
	proto.RegisterType((*GetRevenueSwitchHubReq)(nil), "channelrecommend.GetRevenueSwitchHubReq")
	proto.RegisterType((*GetRevenueSwitchHubResp)(nil), "channelrecommend.GetRevenueSwitchHubResp")
	proto.RegisterMapType((map[uint32]bool)(nil), "channelrecommend.GetRevenueSwitchHubResp.IsOpenMapEntry")
	proto.RegisterType((*SetRevenueSwitchHubReq)(nil), "channelrecommend.SetRevenueSwitchHubReq")
	proto.RegisterType((*SetRevenueSwitchHubResp)(nil), "channelrecommend.SetRevenueSwitchHubResp")
	proto.RegisterType((*GetUserQualityInfoReq)(nil), "channelrecommend.GetUserQualityInfoReq")
	proto.RegisterType((*GetUserQualityInfoResp)(nil), "channelrecommend.GetUserQualityInfoResp")
	proto.RegisterType((*ConfirmQualityHighPopReq)(nil), "channelrecommend.ConfirmQualityHighPopReq")
	proto.RegisterType((*ConfirmQualityHighPopResp)(nil), "channelrecommend.ConfirmQualityHighPopResp")
	proto.RegisterType((*GetTopWinChannelInfoReq)(nil), "channelrecommend.GetTopWinChannelInfoReq")
	proto.RegisterType((*TopWinChannelInfo)(nil), "channelrecommend.TopWinChannelInfo")
	proto.RegisterType((*GetTopWinChannelInfoResp)(nil), "channelrecommend.GetTopWinChannelInfoResp")
	proto.RegisterType((*CleanTopWinFilterReq)(nil), "channelrecommend.CleanTopWinFilterReq")
	proto.RegisterType((*CleanTopWinFilterResp)(nil), "channelrecommend.CleanTopWinFilterResp")
	proto.RegisterType((*GetTopOverLayChannelReq)(nil), "channelrecommend.GetTopOverLayChannelReq")
	proto.RegisterType((*GetTopOverLayChannelResp)(nil), "channelrecommend.GetTopOverLayChannelResp")
	proto.RegisterType((*QuickWeightConf)(nil), "channelrecommend.QuickWeightConf")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channelrecommend.QuickWeightConf.MapLvWeightEntry")
	proto.RegisterType((*GetQuickWeightConfListReq)(nil), "channelrecommend.GetQuickWeightConfListReq")
	proto.RegisterType((*GetQuickWeightConfListResp)(nil), "channelrecommend.GetQuickWeightConfListResp")
	proto.RegisterType((*GetQuickRecChannelListReq)(nil), "channelrecommend.GetQuickRecChannelListReq")
	proto.RegisterType((*GetQuickRecChannelListResp)(nil), "channelrecommend.GetQuickRecChannelListResp")
	proto.RegisterType((*SubTagConfig)(nil), "channelrecommend.SubTagConfig")
	proto.RegisterType((*TagConfigInfo)(nil), "channelrecommend.TagConfigInfo")
	proto.RegisterType((*GetTagConfigInfoListReq)(nil), "channelrecommend.GetTagConfigInfoListReq")
	proto.RegisterType((*GetTagConfigInfoListResp)(nil), "channelrecommend.GetTagConfigInfoListResp")
	proto.RegisterType((*GetRecFeedbackConfigReq)(nil), "channelrecommend.GetRecFeedbackConfigReq")
	proto.RegisterType((*GetRecFeedbackConfigResp)(nil), "channelrecommend.GetRecFeedbackConfigResp")
	proto.RegisterType((*DoRecFeedbackReq)(nil), "channelrecommend.DoRecFeedbackReq")
	proto.RegisterType((*DoRecFeedbackResp)(nil), "channelrecommend.DoRecFeedbackResp")
	proto.RegisterType((*CheckUidIsInWeddingGroupReq)(nil), "channelrecommend.CheckUidIsInWeddingGroupReq")
	proto.RegisterType((*CheckUidIsInWeddingGroupResp)(nil), "channelrecommend.CheckUidIsInWeddingGroupResp")
	proto.RegisterEnum("channelrecommend.RecommendLevel", RecommendLevel_name, RecommendLevel_value)
	proto.RegisterEnum("channelrecommend.FlowCardGrantType", FlowCardGrantType_name, FlowCardGrantType_value)
	proto.RegisterEnum("channelrecommend.UserCategory", UserCategory_name, UserCategory_value)
	proto.RegisterEnum("channelrecommend.ChannelCategory", ChannelCategory_name, ChannelCategory_value)
	proto.RegisterEnum("channelrecommend.ChannelLevel", ChannelLevel_name, ChannelLevel_value)
	proto.RegisterEnum("channelrecommend.RecommendRelationshipType", RecommendRelationshipType_name, RecommendRelationshipType_value)
	proto.RegisterEnum("channelrecommend.RoiChannelCertType", RoiChannelCertType_name, RoiChannelCertType_value)
	proto.RegisterEnum("channelrecommend.PrepareType", PrepareType_name, PrepareType_value)
	proto.RegisterEnum("channelrecommend.PrepareOperType", PrepareOperType_name, PrepareOperType_value)
	proto.RegisterEnum("channelrecommend.DisplaySceneStatusType", DisplaySceneStatusType_name, DisplaySceneStatusType_value)
	proto.RegisterEnum("channelrecommend.DisplaySceneTimeType", DisplaySceneTimeType_name, DisplaySceneTimeType_value)
	proto.RegisterEnum("channelrecommend.QuickRecChannelType", QuickRecChannelType_name, QuickRecChannelType_value)
	proto.RegisterEnum("channelrecommend.GetFlowCardListByTypeReq_GetType", GetFlowCardListByTypeReq_GetType_name, GetFlowCardListByTypeReq_GetType_value)
	proto.RegisterEnum("channelrecommend.TriggerTimerReq_TimerType", TriggerTimerReq_TimerType_name, TriggerTimerReq_TimerType_value)
	proto.RegisterEnum("channelrecommend.GetUserQualityInfoReq_QueryScene", GetUserQualityInfoReq_QueryScene_name, GetUserQualityInfoReq_QueryScene_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelRecommendSvrClient is the client API for ChannelRecommendSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelRecommendSvrClient interface {
	// 获取流量卡限额配置列表
	GetFlowCardLimitConfList(ctx context.Context, in *GetFlowCardLimitConfListReq, opts ...grpc.CallOption) (*GetFlowCardLimitConfListResp, error)
	// 增加流量卡限额配置
	AddFlowCardLimitConf(ctx context.Context, in *AddFlowCardLimitConfReq, opts ...grpc.CallOption) (*AddFlowCardLimitConfResp, error)
	// 更新流量卡限额配置
	UpdateFlowCardLimitConf(ctx context.Context, in *UpdateFlowCardLimitConfReq, opts ...grpc.CallOption) (*UpdateFlowCardLimitConfResp, error)
	// 删除流量卡限额配置
	DelFlowCardLimitConf(ctx context.Context, in *DelFlowCardLimitConfReq, opts ...grpc.CallOption) (*DelFlowCardLimitConfResp, error)
	// 获取流量卡列表
	GetGrantFlowCardList(ctx context.Context, in *GetGrantFlowCardListReq, opts ...grpc.CallOption) (*GetGrantFlowCardListResp, error)
	// 发放流量卡
	GrantFlowCard(ctx context.Context, in *GrantFlowCardReq, opts ...grpc.CallOption) (*GrantFlowCardResp, error)
	// 批量发放流量卡
	BatGrantFlowCard(ctx context.Context, in *BatGrantFlowCardReq, opts ...grpc.CallOption) (*BatGrantFlowCardResp, error)
	// 回收流量卡
	ReclaimGrantedFlowCard(ctx context.Context, in *ReclaimGrantedFlowCardReq, opts ...grpc.CallOption) (*ReclaimGrantedFlowCardResp, error)
	// 禁用流量卡
	BanGrantedFlowCard(ctx context.Context, in *BanGrantedFlowCardReq, opts ...grpc.CallOption) (*BanGrantedFlowCardResp, error)
	// 获取小时流量卡使用的剩余数量
	GetFlowCardHourRemainCnt(ctx context.Context, in *GetFlowCardHourRemainCntReq, opts ...grpc.CallOption) (*GetFlowCardHourRemainCntResp, error)
	// 主播或者公会直接使用流量卡
	UseFlowCard(ctx context.Context, in *UseFlowCardReq, opts ...grpc.CallOption) (*UseFlowCardResp, error)
	// 公会发放主播流量卡
	GrantAnchorFlowCardByGuild(ctx context.Context, in *GrantAnchorFlowCardByGuildReq, opts ...grpc.CallOption) (*GrantAnchorFlowCardByGuildResp, error)
	// 根据类型获取公会或者主播的流量卡列表, 现在只支持主播的
	GetFlowCardListByType(ctx context.Context, in *GetFlowCardListByTypeReq, opts ...grpc.CallOption) (*GetFlowCardListByTypeResp, error)
	// 查询所有使用流量卡的主播
	GetAllUseFlowCardAnchor(ctx context.Context, in *GetAllUseFlowCardAnchorReq, opts ...grpc.CallOption) (*GetAllUseFlowCardAnchorResp, error)
	// 获取抽奖房间推荐列表
	GetRecLotteryChList(ctx context.Context, in *GetRecLotteryChListReq, opts ...grpc.CallOption) (*GetRecLotteryChListResp, error)
	// 根据id获取抽奖房间推荐信息
	GetLotteryChannelRecInfo(ctx context.Context, in *GetLotteryChannelRecInfoReq, opts ...grpc.CallOption) (*GetLotteryChannelRecInfoResp, error)
	// 获取推荐房间列表
	GetRecommendChannel(ctx context.Context, in *GetRecommendChannelReq, opts ...grpc.CallOption) (*GetRecommendChannelResp, error)
	// 新版推荐房间列表
	GetRecommendChannelV2(ctx context.Context, in *GetRecommendChannelV2Req, opts ...grpc.CallOption) (*GetRecommendChannelV2Resp, error)
	// 根据TAGID-获取推荐房间列表
	GetChannelByTagId(ctx context.Context, in *GetChannelByTagIdReq, opts ...grpc.CallOption) (*GetChannelByTagIdResp, error)
	// 根据个性tagid请求房间数据
	GetRecChListByPerTagId(ctx context.Context, in *GetRecChListByPerTagIdReq, opts ...grpc.CallOption) (*GetRecChListByPerTagIdResp, error)
	// 获取推荐库列表
	GetPrepareChannelList(ctx context.Context, in *GetPrepareChannelListReq, opts ...grpc.CallOption) (*GetPrepareChannelListResp, error)
	// 设置推荐库房间信息
	SetPrepareChannel(ctx context.Context, in *SetPrepareChannelReq, opts ...grpc.CallOption) (*SetPrepareChannelResp, error)
	// 删除推荐库房间信息
	DelPrepareChannel(ctx context.Context, in *DelPrepareChannelReq, opts ...grpc.CallOption) (*DelPrepareChannelResp, error)
	// 获取推荐库备份列表
	GetPrepareBackupList(ctx context.Context, in *GetPrepareBackupListReq, opts ...grpc.CallOption) (*GetPrepareBackupListResp, error)
	// 获取操作记录列表
	GetPrepareOperRecordList(ctx context.Context, in *GetPrepareOperRecordListReq, opts ...grpc.CallOption) (*GetPrepareOperRecordListResp, error)
	// 娱乐tab封面配置
	GetDisplaySceneInfoList(ctx context.Context, in *GetDisplaySceneInfoListReq, opts ...grpc.CallOption) (*GetDisplaySceneInfoListResp, error)
	AddDisplaySceneInfo(ctx context.Context, in *AddDisplaySceneInfoReq, opts ...grpc.CallOption) (*AddDisplaySceneInfoResp, error)
	DelDisplaySceneInfo(ctx context.Context, in *DelDisplaySceneInfoReq, opts ...grpc.CallOption) (*DelDisplaySceneInfoResp, error)
	// 获取娱乐tab封面配置
	GetQuickEntryConfigList(ctx context.Context, in *GetQuickEntryConfigListReq, opts ...grpc.CallOption) (*GetQuickEntryConfigListResp, error)
	// 批量获取直播间的歌手标签
	BatGetChannelSoundLabel(ctx context.Context, in *BatGetChannelSoundLabelReq, opts ...grpc.CallOption) (*BatGetChannelSoundLabelResp, error)
	// 触发定时任务
	TriggerTimer(ctx context.Context, in *TriggerTimerReq, opts ...grpc.CallOption) (*TriggerTimerResp, error)
	// 根据短链中的TagId获取快速进度推荐房间id
	GetQuickRecChannelByTagId(ctx context.Context, in *GetQuickRecChannelByTagIdReq, opts ...grpc.CallOption) (*GetQuickRecChannelByTagIdResp, error)
	// 获取用户roi信息
	GetUserRoiInfo(ctx context.Context, in *GetUserRoiInfoReq, opts ...grpc.CallOption) (*GetUserRoiInfoResp, error)
	// 确认高潜付费用户弹窗显示
	ConfirmRoiHighPotentail(ctx context.Context, in *ConfirmRoiHighPotentailReq, opts ...grpc.CallOption) (*ConfirmRoiHighPotentailResp, error)
	// 房间组资源位配置列表
	// 获取房间组资源位配置列表
	GetChannelGroupResourceList(ctx context.Context, in *GetChannelGroupResourceListReq, opts ...grpc.CallOption) (*GetChannelGroupResourceListResp, error)
	// 增加房间组资源位配置
	AddChannelGroupResource(ctx context.Context, in *AddChannelGroupResourceReq, opts ...grpc.CallOption) (*AddChannelGroupResourceResp, error)
	// 更新房间组资源位配置
	UpdateChannelGroupResource(ctx context.Context, in *UpdateChannelGroupResourceReq, opts ...grpc.CallOption) (*UpdateChannelGroupResourceResp, error)
	// 删除房间组资源位配置
	DelChannelGroupResource(ctx context.Context, in *DelChannelGroupResourceReq, opts ...grpc.CallOption) (*DelChannelGroupResourceResp, error)
	// 检查房间组资源位配置有效性
	CheckChannelGroupResource(ctx context.Context, in *CheckChannelGroupResourceReq, opts ...grpc.CallOption) (*CheckChannelGroupResourceResp, error)
	// 批量获取房间的大礼物信息
	BatchGetChannelBigGiftInfo(ctx context.Context, in *BatchGetChannelBigGiftInfoReq, opts ...grpc.CallOption) (*BatchGetChannelBigGiftInfoResp, error)
	// 设置开关
	SetRevenueSwitchHub(ctx context.Context, in *SetRevenueSwitchHubReq, opts ...grpc.CallOption) (*SetRevenueSwitchHubResp, error)
	// 获取单个用户开关
	GetRevenueSwitchHub(ctx context.Context, in *GetRevenueSwitchHubReq, opts ...grpc.CallOption) (*GetRevenueSwitchHubResp, error)
	// 批量获取用户开关
	BatchGetRevenueSwitchHub(ctx context.Context, in *BatchGetRevenueSwitchHubReq, opts ...grpc.CallOption) (*BatchGetRevenueSwitchHubResp, error)
	// 获取优质用户信息
	GetUserQualityInfo(ctx context.Context, in *GetUserQualityInfoReq, opts ...grpc.CallOption) (*GetUserQualityInfoResp, error)
	// 确认壕用户弹窗显示
	ConfirmQualityHighPop(ctx context.Context, in *ConfirmQualityHighPopReq, opts ...grpc.CallOption) (*ConfirmQualityHighPopResp, error)
	// 获取顶部浮窗房间信息
	GetTopWinChannelInfo(ctx context.Context, in *GetTopWinChannelInfoReq, opts ...grpc.CallOption) (*GetTopWinChannelInfoResp, error)
	// 清掉某个用户的顶部浮窗过滤数据
	CleanTopWinFilter(ctx context.Context, in *CleanTopWinFilterReq, opts ...grpc.CallOption) (*CleanTopWinFilterResp, error)
	// 获取快速进房权重配置列表
	GetQuickWeightConfList(ctx context.Context, in *GetQuickWeightConfListReq, opts ...grpc.CallOption) (*GetQuickWeightConfListResp, error)
	// 获取标签配置
	GetTagConfigInfoList(ctx context.Context, in *GetTagConfigInfoListReq, opts ...grpc.CallOption) (*GetTagConfigInfoListResp, error)
	// 获取快速进房在线房间列表
	GetQuickRecChannelList(ctx context.Context, in *GetQuickRecChannelListReq, opts ...grpc.CallOption) (*GetQuickRecChannelListResp, error)
	// 获取顶部浮窗房间
	GetTopOverLayChannel(ctx context.Context, in *GetTopOverLayChannelReq, opts ...grpc.CallOption) (*GetTopOverLayChannelResp, error)
	// 获取推荐列表房间反馈配置
	GetRecFeedbackConfig(ctx context.Context, in *GetRecFeedbackConfigReq, opts ...grpc.CallOption) (*GetRecFeedbackConfigResp, error)
	// 推荐房间反馈
	DoRecFeedback(ctx context.Context, in *DoRecFeedbackReq, opts ...grpc.CallOption) (*DoRecFeedbackResp, error)
	// 检查用户是否能看到婚礼房
	CheckUidIsInWeddingGroup(ctx context.Context, in *CheckUidIsInWeddingGroupReq, opts ...grpc.CallOption) (*CheckUidIsInWeddingGroupResp, error)
}

type channelRecommendSvrClient struct {
	cc *grpc.ClientConn
}

func NewChannelRecommendSvrClient(cc *grpc.ClientConn) ChannelRecommendSvrClient {
	return &channelRecommendSvrClient{cc}
}

func (c *channelRecommendSvrClient) GetFlowCardLimitConfList(ctx context.Context, in *GetFlowCardLimitConfListReq, opts ...grpc.CallOption) (*GetFlowCardLimitConfListResp, error) {
	out := new(GetFlowCardLimitConfListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetFlowCardLimitConfList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) AddFlowCardLimitConf(ctx context.Context, in *AddFlowCardLimitConfReq, opts ...grpc.CallOption) (*AddFlowCardLimitConfResp, error) {
	out := new(AddFlowCardLimitConfResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/AddFlowCardLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) UpdateFlowCardLimitConf(ctx context.Context, in *UpdateFlowCardLimitConfReq, opts ...grpc.CallOption) (*UpdateFlowCardLimitConfResp, error) {
	out := new(UpdateFlowCardLimitConfResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/UpdateFlowCardLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) DelFlowCardLimitConf(ctx context.Context, in *DelFlowCardLimitConfReq, opts ...grpc.CallOption) (*DelFlowCardLimitConfResp, error) {
	out := new(DelFlowCardLimitConfResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/DelFlowCardLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetGrantFlowCardList(ctx context.Context, in *GetGrantFlowCardListReq, opts ...grpc.CallOption) (*GetGrantFlowCardListResp, error) {
	out := new(GetGrantFlowCardListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetGrantFlowCardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GrantFlowCard(ctx context.Context, in *GrantFlowCardReq, opts ...grpc.CallOption) (*GrantFlowCardResp, error) {
	out := new(GrantFlowCardResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GrantFlowCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) BatGrantFlowCard(ctx context.Context, in *BatGrantFlowCardReq, opts ...grpc.CallOption) (*BatGrantFlowCardResp, error) {
	out := new(BatGrantFlowCardResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/BatGrantFlowCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) ReclaimGrantedFlowCard(ctx context.Context, in *ReclaimGrantedFlowCardReq, opts ...grpc.CallOption) (*ReclaimGrantedFlowCardResp, error) {
	out := new(ReclaimGrantedFlowCardResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/ReclaimGrantedFlowCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) BanGrantedFlowCard(ctx context.Context, in *BanGrantedFlowCardReq, opts ...grpc.CallOption) (*BanGrantedFlowCardResp, error) {
	out := new(BanGrantedFlowCardResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/BanGrantedFlowCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetFlowCardHourRemainCnt(ctx context.Context, in *GetFlowCardHourRemainCntReq, opts ...grpc.CallOption) (*GetFlowCardHourRemainCntResp, error) {
	out := new(GetFlowCardHourRemainCntResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetFlowCardHourRemainCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) UseFlowCard(ctx context.Context, in *UseFlowCardReq, opts ...grpc.CallOption) (*UseFlowCardResp, error) {
	out := new(UseFlowCardResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/UseFlowCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GrantAnchorFlowCardByGuild(ctx context.Context, in *GrantAnchorFlowCardByGuildReq, opts ...grpc.CallOption) (*GrantAnchorFlowCardByGuildResp, error) {
	out := new(GrantAnchorFlowCardByGuildResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GrantAnchorFlowCardByGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetFlowCardListByType(ctx context.Context, in *GetFlowCardListByTypeReq, opts ...grpc.CallOption) (*GetFlowCardListByTypeResp, error) {
	out := new(GetFlowCardListByTypeResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetFlowCardListByType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetAllUseFlowCardAnchor(ctx context.Context, in *GetAllUseFlowCardAnchorReq, opts ...grpc.CallOption) (*GetAllUseFlowCardAnchorResp, error) {
	out := new(GetAllUseFlowCardAnchorResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetAllUseFlowCardAnchor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetRecLotteryChList(ctx context.Context, in *GetRecLotteryChListReq, opts ...grpc.CallOption) (*GetRecLotteryChListResp, error) {
	out := new(GetRecLotteryChListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetRecLotteryChList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetLotteryChannelRecInfo(ctx context.Context, in *GetLotteryChannelRecInfoReq, opts ...grpc.CallOption) (*GetLotteryChannelRecInfoResp, error) {
	out := new(GetLotteryChannelRecInfoResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetLotteryChannelRecInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetRecommendChannel(ctx context.Context, in *GetRecommendChannelReq, opts ...grpc.CallOption) (*GetRecommendChannelResp, error) {
	out := new(GetRecommendChannelResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetRecommendChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetRecommendChannelV2(ctx context.Context, in *GetRecommendChannelV2Req, opts ...grpc.CallOption) (*GetRecommendChannelV2Resp, error) {
	out := new(GetRecommendChannelV2Resp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetRecommendChannelV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetChannelByTagId(ctx context.Context, in *GetChannelByTagIdReq, opts ...grpc.CallOption) (*GetChannelByTagIdResp, error) {
	out := new(GetChannelByTagIdResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetChannelByTagId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetRecChListByPerTagId(ctx context.Context, in *GetRecChListByPerTagIdReq, opts ...grpc.CallOption) (*GetRecChListByPerTagIdResp, error) {
	out := new(GetRecChListByPerTagIdResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetRecChListByPerTagId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetPrepareChannelList(ctx context.Context, in *GetPrepareChannelListReq, opts ...grpc.CallOption) (*GetPrepareChannelListResp, error) {
	out := new(GetPrepareChannelListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetPrepareChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) SetPrepareChannel(ctx context.Context, in *SetPrepareChannelReq, opts ...grpc.CallOption) (*SetPrepareChannelResp, error) {
	out := new(SetPrepareChannelResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/SetPrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) DelPrepareChannel(ctx context.Context, in *DelPrepareChannelReq, opts ...grpc.CallOption) (*DelPrepareChannelResp, error) {
	out := new(DelPrepareChannelResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/DelPrepareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetPrepareBackupList(ctx context.Context, in *GetPrepareBackupListReq, opts ...grpc.CallOption) (*GetPrepareBackupListResp, error) {
	out := new(GetPrepareBackupListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetPrepareBackupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetPrepareOperRecordList(ctx context.Context, in *GetPrepareOperRecordListReq, opts ...grpc.CallOption) (*GetPrepareOperRecordListResp, error) {
	out := new(GetPrepareOperRecordListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetPrepareOperRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetDisplaySceneInfoList(ctx context.Context, in *GetDisplaySceneInfoListReq, opts ...grpc.CallOption) (*GetDisplaySceneInfoListResp, error) {
	out := new(GetDisplaySceneInfoListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetDisplaySceneInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) AddDisplaySceneInfo(ctx context.Context, in *AddDisplaySceneInfoReq, opts ...grpc.CallOption) (*AddDisplaySceneInfoResp, error) {
	out := new(AddDisplaySceneInfoResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/AddDisplaySceneInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) DelDisplaySceneInfo(ctx context.Context, in *DelDisplaySceneInfoReq, opts ...grpc.CallOption) (*DelDisplaySceneInfoResp, error) {
	out := new(DelDisplaySceneInfoResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/DelDisplaySceneInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetQuickEntryConfigList(ctx context.Context, in *GetQuickEntryConfigListReq, opts ...grpc.CallOption) (*GetQuickEntryConfigListResp, error) {
	out := new(GetQuickEntryConfigListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetQuickEntryConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) BatGetChannelSoundLabel(ctx context.Context, in *BatGetChannelSoundLabelReq, opts ...grpc.CallOption) (*BatGetChannelSoundLabelResp, error) {
	out := new(BatGetChannelSoundLabelResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/BatGetChannelSoundLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) TriggerTimer(ctx context.Context, in *TriggerTimerReq, opts ...grpc.CallOption) (*TriggerTimerResp, error) {
	out := new(TriggerTimerResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/TriggerTimer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetQuickRecChannelByTagId(ctx context.Context, in *GetQuickRecChannelByTagIdReq, opts ...grpc.CallOption) (*GetQuickRecChannelByTagIdResp, error) {
	out := new(GetQuickRecChannelByTagIdResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetQuickRecChannelByTagId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetUserRoiInfo(ctx context.Context, in *GetUserRoiInfoReq, opts ...grpc.CallOption) (*GetUserRoiInfoResp, error) {
	out := new(GetUserRoiInfoResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetUserRoiInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) ConfirmRoiHighPotentail(ctx context.Context, in *ConfirmRoiHighPotentailReq, opts ...grpc.CallOption) (*ConfirmRoiHighPotentailResp, error) {
	out := new(ConfirmRoiHighPotentailResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/ConfirmRoiHighPotentail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetChannelGroupResourceList(ctx context.Context, in *GetChannelGroupResourceListReq, opts ...grpc.CallOption) (*GetChannelGroupResourceListResp, error) {
	out := new(GetChannelGroupResourceListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetChannelGroupResourceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) AddChannelGroupResource(ctx context.Context, in *AddChannelGroupResourceReq, opts ...grpc.CallOption) (*AddChannelGroupResourceResp, error) {
	out := new(AddChannelGroupResourceResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/AddChannelGroupResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) UpdateChannelGroupResource(ctx context.Context, in *UpdateChannelGroupResourceReq, opts ...grpc.CallOption) (*UpdateChannelGroupResourceResp, error) {
	out := new(UpdateChannelGroupResourceResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/UpdateChannelGroupResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) DelChannelGroupResource(ctx context.Context, in *DelChannelGroupResourceReq, opts ...grpc.CallOption) (*DelChannelGroupResourceResp, error) {
	out := new(DelChannelGroupResourceResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/DelChannelGroupResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) CheckChannelGroupResource(ctx context.Context, in *CheckChannelGroupResourceReq, opts ...grpc.CallOption) (*CheckChannelGroupResourceResp, error) {
	out := new(CheckChannelGroupResourceResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/CheckChannelGroupResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) BatchGetChannelBigGiftInfo(ctx context.Context, in *BatchGetChannelBigGiftInfoReq, opts ...grpc.CallOption) (*BatchGetChannelBigGiftInfoResp, error) {
	out := new(BatchGetChannelBigGiftInfoResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/BatchGetChannelBigGiftInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) SetRevenueSwitchHub(ctx context.Context, in *SetRevenueSwitchHubReq, opts ...grpc.CallOption) (*SetRevenueSwitchHubResp, error) {
	out := new(SetRevenueSwitchHubResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/SetRevenueSwitchHub", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetRevenueSwitchHub(ctx context.Context, in *GetRevenueSwitchHubReq, opts ...grpc.CallOption) (*GetRevenueSwitchHubResp, error) {
	out := new(GetRevenueSwitchHubResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetRevenueSwitchHub", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) BatchGetRevenueSwitchHub(ctx context.Context, in *BatchGetRevenueSwitchHubReq, opts ...grpc.CallOption) (*BatchGetRevenueSwitchHubResp, error) {
	out := new(BatchGetRevenueSwitchHubResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/BatchGetRevenueSwitchHub", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetUserQualityInfo(ctx context.Context, in *GetUserQualityInfoReq, opts ...grpc.CallOption) (*GetUserQualityInfoResp, error) {
	out := new(GetUserQualityInfoResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetUserQualityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) ConfirmQualityHighPop(ctx context.Context, in *ConfirmQualityHighPopReq, opts ...grpc.CallOption) (*ConfirmQualityHighPopResp, error) {
	out := new(ConfirmQualityHighPopResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/ConfirmQualityHighPop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetTopWinChannelInfo(ctx context.Context, in *GetTopWinChannelInfoReq, opts ...grpc.CallOption) (*GetTopWinChannelInfoResp, error) {
	out := new(GetTopWinChannelInfoResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetTopWinChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) CleanTopWinFilter(ctx context.Context, in *CleanTopWinFilterReq, opts ...grpc.CallOption) (*CleanTopWinFilterResp, error) {
	out := new(CleanTopWinFilterResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/CleanTopWinFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetQuickWeightConfList(ctx context.Context, in *GetQuickWeightConfListReq, opts ...grpc.CallOption) (*GetQuickWeightConfListResp, error) {
	out := new(GetQuickWeightConfListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetQuickWeightConfList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetTagConfigInfoList(ctx context.Context, in *GetTagConfigInfoListReq, opts ...grpc.CallOption) (*GetTagConfigInfoListResp, error) {
	out := new(GetTagConfigInfoListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetTagConfigInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetQuickRecChannelList(ctx context.Context, in *GetQuickRecChannelListReq, opts ...grpc.CallOption) (*GetQuickRecChannelListResp, error) {
	out := new(GetQuickRecChannelListResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetQuickRecChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetTopOverLayChannel(ctx context.Context, in *GetTopOverLayChannelReq, opts ...grpc.CallOption) (*GetTopOverLayChannelResp, error) {
	out := new(GetTopOverLayChannelResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetTopOverLayChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) GetRecFeedbackConfig(ctx context.Context, in *GetRecFeedbackConfigReq, opts ...grpc.CallOption) (*GetRecFeedbackConfigResp, error) {
	out := new(GetRecFeedbackConfigResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/GetRecFeedbackConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) DoRecFeedback(ctx context.Context, in *DoRecFeedbackReq, opts ...grpc.CallOption) (*DoRecFeedbackResp, error) {
	out := new(DoRecFeedbackResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/DoRecFeedback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendSvrClient) CheckUidIsInWeddingGroup(ctx context.Context, in *CheckUidIsInWeddingGroupReq, opts ...grpc.CallOption) (*CheckUidIsInWeddingGroupResp, error) {
	out := new(CheckUidIsInWeddingGroupResp)
	err := c.cc.Invoke(ctx, "/channelrecommend.ChannelRecommendSvr/CheckUidIsInWeddingGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelRecommendSvrServer is the server API for ChannelRecommendSvr service.
type ChannelRecommendSvrServer interface {
	// 获取流量卡限额配置列表
	GetFlowCardLimitConfList(context.Context, *GetFlowCardLimitConfListReq) (*GetFlowCardLimitConfListResp, error)
	// 增加流量卡限额配置
	AddFlowCardLimitConf(context.Context, *AddFlowCardLimitConfReq) (*AddFlowCardLimitConfResp, error)
	// 更新流量卡限额配置
	UpdateFlowCardLimitConf(context.Context, *UpdateFlowCardLimitConfReq) (*UpdateFlowCardLimitConfResp, error)
	// 删除流量卡限额配置
	DelFlowCardLimitConf(context.Context, *DelFlowCardLimitConfReq) (*DelFlowCardLimitConfResp, error)
	// 获取流量卡列表
	GetGrantFlowCardList(context.Context, *GetGrantFlowCardListReq) (*GetGrantFlowCardListResp, error)
	// 发放流量卡
	GrantFlowCard(context.Context, *GrantFlowCardReq) (*GrantFlowCardResp, error)
	// 批量发放流量卡
	BatGrantFlowCard(context.Context, *BatGrantFlowCardReq) (*BatGrantFlowCardResp, error)
	// 回收流量卡
	ReclaimGrantedFlowCard(context.Context, *ReclaimGrantedFlowCardReq) (*ReclaimGrantedFlowCardResp, error)
	// 禁用流量卡
	BanGrantedFlowCard(context.Context, *BanGrantedFlowCardReq) (*BanGrantedFlowCardResp, error)
	// 获取小时流量卡使用的剩余数量
	GetFlowCardHourRemainCnt(context.Context, *GetFlowCardHourRemainCntReq) (*GetFlowCardHourRemainCntResp, error)
	// 主播或者公会直接使用流量卡
	UseFlowCard(context.Context, *UseFlowCardReq) (*UseFlowCardResp, error)
	// 公会发放主播流量卡
	GrantAnchorFlowCardByGuild(context.Context, *GrantAnchorFlowCardByGuildReq) (*GrantAnchorFlowCardByGuildResp, error)
	// 根据类型获取公会或者主播的流量卡列表, 现在只支持主播的
	GetFlowCardListByType(context.Context, *GetFlowCardListByTypeReq) (*GetFlowCardListByTypeResp, error)
	// 查询所有使用流量卡的主播
	GetAllUseFlowCardAnchor(context.Context, *GetAllUseFlowCardAnchorReq) (*GetAllUseFlowCardAnchorResp, error)
	// 获取抽奖房间推荐列表
	GetRecLotteryChList(context.Context, *GetRecLotteryChListReq) (*GetRecLotteryChListResp, error)
	// 根据id获取抽奖房间推荐信息
	GetLotteryChannelRecInfo(context.Context, *GetLotteryChannelRecInfoReq) (*GetLotteryChannelRecInfoResp, error)
	// 获取推荐房间列表
	GetRecommendChannel(context.Context, *GetRecommendChannelReq) (*GetRecommendChannelResp, error)
	// 新版推荐房间列表
	GetRecommendChannelV2(context.Context, *GetRecommendChannelV2Req) (*GetRecommendChannelV2Resp, error)
	// 根据TAGID-获取推荐房间列表
	GetChannelByTagId(context.Context, *GetChannelByTagIdReq) (*GetChannelByTagIdResp, error)
	// 根据个性tagid请求房间数据
	GetRecChListByPerTagId(context.Context, *GetRecChListByPerTagIdReq) (*GetRecChListByPerTagIdResp, error)
	// 获取推荐库列表
	GetPrepareChannelList(context.Context, *GetPrepareChannelListReq) (*GetPrepareChannelListResp, error)
	// 设置推荐库房间信息
	SetPrepareChannel(context.Context, *SetPrepareChannelReq) (*SetPrepareChannelResp, error)
	// 删除推荐库房间信息
	DelPrepareChannel(context.Context, *DelPrepareChannelReq) (*DelPrepareChannelResp, error)
	// 获取推荐库备份列表
	GetPrepareBackupList(context.Context, *GetPrepareBackupListReq) (*GetPrepareBackupListResp, error)
	// 获取操作记录列表
	GetPrepareOperRecordList(context.Context, *GetPrepareOperRecordListReq) (*GetPrepareOperRecordListResp, error)
	// 娱乐tab封面配置
	GetDisplaySceneInfoList(context.Context, *GetDisplaySceneInfoListReq) (*GetDisplaySceneInfoListResp, error)
	AddDisplaySceneInfo(context.Context, *AddDisplaySceneInfoReq) (*AddDisplaySceneInfoResp, error)
	DelDisplaySceneInfo(context.Context, *DelDisplaySceneInfoReq) (*DelDisplaySceneInfoResp, error)
	// 获取娱乐tab封面配置
	GetQuickEntryConfigList(context.Context, *GetQuickEntryConfigListReq) (*GetQuickEntryConfigListResp, error)
	// 批量获取直播间的歌手标签
	BatGetChannelSoundLabel(context.Context, *BatGetChannelSoundLabelReq) (*BatGetChannelSoundLabelResp, error)
	// 触发定时任务
	TriggerTimer(context.Context, *TriggerTimerReq) (*TriggerTimerResp, error)
	// 根据短链中的TagId获取快速进度推荐房间id
	GetQuickRecChannelByTagId(context.Context, *GetQuickRecChannelByTagIdReq) (*GetQuickRecChannelByTagIdResp, error)
	// 获取用户roi信息
	GetUserRoiInfo(context.Context, *GetUserRoiInfoReq) (*GetUserRoiInfoResp, error)
	// 确认高潜付费用户弹窗显示
	ConfirmRoiHighPotentail(context.Context, *ConfirmRoiHighPotentailReq) (*ConfirmRoiHighPotentailResp, error)
	// 房间组资源位配置列表
	// 获取房间组资源位配置列表
	GetChannelGroupResourceList(context.Context, *GetChannelGroupResourceListReq) (*GetChannelGroupResourceListResp, error)
	// 增加房间组资源位配置
	AddChannelGroupResource(context.Context, *AddChannelGroupResourceReq) (*AddChannelGroupResourceResp, error)
	// 更新房间组资源位配置
	UpdateChannelGroupResource(context.Context, *UpdateChannelGroupResourceReq) (*UpdateChannelGroupResourceResp, error)
	// 删除房间组资源位配置
	DelChannelGroupResource(context.Context, *DelChannelGroupResourceReq) (*DelChannelGroupResourceResp, error)
	// 检查房间组资源位配置有效性
	CheckChannelGroupResource(context.Context, *CheckChannelGroupResourceReq) (*CheckChannelGroupResourceResp, error)
	// 批量获取房间的大礼物信息
	BatchGetChannelBigGiftInfo(context.Context, *BatchGetChannelBigGiftInfoReq) (*BatchGetChannelBigGiftInfoResp, error)
	// 设置开关
	SetRevenueSwitchHub(context.Context, *SetRevenueSwitchHubReq) (*SetRevenueSwitchHubResp, error)
	// 获取单个用户开关
	GetRevenueSwitchHub(context.Context, *GetRevenueSwitchHubReq) (*GetRevenueSwitchHubResp, error)
	// 批量获取用户开关
	BatchGetRevenueSwitchHub(context.Context, *BatchGetRevenueSwitchHubReq) (*BatchGetRevenueSwitchHubResp, error)
	// 获取优质用户信息
	GetUserQualityInfo(context.Context, *GetUserQualityInfoReq) (*GetUserQualityInfoResp, error)
	// 确认壕用户弹窗显示
	ConfirmQualityHighPop(context.Context, *ConfirmQualityHighPopReq) (*ConfirmQualityHighPopResp, error)
	// 获取顶部浮窗房间信息
	GetTopWinChannelInfo(context.Context, *GetTopWinChannelInfoReq) (*GetTopWinChannelInfoResp, error)
	// 清掉某个用户的顶部浮窗过滤数据
	CleanTopWinFilter(context.Context, *CleanTopWinFilterReq) (*CleanTopWinFilterResp, error)
	// 获取快速进房权重配置列表
	GetQuickWeightConfList(context.Context, *GetQuickWeightConfListReq) (*GetQuickWeightConfListResp, error)
	// 获取标签配置
	GetTagConfigInfoList(context.Context, *GetTagConfigInfoListReq) (*GetTagConfigInfoListResp, error)
	// 获取快速进房在线房间列表
	GetQuickRecChannelList(context.Context, *GetQuickRecChannelListReq) (*GetQuickRecChannelListResp, error)
	// 获取顶部浮窗房间
	GetTopOverLayChannel(context.Context, *GetTopOverLayChannelReq) (*GetTopOverLayChannelResp, error)
	// 获取推荐列表房间反馈配置
	GetRecFeedbackConfig(context.Context, *GetRecFeedbackConfigReq) (*GetRecFeedbackConfigResp, error)
	// 推荐房间反馈
	DoRecFeedback(context.Context, *DoRecFeedbackReq) (*DoRecFeedbackResp, error)
	// 检查用户是否能看到婚礼房
	CheckUidIsInWeddingGroup(context.Context, *CheckUidIsInWeddingGroupReq) (*CheckUidIsInWeddingGroupResp, error)
}

func RegisterChannelRecommendSvrServer(s *grpc.Server, srv ChannelRecommendSvrServer) {
	s.RegisterService(&_ChannelRecommendSvr_serviceDesc, srv)
}

func _ChannelRecommendSvr_GetFlowCardLimitConfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFlowCardLimitConfListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetFlowCardLimitConfList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetFlowCardLimitConfList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetFlowCardLimitConfList(ctx, req.(*GetFlowCardLimitConfListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_AddFlowCardLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFlowCardLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).AddFlowCardLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/AddFlowCardLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).AddFlowCardLimitConf(ctx, req.(*AddFlowCardLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_UpdateFlowCardLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFlowCardLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).UpdateFlowCardLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/UpdateFlowCardLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).UpdateFlowCardLimitConf(ctx, req.(*UpdateFlowCardLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_DelFlowCardLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFlowCardLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).DelFlowCardLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/DelFlowCardLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).DelFlowCardLimitConf(ctx, req.(*DelFlowCardLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetGrantFlowCardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGrantFlowCardListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetGrantFlowCardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetGrantFlowCardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetGrantFlowCardList(ctx, req.(*GetGrantFlowCardListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GrantFlowCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantFlowCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GrantFlowCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GrantFlowCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GrantFlowCard(ctx, req.(*GrantFlowCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_BatGrantFlowCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGrantFlowCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).BatGrantFlowCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/BatGrantFlowCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).BatGrantFlowCard(ctx, req.(*BatGrantFlowCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_ReclaimGrantedFlowCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReclaimGrantedFlowCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).ReclaimGrantedFlowCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/ReclaimGrantedFlowCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).ReclaimGrantedFlowCard(ctx, req.(*ReclaimGrantedFlowCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_BanGrantedFlowCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanGrantedFlowCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).BanGrantedFlowCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/BanGrantedFlowCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).BanGrantedFlowCard(ctx, req.(*BanGrantedFlowCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetFlowCardHourRemainCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFlowCardHourRemainCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetFlowCardHourRemainCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetFlowCardHourRemainCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetFlowCardHourRemainCnt(ctx, req.(*GetFlowCardHourRemainCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_UseFlowCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UseFlowCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).UseFlowCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/UseFlowCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).UseFlowCard(ctx, req.(*UseFlowCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GrantAnchorFlowCardByGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantAnchorFlowCardByGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GrantAnchorFlowCardByGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GrantAnchorFlowCardByGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GrantAnchorFlowCardByGuild(ctx, req.(*GrantAnchorFlowCardByGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetFlowCardListByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFlowCardListByTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetFlowCardListByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetFlowCardListByType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetFlowCardListByType(ctx, req.(*GetFlowCardListByTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetAllUseFlowCardAnchor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllUseFlowCardAnchorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetAllUseFlowCardAnchor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetAllUseFlowCardAnchor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetAllUseFlowCardAnchor(ctx, req.(*GetAllUseFlowCardAnchorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetRecLotteryChList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecLotteryChListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetRecLotteryChList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetRecLotteryChList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetRecLotteryChList(ctx, req.(*GetRecLotteryChListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetLotteryChannelRecInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotteryChannelRecInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetLotteryChannelRecInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetLotteryChannelRecInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetLotteryChannelRecInfo(ctx, req.(*GetLotteryChannelRecInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetRecommendChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetRecommendChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetRecommendChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetRecommendChannel(ctx, req.(*GetRecommendChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetRecommendChannelV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendChannelV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetRecommendChannelV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetRecommendChannelV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetRecommendChannelV2(ctx, req.(*GetRecommendChannelV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetChannelByTagId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelByTagIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetChannelByTagId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetChannelByTagId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetChannelByTagId(ctx, req.(*GetChannelByTagIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetRecChListByPerTagId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecChListByPerTagIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetRecChListByPerTagId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetRecChListByPerTagId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetRecChListByPerTagId(ctx, req.(*GetRecChListByPerTagIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetPrepareChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepareChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetPrepareChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetPrepareChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetPrepareChannelList(ctx, req.(*GetPrepareChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_SetPrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).SetPrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/SetPrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).SetPrepareChannel(ctx, req.(*SetPrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_DelPrepareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPrepareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).DelPrepareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/DelPrepareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).DelPrepareChannel(ctx, req.(*DelPrepareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetPrepareBackupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepareBackupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetPrepareBackupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetPrepareBackupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetPrepareBackupList(ctx, req.(*GetPrepareBackupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetPrepareOperRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepareOperRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetPrepareOperRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetPrepareOperRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetPrepareOperRecordList(ctx, req.(*GetPrepareOperRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetDisplaySceneInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDisplaySceneInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetDisplaySceneInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetDisplaySceneInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetDisplaySceneInfoList(ctx, req.(*GetDisplaySceneInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_AddDisplaySceneInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDisplaySceneInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).AddDisplaySceneInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/AddDisplaySceneInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).AddDisplaySceneInfo(ctx, req.(*AddDisplaySceneInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_DelDisplaySceneInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelDisplaySceneInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).DelDisplaySceneInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/DelDisplaySceneInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).DelDisplaySceneInfo(ctx, req.(*DelDisplaySceneInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetQuickEntryConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickEntryConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetQuickEntryConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetQuickEntryConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetQuickEntryConfigList(ctx, req.(*GetQuickEntryConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_BatGetChannelSoundLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetChannelSoundLabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).BatGetChannelSoundLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/BatGetChannelSoundLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).BatGetChannelSoundLabel(ctx, req.(*BatGetChannelSoundLabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_TriggerTimer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerTimerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).TriggerTimer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/TriggerTimer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).TriggerTimer(ctx, req.(*TriggerTimerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetQuickRecChannelByTagId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickRecChannelByTagIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetQuickRecChannelByTagId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetQuickRecChannelByTagId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetQuickRecChannelByTagId(ctx, req.(*GetQuickRecChannelByTagIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetUserRoiInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRoiInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetUserRoiInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetUserRoiInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetUserRoiInfo(ctx, req.(*GetUserRoiInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_ConfirmRoiHighPotentail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmRoiHighPotentailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).ConfirmRoiHighPotentail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/ConfirmRoiHighPotentail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).ConfirmRoiHighPotentail(ctx, req.(*ConfirmRoiHighPotentailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetChannelGroupResourceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelGroupResourceListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetChannelGroupResourceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetChannelGroupResourceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetChannelGroupResourceList(ctx, req.(*GetChannelGroupResourceListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_AddChannelGroupResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelGroupResourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).AddChannelGroupResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/AddChannelGroupResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).AddChannelGroupResource(ctx, req.(*AddChannelGroupResourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_UpdateChannelGroupResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChannelGroupResourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).UpdateChannelGroupResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/UpdateChannelGroupResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).UpdateChannelGroupResource(ctx, req.(*UpdateChannelGroupResourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_DelChannelGroupResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelChannelGroupResourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).DelChannelGroupResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/DelChannelGroupResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).DelChannelGroupResource(ctx, req.(*DelChannelGroupResourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_CheckChannelGroupResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckChannelGroupResourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).CheckChannelGroupResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/CheckChannelGroupResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).CheckChannelGroupResource(ctx, req.(*CheckChannelGroupResourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_BatchGetChannelBigGiftInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelBigGiftInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).BatchGetChannelBigGiftInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/BatchGetChannelBigGiftInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).BatchGetChannelBigGiftInfo(ctx, req.(*BatchGetChannelBigGiftInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_SetRevenueSwitchHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRevenueSwitchHubReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).SetRevenueSwitchHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/SetRevenueSwitchHub",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).SetRevenueSwitchHub(ctx, req.(*SetRevenueSwitchHubReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetRevenueSwitchHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRevenueSwitchHubReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetRevenueSwitchHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetRevenueSwitchHub",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetRevenueSwitchHub(ctx, req.(*GetRevenueSwitchHubReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_BatchGetRevenueSwitchHub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRevenueSwitchHubReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).BatchGetRevenueSwitchHub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/BatchGetRevenueSwitchHub",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).BatchGetRevenueSwitchHub(ctx, req.(*BatchGetRevenueSwitchHubReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetUserQualityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserQualityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetUserQualityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetUserQualityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetUserQualityInfo(ctx, req.(*GetUserQualityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_ConfirmQualityHighPop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmQualityHighPopReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).ConfirmQualityHighPop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/ConfirmQualityHighPop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).ConfirmQualityHighPop(ctx, req.(*ConfirmQualityHighPopReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetTopWinChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopWinChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetTopWinChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetTopWinChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetTopWinChannelInfo(ctx, req.(*GetTopWinChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_CleanTopWinFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanTopWinFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).CleanTopWinFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/CleanTopWinFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).CleanTopWinFilter(ctx, req.(*CleanTopWinFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetQuickWeightConfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickWeightConfListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetQuickWeightConfList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetQuickWeightConfList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetQuickWeightConfList(ctx, req.(*GetQuickWeightConfListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetTagConfigInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTagConfigInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetTagConfigInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetTagConfigInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetTagConfigInfoList(ctx, req.(*GetTagConfigInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetQuickRecChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickRecChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetQuickRecChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetQuickRecChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetQuickRecChannelList(ctx, req.(*GetQuickRecChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetTopOverLayChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopOverLayChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetTopOverLayChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetTopOverLayChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetTopOverLayChannel(ctx, req.(*GetTopOverLayChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_GetRecFeedbackConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecFeedbackConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).GetRecFeedbackConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/GetRecFeedbackConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).GetRecFeedbackConfig(ctx, req.(*GetRecFeedbackConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_DoRecFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoRecFeedbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).DoRecFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/DoRecFeedback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).DoRecFeedback(ctx, req.(*DoRecFeedbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommendSvr_CheckUidIsInWeddingGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUidIsInWeddingGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendSvrServer).CheckUidIsInWeddingGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommendSvr/CheckUidIsInWeddingGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendSvrServer).CheckUidIsInWeddingGroup(ctx, req.(*CheckUidIsInWeddingGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelRecommendSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelrecommend.ChannelRecommendSvr",
	HandlerType: (*ChannelRecommendSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFlowCardLimitConfList",
			Handler:    _ChannelRecommendSvr_GetFlowCardLimitConfList_Handler,
		},
		{
			MethodName: "AddFlowCardLimitConf",
			Handler:    _ChannelRecommendSvr_AddFlowCardLimitConf_Handler,
		},
		{
			MethodName: "UpdateFlowCardLimitConf",
			Handler:    _ChannelRecommendSvr_UpdateFlowCardLimitConf_Handler,
		},
		{
			MethodName: "DelFlowCardLimitConf",
			Handler:    _ChannelRecommendSvr_DelFlowCardLimitConf_Handler,
		},
		{
			MethodName: "GetGrantFlowCardList",
			Handler:    _ChannelRecommendSvr_GetGrantFlowCardList_Handler,
		},
		{
			MethodName: "GrantFlowCard",
			Handler:    _ChannelRecommendSvr_GrantFlowCard_Handler,
		},
		{
			MethodName: "BatGrantFlowCard",
			Handler:    _ChannelRecommendSvr_BatGrantFlowCard_Handler,
		},
		{
			MethodName: "ReclaimGrantedFlowCard",
			Handler:    _ChannelRecommendSvr_ReclaimGrantedFlowCard_Handler,
		},
		{
			MethodName: "BanGrantedFlowCard",
			Handler:    _ChannelRecommendSvr_BanGrantedFlowCard_Handler,
		},
		{
			MethodName: "GetFlowCardHourRemainCnt",
			Handler:    _ChannelRecommendSvr_GetFlowCardHourRemainCnt_Handler,
		},
		{
			MethodName: "UseFlowCard",
			Handler:    _ChannelRecommendSvr_UseFlowCard_Handler,
		},
		{
			MethodName: "GrantAnchorFlowCardByGuild",
			Handler:    _ChannelRecommendSvr_GrantAnchorFlowCardByGuild_Handler,
		},
		{
			MethodName: "GetFlowCardListByType",
			Handler:    _ChannelRecommendSvr_GetFlowCardListByType_Handler,
		},
		{
			MethodName: "GetAllUseFlowCardAnchor",
			Handler:    _ChannelRecommendSvr_GetAllUseFlowCardAnchor_Handler,
		},
		{
			MethodName: "GetRecLotteryChList",
			Handler:    _ChannelRecommendSvr_GetRecLotteryChList_Handler,
		},
		{
			MethodName: "GetLotteryChannelRecInfo",
			Handler:    _ChannelRecommendSvr_GetLotteryChannelRecInfo_Handler,
		},
		{
			MethodName: "GetRecommendChannel",
			Handler:    _ChannelRecommendSvr_GetRecommendChannel_Handler,
		},
		{
			MethodName: "GetRecommendChannelV2",
			Handler:    _ChannelRecommendSvr_GetRecommendChannelV2_Handler,
		},
		{
			MethodName: "GetChannelByTagId",
			Handler:    _ChannelRecommendSvr_GetChannelByTagId_Handler,
		},
		{
			MethodName: "GetRecChListByPerTagId",
			Handler:    _ChannelRecommendSvr_GetRecChListByPerTagId_Handler,
		},
		{
			MethodName: "GetPrepareChannelList",
			Handler:    _ChannelRecommendSvr_GetPrepareChannelList_Handler,
		},
		{
			MethodName: "SetPrepareChannel",
			Handler:    _ChannelRecommendSvr_SetPrepareChannel_Handler,
		},
		{
			MethodName: "DelPrepareChannel",
			Handler:    _ChannelRecommendSvr_DelPrepareChannel_Handler,
		},
		{
			MethodName: "GetPrepareBackupList",
			Handler:    _ChannelRecommendSvr_GetPrepareBackupList_Handler,
		},
		{
			MethodName: "GetPrepareOperRecordList",
			Handler:    _ChannelRecommendSvr_GetPrepareOperRecordList_Handler,
		},
		{
			MethodName: "GetDisplaySceneInfoList",
			Handler:    _ChannelRecommendSvr_GetDisplaySceneInfoList_Handler,
		},
		{
			MethodName: "AddDisplaySceneInfo",
			Handler:    _ChannelRecommendSvr_AddDisplaySceneInfo_Handler,
		},
		{
			MethodName: "DelDisplaySceneInfo",
			Handler:    _ChannelRecommendSvr_DelDisplaySceneInfo_Handler,
		},
		{
			MethodName: "GetQuickEntryConfigList",
			Handler:    _ChannelRecommendSvr_GetQuickEntryConfigList_Handler,
		},
		{
			MethodName: "BatGetChannelSoundLabel",
			Handler:    _ChannelRecommendSvr_BatGetChannelSoundLabel_Handler,
		},
		{
			MethodName: "TriggerTimer",
			Handler:    _ChannelRecommendSvr_TriggerTimer_Handler,
		},
		{
			MethodName: "GetQuickRecChannelByTagId",
			Handler:    _ChannelRecommendSvr_GetQuickRecChannelByTagId_Handler,
		},
		{
			MethodName: "GetUserRoiInfo",
			Handler:    _ChannelRecommendSvr_GetUserRoiInfo_Handler,
		},
		{
			MethodName: "ConfirmRoiHighPotentail",
			Handler:    _ChannelRecommendSvr_ConfirmRoiHighPotentail_Handler,
		},
		{
			MethodName: "GetChannelGroupResourceList",
			Handler:    _ChannelRecommendSvr_GetChannelGroupResourceList_Handler,
		},
		{
			MethodName: "AddChannelGroupResource",
			Handler:    _ChannelRecommendSvr_AddChannelGroupResource_Handler,
		},
		{
			MethodName: "UpdateChannelGroupResource",
			Handler:    _ChannelRecommendSvr_UpdateChannelGroupResource_Handler,
		},
		{
			MethodName: "DelChannelGroupResource",
			Handler:    _ChannelRecommendSvr_DelChannelGroupResource_Handler,
		},
		{
			MethodName: "CheckChannelGroupResource",
			Handler:    _ChannelRecommendSvr_CheckChannelGroupResource_Handler,
		},
		{
			MethodName: "BatchGetChannelBigGiftInfo",
			Handler:    _ChannelRecommendSvr_BatchGetChannelBigGiftInfo_Handler,
		},
		{
			MethodName: "SetRevenueSwitchHub",
			Handler:    _ChannelRecommendSvr_SetRevenueSwitchHub_Handler,
		},
		{
			MethodName: "GetRevenueSwitchHub",
			Handler:    _ChannelRecommendSvr_GetRevenueSwitchHub_Handler,
		},
		{
			MethodName: "BatchGetRevenueSwitchHub",
			Handler:    _ChannelRecommendSvr_BatchGetRevenueSwitchHub_Handler,
		},
		{
			MethodName: "GetUserQualityInfo",
			Handler:    _ChannelRecommendSvr_GetUserQualityInfo_Handler,
		},
		{
			MethodName: "ConfirmQualityHighPop",
			Handler:    _ChannelRecommendSvr_ConfirmQualityHighPop_Handler,
		},
		{
			MethodName: "GetTopWinChannelInfo",
			Handler:    _ChannelRecommendSvr_GetTopWinChannelInfo_Handler,
		},
		{
			MethodName: "CleanTopWinFilter",
			Handler:    _ChannelRecommendSvr_CleanTopWinFilter_Handler,
		},
		{
			MethodName: "GetQuickWeightConfList",
			Handler:    _ChannelRecommendSvr_GetQuickWeightConfList_Handler,
		},
		{
			MethodName: "GetTagConfigInfoList",
			Handler:    _ChannelRecommendSvr_GetTagConfigInfoList_Handler,
		},
		{
			MethodName: "GetQuickRecChannelList",
			Handler:    _ChannelRecommendSvr_GetQuickRecChannelList_Handler,
		},
		{
			MethodName: "GetTopOverLayChannel",
			Handler:    _ChannelRecommendSvr_GetTopOverLayChannel_Handler,
		},
		{
			MethodName: "GetRecFeedbackConfig",
			Handler:    _ChannelRecommendSvr_GetRecFeedbackConfig_Handler,
		},
		{
			MethodName: "DoRecFeedback",
			Handler:    _ChannelRecommendSvr_DoRecFeedback_Handler,
		},
		{
			MethodName: "CheckUidIsInWeddingGroup",
			Handler:    _ChannelRecommendSvr_CheckUidIsInWeddingGroup_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-recommend-svr/channel-recommend-svr.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-recommend-svr/channel-recommend-svr.proto", fileDescriptor_channel_recommend_svr_9183e74027c33df8)
}

var fileDescriptor_channel_recommend_svr_9183e74027c33df8 = []byte{
	// 5619 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x3c, 0x4d, 0x73, 0x1c, 0xd7,
	0x56, 0xea, 0xd1, 0xd7, 0xcc, 0x19, 0xc9, 0x6a, 0xb7, 0x65, 0x59, 0x1a, 0x59, 0xfe, 0xe8, 0x24,
	0xb6, 0x23, 0xdb, 0xf2, 0xc3, 0x21, 0x38, 0x2f, 0x79, 0x2f, 0x44, 0x92, 0x65, 0x59, 0x41, 0x92,
	0x95, 0x96, 0x64, 0xbf, 0x17, 0x92, 0xd7, 0xb4, 0xba, 0xaf, 0x47, 0x1d, 0xf7, 0x74, 0xb7, 0xfb,
	0xf6, 0x48, 0x56, 0x52, 0x7c, 0xa4, 0xc8, 0x92, 0x57, 0x50, 0x54, 0x01, 0x05, 0x45, 0x15, 0x2c,
	0x28, 0x56, 0xb0, 0x60, 0xc1, 0x8a, 0x0d, 0x54, 0xb1, 0xa3, 0x80, 0x15, 0x7f, 0x00, 0x8a, 0x1f,
	0xc0, 0x8e, 0x2a, 0x36, 0xd4, 0xfd, 0xe8, 0x9e, 0xdb, 0xdd, 0xf7, 0x8e, 0x46, 0x7e, 0x49, 0x56,
	0x9a, 0x3e, 0xf7, 0xe3, 0x7c, 0xdc, 0x73, 0xce, 0x3d, 0xf7, 0xdc, 0x73, 0x05, 0x1f, 0xa5, 0xe9,
	0xbd, 0x97, 0x5d, 0xdf, 0x7d, 0x81, 0xfd, 0xe0, 0x08, 0x25, 0xf7, 0xdc, 0x43, 0x27, 0x0c, 0x51,
	0x70, 0x37, 0x41, 0x6e, 0xd4, 0xe9, 0xa0, 0xd0, 0xbb, 0x8b, 0x8f, 0x14, 0xd0, 0xa5, 0x38, 0x89,
	0xd2, 0xc8, 0xd0, 0x79, 0x63, 0xde, 0x66, 0xbe, 0x03, 0x8d, 0x4d, 0x74, 0x84, 0x82, 0xd5, 0x28,
	0x7c, 0x6e, 0x4c, 0xc3, 0x68, 0x40, 0x3e, 0x66, 0xb5, 0x6b, 0xda, 0xad, 0x49, 0x8b, 0x7d, 0x18,
	0x3a, 0x0c, 0xbb, 0x61, 0x3a, 0x5b, 0xa3, 0x30, 0xf2, 0xd3, 0xb4, 0xa1, 0xb1, 0xe9, 0x77, 0xfc,
	0x94, 0x0e, 0x9a, 0x85, 0xf1, 0xc3, 0xa8, 0x9b, 0xac, 0x86, 0x29, 0x1f, 0x96, 0x7d, 0x1a, 0xef,
	0x41, 0xc3, 0x8d, 0xc2, 0xe7, 0x76, 0xe0, 0x63, 0x32, 0x7c, 0xf8, 0x56, 0xf3, 0xfe, 0xfc, 0x52,
	0x99, 0x82, 0xa5, 0x1c, 0xbd, 0x55, 0x27, 0xbd, 0x37, 0x7d, 0x9c, 0x9a, 0xbf, 0xaf, 0xc1, 0xf9,
	0x47, 0x41, 0x74, 0xbc, 0xea, 0x24, 0x5e, 0x0f, 0xd3, 0x39, 0xa8, 0xf9, 0x1e, 0x47, 0x52, 0xf3,
	0x3d, 0x63, 0x0e, 0xea, 0x07, 0xa8, 0xed, 0x87, 0x76, 0x8a, 0x39, 0x75, 0xe3, 0xf4, 0x7b, 0x0f,
	0x1b, 0x17, 0x61, 0x0c, 0x85, 0x1e, 0x69, 0x18, 0x66, 0xac, 0xa0, 0xd0, 0xdb, 0xc3, 0x45, 0x8a,
	0x46, 0x94, 0x14, 0x65, 0x18, 0x05, 0x8a, 0x7e, 0x47, 0x83, 0xf9, 0x75, 0x94, 0x56, 0x88, 0x22,
	0x8d, 0x16, 0x7a, 0x69, 0x18, 0x30, 0x12, 0x3b, 0x6d, 0xc4, 0xa9, 0xa3, 0xbf, 0x8d, 0x79, 0x68,
	0x90, 0xbf, 0x36, 0xf6, 0xbf, 0x44, 0x9c, 0xc0, 0x3a, 0x01, 0xec, 0xfa, 0x5f, 0xa2, 0x02, 0xf1,
	0xc3, 0x2a, 0xe2, 0x47, 0x04, 0xe2, 0xcd, 0x3f, 0xd3, 0xe0, 0xb2, 0x9a, 0x04, 0x1c, 0x1b, 0x1f,
	0x89, 0xdc, 0x69, 0x94, 0xbb, 0x37, 0xaa, 0xdc, 0x55, 0xc6, 0xf7, 0xb8, 0x24, 0x14, 0x87, 0xe8,
	0x55, 0x6a, 0x53, 0x56, 0x38, 0xc5, 0x04, 0xb0, 0xc3, 0xd9, 0x49, 0xa3, 0xd4, 0x09, 0x6c, 0xa2,
	0x0d, 0x8c, 0xe4, 0x3a, 0x05, 0xac, 0x86, 0xa9, 0x69, 0xc1, 0xa5, 0x65, 0xcf, 0xab, 0xce, 0x8d,
	0x5e, 0x1a, 0x0f, 0x60, 0x84, 0x20, 0xa0, 0xa2, 0x19, 0x90, 0x22, 0x3a, 0xc0, 0x6c, 0xc1, 0xac,
	0x7c, 0x4e, 0x1c, 0x9b, 0xfb, 0xd0, 0xda, 0x8f, 0x3d, 0x27, 0x45, 0xdf, 0x2e, 0xca, 0x05, 0x98,
	0x57, 0x4e, 0x8b, 0x63, 0xf3, 0x6d, 0xb8, 0xf4, 0x10, 0x05, 0x52, 0x94, 0x25, 0xe5, 0x24, 0xc4,
	0xcb, 0xbb, 0xe2, 0xd8, 0xfc, 0x9f, 0x5a, 0x4f, 0xbd, 0xd7, 0x13, 0x27, 0x4c, 0x37, 0xc2, 0xe7,
	0x11, 0xd1, 0x88, 0x36, 0xf9, 0xb0, 0xf3, 0x79, 0xc6, 0xe9, 0xf7, 0x86, 0xc7, 0x27, 0xaf, 0xe5,
	0x9a, 0x9f, 0x1b, 0xea, 0xb0, 0x68, 0xa8, 0xf3, 0xd0, 0x40, 0xaf, 0x62, 0x3f, 0x49, 0x7b, 0xaa,
	0x53, 0x67, 0x80, 0x3d, 0x9c, 0x59, 0xf1, 0x68, 0x6e, 0xc5, 0xc6, 0x0c, 0x8c, 0x25, 0xa8, 0xe3,
	0x24, 0x2f, 0x66, 0xc7, 0xae, 0x69, 0xb7, 0x1a, 0x16, 0xff, 0x32, 0xae, 0xc1, 0xc4, 0x81, 0x13,
	0xda, 0xb9, 0x76, 0x8e, 0xd3, 0x21, 0x70, 0xe0, 0x84, 0x2b, 0x5c, 0x41, 0x2f, 0x03, 0xf9, 0xb2,
	0xb9, 0x92, 0xd6, 0x19, 0xa6, 0x03, 0x27, 0x5c, 0xa3, 0x46, 0x36, 0x07, 0xf5, 0x2e, 0x46, 0x1e,
	0x55, 0x93, 0x06, 0xe3, 0x83, 0x7c, 0x13, 0x8f, 0x30, 0x0f, 0x8d, 0x2e, 0x15, 0x2f, 0x19, 0x07,
	0x6c, 0x1c, 0x03, 0xec, 0x61, 0xd2, 0xe8, 0x26, 0x88, 0x37, 0x36, 0x59, 0x23, 0x03, 0xec, 0x61,
	0xe3, 0x4d, 0x38, 0xd7, 0xee, 0xfa, 0x81, 0x67, 0xe7, 0x22, 0x9a, 0xa0, 0x3d, 0x26, 0x28, 0x74,
	0x9d, 0xcb, 0x69, 0x0e, 0xea, 0x51, 0xe2, 0xa1, 0x84, 0xb4, 0x4f, 0x52, 0xa6, 0xc6, 0xe9, 0xf7,
	0x86, 0x67, 0xfe, 0xbd, 0x06, 0x97, 0xd6, 0x51, 0x4a, 0x7b, 0xf6, 0x56, 0x85, 0x19, 0xef, 0x02,
	0x00, 0x9b, 0x36, 0x3d, 0x89, 0x33, 0x13, 0x6e, 0x50, 0xc8, 0xde, 0x49, 0x8c, 0x2a, 0xd2, 0xcf,
	0x6c, 0x7d, 0x58, 0x65, 0xeb, 0x23, 0x25, 0x5b, 0xaf, 0x12, 0x3f, 0x2a, 0x21, 0x3e, 0x5f, 0xd4,
	0x31, 0x61, 0x51, 0xcd, 0x3f, 0xd6, 0x60, 0x56, 0x4e, 0x37, 0xb3, 0x78, 0x3f, 0x7c, 0x1e, 0x0d,
	0x68, 0xf1, 0xb9, 0xaa, 0x59, 0x75, 0x32, 0xea, 0x17, 0xb4, 0xf8, 0x2f, 0x40, 0x2f, 0x10, 0x35,
	0x80, 0x20, 0x1f, 0xc0, 0x08, 0x41, 0x4c, 0xf1, 0x0c, 0x48, 0x29, 0x1d, 0x60, 0x5e, 0x80, 0xf3,
	0x25, 0x5c, 0x38, 0x36, 0x43, 0x98, 0xa4, 0xc0, 0xb5, 0x24, 0x89, 0x92, 0x2d, 0xdc, 0x2e, 0xac,
	0xbe, 0x56, 0x58, 0x7d, 0xd2, 0x84, 0x92, 0xc4, 0x76, 0x23, 0x8f, 0x71, 0x39, 0x6a, 0x8d, 0xa3,
	0x24, 0x59, 0x8d, 0x3c, 0x64, 0x5c, 0x02, 0xf2, 0xd3, 0xee, 0xe0, 0x36, 0x65, 0xb1, 0x61, 0x8d,
	0xa1, 0x84, 0x4e, 0xc7, 0x96, 0x7d, 0x24, 0xb7, 0xe8, 0x23, 0xb8, 0xb0, 0xe2, 0xa4, 0x67, 0xe5,
	0xb9, 0xb0, 0x44, 0xb5, 0xd7, 0x58, 0x22, 0xd3, 0x82, 0xe9, 0x2a, 0x5e, 0x1c, 0x1b, 0xef, 0x33,
	0x9e, 0x84, 0xb5, 0xbf, 0x5a, 0x9d, 0xb8, 0x20, 0x21, 0xca, 0x34, 0x9d, 0xf3, 0x08, 0xe6, 0x2c,
	0xe4, 0x06, 0x8e, 0xdf, 0xa1, 0x1d, 0x90, 0x77, 0x06, 0x8e, 0x44, 0x3f, 0x55, 0x2b, 0xfa, 0xa9,
	0xab, 0xd0, 0x4c, 0xd8, 0xb4, 0x82, 0xca, 0x00, 0x07, 0x11, 0xa5, 0xb9, 0x0c, 0x2d, 0x15, 0x5e,
	0x1c, 0x9b, 0xbf, 0xab, 0xc1, 0xc5, 0x15, 0x27, 0xfc, 0x56, 0x49, 0x3a, 0xfb, 0x3e, 0x3b, 0x0b,
	0x33, 0x32, 0x22, 0x70, 0x6c, 0xae, 0x16, 0x62, 0x80, 0xc7, 0x51, 0x37, 0xb1, 0x50, 0xc7, 0xf1,
	0xc3, 0xd5, 0x30, 0xe5, 0x5b, 0x40, 0x8a, 0xb3, 0x2d, 0x20, 0xc5, 0x3d, 0x83, 0xae, 0x89, 0x06,
	0xfd, 0xe3, 0xc2, 0x2e, 0x5e, 0x9a, 0x04, 0xc7, 0x84, 0xd5, 0x84, 0x02, 0xa8, 0x08, 0x39, 0xab,
	0x49, 0xd6, 0x85, 0x04, 0x22, 0xe7, 0xf6, 0x31, 0x12, 0x85, 0xd3, 0x67, 0xe3, 0x58, 0x00, 0x70,
	0x42, 0xf7, 0x30, 0x4a, 0xec, 0x6e, 0x2e, 0x9a, 0x06, 0x83, 0xec, 0xfb, 0x7d, 0x85, 0x43, 0x26,
	0xa5, 0x3e, 0x2b, 0xb7, 0x81, 0x71, 0xfa, 0xbd, 0xe1, 0x99, 0xe7, 0x61, 0xaa, 0x40, 0x01, 0x8e,
	0xcd, 0x3f, 0xd1, 0x60, 0x81, 0x4a, 0x6c, 0x99, 0xce, 0x9d, 0xb5, 0xad, 0x9c, 0xac, 0x93, 0x31,
	0x19, 0x91, 0xd9, 0x7c, 0x5a, 0x61, 0xbe, 0x7e, 0xab, 0x57, 0xb0, 0x9e, 0xe1, 0xd7, 0xb1, 0x9e,
	0xcf, 0xe0, 0x4a, 0x3f, 0xc2, 0x7e, 0x41, 0x3b, 0xfa, 0x39, 0xf3, 0xce, 0xa2, 0x63, 0x5e, 0x39,
	0x21, 0x2a, 0x39, 0x80, 0xd2, 0x1a, 0x30, 0x42, 0x1b, 0x18, 0xcb, 0xf4, 0xb7, 0x62, 0x63, 0x2f,
	0x7b, 0xa2, 0x59, 0x18, 0x5f, 0x47, 0x6c, 0x92, 0x49, 0x68, 0x6c, 0x47, 0xe9, 0x3e, 0x46, 0xcb,
	0x41, 0xa0, 0x0f, 0x99, 0x9f, 0xc3, 0x9c, 0x82, 0x9c, 0x6f, 0x63, 0xb7, 0x30, 0xef, 0x40, 0x6b,
	0x1d, 0xa5, 0xcb, 0x41, 0x20, 0xac, 0x3f, 0x13, 0xac, 0x44, 0xff, 0xc9, 0x96, 0x3b, 0xaf, 0xec,
	0x8e, 0x63, 0xe3, 0xa7, 0x00, 0x1d, 0x27, 0xb6, 0x5d, 0xdf, 0xb3, 0x83, 0x23, 0x4e, 0xd0, 0x07,
	0x12, 0xd1, 0xab, 0xa7, 0x58, 0xda, 0x72, 0xe2, 0x55, 0xdf, 0xdb, 0x3c, 0x5a, 0x0b, 0xd3, 0xe4,
	0xc4, 0xaa, 0x77, 0xf8, 0x67, 0xeb, 0x03, 0x98, 0x2c, 0x34, 0x91, 0xf0, 0xe7, 0x05, 0x3a, 0xe1,
	0xc4, 0x91, 0x9f, 0x44, 0xd4, 0x47, 0x4e, 0xd0, 0xcd, 0xe4, 0xcf, 0x3e, 0xde, 0xaf, 0xbd, 0xa7,
	0x99, 0x7f, 0xa5, 0x41, 0x7d, 0xdd, 0x7f, 0xce, 0xa2, 0xb2, 0x4b, 0x30, 0xde, 0xf6, 0x9f, 0x0b,
	0xb6, 0x35, 0x46, 0x3e, 0x37, 0x3c, 0xb2, 0x39, 0xd2, 0x86, 0xd0, 0xe9, 0xb0, 0x39, 0x1a, 0x56,
	0x9d, 0x00, 0xb6, 0x9d, 0x0e, 0xca, 0x1b, 0x7d, 0x37, 0x0a, 0xf9, 0xb6, 0x42, 0x1b, 0x37, 0xdc,
	0x28, 0xa4, 0x7a, 0x41, 0x1a, 0xe3, 0xc4, 0x77, 0xb3, 0x60, 0x81, 0x76, 0xdf, 0x21, 0x00, 0xe3,
	0x06, 0x4c, 0xf5, 0x9a, 0x99, 0xee, 0xb0, 0x70, 0x61, 0x32, 0xef, 0x43, 0x96, 0xd4, 0xfc, 0x5b,
	0x0d, 0x74, 0x0b, 0xb9, 0x9b, 0x51, 0x9a, 0xa2, 0xe4, 0x64, 0xf5, 0x90, 0x92, 0xbb, 0x00, 0xc0,
	0x05, 0xd8, 0xa3, 0xb8, 0xc1, 0x21, 0x1b, 0x1e, 0x89, 0x44, 0x02, 0xd6, 0x3f, 0x8b, 0xde, 0x18,
	0xf7, 0x13, 0x1c, 0xca, 0x22, 0xb8, 0x79, 0x68, 0x38, 0xc7, 0x4e, 0xe2, 0x89, 0xfb, 0x3e, 0x05,
	0x90, 0x18, 0xee, 0x41, 0xc6, 0x1a, 0xd9, 0xc9, 0x47, 0xe8, 0x4e, 0xde, 0x92, 0x2c, 0x1a, 0x97,
	0x1f, 0x67, 0x9b, 0x6c, 0xe2, 0x1b, 0x30, 0xb3, 0x8e, 0x52, 0x91, 0xe2, 0xd7, 0x3d, 0x3c, 0x99,
	0x98, 0xc6, 0x72, 0xd5, 0xa9, 0x70, 0x6c, 0x7c, 0x00, 0xe3, 0xee, 0xa1, 0xa8, 0xe2, 0x66, 0x95,
	0xb8, 0xb2, 0xd4, 0xac, 0x31, 0xf7, 0xf0, 0xd4, 0x68, 0xc8, 0xfc, 0x11, 0xd5, 0xe6, 0x7c, 0x20,
	0x9d, 0xd3, 0x42, 0x2e, 0x9d, 0x80, 0x59, 0x7b, 0x1f, 0xc9, 0x9b, 0xef, 0x52, 0xb7, 0xaf, 0x18,
	0x8d, 0x63, 0xb2, 0x19, 0xf9, 0xd8, 0x4e, 0x90, 0x4b, 0x87, 0xd6, 0xad, 0x51, 0x1f, 0x5b, 0xc8,
	0x35, 0xd7, 0xa1, 0xb9, 0xe7, 0x77, 0xd0, 0x2e, 0x72, 0x53, 0x9f, 0xa9, 0x0e, 0x77, 0xd8, 0x7e,
	0x27, 0x77, 0x29, 0xcc, 0x65, 0xfb, 0x1d, 0xba, 0x0f, 0xd2, 0x65, 0xf5, 0x3b, 0x19, 0xf9, 0xe3,
	0x64, 0x4f, 0xf3, 0x3b, 0xc8, 0xfc, 0xbf, 0x1a, 0xb4, 0x7a, 0x68, 0x99, 0x20, 0x76, 0xfd, 0x4e,
	0x1c, 0xa0, 0x41, 0xf4, 0xa6, 0x05, 0x75, 0xd7, 0x49, 0x51, 0x3b, 0x4a, 0x4e, 0x32, 0xb9, 0x64,
	0xdf, 0x84, 0xf2, 0xd4, 0x69, 0x93, 0x61, 0xdc, 0x69, 0xa5, 0x4e, 0x7b, 0xc3, 0x23, 0x86, 0x83,
	0xbb, 0x07, 0x76, 0xea, 0xb4, 0xa9, 0x96, 0x34, 0xac, 0x31, 0xdc, 0x3d, 0xd8, 0x73, 0xda, 0xc6,
	0x1b, 0x30, 0x99, 0xa1, 0x62, 0xbe, 0x8e, 0x07, 0xc3, 0x1c, 0x48, 0xf3, 0x01, 0xc4, 0x3a, 0xb1,
	0x1b, 0x25, 0x28, 0x0b, 0x86, 0xe9, 0x07, 0xe1, 0x8f, 0xa0, 0xa2, 0x26, 0x37, 0xce, 0x22, 0xbc,
	0xd4, 0x69, 0x53, 0x8b, 0x23, 0x9e, 0xd3, 0x39, 0x40, 0x01, 0x3d, 0x8e, 0x34, 0x2c, 0xf6, 0x41,
	0x88, 0x48, 0x22, 0xdf, 0xee, 0x26, 0x01, 0x3d, 0x8a, 0x90, 0x43, 0x4e, 0xe4, 0xef, 0x27, 0x01,
	0x21, 0x22, 0x41, 0x81, 0x43, 0x84, 0x6a, 0xe3, 0x43, 0x3f, 0xe6, 0xa7, 0x91, 0x89, 0x0c, 0xb8,
	0x7b, 0xe8, 0xc7, 0xe4, 0x24, 0xc4, 0x47, 0x33, 0x33, 0x6c, 0xf2, 0x78, 0x86, 0x4e, 0x41, 0xdd,
	0xef, 0x0d, 0x98, 0x4a, 0x90, 0x6b, 0xc7, 0x51, 0x94, 0xd8, 0x38, 0xea, 0x26, 0x2e, 0xe2, 0xe7,
	0x92, 0xc9, 0x04, 0xb9, 0x3b, 0x51, 0x94, 0xec, 0x52, 0x20, 0x71, 0x85, 0x5c, 0xf9, 0x99, 0xe4,
	0xf3, 0x95, 0x78, 0x49, 0x28, 0xe9, 0x62, 0x94, 0xd8, 0xb9, 0x7c, 0x99, 0xf0, 0x27, 0x08, 0x70,
	0x35, 0x93, 0x31, 0x11, 0x47, 0xea, 0x24, 0x59, 0x16, 0x86, 0x7d, 0x10, 0xa8, 0x1b, 0x75, 0x73,
	0x1b, 0x65, 0x1f, 0x44, 0x89, 0x0f, 0xa3, 0x0e, 0xf7, 0x1c, 0xfc, 0x28, 0x42, 0x00, 0x94, 0x60,
	0x1d, 0x86, 0xbb, 0xf9, 0xf9, 0x83, 0xfc, 0x24, 0xdd, 0x3d, 0x74, 0x44, 0x5c, 0x8d, 0xef, 0xf1,
	0x93, 0x60, 0x9d, 0x01, 0x36, 0x3c, 0xf3, 0x6b, 0x2d, 0xb3, 0xb4, 0x12, 0xdd, 0x38, 0x36, 0x9e,
	0xc0, 0x44, 0xbe, 0x8e, 0x3d, 0x73, 0xbb, 0x53, 0x35, 0x37, 0xb5, 0xda, 0x59, 0xcd, 0x6c, 0xd1,
	0x89, 0xf5, 0x31, 0x13, 0x40, 0x21, 0x8b, 0x02, 0xa8, 0x09, 0xac, 0x85, 0x9e, 0xf9, 0x37, 0x6c,
	0x8f, 0x2d, 0xd3, 0xf0, 0xf4, 0x3e, 0x91, 0x1e, 0xe7, 0x47, 0xeb, 0xf1, 0x53, 0x91, 0x67, 0xad,
	0x9f, 0x3c, 0x87, 0xa5, 0xf2, 0x1c, 0x29, 0xc9, 0xb3, 0x27, 0xa0, 0xd1, 0xa2, 0x80, 0x08, 0x7e,
	0x8c, 0x5e, 0x51, 0xb9, 0x8d, 0x5a, 0xe4, 0x27, 0x09, 0x62, 0xe7, 0x14, 0xe4, 0x7e, 0x8f, 0x42,
	0xfb, 0x8f, 0x61, 0x30, 0x76, 0x12, 0x14, 0x3b, 0x09, 0xe2, 0x33, 0x0d, 0x62, 0xe6, 0x3d, 0x53,
	0xae, 0x89, 0xa6, 0x4c, 0xdd, 0xe2, 0xb1, 0x2d, 0x46, 0x26, 0xf5, 0x10, 0x1d, 0x33, 0x4b, 0xfd,
	0x10, 0x9a, 0xa4, 0x11, 0x33, 0x0f, 0xc5, 0x77, 0x84, 0x85, 0x2a, 0x43, 0x82, 0x1b, 0xb3, 0x20,
	0x44, 0xc7, 0x99, 0x4b, 0x9b, 0x87, 0x46, 0x14, 0x78, 0x05, 0x57, 0x50, 0x8f, 0x02, 0x2f, 0x9f,
	0x9c, 0x34, 0x66, 0x93, 0x8f, 0x0d, 0x34, 0x79, 0x14, 0x78, 0xd9, 0xe4, 0x57, 0xa1, 0x49, 0x33,
	0xa6, 0x7c, 0x7a, 0x9e, 0xca, 0xa0, 0x20, 0x86, 0x60, 0x05, 0x26, 0x59, 0x87, 0x0c, 0x45, 0x7d,
	0x10, 0x14, 0x13, 0x74, 0x8c, 0xc0, 0x41, 0x2f, 0xab, 0xd1, 0x28, 0x65, 0x35, 0xae, 0xc3, 0x44,
	0xcc, 0xd6, 0x81, 0x19, 0x24, 0xf3, 0x33, 0x4d, 0x0e, 0x13, 0xf2, 0x0b, 0xcd, 0x3c, 0xbf, 0xd0,
	0x82, 0x7a, 0x14, 0xa3, 0xc4, 0x49, 0xa3, 0x84, 0x7a, 0x93, 0x86, 0x95, 0x7f, 0x9b, 0x9f, 0x51,
	0x5b, 0x28, 0xae, 0xac, 0x90, 0xc6, 0xe8, 0xb7, 0xb8, 0x65, 0x4a, 0x6a, 0x15, 0x4a, 0x4c, 0x4c,
	0x55, 0x57, 0x36, 0x3b, 0x8e, 0x8d, 0xa7, 0x30, 0x9d, 0x8d, 0x97, 0xa8, 0xf0, 0x9b, 0x55, 0x89,
	0x55, 0xf5, 0xcf, 0x32, 0xe2, 0xca, 0xdc, 0x66, 0x17, 0xa6, 0x77, 0xcb, 0x48, 0x09, 0x3b, 0xcb,
	0xd5, 0x70, 0x75, 0x30, 0x24, 0xbd, 0xec, 0x86, 0x28, 0xc9, 0x5a, 0x49, 0x92, 0x97, 0xe0, 0xa2,
	0x04, 0x2d, 0x8e, 0xcd, 0x4f, 0x60, 0xfa, 0x21, 0x0a, 0xaa, 0xf4, 0x9c, 0xbe, 0x45, 0xf6, 0xc3,
	0x25, 0x99, 0x12, 0xc7, 0xe6, 0x3b, 0xd4, 0xbd, 0xf2, 0x86, 0x15, 0xc7, 0x7d, 0xd1, 0x8d, 0xb3,
	0xd5, 0x9c, 0x85, 0xf1, 0x23, 0x94, 0x60, 0xa2, 0x93, 0x04, 0xd7, 0xb0, 0x95, 0x7d, 0x9a, 0x9f,
	0x8b, 0x3a, 0x20, 0x0e, 0xc2, 0xf1, 0xb7, 0x20, 0x34, 0xf3, 0x0b, 0x38, 0xcf, 0xdb, 0x9f, 0xc4,
	0x28, 0x21, 0x0e, 0x28, 0xa1, 0x2e, 0x80, 0x70, 0x23, 0x1e, 0x65, 0x28, 0x7b, 0x54, 0x81, 0xdf,
	0x2b, 0xe4, 0x75, 0x06, 0xc3, 0xc7, 0x12, 0x3b, 0xdb, 0x34, 0xa6, 0xaa, 0xa0, 0x7b, 0xed, 0xc0,
	0xf0, 0x6b, 0x96, 0x23, 0x57, 0x4c, 0x88, 0x63, 0xe3, 0x21, 0xcd, 0x50, 0x44, 0x89, 0x77, 0xca,
	0x29, 0xa8, 0x32, 0x03, 0x4d, 0x63, 0xf0, 0x99, 0x8a, 0x89, 0xb1, 0x5a, 0x29, 0x31, 0xf6, 0xbf,
	0xa3, 0xa0, 0x3f, 0xf4, 0x71, 0x1c, 0x38, 0x27, 0xbb, 0x2e, 0x0a, 0x59, 0x7c, 0x55, 0xbe, 0xbb,
	0x50, 0x1c, 0xfe, 0x52, 0x3f, 0x0d, 0x10, 0x3f, 0x30, 0xb0, 0x0f, 0x12, 0xf3, 0x60, 0x32, 0x8d,
	0x70, 0x10, 0xa7, 0xdf, 0x2c, 0x9a, 0xef, 0x38, 0xc9, 0x0b, 0x44, 0x4e, 0x27, 0x8c, 0x9f, 0xd1,
	0x6b, 0xc3, 0x64, 0xaf, 0x63, 0xd0, 0x0d, 0x2f, 0x33, 0x82, 0x38, 0x70, 0xd2, 0xe7, 0x51, 0xd2,
	0xe1, 0xd1, 0x54, 0xfe, 0x4d, 0xfc, 0x63, 0xc7, 0x0f, 0xed, 0x4c, 0xd1, 0x58, 0x4c, 0x05, 0x1d,
	0x3f, 0x7c, 0xca, 0x20, 0x85, 0x0c, 0x41, 0x5d, 0x95, 0x3e, 0x69, 0x88, 0x77, 0x2c, 0x0b, 0x00,
	0x8c, 0x5e, 0xc1, 0xdd, 0x35, 0x28, 0x84, 0xea, 0x4a, 0x6f, 0x8b, 0x69, 0x8a, 0x5b, 0xcc, 0x1c,
	0xd4, 0xbf, 0xe8, 0x76, 0x62, 0x1a, 0xa9, 0x31, 0x9f, 0x37, 0x4e, 0xbe, 0x49, 0xa8, 0x36, 0x03,
	0x63, 0xc7, 0xc8, 0x6f, 0x1f, 0xa6, 0x34, 0xa5, 0x3b, 0x69, 0xf1, 0x2f, 0x32, 0xd3, 0x41, 0xdb,
	0xf6, 0x3b, 0xed, 0xd9, 0x73, 0x4c, 0x5e, 0x07, 0xed, 0x8d, 0x4e, 0x9b, 0xac, 0x0d, 0xee, 0x38,
	0x41, 0x40, 0x5b, 0xa6, 0x98, 0x21, 0x52, 0x00, 0x69, 0x24, 0xc4, 0xa5, 0x4e, 0xea, 0xbb, 0xb4,
	0x55, 0xa7, 0xad, 0x0d, 0x06, 0x21, 0xcd, 0x57, 0xa1, 0xe9, 0x9d, 0x84, 0x4e, 0xc7, 0x77, 0x29,
	0x21, 0xe7, 0x99, 0x38, 0x38, 0x88, 0xd0, 0x22, 0x74, 0xe8, 0x78, 0xef, 0xce, 0x1a, 0x85, 0x0e,
	0x5b, 0xde, 0xbb, 0xc6, 0x2d, 0xd0, 0xb3, 0x0e, 0x5f, 0xe0, 0x28, 0xb4, 0xc9, 0xa1, 0xf3, 0x02,
	0xed, 0x75, 0x8e, 0xc3, 0x3f, 0xc6, 0x51, 0xf8, 0x6b, 0xe8, 0xa4, 0xe0, 0x2f, 0xa6, 0x8b, 0xfe,
	0xa2, 0xb8, 0xa3, 0x5c, 0x2c, 0xed, 0x28, 0x33, 0x30, 0x46, 0x28, 0xee, 0xe2, 0xd9, 0x19, 0x26,
	0x0f, 0xf6, 0x45, 0x95, 0xd2, 0xcf, 0xe2, 0xbe, 0x4b, 0x5c, 0x29, 0x7d, 0x1e, 0xf7, 0x99, 0x30,
	0xd9, 0xb5, 0xc5, 0xa5, 0x9e, 0x65, 0xde, 0xbf, 0xbb, 0xd5, 0x5b, 0x6b, 0x22, 0x1c, 0x12, 0xb1,
	0x13, 0xb5, 0xc3, 0xb3, 0x73, 0xd7, 0x86, 0xa9, 0x70, 0xba, 0x07, 0x7b, 0x14, 0x40, 0xf6, 0x8f,
	0x8c, 0x35, 0x8a, 0xa2, 0xc5, 0x66, 0xe0, 0x30, 0xba, 0x7f, 0xfc, 0x0c, 0xf4, 0x4f, 0xc8, 0xce,
	0x48, 0xcf, 0xdc, 0xab, 0x51, 0xf8, 0xdc, 0x6f, 0x1b, 0x1f, 0xc3, 0x14, 0xd7, 0xdf, 0x92, 0x5f,
	0x92, 0x1c, 0xcc, 0xca, 0x66, 0x63, 0x4d, 0xe2, 0xec, 0x27, 0x75, 0x4d, 0xff, 0xa5, 0xd1, 0x04,
	0x44, 0xb9, 0x9b, 0xe0, 0x2e, 0x04, 0xff, 0xc4, 0x8c, 0x4a, 0x34, 0x9f, 0x5a, 0xd1, 0x7c, 0xe6,
	0xa1, 0x91, 0x9b, 0x4f, 0x16, 0xd6, 0x64, 0x96, 0x23, 0x48, 0x79, 0xa4, 0x20, 0x65, 0xd1, 0x20,
	0x46, 0x55, 0x06, 0x31, 0x26, 0x1a, 0x44, 0xe6, 0xc4, 0xc6, 0x55, 0x4e, 0xac, 0x5e, 0x72, 0x62,
	0x5f, 0x51, 0xa7, 0x28, 0x67, 0x12, 0xc7, 0xc6, 0xaf, 0x56, 0x5d, 0xfc, 0x20, 0xa2, 0x2c, 0xe4,
	0xfc, 0xd5, 0xde, 0x6b, 0x07, 0x66, 0x96, 0x3d, 0xaf, 0x32, 0x1a, 0xbd, 0x34, 0x7e, 0x85, 0x7b,
	0x79, 0x76, 0xa9, 0x36, 0x08, 0x4a, 0xe6, 0xe3, 0xe7, 0xe8, 0xd5, 0x60, 0x75, 0x46, 0x1c, 0x9b,
	0xb7, 0x60, 0xe6, 0x21, 0x0a, 0x64, 0xc8, 0xca, 0xd7, 0x69, 0x73, 0xf4, 0xe6, 0x4d, 0x3a, 0xc9,
	0x65, 0xaa, 0x13, 0x65, 0xbd, 0xe3, 0x3a, 0x61, 0xfe, 0x37, 0x4b, 0x42, 0xc9, 0x9b, 0x71, 0x6c,
	0x1c, 0xc0, 0x64, 0xc7, 0x89, 0xa9, 0x46, 0xdb, 0xfc, 0xce, 0x90, 0x48, 0xf4, 0x43, 0x69, 0x1e,
	0x4a, 0x35, 0xcb, 0xd2, 0x96, 0x13, 0x13, 0x03, 0x20, 0x50, 0x96, 0x8a, 0x6a, 0x76, 0x7a, 0x90,
	0xd6, 0x01, 0xe8, 0xe5, 0x0e, 0x92, 0x84, 0xd4, 0x7b, 0x62, 0x42, 0x4a, 0x2a, 0xe0, 0x32, 0x7a,
	0x31, 0x69, 0xf5, 0x00, 0x5a, 0x2b, 0x4e, 0xba, 0x8e, 0x52, 0xbe, 0xc9, 0xee, 0x46, 0xdd, 0xd0,
	0xdb, 0x24, 0x87, 0x60, 0x9e, 0x7d, 0x75, 0x7d, 0x61, 0xcf, 0x9b, 0xb4, 0xc6, 0x5d, 0x9f, 0x6e,
	0x0f, 0xe6, 0x3f, 0x69, 0x30, 0xaf, 0x1c, 0xd9, 0x13, 0x10, 0x1d, 0x4e, 0x0f, 0xd8, 0x4a, 0x01,
	0xf5, 0x99, 0x25, 0x4b, 0xd4, 0x91, 0xef, 0x9e, 0x80, 0x32, 0x48, 0xeb, 0x43, 0x2a, 0xa0, 0x42,
	0x87, 0xd3, 0x32, 0x76, 0x0d, 0x91, 0xf9, 0x7f, 0xd5, 0x60, 0x6a, 0x2f, 0xf1, 0xdb, 0x6d, 0x94,
	0x90, 0x30, 0x9d, 0x66, 0x23, 0x3f, 0x06, 0x20, 0xde, 0x4f, 0x08, 0x59, 0xce, 0xdd, 0xbf, 0x2d,
	0x09, 0xeb, 0x8b, 0xc3, 0x68, 0x98, 0x4f, 0xa3, 0x1a, 0x8b, 0x7a, 0x53, 0xfa, 0xd3, 0x0c, 0xa1,
	0x91, 0xc3, 0x8d, 0x19, 0x30, 0xe8, 0x87, 0x4d, 0xbe, 0xec, 0x8d, 0xf0, 0xc8, 0x09, 0x7c, 0x4f,
	0x1f, 0x32, 0xde, 0x82, 0xeb, 0x02, 0x7c, 0xb9, 0x9b, 0x46, 0xeb, 0x28, 0xdc, 0x69, 0xbb, 0x3c,
	0x94, 0xa0, 0xe7, 0x0d, 0x5d, 0x33, 0x4c, 0xb8, 0x22, 0x74, 0xdb, 0x8c, 0x1c, 0xaf, 0xdc, 0xa7,
	0x66, 0x1a, 0xa0, 0x17, 0xe9, 0xc2, 0xb1, 0xb9, 0x4e, 0x23, 0x1b, 0xaa, 0x02, 0x16, 0x72, 0xb9,
	0x94, 0x57, 0x4e, 0xf6, 0xc8, 0xf6, 0x29, 0x3f, 0x09, 0xcb, 0x4f, 0x73, 0xe6, 0x87, 0xb0, 0xd0,
	0x67, 0x22, 0x76, 0x03, 0xd1, 0x2f, 0x93, 0xb5, 0x02, 0xe7, 0xd7, 0x51, 0xba, 0x8f, 0x51, 0x62,
	0x45, 0x7e, 0x66, 0xaf, 0x55, 0xec, 0x85, 0x63, 0x73, 0xad, 0x94, 0x57, 0x78, 0x05, 0x46, 0x79,
	0x8e, 0x5e, 0x0e, 0x2c, 0xf2, 0x85, 0x1c, 0x58, 0xe4, 0x93, 0xbd, 0x2b, 0x89, 0x7c, 0xfb, 0xd0,
	0x6f, 0x1f, 0xda, 0x2e, 0xe2, 0x49, 0x90, 0x86, 0xd5, 0x4c, 0x22, 0xff, 0xb1, 0xdf, 0x3e, 0x5c,
	0x45, 0x49, 0x6a, 0xdc, 0x04, 0x3d, 0xef, 0x13, 0x47, 0x2c, 0x8e, 0x60, 0x61, 0xd4, 0x24, 0xef,
	0xb6, 0x13, 0x91, 0x68, 0xc2, 0x5c, 0x82, 0x16, 0x35, 0x9e, 0xa4, 0x63, 0x65, 0xf0, 0x14, 0x85,
	0xa9, 0xe3, 0x07, 0x52, 0x36, 0xcc, 0x05, 0x98, 0x57, 0xf6, 0xc7, 0xb1, 0xf9, 0x6f, 0x35, 0x98,
	0xe6, 0x22, 0x5c, 0x4f, 0xa2, 0x6e, 0x6c, 0x21, 0x96, 0x06, 0x92, 0x05, 0x7c, 0x42, 0xa6, 0x98,
	0xfe, 0xa6, 0x7b, 0x53, 0xf7, 0x80, 0xa5, 0xb3, 0x18, 0xb1, 0xe3, 0xb8, 0x7b, 0x40, 0xd3, 0x59,
	0x57, 0x00, 0x0e, 0x1c, 0xf7, 0x45, 0x3b, 0x21, 0x66, 0xc4, 0x13, 0x68, 0x02, 0xa4, 0x10, 0x2f,
	0x8d, 0x16, 0xe3, 0x25, 0x03, 0x46, 0x12, 0x27, 0x64, 0xb7, 0xfa, 0x9a, 0x45, 0x7f, 0x17, 0x76,
	0xad, 0xf1, 0xe2, 0xae, 0x55, 0x88, 0x35, 0xea, 0xca, 0x58, 0xa3, 0x51, 0xd8, 0x05, 0x6f, 0xc0,
	0x54, 0x4f, 0x45, 0x98, 0x5b, 0x01, 0xea, 0x56, 0x26, 0x73, 0x3d, 0xc9, 0xb2, 0x13, 0x7c, 0x4b,
	0x6c, 0x8a, 0x5b, 0xe2, 0x1c, 0xd4, 0xf3, 0xa4, 0xda, 0x04, 0x2f, 0x11, 0x60, 0x19, 0x35, 0xf3,
	0xe7, 0x1a, 0x5c, 0xe9, 0x79, 0x91, 0x82, 0x4c, 0x5f, 0xbb, 0xd6, 0x86, 0xad, 0xc5, 0x70, 0x65,
	0x2d, 0x46, 0x84, 0xb5, 0xe8, 0x71, 0x3a, 0x2a, 0x72, 0x6a, 0x7e, 0x09, 0x57, 0xfb, 0x92, 0x43,
	0x2f, 0x90, 0x46, 0x84, 0xbd, 0xf8, 0x86, 0x32, 0x97, 0x53, 0x18, 0x6d, 0xd1, 0x31, 0xfd, 0xf7,
	0xe2, 0x9f, 0x40, 0x6b, 0xd9, 0xf3, 0xa4, 0xa3, 0xd1, 0x4b, 0x82, 0x56, 0xd8, 0x8f, 0x07, 0x46,
	0x4b, 0xf7, 0xe4, 0x05, 0x98, 0x57, 0xce, 0x8c, 0x63, 0xf3, 0xd7, 0x61, 0x81, 0x95, 0xc1, 0x7c,
	0x17, 0xb8, 0xaf, 0xc1, 0x95, 0x7e, 0x93, 0xe3, 0xd8, 0xbc, 0x03, 0xad, 0x87, 0x28, 0x50, 0xe1,
	0x2e, 0x87, 0x06, 0x0b, 0x30, 0xaf, 0xec, 0x8d, 0x63, 0xf3, 0x53, 0xb8, 0xbc, 0x7a, 0x88, 0xdc,
	0x17, 0xdf, 0x05, 0x2b, 0xef, 0xc3, 0x42, 0x9f, 0xb9, 0x71, 0x4c, 0x14, 0xdd, 0xc7, 0x36, 0xdd,
	0x21, 0xb8, 0x4f, 0x1b, 0xf7, 0xf1, 0x53, 0xf2, 0x69, 0xfe, 0x81, 0x06, 0xd3, 0x3d, 0xcd, 0x12,
	0x1c, 0x79, 0xcf, 0x6d, 0x6b, 0xe2, 0x09, 0xe9, 0x2c, 0x29, 0xe0, 0x4a, 0x0e, 0x74, 0x44, 0x92,
	0x03, 0xad, 0xa4, 0x82, 0xcd, 0xdf, 0x86, 0x8b, 0x12, 0x8a, 0xbe, 0xc7, 0xac, 0xe5, 0x9f, 0xe6,
	0xb9, 0x53, 0x76, 0xa3, 0xb3, 0x72, 0xb2, 0x83, 0x92, 0x5c, 0x30, 0x77, 0xc0, 0x88, 0x51, 0x82,
	0xa3, 0xd0, 0x09, 0xfc, 0xf4, 0xc4, 0x2e, 0x08, 0x49, 0x17, 0x5a, 0xf6, 0xce, 0x2c, 0x2f, 0x9e,
	0xc5, 0x1d, 0xc9, 0xb3, 0xb8, 0x12, 0xe1, 0x7c, 0xc3, 0xce, 0x1e, 0x52, 0xda, 0xbe, 0x47, 0x11,
	0xbd, 0x0f, 0x0b, 0x2b, 0x4e, 0xea, 0x1e, 0x0a, 0x0b, 0xe5, 0xb7, 0xf3, 0xdb, 0xb6, 0xfe, 0xa1,
	0xde, 0x7f, 0x6a, 0x70, 0xa5, 0xdf, 0x60, 0x1a, 0xed, 0x4d, 0x64, 0xd1, 0x5e, 0xdb, 0x7f, 0x9e,
	0xb1, 0xf1, 0x91, 0x34, 0xd8, 0xeb, 0x33, 0x0f, 0x8f, 0xf7, 0x08, 0x88, 0x85, 0x7b, 0xd0, 0xc9,
	0x01, 0xad, 0xcf, 0x60, 0xaa, 0xd4, 0x2c, 0x09, 0xf6, 0xde, 0x29, 0x46, 0xc3, 0x92, 0x84, 0xac,
	0x88, 0x52, 0x88, 0x05, 0x7f, 0x03, 0x9a, 0x42, 0xcb, 0x77, 0x70, 0x7f, 0x6b, 0xee, 0xd0, 0x80,
	0x99, 0x72, 0x6f, 0xa1, 0x23, 0x14, 0x76, 0xd1, 0xee, 0xb1, 0x9f, 0xba, 0x87, 0x8f, 0xbb, 0x07,
	0x85, 0x18, 0x62, 0x38, 0x0b, 0x85, 0xae, 0x42, 0x13, 0xd3, 0x1e, 0x62, 0xe2, 0x15, 0x18, 0x88,
	0x6e, 0x7a, 0xff, 0xa0, 0xc1, 0x65, 0xf5, 0x94, 0x38, 0x36, 0x3e, 0x87, 0xa6, 0x8f, 0xed, 0x28,
	0x46, 0xa1, 0xdd, 0x71, 0x62, 0x5e, 0x47, 0xf4, 0x63, 0xf5, 0xaa, 0xc8, 0x26, 0x59, 0xda, 0xc0,
	0x4f, 0x62, 0x14, 0x6e, 0x39, 0x31, 0x5b, 0x92, 0x86, 0x9f, 0x7d, 0xb7, 0x7e, 0x04, 0xe7, 0x8a,
	0x8d, 0xa7, 0x45, 0xdf, 0x75, 0x51, 0xe2, 0x8b, 0xfc, 0x6e, 0xab, 0x8f, 0x28, 0x72, 0x2b, 0xfa,
	0xbb, 0xec, 0x42, 0x49, 0xc2, 0xe4, 0x4f, 0x64, 0x4c, 0xbe, 0x27, 0x3d, 0x88, 0x7d, 0xaf, 0xfc,
	0x79, 0x30, 0xb3, 0x3b, 0x20, 0x7f, 0xa7, 0x2e, 0x35, 0xd1, 0x47, 0xce, 0x24, 0xd5, 0xab, 0xba,
	0x35, 0xc6, 0xc8, 0x24, 0x27, 0xdc, 0x5d, 0x39, 0x63, 0xe6, 0x5f, 0x68, 0xd4, 0x31, 0x93, 0x70,
	0xf9, 0x93, 0x2e, 0x75, 0x71, 0xc2, 0xa5, 0xb3, 0x90, 0x6c, 0xd3, 0xca, 0xc9, 0x36, 0x4e, 0x5f,
	0xad, 0x27, 0xff, 0x7d, 0x80, 0x4f, 0xba, 0x28, 0x61, 0x47, 0x68, 0xe3, 0x12, 0x5c, 0xa0, 0x5f,
	0x36, 0xfd, 0x14, 0xce, 0x32, 0x17, 0xe1, 0xbc, 0xd8, 0xb0, 0x19, 0xb5, 0xfd, 0x50, 0xd7, 0xca,
	0xfd, 0xb9, 0xed, 0xeb, 0x35, 0xf3, 0x37, 0xa9, 0x0a, 0x54, 0x08, 0x64, 0x87, 0x09, 0x1f, 0xdb,
	0x2f, 0x19, 0x94, 0xef, 0x81, 0x0d, 0x1f, 0xf3, 0x6e, 0x24, 0xb6, 0xcf, 0xe3, 0x7a, 0x1a, 0xcc,
	0xf2, 0xd8, 0xfe, 0x90, 0x07, 0xf6, 0x24, 0xa0, 0xbd, 0x06, 0x13, 0x87, 0x7e, 0x3b, 0x2c, 0xc5,
	0xf5, 0x40, 0x60, 0x3c, 0xa8, 0xbf, 0x03, 0xb3, 0x3c, 0x48, 0xe7, 0xf3, 0xf2, 0x80, 0x5f, 0xae,
	0x83, 0xf3, 0x30, 0xa7, 0xe8, 0x8d, 0x63, 0xf3, 0x36, 0xd5, 0xcf, 0xbd, 0x28, 0x7e, 0xe6, 0x87,
	0x62, 0xbe, 0x5a, 0x3a, 0xd3, 0xef, 0x69, 0x70, 0xbe, 0xd2, 0xf5, 0x35, 0x2f, 0xd9, 0xc4, 0xbb,
	0xed, 0xe1, 0xe2, 0xdd, 0xb6, 0xe4, 0x96, 0x79, 0x44, 0x76, 0xcb, 0x7c, 0x4c, 0x2f, 0x06, 0x24,
	0xb4, 0xe3, 0x38, 0xaf, 0xbd, 0x54, 0x96, 0x44, 0x57, 0x87, 0xd1, 0x01, 0x64, 0x85, 0x7c, 0x6c,
	0x1f, 0xfa, 0xa9, 0xed, 0x1c, 0xa4, 0x08, 0xa7, 0xdc, 0x3e, 0x9a, 0x3e, 0x7e, 0xec, 0xa7, 0xcb,
	0x14, 0x64, 0xde, 0x82, 0xe9, 0xd5, 0x00, 0x39, 0x21, 0x9b, 0xe3, 0x91, 0x1f, 0xa4, 0xec, 0x0c,
	0x5e, 0x95, 0xd8, 0x25, 0xb8, 0x28, 0xe9, 0x29, 0xca, 0xfd, 0xc9, 0x11, 0x4a, 0x36, 0x9d, 0x13,
	0xe1, 0xe2, 0xa5, 0x3a, 0xcb, 0x1f, 0x6a, 0x19, 0xa7, 0xe5, 0xde, 0xa7, 0x1e, 0x5f, 0x5f, 0x43,
	0xfc, 0x6f, 0xc1, 0xb9, 0x5c, 0x4c, 0x64, 0x11, 0x02, 0x41, 0xfa, 0x0c, 0xba, 0x13, 0x45, 0x81,
	0xf9, 0x8f, 0x1a, 0x4c, 0xd1, 0x53, 0xf5, 0x33, 0x9a, 0x9f, 0xa6, 0x4f, 0x16, 0x14, 0xb1, 0xdc,
	0x53, 0x96, 0x53, 0x09, 0x8e, 0x6c, 0x9e, 0xd9, 0x66, 0xbe, 0xee, 0xbe, 0x22, 0xe5, 0xd3, 0x9b,
	0x90, 0xec, 0xab, 0x9b, 0x47, 0xec, 0xbb, 0x97, 0x47, 0xc9, 0x20, 0x3c, 0x8f, 0x52, 0xe8, 0x70,
	0xa6, 0xca, 0xa7, 0x79, 0x1a, 0x7e, 0x95, 0x70, 0x66, 0x99, 0xb4, 0xcf, 0x7a, 0x79, 0xb6, 0x72,
	0x23, 0x8e, 0x8d, 0x0f, 0xab, 0x8f, 0x0f, 0xae, 0x9f, 0xca, 0x8e, 0xf0, 0xc0, 0xe2, 0xa4, 0x87,
	0xba, 0x97, 0x95, 0xc8, 0x4e, 0x7c, 0xea, 0x90, 0xb8, 0x5a, 0x60, 0x79, 0xe6, 0xf2, 0x6c, 0xf3,
	0x41, 0x8f, 0xb1, 0x32, 0x6a, 0x16, 0xc2, 0xab, 0xe2, 0xa9, 0x1f, 0xc2, 0xc4, 0x2e, 0xad, 0x69,
	0xe1, 0xa9, 0x6e, 0x05, 0x99, 0x92, 0xa3, 0xbf, 0xf9, 0x47, 0x1a, 0x4c, 0xe6, 0x03, 0x37, 0xb8,
	0x9d, 0x25, 0x51, 0x94, 0xda, 0xb9, 0x16, 0x6a, 0x59, 0x96, 0x23, 0x4a, 0xf7, 0xb8, 0x26, 0x5e,
	0x81, 0x66, 0xde, 0xa7, 0x57, 0xcf, 0xc9, 0x7b, 0xd0, 0x72, 0xc9, 0x09, 0x5e, 0x73, 0x23, 0x56,
	0x4c, 0x5e, 0xa9, 0xae, 0x83, 0x48, 0xb6, 0x05, 0xac, 0x30, 0x87, 0xb2, 0x34, 0xc7, 0xcc, 0x50,
	0xa4, 0x2c, 0x5b, 0xff, 0xa7, 0xcc, 0xe6, 0xaa, 0x4d, 0xac, 0x86, 0x32, 0x47, 0xaa, 0xac, 0xa1,
	0x2c, 0x0c, 0xa5, 0xe6, 0x25, 0xa0, 0xb4, 0x90, 0xfb, 0x08, 0x21, 0xef, 0xc0, 0x71, 0x5f, 0x70,
	0xb2, 0xd0, 0x4b, 0xf3, 0x83, 0xac, 0xf2, 0xa3, 0xdc, 0x84, 0x63, 0x56, 0x6b, 0xec, 0xe0, 0x28,
	0xec, 0x61, 0x6d, 0x58, 0xc0, 0x40, 0x74, 0x5e, 0x0f, 0xf4, 0x87, 0x91, 0x30, 0x56, 0xbe, 0x61,
	0x17, 0xbd, 0x45, 0xad, 0xec, 0x2d, 0x4a, 0x58, 0x86, 0x2b, 0x58, 0x2e, 0xc0, 0xf9, 0x12, 0x16,
	0x1c, 0x9b, 0xf7, 0x60, 0x9e, 0x9e, 0x0b, 0xf7, 0x7d, 0x6f, 0x03, 0x6f, 0x84, 0xcf, 0x90, 0xe7,
	0xf9, 0x61, 0x9b, 0x1f, 0x0f, 0x65, 0x0e, 0x6d, 0x8b, 0x1f, 0x52, 0xa5, 0x03, 0x70, 0x6c, 0xdc,
	0x85, 0x0b, 0x3e, 0xb6, 0xfd, 0xd0, 0x3e, 0x66, 0x2d, 0x76, 0x9b, 0x34, 0xf1, 0xed, 0x54, 0xf7,
	0x4b, 0x43, 0x16, 0x03, 0x38, 0x97, 0x9f, 0x2f, 0x58, 0x9d, 0xc3, 0x45, 0x38, 0x9f, 0x43, 0x8a,
	0xfb, 0x7c, 0x0f, 0x4c, 0x7b, 0xda, 0xbb, 0xba, 0x26, 0x03, 0x2f, 0xeb, 0x35, 0x19, 0x78, 0x45,
	0x1f, 0x5e, 0xfc, 0xe5, 0xd2, 0x6b, 0x16, 0x5e, 0xd4, 0x00, 0xf4, 0x83, 0xd6, 0xd9, 0xea, 0x43,
	0xc6, 0x14, 0x34, 0x85, 0x3a, 0x5c, 0x5d, 0x5b, 0xfc, 0x19, 0x4c, 0xec, 0x17, 0x8f, 0xa3, 0x13,
	0xdb, 0xe8, 0x98, 0x80, 0x68, 0x66, 0x54, 0x1f, 0x22, 0x90, 0x27, 0x81, 0xd7, 0x83, 0x68, 0xc6,
	0x79, 0x98, 0x5c, 0x89, 0xd2, 0xc3, 0x1e, 0xa8, 0xc6, 0x42, 0x12, 0x96, 0x16, 0x47, 0x49, 0xaf,
	0x61, 0x78, 0xf1, 0x9f, 0x35, 0x98, 0xe2, 0xb6, 0x2c, 0xe2, 0x58, 0x76, 0x53, 0xff, 0x88, 0x1c,
	0x1f, 0x57, 0x36, 0xd6, 0xf5, 0x21, 0xc3, 0x80, 0x73, 0x39, 0x64, 0x77, 0x79, 0x6b, 0x73, 0x53,
	0xd7, 0x08, 0xe9, 0xfb, 0x18, 0x79, 0x36, 0x9d, 0x52, 0xaf, 0x51, 0xca, 0xa2, 0xa4, 0xe3, 0x04,
	0x8c, 0x67, 0x7d, 0x98, 0x30, 0xf3, 0x38, 0x4a, 0xf3, 0xf8, 0x67, 0x84, 0x00, 0xac, 0xc8, 0xcf,
	0x01, 0xa3, 0x84, 0x52, 0xca, 0x79, 0x0e, 0x1a, 0x33, 0x66, 0x61, 0xda, 0xe2, 0xd5, 0x65, 0xf8,
	0xd0, 0x8f, 0xf3, 0x96, 0x71, 0xa3, 0x05, 0x33, 0x24, 0x04, 0xe1, 0xd1, 0xc8, 0x4e, 0xdb, 0xcd,
	0xdb, 0xea, 0x8b, 0xff, 0xa2, 0xc1, 0xc4, 0xaa, 0x58, 0x19, 0x77, 0x21, 0x67, 0x4b, 0x58, 0x47,
	0x01, 0xd8, 0x5b, 0xc5, 0x0a, 0x90, 0xac, 0x61, 0x05, 0xb8, 0xa2, 0x0f, 0x57, 0x81, 0xab, 0xfa,
	0x48, 0x15, 0xf8, 0x50, 0x1f, 0xad, 0x02, 0xd7, 0xf4, 0xb1, 0x2a, 0xf0, 0x91, 0x3e, 0x5e, 0x05,
	0xae, 0xeb, 0xf5, 0xc5, 0xbf, 0xac, 0xd1, 0x87, 0x07, 0x4c, 0x85, 0x44, 0x69, 0x50, 0x9d, 0x79,
	0x0b, 0xae, 0x5b, 0x6b, 0xab, 0x4f, 0xb6, 0xb6, 0xb6, 0xd6, 0xb6, 0xf7, 0x6c, 0x6b, 0x6d, 0x73,
	0x79, 0x6f, 0xe3, 0xc9, 0xf6, 0xee, 0xe3, 0x8d, 0x1d, 0x7b, 0xef, 0xa7, 0x3b, 0x6b, 0xf6, 0xf6,
	0x93, 0xed, 0x35, 0x7d, 0xc8, 0xb8, 0x0d, 0x37, 0xfb, 0x76, 0x7b, 0x64, 0x6d, 0xac, 0x6d, 0x3f,
	0xb4, 0xb7, 0x36, 0x56, 0x75, 0xcd, 0xb8, 0x07, 0xb7, 0x07, 0xe9, 0xbc, 0xfa, 0x78, 0x79, 0x7b,
	0x7b, 0x6d, 0x53, 0xaf, 0x19, 0xb7, 0xe0, 0xcd, 0xbe, 0x03, 0x56, 0x9f, 0x6c, 0x6e, 0xae, 0xad,
	0xee, 0xe9, 0xc3, 0xc6, 0x4d, 0x78, 0xa3, 0xff, 0xd4, 0x4f, 0x36, 0x37, 0x9f, 0x3c, 0xd3, 0x47,
	0x4e, 0x25, 0x78, 0x7f, 0x77, 0xed, 0xa1, 0xbd, 0xb6, 0xbd, 0xb7, 0x66, 0xe9, 0xa3, 0x8b, 0x7f,
	0xad, 0x81, 0x61, 0x45, 0x7e, 0xa6, 0xba, 0x28, 0x61, 0xf6, 0xd4, 0x82, 0x99, 0x2a, 0x74, 0x3b,
	0x0a, 0x89, 0xa1, 0x5c, 0x86, 0xd9, 0x6a, 0xdb, 0xa3, 0xc4, 0x47, 0xa1, 0xa7, 0x6b, 0x8a, 0xd6,
	0x28, 0x08, 0xa2, 0x63, 0xbd, 0x66, 0x2c, 0xc0, 0x5c, 0xb5, 0x75, 0x35, 0x0a, 0x02, 0xe4, 0xa6,
	0xfa, 0x88, 0x31, 0x07, 0x17, 0x25, 0x68, 0xd1, 0xb1, 0x5e, 0x5f, 0x7c, 0x06, 0xcd, 0x1d, 0xa1,
	0x8a, 0x69, 0x16, 0xa6, 0xf9, 0x67, 0xf9, 0x62, 0xe4, 0x12, 0x5c, 0x28, 0xb4, 0x6c, 0x39, 0x61,
	0xd7, 0x09, 0x98, 0x9b, 0x29, 0x34, 0x2c, 0x77, 0xd3, 0x48, 0xaf, 0x2d, 0x7e, 0xa3, 0xc1, 0x94,
	0x50, 0x7f, 0x41, 0x67, 0x5f, 0x80, 0xb9, 0xac, 0x2b, 0x81, 0x95, 0x51, 0xcc, 0xc1, 0xc5, 0x6a,
	0xf3, 0xb2, 0xc7, 0xd9, 0xaf, 0x36, 0xb1, 0x04, 0xa4, 0x5e, 0x93, 0x0f, 0x7c, 0x48, 0x8c, 0x7c,
	0xf1, 0xcf, 0x35, 0x98, 0x11, 0x2f, 0x1c, 0x77, 0x69, 0x42, 0x98, 0x52, 0x73, 0x0d, 0x2e, 0xf3,
	0x16, 0x7e, 0x12, 0x62, 0x6d, 0x02, 0x41, 0x57, 0x61, 0x5e, 0xda, 0xe3, 0x51, 0x37, 0xed, 0x26,
	0xc4, 0x95, 0x5d, 0x81, 0x96, 0xb4, 0x03, 0x4d, 0x0e, 0xea, 0x35, 0xe5, 0x04, 0x6b, 0xaf, 0x62,
	0x3f, 0x21, 0xfe, 0xed, 0x08, 0xa6, 0x45, 0xea, 0xf6, 0xb2, 0x9b, 0xfe, 0xca, 0xc4, 0xa4, 0x45,
	0xa0, 0x4c, 0xde, 0x6e, 0xa1, 0x76, 0x37, 0x70, 0x12, 0x19, 0x61, 0xb4, 0xfd, 0x51, 0x94, 0xa0,
	0x23, 0xe2, 0x0d, 0x17, 0xef, 0x72, 0x87, 0xdb, 0x0b, 0x95, 0x28, 0x5a, 0x80, 0x31, 0xe6, 0x24,
	0xf5, 0x21, 0xf2, 0x7b, 0xcb, 0x77, 0x77, 0xd1, 0x2b, 0x5d, 0xbb, 0xff, 0xef, 0x3f, 0x80, 0x0b,
	0x95, 0x94, 0xd7, 0x51, 0x62, 0x7c, 0x55, 0x7a, 0x38, 0x21, 0x3c, 0x66, 0x35, 0xee, 0x4a, 0xcf,
	0xfc, 0xaa, 0xb7, 0xb7, 0xad, 0xa5, 0xb3, 0x74, 0xc7, 0xb1, 0x39, 0x64, 0x44, 0x30, 0x2d, 0x7b,
	0x59, 0x6a, 0xbc, 0x5d, 0x9d, 0x49, 0xf1, 0xaa, 0xb5, 0xb5, 0x38, 0x68, 0x57, 0x8a, 0xf0, 0x15,
	0x5c, 0x52, 0xbc, 0x2b, 0x35, 0x24, 0x29, 0x42, 0xf5, 0xcb, 0xd6, 0xd6, 0xdd, 0x33, 0xf4, 0xce,
	0x58, 0x95, 0xbd, 0x43, 0x95, 0xb1, 0xaa, 0x78, 0xda, 0x2a, 0x63, 0x55, 0xf9, 0xb4, 0x95, 0x22,
	0x94, 0xbd, 0x57, 0x94, 0x21, 0x54, 0xbc, 0xc7, 0x94, 0x21, 0x54, 0x3d, 0x81, 0x34, 0x87, 0x8c,
	0x4f, 0xf9, 0x3b, 0xc0, 0xac, 0xc9, 0x30, 0x15, 0xcf, 0x77, 0x84, 0x37, 0x53, 0xad, 0x37, 0x4e,
	0xed, 0x43, 0xe7, 0x76, 0x41, 0x2f, 0xbf, 0xbd, 0x33, 0xde, 0x92, 0xdf, 0x7c, 0x97, 0x31, 0xdc,
	0x18, 0xa4, 0x1b, 0x45, 0xd2, 0x85, 0x19, 0xf9, 0xa3, 0x38, 0xe3, 0xb6, 0xf4, 0xed, 0x82, 0xfc,
	0xd9, 0x5e, 0xeb, 0xce, 0xe0, 0x9d, 0x29, 0x5a, 0x1f, 0x8c, 0xea, 0x3b, 0x37, 0xe3, 0xa6, 0x8c,
	0x6c, 0xc9, 0x93, 0xbc, 0xd6, 0xad, 0xc1, 0x3a, 0x52, 0x54, 0x45, 0x63, 0x2f, 0xbc, 0x79, 0x3b,
	0xc5, 0xd8, 0xcb, 0x8f, 0xec, 0x4e, 0x31, 0xf6, 0xca, 0x73, 0x3a, 0x73, 0xc8, 0xd8, 0x83, 0xa6,
	0xf0, 0x78, 0xc8, 0xb8, 0x26, 0xb1, 0xa0, 0xc2, 0x7b, 0xba, 0xd6, 0xf5, 0x53, 0x7a, 0xd0, 0x59,
	0xbf, 0xd6, 0xa0, 0xa5, 0x7e, 0x58, 0x66, 0xdc, 0x53, 0xe8, 0x97, 0xea, 0x7d, 0x5c, 0xeb, 0x07,
	0x67, 0x1b, 0x40, 0x69, 0x48, 0x68, 0x5a, 0xb0, 0xfa, 0xda, 0xcb, 0x58, 0x3c, 0xc5, 0x23, 0x0a,
	0xaf, 0xd4, 0x5a, 0xb7, 0x07, 0xee, 0x9b, 0x79, 0x32, 0xc5, 0x83, 0x2c, 0x99, 0x27, 0x53, 0xbf,
	0x16, 0x6b, 0xdd, 0x3d, 0xd3, 0x4b, 0x2f, 0x73, 0xc8, 0x08, 0xe0, 0x82, 0xe4, 0xd1, 0x8f, 0x71,
	0x4b, 0x91, 0x20, 0xae, 0x3c, 0x33, 0x6a, 0xbd, 0x3d, 0x60, 0x4f, 0x41, 0x65, 0xa5, 0xef, 0x75,
	0x14, 0x2a, 0xab, 0x7a, 0x19, 0xa4, 0x50, 0x59, 0xe5, 0x53, 0x20, 0x91, 0xd5, 0xc2, 0x13, 0x02,
	0x35, 0xab, 0xe5, 0x47, 0x25, 0x6a, 0x56, 0x2b, 0xcf, 0x38, 0x72, 0x35, 0xaa, 0x3e, 0x58, 0x50,
	0xa8, 0x91, 0xf4, 0x21, 0x86, 0x42, 0x8d, 0xe4, 0xaf, 0x20, 0xcc, 0x21, 0xe3, 0x39, 0x2d, 0x22,
	0x29, 0x5e, 0x35, 0x1a, 0x37, 0xa4, 0x73, 0x54, 0x6e, 0x48, 0x5b, 0x37, 0x07, 0xea, 0x97, 0xf9,
	0x56, 0xf9, 0xa5, 0x9d, 0xa1, 0x24, 0x58, 0x72, 0xf5, 0xd8, 0xba, 0x33, 0x78, 0x67, 0x41, 0xa4,
	0xd5, 0x42, 0x7a, 0x85, 0x48, 0xa5, 0xf5, 0xfc, 0x0a, 0x91, 0xca, 0xab, 0xf3, 0x99, 0x48, 0x2b,
	0x05, 0xed, 0x32, 0x91, 0xca, 0x8a, 0xed, 0x65, 0x22, 0x95, 0x57, 0xc7, 0x53, 0x3c, 0x95, 0x62,
	0x76, 0x19, 0x1e, 0x59, 0x11, 0xbd, 0x0c, 0x8f, 0xbc, 0x32, 0x3e, 0x0b, 0x24, 0x2a, 0x65, 0xee,
	0x8a, 0x40, 0x42, 0x56, 0x43, 0xdf, 0x5a, 0x1c, 0xb4, 0xab, 0x60, 0xf2, 0xd2, 0xda, 0x71, 0x85,
	0xc9, 0xab, 0x0a, 0xd7, 0x15, 0x26, 0xaf, 0x2c, 0x4b, 0xcf, 0xfd, 0xaa, 0xac, 0xe8, 0x53, 0xe1,
	0x57, 0x15, 0x45, 0xb0, 0x0a, 0xbf, 0xaa, 0xaa, 0x26, 0x65, 0xce, 0x46, 0x52, 0x9f, 0x29, 0x73,
	0x36, 0xf2, 0xc2, 0xd0, 0xd6, 0xdb, 0x03, 0xf6, 0xcc, 0xb0, 0x49, 0x0a, 0x39, 0x65, 0xd8, 0xe4,
	0x95, 0xa1, 0xad, 0xb7, 0x07, 0xec, 0x29, 0x48, 0x55, 0x56, 0xb6, 0xa9, 0x90, 0xaa, 0xa2, 0x8c,
	0x54, 0x21, 0x55, 0x55, 0x3d, 0x28, 0xc3, 0xac, 0xa8, 0x87, 0x94, 0x61, 0x56, 0x97, 0x6e, 0xca,
	0x30, 0xf7, 0x29, 0xb4, 0x34, 0x87, 0x8c, 0x67, 0x30, 0x21, 0x16, 0x0f, 0x1a, 0xd7, 0x4f, 0x2d,
	0x7a, 0x6c, 0x99, 0xa7, 0x75, 0xa1, 0x13, 0xff, 0x96, 0x2c, 0x45, 0x9f, 0xf9, 0xee, 0x25, 0xb5,
	0x80, 0x64, 0xe5, 0x8a, 0xad, 0x7b, 0x67, 0xea, 0x4f, 0xf1, 0x7f, 0x0e, 0xe7, 0x8a, 0x45, 0x83,
	0xc6, 0x1b, 0xd2, 0x49, 0x8a, 0xa5, 0x89, 0xad, 0x37, 0x4f, 0xef, 0x94, 0xad, 0x98, 0xa2, 0xd2,
	0x4f, 0xb6, 0x62, 0xea, 0x22, 0x42, 0xd9, 0x8a, 0xf5, 0x2b, 0x21, 0x1c, 0x32, 0xbe, 0x61, 0x35,
	0xca, 0xaa, 0x22, 0x33, 0xe3, 0x07, 0xfd, 0xf6, 0x3b, 0x59, 0x89, 0x5c, 0xeb, 0x97, 0xce, 0x38,
	0x22, 0x13, 0x80, 0xa2, 0x28, 0x4c, 0x26, 0x00, 0x75, 0x65, 0x9a, 0x4c, 0x00, 0xfd, 0xaa, 0xcd,
	0x58, 0x30, 0xad, 0xae, 0x09, 0x93, 0x05, 0xd3, 0x7d, 0xcb, 0xd3, 0x64, 0xc1, 0xf4, 0x29, 0x25,
	0x67, 0x94, 0x7b, 0x45, 0x19, 0x99, 0x8c, 0x7b, 0x75, 0x7d, 0x9a, 0x8c, 0xfb, 0x7e, 0xf5, 0x69,
	0xd4, 0xae, 0x94, 0x55, 0x64, 0x32, 0xbb, 0xea, 0x57, 0xce, 0x26, 0xb3, 0xab, 0xbe, 0x25, 0x6a,
	0x5c, 0xfa, 0xea, 0x72, 0x1e, 0x99, 0xf4, 0xfb, 0x56, 0x20, 0xc9, 0xa4, 0xdf, 0xbf, 0x5a, 0x88,
	0x6d, 0x0b, 0x92, 0xea, 0x07, 0xd9, 0xb6, 0x20, 0x2f, 0xc5, 0x90, 0x6d, 0x0b, 0xaa, 0x72, 0x8a,
	0x5e, 0x7c, 0x7d, 0x3a, 0xb6, 0xf5, 0x81, 0xb1, 0xad, 0x2b, 0xb1, 0x7d, 0x05, 0xb3, 0xaa, 0xba,
	0x1c, 0xe3, 0xee, 0x59, 0x6a, 0x78, 0xa4, 0x71, 0x45, 0xbf, 0x92, 0x1f, 0x76, 0xca, 0xaf, 0x56,
	0x66, 0x18, 0x37, 0x95, 0x3e, 0xb1, 0x58, 0x60, 0xd2, 0xba, 0x35, 0x58, 0xc7, 0x2c, 0xe8, 0x95,
	0xd6, 0x55, 0xc8, 0x82, 0x5e, 0x55, 0xb9, 0x86, 0x2c, 0xe8, 0x55, 0x17, 0x6b, 0x64, 0x41, 0x62,
	0xb5, 0x06, 0x43, 0xbe, 0x40, 0xb2, 0xb2, 0x0e, 0x45, 0x90, 0x28, 0xad, 0xa2, 0x60, 0xd1, 0x6f,
	0xa5, 0x80, 0x41, 0x16, 0xfd, 0xca, 0xea, 0x21, 0x64, 0xd1, 0xaf, 0xbc, 0x1a, 0x22, 0x3b, 0xb8,
	0x48, 0x6e, 0xdb, 0x15, 0x07, 0x17, 0xf9, 0xa5, 0x7d, 0xeb, 0xce, 0xe0, 0x9d, 0x45, 0x79, 0x96,
	0x2f, 0x79, 0x55, 0xf2, 0x94, 0xdc, 0x13, 0xab, 0xe4, 0x29, 0xbb, 0x37, 0x2e, 0xf2, 0x59, 0xbc,
	0x7c, 0xef, 0xc7, 0x67, 0xa5, 0x42, 0xa0, 0x1f, 0x9f, 0xd5, 0x3b, 0x7d, 0x51, 0x6f, 0x8a, 0x05,
	0x24, 0x6a, 0xbd, 0xa9, 0x94, 0xa5, 0xa8, 0xf5, 0xa6, 0x5a, 0x93, 0x92, 0x23, 0xac, 0x5c, 0x65,
	0x1b, 0xca, 0x93, 0x7a, 0xe5, 0x36, 0xbc, 0xb5, 0x38, 0x68, 0xd7, 0x2c, 0x2d, 0x5a, 0xb8, 0x98,
	0x96, 0xa5, 0x45, 0xcb, 0xf7, 0xe3, 0xb2, 0xb4, 0x68, 0xf5, 0x76, 0x9b, 0x7a, 0x34, 0xd5, 0x75,
	0xb5, 0xcc, 0xa3, 0xf5, 0xb9, 0x0b, 0x6f, 0x2d, 0x9d, 0xa5, 0x3b, 0x41, 0xbe, 0xf2, 0xc3, 0x4f,
	0x1f, 0xb4, 0xa3, 0xc0, 0x09, 0xdb, 0x4b, 0xef, 0xde, 0x4f, 0xd3, 0x25, 0x37, 0xea, 0xdc, 0xa3,
	0xff, 0xdd, 0xd4, 0x8d, 0x82, 0x7b, 0x18, 0x25, 0x47, 0xbe, 0x8b, 0xb0, 0xfc, 0xbf, 0xa0, 0x1e,
	0x8c, 0xd1, 0x8e, 0xef, 0xfc, 0x7f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xb6, 0xd9, 0x6d, 0xe0, 0x4a,
	0x55, 0x00, 0x00,
}
