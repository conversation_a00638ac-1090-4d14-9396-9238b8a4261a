// Code generated by protoc-gen-go. DO NOT EDIT.
// source: playmate/rcmd_playmate_tf.proto

package tf_serving // import "golang.52tt.com/protocol/services/playmate/tf-serving"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Request struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ctx                  uint64   `protobuf:"varint,2,opt,name=ctx,proto3" json:"ctx,omitempty"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Request) Reset()         { *m = Request{} }
func (m *Request) String() string { return proto.CompactTextString(m) }
func (*Request) ProtoMessage()    {}
func (*Request) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_playmate_tf_ff684be1010589d1, []int{0}
}
func (m *Request) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Request.Unmarshal(m, b)
}
func (m *Request) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Request.Marshal(b, m, deterministic)
}
func (dst *Request) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Request.Merge(dst, src)
}
func (m *Request) XXX_Size() int {
	return xxx_messageInfo_Request.Size(m)
}
func (m *Request) XXX_DiscardUnknown() {
	xxx_messageInfo_Request.DiscardUnknown(m)
}

var xxx_messageInfo_Request proto.InternalMessageInfo

func (m *Request) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Request) GetCtx() uint64 {
	if m != nil {
		return m.Ctx
	}
	return 0
}

func (m *Request) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

type Response struct {
	Code                 uint64   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data                 string   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_playmate_tf_ff684be1010589d1, []int{1}
}
func (m *Response) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Response.Unmarshal(m, b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Response.Marshal(b, m, deterministic)
}
func (dst *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(dst, src)
}
func (m *Response) XXX_Size() int {
	return xxx_messageInfo_Response.Size(m)
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetCode() uint64 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *Response) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *Response) GetData() string {
	if m != nil {
		return m.Data
	}
	return ""
}

func init() {
	proto.RegisterType((*Request)(nil), "tfserving.Request")
	proto.RegisterType((*Response)(nil), "tfserving.Response")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TfServingClient is the client API for TfServing service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TfServingClient interface {
	// grpcurl -plaintext -d '{"uid":435346, "ctx":1,"data": "{\"img\":\"./scripts/test_good_img.jpg\"}"}' localhost:8000 tfserving.TfServing/Predict
	Predict(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
}

type tfServingClient struct {
	cc *grpc.ClientConn
}

func NewTfServingClient(cc *grpc.ClientConn) TfServingClient {
	return &tfServingClient{cc}
}

func (c *tfServingClient) Predict(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/tfserving.TfServing/Predict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TfServingServer is the server API for TfServing service.
type TfServingServer interface {
	// grpcurl -plaintext -d '{"uid":435346, "ctx":1,"data": "{\"img\":\"./scripts/test_good_img.jpg\"}"}' localhost:8000 tfserving.TfServing/Predict
	Predict(context.Context, *Request) (*Response, error)
}

func RegisterTfServingServer(s *grpc.Server, srv TfServingServer) {
	s.RegisterService(&_TfServing_serviceDesc, srv)
}

func _TfServing_Predict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TfServingServer).Predict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tfserving.TfServing/Predict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TfServingServer).Predict(ctx, req.(*Request))
	}
	return interceptor(ctx, in, info, handler)
}

var _TfServing_serviceDesc = grpc.ServiceDesc{
	ServiceName: "tfserving.TfServing",
	HandlerType: (*TfServingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Predict",
			Handler:    _TfServing_Predict_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "playmate/rcmd_playmate_tf.proto",
}

func init() {
	proto.RegisterFile("playmate/rcmd_playmate_tf.proto", fileDescriptor_rcmd_playmate_tf_ff684be1010589d1)
}

var fileDescriptor_rcmd_playmate_tf_ff684be1010589d1 = []byte{
	// 228 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x90, 0xc1, 0x4a, 0x03, 0x31,
	0x10, 0x86, 0x59, 0x5b, 0xac, 0x9b, 0x93, 0xc4, 0xcb, 0xe2, 0xc5, 0xd2, 0x53, 0x2f, 0x26, 0xb0,
	0x52, 0x3c, 0x8a, 0xe2, 0x03, 0x48, 0xf4, 0xe4, 0xa5, 0xc4, 0xc9, 0x24, 0x2c, 0x6c, 0x36, 0x6b,
	0x32, 0x95, 0xfa, 0xf6, 0x92, 0xec, 0x5a, 0x10, 0xbc, 0x7d, 0xf9, 0x33, 0x7c, 0xf9, 0x33, 0xec,
	0x66, 0xec, 0xf5, 0xb7, 0xd7, 0x84, 0x32, 0x82, 0x37, 0xfb, 0xdf, 0xd3, 0x9e, 0xac, 0x18, 0x63,
	0xa0, 0xc0, 0x6b, 0xb2, 0x09, 0xe3, 0x57, 0x37, 0xb8, 0xcd, 0x23, 0x5b, 0x29, 0xfc, 0x3c, 0x60,
	0x22, 0x7e, 0xc9, 0x16, 0x87, 0xce, 0x34, 0xd5, 0xba, 0xda, 0x2e, 0x55, 0xc6, 0x9c, 0x00, 0x1d,
	0x9b, 0xb3, 0x29, 0x01, 0x3a, 0x72, 0xce, 0x96, 0x46, 0x93, 0x6e, 0x16, 0xeb, 0x6a, 0x5b, 0xab,
	0xc2, 0x9b, 0x67, 0x76, 0xa1, 0x30, 0x8d, 0x61, 0x48, 0x98, 0xef, 0x21, 0x18, 0x9c, 0x25, 0x85,
	0xb3, 0xc5, 0x27, 0x57, 0x2c, 0xb5, 0xca, 0xf8, 0x9f, 0xa5, 0x7d, 0x60, 0xf5, 0x9b, 0x7d, 0x9d,
	0x5a, 0xf1, 0x96, 0xad, 0x5e, 0x22, 0x9a, 0x0e, 0x88, 0x73, 0x71, 0x2a, 0x2b, 0xe6, 0xa6, 0xd7,
	0x57, 0x7f, 0xb2, 0xe9, 0xe9, 0xa7, 0xfb, 0xf7, 0x9d, 0x0b, 0xbd, 0x1e, 0x9c, 0xd8, 0xb5, 0x44,
	0x02, 0x82, 0x97, 0xe5, 0xb7, 0x10, 0x7a, 0x59, 0x86, 0x01, 0x93, 0x3c, 0x6d, 0x86, 0xec, 0xed,
	0x6c, 0xf8, 0x38, 0x2f, 0x63, 0x77, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xc7, 0x92, 0x26, 0x85,
	0x37, 0x01, 0x00, 0x00,
}
