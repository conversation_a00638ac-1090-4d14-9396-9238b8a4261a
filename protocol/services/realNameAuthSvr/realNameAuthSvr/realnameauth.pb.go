// Code generated by protoc-gen-gogo.
// source: src/realNameAuthSvr/realnameauth.proto
// DO NOT EDIT!

/*
	Package realnameauth is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/realNameAuthSvr/realnameauth.proto

	It has these top-level messages:
		GetIsEnableCheckReq
		GetIsEnableCheckResp
		AddAuthOperHistoryReq
		AddAuthOperHistoryResp
		AuthInfoReq
		AuthInfoResp
		GetAuthInfoReq
		GetAuthInfoResp
		GetUserIdentityExpireDateReq
		GetUserIdentityExpireDateResp
		GetUserIdentityInfoReq
		GetUserIdentityInfoResp
		AddUserIdentityInfoReq
		AddUserIdentityInfoResp
		UpdateUserIdentityInfoReq
		UpdateUserIdentityInfoResp
		AddOrUpdateUserIdentityInfoReq
		AddOrUpdateUserIdentityInfoResp
		DelUserIdentityInfoReq
		DelUserIdentityInfoResp
		AddIdentityExpireUserPushTimeReq
		AddIdentityExpireUserPushTimeResp
		RecordUserAuthPhoneReq
		RecordUserAuthPhoneResp
		GetUserAuthPhoneReq
		GetUserAuthPhoneResp
		GetPhoneBindNumberReq
		GetPhoneBindNumberResp
		RecordUserIdentityInfoReq
		RecordUserIdentityInfoResp
		ParentGuardianSwitchReq
		ParentGuardianSwitchResp
		ParentGuardianStateReq
		ParentGuardianStateResp
		RealNameAuthInfo
		GetRealNameAuthTokenReq
		GetRealNameAuthTokenResp
		ApplyRealNameAuthDataReq
		ApplyRealNameAuthDataResp
		GetRealNameApplyInfoReq
		GetRealNameApplyInfoResp
*/
package realnameauth

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type EOperType int32

const (
	EOperType_ENUM_REALNAME_OPER_SET   EOperType = 1
	EOperType_ENUM_REALNAME_OPER_UNSET EOperType = 2
	EOperType_ENUM_REALNAME_OPER_RESET EOperType = 3
)

var EOperType_name = map[int32]string{
	1: "ENUM_REALNAME_OPER_SET",
	2: "ENUM_REALNAME_OPER_UNSET",
	3: "ENUM_REALNAME_OPER_RESET",
}
var EOperType_value = map[string]int32{
	"ENUM_REALNAME_OPER_SET":   1,
	"ENUM_REALNAME_OPER_UNSET": 2,
	"ENUM_REALNAME_OPER_RESET": 3,
}

func (x EOperType) Enum() *EOperType {
	p := new(EOperType)
	*p = x
	return p
}
func (x EOperType) String() string {
	return proto.EnumName(EOperType_name, int32(x))
}
func (x *EOperType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EOperType_value, data, "EOperType")
	if err != nil {
		return err
	}
	*x = EOperType(value)
	return nil
}
func (EOperType) EnumDescriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{0} }

type EAuthType int32

const (
	EAuthType_ENUM_REALNAME_AUTH_PHONE EAuthType = 1
)

var EAuthType_name = map[int32]string{
	1: "ENUM_REALNAME_AUTH_PHONE",
}
var EAuthType_value = map[string]int32{
	"ENUM_REALNAME_AUTH_PHONE": 1,
}

func (x EAuthType) Enum() *EAuthType {
	p := new(EAuthType)
	*p = x
	return p
}
func (x EAuthType) String() string {
	return proto.EnumName(EAuthType_name, int32(x))
}
func (x *EAuthType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EAuthType_value, data, "EAuthType")
	if err != nil {
		return err
	}
	*x = EAuthType(value)
	return nil
}
func (EAuthType) EnumDescriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{1} }

type GetIsEnableCheckReq struct {
}

func (m *GetIsEnableCheckReq) Reset()                    { *m = GetIsEnableCheckReq{} }
func (m *GetIsEnableCheckReq) String() string            { return proto.CompactTextString(m) }
func (*GetIsEnableCheckReq) ProtoMessage()               {}
func (*GetIsEnableCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{0} }

type GetIsEnableCheckResp struct {
	IsEnableCheck bool `protobuf:"varint,1,req,name=is_enable_check,json=isEnableCheck" json:"is_enable_check"`
}

func (m *GetIsEnableCheckResp) Reset()                    { *m = GetIsEnableCheckResp{} }
func (m *GetIsEnableCheckResp) String() string            { return proto.CompactTextString(m) }
func (*GetIsEnableCheckResp) ProtoMessage()               {}
func (*GetIsEnableCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{1} }

func (m *GetIsEnableCheckResp) GetIsEnableCheck() bool {
	if m != nil {
		return m.IsEnableCheck
	}
	return false
}

type AddAuthOperHistoryReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OperType uint32 `protobuf:"varint,2,req,name=oper_type,json=operType" json:"oper_type"`
	AuthType uint32 `protobuf:"varint,3,req,name=auth_type,json=authType" json:"auth_type"`
	LogInfo  string `protobuf:"bytes,4,req,name=log_info,json=logInfo" json:"log_info"`
}

func (m *AddAuthOperHistoryReq) Reset()         { *m = AddAuthOperHistoryReq{} }
func (m *AddAuthOperHistoryReq) String() string { return proto.CompactTextString(m) }
func (*AddAuthOperHistoryReq) ProtoMessage()    {}
func (*AddAuthOperHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{2}
}

func (m *AddAuthOperHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddAuthOperHistoryReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *AddAuthOperHistoryReq) GetAuthType() uint32 {
	if m != nil {
		return m.AuthType
	}
	return 0
}

func (m *AddAuthOperHistoryReq) GetLogInfo() string {
	if m != nil {
		return m.LogInfo
	}
	return ""
}

type AddAuthOperHistoryResp struct {
}

func (m *AddAuthOperHistoryResp) Reset()         { *m = AddAuthOperHistoryResp{} }
func (m *AddAuthOperHistoryResp) String() string { return proto.CompactTextString(m) }
func (*AddAuthOperHistoryResp) ProtoMessage()    {}
func (*AddAuthOperHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{3}
}

// add 2018/11/15/11:15 by T1035
type AuthInfoReq struct {
	Uid    uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Status int32  `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *AuthInfoReq) Reset()                    { *m = AuthInfoReq{} }
func (m *AuthInfoReq) String() string            { return proto.CompactTextString(m) }
func (*AuthInfoReq) ProtoMessage()               {}
func (*AuthInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{4} }

func (m *AuthInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AuthInfoReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type AuthInfoResp struct {
}

func (m *AuthInfoResp) Reset()                    { *m = AuthInfoResp{} }
func (m *AuthInfoResp) String() string            { return proto.CompactTextString(m) }
func (*AuthInfoResp) ProtoMessage()               {}
func (*AuthInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{5} }

type GetAuthInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetAuthInfoReq) Reset()                    { *m = GetAuthInfoReq{} }
func (m *GetAuthInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetAuthInfoReq) ProtoMessage()               {}
func (*GetAuthInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{6} }

func (m *GetAuthInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAuthInfoResp struct {
	// 认证状态 -1 未认证;0 待审核; 1 审核通过
	// 2 审核未通过;-5204 redis 中无该用户数据，前往欢城查询
	// 忘记给1 占坑了。如果改回1，会导致线上上一个版本的 pb 解析失败
	Status       int32             `protobuf:"varint,2,req,name=status" json:"status"`
	RealnameInfo *RealNameAuthInfo `protobuf:"bytes,3,opt,name=realname_info,json=realnameInfo" json:"realname_info,omitempty"`
}

func (m *GetAuthInfoResp) Reset()                    { *m = GetAuthInfoResp{} }
func (m *GetAuthInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetAuthInfoResp) ProtoMessage()               {}
func (*GetAuthInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{7} }

func (m *GetAuthInfoResp) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetAuthInfoResp) GetRealnameInfo() *RealNameAuthInfo {
	if m != nil {
		return m.RealnameInfo
	}
	return nil
}

// ////////////////////////////////////////////////////////////
type GetUserIdentityExpireDateReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserIdentityExpireDateReq) Reset()         { *m = GetUserIdentityExpireDateReq{} }
func (m *GetUserIdentityExpireDateReq) String() string { return proto.CompactTextString(m) }
func (*GetUserIdentityExpireDateReq) ProtoMessage()    {}
func (*GetUserIdentityExpireDateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{8}
}

func (m *GetUserIdentityExpireDateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserIdentityExpireDateResp struct {
	IdentityExpireDate string `protobuf:"bytes,1,opt,name=identity_expire_date,json=identityExpireDate" json:"identity_expire_date"`
}

func (m *GetUserIdentityExpireDateResp) Reset()         { *m = GetUserIdentityExpireDateResp{} }
func (m *GetUserIdentityExpireDateResp) String() string { return proto.CompactTextString(m) }
func (*GetUserIdentityExpireDateResp) ProtoMessage()    {}
func (*GetUserIdentityExpireDateResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{9}
}

func (m *GetUserIdentityExpireDateResp) GetIdentityExpireDate() string {
	if m != nil {
		return m.IdentityExpireDate
	}
	return ""
}

type GetUserIdentityInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserIdentityInfoReq) Reset()         { *m = GetUserIdentityInfoReq{} }
func (m *GetUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserIdentityInfoReq) ProtoMessage()    {}
func (*GetUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{10}
}

func (m *GetUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserIdentityInfoResp struct {
	// 未认证/认证未通过/审核中/该功能上线前已认证的用户，数据库没有他们的身份证信息
	// 该功能上线前已认证的用户的身份证信息会逐步补充
	HaveInfo          bool   `protobuf:"varint,1,req,name=have_info,json=haveInfo" json:"have_info"`
	IsAdult           bool   `protobuf:"varint,3,req,name=is_adult,json=isAdult" json:"is_adult"`
	IdentityNumber    string `protobuf:"bytes,4,opt,name=identity_number,json=identityNumber" json:"identity_number"`
	IdentityValidTime string `protobuf:"bytes,5,opt,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
}

func (m *GetUserIdentityInfoResp) Reset()         { *m = GetUserIdentityInfoResp{} }
func (m *GetUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserIdentityInfoResp) ProtoMessage()    {}
func (*GetUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{11}
}

func (m *GetUserIdentityInfoResp) GetHaveInfo() bool {
	if m != nil {
		return m.HaveInfo
	}
	return false
}

func (m *GetUserIdentityInfoResp) GetIsAdult() bool {
	if m != nil {
		return m.IsAdult
	}
	return false
}

func (m *GetUserIdentityInfoResp) GetIdentityNumber() string {
	if m != nil {
		return m.IdentityNumber
	}
	return ""
}

func (m *GetUserIdentityInfoResp) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

type AddUserIdentityInfoReq struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IdentityNumber    string `protobuf:"bytes,2,req,name=identity_number,json=identityNumber" json:"identity_number"`
	IdentityValidTime string `protobuf:"bytes,3,req,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
	HavePush          int32  `protobuf:"varint,4,req,name=have_push,json=havePush" json:"have_push"`
	Name              string `protobuf:"bytes,5,opt,name=name" json:"name"`
}

func (m *AddUserIdentityInfoReq) Reset()         { *m = AddUserIdentityInfoReq{} }
func (m *AddUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddUserIdentityInfoReq) ProtoMessage()    {}
func (*AddUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{12}
}

func (m *AddUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserIdentityInfoReq) GetIdentityNumber() string {
	if m != nil {
		return m.IdentityNumber
	}
	return ""
}

func (m *AddUserIdentityInfoReq) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

func (m *AddUserIdentityInfoReq) GetHavePush() int32 {
	if m != nil {
		return m.HavePush
	}
	return 0
}

func (m *AddUserIdentityInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type AddUserIdentityInfoResp struct {
}

func (m *AddUserIdentityInfoResp) Reset()         { *m = AddUserIdentityInfoResp{} }
func (m *AddUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddUserIdentityInfoResp) ProtoMessage()    {}
func (*AddUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{13}
}

type UpdateUserIdentityInfoReq struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IdentityNumber    string `protobuf:"bytes,2,opt,name=identity_number,json=identityNumber" json:"identity_number"`
	IdentityValidTime string `protobuf:"bytes,3,opt,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
	HavePush          int32  `protobuf:"varint,4,req,name=have_push,json=havePush" json:"have_push"`
}

func (m *UpdateUserIdentityInfoReq) Reset()         { *m = UpdateUserIdentityInfoReq{} }
func (m *UpdateUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserIdentityInfoReq) ProtoMessage()    {}
func (*UpdateUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{14}
}

func (m *UpdateUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserIdentityInfoReq) GetIdentityNumber() string {
	if m != nil {
		return m.IdentityNumber
	}
	return ""
}

func (m *UpdateUserIdentityInfoReq) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

func (m *UpdateUserIdentityInfoReq) GetHavePush() int32 {
	if m != nil {
		return m.HavePush
	}
	return 0
}

type UpdateUserIdentityInfoResp struct {
}

func (m *UpdateUserIdentityInfoResp) Reset()         { *m = UpdateUserIdentityInfoResp{} }
func (m *UpdateUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserIdentityInfoResp) ProtoMessage()    {}
func (*UpdateUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{15}
}

type AddOrUpdateUserIdentityInfoReq struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IdentityNumber    string `protobuf:"bytes,2,opt,name=identity_number,json=identityNumber" json:"identity_number"`
	IdentityValidTime string `protobuf:"bytes,3,opt,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
	HavePush          int32  `protobuf:"varint,4,req,name=have_push,json=havePush" json:"have_push"`
	Name              string `protobuf:"bytes,5,opt,name=name" json:"name"`
}

func (m *AddOrUpdateUserIdentityInfoReq) Reset()         { *m = AddOrUpdateUserIdentityInfoReq{} }
func (m *AddOrUpdateUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*AddOrUpdateUserIdentityInfoReq) ProtoMessage()    {}
func (*AddOrUpdateUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{16}
}

func (m *AddOrUpdateUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddOrUpdateUserIdentityInfoReq) GetIdentityNumber() string {
	if m != nil {
		return m.IdentityNumber
	}
	return ""
}

func (m *AddOrUpdateUserIdentityInfoReq) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

func (m *AddOrUpdateUserIdentityInfoReq) GetHavePush() int32 {
	if m != nil {
		return m.HavePush
	}
	return 0
}

func (m *AddOrUpdateUserIdentityInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type AddOrUpdateUserIdentityInfoResp struct {
}

func (m *AddOrUpdateUserIdentityInfoResp) Reset()         { *m = AddOrUpdateUserIdentityInfoResp{} }
func (m *AddOrUpdateUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*AddOrUpdateUserIdentityInfoResp) ProtoMessage()    {}
func (*AddOrUpdateUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{17}
}

type DelUserIdentityInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *DelUserIdentityInfoReq) Reset()         { *m = DelUserIdentityInfoReq{} }
func (m *DelUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelUserIdentityInfoReq) ProtoMessage()    {}
func (*DelUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{18}
}

func (m *DelUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelUserIdentityInfoResp struct {
}

func (m *DelUserIdentityInfoResp) Reset()         { *m = DelUserIdentityInfoResp{} }
func (m *DelUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelUserIdentityInfoResp) ProtoMessage()    {}
func (*DelUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{19}
}

type AddIdentityExpireUserPushTimeReq struct {
	Uid      int32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	PushTime int32 `protobuf:"varint,2,req,name=push_time,json=pushTime" json:"push_time"`
}

func (m *AddIdentityExpireUserPushTimeReq) Reset()         { *m = AddIdentityExpireUserPushTimeReq{} }
func (m *AddIdentityExpireUserPushTimeReq) String() string { return proto.CompactTextString(m) }
func (*AddIdentityExpireUserPushTimeReq) ProtoMessage()    {}
func (*AddIdentityExpireUserPushTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{20}
}

func (m *AddIdentityExpireUserPushTimeReq) GetUid() int32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddIdentityExpireUserPushTimeReq) GetPushTime() int32 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

type AddIdentityExpireUserPushTimeResp struct {
}

func (m *AddIdentityExpireUserPushTimeResp) Reset()         { *m = AddIdentityExpireUserPushTimeResp{} }
func (m *AddIdentityExpireUserPushTimeResp) String() string { return proto.CompactTextString(m) }
func (*AddIdentityExpireUserPushTimeResp) ProtoMessage()    {}
func (*AddIdentityExpireUserPushTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{21}
}

type RecordUserAuthPhoneReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	AuthPhone string `protobuf:"bytes,2,req,name=auth_phone,json=authPhone" json:"auth_phone"`
}

func (m *RecordUserAuthPhoneReq) Reset()         { *m = RecordUserAuthPhoneReq{} }
func (m *RecordUserAuthPhoneReq) String() string { return proto.CompactTextString(m) }
func (*RecordUserAuthPhoneReq) ProtoMessage()    {}
func (*RecordUserAuthPhoneReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{22}
}

func (m *RecordUserAuthPhoneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordUserAuthPhoneReq) GetAuthPhone() string {
	if m != nil {
		return m.AuthPhone
	}
	return ""
}

type RecordUserAuthPhoneResp struct {
}

func (m *RecordUserAuthPhoneResp) Reset()         { *m = RecordUserAuthPhoneResp{} }
func (m *RecordUserAuthPhoneResp) String() string { return proto.CompactTextString(m) }
func (*RecordUserAuthPhoneResp) ProtoMessage()    {}
func (*RecordUserAuthPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{23}
}

type GetUserAuthPhoneReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserAuthPhoneReq) Reset()                    { *m = GetUserAuthPhoneReq{} }
func (m *GetUserAuthPhoneReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserAuthPhoneReq) ProtoMessage()               {}
func (*GetUserAuthPhoneReq) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{24} }

func (m *GetUserAuthPhoneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserAuthPhoneResp struct {
	HavePhone bool   `protobuf:"varint,1,req,name=have_phone,json=havePhone" json:"have_phone"`
	AuthPhone string `protobuf:"bytes,2,opt,name=auth_phone,json=authPhone" json:"auth_phone"`
}

func (m *GetUserAuthPhoneResp) Reset()         { *m = GetUserAuthPhoneResp{} }
func (m *GetUserAuthPhoneResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAuthPhoneResp) ProtoMessage()    {}
func (*GetUserAuthPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{25}
}

func (m *GetUserAuthPhoneResp) GetHavePhone() bool {
	if m != nil {
		return m.HavePhone
	}
	return false
}

func (m *GetUserAuthPhoneResp) GetAuthPhone() string {
	if m != nil {
		return m.AuthPhone
	}
	return ""
}

type GetPhoneBindNumberReq struct {
	Phone string `protobuf:"bytes,1,req,name=phone" json:"phone"`
}

func (m *GetPhoneBindNumberReq) Reset()         { *m = GetPhoneBindNumberReq{} }
func (m *GetPhoneBindNumberReq) String() string { return proto.CompactTextString(m) }
func (*GetPhoneBindNumberReq) ProtoMessage()    {}
func (*GetPhoneBindNumberReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{26}
}

func (m *GetPhoneBindNumberReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type GetPhoneBindNumberResp struct {
	BindNumber uint32 `protobuf:"varint,1,req,name=bind_number,json=bindNumber" json:"bind_number"`
}

func (m *GetPhoneBindNumberResp) Reset()         { *m = GetPhoneBindNumberResp{} }
func (m *GetPhoneBindNumberResp) String() string { return proto.CompactTextString(m) }
func (*GetPhoneBindNumberResp) ProtoMessage()    {}
func (*GetPhoneBindNumberResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{27}
}

func (m *GetPhoneBindNumberResp) GetBindNumber() uint32 {
	if m != nil {
		return m.BindNumber
	}
	return 0
}

type RecordUserIdentityInfoReq struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	IdentityNum       string `protobuf:"bytes,2,req,name=identity_num,json=identityNum" json:"identity_num"`
	IdentityValidTime string `protobuf:"bytes,3,req,name=identity_valid_time,json=identityValidTime" json:"identity_valid_time"`
	Name              string `protobuf:"bytes,4,req,name=name" json:"name"`
}

func (m *RecordUserIdentityInfoReq) Reset()         { *m = RecordUserIdentityInfoReq{} }
func (m *RecordUserIdentityInfoReq) String() string { return proto.CompactTextString(m) }
func (*RecordUserIdentityInfoReq) ProtoMessage()    {}
func (*RecordUserIdentityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{28}
}

func (m *RecordUserIdentityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordUserIdentityInfoReq) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

func (m *RecordUserIdentityInfoReq) GetIdentityValidTime() string {
	if m != nil {
		return m.IdentityValidTime
	}
	return ""
}

func (m *RecordUserIdentityInfoReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type RecordUserIdentityInfoResp struct {
}

func (m *RecordUserIdentityInfoResp) Reset()         { *m = RecordUserIdentityInfoResp{} }
func (m *RecordUserIdentityInfoResp) String() string { return proto.CompactTextString(m) }
func (*RecordUserIdentityInfoResp) ProtoMessage()    {}
func (*RecordUserIdentityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{29}
}

type ParentGuardianSwitchReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OnOff    bool   `protobuf:"varint,2,req,name=on_off,json=onOff" json:"on_off"`
	Password string `protobuf:"bytes,3,req,name=password" json:"password"`
}

func (m *ParentGuardianSwitchReq) Reset()         { *m = ParentGuardianSwitchReq{} }
func (m *ParentGuardianSwitchReq) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianSwitchReq) ProtoMessage()    {}
func (*ParentGuardianSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{30}
}

func (m *ParentGuardianSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ParentGuardianSwitchReq) GetOnOff() bool {
	if m != nil {
		return m.OnOff
	}
	return false
}

func (m *ParentGuardianSwitchReq) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

type ParentGuardianSwitchResp struct {
}

func (m *ParentGuardianSwitchResp) Reset()         { *m = ParentGuardianSwitchResp{} }
func (m *ParentGuardianSwitchResp) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianSwitchResp) ProtoMessage()    {}
func (*ParentGuardianSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{31}
}

type ParentGuardianStateReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *ParentGuardianStateReq) Reset()         { *m = ParentGuardianStateReq{} }
func (m *ParentGuardianStateReq) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianStateReq) ProtoMessage()    {}
func (*ParentGuardianStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{32}
}

func (m *ParentGuardianStateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ParentGuardianStateResp struct {
	OnOff bool `protobuf:"varint,2,req,name=on_off,json=onOff" json:"on_off"`
}

func (m *ParentGuardianStateResp) Reset()         { *m = ParentGuardianStateResp{} }
func (m *ParentGuardianStateResp) String() string { return proto.CompactTextString(m) }
func (*ParentGuardianStateResp) ProtoMessage()    {}
func (*ParentGuardianStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{33}
}

func (m *ParentGuardianStateResp) GetOnOff() bool {
	if m != nil {
		return m.OnOff
	}
	return false
}

type RealNameAuthInfo struct {
	AuthPhone   string `protobuf:"bytes,1,opt,name=auth_phone,json=authPhone" json:"auth_phone"`
	Name        string `protobuf:"bytes,2,opt,name=name" json:"name"`
	IdentityNum string `protobuf:"bytes,3,opt,name=identity_num,json=identityNum" json:"identity_num"`
}

func (m *RealNameAuthInfo) Reset()                    { *m = RealNameAuthInfo{} }
func (m *RealNameAuthInfo) String() string            { return proto.CompactTextString(m) }
func (*RealNameAuthInfo) ProtoMessage()               {}
func (*RealNameAuthInfo) Descriptor() ([]byte, []int) { return fileDescriptorRealnameauth, []int{34} }

func (m *RealNameAuthInfo) GetAuthPhone() string {
	if m != nil {
		return m.AuthPhone
	}
	return ""
}

func (m *RealNameAuthInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RealNameAuthInfo) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

type GetRealNameAuthTokenReq struct {
	Uid          uint32            `protobuf:"varint,1,req,name=uid" json:"uid"`
	RealnameInfo *RealNameAuthInfo `protobuf:"bytes,2,req,name=realname_info,json=realnameInfo" json:"realname_info,omitempty"`
}

func (m *GetRealNameAuthTokenReq) Reset()         { *m = GetRealNameAuthTokenReq{} }
func (m *GetRealNameAuthTokenReq) String() string { return proto.CompactTextString(m) }
func (*GetRealNameAuthTokenReq) ProtoMessage()    {}
func (*GetRealNameAuthTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{35}
}

func (m *GetRealNameAuthTokenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRealNameAuthTokenReq) GetRealnameInfo() *RealNameAuthInfo {
	if m != nil {
		return m.RealnameInfo
	}
	return nil
}

type GetRealNameAuthTokenResp struct {
	FaceidToken string `protobuf:"bytes,1,req,name=faceid_token,json=faceidToken" json:"faceid_token"`
}

func (m *GetRealNameAuthTokenResp) Reset()         { *m = GetRealNameAuthTokenResp{} }
func (m *GetRealNameAuthTokenResp) String() string { return proto.CompactTextString(m) }
func (*GetRealNameAuthTokenResp) ProtoMessage()    {}
func (*GetRealNameAuthTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{36}
}

func (m *GetRealNameAuthTokenResp) GetFaceidToken() string {
	if m != nil {
		return m.FaceidToken
	}
	return ""
}

type ApplyRealNameAuthDataReq struct {
	Uid             uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	FaceidToken     string `protobuf:"bytes,2,req,name=faceid_token,json=faceidToken" json:"faceid_token"`
	FaceidCheckData string `protobuf:"bytes,3,req,name=faceid_check_data,json=faceidCheckData" json:"faceid_check_data"`
}

func (m *ApplyRealNameAuthDataReq) Reset()         { *m = ApplyRealNameAuthDataReq{} }
func (m *ApplyRealNameAuthDataReq) String() string { return proto.CompactTextString(m) }
func (*ApplyRealNameAuthDataReq) ProtoMessage()    {}
func (*ApplyRealNameAuthDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{37}
}

func (m *ApplyRealNameAuthDataReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyRealNameAuthDataReq) GetFaceidToken() string {
	if m != nil {
		return m.FaceidToken
	}
	return ""
}

func (m *ApplyRealNameAuthDataReq) GetFaceidCheckData() string {
	if m != nil {
		return m.FaceidCheckData
	}
	return ""
}

type ApplyRealNameAuthDataResp struct {
	ResultCode uint32 `protobuf:"varint,1,req,name=result_code,json=resultCode" json:"result_code"`
	ResultMsg  string `protobuf:"bytes,2,req,name=result_msg,json=resultMsg" json:"result_msg"`
}

func (m *ApplyRealNameAuthDataResp) Reset()         { *m = ApplyRealNameAuthDataResp{} }
func (m *ApplyRealNameAuthDataResp) String() string { return proto.CompactTextString(m) }
func (*ApplyRealNameAuthDataResp) ProtoMessage()    {}
func (*ApplyRealNameAuthDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{38}
}

func (m *ApplyRealNameAuthDataResp) GetResultCode() uint32 {
	if m != nil {
		return m.ResultCode
	}
	return 0
}

func (m *ApplyRealNameAuthDataResp) GetResultMsg() string {
	if m != nil {
		return m.ResultMsg
	}
	return ""
}

type GetRealNameApplyInfoReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetRealNameApplyInfoReq) Reset()         { *m = GetRealNameApplyInfoReq{} }
func (m *GetRealNameApplyInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRealNameApplyInfoReq) ProtoMessage()    {}
func (*GetRealNameApplyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{39}
}

func (m *GetRealNameApplyInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRealNameApplyInfoResp struct {
	IdentityNum string `protobuf:"bytes,1,opt,name=identity_num,json=identityNum" json:"identity_num"`
	Name        string `protobuf:"bytes,2,opt,name=name" json:"name"`
	ApplyNum    uint32 `protobuf:"varint,3,opt,name=apply_num,json=applyNum" json:"apply_num"`
}

func (m *GetRealNameApplyInfoResp) Reset()         { *m = GetRealNameApplyInfoResp{} }
func (m *GetRealNameApplyInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRealNameApplyInfoResp) ProtoMessage()    {}
func (*GetRealNameApplyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorRealnameauth, []int{40}
}

func (m *GetRealNameApplyInfoResp) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

func (m *GetRealNameApplyInfoResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetRealNameApplyInfoResp) GetApplyNum() uint32 {
	if m != nil {
		return m.ApplyNum
	}
	return 0
}

func init() {
	proto.RegisterType((*GetIsEnableCheckReq)(nil), "realnameauth.GetIsEnableCheckReq")
	proto.RegisterType((*GetIsEnableCheckResp)(nil), "realnameauth.GetIsEnableCheckResp")
	proto.RegisterType((*AddAuthOperHistoryReq)(nil), "realnameauth.AddAuthOperHistoryReq")
	proto.RegisterType((*AddAuthOperHistoryResp)(nil), "realnameauth.AddAuthOperHistoryResp")
	proto.RegisterType((*AuthInfoReq)(nil), "realnameauth.AuthInfoReq")
	proto.RegisterType((*AuthInfoResp)(nil), "realnameauth.AuthInfoResp")
	proto.RegisterType((*GetAuthInfoReq)(nil), "realnameauth.GetAuthInfoReq")
	proto.RegisterType((*GetAuthInfoResp)(nil), "realnameauth.GetAuthInfoResp")
	proto.RegisterType((*GetUserIdentityExpireDateReq)(nil), "realnameauth.GetUserIdentityExpireDateReq")
	proto.RegisterType((*GetUserIdentityExpireDateResp)(nil), "realnameauth.GetUserIdentityExpireDateResp")
	proto.RegisterType((*GetUserIdentityInfoReq)(nil), "realnameauth.GetUserIdentityInfoReq")
	proto.RegisterType((*GetUserIdentityInfoResp)(nil), "realnameauth.GetUserIdentityInfoResp")
	proto.RegisterType((*AddUserIdentityInfoReq)(nil), "realnameauth.AddUserIdentityInfoReq")
	proto.RegisterType((*AddUserIdentityInfoResp)(nil), "realnameauth.AddUserIdentityInfoResp")
	proto.RegisterType((*UpdateUserIdentityInfoReq)(nil), "realnameauth.UpdateUserIdentityInfoReq")
	proto.RegisterType((*UpdateUserIdentityInfoResp)(nil), "realnameauth.UpdateUserIdentityInfoResp")
	proto.RegisterType((*AddOrUpdateUserIdentityInfoReq)(nil), "realnameauth.AddOrUpdateUserIdentityInfoReq")
	proto.RegisterType((*AddOrUpdateUserIdentityInfoResp)(nil), "realnameauth.AddOrUpdateUserIdentityInfoResp")
	proto.RegisterType((*DelUserIdentityInfoReq)(nil), "realnameauth.DelUserIdentityInfoReq")
	proto.RegisterType((*DelUserIdentityInfoResp)(nil), "realnameauth.DelUserIdentityInfoResp")
	proto.RegisterType((*AddIdentityExpireUserPushTimeReq)(nil), "realnameauth.AddIdentityExpireUserPushTimeReq")
	proto.RegisterType((*AddIdentityExpireUserPushTimeResp)(nil), "realnameauth.AddIdentityExpireUserPushTimeResp")
	proto.RegisterType((*RecordUserAuthPhoneReq)(nil), "realnameauth.RecordUserAuthPhoneReq")
	proto.RegisterType((*RecordUserAuthPhoneResp)(nil), "realnameauth.RecordUserAuthPhoneResp")
	proto.RegisterType((*GetUserAuthPhoneReq)(nil), "realnameauth.GetUserAuthPhoneReq")
	proto.RegisterType((*GetUserAuthPhoneResp)(nil), "realnameauth.GetUserAuthPhoneResp")
	proto.RegisterType((*GetPhoneBindNumberReq)(nil), "realnameauth.GetPhoneBindNumberReq")
	proto.RegisterType((*GetPhoneBindNumberResp)(nil), "realnameauth.GetPhoneBindNumberResp")
	proto.RegisterType((*RecordUserIdentityInfoReq)(nil), "realnameauth.RecordUserIdentityInfoReq")
	proto.RegisterType((*RecordUserIdentityInfoResp)(nil), "realnameauth.RecordUserIdentityInfoResp")
	proto.RegisterType((*ParentGuardianSwitchReq)(nil), "realnameauth.ParentGuardianSwitchReq")
	proto.RegisterType((*ParentGuardianSwitchResp)(nil), "realnameauth.ParentGuardianSwitchResp")
	proto.RegisterType((*ParentGuardianStateReq)(nil), "realnameauth.ParentGuardianStateReq")
	proto.RegisterType((*ParentGuardianStateResp)(nil), "realnameauth.ParentGuardianStateResp")
	proto.RegisterType((*RealNameAuthInfo)(nil), "realnameauth.RealNameAuthInfo")
	proto.RegisterType((*GetRealNameAuthTokenReq)(nil), "realnameauth.GetRealNameAuthTokenReq")
	proto.RegisterType((*GetRealNameAuthTokenResp)(nil), "realnameauth.GetRealNameAuthTokenResp")
	proto.RegisterType((*ApplyRealNameAuthDataReq)(nil), "realnameauth.ApplyRealNameAuthDataReq")
	proto.RegisterType((*ApplyRealNameAuthDataResp)(nil), "realnameauth.ApplyRealNameAuthDataResp")
	proto.RegisterType((*GetRealNameApplyInfoReq)(nil), "realnameauth.GetRealNameApplyInfoReq")
	proto.RegisterType((*GetRealNameApplyInfoResp)(nil), "realnameauth.GetRealNameApplyInfoResp")
	proto.RegisterEnum("realnameauth.EOperType", EOperType_name, EOperType_value)
	proto.RegisterEnum("realnameauth.EAuthType", EAuthType_name, EAuthType_value)
}
func (m *GetIsEnableCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIsEnableCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetIsEnableCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIsEnableCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsEnableCheck {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *AddAuthOperHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddAuthOperHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.OperType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.AuthType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.LogInfo)))
	i += copy(dAtA[i:], m.LogInfo)
	return i, nil
}

func (m *AddAuthOperHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddAuthOperHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AuthInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *AuthInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAuthInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAuthInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetAuthInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAuthInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Status))
	if m.RealnameInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintRealnameauth(dAtA, i, uint64(m.RealnameInfo.Size()))
		n1, err := m.RealnameInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetUserIdentityExpireDateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserIdentityExpireDateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserIdentityExpireDateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserIdentityExpireDateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityExpireDate)))
	i += copy(dAtA[i:], m.IdentityExpireDate)
	return i, nil
}

func (m *GetUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.HaveInfo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.IsAdult {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNumber)))
	i += copy(dAtA[i:], m.IdentityNumber)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	return i, nil
}

func (m *AddUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNumber)))
	i += copy(dAtA[i:], m.IdentityNumber)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	dAtA[i] = 0x20
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.HavePush))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	return i, nil
}

func (m *AddUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UpdateUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNumber)))
	i += copy(dAtA[i:], m.IdentityNumber)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	dAtA[i] = 0x20
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.HavePush))
	return i, nil
}

func (m *UpdateUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AddOrUpdateUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddOrUpdateUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNumber)))
	i += copy(dAtA[i:], m.IdentityNumber)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	dAtA[i] = 0x20
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.HavePush))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	return i, nil
}

func (m *AddOrUpdateUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddOrUpdateUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *DelUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AddIdentityExpireUserPushTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddIdentityExpireUserPushTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.PushTime))
	return i, nil
}

func (m *AddIdentityExpireUserPushTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddIdentityExpireUserPushTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RecordUserAuthPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserAuthPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.AuthPhone)))
	i += copy(dAtA[i:], m.AuthPhone)
	return i, nil
}

func (m *RecordUserAuthPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserAuthPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserAuthPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserAuthPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserAuthPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserAuthPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.HavePhone {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.AuthPhone)))
	i += copy(dAtA[i:], m.AuthPhone)
	return i, nil
}

func (m *GetPhoneBindNumberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPhoneBindNumberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	return i, nil
}

func (m *GetPhoneBindNumberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPhoneBindNumberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.BindNumber))
	return i, nil
}

func (m *RecordUserIdentityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserIdentityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNum)))
	i += copy(dAtA[i:], m.IdentityNum)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityValidTime)))
	i += copy(dAtA[i:], m.IdentityValidTime)
	dAtA[i] = 0x22
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	return i, nil
}

func (m *RecordUserIdentityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserIdentityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ParentGuardianSwitchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianSwitchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.OnOff {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Password)))
	i += copy(dAtA[i:], m.Password)
	return i, nil
}

func (m *ParentGuardianSwitchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianSwitchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ParentGuardianStateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianStateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *ParentGuardianStateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ParentGuardianStateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x10
	i++
	if m.OnOff {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RealNameAuthInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RealNameAuthInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.AuthPhone)))
	i += copy(dAtA[i:], m.AuthPhone)
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNum)))
	i += copy(dAtA[i:], m.IdentityNum)
	return i, nil
}

func (m *GetRealNameAuthTokenReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameAuthTokenReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	if m.RealnameInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("realname_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRealnameauth(dAtA, i, uint64(m.RealnameInfo.Size()))
		n2, err := m.RealnameInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *GetRealNameAuthTokenResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameAuthTokenResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.FaceidToken)))
	i += copy(dAtA[i:], m.FaceidToken)
	return i, nil
}

func (m *ApplyRealNameAuthDataReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApplyRealNameAuthDataReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.FaceidToken)))
	i += copy(dAtA[i:], m.FaceidToken)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.FaceidCheckData)))
	i += copy(dAtA[i:], m.FaceidCheckData)
	return i, nil
}

func (m *ApplyRealNameAuthDataResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApplyRealNameAuthDataResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.ResultCode))
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.ResultMsg)))
	i += copy(dAtA[i:], m.ResultMsg)
	return i, nil
}

func (m *GetRealNameApplyInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameApplyInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetRealNameApplyInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRealNameApplyInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.IdentityNum)))
	i += copy(dAtA[i:], m.IdentityNum)
	dAtA[i] = 0x12
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintRealnameauth(dAtA, i, uint64(m.ApplyNum))
	return i, nil
}

func encodeFixed64Realnameauth(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Realnameauth(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintRealnameauth(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetIsEnableCheckReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetIsEnableCheckResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *AddAuthOperHistoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 1 + sovRealnameauth(uint64(m.OperType))
	n += 1 + sovRealnameauth(uint64(m.AuthType))
	l = len(m.LogInfo)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *AddAuthOperHistoryResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AuthInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 1 + sovRealnameauth(uint64(m.Status))
	return n
}

func (m *AuthInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAuthInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetAuthInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Status))
	if m.RealnameInfo != nil {
		l = m.RealnameInfo.Size()
		n += 1 + l + sovRealnameauth(uint64(l))
	}
	return n
}

func (m *GetUserIdentityExpireDateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetUserIdentityExpireDateResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.IdentityExpireDate)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	n += 2
	l = len(m.IdentityNumber)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *AddUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.IdentityNumber)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.HavePush))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *AddUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UpdateUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.IdentityNumber)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.HavePush))
	return n
}

func (m *UpdateUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AddOrUpdateUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.IdentityNumber)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.HavePush))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *AddOrUpdateUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *DelUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AddIdentityExpireUserPushTimeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 1 + sovRealnameauth(uint64(m.PushTime))
	return n
}

func (m *AddIdentityExpireUserPushTimeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RecordUserAuthPhoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.AuthPhone)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *RecordUserAuthPhoneResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserAuthPhoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetUserAuthPhoneResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	l = len(m.AuthPhone)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetPhoneBindNumberReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetPhoneBindNumberResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.BindNumber))
	return n
}

func (m *RecordUserIdentityInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.IdentityNum)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityValidTime)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *RecordUserIdentityInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ParentGuardianSwitchReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	n += 2
	l = len(m.Password)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *ParentGuardianSwitchResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ParentGuardianStateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *ParentGuardianStateResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *RealNameAuthInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.AuthPhone)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.IdentityNum)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetRealNameAuthTokenReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	if m.RealnameInfo != nil {
		l = m.RealnameInfo.Size()
		n += 1 + l + sovRealnameauth(uint64(l))
	}
	return n
}

func (m *GetRealNameAuthTokenResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.FaceidToken)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *ApplyRealNameAuthDataReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	l = len(m.FaceidToken)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.FaceidCheckData)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *ApplyRealNameAuthDataResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.ResultCode))
	l = len(m.ResultMsg)
	n += 1 + l + sovRealnameauth(uint64(l))
	return n
}

func (m *GetRealNameApplyInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovRealnameauth(uint64(m.Uid))
	return n
}

func (m *GetRealNameApplyInfoResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.IdentityNum)
	n += 1 + l + sovRealnameauth(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovRealnameauth(uint64(l))
	n += 1 + sovRealnameauth(uint64(m.ApplyNum))
	return n
}

func sovRealnameauth(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozRealnameauth(x uint64) (n int) {
	return sovRealnameauth(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GetIsEnableCheckReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIsEnableCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIsEnableCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIsEnableCheckResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIsEnableCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIsEnableCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsEnableCheck", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsEnableCheck = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_enable_check")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddAuthOperHistoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddAuthOperHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddAuthOperHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthType", wireType)
			}
			m.AuthType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AuthType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LogInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LogInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("auth_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("log_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddAuthOperHistoryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddAuthOperHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddAuthOperHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAuthInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAuthInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAuthInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAuthInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAuthInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAuthInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealnameInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RealnameInfo == nil {
				m.RealnameInfo = &RealNameAuthInfo{}
			}
			if err := m.RealnameInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserIdentityExpireDateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserIdentityExpireDateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserIdentityExpireDateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserIdentityExpireDateResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserIdentityExpireDateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserIdentityExpireDateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityExpireDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityExpireDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HaveInfo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HaveInfo = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdult", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdult = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_adult")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HavePush", wireType)
			}
			m.HavePush = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HavePush |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identity_number")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identity_valid_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_push")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HavePush", wireType)
			}
			m.HavePush = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HavePush |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_push")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddOrUpdateUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddOrUpdateUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddOrUpdateUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HavePush", wireType)
			}
			m.HavePush = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HavePush |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_push")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddOrUpdateUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddOrUpdateUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddOrUpdateUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddIdentityExpireUserPushTimeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddIdentityExpireUserPushTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddIdentityExpireUserPushTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushTime", wireType)
			}
			m.PushTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushTime |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddIdentityExpireUserPushTimeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddIdentityExpireUserPushTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddIdentityExpireUserPushTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserAuthPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserAuthPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserAuthPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuthPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("auth_phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserAuthPhoneResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserAuthPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserAuthPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserAuthPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserAuthPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserAuthPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserAuthPhoneResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserAuthPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserAuthPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HavePhone", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HavePhone = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuthPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("have_phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPhoneBindNumberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPhoneBindNumberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPhoneBindNumberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPhoneBindNumberResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPhoneBindNumberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPhoneBindNumberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BindNumber", wireType)
			}
			m.BindNumber = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BindNumber |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("bind_number")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserIdentityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserIdentityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserIdentityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityValidTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityValidTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identity_num")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identity_valid_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserIdentityInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserIdentityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserIdentityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianSwitchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianSwitchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianSwitchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnOff", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OnOff = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Password", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Password = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("on_off")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("password")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianSwitchResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianSwitchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianSwitchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianStateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianStateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianStateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ParentGuardianStateResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ParentGuardianStateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ParentGuardianStateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnOff", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OnOff = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("on_off")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RealNameAuthInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RealNameAuthInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RealNameAuthInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthPhone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuthPhone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameAuthTokenReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameAuthTokenReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameAuthTokenReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RealnameInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RealnameInfo == nil {
				m.RealnameInfo = &RealNameAuthInfo{}
			}
			if err := m.RealnameInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("realname_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameAuthTokenResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameAuthTokenResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameAuthTokenResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceidToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceidToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("faceid_token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ApplyRealNameAuthDataReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ApplyRealNameAuthDataReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ApplyRealNameAuthDataReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceidToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceidToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceidCheckData", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceidCheckData = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("faceid_token")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("faceid_check_data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ApplyRealNameAuthDataResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ApplyRealNameAuthDataResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ApplyRealNameAuthDataResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultCode", wireType)
			}
			m.ResultCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResultCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("result_code")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("result_msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameApplyInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameApplyInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameApplyInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRealNameApplyInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRealNameApplyInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRealNameApplyInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdentityNum", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdentityNum = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRealnameauth
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyNum", wireType)
			}
			m.ApplyNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRealnameauth(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRealnameauth
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipRealnameauth(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRealnameauth
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRealnameauth
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthRealnameauth
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowRealnameauth
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipRealnameauth(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthRealnameauth = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRealnameauth   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/realNameAuthSvr/realnameauth.proto", fileDescriptorRealnameauth) }

var fileDescriptorRealnameauth = []byte{
	// 1891 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x58, 0xcd, 0x6f, 0x1c, 0x49,
	0x15, 0x4f, 0xcd, 0xd8, 0x8e, 0xfd, 0xec, 0xc4, 0x4e, 0x39, 0x76, 0xc6, 0xbd, 0x8e, 0x33, 0xae,
	0x24, 0x1b, 0x6f, 0xf0, 0xc4, 0xf9, 0x22, 0x42, 0xa3, 0xd1, 0xa0, 0x89, 0x3d, 0x72, 0x22, 0x88,
	0x6d, 0x39, 0x36, 0x9c, 0xd0, 0xd0, 0x9e, 0xae, 0xb1, 0x1b, 0xcf, 0x74, 0xd7, 0x4e, 0x55, 0x7b,
	0xe3, 0x65, 0x25, 0x22, 0x21, 0x21, 0x84, 0xb4, 0x08, 0x71, 0x40, 0xda, 0x13, 0x97, 0x70, 0x41,
	0x2b, 0x71, 0xe2, 0xc4, 0x05, 0x21, 0x0e, 0x7b, 0xe0, 0x80, 0x38, 0x72, 0x40, 0x28, 0x5c, 0xf2,
	0x67, 0xa0, 0xaa, 0xfe, 0x70, 0x7f, 0xcf, 0x78, 0x77, 0x2f, 0x7b, 0xb1, 0x3c, 0xef, 0xbd, 0xaa,
	0xf7, 0x7b, 0xaf, 0xde, 0x7b, 0x55, 0xbf, 0x86, 0xf7, 0x79, 0xbf, 0xbd, 0xd6, 0xa7, 0x7a, 0x77,
	0x4b, 0xef, 0xd1, 0x86, 0x23, 0x8e, 0x5e, 0x9e, 0xf4, 0xd5, 0x6f, 0x4b, 0xef, 0x51, 0xdd, 0x11,
	0x47, 0xf7, 0x58, 0xdf, 0x16, 0x36, 0x9e, 0x0a, 0xcb, 0xb4, 0x5b, 0x6d, 0xbb, 0xd7, 0xb3, 0xad,
	0x35, 0xd1, 0x3d, 0x61, 0x66, 0xfb, 0xb8, 0x4b, 0xd7, 0xf8, 0xf1, 0x81, 0x63, 0x76, 0x85, 0x69,
	0x89, 0x53, 0x46, 0xdd, 0x35, 0x64, 0x0e, 0x66, 0x37, 0xa9, 0x78, 0xce, 0x9b, 0x96, 0x7e, 0xd0,
	0xa5, 0xeb, 0x47, 0xb4, 0x7d, 0xbc, 0x4b, 0x3f, 0x24, 0x1b, 0x70, 0x35, 0x29, 0xe6, 0x0c, 0xaf,
	0xc2, 0xb4, 0xc9, 0x5b, 0x54, 0x49, 0x5b, 0x6d, 0x29, 0x2e, 0xa1, 0x72, 0x61, 0x65, 0xfc, 0xe9,
	0xc8, 0x17, 0xff, 0xb9, 0x71, 0x61, 0xf7, 0x92, 0x19, 0x5e, 0x41, 0x7e, 0x87, 0x60, 0xae, 0x61,
	0x18, 0x12, 0xf2, 0x36, 0xa3, 0xfd, 0x67, 0x26, 0x17, 0x76, 0xff, 0x74, 0x97, 0x7e, 0x88, 0xe7,
	0xa1, 0xe8, 0x98, 0x86, 0x5a, 0x7b, 0xc9, 0x5b, 0x2b, 0x05, 0x78, 0x19, 0x26, 0x6c, 0x46, 0xfb,
	0x2d, 0x89, 0xb0, 0x54, 0x08, 0x69, 0xc7, 0xa5, 0x78, 0xef, 0x94, 0x51, 0x69, 0x22, 0xe3, 0x73,
	0x4d, 0x8a, 0x61, 0x13, 0x29, 0x56, 0x26, 0x37, 0x60, 0xbc, 0x6b, 0x1f, 0xb6, 0x4c, 0xab, 0x63,
	0x97, 0x46, 0xca, 0x85, 0x95, 0x09, 0xcf, 0xe2, 0x62, 0xd7, 0x3e, 0x7c, 0x6e, 0x75, 0x6c, 0x52,
	0x82, 0xf9, 0x34, 0x5c, 0x9c, 0x91, 0x75, 0x98, 0x94, 0x62, 0x69, 0x95, 0x87, 0x73, 0x11, 0xc6,
	0xb8, 0xd0, 0x85, 0xc3, 0x15, 0xc8, 0x51, 0x4f, 0xe5, 0xc9, 0xc8, 0x65, 0x98, 0x3a, 0xdb, 0x84,
	0x33, 0xb2, 0x02, 0x97, 0x37, 0xa9, 0x18, 0x62, 0x5f, 0x22, 0x60, 0x3a, 0x62, 0xc9, 0x59, 0xbe,
	0x2b, 0xbc, 0x0e, 0x97, 0xfc, 0x53, 0x77, 0xe3, 0x2d, 0x96, 0xd1, 0xca, 0xe4, 0xc3, 0xa5, 0x7b,
	0x91, 0xfa, 0xd8, 0x0d, 0x15, 0x8f, 0xda, 0x38, 0x28, 0x15, 0x95, 0x8e, 0x27, 0xb0, 0xb8, 0x49,
	0xc5, 0x3e, 0xa7, 0xfd, 0xe7, 0x06, 0xb5, 0x84, 0x29, 0x4e, 0x9b, 0xaf, 0x98, 0xd9, 0xa7, 0x1b,
	0xba, 0xa0, 0x79, 0x68, 0x7f, 0x08, 0xd7, 0x73, 0xd6, 0x71, 0x86, 0x9f, 0xc0, 0x55, 0xd3, 0xd3,
	0xb4, 0xa8, 0x52, 0xb5, 0x0c, 0x5d, 0xd0, 0x12, 0x2a, 0xa3, 0xe0, 0x50, 0xb0, 0x99, 0x58, 0x4b,
	0xee, 0xc3, 0x7c, 0x6c, 0xe3, 0x41, 0x89, 0xfb, 0x1b, 0x82, 0x6b, 0xa9, 0x4b, 0x38, 0x93, 0x15,
	0x73, 0xa4, 0x9f, 0x78, 0xf9, 0x09, 0x97, 0xeb, 0xb8, 0x14, 0x4b, 0x33, 0x59, 0x31, 0x26, 0x6f,
	0xe9, 0x86, 0xd3, 0x15, 0xaa, 0xa6, 0x7c, 0x8b, 0x8b, 0x26, 0x6f, 0x48, 0x21, 0xae, 0xc0, 0x74,
	0x10, 0x89, 0xe5, 0xf4, 0x0e, 0x68, 0xbf, 0x34, 0x12, 0x0a, 0xe2, 0xb2, 0xaf, 0xdc, 0x52, 0x3a,
	0xfc, 0x18, 0x66, 0x03, 0xf3, 0x13, 0xbd, 0x6b, 0x1a, 0x2d, 0x61, 0xf6, 0x68, 0x69, 0x34, 0xb4,
	0xe4, 0x8a, 0x6f, 0xf0, 0x03, 0xa9, 0xdf, 0x33, 0x7b, 0x94, 0xfc, 0x0b, 0xa9, 0xba, 0x3c, 0x47,
	0xdc, 0x69, 0xb8, 0x0a, 0xa1, 0x8a, 0x1f, 0x12, 0x57, 0x31, 0xb4, 0x24, 0x89, 0x2b, 0x48, 0x20,
	0x73, 0xf8, 0x91, 0x6a, 0xa8, 0xd1, 0x70, 0x02, 0x77, 0x1c, 0x7e, 0x84, 0x4b, 0x30, 0x22, 0xcb,
	0x29, 0x12, 0xa1, 0x92, 0x90, 0x05, 0xb8, 0x96, 0x1a, 0x13, 0x67, 0xe4, 0x2f, 0x08, 0x16, 0xf6,
	0x99, 0xac, 0x88, 0xaf, 0x1c, 0x32, 0x3a, 0x7f, 0xc8, 0xe8, 0xab, 0x85, 0x4c, 0x16, 0x41, 0xcb,
	0x02, 0xcf, 0x19, 0xf9, 0x37, 0x82, 0xa5, 0x86, 0x61, 0x6c, 0xf7, 0xbf, 0xb1, 0x01, 0xe6, 0x9c,
	0xe9, 0x32, 0xdc, 0xc8, 0x8d, 0x8d, 0x33, 0xd9, 0xc2, 0x1b, 0xb4, 0x7b, 0x9e, 0x16, 0x5e, 0x80,
	0x6b, 0xa9, 0x2b, 0x38, 0x23, 0x3f, 0x82, 0x72, 0xc3, 0x30, 0xa2, 0x43, 0x46, 0x1a, 0x4a, 0x98,
	0x32, 0x9a, 0xd8, 0xb6, 0xa3, 0xb1, 0x2b, 0x45, 0xc6, 0xe8, 0x26, 0x25, 0x3c, 0x42, 0xc7, 0x99,
	0xb7, 0x9a, 0xdc, 0x84, 0xe5, 0x01, 0xdb, 0x73, 0x46, 0xf6, 0x61, 0x7e, 0x97, 0xb6, 0xed, 0xbe,
	0x2a, 0x65, 0x39, 0x48, 0x77, 0x8e, 0x6c, 0x2b, 0x6f, 0x3c, 0xe2, 0x9b, 0x00, 0xea, 0xa6, 0x62,
	0xd2, 0x30, 0xd2, 0x96, 0xea, 0x06, 0x53, 0xeb, 0x65, 0xd4, 0xa9, 0xdb, 0x72, 0x46, 0x2a, 0xea,
	0x6e, 0x1e, 0xd6, 0x1d, 0xf9, 0xb1, 0xba, 0xb3, 0x13, 0xdb, 0x48, 0x18, 0xee, 0x49, 0x2b, 0x18,
	0xe1, 0xf9, 0xa7, 0x2a, 0x40, 0x19, 0x26, 0xb0, 0xa2, 0x34, 0xac, 0x8f, 0x60, 0x6e, 0x93, 0x0a,
	0xf5, 0xff, 0x53, 0xd3, 0x32, 0xdc, 0xfa, 0x93, 0x90, 0x34, 0x18, 0x3d, 0xdb, 0xdd, 0x5f, 0xe8,
	0x8a, 0xc8, 0x77, 0xd5, 0x2c, 0x4f, 0x2c, 0xe2, 0x0c, 0xdf, 0x86, 0xc9, 0x03, 0xd3, 0x32, 0xfc,
	0x1a, 0x0f, 0x07, 0x04, 0x07, 0x81, 0x29, 0xf9, 0x1c, 0xc1, 0xc2, 0x59, 0x8a, 0x86, 0x6d, 0xa2,
	0x3b, 0x30, 0x15, 0x6e, 0xa2, 0x48, 0xfa, 0x27, 0x43, 0x1d, 0xf4, 0x25, 0x47, 0xa2, 0xdf, 0x1b,
	0xe1, 0xe7, 0x85, 0xdb, 0x1b, 0x8b, 0xa0, 0x65, 0xa1, 0xe5, 0x8c, 0x30, 0xb8, 0xb6, 0xa3, 0xf7,
	0xa9, 0x25, 0x36, 0x1d, 0xbd, 0x6f, 0x98, 0xba, 0xf5, 0xf2, 0x23, 0x53, 0xb4, 0x8f, 0xf2, 0x22,
	0x79, 0x0f, 0xc6, 0x6c, 0xab, 0x65, 0x77, 0x3a, 0x2a, 0x06, 0xff, 0xec, 0x46, 0x6d, 0x6b, 0xbb,
	0xd3, 0xc1, 0x65, 0x18, 0x67, 0x3a, 0xe7, 0x1f, 0xd9, 0x7d, 0x23, 0x02, 0x39, 0x90, 0x12, 0x0d,
	0x4a, 0xe9, 0x1e, 0xdd, 0x26, 0x8d, 0xe9, 0xc4, 0x80, 0x2b, 0xff, 0x49, 0x02, 0xbf, 0xf0, 0x2f,
	0xfb, 0x3c, 0x9c, 0xe4, 0x63, 0x98, 0x89, 0x3f, 0x42, 0x62, 0x35, 0x87, 0x52, 0x6b, 0x2e, 0x48,
	0x74, 0x21, 0x3e, 0x84, 0x12, 0x27, 0x1c, 0x1e, 0x78, 0xe1, 0x13, 0x26, 0x27, 0xea, 0x69, 0x10,
	0x76, 0xbf, 0x67, 0x1f, 0x53, 0x2b, 0x2f, 0xe7, 0x89, 0x67, 0x95, 0x0c, 0xe9, 0xbc, 0xcf, 0xaa,
	0x75, 0x28, 0xa5, 0xfb, 0xe5, 0x4c, 0x82, 0xef, 0xe8, 0x6d, 0x2a, 0xab, 0x4d, 0xca, 0x22, 0x8d,
	0x33, 0xe9, 0x6a, 0x94, 0x31, 0xf9, 0x14, 0x41, 0xa9, 0xc1, 0x58, 0xf7, 0x34, 0xbc, 0xcf, 0x86,
	0x2e, 0xf4, 0x01, 0xc5, 0x1f, 0xd9, 0xbd, 0x90, 0xb1, 0x3b, 0xbe, 0x0f, 0x57, 0x3c, 0x43, 0xf5,
	0x98, 0x97, 0xaf, 0x33, 0x3d, 0x52, 0x47, 0xd3, 0xae, 0x5a, 0xbd, 0xe7, 0xa5, 0x57, 0x72, 0x08,
	0x0b, 0x19, 0x70, 0xdc, 0x8e, 0xee, 0x53, 0xee, 0x74, 0x45, 0xab, 0x6d, 0x1b, 0x34, 0xda, 0xd1,
	0xae, 0x62, 0xdd, 0x36, 0xd4, 0xb0, 0xf1, 0xcc, 0x7a, 0xfc, 0x30, 0x3a, 0x18, 0x5d, 0xf9, 0x0b,
	0x7e, 0x48, 0x1e, 0x44, 0x4f, 0x4d, 0xfa, 0x1c, 0x74, 0x83, 0xbc, 0x46, 0xd1, 0x8c, 0x9f, 0xad,
	0x71, 0x33, 0x1e, 0x29, 0x17, 0x94, 0x51, 0x2e, 0x39, 0x15, 0x27, 0xa9, 0x87, 0xdc, 0x33, 0x28,
	0xb7, 0x33, 0xea, 0x21, 0xc5, 0x5b, 0x4e, 0xef, 0x6e, 0x1b, 0x26, 0x9a, 0xdb, 0x3e, 0x55, 0xd1,
	0x60, 0xbe, 0xb9, 0xb5, 0xff, 0xa2, 0xb5, 0xdb, 0x6c, 0x7c, 0x7f, 0xab, 0xf1, 0xa2, 0xd9, 0xda,
	0xde, 0x69, 0xee, 0xb6, 0x5e, 0x36, 0xf7, 0x66, 0x10, 0x5e, 0x84, 0x52, 0x8a, 0x6e, 0x7f, 0x4b,
	0x6a, 0x0b, 0x19, 0xda, 0xdd, 0xa6, 0xd4, 0x16, 0xef, 0x7e, 0x00, 0x13, 0xcd, 0x86, 0x4f, 0x76,
	0x12, 0xa6, 0x8d, 0xfd, 0xbd, 0x67, 0xad, 0x9d, 0x67, 0xdb, 0x5b, 0xcd, 0x19, 0xf4, 0xf0, 0x1f,
	0x25, 0x98, 0x0a, 0x1f, 0x15, 0xfe, 0x09, 0xcc, 0xc4, 0x99, 0x1d, 0x5e, 0x8e, 0x96, 0x75, 0x0a,
	0x21, 0xd4, 0xc8, 0x20, 0x13, 0xce, 0xc8, 0xf4, 0xeb, 0x37, 0xef, 0x8a, 0xe8, 0x57, 0x6f, 0xde,
	0x15, 0x2f, 0xfc, 0x56, 0xfe, 0xc1, 0x7f, 0x40, 0x80, 0x93, 0x3c, 0x0b, 0xdf, 0x8c, 0xee, 0x95,
	0xca, 0x10, 0xb5, 0x5b, 0x83, 0x8d, 0x38, 0x23, 0x1b, 0xd2, 0x65, 0x41, 0xba, 0x1c, 0x77, 0xaa,
	0x46, 0x55, 0x54, 0x5f, 0x55, 0xa5, 0xeb, 0x4a, 0xc5, 0xa9, 0x39, 0xa6, 0x51, 0x2f, 0x57, 0x8c,
	0x9a, 0xc9, 0xcb, 0x06, 0xed, 0xd6, 0xcb, 0x15, 0x5e, 0x93, 0x3b, 0x95, 0x25, 0x6f, 0xac, 0x97,
	0x2b, 0xaf, 0x6a, 0xb2, 0xaf, 0xcb, 0x5d, 0xfb, 0xb0, 0x8e, 0x7f, 0x8f, 0xe0, 0xaa, 0x3b, 0x15,
	0xa3, 0xb3, 0x0d, 0xdf, 0x8e, 0x82, 0xc8, 0x98, 0xdc, 0xda, 0xfb, 0xc3, 0x98, 0x71, 0x46, 0xbe,
	0x23, 0xd1, 0x16, 0x25, 0xda, 0x31, 0xa7, 0xca, 0xaa, 0x5c, 0x61, 0xbd, 0x19, 0x60, 0x65, 0xb5,
	0x07, 0x55, 0xdb, 0x5a, 0xbd, 0x5f, 0xb5, 0x3b, 0x1d, 0x85, 0xd7, 0x9f, 0xe1, 0x75, 0xfc, 0x33,
	0xf7, 0x12, 0x4d, 0x4e, 0x5e, 0x7c, 0x2b, 0xd7, 0xb7, 0x37, 0xce, 0xb5, 0xdb, 0x43, 0x58, 0x71,
	0x46, 0x4a, 0x12, 0xe0, 0x88, 0x04, 0x58, 0x70, 0x14, 0xb8, 0x8b, 0x1e, 0x38, 0xfc, 0x73, 0x14,
	0x3c, 0xe3, 0x13, 0x73, 0x7c, 0x21, 0x76, 0x54, 0x67, 0x3c, 0x57, 0xd3, 0xb2, 0x54, 0x9c, 0x91,
	0x07, 0xd2, 0xd9, 0xa8, 0x74, 0x36, 0xe2, 0x78, 0xb9, 0x58, 0x0a, 0x72, 0xc1, 0x6b, 0x5c, 0xe8,
	0x8e, 0xe0, 0xd5, 0x07, 0xab, 0x0f, 0x57, 0x1f, 0xad, 0x3e, 0x5e, 0xfd, 0xf6, 0xea, 0x93, 0x3a,
	0x66, 0x01, 0xc9, 0x4b, 0x80, 0x58, 0x4c, 0x14, 0x68, 0x18, 0xc7, 0xf5, 0x1c, 0xad, 0x1f, 0xf7,
	0x58, 0x5a, 0xdc, 0xbf, 0x40, 0xa0, 0xbd, 0xb0, 0x0d, 0xb3, 0x73, 0xfa, 0xb5, 0x87, 0x7e, 0x31,
	0x2f, 0x74, 0xe1, 0xc4, 0x42, 0xff, 0x24, 0x78, 0x0c, 0x86, 0xdf, 0x14, 0xf1, 0xe3, 0x4f, 0x67,
	0xcd, 0xf1, 0xe3, 0xcf, 0x20, 0xca, 0x6e, 0x1a, 0x26, 0xd2, 0xd2, 0xf0, 0x27, 0x04, 0xb3, 0x29,
	0x2c, 0x0e, 0x27, 0xbb, 0x74, 0x08, 0xf7, 0x59, 0x74, 0xf0, 0x7b, 0xd2, 0x3d, 0x78, 0xcd, 0x6c,
	0x55, 0x45, 0x95, 0x29, 0x10, 0x8f, 0x83, 0xcc, 0x58, 0xb5, 0x18, 0x2f, 0xaa, 0x97, 0x2b, 0xa2,
	0x96, 0xf2, 0x7a, 0xab, 0x97, 0xf1, 0x9f, 0x11, 0xcc, 0xa7, 0xd3, 0x13, 0x7c, 0x27, 0x0a, 0x27,
	0x93, 0xa0, 0x69, 0x2b, 0xc3, 0x19, 0x72, 0x46, 0x9e, 0x49, 0xe8, 0x93, 0x5e, 0x67, 0x4b, 0xe8,
	0x12, 0xf8, 0xa3, 0x2f, 0x01, 0x1c, 0xff, 0x1d, 0xc1, 0x7b, 0x39, 0xdc, 0x0a, 0xaf, 0x26, 0x72,
	0x99, 0x43, 0x31, 0xb5, 0xca, 0x39, 0xac, 0xfd, 0x30, 0xa6, 0xbe, 0x8e, 0x30, 0x3e, 0x81, 0xd9,
	0x14, 0x32, 0x17, 0xaf, 0x97, 0x74, 0x86, 0x18, 0xaf, 0x97, 0x2c, 0x56, 0xa8, 0xca, 0xf5, 0x52,
	0x5a, 0xb9, 0xfe, 0x11, 0xc1, 0xf5, 0x5c, 0x46, 0x87, 0xef, 0x25, 0x12, 0x93, 0xcb, 0x2e, 0xb5,
	0xb5, 0x73, 0xd9, 0x73, 0x46, 0x56, 0x24, 0xb8, 0xcb, 0x5e, 0x8b, 0xbb, 0x89, 0x9c, 0x0b, 0x12,
	0x29, 0x6a, 0x01, 0x25, 0xad, 0xe3, 0x5f, 0x23, 0x58, 0xc8, 0xfc, 0x8c, 0x86, 0xef, 0xe6, 0xb6,
	0x6e, 0xe4, 0x3b, 0x9d, 0xf6, 0xad, 0xa1, 0x6d, 0xfd, 0xec, 0x4d, 0xa7, 0x65, 0xef, 0x53, 0x04,
	0xb3, 0x29, 0x9c, 0x34, 0x7e, 0x78, 0xe9, 0x6c, 0x38, 0x7e, 0x78, 0x59, 0xe4, 0xf6, 0x03, 0xe9,
	0x7e, 0x26, 0x32, 0x02, 0xe7, 0x43, 0x23, 0xf0, 0x8c, 0x18, 0xd4, 0x31, 0x57, 0x4f, 0x96, 0x28,
	0x96, 0xe5, 0xd4, 0x50, 0x23, 0x40, 0xc8, 0x20, 0x13, 0x3f, 0x09, 0x57, 0xd2, 0x92, 0xf0, 0x53,
	0xc0, 0x49, 0xda, 0x1a, 0x7f, 0xba, 0xa4, 0xb2, 0x61, 0xed, 0xd6, 0x60, 0x23, 0xce, 0x88, 0x26,
	0x5d, 0x63, 0xe5, 0xda, 0x9d, 0x73, 0x13, 0x15, 0x56, 0xf3, 0x22, 0xfe, 0x1c, 0x29, 0x2e, 0x9f,
	0xa0, 0x0e, 0x38, 0x39, 0xc8, 0xd3, 0x68, 0x4d, 0xfc, 0x41, 0x92, 0xc5, 0x42, 0x48, 0x53, 0x62,
	0x98, 0x0d, 0x26, 0x6e, 0xcf, 0x9b, 0xb8, 0xf7, 0x42, 0x1d, 0x2f, 0x37, 0xa9, 0x97, 0x2b, 0xbd,
	0x94, 0xd6, 0x0f, 0xe0, 0x7e, 0x86, 0x60, 0x2e, 0x95, 0x14, 0xe0, 0x18, 0x90, 0x2c, 0x22, 0xa3,
	0xdd, 0x19, 0xca, 0x8e, 0x33, 0xb2, 0x26, 0x11, 0x5f, 0xf5, 0x26, 0x94, 0xa8, 0x1a, 0x0a, 0xaf,
	0x16, 0x6a, 0x2c, 0x45, 0x78, 0xd4, 0xc3, 0x4f, 0x72, 0x99, 0x3a, 0xfe, 0x2b, 0x0a, 0x7f, 0xb7,
	0xc9, 0xbb, 0x07, 0x32, 0xbf, 0x31, 0xc4, 0xef, 0x81, 0x1c, 0x7a, 0xff, 0x52, 0xc2, 0x9b, 0xf3,
	0x12, 0x6a, 0x06, 0x23, 0xb4, 0x16, 0x00, 0x34, 0x53, 0xf2, 0x18, 0x24, 0x39, 0x63, 0x96, 0xbe,
	0x8e, 0x55, 0x83, 0x4f, 0x6b, 0xf2, 0xaa, 0x21, 0x44, 0x97, 0xf2, 0xaa, 0x21, 0xcc, 0x90, 0xc8,
	0x82, 0x04, 0x3f, 0x1f, 0x6a, 0x86, 0x71, 0x1f, 0xb6, 0x36, 0xf6, 0xcb, 0x37, 0xef, 0x8a, 0x9f,
	0x7d, 0xfc, 0x74, 0xe6, 0x8b, 0xb7, 0x4b, 0xe8, 0x9f, 0x6f, 0x97, 0xd0, 0x7f, 0xdf, 0x2e, 0xa1,
	0xdf, 0xfc, 0x6f, 0xe9, 0xc2, 0xff, 0x03, 0x00, 0x00, 0xff, 0xff, 0x32, 0x02, 0x7d, 0x57, 0x9d,
	0x1a, 0x00, 0x00,
}
