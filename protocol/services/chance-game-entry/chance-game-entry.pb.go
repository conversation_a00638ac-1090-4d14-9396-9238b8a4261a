// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/chance-game-entry/chance-game-entry.proto

package chance_game_entry // import "golang.52tt.com/protocol/services/chance-game-entry"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 以后可能有新的娱乐玩法或者其他的需要用户与房间黑白名单的配置需求，不同的玩法，具体的黑白名单可能不同，所以这里先加一个配置备用
// 玩法类型
type ChanceGameType int32

const (
	ChanceGameType_OnePiece          ChanceGameType = 0
	ChanceGameType_ChanceGameTypeAll ChanceGameType = 1
	ChanceGameType_StarTrek          ChanceGameType = 2
)

var ChanceGameType_name = map[int32]string{
	0: "OnePiece",
	1: "ChanceGameTypeAll",
	2: "StarTrek",
}
var ChanceGameType_value = map[string]int32{
	"OnePiece":          0,
	"ChanceGameTypeAll": 1,
	"StarTrek":          2,
}

func (x ChanceGameType) String() string {
	return proto.EnumName(ChanceGameType_name, int32(x))
}
func (ChanceGameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{0}
}

type ListType int32

const (
	ListType_UserBlack    ListType = 0
	ListType_UserWhite    ListType = 1
	ListType_ChannelBlack ListType = 2
)

var ListType_name = map[int32]string{
	0: "UserBlack",
	1: "UserWhite",
	2: "ChannelBlack",
}
var ListType_value = map[string]int32{
	"UserBlack":    0,
	"UserWhite":    1,
	"ChannelBlack": 2,
}

func (x ListType) String() string {
	return proto.EnumName(ListType_name, int32(x))
}
func (ListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{1}
}

type NewChanceGameType int32

const (
	NewChanceGameType_NewChanceGameType_Invalid     NewChanceGameType = 0
	NewChanceGameType_NewChanceGameType_OnePiece    NewChanceGameType = 1
	NewChanceGameType_NewChanceGameType_StarTrek    NewChanceGameType = 2
	NewChanceGameType_NewChanceGameType_MagicSpirit NewChanceGameType = 3
	NewChanceGameType_NewChanceGameType_SmashEgg    NewChanceGameType = 4
	NewChanceGameType_NewChanceGameType_GoldSmash   NewChanceGameType = 5
	NewChanceGameType_NewChanceGameType_CatCanteen  NewChanceGameType = 6
	NewChanceGameType_NewChanceGameType_GloryMagic  NewChanceGameType = 7
	NewChanceGameType_NewChanceGameType_StarTrain   NewChanceGameType = 8
)

var NewChanceGameType_name = map[int32]string{
	0: "NewChanceGameType_Invalid",
	1: "NewChanceGameType_OnePiece",
	2: "NewChanceGameType_StarTrek",
	3: "NewChanceGameType_MagicSpirit",
	4: "NewChanceGameType_SmashEgg",
	5: "NewChanceGameType_GoldSmash",
	6: "NewChanceGameType_CatCanteen",
	7: "NewChanceGameType_GloryMagic",
	8: "NewChanceGameType_StarTrain",
}
var NewChanceGameType_value = map[string]int32{
	"NewChanceGameType_Invalid":     0,
	"NewChanceGameType_OnePiece":    1,
	"NewChanceGameType_StarTrek":    2,
	"NewChanceGameType_MagicSpirit": 3,
	"NewChanceGameType_SmashEgg":    4,
	"NewChanceGameType_GoldSmash":   5,
	"NewChanceGameType_CatCanteen":  6,
	"NewChanceGameType_GloryMagic":  7,
	"NewChanceGameType_StarTrain":   8,
}

func (x NewChanceGameType) String() string {
	return proto.EnumName(NewChanceGameType_name, int32(x))
}
func (NewChanceGameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{2}
}

// 玩法黑白名单类型
type ChanceGameListType int32

const (
	ChanceGameListType_ChanceGameListType_Invalid ChanceGameListType = 0
	ChanceGameListType_ChanceGameListType_Black   ChanceGameListType = 1
	ChanceGameListType_ChanceGameListType_White   ChanceGameListType = 2
)

var ChanceGameListType_name = map[int32]string{
	0: "ChanceGameListType_Invalid",
	1: "ChanceGameListType_Black",
	2: "ChanceGameListType_White",
}
var ChanceGameListType_value = map[string]int32{
	"ChanceGameListType_Invalid": 0,
	"ChanceGameListType_Black":   1,
	"ChanceGameListType_White":   2,
}

func (x ChanceGameListType) String() string {
	return proto.EnumName(ChanceGameListType_name, int32(x))
}
func (ChanceGameListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{3}
}

// ------------------ 可见人群、豁免条件 配置 ----------
// 可见人群配置
type ConditionType int32

const (
	ConditionType_ConditionType_Invalid                ConditionType = 0
	ConditionType_ConditionType_Nobility               ConditionType = 1
	ConditionType_ConditionType_Wealth                 ConditionType = 2
	ConditionType_ConditionType_Charm                  ConditionType = 3
	ConditionType_ConditionType_PlatformLv             ConditionType = 4
	ConditionType_ConditionType_RechargeThirty         ConditionType = 5
	ConditionType_ConditionType_RechargeEighty         ConditionType = 6
	ConditionType_ConditionType_RechargeHundrednEighty ConditionType = 7
)

var ConditionType_name = map[int32]string{
	0: "ConditionType_Invalid",
	1: "ConditionType_Nobility",
	2: "ConditionType_Wealth",
	3: "ConditionType_Charm",
	4: "ConditionType_PlatformLv",
	5: "ConditionType_RechargeThirty",
	6: "ConditionType_RechargeEighty",
	7: "ConditionType_RechargeHundrednEighty",
}
var ConditionType_value = map[string]int32{
	"ConditionType_Invalid":                0,
	"ConditionType_Nobility":               1,
	"ConditionType_Wealth":                 2,
	"ConditionType_Charm":                  3,
	"ConditionType_PlatformLv":             4,
	"ConditionType_RechargeThirty":         5,
	"ConditionType_RechargeEighty":         6,
	"ConditionType_RechargeHundrednEighty": 7,
}

func (x ConditionType) String() string {
	return proto.EnumName(ConditionType_name, int32(x))
}
func (ConditionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{4}
}

// 豁免条件
type ExemptCondType int32

const (
	ExemptCondType_ExemptCondType_Invalid           ExemptCondType = 0
	ExemptCondType_ExemptCondType_SmashThirty       ExemptCondType = 1
	ExemptCondType_ExemptCondType_SmashChanceRemain ExemptCondType = 2
	ExemptCondType_ExemptCondType_SmashLuckyValue   ExemptCondType = 3
	ExemptCondType_ExemptCondType_OnePieceThirty    ExemptCondType = 4
	ExemptCondType_ExemptCondType_OnePieceMileage   ExemptCondType = 5
	ExemptCondType_ExemptCondType_StarTrekThirty    ExemptCondType = 6
	ExemptCondType_ExemptCondType_MagicSpiritThirty ExemptCondType = 7
	ExemptCondType_ExemptCondType_CatCanteenThirty  ExemptCondType = 8
)

var ExemptCondType_name = map[int32]string{
	0: "ExemptCondType_Invalid",
	1: "ExemptCondType_SmashThirty",
	2: "ExemptCondType_SmashChanceRemain",
	3: "ExemptCondType_SmashLuckyValue",
	4: "ExemptCondType_OnePieceThirty",
	5: "ExemptCondType_OnePieceMileage",
	6: "ExemptCondType_StarTrekThirty",
	7: "ExemptCondType_MagicSpiritThirty",
	8: "ExemptCondType_CatCanteenThirty",
}
var ExemptCondType_value = map[string]int32{
	"ExemptCondType_Invalid":           0,
	"ExemptCondType_SmashThirty":       1,
	"ExemptCondType_SmashChanceRemain": 2,
	"ExemptCondType_SmashLuckyValue":   3,
	"ExemptCondType_OnePieceThirty":    4,
	"ExemptCondType_OnePieceMileage":   5,
	"ExemptCondType_StarTrekThirty":    6,
	"ExemptCondType_MagicSpiritThirty": 7,
	"ExemptCondType_CatCanteenThirty":  8,
}

func (x ExemptCondType) String() string {
	return proto.EnumName(ExemptCondType_name, int32(x))
}
func (ExemptCondType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{5}
}

// 关联关系
type RelateType int32

const (
	RelateType_RelateType_Invalid RelateType = 0
	RelateType_RelateType_And     RelateType = 1
	RelateType_RelateType_Or      RelateType = 2
)

var RelateType_name = map[int32]string{
	0: "RelateType_Invalid",
	1: "RelateType_And",
	2: "RelateType_Or",
}
var RelateType_value = map[string]int32{
	"RelateType_Invalid": 0,
	"RelateType_And":     1,
	"RelateType_Or":      2,
}

func (x RelateType) String() string {
	return proto.EnumName(RelateType_name, int32(x))
}
func (RelateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{6}
}

// 可见人群配置/豁免条件配置
type CondType int32

const (
	CondType_CondType_AccessCond CondType = 0
	CondType_CondType_ExemptCond CondType = 1
)

var CondType_name = map[int32]string{
	0: "CondType_AccessCond",
	1: "CondType_ExemptCond",
}
var CondType_value = map[string]int32{
	"CondType_AccessCond": 0,
	"CondType_ExemptCond": 1,
}

func (x CondType) String() string {
	return proto.EnumName(CondType_name, int32(x))
}
func (CondType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{7}
}

type NotifyGameType int32

const (
	NotifyGameType_NotifyGameType_UNSPECIFIED NotifyGameType = 0
	NotifyGameType_NotifyGameType_CAT_CANTEEN NotifyGameType = 1
)

var NotifyGameType_name = map[int32]string{
	0: "NotifyGameType_UNSPECIFIED",
	1: "NotifyGameType_CAT_CANTEEN",
}
var NotifyGameType_value = map[string]int32{
	"NotifyGameType_UNSPECIFIED": 0,
	"NotifyGameType_CAT_CANTEEN": 1,
}

func (x NotifyGameType) String() string {
	return proto.EnumName(NotifyGameType_name, int32(x))
}
func (NotifyGameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{8}
}

// 提醒浮层通用接口
type NotifyPlaceType int32

const (
	NotifyPlaceType_NotifyPlaceType_UNSPECIFIED NotifyPlaceType = 0
	NotifyPlaceType_NotifyPlaceType_CHANNEL     NotifyPlaceType = 1
	NotifyPlaceType_NotifyPlaceType_GAME        NotifyPlaceType = 2
	NotifyPlaceType_NotifyPlaceType_PUBLIC      NotifyPlaceType = 3
)

var NotifyPlaceType_name = map[int32]string{
	0: "NotifyPlaceType_UNSPECIFIED",
	1: "NotifyPlaceType_CHANNEL",
	2: "NotifyPlaceType_GAME",
	3: "NotifyPlaceType_PUBLIC",
}
var NotifyPlaceType_value = map[string]int32{
	"NotifyPlaceType_UNSPECIFIED": 0,
	"NotifyPlaceType_CHANNEL":     1,
	"NotifyPlaceType_GAME":        2,
	"NotifyPlaceType_PUBLIC":      3,
}

func (x NotifyPlaceType) String() string {
	return proto.EnumName(NotifyPlaceType_name, int32(x))
}
func (NotifyPlaceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{9}
}

type NotifyType int32

const (
	NotifyType_NotifyType_UNEXPECTED NotifyType = 0
	NotifyType_NotifyType_COMMON     NotifyType = 1
)

var NotifyType_name = map[int32]string{
	0: "NotifyType_UNEXPECTED",
	1: "NotifyType_COMMON",
}
var NotifyType_value = map[string]int32{
	"NotifyType_UNEXPECTED": 0,
	"NotifyType_COMMON":     1,
}

func (x NotifyType) String() string {
	return proto.EnumName(NotifyType_name, int32(x))
}
func (NotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{10}
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type AddChannelBWListReq struct {
	DisplayIdList        []uint32 `protobuf:"varint,1,rep,packed,name=display_id_list,json=displayIdList,proto3" json:"display_id_list,omitempty"`
	BlackOrWhite         uint32   `protobuf:"varint,2,opt,name=black_or_white,json=blackOrWhite,proto3" json:"black_or_white,omitempty"`
	GameType             uint32   `protobuf:"varint,3,opt,name=Game_type,json=GameType,proto3" json:"Game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelBWListReq) Reset()         { *m = AddChannelBWListReq{} }
func (m *AddChannelBWListReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelBWListReq) ProtoMessage()    {}
func (*AddChannelBWListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{0}
}
func (m *AddChannelBWListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelBWListReq.Unmarshal(m, b)
}
func (m *AddChannelBWListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelBWListReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelBWListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelBWListReq.Merge(dst, src)
}
func (m *AddChannelBWListReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelBWListReq.Size(m)
}
func (m *AddChannelBWListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelBWListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelBWListReq proto.InternalMessageInfo

func (m *AddChannelBWListReq) GetDisplayIdList() []uint32 {
	if m != nil {
		return m.DisplayIdList
	}
	return nil
}

func (m *AddChannelBWListReq) GetBlackOrWhite() uint32 {
	if m != nil {
		return m.BlackOrWhite
	}
	return 0
}

func (m *AddChannelBWListReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type AddChannelBWListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelBWListResp) Reset()         { *m = AddChannelBWListResp{} }
func (m *AddChannelBWListResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelBWListResp) ProtoMessage()    {}
func (*AddChannelBWListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{1}
}
func (m *AddChannelBWListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelBWListResp.Unmarshal(m, b)
}
func (m *AddChannelBWListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelBWListResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelBWListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelBWListResp.Merge(dst, src)
}
func (m *AddChannelBWListResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelBWListResp.Size(m)
}
func (m *AddChannelBWListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelBWListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelBWListResp proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type AddUserBWListReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	BlackOrWhite         uint32   `protobuf:"varint,2,opt,name=black_or_white,json=blackOrWhite,proto3" json:"black_or_white,omitempty"`
	GameType             uint32   `protobuf:"varint,3,opt,name=Game_type,json=GameType,proto3" json:"Game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserBWListReq) Reset()         { *m = AddUserBWListReq{} }
func (m *AddUserBWListReq) String() string { return proto.CompactTextString(m) }
func (*AddUserBWListReq) ProtoMessage()    {}
func (*AddUserBWListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{2}
}
func (m *AddUserBWListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserBWListReq.Unmarshal(m, b)
}
func (m *AddUserBWListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserBWListReq.Marshal(b, m, deterministic)
}
func (dst *AddUserBWListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserBWListReq.Merge(dst, src)
}
func (m *AddUserBWListReq) XXX_Size() int {
	return xxx_messageInfo_AddUserBWListReq.Size(m)
}
func (m *AddUserBWListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserBWListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserBWListReq proto.InternalMessageInfo

func (m *AddUserBWListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *AddUserBWListReq) GetBlackOrWhite() uint32 {
	if m != nil {
		return m.BlackOrWhite
	}
	return 0
}

func (m *AddUserBWListReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type AddUserBWListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserBWListResp) Reset()         { *m = AddUserBWListResp{} }
func (m *AddUserBWListResp) String() string { return proto.CompactTextString(m) }
func (*AddUserBWListResp) ProtoMessage()    {}
func (*AddUserBWListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{3}
}
func (m *AddUserBWListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserBWListResp.Unmarshal(m, b)
}
func (m *AddUserBWListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserBWListResp.Marshal(b, m, deterministic)
}
func (dst *AddUserBWListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserBWListResp.Merge(dst, src)
}
func (m *AddUserBWListResp) XXX_Size() int {
	return xxx_messageInfo_AddUserBWListResp.Size(m)
}
func (m *AddUserBWListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserBWListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserBWListResp proto.InternalMessageInfo

// 删除黑/白名单
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type DelFromBWListReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	DisplayIdList        []uint32 `protobuf:"varint,2,rep,packed,name=display_id_list,json=displayIdList,proto3" json:"display_id_list,omitempty"`
	BlackOrWhite         uint32   `protobuf:"varint,3,opt,name=black_or_white,json=blackOrWhite,proto3" json:"black_or_white,omitempty"`
	ChannelOrUser        uint32   `protobuf:"varint,4,opt,name=channel_or_user,json=channelOrUser,proto3" json:"channel_or_user,omitempty"`
	GameType             uint32   `protobuf:"varint,5,opt,name=Game_type,json=GameType,proto3" json:"Game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFromBWListReq) Reset()         { *m = DelFromBWListReq{} }
func (m *DelFromBWListReq) String() string { return proto.CompactTextString(m) }
func (*DelFromBWListReq) ProtoMessage()    {}
func (*DelFromBWListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{4}
}
func (m *DelFromBWListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFromBWListReq.Unmarshal(m, b)
}
func (m *DelFromBWListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFromBWListReq.Marshal(b, m, deterministic)
}
func (dst *DelFromBWListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFromBWListReq.Merge(dst, src)
}
func (m *DelFromBWListReq) XXX_Size() int {
	return xxx_messageInfo_DelFromBWListReq.Size(m)
}
func (m *DelFromBWListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFromBWListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelFromBWListReq proto.InternalMessageInfo

func (m *DelFromBWListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *DelFromBWListReq) GetDisplayIdList() []uint32 {
	if m != nil {
		return m.DisplayIdList
	}
	return nil
}

func (m *DelFromBWListReq) GetBlackOrWhite() uint32 {
	if m != nil {
		return m.BlackOrWhite
	}
	return 0
}

func (m *DelFromBWListReq) GetChannelOrUser() uint32 {
	if m != nil {
		return m.ChannelOrUser
	}
	return 0
}

func (m *DelFromBWListReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type DelFromBWListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFromBWListResp) Reset()         { *m = DelFromBWListResp{} }
func (m *DelFromBWListResp) String() string { return proto.CompactTextString(m) }
func (*DelFromBWListResp) ProtoMessage()    {}
func (*DelFromBWListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{5}
}
func (m *DelFromBWListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFromBWListResp.Unmarshal(m, b)
}
func (m *DelFromBWListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFromBWListResp.Marshal(b, m, deterministic)
}
func (dst *DelFromBWListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFromBWListResp.Merge(dst, src)
}
func (m *DelFromBWListResp) XXX_Size() int {
	return xxx_messageInfo_DelFromBWListResp.Size(m)
}
func (m *DelFromBWListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFromBWListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelFromBWListResp proto.InternalMessageInfo

type ChannelBWListInfo struct {
	DisplayId            uint32   `protobuf:"varint,1,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	OwnerTtid            string   `protobuf:"bytes,4,opt,name=owner_ttid,json=ownerTtid,proto3" json:"owner_ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Ctime                uint32   `protobuf:"varint,6,opt,name=ctime,proto3" json:"ctime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelBWListInfo) Reset()         { *m = ChannelBWListInfo{} }
func (m *ChannelBWListInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelBWListInfo) ProtoMessage()    {}
func (*ChannelBWListInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{6}
}
func (m *ChannelBWListInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBWListInfo.Unmarshal(m, b)
}
func (m *ChannelBWListInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBWListInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelBWListInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBWListInfo.Merge(dst, src)
}
func (m *ChannelBWListInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelBWListInfo.Size(m)
}
func (m *ChannelBWListInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBWListInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBWListInfo proto.InternalMessageInfo

func (m *ChannelBWListInfo) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *ChannelBWListInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelBWListInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelBWListInfo) GetOwnerTtid() string {
	if m != nil {
		return m.OwnerTtid
	}
	return ""
}

func (m *ChannelBWListInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChannelBWListInfo) GetCtime() uint32 {
	if m != nil {
		return m.Ctime
	}
	return 0
}

type UserBWListInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Ctime                uint32   `protobuf:"varint,4,opt,name=ctime,proto3" json:"ctime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBWListInfo) Reset()         { *m = UserBWListInfo{} }
func (m *UserBWListInfo) String() string { return proto.CompactTextString(m) }
func (*UserBWListInfo) ProtoMessage()    {}
func (*UserBWListInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{7}
}
func (m *UserBWListInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBWListInfo.Unmarshal(m, b)
}
func (m *UserBWListInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBWListInfo.Marshal(b, m, deterministic)
}
func (dst *UserBWListInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBWListInfo.Merge(dst, src)
}
func (m *UserBWListInfo) XXX_Size() int {
	return xxx_messageInfo_UserBWListInfo.Size(m)
}
func (m *UserBWListInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBWListInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserBWListInfo proto.InternalMessageInfo

func (m *UserBWListInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBWListInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *UserBWListInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserBWListInfo) GetCtime() uint32 {
	if m != nil {
		return m.Ctime
	}
	return 0
}

// 获取房间黑白名单list
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetChannelBWListInfoReq struct {
	BlackOrWhite         uint32   `protobuf:"varint,1,opt,name=black_or_white,json=blackOrWhite,proto3" json:"black_or_white,omitempty"`
	GameType             uint32   `protobuf:"varint,2,opt,name=Game_type,json=GameType,proto3" json:"Game_type,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	DisplayId            uint32   `protobuf:"varint,5,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelBWListInfoReq) Reset()         { *m = GetChannelBWListInfoReq{} }
func (m *GetChannelBWListInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelBWListInfoReq) ProtoMessage()    {}
func (*GetChannelBWListInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{8}
}
func (m *GetChannelBWListInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBWListInfoReq.Unmarshal(m, b)
}
func (m *GetChannelBWListInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBWListInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelBWListInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBWListInfoReq.Merge(dst, src)
}
func (m *GetChannelBWListInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelBWListInfoReq.Size(m)
}
func (m *GetChannelBWListInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBWListInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBWListInfoReq proto.InternalMessageInfo

func (m *GetChannelBWListInfoReq) GetBlackOrWhite() uint32 {
	if m != nil {
		return m.BlackOrWhite
	}
	return 0
}

func (m *GetChannelBWListInfoReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GetChannelBWListInfoReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelBWListInfoReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetChannelBWListInfoReq) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

type GetChannelBWListInfoResp struct {
	CbwInfoList          []*ChannelBWListInfo `protobuf:"bytes,1,rep,name=cbw_info_list,json=cbwInfoList,proto3" json:"cbw_info_list,omitempty"`
	TotalPage            uint32               `protobuf:"varint,2,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChannelBWListInfoResp) Reset()         { *m = GetChannelBWListInfoResp{} }
func (m *GetChannelBWListInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelBWListInfoResp) ProtoMessage()    {}
func (*GetChannelBWListInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{9}
}
func (m *GetChannelBWListInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBWListInfoResp.Unmarshal(m, b)
}
func (m *GetChannelBWListInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBWListInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelBWListInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBWListInfoResp.Merge(dst, src)
}
func (m *GetChannelBWListInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelBWListInfoResp.Size(m)
}
func (m *GetChannelBWListInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBWListInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBWListInfoResp proto.InternalMessageInfo

func (m *GetChannelBWListInfoResp) GetCbwInfoList() []*ChannelBWListInfo {
	if m != nil {
		return m.CbwInfoList
	}
	return nil
}

func (m *GetChannelBWListInfoResp) GetTotalPage() uint32 {
	if m != nil {
		return m.TotalPage
	}
	return 0
}

// 获取用户黑白名单list
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetUserBWListInfoReq struct {
	BlackOrWhite         uint32   `protobuf:"varint,1,opt,name=black_or_white,json=blackOrWhite,proto3" json:"black_or_white,omitempty"`
	GameType             uint32   `protobuf:"varint,2,opt,name=Game_type,json=GameType,proto3" json:"Game_type,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBWListInfoReq) Reset()         { *m = GetUserBWListInfoReq{} }
func (m *GetUserBWListInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBWListInfoReq) ProtoMessage()    {}
func (*GetUserBWListInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{10}
}
func (m *GetUserBWListInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBWListInfoReq.Unmarshal(m, b)
}
func (m *GetUserBWListInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBWListInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBWListInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBWListInfoReq.Merge(dst, src)
}
func (m *GetUserBWListInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBWListInfoReq.Size(m)
}
func (m *GetUserBWListInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBWListInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBWListInfoReq proto.InternalMessageInfo

func (m *GetUserBWListInfoReq) GetBlackOrWhite() uint32 {
	if m != nil {
		return m.BlackOrWhite
	}
	return 0
}

func (m *GetUserBWListInfoReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GetUserBWListInfoReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetUserBWListInfoReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetUserBWListInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserBWListInfoResp struct {
	UbwInfoList          []*UserBWListInfo `protobuf:"bytes,1,rep,name=ubw_info_list,json=ubwInfoList,proto3" json:"ubw_info_list,omitempty"`
	TotalPage            uint32            `protobuf:"varint,2,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserBWListInfoResp) Reset()         { *m = GetUserBWListInfoResp{} }
func (m *GetUserBWListInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBWListInfoResp) ProtoMessage()    {}
func (*GetUserBWListInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{11}
}
func (m *GetUserBWListInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBWListInfoResp.Unmarshal(m, b)
}
func (m *GetUserBWListInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBWListInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBWListInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBWListInfoResp.Merge(dst, src)
}
func (m *GetUserBWListInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBWListInfoResp.Size(m)
}
func (m *GetUserBWListInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBWListInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBWListInfoResp proto.InternalMessageInfo

func (m *GetUserBWListInfoResp) GetUbwInfoList() []*UserBWListInfo {
	if m != nil {
		return m.UbwInfoList
	}
	return nil
}

func (m *GetUserBWListInfoResp) GetTotalPage() uint32 {
	if m != nil {
		return m.TotalPage
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type CheckIfAccessibleReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameType             uint32   `protobuf:"varint,3,opt,name=gameType,proto3" json:"gameType,omitempty"`
	ClientIp             uint32   `protobuf:"varint,4,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	MarketId             uint32   `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfAccessibleReq) Reset()         { *m = CheckIfAccessibleReq{} }
func (m *CheckIfAccessibleReq) String() string { return proto.CompactTextString(m) }
func (*CheckIfAccessibleReq) ProtoMessage()    {}
func (*CheckIfAccessibleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{12}
}
func (m *CheckIfAccessibleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfAccessibleReq.Unmarshal(m, b)
}
func (m *CheckIfAccessibleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfAccessibleReq.Marshal(b, m, deterministic)
}
func (dst *CheckIfAccessibleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfAccessibleReq.Merge(dst, src)
}
func (m *CheckIfAccessibleReq) XXX_Size() int {
	return xxx_messageInfo_CheckIfAccessibleReq.Size(m)
}
func (m *CheckIfAccessibleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfAccessibleReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfAccessibleReq proto.InternalMessageInfo

func (m *CheckIfAccessibleReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckIfAccessibleReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckIfAccessibleReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *CheckIfAccessibleReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *CheckIfAccessibleReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type CheckIfAccessibleResp struct {
	CanSee               bool     `protobuf:"varint,1,opt,name=can_see,json=canSee,proto3" json:"can_see,omitempty"`
	StarTrekEntry        bool     `protobuf:"varint,2,opt,name=star_trek_entry,json=starTrekEntry,proto3" json:"star_trek_entry,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfAccessibleResp) Reset()         { *m = CheckIfAccessibleResp{} }
func (m *CheckIfAccessibleResp) String() string { return proto.CompactTextString(m) }
func (*CheckIfAccessibleResp) ProtoMessage()    {}
func (*CheckIfAccessibleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{13}
}
func (m *CheckIfAccessibleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfAccessibleResp.Unmarshal(m, b)
}
func (m *CheckIfAccessibleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfAccessibleResp.Marshal(b, m, deterministic)
}
func (dst *CheckIfAccessibleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfAccessibleResp.Merge(dst, src)
}
func (m *CheckIfAccessibleResp) XXX_Size() int {
	return xxx_messageInfo_CheckIfAccessibleResp.Size(m)
}
func (m *CheckIfAccessibleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfAccessibleResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfAccessibleResp proto.InternalMessageInfo

func (m *CheckIfAccessibleResp) GetCanSee() bool {
	if m != nil {
		return m.CanSee
	}
	return false
}

func (m *CheckIfAccessibleResp) GetStarTrekEntry() bool {
	if m != nil {
		return m.StarTrekEntry
	}
	return false
}

// 安全开关
type SetChanceGameSwitchReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	OnOrOff              bool     `protobuf:"varint,2,opt,name=on_or_off,json=onOrOff,proto3" json:"on_or_off,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChanceGameSwitchReq) Reset()         { *m = SetChanceGameSwitchReq{} }
func (m *SetChanceGameSwitchReq) String() string { return proto.CompactTextString(m) }
func (*SetChanceGameSwitchReq) ProtoMessage()    {}
func (*SetChanceGameSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{14}
}
func (m *SetChanceGameSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChanceGameSwitchReq.Unmarshal(m, b)
}
func (m *SetChanceGameSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChanceGameSwitchReq.Marshal(b, m, deterministic)
}
func (dst *SetChanceGameSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChanceGameSwitchReq.Merge(dst, src)
}
func (m *SetChanceGameSwitchReq) XXX_Size() int {
	return xxx_messageInfo_SetChanceGameSwitchReq.Size(m)
}
func (m *SetChanceGameSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChanceGameSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChanceGameSwitchReq proto.InternalMessageInfo

func (m *SetChanceGameSwitchReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *SetChanceGameSwitchReq) GetOnOrOff() bool {
	if m != nil {
		return m.OnOrOff
	}
	return false
}

type SetChanceGameSwitchResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChanceGameSwitchResp) Reset()         { *m = SetChanceGameSwitchResp{} }
func (m *SetChanceGameSwitchResp) String() string { return proto.CompactTextString(m) }
func (*SetChanceGameSwitchResp) ProtoMessage()    {}
func (*SetChanceGameSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{15}
}
func (m *SetChanceGameSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChanceGameSwitchResp.Unmarshal(m, b)
}
func (m *SetChanceGameSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChanceGameSwitchResp.Marshal(b, m, deterministic)
}
func (dst *SetChanceGameSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChanceGameSwitchResp.Merge(dst, src)
}
func (m *SetChanceGameSwitchResp) XXX_Size() int {
	return xxx_messageInfo_SetChanceGameSwitchResp.Size(m)
}
func (m *SetChanceGameSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChanceGameSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChanceGameSwitchResp proto.InternalMessageInfo

type ChanceGameSwitchState struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	OnOrOff              bool     `protobuf:"varint,2,opt,name=on_or_off,json=onOrOff,proto3" json:"on_or_off,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChanceGameSwitchState) Reset()         { *m = ChanceGameSwitchState{} }
func (m *ChanceGameSwitchState) String() string { return proto.CompactTextString(m) }
func (*ChanceGameSwitchState) ProtoMessage()    {}
func (*ChanceGameSwitchState) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{16}
}
func (m *ChanceGameSwitchState) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChanceGameSwitchState.Unmarshal(m, b)
}
func (m *ChanceGameSwitchState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChanceGameSwitchState.Marshal(b, m, deterministic)
}
func (dst *ChanceGameSwitchState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChanceGameSwitchState.Merge(dst, src)
}
func (m *ChanceGameSwitchState) XXX_Size() int {
	return xxx_messageInfo_ChanceGameSwitchState.Size(m)
}
func (m *ChanceGameSwitchState) XXX_DiscardUnknown() {
	xxx_messageInfo_ChanceGameSwitchState.DiscardUnknown(m)
}

var xxx_messageInfo_ChanceGameSwitchState proto.InternalMessageInfo

func (m *ChanceGameSwitchState) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *ChanceGameSwitchState) GetOnOrOff() bool {
	if m != nil {
		return m.OnOrOff
	}
	return false
}

// 一次拉取所有玩法入口状态
type GetAllChanceGameSwitchStateReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllChanceGameSwitchStateReq) Reset()         { *m = GetAllChanceGameSwitchStateReq{} }
func (m *GetAllChanceGameSwitchStateReq) String() string { return proto.CompactTextString(m) }
func (*GetAllChanceGameSwitchStateReq) ProtoMessage()    {}
func (*GetAllChanceGameSwitchStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{17}
}
func (m *GetAllChanceGameSwitchStateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChanceGameSwitchStateReq.Unmarshal(m, b)
}
func (m *GetAllChanceGameSwitchStateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChanceGameSwitchStateReq.Marshal(b, m, deterministic)
}
func (dst *GetAllChanceGameSwitchStateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChanceGameSwitchStateReq.Merge(dst, src)
}
func (m *GetAllChanceGameSwitchStateReq) XXX_Size() int {
	return xxx_messageInfo_GetAllChanceGameSwitchStateReq.Size(m)
}
func (m *GetAllChanceGameSwitchStateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChanceGameSwitchStateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChanceGameSwitchStateReq proto.InternalMessageInfo

type GetAllChanceGameSwitchStateResp struct {
	SwitchList           []*ChanceGameSwitchState `protobuf:"bytes,1,rep,name=switch_list,json=switchList,proto3" json:"switch_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetAllChanceGameSwitchStateResp) Reset()         { *m = GetAllChanceGameSwitchStateResp{} }
func (m *GetAllChanceGameSwitchStateResp) String() string { return proto.CompactTextString(m) }
func (*GetAllChanceGameSwitchStateResp) ProtoMessage()    {}
func (*GetAllChanceGameSwitchStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{18}
}
func (m *GetAllChanceGameSwitchStateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChanceGameSwitchStateResp.Unmarshal(m, b)
}
func (m *GetAllChanceGameSwitchStateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChanceGameSwitchStateResp.Marshal(b, m, deterministic)
}
func (dst *GetAllChanceGameSwitchStateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChanceGameSwitchStateResp.Merge(dst, src)
}
func (m *GetAllChanceGameSwitchStateResp) XXX_Size() int {
	return xxx_messageInfo_GetAllChanceGameSwitchStateResp.Size(m)
}
func (m *GetAllChanceGameSwitchStateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChanceGameSwitchStateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChanceGameSwitchStateResp proto.InternalMessageInfo

func (m *GetAllChanceGameSwitchStateResp) GetSwitchList() []*ChanceGameSwitchState {
	if m != nil {
		return m.SwitchList
	}
	return nil
}

type ListItemConf struct {
	ListId               uint32   `protobuf:"varint,1,opt,name=list_id,json=listId,proto3" json:"list_id,omitempty"`
	GameType             []uint32 `protobuf:"varint,2,rep,packed,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	Ctime                uint32   `protobuf:"varint,3,opt,name=ctime,proto3" json:"ctime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListItemConf) Reset()         { *m = ListItemConf{} }
func (m *ListItemConf) String() string { return proto.CompactTextString(m) }
func (*ListItemConf) ProtoMessage()    {}
func (*ListItemConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{19}
}
func (m *ListItemConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListItemConf.Unmarshal(m, b)
}
func (m *ListItemConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListItemConf.Marshal(b, m, deterministic)
}
func (dst *ListItemConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListItemConf.Merge(dst, src)
}
func (m *ListItemConf) XXX_Size() int {
	return xxx_messageInfo_ListItemConf.Size(m)
}
func (m *ListItemConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ListItemConf.DiscardUnknown(m)
}

var xxx_messageInfo_ListItemConf proto.InternalMessageInfo

func (m *ListItemConf) GetListId() uint32 {
	if m != nil {
		return m.ListId
	}
	return 0
}

func (m *ListItemConf) GetGameType() []uint32 {
	if m != nil {
		return m.GameType
	}
	return nil
}

func (m *ListItemConf) GetCtime() uint32 {
	if m != nil {
		return m.Ctime
	}
	return 0
}

type AddChannelBWListV2Req struct {
	ChannelList          []*ListItemConf `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	ListType             uint32          `protobuf:"varint,2,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddChannelBWListV2Req) Reset()         { *m = AddChannelBWListV2Req{} }
func (m *AddChannelBWListV2Req) String() string { return proto.CompactTextString(m) }
func (*AddChannelBWListV2Req) ProtoMessage()    {}
func (*AddChannelBWListV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{20}
}
func (m *AddChannelBWListV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelBWListV2Req.Unmarshal(m, b)
}
func (m *AddChannelBWListV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelBWListV2Req.Marshal(b, m, deterministic)
}
func (dst *AddChannelBWListV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelBWListV2Req.Merge(dst, src)
}
func (m *AddChannelBWListV2Req) XXX_Size() int {
	return xxx_messageInfo_AddChannelBWListV2Req.Size(m)
}
func (m *AddChannelBWListV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelBWListV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelBWListV2Req proto.InternalMessageInfo

func (m *AddChannelBWListV2Req) GetChannelList() []*ListItemConf {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *AddChannelBWListV2Req) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

type AddChannelBWListV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelBWListV2Resp) Reset()         { *m = AddChannelBWListV2Resp{} }
func (m *AddChannelBWListV2Resp) String() string { return proto.CompactTextString(m) }
func (*AddChannelBWListV2Resp) ProtoMessage()    {}
func (*AddChannelBWListV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{21}
}
func (m *AddChannelBWListV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelBWListV2Resp.Unmarshal(m, b)
}
func (m *AddChannelBWListV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelBWListV2Resp.Marshal(b, m, deterministic)
}
func (dst *AddChannelBWListV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelBWListV2Resp.Merge(dst, src)
}
func (m *AddChannelBWListV2Resp) XXX_Size() int {
	return xxx_messageInfo_AddChannelBWListV2Resp.Size(m)
}
func (m *AddChannelBWListV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelBWListV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelBWListV2Resp proto.InternalMessageInfo

type AddUserBWListV2Req struct {
	UserList             []*ListItemConf `protobuf:"bytes,1,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	ListType             uint32          `protobuf:"varint,2,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddUserBWListV2Req) Reset()         { *m = AddUserBWListV2Req{} }
func (m *AddUserBWListV2Req) String() string { return proto.CompactTextString(m) }
func (*AddUserBWListV2Req) ProtoMessage()    {}
func (*AddUserBWListV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{22}
}
func (m *AddUserBWListV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserBWListV2Req.Unmarshal(m, b)
}
func (m *AddUserBWListV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserBWListV2Req.Marshal(b, m, deterministic)
}
func (dst *AddUserBWListV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserBWListV2Req.Merge(dst, src)
}
func (m *AddUserBWListV2Req) XXX_Size() int {
	return xxx_messageInfo_AddUserBWListV2Req.Size(m)
}
func (m *AddUserBWListV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserBWListV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserBWListV2Req proto.InternalMessageInfo

func (m *AddUserBWListV2Req) GetUserList() []*ListItemConf {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *AddUserBWListV2Req) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

type AddUserBWListV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserBWListV2Resp) Reset()         { *m = AddUserBWListV2Resp{} }
func (m *AddUserBWListV2Resp) String() string { return proto.CompactTextString(m) }
func (*AddUserBWListV2Resp) ProtoMessage()    {}
func (*AddUserBWListV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{23}
}
func (m *AddUserBWListV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserBWListV2Resp.Unmarshal(m, b)
}
func (m *AddUserBWListV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserBWListV2Resp.Marshal(b, m, deterministic)
}
func (dst *AddUserBWListV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserBWListV2Resp.Merge(dst, src)
}
func (m *AddUserBWListV2Resp) XXX_Size() int {
	return xxx_messageInfo_AddUserBWListV2Resp.Size(m)
}
func (m *AddUserBWListV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserBWListV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserBWListV2Resp proto.InternalMessageInfo

// 删除用户黑/白名单
type BatDelUserBWListV2Req struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	ListType             uint32   `protobuf:"varint,2,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelUserBWListV2Req) Reset()         { *m = BatDelUserBWListV2Req{} }
func (m *BatDelUserBWListV2Req) String() string { return proto.CompactTextString(m) }
func (*BatDelUserBWListV2Req) ProtoMessage()    {}
func (*BatDelUserBWListV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{24}
}
func (m *BatDelUserBWListV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelUserBWListV2Req.Unmarshal(m, b)
}
func (m *BatDelUserBWListV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelUserBWListV2Req.Marshal(b, m, deterministic)
}
func (dst *BatDelUserBWListV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelUserBWListV2Req.Merge(dst, src)
}
func (m *BatDelUserBWListV2Req) XXX_Size() int {
	return xxx_messageInfo_BatDelUserBWListV2Req.Size(m)
}
func (m *BatDelUserBWListV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelUserBWListV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelUserBWListV2Req proto.InternalMessageInfo

func (m *BatDelUserBWListV2Req) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatDelUserBWListV2Req) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

type BatDelUserBWListV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelUserBWListV2Resp) Reset()         { *m = BatDelUserBWListV2Resp{} }
func (m *BatDelUserBWListV2Resp) String() string { return proto.CompactTextString(m) }
func (*BatDelUserBWListV2Resp) ProtoMessage()    {}
func (*BatDelUserBWListV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{25}
}
func (m *BatDelUserBWListV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelUserBWListV2Resp.Unmarshal(m, b)
}
func (m *BatDelUserBWListV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelUserBWListV2Resp.Marshal(b, m, deterministic)
}
func (dst *BatDelUserBWListV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelUserBWListV2Resp.Merge(dst, src)
}
func (m *BatDelUserBWListV2Resp) XXX_Size() int {
	return xxx_messageInfo_BatDelUserBWListV2Resp.Size(m)
}
func (m *BatDelUserBWListV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelUserBWListV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelUserBWListV2Resp proto.InternalMessageInfo

// 删除房间黑名单
type BatDelChannelBWListV2Req struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelChannelBWListV2Req) Reset()         { *m = BatDelChannelBWListV2Req{} }
func (m *BatDelChannelBWListV2Req) String() string { return proto.CompactTextString(m) }
func (*BatDelChannelBWListV2Req) ProtoMessage()    {}
func (*BatDelChannelBWListV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{26}
}
func (m *BatDelChannelBWListV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelChannelBWListV2Req.Unmarshal(m, b)
}
func (m *BatDelChannelBWListV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelChannelBWListV2Req.Marshal(b, m, deterministic)
}
func (dst *BatDelChannelBWListV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelChannelBWListV2Req.Merge(dst, src)
}
func (m *BatDelChannelBWListV2Req) XXX_Size() int {
	return xxx_messageInfo_BatDelChannelBWListV2Req.Size(m)
}
func (m *BatDelChannelBWListV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelChannelBWListV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelChannelBWListV2Req proto.InternalMessageInfo

func (m *BatDelChannelBWListV2Req) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatDelChannelBWListV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatDelChannelBWListV2Resp) Reset()         { *m = BatDelChannelBWListV2Resp{} }
func (m *BatDelChannelBWListV2Resp) String() string { return proto.CompactTextString(m) }
func (*BatDelChannelBWListV2Resp) ProtoMessage()    {}
func (*BatDelChannelBWListV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{27}
}
func (m *BatDelChannelBWListV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatDelChannelBWListV2Resp.Unmarshal(m, b)
}
func (m *BatDelChannelBWListV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatDelChannelBWListV2Resp.Marshal(b, m, deterministic)
}
func (dst *BatDelChannelBWListV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatDelChannelBWListV2Resp.Merge(dst, src)
}
func (m *BatDelChannelBWListV2Resp) XXX_Size() int {
	return xxx_messageInfo_BatDelChannelBWListV2Resp.Size(m)
}
func (m *BatDelChannelBWListV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatDelChannelBWListV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_BatDelChannelBWListV2Resp proto.InternalMessageInfo

// 获取房间黑白名单list
type GetChannelBWListInfoV2Req struct {
	ListType             uint32   `protobuf:"varint,1,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	ChannelId            uint32   `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelBWListInfoV2Req) Reset()         { *m = GetChannelBWListInfoV2Req{} }
func (m *GetChannelBWListInfoV2Req) String() string { return proto.CompactTextString(m) }
func (*GetChannelBWListInfoV2Req) ProtoMessage()    {}
func (*GetChannelBWListInfoV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{28}
}
func (m *GetChannelBWListInfoV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBWListInfoV2Req.Unmarshal(m, b)
}
func (m *GetChannelBWListInfoV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBWListInfoV2Req.Marshal(b, m, deterministic)
}
func (dst *GetChannelBWListInfoV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBWListInfoV2Req.Merge(dst, src)
}
func (m *GetChannelBWListInfoV2Req) XXX_Size() int {
	return xxx_messageInfo_GetChannelBWListInfoV2Req.Size(m)
}
func (m *GetChannelBWListInfoV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBWListInfoV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBWListInfoV2Req proto.InternalMessageInfo

func (m *GetChannelBWListInfoV2Req) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

func (m *GetChannelBWListInfoV2Req) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelBWListInfoV2Req) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetChannelBWListInfoV2Req) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelBWListInfoV2Resp struct {
	ChannelList          []*ListItemConf `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	TotalCnt             uint32          `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetChannelBWListInfoV2Resp) Reset()         { *m = GetChannelBWListInfoV2Resp{} }
func (m *GetChannelBWListInfoV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetChannelBWListInfoV2Resp) ProtoMessage()    {}
func (*GetChannelBWListInfoV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{29}
}
func (m *GetChannelBWListInfoV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBWListInfoV2Resp.Unmarshal(m, b)
}
func (m *GetChannelBWListInfoV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBWListInfoV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetChannelBWListInfoV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBWListInfoV2Resp.Merge(dst, src)
}
func (m *GetChannelBWListInfoV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetChannelBWListInfoV2Resp.Size(m)
}
func (m *GetChannelBWListInfoV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBWListInfoV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBWListInfoV2Resp proto.InternalMessageInfo

func (m *GetChannelBWListInfoV2Resp) GetChannelList() []*ListItemConf {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetChannelBWListInfoV2Resp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 获取用户黑白名单list
type GetUserBWListInfoV2Req struct {
	ListType             uint32   `protobuf:"varint,1,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBWListInfoV2Req) Reset()         { *m = GetUserBWListInfoV2Req{} }
func (m *GetUserBWListInfoV2Req) String() string { return proto.CompactTextString(m) }
func (*GetUserBWListInfoV2Req) ProtoMessage()    {}
func (*GetUserBWListInfoV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{30}
}
func (m *GetUserBWListInfoV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBWListInfoV2Req.Unmarshal(m, b)
}
func (m *GetUserBWListInfoV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBWListInfoV2Req.Marshal(b, m, deterministic)
}
func (dst *GetUserBWListInfoV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBWListInfoV2Req.Merge(dst, src)
}
func (m *GetUserBWListInfoV2Req) XXX_Size() int {
	return xxx_messageInfo_GetUserBWListInfoV2Req.Size(m)
}
func (m *GetUserBWListInfoV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBWListInfoV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBWListInfoV2Req proto.InternalMessageInfo

func (m *GetUserBWListInfoV2Req) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

func (m *GetUserBWListInfoV2Req) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetUserBWListInfoV2Req) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetUserBWListInfoV2Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserBWListInfoV2Resp struct {
	UbwInfoList          []*ListItemConf `protobuf:"bytes,1,rep,name=ubw_info_list,json=ubwInfoList,proto3" json:"ubw_info_list,omitempty"`
	TotalCnt             uint32          `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserBWListInfoV2Resp) Reset()         { *m = GetUserBWListInfoV2Resp{} }
func (m *GetUserBWListInfoV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetUserBWListInfoV2Resp) ProtoMessage()    {}
func (*GetUserBWListInfoV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{31}
}
func (m *GetUserBWListInfoV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBWListInfoV2Resp.Unmarshal(m, b)
}
func (m *GetUserBWListInfoV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBWListInfoV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetUserBWListInfoV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBWListInfoV2Resp.Merge(dst, src)
}
func (m *GetUserBWListInfoV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetUserBWListInfoV2Resp.Size(m)
}
func (m *GetUserBWListInfoV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBWListInfoV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBWListInfoV2Resp proto.InternalMessageInfo

func (m *GetUserBWListInfoV2Resp) GetUbwInfoList() []*ListItemConf {
	if m != nil {
		return m.UbwInfoList
	}
	return nil
}

func (m *GetUserBWListInfoV2Resp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 二级条件配置
type SubAccessCondition struct {
	ConditionType        uint32   `protobuf:"varint,1,opt,name=condition_type,json=conditionType,proto3" json:"condition_type,omitempty"`
	Threshold            uint32   `protobuf:"varint,2,opt,name=threshold,proto3" json:"threshold,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubAccessCondition) Reset()         { *m = SubAccessCondition{} }
func (m *SubAccessCondition) String() string { return proto.CompactTextString(m) }
func (*SubAccessCondition) ProtoMessage()    {}
func (*SubAccessCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{32}
}
func (m *SubAccessCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubAccessCondition.Unmarshal(m, b)
}
func (m *SubAccessCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubAccessCondition.Marshal(b, m, deterministic)
}
func (dst *SubAccessCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubAccessCondition.Merge(dst, src)
}
func (m *SubAccessCondition) XXX_Size() int {
	return xxx_messageInfo_SubAccessCondition.Size(m)
}
func (m *SubAccessCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_SubAccessCondition.DiscardUnknown(m)
}

var xxx_messageInfo_SubAccessCondition proto.InternalMessageInfo

func (m *SubAccessCondition) GetConditionType() uint32 {
	if m != nil {
		return m.ConditionType
	}
	return 0
}

func (m *SubAccessCondition) GetThreshold() uint32 {
	if m != nil {
		return m.Threshold
	}
	return 0
}

// 一级条件配置
type AccessCondition struct {
	SubList              []*SubAccessCondition `protobuf:"bytes,1,rep,name=sub_list,json=subList,proto3" json:"sub_list,omitempty"`
	RelateType           uint32                `protobuf:"varint,2,opt,name=relate_type,json=relateType,proto3" json:"relate_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *AccessCondition) Reset()         { *m = AccessCondition{} }
func (m *AccessCondition) String() string { return proto.CompactTextString(m) }
func (*AccessCondition) ProtoMessage()    {}
func (*AccessCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{33}
}
func (m *AccessCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccessCondition.Unmarshal(m, b)
}
func (m *AccessCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccessCondition.Marshal(b, m, deterministic)
}
func (dst *AccessCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccessCondition.Merge(dst, src)
}
func (m *AccessCondition) XXX_Size() int {
	return xxx_messageInfo_AccessCondition.Size(m)
}
func (m *AccessCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_AccessCondition.DiscardUnknown(m)
}

var xxx_messageInfo_AccessCondition proto.InternalMessageInfo

func (m *AccessCondition) GetSubList() []*SubAccessCondition {
	if m != nil {
		return m.SubList
	}
	return nil
}

func (m *AccessCondition) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

// 设置可见人群配置
type SetChanceGameAccessCondReq struct {
	GameType      uint32             `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	CondType      uint32             `protobuf:"varint,2,opt,name=cond_type,json=condType,proto3" json:"cond_type,omitempty"`
	ConditionList []*AccessCondition `protobuf:"bytes,3,rep,name=condition_list,json=conditionList,proto3" json:"condition_list,omitempty"`
	RelateType    uint32             `protobuf:"varint,4,opt,name=relate_type,json=relateType,proto3" json:"relate_type,omitempty"`
	// 6.36.0 幸运礼物可见配置区分礼物id
	MagicId              uint32   `protobuf:"varint,5,opt,name=magic_id,json=magicId,proto3" json:"magic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChanceGameAccessCondReq) Reset()         { *m = SetChanceGameAccessCondReq{} }
func (m *SetChanceGameAccessCondReq) String() string { return proto.CompactTextString(m) }
func (*SetChanceGameAccessCondReq) ProtoMessage()    {}
func (*SetChanceGameAccessCondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{34}
}
func (m *SetChanceGameAccessCondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChanceGameAccessCondReq.Unmarshal(m, b)
}
func (m *SetChanceGameAccessCondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChanceGameAccessCondReq.Marshal(b, m, deterministic)
}
func (dst *SetChanceGameAccessCondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChanceGameAccessCondReq.Merge(dst, src)
}
func (m *SetChanceGameAccessCondReq) XXX_Size() int {
	return xxx_messageInfo_SetChanceGameAccessCondReq.Size(m)
}
func (m *SetChanceGameAccessCondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChanceGameAccessCondReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChanceGameAccessCondReq proto.InternalMessageInfo

func (m *SetChanceGameAccessCondReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *SetChanceGameAccessCondReq) GetCondType() uint32 {
	if m != nil {
		return m.CondType
	}
	return 0
}

func (m *SetChanceGameAccessCondReq) GetConditionList() []*AccessCondition {
	if m != nil {
		return m.ConditionList
	}
	return nil
}

func (m *SetChanceGameAccessCondReq) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

func (m *SetChanceGameAccessCondReq) GetMagicId() uint32 {
	if m != nil {
		return m.MagicId
	}
	return 0
}

type SetChanceGameAccessCondResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChanceGameAccessCondResp) Reset()         { *m = SetChanceGameAccessCondResp{} }
func (m *SetChanceGameAccessCondResp) String() string { return proto.CompactTextString(m) }
func (*SetChanceGameAccessCondResp) ProtoMessage()    {}
func (*SetChanceGameAccessCondResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{35}
}
func (m *SetChanceGameAccessCondResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChanceGameAccessCondResp.Unmarshal(m, b)
}
func (m *SetChanceGameAccessCondResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChanceGameAccessCondResp.Marshal(b, m, deterministic)
}
func (dst *SetChanceGameAccessCondResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChanceGameAccessCondResp.Merge(dst, src)
}
func (m *SetChanceGameAccessCondResp) XXX_Size() int {
	return xxx_messageInfo_SetChanceGameAccessCondResp.Size(m)
}
func (m *SetChanceGameAccessCondResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChanceGameAccessCondResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChanceGameAccessCondResp proto.InternalMessageInfo

// 获取可见人群配置
type GetChanceGameAccessCondReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	CondType             uint32   `protobuf:"varint,2,opt,name=cond_type,json=condType,proto3" json:"cond_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChanceGameAccessCondReq) Reset()         { *m = GetChanceGameAccessCondReq{} }
func (m *GetChanceGameAccessCondReq) String() string { return proto.CompactTextString(m) }
func (*GetChanceGameAccessCondReq) ProtoMessage()    {}
func (*GetChanceGameAccessCondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{36}
}
func (m *GetChanceGameAccessCondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChanceGameAccessCondReq.Unmarshal(m, b)
}
func (m *GetChanceGameAccessCondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChanceGameAccessCondReq.Marshal(b, m, deterministic)
}
func (dst *GetChanceGameAccessCondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChanceGameAccessCondReq.Merge(dst, src)
}
func (m *GetChanceGameAccessCondReq) XXX_Size() int {
	return xxx_messageInfo_GetChanceGameAccessCondReq.Size(m)
}
func (m *GetChanceGameAccessCondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChanceGameAccessCondReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChanceGameAccessCondReq proto.InternalMessageInfo

func (m *GetChanceGameAccessCondReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GetChanceGameAccessCondReq) GetCondType() uint32 {
	if m != nil {
		return m.CondType
	}
	return 0
}

type GetChanceGameAccessCondResp struct {
	ConditionList        []*AccessCondition `protobuf:"bytes,1,rep,name=condition_list,json=conditionList,proto3" json:"condition_list,omitempty"`
	RelateType           uint32             `protobuf:"varint,2,opt,name=relate_type,json=relateType,proto3" json:"relate_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChanceGameAccessCondResp) Reset()         { *m = GetChanceGameAccessCondResp{} }
func (m *GetChanceGameAccessCondResp) String() string { return proto.CompactTextString(m) }
func (*GetChanceGameAccessCondResp) ProtoMessage()    {}
func (*GetChanceGameAccessCondResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{37}
}
func (m *GetChanceGameAccessCondResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChanceGameAccessCondResp.Unmarshal(m, b)
}
func (m *GetChanceGameAccessCondResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChanceGameAccessCondResp.Marshal(b, m, deterministic)
}
func (dst *GetChanceGameAccessCondResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChanceGameAccessCondResp.Merge(dst, src)
}
func (m *GetChanceGameAccessCondResp) XXX_Size() int {
	return xxx_messageInfo_GetChanceGameAccessCondResp.Size(m)
}
func (m *GetChanceGameAccessCondResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChanceGameAccessCondResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChanceGameAccessCondResp proto.InternalMessageInfo

func (m *GetChanceGameAccessCondResp) GetConditionList() []*AccessCondition {
	if m != nil {
		return m.ConditionList
	}
	return nil
}

func (m *GetChanceGameAccessCondResp) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

// 获取幸运礼物可见人群配置
type GetMagicSpiritAccessCondReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	CondType             uint32   `protobuf:"varint,2,opt,name=cond_type,json=condType,proto3" json:"cond_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMagicSpiritAccessCondReq) Reset()         { *m = GetMagicSpiritAccessCondReq{} }
func (m *GetMagicSpiritAccessCondReq) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritAccessCondReq) ProtoMessage()    {}
func (*GetMagicSpiritAccessCondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{38}
}
func (m *GetMagicSpiritAccessCondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritAccessCondReq.Unmarshal(m, b)
}
func (m *GetMagicSpiritAccessCondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritAccessCondReq.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritAccessCondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritAccessCondReq.Merge(dst, src)
}
func (m *GetMagicSpiritAccessCondReq) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritAccessCondReq.Size(m)
}
func (m *GetMagicSpiritAccessCondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritAccessCondReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritAccessCondReq proto.InternalMessageInfo

func (m *GetMagicSpiritAccessCondReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GetMagicSpiritAccessCondReq) GetCondType() uint32 {
	if m != nil {
		return m.CondType
	}
	return 0
}

type MagicSpiritAccessCond struct {
	MagicId              uint32             `protobuf:"varint,1,opt,name=magic_id,json=magicId,proto3" json:"magic_id,omitempty"`
	ConditionList        []*AccessCondition `protobuf:"bytes,2,rep,name=condition_list,json=conditionList,proto3" json:"condition_list,omitempty"`
	RelateType           uint32             `protobuf:"varint,3,opt,name=relate_type,json=relateType,proto3" json:"relate_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MagicSpiritAccessCond) Reset()         { *m = MagicSpiritAccessCond{} }
func (m *MagicSpiritAccessCond) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritAccessCond) ProtoMessage()    {}
func (*MagicSpiritAccessCond) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{39}
}
func (m *MagicSpiritAccessCond) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritAccessCond.Unmarshal(m, b)
}
func (m *MagicSpiritAccessCond) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritAccessCond.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritAccessCond) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritAccessCond.Merge(dst, src)
}
func (m *MagicSpiritAccessCond) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritAccessCond.Size(m)
}
func (m *MagicSpiritAccessCond) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritAccessCond.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritAccessCond proto.InternalMessageInfo

func (m *MagicSpiritAccessCond) GetMagicId() uint32 {
	if m != nil {
		return m.MagicId
	}
	return 0
}

func (m *MagicSpiritAccessCond) GetConditionList() []*AccessCondition {
	if m != nil {
		return m.ConditionList
	}
	return nil
}

func (m *MagicSpiritAccessCond) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

type GetMagicSpiritAccessCondResp struct {
	ConfList             []*MagicSpiritAccessCond `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetMagicSpiritAccessCondResp) Reset()         { *m = GetMagicSpiritAccessCondResp{} }
func (m *GetMagicSpiritAccessCondResp) String() string { return proto.CompactTextString(m) }
func (*GetMagicSpiritAccessCondResp) ProtoMessage()    {}
func (*GetMagicSpiritAccessCondResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{40}
}
func (m *GetMagicSpiritAccessCondResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMagicSpiritAccessCondResp.Unmarshal(m, b)
}
func (m *GetMagicSpiritAccessCondResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMagicSpiritAccessCondResp.Marshal(b, m, deterministic)
}
func (dst *GetMagicSpiritAccessCondResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMagicSpiritAccessCondResp.Merge(dst, src)
}
func (m *GetMagicSpiritAccessCondResp) XXX_Size() int {
	return xxx_messageInfo_GetMagicSpiritAccessCondResp.Size(m)
}
func (m *GetMagicSpiritAccessCondResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMagicSpiritAccessCondResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMagicSpiritAccessCondResp proto.InternalMessageInfo

func (m *GetMagicSpiritAccessCondResp) GetConfList() []*MagicSpiritAccessCond {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type DelMagicSpiritAccessCondReq struct {
	MagicId              uint32   `protobuf:"varint,1,opt,name=magic_id,json=magicId,proto3" json:"magic_id,omitempty"`
	CondType             uint32   `protobuf:"varint,2,opt,name=cond_type,json=condType,proto3" json:"cond_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMagicSpiritAccessCondReq) Reset()         { *m = DelMagicSpiritAccessCondReq{} }
func (m *DelMagicSpiritAccessCondReq) String() string { return proto.CompactTextString(m) }
func (*DelMagicSpiritAccessCondReq) ProtoMessage()    {}
func (*DelMagicSpiritAccessCondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{41}
}
func (m *DelMagicSpiritAccessCondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMagicSpiritAccessCondReq.Unmarshal(m, b)
}
func (m *DelMagicSpiritAccessCondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMagicSpiritAccessCondReq.Marshal(b, m, deterministic)
}
func (dst *DelMagicSpiritAccessCondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMagicSpiritAccessCondReq.Merge(dst, src)
}
func (m *DelMagicSpiritAccessCondReq) XXX_Size() int {
	return xxx_messageInfo_DelMagicSpiritAccessCondReq.Size(m)
}
func (m *DelMagicSpiritAccessCondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMagicSpiritAccessCondReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelMagicSpiritAccessCondReq proto.InternalMessageInfo

func (m *DelMagicSpiritAccessCondReq) GetMagicId() uint32 {
	if m != nil {
		return m.MagicId
	}
	return 0
}

func (m *DelMagicSpiritAccessCondReq) GetCondType() uint32 {
	if m != nil {
		return m.CondType
	}
	return 0
}

type DelMagicSpiritAccessCondResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelMagicSpiritAccessCondResp) Reset()         { *m = DelMagicSpiritAccessCondResp{} }
func (m *DelMagicSpiritAccessCondResp) String() string { return proto.CompactTextString(m) }
func (*DelMagicSpiritAccessCondResp) ProtoMessage()    {}
func (*DelMagicSpiritAccessCondResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{42}
}
func (m *DelMagicSpiritAccessCondResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelMagicSpiritAccessCondResp.Unmarshal(m, b)
}
func (m *DelMagicSpiritAccessCondResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelMagicSpiritAccessCondResp.Marshal(b, m, deterministic)
}
func (dst *DelMagicSpiritAccessCondResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelMagicSpiritAccessCondResp.Merge(dst, src)
}
func (m *DelMagicSpiritAccessCondResp) XXX_Size() int {
	return xxx_messageInfo_DelMagicSpiritAccessCondResp.Size(m)
}
func (m *DelMagicSpiritAccessCondResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelMagicSpiritAccessCondResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelMagicSpiritAccessCondResp proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type CheckGameEntryAccessReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameType             []uint32 `protobuf:"varint,3,rep,packed,name=gameType,proto3" json:"gameType,omitempty"`
	ClientIp             uint32   `protobuf:"varint,4,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	MarketId             uint32   `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckGameEntryAccessReq) Reset()         { *m = CheckGameEntryAccessReq{} }
func (m *CheckGameEntryAccessReq) String() string { return proto.CompactTextString(m) }
func (*CheckGameEntryAccessReq) ProtoMessage()    {}
func (*CheckGameEntryAccessReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{43}
}
func (m *CheckGameEntryAccessReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckGameEntryAccessReq.Unmarshal(m, b)
}
func (m *CheckGameEntryAccessReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckGameEntryAccessReq.Marshal(b, m, deterministic)
}
func (dst *CheckGameEntryAccessReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckGameEntryAccessReq.Merge(dst, src)
}
func (m *CheckGameEntryAccessReq) XXX_Size() int {
	return xxx_messageInfo_CheckGameEntryAccessReq.Size(m)
}
func (m *CheckGameEntryAccessReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckGameEntryAccessReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckGameEntryAccessReq proto.InternalMessageInfo

func (m *CheckGameEntryAccessReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckGameEntryAccessReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckGameEntryAccessReq) GetGameType() []uint32 {
	if m != nil {
		return m.GameType
	}
	return nil
}

func (m *CheckGameEntryAccessReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *CheckGameEntryAccessReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GameEntryAccess struct {
	Access               bool               `protobuf:"varint,1,opt,name=access,proto3" json:"access,omitempty"`
	Switch               bool               `protobuf:"varint,2,opt,name=switch,proto3" json:"switch,omitempty"`
	ConditionList        []*AccessCondition `protobuf:"bytes,3,rep,name=condition_list,json=conditionList,proto3" json:"condition_list,omitempty"`
	RelateType           uint32             `protobuf:"varint,4,opt,name=relate_type,json=relateType,proto3" json:"relate_type,omitempty"`
	GameType             uint32             `protobuf:"varint,5,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GameEntryAccess) Reset()         { *m = GameEntryAccess{} }
func (m *GameEntryAccess) String() string { return proto.CompactTextString(m) }
func (*GameEntryAccess) ProtoMessage()    {}
func (*GameEntryAccess) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{44}
}
func (m *GameEntryAccess) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameEntryAccess.Unmarshal(m, b)
}
func (m *GameEntryAccess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameEntryAccess.Marshal(b, m, deterministic)
}
func (dst *GameEntryAccess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameEntryAccess.Merge(dst, src)
}
func (m *GameEntryAccess) XXX_Size() int {
	return xxx_messageInfo_GameEntryAccess.Size(m)
}
func (m *GameEntryAccess) XXX_DiscardUnknown() {
	xxx_messageInfo_GameEntryAccess.DiscardUnknown(m)
}

var xxx_messageInfo_GameEntryAccess proto.InternalMessageInfo

func (m *GameEntryAccess) GetAccess() bool {
	if m != nil {
		return m.Access
	}
	return false
}

func (m *GameEntryAccess) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

func (m *GameEntryAccess) GetConditionList() []*AccessCondition {
	if m != nil {
		return m.ConditionList
	}
	return nil
}

func (m *GameEntryAccess) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

func (m *GameEntryAccess) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type CheckGameEntryAccessResp struct {
	ConfList             []*GameEntryAccess `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CheckGameEntryAccessResp) Reset()         { *m = CheckGameEntryAccessResp{} }
func (m *CheckGameEntryAccessResp) String() string { return proto.CompactTextString(m) }
func (*CheckGameEntryAccessResp) ProtoMessage()    {}
func (*CheckGameEntryAccessResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{45}
}
func (m *CheckGameEntryAccessResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckGameEntryAccessResp.Unmarshal(m, b)
}
func (m *CheckGameEntryAccessResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckGameEntryAccessResp.Marshal(b, m, deterministic)
}
func (dst *CheckGameEntryAccessResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckGameEntryAccessResp.Merge(dst, src)
}
func (m *CheckGameEntryAccessResp) XXX_Size() int {
	return xxx_messageInfo_CheckGameEntryAccessResp.Size(m)
}
func (m *CheckGameEntryAccessResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckGameEntryAccessResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckGameEntryAccessResp proto.InternalMessageInfo

func (m *CheckGameEntryAccessResp) GetConfList() []*GameEntryAccess {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type LocalCacheAccessConf struct {
	CondType             uint32             `protobuf:"varint,1,opt,name=cond_type,json=condType,proto3" json:"cond_type,omitempty"`
	ConditionList        []*AccessCondition `protobuf:"bytes,2,rep,name=condition_list,json=conditionList,proto3" json:"condition_list,omitempty"`
	RelateType           uint32             `protobuf:"varint,3,opt,name=relate_type,json=relateType,proto3" json:"relate_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *LocalCacheAccessConf) Reset()         { *m = LocalCacheAccessConf{} }
func (m *LocalCacheAccessConf) String() string { return proto.CompactTextString(m) }
func (*LocalCacheAccessConf) ProtoMessage()    {}
func (*LocalCacheAccessConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{46}
}
func (m *LocalCacheAccessConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LocalCacheAccessConf.Unmarshal(m, b)
}
func (m *LocalCacheAccessConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LocalCacheAccessConf.Marshal(b, m, deterministic)
}
func (dst *LocalCacheAccessConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LocalCacheAccessConf.Merge(dst, src)
}
func (m *LocalCacheAccessConf) XXX_Size() int {
	return xxx_messageInfo_LocalCacheAccessConf.Size(m)
}
func (m *LocalCacheAccessConf) XXX_DiscardUnknown() {
	xxx_messageInfo_LocalCacheAccessConf.DiscardUnknown(m)
}

var xxx_messageInfo_LocalCacheAccessConf proto.InternalMessageInfo

func (m *LocalCacheAccessConf) GetCondType() uint32 {
	if m != nil {
		return m.CondType
	}
	return 0
}

func (m *LocalCacheAccessConf) GetConditionList() []*AccessCondition {
	if m != nil {
		return m.ConditionList
	}
	return nil
}

func (m *LocalCacheAccessConf) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

type GetLocalCacheConfReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	MagicSpiritId        uint32   `protobuf:"varint,2,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLocalCacheConfReq) Reset()         { *m = GetLocalCacheConfReq{} }
func (m *GetLocalCacheConfReq) String() string { return proto.CompactTextString(m) }
func (*GetLocalCacheConfReq) ProtoMessage()    {}
func (*GetLocalCacheConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{47}
}
func (m *GetLocalCacheConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLocalCacheConfReq.Unmarshal(m, b)
}
func (m *GetLocalCacheConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLocalCacheConfReq.Marshal(b, m, deterministic)
}
func (dst *GetLocalCacheConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLocalCacheConfReq.Merge(dst, src)
}
func (m *GetLocalCacheConfReq) XXX_Size() int {
	return xxx_messageInfo_GetLocalCacheConfReq.Size(m)
}
func (m *GetLocalCacheConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLocalCacheConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLocalCacheConfReq proto.InternalMessageInfo

func (m *GetLocalCacheConfReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GetLocalCacheConfReq) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

type GetLocalCacheConfResp struct {
	State                bool                    `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
	UserWhiteList        []uint32                `protobuf:"varint,2,rep,packed,name=user_white_list,json=userWhiteList,proto3" json:"user_white_list,omitempty"`
	UserBlackList        []uint32                `protobuf:"varint,3,rep,packed,name=user_black_list,json=userBlackList,proto3" json:"user_black_list,omitempty"`
	ChannelList          []uint32                `protobuf:"varint,4,rep,packed,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	ConfList             []*LocalCacheAccessConf `protobuf:"bytes,5,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	BeginTime            uint32                  `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32                  `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetLocalCacheConfResp) Reset()         { *m = GetLocalCacheConfResp{} }
func (m *GetLocalCacheConfResp) String() string { return proto.CompactTextString(m) }
func (*GetLocalCacheConfResp) ProtoMessage()    {}
func (*GetLocalCacheConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{48}
}
func (m *GetLocalCacheConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLocalCacheConfResp.Unmarshal(m, b)
}
func (m *GetLocalCacheConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLocalCacheConfResp.Marshal(b, m, deterministic)
}
func (dst *GetLocalCacheConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLocalCacheConfResp.Merge(dst, src)
}
func (m *GetLocalCacheConfResp) XXX_Size() int {
	return xxx_messageInfo_GetLocalCacheConfResp.Size(m)
}
func (m *GetLocalCacheConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLocalCacheConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLocalCacheConfResp proto.InternalMessageInfo

func (m *GetLocalCacheConfResp) GetState() bool {
	if m != nil {
		return m.State
	}
	return false
}

func (m *GetLocalCacheConfResp) GetUserWhiteList() []uint32 {
	if m != nil {
		return m.UserWhiteList
	}
	return nil
}

func (m *GetLocalCacheConfResp) GetUserBlackList() []uint32 {
	if m != nil {
		return m.UserBlackList
	}
	return nil
}

func (m *GetLocalCacheConfResp) GetChannelList() []uint32 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetLocalCacheConfResp) GetConfList() []*LocalCacheAccessConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *GetLocalCacheConfResp) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetLocalCacheConfResp) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetUserValueReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserValueReq) Reset()         { *m = GetUserValueReq{} }
func (m *GetUserValueReq) String() string { return proto.CompactTextString(m) }
func (*GetUserValueReq) ProtoMessage()    {}
func (*GetUserValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{49}
}
func (m *GetUserValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserValueReq.Unmarshal(m, b)
}
func (m *GetUserValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserValueReq.Marshal(b, m, deterministic)
}
func (dst *GetUserValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserValueReq.Merge(dst, src)
}
func (m *GetUserValueReq) XXX_Size() int {
	return xxx_messageInfo_GetUserValueReq.Size(m)
}
func (m *GetUserValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserValueReq proto.InternalMessageInfo

func (m *GetUserValueReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserValueResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nobility             uint64   `protobuf:"varint,2,opt,name=nobility,proto3" json:"nobility,omitempty"`
	Wealth               uint32   `protobuf:"varint,3,opt,name=wealth,proto3" json:"wealth,omitempty"`
	Charm                uint32   `protobuf:"varint,4,opt,name=charm,proto3" json:"charm,omitempty"`
	PlatformLv           uint32   `protobuf:"varint,5,opt,name=platform_lv,json=platformLv,proto3" json:"platform_lv,omitempty"`
	RechargeThirty       uint32   `protobuf:"varint,6,opt,name=recharge_thirty,json=rechargeThirty,proto3" json:"recharge_thirty,omitempty"`
	RechargeNinety       uint32   `protobuf:"varint,7,opt,name=recharge_ninety,json=rechargeNinety,proto3" json:"recharge_ninety,omitempty"`
	RechargeOez          uint32   `protobuf:"varint,8,opt,name=recharge_oez,json=rechargeOez,proto3" json:"recharge_oez,omitempty"`
	AuthResult           bool     `protobuf:"varint,9,opt,name=auth_result,json=authResult,proto3" json:"auth_result,omitempty"`
	WealthV2             uint64   `protobuf:"varint,10,opt,name=wealth_v2,json=wealthV2,proto3" json:"wealth_v2,omitempty"`
	CharmV2              uint64   `protobuf:"varint,11,opt,name=charm_v2,json=charmV2,proto3" json:"charm_v2,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserValueResp) Reset()         { *m = GetUserValueResp{} }
func (m *GetUserValueResp) String() string { return proto.CompactTextString(m) }
func (*GetUserValueResp) ProtoMessage()    {}
func (*GetUserValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{50}
}
func (m *GetUserValueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserValueResp.Unmarshal(m, b)
}
func (m *GetUserValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserValueResp.Marshal(b, m, deterministic)
}
func (dst *GetUserValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserValueResp.Merge(dst, src)
}
func (m *GetUserValueResp) XXX_Size() int {
	return xxx_messageInfo_GetUserValueResp.Size(m)
}
func (m *GetUserValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserValueResp proto.InternalMessageInfo

func (m *GetUserValueResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserValueResp) GetNobility() uint64 {
	if m != nil {
		return m.Nobility
	}
	return 0
}

func (m *GetUserValueResp) GetWealth() uint32 {
	if m != nil {
		return m.Wealth
	}
	return 0
}

func (m *GetUserValueResp) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *GetUserValueResp) GetPlatformLv() uint32 {
	if m != nil {
		return m.PlatformLv
	}
	return 0
}

func (m *GetUserValueResp) GetRechargeThirty() uint32 {
	if m != nil {
		return m.RechargeThirty
	}
	return 0
}

func (m *GetUserValueResp) GetRechargeNinety() uint32 {
	if m != nil {
		return m.RechargeNinety
	}
	return 0
}

func (m *GetUserValueResp) GetRechargeOez() uint32 {
	if m != nil {
		return m.RechargeOez
	}
	return 0
}

func (m *GetUserValueResp) GetAuthResult() bool {
	if m != nil {
		return m.AuthResult
	}
	return false
}

func (m *GetUserValueResp) GetWealthV2() uint64 {
	if m != nil {
		return m.WealthV2
	}
	return 0
}

func (m *GetUserValueResp) GetCharmV2() uint64 {
	if m != nil {
		return m.CharmV2
	}
	return 0
}

type NotifyItem struct {
	IsNeed               bool            `protobuf:"varint,1,opt,name=is_need,json=isNeed,proto3" json:"is_need,omitempty"`
	Text                 string          `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Color                string          `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
	Duration             uint32          `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	PlaceType            NotifyPlaceType `protobuf:"varint,5,opt,name=place_type,json=placeType,proto3,enum=chance_game_entry.NotifyPlaceType" json:"place_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *NotifyItem) Reset()         { *m = NotifyItem{} }
func (m *NotifyItem) String() string { return proto.CompactTextString(m) }
func (*NotifyItem) ProtoMessage()    {}
func (*NotifyItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{51}
}
func (m *NotifyItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyItem.Unmarshal(m, b)
}
func (m *NotifyItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyItem.Marshal(b, m, deterministic)
}
func (dst *NotifyItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyItem.Merge(dst, src)
}
func (m *NotifyItem) XXX_Size() int {
	return xxx_messageInfo_NotifyItem.Size(m)
}
func (m *NotifyItem) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyItem.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyItem proto.InternalMessageInfo

func (m *NotifyItem) GetIsNeed() bool {
	if m != nil {
		return m.IsNeed
	}
	return false
}

func (m *NotifyItem) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *NotifyItem) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

func (m *NotifyItem) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *NotifyItem) GetPlaceType() NotifyPlaceType {
	if m != nil {
		return m.PlaceType
	}
	return NotifyPlaceType_NotifyPlaceType_UNSPECIFIED
}

type NotifyInfo struct {
	NotifyType           NotifyType    `protobuf:"varint,1,opt,name=notify_type,json=notifyType,proto3,enum=chance_game_entry.NotifyType" json:"notify_type,omitempty"`
	BeginTime            uint32        `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32        `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	NotifyList           []*NotifyItem `protobuf:"bytes,4,rep,name=notify_list,json=notifyList,proto3" json:"notify_list,omitempty"`
	HasRed               bool          `protobuf:"varint,5,opt,name=has_red,json=hasRed,proto3" json:"has_red,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *NotifyInfo) Reset()         { *m = NotifyInfo{} }
func (m *NotifyInfo) String() string { return proto.CompactTextString(m) }
func (*NotifyInfo) ProtoMessage()    {}
func (*NotifyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{52}
}
func (m *NotifyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyInfo.Unmarshal(m, b)
}
func (m *NotifyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyInfo.Marshal(b, m, deterministic)
}
func (dst *NotifyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyInfo.Merge(dst, src)
}
func (m *NotifyInfo) XXX_Size() int {
	return xxx_messageInfo_NotifyInfo.Size(m)
}
func (m *NotifyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyInfo proto.InternalMessageInfo

func (m *NotifyInfo) GetNotifyType() NotifyType {
	if m != nil {
		return m.NotifyType
	}
	return NotifyType_NotifyType_UNEXPECTED
}

func (m *NotifyInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *NotifyInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *NotifyInfo) GetNotifyList() []*NotifyItem {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

func (m *NotifyInfo) GetHasRed() bool {
	if m != nil {
		return m.HasRed
	}
	return false
}

type SetGameNotifyInfoReq struct {
	GameType             NotifyGameType `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=chance_game_entry.NotifyGameType" json:"game_type,omitempty"`
	Info                 *NotifyInfo    `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetGameNotifyInfoReq) Reset()         { *m = SetGameNotifyInfoReq{} }
func (m *SetGameNotifyInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetGameNotifyInfoReq) ProtoMessage()    {}
func (*SetGameNotifyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{53}
}
func (m *SetGameNotifyInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameNotifyInfoReq.Unmarshal(m, b)
}
func (m *SetGameNotifyInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameNotifyInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetGameNotifyInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameNotifyInfoReq.Merge(dst, src)
}
func (m *SetGameNotifyInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetGameNotifyInfoReq.Size(m)
}
func (m *SetGameNotifyInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameNotifyInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameNotifyInfoReq proto.InternalMessageInfo

func (m *SetGameNotifyInfoReq) GetGameType() NotifyGameType {
	if m != nil {
		return m.GameType
	}
	return NotifyGameType_NotifyGameType_UNSPECIFIED
}

func (m *SetGameNotifyInfoReq) GetInfo() *NotifyInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetGameNotifyInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameNotifyInfoResp) Reset()         { *m = SetGameNotifyInfoResp{} }
func (m *SetGameNotifyInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetGameNotifyInfoResp) ProtoMessage()    {}
func (*SetGameNotifyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{54}
}
func (m *SetGameNotifyInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameNotifyInfoResp.Unmarshal(m, b)
}
func (m *SetGameNotifyInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameNotifyInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetGameNotifyInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameNotifyInfoResp.Merge(dst, src)
}
func (m *SetGameNotifyInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetGameNotifyInfoResp.Size(m)
}
func (m *SetGameNotifyInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameNotifyInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameNotifyInfoResp proto.InternalMessageInfo

type GetGameNotifyInfoReq struct {
	GameType             NotifyGameType `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=chance_game_entry.NotifyGameType" json:"game_type,omitempty"`
	NotifyType           NotifyType     `protobuf:"varint,2,opt,name=notify_type,json=notifyType,proto3,enum=chance_game_entry.NotifyType" json:"notify_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGameNotifyInfoReq) Reset()         { *m = GetGameNotifyInfoReq{} }
func (m *GetGameNotifyInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGameNotifyInfoReq) ProtoMessage()    {}
func (*GetGameNotifyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{55}
}
func (m *GetGameNotifyInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameNotifyInfoReq.Unmarshal(m, b)
}
func (m *GetGameNotifyInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameNotifyInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGameNotifyInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameNotifyInfoReq.Merge(dst, src)
}
func (m *GetGameNotifyInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGameNotifyInfoReq.Size(m)
}
func (m *GetGameNotifyInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameNotifyInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameNotifyInfoReq proto.InternalMessageInfo

func (m *GetGameNotifyInfoReq) GetGameType() NotifyGameType {
	if m != nil {
		return m.GameType
	}
	return NotifyGameType_NotifyGameType_UNSPECIFIED
}

func (m *GetGameNotifyInfoReq) GetNotifyType() NotifyType {
	if m != nil {
		return m.NotifyType
	}
	return NotifyType_NotifyType_UNEXPECTED
}

type GetGameNotifyInfoResp struct {
	Info                 *NotifyInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGameNotifyInfoResp) Reset()         { *m = GetGameNotifyInfoResp{} }
func (m *GetGameNotifyInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGameNotifyInfoResp) ProtoMessage()    {}
func (*GetGameNotifyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{56}
}
func (m *GetGameNotifyInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameNotifyInfoResp.Unmarshal(m, b)
}
func (m *GetGameNotifyInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameNotifyInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGameNotifyInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameNotifyInfoResp.Merge(dst, src)
}
func (m *GetGameNotifyInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGameNotifyInfoResp.Size(m)
}
func (m *GetGameNotifyInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameNotifyInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameNotifyInfoResp proto.InternalMessageInfo

func (m *GetGameNotifyInfoResp) GetInfo() *NotifyInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GameNotifyInfo struct {
	GameType             NotifyGameType `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=chance_game_entry.NotifyGameType" json:"game_type,omitempty"`
	NotifyList           []*NotifyInfo  `protobuf:"bytes,2,rep,name=notify_list,json=notifyList,proto3" json:"notify_list,omitempty"`
	VersionTime          uint64         `protobuf:"varint,3,opt,name=version_time,json=versionTime,proto3" json:"version_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GameNotifyInfo) Reset()         { *m = GameNotifyInfo{} }
func (m *GameNotifyInfo) String() string { return proto.CompactTextString(m) }
func (*GameNotifyInfo) ProtoMessage()    {}
func (*GameNotifyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{57}
}
func (m *GameNotifyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameNotifyInfo.Unmarshal(m, b)
}
func (m *GameNotifyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameNotifyInfo.Marshal(b, m, deterministic)
}
func (dst *GameNotifyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameNotifyInfo.Merge(dst, src)
}
func (m *GameNotifyInfo) XXX_Size() int {
	return xxx_messageInfo_GameNotifyInfo.Size(m)
}
func (m *GameNotifyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameNotifyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameNotifyInfo proto.InternalMessageInfo

func (m *GameNotifyInfo) GetGameType() NotifyGameType {
	if m != nil {
		return m.GameType
	}
	return NotifyGameType_NotifyGameType_UNSPECIFIED
}

func (m *GameNotifyInfo) GetNotifyList() []*NotifyInfo {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

func (m *GameNotifyInfo) GetVersionTime() uint64 {
	if m != nil {
		return m.VersionTime
	}
	return 0
}

type BatGetNotifyInfoReq struct {
	GameTypeList         []uint32 `protobuf:"varint,1,rep,packed,name=game_type_list,json=gameTypeList,proto3" json:"game_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetNotifyInfoReq) Reset()         { *m = BatGetNotifyInfoReq{} }
func (m *BatGetNotifyInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatGetNotifyInfoReq) ProtoMessage()    {}
func (*BatGetNotifyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{58}
}
func (m *BatGetNotifyInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetNotifyInfoReq.Unmarshal(m, b)
}
func (m *BatGetNotifyInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetNotifyInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatGetNotifyInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetNotifyInfoReq.Merge(dst, src)
}
func (m *BatGetNotifyInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatGetNotifyInfoReq.Size(m)
}
func (m *BatGetNotifyInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetNotifyInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetNotifyInfoReq proto.InternalMessageInfo

func (m *BatGetNotifyInfoReq) GetGameTypeList() []uint32 {
	if m != nil {
		return m.GameTypeList
	}
	return nil
}

type BatGetNotifyInfoResp struct {
	NotifyList           []*GameNotifyInfo `protobuf:"bytes,1,rep,name=notify_list,json=notifyList,proto3" json:"notify_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatGetNotifyInfoResp) Reset()         { *m = BatGetNotifyInfoResp{} }
func (m *BatGetNotifyInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatGetNotifyInfoResp) ProtoMessage()    {}
func (*BatGetNotifyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{59}
}
func (m *BatGetNotifyInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetNotifyInfoResp.Unmarshal(m, b)
}
func (m *BatGetNotifyInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetNotifyInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatGetNotifyInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetNotifyInfoResp.Merge(dst, src)
}
func (m *BatGetNotifyInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatGetNotifyInfoResp.Size(m)
}
func (m *BatGetNotifyInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetNotifyInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetNotifyInfoResp proto.InternalMessageInfo

func (m *BatGetNotifyInfoResp) GetNotifyList() []*GameNotifyInfo {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

type CheckMagicSpiritAccessReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CheckAll             bool     `protobuf:"varint,3,opt,name=check_all,json=checkAll,proto3" json:"check_all,omitempty"`
	MagicSpiritId        uint32   `protobuf:"varint,4,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckMagicSpiritAccessReq) Reset()         { *m = CheckMagicSpiritAccessReq{} }
func (m *CheckMagicSpiritAccessReq) String() string { return proto.CompactTextString(m) }
func (*CheckMagicSpiritAccessReq) ProtoMessage()    {}
func (*CheckMagicSpiritAccessReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{60}
}
func (m *CheckMagicSpiritAccessReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckMagicSpiritAccessReq.Unmarshal(m, b)
}
func (m *CheckMagicSpiritAccessReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckMagicSpiritAccessReq.Marshal(b, m, deterministic)
}
func (dst *CheckMagicSpiritAccessReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckMagicSpiritAccessReq.Merge(dst, src)
}
func (m *CheckMagicSpiritAccessReq) XXX_Size() int {
	return xxx_messageInfo_CheckMagicSpiritAccessReq.Size(m)
}
func (m *CheckMagicSpiritAccessReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckMagicSpiritAccessReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckMagicSpiritAccessReq proto.InternalMessageInfo

func (m *CheckMagicSpiritAccessReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckMagicSpiritAccessReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckMagicSpiritAccessReq) GetCheckAll() bool {
	if m != nil {
		return m.CheckAll
	}
	return false
}

func (m *CheckMagicSpiritAccessReq) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

type MagicSpiritAccess struct {
	MagicSpiritId        uint32             `protobuf:"varint,1,opt,name=magic_spirit_id,json=magicSpiritId,proto3" json:"magic_spirit_id,omitempty"`
	Access               bool               `protobuf:"varint,2,opt,name=access,proto3" json:"access,omitempty"`
	ConditionList        []*AccessCondition `protobuf:"bytes,3,rep,name=condition_list,json=conditionList,proto3" json:"condition_list,omitempty"`
	RelateType           uint32             `protobuf:"varint,4,opt,name=relate_type,json=relateType,proto3" json:"relate_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MagicSpiritAccess) Reset()         { *m = MagicSpiritAccess{} }
func (m *MagicSpiritAccess) String() string { return proto.CompactTextString(m) }
func (*MagicSpiritAccess) ProtoMessage()    {}
func (*MagicSpiritAccess) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{61}
}
func (m *MagicSpiritAccess) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MagicSpiritAccess.Unmarshal(m, b)
}
func (m *MagicSpiritAccess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MagicSpiritAccess.Marshal(b, m, deterministic)
}
func (dst *MagicSpiritAccess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MagicSpiritAccess.Merge(dst, src)
}
func (m *MagicSpiritAccess) XXX_Size() int {
	return xxx_messageInfo_MagicSpiritAccess.Size(m)
}
func (m *MagicSpiritAccess) XXX_DiscardUnknown() {
	xxx_messageInfo_MagicSpiritAccess.DiscardUnknown(m)
}

var xxx_messageInfo_MagicSpiritAccess proto.InternalMessageInfo

func (m *MagicSpiritAccess) GetMagicSpiritId() uint32 {
	if m != nil {
		return m.MagicSpiritId
	}
	return 0
}

func (m *MagicSpiritAccess) GetAccess() bool {
	if m != nil {
		return m.Access
	}
	return false
}

func (m *MagicSpiritAccess) GetConditionList() []*AccessCondition {
	if m != nil {
		return m.ConditionList
	}
	return nil
}

func (m *MagicSpiritAccess) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

type CheckMagicSpiritAccessResp struct {
	Switch               bool                 `protobuf:"varint,1,opt,name=switch,proto3" json:"switch,omitempty"`
	ConfList             []*MagicSpiritAccess `protobuf:"bytes,2,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CheckMagicSpiritAccessResp) Reset()         { *m = CheckMagicSpiritAccessResp{} }
func (m *CheckMagicSpiritAccessResp) String() string { return proto.CompactTextString(m) }
func (*CheckMagicSpiritAccessResp) ProtoMessage()    {}
func (*CheckMagicSpiritAccessResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{62}
}
func (m *CheckMagicSpiritAccessResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckMagicSpiritAccessResp.Unmarshal(m, b)
}
func (m *CheckMagicSpiritAccessResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckMagicSpiritAccessResp.Marshal(b, m, deterministic)
}
func (dst *CheckMagicSpiritAccessResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckMagicSpiritAccessResp.Merge(dst, src)
}
func (m *CheckMagicSpiritAccessResp) XXX_Size() int {
	return xxx_messageInfo_CheckMagicSpiritAccessResp.Size(m)
}
func (m *CheckMagicSpiritAccessResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckMagicSpiritAccessResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckMagicSpiritAccessResp proto.InternalMessageInfo

func (m *CheckMagicSpiritAccessResp) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

func (m *CheckMagicSpiritAccessResp) GetConfList() []*MagicSpiritAccess {
	if m != nil {
		return m.ConfList
	}
	return nil
}

// -- 6.38.0 -- 玩法开关定时开放
type SetGameSwitchOpenTimeReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	OpUser               string   `protobuf:"bytes,4,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameSwitchOpenTimeReq) Reset()         { *m = SetGameSwitchOpenTimeReq{} }
func (m *SetGameSwitchOpenTimeReq) String() string { return proto.CompactTextString(m) }
func (*SetGameSwitchOpenTimeReq) ProtoMessage()    {}
func (*SetGameSwitchOpenTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{63}
}
func (m *SetGameSwitchOpenTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameSwitchOpenTimeReq.Unmarshal(m, b)
}
func (m *SetGameSwitchOpenTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameSwitchOpenTimeReq.Marshal(b, m, deterministic)
}
func (dst *SetGameSwitchOpenTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameSwitchOpenTimeReq.Merge(dst, src)
}
func (m *SetGameSwitchOpenTimeReq) XXX_Size() int {
	return xxx_messageInfo_SetGameSwitchOpenTimeReq.Size(m)
}
func (m *SetGameSwitchOpenTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameSwitchOpenTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameSwitchOpenTimeReq proto.InternalMessageInfo

func (m *SetGameSwitchOpenTimeReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *SetGameSwitchOpenTimeReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SetGameSwitchOpenTimeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SetGameSwitchOpenTimeReq) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

type SetGameSwitchOpenTimeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameSwitchOpenTimeResp) Reset()         { *m = SetGameSwitchOpenTimeResp{} }
func (m *SetGameSwitchOpenTimeResp) String() string { return proto.CompactTextString(m) }
func (*SetGameSwitchOpenTimeResp) ProtoMessage()    {}
func (*SetGameSwitchOpenTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{64}
}
func (m *SetGameSwitchOpenTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameSwitchOpenTimeResp.Unmarshal(m, b)
}
func (m *SetGameSwitchOpenTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameSwitchOpenTimeResp.Marshal(b, m, deterministic)
}
func (dst *SetGameSwitchOpenTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameSwitchOpenTimeResp.Merge(dst, src)
}
func (m *SetGameSwitchOpenTimeResp) XXX_Size() int {
	return xxx_messageInfo_SetGameSwitchOpenTimeResp.Size(m)
}
func (m *SetGameSwitchOpenTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameSwitchOpenTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameSwitchOpenTimeResp proto.InternalMessageInfo

type GetGameSwitchOpenTimeReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameSwitchOpenTimeReq) Reset()         { *m = GetGameSwitchOpenTimeReq{} }
func (m *GetGameSwitchOpenTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetGameSwitchOpenTimeReq) ProtoMessage()    {}
func (*GetGameSwitchOpenTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{65}
}
func (m *GetGameSwitchOpenTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameSwitchOpenTimeReq.Unmarshal(m, b)
}
func (m *GetGameSwitchOpenTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameSwitchOpenTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetGameSwitchOpenTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameSwitchOpenTimeReq.Merge(dst, src)
}
func (m *GetGameSwitchOpenTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetGameSwitchOpenTimeReq.Size(m)
}
func (m *GetGameSwitchOpenTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameSwitchOpenTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameSwitchOpenTimeReq proto.InternalMessageInfo

func (m *GetGameSwitchOpenTimeReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type GetGameSwitchOpenTimeResp struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	SwitchStatus         bool     `protobuf:"varint,2,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	BeginTime            uint32   `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameSwitchOpenTimeResp) Reset()         { *m = GetGameSwitchOpenTimeResp{} }
func (m *GetGameSwitchOpenTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetGameSwitchOpenTimeResp) ProtoMessage()    {}
func (*GetGameSwitchOpenTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{66}
}
func (m *GetGameSwitchOpenTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameSwitchOpenTimeResp.Unmarshal(m, b)
}
func (m *GetGameSwitchOpenTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameSwitchOpenTimeResp.Marshal(b, m, deterministic)
}
func (dst *GetGameSwitchOpenTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameSwitchOpenTimeResp.Merge(dst, src)
}
func (m *GetGameSwitchOpenTimeResp) XXX_Size() int {
	return xxx_messageInfo_GetGameSwitchOpenTimeResp.Size(m)
}
func (m *GetGameSwitchOpenTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameSwitchOpenTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameSwitchOpenTimeResp proto.InternalMessageInfo

func (m *GetGameSwitchOpenTimeResp) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GetGameSwitchOpenTimeResp) GetSwitchStatus() bool {
	if m != nil {
		return m.SwitchStatus
	}
	return false
}

func (m *GetGameSwitchOpenTimeResp) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetGameSwitchOpenTimeResp) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type CheckChanceGameIsOpenReq struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckChanceGameIsOpenReq) Reset()         { *m = CheckChanceGameIsOpenReq{} }
func (m *CheckChanceGameIsOpenReq) String() string { return proto.CompactTextString(m) }
func (*CheckChanceGameIsOpenReq) ProtoMessage()    {}
func (*CheckChanceGameIsOpenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{67}
}
func (m *CheckChanceGameIsOpenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChanceGameIsOpenReq.Unmarshal(m, b)
}
func (m *CheckChanceGameIsOpenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChanceGameIsOpenReq.Marshal(b, m, deterministic)
}
func (dst *CheckChanceGameIsOpenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChanceGameIsOpenReq.Merge(dst, src)
}
func (m *CheckChanceGameIsOpenReq) XXX_Size() int {
	return xxx_messageInfo_CheckChanceGameIsOpenReq.Size(m)
}
func (m *CheckChanceGameIsOpenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChanceGameIsOpenReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChanceGameIsOpenReq proto.InternalMessageInfo

func (m *CheckChanceGameIsOpenReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type CheckChanceGameIsOpenResp struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	SwitchStatus         bool     `protobuf:"varint,2,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckChanceGameIsOpenResp) Reset()         { *m = CheckChanceGameIsOpenResp{} }
func (m *CheckChanceGameIsOpenResp) String() string { return proto.CompactTextString(m) }
func (*CheckChanceGameIsOpenResp) ProtoMessage()    {}
func (*CheckChanceGameIsOpenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_chance_game_entry_41b904ec1daceec7, []int{68}
}
func (m *CheckChanceGameIsOpenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChanceGameIsOpenResp.Unmarshal(m, b)
}
func (m *CheckChanceGameIsOpenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChanceGameIsOpenResp.Marshal(b, m, deterministic)
}
func (dst *CheckChanceGameIsOpenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChanceGameIsOpenResp.Merge(dst, src)
}
func (m *CheckChanceGameIsOpenResp) XXX_Size() int {
	return xxx_messageInfo_CheckChanceGameIsOpenResp.Size(m)
}
func (m *CheckChanceGameIsOpenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChanceGameIsOpenResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChanceGameIsOpenResp proto.InternalMessageInfo

func (m *CheckChanceGameIsOpenResp) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *CheckChanceGameIsOpenResp) GetSwitchStatus() bool {
	if m != nil {
		return m.SwitchStatus
	}
	return false
}

func init() {
	proto.RegisterType((*AddChannelBWListReq)(nil), "chance_game_entry.AddChannelBWListReq")
	proto.RegisterType((*AddChannelBWListResp)(nil), "chance_game_entry.AddChannelBWListResp")
	proto.RegisterType((*AddUserBWListReq)(nil), "chance_game_entry.AddUserBWListReq")
	proto.RegisterType((*AddUserBWListResp)(nil), "chance_game_entry.AddUserBWListResp")
	proto.RegisterType((*DelFromBWListReq)(nil), "chance_game_entry.DelFromBWListReq")
	proto.RegisterType((*DelFromBWListResp)(nil), "chance_game_entry.DelFromBWListResp")
	proto.RegisterType((*ChannelBWListInfo)(nil), "chance_game_entry.ChannelBWListInfo")
	proto.RegisterType((*UserBWListInfo)(nil), "chance_game_entry.UserBWListInfo")
	proto.RegisterType((*GetChannelBWListInfoReq)(nil), "chance_game_entry.GetChannelBWListInfoReq")
	proto.RegisterType((*GetChannelBWListInfoResp)(nil), "chance_game_entry.GetChannelBWListInfoResp")
	proto.RegisterType((*GetUserBWListInfoReq)(nil), "chance_game_entry.GetUserBWListInfoReq")
	proto.RegisterType((*GetUserBWListInfoResp)(nil), "chance_game_entry.GetUserBWListInfoResp")
	proto.RegisterType((*CheckIfAccessibleReq)(nil), "chance_game_entry.CheckIfAccessibleReq")
	proto.RegisterType((*CheckIfAccessibleResp)(nil), "chance_game_entry.CheckIfAccessibleResp")
	proto.RegisterType((*SetChanceGameSwitchReq)(nil), "chance_game_entry.SetChanceGameSwitchReq")
	proto.RegisterType((*SetChanceGameSwitchResp)(nil), "chance_game_entry.SetChanceGameSwitchResp")
	proto.RegisterType((*ChanceGameSwitchState)(nil), "chance_game_entry.ChanceGameSwitchState")
	proto.RegisterType((*GetAllChanceGameSwitchStateReq)(nil), "chance_game_entry.GetAllChanceGameSwitchStateReq")
	proto.RegisterType((*GetAllChanceGameSwitchStateResp)(nil), "chance_game_entry.GetAllChanceGameSwitchStateResp")
	proto.RegisterType((*ListItemConf)(nil), "chance_game_entry.ListItemConf")
	proto.RegisterType((*AddChannelBWListV2Req)(nil), "chance_game_entry.AddChannelBWListV2Req")
	proto.RegisterType((*AddChannelBWListV2Resp)(nil), "chance_game_entry.AddChannelBWListV2Resp")
	proto.RegisterType((*AddUserBWListV2Req)(nil), "chance_game_entry.AddUserBWListV2Req")
	proto.RegisterType((*AddUserBWListV2Resp)(nil), "chance_game_entry.AddUserBWListV2Resp")
	proto.RegisterType((*BatDelUserBWListV2Req)(nil), "chance_game_entry.BatDelUserBWListV2Req")
	proto.RegisterType((*BatDelUserBWListV2Resp)(nil), "chance_game_entry.BatDelUserBWListV2Resp")
	proto.RegisterType((*BatDelChannelBWListV2Req)(nil), "chance_game_entry.BatDelChannelBWListV2Req")
	proto.RegisterType((*BatDelChannelBWListV2Resp)(nil), "chance_game_entry.BatDelChannelBWListV2Resp")
	proto.RegisterType((*GetChannelBWListInfoV2Req)(nil), "chance_game_entry.GetChannelBWListInfoV2Req")
	proto.RegisterType((*GetChannelBWListInfoV2Resp)(nil), "chance_game_entry.GetChannelBWListInfoV2Resp")
	proto.RegisterType((*GetUserBWListInfoV2Req)(nil), "chance_game_entry.GetUserBWListInfoV2Req")
	proto.RegisterType((*GetUserBWListInfoV2Resp)(nil), "chance_game_entry.GetUserBWListInfoV2Resp")
	proto.RegisterType((*SubAccessCondition)(nil), "chance_game_entry.SubAccessCondition")
	proto.RegisterType((*AccessCondition)(nil), "chance_game_entry.AccessCondition")
	proto.RegisterType((*SetChanceGameAccessCondReq)(nil), "chance_game_entry.SetChanceGameAccessCondReq")
	proto.RegisterType((*SetChanceGameAccessCondResp)(nil), "chance_game_entry.SetChanceGameAccessCondResp")
	proto.RegisterType((*GetChanceGameAccessCondReq)(nil), "chance_game_entry.GetChanceGameAccessCondReq")
	proto.RegisterType((*GetChanceGameAccessCondResp)(nil), "chance_game_entry.GetChanceGameAccessCondResp")
	proto.RegisterType((*GetMagicSpiritAccessCondReq)(nil), "chance_game_entry.GetMagicSpiritAccessCondReq")
	proto.RegisterType((*MagicSpiritAccessCond)(nil), "chance_game_entry.MagicSpiritAccessCond")
	proto.RegisterType((*GetMagicSpiritAccessCondResp)(nil), "chance_game_entry.GetMagicSpiritAccessCondResp")
	proto.RegisterType((*DelMagicSpiritAccessCondReq)(nil), "chance_game_entry.DelMagicSpiritAccessCondReq")
	proto.RegisterType((*DelMagicSpiritAccessCondResp)(nil), "chance_game_entry.DelMagicSpiritAccessCondResp")
	proto.RegisterType((*CheckGameEntryAccessReq)(nil), "chance_game_entry.CheckGameEntryAccessReq")
	proto.RegisterType((*GameEntryAccess)(nil), "chance_game_entry.GameEntryAccess")
	proto.RegisterType((*CheckGameEntryAccessResp)(nil), "chance_game_entry.CheckGameEntryAccessResp")
	proto.RegisterType((*LocalCacheAccessConf)(nil), "chance_game_entry.LocalCacheAccessConf")
	proto.RegisterType((*GetLocalCacheConfReq)(nil), "chance_game_entry.GetLocalCacheConfReq")
	proto.RegisterType((*GetLocalCacheConfResp)(nil), "chance_game_entry.GetLocalCacheConfResp")
	proto.RegisterType((*GetUserValueReq)(nil), "chance_game_entry.GetUserValueReq")
	proto.RegisterType((*GetUserValueResp)(nil), "chance_game_entry.GetUserValueResp")
	proto.RegisterType((*NotifyItem)(nil), "chance_game_entry.NotifyItem")
	proto.RegisterType((*NotifyInfo)(nil), "chance_game_entry.NotifyInfo")
	proto.RegisterType((*SetGameNotifyInfoReq)(nil), "chance_game_entry.SetGameNotifyInfoReq")
	proto.RegisterType((*SetGameNotifyInfoResp)(nil), "chance_game_entry.SetGameNotifyInfoResp")
	proto.RegisterType((*GetGameNotifyInfoReq)(nil), "chance_game_entry.GetGameNotifyInfoReq")
	proto.RegisterType((*GetGameNotifyInfoResp)(nil), "chance_game_entry.GetGameNotifyInfoResp")
	proto.RegisterType((*GameNotifyInfo)(nil), "chance_game_entry.GameNotifyInfo")
	proto.RegisterType((*BatGetNotifyInfoReq)(nil), "chance_game_entry.BatGetNotifyInfoReq")
	proto.RegisterType((*BatGetNotifyInfoResp)(nil), "chance_game_entry.BatGetNotifyInfoResp")
	proto.RegisterType((*CheckMagicSpiritAccessReq)(nil), "chance_game_entry.CheckMagicSpiritAccessReq")
	proto.RegisterType((*MagicSpiritAccess)(nil), "chance_game_entry.MagicSpiritAccess")
	proto.RegisterType((*CheckMagicSpiritAccessResp)(nil), "chance_game_entry.CheckMagicSpiritAccessResp")
	proto.RegisterType((*SetGameSwitchOpenTimeReq)(nil), "chance_game_entry.SetGameSwitchOpenTimeReq")
	proto.RegisterType((*SetGameSwitchOpenTimeResp)(nil), "chance_game_entry.SetGameSwitchOpenTimeResp")
	proto.RegisterType((*GetGameSwitchOpenTimeReq)(nil), "chance_game_entry.GetGameSwitchOpenTimeReq")
	proto.RegisterType((*GetGameSwitchOpenTimeResp)(nil), "chance_game_entry.GetGameSwitchOpenTimeResp")
	proto.RegisterType((*CheckChanceGameIsOpenReq)(nil), "chance_game_entry.CheckChanceGameIsOpenReq")
	proto.RegisterType((*CheckChanceGameIsOpenResp)(nil), "chance_game_entry.CheckChanceGameIsOpenResp")
	proto.RegisterEnum("chance_game_entry.ChanceGameType", ChanceGameType_name, ChanceGameType_value)
	proto.RegisterEnum("chance_game_entry.ListType", ListType_name, ListType_value)
	proto.RegisterEnum("chance_game_entry.NewChanceGameType", NewChanceGameType_name, NewChanceGameType_value)
	proto.RegisterEnum("chance_game_entry.ChanceGameListType", ChanceGameListType_name, ChanceGameListType_value)
	proto.RegisterEnum("chance_game_entry.ConditionType", ConditionType_name, ConditionType_value)
	proto.RegisterEnum("chance_game_entry.ExemptCondType", ExemptCondType_name, ExemptCondType_value)
	proto.RegisterEnum("chance_game_entry.RelateType", RelateType_name, RelateType_value)
	proto.RegisterEnum("chance_game_entry.CondType", CondType_name, CondType_value)
	proto.RegisterEnum("chance_game_entry.NotifyGameType", NotifyGameType_name, NotifyGameType_value)
	proto.RegisterEnum("chance_game_entry.NotifyPlaceType", NotifyPlaceType_name, NotifyPlaceType_value)
	proto.RegisterEnum("chance_game_entry.NotifyType", NotifyType_name, NotifyType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChanceGameEntryClient is the client API for ChanceGameEntry service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChanceGameEntryClient interface {
	// --- 6.20.0 需求 ---
	// 玩法开关
	SetChanceGameSwitch(ctx context.Context, in *SetChanceGameSwitchReq, opts ...grpc.CallOption) (*SetChanceGameSwitchResp, error)
	GetAllChanceGameSwitchState(ctx context.Context, in *GetAllChanceGameSwitchStateReq, opts ...grpc.CallOption) (*GetAllChanceGameSwitchStateResp, error)
	// 玩法黑白名单
	AddChannelBWListV2(ctx context.Context, in *AddChannelBWListV2Req, opts ...grpc.CallOption) (*AddChannelBWListV2Resp, error)
	AddUserBWListV2(ctx context.Context, in *AddUserBWListV2Req, opts ...grpc.CallOption) (*AddUserBWListV2Resp, error)
	BatDelUserBWListV2(ctx context.Context, in *BatDelUserBWListV2Req, opts ...grpc.CallOption) (*BatDelUserBWListV2Resp, error)
	BatDelChannelBWListV2(ctx context.Context, in *BatDelChannelBWListV2Req, opts ...grpc.CallOption) (*BatDelChannelBWListV2Resp, error)
	GetChannelBWListInfoV2(ctx context.Context, in *GetChannelBWListInfoV2Req, opts ...grpc.CallOption) (*GetChannelBWListInfoV2Resp, error)
	GetUserBWListInfoV2(ctx context.Context, in *GetUserBWListInfoV2Req, opts ...grpc.CallOption) (*GetUserBWListInfoV2Resp, error)
	// 可见人群配置
	SetChanceGameAccessCond(ctx context.Context, in *SetChanceGameAccessCondReq, opts ...grpc.CallOption) (*SetChanceGameAccessCondResp, error)
	GetChanceGameAccessCond(ctx context.Context, in *GetChanceGameAccessCondReq, opts ...grpc.CallOption) (*GetChanceGameAccessCondResp, error)
	GetMagicSpiritAccessCond(ctx context.Context, in *GetMagicSpiritAccessCondReq, opts ...grpc.CallOption) (*GetMagicSpiritAccessCondResp, error)
	DelMagicSpiritAccessCond(ctx context.Context, in *DelMagicSpiritAccessCondReq, opts ...grpc.CallOption) (*DelMagicSpiritAccessCondResp, error)
	CheckGameEntryAccess(ctx context.Context, in *CheckGameEntryAccessReq, opts ...grpc.CallOption) (*CheckGameEntryAccessResp, error)
	CheckMagicSpiritAccess(ctx context.Context, in *CheckMagicSpiritAccessReq, opts ...grpc.CallOption) (*CheckMagicSpiritAccessResp, error)
	// 从内存缓存中获取配置
	GetLocalCacheConf(ctx context.Context, in *GetLocalCacheConfReq, opts ...grpc.CallOption) (*GetLocalCacheConfResp, error)
	// 获取用户各条件数值
	GetUserValue(ctx context.Context, in *GetUserValueReq, opts ...grpc.CallOption) (*GetUserValueResp, error)
	// 提醒浮层通用接口
	SetGameNotifyInfo(ctx context.Context, in *SetGameNotifyInfoReq, opts ...grpc.CallOption) (*SetGameNotifyInfoResp, error)
	GetGameNotifyInfo(ctx context.Context, in *GetGameNotifyInfoReq, opts ...grpc.CallOption) (*GetGameNotifyInfoResp, error)
	BatGetNotifyInfo(ctx context.Context, in *BatGetNotifyInfoReq, opts ...grpc.CallOption) (*BatGetNotifyInfoResp, error)
	// 玩法接口定时开放
	SetChanceGameOpenTime(ctx context.Context, in *SetGameSwitchOpenTimeReq, opts ...grpc.CallOption) (*SetGameSwitchOpenTimeResp, error)
	GetChanceGameOpenTime(ctx context.Context, in *GetGameSwitchOpenTimeReq, opts ...grpc.CallOption) (*GetGameSwitchOpenTimeResp, error)
	CheckChanceGameIsOpen(ctx context.Context, in *CheckChanceGameIsOpenReq, opts ...grpc.CallOption) (*CheckChanceGameIsOpenResp, error)
}

type chanceGameEntryClient struct {
	cc *grpc.ClientConn
}

func NewChanceGameEntryClient(cc *grpc.ClientConn) ChanceGameEntryClient {
	return &chanceGameEntryClient{cc}
}

func (c *chanceGameEntryClient) SetChanceGameSwitch(ctx context.Context, in *SetChanceGameSwitchReq, opts ...grpc.CallOption) (*SetChanceGameSwitchResp, error) {
	out := new(SetChanceGameSwitchResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/SetChanceGameSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) GetAllChanceGameSwitchState(ctx context.Context, in *GetAllChanceGameSwitchStateReq, opts ...grpc.CallOption) (*GetAllChanceGameSwitchStateResp, error) {
	out := new(GetAllChanceGameSwitchStateResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/GetAllChanceGameSwitchState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) AddChannelBWListV2(ctx context.Context, in *AddChannelBWListV2Req, opts ...grpc.CallOption) (*AddChannelBWListV2Resp, error) {
	out := new(AddChannelBWListV2Resp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/AddChannelBWListV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) AddUserBWListV2(ctx context.Context, in *AddUserBWListV2Req, opts ...grpc.CallOption) (*AddUserBWListV2Resp, error) {
	out := new(AddUserBWListV2Resp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/AddUserBWListV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) BatDelUserBWListV2(ctx context.Context, in *BatDelUserBWListV2Req, opts ...grpc.CallOption) (*BatDelUserBWListV2Resp, error) {
	out := new(BatDelUserBWListV2Resp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/BatDelUserBWListV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) BatDelChannelBWListV2(ctx context.Context, in *BatDelChannelBWListV2Req, opts ...grpc.CallOption) (*BatDelChannelBWListV2Resp, error) {
	out := new(BatDelChannelBWListV2Resp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/BatDelChannelBWListV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) GetChannelBWListInfoV2(ctx context.Context, in *GetChannelBWListInfoV2Req, opts ...grpc.CallOption) (*GetChannelBWListInfoV2Resp, error) {
	out := new(GetChannelBWListInfoV2Resp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/GetChannelBWListInfoV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) GetUserBWListInfoV2(ctx context.Context, in *GetUserBWListInfoV2Req, opts ...grpc.CallOption) (*GetUserBWListInfoV2Resp, error) {
	out := new(GetUserBWListInfoV2Resp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/GetUserBWListInfoV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) SetChanceGameAccessCond(ctx context.Context, in *SetChanceGameAccessCondReq, opts ...grpc.CallOption) (*SetChanceGameAccessCondResp, error) {
	out := new(SetChanceGameAccessCondResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/SetChanceGameAccessCond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) GetChanceGameAccessCond(ctx context.Context, in *GetChanceGameAccessCondReq, opts ...grpc.CallOption) (*GetChanceGameAccessCondResp, error) {
	out := new(GetChanceGameAccessCondResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/GetChanceGameAccessCond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) GetMagicSpiritAccessCond(ctx context.Context, in *GetMagicSpiritAccessCondReq, opts ...grpc.CallOption) (*GetMagicSpiritAccessCondResp, error) {
	out := new(GetMagicSpiritAccessCondResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/GetMagicSpiritAccessCond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) DelMagicSpiritAccessCond(ctx context.Context, in *DelMagicSpiritAccessCondReq, opts ...grpc.CallOption) (*DelMagicSpiritAccessCondResp, error) {
	out := new(DelMagicSpiritAccessCondResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/DelMagicSpiritAccessCond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) CheckGameEntryAccess(ctx context.Context, in *CheckGameEntryAccessReq, opts ...grpc.CallOption) (*CheckGameEntryAccessResp, error) {
	out := new(CheckGameEntryAccessResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/CheckGameEntryAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) CheckMagicSpiritAccess(ctx context.Context, in *CheckMagicSpiritAccessReq, opts ...grpc.CallOption) (*CheckMagicSpiritAccessResp, error) {
	out := new(CheckMagicSpiritAccessResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/CheckMagicSpiritAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) GetLocalCacheConf(ctx context.Context, in *GetLocalCacheConfReq, opts ...grpc.CallOption) (*GetLocalCacheConfResp, error) {
	out := new(GetLocalCacheConfResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/GetLocalCacheConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) GetUserValue(ctx context.Context, in *GetUserValueReq, opts ...grpc.CallOption) (*GetUserValueResp, error) {
	out := new(GetUserValueResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/GetUserValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) SetGameNotifyInfo(ctx context.Context, in *SetGameNotifyInfoReq, opts ...grpc.CallOption) (*SetGameNotifyInfoResp, error) {
	out := new(SetGameNotifyInfoResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/SetGameNotifyInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) GetGameNotifyInfo(ctx context.Context, in *GetGameNotifyInfoReq, opts ...grpc.CallOption) (*GetGameNotifyInfoResp, error) {
	out := new(GetGameNotifyInfoResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/GetGameNotifyInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) BatGetNotifyInfo(ctx context.Context, in *BatGetNotifyInfoReq, opts ...grpc.CallOption) (*BatGetNotifyInfoResp, error) {
	out := new(BatGetNotifyInfoResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/BatGetNotifyInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) SetChanceGameOpenTime(ctx context.Context, in *SetGameSwitchOpenTimeReq, opts ...grpc.CallOption) (*SetGameSwitchOpenTimeResp, error) {
	out := new(SetGameSwitchOpenTimeResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/SetChanceGameOpenTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) GetChanceGameOpenTime(ctx context.Context, in *GetGameSwitchOpenTimeReq, opts ...grpc.CallOption) (*GetGameSwitchOpenTimeResp, error) {
	out := new(GetGameSwitchOpenTimeResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/GetChanceGameOpenTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chanceGameEntryClient) CheckChanceGameIsOpen(ctx context.Context, in *CheckChanceGameIsOpenReq, opts ...grpc.CallOption) (*CheckChanceGameIsOpenResp, error) {
	out := new(CheckChanceGameIsOpenResp)
	err := c.cc.Invoke(ctx, "/chance_game_entry.ChanceGameEntry/CheckChanceGameIsOpen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChanceGameEntryServer is the server API for ChanceGameEntry service.
type ChanceGameEntryServer interface {
	// --- 6.20.0 需求 ---
	// 玩法开关
	SetChanceGameSwitch(context.Context, *SetChanceGameSwitchReq) (*SetChanceGameSwitchResp, error)
	GetAllChanceGameSwitchState(context.Context, *GetAllChanceGameSwitchStateReq) (*GetAllChanceGameSwitchStateResp, error)
	// 玩法黑白名单
	AddChannelBWListV2(context.Context, *AddChannelBWListV2Req) (*AddChannelBWListV2Resp, error)
	AddUserBWListV2(context.Context, *AddUserBWListV2Req) (*AddUserBWListV2Resp, error)
	BatDelUserBWListV2(context.Context, *BatDelUserBWListV2Req) (*BatDelUserBWListV2Resp, error)
	BatDelChannelBWListV2(context.Context, *BatDelChannelBWListV2Req) (*BatDelChannelBWListV2Resp, error)
	GetChannelBWListInfoV2(context.Context, *GetChannelBWListInfoV2Req) (*GetChannelBWListInfoV2Resp, error)
	GetUserBWListInfoV2(context.Context, *GetUserBWListInfoV2Req) (*GetUserBWListInfoV2Resp, error)
	// 可见人群配置
	SetChanceGameAccessCond(context.Context, *SetChanceGameAccessCondReq) (*SetChanceGameAccessCondResp, error)
	GetChanceGameAccessCond(context.Context, *GetChanceGameAccessCondReq) (*GetChanceGameAccessCondResp, error)
	GetMagicSpiritAccessCond(context.Context, *GetMagicSpiritAccessCondReq) (*GetMagicSpiritAccessCondResp, error)
	DelMagicSpiritAccessCond(context.Context, *DelMagicSpiritAccessCondReq) (*DelMagicSpiritAccessCondResp, error)
	CheckGameEntryAccess(context.Context, *CheckGameEntryAccessReq) (*CheckGameEntryAccessResp, error)
	CheckMagicSpiritAccess(context.Context, *CheckMagicSpiritAccessReq) (*CheckMagicSpiritAccessResp, error)
	// 从内存缓存中获取配置
	GetLocalCacheConf(context.Context, *GetLocalCacheConfReq) (*GetLocalCacheConfResp, error)
	// 获取用户各条件数值
	GetUserValue(context.Context, *GetUserValueReq) (*GetUserValueResp, error)
	// 提醒浮层通用接口
	SetGameNotifyInfo(context.Context, *SetGameNotifyInfoReq) (*SetGameNotifyInfoResp, error)
	GetGameNotifyInfo(context.Context, *GetGameNotifyInfoReq) (*GetGameNotifyInfoResp, error)
	BatGetNotifyInfo(context.Context, *BatGetNotifyInfoReq) (*BatGetNotifyInfoResp, error)
	// 玩法接口定时开放
	SetChanceGameOpenTime(context.Context, *SetGameSwitchOpenTimeReq) (*SetGameSwitchOpenTimeResp, error)
	GetChanceGameOpenTime(context.Context, *GetGameSwitchOpenTimeReq) (*GetGameSwitchOpenTimeResp, error)
	CheckChanceGameIsOpen(context.Context, *CheckChanceGameIsOpenReq) (*CheckChanceGameIsOpenResp, error)
}

func RegisterChanceGameEntryServer(s *grpc.Server, srv ChanceGameEntryServer) {
	s.RegisterService(&_ChanceGameEntry_serviceDesc, srv)
}

func _ChanceGameEntry_SetChanceGameSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChanceGameSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).SetChanceGameSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/SetChanceGameSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).SetChanceGameSwitch(ctx, req.(*SetChanceGameSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_GetAllChanceGameSwitchState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllChanceGameSwitchStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).GetAllChanceGameSwitchState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/GetAllChanceGameSwitchState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).GetAllChanceGameSwitchState(ctx, req.(*GetAllChanceGameSwitchStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_AddChannelBWListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelBWListV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).AddChannelBWListV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/AddChannelBWListV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).AddChannelBWListV2(ctx, req.(*AddChannelBWListV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_AddUserBWListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserBWListV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).AddUserBWListV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/AddUserBWListV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).AddUserBWListV2(ctx, req.(*AddUserBWListV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_BatDelUserBWListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatDelUserBWListV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).BatDelUserBWListV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/BatDelUserBWListV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).BatDelUserBWListV2(ctx, req.(*BatDelUserBWListV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_BatDelChannelBWListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatDelChannelBWListV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).BatDelChannelBWListV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/BatDelChannelBWListV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).BatDelChannelBWListV2(ctx, req.(*BatDelChannelBWListV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_GetChannelBWListInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelBWListInfoV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).GetChannelBWListInfoV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/GetChannelBWListInfoV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).GetChannelBWListInfoV2(ctx, req.(*GetChannelBWListInfoV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_GetUserBWListInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBWListInfoV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).GetUserBWListInfoV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/GetUserBWListInfoV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).GetUserBWListInfoV2(ctx, req.(*GetUserBWListInfoV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_SetChanceGameAccessCond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChanceGameAccessCondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).SetChanceGameAccessCond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/SetChanceGameAccessCond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).SetChanceGameAccessCond(ctx, req.(*SetChanceGameAccessCondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_GetChanceGameAccessCond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChanceGameAccessCondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).GetChanceGameAccessCond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/GetChanceGameAccessCond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).GetChanceGameAccessCond(ctx, req.(*GetChanceGameAccessCondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_GetMagicSpiritAccessCond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMagicSpiritAccessCondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).GetMagicSpiritAccessCond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/GetMagicSpiritAccessCond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).GetMagicSpiritAccessCond(ctx, req.(*GetMagicSpiritAccessCondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_DelMagicSpiritAccessCond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelMagicSpiritAccessCondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).DelMagicSpiritAccessCond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/DelMagicSpiritAccessCond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).DelMagicSpiritAccessCond(ctx, req.(*DelMagicSpiritAccessCondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_CheckGameEntryAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckGameEntryAccessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).CheckGameEntryAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/CheckGameEntryAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).CheckGameEntryAccess(ctx, req.(*CheckGameEntryAccessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_CheckMagicSpiritAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMagicSpiritAccessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).CheckMagicSpiritAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/CheckMagicSpiritAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).CheckMagicSpiritAccess(ctx, req.(*CheckMagicSpiritAccessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_GetLocalCacheConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLocalCacheConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).GetLocalCacheConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/GetLocalCacheConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).GetLocalCacheConf(ctx, req.(*GetLocalCacheConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_GetUserValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).GetUserValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/GetUserValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).GetUserValue(ctx, req.(*GetUserValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_SetGameNotifyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGameNotifyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).SetGameNotifyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/SetGameNotifyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).SetGameNotifyInfo(ctx, req.(*SetGameNotifyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_GetGameNotifyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameNotifyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).GetGameNotifyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/GetGameNotifyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).GetGameNotifyInfo(ctx, req.(*GetGameNotifyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_BatGetNotifyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetNotifyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).BatGetNotifyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/BatGetNotifyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).BatGetNotifyInfo(ctx, req.(*BatGetNotifyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_SetChanceGameOpenTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGameSwitchOpenTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).SetChanceGameOpenTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/SetChanceGameOpenTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).SetChanceGameOpenTime(ctx, req.(*SetGameSwitchOpenTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_GetChanceGameOpenTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameSwitchOpenTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).GetChanceGameOpenTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/GetChanceGameOpenTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).GetChanceGameOpenTime(ctx, req.(*GetGameSwitchOpenTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChanceGameEntry_CheckChanceGameIsOpen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckChanceGameIsOpenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChanceGameEntryServer).CheckChanceGameIsOpen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/chance_game_entry.ChanceGameEntry/CheckChanceGameIsOpen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChanceGameEntryServer).CheckChanceGameIsOpen(ctx, req.(*CheckChanceGameIsOpenReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChanceGameEntry_serviceDesc = grpc.ServiceDesc{
	ServiceName: "chance_game_entry.ChanceGameEntry",
	HandlerType: (*ChanceGameEntryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetChanceGameSwitch",
			Handler:    _ChanceGameEntry_SetChanceGameSwitch_Handler,
		},
		{
			MethodName: "GetAllChanceGameSwitchState",
			Handler:    _ChanceGameEntry_GetAllChanceGameSwitchState_Handler,
		},
		{
			MethodName: "AddChannelBWListV2",
			Handler:    _ChanceGameEntry_AddChannelBWListV2_Handler,
		},
		{
			MethodName: "AddUserBWListV2",
			Handler:    _ChanceGameEntry_AddUserBWListV2_Handler,
		},
		{
			MethodName: "BatDelUserBWListV2",
			Handler:    _ChanceGameEntry_BatDelUserBWListV2_Handler,
		},
		{
			MethodName: "BatDelChannelBWListV2",
			Handler:    _ChanceGameEntry_BatDelChannelBWListV2_Handler,
		},
		{
			MethodName: "GetChannelBWListInfoV2",
			Handler:    _ChanceGameEntry_GetChannelBWListInfoV2_Handler,
		},
		{
			MethodName: "GetUserBWListInfoV2",
			Handler:    _ChanceGameEntry_GetUserBWListInfoV2_Handler,
		},
		{
			MethodName: "SetChanceGameAccessCond",
			Handler:    _ChanceGameEntry_SetChanceGameAccessCond_Handler,
		},
		{
			MethodName: "GetChanceGameAccessCond",
			Handler:    _ChanceGameEntry_GetChanceGameAccessCond_Handler,
		},
		{
			MethodName: "GetMagicSpiritAccessCond",
			Handler:    _ChanceGameEntry_GetMagicSpiritAccessCond_Handler,
		},
		{
			MethodName: "DelMagicSpiritAccessCond",
			Handler:    _ChanceGameEntry_DelMagicSpiritAccessCond_Handler,
		},
		{
			MethodName: "CheckGameEntryAccess",
			Handler:    _ChanceGameEntry_CheckGameEntryAccess_Handler,
		},
		{
			MethodName: "CheckMagicSpiritAccess",
			Handler:    _ChanceGameEntry_CheckMagicSpiritAccess_Handler,
		},
		{
			MethodName: "GetLocalCacheConf",
			Handler:    _ChanceGameEntry_GetLocalCacheConf_Handler,
		},
		{
			MethodName: "GetUserValue",
			Handler:    _ChanceGameEntry_GetUserValue_Handler,
		},
		{
			MethodName: "SetGameNotifyInfo",
			Handler:    _ChanceGameEntry_SetGameNotifyInfo_Handler,
		},
		{
			MethodName: "GetGameNotifyInfo",
			Handler:    _ChanceGameEntry_GetGameNotifyInfo_Handler,
		},
		{
			MethodName: "BatGetNotifyInfo",
			Handler:    _ChanceGameEntry_BatGetNotifyInfo_Handler,
		},
		{
			MethodName: "SetChanceGameOpenTime",
			Handler:    _ChanceGameEntry_SetChanceGameOpenTime_Handler,
		},
		{
			MethodName: "GetChanceGameOpenTime",
			Handler:    _ChanceGameEntry_GetChanceGameOpenTime_Handler,
		},
		{
			MethodName: "CheckChanceGameIsOpen",
			Handler:    _ChanceGameEntry_CheckChanceGameIsOpen_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/chance-game-entry/chance-game-entry.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/chance-game-entry/chance-game-entry.proto", fileDescriptor_chance_game_entry_41b904ec1daceec7)
}

var fileDescriptor_chance_game_entry_41b904ec1daceec7 = []byte{
	// 3052 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x1a, 0x4d, 0x6f, 0xe3, 0xc6,
	0xd5, 0x94, 0xfc, 0x21, 0x3d, 0xdb, 0x32, 0x3d, 0xfe, 0x92, 0xe5, 0xfd, 0xe4, 0x6e, 0x76, 0x37,
	0x4a, 0x76, 0x17, 0x71, 0x10, 0xa4, 0x68, 0x83, 0xb4, 0xb2, 0x56, 0x51, 0xd4, 0x7a, 0x65, 0x57,
	0xf6, 0xee, 0xe6, 0x03, 0x85, 0x40, 0x93, 0x23, 0x89, 0x30, 0x45, 0x72, 0xc9, 0x91, 0xbd, 0x4e,
	0x11, 0xa0, 0x45, 0x91, 0x02, 0xed, 0xad, 0x87, 0xb6, 0xa7, 0xb6, 0xb9, 0xf4, 0xda, 0x4b, 0x8f,
	0x3d, 0xb4, 0x97, 0x5e, 0xda, 0xdf, 0x10, 0xf4, 0x07, 0xf4, 0x4f, 0x14, 0x33, 0xfc, 0x26, 0x87,
	0x92, 0x1c, 0xb8, 0x7b, 0xd3, 0xbc, 0x79, 0x9c, 0xf7, 0xfd, 0xe6, 0xcd, 0x7b, 0x82, 0xef, 0x10,
	0xf2, 0xf8, 0xe5, 0x48, 0x53, 0x4e, 0x1d, 0x4d, 0x3f, 0xc3, 0xf6, 0x63, 0x65, 0x20, 0x1b, 0x0a,
	0x7e, 0xd8, 0x97, 0x87, 0xf8, 0x21, 0x36, 0x88, 0x7d, 0x91, 0x86, 0x3c, 0xb2, 0x6c, 0x93, 0x98,
	0x68, 0xd5, 0xdd, 0xe8, 0xd2, 0x8d, 0x2e, 0xdb, 0x90, 0x7e, 0x26, 0xc0, 0x5a, 0x4d, 0x55, 0xeb,
	0x03, 0xd9, 0x30, 0xb0, 0xbe, 0xf7, 0x62, 0x5f, 0x73, 0x48, 0x07, 0xbf, 0x44, 0xf7, 0x60, 0x45,
	0xd5, 0x1c, 0x4b, 0x97, 0x2f, 0xba, 0x9a, 0xda, 0xd5, 0x35, 0x87, 0x94, 0x85, 0x5b, 0xf9, 0x07,
	0xcb, 0x9d, 0x65, 0x0f, 0xdc, 0x52, 0x29, 0x2a, 0xba, 0x0b, 0xa5, 0x13, 0x5d, 0x56, 0x4e, 0xbb,
	0xa6, 0xdd, 0x3d, 0x1f, 0x68, 0x04, 0x97, 0x73, 0xb7, 0x84, 0x07, 0xcb, 0x9d, 0x25, 0x06, 0x3d,
	0xb0, 0x5f, 0x50, 0x18, 0xda, 0x81, 0x62, 0x93, 0xd2, 0x24, 0x17, 0x16, 0x2e, 0xe7, 0x19, 0x42,
	0x81, 0x02, 0x8e, 0x2f, 0x2c, 0x2c, 0x6d, 0xc2, 0x7a, 0x9a, 0x03, 0xc7, 0x92, 0x2c, 0x10, 0x6b,
	0xaa, 0xfa, 0xcc, 0xc1, 0x76, 0xc8, 0xd6, 0x36, 0x14, 0x46, 0x71, 0x7e, 0x16, 0x46, 0xda, 0x95,
	0x71, 0xb2, 0x06, 0xab, 0x09, 0x8a, 0x8e, 0x25, 0xfd, 0x43, 0x00, 0xf1, 0x09, 0xd6, 0x3f, 0xb2,
	0xcd, 0xe1, 0x54, 0x7c, 0x70, 0x34, 0x97, 0x9b, 0x4e, 0x73, 0x79, 0x0e, 0xbf, 0xf7, 0x60, 0x45,
	0x71, 0x35, 0x43, 0xf1, 0x46, 0x0e, 0xb6, 0xcb, 0xb3, 0x0c, 0x6d, 0xd9, 0x03, 0x1f, 0xd8, 0x94,
	0xdf, 0xb8, 0x5c, 0x73, 0x69, 0xb9, 0x12, 0x12, 0x38, 0x96, 0xf4, 0x4f, 0x01, 0x56, 0x63, 0x4a,
	0x6f, 0x19, 0x3d, 0x13, 0x5d, 0x07, 0x08, 0xb9, 0x2f, 0x0b, 0xec, 0xa0, 0x62, 0xc0, 0x38, 0xdd,
	0xf6, 0xd9, 0xd1, 0x54, 0x4f, 0xc1, 0x45, 0x0f, 0xd2, 0x52, 0xd1, 0x6d, 0x58, 0xf2, 0xb7, 0x0d,
	0x79, 0xe8, 0x4a, 0x54, 0xec, 0x2c, 0x7a, 0xb0, 0xb6, 0x3c, 0xc4, 0xf4, 0x04, 0xf3, 0xdc, 0xc0,
	0x76, 0x97, 0x10, 0x4d, 0x65, 0xb2, 0x14, 0x3b, 0x45, 0x06, 0x39, 0x26, 0x9a, 0x8a, 0x2a, 0x50,
	0x30, 0x34, 0xe5, 0x94, 0x7d, 0x3d, 0xc7, 0x36, 0x83, 0x35, 0x5a, 0x87, 0x39, 0x85, 0x68, 0x43,
	0x5c, 0x9e, 0x67, 0x74, 0xdd, 0x85, 0x34, 0x80, 0x52, 0x68, 0x31, 0x26, 0x83, 0x08, 0xf9, 0x51,
	0xc0, 0x3c, 0xfd, 0x89, 0x10, 0xcc, 0x32, 0x72, 0x39, 0x76, 0x22, 0xfb, 0x1d, 0xa3, 0x94, 0xcf,
	0xa2, 0x34, 0x1b, 0xa5, 0xf4, 0x67, 0x01, 0xb6, 0x9a, 0x98, 0xa4, 0x94, 0x46, 0x1d, 0x22, 0x6d,
	0x4d, 0x61, 0x92, 0xf7, 0xe5, 0xe2, 0x56, 0xa2, 0x4c, 0x5a, 0x72, 0xdf, 0x77, 0x03, 0xf6, 0x9b,
	0x31, 0x62, 0x8e, 0x0c, 0x12, 0x30, 0x42, 0x17, 0x09, 0x23, 0xcd, 0x25, 0x8c, 0x24, 0xfd, 0x42,
	0x80, 0x32, 0x9f, 0x4f, 0xc7, 0x42, 0x1f, 0xc3, 0xb2, 0x72, 0x72, 0xde, 0xd5, 0x8c, 0x9e, 0x19,
	0xba, 0xef, 0xe2, 0xee, 0xdd, 0x47, 0xa9, 0xdc, 0xf0, 0x28, 0x7d, 0xc0, 0xa2, 0x72, 0x72, 0x4e,
	0x7f, 0x30, 0x07, 0xbe, 0x0e, 0x40, 0x4c, 0x22, 0xeb, 0x5d, 0xc6, 0xb5, 0xe7, 0x0b, 0x0c, 0x72,
	0x28, 0xf7, 0xb1, 0xf4, 0x5b, 0x01, 0xd6, 0x9b, 0x98, 0xc4, 0x6d, 0xf3, 0xda, 0x55, 0xe5, 0xf9,
	0xc2, 0x5c, 0xe0, 0x0b, 0xd2, 0x97, 0xb0, 0xc1, 0x61, 0xcb, 0xb1, 0x50, 0x03, 0x96, 0x47, 0x1c,
	0xcd, 0xdc, 0xe6, 0x68, 0x26, 0xf1, 0xf5, 0xe2, 0x68, 0x7a, 0xb5, 0xfc, 0x41, 0x80, 0xf5, 0xfa,
	0x00, 0x2b, 0xa7, 0xad, 0x5e, 0x4d, 0x51, 0xb0, 0xe3, 0x68, 0x27, 0x3a, 0xa6, 0x6a, 0x49, 0x7b,
	0xed, 0x84, 0x60, 0xab, 0x40, 0xa1, 0xef, 0x29, 0xc4, 0xcf, 0x64, 0xfe, 0x9a, 0x6a, 0x4f, 0xd1,
	0x35, 0x6c, 0x90, 0xae, 0x66, 0x79, 0x0a, 0x29, 0xb8, 0x80, 0x96, 0x45, 0x37, 0x87, 0xb2, 0x7d,
	0x8a, 0x49, 0xe8, 0x3d, 0x05, 0x17, 0xd0, 0x52, 0xa5, 0x4f, 0x60, 0x83, 0xc3, 0x9e, 0x63, 0xa1,
	0x2d, 0x58, 0x50, 0x64, 0xa3, 0xeb, 0x60, 0xd7, 0x5e, 0x85, 0xce, 0xbc, 0x22, 0x1b, 0x47, 0x98,
	0xa5, 0x28, 0x87, 0xc8, 0x76, 0x97, 0xd8, 0xf8, 0xd4, 0xd5, 0x0f, 0xe3, 0xb5, 0xd0, 0x59, 0xa6,
	0xe0, 0x63, 0x1b, 0x9f, 0x36, 0xd8, 0x55, 0xf3, 0x63, 0xd8, 0x3c, 0x72, 0xbd, 0x52, 0xc1, 0xd4,
	0x92, 0x47, 0xe7, 0x1a, 0x51, 0x06, 0x54, 0xf4, 0x1d, 0x28, 0xf6, 0x03, 0x5b, 0x0b, 0x09, 0x51,
	0x2a, 0x50, 0x34, 0x0d, 0xea, 0x2b, 0x66, 0xaf, 0xe7, 0x1d, 0xbc, 0x60, 0x1a, 0x07, 0xf6, 0x41,
	0xaf, 0x27, 0x6d, 0xc3, 0x16, 0xf7, 0x48, 0xc7, 0x92, 0x0e, 0xa9, 0x1c, 0x71, 0xf8, 0x11, 0x91,
	0x5d, 0xc7, 0xfa, 0x76, 0xc4, 0x6e, 0xc1, 0x8d, 0x26, 0x26, 0x35, 0x5d, 0xe7, 0x9e, 0xdb, 0xc1,
	0x2f, 0x25, 0x1d, 0x6e, 0x8e, 0xc5, 0x70, 0x2c, 0xd4, 0x82, 0x45, 0x87, 0x81, 0xa2, 0x2e, 0xf6,
	0x20, 0x23, 0xf8, 0xd2, 0x47, 0x80, 0xfb, 0x31, 0x75, 0x34, 0xe9, 0x33, 0x58, 0x62, 0x1e, 0x48,
	0xf0, 0xb0, 0x6e, 0x1a, 0x3d, 0x6a, 0x20, 0x7a, 0x66, 0x98, 0xb7, 0xe7, 0xe9, 0xb2, 0xa5, 0xc6,
	0x25, 0x76, 0xef, 0xa2, 0x50, 0xe2, 0x20, 0xd5, 0xe5, 0xa3, 0xa9, 0xee, 0x15, 0x6c, 0x24, 0xef,
	0xe4, 0xe7, 0xbb, 0xd4, 0x54, 0x7b, 0x61, 0x86, 0x8f, 0x08, 0x70, 0x93, 0x23, 0x40, 0x94, 0xb7,
	0xe0, 0x0a, 0x60, 0x11, 0xb2, 0x03, 0x45, 0xc6, 0x68, 0x34, 0xb4, 0x29, 0x80, 0xdd, 0x55, 0x65,
	0xd8, 0xe4, 0x51, 0x76, 0x2c, 0xc9, 0x04, 0x14, 0xbb, 0x9d, 0x5d, 0x86, 0x3e, 0x80, 0x22, 0xbd,
	0x15, 0x2f, 0xc5, 0x4d, 0x81, 0x7e, 0x31, 0x99, 0x95, 0x0d, 0x56, 0x1a, 0xc5, 0x09, 0x3a, 0x96,
	0x74, 0x00, 0x1b, 0x7b, 0x32, 0x79, 0x82, 0xf5, 0x24, 0x2b, 0x63, 0x8a, 0x82, 0x49, 0x22, 0xf3,
	0x0e, 0x74, 0x2c, 0x69, 0x0f, 0xca, 0xee, 0x0e, 0xc7, 0x12, 0x91, 0xca, 0x20, 0x51, 0xa1, 0x05,
	0x29, 0x82, 0xb9, 0xc9, 0x0e, 0x6c, 0x67, 0x9c, 0xe1, 0x58, 0xd2, 0xcf, 0x05, 0xd8, 0xe6, 0x5d,
	0x15, 0x2e, 0x89, 0x18, 0xd7, 0x42, 0x9c, 0xeb, 0xcb, 0x5d, 0x57, 0x91, 0x3c, 0x36, 0x97, 0xc8,
	0x63, 0xd2, 0x97, 0x50, 0xc9, 0x62, 0xc1, 0xb1, 0xae, 0xca, 0xe1, 0xdc, 0x94, 0xac, 0x18, 0xc4,
	0xd7, 0x3e, 0x03, 0xd4, 0x0d, 0x22, 0xbd, 0x84, 0xcd, 0xd4, 0x7d, 0x70, 0xa5, 0xe2, 0xa7, 0xaf,
	0xa0, 0x9f, 0xb2, 0x3a, 0x22, 0x4d, 0xd2, 0xb1, 0x50, 0x9d, 0x7f, 0x09, 0x4d, 0x96, 0x37, 0x7a,
	0x05, 0x8d, 0x95, 0xf7, 0x53, 0x40, 0x47, 0xa3, 0x13, 0x37, 0xb9, 0xd7, 0x4d, 0x43, 0xd5, 0x88,
	0x66, 0x1a, 0xe8, 0x0d, 0x28, 0x29, 0xfe, 0x22, 0x2a, 0xf0, 0x72, 0x00, 0x65, 0x52, 0x5f, 0x83,
	0x22, 0x19, 0xd8, 0xd8, 0x19, 0x98, 0x7a, 0x70, 0x23, 0x05, 0x00, 0x89, 0xc0, 0x4a, 0xf2, 0xdc,
	0x1f, 0x40, 0xc1, 0x19, 0x9d, 0x44, 0x45, 0x79, 0x83, 0x23, 0x4a, 0x9a, 0xa1, 0xce, 0x82, 0x33,
	0x3a, 0x61, 0xc2, 0xdc, 0x84, 0x45, 0x1b, 0xeb, 0x32, 0x89, 0x95, 0x02, 0xe0, 0x82, 0x58, 0xf8,
	0x7c, 0x23, 0x40, 0x25, 0x76, 0x0b, 0x84, 0x47, 0x4d, 0xbc, 0x5c, 0xe8, 0x3d, 0x69, 0x1a, 0x6a,
	0x2c, 0x2e, 0x29, 0x80, 0x6d, 0xb6, 0xa2, 0x3a, 0x61, 0x12, 0xe4, 0x99, 0x04, 0x12, 0x47, 0x82,
	0x24, 0xfb, 0xa1, 0xde, 0x78, 0x42, 0xcc, 0x26, 0x85, 0xa0, 0xb9, 0x63, 0x28, 0xf7, 0x35, 0x25,
	0x8c, 0x90, 0x05, 0xb6, 0x6e, 0xa9, 0xd2, 0x75, 0xd8, 0xc9, 0x14, 0xcf, 0xb1, 0xa4, 0xe7, 0x41,
	0xf8, 0x5c, 0xa9, 0xf4, 0xd2, 0xaf, 0x04, 0xd8, 0x69, 0x66, 0xd3, 0xe5, 0x68, 0x47, 0xb8, 0x22,
	0xed, 0xa4, 0x4d, 0xfc, 0x82, 0xb1, 0xf2, 0x94, 0x2a, 0xe4, 0xc8, 0xd2, 0x6c, 0x8d, 0x5c, 0x95,
	0x90, 0x7f, 0x14, 0x60, 0x83, 0x7b, 0x6c, 0xcc, 0x20, 0x42, 0xcc, 0x20, 0x1c, 0xc9, 0x73, 0x57,
	0x24, 0x79, 0x3e, 0x25, 0x39, 0x86, 0x6b, 0xd9, 0x92, 0xb3, 0xa2, 0x95, 0x4a, 0xd7, 0x9b, 0x54,
	0x4d, 0xf0, 0x0f, 0xa0, 0x7a, 0xe8, 0xb1, 0x4b, 0xe2, 0x19, 0xec, 0x3c, 0xc1, 0x7a, 0xa6, 0x82,
	0xc7, 0x28, 0x63, 0xac, 0x7a, 0x6f, 0xc0, 0xb5, 0xec, 0x63, 0x1d, 0x4b, 0xfa, 0x5a, 0x80, 0x2d,
	0x56, 0x6d, 0x52, 0xff, 0x62, 0x55, 0xa2, 0x8b, 0x70, 0x05, 0xf5, 0x70, 0xfe, 0x8a, 0xea, 0xe1,
	0x7f, 0x09, 0xb0, 0x92, 0xe0, 0x0e, 0x6d, 0xc2, 0xbc, 0xcc, 0x7e, 0xf9, 0x95, 0xb0, 0x1c, 0xc0,
	0xdd, 0xfa, 0xcc, 0x2b, 0x1d, 0xbd, 0xd5, 0x6b, 0x4d, 0x24, 0xb1, 0x58, 0x98, 0x8b, 0xc7, 0x82,
	0xf4, 0x39, 0x94, 0xf9, 0xea, 0x76, 0x2c, 0xf4, 0xfd, 0xb4, 0x27, 0xf1, 0xf8, 0x4b, 0x7e, 0x1a,
	0xfa, 0xd0, 0x9f, 0x04, 0x58, 0xdf, 0x37, 0x15, 0x59, 0xaf, 0xcb, 0xca, 0x20, 0xcc, 0x16, 0xbd,
	0xb8, 0x8b, 0x08, 0x13, 0x93, 0xec, 0xff, 0x2f, 0x98, 0x3e, 0x67, 0x2f, 0xd2, 0x90, 0x47, 0x76,
	0x73, 0x4e, 0xca, 0x1f, 0xf7, 0x60, 0xc5, 0xf5, 0x7d, 0x87, 0x79, 0x70, 0xe8, 0x7a, 0xcb, 0xc3,
	0xd0, 0xaf, 0x5b, 0xaa, 0xf4, 0x75, 0x8e, 0x3d, 0x2c, 0x93, 0xa7, 0x3b, 0x16, 0x2d, 0x0b, 0x1c,
	0x5a, 0xbd, 0x7b, 0xde, 0xe2, 0x2e, 0xe8, 0xb9, 0xac, 0x70, 0x65, 0x4f, 0xe0, 0x58, 0x9f, 0x88,
	0x82, 0xd9, 0x23, 0xd8, 0xef, 0x27, 0x31, 0x3c, 0xf7, 0xcd, 0x1c, 0x78, 0x8f, 0x87, 0xb7, 0x47,
	0xa1, 0x0c, 0xef, 0x76, 0xa2, 0x50, 0x9a, 0x65, 0x48, 0xb1, 0x3a, 0xe8, 0x49, 0xd4, 0xc4, 0x73,
	0x4c, 0xcd, 0xf7, 0x79, 0x85, 0x05, 0xc7, 0x88, 0xa1, 0x9d, 0x69, 0x18, 0x9e, 0xe0, 0xbe, 0x66,
	0x74, 0x23, 0xbd, 0x98, 0x22, 0x83, 0x1c, 0x6b, 0x43, 0x76, 0x93, 0x61, 0x6a, 0x6c, 0xba, 0xb9,
	0xe0, 0xe6, 0x0a, 0x6c, 0xa8, 0x74, 0x4b, 0xba, 0x03, 0x2b, 0x5e, 0xdd, 0xf3, 0x5c, 0xd6, 0x47,
	0xfc, 0x57, 0xaf, 0xf4, 0x4d, 0x0e, 0xc4, 0x38, 0x96, 0x63, 0x71, 0x92, 0x41, 0x05, 0x0a, 0x86,
	0x79, 0xa2, 0xe9, 0x1a, 0x71, 0x9f, 0x9b, 0xb3, 0x9d, 0x60, 0x4d, 0xe3, 0xf0, 0x1c, 0xcb, 0x3a,
	0x19, 0x78, 0x3e, 0xe0, 0xad, 0x58, 0x7d, 0x36, 0x90, 0xed, 0x61, 0x50, 0x9f, 0xd1, 0x05, 0x75,
	0x1b, 0x4b, 0x97, 0x49, 0xcf, 0xb4, 0x87, 0x5d, 0xfd, 0xcc, 0x8b, 0x19, 0xf0, 0x41, 0xfb, 0x67,
	0xe8, 0x3e, 0xac, 0xd8, 0x98, 0xe2, 0xf6, 0x71, 0x97, 0x0c, 0x34, 0x9b, 0x5c, 0x78, 0x52, 0x97,
	0x7c, 0xf0, 0x31, 0x83, 0xc6, 0x10, 0x0d, 0xcd, 0xc0, 0xe4, 0xc2, 0xd3, 0x40, 0x80, 0xd8, 0x66,
	0x50, 0x6a, 0xab, 0x00, 0xd1, 0xc4, 0x5f, 0x94, 0x0b, 0x0c, 0x6b, 0xd1, 0x87, 0x1d, 0xe0, 0x2f,
	0x28, 0x57, 0xf2, 0x88, 0x0c, 0xba, 0x36, 0x76, 0x46, 0x3a, 0x29, 0x17, 0x99, 0xeb, 0x00, 0x05,
	0x75, 0x18, 0x84, 0x3a, 0xad, 0x2b, 0x56, 0xf7, 0x6c, 0xb7, 0x0c, 0xae, 0x06, 0x5c, 0xc0, 0xf3,
	0x5d, 0x6a, 0x04, 0x26, 0x1c, 0xdd, 0x5b, 0x64, 0x7b, 0x0b, 0x6c, 0xfd, 0x7c, 0x57, 0xfa, 0x8b,
	0x00, 0xd0, 0x36, 0x89, 0xd6, 0xbb, 0xa0, 0xc5, 0x23, 0x7d, 0x35, 0x6a, 0x4e, 0xd7, 0xc0, 0x58,
	0xf5, 0x93, 0x99, 0xe6, 0xb4, 0x31, 0x76, 0x7b, 0x66, 0xf8, 0x15, 0x09, 0x7a, 0x66, 0xf8, 0x15,
	0x71, 0x0b, 0x5c, 0xdd, 0xb4, 0xbd, 0x86, 0x99, 0xbb, 0xa0, 0xa6, 0x50, 0x47, 0xb6, 0x4c, 0xe3,
	0xd0, 0xcf, 0xad, 0xfe, 0x1a, 0xd5, 0x80, 0x6a, 0x52, 0x89, 0xe4, 0xa3, 0x12, 0x37, 0xb4, 0x5d,
	0x8e, 0x0e, 0x29, 0x2a, 0x8d, 0xba, 0x4e, 0xd1, 0xf2, 0x7f, 0x4a, 0xff, 0x09, 0x19, 0x36, 0x7a,
	0x26, 0xfa, 0x10, 0x16, 0x0d, 0xb6, 0x0a, 0xc3, 0xb5, 0xb4, 0x7b, 0x3d, 0xf3, 0x48, 0x76, 0x1a,
	0x18, 0xc1, 0xef, 0x84, 0xfb, 0xe6, 0xc6, 0xb9, 0x6f, 0x3e, 0xe6, 0xbe, 0x11, 0xca, 0x41, 0x80,
	0x2d, 0x8e, 0xa1, 0x4c, 0xd5, 0xeb, 0x53, 0x66, 0x81, 0xb3, 0x05, 0x0b, 0x03, 0xd9, 0xe9, 0xda,
	0xd8, 0xbd, 0x65, 0x0a, 0x9d, 0xf9, 0x81, 0xec, 0x74, 0xb0, 0x4a, 0x4b, 0xad, 0xf5, 0x23, 0x4c,
	0x68, 0x6a, 0x0d, 0x05, 0xa5, 0xd1, 0xf1, 0x61, 0x32, 0x31, 0x95, 0xb8, 0xed, 0x28, 0xf7, 0x23,
	0xbf, 0x3b, 0x16, 0xc9, 0x5d, 0xef, 0xc0, 0x2c, 0x7d, 0x49, 0x30, 0x29, 0xc7, 0xb2, 0x4a, 0xe9,
	0x31, 0x54, 0x69, 0x0b, 0x36, 0x38, 0xac, 0x38, 0x96, 0xf4, 0x3b, 0xb7, 0x9f, 0x77, 0xf5, 0x4c,
	0x26, 0x0c, 0x9a, 0xbb, 0xa4, 0x41, 0xa5, 0x1f, 0xb2, 0xbc, 0x9b, 0xe6, 0x38, 0x90, 0x5e, 0x98,
	0x5e, 0xfa, 0xbf, 0x0a, 0x50, 0x8a, 0x9f, 0x74, 0x85, 0xe2, 0x45, 0x6e, 0xb7, 0x09, 0xcc, 0x44,
	0xbd, 0xe6, 0x36, 0x2c, 0x9d, 0x61, 0xdb, 0x61, 0xef, 0x32, 0xdf, 0x29, 0x67, 0x3b, 0x8b, 0x1e,
	0x8c, 0xe5, 0xd5, 0xef, 0xc1, 0xda, 0x9e, 0x4c, 0x9a, 0x98, 0xc4, 0x0d, 0x73, 0x17, 0x4a, 0x01,
	0xe7, 0xd1, 0x06, 0xc1, 0x92, 0xcf, 0x9b, 0xd7, 0x46, 0x5a, 0x4f, 0x7f, 0xcc, 0x1e, 0xde, 0x31,
	0xbe, 0xb3, 0x9b, 0xa1, 0x09, 0xcd, 0x47, 0x78, 0x97, 0x7e, 0x23, 0xc0, 0x36, 0x2b, 0x38, 0x52,
	0x25, 0xe0, 0xb7, 0xaa, 0xf0, 0x68, 0x21, 0x41, 0x4f, 0xeb, 0xca, 0xba, 0xce, 0xf4, 0x50, 0xe8,
	0x14, 0x18, 0xa0, 0xa6, 0xeb, 0xbc, 0x7b, 0x7a, 0x96, 0x77, 0x4f, 0xff, 0x5d, 0x80, 0xd5, 0x14,
	0x3b, 0xbc, 0xaf, 0x05, 0xce, 0xd7, 0x91, 0xd2, 0x2f, 0x17, 0x2b, 0xfd, 0x5e, 0x63, 0x89, 0x27,
	0x9d, 0x43, 0x25, 0x4b, 0xa9, 0x8e, 0x15, 0x29, 0x42, 0x85, 0x58, 0x11, 0x5a, 0x8b, 0x5e, 0xfe,
	0xb9, 0xcc, 0xa6, 0x7f, 0xfa, 0xd0, 0xb0, 0xc2, 0xfb, 0xb5, 0x00, 0x65, 0x2f, 0x39, 0xb8, 0x4d,
	0xc9, 0x03, 0x0b, 0x33, 0x0f, 0x9c, 0x58, 0x44, 0x7d, 0xfb, 0xa4, 0xbb, 0x05, 0x0b, 0xa6, 0x15,
	0x0e, 0xbe, 0x8a, 0x9d, 0x79, 0xd3, 0xa2, 0xb5, 0x81, 0xb4, 0x03, 0xdb, 0x19, 0xbc, 0x38, 0x96,
	0xf4, 0x3e, 0x9b, 0x80, 0x5c, 0x9e, 0x51, 0xe9, 0xf7, 0x6e, 0x43, 0x8c, 0x7f, 0xec, 0x78, 0x19,
	0xef, 0xc0, 0xb2, 0xd7, 0xda, 0xa5, 0x05, 0xde, 0xc8, 0xf7, 0x90, 0x25, 0x27, 0xe8, 0xdf, 0x8e,
	0x9c, 0x84, 0x22, 0xf2, 0xe3, 0x14, 0x31, 0x1b, 0x2f, 0x9e, 0xde, 0xf7, 0x6a, 0xf7, 0xf0, 0x41,
	0xde, 0x72, 0x28, 0x73, 0x13, 0x45, 0xfa, 0x89, 0x17, 0x83, 0xe9, 0x0f, 0xaf, 0x42, 0xa2, 0x6a,
	0x1d, 0x4a, 0xe1, 0xc9, 0xec, 0xb3, 0x25, 0x28, 0x1c, 0x18, 0xf8, 0x50, 0xc3, 0x0a, 0x16, 0x67,
	0xd0, 0x86, 0x3b, 0x66, 0x0c, 0xf7, 0x6b, 0xba, 0x2e, 0x0a, 0x14, 0xe9, 0xc8, 0x1b, 0x0f, 0x88,
	0xb9, 0xea, 0x77, 0xa1, 0xb0, 0xef, 0x77, 0xd6, 0x96, 0xa1, 0xf8, 0xcc, 0xaf, 0x6c, 0xc5, 0x19,
	0x7f, 0xc9, 0x0a, 0x62, 0x51, 0x40, 0x22, 0x2c, 0xf9, 0xad, 0x42, 0x86, 0x90, 0xab, 0xfe, 0x2d,
	0x07, 0xab, 0x6d, 0x7c, 0x9e, 0x60, 0xe2, 0x3a, 0x6c, 0xa7, 0x80, 0xdd, 0x96, 0x71, 0x26, 0xeb,
	0x9a, 0x2a, 0xce, 0xa0, 0x1b, 0x50, 0x49, 0x6f, 0x07, 0x5c, 0x0b, 0xfc, 0xfd, 0x90, 0x61, 0x74,
	0x1b, 0xae, 0xa7, 0xf7, 0x23, 0xb1, 0x23, 0xe6, 0x33, 0x8e, 0x18, 0xca, 0xce, 0xa0, 0xd1, 0xef,
	0x8b, 0xb3, 0xe8, 0x26, 0xec, 0xa4, 0xf7, 0x9b, 0xa6, 0xae, 0x32, 0x1c, 0x71, 0x0e, 0xdd, 0x82,
	0x6b, 0x69, 0x84, 0xba, 0x4c, 0xea, 0xb2, 0x41, 0x30, 0x36, 0xc4, 0x79, 0x3e, 0x46, 0x53, 0x37,
	0xed, 0x0b, 0xc6, 0x8a, 0xb8, 0xc0, 0x27, 0xe2, 0xca, 0x21, 0x6b, 0x86, 0x58, 0xa8, 0x5a, 0x80,
	0xc2, 0xdd, 0xc0, 0x06, 0x37, 0x68, 0x8a, 0x49, 0x42, 0x23, 0xea, 0xbb, 0x46, 0x9d, 0x31, 0xb5,
	0xef, 0x5a, 0x44, 0xc8, 0xd8, 0x75, 0x2d, 0x98, 0xab, 0x7e, 0x95, 0x83, 0xe5, 0x7a, 0xac, 0xab,
	0xb8, 0x0d, 0x1b, 0x31, 0x40, 0x84, 0x50, 0x05, 0x36, 0xe3, 0x5b, 0x6d, 0xaf, 0xc8, 0x17, 0x05,
	0x54, 0x86, 0xf5, 0xf8, 0xde, 0x0b, 0x56, 0xfe, 0x8a, 0x39, 0xb4, 0x05, 0x6b, 0xf1, 0x9d, 0x3a,
	0x2d, 0x7e, 0xc5, 0x3c, 0xe3, 0x2c, 0xb6, 0x71, 0x18, 0x94, 0xf9, 0xe2, 0x2c, 0x55, 0x67, 0x7c,
	0xb7, 0x13, 0xab, 0xef, 0x5d, 0x93, 0xf0, 0x31, 0x1a, 0x5a, 0x7f, 0x40, 0x2e, 0xc4, 0x79, 0xf4,
	0x00, 0xee, 0xf2, 0x31, 0x3e, 0x1e, 0x19, 0xaa, 0x8d, 0x55, 0xc3, 0xc3, 0x5c, 0xa8, 0xfe, 0x3b,
	0x07, 0xa5, 0xc6, 0x2b, 0x3c, 0xb4, 0x48, 0xdd, 0x7f, 0x0c, 0x57, 0x60, 0x33, 0x0e, 0x89, 0x7b,
	0x6c, 0x62, 0x8f, 0xf9, 0x89, 0xc7, 0x9a, 0x80, 0xee, 0xc2, 0x2d, 0xde, 0xbe, 0x6b, 0x88, 0x0e,
	0x1e, 0x52, 0x73, 0xe7, 0x90, 0x04, 0x37, 0x78, 0x58, 0xfb, 0x23, 0xe5, 0xf4, 0x82, 0x3d, 0xb7,
	0xc4, 0x3c, 0xf5, 0xed, 0x04, 0x8e, 0x1f, 0x18, 0x1e, 0xb1, 0x59, 0xce, 0x31, 0x3e, 0xca, 0x53,
	0x4d, 0xc7, 0x72, 0x1f, 0x8b, 0x73, 0x9c, 0x63, 0xfc, 0xf8, 0xf1, 0x8e, 0x99, 0xe7, 0xf0, 0x1c,
	0x09, 0x21, 0x0f, 0x6b, 0x01, 0xdd, 0x81, 0x9b, 0x09, 0xac, 0x30, 0x08, 0x3c, 0xa4, 0x42, 0xf5,
	0x47, 0x00, 0x9d, 0xb0, 0x0b, 0xb2, 0x09, 0x28, 0x5c, 0x45, 0x94, 0x88, 0xa0, 0x14, 0x81, 0xd7,
	0x0c, 0x55, 0x14, 0xd0, 0x2a, 0x2c, 0x47, 0x60, 0x07, 0xb6, 0x98, 0xab, 0x7e, 0x00, 0x85, 0xc0,
	0x26, 0x9e, 0x2f, 0xb9, 0x1f, 0x04, 0x37, 0xb7, 0x38, 0x13, 0xdb, 0x08, 0xf9, 0x13, 0x85, 0xea,
	0x21, 0x94, 0xe2, 0xd5, 0x20, 0x4b, 0x05, 0x31, 0x48, 0xf7, 0x59, 0xfb, 0xe8, 0xb0, 0x51, 0x6f,
	0x7d, 0xd4, 0x6a, 0x3c, 0xf1, 0xb2, 0x51, 0x7c, 0xbf, 0x5e, 0x3b, 0xee, 0xd6, 0x6b, 0xed, 0xe3,
	0x46, 0xa3, 0x2d, 0x0a, 0xd5, 0xaf, 0x04, 0x58, 0x49, 0xbc, 0x90, 0x58, 0x64, 0xc7, 0x41, 0x89,
	0x43, 0x77, 0x60, 0x2b, 0x89, 0x50, 0xff, 0xb8, 0xd6, 0x6e, 0x37, 0xf6, 0xdd, 0xd8, 0x49, 0x6e,
	0x36, 0x6b, 0x4f, 0x1b, 0x62, 0x8e, 0xfa, 0x60, 0x72, 0xe7, 0xf0, 0xd9, 0xde, 0x7e, 0xab, 0x2e,
	0xe6, 0xab, 0x1f, 0xfa, 0x2f, 0x31, 0x3f, 0x6c, 0xc3, 0x55, 0xf7, 0x59, 0xbb, 0xf1, 0xc9, 0x61,
	0xa3, 0x7e, 0xcc, 0x68, 0x6f, 0xc0, 0x6a, 0x64, 0xab, 0x7e, 0xf0, 0xf4, 0xe9, 0x41, 0x5b, 0x14,
	0x76, 0xff, 0xbb, 0x06, 0x2b, 0x61, 0x66, 0x60, 0x6d, 0x24, 0x64, 0xc0, 0x1a, 0x67, 0x86, 0x8b,
	0xde, 0xe4, 0x8d, 0x09, 0xb8, 0xe3, 0xe3, 0x4a, 0x75, 0x5a, 0x54, 0xc7, 0x92, 0x66, 0xd0, 0x2f,
	0xdd, 0xbe, 0x76, 0xd6, 0x94, 0x16, 0xbd, 0xc3, 0x2b, 0x71, 0xc7, 0xce, 0x7d, 0x2b, 0xbb, 0x97,
	0xfd, 0x84, 0x31, 0x72, 0xca, 0xe6, 0x99, 0x89, 0xa9, 0x1c, 0xe2, 0x75, 0x6f, 0xb9, 0xa3, 0xd8,
	0xca, 0x9b, 0x53, 0x62, 0x32, 0x62, 0x27, 0xb0, 0x92, 0x98, 0x65, 0xa2, 0x37, 0xf8, 0xdf, 0x27,
	0xa6, 0x9a, 0x95, 0x7b, 0xd3, 0xa0, 0xf9, 0x02, 0xa5, 0xe7, 0x98, 0x5c, 0x81, 0xb8, 0xf3, 0x53,
	0xae, 0x40, 0x19, 0x83, 0xd1, 0x19, 0x44, 0xfc, 0x29, 0x6c, 0x52, 0x81, 0x6f, 0x65, 0x9e, 0xc2,
	0xd1, 0xe1, 0xdb, 0xd3, 0x23, 0x33, 0xaa, 0xe7, 0x6c, 0x58, 0xc8, 0x99, 0x55, 0xa2, 0xb7, 0xf9,
	0x3e, 0xc0, 0x9f, 0xac, 0x56, 0x1e, 0x5e, 0x02, 0x9b, 0x11, 0x36, 0x60, 0x8d, 0x33, 0x32, 0xe4,
	0x46, 0x09, 0x7f, 0x9a, 0xc9, 0x8d, 0x92, 0x8c, 0x29, 0xa4, 0x34, 0x83, 0xbe, 0x48, 0xfc, 0xb3,
	0x22, 0x32, 0x19, 0x79, 0x38, 0x29, 0xdc, 0x62, 0xb3, 0x83, 0xca, 0xa3, 0xcb, 0xa0, 0xfb, 0xb4,
	0x9b, 0x97, 0xa0, 0xdd, 0xbc, 0x1c, 0xed, 0xe6, 0x58, 0xda, 0x5f, 0xb2, 0x87, 0x03, 0x7f, 0x24,
	0x94, 0x71, 0x5a, 0xd6, 0xd4, 0xa4, 0xf2, 0xf8, 0x52, 0xf8, 0x3e, 0xf9, 0xac, 0x81, 0x09, 0x97,
	0xfc, 0x98, 0xa1, 0x0d, 0x97, 0xfc, 0xd8, 0x69, 0xcc, 0x0c, 0x7a, 0xe9, 0xfd, 0x37, 0x29, 0x39,
	0xf0, 0xa8, 0x72, 0xff, 0xa0, 0xc2, 0x9d, 0xdb, 0x54, 0xde, 0x9a, 0x1a, 0xd7, 0x8f, 0x28, 0xfe,
	0x63, 0x96, 0x1b, 0x51, 0x99, 0xcd, 0x04, 0x6e, 0x44, 0x65, 0xbf, 0x92, 0xa5, 0x19, 0x34, 0x80,
	0xd5, 0x54, 0xbb, 0x1e, 0xdd, 0xe7, 0x9b, 0x2c, 0x35, 0x32, 0xa8, 0x3c, 0x98, 0x0e, 0x91, 0x51,
	0xfa, 0x14, 0x96, 0xa2, 0x0d, 0x6d, 0x24, 0x65, 0x47, 0xa2, 0xdf, 0x17, 0xaf, 0xdc, 0x99, 0x88,
	0xe3, 0x0b, 0x91, 0xea, 0xd6, 0x71, 0x85, 0xe0, 0xb5, 0x17, 0xb9, 0x42, 0xf0, 0x9b, 0x7f, 0xbe,
	0xba, 0xa6, 0xa0, 0xd4, 0x9c, 0x96, 0x52, 0x33, 0x83, 0x12, 0x06, 0x31, 0xd9, 0x90, 0x42, 0xf7,
	0xf8, 0x79, 0x3a, 0xd9, 0xf2, 0xaa, 0xdc, 0x9f, 0x0a, 0xcf, 0xbf, 0x40, 0x62, 0x69, 0xc8, 0x7f,
	0xe8, 0x73, 0x2f, 0x90, 0xac, 0xae, 0x07, 0xf7, 0x02, 0xc9, 0x6e, 0x4b, 0x30, 0xaa, 0xcd, 0xa9,
	0xa9, 0x36, 0x2f, 0x43, 0xb5, 0x39, 0x9e, 0x2a, 0xb7, 0x05, 0x80, 0x32, 0x83, 0x95, 0xd3, 0x65,
	0xa8, 0xbc, 0x3d, 0x3d, 0x32, 0xa5, 0xba, 0xf7, 0xde, 0x67, 0xef, 0xf6, 0x4d, 0x5d, 0x36, 0xfa,
	0x8f, 0xde, 0xdb, 0x25, 0xe4, 0x91, 0x62, 0x0e, 0x1f, 0xb3, 0xff, 0xa1, 0x2b, 0xa6, 0xfe, 0xd8,
	0xc1, 0xf6, 0x99, 0xa6, 0x60, 0x27, 0xfd, 0x5f, 0xf5, 0x93, 0x79, 0x86, 0xf4, 0xee, 0xff, 0x02,
	0x00, 0x00, 0xff, 0xff, 0xf0, 0xcc, 0xa0, 0x82, 0xe8, 0x2e, 0x00, 0x00,
}
