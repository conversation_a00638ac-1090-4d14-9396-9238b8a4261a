// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/chance-game-entry/chance-game-entry.proto

package chance_game_entry

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockChanceGameEntryClient is a mock of ChanceGameEntryClient interface.
type MockChanceGameEntryClient struct {
	ctrl     *gomock.Controller
	recorder *MockChanceGameEntryClientMockRecorder
}

// MockChanceGameEntryClientMockRecorder is the mock recorder for MockChanceGameEntryClient.
type MockChanceGameEntryClientMockRecorder struct {
	mock *MockChanceGameEntryClient
}

// NewMockChanceGameEntryClient creates a new mock instance.
func NewMockChanceGameEntryClient(ctrl *gomock.Controller) *MockChanceGameEntryClient {
	mock := &MockChanceGameEntryClient{ctrl: ctrl}
	mock.recorder = &MockChanceGameEntryClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChanceGameEntryClient) EXPECT() *MockChanceGameEntryClientMockRecorder {
	return m.recorder
}

// AddChannelBWListV2 mocks base method.
func (m *MockChanceGameEntryClient) AddChannelBWListV2(ctx context.Context, in *AddChannelBWListV2Req, opts ...grpc.CallOption) (*AddChannelBWListV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddChannelBWListV2", varargs...)
	ret0, _ := ret[0].(*AddChannelBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChannelBWListV2 indicates an expected call of AddChannelBWListV2.
func (mr *MockChanceGameEntryClientMockRecorder) AddChannelBWListV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelBWListV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).AddChannelBWListV2), varargs...)
}

// AddUserBWListV2 mocks base method.
func (m *MockChanceGameEntryClient) AddUserBWListV2(ctx context.Context, in *AddUserBWListV2Req, opts ...grpc.CallOption) (*AddUserBWListV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserBWListV2", varargs...)
	ret0, _ := ret[0].(*AddUserBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserBWListV2 indicates an expected call of AddUserBWListV2.
func (mr *MockChanceGameEntryClientMockRecorder) AddUserBWListV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserBWListV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).AddUserBWListV2), varargs...)
}

// BatDelChannelBWListV2 mocks base method.
func (m *MockChanceGameEntryClient) BatDelChannelBWListV2(ctx context.Context, in *BatDelChannelBWListV2Req, opts ...grpc.CallOption) (*BatDelChannelBWListV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatDelChannelBWListV2", varargs...)
	ret0, _ := ret[0].(*BatDelChannelBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatDelChannelBWListV2 indicates an expected call of BatDelChannelBWListV2.
func (mr *MockChanceGameEntryClientMockRecorder) BatDelChannelBWListV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelChannelBWListV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).BatDelChannelBWListV2), varargs...)
}

// BatDelUserBWListV2 mocks base method.
func (m *MockChanceGameEntryClient) BatDelUserBWListV2(ctx context.Context, in *BatDelUserBWListV2Req, opts ...grpc.CallOption) (*BatDelUserBWListV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatDelUserBWListV2", varargs...)
	ret0, _ := ret[0].(*BatDelUserBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatDelUserBWListV2 indicates an expected call of BatDelUserBWListV2.
func (mr *MockChanceGameEntryClientMockRecorder) BatDelUserBWListV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelUserBWListV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).BatDelUserBWListV2), varargs...)
}

// BatGetNotifyInfo mocks base method.
func (m *MockChanceGameEntryClient) BatGetNotifyInfo(ctx context.Context, in *BatGetNotifyInfoReq, opts ...grpc.CallOption) (*BatGetNotifyInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetNotifyInfo", varargs...)
	ret0, _ := ret[0].(*BatGetNotifyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetNotifyInfo indicates an expected call of BatGetNotifyInfo.
func (mr *MockChanceGameEntryClientMockRecorder) BatGetNotifyInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetNotifyInfo", reflect.TypeOf((*MockChanceGameEntryClient)(nil).BatGetNotifyInfo), varargs...)
}

// CheckChanceGameIsOpen mocks base method.
func (m *MockChanceGameEntryClient) CheckChanceGameIsOpen(ctx context.Context, in *CheckChanceGameIsOpenReq, opts ...grpc.CallOption) (*CheckChanceGameIsOpenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckChanceGameIsOpen", varargs...)
	ret0, _ := ret[0].(*CheckChanceGameIsOpenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckChanceGameIsOpen indicates an expected call of CheckChanceGameIsOpen.
func (mr *MockChanceGameEntryClientMockRecorder) CheckChanceGameIsOpen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChanceGameIsOpen", reflect.TypeOf((*MockChanceGameEntryClient)(nil).CheckChanceGameIsOpen), varargs...)
}

// CheckGameEntryAccess mocks base method.
func (m *MockChanceGameEntryClient) CheckGameEntryAccess(ctx context.Context, in *CheckGameEntryAccessReq, opts ...grpc.CallOption) (*CheckGameEntryAccessResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckGameEntryAccess", varargs...)
	ret0, _ := ret[0].(*CheckGameEntryAccessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckGameEntryAccess indicates an expected call of CheckGameEntryAccess.
func (mr *MockChanceGameEntryClientMockRecorder) CheckGameEntryAccess(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckGameEntryAccess", reflect.TypeOf((*MockChanceGameEntryClient)(nil).CheckGameEntryAccess), varargs...)
}

// CheckMagicSpiritAccess mocks base method.
func (m *MockChanceGameEntryClient) CheckMagicSpiritAccess(ctx context.Context, in *CheckMagicSpiritAccessReq, opts ...grpc.CallOption) (*CheckMagicSpiritAccessResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckMagicSpiritAccess", varargs...)
	ret0, _ := ret[0].(*CheckMagicSpiritAccessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMagicSpiritAccess indicates an expected call of CheckMagicSpiritAccess.
func (mr *MockChanceGameEntryClientMockRecorder) CheckMagicSpiritAccess(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMagicSpiritAccess", reflect.TypeOf((*MockChanceGameEntryClient)(nil).CheckMagicSpiritAccess), varargs...)
}

// DelMagicSpiritAccessCond mocks base method.
func (m *MockChanceGameEntryClient) DelMagicSpiritAccessCond(ctx context.Context, in *DelMagicSpiritAccessCondReq, opts ...grpc.CallOption) (*DelMagicSpiritAccessCondResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelMagicSpiritAccessCond", varargs...)
	ret0, _ := ret[0].(*DelMagicSpiritAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelMagicSpiritAccessCond indicates an expected call of DelMagicSpiritAccessCond.
func (mr *MockChanceGameEntryClientMockRecorder) DelMagicSpiritAccessCond(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicSpiritAccessCond", reflect.TypeOf((*MockChanceGameEntryClient)(nil).DelMagicSpiritAccessCond), varargs...)
}

// GetAllChanceGameSwitchState mocks base method.
func (m *MockChanceGameEntryClient) GetAllChanceGameSwitchState(ctx context.Context, in *GetAllChanceGameSwitchStateReq, opts ...grpc.CallOption) (*GetAllChanceGameSwitchStateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllChanceGameSwitchState", varargs...)
	ret0, _ := ret[0].(*GetAllChanceGameSwitchStateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllChanceGameSwitchState indicates an expected call of GetAllChanceGameSwitchState.
func (mr *MockChanceGameEntryClientMockRecorder) GetAllChanceGameSwitchState(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChanceGameSwitchState", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetAllChanceGameSwitchState), varargs...)
}

// GetChanceGameAccessCond mocks base method.
func (m *MockChanceGameEntryClient) GetChanceGameAccessCond(ctx context.Context, in *GetChanceGameAccessCondReq, opts ...grpc.CallOption) (*GetChanceGameAccessCondResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChanceGameAccessCond", varargs...)
	ret0, _ := ret[0].(*GetChanceGameAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanceGameAccessCond indicates an expected call of GetChanceGameAccessCond.
func (mr *MockChanceGameEntryClientMockRecorder) GetChanceGameAccessCond(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanceGameAccessCond", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetChanceGameAccessCond), varargs...)
}

// GetChanceGameOpenTime mocks base method.
func (m *MockChanceGameEntryClient) GetChanceGameOpenTime(ctx context.Context, in *GetGameSwitchOpenTimeReq, opts ...grpc.CallOption) (*GetGameSwitchOpenTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChanceGameOpenTime", varargs...)
	ret0, _ := ret[0].(*GetGameSwitchOpenTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanceGameOpenTime indicates an expected call of GetChanceGameOpenTime.
func (mr *MockChanceGameEntryClientMockRecorder) GetChanceGameOpenTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanceGameOpenTime", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetChanceGameOpenTime), varargs...)
}

// GetChannelBWListInfoV2 mocks base method.
func (m *MockChanceGameEntryClient) GetChannelBWListInfoV2(ctx context.Context, in *GetChannelBWListInfoV2Req, opts ...grpc.CallOption) (*GetChannelBWListInfoV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelBWListInfoV2", varargs...)
	ret0, _ := ret[0].(*GetChannelBWListInfoV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelBWListInfoV2 indicates an expected call of GetChannelBWListInfoV2.
func (mr *MockChanceGameEntryClientMockRecorder) GetChannelBWListInfoV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBWListInfoV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetChannelBWListInfoV2), varargs...)
}

// GetGameNotifyInfo mocks base method.
func (m *MockChanceGameEntryClient) GetGameNotifyInfo(ctx context.Context, in *GetGameNotifyInfoReq, opts ...grpc.CallOption) (*GetGameNotifyInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameNotifyInfo", varargs...)
	ret0, _ := ret[0].(*GetGameNotifyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameNotifyInfo indicates an expected call of GetGameNotifyInfo.
func (mr *MockChanceGameEntryClientMockRecorder) GetGameNotifyInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameNotifyInfo", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetGameNotifyInfo), varargs...)
}

// GetLocalCacheConf mocks base method.
func (m *MockChanceGameEntryClient) GetLocalCacheConf(ctx context.Context, in *GetLocalCacheConfReq, opts ...grpc.CallOption) (*GetLocalCacheConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLocalCacheConf", varargs...)
	ret0, _ := ret[0].(*GetLocalCacheConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLocalCacheConf indicates an expected call of GetLocalCacheConf.
func (mr *MockChanceGameEntryClientMockRecorder) GetLocalCacheConf(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocalCacheConf", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetLocalCacheConf), varargs...)
}

// GetMagicSpiritAccessCond mocks base method.
func (m *MockChanceGameEntryClient) GetMagicSpiritAccessCond(ctx context.Context, in *GetMagicSpiritAccessCondReq, opts ...grpc.CallOption) (*GetMagicSpiritAccessCondResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMagicSpiritAccessCond", varargs...)
	ret0, _ := ret[0].(*GetMagicSpiritAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritAccessCond indicates an expected call of GetMagicSpiritAccessCond.
func (mr *MockChanceGameEntryClientMockRecorder) GetMagicSpiritAccessCond(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritAccessCond", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetMagicSpiritAccessCond), varargs...)
}

// GetUserBWListInfoV2 mocks base method.
func (m *MockChanceGameEntryClient) GetUserBWListInfoV2(ctx context.Context, in *GetUserBWListInfoV2Req, opts ...grpc.CallOption) (*GetUserBWListInfoV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserBWListInfoV2", varargs...)
	ret0, _ := ret[0].(*GetUserBWListInfoV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserBWListInfoV2 indicates an expected call of GetUserBWListInfoV2.
func (mr *MockChanceGameEntryClientMockRecorder) GetUserBWListInfoV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBWListInfoV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetUserBWListInfoV2), varargs...)
}

// GetUserValue mocks base method.
func (m *MockChanceGameEntryClient) GetUserValue(ctx context.Context, in *GetUserValueReq, opts ...grpc.CallOption) (*GetUserValueResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserValue", varargs...)
	ret0, _ := ret[0].(*GetUserValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserValue indicates an expected call of GetUserValue.
func (mr *MockChanceGameEntryClientMockRecorder) GetUserValue(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserValue", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetUserValue), varargs...)
}

// SetChanceGameAccessCond mocks base method.
func (m *MockChanceGameEntryClient) SetChanceGameAccessCond(ctx context.Context, in *SetChanceGameAccessCondReq, opts ...grpc.CallOption) (*SetChanceGameAccessCondResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChanceGameAccessCond", varargs...)
	ret0, _ := ret[0].(*SetChanceGameAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameAccessCond indicates an expected call of SetChanceGameAccessCond.
func (mr *MockChanceGameEntryClientMockRecorder) SetChanceGameAccessCond(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameAccessCond", reflect.TypeOf((*MockChanceGameEntryClient)(nil).SetChanceGameAccessCond), varargs...)
}

// SetChanceGameOpenTime mocks base method.
func (m *MockChanceGameEntryClient) SetChanceGameOpenTime(ctx context.Context, in *SetGameSwitchOpenTimeReq, opts ...grpc.CallOption) (*SetGameSwitchOpenTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChanceGameOpenTime", varargs...)
	ret0, _ := ret[0].(*SetGameSwitchOpenTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameOpenTime indicates an expected call of SetChanceGameOpenTime.
func (mr *MockChanceGameEntryClientMockRecorder) SetChanceGameOpenTime(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameOpenTime", reflect.TypeOf((*MockChanceGameEntryClient)(nil).SetChanceGameOpenTime), varargs...)
}

// SetChanceGameSwitch mocks base method.
func (m *MockChanceGameEntryClient) SetChanceGameSwitch(ctx context.Context, in *SetChanceGameSwitchReq, opts ...grpc.CallOption) (*SetChanceGameSwitchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChanceGameSwitch", varargs...)
	ret0, _ := ret[0].(*SetChanceGameSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameSwitch indicates an expected call of SetChanceGameSwitch.
func (mr *MockChanceGameEntryClientMockRecorder) SetChanceGameSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameSwitch", reflect.TypeOf((*MockChanceGameEntryClient)(nil).SetChanceGameSwitch), varargs...)
}

// SetGameNotifyInfo mocks base method.
func (m *MockChanceGameEntryClient) SetGameNotifyInfo(ctx context.Context, in *SetGameNotifyInfoReq, opts ...grpc.CallOption) (*SetGameNotifyInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetGameNotifyInfo", varargs...)
	ret0, _ := ret[0].(*SetGameNotifyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGameNotifyInfo indicates an expected call of SetGameNotifyInfo.
func (mr *MockChanceGameEntryClientMockRecorder) SetGameNotifyInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameNotifyInfo", reflect.TypeOf((*MockChanceGameEntryClient)(nil).SetGameNotifyInfo), varargs...)
}

// MockChanceGameEntryServer is a mock of ChanceGameEntryServer interface.
type MockChanceGameEntryServer struct {
	ctrl     *gomock.Controller
	recorder *MockChanceGameEntryServerMockRecorder
}

// MockChanceGameEntryServerMockRecorder is the mock recorder for MockChanceGameEntryServer.
type MockChanceGameEntryServerMockRecorder struct {
	mock *MockChanceGameEntryServer
}

// NewMockChanceGameEntryServer creates a new mock instance.
func NewMockChanceGameEntryServer(ctrl *gomock.Controller) *MockChanceGameEntryServer {
	mock := &MockChanceGameEntryServer{ctrl: ctrl}
	mock.recorder = &MockChanceGameEntryServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChanceGameEntryServer) EXPECT() *MockChanceGameEntryServerMockRecorder {
	return m.recorder
}

// AddChannelBWListV2 mocks base method.
func (m *MockChanceGameEntryServer) AddChannelBWListV2(ctx context.Context, in *AddChannelBWListV2Req) (*AddChannelBWListV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelBWListV2", ctx, in)
	ret0, _ := ret[0].(*AddChannelBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChannelBWListV2 indicates an expected call of AddChannelBWListV2.
func (mr *MockChanceGameEntryServerMockRecorder) AddChannelBWListV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelBWListV2", reflect.TypeOf((*MockChanceGameEntryServer)(nil).AddChannelBWListV2), ctx, in)
}

// AddUserBWListV2 mocks base method.
func (m *MockChanceGameEntryServer) AddUserBWListV2(ctx context.Context, in *AddUserBWListV2Req) (*AddUserBWListV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserBWListV2", ctx, in)
	ret0, _ := ret[0].(*AddUserBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserBWListV2 indicates an expected call of AddUserBWListV2.
func (mr *MockChanceGameEntryServerMockRecorder) AddUserBWListV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserBWListV2", reflect.TypeOf((*MockChanceGameEntryServer)(nil).AddUserBWListV2), ctx, in)
}

// BatDelChannelBWListV2 mocks base method.
func (m *MockChanceGameEntryServer) BatDelChannelBWListV2(ctx context.Context, in *BatDelChannelBWListV2Req) (*BatDelChannelBWListV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatDelChannelBWListV2", ctx, in)
	ret0, _ := ret[0].(*BatDelChannelBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatDelChannelBWListV2 indicates an expected call of BatDelChannelBWListV2.
func (mr *MockChanceGameEntryServerMockRecorder) BatDelChannelBWListV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelChannelBWListV2", reflect.TypeOf((*MockChanceGameEntryServer)(nil).BatDelChannelBWListV2), ctx, in)
}

// BatDelUserBWListV2 mocks base method.
func (m *MockChanceGameEntryServer) BatDelUserBWListV2(ctx context.Context, in *BatDelUserBWListV2Req) (*BatDelUserBWListV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatDelUserBWListV2", ctx, in)
	ret0, _ := ret[0].(*BatDelUserBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatDelUserBWListV2 indicates an expected call of BatDelUserBWListV2.
func (mr *MockChanceGameEntryServerMockRecorder) BatDelUserBWListV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelUserBWListV2", reflect.TypeOf((*MockChanceGameEntryServer)(nil).BatDelUserBWListV2), ctx, in)
}

// BatGetNotifyInfo mocks base method.
func (m *MockChanceGameEntryServer) BatGetNotifyInfo(ctx context.Context, in *BatGetNotifyInfoReq) (*BatGetNotifyInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetNotifyInfo", ctx, in)
	ret0, _ := ret[0].(*BatGetNotifyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetNotifyInfo indicates an expected call of BatGetNotifyInfo.
func (mr *MockChanceGameEntryServerMockRecorder) BatGetNotifyInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetNotifyInfo", reflect.TypeOf((*MockChanceGameEntryServer)(nil).BatGetNotifyInfo), ctx, in)
}

// CheckChanceGameIsOpen mocks base method.
func (m *MockChanceGameEntryServer) CheckChanceGameIsOpen(ctx context.Context, in *CheckChanceGameIsOpenReq) (*CheckChanceGameIsOpenResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckChanceGameIsOpen", ctx, in)
	ret0, _ := ret[0].(*CheckChanceGameIsOpenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckChanceGameIsOpen indicates an expected call of CheckChanceGameIsOpen.
func (mr *MockChanceGameEntryServerMockRecorder) CheckChanceGameIsOpen(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChanceGameIsOpen", reflect.TypeOf((*MockChanceGameEntryServer)(nil).CheckChanceGameIsOpen), ctx, in)
}

// CheckGameEntryAccess mocks base method.
func (m *MockChanceGameEntryServer) CheckGameEntryAccess(ctx context.Context, in *CheckGameEntryAccessReq) (*CheckGameEntryAccessResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckGameEntryAccess", ctx, in)
	ret0, _ := ret[0].(*CheckGameEntryAccessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckGameEntryAccess indicates an expected call of CheckGameEntryAccess.
func (mr *MockChanceGameEntryServerMockRecorder) CheckGameEntryAccess(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckGameEntryAccess", reflect.TypeOf((*MockChanceGameEntryServer)(nil).CheckGameEntryAccess), ctx, in)
}

// CheckMagicSpiritAccess mocks base method.
func (m *MockChanceGameEntryServer) CheckMagicSpiritAccess(ctx context.Context, in *CheckMagicSpiritAccessReq) (*CheckMagicSpiritAccessResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckMagicSpiritAccess", ctx, in)
	ret0, _ := ret[0].(*CheckMagicSpiritAccessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMagicSpiritAccess indicates an expected call of CheckMagicSpiritAccess.
func (mr *MockChanceGameEntryServerMockRecorder) CheckMagicSpiritAccess(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMagicSpiritAccess", reflect.TypeOf((*MockChanceGameEntryServer)(nil).CheckMagicSpiritAccess), ctx, in)
}

// DelMagicSpiritAccessCond mocks base method.
func (m *MockChanceGameEntryServer) DelMagicSpiritAccessCond(ctx context.Context, in *DelMagicSpiritAccessCondReq) (*DelMagicSpiritAccessCondResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelMagicSpiritAccessCond", ctx, in)
	ret0, _ := ret[0].(*DelMagicSpiritAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelMagicSpiritAccessCond indicates an expected call of DelMagicSpiritAccessCond.
func (mr *MockChanceGameEntryServerMockRecorder) DelMagicSpiritAccessCond(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicSpiritAccessCond", reflect.TypeOf((*MockChanceGameEntryServer)(nil).DelMagicSpiritAccessCond), ctx, in)
}

// GetAllChanceGameSwitchState mocks base method.
func (m *MockChanceGameEntryServer) GetAllChanceGameSwitchState(ctx context.Context, in *GetAllChanceGameSwitchStateReq) (*GetAllChanceGameSwitchStateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllChanceGameSwitchState", ctx, in)
	ret0, _ := ret[0].(*GetAllChanceGameSwitchStateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllChanceGameSwitchState indicates an expected call of GetAllChanceGameSwitchState.
func (mr *MockChanceGameEntryServerMockRecorder) GetAllChanceGameSwitchState(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChanceGameSwitchState", reflect.TypeOf((*MockChanceGameEntryServer)(nil).GetAllChanceGameSwitchState), ctx, in)
}

// GetChanceGameAccessCond mocks base method.
func (m *MockChanceGameEntryServer) GetChanceGameAccessCond(ctx context.Context, in *GetChanceGameAccessCondReq) (*GetChanceGameAccessCondResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChanceGameAccessCond", ctx, in)
	ret0, _ := ret[0].(*GetChanceGameAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanceGameAccessCond indicates an expected call of GetChanceGameAccessCond.
func (mr *MockChanceGameEntryServerMockRecorder) GetChanceGameAccessCond(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanceGameAccessCond", reflect.TypeOf((*MockChanceGameEntryServer)(nil).GetChanceGameAccessCond), ctx, in)
}

// GetChanceGameOpenTime mocks base method.
func (m *MockChanceGameEntryServer) GetChanceGameOpenTime(ctx context.Context, in *GetGameSwitchOpenTimeReq) (*GetGameSwitchOpenTimeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChanceGameOpenTime", ctx, in)
	ret0, _ := ret[0].(*GetGameSwitchOpenTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanceGameOpenTime indicates an expected call of GetChanceGameOpenTime.
func (mr *MockChanceGameEntryServerMockRecorder) GetChanceGameOpenTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanceGameOpenTime", reflect.TypeOf((*MockChanceGameEntryServer)(nil).GetChanceGameOpenTime), ctx, in)
}

// GetChannelBWListInfoV2 mocks base method.
func (m *MockChanceGameEntryServer) GetChannelBWListInfoV2(ctx context.Context, in *GetChannelBWListInfoV2Req) (*GetChannelBWListInfoV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelBWListInfoV2", ctx, in)
	ret0, _ := ret[0].(*GetChannelBWListInfoV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelBWListInfoV2 indicates an expected call of GetChannelBWListInfoV2.
func (mr *MockChanceGameEntryServerMockRecorder) GetChannelBWListInfoV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBWListInfoV2", reflect.TypeOf((*MockChanceGameEntryServer)(nil).GetChannelBWListInfoV2), ctx, in)
}

// GetGameNotifyInfo mocks base method.
func (m *MockChanceGameEntryServer) GetGameNotifyInfo(ctx context.Context, in *GetGameNotifyInfoReq) (*GetGameNotifyInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameNotifyInfo", ctx, in)
	ret0, _ := ret[0].(*GetGameNotifyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameNotifyInfo indicates an expected call of GetGameNotifyInfo.
func (mr *MockChanceGameEntryServerMockRecorder) GetGameNotifyInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameNotifyInfo", reflect.TypeOf((*MockChanceGameEntryServer)(nil).GetGameNotifyInfo), ctx, in)
}

// GetLocalCacheConf mocks base method.
func (m *MockChanceGameEntryServer) GetLocalCacheConf(ctx context.Context, in *GetLocalCacheConfReq) (*GetLocalCacheConfResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLocalCacheConf", ctx, in)
	ret0, _ := ret[0].(*GetLocalCacheConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLocalCacheConf indicates an expected call of GetLocalCacheConf.
func (mr *MockChanceGameEntryServerMockRecorder) GetLocalCacheConf(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocalCacheConf", reflect.TypeOf((*MockChanceGameEntryServer)(nil).GetLocalCacheConf), ctx, in)
}

// GetMagicSpiritAccessCond mocks base method.
func (m *MockChanceGameEntryServer) GetMagicSpiritAccessCond(ctx context.Context, in *GetMagicSpiritAccessCondReq) (*GetMagicSpiritAccessCondResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMagicSpiritAccessCond", ctx, in)
	ret0, _ := ret[0].(*GetMagicSpiritAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritAccessCond indicates an expected call of GetMagicSpiritAccessCond.
func (mr *MockChanceGameEntryServerMockRecorder) GetMagicSpiritAccessCond(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritAccessCond", reflect.TypeOf((*MockChanceGameEntryServer)(nil).GetMagicSpiritAccessCond), ctx, in)
}

// GetUserBWListInfoV2 mocks base method.
func (m *MockChanceGameEntryServer) GetUserBWListInfoV2(ctx context.Context, in *GetUserBWListInfoV2Req) (*GetUserBWListInfoV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBWListInfoV2", ctx, in)
	ret0, _ := ret[0].(*GetUserBWListInfoV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserBWListInfoV2 indicates an expected call of GetUserBWListInfoV2.
func (mr *MockChanceGameEntryServerMockRecorder) GetUserBWListInfoV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBWListInfoV2", reflect.TypeOf((*MockChanceGameEntryServer)(nil).GetUserBWListInfoV2), ctx, in)
}

// GetUserValue mocks base method.
func (m *MockChanceGameEntryServer) GetUserValue(ctx context.Context, in *GetUserValueReq) (*GetUserValueResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserValue", ctx, in)
	ret0, _ := ret[0].(*GetUserValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserValue indicates an expected call of GetUserValue.
func (mr *MockChanceGameEntryServerMockRecorder) GetUserValue(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserValue", reflect.TypeOf((*MockChanceGameEntryServer)(nil).GetUserValue), ctx, in)
}

// SetChanceGameAccessCond mocks base method.
func (m *MockChanceGameEntryServer) SetChanceGameAccessCond(ctx context.Context, in *SetChanceGameAccessCondReq) (*SetChanceGameAccessCondResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChanceGameAccessCond", ctx, in)
	ret0, _ := ret[0].(*SetChanceGameAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameAccessCond indicates an expected call of SetChanceGameAccessCond.
func (mr *MockChanceGameEntryServerMockRecorder) SetChanceGameAccessCond(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameAccessCond", reflect.TypeOf((*MockChanceGameEntryServer)(nil).SetChanceGameAccessCond), ctx, in)
}

// SetChanceGameOpenTime mocks base method.
func (m *MockChanceGameEntryServer) SetChanceGameOpenTime(ctx context.Context, in *SetGameSwitchOpenTimeReq) (*SetGameSwitchOpenTimeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChanceGameOpenTime", ctx, in)
	ret0, _ := ret[0].(*SetGameSwitchOpenTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameOpenTime indicates an expected call of SetChanceGameOpenTime.
func (mr *MockChanceGameEntryServerMockRecorder) SetChanceGameOpenTime(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameOpenTime", reflect.TypeOf((*MockChanceGameEntryServer)(nil).SetChanceGameOpenTime), ctx, in)
}

// SetChanceGameSwitch mocks base method.
func (m *MockChanceGameEntryServer) SetChanceGameSwitch(ctx context.Context, in *SetChanceGameSwitchReq) (*SetChanceGameSwitchResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChanceGameSwitch", ctx, in)
	ret0, _ := ret[0].(*SetChanceGameSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameSwitch indicates an expected call of SetChanceGameSwitch.
func (mr *MockChanceGameEntryServerMockRecorder) SetChanceGameSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameSwitch", reflect.TypeOf((*MockChanceGameEntryServer)(nil).SetChanceGameSwitch), ctx, in)
}

// SetGameNotifyInfo mocks base method.
func (m *MockChanceGameEntryServer) SetGameNotifyInfo(ctx context.Context, in *SetGameNotifyInfoReq) (*SetGameNotifyInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGameNotifyInfo", ctx, in)
	ret0, _ := ret[0].(*SetGameNotifyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGameNotifyInfo indicates an expected call of SetGameNotifyInfo.
func (mr *MockChanceGameEntryServerMockRecorder) SetGameNotifyInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameNotifyInfo", reflect.TypeOf((*MockChanceGameEntryServer)(nil).SetGameNotifyInfo), ctx, in)
}
