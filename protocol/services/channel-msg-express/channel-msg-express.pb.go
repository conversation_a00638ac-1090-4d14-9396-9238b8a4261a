// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-msg-express/channel-msg-express.proto

package channel_msg_express // import "golang.52tt.com/protocol/services/channel-msg-express"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel "golang.52tt.com/protocol/app/channel"
import v2 "golang.52tt.com/protocol/services/push-notification/v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ERuleType int32

const (
	ERuleType_RULE_UNKNOW  ERuleType = 0
	ERuleType_RULE_DUPLEX  ERuleType = 1
	ERuleType_RULE_SIMPLEX ERuleType = 2
)

var ERuleType_name = map[int32]string{
	0: "RULE_UNKNOW",
	1: "RULE_DUPLEX",
	2: "RULE_SIMPLEX",
}
var ERuleType_value = map[string]int32{
	"RULE_UNKNOW":  0,
	"RULE_DUPLEX":  1,
	"RULE_SIMPLEX": 2,
}

func (x ERuleType) String() string {
	return proto.EnumName(ERuleType_name, int32(x))
}
func (ERuleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{0}
}

type PushOption struct {
	Sequence                    uint32                      `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	TerminalTypeList            []uint32                    `protobuf:"varint,2,rep,packed,name=terminal_type_list,json=terminalTypeList,proto3" json:"terminal_type_list,omitempty"`
	TerminalTypePolicy          *v2.TerminalTypePolicy      `protobuf:"bytes,3,opt,name=terminal_type_policy,json=terminalTypePolicy,proto3" json:"terminal_type_policy,omitempty"`
	ProxyNotificationType       uint32                      `protobuf:"varint,4,opt,name=proxy_notification_type,json=proxyNotificationType,proto3" json:"proxy_notification_type,omitempty"`
	ProxyNotificationPolicy     v2.ProxyNotification_Policy `protobuf:"varint,5,opt,name=proxy_notification_policy,json=proxyNotificationPolicy,proto3,enum=PushNotification.ProxyNotification_Policy" json:"proxy_notification_policy,omitempty"`
	ProxyNotificationExpireTime uint32                      `protobuf:"varint,6,opt,name=proxy_notification_expire_time,json=proxyNotificationExpireTime,proto3" json:"proxy_notification_expire_time,omitempty"`
	Sync                        bool                        `protobuf:"varint,7,opt,name=sync,proto3" json:"sync,omitempty"`
	XXX_NoUnkeyedLiteral        struct{}                    `json:"-"`
	XXX_unrecognized            []byte                      `json:"-"`
	XXX_sizecache               int32                       `json:"-"`
}

func (m *PushOption) Reset()         { *m = PushOption{} }
func (m *PushOption) String() string { return proto.CompactTextString(m) }
func (*PushOption) ProtoMessage()    {}
func (*PushOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{0}
}
func (m *PushOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushOption.Unmarshal(m, b)
}
func (m *PushOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushOption.Marshal(b, m, deterministic)
}
func (dst *PushOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushOption.Merge(dst, src)
}
func (m *PushOption) XXX_Size() int {
	return xxx_messageInfo_PushOption.Size(m)
}
func (m *PushOption) XXX_DiscardUnknown() {
	xxx_messageInfo_PushOption.DiscardUnknown(m)
}

var xxx_messageInfo_PushOption proto.InternalMessageInfo

func (m *PushOption) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *PushOption) GetTerminalTypeList() []uint32 {
	if m != nil {
		return m.TerminalTypeList
	}
	return nil
}

func (m *PushOption) GetTerminalTypePolicy() *v2.TerminalTypePolicy {
	if m != nil {
		return m.TerminalTypePolicy
	}
	return nil
}

func (m *PushOption) GetProxyNotificationType() uint32 {
	if m != nil {
		return m.ProxyNotificationType
	}
	return 0
}

func (m *PushOption) GetProxyNotificationPolicy() v2.ProxyNotification_Policy {
	if m != nil {
		return m.ProxyNotificationPolicy
	}
	return v2.ProxyNotification_DEFAULT
}

func (m *PushOption) GetProxyNotificationExpireTime() uint32 {
	if m != nil {
		return m.ProxyNotificationExpireTime
	}
	return 0
}

func (m *PushOption) GetSync() bool {
	if m != nil {
		return m.Sync
	}
	return false
}

type SendChannelMsgReq struct {
	Msg                  *channel.ChannelMsg `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	ServerName           string              `protobuf:"bytes,2,opt,name=server_name,json=serverName,proto3" json:"server_name,omitempty"`
	PushOpts             *PushOption         `protobuf:"bytes,3,opt,name=push_opts,json=pushOpts,proto3" json:"push_opts,omitempty"`
	SkipUidList          []uint32            `protobuf:"varint,4,rep,packed,name=skip_uid_list,json=skipUidList,proto3" json:"skip_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SendChannelMsgReq) Reset()         { *m = SendChannelMsgReq{} }
func (m *SendChannelMsgReq) String() string { return proto.CompactTextString(m) }
func (*SendChannelMsgReq) ProtoMessage()    {}
func (*SendChannelMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{1}
}
func (m *SendChannelMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelMsgReq.Unmarshal(m, b)
}
func (m *SendChannelMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelMsgReq.Marshal(b, m, deterministic)
}
func (dst *SendChannelMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelMsgReq.Merge(dst, src)
}
func (m *SendChannelMsgReq) XXX_Size() int {
	return xxx_messageInfo_SendChannelMsgReq.Size(m)
}
func (m *SendChannelMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelMsgReq proto.InternalMessageInfo

func (m *SendChannelMsgReq) GetMsg() *channel.ChannelMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *SendChannelMsgReq) GetServerName() string {
	if m != nil {
		return m.ServerName
	}
	return ""
}

func (m *SendChannelMsgReq) GetPushOpts() *PushOption {
	if m != nil {
		return m.PushOpts
	}
	return nil
}

func (m *SendChannelMsgReq) GetSkipUidList() []uint32 {
	if m != nil {
		return m.SkipUidList
	}
	return nil
}

type SendChannelMsgResp struct {
	Sequence             uint32   `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	RequestId            string   `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendChannelMsgResp) Reset()         { *m = SendChannelMsgResp{} }
func (m *SendChannelMsgResp) String() string { return proto.CompactTextString(m) }
func (*SendChannelMsgResp) ProtoMessage()    {}
func (*SendChannelMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{2}
}
func (m *SendChannelMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelMsgResp.Unmarshal(m, b)
}
func (m *SendChannelMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelMsgResp.Marshal(b, m, deterministic)
}
func (dst *SendChannelMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelMsgResp.Merge(dst, src)
}
func (m *SendChannelMsgResp) XXX_Size() int {
	return xxx_messageInfo_SendChannelMsgResp.Size(m)
}
func (m *SendChannelMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelMsgResp proto.InternalMessageInfo

func (m *SendChannelMsgResp) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *SendChannelMsgResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

type SendChannelBroadcastMsgReq struct {
	Msg                  *channel.ChannelBroadcastMsg `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	ServerName           string                       `protobuf:"bytes,2,opt,name=server_name,json=serverName,proto3" json:"server_name,omitempty"`
	PushOpts             *PushOption                  `protobuf:"bytes,3,opt,name=push_opts,json=pushOpts,proto3" json:"push_opts,omitempty"`
	SkipUidList          []uint32                     `protobuf:"varint,4,rep,packed,name=skip_uid_list,json=skipUidList,proto3" json:"skip_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *SendChannelBroadcastMsgReq) Reset()         { *m = SendChannelBroadcastMsgReq{} }
func (m *SendChannelBroadcastMsgReq) String() string { return proto.CompactTextString(m) }
func (*SendChannelBroadcastMsgReq) ProtoMessage()    {}
func (*SendChannelBroadcastMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{3}
}
func (m *SendChannelBroadcastMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelBroadcastMsgReq.Unmarshal(m, b)
}
func (m *SendChannelBroadcastMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelBroadcastMsgReq.Marshal(b, m, deterministic)
}
func (dst *SendChannelBroadcastMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelBroadcastMsgReq.Merge(dst, src)
}
func (m *SendChannelBroadcastMsgReq) XXX_Size() int {
	return xxx_messageInfo_SendChannelBroadcastMsgReq.Size(m)
}
func (m *SendChannelBroadcastMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelBroadcastMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelBroadcastMsgReq proto.InternalMessageInfo

func (m *SendChannelBroadcastMsgReq) GetMsg() *channel.ChannelBroadcastMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *SendChannelBroadcastMsgReq) GetServerName() string {
	if m != nil {
		return m.ServerName
	}
	return ""
}

func (m *SendChannelBroadcastMsgReq) GetPushOpts() *PushOption {
	if m != nil {
		return m.PushOpts
	}
	return nil
}

func (m *SendChannelBroadcastMsgReq) GetSkipUidList() []uint32 {
	if m != nil {
		return m.SkipUidList
	}
	return nil
}

type SendChannelBroadcastMsgResp struct {
	Sequence             uint32   `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	RequestId            string   `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendChannelBroadcastMsgResp) Reset()         { *m = SendChannelBroadcastMsgResp{} }
func (m *SendChannelBroadcastMsgResp) String() string { return proto.CompactTextString(m) }
func (*SendChannelBroadcastMsgResp) ProtoMessage()    {}
func (*SendChannelBroadcastMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{4}
}
func (m *SendChannelBroadcastMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelBroadcastMsgResp.Unmarshal(m, b)
}
func (m *SendChannelBroadcastMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelBroadcastMsgResp.Marshal(b, m, deterministic)
}
func (dst *SendChannelBroadcastMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelBroadcastMsgResp.Merge(dst, src)
}
func (m *SendChannelBroadcastMsgResp) XXX_Size() int {
	return xxx_messageInfo_SendChannelBroadcastMsgResp.Size(m)
}
func (m *SendChannelBroadcastMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelBroadcastMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelBroadcastMsgResp proto.InternalMessageInfo

func (m *SendChannelBroadcastMsgResp) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *SendChannelBroadcastMsgResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

type PushToUsersReq struct {
	Msg                  *channel.ChannelBroadcastMsg `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	ServerName           string                       `protobuf:"bytes,2,opt,name=server_name,json=serverName,proto3" json:"server_name,omitempty"`
	PushOpts             *PushOption                  `protobuf:"bytes,3,opt,name=push_opts,json=pushOpts,proto3" json:"push_opts,omitempty"`
	UidList              []uint32                     `protobuf:"varint,4,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Sequence             uint32                       `protobuf:"varint,5,opt,name=sequence,proto3" json:"sequence,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *PushToUsersReq) Reset()         { *m = PushToUsersReq{} }
func (m *PushToUsersReq) String() string { return proto.CompactTextString(m) }
func (*PushToUsersReq) ProtoMessage()    {}
func (*PushToUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{5}
}
func (m *PushToUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToUsersReq.Unmarshal(m, b)
}
func (m *PushToUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToUsersReq.Marshal(b, m, deterministic)
}
func (dst *PushToUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToUsersReq.Merge(dst, src)
}
func (m *PushToUsersReq) XXX_Size() int {
	return xxx_messageInfo_PushToUsersReq.Size(m)
}
func (m *PushToUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushToUsersReq proto.InternalMessageInfo

func (m *PushToUsersReq) GetMsg() *channel.ChannelBroadcastMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *PushToUsersReq) GetServerName() string {
	if m != nil {
		return m.ServerName
	}
	return ""
}

func (m *PushToUsersReq) GetPushOpts() *PushOption {
	if m != nil {
		return m.PushOpts
	}
	return nil
}

func (m *PushToUsersReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *PushToUsersReq) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

type PushToUsersRsp struct {
	Sequence             uint32   `protobuf:"varint,1,opt,name=sequence,proto3" json:"sequence,omitempty"`
	RequestId            string   `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushToUsersRsp) Reset()         { *m = PushToUsersRsp{} }
func (m *PushToUsersRsp) String() string { return proto.CompactTextString(m) }
func (*PushToUsersRsp) ProtoMessage()    {}
func (*PushToUsersRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{6}
}
func (m *PushToUsersRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToUsersRsp.Unmarshal(m, b)
}
func (m *PushToUsersRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToUsersRsp.Marshal(b, m, deterministic)
}
func (dst *PushToUsersRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToUsersRsp.Merge(dst, src)
}
func (m *PushToUsersRsp) XXX_Size() int {
	return xxx_messageInfo_PushToUsersRsp.Size(m)
}
func (m *PushToUsersRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToUsersRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PushToUsersRsp proto.InternalMessageInfo

func (m *PushToUsersRsp) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *PushToUsersRsp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

type PushToUserMapReq struct {
	Msg                  *channel.ChannelBroadcastMsg `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	ServerName           string                       `protobuf:"bytes,2,opt,name=server_name,json=serverName,proto3" json:"server_name,omitempty"`
	PushOpts             *PushOption                  `protobuf:"bytes,3,opt,name=push_opts,json=pushOpts,proto3" json:"push_opts,omitempty"`
	SeqMap               map[uint32]uint32            `protobuf:"bytes,4,rep,name=seq_map,json=seqMap,proto3" json:"seq_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *PushToUserMapReq) Reset()         { *m = PushToUserMapReq{} }
func (m *PushToUserMapReq) String() string { return proto.CompactTextString(m) }
func (*PushToUserMapReq) ProtoMessage()    {}
func (*PushToUserMapReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{7}
}
func (m *PushToUserMapReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToUserMapReq.Unmarshal(m, b)
}
func (m *PushToUserMapReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToUserMapReq.Marshal(b, m, deterministic)
}
func (dst *PushToUserMapReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToUserMapReq.Merge(dst, src)
}
func (m *PushToUserMapReq) XXX_Size() int {
	return xxx_messageInfo_PushToUserMapReq.Size(m)
}
func (m *PushToUserMapReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToUserMapReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushToUserMapReq proto.InternalMessageInfo

func (m *PushToUserMapReq) GetMsg() *channel.ChannelBroadcastMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *PushToUserMapReq) GetServerName() string {
	if m != nil {
		return m.ServerName
	}
	return ""
}

func (m *PushToUserMapReq) GetPushOpts() *PushOption {
	if m != nil {
		return m.PushOpts
	}
	return nil
}

func (m *PushToUserMapReq) GetSeqMap() map[uint32]uint32 {
	if m != nil {
		return m.SeqMap
	}
	return nil
}

type PushToUserMapRsp struct {
	RequestId            string   `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushToUserMapRsp) Reset()         { *m = PushToUserMapRsp{} }
func (m *PushToUserMapRsp) String() string { return proto.CompactTextString(m) }
func (*PushToUserMapRsp) ProtoMessage()    {}
func (*PushToUserMapRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{8}
}
func (m *PushToUserMapRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushToUserMapRsp.Unmarshal(m, b)
}
func (m *PushToUserMapRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushToUserMapRsp.Marshal(b, m, deterministic)
}
func (dst *PushToUserMapRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushToUserMapRsp.Merge(dst, src)
}
func (m *PushToUserMapRsp) XXX_Size() int {
	return xxx_messageInfo_PushToUserMapRsp.Size(m)
}
func (m *PushToUserMapRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushToUserMapRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PushToUserMapRsp proto.InternalMessageInfo

func (m *PushToUserMapRsp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

type DuplicateRule struct {
	Policy               *CmdPolicy `protobuf:"bytes,1,opt,name=policy,proto3" json:"policy,omitempty"`
	FromChannelId        uint32     `protobuf:"varint,2,opt,name=from_channel_id,json=fromChannelId,proto3" json:"from_channel_id,omitempty"`
	ToChannelIdList      []uint32   `protobuf:"varint,3,rep,packed,name=to_channel_id_list,json=toChannelIdList,proto3" json:"to_channel_id_list,omitempty"`
	ExpireAt             int64      `protobuf:"varint,4,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	RuleId               string     `protobuf:"bytes,5,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	RuleType             uint32     `protobuf:"varint,6,opt,name=rule_type,json=ruleType,proto3" json:"rule_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *DuplicateRule) Reset()         { *m = DuplicateRule{} }
func (m *DuplicateRule) String() string { return proto.CompactTextString(m) }
func (*DuplicateRule) ProtoMessage()    {}
func (*DuplicateRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{9}
}
func (m *DuplicateRule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DuplicateRule.Unmarshal(m, b)
}
func (m *DuplicateRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DuplicateRule.Marshal(b, m, deterministic)
}
func (dst *DuplicateRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DuplicateRule.Merge(dst, src)
}
func (m *DuplicateRule) XXX_Size() int {
	return xxx_messageInfo_DuplicateRule.Size(m)
}
func (m *DuplicateRule) XXX_DiscardUnknown() {
	xxx_messageInfo_DuplicateRule.DiscardUnknown(m)
}

var xxx_messageInfo_DuplicateRule proto.InternalMessageInfo

func (m *DuplicateRule) GetPolicy() *CmdPolicy {
	if m != nil {
		return m.Policy
	}
	return nil
}

func (m *DuplicateRule) GetFromChannelId() uint32 {
	if m != nil {
		return m.FromChannelId
	}
	return 0
}

func (m *DuplicateRule) GetToChannelIdList() []uint32 {
	if m != nil {
		return m.ToChannelIdList
	}
	return nil
}

func (m *DuplicateRule) GetExpireAt() int64 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

func (m *DuplicateRule) GetRuleId() string {
	if m != nil {
		return m.RuleId
	}
	return ""
}

func (m *DuplicateRule) GetRuleType() uint32 {
	if m != nil {
		return m.RuleType
	}
	return 0
}

type SetDuplicateRuleReq struct {
	Rules                []*DuplicateRule `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetDuplicateRuleReq) Reset()         { *m = SetDuplicateRuleReq{} }
func (m *SetDuplicateRuleReq) String() string { return proto.CompactTextString(m) }
func (*SetDuplicateRuleReq) ProtoMessage()    {}
func (*SetDuplicateRuleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{10}
}
func (m *SetDuplicateRuleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDuplicateRuleReq.Unmarshal(m, b)
}
func (m *SetDuplicateRuleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDuplicateRuleReq.Marshal(b, m, deterministic)
}
func (dst *SetDuplicateRuleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDuplicateRuleReq.Merge(dst, src)
}
func (m *SetDuplicateRuleReq) XXX_Size() int {
	return xxx_messageInfo_SetDuplicateRuleReq.Size(m)
}
func (m *SetDuplicateRuleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDuplicateRuleReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDuplicateRuleReq proto.InternalMessageInfo

func (m *SetDuplicateRuleReq) GetRules() []*DuplicateRule {
	if m != nil {
		return m.Rules
	}
	return nil
}

type SetDuplicateRuleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDuplicateRuleResp) Reset()         { *m = SetDuplicateRuleResp{} }
func (m *SetDuplicateRuleResp) String() string { return proto.CompactTextString(m) }
func (*SetDuplicateRuleResp) ProtoMessage()    {}
func (*SetDuplicateRuleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{11}
}
func (m *SetDuplicateRuleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDuplicateRuleResp.Unmarshal(m, b)
}
func (m *SetDuplicateRuleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDuplicateRuleResp.Marshal(b, m, deterministic)
}
func (dst *SetDuplicateRuleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDuplicateRuleResp.Merge(dst, src)
}
func (m *SetDuplicateRuleResp) XXX_Size() int {
	return xxx_messageInfo_SetDuplicateRuleResp.Size(m)
}
func (m *SetDuplicateRuleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDuplicateRuleResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDuplicateRuleResp proto.InternalMessageInfo

type GetDuplicateRuleReq struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDuplicateRuleReq) Reset()         { *m = GetDuplicateRuleReq{} }
func (m *GetDuplicateRuleReq) String() string { return proto.CompactTextString(m) }
func (*GetDuplicateRuleReq) ProtoMessage()    {}
func (*GetDuplicateRuleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{12}
}
func (m *GetDuplicateRuleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDuplicateRuleReq.Unmarshal(m, b)
}
func (m *GetDuplicateRuleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDuplicateRuleReq.Marshal(b, m, deterministic)
}
func (dst *GetDuplicateRuleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDuplicateRuleReq.Merge(dst, src)
}
func (m *GetDuplicateRuleReq) XXX_Size() int {
	return xxx_messageInfo_GetDuplicateRuleReq.Size(m)
}
func (m *GetDuplicateRuleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDuplicateRuleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDuplicateRuleReq proto.InternalMessageInfo

func (m *GetDuplicateRuleReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type GetDuplicateRuleResp struct {
	Rules                []*DuplicateRule `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetDuplicateRuleResp) Reset()         { *m = GetDuplicateRuleResp{} }
func (m *GetDuplicateRuleResp) String() string { return proto.CompactTextString(m) }
func (*GetDuplicateRuleResp) ProtoMessage()    {}
func (*GetDuplicateRuleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{13}
}
func (m *GetDuplicateRuleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDuplicateRuleResp.Unmarshal(m, b)
}
func (m *GetDuplicateRuleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDuplicateRuleResp.Marshal(b, m, deterministic)
}
func (dst *GetDuplicateRuleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDuplicateRuleResp.Merge(dst, src)
}
func (m *GetDuplicateRuleResp) XXX_Size() int {
	return xxx_messageInfo_GetDuplicateRuleResp.Size(m)
}
func (m *GetDuplicateRuleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDuplicateRuleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDuplicateRuleResp proto.InternalMessageInfo

func (m *GetDuplicateRuleResp) GetRules() []*DuplicateRule {
	if m != nil {
		return m.Rules
	}
	return nil
}

type CmdPolicy struct {
	PolicyGroup          string   `protobuf:"bytes,1,opt,name=policy_group,json=policyGroup,proto3" json:"policy_group,omitempty"`
	PolicyName           string   `protobuf:"bytes,2,opt,name=policy_name,json=policyName,proto3" json:"policy_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CmdPolicy) Reset()         { *m = CmdPolicy{} }
func (m *CmdPolicy) String() string { return proto.CompactTextString(m) }
func (*CmdPolicy) ProtoMessage()    {}
func (*CmdPolicy) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{14}
}
func (m *CmdPolicy) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CmdPolicy.Unmarshal(m, b)
}
func (m *CmdPolicy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CmdPolicy.Marshal(b, m, deterministic)
}
func (dst *CmdPolicy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CmdPolicy.Merge(dst, src)
}
func (m *CmdPolicy) XXX_Size() int {
	return xxx_messageInfo_CmdPolicy.Size(m)
}
func (m *CmdPolicy) XXX_DiscardUnknown() {
	xxx_messageInfo_CmdPolicy.DiscardUnknown(m)
}

var xxx_messageInfo_CmdPolicy proto.InternalMessageInfo

func (m *CmdPolicy) GetPolicyGroup() string {
	if m != nil {
		return m.PolicyGroup
	}
	return ""
}

func (m *CmdPolicy) GetPolicyName() string {
	if m != nil {
		return m.PolicyName
	}
	return ""
}

type CmdPolicyInfo struct {
	Policy               *CmdPolicy `protobuf:"bytes,1,opt,name=policy,proto3" json:"policy,omitempty"`
	CmdList              []uint32   `protobuf:"varint,2,rep,packed,name=cmd_list,json=cmdList,proto3" json:"cmd_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CmdPolicyInfo) Reset()         { *m = CmdPolicyInfo{} }
func (m *CmdPolicyInfo) String() string { return proto.CompactTextString(m) }
func (*CmdPolicyInfo) ProtoMessage()    {}
func (*CmdPolicyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{15}
}
func (m *CmdPolicyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CmdPolicyInfo.Unmarshal(m, b)
}
func (m *CmdPolicyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CmdPolicyInfo.Marshal(b, m, deterministic)
}
func (dst *CmdPolicyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CmdPolicyInfo.Merge(dst, src)
}
func (m *CmdPolicyInfo) XXX_Size() int {
	return xxx_messageInfo_CmdPolicyInfo.Size(m)
}
func (m *CmdPolicyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CmdPolicyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CmdPolicyInfo proto.InternalMessageInfo

func (m *CmdPolicyInfo) GetPolicy() *CmdPolicy {
	if m != nil {
		return m.Policy
	}
	return nil
}

func (m *CmdPolicyInfo) GetCmdList() []uint32 {
	if m != nil {
		return m.CmdList
	}
	return nil
}

type SetCmdPolicyInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCmdPolicyInfoResp) Reset()         { *m = SetCmdPolicyInfoResp{} }
func (m *SetCmdPolicyInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetCmdPolicyInfoResp) ProtoMessage()    {}
func (*SetCmdPolicyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{16}
}
func (m *SetCmdPolicyInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCmdPolicyInfoResp.Unmarshal(m, b)
}
func (m *SetCmdPolicyInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCmdPolicyInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetCmdPolicyInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCmdPolicyInfoResp.Merge(dst, src)
}
func (m *SetCmdPolicyInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetCmdPolicyInfoResp.Size(m)
}
func (m *SetCmdPolicyInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCmdPolicyInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCmdPolicyInfoResp proto.InternalMessageInfo

type GetCmdPolicyResp struct {
	Info                 *CmdPolicyInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetCmdPolicyResp) Reset()         { *m = GetCmdPolicyResp{} }
func (m *GetCmdPolicyResp) String() string { return proto.CompactTextString(m) }
func (*GetCmdPolicyResp) ProtoMessage()    {}
func (*GetCmdPolicyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_msg_express_9ac40c0a3dab5133, []int{17}
}
func (m *GetCmdPolicyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCmdPolicyResp.Unmarshal(m, b)
}
func (m *GetCmdPolicyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCmdPolicyResp.Marshal(b, m, deterministic)
}
func (dst *GetCmdPolicyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCmdPolicyResp.Merge(dst, src)
}
func (m *GetCmdPolicyResp) XXX_Size() int {
	return xxx_messageInfo_GetCmdPolicyResp.Size(m)
}
func (m *GetCmdPolicyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCmdPolicyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCmdPolicyResp proto.InternalMessageInfo

func (m *GetCmdPolicyResp) GetInfo() *CmdPolicyInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func init() {
	proto.RegisterType((*PushOption)(nil), "channel_msg_express.PushOption")
	proto.RegisterType((*SendChannelMsgReq)(nil), "channel_msg_express.SendChannelMsgReq")
	proto.RegisterType((*SendChannelMsgResp)(nil), "channel_msg_express.SendChannelMsgResp")
	proto.RegisterType((*SendChannelBroadcastMsgReq)(nil), "channel_msg_express.SendChannelBroadcastMsgReq")
	proto.RegisterType((*SendChannelBroadcastMsgResp)(nil), "channel_msg_express.SendChannelBroadcastMsgResp")
	proto.RegisterType((*PushToUsersReq)(nil), "channel_msg_express.PushToUsersReq")
	proto.RegisterType((*PushToUsersRsp)(nil), "channel_msg_express.PushToUsersRsp")
	proto.RegisterType((*PushToUserMapReq)(nil), "channel_msg_express.PushToUserMapReq")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_msg_express.PushToUserMapReq.SeqMapEntry")
	proto.RegisterType((*PushToUserMapRsp)(nil), "channel_msg_express.PushToUserMapRsp")
	proto.RegisterType((*DuplicateRule)(nil), "channel_msg_express.DuplicateRule")
	proto.RegisterType((*SetDuplicateRuleReq)(nil), "channel_msg_express.SetDuplicateRuleReq")
	proto.RegisterType((*SetDuplicateRuleResp)(nil), "channel_msg_express.SetDuplicateRuleResp")
	proto.RegisterType((*GetDuplicateRuleReq)(nil), "channel_msg_express.GetDuplicateRuleReq")
	proto.RegisterType((*GetDuplicateRuleResp)(nil), "channel_msg_express.GetDuplicateRuleResp")
	proto.RegisterType((*CmdPolicy)(nil), "channel_msg_express.CmdPolicy")
	proto.RegisterType((*CmdPolicyInfo)(nil), "channel_msg_express.CmdPolicyInfo")
	proto.RegisterType((*SetCmdPolicyInfoResp)(nil), "channel_msg_express.SetCmdPolicyInfoResp")
	proto.RegisterType((*GetCmdPolicyResp)(nil), "channel_msg_express.GetCmdPolicyResp")
	proto.RegisterEnum("channel_msg_express.ERuleType", ERuleType_name, ERuleType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelMsgExpressClient is the client API for ChannelMsgExpress service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelMsgExpressClient interface {
	// 发送带序列号的消息
	SendChannelMsg(ctx context.Context, in *SendChannelMsgReq, opts ...grpc.CallOption) (*SendChannelMsgResp, error)
	// 发送无序列号的消息
	SendChannelBroadcastMsg(ctx context.Context, in *SendChannelBroadcastMsgReq, opts ...grpc.CallOption) (*SendChannelBroadcastMsgResp, error)
	// 发送给特定用户
	PushToUsers(ctx context.Context, in *PushToUsersReq, opts ...grpc.CallOption) (*PushToUsersRsp, error)
	// 发送给特定用户（需要可靠推送给多用户时使用该接口）
	PushToUserMap(ctx context.Context, in *PushToUserMapReq, opts ...grpc.CallOption) (*PushToUserMapRsp, error)
	// 消息转发
	SetDuplicateRule(ctx context.Context, in *SetDuplicateRuleReq, opts ...grpc.CallOption) (*SetDuplicateRuleResp, error)
	// 消息转发
	GetDuplicateRule(ctx context.Context, in *GetDuplicateRuleReq, opts ...grpc.CallOption) (*GetDuplicateRuleResp, error)
}

type channelMsgExpressClient struct {
	cc *grpc.ClientConn
}

func NewChannelMsgExpressClient(cc *grpc.ClientConn) ChannelMsgExpressClient {
	return &channelMsgExpressClient{cc}
}

func (c *channelMsgExpressClient) SendChannelMsg(ctx context.Context, in *SendChannelMsgReq, opts ...grpc.CallOption) (*SendChannelMsgResp, error) {
	out := new(SendChannelMsgResp)
	err := c.cc.Invoke(ctx, "/channel_msg_express.ChannelMsgExpress/SendChannelMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMsgExpressClient) SendChannelBroadcastMsg(ctx context.Context, in *SendChannelBroadcastMsgReq, opts ...grpc.CallOption) (*SendChannelBroadcastMsgResp, error) {
	out := new(SendChannelBroadcastMsgResp)
	err := c.cc.Invoke(ctx, "/channel_msg_express.ChannelMsgExpress/SendChannelBroadcastMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMsgExpressClient) PushToUsers(ctx context.Context, in *PushToUsersReq, opts ...grpc.CallOption) (*PushToUsersRsp, error) {
	out := new(PushToUsersRsp)
	err := c.cc.Invoke(ctx, "/channel_msg_express.ChannelMsgExpress/PushToUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMsgExpressClient) PushToUserMap(ctx context.Context, in *PushToUserMapReq, opts ...grpc.CallOption) (*PushToUserMapRsp, error) {
	out := new(PushToUserMapRsp)
	err := c.cc.Invoke(ctx, "/channel_msg_express.ChannelMsgExpress/PushToUserMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMsgExpressClient) SetDuplicateRule(ctx context.Context, in *SetDuplicateRuleReq, opts ...grpc.CallOption) (*SetDuplicateRuleResp, error) {
	out := new(SetDuplicateRuleResp)
	err := c.cc.Invoke(ctx, "/channel_msg_express.ChannelMsgExpress/SetDuplicateRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMsgExpressClient) GetDuplicateRule(ctx context.Context, in *GetDuplicateRuleReq, opts ...grpc.CallOption) (*GetDuplicateRuleResp, error) {
	out := new(GetDuplicateRuleResp)
	err := c.cc.Invoke(ctx, "/channel_msg_express.ChannelMsgExpress/GetDuplicateRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelMsgExpressServer is the server API for ChannelMsgExpress service.
type ChannelMsgExpressServer interface {
	// 发送带序列号的消息
	SendChannelMsg(context.Context, *SendChannelMsgReq) (*SendChannelMsgResp, error)
	// 发送无序列号的消息
	SendChannelBroadcastMsg(context.Context, *SendChannelBroadcastMsgReq) (*SendChannelBroadcastMsgResp, error)
	// 发送给特定用户
	PushToUsers(context.Context, *PushToUsersReq) (*PushToUsersRsp, error)
	// 发送给特定用户（需要可靠推送给多用户时使用该接口）
	PushToUserMap(context.Context, *PushToUserMapReq) (*PushToUserMapRsp, error)
	// 消息转发
	SetDuplicateRule(context.Context, *SetDuplicateRuleReq) (*SetDuplicateRuleResp, error)
	// 消息转发
	GetDuplicateRule(context.Context, *GetDuplicateRuleReq) (*GetDuplicateRuleResp, error)
}

func RegisterChannelMsgExpressServer(s *grpc.Server, srv ChannelMsgExpressServer) {
	s.RegisterService(&_ChannelMsgExpress_serviceDesc, srv)
}

func _ChannelMsgExpress_SendChannelMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendChannelMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMsgExpressServer).SendChannelMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_msg_express.ChannelMsgExpress/SendChannelMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMsgExpressServer).SendChannelMsg(ctx, req.(*SendChannelMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMsgExpress_SendChannelBroadcastMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendChannelBroadcastMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMsgExpressServer).SendChannelBroadcastMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_msg_express.ChannelMsgExpress/SendChannelBroadcastMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMsgExpressServer).SendChannelBroadcastMsg(ctx, req.(*SendChannelBroadcastMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMsgExpress_PushToUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushToUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMsgExpressServer).PushToUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_msg_express.ChannelMsgExpress/PushToUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMsgExpressServer).PushToUsers(ctx, req.(*PushToUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMsgExpress_PushToUserMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushToUserMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMsgExpressServer).PushToUserMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_msg_express.ChannelMsgExpress/PushToUserMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMsgExpressServer).PushToUserMap(ctx, req.(*PushToUserMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMsgExpress_SetDuplicateRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDuplicateRuleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMsgExpressServer).SetDuplicateRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_msg_express.ChannelMsgExpress/SetDuplicateRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMsgExpressServer).SetDuplicateRule(ctx, req.(*SetDuplicateRuleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMsgExpress_GetDuplicateRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDuplicateRuleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMsgExpressServer).GetDuplicateRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_msg_express.ChannelMsgExpress/GetDuplicateRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMsgExpressServer).GetDuplicateRule(ctx, req.(*GetDuplicateRuleReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelMsgExpress_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_msg_express.ChannelMsgExpress",
	HandlerType: (*ChannelMsgExpressServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendChannelMsg",
			Handler:    _ChannelMsgExpress_SendChannelMsg_Handler,
		},
		{
			MethodName: "SendChannelBroadcastMsg",
			Handler:    _ChannelMsgExpress_SendChannelBroadcastMsg_Handler,
		},
		{
			MethodName: "PushToUsers",
			Handler:    _ChannelMsgExpress_PushToUsers_Handler,
		},
		{
			MethodName: "PushToUserMap",
			Handler:    _ChannelMsgExpress_PushToUserMap_Handler,
		},
		{
			MethodName: "SetDuplicateRule",
			Handler:    _ChannelMsgExpress_SetDuplicateRule_Handler,
		},
		{
			MethodName: "GetDuplicateRule",
			Handler:    _ChannelMsgExpress_GetDuplicateRule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-msg-express/channel-msg-express.proto",
}

// CmdPolicyServiceClient is the client API for CmdPolicyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CmdPolicyServiceClient interface {
	SetCmdPolicy(ctx context.Context, in *CmdPolicyInfo, opts ...grpc.CallOption) (*SetCmdPolicyInfoResp, error)
	GetCmdPolicy(ctx context.Context, in *CmdPolicy, opts ...grpc.CallOption) (*GetCmdPolicyResp, error)
}

type cmdPolicyServiceClient struct {
	cc *grpc.ClientConn
}

func NewCmdPolicyServiceClient(cc *grpc.ClientConn) CmdPolicyServiceClient {
	return &cmdPolicyServiceClient{cc}
}

func (c *cmdPolicyServiceClient) SetCmdPolicy(ctx context.Context, in *CmdPolicyInfo, opts ...grpc.CallOption) (*SetCmdPolicyInfoResp, error) {
	out := new(SetCmdPolicyInfoResp)
	err := c.cc.Invoke(ctx, "/channel_msg_express.CmdPolicyService/SetCmdPolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cmdPolicyServiceClient) GetCmdPolicy(ctx context.Context, in *CmdPolicy, opts ...grpc.CallOption) (*GetCmdPolicyResp, error) {
	out := new(GetCmdPolicyResp)
	err := c.cc.Invoke(ctx, "/channel_msg_express.CmdPolicyService/GetCmdPolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CmdPolicyServiceServer is the server API for CmdPolicyService service.
type CmdPolicyServiceServer interface {
	SetCmdPolicy(context.Context, *CmdPolicyInfo) (*SetCmdPolicyInfoResp, error)
	GetCmdPolicy(context.Context, *CmdPolicy) (*GetCmdPolicyResp, error)
}

func RegisterCmdPolicyServiceServer(s *grpc.Server, srv CmdPolicyServiceServer) {
	s.RegisterService(&_CmdPolicyService_serviceDesc, srv)
}

func _CmdPolicyService_SetCmdPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CmdPolicyInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CmdPolicyServiceServer).SetCmdPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_msg_express.CmdPolicyService/SetCmdPolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CmdPolicyServiceServer).SetCmdPolicy(ctx, req.(*CmdPolicyInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _CmdPolicyService_GetCmdPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CmdPolicy)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CmdPolicyServiceServer).GetCmdPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_msg_express.CmdPolicyService/GetCmdPolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CmdPolicyServiceServer).GetCmdPolicy(ctx, req.(*CmdPolicy))
	}
	return interceptor(ctx, in, info, handler)
}

var _CmdPolicyService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_msg_express.CmdPolicyService",
	HandlerType: (*CmdPolicyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetCmdPolicy",
			Handler:    _CmdPolicyService_SetCmdPolicy_Handler,
		},
		{
			MethodName: "GetCmdPolicy",
			Handler:    _CmdPolicyService_GetCmdPolicy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-msg-express/channel-msg-express.proto",
}

func init() {
	proto.RegisterFile("channel-msg-express/channel-msg-express.proto", fileDescriptor_channel_msg_express_9ac40c0a3dab5133)
}

var fileDescriptor_channel_msg_express_9ac40c0a3dab5133 = []byte{
	// 1092 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0xdd, 0x72, 0xdb, 0x44,
	0x14, 0x8e, 0xe2, 0x38, 0xb1, 0x8f, 0xe3, 0xd4, 0xdd, 0x04, 0xe2, 0x38, 0x43, 0x6b, 0x04, 0xb4,
	0x6e, 0x21, 0x32, 0x35, 0xd3, 0x50, 0x18, 0x66, 0x18, 0x9a, 0x7a, 0x3c, 0x69, 0xf3, 0x37, 0x4a,
	0x42, 0x0b, 0x37, 0x1a, 0x55, 0xda, 0x38, 0x9a, 0xea, 0x67, 0xa3, 0x5d, 0x65, 0xe2, 0x1b, 0x1e,
	0x80, 0xd7, 0xe0, 0x82, 0x57, 0xe0, 0x19, 0x18, 0x6e, 0x78, 0x12, 0x5e, 0x81, 0xd9, 0x9f, 0x38,
	0x92, 0x2d, 0x37, 0x86, 0xdc, 0xe4, 0x6e, 0xf5, 0xed, 0xb7, 0xe7, 0xe7, 0xdb, 0x73, 0xce, 0xda,
	0xb0, 0xe1, 0x9c, 0xda, 0x61, 0x88, 0xfd, 0x8d, 0x80, 0xf6, 0x37, 0xf0, 0x05, 0x89, 0x31, 0xa5,
	0xed, 0x1c, 0xcc, 0x20, 0x71, 0xc4, 0x22, 0xb4, 0xac, 0xb6, 0xac, 0x80, 0xf6, 0x2d, 0xb5, 0xd5,
	0x40, 0x36, 0x21, 0x97, 0x67, 0x2c, 0x49, 0x6c, 0x18, 0x24, 0xa1, 0xa7, 0x1b, 0x61, 0xc4, 0xbc,
	0x13, 0xcf, 0xb1, 0x99, 0x17, 0x85, 0xed, 0xf3, 0x4e, 0x9b, 0x83, 0x69, 0xcc, 0x3a, 0xef, 0x48,
	0xbe, 0xfe, 0x7b, 0x01, 0xe0, 0x20, 0xa1, 0xa7, 0xfb, 0x84, 0xe3, 0xa8, 0x01, 0x25, 0x8a, 0xcf,
	0x12, 0x1c, 0x3a, 0xb8, 0xae, 0x35, 0xb5, 0x56, 0xd5, 0x1c, 0x7e, 0xa3, 0x2f, 0x00, 0x31, 0x1c,
	0x07, 0x5e, 0x68, 0xfb, 0x16, 0x1b, 0x10, 0x6c, 0xf9, 0x1e, 0x65, 0xf5, 0xd9, 0x66, 0xa1, 0x55,
	0x35, 0x6b, 0x97, 0x3b, 0x47, 0x03, 0x82, 0x77, 0x3c, 0xca, 0xd0, 0x8f, 0xb0, 0x92, 0x65, 0x93,
	0xc8, 0xf7, 0x9c, 0x41, 0xbd, 0xd0, 0xd4, 0x5a, 0x95, 0xce, 0xa7, 0x06, 0x77, 0xba, 0x97, 0x0a,
	0xc9, 0x38, 0x4a, 0x59, 0x38, 0x10, 0x5c, 0x13, 0xb1, 0x31, 0x0c, 0x6d, 0xc2, 0x2a, 0x89, 0xa3,
	0x8b, 0x81, 0x95, 0xc9, 0x87, 0x7b, 0xa8, 0xcf, 0x89, 0x80, 0x3f, 0x10, 0xdb, 0x69, 0xd3, 0xfc,
	0x34, 0x3a, 0x81, 0xb5, 0x9c, 0x73, 0x2a, 0xa8, 0x62, 0x53, 0x6b, 0x2d, 0x75, 0x1e, 0x8f, 0x07,
	0x75, 0x30, 0x6a, 0xcb, 0x50, 0xa1, 0xad, 0x8e, 0x79, 0x51, 0xf1, 0x6d, 0xc1, 0xbd, 0x1c, 0x3f,
	0xf8, 0x82, 0x78, 0x31, 0xb6, 0x98, 0x17, 0xe0, 0xfa, 0xbc, 0x08, 0x73, 0x7d, 0xcc, 0x40, 0x57,
	0x70, 0x8e, 0xbc, 0x00, 0x23, 0x04, 0x73, 0x74, 0x10, 0x3a, 0xf5, 0x85, 0xa6, 0xd6, 0x2a, 0x99,
	0x62, 0xad, 0xff, 0xa1, 0xc1, 0xdd, 0x43, 0x1c, 0xba, 0x5b, 0xf2, 0xc2, 0x77, 0x69, 0xdf, 0xc4,
	0x67, 0xa8, 0x09, 0x85, 0x80, 0xf6, 0xc5, 0x5d, 0x55, 0x3a, 0x4b, 0x46, 0xdf, 0x36, 0x52, 0xfb,
	0x7c, 0x0b, 0xdd, 0x87, 0x0a, 0xc5, 0xf1, 0x39, 0x8e, 0xad, 0xd0, 0x0e, 0x70, 0x7d, 0xb6, 0xa9,
	0xb5, 0xca, 0x26, 0x48, 0x68, 0xcf, 0x0e, 0x30, 0xfa, 0x0e, 0xca, 0xbc, 0x3e, 0xac, 0x88, 0x30,
	0xaa, 0xae, 0xe7, 0xbe, 0x91, 0x53, 0x6f, 0xc6, 0x55, 0x9d, 0x98, 0x25, 0x22, 0xd7, 0x14, 0xe9,
	0x50, 0xa5, 0xef, 0x3c, 0x62, 0x25, 0x9e, 0x2b, 0x0b, 0x62, 0x4e, 0x14, 0x44, 0x85, 0x83, 0xc7,
	0x9e, 0xcb, 0x6b, 0x41, 0xdf, 0x07, 0x34, 0x1a, 0x39, 0x25, 0xef, 0xad, 0xb5, 0x8f, 0x00, 0x62,
	0xbe, 0xa6, 0xcc, 0xf2, 0x5c, 0x15, 0x73, 0x59, 0x21, 0xdb, 0xae, 0xfe, 0xa7, 0x06, 0x8d, 0x94,
	0xc5, 0xe7, 0x71, 0x64, 0xbb, 0x8e, 0x4d, 0x99, 0x12, 0xe5, 0x51, 0x5a, 0x94, 0xd5, 0x94, 0x28,
	0x19, 0xe2, 0x6d, 0x51, 0xe7, 0x0d, 0xac, 0x4f, 0xcc, 0xe5, 0x66, 0x32, 0xfd, 0xad, 0xc1, 0x12,
	0x0f, 0xeb, 0x28, 0x3a, 0xa6, 0x38, 0xa6, 0xb7, 0x4b, 0x9a, 0x35, 0x28, 0x8d, 0xa8, 0xb2, 0x90,
	0x48, 0x45, 0x32, 0x29, 0x17, 0xb3, 0x29, 0xeb, 0xaf, 0xb2, 0x29, 0xdd, 0x4c, 0xa0, 0xdf, 0x66,
	0xa1, 0x76, 0x65, 0x6d, 0xd7, 0x26, 0xb7, 0x4b, 0xa2, 0x97, 0xb0, 0x40, 0xf1, 0x99, 0x15, 0xd8,
	0x44, 0x28, 0x54, 0xe9, 0x3c, 0x99, 0x78, 0x36, 0x9d, 0x81, 0x71, 0x88, 0xcf, 0x76, 0x6d, 0xd2,
	0x0d, 0x59, 0x3c, 0x30, 0xe7, 0xa9, 0xf8, 0x68, 0x7c, 0x03, 0x95, 0x14, 0x8c, 0x6a, 0x50, 0x78,
	0x87, 0x07, 0x4a, 0x2f, 0xbe, 0x44, 0x2b, 0x50, 0x3c, 0xb7, 0xfd, 0x44, 0x66, 0x51, 0x35, 0xe5,
	0xc7, 0xb7, 0xb3, 0xcf, 0x34, 0xfd, 0xc9, 0xa8, 0x48, 0x94, 0x8c, 0x08, 0xab, 0x8d, 0x0a, 0xfb,
	0x8f, 0x06, 0xd5, 0x17, 0x09, 0xf1, 0xf9, 0x68, 0xc3, 0x66, 0xe2, 0x63, 0xb4, 0x09, 0xf3, 0x6a,
	0xd8, 0x4a, 0x61, 0xef, 0xe5, 0xa6, 0xb2, 0x15, 0xb8, 0x6a, 0xc0, 0x2a, 0x36, 0x7a, 0x00, 0x77,
	0x4e, 0xe2, 0x28, 0xb0, 0x2e, 0xd9, 0xea, 0x1a, 0xab, 0x66, 0x95, 0xc3, 0xea, 0x66, 0xb6, 0x5d,
	0xf4, 0x39, 0x20, 0x16, 0xa5, 0x58, 0xb2, 0xb0, 0x0a, 0xa2, 0xb0, 0xee, 0xb0, 0x68, 0x48, 0x14,
	0x05, 0xb6, 0x0e, 0x65, 0x35, 0x91, 0x6d, 0x26, 0x9e, 0x8d, 0x82, 0x59, 0x92, 0xc0, 0x0f, 0x0c,
	0xad, 0xc2, 0x42, 0x9c, 0xf8, 0x98, 0x7b, 0x2a, 0x8a, 0xbc, 0xe6, 0xf9, 0xe7, 0xb6, 0xcb, 0x4f,
	0x89, 0x0d, 0xf1, 0xd8, 0xc8, 0x29, 0x5e, 0xe2, 0x00, 0x7f, 0x5f, 0xf4, 0x7d, 0x58, 0x3e, 0xc4,
	0x2c, 0x93, 0x33, 0x2f, 0xa6, 0x67, 0x50, 0xe4, 0x14, 0x5a, 0xd7, 0xc4, 0x05, 0xea, 0xb9, 0x59,
	0x67, 0x4f, 0xc9, 0x03, 0xfa, 0x87, 0xb0, 0x32, 0x6e, 0x90, 0x12, 0xfd, 0x21, 0x2c, 0xf7, 0x72,
	0x1c, 0xd5, 0xa0, 0xe0, 0xb9, 0xd2, 0x4d, 0xd9, 0xe4, 0x4b, 0xfd, 0x00, 0x56, 0x7a, 0x39, 0x06,
	0x6e, 0x10, 0xd2, 0x3e, 0x94, 0x87, 0x17, 0x84, 0x3e, 0x86, 0x45, 0x79, 0x45, 0x56, 0x3f, 0x8e,
	0x12, 0xa2, 0x6a, 0xa0, 0x22, 0xb1, 0x1e, 0x87, 0x78, 0x7b, 0x28, 0x4a, 0xba, 0x3d, 0x24, 0xc4,
	0xdb, 0x43, 0x7f, 0x0b, 0xd5, 0xa1, 0xc1, 0xed, 0xf0, 0x24, 0xfa, 0xdf, 0x55, 0xb2, 0x06, 0x25,
	0x27, 0x70, 0xd3, 0xbf, 0x48, 0x16, 0x9c, 0x40, 0x8e, 0x57, 0xa9, 0x63, 0xc6, 0x8d, 0xd0, 0xf1,
	0x25, 0xd4, 0x7a, 0x29, 0x5c, 0x48, 0xb3, 0x09, 0x73, 0x5e, 0x78, 0x12, 0x29, 0xe7, 0xfa, 0xfb,
	0x9d, 0x0b, 0x4b, 0x82, 0xff, 0xf8, 0x7b, 0x28, 0x77, 0x4d, 0x55, 0x09, 0xe8, 0x0e, 0x54, 0xcc,
	0xe3, 0x9d, 0xae, 0x75, 0xbc, 0xf7, 0x6a, 0x6f, 0xff, 0x75, 0x6d, 0x66, 0x08, 0xbc, 0x38, 0x3e,
	0xd8, 0xe9, 0xbe, 0xa9, 0x69, 0xa8, 0x06, 0x8b, 0x02, 0x38, 0xdc, 0xde, 0x15, 0xc8, 0x6c, 0xe7,
	0xd7, 0x22, 0xdc, 0xbd, 0x7a, 0x1e, 0xbb, 0xd2, 0x15, 0x72, 0x60, 0x29, 0xfb, 0x6e, 0xa2, 0x07,
	0xb9, 0x21, 0x8d, 0xfd, 0x2c, 0x68, 0x3c, 0x9c, 0x8a, 0x47, 0x89, 0x3e, 0x83, 0x7e, 0x81, 0xd5,
	0x09, 0xcf, 0x0f, 0x6a, 0x5f, 0x67, 0x65, 0xe4, 0xe1, 0x6d, 0x7c, 0xf9, 0xdf, 0x0e, 0x08, 0xff,
	0x3f, 0x41, 0x25, 0x35, 0xd0, 0xd1, 0x27, 0xd7, 0x8c, 0x38, 0xfe, 0x8a, 0x35, 0xae, 0x27, 0x09,
	0xd3, 0x16, 0x54, 0x33, 0x83, 0x0b, 0x7d, 0x36, 0xd5, 0xfc, 0x6c, 0x4c, 0x43, 0x13, 0x0e, 0x3c,
	0xa8, 0x8d, 0xf6, 0x28, 0x6a, 0x4d, 0xd0, 0x60, 0xac, 0x65, 0x1b, 0x8f, 0xa6, 0x64, 0x5e, 0xba,
	0xea, 0x4d, 0xe7, 0xaa, 0x37, 0xb5, 0xab, 0x5e, 0xae, 0xab, 0xce, 0x5f, 0x1a, 0xd4, 0x86, 0x55,
	0x7e, 0x88, 0xe3, 0x73, 0xcf, 0xc1, 0xc8, 0x82, 0xc5, 0x74, 0x1b, 0xa1, 0x29, 0x9a, 0x63, 0x72,
	0x82, 0xe3, 0xdd, 0x38, 0x83, 0x5e, 0xc3, 0x62, 0xba, 0x1f, 0xd1, 0x35, 0xad, 0x3f, 0xe1, 0x92,
	0x46, 0x5b, 0x5a, 0x9f, 0x79, 0xfe, 0xf5, 0xcf, 0x4f, 0xfb, 0x91, 0x6f, 0x87, 0x7d, 0xe3, 0x69,
	0x87, 0x31, 0xc3, 0x89, 0x82, 0xb6, 0xf8, 0xef, 0xe3, 0x44, 0x7e, 0x9b, 0xca, 0xec, 0x72, 0xff,
	0x7a, 0xbd, 0x9d, 0x17, 0xb4, 0xaf, 0xfe, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x16, 0x19, 0x9b, 0x4a,
	0xac, 0x0d, 0x00, 0x00,
}
