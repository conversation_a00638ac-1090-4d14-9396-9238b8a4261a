// Code generated by protoc-gen-go. DO NOT EDIT.
// source: es-gateway/es-gateway.proto

package es_gateway // import "golang.52tt.com/protocol/services/es-gateway"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SearchReq struct {
	Index                string   `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Body                 string   `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	Offset               int64    `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                int64    `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchReq) Reset()         { *m = SearchReq{} }
func (m *SearchReq) String() string { return proto.CompactTextString(m) }
func (*SearchReq) ProtoMessage()    {}
func (*SearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{0}
}
func (m *SearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchReq.Unmarshal(m, b)
}
func (m *SearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchReq.Marshal(b, m, deterministic)
}
func (dst *SearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchReq.Merge(dst, src)
}
func (m *SearchReq) XXX_Size() int {
	return xxx_messageInfo_SearchReq.Size(m)
}
func (m *SearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchReq proto.InternalMessageInfo

func (m *SearchReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *SearchReq) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

func (m *SearchReq) GetOffset() int64 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchReq) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type SearchResp struct {
	Data                 []byte   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchResp) Reset()         { *m = SearchResp{} }
func (m *SearchResp) String() string { return proto.CompactTextString(m) }
func (*SearchResp) ProtoMessage()    {}
func (*SearchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{1}
}
func (m *SearchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchResp.Unmarshal(m, b)
}
func (m *SearchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchResp.Marshal(b, m, deterministic)
}
func (dst *SearchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchResp.Merge(dst, src)
}
func (m *SearchResp) XXX_Size() int {
	return xxx_messageInfo_SearchResp.Size(m)
}
func (m *SearchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchResp proto.InternalMessageInfo

func (m *SearchResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type CreateReq struct {
	Index                string   `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	DocId                string   `protobuf:"bytes,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	Body                 string   `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateReq) Reset()         { *m = CreateReq{} }
func (m *CreateReq) String() string { return proto.CompactTextString(m) }
func (*CreateReq) ProtoMessage()    {}
func (*CreateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{2}
}
func (m *CreateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateReq.Unmarshal(m, b)
}
func (m *CreateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateReq.Marshal(b, m, deterministic)
}
func (dst *CreateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateReq.Merge(dst, src)
}
func (m *CreateReq) XXX_Size() int {
	return xxx_messageInfo_CreateReq.Size(m)
}
func (m *CreateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateReq proto.InternalMessageInfo

func (m *CreateReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *CreateReq) GetDocId() string {
	if m != nil {
		return m.DocId
	}
	return ""
}

func (m *CreateReq) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

type CreateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateResp) Reset()         { *m = CreateResp{} }
func (m *CreateResp) String() string { return proto.CompactTextString(m) }
func (*CreateResp) ProtoMessage()    {}
func (*CreateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{3}
}
func (m *CreateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateResp.Unmarshal(m, b)
}
func (m *CreateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateResp.Marshal(b, m, deterministic)
}
func (dst *CreateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateResp.Merge(dst, src)
}
func (m *CreateResp) XXX_Size() int {
	return xxx_messageInfo_CreateResp.Size(m)
}
func (m *CreateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateResp proto.InternalMessageInfo

type DeleteReq struct {
	Index                string   `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	DocId                string   `protobuf:"bytes,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteReq) Reset()         { *m = DeleteReq{} }
func (m *DeleteReq) String() string { return proto.CompactTextString(m) }
func (*DeleteReq) ProtoMessage()    {}
func (*DeleteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{4}
}
func (m *DeleteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteReq.Unmarshal(m, b)
}
func (m *DeleteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteReq.Marshal(b, m, deterministic)
}
func (dst *DeleteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteReq.Merge(dst, src)
}
func (m *DeleteReq) XXX_Size() int {
	return xxx_messageInfo_DeleteReq.Size(m)
}
func (m *DeleteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteReq proto.InternalMessageInfo

func (m *DeleteReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *DeleteReq) GetDocId() string {
	if m != nil {
		return m.DocId
	}
	return ""
}

type DeleteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteResp) Reset()         { *m = DeleteResp{} }
func (m *DeleteResp) String() string { return proto.CompactTextString(m) }
func (*DeleteResp) ProtoMessage()    {}
func (*DeleteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{5}
}
func (m *DeleteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteResp.Unmarshal(m, b)
}
func (m *DeleteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteResp.Marshal(b, m, deterministic)
}
func (dst *DeleteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteResp.Merge(dst, src)
}
func (m *DeleteResp) XXX_Size() int {
	return xxx_messageInfo_DeleteResp.Size(m)
}
func (m *DeleteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteResp proto.InternalMessageInfo

type IndexReq struct {
	Index                string   `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	DocId                string   `protobuf:"bytes,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	Body                 string   `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexReq) Reset()         { *m = IndexReq{} }
func (m *IndexReq) String() string { return proto.CompactTextString(m) }
func (*IndexReq) ProtoMessage()    {}
func (*IndexReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{6}
}
func (m *IndexReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexReq.Unmarshal(m, b)
}
func (m *IndexReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexReq.Marshal(b, m, deterministic)
}
func (dst *IndexReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexReq.Merge(dst, src)
}
func (m *IndexReq) XXX_Size() int {
	return xxx_messageInfo_IndexReq.Size(m)
}
func (m *IndexReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexReq.DiscardUnknown(m)
}

var xxx_messageInfo_IndexReq proto.InternalMessageInfo

func (m *IndexReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *IndexReq) GetDocId() string {
	if m != nil {
		return m.DocId
	}
	return ""
}

func (m *IndexReq) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

type IndexResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexResp) Reset()         { *m = IndexResp{} }
func (m *IndexResp) String() string { return proto.CompactTextString(m) }
func (*IndexResp) ProtoMessage()    {}
func (*IndexResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{7}
}
func (m *IndexResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexResp.Unmarshal(m, b)
}
func (m *IndexResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexResp.Marshal(b, m, deterministic)
}
func (dst *IndexResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexResp.Merge(dst, src)
}
func (m *IndexResp) XXX_Size() int {
	return xxx_messageInfo_IndexResp.Size(m)
}
func (m *IndexResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexResp.DiscardUnknown(m)
}

var xxx_messageInfo_IndexResp proto.InternalMessageInfo

type CountReq struct {
	Index                string   `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Body                 string   `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountReq) Reset()         { *m = CountReq{} }
func (m *CountReq) String() string { return proto.CompactTextString(m) }
func (*CountReq) ProtoMessage()    {}
func (*CountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{8}
}
func (m *CountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountReq.Unmarshal(m, b)
}
func (m *CountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountReq.Marshal(b, m, deterministic)
}
func (dst *CountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountReq.Merge(dst, src)
}
func (m *CountReq) XXX_Size() int {
	return xxx_messageInfo_CountReq.Size(m)
}
func (m *CountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CountReq.DiscardUnknown(m)
}

var xxx_messageInfo_CountReq proto.InternalMessageInfo

func (m *CountReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *CountReq) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

type CountResp struct {
	Data                 []byte   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountResp) Reset()         { *m = CountResp{} }
func (m *CountResp) String() string { return proto.CompactTextString(m) }
func (*CountResp) ProtoMessage()    {}
func (*CountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{9}
}
func (m *CountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountResp.Unmarshal(m, b)
}
func (m *CountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountResp.Marshal(b, m, deterministic)
}
func (dst *CountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountResp.Merge(dst, src)
}
func (m *CountResp) XXX_Size() int {
	return xxx_messageInfo_CountResp.Size(m)
}
func (m *CountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CountResp.DiscardUnknown(m)
}

var xxx_messageInfo_CountResp proto.InternalMessageInfo

func (m *CountResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetByIdReq struct {
	Index                string   `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	DocId                string   `protobuf:"bytes,2,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetByIdReq) Reset()         { *m = GetByIdReq{} }
func (m *GetByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetByIdReq) ProtoMessage()    {}
func (*GetByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{10}
}
func (m *GetByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetByIdReq.Unmarshal(m, b)
}
func (m *GetByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetByIdReq.Merge(dst, src)
}
func (m *GetByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetByIdReq.Size(m)
}
func (m *GetByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetByIdReq proto.InternalMessageInfo

func (m *GetByIdReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *GetByIdReq) GetDocId() string {
	if m != nil {
		return m.DocId
	}
	return ""
}

type GetByIdResp struct {
	Data                 []byte   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetByIdResp) Reset()         { *m = GetByIdResp{} }
func (m *GetByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetByIdResp) ProtoMessage()    {}
func (*GetByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{11}
}
func (m *GetByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetByIdResp.Unmarshal(m, b)
}
func (m *GetByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetByIdResp.Merge(dst, src)
}
func (m *GetByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetByIdResp.Size(m)
}
func (m *GetByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetByIdResp proto.InternalMessageInfo

func (m *GetByIdResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type BulkReq struct {
	Index                string   `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Body                 []byte   `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BulkReq) Reset()         { *m = BulkReq{} }
func (m *BulkReq) String() string { return proto.CompactTextString(m) }
func (*BulkReq) ProtoMessage()    {}
func (*BulkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{12}
}
func (m *BulkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BulkReq.Unmarshal(m, b)
}
func (m *BulkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BulkReq.Marshal(b, m, deterministic)
}
func (dst *BulkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BulkReq.Merge(dst, src)
}
func (m *BulkReq) XXX_Size() int {
	return xxx_messageInfo_BulkReq.Size(m)
}
func (m *BulkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BulkReq.DiscardUnknown(m)
}

var xxx_messageInfo_BulkReq proto.InternalMessageInfo

func (m *BulkReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *BulkReq) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

type BulkResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BulkResp) Reset()         { *m = BulkResp{} }
func (m *BulkResp) String() string { return proto.CompactTextString(m) }
func (*BulkResp) ProtoMessage()    {}
func (*BulkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{13}
}
func (m *BulkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BulkResp.Unmarshal(m, b)
}
func (m *BulkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BulkResp.Marshal(b, m, deterministic)
}
func (dst *BulkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BulkResp.Merge(dst, src)
}
func (m *BulkResp) XXX_Size() int {
	return xxx_messageInfo_BulkResp.Size(m)
}
func (m *BulkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BulkResp.DiscardUnknown(m)
}

var xxx_messageInfo_BulkResp proto.InternalMessageInfo

type DeleteByQueryReq struct {
	Index                string   `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Body                 string   `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteByQueryReq) Reset()         { *m = DeleteByQueryReq{} }
func (m *DeleteByQueryReq) String() string { return proto.CompactTextString(m) }
func (*DeleteByQueryReq) ProtoMessage()    {}
func (*DeleteByQueryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{14}
}
func (m *DeleteByQueryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteByQueryReq.Unmarshal(m, b)
}
func (m *DeleteByQueryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteByQueryReq.Marshal(b, m, deterministic)
}
func (dst *DeleteByQueryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteByQueryReq.Merge(dst, src)
}
func (m *DeleteByQueryReq) XXX_Size() int {
	return xxx_messageInfo_DeleteByQueryReq.Size(m)
}
func (m *DeleteByQueryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteByQueryReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteByQueryReq proto.InternalMessageInfo

func (m *DeleteByQueryReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *DeleteByQueryReq) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

type DeleteByQueryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteByQueryResp) Reset()         { *m = DeleteByQueryResp{} }
func (m *DeleteByQueryResp) String() string { return proto.CompactTextString(m) }
func (*DeleteByQueryResp) ProtoMessage()    {}
func (*DeleteByQueryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{15}
}
func (m *DeleteByQueryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteByQueryResp.Unmarshal(m, b)
}
func (m *DeleteByQueryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteByQueryResp.Marshal(b, m, deterministic)
}
func (dst *DeleteByQueryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteByQueryResp.Merge(dst, src)
}
func (m *DeleteByQueryResp) XXX_Size() int {
	return xxx_messageInfo_DeleteByQueryResp.Size(m)
}
func (m *DeleteByQueryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteByQueryResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteByQueryResp proto.InternalMessageInfo

type AnalyzeReq struct {
	Index                string   `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Body                 string   `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnalyzeReq) Reset()         { *m = AnalyzeReq{} }
func (m *AnalyzeReq) String() string { return proto.CompactTextString(m) }
func (*AnalyzeReq) ProtoMessage()    {}
func (*AnalyzeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{16}
}
func (m *AnalyzeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnalyzeReq.Unmarshal(m, b)
}
func (m *AnalyzeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnalyzeReq.Marshal(b, m, deterministic)
}
func (dst *AnalyzeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnalyzeReq.Merge(dst, src)
}
func (m *AnalyzeReq) XXX_Size() int {
	return xxx_messageInfo_AnalyzeReq.Size(m)
}
func (m *AnalyzeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AnalyzeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AnalyzeReq proto.InternalMessageInfo

func (m *AnalyzeReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *AnalyzeReq) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

type AnalyzeResp struct {
	Data                 []byte   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnalyzeResp) Reset()         { *m = AnalyzeResp{} }
func (m *AnalyzeResp) String() string { return proto.CompactTextString(m) }
func (*AnalyzeResp) ProtoMessage()    {}
func (*AnalyzeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{17}
}
func (m *AnalyzeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnalyzeResp.Unmarshal(m, b)
}
func (m *AnalyzeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnalyzeResp.Marshal(b, m, deterministic)
}
func (dst *AnalyzeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnalyzeResp.Merge(dst, src)
}
func (m *AnalyzeResp) XXX_Size() int {
	return xxx_messageInfo_AnalyzeResp.Size(m)
}
func (m *AnalyzeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AnalyzeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AnalyzeResp proto.InternalMessageInfo

func (m *AnalyzeResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

// 深度分页的场景下使用，导出数据
// 调用顺序 SearchDeep -> SearchScroll... -> DeleteScroll
type SearchDeepReq struct {
	Index                string   `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Body                 string   `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchDeepReq) Reset()         { *m = SearchDeepReq{} }
func (m *SearchDeepReq) String() string { return proto.CompactTextString(m) }
func (*SearchDeepReq) ProtoMessage()    {}
func (*SearchDeepReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{18}
}
func (m *SearchDeepReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchDeepReq.Unmarshal(m, b)
}
func (m *SearchDeepReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchDeepReq.Marshal(b, m, deterministic)
}
func (dst *SearchDeepReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchDeepReq.Merge(dst, src)
}
func (m *SearchDeepReq) XXX_Size() int {
	return xxx_messageInfo_SearchDeepReq.Size(m)
}
func (m *SearchDeepReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchDeepReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchDeepReq proto.InternalMessageInfo

func (m *SearchDeepReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *SearchDeepReq) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

func (m *SearchDeepReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type SearchDeepResp struct {
	Data                 []byte   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchDeepResp) Reset()         { *m = SearchDeepResp{} }
func (m *SearchDeepResp) String() string { return proto.CompactTextString(m) }
func (*SearchDeepResp) ProtoMessage()    {}
func (*SearchDeepResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{19}
}
func (m *SearchDeepResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchDeepResp.Unmarshal(m, b)
}
func (m *SearchDeepResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchDeepResp.Marshal(b, m, deterministic)
}
func (dst *SearchDeepResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchDeepResp.Merge(dst, src)
}
func (m *SearchDeepResp) XXX_Size() int {
	return xxx_messageInfo_SearchDeepResp.Size(m)
}
func (m *SearchDeepResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchDeepResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchDeepResp proto.InternalMessageInfo

func (m *SearchDeepResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type SearchScrollReq struct {
	Body                 string   `protobuf:"bytes,1,opt,name=body,proto3" json:"body,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchScrollReq) Reset()         { *m = SearchScrollReq{} }
func (m *SearchScrollReq) String() string { return proto.CompactTextString(m) }
func (*SearchScrollReq) ProtoMessage()    {}
func (*SearchScrollReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{20}
}
func (m *SearchScrollReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchScrollReq.Unmarshal(m, b)
}
func (m *SearchScrollReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchScrollReq.Marshal(b, m, deterministic)
}
func (dst *SearchScrollReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchScrollReq.Merge(dst, src)
}
func (m *SearchScrollReq) XXX_Size() int {
	return xxx_messageInfo_SearchScrollReq.Size(m)
}
func (m *SearchScrollReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchScrollReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchScrollReq proto.InternalMessageInfo

func (m *SearchScrollReq) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

type SearchScrollResp struct {
	Data                 []byte   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchScrollResp) Reset()         { *m = SearchScrollResp{} }
func (m *SearchScrollResp) String() string { return proto.CompactTextString(m) }
func (*SearchScrollResp) ProtoMessage()    {}
func (*SearchScrollResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{21}
}
func (m *SearchScrollResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchScrollResp.Unmarshal(m, b)
}
func (m *SearchScrollResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchScrollResp.Marshal(b, m, deterministic)
}
func (dst *SearchScrollResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchScrollResp.Merge(dst, src)
}
func (m *SearchScrollResp) XXX_Size() int {
	return xxx_messageInfo_SearchScrollResp.Size(m)
}
func (m *SearchScrollResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchScrollResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchScrollResp proto.InternalMessageInfo

func (m *SearchScrollResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type DeleteScrollReq struct {
	Scroll               string   `protobuf:"bytes,1,opt,name=scroll,proto3" json:"scroll,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteScrollReq) Reset()         { *m = DeleteScrollReq{} }
func (m *DeleteScrollReq) String() string { return proto.CompactTextString(m) }
func (*DeleteScrollReq) ProtoMessage()    {}
func (*DeleteScrollReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{22}
}
func (m *DeleteScrollReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteScrollReq.Unmarshal(m, b)
}
func (m *DeleteScrollReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteScrollReq.Marshal(b, m, deterministic)
}
func (dst *DeleteScrollReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteScrollReq.Merge(dst, src)
}
func (m *DeleteScrollReq) XXX_Size() int {
	return xxx_messageInfo_DeleteScrollReq.Size(m)
}
func (m *DeleteScrollReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteScrollReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteScrollReq proto.InternalMessageInfo

func (m *DeleteScrollReq) GetScroll() string {
	if m != nil {
		return m.Scroll
	}
	return ""
}

type DeleteScrollResp struct {
	Data                 []byte   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteScrollResp) Reset()         { *m = DeleteScrollResp{} }
func (m *DeleteScrollResp) String() string { return proto.CompactTextString(m) }
func (*DeleteScrollResp) ProtoMessage()    {}
func (*DeleteScrollResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_es_gateway_1de5174f03452eb0, []int{23}
}
func (m *DeleteScrollResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteScrollResp.Unmarshal(m, b)
}
func (m *DeleteScrollResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteScrollResp.Marshal(b, m, deterministic)
}
func (dst *DeleteScrollResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteScrollResp.Merge(dst, src)
}
func (m *DeleteScrollResp) XXX_Size() int {
	return xxx_messageInfo_DeleteScrollResp.Size(m)
}
func (m *DeleteScrollResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteScrollResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteScrollResp proto.InternalMessageInfo

func (m *DeleteScrollResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterType((*SearchReq)(nil), "es_gateway.SearchReq")
	proto.RegisterType((*SearchResp)(nil), "es_gateway.SearchResp")
	proto.RegisterType((*CreateReq)(nil), "es_gateway.CreateReq")
	proto.RegisterType((*CreateResp)(nil), "es_gateway.CreateResp")
	proto.RegisterType((*DeleteReq)(nil), "es_gateway.DeleteReq")
	proto.RegisterType((*DeleteResp)(nil), "es_gateway.DeleteResp")
	proto.RegisterType((*IndexReq)(nil), "es_gateway.IndexReq")
	proto.RegisterType((*IndexResp)(nil), "es_gateway.IndexResp")
	proto.RegisterType((*CountReq)(nil), "es_gateway.CountReq")
	proto.RegisterType((*CountResp)(nil), "es_gateway.CountResp")
	proto.RegisterType((*GetByIdReq)(nil), "es_gateway.GetByIdReq")
	proto.RegisterType((*GetByIdResp)(nil), "es_gateway.GetByIdResp")
	proto.RegisterType((*BulkReq)(nil), "es_gateway.BulkReq")
	proto.RegisterType((*BulkResp)(nil), "es_gateway.BulkResp")
	proto.RegisterType((*DeleteByQueryReq)(nil), "es_gateway.DeleteByQueryReq")
	proto.RegisterType((*DeleteByQueryResp)(nil), "es_gateway.DeleteByQueryResp")
	proto.RegisterType((*AnalyzeReq)(nil), "es_gateway.AnalyzeReq")
	proto.RegisterType((*AnalyzeResp)(nil), "es_gateway.AnalyzeResp")
	proto.RegisterType((*SearchDeepReq)(nil), "es_gateway.SearchDeepReq")
	proto.RegisterType((*SearchDeepResp)(nil), "es_gateway.SearchDeepResp")
	proto.RegisterType((*SearchScrollReq)(nil), "es_gateway.SearchScrollReq")
	proto.RegisterType((*SearchScrollResp)(nil), "es_gateway.SearchScrollResp")
	proto.RegisterType((*DeleteScrollReq)(nil), "es_gateway.DeleteScrollReq")
	proto.RegisterType((*DeleteScrollResp)(nil), "es_gateway.DeleteScrollResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EsGatewayClient is the client API for EsGateway service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EsGatewayClient interface {
	Search(ctx context.Context, in *SearchReq, opts ...grpc.CallOption) (*SearchResp, error)
	Create(ctx context.Context, in *CreateReq, opts ...grpc.CallOption) (*CreateResp, error)
	Delete(ctx context.Context, in *DeleteReq, opts ...grpc.CallOption) (*DeleteResp, error)
	Index(ctx context.Context, in *IndexReq, opts ...grpc.CallOption) (*IndexResp, error)
	Count(ctx context.Context, in *CountReq, opts ...grpc.CallOption) (*CountResp, error)
	GetById(ctx context.Context, in *GetByIdReq, opts ...grpc.CallOption) (*GetByIdResp, error)
	Bulk(ctx context.Context, in *BulkReq, opts ...grpc.CallOption) (*BulkResp, error)
	DeleteByQuery(ctx context.Context, in *DeleteByQueryReq, opts ...grpc.CallOption) (*DeleteByQueryResp, error)
	Analyze(ctx context.Context, in *AnalyzeReq, opts ...grpc.CallOption) (*AnalyzeResp, error)
	SearchDeep(ctx context.Context, in *SearchDeepReq, opts ...grpc.CallOption) (*SearchDeepResp, error)
	SearchScroll(ctx context.Context, in *SearchScrollReq, opts ...grpc.CallOption) (*SearchScrollResp, error)
	DeleteScroll(ctx context.Context, in *DeleteScrollReq, opts ...grpc.CallOption) (*DeleteScrollResp, error)
}

type esGatewayClient struct {
	cc *grpc.ClientConn
}

func NewEsGatewayClient(cc *grpc.ClientConn) EsGatewayClient {
	return &esGatewayClient{cc}
}

func (c *esGatewayClient) Search(ctx context.Context, in *SearchReq, opts ...grpc.CallOption) (*SearchResp, error) {
	out := new(SearchResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/Search", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) Create(ctx context.Context, in *CreateReq, opts ...grpc.CallOption) (*CreateResp, error) {
	out := new(CreateResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/Create", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) Delete(ctx context.Context, in *DeleteReq, opts ...grpc.CallOption) (*DeleteResp, error) {
	out := new(DeleteResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) Index(ctx context.Context, in *IndexReq, opts ...grpc.CallOption) (*IndexResp, error) {
	out := new(IndexResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/Index", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) Count(ctx context.Context, in *CountReq, opts ...grpc.CallOption) (*CountResp, error) {
	out := new(CountResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/Count", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) GetById(ctx context.Context, in *GetByIdReq, opts ...grpc.CallOption) (*GetByIdResp, error) {
	out := new(GetByIdResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/GetById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) Bulk(ctx context.Context, in *BulkReq, opts ...grpc.CallOption) (*BulkResp, error) {
	out := new(BulkResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/Bulk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) DeleteByQuery(ctx context.Context, in *DeleteByQueryReq, opts ...grpc.CallOption) (*DeleteByQueryResp, error) {
	out := new(DeleteByQueryResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/DeleteByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) Analyze(ctx context.Context, in *AnalyzeReq, opts ...grpc.CallOption) (*AnalyzeResp, error) {
	out := new(AnalyzeResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/Analyze", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) SearchDeep(ctx context.Context, in *SearchDeepReq, opts ...grpc.CallOption) (*SearchDeepResp, error) {
	out := new(SearchDeepResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/SearchDeep", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) SearchScroll(ctx context.Context, in *SearchScrollReq, opts ...grpc.CallOption) (*SearchScrollResp, error) {
	out := new(SearchScrollResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/SearchScroll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esGatewayClient) DeleteScroll(ctx context.Context, in *DeleteScrollReq, opts ...grpc.CallOption) (*DeleteScrollResp, error) {
	out := new(DeleteScrollResp)
	err := c.cc.Invoke(ctx, "/es_gateway.EsGateway/DeleteScroll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EsGatewayServer is the server API for EsGateway service.
type EsGatewayServer interface {
	Search(context.Context, *SearchReq) (*SearchResp, error)
	Create(context.Context, *CreateReq) (*CreateResp, error)
	Delete(context.Context, *DeleteReq) (*DeleteResp, error)
	Index(context.Context, *IndexReq) (*IndexResp, error)
	Count(context.Context, *CountReq) (*CountResp, error)
	GetById(context.Context, *GetByIdReq) (*GetByIdResp, error)
	Bulk(context.Context, *BulkReq) (*BulkResp, error)
	DeleteByQuery(context.Context, *DeleteByQueryReq) (*DeleteByQueryResp, error)
	Analyze(context.Context, *AnalyzeReq) (*AnalyzeResp, error)
	SearchDeep(context.Context, *SearchDeepReq) (*SearchDeepResp, error)
	SearchScroll(context.Context, *SearchScrollReq) (*SearchScrollResp, error)
	DeleteScroll(context.Context, *DeleteScrollReq) (*DeleteScrollResp, error)
}

func RegisterEsGatewayServer(s *grpc.Server, srv EsGatewayServer) {
	s.RegisterService(&_EsGateway_serviceDesc, srv)
}

func _EsGateway_Search_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).Search(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/Search",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).Search(ctx, req.(*SearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/Create",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).Create(ctx, req.(*CreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).Delete(ctx, req.(*DeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_Index_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).Index(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/Index",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).Index(ctx, req.(*IndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_Count_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).Count(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/Count",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).Count(ctx, req.(*CountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_GetById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).GetById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/GetById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).GetById(ctx, req.(*GetByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_Bulk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).Bulk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/Bulk",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).Bulk(ctx, req.(*BulkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_DeleteByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteByQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).DeleteByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/DeleteByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).DeleteByQuery(ctx, req.(*DeleteByQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_Analyze_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnalyzeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).Analyze(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/Analyze",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).Analyze(ctx, req.(*AnalyzeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_SearchDeep_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchDeepReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).SearchDeep(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/SearchDeep",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).SearchDeep(ctx, req.(*SearchDeepReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_SearchScroll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchScrollReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).SearchScroll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/SearchScroll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).SearchScroll(ctx, req.(*SearchScrollReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsGateway_DeleteScroll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteScrollReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsGatewayServer).DeleteScroll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/es_gateway.EsGateway/DeleteScroll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsGatewayServer).DeleteScroll(ctx, req.(*DeleteScrollReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _EsGateway_serviceDesc = grpc.ServiceDesc{
	ServiceName: "es_gateway.EsGateway",
	HandlerType: (*EsGatewayServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Search",
			Handler:    _EsGateway_Search_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _EsGateway_Create_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _EsGateway_Delete_Handler,
		},
		{
			MethodName: "Index",
			Handler:    _EsGateway_Index_Handler,
		},
		{
			MethodName: "Count",
			Handler:    _EsGateway_Count_Handler,
		},
		{
			MethodName: "GetById",
			Handler:    _EsGateway_GetById_Handler,
		},
		{
			MethodName: "Bulk",
			Handler:    _EsGateway_Bulk_Handler,
		},
		{
			MethodName: "DeleteByQuery",
			Handler:    _EsGateway_DeleteByQuery_Handler,
		},
		{
			MethodName: "Analyze",
			Handler:    _EsGateway_Analyze_Handler,
		},
		{
			MethodName: "SearchDeep",
			Handler:    _EsGateway_SearchDeep_Handler,
		},
		{
			MethodName: "SearchScroll",
			Handler:    _EsGateway_SearchScroll_Handler,
		},
		{
			MethodName: "DeleteScroll",
			Handler:    _EsGateway_DeleteScroll_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "es-gateway/es-gateway.proto",
}

func init() {
	proto.RegisterFile("es-gateway/es-gateway.proto", fileDescriptor_es_gateway_1de5174f03452eb0)
}

var fileDescriptor_es_gateway_1de5174f03452eb0 = []byte{
	// 620 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x95, 0xdf, 0x6f, 0xd3, 0x30,
	0x10, 0xc7, 0x5b, 0xb6, 0x65, 0xcd, 0xb5, 0x65, 0xc3, 0x6b, 0x4b, 0xc9, 0x8a, 0xe8, 0x2c, 0x40,
	0x43, 0x82, 0x54, 0x5a, 0x61, 0x62, 0xd2, 0x5e, 0xe8, 0x36, 0x4d, 0xd5, 0x10, 0x88, 0xee, 0x8d,
	0x97, 0x29, 0x4b, 0xbc, 0x52, 0x91, 0x35, 0x21, 0x4e, 0x81, 0xf0, 0x47, 0xf0, 0x37, 0x23, 0xff,
	0x4a, 0xe2, 0xb5, 0x41, 0x44, 0xe2, 0xad, 0x3e, 0xdf, 0xf7, 0xeb, 0x3b, 0xf7, 0x3e, 0x0e, 0xec,
	0x12, 0xfa, 0x6a, 0xea, 0xc4, 0xe4, 0x87, 0x93, 0x0c, 0xb2, 0x9f, 0x76, 0x18, 0x05, 0x71, 0x80,
	0x80, 0xd0, 0x2b, 0x19, 0xc1, 0x2e, 0x98, 0x97, 0xc4, 0x89, 0xdc, 0x2f, 0x13, 0xf2, 0x0d, 0xb5,
	0x60, 0x63, 0x36, 0xf7, 0xc8, 0xcf, 0x6e, 0xb5, 0x5f, 0xdd, 0x37, 0x27, 0x62, 0x81, 0x10, 0xac,
	0x5f, 0x07, 0x5e, 0xd2, 0xbd, 0xc7, 0x83, 0xfc, 0x37, 0xea, 0x80, 0x11, 0xdc, 0xdc, 0x50, 0x12,
	0x77, 0xd7, 0xfa, 0xd5, 0xfd, 0xb5, 0x89, 0x5c, 0x31, 0x07, 0x7f, 0x76, 0x3b, 0x8b, 0xbb, 0xeb,
	0x3c, 0x2c, 0x16, 0xb8, 0x0f, 0xa0, 0x0e, 0xa1, 0x21, 0xf3, 0xf3, 0x9c, 0xd8, 0xe1, 0x87, 0x34,
	0x26, 0xfc, 0x37, 0x7e, 0x0f, 0xe6, 0x49, 0x44, 0x9c, 0x98, 0x14, 0x97, 0xd1, 0x06, 0xc3, 0x0b,
	0xdc, 0xab, 0x99, 0x27, 0x0b, 0xd9, 0xf0, 0x02, 0x77, 0xec, 0xa5, 0xd5, 0xad, 0x65, 0xd5, 0xe1,
	0x06, 0x80, 0x72, 0xa3, 0x21, 0x7e, 0x0b, 0xe6, 0x29, 0xf1, 0x49, 0x79, 0x6f, 0xe6, 0xa3, 0x94,
	0x34, 0xc4, 0x17, 0x50, 0x1b, 0xb3, 0xec, 0xff, 0x52, 0x62, 0x1d, 0x4c, 0x69, 0x46, 0x43, 0xfc,
	0x1a, 0x6a, 0x27, 0xc1, 0x62, 0x1e, 0x97, 0xfa, 0x0f, 0xf0, 0x13, 0x30, 0xa5, 0xaa, 0xe0, 0x52,
	0x8f, 0x00, 0xce, 0x49, 0x3c, 0x4a, 0xc6, 0x5e, 0xe9, 0xce, 0xf7, 0xa0, 0x9e, 0x4a, 0x0b, 0xdc,
	0x87, 0xb0, 0x39, 0x5a, 0xf8, 0x5f, 0xff, 0xad, 0xe6, 0x86, 0xac, 0x19, 0xa0, 0x26, 0x44, 0x34,
	0xc4, 0xc7, 0xb0, 0x2d, 0x6e, 0x77, 0x94, 0x7c, 0x5a, 0x90, 0x28, 0x29, 0xd7, 0xfd, 0x0e, 0x3c,
	0xb8, 0xa3, 0xa6, 0x21, 0x3e, 0x04, 0x78, 0x37, 0x77, 0xfc, 0xe4, 0x17, 0x29, 0x67, 0xb6, 0x07,
	0xf5, 0x54, 0x57, 0xd0, 0xee, 0x47, 0x68, 0x8a, 0x19, 0x3e, 0x25, 0x24, 0x2c, 0x07, 0x4b, 0x0a,
	0x05, 0x1b, 0x80, 0xa6, 0x82, 0xe2, 0x29, 0xdc, 0xcf, 0x1b, 0x16, 0x1c, 0xfb, 0x0c, 0xb6, 0x44,
	0xd6, 0xa5, 0x1b, 0x05, 0xbe, 0xcf, 0x0e, 0x56, 0x47, 0x54, 0x73, 0x0d, 0x3c, 0x87, 0x6d, 0x3d,
	0xad, 0xc0, 0xee, 0x05, 0x6c, 0x89, 0x5b, 0xcb, 0xec, 0x3a, 0x60, 0x50, 0xbe, 0x90, 0x86, 0x72,
	0xc5, 0x2c, 0xf5, 0xd4, 0xd5, 0x96, 0x07, 0xbf, 0x0d, 0x30, 0xcf, 0xe8, 0xb9, 0x78, 0x4f, 0xd0,
	0x11, 0x18, 0xa2, 0x10, 0xd4, 0xb6, 0xb3, 0x67, 0xc6, 0x4e, 0xdf, 0x18, 0xab, 0xb3, 0x2a, 0x4c,
	0x43, 0x5c, 0x61, 0x52, 0x41, 0xad, 0x2e, 0x4d, 0xdf, 0x05, 0x5d, 0x9a, 0x03, 0x9c, 0x4b, 0x45,
	0xad, 0xba, 0x34, 0xc5, 0x5e, 0x97, 0xe6, 0x98, 0xae, 0xa0, 0x43, 0xd8, 0xe0, 0x20, 0xa2, 0x56,
	0x3e, 0x45, 0x81, 0x6e, 0xb5, 0x57, 0x44, 0x95, 0x8e, 0xd3, 0xa7, 0xeb, 0x14, 0xc6, 0xba, 0x2e,
	0xc5, 0x14, 0x57, 0xd0, 0x31, 0x6c, 0x4a, 0xb2, 0x90, 0x56, 0x54, 0x46, 0xaa, 0xf5, 0x70, 0x65,
	0x9c, 0xab, 0x87, 0xb0, 0xce, 0xf8, 0x41, 0x3b, 0xf9, 0x14, 0x89, 0xa1, 0xd5, 0x5a, 0x0e, 0x72,
	0xd1, 0x07, 0x68, 0x6a, 0xa8, 0xa0, 0xde, 0xf2, 0x6d, 0x64, 0x0c, 0x5a, 0x8f, 0xff, 0xb2, 0xab,
	0x5a, 0x90, 0xb4, 0xe8, 0x2d, 0x64, 0xe8, 0xe9, 0x2d, 0xe4, 0xd0, 0xc2, 0x15, 0x74, 0xa6, 0x3e,
	0x06, 0x6c, 0xee, 0xd1, 0xa3, 0xe5, 0x71, 0x90, 0x80, 0x59, 0x56, 0xd1, 0x16, 0xb7, 0xb9, 0x80,
	0x46, 0x7e, 0xe2, 0xd1, 0xee, 0x72, 0x76, 0x3a, 0xe3, 0x56, 0xaf, 0x78, 0x53, 0x99, 0xe5, 0x67,
	0x5d, 0x37, 0xbb, 0x03, 0x8c, 0xd5, 0x2b, 0xde, 0x64, 0x66, 0x23, 0xfb, 0xf3, 0xcb, 0x69, 0xe0,
	0x3b, 0xf3, 0xa9, 0xfd, 0xe6, 0x20, 0x8e, 0x6d, 0x37, 0xb8, 0x1d, 0xf0, 0xef, 0xae, 0x1b, 0xf8,
	0x03, 0x4a, 0xa2, 0xef, 0x33, 0x97, 0xd0, 0xdc, 0x47, 0xf9, 0xda, 0xe0, 0xbb, 0xc3, 0x3f, 0x01,
	0x00, 0x00, 0xff, 0xff, 0xa2, 0x4e, 0x4d, 0x8b, 0xb4, 0x07, 0x00, 0x00,
}
