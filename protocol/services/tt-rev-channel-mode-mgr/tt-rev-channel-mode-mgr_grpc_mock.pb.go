// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/tt-rev-channel-mode-mgr/tt-rev-channel-mode-mgr.proto

package tt_rev_channel_mode_mgr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockChannelModeMgrClient is a mock of ChannelModeMgrClient interface.
type MockChannelModeMgrClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelModeMgrClientMockRecorder
}

// MockChannelModeMgrClientMockRecorder is the mock recorder for MockChannelModeMgrClient.
type MockChannelModeMgrClientMockRecorder struct {
	mock *MockChannelModeMgrClient
}

// NewMockChannelModeMgrClient creates a new mock instance.
func NewMockChannelModeMgrClient(ctrl *gomock.Controller) *MockChannelModeMgrClient {
	mock := &MockChannelModeMgrClient{ctrl: ctrl}
	mock.recorder = &MockChannelModeMgrClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelModeMgrClient) EXPECT() *MockChannelModeMgrClientMockRecorder {
	return m.recorder
}

// BatchGetChannelIdByLayoutType mocks base method.
func (m *MockChannelModeMgrClient) BatchGetChannelIdByLayoutType(ctx context.Context, in *BatchGetChannelIdByLayoutTypeReq, opts ...grpc.CallOption) (*BatchGetChannelIdByLayoutTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetChannelIdByLayoutType", varargs...)
	ret0, _ := ret[0].(*BatchGetChannelIdByLayoutTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelIdByLayoutType indicates an expected call of BatchGetChannelIdByLayoutType.
func (mr *MockChannelModeMgrClientMockRecorder) BatchGetChannelIdByLayoutType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelIdByLayoutType", reflect.TypeOf((*MockChannelModeMgrClient)(nil).BatchGetChannelIdByLayoutType), varargs...)
}

// BatchGetChannelModeEntry mocks base method.
func (m *MockChannelModeMgrClient) BatchGetChannelModeEntry(ctx context.Context, in *BatchGetChannelModeEntryReq, opts ...grpc.CallOption) (*BatchGetChannelModeEntryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetChannelModeEntry", varargs...)
	ret0, _ := ret[0].(*BatchGetChannelModeEntryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelModeEntry indicates an expected call of BatchGetChannelModeEntry.
func (mr *MockChannelModeMgrClientMockRecorder) BatchGetChannelModeEntry(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelModeEntry", reflect.TypeOf((*MockChannelModeMgrClient)(nil).BatchGetChannelModeEntry), varargs...)
}

// BatchOperateChannelMode mocks base method.
func (m *MockChannelModeMgrClient) BatchOperateChannelMode(ctx context.Context, in *BatchOperateChannelModeReq, opts ...grpc.CallOption) (*BatchOperateChannelModeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchOperateChannelMode", varargs...)
	ret0, _ := ret[0].(*BatchOperateChannelModeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchOperateChannelMode indicates an expected call of BatchOperateChannelMode.
func (mr *MockChannelModeMgrClientMockRecorder) BatchOperateChannelMode(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchOperateChannelMode", reflect.TypeOf((*MockChannelModeMgrClient)(nil).BatchOperateChannelMode), varargs...)
}

// BuyWerwolfItem mocks base method.
func (m *MockChannelModeMgrClient) BuyWerwolfItem(ctx context.Context, in *BuyWerwolfItemReq, opts ...grpc.CallOption) (*BuyWerwolfItemResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuyWerwolfItem", varargs...)
	ret0, _ := ret[0].(*BuyWerwolfItemResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuyWerwolfItem indicates an expected call of BuyWerwolfItem.
func (mr *MockChannelModeMgrClientMockRecorder) BuyWerwolfItem(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuyWerwolfItem", reflect.TypeOf((*MockChannelModeMgrClient)(nil).BuyWerwolfItem), varargs...)
}

// GenFinancialFile mocks base method.
func (m *MockChannelModeMgrClient) GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenFinancialFile", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.GenFinancialFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenFinancialFile indicates an expected call of GenFinancialFile.
func (mr *MockChannelModeMgrClientMockRecorder) GenFinancialFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenFinancialFile", reflect.TypeOf((*MockChannelModeMgrClient)(nil).GenFinancialFile), varargs...)
}

// GetChannelMode mocks base method.
func (m *MockChannelModeMgrClient) GetChannelMode(ctx context.Context, in *GetChannelModeReq, opts ...grpc.CallOption) (*GetChannelModeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelMode", varargs...)
	ret0, _ := ret[0].(*GetChannelModeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelMode indicates an expected call of GetChannelMode.
func (mr *MockChannelModeMgrClientMockRecorder) GetChannelMode(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelMode", reflect.TypeOf((*MockChannelModeMgrClient)(nil).GetChannelMode), varargs...)
}

// GetChannelOperator mocks base method.
func (m *MockChannelModeMgrClient) GetChannelOperator(ctx context.Context, in *GetChannelOperatorReq, opts ...grpc.CallOption) (*GetChannelOperatorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelOperator", varargs...)
	ret0, _ := ret[0].(*GetChannelOperatorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelOperator indicates an expected call of GetChannelOperator.
func (mr *MockChannelModeMgrClientMockRecorder) GetChannelOperator(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelOperator", reflect.TypeOf((*MockChannelModeMgrClient)(nil).GetChannelOperator), varargs...)
}

// GetConsumeOrderIds mocks base method.
func (m *MockChannelModeMgrClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockChannelModeMgrClientMockRecorder) GetConsumeOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockChannelModeMgrClient)(nil).GetConsumeOrderIds), varargs...)
}

// GetConsumeTotalCount mocks base method.
func (m *MockChannelModeMgrClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConsumeTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCount indicates an expected call of GetConsumeTotalCount.
func (mr *MockChannelModeMgrClientMockRecorder) GetConsumeTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCount", reflect.TypeOf((*MockChannelModeMgrClient)(nil).GetConsumeTotalCount), varargs...)
}

// GetOrderLogByOrderIds mocks base method.
func (m *MockChannelModeMgrClient) GetOrderLogByOrderIds(ctx context.Context, in *GetOrderLogByOrderIdsReq, opts ...grpc.CallOption) (*GetOrderLogByOrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrderLogByOrderIds", varargs...)
	ret0, _ := ret[0].(*GetOrderLogByOrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderLogByOrderIds indicates an expected call of GetOrderLogByOrderIds.
func (mr *MockChannelModeMgrClientMockRecorder) GetOrderLogByOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderLogByOrderIds", reflect.TypeOf((*MockChannelModeMgrClient)(nil).GetOrderLogByOrderIds), varargs...)
}

// SetChannelMode mocks base method.
func (m *MockChannelModeMgrClient) SetChannelMode(ctx context.Context, in *SetChannelModeReq, opts ...grpc.CallOption) (*SetChannelModeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelMode", varargs...)
	ret0, _ := ret[0].(*SetChannelModeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelMode indicates an expected call of SetChannelMode.
func (mr *MockChannelModeMgrClientMockRecorder) SetChannelMode(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelMode", reflect.TypeOf((*MockChannelModeMgrClient)(nil).SetChannelMode), varargs...)
}

// MockChannelModeMgrServer is a mock of ChannelModeMgrServer interface.
type MockChannelModeMgrServer struct {
	ctrl     *gomock.Controller
	recorder *MockChannelModeMgrServerMockRecorder
}

// MockChannelModeMgrServerMockRecorder is the mock recorder for MockChannelModeMgrServer.
type MockChannelModeMgrServerMockRecorder struct {
	mock *MockChannelModeMgrServer
}

// NewMockChannelModeMgrServer creates a new mock instance.
func NewMockChannelModeMgrServer(ctrl *gomock.Controller) *MockChannelModeMgrServer {
	mock := &MockChannelModeMgrServer{ctrl: ctrl}
	mock.recorder = &MockChannelModeMgrServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelModeMgrServer) EXPECT() *MockChannelModeMgrServerMockRecorder {
	return m.recorder
}

// BatchGetChannelIdByLayoutType mocks base method.
func (m *MockChannelModeMgrServer) BatchGetChannelIdByLayoutType(ctx context.Context, in *BatchGetChannelIdByLayoutTypeReq) (*BatchGetChannelIdByLayoutTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelIdByLayoutType", ctx, in)
	ret0, _ := ret[0].(*BatchGetChannelIdByLayoutTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelIdByLayoutType indicates an expected call of BatchGetChannelIdByLayoutType.
func (mr *MockChannelModeMgrServerMockRecorder) BatchGetChannelIdByLayoutType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelIdByLayoutType", reflect.TypeOf((*MockChannelModeMgrServer)(nil).BatchGetChannelIdByLayoutType), ctx, in)
}

// BatchGetChannelModeEntry mocks base method.
func (m *MockChannelModeMgrServer) BatchGetChannelModeEntry(ctx context.Context, in *BatchGetChannelModeEntryReq) (*BatchGetChannelModeEntryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelModeEntry", ctx, in)
	ret0, _ := ret[0].(*BatchGetChannelModeEntryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelModeEntry indicates an expected call of BatchGetChannelModeEntry.
func (mr *MockChannelModeMgrServerMockRecorder) BatchGetChannelModeEntry(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelModeEntry", reflect.TypeOf((*MockChannelModeMgrServer)(nil).BatchGetChannelModeEntry), ctx, in)
}

// BatchOperateChannelMode mocks base method.
func (m *MockChannelModeMgrServer) BatchOperateChannelMode(ctx context.Context, in *BatchOperateChannelModeReq) (*BatchOperateChannelModeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchOperateChannelMode", ctx, in)
	ret0, _ := ret[0].(*BatchOperateChannelModeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchOperateChannelMode indicates an expected call of BatchOperateChannelMode.
func (mr *MockChannelModeMgrServerMockRecorder) BatchOperateChannelMode(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchOperateChannelMode", reflect.TypeOf((*MockChannelModeMgrServer)(nil).BatchOperateChannelMode), ctx, in)
}

// BuyWerwolfItem mocks base method.
func (m *MockChannelModeMgrServer) BuyWerwolfItem(ctx context.Context, in *BuyWerwolfItemReq) (*BuyWerwolfItemResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BuyWerwolfItem", ctx, in)
	ret0, _ := ret[0].(*BuyWerwolfItemResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuyWerwolfItem indicates an expected call of BuyWerwolfItem.
func (mr *MockChannelModeMgrServerMockRecorder) BuyWerwolfItem(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuyWerwolfItem", reflect.TypeOf((*MockChannelModeMgrServer)(nil).BuyWerwolfItem), ctx, in)
}

// GenFinancialFile mocks base method.
func (m *MockChannelModeMgrServer) GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq) (*reconcile_v2.GenFinancialFileResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenFinancialFile", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.GenFinancialFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenFinancialFile indicates an expected call of GenFinancialFile.
func (mr *MockChannelModeMgrServerMockRecorder) GenFinancialFile(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenFinancialFile", reflect.TypeOf((*MockChannelModeMgrServer)(nil).GenFinancialFile), ctx, in)
}

// GetChannelMode mocks base method.
func (m *MockChannelModeMgrServer) GetChannelMode(ctx context.Context, in *GetChannelModeReq) (*GetChannelModeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelMode", ctx, in)
	ret0, _ := ret[0].(*GetChannelModeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelMode indicates an expected call of GetChannelMode.
func (mr *MockChannelModeMgrServerMockRecorder) GetChannelMode(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelMode", reflect.TypeOf((*MockChannelModeMgrServer)(nil).GetChannelMode), ctx, in)
}

// GetChannelOperator mocks base method.
func (m *MockChannelModeMgrServer) GetChannelOperator(ctx context.Context, in *GetChannelOperatorReq) (*GetChannelOperatorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelOperator", ctx, in)
	ret0, _ := ret[0].(*GetChannelOperatorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelOperator indicates an expected call of GetChannelOperator.
func (mr *MockChannelModeMgrServerMockRecorder) GetChannelOperator(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelOperator", reflect.TypeOf((*MockChannelModeMgrServer)(nil).GetChannelOperator), ctx, in)
}

// GetConsumeOrderIds mocks base method.
func (m *MockChannelModeMgrServer) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockChannelModeMgrServerMockRecorder) GetConsumeOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockChannelModeMgrServer)(nil).GetConsumeOrderIds), ctx, in)
}

// GetConsumeTotalCount mocks base method.
func (m *MockChannelModeMgrServer) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCount indicates an expected call of GetConsumeTotalCount.
func (mr *MockChannelModeMgrServerMockRecorder) GetConsumeTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCount", reflect.TypeOf((*MockChannelModeMgrServer)(nil).GetConsumeTotalCount), ctx, in)
}

// GetOrderLogByOrderIds mocks base method.
func (m *MockChannelModeMgrServer) GetOrderLogByOrderIds(ctx context.Context, in *GetOrderLogByOrderIdsReq) (*GetOrderLogByOrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderLogByOrderIds", ctx, in)
	ret0, _ := ret[0].(*GetOrderLogByOrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderLogByOrderIds indicates an expected call of GetOrderLogByOrderIds.
func (mr *MockChannelModeMgrServerMockRecorder) GetOrderLogByOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderLogByOrderIds", reflect.TypeOf((*MockChannelModeMgrServer)(nil).GetOrderLogByOrderIds), ctx, in)
}

// SetChannelMode mocks base method.
func (m *MockChannelModeMgrServer) SetChannelMode(ctx context.Context, in *SetChannelModeReq) (*SetChannelModeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelMode", ctx, in)
	ret0, _ := ret[0].(*SetChannelModeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelMode indicates an expected call of SetChannelMode.
func (mr *MockChannelModeMgrServerMockRecorder) SetChannelMode(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelMode", reflect.TypeOf((*MockChannelModeMgrServer)(nil).SetChannelMode), ctx, in)
}
