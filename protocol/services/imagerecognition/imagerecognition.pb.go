// Code generated by protoc-gen-gogo.
// source: src/imagerecognition/imagerecognition.proto
// DO NOT EDIT!

/*
	Package imagerecognition is a generated protocol buffer package.

	It is generated from these files:
		src/imagerecognition/imagerecognition.proto

	It has these top-level messages:
		Image
		ImageRecognizeReq
		RecognizeReq
		PornClassifyResult
		PoliticanMatchResult
		ImageRecognizeResult
		ImageRecognizeRsp
		RecognizeRsp
		GetViolationNoticeImageReq
		GetViolationNoticeImageRsp
		RecognizeSyncReq
		RecognizeSyncResp
		RecognizeAsyncReq
		RecognizeAsyncResp
		TupuPornResult
		TupuPoliticianResult
		TupuAdvertisingResult
		TupuViolenceTerrorResult
		TupuSearchGalleryResult
		TupuRecognitionResult
*/
package imagerecognition

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import math3 "math"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import math4 "math"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

//
type ImagePresent int32

const (
	ImagePresent_IMG_PRES_NIL  ImagePresent = 0
	ImagePresent_IMG_PRES_RAW  ImagePresent = 1
	ImagePresent_IMG_PRES_URL  ImagePresent = 2
	ImagePresent_IMG_PRES_FILE ImagePresent = 3
)

var ImagePresent_name = map[int32]string{
	0: "IMG_PRES_NIL",
	1: "IMG_PRES_RAW",
	2: "IMG_PRES_URL",
	3: "IMG_PRES_FILE",
}
var ImagePresent_value = map[string]int32{
	"IMG_PRES_NIL":  0,
	"IMG_PRES_RAW":  1,
	"IMG_PRES_URL":  2,
	"IMG_PRES_FILE": 3,
}

func (x ImagePresent) Enum() *ImagePresent {
	p := new(ImagePresent)
	*p = x
	return p
}
func (x ImagePresent) String() string {
	return proto.EnumName(ImagePresent_name, int32(x))
}
func (x *ImagePresent) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ImagePresent_value, data, "ImagePresent")
	if err != nil {
		return err
	}
	*x = ImagePresent(value)
	return nil
}
func (ImagePresent) EnumDescriptor() ([]byte, []int) { return fileDescriptorImagerecognition, []int{0} }

// task
type RecognizeType int32

const (
	RecognizeType_RECOGNIZE_PORN_CLASSIFY        RecognizeType = 1
	RecognizeType_RECOGNIZE_POLITICIAN_MATCH     RecognizeType = 2
	RecognizeType_RECOGNIZE_ADVERTISING_IDENTIFY RecognizeType = 4
	RecognizeType_RECOGNIZE_VIOLENCE_TERROR      RecognizeType = 8
)

var RecognizeType_name = map[int32]string{
	1: "RECOGNIZE_PORN_CLASSIFY",
	2: "RECOGNIZE_POLITICIAN_MATCH",
	4: "RECOGNIZE_ADVERTISING_IDENTIFY",
	8: "RECOGNIZE_VIOLENCE_TERROR",
}
var RecognizeType_value = map[string]int32{
	"RECOGNIZE_PORN_CLASSIFY":        1,
	"RECOGNIZE_POLITICIAN_MATCH":     2,
	"RECOGNIZE_ADVERTISING_IDENTIFY": 4,
	"RECOGNIZE_VIOLENCE_TERROR":      8,
}

func (x RecognizeType) Enum() *RecognizeType {
	p := new(RecognizeType)
	*p = x
	return p
}
func (x RecognizeType) String() string {
	return proto.EnumName(RecognizeType_name, int32(x))
}
func (x *RecognizeType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RecognizeType_value, data, "RecognizeType")
	if err != nil {
		return err
	}
	*x = RecognizeType(value)
	return nil
}
func (RecognizeType) EnumDescriptor() ([]byte, []int) { return fileDescriptorImagerecognition, []int{1} }

// 概述
type BriefResult int32

const (
	BriefResult_RESULT_DESIRED   BriefResult = 1
	BriefResult_RESULT_NOTSURE   BriefResult = 2
	BriefResult_RESULT_UNDESIRED BriefResult = 4
	BriefResult_RESULT_UNSUPPORT BriefResult = 8
	BriefResult_RESULT_INVALID   BriefResult = 16
)

var BriefResult_name = map[int32]string{
	1:  "RESULT_DESIRED",
	2:  "RESULT_NOTSURE",
	4:  "RESULT_UNDESIRED",
	8:  "RESULT_UNSUPPORT",
	16: "RESULT_INVALID",
}
var BriefResult_value = map[string]int32{
	"RESULT_DESIRED":   1,
	"RESULT_NOTSURE":   2,
	"RESULT_UNDESIRED": 4,
	"RESULT_UNSUPPORT": 8,
	"RESULT_INVALID":   16,
}

func (x BriefResult) Enum() *BriefResult {
	p := new(BriefResult)
	*p = x
	return p
}
func (x BriefResult) String() string {
	return proto.EnumName(BriefResult_name, int32(x))
}
func (x *BriefResult) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(BriefResult_value, data, "BriefResult")
	if err != nil {
		return err
	}
	*x = BriefResult(value)
	return nil
}
func (BriefResult) EnumDescriptor() ([]byte, []int) { return fileDescriptorImagerecognition, []int{2} }

type PornClassifyResult_RESULT int32

const (
	PornClassifyResult_PORNY  PornClassifyResult_RESULT = 0
	PornClassifyResult_SEXY   PornClassifyResult_RESULT = 1
	PornClassifyResult_NORMAL PornClassifyResult_RESULT = 2
)

var PornClassifyResult_RESULT_name = map[int32]string{
	0: "PORNY",
	1: "SEXY",
	2: "NORMAL",
}
var PornClassifyResult_RESULT_value = map[string]int32{
	"PORNY":  0,
	"SEXY":   1,
	"NORMAL": 2,
}

func (x PornClassifyResult_RESULT) Enum() *PornClassifyResult_RESULT {
	p := new(PornClassifyResult_RESULT)
	*p = x
	return p
}
func (x PornClassifyResult_RESULT) String() string {
	return proto.EnumName(PornClassifyResult_RESULT_name, int32(x))
}
func (x *PornClassifyResult_RESULT) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PornClassifyResult_RESULT_value, data, "PornClassifyResult_RESULT")
	if err != nil {
		return err
	}
	*x = PornClassifyResult_RESULT(value)
	return nil
}
func (PornClassifyResult_RESULT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{3, 0}
}

type PoliticanMatchResult_RESULT int32

const (
	PoliticanMatchResult_POLITICAN     PoliticanMatchResult_RESULT = 0
	PoliticanMatchResult_NOT_POLITICAN PoliticanMatchResult_RESULT = 2
	PoliticanMatchResult_UNKOWN        PoliticanMatchResult_RESULT = 3
)

var PoliticanMatchResult_RESULT_name = map[int32]string{
	0: "POLITICAN",
	2: "NOT_POLITICAN",
	3: "UNKOWN",
}
var PoliticanMatchResult_RESULT_value = map[string]int32{
	"POLITICAN":     0,
	"NOT_POLITICAN": 2,
	"UNKOWN":        3,
}

func (x PoliticanMatchResult_RESULT) Enum() *PoliticanMatchResult_RESULT {
	p := new(PoliticanMatchResult_RESULT)
	*p = x
	return p
}
func (x PoliticanMatchResult_RESULT) String() string {
	return proto.EnumName(PoliticanMatchResult_RESULT_name, int32(x))
}
func (x *PoliticanMatchResult_RESULT) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PoliticanMatchResult_RESULT_value, data, "PoliticanMatchResult_RESULT")
	if err != nil {
		return err
	}
	*x = PoliticanMatchResult_RESULT(value)
	return nil
}
func (PoliticanMatchResult_RESULT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{4, 0}
}

type Image struct {
	Name    string `protobuf:"bytes,1,req,name=name" json:"name"`
	Present uint32 `protobuf:"varint,2,req,name=present" json:"present"`
	Raw     []byte `protobuf:"bytes,3,opt,name=raw" json:"raw"`
	Url     string `protobuf:"bytes,4,opt,name=url" json:"url"`
	File    string `protobuf:"bytes,5,opt,name=file" json:"file"`
}

func (m *Image) Reset()                    { *m = Image{} }
func (m *Image) String() string            { return proto.CompactTextString(m) }
func (*Image) ProtoMessage()               {}
func (*Image) Descriptor() ([]byte, []int) { return fileDescriptorImagerecognition, []int{0} }

func (m *Image) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Image) GetPresent() uint32 {
	if m != nil {
		return m.Present
	}
	return 0
}

func (m *Image) GetRaw() []byte {
	if m != nil {
		return m.Raw
	}
	return nil
}

func (m *Image) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Image) GetFile() string {
	if m != nil {
		return m.File
	}
	return ""
}

type ImageRecognizeReq struct {
	TransId       uint32   `protobuf:"varint,1,req,name=trans_id,json=transId" json:"trans_id"`
	RecognizeType uint32   `protobuf:"varint,2,req,name=recognize_type,json=recognizeType" json:"recognize_type"`
	ImageList     []*Image `protobuf:"bytes,3,rep,name=image_list,json=imageList" json:"image_list,omitempty"`
}

func (m *ImageRecognizeReq) Reset()         { *m = ImageRecognizeReq{} }
func (m *ImageRecognizeReq) String() string { return proto.CompactTextString(m) }
func (*ImageRecognizeReq) ProtoMessage()    {}
func (*ImageRecognizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{1}
}

func (m *ImageRecognizeReq) GetTransId() uint32 {
	if m != nil {
		return m.TransId
	}
	return 0
}

func (m *ImageRecognizeReq) GetRecognizeType() uint32 {
	if m != nil {
		return m.RecognizeType
	}
	return 0
}

func (m *ImageRecognizeReq) GetImageList() []*Image {
	if m != nil {
		return m.ImageList
	}
	return nil
}

type RecognizeReq struct {
	Req                *ImageRecognizeReq `protobuf:"bytes,1,req,name=req" json:"req,omitempty"`
	WithDetailedResult bool               `protobuf:"varint,2,opt,name=with_detailed_result,json=withDetailedResult" json:"with_detailed_result"`
	RequestTimeout     uint32             `protobuf:"varint,3,opt,name=request_timeout,json=requestTimeout" json:"request_timeout"`
	TryCount           uint32             `protobuf:"varint,4,opt,name=try_count,json=tryCount" json:"try_count"`
}

func (m *RecognizeReq) Reset()                    { *m = RecognizeReq{} }
func (m *RecognizeReq) String() string            { return proto.CompactTextString(m) }
func (*RecognizeReq) ProtoMessage()               {}
func (*RecognizeReq) Descriptor() ([]byte, []int) { return fileDescriptorImagerecognition, []int{2} }

func (m *RecognizeReq) GetReq() *ImageRecognizeReq {
	if m != nil {
		return m.Req
	}
	return nil
}

func (m *RecognizeReq) GetWithDetailedResult() bool {
	if m != nil {
		return m.WithDetailedResult
	}
	return false
}

func (m *RecognizeReq) GetRequestTimeout() uint32 {
	if m != nil {
		return m.RequestTimeout
	}
	return 0
}

func (m *RecognizeReq) GetTryCount() uint32 {
	if m != nil {
		return m.TryCount
	}
	return 0
}

// 色情
type PornClassifyResult struct {
	Result        uint32 `protobuf:"varint,1,req,name=result" json:"result"`
	RequireReview uint32 `protobuf:"varint,2,req,name=require_review,json=requireReview" json:"require_review"`
}

func (m *PornClassifyResult) Reset()         { *m = PornClassifyResult{} }
func (m *PornClassifyResult) String() string { return proto.CompactTextString(m) }
func (*PornClassifyResult) ProtoMessage()    {}
func (*PornClassifyResult) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{3}
}

func (m *PornClassifyResult) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *PornClassifyResult) GetRequireReview() uint32 {
	if m != nil {
		return m.RequireReview
	}
	return 0
}

// 政治人物
type PoliticanMatchResult struct {
	Result uint32 `protobuf:"varint,1,req,name=result" json:"result"`
}

func (m *PoliticanMatchResult) Reset()         { *m = PoliticanMatchResult{} }
func (m *PoliticanMatchResult) String() string { return proto.CompactTextString(m) }
func (*PoliticanMatchResult) ProtoMessage()    {}
func (*PoliticanMatchResult) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{4}
}

func (m *PoliticanMatchResult) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

type ImageRecognizeResult struct {
	ImgName        string                `protobuf:"bytes,1,req,name=img_name,json=imgName" json:"img_name"`
	Result         uint32                `protobuf:"varint,2,req,name=result" json:"result"`
	PornClassify   *PornClassifyResult   `protobuf:"bytes,3,opt,name=porn_classify,json=pornClassify" json:"porn_classify,omitempty"`
	PoliticanMatch *PoliticanMatchResult `protobuf:"bytes,4,opt,name=politican_match,json=politicanMatch" json:"politican_match,omitempty"`
}

func (m *ImageRecognizeResult) Reset()         { *m = ImageRecognizeResult{} }
func (m *ImageRecognizeResult) String() string { return proto.CompactTextString(m) }
func (*ImageRecognizeResult) ProtoMessage()    {}
func (*ImageRecognizeResult) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{5}
}

func (m *ImageRecognizeResult) GetImgName() string {
	if m != nil {
		return m.ImgName
	}
	return ""
}

func (m *ImageRecognizeResult) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *ImageRecognizeResult) GetPornClassify() *PornClassifyResult {
	if m != nil {
		return m.PornClassify
	}
	return nil
}

func (m *ImageRecognizeResult) GetPoliticanMatch() *PoliticanMatchResult {
	if m != nil {
		return m.PoliticanMatch
	}
	return nil
}

type ImageRecognizeRsp struct {
	TransId       uint32                  `protobuf:"varint,1,req,name=trans_id,json=transId" json:"trans_id"`
	RecognizeType uint32                  `protobuf:"varint,2,req,name=recognize_type,json=recognizeType" json:"recognize_type"`
	Results       []*ImageRecognizeResult `protobuf:"bytes,3,rep,name=results" json:"results,omitempty"`
}

func (m *ImageRecognizeRsp) Reset()         { *m = ImageRecognizeRsp{} }
func (m *ImageRecognizeRsp) String() string { return proto.CompactTextString(m) }
func (*ImageRecognizeRsp) ProtoMessage()    {}
func (*ImageRecognizeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{6}
}

func (m *ImageRecognizeRsp) GetTransId() uint32 {
	if m != nil {
		return m.TransId
	}
	return 0
}

func (m *ImageRecognizeRsp) GetRecognizeType() uint32 {
	if m != nil {
		return m.RecognizeType
	}
	return 0
}

func (m *ImageRecognizeRsp) GetResults() []*ImageRecognizeResult {
	if m != nil {
		return m.Results
	}
	return nil
}

type RecognizeRsp struct {
	Rsp *ImageRecognizeRsp `protobuf:"bytes,1,req,name=rsp" json:"rsp,omitempty"`
}

func (m *RecognizeRsp) Reset()                    { *m = RecognizeRsp{} }
func (m *RecognizeRsp) String() string            { return proto.CompactTextString(m) }
func (*RecognizeRsp) ProtoMessage()               {}
func (*RecognizeRsp) Descriptor() ([]byte, []int) { return fileDescriptorImagerecognition, []int{7} }

func (m *RecognizeRsp) GetRsp() *ImageRecognizeRsp {
	if m != nil {
		return m.Rsp
	}
	return nil
}

type GetViolationNoticeImageReq struct {
}

func (m *GetViolationNoticeImageReq) Reset()         { *m = GetViolationNoticeImageReq{} }
func (m *GetViolationNoticeImageReq) String() string { return proto.CompactTextString(m) }
func (*GetViolationNoticeImageReq) ProtoMessage()    {}
func (*GetViolationNoticeImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{8}
}

type GetViolationNoticeImageRsp struct {
	ImgData []byte `protobuf:"bytes,1,req,name=img_data,json=imgData" json:"img_data"`
}

func (m *GetViolationNoticeImageRsp) Reset()         { *m = GetViolationNoticeImageRsp{} }
func (m *GetViolationNoticeImageRsp) String() string { return proto.CompactTextString(m) }
func (*GetViolationNoticeImageRsp) ProtoMessage()    {}
func (*GetViolationNoticeImageRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{9}
}

func (m *GetViolationNoticeImageRsp) GetImgData() []byte {
	if m != nil {
		return m.ImgData
	}
	return nil
}

type RecognizeSyncReq struct {
	Req            *ImageRecognizeReq `protobuf:"bytes,1,req,name=req" json:"req,omitempty"`
	RequestTimeout uint32             `protobuf:"varint,2,opt,name=request_timeout,json=requestTimeout" json:"request_timeout"`
}

func (m *RecognizeSyncReq) Reset()         { *m = RecognizeSyncReq{} }
func (m *RecognizeSyncReq) String() string { return proto.CompactTextString(m) }
func (*RecognizeSyncReq) ProtoMessage()    {}
func (*RecognizeSyncReq) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{10}
}

func (m *RecognizeSyncReq) GetReq() *ImageRecognizeReq {
	if m != nil {
		return m.Req
	}
	return nil
}

func (m *RecognizeSyncReq) GetRequestTimeout() uint32 {
	if m != nil {
		return m.RequestTimeout
	}
	return 0
}

type RecognizeSyncResp struct {
	ErrorCode int32              `protobuf:"varint,1,opt,name=error_code,json=errorCode" json:"error_code"`
	Rsp       *ImageRecognizeRsp `protobuf:"bytes,2,opt,name=rsp" json:"rsp,omitempty"`
}

func (m *RecognizeSyncResp) Reset()         { *m = RecognizeSyncResp{} }
func (m *RecognizeSyncResp) String() string { return proto.CompactTextString(m) }
func (*RecognizeSyncResp) ProtoMessage()    {}
func (*RecognizeSyncResp) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{11}
}

func (m *RecognizeSyncResp) GetErrorCode() int32 {
	if m != nil {
		return m.ErrorCode
	}
	return 0
}

func (m *RecognizeSyncResp) GetRsp() *ImageRecognizeRsp {
	if m != nil {
		return m.Rsp
	}
	return nil
}

type RecognizeAsyncReq struct {
	Req            *ImageRecognizeReq `protobuf:"bytes,1,req,name=req" json:"req,omitempty"`
	RequestTimeout uint32             `protobuf:"varint,2,opt,name=request_timeout,json=requestTimeout" json:"request_timeout"`
	TryCount       uint32             `protobuf:"varint,3,opt,name=try_count,json=tryCount" json:"try_count"`
}

func (m *RecognizeAsyncReq) Reset()         { *m = RecognizeAsyncReq{} }
func (m *RecognizeAsyncReq) String() string { return proto.CompactTextString(m) }
func (*RecognizeAsyncReq) ProtoMessage()    {}
func (*RecognizeAsyncReq) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{12}
}

func (m *RecognizeAsyncReq) GetReq() *ImageRecognizeReq {
	if m != nil {
		return m.Req
	}
	return nil
}

func (m *RecognizeAsyncReq) GetRequestTimeout() uint32 {
	if m != nil {
		return m.RequestTimeout
	}
	return 0
}

func (m *RecognizeAsyncReq) GetTryCount() uint32 {
	if m != nil {
		return m.TryCount
	}
	return 0
}

type RecognizeAsyncResp struct {
	ErrorCode int32              `protobuf:"varint,1,opt,name=error_code,json=errorCode" json:"error_code"`
	Rsp       *ImageRecognizeRsp `protobuf:"bytes,2,opt,name=rsp" json:"rsp,omitempty"`
}

func (m *RecognizeAsyncResp) Reset()         { *m = RecognizeAsyncResp{} }
func (m *RecognizeAsyncResp) String() string { return proto.CompactTextString(m) }
func (*RecognizeAsyncResp) ProtoMessage()    {}
func (*RecognizeAsyncResp) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{13}
}

func (m *RecognizeAsyncResp) GetErrorCode() int32 {
	if m != nil {
		return m.ErrorCode
	}
	return 0
}

func (m *RecognizeAsyncResp) GetRsp() *ImageRecognizeRsp {
	if m != nil {
		return m.Rsp
	}
	return nil
}

// 内部使用
// 色情识别
type TupuPornResult struct {
	ImageName   string  `protobuf:"bytes,1,req,name=image_name,json=imageName" json:"image_name"`
	BriefResult uint32  `protobuf:"varint,2,req,name=brief_result,json=briefResult" json:"brief_result"`
	Label       int32   `protobuf:"varint,3,req,name=label" json:"label"`
	Review      bool    `protobuf:"varint,4,req,name=review" json:"review"`
	Rate        float64 `protobuf:"fixed64,5,opt,name=rate" json:"rate"`
	Tab         string  `protobuf:"bytes,6,opt,name=tab" json:"tab"`
	ZipFileName string  `protobuf:"bytes,7,opt,name=zip_file_name,json=zipFileName" json:"zip_file_name"`
}

func (m *TupuPornResult) Reset()                    { *m = TupuPornResult{} }
func (m *TupuPornResult) String() string            { return proto.CompactTextString(m) }
func (*TupuPornResult) ProtoMessage()               {}
func (*TupuPornResult) Descriptor() ([]byte, []int) { return fileDescriptorImagerecognition, []int{14} }

func (m *TupuPornResult) GetImageName() string {
	if m != nil {
		return m.ImageName
	}
	return ""
}

func (m *TupuPornResult) GetBriefResult() uint32 {
	if m != nil {
		return m.BriefResult
	}
	return 0
}

func (m *TupuPornResult) GetLabel() int32 {
	if m != nil {
		return m.Label
	}
	return 0
}

func (m *TupuPornResult) GetReview() bool {
	if m != nil {
		return m.Review
	}
	return false
}

func (m *TupuPornResult) GetRate() float64 {
	if m != nil {
		return m.Rate
	}
	return 0
}

func (m *TupuPornResult) GetTab() string {
	if m != nil {
		return m.Tab
	}
	return ""
}

func (m *TupuPornResult) GetZipFileName() string {
	if m != nil {
		return m.ZipFileName
	}
	return ""
}

// 政治任务搜索
type TupuPoliticianResult struct {
	ImageName   string  `protobuf:"bytes,1,req,name=image_name,json=imageName" json:"image_name"`
	BriefResult uint32  `protobuf:"varint,2,req,name=brief_result,json=briefResult" json:"brief_result"`
	Label       int32   `protobuf:"varint,3,req,name=label" json:"label"`
	Review      bool    `protobuf:"varint,4,req,name=review" json:"review"`
	Similarity  float64 `protobuf:"fixed64,5,opt,name=similarity" json:"similarity"`
	FaceId      string  `protobuf:"bytes,6,opt,name=face_id,json=faceId" json:"face_id"`
}

func (m *TupuPoliticianResult) Reset()         { *m = TupuPoliticianResult{} }
func (m *TupuPoliticianResult) String() string { return proto.CompactTextString(m) }
func (*TupuPoliticianResult) ProtoMessage()    {}
func (*TupuPoliticianResult) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{15}
}

func (m *TupuPoliticianResult) GetImageName() string {
	if m != nil {
		return m.ImageName
	}
	return ""
}

func (m *TupuPoliticianResult) GetBriefResult() uint32 {
	if m != nil {
		return m.BriefResult
	}
	return 0
}

func (m *TupuPoliticianResult) GetLabel() int32 {
	if m != nil {
		return m.Label
	}
	return 0
}

func (m *TupuPoliticianResult) GetReview() bool {
	if m != nil {
		return m.Review
	}
	return false
}

func (m *TupuPoliticianResult) GetSimilarity() float64 {
	if m != nil {
		return m.Similarity
	}
	return 0
}

func (m *TupuPoliticianResult) GetFaceId() string {
	if m != nil {
		return m.FaceId
	}
	return ""
}

// 广告识别2.0
type TupuAdvertisingResult struct {
	ImageName   string `protobuf:"bytes,1,req,name=image_name,json=imageName" json:"image_name"`
	BriefResult uint32 `protobuf:"varint,2,req,name=brief_result,json=briefResult" json:"brief_result"`
	Label       int32  `protobuf:"varint,3,req,name=label" json:"label"`
	Review      bool   `protobuf:"varint,4,req,name=review" json:"review"`
}

func (m *TupuAdvertisingResult) Reset()         { *m = TupuAdvertisingResult{} }
func (m *TupuAdvertisingResult) String() string { return proto.CompactTextString(m) }
func (*TupuAdvertisingResult) ProtoMessage()    {}
func (*TupuAdvertisingResult) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{16}
}

func (m *TupuAdvertisingResult) GetImageName() string {
	if m != nil {
		return m.ImageName
	}
	return ""
}

func (m *TupuAdvertisingResult) GetBriefResult() uint32 {
	if m != nil {
		return m.BriefResult
	}
	return 0
}

func (m *TupuAdvertisingResult) GetLabel() int32 {
	if m != nil {
		return m.Label
	}
	return 0
}

func (m *TupuAdvertisingResult) GetReview() bool {
	if m != nil {
		return m.Review
	}
	return false
}

// 暴恐识别3.0
type TupuViolenceTerrorResult struct {
	ImageName   string `protobuf:"bytes,1,req,name=image_name,json=imageName" json:"image_name"`
	BriefResult uint32 `protobuf:"varint,2,req,name=brief_result,json=briefResult" json:"brief_result"`
	Label       int32  `protobuf:"varint,3,req,name=label" json:"label"`
	Review      bool   `protobuf:"varint,4,req,name=review" json:"review"`
}

func (m *TupuViolenceTerrorResult) Reset()         { *m = TupuViolenceTerrorResult{} }
func (m *TupuViolenceTerrorResult) String() string { return proto.CompactTextString(m) }
func (*TupuViolenceTerrorResult) ProtoMessage()    {}
func (*TupuViolenceTerrorResult) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{17}
}

func (m *TupuViolenceTerrorResult) GetImageName() string {
	if m != nil {
		return m.ImageName
	}
	return ""
}

func (m *TupuViolenceTerrorResult) GetBriefResult() uint32 {
	if m != nil {
		return m.BriefResult
	}
	return 0
}

func (m *TupuViolenceTerrorResult) GetLabel() int32 {
	if m != nil {
		return m.Label
	}
	return 0
}

func (m *TupuViolenceTerrorResult) GetReview() bool {
	if m != nil {
		return m.Review
	}
	return false
}

type TupuSearchGalleryResult struct {
	ImageName   string `protobuf:"bytes,1,req,name=image_name,json=imageName" json:"image_name"`
	BriefResult uint32 `protobuf:"varint,2,req,name=brief_result,json=briefResult" json:"brief_result"`
	Label       int32  `protobuf:"varint,3,req,name=label" json:"label"`
	Review      bool   `protobuf:"varint,4,req,name=review" json:"review"`
}

func (m *TupuSearchGalleryResult) Reset()         { *m = TupuSearchGalleryResult{} }
func (m *TupuSearchGalleryResult) String() string { return proto.CompactTextString(m) }
func (*TupuSearchGalleryResult) ProtoMessage()    {}
func (*TupuSearchGalleryResult) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{18}
}

func (m *TupuSearchGalleryResult) GetImageName() string {
	if m != nil {
		return m.ImageName
	}
	return ""
}

func (m *TupuSearchGalleryResult) GetBriefResult() uint32 {
	if m != nil {
		return m.BriefResult
	}
	return 0
}

func (m *TupuSearchGalleryResult) GetLabel() int32 {
	if m != nil {
		return m.Label
	}
	return 0
}

func (m *TupuSearchGalleryResult) GetReview() bool {
	if m != nil {
		return m.Review
	}
	return false
}

// 汇总
type TupuRecognitionResult struct {
	Result               uint32                    `protobuf:"varint,1,req,name=result" json:"result"`
	PornResult           *TupuPornResult           `protobuf:"bytes,2,opt,name=porn_result,json=pornResult" json:"porn_result,omitempty"`
	PoliticianResult     *TupuPoliticianResult     `protobuf:"bytes,3,opt,name=politician_result,json=politicianResult" json:"politician_result,omitempty"`
	AdvertisingResult    *TupuAdvertisingResult    `protobuf:"bytes,4,opt,name=advertising_result,json=advertisingResult" json:"advertising_result,omitempty"`
	ViolenceTerrorResult *TupuViolenceTerrorResult `protobuf:"bytes,5,opt,name=violence_terror_result,json=violenceTerrorResult" json:"violence_terror_result,omitempty"`
	SearchGalleryResult  *TupuSearchGalleryResult  `protobuf:"bytes,6,opt,name=search_gallery_result,json=searchGalleryResult" json:"search_gallery_result,omitempty"`
}

func (m *TupuRecognitionResult) Reset()         { *m = TupuRecognitionResult{} }
func (m *TupuRecognitionResult) String() string { return proto.CompactTextString(m) }
func (*TupuRecognitionResult) ProtoMessage()    {}
func (*TupuRecognitionResult) Descriptor() ([]byte, []int) {
	return fileDescriptorImagerecognition, []int{19}
}

func (m *TupuRecognitionResult) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *TupuRecognitionResult) GetPornResult() *TupuPornResult {
	if m != nil {
		return m.PornResult
	}
	return nil
}

func (m *TupuRecognitionResult) GetPoliticianResult() *TupuPoliticianResult {
	if m != nil {
		return m.PoliticianResult
	}
	return nil
}

func (m *TupuRecognitionResult) GetAdvertisingResult() *TupuAdvertisingResult {
	if m != nil {
		return m.AdvertisingResult
	}
	return nil
}

func (m *TupuRecognitionResult) GetViolenceTerrorResult() *TupuViolenceTerrorResult {
	if m != nil {
		return m.ViolenceTerrorResult
	}
	return nil
}

func (m *TupuRecognitionResult) GetSearchGalleryResult() *TupuSearchGalleryResult {
	if m != nil {
		return m.SearchGalleryResult
	}
	return nil
}

func init() {
	proto.RegisterType((*Image)(nil), "imagerecognition.Image")
	proto.RegisterType((*ImageRecognizeReq)(nil), "imagerecognition.ImageRecognizeReq")
	proto.RegisterType((*RecognizeReq)(nil), "imagerecognition.RecognizeReq")
	proto.RegisterType((*PornClassifyResult)(nil), "imagerecognition.PornClassifyResult")
	proto.RegisterType((*PoliticanMatchResult)(nil), "imagerecognition.PoliticanMatchResult")
	proto.RegisterType((*ImageRecognizeResult)(nil), "imagerecognition.ImageRecognizeResult")
	proto.RegisterType((*ImageRecognizeRsp)(nil), "imagerecognition.ImageRecognizeRsp")
	proto.RegisterType((*RecognizeRsp)(nil), "imagerecognition.RecognizeRsp")
	proto.RegisterType((*GetViolationNoticeImageReq)(nil), "imagerecognition.GetViolationNoticeImageReq")
	proto.RegisterType((*GetViolationNoticeImageRsp)(nil), "imagerecognition.GetViolationNoticeImageRsp")
	proto.RegisterType((*RecognizeSyncReq)(nil), "imagerecognition.RecognizeSyncReq")
	proto.RegisterType((*RecognizeSyncResp)(nil), "imagerecognition.RecognizeSyncResp")
	proto.RegisterType((*RecognizeAsyncReq)(nil), "imagerecognition.RecognizeAsyncReq")
	proto.RegisterType((*RecognizeAsyncResp)(nil), "imagerecognition.RecognizeAsyncResp")
	proto.RegisterType((*TupuPornResult)(nil), "imagerecognition.TupuPornResult")
	proto.RegisterType((*TupuPoliticianResult)(nil), "imagerecognition.TupuPoliticianResult")
	proto.RegisterType((*TupuAdvertisingResult)(nil), "imagerecognition.TupuAdvertisingResult")
	proto.RegisterType((*TupuViolenceTerrorResult)(nil), "imagerecognition.TupuViolenceTerrorResult")
	proto.RegisterType((*TupuSearchGalleryResult)(nil), "imagerecognition.TupuSearchGalleryResult")
	proto.RegisterType((*TupuRecognitionResult)(nil), "imagerecognition.TupuRecognitionResult")
	proto.RegisterEnum("imagerecognition.ImagePresent", ImagePresent_name, ImagePresent_value)
	proto.RegisterEnum("imagerecognition.RecognizeType", RecognizeType_name, RecognizeType_value)
	proto.RegisterEnum("imagerecognition.BriefResult", BriefResult_name, BriefResult_value)
	proto.RegisterEnum("imagerecognition.PornClassifyResult_RESULT", PornClassifyResult_RESULT_name, PornClassifyResult_RESULT_value)
	proto.RegisterEnum("imagerecognition.PoliticanMatchResult_RESULT", PoliticanMatchResult_RESULT_name, PoliticanMatchResult_RESULT_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ImageRecognition service

type ImageRecognitionClient interface {
	Recognize(ctx context.Context, in *RecognizeReq, opts ...grpc.CallOption) (*RecognizeRsp, error)
	GetViolationNoticeImage(ctx context.Context, in *GetViolationNoticeImageReq, opts ...grpc.CallOption) (*GetViolationNoticeImageRsp, error)
	// 错误率太高，老告警，因此把错误码放到pb里返回跳过检查, 新增接口
	// 同步
	RecognizeSync(ctx context.Context, in *RecognizeSyncReq, opts ...grpc.CallOption) (*RecognizeSyncResp, error)
	// 同步
	RecognizeAsync(ctx context.Context, in *RecognizeAsyncReq, opts ...grpc.CallOption) (*RecognizeAsyncResp, error)
}

type imageRecognitionClient struct {
	cc *grpc.ClientConn
}

func NewImageRecognitionClient(cc *grpc.ClientConn) ImageRecognitionClient {
	return &imageRecognitionClient{cc}
}

func (c *imageRecognitionClient) Recognize(ctx context.Context, in *RecognizeReq, opts ...grpc.CallOption) (*RecognizeRsp, error) {
	out := new(RecognizeRsp)
	err := grpc.Invoke(ctx, "/imagerecognition.ImageRecognition/Recognize", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageRecognitionClient) GetViolationNoticeImage(ctx context.Context, in *GetViolationNoticeImageReq, opts ...grpc.CallOption) (*GetViolationNoticeImageRsp, error) {
	out := new(GetViolationNoticeImageRsp)
	err := grpc.Invoke(ctx, "/imagerecognition.ImageRecognition/GetViolationNoticeImage", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageRecognitionClient) RecognizeSync(ctx context.Context, in *RecognizeSyncReq, opts ...grpc.CallOption) (*RecognizeSyncResp, error) {
	out := new(RecognizeSyncResp)
	err := grpc.Invoke(ctx, "/imagerecognition.ImageRecognition/RecognizeSync", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageRecognitionClient) RecognizeAsync(ctx context.Context, in *RecognizeAsyncReq, opts ...grpc.CallOption) (*RecognizeAsyncResp, error) {
	out := new(RecognizeAsyncResp)
	err := grpc.Invoke(ctx, "/imagerecognition.ImageRecognition/RecognizeAsync", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ImageRecognition service

type ImageRecognitionServer interface {
	Recognize(context.Context, *RecognizeReq) (*RecognizeRsp, error)
	GetViolationNoticeImage(context.Context, *GetViolationNoticeImageReq) (*GetViolationNoticeImageRsp, error)
	// 错误率太高，老告警，因此把错误码放到pb里返回跳过检查, 新增接口
	// 同步
	RecognizeSync(context.Context, *RecognizeSyncReq) (*RecognizeSyncResp, error)
	// 同步
	RecognizeAsync(context.Context, *RecognizeAsyncReq) (*RecognizeAsyncResp, error)
}

func RegisterImageRecognitionServer(s *grpc.Server, srv ImageRecognitionServer) {
	s.RegisterService(&_ImageRecognition_serviceDesc, srv)
}

func _ImageRecognition_Recognize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecognizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageRecognitionServer).Recognize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imagerecognition.ImageRecognition/Recognize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageRecognitionServer).Recognize(ctx, req.(*RecognizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageRecognition_GetViolationNoticeImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetViolationNoticeImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageRecognitionServer).GetViolationNoticeImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imagerecognition.ImageRecognition/GetViolationNoticeImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageRecognitionServer).GetViolationNoticeImage(ctx, req.(*GetViolationNoticeImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageRecognition_RecognizeSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecognizeSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageRecognitionServer).RecognizeSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imagerecognition.ImageRecognition/RecognizeSync",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageRecognitionServer).RecognizeSync(ctx, req.(*RecognizeSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImageRecognition_RecognizeAsync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecognizeAsyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageRecognitionServer).RecognizeAsync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/imagerecognition.ImageRecognition/RecognizeAsync",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageRecognitionServer).RecognizeAsync(ctx, req.(*RecognizeAsyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ImageRecognition_serviceDesc = grpc.ServiceDesc{
	ServiceName: "imagerecognition.ImageRecognition",
	HandlerType: (*ImageRecognitionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Recognize",
			Handler:    _ImageRecognition_Recognize_Handler,
		},
		{
			MethodName: "GetViolationNoticeImage",
			Handler:    _ImageRecognition_GetViolationNoticeImage_Handler,
		},
		{
			MethodName: "RecognizeSync",
			Handler:    _ImageRecognition_RecognizeSync_Handler,
		},
		{
			MethodName: "RecognizeAsync",
			Handler:    _ImageRecognition_RecognizeAsync_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/imagerecognition/imagerecognition.proto",
}

func (m *Image) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Image) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.Present))
	if m.Raw != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.Raw)))
		i += copy(dAtA[i:], m.Raw)
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.File)))
	i += copy(dAtA[i:], m.File)
	return i, nil
}

func (m *ImageRecognizeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImageRecognizeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.TransId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.RecognizeType))
	if len(m.ImageList) > 0 {
		for _, msg := range m.ImageList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintImagerecognition(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RecognizeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecognizeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Req == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.Req.Size()))
		n1, err := m.Req.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	if m.WithDetailedResult {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.RequestTimeout))
	dAtA[i] = 0x20
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.TryCount))
	return i, nil
}

func (m *PornClassifyResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PornClassifyResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.RequireReview))
	return i, nil
}

func (m *PoliticanMatchResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PoliticanMatchResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.Result))
	return i, nil
}

func (m *ImageRecognizeResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImageRecognizeResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.ImgName)))
	i += copy(dAtA[i:], m.ImgName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.Result))
	if m.PornClassify != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.PornClassify.Size()))
		n2, err := m.PornClassify.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.PoliticanMatch != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.PoliticanMatch.Size()))
		n3, err := m.PoliticanMatch.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *ImageRecognizeRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImageRecognizeRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.TransId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.RecognizeType))
	if len(m.Results) > 0 {
		for _, msg := range m.Results {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintImagerecognition(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RecognizeRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecognizeRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Rsp == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("rsp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.Rsp.Size()))
		n4, err := m.Rsp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *GetViolationNoticeImageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetViolationNoticeImageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetViolationNoticeImageRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetViolationNoticeImageRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ImgData != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.ImgData)))
		i += copy(dAtA[i:], m.ImgData)
	}
	return i, nil
}

func (m *RecognizeSyncReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecognizeSyncReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Req == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.Req.Size()))
		n5, err := m.Req.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.RequestTimeout))
	return i, nil
}

func (m *RecognizeSyncResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecognizeSyncResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.ErrorCode))
	if m.Rsp != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.Rsp.Size()))
		n6, err := m.Rsp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *RecognizeAsyncReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecognizeAsyncReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Req == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.Req.Size()))
		n7, err := m.Req.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.RequestTimeout))
	dAtA[i] = 0x18
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.TryCount))
	return i, nil
}

func (m *RecognizeAsyncResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecognizeAsyncResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.ErrorCode))
	if m.Rsp != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.Rsp.Size()))
		n8, err := m.Rsp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *TupuPornResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TupuPornResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.ImageName)))
	i += copy(dAtA[i:], m.ImageName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.BriefResult))
	dAtA[i] = 0x18
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.Label))
	dAtA[i] = 0x20
	i++
	if m.Review {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x29
	i++
	i = encodeFixed64Imagerecognition(dAtA, i, uint64(math3.Float64bits(float64(m.Rate))))
	dAtA[i] = 0x32
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.Tab)))
	i += copy(dAtA[i:], m.Tab)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.ZipFileName)))
	i += copy(dAtA[i:], m.ZipFileName)
	return i, nil
}

func (m *TupuPoliticianResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TupuPoliticianResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.ImageName)))
	i += copy(dAtA[i:], m.ImageName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.BriefResult))
	dAtA[i] = 0x18
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.Label))
	dAtA[i] = 0x20
	i++
	if m.Review {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x29
	i++
	i = encodeFixed64Imagerecognition(dAtA, i, uint64(math3.Float64bits(float64(m.Similarity))))
	dAtA[i] = 0x32
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.FaceId)))
	i += copy(dAtA[i:], m.FaceId)
	return i, nil
}

func (m *TupuAdvertisingResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TupuAdvertisingResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.ImageName)))
	i += copy(dAtA[i:], m.ImageName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.BriefResult))
	dAtA[i] = 0x18
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.Label))
	dAtA[i] = 0x20
	i++
	if m.Review {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *TupuViolenceTerrorResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TupuViolenceTerrorResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.ImageName)))
	i += copy(dAtA[i:], m.ImageName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.BriefResult))
	dAtA[i] = 0x18
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.Label))
	dAtA[i] = 0x20
	i++
	if m.Review {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *TupuSearchGalleryResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TupuSearchGalleryResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(len(m.ImageName)))
	i += copy(dAtA[i:], m.ImageName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.BriefResult))
	dAtA[i] = 0x18
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.Label))
	dAtA[i] = 0x20
	i++
	if m.Review {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *TupuRecognitionResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TupuRecognitionResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintImagerecognition(dAtA, i, uint64(m.Result))
	if m.PornResult != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.PornResult.Size()))
		n9, err := m.PornResult.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if m.PoliticianResult != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.PoliticianResult.Size()))
		n10, err := m.PoliticianResult.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if m.AdvertisingResult != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.AdvertisingResult.Size()))
		n11, err := m.AdvertisingResult.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if m.ViolenceTerrorResult != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.ViolenceTerrorResult.Size()))
		n12, err := m.ViolenceTerrorResult.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if m.SearchGalleryResult != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintImagerecognition(dAtA, i, uint64(m.SearchGalleryResult.Size()))
		n13, err := m.SearchGalleryResult.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	return i, nil
}

func encodeFixed64Imagerecognition(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Imagerecognition(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintImagerecognition(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *Image) Size() (n int) {
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovImagerecognition(uint64(l))
	n += 1 + sovImagerecognition(uint64(m.Present))
	if m.Raw != nil {
		l = len(m.Raw)
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	l = len(m.Url)
	n += 1 + l + sovImagerecognition(uint64(l))
	l = len(m.File)
	n += 1 + l + sovImagerecognition(uint64(l))
	return n
}

func (m *ImageRecognizeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovImagerecognition(uint64(m.TransId))
	n += 1 + sovImagerecognition(uint64(m.RecognizeType))
	if len(m.ImageList) > 0 {
		for _, e := range m.ImageList {
			l = e.Size()
			n += 1 + l + sovImagerecognition(uint64(l))
		}
	}
	return n
}

func (m *RecognizeReq) Size() (n int) {
	var l int
	_ = l
	if m.Req != nil {
		l = m.Req.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	n += 2
	n += 1 + sovImagerecognition(uint64(m.RequestTimeout))
	n += 1 + sovImagerecognition(uint64(m.TryCount))
	return n
}

func (m *PornClassifyResult) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovImagerecognition(uint64(m.Result))
	n += 1 + sovImagerecognition(uint64(m.RequireReview))
	return n
}

func (m *PoliticanMatchResult) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovImagerecognition(uint64(m.Result))
	return n
}

func (m *ImageRecognizeResult) Size() (n int) {
	var l int
	_ = l
	l = len(m.ImgName)
	n += 1 + l + sovImagerecognition(uint64(l))
	n += 1 + sovImagerecognition(uint64(m.Result))
	if m.PornClassify != nil {
		l = m.PornClassify.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	if m.PoliticanMatch != nil {
		l = m.PoliticanMatch.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	return n
}

func (m *ImageRecognizeRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovImagerecognition(uint64(m.TransId))
	n += 1 + sovImagerecognition(uint64(m.RecognizeType))
	if len(m.Results) > 0 {
		for _, e := range m.Results {
			l = e.Size()
			n += 1 + l + sovImagerecognition(uint64(l))
		}
	}
	return n
}

func (m *RecognizeRsp) Size() (n int) {
	var l int
	_ = l
	if m.Rsp != nil {
		l = m.Rsp.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	return n
}

func (m *GetViolationNoticeImageReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetViolationNoticeImageRsp) Size() (n int) {
	var l int
	_ = l
	if m.ImgData != nil {
		l = len(m.ImgData)
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	return n
}

func (m *RecognizeSyncReq) Size() (n int) {
	var l int
	_ = l
	if m.Req != nil {
		l = m.Req.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	n += 1 + sovImagerecognition(uint64(m.RequestTimeout))
	return n
}

func (m *RecognizeSyncResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovImagerecognition(uint64(m.ErrorCode))
	if m.Rsp != nil {
		l = m.Rsp.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	return n
}

func (m *RecognizeAsyncReq) Size() (n int) {
	var l int
	_ = l
	if m.Req != nil {
		l = m.Req.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	n += 1 + sovImagerecognition(uint64(m.RequestTimeout))
	n += 1 + sovImagerecognition(uint64(m.TryCount))
	return n
}

func (m *RecognizeAsyncResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovImagerecognition(uint64(m.ErrorCode))
	if m.Rsp != nil {
		l = m.Rsp.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	return n
}

func (m *TupuPornResult) Size() (n int) {
	var l int
	_ = l
	l = len(m.ImageName)
	n += 1 + l + sovImagerecognition(uint64(l))
	n += 1 + sovImagerecognition(uint64(m.BriefResult))
	n += 1 + sovImagerecognition(uint64(m.Label))
	n += 2
	n += 9
	l = len(m.Tab)
	n += 1 + l + sovImagerecognition(uint64(l))
	l = len(m.ZipFileName)
	n += 1 + l + sovImagerecognition(uint64(l))
	return n
}

func (m *TupuPoliticianResult) Size() (n int) {
	var l int
	_ = l
	l = len(m.ImageName)
	n += 1 + l + sovImagerecognition(uint64(l))
	n += 1 + sovImagerecognition(uint64(m.BriefResult))
	n += 1 + sovImagerecognition(uint64(m.Label))
	n += 2
	n += 9
	l = len(m.FaceId)
	n += 1 + l + sovImagerecognition(uint64(l))
	return n
}

func (m *TupuAdvertisingResult) Size() (n int) {
	var l int
	_ = l
	l = len(m.ImageName)
	n += 1 + l + sovImagerecognition(uint64(l))
	n += 1 + sovImagerecognition(uint64(m.BriefResult))
	n += 1 + sovImagerecognition(uint64(m.Label))
	n += 2
	return n
}

func (m *TupuViolenceTerrorResult) Size() (n int) {
	var l int
	_ = l
	l = len(m.ImageName)
	n += 1 + l + sovImagerecognition(uint64(l))
	n += 1 + sovImagerecognition(uint64(m.BriefResult))
	n += 1 + sovImagerecognition(uint64(m.Label))
	n += 2
	return n
}

func (m *TupuSearchGalleryResult) Size() (n int) {
	var l int
	_ = l
	l = len(m.ImageName)
	n += 1 + l + sovImagerecognition(uint64(l))
	n += 1 + sovImagerecognition(uint64(m.BriefResult))
	n += 1 + sovImagerecognition(uint64(m.Label))
	n += 2
	return n
}

func (m *TupuRecognitionResult) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovImagerecognition(uint64(m.Result))
	if m.PornResult != nil {
		l = m.PornResult.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	if m.PoliticianResult != nil {
		l = m.PoliticianResult.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	if m.AdvertisingResult != nil {
		l = m.AdvertisingResult.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	if m.ViolenceTerrorResult != nil {
		l = m.ViolenceTerrorResult.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	if m.SearchGalleryResult != nil {
		l = m.SearchGalleryResult.Size()
		n += 1 + l + sovImagerecognition(uint64(l))
	}
	return n
}

func sovImagerecognition(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozImagerecognition(x uint64) (n int) {
	return sovImagerecognition(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *Image) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Image: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Image: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Present", wireType)
			}
			m.Present = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Present |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Raw", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Raw = append(m.Raw[:0], dAtA[iNdEx:postIndex]...)
			if m.Raw == nil {
				m.Raw = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field File", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.File = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("present")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImageRecognizeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImageRecognizeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImageRecognizeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TransId", wireType)
			}
			m.TransId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TransId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecognizeType", wireType)
			}
			m.RecognizeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecognizeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageList = append(m.ImageList, &Image{})
			if err := m.ImageList[len(m.ImageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("trans_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recognize_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecognizeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecognizeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecognizeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Req", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Req == nil {
				m.Req = &ImageRecognizeReq{}
			}
			if err := m.Req.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WithDetailedResult", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithDetailedResult = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestTimeout", wireType)
			}
			m.RequestTimeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestTimeout |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TryCount", wireType)
			}
			m.TryCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TryCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PornClassifyResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PornClassifyResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PornClassifyResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequireReview", wireType)
			}
			m.RequireReview = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequireReview |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("result")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("require_review")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PoliticanMatchResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PoliticanMatchResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PoliticanMatchResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImageRecognizeResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImageRecognizeResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImageRecognizeResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PornClassify", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PornClassify == nil {
				m.PornClassify = &PornClassifyResult{}
			}
			if err := m.PornClassify.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PoliticanMatch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PoliticanMatch == nil {
				m.PoliticanMatch = &PoliticanMatchResult{}
			}
			if err := m.PoliticanMatch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("img_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImageRecognizeRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImageRecognizeRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImageRecognizeRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TransId", wireType)
			}
			m.TransId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TransId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecognizeType", wireType)
			}
			m.RecognizeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecognizeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Results", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Results = append(m.Results, &ImageRecognizeResult{})
			if err := m.Results[len(m.Results)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("trans_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recognize_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecognizeRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecognizeRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecognizeRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rsp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Rsp == nil {
				m.Rsp = &ImageRecognizeRsp{}
			}
			if err := m.Rsp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rsp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetViolationNoticeImageReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetViolationNoticeImageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetViolationNoticeImageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetViolationNoticeImageRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetViolationNoticeImageRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetViolationNoticeImageRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgData = append(m.ImgData[:0], dAtA[iNdEx:postIndex]...)
			if m.ImgData == nil {
				m.ImgData = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("img_data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecognizeSyncReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecognizeSyncReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecognizeSyncReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Req", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Req == nil {
				m.Req = &ImageRecognizeReq{}
			}
			if err := m.Req.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestTimeout", wireType)
			}
			m.RequestTimeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestTimeout |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecognizeSyncResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecognizeSyncResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecognizeSyncResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrorCode", wireType)
			}
			m.ErrorCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ErrorCode |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rsp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Rsp == nil {
				m.Rsp = &ImageRecognizeRsp{}
			}
			if err := m.Rsp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecognizeAsyncReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecognizeAsyncReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecognizeAsyncReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Req", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Req == nil {
				m.Req = &ImageRecognizeReq{}
			}
			if err := m.Req.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestTimeout", wireType)
			}
			m.RequestTimeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestTimeout |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TryCount", wireType)
			}
			m.TryCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TryCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecognizeAsyncResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecognizeAsyncResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecognizeAsyncResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrorCode", wireType)
			}
			m.ErrorCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ErrorCode |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rsp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Rsp == nil {
				m.Rsp = &ImageRecognizeRsp{}
			}
			if err := m.Rsp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TupuPornResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TupuPornResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TupuPornResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BriefResult", wireType)
			}
			m.BriefResult = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BriefResult |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Label", wireType)
			}
			m.Label = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Label |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Review", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Review = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Rate = float64(math4.Float64frombits(v))
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tab", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Tab = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ZipFileName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ZipFileName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("image_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("brief_result")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("label")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("review")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TupuPoliticianResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TupuPoliticianResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TupuPoliticianResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BriefResult", wireType)
			}
			m.BriefResult = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BriefResult |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Label", wireType)
			}
			m.Label = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Label |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Review", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Review = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Similarity", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Similarity = float64(math4.Float64frombits(v))
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("image_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("brief_result")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("label")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("review")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TupuAdvertisingResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TupuAdvertisingResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TupuAdvertisingResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BriefResult", wireType)
			}
			m.BriefResult = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BriefResult |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Label", wireType)
			}
			m.Label = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Label |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Review", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Review = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("image_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("brief_result")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("label")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("review")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TupuViolenceTerrorResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TupuViolenceTerrorResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TupuViolenceTerrorResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BriefResult", wireType)
			}
			m.BriefResult = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BriefResult |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Label", wireType)
			}
			m.Label = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Label |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Review", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Review = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("image_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("brief_result")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("label")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("review")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TupuSearchGalleryResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TupuSearchGalleryResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TupuSearchGalleryResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BriefResult", wireType)
			}
			m.BriefResult = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BriefResult |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Label", wireType)
			}
			m.Label = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Label |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Review", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Review = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("image_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("brief_result")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("label")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("review")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TupuRecognitionResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TupuRecognitionResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TupuRecognitionResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PornResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PornResult == nil {
				m.PornResult = &TupuPornResult{}
			}
			if err := m.PornResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PoliticianResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PoliticianResult == nil {
				m.PoliticianResult = &TupuPoliticianResult{}
			}
			if err := m.PoliticianResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdvertisingResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AdvertisingResult == nil {
				m.AdvertisingResult = &TupuAdvertisingResult{}
			}
			if err := m.AdvertisingResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ViolenceTerrorResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ViolenceTerrorResult == nil {
				m.ViolenceTerrorResult = &TupuViolenceTerrorResult{}
			}
			if err := m.ViolenceTerrorResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SearchGalleryResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImagerecognition
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SearchGalleryResult == nil {
				m.SearchGalleryResult = &TupuSearchGalleryResult{}
			}
			if err := m.SearchGalleryResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImagerecognition(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImagerecognition
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipImagerecognition(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowImagerecognition
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowImagerecognition
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthImagerecognition
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowImagerecognition
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipImagerecognition(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthImagerecognition = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowImagerecognition   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/imagerecognition/imagerecognition.proto", fileDescriptorImagerecognition)
}

var fileDescriptorImagerecognition = []byte{
	// 1507 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0xcd, 0x6f, 0xdb, 0x46,
	0x16, 0x37, 0x45, 0x59, 0xb6, 0x9e, 0x6c, 0x87, 0x9e, 0x75, 0x62, 0xad, 0x36, 0x51, 0xb4, 0xb4,
	0x91, 0x38, 0x8e, 0xe5, 0x20, 0xc2, 0x6e, 0x0e, 0x81, 0x37, 0x58, 0x45, 0x56, 0xbc, 0xc4, 0xca,
	0x94, 0x40, 0xc9, 0xce, 0x26, 0x8b, 0x80, 0xa5, 0xa5, 0xb1, 0x32, 0x08, 0x25, 0xd2, 0x43, 0xca,
	0xa9, 0x7c, 0xea, 0xb1, 0x48, 0x0e, 0xfd, 0xb8, 0x15, 0x28, 0x50, 0xb4, 0xf5, 0xbf, 0xd1, 0x7b,
	0x50, 0xa0, 0x68, 0xff, 0x82, 0xa2, 0x48, 0x80, 0xc2, 0xd7, 0x02, 0xed, 0xbd, 0x98, 0x21, 0x29,
	0x51, 0x96, 0x9c, 0xb8, 0x68, 0x1b, 0xf8, 0xfa, 0x7b, 0x6f, 0xde, 0xc7, 0x8f, 0xef, 0x8b, 0x70,
	0xdd, 0xa1, 0xf5, 0x1b, 0xa4, 0x65, 0x34, 0x31, 0xc5, 0x75, 0xab, 0xd9, 0x26, 0x2e, 0xb1, 0xda,
	0x43, 0xc0, 0xaa, 0x4d, 0x2d, 0xd7, 0x42, 0xd2, 0x71, 0x3c, 0xb5, 0x58, 0xb7, 0x5a, 0x2d, 0xab,
	0x7d, 0xc3, 0x35, 0xf7, 0x6d, 0x52, 0x7f, 0x62, 0xe2, 0x1b, 0xce, 0x93, 0x9d, 0x0e, 0x31, 0x5d,
	0xd2, 0x76, 0xbb, 0x36, 0xf6, 0xde, 0xc9, 0xcf, 0x05, 0x18, 0x57, 0xd8, 0x53, 0x94, 0x84, 0x68,
	0xdb, 0x68, 0xe1, 0xa4, 0x90, 0x89, 0x2c, 0xc5, 0xef, 0x46, 0x5f, 0x7c, 0x7f, 0x79, 0x4c, 0xe3,
	0x08, 0x4a, 0xc3, 0x84, 0x4d, 0xb1, 0x83, 0xdb, 0x6e, 0x32, 0x92, 0x89, 0x2c, 0x4d, 0xfb, 0xc2,
	0x00, 0x44, 0x17, 0x40, 0xa4, 0xc6, 0xd3, 0xa4, 0x98, 0x11, 0x96, 0xa6, 0x7c, 0x19, 0x03, 0x18,
	0xde, 0xa1, 0x66, 0x32, 0x9a, 0x11, 0x7a, 0x06, 0x19, 0xc0, 0x3c, 0xed, 0x12, 0x13, 0x27, 0xc7,
	0x43, 0x02, 0x8e, 0xc8, 0x9f, 0x08, 0x30, 0xcb, 0xa3, 0xd1, 0xbc, 0x44, 0x0e, 0xb0, 0x86, 0xf7,
	0xd0, 0x65, 0x98, 0x74, 0xa9, 0xd1, 0x76, 0x74, 0xd2, 0xe0, 0xd1, 0xf5, 0x02, 0xe0, 0xa8, 0xd2,
	0x40, 0xd7, 0x61, 0x86, 0x06, 0x0f, 0x74, 0x96, 0xdc, 0x40, 0x9c, 0xd3, 0x3d, 0x59, 0xad, 0x6b,
	0x63, 0x74, 0x0b, 0x80, 0x73, 0xa5, 0x9b, 0xc4, 0x71, 0x93, 0x62, 0x46, 0x5c, 0x4a, 0xe4, 0xe6,
	0x57, 0x87, 0x68, 0xf5, 0xc2, 0x88, 0x73, 0xbc, 0x44, 0x1c, 0x57, 0xfe, 0x56, 0x80, 0xa9, 0x81,
	0xb0, 0xfe, 0x09, 0x22, 0xc5, 0x7b, 0x3c, 0xa2, 0x44, 0x6e, 0xe1, 0x24, 0x0b, 0xa1, 0x17, 0x1a,
	0xd3, 0x47, 0xb7, 0x60, 0xee, 0x29, 0x71, 0x1f, 0xeb, 0x0d, 0xec, 0x1a, 0xc4, 0xc4, 0x0d, 0x9d,
	0x62, 0xa7, 0x63, 0x32, 0x6a, 0x85, 0xa5, 0x49, 0x3f, 0x64, 0xc4, 0x34, 0xd6, 0x7d, 0x05, 0x8d,
	0xcb, 0x51, 0x16, 0xce, 0x51, 0xbc, 0xd7, 0xc1, 0x8e, 0xab, 0xbb, 0xa4, 0x85, 0xad, 0x8e, 0xcb,
	0x19, 0x0f, 0xb2, 0x9c, 0xf1, 0x85, 0x35, 0x4f, 0x86, 0xfe, 0x0e, 0x71, 0x97, 0x76, 0xf5, 0xba,
	0xd5, 0x69, 0xbb, 0xfc, 0x13, 0x04, 0x8a, 0x93, 0x2e, 0xed, 0x16, 0x18, 0x2a, 0x3f, 0x13, 0x00,
	0x55, 0x2c, 0xda, 0x2e, 0x98, 0x86, 0xe3, 0x90, 0xdd, 0xae, 0xef, 0xe8, 0x22, 0xc4, 0xfc, 0x90,
	0xc2, 0x64, 0xfb, 0x98, 0xc7, 0xf5, 0x5e, 0x87, 0x50, 0xac, 0x53, 0xbc, 0x4f, 0xf0, 0xd3, 0xe3,
	0x5c, 0x73, 0x99, 0xc6, 0x45, 0xf2, 0x35, 0x88, 0x69, 0xc5, 0xea, 0x56, 0xa9, 0x86, 0xe2, 0x30,
	0x5e, 0x29, 0x6b, 0xea, 0x03, 0x69, 0x0c, 0x4d, 0x42, 0xb4, 0x5a, 0xfc, 0xdf, 0x03, 0x49, 0x40,
	0x00, 0x31, 0xb5, 0xac, 0x6d, 0xe6, 0x4b, 0x52, 0x44, 0x36, 0x61, 0xae, 0x62, 0x99, 0xc4, 0x25,
	0x75, 0xa3, 0xbd, 0x69, 0xb8, 0xf5, 0xc7, 0xa7, 0x89, 0x46, 0xbe, 0xd5, 0x73, 0x30, 0x0d, 0xf1,
	0x4a, 0xb9, 0xa4, 0xd4, 0x94, 0x42, 0x5e, 0x95, 0xc6, 0xd0, 0x2c, 0x4c, 0xab, 0xe5, 0x9a, 0xde,
	0x87, 0x22, 0xcc, 0xdb, 0x96, 0xfa, 0xdf, 0xf2, 0x7d, 0x55, 0x12, 0xe5, 0x9f, 0x04, 0x98, 0x3b,
	0xfe, 0x7d, 0xb8, 0xbb, 0xcb, 0x30, 0x49, 0x5a, 0x4d, 0x7d, 0xa8, 0x13, 0x26, 0x48, 0xab, 0xa9,
	0xb2, 0x66, 0xe8, 0xc7, 0x13, 0x19, 0xc1, 0x8e, 0x02, 0xd3, 0xb6, 0x45, 0xdb, 0x7a, 0xdd, 0xa7,
	0x94, 0x7f, 0xa2, 0x44, 0x6e, 0x71, 0xb8, 0x3a, 0x86, 0x89, 0xd7, 0xa6, 0xec, 0x10, 0x86, 0xca,
	0x70, 0xce, 0x0e, 0x08, 0xd1, 0x5b, 0x8c, 0x11, 0xfe, 0x19, 0x13, 0xb9, 0x2b, 0xa3, 0x8c, 0x0d,
	0x33, 0xa7, 0xcd, 0xd8, 0x03, 0xa8, 0xfc, 0xe5, 0x70, 0x73, 0x39, 0xf6, 0x1f, 0xdc, 0x5c, 0xff,
	0x86, 0x09, 0x8f, 0x09, 0xc7, 0xef, 0xac, 0x2b, 0x6f, 0xee, 0x0b, 0x1e, 0x6c, 0xf0, 0x4c, 0x2e,
	0x86, 0xbb, 0xcc, 0xb1, 0x79, 0x97, 0x39, 0xf6, 0xa9, 0xbb, 0xcc, 0xb1, 0x35, 0xa6, 0x2f, 0x5f,
	0x84, 0xd4, 0x06, 0x76, 0xb7, 0x89, 0x65, 0x1a, 0x4c, 0x4d, 0xb5, 0x5c, 0x52, 0xc7, 0xbe, 0xee,
	0x9e, 0xfc, 0xaf, 0x93, 0xa5, 0x1e, 0x25, 0xac, 0x06, 0x1a, 0x86, 0x6b, 0x70, 0xbf, 0x53, 0xa1,
	0x1a, 0x58, 0x37, 0x5c, 0x43, 0x7e, 0x17, 0xa4, 0x9e, 0xc7, 0x6a, 0xb7, 0x5d, 0xff, 0x1d, 0xd3,
	0x60, 0x44, 0x57, 0x47, 0x4e, 0xee, 0x6a, 0xd9, 0x82, 0xd9, 0x63, 0x9e, 0x1d, 0x1b, 0x2d, 0x00,
	0x60, 0x4a, 0x2d, 0xaa, 0xd7, 0xad, 0x06, 0xab, 0x5a, 0x61, 0x69, 0xdc, 0x7f, 0x1e, 0xe7, 0x78,
	0xc1, 0x6a, 0xe0, 0x80, 0xc7, 0x08, 0x2f, 0xa1, 0xd3, 0xf3, 0xf8, 0xa9, 0x10, 0xf2, 0x98, 0x77,
	0xde, 0x66, 0xb2, 0x83, 0x23, 0x4c, 0x1c, 0x39, 0xc2, 0x6c, 0x40, 0xc7, 0xa3, 0xfb, 0x93, 0x09,
	0xf9, 0x59, 0x80, 0x99, 0x5a, 0xc7, 0xee, 0xb0, 0xfe, 0xf5, 0x67, 0xc6, 0x42, 0xb0, 0x51, 0x86,
	0xa6, 0x86, 0xb7, 0x3e, 0xf8, 0xdc, 0xb8, 0x0a, 0x53, 0x3b, 0x94, 0xe0, 0x5d, 0x7d, 0xc4, 0xf4,
	0x48, 0x70, 0x89, 0x6f, 0x2d, 0x05, 0xe3, 0xa6, 0xb1, 0x83, 0xcd, 0xa4, 0x98, 0x89, 0xf4, 0xe2,
	0xf6, 0x20, 0x6f, 0xf8, 0xf0, 0xa1, 0x1b, 0xcd, 0x44, 0x7a, 0xdb, 0xc2, 0xc7, 0xd8, 0x5e, 0xa5,
	0x86, 0xeb, 0xed, 0x55, 0x21, 0xd8, 0xab, 0x0c, 0x61, 0x9b, 0xd8, 0x35, 0x76, 0x92, 0xb1, 0xf0,
	0x26, 0x76, 0x8d, 0x1d, 0xb4, 0x04, 0xd3, 0x07, 0xc4, 0xd6, 0xd9, 0xee, 0xf5, 0x82, 0x9f, 0x08,
	0x69, 0x24, 0x0e, 0x88, 0x7d, 0x8f, 0x98, 0x3c, 0x7c, 0xf9, 0x95, 0x00, 0x73, 0x5e, 0xda, 0x7c,
	0xa6, 0x10, 0xe3, 0xac, 0x25, 0xbf, 0x08, 0xe0, 0x90, 0x16, 0x31, 0x0d, 0x4a, 0xdc, 0xee, 0x00,
	0x05, 0x21, 0x1c, 0x5d, 0x82, 0x89, 0x5d, 0xa3, 0x8e, 0xd9, 0xb0, 0x0b, 0x93, 0x11, 0x63, 0xa0,
	0xd2, 0x90, 0x3f, 0x13, 0xe0, 0x3c, 0xcb, 0x32, 0xdf, 0xd8, 0xc7, 0xd4, 0x25, 0x0e, 0x69, 0x37,
	0xcf, 0x56, 0x9a, 0xf2, 0x17, 0x02, 0x24, 0x59, 0x84, 0x6c, 0x76, 0xe1, 0x76, 0x1d, 0xd7, 0x78,
	0x41, 0x9f, 0xb1, 0x20, 0x3f, 0x17, 0x60, 0x9e, 0x05, 0x59, 0xc5, 0x06, 0xad, 0x3f, 0xde, 0x30,
	0x4c, 0x13, 0xd3, 0xee, 0x19, 0x8b, 0xf1, 0x47, 0xd1, 0xfb, 0xd4, 0x5a, 0xbf, 0xe5, 0x4f, 0x75,
	0xff, 0xe4, 0x21, 0xc1, 0x37, 0x7c, 0xe8, 0x6a, 0x4b, 0xe4, 0x32, 0xc3, 0xe3, 0x63, 0x70, 0x46,
	0x68, 0x60, 0xf7, 0xe7, 0x45, 0x15, 0x66, 0xed, 0x5e, 0x1b, 0x05, 0x86, 0xc4, 0x93, 0x76, 0xfb,
	0xa8, 0xae, 0xd3, 0x24, 0xfb, 0x78, 0x1f, 0x6e, 0x03, 0x32, 0xfa, 0x55, 0x1b, 0x58, 0xf5, 0x2e,
	0x86, 0xab, 0xa3, 0xad, 0x0e, 0x55, 0xb9, 0x36, 0x6b, 0x0c, 0x15, 0xfe, 0x3b, 0x70, 0x61, 0xdf,
	0xaf, 0x35, 0xdd, 0xf5, 0xa6, 0xaa, 0x6f, 0x7b, 0x9c, 0xdb, 0x5e, 0x1e, 0x6d, 0x7b, 0x54, 0x7d,
	0x6a, 0x73, 0xfb, 0xa3, 0xaa, 0xf6, 0x11, 0x9c, 0x77, 0x78, 0xa1, 0xe8, 0x4d, 0xaf, 0x52, 0x02,
	0x07, 0x31, 0xee, 0xe0, 0xda, 0x68, 0x07, 0x23, 0x6a, 0x4b, 0xfb, 0x8b, 0x33, 0x0c, 0x2e, 0xdf,
	0x87, 0x29, 0x3e, 0xca, 0x2b, 0xfe, 0xdf, 0x8a, 0x04, 0x53, 0xca, 0xe6, 0x86, 0x5e, 0xd1, 0x8a,
	0x55, 0x5d, 0x55, 0x4a, 0xd2, 0xd8, 0x00, 0xa2, 0xe5, 0xef, 0x4b, 0xc2, 0x00, 0xb2, 0xa5, 0x95,
	0xa4, 0x08, 0xbb, 0x27, 0x7b, 0xc8, 0x3d, 0xa5, 0x54, 0x94, 0xc4, 0xe5, 0x0f, 0x04, 0x98, 0xd6,
	0x06, 0xae, 0x9f, 0xbf, 0xc1, 0xbc, 0x56, 0x2c, 0x94, 0x37, 0x54, 0xe5, 0x61, 0x51, 0x67, 0xe7,
	0xae, 0x5e, 0x28, 0xe5, 0xab, 0x55, 0xe5, 0x1e, 0x3b, 0x76, 0xd3, 0x90, 0x0a, 0x0b, 0xf9, 0x5d,
	0xaa, 0xe4, 0x55, 0x7d, 0x33, 0x5f, 0x2b, 0xfc, 0x47, 0x8a, 0x20, 0x19, 0xd2, 0x7d, 0x79, 0x7e,
	0x7d, 0xbb, 0xa8, 0xd5, 0x94, 0xaa, 0xa2, 0x6e, 0xe8, 0xca, 0x7a, 0x51, 0xad, 0x31, 0x1b, 0x51,
	0x74, 0x09, 0xfe, 0xda, 0xd7, 0xd9, 0x56, 0xca, 0xa5, 0xa2, 0x5a, 0x28, 0xea, 0xb5, 0xa2, 0xa6,
	0x95, 0x35, 0x69, 0x72, 0xb9, 0x03, 0x89, 0xbb, 0xa1, 0xe6, 0x40, 0x30, 0xe3, 0x1d, 0xc7, 0xfa,
	0x7a, 0xb1, 0xaa, 0x68, 0xc5, 0x75, 0x49, 0x08, 0x61, 0x6a, 0xb9, 0x56, 0xdd, 0xd2, 0x8a, 0x52,
	0x04, 0xcd, 0x81, 0xe4, 0x63, 0x5b, 0x6a, 0xa0, 0x19, 0x1d, 0x40, 0xab, 0x5b, 0x95, 0x4a, 0x59,
	0xab, 0x49, 0x93, 0xa1, 0xf7, 0x8a, 0xba, 0x9d, 0x2f, 0x29, 0xeb, 0x92, 0x94, 0x7b, 0x1e, 0x03,
	0x29, 0xbc, 0x2d, 0xd9, 0x37, 0x42, 0x5f, 0x09, 0x10, 0xef, 0xb1, 0x83, 0xd2, 0xc3, 0x1f, 0x31,
	0x7c, 0x1e, 0xa4, 0x5e, 0x2b, 0x77, 0x6c, 0xd9, 0x7e, 0xef, 0xf0, 0x48, 0x14, 0x9e, 0x1d, 0x1e,
	0x89, 0x13, 0xe4, 0xb6, 0x79, 0x9b, 0xde, 0x6e, 0x7c, 0x7c, 0x78, 0x24, 0x3e, 0xcc, 0xd2, 0xcc,
	0x1a, 0x7f, 0x43, 0x0e, 0xf0, 0xca, 0xcd, 0x2c, 0xeb, 0xb1, 0x95, 0x5c, 0xb6, 0xdf, 0x1b, 0x2b,
	0xff, 0xc8, 0x86, 0x2a, 0xfa, 0x4e, 0x26, 0x4b, 0x32, 0x6b, 0xa4, 0xd5, 0xbc, 0xb9, 0x42, 0x5a,
	0xcd, 0xdc, 0xca, 0xea, 0xea, 0xea, 0x9d, 0xcc, 0xff, 0xb3, 0x8d, 0x47, 0x99, 0xac, 0x99, 0x59,
	0xeb, 0x50, 0xf3, 0xe6, 0x4a, 0x87, 0x9a, 0x1e, 0x8e, 0x3e, 0x12, 0x60, 0xfe, 0x84, 0x1b, 0x11,
	0xad, 0x0c, 0x47, 0x7b, 0xf2, 0xb1, 0x99, 0xfa, 0x0d, 0xda, 0x8e, 0x2d, 0xa7, 0x58, 0xa6, 0x11,
	0x96, 0x69, 0x24, 0x5b, 0x66, 0x49, 0xc6, 0xb3, 0xe5, 0xcc, 0x1a, 0xdb, 0xd0, 0x77, 0xd0, 0xd7,
	0xe1, 0x8a, 0x63, 0xe7, 0x1f, 0x92, 0x5f, 0xc3, 0x9b, 0x7f, 0x99, 0xa6, 0x16, 0xde, 0xa8, 0x13,
	0x10, 0x2c, 0xbe, 0x4d, 0x82, 0xbf, 0x11, 0x60, 0x66, 0xf0, 0x76, 0x43, 0xaf, 0x8b, 0x34, 0xb8,
	0x3d, 0x53, 0x8b, 0x6f, 0x56, 0x0a, 0xf2, 0x89, 0xbe, 0xc5, 0x7c, 0x52, 0xb1, 0xf7, 0x0f, 0x8f,
	0xc4, 0x5f, 0xba, 0x77, 0xa5, 0x17, 0x2f, 0xd3, 0xc2, 0x77, 0x2f, 0xd3, 0xc2, 0x0f, 0x2f, 0xd3,
	0xc2, 0x87, 0xaf, 0xd2, 0x63, 0xbf, 0x06, 0x00, 0x00, 0xff, 0xff, 0xbc, 0xfd, 0xee, 0x17, 0xc9,
	0x11, 0x00, 0x00,
}
