// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cat-canteen/cat-canteen.proto

package cat_canteen // import "golang.52tt.com/protocol/services/cat-canteen"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LotteryResultType int32

const (
	LotteryResultType_LOTTERY_RESULT_UNSPECIFIED         LotteryResultType = 0
	LotteryResultType_LOTTERY_RESULT_UPGRADE_FAIL        LotteryResultType = 1
	LotteryResultType_LOTTERY_RESULT_UPGRADE_SUCCESS     LotteryResultType = 2
	LotteryResultType_LOTTERY_RESULT_CROSS_LEVEL_UPGRADE LotteryResultType = 3
)

var LotteryResultType_name = map[int32]string{
	0: "LOTTERY_RESULT_UNSPECIFIED",
	1: "LOTTERY_RESULT_UPGRADE_FAIL",
	2: "LOTTERY_RESULT_UPGRADE_SUCCESS",
	3: "LOTTERY_RESULT_CROSS_LEVEL_UPGRADE",
}
var LotteryResultType_value = map[string]int32{
	"LOTTERY_RESULT_UNSPECIFIED":         0,
	"LOTTERY_RESULT_UPGRADE_FAIL":        1,
	"LOTTERY_RESULT_UPGRADE_SUCCESS":     2,
	"LOTTERY_RESULT_CROSS_LEVEL_UPGRADE": 3,
}

func (x LotteryResultType) String() string {
	return proto.EnumName(LotteryResultType_name, int32(x))
}
func (LotteryResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{0}
}

// -------------------- 运营后台协议 begin ----------------------------------
type ResultType int32

const (
	ResultType_RESULT_UNSPECIFIED             ResultType = 0
	ResultType_RESULT_UPGRADE_FAIL            ResultType = 1
	ResultType_RESULT_UPGRADE_SUCCESS         ResultType = 2
	ResultType_RESULT_CROSS_LEVEL_UPGRADE     ResultType = 3
	ResultType_RESULT_UPGRADE_FAIL_WITH_PRIZE ResultType = 4
)

var ResultType_name = map[int32]string{
	0: "RESULT_UNSPECIFIED",
	1: "RESULT_UPGRADE_FAIL",
	2: "RESULT_UPGRADE_SUCCESS",
	3: "RESULT_CROSS_LEVEL_UPGRADE",
	4: "RESULT_UPGRADE_FAIL_WITH_PRIZE",
}
var ResultType_value = map[string]int32{
	"RESULT_UNSPECIFIED":             0,
	"RESULT_UPGRADE_FAIL":            1,
	"RESULT_UPGRADE_SUCCESS":         2,
	"RESULT_CROSS_LEVEL_UPGRADE":     3,
	"RESULT_UPGRADE_FAIL_WITH_PRIZE": 4,
}

func (x ResultType) String() string {
	return proto.EnumName(ResultType_name, int32(x))
}
func (ResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{1}
}

type LevelType int32

const (
	LevelType_LevelType_UNSPECIFIED LevelType = 0
	LevelType_LevelType_Level_ID_1  LevelType = 1
	LevelType_LevelType_Level_ID_2  LevelType = 2
	LevelType_LevelType_Level_ID_3  LevelType = 3
	LevelType_LevelType_Level_ID_4  LevelType = 4
	LevelType_LevelType_Level_ID_5  LevelType = 5
)

var LevelType_name = map[int32]string{
	0: "LevelType_UNSPECIFIED",
	1: "LevelType_Level_ID_1",
	2: "LevelType_Level_ID_2",
	3: "LevelType_Level_ID_3",
	4: "LevelType_Level_ID_4",
	5: "LevelType_Level_ID_5",
}
var LevelType_value = map[string]int32{
	"LevelType_UNSPECIFIED": 0,
	"LevelType_Level_ID_1":  1,
	"LevelType_Level_ID_2":  2,
	"LevelType_Level_ID_3":  3,
	"LevelType_Level_ID_4":  4,
	"LevelType_Level_ID_5":  5,
}

func (x LevelType) String() string {
	return proto.EnumName(LevelType_name, int32(x))
}
func (LevelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{2}
}

type UserLevelStatus_Status int32

const (
	UserLevelStatus_STATUS_UNSPECIFIED        UserLevelStatus_Status = 0
	UserLevelStatus_STATUS_TO_BE_OPEN         UserLevelStatus_Status = 1
	UserLevelStatus_STATUS_IN_OPENING         UserLevelStatus_Status = 2
	UserLevelStatus_STATUS_TEMPORARILY_CLOSED UserLevelStatus_Status = 3
	UserLevelStatus_STATUS_CLOSED             UserLevelStatus_Status = 4
)

var UserLevelStatus_Status_name = map[int32]string{
	0: "STATUS_UNSPECIFIED",
	1: "STATUS_TO_BE_OPEN",
	2: "STATUS_IN_OPENING",
	3: "STATUS_TEMPORARILY_CLOSED",
	4: "STATUS_CLOSED",
}
var UserLevelStatus_Status_value = map[string]int32{
	"STATUS_UNSPECIFIED":        0,
	"STATUS_TO_BE_OPEN":         1,
	"STATUS_IN_OPENING":         2,
	"STATUS_TEMPORARILY_CLOSED": 3,
	"STATUS_CLOSED":             4,
}

func (x UserLevelStatus_Status) String() string {
	return proto.EnumName(UserLevelStatus_Status_name, int32(x))
}
func (UserLevelStatus_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{24, 0}
}

type GetUserWinningRecordsReq_QueryType int32

const (
	GetUserWinningRecordsReq_QUERY_TYPE_UNSPECIFIED GetUserWinningRecordsReq_QueryType = 0
	GetUserWinningRecordsReq_QUERY_TYPE_ALL         GetUserWinningRecordsReq_QueryType = 1
	GetUserWinningRecordsReq_QUERY_TYPE_BONUS       GetUserWinningRecordsReq_QueryType = 2
)

var GetUserWinningRecordsReq_QueryType_name = map[int32]string{
	0: "QUERY_TYPE_UNSPECIFIED",
	1: "QUERY_TYPE_ALL",
	2: "QUERY_TYPE_BONUS",
}
var GetUserWinningRecordsReq_QueryType_value = map[string]int32{
	"QUERY_TYPE_UNSPECIFIED": 0,
	"QUERY_TYPE_ALL":         1,
	"QUERY_TYPE_BONUS":       2,
}

func (x GetUserWinningRecordsReq_QueryType) String() string {
	return proto.EnumName(GetUserWinningRecordsReq_QueryType_name, int32(x))
}
func (GetUserWinningRecordsReq_QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{40, 0}
}

type GetAllLevelConfReq_ConfType int32

const (
	GetAllLevelConfReq_ConfType_UNEXPECTED GetAllLevelConfReq_ConfType = 0
	GetAllLevelConfReq_ConfType_IN_EFFECT  GetAllLevelConfReq_ConfType = 1
	GetAllLevelConfReq_ConfType_PENDING    GetAllLevelConfReq_ConfType = 2
)

var GetAllLevelConfReq_ConfType_name = map[int32]string{
	0: "ConfType_UNEXPECTED",
	1: "ConfType_IN_EFFECT",
	2: "ConfType_PENDING",
}
var GetAllLevelConfReq_ConfType_value = map[string]int32{
	"ConfType_UNEXPECTED": 0,
	"ConfType_IN_EFFECT":  1,
	"ConfType_PENDING":    2,
}

func (x GetAllLevelConfReq_ConfType) String() string {
	return proto.EnumName(GetAllLevelConfReq_ConfType_name, int32(x))
}
func (GetAllLevelConfReq_ConfType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{54, 0}
}

type CatLightEffect struct {
	ConfId               uint32   `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	EffectUrl            string   `protobuf:"bytes,3,opt,name=effect_url,json=effectUrl,proto3" json:"effect_url,omitempty"`
	EffectMd5            string   `protobuf:"bytes,4,opt,name=effect_md5,json=effectMd5,proto3" json:"effect_md5,omitempty"`
	EffectPic            string   `protobuf:"bytes,5,opt,name=effect_pic,json=effectPic,proto3" json:"effect_pic,omitempty"`
	OpUser               string   `protobuf:"bytes,6,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	OpRemind             string   `protobuf:"bytes,7,opt,name=op_remind,json=opRemind,proto3" json:"op_remind,omitempty"`
	UpdateTime           int64    `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CatLightEffect) Reset()         { *m = CatLightEffect{} }
func (m *CatLightEffect) String() string { return proto.CompactTextString(m) }
func (*CatLightEffect) ProtoMessage()    {}
func (*CatLightEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{0}
}
func (m *CatLightEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatLightEffect.Unmarshal(m, b)
}
func (m *CatLightEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatLightEffect.Marshal(b, m, deterministic)
}
func (dst *CatLightEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatLightEffect.Merge(dst, src)
}
func (m *CatLightEffect) XXX_Size() int {
	return xxx_messageInfo_CatLightEffect.Size(m)
}
func (m *CatLightEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_CatLightEffect.DiscardUnknown(m)
}

var xxx_messageInfo_CatLightEffect proto.InternalMessageInfo

func (m *CatLightEffect) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *CatLightEffect) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *CatLightEffect) GetEffectUrl() string {
	if m != nil {
		return m.EffectUrl
	}
	return ""
}

func (m *CatLightEffect) GetEffectMd5() string {
	if m != nil {
		return m.EffectMd5
	}
	return ""
}

func (m *CatLightEffect) GetEffectPic() string {
	if m != nil {
		return m.EffectPic
	}
	return ""
}

func (m *CatLightEffect) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *CatLightEffect) GetOpRemind() string {
	if m != nil {
		return m.OpRemind
	}
	return ""
}

func (m *CatLightEffect) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetCatLightEffectsV2ByWorthReq struct {
	Worth                uint32   `protobuf:"varint,1,opt,name=worth,proto3" json:"worth,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCatLightEffectsV2ByWorthReq) Reset()         { *m = GetCatLightEffectsV2ByWorthReq{} }
func (m *GetCatLightEffectsV2ByWorthReq) String() string { return proto.CompactTextString(m) }
func (*GetCatLightEffectsV2ByWorthReq) ProtoMessage()    {}
func (*GetCatLightEffectsV2ByWorthReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{1}
}
func (m *GetCatLightEffectsV2ByWorthReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatLightEffectsV2ByWorthReq.Unmarshal(m, b)
}
func (m *GetCatLightEffectsV2ByWorthReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatLightEffectsV2ByWorthReq.Marshal(b, m, deterministic)
}
func (dst *GetCatLightEffectsV2ByWorthReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatLightEffectsV2ByWorthReq.Merge(dst, src)
}
func (m *GetCatLightEffectsV2ByWorthReq) XXX_Size() int {
	return xxx_messageInfo_GetCatLightEffectsV2ByWorthReq.Size(m)
}
func (m *GetCatLightEffectsV2ByWorthReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatLightEffectsV2ByWorthReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatLightEffectsV2ByWorthReq proto.InternalMessageInfo

func (m *GetCatLightEffectsV2ByWorthReq) GetWorth() uint32 {
	if m != nil {
		return m.Worth
	}
	return 0
}

type GetCatLightEffectsV2ByWorthResp struct {
	Cfg                  *CatLightEffect `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	NotifyFormat         string          `protobuf:"bytes,2,opt,name=notify_format,json=notifyFormat,proto3" json:"notify_format,omitempty"`
	EncourageText        string          `protobuf:"bytes,3,opt,name=encourage_text,json=encourageText,proto3" json:"encourage_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCatLightEffectsV2ByWorthResp) Reset()         { *m = GetCatLightEffectsV2ByWorthResp{} }
func (m *GetCatLightEffectsV2ByWorthResp) String() string { return proto.CompactTextString(m) }
func (*GetCatLightEffectsV2ByWorthResp) ProtoMessage()    {}
func (*GetCatLightEffectsV2ByWorthResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{2}
}
func (m *GetCatLightEffectsV2ByWorthResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatLightEffectsV2ByWorthResp.Unmarshal(m, b)
}
func (m *GetCatLightEffectsV2ByWorthResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatLightEffectsV2ByWorthResp.Marshal(b, m, deterministic)
}
func (dst *GetCatLightEffectsV2ByWorthResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatLightEffectsV2ByWorthResp.Merge(dst, src)
}
func (m *GetCatLightEffectsV2ByWorthResp) XXX_Size() int {
	return xxx_messageInfo_GetCatLightEffectsV2ByWorthResp.Size(m)
}
func (m *GetCatLightEffectsV2ByWorthResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatLightEffectsV2ByWorthResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatLightEffectsV2ByWorthResp proto.InternalMessageInfo

func (m *GetCatLightEffectsV2ByWorthResp) GetCfg() *CatLightEffect {
	if m != nil {
		return m.Cfg
	}
	return nil
}

func (m *GetCatLightEffectsV2ByWorthResp) GetNotifyFormat() string {
	if m != nil {
		return m.NotifyFormat
	}
	return ""
}

func (m *GetCatLightEffectsV2ByWorthResp) GetEncourageText() string {
	if m != nil {
		return m.EncourageText
	}
	return ""
}

// 中奖光效管理
type AddCatLightEffectsV2Req struct {
	Conf                 *CatLightEffect `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddCatLightEffectsV2Req) Reset()         { *m = AddCatLightEffectsV2Req{} }
func (m *AddCatLightEffectsV2Req) String() string { return proto.CompactTextString(m) }
func (*AddCatLightEffectsV2Req) ProtoMessage()    {}
func (*AddCatLightEffectsV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{3}
}
func (m *AddCatLightEffectsV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCatLightEffectsV2Req.Unmarshal(m, b)
}
func (m *AddCatLightEffectsV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCatLightEffectsV2Req.Marshal(b, m, deterministic)
}
func (dst *AddCatLightEffectsV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCatLightEffectsV2Req.Merge(dst, src)
}
func (m *AddCatLightEffectsV2Req) XXX_Size() int {
	return xxx_messageInfo_AddCatLightEffectsV2Req.Size(m)
}
func (m *AddCatLightEffectsV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCatLightEffectsV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_AddCatLightEffectsV2Req proto.InternalMessageInfo

func (m *AddCatLightEffectsV2Req) GetConf() *CatLightEffect {
	if m != nil {
		return m.Conf
	}
	return nil
}

type AddCatLightEffectsV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCatLightEffectsV2Resp) Reset()         { *m = AddCatLightEffectsV2Resp{} }
func (m *AddCatLightEffectsV2Resp) String() string { return proto.CompactTextString(m) }
func (*AddCatLightEffectsV2Resp) ProtoMessage()    {}
func (*AddCatLightEffectsV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{4}
}
func (m *AddCatLightEffectsV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCatLightEffectsV2Resp.Unmarshal(m, b)
}
func (m *AddCatLightEffectsV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCatLightEffectsV2Resp.Marshal(b, m, deterministic)
}
func (dst *AddCatLightEffectsV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCatLightEffectsV2Resp.Merge(dst, src)
}
func (m *AddCatLightEffectsV2Resp) XXX_Size() int {
	return xxx_messageInfo_AddCatLightEffectsV2Resp.Size(m)
}
func (m *AddCatLightEffectsV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCatLightEffectsV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCatLightEffectsV2Resp proto.InternalMessageInfo

// 编辑中奖光效,根据光效id覆盖更新
type UpdateCatLightEffectV2Req struct {
	Conf                 *CatLightEffect `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateCatLightEffectV2Req) Reset()         { *m = UpdateCatLightEffectV2Req{} }
func (m *UpdateCatLightEffectV2Req) String() string { return proto.CompactTextString(m) }
func (*UpdateCatLightEffectV2Req) ProtoMessage()    {}
func (*UpdateCatLightEffectV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{5}
}
func (m *UpdateCatLightEffectV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCatLightEffectV2Req.Unmarshal(m, b)
}
func (m *UpdateCatLightEffectV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCatLightEffectV2Req.Marshal(b, m, deterministic)
}
func (dst *UpdateCatLightEffectV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCatLightEffectV2Req.Merge(dst, src)
}
func (m *UpdateCatLightEffectV2Req) XXX_Size() int {
	return xxx_messageInfo_UpdateCatLightEffectV2Req.Size(m)
}
func (m *UpdateCatLightEffectV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCatLightEffectV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCatLightEffectV2Req proto.InternalMessageInfo

func (m *UpdateCatLightEffectV2Req) GetConf() *CatLightEffect {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateCatLightEffectV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCatLightEffectV2Resp) Reset()         { *m = UpdateCatLightEffectV2Resp{} }
func (m *UpdateCatLightEffectV2Resp) String() string { return proto.CompactTextString(m) }
func (*UpdateCatLightEffectV2Resp) ProtoMessage()    {}
func (*UpdateCatLightEffectV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{6}
}
func (m *UpdateCatLightEffectV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCatLightEffectV2Resp.Unmarshal(m, b)
}
func (m *UpdateCatLightEffectV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCatLightEffectV2Resp.Marshal(b, m, deterministic)
}
func (dst *UpdateCatLightEffectV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCatLightEffectV2Resp.Merge(dst, src)
}
func (m *UpdateCatLightEffectV2Resp) XXX_Size() int {
	return xxx_messageInfo_UpdateCatLightEffectV2Resp.Size(m)
}
func (m *UpdateCatLightEffectV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCatLightEffectV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCatLightEffectV2Resp proto.InternalMessageInfo

// 获取对应玩法的所有光效配置
type GetAllCatLightEffectsV2Req struct {
	SearchVal            uint32   `protobuf:"varint,1,opt,name=search_val,json=searchVal,proto3" json:"search_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllCatLightEffectsV2Req) Reset()         { *m = GetAllCatLightEffectsV2Req{} }
func (m *GetAllCatLightEffectsV2Req) String() string { return proto.CompactTextString(m) }
func (*GetAllCatLightEffectsV2Req) ProtoMessage()    {}
func (*GetAllCatLightEffectsV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{7}
}
func (m *GetAllCatLightEffectsV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCatLightEffectsV2Req.Unmarshal(m, b)
}
func (m *GetAllCatLightEffectsV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCatLightEffectsV2Req.Marshal(b, m, deterministic)
}
func (dst *GetAllCatLightEffectsV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCatLightEffectsV2Req.Merge(dst, src)
}
func (m *GetAllCatLightEffectsV2Req) XXX_Size() int {
	return xxx_messageInfo_GetAllCatLightEffectsV2Req.Size(m)
}
func (m *GetAllCatLightEffectsV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCatLightEffectsV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCatLightEffectsV2Req proto.InternalMessageInfo

func (m *GetAllCatLightEffectsV2Req) GetSearchVal() uint32 {
	if m != nil {
		return m.SearchVal
	}
	return 0
}

type GetAllCatLightEffectsV2Resp struct {
	ConfList             []*CatLightEffect `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAllCatLightEffectsV2Resp) Reset()         { *m = GetAllCatLightEffectsV2Resp{} }
func (m *GetAllCatLightEffectsV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetAllCatLightEffectsV2Resp) ProtoMessage()    {}
func (*GetAllCatLightEffectsV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{8}
}
func (m *GetAllCatLightEffectsV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCatLightEffectsV2Resp.Unmarshal(m, b)
}
func (m *GetAllCatLightEffectsV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCatLightEffectsV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetAllCatLightEffectsV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCatLightEffectsV2Resp.Merge(dst, src)
}
func (m *GetAllCatLightEffectsV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetAllCatLightEffectsV2Resp.Size(m)
}
func (m *GetAllCatLightEffectsV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCatLightEffectsV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCatLightEffectsV2Resp proto.InternalMessageInfo

func (m *GetAllCatLightEffectsV2Resp) GetConfList() []*CatLightEffect {
	if m != nil {
		return m.ConfList
	}
	return nil
}

// 删除光效
type DelLightEffectByIdReq struct {
	ConfId               uint32   `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	OpUser               string   `protobuf:"bytes,3,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelLightEffectByIdReq) Reset()         { *m = DelLightEffectByIdReq{} }
func (m *DelLightEffectByIdReq) String() string { return proto.CompactTextString(m) }
func (*DelLightEffectByIdReq) ProtoMessage()    {}
func (*DelLightEffectByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{9}
}
func (m *DelLightEffectByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelLightEffectByIdReq.Unmarshal(m, b)
}
func (m *DelLightEffectByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelLightEffectByIdReq.Marshal(b, m, deterministic)
}
func (dst *DelLightEffectByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelLightEffectByIdReq.Merge(dst, src)
}
func (m *DelLightEffectByIdReq) XXX_Size() int {
	return xxx_messageInfo_DelLightEffectByIdReq.Size(m)
}
func (m *DelLightEffectByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelLightEffectByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelLightEffectByIdReq proto.InternalMessageInfo

func (m *DelLightEffectByIdReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *DelLightEffectByIdReq) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

type DelLightEffectByIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelLightEffectByIdResp) Reset()         { *m = DelLightEffectByIdResp{} }
func (m *DelLightEffectByIdResp) String() string { return proto.CompactTextString(m) }
func (*DelLightEffectByIdResp) ProtoMessage()    {}
func (*DelLightEffectByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{10}
}
func (m *DelLightEffectByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelLightEffectByIdResp.Unmarshal(m, b)
}
func (m *DelLightEffectByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelLightEffectByIdResp.Marshal(b, m, deterministic)
}
func (dst *DelLightEffectByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelLightEffectByIdResp.Merge(dst, src)
}
func (m *DelLightEffectByIdResp) XXX_Size() int {
	return xxx_messageInfo_DelLightEffectByIdResp.Size(m)
}
func (m *DelLightEffectByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelLightEffectByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelLightEffectByIdResp proto.InternalMessageInfo

// 用户道具
type UserProp struct {
	PropInfo             *PropInfo `protobuf:"bytes,1,opt,name=prop_info,json=propInfo,proto3" json:"prop_info,omitempty"`
	ExpireTime           int64     `protobuf:"varint,2,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *UserProp) Reset()         { *m = UserProp{} }
func (m *UserProp) String() string { return proto.CompactTextString(m) }
func (*UserProp) ProtoMessage()    {}
func (*UserProp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{11}
}
func (m *UserProp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProp.Unmarshal(m, b)
}
func (m *UserProp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProp.Marshal(b, m, deterministic)
}
func (dst *UserProp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProp.Merge(dst, src)
}
func (m *UserProp) XXX_Size() int {
	return xxx_messageInfo_UserProp.Size(m)
}
func (m *UserProp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProp.DiscardUnknown(m)
}

var xxx_messageInfo_UserProp proto.InternalMessageInfo

func (m *UserProp) GetPropInfo() *PropInfo {
	if m != nil {
		return m.PropInfo
	}
	return nil
}

func (m *UserProp) GetExpireTime() int64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// 获取用户未过期的道具
type GetUserPropReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPropReq) Reset()         { *m = GetUserPropReq{} }
func (m *GetUserPropReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPropReq) ProtoMessage()    {}
func (*GetUserPropReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{12}
}
func (m *GetUserPropReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPropReq.Unmarshal(m, b)
}
func (m *GetUserPropReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPropReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPropReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPropReq.Merge(dst, src)
}
func (m *GetUserPropReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPropReq.Size(m)
}
func (m *GetUserPropReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPropReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPropReq proto.InternalMessageInfo

func (m *GetUserPropReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserPropResp struct {
	UserPropList         []*UserProp `protobuf:"bytes,1,rep,name=user_prop_list,json=userPropList,proto3" json:"user_prop_list,omitempty"`
	PropDuration         uint32      `protobuf:"varint,2,opt,name=prop_duration,json=propDuration,proto3" json:"prop_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUserPropResp) Reset()         { *m = GetUserPropResp{} }
func (m *GetUserPropResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPropResp) ProtoMessage()    {}
func (*GetUserPropResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{13}
}
func (m *GetUserPropResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPropResp.Unmarshal(m, b)
}
func (m *GetUserPropResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPropResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPropResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPropResp.Merge(dst, src)
}
func (m *GetUserPropResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPropResp.Size(m)
}
func (m *GetUserPropResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPropResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPropResp proto.InternalMessageInfo

func (m *GetUserPropResp) GetUserPropList() []*UserProp {
	if m != nil {
		return m.UserPropList
	}
	return nil
}

func (m *GetUserPropResp) GetPropDuration() uint32 {
	if m != nil {
		return m.PropDuration
	}
	return 0
}

// 解除熔断
type ReleaseFusingReq struct {
	InjectRmb            uint32   `protobuf:"varint,1,opt,name=inject_rmb,json=injectRmb,proto3" json:"inject_rmb,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReleaseFusingReq) Reset()         { *m = ReleaseFusingReq{} }
func (m *ReleaseFusingReq) String() string { return proto.CompactTextString(m) }
func (*ReleaseFusingReq) ProtoMessage()    {}
func (*ReleaseFusingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{14}
}
func (m *ReleaseFusingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReleaseFusingReq.Unmarshal(m, b)
}
func (m *ReleaseFusingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReleaseFusingReq.Marshal(b, m, deterministic)
}
func (dst *ReleaseFusingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReleaseFusingReq.Merge(dst, src)
}
func (m *ReleaseFusingReq) XXX_Size() int {
	return xxx_messageInfo_ReleaseFusingReq.Size(m)
}
func (m *ReleaseFusingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReleaseFusingReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReleaseFusingReq proto.InternalMessageInfo

func (m *ReleaseFusingReq) GetInjectRmb() uint32 {
	if m != nil {
		return m.InjectRmb
	}
	return 0
}

type ReleaseFusingResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReleaseFusingResp) Reset()         { *m = ReleaseFusingResp{} }
func (m *ReleaseFusingResp) String() string { return proto.CompactTextString(m) }
func (*ReleaseFusingResp) ProtoMessage()    {}
func (*ReleaseFusingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{15}
}
func (m *ReleaseFusingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReleaseFusingResp.Unmarshal(m, b)
}
func (m *ReleaseFusingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReleaseFusingResp.Marshal(b, m, deterministic)
}
func (dst *ReleaseFusingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReleaseFusingResp.Merge(dst, src)
}
func (m *ReleaseFusingResp) XXX_Size() int {
	return xxx_messageInfo_ReleaseFusingResp.Size(m)
}
func (m *ReleaseFusingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReleaseFusingResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReleaseFusingResp proto.InternalMessageInfo

type ReportStatsReq struct {
	ReportTs             int64    `protobuf:"varint,1,opt,name=report_ts,json=reportTs,proto3" json:"report_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportStatsReq) Reset()         { *m = ReportStatsReq{} }
func (m *ReportStatsReq) String() string { return proto.CompactTextString(m) }
func (*ReportStatsReq) ProtoMessage()    {}
func (*ReportStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{16}
}
func (m *ReportStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportStatsReq.Unmarshal(m, b)
}
func (m *ReportStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportStatsReq.Marshal(b, m, deterministic)
}
func (dst *ReportStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportStatsReq.Merge(dst, src)
}
func (m *ReportStatsReq) XXX_Size() int {
	return xxx_messageInfo_ReportStatsReq.Size(m)
}
func (m *ReportStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportStatsReq proto.InternalMessageInfo

func (m *ReportStatsReq) GetReportTs() int64 {
	if m != nil {
		return m.ReportTs
	}
	return 0
}

type ReportStatsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportStatsResp) Reset()         { *m = ReportStatsResp{} }
func (m *ReportStatsResp) String() string { return proto.CompactTextString(m) }
func (*ReportStatsResp) ProtoMessage()    {}
func (*ReportStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{17}
}
func (m *ReportStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportStatsResp.Unmarshal(m, b)
}
func (m *ReportStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportStatsResp.Marshal(b, m, deterministic)
}
func (dst *ReportStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportStatsResp.Merge(dst, src)
}
func (m *ReportStatsResp) XXX_Size() int {
	return xxx_messageInfo_ReportStatsResp.Size(m)
}
func (m *ReportStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportStatsResp proto.InternalMessageInfo

type LevelGift struct {
	PackId               uint32   `protobuf:"varint,1,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackName             string   `protobuf:"bytes,2,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string   `protobuf:"bytes,3,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackPrice            uint32   `protobuf:"varint,4,opt,name=pack_price,json=packPrice,proto3" json:"pack_price,omitempty"`
	Amount               uint32   `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelGift) Reset()         { *m = LevelGift{} }
func (m *LevelGift) String() string { return proto.CompactTextString(m) }
func (*LevelGift) ProtoMessage()    {}
func (*LevelGift) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{18}
}
func (m *LevelGift) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelGift.Unmarshal(m, b)
}
func (m *LevelGift) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelGift.Marshal(b, m, deterministic)
}
func (dst *LevelGift) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelGift.Merge(dst, src)
}
func (m *LevelGift) XXX_Size() int {
	return xxx_messageInfo_LevelGift.Size(m)
}
func (m *LevelGift) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelGift.DiscardUnknown(m)
}

var xxx_messageInfo_LevelGift proto.InternalMessageInfo

func (m *LevelGift) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *LevelGift) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *LevelGift) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *LevelGift) GetPackPrice() uint32 {
	if m != nil {
		return m.PackPrice
	}
	return 0
}

func (m *LevelGift) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type LevelMount struct {
	MountId              string   `protobuf:"bytes,1,opt,name=mount_id,json=mountId,proto3" json:"mount_id,omitempty"`
	MountName            string   `protobuf:"bytes,2,opt,name=mount_name,json=mountName,proto3" json:"mount_name,omitempty"`
	MountPic             string   `protobuf:"bytes,3,opt,name=mount_pic,json=mountPic,proto3" json:"mount_pic,omitempty"`
	AwardDays            uint32   `protobuf:"varint,4,opt,name=award_days,json=awardDays,proto3" json:"award_days,omitempty"`
	Amount               uint32   `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelMount) Reset()         { *m = LevelMount{} }
func (m *LevelMount) String() string { return proto.CompactTextString(m) }
func (*LevelMount) ProtoMessage()    {}
func (*LevelMount) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{19}
}
func (m *LevelMount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelMount.Unmarshal(m, b)
}
func (m *LevelMount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelMount.Marshal(b, m, deterministic)
}
func (dst *LevelMount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelMount.Merge(dst, src)
}
func (m *LevelMount) XXX_Size() int {
	return xxx_messageInfo_LevelMount.Size(m)
}
func (m *LevelMount) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelMount.DiscardUnknown(m)
}

var xxx_messageInfo_LevelMount proto.InternalMessageInfo

func (m *LevelMount) GetMountId() string {
	if m != nil {
		return m.MountId
	}
	return ""
}

func (m *LevelMount) GetMountName() string {
	if m != nil {
		return m.MountName
	}
	return ""
}

func (m *LevelMount) GetMountPic() string {
	if m != nil {
		return m.MountPic
	}
	return ""
}

func (m *LevelMount) GetAwardDays() uint32 {
	if m != nil {
		return m.AwardDays
	}
	return 0
}

func (m *LevelMount) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type PropInfo struct {
	PropId               uint32   `protobuf:"varint,1,opt,name=prop_id,json=propId,proto3" json:"prop_id,omitempty"`
	PropIcon             string   `protobuf:"bytes,2,opt,name=prop_icon,json=propIcon,proto3" json:"prop_icon,omitempty"`
	PropName             string   `protobuf:"bytes,3,opt,name=prop_name,json=propName,proto3" json:"prop_name,omitempty"`
	Amount               uint32   `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	DurationDay          uint32   `protobuf:"varint,5,opt,name=duration_day,json=durationDay,proto3" json:"duration_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PropInfo) Reset()         { *m = PropInfo{} }
func (m *PropInfo) String() string { return proto.CompactTextString(m) }
func (*PropInfo) ProtoMessage()    {}
func (*PropInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{20}
}
func (m *PropInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PropInfo.Unmarshal(m, b)
}
func (m *PropInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PropInfo.Marshal(b, m, deterministic)
}
func (dst *PropInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PropInfo.Merge(dst, src)
}
func (m *PropInfo) XXX_Size() int {
	return xxx_messageInfo_PropInfo.Size(m)
}
func (m *PropInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PropInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PropInfo proto.InternalMessageInfo

func (m *PropInfo) GetPropId() uint32 {
	if m != nil {
		return m.PropId
	}
	return 0
}

func (m *PropInfo) GetPropIcon() string {
	if m != nil {
		return m.PropIcon
	}
	return ""
}

func (m *PropInfo) GetPropName() string {
	if m != nil {
		return m.PropName
	}
	return ""
}

func (m *PropInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *PropInfo) GetDurationDay() uint32 {
	if m != nil {
		return m.DurationDay
	}
	return 0
}

// 关卡升级的奖励
type UpgradeAward struct {
	Present              *LevelGift  `protobuf:"bytes,1,opt,name=present,proto3" json:"present,omitempty"`
	Mount                *LevelMount `protobuf:"bytes,2,opt,name=mount,proto3" json:"mount,omitempty"`
	PropInfo             *PropInfo   `protobuf:"bytes,3,opt,name=prop_info,json=propInfo,proto3" json:"prop_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpgradeAward) Reset()         { *m = UpgradeAward{} }
func (m *UpgradeAward) String() string { return proto.CompactTextString(m) }
func (*UpgradeAward) ProtoMessage()    {}
func (*UpgradeAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{21}
}
func (m *UpgradeAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpgradeAward.Unmarshal(m, b)
}
func (m *UpgradeAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpgradeAward.Marshal(b, m, deterministic)
}
func (dst *UpgradeAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpgradeAward.Merge(dst, src)
}
func (m *UpgradeAward) XXX_Size() int {
	return xxx_messageInfo_UpgradeAward.Size(m)
}
func (m *UpgradeAward) XXX_DiscardUnknown() {
	xxx_messageInfo_UpgradeAward.DiscardUnknown(m)
}

var xxx_messageInfo_UpgradeAward proto.InternalMessageInfo

func (m *UpgradeAward) GetPresent() *LevelGift {
	if m != nil {
		return m.Present
	}
	return nil
}

func (m *UpgradeAward) GetMount() *LevelMount {
	if m != nil {
		return m.Mount
	}
	return nil
}

func (m *UpgradeAward) GetPropInfo() *PropInfo {
	if m != nil {
		return m.PropInfo
	}
	return nil
}

type ResourcesCfg struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResourcesCfg) Reset()         { *m = ResourcesCfg{} }
func (m *ResourcesCfg) String() string { return proto.CompactTextString(m) }
func (*ResourcesCfg) ProtoMessage()    {}
func (*ResourcesCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{22}
}
func (m *ResourcesCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResourcesCfg.Unmarshal(m, b)
}
func (m *ResourcesCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResourcesCfg.Marshal(b, m, deterministic)
}
func (dst *ResourcesCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResourcesCfg.Merge(dst, src)
}
func (m *ResourcesCfg) XXX_Size() int {
	return xxx_messageInfo_ResourcesCfg.Size(m)
}
func (m *ResourcesCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ResourcesCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ResourcesCfg proto.InternalMessageInfo

func (m *ResourcesCfg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ResourcesCfg) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

// 关卡配置
type GameLevelCfg struct {
	LevelId                uint32        `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	LevelName              string        `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	ResourcesCfg           *ResourcesCfg `protobuf:"bytes,3,opt,name=resources_cfg,json=resourcesCfg,proto3" json:"resources_cfg,omitempty"`
	EntryGift              *LevelGift    `protobuf:"bytes,4,opt,name=entry_gift,json=entryGift,proto3" json:"entry_gift,omitempty"`
	UpgradeAward           *UpgradeAward `protobuf:"bytes,5,opt,name=upgrade_award,json=upgradeAward,proto3" json:"upgrade_award,omitempty"`
	CostChancePer          uint32        `protobuf:"varint,6,opt,name=cost_chance_per,json=costChancePer,proto3" json:"cost_chance_per,omitempty"`
	BuyChanceAmountOption  []uint32      `protobuf:"varint,7,rep,packed,name=buy_chance_amount_option,json=buyChanceAmountOption,proto3" json:"buy_chance_amount_option,omitempty"`
	CostChanceAmountOption []uint32      `protobuf:"varint,8,rep,packed,name=cost_chance_amount_option,json=costChanceAmountOption,proto3" json:"cost_chance_amount_option,omitempty"`
	NotifyContinueFailCnt  uint32        `protobuf:"varint,9,opt,name=notify_continue_fail_cnt,json=notifyContinueFailCnt,proto3" json:"notify_continue_fail_cnt,omitempty"`
	UnlockNotify           string        `protobuf:"bytes,10,opt,name=unlock_notify,json=unlockNotify,proto3" json:"unlock_notify,omitempty"`
	MaxN                   uint32        `protobuf:"varint,11,opt,name=max_n,json=maxN,proto3" json:"max_n,omitempty"`
	EntryPropInfo          *PropInfo     `protobuf:"bytes,12,opt,name=entry_prop_info,json=entryPropInfo,proto3" json:"entry_prop_info,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}      `json:"-"`
	XXX_unrecognized       []byte        `json:"-"`
	XXX_sizecache          int32         `json:"-"`
}

func (m *GameLevelCfg) Reset()         { *m = GameLevelCfg{} }
func (m *GameLevelCfg) String() string { return proto.CompactTextString(m) }
func (*GameLevelCfg) ProtoMessage()    {}
func (*GameLevelCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{23}
}
func (m *GameLevelCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameLevelCfg.Unmarshal(m, b)
}
func (m *GameLevelCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameLevelCfg.Marshal(b, m, deterministic)
}
func (dst *GameLevelCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameLevelCfg.Merge(dst, src)
}
func (m *GameLevelCfg) XXX_Size() int {
	return xxx_messageInfo_GameLevelCfg.Size(m)
}
func (m *GameLevelCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_GameLevelCfg.DiscardUnknown(m)
}

var xxx_messageInfo_GameLevelCfg proto.InternalMessageInfo

func (m *GameLevelCfg) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *GameLevelCfg) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *GameLevelCfg) GetResourcesCfg() *ResourcesCfg {
	if m != nil {
		return m.ResourcesCfg
	}
	return nil
}

func (m *GameLevelCfg) GetEntryGift() *LevelGift {
	if m != nil {
		return m.EntryGift
	}
	return nil
}

func (m *GameLevelCfg) GetUpgradeAward() *UpgradeAward {
	if m != nil {
		return m.UpgradeAward
	}
	return nil
}

func (m *GameLevelCfg) GetCostChancePer() uint32 {
	if m != nil {
		return m.CostChancePer
	}
	return 0
}

func (m *GameLevelCfg) GetBuyChanceAmountOption() []uint32 {
	if m != nil {
		return m.BuyChanceAmountOption
	}
	return nil
}

func (m *GameLevelCfg) GetCostChanceAmountOption() []uint32 {
	if m != nil {
		return m.CostChanceAmountOption
	}
	return nil
}

func (m *GameLevelCfg) GetNotifyContinueFailCnt() uint32 {
	if m != nil {
		return m.NotifyContinueFailCnt
	}
	return 0
}

func (m *GameLevelCfg) GetUnlockNotify() string {
	if m != nil {
		return m.UnlockNotify
	}
	return ""
}

func (m *GameLevelCfg) GetMaxN() uint32 {
	if m != nil {
		return m.MaxN
	}
	return 0
}

func (m *GameLevelCfg) GetEntryPropInfo() *PropInfo {
	if m != nil {
		return m.EntryPropInfo
	}
	return nil
}

type UserLevelStatus struct {
	Status               UserLevelStatus_Status `protobuf:"varint,1,opt,name=status,proto3,enum=cat_canteen.UserLevelStatus_Status" json:"status,omitempty"`
	Days                 uint32                 `protobuf:"varint,2,opt,name=days,proto3" json:"days,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *UserLevelStatus) Reset()         { *m = UserLevelStatus{} }
func (m *UserLevelStatus) String() string { return proto.CompactTextString(m) }
func (*UserLevelStatus) ProtoMessage()    {}
func (*UserLevelStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{24}
}
func (m *UserLevelStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelStatus.Unmarshal(m, b)
}
func (m *UserLevelStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelStatus.Marshal(b, m, deterministic)
}
func (dst *UserLevelStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelStatus.Merge(dst, src)
}
func (m *UserLevelStatus) XXX_Size() int {
	return xxx_messageInfo_UserLevelStatus.Size(m)
}
func (m *UserLevelStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelStatus.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelStatus proto.InternalMessageInfo

func (m *UserLevelStatus) GetStatus() UserLevelStatus_Status {
	if m != nil {
		return m.Status
	}
	return UserLevelStatus_STATUS_UNSPECIFIED
}

func (m *UserLevelStatus) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

// 用户的关卡存档
type UserPlayFile struct {
	LevelId              uint32           `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	EntryGiftCnt         uint32           `protobuf:"varint,2,opt,name=entry_gift_cnt,json=entryGiftCnt,proto3" json:"entry_gift_cnt,omitempty"`
	RateOfProgress       float32          `protobuf:"fixed32,3,opt,name=rate_of_progress,json=rateOfProgress,proto3" json:"rate_of_progress,omitempty"`
	CanLv                uint32           `protobuf:"varint,4,opt,name=can_lv,json=canLv,proto3" json:"can_lv,omitempty"`
	UserLevelStatus      *UserLevelStatus `protobuf:"bytes,5,opt,name=user_level_status,json=userLevelStatus,proto3" json:"user_level_status,omitempty"`
	PropNum              uint32           `protobuf:"varint,6,opt,name=prop_num,json=propNum,proto3" json:"prop_num,omitempty"`
	UserN                uint32           `protobuf:"varint,7,opt,name=user_n,json=userN,proto3" json:"user_n,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserPlayFile) Reset()         { *m = UserPlayFile{} }
func (m *UserPlayFile) String() string { return proto.CompactTextString(m) }
func (*UserPlayFile) ProtoMessage()    {}
func (*UserPlayFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{25}
}
func (m *UserPlayFile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPlayFile.Unmarshal(m, b)
}
func (m *UserPlayFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPlayFile.Marshal(b, m, deterministic)
}
func (dst *UserPlayFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPlayFile.Merge(dst, src)
}
func (m *UserPlayFile) XXX_Size() int {
	return xxx_messageInfo_UserPlayFile.Size(m)
}
func (m *UserPlayFile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPlayFile.DiscardUnknown(m)
}

var xxx_messageInfo_UserPlayFile proto.InternalMessageInfo

func (m *UserPlayFile) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *UserPlayFile) GetEntryGiftCnt() uint32 {
	if m != nil {
		return m.EntryGiftCnt
	}
	return 0
}

func (m *UserPlayFile) GetRateOfProgress() float32 {
	if m != nil {
		return m.RateOfProgress
	}
	return 0
}

func (m *UserPlayFile) GetCanLv() uint32 {
	if m != nil {
		return m.CanLv
	}
	return 0
}

func (m *UserPlayFile) GetUserLevelStatus() *UserLevelStatus {
	if m != nil {
		return m.UserLevelStatus
	}
	return nil
}

func (m *UserPlayFile) GetPropNum() uint32 {
	if m != nil {
		return m.PropNum
	}
	return 0
}

func (m *UserPlayFile) GetUserN() uint32 {
	if m != nil {
		return m.UserN
	}
	return 0
}

type GetUserPlayFileReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPlayFileReq) Reset()         { *m = GetUserPlayFileReq{} }
func (m *GetUserPlayFileReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPlayFileReq) ProtoMessage()    {}
func (*GetUserPlayFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{26}
}
func (m *GetUserPlayFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPlayFileReq.Unmarshal(m, b)
}
func (m *GetUserPlayFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPlayFileReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPlayFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPlayFileReq.Merge(dst, src)
}
func (m *GetUserPlayFileReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPlayFileReq.Size(m)
}
func (m *GetUserPlayFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPlayFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPlayFileReq proto.InternalMessageInfo

func (m *GetUserPlayFileReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPlayFileReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUserPlayFileResp struct {
	PlayFileList         []*UserPlayFile `protobuf:"bytes,1,rep,name=play_file_list,json=playFileList,proto3" json:"play_file_list,omitempty"`
	UserChanceCnt        uint32          `protobuf:"varint,2,opt,name=user_chance_cnt,json=userChanceCnt,proto3" json:"user_chance_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserPlayFileResp) Reset()         { *m = GetUserPlayFileResp{} }
func (m *GetUserPlayFileResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPlayFileResp) ProtoMessage()    {}
func (*GetUserPlayFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{27}
}
func (m *GetUserPlayFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPlayFileResp.Unmarshal(m, b)
}
func (m *GetUserPlayFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPlayFileResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPlayFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPlayFileResp.Merge(dst, src)
}
func (m *GetUserPlayFileResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPlayFileResp.Size(m)
}
func (m *GetUserPlayFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPlayFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPlayFileResp proto.InternalMessageInfo

func (m *GetUserPlayFileResp) GetPlayFileList() []*UserPlayFile {
	if m != nil {
		return m.PlayFileList
	}
	return nil
}

func (m *GetUserPlayFileResp) GetUserChanceCnt() uint32 {
	if m != nil {
		return m.UserChanceCnt
	}
	return 0
}

type GameLevelInfo struct {
	LevelCfg             *GameLevelCfg `protobuf:"bytes,1,opt,name=level_cfg,json=levelCfg,proto3" json:"level_cfg,omitempty"`
	UserPlayInfo         *UserPlayFile `protobuf:"bytes,2,opt,name=user_play_info,json=userPlayInfo,proto3" json:"user_play_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GameLevelInfo) Reset()         { *m = GameLevelInfo{} }
func (m *GameLevelInfo) String() string { return proto.CompactTextString(m) }
func (*GameLevelInfo) ProtoMessage()    {}
func (*GameLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{28}
}
func (m *GameLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameLevelInfo.Unmarshal(m, b)
}
func (m *GameLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameLevelInfo.Marshal(b, m, deterministic)
}
func (dst *GameLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameLevelInfo.Merge(dst, src)
}
func (m *GameLevelInfo) XXX_Size() int {
	return xxx_messageInfo_GameLevelInfo.Size(m)
}
func (m *GameLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameLevelInfo proto.InternalMessageInfo

func (m *GameLevelInfo) GetLevelCfg() *GameLevelCfg {
	if m != nil {
		return m.LevelCfg
	}
	return nil
}

func (m *GameLevelInfo) GetUserPlayInfo() *UserPlayFile {
	if m != nil {
		return m.UserPlayInfo
	}
	return nil
}

// 获取资源配置
type GetResourcesCfgReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetResourcesCfgReq) Reset()         { *m = GetResourcesCfgReq{} }
func (m *GetResourcesCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetResourcesCfgReq) ProtoMessage()    {}
func (*GetResourcesCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{29}
}
func (m *GetResourcesCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResourcesCfgReq.Unmarshal(m, b)
}
func (m *GetResourcesCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResourcesCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetResourcesCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourcesCfgReq.Merge(dst, src)
}
func (m *GetResourcesCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetResourcesCfgReq.Size(m)
}
func (m *GetResourcesCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourcesCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourcesCfgReq proto.InternalMessageInfo

func (m *GetResourcesCfgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetResourcesCfgResp struct {
	LevelCfgList         []*GameLevelCfg `protobuf:"bytes,1,rep,name=level_cfg_list,json=levelCfgList,proto3" json:"level_cfg_list,omitempty"`
	CanResourcesCfg      *ResourcesCfg   `protobuf:"bytes,2,opt,name=can_resources_cfg,json=canResourcesCfg,proto3" json:"can_resources_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetResourcesCfgResp) Reset()         { *m = GetResourcesCfgResp{} }
func (m *GetResourcesCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetResourcesCfgResp) ProtoMessage()    {}
func (*GetResourcesCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{30}
}
func (m *GetResourcesCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResourcesCfgResp.Unmarshal(m, b)
}
func (m *GetResourcesCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResourcesCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetResourcesCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourcesCfgResp.Merge(dst, src)
}
func (m *GetResourcesCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetResourcesCfgResp.Size(m)
}
func (m *GetResourcesCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourcesCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourcesCfgResp proto.InternalMessageInfo

func (m *GetResourcesCfgResp) GetLevelCfgList() []*GameLevelCfg {
	if m != nil {
		return m.LevelCfgList
	}
	return nil
}

func (m *GetResourcesCfgResp) GetCanResourcesCfg() *ResourcesCfg {
	if m != nil {
		return m.CanResourcesCfg
	}
	return nil
}

// 获取游戏信息
type GetGameInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameInfoReq) Reset()         { *m = GetGameInfoReq{} }
func (m *GetGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoReq) ProtoMessage()    {}
func (*GetGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{31}
}
func (m *GetGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoReq.Unmarshal(m, b)
}
func (m *GetGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoReq.Merge(dst, src)
}
func (m *GetGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoReq.Size(m)
}
func (m *GetGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoReq proto.InternalMessageInfo

func (m *GetGameInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGameInfoResp struct {
	UserChanceCnt        uint32           `protobuf:"varint,1,opt,name=user_chance_cnt,json=userChanceCnt,proto3" json:"user_chance_cnt,omitempty"`
	LevelInfoList        []*GameLevelInfo `protobuf:"bytes,2,rep,name=level_info_list,json=levelInfoList,proto3" json:"level_info_list,omitempty"`
	BuyChanceAward       *LevelGift       `protobuf:"bytes,3,opt,name=buy_chance_award,json=buyChanceAward,proto3" json:"buy_chance_award,omitempty"`
	CanResourcesCfg      *ResourcesCfg    `protobuf:"bytes,4,opt,name=can_resources_cfg,json=canResourcesCfg,proto3" json:"can_resources_cfg,omitempty"`
	BuyChanceExpireDesc  string           `protobuf:"bytes,5,opt,name=buy_chance_expire_desc,json=buyChanceExpireDesc,proto3" json:"buy_chance_expire_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGameInfoResp) Reset()         { *m = GetGameInfoResp{} }
func (m *GetGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoResp) ProtoMessage()    {}
func (*GetGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{32}
}
func (m *GetGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoResp.Unmarshal(m, b)
}
func (m *GetGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoResp.Merge(dst, src)
}
func (m *GetGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoResp.Size(m)
}
func (m *GetGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoResp proto.InternalMessageInfo

func (m *GetGameInfoResp) GetUserChanceCnt() uint32 {
	if m != nil {
		return m.UserChanceCnt
	}
	return 0
}

func (m *GetGameInfoResp) GetLevelInfoList() []*GameLevelInfo {
	if m != nil {
		return m.LevelInfoList
	}
	return nil
}

func (m *GetGameInfoResp) GetBuyChanceAward() *LevelGift {
	if m != nil {
		return m.BuyChanceAward
	}
	return nil
}

func (m *GetGameInfoResp) GetCanResourcesCfg() *ResourcesCfg {
	if m != nil {
		return m.CanResourcesCfg
	}
	return nil
}

func (m *GetGameInfoResp) GetBuyChanceExpireDesc() string {
	if m != nil {
		return m.BuyChanceExpireDesc
	}
	return ""
}

// 购买抽奖机会
type BuyChanceReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChanceAmount         uint32   `protobuf:"varint,2,opt,name=chance_amount,json=chanceAmount,proto3" json:"chance_amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyChanceReq) Reset()         { *m = BuyChanceReq{} }
func (m *BuyChanceReq) String() string { return proto.CompactTextString(m) }
func (*BuyChanceReq) ProtoMessage()    {}
func (*BuyChanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{33}
}
func (m *BuyChanceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyChanceReq.Unmarshal(m, b)
}
func (m *BuyChanceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyChanceReq.Marshal(b, m, deterministic)
}
func (dst *BuyChanceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyChanceReq.Merge(dst, src)
}
func (m *BuyChanceReq) XXX_Size() int {
	return xxx_messageInfo_BuyChanceReq.Size(m)
}
func (m *BuyChanceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyChanceReq.DiscardUnknown(m)
}

var xxx_messageInfo_BuyChanceReq proto.InternalMessageInfo

func (m *BuyChanceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BuyChanceReq) GetChanceAmount() uint32 {
	if m != nil {
		return m.ChanceAmount
	}
	return 0
}

type BuyChanceResp struct {
	FinalChanceAmount    uint32   `protobuf:"varint,1,opt,name=final_chance_amount,json=finalChanceAmount,proto3" json:"final_chance_amount,omitempty"`
	Balance              uint64   `protobuf:"varint,2,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyChanceResp) Reset()         { *m = BuyChanceResp{} }
func (m *BuyChanceResp) String() string { return proto.CompactTextString(m) }
func (*BuyChanceResp) ProtoMessage()    {}
func (*BuyChanceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{34}
}
func (m *BuyChanceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyChanceResp.Unmarshal(m, b)
}
func (m *BuyChanceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyChanceResp.Marshal(b, m, deterministic)
}
func (dst *BuyChanceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyChanceResp.Merge(dst, src)
}
func (m *BuyChanceResp) XXX_Size() int {
	return xxx_messageInfo_BuyChanceResp.Size(m)
}
func (m *BuyChanceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyChanceResp.DiscardUnknown(m)
}

var xxx_messageInfo_BuyChanceResp proto.InternalMessageInfo

func (m *BuyChanceResp) GetFinalChanceAmount() uint32 {
	if m != nil {
		return m.FinalChanceAmount
	}
	return 0
}

func (m *BuyChanceResp) GetBalance() uint64 {
	if m != nil {
		return m.Balance
	}
	return 0
}

// 奖励
type LotteryDrawAward struct {
	ResultType           LotteryResultType `protobuf:"varint,1,opt,name=result_type,json=resultType,proto3,enum=cat_canteen.LotteryResultType" json:"result_type,omitempty"`
	Present              *LevelGift        `protobuf:"bytes,2,opt,name=present,proto3" json:"present,omitempty"`
	Mount                *LevelMount       `protobuf:"bytes,3,opt,name=mount,proto3" json:"mount,omitempty"`
	PropInfo             *PropInfo         `protobuf:"bytes,4,opt,name=prop_info,json=propInfo,proto3" json:"prop_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *LotteryDrawAward) Reset()         { *m = LotteryDrawAward{} }
func (m *LotteryDrawAward) String() string { return proto.CompactTextString(m) }
func (*LotteryDrawAward) ProtoMessage()    {}
func (*LotteryDrawAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{35}
}
func (m *LotteryDrawAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryDrawAward.Unmarshal(m, b)
}
func (m *LotteryDrawAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryDrawAward.Marshal(b, m, deterministic)
}
func (dst *LotteryDrawAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryDrawAward.Merge(dst, src)
}
func (m *LotteryDrawAward) XXX_Size() int {
	return xxx_messageInfo_LotteryDrawAward.Size(m)
}
func (m *LotteryDrawAward) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryDrawAward.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryDrawAward proto.InternalMessageInfo

func (m *LotteryDrawAward) GetResultType() LotteryResultType {
	if m != nil {
		return m.ResultType
	}
	return LotteryResultType_LOTTERY_RESULT_UNSPECIFIED
}

func (m *LotteryDrawAward) GetPresent() *LevelGift {
	if m != nil {
		return m.Present
	}
	return nil
}

func (m *LotteryDrawAward) GetMount() *LevelMount {
	if m != nil {
		return m.Mount
	}
	return nil
}

func (m *LotteryDrawAward) GetPropInfo() *PropInfo {
	if m != nil {
		return m.PropInfo
	}
	return nil
}

type LotteryDrawResult struct {
	ResultType           LotteryResultType   `protobuf:"varint,1,opt,name=result_type,json=resultType,proto3,enum=cat_canteen.LotteryResultType" json:"result_type,omitempty"`
	AwardList            []*LotteryDrawAward `protobuf:"bytes,2,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	NextLevelId          uint32              `protobuf:"varint,3,opt,name=next_level_id,json=nextLevelId,proto3" json:"next_level_id,omitempty"`
	IncrProgressVal      uint32              `protobuf:"varint,4,opt,name=incr_progress_val,json=incrProgressVal,proto3" json:"incr_progress_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *LotteryDrawResult) Reset()         { *m = LotteryDrawResult{} }
func (m *LotteryDrawResult) String() string { return proto.CompactTextString(m) }
func (*LotteryDrawResult) ProtoMessage()    {}
func (*LotteryDrawResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{36}
}
func (m *LotteryDrawResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryDrawResult.Unmarshal(m, b)
}
func (m *LotteryDrawResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryDrawResult.Marshal(b, m, deterministic)
}
func (dst *LotteryDrawResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryDrawResult.Merge(dst, src)
}
func (m *LotteryDrawResult) XXX_Size() int {
	return xxx_messageInfo_LotteryDrawResult.Size(m)
}
func (m *LotteryDrawResult) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryDrawResult.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryDrawResult proto.InternalMessageInfo

func (m *LotteryDrawResult) GetResultType() LotteryResultType {
	if m != nil {
		return m.ResultType
	}
	return LotteryResultType_LOTTERY_RESULT_UNSPECIFIED
}

func (m *LotteryDrawResult) GetAwardList() []*LotteryDrawAward {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *LotteryDrawResult) GetNextLevelId() uint32 {
	if m != nil {
		return m.NextLevelId
	}
	return 0
}

func (m *LotteryDrawResult) GetIncrProgressVal() uint32 {
	if m != nil {
		return m.IncrProgressVal
	}
	return 0
}

// 抽奖（经营）
type LotteryDrawReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Amount               uint32   `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	LevelId              uint32   `protobuf:"varint,4,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LotteryDrawReq) Reset()         { *m = LotteryDrawReq{} }
func (m *LotteryDrawReq) String() string { return proto.CompactTextString(m) }
func (*LotteryDrawReq) ProtoMessage()    {}
func (*LotteryDrawReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{37}
}
func (m *LotteryDrawReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryDrawReq.Unmarshal(m, b)
}
func (m *LotteryDrawReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryDrawReq.Marshal(b, m, deterministic)
}
func (dst *LotteryDrawReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryDrawReq.Merge(dst, src)
}
func (m *LotteryDrawReq) XXX_Size() int {
	return xxx_messageInfo_LotteryDrawReq.Size(m)
}
func (m *LotteryDrawReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryDrawReq.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryDrawReq proto.InternalMessageInfo

func (m *LotteryDrawReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LotteryDrawReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LotteryDrawReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *LotteryDrawReq) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

type LotteryDrawResp struct {
	RemainChance         uint32             `protobuf:"varint,1,opt,name=remain_chance,json=remainChance,proto3" json:"remain_chance,omitempty"`
	PlayFileList         []*UserPlayFile    `protobuf:"bytes,2,rep,name=play_file_list,json=playFileList,proto3" json:"play_file_list,omitempty"`
	Result               *LotteryDrawResult `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *LotteryDrawResp) Reset()         { *m = LotteryDrawResp{} }
func (m *LotteryDrawResp) String() string { return proto.CompactTextString(m) }
func (*LotteryDrawResp) ProtoMessage()    {}
func (*LotteryDrawResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{38}
}
func (m *LotteryDrawResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryDrawResp.Unmarshal(m, b)
}
func (m *LotteryDrawResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryDrawResp.Marshal(b, m, deterministic)
}
func (dst *LotteryDrawResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryDrawResp.Merge(dst, src)
}
func (m *LotteryDrawResp) XXX_Size() int {
	return xxx_messageInfo_LotteryDrawResp.Size(m)
}
func (m *LotteryDrawResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryDrawResp.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryDrawResp proto.InternalMessageInfo

func (m *LotteryDrawResp) GetRemainChance() uint32 {
	if m != nil {
		return m.RemainChance
	}
	return 0
}

func (m *LotteryDrawResp) GetPlayFileList() []*UserPlayFile {
	if m != nil {
		return m.PlayFileList
	}
	return nil
}

func (m *LotteryDrawResp) GetResult() *LotteryDrawResult {
	if m != nil {
		return m.Result
	}
	return nil
}

// 中奖记录
type GameWinningRecord struct {
	Id                   string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	LotteryId            string            `protobuf:"bytes,2,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id,omitempty"`
	ResultType           LotteryResultType `protobuf:"varint,3,opt,name=result_type,json=resultType,proto3,enum=cat_canteen.LotteryResultType" json:"result_type,omitempty"`
	IncrProgressVal      uint32            `protobuf:"varint,4,opt,name=incr_progress_val,json=incrProgressVal,proto3" json:"incr_progress_val,omitempty"`
	AwardInfo            *LevelGift        `protobuf:"bytes,5,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	AwardMount           *LevelMount       `protobuf:"bytes,6,opt,name=award_mount,json=awardMount,proto3" json:"award_mount,omitempty"`
	EntryGift            *LevelGift        `protobuf:"bytes,7,opt,name=entry_gift,json=entryGift,proto3" json:"entry_gift,omitempty"`
	CreateTime           uint32            `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	AwardProp            *PropInfo         `protobuf:"bytes,9,opt,name=award_prop,json=awardProp,proto3" json:"award_prop,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GameWinningRecord) Reset()         { *m = GameWinningRecord{} }
func (m *GameWinningRecord) String() string { return proto.CompactTextString(m) }
func (*GameWinningRecord) ProtoMessage()    {}
func (*GameWinningRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{39}
}
func (m *GameWinningRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameWinningRecord.Unmarshal(m, b)
}
func (m *GameWinningRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameWinningRecord.Marshal(b, m, deterministic)
}
func (dst *GameWinningRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameWinningRecord.Merge(dst, src)
}
func (m *GameWinningRecord) XXX_Size() int {
	return xxx_messageInfo_GameWinningRecord.Size(m)
}
func (m *GameWinningRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_GameWinningRecord.DiscardUnknown(m)
}

var xxx_messageInfo_GameWinningRecord proto.InternalMessageInfo

func (m *GameWinningRecord) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameWinningRecord) GetLotteryId() string {
	if m != nil {
		return m.LotteryId
	}
	return ""
}

func (m *GameWinningRecord) GetResultType() LotteryResultType {
	if m != nil {
		return m.ResultType
	}
	return LotteryResultType_LOTTERY_RESULT_UNSPECIFIED
}

func (m *GameWinningRecord) GetIncrProgressVal() uint32 {
	if m != nil {
		return m.IncrProgressVal
	}
	return 0
}

func (m *GameWinningRecord) GetAwardInfo() *LevelGift {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *GameWinningRecord) GetAwardMount() *LevelMount {
	if m != nil {
		return m.AwardMount
	}
	return nil
}

func (m *GameWinningRecord) GetEntryGift() *LevelGift {
	if m != nil {
		return m.EntryGift
	}
	return nil
}

func (m *GameWinningRecord) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GameWinningRecord) GetAwardProp() *PropInfo {
	if m != nil {
		return m.AwardProp
	}
	return nil
}

// 获取用户中奖纪录
type GetUserWinningRecordsReq struct {
	Uid                  uint32                             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LevelId              uint32                             `protobuf:"varint,2,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	OffsetId             string                             `protobuf:"bytes,3,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Limit                uint32                             `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	QueryType            GetUserWinningRecordsReq_QueryType `protobuf:"varint,5,opt,name=query_type,json=queryType,proto3,enum=cat_canteen.GetUserWinningRecordsReq_QueryType" json:"query_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *GetUserWinningRecordsReq) Reset()         { *m = GetUserWinningRecordsReq{} }
func (m *GetUserWinningRecordsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserWinningRecordsReq) ProtoMessage()    {}
func (*GetUserWinningRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{40}
}
func (m *GetUserWinningRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWinningRecordsReq.Unmarshal(m, b)
}
func (m *GetUserWinningRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWinningRecordsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserWinningRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWinningRecordsReq.Merge(dst, src)
}
func (m *GetUserWinningRecordsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserWinningRecordsReq.Size(m)
}
func (m *GetUserWinningRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWinningRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWinningRecordsReq proto.InternalMessageInfo

func (m *GetUserWinningRecordsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserWinningRecordsReq) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *GetUserWinningRecordsReq) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *GetUserWinningRecordsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetUserWinningRecordsReq) GetQueryType() GetUserWinningRecordsReq_QueryType {
	if m != nil {
		return m.QueryType
	}
	return GetUserWinningRecordsReq_QUERY_TYPE_UNSPECIFIED
}

type GetUserWinningRecordsResp struct {
	Records              []*GameWinningRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetUserWinningRecordsResp) Reset()         { *m = GetUserWinningRecordsResp{} }
func (m *GetUserWinningRecordsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserWinningRecordsResp) ProtoMessage()    {}
func (*GetUserWinningRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{41}
}
func (m *GetUserWinningRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWinningRecordsResp.Unmarshal(m, b)
}
func (m *GetUserWinningRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWinningRecordsResp.Marshal(b, m, deterministic)
}
func (dst *GetUserWinningRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWinningRecordsResp.Merge(dst, src)
}
func (m *GetUserWinningRecordsResp) XXX_Size() int {
	return xxx_messageInfo_GetUserWinningRecordsResp.Size(m)
}
func (m *GetUserWinningRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWinningRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWinningRecordsResp proto.InternalMessageInfo

func (m *GetUserWinningRecordsResp) GetRecords() []*GameWinningRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

// 平台中奖记录  轮播用
type SimpleGameWinningRecord struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Gift                 *LevelGift `protobuf:"bytes,2,opt,name=gift,proto3" json:"gift,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SimpleGameWinningRecord) Reset()         { *m = SimpleGameWinningRecord{} }
func (m *SimpleGameWinningRecord) String() string { return proto.CompactTextString(m) }
func (*SimpleGameWinningRecord) ProtoMessage()    {}
func (*SimpleGameWinningRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{42}
}
func (m *SimpleGameWinningRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleGameWinningRecord.Unmarshal(m, b)
}
func (m *SimpleGameWinningRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleGameWinningRecord.Marshal(b, m, deterministic)
}
func (dst *SimpleGameWinningRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleGameWinningRecord.Merge(dst, src)
}
func (m *SimpleGameWinningRecord) XXX_Size() int {
	return xxx_messageInfo_SimpleGameWinningRecord.Size(m)
}
func (m *SimpleGameWinningRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleGameWinningRecord.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleGameWinningRecord proto.InternalMessageInfo

func (m *SimpleGameWinningRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SimpleGameWinningRecord) GetGift() *LevelGift {
	if m != nil {
		return m.Gift
	}
	return nil
}

// 获取最新的中奖纪录
type GetRecentWinningRecordsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecentWinningRecordsReq) Reset()         { *m = GetRecentWinningRecordsReq{} }
func (m *GetRecentWinningRecordsReq) String() string { return proto.CompactTextString(m) }
func (*GetRecentWinningRecordsReq) ProtoMessage()    {}
func (*GetRecentWinningRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{43}
}
func (m *GetRecentWinningRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentWinningRecordsReq.Unmarshal(m, b)
}
func (m *GetRecentWinningRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentWinningRecordsReq.Marshal(b, m, deterministic)
}
func (dst *GetRecentWinningRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentWinningRecordsReq.Merge(dst, src)
}
func (m *GetRecentWinningRecordsReq) XXX_Size() int {
	return xxx_messageInfo_GetRecentWinningRecordsReq.Size(m)
}
func (m *GetRecentWinningRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentWinningRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentWinningRecordsReq proto.InternalMessageInfo

func (m *GetRecentWinningRecordsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecentWinningRecordsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRecentWinningRecordsResp struct {
	Records              []*SimpleGameWinningRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetRecentWinningRecordsResp) Reset()         { *m = GetRecentWinningRecordsResp{} }
func (m *GetRecentWinningRecordsResp) String() string { return proto.CompactTextString(m) }
func (*GetRecentWinningRecordsResp) ProtoMessage()    {}
func (*GetRecentWinningRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{44}
}
func (m *GetRecentWinningRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentWinningRecordsResp.Unmarshal(m, b)
}
func (m *GetRecentWinningRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentWinningRecordsResp.Marshal(b, m, deterministic)
}
func (dst *GetRecentWinningRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentWinningRecordsResp.Merge(dst, src)
}
func (m *GetRecentWinningRecordsResp) XXX_Size() int {
	return xxx_messageInfo_GetRecentWinningRecordsResp.Size(m)
}
func (m *GetRecentWinningRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentWinningRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentWinningRecordsResp proto.InternalMessageInfo

func (m *GetRecentWinningRecordsResp) GetRecords() []*SimpleGameWinningRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

type PackageInfo struct {
	PackId               uint32   `protobuf:"varint,3,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackWorth            uint32   `protobuf:"varint,4,opt,name=pack_worth,json=packWorth,proto3" json:"pack_worth,omitempty"`
	PackName             string   `protobuf:"bytes,5,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string   `protobuf:"bytes,6,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackAmount           uint32   `protobuf:"varint,7,opt,name=pack_amount,json=packAmount,proto3" json:"pack_amount,omitempty"`
	GiftId               uint32   `protobuf:"varint,8,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	PackItemType         uint32   `protobuf:"varint,9,opt,name=pack_item_type,json=packItemType,proto3" json:"pack_item_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PackageInfo) Reset()         { *m = PackageInfo{} }
func (m *PackageInfo) String() string { return proto.CompactTextString(m) }
func (*PackageInfo) ProtoMessage()    {}
func (*PackageInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{45}
}
func (m *PackageInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PackageInfo.Unmarshal(m, b)
}
func (m *PackageInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PackageInfo.Marshal(b, m, deterministic)
}
func (dst *PackageInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PackageInfo.Merge(dst, src)
}
func (m *PackageInfo) XXX_Size() int {
	return xxx_messageInfo_PackageInfo.Size(m)
}
func (m *PackageInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PackageInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PackageInfo proto.InternalMessageInfo

func (m *PackageInfo) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *PackageInfo) GetPackWorth() uint32 {
	if m != nil {
		return m.PackWorth
	}
	return 0
}

func (m *PackageInfo) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *PackageInfo) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *PackageInfo) GetPackAmount() uint32 {
	if m != nil {
		return m.PackAmount
	}
	return 0
}

func (m *PackageInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PackageInfo) GetPackItemType() uint32 {
	if m != nil {
		return m.PackItemType
	}
	return 0
}

// 关卡信息
type LevelInfo struct {
	LevelId              uint32       `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	LevelName            string       `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	TicketNum            uint32       `protobuf:"varint,3,opt,name=ticket_num,json=ticketNum,proto3" json:"ticket_num,omitempty"`
	MountsInfo           *LevelMount  `protobuf:"bytes,4,opt,name=mounts_info,json=mountsInfo,proto3" json:"mounts_info,omitempty"`
	MountsAwardDays      uint32       `protobuf:"varint,5,opt,name=mounts_award_days,json=mountsAwardDays,proto3" json:"mounts_award_days,omitempty"`
	MaxN                 uint32       `protobuf:"varint,6,opt,name=max_n,json=maxN,proto3" json:"max_n,omitempty"`
	MaxNFixed            bool         `protobuf:"varint,7,opt,name=max_n_fixed,json=maxNFixed,proto3" json:"max_n_fixed,omitempty"`
	PackInfo             *PackageInfo `protobuf:"bytes,8,opt,name=pack_info,json=packInfo,proto3" json:"pack_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LevelInfo) Reset()         { *m = LevelInfo{} }
func (m *LevelInfo) String() string { return proto.CompactTextString(m) }
func (*LevelInfo) ProtoMessage()    {}
func (*LevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{46}
}
func (m *LevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelInfo.Unmarshal(m, b)
}
func (m *LevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelInfo.Marshal(b, m, deterministic)
}
func (dst *LevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelInfo.Merge(dst, src)
}
func (m *LevelInfo) XXX_Size() int {
	return xxx_messageInfo_LevelInfo.Size(m)
}
func (m *LevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LevelInfo proto.InternalMessageInfo

func (m *LevelInfo) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *LevelInfo) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LevelInfo) GetTicketNum() uint32 {
	if m != nil {
		return m.TicketNum
	}
	return 0
}

func (m *LevelInfo) GetMountsInfo() *LevelMount {
	if m != nil {
		return m.MountsInfo
	}
	return nil
}

func (m *LevelInfo) GetMountsAwardDays() uint32 {
	if m != nil {
		return m.MountsAwardDays
	}
	return 0
}

func (m *LevelInfo) GetMaxN() uint32 {
	if m != nil {
		return m.MaxN
	}
	return 0
}

func (m *LevelInfo) GetMaxNFixed() bool {
	if m != nil {
		return m.MaxNFixed
	}
	return false
}

func (m *LevelInfo) GetPackInfo() *PackageInfo {
	if m != nil {
		return m.PackInfo
	}
	return nil
}

// 关卡奖池
type LevelPrizeInfo struct {
	ResultType           ResultType   `protobuf:"varint,1,opt,name=result_type,json=resultType,proto3,enum=cat_canteen.ResultType" json:"result_type,omitempty"`
	Weight               uint32       `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	PackInfo             *PackageInfo `protobuf:"bytes,3,opt,name=pack_info,json=packInfo,proto3" json:"pack_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LevelPrizeInfo) Reset()         { *m = LevelPrizeInfo{} }
func (m *LevelPrizeInfo) String() string { return proto.CompactTextString(m) }
func (*LevelPrizeInfo) ProtoMessage()    {}
func (*LevelPrizeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{47}
}
func (m *LevelPrizeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelPrizeInfo.Unmarshal(m, b)
}
func (m *LevelPrizeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelPrizeInfo.Marshal(b, m, deterministic)
}
func (dst *LevelPrizeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelPrizeInfo.Merge(dst, src)
}
func (m *LevelPrizeInfo) XXX_Size() int {
	return xxx_messageInfo_LevelPrizeInfo.Size(m)
}
func (m *LevelPrizeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelPrizeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LevelPrizeInfo proto.InternalMessageInfo

func (m *LevelPrizeInfo) GetResultType() ResultType {
	if m != nil {
		return m.ResultType
	}
	return ResultType_RESULT_UNSPECIFIED
}

func (m *LevelPrizeInfo) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *LevelPrizeInfo) GetPackInfo() *PackageInfo {
	if m != nil {
		return m.PackInfo
	}
	return nil
}

type LevelInfoWithPrizePool struct {
	LevelInfo            *LevelInfo        `protobuf:"bytes,1,opt,name=level_info,json=levelInfo,proto3" json:"level_info,omitempty"`
	PrizeList            []*LevelPrizeInfo `protobuf:"bytes,2,rep,name=prize_list,json=prizeList,proto3" json:"prize_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *LevelInfoWithPrizePool) Reset()         { *m = LevelInfoWithPrizePool{} }
func (m *LevelInfoWithPrizePool) String() string { return proto.CompactTextString(m) }
func (*LevelInfoWithPrizePool) ProtoMessage()    {}
func (*LevelInfoWithPrizePool) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{48}
}
func (m *LevelInfoWithPrizePool) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelInfoWithPrizePool.Unmarshal(m, b)
}
func (m *LevelInfoWithPrizePool) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelInfoWithPrizePool.Marshal(b, m, deterministic)
}
func (dst *LevelInfoWithPrizePool) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelInfoWithPrizePool.Merge(dst, src)
}
func (m *LevelInfoWithPrizePool) XXX_Size() int {
	return xxx_messageInfo_LevelInfoWithPrizePool.Size(m)
}
func (m *LevelInfoWithPrizePool) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelInfoWithPrizePool.DiscardUnknown(m)
}

var xxx_messageInfo_LevelInfoWithPrizePool proto.InternalMessageInfo

func (m *LevelInfoWithPrizePool) GetLevelInfo() *LevelInfo {
	if m != nil {
		return m.LevelInfo
	}
	return nil
}

func (m *LevelInfoWithPrizePool) GetPrizeList() []*LevelPrizeInfo {
	if m != nil {
		return m.PrizeList
	}
	return nil
}

type LevelConfVerify struct {
	InfoList             []*LevelInfoWithPrizePool `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	MarshalKey           string                    `protobuf:"bytes,2,opt,name=marshal_key,json=marshalKey,proto3" json:"marshal_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *LevelConfVerify) Reset()         { *m = LevelConfVerify{} }
func (m *LevelConfVerify) String() string { return proto.CompactTextString(m) }
func (*LevelConfVerify) ProtoMessage()    {}
func (*LevelConfVerify) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{49}
}
func (m *LevelConfVerify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConfVerify.Unmarshal(m, b)
}
func (m *LevelConfVerify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConfVerify.Marshal(b, m, deterministic)
}
func (dst *LevelConfVerify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConfVerify.Merge(dst, src)
}
func (m *LevelConfVerify) XXX_Size() int {
	return xxx_messageInfo_LevelConfVerify.Size(m)
}
func (m *LevelConfVerify) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConfVerify.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConfVerify proto.InternalMessageInfo

func (m *LevelConfVerify) GetInfoList() []*LevelInfoWithPrizePool {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *LevelConfVerify) GetMarshalKey() string {
	if m != nil {
		return m.MarshalKey
	}
	return ""
}

type SetLevelConfReq struct {
	InfoList             []*LevelInfoWithPrizePool `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	EffectTime           uint32                    `protobuf:"varint,2,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	Token                string                    `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SetLevelConfReq) Reset()         { *m = SetLevelConfReq{} }
func (m *SetLevelConfReq) String() string { return proto.CompactTextString(m) }
func (*SetLevelConfReq) ProtoMessage()    {}
func (*SetLevelConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{50}
}
func (m *SetLevelConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLevelConfReq.Unmarshal(m, b)
}
func (m *SetLevelConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLevelConfReq.Marshal(b, m, deterministic)
}
func (dst *SetLevelConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLevelConfReq.Merge(dst, src)
}
func (m *SetLevelConfReq) XXX_Size() int {
	return xxx_messageInfo_SetLevelConfReq.Size(m)
}
func (m *SetLevelConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLevelConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetLevelConfReq proto.InternalMessageInfo

func (m *SetLevelConfReq) GetInfoList() []*LevelInfoWithPrizePool {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *SetLevelConfReq) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

func (m *SetLevelConfReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type SetLevelConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLevelConfResp) Reset()         { *m = SetLevelConfResp{} }
func (m *SetLevelConfResp) String() string { return proto.CompactTextString(m) }
func (*SetLevelConfResp) ProtoMessage()    {}
func (*SetLevelConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{51}
}
func (m *SetLevelConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLevelConfResp.Unmarshal(m, b)
}
func (m *SetLevelConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLevelConfResp.Marshal(b, m, deterministic)
}
func (dst *SetLevelConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLevelConfResp.Merge(dst, src)
}
func (m *SetLevelConfResp) XXX_Size() int {
	return xxx_messageInfo_SetLevelConfResp.Size(m)
}
func (m *SetLevelConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLevelConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetLevelConfResp proto.InternalMessageInfo

type DelAllPendingLevelConfReq struct {
	EffectTime           uint32   `protobuf:"varint,1,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAllPendingLevelConfReq) Reset()         { *m = DelAllPendingLevelConfReq{} }
func (m *DelAllPendingLevelConfReq) String() string { return proto.CompactTextString(m) }
func (*DelAllPendingLevelConfReq) ProtoMessage()    {}
func (*DelAllPendingLevelConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{52}
}
func (m *DelAllPendingLevelConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAllPendingLevelConfReq.Unmarshal(m, b)
}
func (m *DelAllPendingLevelConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAllPendingLevelConfReq.Marshal(b, m, deterministic)
}
func (dst *DelAllPendingLevelConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAllPendingLevelConfReq.Merge(dst, src)
}
func (m *DelAllPendingLevelConfReq) XXX_Size() int {
	return xxx_messageInfo_DelAllPendingLevelConfReq.Size(m)
}
func (m *DelAllPendingLevelConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAllPendingLevelConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelAllPendingLevelConfReq proto.InternalMessageInfo

func (m *DelAllPendingLevelConfReq) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

type DelAllPendingLevelConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAllPendingLevelConfResp) Reset()         { *m = DelAllPendingLevelConfResp{} }
func (m *DelAllPendingLevelConfResp) String() string { return proto.CompactTextString(m) }
func (*DelAllPendingLevelConfResp) ProtoMessage()    {}
func (*DelAllPendingLevelConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{53}
}
func (m *DelAllPendingLevelConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAllPendingLevelConfResp.Unmarshal(m, b)
}
func (m *DelAllPendingLevelConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAllPendingLevelConfResp.Marshal(b, m, deterministic)
}
func (dst *DelAllPendingLevelConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAllPendingLevelConfResp.Merge(dst, src)
}
func (m *DelAllPendingLevelConfResp) XXX_Size() int {
	return xxx_messageInfo_DelAllPendingLevelConfResp.Size(m)
}
func (m *DelAllPendingLevelConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAllPendingLevelConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelAllPendingLevelConfResp proto.InternalMessageInfo

type GetAllLevelConfReq struct {
	ConfType             GetAllLevelConfReq_ConfType `protobuf:"varint,1,opt,name=conf_type,json=confType,proto3,enum=cat_canteen.GetAllLevelConfReq_ConfType" json:"conf_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetAllLevelConfReq) Reset()         { *m = GetAllLevelConfReq{} }
func (m *GetAllLevelConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllLevelConfReq) ProtoMessage()    {}
func (*GetAllLevelConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{54}
}
func (m *GetAllLevelConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllLevelConfReq.Unmarshal(m, b)
}
func (m *GetAllLevelConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllLevelConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllLevelConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllLevelConfReq.Merge(dst, src)
}
func (m *GetAllLevelConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllLevelConfReq.Size(m)
}
func (m *GetAllLevelConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllLevelConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllLevelConfReq proto.InternalMessageInfo

func (m *GetAllLevelConfReq) GetConfType() GetAllLevelConfReq_ConfType {
	if m != nil {
		return m.ConfType
	}
	return GetAllLevelConfReq_ConfType_UNEXPECTED
}

type LevelConfWithConfVersion struct {
	EffectTime           uint32                    `protobuf:"varint,1,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	InfoList             []*LevelInfoWithPrizePool `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *LevelConfWithConfVersion) Reset()         { *m = LevelConfWithConfVersion{} }
func (m *LevelConfWithConfVersion) String() string { return proto.CompactTextString(m) }
func (*LevelConfWithConfVersion) ProtoMessage()    {}
func (*LevelConfWithConfVersion) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{55}
}
func (m *LevelConfWithConfVersion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConfWithConfVersion.Unmarshal(m, b)
}
func (m *LevelConfWithConfVersion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConfWithConfVersion.Marshal(b, m, deterministic)
}
func (dst *LevelConfWithConfVersion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConfWithConfVersion.Merge(dst, src)
}
func (m *LevelConfWithConfVersion) XXX_Size() int {
	return xxx_messageInfo_LevelConfWithConfVersion.Size(m)
}
func (m *LevelConfWithConfVersion) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConfWithConfVersion.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConfWithConfVersion proto.InternalMessageInfo

func (m *LevelConfWithConfVersion) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

func (m *LevelConfWithConfVersion) GetInfoList() []*LevelInfoWithPrizePool {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type GetAllLevelConfResp struct {
	Conf                 *LevelConfWithConfVersion `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetAllLevelConfResp) Reset()         { *m = GetAllLevelConfResp{} }
func (m *GetAllLevelConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllLevelConfResp) ProtoMessage()    {}
func (*GetAllLevelConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{56}
}
func (m *GetAllLevelConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllLevelConfResp.Unmarshal(m, b)
}
func (m *GetAllLevelConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllLevelConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllLevelConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllLevelConfResp.Merge(dst, src)
}
func (m *GetAllLevelConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllLevelConfResp.Size(m)
}
func (m *GetAllLevelConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllLevelConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllLevelConfResp proto.InternalMessageInfo

func (m *GetAllLevelConfResp) GetConf() *LevelConfWithConfVersion {
	if m != nil {
		return m.Conf
	}
	return nil
}

type SimulateProfit struct {
	LevelId              uint32   `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	ExpectProfit         float32  `protobuf:"fixed32,2,opt,name=expect_profit,json=expectProfit,proto3" json:"expect_profit,omitempty"`
	ExpectProfitRadio    float32  `protobuf:"fixed32,3,opt,name=expect_profit_radio,json=expectProfitRadio,proto3" json:"expect_profit_radio,omitempty"`
	AverageProfit        float32  `protobuf:"fixed32,4,opt,name=average_profit,json=averageProfit,proto3" json:"average_profit,omitempty"`
	Pass                 bool     `protobuf:"varint,5,opt,name=pass,proto3" json:"pass,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimulateProfit) Reset()         { *m = SimulateProfit{} }
func (m *SimulateProfit) String() string { return proto.CompactTextString(m) }
func (*SimulateProfit) ProtoMessage()    {}
func (*SimulateProfit) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{57}
}
func (m *SimulateProfit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimulateProfit.Unmarshal(m, b)
}
func (m *SimulateProfit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimulateProfit.Marshal(b, m, deterministic)
}
func (dst *SimulateProfit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimulateProfit.Merge(dst, src)
}
func (m *SimulateProfit) XXX_Size() int {
	return xxx_messageInfo_SimulateProfit.Size(m)
}
func (m *SimulateProfit) XXX_DiscardUnknown() {
	xxx_messageInfo_SimulateProfit.DiscardUnknown(m)
}

var xxx_messageInfo_SimulateProfit proto.InternalMessageInfo

func (m *SimulateProfit) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *SimulateProfit) GetExpectProfit() float32 {
	if m != nil {
		return m.ExpectProfit
	}
	return 0
}

func (m *SimulateProfit) GetExpectProfitRadio() float32 {
	if m != nil {
		return m.ExpectProfitRadio
	}
	return 0
}

func (m *SimulateProfit) GetAverageProfit() float32 {
	if m != nil {
		return m.AverageProfit
	}
	return 0
}

func (m *SimulateProfit) GetPass() bool {
	if m != nil {
		return m.Pass
	}
	return false
}

// 模拟收益
type SimulateWithPrizePoolReq struct {
	InfoList             []*LevelInfoWithPrizePool `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SimulateWithPrizePoolReq) Reset()         { *m = SimulateWithPrizePoolReq{} }
func (m *SimulateWithPrizePoolReq) String() string { return proto.CompactTextString(m) }
func (*SimulateWithPrizePoolReq) ProtoMessage()    {}
func (*SimulateWithPrizePoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{58}
}
func (m *SimulateWithPrizePoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimulateWithPrizePoolReq.Unmarshal(m, b)
}
func (m *SimulateWithPrizePoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimulateWithPrizePoolReq.Marshal(b, m, deterministic)
}
func (dst *SimulateWithPrizePoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimulateWithPrizePoolReq.Merge(dst, src)
}
func (m *SimulateWithPrizePoolReq) XXX_Size() int {
	return xxx_messageInfo_SimulateWithPrizePoolReq.Size(m)
}
func (m *SimulateWithPrizePoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SimulateWithPrizePoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_SimulateWithPrizePoolReq proto.InternalMessageInfo

func (m *SimulateWithPrizePoolReq) GetInfoList() []*LevelInfoWithPrizePool {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type SimulateWithPrizePoolResp struct {
	ProfitList           []*SimulateProfit `protobuf:"bytes,1,rep,name=profit_list,json=profitList,proto3" json:"profit_list,omitempty"`
	Token                string            `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SimulateWithPrizePoolResp) Reset()         { *m = SimulateWithPrizePoolResp{} }
func (m *SimulateWithPrizePoolResp) String() string { return proto.CompactTextString(m) }
func (*SimulateWithPrizePoolResp) ProtoMessage()    {}
func (*SimulateWithPrizePoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{59}
}
func (m *SimulateWithPrizePoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimulateWithPrizePoolResp.Unmarshal(m, b)
}
func (m *SimulateWithPrizePoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimulateWithPrizePoolResp.Marshal(b, m, deterministic)
}
func (dst *SimulateWithPrizePoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimulateWithPrizePoolResp.Merge(dst, src)
}
func (m *SimulateWithPrizePoolResp) XXX_Size() int {
	return xxx_messageInfo_SimulateWithPrizePoolResp.Size(m)
}
func (m *SimulateWithPrizePoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SimulateWithPrizePoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_SimulateWithPrizePoolResp proto.InternalMessageInfo

func (m *SimulateWithPrizePoolResp) GetProfitList() []*SimulateProfit {
	if m != nil {
		return m.ProfitList
	}
	return nil
}

func (m *SimulateWithPrizePoolResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

// 防违规配置
type SetCatCanteenLimitConfReq struct {
	DailyBuyChanceLimit  uint32   `protobuf:"varint,1,opt,name=daily_buy_chance_limit,json=dailyBuyChanceLimit,proto3" json:"daily_buy_chance_limit,omitempty"`
	DailyUseChanceLimit  uint32   `protobuf:"varint,2,opt,name=daily_use_chance_limit,json=dailyUseChanceLimit,proto3" json:"daily_use_chance_limit,omitempty"`
	SingleBuyChanceLimit uint32   `protobuf:"varint,3,opt,name=single_buy_chance_limit,json=singleBuyChanceLimit,proto3" json:"single_buy_chance_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCatCanteenLimitConfReq) Reset()         { *m = SetCatCanteenLimitConfReq{} }
func (m *SetCatCanteenLimitConfReq) String() string { return proto.CompactTextString(m) }
func (*SetCatCanteenLimitConfReq) ProtoMessage()    {}
func (*SetCatCanteenLimitConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{60}
}
func (m *SetCatCanteenLimitConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCatCanteenLimitConfReq.Unmarshal(m, b)
}
func (m *SetCatCanteenLimitConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCatCanteenLimitConfReq.Marshal(b, m, deterministic)
}
func (dst *SetCatCanteenLimitConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCatCanteenLimitConfReq.Merge(dst, src)
}
func (m *SetCatCanteenLimitConfReq) XXX_Size() int {
	return xxx_messageInfo_SetCatCanteenLimitConfReq.Size(m)
}
func (m *SetCatCanteenLimitConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCatCanteenLimitConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCatCanteenLimitConfReq proto.InternalMessageInfo

func (m *SetCatCanteenLimitConfReq) GetDailyBuyChanceLimit() uint32 {
	if m != nil {
		return m.DailyBuyChanceLimit
	}
	return 0
}

func (m *SetCatCanteenLimitConfReq) GetDailyUseChanceLimit() uint32 {
	if m != nil {
		return m.DailyUseChanceLimit
	}
	return 0
}

func (m *SetCatCanteenLimitConfReq) GetSingleBuyChanceLimit() uint32 {
	if m != nil {
		return m.SingleBuyChanceLimit
	}
	return 0
}

type SetCatCanteenLimitConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCatCanteenLimitConfResp) Reset()         { *m = SetCatCanteenLimitConfResp{} }
func (m *SetCatCanteenLimitConfResp) String() string { return proto.CompactTextString(m) }
func (*SetCatCanteenLimitConfResp) ProtoMessage()    {}
func (*SetCatCanteenLimitConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{61}
}
func (m *SetCatCanteenLimitConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCatCanteenLimitConfResp.Unmarshal(m, b)
}
func (m *SetCatCanteenLimitConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCatCanteenLimitConfResp.Marshal(b, m, deterministic)
}
func (dst *SetCatCanteenLimitConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCatCanteenLimitConfResp.Merge(dst, src)
}
func (m *SetCatCanteenLimitConfResp) XXX_Size() int {
	return xxx_messageInfo_SetCatCanteenLimitConfResp.Size(m)
}
func (m *SetCatCanteenLimitConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCatCanteenLimitConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCatCanteenLimitConfResp proto.InternalMessageInfo

// 获取防违规配置
type GetCatCanteenLimitConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCatCanteenLimitConfReq) Reset()         { *m = GetCatCanteenLimitConfReq{} }
func (m *GetCatCanteenLimitConfReq) String() string { return proto.CompactTextString(m) }
func (*GetCatCanteenLimitConfReq) ProtoMessage()    {}
func (*GetCatCanteenLimitConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{62}
}
func (m *GetCatCanteenLimitConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatCanteenLimitConfReq.Unmarshal(m, b)
}
func (m *GetCatCanteenLimitConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatCanteenLimitConfReq.Marshal(b, m, deterministic)
}
func (dst *GetCatCanteenLimitConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatCanteenLimitConfReq.Merge(dst, src)
}
func (m *GetCatCanteenLimitConfReq) XXX_Size() int {
	return xxx_messageInfo_GetCatCanteenLimitConfReq.Size(m)
}
func (m *GetCatCanteenLimitConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatCanteenLimitConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatCanteenLimitConfReq proto.InternalMessageInfo

type GetCatCanteenLimitConfResp struct {
	DailyBuyChanceLimit  uint32   `protobuf:"varint,1,opt,name=daily_buy_chance_limit,json=dailyBuyChanceLimit,proto3" json:"daily_buy_chance_limit,omitempty"`
	DailyUseChanceLimit  uint32   `protobuf:"varint,2,opt,name=daily_use_chance_limit,json=dailyUseChanceLimit,proto3" json:"daily_use_chance_limit,omitempty"`
	SingleBuyChanceLimit uint32   `protobuf:"varint,3,opt,name=single_buy_chance_limit,json=singleBuyChanceLimit,proto3" json:"single_buy_chance_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCatCanteenLimitConfResp) Reset()         { *m = GetCatCanteenLimitConfResp{} }
func (m *GetCatCanteenLimitConfResp) String() string { return proto.CompactTextString(m) }
func (*GetCatCanteenLimitConfResp) ProtoMessage()    {}
func (*GetCatCanteenLimitConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{63}
}
func (m *GetCatCanteenLimitConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatCanteenLimitConfResp.Unmarshal(m, b)
}
func (m *GetCatCanteenLimitConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatCanteenLimitConfResp.Marshal(b, m, deterministic)
}
func (dst *GetCatCanteenLimitConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatCanteenLimitConfResp.Merge(dst, src)
}
func (m *GetCatCanteenLimitConfResp) XXX_Size() int {
	return xxx_messageInfo_GetCatCanteenLimitConfResp.Size(m)
}
func (m *GetCatCanteenLimitConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatCanteenLimitConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatCanteenLimitConfResp proto.InternalMessageInfo

func (m *GetCatCanteenLimitConfResp) GetDailyBuyChanceLimit() uint32 {
	if m != nil {
		return m.DailyBuyChanceLimit
	}
	return 0
}

func (m *GetCatCanteenLimitConfResp) GetDailyUseChanceLimit() uint32 {
	if m != nil {
		return m.DailyUseChanceLimit
	}
	return 0
}

func (m *GetCatCanteenLimitConfResp) GetSingleBuyChanceLimit() uint32 {
	if m != nil {
		return m.SingleBuyChanceLimit
	}
	return 0
}

// 光效配置
type LightEffectConfig struct {
	ConfId               uint32   `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	PackId               uint32   `protobuf:"varint,2,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	EffectId             uint32   `protobuf:"varint,3,opt,name=effect_id,json=effectId,proto3" json:"effect_id,omitempty"`
	PackName             string   `protobuf:"bytes,4,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	AwardText            string   `protobuf:"bytes,5,opt,name=award_text,json=awardText,proto3" json:"award_text,omitempty"`
	Mtime                uint32   `protobuf:"varint,6,opt,name=mtime,proto3" json:"mtime,omitempty"`
	PrizeName            string   `protobuf:"bytes,7,opt,name=prize_name,json=prizeName,proto3" json:"prize_name,omitempty"`
	Url                  string   `protobuf:"bytes,8,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LightEffectConfig) Reset()         { *m = LightEffectConfig{} }
func (m *LightEffectConfig) String() string { return proto.CompactTextString(m) }
func (*LightEffectConfig) ProtoMessage()    {}
func (*LightEffectConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{64}
}
func (m *LightEffectConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LightEffectConfig.Unmarshal(m, b)
}
func (m *LightEffectConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LightEffectConfig.Marshal(b, m, deterministic)
}
func (dst *LightEffectConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LightEffectConfig.Merge(dst, src)
}
func (m *LightEffectConfig) XXX_Size() int {
	return xxx_messageInfo_LightEffectConfig.Size(m)
}
func (m *LightEffectConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LightEffectConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LightEffectConfig proto.InternalMessageInfo

func (m *LightEffectConfig) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *LightEffectConfig) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *LightEffectConfig) GetEffectId() uint32 {
	if m != nil {
		return m.EffectId
	}
	return 0
}

func (m *LightEffectConfig) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *LightEffectConfig) GetAwardText() string {
	if m != nil {
		return m.AwardText
	}
	return ""
}

func (m *LightEffectConfig) GetMtime() uint32 {
	if m != nil {
		return m.Mtime
	}
	return 0
}

func (m *LightEffectConfig) GetPrizeName() string {
	if m != nil {
		return m.PrizeName
	}
	return ""
}

func (m *LightEffectConfig) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type AddCatLightEffectsReq struct {
	ConfList             []*LightEffectConfig `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddCatLightEffectsReq) Reset()         { *m = AddCatLightEffectsReq{} }
func (m *AddCatLightEffectsReq) String() string { return proto.CompactTextString(m) }
func (*AddCatLightEffectsReq) ProtoMessage()    {}
func (*AddCatLightEffectsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{65}
}
func (m *AddCatLightEffectsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCatLightEffectsReq.Unmarshal(m, b)
}
func (m *AddCatLightEffectsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCatLightEffectsReq.Marshal(b, m, deterministic)
}
func (dst *AddCatLightEffectsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCatLightEffectsReq.Merge(dst, src)
}
func (m *AddCatLightEffectsReq) XXX_Size() int {
	return xxx_messageInfo_AddCatLightEffectsReq.Size(m)
}
func (m *AddCatLightEffectsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCatLightEffectsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCatLightEffectsReq proto.InternalMessageInfo

func (m *AddCatLightEffectsReq) GetConfList() []*LightEffectConfig {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type AddCatLightEffectsResp struct {
	PackIdList           []uint32 `protobuf:"varint,1,rep,packed,name=pack_id_list,json=packIdList,proto3" json:"pack_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCatLightEffectsResp) Reset()         { *m = AddCatLightEffectsResp{} }
func (m *AddCatLightEffectsResp) String() string { return proto.CompactTextString(m) }
func (*AddCatLightEffectsResp) ProtoMessage()    {}
func (*AddCatLightEffectsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{66}
}
func (m *AddCatLightEffectsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCatLightEffectsResp.Unmarshal(m, b)
}
func (m *AddCatLightEffectsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCatLightEffectsResp.Marshal(b, m, deterministic)
}
func (dst *AddCatLightEffectsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCatLightEffectsResp.Merge(dst, src)
}
func (m *AddCatLightEffectsResp) XXX_Size() int {
	return xxx_messageInfo_AddCatLightEffectsResp.Size(m)
}
func (m *AddCatLightEffectsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCatLightEffectsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCatLightEffectsResp proto.InternalMessageInfo

func (m *AddCatLightEffectsResp) GetPackIdList() []uint32 {
	if m != nil {
		return m.PackIdList
	}
	return nil
}

type UpdateCatLightEffectReq struct {
	ConfId               uint32             `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	Conf                 *LightEffectConfig `protobuf:"bytes,2,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateCatLightEffectReq) Reset()         { *m = UpdateCatLightEffectReq{} }
func (m *UpdateCatLightEffectReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCatLightEffectReq) ProtoMessage()    {}
func (*UpdateCatLightEffectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{67}
}
func (m *UpdateCatLightEffectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCatLightEffectReq.Unmarshal(m, b)
}
func (m *UpdateCatLightEffectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCatLightEffectReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCatLightEffectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCatLightEffectReq.Merge(dst, src)
}
func (m *UpdateCatLightEffectReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCatLightEffectReq.Size(m)
}
func (m *UpdateCatLightEffectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCatLightEffectReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCatLightEffectReq proto.InternalMessageInfo

func (m *UpdateCatLightEffectReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *UpdateCatLightEffectReq) GetConf() *LightEffectConfig {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateCatLightEffectResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCatLightEffectResp) Reset()         { *m = UpdateCatLightEffectResp{} }
func (m *UpdateCatLightEffectResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCatLightEffectResp) ProtoMessage()    {}
func (*UpdateCatLightEffectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{68}
}
func (m *UpdateCatLightEffectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCatLightEffectResp.Unmarshal(m, b)
}
func (m *UpdateCatLightEffectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCatLightEffectResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCatLightEffectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCatLightEffectResp.Merge(dst, src)
}
func (m *UpdateCatLightEffectResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCatLightEffectResp.Size(m)
}
func (m *UpdateCatLightEffectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCatLightEffectResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCatLightEffectResp proto.InternalMessageInfo

type DelCatLightEffectByConfIdReq struct {
	ConfIdList           []uint32 `protobuf:"varint,1,rep,packed,name=conf_id_list,json=confIdList,proto3" json:"conf_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCatLightEffectByConfIdReq) Reset()         { *m = DelCatLightEffectByConfIdReq{} }
func (m *DelCatLightEffectByConfIdReq) String() string { return proto.CompactTextString(m) }
func (*DelCatLightEffectByConfIdReq) ProtoMessage()    {}
func (*DelCatLightEffectByConfIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{69}
}
func (m *DelCatLightEffectByConfIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCatLightEffectByConfIdReq.Unmarshal(m, b)
}
func (m *DelCatLightEffectByConfIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCatLightEffectByConfIdReq.Marshal(b, m, deterministic)
}
func (dst *DelCatLightEffectByConfIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCatLightEffectByConfIdReq.Merge(dst, src)
}
func (m *DelCatLightEffectByConfIdReq) XXX_Size() int {
	return xxx_messageInfo_DelCatLightEffectByConfIdReq.Size(m)
}
func (m *DelCatLightEffectByConfIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCatLightEffectByConfIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelCatLightEffectByConfIdReq proto.InternalMessageInfo

func (m *DelCatLightEffectByConfIdReq) GetConfIdList() []uint32 {
	if m != nil {
		return m.ConfIdList
	}
	return nil
}

type DelCatLightEffectByConfIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCatLightEffectByConfIdResp) Reset()         { *m = DelCatLightEffectByConfIdResp{} }
func (m *DelCatLightEffectByConfIdResp) String() string { return proto.CompactTextString(m) }
func (*DelCatLightEffectByConfIdResp) ProtoMessage()    {}
func (*DelCatLightEffectByConfIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{70}
}
func (m *DelCatLightEffectByConfIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCatLightEffectByConfIdResp.Unmarshal(m, b)
}
func (m *DelCatLightEffectByConfIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCatLightEffectByConfIdResp.Marshal(b, m, deterministic)
}
func (dst *DelCatLightEffectByConfIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCatLightEffectByConfIdResp.Merge(dst, src)
}
func (m *DelCatLightEffectByConfIdResp) XXX_Size() int {
	return xxx_messageInfo_DelCatLightEffectByConfIdResp.Size(m)
}
func (m *DelCatLightEffectByConfIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCatLightEffectByConfIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelCatLightEffectByConfIdResp proto.InternalMessageInfo

type GetAllCatLightEffectsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllCatLightEffectsReq) Reset()         { *m = GetAllCatLightEffectsReq{} }
func (m *GetAllCatLightEffectsReq) String() string { return proto.CompactTextString(m) }
func (*GetAllCatLightEffectsReq) ProtoMessage()    {}
func (*GetAllCatLightEffectsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{71}
}
func (m *GetAllCatLightEffectsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCatLightEffectsReq.Unmarshal(m, b)
}
func (m *GetAllCatLightEffectsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCatLightEffectsReq.Marshal(b, m, deterministic)
}
func (dst *GetAllCatLightEffectsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCatLightEffectsReq.Merge(dst, src)
}
func (m *GetAllCatLightEffectsReq) XXX_Size() int {
	return xxx_messageInfo_GetAllCatLightEffectsReq.Size(m)
}
func (m *GetAllCatLightEffectsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCatLightEffectsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCatLightEffectsReq proto.InternalMessageInfo

type GetAllCatLightEffectsResp struct {
	ConfList             []*LightEffectConfig `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetAllCatLightEffectsResp) Reset()         { *m = GetAllCatLightEffectsResp{} }
func (m *GetAllCatLightEffectsResp) String() string { return proto.CompactTextString(m) }
func (*GetAllCatLightEffectsResp) ProtoMessage()    {}
func (*GetAllCatLightEffectsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{72}
}
func (m *GetAllCatLightEffectsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCatLightEffectsResp.Unmarshal(m, b)
}
func (m *GetAllCatLightEffectsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCatLightEffectsResp.Marshal(b, m, deterministic)
}
func (dst *GetAllCatLightEffectsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCatLightEffectsResp.Merge(dst, src)
}
func (m *GetAllCatLightEffectsResp) XXX_Size() int {
	return xxx_messageInfo_GetAllCatLightEffectsResp.Size(m)
}
func (m *GetAllCatLightEffectsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCatLightEffectsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCatLightEffectsResp proto.InternalMessageInfo

func (m *GetAllCatLightEffectsResp) GetConfList() []*LightEffectConfig {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type BatGetLightEffectByPackIdListReq struct {
	PackIdList           []uint32 `protobuf:"varint,1,rep,packed,name=pack_id_list,json=packIdList,proto3" json:"pack_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetLightEffectByPackIdListReq) Reset()         { *m = BatGetLightEffectByPackIdListReq{} }
func (m *BatGetLightEffectByPackIdListReq) String() string { return proto.CompactTextString(m) }
func (*BatGetLightEffectByPackIdListReq) ProtoMessage()    {}
func (*BatGetLightEffectByPackIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{73}
}
func (m *BatGetLightEffectByPackIdListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetLightEffectByPackIdListReq.Unmarshal(m, b)
}
func (m *BatGetLightEffectByPackIdListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetLightEffectByPackIdListReq.Marshal(b, m, deterministic)
}
func (dst *BatGetLightEffectByPackIdListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetLightEffectByPackIdListReq.Merge(dst, src)
}
func (m *BatGetLightEffectByPackIdListReq) XXX_Size() int {
	return xxx_messageInfo_BatGetLightEffectByPackIdListReq.Size(m)
}
func (m *BatGetLightEffectByPackIdListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetLightEffectByPackIdListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetLightEffectByPackIdListReq proto.InternalMessageInfo

func (m *BatGetLightEffectByPackIdListReq) GetPackIdList() []uint32 {
	if m != nil {
		return m.PackIdList
	}
	return nil
}

type BatGetLightEffectByPackIdListResp struct {
	ConfList             []*LightEffectConfig `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatGetLightEffectByPackIdListResp) Reset()         { *m = BatGetLightEffectByPackIdListResp{} }
func (m *BatGetLightEffectByPackIdListResp) String() string { return proto.CompactTextString(m) }
func (*BatGetLightEffectByPackIdListResp) ProtoMessage()    {}
func (*BatGetLightEffectByPackIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{74}
}
func (m *BatGetLightEffectByPackIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetLightEffectByPackIdListResp.Unmarshal(m, b)
}
func (m *BatGetLightEffectByPackIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetLightEffectByPackIdListResp.Marshal(b, m, deterministic)
}
func (dst *BatGetLightEffectByPackIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetLightEffectByPackIdListResp.Merge(dst, src)
}
func (m *BatGetLightEffectByPackIdListResp) XXX_Size() int {
	return xxx_messageInfo_BatGetLightEffectByPackIdListResp.Size(m)
}
func (m *BatGetLightEffectByPackIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetLightEffectByPackIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetLightEffectByPackIdListResp proto.InternalMessageInfo

func (m *BatGetLightEffectByPackIdListResp) GetConfList() []*LightEffectConfig {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type GetCatCanteenExemptValueReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCatCanteenExemptValueReq) Reset()         { *m = GetCatCanteenExemptValueReq{} }
func (m *GetCatCanteenExemptValueReq) String() string { return proto.CompactTextString(m) }
func (*GetCatCanteenExemptValueReq) ProtoMessage()    {}
func (*GetCatCanteenExemptValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{75}
}
func (m *GetCatCanteenExemptValueReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatCanteenExemptValueReq.Unmarshal(m, b)
}
func (m *GetCatCanteenExemptValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatCanteenExemptValueReq.Marshal(b, m, deterministic)
}
func (dst *GetCatCanteenExemptValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatCanteenExemptValueReq.Merge(dst, src)
}
func (m *GetCatCanteenExemptValueReq) XXX_Size() int {
	return xxx_messageInfo_GetCatCanteenExemptValueReq.Size(m)
}
func (m *GetCatCanteenExemptValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatCanteenExemptValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatCanteenExemptValueReq proto.InternalMessageInfo

func (m *GetCatCanteenExemptValueReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetCatCanteenExemptValueResp struct {
	InvestFlag           bool     `protobuf:"varint,1,opt,name=invest_flag,json=investFlag,proto3" json:"invest_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCatCanteenExemptValueResp) Reset()         { *m = GetCatCanteenExemptValueResp{} }
func (m *GetCatCanteenExemptValueResp) String() string { return proto.CompactTextString(m) }
func (*GetCatCanteenExemptValueResp) ProtoMessage()    {}
func (*GetCatCanteenExemptValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{76}
}
func (m *GetCatCanteenExemptValueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatCanteenExemptValueResp.Unmarshal(m, b)
}
func (m *GetCatCanteenExemptValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatCanteenExemptValueResp.Marshal(b, m, deterministic)
}
func (dst *GetCatCanteenExemptValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatCanteenExemptValueResp.Merge(dst, src)
}
func (m *GetCatCanteenExemptValueResp) XXX_Size() int {
	return xxx_messageInfo_GetCatCanteenExemptValueResp.Size(m)
}
func (m *GetCatCanteenExemptValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatCanteenExemptValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatCanteenExemptValueResp proto.InternalMessageInfo

func (m *GetCatCanteenExemptValueResp) GetInvestFlag() bool {
	if m != nil {
		return m.InvestFlag
	}
	return false
}

type SendBreakNewsReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Gift                 *LevelGift `protobuf:"bytes,2,opt,name=gift,proto3" json:"gift,omitempty"`
	TestSecret           string     `protobuf:"bytes,3,opt,name=test_secret,json=testSecret,proto3" json:"test_secret,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SendBreakNewsReq) Reset()         { *m = SendBreakNewsReq{} }
func (m *SendBreakNewsReq) String() string { return proto.CompactTextString(m) }
func (*SendBreakNewsReq) ProtoMessage()    {}
func (*SendBreakNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{77}
}
func (m *SendBreakNewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendBreakNewsReq.Unmarshal(m, b)
}
func (m *SendBreakNewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendBreakNewsReq.Marshal(b, m, deterministic)
}
func (dst *SendBreakNewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendBreakNewsReq.Merge(dst, src)
}
func (m *SendBreakNewsReq) XXX_Size() int {
	return xxx_messageInfo_SendBreakNewsReq.Size(m)
}
func (m *SendBreakNewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendBreakNewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendBreakNewsReq proto.InternalMessageInfo

func (m *SendBreakNewsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendBreakNewsReq) GetGift() *LevelGift {
	if m != nil {
		return m.Gift
	}
	return nil
}

func (m *SendBreakNewsReq) GetTestSecret() string {
	if m != nil {
		return m.TestSecret
	}
	return ""
}

type SendBreakNewsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendBreakNewsResp) Reset()         { *m = SendBreakNewsResp{} }
func (m *SendBreakNewsResp) String() string { return proto.CompactTextString(m) }
func (*SendBreakNewsResp) ProtoMessage()    {}
func (*SendBreakNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{78}
}
func (m *SendBreakNewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendBreakNewsResp.Unmarshal(m, b)
}
func (m *SendBreakNewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendBreakNewsResp.Marshal(b, m, deterministic)
}
func (dst *SendBreakNewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendBreakNewsResp.Merge(dst, src)
}
func (m *SendBreakNewsResp) XXX_Size() int {
	return xxx_messageInfo_SendBreakNewsResp.Size(m)
}
func (m *SendBreakNewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendBreakNewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendBreakNewsResp proto.InternalMessageInfo

type DelEffectiveLevelReq struct {
	LevelId              uint32   `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelEffectiveLevelReq) Reset()         { *m = DelEffectiveLevelReq{} }
func (m *DelEffectiveLevelReq) String() string { return proto.CompactTextString(m) }
func (*DelEffectiveLevelReq) ProtoMessage()    {}
func (*DelEffectiveLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{79}
}
func (m *DelEffectiveLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelEffectiveLevelReq.Unmarshal(m, b)
}
func (m *DelEffectiveLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelEffectiveLevelReq.Marshal(b, m, deterministic)
}
func (dst *DelEffectiveLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelEffectiveLevelReq.Merge(dst, src)
}
func (m *DelEffectiveLevelReq) XXX_Size() int {
	return xxx_messageInfo_DelEffectiveLevelReq.Size(m)
}
func (m *DelEffectiveLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelEffectiveLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelEffectiveLevelReq proto.InternalMessageInfo

func (m *DelEffectiveLevelReq) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

type DelEffectiveLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelEffectiveLevelResp) Reset()         { *m = DelEffectiveLevelResp{} }
func (m *DelEffectiveLevelResp) String() string { return proto.CompactTextString(m) }
func (*DelEffectiveLevelResp) ProtoMessage()    {}
func (*DelEffectiveLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{80}
}
func (m *DelEffectiveLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelEffectiveLevelResp.Unmarshal(m, b)
}
func (m *DelEffectiveLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelEffectiveLevelResp.Marshal(b, m, deterministic)
}
func (dst *DelEffectiveLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelEffectiveLevelResp.Merge(dst, src)
}
func (m *DelEffectiveLevelResp) XXX_Size() int {
	return xxx_messageInfo_DelEffectiveLevelResp.Size(m)
}
func (m *DelEffectiveLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelEffectiveLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelEffectiveLevelResp proto.InternalMessageInfo

type GetUserPropExpireDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PropId               uint32   `protobuf:"varint,2,opt,name=prop_id,json=propId,proto3" json:"prop_id,omitempty"`
	BeginTs              int64    `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                int64    `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageIdx              uint32   `protobuf:"varint,6,opt,name=page_idx,json=pageIdx,proto3" json:"page_idx,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserPropExpireDetailReq) Reset()         { *m = GetUserPropExpireDetailReq{} }
func (m *GetUserPropExpireDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPropExpireDetailReq) ProtoMessage()    {}
func (*GetUserPropExpireDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{81}
}
func (m *GetUserPropExpireDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPropExpireDetailReq.Unmarshal(m, b)
}
func (m *GetUserPropExpireDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPropExpireDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPropExpireDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPropExpireDetailReq.Merge(dst, src)
}
func (m *GetUserPropExpireDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPropExpireDetailReq.Size(m)
}
func (m *GetUserPropExpireDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPropExpireDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPropExpireDetailReq proto.InternalMessageInfo

func (m *GetUserPropExpireDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserPropExpireDetailReq) GetPropId() uint32 {
	if m != nil {
		return m.PropId
	}
	return 0
}

func (m *GetUserPropExpireDetailReq) GetBeginTs() int64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetUserPropExpireDetailReq) GetEndTs() int64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetUserPropExpireDetailReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetUserPropExpireDetailReq) GetPageIdx() uint32 {
	if m != nil {
		return m.PageIdx
	}
	return 0
}

type GetUserPropExpireDetailResp struct {
	UserPropList         []*UserProp `protobuf:"bytes,1,rep,name=user_prop_list,json=userPropList,proto3" json:"user_prop_list,omitempty"`
	TotalCnt             uint32      `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetUserPropExpireDetailResp) Reset()         { *m = GetUserPropExpireDetailResp{} }
func (m *GetUserPropExpireDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPropExpireDetailResp) ProtoMessage()    {}
func (*GetUserPropExpireDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_17a50694a765e81e, []int{82}
}
func (m *GetUserPropExpireDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPropExpireDetailResp.Unmarshal(m, b)
}
func (m *GetUserPropExpireDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPropExpireDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPropExpireDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPropExpireDetailResp.Merge(dst, src)
}
func (m *GetUserPropExpireDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPropExpireDetailResp.Size(m)
}
func (m *GetUserPropExpireDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPropExpireDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPropExpireDetailResp proto.InternalMessageInfo

func (m *GetUserPropExpireDetailResp) GetUserPropList() []*UserProp {
	if m != nil {
		return m.UserPropList
	}
	return nil
}

func (m *GetUserPropExpireDetailResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func init() {
	proto.RegisterType((*CatLightEffect)(nil), "cat_canteen.CatLightEffect")
	proto.RegisterType((*GetCatLightEffectsV2ByWorthReq)(nil), "cat_canteen.GetCatLightEffectsV2ByWorthReq")
	proto.RegisterType((*GetCatLightEffectsV2ByWorthResp)(nil), "cat_canteen.GetCatLightEffectsV2ByWorthResp")
	proto.RegisterType((*AddCatLightEffectsV2Req)(nil), "cat_canteen.AddCatLightEffectsV2Req")
	proto.RegisterType((*AddCatLightEffectsV2Resp)(nil), "cat_canteen.AddCatLightEffectsV2Resp")
	proto.RegisterType((*UpdateCatLightEffectV2Req)(nil), "cat_canteen.UpdateCatLightEffectV2Req")
	proto.RegisterType((*UpdateCatLightEffectV2Resp)(nil), "cat_canteen.UpdateCatLightEffectV2Resp")
	proto.RegisterType((*GetAllCatLightEffectsV2Req)(nil), "cat_canteen.GetAllCatLightEffectsV2Req")
	proto.RegisterType((*GetAllCatLightEffectsV2Resp)(nil), "cat_canteen.GetAllCatLightEffectsV2Resp")
	proto.RegisterType((*DelLightEffectByIdReq)(nil), "cat_canteen.DelLightEffectByIdReq")
	proto.RegisterType((*DelLightEffectByIdResp)(nil), "cat_canteen.DelLightEffectByIdResp")
	proto.RegisterType((*UserProp)(nil), "cat_canteen.UserProp")
	proto.RegisterType((*GetUserPropReq)(nil), "cat_canteen.GetUserPropReq")
	proto.RegisterType((*GetUserPropResp)(nil), "cat_canteen.GetUserPropResp")
	proto.RegisterType((*ReleaseFusingReq)(nil), "cat_canteen.ReleaseFusingReq")
	proto.RegisterType((*ReleaseFusingResp)(nil), "cat_canteen.ReleaseFusingResp")
	proto.RegisterType((*ReportStatsReq)(nil), "cat_canteen.ReportStatsReq")
	proto.RegisterType((*ReportStatsResp)(nil), "cat_canteen.ReportStatsResp")
	proto.RegisterType((*LevelGift)(nil), "cat_canteen.LevelGift")
	proto.RegisterType((*LevelMount)(nil), "cat_canteen.LevelMount")
	proto.RegisterType((*PropInfo)(nil), "cat_canteen.PropInfo")
	proto.RegisterType((*UpgradeAward)(nil), "cat_canteen.UpgradeAward")
	proto.RegisterType((*ResourcesCfg)(nil), "cat_canteen.ResourcesCfg")
	proto.RegisterType((*GameLevelCfg)(nil), "cat_canteen.GameLevelCfg")
	proto.RegisterType((*UserLevelStatus)(nil), "cat_canteen.UserLevelStatus")
	proto.RegisterType((*UserPlayFile)(nil), "cat_canteen.UserPlayFile")
	proto.RegisterType((*GetUserPlayFileReq)(nil), "cat_canteen.GetUserPlayFileReq")
	proto.RegisterType((*GetUserPlayFileResp)(nil), "cat_canteen.GetUserPlayFileResp")
	proto.RegisterType((*GameLevelInfo)(nil), "cat_canteen.GameLevelInfo")
	proto.RegisterType((*GetResourcesCfgReq)(nil), "cat_canteen.GetResourcesCfgReq")
	proto.RegisterType((*GetResourcesCfgResp)(nil), "cat_canteen.GetResourcesCfgResp")
	proto.RegisterType((*GetGameInfoReq)(nil), "cat_canteen.GetGameInfoReq")
	proto.RegisterType((*GetGameInfoResp)(nil), "cat_canteen.GetGameInfoResp")
	proto.RegisterType((*BuyChanceReq)(nil), "cat_canteen.BuyChanceReq")
	proto.RegisterType((*BuyChanceResp)(nil), "cat_canteen.BuyChanceResp")
	proto.RegisterType((*LotteryDrawAward)(nil), "cat_canteen.LotteryDrawAward")
	proto.RegisterType((*LotteryDrawResult)(nil), "cat_canteen.LotteryDrawResult")
	proto.RegisterType((*LotteryDrawReq)(nil), "cat_canteen.LotteryDrawReq")
	proto.RegisterType((*LotteryDrawResp)(nil), "cat_canteen.LotteryDrawResp")
	proto.RegisterType((*GameWinningRecord)(nil), "cat_canteen.GameWinningRecord")
	proto.RegisterType((*GetUserWinningRecordsReq)(nil), "cat_canteen.GetUserWinningRecordsReq")
	proto.RegisterType((*GetUserWinningRecordsResp)(nil), "cat_canteen.GetUserWinningRecordsResp")
	proto.RegisterType((*SimpleGameWinningRecord)(nil), "cat_canteen.SimpleGameWinningRecord")
	proto.RegisterType((*GetRecentWinningRecordsReq)(nil), "cat_canteen.GetRecentWinningRecordsReq")
	proto.RegisterType((*GetRecentWinningRecordsResp)(nil), "cat_canteen.GetRecentWinningRecordsResp")
	proto.RegisterType((*PackageInfo)(nil), "cat_canteen.PackageInfo")
	proto.RegisterType((*LevelInfo)(nil), "cat_canteen.LevelInfo")
	proto.RegisterType((*LevelPrizeInfo)(nil), "cat_canteen.LevelPrizeInfo")
	proto.RegisterType((*LevelInfoWithPrizePool)(nil), "cat_canteen.LevelInfoWithPrizePool")
	proto.RegisterType((*LevelConfVerify)(nil), "cat_canteen.LevelConfVerify")
	proto.RegisterType((*SetLevelConfReq)(nil), "cat_canteen.SetLevelConfReq")
	proto.RegisterType((*SetLevelConfResp)(nil), "cat_canteen.SetLevelConfResp")
	proto.RegisterType((*DelAllPendingLevelConfReq)(nil), "cat_canteen.DelAllPendingLevelConfReq")
	proto.RegisterType((*DelAllPendingLevelConfResp)(nil), "cat_canteen.DelAllPendingLevelConfResp")
	proto.RegisterType((*GetAllLevelConfReq)(nil), "cat_canteen.GetAllLevelConfReq")
	proto.RegisterType((*LevelConfWithConfVersion)(nil), "cat_canteen.LevelConfWithConfVersion")
	proto.RegisterType((*GetAllLevelConfResp)(nil), "cat_canteen.GetAllLevelConfResp")
	proto.RegisterType((*SimulateProfit)(nil), "cat_canteen.SimulateProfit")
	proto.RegisterType((*SimulateWithPrizePoolReq)(nil), "cat_canteen.SimulateWithPrizePoolReq")
	proto.RegisterType((*SimulateWithPrizePoolResp)(nil), "cat_canteen.SimulateWithPrizePoolResp")
	proto.RegisterType((*SetCatCanteenLimitConfReq)(nil), "cat_canteen.SetCatCanteenLimitConfReq")
	proto.RegisterType((*SetCatCanteenLimitConfResp)(nil), "cat_canteen.SetCatCanteenLimitConfResp")
	proto.RegisterType((*GetCatCanteenLimitConfReq)(nil), "cat_canteen.GetCatCanteenLimitConfReq")
	proto.RegisterType((*GetCatCanteenLimitConfResp)(nil), "cat_canteen.GetCatCanteenLimitConfResp")
	proto.RegisterType((*LightEffectConfig)(nil), "cat_canteen.LightEffectConfig")
	proto.RegisterType((*AddCatLightEffectsReq)(nil), "cat_canteen.AddCatLightEffectsReq")
	proto.RegisterType((*AddCatLightEffectsResp)(nil), "cat_canteen.AddCatLightEffectsResp")
	proto.RegisterType((*UpdateCatLightEffectReq)(nil), "cat_canteen.UpdateCatLightEffectReq")
	proto.RegisterType((*UpdateCatLightEffectResp)(nil), "cat_canteen.UpdateCatLightEffectResp")
	proto.RegisterType((*DelCatLightEffectByConfIdReq)(nil), "cat_canteen.DelCatLightEffectByConfIdReq")
	proto.RegisterType((*DelCatLightEffectByConfIdResp)(nil), "cat_canteen.DelCatLightEffectByConfIdResp")
	proto.RegisterType((*GetAllCatLightEffectsReq)(nil), "cat_canteen.GetAllCatLightEffectsReq")
	proto.RegisterType((*GetAllCatLightEffectsResp)(nil), "cat_canteen.GetAllCatLightEffectsResp")
	proto.RegisterType((*BatGetLightEffectByPackIdListReq)(nil), "cat_canteen.BatGetLightEffectByPackIdListReq")
	proto.RegisterType((*BatGetLightEffectByPackIdListResp)(nil), "cat_canteen.BatGetLightEffectByPackIdListResp")
	proto.RegisterType((*GetCatCanteenExemptValueReq)(nil), "cat_canteen.GetCatCanteenExemptValueReq")
	proto.RegisterType((*GetCatCanteenExemptValueResp)(nil), "cat_canteen.GetCatCanteenExemptValueResp")
	proto.RegisterType((*SendBreakNewsReq)(nil), "cat_canteen.SendBreakNewsReq")
	proto.RegisterType((*SendBreakNewsResp)(nil), "cat_canteen.SendBreakNewsResp")
	proto.RegisterType((*DelEffectiveLevelReq)(nil), "cat_canteen.DelEffectiveLevelReq")
	proto.RegisterType((*DelEffectiveLevelResp)(nil), "cat_canteen.DelEffectiveLevelResp")
	proto.RegisterType((*GetUserPropExpireDetailReq)(nil), "cat_canteen.GetUserPropExpireDetailReq")
	proto.RegisterType((*GetUserPropExpireDetailResp)(nil), "cat_canteen.GetUserPropExpireDetailResp")
	proto.RegisterEnum("cat_canteen.LotteryResultType", LotteryResultType_name, LotteryResultType_value)
	proto.RegisterEnum("cat_canteen.ResultType", ResultType_name, ResultType_value)
	proto.RegisterEnum("cat_canteen.LevelType", LevelType_name, LevelType_value)
	proto.RegisterEnum("cat_canteen.UserLevelStatus_Status", UserLevelStatus_Status_name, UserLevelStatus_Status_value)
	proto.RegisterEnum("cat_canteen.GetUserWinningRecordsReq_QueryType", GetUserWinningRecordsReq_QueryType_name, GetUserWinningRecordsReq_QueryType_value)
	proto.RegisterEnum("cat_canteen.GetAllLevelConfReq_ConfType", GetAllLevelConfReq_ConfType_name, GetAllLevelConfReq_ConfType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CatCanteenClient is the client API for CatCanteen service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CatCanteenClient interface {
	// -------------------- 运营后台协议 begin ----------------------------------
	// 关卡、奖池信息
	DelAllPendingLevelConf(ctx context.Context, in *DelAllPendingLevelConfReq, opts ...grpc.CallOption) (*DelAllPendingLevelConfResp, error)
	SetLevelConf(ctx context.Context, in *SetLevelConfReq, opts ...grpc.CallOption) (*SetLevelConfResp, error)
	GetAllLevelConf(ctx context.Context, in *GetAllLevelConfReq, opts ...grpc.CallOption) (*GetAllLevelConfResp, error)
	SimulateWithPrizePool(ctx context.Context, in *SimulateWithPrizePoolReq, opts ...grpc.CallOption) (*SimulateWithPrizePoolResp, error)
	// 防违规配置
	SetCatCanteenLimitConf(ctx context.Context, in *SetCatCanteenLimitConfReq, opts ...grpc.CallOption) (*SetCatCanteenLimitConfResp, error)
	GetCatCanteenLimitConf(ctx context.Context, in *GetCatCanteenLimitConfReq, opts ...grpc.CallOption) (*GetCatCanteenLimitConfResp, error)
	// 光效配置
	AddCatLightEffects(ctx context.Context, in *AddCatLightEffectsReq, opts ...grpc.CallOption) (*AddCatLightEffectsResp, error)
	UpdateCatLightEffect(ctx context.Context, in *UpdateCatLightEffectReq, opts ...grpc.CallOption) (*UpdateCatLightEffectResp, error)
	DelCatLightEffectByConfId(ctx context.Context, in *DelCatLightEffectByConfIdReq, opts ...grpc.CallOption) (*DelCatLightEffectByConfIdResp, error)
	BatGetLightEffectByPackIdList(ctx context.Context, in *BatGetLightEffectByPackIdListReq, opts ...grpc.CallOption) (*BatGetLightEffectByPackIdListResp, error)
	GetAllCatLightEffects(ctx context.Context, in *GetAllCatLightEffectsReq, opts ...grpc.CallOption) (*GetAllCatLightEffectsResp, error)
	// 获取游戏信息
	GetGameInfo(ctx context.Context, in *GetGameInfoReq, opts ...grpc.CallOption) (*GetGameInfoResp, error)
	// 获取用户游戏存档信息
	GetUserPlayFile(ctx context.Context, in *GetUserPlayFileReq, opts ...grpc.CallOption) (*GetUserPlayFileResp, error)
	// 购买抽奖机会
	BuyChance(ctx context.Context, in *BuyChanceReq, opts ...grpc.CallOption) (*BuyChanceResp, error)
	// 进行抽奖
	LotteryDraw(ctx context.Context, in *LotteryDrawReq, opts ...grpc.CallOption) (*LotteryDrawResp, error)
	// 获取用户奖励记录
	GetUserWinningRecords(ctx context.Context, in *GetUserWinningRecordsReq, opts ...grpc.CallOption) (*GetUserWinningRecordsResp, error)
	// 获取平台最近的中奖记录
	GetRecentWinningRecords(ctx context.Context, in *GetRecentWinningRecordsReq, opts ...grpc.CallOption) (*GetRecentWinningRecordsResp, error)
	// 获取豁免条件值
	GetCatCanteenExemptValue(ctx context.Context, in *GetCatCanteenExemptValueReq, opts ...grpc.CallOption) (*GetCatCanteenExemptValueResp, error)
	// 解除熔断
	ReleaseFusing(ctx context.Context, in *ReleaseFusingReq, opts ...grpc.CallOption) (*ReleaseFusingResp, error)
	// 全服接口
	SendBreakNews(ctx context.Context, in *SendBreakNewsReq, opts ...grpc.CallOption) (*SendBreakNewsResp, error)
	// 删除生效中的指定关卡
	DelEffectiveLevel(ctx context.Context, in *DelEffectiveLevelReq, opts ...grpc.CallOption) (*DelEffectiveLevelResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 背包消耗数据对账
	GetBackPackCostTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetBackPackCostOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 尝试回滚对不上的背包消耗订单
	RollbackBackPackCostOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// T+1 对账
	GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error)
	// 小时数据播报
	ReportHourStats(ctx context.Context, in *ReportStatsReq, opts ...grpc.CallOption) (*ReportStatsResp, error)
	// 日数据播报
	ReportDayStats(ctx context.Context, in *ReportStatsReq, opts ...grpc.CallOption) (*ReportStatsResp, error)
	// 中奖光效管理
	AddCatLightEffectsV2(ctx context.Context, in *AddCatLightEffectsV2Req, opts ...grpc.CallOption) (*AddCatLightEffectsV2Resp, error)
	UpdateCatLightEffectV2(ctx context.Context, in *UpdateCatLightEffectV2Req, opts ...grpc.CallOption) (*UpdateCatLightEffectV2Resp, error)
	GetAllCatLightEffectsV2(ctx context.Context, in *GetAllCatLightEffectsV2Req, opts ...grpc.CallOption) (*GetAllCatLightEffectsV2Resp, error)
	DelLightEffectById(ctx context.Context, in *DelLightEffectByIdReq, opts ...grpc.CallOption) (*DelLightEffectByIdResp, error)
	// 用户道具
	GetUserProp(ctx context.Context, in *GetUserPropReq, opts ...grpc.CallOption) (*GetUserPropResp, error)
	// 获取资源配置
	GetResourcesCfg(ctx context.Context, in *GetResourcesCfgReq, opts ...grpc.CallOption) (*GetResourcesCfgResp, error)
	// 根据奖励价值获取中奖光效
	GetCatLightEffectsV2ByWorth(ctx context.Context, in *GetCatLightEffectsV2ByWorthReq, opts ...grpc.CallOption) (*GetCatLightEffectsV2ByWorthResp, error)
	// 获取用户剩余道具明细(包括已过期)
	GetUserPropExpireDetail(ctx context.Context, in *GetUserPropExpireDetailReq, opts ...grpc.CallOption) (*GetUserPropExpireDetailResp, error)
}

type catCanteenClient struct {
	cc *grpc.ClientConn
}

func NewCatCanteenClient(cc *grpc.ClientConn) CatCanteenClient {
	return &catCanteenClient{cc}
}

func (c *catCanteenClient) DelAllPendingLevelConf(ctx context.Context, in *DelAllPendingLevelConfReq, opts ...grpc.CallOption) (*DelAllPendingLevelConfResp, error) {
	out := new(DelAllPendingLevelConfResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/DelAllPendingLevelConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) SetLevelConf(ctx context.Context, in *SetLevelConfReq, opts ...grpc.CallOption) (*SetLevelConfResp, error) {
	out := new(SetLevelConfResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/SetLevelConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetAllLevelConf(ctx context.Context, in *GetAllLevelConfReq, opts ...grpc.CallOption) (*GetAllLevelConfResp, error) {
	out := new(GetAllLevelConfResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetAllLevelConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) SimulateWithPrizePool(ctx context.Context, in *SimulateWithPrizePoolReq, opts ...grpc.CallOption) (*SimulateWithPrizePoolResp, error) {
	out := new(SimulateWithPrizePoolResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/SimulateWithPrizePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) SetCatCanteenLimitConf(ctx context.Context, in *SetCatCanteenLimitConfReq, opts ...grpc.CallOption) (*SetCatCanteenLimitConfResp, error) {
	out := new(SetCatCanteenLimitConfResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/SetCatCanteenLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetCatCanteenLimitConf(ctx context.Context, in *GetCatCanteenLimitConfReq, opts ...grpc.CallOption) (*GetCatCanteenLimitConfResp, error) {
	out := new(GetCatCanteenLimitConfResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetCatCanteenLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) AddCatLightEffects(ctx context.Context, in *AddCatLightEffectsReq, opts ...grpc.CallOption) (*AddCatLightEffectsResp, error) {
	out := new(AddCatLightEffectsResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/AddCatLightEffects", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) UpdateCatLightEffect(ctx context.Context, in *UpdateCatLightEffectReq, opts ...grpc.CallOption) (*UpdateCatLightEffectResp, error) {
	out := new(UpdateCatLightEffectResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/UpdateCatLightEffect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) DelCatLightEffectByConfId(ctx context.Context, in *DelCatLightEffectByConfIdReq, opts ...grpc.CallOption) (*DelCatLightEffectByConfIdResp, error) {
	out := new(DelCatLightEffectByConfIdResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/DelCatLightEffectByConfId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) BatGetLightEffectByPackIdList(ctx context.Context, in *BatGetLightEffectByPackIdListReq, opts ...grpc.CallOption) (*BatGetLightEffectByPackIdListResp, error) {
	out := new(BatGetLightEffectByPackIdListResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/BatGetLightEffectByPackIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetAllCatLightEffects(ctx context.Context, in *GetAllCatLightEffectsReq, opts ...grpc.CallOption) (*GetAllCatLightEffectsResp, error) {
	out := new(GetAllCatLightEffectsResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetAllCatLightEffects", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetGameInfo(ctx context.Context, in *GetGameInfoReq, opts ...grpc.CallOption) (*GetGameInfoResp, error) {
	out := new(GetGameInfoResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetUserPlayFile(ctx context.Context, in *GetUserPlayFileReq, opts ...grpc.CallOption) (*GetUserPlayFileResp, error) {
	out := new(GetUserPlayFileResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetUserPlayFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) BuyChance(ctx context.Context, in *BuyChanceReq, opts ...grpc.CallOption) (*BuyChanceResp, error) {
	out := new(BuyChanceResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/BuyChance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) LotteryDraw(ctx context.Context, in *LotteryDrawReq, opts ...grpc.CallOption) (*LotteryDrawResp, error) {
	out := new(LotteryDrawResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/LotteryDraw", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetUserWinningRecords(ctx context.Context, in *GetUserWinningRecordsReq, opts ...grpc.CallOption) (*GetUserWinningRecordsResp, error) {
	out := new(GetUserWinningRecordsResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetUserWinningRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetRecentWinningRecords(ctx context.Context, in *GetRecentWinningRecordsReq, opts ...grpc.CallOption) (*GetRecentWinningRecordsResp, error) {
	out := new(GetRecentWinningRecordsResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetRecentWinningRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetCatCanteenExemptValue(ctx context.Context, in *GetCatCanteenExemptValueReq, opts ...grpc.CallOption) (*GetCatCanteenExemptValueResp, error) {
	out := new(GetCatCanteenExemptValueResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetCatCanteenExemptValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) ReleaseFusing(ctx context.Context, in *ReleaseFusingReq, opts ...grpc.CallOption) (*ReleaseFusingResp, error) {
	out := new(ReleaseFusingResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/ReleaseFusing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) SendBreakNews(ctx context.Context, in *SendBreakNewsReq, opts ...grpc.CallOption) (*SendBreakNewsResp, error) {
	out := new(SendBreakNewsResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/SendBreakNews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) DelEffectiveLevel(ctx context.Context, in *DelEffectiveLevelReq, opts ...grpc.CallOption) (*DelEffectiveLevelResp, error) {
	out := new(DelEffectiveLevelResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/DelEffectiveLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetAwardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetAwardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetConsumeTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetConsumeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetBackPackCostTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetBackPackCostTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetBackPackCostOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetBackPackCostOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) RollbackBackPackCostOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/RollbackBackPackCostOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error) {
	out := new(reconcile_v2.GenFinancialFileResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GenFinancialFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) ReportHourStats(ctx context.Context, in *ReportStatsReq, opts ...grpc.CallOption) (*ReportStatsResp, error) {
	out := new(ReportStatsResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/ReportHourStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) ReportDayStats(ctx context.Context, in *ReportStatsReq, opts ...grpc.CallOption) (*ReportStatsResp, error) {
	out := new(ReportStatsResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/ReportDayStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) AddCatLightEffectsV2(ctx context.Context, in *AddCatLightEffectsV2Req, opts ...grpc.CallOption) (*AddCatLightEffectsV2Resp, error) {
	out := new(AddCatLightEffectsV2Resp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/AddCatLightEffectsV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) UpdateCatLightEffectV2(ctx context.Context, in *UpdateCatLightEffectV2Req, opts ...grpc.CallOption) (*UpdateCatLightEffectV2Resp, error) {
	out := new(UpdateCatLightEffectV2Resp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/UpdateCatLightEffectV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetAllCatLightEffectsV2(ctx context.Context, in *GetAllCatLightEffectsV2Req, opts ...grpc.CallOption) (*GetAllCatLightEffectsV2Resp, error) {
	out := new(GetAllCatLightEffectsV2Resp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetAllCatLightEffectsV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) DelLightEffectById(ctx context.Context, in *DelLightEffectByIdReq, opts ...grpc.CallOption) (*DelLightEffectByIdResp, error) {
	out := new(DelLightEffectByIdResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/DelLightEffectById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetUserProp(ctx context.Context, in *GetUserPropReq, opts ...grpc.CallOption) (*GetUserPropResp, error) {
	out := new(GetUserPropResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetUserProp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetResourcesCfg(ctx context.Context, in *GetResourcesCfgReq, opts ...grpc.CallOption) (*GetResourcesCfgResp, error) {
	out := new(GetResourcesCfgResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetResourcesCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetCatLightEffectsV2ByWorth(ctx context.Context, in *GetCatLightEffectsV2ByWorthReq, opts ...grpc.CallOption) (*GetCatLightEffectsV2ByWorthResp, error) {
	out := new(GetCatLightEffectsV2ByWorthResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetCatLightEffectsV2ByWorth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catCanteenClient) GetUserPropExpireDetail(ctx context.Context, in *GetUserPropExpireDetailReq, opts ...grpc.CallOption) (*GetUserPropExpireDetailResp, error) {
	out := new(GetUserPropExpireDetailResp)
	err := c.cc.Invoke(ctx, "/cat_canteen.CatCanteen/GetUserPropExpireDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CatCanteenServer is the server API for CatCanteen service.
type CatCanteenServer interface {
	// -------------------- 运营后台协议 begin ----------------------------------
	// 关卡、奖池信息
	DelAllPendingLevelConf(context.Context, *DelAllPendingLevelConfReq) (*DelAllPendingLevelConfResp, error)
	SetLevelConf(context.Context, *SetLevelConfReq) (*SetLevelConfResp, error)
	GetAllLevelConf(context.Context, *GetAllLevelConfReq) (*GetAllLevelConfResp, error)
	SimulateWithPrizePool(context.Context, *SimulateWithPrizePoolReq) (*SimulateWithPrizePoolResp, error)
	// 防违规配置
	SetCatCanteenLimitConf(context.Context, *SetCatCanteenLimitConfReq) (*SetCatCanteenLimitConfResp, error)
	GetCatCanteenLimitConf(context.Context, *GetCatCanteenLimitConfReq) (*GetCatCanteenLimitConfResp, error)
	// 光效配置
	AddCatLightEffects(context.Context, *AddCatLightEffectsReq) (*AddCatLightEffectsResp, error)
	UpdateCatLightEffect(context.Context, *UpdateCatLightEffectReq) (*UpdateCatLightEffectResp, error)
	DelCatLightEffectByConfId(context.Context, *DelCatLightEffectByConfIdReq) (*DelCatLightEffectByConfIdResp, error)
	BatGetLightEffectByPackIdList(context.Context, *BatGetLightEffectByPackIdListReq) (*BatGetLightEffectByPackIdListResp, error)
	GetAllCatLightEffects(context.Context, *GetAllCatLightEffectsReq) (*GetAllCatLightEffectsResp, error)
	// 获取游戏信息
	GetGameInfo(context.Context, *GetGameInfoReq) (*GetGameInfoResp, error)
	// 获取用户游戏存档信息
	GetUserPlayFile(context.Context, *GetUserPlayFileReq) (*GetUserPlayFileResp, error)
	// 购买抽奖机会
	BuyChance(context.Context, *BuyChanceReq) (*BuyChanceResp, error)
	// 进行抽奖
	LotteryDraw(context.Context, *LotteryDrawReq) (*LotteryDrawResp, error)
	// 获取用户奖励记录
	GetUserWinningRecords(context.Context, *GetUserWinningRecordsReq) (*GetUserWinningRecordsResp, error)
	// 获取平台最近的中奖记录
	GetRecentWinningRecords(context.Context, *GetRecentWinningRecordsReq) (*GetRecentWinningRecordsResp, error)
	// 获取豁免条件值
	GetCatCanteenExemptValue(context.Context, *GetCatCanteenExemptValueReq) (*GetCatCanteenExemptValueResp, error)
	// 解除熔断
	ReleaseFusing(context.Context, *ReleaseFusingReq) (*ReleaseFusingResp, error)
	// 全服接口
	SendBreakNews(context.Context, *SendBreakNewsReq) (*SendBreakNewsResp, error)
	// 删除生效中的指定关卡
	DelEffectiveLevel(context.Context, *DelEffectiveLevelReq) (*DelEffectiveLevelResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 背包消耗数据对账
	GetBackPackCostTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetBackPackCostOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 尝试回滚对不上的背包消耗订单
	RollbackBackPackCostOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// T+1 对账
	GenFinancialFile(context.Context, *reconcile_v2.GenFinancialFileReq) (*reconcile_v2.GenFinancialFileResp, error)
	// 小时数据播报
	ReportHourStats(context.Context, *ReportStatsReq) (*ReportStatsResp, error)
	// 日数据播报
	ReportDayStats(context.Context, *ReportStatsReq) (*ReportStatsResp, error)
	// 中奖光效管理
	AddCatLightEffectsV2(context.Context, *AddCatLightEffectsV2Req) (*AddCatLightEffectsV2Resp, error)
	UpdateCatLightEffectV2(context.Context, *UpdateCatLightEffectV2Req) (*UpdateCatLightEffectV2Resp, error)
	GetAllCatLightEffectsV2(context.Context, *GetAllCatLightEffectsV2Req) (*GetAllCatLightEffectsV2Resp, error)
	DelLightEffectById(context.Context, *DelLightEffectByIdReq) (*DelLightEffectByIdResp, error)
	// 用户道具
	GetUserProp(context.Context, *GetUserPropReq) (*GetUserPropResp, error)
	// 获取资源配置
	GetResourcesCfg(context.Context, *GetResourcesCfgReq) (*GetResourcesCfgResp, error)
	// 根据奖励价值获取中奖光效
	GetCatLightEffectsV2ByWorth(context.Context, *GetCatLightEffectsV2ByWorthReq) (*GetCatLightEffectsV2ByWorthResp, error)
	// 获取用户剩余道具明细(包括已过期)
	GetUserPropExpireDetail(context.Context, *GetUserPropExpireDetailReq) (*GetUserPropExpireDetailResp, error)
}

func RegisterCatCanteenServer(s *grpc.Server, srv CatCanteenServer) {
	s.RegisterService(&_CatCanteen_serviceDesc, srv)
}

func _CatCanteen_DelAllPendingLevelConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelAllPendingLevelConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).DelAllPendingLevelConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/DelAllPendingLevelConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).DelAllPendingLevelConf(ctx, req.(*DelAllPendingLevelConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_SetLevelConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLevelConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).SetLevelConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/SetLevelConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).SetLevelConf(ctx, req.(*SetLevelConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetAllLevelConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllLevelConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetAllLevelConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetAllLevelConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetAllLevelConf(ctx, req.(*GetAllLevelConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_SimulateWithPrizePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateWithPrizePoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).SimulateWithPrizePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/SimulateWithPrizePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).SimulateWithPrizePool(ctx, req.(*SimulateWithPrizePoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_SetCatCanteenLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCatCanteenLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).SetCatCanteenLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/SetCatCanteenLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).SetCatCanteenLimitConf(ctx, req.(*SetCatCanteenLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetCatCanteenLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCatCanteenLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetCatCanteenLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetCatCanteenLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetCatCanteenLimitConf(ctx, req.(*GetCatCanteenLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_AddCatLightEffects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCatLightEffectsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).AddCatLightEffects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/AddCatLightEffects",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).AddCatLightEffects(ctx, req.(*AddCatLightEffectsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_UpdateCatLightEffect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCatLightEffectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).UpdateCatLightEffect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/UpdateCatLightEffect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).UpdateCatLightEffect(ctx, req.(*UpdateCatLightEffectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_DelCatLightEffectByConfId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCatLightEffectByConfIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).DelCatLightEffectByConfId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/DelCatLightEffectByConfId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).DelCatLightEffectByConfId(ctx, req.(*DelCatLightEffectByConfIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_BatGetLightEffectByPackIdList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetLightEffectByPackIdListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).BatGetLightEffectByPackIdList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/BatGetLightEffectByPackIdList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).BatGetLightEffectByPackIdList(ctx, req.(*BatGetLightEffectByPackIdListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetAllCatLightEffects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllCatLightEffectsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetAllCatLightEffects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetAllCatLightEffects",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetAllCatLightEffects(ctx, req.(*GetAllCatLightEffectsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetGameInfo(ctx, req.(*GetGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetUserPlayFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPlayFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetUserPlayFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetUserPlayFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetUserPlayFile(ctx, req.(*GetUserPlayFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_BuyChance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyChanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).BuyChance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/BuyChance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).BuyChance(ctx, req.(*BuyChanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_LotteryDraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LotteryDrawReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).LotteryDraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/LotteryDraw",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).LotteryDraw(ctx, req.(*LotteryDrawReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetUserWinningRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserWinningRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetUserWinningRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetUserWinningRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetUserWinningRecords(ctx, req.(*GetUserWinningRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetRecentWinningRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecentWinningRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetRecentWinningRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetRecentWinningRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetRecentWinningRecords(ctx, req.(*GetRecentWinningRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetCatCanteenExemptValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCatCanteenExemptValueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetCatCanteenExemptValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetCatCanteenExemptValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetCatCanteenExemptValue(ctx, req.(*GetCatCanteenExemptValueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_ReleaseFusing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseFusingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).ReleaseFusing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/ReleaseFusing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).ReleaseFusing(ctx, req.(*ReleaseFusingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_SendBreakNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendBreakNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).SendBreakNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/SendBreakNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).SendBreakNews(ctx, req.(*SendBreakNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_DelEffectiveLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelEffectiveLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).DelEffectiveLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/DelEffectiveLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).DelEffectiveLevel(ctx, req.(*DelEffectiveLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetAwardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetAwardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetAwardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetAwardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetAwardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetAwardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetAwardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetAwardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetConsumeTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetConsumeTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetConsumeTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetConsumeTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetConsumeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetConsumeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetConsumeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetConsumeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetBackPackCostTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetBackPackCostTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetBackPackCostTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetBackPackCostTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetBackPackCostOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetBackPackCostOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetBackPackCostOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetBackPackCostOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_RollbackBackPackCostOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).RollbackBackPackCostOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/RollbackBackPackCostOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).RollbackBackPackCostOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GenFinancialFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.GenFinancialFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GenFinancialFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GenFinancialFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GenFinancialFile(ctx, req.(*reconcile_v2.GenFinancialFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_ReportHourStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).ReportHourStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/ReportHourStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).ReportHourStats(ctx, req.(*ReportStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_ReportDayStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).ReportDayStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/ReportDayStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).ReportDayStats(ctx, req.(*ReportStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_AddCatLightEffectsV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCatLightEffectsV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).AddCatLightEffectsV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/AddCatLightEffectsV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).AddCatLightEffectsV2(ctx, req.(*AddCatLightEffectsV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_UpdateCatLightEffectV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCatLightEffectV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).UpdateCatLightEffectV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/UpdateCatLightEffectV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).UpdateCatLightEffectV2(ctx, req.(*UpdateCatLightEffectV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetAllCatLightEffectsV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllCatLightEffectsV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetAllCatLightEffectsV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetAllCatLightEffectsV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetAllCatLightEffectsV2(ctx, req.(*GetAllCatLightEffectsV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_DelLightEffectById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelLightEffectByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).DelLightEffectById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/DelLightEffectById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).DelLightEffectById(ctx, req.(*DelLightEffectByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetUserProp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPropReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetUserProp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetUserProp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetUserProp(ctx, req.(*GetUserPropReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetResourcesCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourcesCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetResourcesCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetResourcesCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetResourcesCfg(ctx, req.(*GetResourcesCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetCatLightEffectsV2ByWorth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCatLightEffectsV2ByWorthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetCatLightEffectsV2ByWorth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetCatLightEffectsV2ByWorth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetCatLightEffectsV2ByWorth(ctx, req.(*GetCatLightEffectsV2ByWorthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CatCanteen_GetUserPropExpireDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPropExpireDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatCanteenServer).GetUserPropExpireDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cat_canteen.CatCanteen/GetUserPropExpireDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatCanteenServer).GetUserPropExpireDetail(ctx, req.(*GetUserPropExpireDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _CatCanteen_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cat_canteen.CatCanteen",
	HandlerType: (*CatCanteenServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DelAllPendingLevelConf",
			Handler:    _CatCanteen_DelAllPendingLevelConf_Handler,
		},
		{
			MethodName: "SetLevelConf",
			Handler:    _CatCanteen_SetLevelConf_Handler,
		},
		{
			MethodName: "GetAllLevelConf",
			Handler:    _CatCanteen_GetAllLevelConf_Handler,
		},
		{
			MethodName: "SimulateWithPrizePool",
			Handler:    _CatCanteen_SimulateWithPrizePool_Handler,
		},
		{
			MethodName: "SetCatCanteenLimitConf",
			Handler:    _CatCanteen_SetCatCanteenLimitConf_Handler,
		},
		{
			MethodName: "GetCatCanteenLimitConf",
			Handler:    _CatCanteen_GetCatCanteenLimitConf_Handler,
		},
		{
			MethodName: "AddCatLightEffects",
			Handler:    _CatCanteen_AddCatLightEffects_Handler,
		},
		{
			MethodName: "UpdateCatLightEffect",
			Handler:    _CatCanteen_UpdateCatLightEffect_Handler,
		},
		{
			MethodName: "DelCatLightEffectByConfId",
			Handler:    _CatCanteen_DelCatLightEffectByConfId_Handler,
		},
		{
			MethodName: "BatGetLightEffectByPackIdList",
			Handler:    _CatCanteen_BatGetLightEffectByPackIdList_Handler,
		},
		{
			MethodName: "GetAllCatLightEffects",
			Handler:    _CatCanteen_GetAllCatLightEffects_Handler,
		},
		{
			MethodName: "GetGameInfo",
			Handler:    _CatCanteen_GetGameInfo_Handler,
		},
		{
			MethodName: "GetUserPlayFile",
			Handler:    _CatCanteen_GetUserPlayFile_Handler,
		},
		{
			MethodName: "BuyChance",
			Handler:    _CatCanteen_BuyChance_Handler,
		},
		{
			MethodName: "LotteryDraw",
			Handler:    _CatCanteen_LotteryDraw_Handler,
		},
		{
			MethodName: "GetUserWinningRecords",
			Handler:    _CatCanteen_GetUserWinningRecords_Handler,
		},
		{
			MethodName: "GetRecentWinningRecords",
			Handler:    _CatCanteen_GetRecentWinningRecords_Handler,
		},
		{
			MethodName: "GetCatCanteenExemptValue",
			Handler:    _CatCanteen_GetCatCanteenExemptValue_Handler,
		},
		{
			MethodName: "ReleaseFusing",
			Handler:    _CatCanteen_ReleaseFusing_Handler,
		},
		{
			MethodName: "SendBreakNews",
			Handler:    _CatCanteen_SendBreakNews_Handler,
		},
		{
			MethodName: "DelEffectiveLevel",
			Handler:    _CatCanteen_DelEffectiveLevel_Handler,
		},
		{
			MethodName: "GetAwardTotalCount",
			Handler:    _CatCanteen_GetAwardTotalCount_Handler,
		},
		{
			MethodName: "GetAwardOrderIds",
			Handler:    _CatCanteen_GetAwardOrderIds_Handler,
		},
		{
			MethodName: "GetConsumeTotalCount",
			Handler:    _CatCanteen_GetConsumeTotalCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderIds",
			Handler:    _CatCanteen_GetConsumeOrderIds_Handler,
		},
		{
			MethodName: "GetBackPackCostTotalCount",
			Handler:    _CatCanteen_GetBackPackCostTotalCount_Handler,
		},
		{
			MethodName: "GetBackPackCostOrderIds",
			Handler:    _CatCanteen_GetBackPackCostOrderIds_Handler,
		},
		{
			MethodName: "RollbackBackPackCostOrder",
			Handler:    _CatCanteen_RollbackBackPackCostOrder_Handler,
		},
		{
			MethodName: "GenFinancialFile",
			Handler:    _CatCanteen_GenFinancialFile_Handler,
		},
		{
			MethodName: "ReportHourStats",
			Handler:    _CatCanteen_ReportHourStats_Handler,
		},
		{
			MethodName: "ReportDayStats",
			Handler:    _CatCanteen_ReportDayStats_Handler,
		},
		{
			MethodName: "AddCatLightEffectsV2",
			Handler:    _CatCanteen_AddCatLightEffectsV2_Handler,
		},
		{
			MethodName: "UpdateCatLightEffectV2",
			Handler:    _CatCanteen_UpdateCatLightEffectV2_Handler,
		},
		{
			MethodName: "GetAllCatLightEffectsV2",
			Handler:    _CatCanteen_GetAllCatLightEffectsV2_Handler,
		},
		{
			MethodName: "DelLightEffectById",
			Handler:    _CatCanteen_DelLightEffectById_Handler,
		},
		{
			MethodName: "GetUserProp",
			Handler:    _CatCanteen_GetUserProp_Handler,
		},
		{
			MethodName: "GetResourcesCfg",
			Handler:    _CatCanteen_GetResourcesCfg_Handler,
		},
		{
			MethodName: "GetCatLightEffectsV2ByWorth",
			Handler:    _CatCanteen_GetCatLightEffectsV2ByWorth_Handler,
		},
		{
			MethodName: "GetUserPropExpireDetail",
			Handler:    _CatCanteen_GetUserPropExpireDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cat-canteen/cat-canteen.proto",
}

func init() {
	proto.RegisterFile("cat-canteen/cat-canteen.proto", fileDescriptor_cat_canteen_17a50694a765e81e)
}

var fileDescriptor_cat_canteen_17a50694a765e81e = []byte{
	// 4282 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3b, 0x4d, 0x6f, 0x1b, 0x49,
	0x76, 0x26, 0xa9, 0x0f, 0xf2, 0x89, 0x94, 0xa8, 0x92, 0x2d, 0x51, 0x94, 0x65, 0x69, 0xda, 0x6b,
	0xc7, 0xeb, 0xc4, 0x72, 0x46, 0xb3, 0x9e, 0x9d, 0xdd, 0x99, 0xcc, 0x8c, 0x24, 0x52, 0x1a, 0xee,
	0x72, 0x24, 0x4e, 0x93, 0xb2, 0xd7, 0x8b, 0x0d, 0x3a, 0xed, 0x66, 0x91, 0xee, 0xa8, 0xd9, 0xdd,
	0xee, 0x6e, 0xda, 0xe2, 0x00, 0x09, 0x16, 0xc8, 0x25, 0x48, 0x72, 0xcb, 0x25, 0x09, 0x32, 0x40,
	0x80, 0x5c, 0x72, 0xda, 0x20, 0x97, 0xe4, 0xb0, 0xc9, 0x3f, 0x48, 0x7e, 0x42, 0x2e, 0x01, 0x16,
	0xc8, 0x3d, 0xb7, 0x9c, 0x82, 0x7a, 0x55, 0xdd, 0xec, 0x4f, 0x4a, 0x1e, 0xcf, 0x21, 0x27, 0xb2,
	0xde, 0x7b, 0xf5, 0xea, 0xd5, 0x7b, 0x55, 0xef, 0xbd, 0xaa, 0x57, 0x0d, 0xdb, 0x9a, 0xea, 0x3d,
	0xd2, 0x54, 0xd3, 0xa3, 0xd4, 0x7c, 0x1c, 0xfa, 0xbf, 0x67, 0x3b, 0x96, 0x67, 0x91, 0x25, 0x4d,
	0xf5, 0x14, 0x01, 0xaa, 0xef, 0xd0, 0x4b, 0x8f, 0x9a, 0xae, 0x6e, 0x99, 0x8f, 0x2d, 0xdb, 0xd3,
	0x2d, 0xd3, 0xf5, 0x7f, 0x39, 0x75, 0x7d, 0xc7, 0xa1, 0x9a, 0x65, 0x6a, 0xba, 0x41, 0x1f, 0xbd,
	0xde, 0x7f, 0x1c, 0x6e, 0x70, 0x02, 0xe9, 0x7f, 0x72, 0xb0, 0x7c, 0xa4, 0x7a, 0x6d, 0x7d, 0xf8,
	0xd2, 0x6b, 0x0e, 0x06, 0x54, 0xf3, 0xc8, 0x06, 0x2c, 0x6a, 0x96, 0x39, 0x50, 0xf4, 0x7e, 0x2d,
	0xb7, 0x9b, 0x7b, 0x50, 0x91, 0x17, 0x58, 0xb3, 0xd5, 0x27, 0x37, 0x61, 0xde, 0xa0, 0xaf, 0xa9,
	0x51, 0xcb, 0x23, 0x98, 0x37, 0xc8, 0x36, 0x00, 0xc5, 0x8e, 0xca, 0xd8, 0x31, 0x6a, 0x85, 0xdd,
	0xdc, 0x83, 0x92, 0x5c, 0xe2, 0x90, 0x73, 0x27, 0x8c, 0x1e, 0xf5, 0x9f, 0xd4, 0xe6, 0xc2, 0xe8,
	0x2f, 0xfb, 0x4f, 0x42, 0x68, 0x5b, 0xd7, 0x6a, 0xf3, 0x61, 0x74, 0x47, 0xd7, 0x98, 0x2c, 0x96,
	0xad, 0x8c, 0x5d, 0xea, 0xd4, 0x16, 0x10, 0xb7, 0x60, 0xd9, 0xe7, 0x2e, 0x75, 0xc8, 0x16, 0x94,
	0x2c, 0x5b, 0x71, 0xe8, 0x48, 0x37, 0xfb, 0xb5, 0x45, 0x44, 0x15, 0x2d, 0x5b, 0xc6, 0x36, 0xd9,
	0x81, 0xa5, 0xb1, 0xdd, 0x57, 0x3d, 0xaa, 0x78, 0xfa, 0x88, 0xd6, 0x8a, 0xbb, 0xb9, 0x07, 0x05,
	0x19, 0x38, 0xa8, 0xa7, 0x8f, 0xa8, 0xf4, 0x21, 0xdc, 0x39, 0xa1, 0x5e, 0x74, 0xde, 0xee, 0xd3,
	0xfd, 0xc3, 0xc9, 0x33, 0xcb, 0xf1, 0x5e, 0xca, 0xf4, 0x15, 0x9b, 0xeb, 0x1b, 0xf6, 0x5f, 0xa8,
	0x80, 0x37, 0xa4, 0xbf, 0xcd, 0xc1, 0xce, 0xcc, 0x8e, 0xae, 0x4d, 0x1e, 0x41, 0x41, 0x1b, 0x0c,
	0xb1, 0xdf, 0xd2, 0xfe, 0xd6, 0x5e, 0xc8, 0x5c, 0x7b, 0xd1, 0x7e, 0x32, 0xa3, 0x23, 0x77, 0xa1,
	0x62, 0x5a, 0x9e, 0x3e, 0x98, 0x28, 0x03, 0xcb, 0x19, 0xa9, 0x1e, 0x2a, 0xb7, 0x24, 0x97, 0x39,
	0xf0, 0x18, 0x61, 0xe4, 0x1e, 0x2c, 0x53, 0x53, 0xb3, 0xc6, 0x8e, 0x3a, 0xa4, 0x8a, 0x47, 0x2f,
	0x3d, 0xa1, 0xe7, 0x4a, 0x00, 0xed, 0xd1, 0x4b, 0x4f, 0xfa, 0x09, 0x6c, 0x1c, 0xf4, 0xfb, 0x09,
	0xe9, 0xd8, 0x7c, 0x1e, 0xc3, 0x1c, 0xb3, 0xe2, 0x75, 0xc4, 0x42, 0x42, 0xa9, 0x0e, 0xb5, 0x74,
	0x5e, 0xae, 0x2d, 0xb5, 0x61, 0xf3, 0x1c, 0x95, 0x19, 0x45, 0x7f, 0xcb, 0x91, 0x6e, 0x43, 0x3d,
	0x8b, 0x9b, 0x6b, 0x4b, 0x1f, 0x43, 0xfd, 0x84, 0x7a, 0x07, 0x86, 0x91, 0x3a, 0xad, 0x6d, 0x00,
	0x97, 0xaa, 0x8e, 0xf6, 0x52, 0x79, 0xad, 0x1a, 0xc2, 0x56, 0x25, 0x0e, 0x79, 0xaa, 0x1a, 0xd2,
	0x33, 0xd8, 0xca, 0xec, 0xec, 0xda, 0xe4, 0x23, 0x28, 0xe1, 0x4a, 0x37, 0x74, 0xd7, 0xab, 0xe5,
	0x76, 0x0b, 0x57, 0xc9, 0x5b, 0x64, 0xd4, 0x6d, 0xdd, 0xf5, 0xa4, 0x16, 0xdc, 0x6a, 0x50, 0x23,
	0x84, 0x3b, 0x9c, 0xb4, 0xfa, 0x4c, 0xa0, 0xcc, 0xcd, 0x13, 0x5a, 0xc9, 0x85, 0xf0, 0x4a, 0x96,
	0x6a, 0xb0, 0x9e, 0xc6, 0xca, 0xb5, 0x25, 0x05, 0x8a, 0x8c, 0xa2, 0xe3, 0x58, 0x36, 0xd9, 0x87,
	0x92, 0xed, 0x58, 0xb6, 0xa2, 0x9b, 0x03, 0x4b, 0xa8, 0xf6, 0x56, 0x44, 0x54, 0x46, 0xd5, 0x32,
	0x07, 0x96, 0x5c, 0xb4, 0xc5, 0x3f, 0xb6, 0x0d, 0xe8, 0xa5, 0xad, 0x3b, 0x62, 0x1b, 0xe4, 0xf9,
	0x36, 0xe0, 0x20, 0xdc, 0x06, 0x12, 0x2c, 0x9f, 0x50, 0xcf, 0x1f, 0x83, 0x89, 0x5f, 0x85, 0xc2,
	0x38, 0x10, 0x9d, 0xfd, 0x95, 0x5c, 0x58, 0x89, 0xd0, 0xb8, 0x36, 0xf9, 0x18, 0x96, 0xd9, 0x3c,
	0x14, 0x14, 0x28, 0xa4, 0xbb, 0xa8, 0x40, 0x41, 0x97, 0xf2, 0x58, 0xfc, 0x63, 0x9a, 0x63, 0xeb,
	0x1d, 0xfb, 0xf5, 0xc7, 0x8e, 0xca, 0x3c, 0x95, 0x70, 0x26, 0x65, 0x06, 0x6c, 0x08, 0x98, 0xf4,
	0x3e, 0x54, 0x65, 0x6a, 0x50, 0xd5, 0xa5, 0xc7, 0x63, 0x57, 0x37, 0x87, 0xc2, 0xd4, 0xba, 0xf9,
	0x87, 0xcc, 0x53, 0x38, 0xa3, 0x17, 0xbe, 0xa9, 0x39, 0x44, 0x1e, 0xbd, 0x90, 0xd6, 0x60, 0x35,
	0xd6, 0xc5, 0xb5, 0xa5, 0x47, 0xb0, 0x2c, 0x53, 0xdb, 0x72, 0xbc, 0xae, 0xa7, 0x7a, 0x2e, 0xe3,
	0xb2, 0x05, 0x25, 0x07, 0x21, 0x8a, 0xe7, 0x22, 0x93, 0x82, 0x5c, 0xe4, 0x80, 0x9e, 0x2b, 0xad,
	0xc2, 0x4a, 0x84, 0xdc, 0xb5, 0xa5, 0xbf, 0xcc, 0x41, 0xa9, 0xcd, 0xfc, 0xdc, 0x89, 0x3e, 0x40,
	0xd7, 0x68, 0xab, 0xda, 0x45, 0xc8, 0xba, 0xac, 0xd9, 0xea, 0x33, 0xb6, 0x88, 0x30, 0x55, 0xa1,
	0xe8, 0x92, 0x5c, 0x64, 0x80, 0x53, 0x75, 0x44, 0xc9, 0x26, 0xe0, 0x7f, 0xf4, 0x70, 0xdc, 0xf6,
	0xc8, 0x85, 0xf9, 0xb7, 0x6d, 0x00, 0x8e, 0x72, 0x74, 0x8d, 0xa2, 0x77, 0xac, 0xc8, 0xc8, 0xa9,
	0xc3, 0x00, 0x64, 0x1d, 0x16, 0xd4, 0x91, 0x35, 0x36, 0x3d, 0xf4, 0x8c, 0x15, 0x59, 0xb4, 0xa4,
	0xbf, 0xc9, 0x01, 0xa0, 0x54, 0x5f, 0xb2, 0x26, 0x1b, 0x00, 0xe1, 0xbe, 0x5c, 0x25, 0x79, 0x11,
	0xdb, 0xad, 0x3e, 0x1b, 0x80, 0xa3, 0x42, 0x92, 0x95, 0x10, 0x82, 0xa2, 0x6d, 0x01, 0x6f, 0x84,
	0x64, 0xe3, 0xac, 0x84, 0x70, 0xea, 0x1b, 0xd5, 0xe9, 0x2b, 0x7d, 0x75, 0xe2, 0xfa, 0xc2, 0x21,
	0xa4, 0xa1, 0x4e, 0xdc, 0x4c, 0xe1, 0xfe, 0x2a, 0x07, 0x45, 0x7f, 0x35, 0xa2, 0xc6, 0x70, 0xdd,
	0x4e, 0x35, 0xc6, 0x50, 0x5c, 0x63, 0x88, 0xd0, 0xc4, 0x1a, 0x28, 0x89, 0x95, 0xab, 0x59, 0x66,
	0x80, 0x44, 0xa1, 0x0b, 0x53, 0x24, 0xca, 0x3c, 0x1d, 0x77, 0x2e, 0x3c, 0x2e, 0x79, 0x0f, 0xca,
	0xfe, 0xa2, 0x62, 0x12, 0x0b, 0xa9, 0x96, 0x7c, 0x58, 0x43, 0x9d, 0x48, 0x7f, 0x9f, 0x83, 0xf2,
	0xb9, 0x3d, 0x74, 0xd4, 0x3e, 0x3d, 0x60, 0xf3, 0x20, 0xbf, 0xcb, 0xc4, 0xa3, 0x2e, 0x35, 0x3d,
	0xb1, 0xa9, 0xd6, 0x23, 0x6b, 0x38, 0xb0, 0xbc, 0xec, 0x93, 0x91, 0x47, 0x30, 0xcf, 0x07, 0xcf,
	0x23, 0xfd, 0x46, 0x92, 0x1e, 0x6d, 0x22, 0x73, 0xaa, 0xe8, 0xbe, 0x2d, 0x5c, 0x6b, 0xdf, 0x4a,
	0xfb, 0x50, 0x96, 0xa9, 0x6b, 0x8d, 0x1d, 0x8d, 0xba, 0x47, 0x83, 0x21, 0x6e, 0x4a, 0xc7, 0x10,
	0x96, 0x65, 0x7f, 0x19, 0x84, 0x45, 0x53, 0xae, 0x36, 0xf6, 0x57, 0xfa, 0x8f, 0x39, 0x28, 0x9f,
	0xa8, 0x23, 0x8a, 0x12, 0xb0, 0x4e, 0x9b, 0x50, 0xc4, 0xf8, 0x3c, 0xd5, 0xfc, 0x22, 0xb6, 0xf9,
	0x9a, 0xe0, 0xa8, 0xf0, 0x9a, 0x40, 0x08, 0xea, 0xf7, 0x53, 0xa8, 0x38, 0xfe, 0xf0, 0x0a, 0x0b,
	0x65, 0x5c, 0xec, 0xcd, 0x88, 0xd8, 0x61, 0x01, 0xe5, 0xb2, 0x13, 0x16, 0xf7, 0x09, 0x00, 0x35,
	0x3d, 0x67, 0xa2, 0x0c, 0xf5, 0x01, 0xb7, 0x51, 0xb6, 0x5a, 0x4b, 0x48, 0x89, 0x7b, 0xeb, 0x53,
	0xa8, 0x8c, 0xb9, 0x69, 0x14, 0x5c, 0x63, 0x68, 0xbf, 0xf8, 0xb0, 0x61, 0xe3, 0xc9, 0xe5, 0x71,
	0xd8, 0x94, 0xf7, 0x61, 0x45, 0xb3, 0x5c, 0x4f, 0xd1, 0x5e, 0xaa, 0xa6, 0x46, 0x15, 0x5b, 0xa4,
	0x0c, 0x15, 0xb9, 0xc2, 0xc0, 0x47, 0x08, 0xed, 0x50, 0x87, 0xfc, 0x10, 0x6a, 0x2f, 0xc6, 0x13,
	0x9f, 0x8c, 0xaf, 0x1d, 0x85, 0x67, 0x4d, 0xb5, 0xc5, 0xdd, 0xc2, 0x83, 0x8a, 0x7c, 0xeb, 0xc5,
	0x78, 0xc2, 0xe9, 0x0f, 0x10, 0x7b, 0x86, 0x48, 0xf2, 0x23, 0xd8, 0x0c, 0x0f, 0x10, 0xed, 0x59,
	0xc4, 0x9e, 0xeb, 0xd3, 0xa1, 0x22, 0x5d, 0x7f, 0x08, 0x35, 0x11, 0xe4, 0x35, 0xcb, 0xf4, 0x74,
	0x73, 0x4c, 0x95, 0x81, 0xaa, 0x1b, 0x8a, 0x66, 0x7a, 0xb5, 0x12, 0x0a, 0x79, 0x8b, 0xe3, 0x8f,
	0x04, 0xfa, 0x58, 0xd5, 0x8d, 0x23, 0x13, 0xbd, 0xe5, 0xd8, 0x34, 0x2c, 0xe6, 0x59, 0x10, 0x5f,
	0x03, 0x9e, 0x1d, 0x70, 0xe0, 0x29, 0xc2, 0xc8, 0x1a, 0xcc, 0x8f, 0xd4, 0x4b, 0xc5, 0xac, 0x2d,
	0x21, 0xab, 0xb9, 0x91, 0x7a, 0x79, 0x4a, 0x7e, 0x0f, 0x56, 0xb8, 0x15, 0xa6, 0xcb, 0xaf, 0x3c,
	0x6b, 0xf9, 0x55, 0x90, 0xda, 0x6f, 0x4a, 0xbf, 0xc9, 0xc1, 0x0a, 0xf3, 0xe0, 0x68, 0x2a, 0xe6,
	0x0e, 0xc7, 0x2e, 0xf9, 0x18, 0x16, 0x5c, 0xfc, 0x87, 0x0b, 0x6a, 0x79, 0xff, 0x6e, 0xc2, 0xdf,
	0x87, 0xa8, 0xf7, 0xf8, 0x8f, 0x2c, 0xba, 0x10, 0x02, 0x73, 0xe8, 0x46, 0xb8, 0xbb, 0xc7, 0xff,
	0xd2, 0x2f, 0x73, 0xb0, 0x20, 0x78, 0xaf, 0x03, 0xe9, 0xf6, 0x0e, 0x7a, 0xe7, 0x5d, 0xe5, 0xfc,
	0xb4, 0xdb, 0x69, 0x1e, 0xb5, 0x8e, 0x5b, 0xcd, 0x46, 0xf5, 0x06, 0xb9, 0x05, 0xab, 0x02, 0xde,
	0x3b, 0x53, 0x0e, 0x9b, 0xca, 0x59, 0xa7, 0x79, 0x5a, 0xcd, 0x85, 0xc0, 0xad, 0x53, 0x84, 0xb5,
	0x4e, 0x4f, 0xaa, 0x79, 0xb2, 0x0d, 0x9b, 0x3e, 0x75, 0xf3, 0xcb, 0xce, 0x99, 0x7c, 0x20, 0xb7,
	0xda, 0xcf, 0x95, 0xa3, 0xf6, 0x59, 0xb7, 0xd9, 0xa8, 0x16, 0xc8, 0x2a, 0x54, 0x04, 0x5a, 0x80,
	0xe6, 0xa4, 0xbf, 0xc8, 0x43, 0x19, 0x23, 0x95, 0xa1, 0x4e, 0x8e, 0x75, 0x83, 0xce, 0xda, 0x37,
	0xdf, 0x63, 0x59, 0x98, 0xbf, 0xb0, 0xd1, 0x76, 0x22, 0x76, 0x05, 0x8b, 0x98, 0x99, 0xec, 0x01,
	0x54, 0x1d, 0x96, 0x7a, 0x5a, 0x03, 0xa6, 0xfa, 0xa1, 0x43, 0x5d, 0x17, 0x77, 0x50, 0x5e, 0x5e,
	0x66, 0xf0, 0xb3, 0x41, 0x47, 0x40, 0xc9, 0x2d, 0x58, 0xd0, 0x54, 0x53, 0x31, 0x5e, 0x0b, 0x47,
	0x36, 0xaf, 0xa9, 0x66, 0xfb, 0x35, 0xf9, 0x02, 0x56, 0x31, 0xbc, 0x72, 0x31, 0x84, 0xc6, 0xf9,
	0x66, 0xb8, 0x3d, 0x4b, 0xe3, 0xf2, 0xca, 0x38, 0x66, 0x30, 0x16, 0x78, 0xd0, 0x8d, 0x8e, 0x47,
	0x62, 0x2f, 0xa0, 0x33, 0x3e, 0x1d, 0x8f, 0xd8, 0xd8, 0x38, 0x88, 0x89, 0xc9, 0x73, 0x45, 0x9e,
	0x67, 0xad, 0x53, 0xa9, 0x09, 0xc4, 0x8f, 0xf6, 0x42, 0x21, 0xa9, 0x59, 0x01, 0x73, 0x21, 0x6c,
	0x1b, 0x98, 0x5c, 0x4f, 0x5c, 0x0d, 0x25, 0x01, 0x69, 0xf5, 0xa5, 0x3f, 0x86, 0xb5, 0x04, 0x1b,
	0xd7, 0x26, 0x9f, 0xc1, 0xb2, 0x6d, 0xa8, 0x13, 0x65, 0xa0, 0x1b, 0x34, 0x9c, 0x38, 0x6c, 0x26,
	0x13, 0x07, 0xbf, 0x5b, 0xd9, 0x16, 0xff, 0x30, 0x79, 0xb8, 0x0f, 0x38, 0x47, 0x7f, 0x0b, 0x4e,
	0x4d, 0x50, 0x61, 0x60, 0xbe, 0xf1, 0x8e, 0x4c, 0x4f, 0xfa, 0xd3, 0x1c, 0x54, 0x02, 0x6f, 0x88,
	0x71, 0xe8, 0x43, 0xe0, 0x1e, 0x4e, 0x99, 0xe6, 0xe6, 0xd1, 0x51, 0xc3, 0xce, 0x53, 0xe6, 0x4b,
	0x80, 0x39, 0xb3, 0xcf, 0xfc, 0x5c, 0x87, 0xc9, 0x8d, 0xbb, 0x28, 0x9f, 0xe6, 0x96, 0x22, 0x22,
	0x8f, 0x45, 0x0b, 0x37, 0xd2, 0x7d, 0xd4, 0x68, 0xc4, 0x5d, 0xa6, 0xe6, 0x59, 0xdf, 0xe4, 0x50,
	0x67, 0x51, 0x42, 0xae, 0xb3, 0x40, 0xf0, 0x6c, 0x9d, 0x45, 0xa4, 0x2f, 0xfb, 0xd2, 0xa3, 0xce,
	0x9a, 0xb0, 0xca, 0x56, 0x59, 0xd4, 0xa5, 0xe7, 0xaf, 0x72, 0xe9, 0x2b, 0x9a, 0x6a, 0x86, 0x01,
	0x22, 0x57, 0x64, 0xe3, 0xa0, 0xbb, 0x48, 0x9d, 0xc3, 0xaf, 0xf3, 0x98, 0x2c, 0x4e, 0x89, 0x5c,
	0x3b, 0xcd, 0x64, 0xb9, 0x14, 0x93, 0x91, 0x43, 0x58, 0x11, 0xfb, 0xce, 0x1c, 0x58, 0x7c, 0xa2,
	0x79, 0x9c, 0x68, 0x3d, 0x7d, 0xa2, 0xdc, 0x69, 0x19, 0xfe, 0x5f, 0x9c, 0xea, 0xe7, 0x50, 0x0d,
	0xbb, 0x76, 0x8c, 0x22, 0x85, 0x99, 0xf1, 0x67, 0x79, 0xea, 0xea, 0x31, 0x88, 0xa4, 0x2a, 0x6b,
	0xee, 0x6d, 0x95, 0x45, 0x3e, 0x80, 0xf5, 0x90, 0x20, 0x22, 0x09, 0xef, 0x53, 0xd7, 0x3f, 0xe1,
	0xae, 0x05, 0xc3, 0x36, 0x11, 0xd7, 0xa0, 0xae, 0x26, 0x35, 0xa1, 0x7c, 0xe8, 0x83, 0xd3, 0x77,
	0xdd, 0x5d, 0xa8, 0x44, 0x82, 0x8f, 0xef, 0x7f, 0xb4, 0x50, 0xc4, 0x91, 0x9e, 0x43, 0x25, 0xc4,
	0xc6, 0xb5, 0xc9, 0x1e, 0xac, 0x0d, 0x74, 0x53, 0x35, 0xa2, 0x81, 0x4b, 0xf0, 0x5d, 0x45, 0x54,
	0x38, 0x64, 0x91, 0x1a, 0x2c, 0xbe, 0x50, 0x0d, 0x06, 0x40, 0xfe, 0x73, 0xb2, 0xdf, 0x94, 0xfe,
	0x3b, 0x07, 0xd5, 0xb6, 0xe5, 0x79, 0xd4, 0x99, 0x34, 0x1c, 0xf5, 0x0d, 0x57, 0xd9, 0x67, 0xb0,
	0xe4, 0x50, 0x77, 0x6c, 0x78, 0x8a, 0x37, 0xb1, 0xa9, 0x08, 0x0d, 0x77, 0xa2, 0xfa, 0xe6, 0x7d,
	0x64, 0x24, 0xeb, 0x4d, 0x6c, 0x2a, 0x83, 0x13, 0xfc, 0x0f, 0xe7, 0x60, 0xf9, 0xb7, 0xcc, 0xc1,
	0x0a, 0x6f, 0x9f, 0x83, 0xcd, 0x5d, 0x2f, 0x07, 0xfb, 0xaf, 0x1c, 0xac, 0x86, 0xa6, 0xca, 0x45,
	0x7f, 0xf7, 0xb9, 0x7e, 0xe2, 0xa7, 0xd4, 0xa1, 0x05, 0xbe, 0x9d, 0xd6, 0x3f, 0xd0, 0xaf, 0xc8,
	0xb8, 0x71, 0x7d, 0x4b, 0x50, 0x31, 0xe9, 0xa5, 0xa7, 0x04, 0x01, 0xaa, 0xc0, 0x53, 0x5c, 0x06,
	0x6c, 0x8b, 0x20, 0xf5, 0x10, 0x56, 0x75, 0x53, 0x73, 0x82, 0xd8, 0x83, 0x07, 0x63, 0x1e, 0x5f,
	0x56, 0x18, 0xc2, 0x8f, 0x3e, 0xec, 0x78, 0xec, 0xc1, 0x72, 0x64, 0x8e, 0x6f, 0xef, 0xe9, 0x43,
	0xc9, 0x78, 0x21, 0x92, 0x8c, 0x87, 0xc3, 0xe8, 0x5c, 0x24, 0x8c, 0x4a, 0xff, 0x98, 0x83, 0x95,
	0xa8, 0x6a, 0x6d, 0xb6, 0xb2, 0x1d, 0x3a, 0x52, 0x75, 0x53, 0x2c, 0x52, 0x21, 0x41, 0x99, 0x03,
	0xf9, 0xf2, 0x4c, 0x09, 0x1f, 0xf9, 0xb7, 0x0b, 0x1f, 0x1f, 0xc2, 0x02, 0xb7, 0x85, 0x58, 0x38,
	0x77, 0xb2, 0x34, 0xcf, 0xad, 0x27, 0x0b, 0x6a, 0xe9, 0x57, 0x05, 0x58, 0x65, 0x8e, 0xe7, 0x99,
	0x6e, 0x9a, 0x78, 0xb4, 0xd4, 0x2c, 0xa7, 0x4f, 0x96, 0x21, 0x1f, 0x9c, 0xb7, 0xf2, 0x5c, 0x53,
	0x06, 0x67, 0xe1, 0x6b, 0x8a, 0xa5, 0xd5, 0x1c, 0xd2, 0x4a, 0xec, 0x93, 0xc2, 0x5b, 0xaf, 0x9d,
	0xb7, 0xb0, 0x2c, 0xcb, 0xc1, 0xf9, 0x3a, 0xc3, 0x35, 0x3f, 0x3f, 0x3b, 0x07, 0x47, 0x4a, 0x8c,
	0x92, 0x1f, 0xc1, 0x12, 0xef, 0xc6, 0x4d, 0xba, 0x30, 0x7b, 0x7b, 0xf1, 0x21, 0xf8, 0x11, 0x34,
	0x9a, 0xf4, 0x2f, 0x5e, 0x37, 0xe9, 0xdf, 0x81, 0x25, 0xcd, 0xa1, 0x91, 0x9b, 0xba, 0x8a, 0x0c,
	0x1c, 0xd4, 0xd3, 0x47, 0x94, 0xfc, 0xc0, 0x9f, 0x08, 0xdb, 0x99, 0x98, 0x2b, 0x67, 0x6e, 0x5e,
	0x3e, 0x0f, 0xd6, 0x94, 0xbe, 0xc9, 0x43, 0x4d, 0x24, 0x20, 0x11, 0x9b, 0xb9, 0xe9, 0x6b, 0x3c,
	0xbc, 0x58, 0xf3, 0xd1, 0x9c, 0x6f, 0x0b, 0x4a, 0xd6, 0x60, 0xe0, 0x52, 0xcf, 0xdf, 0x6e, 0x25,
	0xb9, 0xc8, 0x01, 0xe2, 0x42, 0x54, 0x1f, 0xe9, 0xfe, 0x41, 0x94, 0x37, 0xc8, 0x29, 0xc0, 0xab,
	0x31, 0x5b, 0x05, 0x68, 0xe7, 0x79, 0xb4, 0xf3, 0xe3, 0x68, 0x10, 0xcb, 0x10, 0x6d, 0xef, 0x2b,
	0xd6, 0x0f, 0x0d, 0x5f, 0x7a, 0xe5, 0xff, 0x95, 0xbe, 0x82, 0x52, 0x00, 0x27, 0x75, 0x58, 0xff,
	0xea, 0xbc, 0x29, 0x3f, 0x57, 0x7a, 0xcf, 0x3b, 0xcd, 0x58, 0xae, 0x4c, 0x60, 0x39, 0x84, 0x3b,
	0x68, 0xb7, 0xab, 0x39, 0x72, 0x13, 0xaa, 0x21, 0xd8, 0xe1, 0xd9, 0xe9, 0x79, 0xb7, 0x9a, 0x97,
	0xce, 0x61, 0x33, 0x43, 0x06, 0xbc, 0x15, 0x5b, 0x74, 0x78, 0x53, 0xa4, 0x1a, 0x77, 0x12, 0x11,
	0x38, 0xd2, 0x4b, 0xf6, 0xc9, 0xa5, 0x67, 0xb0, 0xd1, 0xd5, 0x47, 0xb6, 0x41, 0x93, 0x9b, 0x25,
	0xa9, 0xf4, 0x87, 0x30, 0x87, 0x6b, 0x65, 0xb6, 0xcf, 0x47, 0x1a, 0xa9, 0x81, 0x97, 0x80, 0x32,
	0xd5, 0xa8, 0xe9, 0x5d, 0xc7, 0xa0, 0x81, 0x61, 0xf2, 0x21, 0xc3, 0x48, 0xbf, 0x8f, 0xb7, 0x81,
	0xe9, 0x5c, 0x5c, 0x9b, 0x7c, 0x1a, 0x9f, 0xf7, 0xf7, 0x22, 0x32, 0x65, 0xcc, 0x6c, 0x3a, 0xfb,
	0xff, 0xcc, 0xc1, 0x52, 0x47, 0xd5, 0x2e, 0xd4, 0x21, 0x0d, 0xae, 0x3e, 0xc4, 0x65, 0x51, 0x21,
	0x72, 0x59, 0xe4, 0x5f, 0xfa, 0xf0, 0x0b, 0xe6, 0xd0, 0xa5, 0x0f, 0x5e, 0x22, 0x47, 0xef, 0x92,
	0xe6, 0x67, 0xdc, 0x25, 0x2d, 0x44, 0xef, 0x92, 0x76, 0x60, 0x09, 0x51, 0xc2, 0x1f, 0xf3, 0xbc,
	0x1e, 0x47, 0x12, 0x81, 0x7d, 0x03, 0x16, 0xf1, 0xe4, 0xa2, 0xf7, 0xc5, 0x46, 0x5b, 0x60, 0x4d,
	0x7e, 0xb0, 0xe1, 0x92, 0x7a, 0x74, 0xc4, 0x57, 0x6d, 0x49, 0x5c, 0xca, 0x31, 0x81, 0x3d, 0x3a,
	0xc2, 0x75, 0xf8, 0xcf, 0x79, 0x71, 0x15, 0x86, 0xb3, 0xfb, 0xf6, 0xf7, 0x0b, 0xdb, 0x00, 0x9e,
	0xae, 0x5d, 0x50, 0x0f, 0xcf, 0x25, 0x5c, 0x35, 0x25, 0x0e, 0x61, 0x27, 0x93, 0x8f, 0x60, 0x09,
	0xc5, 0x75, 0xc3, 0xf1, 0x3a, 0xdb, 0x07, 0x71, 0x5a, 0x14, 0xe9, 0x21, 0xac, 0x8a, 0x9e, 0xa1,
	0x6b, 0x2b, 0x7e, 0x0b, 0xb4, 0xc2, 0x11, 0x07, 0xc1, 0xe5, 0x55, 0x70, 0x66, 0x5e, 0x08, 0x9d,
	0x99, 0xef, 0xc0, 0x12, 0x02, 0x95, 0x81, 0x7e, 0x49, 0x79, 0x59, 0xa1, 0x28, 0x97, 0x18, 0xea,
	0x98, 0x01, 0xc8, 0x13, 0x61, 0x19, 0x14, 0xac, 0x88, 0x82, 0xd5, 0xa2, 0xbe, 0x68, 0x6a, 0x7e,
	0x6e, 0x33, 0xcc, 0x25, 0xfe, 0x3a, 0x07, 0xcb, 0x28, 0x72, 0xc7, 0xd1, 0xbf, 0xa6, 0xbe, 0xa3,
	0x4d, 0x26, 0x12, 0x1b, 0xf1, 0x0c, 0x33, 0x2d, 0x0a, 0xac, 0xc3, 0xc2, 0x1b, 0xaa, 0x0f, 0x5f,
	0xfa, 0x6b, 0x5b, 0xb4, 0xa2, 0xb2, 0x15, 0xae, 0x2d, 0xdb, 0x9f, 0xe7, 0x60, 0x3d, 0x30, 0xea,
	0x33, 0xdd, 0x7b, 0x89, 0x32, 0x76, 0x2c, 0x0b, 0x63, 0xc8, 0x34, 0x23, 0xcf, 0xbe, 0x1e, 0xe3,
	0xbe, 0x37, 0x48, 0xc4, 0xc9, 0x8f, 0x01, 0x6c, 0xc6, 0x23, 0x1c, 0xa1, 0xb7, 0x92, 0xdd, 0x02,
	0x5d, 0xc8, 0x25, 0x24, 0xc7, 0x6b, 0x75, 0x0f, 0x56, 0xf8, 0x29, 0xc6, 0x32, 0x07, 0x4f, 0xa9,
	0xa3, 0x0f, 0x26, 0xe4, 0x73, 0x28, 0x4d, 0x4f, 0x04, 0x7c, 0x5f, 0xde, 0x4d, 0x17, 0x22, 0x22,
	0xbd, 0x5c, 0xd4, 0xfd, 0x53, 0xc1, 0x0e, 0xb3, 0xaa, 0xe3, 0xbe, 0x54, 0x0d, 0xe5, 0x82, 0x4e,
	0xc4, 0x7a, 0x04, 0x01, 0xfa, 0x29, 0x9d, 0x48, 0x7f, 0x96, 0x83, 0x95, 0x2e, 0xf5, 0x82, 0x91,
	0x99, 0x4f, 0xf9, 0x4e, 0x86, 0x15, 0x95, 0xad, 0xe0, 0xf6, 0xbd, 0x22, 0x8b, 0x62, 0x17, 0x86,
	0xb6, 0x9b, 0x30, 0xef, 0x59, 0x17, 0xd4, 0x14, 0x61, 0x85, 0x37, 0x24, 0x02, 0xd5, 0xa8, 0x2c,
	0xae, 0x2d, 0x7d, 0x02, 0x9b, 0x0d, 0x6a, 0x1c, 0x18, 0x46, 0x87, 0x9a, 0x7d, 0xdd, 0x1c, 0x46,
	0x24, 0x8d, 0x8d, 0x93, 0x8b, 0x8f, 0x23, 0xdd, 0x86, 0x7a, 0x56, 0x6f, 0xd7, 0x96, 0x7e, 0x95,
	0xc3, 0x03, 0xea, 0x81, 0x61, 0x44, 0xb8, 0x36, 0x45, 0x69, 0x24, 0xb4, 0x3c, 0x1f, 0xc4, 0x63,
	0x58, 0xac, 0xcf, 0x1e, 0xfb, 0xc5, 0xf5, 0x8a, 0x75, 0x12, 0x11, 0xbb, 0x8a, 0x3e, 0x94, 0x6c,
	0xc0, 0x9a, 0xff, 0x5f, 0x39, 0x3f, 0x6d, 0xfe, 0xac, 0xd3, 0x3c, 0xea, 0x61, 0xdc, 0x5a, 0x07,
	0x12, 0x20, 0x5a, 0xa7, 0x4a, 0xf3, 0xf8, 0xb8, 0x79, 0xd4, 0xe3, 0xb1, 0x2b, 0x80, 0x77, 0x9a,
	0xa7, 0x0d, 0xbc, 0xe3, 0x91, 0xfe, 0x08, 0x6a, 0xc1, 0xa8, 0x4c, 0xf7, 0x62, 0xad, 0xb8, 0xba,
	0x65, 0x5e, 0xa9, 0x8b, 0xa8, 0x59, 0xf3, 0xdf, 0xc2, 0xac, 0x52, 0x07, 0x8f, 0xe9, 0xd1, 0xa9,
	0xbb, 0x36, 0xf9, 0x51, 0xa4, 0xea, 0x75, 0x2f, 0xc9, 0x33, 0x45, 0x5c, 0x51, 0xff, 0xfa, 0x75,
	0x0e, 0x96, 0xbb, 0xfa, 0x68, 0x6c, 0xa8, 0x1e, 0xed, 0x38, 0xd6, 0x40, 0xf7, 0x66, 0x39, 0xd7,
	0xbb, 0x50, 0xa1, 0x97, 0x36, 0x16, 0x4c, 0x91, 0x16, 0x17, 0x56, 0x5e, 0x2e, 0x73, 0xa0, 0xe8,
	0xbf, 0x07, 0x6b, 0x11, 0x22, 0xc5, 0x51, 0xfb, 0xba, 0x25, 0xae, 0xa1, 0x56, 0xc3, 0xa4, 0x32,
	0x43, 0x90, 0x7b, 0xb0, 0xac, 0xbe, 0xa6, 0x58, 0x5d, 0x14, 0x5c, 0xe7, 0x90, 0xb4, 0x22, 0xa0,
	0x82, 0x2d, 0x81, 0x39, 0x5b, 0x75, 0xb9, 0x4f, 0x2d, 0xca, 0xf8, 0x5f, 0xfa, 0x05, 0xd4, 0x7c,
	0xe1, 0xa3, 0x2a, 0xfb, 0x2e, 0x36, 0x91, 0x64, 0xc1, 0x66, 0x06, 0x77, 0xd7, 0x26, 0x9f, 0xc0,
	0x92, 0x98, 0x5e, 0x66, 0x01, 0x2f, 0xaa, 0x57, 0x19, 0x38, 0x3d, 0xee, 0xcf, 0x60, 0xfb, 0xe5,
	0xc3, 0xdb, 0xef, 0x5f, 0x73, 0xb0, 0xd9, 0xc5, 0x0a, 0xef, 0x11, 0x67, 0xd1, 0x66, 0xa9, 0x83,
	0xbf, 0x2b, 0x3e, 0x80, 0xf5, 0xbe, 0xaa, 0x1b, 0x13, 0x25, 0x74, 0xba, 0xe7, 0x89, 0x06, 0xb7,
	0xd2, 0x1a, 0x62, 0x83, 0xe3, 0x37, 0xf6, 0x9d, 0x76, 0x1a, 0xbb, 0x34, 0xda, 0x29, 0x1f, 0xea,
	0x74, 0xee, 0xd2, 0x70, 0xa7, 0x27, 0xb0, 0xe1, 0xea, 0xe6, 0xd0, 0xa0, 0xc9, 0xa1, 0x78, 0xc4,
	0xbc, 0xc9, 0xd1, 0xd1, 0xb1, 0xd8, 0x5e, 0xcf, 0x92, 0xde, 0xb5, 0xa5, 0x2d, 0x4c, 0xfb, 0xd2,
	0xe7, 0x26, 0xfd, 0x5b, 0x0e, 0x93, 0xac, 0x8c, 0xbe, 0xff, 0xff, 0xa7, 0xfe, 0x1b, 0x76, 0x62,
	0x9f, 0x56, 0x51, 0x99, 0xe0, 0xfa, 0x70, 0x66, 0x3d, 0xd6, 0xcf, 0xce, 0xf2, 0xf1, 0x52, 0x9e,
	0xf0, 0x21, 0x41, 0xe2, 0x56, 0xe4, 0x80, 0x78, 0x9d, 0x6f, 0x2e, 0x96, 0x9b, 0x05, 0xf5, 0x32,
	0xac, 0xd0, 0x8b, 0xb7, 0x0c, 0x08, 0xe9, 0xd1, 0x4b, 0x5c, 0x70, 0x23, 0x74, 0x4b, 0x3c, 0xe5,
	0xe0, 0x0d, 0x4c, 0x06, 0x31, 0x5c, 0x22, 0x4b, 0xfe, 0x92, 0x81, 0x47, 0x44, 0xe4, 0x29, 0x6a,
	0x3f, 0xc5, 0xa0, 0xf6, 0x23, 0xf5, 0xe0, 0x56, 0xb2, 0x30, 0xcf, 0x16, 0xe7, 0xc7, 0xc9, 0x6a,
	0x76, 0xec, 0x78, 0x19, 0xd7, 0x4e, 0xa8, 0xa0, 0xfd, 0x63, 0x58, 0x4f, 0xe3, 0xea, 0xda, 0x64,
	0x17, 0xca, 0x42, 0x51, 0x53, 0xce, 0x22, 0xaf, 0x6c, 0xe1, 0xb5, 0x84, 0x34, 0x80, 0x8d, 0xb4,
	0x02, 0xfe, 0xcc, 0x72, 0xf8, 0xbe, 0xf0, 0x97, 0xf9, 0xb4, 0x83, 0x78, 0x42, 0xce, 0xe0, 0x49,
	0x42, 0xfa, 0x38, 0xae, 0x2d, 0x7d, 0x0e, 0xb7, 0x1b, 0x34, 0x56, 0xe6, 0x3f, 0x9c, 0x1c, 0xe1,
	0x60, 0x4c, 0x90, 0x5d, 0x28, 0x0b, 0x41, 0x22, 0xb3, 0xe0, 0xd2, 0xe0, 0x2c, 0x76, 0x60, 0x7b,
	0x06, 0x07, 0xd7, 0x66, 0xc3, 0xa7, 0x3e, 0x26, 0x60, 0x9b, 0xe7, 0x67, 0xb8, 0xb3, 0xd2, 0x70,
	0x58, 0x2f, 0x7f, 0x07, 0xc3, 0x34, 0x60, 0xf7, 0x50, 0xf5, 0x4e, 0x68, 0x54, 0xac, 0x4e, 0xa0,
	0x7d, 0x31, 0xb9, 0x2b, 0x4c, 0xf4, 0x07, 0xf0, 0xde, 0x15, 0x5c, 0xde, 0x55, 0xce, 0xc7, 0x78,
	0xb8, 0x9a, 0x7a, 0x8f, 0xe6, 0x25, 0x1d, 0xd9, 0xde, 0x53, 0xd5, 0x18, 0xa7, 0x5f, 0x66, 0x4a,
	0x9f, 0xc1, 0xed, 0xec, 0x0e, 0xae, 0xcd, 0x62, 0xb9, 0x6e, 0xbe, 0xa6, 0xae, 0xa7, 0x0c, 0x0c,
	0x95, 0xdf, 0xd9, 0x17, 0x65, 0xe0, 0xa0, 0x63, 0x43, 0x1d, 0x4a, 0xaf, 0x58, 0xa6, 0x64, 0xf6,
	0x0f, 0x1d, 0xaa, 0x5e, 0x9c, 0xd2, 0x37, 0x19, 0x47, 0xc1, 0xb7, 0x38, 0x66, 0xb2, 0x21, 0x3d,
	0x36, 0xa0, 0x4b, 0x35, 0x87, 0xfa, 0x6f, 0x6c, 0x80, 0x81, 0xba, 0x08, 0x91, 0xd6, 0x60, 0x35,
	0x36, 0xa4, 0x6b, 0x4b, 0xef, 0xc3, 0xcd, 0x06, 0x35, 0xb8, 0x5a, 0xf4, 0xd7, 0xfc, 0x76, 0x9a,
	0xc9, 0x92, 0x1d, 0xc4, 0xa5, 0x0d, 0x7c, 0x3e, 0x12, 0xef, 0xe2, 0xda, 0xd2, 0x3f, 0x71, 0x27,
	0xec, 0xbf, 0x9d, 0xf0, 0x6f, 0x87, 0x3d, 0x55, 0x37, 0xd2, 0xa7, 0x17, 0xaa, 0xaf, 0xe7, 0x23,
	0xf5, 0xf5, 0x4d, 0x28, 0xbe, 0xa0, 0x43, 0xdd, 0x54, 0x3c, 0x5e, 0x7e, 0x2a, 0xc8, 0x8b, 0xd8,
	0xee, 0x61, 0xdd, 0x89, 0x9a, 0x7d, 0x86, 0x98, 0x43, 0xc4, 0x3c, 0x35, 0xfb, 0x3d, 0x97, 0xfb,
	0xb6, 0x21, 0x55, 0x5c, 0xfd, 0x6b, 0x2a, 0x8e, 0x4d, 0x45, 0x06, 0xe8, 0xea, 0x5f, 0x8b, 0x73,
	0xe7, 0x90, 0x2a, 0x7a, 0xff, 0x32, 0x28, 0x25, 0xb1, 0xd3, 0x44, 0xff, 0x52, 0x7a, 0x83, 0x96,
	0x4f, 0x17, 0xf9, 0x5d, 0x5f, 0x8b, 0x6c, 0x41, 0xc9, 0xb3, 0x3c, 0xd5, 0x08, 0x95, 0x7a, 0x8a,
	0x08, 0x38, 0x32, 0xbd, 0x87, 0x7f, 0x37, 0xbd, 0xa3, 0x9d, 0x1e, 0x96, 0xc8, 0x1d, 0xa8, 0xb7,
	0xcf, 0x7a, 0xbd, 0xa6, 0xfc, 0x5c, 0x91, 0x9b, 0xdd, 0xf3, 0x76, 0x2f, 0x76, 0x4b, 0xb2, 0x03,
	0x5b, 0x71, 0x7c, 0xe7, 0x44, 0x3e, 0x68, 0x34, 0x95, 0xe3, 0x83, 0x56, 0xbb, 0x9a, 0x23, 0x12,
	0xdc, 0xc9, 0x20, 0xe8, 0x9e, 0x1f, 0x1d, 0x35, 0xbb, 0xdd, 0x6a, 0x9e, 0xdc, 0x07, 0x29, 0x46,
	0x73, 0x24, 0x9f, 0x75, 0xbb, 0x4a, 0xbb, 0xf9, 0xb4, 0xd9, 0xf6, 0xe9, 0xab, 0x85, 0x87, 0xdf,
	0xe4, 0x00, 0xe4, 0xf0, 0xe1, 0x8d, 0xa4, 0xca, 0xb4, 0x01, 0x6b, 0xe9, 0xb2, 0xd4, 0x61, 0x3d,
	0x53, 0x86, 0x3b, 0x50, 0x9f, 0x35, 0x36, 0x9b, 0x47, 0x0a, 0x53, 0xe5, 0x59, 0xab, 0xf7, 0x85,
	0xd2, 0x91, 0x5b, 0x3f, 0x6f, 0x56, 0xe7, 0x1e, 0xfe, 0x83, 0xff, 0xbc, 0x05, 0xc5, 0xdb, 0x84,
	0x5b, 0x41, 0x23, 0x26, 0x61, 0x0d, 0x6e, 0x4e, 0x51, 0xf8, 0x4f, 0x69, 0x35, 0x94, 0xf7, 0xab,
	0xb9, 0x0c, 0xcc, 0x7e, 0x35, 0x9f, 0x81, 0xf9, 0xa0, 0x5a, 0xc8, 0xc0, 0xfc, 0xa0, 0x3a, 0x97,
	0x81, 0x79, 0x52, 0x9d, 0xdf, 0xff, 0x93, 0x6d, 0x80, 0xa9, 0xb7, 0x20, 0x17, 0xf8, 0x6c, 0x2a,
	0xe5, 0x54, 0x43, 0xee, 0x47, 0x16, 0x56, 0xe6, 0xc1, 0xa9, 0xfe, 0x5b, 0xd7, 0xa2, 0x73, 0x6d,
	0xe9, 0x06, 0xf9, 0x12, 0xca, 0xe1, 0x43, 0x19, 0x89, 0xd6, 0x61, 0x63, 0x67, 0xc7, 0xfa, 0xf6,
	0x0c, 0x2c, 0xb2, 0x7b, 0x8a, 0x65, 0xb2, 0xf0, 0x19, 0x82, 0xec, 0x5c, 0x71, 0xb8, 0xaa, 0xef,
	0xce, 0x26, 0x40, 0xbe, 0x2f, 0xe1, 0x56, 0x6a, 0xb6, 0x4c, 0xee, 0xa5, 0x26, 0xc5, 0xf1, 0x7c,
	0xbd, 0x7e, 0xff, 0x3a, 0x64, 0x38, 0xd2, 0x05, 0xac, 0xa7, 0xe7, 0x99, 0x31, 0xed, 0x67, 0xa6,
	0xd2, 0x31, 0xed, 0xcf, 0x48, 0x5a, 0x71, 0xb0, 0x93, 0xeb, 0x0c, 0x76, 0x72, 0xcd, 0xc1, 0x4e,
	0x66, 0x0d, 0xa6, 0x00, 0x49, 0x26, 0x42, 0x44, 0x8a, 0x30, 0x48, 0xcd, 0xbf, 0xea, 0x77, 0xaf,
	0xa4, 0xc1, 0x01, 0x28, 0xdc, 0x4c, 0xcb, 0x62, 0xc8, 0xf7, 0x62, 0x0f, 0x5d, 0x52, 0x13, 0xaa,
	0xfa, 0xbd, 0x6b, 0x50, 0xe1, 0x30, 0x1e, 0xde, 0x19, 0xa4, 0xa7, 0x33, 0xe4, 0xfb, 0xf1, 0xa5,
	0x9f, 0x99, 0x38, 0xd5, 0x1f, 0x5e, 0x97, 0x14, 0x47, 0xfd, 0x65, 0x0e, 0xb6, 0x67, 0x26, 0x1a,
	0xe4, 0x51, 0x84, 0xdf, 0x55, 0xa9, 0x4d, 0x7d, 0xef, 0x6d, 0xc8, 0xfd, 0x4d, 0x90, 0x9a, 0x8a,
	0xc5, 0x36, 0x41, 0x56, 0x2a, 0x57, 0xbf, 0x7f, 0x1d, 0x32, 0x1c, 0xe9, 0x27, 0xb0, 0x14, 0xaa,
	0x76, 0x93, 0xad, 0x78, 0xc7, 0x50, 0xb1, 0xbc, 0x7e, 0x3b, 0x1b, 0x19, 0x72, 0x09, 0x91, 0x97,
	0x28, 0x3b, 0x69, 0x35, 0x83, 0xd0, 0xb3, 0x8c, 0xa4, 0x4b, 0x88, 0x3f, 0xb8, 0x90, 0x6e, 0x90,
	0x06, 0x94, 0x82, 0x73, 0x12, 0x89, 0x16, 0xca, 0xc2, 0xc5, 0xe6, 0x7a, 0x3d, 0x0b, 0xe5, 0xcf,
	0x34, 0x54, 0x1d, 0x8b, 0xcd, 0x34, 0x5a, 0x42, 0x8c, 0xcd, 0x34, 0x56, 0xe8, 0x0b, 0xec, 0x93,
	0xac, 0x3d, 0x24, 0xed, 0x93, 0x5a, 0x23, 0x49, 0xda, 0x27, 0xbd, 0x8c, 0x21, 0xdd, 0x20, 0x26,
	0x6c, 0x64, 0xdc, 0xf7, 0x93, 0x84, 0x43, 0xc8, 0xa8, 0x2d, 0xd4, 0x1f, 0x5c, 0x8f, 0x10, 0xc7,
	0x7b, 0x85, 0x07, 0x84, 0xd4, 0x8c, 0x96, 0x3c, 0xc8, 0xf6, 0x40, 0xd1, 0x4c, 0xb9, 0xfe, 0xfd,
	0x6b, 0x52, 0xe2, 0x90, 0x1d, 0xa8, 0x44, 0x5e, 0xbd, 0x92, 0xed, 0xd8, 0x1d, 0x72, 0xf4, 0x11,
	0x6d, 0xfd, 0xce, 0x2c, 0xb4, 0xcf, 0x31, 0x92, 0xe2, 0x92, 0x78, 0x34, 0x8b, 0x66, 0xdc, 0x31,
	0x8e, 0xc9, 0xec, 0xf8, 0x06, 0xf9, 0x05, 0xac, 0x26, 0x92, 0x5d, 0xf2, 0x5e, 0xdc, 0xad, 0x24,
	0xf2, 0xe7, 0xba, 0x74, 0x15, 0x09, 0x72, 0x6f, 0xf1, 0xeb, 0x4b, 0x3c, 0x65, 0x63, 0x62, 0xc8,
	0xcb, 0xcf, 0x7b, 0xb2, 0xff, 0xad, 0xc3, 0xd3, 0xfd, 0xbd, 0x9e, 0x3e, 0xa2, 0xb2, 0x6a, 0x0e,
	0x51, 0xbf, 0xeb, 0x11, 0x14, 0x92, 0x07, 0xab, 0xbc, 0xea, 0xb3, 0x3a, 0x73, 0xfa, 0xd4, 0x69,
	0xf5, 0xdd, 0x59, 0x8c, 0xa2, 0x28, 0xbf, 0x87, 0xe0, 0xf5, 0x53, 0xb8, 0xc9, 0x4c, 0x67, 0x99,
	0xee, 0x78, 0x44, 0xdf, 0x55, 0xb0, 0x36, 0xce, 0x51, 0x30, 0x7b, 0x67, 0xd1, 0x3a, 0x78, 0x56,
	0x3d, 0x54, 0xb5, 0x0b, 0xe6, 0x3b, 0x8f, 0x2c, 0xd7, 0x7b, 0x57, 0xf9, 0xbe, 0xc2, 0x8d, 0x16,
	0xe6, 0xf8, 0xce, 0x42, 0x76, 0x61, 0x53, 0xb6, 0x0c, 0xe3, 0x85, 0xaa, 0x5d, 0x24, 0xf8, 0x92,
	0xdb, 0x91, 0x9e, 0x32, 0xb5, 0x0d, 0x55, 0xe3, 0x7a, 0x49, 0xca, 0xd9, 0x1c, 0xd9, 0xde, 0x44,
	0x30, 0x7d, 0xce, 0x0c, 0x6c, 0x1e, 0xeb, 0xa6, 0x6a, 0x6a, 0xba, 0x6a, 0xa0, 0x97, 0xdd, 0x8d,
	0x50, 0xc7, 0xd1, 0x8c, 0xdf, 0x7b, 0x57, 0x50, 0x20, 0xeb, 0x53, 0xff, 0xe9, 0xf8, 0x17, 0xd6,
	0xd8, 0xc1, 0xe7, 0xe3, 0x31, 0x2f, 0x19, 0x7d, 0x87, 0x1e, 0xf3, 0x92, 0xf1, 0x57, 0xe7, 0x2c,
	0xe3, 0x14, 0x2f, 0xd7, 0x1b, 0xea, 0xe4, 0x3b, 0x60, 0x47, 0xe1, 0x66, 0xda, 0xd7, 0x1c, 0xb1,
	0xa4, 0x23, 0xe3, 0xe3, 0x91, 0x58, 0xd2, 0x91, 0xf9, 0x59, 0x08, 0x66, 0x6a, 0xe9, 0x9f, 0x72,
	0xc4, 0x32, 0xb5, 0xcc, 0xaf, 0x47, 0x62, 0x99, 0xda, 0x8c, 0xef, 0x42, 0x7c, 0xf7, 0x9e, 0xf6,
	0x71, 0x47, 0xd2, 0xbd, 0x67, 0x7c, 0x3f, 0x52, 0x7f, 0x70, 0x3d, 0x42, 0x3f, 0x33, 0x4c, 0x7e,
	0xa8, 0x41, 0x12, 0x5e, 0x2a, 0xf9, 0x51, 0x48, 0x2c, 0x33, 0xcc, 0xf8, 0xda, 0xc3, 0xcf, 0x27,
	0x82, 0x4f, 0x3e, 0xb6, 0x52, 0xc3, 0x3b, 0xff, 0x50, 0x23, 0x99, 0x4f, 0x84, 0xbf, 0xd0, 0x08,
	0xf2, 0x89, 0xc8, 0xa3, 0xb4, 0x9d, 0x64, 0x28, 0x8b, 0x3c, 0x4a, 0x4c, 0xe6, 0x13, 0xf1, 0xc7,
	0x88, 0xd2, 0x0d, 0xf2, 0xb5, 0x7f, 0xcd, 0x93, 0xfa, 0x01, 0x14, 0xf9, 0xed, 0x94, 0xe0, 0x95,
	0xf5, 0x8d, 0x55, 0xfd, 0x77, 0xae, 0x4f, 0x1c, 0x32, 0x78, 0xda, 0x45, 0x43, 0xd2, 0xe0, 0x19,
	0x37, 0x28, 0x49, 0x83, 0x67, 0xdd, 0x5b, 0x48, 0x37, 0xea, 0xe4, 0x7f, 0xff, 0xe5, 0xdf, 0x7b,
	0x15, 0x58, 0x0a, 0x7d, 0x84, 0x77, 0xf8, 0xf8, 0xe7, 0x8f, 0x86, 0x96, 0xa1, 0x9a, 0xc3, 0xbd,
	0x27, 0xfb, 0x9e, 0xb7, 0xa7, 0x59, 0xa3, 0xc7, 0xf8, 0x21, 0x9d, 0x66, 0x19, 0x8f, 0x5d, 0xea,
	0xbc, 0xd6, 0x35, 0xea, 0x86, 0xbf, 0xda, 0x7b, 0xb1, 0x80, 0xe8, 0x0f, 0xfe, 0x2f, 0x00, 0x00,
	0xff, 0xff, 0xe5, 0xe4, 0xe0, 0x1f, 0xd7, 0x37, 0x00, 0x00,
}
