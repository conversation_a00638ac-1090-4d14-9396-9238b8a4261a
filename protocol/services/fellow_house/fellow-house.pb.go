// Code generated by protoc-gen-go. DO NOT EDIT.
// source: fellow-house/fellow-house.proto

package fellow_house // import "golang.52tt.com/protocol/services/fellow_house"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FellowHouseType int32

const (
	FellowHouseType_ENUM_FELLOW_HOUSE_TYPE_UNKNOWN  FellowHouseType = 0
	FellowHouseType_ENUM_FELLOW_HOUSE_TYPE_CP       FellowHouseType = 1
	FellowHouseType_ENUM_FELLOW_HOUSE_TYPE_BRO      FellowHouseType = 2
	FellowHouseType_ENUM_FELLOW_HOUSE_TYPE_LADYBRO  FellowHouseType = 3
	FellowHouseType_ENUM_FELLOW_HOUSE_TYPE_INTIMATE FellowHouseType = 4
	FellowHouseType_ENUM_FELLOW_HOUSE_TYPE_PARTNER  FellowHouseType = 5
)

var FellowHouseType_name = map[int32]string{
	0: "ENUM_FELLOW_HOUSE_TYPE_UNKNOWN",
	1: "ENUM_FELLOW_HOUSE_TYPE_CP",
	2: "ENUM_FELLOW_HOUSE_TYPE_BRO",
	3: "ENUM_FELLOW_HOUSE_TYPE_LADYBRO",
	4: "ENUM_FELLOW_HOUSE_TYPE_INTIMATE",
	5: "ENUM_FELLOW_HOUSE_TYPE_PARTNER",
}
var FellowHouseType_value = map[string]int32{
	"ENUM_FELLOW_HOUSE_TYPE_UNKNOWN":  0,
	"ENUM_FELLOW_HOUSE_TYPE_CP":       1,
	"ENUM_FELLOW_HOUSE_TYPE_BRO":      2,
	"ENUM_FELLOW_HOUSE_TYPE_LADYBRO":  3,
	"ENUM_FELLOW_HOUSE_TYPE_INTIMATE": 4,
	"ENUM_FELLOW_HOUSE_TYPE_PARTNER":  5,
}

func (x FellowHouseType) String() string {
	return proto.EnumName(FellowHouseType_name, int32(x))
}
func (FellowHouseType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{0}
}

type FellowHouseRes_ResType int32

const (
	FellowHouseRes_ENUM_RES_TYPE_UNKNOWN FellowHouseRes_ResType = 0
	FellowHouseRes_ENUM_RES_TYPE_VAP     FellowHouseRes_ResType = 1
	FellowHouseRes_ENUM_RES_TYPE_LOTTIE  FellowHouseRes_ResType = 2
)

var FellowHouseRes_ResType_name = map[int32]string{
	0: "ENUM_RES_TYPE_UNKNOWN",
	1: "ENUM_RES_TYPE_VAP",
	2: "ENUM_RES_TYPE_LOTTIE",
}
var FellowHouseRes_ResType_value = map[string]int32{
	"ENUM_RES_TYPE_UNKNOWN": 0,
	"ENUM_RES_TYPE_VAP":     1,
	"ENUM_RES_TYPE_LOTTIE":  2,
}

func (x FellowHouseRes_ResType) String() string {
	return proto.EnumName(FellowHouseRes_ResType_name, int32(x))
}
func (FellowHouseRes_ResType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{0, 0}
}

type FellowHouseRes struct {
	ResType              uint32   `protobuf:"varint,1,opt,name=res_type,json=resType,proto3" json:"res_type,omitempty"`
	HouseRes             string   `protobuf:"bytes,2,opt,name=house_res,json=houseRes,proto3" json:"house_res,omitempty"`
	HouseResMd5          string   `protobuf:"bytes,3,opt,name=house_res_md5,json=houseResMd5,proto3" json:"house_res_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowHouseRes) Reset()         { *m = FellowHouseRes{} }
func (m *FellowHouseRes) String() string { return proto.CompactTextString(m) }
func (*FellowHouseRes) ProtoMessage()    {}
func (*FellowHouseRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{0}
}
func (m *FellowHouseRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowHouseRes.Unmarshal(m, b)
}
func (m *FellowHouseRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowHouseRes.Marshal(b, m, deterministic)
}
func (dst *FellowHouseRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowHouseRes.Merge(dst, src)
}
func (m *FellowHouseRes) XXX_Size() int {
	return xxx_messageInfo_FellowHouseRes.Size(m)
}
func (m *FellowHouseRes) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowHouseRes.DiscardUnknown(m)
}

var xxx_messageInfo_FellowHouseRes proto.InternalMessageInfo

func (m *FellowHouseRes) GetResType() uint32 {
	if m != nil {
		return m.ResType
	}
	return 0
}

func (m *FellowHouseRes) GetHouseRes() string {
	if m != nil {
		return m.HouseRes
	}
	return ""
}

func (m *FellowHouseRes) GetHouseResMd5() string {
	if m != nil {
		return m.HouseResMd5
	}
	return ""
}

// 小屋配置
type FellowHouseCfg struct {
	HouseId              uint32          `protobuf:"varint,1,opt,name=house_id,json=houseId,proto3" json:"house_id,omitempty"`
	Name                 string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	HouseDesc            string          `protobuf:"bytes,3,opt,name=house_desc,json=houseDesc,proto3" json:"house_desc,omitempty"`
	Icon                 string          `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	HouseRes             *FellowHouseRes `protobuf:"bytes,5,opt,name=house_res,json=houseRes,proto3" json:"house_res,omitempty"`
	BonusPercentile      uint32          `protobuf:"varint,6,opt,name=bonus_percentile,json=bonusPercentile,proto3" json:"bonus_percentile,omitempty"`
	OriginPrice          uint32          `protobuf:"varint,7,opt,name=origin_price,json=originPrice,proto3" json:"origin_price,omitempty"`
	DiscountPrice        uint32          `protobuf:"varint,8,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *FellowHouseCfg) Reset()         { *m = FellowHouseCfg{} }
func (m *FellowHouseCfg) String() string { return proto.CompactTextString(m) }
func (*FellowHouseCfg) ProtoMessage()    {}
func (*FellowHouseCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{1}
}
func (m *FellowHouseCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowHouseCfg.Unmarshal(m, b)
}
func (m *FellowHouseCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowHouseCfg.Marshal(b, m, deterministic)
}
func (dst *FellowHouseCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowHouseCfg.Merge(dst, src)
}
func (m *FellowHouseCfg) XXX_Size() int {
	return xxx_messageInfo_FellowHouseCfg.Size(m)
}
func (m *FellowHouseCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowHouseCfg.DiscardUnknown(m)
}

var xxx_messageInfo_FellowHouseCfg proto.InternalMessageInfo

func (m *FellowHouseCfg) GetHouseId() uint32 {
	if m != nil {
		return m.HouseId
	}
	return 0
}

func (m *FellowHouseCfg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *FellowHouseCfg) GetHouseDesc() string {
	if m != nil {
		return m.HouseDesc
	}
	return ""
}

func (m *FellowHouseCfg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *FellowHouseCfg) GetHouseRes() *FellowHouseRes {
	if m != nil {
		return m.HouseRes
	}
	return nil
}

func (m *FellowHouseCfg) GetBonusPercentile() uint32 {
	if m != nil {
		return m.BonusPercentile
	}
	return 0
}

func (m *FellowHouseCfg) GetOriginPrice() uint32 {
	if m != nil {
		return m.OriginPrice
	}
	return 0
}

func (m *FellowHouseCfg) GetDiscountPrice() uint32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

type FellowHouseProduct struct {
	HouseCfg             *FellowHouseCfg `protobuf:"bytes,1,opt,name=house_cfg,json=houseCfg,proto3" json:"house_cfg,omitempty"`
	Duration             uint32          `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`
	RankOrder            uint32          `protobuf:"varint,3,opt,name=rank_order,json=rankOrder,proto3" json:"rank_order,omitempty"`
	FellowTypeLimit      uint32          `protobuf:"varint,4,opt,name=fellow_type_limit,json=fellowTypeLimit,proto3" json:"fellow_type_limit,omitempty"`
	FellowLevelLimit     uint32          `protobuf:"varint,5,opt,name=fellow_level_limit,json=fellowLevelLimit,proto3" json:"fellow_level_limit,omitempty"`
	UpdateTime           int64           `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *FellowHouseProduct) Reset()         { *m = FellowHouseProduct{} }
func (m *FellowHouseProduct) String() string { return proto.CompactTextString(m) }
func (*FellowHouseProduct) ProtoMessage()    {}
func (*FellowHouseProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{2}
}
func (m *FellowHouseProduct) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowHouseProduct.Unmarshal(m, b)
}
func (m *FellowHouseProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowHouseProduct.Marshal(b, m, deterministic)
}
func (dst *FellowHouseProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowHouseProduct.Merge(dst, src)
}
func (m *FellowHouseProduct) XXX_Size() int {
	return xxx_messageInfo_FellowHouseProduct.Size(m)
}
func (m *FellowHouseProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowHouseProduct.DiscardUnknown(m)
}

var xxx_messageInfo_FellowHouseProduct proto.InternalMessageInfo

func (m *FellowHouseProduct) GetHouseCfg() *FellowHouseCfg {
	if m != nil {
		return m.HouseCfg
	}
	return nil
}

func (m *FellowHouseProduct) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *FellowHouseProduct) GetRankOrder() uint32 {
	if m != nil {
		return m.RankOrder
	}
	return 0
}

func (m *FellowHouseProduct) GetFellowTypeLimit() uint32 {
	if m != nil {
		return m.FellowTypeLimit
	}
	return 0
}

func (m *FellowHouseProduct) GetFellowLevelLimit() uint32 {
	if m != nil {
		return m.FellowLevelLimit
	}
	return 0
}

func (m *FellowHouseProduct) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 新增小屋配置
type UpdateFellowHouseProductReq struct {
	Product              *FellowHouseProduct `protobuf:"bytes,1,opt,name=product,proto3" json:"product,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UpdateFellowHouseProductReq) Reset()         { *m = UpdateFellowHouseProductReq{} }
func (m *UpdateFellowHouseProductReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFellowHouseProductReq) ProtoMessage()    {}
func (*UpdateFellowHouseProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{3}
}
func (m *UpdateFellowHouseProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFellowHouseProductReq.Unmarshal(m, b)
}
func (m *UpdateFellowHouseProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFellowHouseProductReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFellowHouseProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFellowHouseProductReq.Merge(dst, src)
}
func (m *UpdateFellowHouseProductReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFellowHouseProductReq.Size(m)
}
func (m *UpdateFellowHouseProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFellowHouseProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFellowHouseProductReq proto.InternalMessageInfo

func (m *UpdateFellowHouseProductReq) GetProduct() *FellowHouseProduct {
	if m != nil {
		return m.Product
	}
	return nil
}

type UpdateFellowHouseProductResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFellowHouseProductResp) Reset()         { *m = UpdateFellowHouseProductResp{} }
func (m *UpdateFellowHouseProductResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFellowHouseProductResp) ProtoMessage()    {}
func (*UpdateFellowHouseProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{4}
}
func (m *UpdateFellowHouseProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFellowHouseProductResp.Unmarshal(m, b)
}
func (m *UpdateFellowHouseProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFellowHouseProductResp.Marshal(b, m, deterministic)
}
func (dst *UpdateFellowHouseProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFellowHouseProductResp.Merge(dst, src)
}
func (m *UpdateFellowHouseProductResp) XXX_Size() int {
	return xxx_messageInfo_UpdateFellowHouseProductResp.Size(m)
}
func (m *UpdateFellowHouseProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFellowHouseProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFellowHouseProductResp proto.InternalMessageInfo

// 批量删除小屋配置
type DeleteFellowHouseProductReq struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteFellowHouseProductReq) Reset()         { *m = DeleteFellowHouseProductReq{} }
func (m *DeleteFellowHouseProductReq) String() string { return proto.CompactTextString(m) }
func (*DeleteFellowHouseProductReq) ProtoMessage()    {}
func (*DeleteFellowHouseProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{5}
}
func (m *DeleteFellowHouseProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteFellowHouseProductReq.Unmarshal(m, b)
}
func (m *DeleteFellowHouseProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteFellowHouseProductReq.Marshal(b, m, deterministic)
}
func (dst *DeleteFellowHouseProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteFellowHouseProductReq.Merge(dst, src)
}
func (m *DeleteFellowHouseProductReq) XXX_Size() int {
	return xxx_messageInfo_DeleteFellowHouseProductReq.Size(m)
}
func (m *DeleteFellowHouseProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteFellowHouseProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteFellowHouseProductReq proto.InternalMessageInfo

func (m *DeleteFellowHouseProductReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type DeleteFellowHouseProductResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteFellowHouseProductResp) Reset()         { *m = DeleteFellowHouseProductResp{} }
func (m *DeleteFellowHouseProductResp) String() string { return proto.CompactTextString(m) }
func (*DeleteFellowHouseProductResp) ProtoMessage()    {}
func (*DeleteFellowHouseProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{6}
}
func (m *DeleteFellowHouseProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteFellowHouseProductResp.Unmarshal(m, b)
}
func (m *DeleteFellowHouseProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteFellowHouseProductResp.Marshal(b, m, deterministic)
}
func (dst *DeleteFellowHouseProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteFellowHouseProductResp.Merge(dst, src)
}
func (m *DeleteFellowHouseProductResp) XXX_Size() int {
	return xxx_messageInfo_DeleteFellowHouseProductResp.Size(m)
}
func (m *DeleteFellowHouseProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteFellowHouseProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteFellowHouseProductResp proto.InternalMessageInfo

// 搜索小屋，支持ID\名称模糊搜索 （全量返回结果）
type SearchFellowHouseProductReq struct {
	Keyword              string   `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchFellowHouseProductReq) Reset()         { *m = SearchFellowHouseProductReq{} }
func (m *SearchFellowHouseProductReq) String() string { return proto.CompactTextString(m) }
func (*SearchFellowHouseProductReq) ProtoMessage()    {}
func (*SearchFellowHouseProductReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{7}
}
func (m *SearchFellowHouseProductReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchFellowHouseProductReq.Unmarshal(m, b)
}
func (m *SearchFellowHouseProductReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchFellowHouseProductReq.Marshal(b, m, deterministic)
}
func (dst *SearchFellowHouseProductReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchFellowHouseProductReq.Merge(dst, src)
}
func (m *SearchFellowHouseProductReq) XXX_Size() int {
	return xxx_messageInfo_SearchFellowHouseProductReq.Size(m)
}
func (m *SearchFellowHouseProductReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchFellowHouseProductReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchFellowHouseProductReq proto.InternalMessageInfo

func (m *SearchFellowHouseProductReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

type SearchFellowHouseProductResp struct {
	ProductList          []*FellowHouseProduct `protobuf:"bytes,1,rep,name=product_list,json=productList,proto3" json:"product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SearchFellowHouseProductResp) Reset()         { *m = SearchFellowHouseProductResp{} }
func (m *SearchFellowHouseProductResp) String() string { return proto.CompactTextString(m) }
func (*SearchFellowHouseProductResp) ProtoMessage()    {}
func (*SearchFellowHouseProductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{8}
}
func (m *SearchFellowHouseProductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchFellowHouseProductResp.Unmarshal(m, b)
}
func (m *SearchFellowHouseProductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchFellowHouseProductResp.Marshal(b, m, deterministic)
}
func (dst *SearchFellowHouseProductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchFellowHouseProductResp.Merge(dst, src)
}
func (m *SearchFellowHouseProductResp) XXX_Size() int {
	return xxx_messageInfo_SearchFellowHouseProductResp.Size(m)
}
func (m *SearchFellowHouseProductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchFellowHouseProductResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchFellowHouseProductResp proto.InternalMessageInfo

func (m *SearchFellowHouseProductResp) GetProductList() []*FellowHouseProduct {
	if m != nil {
		return m.ProductList
	}
	return nil
}

// 全量获取小屋配置
type GetFellowHouseProductListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowHouseProductListReq) Reset()         { *m = GetFellowHouseProductListReq{} }
func (m *GetFellowHouseProductListReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseProductListReq) ProtoMessage()    {}
func (*GetFellowHouseProductListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{9}
}
func (m *GetFellowHouseProductListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseProductListReq.Unmarshal(m, b)
}
func (m *GetFellowHouseProductListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseProductListReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseProductListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseProductListReq.Merge(dst, src)
}
func (m *GetFellowHouseProductListReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseProductListReq.Size(m)
}
func (m *GetFellowHouseProductListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseProductListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseProductListReq proto.InternalMessageInfo

type GetFellowHouseProductListResp struct {
	ProductList          []*FellowHouseProduct `protobuf:"bytes,1,rep,name=product_list,json=productList,proto3" json:"product_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetFellowHouseProductListResp) Reset()         { *m = GetFellowHouseProductListResp{} }
func (m *GetFellowHouseProductListResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseProductListResp) ProtoMessage()    {}
func (*GetFellowHouseProductListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{10}
}
func (m *GetFellowHouseProductListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseProductListResp.Unmarshal(m, b)
}
func (m *GetFellowHouseProductListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseProductListResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseProductListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseProductListResp.Merge(dst, src)
}
func (m *GetFellowHouseProductListResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseProductListResp.Size(m)
}
func (m *GetFellowHouseProductListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseProductListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseProductListResp proto.InternalMessageInfo

func (m *GetFellowHouseProductListResp) GetProductList() []*FellowHouseProduct {
	if m != nil {
		return m.ProductList
	}
	return nil
}

// 根据ID获取小屋配置
type GetFellowHouseProductByIdReq struct {
	CfgId                uint32   `protobuf:"varint,1,opt,name=cfg_id,json=cfgId,proto3" json:"cfg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowHouseProductByIdReq) Reset()         { *m = GetFellowHouseProductByIdReq{} }
func (m *GetFellowHouseProductByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseProductByIdReq) ProtoMessage()    {}
func (*GetFellowHouseProductByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{11}
}
func (m *GetFellowHouseProductByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseProductByIdReq.Unmarshal(m, b)
}
func (m *GetFellowHouseProductByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseProductByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseProductByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseProductByIdReq.Merge(dst, src)
}
func (m *GetFellowHouseProductByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseProductByIdReq.Size(m)
}
func (m *GetFellowHouseProductByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseProductByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseProductByIdReq proto.InternalMessageInfo

func (m *GetFellowHouseProductByIdReq) GetCfgId() uint32 {
	if m != nil {
		return m.CfgId
	}
	return 0
}

type GetFellowHouseProductByIdResp struct {
	Cfg                  *FellowHouseProduct `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetFellowHouseProductByIdResp) Reset()         { *m = GetFellowHouseProductByIdResp{} }
func (m *GetFellowHouseProductByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseProductByIdResp) ProtoMessage()    {}
func (*GetFellowHouseProductByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{12}
}
func (m *GetFellowHouseProductByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseProductByIdResp.Unmarshal(m, b)
}
func (m *GetFellowHouseProductByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseProductByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseProductByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseProductByIdResp.Merge(dst, src)
}
func (m *GetFellowHouseProductByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseProductByIdResp.Size(m)
}
func (m *GetFellowHouseProductByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseProductByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseProductByIdResp proto.InternalMessageInfo

func (m *GetFellowHouseProductByIdResp) GetCfg() *FellowHouseProduct {
	if m != nil {
		return m.Cfg
	}
	return nil
}

// 发放小屋奖励
type GiveFellowHouseReq struct {
	HouseId              uint32   `protobuf:"varint,1,opt,name=house_id,json=houseId,proto3" json:"house_id,omitempty"`
	UidA                 uint32   `protobuf:"varint,2,opt,name=uid_a,json=uidA,proto3" json:"uid_a,omitempty"`
	UidB                 uint32   `protobuf:"varint,3,opt,name=uid_b,json=uidB,proto3" json:"uid_b,omitempty"`
	Duration             int32    `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	OrderId              string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	SendTs               int64    `protobuf:"varint,6,opt,name=send_ts,json=sendTs,proto3" json:"send_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveFellowHouseReq) Reset()         { *m = GiveFellowHouseReq{} }
func (m *GiveFellowHouseReq) String() string { return proto.CompactTextString(m) }
func (*GiveFellowHouseReq) ProtoMessage()    {}
func (*GiveFellowHouseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{13}
}
func (m *GiveFellowHouseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveFellowHouseReq.Unmarshal(m, b)
}
func (m *GiveFellowHouseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveFellowHouseReq.Marshal(b, m, deterministic)
}
func (dst *GiveFellowHouseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveFellowHouseReq.Merge(dst, src)
}
func (m *GiveFellowHouseReq) XXX_Size() int {
	return xxx_messageInfo_GiveFellowHouseReq.Size(m)
}
func (m *GiveFellowHouseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveFellowHouseReq.DiscardUnknown(m)
}

var xxx_messageInfo_GiveFellowHouseReq proto.InternalMessageInfo

func (m *GiveFellowHouseReq) GetHouseId() uint32 {
	if m != nil {
		return m.HouseId
	}
	return 0
}

func (m *GiveFellowHouseReq) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *GiveFellowHouseReq) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

func (m *GiveFellowHouseReq) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *GiveFellowHouseReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GiveFellowHouseReq) GetSendTs() int64 {
	if m != nil {
		return m.SendTs
	}
	return 0
}

type GiveFellowHouseResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiveFellowHouseResp) Reset()         { *m = GiveFellowHouseResp{} }
func (m *GiveFellowHouseResp) String() string { return proto.CompactTextString(m) }
func (*GiveFellowHouseResp) ProtoMessage()    {}
func (*GiveFellowHouseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{14}
}
func (m *GiveFellowHouseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiveFellowHouseResp.Unmarshal(m, b)
}
func (m *GiveFellowHouseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiveFellowHouseResp.Marshal(b, m, deterministic)
}
func (dst *GiveFellowHouseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiveFellowHouseResp.Merge(dst, src)
}
func (m *GiveFellowHouseResp) XXX_Size() int {
	return xxx_messageInfo_GiveFellowHouseResp.Size(m)
}
func (m *GiveFellowHouseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GiveFellowHouseResp.DiscardUnknown(m)
}

var xxx_messageInfo_GiveFellowHouseResp proto.InternalMessageInfo

type FellowHouseInfo struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CpUid                uint32          `protobuf:"varint,2,opt,name=cp_uid,json=cpUid,proto3" json:"cp_uid,omitempty"`
	HouseCfg             *FellowHouseCfg `protobuf:"bytes,3,opt,name=house_cfg,json=houseCfg,proto3" json:"house_cfg,omitempty"`
	ExpireTs             int64           `protobuf:"varint,4,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	ObtainTs             int64           `protobuf:"varint,5,opt,name=obtain_ts,json=obtainTs,proto3" json:"obtain_ts,omitempty"`
	InUse                bool            `protobuf:"varint,6,opt,name=in_use,json=inUse,proto3" json:"in_use,omitempty"`
	UseTs                int64           `protobuf:"varint,7,opt,name=use_ts,json=useTs,proto3" json:"use_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *FellowHouseInfo) Reset()         { *m = FellowHouseInfo{} }
func (m *FellowHouseInfo) String() string { return proto.CompactTextString(m) }
func (*FellowHouseInfo) ProtoMessage()    {}
func (*FellowHouseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{15}
}
func (m *FellowHouseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowHouseInfo.Unmarshal(m, b)
}
func (m *FellowHouseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowHouseInfo.Marshal(b, m, deterministic)
}
func (dst *FellowHouseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowHouseInfo.Merge(dst, src)
}
func (m *FellowHouseInfo) XXX_Size() int {
	return xxx_messageInfo_FellowHouseInfo.Size(m)
}
func (m *FellowHouseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowHouseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowHouseInfo proto.InternalMessageInfo

func (m *FellowHouseInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FellowHouseInfo) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

func (m *FellowHouseInfo) GetHouseCfg() *FellowHouseCfg {
	if m != nil {
		return m.HouseCfg
	}
	return nil
}

func (m *FellowHouseInfo) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *FellowHouseInfo) GetObtainTs() int64 {
	if m != nil {
		return m.ObtainTs
	}
	return 0
}

func (m *FellowHouseInfo) GetInUse() bool {
	if m != nil {
		return m.InUse
	}
	return false
}

func (m *FellowHouseInfo) GetUseTs() int64 {
	if m != nil {
		return m.UseTs
	}
	return 0
}

// 获取用户的小屋列表
type GetUserFellowHouseListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CpUid                uint32   `protobuf:"varint,2,opt,name=cp_uid,json=cpUid,proto3" json:"cp_uid,omitempty"`
	IgnoreExpired        bool     `protobuf:"varint,3,opt,name=ignore_expired,json=ignoreExpired,proto3" json:"ignore_expired,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserFellowHouseListReq) Reset()         { *m = GetUserFellowHouseListReq{} }
func (m *GetUserFellowHouseListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFellowHouseListReq) ProtoMessage()    {}
func (*GetUserFellowHouseListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{16}
}
func (m *GetUserFellowHouseListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFellowHouseListReq.Unmarshal(m, b)
}
func (m *GetUserFellowHouseListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFellowHouseListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserFellowHouseListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFellowHouseListReq.Merge(dst, src)
}
func (m *GetUserFellowHouseListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFellowHouseListReq.Size(m)
}
func (m *GetUserFellowHouseListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFellowHouseListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFellowHouseListReq proto.InternalMessageInfo

func (m *GetUserFellowHouseListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserFellowHouseListReq) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

func (m *GetUserFellowHouseListReq) GetIgnoreExpired() bool {
	if m != nil {
		return m.IgnoreExpired
	}
	return false
}

type GetUserFellowHouseListResp struct {
	HouseList            []*FellowHouseInfo `protobuf:"bytes,1,rep,name=house_list,json=houseList,proto3" json:"house_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserFellowHouseListResp) Reset()         { *m = GetUserFellowHouseListResp{} }
func (m *GetUserFellowHouseListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFellowHouseListResp) ProtoMessage()    {}
func (*GetUserFellowHouseListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{17}
}
func (m *GetUserFellowHouseListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFellowHouseListResp.Unmarshal(m, b)
}
func (m *GetUserFellowHouseListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFellowHouseListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserFellowHouseListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFellowHouseListResp.Merge(dst, src)
}
func (m *GetUserFellowHouseListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserFellowHouseListResp.Size(m)
}
func (m *GetUserFellowHouseListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFellowHouseListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFellowHouseListResp proto.InternalMessageInfo

func (m *GetUserFellowHouseListResp) GetHouseList() []*FellowHouseInfo {
	if m != nil {
		return m.HouseList
	}
	return nil
}

// 使用小屋
type SetUserFellowHouseInuseReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CpUid                uint32   `protobuf:"varint,2,opt,name=cp_uid,json=cpUid,proto3" json:"cp_uid,omitempty"`
	HouseId              uint32   `protobuf:"varint,3,opt,name=house_id,json=houseId,proto3" json:"house_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserFellowHouseInuseReq) Reset()         { *m = SetUserFellowHouseInuseReq{} }
func (m *SetUserFellowHouseInuseReq) String() string { return proto.CompactTextString(m) }
func (*SetUserFellowHouseInuseReq) ProtoMessage()    {}
func (*SetUserFellowHouseInuseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{18}
}
func (m *SetUserFellowHouseInuseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserFellowHouseInuseReq.Unmarshal(m, b)
}
func (m *SetUserFellowHouseInuseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserFellowHouseInuseReq.Marshal(b, m, deterministic)
}
func (dst *SetUserFellowHouseInuseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserFellowHouseInuseReq.Merge(dst, src)
}
func (m *SetUserFellowHouseInuseReq) XXX_Size() int {
	return xxx_messageInfo_SetUserFellowHouseInuseReq.Size(m)
}
func (m *SetUserFellowHouseInuseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserFellowHouseInuseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserFellowHouseInuseReq proto.InternalMessageInfo

func (m *SetUserFellowHouseInuseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserFellowHouseInuseReq) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

func (m *SetUserFellowHouseInuseReq) GetHouseId() uint32 {
	if m != nil {
		return m.HouseId
	}
	return 0
}

type SetUserFellowHouseInuseResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserFellowHouseInuseResp) Reset()         { *m = SetUserFellowHouseInuseResp{} }
func (m *SetUserFellowHouseInuseResp) String() string { return proto.CompactTextString(m) }
func (*SetUserFellowHouseInuseResp) ProtoMessage()    {}
func (*SetUserFellowHouseInuseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{19}
}
func (m *SetUserFellowHouseInuseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserFellowHouseInuseResp.Unmarshal(m, b)
}
func (m *SetUserFellowHouseInuseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserFellowHouseInuseResp.Marshal(b, m, deterministic)
}
func (dst *SetUserFellowHouseInuseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserFellowHouseInuseResp.Merge(dst, src)
}
func (m *SetUserFellowHouseInuseResp) XXX_Size() int {
	return xxx_messageInfo_SetUserFellowHouseInuseResp.Size(m)
}
func (m *SetUserFellowHouseInuseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserFellowHouseInuseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserFellowHouseInuseResp proto.InternalMessageInfo

// 获取用户正在使用的小屋列表
type GetUserFellowHouseInuseListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserFellowHouseInuseListReq) Reset()         { *m = GetUserFellowHouseInuseListReq{} }
func (m *GetUserFellowHouseInuseListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFellowHouseInuseListReq) ProtoMessage()    {}
func (*GetUserFellowHouseInuseListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{20}
}
func (m *GetUserFellowHouseInuseListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFellowHouseInuseListReq.Unmarshal(m, b)
}
func (m *GetUserFellowHouseInuseListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFellowHouseInuseListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserFellowHouseInuseListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFellowHouseInuseListReq.Merge(dst, src)
}
func (m *GetUserFellowHouseInuseListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFellowHouseInuseListReq.Size(m)
}
func (m *GetUserFellowHouseInuseListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFellowHouseInuseListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFellowHouseInuseListReq proto.InternalMessageInfo

func (m *GetUserFellowHouseInuseListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserFellowHouseInuseListResp struct {
	HouseList            []*FellowHouseInfo `protobuf:"bytes,1,rep,name=house_list,json=houseList,proto3" json:"house_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserFellowHouseInuseListResp) Reset()         { *m = GetUserFellowHouseInuseListResp{} }
func (m *GetUserFellowHouseInuseListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFellowHouseInuseListResp) ProtoMessage()    {}
func (*GetUserFellowHouseInuseListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{21}
}
func (m *GetUserFellowHouseInuseListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFellowHouseInuseListResp.Unmarshal(m, b)
}
func (m *GetUserFellowHouseInuseListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFellowHouseInuseListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserFellowHouseInuseListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFellowHouseInuseListResp.Merge(dst, src)
}
func (m *GetUserFellowHouseInuseListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserFellowHouseInuseListResp.Size(m)
}
func (m *GetUserFellowHouseInuseListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFellowHouseInuseListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFellowHouseInuseListResp proto.InternalMessageInfo

func (m *GetUserFellowHouseInuseListResp) GetHouseList() []*FellowHouseInfo {
	if m != nil {
		return m.HouseList
	}
	return nil
}

type FellowHouseCp struct {
	UidA                 uint32   `protobuf:"varint,1,opt,name=uid_a,json=uidA,proto3" json:"uid_a,omitempty"`
	UidB                 uint32   `protobuf:"varint,2,opt,name=uid_b,json=uidB,proto3" json:"uid_b,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowHouseCp) Reset()         { *m = FellowHouseCp{} }
func (m *FellowHouseCp) String() string { return proto.CompactTextString(m) }
func (*FellowHouseCp) ProtoMessage()    {}
func (*FellowHouseCp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{22}
}
func (m *FellowHouseCp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowHouseCp.Unmarshal(m, b)
}
func (m *FellowHouseCp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowHouseCp.Marshal(b, m, deterministic)
}
func (dst *FellowHouseCp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowHouseCp.Merge(dst, src)
}
func (m *FellowHouseCp) XXX_Size() int {
	return xxx_messageInfo_FellowHouseCp.Size(m)
}
func (m *FellowHouseCp) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowHouseCp.DiscardUnknown(m)
}

var xxx_messageInfo_FellowHouseCp proto.InternalMessageInfo

func (m *FellowHouseCp) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *FellowHouseCp) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

// 批量获取用户使用的小屋
type GetFellowHouseInuseListByCpsReq struct {
	CpList               []*FellowHouseCp `protobuf:"bytes,1,rep,name=cp_list,json=cpList,proto3" json:"cp_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetFellowHouseInuseListByCpsReq) Reset()         { *m = GetFellowHouseInuseListByCpsReq{} }
func (m *GetFellowHouseInuseListByCpsReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseInuseListByCpsReq) ProtoMessage()    {}
func (*GetFellowHouseInuseListByCpsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{23}
}
func (m *GetFellowHouseInuseListByCpsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseInuseListByCpsReq.Unmarshal(m, b)
}
func (m *GetFellowHouseInuseListByCpsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseInuseListByCpsReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseInuseListByCpsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseInuseListByCpsReq.Merge(dst, src)
}
func (m *GetFellowHouseInuseListByCpsReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseInuseListByCpsReq.Size(m)
}
func (m *GetFellowHouseInuseListByCpsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseInuseListByCpsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseInuseListByCpsReq proto.InternalMessageInfo

func (m *GetFellowHouseInuseListByCpsReq) GetCpList() []*FellowHouseCp {
	if m != nil {
		return m.CpList
	}
	return nil
}

type GetFellowHouseInuseListByCpsResp struct {
	HouseList            []*FellowHouseInfo `protobuf:"bytes,1,rep,name=house_list,json=houseList,proto3" json:"house_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetFellowHouseInuseListByCpsResp) Reset()         { *m = GetFellowHouseInuseListByCpsResp{} }
func (m *GetFellowHouseInuseListByCpsResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseInuseListByCpsResp) ProtoMessage()    {}
func (*GetFellowHouseInuseListByCpsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{24}
}
func (m *GetFellowHouseInuseListByCpsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseInuseListByCpsResp.Unmarshal(m, b)
}
func (m *GetFellowHouseInuseListByCpsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseInuseListByCpsResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseInuseListByCpsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseInuseListByCpsResp.Merge(dst, src)
}
func (m *GetFellowHouseInuseListByCpsResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseInuseListByCpsResp.Size(m)
}
func (m *GetFellowHouseInuseListByCpsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseInuseListByCpsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseInuseListByCpsResp proto.InternalMessageInfo

func (m *GetFellowHouseInuseListByCpsResp) GetHouseList() []*FellowHouseInfo {
	if m != nil {
		return m.HouseList
	}
	return nil
}

// 购买小屋
type BuyFellowHouseReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	HouseId              uint32   `protobuf:"varint,2,opt,name=house_id,json=houseId,proto3" json:"house_id,omitempty"`
	FellowUid            uint32   `protobuf:"varint,3,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid,omitempty"`
	Amount               uint32   `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	BuyTs                int64    `protobuf:"varint,5,opt,name=buy_ts,json=buyTs,proto3" json:"buy_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyFellowHouseReq) Reset()         { *m = BuyFellowHouseReq{} }
func (m *BuyFellowHouseReq) String() string { return proto.CompactTextString(m) }
func (*BuyFellowHouseReq) ProtoMessage()    {}
func (*BuyFellowHouseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{25}
}
func (m *BuyFellowHouseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyFellowHouseReq.Unmarshal(m, b)
}
func (m *BuyFellowHouseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyFellowHouseReq.Marshal(b, m, deterministic)
}
func (dst *BuyFellowHouseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyFellowHouseReq.Merge(dst, src)
}
func (m *BuyFellowHouseReq) XXX_Size() int {
	return xxx_messageInfo_BuyFellowHouseReq.Size(m)
}
func (m *BuyFellowHouseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyFellowHouseReq.DiscardUnknown(m)
}

var xxx_messageInfo_BuyFellowHouseReq proto.InternalMessageInfo

func (m *BuyFellowHouseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BuyFellowHouseReq) GetHouseId() uint32 {
	if m != nil {
		return m.HouseId
	}
	return 0
}

func (m *BuyFellowHouseReq) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *BuyFellowHouseReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *BuyFellowHouseReq) GetBuyTs() int64 {
	if m != nil {
		return m.BuyTs
	}
	return 0
}

type BuyFellowHouseResp struct {
	Balance              uint32   `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyFellowHouseResp) Reset()         { *m = BuyFellowHouseResp{} }
func (m *BuyFellowHouseResp) String() string { return proto.CompactTextString(m) }
func (*BuyFellowHouseResp) ProtoMessage()    {}
func (*BuyFellowHouseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{26}
}
func (m *BuyFellowHouseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyFellowHouseResp.Unmarshal(m, b)
}
func (m *BuyFellowHouseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyFellowHouseResp.Marshal(b, m, deterministic)
}
func (dst *BuyFellowHouseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyFellowHouseResp.Merge(dst, src)
}
func (m *BuyFellowHouseResp) XXX_Size() int {
	return xxx_messageInfo_BuyFellowHouseResp.Size(m)
}
func (m *BuyFellowHouseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyFellowHouseResp.DiscardUnknown(m)
}

var xxx_messageInfo_BuyFellowHouseResp proto.InternalMessageInfo

func (m *BuyFellowHouseResp) GetBalance() uint32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

type GetFellowHouseProductByIdsReq struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowHouseProductByIdsReq) Reset()         { *m = GetFellowHouseProductByIdsReq{} }
func (m *GetFellowHouseProductByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseProductByIdsReq) ProtoMessage()    {}
func (*GetFellowHouseProductByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{27}
}
func (m *GetFellowHouseProductByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseProductByIdsReq.Unmarshal(m, b)
}
func (m *GetFellowHouseProductByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseProductByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseProductByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseProductByIdsReq.Merge(dst, src)
}
func (m *GetFellowHouseProductByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseProductByIdsReq.Size(m)
}
func (m *GetFellowHouseProductByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseProductByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseProductByIdsReq proto.InternalMessageInfo

func (m *GetFellowHouseProductByIdsReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GetFellowHouseProductByIdsResp struct {
	CfgList              []*FellowHouseCfg `protobuf:"bytes,1,rep,name=cfg_list,json=cfgList,proto3" json:"cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetFellowHouseProductByIdsResp) Reset()         { *m = GetFellowHouseProductByIdsResp{} }
func (m *GetFellowHouseProductByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseProductByIdsResp) ProtoMessage()    {}
func (*GetFellowHouseProductByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_house_c1f814c860cc53df, []int{28}
}
func (m *GetFellowHouseProductByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseProductByIdsResp.Unmarshal(m, b)
}
func (m *GetFellowHouseProductByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseProductByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseProductByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseProductByIdsResp.Merge(dst, src)
}
func (m *GetFellowHouseProductByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseProductByIdsResp.Size(m)
}
func (m *GetFellowHouseProductByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseProductByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseProductByIdsResp proto.InternalMessageInfo

func (m *GetFellowHouseProductByIdsResp) GetCfgList() []*FellowHouseCfg {
	if m != nil {
		return m.CfgList
	}
	return nil
}

func init() {
	proto.RegisterType((*FellowHouseRes)(nil), "fellow_house.FellowHouseRes")
	proto.RegisterType((*FellowHouseCfg)(nil), "fellow_house.FellowHouseCfg")
	proto.RegisterType((*FellowHouseProduct)(nil), "fellow_house.FellowHouseProduct")
	proto.RegisterType((*UpdateFellowHouseProductReq)(nil), "fellow_house.UpdateFellowHouseProductReq")
	proto.RegisterType((*UpdateFellowHouseProductResp)(nil), "fellow_house.UpdateFellowHouseProductResp")
	proto.RegisterType((*DeleteFellowHouseProductReq)(nil), "fellow_house.DeleteFellowHouseProductReq")
	proto.RegisterType((*DeleteFellowHouseProductResp)(nil), "fellow_house.DeleteFellowHouseProductResp")
	proto.RegisterType((*SearchFellowHouseProductReq)(nil), "fellow_house.SearchFellowHouseProductReq")
	proto.RegisterType((*SearchFellowHouseProductResp)(nil), "fellow_house.SearchFellowHouseProductResp")
	proto.RegisterType((*GetFellowHouseProductListReq)(nil), "fellow_house.GetFellowHouseProductListReq")
	proto.RegisterType((*GetFellowHouseProductListResp)(nil), "fellow_house.GetFellowHouseProductListResp")
	proto.RegisterType((*GetFellowHouseProductByIdReq)(nil), "fellow_house.GetFellowHouseProductByIdReq")
	proto.RegisterType((*GetFellowHouseProductByIdResp)(nil), "fellow_house.GetFellowHouseProductByIdResp")
	proto.RegisterType((*GiveFellowHouseReq)(nil), "fellow_house.GiveFellowHouseReq")
	proto.RegisterType((*GiveFellowHouseResp)(nil), "fellow_house.GiveFellowHouseResp")
	proto.RegisterType((*FellowHouseInfo)(nil), "fellow_house.FellowHouseInfo")
	proto.RegisterType((*GetUserFellowHouseListReq)(nil), "fellow_house.GetUserFellowHouseListReq")
	proto.RegisterType((*GetUserFellowHouseListResp)(nil), "fellow_house.GetUserFellowHouseListResp")
	proto.RegisterType((*SetUserFellowHouseInuseReq)(nil), "fellow_house.SetUserFellowHouseInuseReq")
	proto.RegisterType((*SetUserFellowHouseInuseResp)(nil), "fellow_house.SetUserFellowHouseInuseResp")
	proto.RegisterType((*GetUserFellowHouseInuseListReq)(nil), "fellow_house.GetUserFellowHouseInuseListReq")
	proto.RegisterType((*GetUserFellowHouseInuseListResp)(nil), "fellow_house.GetUserFellowHouseInuseListResp")
	proto.RegisterType((*FellowHouseCp)(nil), "fellow_house.FellowHouseCp")
	proto.RegisterType((*GetFellowHouseInuseListByCpsReq)(nil), "fellow_house.GetFellowHouseInuseListByCpsReq")
	proto.RegisterType((*GetFellowHouseInuseListByCpsResp)(nil), "fellow_house.GetFellowHouseInuseListByCpsResp")
	proto.RegisterType((*BuyFellowHouseReq)(nil), "fellow_house.BuyFellowHouseReq")
	proto.RegisterType((*BuyFellowHouseResp)(nil), "fellow_house.BuyFellowHouseResp")
	proto.RegisterType((*GetFellowHouseProductByIdsReq)(nil), "fellow_house.GetFellowHouseProductByIdsReq")
	proto.RegisterType((*GetFellowHouseProductByIdsResp)(nil), "fellow_house.GetFellowHouseProductByIdsResp")
	proto.RegisterEnum("fellow_house.FellowHouseType", FellowHouseType_name, FellowHouseType_value)
	proto.RegisterEnum("fellow_house.FellowHouseRes_ResType", FellowHouseRes_ResType_name, FellowHouseRes_ResType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// FellowHouseClient is the client API for FellowHouse service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FellowHouseClient interface {
	// 新增小屋配置
	UpdateFellowHouseProduct(ctx context.Context, in *UpdateFellowHouseProductReq, opts ...grpc.CallOption) (*UpdateFellowHouseProductResp, error)
	// 批量删除小屋配置
	DeleteFellowHouseProduct(ctx context.Context, in *DeleteFellowHouseProductReq, opts ...grpc.CallOption) (*DeleteFellowHouseProductResp, error)
	// 搜索小屋，支持ID\名称模糊搜索 （全量）
	SearchFellowHouseProduct(ctx context.Context, in *SearchFellowHouseProductReq, opts ...grpc.CallOption) (*SearchFellowHouseProductResp, error)
	// 全量获取小屋配置
	GetFellowHouseProductList(ctx context.Context, in *GetFellowHouseProductListReq, opts ...grpc.CallOption) (*GetFellowHouseProductListResp, error)
	// 根据ID获取小屋配置
	GetFellowHouseProductById(ctx context.Context, in *GetFellowHouseProductByIdReq, opts ...grpc.CallOption) (*GetFellowHouseProductByIdResp, error)
	// 根据Id批量获取小屋配置
	GetFellowHouseProductByIds(ctx context.Context, in *GetFellowHouseProductByIdsReq, opts ...grpc.CallOption) (*GetFellowHouseProductByIdsResp, error)
	// 发放小屋奖励
	GiveFellowHouse(ctx context.Context, in *GiveFellowHouseReq, opts ...grpc.CallOption) (*GiveFellowHouseResp, error)
	// 获取用户的小屋列表
	GetUserFellowHouseList(ctx context.Context, in *GetUserFellowHouseListReq, opts ...grpc.CallOption) (*GetUserFellowHouseListResp, error)
	// 使用小屋
	SetUserFellowHouseInuse(ctx context.Context, in *SetUserFellowHouseInuseReq, opts ...grpc.CallOption) (*SetUserFellowHouseInuseResp, error)
	// 获取用户正在使用的小屋列表
	GetUserFellowHouseInuseList(ctx context.Context, in *GetUserFellowHouseInuseListReq, opts ...grpc.CallOption) (*GetUserFellowHouseInuseListResp, error)
	// 批量获取用户使用的小屋
	GetFellowHouseInuseListByCps(ctx context.Context, in *GetFellowHouseInuseListByCpsReq, opts ...grpc.CallOption) (*GetFellowHouseInuseListByCpsResp, error)
	// 购买小屋
	BuyFellowHouse(ctx context.Context, in *BuyFellowHouseReq, opts ...grpc.CallOption) (*BuyFellowHouseResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
}

type fellowHouseClient struct {
	cc *grpc.ClientConn
}

func NewFellowHouseClient(cc *grpc.ClientConn) FellowHouseClient {
	return &fellowHouseClient{cc}
}

func (c *fellowHouseClient) UpdateFellowHouseProduct(ctx context.Context, in *UpdateFellowHouseProductReq, opts ...grpc.CallOption) (*UpdateFellowHouseProductResp, error) {
	out := new(UpdateFellowHouseProductResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/UpdateFellowHouseProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) DeleteFellowHouseProduct(ctx context.Context, in *DeleteFellowHouseProductReq, opts ...grpc.CallOption) (*DeleteFellowHouseProductResp, error) {
	out := new(DeleteFellowHouseProductResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/DeleteFellowHouseProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) SearchFellowHouseProduct(ctx context.Context, in *SearchFellowHouseProductReq, opts ...grpc.CallOption) (*SearchFellowHouseProductResp, error) {
	out := new(SearchFellowHouseProductResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/SearchFellowHouseProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) GetFellowHouseProductList(ctx context.Context, in *GetFellowHouseProductListReq, opts ...grpc.CallOption) (*GetFellowHouseProductListResp, error) {
	out := new(GetFellowHouseProductListResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/GetFellowHouseProductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) GetFellowHouseProductById(ctx context.Context, in *GetFellowHouseProductByIdReq, opts ...grpc.CallOption) (*GetFellowHouseProductByIdResp, error) {
	out := new(GetFellowHouseProductByIdResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/GetFellowHouseProductById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) GetFellowHouseProductByIds(ctx context.Context, in *GetFellowHouseProductByIdsReq, opts ...grpc.CallOption) (*GetFellowHouseProductByIdsResp, error) {
	out := new(GetFellowHouseProductByIdsResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/GetFellowHouseProductByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) GiveFellowHouse(ctx context.Context, in *GiveFellowHouseReq, opts ...grpc.CallOption) (*GiveFellowHouseResp, error) {
	out := new(GiveFellowHouseResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/GiveFellowHouse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) GetUserFellowHouseList(ctx context.Context, in *GetUserFellowHouseListReq, opts ...grpc.CallOption) (*GetUserFellowHouseListResp, error) {
	out := new(GetUserFellowHouseListResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/GetUserFellowHouseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) SetUserFellowHouseInuse(ctx context.Context, in *SetUserFellowHouseInuseReq, opts ...grpc.CallOption) (*SetUserFellowHouseInuseResp, error) {
	out := new(SetUserFellowHouseInuseResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/SetUserFellowHouseInuse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) GetUserFellowHouseInuseList(ctx context.Context, in *GetUserFellowHouseInuseListReq, opts ...grpc.CallOption) (*GetUserFellowHouseInuseListResp, error) {
	out := new(GetUserFellowHouseInuseListResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/GetUserFellowHouseInuseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) GetFellowHouseInuseListByCps(ctx context.Context, in *GetFellowHouseInuseListByCpsReq, opts ...grpc.CallOption) (*GetFellowHouseInuseListByCpsResp, error) {
	out := new(GetFellowHouseInuseListByCpsResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/GetFellowHouseInuseListByCps", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) BuyFellowHouse(ctx context.Context, in *BuyFellowHouseReq, opts ...grpc.CallOption) (*BuyFellowHouseResp, error) {
	out := new(BuyFellowHouseResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/BuyFellowHouse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/GetConsumeTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowHouseClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/fellow_house.FellowHouse/GetConsumeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FellowHouseServer is the server API for FellowHouse service.
type FellowHouseServer interface {
	// 新增小屋配置
	UpdateFellowHouseProduct(context.Context, *UpdateFellowHouseProductReq) (*UpdateFellowHouseProductResp, error)
	// 批量删除小屋配置
	DeleteFellowHouseProduct(context.Context, *DeleteFellowHouseProductReq) (*DeleteFellowHouseProductResp, error)
	// 搜索小屋，支持ID\名称模糊搜索 （全量）
	SearchFellowHouseProduct(context.Context, *SearchFellowHouseProductReq) (*SearchFellowHouseProductResp, error)
	// 全量获取小屋配置
	GetFellowHouseProductList(context.Context, *GetFellowHouseProductListReq) (*GetFellowHouseProductListResp, error)
	// 根据ID获取小屋配置
	GetFellowHouseProductById(context.Context, *GetFellowHouseProductByIdReq) (*GetFellowHouseProductByIdResp, error)
	// 根据Id批量获取小屋配置
	GetFellowHouseProductByIds(context.Context, *GetFellowHouseProductByIdsReq) (*GetFellowHouseProductByIdsResp, error)
	// 发放小屋奖励
	GiveFellowHouse(context.Context, *GiveFellowHouseReq) (*GiveFellowHouseResp, error)
	// 获取用户的小屋列表
	GetUserFellowHouseList(context.Context, *GetUserFellowHouseListReq) (*GetUserFellowHouseListResp, error)
	// 使用小屋
	SetUserFellowHouseInuse(context.Context, *SetUserFellowHouseInuseReq) (*SetUserFellowHouseInuseResp, error)
	// 获取用户正在使用的小屋列表
	GetUserFellowHouseInuseList(context.Context, *GetUserFellowHouseInuseListReq) (*GetUserFellowHouseInuseListResp, error)
	// 批量获取用户使用的小屋
	GetFellowHouseInuseListByCps(context.Context, *GetFellowHouseInuseListByCpsReq) (*GetFellowHouseInuseListByCpsResp, error)
	// 购买小屋
	BuyFellowHouse(context.Context, *BuyFellowHouseReq) (*BuyFellowHouseResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
}

func RegisterFellowHouseServer(s *grpc.Server, srv FellowHouseServer) {
	s.RegisterService(&_FellowHouse_serviceDesc, srv)
}

func _FellowHouse_UpdateFellowHouseProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFellowHouseProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).UpdateFellowHouseProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/UpdateFellowHouseProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).UpdateFellowHouseProduct(ctx, req.(*UpdateFellowHouseProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_DeleteFellowHouseProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFellowHouseProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).DeleteFellowHouseProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/DeleteFellowHouseProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).DeleteFellowHouseProduct(ctx, req.(*DeleteFellowHouseProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_SearchFellowHouseProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchFellowHouseProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).SearchFellowHouseProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/SearchFellowHouseProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).SearchFellowHouseProduct(ctx, req.(*SearchFellowHouseProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_GetFellowHouseProductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowHouseProductListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).GetFellowHouseProductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/GetFellowHouseProductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).GetFellowHouseProductList(ctx, req.(*GetFellowHouseProductListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_GetFellowHouseProductById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowHouseProductByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).GetFellowHouseProductById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/GetFellowHouseProductById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).GetFellowHouseProductById(ctx, req.(*GetFellowHouseProductByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_GetFellowHouseProductByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowHouseProductByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).GetFellowHouseProductByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/GetFellowHouseProductByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).GetFellowHouseProductByIds(ctx, req.(*GetFellowHouseProductByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_GiveFellowHouse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveFellowHouseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).GiveFellowHouse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/GiveFellowHouse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).GiveFellowHouse(ctx, req.(*GiveFellowHouseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_GetUserFellowHouseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFellowHouseListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).GetUserFellowHouseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/GetUserFellowHouseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).GetUserFellowHouseList(ctx, req.(*GetUserFellowHouseListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_SetUserFellowHouseInuse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserFellowHouseInuseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).SetUserFellowHouseInuse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/SetUserFellowHouseInuse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).SetUserFellowHouseInuse(ctx, req.(*SetUserFellowHouseInuseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_GetUserFellowHouseInuseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFellowHouseInuseListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).GetUserFellowHouseInuseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/GetUserFellowHouseInuseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).GetUserFellowHouseInuseList(ctx, req.(*GetUserFellowHouseInuseListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_GetFellowHouseInuseListByCps_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowHouseInuseListByCpsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).GetFellowHouseInuseListByCps(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/GetFellowHouseInuseListByCps",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).GetFellowHouseInuseListByCps(ctx, req.(*GetFellowHouseInuseListByCpsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_BuyFellowHouse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyFellowHouseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).BuyFellowHouse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/BuyFellowHouse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).BuyFellowHouse(ctx, req.(*BuyFellowHouseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_GetConsumeTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).GetConsumeTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/GetConsumeTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).GetConsumeTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowHouse_GetConsumeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowHouseServer).GetConsumeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_house.FellowHouse/GetConsumeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowHouseServer).GetConsumeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _FellowHouse_serviceDesc = grpc.ServiceDesc{
	ServiceName: "fellow_house.FellowHouse",
	HandlerType: (*FellowHouseServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateFellowHouseProduct",
			Handler:    _FellowHouse_UpdateFellowHouseProduct_Handler,
		},
		{
			MethodName: "DeleteFellowHouseProduct",
			Handler:    _FellowHouse_DeleteFellowHouseProduct_Handler,
		},
		{
			MethodName: "SearchFellowHouseProduct",
			Handler:    _FellowHouse_SearchFellowHouseProduct_Handler,
		},
		{
			MethodName: "GetFellowHouseProductList",
			Handler:    _FellowHouse_GetFellowHouseProductList_Handler,
		},
		{
			MethodName: "GetFellowHouseProductById",
			Handler:    _FellowHouse_GetFellowHouseProductById_Handler,
		},
		{
			MethodName: "GetFellowHouseProductByIds",
			Handler:    _FellowHouse_GetFellowHouseProductByIds_Handler,
		},
		{
			MethodName: "GiveFellowHouse",
			Handler:    _FellowHouse_GiveFellowHouse_Handler,
		},
		{
			MethodName: "GetUserFellowHouseList",
			Handler:    _FellowHouse_GetUserFellowHouseList_Handler,
		},
		{
			MethodName: "SetUserFellowHouseInuse",
			Handler:    _FellowHouse_SetUserFellowHouseInuse_Handler,
		},
		{
			MethodName: "GetUserFellowHouseInuseList",
			Handler:    _FellowHouse_GetUserFellowHouseInuseList_Handler,
		},
		{
			MethodName: "GetFellowHouseInuseListByCps",
			Handler:    _FellowHouse_GetFellowHouseInuseListByCps_Handler,
		},
		{
			MethodName: "BuyFellowHouse",
			Handler:    _FellowHouse_BuyFellowHouse_Handler,
		},
		{
			MethodName: "GetConsumeTotalCount",
			Handler:    _FellowHouse_GetConsumeTotalCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderIds",
			Handler:    _FellowHouse_GetConsumeOrderIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "fellow-house/fellow-house.proto",
}

func init() {
	proto.RegisterFile("fellow-house/fellow-house.proto", fileDescriptor_fellow_house_c1f814c860cc53df)
}

var fileDescriptor_fellow_house_c1f814c860cc53df = []byte{
	// 1465 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xdd, 0x72, 0xda, 0xc6,
	0x17, 0x37, 0xc6, 0x7c, 0xf8, 0xd8, 0xd8, 0x64, 0x13, 0x27, 0x58, 0xc4, 0x1f, 0xd9, 0xff, 0x64,
	0xfe, 0x4e, 0x1a, 0xe3, 0x0e, 0xad, 0x9b, 0xa6, 0xd3, 0x1b, 0x43, 0x88, 0xc3, 0x84, 0x18, 0x8f,
	0x2c, 0x92, 0x3a, 0x37, 0x2a, 0x48, 0x0b, 0xd9, 0x09, 0x48, 0x8a, 0x56, 0x72, 0xca, 0x4c, 0x67,
	0x7a, 0xd1, 0xe9, 0x6d, 0x1f, 0xa2, 0x77, 0x7d, 0x8e, 0x3e, 0x47, 0x2f, 0xfa, 0x26, 0x9d, 0x5d,
	0x49, 0x46, 0x02, 0xa4, 0x90, 0x49, 0xef, 0xd8, 0x73, 0x7e, 0xe7, 0x53, 0xe7, 0xcb, 0x86, 0xbd,
	0x3e, 0x19, 0x0e, 0xcd, 0x0f, 0x87, 0x6f, 0x4d, 0x97, 0x91, 0xa3, 0xf0, 0xa3, 0x62, 0xd9, 0xa6,
	0x63, 0xa2, 0x75, 0x8f, 0xa6, 0x0a, 0x9a, 0xb4, 0x67, 0x13, 0xcd, 0x34, 0x34, 0x3a, 0x24, 0x87,
	0x57, 0xd5, 0xa3, 0xf0, 0xc3, 0x83, 0xe3, 0xbf, 0x52, 0xb0, 0xf1, 0x4c, 0x48, 0x3c, 0xe7, 0x02,
	0x32, 0x61, 0x68, 0x1b, 0xf2, 0x36, 0x61, 0xaa, 0x33, 0xb6, 0x48, 0x29, 0xb5, 0x9f, 0x3a, 0x28,
	0xc8, 0x39, 0x9b, 0x30, 0x65, 0x6c, 0x11, 0x54, 0x86, 0x55, 0xa1, 0x57, 0xb5, 0x09, 0x2b, 0x2d,
	0xef, 0xa7, 0x0e, 0x56, 0xe5, 0xfc, 0xdb, 0x40, 0x0e, 0x43, 0xe1, 0x9a, 0xa9, 0x8e, 0xf4, 0xe3,
	0x52, 0x5a, 0x00, 0xd6, 0x02, 0xc0, 0x4b, 0xfd, 0x18, 0x77, 0x20, 0x27, 0xfb, 0xba, 0xb6, 0x61,
	0xab, 0x71, 0xd6, 0x79, 0xa9, 0xca, 0x8d, 0x0b, 0x55, 0xb9, 0x3c, 0x6f, 0xa8, 0x9d, 0xb3, 0x17,
	0x67, 0xed, 0xd7, 0x67, 0xc5, 0x25, 0xb4, 0x05, 0x37, 0xa2, 0xac, 0x57, 0x27, 0xe7, 0xc5, 0x14,
	0x2a, 0xc1, 0xad, 0x28, 0xb9, 0xd5, 0x56, 0x94, 0x66, 0xa3, 0xb8, 0x8c, 0xff, 0x58, 0x8e, 0x44,
	0x51, 0xef, 0x0f, 0x78, 0x14, 0x9e, 0x37, 0x54, 0x0f, 0xa2, 0x10, 0xef, 0xa6, 0x8e, 0x10, 0xac,
	0x18, 0xdd, 0x11, 0xf1, 0x03, 0x10, 0xbf, 0xd1, 0x0e, 0x80, 0x07, 0xd7, 0x09, 0xd3, 0x7c, 0xcf,
	0xbd, 0x58, 0x9f, 0x12, 0xa6, 0x71, 0x11, 0xaa, 0x99, 0x46, 0x69, 0xc5, 0x13, 0xe1, 0xbf, 0xd1,
	0x93, 0x70, 0x32, 0x32, 0xfb, 0xa9, 0x83, 0xb5, 0xea, 0xdd, 0x4a, 0x38, 0xfb, 0x95, 0x68, 0x62,
	0x43, 0xa9, 0x7a, 0x00, 0xc5, 0x9e, 0x69, 0xb8, 0x4c, 0xb5, 0x88, 0xad, 0x11, 0xc3, 0xa1, 0x43,
	0x52, 0xca, 0x0a, 0x27, 0x37, 0x05, 0xfd, 0xfc, 0x9a, 0x8c, 0xee, 0xc1, 0xba, 0x69, 0xd3, 0x01,
	0x35, 0x54, 0xcb, 0xa6, 0x1a, 0x29, 0xe5, 0x04, 0x6c, 0xcd, 0xa3, 0x9d, 0x73, 0x12, 0xba, 0x0f,
	0x1b, 0x3a, 0x65, 0x9a, 0xe9, 0x1a, 0x8e, 0x0f, 0xca, 0x0b, 0x50, 0x21, 0xa0, 0x0a, 0x18, 0xfe,
	0x6d, 0x19, 0x50, 0xc8, 0xa3, 0x73, 0xdb, 0xd4, 0x5d, 0xcd, 0x99, 0x84, 0xa1, 0xf5, 0x07, 0x22,
	0x53, 0x49, 0x61, 0xd4, 0xfb, 0x03, 0x3f, 0x0c, 0x9e, 0x63, 0x09, 0xf2, 0xba, 0x6b, 0x77, 0x1d,
	0x6a, 0x1a, 0x22, 0x99, 0x05, 0xf9, 0xfa, 0xcd, 0x13, 0x6a, 0x77, 0x8d, 0x77, 0xaa, 0x69, 0xeb,
	0xc4, 0x16, 0x09, 0x2d, 0xc8, 0xab, 0x9c, 0xd2, 0xe6, 0x04, 0xf4, 0x10, 0x6e, 0xf8, 0x36, 0x78,
	0x9d, 0xa9, 0x43, 0x3a, 0xa2, 0x8e, 0xc8, 0x6e, 0x41, 0xde, 0xf4, 0x18, 0xbc, 0x48, 0x5a, 0x9c,
	0x8c, 0x1e, 0x01, 0xf2, 0xb1, 0x43, 0x72, 0x45, 0x86, 0x3e, 0x38, 0x23, 0xc0, 0x45, 0x8f, 0xd3,
	0xe2, 0x0c, 0x0f, 0xbd, 0x07, 0x6b, 0xae, 0xa5, 0x77, 0x1d, 0xa2, 0x3a, 0x74, 0xe4, 0xa5, 0x35,
	0x2d, 0x83, 0x47, 0x52, 0xe8, 0x88, 0xe0, 0x4b, 0x28, 0x77, 0xc4, 0x6b, 0x36, 0x19, 0x32, 0x79,
	0x8f, 0xbe, 0x83, 0x9c, 0xe5, 0xbd, 0xfc, 0x6c, 0xec, 0xc7, 0x66, 0x23, 0x90, 0x0a, 0x04, 0xf0,
	0x2e, 0xdc, 0x8d, 0x57, 0xcd, 0x2c, 0xfc, 0x0d, 0x94, 0x9f, 0x92, 0x21, 0x89, 0x33, 0x7d, 0x07,
	0x72, 0x54, 0x57, 0x87, 0x94, 0x71, 0xd3, 0xe9, 0x83, 0x82, 0x9c, 0xa5, 0x7a, 0x8b, 0x32, 0xa1,
	0x37, 0x5e, 0x8e, 0x59, 0xf8, 0x31, 0x94, 0x2f, 0x48, 0xd7, 0xd6, 0xde, 0xce, 0xd7, 0x5b, 0x82,
	0xdc, 0x3b, 0x32, 0xfe, 0x60, 0xda, 0x5e, 0x2b, 0xac, 0xca, 0xc1, 0x13, 0x6b, 0x70, 0x37, 0x5e,
	0x90, 0x59, 0xa8, 0x0e, 0xeb, 0x7e, 0x6c, 0x13, 0xb7, 0x16, 0xc9, 0xc8, 0x9a, 0x2f, 0x15, 0x78,
	0x7f, 0x4a, 0x9c, 0x59, 0x14, 0x67, 0xca, 0xe4, 0x3d, 0xd6, 0x61, 0x27, 0x81, 0xff, 0x5f, 0x79,
	0x71, 0x1c, 0xe3, 0x45, 0x6d, 0xdc, 0xd4, 0x79, 0x92, 0xb6, 0x20, 0xab, 0xf5, 0x07, 0x93, 0x71,
	0x91, 0xd1, 0xfa, 0x83, 0xa6, 0x8e, 0x2f, 0x62, 0x9c, 0xf3, 0xc4, 0x98, 0x85, 0xaa, 0x90, 0x9e,
	0x74, 0xce, 0xc7, 0x7d, 0xe2, 0x60, 0xfc, 0x67, 0x0a, 0xd0, 0x29, 0xbd, 0x22, 0x91, 0x01, 0xf1,
	0x3e, 0x69, 0x66, 0xdd, 0x84, 0x8c, 0x4b, 0x75, 0xb5, 0xeb, 0xf7, 0xd9, 0x8a, 0x4b, 0xf5, 0x93,
	0x80, 0xd8, 0xf3, 0xdb, 0x8b, 0x13, 0x6b, 0x91, 0xa6, 0xe4, 0x0d, 0x95, 0x09, 0x35, 0xe5, 0x36,
	0xe4, 0x45, 0x3f, 0x72, 0x03, 0x19, 0xaf, 0x12, 0xc4, 0xbb, 0xa9, 0xf3, 0xda, 0x63, 0xc4, 0xd0,
	0x55, 0x87, 0xf9, 0x2d, 0x93, 0xe5, 0x4f, 0x85, 0xe1, 0x2d, 0xb8, 0x39, 0xe3, 0x2a, 0xb3, 0xf0,
	0xdf, 0x29, 0xd8, 0x0c, 0xd1, 0x9a, 0x46, 0xdf, 0x44, 0x45, 0x48, 0xbb, 0xd7, 0xae, 0xf3, 0x9f,
	0x22, 0xa9, 0x96, 0xca, 0x89, 0xcb, 0x7e, 0x52, 0xad, 0x0e, 0xd5, 0xa3, 0x33, 0x27, 0xfd, 0x49,
	0x33, 0xa7, 0x0c, 0xab, 0xe4, 0x27, 0x8b, 0xda, 0x84, 0x7b, 0xba, 0x22, 0x3c, 0xcd, 0x7b, 0x04,
	0x85, 0x71, 0xa6, 0xd9, 0x73, 0xba, 0xd4, 0xe0, 0xcc, 0x8c, 0xc7, 0xf4, 0x08, 0x0a, 0xe3, 0xbe,
	0x50, 0x43, 0x75, 0x99, 0x37, 0x13, 0xf2, 0x72, 0x86, 0x1a, 0x1d, 0x46, 0x38, 0x99, 0x7b, 0xe2,
	0x30, 0x31, 0x5a, 0xd3, 0x72, 0xc6, 0x65, 0x44, 0x61, 0xf8, 0x1d, 0x6c, 0x9f, 0x12, 0xa7, 0xc3,
	0x88, 0x1d, 0x72, 0xc5, 0xaf, 0xd8, 0xc5, 0x03, 0xbd, 0x0f, 0x1b, 0x74, 0x60, 0x98, 0x36, 0x51,
	0x3d, 0x1f, 0x75, 0x11, 0x6d, 0x5e, 0x2e, 0x78, 0xd4, 0x86, 0x47, 0xc4, 0x6f, 0x40, 0x8a, 0x33,
	0xc6, 0x2c, 0xf4, 0x7d, 0xb0, 0x9b, 0x42, 0xc5, 0xbf, 0x13, 0x9b, 0x2e, 0xfe, 0x25, 0xfc, 0xd5,
	0x25, 0xea, 0xfe, 0x47, 0x90, 0x2e, 0x66, 0x74, 0x37, 0x0d, 0xbf, 0xe4, 0x16, 0x8e, 0x24, 0x5c,
	0x9b, 0xe9, 0x48, 0x6d, 0xe2, 0x1d, 0x3e, 0x7d, 0x62, 0x2c, 0x30, 0x0b, 0x57, 0x61, 0xf7, 0x74,
	0x3e, 0x3b, 0x36, 0x9d, 0x58, 0x85, 0xbd, 0x44, 0x99, 0xcf, 0xce, 0xca, 0x13, 0x28, 0x84, 0x4b,
	0xcc, 0x9a, 0x34, 0x58, 0x6a, 0x5e, 0x83, 0x4d, 0xba, 0xae, 0x86, 0x5f, 0x0b, 0xdf, 0xe6, 0xf9,
	0x55, 0x1b, 0xd7, 0x2d, 0xc6, 0x03, 0xfa, 0x1a, 0x72, 0x9a, 0x15, 0x76, 0xac, 0x1c, 0x5f, 0xdd,
	0x96, 0x9c, 0xd5, 0x2c, 0xff, 0x4b, 0xed, 0x27, 0x2b, 0xfe, 0xec, 0xa8, 0x7f, 0x4f, 0xc1, 0x8d,
	0x9a, 0x3b, 0x9e, 0x1a, 0x3b, 0xb3, 0x35, 0x10, 0xfe, 0xd8, 0xcb, 0xd1, 0x41, 0xb4, 0x03, 0xe0,
	0x5b, 0x73, 0xaf, 0x2b, 0x61, 0xd5, 0xa3, 0xf0, 0x32, 0xb9, 0x0d, 0xd9, 0xee, 0x88, 0xdf, 0x1c,
	0xfe, 0x32, 0xf7, 0x5f, 0xbc, 0xaa, 0x7a, 0xee, 0x78, 0xd2, 0x96, 0x99, 0x9e, 0x3b, 0x56, 0x18,
	0xae, 0x00, 0x9a, 0xf6, 0x87, 0x59, 0x7c, 0x5f, 0xf5, 0xba, 0xc3, 0xae, 0xa1, 0x5d, 0x1f, 0xa0,
	0xfe, 0x13, 0x7f, 0x9b, 0x30, 0x8d, 0x59, 0xe2, 0x0a, 0xbd, 0x14, 0x55, 0x18, 0x2b, 0xc9, 0x2c,
	0xf4, 0x18, 0xf2, 0x7c, 0x01, 0x84, 0x12, 0x9b, 0x3c, 0x93, 0x72, 0x5a, 0x7f, 0xc0, 0x55, 0x3f,
	0xfc, 0x27, 0x3a, 0x0a, 0xc5, 0x75, 0x8b, 0x61, 0x57, 0xdc, 0xaa, 0xcf, 0x1a, 0xad, 0x56, 0xfb,
	0xb5, 0xfa, 0xbc, 0xdd, 0xb9, 0x68, 0x4c, 0x9f, 0xb9, 0x3b, 0xb0, 0x1d, 0x83, 0xa9, 0xf3, 0x73,
	0x77, 0x17, 0xa4, 0x18, 0x76, 0x4d, 0x6e, 0x17, 0x97, 0x13, 0x4c, 0xb4, 0x4e, 0x9e, 0x5e, 0x72,
	0x4c, 0x1a, 0xfd, 0x0f, 0xf6, 0x62, 0x30, 0xcd, 0x33, 0xa5, 0xf9, 0xf2, 0x44, 0x69, 0x14, 0x57,
	0x12, 0x14, 0x9d, 0x9f, 0xc8, 0xca, 0x59, 0x43, 0x2e, 0x66, 0xaa, 0xbf, 0xae, 0xc3, 0x5a, 0x28,
	0x46, 0xc4, 0xa0, 0x14, 0x77, 0xe9, 0xa0, 0x07, 0xd1, 0xb4, 0x25, 0x1c, 0x5b, 0xd2, 0xc3, 0x45,
	0xa1, 0xcc, 0xc2, 0x4b, 0xdc, 0x68, 0xdc, 0x19, 0x34, 0x6d, 0x34, 0xe1, 0xcc, 0x9a, 0x36, 0x9a,
	0x78, 0x59, 0x09, 0xa3, 0x71, 0x27, 0xd2, 0xb4, 0xd1, 0x84, 0x1b, 0x6c, 0xda, 0x68, 0xd2, 0xd5,
	0x85, 0x97, 0xd0, 0x95, 0xd8, 0x3e, 0xf3, 0x4f, 0x22, 0x34, 0xa5, 0x2a, 0xe9, 0xb6, 0x92, 0xbe,
	0x58, 0x18, 0x9b, 0x68, 0x97, 0x77, 0xc9, 0x42, 0x76, 0xfd, 0x6b, 0x6a, 0x21, 0xbb, 0xc1, 0x09,
	0x85, 0x97, 0xd0, 0x58, 0x2c, 0xc0, 0x98, 0xee, 0x44, 0x8b, 0x2a, 0xe3, 0x13, 0x40, 0x7a, 0xb4,
	0x38, 0x58, 0x98, 0xfe, 0x01, 0x36, 0xa7, 0xee, 0x1b, 0x34, 0x75, 0xc5, 0xcd, 0x5e, 0x6a, 0xd2,
	0xbd, 0x8f, 0x20, 0x84, 0xe6, 0x11, 0xdc, 0x9e, 0xbf, 0xd5, 0xd1, 0xff, 0x67, 0x7c, 0x9c, 0x7f,
	0x68, 0x48, 0x07, 0x8b, 0x01, 0x85, 0x39, 0x0b, 0xee, 0xc4, 0xac, 0x61, 0x74, 0x30, 0x5d, 0x7c,
	0x71, 0xf7, 0x80, 0xf4, 0x60, 0x41, 0xa4, 0xb0, 0xf8, 0x33, 0x94, 0x13, 0xb6, 0x34, 0x7a, 0xf4,
	0x31, 0xe7, 0xc3, 0x47, 0x80, 0x74, 0xf8, 0x09, 0x68, 0x61, 0xfd, 0x97, 0xe9, 0x83, 0x3e, 0xba,
	0x2e, 0xd1, 0x61, 0x52, 0x21, 0xcc, 0xec, 0x6c, 0xa9, 0xf2, 0x29, 0x70, 0xe1, 0x40, 0x07, 0x36,
	0xa2, 0xcb, 0x0b, 0xed, 0x45, 0x75, 0xcc, 0xac, 0x5a, 0x69, 0x3f, 0x19, 0x20, 0xd4, 0xbe, 0x80,
	0x5b, 0xa7, 0xc4, 0xa9, 0x9b, 0x06, 0x73, 0x47, 0x44, 0x31, 0x9d, 0xee, 0xb0, 0x2e, 0x56, 0xe8,
	0x76, 0x45, 0x0e, 0xfe, 0x7f, 0xf3, 0xaa, 0x5a, 0xe1, 0x7f, 0xca, 0xca, 0x5d, 0x63, 0x20, 0xd4,
	0xde, 0x8e, 0xb0, 0x04, 0xdc, 0x57, 0xd6, 0x02, 0x34, 0x51, 0xd6, 0xf6, 0x6e, 0x7d, 0x96, 0xa4,
	0x2a, 0xca, 0x0a, 0x24, 0x3c, 0x6d, 0xb5, 0x2f, 0xdf, 0x54, 0x06, 0xe6, 0xb0, 0x6b, 0x0c, 0x2a,
	0xc7, 0x55, 0xc7, 0xa9, 0x68, 0xe6, 0xe8, 0x48, 0xfc, 0x1b, 0x49, 0x33, 0x87, 0x47, 0x8c, 0xd8,
	0x57, 0x54, 0x23, 0xec, 0x28, 0x1c, 0x61, 0x2f, 0x2b, 0xf8, 0x5f, 0xfd, 0x1b, 0x00, 0x00, 0xff,
	0xff, 0x07, 0xc2, 0x01, 0xe4, 0xb8, 0x12, 0x00, 0x00,
}
