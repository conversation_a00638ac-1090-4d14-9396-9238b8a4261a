// Code generated by protoc-gen-go. DO NOT EDIT.
// source: present-privilege/present-privilege.proto

package presentprivilege // import "golang.52tt.com/protocol/services/presentprivilege"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 条件类型
type ConditionType int32

const (
	ConditionType_NORMAL         ConditionType = 0
	ConditionType_RECHARGE       ConditionType = 1
	ConditionType_SEND_GIFT      ConditionType = 2
	ConditionType_NOBILITY_LEVEL ConditionType = 3
	ConditionType_RICH_LEVEL     ConditionType = 4
	ConditionType_CONSUMER       ConditionType = 5
)

var ConditionType_name = map[int32]string{
	0: "NORMAL",
	1: "RECHARGE",
	2: "SEND_GIFT",
	3: "NOBILITY_LEVEL",
	4: "RICH_LEVEL",
	5: "CONSUMER",
}
var ConditionType_value = map[string]int32{
	"NORMAL":         0,
	"RECHARGE":       1,
	"SEND_GIFT":      2,
	"NOBILITY_LEVEL": 3,
	"RICH_LEVEL":     4,
	"CONSUMER":       5,
}

func (x ConditionType) String() string {
	return proto.EnumName(ConditionType_name, int32(x))
}
func (ConditionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{0}
}

type GiftType int32

const (
	GiftType_GIFT_T_NORMAL GiftType = 0
	GiftType_GIFT_SPECIAL  GiftType = 1
)

var GiftType_name = map[int32]string{
	0: "GIFT_T_NORMAL",
	1: "GIFT_SPECIAL",
}
var GiftType_value = map[string]int32{
	"GIFT_T_NORMAL": 0,
	"GIFT_SPECIAL":  1,
}

func (x GiftType) String() string {
	return proto.EnumName(GiftType_name, int32(x))
}
func (GiftType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{1}
}

// 条件值
type ConditionValue struct {
	Value                uint64   `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	BeginTime            uint32   `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Seconds              uint32   `protobuf:"varint,5,opt,name=seconds,proto3" json:"seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConditionValue) Reset()         { *m = ConditionValue{} }
func (m *ConditionValue) String() string { return proto.CompactTextString(m) }
func (*ConditionValue) ProtoMessage()    {}
func (*ConditionValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{0}
}
func (m *ConditionValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConditionValue.Unmarshal(m, b)
}
func (m *ConditionValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConditionValue.Marshal(b, m, deterministic)
}
func (dst *ConditionValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConditionValue.Merge(dst, src)
}
func (m *ConditionValue) XXX_Size() int {
	return xxx_messageInfo_ConditionValue.Size(m)
}
func (m *ConditionValue) XXX_DiscardUnknown() {
	xxx_messageInfo_ConditionValue.DiscardUnknown(m)
}

var xxx_messageInfo_ConditionValue proto.InternalMessageInfo

func (m *ConditionValue) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *ConditionValue) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ConditionValue) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ConditionValue) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ConditionValue) GetSeconds() uint32 {
	if m != nil {
		return m.Seconds
	}
	return 0
}

type Condition struct {
	Type                 ConditionType   `protobuf:"varint,1,opt,name=type,proto3,enum=presentprivilege.ConditionType" json:"type,omitempty"`
	Value                *ConditionValue `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Condition) Reset()         { *m = Condition{} }
func (m *Condition) String() string { return proto.CompactTextString(m) }
func (*Condition) ProtoMessage()    {}
func (*Condition) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{1}
}
func (m *Condition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Condition.Unmarshal(m, b)
}
func (m *Condition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Condition.Marshal(b, m, deterministic)
}
func (dst *Condition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Condition.Merge(dst, src)
}
func (m *Condition) XXX_Size() int {
	return xxx_messageInfo_Condition.Size(m)
}
func (m *Condition) XXX_DiscardUnknown() {
	xxx_messageInfo_Condition.DiscardUnknown(m)
}

var xxx_messageInfo_Condition proto.InternalMessageInfo

func (m *Condition) GetType() ConditionType {
	if m != nil {
		return m.Type
	}
	return ConditionType_NORMAL
}

func (m *Condition) GetValue() *ConditionValue {
	if m != nil {
		return m.Value
	}
	return nil
}

// 后台礼物权限发放
type Privilege struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GiftId               uint32       `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Condition            []*Condition `protobuf:"bytes,3,rep,name=condition,proto3" json:"condition,omitempty"`
	UpdateTime           uint32       `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *Privilege) Reset()         { *m = Privilege{} }
func (m *Privilege) String() string { return proto.CompactTextString(m) }
func (*Privilege) ProtoMessage()    {}
func (*Privilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{2}
}
func (m *Privilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Privilege.Unmarshal(m, b)
}
func (m *Privilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Privilege.Marshal(b, m, deterministic)
}
func (dst *Privilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Privilege.Merge(dst, src)
}
func (m *Privilege) XXX_Size() int {
	return xxx_messageInfo_Privilege.Size(m)
}
func (m *Privilege) XXX_DiscardUnknown() {
	xxx_messageInfo_Privilege.DiscardUnknown(m)
}

var xxx_messageInfo_Privilege proto.InternalMessageInfo

func (m *Privilege) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Privilege) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *Privilege) GetCondition() []*Condition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (m *Privilege) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type AddPrivilegeReq struct {
	OrderId              string     `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UidList              []uint32   `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Privilege            *Privilege `protobuf:"bytes,3,opt,name=privilege,proto3" json:"privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AddPrivilegeReq) Reset()         { *m = AddPrivilegeReq{} }
func (m *AddPrivilegeReq) String() string { return proto.CompactTextString(m) }
func (*AddPrivilegeReq) ProtoMessage()    {}
func (*AddPrivilegeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{3}
}
func (m *AddPrivilegeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPrivilegeReq.Unmarshal(m, b)
}
func (m *AddPrivilegeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPrivilegeReq.Marshal(b, m, deterministic)
}
func (dst *AddPrivilegeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPrivilegeReq.Merge(dst, src)
}
func (m *AddPrivilegeReq) XXX_Size() int {
	return xxx_messageInfo_AddPrivilegeReq.Size(m)
}
func (m *AddPrivilegeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPrivilegeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPrivilegeReq proto.InternalMessageInfo

func (m *AddPrivilegeReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddPrivilegeReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *AddPrivilegeReq) GetPrivilege() *Privilege {
	if m != nil {
		return m.Privilege
	}
	return nil
}

type AddPrivilegeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPrivilegeResp) Reset()         { *m = AddPrivilegeResp{} }
func (m *AddPrivilegeResp) String() string { return proto.CompactTextString(m) }
func (*AddPrivilegeResp) ProtoMessage()    {}
func (*AddPrivilegeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{4}
}
func (m *AddPrivilegeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPrivilegeResp.Unmarshal(m, b)
}
func (m *AddPrivilegeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPrivilegeResp.Marshal(b, m, deterministic)
}
func (dst *AddPrivilegeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPrivilegeResp.Merge(dst, src)
}
func (m *AddPrivilegeResp) XXX_Size() int {
	return xxx_messageInfo_AddPrivilegeResp.Size(m)
}
func (m *AddPrivilegeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPrivilegeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPrivilegeResp proto.InternalMessageInfo

type CheckPrivilegeReq struct {
	Privilege            *Privilege `protobuf:"bytes,1,opt,name=privilege,proto3" json:"privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CheckPrivilegeReq) Reset()         { *m = CheckPrivilegeReq{} }
func (m *CheckPrivilegeReq) String() string { return proto.CompactTextString(m) }
func (*CheckPrivilegeReq) ProtoMessage()    {}
func (*CheckPrivilegeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{5}
}
func (m *CheckPrivilegeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPrivilegeReq.Unmarshal(m, b)
}
func (m *CheckPrivilegeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPrivilegeReq.Marshal(b, m, deterministic)
}
func (dst *CheckPrivilegeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPrivilegeReq.Merge(dst, src)
}
func (m *CheckPrivilegeReq) XXX_Size() int {
	return xxx_messageInfo_CheckPrivilegeReq.Size(m)
}
func (m *CheckPrivilegeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPrivilegeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPrivilegeReq proto.InternalMessageInfo

func (m *CheckPrivilegeReq) GetPrivilege() *Privilege {
	if m != nil {
		return m.Privilege
	}
	return nil
}

type CheckPrivilegeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckPrivilegeResp) Reset()         { *m = CheckPrivilegeResp{} }
func (m *CheckPrivilegeResp) String() string { return proto.CompactTextString(m) }
func (*CheckPrivilegeResp) ProtoMessage()    {}
func (*CheckPrivilegeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{6}
}
func (m *CheckPrivilegeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckPrivilegeResp.Unmarshal(m, b)
}
func (m *CheckPrivilegeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckPrivilegeResp.Marshal(b, m, deterministic)
}
func (dst *CheckPrivilegeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckPrivilegeResp.Merge(dst, src)
}
func (m *CheckPrivilegeResp) XXX_Size() int {
	return xxx_messageInfo_CheckPrivilegeResp.Size(m)
}
func (m *CheckPrivilegeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckPrivilegeResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckPrivilegeResp proto.InternalMessageInfo

// 取权限列表接口
type GetPrivilegeListReq struct {
	Off                  uint32   `protobuf:"varint,1,opt,name=off,proto3" json:"off,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrivilegeListReq) Reset()         { *m = GetPrivilegeListReq{} }
func (m *GetPrivilegeListReq) String() string { return proto.CompactTextString(m) }
func (*GetPrivilegeListReq) ProtoMessage()    {}
func (*GetPrivilegeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{7}
}
func (m *GetPrivilegeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrivilegeListReq.Unmarshal(m, b)
}
func (m *GetPrivilegeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrivilegeListReq.Marshal(b, m, deterministic)
}
func (dst *GetPrivilegeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrivilegeListReq.Merge(dst, src)
}
func (m *GetPrivilegeListReq) XXX_Size() int {
	return xxx_messageInfo_GetPrivilegeListReq.Size(m)
}
func (m *GetPrivilegeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrivilegeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrivilegeListReq proto.InternalMessageInfo

func (m *GetPrivilegeListReq) GetOff() uint32 {
	if m != nil {
		return m.Off
	}
	return 0
}

func (m *GetPrivilegeListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetPrivilegeListResp struct {
	PrivilegeList        []*Privilege `protobuf:"bytes,1,rep,name=privilege_list,json=privilegeList,proto3" json:"privilege_list,omitempty"`
	Total                uint32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPrivilegeListResp) Reset()         { *m = GetPrivilegeListResp{} }
func (m *GetPrivilegeListResp) String() string { return proto.CompactTextString(m) }
func (*GetPrivilegeListResp) ProtoMessage()    {}
func (*GetPrivilegeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{8}
}
func (m *GetPrivilegeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrivilegeListResp.Unmarshal(m, b)
}
func (m *GetPrivilegeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrivilegeListResp.Marshal(b, m, deterministic)
}
func (dst *GetPrivilegeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrivilegeListResp.Merge(dst, src)
}
func (m *GetPrivilegeListResp) XXX_Size() int {
	return xxx_messageInfo_GetPrivilegeListResp.Size(m)
}
func (m *GetPrivilegeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrivilegeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrivilegeListResp proto.InternalMessageInfo

func (m *GetPrivilegeListResp) GetPrivilegeList() []*Privilege {
	if m != nil {
		return m.PrivilegeList
	}
	return nil
}

func (m *GetPrivilegeListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 删除权限接口
type DelPrivilegeListReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPrivilegeListReq) Reset()         { *m = DelPrivilegeListReq{} }
func (m *DelPrivilegeListReq) String() string { return proto.CompactTextString(m) }
func (*DelPrivilegeListReq) ProtoMessage()    {}
func (*DelPrivilegeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{9}
}
func (m *DelPrivilegeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPrivilegeListReq.Unmarshal(m, b)
}
func (m *DelPrivilegeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPrivilegeListReq.Marshal(b, m, deterministic)
}
func (dst *DelPrivilegeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPrivilegeListReq.Merge(dst, src)
}
func (m *DelPrivilegeListReq) XXX_Size() int {
	return xxx_messageInfo_DelPrivilegeListReq.Size(m)
}
func (m *DelPrivilegeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPrivilegeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPrivilegeListReq proto.InternalMessageInfo

func (m *DelPrivilegeListReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelPrivilegeListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPrivilegeListResp) Reset()         { *m = DelPrivilegeListResp{} }
func (m *DelPrivilegeListResp) String() string { return proto.CompactTextString(m) }
func (*DelPrivilegeListResp) ProtoMessage()    {}
func (*DelPrivilegeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{10}
}
func (m *DelPrivilegeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPrivilegeListResp.Unmarshal(m, b)
}
func (m *DelPrivilegeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPrivilegeListResp.Marshal(b, m, deterministic)
}
func (dst *DelPrivilegeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPrivilegeListResp.Merge(dst, src)
}
func (m *DelPrivilegeListResp) XXX_Size() int {
	return xxx_messageInfo_DelPrivilegeListResp.Size(m)
}
func (m *DelPrivilegeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPrivilegeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPrivilegeListResp proto.InternalMessageInfo

// 礼物权限
type PresentPrivilege struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	BeginTime            uint32   `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,4,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Value                uint64   `protobuf:"varint,5,opt,name=value,proto3" json:"value,omitempty"`
	Days                 uint32   `protobuf:"varint,6,opt,name=days,proto3" json:"days,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentPrivilege) Reset()         { *m = PresentPrivilege{} }
func (m *PresentPrivilege) String() string { return proto.CompactTextString(m) }
func (*PresentPrivilege) ProtoMessage()    {}
func (*PresentPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{11}
}
func (m *PresentPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentPrivilege.Unmarshal(m, b)
}
func (m *PresentPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentPrivilege.Marshal(b, m, deterministic)
}
func (dst *PresentPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentPrivilege.Merge(dst, src)
}
func (m *PresentPrivilege) XXX_Size() int {
	return xxx_messageInfo_PresentPrivilege.Size(m)
}
func (m *PresentPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_PresentPrivilege proto.InternalMessageInfo

func (m *PresentPrivilege) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentPrivilege) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *PresentPrivilege) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *PresentPrivilege) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *PresentPrivilege) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *PresentPrivilege) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

// 去用户礼物权限列表
type GetPresentPrivilegeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentPrivilegeReq) Reset()         { *m = GetPresentPrivilegeReq{} }
func (m *GetPresentPrivilegeReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentPrivilegeReq) ProtoMessage()    {}
func (*GetPresentPrivilegeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{12}
}
func (m *GetPresentPrivilegeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentPrivilegeReq.Unmarshal(m, b)
}
func (m *GetPresentPrivilegeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentPrivilegeReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentPrivilegeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentPrivilegeReq.Merge(dst, src)
}
func (m *GetPresentPrivilegeReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentPrivilegeReq.Size(m)
}
func (m *GetPresentPrivilegeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentPrivilegeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentPrivilegeReq proto.InternalMessageInfo

func (m *GetPresentPrivilegeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPresentPrivilegeResp struct {
	PrivilegeList        []*PresentPrivilege `protobuf:"bytes,1,rep,name=privilege_list,json=privilegeList,proto3" json:"privilege_list,omitempty"`
	MapConditionValue    map[string]uint64   `protobuf:"bytes,2,rep,name=map_condition_value,json=mapConditionValue,proto3" json:"map_condition_value,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPresentPrivilegeResp) Reset()         { *m = GetPresentPrivilegeResp{} }
func (m *GetPresentPrivilegeResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentPrivilegeResp) ProtoMessage()    {}
func (*GetPresentPrivilegeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{13}
}
func (m *GetPresentPrivilegeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentPrivilegeResp.Unmarshal(m, b)
}
func (m *GetPresentPrivilegeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentPrivilegeResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentPrivilegeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentPrivilegeResp.Merge(dst, src)
}
func (m *GetPresentPrivilegeResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentPrivilegeResp.Size(m)
}
func (m *GetPresentPrivilegeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentPrivilegeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentPrivilegeResp proto.InternalMessageInfo

func (m *GetPresentPrivilegeResp) GetPrivilegeList() []*PresentPrivilege {
	if m != nil {
		return m.PrivilegeList
	}
	return nil
}

func (m *GetPresentPrivilegeResp) GetMapConditionValue() map[string]uint64 {
	if m != nil {
		return m.MapConditionValue
	}
	return nil
}

type UserChangeItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserChangeItem) Reset()         { *m = UserChangeItem{} }
func (m *UserChangeItem) String() string { return proto.CompactTextString(m) }
func (*UserChangeItem) ProtoMessage()    {}
func (*UserChangeItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{14}
}
func (m *UserChangeItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChangeItem.Unmarshal(m, b)
}
func (m *UserChangeItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChangeItem.Marshal(b, m, deterministic)
}
func (dst *UserChangeItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChangeItem.Merge(dst, src)
}
func (m *UserChangeItem) XXX_Size() int {
	return xxx_messageInfo_UserChangeItem.Size(m)
}
func (m *UserChangeItem) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChangeItem.DiscardUnknown(m)
}

var xxx_messageInfo_UserChangeItem proto.InternalMessageInfo

func (m *UserChangeItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserChangeItem) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *UserChangeItem) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type PresentPrivilegeChangeEvent struct {
	ChangeList           []*UserChangeItem `protobuf:"bytes,1,rep,name=change_list,json=changeList,proto3" json:"change_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PresentPrivilegeChangeEvent) Reset()         { *m = PresentPrivilegeChangeEvent{} }
func (m *PresentPrivilegeChangeEvent) String() string { return proto.CompactTextString(m) }
func (*PresentPrivilegeChangeEvent) ProtoMessage()    {}
func (*PresentPrivilegeChangeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{15}
}
func (m *PresentPrivilegeChangeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentPrivilegeChangeEvent.Unmarshal(m, b)
}
func (m *PresentPrivilegeChangeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentPrivilegeChangeEvent.Marshal(b, m, deterministic)
}
func (dst *PresentPrivilegeChangeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentPrivilegeChangeEvent.Merge(dst, src)
}
func (m *PresentPrivilegeChangeEvent) XXX_Size() int {
	return xxx_messageInfo_PresentPrivilegeChangeEvent.Size(m)
}
func (m *PresentPrivilegeChangeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentPrivilegeChangeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PresentPrivilegeChangeEvent proto.InternalMessageInfo

func (m *PresentPrivilegeChangeEvent) GetChangeList() []*UserChangeItem {
	if m != nil {
		return m.ChangeList
	}
	return nil
}

type TreasurePrivilege struct {
	GiftId               uint32                        `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	BeginTime            uint32                        `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32                        `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Condition            []*TreasurePrivilegeCondition `protobuf:"bytes,4,rep,name=condition,proto3" json:"condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *TreasurePrivilege) Reset()         { *m = TreasurePrivilege{} }
func (m *TreasurePrivilege) String() string { return proto.CompactTextString(m) }
func (*TreasurePrivilege) ProtoMessage()    {}
func (*TreasurePrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{16}
}
func (m *TreasurePrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TreasurePrivilege.Unmarshal(m, b)
}
func (m *TreasurePrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TreasurePrivilege.Marshal(b, m, deterministic)
}
func (dst *TreasurePrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TreasurePrivilege.Merge(dst, src)
}
func (m *TreasurePrivilege) XXX_Size() int {
	return xxx_messageInfo_TreasurePrivilege.Size(m)
}
func (m *TreasurePrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_TreasurePrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_TreasurePrivilege proto.InternalMessageInfo

func (m *TreasurePrivilege) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *TreasurePrivilege) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TreasurePrivilege) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TreasurePrivilege) GetCondition() []*TreasurePrivilegeCondition {
	if m != nil {
		return m.Condition
	}
	return nil
}

// 权限条件
type TreasurePrivilegeCondition struct {
	GiftId               uint32        `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	ConditionType        ConditionType `protobuf:"varint,2,opt,name=condition_type,json=conditionType,proto3,enum=presentprivilege.ConditionType" json:"condition_type,omitempty"`
	ConditionValue       uint32        `protobuf:"varint,3,opt,name=condition_value,json=conditionValue,proto3" json:"condition_value,omitempty"`
	ConditionTargetValue uint32        `protobuf:"varint,4,opt,name=condition_target_value,json=conditionTargetValue,proto3" json:"condition_target_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *TreasurePrivilegeCondition) Reset()         { *m = TreasurePrivilegeCondition{} }
func (m *TreasurePrivilegeCondition) String() string { return proto.CompactTextString(m) }
func (*TreasurePrivilegeCondition) ProtoMessage()    {}
func (*TreasurePrivilegeCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{17}
}
func (m *TreasurePrivilegeCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TreasurePrivilegeCondition.Unmarshal(m, b)
}
func (m *TreasurePrivilegeCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TreasurePrivilegeCondition.Marshal(b, m, deterministic)
}
func (dst *TreasurePrivilegeCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TreasurePrivilegeCondition.Merge(dst, src)
}
func (m *TreasurePrivilegeCondition) XXX_Size() int {
	return xxx_messageInfo_TreasurePrivilegeCondition.Size(m)
}
func (m *TreasurePrivilegeCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_TreasurePrivilegeCondition.DiscardUnknown(m)
}

var xxx_messageInfo_TreasurePrivilegeCondition proto.InternalMessageInfo

func (m *TreasurePrivilegeCondition) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *TreasurePrivilegeCondition) GetConditionType() ConditionType {
	if m != nil {
		return m.ConditionType
	}
	return ConditionType_NORMAL
}

func (m *TreasurePrivilegeCondition) GetConditionValue() uint32 {
	if m != nil {
		return m.ConditionValue
	}
	return 0
}

func (m *TreasurePrivilegeCondition) GetConditionTargetValue() uint32 {
	if m != nil {
		return m.ConditionTargetValue
	}
	return 0
}

type GetTreasurePrivilegeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTreasurePrivilegeReq) Reset()         { *m = GetTreasurePrivilegeReq{} }
func (m *GetTreasurePrivilegeReq) String() string { return proto.CompactTextString(m) }
func (*GetTreasurePrivilegeReq) ProtoMessage()    {}
func (*GetTreasurePrivilegeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{18}
}
func (m *GetTreasurePrivilegeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTreasurePrivilegeReq.Unmarshal(m, b)
}
func (m *GetTreasurePrivilegeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTreasurePrivilegeReq.Marshal(b, m, deterministic)
}
func (dst *GetTreasurePrivilegeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTreasurePrivilegeReq.Merge(dst, src)
}
func (m *GetTreasurePrivilegeReq) XXX_Size() int {
	return xxx_messageInfo_GetTreasurePrivilegeReq.Size(m)
}
func (m *GetTreasurePrivilegeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTreasurePrivilegeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTreasurePrivilegeReq proto.InternalMessageInfo

func (m *GetTreasurePrivilegeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetTreasurePrivilegeResp struct {
	TreasurePrivilege    []*TreasurePrivilege `protobuf:"bytes,1,rep,name=treasure_privilege,json=treasurePrivilege,proto3" json:"treasure_privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetTreasurePrivilegeResp) Reset()         { *m = GetTreasurePrivilegeResp{} }
func (m *GetTreasurePrivilegeResp) String() string { return proto.CompactTextString(m) }
func (*GetTreasurePrivilegeResp) ProtoMessage()    {}
func (*GetTreasurePrivilegeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{19}
}
func (m *GetTreasurePrivilegeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTreasurePrivilegeResp.Unmarshal(m, b)
}
func (m *GetTreasurePrivilegeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTreasurePrivilegeResp.Marshal(b, m, deterministic)
}
func (dst *GetTreasurePrivilegeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTreasurePrivilegeResp.Merge(dst, src)
}
func (m *GetTreasurePrivilegeResp) XXX_Size() int {
	return xxx_messageInfo_GetTreasurePrivilegeResp.Size(m)
}
func (m *GetTreasurePrivilegeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTreasurePrivilegeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTreasurePrivilegeResp proto.InternalMessageInfo

func (m *GetTreasurePrivilegeResp) GetTreasurePrivilege() []*TreasurePrivilege {
	if m != nil {
		return m.TreasurePrivilege
	}
	return nil
}

// 条件值
type TreasureConditionValue struct {
	Value                uint64   `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PrivilegeSeconds     uint32   `protobuf:"varint,4,opt,name=privilege_seconds,json=privilegeSeconds,proto3" json:"privilege_seconds,omitempty"`
	PrivilegeEnd         uint32   `protobuf:"varint,5,opt,name=privilege_end,json=privilegeEnd,proto3" json:"privilege_end,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,6,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TreasureConditionValue) Reset()         { *m = TreasureConditionValue{} }
func (m *TreasureConditionValue) String() string { return proto.CompactTextString(m) }
func (*TreasureConditionValue) ProtoMessage()    {}
func (*TreasureConditionValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{20}
}
func (m *TreasureConditionValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TreasureConditionValue.Unmarshal(m, b)
}
func (m *TreasureConditionValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TreasureConditionValue.Marshal(b, m, deterministic)
}
func (dst *TreasureConditionValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TreasureConditionValue.Merge(dst, src)
}
func (m *TreasureConditionValue) XXX_Size() int {
	return xxx_messageInfo_TreasureConditionValue.Size(m)
}
func (m *TreasureConditionValue) XXX_DiscardUnknown() {
	xxx_messageInfo_TreasureConditionValue.DiscardUnknown(m)
}

var xxx_messageInfo_TreasureConditionValue proto.InternalMessageInfo

func (m *TreasureConditionValue) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *TreasureConditionValue) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TreasureConditionValue) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TreasureConditionValue) GetPrivilegeSeconds() uint32 {
	if m != nil {
		return m.PrivilegeSeconds
	}
	return 0
}

func (m *TreasureConditionValue) GetPrivilegeEnd() uint32 {
	if m != nil {
		return m.PrivilegeEnd
	}
	return 0
}

func (m *TreasureConditionValue) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type TreasureCondition struct {
	Type                 ConditionType           `protobuf:"varint,1,opt,name=type,proto3,enum=presentprivilege.ConditionType" json:"type,omitempty"`
	Value                *TreasureConditionValue `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *TreasureCondition) Reset()         { *m = TreasureCondition{} }
func (m *TreasureCondition) String() string { return proto.CompactTextString(m) }
func (*TreasureCondition) ProtoMessage()    {}
func (*TreasureCondition) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{21}
}
func (m *TreasureCondition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TreasureCondition.Unmarshal(m, b)
}
func (m *TreasureCondition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TreasureCondition.Marshal(b, m, deterministic)
}
func (dst *TreasureCondition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TreasureCondition.Merge(dst, src)
}
func (m *TreasureCondition) XXX_Size() int {
	return xxx_messageInfo_TreasureCondition.Size(m)
}
func (m *TreasureCondition) XXX_DiscardUnknown() {
	xxx_messageInfo_TreasureCondition.DiscardUnknown(m)
}

var xxx_messageInfo_TreasureCondition proto.InternalMessageInfo

func (m *TreasureCondition) GetType() ConditionType {
	if m != nil {
		return m.Type
	}
	return ConditionType_NORMAL
}

func (m *TreasureCondition) GetValue() *TreasureConditionValue {
	if m != nil {
		return m.Value
	}
	return nil
}

// 后台礼物权限发放
type TreasurePrivilegeConfig struct {
	Id                   uint32               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GiftId               uint32               `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Condition            []*TreasureCondition `protobuf:"bytes,3,rep,name=condition,proto3" json:"condition,omitempty"`
	UpdateTime           uint32               `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *TreasurePrivilegeConfig) Reset()         { *m = TreasurePrivilegeConfig{} }
func (m *TreasurePrivilegeConfig) String() string { return proto.CompactTextString(m) }
func (*TreasurePrivilegeConfig) ProtoMessage()    {}
func (*TreasurePrivilegeConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{22}
}
func (m *TreasurePrivilegeConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TreasurePrivilegeConfig.Unmarshal(m, b)
}
func (m *TreasurePrivilegeConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TreasurePrivilegeConfig.Marshal(b, m, deterministic)
}
func (dst *TreasurePrivilegeConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TreasurePrivilegeConfig.Merge(dst, src)
}
func (m *TreasurePrivilegeConfig) XXX_Size() int {
	return xxx_messageInfo_TreasurePrivilegeConfig.Size(m)
}
func (m *TreasurePrivilegeConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_TreasurePrivilegeConfig.DiscardUnknown(m)
}

var xxx_messageInfo_TreasurePrivilegeConfig proto.InternalMessageInfo

func (m *TreasurePrivilegeConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TreasurePrivilegeConfig) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *TreasurePrivilegeConfig) GetCondition() []*TreasureCondition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (m *TreasurePrivilegeConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type AddTreasurePrivilegeReq struct {
	OrderId              string                   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UidList              []uint32                 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Privilege            *TreasurePrivilegeConfig `protobuf:"bytes,3,opt,name=privilege,proto3" json:"privilege,omitempty"`
	EndTime              uint32                   `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	EndTimeRel           uint32                   `protobuf:"varint,5,opt,name=end_time_rel,json=endTimeRel,proto3" json:"end_time_rel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AddTreasurePrivilegeReq) Reset()         { *m = AddTreasurePrivilegeReq{} }
func (m *AddTreasurePrivilegeReq) String() string { return proto.CompactTextString(m) }
func (*AddTreasurePrivilegeReq) ProtoMessage()    {}
func (*AddTreasurePrivilegeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{23}
}
func (m *AddTreasurePrivilegeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTreasurePrivilegeReq.Unmarshal(m, b)
}
func (m *AddTreasurePrivilegeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTreasurePrivilegeReq.Marshal(b, m, deterministic)
}
func (dst *AddTreasurePrivilegeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTreasurePrivilegeReq.Merge(dst, src)
}
func (m *AddTreasurePrivilegeReq) XXX_Size() int {
	return xxx_messageInfo_AddTreasurePrivilegeReq.Size(m)
}
func (m *AddTreasurePrivilegeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTreasurePrivilegeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTreasurePrivilegeReq proto.InternalMessageInfo

func (m *AddTreasurePrivilegeReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddTreasurePrivilegeReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *AddTreasurePrivilegeReq) GetPrivilege() *TreasurePrivilegeConfig {
	if m != nil {
		return m.Privilege
	}
	return nil
}

func (m *AddTreasurePrivilegeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *AddTreasurePrivilegeReq) GetEndTimeRel() uint32 {
	if m != nil {
		return m.EndTimeRel
	}
	return 0
}

type AddTreasurePrivilegeResp struct {
	Privilege            []*TreasureEnd `protobuf:"bytes,1,rep,name=privilege,proto3" json:"privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AddTreasurePrivilegeResp) Reset()         { *m = AddTreasurePrivilegeResp{} }
func (m *AddTreasurePrivilegeResp) String() string { return proto.CompactTextString(m) }
func (*AddTreasurePrivilegeResp) ProtoMessage()    {}
func (*AddTreasurePrivilegeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{24}
}
func (m *AddTreasurePrivilegeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTreasurePrivilegeResp.Unmarshal(m, b)
}
func (m *AddTreasurePrivilegeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTreasurePrivilegeResp.Marshal(b, m, deterministic)
}
func (dst *AddTreasurePrivilegeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTreasurePrivilegeResp.Merge(dst, src)
}
func (m *AddTreasurePrivilegeResp) XXX_Size() int {
	return xxx_messageInfo_AddTreasurePrivilegeResp.Size(m)
}
func (m *AddTreasurePrivilegeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTreasurePrivilegeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTreasurePrivilegeResp proto.InternalMessageInfo

func (m *AddTreasurePrivilegeResp) GetPrivilege() []*TreasureEnd {
	if m != nil {
		return m.Privilege
	}
	return nil
}

type TreasureEnd struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TreasureEnd) Reset()         { *m = TreasureEnd{} }
func (m *TreasureEnd) String() string { return proto.CompactTextString(m) }
func (*TreasureEnd) ProtoMessage()    {}
func (*TreasureEnd) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{25}
}
func (m *TreasureEnd) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TreasureEnd.Unmarshal(m, b)
}
func (m *TreasureEnd) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TreasureEnd.Marshal(b, m, deterministic)
}
func (dst *TreasureEnd) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TreasureEnd.Merge(dst, src)
}
func (m *TreasureEnd) XXX_Size() int {
	return xxx_messageInfo_TreasureEnd.Size(m)
}
func (m *TreasureEnd) XXX_DiscardUnknown() {
	xxx_messageInfo_TreasureEnd.DiscardUnknown(m)
}

var xxx_messageInfo_TreasureEnd proto.InternalMessageInfo

func (m *TreasureEnd) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TreasureEnd) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *TreasureEnd) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 取权限列表接口
type GetTreasurePrivilegeListReq struct {
	Off                  uint32   `protobuf:"varint,1,opt,name=off,proto3" json:"off,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTreasurePrivilegeListReq) Reset()         { *m = GetTreasurePrivilegeListReq{} }
func (m *GetTreasurePrivilegeListReq) String() string { return proto.CompactTextString(m) }
func (*GetTreasurePrivilegeListReq) ProtoMessage()    {}
func (*GetTreasurePrivilegeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{26}
}
func (m *GetTreasurePrivilegeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTreasurePrivilegeListReq.Unmarshal(m, b)
}
func (m *GetTreasurePrivilegeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTreasurePrivilegeListReq.Marshal(b, m, deterministic)
}
func (dst *GetTreasurePrivilegeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTreasurePrivilegeListReq.Merge(dst, src)
}
func (m *GetTreasurePrivilegeListReq) XXX_Size() int {
	return xxx_messageInfo_GetTreasurePrivilegeListReq.Size(m)
}
func (m *GetTreasurePrivilegeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTreasurePrivilegeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTreasurePrivilegeListReq proto.InternalMessageInfo

func (m *GetTreasurePrivilegeListReq) GetOff() uint32 {
	if m != nil {
		return m.Off
	}
	return 0
}

func (m *GetTreasurePrivilegeListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetTreasurePrivilegeListResp struct {
	PrivilegeList        []*TreasurePrivilegeConfig `protobuf:"bytes,1,rep,name=privilege_list,json=privilegeList,proto3" json:"privilege_list,omitempty"`
	Total                uint32                     `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetTreasurePrivilegeListResp) Reset()         { *m = GetTreasurePrivilegeListResp{} }
func (m *GetTreasurePrivilegeListResp) String() string { return proto.CompactTextString(m) }
func (*GetTreasurePrivilegeListResp) ProtoMessage()    {}
func (*GetTreasurePrivilegeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{27}
}
func (m *GetTreasurePrivilegeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTreasurePrivilegeListResp.Unmarshal(m, b)
}
func (m *GetTreasurePrivilegeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTreasurePrivilegeListResp.Marshal(b, m, deterministic)
}
func (dst *GetTreasurePrivilegeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTreasurePrivilegeListResp.Merge(dst, src)
}
func (m *GetTreasurePrivilegeListResp) XXX_Size() int {
	return xxx_messageInfo_GetTreasurePrivilegeListResp.Size(m)
}
func (m *GetTreasurePrivilegeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTreasurePrivilegeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTreasurePrivilegeListResp proto.InternalMessageInfo

func (m *GetTreasurePrivilegeListResp) GetPrivilegeList() []*TreasurePrivilegeConfig {
	if m != nil {
		return m.PrivilegeList
	}
	return nil
}

func (m *GetTreasurePrivilegeListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 删除权限发放配置接口
type DelTreasurePrivilegeListReq struct {
	Id                   []uint32 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTreasurePrivilegeListReq) Reset()         { *m = DelTreasurePrivilegeListReq{} }
func (m *DelTreasurePrivilegeListReq) String() string { return proto.CompactTextString(m) }
func (*DelTreasurePrivilegeListReq) ProtoMessage()    {}
func (*DelTreasurePrivilegeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{28}
}
func (m *DelTreasurePrivilegeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTreasurePrivilegeListReq.Unmarshal(m, b)
}
func (m *DelTreasurePrivilegeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTreasurePrivilegeListReq.Marshal(b, m, deterministic)
}
func (dst *DelTreasurePrivilegeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTreasurePrivilegeListReq.Merge(dst, src)
}
func (m *DelTreasurePrivilegeListReq) XXX_Size() int {
	return xxx_messageInfo_DelTreasurePrivilegeListReq.Size(m)
}
func (m *DelTreasurePrivilegeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTreasurePrivilegeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTreasurePrivilegeListReq proto.InternalMessageInfo

func (m *DelTreasurePrivilegeListReq) GetId() []uint32 {
	if m != nil {
		return m.Id
	}
	return nil
}

type DelTreasurePrivilegeListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTreasurePrivilegeListResp) Reset()         { *m = DelTreasurePrivilegeListResp{} }
func (m *DelTreasurePrivilegeListResp) String() string { return proto.CompactTextString(m) }
func (*DelTreasurePrivilegeListResp) ProtoMessage()    {}
func (*DelTreasurePrivilegeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{29}
}
func (m *DelTreasurePrivilegeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTreasurePrivilegeListResp.Unmarshal(m, b)
}
func (m *DelTreasurePrivilegeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTreasurePrivilegeListResp.Marshal(b, m, deterministic)
}
func (dst *DelTreasurePrivilegeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTreasurePrivilegeListResp.Merge(dst, src)
}
func (m *DelTreasurePrivilegeListResp) XXX_Size() int {
	return xxx_messageInfo_DelTreasurePrivilegeListResp.Size(m)
}
func (m *DelTreasurePrivilegeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTreasurePrivilegeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTreasurePrivilegeListResp proto.InternalMessageInfo

type CheckTreasurePrivilegeReq struct {
	Privilege            *TreasurePrivilegeConfig `protobuf:"bytes,1,opt,name=privilege,proto3" json:"privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *CheckTreasurePrivilegeReq) Reset()         { *m = CheckTreasurePrivilegeReq{} }
func (m *CheckTreasurePrivilegeReq) String() string { return proto.CompactTextString(m) }
func (*CheckTreasurePrivilegeReq) ProtoMessage()    {}
func (*CheckTreasurePrivilegeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{30}
}
func (m *CheckTreasurePrivilegeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTreasurePrivilegeReq.Unmarshal(m, b)
}
func (m *CheckTreasurePrivilegeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTreasurePrivilegeReq.Marshal(b, m, deterministic)
}
func (dst *CheckTreasurePrivilegeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTreasurePrivilegeReq.Merge(dst, src)
}
func (m *CheckTreasurePrivilegeReq) XXX_Size() int {
	return xxx_messageInfo_CheckTreasurePrivilegeReq.Size(m)
}
func (m *CheckTreasurePrivilegeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTreasurePrivilegeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTreasurePrivilegeReq proto.InternalMessageInfo

func (m *CheckTreasurePrivilegeReq) GetPrivilege() *TreasurePrivilegeConfig {
	if m != nil {
		return m.Privilege
	}
	return nil
}

type CheckTreasurePrivilegeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTreasurePrivilegeResp) Reset()         { *m = CheckTreasurePrivilegeResp{} }
func (m *CheckTreasurePrivilegeResp) String() string { return proto.CompactTextString(m) }
func (*CheckTreasurePrivilegeResp) ProtoMessage()    {}
func (*CheckTreasurePrivilegeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{31}
}
func (m *CheckTreasurePrivilegeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTreasurePrivilegeResp.Unmarshal(m, b)
}
func (m *CheckTreasurePrivilegeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTreasurePrivilegeResp.Marshal(b, m, deterministic)
}
func (dst *CheckTreasurePrivilegeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTreasurePrivilegeResp.Merge(dst, src)
}
func (m *CheckTreasurePrivilegeResp) XXX_Size() int {
	return xxx_messageInfo_CheckTreasurePrivilegeResp.Size(m)
}
func (m *CheckTreasurePrivilegeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTreasurePrivilegeResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTreasurePrivilegeResp proto.InternalMessageInfo

type GetConditionValReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TimeRange            []*TimeRange `protobuf:"bytes,2,rep,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConditionValReq) Reset()         { *m = GetConditionValReq{} }
func (m *GetConditionValReq) String() string { return proto.CompactTextString(m) }
func (*GetConditionValReq) ProtoMessage()    {}
func (*GetConditionValReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{32}
}
func (m *GetConditionValReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConditionValReq.Unmarshal(m, b)
}
func (m *GetConditionValReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConditionValReq.Marshal(b, m, deterministic)
}
func (dst *GetConditionValReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConditionValReq.Merge(dst, src)
}
func (m *GetConditionValReq) XXX_Size() int {
	return xxx_messageInfo_GetConditionValReq.Size(m)
}
func (m *GetConditionValReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConditionValReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConditionValReq proto.InternalMessageInfo

func (m *GetConditionValReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetConditionValReq) GetTimeRange() []*TimeRange {
	if m != nil {
		return m.TimeRange
	}
	return nil
}

type TimeRange struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeRange) Reset()         { *m = TimeRange{} }
func (m *TimeRange) String() string { return proto.CompactTextString(m) }
func (*TimeRange) ProtoMessage()    {}
func (*TimeRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{33}
}
func (m *TimeRange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeRange.Unmarshal(m, b)
}
func (m *TimeRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeRange.Marshal(b, m, deterministic)
}
func (dst *TimeRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeRange.Merge(dst, src)
}
func (m *TimeRange) XXX_Size() int {
	return xxx_messageInfo_TimeRange.Size(m)
}
func (m *TimeRange) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeRange.DiscardUnknown(m)
}

var xxx_messageInfo_TimeRange proto.InternalMessageInfo

func (m *TimeRange) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TimeRange) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetConditionValResp struct {
	TimeValue            []*TimeValue `protobuf:"bytes,1,rep,name=time_value,json=timeValue,proto3" json:"time_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConditionValResp) Reset()         { *m = GetConditionValResp{} }
func (m *GetConditionValResp) String() string { return proto.CompactTextString(m) }
func (*GetConditionValResp) ProtoMessage()    {}
func (*GetConditionValResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{34}
}
func (m *GetConditionValResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConditionValResp.Unmarshal(m, b)
}
func (m *GetConditionValResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConditionValResp.Marshal(b, m, deterministic)
}
func (dst *GetConditionValResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConditionValResp.Merge(dst, src)
}
func (m *GetConditionValResp) XXX_Size() int {
	return xxx_messageInfo_GetConditionValResp.Size(m)
}
func (m *GetConditionValResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConditionValResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConditionValResp proto.InternalMessageInfo

func (m *GetConditionValResp) GetTimeValue() []*TimeValue {
	if m != nil {
		return m.TimeValue
	}
	return nil
}

type TimeValue struct {
	BeginTime            uint32          `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32          `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ConditionVal         []*ConditionVal `protobuf:"bytes,3,rep,name=condition_val,json=conditionVal,proto3" json:"condition_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *TimeValue) Reset()         { *m = TimeValue{} }
func (m *TimeValue) String() string { return proto.CompactTextString(m) }
func (*TimeValue) ProtoMessage()    {}
func (*TimeValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{35}
}
func (m *TimeValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeValue.Unmarshal(m, b)
}
func (m *TimeValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeValue.Marshal(b, m, deterministic)
}
func (dst *TimeValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeValue.Merge(dst, src)
}
func (m *TimeValue) XXX_Size() int {
	return xxx_messageInfo_TimeValue.Size(m)
}
func (m *TimeValue) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeValue.DiscardUnknown(m)
}

var xxx_messageInfo_TimeValue proto.InternalMessageInfo

func (m *TimeValue) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TimeValue) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TimeValue) GetConditionVal() []*ConditionVal {
	if m != nil {
		return m.ConditionVal
	}
	return nil
}

type ConditionVal struct {
	Typ                  ConditionType `protobuf:"varint,1,opt,name=typ,proto3,enum=presentprivilege.ConditionType" json:"typ,omitempty"`
	Val                  uint64        `protobuf:"varint,2,opt,name=val,proto3" json:"val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ConditionVal) Reset()         { *m = ConditionVal{} }
func (m *ConditionVal) String() string { return proto.CompactTextString(m) }
func (*ConditionVal) ProtoMessage()    {}
func (*ConditionVal) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{36}
}
func (m *ConditionVal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConditionVal.Unmarshal(m, b)
}
func (m *ConditionVal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConditionVal.Marshal(b, m, deterministic)
}
func (dst *ConditionVal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConditionVal.Merge(dst, src)
}
func (m *ConditionVal) XXX_Size() int {
	return xxx_messageInfo_ConditionVal.Size(m)
}
func (m *ConditionVal) XXX_DiscardUnknown() {
	xxx_messageInfo_ConditionVal.DiscardUnknown(m)
}

var xxx_messageInfo_ConditionVal proto.InternalMessageInfo

func (m *ConditionVal) GetTyp() ConditionType {
	if m != nil {
		return m.Typ
	}
	return ConditionType_NORMAL
}

func (m *ConditionVal) GetVal() uint64 {
	if m != nil {
		return m.Val
	}
	return 0
}

type GetTreasurePrivilegeHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTreasurePrivilegeHistoryReq) Reset()         { *m = GetTreasurePrivilegeHistoryReq{} }
func (m *GetTreasurePrivilegeHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetTreasurePrivilegeHistoryReq) ProtoMessage()    {}
func (*GetTreasurePrivilegeHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{37}
}
func (m *GetTreasurePrivilegeHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryReq.Unmarshal(m, b)
}
func (m *GetTreasurePrivilegeHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetTreasurePrivilegeHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTreasurePrivilegeHistoryReq.Merge(dst, src)
}
func (m *GetTreasurePrivilegeHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryReq.Size(m)
}
func (m *GetTreasurePrivilegeHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTreasurePrivilegeHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTreasurePrivilegeHistoryReq proto.InternalMessageInfo

func (m *GetTreasurePrivilegeHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetTreasurePrivilegeHistoryResp struct {
	HistoryList          []*TreasurePrivilegeHistory `protobuf:"bytes,1,rep,name=history_list,json=historyList,proto3" json:"history_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetTreasurePrivilegeHistoryResp) Reset()         { *m = GetTreasurePrivilegeHistoryResp{} }
func (m *GetTreasurePrivilegeHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetTreasurePrivilegeHistoryResp) ProtoMessage()    {}
func (*GetTreasurePrivilegeHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{38}
}
func (m *GetTreasurePrivilegeHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryResp.Unmarshal(m, b)
}
func (m *GetTreasurePrivilegeHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetTreasurePrivilegeHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTreasurePrivilegeHistoryResp.Merge(dst, src)
}
func (m *GetTreasurePrivilegeHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetTreasurePrivilegeHistoryResp.Size(m)
}
func (m *GetTreasurePrivilegeHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTreasurePrivilegeHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTreasurePrivilegeHistoryResp proto.InternalMessageInfo

func (m *GetTreasurePrivilegeHistoryResp) GetHistoryList() []*TreasurePrivilegeHistory {
	if m != nil {
		return m.HistoryList
	}
	return nil
}

type TreasurePrivilegeHistory struct {
	GiftIcon             string   `protobuf:"bytes,1,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,3,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	GainTime             uint32   `protobuf:"varint,4,opt,name=gain_time,json=gainTime,proto3" json:"gain_time,omitempty"`
	FinTime              uint32   `protobuf:"varint,5,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	FinSeconds           uint32   `protobuf:"varint,6,opt,name=fin_seconds,json=finSeconds,proto3" json:"fin_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TreasurePrivilegeHistory) Reset()         { *m = TreasurePrivilegeHistory{} }
func (m *TreasurePrivilegeHistory) String() string { return proto.CompactTextString(m) }
func (*TreasurePrivilegeHistory) ProtoMessage()    {}
func (*TreasurePrivilegeHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{39}
}
func (m *TreasurePrivilegeHistory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TreasurePrivilegeHistory.Unmarshal(m, b)
}
func (m *TreasurePrivilegeHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TreasurePrivilegeHistory.Marshal(b, m, deterministic)
}
func (dst *TreasurePrivilegeHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TreasurePrivilegeHistory.Merge(dst, src)
}
func (m *TreasurePrivilegeHistory) XXX_Size() int {
	return xxx_messageInfo_TreasurePrivilegeHistory.Size(m)
}
func (m *TreasurePrivilegeHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_TreasurePrivilegeHistory.DiscardUnknown(m)
}

var xxx_messageInfo_TreasurePrivilegeHistory proto.InternalMessageInfo

func (m *TreasurePrivilegeHistory) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *TreasurePrivilegeHistory) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *TreasurePrivilegeHistory) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *TreasurePrivilegeHistory) GetGainTime() uint32 {
	if m != nil {
		return m.GainTime
	}
	return 0
}

func (m *TreasurePrivilegeHistory) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *TreasurePrivilegeHistory) GetFinSeconds() uint32 {
	if m != nil {
		return m.FinSeconds
	}
	return 0
}

type AddConditionValForTestReq struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Typ                  ConditionType `protobuf:"varint,2,opt,name=typ,proto3,enum=presentprivilege.ConditionType" json:"typ,omitempty"`
	Val                  uint32        `protobuf:"varint,3,opt,name=val,proto3" json:"val,omitempty"`
	Time                 uint32        `protobuf:"varint,4,opt,name=time,proto3" json:"time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddConditionValForTestReq) Reset()         { *m = AddConditionValForTestReq{} }
func (m *AddConditionValForTestReq) String() string { return proto.CompactTextString(m) }
func (*AddConditionValForTestReq) ProtoMessage()    {}
func (*AddConditionValForTestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{40}
}
func (m *AddConditionValForTestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddConditionValForTestReq.Unmarshal(m, b)
}
func (m *AddConditionValForTestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddConditionValForTestReq.Marshal(b, m, deterministic)
}
func (dst *AddConditionValForTestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddConditionValForTestReq.Merge(dst, src)
}
func (m *AddConditionValForTestReq) XXX_Size() int {
	return xxx_messageInfo_AddConditionValForTestReq.Size(m)
}
func (m *AddConditionValForTestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddConditionValForTestReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddConditionValForTestReq proto.InternalMessageInfo

func (m *AddConditionValForTestReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddConditionValForTestReq) GetTyp() ConditionType {
	if m != nil {
		return m.Typ
	}
	return ConditionType_NORMAL
}

func (m *AddConditionValForTestReq) GetVal() uint32 {
	if m != nil {
		return m.Val
	}
	return 0
}

func (m *AddConditionValForTestReq) GetTime() uint32 {
	if m != nil {
		return m.Time
	}
	return 0
}

type AddConditionValForTestResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddConditionValForTestResp) Reset()         { *m = AddConditionValForTestResp{} }
func (m *AddConditionValForTestResp) String() string { return proto.CompactTextString(m) }
func (*AddConditionValForTestResp) ProtoMessage()    {}
func (*AddConditionValForTestResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{41}
}
func (m *AddConditionValForTestResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddConditionValForTestResp.Unmarshal(m, b)
}
func (m *AddConditionValForTestResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddConditionValForTestResp.Marshal(b, m, deterministic)
}
func (dst *AddConditionValForTestResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddConditionValForTestResp.Merge(dst, src)
}
func (m *AddConditionValForTestResp) XXX_Size() int {
	return xxx_messageInfo_AddConditionValForTestResp.Size(m)
}
func (m *AddConditionValForTestResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddConditionValForTestResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddConditionValForTestResp proto.InternalMessageInfo

type GetNowPrivilegePeopleReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNowPrivilegePeopleReq) Reset()         { *m = GetNowPrivilegePeopleReq{} }
func (m *GetNowPrivilegePeopleReq) String() string { return proto.CompactTextString(m) }
func (*GetNowPrivilegePeopleReq) ProtoMessage()    {}
func (*GetNowPrivilegePeopleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{42}
}
func (m *GetNowPrivilegePeopleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNowPrivilegePeopleReq.Unmarshal(m, b)
}
func (m *GetNowPrivilegePeopleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNowPrivilegePeopleReq.Marshal(b, m, deterministic)
}
func (dst *GetNowPrivilegePeopleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNowPrivilegePeopleReq.Merge(dst, src)
}
func (m *GetNowPrivilegePeopleReq) XXX_Size() int {
	return xxx_messageInfo_GetNowPrivilegePeopleReq.Size(m)
}
func (m *GetNowPrivilegePeopleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNowPrivilegePeopleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNowPrivilegePeopleReq proto.InternalMessageInfo

type GetNowPrivilegePeopleResp struct {
	UserMap              map[uint32]uint32 `protobuf:"bytes,1,rep,name=user_map,json=userMap,proto3" json:"user_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetNowPrivilegePeopleResp) Reset()         { *m = GetNowPrivilegePeopleResp{} }
func (m *GetNowPrivilegePeopleResp) String() string { return proto.CompactTextString(m) }
func (*GetNowPrivilegePeopleResp) ProtoMessage()    {}
func (*GetNowPrivilegePeopleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_privilege_b1c25a95f512fca2, []int{43}
}
func (m *GetNowPrivilegePeopleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNowPrivilegePeopleResp.Unmarshal(m, b)
}
func (m *GetNowPrivilegePeopleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNowPrivilegePeopleResp.Marshal(b, m, deterministic)
}
func (dst *GetNowPrivilegePeopleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNowPrivilegePeopleResp.Merge(dst, src)
}
func (m *GetNowPrivilegePeopleResp) XXX_Size() int {
	return xxx_messageInfo_GetNowPrivilegePeopleResp.Size(m)
}
func (m *GetNowPrivilegePeopleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNowPrivilegePeopleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNowPrivilegePeopleResp proto.InternalMessageInfo

func (m *GetNowPrivilegePeopleResp) GetUserMap() map[uint32]uint32 {
	if m != nil {
		return m.UserMap
	}
	return nil
}

func init() {
	proto.RegisterType((*ConditionValue)(nil), "presentprivilege.ConditionValue")
	proto.RegisterType((*Condition)(nil), "presentprivilege.Condition")
	proto.RegisterType((*Privilege)(nil), "presentprivilege.Privilege")
	proto.RegisterType((*AddPrivilegeReq)(nil), "presentprivilege.AddPrivilegeReq")
	proto.RegisterType((*AddPrivilegeResp)(nil), "presentprivilege.AddPrivilegeResp")
	proto.RegisterType((*CheckPrivilegeReq)(nil), "presentprivilege.CheckPrivilegeReq")
	proto.RegisterType((*CheckPrivilegeResp)(nil), "presentprivilege.CheckPrivilegeResp")
	proto.RegisterType((*GetPrivilegeListReq)(nil), "presentprivilege.GetPrivilegeListReq")
	proto.RegisterType((*GetPrivilegeListResp)(nil), "presentprivilege.GetPrivilegeListResp")
	proto.RegisterType((*DelPrivilegeListReq)(nil), "presentprivilege.DelPrivilegeListReq")
	proto.RegisterType((*DelPrivilegeListResp)(nil), "presentprivilege.DelPrivilegeListResp")
	proto.RegisterType((*PresentPrivilege)(nil), "presentprivilege.PresentPrivilege")
	proto.RegisterType((*GetPresentPrivilegeReq)(nil), "presentprivilege.GetPresentPrivilegeReq")
	proto.RegisterType((*GetPresentPrivilegeResp)(nil), "presentprivilege.GetPresentPrivilegeResp")
	proto.RegisterMapType((map[string]uint64)(nil), "presentprivilege.GetPresentPrivilegeResp.MapConditionValueEntry")
	proto.RegisterType((*UserChangeItem)(nil), "presentprivilege.UserChangeItem")
	proto.RegisterType((*PresentPrivilegeChangeEvent)(nil), "presentprivilege.PresentPrivilegeChangeEvent")
	proto.RegisterType((*TreasurePrivilege)(nil), "presentprivilege.TreasurePrivilege")
	proto.RegisterType((*TreasurePrivilegeCondition)(nil), "presentprivilege.TreasurePrivilegeCondition")
	proto.RegisterType((*GetTreasurePrivilegeReq)(nil), "presentprivilege.GetTreasurePrivilegeReq")
	proto.RegisterType((*GetTreasurePrivilegeResp)(nil), "presentprivilege.GetTreasurePrivilegeResp")
	proto.RegisterType((*TreasureConditionValue)(nil), "presentprivilege.TreasureConditionValue")
	proto.RegisterType((*TreasureCondition)(nil), "presentprivilege.TreasureCondition")
	proto.RegisterType((*TreasurePrivilegeConfig)(nil), "presentprivilege.TreasurePrivilegeConfig")
	proto.RegisterType((*AddTreasurePrivilegeReq)(nil), "presentprivilege.AddTreasurePrivilegeReq")
	proto.RegisterType((*AddTreasurePrivilegeResp)(nil), "presentprivilege.AddTreasurePrivilegeResp")
	proto.RegisterType((*TreasureEnd)(nil), "presentprivilege.TreasureEnd")
	proto.RegisterType((*GetTreasurePrivilegeListReq)(nil), "presentprivilege.GetTreasurePrivilegeListReq")
	proto.RegisterType((*GetTreasurePrivilegeListResp)(nil), "presentprivilege.GetTreasurePrivilegeListResp")
	proto.RegisterType((*DelTreasurePrivilegeListReq)(nil), "presentprivilege.DelTreasurePrivilegeListReq")
	proto.RegisterType((*DelTreasurePrivilegeListResp)(nil), "presentprivilege.DelTreasurePrivilegeListResp")
	proto.RegisterType((*CheckTreasurePrivilegeReq)(nil), "presentprivilege.CheckTreasurePrivilegeReq")
	proto.RegisterType((*CheckTreasurePrivilegeResp)(nil), "presentprivilege.CheckTreasurePrivilegeResp")
	proto.RegisterType((*GetConditionValReq)(nil), "presentprivilege.GetConditionValReq")
	proto.RegisterType((*TimeRange)(nil), "presentprivilege.TimeRange")
	proto.RegisterType((*GetConditionValResp)(nil), "presentprivilege.GetConditionValResp")
	proto.RegisterType((*TimeValue)(nil), "presentprivilege.TimeValue")
	proto.RegisterType((*ConditionVal)(nil), "presentprivilege.conditionVal")
	proto.RegisterType((*GetTreasurePrivilegeHistoryReq)(nil), "presentprivilege.GetTreasurePrivilegeHistoryReq")
	proto.RegisterType((*GetTreasurePrivilegeHistoryResp)(nil), "presentprivilege.GetTreasurePrivilegeHistoryResp")
	proto.RegisterType((*TreasurePrivilegeHistory)(nil), "presentprivilege.TreasurePrivilegeHistory")
	proto.RegisterType((*AddConditionValForTestReq)(nil), "presentprivilege.AddConditionValForTestReq")
	proto.RegisterType((*AddConditionValForTestResp)(nil), "presentprivilege.AddConditionValForTestResp")
	proto.RegisterType((*GetNowPrivilegePeopleReq)(nil), "presentprivilege.GetNowPrivilegePeopleReq")
	proto.RegisterType((*GetNowPrivilegePeopleResp)(nil), "presentprivilege.GetNowPrivilegePeopleResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "presentprivilege.GetNowPrivilegePeopleResp.UserMapEntry")
	proto.RegisterEnum("presentprivilege.ConditionType", ConditionType_name, ConditionType_value)
	proto.RegisterEnum("presentprivilege.GiftType", GiftType_name, GiftType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentPrivilegeServerClient is the client API for PresentPrivilegeServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentPrivilegeServerClient interface {
	AddPrivilege(ctx context.Context, in *AddPrivilegeReq, opts ...grpc.CallOption) (*AddPrivilegeResp, error)
	GetPrivilegeList(ctx context.Context, in *GetPrivilegeListReq, opts ...grpc.CallOption) (*GetPrivilegeListResp, error)
	DelPrivilegeList(ctx context.Context, in *DelPrivilegeListReq, opts ...grpc.CallOption) (*DelPrivilegeListResp, error)
	GetPresentPrivilege(ctx context.Context, in *GetPresentPrivilegeReq, opts ...grpc.CallOption) (*GetPresentPrivilegeResp, error)
	CheckPrivilege(ctx context.Context, in *CheckPrivilegeReq, opts ...grpc.CallOption) (*CheckPrivilegeResp, error)
	AddTreasurePrivilege(ctx context.Context, in *AddTreasurePrivilegeReq, opts ...grpc.CallOption) (*AddTreasurePrivilegeResp, error)
	DelTreasurePrivilegeList(ctx context.Context, in *DelTreasurePrivilegeListReq, opts ...grpc.CallOption) (*DelTreasurePrivilegeListResp, error)
	GetTreasurePrivilegeList(ctx context.Context, in *GetTreasurePrivilegeListReq, opts ...grpc.CallOption) (*GetTreasurePrivilegeListResp, error)
	GetTreasurePrivilege(ctx context.Context, in *GetTreasurePrivilegeReq, opts ...grpc.CallOption) (*GetTreasurePrivilegeResp, error)
	CheckTreasurePrivilege(ctx context.Context, in *CheckTreasurePrivilegeReq, opts ...grpc.CallOption) (*CheckTreasurePrivilegeResp, error)
	GetConditionVal(ctx context.Context, in *GetConditionValReq, opts ...grpc.CallOption) (*GetConditionValResp, error)
	GetTreasurePrivilegeHistory(ctx context.Context, in *GetTreasurePrivilegeHistoryReq, opts ...grpc.CallOption) (*GetTreasurePrivilegeHistoryResp, error)
	AddConditionValForTest(ctx context.Context, in *AddConditionValForTestReq, opts ...grpc.CallOption) (*AddConditionValForTestResp, error)
	GetNowPrivilegePeople(ctx context.Context, in *GetNowPrivilegePeopleReq, opts ...grpc.CallOption) (*GetNowPrivilegePeopleResp, error)
}

type presentPrivilegeServerClient struct {
	cc *grpc.ClientConn
}

func NewPresentPrivilegeServerClient(cc *grpc.ClientConn) PresentPrivilegeServerClient {
	return &presentPrivilegeServerClient{cc}
}

func (c *presentPrivilegeServerClient) AddPrivilege(ctx context.Context, in *AddPrivilegeReq, opts ...grpc.CallOption) (*AddPrivilegeResp, error) {
	out := new(AddPrivilegeResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/AddPrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) GetPrivilegeList(ctx context.Context, in *GetPrivilegeListReq, opts ...grpc.CallOption) (*GetPrivilegeListResp, error) {
	out := new(GetPrivilegeListResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/GetPrivilegeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) DelPrivilegeList(ctx context.Context, in *DelPrivilegeListReq, opts ...grpc.CallOption) (*DelPrivilegeListResp, error) {
	out := new(DelPrivilegeListResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/DelPrivilegeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) GetPresentPrivilege(ctx context.Context, in *GetPresentPrivilegeReq, opts ...grpc.CallOption) (*GetPresentPrivilegeResp, error) {
	out := new(GetPresentPrivilegeResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/GetPresentPrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) CheckPrivilege(ctx context.Context, in *CheckPrivilegeReq, opts ...grpc.CallOption) (*CheckPrivilegeResp, error) {
	out := new(CheckPrivilegeResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/CheckPrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) AddTreasurePrivilege(ctx context.Context, in *AddTreasurePrivilegeReq, opts ...grpc.CallOption) (*AddTreasurePrivilegeResp, error) {
	out := new(AddTreasurePrivilegeResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/AddTreasurePrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) DelTreasurePrivilegeList(ctx context.Context, in *DelTreasurePrivilegeListReq, opts ...grpc.CallOption) (*DelTreasurePrivilegeListResp, error) {
	out := new(DelTreasurePrivilegeListResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/DelTreasurePrivilegeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) GetTreasurePrivilegeList(ctx context.Context, in *GetTreasurePrivilegeListReq, opts ...grpc.CallOption) (*GetTreasurePrivilegeListResp, error) {
	out := new(GetTreasurePrivilegeListResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/GetTreasurePrivilegeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) GetTreasurePrivilege(ctx context.Context, in *GetTreasurePrivilegeReq, opts ...grpc.CallOption) (*GetTreasurePrivilegeResp, error) {
	out := new(GetTreasurePrivilegeResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/GetTreasurePrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) CheckTreasurePrivilege(ctx context.Context, in *CheckTreasurePrivilegeReq, opts ...grpc.CallOption) (*CheckTreasurePrivilegeResp, error) {
	out := new(CheckTreasurePrivilegeResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/CheckTreasurePrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) GetConditionVal(ctx context.Context, in *GetConditionValReq, opts ...grpc.CallOption) (*GetConditionValResp, error) {
	out := new(GetConditionValResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/GetConditionVal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) GetTreasurePrivilegeHistory(ctx context.Context, in *GetTreasurePrivilegeHistoryReq, opts ...grpc.CallOption) (*GetTreasurePrivilegeHistoryResp, error) {
	out := new(GetTreasurePrivilegeHistoryResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/GetTreasurePrivilegeHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) AddConditionValForTest(ctx context.Context, in *AddConditionValForTestReq, opts ...grpc.CallOption) (*AddConditionValForTestResp, error) {
	out := new(AddConditionValForTestResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/AddConditionValForTest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentPrivilegeServerClient) GetNowPrivilegePeople(ctx context.Context, in *GetNowPrivilegePeopleReq, opts ...grpc.CallOption) (*GetNowPrivilegePeopleResp, error) {
	out := new(GetNowPrivilegePeopleResp)
	err := c.cc.Invoke(ctx, "/presentprivilege.PresentPrivilegeServer/GetNowPrivilegePeople", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentPrivilegeServerServer is the server API for PresentPrivilegeServer service.
type PresentPrivilegeServerServer interface {
	AddPrivilege(context.Context, *AddPrivilegeReq) (*AddPrivilegeResp, error)
	GetPrivilegeList(context.Context, *GetPrivilegeListReq) (*GetPrivilegeListResp, error)
	DelPrivilegeList(context.Context, *DelPrivilegeListReq) (*DelPrivilegeListResp, error)
	GetPresentPrivilege(context.Context, *GetPresentPrivilegeReq) (*GetPresentPrivilegeResp, error)
	CheckPrivilege(context.Context, *CheckPrivilegeReq) (*CheckPrivilegeResp, error)
	AddTreasurePrivilege(context.Context, *AddTreasurePrivilegeReq) (*AddTreasurePrivilegeResp, error)
	DelTreasurePrivilegeList(context.Context, *DelTreasurePrivilegeListReq) (*DelTreasurePrivilegeListResp, error)
	GetTreasurePrivilegeList(context.Context, *GetTreasurePrivilegeListReq) (*GetTreasurePrivilegeListResp, error)
	GetTreasurePrivilege(context.Context, *GetTreasurePrivilegeReq) (*GetTreasurePrivilegeResp, error)
	CheckTreasurePrivilege(context.Context, *CheckTreasurePrivilegeReq) (*CheckTreasurePrivilegeResp, error)
	GetConditionVal(context.Context, *GetConditionValReq) (*GetConditionValResp, error)
	GetTreasurePrivilegeHistory(context.Context, *GetTreasurePrivilegeHistoryReq) (*GetTreasurePrivilegeHistoryResp, error)
	AddConditionValForTest(context.Context, *AddConditionValForTestReq) (*AddConditionValForTestResp, error)
	GetNowPrivilegePeople(context.Context, *GetNowPrivilegePeopleReq) (*GetNowPrivilegePeopleResp, error)
}

func RegisterPresentPrivilegeServerServer(s *grpc.Server, srv PresentPrivilegeServerServer) {
	s.RegisterService(&_PresentPrivilegeServer_serviceDesc, srv)
}

func _PresentPrivilegeServer_AddPrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).AddPrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/AddPrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).AddPrivilege(ctx, req.(*AddPrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_GetPrivilegeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrivilegeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).GetPrivilegeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/GetPrivilegeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).GetPrivilegeList(ctx, req.(*GetPrivilegeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_DelPrivilegeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPrivilegeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).DelPrivilegeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/DelPrivilegeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).DelPrivilegeList(ctx, req.(*DelPrivilegeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_GetPresentPrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentPrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).GetPresentPrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/GetPresentPrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).GetPresentPrivilege(ctx, req.(*GetPresentPrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_CheckPrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).CheckPrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/CheckPrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).CheckPrivilege(ctx, req.(*CheckPrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_AddTreasurePrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTreasurePrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).AddTreasurePrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/AddTreasurePrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).AddTreasurePrivilege(ctx, req.(*AddTreasurePrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_DelTreasurePrivilegeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTreasurePrivilegeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).DelTreasurePrivilegeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/DelTreasurePrivilegeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).DelTreasurePrivilegeList(ctx, req.(*DelTreasurePrivilegeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_GetTreasurePrivilegeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTreasurePrivilegeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).GetTreasurePrivilegeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/GetTreasurePrivilegeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).GetTreasurePrivilegeList(ctx, req.(*GetTreasurePrivilegeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_GetTreasurePrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTreasurePrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).GetTreasurePrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/GetTreasurePrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).GetTreasurePrivilege(ctx, req.(*GetTreasurePrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_CheckTreasurePrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTreasurePrivilegeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).CheckTreasurePrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/CheckTreasurePrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).CheckTreasurePrivilege(ctx, req.(*CheckTreasurePrivilegeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_GetConditionVal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConditionValReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).GetConditionVal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/GetConditionVal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).GetConditionVal(ctx, req.(*GetConditionValReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_GetTreasurePrivilegeHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTreasurePrivilegeHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).GetTreasurePrivilegeHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/GetTreasurePrivilegeHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).GetTreasurePrivilegeHistory(ctx, req.(*GetTreasurePrivilegeHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_AddConditionValForTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddConditionValForTestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).AddConditionValForTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/AddConditionValForTest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).AddConditionValForTest(ctx, req.(*AddConditionValForTestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentPrivilegeServer_GetNowPrivilegePeople_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNowPrivilegePeopleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentPrivilegeServerServer).GetNowPrivilegePeople(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentprivilege.PresentPrivilegeServer/GetNowPrivilegePeople",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentPrivilegeServerServer).GetNowPrivilegePeople(ctx, req.(*GetNowPrivilegePeopleReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentPrivilegeServer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "presentprivilege.PresentPrivilegeServer",
	HandlerType: (*PresentPrivilegeServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddPrivilege",
			Handler:    _PresentPrivilegeServer_AddPrivilege_Handler,
		},
		{
			MethodName: "GetPrivilegeList",
			Handler:    _PresentPrivilegeServer_GetPrivilegeList_Handler,
		},
		{
			MethodName: "DelPrivilegeList",
			Handler:    _PresentPrivilegeServer_DelPrivilegeList_Handler,
		},
		{
			MethodName: "GetPresentPrivilege",
			Handler:    _PresentPrivilegeServer_GetPresentPrivilege_Handler,
		},
		{
			MethodName: "CheckPrivilege",
			Handler:    _PresentPrivilegeServer_CheckPrivilege_Handler,
		},
		{
			MethodName: "AddTreasurePrivilege",
			Handler:    _PresentPrivilegeServer_AddTreasurePrivilege_Handler,
		},
		{
			MethodName: "DelTreasurePrivilegeList",
			Handler:    _PresentPrivilegeServer_DelTreasurePrivilegeList_Handler,
		},
		{
			MethodName: "GetTreasurePrivilegeList",
			Handler:    _PresentPrivilegeServer_GetTreasurePrivilegeList_Handler,
		},
		{
			MethodName: "GetTreasurePrivilege",
			Handler:    _PresentPrivilegeServer_GetTreasurePrivilege_Handler,
		},
		{
			MethodName: "CheckTreasurePrivilege",
			Handler:    _PresentPrivilegeServer_CheckTreasurePrivilege_Handler,
		},
		{
			MethodName: "GetConditionVal",
			Handler:    _PresentPrivilegeServer_GetConditionVal_Handler,
		},
		{
			MethodName: "GetTreasurePrivilegeHistory",
			Handler:    _PresentPrivilegeServer_GetTreasurePrivilegeHistory_Handler,
		},
		{
			MethodName: "AddConditionValForTest",
			Handler:    _PresentPrivilegeServer_AddConditionValForTest_Handler,
		},
		{
			MethodName: "GetNowPrivilegePeople",
			Handler:    _PresentPrivilegeServer_GetNowPrivilegePeople_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "present-privilege/present-privilege.proto",
}

func init() {
	proto.RegisterFile("present-privilege/present-privilege.proto", fileDescriptor_present_privilege_b1c25a95f512fca2)
}

var fileDescriptor_present_privilege_b1c25a95f512fca2 = []byte{
	// 1754 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x59, 0x5b, 0x6f, 0x1b, 0xc5,
	0x17, 0xcf, 0xda, 0xb9, 0xf9, 0xc4, 0x76, 0x9d, 0x69, 0xfe, 0x89, 0xe3, 0xb4, 0x4d, 0xfe, 0xd3,
	0x16, 0x72, 0x69, 0x13, 0x9a, 0x16, 0x54, 0x8a, 0x40, 0xb8, 0x8e, 0x9b, 0x1a, 0x25, 0x69, 0xba,
	0x4e, 0x5b, 0x81, 0x84, 0xdc, 0xad, 0x77, 0xec, 0x2e, 0xb5, 0x77, 0xa7, 0xbb, 0xeb, 0xd0, 0x88,
	0x37, 0x84, 0x10, 0xe2, 0x89, 0x0f, 0xc0, 0x33, 0x2f, 0x20, 0x21, 0xf1, 0x39, 0x78, 0xe5, 0x09,
	0x3e, 0x0c, 0x9a, 0xd9, 0x8b, 0xf7, 0x32, 0xeb, 0xae, 0xc3, 0xdb, 0xce, 0x9c, 0x33, 0x73, 0xce,
	0x9c, 0xeb, 0x6f, 0x66, 0x61, 0x83, 0x9a, 0xc4, 0x22, 0xba, 0x7d, 0x93, 0x9a, 0xda, 0xa9, 0xd6,
	0x23, 0x5d, 0xb2, 0x13, 0x9b, 0xd9, 0xa6, 0xa6, 0x61, 0x1b, 0xa8, 0xe4, 0x12, 0xfc, 0x79, 0xfc,
	0x93, 0x04, 0xc5, 0x9a, 0xa1, 0xab, 0x9a, 0xad, 0x19, 0xfa, 0x53, 0xa5, 0x37, 0x20, 0x68, 0x01,
	0xa6, 0x4e, 0xd9, 0x47, 0x59, 0x5a, 0x93, 0xd6, 0x27, 0x65, 0x67, 0xc0, 0x66, 0x7b, 0xe4, 0x94,
	0xf4, 0xca, 0x99, 0x35, 0x69, 0xbd, 0x20, 0x3b, 0x03, 0x74, 0x19, 0xe0, 0x05, 0xe9, 0x6a, 0x7a,
	0xcb, 0xd6, 0xfa, 0xa4, 0x9c, 0xe5, 0xa4, 0x1c, 0x9f, 0x39, 0xd1, 0xfa, 0x04, 0x2d, 0xc3, 0x2c,
	0xd1, 0x55, 0x87, 0x38, 0xc9, 0x89, 0x33, 0x44, 0x57, 0x39, 0xa9, 0x0c, 0x33, 0x16, 0x69, 0x1b,
	0xba, 0x6a, 0x95, 0xa7, 0x1c, 0x8a, 0x3b, 0xc4, 0x6f, 0x20, 0xe7, 0x6b, 0x84, 0x6e, 0xc3, 0xa4,
	0x7d, 0x46, 0x1d, 0x5d, 0x8a, 0xbb, 0xab, 0xdb, 0xd1, 0x03, 0x6c, 0xfb, 0xac, 0x27, 0x67, 0x94,
	0xc8, 0x9c, 0x19, 0x7d, 0xe0, 0x9d, 0x80, 0xe9, 0x3a, 0xb7, 0xbb, 0x36, 0x62, 0x15, 0x3f, 0xb2,
	0x7b, 0x46, 0x66, 0x8c, 0xdc, 0xb1, 0xc7, 0x83, 0x8a, 0x90, 0xd1, 0x54, 0x2e, 0xb8, 0x20, 0x67,
	0x34, 0x15, 0x2d, 0xc1, 0x4c, 0x57, 0xeb, 0xd8, 0x2d, 0x4d, 0x75, 0x6d, 0x30, 0xcd, 0x86, 0x0d,
	0x15, 0x7d, 0x08, 0xb9, 0xb6, 0xb7, 0x5f, 0x39, 0xbb, 0x96, 0x5d, 0x9f, 0xdb, 0x5d, 0x19, 0x21,
	0x52, 0x1e, 0x72, 0xa3, 0x55, 0x98, 0x1b, 0x50, 0x55, 0xb1, 0x49, 0xd0, 0x46, 0xe0, 0x4c, 0x31,
	0x33, 0xe1, 0x6f, 0x25, 0xb8, 0x50, 0x55, 0x55, 0x5f, 0x2b, 0x99, 0xbc, 0x66, 0x56, 0x35, 0x4c,
	0x95, 0x98, 0x2d, 0x57, 0xbd, 0x9c, 0x3c, 0xc3, 0xc7, 0x0d, 0x95, 0x91, 0x06, 0x9a, 0xda, 0xea,
	0x69, 0x96, 0x5d, 0xce, 0xac, 0x65, 0x99, 0x59, 0x07, 0x9a, 0x7a, 0xa0, 0x59, 0x36, 0xd3, 0xd2,
	0x57, 0x86, 0x7b, 0x4a, 0xa8, 0xe5, 0x50, 0xd0, 0x90, 0x1b, 0x23, 0x28, 0x85, 0x75, 0xb0, 0x28,
	0x3e, 0x82, 0xf9, 0xda, 0x4b, 0xd2, 0x7e, 0x15, 0xd2, 0x2c, 0x24, 0x43, 0x1a, 0x4b, 0xc6, 0x02,
	0xa0, 0xe8, 0x7e, 0x16, 0xc5, 0x1f, 0xc3, 0xc5, 0x7d, 0x62, 0xfb, 0x73, 0xec, 0x20, 0x4c, 0x4e,
	0x09, 0xb2, 0x46, 0xa7, 0xe3, 0xfa, 0x86, 0x7d, 0xf2, 0xf0, 0xd4, 0xfa, 0x9a, 0xed, 0x87, 0x27,
	0x1b, 0x60, 0x0a, 0x0b, 0xf1, 0xe5, 0x16, 0x45, 0xf7, 0xa1, 0xe8, 0x4b, 0x76, 0x8c, 0x25, 0x25,
	0xb9, 0x6d, 0xa8, 0x4f, 0x81, 0x06, 0xf7, 0x61, 0x12, 0x6d, 0xc3, 0x56, 0xfc, 0x84, 0xe0, 0x03,
	0x7c, 0x1d, 0x2e, 0xee, 0x91, 0x5e, 0x4c, 0xe1, 0x48, 0x2c, 0xe1, 0x45, 0x58, 0x88, 0xb3, 0x59,
	0x14, 0xff, 0x2a, 0x41, 0xe9, 0xd8, 0x51, 0x61, 0x18, 0x88, 0x81, 0xc0, 0x93, 0x42, 0x81, 0x77,
	0xae, 0x9c, 0x5c, 0x85, 0x39, 0xf2, 0x86, 0x6a, 0x66, 0x38, 0xe4, 0x9c, 0x29, 0xce, 0xe0, 0xe7,
	0xff, 0x54, 0x30, 0xff, 0x11, 0x4c, 0xaa, 0xca, 0x99, 0x55, 0x9e, 0xe6, 0xfc, 0xfc, 0x1b, 0x6f,
	0xc2, 0x22, 0x37, 0x6f, 0x58, 0x5f, 0xd7, 0x41, 0x03, 0x5f, 0x5d, 0xf6, 0x89, 0x7f, 0xce, 0xc0,
	0x92, 0x90, 0xd9, 0xa2, 0xa8, 0x91, 0xe0, 0x0e, 0x2c, 0x72, 0x47, 0x64, 0x7d, 0xc4, 0x2b, 0x14,
	0x2e, 0xf6, 0x15, 0xda, 0xf2, 0x33, 0xac, 0xe5, 0x15, 0x02, 0xb6, 0xdf, 0xa7, 0xf1, 0xfd, 0x12,
	0x54, 0xda, 0x3e, 0x54, 0x68, 0xb8, 0x46, 0xd4, 0x75, 0xdb, 0x3c, 0x93, 0xe7, 0xfb, 0xd1, 0xf9,
	0xca, 0x1e, 0x2c, 0x8a, 0x99, 0x99, 0x11, 0x5e, 0x91, 0x33, 0x37, 0x45, 0xd9, 0xe7, 0xd0, 0xb4,
	0x99, 0x80, 0x69, 0xef, 0x65, 0xee, 0x4a, 0xf8, 0x31, 0x14, 0x9f, 0x58, 0xc4, 0xac, 0xbd, 0x54,
	0xf4, 0x2e, 0x69, 0xd8, 0xa4, 0x1f, 0x37, 0x61, 0x72, 0x01, 0xf2, 0xe3, 0x20, 0x1b, 0x88, 0x03,
	0xfc, 0x1c, 0x56, 0xa2, 0x47, 0x73, 0xb6, 0xaf, 0x9f, 0x12, 0xdd, 0x46, 0x55, 0x98, 0x6b, 0xf3,
	0x61, 0xd0, 0xe2, 0x82, 0x52, 0x19, 0x56, 0x4b, 0x06, 0x67, 0x11, 0x33, 0x36, 0xfe, 0x43, 0x82,
	0xf9, 0x13, 0x93, 0x28, 0xd6, 0xc0, 0x24, 0x29, 0xc2, 0x35, 0x1c, 0x98, 0x99, 0x51, 0xcd, 0x22,
	0x1b, 0x6e, 0x16, 0x9f, 0x05, 0x2b, 0xec, 0x24, 0xd7, 0xf4, 0x46, 0x5c, 0xd3, 0x98, 0x2a, 0xa2,
	0x92, 0x8b, 0xff, 0x91, 0xa0, 0x92, 0xcc, 0x99, 0xac, 0xfd, 0x03, 0x28, 0x0e, 0xa3, 0x8a, 0xf7,
	0xa4, 0x4c, 0xba, 0x9e, 0x54, 0x68, 0x07, 0x87, 0xe8, 0x5d, 0xb8, 0x10, 0x8d, 0x4e, 0xe7, 0xb4,
	0xc3, 0xed, 0x9d, 0x3e, 0x7c, 0x07, 0x16, 0x03, 0x02, 0x15, 0xb3, 0x4b, 0x6c, 0x97, 0xdf, 0xc9,
	0xd9, 0x85, 0xe1, 0xbe, 0x9c, 0xc8, 0x57, 0xe1, 0x2d, 0x9e, 0x66, 0xb1, 0x03, 0x8a, 0x93, 0x52,
	0x87, 0xb2, 0x98, 0xd9, 0xa2, 0x48, 0x06, 0x64, 0xbb, 0x84, 0x56, 0xb0, 0xa8, 0x33, 0xe3, 0x5f,
	0x4d, 0x61, 0x7c, 0x79, 0xde, 0x8e, 0x4e, 0xe1, 0xbf, 0x25, 0x58, 0xf4, 0x18, 0x53, 0xa1, 0x8e,
	0xf3, 0x87, 0xcc, 0x16, 0xcc, 0x0f, 0x6b, 0x8a, 0x87, 0x34, 0x1c, 0xc3, 0x95, 0x7c, 0x42, 0xd3,
	0x99, 0x47, 0x57, 0x61, 0x58, 0x46, 0x5a, 0x44, 0x57, 0x5d, 0x48, 0x92, 0xf7, 0x27, 0xeb, 0xba,
	0x1a, 0x2d, 0x9c, 0xd3, 0xd1, 0xc2, 0x89, 0x7f, 0x08, 0xa4, 0xc3, 0x7f, 0x44, 0x30, 0x9f, 0x84,
	0x11, 0xcc, 0x7a, 0xb2, 0xbd, 0xc5, 0x48, 0xe6, 0x17, 0x09, 0x96, 0x44, 0x41, 0xde, 0xd1, 0xba,
	0xe9, 0x71, 0x4d, 0x35, 0x8e, 0x6b, 0xae, 0xa6, 0x50, 0x64, 0x2c, 0x7c, 0xf3, 0x97, 0x04, 0x4b,
	0x55, 0x55, 0x15, 0xc6, 0xeb, 0xf9, 0x70, 0xce, 0x7e, 0x1c, 0xe7, 0x6c, 0xa4, 0xab, 0x15, 0x1d,
	0xad, 0x1b, 0x40, 0x24, 0xa3, 0xc0, 0xeb, 0x1a, 0xe4, 0x3d, 0x52, 0xcb, 0x24, 0x3d, 0x37, 0x5c,
	0xc0, 0x25, 0xcb, 0xa4, 0x87, 0x9f, 0x41, 0x59, 0x7c, 0x2c, 0x8b, 0xa2, 0x8f, 0xc2, 0x28, 0x89,
	0xd9, 0xf5, 0x72, 0xb2, 0x86, 0x75, 0x5d, 0x0d, 0xe2, 0xa4, 0x26, 0xcc, 0x05, 0x28, 0xe3, 0x74,
	0x89, 0xe4, 0x64, 0xc1, 0x75, 0x58, 0x11, 0xd5, 0x81, 0x71, 0xe1, 0xd6, 0xf7, 0x12, 0x5c, 0x4a,
	0xde, 0xc7, 0xa2, 0xe8, 0x38, 0xa1, 0xd1, 0x8f, 0xe1, 0xa0, 0x54, 0x28, 0xec, 0x26, 0xac, 0xec,
	0x91, 0x5e, 0xe2, 0x79, 0xbc, 0x0c, 0xc8, 0xba, 0x68, 0xec, 0x0a, 0x5c, 0x4a, 0x66, 0xb7, 0x28,
	0x56, 0x61, 0x99, 0x63, 0x53, 0x61, 0x94, 0xee, 0xc7, 0x31, 0xef, 0xb9, 0xe2, 0x0d, 0x5f, 0x82,
	0x4a, 0x92, 0x14, 0x8b, 0xe2, 0x17, 0x80, 0xf6, 0x89, 0x1d, 0xcc, 0x76, 0x61, 0x49, 0x47, 0xf7,
	0x00, 0x9c, 0xb0, 0x64, 0x5d, 0xda, 0xc5, 0x3d, 0x02, 0x58, 0xcb, 0xe3, 0x94, 0xb1, 0xc8, 0x39,
	0xdb, 0xfb, 0xc4, 0x75, 0xc8, 0xf9, 0xf3, 0x91, 0xd2, 0x2b, 0x8d, 0x2a, 0xbd, 0x99, 0x70, 0x34,
	0x3d, 0xe6, 0xa0, 0x3d, 0xac, 0xaa, 0x45, 0x7d, 0xcd, 0xbc, 0x32, 0x3f, 0x42, 0x33, 0xa7, 0x96,
	0x71, 0xcd, 0x9c, 0xae, 0xf6, 0xa3, 0xe4, 0xa8, 0xf6, 0x54, 0xd0, 0x15, 0xc6, 0x50, 0x0d, 0xd5,
	0xa0, 0x10, 0x6a, 0xbe, 0x6e, 0x59, 0xbb, 0x12, 0x57, 0x23, 0xd8, 0x8c, 0xe5, 0x7c, 0x70, 0x84,
	0x9b, 0x10, 0x1a, 0xa3, 0x5b, 0x90, 0xb5, 0xcf, 0x68, 0xda, 0x02, 0xcf, 0x78, 0x99, 0xdf, 0x4e,
	0xdd, 0xa0, 0x9d, 0x94, 0xd9, 0x27, 0xde, 0x85, 0x2b, 0xa2, 0xd4, 0x79, 0xa8, 0x59, 0xb6, 0x61,
	0x9e, 0x89, 0xdb, 0x37, 0x85, 0xd5, 0x91, 0x6b, 0x2c, 0x8a, 0x0e, 0x21, 0xff, 0xd2, 0x19, 0x06,
	0xf3, 0x6d, 0x33, 0x45, 0x80, 0x7a, 0xbb, 0xcc, 0xb9, 0xeb, 0x39, 0xe2, 0xfb, 0x53, 0x82, 0x72,
	0x12, 0x27, 0x5a, 0x81, 0x9c, 0x53, 0x79, 0xda, 0x86, 0xee, 0x16, 0xec, 0x59, 0x5e, 0x7b, 0xda,
	0x86, 0xee, 0x13, 0x75, 0xc5, 0xf5, 0x8a, 0x4b, 0x3c, 0x52, 0xfa, 0xdc, 0xa1, 0x9c, 0x48, 0x4d,
	0xad, 0xed, 0x5f, 0x59, 0xd8, 0xcc, 0x31, 0x9b, 0xe0, 0x6b, 0x15, 0xcf, 0xdd, 0x4e, 0x29, 0x9e,
	0x65, 0x13, 0x9e, 0xb7, 0x3b, 0x1e, 0xcd, 0x7d, 0x49, 0xe8, 0x0c, 0xaf, 0x3a, 0x8c, 0xe4, 0x75,
	0x7f, 0xb7, 0x63, 0x77, 0x34, 0xdd, 0xed, 0xfb, 0xac, 0x60, 0x2d, 0x57, 0x55, 0x35, 0x18, 0xaa,
	0x0f, 0x0c, 0xf3, 0x84, 0xf8, 0x65, 0x2f, 0x92, 0x5c, 0xae, 0xa7, 0x33, 0xe3, 0x7b, 0xda, 0x39,
	0x13, 0xfb, 0x64, 0x37, 0xa9, 0xc0, 0x41, 0xf8, 0x37, 0xcb, 0xfd, 0x24, 0x3d, 0x2c, 0x8a, 0x2b,
	0x1c, 0xa6, 0x1d, 0x19, 0x5f, 0xfb, 0x26, 0x3f, 0x26, 0x06, 0xed, 0xb1, 0xf2, 0x83, 0x7f, 0x93,
	0x60, 0x39, 0x81, 0x68, 0x51, 0xd4, 0x84, 0xd9, 0x81, 0x45, 0xcc, 0x56, 0x5f, 0xa1, 0xae, 0xeb,
	0xef, 0x0a, 0xef, 0x40, 0xe2, 0xe5, 0x1c, 0xfb, 0x1f, 0x2a, 0xd4, 0xb9, 0xfb, 0xcc, 0x0c, 0x9c,
	0x51, 0xe5, 0x1e, 0xe4, 0x83, 0x84, 0xe0, 0x3d, 0xa7, 0x20, 0xb8, 0xe7, 0x14, 0x02, 0xf7, 0x9c,
	0xcd, 0xaf, 0xa0, 0x10, 0x32, 0x12, 0x02, 0x98, 0x3e, 0x7a, 0x24, 0x1f, 0x56, 0x0f, 0x4a, 0x13,
	0x28, 0x0f, 0xb3, 0x72, 0xbd, 0xf6, 0xb0, 0x2a, 0xef, 0xd7, 0x4b, 0x12, 0x2a, 0x40, 0xae, 0x59,
	0x3f, 0xda, 0x6b, 0xed, 0x37, 0x1e, 0x9c, 0x94, 0x32, 0x08, 0x41, 0xf1, 0xe8, 0xd1, 0xfd, 0xc6,
	0x41, 0xe3, 0xe4, 0xf3, 0xd6, 0x41, 0xfd, 0x69, 0xfd, 0xa0, 0x94, 0x45, 0x45, 0x00, 0xb9, 0x51,
	0x7b, 0xe8, 0x8e, 0x27, 0xd9, 0x06, 0xb5, 0x47, 0x47, 0xcd, 0x27, 0x87, 0x75, 0xb9, 0x34, 0xb5,
	0xb9, 0x03, 0xb3, 0xfb, 0x5a, 0xc7, 0xe6, 0x62, 0xe6, 0xa1, 0xc0, 0xf6, 0x69, 0x9d, 0xb4, 0x7c,
	0x69, 0x25, 0xc8, 0xf3, 0xa9, 0xe6, 0x71, 0xbd, 0xd6, 0xa8, 0x1e, 0x94, 0xa4, 0xdd, 0xdf, 0xf3,
	0xb0, 0x18, 0xbd, 0x32, 0x35, 0x89, 0x79, 0x4a, 0x4c, 0xf4, 0x0c, 0xf2, 0xc1, 0x27, 0x10, 0xf4,
	0xff, 0xb8, 0x19, 0x23, 0xcf, 0x34, 0x15, 0xfc, 0x36, 0x16, 0x8b, 0xe2, 0x09, 0xd4, 0x86, 0x52,
	0xf4, 0x89, 0x02, 0x5d, 0x4f, 0xb8, 0xa7, 0x86, 0xdb, 0x58, 0xe5, 0x9d, 0x34, 0x6c, 0x9e, 0x90,
	0xe8, 0x73, 0x83, 0x48, 0x88, 0xe0, 0xe5, 0x42, 0x24, 0x44, 0xf8, 0x72, 0x31, 0x81, 0x7a, 0xee,
	0x5b, 0x4d, 0xe4, 0xf5, 0x62, 0x3d, 0xe5, 0xa5, 0xfb, 0x75, 0x65, 0x23, 0xf5, 0xf5, 0x1c, 0x4f,
	0xa0, 0x2f, 0xa1, 0x18, 0x7e, 0x2f, 0x42, 0x02, 0x6c, 0x1a, 0x7b, 0xa1, 0xaa, 0x5c, 0x7b, 0x3b,
	0x13, 0xdf, 0xde, 0x80, 0x05, 0x11, 0x7e, 0x43, 0x1b, 0x42, 0xa7, 0x8a, 0x80, 0x41, 0x65, 0x33,
	0x2d, 0x2b, 0x17, 0xf8, 0x0d, 0x94, 0x93, 0x30, 0x08, 0xba, 0x29, 0xf4, 0x41, 0x12, 0xbc, 0xa9,
	0x6c, 0x8f, 0xc3, 0xee, 0x09, 0x4f, 0xc2, 0x6d, 0x22, 0xe1, 0x23, 0xb0, 0xa2, 0x48, 0xf8, 0x28,
	0x48, 0xe8, 0x98, 0x5a, 0xc4, 0x81, 0x36, 0xd2, 0xed, 0x94, 0x60, 0xea, 0xa4, 0x7b, 0x2d, 0x9e,
	0x40, 0x03, 0x58, 0x14, 0x03, 0x2d, 0xb4, 0x95, 0x10, 0x1d, 0x42, 0xa1, 0x37, 0xd2, 0x33, 0x73,
	0xb1, 0xcf, 0xe1, 0x42, 0x04, 0x16, 0xa1, 0x6b, 0x42, 0xbd, 0x23, 0x20, 0xaf, 0x72, 0x3d, 0x05,
	0x17, 0x97, 0xf0, 0x9d, 0x24, 0xc6, 0xf1, 0x5e, 0x83, 0x7e, 0x2f, 0x9d, 0x99, 0x86, 0x98, 0xa3,
	0x72, 0x6b, 0xcc, 0x15, 0x9e, 0x7d, 0xc5, 0xcd, 0x4c, 0x64, 0xdf, 0xc4, 0xf6, 0x2b, 0xb2, 0xef,
	0x88, 0x1e, 0x39, 0x81, 0x4c, 0xf8, 0x9f, 0xb0, 0x93, 0xa1, 0xcd, 0xd4, 0x2d, 0xef, 0x75, 0x65,
	0x6b, 0x8c, 0xf6, 0x88, 0x27, 0xee, 0xdf, 0xf9, 0x62, 0xb7, 0x6b, 0xf4, 0x14, 0xbd, 0xbb, 0xfd,
	0xfe, 0xae, 0x6d, 0x6f, 0xb7, 0x8d, 0xfe, 0x0e, 0xff, 0xd3, 0xd2, 0x36, 0x7a, 0x3b, 0x16, 0x31,
	0x4f, 0xb5, 0x36, 0xb1, 0x76, 0xa2, 0x3b, 0xbe, 0x98, 0xe6, 0x3c, 0xb7, 0xff, 0x0d, 0x00, 0x00,
	0xff, 0xff, 0x16, 0xc8, 0x74, 0x21, 0xba, 0x19, 0x00, 0x00,
}
