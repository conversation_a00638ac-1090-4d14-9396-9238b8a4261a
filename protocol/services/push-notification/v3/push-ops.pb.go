// Code generated by protoc-gen-go. DO NOT EDIT.
// source: push-notification/v3/push-ops.proto

package push_server // import "golang.52tt.com/protocol/services/push-notification/v3"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PushSource int32

const (
	PushSource_SOURCE_UNDEFINED            PushSource = 0
	PushSource_SOURCE_SPECIFIED_CONDITION  PushSource = 1
	PushSource_SOURCE_SPECIFIED_TT_ACCOUNT PushSource = 2
	PushSource_SOURCE_SPECIFIED_TT_UID     PushSource = 3
)

var PushSource_name = map[int32]string{
	0: "SOURCE_UNDEFINED",
	1: "SOURCE_SPECIFIED_CONDITION",
	2: "SOURCE_SPECIFIED_TT_ACCOUNT",
	3: "SOURCE_SPECIFIED_TT_UID",
}
var PushSource_value = map[string]int32{
	"SOURCE_UNDEFINED":            0,
	"SOURCE_SPECIFIED_CONDITION":  1,
	"SOURCE_SPECIFIED_TT_ACCOUNT": 2,
	"SOURCE_SPECIFIED_TT_UID":     3,
}

func (x PushSource) String() string {
	return proto.EnumName(PushSource_name, int32(x))
}
func (PushSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_ops_c3795966f4eed898, []int{0}
}

type SendPushReq struct {
	BaseInfo             *SendPushReq_BaseInfo  `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	PushSource           PushSource             `protobuf:"varint,2,opt,name=push_source,json=pushSource,proto3,enum=PushNotification.ops.PushSource" json:"push_source,omitempty"`
	Condition            *SendPushReq_Condition `protobuf:"bytes,3,opt,name=condition,proto3" json:"condition,omitempty"`
	TargetIdList         []string               `protobuf:"bytes,4,rep,name=target_id_list,json=targetIdList,proto3" json:"target_id_list,omitempty"`
	Test                 bool                   `protobuf:"varint,5,opt,name=test,proto3" json:"test,omitempty"`
	InvalidTime          uint32                 `protobuf:"varint,6,opt,name=invalid_time,json=invalidTime,proto3" json:"invalid_time,omitempty"`
	BatchesUploadTarget  bool                   `protobuf:"varint,7,opt,name=batches_upload_target,json=batchesUploadTarget,proto3" json:"batches_upload_target,omitempty"`
	Seqid                uint64                 `protobuf:"varint,8,opt,name=seqid,proto3" json:"seqid,omitempty"`
	Priority             uint32                 `protobuf:"varint,9,opt,name=priority,proto3" json:"priority,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SendPushReq) Reset()         { *m = SendPushReq{} }
func (m *SendPushReq) String() string { return proto.CompactTextString(m) }
func (*SendPushReq) ProtoMessage()    {}
func (*SendPushReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_ops_c3795966f4eed898, []int{0}
}
func (m *SendPushReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPushReq.Unmarshal(m, b)
}
func (m *SendPushReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPushReq.Marshal(b, m, deterministic)
}
func (dst *SendPushReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPushReq.Merge(dst, src)
}
func (m *SendPushReq) XXX_Size() int {
	return xxx_messageInfo_SendPushReq.Size(m)
}
func (m *SendPushReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPushReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendPushReq proto.InternalMessageInfo

func (m *SendPushReq) GetBaseInfo() *SendPushReq_BaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *SendPushReq) GetPushSource() PushSource {
	if m != nil {
		return m.PushSource
	}
	return PushSource_SOURCE_UNDEFINED
}

func (m *SendPushReq) GetCondition() *SendPushReq_Condition {
	if m != nil {
		return m.Condition
	}
	return nil
}

func (m *SendPushReq) GetTargetIdList() []string {
	if m != nil {
		return m.TargetIdList
	}
	return nil
}

func (m *SendPushReq) GetTest() bool {
	if m != nil {
		return m.Test
	}
	return false
}

func (m *SendPushReq) GetInvalidTime() uint32 {
	if m != nil {
		return m.InvalidTime
	}
	return 0
}

func (m *SendPushReq) GetBatchesUploadTarget() bool {
	if m != nil {
		return m.BatchesUploadTarget
	}
	return false
}

func (m *SendPushReq) GetSeqid() uint64 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

func (m *SendPushReq) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

type SendPushReq_BaseInfo struct {
	Platform             uint32              `protobuf:"varint,1,opt,name=platform,proto3" json:"platform,omitempty"`
	Title                string              `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Content              string              `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	JumpUrl              string              `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	NotifyType           uint32              `protobuf:"varint,5,opt,name=notify_type,json=notifyType,proto3" json:"notify_type,omitempty"`
	AppPlatform          uint32              `protobuf:"varint,6,opt,name=app_platform,json=appPlatform,proto3" json:"app_platform,omitempty"`
	NotificationTitle    []*NotificationText `protobuf:"bytes,21,rep,name=notification_title,json=notificationTitle,proto3" json:"notification_title,omitempty"`
	NotificationContent  []*NotificationText `protobuf:"bytes,22,rep,name=notification_content,json=notificationContent,proto3" json:"notification_content,omitempty"`
	Labels               []string            `protobuf:"bytes,23,rep,name=labels,proto3" json:"labels,omitempty"`
	IconUrl              string              `protobuf:"bytes,24,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	VoiceType            string              `protobuf:"bytes,25,opt,name=voice_type,json=voiceType,proto3" json:"voice_type,omitempty"`
	IconType             string              `protobuf:"bytes,26,opt,name=icon_type,json=iconType,proto3" json:"icon_type,omitempty"`
	PushType             PushType            `protobuf:"varint,27,opt,name=push_type,json=pushType,proto3,enum=PushNotification.PushType" json:"push_type,omitempty"`
	Extra                map[string]string   `protobuf:"bytes,28,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SendPushReq_BaseInfo) Reset()         { *m = SendPushReq_BaseInfo{} }
func (m *SendPushReq_BaseInfo) String() string { return proto.CompactTextString(m) }
func (*SendPushReq_BaseInfo) ProtoMessage()    {}
func (*SendPushReq_BaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_ops_c3795966f4eed898, []int{0, 0}
}
func (m *SendPushReq_BaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPushReq_BaseInfo.Unmarshal(m, b)
}
func (m *SendPushReq_BaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPushReq_BaseInfo.Marshal(b, m, deterministic)
}
func (dst *SendPushReq_BaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPushReq_BaseInfo.Merge(dst, src)
}
func (m *SendPushReq_BaseInfo) XXX_Size() int {
	return xxx_messageInfo_SendPushReq_BaseInfo.Size(m)
}
func (m *SendPushReq_BaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPushReq_BaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SendPushReq_BaseInfo proto.InternalMessageInfo

func (m *SendPushReq_BaseInfo) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *SendPushReq_BaseInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SendPushReq_BaseInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *SendPushReq_BaseInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *SendPushReq_BaseInfo) GetNotifyType() uint32 {
	if m != nil {
		return m.NotifyType
	}
	return 0
}

func (m *SendPushReq_BaseInfo) GetAppPlatform() uint32 {
	if m != nil {
		return m.AppPlatform
	}
	return 0
}

func (m *SendPushReq_BaseInfo) GetNotificationTitle() []*NotificationText {
	if m != nil {
		return m.NotificationTitle
	}
	return nil
}

func (m *SendPushReq_BaseInfo) GetNotificationContent() []*NotificationText {
	if m != nil {
		return m.NotificationContent
	}
	return nil
}

func (m *SendPushReq_BaseInfo) GetLabels() []string {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *SendPushReq_BaseInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *SendPushReq_BaseInfo) GetVoiceType() string {
	if m != nil {
		return m.VoiceType
	}
	return ""
}

func (m *SendPushReq_BaseInfo) GetIconType() string {
	if m != nil {
		return m.IconType
	}
	return ""
}

func (m *SendPushReq_BaseInfo) GetPushType() PushType {
	if m != nil {
		return m.PushType
	}
	return PushType_Normal
}

func (m *SendPushReq_BaseInfo) GetExtra() map[string]string {
	if m != nil {
		return m.Extra
	}
	return nil
}

type SendPushReq_Condition struct {
	LoginTimeStart       string   `protobuf:"bytes,1,opt,name=login_time_start,json=loginTimeStart,proto3" json:"login_time_start,omitempty"`
	LoginTimeEnd         string   `protobuf:"bytes,2,opt,name=login_time_end,json=loginTimeEnd,proto3" json:"login_time_end,omitempty"`
	LoginIncludeToday    bool     `protobuf:"varint,3,opt,name=login_include_today,json=loginIncludeToday,proto3" json:"login_include_today,omitempty"`
	Gender               uint32   `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	Province             string   `protobuf:"bytes,5,opt,name=province,proto3" json:"province,omitempty"`
	City                 string   `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`
	AndroidVersion       uint32   `protobuf:"varint,7,opt,name=android_version,json=androidVersion,proto3" json:"android_version,omitempty"`
	IosVersion           uint32   `protobuf:"varint,8,opt,name=ios_version,json=iosVersion,proto3" json:"ios_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendPushReq_Condition) Reset()         { *m = SendPushReq_Condition{} }
func (m *SendPushReq_Condition) String() string { return proto.CompactTextString(m) }
func (*SendPushReq_Condition) ProtoMessage()    {}
func (*SendPushReq_Condition) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_ops_c3795966f4eed898, []int{0, 1}
}
func (m *SendPushReq_Condition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPushReq_Condition.Unmarshal(m, b)
}
func (m *SendPushReq_Condition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPushReq_Condition.Marshal(b, m, deterministic)
}
func (dst *SendPushReq_Condition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPushReq_Condition.Merge(dst, src)
}
func (m *SendPushReq_Condition) XXX_Size() int {
	return xxx_messageInfo_SendPushReq_Condition.Size(m)
}
func (m *SendPushReq_Condition) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPushReq_Condition.DiscardUnknown(m)
}

var xxx_messageInfo_SendPushReq_Condition proto.InternalMessageInfo

func (m *SendPushReq_Condition) GetLoginTimeStart() string {
	if m != nil {
		return m.LoginTimeStart
	}
	return ""
}

func (m *SendPushReq_Condition) GetLoginTimeEnd() string {
	if m != nil {
		return m.LoginTimeEnd
	}
	return ""
}

func (m *SendPushReq_Condition) GetLoginIncludeToday() bool {
	if m != nil {
		return m.LoginIncludeToday
	}
	return false
}

func (m *SendPushReq_Condition) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *SendPushReq_Condition) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *SendPushReq_Condition) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *SendPushReq_Condition) GetAndroidVersion() uint32 {
	if m != nil {
		return m.AndroidVersion
	}
	return 0
}

func (m *SendPushReq_Condition) GetIosVersion() uint32 {
	if m != nil {
		return m.IosVersion
	}
	return 0
}

type SendPushResp struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Result               uint32   `protobuf:"varint,2,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendPushResp) Reset()         { *m = SendPushResp{} }
func (m *SendPushResp) String() string { return proto.CompactTextString(m) }
func (*SendPushResp) ProtoMessage()    {}
func (*SendPushResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_ops_c3795966f4eed898, []int{1}
}
func (m *SendPushResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPushResp.Unmarshal(m, b)
}
func (m *SendPushResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPushResp.Marshal(b, m, deterministic)
}
func (dst *SendPushResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPushResp.Merge(dst, src)
}
func (m *SendPushResp) XXX_Size() int {
	return xxx_messageInfo_SendPushResp.Size(m)
}
func (m *SendPushResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPushResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendPushResp proto.InternalMessageInfo

func (m *SendPushResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *SendPushResp) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

type BatchTargetUidListReq struct {
	TaskId               string     `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TargetIdList         []string   `protobuf:"bytes,2,rep,name=target_id_list,json=targetIdList,proto3" json:"target_id_list,omitempty"`
	LatestCommit         bool       `protobuf:"varint,3,opt,name=latest_commit,json=latestCommit,proto3" json:"latest_commit,omitempty"`
	PushSource           PushSource `protobuf:"varint,4,opt,name=push_source,json=pushSource,proto3,enum=PushNotification.ops.PushSource" json:"push_source,omitempty"`
	Seqid                uint64     `protobuf:"varint,5,opt,name=seqid,proto3" json:"seqid,omitempty"`
	Priority             uint32     `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`
	PushType             PushType   `protobuf:"varint,7,opt,name=push_type,json=pushType,proto3,enum=PushNotification.PushType" json:"push_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchTargetUidListReq) Reset()         { *m = BatchTargetUidListReq{} }
func (m *BatchTargetUidListReq) String() string { return proto.CompactTextString(m) }
func (*BatchTargetUidListReq) ProtoMessage()    {}
func (*BatchTargetUidListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_ops_c3795966f4eed898, []int{2}
}
func (m *BatchTargetUidListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchTargetUidListReq.Unmarshal(m, b)
}
func (m *BatchTargetUidListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchTargetUidListReq.Marshal(b, m, deterministic)
}
func (dst *BatchTargetUidListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchTargetUidListReq.Merge(dst, src)
}
func (m *BatchTargetUidListReq) XXX_Size() int {
	return xxx_messageInfo_BatchTargetUidListReq.Size(m)
}
func (m *BatchTargetUidListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchTargetUidListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchTargetUidListReq proto.InternalMessageInfo

func (m *BatchTargetUidListReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *BatchTargetUidListReq) GetTargetIdList() []string {
	if m != nil {
		return m.TargetIdList
	}
	return nil
}

func (m *BatchTargetUidListReq) GetLatestCommit() bool {
	if m != nil {
		return m.LatestCommit
	}
	return false
}

func (m *BatchTargetUidListReq) GetPushSource() PushSource {
	if m != nil {
		return m.PushSource
	}
	return PushSource_SOURCE_UNDEFINED
}

func (m *BatchTargetUidListReq) GetSeqid() uint64 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

func (m *BatchTargetUidListReq) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *BatchTargetUidListReq) GetPushType() PushType {
	if m != nil {
		return m.PushType
	}
	return PushType_Normal
}

type BatchTargetUidListResp struct {
	Result               uint32   `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchTargetUidListResp) Reset()         { *m = BatchTargetUidListResp{} }
func (m *BatchTargetUidListResp) String() string { return proto.CompactTextString(m) }
func (*BatchTargetUidListResp) ProtoMessage()    {}
func (*BatchTargetUidListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_ops_c3795966f4eed898, []int{3}
}
func (m *BatchTargetUidListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchTargetUidListResp.Unmarshal(m, b)
}
func (m *BatchTargetUidListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchTargetUidListResp.Marshal(b, m, deterministic)
}
func (dst *BatchTargetUidListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchTargetUidListResp.Merge(dst, src)
}
func (m *BatchTargetUidListResp) XXX_Size() int {
	return xxx_messageInfo_BatchTargetUidListResp.Size(m)
}
func (m *BatchTargetUidListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchTargetUidListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchTargetUidListResp proto.InternalMessageInfo

func (m *BatchTargetUidListResp) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func init() {
	proto.RegisterType((*SendPushReq)(nil), "PushNotification.ops.SendPushReq")
	proto.RegisterType((*SendPushReq_BaseInfo)(nil), "PushNotification.ops.SendPushReq.BaseInfo")
	proto.RegisterMapType((map[string]string)(nil), "PushNotification.ops.SendPushReq.BaseInfo.ExtraEntry")
	proto.RegisterType((*SendPushReq_Condition)(nil), "PushNotification.ops.SendPushReq.Condition")
	proto.RegisterType((*SendPushResp)(nil), "PushNotification.ops.SendPushResp")
	proto.RegisterType((*BatchTargetUidListReq)(nil), "PushNotification.ops.BatchTargetUidListReq")
	proto.RegisterType((*BatchTargetUidListResp)(nil), "PushNotification.ops.BatchTargetUidListResp")
	proto.RegisterEnum("PushNotification.ops.PushSource", PushSource_name, PushSource_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PushOpsClient is the client API for PushOps service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PushOpsClient interface {
	// 批量推送任务
	// -Step 1, 创建任务
	SendPush(ctx context.Context, in *SendPushReq, opts ...grpc.CallOption) (*SendPushResp, error)
	// -Step 2, 指定推送目标
	BatchTargetUidList(ctx context.Context, in *BatchTargetUidListReq, opts ...grpc.CallOption) (*BatchTargetUidListResp, error)
}

type pushOpsClient struct {
	cc *grpc.ClientConn
}

func NewPushOpsClient(cc *grpc.ClientConn) PushOpsClient {
	return &pushOpsClient{cc}
}

func (c *pushOpsClient) SendPush(ctx context.Context, in *SendPushReq, opts ...grpc.CallOption) (*SendPushResp, error) {
	out := new(SendPushResp)
	err := c.cc.Invoke(ctx, "/PushNotification.ops.PushOps/SendPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushOpsClient) BatchTargetUidList(ctx context.Context, in *BatchTargetUidListReq, opts ...grpc.CallOption) (*BatchTargetUidListResp, error) {
	out := new(BatchTargetUidListResp)
	err := c.cc.Invoke(ctx, "/PushNotification.ops.PushOps/BatchTargetUidList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushOpsServer is the server API for PushOps service.
type PushOpsServer interface {
	// 批量推送任务
	// -Step 1, 创建任务
	SendPush(context.Context, *SendPushReq) (*SendPushResp, error)
	// -Step 2, 指定推送目标
	BatchTargetUidList(context.Context, *BatchTargetUidListReq) (*BatchTargetUidListResp, error)
}

func RegisterPushOpsServer(s *grpc.Server, srv PushOpsServer) {
	s.RegisterService(&_PushOps_serviceDesc, srv)
}

func _PushOps_SendPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushOpsServer).SendPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.ops.PushOps/SendPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushOpsServer).SendPush(ctx, req.(*SendPushReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushOps_BatchTargetUidList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchTargetUidListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushOpsServer).BatchTargetUidList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.ops.PushOps/BatchTargetUidList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushOpsServer).BatchTargetUidList(ctx, req.(*BatchTargetUidListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PushOps_serviceDesc = grpc.ServiceDesc{
	ServiceName: "PushNotification.ops.PushOps",
	HandlerType: (*PushOpsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendPush",
			Handler:    _PushOps_SendPush_Handler,
		},
		{
			MethodName: "BatchTargetUidList",
			Handler:    _PushOps_BatchTargetUidList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "push-notification/v3/push-ops.proto",
}

func init() {
	proto.RegisterFile("push-notification/v3/push-ops.proto", fileDescriptor_push_ops_c3795966f4eed898)
}

var fileDescriptor_push_ops_c3795966f4eed898 = []byte{
	// 1012 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x56, 0xdd, 0x72, 0xdb, 0x44,
	0x14, 0xae, 0x9c, 0xc4, 0xb6, 0x8e, 0x93, 0xe0, 0x6e, 0xfe, 0x54, 0x07, 0xa8, 0xeb, 0x32, 0xe0,
	0x29, 0xe0, 0x30, 0xee, 0x74, 0xe8, 0xc0, 0x05, 0xd3, 0xd8, 0x2e, 0xa3, 0x81, 0x71, 0xc2, 0x5a,
	0xe6, 0x82, 0x1b, 0x8d, 0x22, 0x6d, 0x92, 0xa5, 0xb2, 0x56, 0xd1, 0xae, 0x3d, 0xf5, 0x1d, 0x4f,
	0xc0, 0x1b, 0xf0, 0x00, 0x3c, 0x0e, 0x77, 0x3c, 0x0e, 0xb3, 0x67, 0x65, 0x47, 0x21, 0xce, 0x24,
	0xd3, 0xbb, 0x3d, 0xdf, 0xf9, 0xd1, 0xf1, 0xb7, 0xe7, 0x3b, 0x5e, 0x78, 0x9e, 0x4e, 0xe5, 0xe5,
	0xd7, 0x89, 0x50, 0xfc, 0x9c, 0x87, 0x81, 0xe2, 0x22, 0x39, 0x9a, 0xbd, 0x3c, 0x42, 0x50, 0xa4,
	0xb2, 0x93, 0x66, 0x42, 0x09, 0xb2, 0x7b, 0x3a, 0x95, 0x97, 0xc3, 0x42, 0x4c, 0x47, 0xa4, 0xb2,
	0xf1, 0xf9, 0xdd, 0xa9, 0x92, 0x65, 0x33, 0x96, 0x99, 0xec, 0xd6, 0xbf, 0x00, 0xb5, 0x11, 0x4b,
	0x22, 0x5d, 0x84, 0xb2, 0x2b, 0xf2, 0x23, 0xd8, 0x67, 0x81, 0x64, 0x3e, 0x4f, 0xce, 0x85, 0x63,
	0x35, 0xad, 0x76, 0xad, 0xfb, 0xa2, 0xb3, 0xea, 0x0b, 0x9d, 0x42, 0x56, 0xe7, 0x38, 0x90, 0xcc,
	0x4d, 0xce, 0x05, 0xad, 0x9e, 0xe5, 0x27, 0xf2, 0x06, 0x6a, 0xfa, 0x6b, 0xbe, 0x14, 0xd3, 0x2c,
	0x64, 0x4e, 0xa9, 0x69, 0xb5, 0xb7, 0xbb, 0xcd, 0xd5, 0xa5, 0x34, 0x38, 0xc2, 0x38, 0x0a, 0xe9,
	0xf2, 0x4c, 0x5c, 0xb0, 0x43, 0x91, 0x44, 0x5c, 0xc7, 0x39, 0x6b, 0xd8, 0xcb, 0x97, 0xf7, 0xf7,
	0xd2, 0x5b, 0xa4, 0xd0, 0xeb, 0x6c, 0xf2, 0x19, 0x6c, 0xab, 0x20, 0xbb, 0x60, 0xca, 0xe7, 0x91,
	0x1f, 0x73, 0xa9, 0x9c, 0xf5, 0xe6, 0x5a, 0xdb, 0xa6, 0x9b, 0x06, 0x75, 0xa3, 0x9f, 0xb9, 0x54,
	0x84, 0xc0, 0xba, 0x62, 0x52, 0x39, 0x1b, 0x4d, 0xab, 0x5d, 0xa5, 0x78, 0x26, 0xcf, 0x60, 0x93,
	0x27, 0xb3, 0x20, 0xe6, 0x91, 0xaf, 0xf8, 0x84, 0x39, 0xe5, 0xa6, 0xd5, 0xde, 0xa2, 0xb5, 0x1c,
	0xf3, 0xf8, 0x84, 0x91, 0x2e, 0xec, 0x9d, 0x05, 0x2a, 0xbc, 0x64, 0xd2, 0x9f, 0xa6, 0xb1, 0x08,
	0x22, 0xdf, 0x54, 0x75, 0x2a, 0x58, 0x67, 0x27, 0x77, 0x8e, 0xd1, 0xe7, 0xa1, 0x8b, 0xec, 0xc2,
	0x86, 0x64, 0x57, 0x3c, 0x72, 0xaa, 0x4d, 0xab, 0xbd, 0x4e, 0x8d, 0x41, 0x1a, 0x50, 0x4d, 0x33,
	0x2e, 0x32, 0xae, 0xe6, 0x8e, 0x8d, 0x1f, 0x5a, 0xda, 0x8d, 0x3f, 0x37, 0xa0, 0xba, 0xe0, 0x19,
	0x03, 0xe3, 0x40, 0x9d, 0x8b, 0x6c, 0x82, 0xb7, 0xa4, 0x03, 0x73, 0x5b, 0x97, 0x56, 0x5c, 0xc5,
	0x86, 0x73, 0x9b, 0x1a, 0x83, 0x38, 0x50, 0x09, 0x45, 0xa2, 0x58, 0xa2, 0x90, 0x4a, 0x9b, 0x2e,
	0x4c, 0xf2, 0x04, 0xaa, 0xbf, 0x4f, 0x27, 0xa9, 0x3f, 0xcd, 0x62, 0x67, 0xdd, 0xb8, 0xb4, 0x3d,
	0xce, 0x62, 0xf2, 0x14, 0x6a, 0x38, 0x42, 0x73, 0x5f, 0xcd, 0x53, 0x86, 0xbc, 0x6c, 0x51, 0x30,
	0x90, 0x37, 0x4f, 0x99, 0x66, 0x27, 0x48, 0x53, 0x7f, 0xd9, 0x4b, 0xce, 0x4e, 0x90, 0xa6, 0xa7,
	0x8b, 0x76, 0x7e, 0x01, 0x52, 0x1c, 0x43, 0xdf, 0xf4, 0xb6, 0xd7, 0x5c, 0x6b, 0xd7, 0xba, 0xad,
	0xdb, 0xd7, 0x59, 0x34, 0x3c, 0xf6, 0x5e, 0xd1, 0xc7, 0xc5, 0x6c, 0x0f, 0x7f, 0xcb, 0x18, 0x76,
	0x6f, 0x94, 0x5c, 0xfc, 0xb0, 0xfd, 0x07, 0x17, 0xdd, 0x29, 0xe6, 0xf7, 0x72, 0x22, 0xf6, 0xa1,
	0x1c, 0x07, 0x67, 0x2c, 0x96, 0xce, 0x01, 0x0e, 0x47, 0x6e, 0x69, 0x82, 0x78, 0x28, 0x12, 0x24,
	0xc8, 0x31, 0x04, 0x69, 0x5b, 0x13, 0xf4, 0x09, 0xc0, 0x4c, 0xf0, 0x90, 0x19, 0x7e, 0x9e, 0xa0,
	0xd3, 0x46, 0x04, 0xe9, 0x39, 0x04, 0x1b, 0x33, 0xd1, 0xdb, 0x40, 0x2f, 0x96, 0x42, 0xe7, 0xb7,
	0x60, 0xa3, 0x42, 0xd0, 0x79, 0x88, 0xfa, 0x68, 0xdc, 0x6e, 0x5d, 0x03, 0x3a, 0x9c, 0x56, 0xd3,
	0xfc, 0x44, 0x7e, 0x82, 0x0d, 0xf6, 0x5e, 0x65, 0x81, 0xf3, 0x31, 0xfe, 0xde, 0x57, 0x0f, 0xd7,
	0x67, 0x67, 0xa0, 0xf3, 0x06, 0x89, 0xca, 0xe6, 0xd4, 0xd4, 0x68, 0xbc, 0x06, 0xb8, 0x06, 0x49,
	0x1d, 0xd6, 0xde, 0xb1, 0x39, 0x8e, 0x94, 0x4d, 0xf5, 0x51, 0x4f, 0xd3, 0x2c, 0x88, 0xa7, 0xcb,
	0x69, 0x42, 0xe3, 0xbb, 0xd2, 0x6b, 0xab, 0xf1, 0x57, 0x09, 0xec, 0xa5, 0xd8, 0x48, 0x1b, 0xea,
	0xb1, 0xb8, 0xe0, 0x09, 0xaa, 0xc4, 0x97, 0x2a, 0xc8, 0x54, 0x5e, 0x66, 0x1b, 0x71, 0xad, 0x94,
	0x91, 0x46, 0xb5, 0x16, 0x0b, 0x91, 0x2c, 0x89, 0xf2, 0xd2, 0x9b, 0xcb, 0xb8, 0x41, 0x12, 0x91,
	0x0e, 0xec, 0x98, 0x28, 0x9e, 0x84, 0xf1, 0x34, 0x62, 0xbe, 0x12, 0x51, 0x30, 0xc7, 0xd9, 0xad,
	0xd2, 0xc7, 0xe8, 0x72, 0x8d, 0xc7, 0xd3, 0x0e, 0x7d, 0x79, 0x17, 0x2c, 0x89, 0x58, 0x86, 0x33,
	0xbc, 0x45, 0x73, 0xcb, 0x48, 0x4a, 0xcc, 0x78, 0x12, 0x9a, 0xf9, 0xb5, 0xe9, 0xd2, 0xd6, 0x7a,
	0x0f, 0xb5, 0xd4, 0xca, 0x88, 0xe3, 0x99, 0x7c, 0x01, 0x1f, 0x05, 0x49, 0x94, 0x09, 0x1e, 0xf9,
	0x33, 0x96, 0x49, 0xbd, 0x7a, 0x2a, 0x58, 0x70, 0x3b, 0x87, 0x7f, 0x35, 0xa8, 0xd6, 0x06, 0x17,
	0x72, 0x19, 0x54, 0x35, 0xda, 0xe0, 0x42, 0xe6, 0x01, 0xad, 0x1f, 0x60, 0xf3, 0xfa, 0x0e, 0x64,
	0x4a, 0x0e, 0xa0, 0xa2, 0x02, 0xf9, 0xce, 0xe7, 0x51, 0x4e, 0x4c, 0x59, 0x9b, 0x6e, 0xa4, 0x5b,
	0xcf, 0x98, 0x9c, 0xc6, 0x0a, 0x89, 0xd8, 0xa2, 0xb9, 0xd5, 0xfa, 0xbb, 0x04, 0x7b, 0xc7, 0x7a,
	0x77, 0x98, 0x9d, 0x31, 0xe6, 0xb8, 0xa5, 0xf4, 0x96, 0xbe, 0xb3, 0xd4, 0xed, 0x3d, 0x57, 0x5a,
	0xb1, 0xe7, 0x9e, 0xc3, 0x56, 0x1c, 0xe8, 0xed, 0xe6, 0x87, 0x62, 0x32, 0xe1, 0x2a, 0x67, 0x75,
	0xd3, 0x80, 0x3d, 0xc4, 0xfe, 0xbf, 0xc0, 0xd7, 0x3f, 0x60, 0x81, 0x2f, 0x97, 0xdc, 0xc6, 0x5d,
	0x4b, 0xae, 0x7c, 0x73, 0xc9, 0xdd, 0xd4, 0x44, 0xe5, 0xe1, 0x9a, 0x68, 0x7d, 0x03, 0xfb, 0xab,
	0xa8, 0x92, 0x69, 0x81, 0x5d, 0xab, 0xc8, 0xee, 0x8b, 0x3f, 0x2c, 0x80, 0xd3, 0x62, 0xaf, 0xf5,
	0xd1, 0xc9, 0x98, 0xf6, 0x06, 0xfe, 0x78, 0xd8, 0x1f, 0xbc, 0x75, 0x87, 0x83, 0x7e, 0xfd, 0x11,
	0xf9, 0x14, 0x1a, 0x39, 0x3a, 0x3a, 0x1d, 0xf4, 0xdc, 0xb7, 0xee, 0xa0, 0xef, 0xf7, 0x4e, 0x86,
	0x7d, 0xd7, 0x73, 0x4f, 0x86, 0x75, 0x8b, 0x3c, 0x85, 0xc3, 0x5b, 0x7e, 0xcf, 0xf3, 0xdf, 0xf4,
	0x7a, 0x27, 0xe3, 0xa1, 0x57, 0x2f, 0x91, 0x43, 0x38, 0x58, 0x15, 0x30, 0x76, 0xfb, 0xf5, 0xb5,
	0xee, 0x3f, 0x16, 0x54, 0x74, 0x0b, 0x27, 0xa9, 0x24, 0x23, 0xa8, 0x2e, 0xa6, 0x85, 0x3c, 0xbb,
	0x57, 0xd1, 0x8d, 0xd6, 0x7d, 0x21, 0x32, 0x6d, 0x3d, 0x22, 0x57, 0x40, 0x6e, 0xb3, 0x42, 0xee,
	0xf8, 0x13, 0x5d, 0x39, 0x6a, 0x8d, 0xaf, 0x1e, 0x1e, 0xac, 0x3f, 0x79, 0xdc, 0xff, 0xed, 0xf8,
	0x42, 0xc4, 0x41, 0x72, 0xd1, 0x79, 0xd5, 0x55, 0xaa, 0x13, 0x8a, 0xc9, 0x11, 0xbe, 0x34, 0x42,
	0x11, 0x1f, 0xe9, 0x97, 0x07, 0x0f, 0x99, 0x3c, 0x5a, 0xf5, 0x38, 0xf9, 0xde, 0x4c, 0x1b, 0x3e,
	0x4e, 0xce, 0xca, 0x98, 0xf3, 0xf2, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xaa, 0x94, 0x9c, 0xd2,
	0x02, 0x09, 0x00, 0x00,
}
