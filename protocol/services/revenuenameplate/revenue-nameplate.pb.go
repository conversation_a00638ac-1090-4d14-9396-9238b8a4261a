// Code generated by protoc-gen-go. DO NOT EDIT.
// source: revenue-nameplate/revenue-nameplate.proto

package revenuenameplate // import "golang.52tt.com/protocol/services/revenuenameplate"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// NameplateType 用户铭牌资源类型
type NameplateType int32

const (
	NameplateType_TYPE_ERROR  NameplateType = 0
	NameplateType_TYPE_LOTTER NameplateType = 1
)

var NameplateType_name = map[int32]string{
	0: "TYPE_ERROR",
	1: "TYPE_LOTTER",
}
var NameplateType_value = map[string]int32{
	"TYPE_ERROR":  0,
	"TYPE_LOTTER": 1,
}

func (x NameplateType) String() string {
	return proto.EnumName(NameplateType_name, int32(x))
}
func (NameplateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{0}
}

// ScenesType 场景类型
type ScenesType int32

const (
	ScenesType_SCENES_NORMAL_TYPE      ScenesType = 0
	ScenesType_SCENES_ROOM_FOLLOW_TYPE ScenesType = 1
)

var ScenesType_name = map[int32]string{
	0: "SCENES_NORMAL_TYPE",
	1: "SCENES_ROOM_FOLLOW_TYPE",
}
var ScenesType_value = map[string]int32{
	"SCENES_NORMAL_TYPE":      0,
	"SCENES_ROOM_FOLLOW_TYPE": 1,
}

func (x ScenesType) String() string {
	return proto.EnumName(ScenesType_name, int32(x))
}
func (ScenesType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{1}
}

// RevenueNameplateInfo 营收管理后台铭牌结构
type RevenueNameplateInfo struct {
	Id                   uint32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	BaseUrl              string        `protobuf:"bytes,3,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	DynamicUrl           string        `protobuf:"bytes,4,opt,name=dynamic_url,json=dynamicUrl,proto3" json:"dynamic_url,omitempty"`
	Type                 NameplateType `protobuf:"varint,5,opt,name=type,proto3,enum=revenuenameplate.NameplateType" json:"type,omitempty"`
	IsFollowNeed         bool          `protobuf:"varint,6,opt,name=is_follow_need,json=isFollowNeed,proto3" json:"is_follow_need,omitempty"`
	RewardTime           uint32        `protobuf:"varint,7,opt,name=reward_time,json=rewardTime,proto3" json:"reward_time,omitempty"`
	Remark               string        `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`
	OptTime              uint32        `protobuf:"varint,9,opt,name=opt_time,json=optTime,proto3" json:"opt_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RevenueNameplateInfo) Reset()         { *m = RevenueNameplateInfo{} }
func (m *RevenueNameplateInfo) String() string { return proto.CompactTextString(m) }
func (*RevenueNameplateInfo) ProtoMessage()    {}
func (*RevenueNameplateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{0}
}
func (m *RevenueNameplateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevenueNameplateInfo.Unmarshal(m, b)
}
func (m *RevenueNameplateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevenueNameplateInfo.Marshal(b, m, deterministic)
}
func (dst *RevenueNameplateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevenueNameplateInfo.Merge(dst, src)
}
func (m *RevenueNameplateInfo) XXX_Size() int {
	return xxx_messageInfo_RevenueNameplateInfo.Size(m)
}
func (m *RevenueNameplateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RevenueNameplateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RevenueNameplateInfo proto.InternalMessageInfo

func (m *RevenueNameplateInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RevenueNameplateInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RevenueNameplateInfo) GetBaseUrl() string {
	if m != nil {
		return m.BaseUrl
	}
	return ""
}

func (m *RevenueNameplateInfo) GetDynamicUrl() string {
	if m != nil {
		return m.DynamicUrl
	}
	return ""
}

func (m *RevenueNameplateInfo) GetType() NameplateType {
	if m != nil {
		return m.Type
	}
	return NameplateType_TYPE_ERROR
}

func (m *RevenueNameplateInfo) GetIsFollowNeed() bool {
	if m != nil {
		return m.IsFollowNeed
	}
	return false
}

func (m *RevenueNameplateInfo) GetRewardTime() uint32 {
	if m != nil {
		return m.RewardTime
	}
	return 0
}

func (m *RevenueNameplateInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *RevenueNameplateInfo) GetOptTime() uint32 {
	if m != nil {
		return m.OptTime
	}
	return 0
}

// NameplateDetailInfo 铭牌信息结构
type NameplateDetailInfo struct {
	Id                   uint32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	BaseUrl              string        `protobuf:"bytes,3,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	DynamicUrl           string        `protobuf:"bytes,4,opt,name=dynamic_url,json=dynamicUrl,proto3" json:"dynamic_url,omitempty"`
	Type                 NameplateType `protobuf:"varint,5,opt,name=type,proto3,enum=revenuenameplate.NameplateType" json:"type,omitempty"`
	StartTime            uint32        `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	FinishTime           uint32        `protobuf:"varint,7,opt,name=finish_time,json=finishTime,proto3" json:"finish_time,omitempty"`
	IsUse                bool          `protobuf:"varint,8,opt,name=is_use,json=isUse,proto3" json:"is_use,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *NameplateDetailInfo) Reset()         { *m = NameplateDetailInfo{} }
func (m *NameplateDetailInfo) String() string { return proto.CompactTextString(m) }
func (*NameplateDetailInfo) ProtoMessage()    {}
func (*NameplateDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{1}
}
func (m *NameplateDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NameplateDetailInfo.Unmarshal(m, b)
}
func (m *NameplateDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NameplateDetailInfo.Marshal(b, m, deterministic)
}
func (dst *NameplateDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NameplateDetailInfo.Merge(dst, src)
}
func (m *NameplateDetailInfo) XXX_Size() int {
	return xxx_messageInfo_NameplateDetailInfo.Size(m)
}
func (m *NameplateDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NameplateDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NameplateDetailInfo proto.InternalMessageInfo

func (m *NameplateDetailInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NameplateDetailInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NameplateDetailInfo) GetBaseUrl() string {
	if m != nil {
		return m.BaseUrl
	}
	return ""
}

func (m *NameplateDetailInfo) GetDynamicUrl() string {
	if m != nil {
		return m.DynamicUrl
	}
	return ""
}

func (m *NameplateDetailInfo) GetType() NameplateType {
	if m != nil {
		return m.Type
	}
	return NameplateType_TYPE_ERROR
}

func (m *NameplateDetailInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *NameplateDetailInfo) GetFinishTime() uint32 {
	if m != nil {
		return m.FinishTime
	}
	return 0
}

func (m *NameplateDetailInfo) GetIsUse() bool {
	if m != nil {
		return m.IsUse
	}
	return false
}

type SetNameplateReq struct {
	Nameplate            *RevenueNameplateInfo `protobuf:"bytes,1,opt,name=nameplate,proto3" json:"nameplate,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SetNameplateReq) Reset()         { *m = SetNameplateReq{} }
func (m *SetNameplateReq) String() string { return proto.CompactTextString(m) }
func (*SetNameplateReq) ProtoMessage()    {}
func (*SetNameplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{2}
}
func (m *SetNameplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNameplateReq.Unmarshal(m, b)
}
func (m *SetNameplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNameplateReq.Marshal(b, m, deterministic)
}
func (dst *SetNameplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNameplateReq.Merge(dst, src)
}
func (m *SetNameplateReq) XXX_Size() int {
	return xxx_messageInfo_SetNameplateReq.Size(m)
}
func (m *SetNameplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNameplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetNameplateReq proto.InternalMessageInfo

func (m *SetNameplateReq) GetNameplate() *RevenueNameplateInfo {
	if m != nil {
		return m.Nameplate
	}
	return nil
}

type SetNameplateResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNameplateResp) Reset()         { *m = SetNameplateResp{} }
func (m *SetNameplateResp) String() string { return proto.CompactTextString(m) }
func (*SetNameplateResp) ProtoMessage()    {}
func (*SetNameplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{3}
}
func (m *SetNameplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNameplateResp.Unmarshal(m, b)
}
func (m *SetNameplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNameplateResp.Marshal(b, m, deterministic)
}
func (dst *SetNameplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNameplateResp.Merge(dst, src)
}
func (m *SetNameplateResp) XXX_Size() int {
	return xxx_messageInfo_SetNameplateResp.Size(m)
}
func (m *SetNameplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNameplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetNameplateResp proto.InternalMessageInfo

func (m *SetNameplateResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type UpdateNameplateReq struct {
	Nameplate            *RevenueNameplateInfo `protobuf:"bytes,1,opt,name=nameplate,proto3" json:"nameplate,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UpdateNameplateReq) Reset()         { *m = UpdateNameplateReq{} }
func (m *UpdateNameplateReq) String() string { return proto.CompactTextString(m) }
func (*UpdateNameplateReq) ProtoMessage()    {}
func (*UpdateNameplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{4}
}
func (m *UpdateNameplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNameplateReq.Unmarshal(m, b)
}
func (m *UpdateNameplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNameplateReq.Marshal(b, m, deterministic)
}
func (dst *UpdateNameplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNameplateReq.Merge(dst, src)
}
func (m *UpdateNameplateReq) XXX_Size() int {
	return xxx_messageInfo_UpdateNameplateReq.Size(m)
}
func (m *UpdateNameplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNameplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNameplateReq proto.InternalMessageInfo

func (m *UpdateNameplateReq) GetNameplate() *RevenueNameplateInfo {
	if m != nil {
		return m.Nameplate
	}
	return nil
}

type UpdateNameplateResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNameplateResp) Reset()         { *m = UpdateNameplateResp{} }
func (m *UpdateNameplateResp) String() string { return proto.CompactTextString(m) }
func (*UpdateNameplateResp) ProtoMessage()    {}
func (*UpdateNameplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{5}
}
func (m *UpdateNameplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNameplateResp.Unmarshal(m, b)
}
func (m *UpdateNameplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNameplateResp.Marshal(b, m, deterministic)
}
func (dst *UpdateNameplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNameplateResp.Merge(dst, src)
}
func (m *UpdateNameplateResp) XXX_Size() int {
	return xxx_messageInfo_UpdateNameplateResp.Size(m)
}
func (m *UpdateNameplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNameplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNameplateResp proto.InternalMessageInfo

func (m *UpdateNameplateResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetAllNameplatesReq struct {
	Limit                uint32   `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Id                   uint32   `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllNameplatesReq) Reset()         { *m = GetAllNameplatesReq{} }
func (m *GetAllNameplatesReq) String() string { return proto.CompactTextString(m) }
func (*GetAllNameplatesReq) ProtoMessage()    {}
func (*GetAllNameplatesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{6}
}
func (m *GetAllNameplatesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllNameplatesReq.Unmarshal(m, b)
}
func (m *GetAllNameplatesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllNameplatesReq.Marshal(b, m, deterministic)
}
func (dst *GetAllNameplatesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllNameplatesReq.Merge(dst, src)
}
func (m *GetAllNameplatesReq) XXX_Size() int {
	return xxx_messageInfo_GetAllNameplatesReq.Size(m)
}
func (m *GetAllNameplatesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllNameplatesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllNameplatesReq proto.InternalMessageInfo

func (m *GetAllNameplatesReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAllNameplatesReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAllNameplatesReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetAllNameplatesReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type GetAllNameplatesResp struct {
	Nameplates           []*RevenueNameplateInfo `protobuf:"bytes,1,rep,name=nameplates,proto3" json:"nameplates,omitempty"`
	Limit                uint32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Page                 uint32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32                  `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetAllNameplatesResp) Reset()         { *m = GetAllNameplatesResp{} }
func (m *GetAllNameplatesResp) String() string { return proto.CompactTextString(m) }
func (*GetAllNameplatesResp) ProtoMessage()    {}
func (*GetAllNameplatesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{7}
}
func (m *GetAllNameplatesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllNameplatesResp.Unmarshal(m, b)
}
func (m *GetAllNameplatesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllNameplatesResp.Marshal(b, m, deterministic)
}
func (dst *GetAllNameplatesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllNameplatesResp.Merge(dst, src)
}
func (m *GetAllNameplatesResp) XXX_Size() int {
	return xxx_messageInfo_GetAllNameplatesResp.Size(m)
}
func (m *GetAllNameplatesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllNameplatesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllNameplatesResp proto.InternalMessageInfo

func (m *GetAllNameplatesResp) GetNameplates() []*RevenueNameplateInfo {
	if m != nil {
		return m.Nameplates
	}
	return nil
}

func (m *GetAllNameplatesResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAllNameplatesResp) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAllNameplatesResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetNamePlateReq struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNamePlateReq) Reset()         { *m = GetNamePlateReq{} }
func (m *GetNamePlateReq) String() string { return proto.CompactTextString(m) }
func (*GetNamePlateReq) ProtoMessage()    {}
func (*GetNamePlateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{8}
}
func (m *GetNamePlateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNamePlateReq.Unmarshal(m, b)
}
func (m *GetNamePlateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNamePlateReq.Marshal(b, m, deterministic)
}
func (dst *GetNamePlateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNamePlateReq.Merge(dst, src)
}
func (m *GetNamePlateReq) XXX_Size() int {
	return xxx_messageInfo_GetNamePlateReq.Size(m)
}
func (m *GetNamePlateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNamePlateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNamePlateReq proto.InternalMessageInfo

func (m *GetNamePlateReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type GetNamePlateResp struct {
	Nameplates           []*RevenueNameplateInfo `protobuf:"bytes,1,rep,name=nameplates,proto3" json:"nameplates,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetNamePlateResp) Reset()         { *m = GetNamePlateResp{} }
func (m *GetNamePlateResp) String() string { return proto.CompactTextString(m) }
func (*GetNamePlateResp) ProtoMessage()    {}
func (*GetNamePlateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{9}
}
func (m *GetNamePlateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNamePlateResp.Unmarshal(m, b)
}
func (m *GetNamePlateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNamePlateResp.Marshal(b, m, deterministic)
}
func (dst *GetNamePlateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNamePlateResp.Merge(dst, src)
}
func (m *GetNamePlateResp) XXX_Size() int {
	return xxx_messageInfo_GetNamePlateResp.Size(m)
}
func (m *GetNamePlateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNamePlateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNamePlateResp proto.InternalMessageInfo

func (m *GetNamePlateResp) GetNameplates() []*RevenueNameplateInfo {
	if m != nil {
		return m.Nameplates
	}
	return nil
}

type AssignRecord struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	NameplateId          uint32   `protobuf:"varint,2,opt,name=nameplate_id,json=nameplateId,proto3" json:"nameplate_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	TtId                 string   `protobuf:"bytes,4,opt,name=tt_id,json=ttId,proto3" json:"tt_id,omitempty"`
	RewardTime           uint64   `protobuf:"varint,5,opt,name=reward_time,json=rewardTime,proto3" json:"reward_time,omitempty"`
	IsAssign             uint32   `protobuf:"varint,6,opt,name=is_assign,json=isAssign,proto3" json:"is_assign,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AssignRecord) Reset()         { *m = AssignRecord{} }
func (m *AssignRecord) String() string { return proto.CompactTextString(m) }
func (*AssignRecord) ProtoMessage()    {}
func (*AssignRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{10}
}
func (m *AssignRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssignRecord.Unmarshal(m, b)
}
func (m *AssignRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssignRecord.Marshal(b, m, deterministic)
}
func (dst *AssignRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssignRecord.Merge(dst, src)
}
func (m *AssignRecord) XXX_Size() int {
	return xxx_messageInfo_AssignRecord.Size(m)
}
func (m *AssignRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AssignRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AssignRecord proto.InternalMessageInfo

func (m *AssignRecord) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AssignRecord) GetNameplateId() uint32 {
	if m != nil {
		return m.NameplateId
	}
	return 0
}

func (m *AssignRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AssignRecord) GetTtId() string {
	if m != nil {
		return m.TtId
	}
	return ""
}

func (m *AssignRecord) GetRewardTime() uint64 {
	if m != nil {
		return m.RewardTime
	}
	return 0
}

func (m *AssignRecord) GetIsAssign() uint32 {
	if m != nil {
		return m.IsAssign
	}
	return 0
}

type AssignNamePlateReq struct {
	Record               []*AssignRecord `protobuf:"bytes,1,rep,name=record,proto3" json:"record,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AssignNamePlateReq) Reset()         { *m = AssignNamePlateReq{} }
func (m *AssignNamePlateReq) String() string { return proto.CompactTextString(m) }
func (*AssignNamePlateReq) ProtoMessage()    {}
func (*AssignNamePlateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{11}
}
func (m *AssignNamePlateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssignNamePlateReq.Unmarshal(m, b)
}
func (m *AssignNamePlateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssignNamePlateReq.Marshal(b, m, deterministic)
}
func (dst *AssignNamePlateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssignNamePlateReq.Merge(dst, src)
}
func (m *AssignNamePlateReq) XXX_Size() int {
	return xxx_messageInfo_AssignNamePlateReq.Size(m)
}
func (m *AssignNamePlateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AssignNamePlateReq.DiscardUnknown(m)
}

var xxx_messageInfo_AssignNamePlateReq proto.InternalMessageInfo

func (m *AssignNamePlateReq) GetRecord() []*AssignRecord {
	if m != nil {
		return m.Record
	}
	return nil
}

type AssignNamePlateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AssignNamePlateResp) Reset()         { *m = AssignNamePlateResp{} }
func (m *AssignNamePlateResp) String() string { return proto.CompactTextString(m) }
func (*AssignNamePlateResp) ProtoMessage()    {}
func (*AssignNamePlateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{12}
}
func (m *AssignNamePlateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssignNamePlateResp.Unmarshal(m, b)
}
func (m *AssignNamePlateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssignNamePlateResp.Marshal(b, m, deterministic)
}
func (dst *AssignNamePlateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssignNamePlateResp.Merge(dst, src)
}
func (m *AssignNamePlateResp) XXX_Size() int {
	return xxx_messageInfo_AssignNamePlateResp.Size(m)
}
func (m *AssignNamePlateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AssignNamePlateResp.DiscardUnknown(m)
}

var xxx_messageInfo_AssignNamePlateResp proto.InternalMessageInfo

type GetAssignRecordReq struct {
	Limit                uint32   `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAssignRecordReq) Reset()         { *m = GetAssignRecordReq{} }
func (m *GetAssignRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetAssignRecordReq) ProtoMessage()    {}
func (*GetAssignRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{13}
}
func (m *GetAssignRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAssignRecordReq.Unmarshal(m, b)
}
func (m *GetAssignRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAssignRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetAssignRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAssignRecordReq.Merge(dst, src)
}
func (m *GetAssignRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetAssignRecordReq.Size(m)
}
func (m *GetAssignRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAssignRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAssignRecordReq proto.InternalMessageInfo

func (m *GetAssignRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAssignRecordReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

type GetAssignRecordResp struct {
	Record               []*AssignRecord `protobuf:"bytes,1,rep,name=record,proto3" json:"record,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAssignRecordResp) Reset()         { *m = GetAssignRecordResp{} }
func (m *GetAssignRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetAssignRecordResp) ProtoMessage()    {}
func (*GetAssignRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{14}
}
func (m *GetAssignRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAssignRecordResp.Unmarshal(m, b)
}
func (m *GetAssignRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAssignRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetAssignRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAssignRecordResp.Merge(dst, src)
}
func (m *GetAssignRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetAssignRecordResp.Size(m)
}
func (m *GetAssignRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAssignRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAssignRecordResp proto.InternalMessageInfo

func (m *GetAssignRecordResp) GetRecord() []*AssignRecord {
	if m != nil {
		return m.Record
	}
	return nil
}

type ActivityAssignNameplateReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	NameplateId          uint32   `protobuf:"varint,2,opt,name=nameplate_id,json=nameplateId,proto3" json:"nameplate_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	RewardTime           int64    `protobuf:"varint,5,opt,name=reward_time,json=rewardTime,proto3" json:"reward_time,omitempty"`
	PushMsgTextPrefix    string   `protobuf:"bytes,6,opt,name=push_msg_text_prefix,json=pushMsgTextPrefix,proto3" json:"push_msg_text_prefix,omitempty"`
	CreateTime           int64    `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityAssignNameplateReq) Reset()         { *m = ActivityAssignNameplateReq{} }
func (m *ActivityAssignNameplateReq) String() string { return proto.CompactTextString(m) }
func (*ActivityAssignNameplateReq) ProtoMessage()    {}
func (*ActivityAssignNameplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{15}
}
func (m *ActivityAssignNameplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityAssignNameplateReq.Unmarshal(m, b)
}
func (m *ActivityAssignNameplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityAssignNameplateReq.Marshal(b, m, deterministic)
}
func (dst *ActivityAssignNameplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityAssignNameplateReq.Merge(dst, src)
}
func (m *ActivityAssignNameplateReq) XXX_Size() int {
	return xxx_messageInfo_ActivityAssignNameplateReq.Size(m)
}
func (m *ActivityAssignNameplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityAssignNameplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityAssignNameplateReq proto.InternalMessageInfo

func (m *ActivityAssignNameplateReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ActivityAssignNameplateReq) GetNameplateId() uint32 {
	if m != nil {
		return m.NameplateId
	}
	return 0
}

func (m *ActivityAssignNameplateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ActivityAssignNameplateReq) GetRewardTime() int64 {
	if m != nil {
		return m.RewardTime
	}
	return 0
}

func (m *ActivityAssignNameplateReq) GetPushMsgTextPrefix() string {
	if m != nil {
		return m.PushMsgTextPrefix
	}
	return ""
}

func (m *ActivityAssignNameplateReq) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type ActivityAssignNameplateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityAssignNameplateResp) Reset()         { *m = ActivityAssignNameplateResp{} }
func (m *ActivityAssignNameplateResp) String() string { return proto.CompactTextString(m) }
func (*ActivityAssignNameplateResp) ProtoMessage()    {}
func (*ActivityAssignNameplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{16}
}
func (m *ActivityAssignNameplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityAssignNameplateResp.Unmarshal(m, b)
}
func (m *ActivityAssignNameplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityAssignNameplateResp.Marshal(b, m, deterministic)
}
func (dst *ActivityAssignNameplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityAssignNameplateResp.Merge(dst, src)
}
func (m *ActivityAssignNameplateResp) XXX_Size() int {
	return xxx_messageInfo_ActivityAssignNameplateResp.Size(m)
}
func (m *ActivityAssignNameplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityAssignNameplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityAssignNameplateResp proto.InternalMessageInfo

type GetUserNameplateInfoReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scenes               ScenesType `protobuf:"varint,2,opt,name=scenes,proto3,enum=revenuenameplate.ScenesType" json:"scenes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetUserNameplateInfoReq) Reset()         { *m = GetUserNameplateInfoReq{} }
func (m *GetUserNameplateInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserNameplateInfoReq) ProtoMessage()    {}
func (*GetUserNameplateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{17}
}
func (m *GetUserNameplateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserNameplateInfoReq.Unmarshal(m, b)
}
func (m *GetUserNameplateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserNameplateInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserNameplateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserNameplateInfoReq.Merge(dst, src)
}
func (m *GetUserNameplateInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserNameplateInfoReq.Size(m)
}
func (m *GetUserNameplateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserNameplateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserNameplateInfoReq proto.InternalMessageInfo

func (m *GetUserNameplateInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserNameplateInfoReq) GetScenes() ScenesType {
	if m != nil {
		return m.Scenes
	}
	return ScenesType_SCENES_NORMAL_TYPE
}

type GetUserNameplateInfoResp struct {
	Nameplates           []*NameplateDetailInfo `protobuf:"bytes,1,rep,name=nameplates,proto3" json:"nameplates,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserNameplateInfoResp) Reset()         { *m = GetUserNameplateInfoResp{} }
func (m *GetUserNameplateInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserNameplateInfoResp) ProtoMessage()    {}
func (*GetUserNameplateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{18}
}
func (m *GetUserNameplateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserNameplateInfoResp.Unmarshal(m, b)
}
func (m *GetUserNameplateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserNameplateInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserNameplateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserNameplateInfoResp.Merge(dst, src)
}
func (m *GetUserNameplateInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserNameplateInfoResp.Size(m)
}
func (m *GetUserNameplateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserNameplateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserNameplateInfoResp proto.InternalMessageInfo

func (m *GetUserNameplateInfoResp) GetNameplates() []*NameplateDetailInfo {
	if m != nil {
		return m.Nameplates
	}
	return nil
}

// UserNameplate 用户穿戴铭牌信息
type UserNameplate struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nameplates           []*NameplateDetailInfo `protobuf:"bytes,2,rep,name=nameplates,proto3" json:"nameplates,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *UserNameplate) Reset()         { *m = UserNameplate{} }
func (m *UserNameplate) String() string { return proto.CompactTextString(m) }
func (*UserNameplate) ProtoMessage()    {}
func (*UserNameplate) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{19}
}
func (m *UserNameplate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNameplate.Unmarshal(m, b)
}
func (m *UserNameplate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNameplate.Marshal(b, m, deterministic)
}
func (dst *UserNameplate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNameplate.Merge(dst, src)
}
func (m *UserNameplate) XXX_Size() int {
	return xxx_messageInfo_UserNameplate.Size(m)
}
func (m *UserNameplate) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNameplate.DiscardUnknown(m)
}

var xxx_messageInfo_UserNameplate proto.InternalMessageInfo

func (m *UserNameplate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserNameplate) GetNameplates() []*NameplateDetailInfo {
	if m != nil {
		return m.Nameplates
	}
	return nil
}

type BatchGetUserNameplatesReq struct {
	UidList              []uint32   `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Scenes               ScenesType `protobuf:"varint,2,opt,name=scenes,proto3,enum=revenuenameplate.ScenesType" json:"scenes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetUserNameplatesReq) Reset()         { *m = BatchGetUserNameplatesReq{} }
func (m *BatchGetUserNameplatesReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserNameplatesReq) ProtoMessage()    {}
func (*BatchGetUserNameplatesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{20}
}
func (m *BatchGetUserNameplatesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserNameplatesReq.Unmarshal(m, b)
}
func (m *BatchGetUserNameplatesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserNameplatesReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserNameplatesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserNameplatesReq.Merge(dst, src)
}
func (m *BatchGetUserNameplatesReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserNameplatesReq.Size(m)
}
func (m *BatchGetUserNameplatesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserNameplatesReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserNameplatesReq proto.InternalMessageInfo

func (m *BatchGetUserNameplatesReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetUserNameplatesReq) GetScenes() ScenesType {
	if m != nil {
		return m.Scenes
	}
	return ScenesType_SCENES_NORMAL_TYPE
}

type BatchGetUserNameplatesResp struct {
	UserNameplates       []*UserNameplate `protobuf:"bytes,1,rep,name=user_nameplates,json=userNameplates,proto3" json:"user_nameplates,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetUserNameplatesResp) Reset()         { *m = BatchGetUserNameplatesResp{} }
func (m *BatchGetUserNameplatesResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserNameplatesResp) ProtoMessage()    {}
func (*BatchGetUserNameplatesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{21}
}
func (m *BatchGetUserNameplatesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserNameplatesResp.Unmarshal(m, b)
}
func (m *BatchGetUserNameplatesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserNameplatesResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserNameplatesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserNameplatesResp.Merge(dst, src)
}
func (m *BatchGetUserNameplatesResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserNameplatesResp.Size(m)
}
func (m *BatchGetUserNameplatesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserNameplatesResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserNameplatesResp proto.InternalMessageInfo

func (m *BatchGetUserNameplatesResp) GetUserNameplates() []*UserNameplate {
	if m != nil {
		return m.UserNameplates
	}
	return nil
}

type SetUserNameplateInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IdList               []uint32 `protobuf:"varint,2,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserNameplateInfoReq) Reset()         { *m = SetUserNameplateInfoReq{} }
func (m *SetUserNameplateInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetUserNameplateInfoReq) ProtoMessage()    {}
func (*SetUserNameplateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{22}
}
func (m *SetUserNameplateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserNameplateInfoReq.Unmarshal(m, b)
}
func (m *SetUserNameplateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserNameplateInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetUserNameplateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserNameplateInfoReq.Merge(dst, src)
}
func (m *SetUserNameplateInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetUserNameplateInfoReq.Size(m)
}
func (m *SetUserNameplateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserNameplateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserNameplateInfoReq proto.InternalMessageInfo

func (m *SetUserNameplateInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserNameplateInfoReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type SetUserNameplateInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserNameplateInfoResp) Reset()         { *m = SetUserNameplateInfoResp{} }
func (m *SetUserNameplateInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetUserNameplateInfoResp) ProtoMessage()    {}
func (*SetUserNameplateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{23}
}
func (m *SetUserNameplateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserNameplateInfoResp.Unmarshal(m, b)
}
func (m *SetUserNameplateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserNameplateInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetUserNameplateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserNameplateInfoResp.Merge(dst, src)
}
func (m *SetUserNameplateInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetUserNameplateInfoResp.Size(m)
}
func (m *SetUserNameplateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserNameplateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserNameplateInfoResp proto.InternalMessageInfo

type GetUserAllNameplateListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserAllNameplateListReq) Reset()         { *m = GetUserAllNameplateListReq{} }
func (m *GetUserAllNameplateListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAllNameplateListReq) ProtoMessage()    {}
func (*GetUserAllNameplateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{24}
}
func (m *GetUserAllNameplateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAllNameplateListReq.Unmarshal(m, b)
}
func (m *GetUserAllNameplateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAllNameplateListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAllNameplateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAllNameplateListReq.Merge(dst, src)
}
func (m *GetUserAllNameplateListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAllNameplateListReq.Size(m)
}
func (m *GetUserAllNameplateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAllNameplateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAllNameplateListReq proto.InternalMessageInfo

func (m *GetUserAllNameplateListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserAllNameplateListResp struct {
	Nameplates           []*NameplateDetailInfo `protobuf:"bytes,1,rep,name=nameplates,proto3" json:"nameplates,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserAllNameplateListResp) Reset()         { *m = GetUserAllNameplateListResp{} }
func (m *GetUserAllNameplateListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAllNameplateListResp) ProtoMessage()    {}
func (*GetUserAllNameplateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac, []int{25}
}
func (m *GetUserAllNameplateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAllNameplateListResp.Unmarshal(m, b)
}
func (m *GetUserAllNameplateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAllNameplateListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAllNameplateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAllNameplateListResp.Merge(dst, src)
}
func (m *GetUserAllNameplateListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAllNameplateListResp.Size(m)
}
func (m *GetUserAllNameplateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAllNameplateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAllNameplateListResp proto.InternalMessageInfo

func (m *GetUserAllNameplateListResp) GetNameplates() []*NameplateDetailInfo {
	if m != nil {
		return m.Nameplates
	}
	return nil
}

func init() {
	proto.RegisterType((*RevenueNameplateInfo)(nil), "revenuenameplate.RevenueNameplateInfo")
	proto.RegisterType((*NameplateDetailInfo)(nil), "revenuenameplate.NameplateDetailInfo")
	proto.RegisterType((*SetNameplateReq)(nil), "revenuenameplate.SetNameplateReq")
	proto.RegisterType((*SetNameplateResp)(nil), "revenuenameplate.SetNameplateResp")
	proto.RegisterType((*UpdateNameplateReq)(nil), "revenuenameplate.UpdateNameplateReq")
	proto.RegisterType((*UpdateNameplateResp)(nil), "revenuenameplate.UpdateNameplateResp")
	proto.RegisterType((*GetAllNameplatesReq)(nil), "revenuenameplate.GetAllNameplatesReq")
	proto.RegisterType((*GetAllNameplatesResp)(nil), "revenuenameplate.GetAllNameplatesResp")
	proto.RegisterType((*GetNamePlateReq)(nil), "revenuenameplate.GetNamePlateReq")
	proto.RegisterType((*GetNamePlateResp)(nil), "revenuenameplate.GetNamePlateResp")
	proto.RegisterType((*AssignRecord)(nil), "revenuenameplate.AssignRecord")
	proto.RegisterType((*AssignNamePlateReq)(nil), "revenuenameplate.AssignNamePlateReq")
	proto.RegisterType((*AssignNamePlateResp)(nil), "revenuenameplate.AssignNamePlateResp")
	proto.RegisterType((*GetAssignRecordReq)(nil), "revenuenameplate.GetAssignRecordReq")
	proto.RegisterType((*GetAssignRecordResp)(nil), "revenuenameplate.GetAssignRecordResp")
	proto.RegisterType((*ActivityAssignNameplateReq)(nil), "revenuenameplate.ActivityAssignNameplateReq")
	proto.RegisterType((*ActivityAssignNameplateResp)(nil), "revenuenameplate.ActivityAssignNameplateResp")
	proto.RegisterType((*GetUserNameplateInfoReq)(nil), "revenuenameplate.GetUserNameplateInfoReq")
	proto.RegisterType((*GetUserNameplateInfoResp)(nil), "revenuenameplate.GetUserNameplateInfoResp")
	proto.RegisterType((*UserNameplate)(nil), "revenuenameplate.UserNameplate")
	proto.RegisterType((*BatchGetUserNameplatesReq)(nil), "revenuenameplate.BatchGetUserNameplatesReq")
	proto.RegisterType((*BatchGetUserNameplatesResp)(nil), "revenuenameplate.BatchGetUserNameplatesResp")
	proto.RegisterType((*SetUserNameplateInfoReq)(nil), "revenuenameplate.SetUserNameplateInfoReq")
	proto.RegisterType((*SetUserNameplateInfoResp)(nil), "revenuenameplate.SetUserNameplateInfoResp")
	proto.RegisterType((*GetUserAllNameplateListReq)(nil), "revenuenameplate.GetUserAllNameplateListReq")
	proto.RegisterType((*GetUserAllNameplateListResp)(nil), "revenuenameplate.GetUserAllNameplateListResp")
	proto.RegisterEnum("revenuenameplate.NameplateType", NameplateType_name, NameplateType_value)
	proto.RegisterEnum("revenuenameplate.ScenesType", ScenesType_name, ScenesType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RevenueNameplateClient is the client API for RevenueNameplate service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RevenueNameplateClient interface {
	// 提供给管理后台的接口
	// 配置铭牌
	SetNameplate(ctx context.Context, in *SetNameplateReq, opts ...grpc.CallOption) (*SetNameplateResp, error)
	// 修改铭牌
	UpdateNameplate(ctx context.Context, in *UpdateNameplateReq, opts ...grpc.CallOption) (*UpdateNameplateResp, error)
	// 分页获取所有铭牌配置（支持条件查询）
	GetAllNameplates(ctx context.Context, in *GetAllNameplatesReq, opts ...grpc.CallOption) (*GetAllNameplatesResp, error)
	// 获取指定铭牌配置
	GetNamePlate(ctx context.Context, in *GetNamePlateReq, opts ...grpc.CallOption) (*GetNamePlateResp, error)
	// 发放名牌
	AssignNamePlate(ctx context.Context, in *AssignNamePlateReq, opts ...grpc.CallOption) (*AssignNamePlateResp, error)
	// 获取发放记录
	GetAssignRecord(ctx context.Context, in *GetAssignRecordReq, opts ...grpc.CallOption) (*GetAssignRecordResp, error)
	// 活动发放接口
	ActivityAssignNameplate(ctx context.Context, in *ActivityAssignNameplateReq, opts ...grpc.CallOption) (*ActivityAssignNameplateResp, error)
	// 提供给客户端的接口
	// 获取用户铭牌配置
	GetUserNameplateInfo(ctx context.Context, in *GetUserNameplateInfoReq, opts ...grpc.CallOption) (*GetUserNameplateInfoResp, error)
	// 批量获取用户铭牌配置
	BatchGetUserNameplates(ctx context.Context, in *BatchGetUserNameplatesReq, opts ...grpc.CallOption) (*BatchGetUserNameplatesResp, error)
	// 设置用户铭牌配置
	SetUserNameplateInfo(ctx context.Context, in *SetUserNameplateInfoReq, opts ...grpc.CallOption) (*SetUserNameplateInfoResp, error)
	// 获取用户所有可配置铭牌
	GetUserAllNameplateList(ctx context.Context, in *GetUserAllNameplateListReq, opts ...grpc.CallOption) (*GetUserAllNameplateListResp, error)
	// 获取铭牌配置
	GetNamePlateFromCache(ctx context.Context, in *GetNamePlateReq, opts ...grpc.CallOption) (*GetNamePlateResp, error)
}

type revenueNameplateClient struct {
	cc *grpc.ClientConn
}

func NewRevenueNameplateClient(cc *grpc.ClientConn) RevenueNameplateClient {
	return &revenueNameplateClient{cc}
}

func (c *revenueNameplateClient) SetNameplate(ctx context.Context, in *SetNameplateReq, opts ...grpc.CallOption) (*SetNameplateResp, error) {
	out := new(SetNameplateResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/SetNameplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) UpdateNameplate(ctx context.Context, in *UpdateNameplateReq, opts ...grpc.CallOption) (*UpdateNameplateResp, error) {
	out := new(UpdateNameplateResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/UpdateNameplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) GetAllNameplates(ctx context.Context, in *GetAllNameplatesReq, opts ...grpc.CallOption) (*GetAllNameplatesResp, error) {
	out := new(GetAllNameplatesResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/GetAllNameplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) GetNamePlate(ctx context.Context, in *GetNamePlateReq, opts ...grpc.CallOption) (*GetNamePlateResp, error) {
	out := new(GetNamePlateResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/GetNamePlate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) AssignNamePlate(ctx context.Context, in *AssignNamePlateReq, opts ...grpc.CallOption) (*AssignNamePlateResp, error) {
	out := new(AssignNamePlateResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/AssignNamePlate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) GetAssignRecord(ctx context.Context, in *GetAssignRecordReq, opts ...grpc.CallOption) (*GetAssignRecordResp, error) {
	out := new(GetAssignRecordResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/GetAssignRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) ActivityAssignNameplate(ctx context.Context, in *ActivityAssignNameplateReq, opts ...grpc.CallOption) (*ActivityAssignNameplateResp, error) {
	out := new(ActivityAssignNameplateResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/ActivityAssignNameplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) GetUserNameplateInfo(ctx context.Context, in *GetUserNameplateInfoReq, opts ...grpc.CallOption) (*GetUserNameplateInfoResp, error) {
	out := new(GetUserNameplateInfoResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/GetUserNameplateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) BatchGetUserNameplates(ctx context.Context, in *BatchGetUserNameplatesReq, opts ...grpc.CallOption) (*BatchGetUserNameplatesResp, error) {
	out := new(BatchGetUserNameplatesResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/BatchGetUserNameplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) SetUserNameplateInfo(ctx context.Context, in *SetUserNameplateInfoReq, opts ...grpc.CallOption) (*SetUserNameplateInfoResp, error) {
	out := new(SetUserNameplateInfoResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/SetUserNameplateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) GetUserAllNameplateList(ctx context.Context, in *GetUserAllNameplateListReq, opts ...grpc.CallOption) (*GetUserAllNameplateListResp, error) {
	out := new(GetUserAllNameplateListResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/GetUserAllNameplateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *revenueNameplateClient) GetNamePlateFromCache(ctx context.Context, in *GetNamePlateReq, opts ...grpc.CallOption) (*GetNamePlateResp, error) {
	out := new(GetNamePlateResp)
	err := c.cc.Invoke(ctx, "/revenuenameplate.RevenueNameplate/GetNamePlateFromCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RevenueNameplateServer is the server API for RevenueNameplate service.
type RevenueNameplateServer interface {
	// 提供给管理后台的接口
	// 配置铭牌
	SetNameplate(context.Context, *SetNameplateReq) (*SetNameplateResp, error)
	// 修改铭牌
	UpdateNameplate(context.Context, *UpdateNameplateReq) (*UpdateNameplateResp, error)
	// 分页获取所有铭牌配置（支持条件查询）
	GetAllNameplates(context.Context, *GetAllNameplatesReq) (*GetAllNameplatesResp, error)
	// 获取指定铭牌配置
	GetNamePlate(context.Context, *GetNamePlateReq) (*GetNamePlateResp, error)
	// 发放名牌
	AssignNamePlate(context.Context, *AssignNamePlateReq) (*AssignNamePlateResp, error)
	// 获取发放记录
	GetAssignRecord(context.Context, *GetAssignRecordReq) (*GetAssignRecordResp, error)
	// 活动发放接口
	ActivityAssignNameplate(context.Context, *ActivityAssignNameplateReq) (*ActivityAssignNameplateResp, error)
	// 提供给客户端的接口
	// 获取用户铭牌配置
	GetUserNameplateInfo(context.Context, *GetUserNameplateInfoReq) (*GetUserNameplateInfoResp, error)
	// 批量获取用户铭牌配置
	BatchGetUserNameplates(context.Context, *BatchGetUserNameplatesReq) (*BatchGetUserNameplatesResp, error)
	// 设置用户铭牌配置
	SetUserNameplateInfo(context.Context, *SetUserNameplateInfoReq) (*SetUserNameplateInfoResp, error)
	// 获取用户所有可配置铭牌
	GetUserAllNameplateList(context.Context, *GetUserAllNameplateListReq) (*GetUserAllNameplateListResp, error)
	// 获取铭牌配置
	GetNamePlateFromCache(context.Context, *GetNamePlateReq) (*GetNamePlateResp, error)
}

func RegisterRevenueNameplateServer(s *grpc.Server, srv RevenueNameplateServer) {
	s.RegisterService(&_RevenueNameplate_serviceDesc, srv)
}

func _RevenueNameplate_SetNameplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetNameplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).SetNameplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/SetNameplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).SetNameplate(ctx, req.(*SetNameplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_UpdateNameplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNameplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).UpdateNameplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/UpdateNameplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).UpdateNameplate(ctx, req.(*UpdateNameplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_GetAllNameplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllNameplatesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).GetAllNameplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/GetAllNameplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).GetAllNameplates(ctx, req.(*GetAllNameplatesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_GetNamePlate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNamePlateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).GetNamePlate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/GetNamePlate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).GetNamePlate(ctx, req.(*GetNamePlateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_AssignNamePlate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignNamePlateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).AssignNamePlate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/AssignNamePlate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).AssignNamePlate(ctx, req.(*AssignNamePlateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_GetAssignRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssignRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).GetAssignRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/GetAssignRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).GetAssignRecord(ctx, req.(*GetAssignRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_ActivityAssignNameplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityAssignNameplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).ActivityAssignNameplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/ActivityAssignNameplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).ActivityAssignNameplate(ctx, req.(*ActivityAssignNameplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_GetUserNameplateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserNameplateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).GetUserNameplateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/GetUserNameplateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).GetUserNameplateInfo(ctx, req.(*GetUserNameplateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_BatchGetUserNameplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserNameplatesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).BatchGetUserNameplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/BatchGetUserNameplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).BatchGetUserNameplates(ctx, req.(*BatchGetUserNameplatesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_SetUserNameplateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserNameplateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).SetUserNameplateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/SetUserNameplateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).SetUserNameplateInfo(ctx, req.(*SetUserNameplateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_GetUserAllNameplateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAllNameplateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).GetUserAllNameplateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/GetUserAllNameplateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).GetUserAllNameplateList(ctx, req.(*GetUserAllNameplateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RevenueNameplate_GetNamePlateFromCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNamePlateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RevenueNameplateServer).GetNamePlateFromCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/revenuenameplate.RevenueNameplate/GetNamePlateFromCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RevenueNameplateServer).GetNamePlateFromCache(ctx, req.(*GetNamePlateReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RevenueNameplate_serviceDesc = grpc.ServiceDesc{
	ServiceName: "revenuenameplate.RevenueNameplate",
	HandlerType: (*RevenueNameplateServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetNameplate",
			Handler:    _RevenueNameplate_SetNameplate_Handler,
		},
		{
			MethodName: "UpdateNameplate",
			Handler:    _RevenueNameplate_UpdateNameplate_Handler,
		},
		{
			MethodName: "GetAllNameplates",
			Handler:    _RevenueNameplate_GetAllNameplates_Handler,
		},
		{
			MethodName: "GetNamePlate",
			Handler:    _RevenueNameplate_GetNamePlate_Handler,
		},
		{
			MethodName: "AssignNamePlate",
			Handler:    _RevenueNameplate_AssignNamePlate_Handler,
		},
		{
			MethodName: "GetAssignRecord",
			Handler:    _RevenueNameplate_GetAssignRecord_Handler,
		},
		{
			MethodName: "ActivityAssignNameplate",
			Handler:    _RevenueNameplate_ActivityAssignNameplate_Handler,
		},
		{
			MethodName: "GetUserNameplateInfo",
			Handler:    _RevenueNameplate_GetUserNameplateInfo_Handler,
		},
		{
			MethodName: "BatchGetUserNameplates",
			Handler:    _RevenueNameplate_BatchGetUserNameplates_Handler,
		},
		{
			MethodName: "SetUserNameplateInfo",
			Handler:    _RevenueNameplate_SetUserNameplateInfo_Handler,
		},
		{
			MethodName: "GetUserAllNameplateList",
			Handler:    _RevenueNameplate_GetUserAllNameplateList_Handler,
		},
		{
			MethodName: "GetNamePlateFromCache",
			Handler:    _RevenueNameplate_GetNamePlateFromCache_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "revenue-nameplate/revenue-nameplate.proto",
}

func init() {
	proto.RegisterFile("revenue-nameplate/revenue-nameplate.proto", fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac)
}

var fileDescriptor_revenue_nameplate_ab7c0fec90efb3ac = []byte{
	// 1218 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x58, 0xdd, 0x52, 0xdb, 0x46,
	0x14, 0x8e, 0x6c, 0xb0, 0xf1, 0x01, 0x1b, 0x67, 0xcd, 0x8f, 0x23, 0x9a, 0x42, 0x34, 0x21, 0x43,
	0x68, 0x62, 0x77, 0x9c, 0xb4, 0x97, 0x9d, 0x21, 0xc1, 0x50, 0x66, 0x0c, 0x66, 0xd6, 0x66, 0x98,
	0x72, 0xa3, 0x2a, 0xd2, 0x62, 0x76, 0x2a, 0x4b, 0x8a, 0x76, 0x4d, 0xcc, 0x65, 0xdf, 0xa2, 0x57,
	0xbd, 0x6b, 0x5f, 0xa1, 0x2f, 0xd1, 0x77, 0xe8, 0x7b, 0xf4, 0xaa, 0xa3, 0x95, 0x90, 0x2d, 0x6b,
	0x9d, 0x1a, 0x86, 0xce, 0xf4, 0x0a, 0xed, 0xd9, 0xef, 0xec, 0xf9, 0xce, 0xcf, 0x9e, 0xb3, 0x18,
	0x5e, 0xfa, 0xe4, 0x9a, 0x38, 0x03, 0xf2, 0xda, 0x31, 0xfa, 0xc4, 0xb3, 0x0d, 0x4e, 0xea, 0x29,
	0x49, 0xcd, 0xf3, 0x5d, 0xee, 0xa2, 0x72, 0xb4, 0x11, 0xcb, 0xd5, 0x4d, 0x32, 0xe4, 0xc4, 0x61,
	0xd4, 0x75, 0xea, 0xae, 0xc7, 0xa9, 0xeb, 0xb0, 0xdb, 0xbf, 0xa1, 0x8a, 0xf6, 0x5b, 0x06, 0x56,
	0x70, 0xa8, 0x75, 0x72, 0xab, 0x75, 0xe4, 0x5c, 0xba, 0xa8, 0x04, 0x19, 0x6a, 0x55, 0x95, 0x2d,
	0x65, 0xa7, 0x88, 0x33, 0xd4, 0x42, 0x08, 0xe6, 0x82, 0x63, 0xab, 0x99, 0x2d, 0x65, 0xa7, 0x80,
	0xc5, 0x37, 0x7a, 0x02, 0x0b, 0x1f, 0x0c, 0x46, 0xf4, 0x81, 0x6f, 0x57, 0xb3, 0x42, 0x9e, 0x0f,
	0xd6, 0x67, 0xbe, 0x8d, 0x36, 0x61, 0xd1, 0xba, 0x71, 0x8c, 0x3e, 0x35, 0xc5, 0xee, 0x9c, 0xd8,
	0x85, 0x48, 0x14, 0x00, 0xde, 0xc0, 0x1c, 0xbf, 0xf1, 0x48, 0x75, 0x7e, 0x4b, 0xd9, 0x29, 0x35,
	0x36, 0x6b, 0x93, 0xd4, 0x6b, 0x31, 0x9d, 0xee, 0x8d, 0x47, 0xb0, 0x00, 0xa3, 0xe7, 0x50, 0xa2,
	0x4c, 0xbf, 0x74, 0x6d, 0xdb, 0xfd, 0xa4, 0x3b, 0x84, 0x58, 0xd5, 0xdc, 0x96, 0xb2, 0xb3, 0x80,
	0x97, 0x28, 0x3b, 0x10, 0xc2, 0x13, 0x42, 0xac, 0xc0, 0xb6, 0x4f, 0x3e, 0x19, 0xbe, 0xa5, 0x73,
	0xda, 0x27, 0xd5, 0xbc, 0xf0, 0x01, 0x42, 0x51, 0x97, 0xf6, 0x09, 0x5a, 0x83, 0x9c, 0x4f, 0xfa,
	0x86, 0xff, 0x53, 0x75, 0x41, 0xf0, 0x8a, 0x56, 0x81, 0x3f, 0xae, 0xc7, 0x43, 0xad, 0x82, 0xd0,
	0xca, 0xbb, 0x1e, 0x0f, 0x54, 0xb4, 0x9f, 0x33, 0x50, 0x89, 0x19, 0xed, 0x13, 0x6e, 0x50, 0xfb,
	0xff, 0x1b, 0xa6, 0xa7, 0x00, 0x8c, 0x1b, 0x7e, 0xe4, 0x49, 0x4e, 0x90, 0x2b, 0x08, 0x89, 0x70,
	0x7f, 0x13, 0x16, 0x2f, 0xa9, 0x43, 0xd9, 0x55, 0x22, 0x3e, 0xa1, 0x48, 0x00, 0x56, 0x21, 0x47,
	0x99, 0x3e, 0x60, 0x44, 0xc4, 0x67, 0x01, 0xcf, 0x53, 0x76, 0xc6, 0x88, 0x76, 0x0e, 0xcb, 0x1d,
	0xc2, 0x63, 0x83, 0x98, 0x7c, 0x44, 0xfb, 0x50, 0x88, 0xa9, 0x88, 0x28, 0x2c, 0x36, 0x5e, 0xa4,
	0x39, 0xca, 0x0a, 0x0c, 0x8f, 0x14, 0x35, 0x0d, 0xca, 0xc9, 0x83, 0x99, 0x37, 0x19, 0x58, 0xed,
	0x02, 0xd0, 0x99, 0x67, 0x19, 0x9c, 0xfc, 0x07, 0xf6, 0xb7, 0xa1, 0x92, 0x3a, 0x5b, 0x42, 0xc1,
	0x84, 0xca, 0x21, 0xe1, 0x7b, 0xb6, 0x1d, 0xc3, 0x58, 0xc0, 0x61, 0x05, 0xe6, 0x6d, 0xda, 0xa7,
	0x3c, 0x42, 0x86, 0x8b, 0xa0, 0x10, 0x3c, 0xa3, 0x17, 0x16, 0x42, 0x11, 0x8b, 0xef, 0xe8, 0xc0,
	0x6c, 0xaa, 0x58, 0xe6, 0x46, 0xc5, 0xa2, 0xfd, 0xaa, 0xc0, 0x4a, 0xda, 0x0a, 0xf3, 0xd0, 0x01,
	0x40, 0xcc, 0x98, 0x55, 0x95, 0xad, 0xec, 0x1d, 0x7c, 0x1d, 0xd3, 0x1c, 0xd1, 0xcd, 0xc8, 0xe8,
	0x66, 0xc7, 0xe8, 0xae, 0xc0, 0xbc, 0xe9, 0x0e, 0x1c, 0x2e, 0xf8, 0x15, 0x71, 0xb8, 0xd0, 0x76,
	0x61, 0xf9, 0x30, 0x4c, 0xd6, 0xe9, 0x6d, 0x16, 0xd6, 0x21, 0x4f, 0x2d, 0xdd, 0xa6, 0x8c, 0x0b,
	0x5e, 0x45, 0x9c, 0xa3, 0x56, 0x8b, 0x32, 0xae, 0x5d, 0x40, 0x39, 0x89, 0x7d, 0x38, 0x3f, 0xb4,
	0xdf, 0x15, 0x58, 0xda, 0x63, 0x8c, 0xf6, 0x1c, 0x4c, 0x4c, 0xd7, 0xb7, 0x52, 0x57, 0xf1, 0x19,
	0x2c, 0xc5, 0x70, 0x9d, 0x5a, 0x91, 0xbf, 0x8b, 0xb1, 0xec, 0xc8, 0x42, 0x65, 0xc8, 0x0e, 0xe2,
	0x8c, 0x04, 0x9f, 0xa8, 0x02, 0xf3, 0x9c, 0x07, 0xe8, 0x28, 0x27, 0x9c, 0x1f, 0xa5, 0x1a, 0x4a,
	0x70, 0x17, 0xe7, 0x12, 0x0d, 0x65, 0x03, 0x0a, 0x94, 0xe9, 0x86, 0x60, 0x13, 0xdd, 0xb7, 0x05,
	0xca, 0x42, 0x76, 0x5a, 0x0b, 0x50, 0xf8, 0x95, 0x88, 0xd9, 0xb7, 0x41, 0x0f, 0x0a, 0x78, 0x47,
	0x21, 0xf8, 0x32, 0x1d, 0x82, 0x71, 0xef, 0x70, 0x84, 0xd6, 0x56, 0xa1, 0x92, 0x3a, 0x8d, 0x79,
	0xda, 0x77, 0x80, 0x82, 0xaa, 0x19, 0xd7, 0xb8, 0x4b, 0x69, 0x6a, 0xc7, 0x61, 0x6d, 0x27, 0xf4,
	0x99, 0x77, 0x6f, 0x96, 0x7f, 0x29, 0xa0, 0xee, 0x99, 0x9c, 0x5e, 0x53, 0x7e, 0x33, 0xa2, 0x1b,
	0x5f, 0xdb, 0xa0, 0xd1, 0xfa, 0x16, 0xf1, 0xf5, 0x28, 0x61, 0x05, 0x9c, 0x17, 0xeb, 0xa3, 0x7b,
	0x66, 0x4d, 0x92, 0xa0, 0x6c, 0x22, 0x41, 0x75, 0x58, 0xf1, 0x06, 0xec, 0x4a, 0xef, 0xb3, 0x9e,
	0xce, 0xc9, 0x90, 0xeb, 0x9e, 0x4f, 0x2e, 0xe9, 0x50, 0xe4, 0xaa, 0x80, 0x1f, 0x07, 0x7b, 0xc7,
	0xac, 0xd7, 0x25, 0x43, 0x7e, 0x2a, 0x36, 0x82, 0x13, 0x4d, 0x9f, 0x04, 0x1c, 0xe2, 0x1e, 0x99,
	0xc5, 0x10, 0x8a, 0xc4, 0x40, 0x78, 0x0a, 0x1b, 0x53, 0x1d, 0x64, 0x9e, 0x66, 0xc0, 0xfa, 0x21,
	0xe1, 0x67, 0x8c, 0xf8, 0xc9, 0x0a, 0x26, 0x1f, 0x6f, 0xe9, 0x2b, 0x23, 0xfa, 0x6f, 0x21, 0xc7,
	0x4c, 0xe2, 0x10, 0x26, 0xbc, 0x2d, 0x35, 0xbe, 0x48, 0x47, 0xb9, 0x23, 0xf6, 0x45, 0x8f, 0x8f,
	0xb0, 0x9a, 0x01, 0x55, 0xb9, 0x09, 0xe6, 0xa1, 0xa6, 0xe4, 0x92, 0x6d, 0x7f, 0x66, 0x78, 0x8c,
	0x26, 0x5a, 0xe2, 0x8e, 0x5d, 0x41, 0x31, 0x71, 0xbe, 0x84, 0x7b, 0xd2, 0x52, 0xe6, 0xbe, 0x96,
	0x6c, 0x78, 0xf2, 0xce, 0xe0, 0xe6, 0xd5, 0xa4, 0x47, 0x2c, 0x2a, 0x97, 0x41, 0xb2, 0xc1, 0xe4,
	0x07, 0x61, 0x87, 0xb9, 0x67, 0xe8, 0x2e, 0x41, 0x9d, 0x66, 0x8d, 0x79, 0xe8, 0x7b, 0x58, 0x1e,
	0x30, 0xe2, 0xeb, 0xa9, 0x08, 0x4a, 0xc6, 0x6f, 0x42, 0x1d, 0x97, 0x06, 0x89, 0xd3, 0xb4, 0x7d,
	0x58, 0xef, 0xcc, 0x5c, 0x05, 0x63, 0x5d, 0x34, 0x93, 0xe8, 0xa2, 0x2a, 0x54, 0x3b, 0x53, 0x12,
	0xad, 0xd5, 0x40, 0x8d, 0x9c, 0x18, 0x9f, 0x18, 0x81, 0x9a, 0xd4, 0x88, 0x66, 0xc1, 0xc6, 0x54,
	0xfc, 0x83, 0xd5, 0xcd, 0xee, 0xd7, 0x50, 0x4c, 0xbc, 0x4b, 0x50, 0x09, 0xa0, 0xfb, 0xc3, 0x69,
	0x53, 0x6f, 0x62, 0xdc, 0xc6, 0xe5, 0x47, 0x68, 0x19, 0x16, 0xc5, 0xba, 0xd5, 0xee, 0x76, 0x9b,
	0xb8, 0xac, 0xec, 0xee, 0x01, 0x8c, 0xf2, 0x84, 0xd6, 0x00, 0x75, 0xde, 0x37, 0x4f, 0x9a, 0x1d,
	0xfd, 0xa4, 0x8d, 0x8f, 0xf7, 0x5a, 0x7a, 0x00, 0x2e, 0x3f, 0x42, 0x1b, 0xb0, 0x1e, 0xc9, 0x71,
	0xbb, 0x7d, 0xac, 0x1f, 0xb4, 0x5b, 0xad, 0xf6, 0x79, 0xb8, 0xa9, 0x34, 0x7e, 0x01, 0x28, 0x4f,
	0x4e, 0x0d, 0x74, 0x0e, 0x4b, 0xe3, 0x4f, 0x0b, 0xf4, 0x4c, 0x52, 0x1f, 0xc9, 0x37, 0x8d, 0xaa,
	0xfd, 0x1b, 0x84, 0x79, 0xda, 0x23, 0xf4, 0x23, 0x2c, 0x4f, 0xbc, 0x19, 0xd0, 0x73, 0x49, 0x79,
	0xa4, 0x9e, 0x2c, 0xea, 0xf6, 0x0c, 0x28, 0x61, 0xc1, 0x14, 0xc3, 0x33, 0xf1, 0x10, 0x40, 0x12,
	0x65, 0xc9, 0x93, 0x44, 0x7d, 0x31, 0x0b, 0x4c, 0x18, 0x39, 0x87, 0xa5, 0xf1, 0x09, 0x2d, 0x8b,
	0xcf, 0xc4, 0xb4, 0x97, 0xc5, 0x67, 0x72, 0xc8, 0x87, 0xf1, 0x99, 0x98, 0x53, 0xb2, 0xf8, 0xa4,
	0x07, 0xa3, 0x2c, 0x3e, 0xb2, 0x81, 0x27, 0x2c, 0x4c, 0x8c, 0x2c, 0x99, 0x85, 0xf4, 0x54, 0x54,
	0xb7, 0x67, 0x40, 0x09, 0x0b, 0x43, 0x58, 0x9f, 0xd2, 0xe3, 0xd1, 0x2b, 0x09, 0xcb, 0xa9, 0xf3,
	0x4e, 0x7d, 0x7d, 0x07, 0xb4, 0xb0, 0xec, 0x8a, 0x47, 0x60, 0xea, 0xca, 0xa3, 0x97, 0x52, 0xea,
	0xb2, 0x06, 0xa3, 0xee, 0xce, 0x0a, 0x15, 0x06, 0x07, 0xb0, 0x26, 0xef, 0x88, 0xe8, 0xab, 0xf4,
	0x39, 0x53, 0x3b, 0xb5, 0xfa, 0x6a, 0x76, 0xf0, 0xad, 0x9f, 0x9d, 0x19, 0xfd, 0xec, 0xcc, 0xee,
	0x67, 0x67, 0xba, 0x9f, 0xc3, 0x78, 0x2e, 0x4f, 0xf6, 0x3f, 0x59, 0x4a, 0xa7, 0xb7, 0x56, 0x59,
	0x4a, 0x3f, 0xd3, 0x58, 0x45, 0xb9, 0xae, 0x8e, 0x5f, 0x93, 0x03, 0xdf, 0xed, 0xbf, 0x37, 0xcc,
	0xab, 0x87, 0xbb, 0x72, 0x6a, 0xf5, 0xef, 0x3f, 0xfe, 0xec, 0x56, 0xe0, 0x71, 0xea, 0xe7, 0x81,
	0x77, 0x6f, 0x2f, 0x1a, 0x3d, 0xd7, 0x36, 0x9c, 0x5e, 0xed, 0x9b, 0x06, 0xe7, 0x35, 0xd3, 0xed,
	0xd7, 0xc5, 0xbf, 0xff, 0xa6, 0x6b, 0xd7, 0x19, 0xf1, 0xaf, 0xa9, 0x49, 0x58, 0x7d, 0xd2, 0xc4,
	0x87, 0x9c, 0xc0, 0xbc, 0xf9, 0x27, 0x00, 0x00, 0xff, 0xff, 0xf8, 0x22, 0x54, 0x3d, 0x82, 0x10,
	0x00, 0x00,
}
