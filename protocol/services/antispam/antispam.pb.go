// Code generated by protoc-gen-gogo.
// source: src/antispam/antispam.proto
// DO NOT EDIT!

/*
	Package Antispam is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/antispam/antispam.proto

	It has these top-level messages:
		TextCheckReq
		TextCheckResp
		RegisterCheckReq
		RegisterCheckResp
		AuthCheckReq
		AuthCheckResp
		CommonActivityCheckReq
		CommonActivityCheckResp
		VerifyCodeCheckReq
		VerifyCodeCheckResp
		GpsLocation
		ShumeiRegisterCheckReq
		ShumeiRegisterCheckResp
		ShumeiAuthCheckReq
		ShumeiAuthCheckResp
		ShumeiTextCheckReq
		ShumeiTextCheckResp
		AntispamUserInfo
		DVRegisterCheckReq
		DVLoginCheckReq
		DVCommCheckReq
		DVCheckResp
*/
package Antispam

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import math3 "math"

import io1 "io"
import math4 "math"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type RiskType int32

const (
	RiskType_RESULT_DEFAULT           RiskType = 0
	RiskType_RESULT_HIGH_RISK_ACCOUNT RiskType = 1
)

var RiskType_name = map[int32]string{
	0: "RESULT_DEFAULT",
	1: "RESULT_HIGH_RISK_ACCOUNT",
}
var RiskType_value = map[string]int32{
	"RESULT_DEFAULT":           0,
	"RESULT_HIGH_RISK_ACCOUNT": 1,
}

func (x RiskType) Enum() *RiskType {
	p := new(RiskType)
	*p = x
	return p
}
func (x RiskType) String() string {
	return proto.EnumName(RiskType_name, int32(x))
}
func (x *RiskType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RiskType_value, data, "RiskType")
	if err != nil {
		return err
	}
	*x = RiskType(value)
	return nil
}
func (RiskType) EnumDescriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{0} }

type UserBehavierCheckResult int32

const (
	UserBehavierCheckResult_RESULT_OK        UserBehavierCheckResult = 0
	UserBehavierCheckResult_RESULT_SUSPICION UserBehavierCheckResult = 10
	UserBehavierCheckResult_RESULT_FATAL     UserBehavierCheckResult = 20
)

var UserBehavierCheckResult_name = map[int32]string{
	0:  "RESULT_OK",
	10: "RESULT_SUSPICION",
	20: "RESULT_FATAL",
}
var UserBehavierCheckResult_value = map[string]int32{
	"RESULT_OK":        0,
	"RESULT_SUSPICION": 10,
	"RESULT_FATAL":     20,
}

func (x UserBehavierCheckResult) Enum() *UserBehavierCheckResult {
	p := new(UserBehavierCheckResult)
	*p = x
	return p
}
func (x UserBehavierCheckResult) String() string {
	return proto.EnumName(UserBehavierCheckResult_name, int32(x))
}
func (x *UserBehavierCheckResult) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserBehavierCheckResult_value, data, "UserBehavierCheckResult")
	if err != nil {
		return err
	}
	*x = UserBehavierCheckResult(value)
	return nil
}
func (UserBehavierCheckResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispam, []int{1}
}

type UserBehavierCheckResultHitType int32

const (
	UserBehavierCheckResultHitType_HIT_OK                  UserBehavierCheckResultHitType = 0
	UserBehavierCheckResultHitType_HIT_DATA_ABNORMAL       UserBehavierCheckResultHitType = 1
	UserBehavierCheckResultHitType_HIT_BEHAVIOR_ABNORMAL   UserBehavierCheckResultHitType = 2
	UserBehavierCheckResultHitType_HIT_ENVIROMENT_ABNORMAL UserBehavierCheckResultHitType = 3
	UserBehavierCheckResultHitType_HIT_BIZ_ABNORMAL        UserBehavierCheckResultHitType = 4
)

var UserBehavierCheckResultHitType_name = map[int32]string{
	0: "HIT_OK",
	1: "HIT_DATA_ABNORMAL",
	2: "HIT_BEHAVIOR_ABNORMAL",
	3: "HIT_ENVIROMENT_ABNORMAL",
	4: "HIT_BIZ_ABNORMAL",
}
var UserBehavierCheckResultHitType_value = map[string]int32{
	"HIT_OK":                  0,
	"HIT_DATA_ABNORMAL":       1,
	"HIT_BEHAVIOR_ABNORMAL":   2,
	"HIT_ENVIROMENT_ABNORMAL": 3,
	"HIT_BIZ_ABNORMAL":        4,
}

func (x UserBehavierCheckResultHitType) Enum() *UserBehavierCheckResultHitType {
	p := new(UserBehavierCheckResultHitType)
	*p = x
	return p
}
func (x UserBehavierCheckResultHitType) String() string {
	return proto.EnumName(UserBehavierCheckResultHitType_name, int32(x))
}
func (x *UserBehavierCheckResultHitType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserBehavierCheckResultHitType_value, data, "UserBehavierCheckResultHitType")
	if err != nil {
		return err
	}
	*x = UserBehavierCheckResultHitType(value)
	return nil
}
func (UserBehavierCheckResultHitType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispam, []int{2}
}

type ShumeiOSType int32

const (
	ShumeiOSType_none    ShumeiOSType = 0
	ShumeiOSType_android ShumeiOSType = 1
	ShumeiOSType_ios     ShumeiOSType = 2
	ShumeiOSType_weapp   ShumeiOSType = 3
	ShumeiOSType_web     ShumeiOSType = 4
)

var ShumeiOSType_name = map[int32]string{
	0: "none",
	1: "android",
	2: "ios",
	3: "weapp",
	4: "web",
}
var ShumeiOSType_value = map[string]int32{
	"none":    0,
	"android": 1,
	"ios":     2,
	"weapp":   3,
	"web":     4,
}

func (x ShumeiOSType) Enum() *ShumeiOSType {
	p := new(ShumeiOSType)
	*p = x
	return p
}
func (x ShumeiOSType) String() string {
	return proto.EnumName(ShumeiOSType_name, int32(x))
}
func (x *ShumeiOSType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ShumeiOSType_value, data, "ShumeiOSType")
	if err != nil {
		return err
	}
	*x = ShumeiOSType(value)
	return nil
}
func (ShumeiOSType) EnumDescriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{3} }

type TextCheckReq_DeviceType int32

const (
	TextCheckReq_none    TextCheckReq_DeviceType = 0
	TextCheckReq_web     TextCheckReq_DeviceType = 1
	TextCheckReq_wap     TextCheckReq_DeviceType = 2
	TextCheckReq_android TextCheckReq_DeviceType = 3
	TextCheckReq_iphone  TextCheckReq_DeviceType = 4
	TextCheckReq_ipad    TextCheckReq_DeviceType = 5
	TextCheckReq_pc      TextCheckReq_DeviceType = 6
	TextCheckReq_wp      TextCheckReq_DeviceType = 7
)

var TextCheckReq_DeviceType_name = map[int32]string{
	0: "none",
	1: "web",
	2: "wap",
	3: "android",
	4: "iphone",
	5: "ipad",
	6: "pc",
	7: "wp",
}
var TextCheckReq_DeviceType_value = map[string]int32{
	"none":    0,
	"web":     1,
	"wap":     2,
	"android": 3,
	"iphone":  4,
	"ipad":    5,
	"pc":      6,
	"wp":      7,
}

func (x TextCheckReq_DeviceType) Enum() *TextCheckReq_DeviceType {
	p := new(TextCheckReq_DeviceType)
	*p = x
	return p
}
func (x TextCheckReq_DeviceType) String() string {
	return proto.EnumName(TextCheckReq_DeviceType_name, int32(x))
}
func (x *TextCheckReq_DeviceType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(TextCheckReq_DeviceType_value, data, "TextCheckReq_DeviceType")
	if err != nil {
		return err
	}
	*x = TextCheckReq_DeviceType(value)
	return nil
}
func (TextCheckReq_DeviceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispam, []int{0, 0}
}

type TextCheckResp_RESULT int32

const (
	TextCheckResp_PASS      TextCheckResp_RESULT = 0
	TextCheckResp_SUSPICION TextCheckResp_RESULT = 1
	TextCheckResp_NOT_PASS  TextCheckResp_RESULT = 2
)

var TextCheckResp_RESULT_name = map[int32]string{
	0: "PASS",
	1: "SUSPICION",
	2: "NOT_PASS",
}
var TextCheckResp_RESULT_value = map[string]int32{
	"PASS":      0,
	"SUSPICION": 1,
	"NOT_PASS":  2,
}

func (x TextCheckResp_RESULT) Enum() *TextCheckResp_RESULT {
	p := new(TextCheckResp_RESULT)
	*p = x
	return p
}
func (x TextCheckResp_RESULT) String() string {
	return proto.EnumName(TextCheckResp_RESULT_name, int32(x))
}
func (x *TextCheckResp_RESULT) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(TextCheckResp_RESULT_value, data, "TextCheckResp_RESULT")
	if err != nil {
		return err
	}
	*x = TextCheckResp_RESULT(value)
	return nil
}
func (TextCheckResp_RESULT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispam, []int{1, 0}
}

type ShumeiRegisterCheckReq_SignupPlatformType int32

const (
	ShumeiRegisterCheckReq_tt     ShumeiRegisterCheckReq_SignupPlatformType = 0
	ShumeiRegisterCheckReq_qq     ShumeiRegisterCheckReq_SignupPlatformType = 1
	ShumeiRegisterCheckReq_weixin ShumeiRegisterCheckReq_SignupPlatformType = 2
)

var ShumeiRegisterCheckReq_SignupPlatformType_name = map[int32]string{
	0: "tt",
	1: "qq",
	2: "weixin",
}
var ShumeiRegisterCheckReq_SignupPlatformType_value = map[string]int32{
	"tt":     0,
	"qq":     1,
	"weixin": 2,
}

func (x ShumeiRegisterCheckReq_SignupPlatformType) Enum() *ShumeiRegisterCheckReq_SignupPlatformType {
	p := new(ShumeiRegisterCheckReq_SignupPlatformType)
	*p = x
	return p
}
func (x ShumeiRegisterCheckReq_SignupPlatformType) String() string {
	return proto.EnumName(ShumeiRegisterCheckReq_SignupPlatformType_name, int32(x))
}
func (x *ShumeiRegisterCheckReq_SignupPlatformType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ShumeiRegisterCheckReq_SignupPlatformType_value, data, "ShumeiRegisterCheckReq_SignupPlatformType")
	if err != nil {
		return err
	}
	*x = ShumeiRegisterCheckReq_SignupPlatformType(value)
	return nil
}
func (ShumeiRegisterCheckReq_SignupPlatformType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispam, []int{11, 0}
}

type DVRegisterCheckReq_RegType int32

const (
	DVRegisterCheckReq_phone DVRegisterCheckReq_RegType = 0
	DVRegisterCheckReq_wx    DVRegisterCheckReq_RegType = 1
	DVRegisterCheckReq_qq    DVRegisterCheckReq_RegType = 2
)

var DVRegisterCheckReq_RegType_name = map[int32]string{
	0: "phone",
	1: "wx",
	2: "qq",
}
var DVRegisterCheckReq_RegType_value = map[string]int32{
	"phone": 0,
	"wx":    1,
	"qq":    2,
}

func (x DVRegisterCheckReq_RegType) Enum() *DVRegisterCheckReq_RegType {
	p := new(DVRegisterCheckReq_RegType)
	*p = x
	return p
}
func (x DVRegisterCheckReq_RegType) String() string {
	return proto.EnumName(DVRegisterCheckReq_RegType_name, int32(x))
}
func (x *DVRegisterCheckReq_RegType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(DVRegisterCheckReq_RegType_value, data, "DVRegisterCheckReq_RegType")
	if err != nil {
		return err
	}
	*x = DVRegisterCheckReq_RegType(value)
	return nil
}
func (DVRegisterCheckReq_RegType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAntispam, []int{18, 0}
}

type TextCheckReq struct {
	Text           string                  `protobuf:"bytes,1,req,name=text" json:"text"`
	Ip             string                  `protobuf:"bytes,2,opt,name=ip" json:"ip"`
	Account        string                  `protobuf:"bytes,3,opt,name=account" json:"account"`
	DeviceType     TextCheckReq_DeviceType `protobuf:"varint,4,opt,name=device_type,json=deviceType,enum=Antispam.TextCheckReq_DeviceType" json:"device_type"`
	DeviceId       string                  `protobuf:"bytes,5,opt,name=device_id,json=deviceId" json:"device_id"`
	DataType       uint32                  `protobuf:"varint,6,opt,name=data_type,json=dataType" json:"data_type"`
	DataId         string                  `protobuf:"bytes,7,opt,name=data_id,json=dataId" json:"data_id"`
	ShumeiDeviceId string                  `protobuf:"bytes,8,opt,name=shumei_device_id,json=shumeiDeviceId" json:"shumei_device_id"`
	Nickname       string                  `protobuf:"bytes,9,opt,name=nickname" json:"nickname"`
	Alias          string                  `protobuf:"bytes,10,opt,name=alias" json:"alias"`
	RoomDisplayId  uint32                  `protobuf:"varint,11,opt,name=room_display_id,json=roomDisplayId" json:"room_display_id"`
	SceneType      string                  `protobuf:"bytes,12,opt,name=scene_type,json=sceneType" json:"scene_type"`
	ToTokenid      string                  `protobuf:"bytes,13,opt,name=to_tokenid,json=toTokenid" json:"to_tokenid"`
}

func (m *TextCheckReq) Reset()                    { *m = TextCheckReq{} }
func (m *TextCheckReq) String() string            { return proto.CompactTextString(m) }
func (*TextCheckReq) ProtoMessage()               {}
func (*TextCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{0} }

func (m *TextCheckReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *TextCheckReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *TextCheckReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *TextCheckReq) GetDeviceType() TextCheckReq_DeviceType {
	if m != nil {
		return m.DeviceType
	}
	return TextCheckReq_none
}

func (m *TextCheckReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *TextCheckReq) GetDataType() uint32 {
	if m != nil {
		return m.DataType
	}
	return 0
}

func (m *TextCheckReq) GetDataId() string {
	if m != nil {
		return m.DataId
	}
	return ""
}

func (m *TextCheckReq) GetShumeiDeviceId() string {
	if m != nil {
		return m.ShumeiDeviceId
	}
	return ""
}

func (m *TextCheckReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *TextCheckReq) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *TextCheckReq) GetRoomDisplayId() uint32 {
	if m != nil {
		return m.RoomDisplayId
	}
	return 0
}

func (m *TextCheckReq) GetSceneType() string {
	if m != nil {
		return m.SceneType
	}
	return ""
}

func (m *TextCheckReq) GetToTokenid() string {
	if m != nil {
		return m.ToTokenid
	}
	return ""
}

type TextCheckResp struct {
	Result    TextCheckResp_RESULT `protobuf:"varint,1,req,name=result,enum=Antispam.TextCheckResp_RESULT" json:"result"`
	LabelInfo string               `protobuf:"bytes,2,opt,name=label_info,json=labelInfo" json:"label_info"`
	RequestId string               `protobuf:"bytes,3,opt,name=requestId" json:"requestId"`
	RiskType  uint32               `protobuf:"varint,4,opt,name=risk_type,json=riskType" json:"risk_type"`
}

func (m *TextCheckResp) Reset()                    { *m = TextCheckResp{} }
func (m *TextCheckResp) String() string            { return proto.CompactTextString(m) }
func (*TextCheckResp) ProtoMessage()               {}
func (*TextCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{1} }

func (m *TextCheckResp) GetResult() TextCheckResp_RESULT {
	if m != nil {
		return m.Result
	}
	return TextCheckResp_PASS
}

func (m *TextCheckResp) GetLabelInfo() string {
	if m != nil {
		return m.LabelInfo
	}
	return ""
}

func (m *TextCheckResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *TextCheckResp) GetRiskType() uint32 {
	if m != nil {
		return m.RiskType
	}
	return 0
}

// 注册时行为检测
type RegisterCheckReq struct {
	Token    string `protobuf:"bytes,1,req,name=token" json:"token"`
	Account  string `protobuf:"bytes,2,opt,name=account" json:"account"`
	Email    string `protobuf:"bytes,3,opt,name=email" json:"email"`
	Phone    string `protobuf:"bytes,4,opt,name=phone" json:"phone"`
	Ip       string `protobuf:"bytes,5,opt,name=ip" json:"ip"`
	Nickname string `protobuf:"bytes,6,opt,name=nickname" json:"nickname"`
}

func (m *RegisterCheckReq) Reset()                    { *m = RegisterCheckReq{} }
func (m *RegisterCheckReq) String() string            { return proto.CompactTextString(m) }
func (*RegisterCheckReq) ProtoMessage()               {}
func (*RegisterCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{2} }

func (m *RegisterCheckReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *RegisterCheckReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *RegisterCheckReq) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

func (m *RegisterCheckReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RegisterCheckReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *RegisterCheckReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

type RegisterCheckResp struct {
	Result  UserBehavierCheckResult        `protobuf:"varint,1,req,name=result,enum=Antispam.UserBehavierCheckResult" json:"result"`
	HitType UserBehavierCheckResultHitType `protobuf:"varint,2,req,name=hit_type,json=hitType,enum=Antispam.UserBehavierCheckResultHitType" json:"hit_type"`
	TaskId  string                         `protobuf:"bytes,3,req,name=task_id,json=taskId" json:"task_id"`
}

func (m *RegisterCheckResp) Reset()                    { *m = RegisterCheckResp{} }
func (m *RegisterCheckResp) String() string            { return proto.CompactTextString(m) }
func (*RegisterCheckResp) ProtoMessage()               {}
func (*RegisterCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{3} }

func (m *RegisterCheckResp) GetResult() UserBehavierCheckResult {
	if m != nil {
		return m.Result
	}
	return UserBehavierCheckResult_RESULT_OK
}

func (m *RegisterCheckResp) GetHitType() UserBehavierCheckResultHitType {
	if m != nil {
		return m.HitType
	}
	return UserBehavierCheckResultHitType_HIT_OK
}

func (m *RegisterCheckResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

// 登录时行为检测
type AuthCheckReq struct {
	Token        string `protobuf:"bytes,1,req,name=token" json:"token"`
	Account      string `protobuf:"bytes,2,opt,name=account" json:"account"`
	Email        string `protobuf:"bytes,3,opt,name=email" json:"email"`
	Phone        string `protobuf:"bytes,4,opt,name=phone" json:"phone"`
	Ip           string `protobuf:"bytes,5,opt,name=ip" json:"ip"`
	RegisterTime uint32 `protobuf:"varint,6,opt,name=register_time,json=registerTime" json:"register_time"`
	RegisterIp   string `protobuf:"bytes,7,opt,name=register_ip,json=registerIp" json:"register_ip"`
}

func (m *AuthCheckReq) Reset()                    { *m = AuthCheckReq{} }
func (m *AuthCheckReq) String() string            { return proto.CompactTextString(m) }
func (*AuthCheckReq) ProtoMessage()               {}
func (*AuthCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{4} }

func (m *AuthCheckReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *AuthCheckReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AuthCheckReq) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

func (m *AuthCheckReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *AuthCheckReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *AuthCheckReq) GetRegisterTime() uint32 {
	if m != nil {
		return m.RegisterTime
	}
	return 0
}

func (m *AuthCheckReq) GetRegisterIp() string {
	if m != nil {
		return m.RegisterIp
	}
	return ""
}

type AuthCheckResp struct {
	Result  UserBehavierCheckResult        `protobuf:"varint,1,req,name=result,enum=Antispam.UserBehavierCheckResult" json:"result"`
	HitType UserBehavierCheckResultHitType `protobuf:"varint,2,req,name=hit_type,json=hitType,enum=Antispam.UserBehavierCheckResultHitType" json:"hit_type"`
	TaskId  string                         `protobuf:"bytes,3,req,name=task_id,json=taskId" json:"task_id"`
}

func (m *AuthCheckResp) Reset()                    { *m = AuthCheckResp{} }
func (m *AuthCheckResp) String() string            { return proto.CompactTextString(m) }
func (*AuthCheckResp) ProtoMessage()               {}
func (*AuthCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{5} }

func (m *AuthCheckResp) GetResult() UserBehavierCheckResult {
	if m != nil {
		return m.Result
	}
	return UserBehavierCheckResult_RESULT_OK
}

func (m *AuthCheckResp) GetHitType() UserBehavierCheckResultHitType {
	if m != nil {
		return m.HitType
	}
	return UserBehavierCheckResultHitType_HIT_OK
}

func (m *AuthCheckResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

// 营销活动行为检测
type CommonActivityCheckReq struct {
	Token        string `protobuf:"bytes,1,req,name=token" json:"token"`
	Account      string `protobuf:"bytes,2,opt,name=account" json:"account"`
	Email        string `protobuf:"bytes,3,opt,name=email" json:"email"`
	Phone        string `protobuf:"bytes,4,opt,name=phone" json:"phone"`
	Ip           string `protobuf:"bytes,5,opt,name=ip" json:"ip"`
	RegisterTime uint32 `protobuf:"varint,6,opt,name=register_time,json=registerTime" json:"register_time"`
	RegisterIp   string `protobuf:"bytes,7,opt,name=register_ip,json=registerIp" json:"register_ip"`
	ActivityId   string `protobuf:"bytes,8,opt,name=activity_id,json=activityId" json:"activity_id"`
	Target       string `protobuf:"bytes,9,opt,name=target" json:"target"`
}

func (m *CommonActivityCheckReq) Reset()                    { *m = CommonActivityCheckReq{} }
func (m *CommonActivityCheckReq) String() string            { return proto.CompactTextString(m) }
func (*CommonActivityCheckReq) ProtoMessage()               {}
func (*CommonActivityCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{6} }

func (m *CommonActivityCheckReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *CommonActivityCheckReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *CommonActivityCheckReq) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

func (m *CommonActivityCheckReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *CommonActivityCheckReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *CommonActivityCheckReq) GetRegisterTime() uint32 {
	if m != nil {
		return m.RegisterTime
	}
	return 0
}

func (m *CommonActivityCheckReq) GetRegisterIp() string {
	if m != nil {
		return m.RegisterIp
	}
	return ""
}

func (m *CommonActivityCheckReq) GetActivityId() string {
	if m != nil {
		return m.ActivityId
	}
	return ""
}

func (m *CommonActivityCheckReq) GetTarget() string {
	if m != nil {
		return m.Target
	}
	return ""
}

type CommonActivityCheckResp struct {
	Result  UserBehavierCheckResult        `protobuf:"varint,1,req,name=result,enum=Antispam.UserBehavierCheckResult" json:"result"`
	HitType UserBehavierCheckResultHitType `protobuf:"varint,2,req,name=hit_type,json=hitType,enum=Antispam.UserBehavierCheckResultHitType" json:"hit_type"`
	TaskId  string                         `protobuf:"bytes,3,req,name=task_id,json=taskId" json:"task_id"`
}

func (m *CommonActivityCheckResp) Reset()                    { *m = CommonActivityCheckResp{} }
func (m *CommonActivityCheckResp) String() string            { return proto.CompactTextString(m) }
func (*CommonActivityCheckResp) ProtoMessage()               {}
func (*CommonActivityCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{7} }

func (m *CommonActivityCheckResp) GetResult() UserBehavierCheckResult {
	if m != nil {
		return m.Result
	}
	return UserBehavierCheckResult_RESULT_OK
}

func (m *CommonActivityCheckResp) GetHitType() UserBehavierCheckResultHitType {
	if m != nil {
		return m.HitType
	}
	return UserBehavierCheckResultHitType_HIT_OK
}

func (m *CommonActivityCheckResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

// 验证码验证
type VerifyCodeCheckReq struct {
	CaptchaId string `protobuf:"bytes,1,req,name=captcha_id,json=captchaId" json:"captcha_id"`
	Validate  string `protobuf:"bytes,2,req,name=validate" json:"validate"`
	User      string `protobuf:"bytes,3,opt,name=user" json:"user"`
}

func (m *VerifyCodeCheckReq) Reset()                    { *m = VerifyCodeCheckReq{} }
func (m *VerifyCodeCheckReq) String() string            { return proto.CompactTextString(m) }
func (*VerifyCodeCheckReq) ProtoMessage()               {}
func (*VerifyCodeCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{8} }

func (m *VerifyCodeCheckReq) GetCaptchaId() string {
	if m != nil {
		return m.CaptchaId
	}
	return ""
}

func (m *VerifyCodeCheckReq) GetValidate() string {
	if m != nil {
		return m.Validate
	}
	return ""
}

func (m *VerifyCodeCheckReq) GetUser() string {
	if m != nil {
		return m.User
	}
	return ""
}

type VerifyCodeCheckResp struct {
	IsPass bool `protobuf:"varint,1,req,name=is_pass,json=isPass" json:"is_pass"`
}

func (m *VerifyCodeCheckResp) Reset()                    { *m = VerifyCodeCheckResp{} }
func (m *VerifyCodeCheckResp) String() string            { return proto.CompactTextString(m) }
func (*VerifyCodeCheckResp) ProtoMessage()               {}
func (*VerifyCodeCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{9} }

func (m *VerifyCodeCheckResp) GetIsPass() bool {
	if m != nil {
		return m.IsPass
	}
	return false
}

type GpsLocation struct {
	GpsLongitude float64 `protobuf:"fixed64,1,req,name=gps_longitude,json=gpsLongitude" json:"gps_longitude"`
	GpsLatitude  float64 `protobuf:"fixed64,2,req,name=gps_latitude,json=gpsLatitude" json:"gps_latitude"`
}

func (m *GpsLocation) Reset()                    { *m = GpsLocation{} }
func (m *GpsLocation) String() string            { return proto.CompactTextString(m) }
func (*GpsLocation) ProtoMessage()               {}
func (*GpsLocation) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{10} }

func (m *GpsLocation) GetGpsLongitude() float64 {
	if m != nil {
		return m.GpsLongitude
	}
	return 0
}

func (m *GpsLocation) GetGpsLatitude() float64 {
	if m != nil {
		return m.GpsLatitude
	}
	return 0
}

// 数美注册检测
type ShumeiRegisterCheckReq struct {
	AppId          string                                    `protobuf:"bytes,1,opt,name=appId" json:"appId"`
	Account        string                                    `protobuf:"bytes,2,req,name=account" json:"account"`
	ClientIp       string                                    `protobuf:"bytes,3,req,name=client_ip,json=clientIp" json:"client_ip"`
	Timestamp      int64                                     `protobuf:"varint,4,req,name=timestamp" json:"timestamp"`
	ShumeiDeviceid string                                    `protobuf:"bytes,5,opt,name=shumei_deviceid,json=shumeiDeviceid" json:"shumei_deviceid"`
	Phone          string                                    `protobuf:"bytes,6,opt,name=phone" json:"phone"`
	Os             ShumeiOSType                              `protobuf:"varint,7,opt,name=os,enum=Antispam.ShumeiOSType" json:"os"`
	AppVersion     string                                    `protobuf:"bytes,8,opt,name=app_version,json=appVersion" json:"app_version"`
	Nickname       string                                    `protobuf:"bytes,9,opt,name=nickname" json:"nickname"`
	GpsLocation    *GpsLocation                              `protobuf:"bytes,10,opt,name=gps_location,json=gpsLocation" json:"gps_location,omitempty"`
	SignupPlatform ShumeiRegisterCheckReq_SignupPlatformType `protobuf:"varint,11,opt,name=signup_platform,json=signupPlatform,enum=Antispam.ShumeiRegisterCheckReq_SignupPlatformType" json:"signup_platform"`
}

func (m *ShumeiRegisterCheckReq) Reset()                    { *m = ShumeiRegisterCheckReq{} }
func (m *ShumeiRegisterCheckReq) String() string            { return proto.CompactTextString(m) }
func (*ShumeiRegisterCheckReq) ProtoMessage()               {}
func (*ShumeiRegisterCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{11} }

func (m *ShumeiRegisterCheckReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *ShumeiRegisterCheckReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ShumeiRegisterCheckReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *ShumeiRegisterCheckReq) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ShumeiRegisterCheckReq) GetShumeiDeviceid() string {
	if m != nil {
		return m.ShumeiDeviceid
	}
	return ""
}

func (m *ShumeiRegisterCheckReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *ShumeiRegisterCheckReq) GetOs() ShumeiOSType {
	if m != nil {
		return m.Os
	}
	return ShumeiOSType_none
}

func (m *ShumeiRegisterCheckReq) GetAppVersion() string {
	if m != nil {
		return m.AppVersion
	}
	return ""
}

func (m *ShumeiRegisterCheckReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ShumeiRegisterCheckReq) GetGpsLocation() *GpsLocation {
	if m != nil {
		return m.GpsLocation
	}
	return nil
}

func (m *ShumeiRegisterCheckReq) GetSignupPlatform() ShumeiRegisterCheckReq_SignupPlatformType {
	if m != nil {
		return m.SignupPlatform
	}
	return ShumeiRegisterCheckReq_tt
}

type ShumeiRegisterCheckResp struct {
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId" json:"request_id"`
	Code      int32  `protobuf:"varint,2,opt,name=code" json:"code"`
	Message   string `protobuf:"bytes,3,opt,name=message" json:"message"`
	RiskLevel string `protobuf:"bytes,4,opt,name=risk_level,json=riskLevel" json:"risk_level"`
	Score     int32  `protobuf:"varint,5,opt,name=score" json:"score"`
}

func (m *ShumeiRegisterCheckResp) Reset()                    { *m = ShumeiRegisterCheckResp{} }
func (m *ShumeiRegisterCheckResp) String() string            { return proto.CompactTextString(m) }
func (*ShumeiRegisterCheckResp) ProtoMessage()               {}
func (*ShumeiRegisterCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{12} }

func (m *ShumeiRegisterCheckResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *ShumeiRegisterCheckResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ShumeiRegisterCheckResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ShumeiRegisterCheckResp) GetRiskLevel() string {
	if m != nil {
		return m.RiskLevel
	}
	return ""
}

func (m *ShumeiRegisterCheckResp) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

// 数美登录检测
type ShumeiAuthCheckReq struct {
	AppId          string       `protobuf:"bytes,1,opt,name=appId" json:"appId"`
	Account        string       `protobuf:"bytes,2,req,name=account" json:"account"`
	ClientIp       string       `protobuf:"bytes,3,req,name=client_ip,json=clientIp" json:"client_ip"`
	Timestamp      int64        `protobuf:"varint,4,req,name=timestamp" json:"timestamp"`
	ShumeiDeviceid string       `protobuf:"bytes,5,opt,name=shumei_deviceid,json=shumeiDeviceid" json:"shumei_deviceid"`
	Phone          string       `protobuf:"bytes,6,opt,name=phone" json:"phone"`
	Os             ShumeiOSType `protobuf:"varint,7,opt,name=os,enum=Antispam.ShumeiOSType" json:"os"`
	AppVersion     string       `protobuf:"bytes,8,opt,name=app_version,json=appVersion" json:"app_version"`
	PasswdMatched  int32        `protobuf:"varint,9,opt,name=passwd_matched,json=passwdMatched" json:"passwd_matched"`
}

func (m *ShumeiAuthCheckReq) Reset()                    { *m = ShumeiAuthCheckReq{} }
func (m *ShumeiAuthCheckReq) String() string            { return proto.CompactTextString(m) }
func (*ShumeiAuthCheckReq) ProtoMessage()               {}
func (*ShumeiAuthCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{13} }

func (m *ShumeiAuthCheckReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *ShumeiAuthCheckReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ShumeiAuthCheckReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *ShumeiAuthCheckReq) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ShumeiAuthCheckReq) GetShumeiDeviceid() string {
	if m != nil {
		return m.ShumeiDeviceid
	}
	return ""
}

func (m *ShumeiAuthCheckReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *ShumeiAuthCheckReq) GetOs() ShumeiOSType {
	if m != nil {
		return m.Os
	}
	return ShumeiOSType_none
}

func (m *ShumeiAuthCheckReq) GetAppVersion() string {
	if m != nil {
		return m.AppVersion
	}
	return ""
}

func (m *ShumeiAuthCheckReq) GetPasswdMatched() int32 {
	if m != nil {
		return m.PasswdMatched
	}
	return 0
}

type ShumeiAuthCheckResp struct {
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId" json:"request_id"`
	Code      int32  `protobuf:"varint,2,opt,name=code" json:"code"`
	Message   string `protobuf:"bytes,3,opt,name=message" json:"message"`
	RiskLevel string `protobuf:"bytes,4,opt,name=risk_level,json=riskLevel" json:"risk_level"`
	Score     int32  `protobuf:"varint,5,opt,name=score" json:"score"`
}

func (m *ShumeiAuthCheckResp) Reset()                    { *m = ShumeiAuthCheckResp{} }
func (m *ShumeiAuthCheckResp) String() string            { return proto.CompactTextString(m) }
func (*ShumeiAuthCheckResp) ProtoMessage()               {}
func (*ShumeiAuthCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{14} }

func (m *ShumeiAuthCheckResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *ShumeiAuthCheckResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ShumeiAuthCheckResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ShumeiAuthCheckResp) GetRiskLevel() string {
	if m != nil {
		return m.RiskLevel
	}
	return ""
}

func (m *ShumeiAuthCheckResp) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

// shumei text check
type ShumeiTextCheckReq struct {
	ShumeiDeviceId string `protobuf:"bytes,1,opt,name=shumei_device_id,json=shumeiDeviceId" json:"shumei_device_id"`
	Uid            uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
	Username       string `protobuf:"bytes,3,opt,name=username" json:"username"`
	Alias          string `protobuf:"bytes,4,req,name=alias" json:"alias"`
	Text           string `protobuf:"bytes,5,req,name=text" json:"text"`
	Ip             string `protobuf:"bytes,6,opt,name=ip" json:"ip"`
	Nickname       string `protobuf:"bytes,7,opt,name=nickname" json:"nickname"`
	DataType       int32  `protobuf:"varint,8,opt,name=data_type,json=dataType" json:"data_type"`
	RoomDisplayId  uint32 `protobuf:"varint,9,opt,name=room_display_id,json=roomDisplayId" json:"room_display_id"`
}

func (m *ShumeiTextCheckReq) Reset()                    { *m = ShumeiTextCheckReq{} }
func (m *ShumeiTextCheckReq) String() string            { return proto.CompactTextString(m) }
func (*ShumeiTextCheckReq) ProtoMessage()               {}
func (*ShumeiTextCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{15} }

func (m *ShumeiTextCheckReq) GetShumeiDeviceId() string {
	if m != nil {
		return m.ShumeiDeviceId
	}
	return ""
}

func (m *ShumeiTextCheckReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ShumeiTextCheckReq) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *ShumeiTextCheckReq) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *ShumeiTextCheckReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ShumeiTextCheckReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *ShumeiTextCheckReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ShumeiTextCheckReq) GetDataType() int32 {
	if m != nil {
		return m.DataType
	}
	return 0
}

func (m *ShumeiTextCheckReq) GetRoomDisplayId() uint32 {
	if m != nil {
		return m.RoomDisplayId
	}
	return 0
}

type ShumeiTextCheckResp struct {
	Score     int32  `protobuf:"varint,1,opt,name=score" json:"score"`
	RiskLevel string `protobuf:"bytes,2,opt,name=riskLevel" json:"riskLevel"`
	RequestId string `protobuf:"bytes,3,opt,name=requestId" json:"requestId"`
}

func (m *ShumeiTextCheckResp) Reset()                    { *m = ShumeiTextCheckResp{} }
func (m *ShumeiTextCheckResp) String() string            { return proto.CompactTextString(m) }
func (*ShumeiTextCheckResp) ProtoMessage()               {}
func (*ShumeiTextCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{16} }

func (m *ShumeiTextCheckResp) GetScore() int32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ShumeiTextCheckResp) GetRiskLevel() string {
	if m != nil {
		return m.RiskLevel
	}
	return ""
}

func (m *ShumeiTextCheckResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

type AntispamUserInfo struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	Tokenid     string `protobuf:"bytes,2,opt,name=tokenid" json:"tokenid"`
	Nickname    string `protobuf:"bytes,3,opt,name=nickname" json:"nickname"`
	Gender      uint32 `protobuf:"varint,4,opt,name=gender" json:"gender"`
	PhoneNumber string `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber" json:"phone_number"`
}

func (m *AntispamUserInfo) Reset()                    { *m = AntispamUserInfo{} }
func (m *AntispamUserInfo) String() string            { return proto.CompactTextString(m) }
func (*AntispamUserInfo) ProtoMessage()               {}
func (*AntispamUserInfo) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{17} }

func (m *AntispamUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AntispamUserInfo) GetTokenid() string {
	if m != nil {
		return m.Tokenid
	}
	return ""
}

func (m *AntispamUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AntispamUserInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *AntispamUserInfo) GetPhoneNumber() string {
	if m != nil {
		return m.PhoneNumber
	}
	return ""
}

type DVRegisterCheckReq struct {
	UserInfo       *AntispamUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo" json:"user_info,omitempty"`
	Ip             string            `protobuf:"bytes,2,opt,name=ip" json:"ip"`
	DeviceInfo     string            `protobuf:"bytes,3,opt,name=device_info,json=deviceInfo" json:"device_info"`
	OsVer          string            `protobuf:"bytes,4,opt,name=os_ver,json=osVer" json:"os_ver"`
	OsType         string            `protobuf:"bytes,5,opt,name=os_type,json=osType" json:"os_type"`
	ClientVersion  uint32            `protobuf:"varint,6,opt,name=client_version,json=clientVersion" json:"client_version"`
	DeviceId       string            `protobuf:"bytes,7,opt,name=device_id,json=deviceId" json:"device_id"`
	OriginChannel  string            `protobuf:"bytes,8,opt,name=origin_channel,json=originChannel" json:"origin_channel"`
	CurrentChannel string            `protobuf:"bytes,9,opt,name=current_channel,json=currentChannel" json:"current_channel"`
	RegType        string            `protobuf:"bytes,10,opt,name=reg_type,json=regType" json:"reg_type"`
	Imei           string            `protobuf:"bytes,11,opt,name=imei" json:"imei"`
	Useraddtime    string            `protobuf:"bytes,12,opt,name=useraddtime" json:"useraddtime"`
	Net            string            `protobuf:"bytes,13,opt,name=net" json:"net"`
	Language       string            `protobuf:"bytes,14,opt,name=language" json:"language"`
	Ssid           string            `protobuf:"bytes,15,opt,name=ssid" json:"ssid"`
	FreeStorage    string            `protobuf:"bytes,16,opt,name=free_storage,json=freeStorage" json:"free_storage"`
	Resolution     string            `protobuf:"bytes,17,opt,name=resolution" json:"resolution"`
	SystemVolume   string            `protobuf:"bytes,18,opt,name=system_volume,json=systemVolume" json:"system_volume"`
	TotalStorage   string            `protobuf:"bytes,19,opt,name=total_storage,json=totalStorage" json:"total_storage"`
	Mac            string            `protobuf:"bytes,20,opt,name=mac" json:"mac"`
	BatteryState   string            `protobuf:"bytes,21,opt,name=battery_state,json=batteryState" json:"battery_state"`
	Memory         string            `protobuf:"bytes,22,opt,name=memory" json:"memory"`
	DeviceCarrier  string            `protobuf:"bytes,23,opt,name=device_carrier,json=deviceCarrier" json:"device_carrier"`
	BatteryLevel   string            `protobuf:"bytes,24,opt,name=battery_level,json=batteryLevel" json:"battery_level"`
	DeviceName     string            `protobuf:"bytes,25,opt,name=device_name,json=deviceName" json:"device_name"`
}

func (m *DVRegisterCheckReq) Reset()                    { *m = DVRegisterCheckReq{} }
func (m *DVRegisterCheckReq) String() string            { return proto.CompactTextString(m) }
func (*DVRegisterCheckReq) ProtoMessage()               {}
func (*DVRegisterCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{18} }

func (m *DVRegisterCheckReq) GetUserInfo() *AntispamUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *DVRegisterCheckReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *DVRegisterCheckReq) GetDeviceInfo() string {
	if m != nil {
		return m.DeviceInfo
	}
	return ""
}

func (m *DVRegisterCheckReq) GetOsVer() string {
	if m != nil {
		return m.OsVer
	}
	return ""
}

func (m *DVRegisterCheckReq) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *DVRegisterCheckReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *DVRegisterCheckReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *DVRegisterCheckReq) GetOriginChannel() string {
	if m != nil {
		return m.OriginChannel
	}
	return ""
}

func (m *DVRegisterCheckReq) GetCurrentChannel() string {
	if m != nil {
		return m.CurrentChannel
	}
	return ""
}

func (m *DVRegisterCheckReq) GetRegType() string {
	if m != nil {
		return m.RegType
	}
	return ""
}

func (m *DVRegisterCheckReq) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *DVRegisterCheckReq) GetUseraddtime() string {
	if m != nil {
		return m.Useraddtime
	}
	return ""
}

func (m *DVRegisterCheckReq) GetNet() string {
	if m != nil {
		return m.Net
	}
	return ""
}

func (m *DVRegisterCheckReq) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

func (m *DVRegisterCheckReq) GetSsid() string {
	if m != nil {
		return m.Ssid
	}
	return ""
}

func (m *DVRegisterCheckReq) GetFreeStorage() string {
	if m != nil {
		return m.FreeStorage
	}
	return ""
}

func (m *DVRegisterCheckReq) GetResolution() string {
	if m != nil {
		return m.Resolution
	}
	return ""
}

func (m *DVRegisterCheckReq) GetSystemVolume() string {
	if m != nil {
		return m.SystemVolume
	}
	return ""
}

func (m *DVRegisterCheckReq) GetTotalStorage() string {
	if m != nil {
		return m.TotalStorage
	}
	return ""
}

func (m *DVRegisterCheckReq) GetMac() string {
	if m != nil {
		return m.Mac
	}
	return ""
}

func (m *DVRegisterCheckReq) GetBatteryState() string {
	if m != nil {
		return m.BatteryState
	}
	return ""
}

func (m *DVRegisterCheckReq) GetMemory() string {
	if m != nil {
		return m.Memory
	}
	return ""
}

func (m *DVRegisterCheckReq) GetDeviceCarrier() string {
	if m != nil {
		return m.DeviceCarrier
	}
	return ""
}

func (m *DVRegisterCheckReq) GetBatteryLevel() string {
	if m != nil {
		return m.BatteryLevel
	}
	return ""
}

func (m *DVRegisterCheckReq) GetDeviceName() string {
	if m != nil {
		return m.DeviceName
	}
	return ""
}

type DVLoginCheckReq struct {
	UserInfo      *AntispamUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo" json:"user_info,omitempty"`
	EventType     string            `protobuf:"bytes,2,opt,name=event_type,json=eventType" json:"event_type"`
	Ip            string            `protobuf:"bytes,3,opt,name=ip" json:"ip"`
	ClientVersion uint32            `protobuf:"varint,4,opt,name=clientVersion" json:"clientVersion"`
	EventTime     string            `protobuf:"bytes,5,opt,name=event_time,json=eventTime" json:"event_time"`
	DeviceId      string            `protobuf:"bytes,6,opt,name=device_id,json=deviceId" json:"device_id"`
	OsVer         string            `protobuf:"bytes,7,opt,name=os_ver,json=osVer" json:"os_ver"`
}

func (m *DVLoginCheckReq) Reset()                    { *m = DVLoginCheckReq{} }
func (m *DVLoginCheckReq) String() string            { return proto.CompactTextString(m) }
func (*DVLoginCheckReq) ProtoMessage()               {}
func (*DVLoginCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{19} }

func (m *DVLoginCheckReq) GetUserInfo() *AntispamUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *DVLoginCheckReq) GetEventType() string {
	if m != nil {
		return m.EventType
	}
	return ""
}

func (m *DVLoginCheckReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *DVLoginCheckReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *DVLoginCheckReq) GetEventTime() string {
	if m != nil {
		return m.EventTime
	}
	return ""
}

func (m *DVLoginCheckReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *DVLoginCheckReq) GetOsVer() string {
	if m != nil {
		return m.OsVer
	}
	return ""
}

type DVCommCheckReq struct {
	UserInfo   *AntispamUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo" json:"user_info,omitempty"`
	ToUserInfo *AntispamUserInfo `protobuf:"bytes,2,opt,name=to_user_info,json=toUserInfo" json:"to_user_info,omitempty"`
	EventType  string            `protobuf:"bytes,3,opt,name=event_type,json=eventType" json:"event_type"`
	EventTime  string            `protobuf:"bytes,4,opt,name=event_time,json=eventTime" json:"event_time"`
	ExtInfo    string            `protobuf:"bytes,5,opt,name=ext_info,json=extInfo" json:"ext_info"`
}

func (m *DVCommCheckReq) Reset()                    { *m = DVCommCheckReq{} }
func (m *DVCommCheckReq) String() string            { return proto.CompactTextString(m) }
func (*DVCommCheckReq) ProtoMessage()               {}
func (*DVCommCheckReq) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{20} }

func (m *DVCommCheckReq) GetUserInfo() *AntispamUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *DVCommCheckReq) GetToUserInfo() *AntispamUserInfo {
	if m != nil {
		return m.ToUserInfo
	}
	return nil
}

func (m *DVCommCheckReq) GetEventType() string {
	if m != nil {
		return m.EventType
	}
	return ""
}

func (m *DVCommCheckReq) GetEventTime() string {
	if m != nil {
		return m.EventTime
	}
	return ""
}

func (m *DVCommCheckReq) GetExtInfo() string {
	if m != nil {
		return m.ExtInfo
	}
	return ""
}

type DVCheckResp struct {
	ResponseType string  `protobuf:"bytes,1,opt,name=responseType" json:"responseType"`
	CustNo       string  `protobuf:"bytes,2,opt,name=cust_no,json=custNo" json:"cust_no"`
	ApplyNo      string  `protobuf:"bytes,3,opt,name=apply_no,json=applyNo" json:"apply_no"`
	Score        float64 `protobuf:"fixed64,4,opt,name=score" json:"score"`
	ErrorType    string  `protobuf:"bytes,5,opt,name=errorType" json:"errorType"`
	ErrorDetail  string  `protobuf:"bytes,6,opt,name=errorDetail" json:"errorDetail"`
}

func (m *DVCheckResp) Reset()                    { *m = DVCheckResp{} }
func (m *DVCheckResp) String() string            { return proto.CompactTextString(m) }
func (*DVCheckResp) ProtoMessage()               {}
func (*DVCheckResp) Descriptor() ([]byte, []int) { return fileDescriptorAntispam, []int{21} }

func (m *DVCheckResp) GetResponseType() string {
	if m != nil {
		return m.ResponseType
	}
	return ""
}

func (m *DVCheckResp) GetCustNo() string {
	if m != nil {
		return m.CustNo
	}
	return ""
}

func (m *DVCheckResp) GetApplyNo() string {
	if m != nil {
		return m.ApplyNo
	}
	return ""
}

func (m *DVCheckResp) GetScore() float64 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *DVCheckResp) GetErrorType() string {
	if m != nil {
		return m.ErrorType
	}
	return ""
}

func (m *DVCheckResp) GetErrorDetail() string {
	if m != nil {
		return m.ErrorDetail
	}
	return ""
}

func init() {
	proto.RegisterType((*TextCheckReq)(nil), "Antispam.TextCheckReq")
	proto.RegisterType((*TextCheckResp)(nil), "Antispam.TextCheckResp")
	proto.RegisterType((*RegisterCheckReq)(nil), "Antispam.RegisterCheckReq")
	proto.RegisterType((*RegisterCheckResp)(nil), "Antispam.RegisterCheckResp")
	proto.RegisterType((*AuthCheckReq)(nil), "Antispam.AuthCheckReq")
	proto.RegisterType((*AuthCheckResp)(nil), "Antispam.AuthCheckResp")
	proto.RegisterType((*CommonActivityCheckReq)(nil), "Antispam.CommonActivityCheckReq")
	proto.RegisterType((*CommonActivityCheckResp)(nil), "Antispam.CommonActivityCheckResp")
	proto.RegisterType((*VerifyCodeCheckReq)(nil), "Antispam.VerifyCodeCheckReq")
	proto.RegisterType((*VerifyCodeCheckResp)(nil), "Antispam.VerifyCodeCheckResp")
	proto.RegisterType((*GpsLocation)(nil), "Antispam.GpsLocation")
	proto.RegisterType((*ShumeiRegisterCheckReq)(nil), "Antispam.ShumeiRegisterCheckReq")
	proto.RegisterType((*ShumeiRegisterCheckResp)(nil), "Antispam.ShumeiRegisterCheckResp")
	proto.RegisterType((*ShumeiAuthCheckReq)(nil), "Antispam.ShumeiAuthCheckReq")
	proto.RegisterType((*ShumeiAuthCheckResp)(nil), "Antispam.ShumeiAuthCheckResp")
	proto.RegisterType((*ShumeiTextCheckReq)(nil), "Antispam.ShumeiTextCheckReq")
	proto.RegisterType((*ShumeiTextCheckResp)(nil), "Antispam.ShumeiTextCheckResp")
	proto.RegisterType((*AntispamUserInfo)(nil), "Antispam.AntispamUserInfo")
	proto.RegisterType((*DVRegisterCheckReq)(nil), "Antispam.DVRegisterCheckReq")
	proto.RegisterType((*DVLoginCheckReq)(nil), "Antispam.DVLoginCheckReq")
	proto.RegisterType((*DVCommCheckReq)(nil), "Antispam.DVCommCheckReq")
	proto.RegisterType((*DVCheckResp)(nil), "Antispam.DVCheckResp")
	proto.RegisterEnum("Antispam.RiskType", RiskType_name, RiskType_value)
	proto.RegisterEnum("Antispam.UserBehavierCheckResult", UserBehavierCheckResult_name, UserBehavierCheckResult_value)
	proto.RegisterEnum("Antispam.UserBehavierCheckResultHitType", UserBehavierCheckResultHitType_name, UserBehavierCheckResultHitType_value)
	proto.RegisterEnum("Antispam.ShumeiOSType", ShumeiOSType_name, ShumeiOSType_value)
	proto.RegisterEnum("Antispam.TextCheckReq_DeviceType", TextCheckReq_DeviceType_name, TextCheckReq_DeviceType_value)
	proto.RegisterEnum("Antispam.TextCheckResp_RESULT", TextCheckResp_RESULT_name, TextCheckResp_RESULT_value)
	proto.RegisterEnum("Antispam.ShumeiRegisterCheckReq_SignupPlatformType", ShumeiRegisterCheckReq_SignupPlatformType_name, ShumeiRegisterCheckReq_SignupPlatformType_value)
	proto.RegisterEnum("Antispam.DVRegisterCheckReq_RegType", DVRegisterCheckReq_RegType_name, DVRegisterCheckReq_RegType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Antispam service

type AntispamClient interface {
	TextCheck(ctx context.Context, in *TextCheckReq, opts ...grpc.CallOption) (*TextCheckResp, error)
	RegisterCheck(ctx context.Context, in *RegisterCheckReq, opts ...grpc.CallOption) (*RegisterCheckResp, error)
	AuthCheck(ctx context.Context, in *AuthCheckReq, opts ...grpc.CallOption) (*AuthCheckResp, error)
	CommonActivityCheck(ctx context.Context, in *CommonActivityCheckReq, opts ...grpc.CallOption) (*CommonActivityCheckResp, error)
	VerifyCodeCheck(ctx context.Context, in *VerifyCodeCheckReq, opts ...grpc.CallOption) (*VerifyCodeCheckResp, error)
	ShumeiRegisterCheck(ctx context.Context, in *ShumeiRegisterCheckReq, opts ...grpc.CallOption) (*ShumeiRegisterCheckResp, error)
	ShumeiAuthCheck(ctx context.Context, in *ShumeiAuthCheckReq, opts ...grpc.CallOption) (*ShumeiAuthCheckResp, error)
	ShumeiTextCheck(ctx context.Context, in *ShumeiTextCheckReq, opts ...grpc.CallOption) (*ShumeiTextCheckResp, error)
	DVRegisterCheck(ctx context.Context, in *DVRegisterCheckReq, opts ...grpc.CallOption) (*DVCheckResp, error)
	DVLoginCheck(ctx context.Context, in *DVLoginCheckReq, opts ...grpc.CallOption) (*DVCheckResp, error)
	DVCommCheck(ctx context.Context, in *DVCommCheckReq, opts ...grpc.CallOption) (*DVCheckResp, error)
}

type antispamClient struct {
	cc *grpc.ClientConn
}

func NewAntispamClient(cc *grpc.ClientConn) AntispamClient {
	return &antispamClient{cc}
}

func (c *antispamClient) TextCheck(ctx context.Context, in *TextCheckReq, opts ...grpc.CallOption) (*TextCheckResp, error) {
	out := new(TextCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/TextCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamClient) RegisterCheck(ctx context.Context, in *RegisterCheckReq, opts ...grpc.CallOption) (*RegisterCheckResp, error) {
	out := new(RegisterCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/RegisterCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamClient) AuthCheck(ctx context.Context, in *AuthCheckReq, opts ...grpc.CallOption) (*AuthCheckResp, error) {
	out := new(AuthCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/AuthCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamClient) CommonActivityCheck(ctx context.Context, in *CommonActivityCheckReq, opts ...grpc.CallOption) (*CommonActivityCheckResp, error) {
	out := new(CommonActivityCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/CommonActivityCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamClient) VerifyCodeCheck(ctx context.Context, in *VerifyCodeCheckReq, opts ...grpc.CallOption) (*VerifyCodeCheckResp, error) {
	out := new(VerifyCodeCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/VerifyCodeCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamClient) ShumeiRegisterCheck(ctx context.Context, in *ShumeiRegisterCheckReq, opts ...grpc.CallOption) (*ShumeiRegisterCheckResp, error) {
	out := new(ShumeiRegisterCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/ShumeiRegisterCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamClient) ShumeiAuthCheck(ctx context.Context, in *ShumeiAuthCheckReq, opts ...grpc.CallOption) (*ShumeiAuthCheckResp, error) {
	out := new(ShumeiAuthCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/ShumeiAuthCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamClient) ShumeiTextCheck(ctx context.Context, in *ShumeiTextCheckReq, opts ...grpc.CallOption) (*ShumeiTextCheckResp, error) {
	out := new(ShumeiTextCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/ShumeiTextCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamClient) DVRegisterCheck(ctx context.Context, in *DVRegisterCheckReq, opts ...grpc.CallOption) (*DVCheckResp, error) {
	out := new(DVCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/DVRegisterCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamClient) DVLoginCheck(ctx context.Context, in *DVLoginCheckReq, opts ...grpc.CallOption) (*DVCheckResp, error) {
	out := new(DVCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/DVLoginCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antispamClient) DVCommCheck(ctx context.Context, in *DVCommCheckReq, opts ...grpc.CallOption) (*DVCheckResp, error) {
	out := new(DVCheckResp)
	err := grpc.Invoke(ctx, "/Antispam.Antispam/DVCommCheck", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Antispam service

type AntispamServer interface {
	TextCheck(context.Context, *TextCheckReq) (*TextCheckResp, error)
	RegisterCheck(context.Context, *RegisterCheckReq) (*RegisterCheckResp, error)
	AuthCheck(context.Context, *AuthCheckReq) (*AuthCheckResp, error)
	CommonActivityCheck(context.Context, *CommonActivityCheckReq) (*CommonActivityCheckResp, error)
	VerifyCodeCheck(context.Context, *VerifyCodeCheckReq) (*VerifyCodeCheckResp, error)
	ShumeiRegisterCheck(context.Context, *ShumeiRegisterCheckReq) (*ShumeiRegisterCheckResp, error)
	ShumeiAuthCheck(context.Context, *ShumeiAuthCheckReq) (*ShumeiAuthCheckResp, error)
	ShumeiTextCheck(context.Context, *ShumeiTextCheckReq) (*ShumeiTextCheckResp, error)
	DVRegisterCheck(context.Context, *DVRegisterCheckReq) (*DVCheckResp, error)
	DVLoginCheck(context.Context, *DVLoginCheckReq) (*DVCheckResp, error)
	DVCommCheck(context.Context, *DVCommCheckReq) (*DVCheckResp, error)
}

func RegisterAntispamServer(s *grpc.Server, srv AntispamServer) {
	s.RegisterService(&_Antispam_serviceDesc, srv)
}

func _Antispam_TextCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TextCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).TextCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/TextCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).TextCheck(ctx, req.(*TextCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antispam_RegisterCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).RegisterCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/RegisterCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).RegisterCheck(ctx, req.(*RegisterCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antispam_AuthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).AuthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/AuthCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).AuthCheck(ctx, req.(*AuthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antispam_CommonActivityCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonActivityCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).CommonActivityCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/CommonActivityCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).CommonActivityCheck(ctx, req.(*CommonActivityCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antispam_VerifyCodeCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyCodeCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).VerifyCodeCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/VerifyCodeCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).VerifyCodeCheck(ctx, req.(*VerifyCodeCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antispam_ShumeiRegisterCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShumeiRegisterCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).ShumeiRegisterCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/ShumeiRegisterCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).ShumeiRegisterCheck(ctx, req.(*ShumeiRegisterCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antispam_ShumeiAuthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShumeiAuthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).ShumeiAuthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/ShumeiAuthCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).ShumeiAuthCheck(ctx, req.(*ShumeiAuthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antispam_ShumeiTextCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShumeiTextCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).ShumeiTextCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/ShumeiTextCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).ShumeiTextCheck(ctx, req.(*ShumeiTextCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antispam_DVRegisterCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DVRegisterCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).DVRegisterCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/DVRegisterCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).DVRegisterCheck(ctx, req.(*DVRegisterCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antispam_DVLoginCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DVLoginCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).DVLoginCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/DVLoginCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).DVLoginCheck(ctx, req.(*DVLoginCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antispam_DVCommCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DVCommCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntispamServer).DVCommCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Antispam.Antispam/DVCommCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntispamServer).DVCommCheck(ctx, req.(*DVCommCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Antispam_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Antispam.Antispam",
	HandlerType: (*AntispamServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TextCheck",
			Handler:    _Antispam_TextCheck_Handler,
		},
		{
			MethodName: "RegisterCheck",
			Handler:    _Antispam_RegisterCheck_Handler,
		},
		{
			MethodName: "AuthCheck",
			Handler:    _Antispam_AuthCheck_Handler,
		},
		{
			MethodName: "CommonActivityCheck",
			Handler:    _Antispam_CommonActivityCheck_Handler,
		},
		{
			MethodName: "VerifyCodeCheck",
			Handler:    _Antispam_VerifyCodeCheck_Handler,
		},
		{
			MethodName: "ShumeiRegisterCheck",
			Handler:    _Antispam_ShumeiRegisterCheck_Handler,
		},
		{
			MethodName: "ShumeiAuthCheck",
			Handler:    _Antispam_ShumeiAuthCheck_Handler,
		},
		{
			MethodName: "ShumeiTextCheck",
			Handler:    _Antispam_ShumeiTextCheck_Handler,
		},
		{
			MethodName: "DVRegisterCheck",
			Handler:    _Antispam_DVRegisterCheck_Handler,
		},
		{
			MethodName: "DVLoginCheck",
			Handler:    _Antispam_DVLoginCheck_Handler,
		},
		{
			MethodName: "DVCommCheck",
			Handler:    _Antispam_DVCommCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/antispam/antispam.proto",
}

func (m *TextCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TextCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.DeviceType))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x30
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.DataType))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.DataId)))
	i += copy(dAtA[i:], m.DataId)
	dAtA[i] = 0x42
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ShumeiDeviceId)))
	i += copy(dAtA[i:], m.ShumeiDeviceId)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x52
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Alias)))
	i += copy(dAtA[i:], m.Alias)
	dAtA[i] = 0x58
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.RoomDisplayId))
	dAtA[i] = 0x62
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.SceneType)))
	i += copy(dAtA[i:], m.SceneType)
	dAtA[i] = 0x6a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ToTokenid)))
	i += copy(dAtA[i:], m.ToTokenid)
	return i, nil
}

func (m *TextCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TextCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.LabelInfo)))
	i += copy(dAtA[i:], m.LabelInfo)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.RiskType))
	return i, nil
}

func (m *RegisterCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegisterCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Email)))
	i += copy(dAtA[i:], m.Email)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	return i, nil
}

func (m *RegisterCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegisterCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.HitType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.TaskId)))
	i += copy(dAtA[i:], m.TaskId)
	return i, nil
}

func (m *AuthCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Email)))
	i += copy(dAtA[i:], m.Email)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x30
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.RegisterTime))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.RegisterIp)))
	i += copy(dAtA[i:], m.RegisterIp)
	return i, nil
}

func (m *AuthCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuthCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.HitType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.TaskId)))
	i += copy(dAtA[i:], m.TaskId)
	return i, nil
}

func (m *CommonActivityCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommonActivityCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Email)))
	i += copy(dAtA[i:], m.Email)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x30
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.RegisterTime))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.RegisterIp)))
	i += copy(dAtA[i:], m.RegisterIp)
	dAtA[i] = 0x42
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ActivityId)))
	i += copy(dAtA[i:], m.ActivityId)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Target)))
	i += copy(dAtA[i:], m.Target)
	return i, nil
}

func (m *CommonActivityCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommonActivityCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Result))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.HitType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.TaskId)))
	i += copy(dAtA[i:], m.TaskId)
	return i, nil
}

func (m *VerifyCodeCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyCodeCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.CaptchaId)))
	i += copy(dAtA[i:], m.CaptchaId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Validate)))
	i += copy(dAtA[i:], m.Validate)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.User)))
	i += copy(dAtA[i:], m.User)
	return i, nil
}

func (m *VerifyCodeCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyCodeCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsPass {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GpsLocation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GpsLocation) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x9
	i++
	i = encodeFixed64Antispam(dAtA, i, uint64(math3.Float64bits(float64(m.GpsLongitude))))
	dAtA[i] = 0x11
	i++
	i = encodeFixed64Antispam(dAtA, i, uint64(math3.Float64bits(float64(m.GpsLatitude))))
	return i, nil
}

func (m *ShumeiRegisterCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShumeiRegisterCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ShumeiDeviceid)))
	i += copy(dAtA[i:], m.ShumeiDeviceid)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x38
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Os))
	dAtA[i] = 0x42
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.AppVersion)))
	i += copy(dAtA[i:], m.AppVersion)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	if m.GpsLocation != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintAntispam(dAtA, i, uint64(m.GpsLocation.Size()))
		n1, err := m.GpsLocation.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x58
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.SignupPlatform))
	return i, nil
}

func (m *ShumeiRegisterCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShumeiRegisterCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Code))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Message)))
	i += copy(dAtA[i:], m.Message)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.RiskLevel)))
	i += copy(dAtA[i:], m.RiskLevel)
	dAtA[i] = 0x28
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Score))
	return i, nil
}

func (m *ShumeiAuthCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShumeiAuthCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ShumeiDeviceid)))
	i += copy(dAtA[i:], m.ShumeiDeviceid)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x38
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Os))
	dAtA[i] = 0x42
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.AppVersion)))
	i += copy(dAtA[i:], m.AppVersion)
	dAtA[i] = 0x48
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.PasswdMatched))
	return i, nil
}

func (m *ShumeiAuthCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShumeiAuthCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Code))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Message)))
	i += copy(dAtA[i:], m.Message)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.RiskLevel)))
	i += copy(dAtA[i:], m.RiskLevel)
	dAtA[i] = 0x28
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Score))
	return i, nil
}

func (m *ShumeiTextCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShumeiTextCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ShumeiDeviceId)))
	i += copy(dAtA[i:], m.ShumeiDeviceId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Username)))
	i += copy(dAtA[i:], m.Username)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Alias)))
	i += copy(dAtA[i:], m.Alias)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x40
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.DataType))
	dAtA[i] = 0x48
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.RoomDisplayId))
	return i, nil
}

func (m *ShumeiTextCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShumeiTextCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Score))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.RiskLevel)))
	i += copy(dAtA[i:], m.RiskLevel)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	return i, nil
}

func (m *AntispamUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AntispamUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Tokenid)))
	i += copy(dAtA[i:], m.Tokenid)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.Gender))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.PhoneNumber)))
	i += copy(dAtA[i:], m.PhoneNumber)
	return i, nil
}

func (m *DVRegisterCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DVRegisterCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAntispam(dAtA, i, uint64(m.UserInfo.Size()))
		n2, err := m.UserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.DeviceInfo)))
	i += copy(dAtA[i:], m.DeviceInfo)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.OsVer)))
	i += copy(dAtA[i:], m.OsVer)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.OsType)))
	i += copy(dAtA[i:], m.OsType)
	dAtA[i] = 0x30
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.ClientVersion))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x42
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.OriginChannel)))
	i += copy(dAtA[i:], m.OriginChannel)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.CurrentChannel)))
	i += copy(dAtA[i:], m.CurrentChannel)
	dAtA[i] = 0x52
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.RegType)))
	i += copy(dAtA[i:], m.RegType)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	dAtA[i] = 0x62
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Useraddtime)))
	i += copy(dAtA[i:], m.Useraddtime)
	dAtA[i] = 0x6a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Net)))
	i += copy(dAtA[i:], m.Net)
	dAtA[i] = 0x72
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Language)))
	i += copy(dAtA[i:], m.Language)
	dAtA[i] = 0x7a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Ssid)))
	i += copy(dAtA[i:], m.Ssid)
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.FreeStorage)))
	i += copy(dAtA[i:], m.FreeStorage)
	dAtA[i] = 0x8a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Resolution)))
	i += copy(dAtA[i:], m.Resolution)
	dAtA[i] = 0x92
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.SystemVolume)))
	i += copy(dAtA[i:], m.SystemVolume)
	dAtA[i] = 0x9a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.TotalStorage)))
	i += copy(dAtA[i:], m.TotalStorage)
	dAtA[i] = 0xa2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Mac)))
	i += copy(dAtA[i:], m.Mac)
	dAtA[i] = 0xaa
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.BatteryState)))
	i += copy(dAtA[i:], m.BatteryState)
	dAtA[i] = 0xb2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Memory)))
	i += copy(dAtA[i:], m.Memory)
	dAtA[i] = 0xba
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.DeviceCarrier)))
	i += copy(dAtA[i:], m.DeviceCarrier)
	dAtA[i] = 0xc2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.BatteryLevel)))
	i += copy(dAtA[i:], m.BatteryLevel)
	dAtA[i] = 0xca
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.DeviceName)))
	i += copy(dAtA[i:], m.DeviceName)
	return i, nil
}

func (m *DVLoginCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DVLoginCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAntispam(dAtA, i, uint64(m.UserInfo.Size()))
		n3, err := m.UserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.EventType)))
	i += copy(dAtA[i:], m.EventType)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(m.ClientVersion))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.EventTime)))
	i += copy(dAtA[i:], m.EventTime)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.OsVer)))
	i += copy(dAtA[i:], m.OsVer)
	return i, nil
}

func (m *DVCommCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DVCommCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAntispam(dAtA, i, uint64(m.UserInfo.Size()))
		n4, err := m.UserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.ToUserInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAntispam(dAtA, i, uint64(m.ToUserInfo.Size()))
		n5, err := m.ToUserInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.EventType)))
	i += copy(dAtA[i:], m.EventType)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.EventTime)))
	i += copy(dAtA[i:], m.EventTime)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ExtInfo)))
	i += copy(dAtA[i:], m.ExtInfo)
	return i, nil
}

func (m *DVCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DVCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ResponseType)))
	i += copy(dAtA[i:], m.ResponseType)
	dAtA[i] = 0x12
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.CustNo)))
	i += copy(dAtA[i:], m.CustNo)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ApplyNo)))
	i += copy(dAtA[i:], m.ApplyNo)
	dAtA[i] = 0x21
	i++
	i = encodeFixed64Antispam(dAtA, i, uint64(math3.Float64bits(float64(m.Score))))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ErrorType)))
	i += copy(dAtA[i:], m.ErrorType)
	dAtA[i] = 0x32
	i++
	i = encodeVarintAntispam(dAtA, i, uint64(len(m.ErrorDetail)))
	i += copy(dAtA[i:], m.ErrorDetail)
	return i, nil
}

func encodeFixed64Antispam(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Antispam(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAntispam(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *TextCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Text)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.DeviceType))
	l = len(m.DeviceId)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.DataType))
	l = len(m.DataId)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.ShumeiDeviceId)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Alias)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.RoomDisplayId))
	l = len(m.SceneType)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.ToTokenid)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *TextCheckResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispam(uint64(m.Result))
	l = len(m.LabelInfo)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.RequestId)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.RiskType))
	return n
}

func (m *RegisterCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Token)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Email)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *RegisterCheckResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispam(uint64(m.Result))
	n += 1 + sovAntispam(uint64(m.HitType))
	l = len(m.TaskId)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *AuthCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Token)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Email)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.RegisterTime))
	l = len(m.RegisterIp)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *AuthCheckResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispam(uint64(m.Result))
	n += 1 + sovAntispam(uint64(m.HitType))
	l = len(m.TaskId)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *CommonActivityCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Token)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Email)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.RegisterTime))
	l = len(m.RegisterIp)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.ActivityId)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Target)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *CommonActivityCheckResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispam(uint64(m.Result))
	n += 1 + sovAntispam(uint64(m.HitType))
	l = len(m.TaskId)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *VerifyCodeCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.CaptchaId)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Validate)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.User)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *VerifyCodeCheckResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *GpsLocation) Size() (n int) {
	var l int
	_ = l
	n += 9
	n += 9
	return n
}

func (m *ShumeiRegisterCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.ClientIp)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.Timestamp))
	l = len(m.ShumeiDeviceid)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.Os))
	l = len(m.AppVersion)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovAntispam(uint64(l))
	if m.GpsLocation != nil {
		l = m.GpsLocation.Size()
		n += 1 + l + sovAntispam(uint64(l))
	}
	n += 1 + sovAntispam(uint64(m.SignupPlatform))
	return n
}

func (m *ShumeiRegisterCheckResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.RequestId)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.Code))
	l = len(m.Message)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.RiskLevel)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.Score))
	return n
}

func (m *ShumeiAuthCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.ClientIp)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.Timestamp))
	l = len(m.ShumeiDeviceid)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.Os))
	l = len(m.AppVersion)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.PasswdMatched))
	return n
}

func (m *ShumeiAuthCheckResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.RequestId)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.Code))
	l = len(m.Message)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.RiskLevel)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.Score))
	return n
}

func (m *ShumeiTextCheckReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.ShumeiDeviceId)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.Uid))
	l = len(m.Username)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Alias)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Text)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.DataType))
	n += 1 + sovAntispam(uint64(m.RoomDisplayId))
	return n
}

func (m *ShumeiTextCheckResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispam(uint64(m.Score))
	l = len(m.RiskLevel)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.RequestId)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *AntispamUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAntispam(uint64(m.Uid))
	l = len(m.Tokenid)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.Gender))
	l = len(m.PhoneNumber)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *DVRegisterCheckReq) Size() (n int) {
	var l int
	_ = l
	if m.UserInfo != nil {
		l = m.UserInfo.Size()
		n += 1 + l + sovAntispam(uint64(l))
	}
	l = len(m.Ip)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.DeviceInfo)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.OsVer)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.OsType)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.ClientVersion))
	l = len(m.DeviceId)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.OriginChannel)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.CurrentChannel)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.RegType)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Imei)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Useraddtime)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Net)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Language)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Ssid)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.FreeStorage)
	n += 2 + l + sovAntispam(uint64(l))
	l = len(m.Resolution)
	n += 2 + l + sovAntispam(uint64(l))
	l = len(m.SystemVolume)
	n += 2 + l + sovAntispam(uint64(l))
	l = len(m.TotalStorage)
	n += 2 + l + sovAntispam(uint64(l))
	l = len(m.Mac)
	n += 2 + l + sovAntispam(uint64(l))
	l = len(m.BatteryState)
	n += 2 + l + sovAntispam(uint64(l))
	l = len(m.Memory)
	n += 2 + l + sovAntispam(uint64(l))
	l = len(m.DeviceCarrier)
	n += 2 + l + sovAntispam(uint64(l))
	l = len(m.BatteryLevel)
	n += 2 + l + sovAntispam(uint64(l))
	l = len(m.DeviceName)
	n += 2 + l + sovAntispam(uint64(l))
	return n
}

func (m *DVLoginCheckReq) Size() (n int) {
	var l int
	_ = l
	if m.UserInfo != nil {
		l = m.UserInfo.Size()
		n += 1 + l + sovAntispam(uint64(l))
	}
	l = len(m.EventType)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovAntispam(uint64(l))
	n += 1 + sovAntispam(uint64(m.ClientVersion))
	l = len(m.EventTime)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.DeviceId)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.OsVer)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *DVCommCheckReq) Size() (n int) {
	var l int
	_ = l
	if m.UserInfo != nil {
		l = m.UserInfo.Size()
		n += 1 + l + sovAntispam(uint64(l))
	}
	if m.ToUserInfo != nil {
		l = m.ToUserInfo.Size()
		n += 1 + l + sovAntispam(uint64(l))
	}
	l = len(m.EventType)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.EventTime)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.ExtInfo)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func (m *DVCheckResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.ResponseType)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.CustNo)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.ApplyNo)
	n += 1 + l + sovAntispam(uint64(l))
	n += 9
	l = len(m.ErrorType)
	n += 1 + l + sovAntispam(uint64(l))
	l = len(m.ErrorDetail)
	n += 1 + l + sovAntispam(uint64(l))
	return n
}

func sovAntispam(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAntispam(x uint64) (n int) {
	return sovAntispam(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *TextCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TextCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TextCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceType", wireType)
			}
			m.DeviceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceType |= (TextCheckReq_DeviceType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataType", wireType)
			}
			m.DataType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DataType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DataId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShumeiDeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ShumeiDeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Alias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Alias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomDisplayId", wireType)
			}
			m.RoomDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SceneType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SceneType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToTokenid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ToTokenid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("text")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TextCheckResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TextCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TextCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (TextCheckResp_RESULT(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LabelInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LabelInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RiskType", wireType)
			}
			m.RiskType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RiskType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegisterCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RegisterCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RegisterCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Email", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Email = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegisterCheckResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RegisterCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RegisterCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (UserBehavierCheckResult(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HitType", wireType)
			}
			m.HitType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HitType |= (UserBehavierCheckResultHitType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TaskId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("result")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("hit_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("task_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Email", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Email = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegisterTime", wireType)
			}
			m.RegisterTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegisterTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegisterIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RegisterIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuthCheckResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuthCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuthCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (UserBehavierCheckResult(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HitType", wireType)
			}
			m.HitType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HitType |= (UserBehavierCheckResultHitType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TaskId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("result")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("hit_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("task_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommonActivityCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CommonActivityCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CommonActivityCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Email", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Email = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegisterTime", wireType)
			}
			m.RegisterTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegisterTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegisterIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RegisterIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Target", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Target = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommonActivityCheckResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CommonActivityCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CommonActivityCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (UserBehavierCheckResult(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HitType", wireType)
			}
			m.HitType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HitType |= (UserBehavierCheckResultHitType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TaskId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("result")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("hit_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("task_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyCodeCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyCodeCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyCodeCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CaptchaId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CaptchaId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Validate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Validate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field User", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.User = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("captcha_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("validate")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyCodeCheckResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyCodeCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyCodeCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPass", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPass = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_pass")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GpsLocation) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GpsLocation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GpsLocation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GpsLongitude", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.GpsLongitude = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GpsLatitude", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.GpsLatitude = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gps_longitude")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("gps_latitude")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShumeiRegisterCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ShumeiRegisterCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ShumeiRegisterCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShumeiDeviceid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ShumeiDeviceid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Os", wireType)
			}
			m.Os = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Os |= (ShumeiOSType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GpsLocation", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GpsLocation == nil {
				m.GpsLocation = &GpsLocation{}
			}
			if err := m.GpsLocation.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SignupPlatform", wireType)
			}
			m.SignupPlatform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignupPlatform |= (ShumeiRegisterCheckReq_SignupPlatformType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("client_ip")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShumeiRegisterCheckResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ShumeiRegisterCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ShumeiRegisterCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RiskLevel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RiskLevel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShumeiAuthCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ShumeiAuthCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ShumeiAuthCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShumeiDeviceid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ShumeiDeviceid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Os", wireType)
			}
			m.Os = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Os |= (ShumeiOSType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PasswdMatched", wireType)
			}
			m.PasswdMatched = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PasswdMatched |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("client_ip")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShumeiAuthCheckResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ShumeiAuthCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ShumeiAuthCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RiskLevel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RiskLevel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShumeiTextCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ShumeiTextCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ShumeiTextCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShumeiDeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ShumeiDeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Username", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Username = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Alias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Alias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataType", wireType)
			}
			m.DataType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DataType |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomDisplayId", wireType)
			}
			m.RoomDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("alias")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("text")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShumeiTextCheckResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ShumeiTextCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ShumeiTextCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RiskLevel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RiskLevel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AntispamUserInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AntispamUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AntispamUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tokenid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Tokenid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Gender", wireType)
			}
			m.Gender = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Gender |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneNumber", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhoneNumber = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DVRegisterCheckReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DVRegisterCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DVRegisterCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserInfo == nil {
				m.UserInfo = &AntispamUserInfo{}
			}
			if err := m.UserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OsVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OsVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OsType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OsType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientVersion", wireType)
			}
			m.ClientVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OriginChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OriginChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CurrentChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RegType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Useraddtime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Useraddtime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Net", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Net = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Language", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Language = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ssid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ssid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FreeStorage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FreeStorage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Resolution", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Resolution = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SystemVolume", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SystemVolume = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalStorage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TotalStorage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Mac", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Mac = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BatteryState", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BatteryState = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Memory", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Memory = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceCarrier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceCarrier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BatteryLevel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BatteryLevel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 25:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DVLoginCheckReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DVLoginCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DVLoginCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserInfo == nil {
				m.UserInfo = &AntispamUserInfo{}
			}
			if err := m.UserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EventType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientVersion", wireType)
			}
			m.ClientVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EventTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OsVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OsVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DVCommCheckReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DVCommCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DVCommCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserInfo == nil {
				m.UserInfo = &AntispamUserInfo{}
			}
			if err := m.UserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUserInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ToUserInfo == nil {
				m.ToUserInfo = &AntispamUserInfo{}
			}
			if err := m.ToUserInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EventType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EventTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EventTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DVCheckResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DVCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DVCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResponseType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResponseType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CustNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CustNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ApplyNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Score = float64(math4.Float64frombits(v))
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrorType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ErrorType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ErrorDetail", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAntispam
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ErrorDetail = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAntispam(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAntispam
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAntispam(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAntispam
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAntispam
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAntispam
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAntispam
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAntispam(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAntispam = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAntispam   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/antispam/antispam.proto", fileDescriptorAntispam) }

var fileDescriptorAntispam = []byte{
	// 2381 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x59, 0x4d, 0x6f, 0x1c, 0x49,
	0xf9, 0x77, 0xf7, 0x8c, 0xe7, 0xe5, 0xf1, 0xcc, 0xb8, 0xb7, 0x92, 0xd8, 0x93, 0x49, 0xe2, 0x38,
	0x9d, 0xec, 0xff, 0x6f, 0x92, 0x5d, 0x07, 0x19, 0x24, 0x10, 0x8a, 0x40, 0x63, 0x3b, 0xbb, 0x1e,
	0xad, 0x63, 0x07, 0x7b, 0x6c, 0xde, 0x0e, 0xad, 0x72, 0x77, 0x65, 0x5c, 0xf2, 0xf4, 0x4b, 0xba,
	0x6a, 0xfc, 0x72, 0xe3, 0x88, 0x38, 0x2d, 0x1c, 0xf8, 0x04, 0xb9, 0x72, 0x42, 0x1c, 0x16, 0x09,
	0x4e, 0x1c, 0xf6, 0xb8, 0x07, 0xce, 0x80, 0xb2, 0x1c, 0x22, 0x8e, 0x7c, 0x02, 0x54, 0x55, 0xdd,
	0xd3, 0xd5, 0x33, 0x6d, 0x67, 0xa5, 0xbd, 0x10, 0xc1, 0xc9, 0xd3, 0xbf, 0xa7, 0xaa, 0x9e, 0x7a,
	0x9e, 0x7a, 0xde, 0x0d, 0xb7, 0x58, 0xec, 0x3e, 0xc6, 0x01, 0xa7, 0x2c, 0xc2, 0xfe, 0xf8, 0xc7,
	0x6a, 0x14, 0x87, 0x3c, 0x44, 0xb5, 0x6e, 0xf2, 0xdd, 0x79, 0xe0, 0x86, 0xbe, 0x1f, 0x06, 0x8f,
	0xf9, 0xf0, 0x34, 0xa2, 0xee, 0xc9, 0x90, 0x3c, 0x66, 0x27, 0x47, 0x23, 0x3a, 0xe4, 0x34, 0xe0,
	0x17, 0x11, 0x51, 0xeb, 0xed, 0x2f, 0xca, 0xd0, 0xe8, 0x93, 0x73, 0xbe, 0x71, 0x4c, 0xdc, 0x93,
	0x3d, 0xf2, 0x12, 0xb5, 0xa1, 0xcc, 0xc9, 0x39, 0x6f, 0x1b, 0xcb, 0xe6, 0x4a, 0x7d, 0xbd, 0xfc,
	0xf9, 0x5f, 0xef, 0xce, 0xec, 0x49, 0x04, 0x5d, 0x07, 0x93, 0x46, 0x6d, 0x73, 0xd9, 0x18, 0xe3,
	0x26, 0x8d, 0xd0, 0x12, 0x54, 0xb1, 0xeb, 0x86, 0xa3, 0x80, 0xb7, 0x4b, 0x1a, 0x29, 0x05, 0xd1,
	0x16, 0xcc, 0x79, 0xe4, 0x94, 0xba, 0xc4, 0x11, 0x5c, 0xdb, 0xe5, 0x65, 0x63, 0xa5, 0xb5, 0x76,
	0x6f, 0x35, 0xbd, 0xe6, 0xaa, 0xce, 0x7c, 0x75, 0x53, 0xae, 0xec, 0x5f, 0x44, 0x24, 0x39, 0x06,
	0xbc, 0x31, 0x82, 0xee, 0x41, 0x3d, 0x39, 0x89, 0x7a, 0xed, 0x59, 0x8d, 0x57, 0x4d, 0xc1, 0x3d,
	0x4f, 0x2e, 0xc1, 0x1c, 0x2b, 0x56, 0x95, 0x65, 0x63, 0xa5, 0x39, 0x5e, 0x82, 0x39, 0x96, 0xa7,
	0xdc, 0x81, 0xaa, 0x5c, 0x42, 0xbd, 0x76, 0x55, 0x3b, 0xa3, 0x22, 0xc0, 0x9e, 0x87, 0x56, 0xc1,
	0x62, 0xc7, 0x23, 0x9f, 0x50, 0x27, 0xe3, 0x55, 0xd3, 0xd6, 0xb5, 0x14, 0x75, 0x33, 0xe5, 0xb8,
	0x0c, 0xb5, 0x80, 0xba, 0x27, 0x01, 0xf6, 0x49, 0xbb, 0xae, 0xdf, 0x29, 0x45, 0x51, 0x07, 0x66,
	0xf1, 0x90, 0x62, 0xd6, 0x06, 0x8d, 0xac, 0x20, 0xf4, 0x01, 0xcc, 0xc7, 0x61, 0xe8, 0x3b, 0x1e,
	0x65, 0xd1, 0x10, 0x5f, 0x08, 0x66, 0x73, 0xda, 0xad, 0x9b, 0x82, 0xb8, 0xa9, 0x68, 0x3d, 0x0f,
	0xdd, 0x07, 0x60, 0x2e, 0x09, 0x12, 0x4d, 0x36, 0xb4, 0xe3, 0xea, 0x12, 0x97, 0xf2, 0xdd, 0x07,
	0xe0, 0xa1, 0xc3, 0xc3, 0x13, 0x12, 0x50, 0xaf, 0xdd, 0xd4, 0x17, 0xf1, 0xb0, 0xaf, 0x60, 0xfb,
	0x67, 0x00, 0x99, 0xaa, 0x51, 0x0d, 0xca, 0x41, 0x18, 0x10, 0x6b, 0x06, 0x55, 0xa1, 0x74, 0x46,
	0x8e, 0x2c, 0x43, 0xfe, 0xc0, 0x91, 0x65, 0xa2, 0x39, 0xa8, 0xe2, 0xc0, 0x8b, 0x43, 0xea, 0x59,
	0x25, 0x04, 0x50, 0xa1, 0xd1, 0xb1, 0x58, 0x5a, 0x16, 0x9b, 0x68, 0x84, 0x3d, 0x6b, 0x16, 0x55,
	0xc0, 0x8c, 0x5c, 0xab, 0x22, 0xfe, 0x9e, 0x45, 0x56, 0xd5, 0xfe, 0xa7, 0x01, 0x4d, 0xed, 0x55,
	0x59, 0x84, 0x9e, 0x40, 0x25, 0x26, 0x6c, 0x34, 0x54, 0x56, 0xd5, 0x5a, 0x5b, 0x2a, 0x7c, 0x7e,
	0x16, 0xad, 0xee, 0x3d, 0xdd, 0x3f, 0xd8, 0xee, 0xa7, 0x4f, 0xa2, 0xf6, 0x08, 0x89, 0x86, 0xf8,
	0x88, 0x0c, 0x1d, 0x1a, 0xbc, 0x08, 0x73, 0xf6, 0x57, 0x97, 0x78, 0x2f, 0x78, 0x11, 0x22, 0x1b,
	0xea, 0x31, 0x79, 0x39, 0x22, 0x8c, 0xf7, 0xbc, 0x9c, 0x21, 0x66, 0xb0, 0xb0, 0x8e, 0x98, 0xb2,
	0x93, 0xcc, 0x10, 0xc7, 0xd6, 0x21, 0x60, 0xa1, 0x0a, 0xfb, 0x31, 0x54, 0xd4, 0x1d, 0x84, 0x7c,
	0xcf, 0xbb, 0xfb, 0xfb, 0xd6, 0x0c, 0x6a, 0x42, 0x7d, 0xff, 0x60, 0xff, 0x79, 0x6f, 0xa3, 0xb7,
	0xbb, 0x63, 0x19, 0xa8, 0x01, 0xb5, 0x9d, 0xdd, 0xbe, 0x23, 0x89, 0xa6, 0xfd, 0x27, 0x03, 0xac,
	0x3d, 0x32, 0xa0, 0x8c, 0x93, 0x78, 0xec, 0x43, 0x1d, 0x98, 0x95, 0x0f, 0x90, 0x73, 0x22, 0x05,
	0xe9, 0xfe, 0x62, 0x16, 0xf9, 0x4b, 0x07, 0x66, 0x89, 0x8f, 0xe9, 0x30, 0x27, 0x84, 0x82, 0x04,
	0x4d, 0xaa, 0x5f, 0x5e, 0x7e, 0x4c, 0x93, 0x50, 0xe2, 0x9d, 0xb3, 0x13, 0xde, 0xa9, 0x9b, 0x67,
	0xa5, 0xc8, 0x3c, 0x85, 0x00, 0xef, 0x4d, 0x08, 0xc0, 0x22, 0xf4, 0x83, 0x89, 0x17, 0xd3, 0x1c,
	0xf6, 0x80, 0x91, 0x78, 0x9d, 0x1c, 0xe3, 0x53, 0x9a, 0x6d, 0x18, 0x0d, 0xf9, 0xc4, 0xa3, 0xf5,
	0xa0, 0x76, 0x4c, 0xb9, 0x52, 0xb5, 0x29, 0x8f, 0x58, 0x79, 0xeb, 0x11, 0x5b, 0x94, 0x6b, 0xae,
	0x5f, 0x3d, 0x56, 0x9f, 0xc2, 0x63, 0x39, 0x66, 0x27, 0xc2, 0x39, 0x4a, 0x9a, 0x3e, 0x2b, 0x02,
	0xec, 0x79, 0xc2, 0xdc, 0x1a, 0xdd, 0x11, 0x3f, 0xfe, 0x0f, 0xd4, 0xfe, 0x37, 0xa0, 0x19, 0x27,
	0xaa, 0x75, 0x38, 0xf5, 0xf3, 0x21, 0xa9, 0x91, 0x92, 0xfa, 0xd4, 0x27, 0xe8, 0x7d, 0x98, 0x1b,
	0x2f, 0xa5, 0x51, 0x2e, 0x34, 0x41, 0x4a, 0xe8, 0x45, 0xf6, 0x1f, 0x0c, 0x68, 0x6a, 0xc2, 0xbe,
	0x5b, 0x2f, 0xf5, 0x99, 0x09, 0x0b, 0x1b, 0x32, 0x29, 0x75, 0x5d, 0x4e, 0x4f, 0x29, 0xbf, 0xf8,
	0x6f, 0x78, 0x33, 0xb1, 0x0c, 0x27, 0xf2, 0x4e, 0x66, 0x13, 0x48, 0x09, 0x3d, 0x0f, 0xdd, 0x86,
	0x0a, 0xc7, 0xf1, 0x80, 0xf0, 0x5c, 0x1e, 0x49, 0x30, 0xfb, 0xcf, 0x06, 0x2c, 0x16, 0xea, 0xee,
	0xdd, 0x32, 0x81, 0x11, 0xa0, 0x43, 0x12, 0xd3, 0x17, 0x17, 0x1b, 0xa1, 0x47, 0xc6, 0xaf, 0x7f,
	0x1f, 0xc0, 0xc5, 0x11, 0x77, 0x8f, 0x65, 0x5a, 0xd6, 0x4d, 0xa0, 0x9e, 0xe0, 0x2a, 0xd3, 0x9e,
	0xe2, 0x21, 0xf5, 0x30, 0x57, 0x97, 0x1c, 0x87, 0xb2, 0x14, 0x15, 0xa5, 0xcb, 0x88, 0x91, 0x38,
	0x67, 0x07, 0x12, 0xb1, 0xbf, 0x0d, 0xd7, 0xa6, 0xd8, 0xb2, 0x48, 0x5c, 0x96, 0x32, 0x27, 0xc2,
	0x8c, 0x49, 0xa6, 0xb5, 0xf4, 0xb2, 0x94, 0x3d, 0xc7, 0x8c, 0xd9, 0x18, 0xe6, 0x3e, 0x8e, 0xd8,
	0x76, 0xe8, 0x62, 0x4e, 0xc3, 0x40, 0x58, 0xc6, 0x20, 0x62, 0xce, 0x30, 0x0c, 0x06, 0x94, 0x8f,
	0x3c, 0x22, 0xf7, 0x18, 0xa9, 0x65, 0x0c, 0xc4, 0xca, 0x84, 0x82, 0xfe, 0x1f, 0x1a, 0x72, 0x29,
	0xe6, 0x6a, 0xa5, 0xa9, 0xad, 0x9c, 0x13, 0x2b, 0x13, 0x82, 0xfd, 0xdb, 0x32, 0x2c, 0xec, 0xcb,
	0x8a, 0xa2, 0x28, 0x89, 0xe0, 0x28, 0xea, 0x09, 0x7d, 0xe8, 0x75, 0x83, 0x80, 0xf2, 0x2e, 0x61,
	0x4e, 0xbb, 0xc4, 0x3d, 0xa8, 0xbb, 0x43, 0x4a, 0x02, 0x2e, 0xec, 0x52, 0x7f, 0x87, 0x9a, 0x82,
	0x7b, 0x91, 0x48, 0x98, 0xc2, 0xbc, 0x19, 0xc7, 0x7e, 0xd4, 0x2e, 0x2f, 0x9b, 0x2b, 0xa5, 0x71,
	0x99, 0x90, 0xc2, 0xe8, 0x43, 0x98, 0xcf, 0x15, 0x43, 0x13, 0x75, 0x57, 0xae, 0x16, 0xa2, 0x5e,
	0xe6, 0x6c, 0x95, 0x69, 0x67, 0xfb, 0x00, 0xcc, 0x90, 0x49, 0x17, 0x69, 0xad, 0x2d, 0x64, 0xc6,
	0xa5, 0x64, 0xdf, 0xdd, 0xd7, 0x4c, 0xc9, 0x0c, 0x99, 0x74, 0x99, 0x28, 0x72, 0x4e, 0x49, 0xcc,
	0x68, 0x18, 0x4c, 0xb8, 0x4c, 0x14, 0x1d, 0x2a, 0xfc, 0x2b, 0x14, 0x5f, 0xdf, 0x4d, 0x1e, 0x22,
	0x79, 0x43, 0x59, 0x83, 0xcd, 0xad, 0xdd, 0xc8, 0x2e, 0xa0, 0x3d, 0xb0, 0x7a, 0x99, 0xf4, 0xb5,
	0x8f, 0x60, 0x9e, 0xd1, 0x41, 0x30, 0x8a, 0x9c, 0x68, 0x88, 0xf9, 0x8b, 0x30, 0xf6, 0x65, 0x69,
	0xd6, 0x5a, 0xfb, 0xd6, 0xe4, 0xed, 0x27, 0x5f, 0x6e, 0x75, 0x5f, 0xee, 0x7b, 0x9e, 0x6c, 0xd3,
	0x44, 0x6b, 0xb1, 0x1c, 0xc5, 0xfe, 0x26, 0xa0, 0xe9, 0xb5, 0xa2, 0x8e, 0xe2, 0xdc, 0x9a, 0x11,
	0x7f, 0x5f, 0xbe, 0xb4, 0x0c, 0x51, 0x75, 0x9d, 0x11, 0x7a, 0x4e, 0x03, 0xcb, 0xb4, 0x3f, 0x33,
	0x60, 0xb1, 0x90, 0x2b, 0x8b, 0x84, 0x17, 0x25, 0xb5, 0x8e, 0xf2, 0xa2, 0xc2, 0x1a, 0xa8, 0x0d,
	0x65, 0x37, 0x94, 0x16, 0x69, 0xac, 0xcc, 0xa6, 0x3e, 0x22, 0x10, 0x61, 0x53, 0x3e, 0x61, 0x0c,
	0x0f, 0x48, 0xbe, 0x90, 0x4f, 0x40, 0x79, 0xbc, 0xa8, 0x9e, 0x86, 0xe4, 0x94, 0x0c, 0x73, 0xf1,
	0x54, 0x56, 0x55, 0xdb, 0x02, 0x16, 0x26, 0xc0, 0xdc, 0x30, 0x26, 0xd2, 0x4e, 0xd2, 0xf3, 0x15,
	0x64, 0xff, 0xc3, 0x04, 0xa4, 0xee, 0x3e, 0x99, 0xae, 0xff, 0x67, 0xe7, 0x57, 0xdb, 0xf9, 0x23,
	0x68, 0x89, 0x20, 0x75, 0xe6, 0x39, 0x3e, 0xe6, 0xee, 0x31, 0xf1, 0xa4, 0xb5, 0xa7, 0xea, 0x6d,
	0x2a, 0xda, 0x33, 0x45, 0xb2, 0x7f, 0x6f, 0xc0, 0xb5, 0x29, 0x35, 0xbf, 0x03, 0xe6, 0xf1, 0xc7,
	0xb1, 0x79, 0xe4, 0xfa, 0xd1, 0xa2, 0x86, 0xcc, 0xb8, 0xa2, 0x21, 0x5b, 0x80, 0xd2, 0x88, 0x7a,
	0x52, 0x80, 0x34, 0x6b, 0x0b, 0x40, 0xc4, 0x0a, 0x91, 0x0a, 0x64, 0xac, 0xd0, 0x05, 0x18, 0xa3,
	0x59, 0xa3, 0x56, 0xd6, 0x6b, 0x10, 0xd5, 0xa8, 0xa5, 0x5d, 0xf1, 0xec, 0x25, 0x5d, 0x71, 0xe5,
	0x8a, 0xba, 0xbb, 0x5a, 0x18, 0x99, 0x72, 0xad, 0x6a, 0x4d, 0x53, 0x47, 0xd6, 0xaa, 0x16, 0x74,
	0x87, 0xf5, 0x4b, 0xbb, 0x43, 0xfb, 0x22, 0x7d, 0xf6, 0x7c, 0xef, 0x35, 0x56, 0xb9, 0x31, 0xa5,
	0x72, 0xd9, 0x34, 0xa5, 0x6f, 0x93, 0x6f, 0xac, 0xb2, 0x27, 0xfb, 0x0a, 0x8d, 0x95, 0xfd, 0x3b,
	0x03, 0xac, 0xd4, 0xd4, 0x45, 0xb9, 0x20, 0x3b, 0xb2, 0xe4, 0x21, 0x8c, 0xc9, 0x87, 0x58, 0x82,
	0x6a, 0xda, 0x9d, 0xe6, 0xca, 0xb9, 0x04, 0xcc, 0xa9, 0xae, 0x54, 0xa8, 0xba, 0xdb, 0x50, 0x19,
	0x90, 0xc0, 0x23, 0x71, 0xae, 0x89, 0x4b, 0x30, 0x91, 0x7b, 0xa5, 0x2b, 0x3a, 0xc1, 0xc8, 0x3f,
	0x22, 0x71, 0xce, 0x93, 0xe7, 0x24, 0x65, 0x47, 0x12, 0xec, 0xbf, 0x54, 0x01, 0x6d, 0x1e, 0x4e,
	0xe5, 0xdd, 0xef, 0x40, 0x5d, 0x98, 0x84, 0xea, 0x36, 0x0d, 0x99, 0x2f, 0x3a, 0x99, 0x23, 0x4f,
	0x8a, 0xa9, 0xec, 0x47, 0x0a, 0x5c, 0x3c, 0x1f, 0x79, 0x7f, 0x3c, 0xff, 0x90, 0x07, 0xea, 0x12,
	0x25, 0xc3, 0x0d, 0xb9, 0xf9, 0x16, 0x54, 0x42, 0x26, 0x02, 0x41, 0xbe, 0x52, 0x0d, 0xd9, 0x21,
	0x89, 0x45, 0x9d, 0x12, 0x32, 0x65, 0x29, 0xba, 0x34, 0x95, 0x90, 0x49, 0x3b, 0x79, 0x04, 0xad,
	0x24, 0x0a, 0xa6, 0x81, 0x44, 0xaf, 0x59, 0x9b, 0x8a, 0x96, 0xc6, 0x92, 0xdc, 0x14, 0xa5, 0x5a,
	0x38, 0x45, 0x79, 0x04, 0xad, 0x30, 0xa6, 0x03, 0x1a, 0x38, 0xee, 0x31, 0x0e, 0x02, 0x32, 0xcc,
	0x05, 0xa6, 0xa6, 0xa2, 0x6d, 0x28, 0x92, 0x88, 0x9d, 0xee, 0x28, 0x8e, 0x05, 0xf7, 0x74, 0xb5,
	0x9e, 0x8a, 0x5b, 0x09, 0x31, 0x5d, 0x7e, 0x17, 0x6a, 0x31, 0x19, 0x28, 0x59, 0xf4, 0x81, 0x48,
	0x35, 0x26, 0x03, 0x29, 0x4c, 0x1b, 0xca, 0xd4, 0x27, 0x54, 0x26, 0xdb, 0xb1, 0xa7, 0x09, 0x04,
	0xfd, 0x1f, 0xcc, 0x09, 0x5d, 0x63, 0xcf, 0x93, 0x75, 0xb9, 0x3e, 0xff, 0xd0, 0x09, 0xc2, 0xf0,
	0x02, 0xc2, 0x73, 0xa3, 0x0f, 0x01, 0x08, 0xc3, 0x1a, 0xe2, 0x60, 0x30, 0x12, 0x21, 0xac, 0xa5,
	0x0b, 0x9e, 0xa2, 0x82, 0x37, 0x63, 0xd4, 0x6b, 0xcf, 0xeb, 0xbc, 0x05, 0x22, 0x8c, 0xea, 0x45,
	0x4c, 0x88, 0xc3, 0x78, 0x18, 0x8b, 0xfd, 0x96, 0xce, 0x5c, 0x50, 0xf6, 0x15, 0x01, 0x3d, 0x10,
	0x51, 0x96, 0x85, 0xc3, 0x91, 0x2c, 0x37, 0xde, 0xcb, 0xb7, 0x04, 0x29, 0x2e, 0x4a, 0x49, 0x76,
	0xc1, 0x38, 0xf1, 0x9d, 0xd3, 0x70, 0x38, 0xf2, 0x49, 0x1b, 0x69, 0x0b, 0x1b, 0x8a, 0x74, 0x28,
	0x29, 0x62, 0x29, 0x0f, 0x39, 0x1e, 0x8e, 0x59, 0x5f, 0xd3, 0x97, 0x4a, 0x52, 0xca, 0x7b, 0x01,
	0x4a, 0x3e, 0x76, 0xdb, 0xd7, 0x75, 0xc1, 0x7d, 0xec, 0x8a, 0x23, 0x8e, 0x30, 0xe7, 0x24, 0xbe,
	0x70, 0x18, 0x17, 0xe5, 0xf3, 0x0d, 0xfd, 0x88, 0x84, 0xb4, 0x2f, 0x28, 0xc2, 0xb5, 0x7c, 0xe2,
	0x87, 0xf1, 0x45, 0x7b, 0x41, 0x37, 0x34, 0x85, 0x09, 0xc3, 0x48, 0x6c, 0xc7, 0xc5, 0x71, 0x4c,
	0x49, 0xdc, 0x5e, 0xd4, 0x0d, 0x43, 0xd1, 0x36, 0x14, 0x49, 0xe7, 0xaa, 0x72, 0x42, 0xbb, 0x80,
	0xab, 0x8a, 0x31, 0x99, 0x8f, 0x48, 0xaf, 0xbf, 0x39, 0xed, 0x23, 0x3b, 0xd8, 0x27, 0xf6, 0x03,
	0xa8, 0xee, 0x25, 0x56, 0x52, 0x4f, 0x52, 0xb0, 0x2a, 0x93, 0xce, 0xce, 0x2d, 0x23, 0x29, 0x97,
	0x4c, 0xfb, 0x37, 0x26, 0xcc, 0x6f, 0x1e, 0x6e, 0x87, 0xd2, 0x46, 0xbf, 0xae, 0x4f, 0xdf, 0x07,
	0x20, 0xa7, 0xc2, 0xb6, 0x93, 0xde, 0x48, 0x0b, 0x7f, 0x12, 0x97, 0x97, 0x51, 0x8e, 0x5f, 0x9a,
	0x70, 0xfc, 0x87, 0x90, 0xf7, 0xbc, 0x5c, 0xb0, 0x9a, 0x70, 0xca, 0x8c, 0x8d, 0xb0, 0xec, 0xd9,
	0x69, 0x36, 0xd4, 0x9f, 0x98, 0x7f, 0x56, 0x0a, 0x3d, 0x37, 0x8b, 0x22, 0xd5, 0xa9, 0x28, 0x62,
	0xff, 0xcb, 0x80, 0xd6, 0xe6, 0xa1, 0x68, 0x22, 0xbf, 0xbe, 0x5e, 0x9e, 0x40, 0x83, 0x87, 0x4e,
	0xb6, 0xd7, 0x7c, 0xeb, 0x5e, 0xe0, 0xe1, 0x41, 0xb1, 0x56, 0x4b, 0xc5, 0x5a, 0xcd, 0xeb, 0xa4,
	0x5c, 0xac, 0x93, 0xbb, 0x50, 0x23, 0xe7, 0x5c, 0xdd, 0x41, 0x57, 0x5b, 0x95, 0x9c, 0x73, 0xc1,
	0xca, 0xfe, 0x9b, 0x01, 0x73, 0x9b, 0x87, 0x59, 0x3a, 0x5c, 0x81, 0x46, 0x4c, 0x58, 0x14, 0x06,
	0x4c, 0xce, 0x3e, 0x73, 0xa5, 0x44, 0x8e, 0x22, 0x82, 0xae, 0x3b, 0x62, 0xdc, 0x09, 0xf2, 0x33,
	0xc7, 0x8a, 0x00, 0x77, 0x42, 0xc1, 0x19, 0x47, 0xd1, 0xf0, 0x42, 0xd0, 0xf3, 0x83, 0x6f, 0x81,
	0xee, 0x84, 0x59, 0xe2, 0x15, 0x57, 0x37, 0xa6, 0x12, 0x2f, 0x89, 0xe3, 0x30, 0xee, 0x4f, 0x86,
	0xf4, 0x0c, 0x16, 0xe1, 0x4e, 0x7e, 0x6c, 0x12, 0x8e, 0xe9, 0x30, 0xf7, 0xe0, 0x3a, 0xe1, 0xe1,
	0x13, 0xa8, 0xed, 0x25, 0xe3, 0x4b, 0x84, 0xa0, 0xa5, 0xc6, 0x97, 0xce, 0xe6, 0xd3, 0x8f, 0xba,
	0x07, 0xdb, 0x7d, 0x6b, 0x06, 0xdd, 0x86, 0x76, 0x82, 0x6d, 0xf5, 0x3e, 0xde, 0x72, 0xf6, 0x7a,
	0xfb, 0x9f, 0x38, 0xdd, 0x8d, 0x8d, 0xdd, 0x83, 0x9d, 0xbe, 0x65, 0x3c, 0x7c, 0x0e, 0x8b, 0x97,
	0x34, 0xf8, 0xa8, 0x09, 0xf5, 0x64, 0xe3, 0xee, 0x27, 0xd6, 0x0c, 0xba, 0x0e, 0x56, 0xf2, 0x99,
	0x4d, 0x43, 0x01, 0x59, 0xd0, 0x48, 0xd0, 0x8f, 0xba, 0xfd, 0xee, 0xb6, 0x75, 0xfd, 0xe1, 0xaf,
	0x0c, 0x58, 0xba, 0x7a, 0x66, 0x20, 0x3a, 0x9a, 0xad, 0x5e, 0x72, 0xec, 0x0d, 0x78, 0x4f, 0xfc,
	0xde, 0xec, 0xf6, 0xbb, 0x4e, 0x77, 0x7d, 0x67, 0x77, 0xef, 0x59, 0x77, 0xdb, 0x32, 0xd0, 0x4d,
	0xb8, 0x21, 0xe0, 0xf5, 0xa7, 0x5b, 0xdd, 0xc3, 0xde, 0xee, 0x5e, 0x46, 0x32, 0xd1, 0x2d, 0x58,
	0x14, 0xa4, 0xa7, 0x3b, 0x87, 0xbd, 0xbd, 0xdd, 0x67, 0x4f, 0x77, 0xfa, 0x19, 0xb1, 0x24, 0x6e,
	0x29, 0xf7, 0xf5, 0x7e, 0x9a, 0xa1, 0xe5, 0x87, 0xeb, 0xd0, 0xd0, 0x2b, 0x70, 0x6d, 0xe2, 0xad,
	0xcd, 0xb7, 0xe5, 0xd4, 0x9b, 0x86, 0xcc, 0x32, 0x45, 0x78, 0x39, 0x23, 0x38, 0x8a, 0xac, 0x52,
	0x3a, 0x12, 0x2f, 0xaf, 0x7d, 0x5a, 0x83, 0xf1, 0x3f, 0x57, 0xd0, 0x8f, 0xa1, 0x3e, 0x2e, 0xb3,
	0xd0, 0x42, 0xf1, 0x7f, 0x33, 0x3a, 0x8b, 0x97, 0x8c, 0xb9, 0xed, 0xce, 0xcf, 0x5f, 0xbd, 0x29,
	0x19, 0xbf, 0x7c, 0xf5, 0xa6, 0x64, 0xf2, 0xef, 0xfd, 0xfa, 0xd5, 0x9b, 0x52, 0xfd, 0x43, 0xbe,
	0xfc, 0x44, 0x94, 0x93, 0xdf, 0x47, 0x3f, 0x81, 0x66, 0xae, 0x24, 0x41, 0x9a, 0x53, 0x4d, 0xd6,
	0x2a, 0x9d, 0x5b, 0x97, 0xd2, 0x58, 0x64, 0xcf, 0x0b, 0x2e, 0xa6, 0xe0, 0x32, 0x23, 0x78, 0xcc,
	0xa0, 0x67, 0x50, 0x1f, 0xb7, 0x04, 0xfa, 0xa5, 0xf5, 0x76, 0x4c, 0xbf, 0x74, 0xae, 0x7f, 0x50,
	0xc7, 0x95, 0xb4, 0xe3, 0x4e, 0xe0, 0x5a, 0xc1, 0x44, 0x0a, 0x2d, 0x67, 0x07, 0x14, 0x0f, 0xfb,
	0x3a, 0xf7, 0xde, 0xb2, 0x22, 0x65, 0x56, 0xd6, 0x98, 0x39, 0x30, 0x3f, 0x31, 0xc1, 0x41, 0xb7,
	0xb3, 0x63, 0xa6, 0x67, 0x4a, 0x9d, 0x3b, 0x57, 0x50, 0x53, 0x06, 0xb3, 0x79, 0x69, 0x0a, 0x1a,
	0x6b, 0x5d, 0x9a, 0xe2, 0x6e, 0x5f, 0x97, 0xe6, 0x92, 0xce, 0x5c, 0x31, 0xab, 0xe4, 0xa5, 0x99,
	0x68, 0xd1, 0x74, 0x69, 0xa6, 0x9b, 0x64, 0x5d, 0x9a, 0x82, 0xde, 0x4e, 0x31, 0xa8, 0x16, 0x31,
	0xc8, 0xac, 0x74, 0x8a, 0x41, 0xce, 0x56, 0xef, 0x5c, 0x41, 0x4d, 0x19, 0xd4, 0x34, 0x06, 0x3f,
	0x12, 0x49, 0x36, 0xaf, 0x2a, 0x8d, 0xc1, 0x74, 0x59, 0xdd, 0xb9, 0xa1, 0x53, 0x27, 0x0e, 0xae,
	0x6b, 0x07, 0xff, 0x10, 0x1a, 0x7a, 0xf6, 0x46, 0x37, 0xf5, 0x7d, 0xb9, 0xac, 0x7e, 0xe5, 0x91,
	0xa0, 0x1d, 0xb9, 0x2b, 0x53, 0x40, 0x9a, 0xf7, 0x50, 0x3b, 0xb7, 0x4d, 0x4b, 0x87, 0x57, 0x1e,
	0x38, 0x97, 0x1d, 0xd8, 0xa9, 0xfc, 0xe2, 0xd5, 0x9b, 0xd2, 0x97, 0xa3, 0x75, 0xeb, 0xf3, 0xd7,
	0x4b, 0xc6, 0x17, 0xaf, 0x97, 0x8c, 0xbf, 0xbf, 0x5e, 0x32, 0x3e, 0xfd, 0x72, 0x69, 0xe6, 0xdf,
	0x01, 0x00, 0x00, 0xff, 0xff, 0x72, 0x75, 0xad, 0x98, 0x9c, 0x1d, 0x00, 0x00,
}
