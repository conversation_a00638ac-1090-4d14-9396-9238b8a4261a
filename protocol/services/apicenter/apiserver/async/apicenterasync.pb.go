// Code generated by protoc-gen-gogo.
// source: services/apicenter/apiserver/async/apicenterasync.proto
// DO NOT EDIT!

/*
Package apicenter is a generated protocol buffer package.

It is generated from these files:
	services/apicenter/apiserver/async/apicenterasync.proto

It has these top-level messages:
	ApicenterAsyncTask
	AsyncNotifySyncGrowData
*/
package apicenter

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"

import "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ApicenterAsyncTask_Type int32

const (
	ApicenterAsyncTask_INCRESS_MISSION_FINISH_COUNT ApicenterAsyncTask_Type = 1
	ApicenterAsyncTask_SEND_IM_MSG                  ApicenterAsyncTask_Type = 2
	ApicenterAsyncTask_NTFY_SYNC_GROW               ApicenterAsyncTask_Type = 3
)

var ApicenterAsyncTask_Type_name = map[int32]string{
	1: "INCRESS_MISSION_FINISH_COUNT",
	2: "SEND_IM_MSG",
	3: "NTFY_SYNC_GROW",
}
var ApicenterAsyncTask_Type_value = map[string]int32{
	"INCRESS_MISSION_FINISH_COUNT": 1,
	"SEND_IM_MSG":                  2,
	"NTFY_SYNC_GROW":               3,
}

func (x ApicenterAsyncTask_Type) Enum() *ApicenterAsyncTask_Type {
	p := new(ApicenterAsyncTask_Type)
	*p = x
	return p
}
func (x ApicenterAsyncTask_Type) String() string {
	return proto.EnumName(ApicenterAsyncTask_Type_name, int32(x))
}
func (x *ApicenterAsyncTask_Type) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ApicenterAsyncTask_Type_value, data, "ApicenterAsyncTask_Type")
	if err != nil {
		return err
	}
	*x = ApicenterAsyncTask_Type(value)
	return nil
}
func (ApicenterAsyncTask_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorApicenterasync, []int{0, 0}
}

type ApicenterAsyncTask struct {
}

func (m *ApicenterAsyncTask) Reset()                    { *m = ApicenterAsyncTask{} }
func (m *ApicenterAsyncTask) String() string            { return proto.CompactTextString(m) }
func (*ApicenterAsyncTask) ProtoMessage()               {}
func (*ApicenterAsyncTask) Descriptor() ([]byte, []int) { return fileDescriptorApicenterasync, []int{0} }

type AsyncNotifySyncGrowData struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *AsyncNotifySyncGrowData) Reset()         { *m = AsyncNotifySyncGrowData{} }
func (m *AsyncNotifySyncGrowData) String() string { return proto.CompactTextString(m) }
func (*AsyncNotifySyncGrowData) ProtoMessage()    {}
func (*AsyncNotifySyncGrowData) Descriptor() ([]byte, []int) {
	return fileDescriptorApicenterasync, []int{1}
}

func (m *AsyncNotifySyncGrowData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func init() {
	proto.RegisterType((*ApicenterAsyncTask)(nil), "apicenter.ApicenterAsyncTask")
	proto.RegisterType((*AsyncNotifySyncGrowData)(nil), "apicenter.AsyncNotifySyncGrowData")
	proto.RegisterEnum("apicenter.ApicenterAsyncTask_Type", ApicenterAsyncTask_Type_name, ApicenterAsyncTask_Type_value)
}
func (m *ApicenterAsyncTask) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApicenterAsyncTask) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AsyncNotifySyncGrowData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AsyncNotifySyncGrowData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintApicenterasync(dAtA, i, uint64(m.Uid))
	return i, nil
}

func encodeFixed64Apicenterasync(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Apicenterasync(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintApicenterasync(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ApicenterAsyncTask) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AsyncNotifySyncGrowData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovApicenterasync(uint64(m.Uid))
	return n
}

func sovApicenterasync(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozApicenterasync(x uint64) (n int) {
	return sovApicenterasync(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ApicenterAsyncTask) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowApicenterasync
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ApicenterAsyncTask: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ApicenterAsyncTask: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipApicenterasync(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthApicenterasync
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AsyncNotifySyncGrowData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowApicenterasync
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: AsyncNotifySyncGrowData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: AsyncNotifySyncGrowData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApicenterasync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipApicenterasync(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthApicenterasync
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipApicenterasync(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowApicenterasync
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowApicenterasync
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowApicenterasync
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthApicenterasync
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowApicenterasync
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipApicenterasync(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthApicenterasync = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowApicenterasync   = fmt1.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/apicenter/apiserver/async/apicenterasync.proto", fileDescriptorApicenterasync)
}

var fileDescriptorApicenterasync = []byte{
	// 225 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x32, 0x2f, 0x4e, 0x2d, 0x2a,
	0xcb, 0x4c, 0x4e, 0x2d, 0xd6, 0x4f, 0x2c, 0xc8, 0x4c, 0x4e, 0xcd, 0x2b, 0x49, 0x2d, 0x02, 0xb1,
	0x40, 0xa2, 0x20, 0x56, 0x71, 0x65, 0x5e, 0x32, 0x42, 0x06, 0xcc, 0xd5, 0x2b, 0x28, 0xca, 0x2f,
	0xc9, 0x17, 0xe2, 0x84, 0x8b, 0x2a, 0x25, 0x73, 0x09, 0x39, 0xc2, 0x38, 0x8e, 0x20, 0x25, 0x21,
	0x89, 0xc5, 0xd9, 0x4a, 0xbe, 0x5c, 0x2c, 0x21, 0x95, 0x05, 0xa9, 0x42, 0x0a, 0x5c, 0x32, 0x9e,
	0x7e, 0xce, 0x41, 0xae, 0xc1, 0xc1, 0xf1, 0xbe, 0x9e, 0xc1, 0xc1, 0x9e, 0xfe, 0x7e, 0xf1, 0x6e,
	0x9e, 0x7e, 0x9e, 0xc1, 0x1e, 0xf1, 0xce, 0xfe, 0xa1, 0x7e, 0x21, 0x02, 0x8c, 0x42, 0xfc, 0x5c,
	0xdc, 0xc1, 0xae, 0x7e, 0x2e, 0xf1, 0x9e, 0xbe, 0xf1, 0xbe, 0xc1, 0xee, 0x02, 0x4c, 0x42, 0x42,
	0x5c, 0x7c, 0x7e, 0x21, 0x6e, 0x91, 0xf1, 0xc1, 0x91, 0x7e, 0xce, 0xf1, 0xee, 0x41, 0xfe, 0xe1,
	0x02, 0xcc, 0x4a, 0x86, 0x5c, 0xe2, 0x60, 0xb3, 0xfd, 0xf2, 0x4b, 0x32, 0xd3, 0x2a, 0x83, 0x2b,
	0xf3, 0x92, 0xdd, 0x8b, 0xf2, 0xcb, 0x5d, 0x12, 0x4b, 0x12, 0x85, 0xc4, 0xb8, 0x98, 0x4b, 0x33,
	0x53, 0x24, 0x18, 0x15, 0x98, 0x34, 0x78, 0x9d, 0x58, 0x4e, 0xdc, 0x93, 0x67, 0x08, 0x02, 0x09,
	0x38, 0x09, 0x9c, 0x78, 0x24, 0xc7, 0x78, 0xe1, 0x91, 0x1c, 0xe3, 0x83, 0x47, 0x72, 0x8c, 0x13,
	0x1e, 0xcb, 0x31, 0x00, 0x02, 0x00, 0x00, 0xff, 0xff, 0x2f, 0x15, 0x68, 0xe8, 0xee, 0x00, 0x00,
	0x00,
}
