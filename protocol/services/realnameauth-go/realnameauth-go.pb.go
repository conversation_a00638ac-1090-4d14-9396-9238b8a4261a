// Code generated by protoc-gen-go. DO NOT EDIT.
// source: realnameauth-go/realnameauth-go.proto

package realnameauth_go // import "golang.52tt.com/protocol/services/realnameauth-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 人脸验证上下文
type FaceAuthContext struct {
	BizScene             string   `protobuf:"bytes,1,opt,name=biz_scene,json=bizScene,proto3" json:"biz_scene,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FaceAuthRequireText  string   `protobuf:"bytes,3,opt,name=face_auth_require_text,json=faceAuthRequireText,proto3" json:"face_auth_require_text,omitempty"`
	FaceAuthSuccessText  string   `protobuf:"bytes,4,opt,name=face_auth_success_text,json=faceAuthSuccessText,proto3" json:"face_auth_success_text,omitempty"`
	Uid                  uint64   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	ProcessingText       string   `protobuf:"bytes,6,opt,name=processing_text,json=processingText,proto3" json:"processing_text,omitempty"`
	FailureText          string   `protobuf:"bytes,7,opt,name=failure_text,json=failureText,proto3" json:"failure_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FaceAuthContext) Reset()         { *m = FaceAuthContext{} }
func (m *FaceAuthContext) String() string { return proto.CompactTextString(m) }
func (*FaceAuthContext) ProtoMessage()    {}
func (*FaceAuthContext) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{0}
}
func (m *FaceAuthContext) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FaceAuthContext.Unmarshal(m, b)
}
func (m *FaceAuthContext) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FaceAuthContext.Marshal(b, m, deterministic)
}
func (dst *FaceAuthContext) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FaceAuthContext.Merge(dst, src)
}
func (m *FaceAuthContext) XXX_Size() int {
	return xxx_messageInfo_FaceAuthContext.Size(m)
}
func (m *FaceAuthContext) XXX_DiscardUnknown() {
	xxx_messageInfo_FaceAuthContext.DiscardUnknown(m)
}

var xxx_messageInfo_FaceAuthContext proto.InternalMessageInfo

func (m *FaceAuthContext) GetBizScene() string {
	if m != nil {
		return m.BizScene
	}
	return ""
}

func (m *FaceAuthContext) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FaceAuthContext) GetFaceAuthRequireText() string {
	if m != nil {
		return m.FaceAuthRequireText
	}
	return ""
}

func (m *FaceAuthContext) GetFaceAuthSuccessText() string {
	if m != nil {
		return m.FaceAuthSuccessText
	}
	return ""
}

func (m *FaceAuthContext) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FaceAuthContext) GetProcessingText() string {
	if m != nil {
		return m.ProcessingText
	}
	return ""
}

func (m *FaceAuthContext) GetFailureText() string {
	if m != nil {
		return m.FailureText
	}
	return ""
}

type ReportFaceAuthResultReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsPass               bool             `protobuf:"varint,2,opt,name=is_pass,json=isPass,proto3" json:"is_pass,omitempty"`
	DeviceId             []byte           `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	MarketId             uint32           `protobuf:"varint,4,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	TerminalType         uint32           `protobuf:"varint,5,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	ResultRequestId      string           `protobuf:"bytes,6,opt,name=result_request_id,json=resultRequestId,proto3" json:"result_request_id,omitempty"`
	FaceAuthContext      *FaceAuthContext `protobuf:"bytes,7,opt,name=face_auth_context,json=faceAuthContext,proto3" json:"face_auth_context,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ReportFaceAuthResultReq) Reset()         { *m = ReportFaceAuthResultReq{} }
func (m *ReportFaceAuthResultReq) String() string { return proto.CompactTextString(m) }
func (*ReportFaceAuthResultReq) ProtoMessage()    {}
func (*ReportFaceAuthResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{1}
}
func (m *ReportFaceAuthResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportFaceAuthResultReq.Unmarshal(m, b)
}
func (m *ReportFaceAuthResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportFaceAuthResultReq.Marshal(b, m, deterministic)
}
func (dst *ReportFaceAuthResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportFaceAuthResultReq.Merge(dst, src)
}
func (m *ReportFaceAuthResultReq) XXX_Size() int {
	return xxx_messageInfo_ReportFaceAuthResultReq.Size(m)
}
func (m *ReportFaceAuthResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportFaceAuthResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportFaceAuthResultReq proto.InternalMessageInfo

func (m *ReportFaceAuthResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportFaceAuthResultReq) GetIsPass() bool {
	if m != nil {
		return m.IsPass
	}
	return false
}

func (m *ReportFaceAuthResultReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *ReportFaceAuthResultReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ReportFaceAuthResultReq) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *ReportFaceAuthResultReq) GetResultRequestId() string {
	if m != nil {
		return m.ResultRequestId
	}
	return ""
}

func (m *ReportFaceAuthResultReq) GetFaceAuthContext() *FaceAuthContext {
	if m != nil {
		return m.FaceAuthContext
	}
	return nil
}

type ReportFaceAuthResultResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportFaceAuthResultResp) Reset()         { *m = ReportFaceAuthResultResp{} }
func (m *ReportFaceAuthResultResp) String() string { return proto.CompactTextString(m) }
func (*ReportFaceAuthResultResp) ProtoMessage()    {}
func (*ReportFaceAuthResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{2}
}
func (m *ReportFaceAuthResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportFaceAuthResultResp.Unmarshal(m, b)
}
func (m *ReportFaceAuthResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportFaceAuthResultResp.Marshal(b, m, deterministic)
}
func (dst *ReportFaceAuthResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportFaceAuthResultResp.Merge(dst, src)
}
func (m *ReportFaceAuthResultResp) XXX_Size() int {
	return xxx_messageInfo_ReportFaceAuthResultResp.Size(m)
}
func (m *ReportFaceAuthResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportFaceAuthResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportFaceAuthResultResp proto.InternalMessageInfo

// 上报人脸验证结果
type FaceCheckResultReportReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TriggerScene         uint32   `protobuf:"varint,2,opt,name=trigger_scene,json=triggerScene,proto3" json:"trigger_scene,omitempty"`
	RequestId            []byte   `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	IsPass               bool     `protobuf:"varint,4,opt,name=is_pass,json=isPass,proto3" json:"is_pass,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FaceCheckResultReportReq) Reset()         { *m = FaceCheckResultReportReq{} }
func (m *FaceCheckResultReportReq) String() string { return proto.CompactTextString(m) }
func (*FaceCheckResultReportReq) ProtoMessage()    {}
func (*FaceCheckResultReportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{3}
}
func (m *FaceCheckResultReportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FaceCheckResultReportReq.Unmarshal(m, b)
}
func (m *FaceCheckResultReportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FaceCheckResultReportReq.Marshal(b, m, deterministic)
}
func (dst *FaceCheckResultReportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FaceCheckResultReportReq.Merge(dst, src)
}
func (m *FaceCheckResultReportReq) XXX_Size() int {
	return xxx_messageInfo_FaceCheckResultReportReq.Size(m)
}
func (m *FaceCheckResultReportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FaceCheckResultReportReq.DiscardUnknown(m)
}

var xxx_messageInfo_FaceCheckResultReportReq proto.InternalMessageInfo

func (m *FaceCheckResultReportReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FaceCheckResultReportReq) GetTriggerScene() uint32 {
	if m != nil {
		return m.TriggerScene
	}
	return 0
}

func (m *FaceCheckResultReportReq) GetRequestId() []byte {
	if m != nil {
		return m.RequestId
	}
	return nil
}

func (m *FaceCheckResultReportReq) GetIsPass() bool {
	if m != nil {
		return m.IsPass
	}
	return false
}

func (m *FaceCheckResultReportReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

type FaceCheckResultReportResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FaceCheckResultReportResp) Reset()         { *m = FaceCheckResultReportResp{} }
func (m *FaceCheckResultReportResp) String() string { return proto.CompactTextString(m) }
func (*FaceCheckResultReportResp) ProtoMessage()    {}
func (*FaceCheckResultReportResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{4}
}
func (m *FaceCheckResultReportResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FaceCheckResultReportResp.Unmarshal(m, b)
}
func (m *FaceCheckResultReportResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FaceCheckResultReportResp.Marshal(b, m, deterministic)
}
func (dst *FaceCheckResultReportResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FaceCheckResultReportResp.Merge(dst, src)
}
func (m *FaceCheckResultReportResp) XXX_Size() int {
	return xxx_messageInfo_FaceCheckResultReportResp.Size(m)
}
func (m *FaceCheckResultReportResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FaceCheckResultReportResp.DiscardUnknown(m)
}

var xxx_messageInfo_FaceCheckResultReportResp proto.InternalMessageInfo

type SetDeviceForbidRealNameStatusReq struct {
	DeviceId             []byte   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Ttl                  int64    `protobuf:"varint,2,opt,name=ttl,proto3" json:"ttl,omitempty"`
	Uid                  uint64   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDeviceForbidRealNameStatusReq) Reset()         { *m = SetDeviceForbidRealNameStatusReq{} }
func (m *SetDeviceForbidRealNameStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetDeviceForbidRealNameStatusReq) ProtoMessage()    {}
func (*SetDeviceForbidRealNameStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{5}
}
func (m *SetDeviceForbidRealNameStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDeviceForbidRealNameStatusReq.Unmarshal(m, b)
}
func (m *SetDeviceForbidRealNameStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDeviceForbidRealNameStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetDeviceForbidRealNameStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDeviceForbidRealNameStatusReq.Merge(dst, src)
}
func (m *SetDeviceForbidRealNameStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetDeviceForbidRealNameStatusReq.Size(m)
}
func (m *SetDeviceForbidRealNameStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDeviceForbidRealNameStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDeviceForbidRealNameStatusReq proto.InternalMessageInfo

func (m *SetDeviceForbidRealNameStatusReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *SetDeviceForbidRealNameStatusReq) GetTtl() int64 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

func (m *SetDeviceForbidRealNameStatusReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetDeviceForbidRealNameStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDeviceForbidRealNameStatusResp) Reset()         { *m = SetDeviceForbidRealNameStatusResp{} }
func (m *SetDeviceForbidRealNameStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetDeviceForbidRealNameStatusResp) ProtoMessage()    {}
func (*SetDeviceForbidRealNameStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{6}
}
func (m *SetDeviceForbidRealNameStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDeviceForbidRealNameStatusResp.Unmarshal(m, b)
}
func (m *SetDeviceForbidRealNameStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDeviceForbidRealNameStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetDeviceForbidRealNameStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDeviceForbidRealNameStatusResp.Merge(dst, src)
}
func (m *SetDeviceForbidRealNameStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetDeviceForbidRealNameStatusResp.Size(m)
}
func (m *SetDeviceForbidRealNameStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDeviceForbidRealNameStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDeviceForbidRealNameStatusResp proto.InternalMessageInfo

type InDeviceForbidRealNameStatusReq struct {
	DeviceId             []byte   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InDeviceForbidRealNameStatusReq) Reset()         { *m = InDeviceForbidRealNameStatusReq{} }
func (m *InDeviceForbidRealNameStatusReq) String() string { return proto.CompactTextString(m) }
func (*InDeviceForbidRealNameStatusReq) ProtoMessage()    {}
func (*InDeviceForbidRealNameStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{7}
}
func (m *InDeviceForbidRealNameStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InDeviceForbidRealNameStatusReq.Unmarshal(m, b)
}
func (m *InDeviceForbidRealNameStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InDeviceForbidRealNameStatusReq.Marshal(b, m, deterministic)
}
func (dst *InDeviceForbidRealNameStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InDeviceForbidRealNameStatusReq.Merge(dst, src)
}
func (m *InDeviceForbidRealNameStatusReq) XXX_Size() int {
	return xxx_messageInfo_InDeviceForbidRealNameStatusReq.Size(m)
}
func (m *InDeviceForbidRealNameStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InDeviceForbidRealNameStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_InDeviceForbidRealNameStatusReq proto.InternalMessageInfo

func (m *InDeviceForbidRealNameStatusReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *InDeviceForbidRealNameStatusReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type InDeviceForbidRealNameStatusResp struct {
	InForbid             bool     `protobuf:"varint,1,opt,name=in_forbid,json=inForbid,proto3" json:"in_forbid,omitempty"`
	ExpiredAt            int64    `protobuf:"varint,2,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InDeviceForbidRealNameStatusResp) Reset()         { *m = InDeviceForbidRealNameStatusResp{} }
func (m *InDeviceForbidRealNameStatusResp) String() string { return proto.CompactTextString(m) }
func (*InDeviceForbidRealNameStatusResp) ProtoMessage()    {}
func (*InDeviceForbidRealNameStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{8}
}
func (m *InDeviceForbidRealNameStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InDeviceForbidRealNameStatusResp.Unmarshal(m, b)
}
func (m *InDeviceForbidRealNameStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InDeviceForbidRealNameStatusResp.Marshal(b, m, deterministic)
}
func (dst *InDeviceForbidRealNameStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InDeviceForbidRealNameStatusResp.Merge(dst, src)
}
func (m *InDeviceForbidRealNameStatusResp) XXX_Size() int {
	return xxx_messageInfo_InDeviceForbidRealNameStatusResp.Size(m)
}
func (m *InDeviceForbidRealNameStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InDeviceForbidRealNameStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_InDeviceForbidRealNameStatusResp proto.InternalMessageInfo

func (m *InDeviceForbidRealNameStatusResp) GetInForbid() bool {
	if m != nil {
		return m.InForbid
	}
	return false
}

func (m *InDeviceForbidRealNameStatusResp) GetExpiredAt() int64 {
	if m != nil {
		return m.ExpiredAt
	}
	return 0
}

type ClearDeviceForbidRealNameStatusReq struct {
	DeviceId             []byte   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearDeviceForbidRealNameStatusReq) Reset()         { *m = ClearDeviceForbidRealNameStatusReq{} }
func (m *ClearDeviceForbidRealNameStatusReq) String() string { return proto.CompactTextString(m) }
func (*ClearDeviceForbidRealNameStatusReq) ProtoMessage()    {}
func (*ClearDeviceForbidRealNameStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{9}
}
func (m *ClearDeviceForbidRealNameStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearDeviceForbidRealNameStatusReq.Unmarshal(m, b)
}
func (m *ClearDeviceForbidRealNameStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearDeviceForbidRealNameStatusReq.Marshal(b, m, deterministic)
}
func (dst *ClearDeviceForbidRealNameStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearDeviceForbidRealNameStatusReq.Merge(dst, src)
}
func (m *ClearDeviceForbidRealNameStatusReq) XXX_Size() int {
	return xxx_messageInfo_ClearDeviceForbidRealNameStatusReq.Size(m)
}
func (m *ClearDeviceForbidRealNameStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearDeviceForbidRealNameStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearDeviceForbidRealNameStatusReq proto.InternalMessageInfo

func (m *ClearDeviceForbidRealNameStatusReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *ClearDeviceForbidRealNameStatusReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ClearDeviceForbidRealNameStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearDeviceForbidRealNameStatusResp) Reset()         { *m = ClearDeviceForbidRealNameStatusResp{} }
func (m *ClearDeviceForbidRealNameStatusResp) String() string { return proto.CompactTextString(m) }
func (*ClearDeviceForbidRealNameStatusResp) ProtoMessage()    {}
func (*ClearDeviceForbidRealNameStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{10}
}
func (m *ClearDeviceForbidRealNameStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearDeviceForbidRealNameStatusResp.Unmarshal(m, b)
}
func (m *ClearDeviceForbidRealNameStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearDeviceForbidRealNameStatusResp.Marshal(b, m, deterministic)
}
func (dst *ClearDeviceForbidRealNameStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearDeviceForbidRealNameStatusResp.Merge(dst, src)
}
func (m *ClearDeviceForbidRealNameStatusResp) XXX_Size() int {
	return xxx_messageInfo_ClearDeviceForbidRealNameStatusResp.Size(m)
}
func (m *ClearDeviceForbidRealNameStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearDeviceForbidRealNameStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearDeviceForbidRealNameStatusResp proto.InternalMessageInfo

type FaceAuthResultToken struct {
	ProviderCode         string   `protobuf:"bytes,1,opt,name=provider_code,json=providerCode,proto3" json:"provider_code,omitempty"`
	Result               bool     `protobuf:"varint,2,opt,name=result,proto3" json:"result,omitempty"`
	Uid                  uint64   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Scene                string   `protobuf:"bytes,4,opt,name=scene,proto3" json:"scene,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ExpiredAt            int64    `protobuf:"varint,6,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FaceAuthResultToken) Reset()         { *m = FaceAuthResultToken{} }
func (m *FaceAuthResultToken) String() string { return proto.CompactTextString(m) }
func (*FaceAuthResultToken) ProtoMessage()    {}
func (*FaceAuthResultToken) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{11}
}
func (m *FaceAuthResultToken) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FaceAuthResultToken.Unmarshal(m, b)
}
func (m *FaceAuthResultToken) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FaceAuthResultToken.Marshal(b, m, deterministic)
}
func (dst *FaceAuthResultToken) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FaceAuthResultToken.Merge(dst, src)
}
func (m *FaceAuthResultToken) XXX_Size() int {
	return xxx_messageInfo_FaceAuthResultToken.Size(m)
}
func (m *FaceAuthResultToken) XXX_DiscardUnknown() {
	xxx_messageInfo_FaceAuthResultToken.DiscardUnknown(m)
}

var xxx_messageInfo_FaceAuthResultToken proto.InternalMessageInfo

func (m *FaceAuthResultToken) GetProviderCode() string {
	if m != nil {
		return m.ProviderCode
	}
	return ""
}

func (m *FaceAuthResultToken) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

func (m *FaceAuthResultToken) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FaceAuthResultToken) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *FaceAuthResultToken) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *FaceAuthResultToken) GetExpiredAt() int64 {
	if m != nil {
		return m.ExpiredAt
	}
	return 0
}

type VerifyResultTokenReq struct {
	ResultToken          string   `protobuf:"bytes,1,opt,name=result_token,json=resultToken,proto3" json:"result_token,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Scene                string   `protobuf:"bytes,3,opt,name=scene,proto3" json:"scene,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VerifyResultTokenReq) Reset()         { *m = VerifyResultTokenReq{} }
func (m *VerifyResultTokenReq) String() string { return proto.CompactTextString(m) }
func (*VerifyResultTokenReq) ProtoMessage()    {}
func (*VerifyResultTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{12}
}
func (m *VerifyResultTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VerifyResultTokenReq.Unmarshal(m, b)
}
func (m *VerifyResultTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VerifyResultTokenReq.Marshal(b, m, deterministic)
}
func (dst *VerifyResultTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerifyResultTokenReq.Merge(dst, src)
}
func (m *VerifyResultTokenReq) XXX_Size() int {
	return xxx_messageInfo_VerifyResultTokenReq.Size(m)
}
func (m *VerifyResultTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_VerifyResultTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_VerifyResultTokenReq proto.InternalMessageInfo

func (m *VerifyResultTokenReq) GetResultToken() string {
	if m != nil {
		return m.ResultToken
	}
	return ""
}

func (m *VerifyResultTokenReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VerifyResultTokenReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *VerifyResultTokenReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

type VerifyResultTokenResp struct {
	IsPass               bool     `protobuf:"varint,1,opt,name=is_pass,json=isPass,proto3" json:"is_pass,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VerifyResultTokenResp) Reset()         { *m = VerifyResultTokenResp{} }
func (m *VerifyResultTokenResp) String() string { return proto.CompactTextString(m) }
func (*VerifyResultTokenResp) ProtoMessage()    {}
func (*VerifyResultTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{13}
}
func (m *VerifyResultTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VerifyResultTokenResp.Unmarshal(m, b)
}
func (m *VerifyResultTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VerifyResultTokenResp.Marshal(b, m, deterministic)
}
func (dst *VerifyResultTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerifyResultTokenResp.Merge(dst, src)
}
func (m *VerifyResultTokenResp) XXX_Size() int {
	return xxx_messageInfo_VerifyResultTokenResp.Size(m)
}
func (m *VerifyResultTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_VerifyResultTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_VerifyResultTokenResp proto.InternalMessageInfo

func (m *VerifyResultTokenResp) GetIsPass() bool {
	if m != nil {
		return m.IsPass
	}
	return false
}

func (m *VerifyResultTokenResp) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CreateResultTokenReq struct {
	Data                 *FaceAuthResultToken `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CreateResultTokenReq) Reset()         { *m = CreateResultTokenReq{} }
func (m *CreateResultTokenReq) String() string { return proto.CompactTextString(m) }
func (*CreateResultTokenReq) ProtoMessage()    {}
func (*CreateResultTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{14}
}
func (m *CreateResultTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateResultTokenReq.Unmarshal(m, b)
}
func (m *CreateResultTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateResultTokenReq.Marshal(b, m, deterministic)
}
func (dst *CreateResultTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateResultTokenReq.Merge(dst, src)
}
func (m *CreateResultTokenReq) XXX_Size() int {
	return xxx_messageInfo_CreateResultTokenReq.Size(m)
}
func (m *CreateResultTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateResultTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateResultTokenReq proto.InternalMessageInfo

func (m *CreateResultTokenReq) GetData() *FaceAuthResultToken {
	if m != nil {
		return m.Data
	}
	return nil
}

type CreateResultTokenResp struct {
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateResultTokenResp) Reset()         { *m = CreateResultTokenResp{} }
func (m *CreateResultTokenResp) String() string { return proto.CompactTextString(m) }
func (*CreateResultTokenResp) ProtoMessage()    {}
func (*CreateResultTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_realnameauth_go_39f7cc784b5500e6, []int{15}
}
func (m *CreateResultTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateResultTokenResp.Unmarshal(m, b)
}
func (m *CreateResultTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateResultTokenResp.Marshal(b, m, deterministic)
}
func (dst *CreateResultTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateResultTokenResp.Merge(dst, src)
}
func (m *CreateResultTokenResp) XXX_Size() int {
	return xxx_messageInfo_CreateResultTokenResp.Size(m)
}
func (m *CreateResultTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateResultTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateResultTokenResp proto.InternalMessageInfo

func (m *CreateResultTokenResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func init() {
	proto.RegisterType((*FaceAuthContext)(nil), "realnameauth_go.FaceAuthContext")
	proto.RegisterType((*ReportFaceAuthResultReq)(nil), "realnameauth_go.ReportFaceAuthResultReq")
	proto.RegisterType((*ReportFaceAuthResultResp)(nil), "realnameauth_go.ReportFaceAuthResultResp")
	proto.RegisterType((*FaceCheckResultReportReq)(nil), "realnameauth_go.FaceCheckResultReportReq")
	proto.RegisterType((*FaceCheckResultReportResp)(nil), "realnameauth_go.FaceCheckResultReportResp")
	proto.RegisterType((*SetDeviceForbidRealNameStatusReq)(nil), "realnameauth_go.SetDeviceForbidRealNameStatusReq")
	proto.RegisterType((*SetDeviceForbidRealNameStatusResp)(nil), "realnameauth_go.SetDeviceForbidRealNameStatusResp")
	proto.RegisterType((*InDeviceForbidRealNameStatusReq)(nil), "realnameauth_go.InDeviceForbidRealNameStatusReq")
	proto.RegisterType((*InDeviceForbidRealNameStatusResp)(nil), "realnameauth_go.InDeviceForbidRealNameStatusResp")
	proto.RegisterType((*ClearDeviceForbidRealNameStatusReq)(nil), "realnameauth_go.ClearDeviceForbidRealNameStatusReq")
	proto.RegisterType((*ClearDeviceForbidRealNameStatusResp)(nil), "realnameauth_go.ClearDeviceForbidRealNameStatusResp")
	proto.RegisterType((*FaceAuthResultToken)(nil), "realnameauth_go.FaceAuthResultToken")
	proto.RegisterType((*VerifyResultTokenReq)(nil), "realnameauth_go.VerifyResultTokenReq")
	proto.RegisterType((*VerifyResultTokenResp)(nil), "realnameauth_go.VerifyResultTokenResp")
	proto.RegisterType((*CreateResultTokenReq)(nil), "realnameauth_go.CreateResultTokenReq")
	proto.RegisterType((*CreateResultTokenResp)(nil), "realnameauth_go.CreateResultTokenResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RealNameAuthGoSvrClient is the client API for RealNameAuthGoSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RealNameAuthGoSvrClient interface {
	// （旧）跟人群包相关
	FaceCheckResultReport(ctx context.Context, in *FaceCheckResultReportReq, opts ...grpc.CallOption) (*FaceCheckResultReportResp, error)
	// （新）人脸结果上报
	ReportFaceAuthResult(ctx context.Context, in *ReportFaceAuthResultReq, opts ...grpc.CallOption) (*ReportFaceAuthResultResp, error)
	// 设置设备禁止人脸实名的状态
	SetDeviceForbidRealNameStatus(ctx context.Context, in *SetDeviceForbidRealNameStatusReq, opts ...grpc.CallOption) (*SetDeviceForbidRealNameStatusResp, error)
	ClearDeviceForbidRealNameStatus(ctx context.Context, in *ClearDeviceForbidRealNameStatusReq, opts ...grpc.CallOption) (*ClearDeviceForbidRealNameStatusResp, error)
	InDeviceForbidRealNameStatus(ctx context.Context, in *InDeviceForbidRealNameStatusReq, opts ...grpc.CallOption) (*InDeviceForbidRealNameStatusResp, error)
	CreateFaceAuthResultToken(ctx context.Context, in *CreateResultTokenReq, opts ...grpc.CallOption) (*CreateResultTokenResp, error)
	VerifyFaceAuthResultToken(ctx context.Context, in *VerifyResultTokenReq, opts ...grpc.CallOption) (*VerifyResultTokenResp, error)
}

type realNameAuthGoSvrClient struct {
	cc *grpc.ClientConn
}

func NewRealNameAuthGoSvrClient(cc *grpc.ClientConn) RealNameAuthGoSvrClient {
	return &realNameAuthGoSvrClient{cc}
}

func (c *realNameAuthGoSvrClient) FaceCheckResultReport(ctx context.Context, in *FaceCheckResultReportReq, opts ...grpc.CallOption) (*FaceCheckResultReportResp, error) {
	out := new(FaceCheckResultReportResp)
	err := c.cc.Invoke(ctx, "/realnameauth_go.RealNameAuthGoSvr/FaceCheckResultReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthGoSvrClient) ReportFaceAuthResult(ctx context.Context, in *ReportFaceAuthResultReq, opts ...grpc.CallOption) (*ReportFaceAuthResultResp, error) {
	out := new(ReportFaceAuthResultResp)
	err := c.cc.Invoke(ctx, "/realnameauth_go.RealNameAuthGoSvr/ReportFaceAuthResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthGoSvrClient) SetDeviceForbidRealNameStatus(ctx context.Context, in *SetDeviceForbidRealNameStatusReq, opts ...grpc.CallOption) (*SetDeviceForbidRealNameStatusResp, error) {
	out := new(SetDeviceForbidRealNameStatusResp)
	err := c.cc.Invoke(ctx, "/realnameauth_go.RealNameAuthGoSvr/SetDeviceForbidRealNameStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthGoSvrClient) ClearDeviceForbidRealNameStatus(ctx context.Context, in *ClearDeviceForbidRealNameStatusReq, opts ...grpc.CallOption) (*ClearDeviceForbidRealNameStatusResp, error) {
	out := new(ClearDeviceForbidRealNameStatusResp)
	err := c.cc.Invoke(ctx, "/realnameauth_go.RealNameAuthGoSvr/ClearDeviceForbidRealNameStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthGoSvrClient) InDeviceForbidRealNameStatus(ctx context.Context, in *InDeviceForbidRealNameStatusReq, opts ...grpc.CallOption) (*InDeviceForbidRealNameStatusResp, error) {
	out := new(InDeviceForbidRealNameStatusResp)
	err := c.cc.Invoke(ctx, "/realnameauth_go.RealNameAuthGoSvr/InDeviceForbidRealNameStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthGoSvrClient) CreateFaceAuthResultToken(ctx context.Context, in *CreateResultTokenReq, opts ...grpc.CallOption) (*CreateResultTokenResp, error) {
	out := new(CreateResultTokenResp)
	err := c.cc.Invoke(ctx, "/realnameauth_go.RealNameAuthGoSvr/CreateFaceAuthResultToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *realNameAuthGoSvrClient) VerifyFaceAuthResultToken(ctx context.Context, in *VerifyResultTokenReq, opts ...grpc.CallOption) (*VerifyResultTokenResp, error) {
	out := new(VerifyResultTokenResp)
	err := c.cc.Invoke(ctx, "/realnameauth_go.RealNameAuthGoSvr/VerifyFaceAuthResultToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RealNameAuthGoSvrServer is the server API for RealNameAuthGoSvr service.
type RealNameAuthGoSvrServer interface {
	// （旧）跟人群包相关
	FaceCheckResultReport(context.Context, *FaceCheckResultReportReq) (*FaceCheckResultReportResp, error)
	// （新）人脸结果上报
	ReportFaceAuthResult(context.Context, *ReportFaceAuthResultReq) (*ReportFaceAuthResultResp, error)
	// 设置设备禁止人脸实名的状态
	SetDeviceForbidRealNameStatus(context.Context, *SetDeviceForbidRealNameStatusReq) (*SetDeviceForbidRealNameStatusResp, error)
	ClearDeviceForbidRealNameStatus(context.Context, *ClearDeviceForbidRealNameStatusReq) (*ClearDeviceForbidRealNameStatusResp, error)
	InDeviceForbidRealNameStatus(context.Context, *InDeviceForbidRealNameStatusReq) (*InDeviceForbidRealNameStatusResp, error)
	CreateFaceAuthResultToken(context.Context, *CreateResultTokenReq) (*CreateResultTokenResp, error)
	VerifyFaceAuthResultToken(context.Context, *VerifyResultTokenReq) (*VerifyResultTokenResp, error)
}

func RegisterRealNameAuthGoSvrServer(s *grpc.Server, srv RealNameAuthGoSvrServer) {
	s.RegisterService(&_RealNameAuthGoSvr_serviceDesc, srv)
}

func _RealNameAuthGoSvr_FaceCheckResultReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FaceCheckResultReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthGoSvrServer).FaceCheckResultReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth_go.RealNameAuthGoSvr/FaceCheckResultReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthGoSvrServer).FaceCheckResultReport(ctx, req.(*FaceCheckResultReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuthGoSvr_ReportFaceAuthResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportFaceAuthResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthGoSvrServer).ReportFaceAuthResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth_go.RealNameAuthGoSvr/ReportFaceAuthResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthGoSvrServer).ReportFaceAuthResult(ctx, req.(*ReportFaceAuthResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuthGoSvr_SetDeviceForbidRealNameStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDeviceForbidRealNameStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthGoSvrServer).SetDeviceForbidRealNameStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth_go.RealNameAuthGoSvr/SetDeviceForbidRealNameStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthGoSvrServer).SetDeviceForbidRealNameStatus(ctx, req.(*SetDeviceForbidRealNameStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuthGoSvr_ClearDeviceForbidRealNameStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearDeviceForbidRealNameStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthGoSvrServer).ClearDeviceForbidRealNameStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth_go.RealNameAuthGoSvr/ClearDeviceForbidRealNameStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthGoSvrServer).ClearDeviceForbidRealNameStatus(ctx, req.(*ClearDeviceForbidRealNameStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuthGoSvr_InDeviceForbidRealNameStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InDeviceForbidRealNameStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthGoSvrServer).InDeviceForbidRealNameStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth_go.RealNameAuthGoSvr/InDeviceForbidRealNameStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthGoSvrServer).InDeviceForbidRealNameStatus(ctx, req.(*InDeviceForbidRealNameStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuthGoSvr_CreateFaceAuthResultToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateResultTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthGoSvrServer).CreateFaceAuthResultToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth_go.RealNameAuthGoSvr/CreateFaceAuthResultToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthGoSvrServer).CreateFaceAuthResultToken(ctx, req.(*CreateResultTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RealNameAuthGoSvr_VerifyFaceAuthResultToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyResultTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RealNameAuthGoSvrServer).VerifyFaceAuthResultToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/realnameauth_go.RealNameAuthGoSvr/VerifyFaceAuthResultToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RealNameAuthGoSvrServer).VerifyFaceAuthResultToken(ctx, req.(*VerifyResultTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RealNameAuthGoSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "realnameauth_go.RealNameAuthGoSvr",
	HandlerType: (*RealNameAuthGoSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FaceCheckResultReport",
			Handler:    _RealNameAuthGoSvr_FaceCheckResultReport_Handler,
		},
		{
			MethodName: "ReportFaceAuthResult",
			Handler:    _RealNameAuthGoSvr_ReportFaceAuthResult_Handler,
		},
		{
			MethodName: "SetDeviceForbidRealNameStatus",
			Handler:    _RealNameAuthGoSvr_SetDeviceForbidRealNameStatus_Handler,
		},
		{
			MethodName: "ClearDeviceForbidRealNameStatus",
			Handler:    _RealNameAuthGoSvr_ClearDeviceForbidRealNameStatus_Handler,
		},
		{
			MethodName: "InDeviceForbidRealNameStatus",
			Handler:    _RealNameAuthGoSvr_InDeviceForbidRealNameStatus_Handler,
		},
		{
			MethodName: "CreateFaceAuthResultToken",
			Handler:    _RealNameAuthGoSvr_CreateFaceAuthResultToken_Handler,
		},
		{
			MethodName: "VerifyFaceAuthResultToken",
			Handler:    _RealNameAuthGoSvr_VerifyFaceAuthResultToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "realnameauth-go/realnameauth-go.proto",
}

func init() {
	proto.RegisterFile("realnameauth-go/realnameauth-go.proto", fileDescriptor_realnameauth_go_39f7cc784b5500e6)
}

var fileDescriptor_realnameauth_go_39f7cc784b5500e6 = []byte{
	// 907 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0xc1, 0x6e, 0xdb, 0x46,
	0x10, 0x0d, 0x2d, 0x59, 0x91, 0xc7, 0x72, 0x15, 0x6f, 0xec, 0x44, 0x51, 0x1a, 0x44, 0xa6, 0xeb,
	0xd6, 0x09, 0x10, 0xb9, 0x96, 0x5b, 0xa0, 0xd7, 0x44, 0x85, 0x0b, 0x01, 0x45, 0x61, 0x50, 0x46,
	0x0f, 0x3d, 0x94, 0x58, 0x93, 0x23, 0x79, 0x61, 0x8a, 0xdc, 0xec, 0x2e, 0x8d, 0x38, 0x87, 0xde,
	0x0a, 0xf4, 0xd4, 0x5b, 0x8f, 0xbd, 0xf6, 0x13, 0xfa, 0x65, 0xfd, 0x80, 0x62, 0x77, 0x49, 0x5b,
	0xa2, 0x28, 0xcb, 0x46, 0x7b, 0xd3, 0xce, 0xce, 0xbc, 0x99, 0xf7, 0x76, 0x66, 0x44, 0xd8, 0x13,
	0x48, 0xa3, 0x98, 0x4e, 0x90, 0xa6, 0xea, 0xfc, 0xcd, 0x38, 0x39, 0x28, 0x9c, 0xbb, 0x5c, 0x24,
	0x2a, 0x21, 0xcd, 0x69, 0xb3, 0x3f, 0x4e, 0xdc, 0x3f, 0x56, 0xa0, 0x79, 0x4c, 0x03, 0x7c, 0x9b,
	0xaa, 0xf3, 0x7e, 0x12, 0x2b, 0xfc, 0xa0, 0xc8, 0x73, 0x58, 0x3b, 0x63, 0x1f, 0x7d, 0x19, 0x60,
	0x8c, 0x2d, 0xa7, 0xe3, 0xec, 0xaf, 0x79, 0xf5, 0x33, 0xf6, 0x71, 0xa8, 0xcf, 0xe4, 0x05, 0x40,
	0x70, 0x4e, 0xe3, 0x18, 0x23, 0x9f, 0x85, 0xad, 0x95, 0x8e, 0xb3, 0xbf, 0xe1, 0xad, 0x65, 0x96,
	0x41, 0x48, 0x8e, 0xe0, 0xc9, 0x88, 0x06, 0xe8, 0x1b, 0x7c, 0x81, 0xef, 0x53, 0x26, 0xd0, 0xd7,
	0xa8, 0xad, 0x8a, 0x01, 0x7a, 0x3c, 0xca, 0x92, 0x79, 0xf6, 0xee, 0x54, 0x27, 0x9c, 0x09, 0x92,
	0x69, 0x10, 0xa0, 0x94, 0x36, 0xa8, 0x3a, 0x1b, 0x34, 0xb4, 0x77, 0x26, 0xe8, 0x11, 0x54, 0x52,
	0x16, 0xb6, 0x56, 0x3b, 0xce, 0x7e, 0xd5, 0xd3, 0x3f, 0xc9, 0x17, 0xd0, 0xe4, 0x22, 0xd1, 0x0e,
	0x2c, 0x1e, 0xdb, 0xf8, 0x9a, 0x89, 0xff, 0xe4, 0xc6, 0x6c, 0x42, 0x77, 0xa0, 0x31, 0xa2, 0x2c,
	0x4a, 0xf3, 0xd2, 0x1e, 0x1a, 0xaf, 0xf5, 0xcc, 0xa6, 0x5d, 0xdc, 0x3f, 0x57, 0xe0, 0xa9, 0x87,
	0x3c, 0x11, 0xea, 0xf8, 0xba, 0x60, 0x99, 0x46, 0xca, 0xc3, 0xf7, 0x79, 0x66, 0xc7, 0x70, 0x37,
	0x99, 0x9f, 0xc2, 0x43, 0x26, 0x7d, 0x4e, 0xa5, 0x34, 0x8a, 0xd4, 0xbd, 0x1a, 0x93, 0x27, 0x54,
	0x4a, 0x2d, 0x65, 0x88, 0x97, 0x2c, 0x40, 0x2d, 0x96, 0x56, 0xa0, 0xe1, 0xd5, 0xad, 0x61, 0x10,
	0xea, 0xcb, 0x09, 0x15, 0x17, 0xa8, 0xf4, 0x65, 0xd5, 0xa0, 0xd5, 0xad, 0x61, 0x10, 0x92, 0x5d,
	0xd8, 0x50, 0x28, 0x26, 0x2c, 0xa6, 0x91, 0xaf, 0xae, 0x38, 0x1a, 0xa2, 0x1b, 0x5e, 0x23, 0x37,
	0x9e, 0x5e, 0x71, 0x24, 0xaf, 0x61, 0x53, 0x98, 0xb2, 0x8c, 0xd4, 0x28, 0x0d, 0x92, 0xe5, 0xdc,
	0x14, 0x79, 0xbd, 0xda, 0x3e, 0x08, 0xc9, 0xf7, 0xb0, 0x79, 0x23, 0x72, 0x60, 0x9f, 0xda, 0x30,
	0x5f, 0xef, 0x75, 0xba, 0x85, 0xb6, 0xe8, 0x16, 0x5a, 0xc2, 0x6b, 0x8e, 0x66, 0x0d, 0x6e, 0x1b,
	0x5a, 0xe5, 0xf2, 0x48, 0xee, 0xfe, 0xe5, 0x40, 0x4b, 0x9b, 0xfb, 0xe7, 0x18, 0x5c, 0xe4, 0x76,
	0xed, 0x5b, 0x2e, 0x9e, 0x66, 0x2a, 0xd8, 0x78, 0x8c, 0x22, 0x6b, 0xb9, 0x95, 0x8c, 0xa9, 0x35,
	0x5e, 0xb7, 0xdd, 0x14, 0x45, 0xab, 0xe4, 0x9a, 0xb8, 0x26, 0x37, 0xf5, 0x00, 0xd5, 0xc5, 0x0f,
	0xb0, 0x3a, 0xfb, 0x00, 0xee, 0x73, 0x78, 0xb6, 0xa0, 0x4e, 0xc9, 0xdd, 0x00, 0x3a, 0x43, 0x54,
	0xdf, 0x1a, 0xdf, 0xe3, 0x44, 0x9c, 0xb1, 0xd0, 0x43, 0x1a, 0xfd, 0x40, 0x27, 0x38, 0x54, 0x54,
	0xa5, 0x52, 0x93, 0x99, 0x41, 0x77, 0x0a, 0xcf, 0xfb, 0x08, 0x2a, 0x4a, 0x45, 0x86, 0x4d, 0xc5,
	0xd3, 0x3f, 0x73, 0xee, 0x95, 0xeb, 0x96, 0x75, 0x77, 0x61, 0x67, 0x49, 0x12, 0xc9, 0xdd, 0x13,
	0x78, 0x39, 0x88, 0xff, 0x5b, 0x21, 0x69, 0x36, 0xab, 0x59, 0xda, 0x9f, 0xa1, 0x73, 0x3b, 0xa2,
	0xe4, 0x1a, 0x92, 0xc5, 0xfe, 0xc8, 0x5c, 0x1b, 0xc8, 0xba, 0x57, 0x67, 0xb1, 0x75, 0xd7, 0xcf,
	0x81, 0x1f, 0x38, 0x13, 0x18, 0xfa, 0x54, 0x65, 0x14, 0xd7, 0x32, 0xcb, 0x5b, 0xe5, 0x0e, 0xc1,
	0xed, 0x47, 0x48, 0xc5, 0xff, 0x5a, 0xf4, 0x1e, 0xec, 0x2e, 0x05, 0x95, 0xdc, 0xfd, 0xdb, 0x81,
	0xc7, 0xb3, 0x4d, 0x79, 0x9a, 0x5c, 0x60, 0xac, 0xdb, 0x8c, 0x8b, 0xe4, 0x92, 0x85, 0x28, 0xfc,
	0x20, 0x09, 0xf3, 0xcd, 0xd6, 0xc8, 0x8d, 0xfd, 0x24, 0x44, 0xf2, 0x04, 0x6a, 0x76, 0x6e, 0xf2,
	0x39, 0xb6, 0xa7, 0xf9, 0x97, 0x23, 0x5b, 0xb0, 0x6a, 0xbb, 0xd5, 0xae, 0x28, 0x7b, 0xb8, 0xb5,
	0xdd, 0x0a, 0xa2, 0xd5, 0x8a, 0xa2, 0xfd, 0x02, 0x5b, 0x3f, 0xa2, 0x60, 0xa3, 0xab, 0xa9, 0xaa,
	0xb5, 0x4c, 0x3b, 0xd0, 0xc8, 0x86, 0x5c, 0x69, 0x53, 0x56, 0xf7, 0xba, 0x98, 0xe2, 0x36, 0x27,
	0xd6, 0x4d, 0x79, 0x95, 0x85, 0xe5, 0x55, 0x0b, 0xd3, 0xf0, 0x0e, 0xb6, 0x4b, 0xf2, 0x4b, 0x3e,
	0x3d, 0x5c, 0xce, 0xcc, 0x70, 0xcd, 0xbf, 0xd1, 0x09, 0x6c, 0xf5, 0x05, 0x52, 0x85, 0x05, 0x0e,
	0xdf, 0x40, 0x35, 0xa4, 0x8a, 0x9a, 0xf8, 0xf5, 0xde, 0x67, 0x0b, 0xf7, 0xcd, 0x74, 0x98, 0x89,
	0x70, 0xdf, 0xc0, 0x76, 0x09, 0xa2, 0xe4, 0x9a, 0xe1, 0xb4, 0x1e, 0xf6, 0xd0, 0xfb, 0xa7, 0x06,
	0x9b, 0x79, 0x53, 0x68, 0xc0, 0xef, 0x92, 0xe1, 0xa5, 0x20, 0x1c, 0xb6, 0x4b, 0x07, 0x9d, 0xbc,
	0x2a, 0xad, 0xa4, 0x6c, 0x71, 0xb5, 0x5f, 0xdf, 0xd5, 0x55, 0x72, 0xf7, 0x01, 0x99, 0xc0, 0x56,
	0xd9, 0x7e, 0x24, 0xfb, 0x73, 0x28, 0x0b, 0xfe, 0x65, 0xda, 0xaf, 0xee, 0xe8, 0x69, 0xd2, 0xfd,
	0xe6, 0xc0, 0x8b, 0x5b, 0x17, 0x09, 0x39, 0x9c, 0x83, 0x5b, 0xb6, 0xdd, 0xda, 0xbd, 0xfb, 0x86,
	0x98, 0x52, 0x7e, 0x77, 0xe0, 0xe5, 0x92, 0x39, 0x25, 0x47, 0x73, 0xc8, 0xcb, 0xd7, 0x45, 0xfb,
	0xab, 0xfb, 0x07, 0x99, 0x82, 0x7e, 0x75, 0xe0, 0xd3, 0xdb, 0xb6, 0x1d, 0xf9, 0x72, 0x0e, 0x78,
	0xc9, 0xba, 0x6d, 0x1f, 0xde, 0x33, 0xc2, 0xd4, 0x11, 0xc1, 0x33, 0xdb, 0xc9, 0x65, 0xdb, 0x69,
	0x6f, 0x9e, 0x5c, 0xc9, 0x1c, 0xb5, 0x3f, 0xbf, 0x8b, 0x5b, 0x9e, 0xcd, 0x4e, 0xf3, 0xdd, 0xb2,
	0x95, 0x6d, 0x9e, 0x92, 0x6c, 0xa5, 0x0b, 0xc2, 0x7d, 0xf0, 0xee, 0xe8, 0xa7, 0xc3, 0x71, 0x12,
	0xd1, 0x78, 0xdc, 0xfd, 0xba, 0xa7, 0x54, 0x37, 0x48, 0x26, 0x07, 0xe6, 0x83, 0x33, 0x48, 0xa2,
	0x03, 0x89, 0x42, 0x2b, 0x23, 0x8b, 0x9f, 0xa4, 0x67, 0x35, 0xe3, 0x72, 0xf4, 0x6f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0x35, 0x3d, 0xba, 0x72, 0xbc, 0x0a, 0x00, 0x00,
}
