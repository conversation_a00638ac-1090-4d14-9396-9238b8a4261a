// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cybros/arbiter/v2/audio.proto

package v2 // import "golang.52tt.com/protocol/services/cybros/arbiter/v2"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AudioBinaryData struct {
	// optional format info for PCM format audio data
	FormatInfo           *AudioBinaryData_AudioFormatInfo `protobuf:"bytes,1,opt,name=format_info,json=formatInfo,proto3" json:"format_info,omitempty"`
	Content              []byte                           `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *AudioBinaryData) Reset()         { *m = AudioBinaryData{} }
func (m *AudioBinaryData) String() string { return proto.CompactTextString(m) }
func (*AudioBinaryData) ProtoMessage()    {}
func (*AudioBinaryData) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_8f7093b57ce30354, []int{0}
}
func (m *AudioBinaryData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioBinaryData.Unmarshal(m, b)
}
func (m *AudioBinaryData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioBinaryData.Marshal(b, m, deterministic)
}
func (dst *AudioBinaryData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioBinaryData.Merge(dst, src)
}
func (m *AudioBinaryData) XXX_Size() int {
	return xxx_messageInfo_AudioBinaryData.Size(m)
}
func (m *AudioBinaryData) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioBinaryData.DiscardUnknown(m)
}

var xxx_messageInfo_AudioBinaryData proto.InternalMessageInfo

func (m *AudioBinaryData) GetFormatInfo() *AudioBinaryData_AudioFormatInfo {
	if m != nil {
		return m.FormatInfo
	}
	return nil
}

func (m *AudioBinaryData) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

type AudioBinaryData_AudioFormatInfo struct {
	Format               string   `protobuf:"bytes,1,opt,name=format,proto3" json:"format,omitempty"`
	SampleRate           uint32   `protobuf:"varint,2,opt,name=sample_rate,json=sampleRate,proto3" json:"sample_rate,omitempty"`
	Track                uint32   `protobuf:"varint,3,opt,name=track,proto3" json:"track,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioBinaryData_AudioFormatInfo) Reset()         { *m = AudioBinaryData_AudioFormatInfo{} }
func (m *AudioBinaryData_AudioFormatInfo) String() string { return proto.CompactTextString(m) }
func (*AudioBinaryData_AudioFormatInfo) ProtoMessage()    {}
func (*AudioBinaryData_AudioFormatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_8f7093b57ce30354, []int{0, 0}
}
func (m *AudioBinaryData_AudioFormatInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioBinaryData_AudioFormatInfo.Unmarshal(m, b)
}
func (m *AudioBinaryData_AudioFormatInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioBinaryData_AudioFormatInfo.Marshal(b, m, deterministic)
}
func (dst *AudioBinaryData_AudioFormatInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioBinaryData_AudioFormatInfo.Merge(dst, src)
}
func (m *AudioBinaryData_AudioFormatInfo) XXX_Size() int {
	return xxx_messageInfo_AudioBinaryData_AudioFormatInfo.Size(m)
}
func (m *AudioBinaryData_AudioFormatInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioBinaryData_AudioFormatInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AudioBinaryData_AudioFormatInfo proto.InternalMessageInfo

func (m *AudioBinaryData_AudioFormatInfo) GetFormat() string {
	if m != nil {
		return m.Format
	}
	return ""
}

func (m *AudioBinaryData_AudioFormatInfo) GetSampleRate() uint32 {
	if m != nil {
		return m.SampleRate
	}
	return 0
}

func (m *AudioBinaryData_AudioFormatInfo) GetTrack() uint32 {
	if m != nil {
		return m.Track
	}
	return 0
}

type ZegoStream struct {
	TokenId              string   `protobuf:"bytes,1,opt,name=token_id,json=tokenId,proto3" json:"token_id,omitempty"`
	StreamId             string   `protobuf:"bytes,2,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	TestEnv              bool     `protobuf:"varint,3,opt,name=test_env,json=testEnv,proto3" json:"test_env,omitempty"`
	RoomId               string   `protobuf:"bytes,4,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ZegoStream) Reset()         { *m = ZegoStream{} }
func (m *ZegoStream) String() string { return proto.CompactTextString(m) }
func (*ZegoStream) ProtoMessage()    {}
func (*ZegoStream) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_8f7093b57ce30354, []int{1}
}
func (m *ZegoStream) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ZegoStream.Unmarshal(m, b)
}
func (m *ZegoStream) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ZegoStream.Marshal(b, m, deterministic)
}
func (dst *ZegoStream) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ZegoStream.Merge(dst, src)
}
func (m *ZegoStream) XXX_Size() int {
	return xxx_messageInfo_ZegoStream.Size(m)
}
func (m *ZegoStream) XXX_DiscardUnknown() {
	xxx_messageInfo_ZegoStream.DiscardUnknown(m)
}

var xxx_messageInfo_ZegoStream proto.InternalMessageInfo

func (m *ZegoStream) GetTokenId() string {
	if m != nil {
		return m.TokenId
	}
	return ""
}

func (m *ZegoStream) GetStreamId() string {
	if m != nil {
		return m.StreamId
	}
	return ""
}

func (m *ZegoStream) GetTestEnv() bool {
	if m != nil {
		return m.TestEnv
	}
	return false
}

func (m *ZegoStream) GetRoomId() string {
	if m != nil {
		return m.RoomId
	}
	return ""
}

type AudioStream struct {
	// Types that are valid to be assigned to StreamType:
	//	*AudioStream_ZegoStream
	StreamType           isAudioStream_StreamType `protobuf_oneof:"stream_type"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AudioStream) Reset()         { *m = AudioStream{} }
func (m *AudioStream) String() string { return proto.CompactTextString(m) }
func (*AudioStream) ProtoMessage()    {}
func (*AudioStream) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_8f7093b57ce30354, []int{2}
}
func (m *AudioStream) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioStream.Unmarshal(m, b)
}
func (m *AudioStream) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioStream.Marshal(b, m, deterministic)
}
func (dst *AudioStream) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioStream.Merge(dst, src)
}
func (m *AudioStream) XXX_Size() int {
	return xxx_messageInfo_AudioStream.Size(m)
}
func (m *AudioStream) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioStream.DiscardUnknown(m)
}

var xxx_messageInfo_AudioStream proto.InternalMessageInfo

type isAudioStream_StreamType interface {
	isAudioStream_StreamType()
}

type AudioStream_ZegoStream struct {
	ZegoStream *ZegoStream `protobuf:"bytes,1,opt,name=zego_stream,json=zegoStream,proto3,oneof"`
}

func (*AudioStream_ZegoStream) isAudioStream_StreamType() {}

func (m *AudioStream) GetStreamType() isAudioStream_StreamType {
	if m != nil {
		return m.StreamType
	}
	return nil
}

func (m *AudioStream) GetZegoStream() *ZegoStream {
	if x, ok := m.GetStreamType().(*AudioStream_ZegoStream); ok {
		return x.ZegoStream
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*AudioStream) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _AudioStream_OneofMarshaler, _AudioStream_OneofUnmarshaler, _AudioStream_OneofSizer, []interface{}{
		(*AudioStream_ZegoStream)(nil),
	}
}

func _AudioStream_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*AudioStream)
	// stream_type
	switch x := m.StreamType.(type) {
	case *AudioStream_ZegoStream:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ZegoStream); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("AudioStream.StreamType has unexpected type %T", x)
	}
	return nil
}

func _AudioStream_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*AudioStream)
	switch tag {
	case 1: // stream_type.zego_stream
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ZegoStream)
		err := b.DecodeMessage(msg)
		m.StreamType = &AudioStream_ZegoStream{msg}
		return true, err
	default:
		return false, nil
	}
}

func _AudioStream_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*AudioStream)
	// stream_type
	switch x := m.StreamType.(type) {
	case *AudioStream_ZegoStream:
		s := proto.Size(x.ZegoStream)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type AudioData struct {
	Metadata  *Metadata `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	AudioText string    `protobuf:"bytes,5,opt,name=audio_text,json=audioText,proto3" json:"audio_text,omitempty"`
	// Types that are valid to be assigned to AudioData:
	//	*AudioData_Url
	//	*AudioData_AudioBin
	//	*AudioData_AudioStream
	AudioData            isAudioData_AudioData `protobuf_oneof:"audio_data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *AudioData) Reset()         { *m = AudioData{} }
func (m *AudioData) String() string { return proto.CompactTextString(m) }
func (*AudioData) ProtoMessage()    {}
func (*AudioData) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_8f7093b57ce30354, []int{3}
}
func (m *AudioData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioData.Unmarshal(m, b)
}
func (m *AudioData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioData.Marshal(b, m, deterministic)
}
func (dst *AudioData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioData.Merge(dst, src)
}
func (m *AudioData) XXX_Size() int {
	return xxx_messageInfo_AudioData.Size(m)
}
func (m *AudioData) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioData.DiscardUnknown(m)
}

var xxx_messageInfo_AudioData proto.InternalMessageInfo

func (m *AudioData) GetMetadata() *Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (m *AudioData) GetAudioText() string {
	if m != nil {
		return m.AudioText
	}
	return ""
}

type isAudioData_AudioData interface {
	isAudioData_AudioData()
}

type AudioData_Url struct {
	Url string `protobuf:"bytes,2,opt,name=url,proto3,oneof"`
}

type AudioData_AudioBin struct {
	AudioBin *AudioBinaryData `protobuf:"bytes,3,opt,name=audio_bin,json=audioBin,proto3,oneof"`
}

type AudioData_AudioStream struct {
	AudioStream *AudioStream `protobuf:"bytes,4,opt,name=audio_stream,json=audioStream,proto3,oneof"`
}

func (*AudioData_Url) isAudioData_AudioData() {}

func (*AudioData_AudioBin) isAudioData_AudioData() {}

func (*AudioData_AudioStream) isAudioData_AudioData() {}

func (m *AudioData) GetAudioData() isAudioData_AudioData {
	if m != nil {
		return m.AudioData
	}
	return nil
}

func (m *AudioData) GetUrl() string {
	if x, ok := m.GetAudioData().(*AudioData_Url); ok {
		return x.Url
	}
	return ""
}

func (m *AudioData) GetAudioBin() *AudioBinaryData {
	if x, ok := m.GetAudioData().(*AudioData_AudioBin); ok {
		return x.AudioBin
	}
	return nil
}

func (m *AudioData) GetAudioStream() *AudioStream {
	if x, ok := m.GetAudioData().(*AudioData_AudioStream); ok {
		return x.AudioStream
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*AudioData) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _AudioData_OneofMarshaler, _AudioData_OneofUnmarshaler, _AudioData_OneofSizer, []interface{}{
		(*AudioData_Url)(nil),
		(*AudioData_AudioBin)(nil),
		(*AudioData_AudioStream)(nil),
	}
}

func _AudioData_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*AudioData)
	// audio_data
	switch x := m.AudioData.(type) {
	case *AudioData_Url:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.Url)
	case *AudioData_AudioBin:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.AudioBin); err != nil {
			return err
		}
	case *AudioData_AudioStream:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.AudioStream); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("AudioData.AudioData has unexpected type %T", x)
	}
	return nil
}

func _AudioData_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*AudioData)
	switch tag {
	case 2: // audio_data.url
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.AudioData = &AudioData_Url{x}
		return true, err
	case 3: // audio_data.audio_bin
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(AudioBinaryData)
		err := b.DecodeMessage(msg)
		m.AudioData = &AudioData_AudioBin{msg}
		return true, err
	case 4: // audio_data.audio_stream
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(AudioStream)
		err := b.DecodeMessage(msg)
		m.AudioData = &AudioData_AudioStream{msg}
		return true, err
	default:
		return false, nil
	}
}

func _AudioData_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*AudioData)
	// audio_data
	switch x := m.AudioData.(type) {
	case *AudioData_Url:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.Url)))
		n += len(x.Url)
	case *AudioData_AudioBin:
		s := proto.Size(x.AudioBin)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *AudioData_AudioStream:
		s := proto.Size(x.AudioStream)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type ScanAudioReq struct {
	Context              *TaskContext `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	AudioData            *AudioData   `protobuf:"bytes,2,opt,name=audio_data,json=audioData,proto3" json:"audio_data,omitempty"`
	Callback             *Callback    `protobuf:"bytes,3,opt,name=callback,proto3" json:"callback,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ScanAudioReq) Reset()         { *m = ScanAudioReq{} }
func (m *ScanAudioReq) String() string { return proto.CompactTextString(m) }
func (*ScanAudioReq) ProtoMessage()    {}
func (*ScanAudioReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_8f7093b57ce30354, []int{4}
}
func (m *ScanAudioReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanAudioReq.Unmarshal(m, b)
}
func (m *ScanAudioReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanAudioReq.Marshal(b, m, deterministic)
}
func (dst *ScanAudioReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanAudioReq.Merge(dst, src)
}
func (m *ScanAudioReq) XXX_Size() int {
	return xxx_messageInfo_ScanAudioReq.Size(m)
}
func (m *ScanAudioReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanAudioReq.DiscardUnknown(m)
}

var xxx_messageInfo_ScanAudioReq proto.InternalMessageInfo

func (m *ScanAudioReq) GetContext() *TaskContext {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *ScanAudioReq) GetAudioData() *AudioData {
	if m != nil {
		return m.AudioData
	}
	return nil
}

func (m *ScanAudioReq) GetCallback() *Callback {
	if m != nil {
		return m.Callback
	}
	return nil
}

type ScanAudioResp struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScanAudioResp) Reset()         { *m = ScanAudioResp{} }
func (m *ScanAudioResp) String() string { return proto.CompactTextString(m) }
func (*ScanAudioResp) ProtoMessage()    {}
func (*ScanAudioResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_8f7093b57ce30354, []int{5}
}
func (m *ScanAudioResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanAudioResp.Unmarshal(m, b)
}
func (m *ScanAudioResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanAudioResp.Marshal(b, m, deterministic)
}
func (dst *ScanAudioResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanAudioResp.Merge(dst, src)
}
func (m *ScanAudioResp) XXX_Size() int {
	return xxx_messageInfo_ScanAudioResp.Size(m)
}
func (m *ScanAudioResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanAudioResp.DiscardUnknown(m)
}

var xxx_messageInfo_ScanAudioResp proto.InternalMessageInfo

func (m *ScanAudioResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type QueryAudioTaskResultReq struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryAudioTaskResultReq) Reset()         { *m = QueryAudioTaskResultReq{} }
func (m *QueryAudioTaskResultReq) String() string { return proto.CompactTextString(m) }
func (*QueryAudioTaskResultReq) ProtoMessage()    {}
func (*QueryAudioTaskResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_8f7093b57ce30354, []int{6}
}
func (m *QueryAudioTaskResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryAudioTaskResultReq.Unmarshal(m, b)
}
func (m *QueryAudioTaskResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryAudioTaskResultReq.Marshal(b, m, deterministic)
}
func (dst *QueryAudioTaskResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryAudioTaskResultReq.Merge(dst, src)
}
func (m *QueryAudioTaskResultReq) XXX_Size() int {
	return xxx_messageInfo_QueryAudioTaskResultReq.Size(m)
}
func (m *QueryAudioTaskResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryAudioTaskResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryAudioTaskResultReq proto.InternalMessageInfo

func (m *QueryAudioTaskResultReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type QueryAudioTaskResultResp struct {
	TaskStatus           TaskStatus `protobuf:"varint,1,opt,name=task_status,json=taskStatus,proto3,enum=cybros.arbiter.v2.TaskStatus" json:"task_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *QueryAudioTaskResultResp) Reset()         { *m = QueryAudioTaskResultResp{} }
func (m *QueryAudioTaskResultResp) String() string { return proto.CompactTextString(m) }
func (*QueryAudioTaskResultResp) ProtoMessage()    {}
func (*QueryAudioTaskResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_8f7093b57ce30354, []int{7}
}
func (m *QueryAudioTaskResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryAudioTaskResultResp.Unmarshal(m, b)
}
func (m *QueryAudioTaskResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryAudioTaskResultResp.Marshal(b, m, deterministic)
}
func (dst *QueryAudioTaskResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryAudioTaskResultResp.Merge(dst, src)
}
func (m *QueryAudioTaskResultResp) XXX_Size() int {
	return xxx_messageInfo_QueryAudioTaskResultResp.Size(m)
}
func (m *QueryAudioTaskResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryAudioTaskResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryAudioTaskResultResp proto.InternalMessageInfo

func (m *QueryAudioTaskResultResp) GetTaskStatus() TaskStatus {
	if m != nil {
		return m.TaskStatus
	}
	return TaskStatus_SUCCESS
}

type AudioResultDetail struct {
	AudioText            string   `protobuf:"bytes,1,opt,name=audio_text,json=audioText,proto3" json:"audio_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioResultDetail) Reset()         { *m = AudioResultDetail{} }
func (m *AudioResultDetail) String() string { return proto.CompactTextString(m) }
func (*AudioResultDetail) ProtoMessage()    {}
func (*AudioResultDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_8f7093b57ce30354, []int{8}
}
func (m *AudioResultDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioResultDetail.Unmarshal(m, b)
}
func (m *AudioResultDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioResultDetail.Marshal(b, m, deterministic)
}
func (dst *AudioResultDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioResultDetail.Merge(dst, src)
}
func (m *AudioResultDetail) XXX_Size() int {
	return xxx_messageInfo_AudioResultDetail.Size(m)
}
func (m *AudioResultDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioResultDetail.DiscardUnknown(m)
}

var xxx_messageInfo_AudioResultDetail proto.InternalMessageInfo

func (m *AudioResultDetail) GetAudioText() string {
	if m != nil {
		return m.AudioText
	}
	return ""
}

func init() {
	proto.RegisterType((*AudioBinaryData)(nil), "cybros.arbiter.v2.AudioBinaryData")
	proto.RegisterType((*AudioBinaryData_AudioFormatInfo)(nil), "cybros.arbiter.v2.AudioBinaryData.AudioFormatInfo")
	proto.RegisterType((*ZegoStream)(nil), "cybros.arbiter.v2.ZegoStream")
	proto.RegisterType((*AudioStream)(nil), "cybros.arbiter.v2.AudioStream")
	proto.RegisterType((*AudioData)(nil), "cybros.arbiter.v2.AudioData")
	proto.RegisterType((*ScanAudioReq)(nil), "cybros.arbiter.v2.ScanAudioReq")
	proto.RegisterType((*ScanAudioResp)(nil), "cybros.arbiter.v2.ScanAudioResp")
	proto.RegisterType((*QueryAudioTaskResultReq)(nil), "cybros.arbiter.v2.QueryAudioTaskResultReq")
	proto.RegisterType((*QueryAudioTaskResultResp)(nil), "cybros.arbiter.v2.QueryAudioTaskResultResp")
	proto.RegisterType((*AudioResultDetail)(nil), "cybros.arbiter.v2.AudioResultDetail")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AudioClient is the client API for Audio service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AudioClient interface {
	AsyncScanAudio(ctx context.Context, in *ScanAudioReq, opts ...grpc.CallOption) (*ScanAudioResp, error)
	QueryAudioTaskResult(ctx context.Context, in *QueryAudioTaskResultReq, opts ...grpc.CallOption) (*QueryAudioTaskResultResp, error)
	CancelScanTask(ctx context.Context, in *CancelScanTaskReq, opts ...grpc.CallOption) (*CancelScanTaskResp, error)
}

type audioClient struct {
	cc *grpc.ClientConn
}

func NewAudioClient(cc *grpc.ClientConn) AudioClient {
	return &audioClient{cc}
}

func (c *audioClient) AsyncScanAudio(ctx context.Context, in *ScanAudioReq, opts ...grpc.CallOption) (*ScanAudioResp, error) {
	out := new(ScanAudioResp)
	err := c.cc.Invoke(ctx, "/cybros.arbiter.v2.Audio/AsyncScanAudio", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioClient) QueryAudioTaskResult(ctx context.Context, in *QueryAudioTaskResultReq, opts ...grpc.CallOption) (*QueryAudioTaskResultResp, error) {
	out := new(QueryAudioTaskResultResp)
	err := c.cc.Invoke(ctx, "/cybros.arbiter.v2.Audio/QueryAudioTaskResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioClient) CancelScanTask(ctx context.Context, in *CancelScanTaskReq, opts ...grpc.CallOption) (*CancelScanTaskResp, error) {
	out := new(CancelScanTaskResp)
	err := c.cc.Invoke(ctx, "/cybros.arbiter.v2.Audio/CancelScanTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AudioServer is the server API for Audio service.
type AudioServer interface {
	AsyncScanAudio(context.Context, *ScanAudioReq) (*ScanAudioResp, error)
	QueryAudioTaskResult(context.Context, *QueryAudioTaskResultReq) (*QueryAudioTaskResultResp, error)
	CancelScanTask(context.Context, *CancelScanTaskReq) (*CancelScanTaskResp, error)
}

func RegisterAudioServer(s *grpc.Server, srv AudioServer) {
	s.RegisterService(&_Audio_serviceDesc, srv)
}

func _Audio_AsyncScanAudio_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanAudioReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioServer).AsyncScanAudio(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cybros.arbiter.v2.Audio/AsyncScanAudio",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioServer).AsyncScanAudio(ctx, req.(*ScanAudioReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Audio_QueryAudioTaskResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryAudioTaskResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioServer).QueryAudioTaskResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cybros.arbiter.v2.Audio/QueryAudioTaskResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioServer).QueryAudioTaskResult(ctx, req.(*QueryAudioTaskResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Audio_CancelScanTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelScanTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioServer).CancelScanTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cybros.arbiter.v2.Audio/CancelScanTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioServer).CancelScanTask(ctx, req.(*CancelScanTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Audio_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cybros.arbiter.v2.Audio",
	HandlerType: (*AudioServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AsyncScanAudio",
			Handler:    _Audio_AsyncScanAudio_Handler,
		},
		{
			MethodName: "QueryAudioTaskResult",
			Handler:    _Audio_QueryAudioTaskResult_Handler,
		},
		{
			MethodName: "CancelScanTask",
			Handler:    _Audio_CancelScanTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cybros/arbiter/v2/audio.proto",
}

func init() {
	proto.RegisterFile("cybros/arbiter/v2/audio.proto", fileDescriptor_audio_8f7093b57ce30354)
}

var fileDescriptor_audio_8f7093b57ce30354 = []byte{
	// 693 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x54, 0xdd, 0x6a, 0x1a, 0x4d,
	0x18, 0x8e, 0xe6, 0x47, 0x7d, 0xd7, 0xe4, 0x23, 0x43, 0xf8, 0xe2, 0x67, 0xbe, 0x24, 0xb2, 0xb4,
	0x10, 0x5a, 0x50, 0xd8, 0x10, 0x5a, 0x28, 0x94, 0x46, 0xd3, 0x12, 0x0f, 0x7a, 0xd0, 0x31, 0x47,
	0x81, 0xd6, 0x8e, 0xeb, 0x28, 0x8b, 0xeb, 0xce, 0x76, 0xe7, 0x55, 0x34, 0xf7, 0xd0, 0x3b, 0xe9,
	0x35, 0xf4, 0x62, 0x7a, 0x25, 0x65, 0x7e, 0x5c, 0x25, 0xae, 0x6d, 0xce, 0x7c, 0xe6, 0x7d, 0x9e,
	0xe7, 0xfd, 0x75, 0xe1, 0xd4, 0x9f, 0xf7, 0x12, 0x21, 0x1b, 0x2c, 0xe9, 0x05, 0xc8, 0x93, 0xc6,
	0xd4, 0x6b, 0xb0, 0x49, 0x3f, 0x10, 0xf5, 0x38, 0x11, 0x28, 0xc8, 0xa1, 0x09, 0xd7, 0x6d, 0xb8,
	0x3e, 0xf5, 0xaa, 0x67, 0xeb, 0x8a, 0x61, 0x28, 0x7a, 0x2c, 0x34, 0x92, 0x6a, 0x6d, 0x3d, 0xee,
	0xb3, 0x30, 0xec, 0x31, 0x7f, 0x64, 0x18, 0xee, 0xaf, 0x1c, 0xfc, 0x73, 0xad, 0x92, 0x34, 0x83,
	0x88, 0x25, 0xf3, 0x1b, 0x86, 0x8c, 0x74, 0xc0, 0x19, 0x88, 0x64, 0xcc, 0xb0, 0x1b, 0x44, 0x03,
	0x51, 0xc9, 0xd5, 0x72, 0x17, 0x8e, 0xe7, 0xd5, 0xd7, 0xd2, 0xd7, 0x1f, 0x09, 0x0d, 0xfe, 0xa0,
	0xa5, 0xed, 0x68, 0x20, 0x28, 0x0c, 0xd2, 0xdf, 0xa4, 0x02, 0x05, 0x5f, 0x44, 0xc8, 0x23, 0xac,
	0xe4, 0x6b, 0xb9, 0x8b, 0x32, 0x5d, 0xc0, 0xea, 0x57, 0x5b, 0xc1, 0x52, 0x48, 0xfe, 0x85, 0x3d,
	0x23, 0xd5, 0xc9, 0x4b, 0xd4, 0x22, 0x72, 0x0e, 0x8e, 0x64, 0xe3, 0x38, 0xe4, 0xdd, 0x84, 0x21,
	0xd7, 0x46, 0xfb, 0x14, 0xcc, 0x13, 0x65, 0xc8, 0xc9, 0x11, 0xec, 0x62, 0xc2, 0xfc, 0x51, 0x65,
	0x5b, 0x87, 0x0c, 0x70, 0x67, 0x00, 0xf7, 0x7c, 0x28, 0x3a, 0x98, 0x70, 0x36, 0x26, 0xff, 0x41,
	0x11, 0xc5, 0x88, 0x47, 0xdd, 0xa0, 0x6f, 0xed, 0x0b, 0x1a, 0xb7, 0xfb, 0xe4, 0x04, 0x4a, 0x52,
	0x93, 0x54, 0x2c, 0xaf, 0x63, 0x45, 0xf3, 0xd0, 0xee, 0x6b, 0x1d, 0x97, 0xd8, 0xe5, 0xd1, 0x54,
	0xdb, 0x17, 0x69, 0x41, 0xe1, 0xf7, 0xd1, 0x94, 0x1c, 0x43, 0x21, 0x11, 0x42, 0xab, 0x76, 0x4c,
	0xc1, 0x0a, 0xb6, 0xfb, 0xee, 0x17, 0x70, 0x74, 0x6f, 0x36, 0xf5, 0x3b, 0x70, 0x1e, 0xf8, 0x50,
	0x74, 0x8d, 0xa7, 0x9d, 0xec, 0x69, 0xc6, 0x64, 0x97, 0xe5, 0xde, 0x6e, 0x51, 0x78, 0x48, 0x51,
	0x73, 0x1f, 0x1c, 0x5b, 0x21, 0xce, 0x63, 0xee, 0x7e, 0xcf, 0x43, 0x49, 0x27, 0xd0, 0x8b, 0x7b,
	0x05, 0xc5, 0x31, 0x47, 0xd6, 0x67, 0xc8, 0xac, 0xf7, 0x49, 0x86, 0xf7, 0x47, 0x4b, 0xa1, 0x29,
	0x99, 0x9c, 0x02, 0xe8, 0x4b, 0xeb, 0x22, 0x9f, 0x61, 0x65, 0x57, 0xb7, 0x50, 0xd2, 0x2f, 0x77,
	0x7c, 0x86, 0x84, 0xc0, 0xf6, 0x24, 0x09, 0xcd, 0x40, 0x6e, 0xb7, 0xa8, 0x02, 0xe4, 0x1a, 0x0c,
	0xa1, 0xdb, 0x0b, 0x22, 0x3d, 0x0e, 0xc7, 0x73, 0xff, 0x7e, 0x22, 0xb7, 0x5b, 0xb4, 0xc8, 0xec,
	0x13, 0x69, 0x41, 0xd9, 0x58, 0xd8, 0x71, 0xec, 0x68, 0x97, 0xb3, 0x4d, 0x2e, 0xe9, 0x3c, 0x1c,
	0xb6, 0x84, 0xcd, 0xf2, 0xa2, 0x74, 0xd5, 0x88, 0xfb, 0x33, 0x07, 0xe5, 0x8e, 0xcf, 0x22, 0x2d,
	0xa0, 0xfc, 0x1b, 0x79, 0x6d, 0xcf, 0x6e, 0x86, 0x76, 0x22, 0x59, 0xf6, 0x77, 0x4c, 0x8e, 0x5a,
	0x86, 0x45, 0x17, 0x74, 0xf2, 0x66, 0xd5, 0x58, 0xf7, 0xee, 0x78, 0xff, 0x6f, 0xaa, 0x4d, 0xf5,
	0x66, 0x27, 0xb6, 0xd8, 0xc4, 0xe2, 0x8f, 0x66, 0x87, 0x93, 0xb5, 0x89, 0x96, 0xa5, 0xd0, 0x94,
	0xec, 0x5e, 0xc0, 0xfe, 0x4a, 0xfd, 0x32, 0x56, 0xa7, 0x85, 0x4c, 0x8e, 0x96, 0xc7, 0xba, 0xa7,
	0x60, 0xbb, 0xef, 0x7a, 0x70, 0xfc, 0x69, 0xc2, 0x93, 0xb9, 0xa6, 0xaa, 0x0e, 0x28, 0x97, 0x93,
	0x10, 0x55, 0xd3, 0x1b, 0x35, 0xf7, 0x50, 0xc9, 0xd6, 0xc8, 0x98, 0xbc, 0x05, 0x47, 0x8b, 0x24,
	0x32, 0x9c, 0x48, 0x2d, 0x3c, 0xc8, 0xbc, 0x4d, 0xa5, 0xeb, 0x68, 0x12, 0x05, 0x4c, 0x7f, 0xbb,
	0x1e, 0x1c, 0x2e, 0xaa, 0x9e, 0x84, 0x78, 0xc3, 0x91, 0x05, 0xe1, 0xa3, 0xc3, 0xca, 0x3d, 0x3a,
	0x2c, 0xef, 0x47, 0x1e, 0x76, 0xb5, 0x88, 0x74, 0xe0, 0xe0, 0x5a, 0xce, 0x23, 0x3f, 0x6d, 0x9e,
	0x9c, 0x67, 0xa4, 0x5e, 0x5d, 0x6d, 0xb5, 0xf6, 0x67, 0x82, 0x8c, 0x89, 0x80, 0xa3, 0xac, 0x76,
	0xc9, 0x8b, 0x0c, 0xe5, 0x86, 0x59, 0x56, 0x5f, 0x3e, 0x99, 0x2b, 0x63, 0xf2, 0x19, 0x0e, 0x5a,
	0x2c, 0xf2, 0x79, 0xa8, 0xea, 0x50, 0x31, 0xf2, 0x2c, 0x73, 0xed, 0xab, 0x14, 0x95, 0xe4, 0xf9,
	0x13, 0x58, 0x32, 0x6e, 0x5e, 0xdd, 0x5f, 0x0e, 0x45, 0xc8, 0xa2, 0x61, 0xfd, 0xca, 0x43, 0xac,
	0xfb, 0x62, 0xdc, 0xd0, 0x5f, 0x71, 0x5f, 0x84, 0x0d, 0xc9, 0x93, 0x69, 0xe0, 0x73, 0xd9, 0x58,
	0xfb, 0xe4, 0xf7, 0xf6, 0x34, 0xe9, 0xf2, 0x77, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf0, 0x02, 0xa5,
	0x59, 0x60, 0x06, 0x00, 0x00,
}
