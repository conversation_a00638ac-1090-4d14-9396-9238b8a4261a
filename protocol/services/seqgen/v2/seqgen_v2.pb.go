// Code generated by protoc-gen-go. DO NOT EDIT.
// source: seqgen/v2/seqgen_v2.proto

package seqgen // import "golang.52tt.com/protocol/services/seqgen/v2"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GenerateSeqReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Namespace            string   `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Key                  string   `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	Incr                 uint32   `protobuf:"varint,4,opt,name=incr,proto3" json:"incr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenerateSeqReq) Reset()         { *m = GenerateSeqReq{} }
func (m *GenerateSeqReq) String() string { return proto.CompactTextString(m) }
func (*GenerateSeqReq) ProtoMessage()    {}
func (*GenerateSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{0}
}
func (m *GenerateSeqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateSeqReq.Unmarshal(m, b)
}
func (m *GenerateSeqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateSeqReq.Marshal(b, m, deterministic)
}
func (dst *GenerateSeqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateSeqReq.Merge(dst, src)
}
func (m *GenerateSeqReq) XXX_Size() int {
	return xxx_messageInfo_GenerateSeqReq.Size(m)
}
func (m *GenerateSeqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateSeqReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateSeqReq proto.InternalMessageInfo

func (m *GenerateSeqReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GenerateSeqReq) GetNamespace() string {
	if m != nil {
		return m.Namespace
	}
	return ""
}

func (m *GenerateSeqReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *GenerateSeqReq) GetIncr() uint32 {
	if m != nil {
		return m.Incr
	}
	return 0
}

type GenerateSeqRsp struct {
	Seq                  uint64   `protobuf:"varint,1,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenerateSeqRsp) Reset()         { *m = GenerateSeqRsp{} }
func (m *GenerateSeqRsp) String() string { return proto.CompactTextString(m) }
func (*GenerateSeqRsp) ProtoMessage()    {}
func (*GenerateSeqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{1}
}
func (m *GenerateSeqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateSeqRsp.Unmarshal(m, b)
}
func (m *GenerateSeqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateSeqRsp.Marshal(b, m, deterministic)
}
func (dst *GenerateSeqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateSeqRsp.Merge(dst, src)
}
func (m *GenerateSeqRsp) XXX_Size() int {
	return xxx_messageInfo_GenerateSeqRsp.Size(m)
}
func (m *GenerateSeqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateSeqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateSeqRsp proto.InternalMessageInfo

func (m *GenerateSeqRsp) GetSeq() uint64 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type BatchGenerateSeqReq struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	Namespace            string   `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Key                  string   `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	Incr                 uint32   `protobuf:"varint,4,opt,name=incr,proto3" json:"incr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGenerateSeqReq) Reset()         { *m = BatchGenerateSeqReq{} }
func (m *BatchGenerateSeqReq) String() string { return proto.CompactTextString(m) }
func (*BatchGenerateSeqReq) ProtoMessage()    {}
func (*BatchGenerateSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{2}
}
func (m *BatchGenerateSeqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGenerateSeqReq.Unmarshal(m, b)
}
func (m *BatchGenerateSeqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGenerateSeqReq.Marshal(b, m, deterministic)
}
func (dst *BatchGenerateSeqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGenerateSeqReq.Merge(dst, src)
}
func (m *BatchGenerateSeqReq) XXX_Size() int {
	return xxx_messageInfo_BatchGenerateSeqReq.Size(m)
}
func (m *BatchGenerateSeqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGenerateSeqReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGenerateSeqReq proto.InternalMessageInfo

func (m *BatchGenerateSeqReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *BatchGenerateSeqReq) GetNamespace() string {
	if m != nil {
		return m.Namespace
	}
	return ""
}

func (m *BatchGenerateSeqReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *BatchGenerateSeqReq) GetIncr() uint32 {
	if m != nil {
		return m.Incr
	}
	return 0
}

type BatchGenerateSeqRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGenerateSeqRsp) Reset()         { *m = BatchGenerateSeqRsp{} }
func (m *BatchGenerateSeqRsp) String() string { return proto.CompactTextString(m) }
func (*BatchGenerateSeqRsp) ProtoMessage()    {}
func (*BatchGenerateSeqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{3}
}
func (m *BatchGenerateSeqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGenerateSeqRsp.Unmarshal(m, b)
}
func (m *BatchGenerateSeqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGenerateSeqRsp.Marshal(b, m, deterministic)
}
func (dst *BatchGenerateSeqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGenerateSeqRsp.Merge(dst, src)
}
func (m *BatchGenerateSeqRsp) XXX_Size() int {
	return xxx_messageInfo_BatchGenerateSeqRsp.Size(m)
}
func (m *BatchGenerateSeqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGenerateSeqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGenerateSeqRsp proto.InternalMessageInfo

type RetrieveSeqReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Namespace            string   `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Key                  string   `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RetrieveSeqReq) Reset()         { *m = RetrieveSeqReq{} }
func (m *RetrieveSeqReq) String() string { return proto.CompactTextString(m) }
func (*RetrieveSeqReq) ProtoMessage()    {}
func (*RetrieveSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{4}
}
func (m *RetrieveSeqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RetrieveSeqReq.Unmarshal(m, b)
}
func (m *RetrieveSeqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RetrieveSeqReq.Marshal(b, m, deterministic)
}
func (dst *RetrieveSeqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RetrieveSeqReq.Merge(dst, src)
}
func (m *RetrieveSeqReq) XXX_Size() int {
	return xxx_messageInfo_RetrieveSeqReq.Size(m)
}
func (m *RetrieveSeqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RetrieveSeqReq.DiscardUnknown(m)
}

var xxx_messageInfo_RetrieveSeqReq proto.InternalMessageInfo

func (m *RetrieveSeqReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RetrieveSeqReq) GetNamespace() string {
	if m != nil {
		return m.Namespace
	}
	return ""
}

func (m *RetrieveSeqReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type RetrieveSeqRsp struct {
	Seq                  uint64   `protobuf:"varint,1,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RetrieveSeqRsp) Reset()         { *m = RetrieveSeqRsp{} }
func (m *RetrieveSeqRsp) String() string { return proto.CompactTextString(m) }
func (*RetrieveSeqRsp) ProtoMessage()    {}
func (*RetrieveSeqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{5}
}
func (m *RetrieveSeqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RetrieveSeqRsp.Unmarshal(m, b)
}
func (m *RetrieveSeqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RetrieveSeqRsp.Marshal(b, m, deterministic)
}
func (dst *RetrieveSeqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RetrieveSeqRsp.Merge(dst, src)
}
func (m *RetrieveSeqRsp) XXX_Size() int {
	return xxx_messageInfo_RetrieveSeqRsp.Size(m)
}
func (m *RetrieveSeqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RetrieveSeqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RetrieveSeqRsp proto.InternalMessageInfo

func (m *RetrieveSeqRsp) GetSeq() uint64 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type BatchRetrieveSeqReq struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	Namespace            string   `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Key                  string   `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchRetrieveSeqReq) Reset()         { *m = BatchRetrieveSeqReq{} }
func (m *BatchRetrieveSeqReq) String() string { return proto.CompactTextString(m) }
func (*BatchRetrieveSeqReq) ProtoMessage()    {}
func (*BatchRetrieveSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{6}
}
func (m *BatchRetrieveSeqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchRetrieveSeqReq.Unmarshal(m, b)
}
func (m *BatchRetrieveSeqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchRetrieveSeqReq.Marshal(b, m, deterministic)
}
func (dst *BatchRetrieveSeqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRetrieveSeqReq.Merge(dst, src)
}
func (m *BatchRetrieveSeqReq) XXX_Size() int {
	return xxx_messageInfo_BatchRetrieveSeqReq.Size(m)
}
func (m *BatchRetrieveSeqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRetrieveSeqReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRetrieveSeqReq proto.InternalMessageInfo

func (m *BatchRetrieveSeqReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *BatchRetrieveSeqReq) GetNamespace() string {
	if m != nil {
		return m.Namespace
	}
	return ""
}

func (m *BatchRetrieveSeqReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type BatchRetrieveSeqRsp struct {
	SeqMap               map[uint32]uint64 `protobuf:"bytes,1,rep,name=seq_map,json=seqMap,proto3" json:"seq_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchRetrieveSeqRsp) Reset()         { *m = BatchRetrieveSeqRsp{} }
func (m *BatchRetrieveSeqRsp) String() string { return proto.CompactTextString(m) }
func (*BatchRetrieveSeqRsp) ProtoMessage()    {}
func (*BatchRetrieveSeqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{7}
}
func (m *BatchRetrieveSeqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchRetrieveSeqRsp.Unmarshal(m, b)
}
func (m *BatchRetrieveSeqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchRetrieveSeqRsp.Marshal(b, m, deterministic)
}
func (dst *BatchRetrieveSeqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRetrieveSeqRsp.Merge(dst, src)
}
func (m *BatchRetrieveSeqRsp) XXX_Size() int {
	return xxx_messageInfo_BatchRetrieveSeqRsp.Size(m)
}
func (m *BatchRetrieveSeqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRetrieveSeqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRetrieveSeqRsp proto.InternalMessageInfo

func (m *BatchRetrieveSeqRsp) GetSeqMap() map[uint32]uint64 {
	if m != nil {
		return m.SeqMap
	}
	return nil
}

type RetrieveMultiSeqReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Namespace            string   `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Keys                 []string `protobuf:"bytes,3,rep,name=keys,proto3" json:"keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RetrieveMultiSeqReq) Reset()         { *m = RetrieveMultiSeqReq{} }
func (m *RetrieveMultiSeqReq) String() string { return proto.CompactTextString(m) }
func (*RetrieveMultiSeqReq) ProtoMessage()    {}
func (*RetrieveMultiSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{8}
}
func (m *RetrieveMultiSeqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RetrieveMultiSeqReq.Unmarshal(m, b)
}
func (m *RetrieveMultiSeqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RetrieveMultiSeqReq.Marshal(b, m, deterministic)
}
func (dst *RetrieveMultiSeqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RetrieveMultiSeqReq.Merge(dst, src)
}
func (m *RetrieveMultiSeqReq) XXX_Size() int {
	return xxx_messageInfo_RetrieveMultiSeqReq.Size(m)
}
func (m *RetrieveMultiSeqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RetrieveMultiSeqReq.DiscardUnknown(m)
}

var xxx_messageInfo_RetrieveMultiSeqReq proto.InternalMessageInfo

func (m *RetrieveMultiSeqReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RetrieveMultiSeqReq) GetNamespace() string {
	if m != nil {
		return m.Namespace
	}
	return ""
}

func (m *RetrieveMultiSeqReq) GetKeys() []string {
	if m != nil {
		return m.Keys
	}
	return nil
}

type RetrieveMultiSeqRsp struct {
	SeqMap               map[string]uint64 `protobuf:"bytes,1,rep,name=seq_map,json=seqMap,proto3" json:"seq_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RetrieveMultiSeqRsp) Reset()         { *m = RetrieveMultiSeqRsp{} }
func (m *RetrieveMultiSeqRsp) String() string { return proto.CompactTextString(m) }
func (*RetrieveMultiSeqRsp) ProtoMessage()    {}
func (*RetrieveMultiSeqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{9}
}
func (m *RetrieveMultiSeqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RetrieveMultiSeqRsp.Unmarshal(m, b)
}
func (m *RetrieveMultiSeqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RetrieveMultiSeqRsp.Marshal(b, m, deterministic)
}
func (dst *RetrieveMultiSeqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RetrieveMultiSeqRsp.Merge(dst, src)
}
func (m *RetrieveMultiSeqRsp) XXX_Size() int {
	return xxx_messageInfo_RetrieveMultiSeqRsp.Size(m)
}
func (m *RetrieveMultiSeqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RetrieveMultiSeqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RetrieveMultiSeqRsp proto.InternalMessageInfo

func (m *RetrieveMultiSeqRsp) GetSeqMap() map[string]uint64 {
	if m != nil {
		return m.SeqMap
	}
	return nil
}

type SetSeqReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Namespace            string   `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Key                  string   `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	Seq                  uint64   `protobuf:"varint,4,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSeqReq) Reset()         { *m = SetSeqReq{} }
func (m *SetSeqReq) String() string { return proto.CompactTextString(m) }
func (*SetSeqReq) ProtoMessage()    {}
func (*SetSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{10}
}
func (m *SetSeqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSeqReq.Unmarshal(m, b)
}
func (m *SetSeqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSeqReq.Marshal(b, m, deterministic)
}
func (dst *SetSeqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSeqReq.Merge(dst, src)
}
func (m *SetSeqReq) XXX_Size() int {
	return xxx_messageInfo_SetSeqReq.Size(m)
}
func (m *SetSeqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSeqReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetSeqReq proto.InternalMessageInfo

func (m *SetSeqReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetSeqReq) GetNamespace() string {
	if m != nil {
		return m.Namespace
	}
	return ""
}

func (m *SetSeqReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *SetSeqReq) GetSeq() uint64 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type SetSeqRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSeqRsp) Reset()         { *m = SetSeqRsp{} }
func (m *SetSeqRsp) String() string { return proto.CompactTextString(m) }
func (*SetSeqRsp) ProtoMessage()    {}
func (*SetSeqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{11}
}
func (m *SetSeqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSeqRsp.Unmarshal(m, b)
}
func (m *SetSeqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSeqRsp.Marshal(b, m, deterministic)
}
func (dst *SetSeqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSeqRsp.Merge(dst, src)
}
func (m *SetSeqRsp) XXX_Size() int {
	return xxx_messageInfo_SetSeqRsp.Size(m)
}
func (m *SetSeqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSeqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetSeqRsp proto.InternalMessageInfo

type GenSnowflakeReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenSnowflakeReq) Reset()         { *m = GenSnowflakeReq{} }
func (m *GenSnowflakeReq) String() string { return proto.CompactTextString(m) }
func (*GenSnowflakeReq) ProtoMessage()    {}
func (*GenSnowflakeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{12}
}
func (m *GenSnowflakeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenSnowflakeReq.Unmarshal(m, b)
}
func (m *GenSnowflakeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenSnowflakeReq.Marshal(b, m, deterministic)
}
func (dst *GenSnowflakeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenSnowflakeReq.Merge(dst, src)
}
func (m *GenSnowflakeReq) XXX_Size() int {
	return xxx_messageInfo_GenSnowflakeReq.Size(m)
}
func (m *GenSnowflakeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenSnowflakeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenSnowflakeReq proto.InternalMessageInfo

type GenSnowflakeRsp struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenSnowflakeRsp) Reset()         { *m = GenSnowflakeRsp{} }
func (m *GenSnowflakeRsp) String() string { return proto.CompactTextString(m) }
func (*GenSnowflakeRsp) ProtoMessage()    {}
func (*GenSnowflakeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{13}
}
func (m *GenSnowflakeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenSnowflakeRsp.Unmarshal(m, b)
}
func (m *GenSnowflakeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenSnowflakeRsp.Marshal(b, m, deterministic)
}
func (dst *GenSnowflakeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenSnowflakeRsp.Merge(dst, src)
}
func (m *GenSnowflakeRsp) XXX_Size() int {
	return xxx_messageInfo_GenSnowflakeRsp.Size(m)
}
func (m *GenSnowflakeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenSnowflakeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GenSnowflakeRsp proto.InternalMessageInfo

func (m *GenSnowflakeRsp) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DecodeSnowflakeReq struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecodeSnowflakeReq) Reset()         { *m = DecodeSnowflakeReq{} }
func (m *DecodeSnowflakeReq) String() string { return proto.CompactTextString(m) }
func (*DecodeSnowflakeReq) ProtoMessage()    {}
func (*DecodeSnowflakeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{14}
}
func (m *DecodeSnowflakeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecodeSnowflakeReq.Unmarshal(m, b)
}
func (m *DecodeSnowflakeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecodeSnowflakeReq.Marshal(b, m, deterministic)
}
func (dst *DecodeSnowflakeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecodeSnowflakeReq.Merge(dst, src)
}
func (m *DecodeSnowflakeReq) XXX_Size() int {
	return xxx_messageInfo_DecodeSnowflakeReq.Size(m)
}
func (m *DecodeSnowflakeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DecodeSnowflakeReq.DiscardUnknown(m)
}

var xxx_messageInfo_DecodeSnowflakeReq proto.InternalMessageInfo

func (m *DecodeSnowflakeReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DecodeSnowflakeRsp struct {
	Timestamp            int64    `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	WorkerId             int64    `protobuf:"varint,2,opt,name=workerId,proto3" json:"workerId,omitempty"`
	Sequence             int64    `protobuf:"varint,3,opt,name=sequence,proto3" json:"sequence,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecodeSnowflakeRsp) Reset()         { *m = DecodeSnowflakeRsp{} }
func (m *DecodeSnowflakeRsp) String() string { return proto.CompactTextString(m) }
func (*DecodeSnowflakeRsp) ProtoMessage()    {}
func (*DecodeSnowflakeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_seqgen_v2_d679f0c9868f295c, []int{15}
}
func (m *DecodeSnowflakeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecodeSnowflakeRsp.Unmarshal(m, b)
}
func (m *DecodeSnowflakeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecodeSnowflakeRsp.Marshal(b, m, deterministic)
}
func (dst *DecodeSnowflakeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecodeSnowflakeRsp.Merge(dst, src)
}
func (m *DecodeSnowflakeRsp) XXX_Size() int {
	return xxx_messageInfo_DecodeSnowflakeRsp.Size(m)
}
func (m *DecodeSnowflakeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_DecodeSnowflakeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_DecodeSnowflakeRsp proto.InternalMessageInfo

func (m *DecodeSnowflakeRsp) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *DecodeSnowflakeRsp) GetWorkerId() int64 {
	if m != nil {
		return m.WorkerId
	}
	return 0
}

func (m *DecodeSnowflakeRsp) GetSequence() int64 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func init() {
	proto.RegisterType((*GenerateSeqReq)(nil), "seqgen.GenerateSeqReq")
	proto.RegisterType((*GenerateSeqRsp)(nil), "seqgen.GenerateSeqRsp")
	proto.RegisterType((*BatchGenerateSeqReq)(nil), "seqgen.BatchGenerateSeqReq")
	proto.RegisterType((*BatchGenerateSeqRsp)(nil), "seqgen.BatchGenerateSeqRsp")
	proto.RegisterType((*RetrieveSeqReq)(nil), "seqgen.RetrieveSeqReq")
	proto.RegisterType((*RetrieveSeqRsp)(nil), "seqgen.RetrieveSeqRsp")
	proto.RegisterType((*BatchRetrieveSeqReq)(nil), "seqgen.BatchRetrieveSeqReq")
	proto.RegisterType((*BatchRetrieveSeqRsp)(nil), "seqgen.BatchRetrieveSeqRsp")
	proto.RegisterMapType((map[uint32]uint64)(nil), "seqgen.BatchRetrieveSeqRsp.SeqMapEntry")
	proto.RegisterType((*RetrieveMultiSeqReq)(nil), "seqgen.RetrieveMultiSeqReq")
	proto.RegisterType((*RetrieveMultiSeqRsp)(nil), "seqgen.RetrieveMultiSeqRsp")
	proto.RegisterMapType((map[string]uint64)(nil), "seqgen.RetrieveMultiSeqRsp.SeqMapEntry")
	proto.RegisterType((*SetSeqReq)(nil), "seqgen.SetSeqReq")
	proto.RegisterType((*SetSeqRsp)(nil), "seqgen.SetSeqRsp")
	proto.RegisterType((*GenSnowflakeReq)(nil), "seqgen.GenSnowflakeReq")
	proto.RegisterType((*GenSnowflakeRsp)(nil), "seqgen.GenSnowflakeRsp")
	proto.RegisterType((*DecodeSnowflakeReq)(nil), "seqgen.DecodeSnowflakeReq")
	proto.RegisterType((*DecodeSnowflakeRsp)(nil), "seqgen.DecodeSnowflakeRsp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SeqGenClient is the client API for SeqGen service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SeqGenClient interface {
	// 严格递增的序列号
	// Generate a new sequence
	GenerateSeq(ctx context.Context, in *GenerateSeqReq, opts ...grpc.CallOption) (*GenerateSeqRsp, error)
	BatchGenerateSeq(ctx context.Context, in *BatchGenerateSeqReq, opts ...grpc.CallOption) (*BatchGenerateSeqRsp, error)
	// Retrieve current sequence
	RetrieveSeq(ctx context.Context, in *RetrieveSeqReq, opts ...grpc.CallOption) (*RetrieveSeqRsp, error)
	BatchRetrieveSeq(ctx context.Context, in *BatchRetrieveSeqReq, opts ...grpc.CallOption) (*BatchRetrieveSeqRsp, error)
	RetrieveMultiSeq(ctx context.Context, in *RetrieveMultiSeqReq, opts ...grpc.CallOption) (*RetrieveMultiSeqRsp, error)
	// Set a new sequence
	SetSeq(ctx context.Context, in *SetSeqReq, opts ...grpc.CallOption) (*SetSeqRsp, error)
	// 趋势递增的全局唯一序列号
	// 开箱即用 可以通过id溯源
	GenSnowflakeId(ctx context.Context, in *GenSnowflakeReq, opts ...grpc.CallOption) (*GenSnowflakeRsp, error)
	DecodeSnowflakeId(ctx context.Context, in *DecodeSnowflakeReq, opts ...grpc.CallOption) (*DecodeSnowflakeRsp, error)
}

type seqGenClient struct {
	cc *grpc.ClientConn
}

func NewSeqGenClient(cc *grpc.ClientConn) SeqGenClient {
	return &seqGenClient{cc}
}

func (c *seqGenClient) GenerateSeq(ctx context.Context, in *GenerateSeqReq, opts ...grpc.CallOption) (*GenerateSeqRsp, error) {
	out := new(GenerateSeqRsp)
	err := c.cc.Invoke(ctx, "/seqgen.SeqGen/GenerateSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) BatchGenerateSeq(ctx context.Context, in *BatchGenerateSeqReq, opts ...grpc.CallOption) (*BatchGenerateSeqRsp, error) {
	out := new(BatchGenerateSeqRsp)
	err := c.cc.Invoke(ctx, "/seqgen.SeqGen/BatchGenerateSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) RetrieveSeq(ctx context.Context, in *RetrieveSeqReq, opts ...grpc.CallOption) (*RetrieveSeqRsp, error) {
	out := new(RetrieveSeqRsp)
	err := c.cc.Invoke(ctx, "/seqgen.SeqGen/RetrieveSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) BatchRetrieveSeq(ctx context.Context, in *BatchRetrieveSeqReq, opts ...grpc.CallOption) (*BatchRetrieveSeqRsp, error) {
	out := new(BatchRetrieveSeqRsp)
	err := c.cc.Invoke(ctx, "/seqgen.SeqGen/BatchRetrieveSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) RetrieveMultiSeq(ctx context.Context, in *RetrieveMultiSeqReq, opts ...grpc.CallOption) (*RetrieveMultiSeqRsp, error) {
	out := new(RetrieveMultiSeqRsp)
	err := c.cc.Invoke(ctx, "/seqgen.SeqGen/RetrieveMultiSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) SetSeq(ctx context.Context, in *SetSeqReq, opts ...grpc.CallOption) (*SetSeqRsp, error) {
	out := new(SetSeqRsp)
	err := c.cc.Invoke(ctx, "/seqgen.SeqGen/SetSeq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) GenSnowflakeId(ctx context.Context, in *GenSnowflakeReq, opts ...grpc.CallOption) (*GenSnowflakeRsp, error) {
	out := new(GenSnowflakeRsp)
	err := c.cc.Invoke(ctx, "/seqgen.SeqGen/GenSnowflakeId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *seqGenClient) DecodeSnowflakeId(ctx context.Context, in *DecodeSnowflakeReq, opts ...grpc.CallOption) (*DecodeSnowflakeRsp, error) {
	out := new(DecodeSnowflakeRsp)
	err := c.cc.Invoke(ctx, "/seqgen.SeqGen/DecodeSnowflakeId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SeqGenServer is the server API for SeqGen service.
type SeqGenServer interface {
	// 严格递增的序列号
	// Generate a new sequence
	GenerateSeq(context.Context, *GenerateSeqReq) (*GenerateSeqRsp, error)
	BatchGenerateSeq(context.Context, *BatchGenerateSeqReq) (*BatchGenerateSeqRsp, error)
	// Retrieve current sequence
	RetrieveSeq(context.Context, *RetrieveSeqReq) (*RetrieveSeqRsp, error)
	BatchRetrieveSeq(context.Context, *BatchRetrieveSeqReq) (*BatchRetrieveSeqRsp, error)
	RetrieveMultiSeq(context.Context, *RetrieveMultiSeqReq) (*RetrieveMultiSeqRsp, error)
	// Set a new sequence
	SetSeq(context.Context, *SetSeqReq) (*SetSeqRsp, error)
	// 趋势递增的全局唯一序列号
	// 开箱即用 可以通过id溯源
	GenSnowflakeId(context.Context, *GenSnowflakeReq) (*GenSnowflakeRsp, error)
	DecodeSnowflakeId(context.Context, *DecodeSnowflakeReq) (*DecodeSnowflakeRsp, error)
}

func RegisterSeqGenServer(s *grpc.Server, srv SeqGenServer) {
	s.RegisterService(&_SeqGen_serviceDesc, srv)
}

func _SeqGen_GenerateSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).GenerateSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/seqgen.SeqGen/GenerateSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).GenerateSeq(ctx, req.(*GenerateSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_BatchGenerateSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGenerateSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).BatchGenerateSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/seqgen.SeqGen/BatchGenerateSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).BatchGenerateSeq(ctx, req.(*BatchGenerateSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_RetrieveSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetrieveSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).RetrieveSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/seqgen.SeqGen/RetrieveSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).RetrieveSeq(ctx, req.(*RetrieveSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_BatchRetrieveSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRetrieveSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).BatchRetrieveSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/seqgen.SeqGen/BatchRetrieveSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).BatchRetrieveSeq(ctx, req.(*BatchRetrieveSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_RetrieveMultiSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetrieveMultiSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).RetrieveMultiSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/seqgen.SeqGen/RetrieveMultiSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).RetrieveMultiSeq(ctx, req.(*RetrieveMultiSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_SetSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).SetSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/seqgen.SeqGen/SetSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).SetSeq(ctx, req.(*SetSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_GenSnowflakeId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenSnowflakeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).GenSnowflakeId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/seqgen.SeqGen/GenSnowflakeId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).GenSnowflakeId(ctx, req.(*GenSnowflakeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SeqGen_DecodeSnowflakeId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecodeSnowflakeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SeqGenServer).DecodeSnowflakeId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/seqgen.SeqGen/DecodeSnowflakeId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SeqGenServer).DecodeSnowflakeId(ctx, req.(*DecodeSnowflakeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SeqGen_serviceDesc = grpc.ServiceDesc{
	ServiceName: "seqgen.SeqGen",
	HandlerType: (*SeqGenServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateSeq",
			Handler:    _SeqGen_GenerateSeq_Handler,
		},
		{
			MethodName: "BatchGenerateSeq",
			Handler:    _SeqGen_BatchGenerateSeq_Handler,
		},
		{
			MethodName: "RetrieveSeq",
			Handler:    _SeqGen_RetrieveSeq_Handler,
		},
		{
			MethodName: "BatchRetrieveSeq",
			Handler:    _SeqGen_BatchRetrieveSeq_Handler,
		},
		{
			MethodName: "RetrieveMultiSeq",
			Handler:    _SeqGen_RetrieveMultiSeq_Handler,
		},
		{
			MethodName: "SetSeq",
			Handler:    _SeqGen_SetSeq_Handler,
		},
		{
			MethodName: "GenSnowflakeId",
			Handler:    _SeqGen_GenSnowflakeId_Handler,
		},
		{
			MethodName: "DecodeSnowflakeId",
			Handler:    _SeqGen_DecodeSnowflakeId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "seqgen/v2/seqgen_v2.proto",
}

func init() {
	proto.RegisterFile("seqgen/v2/seqgen_v2.proto", fileDescriptor_seqgen_v2_d679f0c9868f295c)
}

var fileDescriptor_seqgen_v2_d679f0c9868f295c = []byte{
	// 611 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x55, 0x5d, 0x8f, 0xd2, 0x40,
	0x14, 0xdd, 0xd2, 0xca, 0xca, 0x90, 0x5d, 0x97, 0x59, 0x75, 0x6b, 0xf1, 0x01, 0x27, 0x26, 0xf2,
	0x54, 0x4c, 0xd5, 0xc4, 0x8f, 0x87, 0x35, 0x9b, 0x35, 0x64, 0x13, 0x31, 0x66, 0x78, 0x30, 0x31,
	0x51, 0x52, 0xdb, 0x2b, 0x56, 0x4a, 0x3b, 0xed, 0x0c, 0x6c, 0xf8, 0x1b, 0xfe, 0x23, 0xff, 0x99,
	0x99, 0x29, 0x94, 0x52, 0xca, 0x1a, 0x0d, 0x6f, 0xb7, 0x67, 0xee, 0x9c, 0x7b, 0xee, 0xed, 0xb9,
	0x2d, 0x7a, 0xc0, 0x21, 0x19, 0x43, 0xd4, 0x9b, 0x3b, 0xbd, 0x2c, 0x1a, 0xcd, 0x1d, 0x9b, 0xa5,
	0xb1, 0x88, 0x71, 0x3d, 0x03, 0x88, 0x8f, 0x8e, 0xfb, 0x10, 0x41, 0xea, 0x0a, 0x18, 0x42, 0x42,
	0x21, 0xc1, 0xc7, 0xa8, 0x16, 0xf8, 0xa6, 0xd6, 0xd1, 0xba, 0x47, 0xb4, 0x16, 0xf8, 0xf8, 0x21,
	0x6a, 0x44, 0xee, 0x14, 0x38, 0x73, 0x3d, 0x30, 0x6b, 0x1d, 0xad, 0xdb, 0xa0, 0x6b, 0x00, 0x9f,
	0x20, 0x7d, 0x02, 0x0b, 0x53, 0x57, 0xb8, 0x0c, 0x31, 0x46, 0x46, 0x10, 0x79, 0xa9, 0x69, 0x28,
	0x06, 0x15, 0x13, 0xb2, 0x59, 0x85, 0x33, 0x79, 0x8f, 0x43, 0xa2, 0xca, 0x18, 0x54, 0x86, 0x24,
	0x45, 0xa7, 0x17, 0xae, 0xf0, 0x7e, 0x94, 0xe4, 0x9c, 0xa1, 0xc3, 0xc0, 0x1f, 0x85, 0x01, 0x17,
	0xa6, 0xd6, 0xd1, 0xbb, 0x47, 0xb4, 0x1e, 0xf8, 0xef, 0x03, 0x2e, 0xf6, 0xa2, 0xeb, 0x5e, 0x45,
	0x4d, 0xce, 0xc8, 0x47, 0x74, 0x4c, 0x41, 0xa4, 0x01, 0xcc, 0xf7, 0x34, 0x14, 0x39, 0x80, 0x22,
	0x63, 0xe5, 0x00, 0xbe, 0x2e, 0xc5, 0x94, 0x4a, 0xef, 0x6b, 0x00, 0xe4, 0x97, 0x56, 0x51, 0x80,
	0x33, 0xfc, 0x16, 0x1d, 0x72, 0x48, 0x46, 0x53, 0x97, 0xa9, 0x02, 0x4d, 0xe7, 0x89, 0x9d, 0x99,
	0xc3, 0xae, 0xc8, 0xb6, 0x87, 0x90, 0x0c, 0x5c, 0xf6, 0x2e, 0x12, 0xe9, 0x82, 0x4a, 0x13, 0x0d,
	0x5c, 0x66, 0xbd, 0x42, 0xcd, 0x02, 0xbc, 0x2a, 0x9d, 0x4d, 0x4b, 0xcd, 0xfe, 0x2e, 0xba, 0x35,
	0x77, 0xc3, 0x59, 0x26, 0xd3, 0xa0, 0xd9, 0xc3, 0xeb, 0xda, 0x4b, 0x8d, 0x7c, 0x42, 0xa7, 0xab,
	0x02, 0x83, 0x59, 0x28, 0x82, 0xff, 0x9a, 0x37, 0x46, 0xc6, 0x04, 0x16, 0xdc, 0xd4, 0x3b, 0x7a,
	0xb7, 0x41, 0x55, 0xac, 0xba, 0xdd, 0x62, 0xbe, 0xb1, 0xdb, 0x8a, 0xec, 0x7f, 0xec, 0xb6, 0xf1,
	0xb7, 0x6e, 0xbf, 0xa0, 0xc6, 0x10, 0xc4, 0x9e, 0x16, 0x6d, 0xe9, 0x20, 0x63, 0xed, 0xa0, 0x66,
	0x4e, 0xcf, 0x19, 0x69, 0xa1, 0x3b, 0x7d, 0x88, 0x86, 0x51, 0x7c, 0xfd, 0x3d, 0x74, 0x27, 0x40,
	0x21, 0x21, 0x8f, 0x4a, 0x10, 0x67, 0x05, 0x11, 0xba, 0x14, 0x41, 0x1e, 0x23, 0x7c, 0x09, 0x5e,
	0xec, 0x43, 0xf1, 0xe2, 0x56, 0xd6, 0xcf, 0xed, 0x2c, 0xce, 0x64, 0x03, 0x22, 0x98, 0x02, 0x17,
	0xee, 0x94, 0x2d, 0x93, 0xd7, 0x00, 0xb6, 0xd0, 0xed, 0xeb, 0x38, 0x9d, 0x40, 0x7a, 0xe5, 0xab,
	0xee, 0x74, 0x9a, 0x3f, 0xcb, 0x33, 0x0e, 0xc9, 0x0c, 0x22, 0x0f, 0x54, 0x87, 0x3a, 0xcd, 0x9f,
	0x9d, 0xdf, 0x06, 0xaa, 0x0f, 0x21, 0xe9, 0x43, 0x84, 0xcf, 0x51, 0xb3, 0xb0, 0xa9, 0xf8, 0xfe,
	0xea, 0xcd, 0x6d, 0x7e, 0x32, 0xac, 0x4a, 0x9c, 0x33, 0x72, 0x80, 0x3f, 0xa0, 0x93, 0xf2, 0xbe,
	0xe3, 0xf6, 0x86, 0xdb, 0x4b, 0x54, 0xbb, 0x0f, 0x15, 0xdf, 0x39, 0x6a, 0x16, 0xd6, 0x63, 0x2d,
	0x68, 0x73, 0x85, 0xad, 0x4a, 0x7c, 0x43, 0x50, 0x91, 0xa5, 0xbd, 0x73, 0xfd, 0xb6, 0x04, 0x55,
	0xf1, 0x95, 0x6d, 0xbc, 0xe6, 0xab, 0x58, 0x34, 0xab, 0x7d, 0x83, 0xfb, 0xc9, 0x01, 0x7e, 0x2a,
	0x67, 0x2f, 0x1d, 0x85, 0x5b, 0xab, 0xc4, 0xdc, 0xc0, 0x56, 0x19, 0x52, 0x37, 0x2e, 0xd5, 0xa7,
	0x3e, 0xf7, 0xc5, 0x95, 0x8f, 0xcf, 0x0a, 0xaf, 0xa3, 0xe8, 0x2a, 0xab, 0xfa, 0x40, 0xb1, 0x0c,
	0x50, 0xab, 0x64, 0x30, 0xe9, 0x92, 0x55, 0xfe, 0xb6, 0x43, 0xad, 0x9d, 0x67, 0x92, 0xee, 0xe2,
	0xf9, 0x67, 0x67, 0x1c, 0x87, 0x6e, 0x34, 0xb6, 0x5f, 0x38, 0x42, 0xd8, 0x5e, 0x3c, 0xed, 0xa9,
	0xdf, 0xa0, 0x17, 0x87, 0x3d, 0x0e, 0xe9, 0x3c, 0xf0, 0x80, 0xf7, 0xf2, 0x9f, 0xe5, 0x9b, 0x2c,
	0xfa, 0x56, 0x57, 0x39, 0xcf, 0xfe, 0x04, 0x00, 0x00, 0xff, 0xff, 0x2e, 0x31, 0x8d, 0x05, 0x47,
	0x07, 0x00, 0x00,
}
