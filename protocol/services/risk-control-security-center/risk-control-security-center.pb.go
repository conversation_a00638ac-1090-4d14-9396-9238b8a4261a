// Code generated by protoc-gen-go. DO NOT EDIT.
// source: risk-control-security-center/risk-control-security-center.proto

package risk_control_security_center // import "golang.52tt.com/protocol/services/risk-control-security-center"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SecurityCenterPostInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostTitle            string   `protobuf:"bytes,2,opt,name=post_title,json=postTitle,proto3" json:"post_title,omitempty"`
	PostCoverUrl         string   `protobuf:"bytes,3,opt,name=post_cover_url,json=postCoverUrl,proto3" json:"post_cover_url,omitempty"`
	PostContentUrl       string   `protobuf:"bytes,4,opt,name=post_content_url,json=postContentUrl,proto3" json:"post_content_url,omitempty"`
	CreateAt             uint32   `protobuf:"varint,5,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SecurityCenterPostInfo) Reset()         { *m = SecurityCenterPostInfo{} }
func (m *SecurityCenterPostInfo) String() string { return proto.CompactTextString(m) }
func (*SecurityCenterPostInfo) ProtoMessage()    {}
func (*SecurityCenterPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{0}
}
func (m *SecurityCenterPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SecurityCenterPostInfo.Unmarshal(m, b)
}
func (m *SecurityCenterPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SecurityCenterPostInfo.Marshal(b, m, deterministic)
}
func (dst *SecurityCenterPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SecurityCenterPostInfo.Merge(dst, src)
}
func (m *SecurityCenterPostInfo) XXX_Size() int {
	return xxx_messageInfo_SecurityCenterPostInfo.Size(m)
}
func (m *SecurityCenterPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SecurityCenterPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SecurityCenterPostInfo proto.InternalMessageInfo

func (m *SecurityCenterPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *SecurityCenterPostInfo) GetPostTitle() string {
	if m != nil {
		return m.PostTitle
	}
	return ""
}

func (m *SecurityCenterPostInfo) GetPostCoverUrl() string {
	if m != nil {
		return m.PostCoverUrl
	}
	return ""
}

func (m *SecurityCenterPostInfo) GetPostContentUrl() string {
	if m != nil {
		return m.PostContentUrl
	}
	return ""
}

func (m *SecurityCenterPostInfo) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

type SecurityCenterPostRespInfo struct {
	TabId                uint32                    `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string                    `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	PostList             []*SecurityCenterPostInfo `protobuf:"bytes,3,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SecurityCenterPostRespInfo) Reset()         { *m = SecurityCenterPostRespInfo{} }
func (m *SecurityCenterPostRespInfo) String() string { return proto.CompactTextString(m) }
func (*SecurityCenterPostRespInfo) ProtoMessage()    {}
func (*SecurityCenterPostRespInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{1}
}
func (m *SecurityCenterPostRespInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SecurityCenterPostRespInfo.Unmarshal(m, b)
}
func (m *SecurityCenterPostRespInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SecurityCenterPostRespInfo.Marshal(b, m, deterministic)
}
func (dst *SecurityCenterPostRespInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SecurityCenterPostRespInfo.Merge(dst, src)
}
func (m *SecurityCenterPostRespInfo) XXX_Size() int {
	return xxx_messageInfo_SecurityCenterPostRespInfo.Size(m)
}
func (m *SecurityCenterPostRespInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SecurityCenterPostRespInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SecurityCenterPostRespInfo proto.InternalMessageInfo

func (m *SecurityCenterPostRespInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SecurityCenterPostRespInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *SecurityCenterPostRespInfo) GetPostList() []*SecurityCenterPostInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

type GetAllSecurityCenterPostReq struct {
	AppId                uint32   `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	SearchText           string   `protobuf:"bytes,2,opt,name=search_text,json=searchText,proto3" json:"search_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllSecurityCenterPostReq) Reset()         { *m = GetAllSecurityCenterPostReq{} }
func (m *GetAllSecurityCenterPostReq) String() string { return proto.CompactTextString(m) }
func (*GetAllSecurityCenterPostReq) ProtoMessage()    {}
func (*GetAllSecurityCenterPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{2}
}
func (m *GetAllSecurityCenterPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSecurityCenterPostReq.Unmarshal(m, b)
}
func (m *GetAllSecurityCenterPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSecurityCenterPostReq.Marshal(b, m, deterministic)
}
func (dst *GetAllSecurityCenterPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSecurityCenterPostReq.Merge(dst, src)
}
func (m *GetAllSecurityCenterPostReq) XXX_Size() int {
	return xxx_messageInfo_GetAllSecurityCenterPostReq.Size(m)
}
func (m *GetAllSecurityCenterPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSecurityCenterPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSecurityCenterPostReq proto.InternalMessageInfo

func (m *GetAllSecurityCenterPostReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GetAllSecurityCenterPostReq) GetSearchText() string {
	if m != nil {
		return m.SearchText
	}
	return ""
}

type GetAllSecurityCenterPostResp struct {
	PostRespList         []*SecurityCenterPostRespInfo `protobuf:"bytes,1,rep,name=post_resp_list,json=postRespList,proto3" json:"post_resp_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetAllSecurityCenterPostResp) Reset()         { *m = GetAllSecurityCenterPostResp{} }
func (m *GetAllSecurityCenterPostResp) String() string { return proto.CompactTextString(m) }
func (*GetAllSecurityCenterPostResp) ProtoMessage()    {}
func (*GetAllSecurityCenterPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{3}
}
func (m *GetAllSecurityCenterPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSecurityCenterPostResp.Unmarshal(m, b)
}
func (m *GetAllSecurityCenterPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSecurityCenterPostResp.Marshal(b, m, deterministic)
}
func (dst *GetAllSecurityCenterPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSecurityCenterPostResp.Merge(dst, src)
}
func (m *GetAllSecurityCenterPostResp) XXX_Size() int {
	return xxx_messageInfo_GetAllSecurityCenterPostResp.Size(m)
}
func (m *GetAllSecurityCenterPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSecurityCenterPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSecurityCenterPostResp proto.InternalMessageInfo

func (m *GetAllSecurityCenterPostResp) GetPostRespList() []*SecurityCenterPostRespInfo {
	if m != nil {
		return m.PostRespList
	}
	return nil
}

type GetSecurityCenterPostInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostTitle            string   `protobuf:"bytes,2,opt,name=post_title,json=postTitle,proto3" json:"post_title,omitempty"`
	AppIdList            []uint32 `protobuf:"varint,3,rep,packed,name=app_id_list,json=appIdList,proto3" json:"app_id_list,omitempty"`
	TabName              string   `protobuf:"bytes,4,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	PostCoverUrl         string   `protobuf:"bytes,5,opt,name=post_cover_url,json=postCoverUrl,proto3" json:"post_cover_url,omitempty"`
	PostContentUrl       string   `protobuf:"bytes,6,opt,name=post_content_url,json=postContentUrl,proto3" json:"post_content_url,omitempty"`
	UpdatedPeople        string   `protobuf:"bytes,7,opt,name=updated_people,json=updatedPeople,proto3" json:"updated_people,omitempty"`
	CreateAt             uint32   `protobuf:"varint,8,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	UpdateAt             uint32   `protobuf:"varint,9,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSecurityCenterPostInfo) Reset()         { *m = GetSecurityCenterPostInfo{} }
func (m *GetSecurityCenterPostInfo) String() string { return proto.CompactTextString(m) }
func (*GetSecurityCenterPostInfo) ProtoMessage()    {}
func (*GetSecurityCenterPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{4}
}
func (m *GetSecurityCenterPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSecurityCenterPostInfo.Unmarshal(m, b)
}
func (m *GetSecurityCenterPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSecurityCenterPostInfo.Marshal(b, m, deterministic)
}
func (dst *GetSecurityCenterPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSecurityCenterPostInfo.Merge(dst, src)
}
func (m *GetSecurityCenterPostInfo) XXX_Size() int {
	return xxx_messageInfo_GetSecurityCenterPostInfo.Size(m)
}
func (m *GetSecurityCenterPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSecurityCenterPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetSecurityCenterPostInfo proto.InternalMessageInfo

func (m *GetSecurityCenterPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetSecurityCenterPostInfo) GetPostTitle() string {
	if m != nil {
		return m.PostTitle
	}
	return ""
}

func (m *GetSecurityCenterPostInfo) GetAppIdList() []uint32 {
	if m != nil {
		return m.AppIdList
	}
	return nil
}

func (m *GetSecurityCenterPostInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GetSecurityCenterPostInfo) GetPostCoverUrl() string {
	if m != nil {
		return m.PostCoverUrl
	}
	return ""
}

func (m *GetSecurityCenterPostInfo) GetPostContentUrl() string {
	if m != nil {
		return m.PostContentUrl
	}
	return ""
}

func (m *GetSecurityCenterPostInfo) GetUpdatedPeople() string {
	if m != nil {
		return m.UpdatedPeople
	}
	return ""
}

func (m *GetSecurityCenterPostInfo) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *GetSecurityCenterPostInfo) GetUpdateAt() uint32 {
	if m != nil {
		return m.UpdateAt
	}
	return 0
}

type GetSecurityCenterPostReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tabId,proto3" json:"tabId,omitempty"`
	PostTitle            string   `protobuf:"bytes,4,opt,name=post_title,json=postTitle,proto3" json:"post_title,omitempty"`
	AppIdList            []uint32 `protobuf:"varint,5,rep,packed,name=app_id_list,json=appIdList,proto3" json:"app_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSecurityCenterPostReq) Reset()         { *m = GetSecurityCenterPostReq{} }
func (m *GetSecurityCenterPostReq) String() string { return proto.CompactTextString(m) }
func (*GetSecurityCenterPostReq) ProtoMessage()    {}
func (*GetSecurityCenterPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{5}
}
func (m *GetSecurityCenterPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSecurityCenterPostReq.Unmarshal(m, b)
}
func (m *GetSecurityCenterPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSecurityCenterPostReq.Marshal(b, m, deterministic)
}
func (dst *GetSecurityCenterPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSecurityCenterPostReq.Merge(dst, src)
}
func (m *GetSecurityCenterPostReq) XXX_Size() int {
	return xxx_messageInfo_GetSecurityCenterPostReq.Size(m)
}
func (m *GetSecurityCenterPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSecurityCenterPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSecurityCenterPostReq proto.InternalMessageInfo

func (m *GetSecurityCenterPostReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetSecurityCenterPostReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetSecurityCenterPostReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetSecurityCenterPostReq) GetPostTitle() string {
	if m != nil {
		return m.PostTitle
	}
	return ""
}

func (m *GetSecurityCenterPostReq) GetAppIdList() []uint32 {
	if m != nil {
		return m.AppIdList
	}
	return nil
}

type GetSecurityCenterPostResp struct {
	PostInfoList         []*GetSecurityCenterPostInfo `protobuf:"bytes,1,rep,name=post_info_list,json=postInfoList,proto3" json:"post_info_list,omitempty"`
	Total                uint32                       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetSecurityCenterPostResp) Reset()         { *m = GetSecurityCenterPostResp{} }
func (m *GetSecurityCenterPostResp) String() string { return proto.CompactTextString(m) }
func (*GetSecurityCenterPostResp) ProtoMessage()    {}
func (*GetSecurityCenterPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{6}
}
func (m *GetSecurityCenterPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSecurityCenterPostResp.Unmarshal(m, b)
}
func (m *GetSecurityCenterPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSecurityCenterPostResp.Marshal(b, m, deterministic)
}
func (dst *GetSecurityCenterPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSecurityCenterPostResp.Merge(dst, src)
}
func (m *GetSecurityCenterPostResp) XXX_Size() int {
	return xxx_messageInfo_GetSecurityCenterPostResp.Size(m)
}
func (m *GetSecurityCenterPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSecurityCenterPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSecurityCenterPostResp proto.InternalMessageInfo

func (m *GetSecurityCenterPostResp) GetPostInfoList() []*GetSecurityCenterPostInfo {
	if m != nil {
		return m.PostInfoList
	}
	return nil
}

func (m *GetSecurityCenterPostResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type UpdateSecurityCenterPostReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostTitle            string   `protobuf:"bytes,2,opt,name=post_title,json=postTitle,proto3" json:"post_title,omitempty"`
	AppIdList            []uint32 `protobuf:"varint,3,rep,packed,name=app_id_list,json=appIdList,proto3" json:"app_id_list,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	PostCoverUrl         string   `protobuf:"bytes,5,opt,name=post_cover_url,json=postCoverUrl,proto3" json:"post_cover_url,omitempty"`
	PostContentUrl       string   `protobuf:"bytes,6,opt,name=post_content_url,json=postContentUrl,proto3" json:"post_content_url,omitempty"`
	IsDelete             bool     `protobuf:"varint,7,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	UpdatedPeople        string   `protobuf:"bytes,8,opt,name=updated_people,json=updatedPeople,proto3" json:"updated_people,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSecurityCenterPostReq) Reset()         { *m = UpdateSecurityCenterPostReq{} }
func (m *UpdateSecurityCenterPostReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSecurityCenterPostReq) ProtoMessage()    {}
func (*UpdateSecurityCenterPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{7}
}
func (m *UpdateSecurityCenterPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSecurityCenterPostReq.Unmarshal(m, b)
}
func (m *UpdateSecurityCenterPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSecurityCenterPostReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSecurityCenterPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSecurityCenterPostReq.Merge(dst, src)
}
func (m *UpdateSecurityCenterPostReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSecurityCenterPostReq.Size(m)
}
func (m *UpdateSecurityCenterPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSecurityCenterPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSecurityCenterPostReq proto.InternalMessageInfo

func (m *UpdateSecurityCenterPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpdateSecurityCenterPostReq) GetPostTitle() string {
	if m != nil {
		return m.PostTitle
	}
	return ""
}

func (m *UpdateSecurityCenterPostReq) GetAppIdList() []uint32 {
	if m != nil {
		return m.AppIdList
	}
	return nil
}

func (m *UpdateSecurityCenterPostReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UpdateSecurityCenterPostReq) GetPostCoverUrl() string {
	if m != nil {
		return m.PostCoverUrl
	}
	return ""
}

func (m *UpdateSecurityCenterPostReq) GetPostContentUrl() string {
	if m != nil {
		return m.PostContentUrl
	}
	return ""
}

func (m *UpdateSecurityCenterPostReq) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

func (m *UpdateSecurityCenterPostReq) GetUpdatedPeople() string {
	if m != nil {
		return m.UpdatedPeople
	}
	return ""
}

type UpdateSecurityCenterPostResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSecurityCenterPostResp) Reset()         { *m = UpdateSecurityCenterPostResp{} }
func (m *UpdateSecurityCenterPostResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSecurityCenterPostResp) ProtoMessage()    {}
func (*UpdateSecurityCenterPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{8}
}
func (m *UpdateSecurityCenterPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSecurityCenterPostResp.Unmarshal(m, b)
}
func (m *UpdateSecurityCenterPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSecurityCenterPostResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSecurityCenterPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSecurityCenterPostResp.Merge(dst, src)
}
func (m *UpdateSecurityCenterPostResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSecurityCenterPostResp.Size(m)
}
func (m *UpdateSecurityCenterPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSecurityCenterPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSecurityCenterPostResp proto.InternalMessageInfo

type GetAllConfigTabReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllConfigTabReq) Reset()         { *m = GetAllConfigTabReq{} }
func (m *GetAllConfigTabReq) String() string { return proto.CompactTextString(m) }
func (*GetAllConfigTabReq) ProtoMessage()    {}
func (*GetAllConfigTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{9}
}
func (m *GetAllConfigTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConfigTabReq.Unmarshal(m, b)
}
func (m *GetAllConfigTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConfigTabReq.Marshal(b, m, deterministic)
}
func (dst *GetAllConfigTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConfigTabReq.Merge(dst, src)
}
func (m *GetAllConfigTabReq) XXX_Size() int {
	return xxx_messageInfo_GetAllConfigTabReq.Size(m)
}
func (m *GetAllConfigTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConfigTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConfigTabReq proto.InternalMessageInfo

type ConfigTabInfo struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfigTabInfo) Reset()         { *m = ConfigTabInfo{} }
func (m *ConfigTabInfo) String() string { return proto.CompactTextString(m) }
func (*ConfigTabInfo) ProtoMessage()    {}
func (*ConfigTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{10}
}
func (m *ConfigTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfigTabInfo.Unmarshal(m, b)
}
func (m *ConfigTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfigTabInfo.Marshal(b, m, deterministic)
}
func (dst *ConfigTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfigTabInfo.Merge(dst, src)
}
func (m *ConfigTabInfo) XXX_Size() int {
	return xxx_messageInfo_ConfigTabInfo.Size(m)
}
func (m *ConfigTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfigTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ConfigTabInfo proto.InternalMessageInfo

func (m *ConfigTabInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ConfigTabInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

type GetAllConfigTabResp struct {
	TabList              []*ConfigTabInfo `protobuf:"bytes,1,rep,name=tab_list,json=tabList,proto3" json:"tab_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAllConfigTabResp) Reset()         { *m = GetAllConfigTabResp{} }
func (m *GetAllConfigTabResp) String() string { return proto.CompactTextString(m) }
func (*GetAllConfigTabResp) ProtoMessage()    {}
func (*GetAllConfigTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{11}
}
func (m *GetAllConfigTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConfigTabResp.Unmarshal(m, b)
}
func (m *GetAllConfigTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConfigTabResp.Marshal(b, m, deterministic)
}
func (dst *GetAllConfigTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConfigTabResp.Merge(dst, src)
}
func (m *GetAllConfigTabResp) XXX_Size() int {
	return xxx_messageInfo_GetAllConfigTabResp.Size(m)
}
func (m *GetAllConfigTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConfigTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConfigTabResp proto.InternalMessageInfo

func (m *GetAllConfigTabResp) GetTabList() []*ConfigTabInfo {
	if m != nil {
		return m.TabList
	}
	return nil
}

type UpdateConfigTabReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	IsDelete             bool     `protobuf:"varint,3,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateConfigTabReq) Reset()         { *m = UpdateConfigTabReq{} }
func (m *UpdateConfigTabReq) String() string { return proto.CompactTextString(m) }
func (*UpdateConfigTabReq) ProtoMessage()    {}
func (*UpdateConfigTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{12}
}
func (m *UpdateConfigTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateConfigTabReq.Unmarshal(m, b)
}
func (m *UpdateConfigTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateConfigTabReq.Marshal(b, m, deterministic)
}
func (dst *UpdateConfigTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateConfigTabReq.Merge(dst, src)
}
func (m *UpdateConfigTabReq) XXX_Size() int {
	return xxx_messageInfo_UpdateConfigTabReq.Size(m)
}
func (m *UpdateConfigTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateConfigTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateConfigTabReq proto.InternalMessageInfo

func (m *UpdateConfigTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UpdateConfigTabReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *UpdateConfigTabReq) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

type UpdateConfigTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateConfigTabResp) Reset()         { *m = UpdateConfigTabResp{} }
func (m *UpdateConfigTabResp) String() string { return proto.CompactTextString(m) }
func (*UpdateConfigTabResp) ProtoMessage()    {}
func (*UpdateConfigTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_risk_control_security_center_604d0d3bc93fc14b, []int{13}
}
func (m *UpdateConfigTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateConfigTabResp.Unmarshal(m, b)
}
func (m *UpdateConfigTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateConfigTabResp.Marshal(b, m, deterministic)
}
func (dst *UpdateConfigTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateConfigTabResp.Merge(dst, src)
}
func (m *UpdateConfigTabResp) XXX_Size() int {
	return xxx_messageInfo_UpdateConfigTabResp.Size(m)
}
func (m *UpdateConfigTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateConfigTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateConfigTabResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*SecurityCenterPostInfo)(nil), "risk_control_security_center.SecurityCenterPostInfo")
	proto.RegisterType((*SecurityCenterPostRespInfo)(nil), "risk_control_security_center.SecurityCenterPostRespInfo")
	proto.RegisterType((*GetAllSecurityCenterPostReq)(nil), "risk_control_security_center.GetAllSecurityCenterPostReq")
	proto.RegisterType((*GetAllSecurityCenterPostResp)(nil), "risk_control_security_center.GetAllSecurityCenterPostResp")
	proto.RegisterType((*GetSecurityCenterPostInfo)(nil), "risk_control_security_center.GetSecurityCenterPostInfo")
	proto.RegisterType((*GetSecurityCenterPostReq)(nil), "risk_control_security_center.GetSecurityCenterPostReq")
	proto.RegisterType((*GetSecurityCenterPostResp)(nil), "risk_control_security_center.GetSecurityCenterPostResp")
	proto.RegisterType((*UpdateSecurityCenterPostReq)(nil), "risk_control_security_center.UpdateSecurityCenterPostReq")
	proto.RegisterType((*UpdateSecurityCenterPostResp)(nil), "risk_control_security_center.UpdateSecurityCenterPostResp")
	proto.RegisterType((*GetAllConfigTabReq)(nil), "risk_control_security_center.GetAllConfigTabReq")
	proto.RegisterType((*ConfigTabInfo)(nil), "risk_control_security_center.ConfigTabInfo")
	proto.RegisterType((*GetAllConfigTabResp)(nil), "risk_control_security_center.GetAllConfigTabResp")
	proto.RegisterType((*UpdateConfigTabReq)(nil), "risk_control_security_center.UpdateConfigTabReq")
	proto.RegisterType((*UpdateConfigTabResp)(nil), "risk_control_security_center.UpdateConfigTabResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RiskControlSecurityCenterClient is the client API for RiskControlSecurityCenter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RiskControlSecurityCenterClient interface {
	// 拉取所有帖子
	GetAllSecurityCenterPost(ctx context.Context, in *GetAllSecurityCenterPostReq, opts ...grpc.CallOption) (*GetAllSecurityCenterPostResp, error)
	// 运营后台
	GetAllConfigTab(ctx context.Context, in *GetAllConfigTabReq, opts ...grpc.CallOption) (*GetAllConfigTabResp, error)
	UpdateSecurityCenterPost(ctx context.Context, in *UpdateSecurityCenterPostReq, opts ...grpc.CallOption) (*UpdateSecurityCenterPostResp, error)
	GetSecurityCenterPost(ctx context.Context, in *GetSecurityCenterPostReq, opts ...grpc.CallOption) (*GetSecurityCenterPostResp, error)
	// 配置 tab 的接口，tab 相对固定，到时候只需要调用一次（与运营后台无关）
	UpdateConfigTab(ctx context.Context, in *UpdateConfigTabReq, opts ...grpc.CallOption) (*UpdateConfigTabResp, error)
}

type riskControlSecurityCenterClient struct {
	cc *grpc.ClientConn
}

func NewRiskControlSecurityCenterClient(cc *grpc.ClientConn) RiskControlSecurityCenterClient {
	return &riskControlSecurityCenterClient{cc}
}

func (c *riskControlSecurityCenterClient) GetAllSecurityCenterPost(ctx context.Context, in *GetAllSecurityCenterPostReq, opts ...grpc.CallOption) (*GetAllSecurityCenterPostResp, error) {
	out := new(GetAllSecurityCenterPostResp)
	err := c.cc.Invoke(ctx, "/risk_control_security_center.RiskControlSecurityCenter/GetAllSecurityCenterPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskControlSecurityCenterClient) GetAllConfigTab(ctx context.Context, in *GetAllConfigTabReq, opts ...grpc.CallOption) (*GetAllConfigTabResp, error) {
	out := new(GetAllConfigTabResp)
	err := c.cc.Invoke(ctx, "/risk_control_security_center.RiskControlSecurityCenter/GetAllConfigTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskControlSecurityCenterClient) UpdateSecurityCenterPost(ctx context.Context, in *UpdateSecurityCenterPostReq, opts ...grpc.CallOption) (*UpdateSecurityCenterPostResp, error) {
	out := new(UpdateSecurityCenterPostResp)
	err := c.cc.Invoke(ctx, "/risk_control_security_center.RiskControlSecurityCenter/UpdateSecurityCenterPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskControlSecurityCenterClient) GetSecurityCenterPost(ctx context.Context, in *GetSecurityCenterPostReq, opts ...grpc.CallOption) (*GetSecurityCenterPostResp, error) {
	out := new(GetSecurityCenterPostResp)
	err := c.cc.Invoke(ctx, "/risk_control_security_center.RiskControlSecurityCenter/GetSecurityCenterPost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskControlSecurityCenterClient) UpdateConfigTab(ctx context.Context, in *UpdateConfigTabReq, opts ...grpc.CallOption) (*UpdateConfigTabResp, error) {
	out := new(UpdateConfigTabResp)
	err := c.cc.Invoke(ctx, "/risk_control_security_center.RiskControlSecurityCenter/UpdateConfigTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RiskControlSecurityCenterServer is the server API for RiskControlSecurityCenter service.
type RiskControlSecurityCenterServer interface {
	// 拉取所有帖子
	GetAllSecurityCenterPost(context.Context, *GetAllSecurityCenterPostReq) (*GetAllSecurityCenterPostResp, error)
	// 运营后台
	GetAllConfigTab(context.Context, *GetAllConfigTabReq) (*GetAllConfigTabResp, error)
	UpdateSecurityCenterPost(context.Context, *UpdateSecurityCenterPostReq) (*UpdateSecurityCenterPostResp, error)
	GetSecurityCenterPost(context.Context, *GetSecurityCenterPostReq) (*GetSecurityCenterPostResp, error)
	// 配置 tab 的接口，tab 相对固定，到时候只需要调用一次（与运营后台无关）
	UpdateConfigTab(context.Context, *UpdateConfigTabReq) (*UpdateConfigTabResp, error)
}

func RegisterRiskControlSecurityCenterServer(s *grpc.Server, srv RiskControlSecurityCenterServer) {
	s.RegisterService(&_RiskControlSecurityCenter_serviceDesc, srv)
}

func _RiskControlSecurityCenter_GetAllSecurityCenterPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllSecurityCenterPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskControlSecurityCenterServer).GetAllSecurityCenterPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_control_security_center.RiskControlSecurityCenter/GetAllSecurityCenterPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskControlSecurityCenterServer).GetAllSecurityCenterPost(ctx, req.(*GetAllSecurityCenterPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskControlSecurityCenter_GetAllConfigTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllConfigTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskControlSecurityCenterServer).GetAllConfigTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_control_security_center.RiskControlSecurityCenter/GetAllConfigTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskControlSecurityCenterServer).GetAllConfigTab(ctx, req.(*GetAllConfigTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskControlSecurityCenter_UpdateSecurityCenterPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSecurityCenterPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskControlSecurityCenterServer).UpdateSecurityCenterPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_control_security_center.RiskControlSecurityCenter/UpdateSecurityCenterPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskControlSecurityCenterServer).UpdateSecurityCenterPost(ctx, req.(*UpdateSecurityCenterPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskControlSecurityCenter_GetSecurityCenterPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecurityCenterPostReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskControlSecurityCenterServer).GetSecurityCenterPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_control_security_center.RiskControlSecurityCenter/GetSecurityCenterPost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskControlSecurityCenterServer).GetSecurityCenterPost(ctx, req.(*GetSecurityCenterPostReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskControlSecurityCenter_UpdateConfigTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateConfigTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskControlSecurityCenterServer).UpdateConfigTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risk_control_security_center.RiskControlSecurityCenter/UpdateConfigTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskControlSecurityCenterServer).UpdateConfigTab(ctx, req.(*UpdateConfigTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RiskControlSecurityCenter_serviceDesc = grpc.ServiceDesc{
	ServiceName: "risk_control_security_center.RiskControlSecurityCenter",
	HandlerType: (*RiskControlSecurityCenterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAllSecurityCenterPost",
			Handler:    _RiskControlSecurityCenter_GetAllSecurityCenterPost_Handler,
		},
		{
			MethodName: "GetAllConfigTab",
			Handler:    _RiskControlSecurityCenter_GetAllConfigTab_Handler,
		},
		{
			MethodName: "UpdateSecurityCenterPost",
			Handler:    _RiskControlSecurityCenter_UpdateSecurityCenterPost_Handler,
		},
		{
			MethodName: "GetSecurityCenterPost",
			Handler:    _RiskControlSecurityCenter_GetSecurityCenterPost_Handler,
		},
		{
			MethodName: "UpdateConfigTab",
			Handler:    _RiskControlSecurityCenter_UpdateConfigTab_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "risk-control-security-center/risk-control-security-center.proto",
}

func init() {
	proto.RegisterFile("risk-control-security-center/risk-control-security-center.proto", fileDescriptor_risk_control_security_center_604d0d3bc93fc14b)
}

var fileDescriptor_risk_control_security_center_604d0d3bc93fc14b = []byte{
	// 794 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x96, 0x5d, 0x4f, 0x13, 0x4d,
	0x14, 0xc7, 0x59, 0xfa, 0x42, 0x7b, 0x78, 0xca, 0xf3, 0x64, 0x78, 0x79, 0x0a, 0x45, 0x24, 0x1b,
	0x4d, 0x9a, 0x18, 0x8a, 0xe2, 0x0b, 0xea, 0x85, 0x5a, 0x6b, 0x24, 0x4d, 0x8c, 0xc1, 0x15, 0x6e,
	0x4c, 0x70, 0xb3, 0xdd, 0x4e, 0x71, 0xc3, 0x76, 0x67, 0xd8, 0x39, 0x10, 0xbc, 0xf1, 0x03, 0x78,
	0x65, 0xe2, 0xb5, 0x89, 0x7e, 0x10, 0xe3, 0xad, 0x1f, 0xcb, 0xcc, 0xcc, 0x16, 0xba, 0x74, 0xbb,
	0x85, 0x06, 0xef, 0x98, 0xff, 0x9c, 0x99, 0x3d, 0xe7, 0x7f, 0x7e, 0x73, 0x28, 0x3c, 0x0d, 0x3d,
	0x71, 0xb0, 0xe6, 0xb2, 0x00, 0x43, 0xe6, 0xaf, 0x09, 0xea, 0x1e, 0x85, 0x1e, 0x7e, 0x5c, 0x73,
	0x69, 0x80, 0x34, 0x5c, 0x4f, 0xdb, 0xac, 0xf1, 0x90, 0x21, 0x23, 0xcb, 0x32, 0xc6, 0x8e, 0x62,
	0xec, 0x5e, 0x8c, 0xad, 0x63, 0xcc, 0x9f, 0x06, 0x2c, 0xbc, 0x8d, 0xb4, 0x86, 0x92, 0xb6, 0x99,
	0xc0, 0x66, 0xd0, 0x61, 0xe4, 0x7f, 0x98, 0xe2, 0x4c, 0xa0, 0xed, 0xb5, 0xcb, 0xc6, 0xaa, 0x51,
	0x2d, 0x5a, 0x79, 0xb9, 0x6c, 0xb6, 0xc9, 0x35, 0x00, 0xb5, 0x81, 0x1e, 0xfa, 0xb4, 0x3c, 0xa9,
	0xf6, 0x8a, 0x52, 0xd9, 0x91, 0x02, 0xb9, 0x01, 0x33, 0x6a, 0xdb, 0x65, 0xc7, 0x34, 0xb4, 0x8f,
	0x42, 0xbf, 0x9c, 0x51, 0x21, 0xff, 0x48, 0xb5, 0x21, 0xc5, 0xdd, 0xd0, 0x27, 0x55, 0xf8, 0x2f,
	0x8a, 0x0a, 0x90, 0x06, 0xa8, 0xe2, 0xb2, 0x2a, 0x6e, 0x46, 0xc7, 0x29, 0x59, 0x46, 0x56, 0xa0,
	0xe8, 0x86, 0xd4, 0x41, 0x6a, 0x3b, 0x58, 0xce, 0xad, 0x1a, 0xd5, 0x92, 0x55, 0xd0, 0x42, 0x1d,
	0xcd, 0x1f, 0x06, 0x2c, 0x0d, 0xe6, 0x6f, 0x51, 0xc1, 0x55, 0x0d, 0xf3, 0x90, 0x47, 0xa7, 0xd5,
	0x2b, 0xa1, 0x64, 0xe5, 0xd0, 0x69, 0x35, 0xdb, 0x64, 0x11, 0x0a, 0x52, 0x0e, 0x9c, 0x6e, 0x2f,
	0xff, 0x29, 0x74, 0x5a, 0xaf, 0x9d, 0x2e, 0x25, 0x6f, 0x40, 0x95, 0x62, 0xfb, 0x9e, 0xc0, 0x72,
	0x66, 0x35, 0x53, 0x9d, 0xde, 0xb8, 0x57, 0x4b, 0xb3, 0xb0, 0x96, 0x6c, 0x9f, 0x55, 0x90, 0xd7,
	0xbc, 0xf2, 0x04, 0x9a, 0xbb, 0x50, 0xd9, 0xa2, 0x58, 0xf7, 0xfd, 0xa4, 0x44, 0x0f, 0x65, 0x8e,
	0x0e, 0xe7, 0x7d, 0x39, 0x3a, 0x9c, 0x37, 0xdb, 0xe4, 0x3a, 0x4c, 0x0b, 0xea, 0x84, 0xee, 0x07,
	0x1b, 0xe9, 0x09, 0x46, 0x69, 0x82, 0x96, 0x76, 0xe8, 0x09, 0x9a, 0x9f, 0x60, 0x79, 0xf8, 0xb5,
	0x82, 0x93, 0xf7, 0x51, 0x1f, 0x42, 0x2a, 0xb8, 0x2e, 0xc7, 0x50, 0xe5, 0x3c, 0xbc, 0x6c, 0x39,
	0x3d, 0x37, 0x75, 0x07, 0xe5, 0x4a, 0x95, 0xf5, 0x6b, 0x12, 0x16, 0xb7, 0x28, 0x5e, 0x31, 0x3d,
	0x2b, 0x30, 0xad, 0xdd, 0x38, 0xeb, 0x40, 0xc9, 0x2a, 0x2a, 0x4b, 0xe4, 0x57, 0x63, 0xad, 0xcb,
	0xc6, 0x5b, 0x37, 0x08, 0x5e, 0xee, 0x82, 0xe0, 0xe5, 0x13, 0xc1, 0xbb, 0x09, 0x33, 0x47, 0xbc,
	0xed, 0x20, 0x6d, 0xdb, 0x9c, 0x32, 0xee, 0xd3, 0xf2, 0x94, 0x8a, 0x2b, 0x45, 0xea, 0xb6, 0x12,
	0xe3, 0x7c, 0x16, 0xe2, 0x7c, 0xca, 0x4d, 0x1d, 0x2d, 0x37, 0x8b, 0x7a, 0x53, 0x0b, 0x75, 0x34,
	0xbf, 0x19, 0x50, 0x4e, 0x74, 0x50, 0x62, 0xb1, 0x00, 0x79, 0xd6, 0xe9, 0x08, 0x8a, 0x11, 0x16,
	0xd1, 0x8a, 0xcc, 0x41, 0xce, 0xf7, 0xba, 0x9e, 0x26, 0xa2, 0x64, 0xe9, 0x85, 0x54, 0x15, 0xda,
	0xea, 0xad, 0x9d, 0x72, 0x1e, 0xf7, 0x3a, 0x3b, 0xc2, 0xeb, 0xdc, 0x39, 0xaf, 0xcd, 0x2f, 0xc6,
	0x90, 0x0e, 0x2b, 0xbe, 0xf6, 0x22, 0xbb, 0xbd, 0xa0, 0xc3, 0xfa, 0xf9, 0xda, 0x4c, 0xe7, 0x6b,
	0x28, 0x32, 0xba, 0x4f, 0xf2, 0x2f, 0xd5, 0x68, 0x59, 0x11, 0x43, 0xc7, 0xef, 0xd5, 0xa9, 0x16,
	0xe6, 0xf7, 0x49, 0xa8, 0xec, 0x2a, 0xff, 0x92, 0x5d, 0xfb, 0x5b, 0xd8, 0x9d, 0x0d, 0x92, 0x6c,
	0xbf, 0xc1, 0x57, 0x8d, 0x5c, 0x05, 0x8a, 0x9e, 0xb0, 0xdb, 0xd4, 0xa7, 0xa8, 0x69, 0x2b, 0x58,
	0x05, 0x4f, 0xbc, 0x50, 0xeb, 0x04, 0x1e, 0x0b, 0x09, 0x3c, 0x9a, 0x2b, 0xb0, 0x3c, 0xdc, 0x21,
	0xc1, 0xcd, 0x39, 0x20, 0x7a, 0x6e, 0x34, 0x58, 0xd0, 0xf1, 0xf6, 0x77, 0x9c, 0x96, 0x45, 0x0f,
	0xcd, 0x3a, 0x94, 0x4e, 0xd7, 0xe3, 0x8d, 0x4e, 0x73, 0x0f, 0x66, 0x07, 0x2e, 0x16, 0x9c, 0xbc,
	0xd4, 0x27, 0xfa, 0x08, 0xb9, 0x95, 0x4e, 0x48, 0x2c, 0x0f, 0x75, 0xbd, 0xa2, 0xd1, 0x05, 0xa2,
	0xeb, 0xea, 0xcf, 0x7b, 0x8c, 0x09, 0x1f, 0xf3, 0x38, 0x13, 0xf7, 0xd8, 0x9c, 0x87, 0xd9, 0x81,
	0x8f, 0x08, 0xbe, 0xf1, 0x3b, 0x07, 0x8b, 0x96, 0x27, 0x0e, 0x1a, 0x3a, 0xe5, 0xb8, 0xb3, 0xe4,
	0xab, 0x7e, 0xc7, 0x89, 0xa3, 0x98, 0x3c, 0x1a, 0xf9, 0x1c, 0x86, 0xfd, 0x67, 0x58, 0x7a, 0x3c,
	0xee, 0x51, 0xc1, 0xcd, 0x09, 0x72, 0x02, 0xff, 0x9e, 0x6b, 0x07, 0xb9, 0x7d, 0x91, 0x0b, 0xfb,
	0xed, 0x5d, 0xba, 0x73, 0xc9, 0x13, 0xea, 0xcb, 0xd2, 0x8f, 0x61, 0x08, 0x8e, 0xf2, 0x23, 0xe5,
	0x71, 0x8f, 0xf2, 0x23, 0x95, 0xfa, 0x09, 0xf2, 0xd9, 0x80, 0xf9, 0xc4, 0xe1, 0x43, 0x1e, 0x8c,
	0x31, 0xb1, 0x64, 0x3e, 0x9b, 0x63, 0x9d, 0xeb, 0x35, 0xe7, 0x1c, 0x67, 0xa3, 0x9a, 0x33, 0xc8,
	0xfe, 0xa8, 0xe6, 0x24, 0x80, 0x6c, 0x4e, 0x3c, 0x7f, 0xf6, 0xee, 0xc9, 0x3e, 0xf3, 0x9d, 0x60,
	0xbf, 0x76, 0x7f, 0x03, 0xb1, 0xe6, 0xb2, 0xee, 0xba, 0xfa, 0xa1, 0xe8, 0x32, 0x7f, 0x5d, 0xd0,
	0xf0, 0xd8, 0x73, 0xa9, 0x48, 0xfd, 0x5d, 0xd9, 0xca, 0xab, 0xf8, 0xbb, 0x7f, 0x02, 0x00, 0x00,
	0xff, 0xff, 0xb7, 0xd3, 0x4f, 0x7b, 0x9b, 0x0a, 0x00, 0x00,
}
