// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sendim/bot/sendim-bot.proto

package sendim_bot // import "golang.52tt.com/protocol/services/sendim/sendim-bot"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ErrCode int32

const (
	ErrCode_ERR_OK     ErrCode = 0
	ErrCode_ERR_PARAM  ErrCode = 1
	ErrCode_ERR_SYSTEM ErrCode = 2
)

var ErrCode_name = map[int32]string{
	0: "ERR_OK",
	1: "ERR_PARAM",
	2: "ERR_SYSTEM",
}
var ErrCode_value = map[string]int32{
	"ERR_OK":     0,
	"ERR_PARAM":  1,
	"ERR_SYSTEM": 2,
}

func (x ErrCode) String() string {
	return proto.EnumName(ErrCode_name, int32(x))
}
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{0}
}

type Sender_SenderType int32

const (
	Sender_Unknown Sender_SenderType = 0
	Sender_User    Sender_SenderType = 1
	Sender_Public  Sender_SenderType = 2
)

var Sender_SenderType_name = map[int32]string{
	0: "Unknown",
	1: "User",
	2: "Public",
}
var Sender_SenderType_value = map[string]int32{
	"Unknown": 0,
	"User":    1,
	"Public":  2,
}

func (x Sender_SenderType) String() string {
	return proto.EnumName(Sender_SenderType_name, int32(x))
}
func (Sender_SenderType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{7, 0}
}

type Receiver_ReceiverType int32

const (
	Receiver_Unknown   Receiver_ReceiverType = 0
	Receiver_User      Receiver_ReceiverType = 1
	Receiver_Public    Receiver_ReceiverType = 2
	Receiver_Broadcast Receiver_ReceiverType = 3
)

var Receiver_ReceiverType_name = map[int32]string{
	0: "Unknown",
	1: "User",
	2: "Public",
	3: "Broadcast",
}
var Receiver_ReceiverType_value = map[string]int32{
	"Unknown":   0,
	"User":      1,
	"Public":    2,
	"Broadcast": 3,
}

func (x Receiver_ReceiverType) String() string {
	return proto.EnumName(Receiver_ReceiverType_name, int32(x))
}
func (Receiver_ReceiverType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{8, 0}
}

type Content_ContentType int32

const (
	Content_Unknown              Content_ContentType = 0
	Content_Text                 Content_ContentType = 1
	Content_TextWithHighlightUrl Content_ContentType = 2
	Content_OfficialMessage      Content_ContentType = 3
	Content_NewExtended          Content_ContentType = 4
)

var Content_ContentType_name = map[int32]string{
	0: "Unknown",
	1: "Text",
	2: "TextWithHighlightUrl",
	3: "OfficialMessage",
	4: "NewExtended",
}
var Content_ContentType_value = map[string]int32{
	"Unknown":              0,
	"Text":                 1,
	"TextWithHighlightUrl": 2,
	"OfficialMessage":      3,
	"NewExtended":          4,
}

func (x Content_ContentType) String() string {
	return proto.EnumName(Content_ContentType_name, int32(x))
}
func (Content_ContentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{9, 0}
}

type OfficialMessageBody_OfficialMessageType int32

const (
	OfficialMessageBody_Unknown          OfficialMessageBody_OfficialMessageType = 0
	OfficialMessageBody_TEXT             OfficialMessageBody_OfficialMessageType = 1
	OfficialMessageBody_PICTURE_AND_TEXT OfficialMessageBody_OfficialMessageType = 2
	OfficialMessageBody_GAME_DOWNLOAD    OfficialMessageBody_OfficialMessageType = 3
	OfficialMessageBody_TEXT_JUMP        OfficialMessageBody_OfficialMessageType = 4
)

var OfficialMessageBody_OfficialMessageType_name = map[int32]string{
	0: "Unknown",
	1: "TEXT",
	2: "PICTURE_AND_TEXT",
	3: "GAME_DOWNLOAD",
	4: "TEXT_JUMP",
}
var OfficialMessageBody_OfficialMessageType_value = map[string]int32{
	"Unknown":          0,
	"TEXT":             1,
	"PICTURE_AND_TEXT": 2,
	"GAME_DOWNLOAD":    3,
	"TEXT_JUMP":        4,
}

func (x OfficialMessageBody_OfficialMessageType) String() string {
	return proto.EnumName(OfficialMessageBody_OfficialMessageType_name, int32(x))
}
func (OfficialMessageBody_OfficialMessageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{14, 0}
}

type Receipt_SuffixType int32

const (
	Receipt_Unknown   Receipt_SuffixType = 0
	Receipt_User      Receipt_SuffixType = 1
	Receipt_Public    Receipt_SuffixType = 2
	Receipt_Broadcast Receipt_SuffixType = 3
)

var Receipt_SuffixType_name = map[int32]string{
	0: "Unknown",
	1: "User",
	2: "Public",
	3: "Broadcast",
}
var Receipt_SuffixType_value = map[string]int32{
	"Unknown":   0,
	"User":      1,
	"Public":    2,
	"Broadcast": 3,
}

func (x Receipt_SuffixType) String() string {
	return proto.EnumName(Receipt_SuffixType_name, int32(x))
}
func (Receipt_SuffixType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{17, 0}
}

type SendTaskReq struct {
	Task                 string    `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	Sender               *Sender   `protobuf:"bytes,2,opt,name=sender,proto3" json:"sender,omitempty"`
	Receiver             *Receiver `protobuf:"bytes,3,opt,name=receiver,proto3" json:"receiver,omitempty"`
	Msg                  *ImMsg    `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SendTaskReq) Reset()         { *m = SendTaskReq{} }
func (m *SendTaskReq) String() string { return proto.CompactTextString(m) }
func (*SendTaskReq) ProtoMessage()    {}
func (*SendTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{0}
}
func (m *SendTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendTaskReq.Unmarshal(m, b)
}
func (m *SendTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendTaskReq.Marshal(b, m, deterministic)
}
func (dst *SendTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendTaskReq.Merge(dst, src)
}
func (m *SendTaskReq) XXX_Size() int {
	return xxx_messageInfo_SendTaskReq.Size(m)
}
func (m *SendTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendTaskReq proto.InternalMessageInfo

func (m *SendTaskReq) GetTask() string {
	if m != nil {
		return m.Task
	}
	return ""
}

func (m *SendTaskReq) GetSender() *Sender {
	if m != nil {
		return m.Sender
	}
	return nil
}

func (m *SendTaskReq) GetReceiver() *Receiver {
	if m != nil {
		return m.Receiver
	}
	return nil
}

func (m *SendTaskReq) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type SendTaskResp struct {
	Err                  ErrCode  `protobuf:"varint,1,opt,name=err,proto3,enum=sendim_bot.ErrCode" json:"err,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendTaskResp) Reset()         { *m = SendTaskResp{} }
func (m *SendTaskResp) String() string { return proto.CompactTextString(m) }
func (*SendTaskResp) ProtoMessage()    {}
func (*SendTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{1}
}
func (m *SendTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendTaskResp.Unmarshal(m, b)
}
func (m *SendTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendTaskResp.Marshal(b, m, deterministic)
}
func (dst *SendTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendTaskResp.Merge(dst, src)
}
func (m *SendTaskResp) XXX_Size() int {
	return xxx_messageInfo_SendTaskResp.Size(m)
}
func (m *SendTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendTaskResp proto.InternalMessageInfo

func (m *SendTaskResp) GetErr() ErrCode {
	if m != nil {
		return m.Err
	}
	return ErrCode_ERR_OK
}

func (m *SendTaskResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type StopTaskReq struct {
	Task                 string   `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopTaskReq) Reset()         { *m = StopTaskReq{} }
func (m *StopTaskReq) String() string { return proto.CompactTextString(m) }
func (*StopTaskReq) ProtoMessage()    {}
func (*StopTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{2}
}
func (m *StopTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopTaskReq.Unmarshal(m, b)
}
func (m *StopTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopTaskReq.Marshal(b, m, deterministic)
}
func (dst *StopTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopTaskReq.Merge(dst, src)
}
func (m *StopTaskReq) XXX_Size() int {
	return xxx_messageInfo_StopTaskReq.Size(m)
}
func (m *StopTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopTaskReq proto.InternalMessageInfo

func (m *StopTaskReq) GetTask() string {
	if m != nil {
		return m.Task
	}
	return ""
}

type StopTaskResp struct {
	Err                  ErrCode  `protobuf:"varint,1,opt,name=err,proto3,enum=sendim_bot.ErrCode" json:"err,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopTaskResp) Reset()         { *m = StopTaskResp{} }
func (m *StopTaskResp) String() string { return proto.CompactTextString(m) }
func (*StopTaskResp) ProtoMessage()    {}
func (*StopTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{3}
}
func (m *StopTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopTaskResp.Unmarshal(m, b)
}
func (m *StopTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopTaskResp.Marshal(b, m, deterministic)
}
func (dst *StopTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopTaskResp.Merge(dst, src)
}
func (m *StopTaskResp) XXX_Size() int {
	return xxx_messageInfo_StopTaskResp.Size(m)
}
func (m *StopTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopTaskResp proto.InternalMessageInfo

func (m *StopTaskResp) GetErr() ErrCode {
	if m != nil {
		return m.Err
	}
	return ErrCode_ERR_OK
}

func (m *StopTaskResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type RecallTaskReq struct {
	Task                 string   `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallTaskReq) Reset()         { *m = RecallTaskReq{} }
func (m *RecallTaskReq) String() string { return proto.CompactTextString(m) }
func (*RecallTaskReq) ProtoMessage()    {}
func (*RecallTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{4}
}
func (m *RecallTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallTaskReq.Unmarshal(m, b)
}
func (m *RecallTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallTaskReq.Marshal(b, m, deterministic)
}
func (dst *RecallTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallTaskReq.Merge(dst, src)
}
func (m *RecallTaskReq) XXX_Size() int {
	return xxx_messageInfo_RecallTaskReq.Size(m)
}
func (m *RecallTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecallTaskReq proto.InternalMessageInfo

func (m *RecallTaskReq) GetTask() string {
	if m != nil {
		return m.Task
	}
	return ""
}

type RecallTaskResp struct {
	Err                  ErrCode  `protobuf:"varint,1,opt,name=err,proto3,enum=sendim_bot.ErrCode" json:"err,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallTaskResp) Reset()         { *m = RecallTaskResp{} }
func (m *RecallTaskResp) String() string { return proto.CompactTextString(m) }
func (*RecallTaskResp) ProtoMessage()    {}
func (*RecallTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{5}
}
func (m *RecallTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallTaskResp.Unmarshal(m, b)
}
func (m *RecallTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallTaskResp.Marshal(b, m, deterministic)
}
func (dst *RecallTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallTaskResp.Merge(dst, src)
}
func (m *RecallTaskResp) XXX_Size() int {
	return xxx_messageInfo_RecallTaskResp.Size(m)
}
func (m *RecallTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecallTaskResp proto.InternalMessageInfo

func (m *RecallTaskResp) GetErr() ErrCode {
	if m != nil {
		return m.Err
	}
	return ErrCode_ERR_OK
}

func (m *RecallTaskResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type ImMsg struct {
	Content              *Content `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	ClientMsgId          uint32   `protobuf:"varint,2,opt,name=client_msg_id,json=clientMsgId,proto3" json:"client_msg_id,omitempty"`
	ClientMsgTime        uint32   `protobuf:"varint,3,opt,name=client_msg_time,json=clientMsgTime,proto3" json:"client_msg_time,omitempty"`
	AppName              string   `protobuf:"bytes,4,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	AppPlatform          string   `protobuf:"bytes,5,opt,name=app_platform,json=appPlatform,proto3" json:"app_platform,omitempty"`
	ExpiredAt            uint32   `protobuf:"varint,6,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImMsg) Reset()         { *m = ImMsg{} }
func (m *ImMsg) String() string { return proto.CompactTextString(m) }
func (*ImMsg) ProtoMessage()    {}
func (*ImMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{6}
}
func (m *ImMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImMsg.Unmarshal(m, b)
}
func (m *ImMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImMsg.Marshal(b, m, deterministic)
}
func (dst *ImMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImMsg.Merge(dst, src)
}
func (m *ImMsg) XXX_Size() int {
	return xxx_messageInfo_ImMsg.Size(m)
}
func (m *ImMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ImMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ImMsg proto.InternalMessageInfo

func (m *ImMsg) GetContent() *Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *ImMsg) GetClientMsgId() uint32 {
	if m != nil {
		return m.ClientMsgId
	}
	return 0
}

func (m *ImMsg) GetClientMsgTime() uint32 {
	if m != nil {
		return m.ClientMsgTime
	}
	return 0
}

func (m *ImMsg) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

func (m *ImMsg) GetAppPlatform() string {
	if m != nil {
		return m.AppPlatform
	}
	return ""
}

func (m *ImMsg) GetExpiredAt() uint32 {
	if m != nil {
		return m.ExpiredAt
	}
	return 0
}

type Sender struct {
	Type                 Sender_SenderType `protobuf:"varint,1,opt,name=type,proto3,enum=sendim_bot.Sender_SenderType" json:"type,omitempty"`
	Id                   uint32            `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Sender) Reset()         { *m = Sender{} }
func (m *Sender) String() string { return proto.CompactTextString(m) }
func (*Sender) ProtoMessage()    {}
func (*Sender) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{7}
}
func (m *Sender) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Sender.Unmarshal(m, b)
}
func (m *Sender) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Sender.Marshal(b, m, deterministic)
}
func (dst *Sender) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Sender.Merge(dst, src)
}
func (m *Sender) XXX_Size() int {
	return xxx_messageInfo_Sender.Size(m)
}
func (m *Sender) XXX_DiscardUnknown() {
	xxx_messageInfo_Sender.DiscardUnknown(m)
}

var xxx_messageInfo_Sender proto.InternalMessageInfo

func (m *Sender) GetType() Sender_SenderType {
	if m != nil {
		return m.Type
	}
	return Sender_Unknown
}

func (m *Sender) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type Receiver struct {
	Type                 Receiver_ReceiverType `protobuf:"varint,1,opt,name=type,proto3,enum=sendim_bot.Receiver_ReceiverType" json:"type,omitempty"`
	IdList               []uint32              `protobuf:"varint,2,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *Receiver) Reset()         { *m = Receiver{} }
func (m *Receiver) String() string { return proto.CompactTextString(m) }
func (*Receiver) ProtoMessage()    {}
func (*Receiver) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{8}
}
func (m *Receiver) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Receiver.Unmarshal(m, b)
}
func (m *Receiver) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Receiver.Marshal(b, m, deterministic)
}
func (dst *Receiver) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Receiver.Merge(dst, src)
}
func (m *Receiver) XXX_Size() int {
	return xxx_messageInfo_Receiver.Size(m)
}
func (m *Receiver) XXX_DiscardUnknown() {
	xxx_messageInfo_Receiver.DiscardUnknown(m)
}

var xxx_messageInfo_Receiver proto.InternalMessageInfo

func (m *Receiver) GetType() Receiver_ReceiverType {
	if m != nil {
		return m.Type
	}
	return Receiver_Unknown
}

func (m *Receiver) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type Content struct {
	Type                 Content_ContentType     `protobuf:"varint,1,opt,name=type,proto3,enum=sendim_bot.Content_ContentType" json:"type,omitempty"`
	TextNormal           *ImTextNormal           `protobuf:"bytes,2,opt,name=text_normal,json=textNormal,proto3" json:"text_normal,omitempty"`
	TextHlUrl            *ImTextWithHighlightUrl `protobuf:"bytes,3,opt,name=text_hl_url,json=textHlUrl,proto3" json:"text_hl_url,omitempty"`
	OfficialMessage      *ImOfficialMessage      `protobuf:"bytes,4,opt,name=official_message,json=officialMessage,proto3" json:"official_message,omitempty"`
	NewExtended          *ImNewExtended          `protobuf:"bytes,5,opt,name=new_extended,json=newExtended,proto3" json:"new_extended,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *Content) Reset()         { *m = Content{} }
func (m *Content) String() string { return proto.CompactTextString(m) }
func (*Content) ProtoMessage()    {}
func (*Content) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{9}
}
func (m *Content) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Content.Unmarshal(m, b)
}
func (m *Content) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Content.Marshal(b, m, deterministic)
}
func (dst *Content) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Content.Merge(dst, src)
}
func (m *Content) XXX_Size() int {
	return xxx_messageInfo_Content.Size(m)
}
func (m *Content) XXX_DiscardUnknown() {
	xxx_messageInfo_Content.DiscardUnknown(m)
}

var xxx_messageInfo_Content proto.InternalMessageInfo

func (m *Content) GetType() Content_ContentType {
	if m != nil {
		return m.Type
	}
	return Content_Unknown
}

func (m *Content) GetTextNormal() *ImTextNormal {
	if m != nil {
		return m.TextNormal
	}
	return nil
}

func (m *Content) GetTextHlUrl() *ImTextWithHighlightUrl {
	if m != nil {
		return m.TextHlUrl
	}
	return nil
}

func (m *Content) GetOfficialMessage() *ImOfficialMessage {
	if m != nil {
		return m.OfficialMessage
	}
	return nil
}

func (m *Content) GetNewExtended() *ImNewExtended {
	if m != nil {
		return m.NewExtended
	}
	return nil
}

// Content::Text
type ImTextNormal struct {
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImTextNormal) Reset()         { *m = ImTextNormal{} }
func (m *ImTextNormal) String() string { return proto.CompactTextString(m) }
func (*ImTextNormal) ProtoMessage()    {}
func (*ImTextNormal) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{10}
}
func (m *ImTextNormal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImTextNormal.Unmarshal(m, b)
}
func (m *ImTextNormal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImTextNormal.Marshal(b, m, deterministic)
}
func (dst *ImTextNormal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImTextNormal.Merge(dst, src)
}
func (m *ImTextNormal) XXX_Size() int {
	return xxx_messageInfo_ImTextNormal.Size(m)
}
func (m *ImTextNormal) XXX_DiscardUnknown() {
	xxx_messageInfo_ImTextNormal.DiscardUnknown(m)
}

var xxx_messageInfo_ImTextNormal proto.InternalMessageInfo

func (m *ImTextNormal) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// Content::TextWithHighlightUrl
type ImTextWithHighlightUrl struct {
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Highlight            string   `protobuf:"bytes,2,opt,name=highlight,proto3" json:"highlight,omitempty"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImTextWithHighlightUrl) Reset()         { *m = ImTextWithHighlightUrl{} }
func (m *ImTextWithHighlightUrl) String() string { return proto.CompactTextString(m) }
func (*ImTextWithHighlightUrl) ProtoMessage()    {}
func (*ImTextWithHighlightUrl) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{11}
}
func (m *ImTextWithHighlightUrl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImTextWithHighlightUrl.Unmarshal(m, b)
}
func (m *ImTextWithHighlightUrl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImTextWithHighlightUrl.Marshal(b, m, deterministic)
}
func (dst *ImTextWithHighlightUrl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImTextWithHighlightUrl.Merge(dst, src)
}
func (m *ImTextWithHighlightUrl) XXX_Size() int {
	return xxx_messageInfo_ImTextWithHighlightUrl.Size(m)
}
func (m *ImTextWithHighlightUrl) XXX_DiscardUnknown() {
	xxx_messageInfo_ImTextWithHighlightUrl.DiscardUnknown(m)
}

var xxx_messageInfo_ImTextWithHighlightUrl proto.InternalMessageInfo

func (m *ImTextWithHighlightUrl) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ImTextWithHighlightUrl) GetHighlight() string {
	if m != nil {
		return m.Highlight
	}
	return ""
}

func (m *ImTextWithHighlightUrl) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

// Content::IM_CONTENT_NEW_EXTENDED
type ImNewExtended struct {
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImNewExtended) Reset()         { *m = ImNewExtended{} }
func (m *ImNewExtended) String() string { return proto.CompactTextString(m) }
func (*ImNewExtended) ProtoMessage()    {}
func (*ImNewExtended) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{12}
}
func (m *ImNewExtended) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImNewExtended.Unmarshal(m, b)
}
func (m *ImNewExtended) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImNewExtended.Marshal(b, m, deterministic)
}
func (dst *ImNewExtended) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImNewExtended.Merge(dst, src)
}
func (m *ImNewExtended) XXX_Size() int {
	return xxx_messageInfo_ImNewExtended.Size(m)
}
func (m *ImNewExtended) XXX_DiscardUnknown() {
	xxx_messageInfo_ImNewExtended.DiscardUnknown(m)
}

var xxx_messageInfo_ImNewExtended proto.InternalMessageInfo

func (m *ImNewExtended) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// Content::OfficialMessage
type ImOfficialMessage struct {
	MessageId            uint32                 `protobuf:"varint,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	Messages             []*OfficialMessageBody `protobuf:"bytes,2,rep,name=messages,proto3" json:"messages,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ImOfficialMessage) Reset()         { *m = ImOfficialMessage{} }
func (m *ImOfficialMessage) String() string { return proto.CompactTextString(m) }
func (*ImOfficialMessage) ProtoMessage()    {}
func (*ImOfficialMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{13}
}
func (m *ImOfficialMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImOfficialMessage.Unmarshal(m, b)
}
func (m *ImOfficialMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImOfficialMessage.Marshal(b, m, deterministic)
}
func (dst *ImOfficialMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImOfficialMessage.Merge(dst, src)
}
func (m *ImOfficialMessage) XXX_Size() int {
	return xxx_messageInfo_ImOfficialMessage.Size(m)
}
func (m *ImOfficialMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_ImOfficialMessage.DiscardUnknown(m)
}

var xxx_messageInfo_ImOfficialMessage proto.InternalMessageInfo

func (m *ImOfficialMessage) GetMessageId() uint32 {
	if m != nil {
		return m.MessageId
	}
	return 0
}

func (m *ImOfficialMessage) GetMessages() []*OfficialMessageBody {
	if m != nil {
		return m.Messages
	}
	return nil
}

// 对应 proto/pbfile/ga_base.proto : OfficialMessageBody
type OfficialMessageBody struct {
	Type                 OfficialMessageBody_OfficialMessageType `protobuf:"varint,1,opt,name=type,proto3,enum=sendim_bot.OfficialMessageBody_OfficialMessageType" json:"type,omitempty"`
	Title                string                                  `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Date                 uint32                                  `protobuf:"varint,3,opt,name=date,proto3" json:"date,omitempty"`
	Content              string                                  `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Intro                string                                  `protobuf:"bytes,5,opt,name=intro,proto3" json:"intro,omitempty"`
	Image                string                                  `protobuf:"bytes,6,opt,name=image,proto3" json:"image,omitempty"`
	CtrlBlock            *ControlBlock                           `protobuf:"bytes,7,opt,name=ctrl_block,json=ctrlBlock,proto3" json:"ctrl_block,omitempty"`
	AllowRetransmit      uint32                                  `protobuf:"varint,8,opt,name=allow_retransmit,json=allowRetransmit,proto3" json:"allow_retransmit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *OfficialMessageBody) Reset()         { *m = OfficialMessageBody{} }
func (m *OfficialMessageBody) String() string { return proto.CompactTextString(m) }
func (*OfficialMessageBody) ProtoMessage()    {}
func (*OfficialMessageBody) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{14}
}
func (m *OfficialMessageBody) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialMessageBody.Unmarshal(m, b)
}
func (m *OfficialMessageBody) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialMessageBody.Marshal(b, m, deterministic)
}
func (dst *OfficialMessageBody) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialMessageBody.Merge(dst, src)
}
func (m *OfficialMessageBody) XXX_Size() int {
	return xxx_messageInfo_OfficialMessageBody.Size(m)
}
func (m *OfficialMessageBody) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialMessageBody.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialMessageBody proto.InternalMessageInfo

func (m *OfficialMessageBody) GetType() OfficialMessageBody_OfficialMessageType {
	if m != nil {
		return m.Type
	}
	return OfficialMessageBody_Unknown
}

func (m *OfficialMessageBody) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *OfficialMessageBody) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *OfficialMessageBody) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *OfficialMessageBody) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *OfficialMessageBody) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *OfficialMessageBody) GetCtrlBlock() *ControlBlock {
	if m != nil {
		return m.CtrlBlock
	}
	return nil
}

func (m *OfficialMessageBody) GetAllowRetransmit() uint32 {
	if m != nil {
		return m.AllowRetransmit
	}
	return 0
}

// 对应 proto/pbfile/ga_base.proto : ControlBlock
type ControlBlock struct {
	Cmd                  uint32   `protobuf:"varint,1,opt,name=cmd,proto3" json:"cmd,omitempty"`
	CmdBody              []byte   `protobuf:"bytes,2,opt,name=cmd_body,json=cmdBody,proto3" json:"cmd_body,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ControlBlock) Reset()         { *m = ControlBlock{} }
func (m *ControlBlock) String() string { return proto.CompactTextString(m) }
func (*ControlBlock) ProtoMessage()    {}
func (*ControlBlock) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{15}
}
func (m *ControlBlock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ControlBlock.Unmarshal(m, b)
}
func (m *ControlBlock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ControlBlock.Marshal(b, m, deterministic)
}
func (dst *ControlBlock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ControlBlock.Merge(dst, src)
}
func (m *ControlBlock) XXX_Size() int {
	return xxx_messageInfo_ControlBlock.Size(m)
}
func (m *ControlBlock) XXX_DiscardUnknown() {
	xxx_messageInfo_ControlBlock.DiscardUnknown(m)
}

var xxx_messageInfo_ControlBlock proto.InternalMessageInfo

func (m *ControlBlock) GetCmd() uint32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

func (m *ControlBlock) GetCmdBody() []byte {
	if m != nil {
		return m.CmdBody
	}
	return nil
}

type ImAccount struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Alias                string   `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImAccount) Reset()         { *m = ImAccount{} }
func (m *ImAccount) String() string { return proto.CompactTextString(m) }
func (*ImAccount) ProtoMessage()    {}
func (*ImAccount) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{16}
}
func (m *ImAccount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImAccount.Unmarshal(m, b)
}
func (m *ImAccount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImAccount.Marshal(b, m, deterministic)
}
func (dst *ImAccount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImAccount.Merge(dst, src)
}
func (m *ImAccount) XXX_Size() int {
	return xxx_messageInfo_ImAccount.Size(m)
}
func (m *ImAccount) XXX_DiscardUnknown() {
	xxx_messageInfo_ImAccount.DiscardUnknown(m)
}

var xxx_messageInfo_ImAccount proto.InternalMessageInfo

func (m *ImAccount) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ImAccount) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ImAccount) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ImAccount) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

type Receipt struct {
	Id                   uint32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Seq                  uint32             `protobuf:"varint,2,opt,name=seq,proto3" json:"seq,omitempty"`
	Type                 Receipt_SuffixType `protobuf:"varint,3,opt,name=type,proto3,enum=sendim_bot.Receipt_SuffixType" json:"type,omitempty"`
	FromId               uint32             `protobuf:"varint,4,opt,name=from_id,json=fromId,proto3" json:"from_id,omitempty"`
	ClientMsgId          uint32             `protobuf:"varint,5,opt,name=client_msg_id,json=clientMsgId,proto3" json:"client_msg_id,omitempty"`
	SvrMsgId             uint32             `protobuf:"varint,6,opt,name=svr_msg_id,json=svrMsgId,proto3" json:"svr_msg_id,omitempty"`
	FromName             string             `protobuf:"bytes,7,opt,name=from_name,json=fromName,proto3" json:"from_name,omitempty"`
	ToName               string             `protobuf:"bytes,8,opt,name=to_name,json=toName,proto3" json:"to_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *Receipt) Reset()         { *m = Receipt{} }
func (m *Receipt) String() string { return proto.CompactTextString(m) }
func (*Receipt) ProtoMessage()    {}
func (*Receipt) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_bot_4f85d621da557f01, []int{17}
}
func (m *Receipt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Receipt.Unmarshal(m, b)
}
func (m *Receipt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Receipt.Marshal(b, m, deterministic)
}
func (dst *Receipt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Receipt.Merge(dst, src)
}
func (m *Receipt) XXX_Size() int {
	return xxx_messageInfo_Receipt.Size(m)
}
func (m *Receipt) XXX_DiscardUnknown() {
	xxx_messageInfo_Receipt.DiscardUnknown(m)
}

var xxx_messageInfo_Receipt proto.InternalMessageInfo

func (m *Receipt) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Receipt) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

func (m *Receipt) GetType() Receipt_SuffixType {
	if m != nil {
		return m.Type
	}
	return Receipt_Unknown
}

func (m *Receipt) GetFromId() uint32 {
	if m != nil {
		return m.FromId
	}
	return 0
}

func (m *Receipt) GetClientMsgId() uint32 {
	if m != nil {
		return m.ClientMsgId
	}
	return 0
}

func (m *Receipt) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

func (m *Receipt) GetFromName() string {
	if m != nil {
		return m.FromName
	}
	return ""
}

func (m *Receipt) GetToName() string {
	if m != nil {
		return m.ToName
	}
	return ""
}

func init() {
	proto.RegisterType((*SendTaskReq)(nil), "sendim_bot.SendTaskReq")
	proto.RegisterType((*SendTaskResp)(nil), "sendim_bot.SendTaskResp")
	proto.RegisterType((*StopTaskReq)(nil), "sendim_bot.StopTaskReq")
	proto.RegisterType((*StopTaskResp)(nil), "sendim_bot.StopTaskResp")
	proto.RegisterType((*RecallTaskReq)(nil), "sendim_bot.RecallTaskReq")
	proto.RegisterType((*RecallTaskResp)(nil), "sendim_bot.RecallTaskResp")
	proto.RegisterType((*ImMsg)(nil), "sendim_bot.ImMsg")
	proto.RegisterType((*Sender)(nil), "sendim_bot.Sender")
	proto.RegisterType((*Receiver)(nil), "sendim_bot.Receiver")
	proto.RegisterType((*Content)(nil), "sendim_bot.Content")
	proto.RegisterType((*ImTextNormal)(nil), "sendim_bot.ImTextNormal")
	proto.RegisterType((*ImTextWithHighlightUrl)(nil), "sendim_bot.ImTextWithHighlightUrl")
	proto.RegisterType((*ImNewExtended)(nil), "sendim_bot.ImNewExtended")
	proto.RegisterType((*ImOfficialMessage)(nil), "sendim_bot.ImOfficialMessage")
	proto.RegisterType((*OfficialMessageBody)(nil), "sendim_bot.OfficialMessageBody")
	proto.RegisterType((*ControlBlock)(nil), "sendim_bot.ControlBlock")
	proto.RegisterType((*ImAccount)(nil), "sendim_bot.ImAccount")
	proto.RegisterType((*Receipt)(nil), "sendim_bot.Receipt")
	proto.RegisterEnum("sendim_bot.ErrCode", ErrCode_name, ErrCode_value)
	proto.RegisterEnum("sendim_bot.Sender_SenderType", Sender_SenderType_name, Sender_SenderType_value)
	proto.RegisterEnum("sendim_bot.Receiver_ReceiverType", Receiver_ReceiverType_name, Receiver_ReceiverType_value)
	proto.RegisterEnum("sendim_bot.Content_ContentType", Content_ContentType_name, Content_ContentType_value)
	proto.RegisterEnum("sendim_bot.OfficialMessageBody_OfficialMessageType", OfficialMessageBody_OfficialMessageType_name, OfficialMessageBody_OfficialMessageType_value)
	proto.RegisterEnum("sendim_bot.Receipt_SuffixType", Receipt_SuffixType_name, Receipt_SuffixType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SendImBotClient is the client API for SendImBot service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SendImBotClient interface {
	// 发送消息
	SendTask(ctx context.Context, in *SendTaskReq, opts ...grpc.CallOption) (*SendTaskResp, error)
	// 停止发送，已收到的用户还会继续看到消息，未收到的用户不会看到消息
	StopTask(ctx context.Context, in *StopTaskReq, opts ...grpc.CallOption) (*StopTaskResp, error)
	// 撤回全部消息，包括未读和已读全部撤回，消息会直接删除不会有任何提示
	RecallTask(ctx context.Context, in *RecallTaskReq, opts ...grpc.CallOption) (*RecallTaskResp, error)
}

type sendImBotClient struct {
	cc *grpc.ClientConn
}

func NewSendImBotClient(cc *grpc.ClientConn) SendImBotClient {
	return &sendImBotClient{cc}
}

func (c *sendImBotClient) SendTask(ctx context.Context, in *SendTaskReq, opts ...grpc.CallOption) (*SendTaskResp, error) {
	out := new(SendTaskResp)
	err := c.cc.Invoke(ctx, "/sendim_bot.SendImBot/SendTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendImBotClient) StopTask(ctx context.Context, in *StopTaskReq, opts ...grpc.CallOption) (*StopTaskResp, error) {
	out := new(StopTaskResp)
	err := c.cc.Invoke(ctx, "/sendim_bot.SendImBot/StopTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendImBotClient) RecallTask(ctx context.Context, in *RecallTaskReq, opts ...grpc.CallOption) (*RecallTaskResp, error) {
	out := new(RecallTaskResp)
	err := c.cc.Invoke(ctx, "/sendim_bot.SendImBot/RecallTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SendImBotServer is the server API for SendImBot service.
type SendImBotServer interface {
	// 发送消息
	SendTask(context.Context, *SendTaskReq) (*SendTaskResp, error)
	// 停止发送，已收到的用户还会继续看到消息，未收到的用户不会看到消息
	StopTask(context.Context, *StopTaskReq) (*StopTaskResp, error)
	// 撤回全部消息，包括未读和已读全部撤回，消息会直接删除不会有任何提示
	RecallTask(context.Context, *RecallTaskReq) (*RecallTaskResp, error)
}

func RegisterSendImBotServer(s *grpc.Server, srv SendImBotServer) {
	s.RegisterService(&_SendImBot_serviceDesc, srv)
}

func _SendImBot_SendTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendImBotServer).SendTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sendim_bot.SendImBot/SendTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendImBotServer).SendTask(ctx, req.(*SendTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendImBot_StopTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendImBotServer).StopTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sendim_bot.SendImBot/StopTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendImBotServer).StopTask(ctx, req.(*StopTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendImBot_RecallTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecallTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendImBotServer).RecallTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sendim_bot.SendImBot/RecallTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendImBotServer).RecallTask(ctx, req.(*RecallTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SendImBot_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sendim_bot.SendImBot",
	HandlerType: (*SendImBotServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendTask",
			Handler:    _SendImBot_SendTask_Handler,
		},
		{
			MethodName: "StopTask",
			Handler:    _SendImBot_StopTask_Handler,
		},
		{
			MethodName: "RecallTask",
			Handler:    _SendImBot_RecallTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sendim/bot/sendim-bot.proto",
}

func init() {
	proto.RegisterFile("sendim/bot/sendim-bot.proto", fileDescriptor_sendim_bot_4f85d621da557f01)
}

var fileDescriptor_sendim_bot_4f85d621da557f01 = []byte{
	// 1240 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xcd, 0x6e, 0xdb, 0xc6,
	0x13, 0x37, 0x25, 0x59, 0x12, 0x87, 0x92, 0xcd, 0x6c, 0x8c, 0x7f, 0x18, 0x27, 0xf9, 0x37, 0x61,
	0xd0, 0xc2, 0x09, 0x10, 0xbb, 0xb5, 0x1b, 0x14, 0x45, 0x8a, 0xb6, 0x72, 0x22, 0x24, 0x6a, 0x23,
	0xdb, 0x58, 0xcb, 0x48, 0xdb, 0x0b, 0x41, 0x91, 0x2b, 0x79, 0x61, 0x92, 0xcb, 0x2c, 0xd7, 0x5f,
	0x40, 0x1f, 0xa4, 0x87, 0x1e, 0x7b, 0xeb, 0xa5, 0x0f, 0xd3, 0x7b, 0x5f, 0xa5, 0xd8, 0xe5, 0x4a,
	0x22, 0x25, 0x25, 0x6d, 0x91, 0x13, 0xe7, 0xe3, 0xc7, 0xd9, 0xd9, 0x99, 0xd9, 0x99, 0x81, 0x3b,
	0x19, 0x49, 0x42, 0x1a, 0xef, 0x0c, 0x99, 0xd8, 0xc9, 0xc9, 0x27, 0x43, 0x26, 0xb6, 0x53, 0xce,
	0x04, 0x43, 0x90, 0x4b, 0xbc, 0x21, 0x13, 0xee, 0x6f, 0x06, 0x58, 0xc7, 0x24, 0x09, 0x07, 0x7e,
	0x76, 0x86, 0xc9, 0x5b, 0x84, 0xa0, 0x26, 0xfc, 0xec, 0xcc, 0x31, 0xee, 0x1b, 0x5b, 0x26, 0x56,
	0x34, 0x7a, 0x0c, 0x75, 0xf9, 0x07, 0xe1, 0x4e, 0xe5, 0xbe, 0xb1, 0x65, 0xed, 0xa2, 0xed, 0x99,
	0x81, 0xed, 0x63, 0xa5, 0xc1, 0x1a, 0x81, 0x3e, 0x85, 0x26, 0x27, 0x01, 0xa1, 0x17, 0x84, 0x3b,
	0x55, 0x85, 0xde, 0x28, 0xa2, 0xb1, 0xd6, 0xe1, 0x29, 0x0a, 0x3d, 0x84, 0x6a, 0x9c, 0x8d, 0x9d,
	0x9a, 0x02, 0xdf, 0x28, 0x82, 0x7b, 0x71, 0x3f, 0x1b, 0x63, 0xa9, 0x75, 0x5f, 0x42, 0x6b, 0xe6,
	0x65, 0x96, 0xa2, 0x8f, 0xa1, 0x4a, 0x38, 0x57, 0x5e, 0xae, 0xed, 0xde, 0x2c, 0xfe, 0xd4, 0xe5,
	0xfc, 0x39, 0x0b, 0x09, 0x96, 0x7a, 0x64, 0xe7, 0xb6, 0x2b, 0xea, 0x32, 0xca, 0xd0, 0x03, 0xb0,
	0x8e, 0x05, 0x4b, 0xdf, 0x73, 0x5d, 0x75, 0xd6, 0x14, 0xf2, 0x21, 0x67, 0x3d, 0x84, 0x36, 0x26,
	0x81, 0x1f, 0x45, 0xef, 0x3b, 0xad, 0x07, 0x6b, 0x45, 0xd0, 0x87, 0x9c, 0xf7, 0x97, 0x01, 0xab,
	0x2a, 0x66, 0xe8, 0x09, 0x34, 0x02, 0x96, 0x08, 0x92, 0x08, 0x65, 0xc6, 0x2a, 0x9b, 0x79, 0x9e,
	0xab, 0xf0, 0x04, 0x83, 0x5c, 0x68, 0x07, 0x11, 0x25, 0x89, 0xf0, 0xe2, 0x6c, 0xec, 0xd1, 0x50,
	0x19, 0x6d, 0x63, 0x2b, 0x17, 0xf6, 0xb3, 0x71, 0x2f, 0x44, 0x9f, 0xc0, 0x7a, 0x01, 0x23, 0x68,
	0x4c, 0x54, 0x7e, 0xdb, 0xb8, 0x3d, 0x45, 0x0d, 0x68, 0x4c, 0xd0, 0x6d, 0x68, 0xfa, 0x69, 0xea,
	0x25, 0x7e, 0x4c, 0x54, 0x4e, 0x4d, 0xdc, 0xf0, 0xd3, 0xf4, 0xc0, 0x8f, 0x09, 0x7a, 0x00, 0x2d,
	0xa9, 0x4a, 0x23, 0x5f, 0x8c, 0x18, 0x8f, 0x9d, 0x55, 0xa5, 0xb6, 0xfc, 0x34, 0x3d, 0xd2, 0x22,
	0x74, 0x0f, 0x80, 0x5c, 0xa5, 0x94, 0x93, 0xd0, 0xf3, 0x85, 0x53, 0x57, 0x07, 0x98, 0x5a, 0xd2,
	0x11, 0xee, 0xcf, 0x50, 0xcf, 0xeb, 0x0d, 0x7d, 0x06, 0x35, 0x71, 0x9d, 0x12, 0x1d, 0xa5, 0x7b,
	0x8b, 0x15, 0xa9, 0x3f, 0x83, 0xeb, 0x94, 0x60, 0x05, 0x45, 0x6b, 0x50, 0x99, 0x5e, 0xad, 0x42,
	0x43, 0x77, 0x07, 0x60, 0x86, 0x41, 0x16, 0x34, 0x4e, 0x92, 0xb3, 0x84, 0x5d, 0x26, 0xf6, 0x0a,
	0x6a, 0x42, 0xed, 0x24, 0x23, 0xdc, 0x36, 0x10, 0x40, 0xfd, 0xe8, 0x7c, 0x18, 0xd1, 0xc0, 0xae,
	0xb8, 0xbf, 0x1a, 0xd0, 0x9c, 0x14, 0x30, 0x7a, 0x5a, 0x72, 0xe0, 0xc1, 0xb2, 0x22, 0x9f, 0x12,
	0x05, 0x27, 0x6e, 0x41, 0x83, 0x86, 0x5e, 0x44, 0x33, 0xe1, 0x54, 0xee, 0x57, 0xb7, 0xda, 0xb8,
	0x4e, 0xc3, 0xd7, 0x34, 0x13, 0xee, 0xb7, 0xd0, 0x2a, 0xc2, 0xff, 0x85, 0x3f, 0xa8, 0x0d, 0xe6,
	0x3e, 0x67, 0x7e, 0x18, 0xf8, 0x99, 0xb0, 0xab, 0xee, 0xef, 0x55, 0x68, 0xe8, 0xd4, 0xa2, 0xbd,
	0x92, 0x77, 0x1f, 0x2d, 0xc9, 0xfe, 0xe4, 0x5b, 0xf0, 0xed, 0x4b, 0xb0, 0x04, 0xb9, 0x12, 0x5e,
	0xc2, 0x78, 0xec, 0x47, 0xfa, 0xb1, 0x3b, 0xe5, 0x17, 0x39, 0x20, 0x57, 0xe2, 0x40, 0xe9, 0x31,
	0x88, 0x29, 0x8d, 0xf6, 0xf5, 0xaf, 0xa7, 0x91, 0x77, 0xce, 0x23, 0xfd, 0xf2, 0xdd, 0xc5, 0x5f,
	0xdf, 0x50, 0x71, 0xfa, 0x8a, 0x8e, 0x4f, 0x23, 0x3a, 0x3e, 0x15, 0x27, 0x3c, 0xc2, 0xa6, 0xfc,
	0xed, 0x55, 0x74, 0xc2, 0x23, 0xf4, 0x0a, 0x6c, 0x36, 0x1a, 0xd1, 0x80, 0xfa, 0x91, 0x17, 0x93,
	0x2c, 0xf3, 0xc7, 0x44, 0x77, 0x85, 0x7b, 0x65, 0x43, 0x87, 0x1a, 0xd5, 0xcf, 0x41, 0x78, 0x9d,
	0x95, 0x05, 0xe8, 0x2b, 0x68, 0x25, 0xe4, 0xd2, 0x23, 0x57, 0x42, 0xe6, 0x37, 0x54, 0x85, 0x66,
	0xed, 0xde, 0x2e, 0x5b, 0x39, 0x20, 0x97, 0x5d, 0x0d, 0xc0, 0x56, 0x32, 0x63, 0xdc, 0x10, 0xac,
	0x42, 0x6c, 0x16, 0x12, 0x21, 0xaf, 0x61, 0x1b, 0xc8, 0x81, 0x8d, 0x65, 0x17, 0xb2, 0x2b, 0xe8,
	0x26, 0xac, 0xcf, 0x79, 0x68, 0x57, 0xd1, 0x3a, 0x58, 0x85, 0x03, 0xed, 0x9a, 0xbb, 0x05, 0xad,
	0x62, 0x34, 0x91, 0x53, 0x7e, 0xb2, 0xe6, 0xf4, 0x75, 0xba, 0x43, 0xf8, 0xdf, 0xf2, 0xe0, 0xbd,
	0xfb, 0x1f, 0x74, 0x17, 0xcc, 0xd3, 0x09, 0x52, 0xb7, 0x88, 0x99, 0x40, 0xb6, 0x8e, 0x49, 0x96,
	0x4c, 0x2c, 0x49, 0xf7, 0x11, 0xb4, 0x4b, 0x11, 0x79, 0x8f, 0x3b, 0x0c, 0x6e, 0x2c, 0xa4, 0x40,
	0xbe, 0x5b, 0x9d, 0x32, 0xd9, 0x3e, 0x8c, 0xfc, 0xdd, 0x6a, 0x49, 0x2f, 0x44, 0xcf, 0xa0, 0xa9,
	0x99, 0x4c, 0x95, 0xbd, 0x55, 0x2e, 0xc9, 0x39, 0x6b, 0xfb, 0x2c, 0xbc, 0xc6, 0xd3, 0x1f, 0xdc,
	0x5f, 0xaa, 0x70, 0x73, 0x09, 0x02, 0xbd, 0x2c, 0xd5, 0xf8, 0xde, 0x3f, 0x18, 0x9c, 0x97, 0x15,
	0xea, 0x7e, 0x03, 0x56, 0x05, 0x15, 0x11, 0xd1, 0x81, 0xca, 0x19, 0xd9, 0xac, 0x43, 0x5f, 0x4c,
	0xba, 0x9c, 0xa2, 0x8b, 0x51, 0xa9, 0x95, 0x03, 0xbe, 0x01, 0xab, 0x34, 0x11, 0x9c, 0xe9, 0xa6,
	0x96, 0x33, 0x4a, 0x1a, 0xcb, 0x3a, 0xae, 0x6b, 0xa9, 0x64, 0xd0, 0x17, 0x00, 0x81, 0xe0, 0x91,
	0x37, 0x8c, 0x58, 0x70, 0xe6, 0x34, 0x16, 0x9f, 0x99, 0x2c, 0x3f, 0xce, 0xa2, 0x7d, 0xa9, 0xc7,
	0xa6, 0xc4, 0x2a, 0x12, 0x3d, 0x02, 0xdb, 0x8f, 0x22, 0x76, 0xe9, 0x71, 0x22, 0xb8, 0x9f, 0x64,
	0x31, 0x15, 0x4e, 0x53, 0xb9, 0xb7, 0xae, 0xe4, 0x78, 0x2a, 0x76, 0xc3, 0x85, 0x98, 0x2d, 0x2f,
	0xe6, 0xee, 0x0f, 0x03, 0xdb, 0x40, 0x1b, 0x60, 0x1f, 0xf5, 0x9e, 0x0f, 0x4e, 0x70, 0xd7, 0xeb,
	0x1c, 0xbc, 0xf0, 0x94, 0xb4, 0x82, 0x6e, 0x40, 0xfb, 0x65, 0xa7, 0xdf, 0xf5, 0x5e, 0x1c, 0xbe,
	0x39, 0x78, 0x7d, 0xd8, 0x79, 0x61, 0x57, 0x65, 0xcb, 0x91, 0x4a, 0xef, 0xbb, 0x93, 0xfe, 0x91,
	0x5d, 0x73, 0x9f, 0x41, 0xab, 0xe8, 0xab, 0x2c, 0xac, 0x20, 0x9e, 0xe4, 0x5f, 0x92, 0x72, 0x1c,
	0x04, 0x71, 0xe8, 0x0d, 0x59, 0x78, 0xad, 0xc2, 0xdb, 0xc2, 0x8d, 0x20, 0x0e, 0x65, 0x42, 0xdc,
	0x31, 0x98, 0xbd, 0xb8, 0x13, 0x04, 0xec, 0x3c, 0x11, 0xba, 0x39, 0x1b, 0x93, 0xe6, 0x2c, 0x23,
	0xed, 0xe7, 0x2a, 0x9d, 0x95, 0x09, 0x8b, 0x36, 0xa1, 0x99, 0xd0, 0xe0, 0x4c, 0x0d, 0x98, 0xbc,
	0x82, 0xa7, 0xbc, 0x8c, 0xb7, 0x1f, 0x51, 0x3f, 0xd3, 0xd9, 0xc9, 0x19, 0xf7, 0x8f, 0x0a, 0x34,
	0x54, 0x6f, 0x4d, 0x17, 0xcf, 0xb1, 0xa1, 0x9a, 0x91, 0xb7, 0x7a, 0x2a, 0x48, 0x12, 0xed, 0xea,
	0xb2, 0xaa, 0xaa, 0xb2, 0xfa, 0xff, 0x42, 0x63, 0x4f, 0xc5, 0xf6, 0xf1, 0xf9, 0x68, 0x44, 0xaf,
	0xca, 0x5d, 0x7d, 0xc4, 0x59, 0x2c, 0x6b, 0xbf, 0xa6, 0x2c, 0xd5, 0x25, 0xdb, 0x0b, 0x17, 0x27,
	0xeb, 0xea, 0xe2, 0x64, 0xbd, 0x0b, 0x90, 0x5d, 0xf0, 0x09, 0x20, 0x9f, 0x79, 0xcd, 0xec, 0x82,
	0xe7, 0xda, 0x3b, 0x60, 0x2a, 0xd3, 0xea, 0xbe, 0x8d, 0xfc, 0xbe, 0x52, 0xa0, 0x26, 0xea, 0x2d,
	0x68, 0x08, 0x96, 0xab, 0x9a, 0x4a, 0x55, 0x17, 0x4c, 0x2a, 0xdc, 0xaf, 0x01, 0x66, 0x4e, 0xfe,
	0xf7, 0x59, 0xf2, 0xf8, 0x73, 0x68, 0xe8, 0x65, 0x43, 0xa2, 0xba, 0x18, 0x7b, 0x87, 0xdf, 0xdb,
	0x2b, 0x12, 0x25, 0xe9, 0xa3, 0x0e, 0xee, 0xf4, 0x6d, 0x03, 0xad, 0x01, 0x48, 0xf6, 0xf8, 0xc7,
	0xe3, 0x41, 0xb7, 0x6f, 0x57, 0x76, 0xff, 0x34, 0xc0, 0x94, 0x23, 0xb5, 0x17, 0xef, 0x33, 0x81,
	0xbe, 0x81, 0xe6, 0x64, 0x67, 0x43, 0xb7, 0xe6, 0x07, 0xb4, 0x5e, 0x89, 0x36, 0x9d, 0xe5, 0x8a,
	0x2c, 0x75, 0x57, 0x94, 0x01, 0xbd, 0x88, 0xcd, 0x19, 0x98, 0x6d, 0x70, 0x73, 0x06, 0x0a, 0x7b,
	0x9b, 0xbb, 0x82, 0xba, 0x00, 0xb3, 0xdd, 0x0a, 0xdd, 0x9e, 0x4b, 0xe5, 0x6c, 0x31, 0xdb, 0xdc,
	0x7c, 0x97, 0x4a, 0x9a, 0xd9, 0x7f, 0xfa, 0xd3, 0xde, 0x98, 0x45, 0x7e, 0x32, 0xde, 0x7e, 0xba,
	0x2b, 0xc4, 0x76, 0xc0, 0xe2, 0x1d, 0xb5, 0x48, 0x07, 0x2c, 0xda, 0xc9, 0x08, 0xbf, 0xa0, 0x01,
	0xc9, 0xf4, 0x96, 0x5d, 0x58, 0xb6, 0x87, 0x75, 0x05, 0xda, 0xfb, 0x3b, 0x00, 0x00, 0xff, 0xff,
	0xff, 0xf5, 0xc2, 0x12, 0x8c, 0x0b, 0x00, 0x00,
}
