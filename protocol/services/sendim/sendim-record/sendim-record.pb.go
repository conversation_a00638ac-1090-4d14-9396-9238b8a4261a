// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sendim/record/sendim-record.proto

package sendim_record // import "golang.52tt.com/protocol/services/sendim/sendim-record"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SimplePushRecord struct {
	RecordKey            string   `protobuf:"bytes,1,opt,name=record_key,json=recordKey,proto3" json:"record_key,omitempty"`
	FromId               string   `protobuf:"bytes,2,opt,name=from_id,json=fromId,proto3" json:"from_id,omitempty"`
	ToId                 string   `protobuf:"bytes,3,opt,name=to_id,json=toId,proto3" json:"to_id,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	OpTime               int64    `protobuf:"varint,5,opt,name=op_time,json=opTime,proto3" json:"op_time,omitempty"`
	Content              []byte   `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	Error                string   `protobuf:"bytes,7,opt,name=error,proto3" json:"error,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimplePushRecord) Reset()         { *m = SimplePushRecord{} }
func (m *SimplePushRecord) String() string { return proto.CompactTextString(m) }
func (*SimplePushRecord) ProtoMessage()    {}
func (*SimplePushRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_record_31ad0e0351ad42f3, []int{0}
}
func (m *SimplePushRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimplePushRecord.Unmarshal(m, b)
}
func (m *SimplePushRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimplePushRecord.Marshal(b, m, deterministic)
}
func (dst *SimplePushRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimplePushRecord.Merge(dst, src)
}
func (m *SimplePushRecord) XXX_Size() int {
	return xxx_messageInfo_SimplePushRecord.Size(m)
}
func (m *SimplePushRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_SimplePushRecord.DiscardUnknown(m)
}

var xxx_messageInfo_SimplePushRecord proto.InternalMessageInfo

func (m *SimplePushRecord) GetRecordKey() string {
	if m != nil {
		return m.RecordKey
	}
	return ""
}

func (m *SimplePushRecord) GetFromId() string {
	if m != nil {
		return m.FromId
	}
	return ""
}

func (m *SimplePushRecord) GetToId() string {
	if m != nil {
		return m.ToId
	}
	return ""
}

func (m *SimplePushRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *SimplePushRecord) GetOpTime() int64 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

func (m *SimplePushRecord) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *SimplePushRecord) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type AddSimplePushRecordReq struct {
	Scene                string              `protobuf:"bytes,1,opt,name=scene,proto3" json:"scene,omitempty"`
	RecordList           []*SimplePushRecord `protobuf:"bytes,2,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AddSimplePushRecordReq) Reset()         { *m = AddSimplePushRecordReq{} }
func (m *AddSimplePushRecordReq) String() string { return proto.CompactTextString(m) }
func (*AddSimplePushRecordReq) ProtoMessage()    {}
func (*AddSimplePushRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_record_31ad0e0351ad42f3, []int{1}
}
func (m *AddSimplePushRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSimplePushRecordReq.Unmarshal(m, b)
}
func (m *AddSimplePushRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSimplePushRecordReq.Marshal(b, m, deterministic)
}
func (dst *AddSimplePushRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSimplePushRecordReq.Merge(dst, src)
}
func (m *AddSimplePushRecordReq) XXX_Size() int {
	return xxx_messageInfo_AddSimplePushRecordReq.Size(m)
}
func (m *AddSimplePushRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSimplePushRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSimplePushRecordReq proto.InternalMessageInfo

func (m *AddSimplePushRecordReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *AddSimplePushRecordReq) GetRecordList() []*SimplePushRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

type AddSimplePushRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSimplePushRecordResp) Reset()         { *m = AddSimplePushRecordResp{} }
func (m *AddSimplePushRecordResp) String() string { return proto.CompactTextString(m) }
func (*AddSimplePushRecordResp) ProtoMessage()    {}
func (*AddSimplePushRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_record_31ad0e0351ad42f3, []int{2}
}
func (m *AddSimplePushRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSimplePushRecordResp.Unmarshal(m, b)
}
func (m *AddSimplePushRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSimplePushRecordResp.Marshal(b, m, deterministic)
}
func (dst *AddSimplePushRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSimplePushRecordResp.Merge(dst, src)
}
func (m *AddSimplePushRecordResp) XXX_Size() int {
	return xxx_messageInfo_AddSimplePushRecordResp.Size(m)
}
func (m *AddSimplePushRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSimplePushRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSimplePushRecordResp proto.InternalMessageInfo

type GetSimplePushRecordReq struct {
	Scene                string   `protobuf:"bytes,1,opt,name=scene,proto3" json:"scene,omitempty"`
	RecordKeyPrefix      string   `protobuf:"bytes,2,opt,name=record_key_prefix,json=recordKeyPrefix,proto3" json:"record_key_prefix,omitempty"`
	FromId               string   `protobuf:"bytes,3,opt,name=from_id,json=fromId,proto3" json:"from_id,omitempty"`
	ToId                 string   `protobuf:"bytes,4,opt,name=to_id,json=toId,proto3" json:"to_id,omitempty"`
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	BeginTime            int64    `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	NoError              bool     `protobuf:"varint,8,opt,name=no_error,json=noError,proto3" json:"no_error,omitempty"`
	Offset               uint32   `protobuf:"varint,9,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,10,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSimplePushRecordReq) Reset()         { *m = GetSimplePushRecordReq{} }
func (m *GetSimplePushRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetSimplePushRecordReq) ProtoMessage()    {}
func (*GetSimplePushRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_record_31ad0e0351ad42f3, []int{3}
}
func (m *GetSimplePushRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSimplePushRecordReq.Unmarshal(m, b)
}
func (m *GetSimplePushRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSimplePushRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetSimplePushRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSimplePushRecordReq.Merge(dst, src)
}
func (m *GetSimplePushRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetSimplePushRecordReq.Size(m)
}
func (m *GetSimplePushRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSimplePushRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSimplePushRecordReq proto.InternalMessageInfo

func (m *GetSimplePushRecordReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *GetSimplePushRecordReq) GetRecordKeyPrefix() string {
	if m != nil {
		return m.RecordKeyPrefix
	}
	return ""
}

func (m *GetSimplePushRecordReq) GetFromId() string {
	if m != nil {
		return m.FromId
	}
	return ""
}

func (m *GetSimplePushRecordReq) GetToId() string {
	if m != nil {
		return m.ToId
	}
	return ""
}

func (m *GetSimplePushRecordReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *GetSimplePushRecordReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetSimplePushRecordReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetSimplePushRecordReq) GetNoError() bool {
	if m != nil {
		return m.NoError
	}
	return false
}

func (m *GetSimplePushRecordReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetSimplePushRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetSimplePushRecordResp struct {
	RecordList           []*SimplePushRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	Total                uint32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetSimplePushRecordResp) Reset()         { *m = GetSimplePushRecordResp{} }
func (m *GetSimplePushRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetSimplePushRecordResp) ProtoMessage()    {}
func (*GetSimplePushRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sendim_record_31ad0e0351ad42f3, []int{4}
}
func (m *GetSimplePushRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSimplePushRecordResp.Unmarshal(m, b)
}
func (m *GetSimplePushRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSimplePushRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetSimplePushRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSimplePushRecordResp.Merge(dst, src)
}
func (m *GetSimplePushRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetSimplePushRecordResp.Size(m)
}
func (m *GetSimplePushRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSimplePushRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSimplePushRecordResp proto.InternalMessageInfo

func (m *GetSimplePushRecordResp) GetRecordList() []*SimplePushRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetSimplePushRecordResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func init() {
	proto.RegisterType((*SimplePushRecord)(nil), "sendim_record.SimplePushRecord")
	proto.RegisterType((*AddSimplePushRecordReq)(nil), "sendim_record.AddSimplePushRecordReq")
	proto.RegisterType((*AddSimplePushRecordResp)(nil), "sendim_record.AddSimplePushRecordResp")
	proto.RegisterType((*GetSimplePushRecordReq)(nil), "sendim_record.GetSimplePushRecordReq")
	proto.RegisterType((*GetSimplePushRecordResp)(nil), "sendim_record.GetSimplePushRecordResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SendImRecordClient is the client API for SendImRecord service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SendImRecordClient interface {
	AddSimplePushRecord(ctx context.Context, in *AddSimplePushRecordReq, opts ...grpc.CallOption) (*AddSimplePushRecordResp, error)
	GetSimplePushRecord(ctx context.Context, in *GetSimplePushRecordReq, opts ...grpc.CallOption) (*GetSimplePushRecordResp, error)
}

type sendImRecordClient struct {
	cc *grpc.ClientConn
}

func NewSendImRecordClient(cc *grpc.ClientConn) SendImRecordClient {
	return &sendImRecordClient{cc}
}

func (c *sendImRecordClient) AddSimplePushRecord(ctx context.Context, in *AddSimplePushRecordReq, opts ...grpc.CallOption) (*AddSimplePushRecordResp, error) {
	out := new(AddSimplePushRecordResp)
	err := c.cc.Invoke(ctx, "/sendim_record.SendImRecord/AddSimplePushRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendImRecordClient) GetSimplePushRecord(ctx context.Context, in *GetSimplePushRecordReq, opts ...grpc.CallOption) (*GetSimplePushRecordResp, error) {
	out := new(GetSimplePushRecordResp)
	err := c.cc.Invoke(ctx, "/sendim_record.SendImRecord/GetSimplePushRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SendImRecordServer is the server API for SendImRecord service.
type SendImRecordServer interface {
	AddSimplePushRecord(context.Context, *AddSimplePushRecordReq) (*AddSimplePushRecordResp, error)
	GetSimplePushRecord(context.Context, *GetSimplePushRecordReq) (*GetSimplePushRecordResp, error)
}

func RegisterSendImRecordServer(s *grpc.Server, srv SendImRecordServer) {
	s.RegisterService(&_SendImRecord_serviceDesc, srv)
}

func _SendImRecord_AddSimplePushRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSimplePushRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendImRecordServer).AddSimplePushRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sendim_record.SendImRecord/AddSimplePushRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendImRecordServer).AddSimplePushRecord(ctx, req.(*AddSimplePushRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendImRecord_GetSimplePushRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSimplePushRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendImRecordServer).GetSimplePushRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sendim_record.SendImRecord/GetSimplePushRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendImRecordServer).GetSimplePushRecord(ctx, req.(*GetSimplePushRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SendImRecord_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sendim_record.SendImRecord",
	HandlerType: (*SendImRecordServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddSimplePushRecord",
			Handler:    _SendImRecord_AddSimplePushRecord_Handler,
		},
		{
			MethodName: "GetSimplePushRecord",
			Handler:    _SendImRecord_GetSimplePushRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sendim/record/sendim-record.proto",
}

func init() {
	proto.RegisterFile("sendim/record/sendim-record.proto", fileDescriptor_sendim_record_31ad0e0351ad42f3)
}

var fileDescriptor_sendim_record_31ad0e0351ad42f3 = []byte{
	// 486 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0x5f, 0x6f, 0xd3, 0x3c,
	0x14, 0xc6, 0x97, 0xfe, 0x49, 0xda, 0xb3, 0x55, 0xef, 0x8b, 0x87, 0x5a, 0x6f, 0xd2, 0x44, 0x88,
	0x04, 0x8a, 0x90, 0x68, 0xa5, 0x22, 0x10, 0x97, 0x80, 0x84, 0x50, 0x05, 0x17, 0x53, 0xc6, 0x15,
	0x37, 0x51, 0x17, 0x9f, 0x14, 0x8b, 0xc4, 0xf6, 0x6c, 0x83, 0xd8, 0x27, 0xe2, 0x8b, 0xf0, 0x39,
	0xf8, 0x2c, 0x28, 0x76, 0x36, 0x58, 0x9b, 0x49, 0xe3, 0xf2, 0x79, 0x8e, 0xe5, 0x27, 0xe7, 0x77,
	0x4e, 0x0c, 0x0f, 0x0d, 0x0a, 0xc6, 0xeb, 0x85, 0xc6, 0x42, 0x6a, 0xb6, 0xf0, 0xea, 0xa9, 0x57,
	0x73, 0xa5, 0xa5, 0x95, 0x64, 0xe2, 0xcd, 0xdc, 0x9b, 0xc9, 0xcf, 0x00, 0xfe, 0x3f, 0xe3, 0xb5,
	0xaa, 0xf0, 0xf4, 0xab, 0xf9, 0x9c, 0x39, 0x93, 0x9c, 0x00, 0xf8, 0x72, 0xfe, 0x05, 0x2f, 0x69,
	0x10, 0x07, 0xe9, 0x38, 0x1b, 0x7b, 0xe7, 0x3d, 0x5e, 0x92, 0x19, 0x44, 0xa5, 0x96, 0x75, 0xce,
	0x19, 0xed, 0xb9, 0x5a, 0xd8, 0xc8, 0x15, 0x23, 0x87, 0x30, 0xb4, 0xb2, 0xb1, 0xfb, 0xce, 0x1e,
	0x58, 0xb9, 0x62, 0xe4, 0x18, 0x46, 0x52, 0xa1, 0x5e, 0x5b, 0xa9, 0xe9, 0xc0, 0xf9, 0xd7, 0xba,
	0xb9, 0x49, 0xaa, 0xdc, 0xf2, 0x1a, 0xe9, 0x30, 0x0e, 0xd2, 0x7e, 0x16, 0x4a, 0xf5, 0x91, 0xd7,
	0x48, 0x28, 0x44, 0x85, 0x14, 0x16, 0x85, 0xa5, 0x61, 0x1c, 0xa4, 0x07, 0xd9, 0x95, 0x24, 0xf7,
	0x61, 0x88, 0x5a, 0x4b, 0x4d, 0x23, 0x77, 0x97, 0x17, 0x89, 0x82, 0xe9, 0x6b, 0xc6, 0xb6, 0x1b,
	0xc9, 0xf0, 0xa2, 0x39, 0x6f, 0x0a, 0x14, 0xd8, 0xb6, 0xe1, 0x05, 0x79, 0x05, 0xfb, 0x6d, 0x87,
	0x15, 0x37, 0x96, 0xf6, 0xe2, 0x7e, 0xba, 0xbf, 0x7c, 0x30, 0xbf, 0xc1, 0x66, 0xbe, 0x73, 0x5d,
	0x4b, 0xe5, 0x03, 0x37, 0x36, 0x39, 0x82, 0x59, 0x67, 0xa2, 0x51, 0xc9, 0x8f, 0x1e, 0x4c, 0xdf,
	0xa1, 0xbd, 0xfb, 0xd7, 0x3c, 0x81, 0x7b, 0x7f, 0x78, 0xe7, 0x4a, 0x63, 0xc9, 0xbf, 0xb7, 0x68,
	0xff, 0xbb, 0xc6, 0x7e, 0xea, 0xec, 0xbf, 0xe1, 0xf7, 0xbb, 0xe1, 0x0f, 0x6e, 0x81, 0x3f, 0xdc,
	0x82, 0x7f, 0x02, 0x70, 0x8e, 0x1b, 0x2e, 0x3c, 0xff, 0xd0, 0xf1, 0x1f, 0x3b, 0xc7, 0x8d, 0xe0,
	0x08, 0x46, 0x28, 0x98, 0x2f, 0x46, 0xae, 0x18, 0xa1, 0x60, 0x57, 0x25, 0x21, 0x73, 0x3f, 0x86,
	0x51, 0x1c, 0xa4, 0xa3, 0x2c, 0x12, 0xf2, 0x6d, 0x23, 0xc9, 0x14, 0x42, 0x59, 0x96, 0x06, 0x2d,
	0x1d, 0xc7, 0x41, 0x3a, 0xc9, 0x5a, 0xd5, 0x34, 0x5e, 0xf1, 0x9a, 0x5b, 0x0a, 0xce, 0xf6, 0x22,
	0xb9, 0x80, 0x59, 0x27, 0x28, 0xa3, 0xb6, 0x27, 0x14, 0xfc, 0xf3, 0x84, 0x9a, 0x48, 0x2b, 0xed,
	0xba, 0x72, 0x24, 0x27, 0x99, 0x17, 0xcb, 0x5f, 0x01, 0x1c, 0x9c, 0xa1, 0x60, 0xab, 0xba, 0x5d,
	0xf6, 0x12, 0x0e, 0x3b, 0x06, 0x49, 0x1e, 0x6d, 0x45, 0x75, 0xaf, 0xd7, 0xf1, 0xe3, 0xbb, 0x1c,
	0x33, 0x2a, 0xd9, 0x6b, 0x72, 0x3a, 0x7a, 0xdd, 0xc9, 0xe9, 0x5e, 0x9c, 0x9d, 0x9c, 0x5b, 0xb0,
	0x25, 0x7b, 0x6f, 0x5e, 0x7e, 0x7a, 0xb1, 0x91, 0xd5, 0x5a, 0x6c, 0xe6, 0xcf, 0x97, 0xd6, 0xce,
	0x0b, 0x59, 0x2f, 0xdc, 0x9f, 0x5f, 0xc8, 0x6a, 0x61, 0x50, 0x7f, 0xe3, 0x05, 0x9a, 0xf6, 0x65,
	0xb8, 0xf9, 0x40, 0x9c, 0x87, 0xee, 0xdc, 0xb3, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xf2, 0xae,
	0xd3, 0xdb, 0x46, 0x04, 0x00, 0x00,
}
