// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user-recall-award/user-recall-award.proto

package user_recall_award // import "golang.52tt.com/protocol/services/user-recall-award"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AwardReceiveStatus int32

const (
	AwardReceiveStatus_AWARD_RECEIVE_STATUS_UNKNOWN     AwardReceiveStatus = 0
	AwardReceiveStatus_AWARD_RECEIVE_STATUS_NOT_RECEIVE AwardReceiveStatus = 1
	AwardReceiveStatus_AWARD_RECEIVE_STATUS_RECEIVED    AwardReceiveStatus = 2
)

var AwardReceiveStatus_name = map[int32]string{
	0: "AWARD_RECEIVE_STATUS_UNKNOWN",
	1: "AWARD_RECEIVE_STATUS_NOT_RECEIVE",
	2: "AWARD_RECEIVE_STATUS_RECEIVED",
}
var AwardReceiveStatus_value = map[string]int32{
	"AWARD_RECEIVE_STATUS_UNKNOWN":     0,
	"AWARD_RECEIVE_STATUS_NOT_RECEIVE": 1,
	"AWARD_RECEIVE_STATUS_RECEIVED":    2,
}

func (x AwardReceiveStatus) String() string {
	return proto.EnumName(AwardReceiveStatus_name, int32(x))
}
func (AwardReceiveStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{0}
}

type RecallStatus int32

const (
	RecallStatus_RECALL_STATUS_UNKNOWN   RecallStatus = 0
	RecallStatus_RECALL_STATUS_DOING     RecallStatus = 1
	RecallStatus_RECALL_STATUS_HAS_AWARD RecallStatus = 2
	RecallStatus_RECALL_STATUS_END       RecallStatus = 3
)

var RecallStatus_name = map[int32]string{
	0: "RECALL_STATUS_UNKNOWN",
	1: "RECALL_STATUS_DOING",
	2: "RECALL_STATUS_HAS_AWARD",
	3: "RECALL_STATUS_END",
}
var RecallStatus_value = map[string]int32{
	"RECALL_STATUS_UNKNOWN":   0,
	"RECALL_STATUS_DOING":     1,
	"RECALL_STATUS_HAS_AWARD": 2,
	"RECALL_STATUS_END":       3,
}

func (x RecallStatus) String() string {
	return proto.EnumName(RecallStatus_name, int32(x))
}
func (RecallStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{1}
}

type AwardType int32

const (
	AwardType_AWARD_TYPE_UNKNOWN        AwardType = 0
	AwardType_AWARD_TYPE_LOGIN          AwardType = 1
	AwardType_AWARD_TYPE_CONTINUE_LOGIN AwardType = 2
	AwardType_AWARD_TYPE_CONSUME        AwardType = 3
	AwardType_AWARD_TYPE_INVITE         AwardType = 4
)

var AwardType_name = map[int32]string{
	0: "AWARD_TYPE_UNKNOWN",
	1: "AWARD_TYPE_LOGIN",
	2: "AWARD_TYPE_CONTINUE_LOGIN",
	3: "AWARD_TYPE_CONSUME",
	4: "AWARD_TYPE_INVITE",
}
var AwardType_value = map[string]int32{
	"AWARD_TYPE_UNKNOWN":        0,
	"AWARD_TYPE_LOGIN":          1,
	"AWARD_TYPE_CONTINUE_LOGIN": 2,
	"AWARD_TYPE_CONSUME":        3,
	"AWARD_TYPE_INVITE":         4,
}

func (x AwardType) String() string {
	return proto.EnumName(AwardType_name, int32(x))
}
func (AwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{2}
}

// 奖励物品类型
type AwardGiftType int32

const (
	AwardGiftType_AWARD_GIFT_TYPE_UNSPECIFIED AwardGiftType = 0
	AwardGiftType_AWARD_GIFT_TYPE_PACKAGE     AwardGiftType = 1
	AwardGiftType_AWARD_GIFT_TYPE_NOBLE_CARD  AwardGiftType = 2
	AwardGiftType_AWARD_GIFT_TYPE_MIC_STYLE   AwardGiftType = 3
	AwardGiftType_AWARD_GIFT_TYPE_HORSE       AwardGiftType = 4
)

var AwardGiftType_name = map[int32]string{
	0: "AWARD_GIFT_TYPE_UNSPECIFIED",
	1: "AWARD_GIFT_TYPE_PACKAGE",
	2: "AWARD_GIFT_TYPE_NOBLE_CARD",
	3: "AWARD_GIFT_TYPE_MIC_STYLE",
	4: "AWARD_GIFT_TYPE_HORSE",
}
var AwardGiftType_value = map[string]int32{
	"AWARD_GIFT_TYPE_UNSPECIFIED": 0,
	"AWARD_GIFT_TYPE_PACKAGE":     1,
	"AWARD_GIFT_TYPE_NOBLE_CARD":  2,
	"AWARD_GIFT_TYPE_MIC_STYLE":   3,
	"AWARD_GIFT_TYPE_HORSE":       4,
}

func (x AwardGiftType) String() string {
	return proto.EnumName(AwardGiftType_name, int32(x))
}
func (AwardGiftType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{3}
}

// 获取用户回归登录礼包信息
type GetRecallLoginAwardInfoReq struct {
	RecalledUid          uint32   `protobuf:"varint,1,opt,name=recalled_uid,json=recalledUid,proto3" json:"recalled_uid,omitempty"`
	ConsumeVal           uint32   `protobuf:"varint,2,opt,name=consume_val,json=consumeVal,proto3" json:"consume_val,omitempty"`
	LastLoginTime        int64    `protobuf:"varint,3,opt,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecallLoginAwardInfoReq) Reset()         { *m = GetRecallLoginAwardInfoReq{} }
func (m *GetRecallLoginAwardInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRecallLoginAwardInfoReq) ProtoMessage()    {}
func (*GetRecallLoginAwardInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{0}
}
func (m *GetRecallLoginAwardInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallLoginAwardInfoReq.Unmarshal(m, b)
}
func (m *GetRecallLoginAwardInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallLoginAwardInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetRecallLoginAwardInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallLoginAwardInfoReq.Merge(dst, src)
}
func (m *GetRecallLoginAwardInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetRecallLoginAwardInfoReq.Size(m)
}
func (m *GetRecallLoginAwardInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallLoginAwardInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallLoginAwardInfoReq proto.InternalMessageInfo

func (m *GetRecallLoginAwardInfoReq) GetRecalledUid() uint32 {
	if m != nil {
		return m.RecalledUid
	}
	return 0
}

func (m *GetRecallLoginAwardInfoReq) GetConsumeVal() uint32 {
	if m != nil {
		return m.ConsumeVal
	}
	return 0
}

func (m *GetRecallLoginAwardInfoReq) GetLastLoginTime() int64 {
	if m != nil {
		return m.LastLoginTime
	}
	return 0
}

type GetRecallLoginAwardInfoResp struct {
	MinVal               uint32     `protobuf:"varint,1,opt,name=min_val,json=minVal,proto3" json:"min_val,omitempty"`
	MaxVal               uint32     `protobuf:"varint,2,opt,name=max_val,json=maxVal,proto3" json:"max_val,omitempty"`
	Expire               uint32     `protobuf:"varint,3,opt,name=expire,proto3" json:"expire,omitempty"`
	AwardInfo            *AwardInfo `protobuf:"bytes,4,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetRecallLoginAwardInfoResp) Reset()         { *m = GetRecallLoginAwardInfoResp{} }
func (m *GetRecallLoginAwardInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRecallLoginAwardInfoResp) ProtoMessage()    {}
func (*GetRecallLoginAwardInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{1}
}
func (m *GetRecallLoginAwardInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallLoginAwardInfoResp.Unmarshal(m, b)
}
func (m *GetRecallLoginAwardInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallLoginAwardInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetRecallLoginAwardInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallLoginAwardInfoResp.Merge(dst, src)
}
func (m *GetRecallLoginAwardInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetRecallLoginAwardInfoResp.Size(m)
}
func (m *GetRecallLoginAwardInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallLoginAwardInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallLoginAwardInfoResp proto.InternalMessageInfo

func (m *GetRecallLoginAwardInfoResp) GetMinVal() uint32 {
	if m != nil {
		return m.MinVal
	}
	return 0
}

func (m *GetRecallLoginAwardInfoResp) GetMaxVal() uint32 {
	if m != nil {
		return m.MaxVal
	}
	return 0
}

func (m *GetRecallLoginAwardInfoResp) GetExpire() uint32 {
	if m != nil {
		return m.Expire
	}
	return 0
}

func (m *GetRecallLoginAwardInfoResp) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

type AwardInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftCnt              uint32   `protobuf:"varint,3,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt,omitempty"`
	GiftIcon             string   `protobuf:"bytes,4,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	GiftVal              uint32   `protobuf:"varint,5,opt,name=gift_val,json=giftVal,proto3" json:"gift_val,omitempty"`
	ExpireDay            uint32   `protobuf:"varint,6,opt,name=expire_day,json=expireDay,proto3" json:"expire_day,omitempty"`
	AwardType            uint32   `protobuf:"varint,7,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	GiftType             uint32   `protobuf:"varint,8,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardInfo) Reset()         { *m = AwardInfo{} }
func (m *AwardInfo) String() string { return proto.CompactTextString(m) }
func (*AwardInfo) ProtoMessage()    {}
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{2}
}
func (m *AwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardInfo.Unmarshal(m, b)
}
func (m *AwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardInfo.Marshal(b, m, deterministic)
}
func (dst *AwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardInfo.Merge(dst, src)
}
func (m *AwardInfo) XXX_Size() int {
	return xxx_messageInfo_AwardInfo.Size(m)
}
func (m *AwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardInfo proto.InternalMessageInfo

func (m *AwardInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *AwardInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *AwardInfo) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

func (m *AwardInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *AwardInfo) GetGiftVal() uint32 {
	if m != nil {
		return m.GiftVal
	}
	return 0
}

func (m *AwardInfo) GetExpireDay() uint32 {
	if m != nil {
		return m.ExpireDay
	}
	return 0
}

func (m *AwardInfo) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *AwardInfo) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

// 检查召回状态
type CheckRecallStatusReq struct {
	RecalledUid          uint32   `protobuf:"varint,1,opt,name=recalled_uid,json=recalledUid,proto3" json:"recalled_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckRecallStatusReq) Reset()         { *m = CheckRecallStatusReq{} }
func (m *CheckRecallStatusReq) String() string { return proto.CompactTextString(m) }
func (*CheckRecallStatusReq) ProtoMessage()    {}
func (*CheckRecallStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{3}
}
func (m *CheckRecallStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRecallStatusReq.Unmarshal(m, b)
}
func (m *CheckRecallStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRecallStatusReq.Marshal(b, m, deterministic)
}
func (dst *CheckRecallStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRecallStatusReq.Merge(dst, src)
}
func (m *CheckRecallStatusReq) XXX_Size() int {
	return xxx_messageInfo_CheckRecallStatusReq.Size(m)
}
func (m *CheckRecallStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRecallStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRecallStatusReq proto.InternalMessageInfo

func (m *CheckRecallStatusReq) GetRecalledUid() uint32 {
	if m != nil {
		return m.RecalledUid
	}
	return 0
}

type CheckRecallStatusResp struct {
	RecallStatus         uint32   `protobuf:"varint,1,opt,name=recall_status,json=recallStatus,proto3" json:"recall_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckRecallStatusResp) Reset()         { *m = CheckRecallStatusResp{} }
func (m *CheckRecallStatusResp) String() string { return proto.CompactTextString(m) }
func (*CheckRecallStatusResp) ProtoMessage()    {}
func (*CheckRecallStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{4}
}
func (m *CheckRecallStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRecallStatusResp.Unmarshal(m, b)
}
func (m *CheckRecallStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRecallStatusResp.Marshal(b, m, deterministic)
}
func (dst *CheckRecallStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRecallStatusResp.Merge(dst, src)
}
func (m *CheckRecallStatusResp) XXX_Size() int {
	return xxx_messageInfo_CheckRecallStatusResp.Size(m)
}
func (m *CheckRecallStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRecallStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRecallStatusResp proto.InternalMessageInfo

func (m *CheckRecallStatusResp) GetRecallStatus() uint32 {
	if m != nil {
		return m.RecallStatus
	}
	return 0
}

// 检查召回状态
type BatchCheckRecallStatusReq struct {
	RecalledUidList      []uint32 `protobuf:"varint,1,rep,packed,name=recalled_uid_list,json=recalledUidList,proto3" json:"recalled_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckRecallStatusReq) Reset()         { *m = BatchCheckRecallStatusReq{} }
func (m *BatchCheckRecallStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchCheckRecallStatusReq) ProtoMessage()    {}
func (*BatchCheckRecallStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{5}
}
func (m *BatchCheckRecallStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckRecallStatusReq.Unmarshal(m, b)
}
func (m *BatchCheckRecallStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckRecallStatusReq.Marshal(b, m, deterministic)
}
func (dst *BatchCheckRecallStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckRecallStatusReq.Merge(dst, src)
}
func (m *BatchCheckRecallStatusReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckRecallStatusReq.Size(m)
}
func (m *BatchCheckRecallStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckRecallStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckRecallStatusReq proto.InternalMessageInfo

func (m *BatchCheckRecallStatusReq) GetRecalledUidList() []uint32 {
	if m != nil {
		return m.RecalledUidList
	}
	return nil
}

type BatchCheckRecallStatusResp struct {
	RecallStatus         map[uint32]uint32 `protobuf:"bytes,1,rep,name=recall_status,json=recallStatus,proto3" json:"recall_status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchCheckRecallStatusResp) Reset()         { *m = BatchCheckRecallStatusResp{} }
func (m *BatchCheckRecallStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchCheckRecallStatusResp) ProtoMessage()    {}
func (*BatchCheckRecallStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{6}
}
func (m *BatchCheckRecallStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckRecallStatusResp.Unmarshal(m, b)
}
func (m *BatchCheckRecallStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckRecallStatusResp.Marshal(b, m, deterministic)
}
func (dst *BatchCheckRecallStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckRecallStatusResp.Merge(dst, src)
}
func (m *BatchCheckRecallStatusResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckRecallStatusResp.Size(m)
}
func (m *BatchCheckRecallStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckRecallStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckRecallStatusResp proto.InternalMessageInfo

func (m *BatchCheckRecallStatusResp) GetRecallStatus() map[uint32]uint32 {
	if m != nil {
		return m.RecallStatus
	}
	return nil
}

// 检查召回状态
type BatchCheckRecallStatusWithLastLoginTimeReq struct {
	RecalledUidList      []uint32 `protobuf:"varint,1,rep,packed,name=recalled_uid_list,json=recalledUidList,proto3" json:"recalled_uid_list,omitempty"`
	LastLoginTime        []uint32 `protobuf:"varint,2,rep,packed,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchCheckRecallStatusWithLastLoginTimeReq) Reset() {
	*m = BatchCheckRecallStatusWithLastLoginTimeReq{}
}
func (m *BatchCheckRecallStatusWithLastLoginTimeReq) String() string {
	return proto.CompactTextString(m)
}
func (*BatchCheckRecallStatusWithLastLoginTimeReq) ProtoMessage() {}
func (*BatchCheckRecallStatusWithLastLoginTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{7}
}
func (m *BatchCheckRecallStatusWithLastLoginTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeReq.Unmarshal(m, b)
}
func (m *BatchCheckRecallStatusWithLastLoginTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeReq.Marshal(b, m, deterministic)
}
func (dst *BatchCheckRecallStatusWithLastLoginTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeReq.Merge(dst, src)
}
func (m *BatchCheckRecallStatusWithLastLoginTimeReq) XXX_Size() int {
	return xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeReq.Size(m)
}
func (m *BatchCheckRecallStatusWithLastLoginTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeReq proto.InternalMessageInfo

func (m *BatchCheckRecallStatusWithLastLoginTimeReq) GetRecalledUidList() []uint32 {
	if m != nil {
		return m.RecalledUidList
	}
	return nil
}

func (m *BatchCheckRecallStatusWithLastLoginTimeReq) GetLastLoginTime() []uint32 {
	if m != nil {
		return m.LastLoginTime
	}
	return nil
}

type BatchCheckRecallStatusWithLastLoginTimeResp struct {
	RecallStatus         map[uint32]uint32 `protobuf:"bytes,1,rep,name=recall_status,json=recallStatus,proto3" json:"recall_status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchCheckRecallStatusWithLastLoginTimeResp) Reset() {
	*m = BatchCheckRecallStatusWithLastLoginTimeResp{}
}
func (m *BatchCheckRecallStatusWithLastLoginTimeResp) String() string {
	return proto.CompactTextString(m)
}
func (*BatchCheckRecallStatusWithLastLoginTimeResp) ProtoMessage() {}
func (*BatchCheckRecallStatusWithLastLoginTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{8}
}
func (m *BatchCheckRecallStatusWithLastLoginTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeResp.Unmarshal(m, b)
}
func (m *BatchCheckRecallStatusWithLastLoginTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeResp.Marshal(b, m, deterministic)
}
func (dst *BatchCheckRecallStatusWithLastLoginTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeResp.Merge(dst, src)
}
func (m *BatchCheckRecallStatusWithLastLoginTimeResp) XXX_Size() int {
	return xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeResp.Size(m)
}
func (m *BatchCheckRecallStatusWithLastLoginTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCheckRecallStatusWithLastLoginTimeResp proto.InternalMessageInfo

func (m *BatchCheckRecallStatusWithLastLoginTimeResp) GetRecallStatus() map[uint32]uint32 {
	if m != nil {
		return m.RecallStatus
	}
	return nil
}

type RecalledUserInfo struct {
	RecalledUid          uint32   `protobuf:"varint,1,opt,name=recalled_uid,json=recalledUid,proto3" json:"recalled_uid,omitempty"`
	ConsumeVal           uint32   `protobuf:"varint,2,opt,name=consume_val,json=consumeVal,proto3" json:"consume_val,omitempty"`
	LastLoginTime        int64    `protobuf:"varint,3,opt,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecalledUserInfo) Reset()         { *m = RecalledUserInfo{} }
func (m *RecalledUserInfo) String() string { return proto.CompactTextString(m) }
func (*RecalledUserInfo) ProtoMessage()    {}
func (*RecalledUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{9}
}
func (m *RecalledUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecalledUserInfo.Unmarshal(m, b)
}
func (m *RecalledUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecalledUserInfo.Marshal(b, m, deterministic)
}
func (dst *RecalledUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecalledUserInfo.Merge(dst, src)
}
func (m *RecalledUserInfo) XXX_Size() int {
	return xxx_messageInfo_RecalledUserInfo.Size(m)
}
func (m *RecalledUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecalledUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecalledUserInfo proto.InternalMessageInfo

func (m *RecalledUserInfo) GetRecalledUid() uint32 {
	if m != nil {
		return m.RecalledUid
	}
	return 0
}

func (m *RecalledUserInfo) GetConsumeVal() uint32 {
	if m != nil {
		return m.ConsumeVal
	}
	return 0
}

func (m *RecalledUserInfo) GetLastLoginTime() int64 {
	if m != nil {
		return m.LastLoginTime
	}
	return 0
}

// 批量查询邀请奖励
type BatchGetInviteAwardInfoReq struct {
	RecalledUser         []*RecalledUserInfo `protobuf:"bytes,1,rep,name=recalled_user,json=recalledUser,proto3" json:"recalled_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetInviteAwardInfoReq) Reset()         { *m = BatchGetInviteAwardInfoReq{} }
func (m *BatchGetInviteAwardInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetInviteAwardInfoReq) ProtoMessage()    {}
func (*BatchGetInviteAwardInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{10}
}
func (m *BatchGetInviteAwardInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetInviteAwardInfoReq.Unmarshal(m, b)
}
func (m *BatchGetInviteAwardInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetInviteAwardInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetInviteAwardInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetInviteAwardInfoReq.Merge(dst, src)
}
func (m *BatchGetInviteAwardInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetInviteAwardInfoReq.Size(m)
}
func (m *BatchGetInviteAwardInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetInviteAwardInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetInviteAwardInfoReq proto.InternalMessageInfo

func (m *BatchGetInviteAwardInfoReq) GetRecalledUser() []*RecalledUserInfo {
	if m != nil {
		return m.RecalledUser
	}
	return nil
}

type InviteAwardInfo struct {
	RecalledUid          uint32     `protobuf:"varint,1,opt,name=recalled_uid,json=recalledUid,proto3" json:"recalled_uid,omitempty"`
	AwardInfo            *AwardInfo `protobuf:"bytes,2,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *InviteAwardInfo) Reset()         { *m = InviteAwardInfo{} }
func (m *InviteAwardInfo) String() string { return proto.CompactTextString(m) }
func (*InviteAwardInfo) ProtoMessage()    {}
func (*InviteAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{11}
}
func (m *InviteAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteAwardInfo.Unmarshal(m, b)
}
func (m *InviteAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteAwardInfo.Marshal(b, m, deterministic)
}
func (dst *InviteAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteAwardInfo.Merge(dst, src)
}
func (m *InviteAwardInfo) XXX_Size() int {
	return xxx_messageInfo_InviteAwardInfo.Size(m)
}
func (m *InviteAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InviteAwardInfo proto.InternalMessageInfo

func (m *InviteAwardInfo) GetRecalledUid() uint32 {
	if m != nil {
		return m.RecalledUid
	}
	return 0
}

func (m *InviteAwardInfo) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

type BatchGetInviteAwardInfoResp struct {
	AwardInfos           []*InviteAwardInfo `protobuf:"bytes,1,rep,name=award_infos,json=awardInfos,proto3" json:"award_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetInviteAwardInfoResp) Reset()         { *m = BatchGetInviteAwardInfoResp{} }
func (m *BatchGetInviteAwardInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetInviteAwardInfoResp) ProtoMessage()    {}
func (*BatchGetInviteAwardInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{12}
}
func (m *BatchGetInviteAwardInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetInviteAwardInfoResp.Unmarshal(m, b)
}
func (m *BatchGetInviteAwardInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetInviteAwardInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetInviteAwardInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetInviteAwardInfoResp.Merge(dst, src)
}
func (m *BatchGetInviteAwardInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetInviteAwardInfoResp.Size(m)
}
func (m *BatchGetInviteAwardInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetInviteAwardInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetInviteAwardInfoResp proto.InternalMessageInfo

func (m *BatchGetInviteAwardInfoResp) GetAwardInfos() []*InviteAwardInfo {
	if m != nil {
		return m.AwardInfos
	}
	return nil
}

// 获取回归奖励信息
type GetReturnAwardInfoReq struct {
	RecalledUid          uint32   `protobuf:"varint,1,opt,name=recalled_uid,json=recalledUid,proto3" json:"recalled_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReturnAwardInfoReq) Reset()         { *m = GetReturnAwardInfoReq{} }
func (m *GetReturnAwardInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetReturnAwardInfoReq) ProtoMessage()    {}
func (*GetReturnAwardInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{13}
}
func (m *GetReturnAwardInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReturnAwardInfoReq.Unmarshal(m, b)
}
func (m *GetReturnAwardInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReturnAwardInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetReturnAwardInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReturnAwardInfoReq.Merge(dst, src)
}
func (m *GetReturnAwardInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetReturnAwardInfoReq.Size(m)
}
func (m *GetReturnAwardInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReturnAwardInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetReturnAwardInfoReq proto.InternalMessageInfo

func (m *GetReturnAwardInfoReq) GetRecalledUid() uint32 {
	if m != nil {
		return m.RecalledUid
	}
	return 0
}

type ReturnConsumeAwardInfo struct {
	AwardInfo            *AwardInfo `protobuf:"bytes,1,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	ReceiveVal           uint32     `protobuf:"varint,2,opt,name=receive_val,json=receiveVal,proto3" json:"receive_val,omitempty"`
	ReceiveStatus        uint32     `protobuf:"varint,3,opt,name=receive_status,json=receiveStatus,proto3" json:"receive_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ReturnConsumeAwardInfo) Reset()         { *m = ReturnConsumeAwardInfo{} }
func (m *ReturnConsumeAwardInfo) String() string { return proto.CompactTextString(m) }
func (*ReturnConsumeAwardInfo) ProtoMessage()    {}
func (*ReturnConsumeAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{14}
}
func (m *ReturnConsumeAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReturnConsumeAwardInfo.Unmarshal(m, b)
}
func (m *ReturnConsumeAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReturnConsumeAwardInfo.Marshal(b, m, deterministic)
}
func (dst *ReturnConsumeAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReturnConsumeAwardInfo.Merge(dst, src)
}
func (m *ReturnConsumeAwardInfo) XXX_Size() int {
	return xxx_messageInfo_ReturnConsumeAwardInfo.Size(m)
}
func (m *ReturnConsumeAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReturnConsumeAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReturnConsumeAwardInfo proto.InternalMessageInfo

func (m *ReturnConsumeAwardInfo) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *ReturnConsumeAwardInfo) GetReceiveVal() uint32 {
	if m != nil {
		return m.ReceiveVal
	}
	return 0
}

func (m *ReturnConsumeAwardInfo) GetReceiveStatus() uint32 {
	if m != nil {
		return m.ReceiveStatus
	}
	return 0
}

type GetReturnAwardInfoResp struct {
	TaskLimitedTime         uint32                    `protobuf:"varint,1,opt,name=task_limited_time,json=taskLimitedTime,proto3" json:"task_limited_time,omitempty"`
	ConLoginDay             uint32                    `protobuf:"varint,2,opt,name=con_login_day,json=conLoginDay,proto3" json:"con_login_day,omitempty"`
	MaxLoginDay             uint32                    `protobuf:"varint,3,opt,name=max_login_day,json=maxLoginDay,proto3" json:"max_login_day,omitempty"`
	LoginAward              []*AwardInfo              `protobuf:"bytes,4,rep,name=login_award,json=loginAward,proto3" json:"login_award,omitempty"`
	LoginAwardReceiveStatus uint32                    `protobuf:"varint,5,opt,name=login_award_receive_status,json=loginAwardReceiveStatus,proto3" json:"login_award_receive_status,omitempty"`
	ConConsumeVal           uint32                    `protobuf:"varint,6,opt,name=con_consume_val,json=conConsumeVal,proto3" json:"con_consume_val,omitempty"`
	MaxConsumeVal           uint32                    `protobuf:"varint,7,opt,name=max_consume_val,json=maxConsumeVal,proto3" json:"max_consume_val,omitempty"`
	ConsumeAward            []*ReturnConsumeAwardInfo `protobuf:"bytes,8,rep,name=consume_award,json=consumeAward,proto3" json:"consume_award,omitempty"`
	AwardLimitedTime        uint32                    `protobuf:"varint,9,opt,name=award_limited_time,json=awardLimitedTime,proto3" json:"award_limited_time,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                  `json:"-"`
	XXX_unrecognized        []byte                    `json:"-"`
	XXX_sizecache           int32                     `json:"-"`
}

func (m *GetReturnAwardInfoResp) Reset()         { *m = GetReturnAwardInfoResp{} }
func (m *GetReturnAwardInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetReturnAwardInfoResp) ProtoMessage()    {}
func (*GetReturnAwardInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{15}
}
func (m *GetReturnAwardInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReturnAwardInfoResp.Unmarshal(m, b)
}
func (m *GetReturnAwardInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReturnAwardInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetReturnAwardInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReturnAwardInfoResp.Merge(dst, src)
}
func (m *GetReturnAwardInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetReturnAwardInfoResp.Size(m)
}
func (m *GetReturnAwardInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReturnAwardInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReturnAwardInfoResp proto.InternalMessageInfo

func (m *GetReturnAwardInfoResp) GetTaskLimitedTime() uint32 {
	if m != nil {
		return m.TaskLimitedTime
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetConLoginDay() uint32 {
	if m != nil {
		return m.ConLoginDay
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetMaxLoginDay() uint32 {
	if m != nil {
		return m.MaxLoginDay
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetLoginAward() []*AwardInfo {
	if m != nil {
		return m.LoginAward
	}
	return nil
}

func (m *GetReturnAwardInfoResp) GetLoginAwardReceiveStatus() uint32 {
	if m != nil {
		return m.LoginAwardReceiveStatus
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetConConsumeVal() uint32 {
	if m != nil {
		return m.ConConsumeVal
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetMaxConsumeVal() uint32 {
	if m != nil {
		return m.MaxConsumeVal
	}
	return 0
}

func (m *GetReturnAwardInfoResp) GetConsumeAward() []*ReturnConsumeAwardInfo {
	if m != nil {
		return m.ConsumeAward
	}
	return nil
}

func (m *GetReturnAwardInfoResp) GetAwardLimitedTime() uint32 {
	if m != nil {
		return m.AwardLimitedTime
	}
	return 0
}

type GetInviteAwardReq struct {
	RecalledUid          uint32   `protobuf:"varint,1,opt,name=recalled_uid,json=recalledUid,proto3" json:"recalled_uid,omitempty"`
	InviteUid            uint32   `protobuf:"varint,2,opt,name=invite_uid,json=inviteUid,proto3" json:"invite_uid,omitempty"`
	LastLoginTime        int64    `protobuf:"varint,3,opt,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInviteAwardReq) Reset()         { *m = GetInviteAwardReq{} }
func (m *GetInviteAwardReq) String() string { return proto.CompactTextString(m) }
func (*GetInviteAwardReq) ProtoMessage()    {}
func (*GetInviteAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{16}
}
func (m *GetInviteAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInviteAwardReq.Unmarshal(m, b)
}
func (m *GetInviteAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInviteAwardReq.Marshal(b, m, deterministic)
}
func (dst *GetInviteAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInviteAwardReq.Merge(dst, src)
}
func (m *GetInviteAwardReq) XXX_Size() int {
	return xxx_messageInfo_GetInviteAwardReq.Size(m)
}
func (m *GetInviteAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInviteAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInviteAwardReq proto.InternalMessageInfo

func (m *GetInviteAwardReq) GetRecalledUid() uint32 {
	if m != nil {
		return m.RecalledUid
	}
	return 0
}

func (m *GetInviteAwardReq) GetInviteUid() uint32 {
	if m != nil {
		return m.InviteUid
	}
	return 0
}

func (m *GetInviteAwardReq) GetLastLoginTime() int64 {
	if m != nil {
		return m.LastLoginTime
	}
	return 0
}

type GetInviteAwardResp struct {
	AwardInfo            *AwardInfo `protobuf:"bytes,1,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetInviteAwardResp) Reset()         { *m = GetInviteAwardResp{} }
func (m *GetInviteAwardResp) String() string { return proto.CompactTextString(m) }
func (*GetInviteAwardResp) ProtoMessage()    {}
func (*GetInviteAwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{17}
}
func (m *GetInviteAwardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInviteAwardResp.Unmarshal(m, b)
}
func (m *GetInviteAwardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInviteAwardResp.Marshal(b, m, deterministic)
}
func (dst *GetInviteAwardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInviteAwardResp.Merge(dst, src)
}
func (m *GetInviteAwardResp) XXX_Size() int {
	return xxx_messageInfo_GetInviteAwardResp.Size(m)
}
func (m *GetInviteAwardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInviteAwardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInviteAwardResp proto.InternalMessageInfo

func (m *GetInviteAwardResp) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

type GetReturnAwardReq struct {
	RecalledUid          uint32   `protobuf:"varint,1,opt,name=recalled_uid,json=recalledUid,proto3" json:"recalled_uid,omitempty"`
	AwardType            uint32   `protobuf:"varint,2,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	ConsumeLv            uint32   `protobuf:"varint,3,opt,name=consume_lv,json=consumeLv,proto3" json:"consume_lv,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReturnAwardReq) Reset()         { *m = GetReturnAwardReq{} }
func (m *GetReturnAwardReq) String() string { return proto.CompactTextString(m) }
func (*GetReturnAwardReq) ProtoMessage()    {}
func (*GetReturnAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{18}
}
func (m *GetReturnAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReturnAwardReq.Unmarshal(m, b)
}
func (m *GetReturnAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReturnAwardReq.Marshal(b, m, deterministic)
}
func (dst *GetReturnAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReturnAwardReq.Merge(dst, src)
}
func (m *GetReturnAwardReq) XXX_Size() int {
	return xxx_messageInfo_GetReturnAwardReq.Size(m)
}
func (m *GetReturnAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReturnAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetReturnAwardReq proto.InternalMessageInfo

func (m *GetReturnAwardReq) GetRecalledUid() uint32 {
	if m != nil {
		return m.RecalledUid
	}
	return 0
}

func (m *GetReturnAwardReq) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *GetReturnAwardReq) GetConsumeLv() uint32 {
	if m != nil {
		return m.ConsumeLv
	}
	return 0
}

type GetReturnAwardResp struct {
	AwardInfo            *AwardInfo `protobuf:"bytes,1,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetReturnAwardResp) Reset()         { *m = GetReturnAwardResp{} }
func (m *GetReturnAwardResp) String() string { return proto.CompactTextString(m) }
func (*GetReturnAwardResp) ProtoMessage()    {}
func (*GetReturnAwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{19}
}
func (m *GetReturnAwardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReturnAwardResp.Unmarshal(m, b)
}
func (m *GetReturnAwardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReturnAwardResp.Marshal(b, m, deterministic)
}
func (dst *GetReturnAwardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReturnAwardResp.Merge(dst, src)
}
func (m *GetReturnAwardResp) XXX_Size() int {
	return xxx_messageInfo_GetReturnAwardResp.Size(m)
}
func (m *GetReturnAwardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReturnAwardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReturnAwardResp proto.InternalMessageInfo

func (m *GetReturnAwardResp) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

type GetMostExpAwardInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMostExpAwardInfoReq) Reset()         { *m = GetMostExpAwardInfoReq{} }
func (m *GetMostExpAwardInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMostExpAwardInfoReq) ProtoMessage()    {}
func (*GetMostExpAwardInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{20}
}
func (m *GetMostExpAwardInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMostExpAwardInfoReq.Unmarshal(m, b)
}
func (m *GetMostExpAwardInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMostExpAwardInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetMostExpAwardInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMostExpAwardInfoReq.Merge(dst, src)
}
func (m *GetMostExpAwardInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetMostExpAwardInfoReq.Size(m)
}
func (m *GetMostExpAwardInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMostExpAwardInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMostExpAwardInfoReq proto.InternalMessageInfo

type GetMostExpAwardInfoResp struct {
	AwardInfoList        []*AwardInfo `protobuf:"bytes,1,rep,name=award_info_list,json=awardInfoList,proto3" json:"award_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMostExpAwardInfoResp) Reset()         { *m = GetMostExpAwardInfoResp{} }
func (m *GetMostExpAwardInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMostExpAwardInfoResp) ProtoMessage()    {}
func (*GetMostExpAwardInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{21}
}
func (m *GetMostExpAwardInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMostExpAwardInfoResp.Unmarshal(m, b)
}
func (m *GetMostExpAwardInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMostExpAwardInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetMostExpAwardInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMostExpAwardInfoResp.Merge(dst, src)
}
func (m *GetMostExpAwardInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetMostExpAwardInfoResp.Size(m)
}
func (m *GetMostExpAwardInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMostExpAwardInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMostExpAwardInfoResp proto.InternalMessageInfo

func (m *GetMostExpAwardInfoResp) GetAwardInfoList() []*AwardInfo {
	if m != nil {
		return m.AwardInfoList
	}
	return nil
}

// 邀请奖励
type InviteAward struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MinVal               uint32       `protobuf:"varint,2,opt,name=min_val,json=minVal,proto3" json:"min_val,omitempty"`
	MaxVal               uint32       `protobuf:"varint,3,opt,name=max_val,json=maxVal,proto3" json:"max_val,omitempty"`
	Award                *AwardConfig `protobuf:"bytes,4,opt,name=award,proto3" json:"award,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *InviteAward) Reset()         { *m = InviteAward{} }
func (m *InviteAward) String() string { return proto.CompactTextString(m) }
func (*InviteAward) ProtoMessage()    {}
func (*InviteAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{22}
}
func (m *InviteAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteAward.Unmarshal(m, b)
}
func (m *InviteAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteAward.Marshal(b, m, deterministic)
}
func (dst *InviteAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteAward.Merge(dst, src)
}
func (m *InviteAward) XXX_Size() int {
	return xxx_messageInfo_InviteAward.Size(m)
}
func (m *InviteAward) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteAward.DiscardUnknown(m)
}

var xxx_messageInfo_InviteAward proto.InternalMessageInfo

func (m *InviteAward) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *InviteAward) GetMinVal() uint32 {
	if m != nil {
		return m.MinVal
	}
	return 0
}

func (m *InviteAward) GetMaxVal() uint32 {
	if m != nil {
		return m.MaxVal
	}
	return 0
}

func (m *InviteAward) GetAward() *AwardConfig {
	if m != nil {
		return m.Award
	}
	return nil
}

type InviteAwardList struct {
	List                 []*InviteAward `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *InviteAwardList) Reset()         { *m = InviteAwardList{} }
func (m *InviteAwardList) String() string { return proto.CompactTextString(m) }
func (*InviteAwardList) ProtoMessage()    {}
func (*InviteAwardList) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{23}
}
func (m *InviteAwardList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteAwardList.Unmarshal(m, b)
}
func (m *InviteAwardList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteAwardList.Marshal(b, m, deterministic)
}
func (dst *InviteAwardList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteAwardList.Merge(dst, src)
}
func (m *InviteAwardList) XXX_Size() int {
	return xxx_messageInfo_InviteAwardList.Size(m)
}
func (m *InviteAwardList) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteAwardList.DiscardUnknown(m)
}

var xxx_messageInfo_InviteAwardList proto.InternalMessageInfo

func (m *InviteAwardList) GetList() []*InviteAward {
	if m != nil {
		return m.List
	}
	return nil
}

// 运营后台奖励信息
type AwardConfig struct {
	GiftType             uint32   `protobuf:"varint,1,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	GiftId               string   `protobuf:"bytes,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	DayCount             uint32   `protobuf:"varint,3,opt,name=day_count,json=dayCount,proto3" json:"day_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardConfig) Reset()         { *m = AwardConfig{} }
func (m *AwardConfig) String() string { return proto.CompactTextString(m) }
func (*AwardConfig) ProtoMessage()    {}
func (*AwardConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{24}
}
func (m *AwardConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardConfig.Unmarshal(m, b)
}
func (m *AwardConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardConfig.Marshal(b, m, deterministic)
}
func (dst *AwardConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardConfig.Merge(dst, src)
}
func (m *AwardConfig) XXX_Size() int {
	return xxx_messageInfo_AwardConfig.Size(m)
}
func (m *AwardConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardConfig.DiscardUnknown(m)
}

var xxx_messageInfo_AwardConfig proto.InternalMessageInfo

func (m *AwardConfig) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *AwardConfig) GetGiftId() string {
	if m != nil {
		return m.GiftId
	}
	return ""
}

func (m *AwardConfig) GetDayCount() uint32 {
	if m != nil {
		return m.DayCount
	}
	return 0
}

// 回归奖励
type ReturnedAwardChance struct {
	Chance               uint32       `protobuf:"varint,1,opt,name=chance,proto3" json:"chance,omitempty"`
	Award                *AwardConfig `protobuf:"bytes,2,opt,name=award,proto3" json:"award,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReturnedAwardChance) Reset()         { *m = ReturnedAwardChance{} }
func (m *ReturnedAwardChance) String() string { return proto.CompactTextString(m) }
func (*ReturnedAwardChance) ProtoMessage()    {}
func (*ReturnedAwardChance) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{25}
}
func (m *ReturnedAwardChance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReturnedAwardChance.Unmarshal(m, b)
}
func (m *ReturnedAwardChance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReturnedAwardChance.Marshal(b, m, deterministic)
}
func (dst *ReturnedAwardChance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReturnedAwardChance.Merge(dst, src)
}
func (m *ReturnedAwardChance) XXX_Size() int {
	return xxx_messageInfo_ReturnedAwardChance.Size(m)
}
func (m *ReturnedAwardChance) XXX_DiscardUnknown() {
	xxx_messageInfo_ReturnedAwardChance.DiscardUnknown(m)
}

var xxx_messageInfo_ReturnedAwardChance proto.InternalMessageInfo

func (m *ReturnedAwardChance) GetChance() uint32 {
	if m != nil {
		return m.Chance
	}
	return 0
}

func (m *ReturnedAwardChance) GetAward() *AwardConfig {
	if m != nil {
		return m.Award
	}
	return nil
}

type ReturnedAward struct {
	Id                   uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MinVal               uint32                 `protobuf:"varint,2,opt,name=min_val,json=minVal,proto3" json:"min_val,omitempty"`
	MaxVal               uint32                 `protobuf:"varint,3,opt,name=max_val,json=maxVal,proto3" json:"max_val,omitempty"`
	List                 []*ReturnedAwardChance `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ReturnedAward) Reset()         { *m = ReturnedAward{} }
func (m *ReturnedAward) String() string { return proto.CompactTextString(m) }
func (*ReturnedAward) ProtoMessage()    {}
func (*ReturnedAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{26}
}
func (m *ReturnedAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReturnedAward.Unmarshal(m, b)
}
func (m *ReturnedAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReturnedAward.Marshal(b, m, deterministic)
}
func (dst *ReturnedAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReturnedAward.Merge(dst, src)
}
func (m *ReturnedAward) XXX_Size() int {
	return xxx_messageInfo_ReturnedAward.Size(m)
}
func (m *ReturnedAward) XXX_DiscardUnknown() {
	xxx_messageInfo_ReturnedAward.DiscardUnknown(m)
}

var xxx_messageInfo_ReturnedAward proto.InternalMessageInfo

func (m *ReturnedAward) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ReturnedAward) GetMinVal() uint32 {
	if m != nil {
		return m.MinVal
	}
	return 0
}

func (m *ReturnedAward) GetMaxVal() uint32 {
	if m != nil {
		return m.MaxVal
	}
	return 0
}

func (m *ReturnedAward) GetList() []*ReturnedAwardChance {
	if m != nil {
		return m.List
	}
	return nil
}

// 消费奖励
type ConsumeAward struct {
	Price                uint32       `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	Award                *AwardConfig `protobuf:"bytes,2,opt,name=award,proto3" json:"award,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ConsumeAward) Reset()         { *m = ConsumeAward{} }
func (m *ConsumeAward) String() string { return proto.CompactTextString(m) }
func (*ConsumeAward) ProtoMessage()    {}
func (*ConsumeAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{27}
}
func (m *ConsumeAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeAward.Unmarshal(m, b)
}
func (m *ConsumeAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeAward.Marshal(b, m, deterministic)
}
func (dst *ConsumeAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeAward.Merge(dst, src)
}
func (m *ConsumeAward) XXX_Size() int {
	return xxx_messageInfo_ConsumeAward.Size(m)
}
func (m *ConsumeAward) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeAward.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeAward proto.InternalMessageInfo

func (m *ConsumeAward) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ConsumeAward) GetAward() *AwardConfig {
	if m != nil {
		return m.Award
	}
	return nil
}

// 任务奖励
type TaskAward struct {
	Login                []*AwardConfig  `protobuf:"bytes,1,rep,name=login,proto3" json:"login,omitempty"`
	Consume              []*ConsumeAward `protobuf:"bytes,2,rep,name=consume,proto3" json:"consume,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *TaskAward) Reset()         { *m = TaskAward{} }
func (m *TaskAward) String() string { return proto.CompactTextString(m) }
func (*TaskAward) ProtoMessage()    {}
func (*TaskAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{28}
}
func (m *TaskAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskAward.Unmarshal(m, b)
}
func (m *TaskAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskAward.Marshal(b, m, deterministic)
}
func (dst *TaskAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskAward.Merge(dst, src)
}
func (m *TaskAward) XXX_Size() int {
	return xxx_messageInfo_TaskAward.Size(m)
}
func (m *TaskAward) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskAward.DiscardUnknown(m)
}

var xxx_messageInfo_TaskAward proto.InternalMessageInfo

func (m *TaskAward) GetLogin() []*AwardConfig {
	if m != nil {
		return m.Login
	}
	return nil
}

func (m *TaskAward) GetConsume() []*ConsumeAward {
	if m != nil {
		return m.Consume
	}
	return nil
}

type GetRecallAwardReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecallAwardReq) Reset()         { *m = GetRecallAwardReq{} }
func (m *GetRecallAwardReq) String() string { return proto.CompactTextString(m) }
func (*GetRecallAwardReq) ProtoMessage()    {}
func (*GetRecallAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{29}
}
func (m *GetRecallAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallAwardReq.Unmarshal(m, b)
}
func (m *GetRecallAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallAwardReq.Marshal(b, m, deterministic)
}
func (dst *GetRecallAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallAwardReq.Merge(dst, src)
}
func (m *GetRecallAwardReq) XXX_Size() int {
	return xxx_messageInfo_GetRecallAwardReq.Size(m)
}
func (m *GetRecallAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallAwardReq proto.InternalMessageInfo

type GetRecallAwardResp struct {
	Invite               *InviteAwardList `protobuf:"bytes,1,opt,name=invite,proto3" json:"invite,omitempty"`
	Returned             []*ReturnedAward `protobuf:"bytes,2,rep,name=returned,proto3" json:"returned,omitempty"`
	Task                 *TaskAward       `protobuf:"bytes,3,opt,name=task,proto3" json:"task,omitempty"`
	UpdateTime           uint32           `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetRecallAwardResp) Reset()         { *m = GetRecallAwardResp{} }
func (m *GetRecallAwardResp) String() string { return proto.CompactTextString(m) }
func (*GetRecallAwardResp) ProtoMessage()    {}
func (*GetRecallAwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{30}
}
func (m *GetRecallAwardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecallAwardResp.Unmarshal(m, b)
}
func (m *GetRecallAwardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecallAwardResp.Marshal(b, m, deterministic)
}
func (dst *GetRecallAwardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecallAwardResp.Merge(dst, src)
}
func (m *GetRecallAwardResp) XXX_Size() int {
	return xxx_messageInfo_GetRecallAwardResp.Size(m)
}
func (m *GetRecallAwardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecallAwardResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecallAwardResp proto.InternalMessageInfo

func (m *GetRecallAwardResp) GetInvite() *InviteAwardList {
	if m != nil {
		return m.Invite
	}
	return nil
}

func (m *GetRecallAwardResp) GetReturned() []*ReturnedAward {
	if m != nil {
		return m.Returned
	}
	return nil
}

func (m *GetRecallAwardResp) GetTask() *TaskAward {
	if m != nil {
		return m.Task
	}
	return nil
}

func (m *GetRecallAwardResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type UpdateRecallAwardReq struct {
	Invite               *InviteAwardList `protobuf:"bytes,1,opt,name=invite,proto3" json:"invite,omitempty"`
	Returned             []*ReturnedAward `protobuf:"bytes,2,rep,name=returned,proto3" json:"returned,omitempty"`
	Task                 *TaskAward       `protobuf:"bytes,3,opt,name=task,proto3" json:"task,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UpdateRecallAwardReq) Reset()         { *m = UpdateRecallAwardReq{} }
func (m *UpdateRecallAwardReq) String() string { return proto.CompactTextString(m) }
func (*UpdateRecallAwardReq) ProtoMessage()    {}
func (*UpdateRecallAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{31}
}
func (m *UpdateRecallAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRecallAwardReq.Unmarshal(m, b)
}
func (m *UpdateRecallAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRecallAwardReq.Marshal(b, m, deterministic)
}
func (dst *UpdateRecallAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRecallAwardReq.Merge(dst, src)
}
func (m *UpdateRecallAwardReq) XXX_Size() int {
	return xxx_messageInfo_UpdateRecallAwardReq.Size(m)
}
func (m *UpdateRecallAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRecallAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRecallAwardReq proto.InternalMessageInfo

func (m *UpdateRecallAwardReq) GetInvite() *InviteAwardList {
	if m != nil {
		return m.Invite
	}
	return nil
}

func (m *UpdateRecallAwardReq) GetReturned() []*ReturnedAward {
	if m != nil {
		return m.Returned
	}
	return nil
}

func (m *UpdateRecallAwardReq) GetTask() *TaskAward {
	if m != nil {
		return m.Task
	}
	return nil
}

type UpdateRecallAwardResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRecallAwardResp) Reset()         { *m = UpdateRecallAwardResp{} }
func (m *UpdateRecallAwardResp) String() string { return proto.CompactTextString(m) }
func (*UpdateRecallAwardResp) ProtoMessage()    {}
func (*UpdateRecallAwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{32}
}
func (m *UpdateRecallAwardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRecallAwardResp.Unmarshal(m, b)
}
func (m *UpdateRecallAwardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRecallAwardResp.Marshal(b, m, deterministic)
}
func (dst *UpdateRecallAwardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRecallAwardResp.Merge(dst, src)
}
func (m *UpdateRecallAwardResp) XXX_Size() int {
	return xxx_messageInfo_UpdateRecallAwardResp.Size(m)
}
func (m *UpdateRecallAwardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRecallAwardResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRecallAwardResp proto.InternalMessageInfo

// 清除数据
type ClearDataReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearDataReq) Reset()         { *m = ClearDataReq{} }
func (m *ClearDataReq) String() string { return proto.CompactTextString(m) }
func (*ClearDataReq) ProtoMessage()    {}
func (*ClearDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{33}
}
func (m *ClearDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearDataReq.Unmarshal(m, b)
}
func (m *ClearDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearDataReq.Marshal(b, m, deterministic)
}
func (dst *ClearDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearDataReq.Merge(dst, src)
}
func (m *ClearDataReq) XXX_Size() int {
	return xxx_messageInfo_ClearDataReq.Size(m)
}
func (m *ClearDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearDataReq proto.InternalMessageInfo

func (m *ClearDataReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ClearDataResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearDataResp) Reset()         { *m = ClearDataResp{} }
func (m *ClearDataResp) String() string { return proto.CompactTextString(m) }
func (*ClearDataResp) ProtoMessage()    {}
func (*ClearDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_recall_award_4d544aedd6f23e67, []int{34}
}
func (m *ClearDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearDataResp.Unmarshal(m, b)
}
func (m *ClearDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearDataResp.Marshal(b, m, deterministic)
}
func (dst *ClearDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearDataResp.Merge(dst, src)
}
func (m *ClearDataResp) XXX_Size() int {
	return xxx_messageInfo_ClearDataResp.Size(m)
}
func (m *ClearDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearDataResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetRecallLoginAwardInfoReq)(nil), "user_recall_award.GetRecallLoginAwardInfoReq")
	proto.RegisterType((*GetRecallLoginAwardInfoResp)(nil), "user_recall_award.GetRecallLoginAwardInfoResp")
	proto.RegisterType((*AwardInfo)(nil), "user_recall_award.AwardInfo")
	proto.RegisterType((*CheckRecallStatusReq)(nil), "user_recall_award.CheckRecallStatusReq")
	proto.RegisterType((*CheckRecallStatusResp)(nil), "user_recall_award.CheckRecallStatusResp")
	proto.RegisterType((*BatchCheckRecallStatusReq)(nil), "user_recall_award.BatchCheckRecallStatusReq")
	proto.RegisterType((*BatchCheckRecallStatusResp)(nil), "user_recall_award.BatchCheckRecallStatusResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "user_recall_award.BatchCheckRecallStatusResp.RecallStatusEntry")
	proto.RegisterType((*BatchCheckRecallStatusWithLastLoginTimeReq)(nil), "user_recall_award.BatchCheckRecallStatusWithLastLoginTimeReq")
	proto.RegisterType((*BatchCheckRecallStatusWithLastLoginTimeResp)(nil), "user_recall_award.BatchCheckRecallStatusWithLastLoginTimeResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "user_recall_award.BatchCheckRecallStatusWithLastLoginTimeResp.RecallStatusEntry")
	proto.RegisterType((*RecalledUserInfo)(nil), "user_recall_award.RecalledUserInfo")
	proto.RegisterType((*BatchGetInviteAwardInfoReq)(nil), "user_recall_award.BatchGetInviteAwardInfoReq")
	proto.RegisterType((*InviteAwardInfo)(nil), "user_recall_award.InviteAwardInfo")
	proto.RegisterType((*BatchGetInviteAwardInfoResp)(nil), "user_recall_award.BatchGetInviteAwardInfoResp")
	proto.RegisterType((*GetReturnAwardInfoReq)(nil), "user_recall_award.GetReturnAwardInfoReq")
	proto.RegisterType((*ReturnConsumeAwardInfo)(nil), "user_recall_award.ReturnConsumeAwardInfo")
	proto.RegisterType((*GetReturnAwardInfoResp)(nil), "user_recall_award.GetReturnAwardInfoResp")
	proto.RegisterType((*GetInviteAwardReq)(nil), "user_recall_award.GetInviteAwardReq")
	proto.RegisterType((*GetInviteAwardResp)(nil), "user_recall_award.GetInviteAwardResp")
	proto.RegisterType((*GetReturnAwardReq)(nil), "user_recall_award.GetReturnAwardReq")
	proto.RegisterType((*GetReturnAwardResp)(nil), "user_recall_award.GetReturnAwardResp")
	proto.RegisterType((*GetMostExpAwardInfoReq)(nil), "user_recall_award.GetMostExpAwardInfoReq")
	proto.RegisterType((*GetMostExpAwardInfoResp)(nil), "user_recall_award.GetMostExpAwardInfoResp")
	proto.RegisterType((*InviteAward)(nil), "user_recall_award.InviteAward")
	proto.RegisterType((*InviteAwardList)(nil), "user_recall_award.InviteAwardList")
	proto.RegisterType((*AwardConfig)(nil), "user_recall_award.AwardConfig")
	proto.RegisterType((*ReturnedAwardChance)(nil), "user_recall_award.ReturnedAwardChance")
	proto.RegisterType((*ReturnedAward)(nil), "user_recall_award.ReturnedAward")
	proto.RegisterType((*ConsumeAward)(nil), "user_recall_award.ConsumeAward")
	proto.RegisterType((*TaskAward)(nil), "user_recall_award.TaskAward")
	proto.RegisterType((*GetRecallAwardReq)(nil), "user_recall_award.GetRecallAwardReq")
	proto.RegisterType((*GetRecallAwardResp)(nil), "user_recall_award.GetRecallAwardResp")
	proto.RegisterType((*UpdateRecallAwardReq)(nil), "user_recall_award.UpdateRecallAwardReq")
	proto.RegisterType((*UpdateRecallAwardResp)(nil), "user_recall_award.UpdateRecallAwardResp")
	proto.RegisterType((*ClearDataReq)(nil), "user_recall_award.ClearDataReq")
	proto.RegisterType((*ClearDataResp)(nil), "user_recall_award.ClearDataResp")
	proto.RegisterEnum("user_recall_award.AwardReceiveStatus", AwardReceiveStatus_name, AwardReceiveStatus_value)
	proto.RegisterEnum("user_recall_award.RecallStatus", RecallStatus_name, RecallStatus_value)
	proto.RegisterEnum("user_recall_award.AwardType", AwardType_name, AwardType_value)
	proto.RegisterEnum("user_recall_award.AwardGiftType", AwardGiftType_name, AwardGiftType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserRecallAwardClient is the client API for UserRecallAward service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserRecallAwardClient interface {
	// -------------------- 客户端协议 begain ----------------------------------------
	// 获取用户回归登录礼包信息
	GetRecallLoginAwardInfo(ctx context.Context, in *GetRecallLoginAwardInfoReq, opts ...grpc.CallOption) (*GetRecallLoginAwardInfoResp, error)
	// 查看召回状态
	CheckRecallStatus(ctx context.Context, in *CheckRecallStatusReq, opts ...grpc.CallOption) (*CheckRecallStatusResp, error)
	// 批量查看召回状态
	BatchCheckRecallStatus(ctx context.Context, in *BatchCheckRecallStatusReq, opts ...grpc.CallOption) (*BatchCheckRecallStatusResp, error)
	// 批量查看召回状态(判断流失时间)
	BatchCheckRecallStatusWithLastLoginTime(ctx context.Context, in *BatchCheckRecallStatusWithLastLoginTimeReq, opts ...grpc.CallOption) (*BatchCheckRecallStatusWithLastLoginTimeResp, error)
	// 批量查询邀请奖励
	BatchGetInviteAwardInfo(ctx context.Context, in *BatchGetInviteAwardInfoReq, opts ...grpc.CallOption) (*BatchGetInviteAwardInfoResp, error)
	// 获取回归奖励信息
	GetReturnAwardInfo(ctx context.Context, in *GetReturnAwardInfoReq, opts ...grpc.CallOption) (*GetReturnAwardInfoResp, error)
	// 获取邀请奖励
	GetInviteAward(ctx context.Context, in *GetInviteAwardReq, opts ...grpc.CallOption) (*GetInviteAwardResp, error)
	// 获取回归奖励
	GetReturnAward(ctx context.Context, in *GetReturnAwardReq, opts ...grpc.CallOption) (*GetReturnAwardResp, error)
	// 获取最贵奖励配置
	GetMostExpAwardInfo(ctx context.Context, in *GetMostExpAwardInfoReq, opts ...grpc.CallOption) (*GetMostExpAwardInfoResp, error)
	// 数据清清除接口（测试用）
	ClearData(ctx context.Context, in *ClearDataReq, opts ...grpc.CallOption) (*ClearDataResp, error)
	// 订单数对账
	CntOrder(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 订单列表对账
	GetOrderId(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 自动补单
	ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 获取回归奖励
	GetRecallAward(ctx context.Context, in *GetRecallAwardReq, opts ...grpc.CallOption) (*GetRecallAwardResp, error)
	// 设置回归奖励
	UpdateRecallAward(ctx context.Context, in *UpdateRecallAwardReq, opts ...grpc.CallOption) (*UpdateRecallAwardResp, error)
}

type userRecallAwardClient struct {
	cc *grpc.ClientConn
}

func NewUserRecallAwardClient(cc *grpc.ClientConn) UserRecallAwardClient {
	return &userRecallAwardClient{cc}
}

func (c *userRecallAwardClient) GetRecallLoginAwardInfo(ctx context.Context, in *GetRecallLoginAwardInfoReq, opts ...grpc.CallOption) (*GetRecallLoginAwardInfoResp, error) {
	out := new(GetRecallLoginAwardInfoResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/GetRecallLoginAwardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) CheckRecallStatus(ctx context.Context, in *CheckRecallStatusReq, opts ...grpc.CallOption) (*CheckRecallStatusResp, error) {
	out := new(CheckRecallStatusResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/CheckRecallStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) BatchCheckRecallStatus(ctx context.Context, in *BatchCheckRecallStatusReq, opts ...grpc.CallOption) (*BatchCheckRecallStatusResp, error) {
	out := new(BatchCheckRecallStatusResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/BatchCheckRecallStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) BatchCheckRecallStatusWithLastLoginTime(ctx context.Context, in *BatchCheckRecallStatusWithLastLoginTimeReq, opts ...grpc.CallOption) (*BatchCheckRecallStatusWithLastLoginTimeResp, error) {
	out := new(BatchCheckRecallStatusWithLastLoginTimeResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/BatchCheckRecallStatusWithLastLoginTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) BatchGetInviteAwardInfo(ctx context.Context, in *BatchGetInviteAwardInfoReq, opts ...grpc.CallOption) (*BatchGetInviteAwardInfoResp, error) {
	out := new(BatchGetInviteAwardInfoResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/BatchGetInviteAwardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) GetReturnAwardInfo(ctx context.Context, in *GetReturnAwardInfoReq, opts ...grpc.CallOption) (*GetReturnAwardInfoResp, error) {
	out := new(GetReturnAwardInfoResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/GetReturnAwardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) GetInviteAward(ctx context.Context, in *GetInviteAwardReq, opts ...grpc.CallOption) (*GetInviteAwardResp, error) {
	out := new(GetInviteAwardResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/GetInviteAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) GetReturnAward(ctx context.Context, in *GetReturnAwardReq, opts ...grpc.CallOption) (*GetReturnAwardResp, error) {
	out := new(GetReturnAwardResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/GetReturnAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) GetMostExpAwardInfo(ctx context.Context, in *GetMostExpAwardInfoReq, opts ...grpc.CallOption) (*GetMostExpAwardInfoResp, error) {
	out := new(GetMostExpAwardInfoResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/GetMostExpAwardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) ClearData(ctx context.Context, in *ClearDataReq, opts ...grpc.CallOption) (*ClearDataResp, error) {
	out := new(ClearDataResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/ClearData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) CntOrder(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/CntOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) GetOrderId(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/GetOrderId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/ReplaceOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) GetRecallAward(ctx context.Context, in *GetRecallAwardReq, opts ...grpc.CallOption) (*GetRecallAwardResp, error) {
	out := new(GetRecallAwardResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/GetRecallAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userRecallAwardClient) UpdateRecallAward(ctx context.Context, in *UpdateRecallAwardReq, opts ...grpc.CallOption) (*UpdateRecallAwardResp, error) {
	out := new(UpdateRecallAwardResp)
	err := c.cc.Invoke(ctx, "/user_recall_award.UserRecallAward/UpdateRecallAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserRecallAwardServer is the server API for UserRecallAward service.
type UserRecallAwardServer interface {
	// -------------------- 客户端协议 begain ----------------------------------------
	// 获取用户回归登录礼包信息
	GetRecallLoginAwardInfo(context.Context, *GetRecallLoginAwardInfoReq) (*GetRecallLoginAwardInfoResp, error)
	// 查看召回状态
	CheckRecallStatus(context.Context, *CheckRecallStatusReq) (*CheckRecallStatusResp, error)
	// 批量查看召回状态
	BatchCheckRecallStatus(context.Context, *BatchCheckRecallStatusReq) (*BatchCheckRecallStatusResp, error)
	// 批量查看召回状态(判断流失时间)
	BatchCheckRecallStatusWithLastLoginTime(context.Context, *BatchCheckRecallStatusWithLastLoginTimeReq) (*BatchCheckRecallStatusWithLastLoginTimeResp, error)
	// 批量查询邀请奖励
	BatchGetInviteAwardInfo(context.Context, *BatchGetInviteAwardInfoReq) (*BatchGetInviteAwardInfoResp, error)
	// 获取回归奖励信息
	GetReturnAwardInfo(context.Context, *GetReturnAwardInfoReq) (*GetReturnAwardInfoResp, error)
	// 获取邀请奖励
	GetInviteAward(context.Context, *GetInviteAwardReq) (*GetInviteAwardResp, error)
	// 获取回归奖励
	GetReturnAward(context.Context, *GetReturnAwardReq) (*GetReturnAwardResp, error)
	// 获取最贵奖励配置
	GetMostExpAwardInfo(context.Context, *GetMostExpAwardInfoReq) (*GetMostExpAwardInfoResp, error)
	// 数据清清除接口（测试用）
	ClearData(context.Context, *ClearDataReq) (*ClearDataResp, error)
	// 订单数对账
	CntOrder(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 订单列表对账
	GetOrderId(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 自动补单
	ReplaceOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 获取回归奖励
	GetRecallAward(context.Context, *GetRecallAwardReq) (*GetRecallAwardResp, error)
	// 设置回归奖励
	UpdateRecallAward(context.Context, *UpdateRecallAwardReq) (*UpdateRecallAwardResp, error)
}

func RegisterUserRecallAwardServer(s *grpc.Server, srv UserRecallAwardServer) {
	s.RegisterService(&_UserRecallAward_serviceDesc, srv)
}

func _UserRecallAward_GetRecallLoginAwardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallLoginAwardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).GetRecallLoginAwardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/GetRecallLoginAwardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).GetRecallLoginAwardInfo(ctx, req.(*GetRecallLoginAwardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_CheckRecallStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRecallStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).CheckRecallStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/CheckRecallStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).CheckRecallStatus(ctx, req.(*CheckRecallStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_BatchCheckRecallStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckRecallStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).BatchCheckRecallStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/BatchCheckRecallStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).BatchCheckRecallStatus(ctx, req.(*BatchCheckRecallStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_BatchCheckRecallStatusWithLastLoginTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckRecallStatusWithLastLoginTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).BatchCheckRecallStatusWithLastLoginTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/BatchCheckRecallStatusWithLastLoginTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).BatchCheckRecallStatusWithLastLoginTime(ctx, req.(*BatchCheckRecallStatusWithLastLoginTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_BatchGetInviteAwardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetInviteAwardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).BatchGetInviteAwardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/BatchGetInviteAwardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).BatchGetInviteAwardInfo(ctx, req.(*BatchGetInviteAwardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_GetReturnAwardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReturnAwardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).GetReturnAwardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/GetReturnAwardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).GetReturnAwardInfo(ctx, req.(*GetReturnAwardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_GetInviteAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInviteAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).GetInviteAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/GetInviteAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).GetInviteAward(ctx, req.(*GetInviteAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_GetReturnAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReturnAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).GetReturnAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/GetReturnAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).GetReturnAward(ctx, req.(*GetReturnAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_GetMostExpAwardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMostExpAwardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).GetMostExpAwardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/GetMostExpAwardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).GetMostExpAwardInfo(ctx, req.(*GetMostExpAwardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_ClearData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).ClearData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/ClearData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).ClearData(ctx, req.(*ClearDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_CntOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).CntOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/CntOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).CntOrder(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_GetOrderId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).GetOrderId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/GetOrderId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).GetOrderId(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_ReplaceOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).ReplaceOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/ReplaceOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).ReplaceOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_GetRecallAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).GetRecallAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/GetRecallAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).GetRecallAward(ctx, req.(*GetRecallAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserRecallAward_UpdateRecallAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRecallAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserRecallAwardServer).UpdateRecallAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_recall_award.UserRecallAward/UpdateRecallAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserRecallAwardServer).UpdateRecallAward(ctx, req.(*UpdateRecallAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserRecallAward_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user_recall_award.UserRecallAward",
	HandlerType: (*UserRecallAwardServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecallLoginAwardInfo",
			Handler:    _UserRecallAward_GetRecallLoginAwardInfo_Handler,
		},
		{
			MethodName: "CheckRecallStatus",
			Handler:    _UserRecallAward_CheckRecallStatus_Handler,
		},
		{
			MethodName: "BatchCheckRecallStatus",
			Handler:    _UserRecallAward_BatchCheckRecallStatus_Handler,
		},
		{
			MethodName: "BatchCheckRecallStatusWithLastLoginTime",
			Handler:    _UserRecallAward_BatchCheckRecallStatusWithLastLoginTime_Handler,
		},
		{
			MethodName: "BatchGetInviteAwardInfo",
			Handler:    _UserRecallAward_BatchGetInviteAwardInfo_Handler,
		},
		{
			MethodName: "GetReturnAwardInfo",
			Handler:    _UserRecallAward_GetReturnAwardInfo_Handler,
		},
		{
			MethodName: "GetInviteAward",
			Handler:    _UserRecallAward_GetInviteAward_Handler,
		},
		{
			MethodName: "GetReturnAward",
			Handler:    _UserRecallAward_GetReturnAward_Handler,
		},
		{
			MethodName: "GetMostExpAwardInfo",
			Handler:    _UserRecallAward_GetMostExpAwardInfo_Handler,
		},
		{
			MethodName: "ClearData",
			Handler:    _UserRecallAward_ClearData_Handler,
		},
		{
			MethodName: "CntOrder",
			Handler:    _UserRecallAward_CntOrder_Handler,
		},
		{
			MethodName: "GetOrderId",
			Handler:    _UserRecallAward_GetOrderId_Handler,
		},
		{
			MethodName: "ReplaceOrder",
			Handler:    _UserRecallAward_ReplaceOrder_Handler,
		},
		{
			MethodName: "GetRecallAward",
			Handler:    _UserRecallAward_GetRecallAward_Handler,
		},
		{
			MethodName: "UpdateRecallAward",
			Handler:    _UserRecallAward_UpdateRecallAward_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user-recall-award/user-recall-award.proto",
}

func init() {
	proto.RegisterFile("user-recall-award/user-recall-award.proto", fileDescriptor_user_recall_award_4d544aedd6f23e67)
}

var fileDescriptor_user_recall_award_4d544aedd6f23e67 = []byte{
	// 1875 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x59, 0x4f, 0x73, 0xdb, 0xc6,
	0x15, 0x17, 0x48, 0x59, 0x16, 0x1f, 0x45, 0x0b, 0x5a, 0x5b, 0x12, 0x45, 0x59, 0x36, 0x83, 0x38,
	0x89, 0xac, 0x46, 0x74, 0x47, 0x49, 0x66, 0x1a, 0x27, 0xa9, 0x47, 0x06, 0x11, 0x9a, 0x13, 0x9a,
	0x54, 0x21, 0xca, 0x9e, 0xe4, 0x82, 0x22, 0xe0, 0x4a, 0xc2, 0x08, 0x04, 0x60, 0x00, 0x64, 0xc4,
	0x4e, 0xff, 0x5d, 0x3a, 0xed, 0xa5, 0x1f, 0xa1, 0x97, 0x4e, 0x67, 0x3a, 0xd3, 0x8f, 0xd1, 0x7e,
	0x87, 0xde, 0x7b, 0xe8, 0xb9, 0x5f, 0xa1, 0xb3, 0xbb, 0x00, 0xb8, 0x20, 0x97, 0x32, 0x55, 0xb5,
	0x9d, 0xc9, 0x8d, 0x78, 0xff, 0x7f, 0x6f, 0xdf, 0xbe, 0x7d, 0xbb, 0x84, 0xc7, 0x83, 0x10, 0x07,
	0xfb, 0x01, 0xb6, 0x4c, 0xc7, 0xd9, 0x37, 0xbf, 0x33, 0x83, 0xde, 0x93, 0x29, 0x4a, 0xcd, 0x0f,
	0xbc, 0xc8, 0x43, 0x6b, 0x84, 0x61, 0x30, 0x86, 0x41, 0x19, 0x95, 0x87, 0x01, 0xb6, 0x3c, 0xd7,
	0xb2, 0x1d, 0xbc, 0x3f, 0x3c, 0x78, 0xc2, 0x7f, 0x30, 0x1d, 0xe5, 0x77, 0x12, 0x54, 0x1a, 0x38,
	0xd2, 0xa9, 0x52, 0xcb, 0x3b, 0xb3, 0xdd, 0x43, 0xa2, 0xd8, 0x74, 0x4f, 0x3d, 0x1d, 0xbf, 0x41,
	0xef, 0xc0, 0x0a, 0xb3, 0x87, 0x7b, 0xc6, 0xc0, 0xee, 0x95, 0xa5, 0xaa, 0xb4, 0x5b, 0xd2, 0x8b,
	0x09, 0xed, 0xc4, 0xee, 0xa1, 0x87, 0x50, 0xb4, 0x3c, 0x37, 0x1c, 0xf4, 0xb1, 0x31, 0x34, 0x9d,
	0x72, 0x8e, 0x4a, 0x40, 0x4c, 0x7a, 0x65, 0x3a, 0xe8, 0x7d, 0x58, 0x75, 0xcc, 0x30, 0x32, 0x1c,
	0x62, 0xdd, 0x88, 0xec, 0x3e, 0x2e, 0xe7, 0xab, 0xd2, 0x6e, 0x5e, 0x2f, 0x11, 0x32, 0xf5, 0xd9,
	0xb5, 0xfb, 0x58, 0xf9, 0x93, 0x04, 0xdb, 0x33, 0x43, 0x09, 0x7d, 0xb4, 0x09, 0xb7, 0xfb, 0xb6,
	0x4b, 0x9d, 0xb0, 0x30, 0x96, 0xfa, 0xb6, 0x4b, 0x1c, 0x10, 0x86, 0x79, 0xc9, 0x79, 0x5f, 0xea,
	0x9b, 0x97, 0x84, 0xb1, 0x01, 0x4b, 0xf8, 0xd2, 0xb7, 0x03, 0xe6, 0xb0, 0xa4, 0xc7, 0x5f, 0xe8,
	0x33, 0x00, 0x9a, 0x1e, 0xc3, 0x76, 0x4f, 0xbd, 0xf2, 0x62, 0x55, 0xda, 0x2d, 0x1e, 0xdc, 0xaf,
	0x4d, 0x65, 0xaf, 0x36, 0xf6, 0x5f, 0x30, 0x93, 0x9f, 0xca, 0xbf, 0x24, 0x28, 0xa4, 0x0c, 0xe2,
	0xfb, 0xcc, 0x3e, 0x8d, 0x8c, 0x34, 0x37, 0x4b, 0xe4, 0xb3, 0xd9, 0x43, 0xdb, 0x50, 0xa0, 0x0c,
	0xd7, 0xec, 0x63, 0x1a, 0x56, 0x41, 0x5f, 0x26, 0x84, 0xb6, 0xd9, 0xc7, 0x68, 0x0b, 0xe8, 0x6f,
	0xc3, 0x72, 0xa3, 0x38, 0x34, 0x6a, 0x45, 0x75, 0xa3, 0x54, 0xcf, 0xb6, 0x3c, 0x97, 0x86, 0x16,
	0xeb, 0x35, 0x2d, 0xcf, 0x4d, 0xf5, 0x08, 0xd4, 0x5b, 0x63, 0x3d, 0x82, 0x75, 0x07, 0x80, 0xa1,
	0x33, 0x7a, 0xe6, 0xa8, 0xbc, 0x44, 0x99, 0x05, 0x46, 0xa9, 0x9b, 0x23, 0xc2, 0x66, 0x90, 0xa3,
	0x91, 0x8f, 0xcb, 0xb7, 0x19, 0x9b, 0x52, 0xba, 0x23, 0x1f, 0xa7, 0x5e, 0x29, 0x77, 0x99, 0x72,
	0xa9, 0x27, 0xc2, 0x54, 0x3e, 0x85, 0x7b, 0xea, 0x39, 0xb6, 0x2e, 0xd8, 0xca, 0x1c, 0x47, 0x66,
	0x34, 0x08, 0xe7, 0x2b, 0x0e, 0xe5, 0x73, 0x58, 0x17, 0xa8, 0x86, 0x3e, 0x7a, 0x17, 0x4a, 0x71,
	0xaa, 0x43, 0x4a, 0x8c, 0x95, 0x63, 0x83, 0x4c, 0x50, 0x69, 0xc0, 0xd6, 0x73, 0x33, 0xb2, 0xce,
	0x85, 0xde, 0xf7, 0x60, 0x8d, 0xf7, 0x6e, 0x38, 0x76, 0x18, 0x95, 0xa5, 0x6a, 0x7e, 0xb7, 0xa4,
	0xaf, 0x72, 0x21, 0xb4, 0xec, 0x30, 0x52, 0xfe, 0x26, 0x41, 0x65, 0x96, 0xa5, 0xd0, 0x47, 0xbd,
	0xe9, 0x60, 0xf2, 0xbb, 0xc5, 0x83, 0x67, 0x82, 0x92, 0x98, 0x6d, 0xa5, 0xc6, 0x13, 0x34, 0x37,
	0x0a, 0x46, 0x59, 0x34, 0x95, 0x67, 0xb0, 0x36, 0x25, 0x82, 0x64, 0xc8, 0x5f, 0xe0, 0x51, 0x8c,
	0x9e, 0xfc, 0x44, 0xf7, 0xe0, 0xd6, 0xd0, 0x74, 0x06, 0x38, 0xae, 0x65, 0xf6, 0xf1, 0x34, 0xf7,
	0x23, 0x49, 0xf9, 0xb5, 0x04, 0x7b, 0x62, 0xff, 0xaf, 0xed, 0xe8, 0xbc, 0xc5, 0x6f, 0xa6, 0x6b,
	0x26, 0x48, 0xb4, 0x47, 0x73, 0x54, 0x72, 0x62, 0x8f, 0xfe, 0x53, 0x82, 0x1f, 0xcc, 0x1d, 0x42,
	0xe8, 0xa3, 0x81, 0x38, 0xb3, 0x47, 0x73, 0x67, 0x56, 0x68, 0xf6, 0x7f, 0x9f, 0xea, 0x5f, 0x82,
	0xac, 0x27, 0x29, 0x0a, 0x71, 0x40, 0xb7, 0xfa, 0xff, 0xb3, 0x17, 0x9e, 0xc6, 0xf5, 0xda, 0xc0,
	0x51, 0xd3, 0x1d, 0xda, 0x11, 0xce, 0x74, 0xe5, 0x17, 0x49, 0x56, 0x49, 0x24, 0x21, 0x0e, 0xe2,
	0xac, 0xbe, 0x2b, 0xc8, 0xea, 0x24, 0x0a, 0x3d, 0xc5, 0x40, 0x28, 0xca, 0x1b, 0x58, 0x9d, 0xb0,
	0x3f, 0x0f, 0xcc, 0x6c, 0xff, 0xcc, 0x5d, 0xaf, 0x7f, 0x7e, 0x0b, 0xdb, 0x33, 0xa1, 0x85, 0x3e,
	0x52, 0xa1, 0x38, 0xb6, 0x9d, 0xd4, 0x8b, 0x22, 0x30, 0x3e, 0xa9, 0x0c, 0xa9, 0x8b, 0x50, 0x79,
	0x0a, 0xeb, 0xf4, 0x24, 0x89, 0x06, 0xc1, 0x75, 0xcf, 0x33, 0xe5, 0x0f, 0x12, 0x6c, 0x30, 0x4d,
	0x95, 0xad, 0xdb, 0x38, 0x35, 0x59, 0xdc, 0xd2, 0xb5, 0x70, 0x93, 0xda, 0x08, 0xb0, 0x85, 0xed,
	0x61, 0xa6, 0x36, 0x62, 0x12, 0xa9, 0x8d, 0xf7, 0xe0, 0x4e, 0x22, 0x10, 0x6f, 0x16, 0x76, 0x34,
	0x94, 0x62, 0x6a, 0xdc, 0x14, 0xff, 0x9e, 0x87, 0x0d, 0x11, 0xb8, 0xd0, 0x27, 0x3b, 0x3e, 0x32,
	0xc3, 0x0b, 0xc3, 0xb1, 0xfb, 0x76, 0x84, 0x7b, 0xac, 0xbe, 0x18, 0xc4, 0x55, 0xc2, 0x68, 0x31,
	0x3a, 0xa9, 0x30, 0xa4, 0x40, 0xc9, 0xf2, 0xdc, 0xb8, 0x10, 0xc9, 0x91, 0xc1, 0x02, 0x22, 0xf5,
	0x4b, 0xcb, 0x90, 0x1c, 0x1a, 0x0a, 0x94, 0xc8, 0xc1, 0x3a, 0x96, 0x61, 0x01, 0x15, 0xfb, 0xe6,
	0x65, 0x2a, 0xf3, 0x05, 0x14, 0x19, 0x9f, 0x22, 0x2d, 0x2f, 0xd2, 0xf5, 0xba, 0x3a, 0x29, 0xe0,
	0xa4, 0x87, 0x3b, 0xfa, 0x0c, 0x2a, 0x9c, 0xba, 0x31, 0x91, 0x00, 0x76, 0xc6, 0x6d, 0x8e, 0xe5,
	0x75, 0x3e, 0x15, 0x64, 0x37, 0x11, 0x0c, 0xfc, 0x96, 0x63, 0x07, 0x1f, 0x81, 0xa6, 0x66, 0x76,
	0x1d, 0xc1, 0xc1, 0xcb, 0xb1, 0x13, 0x90, 0xc0, 0xe3, 0xe4, 0xda, 0x34, 0x27, 0x54, 0x86, 0xa1,
	0x59, 0xa6, 0x68, 0x1e, 0x0b, 0xf7, 0x95, 0xa8, 0x42, 0xf4, 0x15, 0x8b, 0xa3, 0xa0, 0x0f, 0x01,
	0x31, 0x58, 0x99, 0x05, 0x29, 0x50, 0xd7, 0x32, 0xe5, 0x70, 0x2b, 0xa2, 0xfc, 0x02, 0xd6, 0xb2,
	0x7b, 0x62, 0xce, 0x01, 0x6c, 0x07, 0xc0, 0xa6, 0x4a, 0x54, 0x80, 0x2d, 0x63, 0x81, 0x51, 0x08,
	0x7b, 0xde, 0x96, 0xf3, 0x13, 0x40, 0x93, 0xee, 0x43, 0xff, 0x46, 0x25, 0xaf, 0x44, 0x14, 0x11,
	0x57, 0xa9, 0xf3, 0x23, 0xe2, 0x86, 0x95, 0xdc, 0xe4, 0xb0, 0xb2, 0x03, 0x49, 0x4b, 0x35, 0x9c,
	0x61, 0x5c, 0x93, 0x85, 0x98, 0xd2, 0x1a, 0xc6, 0x40, 0x32, 0x5e, 0x6f, 0x0a, 0xa4, 0x4c, 0xb7,
	0xdc, 0x4b, 0x2f, 0x8c, 0xb4, 0x4b, 0x9f, 0x6f, 0x28, 0x8a, 0x01, 0x9b, 0x42, 0x4e, 0xe8, 0xa3,
	0x3a, 0xac, 0x8e, 0x3d, 0x8e, 0x4f, 0xdf, 0xb7, 0xb9, 0x2d, 0xa5, 0x6e, 0xe9, 0xe8, 0xf2, 0x1b,
	0x09, 0x8a, 0xdc, 0xa2, 0xa0, 0x3b, 0x90, 0x4b, 0x93, 0x96, 0xb3, 0x7b, 0xfc, 0x54, 0x9c, 0x9b,
	0x35, 0x15, 0xe7, 0x33, 0x53, 0xf1, 0xc7, 0x70, 0x2b, 0xd9, 0xab, 0x24, 0x09, 0x0f, 0x66, 0x45,
	0xa3, 0x7a, 0xee, 0xa9, 0x7d, 0xa6, 0x33, 0x61, 0x45, 0xcb, 0x9c, 0x14, 0x74, 0x68, 0x38, 0x80,
	0x45, 0x0e, 0xd5, 0x83, 0xab, 0x7b, 0xb4, 0x4e, 0x65, 0x95, 0x9f, 0x42, 0x91, 0x33, 0x9e, 0x9d,
	0x3b, 0xa5, 0xec, 0xdc, 0xc9, 0xcf, 0xd6, 0x6c, 0x80, 0xe6, 0x66, 0xeb, 0x9e, 0x39, 0x32, 0x2c,
	0x6f, 0x90, 0xce, 0xcf, 0xcb, 0x3d, 0x73, 0xa4, 0x92, 0x6f, 0xc5, 0x82, 0xbb, 0x6c, 0xed, 0x71,
	0x8f, 0x79, 0x3a, 0x37, 0x5d, 0x0b, 0x93, 0xbb, 0x80, 0x45, 0x7f, 0x25, 0x73, 0x3a, 0xfb, 0x1a,
	0x67, 0x23, 0x77, 0x9d, 0x6c, 0xfc, 0x5e, 0x82, 0x52, 0xc6, 0xcb, 0x7f, 0x61, 0x5d, 0x9e, 0xc6,
	0xe9, 0x64, 0x2d, 0xf4, 0xfd, 0x99, 0x4d, 0x27, 0x83, 0x2b, 0x4e, 0xeb, 0x37, 0xb0, 0xc2, 0xf7,
	0x22, 0x32, 0xd9, 0xf8, 0x81, 0x9d, 0x82, 0x65, 0x1f, 0xff, 0x21, 0xd6, 0x9f, 0x43, 0xa1, 0x6b,
	0x86, 0x17, 0xcc, 0xf0, 0xc7, 0x70, 0x8b, 0x36, 0x92, 0x2b, 0x16, 0x3d, 0x63, 0x82, 0x0a, 0xa3,
	0x4f, 0xe1, 0x76, 0xbc, 0x3f, 0xe9, 0x58, 0x59, 0x3c, 0x78, 0x28, 0xd0, 0xe3, 0x01, 0xe8, 0x89,
	0xbc, 0x72, 0x37, 0xee, 0x21, 0x44, 0x30, 0xe9, 0x21, 0xca, 0x3f, 0xa4, 0x78, 0x8f, 0x73, 0xd4,
	0xd0, 0x47, 0x4f, 0x61, 0x89, 0xf5, 0xbd, 0x78, 0x7f, 0xbf, 0x65, 0x6c, 0x20, 0x45, 0xac, 0xc7,
	0x1a, 0xe8, 0x73, 0x58, 0x0e, 0xe2, 0xf4, 0xc6, 0x31, 0x56, 0xdf, 0xb6, 0x02, 0x7a, 0xaa, 0x81,
	0x7e, 0x08, 0x8b, 0xe4, 0x80, 0xa5, 0x2b, 0x2a, 0xde, 0xe0, 0x69, 0x0a, 0x75, 0x2a, 0x49, 0xc6,
	0x81, 0x81, 0xdf, 0x33, 0x23, 0xcc, 0x5a, 0xf2, 0x22, 0x1b, 0x07, 0x18, 0x89, 0xf6, 0xe3, 0xbf,
	0x4a, 0x70, 0xef, 0x84, 0x7e, 0x66, 0xc1, 0x7f, 0x9f, 0x50, 0x2a, 0x9b, 0xb0, 0x2e, 0xc0, 0x10,
	0xfa, 0x4a, 0x15, 0x56, 0x54, 0x07, 0x9b, 0x41, 0xdd, 0x8c, 0x4c, 0x02, 0x4a, 0x86, 0xfc, 0xf8,
	0x30, 0x20, 0x3f, 0x95, 0x55, 0x28, 0x71, 0x12, 0xa1, 0xbf, 0xf7, 0x2b, 0x40, 0x82, 0x19, 0xa0,
	0x0a, 0xf7, 0x0f, 0x5f, 0x1f, 0xea, 0x75, 0x43, 0xd7, 0x54, 0xad, 0xf9, 0x4a, 0x33, 0x8e, 0xbb,
	0x87, 0xdd, 0x93, 0x63, 0xe3, 0xa4, 0xfd, 0x55, 0xbb, 0xf3, 0xba, 0x2d, 0x2f, 0xa0, 0x47, 0x50,
	0x15, 0x4a, 0xb4, 0x3b, 0xdd, 0x84, 0x24, 0x4b, 0xe8, 0x1d, 0xd8, 0x11, 0x4a, 0xc5, 0x9f, 0x75,
	0x39, 0xb7, 0x37, 0x84, 0x15, 0xfe, 0x56, 0x81, 0xb6, 0x60, 0x5d, 0xd7, 0xd4, 0xc3, 0x56, 0x6b,
	0xda, 0xe7, 0x26, 0xdc, 0xcd, 0xb2, 0xea, 0x9d, 0x66, 0xbb, 0x21, 0x4b, 0x68, 0x1b, 0x36, 0xb3,
	0x8c, 0x17, 0x87, 0xc7, 0x06, 0x75, 0x2c, 0xe7, 0xd0, 0x3a, 0xac, 0x65, 0x99, 0x5a, 0xbb, 0x2e,
	0xe7, 0xf7, 0x7e, 0x9b, 0xbc, 0x38, 0xd0, 0xae, 0xb8, 0x01, 0x88, 0x05, 0xda, 0xfd, 0xfa, 0x48,
	0xe3, 0x5c, 0xde, 0x03, 0x99, 0xa3, 0xb7, 0x3a, 0x8d, 0x66, 0x5b, 0x96, 0xd0, 0x0e, 0x6c, 0x71,
	0x54, 0xb5, 0xd3, 0xee, 0x36, 0xdb, 0x27, 0x09, 0x3b, 0x37, 0x61, 0x4c, 0xed, 0xb4, 0x8f, 0x4f,
	0x5e, 0x6a, 0x72, 0x9e, 0x44, 0xc2, 0xd1, 0x9b, 0xed, 0x57, 0xcd, 0xae, 0x26, 0x2f, 0xee, 0xfd,
	0x59, 0x82, 0x12, 0x8d, 0xa4, 0x91, 0xf4, 0xe8, 0x87, 0xb0, 0xcd, 0x04, 0x1b, 0xcd, 0x2f, 0xbb,
	0x49, 0x48, 0xc7, 0x47, 0x9a, 0xda, 0xfc, 0xb2, 0xa9, 0xd5, 0xe5, 0x05, 0x02, 0x78, 0x52, 0xe0,
	0xe8, 0x50, 0xfd, 0xea, 0xb0, 0x41, 0x92, 0xfe, 0x00, 0x2a, 0x93, 0xcc, 0x76, 0xe7, 0x79, 0x4b,
	0x33, 0x54, 0x96, 0x90, 0x34, 0xfa, 0x31, 0xff, 0x65, 0x53, 0x35, 0x8e, 0xbb, 0x5f, 0xb7, 0x48,
	0x94, 0x5b, 0xb0, 0x3e, 0xc9, 0x7e, 0xd1, 0xd1, 0x8f, 0x35, 0x79, 0xf1, 0xe0, 0x8f, 0x2b, 0xb0,
	0x4a, 0x6e, 0x38, 0x5c, 0xdd, 0xa1, 0x9f, 0xd1, 0xb3, 0x5a, 0xf4, 0xbe, 0x84, 0xf6, 0x05, 0xb5,
	0x3c, 0xfb, 0x59, 0xac, 0x52, 0xbb, 0x8e, 0x78, 0xe8, 0x2b, 0x0b, 0xe8, 0x1c, 0xd6, 0xa6, 0xee,
	0xb6, 0xe8, 0x03, 0x51, 0x17, 0x14, 0xbc, 0x75, 0x54, 0x76, 0xe7, 0x13, 0xa4, 0x9e, 0xbe, 0x83,
	0x0d, 0xf1, 0x55, 0x1a, 0x7d, 0x78, 0x8d, 0xf7, 0x8c, 0x37, 0x95, 0xfd, 0x6b, 0xbd, 0x7e, 0x28,
	0x0b, 0xe8, 0x2f, 0x12, 0x7c, 0x30, 0xe7, 0x25, 0x1e, 0x7d, 0x71, 0x93, 0x07, 0x80, 0x37, 0x95,
	0x1f, 0xdf, 0xec, 0xfd, 0x40, 0x59, 0x20, 0xb5, 0x30, 0xe3, 0x16, 0x8a, 0x66, 0x02, 0x17, 0x5e,
	0xc6, 0x85, 0xb5, 0x70, 0xc5, 0x05, 0x57, 0x59, 0x40, 0x17, 0x93, 0x03, 0x2a, 0x75, 0xbb, 0x3b,
	0xab, 0xa6, 0x26, 0x2f, 0xb1, 0x95, 0xc7, 0x73, 0x4a, 0x52, 0x67, 0x06, 0xdc, 0xc9, 0x06, 0x82,
	0x1e, 0x89, 0xd5, 0xb3, 0x17, 0x8f, 0xca, 0x7b, 0x73, 0x48, 0x71, 0x0e, 0x38, 0xe7, 0xb3, 0x1c,
	0x64, 0xef, 0x01, 0xb3, 0x1c, 0x4c, 0xcc, 0xed, 0xca, 0x02, 0x72, 0xe1, 0xae, 0x60, 0xc4, 0x46,
	0x33, 0xb2, 0x20, 0x18, 0xd2, 0x2b, 0x7b, 0xf3, 0x8a, 0x52, 0x7f, 0x47, 0x50, 0x48, 0x0f, 0x1e,
	0x24, 0x1c, 0x54, 0xb8, 0x83, 0xab, 0x52, 0xbd, 0x5a, 0x80, 0x5a, 0x7c, 0x06, 0xcb, 0xaa, 0x1b,
	0x75, 0x82, 0x1e, 0x0e, 0xd0, 0x56, 0x4d, 0x4f, 0x5e, 0xe1, 0x5f, 0x1d, 0xd4, 0x68, 0x45, 0x9a,
	0xee, 0x19, 0xad, 0xea, 0x8d, 0x0c, 0x8b, 0x4e, 0xb3, 0xb1, 0x81, 0x3a, 0x40, 0x03, 0x33, 0x03,
	0xcd, 0xde, 0x55, 0x26, 0xb2, 0xac, 0x58, 0x21, 0x4c, 0xad, 0xac, 0xe8, 0xd8, 0x77, 0x4c, 0x0b,
	0xb3, 0x50, 0xee, 0x67, 0x84, 0x79, 0xd6, 0x74, 0x34, 0x5a, 0xdf, 0x8f, 0x46, 0x74, 0xc8, 0x4a,
	0xd6, 0x7b, 0xdc, 0x57, 0x1f, 0x5d, 0xd5, 0x0d, 0xdf, 0xbe, 0xde, 0xd9, 0xc1, 0x80, 0xb6, 0xca,
	0xa9, 0x99, 0x41, 0xd8, 0x2a, 0x45, 0xd3, 0x91, 0xb0, 0x55, 0x8a, 0x47, 0x90, 0x85, 0xe7, 0x9f,
	0x7c, 0xf3, 0xd1, 0x99, 0xe7, 0x98, 0xee, 0x59, 0xed, 0x93, 0x83, 0x28, 0xaa, 0x59, 0x5e, 0xff,
	0x09, 0xfd, 0x57, 0xc4, 0xf2, 0x9c, 0x27, 0x21, 0x0e, 0x86, 0xb6, 0x85, 0xc3, 0xe9, 0x7f, 0x5b,
	0xbe, 0x5d, 0xa2, 0x42, 0x1f, 0xfd, 0x3b, 0x00, 0x00, 0xff, 0xff, 0xa2, 0x53, 0xf5, 0xbf, 0x9b,
	0x19, 0x00, 0x00,
}
