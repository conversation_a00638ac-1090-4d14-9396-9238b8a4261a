// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-mic-ext/channel-mic-ext.proto

package channel_mic_ext // import "golang.52tt.com/protocol/services/channel-mic-ext"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SetGameNickReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId,omitempty"`
	MicId                uint32   `protobuf:"varint,2,opt,name=micId,proto3" json:"micId,omitempty"`
	GameNick             string   `protobuf:"bytes,3,opt,name=gameNick,proto3" json:"gameNick,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameNickReq) Reset()         { *m = SetGameNickReq{} }
func (m *SetGameNickReq) String() string { return proto.CompactTextString(m) }
func (*SetGameNickReq) ProtoMessage()    {}
func (*SetGameNickReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{0}
}
func (m *SetGameNickReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameNickReq.Unmarshal(m, b)
}
func (m *SetGameNickReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameNickReq.Marshal(b, m, deterministic)
}
func (dst *SetGameNickReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameNickReq.Merge(dst, src)
}
func (m *SetGameNickReq) XXX_Size() int {
	return xxx_messageInfo_SetGameNickReq.Size(m)
}
func (m *SetGameNickReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameNickReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameNickReq proto.InternalMessageInfo

func (m *SetGameNickReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetGameNickReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *SetGameNickReq) GetGameNick() string {
	if m != nil {
		return m.GameNick
	}
	return ""
}

func (m *SetGameNickReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetGameNickResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameNickResp) Reset()         { *m = SetGameNickResp{} }
func (m *SetGameNickResp) String() string { return proto.CompactTextString(m) }
func (*SetGameNickResp) ProtoMessage()    {}
func (*SetGameNickResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{1}
}
func (m *SetGameNickResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameNickResp.Unmarshal(m, b)
}
func (m *SetGameNickResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameNickResp.Marshal(b, m, deterministic)
}
func (dst *SetGameNickResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameNickResp.Merge(dst, src)
}
func (m *SetGameNickResp) XXX_Size() int {
	return xxx_messageInfo_SetGameNickResp.Size(m)
}
func (m *SetGameNickResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameNickResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameNickResp proto.InternalMessageInfo

type GameNickResult struct {
	MicId                uint32   `protobuf:"varint,1,opt,name=micId,proto3" json:"micId,omitempty"`
	GameNick             string   `protobuf:"bytes,2,opt,name=gameNick,proto3" json:"gameNick,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Time                 uint32   `protobuf:"varint,4,opt,name=time,proto3" json:"time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameNickResult) Reset()         { *m = GameNickResult{} }
func (m *GameNickResult) String() string { return proto.CompactTextString(m) }
func (*GameNickResult) ProtoMessage()    {}
func (*GameNickResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{2}
}
func (m *GameNickResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameNickResult.Unmarshal(m, b)
}
func (m *GameNickResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameNickResult.Marshal(b, m, deterministic)
}
func (dst *GameNickResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameNickResult.Merge(dst, src)
}
func (m *GameNickResult) XXX_Size() int {
	return xxx_messageInfo_GameNickResult.Size(m)
}
func (m *GameNickResult) XXX_DiscardUnknown() {
	xxx_messageInfo_GameNickResult.DiscardUnknown(m)
}

var xxx_messageInfo_GameNickResult proto.InternalMessageInfo

func (m *GameNickResult) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *GameNickResult) GetGameNick() string {
	if m != nil {
		return m.GameNick
	}
	return ""
}

func (m *GameNickResult) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameNickResult) GetTime() uint32 {
	if m != nil {
		return m.Time
	}
	return 0
}

type GetGameNickReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameNickReq) Reset()         { *m = GetGameNickReq{} }
func (m *GetGameNickReq) String() string { return proto.CompactTextString(m) }
func (*GetGameNickReq) ProtoMessage()    {}
func (*GetGameNickReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{3}
}
func (m *GetGameNickReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameNickReq.Unmarshal(m, b)
}
func (m *GetGameNickReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameNickReq.Marshal(b, m, deterministic)
}
func (dst *GetGameNickReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameNickReq.Merge(dst, src)
}
func (m *GetGameNickReq) XXX_Size() int {
	return xxx_messageInfo_GetGameNickReq.Size(m)
}
func (m *GetGameNickReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameNickReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameNickReq proto.InternalMessageInfo

func (m *GetGameNickReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetGameNickResp struct {
	ResultList           []*GameNickResult `protobuf:"bytes,1,rep,name=resultList,proto3" json:"resultList,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGameNickResp) Reset()         { *m = GetGameNickResp{} }
func (m *GetGameNickResp) String() string { return proto.CompactTextString(m) }
func (*GetGameNickResp) ProtoMessage()    {}
func (*GetGameNickResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{4}
}
func (m *GetGameNickResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameNickResp.Unmarshal(m, b)
}
func (m *GetGameNickResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameNickResp.Marshal(b, m, deterministic)
}
func (dst *GetGameNickResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameNickResp.Merge(dst, src)
}
func (m *GetGameNickResp) XXX_Size() int {
	return xxx_messageInfo_GetGameNickResp.Size(m)
}
func (m *GetGameNickResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameNickResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameNickResp proto.InternalMessageInfo

func (m *GetGameNickResp) GetResultList() []*GameNickResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

// 麦位名称 begin
type MicName struct {
	MicId                uint32   `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	MicName              string   `protobuf:"bytes,2,opt,name=mic_name,json=micName,proto3" json:"mic_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicName) Reset()         { *m = MicName{} }
func (m *MicName) String() string { return proto.CompactTextString(m) }
func (*MicName) ProtoMessage()    {}
func (*MicName) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{5}
}
func (m *MicName) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicName.Unmarshal(m, b)
}
func (m *MicName) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicName.Marshal(b, m, deterministic)
}
func (dst *MicName) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicName.Merge(dst, src)
}
func (m *MicName) XXX_Size() int {
	return xxx_messageInfo_MicName.Size(m)
}
func (m *MicName) XXX_DiscardUnknown() {
	xxx_messageInfo_MicName.DiscardUnknown(m)
}

var xxx_messageInfo_MicName proto.InternalMessageInfo

func (m *MicName) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *MicName) GetMicName() string {
	if m != nil {
		return m.MicName
	}
	return ""
}

type SchemeMicName struct {
	SchemeId             uint32     `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	MicNames             []*MicName `protobuf:"bytes,2,rep,name=mic_names,json=micNames,proto3" json:"mic_names,omitempty"`
	LastOperateTime      int64      `protobuf:"varint,3,opt,name=last_operate_time,json=lastOperateTime,proto3" json:"last_operate_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SchemeMicName) Reset()         { *m = SchemeMicName{} }
func (m *SchemeMicName) String() string { return proto.CompactTextString(m) }
func (*SchemeMicName) ProtoMessage()    {}
func (*SchemeMicName) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{6}
}
func (m *SchemeMicName) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SchemeMicName.Unmarshal(m, b)
}
func (m *SchemeMicName) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SchemeMicName.Marshal(b, m, deterministic)
}
func (dst *SchemeMicName) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SchemeMicName.Merge(dst, src)
}
func (m *SchemeMicName) XXX_Size() int {
	return xxx_messageInfo_SchemeMicName.Size(m)
}
func (m *SchemeMicName) XXX_DiscardUnknown() {
	xxx_messageInfo_SchemeMicName.DiscardUnknown(m)
}

var xxx_messageInfo_SchemeMicName proto.InternalMessageInfo

func (m *SchemeMicName) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *SchemeMicName) GetMicNames() []*MicName {
	if m != nil {
		return m.MicNames
	}
	return nil
}

func (m *SchemeMicName) GetLastOperateTime() int64 {
	if m != nil {
		return m.LastOperateTime
	}
	return 0
}

type GetMicNameReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SchemeId             uint32   `protobuf:"varint,2,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMicNameReq) Reset()         { *m = GetMicNameReq{} }
func (m *GetMicNameReq) String() string { return proto.CompactTextString(m) }
func (*GetMicNameReq) ProtoMessage()    {}
func (*GetMicNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{7}
}
func (m *GetMicNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicNameReq.Unmarshal(m, b)
}
func (m *GetMicNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicNameReq.Marshal(b, m, deterministic)
}
func (dst *GetMicNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicNameReq.Merge(dst, src)
}
func (m *GetMicNameReq) XXX_Size() int {
	return xxx_messageInfo_GetMicNameReq.Size(m)
}
func (m *GetMicNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicNameReq proto.InternalMessageInfo

func (m *GetMicNameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMicNameReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

type GetMicNameResp struct {
	SwitchState           bool           `protobuf:"varint,1,opt,name=switch_state,json=switchState,proto3" json:"switch_state,omitempty"`
	LastSwitchOperateTime int64          `protobuf:"varint,2,opt,name=last_switch_operate_time,json=lastSwitchOperateTime,proto3" json:"last_switch_operate_time,omitempty"`
	SchemeMicName         *SchemeMicName `protobuf:"bytes,3,opt,name=scheme_mic_name,json=schemeMicName,proto3" json:"scheme_mic_name,omitempty"`
	LastModifiedTime      int64          `protobuf:"varint,4,opt,name=last_modified_time,json=lastModifiedTime,proto3" json:"last_modified_time,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}       `json:"-"`
	XXX_unrecognized      []byte         `json:"-"`
	XXX_sizecache         int32          `json:"-"`
}

func (m *GetMicNameResp) Reset()         { *m = GetMicNameResp{} }
func (m *GetMicNameResp) String() string { return proto.CompactTextString(m) }
func (*GetMicNameResp) ProtoMessage()    {}
func (*GetMicNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{8}
}
func (m *GetMicNameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicNameResp.Unmarshal(m, b)
}
func (m *GetMicNameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicNameResp.Marshal(b, m, deterministic)
}
func (dst *GetMicNameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicNameResp.Merge(dst, src)
}
func (m *GetMicNameResp) XXX_Size() int {
	return xxx_messageInfo_GetMicNameResp.Size(m)
}
func (m *GetMicNameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicNameResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicNameResp proto.InternalMessageInfo

func (m *GetMicNameResp) GetSwitchState() bool {
	if m != nil {
		return m.SwitchState
	}
	return false
}

func (m *GetMicNameResp) GetLastSwitchOperateTime() int64 {
	if m != nil {
		return m.LastSwitchOperateTime
	}
	return 0
}

func (m *GetMicNameResp) GetSchemeMicName() *SchemeMicName {
	if m != nil {
		return m.SchemeMicName
	}
	return nil
}

func (m *GetMicNameResp) GetLastModifiedTime() int64 {
	if m != nil {
		return m.LastModifiedTime
	}
	return 0
}

type GetAllMicNameReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllMicNameReq) Reset()         { *m = GetAllMicNameReq{} }
func (m *GetAllMicNameReq) String() string { return proto.CompactTextString(m) }
func (*GetAllMicNameReq) ProtoMessage()    {}
func (*GetAllMicNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{9}
}
func (m *GetAllMicNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllMicNameReq.Unmarshal(m, b)
}
func (m *GetAllMicNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllMicNameReq.Marshal(b, m, deterministic)
}
func (dst *GetAllMicNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllMicNameReq.Merge(dst, src)
}
func (m *GetAllMicNameReq) XXX_Size() int {
	return xxx_messageInfo_GetAllMicNameReq.Size(m)
}
func (m *GetAllMicNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllMicNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllMicNameReq proto.InternalMessageInfo

func (m *GetAllMicNameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetAllMicNameResp struct {
	SwitchState           bool             `protobuf:"varint,1,opt,name=switch_state,json=switchState,proto3" json:"switch_state,omitempty"`
	LastSwitchOperateTime int64            `protobuf:"varint,2,opt,name=last_switch_operate_time,json=lastSwitchOperateTime,proto3" json:"last_switch_operate_time,omitempty"`
	SchemeMicNames        []*SchemeMicName `protobuf:"bytes,3,rep,name=scheme_mic_names,json=schemeMicNames,proto3" json:"scheme_mic_names,omitempty"`
	LastModifiedTime      int64            `protobuf:"varint,4,opt,name=last_modified_time,json=lastModifiedTime,proto3" json:"last_modified_time,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}         `json:"-"`
	XXX_unrecognized      []byte           `json:"-"`
	XXX_sizecache         int32            `json:"-"`
}

func (m *GetAllMicNameResp) Reset()         { *m = GetAllMicNameResp{} }
func (m *GetAllMicNameResp) String() string { return proto.CompactTextString(m) }
func (*GetAllMicNameResp) ProtoMessage()    {}
func (*GetAllMicNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{10}
}
func (m *GetAllMicNameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllMicNameResp.Unmarshal(m, b)
}
func (m *GetAllMicNameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllMicNameResp.Marshal(b, m, deterministic)
}
func (dst *GetAllMicNameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllMicNameResp.Merge(dst, src)
}
func (m *GetAllMicNameResp) XXX_Size() int {
	return xxx_messageInfo_GetAllMicNameResp.Size(m)
}
func (m *GetAllMicNameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllMicNameResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllMicNameResp proto.InternalMessageInfo

func (m *GetAllMicNameResp) GetSwitchState() bool {
	if m != nil {
		return m.SwitchState
	}
	return false
}

func (m *GetAllMicNameResp) GetLastSwitchOperateTime() int64 {
	if m != nil {
		return m.LastSwitchOperateTime
	}
	return 0
}

func (m *GetAllMicNameResp) GetSchemeMicNames() []*SchemeMicName {
	if m != nil {
		return m.SchemeMicNames
	}
	return nil
}

func (m *GetAllMicNameResp) GetLastModifiedTime() int64 {
	if m != nil {
		return m.LastModifiedTime
	}
	return 0
}

type SetMicNameReq struct {
	ChannelId            uint32     `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SchemeId             uint32     `protobuf:"varint,2,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	MicNames             []*MicName `protobuf:"bytes,3,rep,name=mic_names,json=micNames,proto3" json:"mic_names,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SetMicNameReq) Reset()         { *m = SetMicNameReq{} }
func (m *SetMicNameReq) String() string { return proto.CompactTextString(m) }
func (*SetMicNameReq) ProtoMessage()    {}
func (*SetMicNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{11}
}
func (m *SetMicNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicNameReq.Unmarshal(m, b)
}
func (m *SetMicNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicNameReq.Marshal(b, m, deterministic)
}
func (dst *SetMicNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicNameReq.Merge(dst, src)
}
func (m *SetMicNameReq) XXX_Size() int {
	return xxx_messageInfo_SetMicNameReq.Size(m)
}
func (m *SetMicNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicNameReq proto.InternalMessageInfo

func (m *SetMicNameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMicNameReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *SetMicNameReq) GetMicNames() []*MicName {
	if m != nil {
		return m.MicNames
	}
	return nil
}

type SetMicNameResp struct {
	LastOperateTime      int64    `protobuf:"varint,1,opt,name=last_operate_time,json=lastOperateTime,proto3" json:"last_operate_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMicNameResp) Reset()         { *m = SetMicNameResp{} }
func (m *SetMicNameResp) String() string { return proto.CompactTextString(m) }
func (*SetMicNameResp) ProtoMessage()    {}
func (*SetMicNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{12}
}
func (m *SetMicNameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicNameResp.Unmarshal(m, b)
}
func (m *SetMicNameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicNameResp.Marshal(b, m, deterministic)
}
func (dst *SetMicNameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicNameResp.Merge(dst, src)
}
func (m *SetMicNameResp) XXX_Size() int {
	return xxx_messageInfo_SetMicNameResp.Size(m)
}
func (m *SetMicNameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicNameResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicNameResp proto.InternalMessageInfo

func (m *SetMicNameResp) GetLastOperateTime() int64 {
	if m != nil {
		return m.LastOperateTime
	}
	return 0
}

type SetMicNameSwitchReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	State                bool     `protobuf:"varint,2,opt,name=state,proto3" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMicNameSwitchReq) Reset()         { *m = SetMicNameSwitchReq{} }
func (m *SetMicNameSwitchReq) String() string { return proto.CompactTextString(m) }
func (*SetMicNameSwitchReq) ProtoMessage()    {}
func (*SetMicNameSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{13}
}
func (m *SetMicNameSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicNameSwitchReq.Unmarshal(m, b)
}
func (m *SetMicNameSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicNameSwitchReq.Marshal(b, m, deterministic)
}
func (dst *SetMicNameSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicNameSwitchReq.Merge(dst, src)
}
func (m *SetMicNameSwitchReq) XXX_Size() int {
	return xxx_messageInfo_SetMicNameSwitchReq.Size(m)
}
func (m *SetMicNameSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicNameSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicNameSwitchReq proto.InternalMessageInfo

func (m *SetMicNameSwitchReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMicNameSwitchReq) GetState() bool {
	if m != nil {
		return m.State
	}
	return false
}

type SetMicNameSwitchResp struct {
	LastSwitchOperateTime int64    `protobuf:"varint,1,opt,name=last_switch_operate_time,json=lastSwitchOperateTime,proto3" json:"last_switch_operate_time,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *SetMicNameSwitchResp) Reset()         { *m = SetMicNameSwitchResp{} }
func (m *SetMicNameSwitchResp) String() string { return proto.CompactTextString(m) }
func (*SetMicNameSwitchResp) ProtoMessage()    {}
func (*SetMicNameSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_mic_ext_15122b066fbff91c, []int{14}
}
func (m *SetMicNameSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicNameSwitchResp.Unmarshal(m, b)
}
func (m *SetMicNameSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicNameSwitchResp.Marshal(b, m, deterministic)
}
func (dst *SetMicNameSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicNameSwitchResp.Merge(dst, src)
}
func (m *SetMicNameSwitchResp) XXX_Size() int {
	return xxx_messageInfo_SetMicNameSwitchResp.Size(m)
}
func (m *SetMicNameSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicNameSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicNameSwitchResp proto.InternalMessageInfo

func (m *SetMicNameSwitchResp) GetLastSwitchOperateTime() int64 {
	if m != nil {
		return m.LastSwitchOperateTime
	}
	return 0
}

func init() {
	proto.RegisterType((*SetGameNickReq)(nil), "channel_mic_ext.SetGameNickReq")
	proto.RegisterType((*SetGameNickResp)(nil), "channel_mic_ext.SetGameNickResp")
	proto.RegisterType((*GameNickResult)(nil), "channel_mic_ext.GameNickResult")
	proto.RegisterType((*GetGameNickReq)(nil), "channel_mic_ext.GetGameNickReq")
	proto.RegisterType((*GetGameNickResp)(nil), "channel_mic_ext.GetGameNickResp")
	proto.RegisterType((*MicName)(nil), "channel_mic_ext.MicName")
	proto.RegisterType((*SchemeMicName)(nil), "channel_mic_ext.SchemeMicName")
	proto.RegisterType((*GetMicNameReq)(nil), "channel_mic_ext.GetMicNameReq")
	proto.RegisterType((*GetMicNameResp)(nil), "channel_mic_ext.GetMicNameResp")
	proto.RegisterType((*GetAllMicNameReq)(nil), "channel_mic_ext.GetAllMicNameReq")
	proto.RegisterType((*GetAllMicNameResp)(nil), "channel_mic_ext.GetAllMicNameResp")
	proto.RegisterType((*SetMicNameReq)(nil), "channel_mic_ext.SetMicNameReq")
	proto.RegisterType((*SetMicNameResp)(nil), "channel_mic_ext.SetMicNameResp")
	proto.RegisterType((*SetMicNameSwitchReq)(nil), "channel_mic_ext.SetMicNameSwitchReq")
	proto.RegisterType((*SetMicNameSwitchResp)(nil), "channel_mic_ext.SetMicNameSwitchResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelMicExtClient is the client API for ChannelMicExt service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelMicExtClient interface {
	SetGameNick(ctx context.Context, in *SetGameNickReq, opts ...grpc.CallOption) (*SetGameNickResp, error)
	GetGameNick(ctx context.Context, in *GetGameNickReq, opts ...grpc.CallOption) (*GetGameNickResp, error)
	// 查询麦位名称信息
	GetMicName(ctx context.Context, in *GetMicNameReq, opts ...grpc.CallOption) (*GetMicNameResp, error)
	// 查询全部麦位名称信息
	GetAllMicName(ctx context.Context, in *GetAllMicNameReq, opts ...grpc.CallOption) (*GetAllMicNameResp, error)
	// 设置麦位名称信息
	SetMicName(ctx context.Context, in *SetMicNameReq, opts ...grpc.CallOption) (*SetMicNameResp, error)
	// 设置麦位名称开关状态
	SetMicNameSwitch(ctx context.Context, in *SetMicNameSwitchReq, opts ...grpc.CallOption) (*SetMicNameSwitchResp, error)
}

type channelMicExtClient struct {
	cc *grpc.ClientConn
}

func NewChannelMicExtClient(cc *grpc.ClientConn) ChannelMicExtClient {
	return &channelMicExtClient{cc}
}

func (c *channelMicExtClient) SetGameNick(ctx context.Context, in *SetGameNickReq, opts ...grpc.CallOption) (*SetGameNickResp, error) {
	out := new(SetGameNickResp)
	err := c.cc.Invoke(ctx, "/channel_mic_ext.ChannelMicExt/SetGameNick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicExtClient) GetGameNick(ctx context.Context, in *GetGameNickReq, opts ...grpc.CallOption) (*GetGameNickResp, error) {
	out := new(GetGameNickResp)
	err := c.cc.Invoke(ctx, "/channel_mic_ext.ChannelMicExt/GetGameNick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicExtClient) GetMicName(ctx context.Context, in *GetMicNameReq, opts ...grpc.CallOption) (*GetMicNameResp, error) {
	out := new(GetMicNameResp)
	err := c.cc.Invoke(ctx, "/channel_mic_ext.ChannelMicExt/GetMicName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicExtClient) GetAllMicName(ctx context.Context, in *GetAllMicNameReq, opts ...grpc.CallOption) (*GetAllMicNameResp, error) {
	out := new(GetAllMicNameResp)
	err := c.cc.Invoke(ctx, "/channel_mic_ext.ChannelMicExt/GetAllMicName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicExtClient) SetMicName(ctx context.Context, in *SetMicNameReq, opts ...grpc.CallOption) (*SetMicNameResp, error) {
	out := new(SetMicNameResp)
	err := c.cc.Invoke(ctx, "/channel_mic_ext.ChannelMicExt/SetMicName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMicExtClient) SetMicNameSwitch(ctx context.Context, in *SetMicNameSwitchReq, opts ...grpc.CallOption) (*SetMicNameSwitchResp, error) {
	out := new(SetMicNameSwitchResp)
	err := c.cc.Invoke(ctx, "/channel_mic_ext.ChannelMicExt/SetMicNameSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelMicExtServer is the server API for ChannelMicExt service.
type ChannelMicExtServer interface {
	SetGameNick(context.Context, *SetGameNickReq) (*SetGameNickResp, error)
	GetGameNick(context.Context, *GetGameNickReq) (*GetGameNickResp, error)
	// 查询麦位名称信息
	GetMicName(context.Context, *GetMicNameReq) (*GetMicNameResp, error)
	// 查询全部麦位名称信息
	GetAllMicName(context.Context, *GetAllMicNameReq) (*GetAllMicNameResp, error)
	// 设置麦位名称信息
	SetMicName(context.Context, *SetMicNameReq) (*SetMicNameResp, error)
	// 设置麦位名称开关状态
	SetMicNameSwitch(context.Context, *SetMicNameSwitchReq) (*SetMicNameSwitchResp, error)
}

func RegisterChannelMicExtServer(s *grpc.Server, srv ChannelMicExtServer) {
	s.RegisterService(&_ChannelMicExt_serviceDesc, srv)
}

func _ChannelMicExt_SetGameNick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGameNickReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicExtServer).SetGameNick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_ext.ChannelMicExt/SetGameNick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicExtServer).SetGameNick(ctx, req.(*SetGameNickReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicExt_GetGameNick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameNickReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicExtServer).GetGameNick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_ext.ChannelMicExt/GetGameNick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicExtServer).GetGameNick(ctx, req.(*GetGameNickReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicExt_GetMicName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMicNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicExtServer).GetMicName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_ext.ChannelMicExt/GetMicName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicExtServer).GetMicName(ctx, req.(*GetMicNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicExt_GetAllMicName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllMicNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicExtServer).GetAllMicName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_ext.ChannelMicExt/GetAllMicName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicExtServer).GetAllMicName(ctx, req.(*GetAllMicNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicExt_SetMicName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMicNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicExtServer).SetMicName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_ext.ChannelMicExt/SetMicName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicExtServer).SetMicName(ctx, req.(*SetMicNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMicExt_SetMicNameSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMicNameSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMicExtServer).SetMicNameSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_mic_ext.ChannelMicExt/SetMicNameSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMicExtServer).SetMicNameSwitch(ctx, req.(*SetMicNameSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelMicExt_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_mic_ext.ChannelMicExt",
	HandlerType: (*ChannelMicExtServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetGameNick",
			Handler:    _ChannelMicExt_SetGameNick_Handler,
		},
		{
			MethodName: "GetGameNick",
			Handler:    _ChannelMicExt_GetGameNick_Handler,
		},
		{
			MethodName: "GetMicName",
			Handler:    _ChannelMicExt_GetMicName_Handler,
		},
		{
			MethodName: "GetAllMicName",
			Handler:    _ChannelMicExt_GetAllMicName_Handler,
		},
		{
			MethodName: "SetMicName",
			Handler:    _ChannelMicExt_SetMicName_Handler,
		},
		{
			MethodName: "SetMicNameSwitch",
			Handler:    _ChannelMicExt_SetMicNameSwitch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-mic-ext/channel-mic-ext.proto",
}

func init() {
	proto.RegisterFile("channel-mic-ext/channel-mic-ext.proto", fileDescriptor_channel_mic_ext_15122b066fbff91c)
}

var fileDescriptor_channel_mic_ext_15122b066fbff91c = []byte{
	// 687 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x56, 0xdd, 0x6e, 0xd3, 0x30,
	0x14, 0x26, 0xcd, 0x7e, 0xda, 0x53, 0xfa, 0x33, 0xb3, 0x49, 0xa1, 0xc0, 0xd6, 0x59, 0x4c, 0x9a,
	0x10, 0xeb, 0xb4, 0x4e, 0x13, 0x17, 0x20, 0x21, 0x40, 0x10, 0x06, 0x6c, 0x93, 0x12, 0x2e, 0x10,
	0x37, 0x55, 0x70, 0xcd, 0x6a, 0x91, 0x34, 0x5d, 0xed, 0xc1, 0xae, 0x79, 0x01, 0xee, 0x78, 0x40,
	0x6e, 0x78, 0x0d, 0x64, 0x27, 0x4d, 0xea, 0x34, 0xa4, 0x9d, 0x04, 0x77, 0xf5, 0xf9, 0xf9, 0xce,
	0x77, 0xbe, 0x73, 0xec, 0x06, 0x76, 0xc8, 0xc0, 0x1b, 0x0e, 0xa9, 0xbf, 0x17, 0x30, 0xb2, 0x47,
	0xaf, 0xc4, 0x7e, 0xe6, 0xdc, 0x19, 0x8d, 0x43, 0x11, 0xa2, 0x46, 0x6c, 0xee, 0x05, 0x8c, 0xf4,
	0xe8, 0x95, 0xc0, 0x63, 0xa8, 0xbb, 0x54, 0xd8, 0x5e, 0x40, 0x4f, 0x19, 0xf9, 0xe2, 0xd0, 0x0b,
	0x74, 0x17, 0x2a, 0x71, 0xd0, 0x71, 0xdf, 0x32, 0xda, 0xc6, 0x6e, 0xcd, 0x49, 0x0d, 0x68, 0x1d,
	0x96, 0x03, 0x46, 0x8e, 0xfb, 0x56, 0x49, 0x79, 0xa2, 0x03, 0x6a, 0x41, 0xf9, 0x3c, 0x86, 0xb0,
	0xcc, 0xb6, 0xb1, 0x5b, 0x71, 0x92, 0x33, 0x6a, 0x82, 0x79, 0xc9, 0xfa, 0xd6, 0x92, 0x8a, 0x97,
	0x3f, 0xf1, 0x1a, 0x34, 0xb4, 0x9a, 0x7c, 0x84, 0x07, 0x50, 0x9f, 0x3a, 0x5f, 0xfa, 0x22, 0x2d,
	0x64, 0xfc, 0xad, 0x50, 0x29, 0xbf, 0x90, 0x99, 0x14, 0x42, 0x08, 0x96, 0x04, 0x0b, 0x68, 0x5c,
	0x5b, 0xfd, 0xc6, 0x1d, 0xa8, 0xdb, 0xd7, 0x68, 0x18, 0x3b, 0xd0, 0xb0, 0x75, 0xb2, 0xe8, 0x29,
	0xc0, 0x58, 0x91, 0x7c, 0xc7, 0xb8, 0xb0, 0x8c, 0xb6, 0xb9, 0x5b, 0xed, 0x6e, 0x75, 0x32, 0xca,
	0x76, 0xf4, 0x7e, 0x9c, 0xa9, 0x14, 0xfc, 0x18, 0x56, 0x4f, 0x18, 0x39, 0xf5, 0x02, 0x8a, 0x36,
	0x60, 0x45, 0x26, 0xb0, 0x4c, 0x9f, 0xb7, 0xa1, 0x2c, 0xcd, 0x43, 0x2f, 0xa0, 0x71, 0x9f, 0xab,
	0x41, 0x94, 0x81, 0x7f, 0x18, 0x50, 0x73, 0xc9, 0x80, 0x06, 0x74, 0x82, 0x71, 0x07, 0x2a, 0x5c,
	0x19, 0x52, 0x98, 0x72, 0x64, 0x38, 0xee, 0xa3, 0x23, 0xa8, 0x4c, 0x90, 0xb8, 0x55, 0x52, 0x5c,
	0xad, 0x19, 0xae, 0x31, 0x92, 0x53, 0x8e, 0x8b, 0x70, 0xf4, 0x00, 0xd6, 0x7c, 0x8f, 0x8b, 0x5e,
	0x38, 0xa2, 0x63, 0x4f, 0xd0, 0x9e, 0xd2, 0x51, 0x4a, 0x6b, 0x3a, 0x0d, 0xe9, 0x38, 0x8b, 0xec,
	0xef, 0xa5, 0xa4, 0x6f, 0xa1, 0x66, 0x53, 0x31, 0xc1, 0xa0, 0x17, 0xe8, 0x1e, 0xc0, 0xa4, 0x02,
	0xcb, 0xd9, 0x21, 0x8d, 0x6f, 0x49, 0xe7, 0x8b, 0x7f, 0x19, 0x6a, 0x40, 0x09, 0x1a, 0x1f, 0xa1,
	0x6d, 0xb8, 0xc9, 0xbf, 0x31, 0x41, 0x06, 0x3d, 0x2e, 0x3c, 0x41, 0x15, 0x60, 0xd9, 0xa9, 0x46,
	0x36, 0x57, 0x9a, 0xd0, 0x23, 0xb0, 0x14, 0xdd, 0x38, 0x4e, 0x63, 0x5d, 0x52, 0xac, 0x37, 0xa4,
	0xdf, 0x55, 0xee, 0x29, 0xee, 0xe8, 0x15, 0x34, 0x62, 0x2e, 0x89, 0xde, 0xb2, 0xcb, 0x6a, 0x77,
	0x73, 0x46, 0x24, 0x4d, 0x74, 0xa7, 0xc6, 0xb5, 0x19, 0x3c, 0x04, 0xa4, 0x08, 0x04, 0x61, 0x9f,
	0x7d, 0x66, 0xb4, 0xdf, 0x4b, 0x16, 0xcf, 0x74, 0x9a, 0xd2, 0x73, 0x12, 0x3b, 0x94, 0x62, 0x07,
	0xd0, 0xb4, 0xa9, 0x78, 0xe6, 0xfb, 0x0b, 0x8b, 0x86, 0x7f, 0x1b, 0xb0, 0x96, 0xc9, 0xf9, 0xcf,
	0xd2, 0xbc, 0x86, 0x66, 0x46, 0x1a, 0x6e, 0x99, 0x6a, 0x81, 0xe6, 0x69, 0x53, 0xd7, 0xb4, 0xe1,
	0xd7, 0x14, 0xe7, 0xbb, 0x5c, 0xf0, 0x7f, 0xb5, 0x4f, 0xfa, 0xfe, 0x9b, 0x8b, 0xee, 0x3f, 0x7e,
	0xa2, 0xde, 0xc5, 0x69, 0xa9, 0x73, 0x6f, 0x84, 0x91, 0x7f, 0x23, 0xde, 0xc0, 0xad, 0x34, 0x3b,
	0x52, 0x76, 0x81, 0x3e, 0xd6, 0x61, 0x39, 0x9a, 0x62, 0x49, 0x4d, 0x31, 0x3a, 0xe0, 0x33, 0x58,
	0x9f, 0xc5, 0xe2, 0xa3, 0xc2, 0xb9, 0x1a, 0x05, 0x73, 0xed, 0xfe, 0x5c, 0x82, 0xda, 0x8b, 0xa8,
	0xe8, 0x09, 0x23, 0x2f, 0xaf, 0x04, 0x72, 0xa0, 0x3a, 0xf5, 0x20, 0xa3, 0xd9, 0xb7, 0x4c, 0xff,
	0x8b, 0x68, 0xb5, 0x8b, 0x03, 0xf8, 0x08, 0xdf, 0x90, 0x98, 0x76, 0x21, 0xa6, 0x3d, 0x0f, 0xd3,
	0x9e, 0xc1, 0x3c, 0x03, 0x48, 0x9f, 0x06, 0xb4, 0x99, 0x97, 0x91, 0x6e, 0x4d, 0x6b, 0xab, 0xd0,
	0xaf, 0x00, 0x3f, 0xa8, 0x97, 0x2b, 0xbd, 0x53, 0x68, 0x3b, 0x2f, 0x47, 0xbb, 0xa7, 0x2d, 0x3c,
	0x2f, 0x64, 0x42, 0xd5, 0x2d, 0xa2, 0xea, 0xce, 0xa1, 0xea, 0x66, 0xa9, 0x7a, 0xd0, 0xcc, 0xae,
	0x01, 0xba, 0x5f, 0x90, 0x96, 0x6c, 0x5d, 0x6b, 0x67, 0x81, 0x28, 0x59, 0xe2, 0xf9, 0xe1, 0xc7,
	0x83, 0xf3, 0xd0, 0xf7, 0x86, 0xe7, 0x9d, 0xa3, 0xae, 0x10, 0x1d, 0x12, 0x06, 0xfb, 0xea, 0xab,
	0x81, 0x84, 0xfe, 0x3e, 0xa7, 0xe3, 0xaf, 0x8c, 0x50, 0x9e, 0xfd, 0xae, 0xf8, 0xb4, 0xa2, 0x42,
	0x0e, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0x74, 0x4a, 0xe9, 0x72, 0x81, 0x08, 0x00, 0x00,
}
