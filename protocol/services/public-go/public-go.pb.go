// Code generated by protoc-gen-go. DO NOT EDIT.
// source: public-go/public-go.proto

package public_go // import "golang.52tt.com/protocol/services/public-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PublicAccountType int32

const (
	PublicAccountType_NIL                PublicAccountType = 0
	PublicAccountType_NOT_BIND           PublicAccountType = 1
	PublicAccountType_SYSTEM             PublicAccountType = 50
	PublicAccountType_BIZ_TYPE_BEGIN     PublicAccountType = 100
	PublicAccountType_BIZ_TYPE_CIRCLE    PublicAccountType = 101
	PublicAccountType_BIZ_TYPE_UGC_TOPIC PublicAccountType = 102
)

var PublicAccountType_name = map[int32]string{
	0:   "NIL",
	1:   "NOT_BIND",
	50:  "SYSTEM",
	100: "BIZ_TYPE_BEGIN",
	101: "BIZ_TYPE_CIRCLE",
	102: "BIZ_TYPE_UGC_TOPIC",
}
var PublicAccountType_value = map[string]int32{
	"NIL":                0,
	"NOT_BIND":           1,
	"SYSTEM":             50,
	"BIZ_TYPE_BEGIN":     100,
	"BIZ_TYPE_CIRCLE":    101,
	"BIZ_TYPE_UGC_TOPIC": 102,
}

func (x PublicAccountType) String() string {
	return proto.EnumName(PublicAccountType_name, int32(x))
}
func (PublicAccountType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{0}
}

// 公众号类型
type StPublicAccount struct {
	PublicId             uint32   `protobuf:"varint,1,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type                 uint32   `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	BindId               uint64   `protobuf:"varint,5,opt,name=bind_id,json=bindId,proto3" json:"bind_id,omitempty"`
	Authority            string   `protobuf:"bytes,6,opt,name=authority,proto3" json:"authority,omitempty"`
	AuthorityUrl         string   `protobuf:"bytes,7,opt,name=authority_url,json=authorityUrl,proto3" json:"authority_url,omitempty"`
	Intro                string   `protobuf:"bytes,8,opt,name=intro,proto3" json:"intro,omitempty"`
	UpdateTime           int64    `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StPublicAccount) Reset()         { *m = StPublicAccount{} }
func (m *StPublicAccount) String() string { return proto.CompactTextString(m) }
func (*StPublicAccount) ProtoMessage()    {}
func (*StPublicAccount) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{0}
}
func (m *StPublicAccount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StPublicAccount.Unmarshal(m, b)
}
func (m *StPublicAccount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StPublicAccount.Marshal(b, m, deterministic)
}
func (dst *StPublicAccount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StPublicAccount.Merge(dst, src)
}
func (m *StPublicAccount) XXX_Size() int {
	return xxx_messageInfo_StPublicAccount.Size(m)
}
func (m *StPublicAccount) XXX_DiscardUnknown() {
	xxx_messageInfo_StPublicAccount.DiscardUnknown(m)
}

var xxx_messageInfo_StPublicAccount proto.InternalMessageInfo

func (m *StPublicAccount) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

func (m *StPublicAccount) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *StPublicAccount) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StPublicAccount) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *StPublicAccount) GetBindId() uint64 {
	if m != nil {
		return m.BindId
	}
	return 0
}

func (m *StPublicAccount) GetAuthority() string {
	if m != nil {
		return m.Authority
	}
	return ""
}

func (m *StPublicAccount) GetAuthorityUrl() string {
	if m != nil {
		return m.AuthorityUrl
	}
	return ""
}

func (m *StPublicAccount) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *StPublicAccount) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type CreatePublicAccountReq struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	BindId               uint64   `protobuf:"varint,3,opt,name=bind_id,json=bindId,proto3" json:"bind_id,omitempty"`
	Authority            string   `protobuf:"bytes,4,opt,name=authority,proto3" json:"authority,omitempty"`
	AuthorityUrl         string   `protobuf:"bytes,5,opt,name=authority_url,json=authorityUrl,proto3" json:"authority_url,omitempty"`
	Intro                string   `protobuf:"bytes,6,opt,name=intro,proto3" json:"intro,omitempty"`
	Account              string   `protobuf:"bytes,7,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePublicAccountReq) Reset()         { *m = CreatePublicAccountReq{} }
func (m *CreatePublicAccountReq) String() string { return proto.CompactTextString(m) }
func (*CreatePublicAccountReq) ProtoMessage()    {}
func (*CreatePublicAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{1}
}
func (m *CreatePublicAccountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePublicAccountReq.Unmarshal(m, b)
}
func (m *CreatePublicAccountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePublicAccountReq.Marshal(b, m, deterministic)
}
func (dst *CreatePublicAccountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePublicAccountReq.Merge(dst, src)
}
func (m *CreatePublicAccountReq) XXX_Size() int {
	return xxx_messageInfo_CreatePublicAccountReq.Size(m)
}
func (m *CreatePublicAccountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePublicAccountReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePublicAccountReq proto.InternalMessageInfo

func (m *CreatePublicAccountReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreatePublicAccountReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CreatePublicAccountReq) GetBindId() uint64 {
	if m != nil {
		return m.BindId
	}
	return 0
}

func (m *CreatePublicAccountReq) GetAuthority() string {
	if m != nil {
		return m.Authority
	}
	return ""
}

func (m *CreatePublicAccountReq) GetAuthorityUrl() string {
	if m != nil {
		return m.AuthorityUrl
	}
	return ""
}

func (m *CreatePublicAccountReq) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *CreatePublicAccountReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type CreatePublicAccountResp struct {
	PublicId             uint32   `protobuf:"varint,1,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePublicAccountResp) Reset()         { *m = CreatePublicAccountResp{} }
func (m *CreatePublicAccountResp) String() string { return proto.CompactTextString(m) }
func (*CreatePublicAccountResp) ProtoMessage()    {}
func (*CreatePublicAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{2}
}
func (m *CreatePublicAccountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePublicAccountResp.Unmarshal(m, b)
}
func (m *CreatePublicAccountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePublicAccountResp.Marshal(b, m, deterministic)
}
func (dst *CreatePublicAccountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePublicAccountResp.Merge(dst, src)
}
func (m *CreatePublicAccountResp) XXX_Size() int {
	return xxx_messageInfo_CreatePublicAccountResp.Size(m)
}
func (m *CreatePublicAccountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePublicAccountResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePublicAccountResp proto.InternalMessageInfo

func (m *CreatePublicAccountResp) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

func (m *CreatePublicAccountResp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type GetPublicAccountReq struct {
	PublicId             uint32   `protobuf:"varint,1,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublicAccountReq) Reset()         { *m = GetPublicAccountReq{} }
func (m *GetPublicAccountReq) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountReq) ProtoMessage()    {}
func (*GetPublicAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{3}
}
func (m *GetPublicAccountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountReq.Unmarshal(m, b)
}
func (m *GetPublicAccountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountReq.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountReq.Merge(dst, src)
}
func (m *GetPublicAccountReq) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountReq.Size(m)
}
func (m *GetPublicAccountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountReq proto.InternalMessageInfo

func (m *GetPublicAccountReq) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

type GetPublicAccountResp struct {
	PublicAccount        *StPublicAccount `protobuf:"bytes,1,opt,name=public_account,json=publicAccount,proto3" json:"public_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPublicAccountResp) Reset()         { *m = GetPublicAccountResp{} }
func (m *GetPublicAccountResp) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountResp) ProtoMessage()    {}
func (*GetPublicAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{4}
}
func (m *GetPublicAccountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountResp.Unmarshal(m, b)
}
func (m *GetPublicAccountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountResp.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountResp.Merge(dst, src)
}
func (m *GetPublicAccountResp) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountResp.Size(m)
}
func (m *GetPublicAccountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountResp proto.InternalMessageInfo

func (m *GetPublicAccountResp) GetPublicAccount() *StPublicAccount {
	if m != nil {
		return m.PublicAccount
	}
	return nil
}

type GetPublicAccountByBindIdReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	BindId               uint64   `protobuf:"varint,2,opt,name=bind_id,json=bindId,proto3" json:"bind_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublicAccountByBindIdReq) Reset()         { *m = GetPublicAccountByBindIdReq{} }
func (m *GetPublicAccountByBindIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountByBindIdReq) ProtoMessage()    {}
func (*GetPublicAccountByBindIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{5}
}
func (m *GetPublicAccountByBindIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountByBindIdReq.Unmarshal(m, b)
}
func (m *GetPublicAccountByBindIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountByBindIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountByBindIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountByBindIdReq.Merge(dst, src)
}
func (m *GetPublicAccountByBindIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountByBindIdReq.Size(m)
}
func (m *GetPublicAccountByBindIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountByBindIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountByBindIdReq proto.InternalMessageInfo

func (m *GetPublicAccountByBindIdReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetPublicAccountByBindIdReq) GetBindId() uint64 {
	if m != nil {
		return m.BindId
	}
	return 0
}

type GetPublicAccountByBindIdResp struct {
	PublicAccount        *StPublicAccount `protobuf:"bytes,1,opt,name=public_account,json=publicAccount,proto3" json:"public_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPublicAccountByBindIdResp) Reset()         { *m = GetPublicAccountByBindIdResp{} }
func (m *GetPublicAccountByBindIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountByBindIdResp) ProtoMessage()    {}
func (*GetPublicAccountByBindIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{6}
}
func (m *GetPublicAccountByBindIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountByBindIdResp.Unmarshal(m, b)
}
func (m *GetPublicAccountByBindIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountByBindIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountByBindIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountByBindIdResp.Merge(dst, src)
}
func (m *GetPublicAccountByBindIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountByBindIdResp.Size(m)
}
func (m *GetPublicAccountByBindIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountByBindIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountByBindIdResp proto.InternalMessageInfo

func (m *GetPublicAccountByBindIdResp) GetPublicAccount() *StPublicAccount {
	if m != nil {
		return m.PublicAccount
	}
	return nil
}

type GetPublicAccountsByIdListReq struct {
	PublicIdList         []uint32 `protobuf:"varint,1,rep,packed,name=public_id_list,json=publicIdList,proto3" json:"public_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublicAccountsByIdListReq) Reset()         { *m = GetPublicAccountsByIdListReq{} }
func (m *GetPublicAccountsByIdListReq) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountsByIdListReq) ProtoMessage()    {}
func (*GetPublicAccountsByIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{7}
}
func (m *GetPublicAccountsByIdListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountsByIdListReq.Unmarshal(m, b)
}
func (m *GetPublicAccountsByIdListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountsByIdListReq.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountsByIdListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountsByIdListReq.Merge(dst, src)
}
func (m *GetPublicAccountsByIdListReq) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountsByIdListReq.Size(m)
}
func (m *GetPublicAccountsByIdListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountsByIdListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountsByIdListReq proto.InternalMessageInfo

func (m *GetPublicAccountsByIdListReq) GetPublicIdList() []uint32 {
	if m != nil {
		return m.PublicIdList
	}
	return nil
}

type GetPublicAccountsByIdListResp struct {
	PublicAccountList    []*StPublicAccount `protobuf:"bytes,1,rep,name=public_account_list,json=publicAccountList,proto3" json:"public_account_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetPublicAccountsByIdListResp) Reset()         { *m = GetPublicAccountsByIdListResp{} }
func (m *GetPublicAccountsByIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountsByIdListResp) ProtoMessage()    {}
func (*GetPublicAccountsByIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{8}
}
func (m *GetPublicAccountsByIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountsByIdListResp.Unmarshal(m, b)
}
func (m *GetPublicAccountsByIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountsByIdListResp.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountsByIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountsByIdListResp.Merge(dst, src)
}
func (m *GetPublicAccountsByIdListResp) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountsByIdListResp.Size(m)
}
func (m *GetPublicAccountsByIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountsByIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountsByIdListResp proto.InternalMessageInfo

func (m *GetPublicAccountsByIdListResp) GetPublicAccountList() []*StPublicAccount {
	if m != nil {
		return m.PublicAccountList
	}
	return nil
}

type GetPublicAccountsByBindIdListReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	BindIdList           []uint64 `protobuf:"varint,2,rep,packed,name=bind_id_list,json=bindIdList,proto3" json:"bind_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublicAccountsByBindIdListReq) Reset()         { *m = GetPublicAccountsByBindIdListReq{} }
func (m *GetPublicAccountsByBindIdListReq) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountsByBindIdListReq) ProtoMessage()    {}
func (*GetPublicAccountsByBindIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{9}
}
func (m *GetPublicAccountsByBindIdListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountsByBindIdListReq.Unmarshal(m, b)
}
func (m *GetPublicAccountsByBindIdListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountsByBindIdListReq.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountsByBindIdListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountsByBindIdListReq.Merge(dst, src)
}
func (m *GetPublicAccountsByBindIdListReq) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountsByBindIdListReq.Size(m)
}
func (m *GetPublicAccountsByBindIdListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountsByBindIdListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountsByBindIdListReq proto.InternalMessageInfo

func (m *GetPublicAccountsByBindIdListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetPublicAccountsByBindIdListReq) GetBindIdList() []uint64 {
	if m != nil {
		return m.BindIdList
	}
	return nil
}

type GetPublicAccountsByBindIdListResp struct {
	PublicAccountList    []*StPublicAccount `protobuf:"bytes,1,rep,name=public_account_list,json=publicAccountList,proto3" json:"public_account_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetPublicAccountsByBindIdListResp) Reset()         { *m = GetPublicAccountsByBindIdListResp{} }
func (m *GetPublicAccountsByBindIdListResp) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountsByBindIdListResp) ProtoMessage()    {}
func (*GetPublicAccountsByBindIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{10}
}
func (m *GetPublicAccountsByBindIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountsByBindIdListResp.Unmarshal(m, b)
}
func (m *GetPublicAccountsByBindIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountsByBindIdListResp.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountsByBindIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountsByBindIdListResp.Merge(dst, src)
}
func (m *GetPublicAccountsByBindIdListResp) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountsByBindIdListResp.Size(m)
}
func (m *GetPublicAccountsByBindIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountsByBindIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountsByBindIdListResp proto.InternalMessageInfo

func (m *GetPublicAccountsByBindIdListResp) GetPublicAccountList() []*StPublicAccount {
	if m != nil {
		return m.PublicAccountList
	}
	return nil
}

type SetPublicAccountAutoReplyReq struct {
	PublicId             uint32   `protobuf:"varint,1,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	AutoReply            string   `protobuf:"bytes,2,opt,name=auto_reply,json=autoReply,proto3" json:"auto_reply,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPublicAccountAutoReplyReq) Reset()         { *m = SetPublicAccountAutoReplyReq{} }
func (m *SetPublicAccountAutoReplyReq) String() string { return proto.CompactTextString(m) }
func (*SetPublicAccountAutoReplyReq) ProtoMessage()    {}
func (*SetPublicAccountAutoReplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{11}
}
func (m *SetPublicAccountAutoReplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPublicAccountAutoReplyReq.Unmarshal(m, b)
}
func (m *SetPublicAccountAutoReplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPublicAccountAutoReplyReq.Marshal(b, m, deterministic)
}
func (dst *SetPublicAccountAutoReplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPublicAccountAutoReplyReq.Merge(dst, src)
}
func (m *SetPublicAccountAutoReplyReq) XXX_Size() int {
	return xxx_messageInfo_SetPublicAccountAutoReplyReq.Size(m)
}
func (m *SetPublicAccountAutoReplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPublicAccountAutoReplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPublicAccountAutoReplyReq proto.InternalMessageInfo

func (m *SetPublicAccountAutoReplyReq) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

func (m *SetPublicAccountAutoReplyReq) GetAutoReply() string {
	if m != nil {
		return m.AutoReply
	}
	return ""
}

type SetPublicAccountAutoReplyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPublicAccountAutoReplyResp) Reset()         { *m = SetPublicAccountAutoReplyResp{} }
func (m *SetPublicAccountAutoReplyResp) String() string { return proto.CompactTextString(m) }
func (*SetPublicAccountAutoReplyResp) ProtoMessage()    {}
func (*SetPublicAccountAutoReplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{12}
}
func (m *SetPublicAccountAutoReplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPublicAccountAutoReplyResp.Unmarshal(m, b)
}
func (m *SetPublicAccountAutoReplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPublicAccountAutoReplyResp.Marshal(b, m, deterministic)
}
func (dst *SetPublicAccountAutoReplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPublicAccountAutoReplyResp.Merge(dst, src)
}
func (m *SetPublicAccountAutoReplyResp) XXX_Size() int {
	return xxx_messageInfo_SetPublicAccountAutoReplyResp.Size(m)
}
func (m *SetPublicAccountAutoReplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPublicAccountAutoReplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPublicAccountAutoReplyResp proto.InternalMessageInfo

type GetPublicAccountAutoReplyReq struct {
	PublicId             uint32   `protobuf:"varint,1,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublicAccountAutoReplyReq) Reset()         { *m = GetPublicAccountAutoReplyReq{} }
func (m *GetPublicAccountAutoReplyReq) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountAutoReplyReq) ProtoMessage()    {}
func (*GetPublicAccountAutoReplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{13}
}
func (m *GetPublicAccountAutoReplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountAutoReplyReq.Unmarshal(m, b)
}
func (m *GetPublicAccountAutoReplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountAutoReplyReq.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountAutoReplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountAutoReplyReq.Merge(dst, src)
}
func (m *GetPublicAccountAutoReplyReq) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountAutoReplyReq.Size(m)
}
func (m *GetPublicAccountAutoReplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountAutoReplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountAutoReplyReq proto.InternalMessageInfo

func (m *GetPublicAccountAutoReplyReq) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

type GetPublicAccountAutoReplyResp struct {
	AutoReply            string   `protobuf:"bytes,2,opt,name=auto_reply,json=autoReply,proto3" json:"auto_reply,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublicAccountAutoReplyResp) Reset()         { *m = GetPublicAccountAutoReplyResp{} }
func (m *GetPublicAccountAutoReplyResp) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountAutoReplyResp) ProtoMessage()    {}
func (*GetPublicAccountAutoReplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{14}
}
func (m *GetPublicAccountAutoReplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountAutoReplyResp.Unmarshal(m, b)
}
func (m *GetPublicAccountAutoReplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountAutoReplyResp.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountAutoReplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountAutoReplyResp.Merge(dst, src)
}
func (m *GetPublicAccountAutoReplyResp) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountAutoReplyResp.Size(m)
}
func (m *GetPublicAccountAutoReplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountAutoReplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountAutoReplyResp proto.InternalMessageInfo

func (m *GetPublicAccountAutoReplyResp) GetAutoReply() string {
	if m != nil {
		return m.AutoReply
	}
	return ""
}

type UpdatePublicAccountReq struct {
	PublicId             uint32   `protobuf:"varint,1,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Authority            string   `protobuf:"bytes,3,opt,name=authority,proto3" json:"authority,omitempty"`
	AuthorityUrl         string   `protobuf:"bytes,4,opt,name=authority_url,json=authorityUrl,proto3" json:"authority_url,omitempty"`
	Intro                string   `protobuf:"bytes,5,opt,name=intro,proto3" json:"intro,omitempty"`
	UpdateTime           int64    `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePublicAccountReq) Reset()         { *m = UpdatePublicAccountReq{} }
func (m *UpdatePublicAccountReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePublicAccountReq) ProtoMessage()    {}
func (*UpdatePublicAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{15}
}
func (m *UpdatePublicAccountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePublicAccountReq.Unmarshal(m, b)
}
func (m *UpdatePublicAccountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePublicAccountReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePublicAccountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePublicAccountReq.Merge(dst, src)
}
func (m *UpdatePublicAccountReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePublicAccountReq.Size(m)
}
func (m *UpdatePublicAccountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePublicAccountReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePublicAccountReq proto.InternalMessageInfo

func (m *UpdatePublicAccountReq) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

func (m *UpdatePublicAccountReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdatePublicAccountReq) GetAuthority() string {
	if m != nil {
		return m.Authority
	}
	return ""
}

func (m *UpdatePublicAccountReq) GetAuthorityUrl() string {
	if m != nil {
		return m.AuthorityUrl
	}
	return ""
}

func (m *UpdatePublicAccountReq) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *UpdatePublicAccountReq) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type UpdatePublicAccountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePublicAccountResp) Reset()         { *m = UpdatePublicAccountResp{} }
func (m *UpdatePublicAccountResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePublicAccountResp) ProtoMessage()    {}
func (*UpdatePublicAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{16}
}
func (m *UpdatePublicAccountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePublicAccountResp.Unmarshal(m, b)
}
func (m *UpdatePublicAccountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePublicAccountResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePublicAccountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePublicAccountResp.Merge(dst, src)
}
func (m *UpdatePublicAccountResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePublicAccountResp.Size(m)
}
func (m *UpdatePublicAccountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePublicAccountResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePublicAccountResp proto.InternalMessageInfo

type PublicAccountConfig struct {
	PublicId             uint32   `protobuf:"varint,1,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	ConfigBinary         []byte   `protobuf:"bytes,2,opt,name=config_binary,json=configBinary,proto3" json:"config_binary,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PublicAccountConfig) Reset()         { *m = PublicAccountConfig{} }
func (m *PublicAccountConfig) String() string { return proto.CompactTextString(m) }
func (*PublicAccountConfig) ProtoMessage()    {}
func (*PublicAccountConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{17}
}
func (m *PublicAccountConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublicAccountConfig.Unmarshal(m, b)
}
func (m *PublicAccountConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublicAccountConfig.Marshal(b, m, deterministic)
}
func (dst *PublicAccountConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublicAccountConfig.Merge(dst, src)
}
func (m *PublicAccountConfig) XXX_Size() int {
	return xxx_messageInfo_PublicAccountConfig.Size(m)
}
func (m *PublicAccountConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PublicAccountConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PublicAccountConfig proto.InternalMessageInfo

func (m *PublicAccountConfig) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

func (m *PublicAccountConfig) GetConfigBinary() []byte {
	if m != nil {
		return m.ConfigBinary
	}
	return nil
}

type UpdatePublicAccountConfigReq struct {
	PublicConfig         *PublicAccountConfig `protobuf:"bytes,1,opt,name=public_config,json=publicConfig,proto3" json:"public_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdatePublicAccountConfigReq) Reset()         { *m = UpdatePublicAccountConfigReq{} }
func (m *UpdatePublicAccountConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePublicAccountConfigReq) ProtoMessage()    {}
func (*UpdatePublicAccountConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{18}
}
func (m *UpdatePublicAccountConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePublicAccountConfigReq.Unmarshal(m, b)
}
func (m *UpdatePublicAccountConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePublicAccountConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePublicAccountConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePublicAccountConfigReq.Merge(dst, src)
}
func (m *UpdatePublicAccountConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePublicAccountConfigReq.Size(m)
}
func (m *UpdatePublicAccountConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePublicAccountConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePublicAccountConfigReq proto.InternalMessageInfo

func (m *UpdatePublicAccountConfigReq) GetPublicConfig() *PublicAccountConfig {
	if m != nil {
		return m.PublicConfig
	}
	return nil
}

type UpdatePublicAccountConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePublicAccountConfigResp) Reset()         { *m = UpdatePublicAccountConfigResp{} }
func (m *UpdatePublicAccountConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePublicAccountConfigResp) ProtoMessage()    {}
func (*UpdatePublicAccountConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{19}
}
func (m *UpdatePublicAccountConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePublicAccountConfigResp.Unmarshal(m, b)
}
func (m *UpdatePublicAccountConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePublicAccountConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePublicAccountConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePublicAccountConfigResp.Merge(dst, src)
}
func (m *UpdatePublicAccountConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePublicAccountConfigResp.Size(m)
}
func (m *UpdatePublicAccountConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePublicAccountConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePublicAccountConfigResp proto.InternalMessageInfo

type BatchGetPublicAccountConfigReq struct {
	PublicIdList         []uint32 `protobuf:"varint,1,rep,packed,name=public_id_list,json=publicIdList,proto3" json:"public_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetPublicAccountConfigReq) Reset()         { *m = BatchGetPublicAccountConfigReq{} }
func (m *BatchGetPublicAccountConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPublicAccountConfigReq) ProtoMessage()    {}
func (*BatchGetPublicAccountConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{20}
}
func (m *BatchGetPublicAccountConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPublicAccountConfigReq.Unmarshal(m, b)
}
func (m *BatchGetPublicAccountConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPublicAccountConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetPublicAccountConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPublicAccountConfigReq.Merge(dst, src)
}
func (m *BatchGetPublicAccountConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetPublicAccountConfigReq.Size(m)
}
func (m *BatchGetPublicAccountConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPublicAccountConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPublicAccountConfigReq proto.InternalMessageInfo

func (m *BatchGetPublicAccountConfigReq) GetPublicIdList() []uint32 {
	if m != nil {
		return m.PublicIdList
	}
	return nil
}

type BatchGetPublicAccountConfigResp struct {
	PublicConfigList     []*PublicAccountConfig `protobuf:"bytes,1,rep,name=public_config_list,json=publicConfigList,proto3" json:"public_config_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchGetPublicAccountConfigResp) Reset()         { *m = BatchGetPublicAccountConfigResp{} }
func (m *BatchGetPublicAccountConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPublicAccountConfigResp) ProtoMessage()    {}
func (*BatchGetPublicAccountConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{21}
}
func (m *BatchGetPublicAccountConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPublicAccountConfigResp.Unmarshal(m, b)
}
func (m *BatchGetPublicAccountConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPublicAccountConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetPublicAccountConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPublicAccountConfigResp.Merge(dst, src)
}
func (m *BatchGetPublicAccountConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetPublicAccountConfigResp.Size(m)
}
func (m *BatchGetPublicAccountConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPublicAccountConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPublicAccountConfigResp proto.InternalMessageInfo

func (m *BatchGetPublicAccountConfigResp) GetPublicConfigList() []*PublicAccountConfig {
	if m != nil {
		return m.PublicConfigList
	}
	return nil
}

// 默认消息配置
type PublicAccountDefaultMessage struct {
	MessageId            uint32   `protobuf:"varint,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	PublicId             uint32   `protobuf:"varint,2,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	MessageBinary        []byte   `protobuf:"bytes,3,opt,name=message_binary,json=messageBinary,proto3" json:"message_binary,omitempty"`
	IsTest               bool     `protobuf:"varint,4,opt,name=is_test,json=isTest,proto3" json:"is_test,omitempty"`
	TestUidList          []uint64 `protobuf:"varint,5,rep,packed,name=test_uid_list,json=testUidList,proto3" json:"test_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PublicAccountDefaultMessage) Reset()         { *m = PublicAccountDefaultMessage{} }
func (m *PublicAccountDefaultMessage) String() string { return proto.CompactTextString(m) }
func (*PublicAccountDefaultMessage) ProtoMessage()    {}
func (*PublicAccountDefaultMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{22}
}
func (m *PublicAccountDefaultMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublicAccountDefaultMessage.Unmarshal(m, b)
}
func (m *PublicAccountDefaultMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublicAccountDefaultMessage.Marshal(b, m, deterministic)
}
func (dst *PublicAccountDefaultMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublicAccountDefaultMessage.Merge(dst, src)
}
func (m *PublicAccountDefaultMessage) XXX_Size() int {
	return xxx_messageInfo_PublicAccountDefaultMessage.Size(m)
}
func (m *PublicAccountDefaultMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_PublicAccountDefaultMessage.DiscardUnknown(m)
}

var xxx_messageInfo_PublicAccountDefaultMessage proto.InternalMessageInfo

func (m *PublicAccountDefaultMessage) GetMessageId() uint32 {
	if m != nil {
		return m.MessageId
	}
	return 0
}

func (m *PublicAccountDefaultMessage) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

func (m *PublicAccountDefaultMessage) GetMessageBinary() []byte {
	if m != nil {
		return m.MessageBinary
	}
	return nil
}

func (m *PublicAccountDefaultMessage) GetIsTest() bool {
	if m != nil {
		return m.IsTest
	}
	return false
}

func (m *PublicAccountDefaultMessage) GetTestUidList() []uint64 {
	if m != nil {
		return m.TestUidList
	}
	return nil
}

type AddOrUpdatePublicAccountDefaultMessageReq struct {
	PublicId             uint32                       `protobuf:"varint,1,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	Message              *PublicAccountDefaultMessage `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *AddOrUpdatePublicAccountDefaultMessageReq) Reset() {
	*m = AddOrUpdatePublicAccountDefaultMessageReq{}
}
func (m *AddOrUpdatePublicAccountDefaultMessageReq) String() string { return proto.CompactTextString(m) }
func (*AddOrUpdatePublicAccountDefaultMessageReq) ProtoMessage()    {}
func (*AddOrUpdatePublicAccountDefaultMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{23}
}
func (m *AddOrUpdatePublicAccountDefaultMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageReq.Unmarshal(m, b)
}
func (m *AddOrUpdatePublicAccountDefaultMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageReq.Marshal(b, m, deterministic)
}
func (dst *AddOrUpdatePublicAccountDefaultMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageReq.Merge(dst, src)
}
func (m *AddOrUpdatePublicAccountDefaultMessageReq) XXX_Size() int {
	return xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageReq.Size(m)
}
func (m *AddOrUpdatePublicAccountDefaultMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageReq proto.InternalMessageInfo

func (m *AddOrUpdatePublicAccountDefaultMessageReq) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

func (m *AddOrUpdatePublicAccountDefaultMessageReq) GetMessage() *PublicAccountDefaultMessage {
	if m != nil {
		return m.Message
	}
	return nil
}

type AddOrUpdatePublicAccountDefaultMessageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddOrUpdatePublicAccountDefaultMessageResp) Reset() {
	*m = AddOrUpdatePublicAccountDefaultMessageResp{}
}
func (m *AddOrUpdatePublicAccountDefaultMessageResp) String() string {
	return proto.CompactTextString(m)
}
func (*AddOrUpdatePublicAccountDefaultMessageResp) ProtoMessage() {}
func (*AddOrUpdatePublicAccountDefaultMessageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{24}
}
func (m *AddOrUpdatePublicAccountDefaultMessageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageResp.Unmarshal(m, b)
}
func (m *AddOrUpdatePublicAccountDefaultMessageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageResp.Marshal(b, m, deterministic)
}
func (dst *AddOrUpdatePublicAccountDefaultMessageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageResp.Merge(dst, src)
}
func (m *AddOrUpdatePublicAccountDefaultMessageResp) XXX_Size() int {
	return xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageResp.Size(m)
}
func (m *AddOrUpdatePublicAccountDefaultMessageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddOrUpdatePublicAccountDefaultMessageResp proto.InternalMessageInfo

type DeletePublicAccountDefaultMessageReq struct {
	PublicId             uint32   `protobuf:"varint,1,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	MessageId            uint32   `protobuf:"varint,2,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePublicAccountDefaultMessageReq) Reset()         { *m = DeletePublicAccountDefaultMessageReq{} }
func (m *DeletePublicAccountDefaultMessageReq) String() string { return proto.CompactTextString(m) }
func (*DeletePublicAccountDefaultMessageReq) ProtoMessage()    {}
func (*DeletePublicAccountDefaultMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{25}
}
func (m *DeletePublicAccountDefaultMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePublicAccountDefaultMessageReq.Unmarshal(m, b)
}
func (m *DeletePublicAccountDefaultMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePublicAccountDefaultMessageReq.Marshal(b, m, deterministic)
}
func (dst *DeletePublicAccountDefaultMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePublicAccountDefaultMessageReq.Merge(dst, src)
}
func (m *DeletePublicAccountDefaultMessageReq) XXX_Size() int {
	return xxx_messageInfo_DeletePublicAccountDefaultMessageReq.Size(m)
}
func (m *DeletePublicAccountDefaultMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePublicAccountDefaultMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePublicAccountDefaultMessageReq proto.InternalMessageInfo

func (m *DeletePublicAccountDefaultMessageReq) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

func (m *DeletePublicAccountDefaultMessageReq) GetMessageId() uint32 {
	if m != nil {
		return m.MessageId
	}
	return 0
}

type DeletePublicAccountDefaultMessageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeletePublicAccountDefaultMessageResp) Reset()         { *m = DeletePublicAccountDefaultMessageResp{} }
func (m *DeletePublicAccountDefaultMessageResp) String() string { return proto.CompactTextString(m) }
func (*DeletePublicAccountDefaultMessageResp) ProtoMessage()    {}
func (*DeletePublicAccountDefaultMessageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{26}
}
func (m *DeletePublicAccountDefaultMessageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeletePublicAccountDefaultMessageResp.Unmarshal(m, b)
}
func (m *DeletePublicAccountDefaultMessageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeletePublicAccountDefaultMessageResp.Marshal(b, m, deterministic)
}
func (dst *DeletePublicAccountDefaultMessageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeletePublicAccountDefaultMessageResp.Merge(dst, src)
}
func (m *DeletePublicAccountDefaultMessageResp) XXX_Size() int {
	return xxx_messageInfo_DeletePublicAccountDefaultMessageResp.Size(m)
}
func (m *DeletePublicAccountDefaultMessageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeletePublicAccountDefaultMessageResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeletePublicAccountDefaultMessageResp proto.InternalMessageInfo

type GetPublicAccountDefaultMessagesReq struct {
	PublicId             uint32   `protobuf:"varint,1,opt,name=public_id,json=publicId,proto3" json:"public_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPublicAccountDefaultMessagesReq) Reset()         { *m = GetPublicAccountDefaultMessagesReq{} }
func (m *GetPublicAccountDefaultMessagesReq) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountDefaultMessagesReq) ProtoMessage()    {}
func (*GetPublicAccountDefaultMessagesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{27}
}
func (m *GetPublicAccountDefaultMessagesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountDefaultMessagesReq.Unmarshal(m, b)
}
func (m *GetPublicAccountDefaultMessagesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountDefaultMessagesReq.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountDefaultMessagesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountDefaultMessagesReq.Merge(dst, src)
}
func (m *GetPublicAccountDefaultMessagesReq) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountDefaultMessagesReq.Size(m)
}
func (m *GetPublicAccountDefaultMessagesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountDefaultMessagesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountDefaultMessagesReq proto.InternalMessageInfo

func (m *GetPublicAccountDefaultMessagesReq) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

type GetPublicAccountDefaultMessagesResp struct {
	MessageList          []*PublicAccountDefaultMessage `protobuf:"bytes,1,rep,name=message_list,json=messageList,proto3" json:"message_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *GetPublicAccountDefaultMessagesResp) Reset()         { *m = GetPublicAccountDefaultMessagesResp{} }
func (m *GetPublicAccountDefaultMessagesResp) String() string { return proto.CompactTextString(m) }
func (*GetPublicAccountDefaultMessagesResp) ProtoMessage()    {}
func (*GetPublicAccountDefaultMessagesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_public_go_4425fcc638a6adcf, []int{28}
}
func (m *GetPublicAccountDefaultMessagesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicAccountDefaultMessagesResp.Unmarshal(m, b)
}
func (m *GetPublicAccountDefaultMessagesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicAccountDefaultMessagesResp.Marshal(b, m, deterministic)
}
func (dst *GetPublicAccountDefaultMessagesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicAccountDefaultMessagesResp.Merge(dst, src)
}
func (m *GetPublicAccountDefaultMessagesResp) XXX_Size() int {
	return xxx_messageInfo_GetPublicAccountDefaultMessagesResp.Size(m)
}
func (m *GetPublicAccountDefaultMessagesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicAccountDefaultMessagesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicAccountDefaultMessagesResp proto.InternalMessageInfo

func (m *GetPublicAccountDefaultMessagesResp) GetMessageList() []*PublicAccountDefaultMessage {
	if m != nil {
		return m.MessageList
	}
	return nil
}

func init() {
	proto.RegisterType((*StPublicAccount)(nil), "public_go.StPublicAccount")
	proto.RegisterType((*CreatePublicAccountReq)(nil), "public_go.CreatePublicAccountReq")
	proto.RegisterType((*CreatePublicAccountResp)(nil), "public_go.CreatePublicAccountResp")
	proto.RegisterType((*GetPublicAccountReq)(nil), "public_go.GetPublicAccountReq")
	proto.RegisterType((*GetPublicAccountResp)(nil), "public_go.GetPublicAccountResp")
	proto.RegisterType((*GetPublicAccountByBindIdReq)(nil), "public_go.GetPublicAccountByBindIdReq")
	proto.RegisterType((*GetPublicAccountByBindIdResp)(nil), "public_go.GetPublicAccountByBindIdResp")
	proto.RegisterType((*GetPublicAccountsByIdListReq)(nil), "public_go.GetPublicAccountsByIdListReq")
	proto.RegisterType((*GetPublicAccountsByIdListResp)(nil), "public_go.GetPublicAccountsByIdListResp")
	proto.RegisterType((*GetPublicAccountsByBindIdListReq)(nil), "public_go.GetPublicAccountsByBindIdListReq")
	proto.RegisterType((*GetPublicAccountsByBindIdListResp)(nil), "public_go.GetPublicAccountsByBindIdListResp")
	proto.RegisterType((*SetPublicAccountAutoReplyReq)(nil), "public_go.SetPublicAccountAutoReplyReq")
	proto.RegisterType((*SetPublicAccountAutoReplyResp)(nil), "public_go.SetPublicAccountAutoReplyResp")
	proto.RegisterType((*GetPublicAccountAutoReplyReq)(nil), "public_go.GetPublicAccountAutoReplyReq")
	proto.RegisterType((*GetPublicAccountAutoReplyResp)(nil), "public_go.GetPublicAccountAutoReplyResp")
	proto.RegisterType((*UpdatePublicAccountReq)(nil), "public_go.UpdatePublicAccountReq")
	proto.RegisterType((*UpdatePublicAccountResp)(nil), "public_go.UpdatePublicAccountResp")
	proto.RegisterType((*PublicAccountConfig)(nil), "public_go.PublicAccountConfig")
	proto.RegisterType((*UpdatePublicAccountConfigReq)(nil), "public_go.UpdatePublicAccountConfigReq")
	proto.RegisterType((*UpdatePublicAccountConfigResp)(nil), "public_go.UpdatePublicAccountConfigResp")
	proto.RegisterType((*BatchGetPublicAccountConfigReq)(nil), "public_go.BatchGetPublicAccountConfigReq")
	proto.RegisterType((*BatchGetPublicAccountConfigResp)(nil), "public_go.BatchGetPublicAccountConfigResp")
	proto.RegisterType((*PublicAccountDefaultMessage)(nil), "public_go.PublicAccountDefaultMessage")
	proto.RegisterType((*AddOrUpdatePublicAccountDefaultMessageReq)(nil), "public_go.AddOrUpdatePublicAccountDefaultMessageReq")
	proto.RegisterType((*AddOrUpdatePublicAccountDefaultMessageResp)(nil), "public_go.AddOrUpdatePublicAccountDefaultMessageResp")
	proto.RegisterType((*DeletePublicAccountDefaultMessageReq)(nil), "public_go.DeletePublicAccountDefaultMessageReq")
	proto.RegisterType((*DeletePublicAccountDefaultMessageResp)(nil), "public_go.DeletePublicAccountDefaultMessageResp")
	proto.RegisterType((*GetPublicAccountDefaultMessagesReq)(nil), "public_go.GetPublicAccountDefaultMessagesReq")
	proto.RegisterType((*GetPublicAccountDefaultMessagesResp)(nil), "public_go.GetPublicAccountDefaultMessagesResp")
	proto.RegisterEnum("public_go.PublicAccountType", PublicAccountType_name, PublicAccountType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PublicGoClient is the client API for PublicGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PublicGoClient interface {
	CreatePublicAccount(ctx context.Context, in *CreatePublicAccountReq, opts ...grpc.CallOption) (*CreatePublicAccountResp, error)
	GetPublicAccount(ctx context.Context, in *GetPublicAccountReq, opts ...grpc.CallOption) (*GetPublicAccountResp, error)
	UpdatePublicAccount(ctx context.Context, in *UpdatePublicAccountReq, opts ...grpc.CallOption) (*UpdatePublicAccountResp, error)
	GetPublicAccountByBindId(ctx context.Context, in *GetPublicAccountByBindIdReq, opts ...grpc.CallOption) (*GetPublicAccountByBindIdResp, error)
	GetPublicAccountsByIdList(ctx context.Context, in *GetPublicAccountsByIdListReq, opts ...grpc.CallOption) (*GetPublicAccountsByIdListResp, error)
	GetPublicAccountsByBindIdList(ctx context.Context, in *GetPublicAccountsByBindIdListReq, opts ...grpc.CallOption) (*GetPublicAccountsByBindIdListResp, error)
	SetPublicAccountAutoReply(ctx context.Context, in *SetPublicAccountAutoReplyReq, opts ...grpc.CallOption) (*SetPublicAccountAutoReplyResp, error)
	GetPublicAccountAutoReply(ctx context.Context, in *GetPublicAccountAutoReplyReq, opts ...grpc.CallOption) (*GetPublicAccountAutoReplyResp, error)
	UpdatePublicAccountConfig(ctx context.Context, in *UpdatePublicAccountConfigReq, opts ...grpc.CallOption) (*UpdatePublicAccountConfigResp, error)
	BatchGetPublicAccountConfig(ctx context.Context, in *BatchGetPublicAccountConfigReq, opts ...grpc.CallOption) (*BatchGetPublicAccountConfigResp, error)
	AddOrUpdatePublicAccountDefaultMessage(ctx context.Context, in *AddOrUpdatePublicAccountDefaultMessageReq, opts ...grpc.CallOption) (*AddOrUpdatePublicAccountDefaultMessageResp, error)
	DeletePublicAccountDefaultMessage(ctx context.Context, in *DeletePublicAccountDefaultMessageReq, opts ...grpc.CallOption) (*DeletePublicAccountDefaultMessageResp, error)
	GetPublicAccountDefaultMessages(ctx context.Context, in *GetPublicAccountDefaultMessagesReq, opts ...grpc.CallOption) (*GetPublicAccountDefaultMessagesResp, error)
}

type publicGoClient struct {
	cc *grpc.ClientConn
}

func NewPublicGoClient(cc *grpc.ClientConn) PublicGoClient {
	return &publicGoClient{cc}
}

func (c *publicGoClient) CreatePublicAccount(ctx context.Context, in *CreatePublicAccountReq, opts ...grpc.CallOption) (*CreatePublicAccountResp, error) {
	out := new(CreatePublicAccountResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/CreatePublicAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) GetPublicAccount(ctx context.Context, in *GetPublicAccountReq, opts ...grpc.CallOption) (*GetPublicAccountResp, error) {
	out := new(GetPublicAccountResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/GetPublicAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) UpdatePublicAccount(ctx context.Context, in *UpdatePublicAccountReq, opts ...grpc.CallOption) (*UpdatePublicAccountResp, error) {
	out := new(UpdatePublicAccountResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/UpdatePublicAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) GetPublicAccountByBindId(ctx context.Context, in *GetPublicAccountByBindIdReq, opts ...grpc.CallOption) (*GetPublicAccountByBindIdResp, error) {
	out := new(GetPublicAccountByBindIdResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/GetPublicAccountByBindId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) GetPublicAccountsByIdList(ctx context.Context, in *GetPublicAccountsByIdListReq, opts ...grpc.CallOption) (*GetPublicAccountsByIdListResp, error) {
	out := new(GetPublicAccountsByIdListResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/GetPublicAccountsByIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) GetPublicAccountsByBindIdList(ctx context.Context, in *GetPublicAccountsByBindIdListReq, opts ...grpc.CallOption) (*GetPublicAccountsByBindIdListResp, error) {
	out := new(GetPublicAccountsByBindIdListResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/GetPublicAccountsByBindIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) SetPublicAccountAutoReply(ctx context.Context, in *SetPublicAccountAutoReplyReq, opts ...grpc.CallOption) (*SetPublicAccountAutoReplyResp, error) {
	out := new(SetPublicAccountAutoReplyResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/SetPublicAccountAutoReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) GetPublicAccountAutoReply(ctx context.Context, in *GetPublicAccountAutoReplyReq, opts ...grpc.CallOption) (*GetPublicAccountAutoReplyResp, error) {
	out := new(GetPublicAccountAutoReplyResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/GetPublicAccountAutoReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) UpdatePublicAccountConfig(ctx context.Context, in *UpdatePublicAccountConfigReq, opts ...grpc.CallOption) (*UpdatePublicAccountConfigResp, error) {
	out := new(UpdatePublicAccountConfigResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/UpdatePublicAccountConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) BatchGetPublicAccountConfig(ctx context.Context, in *BatchGetPublicAccountConfigReq, opts ...grpc.CallOption) (*BatchGetPublicAccountConfigResp, error) {
	out := new(BatchGetPublicAccountConfigResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/BatchGetPublicAccountConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) AddOrUpdatePublicAccountDefaultMessage(ctx context.Context, in *AddOrUpdatePublicAccountDefaultMessageReq, opts ...grpc.CallOption) (*AddOrUpdatePublicAccountDefaultMessageResp, error) {
	out := new(AddOrUpdatePublicAccountDefaultMessageResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/AddOrUpdatePublicAccountDefaultMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) DeletePublicAccountDefaultMessage(ctx context.Context, in *DeletePublicAccountDefaultMessageReq, opts ...grpc.CallOption) (*DeletePublicAccountDefaultMessageResp, error) {
	out := new(DeletePublicAccountDefaultMessageResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/DeletePublicAccountDefaultMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicGoClient) GetPublicAccountDefaultMessages(ctx context.Context, in *GetPublicAccountDefaultMessagesReq, opts ...grpc.CallOption) (*GetPublicAccountDefaultMessagesResp, error) {
	out := new(GetPublicAccountDefaultMessagesResp)
	err := c.cc.Invoke(ctx, "/public_go.PublicGo/GetPublicAccountDefaultMessages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PublicGoServer is the server API for PublicGo service.
type PublicGoServer interface {
	CreatePublicAccount(context.Context, *CreatePublicAccountReq) (*CreatePublicAccountResp, error)
	GetPublicAccount(context.Context, *GetPublicAccountReq) (*GetPublicAccountResp, error)
	UpdatePublicAccount(context.Context, *UpdatePublicAccountReq) (*UpdatePublicAccountResp, error)
	GetPublicAccountByBindId(context.Context, *GetPublicAccountByBindIdReq) (*GetPublicAccountByBindIdResp, error)
	GetPublicAccountsByIdList(context.Context, *GetPublicAccountsByIdListReq) (*GetPublicAccountsByIdListResp, error)
	GetPublicAccountsByBindIdList(context.Context, *GetPublicAccountsByBindIdListReq) (*GetPublicAccountsByBindIdListResp, error)
	SetPublicAccountAutoReply(context.Context, *SetPublicAccountAutoReplyReq) (*SetPublicAccountAutoReplyResp, error)
	GetPublicAccountAutoReply(context.Context, *GetPublicAccountAutoReplyReq) (*GetPublicAccountAutoReplyResp, error)
	UpdatePublicAccountConfig(context.Context, *UpdatePublicAccountConfigReq) (*UpdatePublicAccountConfigResp, error)
	BatchGetPublicAccountConfig(context.Context, *BatchGetPublicAccountConfigReq) (*BatchGetPublicAccountConfigResp, error)
	AddOrUpdatePublicAccountDefaultMessage(context.Context, *AddOrUpdatePublicAccountDefaultMessageReq) (*AddOrUpdatePublicAccountDefaultMessageResp, error)
	DeletePublicAccountDefaultMessage(context.Context, *DeletePublicAccountDefaultMessageReq) (*DeletePublicAccountDefaultMessageResp, error)
	GetPublicAccountDefaultMessages(context.Context, *GetPublicAccountDefaultMessagesReq) (*GetPublicAccountDefaultMessagesResp, error)
}

func RegisterPublicGoServer(s *grpc.Server, srv PublicGoServer) {
	s.RegisterService(&_PublicGo_serviceDesc, srv)
}

func _PublicGo_CreatePublicAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePublicAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).CreatePublicAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/CreatePublicAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).CreatePublicAccount(ctx, req.(*CreatePublicAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_GetPublicAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublicAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).GetPublicAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/GetPublicAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).GetPublicAccount(ctx, req.(*GetPublicAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_UpdatePublicAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePublicAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).UpdatePublicAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/UpdatePublicAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).UpdatePublicAccount(ctx, req.(*UpdatePublicAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_GetPublicAccountByBindId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublicAccountByBindIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).GetPublicAccountByBindId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/GetPublicAccountByBindId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).GetPublicAccountByBindId(ctx, req.(*GetPublicAccountByBindIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_GetPublicAccountsByIdList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublicAccountsByIdListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).GetPublicAccountsByIdList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/GetPublicAccountsByIdList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).GetPublicAccountsByIdList(ctx, req.(*GetPublicAccountsByIdListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_GetPublicAccountsByBindIdList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublicAccountsByBindIdListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).GetPublicAccountsByBindIdList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/GetPublicAccountsByBindIdList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).GetPublicAccountsByBindIdList(ctx, req.(*GetPublicAccountsByBindIdListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_SetPublicAccountAutoReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPublicAccountAutoReplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).SetPublicAccountAutoReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/SetPublicAccountAutoReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).SetPublicAccountAutoReply(ctx, req.(*SetPublicAccountAutoReplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_GetPublicAccountAutoReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublicAccountAutoReplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).GetPublicAccountAutoReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/GetPublicAccountAutoReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).GetPublicAccountAutoReply(ctx, req.(*GetPublicAccountAutoReplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_UpdatePublicAccountConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePublicAccountConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).UpdatePublicAccountConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/UpdatePublicAccountConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).UpdatePublicAccountConfig(ctx, req.(*UpdatePublicAccountConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_BatchGetPublicAccountConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPublicAccountConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).BatchGetPublicAccountConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/BatchGetPublicAccountConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).BatchGetPublicAccountConfig(ctx, req.(*BatchGetPublicAccountConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_AddOrUpdatePublicAccountDefaultMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddOrUpdatePublicAccountDefaultMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).AddOrUpdatePublicAccountDefaultMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/AddOrUpdatePublicAccountDefaultMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).AddOrUpdatePublicAccountDefaultMessage(ctx, req.(*AddOrUpdatePublicAccountDefaultMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_DeletePublicAccountDefaultMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePublicAccountDefaultMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).DeletePublicAccountDefaultMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/DeletePublicAccountDefaultMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).DeletePublicAccountDefaultMessage(ctx, req.(*DeletePublicAccountDefaultMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicGo_GetPublicAccountDefaultMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublicAccountDefaultMessagesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicGoServer).GetPublicAccountDefaultMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/public_go.PublicGo/GetPublicAccountDefaultMessages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicGoServer).GetPublicAccountDefaultMessages(ctx, req.(*GetPublicAccountDefaultMessagesReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PublicGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "public_go.PublicGo",
	HandlerType: (*PublicGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePublicAccount",
			Handler:    _PublicGo_CreatePublicAccount_Handler,
		},
		{
			MethodName: "GetPublicAccount",
			Handler:    _PublicGo_GetPublicAccount_Handler,
		},
		{
			MethodName: "UpdatePublicAccount",
			Handler:    _PublicGo_UpdatePublicAccount_Handler,
		},
		{
			MethodName: "GetPublicAccountByBindId",
			Handler:    _PublicGo_GetPublicAccountByBindId_Handler,
		},
		{
			MethodName: "GetPublicAccountsByIdList",
			Handler:    _PublicGo_GetPublicAccountsByIdList_Handler,
		},
		{
			MethodName: "GetPublicAccountsByBindIdList",
			Handler:    _PublicGo_GetPublicAccountsByBindIdList_Handler,
		},
		{
			MethodName: "SetPublicAccountAutoReply",
			Handler:    _PublicGo_SetPublicAccountAutoReply_Handler,
		},
		{
			MethodName: "GetPublicAccountAutoReply",
			Handler:    _PublicGo_GetPublicAccountAutoReply_Handler,
		},
		{
			MethodName: "UpdatePublicAccountConfig",
			Handler:    _PublicGo_UpdatePublicAccountConfig_Handler,
		},
		{
			MethodName: "BatchGetPublicAccountConfig",
			Handler:    _PublicGo_BatchGetPublicAccountConfig_Handler,
		},
		{
			MethodName: "AddOrUpdatePublicAccountDefaultMessage",
			Handler:    _PublicGo_AddOrUpdatePublicAccountDefaultMessage_Handler,
		},
		{
			MethodName: "DeletePublicAccountDefaultMessage",
			Handler:    _PublicGo_DeletePublicAccountDefaultMessage_Handler,
		},
		{
			MethodName: "GetPublicAccountDefaultMessages",
			Handler:    _PublicGo_GetPublicAccountDefaultMessages_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "public-go/public-go.proto",
}

func init() {
	proto.RegisterFile("public-go/public-go.proto", fileDescriptor_public_go_4425fcc638a6adcf)
}

var fileDescriptor_public_go_4425fcc638a6adcf = []byte{
	// 1175 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0x4f, 0x73, 0xda, 0x46,
	0x14, 0xb7, 0x00, 0x83, 0xfd, 0x00, 0x87, 0x2c, 0x19, 0x1b, 0x63, 0x3b, 0xc6, 0xeb, 0xc4, 0x21,
	0x4e, 0x8c, 0x3b, 0xb4, 0x39, 0x75, 0xa6, 0x53, 0x83, 0x5d, 0x46, 0x19, 0xc7, 0xf6, 0xc8, 0x78,
	0x5a, 0xfb, 0x10, 0x8d, 0x40, 0x32, 0xd1, 0x14, 0x90, 0xca, 0x2e, 0xe9, 0x30, 0xd3, 0x53, 0x7b,
	0xe9, 0xf4, 0x0b, 0xf4, 0xd3, 0xf4, 0xd4, 0x6b, 0x3f, 0x50, 0x8f, 0x1d, 0xad, 0x04, 0x68, 0x41,
	0xac, 0xe4, 0x34, 0x37, 0xed, 0xe3, 0xed, 0xef, 0xf7, 0xfe, 0xed, 0x7b, 0x6f, 0x80, 0x4d, 0x7b,
	0xd8, 0xea, 0x9a, 0xed, 0xa3, 0x8e, 0x75, 0x3c, 0xf9, 0xaa, 0xd8, 0x03, 0x8b, 0x5a, 0x68, 0xd5,
	0x15, 0xa8, 0x1d, 0x0b, 0xff, 0x16, 0x83, 0x47, 0xd7, 0xf4, 0x8a, 0x9d, 0x4f, 0xda, 0x6d, 0x6b,
	0xd8, 0xa7, 0x68, 0x0b, 0xc6, 0x0a, 0xa6, 0x5e, 0x90, 0x4a, 0x52, 0x39, 0xab, 0xac, 0xb8, 0x02,
	0x59, 0x47, 0x05, 0x48, 0x69, 0xae, 0x5e, 0x21, 0x56, 0x92, 0xca, 0xab, 0xca, 0xf8, 0x88, 0x10,
	0x24, 0xfa, 0x5a, 0xcf, 0x28, 0xc4, 0x99, 0x98, 0x7d, 0x3b, 0x32, 0x3a, 0xb2, 0x8d, 0x42, 0x82,
	0xa1, 0xb0, 0x6f, 0xb4, 0x01, 0xa9, 0x96, 0xd9, 0xd7, 0x1d, 0xf0, 0xe5, 0x92, 0x54, 0x4e, 0x28,
	0x49, 0xe7, 0x28, 0xeb, 0x68, 0x1b, 0x56, 0xb5, 0x21, 0xfd, 0x60, 0x0d, 0x4c, 0x3a, 0x2a, 0x24,
	0x19, 0xca, 0x54, 0x80, 0xf6, 0x21, 0x3b, 0x39, 0xa8, 0xc3, 0x41, 0xb7, 0x90, 0x62, 0x1a, 0x99,
	0x89, 0xf0, 0x66, 0xd0, 0x45, 0x4f, 0x60, 0xd9, 0xec, 0xd3, 0x81, 0x55, 0x58, 0x61, 0x3f, 0xba,
	0x07, 0xb4, 0x0b, 0xe9, 0xa1, 0xad, 0x6b, 0xd4, 0x50, 0xa9, 0xd9, 0x33, 0x0a, 0xab, 0x25, 0xa9,
	0x1c, 0x57, 0xc0, 0x15, 0x35, 0xcd, 0x9e, 0x81, 0xff, 0x91, 0x60, 0xbd, 0x3e, 0x30, 0x34, 0x6a,
	0x70, 0x91, 0x50, 0x8c, 0x9f, 0x26, 0x5e, 0x49, 0x01, 0x5e, 0xc5, 0x82, 0xbd, 0x8a, 0x2f, 0xf6,
	0x2a, 0x11, 0xea, 0xd5, 0xb2, 0xc8, 0xab, 0xa4, 0xdf, 0x2b, 0x5f, 0x26, 0x52, 0x5c, 0x26, 0xf0,
	0x15, 0x6c, 0x04, 0x7a, 0x43, 0xec, 0x4f, 0xcc, 0x2d, 0xae, 0x42, 0xbe, 0x61, 0xd0, 0xb9, 0xe0,
	0x88, 0xd0, 0xf0, 0x2d, 0x3c, 0x99, 0xbf, 0x43, 0x6c, 0x74, 0x02, 0x6b, 0xde, 0xa5, 0x31, 0x99,
	0x73, 0x33, 0x5d, 0x2d, 0x56, 0x26, 0x65, 0x59, 0x99, 0x29, 0x49, 0x25, 0x6b, 0xfb, 0x8f, 0xf8,
	0x2d, 0x6c, 0xcd, 0x42, 0xd7, 0x46, 0x35, 0x16, 0x6f, 0x2f, 0x67, 0x2c, 0x3f, 0x52, 0x70, 0x7e,
	0x62, 0xfe, 0xfc, 0x60, 0x0d, 0xb6, 0x17, 0x63, 0x7d, 0x1e, 0x73, 0x4f, 0xe7, 0x29, 0x48, 0x6d,
	0x24, 0xeb, 0xe7, 0x26, 0x61, 0x61, 0x7c, 0x36, 0xa1, 0x30, 0x75, 0xb5, 0x6b, 0x12, 0x87, 0x22,
	0x5e, 0xce, 0x2a, 0x99, 0x71, 0x2c, 0x1d, 0x45, 0xfc, 0x23, 0xec, 0x08, 0x50, 0x88, 0x8d, 0xde,
	0x42, 0x9e, 0xb7, 0x74, 0x8a, 0x25, 0x36, 0xf7, 0x31, 0x67, 0x2e, 0x23, 0xfb, 0x01, 0x4a, 0x01,
	0x64, 0x6e, 0x58, 0xc6, 0x66, 0x07, 0x85, 0xb9, 0x04, 0x19, 0x2f, 0xcc, 0x2e, 0x79, 0xac, 0x14,
	0x2f, 0x27, 0x14, 0x68, 0x4d, 0x2e, 0x62, 0x0b, 0xf6, 0x42, 0x90, 0x3f, 0xb3, 0x2b, 0x77, 0xb0,
	0x7d, 0x3d, 0x43, 0x78, 0x32, 0xa4, 0x96, 0x62, 0xd8, 0xdd, 0x51, 0x58, 0x11, 0xa3, 0x1d, 0x00,
	0x6d, 0x48, 0x2d, 0x75, 0xe0, 0x68, 0x7b, 0xaf, 0xc2, 0x79, 0xbe, 0xee, 0x75, 0xbc, 0x0b, 0x3b,
	0x02, 0x6c, 0x62, 0xe3, 0xaf, 0xe7, 0x53, 0x1f, 0x99, 0x1c, 0x7f, 0x33, 0x9f, 0x71, 0x0e, 0x3d,
	0xcc, 0xba, 0xbf, 0x25, 0x58, 0xbf, 0x61, 0x5d, 0xee, 0x41, 0x2f, 0x77, 0xd2, 0xf3, 0x62, 0xbe,
	0x9e, 0xc7, 0xb5, 0xb1, 0x78, 0x68, 0x1b, 0x4b, 0x88, 0xda, 0xd8, 0xb2, 0xa0, 0x39, 0x27, 0xe7,
	0x9a, 0xf3, 0x26, 0x6c, 0x04, 0x3a, 0x41, 0x6c, 0xfc, 0x3d, 0xe4, 0x39, 0x61, 0xdd, 0xea, 0xdf,
	0x9b, 0x1d, 0xb1, 0x73, 0xfb, 0x90, 0x6d, 0x33, 0x35, 0xb5, 0x65, 0xf6, 0xb5, 0x81, 0x1b, 0xb6,
	0x8c, 0x92, 0x71, 0x85, 0x35, 0x26, 0xc3, 0x6d, 0xd8, 0x0e, 0xe0, 0x74, 0xe1, 0x9d, 0xf0, 0xd5,
	0xc1, 0x7b, 0xe2, 0xaa, 0x7b, 0xcd, 0xeb, 0x09, 0x4f, 0x7d, 0x95, 0x19, 0x74, 0xd3, 0x7b, 0xd0,
	0xee, 0xc9, 0x29, 0x1e, 0x01, 0x09, 0xb1, 0xf1, 0x77, 0xf0, 0xb4, 0xa6, 0xd1, 0xf6, 0x87, 0xd9,
	0x22, 0x98, 0xda, 0x11, 0xad, 0x73, 0x58, 0xb0, 0x2b, 0xc4, 0x21, 0x36, 0x3a, 0x07, 0xc4, 0x39,
	0xe4, 0x7f, 0x6f, 0x61, 0x5e, 0xe5, 0xfc, 0x5e, 0x31, 0xc2, 0xbf, 0x24, 0xd8, 0xe2, 0x34, 0x4f,
	0x8d, 0x7b, 0x6d, 0xd8, 0xa5, 0xef, 0x0c, 0x42, 0xb4, 0x8e, 0xe1, 0xd4, 0x6d, 0xcf, 0xfd, 0x9c,
	0x66, 0x68, 0xd5, 0x93, 0xc8, 0x3a, 0x9f, 0xbf, 0xd8, 0x4c, 0xfe, 0x9e, 0xc3, 0xda, 0xf8, 0xae,
	0x97, 0xc0, 0x38, 0x4b, 0x60, 0xd6, 0x93, 0xba, 0x19, 0x74, 0xfa, 0xbd, 0x49, 0x54, 0x6a, 0x10,
	0xca, 0x6a, 0x71, 0x45, 0x49, 0x9a, 0xa4, 0x69, 0x10, 0x8a, 0x30, 0x64, 0x1d, 0xa9, 0x3a, 0x1c,
	0x47, 0x6c, 0x99, 0xb5, 0xa8, 0xb4, 0x23, 0xbc, 0x31, 0xdd, 0x80, 0xfd, 0x21, 0xc1, 0xcb, 0x13,
	0x5d, 0xbf, 0x1c, 0x04, 0xe4, 0x87, 0x77, 0x25, 0xf4, 0x2d, 0x7d, 0x0b, 0x29, 0xcf, 0x30, 0xe6,
	0x49, 0xba, 0x7a, 0xb0, 0x28, 0x9a, 0x33, 0xc0, 0xe3, 0x6b, 0xf8, 0x35, 0x1c, 0x46, 0xb5, 0x85,
	0xd8, 0xb8, 0x05, 0xcf, 0x4e, 0x8d, 0xae, 0xf1, 0xff, 0x8c, 0xe6, 0xf3, 0x13, 0x9b, 0xc9, 0x0f,
	0x7e, 0x01, 0xcf, 0x23, 0x70, 0x10, 0x1b, 0x9f, 0x00, 0x9e, 0xad, 0x39, 0x5e, 0x8b, 0x84, 0xf6,
	0x40, 0x1b, 0xf6, 0x43, 0x21, 0x88, 0x8d, 0x64, 0xc8, 0x8c, 0x2d, 0xf6, 0x55, 0x6e, 0xd4, 0x58,
	0xa7, 0xbd, 0xbb, 0x4e, 0xf2, 0x0f, 0x7f, 0x86, 0xc7, 0x9c, 0x6e, 0xd3, 0x99, 0x6b, 0x29, 0x88,
	0x5f, 0xc8, 0xe7, 0xb9, 0x25, 0x94, 0x81, 0x95, 0x8b, 0xcb, 0xa6, 0x5a, 0x93, 0x2f, 0x4e, 0x73,
	0x12, 0x02, 0x48, 0x5e, 0xdf, 0x5e, 0x37, 0xcf, 0xde, 0xe5, 0xaa, 0x08, 0xc1, 0x5a, 0x4d, 0xbe,
	0x53, 0x9b, 0xb7, 0x57, 0x67, 0x6a, 0xed, 0xac, 0x21, 0x5f, 0xe4, 0x74, 0x94, 0x87, 0x47, 0x13,
	0x59, 0x5d, 0x56, 0xea, 0xe7, 0x67, 0x39, 0x03, 0xad, 0x03, 0x9a, 0x08, 0x6f, 0x1a, 0x75, 0xb5,
	0x79, 0x79, 0x25, 0xd7, 0x73, 0xf7, 0xd5, 0x7f, 0xd3, 0xb0, 0xe2, 0x32, 0x37, 0x2c, 0xf4, 0x1e,
	0xf2, 0x01, 0x3b, 0x1c, 0xda, 0xf3, 0x79, 0x14, 0xbc, 0xb1, 0x16, 0x71, 0x98, 0x0a, 0xb1, 0xf1,
	0x12, 0xba, 0x81, 0xdc, 0x6c, 0x5c, 0x91, 0xff, 0xa1, 0x07, 0xac, 0x7b, 0xc5, 0x5d, 0xe1, 0xef,
	0x0c, 0xf6, 0x3d, 0xe4, 0x03, 0xea, 0x94, 0x33, 0x3b, 0x78, 0x22, 0x71, 0x66, 0x2f, 0xea, 0xf7,
	0x4b, 0xa8, 0x07, 0x85, 0x45, 0xdb, 0x1a, 0x3a, 0x10, 0x98, 0xe7, 0x5b, 0x0f, 0x8b, 0x2f, 0x22,
	0xe9, 0x31, 0x3a, 0x1b, 0x36, 0x17, 0xee, 0x5c, 0x48, 0x84, 0xe3, 0xdf, 0xef, 0x8a, 0xe5, 0x68,
	0x8a, 0x8c, 0xf1, 0x97, 0xc0, 0x2d, 0x6f, 0xba, 0x1e, 0xa1, 0x57, 0x62, 0x30, 0x6e, 0x45, 0x2b,
	0xbe, 0x8e, 0xae, 0x3c, 0xf6, 0x77, 0xe1, 0x3e, 0xc3, 0xf9, 0x2b, 0xda, 0xa8, 0x38, 0x7f, 0xc5,
	0xeb, 0x51, 0x60, 0x84, 0x83, 0x19, 0x1b, 0x51, 0x19, 0x1b, 0xe1, 0x8c, 0x0b, 0xc7, 0x2e, 0xc7,
	0x28, 0xda, 0x00, 0x38, 0x46, 0xf1, 0x14, 0x5f, 0x42, 0x1f, 0x61, 0x4b, 0x30, 0x7f, 0xd1, 0x4b,
	0x1f, 0x94, 0x78, 0xde, 0x17, 0x0f, 0xa3, 0xaa, 0x32, 0xde, 0x3f, 0x25, 0x38, 0x88, 0x36, 0x3a,
	0xd0, 0x57, 0x3e, 0xe0, 0xc8, 0x93, 0xaf, 0xf8, 0xe6, 0x13, 0x6e, 0x31, 0xcb, 0x7e, 0x97, 0x60,
	0x2f, 0x74, 0x84, 0xa0, 0x63, 0x1f, 0x7c, 0x94, 0xa1, 0x56, 0xfc, 0xe2, 0x61, 0x17, 0x98, 0x29,
	0xbf, 0x4a, 0xb0, 0x1b, 0x32, 0x61, 0xd0, 0x91, 0xa0, 0xbc, 0xe6, 0x07, 0x5a, 0xb1, 0xf2, 0x10,
	0x75, 0xc7, 0x88, 0xda, 0xd1, 0xdd, 0xab, 0x8e, 0xd5, 0xd5, 0xfa, 0x9d, 0xca, 0x9b, 0x2a, 0xa5,
	0x95, 0xb6, 0xd5, 0x3b, 0x66, 0x7f, 0xd5, 0xb4, 0xad, 0xee, 0x31, 0x31, 0x06, 0x1f, 0xcd, 0xb6,
	0x41, 0xa6, 0x7f, 0xe3, 0xb4, 0x92, 0xec, 0xc7, 0x2f, 0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0xab,
	0x6b, 0x8d, 0xb3, 0xe4, 0x11, 0x00, 0x00,
}
