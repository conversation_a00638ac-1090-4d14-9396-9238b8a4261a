// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/one-piece-api.proto

package one_piece_api // import "golang.52tt.com/protocol/services/one-piece-api"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import chance_game_entry "golang.52tt.com/protocol/services/chance-game-entry"
import one_piece "golang.52tt.com/protocol/services/one-piece"
import one_piece_notify "golang.52tt.com/protocol/services/one-piece-notify"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// OnePieceApiClient is the client API for OnePieceApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OnePieceApiClient interface {
	// 配置奖池
	SetOnePiecePrizePool(ctx context.Context, in *one_piece.SetOnePiecePrizePoolReq, opts ...grpc.CallOption) (*one_piece.SetOnePiecePrizePoolResp, error)
	// 获取奖池
	GetOnePiecePrizePool(ctx context.Context, in *one_piece.GetOnePiecePrizePoolReq, opts ...grpc.CallOption) (*one_piece.GetOnePiecePrizePoolResp, error)
	// 模拟抽奖
	SimulateWithPrizePool(ctx context.Context, in *one_piece.SimulateWithPrizePoolReq, opts ...grpc.CallOption) (*one_piece.SimulateWithPrizePoolResp, error)
	// 防违规配置
	SetOnePieceLimitConf(ctx context.Context, in *one_piece.SetOnePieceLimitConfReq, opts ...grpc.CallOption) (*one_piece.SetOnePieceLimitConfResp, error)
	// 获取防违规配置
	GetOnePieceLimitConf(ctx context.Context, in *one_piece.GetOnePieceLimitConfReq, opts ...grpc.CallOption) (*one_piece.GetOnePieceLimitConfResp, error)
	// 配置宝箱
	SetBoxCfg(ctx context.Context, in *one_piece.SetBoxCfgReq, opts ...grpc.CallOption) (*one_piece.SetBoxCfgResp, error)
	// 获取宝箱配置列表
	GetBoxCfgList(ctx context.Context, in *one_piece.GetBoxCfgListReq, opts ...grpc.CallOption) (*one_piece.GetBoxCfgListResp, error)
	// 删除宝箱配置
	DelBoxCfg(ctx context.Context, in *one_piece.DelBoxCfgReq, opts ...grpc.CallOption) (*one_piece.DelBoxCfgResp, error)
	// 更新宝箱配置
	UpdateBoxCfg(ctx context.Context, in *one_piece.UpdateBoxCfgReq, opts ...grpc.CallOption) (*one_piece.UpdateBoxCfgResp, error)
	// =============================== 概率玩法 黑白名单 ==========================
	// 新增房间黑/白名单
	AddChanceGameChannelBWList(ctx context.Context, in *chance_game_entry.AddChannelBWListReq, opts ...grpc.CallOption) (*chance_game_entry.AddChannelBWListResp, error)
	// 新增用户黑/白名单
	AddChanceGameUserBWList(ctx context.Context, in *chance_game_entry.AddUserBWListReq, opts ...grpc.CallOption) (*chance_game_entry.AddUserBWListResp, error)
	// 删除用户或房间黑白名单
	DelBlackWhiteList(ctx context.Context, in *chance_game_entry.DelFromBWListReq, opts ...grpc.CallOption) (*chance_game_entry.DelFromBWListResp, error)
	// 获取房间黑/白名单信息
	GetChannelBWListInfo(ctx context.Context, in *chance_game_entry.GetChannelBWListInfoReq, opts ...grpc.CallOption) (*chance_game_entry.GetChannelBWListInfoResp, error)
	// 获取用户黑/白名单信息
	GetUserBWListInfo(ctx context.Context, in *chance_game_entry.GetUserBWListInfoReq, opts ...grpc.CallOption) (*chance_game_entry.GetUserBWListInfoResp, error)
	// =============================== 航海寻宝相关浮层配置 ========================
	SetNotifyInfo(ctx context.Context, in *one_piece_notify.SetNotifyInfoReq, opts ...grpc.CallOption) (*one_piece_notify.SetNotifyInfoResp, error)
	GetNotifyInfo(ctx context.Context, in *one_piece_notify.GetNotifyInfoReq, opts ...grpc.CallOption) (*one_piece_notify.GetNotifyInfoResp, error)
}

type onePieceApiClient struct {
	cc *grpc.ClientConn
}

func NewOnePieceApiClient(cc *grpc.ClientConn) OnePieceApiClient {
	return &onePieceApiClient{cc}
}

func (c *onePieceApiClient) SetOnePiecePrizePool(ctx context.Context, in *one_piece.SetOnePiecePrizePoolReq, opts ...grpc.CallOption) (*one_piece.SetOnePiecePrizePoolResp, error) {
	out := new(one_piece.SetOnePiecePrizePoolResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/SetOnePiecePrizePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) GetOnePiecePrizePool(ctx context.Context, in *one_piece.GetOnePiecePrizePoolReq, opts ...grpc.CallOption) (*one_piece.GetOnePiecePrizePoolResp, error) {
	out := new(one_piece.GetOnePiecePrizePoolResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/GetOnePiecePrizePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) SimulateWithPrizePool(ctx context.Context, in *one_piece.SimulateWithPrizePoolReq, opts ...grpc.CallOption) (*one_piece.SimulateWithPrizePoolResp, error) {
	out := new(one_piece.SimulateWithPrizePoolResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/SimulateWithPrizePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) SetOnePieceLimitConf(ctx context.Context, in *one_piece.SetOnePieceLimitConfReq, opts ...grpc.CallOption) (*one_piece.SetOnePieceLimitConfResp, error) {
	out := new(one_piece.SetOnePieceLimitConfResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/SetOnePieceLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) GetOnePieceLimitConf(ctx context.Context, in *one_piece.GetOnePieceLimitConfReq, opts ...grpc.CallOption) (*one_piece.GetOnePieceLimitConfResp, error) {
	out := new(one_piece.GetOnePieceLimitConfResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/GetOnePieceLimitConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) SetBoxCfg(ctx context.Context, in *one_piece.SetBoxCfgReq, opts ...grpc.CallOption) (*one_piece.SetBoxCfgResp, error) {
	out := new(one_piece.SetBoxCfgResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/SetBoxCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) GetBoxCfgList(ctx context.Context, in *one_piece.GetBoxCfgListReq, opts ...grpc.CallOption) (*one_piece.GetBoxCfgListResp, error) {
	out := new(one_piece.GetBoxCfgListResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/GetBoxCfgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) DelBoxCfg(ctx context.Context, in *one_piece.DelBoxCfgReq, opts ...grpc.CallOption) (*one_piece.DelBoxCfgResp, error) {
	out := new(one_piece.DelBoxCfgResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/DelBoxCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) UpdateBoxCfg(ctx context.Context, in *one_piece.UpdateBoxCfgReq, opts ...grpc.CallOption) (*one_piece.UpdateBoxCfgResp, error) {
	out := new(one_piece.UpdateBoxCfgResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/UpdateBoxCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) AddChanceGameChannelBWList(ctx context.Context, in *chance_game_entry.AddChannelBWListReq, opts ...grpc.CallOption) (*chance_game_entry.AddChannelBWListResp, error) {
	out := new(chance_game_entry.AddChannelBWListResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/AddChanceGameChannelBWList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) AddChanceGameUserBWList(ctx context.Context, in *chance_game_entry.AddUserBWListReq, opts ...grpc.CallOption) (*chance_game_entry.AddUserBWListResp, error) {
	out := new(chance_game_entry.AddUserBWListResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/AddChanceGameUserBWList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) DelBlackWhiteList(ctx context.Context, in *chance_game_entry.DelFromBWListReq, opts ...grpc.CallOption) (*chance_game_entry.DelFromBWListResp, error) {
	out := new(chance_game_entry.DelFromBWListResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/DelBlackWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) GetChannelBWListInfo(ctx context.Context, in *chance_game_entry.GetChannelBWListInfoReq, opts ...grpc.CallOption) (*chance_game_entry.GetChannelBWListInfoResp, error) {
	out := new(chance_game_entry.GetChannelBWListInfoResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/GetChannelBWListInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) GetUserBWListInfo(ctx context.Context, in *chance_game_entry.GetUserBWListInfoReq, opts ...grpc.CallOption) (*chance_game_entry.GetUserBWListInfoResp, error) {
	out := new(chance_game_entry.GetUserBWListInfoResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/GetUserBWListInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) SetNotifyInfo(ctx context.Context, in *one_piece_notify.SetNotifyInfoReq, opts ...grpc.CallOption) (*one_piece_notify.SetNotifyInfoResp, error) {
	out := new(one_piece_notify.SetNotifyInfoResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/SetNotifyInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onePieceApiClient) GetNotifyInfo(ctx context.Context, in *one_piece_notify.GetNotifyInfoReq, opts ...grpc.CallOption) (*one_piece_notify.GetNotifyInfoResp, error) {
	out := new(one_piece_notify.GetNotifyInfoResp)
	err := c.cc.Invoke(ctx, "/one_piece_api.OnePieceApi/GetNotifyInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OnePieceApiServer is the server API for OnePieceApi service.
type OnePieceApiServer interface {
	// 配置奖池
	SetOnePiecePrizePool(context.Context, *one_piece.SetOnePiecePrizePoolReq) (*one_piece.SetOnePiecePrizePoolResp, error)
	// 获取奖池
	GetOnePiecePrizePool(context.Context, *one_piece.GetOnePiecePrizePoolReq) (*one_piece.GetOnePiecePrizePoolResp, error)
	// 模拟抽奖
	SimulateWithPrizePool(context.Context, *one_piece.SimulateWithPrizePoolReq) (*one_piece.SimulateWithPrizePoolResp, error)
	// 防违规配置
	SetOnePieceLimitConf(context.Context, *one_piece.SetOnePieceLimitConfReq) (*one_piece.SetOnePieceLimitConfResp, error)
	// 获取防违规配置
	GetOnePieceLimitConf(context.Context, *one_piece.GetOnePieceLimitConfReq) (*one_piece.GetOnePieceLimitConfResp, error)
	// 配置宝箱
	SetBoxCfg(context.Context, *one_piece.SetBoxCfgReq) (*one_piece.SetBoxCfgResp, error)
	// 获取宝箱配置列表
	GetBoxCfgList(context.Context, *one_piece.GetBoxCfgListReq) (*one_piece.GetBoxCfgListResp, error)
	// 删除宝箱配置
	DelBoxCfg(context.Context, *one_piece.DelBoxCfgReq) (*one_piece.DelBoxCfgResp, error)
	// 更新宝箱配置
	UpdateBoxCfg(context.Context, *one_piece.UpdateBoxCfgReq) (*one_piece.UpdateBoxCfgResp, error)
	// =============================== 概率玩法 黑白名单 ==========================
	// 新增房间黑/白名单
	AddChanceGameChannelBWList(context.Context, *chance_game_entry.AddChannelBWListReq) (*chance_game_entry.AddChannelBWListResp, error)
	// 新增用户黑/白名单
	AddChanceGameUserBWList(context.Context, *chance_game_entry.AddUserBWListReq) (*chance_game_entry.AddUserBWListResp, error)
	// 删除用户或房间黑白名单
	DelBlackWhiteList(context.Context, *chance_game_entry.DelFromBWListReq) (*chance_game_entry.DelFromBWListResp, error)
	// 获取房间黑/白名单信息
	GetChannelBWListInfo(context.Context, *chance_game_entry.GetChannelBWListInfoReq) (*chance_game_entry.GetChannelBWListInfoResp, error)
	// 获取用户黑/白名单信息
	GetUserBWListInfo(context.Context, *chance_game_entry.GetUserBWListInfoReq) (*chance_game_entry.GetUserBWListInfoResp, error)
	// =============================== 航海寻宝相关浮层配置 ========================
	SetNotifyInfo(context.Context, *one_piece_notify.SetNotifyInfoReq) (*one_piece_notify.SetNotifyInfoResp, error)
	GetNotifyInfo(context.Context, *one_piece_notify.GetNotifyInfoReq) (*one_piece_notify.GetNotifyInfoResp, error)
}

func RegisterOnePieceApiServer(s *grpc.Server, srv OnePieceApiServer) {
	s.RegisterService(&_OnePieceApi_serviceDesc, srv)
}

func _OnePieceApi_SetOnePiecePrizePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece.SetOnePiecePrizePoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).SetOnePiecePrizePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/SetOnePiecePrizePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).SetOnePiecePrizePool(ctx, req.(*one_piece.SetOnePiecePrizePoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_GetOnePiecePrizePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece.GetOnePiecePrizePoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).GetOnePiecePrizePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/GetOnePiecePrizePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).GetOnePiecePrizePool(ctx, req.(*one_piece.GetOnePiecePrizePoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_SimulateWithPrizePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece.SimulateWithPrizePoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).SimulateWithPrizePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/SimulateWithPrizePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).SimulateWithPrizePool(ctx, req.(*one_piece.SimulateWithPrizePoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_SetOnePieceLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece.SetOnePieceLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).SetOnePieceLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/SetOnePieceLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).SetOnePieceLimitConf(ctx, req.(*one_piece.SetOnePieceLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_GetOnePieceLimitConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece.GetOnePieceLimitConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).GetOnePieceLimitConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/GetOnePieceLimitConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).GetOnePieceLimitConf(ctx, req.(*one_piece.GetOnePieceLimitConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_SetBoxCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece.SetBoxCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).SetBoxCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/SetBoxCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).SetBoxCfg(ctx, req.(*one_piece.SetBoxCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_GetBoxCfgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece.GetBoxCfgListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).GetBoxCfgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/GetBoxCfgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).GetBoxCfgList(ctx, req.(*one_piece.GetBoxCfgListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_DelBoxCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece.DelBoxCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).DelBoxCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/DelBoxCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).DelBoxCfg(ctx, req.(*one_piece.DelBoxCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_UpdateBoxCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece.UpdateBoxCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).UpdateBoxCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/UpdateBoxCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).UpdateBoxCfg(ctx, req.(*one_piece.UpdateBoxCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_AddChanceGameChannelBWList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chance_game_entry.AddChannelBWListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).AddChanceGameChannelBWList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/AddChanceGameChannelBWList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).AddChanceGameChannelBWList(ctx, req.(*chance_game_entry.AddChannelBWListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_AddChanceGameUserBWList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chance_game_entry.AddUserBWListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).AddChanceGameUserBWList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/AddChanceGameUserBWList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).AddChanceGameUserBWList(ctx, req.(*chance_game_entry.AddUserBWListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_DelBlackWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chance_game_entry.DelFromBWListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).DelBlackWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/DelBlackWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).DelBlackWhiteList(ctx, req.(*chance_game_entry.DelFromBWListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_GetChannelBWListInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chance_game_entry.GetChannelBWListInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).GetChannelBWListInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/GetChannelBWListInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).GetChannelBWListInfo(ctx, req.(*chance_game_entry.GetChannelBWListInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_GetUserBWListInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(chance_game_entry.GetUserBWListInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).GetUserBWListInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/GetUserBWListInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).GetUserBWListInfo(ctx, req.(*chance_game_entry.GetUserBWListInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_SetNotifyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece_notify.SetNotifyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).SetNotifyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/SetNotifyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).SetNotifyInfo(ctx, req.(*one_piece_notify.SetNotifyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnePieceApi_GetNotifyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(one_piece_notify.GetNotifyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnePieceApiServer).GetNotifyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/one_piece_api.OnePieceApi/GetNotifyInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnePieceApiServer).GetNotifyInfo(ctx, req.(*one_piece_notify.GetNotifyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _OnePieceApi_serviceDesc = grpc.ServiceDesc{
	ServiceName: "one_piece_api.OnePieceApi",
	HandlerType: (*OnePieceApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetOnePiecePrizePool",
			Handler:    _OnePieceApi_SetOnePiecePrizePool_Handler,
		},
		{
			MethodName: "GetOnePiecePrizePool",
			Handler:    _OnePieceApi_GetOnePiecePrizePool_Handler,
		},
		{
			MethodName: "SimulateWithPrizePool",
			Handler:    _OnePieceApi_SimulateWithPrizePool_Handler,
		},
		{
			MethodName: "SetOnePieceLimitConf",
			Handler:    _OnePieceApi_SetOnePieceLimitConf_Handler,
		},
		{
			MethodName: "GetOnePieceLimitConf",
			Handler:    _OnePieceApi_GetOnePieceLimitConf_Handler,
		},
		{
			MethodName: "SetBoxCfg",
			Handler:    _OnePieceApi_SetBoxCfg_Handler,
		},
		{
			MethodName: "GetBoxCfgList",
			Handler:    _OnePieceApi_GetBoxCfgList_Handler,
		},
		{
			MethodName: "DelBoxCfg",
			Handler:    _OnePieceApi_DelBoxCfg_Handler,
		},
		{
			MethodName: "UpdateBoxCfg",
			Handler:    _OnePieceApi_UpdateBoxCfg_Handler,
		},
		{
			MethodName: "AddChanceGameChannelBWList",
			Handler:    _OnePieceApi_AddChanceGameChannelBWList_Handler,
		},
		{
			MethodName: "AddChanceGameUserBWList",
			Handler:    _OnePieceApi_AddChanceGameUserBWList_Handler,
		},
		{
			MethodName: "DelBlackWhiteList",
			Handler:    _OnePieceApi_DelBlackWhiteList_Handler,
		},
		{
			MethodName: "GetChannelBWListInfo",
			Handler:    _OnePieceApi_GetChannelBWListInfo_Handler,
		},
		{
			MethodName: "GetUserBWListInfo",
			Handler:    _OnePieceApi_GetUserBWListInfo_Handler,
		},
		{
			MethodName: "SetNotifyInfo",
			Handler:    _OnePieceApi_SetNotifyInfo_Handler,
		},
		{
			MethodName: "GetNotifyInfo",
			Handler:    _OnePieceApi_GetNotifyInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "apicenter-go/one-piece-api.proto",
}

func init() {
	proto.RegisterFile("apicenter-go/one-piece-api.proto", fileDescriptor_one_piece_api_7d4b5e94e1bcd556)
}

var fileDescriptor_one_piece_api_7d4b5e94e1bcd556 = []byte{
	// 509 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x95, 0x4d, 0x6f, 0xd3, 0x40,
	0x10, 0x86, 0x39, 0x21, 0xb1, 0x25, 0x87, 0x5a, 0xa0, 0x82, 0xcb, 0x01, 0xb5, 0x88, 0xf0, 0x21,
	0xdb, 0xa2, 0x88, 0x3b, 0x4d, 0x2a, 0x56, 0x95, 0x22, 0x88, 0x88, 0xaa, 0x48, 0xbd, 0x98, 0xc5,
	0x19, 0x3b, 0x2b, 0xec, 0x5d, 0xd7, 0x5e, 0x10, 0xe5, 0x37, 0xf3, 0x23, 0xaa, 0x1d, 0x7f, 0xa7,
	0xeb, 0xd8, 0xb7, 0x68, 0xe6, 0xc9, 0xfb, 0xcc, 0xae, 0x3c, 0x36, 0x79, 0xc9, 0x52, 0x1e, 0x80,
	0x50, 0x90, 0x39, 0x91, 0xf4, 0xa4, 0x00, 0x27, 0xe5, 0x10, 0x80, 0xc3, 0x52, 0xee, 0xa6, 0x99,
	0x54, 0xd2, 0x9a, 0x48, 0x01, 0x3e, 0x16, 0x7d, 0x96, 0x72, 0xfb, 0x6d, 0xb0, 0x65, 0x22, 0x00,
	0x27, 0x62, 0x09, 0x38, 0x20, 0x54, 0x76, 0xeb, 0xdd, 0xab, 0x14, 0xff, 0xb4, 0xa7, 0x4d, 0x9c,
	0x90, 0x8a, 0x87, 0xb7, 0xde, 0x6e, 0xa1, 0x04, 0x9f, 0xd7, 0xf5, 0x86, 0x28, 0x5a, 0x67, 0xff,
	0x0f, 0xc8, 0xc1, 0x37, 0x01, 0x4b, 0x5d, 0x3a, 0x4f, 0xb9, 0xc5, 0xc8, 0x93, 0x15, 0xa8, 0xaa,
	0xb2, 0xcc, 0xf8, 0x3f, 0x58, 0x4a, 0x19, 0x5b, 0x27, 0x6e, 0x3d, 0xa6, 0x6b, 0x02, 0xbe, 0xc3,
	0x8d, 0x7d, 0x3a, 0xc8, 0xe4, 0xe9, 0xc9, 0x03, 0xad, 0xa0, 0x43, 0x0a, 0x3a, 0x42, 0x41, 0xfb,
	0x15, 0x1b, 0xf2, 0x74, 0xc5, 0x93, 0xdf, 0x31, 0x53, 0xb0, 0xe6, 0x6a, 0xdb, 0x38, 0x3a, 0x23,
	0x9a, 0x08, 0x2d, 0x79, 0x35, 0x0c, 0x55, 0x07, 0x69, 0x1d, 0x73, 0xc1, 0x13, 0xae, 0xe6, 0x52,
	0x84, 0x7d, 0x77, 0x55, 0x03, 0x7b, 0xee, 0xaa, 0xc5, 0x18, 0xee, 0xca, 0xac, 0xa0, 0x23, 0x14,
	0xb4, 0x5f, 0xf1, 0x99, 0x3c, 0x5a, 0x81, 0x9a, 0xc9, 0xbf, 0xf3, 0x30, 0xb2, 0x8e, 0xba, 0x63,
	0x15, 0x55, 0x1d, 0xf6, 0xcc, 0xdc, 0xc0, 0x84, 0x05, 0x99, 0xd0, 0xaa, 0xb4, 0xe0, 0xb9, 0xb2,
	0x8e, 0xbb, 0xe6, 0xa6, 0xa3, 0x93, 0x5e, 0xf4, 0x37, 0xab, 0x79, 0x2e, 0x20, 0x36, 0xcc, 0x53,
	0x57, 0x77, 0xe7, 0x69, 0x35, 0x30, 0xe1, 0x92, 0x3c, 0xbe, 0x4a, 0x37, 0x4c, 0x41, 0x19, 0x62,
	0xb7, 0xd8, 0x76, 0x43, 0xe7, 0x1c, 0xf7, 0xf6, 0x30, 0x4a, 0x12, 0xfb, 0x7c, 0xb3, 0x99, 0xe3,
	0x02, 0x52, 0x96, 0x80, 0xfe, 0x25, 0x20, 0x9e, 0xad, 0xf1, 0x9c, 0xaf, 0xdd, 0x62, 0x35, 0x7d,
	0xbd, 0x9a, 0x7e, 0xb1, 0x9a, 0x25, 0x5e, 0x43, 0x5a, 0x32, 0x1d, 0xc5, 0xa1, 0x30, 0x24, 0x47,
	0x1d, 0xe1, 0x55, 0x0e, 0x59, 0x69, 0x3b, 0x35, 0xa7, 0x34, 0x44, 0xf1, 0xec, 0x0e, 0x42, 0xe8,
	0xf9, 0x41, 0x0e, 0xf5, 0xb5, 0xc5, 0x2c, 0xf8, 0xb5, 0xde, 0x72, 0x05, 0xbd, 0x86, 0x0b, 0x88,
	0xbf, 0x64, 0x32, 0xd9, 0x6f, 0xd8, 0x81, 0xd0, 0x70, 0x83, 0x8f, 0x6e, 0xe7, 0x8c, 0x97, 0x22,
	0x94, 0xd6, 0x3b, 0xc3, 0xff, 0x4d, 0xa0, 0x76, 0xbd, 0x1f, 0xcd, 0xa2, 0x72, 0x4b, 0x0e, 0x29,
	0xa8, 0xe6, 0xac, 0xe8, 0x9b, 0x9a, 0x33, 0xba, 0x94, 0x96, 0xbd, 0x19, 0x07, 0xa2, 0xe9, 0x9a,
	0x4c, 0x56, 0xa0, 0xbe, 0xe2, 0x4b, 0x16, 0x2d, 0xad, 0x85, 0xf4, 0xcb, 0x77, 0x6f, 0x07, 0xd8,
	0x59, 0xc8, 0x1e, 0xa6, 0xca, 0xa6, 0x43, 0xd9, 0x74, 0x44, 0x36, 0xbd, 0x9f, 0x3d, 0xfb, 0x70,
	0xed, 0x45, 0x32, 0x66, 0x22, 0x72, 0x3f, 0x9d, 0x29, 0xe5, 0x06, 0x32, 0xf1, 0xf0, 0x3b, 0x10,
	0xc8, 0xd8, 0xcb, 0x21, 0xfb, 0xc3, 0x03, 0xc8, 0xbb, 0x5f, 0xa9, 0x9f, 0x0f, 0x11, 0xf8, 0x78,
	0x17, 0x00, 0x00, 0xff, 0xff, 0xf7, 0x4e, 0x35, 0x90, 0xca, 0x06, 0x00, 0x00,
}
