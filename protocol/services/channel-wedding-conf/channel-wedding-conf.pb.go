// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-wedding-conf/channel-wedding-conf.proto

package channel_wedding_conf // import "golang.52tt.com/protocol/services/channel-wedding-conf"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type WeddingAwardType int32

const (
	WeddingAwardType_WEDDING_AWARD_TYPE_UNSPECIFIED       WeddingAwardType = 0
	WeddingAwardType_WEDDING_AWARD_TYPE_HEADWEAR          WeddingAwardType = 1
	WeddingAwardType_WEDDING_AWARD_TYPE_VA_FOLLOW_CHANNEL WeddingAwardType = 2
	WeddingAwardType_WEDDING_AWARD_TYPE_VA_BACKGROUND     WeddingAwardType = 3
	WeddingAwardType_WEDDING_AWARD_TYPE_FELLOW_LIGATURE   WeddingAwardType = 4
)

var WeddingAwardType_name = map[int32]string{
	0: "WEDDING_AWARD_TYPE_UNSPECIFIED",
	1: "WEDDING_AWARD_TYPE_HEADWEAR",
	2: "WEDDING_AWARD_TYPE_VA_FOLLOW_CHANNEL",
	3: "WEDDING_AWARD_TYPE_VA_BACKGROUND",
	4: "WEDDING_AWARD_TYPE_FELLOW_LIGATURE",
}
var WeddingAwardType_value = map[string]int32{
	"WEDDING_AWARD_TYPE_UNSPECIFIED":       0,
	"WEDDING_AWARD_TYPE_HEADWEAR":          1,
	"WEDDING_AWARD_TYPE_VA_FOLLOW_CHANNEL": 2,
	"WEDDING_AWARD_TYPE_VA_BACKGROUND":     3,
	"WEDDING_AWARD_TYPE_FELLOW_LIGATURE":   4,
}

func (x WeddingAwardType) String() string {
	return proto.EnumName(WeddingAwardType_name, int32(x))
}
func (WeddingAwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{0}
}

// 婚礼场景动画枚举
type WeddingScene int32

const (
	WeddingScene_WEDDING_SCENE_UNSPECIFIED       WeddingScene = 0
	WeddingScene_WEDDING_SCENE_BRIDE_GROOM_ENTER WeddingScene = 1
	WeddingScene_WEDDING_SCENE_EXCHANGE_RING     WeddingScene = 2
	WeddingScene_WEDDING_SCENE_HIGHLIGHT         WeddingScene = 3
	WeddingScene_WEDDING_SCENE_GROUP_PHOTO       WeddingScene = 4
)

var WeddingScene_name = map[int32]string{
	0: "WEDDING_SCENE_UNSPECIFIED",
	1: "WEDDING_SCENE_BRIDE_GROOM_ENTER",
	2: "WEDDING_SCENE_EXCHANGE_RING",
	3: "WEDDING_SCENE_HIGHLIGHT",
	4: "WEDDING_SCENE_GROUP_PHOTO",
}
var WeddingScene_value = map[string]int32{
	"WEDDING_SCENE_UNSPECIFIED":       0,
	"WEDDING_SCENE_BRIDE_GROOM_ENTER": 1,
	"WEDDING_SCENE_EXCHANGE_RING":     2,
	"WEDDING_SCENE_HIGHLIGHT":         3,
	"WEDDING_SCENE_GROUP_PHOTO":       4,
}

func (x WeddingScene) String() string {
	return proto.EnumName(WeddingScene_name, int32(x))
}
func (WeddingScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{1}
}

// 获取婚礼主题配置
type GetThemeCfgReq struct {
	ThemeId              uint32   `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetThemeCfgReq) Reset()         { *m = GetThemeCfgReq{} }
func (m *GetThemeCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetThemeCfgReq) ProtoMessage()    {}
func (*GetThemeCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{0}
}
func (m *GetThemeCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeCfgReq.Unmarshal(m, b)
}
func (m *GetThemeCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetThemeCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeCfgReq.Merge(dst, src)
}
func (m *GetThemeCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetThemeCfgReq.Size(m)
}
func (m *GetThemeCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeCfgReq proto.InternalMessageInfo

func (m *GetThemeCfgReq) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

type GetThemeCfgResp struct {
	ThemeCfg             *ThemeCfg `protobuf:"bytes,1,opt,name=theme_cfg,json=themeCfg,proto3" json:"theme_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetThemeCfgResp) Reset()         { *m = GetThemeCfgResp{} }
func (m *GetThemeCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetThemeCfgResp) ProtoMessage()    {}
func (*GetThemeCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{1}
}
func (m *GetThemeCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeCfgResp.Unmarshal(m, b)
}
func (m *GetThemeCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetThemeCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeCfgResp.Merge(dst, src)
}
func (m *GetThemeCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetThemeCfgResp.Size(m)
}
func (m *GetThemeCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeCfgResp proto.InternalMessageInfo

func (m *GetThemeCfgResp) GetThemeCfg() *ThemeCfg {
	if m != nil {
		return m.ThemeCfg
	}
	return nil
}

// 获取婚礼主题配置列表
type GetThemeCfgListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetThemeCfgListReq) Reset()         { *m = GetThemeCfgListReq{} }
func (m *GetThemeCfgListReq) String() string { return proto.CompactTextString(m) }
func (*GetThemeCfgListReq) ProtoMessage()    {}
func (*GetThemeCfgListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{2}
}
func (m *GetThemeCfgListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeCfgListReq.Unmarshal(m, b)
}
func (m *GetThemeCfgListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeCfgListReq.Marshal(b, m, deterministic)
}
func (dst *GetThemeCfgListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeCfgListReq.Merge(dst, src)
}
func (m *GetThemeCfgListReq) XXX_Size() int {
	return xxx_messageInfo_GetThemeCfgListReq.Size(m)
}
func (m *GetThemeCfgListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeCfgListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeCfgListReq proto.InternalMessageInfo

type GetThemeCfgListResp struct {
	ThemeCfgList           []*ThemeCfg `protobuf:"bytes,1,rep,name=theme_cfg_list,json=themeCfgList,proto3" json:"theme_cfg_list,omitempty"`
	ThemeTitleSelectedIcon string      `protobuf:"bytes,2,opt,name=theme_title_selected_icon,json=themeTitleSelectedIcon,proto3" json:"theme_title_selected_icon,omitempty"`
	ThemeTitleBgIcon       string      `protobuf:"bytes,3,opt,name=theme_title_bg_icon,json=themeTitleBgIcon,proto3" json:"theme_title_bg_icon,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}    `json:"-"`
	XXX_unrecognized       []byte      `json:"-"`
	XXX_sizecache          int32       `json:"-"`
}

func (m *GetThemeCfgListResp) Reset()         { *m = GetThemeCfgListResp{} }
func (m *GetThemeCfgListResp) String() string { return proto.CompactTextString(m) }
func (*GetThemeCfgListResp) ProtoMessage()    {}
func (*GetThemeCfgListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{3}
}
func (m *GetThemeCfgListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeCfgListResp.Unmarshal(m, b)
}
func (m *GetThemeCfgListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeCfgListResp.Marshal(b, m, deterministic)
}
func (dst *GetThemeCfgListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeCfgListResp.Merge(dst, src)
}
func (m *GetThemeCfgListResp) XXX_Size() int {
	return xxx_messageInfo_GetThemeCfgListResp.Size(m)
}
func (m *GetThemeCfgListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeCfgListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeCfgListResp proto.InternalMessageInfo

func (m *GetThemeCfgListResp) GetThemeCfgList() []*ThemeCfg {
	if m != nil {
		return m.ThemeCfgList
	}
	return nil
}

func (m *GetThemeCfgListResp) GetThemeTitleSelectedIcon() string {
	if m != nil {
		return m.ThemeTitleSelectedIcon
	}
	return ""
}

func (m *GetThemeCfgListResp) GetThemeTitleBgIcon() string {
	if m != nil {
		return m.ThemeTitleBgIcon
	}
	return ""
}

// 主题配置
type ThemeCfg struct {
	ThemeId                     uint32                `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ThemeName                   string                `protobuf:"bytes,2,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	PriceInfo                   *WeddingPriceInfo     `protobuf:"bytes,3,opt,name=price_info,json=priceInfo,proto3" json:"price_info,omitempty"`
	ThemeRoomResource           *ResourceCfg          `protobuf:"bytes,4,opt,name=theme_room_resource,json=themeRoomResource,proto3" json:"theme_room_resource,omitempty"`
	SceneCfgList                []*WeddingSceneCfg    `protobuf:"bytes,5,rep,name=scene_cfg_list,json=sceneCfgList,proto3" json:"scene_cfg_list,omitempty"`
	ThemeLevelCfgList           []*ThemeLevelCfg      `protobuf:"bytes,6,rep,name=theme_level_cfg_list,json=themeLevelCfgList,proto3" json:"theme_level_cfg_list,omitempty"`
	WeddingPreviewCfg           *WeddingPreviewCfg    `protobuf:"bytes,7,opt,name=wedding_preview_cfg,json=weddingPreviewCfg,proto3" json:"wedding_preview_cfg,omitempty"`
	MemorialVideoResource       *ResourceCfg          `protobuf:"bytes,8,opt,name=memorial_video_resource,json=memorialVideoResource,proto3" json:"memorial_video_resource,omitempty"`
	ChairResCfg                 *ChairGameResourceCfg `protobuf:"bytes,9,opt,name=chair_res_cfg,json=chairResCfg,proto3" json:"chair_res_cfg,omitempty"`
	SelectedThemeTitleIcon      string                `protobuf:"bytes,10,opt,name=selected_theme_title_icon,json=selectedThemeTitleIcon,proto3" json:"selected_theme_title_icon,omitempty"`
	UnselectedThemeTitleIcon    string                `protobuf:"bytes,11,opt,name=unselected_theme_title_icon,json=unselectedThemeTitleIcon,proto3" json:"unselected_theme_title_icon,omitempty"`
	ExamplePhoto                string                `protobuf:"bytes,12,opt,name=example_photo,json=examplePhoto,proto3" json:"example_photo,omitempty"`
	ThemeBackground             string                `protobuf:"bytes,13,opt,name=theme_background,json=themeBackground,proto3" json:"theme_background,omitempty"`
	ThemePreviewText            string                `protobuf:"bytes,14,opt,name=theme_preview_text,json=themePreviewText,proto3" json:"theme_preview_text,omitempty"`
	RewardInfoList              []*FinishWeddingAward `protobuf:"bytes,15,rep,name=reward_info_list,json=rewardInfoList,proto3" json:"reward_info_list,omitempty"`
	WeddingHallComingBackground string                `protobuf:"bytes,16,opt,name=wedding_hall_coming_background,json=weddingHallComingBackground,proto3" json:"wedding_hall_coming_background,omitempty"`
	MailLadyLeftBgIcon          string                `protobuf:"bytes,17,opt,name=mail_lady_left_bg_icon,json=mailLadyLeftBgIcon,proto3" json:"mail_lady_left_bg_icon,omitempty"`
	MailLadyRightBgIcon         string                `protobuf:"bytes,18,opt,name=mail_lady_right_bg_icon,json=mailLadyRightBgIcon,proto3" json:"mail_lady_right_bg_icon,omitempty"`
	ThemeIcon                   string                `protobuf:"bytes,19,opt,name=theme_icon,json=themeIcon,proto3" json:"theme_icon,omitempty"`
	IsDeleted                   bool                  `protobuf:"varint,20,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	XXX_NoUnkeyedLiteral        struct{}              `json:"-"`
	XXX_unrecognized            []byte                `json:"-"`
	XXX_sizecache               int32                 `json:"-"`
}

func (m *ThemeCfg) Reset()         { *m = ThemeCfg{} }
func (m *ThemeCfg) String() string { return proto.CompactTextString(m) }
func (*ThemeCfg) ProtoMessage()    {}
func (*ThemeCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{4}
}
func (m *ThemeCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ThemeCfg.Unmarshal(m, b)
}
func (m *ThemeCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ThemeCfg.Marshal(b, m, deterministic)
}
func (dst *ThemeCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ThemeCfg.Merge(dst, src)
}
func (m *ThemeCfg) XXX_Size() int {
	return xxx_messageInfo_ThemeCfg.Size(m)
}
func (m *ThemeCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ThemeCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ThemeCfg proto.InternalMessageInfo

func (m *ThemeCfg) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *ThemeCfg) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *ThemeCfg) GetPriceInfo() *WeddingPriceInfo {
	if m != nil {
		return m.PriceInfo
	}
	return nil
}

func (m *ThemeCfg) GetThemeRoomResource() *ResourceCfg {
	if m != nil {
		return m.ThemeRoomResource
	}
	return nil
}

func (m *ThemeCfg) GetSceneCfgList() []*WeddingSceneCfg {
	if m != nil {
		return m.SceneCfgList
	}
	return nil
}

func (m *ThemeCfg) GetThemeLevelCfgList() []*ThemeLevelCfg {
	if m != nil {
		return m.ThemeLevelCfgList
	}
	return nil
}

func (m *ThemeCfg) GetWeddingPreviewCfg() *WeddingPreviewCfg {
	if m != nil {
		return m.WeddingPreviewCfg
	}
	return nil
}

func (m *ThemeCfg) GetMemorialVideoResource() *ResourceCfg {
	if m != nil {
		return m.MemorialVideoResource
	}
	return nil
}

func (m *ThemeCfg) GetChairResCfg() *ChairGameResourceCfg {
	if m != nil {
		return m.ChairResCfg
	}
	return nil
}

func (m *ThemeCfg) GetSelectedThemeTitleIcon() string {
	if m != nil {
		return m.SelectedThemeTitleIcon
	}
	return ""
}

func (m *ThemeCfg) GetUnselectedThemeTitleIcon() string {
	if m != nil {
		return m.UnselectedThemeTitleIcon
	}
	return ""
}

func (m *ThemeCfg) GetExamplePhoto() string {
	if m != nil {
		return m.ExamplePhoto
	}
	return ""
}

func (m *ThemeCfg) GetThemeBackground() string {
	if m != nil {
		return m.ThemeBackground
	}
	return ""
}

func (m *ThemeCfg) GetThemePreviewText() string {
	if m != nil {
		return m.ThemePreviewText
	}
	return ""
}

func (m *ThemeCfg) GetRewardInfoList() []*FinishWeddingAward {
	if m != nil {
		return m.RewardInfoList
	}
	return nil
}

func (m *ThemeCfg) GetWeddingHallComingBackground() string {
	if m != nil {
		return m.WeddingHallComingBackground
	}
	return ""
}

func (m *ThemeCfg) GetMailLadyLeftBgIcon() string {
	if m != nil {
		return m.MailLadyLeftBgIcon
	}
	return ""
}

func (m *ThemeCfg) GetMailLadyRightBgIcon() string {
	if m != nil {
		return m.MailLadyRightBgIcon
	}
	return ""
}

func (m *ThemeCfg) GetThemeIcon() string {
	if m != nil {
		return m.ThemeIcon
	}
	return ""
}

func (m *ThemeCfg) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

type AwardInfo struct {
	AwardType            uint32   `protobuf:"varint,1,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	AwardId              string   `protobuf:"bytes,2,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	AwardIdFemale        string   `protobuf:"bytes,3,opt,name=award_id_female,json=awardIdFemale,proto3" json:"award_id_female,omitempty"`
	Amount               uint32   `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	Level                uint32   `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
	LvName               string   `protobuf:"bytes,6,opt,name=lv_name,json=lvName,proto3" json:"lv_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardInfo) Reset()         { *m = AwardInfo{} }
func (m *AwardInfo) String() string { return proto.CompactTextString(m) }
func (*AwardInfo) ProtoMessage()    {}
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{5}
}
func (m *AwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardInfo.Unmarshal(m, b)
}
func (m *AwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardInfo.Marshal(b, m, deterministic)
}
func (dst *AwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardInfo.Merge(dst, src)
}
func (m *AwardInfo) XXX_Size() int {
	return xxx_messageInfo_AwardInfo.Size(m)
}
func (m *AwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardInfo proto.InternalMessageInfo

func (m *AwardInfo) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *AwardInfo) GetAwardId() string {
	if m != nil {
		return m.AwardId
	}
	return ""
}

func (m *AwardInfo) GetAwardIdFemale() string {
	if m != nil {
		return m.AwardIdFemale
	}
	return ""
}

func (m *AwardInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *AwardInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *AwardInfo) GetLvName() string {
	if m != nil {
		return m.LvName
	}
	return ""
}

type FinishWeddingAward struct {
	AwardAnimation       *ResourceCfg `protobuf:"bytes,1,opt,name=award_animation,json=awardAnimation,proto3" json:"award_animation,omitempty"`
	TopText              string       `protobuf:"bytes,2,opt,name=top_text,json=topText,proto3" json:"top_text,omitempty"`
	BottomText           string       `protobuf:"bytes,3,opt,name=bottom_text,json=bottomText,proto3" json:"bottom_text,omitempty"`
	AwardInfo            *AwardInfo   `protobuf:"bytes,4,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"` // Deprecated: Do not use.
	AwardList            []*AwardInfo `protobuf:"bytes,5,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *FinishWeddingAward) Reset()         { *m = FinishWeddingAward{} }
func (m *FinishWeddingAward) String() string { return proto.CompactTextString(m) }
func (*FinishWeddingAward) ProtoMessage()    {}
func (*FinishWeddingAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{6}
}
func (m *FinishWeddingAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishWeddingAward.Unmarshal(m, b)
}
func (m *FinishWeddingAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishWeddingAward.Marshal(b, m, deterministic)
}
func (dst *FinishWeddingAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishWeddingAward.Merge(dst, src)
}
func (m *FinishWeddingAward) XXX_Size() int {
	return xxx_messageInfo_FinishWeddingAward.Size(m)
}
func (m *FinishWeddingAward) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishWeddingAward.DiscardUnknown(m)
}

var xxx_messageInfo_FinishWeddingAward proto.InternalMessageInfo

func (m *FinishWeddingAward) GetAwardAnimation() *ResourceCfg {
	if m != nil {
		return m.AwardAnimation
	}
	return nil
}

func (m *FinishWeddingAward) GetTopText() string {
	if m != nil {
		return m.TopText
	}
	return ""
}

func (m *FinishWeddingAward) GetBottomText() string {
	if m != nil {
		return m.BottomText
	}
	return ""
}

// Deprecated: Do not use.
func (m *FinishWeddingAward) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *FinishWeddingAward) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type ResourceCfg struct {
	ResourceType         uint32   `protobuf:"varint,1,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	ResourceUrl          string   `protobuf:"bytes,2,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceMd5          string   `protobuf:"bytes,3,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	ResourcePng          string   `protobuf:"bytes,4,opt,name=resource_png,json=resourcePng,proto3" json:"resource_png,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResourceCfg) Reset()         { *m = ResourceCfg{} }
func (m *ResourceCfg) String() string { return proto.CompactTextString(m) }
func (*ResourceCfg) ProtoMessage()    {}
func (*ResourceCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{7}
}
func (m *ResourceCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResourceCfg.Unmarshal(m, b)
}
func (m *ResourceCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResourceCfg.Marshal(b, m, deterministic)
}
func (dst *ResourceCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResourceCfg.Merge(dst, src)
}
func (m *ResourceCfg) XXX_Size() int {
	return xxx_messageInfo_ResourceCfg.Size(m)
}
func (m *ResourceCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ResourceCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ResourceCfg proto.InternalMessageInfo

func (m *ResourceCfg) GetResourceType() uint32 {
	if m != nil {
		return m.ResourceType
	}
	return 0
}

func (m *ResourceCfg) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *ResourceCfg) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

func (m *ResourceCfg) GetResourcePng() string {
	if m != nil {
		return m.ResourcePng
	}
	return ""
}

// 婚礼场景动画配置
type WeddingSceneCfg struct {
	Scene                uint32                 `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	Resource             *ResourceCfg           `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
	BoneCfgList          []*WeddingSceneBoneCfg `protobuf:"bytes,3,rep,name=bone_cfg_list,json=boneCfgList,proto3" json:"bone_cfg_list,omitempty"`
	SceneIcon            string                 `protobuf:"bytes,4,opt,name=scene_icon,json=sceneIcon,proto3" json:"scene_icon,omitempty"`
	ClipImSmallPic       string                 `protobuf:"bytes,5,opt,name=clip_im_small_pic,json=clipImSmallPic,proto3" json:"clip_im_small_pic,omitempty"`
	ClipDefaultPic       string                 `protobuf:"bytes,6,opt,name=clip_default_pic,json=clipDefaultPic,proto3" json:"clip_default_pic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *WeddingSceneCfg) Reset()         { *m = WeddingSceneCfg{} }
func (m *WeddingSceneCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneCfg) ProtoMessage()    {}
func (*WeddingSceneCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{8}
}
func (m *WeddingSceneCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneCfg.Unmarshal(m, b)
}
func (m *WeddingSceneCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneCfg.Merge(dst, src)
}
func (m *WeddingSceneCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneCfg.Size(m)
}
func (m *WeddingSceneCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneCfg proto.InternalMessageInfo

func (m *WeddingSceneCfg) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *WeddingSceneCfg) GetResource() *ResourceCfg {
	if m != nil {
		return m.Resource
	}
	return nil
}

func (m *WeddingSceneCfg) GetBoneCfgList() []*WeddingSceneBoneCfg {
	if m != nil {
		return m.BoneCfgList
	}
	return nil
}

func (m *WeddingSceneCfg) GetSceneIcon() string {
	if m != nil {
		return m.SceneIcon
	}
	return ""
}

func (m *WeddingSceneCfg) GetClipImSmallPic() string {
	if m != nil {
		return m.ClipImSmallPic
	}
	return ""
}

func (m *WeddingSceneCfg) GetClipDefaultPic() string {
	if m != nil {
		return m.ClipDefaultPic
	}
	return ""
}

type WeddingScenePreview struct {
	SceneAnimation       *ResourceCfg `protobuf:"bytes,1,opt,name=scene_animation,json=sceneAnimation,proto3" json:"scene_animation,omitempty"`
	SmallIcon            string       `protobuf:"bytes,2,opt,name=small_icon,json=smallIcon,proto3" json:"small_icon,omitempty"`
	ZoomAnimation        *ResourceCfg `protobuf:"bytes,3,opt,name=zoom_animation,json=zoomAnimation,proto3" json:"zoom_animation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WeddingScenePreview) Reset()         { *m = WeddingScenePreview{} }
func (m *WeddingScenePreview) String() string { return proto.CompactTextString(m) }
func (*WeddingScenePreview) ProtoMessage()    {}
func (*WeddingScenePreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{9}
}
func (m *WeddingScenePreview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingScenePreview.Unmarshal(m, b)
}
func (m *WeddingScenePreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingScenePreview.Marshal(b, m, deterministic)
}
func (dst *WeddingScenePreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingScenePreview.Merge(dst, src)
}
func (m *WeddingScenePreview) XXX_Size() int {
	return xxx_messageInfo_WeddingScenePreview.Size(m)
}
func (m *WeddingScenePreview) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingScenePreview.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingScenePreview proto.InternalMessageInfo

func (m *WeddingScenePreview) GetSceneAnimation() *ResourceCfg {
	if m != nil {
		return m.SceneAnimation
	}
	return nil
}

func (m *WeddingScenePreview) GetSmallIcon() string {
	if m != nil {
		return m.SmallIcon
	}
	return ""
}

func (m *WeddingScenePreview) GetZoomAnimation() *ResourceCfg {
	if m != nil {
		return m.ZoomAnimation
	}
	return nil
}

type WeddingSceneBoneCfg struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	SeqIndex             uint32   `protobuf:"varint,2,opt,name=seq_index,json=seqIndex,proto3" json:"seq_index,omitempty"`
	AnimationName        string   `protobuf:"bytes,3,opt,name=animation_name,json=animationName,proto3" json:"animation_name,omitempty"`
	BoneId               uint32   `protobuf:"varint,4,opt,name=bone_id,json=boneId,proto3" json:"bone_id,omitempty"`
	BaseBoneId           uint32   `protobuf:"varint,5,opt,name=base_bone_id,json=baseBoneId,proto3" json:"base_bone_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingSceneBoneCfg) Reset()         { *m = WeddingSceneBoneCfg{} }
func (m *WeddingSceneBoneCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneBoneCfg) ProtoMessage()    {}
func (*WeddingSceneBoneCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{10}
}
func (m *WeddingSceneBoneCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneBoneCfg.Unmarshal(m, b)
}
func (m *WeddingSceneBoneCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneBoneCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneBoneCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneBoneCfg.Merge(dst, src)
}
func (m *WeddingSceneBoneCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneBoneCfg.Size(m)
}
func (m *WeddingSceneBoneCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneBoneCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneBoneCfg proto.InternalMessageInfo

func (m *WeddingSceneBoneCfg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetSeqIndex() uint32 {
	if m != nil {
		return m.SeqIndex
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetAnimationName() string {
	if m != nil {
		return m.AnimationName
	}
	return ""
}

func (m *WeddingSceneBoneCfg) GetBoneId() uint32 {
	if m != nil {
		return m.BoneId
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetBaseBoneId() uint32 {
	if m != nil {
		return m.BaseBoneId
	}
	return 0
}

type WeddingPriceInfo struct {
	Price                uint32              `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	PriceType            uint32              `protobuf:"varint,2,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	NormalTimePrice      []*WeddingPriceItem `protobuf:"bytes,3,rep,name=normal_time_price,json=normalTimePrice,proto3" json:"normal_time_price,omitempty"`
	HotTimePrice         []*WeddingPriceItem `protobuf:"bytes,4,rep,name=hot_time_price,json=hotTimePrice,proto3" json:"hot_time_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *WeddingPriceInfo) Reset()         { *m = WeddingPriceInfo{} }
func (m *WeddingPriceInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingPriceInfo) ProtoMessage()    {}
func (*WeddingPriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{11}
}
func (m *WeddingPriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPriceInfo.Unmarshal(m, b)
}
func (m *WeddingPriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPriceInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingPriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPriceInfo.Merge(dst, src)
}
func (m *WeddingPriceInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingPriceInfo.Size(m)
}
func (m *WeddingPriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPriceInfo proto.InternalMessageInfo

func (m *WeddingPriceInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *WeddingPriceInfo) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *WeddingPriceInfo) GetNormalTimePrice() []*WeddingPriceItem {
	if m != nil {
		return m.NormalTimePrice
	}
	return nil
}

func (m *WeddingPriceInfo) GetHotTimePrice() []*WeddingPriceItem {
	if m != nil {
		return m.HotTimePrice
	}
	return nil
}

type WeddingPriceItem struct {
	Price                uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPriceItem) Reset()         { *m = WeddingPriceItem{} }
func (m *WeddingPriceItem) String() string { return proto.CompactTextString(m) }
func (*WeddingPriceItem) ProtoMessage()    {}
func (*WeddingPriceItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{12}
}
func (m *WeddingPriceItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPriceItem.Unmarshal(m, b)
}
func (m *WeddingPriceItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPriceItem.Marshal(b, m, deterministic)
}
func (dst *WeddingPriceItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPriceItem.Merge(dst, src)
}
func (m *WeddingPriceItem) XXX_Size() int {
	return xxx_messageInfo_WeddingPriceItem.Size(m)
}
func (m *WeddingPriceItem) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPriceItem.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPriceItem proto.InternalMessageInfo

func (m *WeddingPriceItem) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *WeddingPriceItem) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type ThemeLevelCfg struct {
	Level                             uint32                    `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	RoomBackgroundPicture             string                    `protobuf:"bytes,2,opt,name=room_background_picture,json=roomBackgroundPicture,proto3" json:"room_background_picture,omitempty"`
	RoomBackgroundMp4Url              string                    `protobuf:"bytes,3,opt,name=room_background_mp4_url,json=roomBackgroundMp4Url,proto3" json:"room_background_mp4_url,omitempty"`
	GuestDressCfgMap                  map[uint32]*GuestDressCfg `protobuf:"bytes,4,rep,name=guest_dress_cfg_map,json=guestDressCfgMap,proto3" json:"guest_dress_cfg_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	WeddingPreview                    *WeddingScenePreview      `protobuf:"bytes,5,opt,name=wedding_preview,json=weddingPreview,proto3" json:"wedding_preview,omitempty"`
	WeddingHallGoingBackground        string                    `protobuf:"bytes,6,opt,name=wedding_hall_going_background,json=weddingHallGoingBackground,proto3" json:"wedding_hall_going_background,omitempty"`
	CertificatePic                    string                    `protobuf:"bytes,7,opt,name=certificate_pic,json=certificatePic,proto3" json:"certificate_pic,omitempty"`
	PresentId                         uint32                    `protobuf:"varint,8,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	PresentDay                        uint32                    `protobuf:"varint,9,opt,name=present_day,json=presentDay,proto3" json:"present_day,omitempty"`
	SpecialBackgroundPicture          string                    `protobuf:"bytes,10,opt,name=special_background_picture,json=specialBackgroundPicture,proto3" json:"special_background_picture,omitempty"`
	SpecialBackgroundMp4Url           string                    `protobuf:"bytes,11,opt,name=special_background_mp4_url,json=specialBackgroundMp4Url,proto3" json:"special_background_mp4_url,omitempty"`
	FellowSpaceWeddingElement         string                    `protobuf:"bytes,12,opt,name=fellow_space_wedding_element,json=fellowSpaceWeddingElement,proto3" json:"fellow_space_wedding_element,omitempty"`
	FellowSpaceWeddingBackground      string                    `protobuf:"bytes,13,opt,name=fellow_space_wedding_background,json=fellowSpaceWeddingBackground,proto3" json:"fellow_space_wedding_background,omitempty"`
	FellowHouseSpaceWeddingBackground string                    `protobuf:"bytes,14,opt,name=fellow_house_space_wedding_background,json=fellowHouseSpaceWeddingBackground,proto3" json:"fellow_house_space_wedding_background,omitempty"`
	FellowSpaceWeddingColor           string                    `protobuf:"bytes,15,opt,name=fellow_space_wedding_color,json=fellowSpaceWeddingColor,proto3" json:"fellow_space_wedding_color,omitempty"`
	FellowSpaceWeddingCertificate     string                    `protobuf:"bytes,16,opt,name=fellow_space_wedding_certificate,json=fellowSpaceWeddingCertificate,proto3" json:"fellow_space_wedding_certificate,omitempty"`
	BuyWeddingPresentId               uint32                    `protobuf:"varint,17,opt,name=buy_wedding_present_id,json=buyWeddingPresentId,proto3" json:"buy_wedding_present_id,omitempty"`
	XXX_NoUnkeyedLiteral              struct{}                  `json:"-"`
	XXX_unrecognized                  []byte                    `json:"-"`
	XXX_sizecache                     int32                     `json:"-"`
}

func (m *ThemeLevelCfg) Reset()         { *m = ThemeLevelCfg{} }
func (m *ThemeLevelCfg) String() string { return proto.CompactTextString(m) }
func (*ThemeLevelCfg) ProtoMessage()    {}
func (*ThemeLevelCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{13}
}
func (m *ThemeLevelCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ThemeLevelCfg.Unmarshal(m, b)
}
func (m *ThemeLevelCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ThemeLevelCfg.Marshal(b, m, deterministic)
}
func (dst *ThemeLevelCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ThemeLevelCfg.Merge(dst, src)
}
func (m *ThemeLevelCfg) XXX_Size() int {
	return xxx_messageInfo_ThemeLevelCfg.Size(m)
}
func (m *ThemeLevelCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ThemeLevelCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ThemeLevelCfg proto.InternalMessageInfo

func (m *ThemeLevelCfg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ThemeLevelCfg) GetRoomBackgroundPicture() string {
	if m != nil {
		return m.RoomBackgroundPicture
	}
	return ""
}

func (m *ThemeLevelCfg) GetRoomBackgroundMp4Url() string {
	if m != nil {
		return m.RoomBackgroundMp4Url
	}
	return ""
}

func (m *ThemeLevelCfg) GetGuestDressCfgMap() map[uint32]*GuestDressCfg {
	if m != nil {
		return m.GuestDressCfgMap
	}
	return nil
}

func (m *ThemeLevelCfg) GetWeddingPreview() *WeddingScenePreview {
	if m != nil {
		return m.WeddingPreview
	}
	return nil
}

func (m *ThemeLevelCfg) GetWeddingHallGoingBackground() string {
	if m != nil {
		return m.WeddingHallGoingBackground
	}
	return ""
}

func (m *ThemeLevelCfg) GetCertificatePic() string {
	if m != nil {
		return m.CertificatePic
	}
	return ""
}

func (m *ThemeLevelCfg) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *ThemeLevelCfg) GetPresentDay() uint32 {
	if m != nil {
		return m.PresentDay
	}
	return 0
}

func (m *ThemeLevelCfg) GetSpecialBackgroundPicture() string {
	if m != nil {
		return m.SpecialBackgroundPicture
	}
	return ""
}

func (m *ThemeLevelCfg) GetSpecialBackgroundMp4Url() string {
	if m != nil {
		return m.SpecialBackgroundMp4Url
	}
	return ""
}

func (m *ThemeLevelCfg) GetFellowSpaceWeddingElement() string {
	if m != nil {
		return m.FellowSpaceWeddingElement
	}
	return ""
}

func (m *ThemeLevelCfg) GetFellowSpaceWeddingBackground() string {
	if m != nil {
		return m.FellowSpaceWeddingBackground
	}
	return ""
}

func (m *ThemeLevelCfg) GetFellowHouseSpaceWeddingBackground() string {
	if m != nil {
		return m.FellowHouseSpaceWeddingBackground
	}
	return ""
}

func (m *ThemeLevelCfg) GetFellowSpaceWeddingColor() string {
	if m != nil {
		return m.FellowSpaceWeddingColor
	}
	return ""
}

func (m *ThemeLevelCfg) GetFellowSpaceWeddingCertificate() string {
	if m != nil {
		return m.FellowSpaceWeddingCertificate
	}
	return ""
}

func (m *ThemeLevelCfg) GetBuyWeddingPresentId() uint32 {
	if m != nil {
		return m.BuyWeddingPresentId
	}
	return 0
}

// 抢椅子 椅子资源配置
type ChairGameResourceCfg struct {
	ChairPic             string   `protobuf:"bytes,1,opt,name=chair_pic,json=chairPic,proto3" json:"chair_pic,omitempty"`
	SittingPoseFemaleId  uint32   `protobuf:"varint,2,opt,name=sitting_pose_female_id,json=sittingPoseFemaleId,proto3" json:"sitting_pose_female_id,omitempty"`
	SittingPoseMaleId    uint32   `protobuf:"varint,3,opt,name=sitting_pose_male_id,json=sittingPoseMaleId,proto3" json:"sitting_pose_male_id,omitempty"`
	StandbyFemaleId      uint32   `protobuf:"varint,4,opt,name=standby_female_id,json=standbyFemaleId,proto3" json:"standby_female_id,omitempty"`
	StandbyMaleId        uint32   `protobuf:"varint,5,opt,name=standby_male_id,json=standbyMaleId,proto3" json:"standby_male_id,omitempty"`
	FailFemaleIds        []uint32 `protobuf:"varint,6,rep,packed,name=fail_female_ids,json=failFemaleIds,proto3" json:"fail_female_ids,omitempty"`
	FailMaleIds          []uint32 `protobuf:"varint,7,rep,packed,name=fail_male_ids,json=failMaleIds,proto3" json:"fail_male_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChairGameResourceCfg) Reset()         { *m = ChairGameResourceCfg{} }
func (m *ChairGameResourceCfg) String() string { return proto.CompactTextString(m) }
func (*ChairGameResourceCfg) ProtoMessage()    {}
func (*ChairGameResourceCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{14}
}
func (m *ChairGameResourceCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGameResourceCfg.Unmarshal(m, b)
}
func (m *ChairGameResourceCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGameResourceCfg.Marshal(b, m, deterministic)
}
func (dst *ChairGameResourceCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGameResourceCfg.Merge(dst, src)
}
func (m *ChairGameResourceCfg) XXX_Size() int {
	return xxx_messageInfo_ChairGameResourceCfg.Size(m)
}
func (m *ChairGameResourceCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGameResourceCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGameResourceCfg proto.InternalMessageInfo

func (m *ChairGameResourceCfg) GetChairPic() string {
	if m != nil {
		return m.ChairPic
	}
	return ""
}

func (m *ChairGameResourceCfg) GetSittingPoseFemaleId() uint32 {
	if m != nil {
		return m.SittingPoseFemaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetSittingPoseMaleId() uint32 {
	if m != nil {
		return m.SittingPoseMaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetStandbyFemaleId() uint32 {
	if m != nil {
		return m.StandbyFemaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetStandbyMaleId() uint32 {
	if m != nil {
		return m.StandbyMaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetFailFemaleIds() []uint32 {
	if m != nil {
		return m.FailFemaleIds
	}
	return nil
}

func (m *ChairGameResourceCfg) GetFailMaleIds() []uint32 {
	if m != nil {
		return m.FailMaleIds
	}
	return nil
}

// 嘉宾服装配置
type GuestDressCfg struct {
	DressText              string        `protobuf:"bytes,1,opt,name=dress_text,json=dressText,proto3" json:"dress_text,omitempty"`
	SuitCfg                *GuestSuitCfg `protobuf:"bytes,2,opt,name=suit_cfg,json=suitCfg,proto3" json:"suit_cfg,omitempty"`
	WeddingDress           *ResourceCfg  `protobuf:"bytes,3,opt,name=wedding_dress,json=weddingDress,proto3" json:"wedding_dress,omitempty"`
	SmallIcon              string        `protobuf:"bytes,4,opt,name=small_icon,json=smallIcon,proto3" json:"small_icon,omitempty"`
	ClothesUpgradePopupPng string        `protobuf:"bytes,5,opt,name=clothes_upgrade_popup_png,json=clothesUpgradePopupPng,proto3" json:"clothes_upgrade_popup_png,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}      `json:"-"`
	XXX_unrecognized       []byte        `json:"-"`
	XXX_sizecache          int32         `json:"-"`
}

func (m *GuestDressCfg) Reset()         { *m = GuestDressCfg{} }
func (m *GuestDressCfg) String() string { return proto.CompactTextString(m) }
func (*GuestDressCfg) ProtoMessage()    {}
func (*GuestDressCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{15}
}
func (m *GuestDressCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuestDressCfg.Unmarshal(m, b)
}
func (m *GuestDressCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuestDressCfg.Marshal(b, m, deterministic)
}
func (dst *GuestDressCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuestDressCfg.Merge(dst, src)
}
func (m *GuestDressCfg) XXX_Size() int {
	return xxx_messageInfo_GuestDressCfg.Size(m)
}
func (m *GuestDressCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_GuestDressCfg.DiscardUnknown(m)
}

var xxx_messageInfo_GuestDressCfg proto.InternalMessageInfo

func (m *GuestDressCfg) GetDressText() string {
	if m != nil {
		return m.DressText
	}
	return ""
}

func (m *GuestDressCfg) GetSuitCfg() *GuestSuitCfg {
	if m != nil {
		return m.SuitCfg
	}
	return nil
}

func (m *GuestDressCfg) GetWeddingDress() *ResourceCfg {
	if m != nil {
		return m.WeddingDress
	}
	return nil
}

func (m *GuestDressCfg) GetSmallIcon() string {
	if m != nil {
		return m.SmallIcon
	}
	return ""
}

func (m *GuestDressCfg) GetClothesUpgradePopupPng() string {
	if m != nil {
		return m.ClothesUpgradePopupPng
	}
	return ""
}

// 套装配置
type GuestSuitCfg struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	ItemIds              []uint32 `protobuf:"varint,3,rep,packed,name=item_ids,json=itemIds,proto3" json:"item_ids,omitempty"`
	DurationSec          uint32   `protobuf:"varint,4,opt,name=duration_sec,json=durationSec,proto3" json:"duration_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuestSuitCfg) Reset()         { *m = GuestSuitCfg{} }
func (m *GuestSuitCfg) String() string { return proto.CompactTextString(m) }
func (*GuestSuitCfg) ProtoMessage()    {}
func (*GuestSuitCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{16}
}
func (m *GuestSuitCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuestSuitCfg.Unmarshal(m, b)
}
func (m *GuestSuitCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuestSuitCfg.Marshal(b, m, deterministic)
}
func (dst *GuestSuitCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuestSuitCfg.Merge(dst, src)
}
func (m *GuestSuitCfg) XXX_Size() int {
	return xxx_messageInfo_GuestSuitCfg.Size(m)
}
func (m *GuestSuitCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_GuestSuitCfg.DiscardUnknown(m)
}

var xxx_messageInfo_GuestSuitCfg proto.InternalMessageInfo

func (m *GuestSuitCfg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GuestSuitCfg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GuestSuitCfg) GetItemIds() []uint32 {
	if m != nil {
		return m.ItemIds
	}
	return nil
}

func (m *GuestSuitCfg) GetDurationSec() uint32 {
	if m != nil {
		return m.DurationSec
	}
	return 0
}

// 婚礼预告配置
type WeddingPreviewCfg struct {
	Resource             *ResourceCfg `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	CpBoneId             uint32       `protobuf:"varint,2,opt,name=cp_bone_id,json=cpBoneId,proto3" json:"cp_bone_id,omitempty"`
	ItemIds              []uint32     `protobuf:"varint,3,rep,packed,name=item_ids,json=itemIds,proto3" json:"item_ids,omitempty"`
	BaseCpBoneId         uint32       `protobuf:"varint,4,opt,name=base_cp_bone_id,json=baseCpBoneId,proto3" json:"base_cp_bone_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WeddingPreviewCfg) Reset()         { *m = WeddingPreviewCfg{} }
func (m *WeddingPreviewCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingPreviewCfg) ProtoMessage()    {}
func (*WeddingPreviewCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{17}
}
func (m *WeddingPreviewCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPreviewCfg.Unmarshal(m, b)
}
func (m *WeddingPreviewCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPreviewCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingPreviewCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPreviewCfg.Merge(dst, src)
}
func (m *WeddingPreviewCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingPreviewCfg.Size(m)
}
func (m *WeddingPreviewCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPreviewCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPreviewCfg proto.InternalMessageInfo

func (m *WeddingPreviewCfg) GetResource() *ResourceCfg {
	if m != nil {
		return m.Resource
	}
	return nil
}

func (m *WeddingPreviewCfg) GetCpBoneId() uint32 {
	if m != nil {
		return m.CpBoneId
	}
	return 0
}

func (m *WeddingPreviewCfg) GetItemIds() []uint32 {
	if m != nil {
		return m.ItemIds
	}
	return nil
}

func (m *WeddingPreviewCfg) GetBaseCpBoneId() uint32 {
	if m != nil {
		return m.BaseCpBoneId
	}
	return 0
}

type WeddingRankBg struct {
	Lv                   uint32   `protobuf:"varint,1,opt,name=lv,proto3" json:"lv,omitempty"`
	BgUrl                string   `protobuf:"bytes,2,opt,name=bg_url,json=bgUrl,proto3" json:"bg_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingRankBg) Reset()         { *m = WeddingRankBg{} }
func (m *WeddingRankBg) String() string { return proto.CompactTextString(m) }
func (*WeddingRankBg) ProtoMessage()    {}
func (*WeddingRankBg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{18}
}
func (m *WeddingRankBg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRankBg.Unmarshal(m, b)
}
func (m *WeddingRankBg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRankBg.Marshal(b, m, deterministic)
}
func (dst *WeddingRankBg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRankBg.Merge(dst, src)
}
func (m *WeddingRankBg) XXX_Size() int {
	return xxx_messageInfo_WeddingRankBg.Size(m)
}
func (m *WeddingRankBg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRankBg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRankBg proto.InternalMessageInfo

func (m *WeddingRankBg) GetLv() uint32 {
	if m != nil {
		return m.Lv
	}
	return 0
}

func (m *WeddingRankBg) GetBgUrl() string {
	if m != nil {
		return m.BgUrl
	}
	return ""
}

type WeddingRankBgList struct {
	WeddingRankBgList    []*WeddingRankBg `protobuf:"bytes,1,rep,name=wedding_rank_bg_list,json=weddingRankBgList,proto3" json:"wedding_rank_bg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WeddingRankBgList) Reset()         { *m = WeddingRankBgList{} }
func (m *WeddingRankBgList) String() string { return proto.CompactTextString(m) }
func (*WeddingRankBgList) ProtoMessage()    {}
func (*WeddingRankBgList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{19}
}
func (m *WeddingRankBgList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRankBgList.Unmarshal(m, b)
}
func (m *WeddingRankBgList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRankBgList.Marshal(b, m, deterministic)
}
func (dst *WeddingRankBgList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRankBgList.Merge(dst, src)
}
func (m *WeddingRankBgList) XXX_Size() int {
	return xxx_messageInfo_WeddingRankBgList.Size(m)
}
func (m *WeddingRankBgList) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRankBgList.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRankBgList proto.InternalMessageInfo

func (m *WeddingRankBgList) GetWeddingRankBgList() []*WeddingRankBg {
	if m != nil {
		return m.WeddingRankBgList
	}
	return nil
}

type GetAllWeddingRankBackgroundReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllWeddingRankBackgroundReq) Reset()         { *m = GetAllWeddingRankBackgroundReq{} }
func (m *GetAllWeddingRankBackgroundReq) String() string { return proto.CompactTextString(m) }
func (*GetAllWeddingRankBackgroundReq) ProtoMessage()    {}
func (*GetAllWeddingRankBackgroundReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{20}
}
func (m *GetAllWeddingRankBackgroundReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWeddingRankBackgroundReq.Unmarshal(m, b)
}
func (m *GetAllWeddingRankBackgroundReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWeddingRankBackgroundReq.Marshal(b, m, deterministic)
}
func (dst *GetAllWeddingRankBackgroundReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWeddingRankBackgroundReq.Merge(dst, src)
}
func (m *GetAllWeddingRankBackgroundReq) XXX_Size() int {
	return xxx_messageInfo_GetAllWeddingRankBackgroundReq.Size(m)
}
func (m *GetAllWeddingRankBackgroundReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWeddingRankBackgroundReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWeddingRankBackgroundReq proto.InternalMessageInfo

type GetAllWeddingRankBackgroundResp struct {
	WeddingRankBgMap     map[uint32]*WeddingRankBgList `protobuf:"bytes,1,rep,name=wedding_rank_bg_map,json=weddingRankBgMap,proto3" json:"wedding_rank_bg_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetAllWeddingRankBackgroundResp) Reset()         { *m = GetAllWeddingRankBackgroundResp{} }
func (m *GetAllWeddingRankBackgroundResp) String() string { return proto.CompactTextString(m) }
func (*GetAllWeddingRankBackgroundResp) ProtoMessage()    {}
func (*GetAllWeddingRankBackgroundResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{21}
}
func (m *GetAllWeddingRankBackgroundResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWeddingRankBackgroundResp.Unmarshal(m, b)
}
func (m *GetAllWeddingRankBackgroundResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWeddingRankBackgroundResp.Marshal(b, m, deterministic)
}
func (dst *GetAllWeddingRankBackgroundResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWeddingRankBackgroundResp.Merge(dst, src)
}
func (m *GetAllWeddingRankBackgroundResp) XXX_Size() int {
	return xxx_messageInfo_GetAllWeddingRankBackgroundResp.Size(m)
}
func (m *GetAllWeddingRankBackgroundResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWeddingRankBackgroundResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWeddingRankBackgroundResp proto.InternalMessageInfo

func (m *GetAllWeddingRankBackgroundResp) GetWeddingRankBgMap() map[uint32]*WeddingRankBgList {
	if m != nil {
		return m.WeddingRankBgMap
	}
	return nil
}

type GetThemeFinishedAwardCfgReq struct {
	ThemeId              uint32   `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetThemeFinishedAwardCfgReq) Reset()         { *m = GetThemeFinishedAwardCfgReq{} }
func (m *GetThemeFinishedAwardCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetThemeFinishedAwardCfgReq) ProtoMessage()    {}
func (*GetThemeFinishedAwardCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{22}
}
func (m *GetThemeFinishedAwardCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeFinishedAwardCfgReq.Unmarshal(m, b)
}
func (m *GetThemeFinishedAwardCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeFinishedAwardCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetThemeFinishedAwardCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeFinishedAwardCfgReq.Merge(dst, src)
}
func (m *GetThemeFinishedAwardCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetThemeFinishedAwardCfgReq.Size(m)
}
func (m *GetThemeFinishedAwardCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeFinishedAwardCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeFinishedAwardCfgReq proto.InternalMessageInfo

func (m *GetThemeFinishedAwardCfgReq) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

type FinishWeddingAwardList struct {
	ThemeId              uint32                `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	AwardList            []*FinishWeddingAward `protobuf:"bytes,2,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *FinishWeddingAwardList) Reset()         { *m = FinishWeddingAwardList{} }
func (m *FinishWeddingAwardList) String() string { return proto.CompactTextString(m) }
func (*FinishWeddingAwardList) ProtoMessage()    {}
func (*FinishWeddingAwardList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{23}
}
func (m *FinishWeddingAwardList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishWeddingAwardList.Unmarshal(m, b)
}
func (m *FinishWeddingAwardList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishWeddingAwardList.Marshal(b, m, deterministic)
}
func (dst *FinishWeddingAwardList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishWeddingAwardList.Merge(dst, src)
}
func (m *FinishWeddingAwardList) XXX_Size() int {
	return xxx_messageInfo_FinishWeddingAwardList.Size(m)
}
func (m *FinishWeddingAwardList) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishWeddingAwardList.DiscardUnknown(m)
}

var xxx_messageInfo_FinishWeddingAwardList proto.InternalMessageInfo

func (m *FinishWeddingAwardList) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *FinishWeddingAwardList) GetAwardList() []*FinishWeddingAward {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type GetThemeFinishedAwardCfgResp struct {
	ThemeFinishedAwardMap map[uint32]*FinishWeddingAwardList `protobuf:"bytes,1,rep,name=theme_finished_award_map,json=themeFinishedAwardMap,proto3" json:"theme_finished_award_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral  struct{}                           `json:"-"`
	XXX_unrecognized      []byte                             `json:"-"`
	XXX_sizecache         int32                              `json:"-"`
}

func (m *GetThemeFinishedAwardCfgResp) Reset()         { *m = GetThemeFinishedAwardCfgResp{} }
func (m *GetThemeFinishedAwardCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetThemeFinishedAwardCfgResp) ProtoMessage()    {}
func (*GetThemeFinishedAwardCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_7c5695b911a05dda, []int{24}
}
func (m *GetThemeFinishedAwardCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeFinishedAwardCfgResp.Unmarshal(m, b)
}
func (m *GetThemeFinishedAwardCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeFinishedAwardCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetThemeFinishedAwardCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeFinishedAwardCfgResp.Merge(dst, src)
}
func (m *GetThemeFinishedAwardCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetThemeFinishedAwardCfgResp.Size(m)
}
func (m *GetThemeFinishedAwardCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeFinishedAwardCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeFinishedAwardCfgResp proto.InternalMessageInfo

func (m *GetThemeFinishedAwardCfgResp) GetThemeFinishedAwardMap() map[uint32]*FinishWeddingAwardList {
	if m != nil {
		return m.ThemeFinishedAwardMap
	}
	return nil
}

func init() {
	proto.RegisterType((*GetThemeCfgReq)(nil), "channel_wedding_conf.GetThemeCfgReq")
	proto.RegisterType((*GetThemeCfgResp)(nil), "channel_wedding_conf.GetThemeCfgResp")
	proto.RegisterType((*GetThemeCfgListReq)(nil), "channel_wedding_conf.GetThemeCfgListReq")
	proto.RegisterType((*GetThemeCfgListResp)(nil), "channel_wedding_conf.GetThemeCfgListResp")
	proto.RegisterType((*ThemeCfg)(nil), "channel_wedding_conf.ThemeCfg")
	proto.RegisterType((*AwardInfo)(nil), "channel_wedding_conf.AwardInfo")
	proto.RegisterType((*FinishWeddingAward)(nil), "channel_wedding_conf.FinishWeddingAward")
	proto.RegisterType((*ResourceCfg)(nil), "channel_wedding_conf.ResourceCfg")
	proto.RegisterType((*WeddingSceneCfg)(nil), "channel_wedding_conf.WeddingSceneCfg")
	proto.RegisterType((*WeddingScenePreview)(nil), "channel_wedding_conf.WeddingScenePreview")
	proto.RegisterType((*WeddingSceneBoneCfg)(nil), "channel_wedding_conf.WeddingSceneBoneCfg")
	proto.RegisterType((*WeddingPriceInfo)(nil), "channel_wedding_conf.WeddingPriceInfo")
	proto.RegisterType((*WeddingPriceItem)(nil), "channel_wedding_conf.WeddingPriceItem")
	proto.RegisterType((*ThemeLevelCfg)(nil), "channel_wedding_conf.ThemeLevelCfg")
	proto.RegisterMapType((map[uint32]*GuestDressCfg)(nil), "channel_wedding_conf.ThemeLevelCfg.GuestDressCfgMapEntry")
	proto.RegisterType((*ChairGameResourceCfg)(nil), "channel_wedding_conf.ChairGameResourceCfg")
	proto.RegisterType((*GuestDressCfg)(nil), "channel_wedding_conf.GuestDressCfg")
	proto.RegisterType((*GuestSuitCfg)(nil), "channel_wedding_conf.GuestSuitCfg")
	proto.RegisterType((*WeddingPreviewCfg)(nil), "channel_wedding_conf.WeddingPreviewCfg")
	proto.RegisterType((*WeddingRankBg)(nil), "channel_wedding_conf.WeddingRankBg")
	proto.RegisterType((*WeddingRankBgList)(nil), "channel_wedding_conf.WeddingRankBgList")
	proto.RegisterType((*GetAllWeddingRankBackgroundReq)(nil), "channel_wedding_conf.GetAllWeddingRankBackgroundReq")
	proto.RegisterType((*GetAllWeddingRankBackgroundResp)(nil), "channel_wedding_conf.GetAllWeddingRankBackgroundResp")
	proto.RegisterMapType((map[uint32]*WeddingRankBgList)(nil), "channel_wedding_conf.GetAllWeddingRankBackgroundResp.WeddingRankBgMapEntry")
	proto.RegisterType((*GetThemeFinishedAwardCfgReq)(nil), "channel_wedding_conf.GetThemeFinishedAwardCfgReq")
	proto.RegisterType((*FinishWeddingAwardList)(nil), "channel_wedding_conf.FinishWeddingAwardList")
	proto.RegisterType((*GetThemeFinishedAwardCfgResp)(nil), "channel_wedding_conf.GetThemeFinishedAwardCfgResp")
	proto.RegisterMapType((map[uint32]*FinishWeddingAwardList)(nil), "channel_wedding_conf.GetThemeFinishedAwardCfgResp.ThemeFinishedAwardMapEntry")
	proto.RegisterEnum("channel_wedding_conf.WeddingAwardType", WeddingAwardType_name, WeddingAwardType_value)
	proto.RegisterEnum("channel_wedding_conf.WeddingScene", WeddingScene_name, WeddingScene_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelWeddingConfClient is the client API for ChannelWeddingConf service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelWeddingConfClient interface {
	// 获取婚礼主题配置
	GetThemeCfg(ctx context.Context, in *GetThemeCfgReq, opts ...grpc.CallOption) (*GetThemeCfgResp, error)
	// 获取婚礼主题配置列表
	GetThemeCfgList(ctx context.Context, in *GetThemeCfgListReq, opts ...grpc.CallOption) (*GetThemeCfgListResp, error)
	// 获取全量的婚礼榜单背景资源
	GetAllWeddingRankBackground(ctx context.Context, in *GetAllWeddingRankBackgroundReq, opts ...grpc.CallOption) (*GetAllWeddingRankBackgroundResp, error)
	// 获取婚礼主题的奖励配置
	GetThemeFinishedAwardCfg(ctx context.Context, in *GetThemeFinishedAwardCfgReq, opts ...grpc.CallOption) (*GetThemeFinishedAwardCfgResp, error)
}

type channelWeddingConfClient struct {
	cc *grpc.ClientConn
}

func NewChannelWeddingConfClient(cc *grpc.ClientConn) ChannelWeddingConfClient {
	return &channelWeddingConfClient{cc}
}

func (c *channelWeddingConfClient) GetThemeCfg(ctx context.Context, in *GetThemeCfgReq, opts ...grpc.CallOption) (*GetThemeCfgResp, error) {
	out := new(GetThemeCfgResp)
	err := c.cc.Invoke(ctx, "/channel_wedding_conf.ChannelWeddingConf/GetThemeCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingConfClient) GetThemeCfgList(ctx context.Context, in *GetThemeCfgListReq, opts ...grpc.CallOption) (*GetThemeCfgListResp, error) {
	out := new(GetThemeCfgListResp)
	err := c.cc.Invoke(ctx, "/channel_wedding_conf.ChannelWeddingConf/GetThemeCfgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingConfClient) GetAllWeddingRankBackground(ctx context.Context, in *GetAllWeddingRankBackgroundReq, opts ...grpc.CallOption) (*GetAllWeddingRankBackgroundResp, error) {
	out := new(GetAllWeddingRankBackgroundResp)
	err := c.cc.Invoke(ctx, "/channel_wedding_conf.ChannelWeddingConf/GetAllWeddingRankBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingConfClient) GetThemeFinishedAwardCfg(ctx context.Context, in *GetThemeFinishedAwardCfgReq, opts ...grpc.CallOption) (*GetThemeFinishedAwardCfgResp, error) {
	out := new(GetThemeFinishedAwardCfgResp)
	err := c.cc.Invoke(ctx, "/channel_wedding_conf.ChannelWeddingConf/GetThemeFinishedAwardCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelWeddingConfServer is the server API for ChannelWeddingConf service.
type ChannelWeddingConfServer interface {
	// 获取婚礼主题配置
	GetThemeCfg(context.Context, *GetThemeCfgReq) (*GetThemeCfgResp, error)
	// 获取婚礼主题配置列表
	GetThemeCfgList(context.Context, *GetThemeCfgListReq) (*GetThemeCfgListResp, error)
	// 获取全量的婚礼榜单背景资源
	GetAllWeddingRankBackground(context.Context, *GetAllWeddingRankBackgroundReq) (*GetAllWeddingRankBackgroundResp, error)
	// 获取婚礼主题的奖励配置
	GetThemeFinishedAwardCfg(context.Context, *GetThemeFinishedAwardCfgReq) (*GetThemeFinishedAwardCfgResp, error)
}

func RegisterChannelWeddingConfServer(s *grpc.Server, srv ChannelWeddingConfServer) {
	s.RegisterService(&_ChannelWeddingConf_serviceDesc, srv)
}

func _ChannelWeddingConf_GetThemeCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetThemeCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingConfServer).GetThemeCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_conf.ChannelWeddingConf/GetThemeCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingConfServer).GetThemeCfg(ctx, req.(*GetThemeCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingConf_GetThemeCfgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetThemeCfgListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingConfServer).GetThemeCfgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_conf.ChannelWeddingConf/GetThemeCfgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingConfServer).GetThemeCfgList(ctx, req.(*GetThemeCfgListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingConf_GetAllWeddingRankBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllWeddingRankBackgroundReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingConfServer).GetAllWeddingRankBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_conf.ChannelWeddingConf/GetAllWeddingRankBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingConfServer).GetAllWeddingRankBackground(ctx, req.(*GetAllWeddingRankBackgroundReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingConf_GetThemeFinishedAwardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetThemeFinishedAwardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingConfServer).GetThemeFinishedAwardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_conf.ChannelWeddingConf/GetThemeFinishedAwardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingConfServer).GetThemeFinishedAwardCfg(ctx, req.(*GetThemeFinishedAwardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelWeddingConf_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_wedding_conf.ChannelWeddingConf",
	HandlerType: (*ChannelWeddingConfServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetThemeCfg",
			Handler:    _ChannelWeddingConf_GetThemeCfg_Handler,
		},
		{
			MethodName: "GetThemeCfgList",
			Handler:    _ChannelWeddingConf_GetThemeCfgList_Handler,
		},
		{
			MethodName: "GetAllWeddingRankBackground",
			Handler:    _ChannelWeddingConf_GetAllWeddingRankBackground_Handler,
		},
		{
			MethodName: "GetThemeFinishedAwardCfg",
			Handler:    _ChannelWeddingConf_GetThemeFinishedAwardCfg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-wedding-conf/channel-wedding-conf.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-wedding-conf/channel-wedding-conf.proto", fileDescriptor_channel_wedding_conf_7c5695b911a05dda)
}

var fileDescriptor_channel_wedding_conf_7c5695b911a05dda = []byte{
	// 2564 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x59, 0x4b, 0x6f, 0x1b, 0xc9,
	0x11, 0x36, 0x29, 0x59, 0x12, 0x4b, 0xa2, 0x44, 0xb5, 0x64, 0x89, 0xa6, 0xfc, 0x90, 0xc7, 0x6b,
	0xaf, 0xec, 0xec, 0xca, 0x88, 0xd7, 0x5e, 0xac, 0xb3, 0xf1, 0x06, 0x14, 0x45, 0x53, 0xcc, 0xea,
	0xc1, 0x8c, 0xe8, 0x75, 0x36, 0x08, 0x30, 0x18, 0xce, 0x34, 0xc9, 0x81, 0xe7, 0xa5, 0x99, 0xa6,
	0x6c, 0x2d, 0x72, 0x0b, 0x90, 0x4b, 0x82, 0xe4, 0x07, 0xe4, 0x90, 0x5c, 0x93, 0x4b, 0x80, 0x20,
	0x40, 0x90, 0x4b, 0x6e, 0xb9, 0x04, 0x01, 0xf2, 0x3b, 0xf2, 0x0f, 0x82, 0x9c, 0x82, 0xae, 0xee,
	0x79, 0x90, 0x1c, 0xca, 0xf4, 0x9e, 0x38, 0x5d, 0xfd, 0x55, 0x4d, 0x75, 0x55, 0x75, 0xf5, 0xd7,
	0x43, 0xf8, 0x82, 0xb1, 0x47, 0x67, 0x03, 0xcb, 0x78, 0x1d, 0x5a, 0xf6, 0x39, 0x0d, 0x1e, 0x19,
	0x7d, 0xdd, 0x75, 0xa9, 0xfd, 0xf1, 0x1b, 0x6a, 0x9a, 0x96, 0xdb, 0xfb, 0xd8, 0xf0, 0xdc, 0x6e,
	0xa6, 0x70, 0xd7, 0x0f, 0x3c, 0xe6, 0x91, 0x75, 0x39, 0xa7, 0xc9, 0x39, 0x8d, 0xcf, 0x55, 0x76,
	0x47, 0xac, 0xd2, 0xb7, 0x8c, 0xba, 0xa1, 0xe5, 0xb9, 0x8f, 0x3c, 0x9f, 0x59, 0x9e, 0x1b, 0x46,
	0xbf, 0xc2, 0x8a, 0xf2, 0x1d, 0x58, 0x6e, 0x50, 0xd6, 0xee, 0x53, 0x87, 0xd6, 0xba, 0x3d, 0x95,
	0x9e, 0x91, 0xeb, 0xb0, 0xc0, 0xf8, 0x50, 0xb3, 0xcc, 0x72, 0x6e, 0x3b, 0xb7, 0x53, 0x54, 0xe7,
	0x71, 0xdc, 0x34, 0x95, 0x63, 0x58, 0x19, 0x02, 0x87, 0x3e, 0xf9, 0x1c, 0x0a, 0x02, 0x6d, 0x74,
	0x7b, 0x08, 0x5f, 0x7c, 0x7c, 0x6b, 0x37, 0xcb, 0xb3, 0xdd, 0x58, 0x4d, 0x98, 0xaf, 0x75, 0x7b,
	0xca, 0x3a, 0x90, 0x94, 0xbd, 0x43, 0x2b, 0x64, 0x2a, 0x3d, 0x53, 0xfe, 0x91, 0x83, 0xb5, 0x31,
	0x71, 0xe8, 0x93, 0x7d, 0x58, 0x8e, 0x5f, 0xa5, 0xd9, 0x56, 0xc8, 0xca, 0xb9, 0xed, 0x99, 0x29,
	0xde, 0xb7, 0xc4, 0x52, 0x96, 0xc8, 0x33, 0xb8, 0x2e, 0xac, 0x30, 0x8b, 0xd9, 0x54, 0x0b, 0xa9,
	0x4d, 0x0d, 0x46, 0x4d, 0xcd, 0x32, 0x3c, 0xb7, 0x9c, 0xdf, 0xce, 0xed, 0x14, 0xd4, 0x0d, 0x04,
	0xb4, 0xf9, 0xfc, 0xa9, 0x9c, 0x6e, 0x1a, 0x9e, 0x4b, 0x3e, 0x86, 0xb5, 0xb4, 0x6a, 0xa7, 0x27,
	0x94, 0x66, 0x50, 0xa9, 0x94, 0x28, 0xed, 0xf5, 0x38, 0x5c, 0xf9, 0x7b, 0x01, 0x16, 0x22, 0x27,
	0x2e, 0x89, 0x2a, 0xb9, 0x09, 0x20, 0xa6, 0x5c, 0xdd, 0xa1, 0xd2, 0x05, 0x11, 0xd4, 0x63, 0xdd,
	0xa1, 0xa4, 0x0e, 0xe0, 0x07, 0x96, 0x41, 0x35, 0xcb, 0xed, 0x7a, 0xf8, 0xb2, 0xc5, 0xc7, 0xf7,
	0xb3, 0x97, 0xfc, 0x4a, 0x0c, 0x5a, 0x1c, 0xde, 0x74, 0xbb, 0x9e, 0x5a, 0xf0, 0xa3, 0x47, 0xf2,
	0xa3, 0xc8, 0xf9, 0xc0, 0xf3, 0x1c, 0x2d, 0xa0, 0xa1, 0x37, 0x08, 0x0c, 0x5a, 0x9e, 0x45, 0x7b,
	0x77, 0xb2, 0xed, 0xa9, 0x12, 0xc5, 0xa3, 0xb8, 0x8a, 0xda, 0xaa, 0xe7, 0x39, 0x91, 0x94, 0x7c,
	0x09, 0xcb, 0xa1, 0x41, 0xdd, 0x54, 0x42, 0xae, 0x62, 0x42, 0xee, 0x5d, 0xea, 0xdd, 0x29, 0x57,
	0xc1, 0xbc, 0x84, 0xf2, 0x09, 0xf3, 0xd2, 0x86, 0x75, 0xe1, 0x9f, 0x4d, 0xcf, 0xa9, 0x9d, 0x98,
	0x9c, 0x43, 0x93, 0x77, 0x2f, 0xc9, 0xf1, 0x21, 0x57, 0x48, 0x5c, 0x8c, 0x86, 0x68, 0xf5, 0x15,
	0xac, 0x45, 0x0a, 0x7e, 0x40, 0xcf, 0x2d, 0xfa, 0x06, 0x0b, 0x75, 0x1e, 0x57, 0xfd, 0xe1, 0x3b,
	0xa2, 0x88, 0x78, 0x34, 0xfc, 0x66, 0x54, 0x44, 0xbe, 0x86, 0x4d, 0x87, 0x3a, 0x5e, 0x60, 0xe9,
	0xb6, 0x76, 0x6e, 0x99, 0xd4, 0x4b, 0x42, 0xba, 0x30, 0x6d, 0x48, 0xaf, 0x45, 0x16, 0xbe, 0xe2,
	0x06, 0xe2, 0xb0, 0x1e, 0x43, 0xd1, 0xe8, 0xeb, 0x56, 0xc0, 0x2d, 0xa2, 0xb7, 0x05, 0x34, 0xf8,
	0x30, 0xdb, 0x60, 0x8d, 0x43, 0x1b, 0xba, 0x43, 0xd3, 0x96, 0x17, 0xd1, 0x80, 0x4a, 0x43, 0xee,
	0xea, 0x33, 0xb8, 0x1e, 0x57, 0x79, 0xba, 0x7e, 0xb1, 0x78, 0x41, 0x54, 0x7c, 0x04, 0x68, 0xc7,
	0x45, 0x8c, 0x15, 0xff, 0x1c, 0xb6, 0x06, 0xee, 0x64, 0xe5, 0x45, 0x54, 0x2e, 0x27, 0x90, 0x11,
	0xf5, 0xbb, 0x50, 0xa4, 0x6f, 0x75, 0xc7, 0xb7, 0xa9, 0xe6, 0xf7, 0x3d, 0xe6, 0x95, 0x97, 0x50,
	0x61, 0x49, 0x0a, 0x5b, 0x5c, 0x46, 0x1e, 0x80, 0xd8, 0x3a, 0x5a, 0x47, 0x37, 0x5e, 0xf7, 0x02,
	0x6f, 0xe0, 0x9a, 0xe5, 0x22, 0xe2, 0x56, 0x50, 0xbe, 0x17, 0x8b, 0xc9, 0x47, 0x40, 0x04, 0x34,
	0xca, 0x25, 0xa3, 0x6f, 0x59, 0x79, 0x39, 0xb5, 0xff, 0x64, 0x86, 0xda, 0xf4, 0x2d, 0x23, 0x2a,
	0x94, 0x02, 0xfa, 0x46, 0x0f, 0x4c, 0xdc, 0x39, 0xa2, 0x9a, 0x56, 0xb0, 0x9a, 0x76, 0xb2, 0x43,
	0xf9, 0xc2, 0x72, 0xad, 0xb0, 0x2f, 0xd3, 0x5f, 0xe5, 0x9a, 0xea, 0xb2, 0xb0, 0xc0, 0x77, 0x10,
	0xd6, 0x53, 0x0d, 0x6e, 0x45, 0x2a, 0x7d, 0xdd, 0xb6, 0x35, 0xc3, 0x73, 0xf8, 0x73, 0xca, 0xf5,
	0x12, 0x7a, 0xb3, 0x25, 0x51, 0x07, 0xba, 0x6d, 0xd7, 0x10, 0x93, 0x5a, 0xc6, 0x63, 0xd8, 0x70,
	0x74, 0xcb, 0xd6, 0x6c, 0xdd, 0xbc, 0xd0, 0x6c, 0xda, 0x65, 0x71, 0x2b, 0x59, 0x45, 0x65, 0xc2,
	0x67, 0x0f, 0x75, 0xf3, 0xe2, 0x90, 0x76, 0x99, 0x68, 0x26, 0xe4, 0x09, 0x6c, 0x26, 0x3a, 0x81,
	0xd5, 0xeb, 0x27, 0x4a, 0x04, 0x95, 0xd6, 0x22, 0x25, 0x95, 0x4f, 0x4a, 0xad, 0xb8, 0xb5, 0x20,
	0x70, 0x2d, 0xd5, 0x5a, 0xa2, 0x69, 0x2b, 0xd4, 0x4c, 0x6a, 0x53, 0x46, 0xcd, 0xf2, 0xfa, 0x76,
	0x6e, 0x67, 0x41, 0x2d, 0x58, 0xe1, 0xbe, 0x10, 0x28, 0x7f, 0xc9, 0x41, 0xa1, 0x1a, 0x2d, 0x9f,
	0x83, 0x75, 0x8c, 0x26, 0xbb, 0xf0, 0xa9, 0xec, 0x61, 0x05, 0x94, 0xb4, 0x2f, 0x7c, 0xca, 0x1b,
	0x9c, 0x98, 0xb6, 0x4c, 0xd9, 0xc3, 0xe6, 0x71, 0xdc, 0x34, 0xc9, 0x7d, 0x58, 0x89, 0xa6, 0xb4,
	0x2e, 0x75, 0x74, 0x9b, 0xca, 0x9e, 0x59, 0x94, 0x88, 0x17, 0x28, 0x24, 0x1b, 0x30, 0xa7, 0x3b,
	0xde, 0xc0, 0x65, 0xd8, 0x95, 0x8a, 0xaa, 0x1c, 0x91, 0x75, 0xb8, 0x8a, 0x4d, 0xa1, 0x7c, 0x15,
	0xc5, 0x62, 0x40, 0x36, 0x61, 0xde, 0x3e, 0x17, 0x3d, 0x73, 0x0e, 0xad, 0xcd, 0xd9, 0xe7, 0xbc,
	0x61, 0x2a, 0xbf, 0xcf, 0x03, 0x19, 0x4f, 0x25, 0xf9, 0x61, 0xe4, 0x85, 0xee, 0x5a, 0x8e, 0xce,
	0xcf, 0x40, 0x79, 0x5e, 0x4d, 0xb1, 0x53, 0x97, 0x51, 0xb3, 0x1a, 0x29, 0x62, 0x37, 0xf7, 0x7c,
	0x51, 0x7e, 0x72, 0xb1, 0xcc, 0xf3, 0xb1, 0xea, 0x6e, 0xc3, 0x62, 0xc7, 0x63, 0xcc, 0x73, 0xc4,
	0xac, 0x58, 0x28, 0x08, 0x11, 0x02, 0xf6, 0xa2, 0x38, 0x62, 0x3f, 0x17, 0xfd, 0xf7, 0x76, 0xb6,
	0x0b, 0x71, 0xf0, 0xf7, 0xf2, 0xe5, 0x9c, 0x0c, 0x36, 0xe6, 0xe2, 0x8b, 0xc8, 0x46, 0xaa, 0xeb,
	0xbe, 0xcb, 0x86, 0xd4, 0xe7, 0x65, 0xac, 0xfc, 0x36, 0x07, 0x8b, 0xa9, 0xf5, 0xf1, 0x8d, 0x1a,
	0xb5, 0xaf, 0x74, 0x7a, 0x97, 0x22, 0x21, 0x66, 0xf8, 0x0e, 0xc4, 0x63, 0x6d, 0x10, 0xd8, 0x72,
	0xe1, 0x8b, 0x91, 0xec, 0x65, 0x60, 0x0f, 0x41, 0x1c, 0xf3, 0xa9, 0x5c, 0x7d, 0x0c, 0x39, 0x32,
	0x9f, 0x0e, 0x41, 0x7c, 0xb7, 0x87, 0x01, 0x48, 0x41, 0x5a, 0x6e, 0x4f, 0xf9, 0x43, 0x1e, 0x56,
	0x46, 0x0e, 0x0b, 0x5e, 0x03, 0x78, 0x5c, 0x48, 0xcf, 0xc4, 0x80, 0x3c, 0x87, 0x85, 0xb8, 0xed,
	0xe6, 0xa7, 0x4d, 0x66, 0xac, 0x42, 0x8e, 0xa0, 0xd8, 0xf1, 0xd2, 0xe7, 0xd7, 0x0c, 0x46, 0xf2,
	0xc1, 0xbb, 0xcf, 0xaf, 0x3d, 0x4f, 0x9c, 0x61, 0x8b, 0x1d, 0x2f, 0x39, 0xc2, 0x6e, 0x02, 0x88,
	0xf3, 0x10, 0x77, 0x9b, 0x58, 0x58, 0x01, 0x25, 0xb8, 0xdb, 0x1e, 0xc0, 0xaa, 0x61, 0x5b, 0xbe,
	0x66, 0x39, 0x5a, 0xe8, 0xf0, 0xe6, 0xe1, 0x5b, 0x06, 0x96, 0x74, 0x41, 0x5d, 0xe6, 0x13, 0x4d,
	0xe7, 0x94, 0x8b, 0x5b, 0x96, 0x41, 0x76, 0xa0, 0x84, 0x50, 0x93, 0x76, 0xf5, 0x81, 0xcd, 0x10,
	0x39, 0x97, 0x20, 0xf7, 0x85, 0xb8, 0x65, 0x19, 0xca, 0x3f, 0x73, 0xb0, 0x96, 0x76, 0x4c, 0x36,
	0x40, 0x5e, 0xed, 0xc2, 0x97, 0x6f, 0x53, 0xed, 0xa8, 0x99, 0x54, 0x3b, 0x5f, 0x17, 0x3a, 0x9c,
	0xe2, 0x48, 0x05, 0x94, 0xe0, 0xba, 0x0e, 0x60, 0xf9, 0x1b, 0xce, 0x29, 0x92, 0x37, 0xcd, 0x4c,
	0xfb, 0xa6, 0x22, 0x57, 0x8c, 0x5f, 0xa4, 0xfc, 0x71, 0x64, 0x31, 0x32, 0xca, 0x49, 0x03, 0xc8,
	0xa5, 0x1b, 0xc0, 0x16, 0x14, 0x42, 0x7a, 0xa6, 0x59, 0xae, 0x49, 0xdf, 0xa2, 0x57, 0x45, 0x75,
	0x21, 0xa4, 0x67, 0x4d, 0x3e, 0x26, 0xf7, 0x60, 0x39, 0xf6, 0x47, 0x34, 0x89, 0xa8, 0xe5, 0x44,
	0x52, 0x24, 0x57, 0x9b, 0x30, 0x8f, 0x15, 0x60, 0x99, 0x51, 0xcf, 0xe1, 0xc3, 0xa6, 0x49, 0xb6,
	0x61, 0xa9, 0xa3, 0x87, 0x54, 0x8b, 0x66, 0x45, 0xeb, 0x01, 0x2e, 0xdb, 0x43, 0x84, 0xf2, 0x9f,
	0x1c, 0x94, 0x46, 0x09, 0x17, 0xf7, 0x14, 0x29, 0x57, 0xe4, 0x29, 0x0e, 0x78, 0x00, 0x05, 0x85,
	0xc3, 0xbd, 0x25, 0x5c, 0x15, 0xd4, 0x0c, 0x37, 0x96, 0x0a, 0xab, 0xae, 0x17, 0x38, 0xba, 0xad,
	0x31, 0x0b, 0x0f, 0x37, 0x6e, 0x40, 0x94, 0xe2, 0x34, 0x44, 0x8f, 0x51, 0x47, 0x5d, 0x11, 0x06,
	0xda, 0x16, 0x3f, 0x02, 0xf9, 0x2b, 0x0f, 0x61, 0xb9, 0xef, 0xb1, 0xb4, 0xc1, 0xd9, 0xf7, 0x32,
	0xb8, 0xd4, 0xf7, 0x58, 0x6c, 0x4d, 0xa9, 0x8e, 0x2c, 0x95, 0x51, 0x67, 0xc2, 0x52, 0x37, 0x61,
	0xbe, 0x67, 0x75, 0x59, 0x74, 0x0a, 0x14, 0xd5, 0x39, 0x3e, 0x6c, 0x9a, 0xca, 0x7f, 0x17, 0xa0,
	0x38, 0x44, 0xd7, 0x26, 0x64, 0xf5, 0x53, 0xd8, 0x44, 0x86, 0x9a, 0x1c, 0xa9, 0xbc, 0xfa, 0xd9,
	0x20, 0x88, 0xa8, 0xf1, 0x35, 0x3e, 0x9d, 0x9c, 0xa6, 0x2d, 0x31, 0x49, 0x9e, 0x8e, 0xeb, 0x39,
	0xfe, 0x13, 0x6c, 0x54, 0x22, 0xf3, 0xeb, 0xc3, 0x7a, 0x47, 0xfe, 0x13, 0xde, 0xb1, 0xfa, 0xb0,
	0xd6, 0x1b, 0xd0, 0x90, 0x69, 0x66, 0x40, 0x43, 0xa4, 0x5b, 0x9a, 0xa3, 0xfb, 0x32, 0x58, 0xcf,
	0xa6, 0x60, 0x9d, 0xbb, 0x0d, 0xae, 0xbe, 0xcf, 0xb5, 0x6b, 0xdd, 0xde, 0x91, 0xee, 0xd7, 0x5d,
	0x16, 0x5c, 0xa8, 0xa5, 0xde, 0x88, 0x98, 0xa8, 0xb0, 0x32, 0x42, 0x45, 0xb1, 0xa8, 0xa6, 0x6a,
	0x37, 0x72, 0x57, 0xab, 0xcb, 0xc3, 0x44, 0x94, 0x54, 0xe1, 0xe6, 0x10, 0x1d, 0xe9, 0x79, 0x23,
	0x6c, 0x44, 0x34, 0x8d, 0x4a, 0x8a, 0x8d, 0x34, 0xbc, 0x61, 0x32, 0xf2, 0x21, 0xac, 0x18, 0x34,
	0x60, 0x56, 0xd7, 0x32, 0x74, 0x46, 0xb1, 0xd3, 0xcc, 0xcb, 0x4e, 0x93, 0x88, 0x79, 0x4f, 0xc2,
	0x22, 0xa6, 0x21, 0x75, 0x31, 0xb9, 0x0b, 0x51, 0x11, 0xa3, 0xa4, 0x69, 0xf2, 0x73, 0x2f, 0x9a,
	0x36, 0xf5, 0x0b, 0xe4, 0xac, 0x45, 0x35, 0xd2, 0xd8, 0xd7, 0x2f, 0xc8, 0xf7, 0xa1, 0x12, 0xfa,
	0xd4, 0xe0, 0x84, 0x39, 0x23, 0xb7, 0x82, 0x87, 0x96, 0x25, 0x62, 0x3c, 0xbd, 0x9f, 0x67, 0x6a,
	0x47, 0x19, 0x16, 0x44, 0x74, 0x73, 0x4c, 0x5b, 0x26, 0xf9, 0x07, 0x70, 0xa3, 0x4b, 0x6d, 0xdb,
	0x7b, 0xa3, 0x85, 0xbe, 0x6e, 0xd0, 0x38, 0xce, 0xd4, 0xa6, 0x0e, 0x75, 0x99, 0xa4, 0xa5, 0xd7,
	0x05, 0xe6, 0x94, 0x43, 0x64, 0xf0, 0xeb, 0x02, 0x40, 0xea, 0x70, 0x3b, 0xd3, 0xc0, 0x18, 0x65,
	0xbd, 0x31, 0x6e, 0x23, 0x15, 0xeb, 0x16, 0xdc, 0x93, 0x66, 0xfa, 0xde, 0x20, 0xa4, 0x93, 0x8d,
	0x09, 0x4a, 0x7b, 0x47, 0x80, 0x0f, 0x38, 0x76, 0x82, 0xc5, 0xcf, 0xa1, 0x92, 0xe9, 0x98, 0xe1,
	0xd9, 0x5e, 0x50, 0x5e, 0x11, 0x61, 0x19, 0xf7, 0xa9, 0xc6, 0xa7, 0x49, 0x03, 0xb6, 0xb3, 0x95,
	0x93, 0xc4, 0x4b, 0x3a, 0x7b, 0x33, 0xc3, 0x44, 0x02, 0x22, 0x9f, 0xc0, 0x46, 0x67, 0x70, 0xa1,
	0xa5, 0xca, 0x3b, 0x2a, 0x93, 0x55, 0x2c, 0x83, 0xb5, 0xce, 0xe0, 0x22, 0xb9, 0x55, 0x89, 0x82,
	0xa9, 0xf4, 0xe1, 0x5a, 0xe6, 0xd6, 0x21, 0x25, 0x98, 0x79, 0x4d, 0x2f, 0x64, 0x57, 0xe0, 0x8f,
	0xe4, 0x19, 0x5c, 0x3d, 0xd7, 0xed, 0x41, 0x74, 0xc6, 0x4f, 0xb8, 0x0c, 0x0e, 0x59, 0x53, 0x85,
	0xc6, 0xf7, 0xf2, 0x9f, 0xe5, 0x94, 0xbf, 0xe5, 0x61, 0x3d, 0xeb, 0x9a, 0xc4, 0x4f, 0x10, 0x71,
	0xd3, 0xe2, 0x55, 0x9f, 0xc3, 0x95, 0x2e, 0xa0, 0x80, 0xd7, 0xfb, 0x27, 0xb0, 0x11, 0x5a, 0x8c,
	0xe1, 0x82, 0xbc, 0x90, 0x4a, 0xe6, 0x9a, 0x34, 0xb6, 0x35, 0x39, 0xdb, 0xf2, 0x42, 0x2a, 0x08,
	0x6c, 0xd3, 0x24, 0x8f, 0x60, 0x7d, 0x48, 0x29, 0x52, 0x99, 0x41, 0x95, 0xd5, 0x94, 0xca, 0x91,
	0x50, 0x78, 0x08, 0xab, 0x21, 0xd3, 0x5d, 0xb3, 0x73, 0x91, 0x7a, 0x81, 0x38, 0x8a, 0x56, 0xe4,
	0x44, 0x6c, 0xfc, 0x3e, 0x44, 0xa2, 0xd8, 0xae, 0x38, 0x96, 0x8a, 0x52, 0x7c, 0x14, 0xe3, 0xba,
	0xfc, 0xae, 0x10, 0x1b, 0x0c, 0xf1, 0x16, 0x5d, 0x54, 0x8b, 0x5c, 0x1c, 0x99, 0x0b, 0x89, 0x02,
	0x28, 0xd0, 0x62, 0xd4, 0x3c, 0xa2, 0x16, 0xb9, 0x50, 0x98, 0x0a, 0x95, 0xdf, 0xe4, 0xa1, 0x38,
	0x14, 0x58, 0xde, 0x07, 0x44, 0xaf, 0x44, 0x7e, 0x2b, 0xa2, 0x56, 0x40, 0x09, 0xd2, 0xdb, 0xe7,
	0xb0, 0x10, 0x0e, 0x2c, 0x86, 0x17, 0x57, 0x91, 0x2e, 0xe5, 0x92, 0x74, 0x9d, 0x0e, 0x2c, 0xc6,
	0xb3, 0x35, 0x1f, 0x8a, 0x07, 0xf2, 0x02, 0x8a, 0x11, 0x0a, 0x6d, 0x4e, 0xcf, 0x25, 0x96, 0xe4,
	0x0c, 0x3a, 0x3a, 0xc2, 0x59, 0x66, 0x47, 0x39, 0xcb, 0x33, 0xb8, 0x6e, 0xd8, 0x1e, 0xeb, 0xd3,
	0x50, 0x1b, 0xf8, 0xbd, 0x40, 0x37, 0xa9, 0xe6, 0x7b, 0xfe, 0xc0, 0x47, 0x4a, 0x2a, 0x38, 0xd9,
	0x86, 0x04, 0xbc, 0x14, 0xf3, 0x2d, 0x3e, 0xcd, 0xd9, 0x29, 0x83, 0xa5, 0xb4, 0xeb, 0x84, 0xc0,
	0x2c, 0xf2, 0x0b, 0x11, 0x09, 0x7c, 0xe6, 0xb2, 0x14, 0x57, 0xc2, 0x67, 0x7e, 0x67, 0xb0, 0x18,
	0x75, 0x30, 0xd0, 0x33, 0x18, 0xe8, 0x79, 0x3e, 0xe6, 0x89, 0xb8, 0x03, 0x4b, 0xe6, 0x20, 0x10,
	0x5c, 0x25, 0xa4, 0x86, 0xcc, 0xff, 0x62, 0x24, 0x3b, 0xa5, 0x86, 0xf2, 0xe7, 0x1c, 0xac, 0x8e,
	0x7d, 0x98, 0x18, 0xe2, 0xbf, 0xb9, 0xf7, 0xe7, 0xbf, 0x37, 0x00, 0x0c, 0x3f, 0xa6, 0x38, 0x92,
	0x42, 0x19, 0xbe, 0x20, 0x38, 0x97, 0x39, 0x7c, 0x0f, 0x56, 0x90, 0x1d, 0xa5, 0xb4, 0x85, 0xcf,
	0x48, 0x9a, 0x6a, 0xd2, 0x82, 0xf2, 0x29, 0x14, 0xa5, 0xcf, 0xaa, 0xee, 0xbe, 0xde, 0xeb, 0x91,
	0x65, 0xc8, 0xdb, 0xe7, 0x72, 0x67, 0xe7, 0xed, 0x73, 0x72, 0x0d, 0xe6, 0x3a, 0xbd, 0xd4, 0x65,
	0xe2, 0x6a, 0xa7, 0xf7, 0x32, 0xb0, 0x15, 0x2b, 0x5e, 0xab, 0xd0, 0x8b, 0x3e, 0x10, 0x45, 0x4b,
	0x0a, 0x74, 0xf7, 0x35, 0xbf, 0xfe, 0xa6, 0x3e, 0x02, 0xde, 0xbd, 0xf4, 0x10, 0x15, 0x66, 0xe2,
	0xef, 0x38, 0x89, 0x55, 0x65, 0x1b, 0x6e, 0x35, 0x28, 0xab, 0xda, 0x76, 0x1a, 0x19, 0xf7, 0x57,
	0x95, 0x9e, 0x29, 0xbf, 0xcc, 0xc3, 0xed, 0x4b, 0x21, 0xa1, 0x4f, 0xbe, 0x49, 0x3e, 0x33, 0x45,
	0xbe, 0x71, 0x16, 0x21, 0x5c, 0xfb, 0x72, 0x42, 0xfd, 0x5f, 0x6e, 0x73, 0xd8, 0xf5, 0x84, 0x57,
	0xbc, 0x19, 0x11, 0x57, 0x6c, 0xb8, 0x96, 0x09, 0xcd, 0xe8, 0xa3, 0xcf, 0x87, 0xfb, 0xe8, 0x87,
	0x53, 0xc4, 0x0c, 0xbf, 0xbe, 0xa6, 0x7a, 0xe9, 0x67, 0xb0, 0x15, 0x7d, 0x9b, 0x15, 0x77, 0x6c,
	0x6a, 0xe2, 0x0d, 0xf3, 0xdd, 0x1f, 0x8f, 0x7f, 0x06, 0x1b, 0xe3, 0xb7, 0x72, 0xcc, 0xec, 0x25,
	0xdf, 0x46, 0x1b, 0x43, 0x17, 0xdd, 0xfc, 0x7b, 0x7e, 0xbd, 0x49, 0xdd, 0x78, 0x7f, 0x97, 0x87,
	0x1b, 0x93, 0x1d, 0x0f, 0x7d, 0xf2, 0x8b, 0x1c, 0x94, 0x85, 0x17, 0x5d, 0x39, 0xad, 0x89, 0x37,
	0x27, 0x89, 0x3c, 0x9a, 0x98, 0xc8, 0x89, 0x66, 0x77, 0xc7, 0x67, 0xe2, 0x54, 0x5e, 0x63, 0x59,
	0x73, 0x95, 0x73, 0xa8, 0x4c, 0x56, 0xca, 0x48, 0xea, 0xde, 0x70, 0x52, 0x3f, 0x9a, 0x36, 0x3a,
	0x23, 0x99, 0x7d, 0xf8, 0xef, 0xe4, 0x3e, 0x53, 0x8d, 0xbf, 0xea, 0x28, 0x70, 0xeb, 0x55, 0x7d,
	0x7f, 0xbf, 0x79, 0xdc, 0xd0, 0xaa, 0xaf, 0xaa, 0xea, 0xbe, 0xd6, 0xfe, 0xba, 0x55, 0xd7, 0x5e,
	0x1e, 0x9f, 0xb6, 0xea, 0xb5, 0xe6, 0x8b, 0x66, 0x7d, 0xbf, 0x74, 0x85, 0xdc, 0x86, 0xad, 0x0c,
	0xcc, 0x41, 0xbd, 0xba, 0xff, 0xaa, 0x5e, 0x55, 0x4b, 0x39, 0xb2, 0x03, 0x1f, 0x64, 0x00, 0xbe,
	0xaa, 0x6a, 0x2f, 0x4e, 0x0e, 0x0f, 0x4f, 0x5e, 0x69, 0xb5, 0x83, 0xea, 0xf1, 0x71, 0xfd, 0xb0,
	0x94, 0x27, 0x1f, 0xc0, 0x76, 0x36, 0x72, 0xaf, 0x5a, 0xfb, 0xb2, 0xa1, 0x9e, 0xbc, 0x3c, 0xde,
	0x2f, 0xcd, 0x90, 0xfb, 0xa0, 0x64, 0xa0, 0x5e, 0xd4, 0xd1, 0xd8, 0x61, 0xb3, 0x51, 0x6d, 0xbf,
	0x54, 0xeb, 0xa5, 0xd9, 0x87, 0x7f, 0xca, 0xc1, 0x52, 0x9a, 0x45, 0x93, 0x9b, 0x70, 0x3d, 0x52,
	0x3c, 0xad, 0xd5, 0x8f, 0x47, 0x17, 0x72, 0x17, 0x6e, 0x0f, 0x4f, 0xef, 0xa9, 0xcd, 0xfd, 0xba,
	0xd6, 0x50, 0x4f, 0x4e, 0x8e, 0xb4, 0xfa, 0x71, 0xbb, 0xce, 0x17, 0x93, 0x5a, 0xad, 0x00, 0xd5,
	0x7f, 0xcc, 0xfd, 0x6f, 0xd4, 0x35, 0xb5, 0x79, 0xdc, 0x28, 0xe5, 0xc9, 0x16, 0x6c, 0x0e, 0x03,
	0x0e, 0x9a, 0x8d, 0x83, 0xc3, 0x66, 0xe3, 0xa0, 0x5d, 0x9a, 0x19, 0xf7, 0x80, 0x2f, 0xaa, 0xa5,
	0xb5, 0x0e, 0x4e, 0xda, 0x27, 0xa5, 0xd9, 0xc7, 0xbf, 0x9e, 0x05, 0x52, 0x13, 0xe9, 0x8b, 0x99,
	0x9a, 0xdb, 0x25, 0x3f, 0x85, 0xc5, 0xd4, 0x1f, 0x22, 0xe4, 0x83, 0xcb, 0xeb, 0x50, 0x6c, 0xc5,
	0xca, 0xbd, 0x29, 0x50, 0xa1, 0xaf, 0x5c, 0x21, 0xfd, 0xa1, 0x7f, 0x75, 0x70, 0x47, 0xee, 0xbc,
	0x53, 0x57, 0xfe, 0x59, 0x53, 0x79, 0x30, 0x25, 0x12, 0xdf, 0xf4, 0xab, 0x1c, 0x76, 0x8f, 0x49,
	0x6d, 0x8f, 0x3c, 0xf9, 0x16, 0x9d, 0xf2, 0xac, 0xf2, 0xf4, 0x5b, 0xf5, 0x57, 0xe5, 0x0a, 0xf9,
	0x79, 0x0e, 0xca, 0x93, 0x36, 0x2f, 0xf9, 0xee, 0xfb, 0x6e, 0xf6, 0xb3, 0xca, 0xe3, 0xf7, 0xef,
	0x0f, 0xca, 0x95, 0xca, 0xd6, 0xff, 0xfe, 0xfa, 0xaf, 0xf6, 0x06, 0xac, 0x67, 0xfd, 0xd5, 0xb7,
	0xf7, 0xd9, 0x4f, 0x3e, 0xed, 0x79, 0xb6, 0xee, 0xf6, 0x76, 0x9f, 0x3e, 0x66, 0x6c, 0xd7, 0xf0,
	0x9c, 0x47, 0xf8, 0xbf, 0x9d, 0xe1, 0xd9, 0x8f, 0x42, 0x1a, 0x9c, 0x5b, 0x06, 0x0d, 0x33, 0xff,
	0x24, 0xec, 0xcc, 0x21, 0xee, 0x93, 0xff, 0x07, 0x00, 0x00, 0xff, 0xff, 0x0f, 0x7b, 0xfa, 0xe0,
	0x67, 0x1c, 0x00, 0x00,
}
