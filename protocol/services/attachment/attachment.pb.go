// Code generated by protoc-gen-gogo.
// source: services/attachment/attachment.proto
// DO NOT EDIT!

/*
	Package Attachment is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/attachment/attachment.proto

	It has these top-level messages:
		StImMsgCache
		StFileProperty
		StFile
		AddFileReq
		GetFileReq
		GetFileResp
		MMAttributeT
		MMBufDataT
		MMBufLocationT
		LowLevelAddFileReq
		LowLevelAddFileResp
		LowLevelGetFileReq
		LowLevelGetFileResp
		DelFileReq
		DelFileResp
*/
package Attachment

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type StImMsgCache struct {
	TargetUid   uint32 `protobuf:"varint,1,opt,name=target_uid,json=targetUid" json:"target_uid"`
	TargetGroup uint32 `protobuf:"varint,2,opt,name=target_group,json=targetGroup" json:"target_group"`
	SvrMsgId    uint32 `protobuf:"varint,3,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
	Msg         []byte `protobuf:"bytes,4,req,name=msg" json:"msg"`
}

func (m *StImMsgCache) Reset()                    { *m = StImMsgCache{} }
func (m *StImMsgCache) String() string            { return proto.CompactTextString(m) }
func (*StImMsgCache) ProtoMessage()               {}
func (*StImMsgCache) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{0} }

func (m *StImMsgCache) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *StImMsgCache) GetTargetGroup() uint32 {
	if m != nil {
		return m.TargetGroup
	}
	return 0
}

func (m *StImMsgCache) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

func (m *StImMsgCache) GetMsg() []byte {
	if m != nil {
		return m.Msg
	}
	return nil
}

type StFileProperty struct {
	SvrTime         uint32          `protobuf:"varint,1,opt,name=svr_time,json=svrTime" json:"svr_time"`
	ClientProp      []byte          `protobuf:"bytes,2,opt,name=client_prop,json=clientProp" json:"client_prop"`
	Status          uint32          `protobuf:"varint,3,opt,name=status" json:"status"`
	CacheMsgList    []*StImMsgCache `protobuf:"bytes,4,rep,name=cache_msg_list,json=cacheMsgList" json:"cache_msg_list,omitempty"`
	Type            uint32          `protobuf:"varint,5,req,name=type" json:"type"`
	TargetAccount   string          `protobuf:"bytes,6,req,name=target_account,json=targetAccount" json:"target_account"`
	FromAccount     string          `protobuf:"bytes,7,req,name=from_account,json=fromAccount" json:"from_account"`
	NewFileLocation *MMBufLocationT `protobuf:"bytes,8,opt,name=new_file_location,json=newFileLocation" json:"new_file_location,omitempty"`
}

func (m *StFileProperty) Reset()                    { *m = StFileProperty{} }
func (m *StFileProperty) String() string            { return proto.CompactTextString(m) }
func (*StFileProperty) ProtoMessage()               {}
func (*StFileProperty) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{1} }

func (m *StFileProperty) GetSvrTime() uint32 {
	if m != nil {
		return m.SvrTime
	}
	return 0
}

func (m *StFileProperty) GetClientProp() []byte {
	if m != nil {
		return m.ClientProp
	}
	return nil
}

func (m *StFileProperty) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StFileProperty) GetCacheMsgList() []*StImMsgCache {
	if m != nil {
		return m.CacheMsgList
	}
	return nil
}

func (m *StFileProperty) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *StFileProperty) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *StFileProperty) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *StFileProperty) GetNewFileLocation() *MMBufLocationT {
	if m != nil {
		return m.NewFileLocation
	}
	return nil
}

type StFile struct {
	Data []byte          `protobuf:"bytes,1,req,name=data" json:"data"`
	Prop *StFileProperty `protobuf:"bytes,2,req,name=prop" json:"prop,omitempty"`
}

func (m *StFile) Reset()                    { *m = StFile{} }
func (m *StFile) String() string            { return proto.CompactTextString(m) }
func (*StFile) ProtoMessage()               {}
func (*StFile) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{2} }

func (m *StFile) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *StFile) GetProp() *StFileProperty {
	if m != nil {
		return m.Prop
	}
	return nil
}

type AddFileReq struct {
	Key  string  `protobuf:"bytes,1,req,name=key" json:"key"`
	File *StFile `protobuf:"bytes,2,req,name=file" json:"file,omitempty"`
	Ttl  uint32  `protobuf:"varint,3,req,name=ttl" json:"ttl"`
}

func (m *AddFileReq) Reset()                    { *m = AddFileReq{} }
func (m *AddFileReq) String() string            { return proto.CompactTextString(m) }
func (*AddFileReq) ProtoMessage()               {}
func (*AddFileReq) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{3} }

func (m *AddFileReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *AddFileReq) GetFile() *StFile {
	if m != nil {
		return m.File
	}
	return nil
}

func (m *AddFileReq) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

type GetFileReq struct {
	Key string `protobuf:"bytes,1,req,name=key" json:"key"`
}

func (m *GetFileReq) Reset()                    { *m = GetFileReq{} }
func (m *GetFileReq) String() string            { return proto.CompactTextString(m) }
func (*GetFileReq) ProtoMessage()               {}
func (*GetFileReq) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{4} }

func (m *GetFileReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type GetFileResp struct {
	File *StFile `protobuf:"bytes,1,opt,name=file" json:"file,omitempty"`
}

func (m *GetFileResp) Reset()                    { *m = GetFileResp{} }
func (m *GetFileResp) String() string            { return proto.CompactTextString(m) }
func (*GetFileResp) ProtoMessage()               {}
func (*GetFileResp) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{5} }

func (m *GetFileResp) GetFile() *StFile {
	if m != nil {
		return m.File
	}
	return nil
}

type MMAttributeT struct {
	IDataLen uint32 `protobuf:"varint,1,req,name=iDataLen" json:"iDataLen"`
}

func (m *MMAttributeT) Reset()                    { *m = MMAttributeT{} }
func (m *MMAttributeT) String() string            { return proto.CompactTextString(m) }
func (*MMAttributeT) ProtoMessage()               {}
func (*MMAttributeT) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{6} }

func (m *MMAttributeT) GetIDataLen() uint32 {
	if m != nil {
		return m.IDataLen
	}
	return 0
}

type MMBufDataT struct {
	TAttribute *MMAttributeT                 `protobuf:"bytes,1,req,name=tAttribute" json:"tAttribute,omitempty"`
	TData      *tlvpickle.SKBuiltinBuffer_PB `protobuf:"bytes,2,req,name=tData" json:"tData,omitempty"`
}

func (m *MMBufDataT) Reset()                    { *m = MMBufDataT{} }
func (m *MMBufDataT) String() string            { return proto.CompactTextString(m) }
func (*MMBufDataT) ProtoMessage()               {}
func (*MMBufDataT) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{7} }

func (m *MMBufDataT) GetTAttribute() *MMAttributeT {
	if m != nil {
		return m.TAttribute
	}
	return nil
}

func (m *MMBufDataT) GetTData() *tlvpickle.SKBuiltinBuffer_PB {
	if m != nil {
		return m.TData
	}
	return nil
}

type MMBufLocationT struct {
	IOffset    uint32 `protobuf:"varint,1,req,name=iOffset" json:"iOffset"`
	IFileIndex uint32 `protobuf:"varint,2,req,name=iFileIndex" json:"iFileIndex"`
}

func (m *MMBufLocationT) Reset()                    { *m = MMBufLocationT{} }
func (m *MMBufLocationT) String() string            { return proto.CompactTextString(m) }
func (*MMBufLocationT) ProtoMessage()               {}
func (*MMBufLocationT) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{8} }

func (m *MMBufLocationT) GetIOffset() uint32 {
	if m != nil {
		return m.IOffset
	}
	return 0
}

func (m *MMBufLocationT) GetIFileIndex() uint32 {
	if m != nil {
		return m.IFileIndex
	}
	return 0
}

type LowLevelAddFileReq struct {
	Data []byte `protobuf:"bytes,1,req,name=data" json:"data"`
}

func (m *LowLevelAddFileReq) Reset()                    { *m = LowLevelAddFileReq{} }
func (m *LowLevelAddFileReq) String() string            { return proto.CompactTextString(m) }
func (*LowLevelAddFileReq) ProtoMessage()               {}
func (*LowLevelAddFileReq) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{9} }

func (m *LowLevelAddFileReq) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type LowLevelAddFileResp struct {
	FileLocation *MMBufLocationT `protobuf:"bytes,1,req,name=file_location,json=fileLocation" json:"file_location,omitempty"`
}

func (m *LowLevelAddFileResp) Reset()                    { *m = LowLevelAddFileResp{} }
func (m *LowLevelAddFileResp) String() string            { return proto.CompactTextString(m) }
func (*LowLevelAddFileResp) ProtoMessage()               {}
func (*LowLevelAddFileResp) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{10} }

func (m *LowLevelAddFileResp) GetFileLocation() *MMBufLocationT {
	if m != nil {
		return m.FileLocation
	}
	return nil
}

type LowLevelGetFileReq struct {
	FileLocation *MMBufLocationT `protobuf:"bytes,1,req,name=file_location,json=fileLocation" json:"file_location,omitempty"`
}

func (m *LowLevelGetFileReq) Reset()                    { *m = LowLevelGetFileReq{} }
func (m *LowLevelGetFileReq) String() string            { return proto.CompactTextString(m) }
func (*LowLevelGetFileReq) ProtoMessage()               {}
func (*LowLevelGetFileReq) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{11} }

func (m *LowLevelGetFileReq) GetFileLocation() *MMBufLocationT {
	if m != nil {
		return m.FileLocation
	}
	return nil
}

type LowLevelGetFileResp struct {
	Data []byte `protobuf:"bytes,1,req,name=data" json:"data"`
}

func (m *LowLevelGetFileResp) Reset()                    { *m = LowLevelGetFileResp{} }
func (m *LowLevelGetFileResp) String() string            { return proto.CompactTextString(m) }
func (*LowLevelGetFileResp) ProtoMessage()               {}
func (*LowLevelGetFileResp) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{12} }

func (m *LowLevelGetFileResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type DelFileReq struct {
	Key string `protobuf:"bytes,1,req,name=key" json:"key"`
}

func (m *DelFileReq) Reset()                    { *m = DelFileReq{} }
func (m *DelFileReq) String() string            { return proto.CompactTextString(m) }
func (*DelFileReq) ProtoMessage()               {}
func (*DelFileReq) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{13} }

func (m *DelFileReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type DelFileResp struct {
}

func (m *DelFileResp) Reset()                    { *m = DelFileResp{} }
func (m *DelFileResp) String() string            { return proto.CompactTextString(m) }
func (*DelFileResp) ProtoMessage()               {}
func (*DelFileResp) Descriptor() ([]byte, []int) { return fileDescriptorAttachment, []int{14} }

func init() {
	proto.RegisterType((*StImMsgCache)(nil), "Attachment.stImMsgCache")
	proto.RegisterType((*StFileProperty)(nil), "Attachment.stFileProperty")
	proto.RegisterType((*StFile)(nil), "Attachment.stFile")
	proto.RegisterType((*AddFileReq)(nil), "Attachment.AddFileReq")
	proto.RegisterType((*GetFileReq)(nil), "Attachment.GetFileReq")
	proto.RegisterType((*GetFileResp)(nil), "Attachment.GetFileResp")
	proto.RegisterType((*MMAttributeT)(nil), "Attachment.MMAttribute_t")
	proto.RegisterType((*MMBufDataT)(nil), "Attachment.MMBufData_t")
	proto.RegisterType((*MMBufLocationT)(nil), "Attachment.MMBufLocation_t")
	proto.RegisterType((*LowLevelAddFileReq)(nil), "Attachment.LowLevelAddFileReq")
	proto.RegisterType((*LowLevelAddFileResp)(nil), "Attachment.LowLevelAddFileResp")
	proto.RegisterType((*LowLevelGetFileReq)(nil), "Attachment.LowLevelGetFileReq")
	proto.RegisterType((*LowLevelGetFileResp)(nil), "Attachment.LowLevelGetFileResp")
	proto.RegisterType((*DelFileReq)(nil), "Attachment.DelFileReq")
	proto.RegisterType((*DelFileResp)(nil), "Attachment.DelFileResp")
}
func (m *StImMsgCache) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StImMsgCache) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(m.TargetGroup))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(m.SvrMsgId))
	if m.Msg != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(len(m.Msg)))
		i += copy(dAtA[i:], m.Msg)
	}
	return i, nil
}

func (m *StFileProperty) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StFileProperty) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(m.SvrTime))
	if m.ClientProp != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(len(m.ClientProp)))
		i += copy(dAtA[i:], m.ClientProp)
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(m.Status))
	if len(m.CacheMsgList) > 0 {
		for _, msg := range m.CacheMsgList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintAttachment(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x32
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(len(m.FromAccount)))
	i += copy(dAtA[i:], m.FromAccount)
	if m.NewFileLocation != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(m.NewFileLocation.Size()))
		n1, err := m.NewFileLocation.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *StFile) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StFile) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	if m.Prop == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("prop")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(m.Prop.Size()))
		n2, err := m.Prop.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *AddFileReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFileReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	if m.File == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("file")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(m.File.Size()))
		n3, err := m.File.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(m.Ttl))
	return i, nil
}

func (m *GetFileReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFileReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	return i, nil
}

func (m *GetFileResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFileResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.File != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(m.File.Size()))
		n4, err := m.File.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *MMAttributeT) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MMAttributeT) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(m.IDataLen))
	return i, nil
}

func (m *MMBufDataT) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MMBufDataT) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TAttribute == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tAttribute")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(m.TAttribute.Size()))
		n5, err := m.TAttribute.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.TData == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tData")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(m.TData.Size()))
		n6, err := m.TData.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *MMBufLocationT) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MMBufLocationT) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(m.IOffset))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(m.IFileIndex))
	return i, nil
}

func (m *LowLevelAddFileReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LowLevelAddFileReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	return i, nil
}

func (m *LowLevelAddFileResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LowLevelAddFileResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FileLocation == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("file_location")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(m.FileLocation.Size()))
		n7, err := m.FileLocation.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *LowLevelGetFileReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LowLevelGetFileReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.FileLocation == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("file_location")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(m.FileLocation.Size()))
		n8, err := m.FileLocation.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *LowLevelGetFileResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LowLevelGetFileResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAttachment(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	return i, nil
}

func (m *DelFileReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelFileReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintAttachment(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	return i, nil
}

func (m *DelFileResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelFileResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Attachment(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Attachment(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAttachment(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *StImMsgCache) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAttachment(uint64(m.TargetUid))
	n += 1 + sovAttachment(uint64(m.TargetGroup))
	n += 1 + sovAttachment(uint64(m.SvrMsgId))
	if m.Msg != nil {
		l = len(m.Msg)
		n += 1 + l + sovAttachment(uint64(l))
	}
	return n
}

func (m *StFileProperty) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAttachment(uint64(m.SvrTime))
	if m.ClientProp != nil {
		l = len(m.ClientProp)
		n += 1 + l + sovAttachment(uint64(l))
	}
	n += 1 + sovAttachment(uint64(m.Status))
	if len(m.CacheMsgList) > 0 {
		for _, e := range m.CacheMsgList {
			l = e.Size()
			n += 1 + l + sovAttachment(uint64(l))
		}
	}
	n += 1 + sovAttachment(uint64(m.Type))
	l = len(m.TargetAccount)
	n += 1 + l + sovAttachment(uint64(l))
	l = len(m.FromAccount)
	n += 1 + l + sovAttachment(uint64(l))
	if m.NewFileLocation != nil {
		l = m.NewFileLocation.Size()
		n += 1 + l + sovAttachment(uint64(l))
	}
	return n
}

func (m *StFile) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovAttachment(uint64(l))
	}
	if m.Prop != nil {
		l = m.Prop.Size()
		n += 1 + l + sovAttachment(uint64(l))
	}
	return n
}

func (m *AddFileReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovAttachment(uint64(l))
	if m.File != nil {
		l = m.File.Size()
		n += 1 + l + sovAttachment(uint64(l))
	}
	n += 1 + sovAttachment(uint64(m.Ttl))
	return n
}

func (m *GetFileReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovAttachment(uint64(l))
	return n
}

func (m *GetFileResp) Size() (n int) {
	var l int
	_ = l
	if m.File != nil {
		l = m.File.Size()
		n += 1 + l + sovAttachment(uint64(l))
	}
	return n
}

func (m *MMAttributeT) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAttachment(uint64(m.IDataLen))
	return n
}

func (m *MMBufDataT) Size() (n int) {
	var l int
	_ = l
	if m.TAttribute != nil {
		l = m.TAttribute.Size()
		n += 1 + l + sovAttachment(uint64(l))
	}
	if m.TData != nil {
		l = m.TData.Size()
		n += 1 + l + sovAttachment(uint64(l))
	}
	return n
}

func (m *MMBufLocationT) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAttachment(uint64(m.IOffset))
	n += 1 + sovAttachment(uint64(m.IFileIndex))
	return n
}

func (m *LowLevelAddFileReq) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovAttachment(uint64(l))
	}
	return n
}

func (m *LowLevelAddFileResp) Size() (n int) {
	var l int
	_ = l
	if m.FileLocation != nil {
		l = m.FileLocation.Size()
		n += 1 + l + sovAttachment(uint64(l))
	}
	return n
}

func (m *LowLevelGetFileReq) Size() (n int) {
	var l int
	_ = l
	if m.FileLocation != nil {
		l = m.FileLocation.Size()
		n += 1 + l + sovAttachment(uint64(l))
	}
	return n
}

func (m *LowLevelGetFileResp) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovAttachment(uint64(l))
	}
	return n
}

func (m *DelFileReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovAttachment(uint64(l))
	return n
}

func (m *DelFileResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovAttachment(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAttachment(x uint64) (n int) {
	return sovAttachment(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *StImMsgCache) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stImMsgCache: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stImMsgCache: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetGroup", wireType)
			}
			m.TargetGroup = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetGroup |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msg = append(m.Msg[:0], dAtA[iNdEx:postIndex]...)
			if m.Msg == nil {
				m.Msg = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("svr_msg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StFileProperty) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stFileProperty: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stFileProperty: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SvrTime", wireType)
			}
			m.SvrTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientProp", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientProp = append(m.ClientProp[:0], dAtA[iNdEx:postIndex]...)
			if m.ClientProp == nil {
				m.ClientProp = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CacheMsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CacheMsgList = append(m.CacheMsgList, &StImMsgCache{})
			if err := m.CacheMsgList[len(m.CacheMsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FromAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewFileLocation", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NewFileLocation == nil {
				m.NewFileLocation = &MMBufLocationT{}
			}
			if err := m.NewFileLocation.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StFile) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stFile: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stFile: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Prop", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Prop == nil {
				m.Prop = &StFileProperty{}
			}
			if err := m.Prop.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("data")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("prop")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFileReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFileReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFileReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field File", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.File == nil {
				m.File = &StFile{}
			}
			if err := m.File.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("file")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ttl")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFileReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFileReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFileReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFileResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFileResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFileResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field File", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.File == nil {
				m.File = &StFile{}
			}
			if err := m.File.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MMAttributeT) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MMAttribute_t: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MMAttribute_t: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IDataLen", wireType)
			}
			m.IDataLen = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IDataLen |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("iDataLen")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MMBufDataT) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MMBufData_t: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MMBufData_t: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TAttribute", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TAttribute == nil {
				m.TAttribute = &MMAttributeT{}
			}
			if err := m.TAttribute.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TData", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TData == nil {
				m.TData = &tlvpickle.SKBuiltinBuffer_PB{}
			}
			if err := m.TData.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tAttribute")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tData")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MMBufLocationT) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MMBufLocation_t: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MMBufLocation_t: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IOffset", wireType)
			}
			m.IOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IOffset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IFileIndex", wireType)
			}
			m.IFileIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IFileIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("iOffset")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("iFileIndex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LowLevelAddFileReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LowLevelAddFileReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LowLevelAddFileReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LowLevelAddFileResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LowLevelAddFileResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LowLevelAddFileResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FileLocation", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FileLocation == nil {
				m.FileLocation = &MMBufLocationT{}
			}
			if err := m.FileLocation.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("file_location")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LowLevelGetFileReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LowLevelGetFileReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LowLevelGetFileReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FileLocation", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FileLocation == nil {
				m.FileLocation = &MMBufLocationT{}
			}
			if err := m.FileLocation.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("file_location")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LowLevelGetFileResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LowLevelGetFileResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LowLevelGetFileResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelFileReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelFileReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelFileReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAttachment
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelFileResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelFileResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelFileResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAttachment(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAttachment
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAttachment(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAttachment
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAttachment
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAttachment
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAttachment
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAttachment(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAttachment = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAttachment   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/attachment/attachment.proto", fileDescriptorAttachment) }

var fileDescriptorAttachment = []byte{
	// 919 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x54, 0xcd, 0x6e, 0x1b, 0x55,
	0x14, 0xee, 0xd8, 0x4e, 0x9c, 0x9e, 0xb1, 0x13, 0xb8, 0x48, 0x65, 0x6a, 0x8a, 0x33, 0x1a, 0x0c,
	0x58, 0x42, 0x76, 0x84, 0x2b, 0x16, 0x8d, 0x22, 0x8b, 0x98, 0x42, 0x14, 0x61, 0x8b, 0x68, 0xa0,
	0x54, 0x62, 0x33, 0x9a, 0xce, 0x5c, 0xbb, 0x57, 0x9e, 0x3f, 0xe6, 0x1e, 0x3b, 0x18, 0x09, 0x89,
	0x1d, 0x15, 0x2b, 0xc4, 0x8a, 0x07, 0xc8, 0x8b, 0xb0, 0xeb, 0x92, 0x27, 0x40, 0x28, 0x6c, 0xb2,
	0xe3, 0x15, 0xd0, 0x9d, 0x3f, 0xdf, 0x69, 0xfe, 0x16, 0xdd, 0x79, 0xbe, 0xf3, 0xdd, 0xf3, 0x9d,
	0xf3, 0x9d, 0x73, 0x0c, 0x1d, 0x4e, 0xe3, 0x25, 0x73, 0x28, 0xdf, 0xb3, 0x11, 0x6d, 0xe7, 0xb9,
	0x4f, 0x03, 0x94, 0x7e, 0xf6, 0xa3, 0x38, 0xc4, 0x90, 0xc0, 0x61, 0x81, 0xb4, 0x3a, 0x4e, 0xe8,
	0xfb, 0x61, 0xb0, 0x87, 0xde, 0x32, 0x62, 0xce, 0xdc, 0xa3, 0x7b, 0x7c, 0xfe, 0x6c, 0xc1, 0x3c,
	0x64, 0x01, 0xae, 0x22, 0x9a, 0xbe, 0x30, 0xfe, 0x50, 0xa0, 0xc1, 0xf1, 0xd8, 0x9f, 0xf0, 0xd9,
	0x67, 0xb6, 0xf3, 0x9c, 0x92, 0xf7, 0x00, 0xd0, 0x8e, 0x67, 0x14, 0xad, 0x05, 0x73, 0x35, 0x45,
	0x57, 0xba, 0xcd, 0x51, 0xed, 0xe5, 0xdf, 0xbb, 0x77, 0xcc, 0xbb, 0x29, 0xfe, 0x84, 0xb9, 0xe4,
	0x43, 0x68, 0x64, 0xa4, 0x59, 0x1c, 0x2e, 0x22, 0xad, 0x22, 0xd1, 0xd4, 0x34, 0x72, 0x24, 0x02,
	0xc4, 0x00, 0xe0, 0xcb, 0xd8, 0xf2, 0xf9, 0xcc, 0x62, 0xae, 0x56, 0xd5, 0x2b, 0x05, 0x6d, 0x8b,
	0x2f, 0xe3, 0x09, 0x9f, 0x1d, 0xbb, 0xe4, 0x1e, 0x54, 0x7d, 0x3e, 0xd3, 0x6a, 0x7a, 0xa5, 0xdb,
	0xc8, 0x82, 0x02, 0x30, 0xfe, 0xab, 0xc0, 0x36, 0xc7, 0x2f, 0x98, 0x47, 0x4f, 0xe2, 0x30, 0xa2,
	0x31, 0xae, 0xc8, 0x2e, 0x88, 0x67, 0x16, 0x32, 0x9f, 0x96, 0x4a, 0xab, 0xf3, 0x65, 0xfc, 0x0d,
	0xf3, 0x29, 0x79, 0x1f, 0x54, 0xc7, 0x63, 0x34, 0x40, 0x2b, 0x8a, 0xc3, 0xb4, 0xae, 0x3c, 0x27,
	0xa4, 0x01, 0x91, 0x8b, 0x3c, 0x80, 0x4d, 0x8e, 0x36, 0x2e, 0xb8, 0x56, 0x95, 0xb2, 0x64, 0x18,
	0x19, 0xc2, 0xb6, 0x23, 0xbc, 0x48, 0xca, 0xf6, 0x18, 0x47, 0xad, 0xa6, 0x57, 0xbb, 0xea, 0x40,
	0xeb, 0xaf, 0xed, 0xed, 0xcb, 0xa6, 0x99, 0x8d, 0x84, 0x3f, 0xe1, 0xb3, 0x31, 0xe3, 0x48, 0x34,
	0xa8, 0x09, 0x87, 0xb5, 0x0d, 0xa9, 0xdd, 0x04, 0x21, 0x1f, 0xc1, 0x76, 0xe6, 0x9b, 0xed, 0x38,
	0xe1, 0x22, 0x40, 0x6d, 0x53, 0xaf, 0x74, 0xef, 0x66, 0x9c, 0x66, 0x1a, 0x3b, 0x4c, 0x43, 0xc2,
	0xe4, 0x69, 0x1c, 0xfa, 0x05, 0xb5, 0x2e, 0x51, 0x55, 0x11, 0xc9, 0x89, 0x47, 0xf0, 0x66, 0x40,
	0x4f, 0xad, 0x29, 0xf3, 0xa8, 0xe5, 0x85, 0x8e, 0x8d, 0x2c, 0x0c, 0xb4, 0x2d, 0x5d, 0xe9, 0xaa,
	0x83, 0x77, 0xe4, 0x92, 0x27, 0x93, 0xd1, 0x62, 0x3a, 0xce, 0x08, 0x16, 0x9a, 0x3b, 0x01, 0x3d,
	0x15, 0xf6, 0xe6, 0x90, 0x61, 0x0a, 0x5b, 0x04, 0x22, 0x5a, 0x70, 0x6d, 0xb4, 0x35, 0x45, 0x1a,
	0x4a, 0x82, 0x90, 0x3e, 0xd4, 0x32, 0x6b, 0x2b, 0x5d, 0x75, 0xd0, 0x2a, 0x5b, 0x22, 0x0f, 0xcb,
	0x4c, 0x78, 0x86, 0x0b, 0x70, 0xe8, 0xba, 0x22, 0x60, 0xd2, 0xef, 0xc5, 0xac, 0xe7, 0x74, 0x95,
	0xa4, 0xcd, 0x5b, 0x11, 0x00, 0xf9, 0x00, 0x6a, 0xa2, 0xfc, 0x2c, 0x2b, 0xb9, 0x9c, 0xd5, 0x4c,
	0xe2, 0xe2, 0x3d, 0xa2, 0x57, 0x5a, 0x24, 0x01, 0x18, 0x1d, 0x80, 0x23, 0x8a, 0xb7, 0xa8, 0x18,
	0x9f, 0x80, 0x5a, 0xb0, 0x78, 0x54, 0x88, 0x2a, 0x89, 0x55, 0xd7, 0x8a, 0x1a, 0x1f, 0x43, 0x73,
	0x32, 0x39, 0x44, 0x8c, 0xd9, 0xb3, 0x05, 0x52, 0x0b, 0x89, 0x0e, 0x5b, 0xec, 0xb1, 0x8d, 0xf6,
	0x98, 0x06, 0x89, 0x48, 0xb1, 0xd3, 0x39, 0x6a, 0xfc, 0x04, 0x6a, 0xe2, 0xb6, 0xf8, 0xb6, 0x90,
	0x3c, 0x02, 0xc0, 0x22, 0x41, 0xf2, 0x44, 0x1d, 0xdc, 0x2f, 0x8f, 0x46, 0xca, 0x6f, 0x4a, 0x64,
	0xf2, 0x10, 0x36, 0x50, 0x64, 0xc9, 0xac, 0x79, 0xb7, 0x5f, 0xdc, 0x73, 0xff, 0xeb, 0x2f, 0x47,
	0xe9, 0x3d, 0x8f, 0x16, 0xd3, 0x29, 0x8d, 0xad, 0x93, 0x91, 0x99, 0x72, 0x8d, 0xa7, 0xb0, 0xf3,
	0xca, 0xb0, 0x49, 0x1b, 0xea, 0xec, 0xab, 0xe9, 0x94, 0x53, 0x2c, 0x95, 0x9c, 0x83, 0xa4, 0x03,
	0xc0, 0x44, 0xcf, 0xc7, 0x81, 0x4b, 0x7f, 0x48, 0xc4, 0x72, 0x8a, 0x84, 0x1b, 0x7d, 0x20, 0xe3,
	0xf0, 0x74, 0x4c, 0x97, 0xd4, 0x93, 0xa6, 0x7a, 0xed, 0xb6, 0x18, 0x4f, 0xe1, 0xad, 0x4b, 0x7c,
	0x1e, 0x91, 0x4f, 0xa1, 0x59, 0xde, 0xd6, 0xd4, 0x92, 0x1b, 0xb7, 0xb5, 0x31, 0x95, 0x57, 0xf5,
	0xdb, 0x75, 0x21, 0xd2, 0xe0, 0x5f, 0x3f, 0xef, 0xde, 0xba, 0x60, 0x79, 0x55, 0xae, 0xef, 0xb0,
	0x03, 0xf0, 0x98, 0x7a, 0xb7, 0x6d, 0x5e, 0x13, 0xd4, 0x82, 0xc5, 0xa3, 0xc1, 0x9f, 0x35, 0x90,
	0xfe, 0xaa, 0xc9, 0x8f, 0x50, 0xcf, 0xdc, 0x21, 0xf7, 0xe4, 0x52, 0xd7, 0x16, 0xb7, 0x1e, 0x5c,
	0x35, 0xf7, 0xcf, 0xfd, 0x08, 0x57, 0xd6, 0xc9, 0xc8, 0x78, 0xf4, 0xf3, 0xd9, 0x45, 0x55, 0xf9,
	0xf5, 0xec, 0xa2, 0xba, 0x39, 0xdf, 0x77, 0xf7, 0x71, 0xff, 0xf7, 0xb3, 0x8b, 0x6a, 0xa7, 0x37,
	0xd7, 0x0f, 0xe6, 0x74, 0x65, 0x05, 0xb6, 0x4f, 0x87, 0x7a, 0xcf, 0xd5, 0x0f, 0x12, 0x73, 0x5c,
	0x16, 0x0f, 0xf5, 0x1e, 0xea, 0x07, 0x88, 0xde, 0x90, 0x7c, 0x07, 0xf5, 0xac, 0xd1, 0xb2, 0xf6,
	0xda, 0xd5, 0xd6, 0xdb, 0x57, 0xe2, 0x3c, 0x32, 0xda, 0x42, 0xb6, 0x22, 0x64, 0x2b, 0xf3, 0x44,
	0xb2, 0x59, 0x92, 0x24, 0x31, 0xec, 0xbc, 0x32, 0x7d, 0xd2, 0x96, 0x73, 0x5d, 0x5e, 0xa5, 0xd6,
	0xee, 0x8d, 0xf1, 0x5c, 0xb3, 0x9a, 0x68, 0xba, 0x99, 0xa6, 0xdc, 0x18, 0xf9, 0x45, 0x59, 0x8b,
	0xe6, 0x8d, 0x5d, 0x29, 0x2a, 0x35, 0xb8, 0x7b, 0x63, 0x9c, 0x47, 0xc6, 0x43, 0x21, 0x5a, 0x13,
	0xa2, 0x35, 0xb6, 0x1f, 0x26, 0xb2, 0x7a, 0x8f, 0xa5, 0xb2, 0x3a, 0x13, 0x97, 0x31, 0xd4, 0x7b,
	0x61, 0xf6, 0x1d, 0x26, 0x07, 0x35, 0x24, 0x4f, 0xa0, 0x9e, 0xcd, 0xbc, 0xec, 0xec, 0x7a, 0x5d,
	0xca, 0xce, 0x4a, 0x0b, 0x62, 0xdc, 0x17, 0x82, 0x1b, 0x92, 0xb3, 0x5b, 0x99, 0xb3, 0xc3, 0xd6,
	0xe6, 0x8b, 0xb3, 0x8b, 0xea, 0x8b, 0x78, 0xf4, 0xc6, 0xcb, 0xf3, 0xb6, 0xf2, 0xd7, 0x79, 0x5b,
	0xf9, 0xe7, 0xbc, 0xad, 0xfc, 0xf6, 0x6f, 0xfb, 0xce, 0xff, 0x01, 0x00, 0x00, 0xff, 0xff, 0x6c,
	0x5a, 0x91, 0xbc, 0x24, 0x08, 0x00, 0x00,
}
