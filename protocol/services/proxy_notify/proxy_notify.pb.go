// Code generated by protoc-gen-gogo.
// source: src/proto/svr_pbfile/proxy_notify.proto
// DO NOT EDIT!

/*
	Package proxy_notify is a generated protocol buffer package.

	It is generated from these files:
		src/proto/svr_pbfile/proxy_notify.proto

	It has these top-level messages:
		PushCmd
		NotifyUser
		MulticastAccount
		PushBizData
		PushWithUserList
		PushWithMulticastAccount
		BatchNotifyInfo
		NotifyWithDataInfo
		NotifyWithDataByGroupId
		TransmissionPacket
		Empty
		NotifyReq
		NotifyPayload
*/
package proxy_notify

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import UserGroupRelationReg "golang.52tt.com/protocol/services/proxy_notify/user_group_relation"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type PushCmd_Values int32

const (
	PushCmd_PUSH_CMD_NOTIFY                      PushCmd_Values = 1
	PushCmd_PUSH_CMD_KICKOUT                     PushCmd_Values = 2
	PushCmd_PUSH_CMD_PUSH                        PushCmd_Values = 3
	PushCmd_PUSH_CMD_BATCHPUSH                   PushCmd_Values = 4
	PushCmd_PUSH_CMD_REGEVENT                    PushCmd_Values = 5
	PushCmd_PUSH_CMD_BATCH_REGEVENT              PushCmd_Values = 6
	PushCmd_PUSH_CMD_PUSH_BYGROUPID              PushCmd_Values = 7
	PushCmd_PUSH_CMD_NOTIFY_BYGROUPID            PushCmd_Values = 8
	PushCmd_PUSH_CMD_PUSH_BYCHANNELID            PushCmd_Values = 9
	PushCmd_PUSH_CMD_NOTIFY_WITH_DATA            PushCmd_Values = 10
	PushCmd_PUSH_CMD_NOTIFY_WITH_DATA_BY_GROUPID PushCmd_Values = 11
	PushCmd_PUSH_CMD_PUSH_WITH_USER_LIST         PushCmd_Values = 12
	PushCmd_PUSH_CMD_PUSH_WITH_MULTICAST_ACCOUNT PushCmd_Values = 13
	PushCmd_PUSH_CMD_PUSH_V2_CH_CAST_MSG         PushCmd_Values = 14
	PushCmd_PUSH_CMD_PUSH_V2_UN_CAST_MSG         PushCmd_Values = 15
	PushCmd_PUSH_CMD_PUSH_V2_BC_CAST_MSG         PushCmd_Values = 16
)

var PushCmd_Values_name = map[int32]string{
	1:  "PUSH_CMD_NOTIFY",
	2:  "PUSH_CMD_KICKOUT",
	3:  "PUSH_CMD_PUSH",
	4:  "PUSH_CMD_BATCHPUSH",
	5:  "PUSH_CMD_REGEVENT",
	6:  "PUSH_CMD_BATCH_REGEVENT",
	7:  "PUSH_CMD_PUSH_BYGROUPID",
	8:  "PUSH_CMD_NOTIFY_BYGROUPID",
	9:  "PUSH_CMD_PUSH_BYCHANNELID",
	10: "PUSH_CMD_NOTIFY_WITH_DATA",
	11: "PUSH_CMD_NOTIFY_WITH_DATA_BY_GROUPID",
	12: "PUSH_CMD_PUSH_WITH_USER_LIST",
	13: "PUSH_CMD_PUSH_WITH_MULTICAST_ACCOUNT",
	14: "PUSH_CMD_PUSH_V2_CH_CAST_MSG",
	15: "PUSH_CMD_PUSH_V2_UN_CAST_MSG",
	16: "PUSH_CMD_PUSH_V2_BC_CAST_MSG",
}
var PushCmd_Values_value = map[string]int32{
	"PUSH_CMD_NOTIFY":                      1,
	"PUSH_CMD_KICKOUT":                     2,
	"PUSH_CMD_PUSH":                        3,
	"PUSH_CMD_BATCHPUSH":                   4,
	"PUSH_CMD_REGEVENT":                    5,
	"PUSH_CMD_BATCH_REGEVENT":              6,
	"PUSH_CMD_PUSH_BYGROUPID":              7,
	"PUSH_CMD_NOTIFY_BYGROUPID":            8,
	"PUSH_CMD_PUSH_BYCHANNELID":            9,
	"PUSH_CMD_NOTIFY_WITH_DATA":            10,
	"PUSH_CMD_NOTIFY_WITH_DATA_BY_GROUPID": 11,
	"PUSH_CMD_PUSH_WITH_USER_LIST":         12,
	"PUSH_CMD_PUSH_WITH_MULTICAST_ACCOUNT": 13,
	"PUSH_CMD_PUSH_V2_CH_CAST_MSG":         14,
	"PUSH_CMD_PUSH_V2_UN_CAST_MSG":         15,
	"PUSH_CMD_PUSH_V2_BC_CAST_MSG":         16,
}

func (x PushCmd_Values) Enum() *PushCmd_Values {
	p := new(PushCmd_Values)
	*p = x
	return p
}
func (x PushCmd_Values) String() string {
	return proto.EnumName(PushCmd_Values_name, int32(x))
}
func (x *PushCmd_Values) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PushCmd_Values_value, data, "PushCmd_Values")
	if err != nil {
		return err
	}
	*x = PushCmd_Values(value)
	return nil
}
func (PushCmd_Values) EnumDescriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{0, 0} }

// 保持与定义一致
type MulticastAccount_AccountType int32

const (
	MulticastAccount_ACCOUNT_TYPE_USER           MulticastAccount_AccountType = 0
	MulticastAccount_ACCOUNT_TYPE_GUILD          MulticastAccount_AccountType = 1
	MulticastAccount_ACCOUNT_TYPE_GROUP          MulticastAccount_AccountType = 2
	MulticastAccount_ACCOUNT_TYPE_GAME           MulticastAccount_AccountType = 3
	MulticastAccount_ACCOUNT_TYPE_GUILD_GROUP    MulticastAccount_AccountType = 4
	MulticastAccount_ACCOUNT_TYPE_GAME_GROUP     MulticastAccount_AccountType = 5
	MulticastAccount_ACCOUNT_TYPE_PUBLIC_ACCOUNT MulticastAccount_AccountType = 6
	MulticastAccount_ACCOUNT_TYPE_TGROUP         MulticastAccount_AccountType = 7
	MulticastAccount_ACCOUNT_TYPE_CHANNEL        MulticastAccount_AccountType = 8
)

var MulticastAccount_AccountType_name = map[int32]string{
	0: "ACCOUNT_TYPE_USER",
	1: "ACCOUNT_TYPE_GUILD",
	2: "ACCOUNT_TYPE_GROUP",
	3: "ACCOUNT_TYPE_GAME",
	4: "ACCOUNT_TYPE_GUILD_GROUP",
	5: "ACCOUNT_TYPE_GAME_GROUP",
	6: "ACCOUNT_TYPE_PUBLIC_ACCOUNT",
	7: "ACCOUNT_TYPE_TGROUP",
	8: "ACCOUNT_TYPE_CHANNEL",
}
var MulticastAccount_AccountType_value = map[string]int32{
	"ACCOUNT_TYPE_USER":           0,
	"ACCOUNT_TYPE_GUILD":          1,
	"ACCOUNT_TYPE_GROUP":          2,
	"ACCOUNT_TYPE_GAME":           3,
	"ACCOUNT_TYPE_GUILD_GROUP":    4,
	"ACCOUNT_TYPE_GAME_GROUP":     5,
	"ACCOUNT_TYPE_PUBLIC_ACCOUNT": 6,
	"ACCOUNT_TYPE_TGROUP":         7,
	"ACCOUNT_TYPE_CHANNEL":        8,
}

func (x MulticastAccount_AccountType) Enum() *MulticastAccount_AccountType {
	p := new(MulticastAccount_AccountType)
	*p = x
	return p
}
func (x MulticastAccount_AccountType) String() string {
	return proto.EnumName(MulticastAccount_AccountType_name, int32(x))
}
func (x *MulticastAccount_AccountType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MulticastAccount_AccountType_value, data, "MulticastAccount_AccountType")
	if err != nil {
		return err
	}
	*x = MulticastAccount_AccountType(value)
	return nil
}
func (MulticastAccount_AccountType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorProxyNotify, []int{2, 0}
}

type PushCmd struct {
}

func (m *PushCmd) Reset()                    { *m = PushCmd{} }
func (m *PushCmd) String() string            { return proto.CompactTextString(m) }
func (*PushCmd) ProtoMessage()               {}
func (*PushCmd) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{0} }

// 通知用户
type NotifyUser struct {
	Clientid uint32 `protobuf:"varint,1,req,name=clientid" json:"clientid"`
	Uid      uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *NotifyUser) Reset()                    { *m = NotifyUser{} }
func (m *NotifyUser) String() string            { return proto.CompactTextString(m) }
func (*NotifyUser) ProtoMessage()               {}
func (*NotifyUser) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{1} }

func (m *NotifyUser) GetClientid() uint32 {
	if m != nil {
		return m.Clientid
	}
	return 0
}

func (m *NotifyUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type MulticastAccount struct {
	Id          uint64 `protobuf:"varint,1,req,name=id" json:"id"`
	AccountType uint32 `protobuf:"varint,2,req,name=account_type,json=accountType" json:"account_type"`
	Account     string `protobuf:"bytes,3,req,name=account" json:"account"`
}

func (m *MulticastAccount) Reset()                    { *m = MulticastAccount{} }
func (m *MulticastAccount) String() string            { return proto.CompactTextString(m) }
func (*MulticastAccount) ProtoMessage()               {}
func (*MulticastAccount) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{2} }

func (m *MulticastAccount) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MulticastAccount) GetAccountType() uint32 {
	if m != nil {
		return m.AccountType
	}
	return 0
}

func (m *MulticastAccount) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

// 业务无关的推送, 所有业务相关数据放置在PushBizData中
type PushBizData struct {
	PushCmd uint32 `protobuf:"varint,1,req,name=push_cmd,json=pushCmd" json:"push_cmd"`
	Data    []byte `protobuf:"bytes,2,req,name=data" json:"data"`
}

func (m *PushBizData) Reset()                    { *m = PushBizData{} }
func (m *PushBizData) String() string            { return proto.CompactTextString(m) }
func (*PushBizData) ProtoMessage()               {}
func (*PushBizData) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{3} }

func (m *PushBizData) GetPushCmd() uint32 {
	if m != nil {
		return m.PushCmd
	}
	return 0
}

func (m *PushBizData) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

// 根据用户列表推送, 由调用方去查询在线信息并填写进notify_user_list
type PushWithUserList struct {
	TargetTerminalList []uint32      `protobuf:"varint,1,rep,name=target_terminal_list,json=targetTerminalList" json:"target_terminal_list,omitempty"`
	NotifyUserList     []*NotifyUser `protobuf:"bytes,2,rep,name=notify_user_list,json=notifyUserList" json:"notify_user_list,omitempty"`
	PushBizData        *PushBizData  `protobuf:"bytes,3,req,name=push_biz_data,json=pushBizData" json:"push_biz_data,omitempty"`
	Sequence           uint32        `protobuf:"varint,4,opt,name=sequence" json:"sequence"`
	ServerReceivedAt   uint64        `protobuf:"varint,15,opt,name=server_received_at,json=serverReceivedAt" json:"server_received_at"`
	RequestId          string        `protobuf:"bytes,16,opt,name=request_id,json=requestId" json:"request_id"`
	DyeId              uint64        `protobuf:"varint,17,opt,name=dye_id,json=dyeId" json:"dye_id"`
}

func (m *PushWithUserList) Reset()                    { *m = PushWithUserList{} }
func (m *PushWithUserList) String() string            { return proto.CompactTextString(m) }
func (*PushWithUserList) ProtoMessage()               {}
func (*PushWithUserList) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{4} }

func (m *PushWithUserList) GetTargetTerminalList() []uint32 {
	if m != nil {
		return m.TargetTerminalList
	}
	return nil
}

func (m *PushWithUserList) GetNotifyUserList() []*NotifyUser {
	if m != nil {
		return m.NotifyUserList
	}
	return nil
}

func (m *PushWithUserList) GetPushBizData() *PushBizData {
	if m != nil {
		return m.PushBizData
	}
	return nil
}

func (m *PushWithUserList) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *PushWithUserList) GetServerReceivedAt() uint64 {
	if m != nil {
		return m.ServerReceivedAt
	}
	return 0
}

func (m *PushWithUserList) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *PushWithUserList) GetDyeId() uint64 {
	if m != nil {
		return m.DyeId
	}
	return 0
}

// 根据多播订阅关系进行推送
type PushWithMulticastAccount struct {
	TargetTerminalList []uint32            `protobuf:"varint,1,rep,name=target_terminal_list,json=targetTerminalList" json:"target_terminal_list,omitempty"`
	MulticastAccount   *MulticastAccount   `protobuf:"bytes,2,req,name=multicast_account,json=multicastAccount" json:"multicast_account,omitempty"`
	SkipUids           []uint32            `protobuf:"varint,3,rep,name=skip_uids,json=skipUids" json:"skip_uids,omitempty"`
	PushBizData        *PushBizData        `protobuf:"bytes,4,req,name=push_biz_data,json=pushBizData" json:"push_biz_data,omitempty"`
	Sequence           uint32              `protobuf:"varint,5,opt,name=sequence" json:"sequence"`
	MulticastAccounts  []*MulticastAccount `protobuf:"bytes,6,rep,name=multicast_accounts,json=multicastAccounts" json:"multicast_accounts,omitempty"`
	ServerReceivedAt   uint64              `protobuf:"varint,15,opt,name=server_received_at,json=serverReceivedAt" json:"server_received_at"`
	RequestId          string              `protobuf:"bytes,16,opt,name=request_id,json=requestId" json:"request_id"`
	DyeId              uint64              `protobuf:"varint,17,opt,name=dye_id,json=dyeId" json:"dye_id"`
}

func (m *PushWithMulticastAccount) Reset()         { *m = PushWithMulticastAccount{} }
func (m *PushWithMulticastAccount) String() string { return proto.CompactTextString(m) }
func (*PushWithMulticastAccount) ProtoMessage()    {}
func (*PushWithMulticastAccount) Descriptor() ([]byte, []int) {
	return fileDescriptorProxyNotify, []int{5}
}

func (m *PushWithMulticastAccount) GetTargetTerminalList() []uint32 {
	if m != nil {
		return m.TargetTerminalList
	}
	return nil
}

func (m *PushWithMulticastAccount) GetMulticastAccount() *MulticastAccount {
	if m != nil {
		return m.MulticastAccount
	}
	return nil
}

func (m *PushWithMulticastAccount) GetSkipUids() []uint32 {
	if m != nil {
		return m.SkipUids
	}
	return nil
}

func (m *PushWithMulticastAccount) GetPushBizData() *PushBizData {
	if m != nil {
		return m.PushBizData
	}
	return nil
}

func (m *PushWithMulticastAccount) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *PushWithMulticastAccount) GetMulticastAccounts() []*MulticastAccount {
	if m != nil {
		return m.MulticastAccounts
	}
	return nil
}

func (m *PushWithMulticastAccount) GetServerReceivedAt() uint64 {
	if m != nil {
		return m.ServerReceivedAt
	}
	return 0
}

func (m *PushWithMulticastAccount) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *PushWithMulticastAccount) GetDyeId() uint64 {
	if m != nil {
		return m.DyeId
	}
	return 0
}

// 批量通知用户
type BatchNotifyInfo struct {
	NotifyType uint32        `protobuf:"varint,1,req,name=notify_type,json=notifyType" json:"notify_type"`
	Info       []byte        `protobuf:"bytes,2,req,name=info" json:"info"`
	Usrlists   []*NotifyUser `protobuf:"bytes,3,rep,name=usrlists" json:"usrlists,omitempty"`
}

func (m *BatchNotifyInfo) Reset()                    { *m = BatchNotifyInfo{} }
func (m *BatchNotifyInfo) String() string            { return proto.CompactTextString(m) }
func (*BatchNotifyInfo) ProtoMessage()               {}
func (*BatchNotifyInfo) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{6} }

func (m *BatchNotifyInfo) GetNotifyType() uint32 {
	if m != nil {
		return m.NotifyType
	}
	return 0
}

func (m *BatchNotifyInfo) GetInfo() []byte {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *BatchNotifyInfo) GetUsrlists() []*NotifyUser {
	if m != nil {
		return m.Usrlists
	}
	return nil
}

// 带内容的批量通知
type NotifyWithDataInfo struct {
	NotifyType        uint32        `protobuf:"varint,1,req,name=notify_type,json=notifyType" json:"notify_type"`
	NotifyExtendMulti []byte        `protobuf:"bytes,2,req,name=notify_extend_multi,json=notifyExtendMulti" json:"notify_extend_multi"`
	Usrlists          []*NotifyUser `protobuf:"bytes,3,rep,name=usrlists" json:"usrlists,omitempty"`
}

func (m *NotifyWithDataInfo) Reset()                    { *m = NotifyWithDataInfo{} }
func (m *NotifyWithDataInfo) String() string            { return proto.CompactTextString(m) }
func (*NotifyWithDataInfo) ProtoMessage()               {}
func (*NotifyWithDataInfo) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{7} }

func (m *NotifyWithDataInfo) GetNotifyType() uint32 {
	if m != nil {
		return m.NotifyType
	}
	return 0
}

func (m *NotifyWithDataInfo) GetNotifyExtendMulti() []byte {
	if m != nil {
		return m.NotifyExtendMulti
	}
	return nil
}

func (m *NotifyWithDataInfo) GetUsrlists() []*NotifyUser {
	if m != nil {
		return m.Usrlists
	}
	return nil
}

type NotifyWithDataByGroupId struct {
	GroupId           uint32   `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
	NotifyType        uint32   `protobuf:"varint,2,req,name=notify_type,json=notifyType" json:"notify_type"`
	NotifyExtendMulti []byte   `protobuf:"bytes,3,req,name=notify_extend_multi,json=notifyExtendMulti" json:"notify_extend_multi"`
	SkipUids          []uint32 `protobuf:"varint,4,rep,name=skip_uids,json=skipUids" json:"skip_uids,omitempty"`
}

func (m *NotifyWithDataByGroupId) Reset()         { *m = NotifyWithDataByGroupId{} }
func (m *NotifyWithDataByGroupId) String() string { return proto.CompactTextString(m) }
func (*NotifyWithDataByGroupId) ProtoMessage()    {}
func (*NotifyWithDataByGroupId) Descriptor() ([]byte, []int) {
	return fileDescriptorProxyNotify, []int{8}
}

func (m *NotifyWithDataByGroupId) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *NotifyWithDataByGroupId) GetNotifyType() uint32 {
	if m != nil {
		return m.NotifyType
	}
	return 0
}

func (m *NotifyWithDataByGroupId) GetNotifyExtendMulti() []byte {
	if m != nil {
		return m.NotifyExtendMulti
	}
	return nil
}

func (m *NotifyWithDataByGroupId) GetSkipUids() []uint32 {
	if m != nil {
		return m.SkipUids
	}
	return nil
}

// 这个协议和transmission.proto中保持一致
type TransmissionPacket struct {
	AppId   uint32 `protobuf:"varint,1,req,name=app_id,json=appId" json:"app_id"`
	Payload []byte `protobuf:"bytes,2,req,name=payload" json:"payload"`
}

func (m *TransmissionPacket) Reset()                    { *m = TransmissionPacket{} }
func (m *TransmissionPacket) String() string            { return proto.CompactTextString(m) }
func (*TransmissionPacket) ProtoMessage()               {}
func (*TransmissionPacket) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{9} }

func (m *TransmissionPacket) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *TransmissionPacket) GetPayload() []byte {
	if m != nil {
		return m.Payload
	}
	return nil
}

type Empty struct {
}

func (m *Empty) Reset()                    { *m = Empty{} }
func (m *Empty) String() string            { return proto.CompactTextString(m) }
func (*Empty) ProtoMessage()               {}
func (*Empty) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{10} }

type NotifyReq struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ClientId   uint32 `protobuf:"varint,2,req,name=client_id,json=clientId" json:"client_id"`
	NotifyType uint32 `protobuf:"varint,3,req,name=notify_type,json=notifyType" json:"notify_type"`
}

func (m *NotifyReq) Reset()                    { *m = NotifyReq{} }
func (m *NotifyReq) String() string            { return proto.CompactTextString(m) }
func (*NotifyReq) ProtoMessage()               {}
func (*NotifyReq) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{11} }

func (m *NotifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NotifyReq) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *NotifyReq) GetNotifyType() uint32 {
	if m != nil {
		return m.NotifyType
	}
	return 0
}

type NotifyPayload struct {
	Payload []byte `protobuf:"bytes,1,req,name=payload" json:"payload"`
}

func (m *NotifyPayload) Reset()                    { *m = NotifyPayload{} }
func (m *NotifyPayload) String() string            { return proto.CompactTextString(m) }
func (*NotifyPayload) ProtoMessage()               {}
func (*NotifyPayload) Descriptor() ([]byte, []int) { return fileDescriptorProxyNotify, []int{12} }

func (m *NotifyPayload) GetPayload() []byte {
	if m != nil {
		return m.Payload
	}
	return nil
}

func init() {
	proto.RegisterType((*PushCmd)(nil), "notify.PushCmd")
	proto.RegisterType((*NotifyUser)(nil), "notify.NotifyUser")
	proto.RegisterType((*MulticastAccount)(nil), "notify.MulticastAccount")
	proto.RegisterType((*PushBizData)(nil), "notify.PushBizData")
	proto.RegisterType((*PushWithUserList)(nil), "notify.PushWithUserList")
	proto.RegisterType((*PushWithMulticastAccount)(nil), "notify.PushWithMulticastAccount")
	proto.RegisterType((*BatchNotifyInfo)(nil), "notify.BatchNotifyInfo")
	proto.RegisterType((*NotifyWithDataInfo)(nil), "notify.NotifyWithDataInfo")
	proto.RegisterType((*NotifyWithDataByGroupId)(nil), "notify.NotifyWithDataByGroupId")
	proto.RegisterType((*TransmissionPacket)(nil), "notify.TransmissionPacket")
	proto.RegisterType((*Empty)(nil), "notify.Empty")
	proto.RegisterType((*NotifyReq)(nil), "notify.NotifyReq")
	proto.RegisterType((*NotifyPayload)(nil), "notify.NotifyPayload")
	proto.RegisterEnum("notify.PushCmd_Values", PushCmd_Values_name, PushCmd_Values_value)
	proto.RegisterEnum("notify.MulticastAccount_AccountType", MulticastAccount_AccountType_name, MulticastAccount_AccountType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Notify service

type NotifyClient interface {
	Notify(ctx context.Context, in *NotifyReq, opts ...grpc.CallOption) (*Empty, error)
	Kick(ctx context.Context, in *NotifyPayload, opts ...grpc.CallOption) (*Empty, error)
	Push(ctx context.Context, in *NotifyPayload, opts ...grpc.CallOption) (*Empty, error)
	BatchPush(ctx context.Context, in *BatchNotifyInfo, opts ...grpc.CallOption) (*Empty, error)
	RegEvent(ctx context.Context, in *UserGroupRelationReg.UserGroupRelationRegistEvent, opts ...grpc.CallOption) (*Empty, error)
	BatchRegEvent(ctx context.Context, in *UserGroupRelationReg.BatchUserGroupRelationRegistEvent, opts ...grpc.CallOption) (*Empty, error)
	PushByGroupId(ctx context.Context, in *NotifyPayload, opts ...grpc.CallOption) (*Empty, error)
	NotifyByGroupId(ctx context.Context, in *NotifyPayload, opts ...grpc.CallOption) (*Empty, error)
	PushByChannelId(ctx context.Context, in *NotifyPayload, opts ...grpc.CallOption) (*Empty, error)
	NotifyWithData(ctx context.Context, in *NotifyWithDataInfo, opts ...grpc.CallOption) (*Empty, error)
	NotifyWithDataByGroup(ctx context.Context, in *NotifyWithDataByGroupId, opts ...grpc.CallOption) (*Empty, error)
	PushWithUserLst(ctx context.Context, in *PushWithUserList, opts ...grpc.CallOption) (*Empty, error)
	PushWithMultiCastAccount(ctx context.Context, in *PushWithMulticastAccount, opts ...grpc.CallOption) (*Empty, error)
}

type notifyClient struct {
	cc *grpc.ClientConn
}

func NewNotifyClient(cc *grpc.ClientConn) NotifyClient {
	return &notifyClient{cc}
}

func (c *notifyClient) Notify(ctx context.Context, in *NotifyReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/Notify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) Kick(ctx context.Context, in *NotifyPayload, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/Kick", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) Push(ctx context.Context, in *NotifyPayload, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/Push", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) BatchPush(ctx context.Context, in *BatchNotifyInfo, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/BatchPush", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) RegEvent(ctx context.Context, in *UserGroupRelationReg.UserGroupRelationRegistEvent, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/RegEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) BatchRegEvent(ctx context.Context, in *UserGroupRelationReg.BatchUserGroupRelationRegistEvent, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/BatchRegEvent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) PushByGroupId(ctx context.Context, in *NotifyPayload, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/PushByGroupId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) NotifyByGroupId(ctx context.Context, in *NotifyPayload, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/NotifyByGroupId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) PushByChannelId(ctx context.Context, in *NotifyPayload, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/PushByChannelId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) NotifyWithData(ctx context.Context, in *NotifyWithDataInfo, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/NotifyWithData", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) NotifyWithDataByGroup(ctx context.Context, in *NotifyWithDataByGroupId, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/NotifyWithDataByGroup", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) PushWithUserLst(ctx context.Context, in *PushWithUserList, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/PushWithUserLst", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) PushWithMultiCastAccount(ctx context.Context, in *PushWithMulticastAccount, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := grpc.Invoke(ctx, "/notify.notify/PushWithMultiCastAccount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Notify service

type NotifyServer interface {
	Notify(context.Context, *NotifyReq) (*Empty, error)
	Kick(context.Context, *NotifyPayload) (*Empty, error)
	Push(context.Context, *NotifyPayload) (*Empty, error)
	BatchPush(context.Context, *BatchNotifyInfo) (*Empty, error)
	RegEvent(context.Context, *UserGroupRelationReg.UserGroupRelationRegistEvent) (*Empty, error)
	BatchRegEvent(context.Context, *UserGroupRelationReg.BatchUserGroupRelationRegistEvent) (*Empty, error)
	PushByGroupId(context.Context, *NotifyPayload) (*Empty, error)
	NotifyByGroupId(context.Context, *NotifyPayload) (*Empty, error)
	PushByChannelId(context.Context, *NotifyPayload) (*Empty, error)
	NotifyWithData(context.Context, *NotifyWithDataInfo) (*Empty, error)
	NotifyWithDataByGroup(context.Context, *NotifyWithDataByGroupId) (*Empty, error)
	PushWithUserLst(context.Context, *PushWithUserList) (*Empty, error)
	PushWithMultiCastAccount(context.Context, *PushWithMulticastAccount) (*Empty, error)
}

func RegisterNotifyServer(s *grpc.Server, srv NotifyServer) {
	s.RegisterService(&_Notify_serviceDesc, srv)
}

func _Notify_Notify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).Notify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/Notify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).Notify(ctx, req.(*NotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_Kick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPayload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).Kick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/Kick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).Kick(ctx, req.(*NotifyPayload))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_Push_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPayload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).Push(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/Push",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).Push(ctx, req.(*NotifyPayload))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_BatchPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchNotifyInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).BatchPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/BatchPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).BatchPush(ctx, req.(*BatchNotifyInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_RegEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserGroupRelationReg.UserGroupRelationRegistEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).RegEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/RegEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).RegEvent(ctx, req.(*UserGroupRelationReg.UserGroupRelationRegistEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_BatchRegEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserGroupRelationReg.BatchUserGroupRelationRegistEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).BatchRegEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/BatchRegEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).BatchRegEvent(ctx, req.(*UserGroupRelationReg.BatchUserGroupRelationRegistEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_PushByGroupId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPayload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).PushByGroupId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/PushByGroupId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).PushByGroupId(ctx, req.(*NotifyPayload))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_NotifyByGroupId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPayload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).NotifyByGroupId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/NotifyByGroupId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).NotifyByGroupId(ctx, req.(*NotifyPayload))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_PushByChannelId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPayload)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).PushByChannelId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/PushByChannelId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).PushByChannelId(ctx, req.(*NotifyPayload))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_NotifyWithData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyWithDataInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).NotifyWithData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/NotifyWithData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).NotifyWithData(ctx, req.(*NotifyWithDataInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_NotifyWithDataByGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyWithDataByGroupId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).NotifyWithDataByGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/NotifyWithDataByGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).NotifyWithDataByGroup(ctx, req.(*NotifyWithDataByGroupId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_PushWithUserLst_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushWithUserList)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).PushWithUserLst(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/PushWithUserLst",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).PushWithUserLst(ctx, req.(*PushWithUserList))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_PushWithMultiCastAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushWithMulticastAccount)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).PushWithMultiCastAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/notify.notify/PushWithMultiCastAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).PushWithMultiCastAccount(ctx, req.(*PushWithMulticastAccount))
	}
	return interceptor(ctx, in, info, handler)
}

var _Notify_serviceDesc = grpc.ServiceDesc{
	ServiceName: "notify.notify",
	HandlerType: (*NotifyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Notify",
			Handler:    _Notify_Notify_Handler,
		},
		{
			MethodName: "Kick",
			Handler:    _Notify_Kick_Handler,
		},
		{
			MethodName: "Push",
			Handler:    _Notify_Push_Handler,
		},
		{
			MethodName: "BatchPush",
			Handler:    _Notify_BatchPush_Handler,
		},
		{
			MethodName: "RegEvent",
			Handler:    _Notify_RegEvent_Handler,
		},
		{
			MethodName: "BatchRegEvent",
			Handler:    _Notify_BatchRegEvent_Handler,
		},
		{
			MethodName: "PushByGroupId",
			Handler:    _Notify_PushByGroupId_Handler,
		},
		{
			MethodName: "NotifyByGroupId",
			Handler:    _Notify_NotifyByGroupId_Handler,
		},
		{
			MethodName: "PushByChannelId",
			Handler:    _Notify_PushByChannelId_Handler,
		},
		{
			MethodName: "NotifyWithData",
			Handler:    _Notify_NotifyWithData_Handler,
		},
		{
			MethodName: "NotifyWithDataByGroup",
			Handler:    _Notify_NotifyWithDataByGroup_Handler,
		},
		{
			MethodName: "PushWithUserLst",
			Handler:    _Notify_PushWithUserLst_Handler,
		},
		{
			MethodName: "PushWithMultiCastAccount",
			Handler:    _Notify_PushWithMultiCastAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/proto/svr_pbfile/proxy_notify.proto",
}

func (m *PushCmd) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PushCmd) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *NotifyUser) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyUser) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.Clientid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *MulticastAccount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MulticastAccount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.AccountType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	return i, nil
}

func (m *PushBizData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PushBizData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.PushCmd))
	if m.Data != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintProxyNotify(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	return i, nil
}

func (m *PushWithUserList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PushWithUserList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TargetTerminalList) > 0 {
		for _, num := range m.TargetTerminalList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintProxyNotify(dAtA, i, uint64(num))
		}
	}
	if len(m.NotifyUserList) > 0 {
		for _, msg := range m.NotifyUserList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintProxyNotify(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.PushBizData == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("push_biz_data")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintProxyNotify(dAtA, i, uint64(m.PushBizData.Size()))
		n1, err := m.PushBizData.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.Sequence))
	dAtA[i] = 0x78
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.ServerReceivedAt))
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.DyeId))
	return i, nil
}

func (m *PushWithMulticastAccount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PushWithMulticastAccount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TargetTerminalList) > 0 {
		for _, num := range m.TargetTerminalList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintProxyNotify(dAtA, i, uint64(num))
		}
	}
	if m.MulticastAccount == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("multicast_account")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintProxyNotify(dAtA, i, uint64(m.MulticastAccount.Size()))
		n2, err := m.MulticastAccount.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.SkipUids) > 0 {
		for _, num := range m.SkipUids {
			dAtA[i] = 0x18
			i++
			i = encodeVarintProxyNotify(dAtA, i, uint64(num))
		}
	}
	if m.PushBizData == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("push_biz_data")
	} else {
		dAtA[i] = 0x22
		i++
		i = encodeVarintProxyNotify(dAtA, i, uint64(m.PushBizData.Size()))
		n3, err := m.PushBizData.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.Sequence))
	if len(m.MulticastAccounts) > 0 {
		for _, msg := range m.MulticastAccounts {
			dAtA[i] = 0x32
			i++
			i = encodeVarintProxyNotify(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x78
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.ServerReceivedAt))
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(len(m.RequestId)))
	i += copy(dAtA[i:], m.RequestId)
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.DyeId))
	return i, nil
}

func (m *BatchNotifyInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchNotifyInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.NotifyType))
	if m.Info != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintProxyNotify(dAtA, i, uint64(len(m.Info)))
		i += copy(dAtA[i:], m.Info)
	}
	if len(m.Usrlists) > 0 {
		for _, msg := range m.Usrlists {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintProxyNotify(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *NotifyWithDataInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyWithDataInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.NotifyType))
	if m.NotifyExtendMulti != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintProxyNotify(dAtA, i, uint64(len(m.NotifyExtendMulti)))
		i += copy(dAtA[i:], m.NotifyExtendMulti)
	}
	if len(m.Usrlists) > 0 {
		for _, msg := range m.Usrlists {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintProxyNotify(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *NotifyWithDataByGroupId) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyWithDataByGroupId) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.NotifyType))
	if m.NotifyExtendMulti != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintProxyNotify(dAtA, i, uint64(len(m.NotifyExtendMulti)))
		i += copy(dAtA[i:], m.NotifyExtendMulti)
	}
	if len(m.SkipUids) > 0 {
		for _, num := range m.SkipUids {
			dAtA[i] = 0x20
			i++
			i = encodeVarintProxyNotify(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *TransmissionPacket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransmissionPacket) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.AppId))
	if m.Payload != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintProxyNotify(dAtA, i, uint64(len(m.Payload)))
		i += copy(dAtA[i:], m.Payload)
	}
	return i, nil
}

func (m *Empty) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Empty) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *NotifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.ClientId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintProxyNotify(dAtA, i, uint64(m.NotifyType))
	return i, nil
}

func (m *NotifyPayload) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyPayload) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Payload != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintProxyNotify(dAtA, i, uint64(len(m.Payload)))
		i += copy(dAtA[i:], m.Payload)
	}
	return i, nil
}

func encodeFixed64ProxyNotify(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32ProxyNotify(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintProxyNotify(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *PushCmd) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *NotifyUser) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovProxyNotify(uint64(m.Clientid))
	n += 1 + sovProxyNotify(uint64(m.Uid))
	return n
}

func (m *MulticastAccount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovProxyNotify(uint64(m.Id))
	n += 1 + sovProxyNotify(uint64(m.AccountType))
	l = len(m.Account)
	n += 1 + l + sovProxyNotify(uint64(l))
	return n
}

func (m *PushBizData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovProxyNotify(uint64(m.PushCmd))
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovProxyNotify(uint64(l))
	}
	return n
}

func (m *PushWithUserList) Size() (n int) {
	var l int
	_ = l
	if len(m.TargetTerminalList) > 0 {
		for _, e := range m.TargetTerminalList {
			n += 1 + sovProxyNotify(uint64(e))
		}
	}
	if len(m.NotifyUserList) > 0 {
		for _, e := range m.NotifyUserList {
			l = e.Size()
			n += 1 + l + sovProxyNotify(uint64(l))
		}
	}
	if m.PushBizData != nil {
		l = m.PushBizData.Size()
		n += 1 + l + sovProxyNotify(uint64(l))
	}
	n += 1 + sovProxyNotify(uint64(m.Sequence))
	n += 1 + sovProxyNotify(uint64(m.ServerReceivedAt))
	l = len(m.RequestId)
	n += 2 + l + sovProxyNotify(uint64(l))
	n += 2 + sovProxyNotify(uint64(m.DyeId))
	return n
}

func (m *PushWithMulticastAccount) Size() (n int) {
	var l int
	_ = l
	if len(m.TargetTerminalList) > 0 {
		for _, e := range m.TargetTerminalList {
			n += 1 + sovProxyNotify(uint64(e))
		}
	}
	if m.MulticastAccount != nil {
		l = m.MulticastAccount.Size()
		n += 1 + l + sovProxyNotify(uint64(l))
	}
	if len(m.SkipUids) > 0 {
		for _, e := range m.SkipUids {
			n += 1 + sovProxyNotify(uint64(e))
		}
	}
	if m.PushBizData != nil {
		l = m.PushBizData.Size()
		n += 1 + l + sovProxyNotify(uint64(l))
	}
	n += 1 + sovProxyNotify(uint64(m.Sequence))
	if len(m.MulticastAccounts) > 0 {
		for _, e := range m.MulticastAccounts {
			l = e.Size()
			n += 1 + l + sovProxyNotify(uint64(l))
		}
	}
	n += 1 + sovProxyNotify(uint64(m.ServerReceivedAt))
	l = len(m.RequestId)
	n += 2 + l + sovProxyNotify(uint64(l))
	n += 2 + sovProxyNotify(uint64(m.DyeId))
	return n
}

func (m *BatchNotifyInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovProxyNotify(uint64(m.NotifyType))
	if m.Info != nil {
		l = len(m.Info)
		n += 1 + l + sovProxyNotify(uint64(l))
	}
	if len(m.Usrlists) > 0 {
		for _, e := range m.Usrlists {
			l = e.Size()
			n += 1 + l + sovProxyNotify(uint64(l))
		}
	}
	return n
}

func (m *NotifyWithDataInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovProxyNotify(uint64(m.NotifyType))
	if m.NotifyExtendMulti != nil {
		l = len(m.NotifyExtendMulti)
		n += 1 + l + sovProxyNotify(uint64(l))
	}
	if len(m.Usrlists) > 0 {
		for _, e := range m.Usrlists {
			l = e.Size()
			n += 1 + l + sovProxyNotify(uint64(l))
		}
	}
	return n
}

func (m *NotifyWithDataByGroupId) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovProxyNotify(uint64(m.GroupId))
	n += 1 + sovProxyNotify(uint64(m.NotifyType))
	if m.NotifyExtendMulti != nil {
		l = len(m.NotifyExtendMulti)
		n += 1 + l + sovProxyNotify(uint64(l))
	}
	if len(m.SkipUids) > 0 {
		for _, e := range m.SkipUids {
			n += 1 + sovProxyNotify(uint64(e))
		}
	}
	return n
}

func (m *TransmissionPacket) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovProxyNotify(uint64(m.AppId))
	if m.Payload != nil {
		l = len(m.Payload)
		n += 1 + l + sovProxyNotify(uint64(l))
	}
	return n
}

func (m *Empty) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *NotifyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovProxyNotify(uint64(m.Uid))
	n += 1 + sovProxyNotify(uint64(m.ClientId))
	n += 1 + sovProxyNotify(uint64(m.NotifyType))
	return n
}

func (m *NotifyPayload) Size() (n int) {
	var l int
	_ = l
	if m.Payload != nil {
		l = len(m.Payload)
		n += 1 + l + sovProxyNotify(uint64(l))
	}
	return n
}

func sovProxyNotify(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozProxyNotify(x uint64) (n int) {
	return sovProxyNotify(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *PushCmd) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PushCmd: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PushCmd: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyUser) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyUser: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyUser: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Clientid", wireType)
			}
			m.Clientid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Clientid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("clientid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MulticastAccount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MulticastAccount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MulticastAccount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccountType", wireType)
			}
			m.AccountType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AccountType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PushBizData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PushBizData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PushBizData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushCmd", wireType)
			}
			m.PushCmd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushCmd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_cmd")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PushWithUserList) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PushWithUserList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PushWithUserList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowProxyNotify
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TargetTerminalList = append(m.TargetTerminalList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowProxyNotify
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthProxyNotify
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowProxyNotify
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TargetTerminalList = append(m.TargetTerminalList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetTerminalList", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyUserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NotifyUserList = append(m.NotifyUserList, &NotifyUser{})
			if err := m.NotifyUserList[len(m.NotifyUserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushBizData", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PushBizData == nil {
				m.PushBizData = &PushBizData{}
			}
			if err := m.PushBizData.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sequence", wireType)
			}
			m.Sequence = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sequence |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerReceivedAt", wireType)
			}
			m.ServerReceivedAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerReceivedAt |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DyeId", wireType)
			}
			m.DyeId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DyeId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_biz_data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PushWithMulticastAccount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PushWithMulticastAccount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PushWithMulticastAccount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowProxyNotify
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TargetTerminalList = append(m.TargetTerminalList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowProxyNotify
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthProxyNotify
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowProxyNotify
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TargetTerminalList = append(m.TargetTerminalList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetTerminalList", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MulticastAccount", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MulticastAccount == nil {
				m.MulticastAccount = &MulticastAccount{}
			}
			if err := m.MulticastAccount.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowProxyNotify
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SkipUids = append(m.SkipUids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowProxyNotify
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthProxyNotify
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowProxyNotify
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SkipUids = append(m.SkipUids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SkipUids", wireType)
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PushBizData", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PushBizData == nil {
				m.PushBizData = &PushBizData{}
			}
			if err := m.PushBizData.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sequence", wireType)
			}
			m.Sequence = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sequence |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MulticastAccounts", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MulticastAccounts = append(m.MulticastAccounts, &MulticastAccount{})
			if err := m.MulticastAccounts[len(m.MulticastAccounts)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerReceivedAt", wireType)
			}
			m.ServerReceivedAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerReceivedAt |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RequestId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DyeId", wireType)
			}
			m.DyeId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DyeId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("multicast_account")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("push_biz_data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchNotifyInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchNotifyInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchNotifyInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyType", wireType)
			}
			m.NotifyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NotifyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Info = append(m.Info[:0], dAtA[iNdEx:postIndex]...)
			if m.Info == nil {
				m.Info = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Usrlists", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Usrlists = append(m.Usrlists, &NotifyUser{})
			if err := m.Usrlists[len(m.Usrlists)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("notify_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyWithDataInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyWithDataInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyWithDataInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyType", wireType)
			}
			m.NotifyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NotifyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyExtendMulti", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NotifyExtendMulti = append(m.NotifyExtendMulti[:0], dAtA[iNdEx:postIndex]...)
			if m.NotifyExtendMulti == nil {
				m.NotifyExtendMulti = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Usrlists", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Usrlists = append(m.Usrlists, &NotifyUser{})
			if err := m.Usrlists[len(m.Usrlists)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("notify_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("notify_extend_multi")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyWithDataByGroupId) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyWithDataByGroupId: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyWithDataByGroupId: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyType", wireType)
			}
			m.NotifyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NotifyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyExtendMulti", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NotifyExtendMulti = append(m.NotifyExtendMulti[:0], dAtA[iNdEx:postIndex]...)
			if m.NotifyExtendMulti == nil {
				m.NotifyExtendMulti = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowProxyNotify
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SkipUids = append(m.SkipUids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowProxyNotify
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthProxyNotify
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowProxyNotify
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SkipUids = append(m.SkipUids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SkipUids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("notify_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("notify_extend_multi")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransmissionPacket) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TransmissionPacket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TransmissionPacket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Payload", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Payload = append(m.Payload[:0], dAtA[iNdEx:postIndex]...)
			if m.Payload == nil {
				m.Payload = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("payload")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Empty) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Empty: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Empty: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			m.ClientId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyType", wireType)
			}
			m.NotifyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NotifyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("notify_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyPayload) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyPayload: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyPayload: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Payload", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthProxyNotify
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Payload = append(m.Payload[:0], dAtA[iNdEx:postIndex]...)
			if m.Payload == nil {
				m.Payload = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipProxyNotify(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthProxyNotify
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("payload")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipProxyNotify(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowProxyNotify
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowProxyNotify
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthProxyNotify
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowProxyNotify
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipProxyNotify(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthProxyNotify = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowProxyNotify   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/proto/svr_pbfile/proxy_notify.proto", fileDescriptorProxyNotify) }

var fileDescriptorProxyNotify = []byte{
	// 1296 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x57, 0x3f, 0x6f, 0xdb, 0x46,
	0x14, 0x37, 0x25, 0x59, 0x92, 0x9f, 0xa2, 0x98, 0x3a, 0xdb, 0x31, 0x1b, 0xa7, 0xb6, 0xa2, 0xb6,
	0x88, 0x87, 0x42, 0x2e, 0x84, 0x06, 0x41, 0x91, 0x74, 0x90, 0x64, 0x45, 0x26, 0x62, 0xc9, 0x2a,
	0x4d, 0x25, 0x70, 0x97, 0x03, 0x43, 0x9e, 0xe5, 0x43, 0x24, 0x92, 0xe1, 0x9d, 0x8c, 0x28, 0x63,
	0x3f, 0x41, 0xbf, 0x42, 0xbf, 0x43, 0xf7, 0xae, 0x41, 0xa7, 0x02, 0xdd, 0x8b, 0x22, 0x5d, 0x3a,
	0x75, 0xea, 0xd4, 0xa9, 0xe0, 0x91, 0xa2, 0x49, 0x51, 0x29, 0xe2, 0x2c, 0xdd, 0xc4, 0xf7, 0xfb,
	0xbd, 0x77, 0xef, 0xde, 0xdf, 0x13, 0xdc, 0x63, 0x9e, 0x79, 0xe0, 0x7a, 0x0e, 0x77, 0x0e, 0xd8,
	0xa5, 0x87, 0xdd, 0xe7, 0xe7, 0x74, 0x4c, 0x7c, 0xc1, 0xab, 0x19, 0xb6, 0x1d, 0x4e, 0xcf, 0x67,
	0x75, 0x81, 0xa2, 0x7c, 0xf0, 0x75, 0xfb, 0xee, 0x94, 0x11, 0x6f, 0xe4, 0x39, 0x53, 0x17, 0x7b,
	0x64, 0x6c, 0x70, 0xea, 0xd8, 0xd8, 0x23, 0x23, 0xca, 0x38, 0xf1, 0x02, 0x6a, 0xed, 0xef, 0x2c,
	0x14, 0x06, 0x53, 0x76, 0xd1, 0x9e, 0x58, 0xb5, 0x3f, 0xb3, 0x90, 0x7f, 0x6a, 0x8c, 0xa7, 0x84,
	0xa1, 0x0d, 0x58, 0x1f, 0x0c, 0x4f, 0x8f, 0x70, 0xbb, 0x77, 0x88, 0xfb, 0x27, 0xba, 0xfa, 0xf8,
	0x4c, 0x96, 0xd0, 0x26, 0xc8, 0x91, 0xf0, 0x89, 0xda, 0x7e, 0x72, 0x32, 0xd4, 0xe5, 0x0c, 0xaa,
	0x40, 0x39, 0x92, 0xfa, 0x3f, 0xe4, 0x2c, 0xba, 0x05, 0x28, 0x12, 0xb5, 0x9a, 0x7a, 0xfb, 0x48,
	0xc8, 0x73, 0x68, 0x0b, 0x2a, 0x91, 0x5c, 0xeb, 0x74, 0x3b, 0x4f, 0x3b, 0x7d, 0x5d, 0x5e, 0x45,
	0x3b, 0xb0, 0x9d, 0xa4, 0x5f, 0x81, 0xf9, 0x04, 0x28, 0x7e, 0xb4, 0xce, 0xba, 0xda, 0xc9, 0x70,
	0xa0, 0x1e, 0xca, 0x05, 0xf4, 0x31, 0x7c, 0xb4, 0xe0, 0x66, 0x0c, 0x2e, 0x26, 0xe0, 0x50, 0xb7,
	0x7d, 0xd4, 0xec, 0xf7, 0x3b, 0xc7, 0xea, 0xa1, 0xbc, 0xb6, 0x4c, 0xfb, 0x99, 0xaa, 0x1f, 0xe1,
	0xc3, 0xa6, 0xde, 0x94, 0x01, 0xed, 0xc3, 0xa7, 0xef, 0x84, 0x71, 0xeb, 0x0c, 0xcf, 0xcf, 0x29,
	0xa1, 0x2a, 0xdc, 0x49, 0x9e, 0x23, 0x78, 0xc3, 0xd3, 0x8e, 0x86, 0x8f, 0xd5, 0x53, 0x5d, 0xbe,
	0x91, 0xb0, 0x75, 0xc5, 0xe8, 0x0d, 0x8f, 0x75, 0xb5, 0xdd, 0x3c, 0xd5, 0x71, 0xb3, 0xdd, 0x3e,
	0x19, 0xf6, 0x75, 0xb9, 0x9c, 0xb6, 0xf5, 0xb4, 0x81, 0xdb, 0x47, 0x58, 0xb0, 0x7a, 0xa7, 0x5d,
	0xf9, 0xe6, 0x52, 0xc6, 0xb0, 0x7f, 0xc5, 0x58, 0x5f, 0xca, 0x68, 0xb5, 0xaf, 0x18, 0x72, 0xed,
	0x31, 0x40, 0x5f, 0xd4, 0xc8, 0x90, 0x11, 0x0f, 0x55, 0xa1, 0x68, 0x8e, 0x29, 0xb1, 0x39, 0xb5,
	0x14, 0xa9, 0x9a, 0xd9, 0x2f, 0xb7, 0x72, 0x6f, 0x7e, 0xdb, 0x5b, 0xd1, 0x22, 0x29, 0xba, 0x05,
	0xd9, 0x29, 0xb5, 0x94, 0x4c, 0x0c, 0xf4, 0x05, 0xb5, 0xbf, 0x32, 0x20, 0xf7, 0xa6, 0x63, 0x4e,
	0x4d, 0x83, 0xf1, 0xa6, 0x69, 0x3a, 0x53, 0x9b, 0xa3, 0x4d, 0xc8, 0x84, 0x86, 0x72, 0x21, 0x37,
	0x43, 0x2d, 0x74, 0x0f, 0x6e, 0x18, 0x01, 0x01, 0xf3, 0x99, 0x4b, 0x12, 0xb6, 0x4a, 0x21, 0xa2,
	0xcf, 0x5c, 0x82, 0x76, 0xa1, 0x10, 0x7e, 0x2a, 0xd9, 0x6a, 0x66, 0x7f, 0x2d, 0xe4, 0xcc, 0x85,
	0xb5, 0x7f, 0x24, 0x28, 0x35, 0x63, 0xfc, 0x2d, 0xa8, 0x84, 0xe1, 0xc3, 0xfa, 0xd9, 0xa0, 0x23,
	0xe2, 0x2e, 0xaf, 0xf8, 0x45, 0x98, 0x10, 0x77, 0x87, 0xea, 0xf1, 0xa1, 0x2c, 0xa5, 0xe5, 0x7e,
	0x1a, 0xe5, 0x4c, 0xca, 0x4c, 0xb7, 0xd9, 0xeb, 0xc8, 0x59, 0x74, 0x07, 0x94, 0xb4, 0x99, 0x50,
	0x29, 0xe7, 0x57, 0x67, 0x4a, 0x29, 0x04, 0x57, 0xd1, 0x1e, 0xec, 0x24, 0xc0, 0xc1, 0xb0, 0x75,
	0xac, 0xb6, 0xa3, 0x5c, 0xe7, 0xd1, 0x36, 0x6c, 0x24, 0x08, 0x7a, 0xa0, 0x59, 0x40, 0x0a, 0x6c,
	0x26, 0x80, 0xb0, 0x6a, 0xe5, 0x62, 0xed, 0x08, 0x4a, 0x7e, 0xbb, 0xb6, 0xe8, 0xeb, 0x43, 0x83,
	0x1b, 0x68, 0x0f, 0x8a, 0xee, 0x94, 0x5d, 0x60, 0x73, 0x92, 0xcc, 0x5c, 0xc1, 0x0d, 0x7a, 0x1a,
	0x29, 0x90, 0xb3, 0x0c, 0x6e, 0x88, 0x68, 0xdf, 0x08, 0x41, 0x21, 0xa9, 0xfd, 0x9a, 0x01, 0xd9,
	0x37, 0xf5, 0x8c, 0xf2, 0x0b, 0xbf, 0x0a, 0x8e, 0x29, 0xe3, 0xe8, 0x0b, 0xd8, 0xe4, 0x86, 0x37,
	0x22, 0x1c, 0x73, 0xe2, 0x4d, 0xa8, 0x6d, 0x8c, 0xf1, 0x98, 0x32, 0xae, 0x48, 0xd5, 0xec, 0x7e,
	0x59, 0x43, 0x01, 0xa6, 0x87, 0x90, 0xd0, 0x78, 0x04, 0x72, 0x30, 0x6d, 0xb0, 0x3f, 0x6c, 0x02,
	0x76, 0xa6, 0x9a, 0xdd, 0x2f, 0x35, 0x50, 0x3d, 0x1c, 0x4a, 0x57, 0x95, 0xa6, 0xdd, 0xb4, 0xa3,
	0xdf, 0x42, 0xfb, 0x01, 0x94, 0x85, 0xff, 0xcf, 0xe9, 0x6b, 0x2c, 0xfc, 0xf4, 0x33, 0x5e, 0x6a,
	0x6c, 0xcc, 0x55, 0x63, 0x77, 0xd5, 0x4a, 0x6e, 0xec, 0xe2, 0x55, 0x28, 0x32, 0xf2, 0x72, 0x4a,
	0x6c, 0x93, 0x28, 0xb9, 0xaa, 0x74, 0x55, 0xb2, 0x73, 0x29, 0x6a, 0x00, 0x62, 0xc4, 0xbb, 0x24,
	0x1e, 0xf6, 0x88, 0x49, 0xe8, 0x25, 0xb1, 0xb0, 0xc1, 0x95, 0xf5, 0xaa, 0x14, 0x55, 0xa5, 0x1c,
	0xe0, 0x5a, 0x08, 0x37, 0x39, 0xfa, 0x04, 0xc0, 0xf3, 0xf5, 0x19, 0xc7, 0xd4, 0x52, 0xe4, 0xaa,
	0x14, 0x55, 0xdf, 0x5a, 0x28, 0x57, 0x2d, 0xb4, 0x03, 0x79, 0x6b, 0x46, 0x7c, 0x42, 0x25, 0x66,
	0x6c, 0xd5, 0x9a, 0x11, 0xd5, 0xaa, 0xfd, 0x9c, 0x05, 0x65, 0x1e, 0xd5, 0x54, 0x63, 0x5c, 0x3f,
	0xba, 0x1d, 0xa8, 0x4c, 0xe6, 0x56, 0xf0, 0xbc, 0x2b, 0x32, 0x22, 0x46, 0xca, 0x3c, 0x46, 0x8b,
	0xc7, 0x68, 0xf2, 0x64, 0xf1, 0xe0, 0x1d, 0x58, 0x63, 0x2f, 0xa8, 0x8b, 0xa7, 0xd4, 0x62, 0x4a,
	0x56, 0x9c, 0x56, 0xf4, 0x05, 0x43, 0x6a, 0xb1, 0x74, 0x0e, 0x72, 0x1f, 0x90, 0x83, 0xd5, 0xa5,
	0x39, 0xe8, 0x02, 0x4a, 0xb9, 0xcf, 0x94, 0xbc, 0x28, 0x8f, 0x77, 0xfb, 0x5f, 0x59, 0xf4, 0x9f,
	0xfd, 0x4f, 0xc9, 0xfc, 0x4e, 0x82, 0xf5, 0x96, 0xc1, 0xcd, 0x8b, 0xa0, 0x82, 0x55, 0xfb, 0xdc,
	0x41, 0x9f, 0x41, 0x29, 0xac, 0x77, 0x31, 0xc5, 0xe2, 0x4d, 0x07, 0x01, 0x20, 0x86, 0x92, 0x02,
	0x39, 0x6a, 0x9f, 0x3b, 0xc9, 0xbe, 0xf3, 0x25, 0xa8, 0x0e, 0xc5, 0x29, 0xf3, 0xfc, 0xbc, 0x07,
	0xa9, 0x58, 0xde, 0x28, 0x11, 0xa7, 0xf6, 0x83, 0x04, 0x28, 0x00, 0xfc, 0x9a, 0xf2, 0x03, 0x7f,
	0x1d, 0x3f, 0xbe, 0x84, 0x8d, 0x90, 0x46, 0x5e, 0x71, 0x62, 0x5b, 0x58, 0xc4, 0x36, 0xe1, 0x56,
	0x25, 0x20, 0x74, 0x04, 0x2e, 0x92, 0x71, 0x6d, 0x1f, 0x7f, 0x94, 0x60, 0x3b, 0xe9, 0x63, 0x6b,
	0xd6, 0xf5, 0xdf, 0x1d, 0xaa, 0xe5, 0x8f, 0xa8, 0xe0, 0x09, 0xb2, 0xb0, 0x5c, 0x0a, 0xa3, 0x90,
	0xb0, 0x70, 0x93, 0xcc, 0xf5, 0x6e, 0x92, 0xfd, 0xef, 0x9b, 0x24, 0x2a, 0x3f, 0x97, 0xac, 0xfc,
	0xda, 0x37, 0x80, 0x74, 0xcf, 0xb0, 0xd9, 0x84, 0x32, 0x46, 0x1d, 0x7b, 0x60, 0x98, 0x2f, 0x88,
	0xdf, 0x2c, 0x79, 0xc3, 0x4d, 0xb9, 0xbb, 0x6a, 0xb8, 0xbe, 0xb3, 0xbb, 0x50, 0x70, 0x8d, 0xd9,
	0xd8, 0x31, 0xac, 0x44, 0x0c, 0xe7, 0xc2, 0x5a, 0x01, 0x56, 0x3b, 0x13, 0x97, 0xcf, 0x6a, 0x13,
	0x58, 0x0b, 0x22, 0xa2, 0x91, 0x97, 0xf3, 0xf5, 0x29, 0x2d, 0xac, 0x4f, 0x74, 0x17, 0xd6, 0x82,
	0x15, 0x8b, 0x17, 0x96, 0x6b, 0xb8, 0x79, 0xd3, 0xd1, 0xc9, 0x2e, 0x8f, 0x4e, 0xed, 0x00, 0xca,
	0xc1, 0x71, 0x83, 0xc0, 0x91, 0xb8, 0xa3, 0xd2, 0x12, 0x47, 0x1b, 0x3f, 0xe5, 0x21, 0x7c, 0x26,
	0xa2, 0xcf, 0x21, 0x1f, 0xe8, 0xa2, 0x4a, 0x32, 0xcb, 0x1a, 0x79, 0x79, 0xbb, 0x3c, 0x17, 0x05,
	0xd7, 0x5a, 0x41, 0x75, 0xc8, 0x3d, 0xa1, 0xe6, 0x0b, 0xb4, 0x95, 0xe4, 0x86, 0xe7, 0x2e, 0xe5,
	0xfb, 0x13, 0xe4, 0xbd, 0xf9, 0xf7, 0x61, 0x4d, 0xf4, 0x9c, 0x50, 0xda, 0x9e, 0xa3, 0x0b, 0x6d,
	0x98, 0x56, 0x3b, 0x81, 0xa2, 0x46, 0x46, 0x9d, 0x4b, 0x62, 0x73, 0xd4, 0xa8, 0xfb, 0xf5, 0x29,
	0x0a, 0x50, 0x0b, 0xdf, 0xbd, 0x1a, 0x19, 0x2d, 0x15, 0x52, 0xc6, 0x85, 0x4e, 0xda, 0xe0, 0x19,
	0x94, 0xc5, 0xa1, 0x91, 0xd5, 0x07, 0xcb, 0xad, 0x0a, 0xd2, 0xf5, 0x4c, 0x3f, 0x80, 0xb2, 0x18,
	0xaa, 0x51, 0x8f, 0xbc, 0x6f, 0x6c, 0xbe, 0x82, 0xf5, 0x80, 0xf1, 0x41, 0xaa, 0xc1, 0x99, 0xed,
	0x0b, 0xc3, 0xb6, 0xc9, 0xf8, 0x1a, 0xaa, 0x5f, 0xc3, 0xcd, 0x64, 0x73, 0xa3, 0xdb, 0x49, 0xcd,
	0xf8, 0x60, 0x4a, 0xab, 0xab, 0xb0, 0xb5, 0x74, 0x36, 0xa0, 0xbd, 0xe5, 0x56, 0xa2, 0xbb, 0xa5,
	0x4d, 0x3d, 0x0a, 0x2e, 0x11, 0x3d, 0x59, 0x18, 0x47, 0x4a, 0x7c, 0x4d, 0xc5, 0xdf, 0x32, 0x69,
	0xed, 0xde, 0xc2, 0x6a, 0x6e, 0xc7, 0x36, 0x64, 0x75, 0xd1, 0xcc, 0xe2, 0x56, 0x4a, 0x99, 0x6b,
	0xf5, 0xde, 0xbc, 0xdd, 0x95, 0x7e, 0x79, 0xbb, 0x2b, 0xfd, 0xfe, 0x76, 0x57, 0xfa, 0xfe, 0x8f,
	0xdd, 0x95, 0x6f, 0x1f, 0x8e, 0x9c, 0xb1, 0x61, 0x8f, 0xea, 0xf7, 0x1b, 0x9c, 0xd7, 0x4d, 0x67,
	0x12, 0xfc, 0x59, 0x33, 0x9d, 0xf1, 0x81, 0xbf, 0x9c, 0xa8, 0x49, 0x58, 0xe2, 0xdf, 0xda, 0xc3,
	0xf8, 0xc7, 0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xb3, 0xad, 0x86, 0xf9, 0xde, 0x0d, 0x00, 0x00,
}
