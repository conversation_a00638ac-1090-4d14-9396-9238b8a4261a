// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/revenue-api-go/revenue-api-go.proto

package revenue_api_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRevenueApiGoClient is a mock of RevenueApiGoClient interface.
type MockRevenueApiGoClient struct {
	ctrl     *gomock.Controller
	recorder *MockRevenueApiGoClientMockRecorder
}

// MockRevenueApiGoClientMockRecorder is the mock recorder for MockRevenueApiGoClient.
type MockRevenueApiGoClientMockRecorder struct {
	mock *MockRevenueApiGoClient
}

// NewMockRevenueApiGoClient creates a new mock instance.
func NewMockRevenueApiGoClient(ctrl *gomock.Controller) *MockRevenueApiGoClient {
	mock := &MockRevenueApiGoClient{ctrl: ctrl}
	mock.recorder = &MockRevenueApiGoClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRevenueApiGoClient) EXPECT() *MockRevenueApiGoClientMockRecorder {
	return m.recorder
}

// BatchGetRevenueAwardInfosByIds mocks base method.
func (m *MockRevenueApiGoClient) BatchGetRevenueAwardInfosByIds(ctx context.Context, in *BatchGetRevenueAwardInfosByIdsReq, opts ...grpc.CallOption) (*BatchGetRevenueAwardInfosByIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetRevenueAwardInfosByIds", varargs...)
	ret0, _ := ret[0].(*BatchGetRevenueAwardInfosByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRevenueAwardInfosByIds indicates an expected call of BatchGetRevenueAwardInfosByIds.
func (mr *MockRevenueApiGoClientMockRecorder) BatchGetRevenueAwardInfosByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRevenueAwardInfosByIds", reflect.TypeOf((*MockRevenueApiGoClient)(nil).BatchGetRevenueAwardInfosByIds), varargs...)
}

// BatchGetRevenueMicInfo mocks base method.
func (m *MockRevenueApiGoClient) BatchGetRevenueMicInfo(ctx context.Context, in *BatchGetRevenueMicInfoReq, opts ...grpc.CallOption) (*BatchGetRevenueMicInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetRevenueMicInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetRevenueMicInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRevenueMicInfo indicates an expected call of BatchGetRevenueMicInfo.
func (mr *MockRevenueApiGoClientMockRecorder) BatchGetRevenueMicInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRevenueMicInfo", reflect.TypeOf((*MockRevenueApiGoClient)(nil).BatchGetRevenueMicInfo), varargs...)
}

// CheckCanModifySex mocks base method.
func (m *MockRevenueApiGoClient) CheckCanModifySex(ctx context.Context, in *CheckCanModifySexReq, opts ...grpc.CallOption) (*CheckCanModifySexResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckCanModifySex", varargs...)
	ret0, _ := ret[0].(*CheckCanModifySexResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCanModifySex indicates an expected call of CheckCanModifySex.
func (mr *MockRevenueApiGoClientMockRecorder) CheckCanModifySex(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanModifySex", reflect.TypeOf((*MockRevenueApiGoClient)(nil).CheckCanModifySex), varargs...)
}

// GetRevenueAwardInfosByType mocks base method.
func (m *MockRevenueApiGoClient) GetRevenueAwardInfosByType(ctx context.Context, in *GetRevenueAwardInfosByTypeReq, opts ...grpc.CallOption) (*GetRevenueAwardInfosByTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRevenueAwardInfosByType", varargs...)
	ret0, _ := ret[0].(*GetRevenueAwardInfosByTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRevenueAwardInfosByType indicates an expected call of GetRevenueAwardInfosByType.
func (mr *MockRevenueApiGoClientMockRecorder) GetRevenueAwardInfosByType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRevenueAwardInfosByType", reflect.TypeOf((*MockRevenueApiGoClient)(nil).GetRevenueAwardInfosByType), varargs...)
}

// GetRevenueEnterChannelInfo mocks base method.
func (m *MockRevenueApiGoClient) GetRevenueEnterChannelInfo(ctx context.Context, in *GetRevenueEnterChannelInfoReq, opts ...grpc.CallOption) (*GetRevenueEnterChannelInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRevenueEnterChannelInfo", varargs...)
	ret0, _ := ret[0].(*GetRevenueEnterChannelInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRevenueEnterChannelInfo indicates an expected call of GetRevenueEnterChannelInfo.
func (mr *MockRevenueApiGoClientMockRecorder) GetRevenueEnterChannelInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRevenueEnterChannelInfo", reflect.TypeOf((*MockRevenueApiGoClient)(nil).GetRevenueEnterChannelInfo), varargs...)
}

// GetRevenueMicInfo mocks base method.
func (m *MockRevenueApiGoClient) GetRevenueMicInfo(ctx context.Context, in *GetRevenueMicInfoReq, opts ...grpc.CallOption) (*GetRevenueMicInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRevenueMicInfo", varargs...)
	ret0, _ := ret[0].(*GetRevenueMicInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRevenueMicInfo indicates an expected call of GetRevenueMicInfo.
func (mr *MockRevenueApiGoClientMockRecorder) GetRevenueMicInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRevenueMicInfo", reflect.TypeOf((*MockRevenueApiGoClient)(nil).GetRevenueMicInfo), varargs...)
}

// GetRevenueUserVisitorRecord mocks base method.
func (m *MockRevenueApiGoClient) GetRevenueUserVisitorRecord(ctx context.Context, in *GetRevenueUserVisitorRecordReq, opts ...grpc.CallOption) (*GetRevenueUserVisitorRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRevenueUserVisitorRecord", varargs...)
	ret0, _ := ret[0].(*GetRevenueUserVisitorRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRevenueUserVisitorRecord indicates an expected call of GetRevenueUserVisitorRecord.
func (mr *MockRevenueApiGoClientMockRecorder) GetRevenueUserVisitorRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRevenueUserVisitorRecord", reflect.TypeOf((*MockRevenueApiGoClient)(nil).GetRevenueUserVisitorRecord), varargs...)
}

// MockRevenueApiGoServer is a mock of RevenueApiGoServer interface.
type MockRevenueApiGoServer struct {
	ctrl     *gomock.Controller
	recorder *MockRevenueApiGoServerMockRecorder
}

// MockRevenueApiGoServerMockRecorder is the mock recorder for MockRevenueApiGoServer.
type MockRevenueApiGoServerMockRecorder struct {
	mock *MockRevenueApiGoServer
}

// NewMockRevenueApiGoServer creates a new mock instance.
func NewMockRevenueApiGoServer(ctrl *gomock.Controller) *MockRevenueApiGoServer {
	mock := &MockRevenueApiGoServer{ctrl: ctrl}
	mock.recorder = &MockRevenueApiGoServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRevenueApiGoServer) EXPECT() *MockRevenueApiGoServerMockRecorder {
	return m.recorder
}

// BatchGetRevenueAwardInfosByIds mocks base method.
func (m *MockRevenueApiGoServer) BatchGetRevenueAwardInfosByIds(ctx context.Context, in *BatchGetRevenueAwardInfosByIdsReq) (*BatchGetRevenueAwardInfosByIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetRevenueAwardInfosByIds", ctx, in)
	ret0, _ := ret[0].(*BatchGetRevenueAwardInfosByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRevenueAwardInfosByIds indicates an expected call of BatchGetRevenueAwardInfosByIds.
func (mr *MockRevenueApiGoServerMockRecorder) BatchGetRevenueAwardInfosByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRevenueAwardInfosByIds", reflect.TypeOf((*MockRevenueApiGoServer)(nil).BatchGetRevenueAwardInfosByIds), ctx, in)
}

// BatchGetRevenueMicInfo mocks base method.
func (m *MockRevenueApiGoServer) BatchGetRevenueMicInfo(ctx context.Context, in *BatchGetRevenueMicInfoReq) (*BatchGetRevenueMicInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetRevenueMicInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetRevenueMicInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRevenueMicInfo indicates an expected call of BatchGetRevenueMicInfo.
func (mr *MockRevenueApiGoServerMockRecorder) BatchGetRevenueMicInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRevenueMicInfo", reflect.TypeOf((*MockRevenueApiGoServer)(nil).BatchGetRevenueMicInfo), ctx, in)
}

// CheckCanModifySex mocks base method.
func (m *MockRevenueApiGoServer) CheckCanModifySex(ctx context.Context, in *CheckCanModifySexReq) (*CheckCanModifySexResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCanModifySex", ctx, in)
	ret0, _ := ret[0].(*CheckCanModifySexResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCanModifySex indicates an expected call of CheckCanModifySex.
func (mr *MockRevenueApiGoServerMockRecorder) CheckCanModifySex(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanModifySex", reflect.TypeOf((*MockRevenueApiGoServer)(nil).CheckCanModifySex), ctx, in)
}

// GetRevenueAwardInfosByType mocks base method.
func (m *MockRevenueApiGoServer) GetRevenueAwardInfosByType(ctx context.Context, in *GetRevenueAwardInfosByTypeReq) (*GetRevenueAwardInfosByTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRevenueAwardInfosByType", ctx, in)
	ret0, _ := ret[0].(*GetRevenueAwardInfosByTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRevenueAwardInfosByType indicates an expected call of GetRevenueAwardInfosByType.
func (mr *MockRevenueApiGoServerMockRecorder) GetRevenueAwardInfosByType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRevenueAwardInfosByType", reflect.TypeOf((*MockRevenueApiGoServer)(nil).GetRevenueAwardInfosByType), ctx, in)
}

// GetRevenueEnterChannelInfo mocks base method.
func (m *MockRevenueApiGoServer) GetRevenueEnterChannelInfo(ctx context.Context, in *GetRevenueEnterChannelInfoReq) (*GetRevenueEnterChannelInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRevenueEnterChannelInfo", ctx, in)
	ret0, _ := ret[0].(*GetRevenueEnterChannelInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRevenueEnterChannelInfo indicates an expected call of GetRevenueEnterChannelInfo.
func (mr *MockRevenueApiGoServerMockRecorder) GetRevenueEnterChannelInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRevenueEnterChannelInfo", reflect.TypeOf((*MockRevenueApiGoServer)(nil).GetRevenueEnterChannelInfo), ctx, in)
}

// GetRevenueMicInfo mocks base method.
func (m *MockRevenueApiGoServer) GetRevenueMicInfo(ctx context.Context, in *GetRevenueMicInfoReq) (*GetRevenueMicInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRevenueMicInfo", ctx, in)
	ret0, _ := ret[0].(*GetRevenueMicInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRevenueMicInfo indicates an expected call of GetRevenueMicInfo.
func (mr *MockRevenueApiGoServerMockRecorder) GetRevenueMicInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRevenueMicInfo", reflect.TypeOf((*MockRevenueApiGoServer)(nil).GetRevenueMicInfo), ctx, in)
}

// GetRevenueUserVisitorRecord mocks base method.
func (m *MockRevenueApiGoServer) GetRevenueUserVisitorRecord(ctx context.Context, in *GetRevenueUserVisitorRecordReq) (*GetRevenueUserVisitorRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRevenueUserVisitorRecord", ctx, in)
	ret0, _ := ret[0].(*GetRevenueUserVisitorRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRevenueUserVisitorRecord indicates an expected call of GetRevenueUserVisitorRecord.
func (mr *MockRevenueApiGoServerMockRecorder) GetRevenueUserVisitorRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRevenueUserVisitorRecord", reflect.TypeOf((*MockRevenueApiGoServer)(nil).GetRevenueUserVisitorRecord), ctx, in)
}
