// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/guild-cooperation (interfaces: GuildCooperationClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	guild_cooperation "golang.52tt.com/protocol/services/guild-cooperation"
	grpc "google.golang.org/grpc"
)

// MockGuildCooperationClient is a mock of GuildCooperationClient interface.
type MockGuildCooperationClient struct {
	ctrl     *gomock.Controller
	recorder *MockGuildCooperationClientMockRecorder
}

// MockGuildCooperationClientMockRecorder is the mock recorder for MockGuildCooperationClient.
type MockGuildCooperationClientMockRecorder struct {
	mock *MockGuildCooperationClient
}

// NewMockGuildCooperationClient creates a new mock instance.
func NewMockGuildCooperationClient(ctrl *gomock.Controller) *MockGuildCooperationClient {
	mock := &MockGuildCooperationClient{ctrl: ctrl}
	mock.recorder = &MockGuildCooperationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGuildCooperationClient) EXPECT() *MockGuildCooperationClientMockRecorder {
	return m.recorder
}

// AddCooperationGuild mocks base method.
func (m *MockGuildCooperationClient) AddCooperationGuild(arg0 context.Context, arg1 *guild_cooperation.AddCooperationGuildReq, arg2 ...grpc.CallOption) (*guild_cooperation.AddCooperationGuildResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddCooperationGuild", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.AddCooperationGuildResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCooperationGuild indicates an expected call of AddCooperationGuild.
func (mr *MockGuildCooperationClientMockRecorder) AddCooperationGuild(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCooperationGuild", reflect.TypeOf((*MockGuildCooperationClient)(nil).AddCooperationGuild), varargs...)
}

// ApplyCooperation mocks base method.
func (m *MockGuildCooperationClient) ApplyCooperation(arg0 context.Context, arg1 *guild_cooperation.ApplyCooperationReq, arg2 ...grpc.CallOption) (*guild_cooperation.ApplyCooperationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplyCooperation", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.ApplyCooperationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyCooperation indicates an expected call of ApplyCooperation.
func (mr *MockGuildCooperationClientMockRecorder) ApplyCooperation(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyCooperation", reflect.TypeOf((*MockGuildCooperationClient)(nil).ApplyCooperation), varargs...)
}

// ApproveApplication mocks base method.
func (m *MockGuildCooperationClient) ApproveApplication(arg0 context.Context, arg1 *guild_cooperation.ApproveApplicationReq, arg2 ...grpc.CallOption) (*guild_cooperation.ApproveApplicationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApproveApplication", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.ApproveApplicationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApproveApplication indicates an expected call of ApproveApplication.
func (mr *MockGuildCooperationClientMockRecorder) ApproveApplication(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApproveApplication", reflect.TypeOf((*MockGuildCooperationClient)(nil).ApproveApplication), varargs...)
}

// BatchAddCooperationGuild mocks base method.
func (m *MockGuildCooperationClient) BatchAddCooperationGuild(arg0 context.Context, arg1 *guild_cooperation.BatchAddCooperationGuildReq, arg2 ...grpc.CallOption) (*guild_cooperation.BatchAddCooperationGuildResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchAddCooperationGuild", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.BatchAddCooperationGuildResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddCooperationGuild indicates an expected call of BatchAddCooperationGuild.
func (mr *MockGuildCooperationClientMockRecorder) BatchAddCooperationGuild(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddCooperationGuild", reflect.TypeOf((*MockGuildCooperationClient)(nil).BatchAddCooperationGuild), varargs...)
}

// CreateApplyLimit mocks base method.
func (m *MockGuildCooperationClient) CreateApplyLimit(arg0 context.Context, arg1 *guild_cooperation.CreateApplyLimitReq, arg2 ...grpc.CallOption) (*guild_cooperation.CreateApplyLimitResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateApplyLimit", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.CreateApplyLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateApplyLimit indicates an expected call of CreateApplyLimit.
func (mr *MockGuildCooperationClientMockRecorder) CreateApplyLimit(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateApplyLimit", reflect.TypeOf((*MockGuildCooperationClient)(nil).CreateApplyLimit), varargs...)
}

// CreateGuild mocks base method.
func (m *MockGuildCooperationClient) CreateGuild(arg0 context.Context, arg1 *guild_cooperation.CreateGuildReq, arg2 ...grpc.CallOption) (*guild_cooperation.CreateGuildResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateGuild", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.CreateGuildResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGuild indicates an expected call of CreateGuild.
func (mr *MockGuildCooperationClientMockRecorder) CreateGuild(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGuild", reflect.TypeOf((*MockGuildCooperationClient)(nil).CreateGuild), varargs...)
}

// DelCooperationGuild mocks base method.
func (m *MockGuildCooperationClient) DelCooperationGuild(arg0 context.Context, arg1 *guild_cooperation.DelCooperationGuildReq, arg2 ...grpc.CallOption) (*guild_cooperation.DelCooperationGuildResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelCooperationGuild", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.DelCooperationGuildResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelCooperationGuild indicates an expected call of DelCooperationGuild.
func (mr *MockGuildCooperationClientMockRecorder) DelCooperationGuild(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCooperationGuild", reflect.TypeOf((*MockGuildCooperationClient)(nil).DelCooperationGuild), varargs...)
}

// DeleteApplyLimit mocks base method.
func (m *MockGuildCooperationClient) DeleteApplyLimit(arg0 context.Context, arg1 *guild_cooperation.DeleteApplyLimitReq, arg2 ...grpc.CallOption) (*guild_cooperation.DeleteApplyLimitResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteApplyLimit", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.DeleteApplyLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteApplyLimit indicates an expected call of DeleteApplyLimit.
func (mr *MockGuildCooperationClientMockRecorder) DeleteApplyLimit(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteApplyLimit", reflect.TypeOf((*MockGuildCooperationClient)(nil).DeleteApplyLimit), varargs...)
}

// DeleteNotification mocks base method.
func (m *MockGuildCooperationClient) DeleteNotification(arg0 context.Context, arg1 *guild_cooperation.DeleteNotificationReq, arg2 ...grpc.CallOption) (*guild_cooperation.DeleteNotificationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteNotification", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.DeleteNotificationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteNotification indicates an expected call of DeleteNotification.
func (mr *MockGuildCooperationClientMockRecorder) DeleteNotification(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNotification", reflect.TypeOf((*MockGuildCooperationClient)(nil).DeleteNotification), varargs...)
}

// EditNotification mocks base method.
func (m *MockGuildCooperationClient) EditNotification(arg0 context.Context, arg1 *guild_cooperation.EditNotificationReq, arg2 ...grpc.CallOption) (*guild_cooperation.EditNotificationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EditNotification", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.EditNotificationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditNotification indicates an expected call of EditNotification.
func (mr *MockGuildCooperationClientMockRecorder) EditNotification(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditNotification", reflect.TypeOf((*MockGuildCooperationClient)(nil).EditNotification), varargs...)
}

// GetAllNoBonusGuild mocks base method.
func (m *MockGuildCooperationClient) GetAllNoBonusGuild(arg0 context.Context, arg1 *guild_cooperation.GetAllNoBonusGuildReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetAllNoBonusGuildResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllNoBonusGuild", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetAllNoBonusGuildResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllNoBonusGuild indicates an expected call of GetAllNoBonusGuild.
func (mr *MockGuildCooperationClientMockRecorder) GetAllNoBonusGuild(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllNoBonusGuild", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetAllNoBonusGuild), varargs...)
}

// GetApplicationInfo mocks base method.
func (m *MockGuildCooperationClient) GetApplicationInfo(arg0 context.Context, arg1 *guild_cooperation.GetApplicationInfoReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetApplicationInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApplicationInfo", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetApplicationInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplicationInfo indicates an expected call of GetApplicationInfo.
func (mr *MockGuildCooperationClientMockRecorder) GetApplicationInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicationInfo", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetApplicationInfo), varargs...)
}

// GetApplications mocks base method.
func (m *MockGuildCooperationClient) GetApplications(arg0 context.Context, arg1 *guild_cooperation.GetApplicationsReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetApplicationsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApplications", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetApplicationsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplications indicates an expected call of GetApplications.
func (mr *MockGuildCooperationClientMockRecorder) GetApplications(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplications", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetApplications), varargs...)
}

// GetApplyLimitList mocks base method.
func (m *MockGuildCooperationClient) GetApplyLimitList(arg0 context.Context, arg1 *guild_cooperation.GetApplyLimitListReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetApplyLimitListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApplyLimitList", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetApplyLimitListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplyLimitList indicates an expected call of GetApplyLimitList.
func (mr *MockGuildCooperationClientMockRecorder) GetApplyLimitList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplyLimitList", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetApplyLimitList), varargs...)
}

// GetCooperationGuildIds mocks base method.
func (m *MockGuildCooperationClient) GetCooperationGuildIds(arg0 context.Context, arg1 *guild_cooperation.GetCooperationGuildIdsReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetCooperationGuildIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCooperationGuildIds", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetCooperationGuildIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCooperationGuildIds indicates an expected call of GetCooperationGuildIds.
func (mr *MockGuildCooperationClientMockRecorder) GetCooperationGuildIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCooperationGuildIds", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetCooperationGuildIds), varargs...)
}

// GetCooperationGuildList mocks base method.
func (m *MockGuildCooperationClient) GetCooperationGuildList(arg0 context.Context, arg1 *guild_cooperation.GetCooperationGuildListReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetCooperationGuildListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCooperationGuildList", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetCooperationGuildListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCooperationGuildList indicates an expected call of GetCooperationGuildList.
func (mr *MockGuildCooperationClientMockRecorder) GetCooperationGuildList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCooperationGuildList", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetCooperationGuildList), varargs...)
}

// GetCooperationInfo mocks base method.
func (m *MockGuildCooperationClient) GetCooperationInfo(arg0 context.Context, arg1 *guild_cooperation.GetCooperationInfoReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetCooperationInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCooperationInfo", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetCooperationInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCooperationInfo indicates an expected call of GetCooperationInfo.
func (mr *MockGuildCooperationClientMockRecorder) GetCooperationInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCooperationInfo", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetCooperationInfo), varargs...)
}

// GetCooperationInfoByPhone mocks base method.
func (m *MockGuildCooperationClient) GetCooperationInfoByPhone(arg0 context.Context, arg1 *guild_cooperation.GetCooperationInfoByPhoneReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetCooperationInfoByPhoneResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCooperationInfoByPhone", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetCooperationInfoByPhoneResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCooperationInfoByPhone indicates an expected call of GetCooperationInfoByPhone.
func (mr *MockGuildCooperationClientMockRecorder) GetCooperationInfoByPhone(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCooperationInfoByPhone", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetCooperationInfoByPhone), varargs...)
}

// GetCooperationOptList mocks base method.
func (m *MockGuildCooperationClient) GetCooperationOptList(arg0 context.Context, arg1 *guild_cooperation.GetCooperationOptListReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetCooperationOptListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCooperationOptList", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetCooperationOptListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCooperationOptList indicates an expected call of GetCooperationOptList.
func (mr *MockGuildCooperationClientMockRecorder) GetCooperationOptList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCooperationOptList", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetCooperationOptList), varargs...)
}

// GetGuildCooperationHistory mocks base method.
func (m *MockGuildCooperationClient) GetGuildCooperationHistory(arg0 context.Context, arg1 *guild_cooperation.GetGuildCooperationHistoryReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetGuildCooperationHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildCooperationHistory", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetGuildCooperationHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildCooperationHistory indicates an expected call of GetGuildCooperationHistory.
func (mr *MockGuildCooperationClientMockRecorder) GetGuildCooperationHistory(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildCooperationHistory", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetGuildCooperationHistory), varargs...)
}

// GetGuildCooperationHistoryPeriod mocks base method.
func (m *MockGuildCooperationClient) GetGuildCooperationHistoryPeriod(arg0 context.Context, arg1 *guild_cooperation.GetGuildCooperationHistoryPeriodReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetGuildCooperationHistoryPeriodResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildCooperationHistoryPeriod", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetGuildCooperationHistoryPeriodResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildCooperationHistoryPeriod indicates an expected call of GetGuildCooperationHistoryPeriod.
func (mr *MockGuildCooperationClientMockRecorder) GetGuildCooperationHistoryPeriod(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildCooperationHistoryPeriod", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetGuildCooperationHistoryPeriod), varargs...)
}

// GetGuildCooperationInfos mocks base method.
func (m *MockGuildCooperationClient) GetGuildCooperationInfos(arg0 context.Context, arg1 *guild_cooperation.GetGuildCooperationInfosReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetGuildCooperationInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildCooperationInfos", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetGuildCooperationInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildCooperationInfos indicates an expected call of GetGuildCooperationInfos.
func (mr *MockGuildCooperationClientMockRecorder) GetGuildCooperationInfos(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildCooperationInfos", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetGuildCooperationInfos), varargs...)
}

// GetGuildCooperationWithTime mocks base method.
func (m *MockGuildCooperationClient) GetGuildCooperationWithTime(arg0 context.Context, arg1 *guild_cooperation.GetGuildCooperationWithTimeReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetGuildCooperationWithTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildCooperationWithTime", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetGuildCooperationWithTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildCooperationWithTime indicates an expected call of GetGuildCooperationWithTime.
func (mr *MockGuildCooperationClientMockRecorder) GetGuildCooperationWithTime(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildCooperationWithTime", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetGuildCooperationWithTime), varargs...)
}

// GetGuildInfo mocks base method.
func (m *MockGuildCooperationClient) GetGuildInfo(arg0 context.Context, arg1 *guild_cooperation.GetGuildInfoReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetGuildInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGuildInfo", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetGuildInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildInfo indicates an expected call of GetGuildInfo.
func (mr *MockGuildCooperationClientMockRecorder) GetGuildInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildInfo", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetGuildInfo), varargs...)
}

// GetNotificationList mocks base method.
func (m *MockGuildCooperationClient) GetNotificationList(arg0 context.Context, arg1 *guild_cooperation.GetNotificationListReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetNotificationListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNotificationList", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetNotificationListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNotificationList indicates an expected call of GetNotificationList.
func (mr *MockGuildCooperationClientMockRecorder) GetNotificationList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotificationList", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetNotificationList), varargs...)
}

// GetOperationHistory mocks base method.
func (m *MockGuildCooperationClient) GetOperationHistory(arg0 context.Context, arg1 *guild_cooperation.GetOperationHistoryReq, arg2 ...grpc.CallOption) (*guild_cooperation.GetOperationHistoryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOperationHistory", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.GetOperationHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOperationHistory indicates an expected call of GetOperationHistory.
func (mr *MockGuildCooperationClientMockRecorder) GetOperationHistory(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOperationHistory", reflect.TypeOf((*MockGuildCooperationClient)(nil).GetOperationHistory), varargs...)
}

// RejectApplication mocks base method.
func (m *MockGuildCooperationClient) RejectApplication(arg0 context.Context, arg1 *guild_cooperation.RejectApplicationReq, arg2 ...grpc.CallOption) (*guild_cooperation.RejectApplicationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RejectApplication", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.RejectApplicationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RejectApplication indicates an expected call of RejectApplication.
func (mr *MockGuildCooperationClientMockRecorder) RejectApplication(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RejectApplication", reflect.TypeOf((*MockGuildCooperationClient)(nil).RejectApplication), varargs...)
}

// SetNoBonusGuild mocks base method.
func (m *MockGuildCooperationClient) SetNoBonusGuild(arg0 context.Context, arg1 *guild_cooperation.SetNoBonusGuildReq, arg2 ...grpc.CallOption) (*guild_cooperation.SetNoBonusGuildResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetNoBonusGuild", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.SetNoBonusGuildResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetNoBonusGuild indicates an expected call of SetNoBonusGuild.
func (mr *MockGuildCooperationClientMockRecorder) SetNoBonusGuild(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNoBonusGuild", reflect.TypeOf((*MockGuildCooperationClient)(nil).SetNoBonusGuild), varargs...)
}

// SubmitApplication mocks base method.
func (m *MockGuildCooperationClient) SubmitApplication(arg0 context.Context, arg1 *guild_cooperation.SubmitApplicationReq, arg2 ...grpc.CallOption) (*guild_cooperation.SubmitApplicationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitApplication", varargs...)
	ret0, _ := ret[0].(*guild_cooperation.SubmitApplicationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitApplication indicates an expected call of SubmitApplication.
func (mr *MockGuildCooperationClientMockRecorder) SubmitApplication(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitApplication", reflect.TypeOf((*MockGuildCooperationClient)(nil).SubmitApplication), varargs...)
}
