// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/super-player-privilege (interfaces: SuperPlayerPrivilegeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	super_player_privilege "golang.52tt.com/protocol/services/super-player-privilege"
	grpc "google.golang.org/grpc"
)

// MockSuperPlayerPrivilegeClient is a mock of SuperPlayerPrivilegeClient interface.
type MockSuperPlayerPrivilegeClient struct {
	ctrl     *gomock.Controller
	recorder *MockSuperPlayerPrivilegeClientMockRecorder
}

// MockSuperPlayerPrivilegeClientMockRecorder is the mock recorder for MockSuperPlayerPrivilegeClient.
type MockSuperPlayerPrivilegeClientMockRecorder struct {
	mock *MockSuperPlayerPrivilegeClient
}

// NewMockSuperPlayerPrivilegeClient creates a new mock instance.
func NewMockSuperPlayerPrivilegeClient(ctrl *gomock.Controller) *MockSuperPlayerPrivilegeClient {
	mock := &MockSuperPlayerPrivilegeClient{ctrl: ctrl}
	mock.recorder = &MockSuperPlayerPrivilegeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSuperPlayerPrivilegeClient) EXPECT() *MockSuperPlayerPrivilegeClientMockRecorder {
	return m.recorder
}

// AddUserSpecialConcern mocks base method.
func (m *MockSuperPlayerPrivilegeClient) AddUserSpecialConcern(arg0 context.Context, arg1 *super_player_privilege.AddUserSpecialConcernReq, arg2 ...grpc.CallOption) (*super_player_privilege.AddUserSpecialConcernResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserSpecialConcern", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.AddUserSpecialConcernResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserSpecialConcern indicates an expected call of AddUserSpecialConcern.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) AddUserSpecialConcern(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserSpecialConcern", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).AddUserSpecialConcern), varargs...)
}

// BatchGetUserFellowVisibleProfile mocks base method.
func (m *MockSuperPlayerPrivilegeClient) BatchGetUserFellowVisibleProfile(arg0 context.Context, arg1 *super_player_privilege.BatchGetFellowVisibleProfilesReq, arg2 ...grpc.CallOption) (*super_player_privilege.BatchGetFellowVisibleProfilesResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserFellowVisibleProfile", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.BatchGetFellowVisibleProfilesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserFellowVisibleProfile indicates an expected call of BatchGetUserFellowVisibleProfile.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) BatchGetUserFellowVisibleProfile(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserFellowVisibleProfile", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).BatchGetUserFellowVisibleProfile), varargs...)
}

// DelUserSpecialConcern mocks base method.
func (m *MockSuperPlayerPrivilegeClient) DelUserSpecialConcern(arg0 context.Context, arg1 *super_player_privilege.DelUserSpecialConcernReq, arg2 ...grpc.CallOption) (*super_player_privilege.DelUserSpecialConcernResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelUserSpecialConcern", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.DelUserSpecialConcernResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelUserSpecialConcern indicates an expected call of DelUserSpecialConcern.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) DelUserSpecialConcern(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserSpecialConcern", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).DelUserSpecialConcern), varargs...)
}

// GetIMPrivilegeCount mocks base method.
func (m *MockSuperPlayerPrivilegeClient) GetIMPrivilegeCount(arg0 context.Context, arg1 *super_player_privilege.GetIMPrivilegeCountReq, arg2 ...grpc.CallOption) (*super_player_privilege.GetIMPrivilegeCountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIMPrivilegeCount", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.GetIMPrivilegeCountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIMPrivilegeCount indicates an expected call of GetIMPrivilegeCount.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) GetIMPrivilegeCount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIMPrivilegeCount", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).GetIMPrivilegeCount), varargs...)
}

// GetPrivilegeTarget mocks base method.
func (m *MockSuperPlayerPrivilegeClient) GetPrivilegeTarget(arg0 context.Context, arg1 *super_player_privilege.GetPrivilegeTargetReq, arg2 ...grpc.CallOption) (*super_player_privilege.GetPrivilegeTargetResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPrivilegeTarget", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.GetPrivilegeTargetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrivilegeTarget indicates an expected call of GetPrivilegeTarget.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) GetPrivilegeTarget(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrivilegeTarget", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).GetPrivilegeTarget), varargs...)
}

// GetUserBeSpecialConcern mocks base method.
func (m *MockSuperPlayerPrivilegeClient) GetUserBeSpecialConcern(arg0 context.Context, arg1 *super_player_privilege.GetUserBeSpecialConcernReq, arg2 ...grpc.CallOption) (*super_player_privilege.GetUserBeSpecialConcernResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserBeSpecialConcern", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.GetUserBeSpecialConcernResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserBeSpecialConcern indicates an expected call of GetUserBeSpecialConcern.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) GetUserBeSpecialConcern(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBeSpecialConcern", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).GetUserBeSpecialConcern), varargs...)
}

// GetUserSVIPPrivilegeProfile mocks base method.
func (m *MockSuperPlayerPrivilegeClient) GetUserSVIPPrivilegeProfile(arg0 context.Context, arg1 *super_player_privilege.GetUserSVIPPrivilegeProfileReq, arg2 ...grpc.CallOption) (*super_player_privilege.GetUserSVIPPrivilegeProfileResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSVIPPrivilegeProfile", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.GetUserSVIPPrivilegeProfileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSVIPPrivilegeProfile indicates an expected call of GetUserSVIPPrivilegeProfile.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) GetUserSVIPPrivilegeProfile(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSVIPPrivilegeProfile", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).GetUserSVIPPrivilegeProfile), varargs...)
}

// GetUserSpecialConcern mocks base method.
func (m *MockSuperPlayerPrivilegeClient) GetUserSpecialConcern(arg0 context.Context, arg1 *super_player_privilege.GetUserSpecialConcernReq, arg2 ...grpc.CallOption) (*super_player_privilege.GetUserSpecialConcernResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSpecialConcern", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.GetUserSpecialConcernResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSpecialConcern indicates an expected call of GetUserSpecialConcern.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) GetUserSpecialConcern(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSpecialConcern", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).GetUserSpecialConcern), varargs...)
}

// SetUserSVIPPrivilegeProfile mocks base method.
func (m *MockSuperPlayerPrivilegeClient) SetUserSVIPPrivilegeProfile(arg0 context.Context, arg1 *super_player_privilege.SetUserSVIPPrivilegeProfileReq, arg2 ...grpc.CallOption) (*super_player_privilege.SetUserSVIPPrivilegeProfileResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserSVIPPrivilegeProfile", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.SetUserSVIPPrivilegeProfileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserSVIPPrivilegeProfile indicates an expected call of SetUserSVIPPrivilegeProfile.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) SetUserSVIPPrivilegeProfile(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSVIPPrivilegeProfile", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).SetUserSVIPPrivilegeProfile), varargs...)
}

// UseIMPrivilege mocks base method.
func (m *MockSuperPlayerPrivilegeClient) UseIMPrivilege(arg0 context.Context, arg1 *super_player_privilege.UseIMPrivilegeReq, arg2 ...grpc.CallOption) (*super_player_privilege.UseIMPrivilegeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UseIMPrivilege", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.UseIMPrivilegeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UseIMPrivilege indicates an expected call of UseIMPrivilege.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) UseIMPrivilege(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseIMPrivilege", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).UseIMPrivilege), varargs...)
}

// UseSVIPStealthAhead mocks base method.
func (m *MockSuperPlayerPrivilegeClient) UseSVIPStealthAhead(arg0 context.Context, arg1 *super_player_privilege.UseSVIPStealthAheadReq, arg2 ...grpc.CallOption) (*super_player_privilege.UseSVIPStealthAheadResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UseSVIPStealthAhead", varargs...)
	ret0, _ := ret[0].(*super_player_privilege.UseSVIPStealthAheadResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UseSVIPStealthAhead indicates an expected call of UseSVIPStealthAhead.
func (mr *MockSuperPlayerPrivilegeClientMockRecorder) UseSVIPStealthAhead(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseSVIPStealthAhead", reflect.TypeOf((*MockSuperPlayerPrivilegeClient)(nil).UseSVIPStealthAhead), varargs...)
}
