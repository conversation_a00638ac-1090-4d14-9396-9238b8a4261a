// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/userpresent-go (interfaces: UserPresentGOClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
	grpc "google.golang.org/grpc"
)

// MockUserPresentGOClient is a mock of UserPresentGOClient interface.
type MockUserPresentGOClient struct {
	ctrl     *gomock.Controller
	recorder *MockUserPresentGOClientMockRecorder
}

// MockUserPresentGOClientMockRecorder is the mock recorder for MockUserPresentGOClient.
type MockUserPresentGOClientMockRecorder struct {
	mock *MockUserPresentGOClient
}

// NewMockUserPresentGOClient creates a new mock instance.
func NewMockUserPresentGOClient(ctrl *gomock.Controller) *MockUserPresentGOClient {
	mock := &MockUserPresentGOClient{ctrl: ctrl}
	mock.recorder = &MockUserPresentGOClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserPresentGOClient) EXPECT() *MockUserPresentGOClientMockRecorder {
	return m.recorder
}

// AddChanceItemSource mocks base method.
func (m *MockUserPresentGOClient) AddChanceItemSource(arg0 context.Context, arg1 *userpresent_go.AddChanceItemSourceReq, arg2 ...grpc.CallOption) (*userpresent_go.AddChanceItemSourceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddChanceItemSource", varargs...)
	ret0, _ := ret[0].(*userpresent_go.AddChanceItemSourceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChanceItemSource indicates an expected call of AddChanceItemSource.
func (mr *MockUserPresentGOClientMockRecorder) AddChanceItemSource(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChanceItemSource", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddChanceItemSource), varargs...)
}

// AddDynamicEffectTemplate mocks base method.
func (m *MockUserPresentGOClient) AddDynamicEffectTemplate(arg0 context.Context, arg1 *userpresent_go.AddDynamicEffectTemplateReq, arg2 ...grpc.CallOption) (*userpresent_go.AddDynamicEffectTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddDynamicEffectTemplate", varargs...)
	ret0, _ := ret[0].(*userpresent_go.AddDynamicEffectTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddDynamicEffectTemplate indicates an expected call of AddDynamicEffectTemplate.
func (mr *MockUserPresentGOClientMockRecorder) AddDynamicEffectTemplate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDynamicEffectTemplate", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddDynamicEffectTemplate), varargs...)
}

// AddNamingPresentInfo mocks base method.
func (m *MockUserPresentGOClient) AddNamingPresentInfo(arg0 context.Context, arg1 *userpresent_go.AddNamingPresentInfoReq, arg2 ...grpc.CallOption) (*userpresent_go.AddNamingPresentInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddNamingPresentInfo", varargs...)
	ret0, _ := ret[0].(*userpresent_go.AddNamingPresentInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNamingPresentInfo indicates an expected call of AddNamingPresentInfo.
func (mr *MockUserPresentGOClientMockRecorder) AddNamingPresentInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNamingPresentInfo", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddNamingPresentInfo), varargs...)
}

// AddPresentConfig mocks base method.
func (m *MockUserPresentGOClient) AddPresentConfig(arg0 context.Context, arg1 *userpresent_go.AddPresentConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.AddPresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.AddPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentConfig indicates an expected call of AddPresentConfig.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentConfig), varargs...)
}

// AddPresentConfigBackend mocks base method.
func (m *MockUserPresentGOClient) AddPresentConfigBackend(arg0 context.Context, arg1 *userpresent_go.AddPresentConfigBackendReq, arg2 ...grpc.CallOption) (*userpresent_go.AddPresentConfigBackendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentConfigBackend", varargs...)
	ret0, _ := ret[0].(*userpresent_go.AddPresentConfigBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentConfigBackend indicates an expected call of AddPresentConfigBackend.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentConfigBackend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentConfigBackend", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentConfigBackend), varargs...)
}

// AddPresentEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOClient) AddPresentEffectTemplateConfig(arg0 context.Context, arg1 *userpresent_go.AddPresentEffectTemplateConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.AddPresentEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentEffectTemplateConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.AddPresentEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentEffectTemplateConfig indicates an expected call of AddPresentEffectTemplateConfig.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentEffectTemplateConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentEffectTemplateConfig), varargs...)
}

// AddPresentFlowConfig mocks base method.
func (m *MockUserPresentGOClient) AddPresentFlowConfig(arg0 context.Context, arg1 *userpresent_go.AddPresentFlowConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.AddPresentFlowConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentFlowConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.AddPresentFlowConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentFlowConfig indicates an expected call of AddPresentFlowConfig.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentFlowConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentFlowConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentFlowConfig), varargs...)
}

// AddPresentMarkConfig mocks base method.
func (m *MockUserPresentGOClient) AddPresentMarkConfig(arg0 context.Context, arg1 *userpresent_go.AddPresentMarkConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.AddPresentMarkConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPresentMarkConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.AddPresentMarkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPresentMarkConfig indicates an expected call of AddPresentMarkConfig.
func (mr *MockUserPresentGOClientMockRecorder) AddPresentMarkConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPresentMarkConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).AddPresentMarkConfig), varargs...)
}

// BatchAddPresentMarkConfig mocks base method.
func (m *MockUserPresentGOClient) BatchAddPresentMarkConfig(arg0 context.Context, arg1 *userpresent_go.BatchAddPresentMarkConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.BatchAddPresentMarkConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchAddPresentMarkConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.BatchAddPresentMarkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddPresentMarkConfig indicates an expected call of BatchAddPresentMarkConfig.
func (mr *MockUserPresentGOClientMockRecorder) BatchAddPresentMarkConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddPresentMarkConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).BatchAddPresentMarkConfig), varargs...)
}

// BatchSendPresent mocks base method.
func (m *MockUserPresentGOClient) BatchSendPresent(arg0 context.Context, arg1 *userpresent_go.BatchSendPresentReq, arg2 ...grpc.CallOption) (*userpresent_go.BatchSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchSendPresent", varargs...)
	ret0, _ := ret[0].(*userpresent_go.BatchSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchSendPresent indicates an expected call of BatchSendPresent.
func (mr *MockUserPresentGOClientMockRecorder) BatchSendPresent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSendPresent", reflect.TypeOf((*MockUserPresentGOClient)(nil).BatchSendPresent), varargs...)
}

// ClearScenePresent mocks base method.
func (m *MockUserPresentGOClient) ClearScenePresent(arg0 context.Context, arg1 *userpresent_go.ClearScenePresentReq, arg2 ...grpc.CallOption) (*userpresent_go.ClearScenePresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearScenePresent", varargs...)
	ret0, _ := ret[0].(*userpresent_go.ClearScenePresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearScenePresent indicates an expected call of ClearScenePresent.
func (mr *MockUserPresentGOClientMockRecorder) ClearScenePresent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearScenePresent", reflect.TypeOf((*MockUserPresentGOClient)(nil).ClearScenePresent), varargs...)
}

// DelChanceItemSource mocks base method.
func (m *MockUserPresentGOClient) DelChanceItemSource(arg0 context.Context, arg1 *userpresent_go.DelChanceItemSourceReq, arg2 ...grpc.CallOption) (*userpresent_go.DelChanceItemSourceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelChanceItemSource", varargs...)
	ret0, _ := ret[0].(*userpresent_go.DelChanceItemSourceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelChanceItemSource indicates an expected call of DelChanceItemSource.
func (mr *MockUserPresentGOClientMockRecorder) DelChanceItemSource(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChanceItemSource", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelChanceItemSource), varargs...)
}

// DelDynamicEffectTemplate mocks base method.
func (m *MockUserPresentGOClient) DelDynamicEffectTemplate(arg0 context.Context, arg1 *userpresent_go.DelDynamicEffectTemplateReq, arg2 ...grpc.CallOption) (*userpresent_go.DelDynamicEffectTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelDynamicEffectTemplate", varargs...)
	ret0, _ := ret[0].(*userpresent_go.DelDynamicEffectTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelDynamicEffectTemplate indicates an expected call of DelDynamicEffectTemplate.
func (mr *MockUserPresentGOClientMockRecorder) DelDynamicEffectTemplate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelDynamicEffectTemplate", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelDynamicEffectTemplate), varargs...)
}

// DelNamingPresentInfo mocks base method.
func (m *MockUserPresentGOClient) DelNamingPresentInfo(arg0 context.Context, arg1 *userpresent_go.DelNamingPresentInfoReq, arg2 ...grpc.CallOption) (*userpresent_go.DelNamingPresentInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelNamingPresentInfo", varargs...)
	ret0, _ := ret[0].(*userpresent_go.DelNamingPresentInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelNamingPresentInfo indicates an expected call of DelNamingPresentInfo.
func (mr *MockUserPresentGOClientMockRecorder) DelNamingPresentInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNamingPresentInfo", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelNamingPresentInfo), varargs...)
}

// DelPresentConfig mocks base method.
func (m *MockUserPresentGOClient) DelPresentConfig(arg0 context.Context, arg1 *userpresent_go.DelPresentConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.DelPresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.DelPresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentConfig indicates an expected call of DelPresentConfig.
func (mr *MockUserPresentGOClientMockRecorder) DelPresentConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelPresentConfig), varargs...)
}

// DelPresentConfigBackend mocks base method.
func (m *MockUserPresentGOClient) DelPresentConfigBackend(arg0 context.Context, arg1 *userpresent_go.DelPresentConfigBackendReq, arg2 ...grpc.CallOption) (*userpresent_go.DelPresentConfigBackendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentConfigBackend", varargs...)
	ret0, _ := ret[0].(*userpresent_go.DelPresentConfigBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentConfigBackend indicates an expected call of DelPresentConfigBackend.
func (mr *MockUserPresentGOClientMockRecorder) DelPresentConfigBackend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentConfigBackend", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelPresentConfigBackend), varargs...)
}

// DelPresentEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOClient) DelPresentEffectTemplateConfig(arg0 context.Context, arg1 *userpresent_go.DelPresentEffectTemplateConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.DelPresentEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentEffectTemplateConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.DelPresentEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentEffectTemplateConfig indicates an expected call of DelPresentEffectTemplateConfig.
func (mr *MockUserPresentGOClientMockRecorder) DelPresentEffectTemplateConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelPresentEffectTemplateConfig), varargs...)
}

// DelPresentFlowConfig mocks base method.
func (m *MockUserPresentGOClient) DelPresentFlowConfig(arg0 context.Context, arg1 *userpresent_go.DelPresentFlowConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.DelPresentFlowConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentFlowConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.DelPresentFlowConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentFlowConfig indicates an expected call of DelPresentFlowConfig.
func (mr *MockUserPresentGOClientMockRecorder) DelPresentFlowConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentFlowConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelPresentFlowConfig), varargs...)
}

// DelPresentMarkConfig mocks base method.
func (m *MockUserPresentGOClient) DelPresentMarkConfig(arg0 context.Context, arg1 *userpresent_go.DelPresentMarkConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.DelPresentMarkConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelPresentMarkConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.DelPresentMarkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelPresentMarkConfig indicates an expected call of DelPresentMarkConfig.
func (mr *MockUserPresentGOClientMockRecorder) DelPresentMarkConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPresentMarkConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).DelPresentMarkConfig), varargs...)
}

// GetAllFellowPresent mocks base method.
func (m *MockUserPresentGOClient) GetAllFellowPresent(arg0 context.Context, arg1 *userpresent_go.GetAllFellowPresentReq, arg2 ...grpc.CallOption) (*userpresent_go.GetAllFellowPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllFellowPresent", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetAllFellowPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllFellowPresent indicates an expected call of GetAllFellowPresent.
func (mr *MockUserPresentGOClientMockRecorder) GetAllFellowPresent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllFellowPresent", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetAllFellowPresent), varargs...)
}

// GetDynamicEffectTemplateList mocks base method.
func (m *MockUserPresentGOClient) GetDynamicEffectTemplateList(arg0 context.Context, arg1 *userpresent_go.GetDynamicEffectTemplateListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetDynamicEffectTemplateListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDynamicEffectTemplateList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetDynamicEffectTemplateListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDynamicEffectTemplateList indicates an expected call of GetDynamicEffectTemplateList.
func (mr *MockUserPresentGOClientMockRecorder) GetDynamicEffectTemplateList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDynamicEffectTemplateList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetDynamicEffectTemplateList), varargs...)
}

// GetLivePresentOrderList mocks base method.
func (m *MockUserPresentGOClient) GetLivePresentOrderList(arg0 context.Context, arg1 *userpresent_go.GetLivePresentOrderListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetLivePresentOrderListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLivePresentOrderList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetLivePresentOrderListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivePresentOrderList indicates an expected call of GetLivePresentOrderList.
func (mr *MockUserPresentGOClientMockRecorder) GetLivePresentOrderList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivePresentOrderList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetLivePresentOrderList), varargs...)
}

// GetNamingPresentInfoList mocks base method.
func (m *MockUserPresentGOClient) GetNamingPresentInfoList(arg0 context.Context, arg1 *userpresent_go.GetNamingPresentInfoListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetNamingPresentInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNamingPresentInfoList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetNamingPresentInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamingPresentInfoList indicates an expected call of GetNamingPresentInfoList.
func (mr *MockUserPresentGOClientMockRecorder) GetNamingPresentInfoList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamingPresentInfoList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetNamingPresentInfoList), varargs...)
}

// GetOrderLogByOrderIds mocks base method.
func (m *MockUserPresentGOClient) GetOrderLogByOrderIds(arg0 context.Context, arg1 *userpresent_go.GetOrderLogByOrderIdsReq, arg2 ...grpc.CallOption) (*userpresent_go.GetOrderLogByOrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrderLogByOrderIds", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetOrderLogByOrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderLogByOrderIds indicates an expected call of GetOrderLogByOrderIds.
func (mr *MockUserPresentGOClientMockRecorder) GetOrderLogByOrderIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderLogByOrderIds", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetOrderLogByOrderIds), varargs...)
}

// GetPresentConfigById mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigById(arg0 context.Context, arg1 *userpresent_go.GetPresentConfigByIdReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentConfigByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigById", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigById indicates an expected call of GetPresentConfigById.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigById(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigById", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigById), varargs...)
}

// GetPresentConfigByIdBackend mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigByIdBackend(arg0 context.Context, arg1 *userpresent_go.GetPresentConfigByIdBackendReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentConfigByIdBackendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigByIdBackend", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigByIdBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigByIdBackend indicates an expected call of GetPresentConfigByIdBackend.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigByIdBackend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigByIdBackend", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigByIdBackend), varargs...)
}

// GetPresentConfigList mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigList(arg0 context.Context, arg1 *userpresent_go.GetPresentConfigListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentConfigListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigList indicates an expected call of GetPresentConfigList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigList), varargs...)
}

// GetPresentConfigListBackend mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigListBackend(arg0 context.Context, arg1 *userpresent_go.GetPresentConfigListBackendReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentConfigListBackendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigListBackend", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigListBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListBackend indicates an expected call of GetPresentConfigListBackend.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigListBackend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListBackend", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigListBackend), varargs...)
}

// GetPresentConfigListByIdList mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigListByIdList(arg0 context.Context, arg1 *userpresent_go.GetPresentConfigListByIdListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentConfigListByIdListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigListByIdList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigListByIdListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListByIdList indicates an expected call of GetPresentConfigListByIdList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigListByIdList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListByIdList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigListByIdList), varargs...)
}

// GetPresentConfigListV3 mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigListV3(arg0 context.Context, arg1 *userpresent_go.GetPresentConfigListV3Req, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentConfigListV3Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigListV3", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigListV3Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigListV3 indicates an expected call of GetPresentConfigListV3.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigListV3(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigListV3", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigListV3), varargs...)
}

// GetPresentConfigUpdateTime mocks base method.
func (m *MockUserPresentGOClient) GetPresentConfigUpdateTime(arg0 context.Context, arg1 *userpresent_go.GetPresentConfigUpdateTimeReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentConfigUpdateTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentConfigUpdateTime", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentConfigUpdateTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentConfigUpdateTime indicates an expected call of GetPresentConfigUpdateTime.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentConfigUpdateTime(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentConfigUpdateTime", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentConfigUpdateTime), varargs...)
}

// GetPresentDETConfigById mocks base method.
func (m *MockUserPresentGOClient) GetPresentDETConfigById(arg0 context.Context, arg1 *userpresent_go.GetPresentDETConfigByIdReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentDETConfigByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentDETConfigById", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentDETConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentDETConfigById indicates an expected call of GetPresentDETConfigById.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentDETConfigById(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDETConfigById", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentDETConfigById), varargs...)
}

// GetPresentDynamicEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOClient) GetPresentDynamicEffectTemplateConfig(arg0 context.Context, arg1 *userpresent_go.GetPresentDynamicEffectTemplateConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentDynamicEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentDynamicEffectTemplateConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentDynamicEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentDynamicEffectTemplateConfig indicates an expected call of GetPresentDynamicEffectTemplateConfig.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentDynamicEffectTemplateConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDynamicEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentDynamicEffectTemplateConfig), varargs...)
}

// GetPresentDynamicTemplateConfUpdateTime mocks base method.
func (m *MockUserPresentGOClient) GetPresentDynamicTemplateConfUpdateTime(arg0 context.Context, arg1 *userpresent_go.GetPresentDynamicTemplateConfUpdateTimeReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentDynamicTemplateConfUpdateTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentDynamicTemplateConfUpdateTime", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentDynamicTemplateConfUpdateTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentDynamicTemplateConfUpdateTime indicates an expected call of GetPresentDynamicTemplateConfUpdateTime.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentDynamicTemplateConfUpdateTime(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentDynamicTemplateConfUpdateTime", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentDynamicTemplateConfUpdateTime), varargs...)
}

// GetPresentEffectTemplateConfigList mocks base method.
func (m *MockUserPresentGOClient) GetPresentEffectTemplateConfigList(arg0 context.Context, arg1 *userpresent_go.GetPresentEffectTemplateConfigListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentEffectTemplateConfigListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentEffectTemplateConfigList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentEffectTemplateConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentEffectTemplateConfigList indicates an expected call of GetPresentEffectTemplateConfigList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentEffectTemplateConfigList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentEffectTemplateConfigList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentEffectTemplateConfigList), varargs...)
}

// GetPresentFlowConfigById mocks base method.
func (m *MockUserPresentGOClient) GetPresentFlowConfigById(arg0 context.Context, arg1 *userpresent_go.GetPresentFlowConfigByIdReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentFlowConfigByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentFlowConfigById", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentFlowConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigById indicates an expected call of GetPresentFlowConfigById.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentFlowConfigById(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigById", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentFlowConfigById), varargs...)
}

// GetPresentFlowConfigList mocks base method.
func (m *MockUserPresentGOClient) GetPresentFlowConfigList(arg0 context.Context, arg1 *userpresent_go.GetPresentFlowConfigListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentFlowConfigListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentFlowConfigList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentFlowConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigList indicates an expected call of GetPresentFlowConfigList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentFlowConfigList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentFlowConfigList), varargs...)
}

// GetPresentFlowConfigUpdateTime mocks base method.
func (m *MockUserPresentGOClient) GetPresentFlowConfigUpdateTime(arg0 context.Context, arg1 *userpresent_go.GetPresentFlowConfigUpdateTimeReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentFlowConfigUpdateTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentFlowConfigUpdateTime", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentFlowConfigUpdateTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlowConfigUpdateTime indicates an expected call of GetPresentFlowConfigUpdateTime.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentFlowConfigUpdateTime(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlowConfigUpdateTime", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentFlowConfigUpdateTime), varargs...)
}

// GetPresentMarkConfigList mocks base method.
func (m *MockUserPresentGOClient) GetPresentMarkConfigList(arg0 context.Context, arg1 *userpresent_go.GetPresentMarkConfigListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentMarkConfigListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentMarkConfigList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentMarkConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentMarkConfigList indicates an expected call of GetPresentMarkConfigList.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentMarkConfigList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentMarkConfigList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentMarkConfigList), varargs...)
}

// GetPresentMarkIconByPresentId mocks base method.
func (m *MockUserPresentGOClient) GetPresentMarkIconByPresentId(arg0 context.Context, arg1 *userpresent_go.GetPresentMarkIconByPresentIdReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentMarkIconByPresentIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentMarkIconByPresentId", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentMarkIconByPresentIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentMarkIconByPresentId indicates an expected call of GetPresentMarkIconByPresentId.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentMarkIconByPresentId(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentMarkIconByPresentId", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentMarkIconByPresentId), varargs...)
}

// GetPresentOrderStatus mocks base method.
func (m *MockUserPresentGOClient) GetPresentOrderStatus(arg0 context.Context, arg1 *userpresent_go.GetPresentOrderStatusReq, arg2 ...grpc.CallOption) (*userpresent_go.GetPresentOrderStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentOrderStatus", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetPresentOrderStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentOrderStatus indicates an expected call of GetPresentOrderStatus.
func (mr *MockUserPresentGOClientMockRecorder) GetPresentOrderStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentOrderStatus", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetPresentOrderStatus), varargs...)
}

// GetScenePresentDetailList mocks base method.
func (m *MockUserPresentGOClient) GetScenePresentDetailList(arg0 context.Context, arg1 *userpresent_go.GetScenePresentDetailListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetScenePresentDetailListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScenePresentDetailList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetScenePresentDetailListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScenePresentDetailList indicates an expected call of GetScenePresentDetailList.
func (mr *MockUserPresentGOClientMockRecorder) GetScenePresentDetailList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenePresentDetailList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetScenePresentDetailList), varargs...)
}

// GetScenePresentSummary mocks base method.
func (m *MockUserPresentGOClient) GetScenePresentSummary(arg0 context.Context, arg1 *userpresent_go.GetScenePresentSummaryReq, arg2 ...grpc.CallOption) (*userpresent_go.GetScenePresentSummaryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScenePresentSummary", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetScenePresentSummaryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScenePresentSummary indicates an expected call of GetScenePresentSummary.
func (mr *MockUserPresentGOClientMockRecorder) GetScenePresentSummary(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScenePresentSummary", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetScenePresentSummary), varargs...)
}

// GetUserPresentDetailList mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentDetailList(arg0 context.Context, arg1 *userpresent_go.GetUserPresentDetailListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetUserPresentDetailListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentDetailList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetUserPresentDetailListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentDetailList indicates an expected call of GetUserPresentDetailList.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentDetailList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentDetailList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentDetailList), varargs...)
}

// GetUserPresentReceive mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentReceive(arg0 context.Context, arg1 *userpresent_go.GetUserPresentReceiveReq, arg2 ...grpc.CallOption) (*userpresent_go.GetUserPresentReceiveResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentReceive", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetUserPresentReceiveResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentReceive indicates an expected call of GetUserPresentReceive.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentReceive(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentReceive", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentReceive), varargs...)
}

// GetUserPresentSend mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentSend(arg0 context.Context, arg1 *userpresent_go.GetUserPresentSendReq, arg2 ...grpc.CallOption) (*userpresent_go.GetUserPresentSendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentSend", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetUserPresentSendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSend indicates an expected call of GetUserPresentSend.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentSend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSend", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentSend), varargs...)
}

// GetUserPresentSendDetailList mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentSendDetailList(arg0 context.Context, arg1 *userpresent_go.GetUserPresentSendDetailListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetUserPresentSendDetailListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentSendDetailList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetUserPresentSendDetailListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSendDetailList indicates an expected call of GetUserPresentSendDetailList.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentSendDetailList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSendDetailList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentSendDetailList), varargs...)
}

// GetUserPresentSummary mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentSummary(arg0 context.Context, arg1 *userpresent_go.GetUserPresentSummaryReq, arg2 ...grpc.CallOption) (*userpresent_go.GetUserPresentSummaryResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentSummary", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetUserPresentSummaryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSummary indicates an expected call of GetUserPresentSummary.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentSummary(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummary", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentSummary), varargs...)
}

// GetUserPresentSummaryByItemList mocks base method.
func (m *MockUserPresentGOClient) GetUserPresentSummaryByItemList(arg0 context.Context, arg1 *userpresent_go.GetUserPresentSummaryByItemListReq, arg2 ...grpc.CallOption) (*userpresent_go.GetUserPresentSummaryByItemListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPresentSummaryByItemList", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetUserPresentSummaryByItemListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresentSummaryByItemList indicates an expected call of GetUserPresentSummaryByItemList.
func (mr *MockUserPresentGOClientMockRecorder) GetUserPresentSummaryByItemList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresentSummaryByItemList", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetUserPresentSummaryByItemList), varargs...)
}

// GetValidNamingPresentInfos mocks base method.
func (m *MockUserPresentGOClient) GetValidNamingPresentInfos(arg0 context.Context, arg1 *userpresent_go.GetValidNamingPresentInfosReq, arg2 ...grpc.CallOption) (*userpresent_go.GetValidNamingPresentInfosResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetValidNamingPresentInfos", varargs...)
	ret0, _ := ret[0].(*userpresent_go.GetValidNamingPresentInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetValidNamingPresentInfos indicates an expected call of GetValidNamingPresentInfos.
func (mr *MockUserPresentGOClientMockRecorder) GetValidNamingPresentInfos(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidNamingPresentInfos", reflect.TypeOf((*MockUserPresentGOClient)(nil).GetValidNamingPresentInfos), varargs...)
}

// RecordSceneSendPresent mocks base method.
func (m *MockUserPresentGOClient) RecordSceneSendPresent(arg0 context.Context, arg1 *userpresent_go.RecordSceneSendPresentReq, arg2 ...grpc.CallOption) (*userpresent_go.RecordSceneSendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordSceneSendPresent", varargs...)
	ret0, _ := ret[0].(*userpresent_go.RecordSceneSendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordSceneSendPresent indicates an expected call of RecordSceneSendPresent.
func (mr *MockUserPresentGOClientMockRecorder) RecordSceneSendPresent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSceneSendPresent", reflect.TypeOf((*MockUserPresentGOClient)(nil).RecordSceneSendPresent), varargs...)
}

// SendPresent mocks base method.
func (m *MockUserPresentGOClient) SendPresent(arg0 context.Context, arg1 *userpresent_go.SendPresentReq, arg2 ...grpc.CallOption) (*userpresent_go.SendPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendPresent", varargs...)
	ret0, _ := ret[0].(*userpresent_go.SendPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPresent indicates an expected call of SendPresent.
func (mr *MockUserPresentGOClientMockRecorder) SendPresent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresent", reflect.TypeOf((*MockUserPresentGOClient)(nil).SendPresent), varargs...)
}

// UpdateDynamicEffectTemplate mocks base method.
func (m *MockUserPresentGOClient) UpdateDynamicEffectTemplate(arg0 context.Context, arg1 *userpresent_go.UpdateDynamicEffectTemplateReq, arg2 ...grpc.CallOption) (*userpresent_go.UpdateDynamicEffectTemplateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateDynamicEffectTemplate", varargs...)
	ret0, _ := ret[0].(*userpresent_go.UpdateDynamicEffectTemplateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateDynamicEffectTemplate indicates an expected call of UpdateDynamicEffectTemplate.
func (mr *MockUserPresentGOClientMockRecorder) UpdateDynamicEffectTemplate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDynamicEffectTemplate", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdateDynamicEffectTemplate), varargs...)
}

// UpdateNamingPresentInfo mocks base method.
func (m *MockUserPresentGOClient) UpdateNamingPresentInfo(arg0 context.Context, arg1 *userpresent_go.UpdateNamingPresentInfoReq, arg2 ...grpc.CallOption) (*userpresent_go.UpdateNamingPresentInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateNamingPresentInfo", varargs...)
	ret0, _ := ret[0].(*userpresent_go.UpdateNamingPresentInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNamingPresentInfo indicates an expected call of UpdateNamingPresentInfo.
func (mr *MockUserPresentGOClientMockRecorder) UpdateNamingPresentInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNamingPresentInfo", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdateNamingPresentInfo), varargs...)
}

// UpdatePresentConfig mocks base method.
func (m *MockUserPresentGOClient) UpdatePresentConfig(arg0 context.Context, arg1 *userpresent_go.UpdatePresentConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.UpdatePresentConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePresentConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.UpdatePresentConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentConfig indicates an expected call of UpdatePresentConfig.
func (mr *MockUserPresentGOClientMockRecorder) UpdatePresentConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdatePresentConfig), varargs...)
}

// UpdatePresentConfigBackend mocks base method.
func (m *MockUserPresentGOClient) UpdatePresentConfigBackend(arg0 context.Context, arg1 *userpresent_go.UpdatePresentConfigBackendReq, arg2 ...grpc.CallOption) (*userpresent_go.UpdatePresentConfigBackendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePresentConfigBackend", varargs...)
	ret0, _ := ret[0].(*userpresent_go.UpdatePresentConfigBackendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentConfigBackend indicates an expected call of UpdatePresentConfigBackend.
func (mr *MockUserPresentGOClientMockRecorder) UpdatePresentConfigBackend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentConfigBackend", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdatePresentConfigBackend), varargs...)
}

// UpdatePresentEffectTemplateConfig mocks base method.
func (m *MockUserPresentGOClient) UpdatePresentEffectTemplateConfig(arg0 context.Context, arg1 *userpresent_go.UpdatePresentEffectTemplateConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.UpdatePresentEffectTemplateConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePresentEffectTemplateConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.UpdatePresentEffectTemplateConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentEffectTemplateConfig indicates an expected call of UpdatePresentEffectTemplateConfig.
func (mr *MockUserPresentGOClientMockRecorder) UpdatePresentEffectTemplateConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentEffectTemplateConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdatePresentEffectTemplateConfig), varargs...)
}

// UpdatePresentFlowConfig mocks base method.
func (m *MockUserPresentGOClient) UpdatePresentFlowConfig(arg0 context.Context, arg1 *userpresent_go.UpdatePresentFlowConfigReq, arg2 ...grpc.CallOption) (*userpresent_go.UpdatePresentFlowConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePresentFlowConfig", varargs...)
	ret0, _ := ret[0].(*userpresent_go.UpdatePresentFlowConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePresentFlowConfig indicates an expected call of UpdatePresentFlowConfig.
func (mr *MockUserPresentGOClientMockRecorder) UpdatePresentFlowConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentFlowConfig", reflect.TypeOf((*MockUserPresentGOClient)(nil).UpdatePresentFlowConfig), varargs...)
}
