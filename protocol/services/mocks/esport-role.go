// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/esport_role (interfaces: ESportRoleClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	esport_role "golang.52tt.com/protocol/services/esport_role"
	grpc "google.golang.org/grpc"
)

// MockESportRoleClient is a mock of ESportRoleClient interface.
type MockESportRoleClient struct {
	ctrl     *gomock.Controller
	recorder *MockESportRoleClientMockRecorder
}

// MockESportRoleClientMockRecorder is the mock recorder for MockESportRoleClient.
type MockESportRoleClientMockRecorder struct {
	mock *MockESportRoleClient
}

// NewMockESportRoleClient creates a new mock instance.
func NewMockESportRoleClient(ctrl *gomock.Controller) *MockESportRoleClient {
	mock := &MockESportRoleClient{ctrl: ctrl}
	mock.recorder = &MockESportRoleClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockESportRoleClient) EXPECT() *MockESportRoleClientMockRecorder {
	return m.recorder
}

// ApplyESportRequest mocks base method.
func (m *MockESportRoleClient) ApplyESportRequest(arg0 context.Context, arg1 *esport_role.ApplyESportRequset, arg2 ...grpc.CallOption) (*esport_role.ApplyESportResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplyESportRequest", varargs...)
	ret0, _ := ret[0].(*esport_role.ApplyESportResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyESportRequest indicates an expected call of ApplyESportRequest.
func (mr *MockESportRoleClientMockRecorder) ApplyESportRequest(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyESportRequest", reflect.TypeOf((*MockESportRoleClient)(nil).ApplyESportRequest), varargs...)
}

// BatCheckUserESportRole mocks base method.
func (m *MockESportRoleClient) BatCheckUserESportRole(arg0 context.Context, arg1 *esport_role.BatCheckUserESportRoleReq, arg2 ...grpc.CallOption) (*esport_role.BatCheckUserESportRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatCheckUserESportRole", varargs...)
	ret0, _ := ret[0].(*esport_role.BatCheckUserESportRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatCheckUserESportRole indicates an expected call of BatCheckUserESportRole.
func (mr *MockESportRoleClientMockRecorder) BatCheckUserESportRole(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatCheckUserESportRole", reflect.TypeOf((*MockESportRoleClient)(nil).BatCheckUserESportRole), varargs...)
}

// BatchGetCoachInfoByTTid mocks base method.
func (m *MockESportRoleClient) BatchGetCoachInfoByTTid(arg0 context.Context, arg1 *esport_role.BatchGetCoachInfoByTTidRequest, arg2 ...grpc.CallOption) (*esport_role.BatchGetCoachInfoByTTidResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetCoachInfoByTTid", varargs...)
	ret0, _ := ret[0].(*esport_role.BatchGetCoachInfoByTTidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCoachInfoByTTid indicates an expected call of BatchGetCoachInfoByTTid.
func (mr *MockESportRoleClientMockRecorder) BatchGetCoachInfoByTTid(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCoachInfoByTTid", reflect.TypeOf((*MockESportRoleClient)(nil).BatchGetCoachInfoByTTid), varargs...)
}

// BatchGetCoachLabel mocks base method.
func (m *MockESportRoleClient) BatchGetCoachLabel(arg0 context.Context, arg1 *esport_role.BatchGetCoachLabelRequest, arg2 ...grpc.CallOption) (*esport_role.BatchGetCoachLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetCoachLabel", varargs...)
	ret0, _ := ret[0].(*esport_role.BatchGetCoachLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCoachLabel indicates an expected call of BatchGetCoachLabel.
func (mr *MockESportRoleClientMockRecorder) BatchGetCoachLabel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCoachLabel", reflect.TypeOf((*MockESportRoleClient)(nil).BatchGetCoachLabel), varargs...)
}

// BatchGetGuildIdByCoachUids mocks base method.
func (m *MockESportRoleClient) BatchGetGuildIdByCoachUids(arg0 context.Context, arg1 *esport_role.BatchGetGuildIdByCoachUidsRequest, arg2 ...grpc.CallOption) (*esport_role.BatchGetGuildIdByCoachUidsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetGuildIdByCoachUids", varargs...)
	ret0, _ := ret[0].(*esport_role.BatchGetGuildIdByCoachUidsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGuildIdByCoachUids indicates an expected call of BatchGetGuildIdByCoachUids.
func (mr *MockESportRoleClientMockRecorder) BatchGetGuildIdByCoachUids(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGuildIdByCoachUids", reflect.TypeOf((*MockESportRoleClient)(nil).BatchGetGuildIdByCoachUids), varargs...)
}

// BatchGetUserESportRole mocks base method.
func (m *MockESportRoleClient) BatchGetUserESportRole(arg0 context.Context, arg1 *esport_role.BatchGetUserESportRoleReq, arg2 ...grpc.CallOption) (*esport_role.BatchGetUserESportRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserESportRole", varargs...)
	ret0, _ := ret[0].(*esport_role.BatchGetUserESportRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserESportRole indicates an expected call of BatchGetUserESportRole.
func (mr *MockESportRoleClientMockRecorder) BatchGetUserESportRole(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserESportRole", reflect.TypeOf((*MockESportRoleClient)(nil).BatchGetUserESportRole), varargs...)
}

// BatchGrantCoachLabel mocks base method.
func (m *MockESportRoleClient) BatchGrantCoachLabel(arg0 context.Context, arg1 *esport_role.BatchGrantCoachLabelRequest, arg2 ...grpc.CallOption) (*esport_role.BatchGrantCoachLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGrantCoachLabel", varargs...)
	ret0, _ := ret[0].(*esport_role.BatchGrantCoachLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGrantCoachLabel indicates an expected call of BatchGrantCoachLabel.
func (mr *MockESportRoleClientMockRecorder) BatchGrantCoachLabel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGrantCoachLabel", reflect.TypeOf((*MockESportRoleClient)(nil).BatchGrantCoachLabel), varargs...)
}

// CheckInLarkPool mocks base method.
func (m *MockESportRoleClient) CheckInLarkPool(arg0 context.Context, arg1 *esport_role.CheckInLarkPoolRequest, arg2 ...grpc.CallOption) (*esport_role.CheckInLarkPoolResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckInLarkPool", varargs...)
	ret0, _ := ret[0].(*esport_role.CheckInLarkPoolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckInLarkPool indicates an expected call of CheckInLarkPool.
func (mr *MockESportRoleClientMockRecorder) CheckInLarkPool(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInLarkPool", reflect.TypeOf((*MockESportRoleClient)(nil).CheckInLarkPool), varargs...)
}

// CheckSignGuildRiskAuditing mocks base method.
func (m *MockESportRoleClient) CheckSignGuildRiskAuditing(arg0 context.Context, arg1 *esport_role.CheckSignGuildRiskAuditingReq, arg2 ...grpc.CallOption) (*esport_role.CheckSignGuildRiskAuditingResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckSignGuildRiskAuditing", varargs...)
	ret0, _ := ret[0].(*esport_role.CheckSignGuildRiskAuditingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckSignGuildRiskAuditing indicates an expected call of CheckSignGuildRiskAuditing.
func (mr *MockESportRoleClientMockRecorder) CheckSignGuildRiskAuditing(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckSignGuildRiskAuditing", reflect.TypeOf((*MockESportRoleClient)(nil).CheckSignGuildRiskAuditing), varargs...)
}

// CheckTimeOverlapped mocks base method.
func (m *MockESportRoleClient) CheckTimeOverlapped(arg0 context.Context, arg1 *esport_role.CheckTimeOverlappedRequest, arg2 ...grpc.CallOption) (*esport_role.CheckTimeOverlappedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckTimeOverlapped", varargs...)
	ret0, _ := ret[0].(*esport_role.CheckTimeOverlappedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckTimeOverlapped indicates an expected call of CheckTimeOverlapped.
func (mr *MockESportRoleClientMockRecorder) CheckTimeOverlapped(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTimeOverlapped", reflect.TypeOf((*MockESportRoleClient)(nil).CheckTimeOverlapped), varargs...)
}

// CheckUserApplyESport mocks base method.
func (m *MockESportRoleClient) CheckUserApplyESport(arg0 context.Context, arg1 *esport_role.CheckUserApplyESportReq, arg2 ...grpc.CallOption) (*esport_role.CheckUserApplyESportResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckUserApplyESport", varargs...)
	ret0, _ := ret[0].(*esport_role.CheckUserApplyESportResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserApplyESport indicates an expected call of CheckUserApplyESport.
func (mr *MockESportRoleClientMockRecorder) CheckUserApplyESport(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserApplyESport", reflect.TypeOf((*MockESportRoleClient)(nil).CheckUserApplyESport), varargs...)
}

// ESportGuildAuditResult mocks base method.
func (m *MockESportRoleClient) ESportGuildAuditResult(arg0 context.Context, arg1 *esport_role.ESportGuildAuditResultReq, arg2 ...grpc.CallOption) (*esport_role.ESportGuildAuditResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ESportGuildAuditResult", varargs...)
	ret0, _ := ret[0].(*esport_role.ESportGuildAuditResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ESportGuildAuditResult indicates an expected call of ESportGuildAuditResult.
func (mr *MockESportRoleClientMockRecorder) ESportGuildAuditResult(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ESportGuildAuditResult", reflect.TypeOf((*MockESportRoleClient)(nil).ESportGuildAuditResult), varargs...)
}

// ESportRiskAuditResult mocks base method.
func (m *MockESportRoleClient) ESportRiskAuditResult(arg0 context.Context, arg1 *esport_role.ESportRiskAuditResultReq, arg2 ...grpc.CallOption) (*esport_role.ESportRiskAuditResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ESportRiskAuditResult", varargs...)
	ret0, _ := ret[0].(*esport_role.ESportRiskAuditResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ESportRiskAuditResult indicates an expected call of ESportRiskAuditResult.
func (mr *MockESportRoleClientMockRecorder) ESportRiskAuditResult(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ESportRiskAuditResult", reflect.TypeOf((*MockESportRoleClient)(nil).ESportRiskAuditResult), varargs...)
}

// GetAllESportRole mocks base method.
func (m *MockESportRoleClient) GetAllESportRole(arg0 context.Context, arg1 *esport_role.GetAllESportRoleReq, arg2 ...grpc.CallOption) (*esport_role.GetAllESportRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllESportRole", varargs...)
	ret0, _ := ret[0].(*esport_role.GetAllESportRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllESportRole indicates an expected call of GetAllESportRole.
func (mr *MockESportRoleClientMockRecorder) GetAllESportRole(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllESportRole", reflect.TypeOf((*MockESportRoleClient)(nil).GetAllESportRole), varargs...)
}

// GetApplyBlackList mocks base method.
func (m *MockESportRoleClient) GetApplyBlackList(arg0 context.Context, arg1 *esport_role.GetApplyBlackListReq, arg2 ...grpc.CallOption) (*esport_role.GetApplyBlackListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApplyBlackList", varargs...)
	ret0, _ := ret[0].(*esport_role.GetApplyBlackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplyBlackList indicates an expected call of GetApplyBlackList.
func (mr *MockESportRoleClientMockRecorder) GetApplyBlackList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplyBlackList", reflect.TypeOf((*MockESportRoleClient)(nil).GetApplyBlackList), varargs...)
}

// GetBlackListHandleRecord mocks base method.
func (m *MockESportRoleClient) GetBlackListHandleRecord(arg0 context.Context, arg1 *esport_role.GetBlackListHandleRecordReq, arg2 ...grpc.CallOption) (*esport_role.GetBlackListHandleRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBlackListHandleRecord", varargs...)
	ret0, _ := ret[0].(*esport_role.GetBlackListHandleRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlackListHandleRecord indicates an expected call of GetBlackListHandleRecord.
func (mr *MockESportRoleClientMockRecorder) GetBlackListHandleRecord(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlackListHandleRecord", reflect.TypeOf((*MockESportRoleClient)(nil).GetBlackListHandleRecord), varargs...)
}

// GetCoachByGuild mocks base method.
func (m *MockESportRoleClient) GetCoachByGuild(arg0 context.Context, arg1 *esport_role.GetCoachByGuildReq, arg2 ...grpc.CallOption) (*esport_role.GetCoachByGuildResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachByGuild", varargs...)
	ret0, _ := ret[0].(*esport_role.GetCoachByGuildResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachByGuild indicates an expected call of GetCoachByGuild.
func (mr *MockESportRoleClientMockRecorder) GetCoachByGuild(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachByGuild", reflect.TypeOf((*MockESportRoleClient)(nil).GetCoachByGuild), varargs...)
}

// GetCoachLabelRecordByPage mocks base method.
func (m *MockESportRoleClient) GetCoachLabelRecordByPage(arg0 context.Context, arg1 *esport_role.GetCoachLabelRecordByPageRequest, arg2 ...grpc.CallOption) (*esport_role.GetCoachLabelRecordByPageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachLabelRecordByPage", varargs...)
	ret0, _ := ret[0].(*esport_role.GetCoachLabelRecordByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachLabelRecordByPage indicates an expected call of GetCoachLabelRecordByPage.
func (mr *MockESportRoleClientMockRecorder) GetCoachLabelRecordByPage(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachLabelRecordByPage", reflect.TypeOf((*MockESportRoleClient)(nil).GetCoachLabelRecordByPage), varargs...)
}

// GetESportAuditList mocks base method.
func (m *MockESportRoleClient) GetESportAuditList(arg0 context.Context, arg1 *esport_role.GetApplyESportRecordReq, arg2 ...grpc.CallOption) (*esport_role.GetApplyESportRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetESportAuditList", varargs...)
	ret0, _ := ret[0].(*esport_role.GetApplyESportRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetESportAuditList indicates an expected call of GetESportAuditList.
func (mr *MockESportRoleClientMockRecorder) GetESportAuditList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetESportAuditList", reflect.TypeOf((*MockESportRoleClient)(nil).GetESportAuditList), varargs...)
}

// GetESportErInfoList mocks base method.
func (m *MockESportRoleClient) GetESportErInfoList(arg0 context.Context, arg1 *esport_role.GetESportErInfoListReq, arg2 ...grpc.CallOption) (*esport_role.GetESportErInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetESportErInfoList", varargs...)
	ret0, _ := ret[0].(*esport_role.GetESportErInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetESportErInfoList indicates an expected call of GetESportErInfoList.
func (mr *MockESportRoleClientMockRecorder) GetESportErInfoList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetESportErInfoList", reflect.TypeOf((*MockESportRoleClient)(nil).GetESportErInfoList), varargs...)
}

// GetLarkPool mocks base method.
func (m *MockESportRoleClient) GetLarkPool(arg0 context.Context, arg1 *esport_role.GetLarkPoolRequest, arg2 ...grpc.CallOption) (*esport_role.GetLarkPoolResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLarkPool", varargs...)
	ret0, _ := ret[0].(*esport_role.GetLarkPoolResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLarkPool indicates an expected call of GetLarkPool.
func (mr *MockESportRoleClientMockRecorder) GetLarkPool(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLarkPool", reflect.TypeOf((*MockESportRoleClient)(nil).GetLarkPool), varargs...)
}

// GetReclaimInfoList mocks base method.
func (m *MockESportRoleClient) GetReclaimInfoList(arg0 context.Context, arg1 *esport_role.GetReclaimInfoListReq, arg2 ...grpc.CallOption) (*esport_role.GetReclaimInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReclaimInfoList", varargs...)
	ret0, _ := ret[0].(*esport_role.GetReclaimInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReclaimInfoList indicates an expected call of GetReclaimInfoList.
func (mr *MockESportRoleClientMockRecorder) GetReclaimInfoList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReclaimInfoList", reflect.TypeOf((*MockESportRoleClient)(nil).GetReclaimInfoList), varargs...)
}

// GetUserESportRole mocks base method.
func (m *MockESportRoleClient) GetUserESportRole(arg0 context.Context, arg1 *esport_role.GetUserESportRoleReq, arg2 ...grpc.CallOption) (*esport_role.GetUserESportRoleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserESportRole", varargs...)
	ret0, _ := ret[0].(*esport_role.GetUserESportRoleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserESportRole indicates an expected call of GetUserESportRole.
func (mr *MockESportRoleClientMockRecorder) GetUserESportRole(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserESportRole", reflect.TypeOf((*MockESportRoleClient)(nil).GetUserESportRole), varargs...)
}

// ManualAddUserESport mocks base method.
func (m *MockESportRoleClient) ManualAddUserESport(arg0 context.Context, arg1 *esport_role.ManualAddUserESportReq, arg2 ...grpc.CallOption) (*esport_role.ManualAddUserESportResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ManualAddUserESport", varargs...)
	ret0, _ := ret[0].(*esport_role.ManualAddUserESportResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ManualAddUserESport indicates an expected call of ManualAddUserESport.
func (mr *MockESportRoleClientMockRecorder) ManualAddUserESport(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManualAddUserESport", reflect.TypeOf((*MockESportRoleClient)(nil).ManualAddUserESport), varargs...)
}

// OfficialHandleApplyESport mocks base method.
func (m *MockESportRoleClient) OfficialHandleApplyESport(arg0 context.Context, arg1 *esport_role.OfficialHandleApplyESportReq, arg2 ...grpc.CallOption) (*esport_role.OfficialHandleApplyESportResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OfficialHandleApplyESport", varargs...)
	ret0, _ := ret[0].(*esport_role.OfficialHandleApplyESportResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OfficialHandleApplyESport indicates an expected call of OfficialHandleApplyESport.
func (mr *MockESportRoleClientMockRecorder) OfficialHandleApplyESport(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleApplyESport", reflect.TypeOf((*MockESportRoleClient)(nil).OfficialHandleApplyESport), varargs...)
}

// RecallCoachLabel mocks base method.
func (m *MockESportRoleClient) RecallCoachLabel(arg0 context.Context, arg1 *esport_role.RecallCoachLabelRequest, arg2 ...grpc.CallOption) (*esport_role.RecallCoachLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecallCoachLabel", varargs...)
	ret0, _ := ret[0].(*esport_role.RecallCoachLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecallCoachLabel indicates an expected call of RecallCoachLabel.
func (mr *MockESportRoleClientMockRecorder) RecallCoachLabel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecallCoachLabel", reflect.TypeOf((*MockESportRoleClient)(nil).RecallCoachLabel), varargs...)
}

// ReclaimESportErIdentity mocks base method.
func (m *MockESportRoleClient) ReclaimESportErIdentity(arg0 context.Context, arg1 *esport_role.ReclaimESportErIdentityReq, arg2 ...grpc.CallOption) (*esport_role.ReclaimESportErIdentityResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReclaimESportErIdentity", varargs...)
	ret0, _ := ret[0].(*esport_role.ReclaimESportErIdentityResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReclaimESportErIdentity indicates an expected call of ReclaimESportErIdentity.
func (mr *MockESportRoleClientMockRecorder) ReclaimESportErIdentity(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReclaimESportErIdentity", reflect.TypeOf((*MockESportRoleClient)(nil).ReclaimESportErIdentity), varargs...)
}

// UpdateApplyBlacklistFreezeTime mocks base method.
func (m *MockESportRoleClient) UpdateApplyBlacklistFreezeTime(arg0 context.Context, arg1 *esport_role.UpdateApplyBlacklistFreezeTimeReq, arg2 ...grpc.CallOption) (*esport_role.UpdateApplyBlacklistFreezeTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateApplyBlacklistFreezeTime", varargs...)
	ret0, _ := ret[0].(*esport_role.UpdateApplyBlacklistFreezeTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateApplyBlacklistFreezeTime indicates an expected call of UpdateApplyBlacklistFreezeTime.
func (mr *MockESportRoleClientMockRecorder) UpdateApplyBlacklistFreezeTime(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateApplyBlacklistFreezeTime", reflect.TypeOf((*MockESportRoleClient)(nil).UpdateApplyBlacklistFreezeTime), varargs...)
}

// UpdateCoachLabel mocks base method.
func (m *MockESportRoleClient) UpdateCoachLabel(arg0 context.Context, arg1 *esport_role.UpdateCoachLabelRequest, arg2 ...grpc.CallOption) (*esport_role.UpdateCoachLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCoachLabel", varargs...)
	ret0, _ := ret[0].(*esport_role.UpdateCoachLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCoachLabel indicates an expected call of UpdateCoachLabel.
func (mr *MockESportRoleClientMockRecorder) UpdateCoachLabel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCoachLabel", reflect.TypeOf((*MockESportRoleClient)(nil).UpdateCoachLabel), varargs...)
}
