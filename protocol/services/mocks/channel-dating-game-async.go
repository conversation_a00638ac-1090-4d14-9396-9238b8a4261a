// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/channel-dating-game-async (interfaces: ChannelDatingGameAsyncClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	gomock "github.com/golang/mock/gomock"
)

// MockChannelDatingGameAsyncClient is a mock of ChannelDatingGameAsyncClient interface.
type MockChannelDatingGameAsyncClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelDatingGameAsyncClientMockRecorder
}

// MockChannelDatingGameAsyncClientMockRecorder is the mock recorder for MockChannelDatingGameAsyncClient.
type MockChannelDatingGameAsyncClientMockRecorder struct {
	mock *MockChannelDatingGameAsyncClient
}

// NewMockChannelDatingGameAsyncClient creates a new mock instance.
func NewMockChannelDatingGameAsyncClient(ctrl *gomock.Controller) *MockChannelDatingGameAsyncClient {
	mock := &MockChannelDatingGameAsyncClient{ctrl: ctrl}
	mock.recorder = &MockChannelDatingGameAsyncClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelDatingGameAsyncClient) EXPECT() *MockChannelDatingGameAsyncClientMockRecorder {
	return m.recorder
}
