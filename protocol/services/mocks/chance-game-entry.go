// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/chance-game-entry (interfaces: ChanceGameEntryClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	chance_game_entry "golang.52tt.com/protocol/services/chance-game-entry"
	grpc "google.golang.org/grpc"
)

// MockChanceGameEntryClient is a mock of ChanceGameEntryClient interface.
type MockChanceGameEntryClient struct {
	ctrl     *gomock.Controller
	recorder *MockChanceGameEntryClientMockRecorder
}

// MockChanceGameEntryClientMockRecorder is the mock recorder for MockChanceGameEntryClient.
type MockChanceGameEntryClientMockRecorder struct {
	mock *MockChanceGameEntryClient
}

// NewMockChanceGameEntryClient creates a new mock instance.
func NewMockChanceGameEntryClient(ctrl *gomock.Controller) *MockChanceGameEntryClient {
	mock := &MockChanceGameEntryClient{ctrl: ctrl}
	mock.recorder = &MockChanceGameEntryClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChanceGameEntryClient) EXPECT() *MockChanceGameEntryClientMockRecorder {
	return m.recorder
}

// AddChannelBWListV2 mocks base method.
func (m *MockChanceGameEntryClient) AddChannelBWListV2(arg0 context.Context, arg1 *chance_game_entry.AddChannelBWListV2Req, arg2 ...grpc.CallOption) (*chance_game_entry.AddChannelBWListV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddChannelBWListV2", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.AddChannelBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChannelBWListV2 indicates an expected call of AddChannelBWListV2.
func (mr *MockChanceGameEntryClientMockRecorder) AddChannelBWListV2(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelBWListV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).AddChannelBWListV2), varargs...)
}

// AddUserBWListV2 mocks base method.
func (m *MockChanceGameEntryClient) AddUserBWListV2(arg0 context.Context, arg1 *chance_game_entry.AddUserBWListV2Req, arg2 ...grpc.CallOption) (*chance_game_entry.AddUserBWListV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserBWListV2", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.AddUserBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserBWListV2 indicates an expected call of AddUserBWListV2.
func (mr *MockChanceGameEntryClientMockRecorder) AddUserBWListV2(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserBWListV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).AddUserBWListV2), varargs...)
}

// BatDelChannelBWListV2 mocks base method.
func (m *MockChanceGameEntryClient) BatDelChannelBWListV2(arg0 context.Context, arg1 *chance_game_entry.BatDelChannelBWListV2Req, arg2 ...grpc.CallOption) (*chance_game_entry.BatDelChannelBWListV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatDelChannelBWListV2", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.BatDelChannelBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatDelChannelBWListV2 indicates an expected call of BatDelChannelBWListV2.
func (mr *MockChanceGameEntryClientMockRecorder) BatDelChannelBWListV2(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelChannelBWListV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).BatDelChannelBWListV2), varargs...)
}

// BatDelUserBWListV2 mocks base method.
func (m *MockChanceGameEntryClient) BatDelUserBWListV2(arg0 context.Context, arg1 *chance_game_entry.BatDelUserBWListV2Req, arg2 ...grpc.CallOption) (*chance_game_entry.BatDelUserBWListV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatDelUserBWListV2", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.BatDelUserBWListV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatDelUserBWListV2 indicates an expected call of BatDelUserBWListV2.
func (mr *MockChanceGameEntryClientMockRecorder) BatDelUserBWListV2(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelUserBWListV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).BatDelUserBWListV2), varargs...)
}

// BatGetNotifyInfo mocks base method.
func (m *MockChanceGameEntryClient) BatGetNotifyInfo(arg0 context.Context, arg1 *chance_game_entry.BatGetNotifyInfoReq, arg2 ...grpc.CallOption) (*chance_game_entry.BatGetNotifyInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetNotifyInfo", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.BatGetNotifyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetNotifyInfo indicates an expected call of BatGetNotifyInfo.
func (mr *MockChanceGameEntryClientMockRecorder) BatGetNotifyInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetNotifyInfo", reflect.TypeOf((*MockChanceGameEntryClient)(nil).BatGetNotifyInfo), varargs...)
}

// CheckChanceGameIsOpen mocks base method.
func (m *MockChanceGameEntryClient) CheckChanceGameIsOpen(arg0 context.Context, arg1 *chance_game_entry.CheckChanceGameIsOpenReq, arg2 ...grpc.CallOption) (*chance_game_entry.CheckChanceGameIsOpenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckChanceGameIsOpen", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.CheckChanceGameIsOpenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckChanceGameIsOpen indicates an expected call of CheckChanceGameIsOpen.
func (mr *MockChanceGameEntryClientMockRecorder) CheckChanceGameIsOpen(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChanceGameIsOpen", reflect.TypeOf((*MockChanceGameEntryClient)(nil).CheckChanceGameIsOpen), varargs...)
}

// CheckGameEntryAccess mocks base method.
func (m *MockChanceGameEntryClient) CheckGameEntryAccess(arg0 context.Context, arg1 *chance_game_entry.CheckGameEntryAccessReq, arg2 ...grpc.CallOption) (*chance_game_entry.CheckGameEntryAccessResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckGameEntryAccess", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.CheckGameEntryAccessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckGameEntryAccess indicates an expected call of CheckGameEntryAccess.
func (mr *MockChanceGameEntryClientMockRecorder) CheckGameEntryAccess(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckGameEntryAccess", reflect.TypeOf((*MockChanceGameEntryClient)(nil).CheckGameEntryAccess), varargs...)
}

// CheckMagicSpiritAccess mocks base method.
func (m *MockChanceGameEntryClient) CheckMagicSpiritAccess(arg0 context.Context, arg1 *chance_game_entry.CheckMagicSpiritAccessReq, arg2 ...grpc.CallOption) (*chance_game_entry.CheckMagicSpiritAccessResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckMagicSpiritAccess", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.CheckMagicSpiritAccessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckMagicSpiritAccess indicates an expected call of CheckMagicSpiritAccess.
func (mr *MockChanceGameEntryClientMockRecorder) CheckMagicSpiritAccess(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckMagicSpiritAccess", reflect.TypeOf((*MockChanceGameEntryClient)(nil).CheckMagicSpiritAccess), varargs...)
}

// DelMagicSpiritAccessCond mocks base method.
func (m *MockChanceGameEntryClient) DelMagicSpiritAccessCond(arg0 context.Context, arg1 *chance_game_entry.DelMagicSpiritAccessCondReq, arg2 ...grpc.CallOption) (*chance_game_entry.DelMagicSpiritAccessCondResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelMagicSpiritAccessCond", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.DelMagicSpiritAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelMagicSpiritAccessCond indicates an expected call of DelMagicSpiritAccessCond.
func (mr *MockChanceGameEntryClientMockRecorder) DelMagicSpiritAccessCond(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelMagicSpiritAccessCond", reflect.TypeOf((*MockChanceGameEntryClient)(nil).DelMagicSpiritAccessCond), varargs...)
}

// GetAllChanceGameSwitchState mocks base method.
func (m *MockChanceGameEntryClient) GetAllChanceGameSwitchState(arg0 context.Context, arg1 *chance_game_entry.GetAllChanceGameSwitchStateReq, arg2 ...grpc.CallOption) (*chance_game_entry.GetAllChanceGameSwitchStateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllChanceGameSwitchState", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetAllChanceGameSwitchStateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllChanceGameSwitchState indicates an expected call of GetAllChanceGameSwitchState.
func (mr *MockChanceGameEntryClientMockRecorder) GetAllChanceGameSwitchState(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllChanceGameSwitchState", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetAllChanceGameSwitchState), varargs...)
}

// GetChanceGameAccessCond mocks base method.
func (m *MockChanceGameEntryClient) GetChanceGameAccessCond(arg0 context.Context, arg1 *chance_game_entry.GetChanceGameAccessCondReq, arg2 ...grpc.CallOption) (*chance_game_entry.GetChanceGameAccessCondResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChanceGameAccessCond", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetChanceGameAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanceGameAccessCond indicates an expected call of GetChanceGameAccessCond.
func (mr *MockChanceGameEntryClientMockRecorder) GetChanceGameAccessCond(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanceGameAccessCond", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetChanceGameAccessCond), varargs...)
}

// GetChanceGameOpenTime mocks base method.
func (m *MockChanceGameEntryClient) GetChanceGameOpenTime(arg0 context.Context, arg1 *chance_game_entry.GetGameSwitchOpenTimeReq, arg2 ...grpc.CallOption) (*chance_game_entry.GetGameSwitchOpenTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChanceGameOpenTime", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetGameSwitchOpenTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanceGameOpenTime indicates an expected call of GetChanceGameOpenTime.
func (mr *MockChanceGameEntryClientMockRecorder) GetChanceGameOpenTime(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanceGameOpenTime", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetChanceGameOpenTime), varargs...)
}

// GetChannelBWListInfoV2 mocks base method.
func (m *MockChanceGameEntryClient) GetChannelBWListInfoV2(arg0 context.Context, arg1 *chance_game_entry.GetChannelBWListInfoV2Req, arg2 ...grpc.CallOption) (*chance_game_entry.GetChannelBWListInfoV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelBWListInfoV2", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetChannelBWListInfoV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelBWListInfoV2 indicates an expected call of GetChannelBWListInfoV2.
func (mr *MockChanceGameEntryClientMockRecorder) GetChannelBWListInfoV2(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBWListInfoV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetChannelBWListInfoV2), varargs...)
}

// GetGameNotifyInfo mocks base method.
func (m *MockChanceGameEntryClient) GetGameNotifyInfo(arg0 context.Context, arg1 *chance_game_entry.GetGameNotifyInfoReq, arg2 ...grpc.CallOption) (*chance_game_entry.GetGameNotifyInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameNotifyInfo", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetGameNotifyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameNotifyInfo indicates an expected call of GetGameNotifyInfo.
func (mr *MockChanceGameEntryClientMockRecorder) GetGameNotifyInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameNotifyInfo", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetGameNotifyInfo), varargs...)
}

// GetLocalCacheConf mocks base method.
func (m *MockChanceGameEntryClient) GetLocalCacheConf(arg0 context.Context, arg1 *chance_game_entry.GetLocalCacheConfReq, arg2 ...grpc.CallOption) (*chance_game_entry.GetLocalCacheConfResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLocalCacheConf", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetLocalCacheConfResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLocalCacheConf indicates an expected call of GetLocalCacheConf.
func (mr *MockChanceGameEntryClientMockRecorder) GetLocalCacheConf(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocalCacheConf", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetLocalCacheConf), varargs...)
}

// GetMagicSpiritAccessCond mocks base method.
func (m *MockChanceGameEntryClient) GetMagicSpiritAccessCond(arg0 context.Context, arg1 *chance_game_entry.GetMagicSpiritAccessCondReq, arg2 ...grpc.CallOption) (*chance_game_entry.GetMagicSpiritAccessCondResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMagicSpiritAccessCond", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetMagicSpiritAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMagicSpiritAccessCond indicates an expected call of GetMagicSpiritAccessCond.
func (mr *MockChanceGameEntryClientMockRecorder) GetMagicSpiritAccessCond(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMagicSpiritAccessCond", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetMagicSpiritAccessCond), varargs...)
}

// GetUserBWListInfoV2 mocks base method.
func (m *MockChanceGameEntryClient) GetUserBWListInfoV2(arg0 context.Context, arg1 *chance_game_entry.GetUserBWListInfoV2Req, arg2 ...grpc.CallOption) (*chance_game_entry.GetUserBWListInfoV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserBWListInfoV2", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetUserBWListInfoV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserBWListInfoV2 indicates an expected call of GetUserBWListInfoV2.
func (mr *MockChanceGameEntryClientMockRecorder) GetUserBWListInfoV2(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBWListInfoV2", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetUserBWListInfoV2), varargs...)
}

// GetUserValue mocks base method.
func (m *MockChanceGameEntryClient) GetUserValue(arg0 context.Context, arg1 *chance_game_entry.GetUserValueReq, arg2 ...grpc.CallOption) (*chance_game_entry.GetUserValueResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserValue", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.GetUserValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserValue indicates an expected call of GetUserValue.
func (mr *MockChanceGameEntryClientMockRecorder) GetUserValue(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserValue", reflect.TypeOf((*MockChanceGameEntryClient)(nil).GetUserValue), varargs...)
}

// SetChanceGameAccessCond mocks base method.
func (m *MockChanceGameEntryClient) SetChanceGameAccessCond(arg0 context.Context, arg1 *chance_game_entry.SetChanceGameAccessCondReq, arg2 ...grpc.CallOption) (*chance_game_entry.SetChanceGameAccessCondResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChanceGameAccessCond", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.SetChanceGameAccessCondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameAccessCond indicates an expected call of SetChanceGameAccessCond.
func (mr *MockChanceGameEntryClientMockRecorder) SetChanceGameAccessCond(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameAccessCond", reflect.TypeOf((*MockChanceGameEntryClient)(nil).SetChanceGameAccessCond), varargs...)
}

// SetChanceGameOpenTime mocks base method.
func (m *MockChanceGameEntryClient) SetChanceGameOpenTime(arg0 context.Context, arg1 *chance_game_entry.SetGameSwitchOpenTimeReq, arg2 ...grpc.CallOption) (*chance_game_entry.SetGameSwitchOpenTimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChanceGameOpenTime", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.SetGameSwitchOpenTimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameOpenTime indicates an expected call of SetChanceGameOpenTime.
func (mr *MockChanceGameEntryClientMockRecorder) SetChanceGameOpenTime(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameOpenTime", reflect.TypeOf((*MockChanceGameEntryClient)(nil).SetChanceGameOpenTime), varargs...)
}

// SetChanceGameSwitch mocks base method.
func (m *MockChanceGameEntryClient) SetChanceGameSwitch(arg0 context.Context, arg1 *chance_game_entry.SetChanceGameSwitchReq, arg2 ...grpc.CallOption) (*chance_game_entry.SetChanceGameSwitchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChanceGameSwitch", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.SetChanceGameSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChanceGameSwitch indicates an expected call of SetChanceGameSwitch.
func (mr *MockChanceGameEntryClientMockRecorder) SetChanceGameSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChanceGameSwitch", reflect.TypeOf((*MockChanceGameEntryClient)(nil).SetChanceGameSwitch), varargs...)
}

// SetGameNotifyInfo mocks base method.
func (m *MockChanceGameEntryClient) SetGameNotifyInfo(arg0 context.Context, arg1 *chance_game_entry.SetGameNotifyInfoReq, arg2 ...grpc.CallOption) (*chance_game_entry.SetGameNotifyInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetGameNotifyInfo", varargs...)
	ret0, _ := ret[0].(*chance_game_entry.SetGameNotifyInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGameNotifyInfo indicates an expected call of SetGameNotifyInfo.
func (mr *MockChanceGameEntryClientMockRecorder) SetGameNotifyInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameNotifyInfo", reflect.TypeOf((*MockChanceGameEntryClient)(nil).SetGameNotifyInfo), varargs...)
}
