// Code generated by protoc-gen-go. DO NOT EDIT.
// source: account-isolator/account-isolator.proto

package account_isolator // import "golang.52tt.com/protocol/services/account-isolator"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetIsolationSequenceReq struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPkg           string   `protobuf:"bytes,2,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	TerminalType         uint32   `protobuf:"varint,3,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,4,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIsolationSequenceReq) Reset()         { *m = GetIsolationSequenceReq{} }
func (m *GetIsolationSequenceReq) String() string { return proto.CompactTextString(m) }
func (*GetIsolationSequenceReq) ProtoMessage()    {}
func (*GetIsolationSequenceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_isolator_ba34002a893b9afa, []int{0}
}
func (m *GetIsolationSequenceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIsolationSequenceReq.Unmarshal(m, b)
}
func (m *GetIsolationSequenceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIsolationSequenceReq.Marshal(b, m, deterministic)
}
func (dst *GetIsolationSequenceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIsolationSequenceReq.Merge(dst, src)
}
func (m *GetIsolationSequenceReq) XXX_Size() int {
	return xxx_messageInfo_GetIsolationSequenceReq.Size(m)
}
func (m *GetIsolationSequenceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIsolationSequenceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIsolationSequenceReq proto.InternalMessageInfo

func (m *GetIsolationSequenceReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetIsolationSequenceReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *GetIsolationSequenceReq) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *GetIsolationSequenceReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

type GetIsolationSequenceResp struct {
	Scenes               []string `protobuf:"bytes,1,rep,name=scenes,proto3" json:"scenes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIsolationSequenceResp) Reset()         { *m = GetIsolationSequenceResp{} }
func (m *GetIsolationSequenceResp) String() string { return proto.CompactTextString(m) }
func (*GetIsolationSequenceResp) ProtoMessage()    {}
func (*GetIsolationSequenceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_isolator_ba34002a893b9afa, []int{1}
}
func (m *GetIsolationSequenceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIsolationSequenceResp.Unmarshal(m, b)
}
func (m *GetIsolationSequenceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIsolationSequenceResp.Marshal(b, m, deterministic)
}
func (dst *GetIsolationSequenceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIsolationSequenceResp.Merge(dst, src)
}
func (m *GetIsolationSequenceResp) XXX_Size() int {
	return xxx_messageInfo_GetIsolationSequenceResp.Size(m)
}
func (m *GetIsolationSequenceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIsolationSequenceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIsolationSequenceResp proto.InternalMessageInfo

func (m *GetIsolationSequenceResp) GetScenes() []string {
	if m != nil {
		return m.Scenes
	}
	return nil
}

type LockUidIsolationReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Scene                string   `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LockUidIsolationReq) Reset()         { *m = LockUidIsolationReq{} }
func (m *LockUidIsolationReq) String() string { return proto.CompactTextString(m) }
func (*LockUidIsolationReq) ProtoMessage()    {}
func (*LockUidIsolationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_isolator_ba34002a893b9afa, []int{2}
}
func (m *LockUidIsolationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LockUidIsolationReq.Unmarshal(m, b)
}
func (m *LockUidIsolationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LockUidIsolationReq.Marshal(b, m, deterministic)
}
func (dst *LockUidIsolationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LockUidIsolationReq.Merge(dst, src)
}
func (m *LockUidIsolationReq) XXX_Size() int {
	return xxx_messageInfo_LockUidIsolationReq.Size(m)
}
func (m *LockUidIsolationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LockUidIsolationReq.DiscardUnknown(m)
}

var xxx_messageInfo_LockUidIsolationReq proto.InternalMessageInfo

func (m *LockUidIsolationReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LockUidIsolationReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

type LockUidIsolationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LockUidIsolationResp) Reset()         { *m = LockUidIsolationResp{} }
func (m *LockUidIsolationResp) String() string { return proto.CompactTextString(m) }
func (*LockUidIsolationResp) ProtoMessage()    {}
func (*LockUidIsolationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_isolator_ba34002a893b9afa, []int{3}
}
func (m *LockUidIsolationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LockUidIsolationResp.Unmarshal(m, b)
}
func (m *LockUidIsolationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LockUidIsolationResp.Marshal(b, m, deterministic)
}
func (dst *LockUidIsolationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LockUidIsolationResp.Merge(dst, src)
}
func (m *LockUidIsolationResp) XXX_Size() int {
	return xxx_messageInfo_LockUidIsolationResp.Size(m)
}
func (m *LockUidIsolationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LockUidIsolationResp.DiscardUnknown(m)
}

var xxx_messageInfo_LockUidIsolationResp proto.InternalMessageInfo

type TryLockUidIsolationReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPkg           string   `protobuf:"bytes,3,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	TerminalType         uint32   `protobuf:"varint,4,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,5,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TryLockUidIsolationReq) Reset()         { *m = TryLockUidIsolationReq{} }
func (m *TryLockUidIsolationReq) String() string { return proto.CompactTextString(m) }
func (*TryLockUidIsolationReq) ProtoMessage()    {}
func (*TryLockUidIsolationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_isolator_ba34002a893b9afa, []int{4}
}
func (m *TryLockUidIsolationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TryLockUidIsolationReq.Unmarshal(m, b)
}
func (m *TryLockUidIsolationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TryLockUidIsolationReq.Marshal(b, m, deterministic)
}
func (dst *TryLockUidIsolationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TryLockUidIsolationReq.Merge(dst, src)
}
func (m *TryLockUidIsolationReq) XXX_Size() int {
	return xxx_messageInfo_TryLockUidIsolationReq.Size(m)
}
func (m *TryLockUidIsolationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TryLockUidIsolationReq.DiscardUnknown(m)
}

var xxx_messageInfo_TryLockUidIsolationReq proto.InternalMessageInfo

func (m *TryLockUidIsolationReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TryLockUidIsolationReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *TryLockUidIsolationReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *TryLockUidIsolationReq) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *TryLockUidIsolationReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

type TryLockUidIsolationResp struct {
	Lock                 bool     `protobuf:"varint,1,opt,name=lock,proto3" json:"lock,omitempty"`
	Scene                string   `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TryLockUidIsolationResp) Reset()         { *m = TryLockUidIsolationResp{} }
func (m *TryLockUidIsolationResp) String() string { return proto.CompactTextString(m) }
func (*TryLockUidIsolationResp) ProtoMessage()    {}
func (*TryLockUidIsolationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_isolator_ba34002a893b9afa, []int{5}
}
func (m *TryLockUidIsolationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TryLockUidIsolationResp.Unmarshal(m, b)
}
func (m *TryLockUidIsolationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TryLockUidIsolationResp.Marshal(b, m, deterministic)
}
func (dst *TryLockUidIsolationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TryLockUidIsolationResp.Merge(dst, src)
}
func (m *TryLockUidIsolationResp) XXX_Size() int {
	return xxx_messageInfo_TryLockUidIsolationResp.Size(m)
}
func (m *TryLockUidIsolationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TryLockUidIsolationResp.DiscardUnknown(m)
}

var xxx_messageInfo_TryLockUidIsolationResp proto.InternalMessageInfo

func (m *TryLockUidIsolationResp) GetLock() bool {
	if m != nil {
		return m.Lock
	}
	return false
}

func (m *TryLockUidIsolationResp) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

type GetUidIsolationInfoReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUidIsolationInfoReq) Reset()         { *m = GetUidIsolationInfoReq{} }
func (m *GetUidIsolationInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUidIsolationInfoReq) ProtoMessage()    {}
func (*GetUidIsolationInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_isolator_ba34002a893b9afa, []int{6}
}
func (m *GetUidIsolationInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUidIsolationInfoReq.Unmarshal(m, b)
}
func (m *GetUidIsolationInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUidIsolationInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUidIsolationInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUidIsolationInfoReq.Merge(dst, src)
}
func (m *GetUidIsolationInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUidIsolationInfoReq.Size(m)
}
func (m *GetUidIsolationInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUidIsolationInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUidIsolationInfoReq proto.InternalMessageInfo

func (m *GetUidIsolationInfoReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUidIsolationInfoResp struct {
	Scene                string   `protobuf:"bytes,1,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUidIsolationInfoResp) Reset()         { *m = GetUidIsolationInfoResp{} }
func (m *GetUidIsolationInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUidIsolationInfoResp) ProtoMessage()    {}
func (*GetUidIsolationInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_isolator_ba34002a893b9afa, []int{7}
}
func (m *GetUidIsolationInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUidIsolationInfoResp.Unmarshal(m, b)
}
func (m *GetUidIsolationInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUidIsolationInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUidIsolationInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUidIsolationInfoResp.Merge(dst, src)
}
func (m *GetUidIsolationInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUidIsolationInfoResp.Size(m)
}
func (m *GetUidIsolationInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUidIsolationInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUidIsolationInfoResp proto.InternalMessageInfo

func (m *GetUidIsolationInfoResp) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

type CheckUidIsolationReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPkg           string   `protobuf:"bytes,3,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	TerminalType         uint32   `protobuf:"varint,4,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,5,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUidIsolationReq) Reset()         { *m = CheckUidIsolationReq{} }
func (m *CheckUidIsolationReq) String() string { return proto.CompactTextString(m) }
func (*CheckUidIsolationReq) ProtoMessage()    {}
func (*CheckUidIsolationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_isolator_ba34002a893b9afa, []int{8}
}
func (m *CheckUidIsolationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUidIsolationReq.Unmarshal(m, b)
}
func (m *CheckUidIsolationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUidIsolationReq.Marshal(b, m, deterministic)
}
func (dst *CheckUidIsolationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUidIsolationReq.Merge(dst, src)
}
func (m *CheckUidIsolationReq) XXX_Size() int {
	return xxx_messageInfo_CheckUidIsolationReq.Size(m)
}
func (m *CheckUidIsolationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUidIsolationReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUidIsolationReq proto.InternalMessageInfo

func (m *CheckUidIsolationReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUidIsolationReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *CheckUidIsolationReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *CheckUidIsolationReq) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *CheckUidIsolationReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

type CheckUidIsolationResp struct {
	Pass                 bool     `protobuf:"varint,1,opt,name=pass,proto3" json:"pass,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUidIsolationResp) Reset()         { *m = CheckUidIsolationResp{} }
func (m *CheckUidIsolationResp) String() string { return proto.CompactTextString(m) }
func (*CheckUidIsolationResp) ProtoMessage()    {}
func (*CheckUidIsolationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_account_isolator_ba34002a893b9afa, []int{9}
}
func (m *CheckUidIsolationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUidIsolationResp.Unmarshal(m, b)
}
func (m *CheckUidIsolationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUidIsolationResp.Marshal(b, m, deterministic)
}
func (dst *CheckUidIsolationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUidIsolationResp.Merge(dst, src)
}
func (m *CheckUidIsolationResp) XXX_Size() int {
	return xxx_messageInfo_CheckUidIsolationResp.Size(m)
}
func (m *CheckUidIsolationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUidIsolationResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUidIsolationResp proto.InternalMessageInfo

func (m *CheckUidIsolationResp) GetPass() bool {
	if m != nil {
		return m.Pass
	}
	return false
}

func init() {
	proto.RegisterType((*GetIsolationSequenceReq)(nil), "account_isolator.GetIsolationSequenceReq")
	proto.RegisterType((*GetIsolationSequenceResp)(nil), "account_isolator.GetIsolationSequenceResp")
	proto.RegisterType((*LockUidIsolationReq)(nil), "account_isolator.LockUidIsolationReq")
	proto.RegisterType((*LockUidIsolationResp)(nil), "account_isolator.LockUidIsolationResp")
	proto.RegisterType((*TryLockUidIsolationReq)(nil), "account_isolator.TryLockUidIsolationReq")
	proto.RegisterType((*TryLockUidIsolationResp)(nil), "account_isolator.TryLockUidIsolationResp")
	proto.RegisterType((*GetUidIsolationInfoReq)(nil), "account_isolator.GetUidIsolationInfoReq")
	proto.RegisterType((*GetUidIsolationInfoResp)(nil), "account_isolator.GetUidIsolationInfoResp")
	proto.RegisterType((*CheckUidIsolationReq)(nil), "account_isolator.CheckUidIsolationReq")
	proto.RegisterType((*CheckUidIsolationResp)(nil), "account_isolator.CheckUidIsolationResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AccountIsolatorClient is the client API for AccountIsolator service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AccountIsolatorClient interface {
	GetIsolationSequence(ctx context.Context, in *GetIsolationSequenceReq, opts ...grpc.CallOption) (*GetIsolationSequenceResp, error)
	LockUidIsolation(ctx context.Context, in *LockUidIsolationReq, opts ...grpc.CallOption) (*LockUidIsolationResp, error)
	TryLockUidIsolation(ctx context.Context, in *TryLockUidIsolationReq, opts ...grpc.CallOption) (*TryLockUidIsolationResp, error)
	GetUidIsolationInfo(ctx context.Context, in *GetUidIsolationInfoReq, opts ...grpc.CallOption) (*GetUidIsolationInfoResp, error)
	CheckUidIsolation(ctx context.Context, in *CheckUidIsolationReq, opts ...grpc.CallOption) (*CheckUidIsolationResp, error)
}

type accountIsolatorClient struct {
	cc *grpc.ClientConn
}

func NewAccountIsolatorClient(cc *grpc.ClientConn) AccountIsolatorClient {
	return &accountIsolatorClient{cc}
}

func (c *accountIsolatorClient) GetIsolationSequence(ctx context.Context, in *GetIsolationSequenceReq, opts ...grpc.CallOption) (*GetIsolationSequenceResp, error) {
	out := new(GetIsolationSequenceResp)
	err := c.cc.Invoke(ctx, "/account_isolator.AccountIsolator/GetIsolationSequence", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountIsolatorClient) LockUidIsolation(ctx context.Context, in *LockUidIsolationReq, opts ...grpc.CallOption) (*LockUidIsolationResp, error) {
	out := new(LockUidIsolationResp)
	err := c.cc.Invoke(ctx, "/account_isolator.AccountIsolator/LockUidIsolation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountIsolatorClient) TryLockUidIsolation(ctx context.Context, in *TryLockUidIsolationReq, opts ...grpc.CallOption) (*TryLockUidIsolationResp, error) {
	out := new(TryLockUidIsolationResp)
	err := c.cc.Invoke(ctx, "/account_isolator.AccountIsolator/TryLockUidIsolation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountIsolatorClient) GetUidIsolationInfo(ctx context.Context, in *GetUidIsolationInfoReq, opts ...grpc.CallOption) (*GetUidIsolationInfoResp, error) {
	out := new(GetUidIsolationInfoResp)
	err := c.cc.Invoke(ctx, "/account_isolator.AccountIsolator/GetUidIsolationInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountIsolatorClient) CheckUidIsolation(ctx context.Context, in *CheckUidIsolationReq, opts ...grpc.CallOption) (*CheckUidIsolationResp, error) {
	out := new(CheckUidIsolationResp)
	err := c.cc.Invoke(ctx, "/account_isolator.AccountIsolator/CheckUidIsolation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountIsolatorServer is the server API for AccountIsolator service.
type AccountIsolatorServer interface {
	GetIsolationSequence(context.Context, *GetIsolationSequenceReq) (*GetIsolationSequenceResp, error)
	LockUidIsolation(context.Context, *LockUidIsolationReq) (*LockUidIsolationResp, error)
	TryLockUidIsolation(context.Context, *TryLockUidIsolationReq) (*TryLockUidIsolationResp, error)
	GetUidIsolationInfo(context.Context, *GetUidIsolationInfoReq) (*GetUidIsolationInfoResp, error)
	CheckUidIsolation(context.Context, *CheckUidIsolationReq) (*CheckUidIsolationResp, error)
}

func RegisterAccountIsolatorServer(s *grpc.Server, srv AccountIsolatorServer) {
	s.RegisterService(&_AccountIsolator_serviceDesc, srv)
}

func _AccountIsolator_GetIsolationSequence_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIsolationSequenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountIsolatorServer).GetIsolationSequence(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_isolator.AccountIsolator/GetIsolationSequence",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountIsolatorServer).GetIsolationSequence(ctx, req.(*GetIsolationSequenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountIsolator_LockUidIsolation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LockUidIsolationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountIsolatorServer).LockUidIsolation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_isolator.AccountIsolator/LockUidIsolation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountIsolatorServer).LockUidIsolation(ctx, req.(*LockUidIsolationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountIsolator_TryLockUidIsolation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TryLockUidIsolationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountIsolatorServer).TryLockUidIsolation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_isolator.AccountIsolator/TryLockUidIsolation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountIsolatorServer).TryLockUidIsolation(ctx, req.(*TryLockUidIsolationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountIsolator_GetUidIsolationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUidIsolationInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountIsolatorServer).GetUidIsolationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_isolator.AccountIsolator/GetUidIsolationInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountIsolatorServer).GetUidIsolationInfo(ctx, req.(*GetUidIsolationInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountIsolator_CheckUidIsolation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUidIsolationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountIsolatorServer).CheckUidIsolation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account_isolator.AccountIsolator/CheckUidIsolation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountIsolatorServer).CheckUidIsolation(ctx, req.(*CheckUidIsolationReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AccountIsolator_serviceDesc = grpc.ServiceDesc{
	ServiceName: "account_isolator.AccountIsolator",
	HandlerType: (*AccountIsolatorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetIsolationSequence",
			Handler:    _AccountIsolator_GetIsolationSequence_Handler,
		},
		{
			MethodName: "LockUidIsolation",
			Handler:    _AccountIsolator_LockUidIsolation_Handler,
		},
		{
			MethodName: "TryLockUidIsolation",
			Handler:    _AccountIsolator_TryLockUidIsolation_Handler,
		},
		{
			MethodName: "GetUidIsolationInfo",
			Handler:    _AccountIsolator_GetUidIsolationInfo_Handler,
		},
		{
			MethodName: "CheckUidIsolation",
			Handler:    _AccountIsolator_CheckUidIsolation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "account-isolator/account-isolator.proto",
}

func init() {
	proto.RegisterFile("account-isolator/account-isolator.proto", fileDescriptor_account_isolator_ba34002a893b9afa)
}

var fileDescriptor_account_isolator_ba34002a893b9afa = []byte{
	// 490 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x54, 0x41, 0x6f, 0xd3, 0x30,
	0x14, 0x26, 0x6b, 0x36, 0xad, 0x0f, 0x0a, 0xc5, 0x2b, 0x5d, 0x14, 0x0e, 0x54, 0x41, 0x63, 0xed,
	0x10, 0xa9, 0x54, 0xe0, 0xc8, 0x01, 0x76, 0x98, 0x2a, 0x71, 0x40, 0x61, 0x70, 0xe0, 0x12, 0x05,
	0xf7, 0xad, 0x8b, 0xe2, 0xda, 0x5e, 0xec, 0x4e, 0xea, 0xef, 0xe1, 0xc8, 0x0d, 0xf1, 0x03, 0x51,
	0x9d, 0x04, 0x46, 0xe3, 0x42, 0xae, 0xbb, 0xd9, 0x9f, 0x3e, 0x7f, 0xef, 0xbd, 0x2f, 0xdf, 0x0b,
	0x1c, 0x27, 0x94, 0x8a, 0x25, 0xd7, 0x2f, 0x52, 0x25, 0x58, 0xa2, 0x45, 0x3e, 0xde, 0x04, 0x42,
	0x99, 0x0b, 0x2d, 0x48, 0xb7, 0xc4, 0xe3, 0x0a, 0x0f, 0xbe, 0x39, 0x70, 0x78, 0x86, 0x7a, 0x6a,
	0xee, 0xa9, 0xe0, 0x1f, 0xf1, 0x6a, 0x89, 0x9c, 0x62, 0x84, 0x57, 0xe4, 0x31, 0xb4, 0x17, 0x49,
	0x9e, 0xa1, 0x8e, 0xd3, 0x99, 0xe7, 0x0c, 0x9c, 0x61, 0x27, 0xda, 0x2f, 0x80, 0xe9, 0x8c, 0x3c,
	0x81, 0xbb, 0xf4, 0x32, 0xe1, 0x1c, 0x59, 0x2c, 0xb3, 0xb9, 0xb7, 0x33, 0x70, 0x86, 0xed, 0x08,
	0x4a, 0xe8, 0x43, 0x36, 0x27, 0x4f, 0xa1, 0xa3, 0x31, 0x5f, 0xa4, 0x3c, 0x61, 0xb1, 0x5e, 0x49,
	0xf4, 0x5a, 0x46, 0xe1, 0x5e, 0x05, 0x9e, 0xaf, 0x24, 0x92, 0x23, 0xb8, 0x4f, 0x59, 0x8a, 0x5c,
	0xc7, 0xd7, 0x98, 0xab, 0x54, 0x70, 0xcf, 0x35, 0xac, 0x4e, 0x81, 0x7e, 0x2e, 0xc0, 0x60, 0x02,
	0x9e, 0xbd, 0x49, 0x25, 0x49, 0x1f, 0xf6, 0x14, 0x45, 0x8e, 0xca, 0x73, 0x06, 0xad, 0x61, 0x3b,
	0x2a, 0x6f, 0xc1, 0x1b, 0x38, 0x78, 0x2f, 0x68, 0xf6, 0x29, 0x9d, 0xfd, 0x7e, 0xb7, 0x1e, 0xaa,
	0x0b, 0xad, 0x65, 0x39, 0x8e, 0x1b, 0xad, 0x8f, 0xa4, 0x07, 0xbb, 0xe6, 0x49, 0x39, 0x43, 0x71,
	0x09, 0xfa, 0xd0, 0xab, 0x3f, 0x57, 0x32, 0xf8, 0xe9, 0x40, 0xff, 0x3c, 0x5f, 0x35, 0x93, 0xfe,
	0xcb, 0xc1, 0x9d, 0x7f, 0x3b, 0xd8, 0xfa, 0xbf, 0x83, 0x6e, 0x23, 0x07, 0x77, 0x6d, 0x0e, 0x9e,
	0xc2, 0xa1, 0xb5, 0x6b, 0x25, 0x09, 0x01, 0x97, 0x09, 0x9a, 0x99, 0xbe, 0xf7, 0x23, 0x73, 0xde,
	0xe2, 0xc9, 0x09, 0xf4, 0xcf, 0x50, 0xdf, 0x14, 0x98, 0xf2, 0x0b, 0x61, 0x1d, 0x3d, 0x18, 0x9b,
	0x5c, 0xd5, 0xb9, 0x4a, 0xfe, 0x11, 0x77, 0x6e, 0x8a, 0xff, 0x70, 0xa0, 0x77, 0x7a, 0x89, 0xb7,
	0xcb, 0xd6, 0xe7, 0xf0, 0xc8, 0xd2, 0x73, 0x61, 0xaa, 0x4c, 0x94, 0xaa, 0x4c, 0x5d, 0x9f, 0x27,
	0xdf, 0x5d, 0x78, 0xf0, 0xb6, 0x58, 0xc0, 0x69, 0xb9, 0x7f, 0x44, 0x40, 0xcf, 0x96, 0x6c, 0x32,
	0x0a, 0x37, 0x57, 0x35, 0xdc, 0xb2, 0xa6, 0xfe, 0x49, 0x53, 0xaa, 0x92, 0xc1, 0x1d, 0x42, 0xa1,
	0xbb, 0x99, 0x02, 0x72, 0x54, 0x57, 0xb0, 0xe4, 0xdb, 0x7f, 0xd6, 0x84, 0x66, 0x8a, 0x30, 0x38,
	0xb0, 0xa4, 0x8d, 0x0c, 0xeb, 0x02, 0xf6, 0x55, 0xf2, 0x47, 0x0d, 0x99, 0x55, 0x35, 0x4b, 0xd4,
	0x6c, 0xd5, 0xec, 0xe9, 0xf5, 0x47, 0x0d, 0x99, 0xa6, 0xda, 0x05, 0x3c, 0xac, 0x7d, 0x72, 0x62,
	0xb1, 0xc6, 0x96, 0x65, 0xff, 0xb8, 0x11, 0x6f, 0x5d, 0xe7, 0xdd, 0xab, 0x2f, 0x93, 0xb9, 0x60,
	0x09, 0x9f, 0x87, 0xaf, 0x27, 0x5a, 0x87, 0x54, 0x2c, 0xc6, 0xe6, 0x27, 0x4e, 0x05, 0x1b, 0x2b,
	0xcc, 0xaf, 0x53, 0x8a, 0xaa, 0xf6, 0x9f, 0xff, 0xba, 0x67, 0x38, 0x2f, 0x7f, 0x05, 0x00, 0x00,
	0xff, 0xff, 0x6e, 0xb0, 0x06, 0x0f, 0x13, 0x06, 0x00, 0x00,
}
