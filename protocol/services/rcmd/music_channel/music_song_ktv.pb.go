// Code generated by protoc-gen-go. DO NOT EDIT.
// source: music-channel/music_song_ktv.proto

package music_channel // import "golang.52tt.com/protocol/services/rcmd/music_channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MusicSongKtvReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicSongKtvReq) Reset()         { *m = MusicSongKtvReq{} }
func (m *MusicSongKtvReq) String() string { return proto.CompactTextString(m) }
func (*MusicSongKtvReq) ProtoMessage()    {}
func (*MusicSongKtvReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_song_ktv_d95e9ddd75be2b83, []int{0}
}
func (m *MusicSongKtvReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicSongKtvReq.Unmarshal(m, b)
}
func (m *MusicSongKtvReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicSongKtvReq.Marshal(b, m, deterministic)
}
func (dst *MusicSongKtvReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicSongKtvReq.Merge(dst, src)
}
func (m *MusicSongKtvReq) XXX_Size() int {
	return xxx_messageInfo_MusicSongKtvReq.Size(m)
}
func (m *MusicSongKtvReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicSongKtvReq.DiscardUnknown(m)
}

var xxx_messageInfo_MusicSongKtvReq proto.InternalMessageInfo

func (m *MusicSongKtvReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MusicSongKtvReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type KTVCopyright struct {
	SongLyric            int32    `protobuf:"varint,1,opt,name=song_lyric,json=songLyric,proto3" json:"song_lyric,omitempty"`
	Recordingval         int32    `protobuf:"varint,2,opt,name=recordingval,proto3" json:"recordingval,omitempty"`
	Channel              int32    `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVCopyright) Reset()         { *m = KTVCopyright{} }
func (m *KTVCopyright) String() string { return proto.CompactTextString(m) }
func (*KTVCopyright) ProtoMessage()    {}
func (*KTVCopyright) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_song_ktv_d95e9ddd75be2b83, []int{1}
}
func (m *KTVCopyright) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVCopyright.Unmarshal(m, b)
}
func (m *KTVCopyright) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVCopyright.Marshal(b, m, deterministic)
}
func (dst *KTVCopyright) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVCopyright.Merge(dst, src)
}
func (m *KTVCopyright) XXX_Size() int {
	return xxx_messageInfo_KTVCopyright.Size(m)
}
func (m *KTVCopyright) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVCopyright.DiscardUnknown(m)
}

var xxx_messageInfo_KTVCopyright proto.InternalMessageInfo

func (m *KTVCopyright) GetSongLyric() int32 {
	if m != nil {
		return m.SongLyric
	}
	return 0
}

func (m *KTVCopyright) GetRecordingval() int32 {
	if m != nil {
		return m.Recordingval
	}
	return 0
}

func (m *KTVCopyright) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

// 歌曲信息
type SongInfo struct {
	SongId               string        `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName             string        `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SingerName           string        `protobuf:"bytes,3,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	AlbumName            string        `protobuf:"bytes,4,opt,name=album_name,json=albumName,proto3" json:"album_name,omitempty"`
	AlbumImg             string        `protobuf:"bytes,5,opt,name=album_img,json=albumImg,proto3" json:"album_img,omitempty"`
	Duration             int32         `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
	Copyright            *KTVCopyright `protobuf:"bytes,7,opt,name=copyright,proto3" json:"copyright,omitempty"`
	MetaId               string        `protobuf:"bytes,8,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	VendorId             int32         `protobuf:"varint,9,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	PitchAbility         int32         `protobuf:"varint,10,opt,name=pitch_ability,json=pitchAbility,proto3" json:"pitch_ability,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SongInfo) Reset()         { *m = SongInfo{} }
func (m *SongInfo) String() string { return proto.CompactTextString(m) }
func (*SongInfo) ProtoMessage()    {}
func (*SongInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_song_ktv_d95e9ddd75be2b83, []int{2}
}
func (m *SongInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongInfo.Unmarshal(m, b)
}
func (m *SongInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongInfo.Marshal(b, m, deterministic)
}
func (dst *SongInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongInfo.Merge(dst, src)
}
func (m *SongInfo) XXX_Size() int {
	return xxx_messageInfo_SongInfo.Size(m)
}
func (m *SongInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SongInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SongInfo proto.InternalMessageInfo

func (m *SongInfo) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *SongInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *SongInfo) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *SongInfo) GetAlbumName() string {
	if m != nil {
		return m.AlbumName
	}
	return ""
}

func (m *SongInfo) GetAlbumImg() string {
	if m != nil {
		return m.AlbumImg
	}
	return ""
}

func (m *SongInfo) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *SongInfo) GetCopyright() *KTVCopyright {
	if m != nil {
		return m.Copyright
	}
	return nil
}

func (m *SongInfo) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

func (m *SongInfo) GetVendorId() int32 {
	if m != nil {
		return m.VendorId
	}
	return 0
}

func (m *SongInfo) GetPitchAbility() int32 {
	if m != nil {
		return m.PitchAbility
	}
	return 0
}

type MusicSongKtvRsp struct {
	SongInfoList         []*SongInfo `protobuf:"bytes,1,rep,name=song_info_list,json=songInfoList,proto3" json:"song_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *MusicSongKtvRsp) Reset()         { *m = MusicSongKtvRsp{} }
func (m *MusicSongKtvRsp) String() string { return proto.CompactTextString(m) }
func (*MusicSongKtvRsp) ProtoMessage()    {}
func (*MusicSongKtvRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_song_ktv_d95e9ddd75be2b83, []int{3}
}
func (m *MusicSongKtvRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicSongKtvRsp.Unmarshal(m, b)
}
func (m *MusicSongKtvRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicSongKtvRsp.Marshal(b, m, deterministic)
}
func (dst *MusicSongKtvRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicSongKtvRsp.Merge(dst, src)
}
func (m *MusicSongKtvRsp) XXX_Size() int {
	return xxx_messageInfo_MusicSongKtvRsp.Size(m)
}
func (m *MusicSongKtvRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicSongKtvRsp.DiscardUnknown(m)
}

var xxx_messageInfo_MusicSongKtvRsp proto.InternalMessageInfo

func (m *MusicSongKtvRsp) GetSongInfoList() []*SongInfo {
	if m != nil {
		return m.SongInfoList
	}
	return nil
}

type MusicSongKtvBlockReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	BlockId              uint32   `protobuf:"varint,3,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicSongKtvBlockReq) Reset()         { *m = MusicSongKtvBlockReq{} }
func (m *MusicSongKtvBlockReq) String() string { return proto.CompactTextString(m) }
func (*MusicSongKtvBlockReq) ProtoMessage()    {}
func (*MusicSongKtvBlockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_music_song_ktv_d95e9ddd75be2b83, []int{4}
}
func (m *MusicSongKtvBlockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicSongKtvBlockReq.Unmarshal(m, b)
}
func (m *MusicSongKtvBlockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicSongKtvBlockReq.Marshal(b, m, deterministic)
}
func (dst *MusicSongKtvBlockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicSongKtvBlockReq.Merge(dst, src)
}
func (m *MusicSongKtvBlockReq) XXX_Size() int {
	return xxx_messageInfo_MusicSongKtvBlockReq.Size(m)
}
func (m *MusicSongKtvBlockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicSongKtvBlockReq.DiscardUnknown(m)
}

var xxx_messageInfo_MusicSongKtvBlockReq proto.InternalMessageInfo

func (m *MusicSongKtvBlockReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MusicSongKtvBlockReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *MusicSongKtvBlockReq) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func init() {
	proto.RegisterType((*MusicSongKtvReq)(nil), "rcmd.music_channel.MusicSongKtvReq")
	proto.RegisterType((*KTVCopyright)(nil), "rcmd.music_channel.KTVCopyright")
	proto.RegisterType((*SongInfo)(nil), "rcmd.music_channel.SongInfo")
	proto.RegisterType((*MusicSongKtvRsp)(nil), "rcmd.music_channel.MusicSongKtvRsp")
	proto.RegisterType((*MusicSongKtvBlockReq)(nil), "rcmd.music_channel.MusicSongKtvBlockReq")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MusicSongKtvClient is the client API for MusicSongKtv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MusicSongKtvClient interface {
	// KTV猜你喜欢
	GetKtvLikeSong(ctx context.Context, in *MusicSongKtvReq, opts ...grpc.CallOption) (*MusicSongKtvRsp, error)
	// KTV推荐列表
	GetKtvList(ctx context.Context, in *MusicSongKtvReq, opts ...grpc.CallOption) (*MusicSongKtvRsp, error)
	// KTV豆腐块内容推荐
	GetKtvBlockSong(ctx context.Context, in *MusicSongKtvBlockReq, opts ...grpc.CallOption) (*MusicSongKtvRsp, error)
}

type musicSongKtvClient struct {
	cc *grpc.ClientConn
}

func NewMusicSongKtvClient(cc *grpc.ClientConn) MusicSongKtvClient {
	return &musicSongKtvClient{cc}
}

func (c *musicSongKtvClient) GetKtvLikeSong(ctx context.Context, in *MusicSongKtvReq, opts ...grpc.CallOption) (*MusicSongKtvRsp, error) {
	out := new(MusicSongKtvRsp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicSongKtv/GetKtvLikeSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicSongKtvClient) GetKtvList(ctx context.Context, in *MusicSongKtvReq, opts ...grpc.CallOption) (*MusicSongKtvRsp, error) {
	out := new(MusicSongKtvRsp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicSongKtv/GetKtvList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *musicSongKtvClient) GetKtvBlockSong(ctx context.Context, in *MusicSongKtvBlockReq, opts ...grpc.CallOption) (*MusicSongKtvRsp, error) {
	out := new(MusicSongKtvRsp)
	err := c.cc.Invoke(ctx, "/rcmd.music_channel.MusicSongKtv/GetKtvBlockSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MusicSongKtvServer is the server API for MusicSongKtv service.
type MusicSongKtvServer interface {
	// KTV猜你喜欢
	GetKtvLikeSong(context.Context, *MusicSongKtvReq) (*MusicSongKtvRsp, error)
	// KTV推荐列表
	GetKtvList(context.Context, *MusicSongKtvReq) (*MusicSongKtvRsp, error)
	// KTV豆腐块内容推荐
	GetKtvBlockSong(context.Context, *MusicSongKtvBlockReq) (*MusicSongKtvRsp, error)
}

func RegisterMusicSongKtvServer(s *grpc.Server, srv MusicSongKtvServer) {
	s.RegisterService(&_MusicSongKtv_serviceDesc, srv)
}

func _MusicSongKtv_GetKtvLikeSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MusicSongKtvReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicSongKtvServer).GetKtvLikeSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicSongKtv/GetKtvLikeSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicSongKtvServer).GetKtvLikeSong(ctx, req.(*MusicSongKtvReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicSongKtv_GetKtvList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MusicSongKtvReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicSongKtvServer).GetKtvList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicSongKtv/GetKtvList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicSongKtvServer).GetKtvList(ctx, req.(*MusicSongKtvReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MusicSongKtv_GetKtvBlockSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MusicSongKtvBlockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MusicSongKtvServer).GetKtvBlockSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.music_channel.MusicSongKtv/GetKtvBlockSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MusicSongKtvServer).GetKtvBlockSong(ctx, req.(*MusicSongKtvBlockReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MusicSongKtv_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.music_channel.MusicSongKtv",
	HandlerType: (*MusicSongKtvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetKtvLikeSong",
			Handler:    _MusicSongKtv_GetKtvLikeSong_Handler,
		},
		{
			MethodName: "GetKtvList",
			Handler:    _MusicSongKtv_GetKtvList_Handler,
		},
		{
			MethodName: "GetKtvBlockSong",
			Handler:    _MusicSongKtv_GetKtvBlockSong_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "music-channel/music_song_ktv.proto",
}

func init() {
	proto.RegisterFile("music-channel/music_song_ktv.proto", fileDescriptor_music_song_ktv_d95e9ddd75be2b83)
}

var fileDescriptor_music_song_ktv_d95e9ddd75be2b83 = []byte{
	// 521 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x54, 0x5d, 0x6f, 0xd3, 0x30,
	0x14, 0x55, 0x5b, 0xfa, 0x91, 0xdb, 0x6e, 0x43, 0xd6, 0x24, 0xc2, 0xc6, 0x44, 0x95, 0xbd, 0xf4,
	0x85, 0x54, 0x2a, 0x1f, 0x12, 0x2f, 0x48, 0x94, 0x07, 0x54, 0xb5, 0xf0, 0x10, 0x60, 0x12, 0x7b,
	0x09, 0xa9, 0xe3, 0xa5, 0x56, 0x63, 0xbb, 0xd8, 0x6e, 0xa4, 0xfe, 0x1f, 0x7e, 0x12, 0x3f, 0x08,
	0xf9, 0xba, 0x85, 0x6e, 0x4c, 0x1a, 0x48, 0x7b, 0xcb, 0x39, 0xc7, 0xf7, 0xdc, 0x73, 0xaf, 0xad,
	0x40, 0x24, 0xd6, 0x86, 0xd3, 0x67, 0x74, 0x91, 0x49, 0xc9, 0xca, 0x21, 0xa2, 0xd4, 0x28, 0x59,
	0xa4, 0x4b, 0x5b, 0xc5, 0x2b, 0xad, 0xac, 0x22, 0x44, 0x53, 0x91, 0xc7, 0x5e, 0xda, 0x1e, 0x8c,
	0x5e, 0xc3, 0xd1, 0x07, 0x47, 0x7c, 0x52, 0xb2, 0x98, 0xda, 0x2a, 0x61, 0xdf, 0xc9, 0x43, 0x68,
	0xac, 0x79, 0x1e, 0xd6, 0xfa, 0xb5, 0xc1, 0x41, 0xe2, 0x3e, 0xc9, 0x31, 0x34, 0x4b, 0x2e, 0xb8,
	0x0d, 0xeb, 0xc8, 0x79, 0x10, 0x2d, 0xa1, 0x37, 0xfd, 0x7c, 0xf1, 0x4e, 0xad, 0x36, 0x9a, 0x17,
	0x0b, 0x4b, 0xce, 0x00, 0xb0, 0x61, 0xb9, 0xd1, 0x9c, 0x62, 0x79, 0x33, 0x09, 0x1c, 0x33, 0x73,
	0x04, 0x89, 0xa0, 0xa7, 0x19, 0x55, 0x3a, 0xe7, 0xb2, 0xa8, 0xb2, 0x12, 0xbd, 0x9a, 0xc9, 0x35,
	0x8e, 0x84, 0xd0, 0xde, 0x06, 0x0b, 0x1b, 0x28, 0xef, 0x60, 0xf4, 0xb3, 0x0e, 0x1d, 0x97, 0x71,
	0x22, 0xaf, 0x14, 0x79, 0x04, 0x6d, 0xec, 0xb4, 0x4d, 0x19, 0x24, 0x2d, 0x07, 0x27, 0x39, 0x39,
	0x05, 0x6c, 0x98, 0xca, 0x4c, 0x30, 0x6c, 0x10, 0x24, 0x1d, 0x47, 0x7c, 0xcc, 0x04, 0x23, 0x4f,
	0xa1, 0x6b, 0xb8, 0x2c, 0x98, 0xf6, 0x72, 0x03, 0x65, 0xf0, 0x14, 0x1e, 0x38, 0x03, 0xc8, 0xca,
	0xf9, 0x5a, 0x78, 0xfd, 0x01, 0xea, 0x01, 0x32, 0x28, 0x9f, 0x82, 0x07, 0x29, 0x17, 0x45, 0xd8,
	0xf4, 0xe6, 0x48, 0x4c, 0x44, 0x41, 0x4e, 0xa0, 0x93, 0xaf, 0x75, 0x66, 0xb9, 0x92, 0x61, 0x0b,
	0xa3, 0xff, 0xc6, 0xe4, 0x0d, 0x04, 0x74, 0xb7, 0xa5, 0xb0, 0xdd, 0xaf, 0x0d, 0xba, 0xa3, 0x7e,
	0xfc, 0xf7, 0x5d, 0xc4, 0xfb, 0xdb, 0x4c, 0xfe, 0x94, 0xb8, 0x71, 0x05, 0xb3, 0x99, 0x1b, 0xb7,
	0xe3, 0xc7, 0x75, 0xd0, 0x8f, 0x5b, 0x31, 0x99, 0x2b, 0xed, 0xa4, 0xc0, 0x77, 0xf5, 0xc4, 0x24,
	0x27, 0xe7, 0x70, 0xb0, 0xe2, 0x96, 0x2e, 0xd2, 0x6c, 0xce, 0x4b, 0x6e, 0x37, 0x21, 0xf8, 0x85,
	0x23, 0xf9, 0xd6, 0x73, 0xd1, 0x97, 0x1b, 0xd7, 0x6f, 0x56, 0x64, 0x0c, 0x87, 0x7e, 0xb9, 0xf2,
	0x4a, 0xa5, 0x25, 0x37, 0x36, 0xac, 0xf5, 0x1b, 0x83, 0xee, 0xe8, 0xc9, 0x6d, 0x91, 0x77, 0x57,
	0x92, 0xf4, 0xcc, 0xf6, 0x6b, 0xc6, 0x8d, 0x8d, 0xbe, 0xc2, 0xf1, 0xbe, 0xed, 0xb8, 0x54, 0x74,
	0xf9, 0x1f, 0x4f, 0x8b, 0x3c, 0x86, 0xce, 0xdc, 0xd5, 0xb8, 0xb9, 0x1a, 0x28, 0xb4, 0x11, 0x4f,
	0xf2, 0xd1, 0x8f, 0x3a, 0xf4, 0xf6, 0xbd, 0xc9, 0x25, 0x1c, 0xbe, 0x67, 0x76, 0x6a, 0xab, 0x19,
	0x5f, 0x32, 0x47, 0x92, 0xf3, 0xdb, 0x92, 0xde, 0x78, 0xe5, 0x27, 0x77, 0x1f, 0x32, 0x2b, 0x72,
	0x01, 0xb0, 0xf3, 0x36, 0xf6, 0x1e, 0x7d, 0xbf, 0xc1, 0x91, 0xf7, 0xc5, 0xcd, 0x60, 0xe8, 0xc1,
	0x5d, 0x75, 0xbb, 0x25, 0xfe, 0x53, 0x87, 0xf1, 0xab, 0xcb, 0x17, 0x85, 0x2a, 0x33, 0x59, 0xc4,
	0x2f, 0x47, 0xd6, 0xc6, 0x54, 0x89, 0x21, 0xfe, 0x04, 0xa8, 0x2a, 0x87, 0x86, 0xe9, 0x8a, 0x53,
	0x66, 0x86, 0xce, 0x67, 0x78, 0xcd, 0x67, 0xde, 0xc2, 0x53, 0xcf, 0x7f, 0x05, 0x00, 0x00, 0xff,
	0xff, 0x9b, 0x7e, 0xab, 0xed, 0x50, 0x04, 0x00, 0x00,
}
