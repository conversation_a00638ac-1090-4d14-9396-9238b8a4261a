// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/abtestlogic/abtestlogic.proto

package abtestlogic // import "golang.52tt.com/protocol/services/rcmd/abtestlogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PS_AbtestDomain struct {
	Did                  uint32   `protobuf:"varint,1,opt,name=did,proto3" json:"did,omitempty"`
	UpLayerId            uint32   `protobuf:"varint,2,opt,name=up_layer_id,json=upLayerId,proto3" json:"up_layer_id,omitempty"`
	LdRatio              uint32   `protobuf:"varint,3,opt,name=ld_ratio,json=ldRatio,proto3" json:"ld_ratio,omitempty"`
	RatioVer             uint32   `protobuf:"varint,4,opt,name=ratio_ver,json=ratioVer,proto3" json:"ratio_ver,omitempty"`
	UpLayerTag           string   `protobuf:"bytes,5,opt,name=up_layer_tag,json=upLayerTag,proto3" json:"up_layer_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PS_AbtestDomain) Reset()         { *m = PS_AbtestDomain{} }
func (m *PS_AbtestDomain) String() string { return proto.CompactTextString(m) }
func (*PS_AbtestDomain) ProtoMessage()    {}
func (*PS_AbtestDomain) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{0}
}
func (m *PS_AbtestDomain) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_AbtestDomain.Unmarshal(m, b)
}
func (m *PS_AbtestDomain) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_AbtestDomain.Marshal(b, m, deterministic)
}
func (dst *PS_AbtestDomain) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_AbtestDomain.Merge(dst, src)
}
func (m *PS_AbtestDomain) XXX_Size() int {
	return xxx_messageInfo_PS_AbtestDomain.Size(m)
}
func (m *PS_AbtestDomain) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_AbtestDomain.DiscardUnknown(m)
}

var xxx_messageInfo_PS_AbtestDomain proto.InternalMessageInfo

func (m *PS_AbtestDomain) GetDid() uint32 {
	if m != nil {
		return m.Did
	}
	return 0
}

func (m *PS_AbtestDomain) GetUpLayerId() uint32 {
	if m != nil {
		return m.UpLayerId
	}
	return 0
}

func (m *PS_AbtestDomain) GetLdRatio() uint32 {
	if m != nil {
		return m.LdRatio
	}
	return 0
}

func (m *PS_AbtestDomain) GetRatioVer() uint32 {
	if m != nil {
		return m.RatioVer
	}
	return 0
}

func (m *PS_AbtestDomain) GetUpLayerTag() string {
	if m != nil {
		return m.UpLayerTag
	}
	return ""
}

type PS_AbtestDomainRoute struct {
	UpDomainList         []*PS_AbtestDomain `protobuf:"bytes,1,rep,name=up_domain_list,json=upDomainList,proto3" json:"up_domain_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PS_AbtestDomainRoute) Reset()         { *m = PS_AbtestDomainRoute{} }
func (m *PS_AbtestDomainRoute) String() string { return proto.CompactTextString(m) }
func (*PS_AbtestDomainRoute) ProtoMessage()    {}
func (*PS_AbtestDomainRoute) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{1}
}
func (m *PS_AbtestDomainRoute) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_AbtestDomainRoute.Unmarshal(m, b)
}
func (m *PS_AbtestDomainRoute) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_AbtestDomainRoute.Marshal(b, m, deterministic)
}
func (dst *PS_AbtestDomainRoute) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_AbtestDomainRoute.Merge(dst, src)
}
func (m *PS_AbtestDomainRoute) XXX_Size() int {
	return xxx_messageInfo_PS_AbtestDomainRoute.Size(m)
}
func (m *PS_AbtestDomainRoute) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_AbtestDomainRoute.DiscardUnknown(m)
}

var xxx_messageInfo_PS_AbtestDomainRoute proto.InternalMessageInfo

func (m *PS_AbtestDomainRoute) GetUpDomainList() []*PS_AbtestDomain {
	if m != nil {
		return m.UpDomainList
	}
	return nil
}

type PS_AbtestExptVer struct {
	ExptvId              uint32                `protobuf:"varint,1,opt,name=exptv_id,json=exptvId,proto3" json:"exptv_id,omitempty"`
	ExptvRatio           uint32                `protobuf:"varint,2,opt,name=exptv_ratio,json=exptvRatio,proto3" json:"exptv_ratio,omitempty"`
	ExptId               uint32                `protobuf:"varint,3,opt,name=expt_id,json=exptId,proto3" json:"expt_id,omitempty"`
	ExptRatio            uint32                `protobuf:"varint,4,opt,name=expt_ratio,json=exptRatio,proto3" json:"expt_ratio,omitempty"`
	ExptStatus           uint32                `protobuf:"varint,5,opt,name=expt_status,json=exptStatus,proto3" json:"expt_status,omitempty"`
	LayerId              uint32                `protobuf:"varint,6,opt,name=layer_id,json=layerId,proto3" json:"layer_id,omitempty"`
	ExptBegin            uint32                `protobuf:"varint,7,opt,name=expt_begin,json=exptBegin,proto3" json:"expt_begin,omitempty"`
	ExptEnd              uint32                `protobuf:"varint,8,opt,name=expt_end,json=exptEnd,proto3" json:"expt_end,omitempty"`
	RatioVer             uint32                `protobuf:"varint,9,opt,name=ratio_ver,json=ratioVer,proto3" json:"ratio_ver,omitempty"`
	LayerTag             string                `protobuf:"bytes,10,opt,name=layer_tag,json=layerTag,proto3" json:"layer_tag,omitempty"`
	DomainRoute          *PS_AbtestDomainRoute `protobuf:"bytes,11,opt,name=domain_route,json=domainRoute,proto3" json:"domain_route,omitempty"`
	ClientType           uint32                `protobuf:"varint,12,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	TerminalType         uint32                `protobuf:"varint,13,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *PS_AbtestExptVer) Reset()         { *m = PS_AbtestExptVer{} }
func (m *PS_AbtestExptVer) String() string { return proto.CompactTextString(m) }
func (*PS_AbtestExptVer) ProtoMessage()    {}
func (*PS_AbtestExptVer) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{2}
}
func (m *PS_AbtestExptVer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_AbtestExptVer.Unmarshal(m, b)
}
func (m *PS_AbtestExptVer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_AbtestExptVer.Marshal(b, m, deterministic)
}
func (dst *PS_AbtestExptVer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_AbtestExptVer.Merge(dst, src)
}
func (m *PS_AbtestExptVer) XXX_Size() int {
	return xxx_messageInfo_PS_AbtestExptVer.Size(m)
}
func (m *PS_AbtestExptVer) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_AbtestExptVer.DiscardUnknown(m)
}

var xxx_messageInfo_PS_AbtestExptVer proto.InternalMessageInfo

func (m *PS_AbtestExptVer) GetExptvId() uint32 {
	if m != nil {
		return m.ExptvId
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptvRatio() uint32 {
	if m != nil {
		return m.ExptvRatio
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptId() uint32 {
	if m != nil {
		return m.ExptId
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptRatio() uint32 {
	if m != nil {
		return m.ExptRatio
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptStatus() uint32 {
	if m != nil {
		return m.ExptStatus
	}
	return 0
}

func (m *PS_AbtestExptVer) GetLayerId() uint32 {
	if m != nil {
		return m.LayerId
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptBegin() uint32 {
	if m != nil {
		return m.ExptBegin
	}
	return 0
}

func (m *PS_AbtestExptVer) GetExptEnd() uint32 {
	if m != nil {
		return m.ExptEnd
	}
	return 0
}

func (m *PS_AbtestExptVer) GetRatioVer() uint32 {
	if m != nil {
		return m.RatioVer
	}
	return 0
}

func (m *PS_AbtestExptVer) GetLayerTag() string {
	if m != nil {
		return m.LayerTag
	}
	return ""
}

func (m *PS_AbtestExptVer) GetDomainRoute() *PS_AbtestDomainRoute {
	if m != nil {
		return m.DomainRoute
	}
	return nil
}

func (m *PS_AbtestExptVer) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *PS_AbtestExptVer) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

type PS_AbtestExpt struct {
	ExptArgv             map[string]string `protobuf:"bytes,1,rep,name=expt_argv,json=exptArgv,proto3" json:"expt_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ExptVer              *PS_AbtestExptVer `protobuf:"bytes,2,opt,name=expt_ver,json=exptVer,proto3" json:"expt_ver,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PS_AbtestExpt) Reset()         { *m = PS_AbtestExpt{} }
func (m *PS_AbtestExpt) String() string { return proto.CompactTextString(m) }
func (*PS_AbtestExpt) ProtoMessage()    {}
func (*PS_AbtestExpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{3}
}
func (m *PS_AbtestExpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_AbtestExpt.Unmarshal(m, b)
}
func (m *PS_AbtestExpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_AbtestExpt.Marshal(b, m, deterministic)
}
func (dst *PS_AbtestExpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_AbtestExpt.Merge(dst, src)
}
func (m *PS_AbtestExpt) XXX_Size() int {
	return xxx_messageInfo_PS_AbtestExpt.Size(m)
}
func (m *PS_AbtestExpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_AbtestExpt.DiscardUnknown(m)
}

var xxx_messageInfo_PS_AbtestExpt proto.InternalMessageInfo

func (m *PS_AbtestExpt) GetExptArgv() map[string]string {
	if m != nil {
		return m.ExptArgv
	}
	return nil
}

func (m *PS_AbtestExpt) GetExptVer() *PS_AbtestExptVer {
	if m != nil {
		return m.ExptVer
	}
	return nil
}

type PS_AbtestUser struct {
	ClientType           uint32   `protobuf:"varint,1,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientId             string   `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PS_AbtestUser) Reset()         { *m = PS_AbtestUser{} }
func (m *PS_AbtestUser) String() string { return proto.CompactTextString(m) }
func (*PS_AbtestUser) ProtoMessage()    {}
func (*PS_AbtestUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{4}
}
func (m *PS_AbtestUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_AbtestUser.Unmarshal(m, b)
}
func (m *PS_AbtestUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_AbtestUser.Marshal(b, m, deterministic)
}
func (dst *PS_AbtestUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_AbtestUser.Merge(dst, src)
}
func (m *PS_AbtestUser) XXX_Size() int {
	return xxx_messageInfo_PS_AbtestUser.Size(m)
}
func (m *PS_AbtestUser) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_AbtestUser.DiscardUnknown(m)
}

var xxx_messageInfo_PS_AbtestUser proto.InternalMessageInfo

func (m *PS_AbtestUser) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *PS_AbtestUser) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

type PS_UsersAbtest struct {
	User        *PS_AbtestUser    `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	ExptList    []*PS_AbtestExpt  `protobuf:"bytes,2,rep,name=expt_list,json=exptList,proto3" json:"expt_list,omitempty"`
	DefaultArgv map[string]string `protobuf:"bytes,3,rep,name=default_argv,json=defaultArgv,proto3" json:"default_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// default_argv 中的key与 expt_list 中 expt_argv 中的key互斥
	AllArgv              map[string]string `protobuf:"bytes,4,rep,name=all_argv,json=allArgv,proto3" json:"all_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PS_UsersAbtest) Reset()         { *m = PS_UsersAbtest{} }
func (m *PS_UsersAbtest) String() string { return proto.CompactTextString(m) }
func (*PS_UsersAbtest) ProtoMessage()    {}
func (*PS_UsersAbtest) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{5}
}
func (m *PS_UsersAbtest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_UsersAbtest.Unmarshal(m, b)
}
func (m *PS_UsersAbtest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_UsersAbtest.Marshal(b, m, deterministic)
}
func (dst *PS_UsersAbtest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_UsersAbtest.Merge(dst, src)
}
func (m *PS_UsersAbtest) XXX_Size() int {
	return xxx_messageInfo_PS_UsersAbtest.Size(m)
}
func (m *PS_UsersAbtest) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_UsersAbtest.DiscardUnknown(m)
}

var xxx_messageInfo_PS_UsersAbtest proto.InternalMessageInfo

func (m *PS_UsersAbtest) GetUser() *PS_AbtestUser {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *PS_UsersAbtest) GetExptList() []*PS_AbtestExpt {
	if m != nil {
		return m.ExptList
	}
	return nil
}

func (m *PS_UsersAbtest) GetDefaultArgv() map[string]string {
	if m != nil {
		return m.DefaultArgv
	}
	return nil
}

func (m *PS_UsersAbtest) GetAllArgv() map[string]string {
	if m != nil {
		return m.AllArgv
	}
	return nil
}

// 按层标签搜索用户的实验配置信息
type PS_GetUsersAbtestByTagReq struct {
	LayerTag             string           `protobuf:"bytes,1,opt,name=layer_tag,json=layerTag,proto3" json:"layer_tag,omitempty"`
	UserList             []*PS_AbtestUser `protobuf:"bytes,2,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	IsNotNeedExptVer     bool             `protobuf:"varint,3,opt,name=is_not_need_expt_ver,json=isNotNeedExptVer,proto3" json:"is_not_need_expt_ver,omitempty"`
	IsNotNeedSubmit      bool             `protobuf:"varint,4,opt,name=is_not_need_submit,json=isNotNeedSubmit,proto3" json:"is_not_need_submit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PS_GetUsersAbtestByTagReq) Reset()         { *m = PS_GetUsersAbtestByTagReq{} }
func (m *PS_GetUsersAbtestByTagReq) String() string { return proto.CompactTextString(m) }
func (*PS_GetUsersAbtestByTagReq) ProtoMessage()    {}
func (*PS_GetUsersAbtestByTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{6}
}
func (m *PS_GetUsersAbtestByTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_GetUsersAbtestByTagReq.Unmarshal(m, b)
}
func (m *PS_GetUsersAbtestByTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_GetUsersAbtestByTagReq.Marshal(b, m, deterministic)
}
func (dst *PS_GetUsersAbtestByTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_GetUsersAbtestByTagReq.Merge(dst, src)
}
func (m *PS_GetUsersAbtestByTagReq) XXX_Size() int {
	return xxx_messageInfo_PS_GetUsersAbtestByTagReq.Size(m)
}
func (m *PS_GetUsersAbtestByTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_GetUsersAbtestByTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_PS_GetUsersAbtestByTagReq proto.InternalMessageInfo

func (m *PS_GetUsersAbtestByTagReq) GetLayerTag() string {
	if m != nil {
		return m.LayerTag
	}
	return ""
}

func (m *PS_GetUsersAbtestByTagReq) GetUserList() []*PS_AbtestUser {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *PS_GetUsersAbtestByTagReq) GetIsNotNeedExptVer() bool {
	if m != nil {
		return m.IsNotNeedExptVer
	}
	return false
}

func (m *PS_GetUsersAbtestByTagReq) GetIsNotNeedSubmit() bool {
	if m != nil {
		return m.IsNotNeedSubmit
	}
	return false
}

type PS_GetUsersAbtestByTagRsp struct {
	UserTestList         []*PS_UsersAbtest `protobuf:"bytes,1,rep,name=user_test_list,json=userTestList,proto3" json:"user_test_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PS_GetUsersAbtestByTagRsp) Reset()         { *m = PS_GetUsersAbtestByTagRsp{} }
func (m *PS_GetUsersAbtestByTagRsp) String() string { return proto.CompactTextString(m) }
func (*PS_GetUsersAbtestByTagRsp) ProtoMessage()    {}
func (*PS_GetUsersAbtestByTagRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{7}
}
func (m *PS_GetUsersAbtestByTagRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_GetUsersAbtestByTagRsp.Unmarshal(m, b)
}
func (m *PS_GetUsersAbtestByTagRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_GetUsersAbtestByTagRsp.Marshal(b, m, deterministic)
}
func (dst *PS_GetUsersAbtestByTagRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_GetUsersAbtestByTagRsp.Merge(dst, src)
}
func (m *PS_GetUsersAbtestByTagRsp) XXX_Size() int {
	return xxx_messageInfo_PS_GetUsersAbtestByTagRsp.Size(m)
}
func (m *PS_GetUsersAbtestByTagRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_GetUsersAbtestByTagRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PS_GetUsersAbtestByTagRsp proto.InternalMessageInfo

func (m *PS_GetUsersAbtestByTagRsp) GetUserTestList() []*PS_UsersAbtest {
	if m != nil {
		return m.UserTestList
	}
	return nil
}

// 获取用户所有的实验配置信息 请求data字段内容 (用户id在 base_req 中传递)
type PS_GetUserAllAbtestReq struct {
	TerminalType         uint32   `protobuf:"varint,1,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PS_GetUserAllAbtestReq) Reset()         { *m = PS_GetUserAllAbtestReq{} }
func (m *PS_GetUserAllAbtestReq) String() string { return proto.CompactTextString(m) }
func (*PS_GetUserAllAbtestReq) ProtoMessage()    {}
func (*PS_GetUserAllAbtestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{8}
}
func (m *PS_GetUserAllAbtestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_GetUserAllAbtestReq.Unmarshal(m, b)
}
func (m *PS_GetUserAllAbtestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_GetUserAllAbtestReq.Marshal(b, m, deterministic)
}
func (dst *PS_GetUserAllAbtestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_GetUserAllAbtestReq.Merge(dst, src)
}
func (m *PS_GetUserAllAbtestReq) XXX_Size() int {
	return xxx_messageInfo_PS_GetUserAllAbtestReq.Size(m)
}
func (m *PS_GetUserAllAbtestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_GetUserAllAbtestReq.DiscardUnknown(m)
}

var xxx_messageInfo_PS_GetUserAllAbtestReq proto.InternalMessageInfo

func (m *PS_GetUserAllAbtestReq) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

// 获取用户所有的实验配置信息  响应data字段内容
type PS_GetUserAllAbtestRsp struct {
	ExptList    []*PS_AbtestExpt  `protobuf:"bytes,1,rep,name=expt_list,json=exptList,proto3" json:"expt_list,omitempty"`
	DefaultArgv map[string]string `protobuf:"bytes,2,rep,name=default_argv,json=defaultArgv,proto3" json:"default_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// default_argv 中的key与 expt_list 中 expt_argv 中的key互斥
	UidDefaultArgv       map[string]string `protobuf:"bytes,3,rep,name=uid_default_argv,json=uidDefaultArgv,proto3" json:"uid_default_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	DeviceidDefaultArgv  map[string]string `protobuf:"bytes,4,rep,name=deviceid_default_argv,json=deviceidDefaultArgv,proto3" json:"deviceid_default_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PS_GetUserAllAbtestRsp) Reset()         { *m = PS_GetUserAllAbtestRsp{} }
func (m *PS_GetUserAllAbtestRsp) String() string { return proto.CompactTextString(m) }
func (*PS_GetUserAllAbtestRsp) ProtoMessage()    {}
func (*PS_GetUserAllAbtestRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{9}
}
func (m *PS_GetUserAllAbtestRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_GetUserAllAbtestRsp.Unmarshal(m, b)
}
func (m *PS_GetUserAllAbtestRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_GetUserAllAbtestRsp.Marshal(b, m, deterministic)
}
func (dst *PS_GetUserAllAbtestRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_GetUserAllAbtestRsp.Merge(dst, src)
}
func (m *PS_GetUserAllAbtestRsp) XXX_Size() int {
	return xxx_messageInfo_PS_GetUserAllAbtestRsp.Size(m)
}
func (m *PS_GetUserAllAbtestRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_GetUserAllAbtestRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PS_GetUserAllAbtestRsp proto.InternalMessageInfo

func (m *PS_GetUserAllAbtestRsp) GetExptList() []*PS_AbtestExpt {
	if m != nil {
		return m.ExptList
	}
	return nil
}

func (m *PS_GetUserAllAbtestRsp) GetDefaultArgv() map[string]string {
	if m != nil {
		return m.DefaultArgv
	}
	return nil
}

func (m *PS_GetUserAllAbtestRsp) GetUidDefaultArgv() map[string]string {
	if m != nil {
		return m.UidDefaultArgv
	}
	return nil
}

func (m *PS_GetUserAllAbtestRsp) GetDeviceidDefaultArgv() map[string]string {
	if m != nil {
		return m.DeviceidDefaultArgv
	}
	return nil
}

// 查询用户指定参数的值 请求data字段内容  (用户id在 base_req 中传递)
type PS_GetUserAbtestByArgkeysReq struct {
	ArgKeyList           []string `protobuf:"bytes,1,rep,name=arg_key_list,json=argKeyList,proto3" json:"arg_key_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PS_GetUserAbtestByArgkeysReq) Reset()         { *m = PS_GetUserAbtestByArgkeysReq{} }
func (m *PS_GetUserAbtestByArgkeysReq) String() string { return proto.CompactTextString(m) }
func (*PS_GetUserAbtestByArgkeysReq) ProtoMessage()    {}
func (*PS_GetUserAbtestByArgkeysReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{10}
}
func (m *PS_GetUserAbtestByArgkeysReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_GetUserAbtestByArgkeysReq.Unmarshal(m, b)
}
func (m *PS_GetUserAbtestByArgkeysReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_GetUserAbtestByArgkeysReq.Marshal(b, m, deterministic)
}
func (dst *PS_GetUserAbtestByArgkeysReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_GetUserAbtestByArgkeysReq.Merge(dst, src)
}
func (m *PS_GetUserAbtestByArgkeysReq) XXX_Size() int {
	return xxx_messageInfo_PS_GetUserAbtestByArgkeysReq.Size(m)
}
func (m *PS_GetUserAbtestByArgkeysReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_GetUserAbtestByArgkeysReq.DiscardUnknown(m)
}

var xxx_messageInfo_PS_GetUserAbtestByArgkeysReq proto.InternalMessageInfo

func (m *PS_GetUserAbtestByArgkeysReq) GetArgKeyList() []string {
	if m != nil {
		return m.ArgKeyList
	}
	return nil
}

// 查询用户指定参数的值  响应data字段内容
type PS_GetUserAbtestByArgkeysRsp struct {
	ExptList    []*PS_AbtestExpt  `protobuf:"bytes,1,rep,name=expt_list,json=exptList,proto3" json:"expt_list,omitempty"`
	DefaultArgv map[string]string `protobuf:"bytes,2,rep,name=default_argv,json=defaultArgv,proto3" json:"default_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// default_argv 中的key与 expt_list 中 expt_argv 中的key互斥
	UidDefaultArgv       map[string]string `protobuf:"bytes,3,rep,name=uid_default_argv,json=uidDefaultArgv,proto3" json:"uid_default_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	DeviceidDefaultArgv  map[string]string `protobuf:"bytes,4,rep,name=deviceid_default_argv,json=deviceidDefaultArgv,proto3" json:"deviceid_default_argv,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PS_GetUserAbtestByArgkeysRsp) Reset()         { *m = PS_GetUserAbtestByArgkeysRsp{} }
func (m *PS_GetUserAbtestByArgkeysRsp) String() string { return proto.CompactTextString(m) }
func (*PS_GetUserAbtestByArgkeysRsp) ProtoMessage()    {}
func (*PS_GetUserAbtestByArgkeysRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{11}
}
func (m *PS_GetUserAbtestByArgkeysRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_GetUserAbtestByArgkeysRsp.Unmarshal(m, b)
}
func (m *PS_GetUserAbtestByArgkeysRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_GetUserAbtestByArgkeysRsp.Marshal(b, m, deterministic)
}
func (dst *PS_GetUserAbtestByArgkeysRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_GetUserAbtestByArgkeysRsp.Merge(dst, src)
}
func (m *PS_GetUserAbtestByArgkeysRsp) XXX_Size() int {
	return xxx_messageInfo_PS_GetUserAbtestByArgkeysRsp.Size(m)
}
func (m *PS_GetUserAbtestByArgkeysRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_GetUserAbtestByArgkeysRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PS_GetUserAbtestByArgkeysRsp proto.InternalMessageInfo

func (m *PS_GetUserAbtestByArgkeysRsp) GetExptList() []*PS_AbtestExpt {
	if m != nil {
		return m.ExptList
	}
	return nil
}

func (m *PS_GetUserAbtestByArgkeysRsp) GetDefaultArgv() map[string]string {
	if m != nil {
		return m.DefaultArgv
	}
	return nil
}

func (m *PS_GetUserAbtestByArgkeysRsp) GetUidDefaultArgv() map[string]string {
	if m != nil {
		return m.UidDefaultArgv
	}
	return nil
}

func (m *PS_GetUserAbtestByArgkeysRsp) GetDeviceidDefaultArgv() map[string]string {
	if m != nil {
		return m.DeviceidDefaultArgv
	}
	return nil
}

// 上报用户实验分组信息 请求data字段内容  (用户id在 base_req 中传递)
type PS_SubmitUserAbtestReq struct {
	ExptVer              *PS_AbtestExptVer `protobuf:"bytes,1,opt,name=expt_ver,json=exptVer,proto3" json:"expt_ver,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PS_SubmitUserAbtestReq) Reset()         { *m = PS_SubmitUserAbtestReq{} }
func (m *PS_SubmitUserAbtestReq) String() string { return proto.CompactTextString(m) }
func (*PS_SubmitUserAbtestReq) ProtoMessage()    {}
func (*PS_SubmitUserAbtestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{12}
}
func (m *PS_SubmitUserAbtestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_SubmitUserAbtestReq.Unmarshal(m, b)
}
func (m *PS_SubmitUserAbtestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_SubmitUserAbtestReq.Marshal(b, m, deterministic)
}
func (dst *PS_SubmitUserAbtestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_SubmitUserAbtestReq.Merge(dst, src)
}
func (m *PS_SubmitUserAbtestReq) XXX_Size() int {
	return xxx_messageInfo_PS_SubmitUserAbtestReq.Size(m)
}
func (m *PS_SubmitUserAbtestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_SubmitUserAbtestReq.DiscardUnknown(m)
}

var xxx_messageInfo_PS_SubmitUserAbtestReq proto.InternalMessageInfo

func (m *PS_SubmitUserAbtestReq) GetExptVer() *PS_AbtestExptVer {
	if m != nil {
		return m.ExptVer
	}
	return nil
}

// 上报用户实验分组信息  响应data字段内容
type PS_SubmitUserAbtestRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PS_SubmitUserAbtestRsp) Reset()         { *m = PS_SubmitUserAbtestRsp{} }
func (m *PS_SubmitUserAbtestRsp) String() string { return proto.CompactTextString(m) }
func (*PS_SubmitUserAbtestRsp) ProtoMessage()    {}
func (*PS_SubmitUserAbtestRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_abtestlogic_52fccbbaae41f16e, []int{13}
}
func (m *PS_SubmitUserAbtestRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PS_SubmitUserAbtestRsp.Unmarshal(m, b)
}
func (m *PS_SubmitUserAbtestRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PS_SubmitUserAbtestRsp.Marshal(b, m, deterministic)
}
func (dst *PS_SubmitUserAbtestRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PS_SubmitUserAbtestRsp.Merge(dst, src)
}
func (m *PS_SubmitUserAbtestRsp) XXX_Size() int {
	return xxx_messageInfo_PS_SubmitUserAbtestRsp.Size(m)
}
func (m *PS_SubmitUserAbtestRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PS_SubmitUserAbtestRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PS_SubmitUserAbtestRsp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*PS_AbtestDomain)(nil), "rcmd.abtestlogic.PS_AbtestDomain")
	proto.RegisterType((*PS_AbtestDomainRoute)(nil), "rcmd.abtestlogic.PS_AbtestDomainRoute")
	proto.RegisterType((*PS_AbtestExptVer)(nil), "rcmd.abtestlogic.PS_AbtestExptVer")
	proto.RegisterType((*PS_AbtestExpt)(nil), "rcmd.abtestlogic.PS_AbtestExpt")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.abtestlogic.PS_AbtestExpt.ExptArgvEntry")
	proto.RegisterType((*PS_AbtestUser)(nil), "rcmd.abtestlogic.PS_AbtestUser")
	proto.RegisterType((*PS_UsersAbtest)(nil), "rcmd.abtestlogic.PS_UsersAbtest")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.abtestlogic.PS_UsersAbtest.AllArgvEntry")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.abtestlogic.PS_UsersAbtest.DefaultArgvEntry")
	proto.RegisterType((*PS_GetUsersAbtestByTagReq)(nil), "rcmd.abtestlogic.PS_GetUsersAbtestByTagReq")
	proto.RegisterType((*PS_GetUsersAbtestByTagRsp)(nil), "rcmd.abtestlogic.PS_GetUsersAbtestByTagRsp")
	proto.RegisterType((*PS_GetUserAllAbtestReq)(nil), "rcmd.abtestlogic.PS_GetUserAllAbtestReq")
	proto.RegisterType((*PS_GetUserAllAbtestRsp)(nil), "rcmd.abtestlogic.PS_GetUserAllAbtestRsp")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.abtestlogic.PS_GetUserAllAbtestRsp.DefaultArgvEntry")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.abtestlogic.PS_GetUserAllAbtestRsp.DeviceidDefaultArgvEntry")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.abtestlogic.PS_GetUserAllAbtestRsp.UidDefaultArgvEntry")
	proto.RegisterType((*PS_GetUserAbtestByArgkeysReq)(nil), "rcmd.abtestlogic.PS_GetUserAbtestByArgkeysReq")
	proto.RegisterType((*PS_GetUserAbtestByArgkeysRsp)(nil), "rcmd.abtestlogic.PS_GetUserAbtestByArgkeysRsp")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.abtestlogic.PS_GetUserAbtestByArgkeysRsp.DefaultArgvEntry")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.abtestlogic.PS_GetUserAbtestByArgkeysRsp.DeviceidDefaultArgvEntry")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.abtestlogic.PS_GetUserAbtestByArgkeysRsp.UidDefaultArgvEntry")
	proto.RegisterType((*PS_SubmitUserAbtestReq)(nil), "rcmd.abtestlogic.PS_SubmitUserAbtestReq")
	proto.RegisterType((*PS_SubmitUserAbtestRsp)(nil), "rcmd.abtestlogic.PS_SubmitUserAbtestRsp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AbtestLogicServiceClient is the client API for AbtestLogicService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AbtestLogicServiceClient interface {
	GetUsersAbtestByTag(ctx context.Context, in *PS_LogicCommonReq, opts ...grpc.CallOption) (*PS_LogicCommonRsp, error)
	GetUserAllAbtest(ctx context.Context, in *PS_LogicCommonReq, opts ...grpc.CallOption) (*PS_LogicCommonRsp, error)
	GetUserAbtestByArgkeys(ctx context.Context, in *PS_LogicCommonReq, opts ...grpc.CallOption) (*PS_LogicCommonRsp, error)
	SubmitUserAbtest(ctx context.Context, in *PS_LogicCommonReq, opts ...grpc.CallOption) (*PS_LogicCommonRsp, error)
}

type abtestLogicServiceClient struct {
	cc *grpc.ClientConn
}

func NewAbtestLogicServiceClient(cc *grpc.ClientConn) AbtestLogicServiceClient {
	return &abtestLogicServiceClient{cc}
}

func (c *abtestLogicServiceClient) GetUsersAbtestByTag(ctx context.Context, in *PS_LogicCommonReq, opts ...grpc.CallOption) (*PS_LogicCommonRsp, error) {
	out := new(PS_LogicCommonRsp)
	err := c.cc.Invoke(ctx, "/rcmd.abtestlogic.AbtestLogicService/GetUsersAbtestByTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *abtestLogicServiceClient) GetUserAllAbtest(ctx context.Context, in *PS_LogicCommonReq, opts ...grpc.CallOption) (*PS_LogicCommonRsp, error) {
	out := new(PS_LogicCommonRsp)
	err := c.cc.Invoke(ctx, "/rcmd.abtestlogic.AbtestLogicService/GetUserAllAbtest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *abtestLogicServiceClient) GetUserAbtestByArgkeys(ctx context.Context, in *PS_LogicCommonReq, opts ...grpc.CallOption) (*PS_LogicCommonRsp, error) {
	out := new(PS_LogicCommonRsp)
	err := c.cc.Invoke(ctx, "/rcmd.abtestlogic.AbtestLogicService/GetUserAbtestByArgkeys", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *abtestLogicServiceClient) SubmitUserAbtest(ctx context.Context, in *PS_LogicCommonReq, opts ...grpc.CallOption) (*PS_LogicCommonRsp, error) {
	out := new(PS_LogicCommonRsp)
	err := c.cc.Invoke(ctx, "/rcmd.abtestlogic.AbtestLogicService/SubmitUserAbtest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AbtestLogicServiceServer is the server API for AbtestLogicService service.
type AbtestLogicServiceServer interface {
	GetUsersAbtestByTag(context.Context, *PS_LogicCommonReq) (*PS_LogicCommonRsp, error)
	GetUserAllAbtest(context.Context, *PS_LogicCommonReq) (*PS_LogicCommonRsp, error)
	GetUserAbtestByArgkeys(context.Context, *PS_LogicCommonReq) (*PS_LogicCommonRsp, error)
	SubmitUserAbtest(context.Context, *PS_LogicCommonReq) (*PS_LogicCommonRsp, error)
}

func RegisterAbtestLogicServiceServer(s *grpc.Server, srv AbtestLogicServiceServer) {
	s.RegisterService(&_AbtestLogicService_serviceDesc, srv)
}

func _AbtestLogicService_GetUsersAbtestByTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PS_LogicCommonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AbtestLogicServiceServer).GetUsersAbtestByTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.abtestlogic.AbtestLogicService/GetUsersAbtestByTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AbtestLogicServiceServer).GetUsersAbtestByTag(ctx, req.(*PS_LogicCommonReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AbtestLogicService_GetUserAllAbtest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PS_LogicCommonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AbtestLogicServiceServer).GetUserAllAbtest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.abtestlogic.AbtestLogicService/GetUserAllAbtest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AbtestLogicServiceServer).GetUserAllAbtest(ctx, req.(*PS_LogicCommonReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AbtestLogicService_GetUserAbtestByArgkeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PS_LogicCommonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AbtestLogicServiceServer).GetUserAbtestByArgkeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.abtestlogic.AbtestLogicService/GetUserAbtestByArgkeys",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AbtestLogicServiceServer).GetUserAbtestByArgkeys(ctx, req.(*PS_LogicCommonReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AbtestLogicService_SubmitUserAbtest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PS_LogicCommonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AbtestLogicServiceServer).SubmitUserAbtest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.abtestlogic.AbtestLogicService/SubmitUserAbtest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AbtestLogicServiceServer).SubmitUserAbtest(ctx, req.(*PS_LogicCommonReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AbtestLogicService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.abtestlogic.AbtestLogicService",
	HandlerType: (*AbtestLogicServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUsersAbtestByTag",
			Handler:    _AbtestLogicService_GetUsersAbtestByTag_Handler,
		},
		{
			MethodName: "GetUserAllAbtest",
			Handler:    _AbtestLogicService_GetUserAllAbtest_Handler,
		},
		{
			MethodName: "GetUserAbtestByArgkeys",
			Handler:    _AbtestLogicService_GetUserAbtestByArgkeys_Handler,
		},
		{
			MethodName: "SubmitUserAbtest",
			Handler:    _AbtestLogicService_SubmitUserAbtest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd/abtestlogic/abtestlogic.proto",
}

func init() {
	proto.RegisterFile("rcmd/abtestlogic/abtestlogic.proto", fileDescriptor_abtestlogic_52fccbbaae41f16e)
}

var fileDescriptor_abtestlogic_52fccbbaae41f16e = []byte{
	// 1070 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x57, 0xd1, 0x6e, 0xe3, 0x44,
	0x14, 0x95, 0x9b, 0x6c, 0x1b, 0xdf, 0x24, 0xdd, 0x68, 0x5a, 0x16, 0x37, 0x65, 0x69, 0xf0, 0x4a,
	0xa8, 0x12, 0x22, 0x15, 0x59, 0x90, 0x60, 0xe9, 0x02, 0x29, 0xdb, 0x2d, 0x81, 0xb2, 0xaa, 0x9c,
	0x6c, 0x91, 0xd0, 0x4a, 0x23, 0x27, 0x33, 0x6b, 0x59, 0x75, 0x6c, 0xd7, 0x33, 0x8e, 0x36, 0xe2,
	0x3b, 0x78, 0x44, 0x7c, 0x0e, 0x1f, 0xc0, 0x03, 0x6f, 0x7c, 0x06, 0xcf, 0x68, 0x66, 0x9c, 0xc4,
	0x71, 0xdc, 0xa4, 0xd1, 0xf6, 0x6d, 0x5f, 0x2a, 0xcf, 0xf5, 0xbd, 0x67, 0xe6, 0x9e, 0x73, 0xee,
	0x34, 0x06, 0x33, 0x1a, 0x0c, 0xc9, 0x91, 0xdd, 0xe7, 0x94, 0x71, 0x2f, 0x70, 0xdc, 0x41, 0xfa,
	0xb9, 0x19, 0x46, 0x01, 0x0f, 0x50, 0x4d, 0xe4, 0x34, 0x53, 0xf1, 0xfa, 0xc3, 0x85, 0xaa, 0x41,
	0x30, 0x1c, 0x06, 0xbe, 0x2a, 0x30, 0xff, 0xd0, 0xe0, 0xfe, 0x45, 0x17, 0xb7, 0xe5, 0xfb, 0x67,
	0xc1, 0xd0, 0x76, 0x7d, 0x54, 0x83, 0x02, 0x71, 0x89, 0xa1, 0x35, 0xb4, 0xc3, 0xaa, 0x25, 0x1e,
	0xd1, 0x87, 0x50, 0x8e, 0x43, 0xec, 0xd9, 0x63, 0x1a, 0x61, 0x97, 0x18, 0x1b, 0xf2, 0x8d, 0x1e,
	0x87, 0xe7, 0x22, 0xd2, 0x21, 0x68, 0x0f, 0x4a, 0x1e, 0xc1, 0x91, 0xcd, 0xdd, 0xc0, 0x28, 0xc8,
	0x97, 0x5b, 0x1e, 0xb1, 0xc4, 0x12, 0xed, 0x83, 0x2e, 0xe3, 0x78, 0x44, 0x23, 0xa3, 0x28, 0xdf,
	0x95, 0x64, 0xe0, 0x92, 0x46, 0xa8, 0x01, 0x95, 0x29, 0x2e, 0xb7, 0x1d, 0xe3, 0x5e, 0x43, 0x3b,
	0xd4, 0x2d, 0x48, 0x80, 0x7b, 0xb6, 0x63, 0x62, 0xd8, 0xcd, 0x1c, 0xcf, 0x0a, 0x62, 0x4e, 0xd1,
	0x19, 0x6c, 0xc7, 0x21, 0x26, 0x32, 0x82, 0x3d, 0x97, 0x71, 0x43, 0x6b, 0x14, 0x0e, 0xcb, 0xad,
	0x8f, 0x9a, 0x59, 0x06, 0x9a, 0xd9, 0xfa, 0x4a, 0x1c, 0xaa, 0xa7, 0x73, 0x97, 0x71, 0xf3, 0xaf,
	0x02, 0xd4, 0xa6, 0x19, 0xa7, 0x6f, 0x42, 0x2e, 0xce, 0xb5, 0x07, 0x25, 0xfa, 0x26, 0xe4, 0x23,
	0x3c, 0xa5, 0x61, 0x4b, 0xae, 0x3b, 0x04, 0x1d, 0x40, 0x59, 0xbd, 0x52, 0xdd, 0x2a, 0x2a, 0x40,
	0x86, 0x54, 0xc3, 0xef, 0x83, 0xcc, 0x15, 0xa5, 0x8a, 0x8a, 0x4d, 0xb1, 0xec, 0x10, 0xf4, 0x10,
	0x64, 0x5a, 0x52, 0xa8, 0xa8, 0xd0, 0x45, 0x44, 0xd5, 0x25, 0xc0, 0x98, 0x71, 0x9b, 0xc7, 0x4c,
	0x52, 0x91, 0x00, 0x77, 0x65, 0x44, 0x92, 0x3c, 0x51, 0x60, 0x33, 0x21, 0x39, 0xe1, 0x7f, 0x02,
	0xdd, 0xa7, 0x8e, 0xeb, 0x1b, 0x5b, 0x33, 0xe8, 0x13, 0x11, 0x98, 0xb4, 0x83, 0xa9, 0x4f, 0x8c,
	0xd2, 0xac, 0x9d, 0x53, 0x9f, 0xcc, 0xcb, 0xa3, 0x67, 0xe4, 0xd9, 0x07, 0x7d, 0xa6, 0x0d, 0x48,
	0x6d, 0xd4, 0x11, 0x7a, 0xb6, 0x83, 0x3a, 0x50, 0x49, 0xe8, 0x8f, 0x84, 0x22, 0x46, 0xb9, 0xa1,
	0x1d, 0x96, 0x5b, 0x1f, 0xaf, 0xe6, 0x5f, 0x64, 0x5b, 0x65, 0x92, 0x12, 0xf3, 0x00, 0xca, 0x03,
	0xcf, 0xa5, 0x3e, 0xc7, 0x7c, 0x1c, 0x52, 0xa3, 0xa2, 0x5a, 0x57, 0xa1, 0xde, 0x38, 0xa4, 0xe8,
	0x11, 0x54, 0x39, 0x8d, 0x86, 0xae, 0x6f, 0x7b, 0x2a, 0xa5, 0x2a, 0x53, 0x2a, 0x93, 0xa0, 0x48,
	0x32, 0xff, 0xd1, 0xa0, 0x3a, 0xa7, 0x24, 0xfa, 0x11, 0x24, 0x09, 0xd8, 0x8e, 0x9c, 0x51, 0xe2,
	0x8f, 0x4f, 0x97, 0x9c, 0x4f, 0xd4, 0x34, 0xc5, 0x9f, 0x76, 0xe4, 0x8c, 0x4e, 0x7d, 0x1e, 0x8d,
	0x2d, 0xc9, 0x9b, 0x58, 0xa2, 0xa7, 0x09, 0x87, 0x82, 0xa7, 0x0d, 0xd9, 0xaa, 0xb9, 0x02, 0xea,
	0x92, 0x46, 0x8a, 0xe7, 0x4b, 0x1a, 0xd5, 0xbf, 0x86, 0xea, 0x1c, 0xb2, 0x18, 0xb2, 0x2b, 0x3a,
	0x96, 0xee, 0xd2, 0x2d, 0xf1, 0x88, 0x76, 0xe1, 0xde, 0xc8, 0xf6, 0x62, 0x2a, 0xe1, 0x75, 0x4b,
	0x2d, 0x9e, 0x6c, 0x7c, 0xa9, 0x99, 0x3f, 0xa7, 0x1a, 0x7b, 0xc9, 0x68, 0x94, 0x25, 0x4c, 0x5b,
	0x20, 0x6c, 0x1f, 0xf4, 0x24, 0x21, 0x19, 0x57, 0xdd, 0x2a, 0xa9, 0x40, 0x87, 0x98, 0x7f, 0x16,
	0x60, 0xfb, 0xa2, 0x8b, 0x05, 0x12, 0x53, 0xa0, 0xe8, 0x31, 0x14, 0x63, 0x46, 0x23, 0x89, 0x54,
	0x6e, 0x1d, 0x2c, 0xe9, 0x4c, 0x54, 0x59, 0x32, 0x19, 0x1d, 0x27, 0xf4, 0xca, 0xf1, 0xdb, 0x90,
	0xf4, 0x1e, 0xac, 0xe0, 0x44, 0x11, 0x2a, 0x06, 0x0f, 0xf5, 0xa0, 0x42, 0xe8, 0x6b, 0x3b, 0xf6,
	0x12, 0x7d, 0x0a, 0x12, 0xe0, 0xb3, 0x5c, 0x80, 0xd4, 0x51, 0x9b, 0xcf, 0x54, 0xd1, 0x4c, 0xa3,
	0x32, 0x99, 0x45, 0xd0, 0x0f, 0x50, 0xb2, 0x3d, 0x4f, 0x21, 0x16, 0x97, 0x28, 0x9e, 0x46, 0x6c,
	0x7b, 0xde, 0x0c, 0x6d, 0xcb, 0x56, 0xab, 0xfa, 0x37, 0x50, 0xcb, 0x6e, 0xb5, 0x8e, 0x68, 0xf5,
	0x27, 0x50, 0x49, 0x03, 0xaf, 0x25, 0xf8, 0xdf, 0x1a, 0xec, 0x5d, 0x74, 0xf1, 0x19, 0xe5, 0xa9,
	0x73, 0x9e, 0x8c, 0x7b, 0xb6, 0x63, 0xd1, 0xeb, 0xf9, 0xb1, 0xd4, 0x32, 0x63, 0x79, 0x0c, 0xba,
	0x10, 0xe7, 0xb6, 0xa2, 0x48, 0x39, 0x4b, 0xa2, 0x42, 0x8a, 0xd2, 0x84, 0x5d, 0x97, 0x61, 0x3f,
	0xe0, 0xd8, 0xa7, 0x94, 0xe0, 0xa9, 0xe3, 0xc5, 0x4d, 0x56, 0xb2, 0x6a, 0x2e, 0x7b, 0x11, 0xf0,
	0x17, 0x94, 0x92, 0xc9, 0x45, 0xf9, 0x09, 0xa0, 0x74, 0x3e, 0x8b, 0xfb, 0x43, 0x97, 0xcb, 0xbb,
	0xad, 0x64, 0xdd, 0x9f, 0x66, 0x77, 0x65, 0xd8, 0x1c, 0xdc, 0xd8, 0x14, 0x0b, 0xd1, 0x73, 0xd8,
	0x96, 0xe7, 0x16, 0xc1, 0xf4, 0x85, 0xde, 0x58, 0x25, 0x9f, 0x55, 0x11, 0x75, 0x3d, 0xca, 0xa4,
	0xad, 0xcc, 0xa7, 0xf0, 0x60, 0xb6, 0x89, 0x10, 0x40, 0x25, 0xd1, 0xeb, 0xc5, 0x4b, 0x44, 0xcb,
	0xb9, 0x44, 0xfe, 0x2d, 0xe6, 0xd7, 0xb3, 0x70, 0xde, 0xee, 0xda, 0xba, 0x76, 0x7f, 0x95, 0xb1,
	0xbb, 0x92, 0xe6, 0xab, 0x5c, 0x80, 0x9c, 0xdd, 0x57, 0xd8, 0xfe, 0x35, 0xd4, 0x62, 0x97, 0xe0,
	0x9c, 0x81, 0x3a, 0xbe, 0xf5, 0x0e, 0x2f, 0x5d, 0xb2, 0xb0, 0xc9, 0x76, 0x3c, 0x17, 0x44, 0x31,
	0xbc, 0x47, 0xe8, 0xc8, 0x1d, 0xd0, 0xec, 0x66, 0x6a, 0xd6, 0xda, 0x6b, 0xb4, 0xa3, 0x50, 0x16,
	0x76, 0xdc, 0x21, 0x8b, 0x6f, 0xde, 0x7a, 0x16, 0xdb, 0xb0, 0x93, 0xd3, 0xdd, 0x5a, 0x10, 0xcf,
	0xc1, 0xb8, 0xe9, 0xcc, 0x6b, 0x8d, 0xf6, 0x77, 0xf0, 0x41, 0x8a, 0x92, 0x64, 0x06, 0xda, 0x91,
	0x73, 0x45, 0xc7, 0x4c, 0xb8, 0xb4, 0x01, 0x15, 0x3b, 0x72, 0xf0, 0x15, 0x1d, 0xcf, 0x8c, 0xa6,
	0x5b, 0x60, 0x47, 0xce, 0x4f, 0x74, 0x2c, 0x1d, 0xfe, 0x5f, 0x71, 0x19, 0xc4, 0x5b, 0x1b, 0xb5,
	0x9f, 0x6b, 0xd4, 0x6f, 0x97, 0x2a, 0xbb, 0x70, 0x86, 0x15, 0x76, 0xf5, 0x6e, 0xb4, 0xeb, 0xc9,
	0x9a, 0xfb, 0xdc, 0xc6, 0xb4, 0xbf, 0x2d, 0x37, 0xed, 0xd9, 0xda, 0xad, 0xbd, 0xa3, 0xd6, 0xfd,
	0x45, 0x5e, 0x8d, 0xea, 0x32, 0x9f, 0x71, 0x23, 0x4c, 0x9b, 0xfe, 0x71, 0xa4, 0xad, 0xfd, 0xe3,
	0xc8, 0x34, 0xf2, 0x81, 0x59, 0xd8, 0xfa, 0xbd, 0x00, 0x48, 0xad, 0xce, 0x05, 0x46, 0x97, 0x46,
	0xa2, 0x0f, 0x84, 0x61, 0x27, 0xe7, 0xdf, 0x08, 0x7a, 0x94, 0xbb, 0xa9, 0xac, 0xfc, 0x5e, 0x7e,
	0xf4, 0x58, 0xf4, 0xba, 0xbe, 0x3a, 0x89, 0x85, 0xe8, 0x15, 0xd4, 0xb2, 0xb7, 0xd6, 0x1d, 0xa2,
	0xf7, 0xe1, 0x41, 0xbe, 0xbd, 0xee, 0xb6, 0x83, 0x2c, 0xa1, 0x77, 0x87, 0x7e, 0xf2, 0xf9, 0xaf,
	0x2d, 0x27, 0xf0, 0x6c, 0xdf, 0x69, 0x7e, 0xd1, 0xe2, 0xbc, 0x39, 0x08, 0x86, 0x47, 0xf2, 0x7b,
	0x72, 0x10, 0x78, 0x47, 0x4c, 0x89, 0xc4, 0x8e, 0xb2, 0x5f, 0x9e, 0xfd, 0x4d, 0x99, 0xf3, 0xf8,
	0xff, 0x00, 0x00, 0x00, 0xff, 0xff, 0x2e, 0x29, 0xf5, 0x6f, 0xca, 0x0e, 0x00, 0x00,
}
