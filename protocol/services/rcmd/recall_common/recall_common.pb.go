// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/recall/recall_common.proto

package recall_common // import "golang.52tt.com/protocol/services/rcmd/recall_common"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type App int32

const (
	App_Invalid_App App = 0
	App_Tt          App = 1
	App_YinPai      App = 2
)

var App_name = map[int32]string{
	0: "Invalid_App",
	1: "Tt",
	2: "YinPai",
}
var App_value = map[string]int32{
	"Invalid_App": 0,
	"Tt":          1,
	"YinPai":      2,
}

func (x App) String() string {
	return proto.EnumName(App_name, int32(x))
}
func (App) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{0}
}

type Item int32

const (
	Item_Invalid_Item Item = 0
	Item_User         Item = 1
	Item_Channel      Item = 2
	Item_Post         Item = 3
	Item_Slog         Item = 4
)

var Item_name = map[int32]string{
	0: "Invalid_Item",
	1: "User",
	2: "Channel",
	3: "Post",
	4: "Slog",
}
var Item_value = map[string]int32{
	"Invalid_Item": 0,
	"User":         1,
	"Channel":      2,
	"Post":         3,
	"Slog":         4,
}

func (x Item) String() string {
	return proto.EnumName(Item_name, int32(x))
}
func (Item) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{1}
}

type Key int32

const (
	Key_Invalid_Key                 Key = 0
	Key_ActiveUser                  Key = 1
	Key_ActiveChannel               Key = 2
	Key_TabChannel                  Key = 3
	Key_TypeChannel                 Key = 4
	Key_PlaymateDeliver             Key = 5
	Key_SayHiFrequently             Key = 6
	Key_PlayerSetting               Key = 7
	Key_Black                       Key = 8
	Key_OnMic                       Key = 9
	Key_PlaymateDeliverTooMany      Key = 10
	Key_LowQualityChannel           Key = 11
	Key_AgeGroup                    Key = 12
	Key_RadarGame                   Key = 13
	Key_RadarDeliver                Key = 14
	Key_RadarDating                 Key = 15
	Key_InGame                      Key = 16
	Key_Sex                         Key = 17
	Key_Online                      Key = 18
	Key_ChannelSex                  Key = 19
	Key_ChannelDeliver              Key = 20
	Key_RadarDatingList             Key = 21
	Key_ChannelWithRiskyTitle       Key = 22
	Key_LocationCity                Key = 23
	Key_LocationProvince            Key = 24
	Key_ChatCardPublish             Key = 25
	Key_ChatCardDeliver             Key = 26
	Key_ChatCardWhite               Key = 27
	Key_ChatCardRealTime            Key = 28
	Key_TopicChannelRelationship    Key = 30
	Key_PostOfflineAlgoRanking10Uid Key = 31
	Key_PostOfflineAlgoRanking11    Key = 32
	// 1xxxx，声洞
	// 圈子的召回
	Key_CircleOnlineUserCf        Key = 10029
	Key_CircleOnlineItemCf        Key = 10030
	Key_CircleOnlineAls           Key = 10031
	Key_CircleOnlineRelation      Key = 10032
	Key_CircleOnlinePreferContent Key = 10033
)

var Key_name = map[int32]string{
	0:     "Invalid_Key",
	1:     "ActiveUser",
	2:     "ActiveChannel",
	3:     "TabChannel",
	4:     "TypeChannel",
	5:     "PlaymateDeliver",
	6:     "SayHiFrequently",
	7:     "PlayerSetting",
	8:     "Black",
	9:     "OnMic",
	10:    "PlaymateDeliverTooMany",
	11:    "LowQualityChannel",
	12:    "AgeGroup",
	13:    "RadarGame",
	14:    "RadarDeliver",
	15:    "RadarDating",
	16:    "InGame",
	17:    "Sex",
	18:    "Online",
	19:    "ChannelSex",
	20:    "ChannelDeliver",
	21:    "RadarDatingList",
	22:    "ChannelWithRiskyTitle",
	23:    "LocationCity",
	24:    "LocationProvince",
	25:    "ChatCardPublish",
	26:    "ChatCardDeliver",
	27:    "ChatCardWhite",
	28:    "ChatCardRealTime",
	30:    "TopicChannelRelationship",
	31:    "PostOfflineAlgoRanking10Uid",
	32:    "PostOfflineAlgoRanking11",
	10029: "CircleOnlineUserCf",
	10030: "CircleOnlineItemCf",
	10031: "CircleOnlineAls",
	10032: "CircleOnlineRelation",
	10033: "CircleOnlinePreferContent",
}
var Key_value = map[string]int32{
	"Invalid_Key":                 0,
	"ActiveUser":                  1,
	"ActiveChannel":               2,
	"TabChannel":                  3,
	"TypeChannel":                 4,
	"PlaymateDeliver":             5,
	"SayHiFrequently":             6,
	"PlayerSetting":               7,
	"Black":                       8,
	"OnMic":                       9,
	"PlaymateDeliverTooMany":      10,
	"LowQualityChannel":           11,
	"AgeGroup":                    12,
	"RadarGame":                   13,
	"RadarDeliver":                14,
	"RadarDating":                 15,
	"InGame":                      16,
	"Sex":                         17,
	"Online":                      18,
	"ChannelSex":                  19,
	"ChannelDeliver":              20,
	"RadarDatingList":             21,
	"ChannelWithRiskyTitle":       22,
	"LocationCity":                23,
	"LocationProvince":            24,
	"ChatCardPublish":             25,
	"ChatCardDeliver":             26,
	"ChatCardWhite":               27,
	"ChatCardRealTime":            28,
	"TopicChannelRelationship":    30,
	"PostOfflineAlgoRanking10Uid": 31,
	"PostOfflineAlgoRanking11":    32,
	"CircleOnlineUserCf":          10029,
	"CircleOnlineItemCf":          10030,
	"CircleOnlineAls":             10031,
	"CircleOnlineRelation":        10032,
	"CircleOnlinePreferContent":   10033,
}

func (x Key) String() string {
	return proto.EnumName(Key_name, int32(x))
}
func (Key) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{2}
}

type IndexOp int32

const (
	IndexOp_Invalid_Op      IndexOp = 0
	IndexOp_Enter           IndexOp = 1
	IndexOp_Exit            IndexOp = 2
	IndexOp_Broadcast_Enter IndexOp = 3
	IndexOp_Broadcast_Exit  IndexOp = 4
	IndexOp_Del             IndexOp = 5
	IndexOp_Cover           IndexOp = 6
)

var IndexOp_name = map[int32]string{
	0: "Invalid_Op",
	1: "Enter",
	2: "Exit",
	3: "Broadcast_Enter",
	4: "Broadcast_Exit",
	5: "Del",
	6: "Cover",
}
var IndexOp_value = map[string]int32{
	"Invalid_Op":      0,
	"Enter":           1,
	"Exit":            2,
	"Broadcast_Enter": 3,
	"Broadcast_Exit":  4,
	"Del":             5,
	"Cover":           6,
}

func (x IndexOp) String() string {
	return proto.EnumName(IndexOp_name, int32(x))
}
func (IndexOp) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{3}
}

type Scene int32

const (
	Scene_Invalid_Scene   Scene = 0
	Scene_Playmate        Scene = 1
	Scene_TopicChannel    Scene = 2
	Scene_RecommendStream Scene = 3
)

var Scene_name = map[int32]string{
	0: "Invalid_Scene",
	1: "Playmate",
	2: "TopicChannel",
	3: "RecommendStream",
}
var Scene_value = map[string]int32{
	"Invalid_Scene":   0,
	"Playmate":        1,
	"TopicChannel":    2,
	"RecommendStream": 3,
}

func (x Scene) String() string {
	return proto.EnumName(Scene_name, int32(x))
}
func (Scene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{4}
}

type Queue int32

const (
	Queue_Invalid_Queue        Queue = 0
	Queue_ActiveUserOld        Queue = 1
	Queue_PreferenceChannelOld Queue = 2
)

var Queue_name = map[int32]string{
	0: "Invalid_Queue",
	1: "ActiveUserOld",
	2: "PreferenceChannelOld",
}
var Queue_value = map[string]int32{
	"Invalid_Queue":        0,
	"ActiveUserOld":        1,
	"PreferenceChannelOld": 2,
}

func (x Queue) String() string {
	return proto.EnumName(Queue_name, int32(x))
}
func (Queue) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{5}
}

type FilterName int32

const (
	FilterName_Invalid_Filter     FilterName = 0
	FilterName_DeliverOld         FilterName = 1
	FilterName_SayHiFrequentlyOld FilterName = 2
	FilterName_PlayerSettingOld   FilterName = 3
	FilterName_BlackOld           FilterName = 4
	FilterName_OnMicOld           FilterName = 5
	FilterName_DelverTooManyOld   FilterName = 6
)

var FilterName_name = map[int32]string{
	0: "Invalid_Filter",
	1: "DeliverOld",
	2: "SayHiFrequentlyOld",
	3: "PlayerSettingOld",
	4: "BlackOld",
	5: "OnMicOld",
	6: "DelverTooManyOld",
}
var FilterName_value = map[string]int32{
	"Invalid_Filter":     0,
	"DeliverOld":         1,
	"SayHiFrequentlyOld": 2,
	"PlayerSettingOld":   3,
	"BlackOld":           4,
	"OnMicOld":           5,
	"DelverTooManyOld":   6,
}

func (x FilterName) String() string {
	return proto.EnumName(FilterName_name, int32(x))
}
func (FilterName) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{6}
}

type BloomLikeType int32

const (
	BloomLikeType_UnknownBloomLike    BloomLikeType = 0
	BloomLikeType_CuckooFilter        BloomLikeType = 1
	BloomLikeType_RollingCuckooFilter BloomLikeType = 2
)

var BloomLikeType_name = map[int32]string{
	0: "UnknownBloomLike",
	1: "CuckooFilter",
	2: "RollingCuckooFilter",
}
var BloomLikeType_value = map[string]int32{
	"UnknownBloomLike":    0,
	"CuckooFilter":        1,
	"RollingCuckooFilter": 2,
}

func (x BloomLikeType) String() string {
	return proto.EnumName(BloomLikeType_name, int32(x))
}
func (BloomLikeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{7}
}

// 按时间滚动的cuckoo filter存储在redis内的格式
type RollingCuckooFilterData struct {
	Segments             []*RollingCuckooFilterSegment `protobuf:"bytes,1,rep,name=segments,proto3" json:"segments,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *RollingCuckooFilterData) Reset()         { *m = RollingCuckooFilterData{} }
func (m *RollingCuckooFilterData) String() string { return proto.CompactTextString(m) }
func (*RollingCuckooFilterData) ProtoMessage()    {}
func (*RollingCuckooFilterData) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{0}
}
func (m *RollingCuckooFilterData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RollingCuckooFilterData.Unmarshal(m, b)
}
func (m *RollingCuckooFilterData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RollingCuckooFilterData.Marshal(b, m, deterministic)
}
func (dst *RollingCuckooFilterData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollingCuckooFilterData.Merge(dst, src)
}
func (m *RollingCuckooFilterData) XXX_Size() int {
	return xxx_messageInfo_RollingCuckooFilterData.Size(m)
}
func (m *RollingCuckooFilterData) XXX_DiscardUnknown() {
	xxx_messageInfo_RollingCuckooFilterData.DiscardUnknown(m)
}

var xxx_messageInfo_RollingCuckooFilterData proto.InternalMessageInfo

func (m *RollingCuckooFilterData) GetSegments() []*RollingCuckooFilterSegment {
	if m != nil {
		return m.Segments
	}
	return nil
}

type RollingCuckooFilterSegment struct {
	Ctime                uint32   `protobuf:"varint,1,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Mtime                uint32   `protobuf:"varint,2,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	Data                 []byte   `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RollingCuckooFilterSegment) Reset()         { *m = RollingCuckooFilterSegment{} }
func (m *RollingCuckooFilterSegment) String() string { return proto.CompactTextString(m) }
func (*RollingCuckooFilterSegment) ProtoMessage()    {}
func (*RollingCuckooFilterSegment) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{1}
}
func (m *RollingCuckooFilterSegment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RollingCuckooFilterSegment.Unmarshal(m, b)
}
func (m *RollingCuckooFilterSegment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RollingCuckooFilterSegment.Marshal(b, m, deterministic)
}
func (dst *RollingCuckooFilterSegment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollingCuckooFilterSegment.Merge(dst, src)
}
func (m *RollingCuckooFilterSegment) XXX_Size() int {
	return xxx_messageInfo_RollingCuckooFilterSegment.Size(m)
}
func (m *RollingCuckooFilterSegment) XXX_DiscardUnknown() {
	xxx_messageInfo_RollingCuckooFilterSegment.DiscardUnknown(m)
}

var xxx_messageInfo_RollingCuckooFilterSegment proto.InternalMessageInfo

func (m *RollingCuckooFilterSegment) GetCtime() uint32 {
	if m != nil {
		return m.Ctime
	}
	return 0
}

func (m *RollingCuckooFilterSegment) GetMtime() uint32 {
	if m != nil {
		return m.Mtime
	}
	return 0
}

func (m *RollingCuckooFilterSegment) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *RollingCuckooFilterSegment) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type BloomLikeDesc struct {
	Type                 BloomLikeType `protobuf:"varint,1,opt,name=type,proto3,enum=rcmd.recall_common.BloomLikeType" json:"type,omitempty"`
	Namespace            string        `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Key                  string        `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BloomLikeDesc) Reset()         { *m = BloomLikeDesc{} }
func (m *BloomLikeDesc) String() string { return proto.CompactTextString(m) }
func (*BloomLikeDesc) ProtoMessage()    {}
func (*BloomLikeDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{2}
}
func (m *BloomLikeDesc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BloomLikeDesc.Unmarshal(m, b)
}
func (m *BloomLikeDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BloomLikeDesc.Marshal(b, m, deterministic)
}
func (dst *BloomLikeDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BloomLikeDesc.Merge(dst, src)
}
func (m *BloomLikeDesc) XXX_Size() int {
	return xxx_messageInfo_BloomLikeDesc.Size(m)
}
func (m *BloomLikeDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_BloomLikeDesc.DiscardUnknown(m)
}

var xxx_messageInfo_BloomLikeDesc proto.InternalMessageInfo

func (m *BloomLikeDesc) GetType() BloomLikeType {
	if m != nil {
		return m.Type
	}
	return BloomLikeType_UnknownBloomLike
}

func (m *BloomLikeDesc) GetNamespace() string {
	if m != nil {
		return m.Namespace
	}
	return ""
}

func (m *BloomLikeDesc) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type SetDesc struct {
	SetName              string   `protobuf:"bytes,1,opt,name=set_name,json=setName,proto3" json:"set_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDesc) Reset()         { *m = SetDesc{} }
func (m *SetDesc) String() string { return proto.CompactTextString(m) }
func (*SetDesc) ProtoMessage()    {}
func (*SetDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_common_caf4a97117256e14, []int{3}
}
func (m *SetDesc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDesc.Unmarshal(m, b)
}
func (m *SetDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDesc.Marshal(b, m, deterministic)
}
func (dst *SetDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDesc.Merge(dst, src)
}
func (m *SetDesc) XXX_Size() int {
	return xxx_messageInfo_SetDesc.Size(m)
}
func (m *SetDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDesc.DiscardUnknown(m)
}

var xxx_messageInfo_SetDesc proto.InternalMessageInfo

func (m *SetDesc) GetSetName() string {
	if m != nil {
		return m.SetName
	}
	return ""
}

func init() {
	proto.RegisterType((*RollingCuckooFilterData)(nil), "rcmd.recall_common.RollingCuckooFilterData")
	proto.RegisterType((*RollingCuckooFilterSegment)(nil), "rcmd.recall_common.RollingCuckooFilterSegment")
	proto.RegisterType((*BloomLikeDesc)(nil), "rcmd.recall_common.BloomLikeDesc")
	proto.RegisterType((*SetDesc)(nil), "rcmd.recall_common.SetDesc")
	proto.RegisterEnum("rcmd.recall_common.App", App_name, App_value)
	proto.RegisterEnum("rcmd.recall_common.Item", Item_name, Item_value)
	proto.RegisterEnum("rcmd.recall_common.Key", Key_name, Key_value)
	proto.RegisterEnum("rcmd.recall_common.IndexOp", IndexOp_name, IndexOp_value)
	proto.RegisterEnum("rcmd.recall_common.Scene", Scene_name, Scene_value)
	proto.RegisterEnum("rcmd.recall_common.Queue", Queue_name, Queue_value)
	proto.RegisterEnum("rcmd.recall_common.FilterName", FilterName_name, FilterName_value)
	proto.RegisterEnum("rcmd.recall_common.BloomLikeType", BloomLikeType_name, BloomLikeType_value)
}

func init() {
	proto.RegisterFile("rcmd/recall/recall_common.proto", fileDescriptor_recall_common_caf4a97117256e14)
}

var fileDescriptor_recall_common_caf4a97117256e14 = []byte{
	// 1006 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x55, 0xdb, 0x72, 0x13, 0x47,
	0x10, 0xd5, 0x5d, 0x56, 0xfb, 0xd6, 0x0c, 0x02, 0xcb, 0x40, 0x81, 0xe3, 0xca, 0x03, 0xa5, 0x07,
	0x39, 0x90, 0x90, 0x77, 0x59, 0x06, 0xe2, 0x60, 0x24, 0xb1, 0x12, 0x45, 0x25, 0x2f, 0xae, 0x61,
	0xd5, 0x96, 0xa6, 0x34, 0x3b, 0xb3, 0xd9, 0x1d, 0x09, 0xef, 0x5f, 0xe4, 0x47, 0x72, 0xfb, 0xb3,
	0x7c, 0x42, 0xaa, 0x77, 0xb5, 0xb6, 0xec, 0x98, 0xa7, 0xdd, 0x3e, 0xd3, 0x73, 0xba, 0xe7, 0x4c,
	0x77, 0x0f, 0x3c, 0x8b, 0xfc, 0x60, 0x72, 0x14, 0x91, 0x2f, 0xb5, 0x5e, 0x7d, 0xce, 0x7d, 0x1b,
	0x04, 0xd6, 0x74, 0xc2, 0xc8, 0x3a, 0x2b, 0x04, 0x3b, 0x74, 0x6e, 0xac, 0x1c, 0x12, 0xec, 0x79,
	0x56, 0x6b, 0x65, 0xa6, 0xbd, 0x85, 0x3f, 0xb7, 0xf6, 0x8d, 0xd2, 0x8e, 0xa2, 0x13, 0xe9, 0xa4,
	0xf8, 0x19, 0x36, 0x62, 0x9a, 0x06, 0x64, 0x5c, 0xdc, 0x2a, 0x1e, 0x94, 0x9f, 0x6f, 0xbe, 0xec,
	0x74, 0xfe, 0xcf, 0xd0, 0xb9, 0x63, 0xfb, 0x28, 0xdb, 0xe6, 0x5d, 0xed, 0x3f, 0x8c, 0xe0, 0xd1,
	0xd7, 0xfd, 0x44, 0x13, 0xaa, 0xbe, 0x53, 0x01, 0xb5, 0x8a, 0x07, 0xc5, 0xe7, 0xdb, 0x5e, 0x66,
	0x30, 0x1a, 0xa4, 0x68, 0x29, 0x43, 0x83, 0x1c, 0xf5, 0xed, 0xc2, 0xb8, 0x56, 0x79, 0xe5, 0xcb,
	0x86, 0x10, 0x50, 0x99, 0x48, 0x27, 0x5b, 0x95, 0x83, 0xe2, 0xf3, 0x2d, 0x2f, 0xfd, 0x3f, 0x5c,
	0xc2, 0xf6, 0xb1, 0xb6, 0x36, 0x38, 0x53, 0x73, 0x3a, 0xa1, 0xd8, 0x17, 0xaf, 0xa0, 0xe2, 0x92,
	0x30, 0x8b, 0xb2, 0xf3, 0xf2, 0x9b, 0xbb, 0x0e, 0x73, 0xb5, 0x61, 0x9c, 0x84, 0xe4, 0xa5, 0xee,
	0xe2, 0x09, 0x34, 0x8c, 0x0c, 0x28, 0x0e, 0xa5, 0x9f, 0xe5, 0xd2, 0xf0, 0xae, 0x01, 0x81, 0x50,
	0x9e, 0x53, 0x92, 0x66, 0xd3, 0xf0, 0xf8, 0xf7, 0xf0, 0x5b, 0xa8, 0x8f, 0xc8, 0xa5, 0x11, 0xf7,
	0x59, 0x42, 0x77, 0xce, 0xde, 0x69, 0xd4, 0x86, 0x57, 0x8f, 0xc9, 0xf5, 0x65, 0x40, 0xed, 0x36,
	0x94, 0xbb, 0x61, 0x28, 0x76, 0x61, 0xf3, 0xd4, 0x2c, 0xa5, 0x56, 0x93, 0xf3, 0x6e, 0x18, 0x62,
	0x41, 0xd4, 0xa0, 0x34, 0x76, 0x58, 0x14, 0x00, 0xb5, 0x5f, 0x94, 0x19, 0x4a, 0x85, 0xa5, 0x76,
	0x0f, 0x2a, 0xa7, 0x8e, 0x02, 0x81, 0xb0, 0x95, 0x3b, 0xb3, 0x8d, 0x05, 0xb1, 0x01, 0x95, 0x8f,
	0x31, 0x45, 0x58, 0x14, 0x9b, 0x50, 0xef, 0xcd, 0xa4, 0x31, 0xa4, 0xb1, 0xc4, 0xf0, 0xd0, 0xc6,
	0x0e, 0xcb, 0xfc, 0x37, 0xd2, 0x76, 0x8a, 0x95, 0xf6, 0xbf, 0x55, 0x28, 0xbf, 0xa3, 0x64, 0x3d,
	0xe2, 0x3b, 0x4a, 0xb0, 0x20, 0x76, 0x00, 0xba, 0xbe, 0x53, 0x4b, 0x5a, 0x31, 0xdd, 0x83, 0xed,
	0xcc, 0xbe, 0xe6, 0xdb, 0x01, 0x18, 0xcb, 0xcf, 0xb9, 0x5d, 0x66, 0x0e, 0x16, 0x28, 0x07, 0x2a,
	0xe2, 0x3e, 0xec, 0x0e, 0xb5, 0x4c, 0x02, 0xe9, 0xe8, 0x84, 0xb4, 0x5a, 0x52, 0x84, 0x55, 0x06,
	0x47, 0x32, 0xf9, 0x49, 0xbd, 0x89, 0xe8, 0xb7, 0x05, 0x19, 0xa7, 0x13, 0xac, 0x31, 0x3b, 0x7b,
	0xf2, 0xe5, 0x3b, 0xa7, 0xcc, 0x14, 0xeb, 0xa2, 0x01, 0xd5, 0x63, 0x2d, 0xfd, 0x39, 0x6e, 0xf0,
	0xef, 0xc0, 0xbc, 0x57, 0x3e, 0x36, 0xc4, 0x23, 0x78, 0x78, 0x8b, 0x72, 0x6c, 0xed, 0x7b, 0x69,
	0x12, 0x04, 0xf1, 0x00, 0xee, 0x9d, 0xd9, 0x2f, 0x1f, 0x16, 0x52, 0x2b, 0x97, 0xe4, 0x59, 0x6c,
	0x8a, 0x2d, 0xd8, 0xe8, 0x4e, 0xe9, 0x6d, 0x64, 0x17, 0x21, 0x6e, 0x89, 0x6d, 0x68, 0x78, 0x72,
	0x22, 0xa3, 0xb7, 0x32, 0x20, 0xdc, 0x66, 0xf1, 0x52, 0x33, 0xcf, 0x6f, 0x87, 0x4f, 0x91, 0x21,
	0x32, 0x4d, 0x64, 0x97, 0x35, 0x3f, 0x35, 0xa9, 0x3b, 0x8a, 0x3a, 0x94, 0x47, 0x74, 0x89, 0xf7,
	0x18, 0x1c, 0x18, 0xad, 0x0c, 0xa1, 0x60, 0x1d, 0x56, 0xd1, 0x78, 0xed, 0xbe, 0x10, 0xb0, 0xb3,
	0xb2, 0x73, 0xd6, 0x26, 0x9f, 0x7a, 0x8d, 0xf5, 0x4c, 0xc5, 0x0e, 0x1f, 0x88, 0x7d, 0x78, 0xb0,
	0x72, 0xfc, 0xa4, 0xdc, 0xcc, 0x53, 0xf1, 0x3c, 0x19, 0x2b, 0xa7, 0x09, 0x1f, 0x72, 0x5e, 0x67,
	0xd6, 0x97, 0x4e, 0x59, 0xd3, 0x53, 0x2e, 0xc1, 0x3d, 0xd1, 0x04, 0xcc, 0x91, 0x61, 0x64, 0x97,
	0xca, 0xf8, 0x84, 0x2d, 0xe6, 0xed, 0xcd, 0xa4, 0xeb, 0xc9, 0x68, 0x32, 0x5c, 0x7c, 0xd6, 0x2a,
	0x9e, 0xe1, 0xfe, 0x3a, 0x98, 0x67, 0xf0, 0x88, 0x25, 0xce, 0xc1, 0x4f, 0x33, 0xe5, 0x08, 0x1f,
	0x33, 0x65, 0x0e, 0x79, 0x24, 0xf5, 0x58, 0x05, 0x84, 0x4f, 0xc4, 0x13, 0x68, 0x8d, 0x6d, 0xa8,
	0xfc, 0x55, 0x6a, 0x1e, 0xe9, 0x34, 0x68, 0x3c, 0x53, 0x21, 0x3e, 0x15, 0xcf, 0xe0, 0x31, 0x17,
	0xd1, 0xe0, 0xe2, 0x82, 0x4f, 0xdf, 0xd5, 0x53, 0xeb, 0x49, 0x33, 0x57, 0x66, 0xfa, 0xe2, 0xbb,
	0x8f, 0x6a, 0x82, 0xcf, 0x78, 0xfb, 0x57, 0x1c, 0x5e, 0xe0, 0x81, 0xd8, 0x03, 0xd1, 0x53, 0x91,
	0xaf, 0x29, 0x53, 0x8f, 0x8b, 0xab, 0x77, 0x81, 0x7f, 0xf4, 0x6f, 0x2f, 0x70, 0x25, 0xf7, 0x2e,
	0xf0, 0xcf, 0xbe, 0x68, 0xc2, 0xee, 0xfa, 0x42, 0x57, 0xc7, 0xf8, 0x57, 0x5f, 0xec, 0x43, 0x73,
	0x1d, 0xcd, 0x93, 0xc4, 0xbf, 0xfb, 0xe2, 0x29, 0xec, 0xaf, 0x2f, 0x0d, 0x23, 0xba, 0xa0, 0xa8,
	0x67, 0x8d, 0x23, 0xe3, 0xf0, 0x9f, 0x7e, 0x7b, 0x0e, 0xf5, 0x53, 0x33, 0xa1, 0xcb, 0x41, 0xc8,
	0x37, 0x97, 0x57, 0xfd, 0x80, 0xdb, 0xac, 0x01, 0xd5, 0xd7, 0xc6, 0xa5, 0xf5, 0xbe, 0x01, 0x95,
	0xd7, 0x97, 0xca, 0x61, 0x89, 0xd5, 0x3c, 0x8e, 0xac, 0x9c, 0xf8, 0x32, 0x76, 0xe7, 0xd9, 0x72,
	0x99, 0xef, 0x78, 0x0d, 0x64, 0xc7, 0x0a, 0x17, 0xc7, 0x09, 0x69, 0xac, 0x32, 0x4d, 0xcf, 0xb2,
	0xea, 0xb5, 0xf6, 0x00, 0xaa, 0x23, 0x9f, 0x0c, 0xb1, 0xfc, 0x79, 0xa8, 0x14, 0xc0, 0x02, 0x17,
	0x66, 0x5e, 0xcb, 0x58, 0xe4, 0x1b, 0x5f, 0x97, 0x3d, 0x0b, 0xec, 0x11, 0x8f, 0x20, 0x32, 0x93,
	0x91, 0x8b, 0x48, 0x06, 0x58, 0x6e, 0xbf, 0x85, 0xea, 0x87, 0x05, 0x2d, 0x6e, 0x10, 0xa6, 0x00,
	0x16, 0xae, 0x7b, 0x94, 0x65, 0x1d, 0xe8, 0x09, 0x16, 0x45, 0x0b, 0x9a, 0x99, 0x00, 0x64, 0xfc,
	0xbc, 0x33, 0x79, 0xa5, 0xd4, 0xfe, 0xbd, 0x08, 0x90, 0x0d, 0x5c, 0x9e, 0x3c, 0x7c, 0xa0, 0x9c,
	0x2e, 0x43, 0xb3, 0x19, 0xb0, 0xaa, 0x9f, 0x8c, 0xec, 0x21, 0x88, 0x5b, 0xad, 0x9b, 0x52, 0x71,
	0x1d, 0xdd, 0xe8, 0x5e, 0x46, 0xcb, 0x7c, 0xbc, 0xb4, 0x81, 0xd9, 0xaa, 0xb0, 0x95, 0xf6, 0x30,
	0x5b, 0x55, 0xde, 0x71, 0x42, 0xfa, 0xba, 0x7b, 0x19, 0xad, 0xb5, 0x87, 0x6b, 0xb3, 0x99, 0x27,
	0x09, 0xbb, 0x7d, 0x34, 0x73, 0x63, 0xbf, 0x98, 0x2b, 0x1c, 0x0b, 0xac, 0xd4, 0xfa, 0x7b, 0x81,
	0x45, 0xb1, 0x07, 0xf7, 0xef, 0x78, 0x48, 0xb0, 0x74, 0xfc, 0xe3, 0xaf, 0x3f, 0x4c, 0xad, 0x96,
	0x66, 0xda, 0x79, 0xf5, 0xd2, 0xb9, 0x8e, 0x6f, 0x83, 0xa3, 0xf4, 0xd5, 0xf3, 0xad, 0x3e, 0x8a,
	0x29, 0x5a, 0x2a, 0x9f, 0xe2, 0xa3, 0xb5, 0x17, 0x72, 0x35, 0xf1, 0x3f, 0xd7, 0x52, 0xaf, 0xef,
	0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0xd7, 0x42, 0x22, 0xe2, 0x3e, 0x07, 0x00, 0x00,
}
