// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/datacenter/oss_sdk.proto

package datacenter // import "golang.52tt.com/protocol/services/rcmd/datacenter"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type OssStatisBatchEvent struct {
	SrcIp                string          `protobuf:"bytes,1,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	ServerTimestamp      uint32          `protobuf:"varint,2,opt,name=server_timestamp,json=serverTimestamp,proto3" json:"server_timestamp,omitempty"`
	BizType              string          `protobuf:"bytes,3,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	RowList              []*OssStatisRow `protobuf:"bytes,4,rep,name=row_list,json=rowList,proto3" json:"row_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *OssStatisBatchEvent) Reset()         { *m = OssStatisBatchEvent{} }
func (m *OssStatisBatchEvent) String() string { return proto.CompactTextString(m) }
func (*OssStatisBatchEvent) ProtoMessage()    {}
func (*OssStatisBatchEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_oss_sdk_41da623427d70cfb, []int{0}
}
func (m *OssStatisBatchEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OssStatisBatchEvent.Unmarshal(m, b)
}
func (m *OssStatisBatchEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OssStatisBatchEvent.Marshal(b, m, deterministic)
}
func (dst *OssStatisBatchEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OssStatisBatchEvent.Merge(dst, src)
}
func (m *OssStatisBatchEvent) XXX_Size() int {
	return xxx_messageInfo_OssStatisBatchEvent.Size(m)
}
func (m *OssStatisBatchEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_OssStatisBatchEvent.DiscardUnknown(m)
}

var xxx_messageInfo_OssStatisBatchEvent proto.InternalMessageInfo

func (m *OssStatisBatchEvent) GetSrcIp() string {
	if m != nil {
		return m.SrcIp
	}
	return ""
}

func (m *OssStatisBatchEvent) GetServerTimestamp() uint32 {
	if m != nil {
		return m.ServerTimestamp
	}
	return 0
}

func (m *OssStatisBatchEvent) GetBizType() string {
	if m != nil {
		return m.BizType
	}
	return ""
}

func (m *OssStatisBatchEvent) GetRowList() []*OssStatisRow {
	if m != nil {
		return m.RowList
	}
	return nil
}

type OssStatisRow struct {
	Values               []*OssColumn `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OssStatisRow) Reset()         { *m = OssStatisRow{} }
func (m *OssStatisRow) String() string { return proto.CompactTextString(m) }
func (*OssStatisRow) ProtoMessage()    {}
func (*OssStatisRow) Descriptor() ([]byte, []int) {
	return fileDescriptor_oss_sdk_41da623427d70cfb, []int{1}
}
func (m *OssStatisRow) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OssStatisRow.Unmarshal(m, b)
}
func (m *OssStatisRow) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OssStatisRow.Marshal(b, m, deterministic)
}
func (dst *OssStatisRow) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OssStatisRow.Merge(dst, src)
}
func (m *OssStatisRow) XXX_Size() int {
	return xxx_messageInfo_OssStatisRow.Size(m)
}
func (m *OssStatisRow) XXX_DiscardUnknown() {
	xxx_messageInfo_OssStatisRow.DiscardUnknown(m)
}

var xxx_messageInfo_OssStatisRow proto.InternalMessageInfo

func (m *OssStatisRow) GetValues() []*OssColumn {
	if m != nil {
		return m.Values
	}
	return nil
}

type OssColumn struct {
	Idx                  uint32   `protobuf:"varint,1,opt,name=idx,proto3" json:"idx,omitempty"`
	Val                  string   `protobuf:"bytes,2,opt,name=val,proto3" json:"val,omitempty"`
	Key                  string   `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OssColumn) Reset()         { *m = OssColumn{} }
func (m *OssColumn) String() string { return proto.CompactTextString(m) }
func (*OssColumn) ProtoMessage()    {}
func (*OssColumn) Descriptor() ([]byte, []int) {
	return fileDescriptor_oss_sdk_41da623427d70cfb, []int{2}
}
func (m *OssColumn) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OssColumn.Unmarshal(m, b)
}
func (m *OssColumn) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OssColumn.Marshal(b, m, deterministic)
}
func (dst *OssColumn) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OssColumn.Merge(dst, src)
}
func (m *OssColumn) XXX_Size() int {
	return xxx_messageInfo_OssColumn.Size(m)
}
func (m *OssColumn) XXX_DiscardUnknown() {
	xxx_messageInfo_OssColumn.DiscardUnknown(m)
}

var xxx_messageInfo_OssColumn proto.InternalMessageInfo

func (m *OssColumn) GetIdx() uint32 {
	if m != nil {
		return m.Idx
	}
	return 0
}

func (m *OssColumn) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *OssColumn) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func init() {
	proto.RegisterType((*OssStatisBatchEvent)(nil), "datacenter.OssStatisBatchEvent")
	proto.RegisterType((*OssStatisRow)(nil), "datacenter.OssStatisRow")
	proto.RegisterType((*OssColumn)(nil), "datacenter.OssColumn")
}

func init() {
	proto.RegisterFile("rcmd/datacenter/oss_sdk.proto", fileDescriptor_oss_sdk_41da623427d70cfb)
}

var fileDescriptor_oss_sdk_41da623427d70cfb = []byte{
	// 298 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x5c, 0x90, 0xcf, 0x4a, 0x33, 0x31,
	0x14, 0xc5, 0x99, 0xaf, 0x9f, 0xfd, 0x13, 0x2d, 0x96, 0x48, 0x21, 0x2e, 0x84, 0xd2, 0x55, 0x5d,
	0x38, 0x83, 0x2d, 0x2e, 0x5d, 0x58, 0x71, 0x21, 0x08, 0x42, 0xec, 0xca, 0xcd, 0x90, 0x66, 0x42,
	0x0d, 0xcd, 0x4c, 0x42, 0xee, 0xed, 0xd4, 0xf6, 0x85, 0x7c, 0x4d, 0x49, 0xac, 0xad, 0x74, 0x77,
	0xf8, 0xdd, 0xcb, 0xe1, 0x9c, 0x43, 0xae, 0xbc, 0x2c, 0x8b, 0xac, 0x10, 0x28, 0xa4, 0xaa, 0x50,
	0xf9, 0xcc, 0x02, 0xe4, 0x50, 0x2c, 0x53, 0xe7, 0x2d, 0x5a, 0x4a, 0x0e, 0x97, 0xe1, 0x57, 0x42,
	0x2e, 0x5e, 0x01, 0xde, 0x50, 0xa0, 0x86, 0xa9, 0x40, 0xf9, 0xf1, 0x54, 0xab, 0x0a, 0x69, 0x9f,
	0x34, 0xc1, 0xcb, 0x5c, 0x3b, 0x96, 0x0c, 0x92, 0x51, 0x87, 0x9f, 0x80, 0x97, 0xcf, 0x8e, 0x5e,
	0x93, 0x1e, 0x28, 0x5f, 0x2b, 0x9f, 0xa3, 0x2e, 0x15, 0xa0, 0x28, 0x1d, 0xfb, 0x37, 0x48, 0x46,
	0x5d, 0x7e, 0xfe, 0xc3, 0x67, 0xbf, 0x98, 0x5e, 0x92, 0xf6, 0x5c, 0x6f, 0x73, 0xdc, 0x38, 0xc5,
	0x1a, 0xd1, 0xa3, 0x35, 0xd7, 0xdb, 0xd9, 0xc6, 0x29, 0x3a, 0x21, 0x6d, 0x6f, 0xd7, 0xb9, 0xd1,
	0x80, 0xec, 0xff, 0xa0, 0x31, 0x3a, 0x1d, 0xb3, 0xf4, 0x90, 0x29, 0xdd, 0xe7, 0xe1, 0x76, 0xcd,
	0x5b, 0xde, 0xae, 0x5f, 0x34, 0xe0, 0xf0, 0x9e, 0x9c, 0xfd, 0x3d, 0xd0, 0x1b, 0xd2, 0xac, 0x85,
	0x59, 0x29, 0x60, 0x49, 0xb4, 0xe8, 0x1f, 0x59, 0x3c, 0x5a, 0xb3, 0x2a, 0x2b, 0xbe, 0x7b, 0x1a,
	0x3e, 0x90, 0xce, 0x1e, 0xd2, 0x1e, 0x69, 0xe8, 0xe2, 0x33, 0x56, 0xeb, 0xf2, 0x20, 0x03, 0xa9,
	0x85, 0x89, 0x5d, 0x3a, 0x3c, 0xc8, 0x40, 0x96, 0x6a, 0xb3, 0x8b, 0x1e, 0xe4, 0x74, 0xf2, 0x7e,
	0xbb, 0xb0, 0x46, 0x54, 0x8b, 0xf4, 0x6e, 0x8c, 0x98, 0x4a, 0x5b, 0x66, 0x71, 0x50, 0x69, 0x4d,
	0x16, 0xda, 0x6b, 0xa9, 0x20, 0x3b, 0x9a, 0x7e, 0xde, 0x8c, 0x2f, 0x93, 0xef, 0x00, 0x00, 0x00,
	0xff, 0xff, 0x32, 0xe8, 0x27, 0xa0, 0x94, 0x01, 0x00, 0x00,
}
