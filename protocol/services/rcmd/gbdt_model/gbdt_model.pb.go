// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/model/gbdt_model.proto

package gbdt_model // import "golang.52tt.com/protocol/services/rcmd/gbdt_model"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ModelType int32

const (
	ModelType_TypeInvild   ModelType = 0
	ModelType_TypeGbdt     ModelType = 1
	ModelType_TypeLightGbm ModelType = 2
	ModelType_TypeFm       ModelType = 3
)

var ModelType_name = map[int32]string{
	0: "TypeInvild",
	1: "TypeGbdt",
	2: "TypeLightGbm",
	3: "TypeFm",
}
var ModelType_value = map[string]int32{
	"TypeInvild":   0,
	"TypeGbdt":     1,
	"TypeLightGbm": 2,
	"TypeFm":       3,
}

func (x ModelType) String() string {
	return proto.EnumName(ModelType_name, int32(x))
}
func (ModelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_gbdt_model_e709b978479b5b34, []int{0}
}

type PredictReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ModelVersion         string           `protobuf:"bytes,2,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
	InputData            *ModelInputArray `protobuf:"bytes,3,opt,name=input_data,json=inputData,proto3" json:"input_data,omitempty"`
	ModelType            ModelType        `protobuf:"varint,4,opt,name=model_type,json=modelType,proto3,enum=rcmd.gbdt_model.ModelType" json:"model_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PredictReq) Reset()         { *m = PredictReq{} }
func (m *PredictReq) String() string { return proto.CompactTextString(m) }
func (*PredictReq) ProtoMessage()    {}
func (*PredictReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_gbdt_model_e709b978479b5b34, []int{0}
}
func (m *PredictReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PredictReq.Unmarshal(m, b)
}
func (m *PredictReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PredictReq.Marshal(b, m, deterministic)
}
func (dst *PredictReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PredictReq.Merge(dst, src)
}
func (m *PredictReq) XXX_Size() int {
	return xxx_messageInfo_PredictReq.Size(m)
}
func (m *PredictReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PredictReq.DiscardUnknown(m)
}

var xxx_messageInfo_PredictReq proto.InternalMessageInfo

func (m *PredictReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PredictReq) GetModelVersion() string {
	if m != nil {
		return m.ModelVersion
	}
	return ""
}

func (m *PredictReq) GetInputData() *ModelInputArray {
	if m != nil {
		return m.InputData
	}
	return nil
}

func (m *PredictReq) GetModelType() ModelType {
	if m != nil {
		return m.ModelType
	}
	return ModelType_TypeInvild
}

type ModelInputArray struct {
	Data                 []float64 `protobuf:"fixed64,1,rep,packed,name=data,proto3" json:"data,omitempty"`
	Rows                 uint32    `protobuf:"varint,2,opt,name=rows,proto3" json:"rows,omitempty"`
	Cols                 uint32    `protobuf:"varint,3,opt,name=cols,proto3" json:"cols,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ModelInputArray) Reset()         { *m = ModelInputArray{} }
func (m *ModelInputArray) String() string { return proto.CompactTextString(m) }
func (*ModelInputArray) ProtoMessage()    {}
func (*ModelInputArray) Descriptor() ([]byte, []int) {
	return fileDescriptor_gbdt_model_e709b978479b5b34, []int{1}
}
func (m *ModelInputArray) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModelInputArray.Unmarshal(m, b)
}
func (m *ModelInputArray) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModelInputArray.Marshal(b, m, deterministic)
}
func (dst *ModelInputArray) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModelInputArray.Merge(dst, src)
}
func (m *ModelInputArray) XXX_Size() int {
	return xxx_messageInfo_ModelInputArray.Size(m)
}
func (m *ModelInputArray) XXX_DiscardUnknown() {
	xxx_messageInfo_ModelInputArray.DiscardUnknown(m)
}

var xxx_messageInfo_ModelInputArray proto.InternalMessageInfo

func (m *ModelInputArray) GetData() []float64 {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ModelInputArray) GetRows() uint32 {
	if m != nil {
		return m.Rows
	}
	return 0
}

func (m *ModelInputArray) GetCols() uint32 {
	if m != nil {
		return m.Cols
	}
	return 0
}

type PredictRsp struct {
	Predictions          []float64 `protobuf:"fixed64,1,rep,packed,name=predictions,proto3" json:"predictions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *PredictRsp) Reset()         { *m = PredictRsp{} }
func (m *PredictRsp) String() string { return proto.CompactTextString(m) }
func (*PredictRsp) ProtoMessage()    {}
func (*PredictRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_gbdt_model_e709b978479b5b34, []int{2}
}
func (m *PredictRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PredictRsp.Unmarshal(m, b)
}
func (m *PredictRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PredictRsp.Marshal(b, m, deterministic)
}
func (dst *PredictRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PredictRsp.Merge(dst, src)
}
func (m *PredictRsp) XXX_Size() int {
	return xxx_messageInfo_PredictRsp.Size(m)
}
func (m *PredictRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PredictRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PredictRsp proto.InternalMessageInfo

func (m *PredictRsp) GetPredictions() []float64 {
	if m != nil {
		return m.Predictions
	}
	return nil
}

func init() {
	proto.RegisterType((*PredictReq)(nil), "rcmd.gbdt_model.PredictReq")
	proto.RegisterType((*ModelInputArray)(nil), "rcmd.gbdt_model.ModelInputArray")
	proto.RegisterType((*PredictRsp)(nil), "rcmd.gbdt_model.PredictRsp")
	proto.RegisterEnum("rcmd.gbdt_model.ModelType", ModelType_name, ModelType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RcmdGbdtModelClient is the client API for RcmdGbdtModel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RcmdGbdtModelClient interface {
	// 纯调用模型打分
	Predict(ctx context.Context, in *PredictReq, opts ...grpc.CallOption) (*PredictRsp, error)
}

type rcmdGbdtModelClient struct {
	cc *grpc.ClientConn
}

func NewRcmdGbdtModelClient(cc *grpc.ClientConn) RcmdGbdtModelClient {
	return &rcmdGbdtModelClient{cc}
}

func (c *rcmdGbdtModelClient) Predict(ctx context.Context, in *PredictReq, opts ...grpc.CallOption) (*PredictRsp, error) {
	out := new(PredictRsp)
	err := c.cc.Invoke(ctx, "/rcmd.gbdt_model.RcmdGbdtModel/Predict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RcmdGbdtModelServer is the server API for RcmdGbdtModel service.
type RcmdGbdtModelServer interface {
	// 纯调用模型打分
	Predict(context.Context, *PredictReq) (*PredictRsp, error)
}

func RegisterRcmdGbdtModelServer(s *grpc.Server, srv RcmdGbdtModelServer) {
	s.RegisterService(&_RcmdGbdtModel_serviceDesc, srv)
}

func _RcmdGbdtModel_Predict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PredictReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdGbdtModelServer).Predict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.gbdt_model.RcmdGbdtModel/Predict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdGbdtModelServer).Predict(ctx, req.(*PredictReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RcmdGbdtModel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.gbdt_model.RcmdGbdtModel",
	HandlerType: (*RcmdGbdtModelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Predict",
			Handler:    _RcmdGbdtModel_Predict_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd/model/gbdt_model.proto",
}

func init() {
	proto.RegisterFile("rcmd/model/gbdt_model.proto", fileDescriptor_gbdt_model_e709b978479b5b34)
}

var fileDescriptor_gbdt_model_e709b978479b5b34 = []byte{
	// 362 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x52, 0xc1, 0x6a, 0xe3, 0x30,
	0x10, 0x5d, 0xc5, 0x21, 0xbb, 0x9e, 0xc4, 0x89, 0xd1, 0xc9, 0x24, 0x17, 0x93, 0xbd, 0x98, 0x3d,
	0xd8, 0xac, 0xc3, 0x1e, 0xf6, 0xb4, 0x6c, 0x5b, 0x1a, 0x02, 0x0d, 0x14, 0x11, 0x7a, 0xe8, 0x25,
	0xd8, 0x96, 0x70, 0x05, 0xb6, 0xe5, 0x4a, 0x4a, 0x4a, 0xbe, 0xae, 0xbf, 0x56, 0xa4, 0xa4, 0x75,
	0x49, 0xdb, 0x8b, 0xfd, 0xe6, 0xe9, 0xe9, 0xcd, 0x63, 0x34, 0x30, 0x93, 0x45, 0x4d, 0x93, 0x5a,
	0x50, 0x56, 0x25, 0x65, 0x4e, 0xf5, 0xd6, 0xc2, 0xb8, 0x95, 0x42, 0x0b, 0x3c, 0x31, 0x87, 0x71,
	0x47, 0xcf, 0x9f, 0x11, 0xc0, 0xad, 0x64, 0x94, 0x17, 0x9a, 0xb0, 0x47, 0xec, 0x83, 0xb3, 0xe3,
	0x34, 0x40, 0x21, 0x8a, 0x3c, 0x62, 0x20, 0xfe, 0x09, 0x9e, 0x55, 0x6e, 0xf7, 0x4c, 0x2a, 0x2e,
	0x9a, 0xa0, 0x17, 0xa2, 0xc8, 0x25, 0x23, 0x4b, 0xde, 0x1d, 0x39, 0xfc, 0x0f, 0x80, 0x37, 0xed,
	0x4e, 0x6f, 0x69, 0xa6, 0xb3, 0xc0, 0x09, 0x51, 0x34, 0x4c, 0xc3, 0xf8, 0xac, 0x57, 0xbc, 0x36,
	0xdf, 0x95, 0xd1, 0xfd, 0x97, 0x32, 0x3b, 0x10, 0xd7, 0xde, 0xb9, 0xca, 0x74, 0x86, 0xff, 0x02,
	0x1c, 0xbb, 0xe8, 0x43, 0xcb, 0x82, 0x7e, 0x88, 0xa2, 0x71, 0x3a, 0xfd, 0xdc, 0x60, 0x73, 0x68,
	0x19, 0x71, 0xeb, 0x57, 0x38, 0x5f, 0xc3, 0xe4, 0xcc, 0x18, 0x63, 0xe8, 0xdb, 0x20, 0x28, 0x74,
	0x22, 0x44, 0x2c, 0x36, 0x9c, 0x14, 0x4f, 0xca, 0xc6, 0xf7, 0x88, 0xc5, 0x86, 0x2b, 0x44, 0xa5,
	0x6c, 0x60, 0x8f, 0x58, 0x3c, 0x8f, 0xbb, 0x79, 0xa8, 0x16, 0x87, 0x30, 0x6c, 0x8f, 0x15, 0x17,
	0x8d, 0x3a, 0x19, 0xbe, 0xa7, 0x7e, 0x2d, 0xc1, 0x7d, 0x8b, 0x85, 0xc7, 0x00, 0xe6, 0xbf, 0x6a,
	0xf6, 0xbc, 0xa2, 0xfe, 0x37, 0x3c, 0x82, 0x1f, 0xa6, 0x5e, 0xe6, 0x54, 0xfb, 0x08, 0xfb, 0x30,
	0x32, 0xd5, 0x0d, 0x2f, 0x1f, 0xf4, 0x32, 0xaf, 0xfd, 0x1e, 0x06, 0x18, 0x18, 0xe6, 0xba, 0xf6,
	0x9d, 0x74, 0x03, 0x1e, 0x29, 0x6a, 0x6a, 0xb4, 0xd6, 0x10, 0x5f, 0xc2, 0xf7, 0x53, 0x12, 0x3c,
	0xfb, 0x30, 0x8a, 0xee, 0xcd, 0xa6, 0x5f, 0x1f, 0xaa, 0xf6, 0x62, 0x71, 0xff, 0xbb, 0x14, 0x55,
	0xd6, 0x94, 0xf1, 0x9f, 0x54, 0xeb, 0xb8, 0x10, 0x75, 0x62, 0x37, 0xa1, 0x10, 0x55, 0xa2, 0x98,
	0xdc, 0xf3, 0x82, 0xa9, 0xc4, 0x6e, 0x4c, 0x77, 0x3f, 0x1f, 0x58, 0xc9, 0xe2, 0x25, 0x00, 0x00,
	0xff, 0xff, 0xc2, 0x09, 0x63, 0x5f, 0x4b, 0x02, 0x00, 0x00,
}
