// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/id.proto

package id // import "golang.52tt.com/protocol/services/rcmd/persona/id"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ProfileId int32

const (
	ProfileId_Invalid ProfileId = 0
	// tt信息从10001开始编码
	ProfileId_User_TT_Basic                     ProfileId = 10001
	ProfileId_Playmate_Rule_UserInfo            ProfileId = 19001
	ProfileId_Playmate_Rule_OnlineInfo          ProfileId = 19002
	ProfileId_Playmate_Rule_OnMicInfo           ProfileId = 19003
	ProfileId_Playmate_Rule_UserFilteredInfo    ProfileId = 19004
	ProfileId_Playmate_Rule_ImInfo              ProfileId = 19005
	ProfileId_Playmate_Rule_PlaymateSettings    ProfileId = 19006
	ProfileId_Playmate_Rule_PlaymateDeliverInfo ProfileId = 19007
	// 音派信息从20001开始编码
	ProfileId_ProfileId_User_YinPai_Basic ProfileId = 20001
)

var ProfileId_name = map[int32]string{
	0:     "Invalid",
	10001: "User_TT_Basic",
	19001: "Playmate_Rule_UserInfo",
	19002: "Playmate_Rule_OnlineInfo",
	19003: "Playmate_Rule_OnMicInfo",
	19004: "Playmate_Rule_UserFilteredInfo",
	19005: "Playmate_Rule_ImInfo",
	19006: "Playmate_Rule_PlaymateSettings",
	19007: "Playmate_Rule_PlaymateDeliverInfo",
	20001: "ProfileId_User_YinPai_Basic",
}
var ProfileId_value = map[string]int32{
	"Invalid":                           0,
	"User_TT_Basic":                     10001,
	"Playmate_Rule_UserInfo":            19001,
	"Playmate_Rule_OnlineInfo":          19002,
	"Playmate_Rule_OnMicInfo":           19003,
	"Playmate_Rule_UserFilteredInfo":    19004,
	"Playmate_Rule_ImInfo":              19005,
	"Playmate_Rule_PlaymateSettings":    19006,
	"Playmate_Rule_PlaymateDeliverInfo": 19007,
	"ProfileId_User_YinPai_Basic":       20001,
}

func (x ProfileId) String() string {
	return proto.EnumName(ProfileId_name, int32(x))
}
func (ProfileId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_id_095a68b4bb04b178, []int{0}
}

func init() {
	proto.RegisterEnum("rcmd.persona.id.ProfileId", ProfileId_name, ProfileId_value)
}

func init() { proto.RegisterFile("rcmd/persona/id.proto", fileDescriptor_id_095a68b4bb04b178) }

var fileDescriptor_id_095a68b4bb04b178 = []byte{
	// 286 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x91, 0xcb, 0x4a, 0x33, 0x31,
	0x18, 0x86, 0xff, 0xfc, 0x0b, 0xc5, 0x88, 0x38, 0x04, 0x4f, 0x78, 0x28, 0x14, 0x04, 0xc1, 0xc5,
	0x0c, 0x5a, 0xbc, 0x81, 0x22, 0xc2, 0x2c, 0xd4, 0x41, 0xeb, 0x42, 0x37, 0x43, 0x4c, 0xbe, 0x0e,
	0x1f, 0x64, 0x92, 0x92, 0xc4, 0x01, 0x2f, 0x43, 0xc8, 0xd2, 0x8d, 0x97, 0xe1, 0xa1, 0x7a, 0x6b,
	0xd2, 0x30, 0x0a, 0xad, 0x5d, 0xe6, 0x7d, 0x1e, 0x5e, 0x92, 0x37, 0x74, 0xdd, 0x8a, 0x5a, 0x66,
	0x23, 0xb0, 0xce, 0x68, 0x9e, 0xa1, 0x4c, 0x47, 0xd6, 0x78, 0xc3, 0x56, 0x27, 0x71, 0xda, 0xc6,
	0x29, 0xca, 0xc3, 0xf1, 0x7f, 0xba, 0x54, 0x58, 0x33, 0x44, 0x05, 0xb9, 0x64, 0xcb, 0x74, 0x31,
	0xd7, 0x0d, 0x57, 0x28, 0x93, 0x7f, 0x8c, 0xd1, 0x95, 0x1b, 0x07, 0xb6, 0x1c, 0x0c, 0xca, 0x3e,
	0x77, 0x28, 0x92, 0xa7, 0x0b, 0xb6, 0x4b, 0x37, 0x0a, 0xc5, 0x1f, 0x6b, 0xee, 0xa1, 0xbc, 0x7a,
	0x50, 0x50, 0x4e, 0x8c, 0x5c, 0x0f, 0x4d, 0xf2, 0x1a, 0x08, 0xeb, 0xd0, 0xad, 0x69, 0x7a, 0xa9,
	0x15, 0x6a, 0x88, 0xfc, 0x2d, 0x10, 0xb6, 0x47, 0x37, 0x67, 0xf9, 0x39, 0x8a, 0x88, 0xdf, 0x03,
	0x61, 0xfb, 0xb4, 0xf3, 0xb7, 0xfc, 0x0c, 0x95, 0x07, 0x0b, 0x32, 0x5a, 0x1f, 0x81, 0xb0, 0x6d,
	0xba, 0x36, 0x6d, 0xe5, 0x75, 0x64, 0xe3, 0x79, 0x0d, 0x3f, 0xa7, 0x6b, 0xf0, 0x1e, 0x75, 0xe5,
	0x92, 0xcf, 0x40, 0xd8, 0x01, 0xed, 0xce, 0xb7, 0x4e, 0x41, 0x61, 0xd3, 0xbe, 0xe7, 0x2b, 0x10,
	0xd6, 0xa5, 0x3b, 0xbf, 0xdb, 0xc4, 0xcb, 0x94, 0xb7, 0xa8, 0x0b, 0x8e, 0xed, 0x1e, 0x2f, 0xcf,
	0xa4, 0xdf, 0xbb, 0x3b, 0xaa, 0x8c, 0xe2, 0xba, 0x4a, 0x4f, 0x8e, 0xbd, 0x4f, 0x85, 0xa9, 0xb3,
	0xb8, 0xb4, 0x30, 0x2a, 0x73, 0x60, 0x1b, 0x14, 0xe0, 0xb2, 0x99, 0xbf, 0xb8, 0x5f, 0x88, 0x4a,
	0xef, 0x3b, 0x00, 0x00, 0xff, 0xff, 0xc9, 0x98, 0x4e, 0xb7, 0xa5, 0x01, 0x00, 0x00,
}
