// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/ttc/two_dim_feature.proto

package rcmd_persona_ttc_channel

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/rcmd/persona"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TwoDimFeature struct {
	Id                   string                        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Info                 []*TwoDimFeature_CategoryInfo `protobuf:"bytes,2,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *TwoDimFeature) Reset()         { *m = TwoDimFeature{} }
func (m *TwoDimFeature) String() string { return proto.CompactTextString(m) }
func (*TwoDimFeature) ProtoMessage()    {}
func (*TwoDimFeature) Descriptor() ([]byte, []int) {
	return fileDescriptor_two_dim_feature_e0c3750ae7f0f585, []int{0}
}
func (m *TwoDimFeature) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TwoDimFeature.Unmarshal(m, b)
}
func (m *TwoDimFeature) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TwoDimFeature.Marshal(b, m, deterministic)
}
func (dst *TwoDimFeature) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TwoDimFeature.Merge(dst, src)
}
func (m *TwoDimFeature) XXX_Size() int {
	return xxx_messageInfo_TwoDimFeature.Size(m)
}
func (m *TwoDimFeature) XXX_DiscardUnknown() {
	xxx_messageInfo_TwoDimFeature.DiscardUnknown(m)
}

var xxx_messageInfo_TwoDimFeature proto.InternalMessageInfo

func (m *TwoDimFeature) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TwoDimFeature) GetInfo() []*TwoDimFeature_CategoryInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type TwoDimFeature_Feature struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TwoDimFeature_Feature) Reset()         { *m = TwoDimFeature_Feature{} }
func (m *TwoDimFeature_Feature) String() string { return proto.CompactTextString(m) }
func (*TwoDimFeature_Feature) ProtoMessage()    {}
func (*TwoDimFeature_Feature) Descriptor() ([]byte, []int) {
	return fileDescriptor_two_dim_feature_e0c3750ae7f0f585, []int{0, 0}
}
func (m *TwoDimFeature_Feature) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TwoDimFeature_Feature.Unmarshal(m, b)
}
func (m *TwoDimFeature_Feature) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TwoDimFeature_Feature.Marshal(b, m, deterministic)
}
func (dst *TwoDimFeature_Feature) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TwoDimFeature_Feature.Merge(dst, src)
}
func (m *TwoDimFeature_Feature) XXX_Size() int {
	return xxx_messageInfo_TwoDimFeature_Feature.Size(m)
}
func (m *TwoDimFeature_Feature) XXX_DiscardUnknown() {
	xxx_messageInfo_TwoDimFeature_Feature.DiscardUnknown(m)
}

var xxx_messageInfo_TwoDimFeature_Feature proto.InternalMessageInfo

func (m *TwoDimFeature_Feature) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TwoDimFeature_Feature) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type TwoDimFeature_CategoryInfo struct {
	CategoryName         string                   `protobuf:"bytes,1,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	Features             []*TwoDimFeature_Feature `protobuf:"bytes,2,rep,name=features,proto3" json:"features,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *TwoDimFeature_CategoryInfo) Reset()         { *m = TwoDimFeature_CategoryInfo{} }
func (m *TwoDimFeature_CategoryInfo) String() string { return proto.CompactTextString(m) }
func (*TwoDimFeature_CategoryInfo) ProtoMessage()    {}
func (*TwoDimFeature_CategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_two_dim_feature_e0c3750ae7f0f585, []int{0, 1}
}
func (m *TwoDimFeature_CategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TwoDimFeature_CategoryInfo.Unmarshal(m, b)
}
func (m *TwoDimFeature_CategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TwoDimFeature_CategoryInfo.Marshal(b, m, deterministic)
}
func (dst *TwoDimFeature_CategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TwoDimFeature_CategoryInfo.Merge(dst, src)
}
func (m *TwoDimFeature_CategoryInfo) XXX_Size() int {
	return xxx_messageInfo_TwoDimFeature_CategoryInfo.Size(m)
}
func (m *TwoDimFeature_CategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TwoDimFeature_CategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TwoDimFeature_CategoryInfo proto.InternalMessageInfo

func (m *TwoDimFeature_CategoryInfo) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *TwoDimFeature_CategoryInfo) GetFeatures() []*TwoDimFeature_Feature {
	if m != nil {
		return m.Features
	}
	return nil
}

func init() {
	proto.RegisterType((*TwoDimFeature)(nil), "rcmd.persona.ttc.channel.TwoDimFeature")
	proto.RegisterType((*TwoDimFeature_Feature)(nil), "rcmd.persona.ttc.channel.TwoDimFeature.Feature")
	proto.RegisterType((*TwoDimFeature_CategoryInfo)(nil), "rcmd.persona.ttc.channel.TwoDimFeature.CategoryInfo")
}

func init() {
	proto.RegisterFile("rcmd/persona/ttc/two_dim_feature.proto", fileDescriptor_two_dim_feature_e0c3750ae7f0f585)
}

var fileDescriptor_two_dim_feature_e0c3750ae7f0f585 = []byte{
	// 243 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x2b, 0x4a, 0xce, 0x4d,
	0xd1, 0x2f, 0x48, 0x2d, 0x2a, 0xce, 0xcf, 0x4b, 0xd4, 0x2f, 0x29, 0x49, 0xd6, 0x2f, 0x29, 0xcf,
	0x8f, 0x4f, 0xc9, 0xcc, 0x8d, 0x4f, 0x4b, 0x4d, 0x2c, 0x29, 0x2d, 0x4a, 0xd5, 0x2b, 0x28, 0xca,
	0x2f, 0xc9, 0x17, 0x92, 0x00, 0xa9, 0xd3, 0x83, 0xaa, 0xd3, 0x2b, 0x29, 0x49, 0xd6, 0x4b, 0xce,
	0x48, 0xcc, 0xcb, 0x4b, 0xcd, 0x91, 0x92, 0x42, 0x31, 0x21, 0xbf, 0xa0, 0x24, 0x33, 0x3f, 0xaf,
	0x18, 0xa2, 0x4b, 0x69, 0x21, 0x13, 0x17, 0x6f, 0x48, 0x79, 0xbe, 0x4b, 0x66, 0xae, 0x1b, 0xc4,
	0x34, 0x21, 0x3e, 0x2e, 0xa6, 0xcc, 0x14, 0x09, 0x46, 0x05, 0x46, 0x0d, 0xce, 0x20, 0xa6, 0xcc,
	0x14, 0x21, 0x0f, 0x2e, 0x96, 0xcc, 0xbc, 0xb4, 0x7c, 0x09, 0x26, 0x05, 0x66, 0x0d, 0x6e, 0x23,
	0x13, 0x3d, 0x5c, 0xd6, 0xe8, 0xa1, 0x18, 0xa3, 0xe7, 0x9c, 0x58, 0x92, 0x9a, 0x9e, 0x5f, 0x54,
	0xe9, 0x99, 0x97, 0x96, 0x1f, 0x04, 0x36, 0x41, 0xca, 0x98, 0x8b, 0x1d, 0x66, 0x89, 0x10, 0x17,
	0x4b, 0x5e, 0x62, 0x6e, 0x2a, 0xd4, 0x1a, 0x30, 0x5b, 0x48, 0x84, 0x8b, 0xb5, 0x2c, 0x31, 0xa7,
	0x34, 0x55, 0x82, 0x09, 0x2c, 0x08, 0xe1, 0x48, 0x35, 0x30, 0x72, 0xf1, 0x20, 0x9b, 0x25, 0xa4,
	0xcc, 0xc5, 0x9b, 0x0c, 0xe5, 0xc7, 0x23, 0x99, 0xc1, 0x03, 0x13, 0xf4, 0x03, 0x99, 0xe5, 0xcd,
	0xc5, 0x01, 0x0d, 0x9d, 0x62, 0xa8, 0xc3, 0xf5, 0x89, 0x75, 0x38, 0x94, 0x0e, 0x82, 0x1b, 0x90,
	0xc4, 0x06, 0x0e, 0x2a, 0x63, 0x40, 0x00, 0x00, 0x00, 0xff, 0xff, 0xa9, 0xf0, 0x6b, 0x89, 0x8a,
	0x01, 0x00, 0x00,
}
