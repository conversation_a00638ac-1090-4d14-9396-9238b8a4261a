// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/persona_scan.proto

package scan // import "golang.52tt.com/protocol/services/rcmd/persona/scan"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/rcmd/recall_common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ScanJob struct {
	ProfileApp           string   `protobuf:"bytes,1,opt,name=profile_app,json=profileApp,proto3" json:"profile_app,omitempty"`
	Version              uint32   `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	Field                string   `protobuf:"bytes,4,opt,name=field,proto3" json:"field,omitempty"`
	Key                  string   `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	ExpireAt             uint32   `protobuf:"varint,5,opt,name=expireAt,proto3" json:"expireAt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScanJob) Reset()         { *m = ScanJob{} }
func (m *ScanJob) String() string { return proto.CompactTextString(m) }
func (*ScanJob) ProtoMessage()    {}
func (*ScanJob) Descriptor() ([]byte, []int) {
	return fileDescriptor_persona_scan_2bc3783ae5d3731e, []int{0}
}
func (m *ScanJob) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanJob.Unmarshal(m, b)
}
func (m *ScanJob) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanJob.Marshal(b, m, deterministic)
}
func (dst *ScanJob) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanJob.Merge(dst, src)
}
func (m *ScanJob) XXX_Size() int {
	return xxx_messageInfo_ScanJob.Size(m)
}
func (m *ScanJob) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanJob.DiscardUnknown(m)
}

var xxx_messageInfo_ScanJob proto.InternalMessageInfo

func (m *ScanJob) GetProfileApp() string {
	if m != nil {
		return m.ProfileApp
	}
	return ""
}

func (m *ScanJob) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *ScanJob) GetField() string {
	if m != nil {
		return m.Field
	}
	return ""
}

func (m *ScanJob) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ScanJob) GetExpireAt() uint32 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

type BatchGetScanJobReq struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	ProfileId            uint32   `protobuf:"varint,2,opt,name=profile_id,json=profileId,proto3" json:"profile_id,omitempty"`
	Version              uint32   `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	UpdateTimeStart      uint32   `protobuf:"varint,4,opt,name=update_time_start,json=updateTimeStart,proto3" json:"update_time_start,omitempty"`
	UpdateTimeEnd        uint32   `protobuf:"varint,5,opt,name=update_time_end,json=updateTimeEnd,proto3" json:"update_time_end,omitempty"`
	Offset               uint32   `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetScanJobReq) Reset()         { *m = BatchGetScanJobReq{} }
func (m *BatchGetScanJobReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetScanJobReq) ProtoMessage()    {}
func (*BatchGetScanJobReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_persona_scan_2bc3783ae5d3731e, []int{1}
}
func (m *BatchGetScanJobReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetScanJobReq.Unmarshal(m, b)
}
func (m *BatchGetScanJobReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetScanJobReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetScanJobReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetScanJobReq.Merge(dst, src)
}
func (m *BatchGetScanJobReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetScanJobReq.Size(m)
}
func (m *BatchGetScanJobReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetScanJobReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetScanJobReq proto.InternalMessageInfo

func (m *BatchGetScanJobReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *BatchGetScanJobReq) GetProfileId() uint32 {
	if m != nil {
		return m.ProfileId
	}
	return 0
}

func (m *BatchGetScanJobReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *BatchGetScanJobReq) GetUpdateTimeStart() uint32 {
	if m != nil {
		return m.UpdateTimeStart
	}
	return 0
}

func (m *BatchGetScanJobReq) GetUpdateTimeEnd() uint32 {
	if m != nil {
		return m.UpdateTimeEnd
	}
	return 0
}

func (m *BatchGetScanJobReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *BatchGetScanJobReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type BatchGetScanJobRsp struct {
	JobList              []*ScanJob `protobuf:"bytes,1,rep,name=job_list,json=jobList,proto3" json:"job_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BatchGetScanJobRsp) Reset()         { *m = BatchGetScanJobRsp{} }
func (m *BatchGetScanJobRsp) String() string { return proto.CompactTextString(m) }
func (*BatchGetScanJobRsp) ProtoMessage()    {}
func (*BatchGetScanJobRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_persona_scan_2bc3783ae5d3731e, []int{2}
}
func (m *BatchGetScanJobRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetScanJobRsp.Unmarshal(m, b)
}
func (m *BatchGetScanJobRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetScanJobRsp.Marshal(b, m, deterministic)
}
func (dst *BatchGetScanJobRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetScanJobRsp.Merge(dst, src)
}
func (m *BatchGetScanJobRsp) XXX_Size() int {
	return xxx_messageInfo_BatchGetScanJobRsp.Size(m)
}
func (m *BatchGetScanJobRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetScanJobRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetScanJobRsp proto.InternalMessageInfo

func (m *BatchGetScanJobRsp) GetJobList() []*ScanJob {
	if m != nil {
		return m.JobList
	}
	return nil
}

func init() {
	proto.RegisterType((*ScanJob)(nil), "rcmd.persona.scan.ScanJob")
	proto.RegisterType((*BatchGetScanJobReq)(nil), "rcmd.persona.scan.BatchGetScanJobReq")
	proto.RegisterType((*BatchGetScanJobRsp)(nil), "rcmd.persona.scan.BatchGetScanJobRsp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ScanClient is the client API for Scan service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ScanClient interface {
	BatchGetScanJob(ctx context.Context, in *BatchGetScanJobReq, opts ...grpc.CallOption) (*BatchGetScanJobRsp, error)
}

type scanClient struct {
	cc *grpc.ClientConn
}

func NewScanClient(cc *grpc.ClientConn) ScanClient {
	return &scanClient{cc}
}

func (c *scanClient) BatchGetScanJob(ctx context.Context, in *BatchGetScanJobReq, opts ...grpc.CallOption) (*BatchGetScanJobRsp, error) {
	out := new(BatchGetScanJobRsp)
	err := c.cc.Invoke(ctx, "/rcmd.persona.scan.Scan/BatchGetScanJob", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScanServer is the server API for Scan service.
type ScanServer interface {
	BatchGetScanJob(context.Context, *BatchGetScanJobReq) (*BatchGetScanJobRsp, error)
}

func RegisterScanServer(s *grpc.Server, srv ScanServer) {
	s.RegisterService(&_Scan_serviceDesc, srv)
}

func _Scan_BatchGetScanJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetScanJobReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScanServer).BatchGetScanJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.persona.scan.Scan/BatchGetScanJob",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScanServer).BatchGetScanJob(ctx, req.(*BatchGetScanJobReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Scan_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.persona.scan.Scan",
	HandlerType: (*ScanServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetScanJob",
			Handler:    _Scan_BatchGetScanJob_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd/persona/persona_scan.proto",
}

func init() {
	proto.RegisterFile("rcmd/persona/persona_scan.proto", fileDescriptor_persona_scan_2bc3783ae5d3731e)
}

var fileDescriptor_persona_scan_2bc3783ae5d3731e = []byte{
	// 390 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x52, 0xcd, 0x6a, 0xdc, 0x30,
	0x18, 0xc4, 0x75, 0xb2, 0x4e, 0xbe, 0xb0, 0x6c, 0x23, 0x4a, 0x11, 0x86, 0x92, 0x65, 0xa1, 0x65,
	0xe9, 0xc1, 0x86, 0x0d, 0xfb, 0x00, 0x09, 0x94, 0xd2, 0x9f, 0x93, 0xd3, 0x53, 0x2f, 0x42, 0x96,
	0x3f, 0x6f, 0x95, 0xca, 0x96, 0x2a, 0xa9, 0xa1, 0x7d, 0x83, 0xbe, 0x66, 0xdf, 0xa4, 0x58, 0x96,
	0x9b, 0x6c, 0xb7, 0x87, 0x9c, 0xe4, 0x99, 0x6f, 0x66, 0x18, 0x4b, 0x1f, 0x5c, 0x58, 0xd1, 0x35,
	0xa5, 0x41, 0xeb, 0x74, 0xcf, 0xa7, 0x93, 0x39, 0xc1, 0xfb, 0xc2, 0x58, 0xed, 0x35, 0x39, 0x1f,
	0x04, 0x45, 0x1c, 0x14, 0xc3, 0x20, 0x1f, 0x3d, 0x16, 0x05, 0x57, 0x2a, 0x1e, 0x4c, 0xe8, 0xae,
	0xd3, 0xd1, 0xb3, 0xfa, 0x95, 0x40, 0x76, 0x23, 0x78, 0xff, 0x5e, 0xd7, 0xe4, 0x02, 0xce, 0x8c,
	0xd5, 0xad, 0x54, 0xc8, 0xb8, 0x31, 0x34, 0x59, 0x26, 0xeb, 0xd3, 0x0a, 0x22, 0x75, 0x65, 0x0c,
	0xa1, 0x90, 0xdd, 0xa1, 0x75, 0x52, 0xf7, 0xf4, 0xc9, 0x32, 0x59, 0xcf, 0xab, 0x09, 0x92, 0x67,
	0x70, 0xdc, 0x4a, 0x54, 0x0d, 0x3d, 0x0a, 0xa6, 0x11, 0x90, 0xa7, 0x90, 0x7e, 0xc5, 0x9f, 0x34,
	0x0d, 0xdc, 0xf0, 0x49, 0x72, 0x38, 0xc1, 0x1f, 0x46, 0x5a, 0xbc, 0xf2, 0xf4, 0x38, 0x44, 0xfc,
	0xc5, 0xab, 0xdf, 0x09, 0x90, 0x6b, 0xee, 0xc5, 0x97, 0xb7, 0xe8, 0x63, 0xa5, 0x0a, 0xbf, 0x4d,
	0x21, 0xc9, 0x7d, 0xc8, 0x0b, 0x98, 0x4a, 0x31, 0xd9, 0xc4, 0x26, 0xa7, 0x91, 0x79, 0xd7, 0x3c,
	0x6c, 0x99, 0xee, 0xb7, 0x7c, 0x0d, 0xe7, 0xdf, 0x4d, 0xc3, 0x3d, 0x32, 0x2f, 0x3b, 0x64, 0xce,
	0x73, 0xeb, 0x43, 0xe3, 0x79, 0xb5, 0x18, 0x07, 0x9f, 0x64, 0x87, 0x37, 0x03, 0x4d, 0x5e, 0xc1,
	0xe2, 0xa1, 0x16, 0xfb, 0x26, 0x16, 0x9e, 0xdf, 0x2b, 0xdf, 0xf4, 0x0d, 0x79, 0x0e, 0x33, 0xdd,
	0xb6, 0x0e, 0x3d, 0x9d, 0x85, 0x71, 0x44, 0xc3, 0x8d, 0x28, 0xd9, 0x49, 0x4f, 0xb3, 0x40, 0x8f,
	0x60, 0xf5, 0xe1, 0xf0, 0x17, 0x9d, 0x21, 0x5b, 0x38, 0xb9, 0xd5, 0x35, 0x53, 0xd2, 0x79, 0x9a,
	0x2c, 0xd3, 0xf5, 0xd9, 0x26, 0x2f, 0x0e, 0xde, 0xb2, 0x98, 0x0c, 0xd9, 0xad, 0xae, 0x3f, 0x4a,
	0xe7, 0x37, 0x3b, 0x38, 0x1a, 0x38, 0xc2, 0x60, 0xf1, 0x4f, 0x28, 0x79, 0xf9, 0x1f, 0xff, 0xe1,
	0xdd, 0xe6, 0x8f, 0x91, 0x39, 0x73, 0xbd, 0xfd, 0x7c, 0xb9, 0xd3, 0x8a, 0xf7, 0xbb, 0x62, 0xbb,
	0xf1, 0xbe, 0x10, 0xba, 0x2b, 0xc3, 0xf6, 0x08, 0xad, 0x4a, 0x87, 0xf6, 0x4e, 0x0a, 0x74, 0xe5,
	0xde, 0x76, 0x0e, 0x49, 0xf5, 0x2c, 0x88, 0x2e, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0xf1, 0xa9,
	0xd2, 0xa7, 0xb9, 0x02, 0x00, 0x00,
}
