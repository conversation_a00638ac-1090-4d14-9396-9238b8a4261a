// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/tt/chatcard_view.proto

package chatcard_view // import "golang.52tt.com/protocol/services/rcmd/persona/tt/chatcard_view"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/rcmd/persona"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChatCardView struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RegTime              uint32   `protobuf:"varint,2,opt,name=reg_time,json=regTime,proto3" json:"reg_time,omitempty"`
	Group                uint32   `protobuf:"varint,3,opt,name=group,proto3" json:"group,omitempty"`
	GangupList           []uint32 `protobuf:"varint,4,rep,packed,name=gangup_list,json=gangupList,proto3" json:"gangup_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChatCardView) Reset()         { *m = ChatCardView{} }
func (m *ChatCardView) String() string { return proto.CompactTextString(m) }
func (*ChatCardView) ProtoMessage()    {}
func (*ChatCardView) Descriptor() ([]byte, []int) {
	return fileDescriptor_chatcard_view_6ff1b326a4554a0c, []int{0}
}
func (m *ChatCardView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatCardView.Unmarshal(m, b)
}
func (m *ChatCardView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatCardView.Marshal(b, m, deterministic)
}
func (dst *ChatCardView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatCardView.Merge(dst, src)
}
func (m *ChatCardView) XXX_Size() int {
	return xxx_messageInfo_ChatCardView.Size(m)
}
func (m *ChatCardView) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatCardView.DiscardUnknown(m)
}

var xxx_messageInfo_ChatCardView proto.InternalMessageInfo

func (m *ChatCardView) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChatCardView) GetRegTime() uint32 {
	if m != nil {
		return m.RegTime
	}
	return 0
}

func (m *ChatCardView) GetGroup() uint32 {
	if m != nil {
		return m.Group
	}
	return 0
}

func (m *ChatCardView) GetGangupList() []uint32 {
	if m != nil {
		return m.GangupList
	}
	return nil
}

func init() {
	proto.RegisterType((*ChatCardView)(nil), "rcmd.persona.tt.chatcard_view.ChatCardView")
}

func init() {
	proto.RegisterFile("rcmd/persona/tt/chatcard_view.proto", fileDescriptor_chatcard_view_6ff1b326a4554a0c)
}

var fileDescriptor_chatcard_view_6ff1b326a4554a0c = []byte{
	// 297 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x90, 0xb1, 0x4a, 0x33, 0x41,
	0x10, 0xc7, 0xb9, 0x2f, 0x9f, 0x51, 0x57, 0x03, 0x72, 0x85, 0xae, 0x81, 0x83, 0x10, 0x41, 0xd2,
	0x78, 0x8b, 0x8a, 0x8d, 0x8d, 0x98, 0x14, 0x36, 0x56, 0x41, 0x2c, 0x6c, 0x8e, 0xbd, 0xbd, 0x61,
	0x33, 0x70, 0x77, 0x7b, 0xcc, 0xee, 0x25, 0xd8, 0xa6, 0x12, 0x1f, 0xe4, 0xde, 0xc0, 0x77, 0x10,
	0xec, 0x7c, 0x95, 0xbc, 0x80, 0xdc, 0x46, 0xc1, 0x34, 0x76, 0x33, 0x7f, 0x7e, 0xbf, 0x81, 0xf9,
	0xb3, 0x13, 0x52, 0x45, 0x26, 0x2a, 0x20, 0x6b, 0x4a, 0x29, 0x9c, 0x13, 0x6a, 0x26, 0x9d, 0x92,
	0x94, 0x25, 0x73, 0x84, 0x45, 0x5c, 0x91, 0x71, 0x26, 0x8c, 0x5a, 0x28, 0xfe, 0x86, 0x62, 0xe7,
	0xe2, 0x0d, 0xa8, 0xdf, 0xdf, 0xb8, 0xf1, 0x83, 0x79, 0x75, 0xf8, 0x11, 0xb0, 0xfd, 0xc9, 0x4c,
	0xba, 0x89, 0xa4, 0xec, 0x11, 0x61, 0x11, 0x1e, 0xb0, 0x4e, 0x8d, 0x19, 0x0f, 0x06, 0xc1, 0xa8,
	0x37, 0x6d, 0xc7, 0xf0, 0x98, 0xed, 0x10, 0xe8, 0xc4, 0x61, 0x01, 0xfc, 0x9f, 0x8f, 0xb7, 0x09,
	0xf4, 0x03, 0x16, 0x10, 0x9e, 0xb1, 0x2d, 0x4d, 0xa6, 0xae, 0x78, 0xa7, 0xcd, 0xc7, 0x47, 0xcb,
	0x26, 0xda, 0x95, 0x1a, 0x12, 0x1f, 0xbe, 0x36, 0x51, 0x17, 0x4b, 0x45, 0xe9, 0xf3, 0x74, 0x4d,
	0x85, 0x77, 0x6c, 0x4f, 0xcb, 0x52, 0xd7, 0x55, 0x92, 0xa3, 0x75, 0xfc, 0xff, 0xa0, 0x33, 0xea,
	0x8d, 0x4f, 0x97, 0x4d, 0x34, 0xac, 0x2d, 0x50, 0x62, 0x8d, 0x42, 0x99, 0x27, 0x29, 0xcc, 0xe4,
	0x1c, 0x0d, 0x89, 0x73, 0xf1, 0x8b, 0x9e, 0xb2, 0xf5, 0x72, 0x8f, 0xd6, 0x5d, 0x1f, 0xbe, 0xbc,
	0xf1, 0xe0, 0x7d, 0xc5, 0x83, 0xcf, 0x15, 0x67, 0xde, 0x4d, 0xa5, 0x45, 0x35, 0xbe, 0x7d, 0xba,
	0xd1, 0x26, 0x97, 0xa5, 0x8e, 0xaf, 0x2e, 0xda, 0x1a, 0x4c, 0x21, 0xfc, 0x9b, 0xca, 0xe4, 0xc2,
	0x02, 0xcd, 0x51, 0x81, 0x15, 0x7f, 0x36, 0x9a, 0x76, 0xbd, 0x70, 0xf9, 0x15, 0x00, 0x00, 0xff,
	0xff, 0xa4, 0x0b, 0x19, 0xa9, 0x79, 0x01, 0x00, 0x00,
}
