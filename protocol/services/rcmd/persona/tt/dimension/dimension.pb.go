// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/tt/dimension.proto

package dimension // import "golang.52tt.com/protocol/services/rcmd/persona/tt/dimension"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/rcmd/persona"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PreferGameLabelType int32

const (
	PreferGameLabelType_system PreferGameLabelType = 0
	PreferGameLabelType_custom PreferGameLabelType = 1
)

var PreferGameLabelType_name = map[int32]string{
	0: "system",
	1: "custom",
}
var PreferGameLabelType_value = map[string]int32{
	"system": 0,
	"custom": 1,
}

func (x PreferGameLabelType) String() string {
	return proto.EnumName(PreferGameLabelType_name, int32(x))
}
func (PreferGameLabelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_dimension_daac45275c1e40d8, []int{0}
}

// Deprecated
type UserSingScale struct {
	// 歌神专区
	SongGodClassALowerLimit float64 `protobuf:"fixed64,1,opt,name=song_god_class_a_lower_limit,json=songGodClassALowerLimit,proto3" json:"song_god_class_a_lower_limit,omitempty"`
	SongGodClassBLowerLimit float64 `protobuf:"fixed64,2,opt,name=song_god_class_b_lower_limit,json=songGodClassBLowerLimit,proto3" json:"song_god_class_b_lower_limit,omitempty"`
	SongGodClassCLowerLimit float64 `protobuf:"fixed64,3,opt,name=song_god_class_c_lower_limit,json=songGodClassCLowerLimit,proto3" json:"song_god_class_c_lower_limit,omitempty"`
	// 走音专区
	OutToneClassALowerLimit float64  `protobuf:"fixed64,4,opt,name=out_tone_class_a_lower_limit,json=outToneClassALowerLimit,proto3" json:"out_tone_class_a_lower_limit,omitempty"`
	OutToneClassBLowerLimit float64  `protobuf:"fixed64,5,opt,name=out_tone_class_b_lower_limit,json=outToneClassBLowerLimit,proto3" json:"out_tone_class_b_lower_limit,omitempty"`
	OutToneClassCLowerLimit float64  `protobuf:"fixed64,6,opt,name=out_tone_class_c_lower_limit,json=outToneClassCLowerLimit,proto3" json:"out_tone_class_c_lower_limit,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *UserSingScale) Reset()         { *m = UserSingScale{} }
func (m *UserSingScale) String() string { return proto.CompactTextString(m) }
func (*UserSingScale) ProtoMessage()    {}
func (*UserSingScale) Descriptor() ([]byte, []int) {
	return fileDescriptor_dimension_daac45275c1e40d8, []int{0}
}
func (m *UserSingScale) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSingScale.Unmarshal(m, b)
}
func (m *UserSingScale) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSingScale.Marshal(b, m, deterministic)
}
func (dst *UserSingScale) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSingScale.Merge(dst, src)
}
func (m *UserSingScale) XXX_Size() int {
	return xxx_messageInfo_UserSingScale.Size(m)
}
func (m *UserSingScale) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSingScale.DiscardUnknown(m)
}

var xxx_messageInfo_UserSingScale proto.InternalMessageInfo

func (m *UserSingScale) GetSongGodClassALowerLimit() float64 {
	if m != nil {
		return m.SongGodClassALowerLimit
	}
	return 0
}

func (m *UserSingScale) GetSongGodClassBLowerLimit() float64 {
	if m != nil {
		return m.SongGodClassBLowerLimit
	}
	return 0
}

func (m *UserSingScale) GetSongGodClassCLowerLimit() float64 {
	if m != nil {
		return m.SongGodClassCLowerLimit
	}
	return 0
}

func (m *UserSingScale) GetOutToneClassALowerLimit() float64 {
	if m != nil {
		return m.OutToneClassALowerLimit
	}
	return 0
}

func (m *UserSingScale) GetOutToneClassBLowerLimit() float64 {
	if m != nil {
		return m.OutToneClassBLowerLimit
	}
	return 0
}

func (m *UserSingScale) GetOutToneClassCLowerLimit() float64 {
	if m != nil {
		return m.OutToneClassCLowerLimit
	}
	return 0
}

// 接歌抢唱专区分数等级V2
type UserSingScaleV2 struct {
	SingScoreLimit       map[string]*SingScoreLimit `protobuf:"bytes,1,rep,name=sing_score_limit,json=singScoreLimit,proto3" json:"sing_score_limit,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *UserSingScaleV2) Reset()         { *m = UserSingScaleV2{} }
func (m *UserSingScaleV2) String() string { return proto.CompactTextString(m) }
func (*UserSingScaleV2) ProtoMessage()    {}
func (*UserSingScaleV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_dimension_daac45275c1e40d8, []int{1}
}
func (m *UserSingScaleV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSingScaleV2.Unmarshal(m, b)
}
func (m *UserSingScaleV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSingScaleV2.Marshal(b, m, deterministic)
}
func (dst *UserSingScaleV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSingScaleV2.Merge(dst, src)
}
func (m *UserSingScaleV2) XXX_Size() int {
	return xxx_messageInfo_UserSingScaleV2.Size(m)
}
func (m *UserSingScaleV2) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSingScaleV2.DiscardUnknown(m)
}

var xxx_messageInfo_UserSingScaleV2 proto.InternalMessageInfo

func (m *UserSingScaleV2) GetSingScoreLimit() map[string]*SingScoreLimit {
	if m != nil {
		return m.SingScoreLimit
	}
	return nil
}

type SingScoreLimit struct {
	ALowerLimit          float64  `protobuf:"fixed64,1,opt,name=a_lower_limit,json=aLowerLimit,proto3" json:"a_lower_limit,omitempty"`
	BLowerLimit          float64  `protobuf:"fixed64,2,opt,name=b_lower_limit,json=bLowerLimit,proto3" json:"b_lower_limit,omitempty"`
	CLowerLimit          float64  `protobuf:"fixed64,3,opt,name=c_lower_limit,json=cLowerLimit,proto3" json:"c_lower_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingScoreLimit) Reset()         { *m = SingScoreLimit{} }
func (m *SingScoreLimit) String() string { return proto.CompactTextString(m) }
func (*SingScoreLimit) ProtoMessage()    {}
func (*SingScoreLimit) Descriptor() ([]byte, []int) {
	return fileDescriptor_dimension_daac45275c1e40d8, []int{2}
}
func (m *SingScoreLimit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingScoreLimit.Unmarshal(m, b)
}
func (m *SingScoreLimit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingScoreLimit.Marshal(b, m, deterministic)
}
func (dst *SingScoreLimit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingScoreLimit.Merge(dst, src)
}
func (m *SingScoreLimit) XXX_Size() int {
	return xxx_messageInfo_SingScoreLimit.Size(m)
}
func (m *SingScoreLimit) XXX_DiscardUnknown() {
	xxx_messageInfo_SingScoreLimit.DiscardUnknown(m)
}

var xxx_messageInfo_SingScoreLimit proto.InternalMessageInfo

func (m *SingScoreLimit) GetALowerLimit() float64 {
	if m != nil {
		return m.ALowerLimit
	}
	return 0
}

func (m *SingScoreLimit) GetBLowerLimit() float64 {
	if m != nil {
		return m.BLowerLimit
	}
	return 0
}

func (m *SingScoreLimit) GetCLowerLimit() float64 {
	if m != nil {
		return m.CLowerLimit
	}
	return 0
}

// 房间全局画像
type ChannelGlobalDimension struct {
	EnterRatioList       []float64     `protobuf:"fixed64,1,rep,packed,name=enter_ratio_list,json=enterRatioList,proto3" json:"enter_ratio_list,omitempty"`
	PreferThreshold      float64       `protobuf:"fixed64,2,opt,name=prefer_threshold,json=preferThreshold,proto3" json:"prefer_threshold,omitempty"`
	Games                []*PreferGame `protobuf:"bytes,3,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelGlobalDimension) Reset()         { *m = ChannelGlobalDimension{} }
func (m *ChannelGlobalDimension) String() string { return proto.CompactTextString(m) }
func (*ChannelGlobalDimension) ProtoMessage()    {}
func (*ChannelGlobalDimension) Descriptor() ([]byte, []int) {
	return fileDescriptor_dimension_daac45275c1e40d8, []int{3}
}
func (m *ChannelGlobalDimension) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGlobalDimension.Unmarshal(m, b)
}
func (m *ChannelGlobalDimension) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGlobalDimension.Marshal(b, m, deterministic)
}
func (dst *ChannelGlobalDimension) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGlobalDimension.Merge(dst, src)
}
func (m *ChannelGlobalDimension) XXX_Size() int {
	return xxx_messageInfo_ChannelGlobalDimension.Size(m)
}
func (m *ChannelGlobalDimension) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGlobalDimension.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGlobalDimension proto.InternalMessageInfo

func (m *ChannelGlobalDimension) GetEnterRatioList() []float64 {
	if m != nil {
		return m.EnterRatioList
	}
	return nil
}

func (m *ChannelGlobalDimension) GetPreferThreshold() float64 {
	if m != nil {
		return m.PreferThreshold
	}
	return 0
}

func (m *ChannelGlobalDimension) GetGames() []*PreferGame {
	if m != nil {
		return m.Games
	}
	return nil
}

type ChannelGlobalDimension_EnterRatioListUpdate struct {
	EnterRatioList       []float64 `protobuf:"fixed64,1,rep,packed,name=enter_ratio_list,json=enterRatioList,proto3" json:"enter_ratio_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ChannelGlobalDimension_EnterRatioListUpdate) Reset() {
	*m = ChannelGlobalDimension_EnterRatioListUpdate{}
}
func (m *ChannelGlobalDimension_EnterRatioListUpdate) String() string {
	return proto.CompactTextString(m)
}
func (*ChannelGlobalDimension_EnterRatioListUpdate) ProtoMessage() {}
func (*ChannelGlobalDimension_EnterRatioListUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_dimension_daac45275c1e40d8, []int{3, 0}
}
func (m *ChannelGlobalDimension_EnterRatioListUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGlobalDimension_EnterRatioListUpdate.Unmarshal(m, b)
}
func (m *ChannelGlobalDimension_EnterRatioListUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGlobalDimension_EnterRatioListUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelGlobalDimension_EnterRatioListUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGlobalDimension_EnterRatioListUpdate.Merge(dst, src)
}
func (m *ChannelGlobalDimension_EnterRatioListUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelGlobalDimension_EnterRatioListUpdate.Size(m)
}
func (m *ChannelGlobalDimension_EnterRatioListUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGlobalDimension_EnterRatioListUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGlobalDimension_EnterRatioListUpdate proto.InternalMessageInfo

func (m *ChannelGlobalDimension_EnterRatioListUpdate) GetEnterRatioList() []float64 {
	if m != nil {
		return m.EnterRatioList
	}
	return nil
}

type ChannelGlobalDimension_PreferThresholdUpdate struct {
	PreferThreshold      float64  `protobuf:"fixed64,1,opt,name=prefer_threshold,json=preferThreshold,proto3" json:"prefer_threshold,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGlobalDimension_PreferThresholdUpdate) Reset() {
	*m = ChannelGlobalDimension_PreferThresholdUpdate{}
}
func (m *ChannelGlobalDimension_PreferThresholdUpdate) String() string {
	return proto.CompactTextString(m)
}
func (*ChannelGlobalDimension_PreferThresholdUpdate) ProtoMessage() {}
func (*ChannelGlobalDimension_PreferThresholdUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_dimension_daac45275c1e40d8, []int{3, 1}
}
func (m *ChannelGlobalDimension_PreferThresholdUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGlobalDimension_PreferThresholdUpdate.Unmarshal(m, b)
}
func (m *ChannelGlobalDimension_PreferThresholdUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGlobalDimension_PreferThresholdUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelGlobalDimension_PreferThresholdUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGlobalDimension_PreferThresholdUpdate.Merge(dst, src)
}
func (m *ChannelGlobalDimension_PreferThresholdUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelGlobalDimension_PreferThresholdUpdate.Size(m)
}
func (m *ChannelGlobalDimension_PreferThresholdUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGlobalDimension_PreferThresholdUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGlobalDimension_PreferThresholdUpdate proto.InternalMessageInfo

func (m *ChannelGlobalDimension_PreferThresholdUpdate) GetPreferThreshold() float64 {
	if m != nil {
		return m.PreferThreshold
	}
	return 0
}

type ChannelGlobalDimension_SetPreferGames struct {
	Games                []*PreferGame `protobuf:"bytes,1,rep,name=games,proto3" json:"games,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelGlobalDimension_SetPreferGames) Reset()         { *m = ChannelGlobalDimension_SetPreferGames{} }
func (m *ChannelGlobalDimension_SetPreferGames) String() string { return proto.CompactTextString(m) }
func (*ChannelGlobalDimension_SetPreferGames) ProtoMessage()    {}
func (*ChannelGlobalDimension_SetPreferGames) Descriptor() ([]byte, []int) {
	return fileDescriptor_dimension_daac45275c1e40d8, []int{3, 2}
}
func (m *ChannelGlobalDimension_SetPreferGames) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGlobalDimension_SetPreferGames.Unmarshal(m, b)
}
func (m *ChannelGlobalDimension_SetPreferGames) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGlobalDimension_SetPreferGames.Marshal(b, m, deterministic)
}
func (dst *ChannelGlobalDimension_SetPreferGames) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGlobalDimension_SetPreferGames.Merge(dst, src)
}
func (m *ChannelGlobalDimension_SetPreferGames) XXX_Size() int {
	return xxx_messageInfo_ChannelGlobalDimension_SetPreferGames.Size(m)
}
func (m *ChannelGlobalDimension_SetPreferGames) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGlobalDimension_SetPreferGames.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGlobalDimension_SetPreferGames proto.InternalMessageInfo

func (m *ChannelGlobalDimension_SetPreferGames) GetGames() []*PreferGame {
	if m != nil {
		return m.Games
	}
	return nil
}

type PreferGame struct {
	Name                 string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Labels               []*PreferGameLabel `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PreferGame) Reset()         { *m = PreferGame{} }
func (m *PreferGame) String() string { return proto.CompactTextString(m) }
func (*PreferGame) ProtoMessage()    {}
func (*PreferGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_dimension_daac45275c1e40d8, []int{4}
}
func (m *PreferGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreferGame.Unmarshal(m, b)
}
func (m *PreferGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreferGame.Marshal(b, m, deterministic)
}
func (dst *PreferGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreferGame.Merge(dst, src)
}
func (m *PreferGame) XXX_Size() int {
	return xxx_messageInfo_PreferGame.Size(m)
}
func (m *PreferGame) XXX_DiscardUnknown() {
	xxx_messageInfo_PreferGame.DiscardUnknown(m)
}

var xxx_messageInfo_PreferGame proto.InternalMessageInfo

func (m *PreferGame) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PreferGame) GetLabels() []*PreferGameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

type PreferGameLabel struct {
	LabelId              uint32              `protobuf:"varint,1,opt,name=label_id,json=labelId,proto3" json:"label_id,omitempty"`
	Val                  string              `protobuf:"bytes,2,opt,name=val,proto3" json:"val,omitempty"`
	Type                 PreferGameLabelType `protobuf:"varint,3,opt,name=type,proto3,enum=dimension.PreferGameLabelType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PreferGameLabel) Reset()         { *m = PreferGameLabel{} }
func (m *PreferGameLabel) String() string { return proto.CompactTextString(m) }
func (*PreferGameLabel) ProtoMessage()    {}
func (*PreferGameLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_dimension_daac45275c1e40d8, []int{5}
}
func (m *PreferGameLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreferGameLabel.Unmarshal(m, b)
}
func (m *PreferGameLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreferGameLabel.Marshal(b, m, deterministic)
}
func (dst *PreferGameLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreferGameLabel.Merge(dst, src)
}
func (m *PreferGameLabel) XXX_Size() int {
	return xxx_messageInfo_PreferGameLabel.Size(m)
}
func (m *PreferGameLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_PreferGameLabel.DiscardUnknown(m)
}

var xxx_messageInfo_PreferGameLabel proto.InternalMessageInfo

func (m *PreferGameLabel) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

func (m *PreferGameLabel) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *PreferGameLabel) GetType() PreferGameLabelType {
	if m != nil {
		return m.Type
	}
	return PreferGameLabelType_system
}

func init() {
	proto.RegisterType((*UserSingScale)(nil), "dimension.UserSingScale")
	proto.RegisterType((*UserSingScaleV2)(nil), "dimension.UserSingScaleV2")
	proto.RegisterMapType((map[string]*SingScoreLimit)(nil), "dimension.UserSingScaleV2.SingScoreLimitEntry")
	proto.RegisterType((*SingScoreLimit)(nil), "dimension.SingScoreLimit")
	proto.RegisterType((*ChannelGlobalDimension)(nil), "dimension.ChannelGlobalDimension")
	proto.RegisterType((*ChannelGlobalDimension_EnterRatioListUpdate)(nil), "dimension.ChannelGlobalDimension.EnterRatioListUpdate")
	proto.RegisterType((*ChannelGlobalDimension_PreferThresholdUpdate)(nil), "dimension.ChannelGlobalDimension.PreferThresholdUpdate")
	proto.RegisterType((*ChannelGlobalDimension_SetPreferGames)(nil), "dimension.ChannelGlobalDimension.SetPreferGames")
	proto.RegisterType((*PreferGame)(nil), "dimension.PreferGame")
	proto.RegisterType((*PreferGameLabel)(nil), "dimension.PreferGameLabel")
	proto.RegisterEnum("dimension.PreferGameLabelType", PreferGameLabelType_name, PreferGameLabelType_value)
}

func init() {
	proto.RegisterFile("rcmd/persona/tt/dimension.proto", fileDescriptor_dimension_daac45275c1e40d8)
}

var fileDescriptor_dimension_daac45275c1e40d8 = []byte{
	// 709 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x55, 0x5f, 0x6b, 0x13, 0x4f,
	0x14, 0xfd, 0x6d, 0xd3, 0xa6, 0xbf, 0x4e, 0xc8, 0x1f, 0xa6, 0x56, 0xb7, 0x41, 0xab, 0xee, 0x8b,
	0x55, 0x31, 0x81, 0x55, 0x41, 0x22, 0x7d, 0x30, 0xb5, 0x94, 0x62, 0xc0, 0xb2, 0x4d, 0x45, 0x44,
	0x58, 0x26, 0x9b, 0xeb, 0x76, 0x71, 0x76, 0x26, 0xcc, 0x4c, 0x22, 0x01, 0x1f, 0xf2, 0xd0, 0x07,
	0xf1, 0xad, 0x7e, 0x06, 0xad, 0x08, 0xbe, 0xe6, 0x33, 0xe9, 0x83, 0xfd, 0x0e, 0xb2, 0xb3, 0x49,
	0x93, 0x4d, 0xb6, 0xc5, 0xb7, 0x9b, 0x7b, 0xcf, 0xb9, 0xf7, 0xdc, 0x39, 0x37, 0x09, 0xba, 0x29,
	0xbc, 0xb0, 0x5d, 0xed, 0x80, 0x90, 0x9c, 0x91, 0xaa, 0x52, 0xd5, 0x76, 0x10, 0x02, 0x93, 0x01,
	0x67, 0x95, 0x8e, 0xe0, 0x8a, 0xe3, 0x95, 0xf3, 0x44, 0xb9, 0x9c, 0xc0, 0xf2, 0x8e, 0x0a, 0x38,
	0x93, 0x31, 0xcc, 0xfa, 0x91, 0x41, 0xf9, 0x43, 0x09, 0xe2, 0x20, 0x60, 0xfe, 0x81, 0x47, 0x28,
	0xe0, 0x2d, 0x74, 0x5d, 0x72, 0xe6, 0xbb, 0x3e, 0x6f, 0xbb, 0x1e, 0x25, 0x52, 0xba, 0xc4, 0xa5,
	0xfc, 0x03, 0x08, 0x97, 0x06, 0x61, 0xa0, 0x4c, 0xe3, 0x96, 0xb1, 0x69, 0x38, 0xd7, 0x22, 0xcc,
	0x2e, 0x6f, 0x6f, 0x47, 0x88, 0x67, 0x8d, 0xa8, 0xde, 0x88, 0xca, 0x29, 0xf4, 0x56, 0x82, 0xbe,
	0x30, 0x4f, 0xaf, 0x5f, 0x4a, 0xf7, 0x12, 0xf4, 0xcc, 0x3c, 0x7d, 0x3b, 0x49, 0xe7, 0x5d, 0xe5,
	0x2a, 0xce, 0x20, 0x55, 0xfc, 0x62, 0x4c, 0xe7, 0x5d, 0xd5, 0xe4, 0x0c, 0xd2, 0xc4, 0xcf, 0xd0,
	0x93, 0xe2, 0x97, 0xe6, 0xe9, 0xf5, 0x4b, 0xe9, 0x49, 0xf1, 0xd9, 0x79, 0xfa, 0x94, 0xf8, 0xda,
	0xc6, 0x60, 0x68, 0x1e, 0x1f, 0xff, 0xaa, 0x7f, 0x1a, 0x9a, 0xc6, 0x97, 0xa1, 0x59, 0xec, 0x4a,
	0x10, 0xae, 0x0c, 0x98, 0xef, 0xca, 0xc8, 0x19, 0xeb, 0x8f, 0x81, 0x8a, 0x09, 0xaf, 0x5e, 0xd9,
	0xf8, 0x35, 0x2a, 0x8d, 0x10, 0x5c, 0xc0, 0xb9, 0x43, 0x99, 0xcd, 0x9c, 0x5d, 0xa9, 0x4c, 0x4e,
	0x62, 0x86, 0x55, 0x89, 0x63, 0x2e, 0x40, 0x0f, 0xde, 0x61, 0x4a, 0xf4, 0x9d, 0x82, 0x4c, 0x24,
	0xcb, 0x6f, 0xd1, 0x6a, 0x0a, 0x0c, 0x97, 0x50, 0xe6, 0x3d, 0xf4, 0xf5, 0x15, 0xac, 0x38, 0x51,
	0x88, 0xab, 0x68, 0xa9, 0x47, 0x68, 0x17, 0xb4, 0xb5, 0x39, 0x7b, 0x7d, 0x6a, 0x6e, 0xb2, 0x81,
	0x13, 0xe3, 0x6a, 0x0b, 0x4f, 0x8c, 0xda, 0xed, 0xc1, 0xd0, 0xfc, 0x3d, 0xd9, 0x15, 0xcf, 0xec,
	0xea, 0xf6, 0x6c, 0xeb, 0x23, 0x2a, 0x24, 0xf9, 0xd8, 0x42, 0xf9, 0xb4, 0x5b, 0xcc, 0x91, 0x29,
	0x0f, 0x2c, 0x94, 0x4f, 0x3b, 0xb8, 0x5c, 0x2b, 0x89, 0x49, 0xbb, 0xaa, 0x9c, 0x37, 0xc1, 0x58,
	0x3f, 0x33, 0xe8, 0xea, 0xf6, 0x11, 0x61, 0x0c, 0xe8, 0x2e, 0xe5, 0x2d, 0x42, 0x9f, 0x8f, 0xb7,
	0xc2, 0x9b, 0xa8, 0x04, 0x4c, 0x81, 0x70, 0x05, 0x51, 0x01, 0x77, 0x69, 0x20, 0xe3, 0x37, 0x37,
	0x9c, 0x82, 0xce, 0x3b, 0x51, 0xba, 0x11, 0x48, 0x85, 0xef, 0xa2, 0x52, 0x47, 0xc0, 0x3b, 0x10,
	0xae, 0x3a, 0x12, 0x20, 0x8f, 0x38, 0x6d, 0x8f, 0xf4, 0x14, 0xe3, 0x7c, 0x73, 0x9c, 0xc6, 0xf7,
	0xd1, 0x92, 0x4f, 0x42, 0x90, 0x66, 0x46, 0xbb, 0xb7, 0x36, 0xf5, 0x8a, 0xfb, 0x1a, 0xba, 0x4b,
	0x42, 0x70, 0x62, 0x4c, 0x79, 0x0f, 0x5d, 0xd9, 0x49, 0x4c, 0x3a, 0xec, 0xb4, 0x89, 0x82, 0x7f,
	0x57, 0x56, 0x5b, 0x1e, 0x0c, 0xcd, 0x6f, 0x27, 0x67, 0xf5, 0xf2, 0x0b, 0xb4, 0xb6, 0x9f, 0x94,
	0x32, 0xea, 0x95, 0xa6, 0xdd, 0x48, 0xd5, 0xae, 0x9b, 0x9d, 0x46, 0xcd, 0x5e, 0xa2, 0xc2, 0x01,
	0xa8, 0x89, 0x5e, 0x89, 0x1f, 0x8d, 0xd7, 0x32, 0x2e, 0x59, 0xab, 0xbe, 0xfc, 0xf9, 0xf4, 0x46,
	0x46, 0x82, 0x1a, 0xed, 0xa7, 0x1b, 0x7e, 0x3f, 0x39, 0xab, 0xd7, 0xee, 0x0c, 0x86, 0xe6, 0xd7,
	0x93, 0xb3, 0xf1, 0x99, 0x98, 0x5e, 0xec, 0x88, 0xeb, 0x6b, 0x4b, 0xdc, 0xf3, 0x66, 0x56, 0x13,
	0xa1, 0x49, 0x3f, 0x8c, 0xd1, 0x22, 0x23, 0x21, 0x8c, 0xae, 0x54, 0xc7, 0xd8, 0x46, 0x59, 0x4a,
	0x5a, 0x40, 0xa5, 0xb9, 0xa0, 0xa5, 0x94, 0x53, 0xa5, 0x34, 0x22, 0x88, 0x33, 0x42, 0x5a, 0x02,
	0x15, 0x67, 0x4a, 0x78, 0x1d, 0xfd, 0xaf, 0x8b, 0x6e, 0x10, 0x3f, 0x47, 0xde, 0x59, 0xd6, 0x9f,
	0xf7, 0xda, 0xd1, 0x57, 0xa3, 0x47, 0xa8, 0x36, 0x78, 0xc5, 0x89, 0x42, 0x6c, 0xa3, 0x45, 0xd5,
	0xef, 0x80, 0xbe, 0xaf, 0x82, 0xbd, 0x71, 0xf1, 0xc4, 0x66, 0xbf, 0x03, 0x8e, 0xc6, 0xde, 0x7b,
	0x80, 0x56, 0x53, 0x8a, 0x18, 0xa1, 0xac, 0xec, 0x4b, 0x05, 0x61, 0xe9, 0xbf, 0x28, 0xf6, 0xba,
	0x52, 0xf1, 0xb0, 0x64, 0xd4, 0xb7, 0xde, 0x3c, 0xf5, 0x39, 0x25, 0xcc, 0xaf, 0x3c, 0xb6, 0x95,
	0xaa, 0x78, 0x3c, 0xac, 0xea, 0x5f, 0x76, 0x8f, 0xd3, 0xaa, 0x04, 0xd1, 0x0b, 0x3c, 0x90, 0xd5,
	0x0b, 0xff, 0x2c, 0x5a, 0x59, 0x0d, 0x7e, 0xf8, 0x37, 0x00, 0x00, 0xff, 0xff, 0xfa, 0x1e, 0x05,
	0xe0, 0x50, 0x06, 0x00, 0x00,
}
