// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/tt/rule.proto

package rule // import "golang.52tt.com/protocol/services/rcmd/persona/tt/rule"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/rcmd/persona"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RuleDeprecated struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RuleDeprecated) Reset()         { *m = RuleDeprecated{} }
func (m *RuleDeprecated) String() string { return proto.CompactTextString(m) }
func (*RuleDeprecated) ProtoMessage()    {}
func (*RuleDeprecated) Descriptor() ([]byte, []int) {
	return fileDescriptor_rule_3ff221efe9225cf9, []int{0}
}
func (m *RuleDeprecated) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RuleDeprecated.Unmarshal(m, b)
}
func (m *RuleDeprecated) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RuleDeprecated.Marshal(b, m, deterministic)
}
func (dst *RuleDeprecated) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RuleDeprecated.Merge(dst, src)
}
func (m *RuleDeprecated) XXX_Size() int {
	return xxx_messageInfo_RuleDeprecated.Size(m)
}
func (m *RuleDeprecated) XXX_DiscardUnknown() {
	xxx_messageInfo_RuleDeprecated.DiscardUnknown(m)
}

var xxx_messageInfo_RuleDeprecated proto.InternalMessageInfo

func init() {
	proto.RegisterType((*RuleDeprecated)(nil), "rcmd.persona.tt.rule.rule_deprecated")
}

func init() { proto.RegisterFile("rcmd/persona/tt/rule.proto", fileDescriptor_rule_3ff221efe9225cf9) }

var fileDescriptor_rule_3ff221efe9225cf9 = []byte{
	// 131 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x92, 0x2a, 0x4a, 0xce, 0x4d,
	0xd1, 0x2f, 0x48, 0x2d, 0x2a, 0xce, 0xcf, 0x4b, 0xd4, 0x2f, 0x29, 0xd1, 0x2f, 0x2a, 0xcd, 0x49,
	0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x12, 0x01, 0xc9, 0xe9, 0x41, 0xe5, 0xf4, 0x4a, 0x4a,
	0xf4, 0x40, 0x72, 0x52, 0xa8, 0x3a, 0x60, 0xb2, 0x60, 0x1d, 0x4a, 0x82, 0x5c, 0xfc, 0x20, 0x35,
	0xf1, 0x29, 0xa9, 0x05, 0x45, 0xa9, 0xc9, 0x89, 0x25, 0xa9, 0x29, 0x4e, 0x16, 0x51, 0x66, 0xe9,
	0xf9, 0x39, 0x89, 0x79, 0xe9, 0x7a, 0xa6, 0x46, 0x25, 0x25, 0x7a, 0xc9, 0xf9, 0xb9, 0xfa, 0x60,
	0xb5, 0xc9, 0xf9, 0x39, 0xfa, 0xc5, 0xa9, 0x45, 0x65, 0x99, 0xc9, 0xa9, 0xc5, 0xfa, 0xd8, 0x1c,
	0x91, 0xc4, 0x06, 0x56, 0x67, 0x0c, 0x08, 0x00, 0x00, 0xff, 0xff, 0x48, 0xa7, 0x7a, 0xed, 0xa3,
	0x00, 0x00, 0x00,
}
