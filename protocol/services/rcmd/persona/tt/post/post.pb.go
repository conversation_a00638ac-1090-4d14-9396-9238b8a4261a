// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/tt/post.proto

package post // import "golang.52tt.com/protocol/services/rcmd/persona/tt/post"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/rcmd/persona"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PostInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	AttitudeCount        uint32   `protobuf:"varint,3,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	IsDelete             bool     `protobuf:"varint,4,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	CommentCount         uint32   `protobuf:"varint,5,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	Province             string   `protobuf:"bytes,6,opt,name=province,proto3" json:"province,omitempty"`
	City                 string   `protobuf:"bytes,7,opt,name=city,proto3" json:"city,omitempty"`
	CreatedAt            uint32   `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	IsSystem             bool     `protobuf:"varint,9,opt,name=is_system,json=isSystem,proto3" json:"is_system,omitempty"`
	IsWater              bool     `protobuf:"varint,10,opt,name=is_water,json=isWater,proto3" json:"is_water,omitempty"`
	IsPrivate            bool     `protobuf:"varint,11,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	ExposeCount          uint32   `protobuf:"varint,12,opt,name=expose_count,json=exposeCount,proto3" json:"expose_count,omitempty"`
	IsExposeWhite        bool     `protobuf:"varint,13,opt,name=is_expose_white,json=isExposeWhite,proto3" json:"is_expose_white,omitempty"`
	IsAppBlock           bool     `protobuf:"varint,14,opt,name=is_app_block,json=isAppBlock,proto3" json:"is_app_block,omitempty"`
	OwnerReplyCommentNum uint32   `protobuf:"varint,15,opt,name=owner_reply_comment_num,json=ownerReplyCommentNum,proto3" json:"owner_reply_comment_num,omitempty"`
	UserRegisterTime     uint32   `protobuf:"varint,16,opt,name=user_register_time,json=userRegisterTime,proto3" json:"user_register_time,omitempty"`
	UserSex              uint32   `protobuf:"varint,17,opt,name=user_sex,json=userSex,proto3" json:"user_sex,omitempty"`
	Topics               []string `protobuf:"bytes,18,rep,name=topics,proto3" json:"topics,omitempty"`
	BadScore             float64  `protobuf:"fixed64,19,opt,name=bad_score,json=badScore,proto3" json:"bad_score,omitempty"`
	PostType             uint32   `protobuf:"varint,20,opt,name=post_type,json=postType,proto3" json:"post_type,omitempty"`
	BadLevel             string   `protobuf:"bytes,21,opt,name=bad_level,json=badLevel,proto3" json:"bad_level,omitempty"`
	UserAge              uint32   `protobuf:"varint,22,opt,name=user_age,json=userAge,proto3" json:"user_age,omitempty"`
	ImageTags            string   `protobuf:"bytes,23,opt,name=image_tags,json=imageTags,proto3" json:"image_tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo) Reset()         { *m = PostInfo{} }
func (m *PostInfo) String() string { return proto.CompactTextString(m) }
func (*PostInfo) ProtoMessage()    {}
func (*PostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{0}
}
func (m *PostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo.Unmarshal(m, b)
}
func (m *PostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo.Marshal(b, m, deterministic)
}
func (dst *PostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo.Merge(dst, src)
}
func (m *PostInfo) XXX_Size() int {
	return xxx_messageInfo_PostInfo.Size(m)
}
func (m *PostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo proto.InternalMessageInfo

func (m *PostInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *PostInfo) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

func (m *PostInfo) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *PostInfo) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *PostInfo) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *PostInfo) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *PostInfo) GetIsSystem() bool {
	if m != nil {
		return m.IsSystem
	}
	return false
}

func (m *PostInfo) GetIsWater() bool {
	if m != nil {
		return m.IsWater
	}
	return false
}

func (m *PostInfo) GetIsPrivate() bool {
	if m != nil {
		return m.IsPrivate
	}
	return false
}

func (m *PostInfo) GetExposeCount() uint32 {
	if m != nil {
		return m.ExposeCount
	}
	return 0
}

func (m *PostInfo) GetIsExposeWhite() bool {
	if m != nil {
		return m.IsExposeWhite
	}
	return false
}

func (m *PostInfo) GetIsAppBlock() bool {
	if m != nil {
		return m.IsAppBlock
	}
	return false
}

func (m *PostInfo) GetOwnerReplyCommentNum() uint32 {
	if m != nil {
		return m.OwnerReplyCommentNum
	}
	return 0
}

func (m *PostInfo) GetUserRegisterTime() uint32 {
	if m != nil {
		return m.UserRegisterTime
	}
	return 0
}

func (m *PostInfo) GetUserSex() uint32 {
	if m != nil {
		return m.UserSex
	}
	return 0
}

func (m *PostInfo) GetTopics() []string {
	if m != nil {
		return m.Topics
	}
	return nil
}

func (m *PostInfo) GetBadScore() float64 {
	if m != nil {
		return m.BadScore
	}
	return 0
}

func (m *PostInfo) GetPostType() uint32 {
	if m != nil {
		return m.PostType
	}
	return 0
}

func (m *PostInfo) GetBadLevel() string {
	if m != nil {
		return m.BadLevel
	}
	return ""
}

func (m *PostInfo) GetUserAge() uint32 {
	if m != nil {
		return m.UserAge
	}
	return 0
}

func (m *PostInfo) GetImageTags() string {
	if m != nil {
		return m.ImageTags
	}
	return ""
}

type PostInfo_Delete struct {
	IsDelete             bool     `protobuf:"varint,1,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo_Delete) Reset()         { *m = PostInfo_Delete{} }
func (m *PostInfo_Delete) String() string { return proto.CompactTextString(m) }
func (*PostInfo_Delete) ProtoMessage()    {}
func (*PostInfo_Delete) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{0, 0}
}
func (m *PostInfo_Delete) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo_Delete.Unmarshal(m, b)
}
func (m *PostInfo_Delete) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo_Delete.Marshal(b, m, deterministic)
}
func (dst *PostInfo_Delete) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo_Delete.Merge(dst, src)
}
func (m *PostInfo_Delete) XXX_Size() int {
	return xxx_messageInfo_PostInfo_Delete.Size(m)
}
func (m *PostInfo_Delete) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo_Delete.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo_Delete proto.InternalMessageInfo

func (m *PostInfo_Delete) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

type PostInfo_Attitude struct {
	By                   int64    `protobuf:"varint,1,opt,name=by,proto3" json:"by,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo_Attitude) Reset()         { *m = PostInfo_Attitude{} }
func (m *PostInfo_Attitude) String() string { return proto.CompactTextString(m) }
func (*PostInfo_Attitude) ProtoMessage()    {}
func (*PostInfo_Attitude) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{0, 1}
}
func (m *PostInfo_Attitude) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo_Attitude.Unmarshal(m, b)
}
func (m *PostInfo_Attitude) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo_Attitude.Marshal(b, m, deterministic)
}
func (dst *PostInfo_Attitude) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo_Attitude.Merge(dst, src)
}
func (m *PostInfo_Attitude) XXX_Size() int {
	return xxx_messageInfo_PostInfo_Attitude.Size(m)
}
func (m *PostInfo_Attitude) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo_Attitude.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo_Attitude proto.InternalMessageInfo

func (m *PostInfo_Attitude) GetBy() int64 {
	if m != nil {
		return m.By
	}
	return 0
}

type PostInfo_PrivacyChange struct {
	IsPrivate            bool     `protobuf:"varint,1,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo_PrivacyChange) Reset()         { *m = PostInfo_PrivacyChange{} }
func (m *PostInfo_PrivacyChange) String() string { return proto.CompactTextString(m) }
func (*PostInfo_PrivacyChange) ProtoMessage()    {}
func (*PostInfo_PrivacyChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{0, 2}
}
func (m *PostInfo_PrivacyChange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo_PrivacyChange.Unmarshal(m, b)
}
func (m *PostInfo_PrivacyChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo_PrivacyChange.Marshal(b, m, deterministic)
}
func (dst *PostInfo_PrivacyChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo_PrivacyChange.Merge(dst, src)
}
func (m *PostInfo_PrivacyChange) XXX_Size() int {
	return xxx_messageInfo_PostInfo_PrivacyChange.Size(m)
}
func (m *PostInfo_PrivacyChange) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo_PrivacyChange.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo_PrivacyChange proto.InternalMessageInfo

func (m *PostInfo_PrivacyChange) GetIsPrivate() bool {
	if m != nil {
		return m.IsPrivate
	}
	return false
}

type PostInfo_Comment struct {
	By                   int64    `protobuf:"varint,1,opt,name=by,proto3" json:"by,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo_Comment) Reset()         { *m = PostInfo_Comment{} }
func (m *PostInfo_Comment) String() string { return proto.CompactTextString(m) }
func (*PostInfo_Comment) ProtoMessage()    {}
func (*PostInfo_Comment) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{0, 3}
}
func (m *PostInfo_Comment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo_Comment.Unmarshal(m, b)
}
func (m *PostInfo_Comment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo_Comment.Marshal(b, m, deterministic)
}
func (dst *PostInfo_Comment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo_Comment.Merge(dst, src)
}
func (m *PostInfo_Comment) XXX_Size() int {
	return xxx_messageInfo_PostInfo_Comment.Size(m)
}
func (m *PostInfo_Comment) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo_Comment.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo_Comment proto.InternalMessageInfo

func (m *PostInfo_Comment) GetBy() int64 {
	if m != nil {
		return m.By
	}
	return 0
}

type PostInfo_OwnerReplyComment struct {
	By                   int64    `protobuf:"varint,1,opt,name=by,proto3" json:"by,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo_OwnerReplyComment) Reset()         { *m = PostInfo_OwnerReplyComment{} }
func (m *PostInfo_OwnerReplyComment) String() string { return proto.CompactTextString(m) }
func (*PostInfo_OwnerReplyComment) ProtoMessage()    {}
func (*PostInfo_OwnerReplyComment) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{0, 4}
}
func (m *PostInfo_OwnerReplyComment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo_OwnerReplyComment.Unmarshal(m, b)
}
func (m *PostInfo_OwnerReplyComment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo_OwnerReplyComment.Marshal(b, m, deterministic)
}
func (dst *PostInfo_OwnerReplyComment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo_OwnerReplyComment.Merge(dst, src)
}
func (m *PostInfo_OwnerReplyComment) XXX_Size() int {
	return xxx_messageInfo_PostInfo_OwnerReplyComment.Size(m)
}
func (m *PostInfo_OwnerReplyComment) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo_OwnerReplyComment.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo_OwnerReplyComment proto.InternalMessageInfo

func (m *PostInfo_OwnerReplyComment) GetBy() int64 {
	if m != nil {
		return m.By
	}
	return 0
}

type PostInfo_SetBadScore struct {
	BadScore             float64  `protobuf:"fixed64,1,opt,name=bad_score,json=badScore,proto3" json:"bad_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo_SetBadScore) Reset()         { *m = PostInfo_SetBadScore{} }
func (m *PostInfo_SetBadScore) String() string { return proto.CompactTextString(m) }
func (*PostInfo_SetBadScore) ProtoMessage()    {}
func (*PostInfo_SetBadScore) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{0, 5}
}
func (m *PostInfo_SetBadScore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo_SetBadScore.Unmarshal(m, b)
}
func (m *PostInfo_SetBadScore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo_SetBadScore.Marshal(b, m, deterministic)
}
func (dst *PostInfo_SetBadScore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo_SetBadScore.Merge(dst, src)
}
func (m *PostInfo_SetBadScore) XXX_Size() int {
	return xxx_messageInfo_PostInfo_SetBadScore.Size(m)
}
func (m *PostInfo_SetBadScore) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo_SetBadScore.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo_SetBadScore proto.InternalMessageInfo

func (m *PostInfo_SetBadScore) GetBadScore() float64 {
	if m != nil {
		return m.BadScore
	}
	return 0
}

type PostInfo_SetBadLevel struct {
	BadLevel             string   `protobuf:"bytes,1,opt,name=bad_level,json=badLevel,proto3" json:"bad_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo_SetBadLevel) Reset()         { *m = PostInfo_SetBadLevel{} }
func (m *PostInfo_SetBadLevel) String() string { return proto.CompactTextString(m) }
func (*PostInfo_SetBadLevel) ProtoMessage()    {}
func (*PostInfo_SetBadLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{0, 6}
}
func (m *PostInfo_SetBadLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo_SetBadLevel.Unmarshal(m, b)
}
func (m *PostInfo_SetBadLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo_SetBadLevel.Marshal(b, m, deterministic)
}
func (dst *PostInfo_SetBadLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo_SetBadLevel.Merge(dst, src)
}
func (m *PostInfo_SetBadLevel) XXX_Size() int {
	return xxx_messageInfo_PostInfo_SetBadLevel.Size(m)
}
func (m *PostInfo_SetBadLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo_SetBadLevel.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo_SetBadLevel proto.InternalMessageInfo

func (m *PostInfo_SetBadLevel) GetBadLevel() string {
	if m != nil {
		return m.BadLevel
	}
	return ""
}

type PostInfo_ImageTagUpdate struct {
	ImageTags            string   `protobuf:"bytes,1,opt,name=image_tags,json=imageTags,proto3" json:"image_tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostInfo_ImageTagUpdate) Reset()         { *m = PostInfo_ImageTagUpdate{} }
func (m *PostInfo_ImageTagUpdate) String() string { return proto.CompactTextString(m) }
func (*PostInfo_ImageTagUpdate) ProtoMessage()    {}
func (*PostInfo_ImageTagUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{0, 7}
}
func (m *PostInfo_ImageTagUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostInfo_ImageTagUpdate.Unmarshal(m, b)
}
func (m *PostInfo_ImageTagUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostInfo_ImageTagUpdate.Marshal(b, m, deterministic)
}
func (dst *PostInfo_ImageTagUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostInfo_ImageTagUpdate.Merge(dst, src)
}
func (m *PostInfo_ImageTagUpdate) XXX_Size() int {
	return xxx_messageInfo_PostInfo_ImageTagUpdate.Size(m)
}
func (m *PostInfo_ImageTagUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_PostInfo_ImageTagUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_PostInfo_ImageTagUpdate proto.InternalMessageInfo

func (m *PostInfo_ImageTagUpdate) GetImageTags() string {
	if m != nil {
		return m.ImageTags
	}
	return ""
}

type PostOffline struct {
	ICommentRatio_14D      float64  `protobuf:"fixed64,1,opt,name=i_comment_ratio_14d,json=iCommentRatio14d,proto3" json:"i_comment_ratio_14d,omitempty"`
	IClickRatio_14D        float64  `protobuf:"fixed64,2,opt,name=i_click_ratio_14d,json=iClickRatio14d,proto3" json:"i_click_ratio_14d,omitempty"`
	IClickRatio_3D         float64  `protobuf:"fixed64,3,opt,name=i_click_ratio_3d,json=iClickRatio3d,proto3" json:"i_click_ratio_3d,omitempty"`
	ICommentRatio_3D       float64  `protobuf:"fixed64,4,opt,name=i_comment_ratio_3d,json=iCommentRatio3d,proto3" json:"i_comment_ratio_3d,omitempty"`
	IDiff                  uint32   `protobuf:"varint,5,opt,name=i_diff,json=iDiff,proto3" json:"i_diff,omitempty"`
	IVPublisherClickCnt_3D uint32   `protobuf:"varint,6,opt,name=i_v_publisher_click_cnt_3d,json=iVPublisherClickCnt3d,proto3" json:"i_v_publisher_click_cnt_3d,omitempty"`
	IVPublisherClickCnt_7D uint32   `protobuf:"varint,7,opt,name=i_v_publisher_click_cnt_7d,json=iVPublisherClickCnt7d,proto3" json:"i_v_publisher_click_cnt_7d,omitempty"`
	IViewCnt_14D           uint32   `protobuf:"varint,8,opt,name=i_view_cnt_14d,json=iViewCnt14d,proto3" json:"i_view_cnt_14d,omitempty"`
	IViewCnt_3D            uint32   `protobuf:"varint,9,opt,name=i_view_cnt_3d,json=iViewCnt3d,proto3" json:"i_view_cnt_3d,omitempty"`
	ITypeId                uint32   `protobuf:"varint,10,opt,name=i_type_id,json=iTypeId,proto3" json:"i_type_id,omitempty"`
	ICommentRatio_1D       float64  `protobuf:"fixed64,11,opt,name=i_comment_ratio_1d,json=iCommentRatio1d,proto3" json:"i_comment_ratio_1d,omitempty"`
	IClickRatio_1D         float64  `protobuf:"fixed64,12,opt,name=i_click_ratio_1d,json=iClickRatio1d,proto3" json:"i_click_ratio_1d,omitempty"`
	IViewCnt_1D            uint32   `protobuf:"varint,13,opt,name=i_view_cnt_1d,json=iViewCnt1d,proto3" json:"i_view_cnt_1d,omitempty"`
	ICommentRatio_7D       float64  `protobuf:"fixed64,14,opt,name=i_comment_ratio_7d,json=iCommentRatio7d,proto3" json:"i_comment_ratio_7d,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *PostOffline) Reset()         { *m = PostOffline{} }
func (m *PostOffline) String() string { return proto.CompactTextString(m) }
func (*PostOffline) ProtoMessage()    {}
func (*PostOffline) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{1}
}
func (m *PostOffline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostOffline.Unmarshal(m, b)
}
func (m *PostOffline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostOffline.Marshal(b, m, deterministic)
}
func (dst *PostOffline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostOffline.Merge(dst, src)
}
func (m *PostOffline) XXX_Size() int {
	return xxx_messageInfo_PostOffline.Size(m)
}
func (m *PostOffline) XXX_DiscardUnknown() {
	xxx_messageInfo_PostOffline.DiscardUnknown(m)
}

var xxx_messageInfo_PostOffline proto.InternalMessageInfo

func (m *PostOffline) GetICommentRatio_14D() float64 {
	if m != nil {
		return m.ICommentRatio_14D
	}
	return 0
}

func (m *PostOffline) GetIClickRatio_14D() float64 {
	if m != nil {
		return m.IClickRatio_14D
	}
	return 0
}

func (m *PostOffline) GetIClickRatio_3D() float64 {
	if m != nil {
		return m.IClickRatio_3D
	}
	return 0
}

func (m *PostOffline) GetICommentRatio_3D() float64 {
	if m != nil {
		return m.ICommentRatio_3D
	}
	return 0
}

func (m *PostOffline) GetIDiff() uint32 {
	if m != nil {
		return m.IDiff
	}
	return 0
}

func (m *PostOffline) GetIVPublisherClickCnt_3D() uint32 {
	if m != nil {
		return m.IVPublisherClickCnt_3D
	}
	return 0
}

func (m *PostOffline) GetIVPublisherClickCnt_7D() uint32 {
	if m != nil {
		return m.IVPublisherClickCnt_7D
	}
	return 0
}

func (m *PostOffline) GetIViewCnt_14D() uint32 {
	if m != nil {
		return m.IViewCnt_14D
	}
	return 0
}

func (m *PostOffline) GetIViewCnt_3D() uint32 {
	if m != nil {
		return m.IViewCnt_3D
	}
	return 0
}

func (m *PostOffline) GetITypeId() uint32 {
	if m != nil {
		return m.ITypeId
	}
	return 0
}

func (m *PostOffline) GetICommentRatio_1D() float64 {
	if m != nil {
		return m.ICommentRatio_1D
	}
	return 0
}

func (m *PostOffline) GetIClickRatio_1D() float64 {
	if m != nil {
		return m.IClickRatio_1D
	}
	return 0
}

func (m *PostOffline) GetIViewCnt_1D() uint32 {
	if m != nil {
		return m.IViewCnt_1D
	}
	return 0
}

func (m *PostOffline) GetICommentRatio_7D() float64 {
	if m != nil {
		return m.ICommentRatio_7D
	}
	return 0
}

type PostOnline struct {
	IViewCnt_12H         uint32   `protobuf:"varint,1,opt,name=i_view_cnt_12h,json=iViewCnt12h,proto3" json:"i_view_cnt_12h,omitempty"`
	ICommentCnt_12H      uint32   `protobuf:"varint,2,opt,name=i_comment_cnt_12h,json=iCommentCnt12h,proto3" json:"i_comment_cnt_12h,omitempty"`
	IThumpUpCnt_12H      uint32   `protobuf:"varint,3,opt,name=i_thump_up_cnt_12h,json=iThumpUpCnt12h,proto3" json:"i_thump_up_cnt_12h,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostOnline) Reset()         { *m = PostOnline{} }
func (m *PostOnline) String() string { return proto.CompactTextString(m) }
func (*PostOnline) ProtoMessage()    {}
func (*PostOnline) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{2}
}
func (m *PostOnline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostOnline.Unmarshal(m, b)
}
func (m *PostOnline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostOnline.Marshal(b, m, deterministic)
}
func (dst *PostOnline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostOnline.Merge(dst, src)
}
func (m *PostOnline) XXX_Size() int {
	return xxx_messageInfo_PostOnline.Size(m)
}
func (m *PostOnline) XXX_DiscardUnknown() {
	xxx_messageInfo_PostOnline.DiscardUnknown(m)
}

var xxx_messageInfo_PostOnline proto.InternalMessageInfo

func (m *PostOnline) GetIViewCnt_12H() uint32 {
	if m != nil {
		return m.IViewCnt_12H
	}
	return 0
}

func (m *PostOnline) GetICommentCnt_12H() uint32 {
	if m != nil {
		return m.ICommentCnt_12H
	}
	return 0
}

func (m *PostOnline) GetIThumpUpCnt_12H() uint32 {
	if m != nil {
		return m.IThumpUpCnt_12H
	}
	return 0
}

type PostTextEmb struct {
	ITextEmb             []float32 `protobuf:"fixed32,1,rep,packed,name=i_text_emb,json=iTextEmb,proto3" json:"i_text_emb,omitempty"`
	ITextHumanlen        uint32    `protobuf:"varint,2,opt,name=i_text_humanlen,json=iTextHumanlen,proto3" json:"i_text_humanlen,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *PostTextEmb) Reset()         { *m = PostTextEmb{} }
func (m *PostTextEmb) String() string { return proto.CompactTextString(m) }
func (*PostTextEmb) ProtoMessage()    {}
func (*PostTextEmb) Descriptor() ([]byte, []int) {
	return fileDescriptor_post_0078d49230e319f2, []int{3}
}
func (m *PostTextEmb) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostTextEmb.Unmarshal(m, b)
}
func (m *PostTextEmb) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostTextEmb.Marshal(b, m, deterministic)
}
func (dst *PostTextEmb) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostTextEmb.Merge(dst, src)
}
func (m *PostTextEmb) XXX_Size() int {
	return xxx_messageInfo_PostTextEmb.Size(m)
}
func (m *PostTextEmb) XXX_DiscardUnknown() {
	xxx_messageInfo_PostTextEmb.DiscardUnknown(m)
}

var xxx_messageInfo_PostTextEmb proto.InternalMessageInfo

func (m *PostTextEmb) GetITextEmb() []float32 {
	if m != nil {
		return m.ITextEmb
	}
	return nil
}

func (m *PostTextEmb) GetITextHumanlen() uint32 {
	if m != nil {
		return m.ITextHumanlen
	}
	return 0
}

func init() {
	proto.RegisterType((*PostInfo)(nil), "rcmd.persona.tt.post.PostInfo")
	proto.RegisterType((*PostInfo_Delete)(nil), "rcmd.persona.tt.post.PostInfo.Delete")
	proto.RegisterType((*PostInfo_Attitude)(nil), "rcmd.persona.tt.post.PostInfo.Attitude")
	proto.RegisterType((*PostInfo_PrivacyChange)(nil), "rcmd.persona.tt.post.PostInfo.PrivacyChange")
	proto.RegisterType((*PostInfo_Comment)(nil), "rcmd.persona.tt.post.PostInfo.Comment")
	proto.RegisterType((*PostInfo_OwnerReplyComment)(nil), "rcmd.persona.tt.post.PostInfo.OwnerReplyComment")
	proto.RegisterType((*PostInfo_SetBadScore)(nil), "rcmd.persona.tt.post.PostInfo.SetBadScore")
	proto.RegisterType((*PostInfo_SetBadLevel)(nil), "rcmd.persona.tt.post.PostInfo.SetBadLevel")
	proto.RegisterType((*PostInfo_ImageTagUpdate)(nil), "rcmd.persona.tt.post.PostInfo.ImageTagUpdate")
	proto.RegisterType((*PostOffline)(nil), "rcmd.persona.tt.post.PostOffline")
	proto.RegisterType((*PostOnline)(nil), "rcmd.persona.tt.post.PostOnline")
	proto.RegisterType((*PostTextEmb)(nil), "rcmd.persona.tt.post.PostTextEmb")
}

func init() { proto.RegisterFile("rcmd/persona/tt/post.proto", fileDescriptor_post_0078d49230e319f2) }

var fileDescriptor_post_0078d49230e319f2 = []byte{
	// 1169 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x56, 0xdd, 0x6e, 0x1a, 0xc7,
	0x17, 0xd7, 0x02, 0xe1, 0x63, 0x30, 0xc4, 0x9e, 0x7c, 0x4d, 0x36, 0x46, 0x22, 0x8e, 0x93, 0xbf,
	0xff, 0x4d, 0x0a, 0xc2, 0xd8, 0x75, 0x83, 0xda, 0x4a, 0x36, 0x89, 0x54, 0x4b, 0x55, 0x63, 0xad,
	0x3f, 0x22, 0xf5, 0x66, 0xb5, 0xec, 0x0c, 0x30, 0x0a, 0xfb, 0xd1, 0x9d, 0x01, 0x9b, 0x3b, 0xcb,
	0x57, 0x55, 0x2e, 0xf3, 0x06, 0x95, 0x2a, 0xab, 0x2f, 0xc0, 0x7d, 0x1e, 0x20, 0x2f, 0xd1, 0x27,
	0xe8, 0x45, 0x7b, 0x53, 0xa9, 0x17, 0xd5, 0x9c, 0x9d, 0xc5, 0x80, 0x68, 0xef, 0x38, 0xe7, 0xfc,
	0x7e, 0xe7, 0x1c, 0xce, 0xf9, 0xcd, 0x01, 0x64, 0x46, 0xae, 0x47, 0xeb, 0x21, 0x8b, 0x44, 0xe0,
	0x3b, 0x75, 0x29, 0xeb, 0x61, 0x20, 0x64, 0x2d, 0x8c, 0x02, 0x19, 0xe0, 0xbb, 0x2a, 0x56, 0xd3,
	0xb1, 0x9a, 0x94, 0x35, 0x15, 0x33, 0xe7, 0x19, 0x41, 0x28, 0x79, 0xe0, 0x8b, 0x98, 0xb1, 0xf1,
	0x5b, 0x01, 0xe5, 0x8f, 0x02, 0x21, 0x0f, 0xfd, 0x6e, 0x80, 0x57, 0x51, 0x7a, 0xc8, 0x29, 0x31,
	0xaa, 0xc6, 0x56, 0xc9, 0x52, 0x1f, 0xf1, 0x03, 0x94, 0x53, 0x29, 0x6c, 0x4e, 0x49, 0xaa, 0x6a,
	0x6c, 0x15, 0xac, 0xac, 0x32, 0x0f, 0x29, 0x7e, 0x8a, 0xca, 0x8e, 0x94, 0x5c, 0x0e, 0x29, 0xb3,
	0xdd, 0x60, 0xe8, 0x4b, 0x92, 0x06, 0x56, 0x29, 0xf1, 0xb6, 0x95, 0x13, 0x3f, 0x42, 0x05, 0x2e,
	0x6c, 0xca, 0x06, 0x4c, 0x32, 0x92, 0xa9, 0x1a, 0x5b, 0x79, 0x2b, 0xcf, 0xc5, 0x2b, 0xb0, 0xf1,
	0x13, 0x54, 0x72, 0x03, 0xcf, 0x63, 0xbe, 0xd4, 0x29, 0x6e, 0x41, 0x8a, 0x15, 0xed, 0x8c, 0x33,
	0x98, 0x28, 0x1f, 0x46, 0xc1, 0x88, 0xfb, 0x2e, 0x23, 0x59, 0x68, 0x61, 0x6a, 0x63, 0x8c, 0x32,
	0x2e, 0x97, 0x63, 0x92, 0x03, 0x3f, 0x7c, 0xc6, 0x15, 0x84, 0xdc, 0x88, 0x39, 0x92, 0x51, 0xdb,
	0x91, 0x24, 0x0f, 0x19, 0x0b, 0xda, 0xb3, 0x9f, 0x34, 0x24, 0xc6, 0x42, 0x32, 0x8f, 0x14, 0x92,
	0x86, 0x8e, 0xc1, 0xc6, 0x0f, 0x51, 0x9e, 0x0b, 0xfb, 0xdc, 0x91, 0x2c, 0x22, 0x08, 0x62, 0x39,
	0x2e, 0xde, 0x2a, 0x53, 0xa5, 0xe5, 0xc2, 0x0e, 0x23, 0x3e, 0x72, 0x24, 0x23, 0x45, 0x08, 0x16,
	0xb8, 0x38, 0x8a, 0x1d, 0xf8, 0x31, 0x5a, 0x61, 0x17, 0x61, 0x20, 0x92, 0x61, 0xac, 0x40, 0xdd,
	0x62, 0xec, 0x8b, 0xbf, 0xc8, 0x33, 0x74, 0x9b, 0x0b, 0x5b, 0xa3, 0xce, 0xfb, 0x5c, 0x32, 0x52,
	0x82, 0x34, 0x25, 0x2e, 0x5e, 0x83, 0xf7, 0xad, 0x72, 0xe2, 0x2a, 0x5a, 0xe1, 0xc2, 0x76, 0xc2,
	0xd0, 0xee, 0x0c, 0x02, 0xf7, 0x1d, 0x29, 0x03, 0x08, 0x71, 0xb1, 0x1f, 0x86, 0x07, 0xca, 0x83,
	0x77, 0xd1, 0x83, 0xe0, 0xdc, 0x67, 0x91, 0x1d, 0xb1, 0x70, 0x30, 0xb6, 0x93, 0x19, 0xfa, 0x43,
	0x8f, 0xdc, 0x86, 0xba, 0x77, 0x21, 0x6c, 0xa9, 0x68, 0x3b, 0x0e, 0x7e, 0x3f, 0xf4, 0xf0, 0x0b,
	0x84, 0x87, 0x02, 0x58, 0x3d, 0x2e, 0x24, 0x8b, 0x6c, 0xc9, 0x3d, 0x46, 0x56, 0x81, 0xb1, 0xaa,
	0x22, 0x96, 0x0e, 0x9c, 0x70, 0x8f, 0xa9, 0x59, 0x00, 0x5a, 0xb0, 0x0b, 0xb2, 0x06, 0x98, 0x9c,
	0xb2, 0x8f, 0xd9, 0x05, 0xbe, 0x8f, 0xb2, 0x32, 0x08, 0xb9, 0x2b, 0x08, 0xae, 0xa6, 0x95, 0x26,
	0x62, 0x4b, 0xcd, 0xb6, 0xe3, 0x50, 0x5b, 0xb8, 0x41, 0xc4, 0xc8, 0x9d, 0xaa, 0xb1, 0x65, 0x58,
	0xf9, 0x8e, 0x43, 0x8f, 0x95, 0xad, 0x82, 0xa0, 0x24, 0x39, 0x0e, 0x19, 0xb9, 0x0b, 0x09, 0xf3,
	0xca, 0x71, 0x32, 0x0e, 0x59, 0xc2, 0x1c, 0xb0, 0x11, 0x1b, 0x90, 0x7b, 0xf1, 0x96, 0x3b, 0x0e,
	0xfd, 0x4e, 0xd9, 0xd3, 0x4e, 0x9c, 0x1e, 0x23, 0xf7, 0x6f, 0x3a, 0xd9, 0xef, 0x31, 0xd8, 0x8a,
	0xe7, 0xf4, 0x98, 0x2d, 0x9d, 0x9e, 0x20, 0x0f, 0x80, 0x58, 0x00, 0xcf, 0x89, 0xd3, 0x13, 0xe6,
	0x0e, 0xca, 0x6a, 0xa9, 0x6d, 0xce, 0xea, 0x50, 0xe9, 0x3b, 0x7f, 0x90, 0x7b, 0x7f, 0x5d, 0x49,
	0x0b, 0x26, 0x6f, 0x04, 0xd9, 0xca, 0x7c, 0xfc, 0x83, 0x18, 0xe6, 0x37, 0x28, 0xbf, 0xaf, 0x45,
	0x8c, 0x5f, 0xa0, 0x54, 0x67, 0x0c, 0x84, 0xf4, 0xc1, 0xfa, 0xd5, 0x75, 0x65, 0x41, 0xf4, 0xef,
	0xaf, 0x2b, 0x59, 0xee, 0xbb, 0x51, 0x67, 0x6c, 0xa5, 0x3a, 0x63, 0xe0, 0xa7, 0xcc, 0xaf, 0x51,
	0x09, 0x64, 0xe1, 0x8e, 0xdb, 0x7d, 0xc7, 0xef, 0x31, 0xfc, 0x6c, 0x4e, 0x3b, 0x0b, 0xd5, 0x6f,
	0x44, 0x04, 0xf4, 0xb4, 0xf9, 0x15, 0xca, 0xe9, 0xa5, 0xe1, 0xe7, 0x33, 0xd5, 0x1f, 0x5d, 0x5d,
	0x57, 0xe6, 0x9f, 0xcb, 0x92, 0xe2, 0x19, 0xf3, 0x08, 0xad, 0xbd, 0x59, 0x5c, 0x3e, 0xde, 0x9d,
	0xc9, 0xf3, 0xf4, 0xea, 0xba, 0xf2, 0x6f, 0xf2, 0x59, 0x92, 0xf1, 0x96, 0xf9, 0x12, 0x15, 0x8f,
	0x99, 0x3c, 0x48, 0xf6, 0xb8, 0x39, 0xbb, 0x64, 0x95, 0xd2, 0x98, 0x99, 0x64, 0xb2, 0x6d, 0xa0,
	0x66, 0x6f, 0xa8, 0xf1, 0x22, 0x37, 0x67, 0xb7, 0xac, 0xa8, 0x85, 0x79, 0x2a, 0xa0, 0x80, 0x9a,
	0x33, 0xf7, 0x50, 0xf9, 0x50, 0xef, 0xf1, 0x34, 0xa4, 0xea, 0x89, 0xcd, 0xef, 0xda, 0x58, 0xd8,
	0x75, 0x2b, 0x7b, 0x39, 0x21, 0x9f, 0x3e, 0xb4, 0x5b, 0x6b, 0x97, 0x13, 0xf2, 0xd7, 0xe9, 0x4f,
	0x13, 0x62, 0x7c, 0x98, 0x90, 0x8c, 0x52, 0xd8, 0xc6, 0xef, 0x19, 0x54, 0x54, 0x37, 0xee, 0x4d,
	0xb7, 0x3b, 0xe0, 0x3e, 0xc3, 0x9f, 0xa3, 0x3b, 0x7c, 0xfa, 0xb5, 0x23, 0x47, 0xf2, 0xc0, 0x6e,
	0xec, 0xc4, 0x67, 0xcf, 0xb0, 0x56, 0xb9, 0x9e, 0x9a, 0xa5, 0x02, 0x8d, 0x1d, 0x8a, 0xff, 0x8f,
	0xd6, 0xb8, 0xed, 0x0e, 0xb8, 0xfb, 0x6e, 0x06, 0x9c, 0x02, 0x70, 0x99, 0xb7, 0x95, 0x7f, 0x0a,
	0xfd, 0x1f, 0x5a, 0x9d, 0x87, 0x36, 0x29, 0xdc, 0x45, 0xc3, 0x2a, 0xcd, 0x20, 0x9b, 0x14, 0x3f,
	0x47, 0x78, 0xb1, 0x85, 0x26, 0x85, 0x03, 0x69, 0x58, 0xb7, 0xe7, 0x3a, 0x68, 0x52, 0x7c, 0x0f,
	0x65, 0xb9, 0x4d, 0x79, 0xb7, 0xab, 0x0f, 0xe4, 0x2d, 0xfe, 0x8a, 0x77, 0xbb, 0xf8, 0x25, 0x32,
	0xb9, 0x3d, 0xb2, 0xc3, 0x61, 0x67, 0xc0, 0x45, 0x9f, 0x45, 0xba, 0xb0, 0xeb, 0x4b, 0x95, 0x2b,
	0x0b, 0xd0, 0x7b, 0xfc, 0xec, 0x28, 0x89, 0x43, 0x03, 0x6d, 0x5f, 0x36, 0xe9, 0x7f, 0x51, 0xf7,
	0x28, 0x9c, 0xd3, 0xe5, 0xd4, 0x3d, 0x8a, 0x9f, 0xa0, 0x32, 0xb7, 0x47, 0x9c, 0x9d, 0x03, 0x5a,
	0x8d, 0x22, 0xbe, 0xb1, 0x45, 0x7e, 0xc6, 0xd9, 0x79, 0xdb, 0x97, 0x6a, 0x0e, 0x8f, 0x51, 0x69,
	0x06, 0xd4, 0xa4, 0x70, 0x69, 0x4b, 0x16, 0x4a, 0x30, 0x4d, 0x8a, 0x4d, 0x54, 0xe0, 0x70, 0x0c,
	0xd4, 0x6f, 0x0b, 0x8a, 0x9f, 0x35, 0x57, 0xc7, 0xe0, 0x70, 0xe9, 0x74, 0x1a, 0x14, 0x8e, 0xee,
	0xe2, 0x74, 0x1a, 0x4b, 0x66, 0xde, 0xa0, 0x70, 0x7e, 0xe7, 0x67, 0xde, 0x58, 0x6c, 0xaa, 0x41,
	0xe1, 0xfc, 0xce, 0x34, 0xd5, 0x58, 0x5a, 0x78, 0x8f, 0xc2, 0x05, 0x5e, 0x2c, 0xbc, 0x47, 0x5b,
	0x0f, 0x2f, 0x27, 0xe4, 0xe7, 0x33, 0xad, 0xb4, 0x15, 0x38, 0x6e, 0x41, 0xac, 0xb0, 0x8d, 0x5f,
	0x0c, 0x84, 0x40, 0x71, 0x3e, 0x08, 0x6e, 0x61, 0x66, 0xdb, 0x7d, 0xfd, 0x13, 0x7b, 0x33, 0xb3,
	0xed, 0xbe, 0x96, 0x59, 0xf2, 0xc0, 0x35, 0x2e, 0x05, 0xb8, 0x72, 0x52, 0x5a, 0x43, 0x3f, 0x53,
	0x6d, 0xca, 0xfe, 0xd0, 0x0b, 0xed, 0x61, 0x38, 0xc5, 0xa6, 0x35, 0xf6, 0x44, 0x05, 0x4e, 0xc3,
	0x18, 0xdb, 0x32, 0x2f, 0x27, 0xe4, 0xe3, 0x9f, 0x9f, 0x76, 0x74, 0x9f, 0xc5, 0xb8, 0x4f, 0xe8,
	0x6b, 0xe3, 0xc7, 0xf8, 0x5d, 0x9c, 0xb0, 0x0b, 0xf9, 0xda, 0xeb, 0xe0, 0x75, 0x84, 0xb8, 0x2d,
	0xd9, 0x85, 0xb4, 0x99, 0xd7, 0x21, 0x46, 0x35, 0xbd, 0x95, 0xb2, 0xf2, 0x3c, 0x89, 0xaa, 0xdf,
	0xaf, 0x38, 0xda, 0x1f, 0x7a, 0x8e, 0x3f, 0x60, 0xbe, 0xee, 0xae, 0x04, 0x90, 0x6f, 0xb5, 0xb3,
	0xb5, 0x7e, 0x39, 0x21, 0xbf, 0xfe, 0x3d, 0x2d, 0x58, 0x8a, 0xaf, 0xbe, 0xce, 0x7a, 0xf0, 0xe5,
	0x0f, 0x5f, 0xf4, 0x82, 0x81, 0xe3, 0xf7, 0x6a, 0xbb, 0xdb, 0x52, 0xd6, 0xdc, 0xc0, 0xab, 0xc3,
	0x1f, 0x11, 0x37, 0x18, 0xd4, 0x05, 0x8b, 0x46, 0xdc, 0x65, 0xa2, 0xbe, 0xec, 0x1f, 0x4e, 0x27,
	0x0b, 0xb8, 0xe6, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x43, 0x0c, 0x17, 0x24, 0x00, 0x09, 0x00,
	0x00,
}
