// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/tt/channel.proto

package channel // import "golang.52tt.com/protocol/services/rcmd/persona/tt/channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import common "golang.52tt.com/protocol/services/rcmd/common"
import _ "golang.52tt.com/protocol/services/rcmd/persona"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 为了兼容开黑和音乐，额外开的音乐房类型字段
type ChannelBasic_MusicChannelType int32

const (
	ChannelBasic_UnknownMusicChannelType    ChannelBasic_MusicChannelType = 0
	ChannelBasic_UnknownButNotKaiheiChannel ChannelBasic_MusicChannelType = 1
	ChannelBasic_UCanUSingMusicChannel      ChannelBasic_MusicChannelType = 2
	ChannelBasic_KtvMusicChannel            ChannelBasic_MusicChannelType = 3
	ChannelBasic_RapMusicChannel            ChannelBasic_MusicChannelType = 4
	ChannelBasic_ListenSongMusicChannel     ChannelBasic_MusicChannelType = 5
	ChannelBasic_SingAndChatMusicChannel    ChannelBasic_MusicChannelType = 6
)

var ChannelBasic_MusicChannelType_name = map[int32]string{
	0: "UnknownMusicChannelType",
	1: "UnknownButNotKaiheiChannel",
	2: "UCanUSingMusicChannel",
	3: "KtvMusicChannel",
	4: "RapMusicChannel",
	5: "ListenSongMusicChannel",
	6: "SingAndChatMusicChannel",
}
var ChannelBasic_MusicChannelType_value = map[string]int32{
	"UnknownMusicChannelType":    0,
	"UnknownButNotKaiheiChannel": 1,
	"UCanUSingMusicChannel":      2,
	"KtvMusicChannel":            3,
	"RapMusicChannel":            4,
	"ListenSongMusicChannel":     5,
	"SingAndChatMusicChannel":    6,
}

func (x ChannelBasic_MusicChannelType) String() string {
	return proto.EnumName(ChannelBasic_MusicChannelType_name, int32(x))
}
func (ChannelBasic_MusicChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{1, 0}
}

type ChannelSingBasic_Status int32

const (
	ChannelSingBasic_StatusNormal  ChannelSingBasic_Status = 0
	ChannelSingBasic_StatusDismiss ChannelSingBasic_Status = 1
)

var ChannelSingBasic_Status_name = map[int32]string{
	0: "StatusNormal",
	1: "StatusDismiss",
}
var ChannelSingBasic_Status_value = map[string]int32{
	"StatusNormal":  0,
	"StatusDismiss": 1,
}

func (x ChannelSingBasic_Status) String() string {
	return proto.EnumName(ChannelSingBasic_Status_name, int32(x))
}
func (ChannelSingBasic_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{21, 0}
}

type ChannelSingBasic_GameStatus int32

const (
	ChannelSingBasic_GameStatusNoStart ChannelSingBasic_GameStatus = 0
	ChannelSingBasic_GameStatusStart   ChannelSingBasic_GameStatus = 1
)

var ChannelSingBasic_GameStatus_name = map[int32]string{
	0: "GameStatusNoStart",
	1: "GameStatusStart",
}
var ChannelSingBasic_GameStatus_value = map[string]int32{
	"GameStatusNoStart": 0,
	"GameStatusStart":   1,
}

func (x ChannelSingBasic_GameStatus) String() string {
	return proto.EnumName(ChannelSingBasic_GameStatus_name, int32(x))
}
func (ChannelSingBasic_GameStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{21, 1}
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	BlockTitle           string   `protobuf:"bytes,3,opt,name=block_title,json=blockTitle,proto3" json:"block_title,omitempty"`
	ElementTitle         string   `protobuf:"bytes,4,opt,name=element_title,json=elementTitle,proto3" json:"element_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{0}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *BlockOption) GetBlockTitle() string {
	if m != nil {
		return m.BlockTitle
	}
	return ""
}

func (m *BlockOption) GetElementTitle() string {
	if m != nil {
		return m.ElementTitle
	}
	return ""
}

// 房间基本信息
type ChannelBasic struct {
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32               `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsAlive              uint32               `protobuf:"varint,3,opt,name=is_alive,json=isAlive,proto3" json:"is_alive,omitempty"`
	TagId                uint32               `protobuf:"varint,4,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string               `protobuf:"bytes,5,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	Tag_4Opt1            string               `protobuf:"bytes,6,opt,name=tag_4_opt1,json=tag4Opt1,proto3" json:"tag_4_opt1,omitempty"`
	Tag_4Opt2            string               `protobuf:"bytes,7,opt,name=tag_4_opt2,json=tag4Opt2,proto3" json:"tag_4_opt2,omitempty"`
	Tag_4Opt3            string               `protobuf:"bytes,8,opt,name=tag_4_opt3,json=tag4Opt3,proto3" json:"tag_4_opt3,omitempty"`
	Tag_4Opt4            string               `protobuf:"bytes,9,opt,name=tag_4_opt4,json=tag4Opt4,proto3" json:"tag_4_opt4,omitempty"`
	ChannelName          string               `protobuf:"bytes,10,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	PushTime             uint32               `protobuf:"varint,11,opt,name=push_time,json=pushTime,proto3" json:"push_time,omitempty"`
	BlockOptions         []*BlockOption       `protobuf:"bytes,12,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	WantFresh            bool                 `protobuf:"varint,13,opt,name=want_fresh,json=wantFresh,proto3" json:"want_fresh,omitempty"`
	Loc                  *common.LocationInfo `protobuf:"bytes,14,opt,name=loc,proto3" json:"loc,omitempty"`
	ChannelType          uint32               `protobuf:"varint,15,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelGameStatus    uint32               `protobuf:"varint,16,opt,name=channel_game_status,json=channelGameStatus,proto3" json:"channel_game_status,omitempty"`
	CompanionType        uint32               `protobuf:"varint,17,opt,name=companion_type,json=companionType,proto3" json:"companion_type,omitempty"`
	Sex                  uint32               `protobuf:"varint,18,opt,name=sex,proto3" json:"sex,omitempty"`
	MusicChannelType     uint32               `protobuf:"varint,19,opt,name=music_channel_type,json=musicChannelType,proto3" json:"music_channel_type,omitempty"`
	UcusTagId            uint32               `protobuf:"varint,20,opt,name=ucus_tag_id,json=ucusTagId,proto3" json:"ucus_tag_id,omitempty"`
	UcusTagName          string               `protobuf:"bytes,21,opt,name=ucus_tag_name,json=ucusTagName,proto3" json:"ucus_tag_name,omitempty"`
	BlockIdElemId        []string             `protobuf:"bytes,22,rep,name=blockId_elemId,json=blockIdElemId,proto3" json:"blockId_elemId,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChannelBasic) Reset()         { *m = ChannelBasic{} }
func (m *ChannelBasic) String() string { return proto.CompactTextString(m) }
func (*ChannelBasic) ProtoMessage()    {}
func (*ChannelBasic) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{1}
}
func (m *ChannelBasic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBasic.Unmarshal(m, b)
}
func (m *ChannelBasic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBasic.Marshal(b, m, deterministic)
}
func (dst *ChannelBasic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBasic.Merge(dst, src)
}
func (m *ChannelBasic) XXX_Size() int {
	return xxx_messageInfo_ChannelBasic.Size(m)
}
func (m *ChannelBasic) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBasic.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBasic proto.InternalMessageInfo

func (m *ChannelBasic) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelBasic) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelBasic) GetIsAlive() uint32 {
	if m != nil {
		return m.IsAlive
	}
	return 0
}

func (m *ChannelBasic) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelBasic) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *ChannelBasic) GetTag_4Opt1() string {
	if m != nil {
		return m.Tag_4Opt1
	}
	return ""
}

func (m *ChannelBasic) GetTag_4Opt2() string {
	if m != nil {
		return m.Tag_4Opt2
	}
	return ""
}

func (m *ChannelBasic) GetTag_4Opt3() string {
	if m != nil {
		return m.Tag_4Opt3
	}
	return ""
}

func (m *ChannelBasic) GetTag_4Opt4() string {
	if m != nil {
		return m.Tag_4Opt4
	}
	return ""
}

func (m *ChannelBasic) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelBasic) GetPushTime() uint32 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

func (m *ChannelBasic) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *ChannelBasic) GetWantFresh() bool {
	if m != nil {
		return m.WantFresh
	}
	return false
}

func (m *ChannelBasic) GetLoc() *common.LocationInfo {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *ChannelBasic) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelBasic) GetChannelGameStatus() uint32 {
	if m != nil {
		return m.ChannelGameStatus
	}
	return 0
}

func (m *ChannelBasic) GetCompanionType() uint32 {
	if m != nil {
		return m.CompanionType
	}
	return 0
}

func (m *ChannelBasic) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ChannelBasic) GetMusicChannelType() uint32 {
	if m != nil {
		return m.MusicChannelType
	}
	return 0
}

func (m *ChannelBasic) GetUcusTagId() uint32 {
	if m != nil {
		return m.UcusTagId
	}
	return 0
}

func (m *ChannelBasic) GetUcusTagName() string {
	if m != nil {
		return m.UcusTagName
	}
	return ""
}

func (m *ChannelBasic) GetBlockIdElemId() []string {
	if m != nil {
		return m.BlockIdElemId
	}
	return nil
}

type ChannelBasic_ChannelBasicUpdate struct {
	// option (rcmd.persona.process) = "index_es|150200200,channel_index_es_view";
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32               `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsAlive              uint32               `protobuf:"varint,3,opt,name=is_alive,json=isAlive,proto3" json:"is_alive,omitempty"`
	TagId                uint32               `protobuf:"varint,4,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string               `protobuf:"bytes,5,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	Tag_4Opt1            string               `protobuf:"bytes,6,opt,name=tag_4_opt1,json=tag4Opt1,proto3" json:"tag_4_opt1,omitempty"`
	Tag_4Opt2            string               `protobuf:"bytes,7,opt,name=tag_4_opt2,json=tag4Opt2,proto3" json:"tag_4_opt2,omitempty"`
	Tag_4Opt3            string               `protobuf:"bytes,8,opt,name=tag_4_opt3,json=tag4Opt3,proto3" json:"tag_4_opt3,omitempty"`
	Tag_4Opt4            string               `protobuf:"bytes,9,opt,name=tag_4_opt4,json=tag4Opt4,proto3" json:"tag_4_opt4,omitempty"`
	ChannelName          string               `protobuf:"bytes,10,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	PushTime             uint32               `protobuf:"varint,11,opt,name=push_time,json=pushTime,proto3" json:"push_time,omitempty"`
	BlockOptions         []*BlockOption       `protobuf:"bytes,12,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	WantFresh            bool                 `protobuf:"varint,13,opt,name=want_fresh,json=wantFresh,proto3" json:"want_fresh,omitempty"`
	Loc                  *common.LocationInfo `protobuf:"bytes,14,opt,name=loc,proto3" json:"loc,omitempty"`
	ChannelType          uint32               `protobuf:"varint,15,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Sex                  uint32               `protobuf:"varint,16,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChannelBasic_ChannelBasicUpdate) Reset()         { *m = ChannelBasic_ChannelBasicUpdate{} }
func (m *ChannelBasic_ChannelBasicUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelBasic_ChannelBasicUpdate) ProtoMessage()    {}
func (*ChannelBasic_ChannelBasicUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{1, 0}
}
func (m *ChannelBasic_ChannelBasicUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBasic_ChannelBasicUpdate.Unmarshal(m, b)
}
func (m *ChannelBasic_ChannelBasicUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBasic_ChannelBasicUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelBasic_ChannelBasicUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBasic_ChannelBasicUpdate.Merge(dst, src)
}
func (m *ChannelBasic_ChannelBasicUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelBasic_ChannelBasicUpdate.Size(m)
}
func (m *ChannelBasic_ChannelBasicUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBasic_ChannelBasicUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBasic_ChannelBasicUpdate proto.InternalMessageInfo

func (m *ChannelBasic_ChannelBasicUpdate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelBasic_ChannelBasicUpdate) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelBasic_ChannelBasicUpdate) GetIsAlive() uint32 {
	if m != nil {
		return m.IsAlive
	}
	return 0
}

func (m *ChannelBasic_ChannelBasicUpdate) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelBasic_ChannelBasicUpdate) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *ChannelBasic_ChannelBasicUpdate) GetTag_4Opt1() string {
	if m != nil {
		return m.Tag_4Opt1
	}
	return ""
}

func (m *ChannelBasic_ChannelBasicUpdate) GetTag_4Opt2() string {
	if m != nil {
		return m.Tag_4Opt2
	}
	return ""
}

func (m *ChannelBasic_ChannelBasicUpdate) GetTag_4Opt3() string {
	if m != nil {
		return m.Tag_4Opt3
	}
	return ""
}

func (m *ChannelBasic_ChannelBasicUpdate) GetTag_4Opt4() string {
	if m != nil {
		return m.Tag_4Opt4
	}
	return ""
}

func (m *ChannelBasic_ChannelBasicUpdate) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelBasic_ChannelBasicUpdate) GetPushTime() uint32 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

func (m *ChannelBasic_ChannelBasicUpdate) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *ChannelBasic_ChannelBasicUpdate) GetWantFresh() bool {
	if m != nil {
		return m.WantFresh
	}
	return false
}

func (m *ChannelBasic_ChannelBasicUpdate) GetLoc() *common.LocationInfo {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *ChannelBasic_ChannelBasicUpdate) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelBasic_ChannelBasicUpdate) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type ChannelBasic_ChannelGameStatusUpdate struct {
	ChannelGameStatus    uint32   `protobuf:"varint,1,opt,name=channel_game_status,json=channelGameStatus,proto3" json:"channel_game_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelBasic_ChannelGameStatusUpdate) Reset()         { *m = ChannelBasic_ChannelGameStatusUpdate{} }
func (m *ChannelBasic_ChannelGameStatusUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelBasic_ChannelGameStatusUpdate) ProtoMessage()    {}
func (*ChannelBasic_ChannelGameStatusUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{1, 1}
}
func (m *ChannelBasic_ChannelGameStatusUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBasic_ChannelGameStatusUpdate.Unmarshal(m, b)
}
func (m *ChannelBasic_ChannelGameStatusUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBasic_ChannelGameStatusUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelBasic_ChannelGameStatusUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBasic_ChannelGameStatusUpdate.Merge(dst, src)
}
func (m *ChannelBasic_ChannelGameStatusUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelBasic_ChannelGameStatusUpdate.Size(m)
}
func (m *ChannelBasic_ChannelGameStatusUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBasic_ChannelGameStatusUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBasic_ChannelGameStatusUpdate proto.InternalMessageInfo

func (m *ChannelBasic_ChannelGameStatusUpdate) GetChannelGameStatus() uint32 {
	if m != nil {
		return m.ChannelGameStatus
	}
	return 0
}

type ChannelBasic_ChannelCompanionTypeUpdate struct {
	CompanionType        uint32   `protobuf:"varint,1,opt,name=companion_type,json=companionType,proto3" json:"companion_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelBasic_ChannelCompanionTypeUpdate) Reset() {
	*m = ChannelBasic_ChannelCompanionTypeUpdate{}
}
func (m *ChannelBasic_ChannelCompanionTypeUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelBasic_ChannelCompanionTypeUpdate) ProtoMessage()    {}
func (*ChannelBasic_ChannelCompanionTypeUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{1, 2}
}
func (m *ChannelBasic_ChannelCompanionTypeUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBasic_ChannelCompanionTypeUpdate.Unmarshal(m, b)
}
func (m *ChannelBasic_ChannelCompanionTypeUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBasic_ChannelCompanionTypeUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelBasic_ChannelCompanionTypeUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBasic_ChannelCompanionTypeUpdate.Merge(dst, src)
}
func (m *ChannelBasic_ChannelCompanionTypeUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelBasic_ChannelCompanionTypeUpdate.Size(m)
}
func (m *ChannelBasic_ChannelCompanionTypeUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBasic_ChannelCompanionTypeUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBasic_ChannelCompanionTypeUpdate proto.InternalMessageInfo

func (m *ChannelBasic_ChannelCompanionTypeUpdate) GetCompanionType() uint32 {
	if m != nil {
		return m.CompanionType
	}
	return 0
}

type ChannelBasic_ChannelMusicChannelUpdate struct {
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32               `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TagId                uint32               `protobuf:"varint,4,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	PushTime             uint32               `protobuf:"varint,5,opt,name=push_time,json=pushTime,proto3" json:"push_time,omitempty"`
	BlockOptions         []*BlockOption       `protobuf:"bytes,6,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	Loc                  *common.LocationInfo `protobuf:"bytes,7,opt,name=loc,proto3" json:"loc,omitempty"`
	Sex                  uint32               `protobuf:"varint,8,opt,name=sex,proto3" json:"sex,omitempty"`
	MusicChannelType     uint32               `protobuf:"varint,9,opt,name=music_channel_type,json=musicChannelType,proto3" json:"music_channel_type,omitempty"`
	BlockIdElemId        []string             `protobuf:"bytes,10,rep,name=blockId_elemId,json=blockIdElemId,proto3" json:"blockId_elemId,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChannelBasic_ChannelMusicChannelUpdate) Reset() {
	*m = ChannelBasic_ChannelMusicChannelUpdate{}
}
func (m *ChannelBasic_ChannelMusicChannelUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelBasic_ChannelMusicChannelUpdate) ProtoMessage()    {}
func (*ChannelBasic_ChannelMusicChannelUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{1, 3}
}
func (m *ChannelBasic_ChannelMusicChannelUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBasic_ChannelMusicChannelUpdate.Unmarshal(m, b)
}
func (m *ChannelBasic_ChannelMusicChannelUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBasic_ChannelMusicChannelUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelBasic_ChannelMusicChannelUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBasic_ChannelMusicChannelUpdate.Merge(dst, src)
}
func (m *ChannelBasic_ChannelMusicChannelUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelBasic_ChannelMusicChannelUpdate.Size(m)
}
func (m *ChannelBasic_ChannelMusicChannelUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBasic_ChannelMusicChannelUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBasic_ChannelMusicChannelUpdate proto.InternalMessageInfo

func (m *ChannelBasic_ChannelMusicChannelUpdate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelBasic_ChannelMusicChannelUpdate) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelBasic_ChannelMusicChannelUpdate) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelBasic_ChannelMusicChannelUpdate) GetPushTime() uint32 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

func (m *ChannelBasic_ChannelMusicChannelUpdate) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *ChannelBasic_ChannelMusicChannelUpdate) GetLoc() *common.LocationInfo {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *ChannelBasic_ChannelMusicChannelUpdate) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ChannelBasic_ChannelMusicChannelUpdate) GetMusicChannelType() uint32 {
	if m != nil {
		return m.MusicChannelType
	}
	return 0
}

func (m *ChannelBasic_ChannelMusicChannelUpdate) GetBlockIdElemId() []string {
	if m != nil {
		return m.BlockIdElemId
	}
	return nil
}

type ChannelBasic_UcusTagUpdate struct {
	UcusTagId            uint32   `protobuf:"varint,1,opt,name=ucus_tag_id,json=ucusTagId,proto3" json:"ucus_tag_id,omitempty"`
	UcusTagName          string   `protobuf:"bytes,2,opt,name=ucus_tag_name,json=ucusTagName,proto3" json:"ucus_tag_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelBasic_UcusTagUpdate) Reset()         { *m = ChannelBasic_UcusTagUpdate{} }
func (m *ChannelBasic_UcusTagUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelBasic_UcusTagUpdate) ProtoMessage()    {}
func (*ChannelBasic_UcusTagUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{1, 4}
}
func (m *ChannelBasic_UcusTagUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBasic_UcusTagUpdate.Unmarshal(m, b)
}
func (m *ChannelBasic_UcusTagUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBasic_UcusTagUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelBasic_UcusTagUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBasic_UcusTagUpdate.Merge(dst, src)
}
func (m *ChannelBasic_UcusTagUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelBasic_UcusTagUpdate.Size(m)
}
func (m *ChannelBasic_UcusTagUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBasic_UcusTagUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBasic_UcusTagUpdate proto.InternalMessageInfo

func (m *ChannelBasic_UcusTagUpdate) GetUcusTagId() uint32 {
	if m != nil {
		return m.UcusTagId
	}
	return 0
}

func (m *ChannelBasic_UcusTagUpdate) GetUcusTagName() string {
	if m != nil {
		return m.UcusTagName
	}
	return ""
}

// 房间动态数据
type ChannelDynamic struct {
	UserNum                  uint32   `protobuf:"varint,1,opt,name=user_num,json=userNum,proto3" json:"user_num,omitempty"`
	EnterFemaleNum           uint32   `protobuf:"varint,2,opt,name=enter_female_num,json=enterFemaleNum,proto3" json:"enter_female_num,omitempty"`
	MicNum                   uint32   `protobuf:"varint,3,opt,name=mic_num,json=micNum,proto3" json:"mic_num,omitempty"`
	MicFemaleNum             uint32   `protobuf:"varint,4,opt,name=mic_female_num,json=micFemaleNum,proto3" json:"mic_female_num,omitempty"`
	LastFixTime              uint32   `protobuf:"varint,9,opt,name=last_fix_time,json=lastFixTime,proto3" json:"last_fix_time,omitempty"`
	LastEnterTime            uint32   `protobuf:"varint,10,opt,name=last_enter_time,json=lastEnterTime,proto3" json:"last_enter_time,omitempty"`
	MicAgeSum                uint32   `protobuf:"varint,11,opt,name=mic_age_sum,json=micAgeSum,proto3" json:"mic_age_sum,omitempty"`
	MicNoAgeUserNum          uint32   `protobuf:"varint,12,opt,name=mic_no_age_user_num,json=micNoAgeUserNum,proto3" json:"mic_no_age_user_num,omitempty"`
	SongList                 []string `protobuf:"bytes,13,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	MicMaleNum               uint32   `protobuf:"varint,14,opt,name=mic_male_num,json=micMaleNum,proto3" json:"mic_male_num,omitempty"`
	HighQuality              bool     `protobuf:"varint,15,opt,name=high_quality,json=highQuality,proto3" json:"high_quality,omitempty"`
	HqSetTime                uint32   `protobuf:"varint,16,opt,name=hq_set_time,json=hqSetTime,proto3" json:"hq_set_time,omitempty"`
	KtvExcellentProducerNum  int32    `protobuf:"varint,17,opt,name=ktv_excellent_producer_num,json=ktvExcellentProducerNum,proto3" json:"ktv_excellent_producer_num,omitempty"`
	KtvSocialKingNum         int32    `protobuf:"varint,19,opt,name=ktv_social_king_num,json=ktvSocialKingNum,proto3" json:"ktv_social_king_num,omitempty"`
	KtvContentCosumerNum     int32    `protobuf:"varint,20,opt,name=ktv_content_cosumer_num,json=ktvContentCosumerNum,proto3" json:"ktv_content_cosumer_num,omitempty"`
	KtvImpreciseUserNum      int32    `protobuf:"varint,21,opt,name=ktv_imprecise_user_num,json=ktvImpreciseUserNum,proto3" json:"ktv_imprecise_user_num,omitempty"`
	UcusExcellentProducerNum int32    `protobuf:"varint,22,opt,name=ucus_excellent_producer_num,json=ucusExcellentProducerNum,proto3" json:"ucus_excellent_producer_num,omitempty"`
	UcusSocialKingNum        int32    `protobuf:"varint,23,opt,name=ucus_social_king_num,json=ucusSocialKingNum,proto3" json:"ucus_social_king_num,omitempty"`
	UcusContentCosumerNum    int32    `protobuf:"varint,24,opt,name=ucus_content_cosumer_num,json=ucusContentCosumerNum,proto3" json:"ucus_content_cosumer_num,omitempty"`
	UcusImpreciseUserNum     int32    `protobuf:"varint,25,opt,name=ucus_imprecise_user_num,json=ucusImpreciseUserNum,proto3" json:"ucus_imprecise_user_num,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *ChannelDynamic) Reset()         { *m = ChannelDynamic{} }
func (m *ChannelDynamic) String() string { return proto.CompactTextString(m) }
func (*ChannelDynamic) ProtoMessage()    {}
func (*ChannelDynamic) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{2}
}
func (m *ChannelDynamic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelDynamic.Unmarshal(m, b)
}
func (m *ChannelDynamic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelDynamic.Marshal(b, m, deterministic)
}
func (dst *ChannelDynamic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelDynamic.Merge(dst, src)
}
func (m *ChannelDynamic) XXX_Size() int {
	return xxx_messageInfo_ChannelDynamic.Size(m)
}
func (m *ChannelDynamic) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelDynamic.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelDynamic proto.InternalMessageInfo

func (m *ChannelDynamic) GetUserNum() uint32 {
	if m != nil {
		return m.UserNum
	}
	return 0
}

func (m *ChannelDynamic) GetEnterFemaleNum() uint32 {
	if m != nil {
		return m.EnterFemaleNum
	}
	return 0
}

func (m *ChannelDynamic) GetMicNum() uint32 {
	if m != nil {
		return m.MicNum
	}
	return 0
}

func (m *ChannelDynamic) GetMicFemaleNum() uint32 {
	if m != nil {
		return m.MicFemaleNum
	}
	return 0
}

func (m *ChannelDynamic) GetLastFixTime() uint32 {
	if m != nil {
		return m.LastFixTime
	}
	return 0
}

func (m *ChannelDynamic) GetLastEnterTime() uint32 {
	if m != nil {
		return m.LastEnterTime
	}
	return 0
}

func (m *ChannelDynamic) GetMicAgeSum() uint32 {
	if m != nil {
		return m.MicAgeSum
	}
	return 0
}

func (m *ChannelDynamic) GetMicNoAgeUserNum() uint32 {
	if m != nil {
		return m.MicNoAgeUserNum
	}
	return 0
}

func (m *ChannelDynamic) GetSongList() []string {
	if m != nil {
		return m.SongList
	}
	return nil
}

func (m *ChannelDynamic) GetMicMaleNum() uint32 {
	if m != nil {
		return m.MicMaleNum
	}
	return 0
}

func (m *ChannelDynamic) GetHighQuality() bool {
	if m != nil {
		return m.HighQuality
	}
	return false
}

func (m *ChannelDynamic) GetHqSetTime() uint32 {
	if m != nil {
		return m.HqSetTime
	}
	return 0
}

func (m *ChannelDynamic) GetKtvExcellentProducerNum() int32 {
	if m != nil {
		return m.KtvExcellentProducerNum
	}
	return 0
}

func (m *ChannelDynamic) GetKtvSocialKingNum() int32 {
	if m != nil {
		return m.KtvSocialKingNum
	}
	return 0
}

func (m *ChannelDynamic) GetKtvContentCosumerNum() int32 {
	if m != nil {
		return m.KtvContentCosumerNum
	}
	return 0
}

func (m *ChannelDynamic) GetKtvImpreciseUserNum() int32 {
	if m != nil {
		return m.KtvImpreciseUserNum
	}
	return 0
}

func (m *ChannelDynamic) GetUcusExcellentProducerNum() int32 {
	if m != nil {
		return m.UcusExcellentProducerNum
	}
	return 0
}

func (m *ChannelDynamic) GetUcusSocialKingNum() int32 {
	if m != nil {
		return m.UcusSocialKingNum
	}
	return 0
}

func (m *ChannelDynamic) GetUcusContentCosumerNum() int32 {
	if m != nil {
		return m.UcusContentCosumerNum
	}
	return 0
}

func (m *ChannelDynamic) GetUcusImpreciseUserNum() int32 {
	if m != nil {
		return m.UcusImpreciseUserNum
	}
	return 0
}

type ChannelDynamic_SongListUpdate struct {
	SongList             []string `protobuf:"bytes,1,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelDynamic_SongListUpdate) Reset()         { *m = ChannelDynamic_SongListUpdate{} }
func (m *ChannelDynamic_SongListUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelDynamic_SongListUpdate) ProtoMessage()    {}
func (*ChannelDynamic_SongListUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{2, 0}
}
func (m *ChannelDynamic_SongListUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelDynamic_SongListUpdate.Unmarshal(m, b)
}
func (m *ChannelDynamic_SongListUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelDynamic_SongListUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelDynamic_SongListUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelDynamic_SongListUpdate.Merge(dst, src)
}
func (m *ChannelDynamic_SongListUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelDynamic_SongListUpdate.Size(m)
}
func (m *ChannelDynamic_SongListUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelDynamic_SongListUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelDynamic_SongListUpdate proto.InternalMessageInfo

func (m *ChannelDynamic_SongListUpdate) GetSongList() []string {
	if m != nil {
		return m.SongList
	}
	return nil
}

type ChannelDynamic_HighQualityUpdate struct {
	HighQuality          bool     `protobuf:"varint,1,opt,name=high_quality,json=highQuality,proto3" json:"high_quality,omitempty"`
	HqSetTime            uint32   `protobuf:"varint,2,opt,name=hq_set_time,json=hqSetTime,proto3" json:"hq_set_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelDynamic_HighQualityUpdate) Reset()         { *m = ChannelDynamic_HighQualityUpdate{} }
func (m *ChannelDynamic_HighQualityUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelDynamic_HighQualityUpdate) ProtoMessage()    {}
func (*ChannelDynamic_HighQualityUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{2, 1}
}
func (m *ChannelDynamic_HighQualityUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelDynamic_HighQualityUpdate.Unmarshal(m, b)
}
func (m *ChannelDynamic_HighQualityUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelDynamic_HighQualityUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelDynamic_HighQualityUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelDynamic_HighQualityUpdate.Merge(dst, src)
}
func (m *ChannelDynamic_HighQualityUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelDynamic_HighQualityUpdate.Size(m)
}
func (m *ChannelDynamic_HighQualityUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelDynamic_HighQualityUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelDynamic_HighQualityUpdate proto.InternalMessageInfo

func (m *ChannelDynamic_HighQualityUpdate) GetHighQuality() bool {
	if m != nil {
		return m.HighQuality
	}
	return false
}

func (m *ChannelDynamic_HighQualityUpdate) GetHqSetTime() uint32 {
	if m != nil {
		return m.HqSetTime
	}
	return 0
}

// 查询房间小队情况
type ChannelTeam struct {
	OwnerUid             uint32            `protobuf:"varint,1,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	TabId                uint32            `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Location2Uid         map[uint32]uint32 `protobuf:"bytes,3,rep,name=location2uid,proto3" json:"location2uid,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	Uid2Location         map[uint32]uint32 `protobuf:"bytes,4,rep,name=uid2location,proto3" json:"uid2location,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	LackNum              uint32            `protobuf:"varint,5,opt,name=lack_num,json=lackNum,proto3" json:"lack_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChannelTeam) Reset()         { *m = ChannelTeam{} }
func (m *ChannelTeam) String() string { return proto.CompactTextString(m) }
func (*ChannelTeam) ProtoMessage()    {}
func (*ChannelTeam) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{3}
}
func (m *ChannelTeam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTeam.Unmarshal(m, b)
}
func (m *ChannelTeam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTeam.Marshal(b, m, deterministic)
}
func (dst *ChannelTeam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTeam.Merge(dst, src)
}
func (m *ChannelTeam) XXX_Size() int {
	return xxx_messageInfo_ChannelTeam.Size(m)
}
func (m *ChannelTeam) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTeam.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTeam proto.InternalMessageInfo

func (m *ChannelTeam) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *ChannelTeam) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ChannelTeam) GetLocation2Uid() map[uint32]uint32 {
	if m != nil {
		return m.Location2Uid
	}
	return nil
}

func (m *ChannelTeam) GetUid2Location() map[uint32]uint32 {
	if m != nil {
		return m.Uid2Location
	}
	return nil
}

func (m *ChannelTeam) GetLackNum() uint32 {
	if m != nil {
		return m.LackNum
	}
	return 0
}

type ChannelExpDetail struct {
	BrowseCount          uint32   `protobuf:"varint,1,opt,name=browse_count,json=browseCount,proto3" json:"browse_count,omitempty"`
	ClickCount           uint32   `protobuf:"varint,2,opt,name=click_count,json=clickCount,proto3" json:"click_count,omitempty"`
	ClickRate            float64  `protobuf:"fixed64,3,opt,name=click_rate,json=clickRate,proto3" json:"click_rate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelExpDetail) Reset()         { *m = ChannelExpDetail{} }
func (m *ChannelExpDetail) String() string { return proto.CompactTextString(m) }
func (*ChannelExpDetail) ProtoMessage()    {}
func (*ChannelExpDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{4}
}
func (m *ChannelExpDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelExpDetail.Unmarshal(m, b)
}
func (m *ChannelExpDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelExpDetail.Marshal(b, m, deterministic)
}
func (dst *ChannelExpDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelExpDetail.Merge(dst, src)
}
func (m *ChannelExpDetail) XXX_Size() int {
	return xxx_messageInfo_ChannelExpDetail.Size(m)
}
func (m *ChannelExpDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelExpDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelExpDetail proto.InternalMessageInfo

func (m *ChannelExpDetail) GetBrowseCount() uint32 {
	if m != nil {
		return m.BrowseCount
	}
	return 0
}

func (m *ChannelExpDetail) GetClickCount() uint32 {
	if m != nil {
		return m.ClickCount
	}
	return 0
}

func (m *ChannelExpDetail) GetClickRate() float64 {
	if m != nil {
		return m.ClickRate
	}
	return 0
}

type ChannelEnterDetail struct {
	EnterCount           uint32   `protobuf:"varint,1,opt,name=enter_count,json=enterCount,proto3" json:"enter_count,omitempty"`
	AvgDuration          float64  `protobuf:"fixed64,2,opt,name=avg_duration,json=avgDuration,proto3" json:"avg_duration,omitempty"`
	TeamRate             float64  `protobuf:"fixed64,3,opt,name=team_rate,json=teamRate,proto3" json:"team_rate,omitempty"`
	OutRate              float64  `protobuf:"fixed64,4,opt,name=out_rate,json=outRate,proto3" json:"out_rate,omitempty"`
	MicRate              float64  `protobuf:"fixed64,5,opt,name=mic_rate,json=micRate,proto3" json:"mic_rate,omitempty"`
	SumDuration          float64  `protobuf:"fixed64,6,opt,name=sum_duration,json=sumDuration,proto3" json:"sum_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelEnterDetail) Reset()         { *m = ChannelEnterDetail{} }
func (m *ChannelEnterDetail) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterDetail) ProtoMessage()    {}
func (*ChannelEnterDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{5}
}
func (m *ChannelEnterDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterDetail.Unmarshal(m, b)
}
func (m *ChannelEnterDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterDetail.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterDetail.Merge(dst, src)
}
func (m *ChannelEnterDetail) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterDetail.Size(m)
}
func (m *ChannelEnterDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterDetail proto.InternalMessageInfo

func (m *ChannelEnterDetail) GetEnterCount() uint32 {
	if m != nil {
		return m.EnterCount
	}
	return 0
}

func (m *ChannelEnterDetail) GetAvgDuration() float64 {
	if m != nil {
		return m.AvgDuration
	}
	return 0
}

func (m *ChannelEnterDetail) GetTeamRate() float64 {
	if m != nil {
		return m.TeamRate
	}
	return 0
}

func (m *ChannelEnterDetail) GetOutRate() float64 {
	if m != nil {
		return m.OutRate
	}
	return 0
}

func (m *ChannelEnterDetail) GetMicRate() float64 {
	if m != nil {
		return m.MicRate
	}
	return 0
}

func (m *ChannelEnterDetail) GetSumDuration() float64 {
	if m != nil {
		return m.SumDuration
	}
	return 0
}

// 离线统计数据
type ChannelOffline struct {
	TEnterCount          uint32                         `protobuf:"varint,1,opt,name=t_enter_count,json=tEnterCount,proto3" json:"t_enter_count,omitempty"`
	TEnterUserCount      uint32                         `protobuf:"varint,2,opt,name=t_enter_user_count,json=tEnterUserCount,proto3" json:"t_enter_user_count,omitempty"`
	TTotalDuration       float64                        `protobuf:"fixed64,3,opt,name=t_total_duration,json=tTotalDuration,proto3" json:"t_total_duration,omitempty"`
	TAvgDuration         float64                        `protobuf:"fixed64,4,opt,name=t_avg_duration,json=tAvgDuration,proto3" json:"t_avg_duration,omitempty"`
	TTeamRate            float64                        `protobuf:"fixed64,5,opt,name=t_team_rate,json=tTeamRate,proto3" json:"t_team_rate,omitempty"`
	TMicRate             float64                        `protobuf:"fixed64,6,opt,name=t_mic_rate,json=tMicRate,proto3" json:"t_mic_rate,omitempty"`
	TOutRate             float64                        `protobuf:"fixed64,7,opt,name=t_out_rate,json=tOutRate,proto3" json:"t_out_rate,omitempty"`
	TFollowRate          float64                        `protobuf:"fixed64,8,opt,name=t_follow_rate,json=tFollowRate,proto3" json:"t_follow_rate,omitempty"`
	TUAvgDuration        float64                        `protobuf:"fixed64,9,opt,name=t_u_avg_duration,json=tUAvgDuration,proto3" json:"t_u_avg_duration,omitempty"`
	TUTeamRate           float64                        `protobuf:"fixed64,10,opt,name=t_u_team_rate,json=tUTeamRate,proto3" json:"t_u_team_rate,omitempty"`
	TUMicRate            float64                        `protobuf:"fixed64,11,opt,name=t_u_mic_rate,json=tUMicRate,proto3" json:"t_u_mic_rate,omitempty"`
	TUOutRate            float64                        `protobuf:"fixed64,12,opt,name=t_u_out_rate,json=tUOutRate,proto3" json:"t_u_out_rate,omitempty"`
	TUFollowRate         float64                        `protobuf:"fixed64,13,opt,name=t_u_follow_rate,json=tUFollowRate,proto3" json:"t_u_follow_rate,omitempty"`
	TBrowseCount         uint32                         `protobuf:"varint,14,opt,name=t_browse_count,json=tBrowseCount,proto3" json:"t_browse_count,omitempty"`
	TClickCount          uint32                         `protobuf:"varint,15,opt,name=t_click_count,json=tClickCount,proto3" json:"t_click_count,omitempty"`
	TClickRate           float64                        `protobuf:"fixed64,16,opt,name=t_click_rate,json=tClickRate,proto3" json:"t_click_rate,omitempty"`
	TExpDSex             map[uint32]*ChannelExpDetail   `protobuf:"bytes,17,rep,name=t_exp_d_sex,json=tExpDSex,proto3" json:"t_exp_d_sex,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TExpDTagId           map[uint32]*ChannelExpDetail   `protobuf:"bytes,18,rep,name=t_exp_d_tag_id,json=tExpDTagId,proto3" json:"t_exp_d_tag_id,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TEnterDSex           map[uint32]*ChannelEnterDetail `protobuf:"bytes,19,rep,name=t_enter_d_sex,json=tEnterDSex,proto3" json:"t_enter_d_sex,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	TEnterDTagId         map[uint32]*ChannelEnterDetail `protobuf:"bytes,20,rep,name=t_enter_d_tag_id,json=tEnterDTagId,proto3" json:"t_enter_d_tag_id,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *ChannelOffline) Reset()         { *m = ChannelOffline{} }
func (m *ChannelOffline) String() string { return proto.CompactTextString(m) }
func (*ChannelOffline) ProtoMessage()    {}
func (*ChannelOffline) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{6}
}
func (m *ChannelOffline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelOffline.Unmarshal(m, b)
}
func (m *ChannelOffline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelOffline.Marshal(b, m, deterministic)
}
func (dst *ChannelOffline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelOffline.Merge(dst, src)
}
func (m *ChannelOffline) XXX_Size() int {
	return xxx_messageInfo_ChannelOffline.Size(m)
}
func (m *ChannelOffline) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelOffline.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelOffline proto.InternalMessageInfo

func (m *ChannelOffline) GetTEnterCount() uint32 {
	if m != nil {
		return m.TEnterCount
	}
	return 0
}

func (m *ChannelOffline) GetTEnterUserCount() uint32 {
	if m != nil {
		return m.TEnterUserCount
	}
	return 0
}

func (m *ChannelOffline) GetTTotalDuration() float64 {
	if m != nil {
		return m.TTotalDuration
	}
	return 0
}

func (m *ChannelOffline) GetTAvgDuration() float64 {
	if m != nil {
		return m.TAvgDuration
	}
	return 0
}

func (m *ChannelOffline) GetTTeamRate() float64 {
	if m != nil {
		return m.TTeamRate
	}
	return 0
}

func (m *ChannelOffline) GetTMicRate() float64 {
	if m != nil {
		return m.TMicRate
	}
	return 0
}

func (m *ChannelOffline) GetTOutRate() float64 {
	if m != nil {
		return m.TOutRate
	}
	return 0
}

func (m *ChannelOffline) GetTFollowRate() float64 {
	if m != nil {
		return m.TFollowRate
	}
	return 0
}

func (m *ChannelOffline) GetTUAvgDuration() float64 {
	if m != nil {
		return m.TUAvgDuration
	}
	return 0
}

func (m *ChannelOffline) GetTUTeamRate() float64 {
	if m != nil {
		return m.TUTeamRate
	}
	return 0
}

func (m *ChannelOffline) GetTUMicRate() float64 {
	if m != nil {
		return m.TUMicRate
	}
	return 0
}

func (m *ChannelOffline) GetTUOutRate() float64 {
	if m != nil {
		return m.TUOutRate
	}
	return 0
}

func (m *ChannelOffline) GetTUFollowRate() float64 {
	if m != nil {
		return m.TUFollowRate
	}
	return 0
}

func (m *ChannelOffline) GetTBrowseCount() uint32 {
	if m != nil {
		return m.TBrowseCount
	}
	return 0
}

func (m *ChannelOffline) GetTClickCount() uint32 {
	if m != nil {
		return m.TClickCount
	}
	return 0
}

func (m *ChannelOffline) GetTClickRate() float64 {
	if m != nil {
		return m.TClickRate
	}
	return 0
}

func (m *ChannelOffline) GetTExpDSex() map[uint32]*ChannelExpDetail {
	if m != nil {
		return m.TExpDSex
	}
	return nil
}

func (m *ChannelOffline) GetTExpDTagId() map[uint32]*ChannelExpDetail {
	if m != nil {
		return m.TExpDTagId
	}
	return nil
}

func (m *ChannelOffline) GetTEnterDSex() map[uint32]*ChannelEnterDetail {
	if m != nil {
		return m.TEnterDSex
	}
	return nil
}

func (m *ChannelOffline) GetTEnterDTagId() map[uint32]*ChannelEnterDetail {
	if m != nil {
		return m.TEnterDTagId
	}
	return nil
}

type Nothing struct {
	Nothing              uint32   `protobuf:"varint,1,opt,name=nothing,proto3" json:"nothing,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Nothing) Reset()         { *m = Nothing{} }
func (m *Nothing) String() string { return proto.CompactTextString(m) }
func (*Nothing) ProtoMessage()    {}
func (*Nothing) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{7}
}
func (m *Nothing) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Nothing.Unmarshal(m, b)
}
func (m *Nothing) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Nothing.Marshal(b, m, deterministic)
}
func (dst *Nothing) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Nothing.Merge(dst, src)
}
func (m *Nothing) XXX_Size() int {
	return xxx_messageInfo_Nothing.Size(m)
}
func (m *Nothing) XXX_DiscardUnknown() {
	xxx_messageInfo_Nothing.DiscardUnknown(m)
}

var xxx_messageInfo_Nothing proto.InternalMessageInfo

func (m *Nothing) GetNothing() uint32 {
	if m != nil {
		return m.Nothing
	}
	return 0
}

type ChannelTabIdDetail struct {
	ClickRate            float64  `protobuf:"fixed64,1,opt,name=click_rate,json=clickRate,proto3" json:"click_rate,omitempty"`
	TeamRate             float64  `protobuf:"fixed64,2,opt,name=team_rate,json=teamRate,proto3" json:"team_rate,omitempty"`
	ExpCnt               uint32   `protobuf:"varint,3,opt,name=exp_cnt,json=expCnt,proto3" json:"exp_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelTabIdDetail) Reset()         { *m = ChannelTabIdDetail{} }
func (m *ChannelTabIdDetail) String() string { return proto.CompactTextString(m) }
func (*ChannelTabIdDetail) ProtoMessage()    {}
func (*ChannelTabIdDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{8}
}
func (m *ChannelTabIdDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTabIdDetail.Unmarshal(m, b)
}
func (m *ChannelTabIdDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTabIdDetail.Marshal(b, m, deterministic)
}
func (dst *ChannelTabIdDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTabIdDetail.Merge(dst, src)
}
func (m *ChannelTabIdDetail) XXX_Size() int {
	return xxx_messageInfo_ChannelTabIdDetail.Size(m)
}
func (m *ChannelTabIdDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTabIdDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTabIdDetail proto.InternalMessageInfo

func (m *ChannelTabIdDetail) GetClickRate() float64 {
	if m != nil {
		return m.ClickRate
	}
	return 0
}

func (m *ChannelTabIdDetail) GetTeamRate() float64 {
	if m != nil {
		return m.TeamRate
	}
	return 0
}

func (m *ChannelTabIdDetail) GetExpCnt() uint32 {
	if m != nil {
		return m.ExpCnt
	}
	return 0
}

type GameSimilar struct {
	GameSimilar          map[string]float64 `protobuf:"bytes,1,rep,name=game_similar,json=gameSimilar,proto3" json:"game_similar,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GameSimilar) Reset()         { *m = GameSimilar{} }
func (m *GameSimilar) String() string { return proto.CompactTextString(m) }
func (*GameSimilar) ProtoMessage()    {}
func (*GameSimilar) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{9}
}
func (m *GameSimilar) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameSimilar.Unmarshal(m, b)
}
func (m *GameSimilar) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameSimilar.Marshal(b, m, deterministic)
}
func (dst *GameSimilar) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameSimilar.Merge(dst, src)
}
func (m *GameSimilar) XXX_Size() int {
	return xxx_messageInfo_GameSimilar.Size(m)
}
func (m *GameSimilar) XXX_DiscardUnknown() {
	xxx_messageInfo_GameSimilar.DiscardUnknown(m)
}

var xxx_messageInfo_GameSimilar proto.InternalMessageInfo

func (m *GameSimilar) GetGameSimilar() map[string]float64 {
	if m != nil {
		return m.GameSimilar
	}
	return nil
}

type ChannelTabId struct {
	TagIdTrans           map[uint32]*ChannelTabIdDetail `protobuf:"bytes,1,rep,name=tag_id_trans,json=tagIdTrans,proto3" json:"tag_id_trans,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	GameSimilarScore     map[string]*GameSimilar        `protobuf:"bytes,2,rep,name=game_similar_score,json=gameSimilarScore,proto3" json:"game_similar_score,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *ChannelTabId) Reset()         { *m = ChannelTabId{} }
func (m *ChannelTabId) String() string { return proto.CompactTextString(m) }
func (*ChannelTabId) ProtoMessage()    {}
func (*ChannelTabId) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{10}
}
func (m *ChannelTabId) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTabId.Unmarshal(m, b)
}
func (m *ChannelTabId) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTabId.Marshal(b, m, deterministic)
}
func (dst *ChannelTabId) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTabId.Merge(dst, src)
}
func (m *ChannelTabId) XXX_Size() int {
	return xxx_messageInfo_ChannelTabId.Size(m)
}
func (m *ChannelTabId) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTabId.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTabId proto.InternalMessageInfo

func (m *ChannelTabId) GetTagIdTrans() map[uint32]*ChannelTabIdDetail {
	if m != nil {
		return m.TagIdTrans
	}
	return nil
}

func (m *ChannelTabId) GetGameSimilarScore() map[string]*GameSimilar {
	if m != nil {
		return m.GameSimilarScore
	}
	return nil
}

// -- 实时特征 ----
// 近3分钟的房间转化数据
type ChannelEnterRealTransMinute3 struct {
	TransDetail          *ChannelEnterRealTrans `protobuf:"bytes,1,opt,name=trans_detail,json=transDetail,proto3" json:"trans_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ChannelEnterRealTransMinute3) Reset()         { *m = ChannelEnterRealTransMinute3{} }
func (m *ChannelEnterRealTransMinute3) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterRealTransMinute3) ProtoMessage()    {}
func (*ChannelEnterRealTransMinute3) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{11}
}
func (m *ChannelEnterRealTransMinute3) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterRealTransMinute3.Unmarshal(m, b)
}
func (m *ChannelEnterRealTransMinute3) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterRealTransMinute3.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterRealTransMinute3) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterRealTransMinute3.Merge(dst, src)
}
func (m *ChannelEnterRealTransMinute3) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterRealTransMinute3.Size(m)
}
func (m *ChannelEnterRealTransMinute3) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterRealTransMinute3.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterRealTransMinute3 proto.InternalMessageInfo

func (m *ChannelEnterRealTransMinute3) GetTransDetail() *ChannelEnterRealTrans {
	if m != nil {
		return m.TransDetail
	}
	return nil
}

// 近10分钟的房间转化数据
type ChannelEnterRealTransMinute10 struct {
	TransDetail          *ChannelEnterRealTrans `protobuf:"bytes,1,opt,name=trans_detail,json=transDetail,proto3" json:"trans_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ChannelEnterRealTransMinute10) Reset()         { *m = ChannelEnterRealTransMinute10{} }
func (m *ChannelEnterRealTransMinute10) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterRealTransMinute10) ProtoMessage()    {}
func (*ChannelEnterRealTransMinute10) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{12}
}
func (m *ChannelEnterRealTransMinute10) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterRealTransMinute10.Unmarshal(m, b)
}
func (m *ChannelEnterRealTransMinute10) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterRealTransMinute10.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterRealTransMinute10) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterRealTransMinute10.Merge(dst, src)
}
func (m *ChannelEnterRealTransMinute10) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterRealTransMinute10.Size(m)
}
func (m *ChannelEnterRealTransMinute10) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterRealTransMinute10.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterRealTransMinute10 proto.InternalMessageInfo

func (m *ChannelEnterRealTransMinute10) GetTransDetail() *ChannelEnterRealTrans {
	if m != nil {
		return m.TransDetail
	}
	return nil
}

// 房间特征
type ChannelEnterRealTrans struct {
	EnterCount           uint32   `protobuf:"varint,1,opt,name=enter_count,json=enterCount,proto3" json:"enter_count,omitempty"`
	TotalDuration        uint32   `protobuf:"varint,2,opt,name=total_duration,json=totalDuration,proto3" json:"total_duration,omitempty"`
	TeamCount            uint32   `protobuf:"varint,3,opt,name=team_count,json=teamCount,proto3" json:"team_count,omitempty"`
	OutLt20SCount        uint32   `protobuf:"varint,4,opt,name=out_lt20s_count,json=outLt20sCount,proto3" json:"out_lt20s_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelEnterRealTrans) Reset()         { *m = ChannelEnterRealTrans{} }
func (m *ChannelEnterRealTrans) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterRealTrans) ProtoMessage()    {}
func (*ChannelEnterRealTrans) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{13}
}
func (m *ChannelEnterRealTrans) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterRealTrans.Unmarshal(m, b)
}
func (m *ChannelEnterRealTrans) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterRealTrans.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterRealTrans) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterRealTrans.Merge(dst, src)
}
func (m *ChannelEnterRealTrans) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterRealTrans.Size(m)
}
func (m *ChannelEnterRealTrans) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterRealTrans.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterRealTrans proto.InternalMessageInfo

func (m *ChannelEnterRealTrans) GetEnterCount() uint32 {
	if m != nil {
		return m.EnterCount
	}
	return 0
}

func (m *ChannelEnterRealTrans) GetTotalDuration() uint32 {
	if m != nil {
		return m.TotalDuration
	}
	return 0
}

func (m *ChannelEnterRealTrans) GetTeamCount() uint32 {
	if m != nil {
		return m.TeamCount
	}
	return 0
}

func (m *ChannelEnterRealTrans) GetOutLt20SCount() uint32 {
	if m != nil {
		return m.OutLt20SCount
	}
	return 0
}

// 直播房基本信息（开播不会重置，永久不过期）
type ChannelLive struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TagId                string   `protobuf:"bytes,3,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string   `protobuf:"bytes,4,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	Level                string   `protobuf:"bytes,5,opt,name=level,proto3" json:"level,omitempty"`
	LevelType            string   `protobuf:"bytes,6,opt,name=level_type,json=levelType,proto3" json:"level_type,omitempty"`
	LevelChangeType      string   `protobuf:"bytes,7,opt,name=level_change_type,json=levelChangeType,proto3" json:"level_change_type,omitempty"`
	BindType             string   `protobuf:"bytes,8,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	CreateTime           uint32   `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	BroadcastStatus      uint32   `protobuf:"varint,10,opt,name=broadcast_status,json=broadcastStatus,proto3" json:"broadcast_status,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,11,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	BroadcastStartTime   uint32   `protobuf:"varint,12,opt,name=broadcast_start_time,json=broadcastStartTime,proto3" json:"broadcast_start_time,omitempty"`
	BroadcastEndTime     uint32   `protobuf:"varint,13,opt,name=broadcast_end_time,json=broadcastEndTime,proto3" json:"broadcast_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLive) Reset()         { *m = ChannelLive{} }
func (m *ChannelLive) String() string { return proto.CompactTextString(m) }
func (*ChannelLive) ProtoMessage()    {}
func (*ChannelLive) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{14}
}
func (m *ChannelLive) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLive.Unmarshal(m, b)
}
func (m *ChannelLive) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLive.Marshal(b, m, deterministic)
}
func (dst *ChannelLive) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLive.Merge(dst, src)
}
func (m *ChannelLive) XXX_Size() int {
	return xxx_messageInfo_ChannelLive.Size(m)
}
func (m *ChannelLive) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLive.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLive proto.InternalMessageInfo

func (m *ChannelLive) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelLive) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelLive) GetTagId() string {
	if m != nil {
		return m.TagId
	}
	return ""
}

func (m *ChannelLive) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *ChannelLive) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

func (m *ChannelLive) GetLevelType() string {
	if m != nil {
		return m.LevelType
	}
	return ""
}

func (m *ChannelLive) GetLevelChangeType() string {
	if m != nil {
		return m.LevelChangeType
	}
	return ""
}

func (m *ChannelLive) GetBindType() string {
	if m != nil {
		return m.BindType
	}
	return ""
}

func (m *ChannelLive) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ChannelLive) GetBroadcastStatus() uint32 {
	if m != nil {
		return m.BroadcastStatus
	}
	return 0
}

func (m *ChannelLive) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChannelLive) GetBroadcastStartTime() uint32 {
	if m != nil {
		return m.BroadcastStartTime
	}
	return 0
}

func (m *ChannelLive) GetBroadcastEndTime() uint32 {
	if m != nil {
		return m.BroadcastEndTime
	}
	return 0
}

type ChannelLive_ChannelLiveTag struct {
	TagId                string   `protobuf:"bytes,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLive_ChannelLiveTag) Reset()         { *m = ChannelLive_ChannelLiveTag{} }
func (m *ChannelLive_ChannelLiveTag) String() string { return proto.CompactTextString(m) }
func (*ChannelLive_ChannelLiveTag) ProtoMessage()    {}
func (*ChannelLive_ChannelLiveTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{14, 0}
}
func (m *ChannelLive_ChannelLiveTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLive_ChannelLiveTag.Unmarshal(m, b)
}
func (m *ChannelLive_ChannelLiveTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLive_ChannelLiveTag.Marshal(b, m, deterministic)
}
func (dst *ChannelLive_ChannelLiveTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLive_ChannelLiveTag.Merge(dst, src)
}
func (m *ChannelLive_ChannelLiveTag) XXX_Size() int {
	return xxx_messageInfo_ChannelLive_ChannelLiveTag.Size(m)
}
func (m *ChannelLive_ChannelLiveTag) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLive_ChannelLiveTag.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLive_ChannelLiveTag proto.InternalMessageInfo

func (m *ChannelLive_ChannelLiveTag) GetTagId() string {
	if m != nil {
		return m.TagId
	}
	return ""
}

func (m *ChannelLive_ChannelLiveTag) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

type ChannelLive_ChannelLiveLevel struct {
	Level                string   `protobuf:"bytes,1,opt,name=level,proto3" json:"level,omitempty"`
	LevelType            string   `protobuf:"bytes,2,opt,name=level_type,json=levelType,proto3" json:"level_type,omitempty"`
	LevelChangeType      string   `protobuf:"bytes,3,opt,name=level_change_type,json=levelChangeType,proto3" json:"level_change_type,omitempty"`
	BindType             string   `protobuf:"bytes,4,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLive_ChannelLiveLevel) Reset()         { *m = ChannelLive_ChannelLiveLevel{} }
func (m *ChannelLive_ChannelLiveLevel) String() string { return proto.CompactTextString(m) }
func (*ChannelLive_ChannelLiveLevel) ProtoMessage()    {}
func (*ChannelLive_ChannelLiveLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{14, 1}
}
func (m *ChannelLive_ChannelLiveLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLive_ChannelLiveLevel.Unmarshal(m, b)
}
func (m *ChannelLive_ChannelLiveLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLive_ChannelLiveLevel.Marshal(b, m, deterministic)
}
func (dst *ChannelLive_ChannelLiveLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLive_ChannelLiveLevel.Merge(dst, src)
}
func (m *ChannelLive_ChannelLiveLevel) XXX_Size() int {
	return xxx_messageInfo_ChannelLive_ChannelLiveLevel.Size(m)
}
func (m *ChannelLive_ChannelLiveLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLive_ChannelLiveLevel.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLive_ChannelLiveLevel proto.InternalMessageInfo

func (m *ChannelLive_ChannelLiveLevel) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

func (m *ChannelLive_ChannelLiveLevel) GetLevelType() string {
	if m != nil {
		return m.LevelType
	}
	return ""
}

func (m *ChannelLive_ChannelLiveLevel) GetLevelChangeType() string {
	if m != nil {
		return m.LevelChangeType
	}
	return ""
}

func (m *ChannelLive_ChannelLiveLevel) GetBindType() string {
	if m != nil {
		return m.BindType
	}
	return ""
}

// 事件在官频转播开始时上报，并带上计划的结束时间
type ChannelLive_ChannelLiveBroadcast struct {
	ChannelDisplayId     uint32   `protobuf:"varint,1,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	BroadcastStartTime   uint32   `protobuf:"varint,2,opt,name=broadcast_start_time,json=broadcastStartTime,proto3" json:"broadcast_start_time,omitempty"`
	BroadcastEndTime     uint32   `protobuf:"varint,3,opt,name=broadcast_end_time,json=broadcastEndTime,proto3" json:"broadcast_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLive_ChannelLiveBroadcast) Reset()         { *m = ChannelLive_ChannelLiveBroadcast{} }
func (m *ChannelLive_ChannelLiveBroadcast) String() string { return proto.CompactTextString(m) }
func (*ChannelLive_ChannelLiveBroadcast) ProtoMessage()    {}
func (*ChannelLive_ChannelLiveBroadcast) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{14, 2}
}
func (m *ChannelLive_ChannelLiveBroadcast) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLive_ChannelLiveBroadcast.Unmarshal(m, b)
}
func (m *ChannelLive_ChannelLiveBroadcast) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLive_ChannelLiveBroadcast.Marshal(b, m, deterministic)
}
func (dst *ChannelLive_ChannelLiveBroadcast) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLive_ChannelLiveBroadcast.Merge(dst, src)
}
func (m *ChannelLive_ChannelLiveBroadcast) XXX_Size() int {
	return xxx_messageInfo_ChannelLive_ChannelLiveBroadcast.Size(m)
}
func (m *ChannelLive_ChannelLiveBroadcast) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLive_ChannelLiveBroadcast.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLive_ChannelLiveBroadcast proto.InternalMessageInfo

func (m *ChannelLive_ChannelLiveBroadcast) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *ChannelLive_ChannelLiveBroadcast) GetBroadcastStartTime() uint32 {
	if m != nil {
		return m.BroadcastStartTime
	}
	return 0
}

func (m *ChannelLive_ChannelLiveBroadcast) GetBroadcastEndTime() uint32 {
	if m != nil {
		return m.BroadcastEndTime
	}
	return 0
}

// 直播房动态信息(每次开播会重置)
type ChannelLiveDynamic struct {
	LiveStartTime        uint32   `protobuf:"varint,1,opt,name=live_start_time,json=liveStartTime,proto3" json:"live_start_time,omitempty"`
	LiveStatus           uint32   `protobuf:"varint,2,opt,name=live_status,json=liveStatus,proto3" json:"live_status,omitempty"`
	LivePkStatus         uint32   `protobuf:"varint,3,opt,name=live_pk_status,json=livePkStatus,proto3" json:"live_pk_status,omitempty"`
	EnterMaleNum         int32    `protobuf:"varint,4,opt,name=enter_male_num,json=enterMaleNum,proto3" json:"enter_male_num,omitempty"`
	EnterFemaleNum       int32    `protobuf:"varint,5,opt,name=enter_female_num,json=enterFemaleNum,proto3" json:"enter_female_num,omitempty"`
	MicMaleNum           int32    `protobuf:"varint,6,opt,name=mic_male_num,json=micMaleNum,proto3" json:"mic_male_num,omitempty"`
	MicFemaleNum         int32    `protobuf:"varint,7,opt,name=mic_female_num,json=micFemaleNum,proto3" json:"mic_female_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLiveDynamic) Reset()         { *m = ChannelLiveDynamic{} }
func (m *ChannelLiveDynamic) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveDynamic) ProtoMessage()    {}
func (*ChannelLiveDynamic) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{15}
}
func (m *ChannelLiveDynamic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveDynamic.Unmarshal(m, b)
}
func (m *ChannelLiveDynamic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveDynamic.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveDynamic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveDynamic.Merge(dst, src)
}
func (m *ChannelLiveDynamic) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveDynamic.Size(m)
}
func (m *ChannelLiveDynamic) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveDynamic.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveDynamic proto.InternalMessageInfo

func (m *ChannelLiveDynamic) GetLiveStartTime() uint32 {
	if m != nil {
		return m.LiveStartTime
	}
	return 0
}

func (m *ChannelLiveDynamic) GetLiveStatus() uint32 {
	if m != nil {
		return m.LiveStatus
	}
	return 0
}

func (m *ChannelLiveDynamic) GetLivePkStatus() uint32 {
	if m != nil {
		return m.LivePkStatus
	}
	return 0
}

func (m *ChannelLiveDynamic) GetEnterMaleNum() int32 {
	if m != nil {
		return m.EnterMaleNum
	}
	return 0
}

func (m *ChannelLiveDynamic) GetEnterFemaleNum() int32 {
	if m != nil {
		return m.EnterFemaleNum
	}
	return 0
}

func (m *ChannelLiveDynamic) GetMicMaleNum() int32 {
	if m != nil {
		return m.MicMaleNum
	}
	return 0
}

func (m *ChannelLiveDynamic) GetMicFemaleNum() int32 {
	if m != nil {
		return m.MicFemaleNum
	}
	return 0
}

type ChannelLiveDynamic_ChannelLiveStartTime struct {
	LiveStartTime        uint32   `protobuf:"varint,1,opt,name=live_start_time,json=liveStartTime,proto3" json:"live_start_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLiveDynamic_ChannelLiveStartTime) Reset() {
	*m = ChannelLiveDynamic_ChannelLiveStartTime{}
}
func (m *ChannelLiveDynamic_ChannelLiveStartTime) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveDynamic_ChannelLiveStartTime) ProtoMessage()    {}
func (*ChannelLiveDynamic_ChannelLiveStartTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{15, 0}
}
func (m *ChannelLiveDynamic_ChannelLiveStartTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStartTime.Unmarshal(m, b)
}
func (m *ChannelLiveDynamic_ChannelLiveStartTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStartTime.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveDynamic_ChannelLiveStartTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStartTime.Merge(dst, src)
}
func (m *ChannelLiveDynamic_ChannelLiveStartTime) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStartTime.Size(m)
}
func (m *ChannelLiveDynamic_ChannelLiveStartTime) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStartTime.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStartTime proto.InternalMessageInfo

func (m *ChannelLiveDynamic_ChannelLiveStartTime) GetLiveStartTime() uint32 {
	if m != nil {
		return m.LiveStartTime
	}
	return 0
}

// 局部更新不能使用枚举
type ChannelLiveDynamic_ChannelLiveStatus struct {
	LiveStatus           uint32   `protobuf:"varint,1,opt,name=live_status,json=liveStatus,proto3" json:"live_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLiveDynamic_ChannelLiveStatus) Reset()         { *m = ChannelLiveDynamic_ChannelLiveStatus{} }
func (m *ChannelLiveDynamic_ChannelLiveStatus) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveDynamic_ChannelLiveStatus) ProtoMessage()    {}
func (*ChannelLiveDynamic_ChannelLiveStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{15, 1}
}
func (m *ChannelLiveDynamic_ChannelLiveStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStatus.Unmarshal(m, b)
}
func (m *ChannelLiveDynamic_ChannelLiveStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStatus.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveDynamic_ChannelLiveStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStatus.Merge(dst, src)
}
func (m *ChannelLiveDynamic_ChannelLiveStatus) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStatus.Size(m)
}
func (m *ChannelLiveDynamic_ChannelLiveStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStatus.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveDynamic_ChannelLiveStatus proto.InternalMessageInfo

func (m *ChannelLiveDynamic_ChannelLiveStatus) GetLiveStatus() uint32 {
	if m != nil {
		return m.LiveStatus
	}
	return 0
}

type ChannelLiveDynamic_ChannelLivePKStatus struct {
	LivePkStatus         uint32   `protobuf:"varint,1,opt,name=live_pk_status,json=livePkStatus,proto3" json:"live_pk_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLiveDynamic_ChannelLivePKStatus) Reset() {
	*m = ChannelLiveDynamic_ChannelLivePKStatus{}
}
func (m *ChannelLiveDynamic_ChannelLivePKStatus) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveDynamic_ChannelLivePKStatus) ProtoMessage()    {}
func (*ChannelLiveDynamic_ChannelLivePKStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{15, 2}
}
func (m *ChannelLiveDynamic_ChannelLivePKStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLivePKStatus.Unmarshal(m, b)
}
func (m *ChannelLiveDynamic_ChannelLivePKStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLivePKStatus.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveDynamic_ChannelLivePKStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLivePKStatus.Merge(dst, src)
}
func (m *ChannelLiveDynamic_ChannelLivePKStatus) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLivePKStatus.Size(m)
}
func (m *ChannelLiveDynamic_ChannelLivePKStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLivePKStatus.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveDynamic_ChannelLivePKStatus proto.InternalMessageInfo

func (m *ChannelLiveDynamic_ChannelLivePKStatus) GetLivePkStatus() uint32 {
	if m != nil {
		return m.LivePkStatus
	}
	return 0
}

type ChannelLiveDynamic_ChannelLiveEnterMale struct {
	EnterMaleNum         int32    `protobuf:"varint,1,opt,name=enter_male_num,json=enterMaleNum,proto3" json:"enter_male_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLiveDynamic_ChannelLiveEnterMale) Reset() {
	*m = ChannelLiveDynamic_ChannelLiveEnterMale{}
}
func (m *ChannelLiveDynamic_ChannelLiveEnterMale) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveDynamic_ChannelLiveEnterMale) ProtoMessage()    {}
func (*ChannelLiveDynamic_ChannelLiveEnterMale) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{15, 3}
}
func (m *ChannelLiveDynamic_ChannelLiveEnterMale) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterMale.Unmarshal(m, b)
}
func (m *ChannelLiveDynamic_ChannelLiveEnterMale) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterMale.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveDynamic_ChannelLiveEnterMale) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterMale.Merge(dst, src)
}
func (m *ChannelLiveDynamic_ChannelLiveEnterMale) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterMale.Size(m)
}
func (m *ChannelLiveDynamic_ChannelLiveEnterMale) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterMale.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterMale proto.InternalMessageInfo

func (m *ChannelLiveDynamic_ChannelLiveEnterMale) GetEnterMaleNum() int32 {
	if m != nil {
		return m.EnterMaleNum
	}
	return 0
}

type ChannelLiveDynamic_ChannelLiveEnterFemale struct {
	EnterFemaleNum       int32    `protobuf:"varint,1,opt,name=enter_female_num,json=enterFemaleNum,proto3" json:"enter_female_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLiveDynamic_ChannelLiveEnterFemale) Reset() {
	*m = ChannelLiveDynamic_ChannelLiveEnterFemale{}
}
func (m *ChannelLiveDynamic_ChannelLiveEnterFemale) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveDynamic_ChannelLiveEnterFemale) ProtoMessage()    {}
func (*ChannelLiveDynamic_ChannelLiveEnterFemale) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{15, 4}
}
func (m *ChannelLiveDynamic_ChannelLiveEnterFemale) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterFemale.Unmarshal(m, b)
}
func (m *ChannelLiveDynamic_ChannelLiveEnterFemale) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterFemale.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveDynamic_ChannelLiveEnterFemale) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterFemale.Merge(dst, src)
}
func (m *ChannelLiveDynamic_ChannelLiveEnterFemale) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterFemale.Size(m)
}
func (m *ChannelLiveDynamic_ChannelLiveEnterFemale) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterFemale.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveDynamic_ChannelLiveEnterFemale proto.InternalMessageInfo

func (m *ChannelLiveDynamic_ChannelLiveEnterFemale) GetEnterFemaleNum() int32 {
	if m != nil {
		return m.EnterFemaleNum
	}
	return 0
}

type ChannelLiveDynamic_ChannelLiveMicMale struct {
	MicMaleNum           int32    `protobuf:"varint,1,opt,name=mic_male_num,json=micMaleNum,proto3" json:"mic_male_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLiveDynamic_ChannelLiveMicMale) Reset()         { *m = ChannelLiveDynamic_ChannelLiveMicMale{} }
func (m *ChannelLiveDynamic_ChannelLiveMicMale) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveDynamic_ChannelLiveMicMale) ProtoMessage()    {}
func (*ChannelLiveDynamic_ChannelLiveMicMale) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{15, 5}
}
func (m *ChannelLiveDynamic_ChannelLiveMicMale) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicMale.Unmarshal(m, b)
}
func (m *ChannelLiveDynamic_ChannelLiveMicMale) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicMale.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveDynamic_ChannelLiveMicMale) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicMale.Merge(dst, src)
}
func (m *ChannelLiveDynamic_ChannelLiveMicMale) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicMale.Size(m)
}
func (m *ChannelLiveDynamic_ChannelLiveMicMale) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicMale.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicMale proto.InternalMessageInfo

func (m *ChannelLiveDynamic_ChannelLiveMicMale) GetMicMaleNum() int32 {
	if m != nil {
		return m.MicMaleNum
	}
	return 0
}

type ChannelLiveDynamic_ChannelLiveMicFemale struct {
	MicFemaleNum         int32    `protobuf:"varint,1,opt,name=mic_female_num,json=micFemaleNum,proto3" json:"mic_female_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLiveDynamic_ChannelLiveMicFemale) Reset() {
	*m = ChannelLiveDynamic_ChannelLiveMicFemale{}
}
func (m *ChannelLiveDynamic_ChannelLiveMicFemale) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveDynamic_ChannelLiveMicFemale) ProtoMessage()    {}
func (*ChannelLiveDynamic_ChannelLiveMicFemale) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{15, 6}
}
func (m *ChannelLiveDynamic_ChannelLiveMicFemale) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicFemale.Unmarshal(m, b)
}
func (m *ChannelLiveDynamic_ChannelLiveMicFemale) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicFemale.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveDynamic_ChannelLiveMicFemale) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicFemale.Merge(dst, src)
}
func (m *ChannelLiveDynamic_ChannelLiveMicFemale) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicFemale.Size(m)
}
func (m *ChannelLiveDynamic_ChannelLiveMicFemale) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicFemale.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveDynamic_ChannelLiveMicFemale proto.InternalMessageInfo

func (m *ChannelLiveDynamic_ChannelLiveMicFemale) GetMicFemaleNum() int32 {
	if m != nil {
		return m.MicFemaleNum
	}
	return 0
}

// 直播房房间侧离线画像
type ChannelLiveRoomOffline struct {
	IPLiveLoginCnt_14D          uint32             `protobuf:"varint,1,opt,name=i_p_live_login_cnt_14d,json=iPLiveLoginCnt14d,proto3" json:"i_p_live_login_cnt_14d,omitempty"`
	IPLiveEnterCnt_14D          uint32             `protobuf:"varint,2,opt,name=i_p_live_enter_cnt_14d,json=iPLiveEnterCnt14d,proto3" json:"i_p_live_enter_cnt_14d,omitempty"`
	IPLiveEnterUserCnt_14D      uint32             `protobuf:"varint,3,opt,name=i_p_live_enter_user_cnt_14d,json=iPLiveEnterUserCnt14d,proto3" json:"i_p_live_enter_user_cnt_14d,omitempty"`
	IPLiveTotalDuration_14D     uint32             `protobuf:"varint,4,opt,name=i_p_live_total_duration_14d,json=iPLiveTotalDuration14d,proto3" json:"i_p_live_total_duration_14d,omitempty"`
	IPLiveFollowUserCnt_14D     uint32             `protobuf:"varint,5,opt,name=i_p_live_follow_user_cnt_14d,json=iPLiveFollowUserCnt14d,proto3" json:"i_p_live_follow_user_cnt_14d,omitempty"`
	IPLiveFansUserCnt_14D       uint32             `protobuf:"varint,6,opt,name=i_p_live_fans_user_cnt_14d,json=iPLiveFansUserCnt14d,proto3" json:"i_p_live_fans_user_cnt_14d,omitempty"`
	IPLiveGiftUserCnt_14D       uint32             `protobuf:"varint,7,opt,name=i_p_live_gift_user_cnt_14d,json=iPLiveGiftUserCnt14d,proto3" json:"i_p_live_gift_user_cnt_14d,omitempty"`
	IPLiveGiftCnt_14D           uint32             `protobuf:"varint,8,opt,name=i_p_live_gift_cnt_14d,json=iPLiveGiftCnt14d,proto3" json:"i_p_live_gift_cnt_14d,omitempty"`
	IPLiveGiftAmt_14D           float64            `protobuf:"fixed64,9,opt,name=i_p_live_gift_amt_14d,json=iPLiveGiftAmt14d,proto3" json:"i_p_live_gift_amt_14d,omitempty"`
	IPLiveGiftMaxAmt_14D        float64            `protobuf:"fixed64,10,opt,name=i_p_live_gift_max_amt_14d,json=iPLiveGiftMaxAmt14d,proto3" json:"i_p_live_gift_max_amt_14d,omitempty"`
	IPLiveEnterCnt2_14D         uint32             `protobuf:"varint,11,opt,name=i_p_live_enter_cnt2_14d,json=iPLiveEnterCnt214d,proto3" json:"i_p_live_enter_cnt2_14d,omitempty"`
	IPLiveEnterUserCnt2_14D     uint32             `protobuf:"varint,12,opt,name=i_p_live_enter_user_cnt2_14d,json=iPLiveEnterUserCnt214d,proto3" json:"i_p_live_enter_user_cnt2_14d,omitempty"`
	IPLiveTotalDuration2_14D    uint32             `protobuf:"varint,13,opt,name=i_p_live_total_duration2_14d,json=iPLiveTotalDuration214d,proto3" json:"i_p_live_total_duration2_14d,omitempty"`
	IPLiveFollowUserCnt2_14D    uint32             `protobuf:"varint,14,opt,name=i_p_live_follow_user_cnt2_14d,json=iPLiveFollowUserCnt214d,proto3" json:"i_p_live_follow_user_cnt2_14d,omitempty"`
	IPLiveFansUserCnt2_14D      uint32             `protobuf:"varint,15,opt,name=i_p_live_fans_user_cnt2_14d,json=iPLiveFansUserCnt214d,proto3" json:"i_p_live_fans_user_cnt2_14d,omitempty"`
	IPLiveGiftUserCnt2_14D      uint32             `protobuf:"varint,16,opt,name=i_p_live_gift_user_cnt2_14d,json=iPLiveGiftUserCnt214d,proto3" json:"i_p_live_gift_user_cnt2_14d,omitempty"`
	IPLiveGiftCnt2_14D          uint32             `protobuf:"varint,17,opt,name=i_p_live_gift_cnt2_14d,json=iPLiveGiftCnt214d,proto3" json:"i_p_live_gift_cnt2_14d,omitempty"`
	IPLiveGiftAmt2_14D          float64            `protobuf:"fixed64,18,opt,name=i_p_live_gift_amt2_14d,json=iPLiveGiftAmt214d,proto3" json:"i_p_live_gift_amt2_14d,omitempty"`
	IPLiveGiftMaxAmt2_14D       float64            `protobuf:"fixed64,19,opt,name=i_p_live_gift_max_amt2_14d,json=iPLiveGiftMaxAmt214d,proto3" json:"i_p_live_gift_max_amt2_14d,omitempty"`
	IPLiveGenderPref_14D        map[uint32]float64 `protobuf:"bytes,20,rep,name=i_p_live_gender_pref_14d,json=iPLiveGenderPref14d,proto3" json:"i_p_live_gender_pref_14d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	IPLiveAgeGroupPref_14D      map[uint32]float64 `protobuf:"bytes,21,rep,name=i_p_live_age_group_pref_14d,json=iPLiveAgeGroupPref14d,proto3" json:"i_p_live_age_group_pref_14d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	IPLiveTransGenderPref_14D   map[uint32]string  `protobuf:"bytes,22,rep,name=i_p_live_trans_gender_pref_14d,json=iPLiveTransGenderPref14d,proto3" json:"i_p_live_trans_gender_pref_14d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IPLiveTransAgeGroupPref_14D map[uint32]string  `protobuf:"bytes,23,rep,name=i_p_live_trans_age_group_pref_14d,json=iPLiveTransAgeGroupPref14d,proto3" json:"i_p_live_trans_age_group_pref_14d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IPLiveTransNewUserPref_14D  map[uint32]string  `protobuf:"bytes,24,rep,name=i_p_live_trans_new_user_pref_14d,json=iPLiveTransNewUserPref14d,proto3" json:"i_p_live_trans_new_user_pref_14d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral        struct{}           `json:"-"`
	XXX_unrecognized            []byte             `json:"-"`
	XXX_sizecache               int32              `json:"-"`
}

func (m *ChannelLiveRoomOffline) Reset()         { *m = ChannelLiveRoomOffline{} }
func (m *ChannelLiveRoomOffline) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveRoomOffline) ProtoMessage()    {}
func (*ChannelLiveRoomOffline) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{16}
}
func (m *ChannelLiveRoomOffline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveRoomOffline.Unmarshal(m, b)
}
func (m *ChannelLiveRoomOffline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveRoomOffline.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveRoomOffline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveRoomOffline.Merge(dst, src)
}
func (m *ChannelLiveRoomOffline) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveRoomOffline.Size(m)
}
func (m *ChannelLiveRoomOffline) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveRoomOffline.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveRoomOffline proto.InternalMessageInfo

func (m *ChannelLiveRoomOffline) GetIPLiveLoginCnt_14D() uint32 {
	if m != nil {
		return m.IPLiveLoginCnt_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveEnterCnt_14D() uint32 {
	if m != nil {
		return m.IPLiveEnterCnt_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveEnterUserCnt_14D() uint32 {
	if m != nil {
		return m.IPLiveEnterUserCnt_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveTotalDuration_14D() uint32 {
	if m != nil {
		return m.IPLiveTotalDuration_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveFollowUserCnt_14D() uint32 {
	if m != nil {
		return m.IPLiveFollowUserCnt_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveFansUserCnt_14D() uint32 {
	if m != nil {
		return m.IPLiveFansUserCnt_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveGiftUserCnt_14D() uint32 {
	if m != nil {
		return m.IPLiveGiftUserCnt_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveGiftCnt_14D() uint32 {
	if m != nil {
		return m.IPLiveGiftCnt_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveGiftAmt_14D() float64 {
	if m != nil {
		return m.IPLiveGiftAmt_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveGiftMaxAmt_14D() float64 {
	if m != nil {
		return m.IPLiveGiftMaxAmt_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveEnterCnt2_14D() uint32 {
	if m != nil {
		return m.IPLiveEnterCnt2_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveEnterUserCnt2_14D() uint32 {
	if m != nil {
		return m.IPLiveEnterUserCnt2_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveTotalDuration2_14D() uint32 {
	if m != nil {
		return m.IPLiveTotalDuration2_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveFollowUserCnt2_14D() uint32 {
	if m != nil {
		return m.IPLiveFollowUserCnt2_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveFansUserCnt2_14D() uint32 {
	if m != nil {
		return m.IPLiveFansUserCnt2_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveGiftUserCnt2_14D() uint32 {
	if m != nil {
		return m.IPLiveGiftUserCnt2_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveGiftCnt2_14D() uint32 {
	if m != nil {
		return m.IPLiveGiftCnt2_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveGiftAmt2_14D() float64 {
	if m != nil {
		return m.IPLiveGiftAmt2_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveGiftMaxAmt2_14D() float64 {
	if m != nil {
		return m.IPLiveGiftMaxAmt2_14D
	}
	return 0
}

func (m *ChannelLiveRoomOffline) GetIPLiveGenderPref_14D() map[uint32]float64 {
	if m != nil {
		return m.IPLiveGenderPref_14D
	}
	return nil
}

func (m *ChannelLiveRoomOffline) GetIPLiveAgeGroupPref_14D() map[uint32]float64 {
	if m != nil {
		return m.IPLiveAgeGroupPref_14D
	}
	return nil
}

func (m *ChannelLiveRoomOffline) GetIPLiveTransGenderPref_14D() map[uint32]string {
	if m != nil {
		return m.IPLiveTransGenderPref_14D
	}
	return nil
}

func (m *ChannelLiveRoomOffline) GetIPLiveTransAgeGroupPref_14D() map[uint32]string {
	if m != nil {
		return m.IPLiveTransAgeGroupPref_14D
	}
	return nil
}

func (m *ChannelLiveRoomOffline) GetIPLiveTransNewUserPref_14D() map[uint32]string {
	if m != nil {
		return m.IPLiveTransNewUserPref_14D
	}
	return nil
}

type RoomEmbeddingVec struct {
	Embs                 []float32 `protobuf:"fixed32,1,rep,packed,name=embs,proto3" json:"embs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *RoomEmbeddingVec) Reset()         { *m = RoomEmbeddingVec{} }
func (m *RoomEmbeddingVec) String() string { return proto.CompactTextString(m) }
func (*RoomEmbeddingVec) ProtoMessage()    {}
func (*RoomEmbeddingVec) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{17}
}
func (m *RoomEmbeddingVec) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomEmbeddingVec.Unmarshal(m, b)
}
func (m *RoomEmbeddingVec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomEmbeddingVec.Marshal(b, m, deterministic)
}
func (dst *RoomEmbeddingVec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomEmbeddingVec.Merge(dst, src)
}
func (m *RoomEmbeddingVec) XXX_Size() int {
	return xxx_messageInfo_RoomEmbeddingVec.Size(m)
}
func (m *RoomEmbeddingVec) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomEmbeddingVec.DiscardUnknown(m)
}

var xxx_messageInfo_RoomEmbeddingVec proto.InternalMessageInfo

func (m *RoomEmbeddingVec) GetEmbs() []float32 {
	if m != nil {
		return m.Embs
	}
	return nil
}

type TokenizeResultV1 struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Tag                  []string `protobuf:"bytes,2,rep,name=tag,proto3" json:"tag,omitempty"`
	CutWord              []string `protobuf:"bytes,3,rep,name=cut_word,json=cutWord,proto3" json:"cut_word,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TokenizeResultV1) Reset()         { *m = TokenizeResultV1{} }
func (m *TokenizeResultV1) String() string { return proto.CompactTextString(m) }
func (*TokenizeResultV1) ProtoMessage()    {}
func (*TokenizeResultV1) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{18}
}
func (m *TokenizeResultV1) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenizeResultV1.Unmarshal(m, b)
}
func (m *TokenizeResultV1) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenizeResultV1.Marshal(b, m, deterministic)
}
func (dst *TokenizeResultV1) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenizeResultV1.Merge(dst, src)
}
func (m *TokenizeResultV1) XXX_Size() int {
	return xxx_messageInfo_TokenizeResultV1.Size(m)
}
func (m *TokenizeResultV1) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenizeResultV1.DiscardUnknown(m)
}

var xxx_messageInfo_TokenizeResultV1 proto.InternalMessageInfo

func (m *TokenizeResultV1) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TokenizeResultV1) GetTag() []string {
	if m != nil {
		return m.Tag
	}
	return nil
}

func (m *TokenizeResultV1) GetCutWord() []string {
	if m != nil {
		return m.CutWord
	}
	return nil
}

type TokenizeResultV2 struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Tag                  []string `protobuf:"bytes,2,rep,name=tag,proto3" json:"tag,omitempty"`
	CutWord              []string `protobuf:"bytes,3,rep,name=cut_word,json=cutWord,proto3" json:"cut_word,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TokenizeResultV2) Reset()         { *m = TokenizeResultV2{} }
func (m *TokenizeResultV2) String() string { return proto.CompactTextString(m) }
func (*TokenizeResultV2) ProtoMessage()    {}
func (*TokenizeResultV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{19}
}
func (m *TokenizeResultV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenizeResultV2.Unmarshal(m, b)
}
func (m *TokenizeResultV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenizeResultV2.Marshal(b, m, deterministic)
}
func (dst *TokenizeResultV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenizeResultV2.Merge(dst, src)
}
func (m *TokenizeResultV2) XXX_Size() int {
	return xxx_messageInfo_TokenizeResultV2.Size(m)
}
func (m *TokenizeResultV2) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenizeResultV2.DiscardUnknown(m)
}

var xxx_messageInfo_TokenizeResultV2 proto.InternalMessageInfo

func (m *TokenizeResultV2) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *TokenizeResultV2) GetTag() []string {
	if m != nil {
		return m.Tag
	}
	return nil
}

func (m *TokenizeResultV2) GetCutWord() []string {
	if m != nil {
		return m.CutWord
	}
	return nil
}

type TokenizeResultV3 struct {
	Tag                  []string `protobuf:"bytes,1,rep,name=tag,proto3" json:"tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TokenizeResultV3) Reset()         { *m = TokenizeResultV3{} }
func (m *TokenizeResultV3) String() string { return proto.CompactTextString(m) }
func (*TokenizeResultV3) ProtoMessage()    {}
func (*TokenizeResultV3) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{20}
}
func (m *TokenizeResultV3) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenizeResultV3.Unmarshal(m, b)
}
func (m *TokenizeResultV3) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenizeResultV3.Marshal(b, m, deterministic)
}
func (dst *TokenizeResultV3) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenizeResultV3.Merge(dst, src)
}
func (m *TokenizeResultV3) XXX_Size() int {
	return xxx_messageInfo_TokenizeResultV3.Size(m)
}
func (m *TokenizeResultV3) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenizeResultV3.DiscardUnknown(m)
}

var xxx_messageInfo_TokenizeResultV3 proto.InternalMessageInfo

func (m *TokenizeResultV3) GetTag() []string {
	if m != nil {
		return m.Tag
	}
	return nil
}

// 接唱房基本画像
type ChannelSingBasic struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TagId                uint32   `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	GameStatus           uint32   `protobuf:"varint,4,opt,name=game_status,json=gameStatus,proto3" json:"game_status,omitempty"`
	NumLimit             uint32   `protobuf:"varint,5,opt,name=num_limit,json=numLimit,proto3" json:"num_limit,omitempty"`
	OccupyUidList        []uint32 `protobuf:"varint,6,rep,packed,name=occupy_uid_list,json=occupyUidList,proto3" json:"occupy_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSingBasic) Reset()         { *m = ChannelSingBasic{} }
func (m *ChannelSingBasic) String() string { return proto.CompactTextString(m) }
func (*ChannelSingBasic) ProtoMessage()    {}
func (*ChannelSingBasic) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{21}
}
func (m *ChannelSingBasic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSingBasic.Unmarshal(m, b)
}
func (m *ChannelSingBasic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSingBasic.Marshal(b, m, deterministic)
}
func (dst *ChannelSingBasic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSingBasic.Merge(dst, src)
}
func (m *ChannelSingBasic) XXX_Size() int {
	return xxx_messageInfo_ChannelSingBasic.Size(m)
}
func (m *ChannelSingBasic) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSingBasic.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSingBasic proto.InternalMessageInfo

func (m *ChannelSingBasic) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelSingBasic) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ChannelSingBasic) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelSingBasic) GetGameStatus() uint32 {
	if m != nil {
		return m.GameStatus
	}
	return 0
}

func (m *ChannelSingBasic) GetNumLimit() uint32 {
	if m != nil {
		return m.NumLimit
	}
	return 0
}

func (m *ChannelSingBasic) GetOccupyUidList() []uint32 {
	if m != nil {
		return m.OccupyUidList
	}
	return nil
}

type ChannelSingBasic_SingGameStatusUpdate struct {
	GameStatus           uint32   `protobuf:"varint,1,opt,name=game_status,json=gameStatus,proto3" json:"game_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSingBasic_SingGameStatusUpdate) Reset()         { *m = ChannelSingBasic_SingGameStatusUpdate{} }
func (m *ChannelSingBasic_SingGameStatusUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelSingBasic_SingGameStatusUpdate) ProtoMessage()    {}
func (*ChannelSingBasic_SingGameStatusUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{21, 0}
}
func (m *ChannelSingBasic_SingGameStatusUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSingBasic_SingGameStatusUpdate.Unmarshal(m, b)
}
func (m *ChannelSingBasic_SingGameStatusUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSingBasic_SingGameStatusUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelSingBasic_SingGameStatusUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSingBasic_SingGameStatusUpdate.Merge(dst, src)
}
func (m *ChannelSingBasic_SingGameStatusUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelSingBasic_SingGameStatusUpdate.Size(m)
}
func (m *ChannelSingBasic_SingGameStatusUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSingBasic_SingGameStatusUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSingBasic_SingGameStatusUpdate proto.InternalMessageInfo

func (m *ChannelSingBasic_SingGameStatusUpdate) GetGameStatus() uint32 {
	if m != nil {
		return m.GameStatus
	}
	return 0
}

type ChannelSingBasic_ChannelOccupyUpdate struct {
	OccupyUidList        []uint32 `protobuf:"varint,1,rep,packed,name=occupy_uid_list,json=occupyUidList,proto3" json:"occupy_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSingBasic_ChannelOccupyUpdate) Reset()         { *m = ChannelSingBasic_ChannelOccupyUpdate{} }
func (m *ChannelSingBasic_ChannelOccupyUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelSingBasic_ChannelOccupyUpdate) ProtoMessage()    {}
func (*ChannelSingBasic_ChannelOccupyUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{21, 1}
}
func (m *ChannelSingBasic_ChannelOccupyUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSingBasic_ChannelOccupyUpdate.Unmarshal(m, b)
}
func (m *ChannelSingBasic_ChannelOccupyUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSingBasic_ChannelOccupyUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelSingBasic_ChannelOccupyUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSingBasic_ChannelOccupyUpdate.Merge(dst, src)
}
func (m *ChannelSingBasic_ChannelOccupyUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelSingBasic_ChannelOccupyUpdate.Size(m)
}
func (m *ChannelSingBasic_ChannelOccupyUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSingBasic_ChannelOccupyUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSingBasic_ChannelOccupyUpdate proto.InternalMessageInfo

func (m *ChannelSingBasic_ChannelOccupyUpdate) GetOccupyUidList() []uint32 {
	if m != nil {
		return m.OccupyUidList
	}
	return nil
}

// 直播房预估请求数，算法组定期写入，例如写入将来7天的，每天一个画像(key为yymmdd格式的日期，要补0，例如20220105)，
// 每个画像包含这天每小时的预估请求数
type ChannelEnterCountPrediction struct {
	HourCount            []uint32 `protobuf:"varint,1,rep,packed,name=hour_count,json=hourCount,proto3" json:"hour_count,omitempty"`
	DecrFactor           float64  `protobuf:"fixed64,2,opt,name=decr_factor,json=decrFactor,proto3" json:"decr_factor,omitempty"`
	IncrFactor           float64  `protobuf:"fixed64,3,opt,name=incr_factor,json=incrFactor,proto3" json:"incr_factor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelEnterCountPrediction) Reset()         { *m = ChannelEnterCountPrediction{} }
func (m *ChannelEnterCountPrediction) String() string { return proto.CompactTextString(m) }
func (*ChannelEnterCountPrediction) ProtoMessage()    {}
func (*ChannelEnterCountPrediction) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{22}
}
func (m *ChannelEnterCountPrediction) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelEnterCountPrediction.Unmarshal(m, b)
}
func (m *ChannelEnterCountPrediction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelEnterCountPrediction.Marshal(b, m, deterministic)
}
func (dst *ChannelEnterCountPrediction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelEnterCountPrediction.Merge(dst, src)
}
func (m *ChannelEnterCountPrediction) XXX_Size() int {
	return xxx_messageInfo_ChannelEnterCountPrediction.Size(m)
}
func (m *ChannelEnterCountPrediction) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelEnterCountPrediction.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelEnterCountPrediction proto.InternalMessageInfo

func (m *ChannelEnterCountPrediction) GetHourCount() []uint32 {
	if m != nil {
		return m.HourCount
	}
	return nil
}

func (m *ChannelEnterCountPrediction) GetDecrFactor() float64 {
	if m != nil {
		return m.DecrFactor
	}
	return 0
}

func (m *ChannelEnterCountPrediction) GetIncrFactor() float64 {
	if m != nil {
		return m.IncrFactor
	}
	return 0
}

// 记录最近5次进房推荐的时间t1-t5
type ChannelRecommendItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EnterTime            uint32   `protobuf:"varint,2,opt,name=enter_time,json=enterTime,proto3" json:"enter_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRecommendItem) Reset()         { *m = ChannelRecommendItem{} }
func (m *ChannelRecommendItem) String() string { return proto.CompactTextString(m) }
func (*ChannelRecommendItem) ProtoMessage()    {}
func (*ChannelRecommendItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{23}
}
func (m *ChannelRecommendItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRecommendItem.Unmarshal(m, b)
}
func (m *ChannelRecommendItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRecommendItem.Marshal(b, m, deterministic)
}
func (dst *ChannelRecommendItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRecommendItem.Merge(dst, src)
}
func (m *ChannelRecommendItem) XXX_Size() int {
	return xxx_messageInfo_ChannelRecommendItem.Size(m)
}
func (m *ChannelRecommendItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRecommendItem.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRecommendItem proto.InternalMessageInfo

func (m *ChannelRecommendItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelRecommendItem) GetEnterTime() uint32 {
	if m != nil {
		return m.EnterTime
	}
	return 0
}

type ChannelRecommendList struct {
	Items                []*ChannelRecommendItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ChannelRecommendList) Reset()         { *m = ChannelRecommendList{} }
func (m *ChannelRecommendList) String() string { return proto.CompactTextString(m) }
func (*ChannelRecommendList) ProtoMessage()    {}
func (*ChannelRecommendList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{24}
}
func (m *ChannelRecommendList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRecommendList.Unmarshal(m, b)
}
func (m *ChannelRecommendList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRecommendList.Marshal(b, m, deterministic)
}
func (dst *ChannelRecommendList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRecommendList.Merge(dst, src)
}
func (m *ChannelRecommendList) XXX_Size() int {
	return xxx_messageInfo_ChannelRecommendList.Size(m)
}
func (m *ChannelRecommendList) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRecommendList.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRecommendList proto.InternalMessageInfo

func (m *ChannelRecommendList) GetItems() []*ChannelRecommendItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type ChannelRecommendList_Append struct {
	Items                []*ChannelRecommendItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ChannelRecommendList_Append) Reset()         { *m = ChannelRecommendList_Append{} }
func (m *ChannelRecommendList_Append) String() string { return proto.CompactTextString(m) }
func (*ChannelRecommendList_Append) ProtoMessage()    {}
func (*ChannelRecommendList_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_b27c50824e9af3de, []int{24, 0}
}
func (m *ChannelRecommendList_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRecommendList_Append.Unmarshal(m, b)
}
func (m *ChannelRecommendList_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRecommendList_Append.Marshal(b, m, deterministic)
}
func (dst *ChannelRecommendList_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRecommendList_Append.Merge(dst, src)
}
func (m *ChannelRecommendList_Append) XXX_Size() int {
	return xxx_messageInfo_ChannelRecommendList_Append.Size(m)
}
func (m *ChannelRecommendList_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRecommendList_Append.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRecommendList_Append proto.InternalMessageInfo

func (m *ChannelRecommendList_Append) GetItems() []*ChannelRecommendItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func init() {
	proto.RegisterType((*BlockOption)(nil), "rcmd.persona.tt.channel.BlockOption")
	proto.RegisterType((*ChannelBasic)(nil), "rcmd.persona.tt.channel.ChannelBasic")
	proto.RegisterType((*ChannelBasic_ChannelBasicUpdate)(nil), "rcmd.persona.tt.channel.ChannelBasic.ChannelBasicUpdate")
	proto.RegisterType((*ChannelBasic_ChannelGameStatusUpdate)(nil), "rcmd.persona.tt.channel.ChannelBasic.ChannelGameStatusUpdate")
	proto.RegisterType((*ChannelBasic_ChannelCompanionTypeUpdate)(nil), "rcmd.persona.tt.channel.ChannelBasic.ChannelCompanionTypeUpdate")
	proto.RegisterType((*ChannelBasic_ChannelMusicChannelUpdate)(nil), "rcmd.persona.tt.channel.ChannelBasic.ChannelMusicChannelUpdate")
	proto.RegisterType((*ChannelBasic_UcusTagUpdate)(nil), "rcmd.persona.tt.channel.ChannelBasic.UcusTagUpdate")
	proto.RegisterType((*ChannelDynamic)(nil), "rcmd.persona.tt.channel.ChannelDynamic")
	proto.RegisterType((*ChannelDynamic_SongListUpdate)(nil), "rcmd.persona.tt.channel.ChannelDynamic.SongListUpdate")
	proto.RegisterType((*ChannelDynamic_HighQualityUpdate)(nil), "rcmd.persona.tt.channel.ChannelDynamic.HighQualityUpdate")
	proto.RegisterType((*ChannelTeam)(nil), "rcmd.persona.tt.channel.ChannelTeam")
	proto.RegisterMapType((map[uint32]uint32)(nil), "rcmd.persona.tt.channel.ChannelTeam.Location2uidEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "rcmd.persona.tt.channel.ChannelTeam.Uid2locationEntry")
	proto.RegisterType((*ChannelExpDetail)(nil), "rcmd.persona.tt.channel.ChannelExpDetail")
	proto.RegisterType((*ChannelEnterDetail)(nil), "rcmd.persona.tt.channel.ChannelEnterDetail")
	proto.RegisterType((*ChannelOffline)(nil), "rcmd.persona.tt.channel.ChannelOffline")
	proto.RegisterMapType((map[uint32]*ChannelEnterDetail)(nil), "rcmd.persona.tt.channel.ChannelOffline.TEnterDSexEntry")
	proto.RegisterMapType((map[uint32]*ChannelEnterDetail)(nil), "rcmd.persona.tt.channel.ChannelOffline.TEnterDTagIdEntry")
	proto.RegisterMapType((map[uint32]*ChannelExpDetail)(nil), "rcmd.persona.tt.channel.ChannelOffline.TExpDSexEntry")
	proto.RegisterMapType((map[uint32]*ChannelExpDetail)(nil), "rcmd.persona.tt.channel.ChannelOffline.TExpDTagIdEntry")
	proto.RegisterType((*Nothing)(nil), "rcmd.persona.tt.channel.Nothing")
	proto.RegisterType((*ChannelTabIdDetail)(nil), "rcmd.persona.tt.channel.ChannelTabIdDetail")
	proto.RegisterType((*GameSimilar)(nil), "rcmd.persona.tt.channel.GameSimilar")
	proto.RegisterMapType((map[string]float64)(nil), "rcmd.persona.tt.channel.GameSimilar.GameSimilarEntry")
	proto.RegisterType((*ChannelTabId)(nil), "rcmd.persona.tt.channel.ChannelTabId")
	proto.RegisterMapType((map[string]*GameSimilar)(nil), "rcmd.persona.tt.channel.ChannelTabId.GameSimilarScoreEntry")
	proto.RegisterMapType((map[uint32]*ChannelTabIdDetail)(nil), "rcmd.persona.tt.channel.ChannelTabId.TagIdTransEntry")
	proto.RegisterType((*ChannelEnterRealTransMinute3)(nil), "rcmd.persona.tt.channel.ChannelEnterRealTransMinute3")
	proto.RegisterType((*ChannelEnterRealTransMinute10)(nil), "rcmd.persona.tt.channel.ChannelEnterRealTransMinute10")
	proto.RegisterType((*ChannelEnterRealTrans)(nil), "rcmd.persona.tt.channel.ChannelEnterRealTrans")
	proto.RegisterType((*ChannelLive)(nil), "rcmd.persona.tt.channel.ChannelLive")
	proto.RegisterType((*ChannelLive_ChannelLiveTag)(nil), "rcmd.persona.tt.channel.ChannelLive.ChannelLiveTag")
	proto.RegisterType((*ChannelLive_ChannelLiveLevel)(nil), "rcmd.persona.tt.channel.ChannelLive.ChannelLiveLevel")
	proto.RegisterType((*ChannelLive_ChannelLiveBroadcast)(nil), "rcmd.persona.tt.channel.ChannelLive.ChannelLiveBroadcast")
	proto.RegisterType((*ChannelLiveDynamic)(nil), "rcmd.persona.tt.channel.ChannelLiveDynamic")
	proto.RegisterType((*ChannelLiveDynamic_ChannelLiveStartTime)(nil), "rcmd.persona.tt.channel.ChannelLiveDynamic.ChannelLiveStartTime")
	proto.RegisterType((*ChannelLiveDynamic_ChannelLiveStatus)(nil), "rcmd.persona.tt.channel.ChannelLiveDynamic.ChannelLiveStatus")
	proto.RegisterType((*ChannelLiveDynamic_ChannelLivePKStatus)(nil), "rcmd.persona.tt.channel.ChannelLiveDynamic.ChannelLivePKStatus")
	proto.RegisterType((*ChannelLiveDynamic_ChannelLiveEnterMale)(nil), "rcmd.persona.tt.channel.ChannelLiveDynamic.ChannelLiveEnterMale")
	proto.RegisterType((*ChannelLiveDynamic_ChannelLiveEnterFemale)(nil), "rcmd.persona.tt.channel.ChannelLiveDynamic.ChannelLiveEnterFemale")
	proto.RegisterType((*ChannelLiveDynamic_ChannelLiveMicMale)(nil), "rcmd.persona.tt.channel.ChannelLiveDynamic.ChannelLiveMicMale")
	proto.RegisterType((*ChannelLiveDynamic_ChannelLiveMicFemale)(nil), "rcmd.persona.tt.channel.ChannelLiveDynamic.ChannelLiveMicFemale")
	proto.RegisterType((*ChannelLiveRoomOffline)(nil), "rcmd.persona.tt.channel.ChannelLiveRoomOffline")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.channel.ChannelLiveRoomOffline.IPLiveAgeGroupPref14dEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.channel.ChannelLiveRoomOffline.IPLiveGenderPref14dEntry")
	proto.RegisterMapType((map[uint32]string)(nil), "rcmd.persona.tt.channel.ChannelLiveRoomOffline.IPLiveTransAgeGroupPref14dEntry")
	proto.RegisterMapType((map[uint32]string)(nil), "rcmd.persona.tt.channel.ChannelLiveRoomOffline.IPLiveTransGenderPref14dEntry")
	proto.RegisterMapType((map[uint32]string)(nil), "rcmd.persona.tt.channel.ChannelLiveRoomOffline.IPLiveTransNewUserPref14dEntry")
	proto.RegisterType((*RoomEmbeddingVec)(nil), "rcmd.persona.tt.channel.RoomEmbeddingVec")
	proto.RegisterType((*TokenizeResultV1)(nil), "rcmd.persona.tt.channel.TokenizeResultV1")
	proto.RegisterType((*TokenizeResultV2)(nil), "rcmd.persona.tt.channel.TokenizeResultV2")
	proto.RegisterType((*TokenizeResultV3)(nil), "rcmd.persona.tt.channel.TokenizeResultV3")
	proto.RegisterType((*ChannelSingBasic)(nil), "rcmd.persona.tt.channel.ChannelSingBasic")
	proto.RegisterType((*ChannelSingBasic_SingGameStatusUpdate)(nil), "rcmd.persona.tt.channel.ChannelSingBasic.SingGameStatusUpdate")
	proto.RegisterType((*ChannelSingBasic_ChannelOccupyUpdate)(nil), "rcmd.persona.tt.channel.ChannelSingBasic.ChannelOccupyUpdate")
	proto.RegisterType((*ChannelEnterCountPrediction)(nil), "rcmd.persona.tt.channel.ChannelEnterCountPrediction")
	proto.RegisterType((*ChannelRecommendItem)(nil), "rcmd.persona.tt.channel.ChannelRecommendItem")
	proto.RegisterType((*ChannelRecommendList)(nil), "rcmd.persona.tt.channel.ChannelRecommendList")
	proto.RegisterType((*ChannelRecommendList_Append)(nil), "rcmd.persona.tt.channel.ChannelRecommendList.Append")
	proto.RegisterEnum("rcmd.persona.tt.channel.ChannelBasic_MusicChannelType", ChannelBasic_MusicChannelType_name, ChannelBasic_MusicChannelType_value)
	proto.RegisterEnum("rcmd.persona.tt.channel.ChannelSingBasic_Status", ChannelSingBasic_Status_name, ChannelSingBasic_Status_value)
	proto.RegisterEnum("rcmd.persona.tt.channel.ChannelSingBasic_GameStatus", ChannelSingBasic_GameStatus_name, ChannelSingBasic_GameStatus_value)
}

func init() {
	proto.RegisterFile("rcmd/persona/tt/channel.proto", fileDescriptor_channel_b27c50824e9af3de)
}

var fileDescriptor_channel_b27c50824e9af3de = []byte{
	// 3958 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x3b, 0x5b, 0x6f, 0x1c, 0xd7,
	0x79, 0x1e, 0x5e, 0x96, 0xdc, 0x6f, 0x77, 0xc9, 0xe5, 0xf0, 0x36, 0x5a, 0x99, 0x12, 0xb5, 0x91,
	0x5c, 0xb9, 0x96, 0x29, 0x71, 0x45, 0x25, 0x36, 0x1d, 0x25, 0x15, 0x57, 0x92, 0x4d, 0x4b, 0xa4,
	0xa4, 0x25, 0xe9, 0x14, 0x2e, 0xd0, 0xc1, 0x70, 0xf6, 0x70, 0x79, 0xca, 0x9d, 0x99, 0xf5, 0xcc,
	0x19, 0x8a, 0x6c, 0xd3, 0x96, 0x4f, 0x4d, 0x20, 0x20, 0x4d, 0x91, 0x00, 0x45, 0x81, 0x02, 0x01,
	0x6a, 0xb4, 0x6a, 0x1e, 0xea, 0x17, 0x21, 0x5b, 0xf8, 0xa1, 0xcf, 0x06, 0xd2, 0x36, 0x29, 0x22,
	0xb7, 0x0f, 0xbd, 0xfc, 0x84, 0xf6, 0xa1, 0x40, 0x5b, 0x34, 0xcd, 0x4b, 0x71, 0xbe, 0x73, 0x66,
	0xf6, 0xcc, 0xec, 0xae, 0x29, 0x4a, 0x36, 0xfa, 0x52, 0x40, 0x80, 0x76, 0xbe, 0xfb, 0xf5, 0x5c,
	0xbe, 0x19, 0xc2, 0x9c, 0x6f, 0x3b, 0xf5, 0xcb, 0x2d, 0xe2, 0x07, 0x9e, 0x6b, 0x5d, 0x66, 0xec,
	0xb2, 0xbd, 0x6b, 0xb9, 0x2e, 0x69, 0x2e, 0xb4, 0x7c, 0x8f, 0x79, 0xfa, 0x2c, 0x47, 0x2f, 0x48,
	0xf4, 0x02, 0x63, 0x0b, 0x12, 0x5d, 0x2a, 0x25, 0xf8, 0xbc, 0x16, 0xa3, 0x9e, 0x1b, 0x08, 0xa6,
	0x92, 0x81, 0x38, 0xdb, 0x73, 0x1c, 0xcf, 0x95, 0xff, 0x09, 0x4c, 0xf9, 0x5b, 0x1a, 0xe4, 0x56,
	0x9a, 0x9e, 0xbd, 0x77, 0x0f, 0x19, 0xf4, 0x53, 0x30, 0xba, 0xcd, 0x1f, 0x4d, 0x5a, 0x37, 0xb4,
	0x79, 0xed, 0x62, 0xa1, 0x36, 0x82, 0xcf, 0xab, 0x75, 0x7d, 0x16, 0x46, 0x48, 0x93, 0x38, 0x1c,
	0x33, 0x80, 0x98, 0x0c, 0x7f, 0x5c, 0xad, 0xeb, 0x67, 0x21, 0x27, 0x78, 0x18, 0x65, 0x4d, 0x62,
	0x0c, 0xce, 0x6b, 0x17, 0xb3, 0x35, 0x40, 0xd0, 0x26, 0x87, 0xe8, 0x5f, 0x82, 0x02, 0x27, 0x25,
	0x2e, 0x93, 0x24, 0x43, 0x48, 0x92, 0x97, 0x40, 0x24, 0x2a, 0xff, 0x81, 0x0e, 0xf9, 0xaa, 0xf0,
	0x65, 0xc5, 0x0a, 0xa8, 0xad, 0x17, 0x61, 0x30, 0x8c, 0xad, 0xe0, 0x3f, 0xf5, 0x39, 0x00, 0xe9,
	0x6d, 0xc7, 0x88, 0xac, 0x84, 0xac, 0xd6, 0xb9, 0xed, 0x34, 0x30, 0xad, 0x26, 0xdd, 0x17, 0x46,
	0x14, 0x6a, 0x23, 0x34, 0xb8, 0xc1, 0x1f, 0xf5, 0x69, 0xc8, 0x30, 0xab, 0xc1, 0xb9, 0x86, 0x10,
	0x31, 0xcc, 0xac, 0x86, 0xe0, 0xe0, 0x60, 0xd7, 0x72, 0x88, 0x31, 0x8c, 0x36, 0x8d, 0x30, 0xab,
	0xb1, 0x6e, 0x39, 0x44, 0x7f, 0x19, 0x80, 0xa3, 0x96, 0x4c, 0xaf, 0xc5, 0x16, 0x8d, 0x0c, 0x22,
	0x39, 0xf1, 0xd2, 0xbd, 0x16, 0x5b, 0x4c, 0x60, 0x2b, 0xc6, 0x48, 0x02, 0x5b, 0x49, 0x60, 0xaf,
	0x1a, 0xa3, 0x09, 0xec, 0xd5, 0x04, 0x76, 0xc9, 0xc8, 0x26, 0xb0, 0x4b, 0xfa, 0x39, 0xc8, 0x47,
	0x3e, 0xa2, 0x59, 0x80, 0xf8, 0x9c, 0x84, 0xa1, 0x69, 0xa7, 0x21, 0xdb, 0x0a, 0x83, 0x5d, 0x93,
	0x51, 0x87, 0x18, 0x39, 0xf4, 0x67, 0x94, 0x03, 0x36, 0xa9, 0x43, 0xf4, 0x0d, 0x28, 0x88, 0x64,
	0xc8, 0x0a, 0x30, 0xf2, 0xf3, 0x83, 0x17, 0x73, 0x95, 0xf3, 0x0b, 0x7d, 0xea, 0x66, 0x41, 0xc9,
	0xfe, 0xca, 0xe8, 0xf7, 0x1e, 0xcf, 0x0d, 0x35, 0x69, 0xc0, 0x6a, 0xf9, 0xed, 0x0e, 0x38, 0xe0,
	0x81, 0x7f, 0x68, 0xb9, 0xcc, 0xdc, 0xf1, 0x49, 0xb0, 0x6b, 0x14, 0xe6, 0xb5, 0x8b, 0xa3, 0xb5,
	0x2c, 0x87, 0xdc, 0xe6, 0x00, 0xfd, 0x35, 0x18, 0x6c, 0x7a, 0xb6, 0x31, 0x36, 0xaf, 0x5d, 0xcc,
	0x55, 0x4e, 0x09, 0x4d, 0xb2, 0xca, 0xee, 0x7a, 0xb6, 0xc5, 0x65, 0xac, 0xba, 0x3b, 0x5e, 0x8d,
	0x53, 0xa9, 0x0e, 0xb2, 0xc3, 0x16, 0x31, 0xc6, 0xd1, 0x81, 0xc8, 0xc1, 0xcd, 0xc3, 0x16, 0xd1,
	0x17, 0x60, 0x32, 0x22, 0x69, 0x58, 0x0e, 0x31, 0x03, 0x66, 0xb1, 0x30, 0x30, 0x8a, 0x48, 0x39,
	0x21, 0x51, 0x6f, 0x5b, 0x0e, 0xd9, 0x40, 0x84, 0x7e, 0x01, 0xc6, 0x6c, 0xcf, 0x69, 0x59, 0x2e,
	0xf5, 0x5c, 0x21, 0x74, 0x02, 0x49, 0x0b, 0x31, 0x14, 0xc5, 0x16, 0x61, 0x30, 0x20, 0x07, 0x86,
	0x2e, 0x0a, 0x2a, 0x20, 0x07, 0xfa, 0x25, 0xd0, 0x9d, 0x30, 0xa0, 0xb6, 0x99, 0xb0, 0x68, 0x12,
	0x09, 0x8a, 0x88, 0xa9, 0x2a, 0x66, 0x9d, 0x81, 0x5c, 0x68, 0x87, 0x81, 0x29, 0x2b, 0x69, 0x4a,
	0xd4, 0x1f, 0x07, 0x6d, 0x62, 0x35, 0x95, 0xa1, 0x10, 0xe3, 0x31, 0x77, 0xd3, 0x22, 0x77, 0x92,
	0x02, 0x73, 0x77, 0x01, 0xc6, 0x64, 0x3f, 0x99, 0xa2, 0x7b, 0x8c, 0x99, 0xf9, 0xc1, 0x8b, 0xd9,
	0x5a, 0x41, 0x42, 0x6f, 0x21, 0xb0, 0xf4, 0x83, 0x21, 0xd0, 0xd5, 0x66, 0xd8, 0x6a, 0xd5, 0x2d,
	0x46, 0xfe, 0xbf, 0x25, 0x9e, 0xab, 0x25, 0x56, 0x5f, 0xa0, 0x25, 0xfe, 0x6f, 0x1b, 0x41, 0x56,
	0x6c, 0x31, 0xae, 0xd8, 0xe5, 0xcc, 0x51, 0xdb, 0xf8, 0xd9, 0xd3, 0xeb, 0xa5, 0x07, 0x30, 0x5b,
	0x4d, 0xf7, 0x81, 0x2c, 0x92, 0x3e, 0xdd, 0xa3, 0xf5, 0xe9, 0x1e, 0x14, 0xf9, 0xf4, 0xe9, 0xf5,
	0xd2, 0x1d, 0x28, 0x49, 0x91, 0x55, 0xb5, 0x6d, 0xa4, 0xd4, 0xee, 0x1e, 0xd3, 0x7a, 0xf4, 0x18,
	0x0a, 0xfb, 0xf4, 0xe9, 0xf5, 0xd2, 0xf7, 0x07, 0xe1, 0x94, 0x94, 0xb6, 0xa6, 0xf4, 0xd1, 0xf3,
	0xd6, 0x71, 0x9f, 0x62, 0x4d, 0xa4, 0x7d, 0xf8, 0xb8, 0xb4, 0x67, 0x9e, 0x3b, 0xed, 0x32, 0xaf,
	0x23, 0xcf, 0x94, 0x57, 0x99, 0xb4, 0xd1, 0xe3, 0x96, 0x99, 0x6c, 0x9f, 0x65, 0xa6, 0x7b, 0x89,
	0x80, 0x1e, 0x4b, 0xc4, 0x72, 0xe5, 0xa8, 0x6d, 0xfc, 0xfd, 0xd3, 0xeb, 0x1f, 0xb6, 0x8d, 0x8b,
	0xd4, 0xad, 0x93, 0x03, 0x93, 0x04, 0xdf, 0x5c, 0xbc, 0x76, 0xa5, 0x72, 0x85, 0xff, 0xbb, 0x14,
	0x07, 0x53, 0xa2, 0xcc, 0x7d, 0x4a, 0x1e, 0x96, 0xbe, 0xa5, 0x41, 0x61, 0x4b, 0xac, 0x46, 0x32,
	0x13, 0xa9, 0x35, 0x4d, 0x3b, 0x76, 0x4d, 0x1b, 0xe8, 0x5a, 0xd3, 0xd0, 0x92, 0x7f, 0x38, 0x99,
	0x25, 0xe5, 0x9f, 0x68, 0x50, 0x5c, 0x4b, 0x7b, 0x7e, 0x1a, 0x66, 0xb7, 0xdc, 0x3d, 0xd7, 0x7b,
	0xe8, 0xa6, 0x51, 0xc5, 0x97, 0xf4, 0x33, 0x50, 0x92, 0xc8, 0x95, 0x90, 0xad, 0x7b, 0xec, 0x8e,
	0x45, 0x77, 0x09, 0x95, 0x34, 0x45, 0x4d, 0x3f, 0x05, 0xd3, 0x5b, 0x55, 0xcb, 0xdd, 0xda, 0xa0,
	0x6e, 0x43, 0x65, 0x2f, 0x0e, 0xe8, 0x93, 0x30, 0x7e, 0x87, 0xed, 0x27, 0x80, 0x83, 0x1c, 0x58,
	0xb3, 0x5a, 0x09, 0xe0, 0x90, 0x5e, 0x82, 0x99, 0xbb, 0x34, 0x60, 0xc4, 0xdd, 0xf0, 0x52, 0x52,
	0x86, 0xb9, 0x75, 0x5c, 0xf6, 0x0d, 0xb7, 0x5e, 0xdd, 0xb5, 0x58, 0x02, 0x99, 0x59, 0xde, 0x38,
	0x6a, 0x1b, 0xff, 0xb1, 0xfe, 0xed, 0xb6, 0xa1, 0x7d, 0xaf, 0x6d, 0x14, 0x22, 0x9f, 0xb7, 0xf9,
	0xda, 0xfd, 0x61, 0xdb, 0xc8, 0xee, 0x5a, 0x7e, 0x1d, 0x1d, 0x3f, 0x51, 0x90, 0x7e, 0x3c, 0x0a,
	0x63, 0x52, 0xc5, 0xcd, 0x43, 0xd7, 0x72, 0xa8, 0xcd, 0x97, 0xe7, 0x30, 0x20, 0xbe, 0xe9, 0x86,
	0x4e, 0x74, 0x3e, 0xe3, 0xcf, 0xeb, 0xa1, 0xa3, 0x5f, 0x84, 0x22, 0x71, 0x19, 0xf1, 0xcd, 0x1d,
	0xe2, 0x58, 0x4d, 0x82, 0x24, 0xa2, 0x91, 0xc6, 0x10, 0x7e, 0x1b, 0xc1, 0x9c, 0x72, 0x16, 0x46,
	0x1c, 0x6a, 0x23, 0x81, 0xd8, 0x14, 0x32, 0x0e, 0xb5, 0x39, 0xe2, 0x3c, 0x8c, 0x71, 0x84, 0x22,
	0x40, 0xb4, 0x5b, 0xde, 0xa1, 0x76, 0x87, 0xbd, 0x0c, 0x85, 0xa6, 0x15, 0x30, 0x73, 0x87, 0x1e,
	0x88, 0xce, 0x13, 0x95, 0x9c, 0xe3, 0xc0, 0xdb, 0xf4, 0x00, 0x9b, 0xef, 0x15, 0x18, 0x47, 0x1a,
	0x61, 0x11, 0x52, 0x81, 0x58, 0x2f, 0x38, 0xf8, 0x16, 0x87, 0x22, 0xdd, 0x19, 0xc8, 0x71, 0x8d,
	0x56, 0x83, 0x98, 0x41, 0xe8, 0xc8, 0xa5, 0x3b, 0xeb, 0x50, 0xfb, 0x46, 0x83, 0x6c, 0x84, 0x8e,
	0x7e, 0x09, 0x26, 0xd1, 0x54, 0x0f, 0x49, 0x62, 0xd7, 0xf3, 0x48, 0x37, 0xce, 0xcd, 0xf6, 0x6e,
	0x34, 0xc8, 0x96, 0x0c, 0xc1, 0x69, 0xc8, 0x06, 0x9e, 0xdb, 0x30, 0xf9, 0x11, 0xc6, 0x28, 0x60,
	0xd7, 0x8c, 0x72, 0x00, 0xcf, 0xa9, 0x3e, 0x0f, 0xdc, 0x0d, 0x33, 0x76, 0x6d, 0x0c, 0x65, 0x80,
	0x43, 0xed, 0x35, 0xe9, 0xd8, 0x39, 0xc8, 0xef, 0xd2, 0xc6, 0xae, 0xf9, 0x41, 0x68, 0x35, 0x29,
	0x3b, 0xc4, 0x15, 0x79, 0xb4, 0x96, 0xe3, 0xb0, 0x07, 0x02, 0xc4, 0xed, 0xdd, 0xfd, 0xc0, 0x0c,
	0x08, 0x13, 0x3e, 0x89, 0x95, 0x39, 0xbb, 0xfb, 0xc1, 0x06, 0x61, 0xe8, 0xcf, 0x5b, 0x50, 0xda,
	0x63, 0xfb, 0x26, 0x39, 0xb0, 0x49, 0xb3, 0xc9, 0x0f, 0xbc, 0x2d, 0xdf, 0xab, 0x87, 0xb6, 0x34,
	0x9b, 0x1f, 0x4b, 0x86, 0x6b, 0xb3, 0x7b, 0x6c, 0xff, 0x56, 0x44, 0x70, 0x5f, 0xe2, 0xb9, 0xfe,
	0xd7, 0x61, 0x92, 0x33, 0x07, 0x9e, 0x4d, 0xad, 0xa6, 0xb9, 0x47, 0xdd, 0x06, 0x72, 0x4d, 0x22,
	0x57, 0x71, 0x8f, 0xed, 0x6f, 0x20, 0xe6, 0x0e, 0x75, 0x1b, 0x9c, 0xfc, 0x1a, 0x70, 0x49, 0xa6,
	0xed, 0xb9, 0x8c, 0x6b, 0xb2, 0xbd, 0x20, 0x74, 0xa4, 0xa2, 0x29, 0x64, 0x99, 0xda, 0x63, 0xfb,
	0x55, 0x81, 0xad, 0x0a, 0x24, 0x67, 0xbb, 0x0a, 0x33, 0x9c, 0x8d, 0x3a, 0x2d, 0x9f, 0xd8, 0x34,
	0x50, 0xa2, 0x3a, 0x8d, 0x5c, 0xdc, 0x86, 0xd5, 0x08, 0x19, 0x45, 0xf6, 0x3a, 0x9c, 0xc6, 0x75,
	0xa0, 0x8f, 0x63, 0x33, 0xc8, 0x69, 0x70, 0x92, 0x9e, 0x9e, 0x5d, 0x86, 0x29, 0x64, 0x4f, 0xbb,
	0x36, 0x8b, 0x7c, 0x13, 0x1c, 0x97, 0xf4, 0xed, 0x2b, 0x80, 0xc2, 0x7a, 0x3a, 0x67, 0x20, 0xd3,
	0x34, 0xc7, 0x77, 0x7b, 0x77, 0x0d, 0x66, 0x91, 0xb1, 0x87, 0x7b, 0xa7, 0x44, 0x50, 0x38, 0x3a,
	0xed, 0x5f, 0xe9, 0x1a, 0x8c, 0x6d, 0xc8, 0x42, 0x91, 0x2b, 0x63, 0xa2, 0x96, 0xb4, 0x64, 0x2d,
	0xe1, 0x36, 0xd7, 0xfe, 0xf4, 0x7a, 0xe9, 0xd7, 0x61, 0xe2, 0x9d, 0x4e, 0x75, 0x48, 0xce, 0x74,
	0x19, 0x69, 0xc7, 0x96, 0xd1, 0x40, 0xaa, 0x8c, 0x50, 0xfe, 0x5f, 0x7e, 0x7a, 0x7d, 0xf9, 0x9d,
	0xa3, 0xb6, 0xf1, 0x9f, 0xd1, 0xb2, 0x32, 0x1e, 0xad, 0x12, 0x75, 0xb1, 0x18, 0x9c, 0x68, 0x2d,
	0xf9, 0x93, 0x41, 0xc8, 0x45, 0x0b, 0x2a, 0xb1, 0xb0, 0x55, 0xbc, 0x87, 0x2e, 0xf1, 0xcd, 0xce,
	0x46, 0x3c, 0x8a, 0x80, 0x2d, 0x2a, 0xb7, 0xdb, 0xed, 0xce, 0x4e, 0x3c, 0xcc, 0xac, 0xed, 0xd5,
	0xba, 0xfe, 0x3e, 0xe4, 0x9b, 0x72, 0xbb, 0xab, 0x70, 0xb6, 0x41, 0xdc, 0x50, 0xbf, 0xdc, 0x77,
	0x43, 0x55, 0xf4, 0xc5, 0xfb, 0x24, 0x67, 0xbc, 0xe5, 0x32, 0xff, 0xb0, 0x96, 0x90, 0xc5, 0x65,
	0x87, 0xb4, 0x5e, 0x89, 0x60, 0xc6, 0xd0, 0x09, 0x64, 0x6f, 0x29, 0x8c, 0x52, 0xb6, 0x2a, 0x8b,
	0x2f, 0x9a, 0x4d, 0xcb, 0xde, 0xc3, 0x22, 0x10, 0xa7, 0x84, 0x11, 0xfe, 0xcc, 0xf3, 0xfe, 0x75,
	0x98, 0xe8, 0xb2, 0x8c, 0xef, 0xe0, 0x7b, 0xe4, 0x30, 0x3a, 0x9e, 0xec, 0x91, 0x43, 0x7d, 0x0a,
	0x86, 0xf7, 0xad, 0x66, 0x18, 0x65, 0x4a, 0x3c, 0x2c, 0x0f, 0xbc, 0xa1, 0x71, 0x01, 0x5d, 0xea,
	0x4f, 0x22, 0x60, 0x39, 0x7b, 0xd4, 0x36, 0x7e, 0x8e, 0x29, 0x2e, 0x87, 0x50, 0x94, 0x6e, 0xdd,
	0x3a, 0x68, 0xdd, 0x24, 0xcc, 0xa2, 0x4d, 0x5e, 0x4c, 0xdb, 0xbe, 0xf7, 0x30, 0x20, 0xa6, 0xed,
	0x85, 0x2e, 0x93, 0x32, 0x73, 0x02, 0x56, 0xe5, 0x20, 0x7e, 0xff, 0xb6, 0x9b, 0xd4, 0xde, 0x93,
	0x14, 0x42, 0x03, 0x20, 0x48, 0x10, 0xf0, 0xc3, 0x15, 0x12, 0xf8, 0x16, 0x13, 0xf7, 0x00, 0xad,
	0x96, 0x45, 0x48, 0xcd, 0x62, 0xa4, 0xfc, 0x53, 0x2d, 0xbe, 0x6c, 0xe0, 0xc2, 0x2c, 0x35, 0x9f,
	0x85, 0x9c, 0x58, 0xbd, 0x55, 0xc5, 0x80, 0x20, 0x21, 0xf6, 0x1c, 0xe4, 0xad, 0xfd, 0x86, 0x59,
	0x0f, 0x7d, 0x91, 0xb2, 0x01, 0x14, 0x9c, 0xb3, 0xf6, 0x1b, 0x37, 0x25, 0x88, 0x57, 0x19, 0x23,
	0x96, 0xa3, 0x2a, 0x1e, 0xe5, 0x00, 0xae, 0x97, 0xa7, 0xc5, 0x0b, 0x99, 0xc0, 0x0d, 0x21, 0x6e,
	0xc4, 0x0b, 0x59, 0x84, 0xe2, 0x6b, 0x35, 0xa2, 0x86, 0x05, 0xca, 0xa1, 0x76, 0x4d, 0x76, 0x57,
	0x10, 0x3a, 0x1d, 0xad, 0x19, 0xa1, 0x35, 0x08, 0x9d, 0x48, 0x6b, 0xf9, 0x17, 0x10, 0xef, 0x9b,
	0xf7, 0x76, 0x76, 0x9a, 0xd4, 0x25, 0x7c, 0xcf, 0x8a, 0x36, 0xa3, 0x44, 0x1c, 0xc5, 0x56, 0x24,
	0xfc, 0x79, 0x0d, 0xf4, 0x88, 0x06, 0xd7, 0x0c, 0x35, 0x9c, 0xe3, 0x82, 0x90, 0x2f, 0x17, 0x82,
	0xf8, 0x22, 0x14, 0x99, 0xc9, 0x3c, 0x66, 0x35, 0x3b, 0xa6, 0x08, 0x07, 0xc7, 0xd8, 0x26, 0x07,
	0xc7, 0x31, 0x38, 0x0f, 0x63, 0xcc, 0x4c, 0x04, 0x4a, 0x38, 0x9b, 0x67, 0x37, 0x94, 0x48, 0x9d,
	0x81, 0x1c, 0x33, 0x3b, 0xb1, 0x12, 0x4e, 0x67, 0xd9, 0x66, 0x14, 0x2c, 0x7e, 0x45, 0x32, 0xe3,
	0x98, 0x64, 0x64, 0x28, 0xd7, 0x64, 0x50, 0x10, 0x1b, 0x07, 0x73, 0x44, 0x62, 0xef, 0xc9, 0x68,
	0xa2, 0xf3, 0x3b, 0x5e, 0xb3, 0xe9, 0x3d, 0x14, 0x04, 0xa3, 0x22, 0x66, 0xec, 0x36, 0xc2, 0x90,
	0xe6, 0x97, 0xb8, 0x3f, 0x61, 0xd2, 0xce, 0x2c, 0x92, 0x15, 0xd8, 0x96, 0x6a, 0xe8, 0x39, 0x2e,
	0x2c, 0x54, 0x4c, 0x05, 0xa4, 0x02, 0xb6, 0x15, 0xdb, 0x7a, 0x16, 0xf2, 0x9c, 0x24, 0xb6, 0x36,
	0x27, 0x9d, 0xd9, 0x8a, 0xcc, 0x95, 0x04, 0xb1, 0xc1, 0xf9, 0x88, 0x20, 0xb2, 0xf8, 0x02, 0x8c,
	0x73, 0x02, 0xd5, 0xe6, 0x82, 0x0c, 0xda, 0x96, 0x62, 0x34, 0x86, 0x36, 0xd1, 0x1e, 0x62, 0x53,
	0xcf, 0xb3, 0x15, 0xa5, 0x3f, 0xd0, 0x7d, 0xb5, 0x43, 0xe4, 0x4d, 0x8b, 0x55, 0x3b, 0x2d, 0x32,
	0xcf, 0x2d, 0x52, 0x9a, 0xa4, 0x28, 0x9d, 0xaa, 0x46, 0x5d, 0xa2, 0x6f, 0xf2, 0x04, 0x91, 0x83,
	0x96, 0x59, 0x37, 0xf9, 0xf1, 0x7e, 0x02, 0xd7, 0xa7, 0x6b, 0xc7, 0xad, 0x4f, 0xb2, 0xfe, 0x16,
	0x36, 0x79, 0x47, 0x6f, 0x90, 0x03, 0xb1, 0x3c, 0x8d, 0x32, 0xf9, 0xa8, 0xff, 0x1a, 0xf7, 0x40,
	0x48, 0x95, 0x47, 0x70, 0x1d, 0x05, 0x7f, 0xe5, 0x44, 0x82, 0xf1, 0xac, 0x2e, 0x44, 0x03, 0x8b,
	0x01, 0xfa, 0xfb, 0x9d, 0xa2, 0x17, 0x46, 0x4f, 0x9e, 0x54, 0x36, 0x2e, 0x07, 0xb1, 0xd9, 0xc0,
	0x62, 0x80, 0x6e, 0xf1, 0x7a, 0x89, 0x64, 0xc7, 0x13, 0x11, 0x2e, 0xfe, 0xcd, 0x13, 0x8a, 0x57,
	0x8c, 0xcf, 0x33, 0x05, 0x54, 0xda, 0x81, 0x42, 0x22, 0x6c, 0x3d, 0x96, 0xd5, 0xaf, 0xab, 0xcb,
	0x6a, 0xae, 0xf2, 0xea, 0x71, 0xaa, 0xe3, 0x75, 0x55, 0x5d, 0xc2, 0x77, 0x61, 0x3c, 0x15, 0xc5,
	0x2f, 0x4a, 0xd3, 0x6f, 0x70, 0x4d, 0x89, 0x98, 0xf6, 0xd0, 0x74, 0x23, 0xa9, 0xe9, 0xb5, 0x63,
	0x35, 0x75, 0xd6, 0x6c, 0x55, 0x57, 0x13, 0x26, 0xba, 0x02, 0xfc, 0x85, 0x69, 0xc3, 0x5d, 0xec,
	0xbf, 0xc4, 0x2e, 0xf6, 0x25, 0x18, 0x59, 0xf7, 0xd8, 0x2e, 0x75, 0x1b, 0xba, 0x01, 0x23, 0xae,
	0xf8, 0x19, 0x5d, 0x56, 0xe4, 0x63, 0x99, 0xc6, 0x5b, 0xce, 0x26, 0x3f, 0x5a, 0xc8, 0x2d, 0x27,
	0xb9, 0x51, 0x69, 0xa9, 0x8d, 0x2a, 0xb9, 0x9b, 0x0c, 0xa4, 0x76, 0x93, 0x59, 0x18, 0xe1, 0x7d,
	0x64, 0xbb, 0x2c, 0xba, 0xd4, 0x90, 0x83, 0x56, 0xd5, 0x65, 0xe5, 0x3f, 0xd7, 0x20, 0x87, 0xe3,
	0x0e, 0xea, 0xd0, 0xa6, 0xe5, 0xeb, 0xbf, 0x0a, 0x79, 0x31, 0x17, 0x11, 0xcf, 0x78, 0xb6, 0xfb,
	0xac, 0x4e, 0x56, 0x78, 0xd5, 0xdf, 0xa2, 0x62, 0x73, 0x8d, 0x0e, 0xa4, 0xf4, 0x35, 0x28, 0xa6,
	0x09, 0xd4, 0x88, 0x67, 0x7b, 0x1c, 0x05, 0x34, 0x25, 0x88, 0xe5, 0x1f, 0x0d, 0xc6, 0x23, 0x70,
	0x8c, 0x8a, 0xfe, 0x0d, 0xc8, 0x8b, 0xd6, 0x32, 0x99, 0x6f, 0xb9, 0xc1, 0xb1, 0xa6, 0xaa, 0xcc,
	0x0b, 0x98, 0xf6, 0x4d, 0xce, 0x17, 0x75, 0x6f, 0x0c, 0xd0, 0x29, 0xe8, 0x6a, 0x0c, 0xcc, 0xc0,
	0xf6, 0x7c, 0x6e, 0x10, 0x17, 0xff, 0xd6, 0xb3, 0x89, 0x57, 0x3c, 0xdd, 0xe0, 0xdc, 0x42, 0x49,
	0xb1, 0x91, 0x02, 0x63, 0xcd, 0x27, 0x2d, 0xf9, 0x1c, 0xaa, 0x50, 0x29, 0x1a, 0xb5, 0xe6, 0x29,
	0x4c, 0xf7, 0x34, 0xab, 0x47, 0x16, 0x96, 0x93, 0x1a, 0xcf, 0x3f, 0x4b, 0xfa, 0xd3, 0x05, 0xff,
	0xdf, 0xa2, 0xe0, 0xbf, 0x09, 0x2f, 0xab, 0xcd, 0x51, 0x23, 0x56, 0x13, 0x9d, 0x5d, 0xa3, 0x6e,
	0xc8, 0xc8, 0x55, 0xfd, 0x01, 0xe4, 0x31, 0x7d, 0x66, 0x1d, 0x0d, 0x46, 0x2b, 0x72, 0x95, 0x85,
	0x67, 0xea, 0xb4, 0x58, 0x58, 0x2d, 0x87, 0x32, 0x84, 0xcf, 0xa8, 0xfd, 0x7f, 0x84, 0xf6, 0xdf,
	0x86, 0xb9, 0xcf, 0xd0, 0xbe, 0x78, 0xe5, 0x8b, 0x52, 0xff, 0x0b, 0xa1, 0xfe, 0xb1, 0x06, 0xd3,
	0x3d, 0x39, 0x8e, 0x3f, 0x3f, 0x5e, 0x80, 0xb1, 0xd4, 0x01, 0x4a, 0x9c, 0xb5, 0x0a, 0x2c, 0x71,
	0x7e, 0x9a, 0x03, 0xc0, 0xae, 0x17, 0x62, 0x44, 0x6f, 0xe3, 0x3a, 0x20, 0xa4, 0xbc, 0x02, 0xe3,
	0xfc, 0x1c, 0xd1, 0x64, 0x95, 0x2b, 0x81, 0xa4, 0x11, 0x43, 0x8b, 0x82, 0x17, 0xb2, 0xbb, 0x1c,
	0x8a, 0x74, 0xe5, 0x9f, 0x67, 0xe2, 0x0b, 0xd0, 0x5d, 0xba, 0xff, 0x42, 0x33, 0x48, 0xf1, 0x86,
	0xab, 0xc7, 0xc0, 0x7c, 0x28, 0x39, 0x30, 0x9f, 0x82, 0xe1, 0x26, 0xd9, 0x27, 0x4d, 0x39, 0x48,
	0x17, 0x0f, 0x5c, 0x0d, 0xfe, 0x10, 0x53, 0x40, 0x31, 0x46, 0xcf, 0x22, 0x04, 0x87, 0x60, 0xbf,
	0x0c, 0x13, 0x02, 0xcd, 0x35, 0x37, 0x88, 0xa0, 0x12, 0xe3, 0xf4, 0x71, 0x44, 0x54, 0x11, 0x2e,
	0x07, 0x66, 0xd9, 0x6d, 0xea, 0xd6, 0x05, 0x8d, 0x1c, 0xaa, 0x73, 0x00, 0x22, 0xf9, 0xb5, 0xc0,
	0x27, 0x16, 0x23, 0xea, 0x90, 0x06, 0x04, 0x08, 0x67, 0x15, 0xaf, 0x42, 0x71, 0xdb, 0xf7, 0xac,
	0xba, 0x6d, 0x05, 0x2c, 0x9a, 0x12, 0x8b, 0x21, 0xcd, 0x78, 0x0c, 0x97, 0x6f, 0x58, 0x2e, 0x81,
	0x1e, 0x5f, 0x3e, 0x69, 0xd0, 0x6a, 0x5a, 0x87, 0x3c, 0x0e, 0x62, 0x5a, 0x53, 0x94, 0x98, 0x9b,
	0x02, 0xb1, 0x5a, 0xd7, 0xaf, 0xc0, 0x54, 0x42, 0xb0, 0x2f, 0xaf, 0xb9, 0x62, 0x6a, 0xa3, 0xab,
	0xc2, 0x7d, 0x31, 0x36, 0xb9, 0x04, 0x1d, 0xa8, 0x49, 0xb8, 0x47, 0x9c, 0xbe, 0x20, 0xe4, 0xc7,
	0x98, 0x5b, 0x6e, 0x9d, 0x53, 0x97, 0xde, 0x8d, 0x8f, 0xf7, 0x3c, 0x93, 0x9b, 0x56, 0x43, 0xc9,
	0x8d, 0xd6, 0x2f, 0x37, 0x03, 0x89, 0xdc, 0xe0, 0x0d, 0xfb, 0x3b, 0x1f, 0x55, 0x4b, 0x7f, 0xa4,
	0xc5, 0x97, 0x2e, 0x2e, 0xec, 0x2e, 0xa6, 0x28, 0x4e, 0x9c, 0xd6, 0x3f, 0x71, 0x03, 0xcf, 0x94,
	0xb8, 0xc1, 0x67, 0x48, 0xdc, 0x50, 0x32, 0x71, 0x68, 0xda, 0xef, 0x7f, 0x54, 0x2d, 0xfd, 0x85,
	0x06, 0x53, 0x8a, 0x69, 0x2b, 0x51, 0x18, 0xfa, 0x64, 0x43, 0x3b, 0x61, 0x36, 0x06, 0x4e, 0x98,
	0x8d, 0xc1, 0xde, 0xd9, 0x40, 0x73, 0xbf, 0xfb, 0x51, 0x75, 0xf9, 0xd4, 0x51, 0xdb, 0xf8, 0xe3,
	0xf7, 0xe4, 0xac, 0x22, 0x7e, 0xb5, 0xd1, 0xa4, 0xfb, 0xa4, 0xfc, 0xb3, 0x4c, 0xbc, 0xdd, 0x73,
	0x4f, 0xa2, 0x61, 0xe6, 0x2b, 0x30, 0xce, 0xd1, 0xaa, 0x51, 0xf2, 0xa5, 0x02, 0x07, 0x77, 0xec,
	0x39, 0x0b, 0xb9, 0x88, 0x8e, 0xd7, 0xa8, 0xbc, 0xe0, 0x4a, 0x1a, 0x5e, 0x9e, 0xe7, 0x61, 0x0c,
	0x09, 0x5a, 0x7b, 0x11, 0x8d, 0x30, 0x36, 0xcf, 0xa1, 0xf7, 0xf7, 0x3a, 0x54, 0x62, 0x41, 0x4a,
	0x4c, 0x37, 0x87, 0x6b, 0x79, 0x84, 0x46, 0x43, 0xc0, 0x5e, 0x63, 0xd4, 0x61, 0xa4, 0x4b, 0x8f,
	0x51, 0xd3, 0x03, 0xc5, 0x0c, 0x52, 0xa9, 0x03, 0xc5, 0xee, 0x79, 0xea, 0x88, 0xd0, 0xa8, 0xce,
	0x53, 0x4b, 0xb7, 0x13, 0x69, 0xee, 0xb8, 0xfd, 0x8c, 0xe1, 0xc1, 0x04, 0xfc, 0xf8, 0x49, 0xb5,
	0xf4, 0x55, 0x98, 0x48, 0xca, 0xe1, 0x4e, 0xa7, 0x62, 0xa7, 0xa5, 0x63, 0x87, 0xdc, 0x7f, 0xfd,
	0xa4, 0x5a, 0xaa, 0xc2, 0xa4, 0xc2, 0x7d, 0xff, 0x4e, 0xdf, 0xd0, 0x6a, 0xdd, 0xa1, 0x45, 0x21,
	0x7f, 0xf3, 0xa4, 0x5a, 0xba, 0x9f, 0x70, 0xe5, 0x56, 0x14, 0x57, 0xfd, 0x4a, 0x57, 0xe8, 0xb9,
	0x94, 0xe1, 0x15, 0x78, 0xf4, 0x78, 0x2e, 0x43, 0x5d, 0xdb, 0xdf, 0x3e, 0x4c, 0xa6, 0x01, 0x25,
	0xfe, 0xed, 0x93, 0x6a, 0xe9, 0x3d, 0x98, 0x49, 0x4b, 0x14, 0x91, 0xd3, 0x97, 0x7a, 0x24, 0xaa,
	0x5b, 0x6a, 0x2a, 0x69, 0x28, 0xf7, 0x27, 0x4f, 0xaa, 0xa5, 0x77, 0x13, 0x15, 0xb9, 0x26, 0x72,
	0xa6, 0x5f, 0x4a, 0xa5, 0xb4, 0x5b, 0x9e, 0x92, 0x5e, 0x94, 0xf5, 0xd3, 0x2e, 0xaf, 0xd7, 0xa2,
	0xdc, 0x72, 0xaf, 0x53, 0xe9, 0xef, 0xe1, 0xb5, 0x5a, 0x0a, 0x28, 0xf1, 0xef, 0x9e, 0x54, 0x97,
	0xcf, 0x1d, 0xb5, 0x8d, 0x0f, 0xa3, 0x5e, 0x9a, 0x52, 0x7b, 0x29, 0x1a, 0xfe, 0x95, 0xff, 0x6a,
	0x22, 0x11, 0x99, 0x9a, 0xe7, 0x39, 0xd1, 0xb0, 0x63, 0x11, 0x66, 0xa8, 0xd9, 0x12, 0xe4, 0x4d,
	0xaf, 0x41, 0x5d, 0x7e, 0x2a, 0x36, 0x17, 0x97, 0xa2, 0x35, 0x62, 0x82, 0xde, 0xc7, 0xb5, 0x8e,
	0xa3, 0xaa, 0x2e, 0x5b, 0x5c, 0xaa, 0x27, 0x58, 0xe4, 0xae, 0x2d, 0x59, 0x06, 0x54, 0x16, 0x31,
	0x2d, 0x11, 0x2c, 0xcb, 0x70, 0x3a, 0xc5, 0x22, 0xa6, 0x26, 0x92, 0x4f, 0x74, 0xe0, 0xb4, 0xc2,
	0x87, 0xc3, 0x13, 0xc1, 0xfb, 0x96, 0xc2, 0x9b, 0x3c, 0x03, 0x20, 0xaf, 0xd8, 0xc0, 0x67, 0x04,
	0x6f, 0x62, 0x9a, 0xc2, 0x99, 0xbf, 0x0a, 0x2f, 0xc7, 0xcc, 0x72, 0x42, 0x90, 0xd0, 0x3c, 0xac,
	0x72, 0x8b, 0x69, 0x81, 0xa2, 0xfa, 0x0d, 0x28, 0x75, 0xb8, 0xf9, 0xb1, 0x28, 0xc1, 0x9b, 0x41,
	0xde, 0x29, 0xc9, 0x6b, 0xb9, 0x41, 0x1f, 0xce, 0x06, 0xdd, 0x61, 0x49, 0xce, 0x11, 0x95, 0xf3,
	0x6d, 0xba, 0xc3, 0x14, 0xce, 0xcb, 0x30, 0x9d, 0xe4, 0x8c, 0x98, 0xc4, 0x4b, 0xc2, 0x62, 0x87,
	0xa9, 0x1f, 0x83, 0xe5, 0x08, 0x06, 0x31, 0x92, 0x51, 0x18, 0x6e, 0x38, 0xc8, 0xf0, 0x65, 0x38,
	0x95, 0x64, 0x70, 0xac, 0x83, 0x98, 0x49, 0x4c, 0x68, 0x26, 0x3b, 0x4c, 0x6b, 0xd6, 0x81, 0xe4,
	0xbb, 0x0a, 0xb3, 0xdd, 0x79, 0xaf, 0x20, 0x97, 0xd8, 0xdd, 0xf5, 0x64, 0xe2, 0x2b, 0xe9, 0x04,
	0x24, 0x33, 0x2f, 0x38, 0xf3, 0x6a, 0x02, 0xd4, 0xd4, 0x23, 0xf7, 0x75, 0x85, 0x3b, 0x99, 0x7b,
	0xc1, 0x2d, 0x76, 0xfd, 0xd9, 0x1e, 0xc9, 0x47, 0xf6, 0xaf, 0xc1, 0x5c, 0xbf, 0xec, 0x0b, 0xfe,
	0x31, 0x95, 0x3f, 0x91, 0xfe, 0x4a, 0xba, 0x6c, 0x13, 0xf9, 0x17, 0xdc, 0xe3, 0x6a, 0xd9, 0x2a,
	0x05, 0xd0, 0xc5, 0x9b, 0xa8, 0x00, 0xc1, 0x5b, 0x54, 0x79, 0x95, 0x12, 0xa8, 0xa4, 0x3b, 0x2c,
	0xaa, 0x01, 0xc1, 0x36, 0xa1, 0x76, 0x98, 0x2c, 0x82, 0xde, 0x2c, 0x96, 0x23, 0x59, 0x74, 0xcc,
	0xe8, 0x44, 0xa2, 0x0c, 0x2a, 0x3d, 0x6b, 0x54, 0xd6, 0x81, 0x60, 0x9b, 0x44, 0xb6, 0xa9, 0x74,
	0x21, 0x20, 0xe7, 0xef, 0x82, 0xd1, 0xe1, 0x24, 0x6e, 0x9d, 0xf8, 0x66, 0xcb, 0x27, 0x3b, 0xc8,
	0x27, 0x06, 0x3b, 0xef, 0x1c, 0x77, 0x65, 0x48, 0xad, 0x43, 0x0b, 0xab, 0x42, 0x0f, 0x0a, 0xbb,
	0xef, 0x93, 0x9d, 0xc5, 0x25, 0x39, 0xe7, 0x89, 0x4a, 0x51, 0xc5, 0xe8, 0xbf, 0xa7, 0x29, 0xd1,
	0xb5, 0x1a, 0xc4, 0x6c, 0xf8, 0x5e, 0xd8, 0xea, 0x18, 0x31, 0x8d, 0x46, 0xbc, 0xfb, 0x7c, 0x46,
	0xdc, 0x68, 0x90, 0xb7, 0xb9, 0xb8, 0x84, 0x19, 0x32, 0x53, 0x29, 0x9c, 0xfe, 0x5d, 0x0d, 0xce,
	0x74, 0x2a, 0x14, 0xaf, 0x4e, 0xe9, 0x80, 0xcc, 0xa0, 0x2d, 0x6b, 0xcf, 0x67, 0x0b, 0x5e, 0x8f,
	0x7a, 0x44, 0xc5, 0xa0, 0x7d, 0xd0, 0xfa, 0x1f, 0x6a, 0x70, 0x2e, 0x65, 0x51, 0x8f, 0x00, 0xcd,
	0xa2, 0x51, 0xf7, 0x5e, 0xc0, 0xa8, 0x9e, 0x51, 0x2a, 0xd1, 0xbe, 0x04, 0xfa, 0xf7, 0x35, 0x98,
	0x4f, 0x19, 0xe6, 0x12, 0xd9, 0x90, 0xb1, 0x5d, 0x06, 0xda, 0xb5, 0xfe, 0x02, 0x76, 0xad, 0x13,
	0x6c, 0xe1, 0x84, 0x59, 0xa7, 0x68, 0x3f, 0x7c, 0xe9, 0x36, 0x18, 0xfd, 0x4a, 0xef, 0xb8, 0x57,
	0x33, 0xea, 0x3c, 0xa6, 0xf4, 0x0e, 0x94, 0xfa, 0x57, 0xcf, 0x89, 0x24, 0xdd, 0x81, 0xb9, 0xcf,
	0xcc, 0xfd, 0x71, 0xc2, 0xb2, 0xaa, 0xb0, 0x35, 0x38, 0x7b, 0x4c, 0xce, 0x4e, 0x24, 0xee, 0x2e,
	0x9c, 0xf9, 0xec, 0x50, 0x9f, 0x44, 0xda, 0xf2, 0x2b, 0x47, 0x6d, 0xe3, 0x07, 0xd1, 0xc9, 0xe5,
	0x54, 0xe2, 0xe4, 0xe2, 0x7b, 0x9e, 0x63, 0x7a, 0x22, 0xbb, 0xe5, 0x2a, 0x14, 0x79, 0xb2, 0x6f,
	0x39, 0xdb, 0xa4, 0x5e, 0xa7, 0x6e, 0xe3, 0x3d, 0x62, 0xeb, 0x3a, 0x0c, 0x11, 0x67, 0x5b, 0x8c,
	0xb9, 0x06, 0x6a, 0xf8, 0x7b, 0xf9, 0xf4, 0x51, 0xdb, 0xf8, 0xc7, 0x7b, 0x52, 0xde, 0x18, 0x8a,
	0x20, 0x11, 0x4f, 0xf9, 0x77, 0xa0, 0xb8, 0xe9, 0xed, 0x11, 0x97, 0xfe, 0x26, 0xa9, 0x91, 0x20,
	0x6c, 0xb2, 0xf7, 0x16, 0xb9, 0x10, 0xdb, 0xab, 0x47, 0x47, 0x65, 0xfc, 0xcd, 0x1d, 0x60, 0x56,
	0x03, 0xe7, 0x5b, 0xd9, 0x1a, 0xff, 0xc9, 0x6f, 0x86, 0x76, 0xc8, 0xcc, 0x87, 0x9e, 0x2f, 0x5e,
	0x63, 0x66, 0x6b, 0x23, 0x76, 0xc8, 0xbe, 0xe1, 0xf9, 0xf5, 0xe5, 0x57, 0x8f, 0xda, 0xc6, 0x3f,
	0x45, 0x1a, 0xc7, 0x51, 0x23, 0x93, 0x4a, 0xf6, 0x17, 0x13, 0x1f, 0x73, 0xf4, 0xd0, 0x5f, 0xf9,
	0x7c, 0xf4, 0xff, 0x73, 0x1f, 0xfd, 0x95, 0xa4, 0xfe, 0x07, 0x5d, 0xfa, 0xaf, 0x46, 0xba, 0xb4,
	0x58, 0xd7, 0xf2, 0x6b, 0x47, 0x6d, 0xe3, 0x47, 0xdf, 0xf9, 0x64, 0xa9, 0xb7, 0xc8, 0xab, 0x49,
	0x91, 0xff, 0x32, 0x18, 0xdf, 0x87, 0x37, 0xa8, 0xdb, 0x10, 0x9f, 0xe2, 0x26, 0x27, 0x23, 0x5a,
	0xff, 0xc9, 0xc8, 0x80, 0xfa, 0x75, 0xd6, 0x0c, 0x64, 0x12, 0xb7, 0x31, 0xf9, 0xc4, 0xaf, 0x24,
	0xea, 0x87, 0x69, 0xe2, 0xb0, 0x07, 0x8d, 0xce, 0xf7, 0x9c, 0xa7, 0x21, 0xeb, 0x86, 0x8e, 0xd9,
	0xa4, 0x0e, 0x65, 0xd1, 0x67, 0x5d, 0x6e, 0xe8, 0xdc, 0xe5, 0xcf, 0x38, 0xef, 0xb1, 0xed, 0xb0,
	0x75, 0x68, 0x86, 0xb4, 0x2e, 0xde, 0xce, 0x67, 0xe6, 0x07, 0x71, 0xde, 0x83, 0xe0, 0x2d, 0x5a,
	0xbf, 0x4b, 0x03, 0x56, 0xfa, 0x15, 0x98, 0xe2, 0x0e, 0x74, 0x7d, 0x1e, 0x97, 0xd2, 0xae, 0xa5,
	0xb5, 0x2f, 0x8f, 0x1c, 0xb5, 0x8d, 0x3f, 0x7b, 0xf4, 0xc9, 0x52, 0xe9, 0x76, 0x7c, 0x23, 0xba,
	0x27, 0x24, 0x0b, 0x01, 0x3d, 0x0c, 0xd0, 0x7a, 0x18, 0x80, 0x72, 0x1e, 0x3f, 0xfa, 0x64, 0xa9,
	0xfc, 0x3a, 0x64, 0xa4, 0x63, 0x45, 0xc8, 0x8b, 0x5f, 0xeb, 0x9e, 0xef, 0x58, 0xcd, 0xe2, 0x4b,
	0xfa, 0x04, 0x14, 0x04, 0xe4, 0x26, 0x0d, 0x1c, 0x1a, 0x04, 0x45, 0xad, 0xfc, 0x06, 0x80, 0xf2,
	0x6d, 0xeb, 0x34, 0x4c, 0x74, 0x9e, 0xd6, 0x3d, 0xbc, 0xf5, 0x15, 0x5f, 0xd2, 0x27, 0x61, 0xbc,
	0x03, 0x16, 0x40, 0x0d, 0x6f, 0x0d, 0x7f, 0xfa, 0x28, 0x4e, 0x74, 0x3c, 0x24, 0x08, 0xa8, 0xdb,
	0x10, 0xdf, 0x22, 0x95, 0x3f, 0xd2, 0xe0, 0xb4, 0x3a, 0xae, 0xc3, 0xd9, 0xd8, 0x7d, 0x9f, 0xd4,
	0xa9, 0x1d, 0x0d, 0xdb, 0x76, 0xbd, 0xb0, 0x33, 0xb3, 0x1b, 0xc4, 0xef, 0x12, 0xbc, 0xd0, 0x8f,
	0x5f, 0x35, 0xd7, 0x89, 0xed, 0x9b, 0x3b, 0x96, 0xcd, 0x3c, 0x5f, 0xae, 0x73, 0xc0, 0x41, 0xb7,
	0x11, 0xc2, 0x09, 0xf8, 0xc5, 0x26, 0x22, 0x10, 0x6f, 0x44, 0x81, 0x83, 0x04, 0xc1, 0xf2, 0xeb,
	0x47, 0x6d, 0xe3, 0xdb, 0x1f, 0xff, 0xf0, 0x4d, 0x69, 0xe3, 0x99, 0xc8, 0x46, 0x65, 0x54, 0xc8,
	0x77, 0x0e, 0x69, 0x4f, 0xf9, 0xed, 0xf8, 0x6a, 0x55, 0x23, 0xb6, 0xe7, 0x38, 0xc4, 0xad, 0xaf,
	0x32, 0xe2, 0xf4, 0x9e, 0xde, 0x29, 0x1f, 0x1b, 0xc9, 0xe9, 0x1d, 0x89, 0x3e, 0x34, 0x2a, 0xff,
	0xbb, 0xd6, 0x2d, 0x09, 0x3f, 0x0b, 0x7a, 0x00, 0xc3, 0x94, 0x11, 0x27, 0x1a, 0xae, 0xbf, 0x7e,
	0xdc, 0x36, 0x95, 0xb0, 0x63, 0x25, 0x1f, 0x7d, 0x31, 0xfd, 0xf1, 0xe3, 0xb9, 0xe1, 0x9a, 0x90,
	0x54, 0x32, 0x21, 0x73, 0xa3, 0xd5, 0x22, 0x6e, 0x5d, 0xbf, 0xfb, 0x42, 0xc2, 0x47, 0x1e, 0x3d,
	0x9e, 0x1b, 0xb4, 0xea, 0x75, 0x29, 0x17, 0x2b, 0xea, 0xdf, 0x3e, 0xfe, 0xe1, 0x9b, 0xcb, 0x17,
	0x8e, 0xda, 0xc6, 0xbf, 0x76, 0x82, 0x38, 0x13, 0x05, 0xd1, 0x8f, 0xd8, 0xb1, 0x2c, 0x57, 0x7e,
	0x0b, 0xae, 0xd8, 0x9e, 0xc3, 0xd5, 0xe0, 0xc7, 0xfe, 0xb6, 0xd7, 0x5c, 0x08, 0x88, 0xbf, 0x4f,
	0x6d, 0x12, 0xf4, 0x33, 0x64, 0x25, 0x7a, 0x01, 0x71, 0x9f, 0x73, 0xbc, 0xff, 0x66, 0xc3, 0x6b,
	0x5a, 0x6e, 0x63, 0xe1, 0x5a, 0x85, 0x93, 0x78, 0xce, 0xe5, 0x48, 0xd0, 0xe5, 0x48, 0xd0, 0xe5,
	0x3e, 0x7f, 0xae, 0xb0, 0x9d, 0x41, 0xd2, 0xab, 0xff, 0x1b, 0x00, 0x00, 0xff, 0xff, 0x6f, 0x66,
	0x91, 0x31, 0xd0, 0x30, 0x00, 0x00,
}
