// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/tt/cpid_channel_cpid.proto

package rcmd_persona_tt_cpid

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/rcmd/persona"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CpidProfileInfo struct {
	UPCpid               string   `protobuf:"bytes,1,opt,name=u_p_cpid,json=uPCpid,proto3" json:"u_p_cpid,omitempty"`
	UPChannelFlagIndex   float64  `protobuf:"fixed64,2,opt,name=u_p_channel_flag_index,json=uPChannelFlagIndex,proto3" json:"u_p_channel_flag_index,omitempty"`
	UPTgChannelTypeIndex float64  `protobuf:"fixed64,3,opt,name=u_p_tg_channel_type_index,json=uPTgChannelTypeIndex,proto3" json:"u_p_tg_channel_type_index,omitempty"`
	UPMediaNameIndex     float64  `protobuf:"fixed64,4,opt,name=u_p_media_name_index,json=uPMediaNameIndex,proto3" json:"u_p_media_name_index,omitempty"`
	UPCpidExposure_7D    int64    `protobuf:"varint,5,opt,name=u_p_cpid_exposure_7d,json=uPCpidExposure7d,proto3" json:"u_p_cpid_exposure_7d,omitempty"`
	UPCpidClick_7D       int64    `protobuf:"varint,6,opt,name=u_p_cpid_click_7d,json=uPCpidClick7d,proto3" json:"u_p_cpid_click_7d,omitempty"`
	UPCpidClickrate_7D   float64  `protobuf:"fixed64,7,opt,name=u_p_cpid_clickrate_7d,json=uPCpidClickrate7d,proto3" json:"u_p_cpid_clickrate_7d,omitempty"`
	UPCpidIm_7D          int64    `protobuf:"varint,8,opt,name=u_p_cpid_im_7d,json=uPCpidIm7d,proto3" json:"u_p_cpid_im_7d,omitempty"`
	UPCpidImrate_7D      float64  `protobuf:"fixed64,9,opt,name=u_p_cpid_imrate_7d,json=uPCpidImrate7d,proto3" json:"u_p_cpid_imrate_7d,omitempty"`
	UgcDuration          int64    `protobuf:"varint,10,opt,name=ugc_duration,json=ugcDuration,proto3" json:"ugc_duration,omitempty"`
	ActiveUserCount      int64    `protobuf:"varint,11,opt,name=active_user_count,json=activeUserCount,proto3" json:"active_user_count,omitempty"`
	ActiveUserProp       float64  `protobuf:"fixed64,12,opt,name=active_user_prop,json=activeUserProp,proto3" json:"active_user_prop,omitempty"`
	UPCpidIndex          float64  `protobuf:"fixed64,13,opt,name=u_p_cpid_index,json=uPCpidIndex,proto3" json:"u_p_cpid_index,omitempty"`
	Dt                   string   `protobuf:"bytes,14,opt,name=dt,proto3" json:"dt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CpidProfileInfo) Reset()         { *m = CpidProfileInfo{} }
func (m *CpidProfileInfo) String() string { return proto.CompactTextString(m) }
func (*CpidProfileInfo) ProtoMessage()    {}
func (*CpidProfileInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpid_channel_cpid_eb416f737a5fcd82, []int{0}
}
func (m *CpidProfileInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CpidProfileInfo.Unmarshal(m, b)
}
func (m *CpidProfileInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CpidProfileInfo.Marshal(b, m, deterministic)
}
func (dst *CpidProfileInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CpidProfileInfo.Merge(dst, src)
}
func (m *CpidProfileInfo) XXX_Size() int {
	return xxx_messageInfo_CpidProfileInfo.Size(m)
}
func (m *CpidProfileInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CpidProfileInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CpidProfileInfo proto.InternalMessageInfo

func (m *CpidProfileInfo) GetUPCpid() string {
	if m != nil {
		return m.UPCpid
	}
	return ""
}

func (m *CpidProfileInfo) GetUPChannelFlagIndex() float64 {
	if m != nil {
		return m.UPChannelFlagIndex
	}
	return 0
}

func (m *CpidProfileInfo) GetUPTgChannelTypeIndex() float64 {
	if m != nil {
		return m.UPTgChannelTypeIndex
	}
	return 0
}

func (m *CpidProfileInfo) GetUPMediaNameIndex() float64 {
	if m != nil {
		return m.UPMediaNameIndex
	}
	return 0
}

func (m *CpidProfileInfo) GetUPCpidExposure_7D() int64 {
	if m != nil {
		return m.UPCpidExposure_7D
	}
	return 0
}

func (m *CpidProfileInfo) GetUPCpidClick_7D() int64 {
	if m != nil {
		return m.UPCpidClick_7D
	}
	return 0
}

func (m *CpidProfileInfo) GetUPCpidClickrate_7D() float64 {
	if m != nil {
		return m.UPCpidClickrate_7D
	}
	return 0
}

func (m *CpidProfileInfo) GetUPCpidIm_7D() int64 {
	if m != nil {
		return m.UPCpidIm_7D
	}
	return 0
}

func (m *CpidProfileInfo) GetUPCpidImrate_7D() float64 {
	if m != nil {
		return m.UPCpidImrate_7D
	}
	return 0
}

func (m *CpidProfileInfo) GetUgcDuration() int64 {
	if m != nil {
		return m.UgcDuration
	}
	return 0
}

func (m *CpidProfileInfo) GetActiveUserCount() int64 {
	if m != nil {
		return m.ActiveUserCount
	}
	return 0
}

func (m *CpidProfileInfo) GetActiveUserProp() float64 {
	if m != nil {
		return m.ActiveUserProp
	}
	return 0
}

func (m *CpidProfileInfo) GetUPCpidIndex() float64 {
	if m != nil {
		return m.UPCpidIndex
	}
	return 0
}

func (m *CpidProfileInfo) GetDt() string {
	if m != nil {
		return m.Dt
	}
	return ""
}

func init() {
	proto.RegisterType((*CpidProfileInfo)(nil), "rcmd.persona.tt.cpid.CpidProfileInfo")
}

func init() {
	proto.RegisterFile("rcmd/persona/tt/cpid_channel_cpid.proto", fileDescriptor_cpid_channel_cpid_eb416f737a5fcd82)
}

var fileDescriptor_cpid_channel_cpid_eb416f737a5fcd82 = []byte{
	// 429 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x54, 0x92, 0x41, 0x6f, 0xd3, 0x30,
	0x18, 0x86, 0x95, 0xae, 0x94, 0xcd, 0xdd, 0xba, 0xd5, 0x2a, 0xc8, 0xec, 0x54, 0xc6, 0x81, 0x68,
	0x87, 0x14, 0xc1, 0x21, 0x12, 0xd7, 0x02, 0x52, 0x0f, 0xa0, 0x68, 0x1a, 0x67, 0xcb, 0xd8, 0x6e,
	0xb0, 0x48, 0x62, 0xcb, 0xb1, 0xd1, 0x7a, 0xe3, 0xc8, 0x99, 0xff, 0xd2, 0x1b, 0x7f, 0x87, 0xff,
	0x81, 0xbe, 0x2f, 0x69, 0xd7, 0x1e, 0xe3, 0xf7, 0x79, 0x5e, 0xdb, 0x5f, 0x4c, 0x5e, 0x7b, 0x59,
	0xab, 0x85, 0xd3, 0xbe, 0xb5, 0x8d, 0x58, 0x84, 0xb0, 0x90, 0xce, 0x28, 0x2e, 0xbf, 0x8b, 0xa6,
	0xd1, 0x15, 0x87, 0x8f, 0xcc, 0x79, 0x1b, 0x2c, 0x9d, 0x01, 0x98, 0xf5, 0x60, 0x16, 0x42, 0x06,
	0xd9, 0xf5, 0xf5, 0x91, 0x6e, 0x5d, 0x30, 0xb6, 0x69, 0x3b, 0xe3, 0xe6, 0xdf, 0x90, 0x5c, 0x2e,
	0x9d, 0x51, 0x85, 0xb7, 0x6b, 0x53, 0xe9, 0x55, 0xb3, 0xb6, 0x94, 0x91, 0xd3, 0xc8, 0x1d, 0xf6,
	0xb2, 0x64, 0x9e, 0xa4, 0x67, 0x77, 0xa3, 0x58, 0x00, 0x44, 0xdf, 0x92, 0xe7, 0x98, 0xf4, 0x3b,
	0xaf, 0x2b, 0x51, 0x72, 0xd3, 0x28, 0xfd, 0xc0, 0x06, 0xf3, 0x24, 0x4d, 0xee, 0x68, 0x2c, 0x96,
	0x5d, 0xf6, 0xa9, 0x12, 0xe5, 0x0a, 0x12, 0x9a, 0x93, 0x17, 0xe0, 0x84, 0x72, 0xaf, 0x85, 0x8d,
	0xd3, 0xbd, 0x76, 0x82, 0xda, 0x2c, 0x16, 0xf7, 0x65, 0x2f, 0xde, 0x6f, 0x9c, 0xee, 0xc4, 0x8c,
	0xcc, 0x40, 0xac, 0xb5, 0x32, 0x82, 0x37, 0xa2, 0xde, 0x39, 0x43, 0x74, 0xae, 0x62, 0xf1, 0x19,
	0x92, 0x2f, 0xa2, 0x3e, 0xe6, 0x71, 0x36, 0xfa, 0xc1, 0xd9, 0x36, 0x7a, 0xcd, 0x73, 0xc5, 0x9e,
	0xcc, 0x93, 0xf4, 0x04, 0x78, 0xb8, 0xc2, 0xc7, 0x3e, 0xc8, 0x15, 0x4d, 0xc9, 0x74, 0xcf, 0xcb,
	0xca, 0xc8, 0x1f, 0x00, 0x8f, 0x10, 0xbe, 0xe8, 0xe0, 0x25, 0xac, 0xe6, 0x8a, 0xbe, 0x21, 0xcf,
	0x8e, 0x49, 0x2f, 0x02, 0x56, 0x3f, 0xc5, 0xa3, 0x4c, 0x0f, 0x68, 0x48, 0x72, 0x45, 0x6f, 0xc8,
	0x64, 0x6f, 0x98, 0x1a, 0xd0, 0x53, 0x2c, 0x26, 0x1d, 0xba, 0xaa, 0x73, 0x45, 0x6f, 0x09, 0x3d,
	0x60, 0x76, 0x95, 0x67, 0x58, 0x39, 0xd9, 0x71, 0x7d, 0xdf, 0x4b, 0x72, 0x1e, 0x4b, 0xc9, 0x55,
	0xf4, 0x02, 0xfe, 0x1e, 0x23, 0xd8, 0x36, 0x8e, 0xa5, 0xfc, 0xd0, 0x2f, 0xd1, 0x5b, 0x32, 0x15,
	0x32, 0x98, 0x9f, 0x9a, 0xc7, 0x56, 0x7b, 0x2e, 0x6d, 0x6c, 0x02, 0x1b, 0x23, 0x77, 0xd9, 0x05,
	0x5f, 0x5b, 0xed, 0x97, 0xb0, 0x4c, 0x53, 0x72, 0x75, 0xc8, 0x3a, 0x6f, 0x1d, 0x3b, 0xef, 0x36,
	0x7e, 0x44, 0x0b, 0x6f, 0x1d, 0x7d, 0x75, 0x78, 0x11, 0x1c, 0xff, 0x05, 0x72, 0xe3, 0xfe, 0x80,
	0x38, 0xf9, 0x09, 0x19, 0xa8, 0xc0, 0x26, 0xf8, 0x54, 0x06, 0x2a, 0xbc, 0x9f, 0xfe, 0xda, 0xb2,
	0xbf, 0xc5, 0xef, 0x2d, 0x4b, 0xfe, 0x6c, 0xd9, 0x10, 0xdc, 0x6f, 0x23, 0x7c, 0x6e, 0xef, 0xfe,
	0x07, 0x00, 0x00, 0xff, 0xff, 0x80, 0xea, 0x6c, 0xdb, 0xcb, 0x02, 0x00, 0x00,
}
