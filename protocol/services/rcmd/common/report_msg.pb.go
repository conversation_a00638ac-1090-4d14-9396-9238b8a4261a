// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/common/report/report_msg.proto

package common // import "golang.52tt.com/protocol/services/rcmd/common"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UserFollowMsg_Source int32

const (
	UserFollowMsg_USER_OPERATION UserFollowMsg_Source = 0
	UserFollowMsg_SYNCHORNIZER   UserFollowMsg_Source = 1
	UserFollowMsg_FRIEND_VERIFY  UserFollowMsg_Source = 2
)

var UserFollowMsg_Source_name = map[int32]string{
	0: "USER_OPERATION",
	1: "SYNCHORNIZER",
	2: "FRIEND_VERIFY",
}
var UserFollowMsg_Source_value = map[string]int32{
	"USER_OPERATION": 0,
	"SYNCHORNIZER":   1,
	"FRIEND_VERIFY":  2,
}

func (x UserFollowMsg_Source) String() string {
	return proto.EnumName(UserFollowMsg_Source_name, int32(x))
}
func (UserFollowMsg_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_report_msg_aef0cfeac328f8e8, []int{2, 0}
}

// 1000001
type CommonLogMsg struct {
	Log                  string   `protobuf:"bytes,1,opt,name=log,proto3" json:"log,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonLogMsg) Reset()         { *m = CommonLogMsg{} }
func (m *CommonLogMsg) String() string { return proto.CompactTextString(m) }
func (*CommonLogMsg) ProtoMessage()    {}
func (*CommonLogMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_report_msg_aef0cfeac328f8e8, []int{0}
}
func (m *CommonLogMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonLogMsg.Unmarshal(m, b)
}
func (m *CommonLogMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonLogMsg.Marshal(b, m, deterministic)
}
func (dst *CommonLogMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonLogMsg.Merge(dst, src)
}
func (m *CommonLogMsg) XXX_Size() int {
	return xxx_messageInfo_CommonLogMsg.Size(m)
}
func (m *CommonLogMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonLogMsg.DiscardUnknown(m)
}

var xxx_messageInfo_CommonLogMsg proto.InternalMessageInfo

func (m *CommonLogMsg) GetLog() string {
	if m != nil {
		return m.Log
	}
	return ""
}

// 3000001 用户事件（含注销、封禁）
type UserEventMsg struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type                 uint32   `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	UpdateTs             uint32   `protobuf:"varint,3,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserEventMsg) Reset()         { *m = UserEventMsg{} }
func (m *UserEventMsg) String() string { return proto.CompactTextString(m) }
func (*UserEventMsg) ProtoMessage()    {}
func (*UserEventMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_report_msg_aef0cfeac328f8e8, []int{1}
}
func (m *UserEventMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserEventMsg.Unmarshal(m, b)
}
func (m *UserEventMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserEventMsg.Marshal(b, m, deterministic)
}
func (dst *UserEventMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserEventMsg.Merge(dst, src)
}
func (m *UserEventMsg) XXX_Size() int {
	return xxx_messageInfo_UserEventMsg.Size(m)
}
func (m *UserEventMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UserEventMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UserEventMsg proto.InternalMessageInfo

func (m *UserEventMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserEventMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *UserEventMsg) GetUpdateTs() uint32 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

// 3000002 用户关注
type UserFollowMsg struct {
	FromUserId           uint32               `protobuf:"varint,1,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id,omitempty"`
	ToUserId             uint32               `protobuf:"varint,2,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	IsFirstTime          bool                 `protobuf:"varint,3,opt,name=is_first_time,json=isFirstTime,proto3" json:"is_first_time,omitempty"`
	CreateAt             uint64               `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	IsDeleted            bool                 `protobuf:"varint,5,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	Source               UserFollowMsg_Source `protobuf:"varint,6,opt,name=source,proto3,enum=rcmd.common.UserFollowMsg_Source" json:"source,omitempty"`
	ClientSource         uint32               `protobuf:"varint,7,opt,name=client_source,json=clientSource,proto3" json:"client_source,omitempty"`
	ClientCustomSource   string               `protobuf:"bytes,8,opt,name=client_custom_source,json=clientCustomSource,proto3" json:"client_custom_source,omitempty"`
	IsRobot              bool                 `protobuf:"varint,9,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UserFollowMsg) Reset()         { *m = UserFollowMsg{} }
func (m *UserFollowMsg) String() string { return proto.CompactTextString(m) }
func (*UserFollowMsg) ProtoMessage()    {}
func (*UserFollowMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_report_msg_aef0cfeac328f8e8, []int{2}
}
func (m *UserFollowMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserFollowMsg.Unmarshal(m, b)
}
func (m *UserFollowMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserFollowMsg.Marshal(b, m, deterministic)
}
func (dst *UserFollowMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserFollowMsg.Merge(dst, src)
}
func (m *UserFollowMsg) XXX_Size() int {
	return xxx_messageInfo_UserFollowMsg.Size(m)
}
func (m *UserFollowMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UserFollowMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UserFollowMsg proto.InternalMessageInfo

func (m *UserFollowMsg) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *UserFollowMsg) GetToUserId() uint32 {
	if m != nil {
		return m.ToUserId
	}
	return 0
}

func (m *UserFollowMsg) GetIsFirstTime() bool {
	if m != nil {
		return m.IsFirstTime
	}
	return false
}

func (m *UserFollowMsg) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *UserFollowMsg) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

func (m *UserFollowMsg) GetSource() UserFollowMsg_Source {
	if m != nil {
		return m.Source
	}
	return UserFollowMsg_USER_OPERATION
}

func (m *UserFollowMsg) GetClientSource() uint32 {
	if m != nil {
		return m.ClientSource
	}
	return 0
}

func (m *UserFollowMsg) GetClientCustomSource() string {
	if m != nil {
		return m.ClientCustomSource
	}
	return ""
}

func (m *UserFollowMsg) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

// 3000003 用户行为（含黑产）
type UserBehaviorMsg struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BehaviorType         uint32   `protobuf:"varint,2,opt,name=behavior_type,json=behaviorType,proto3" json:"behavior_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBehaviorMsg) Reset()         { *m = UserBehaviorMsg{} }
func (m *UserBehaviorMsg) String() string { return proto.CompactTextString(m) }
func (*UserBehaviorMsg) ProtoMessage()    {}
func (*UserBehaviorMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_report_msg_aef0cfeac328f8e8, []int{3}
}
func (m *UserBehaviorMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBehaviorMsg.Unmarshal(m, b)
}
func (m *UserBehaviorMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBehaviorMsg.Marshal(b, m, deterministic)
}
func (dst *UserBehaviorMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBehaviorMsg.Merge(dst, src)
}
func (m *UserBehaviorMsg) XXX_Size() int {
	return xxx_messageInfo_UserBehaviorMsg.Size(m)
}
func (m *UserBehaviorMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBehaviorMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UserBehaviorMsg proto.InternalMessageInfo

func (m *UserBehaviorMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBehaviorMsg) GetBehaviorType() uint32 {
	if m != nil {
		return m.BehaviorType
	}
	return 0
}

// 3000004 用户地理信息
type UserRegionMsg struct {
	UserRegionInfo       []*UserRegionMsg_UserRegionInfo `protobuf:"bytes,1,rep,name=user_region_info,json=userRegionInfo,proto3" json:"user_region_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *UserRegionMsg) Reset()         { *m = UserRegionMsg{} }
func (m *UserRegionMsg) String() string { return proto.CompactTextString(m) }
func (*UserRegionMsg) ProtoMessage()    {}
func (*UserRegionMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_report_msg_aef0cfeac328f8e8, []int{4}
}
func (m *UserRegionMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRegionMsg.Unmarshal(m, b)
}
func (m *UserRegionMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRegionMsg.Marshal(b, m, deterministic)
}
func (dst *UserRegionMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRegionMsg.Merge(dst, src)
}
func (m *UserRegionMsg) XXX_Size() int {
	return xxx_messageInfo_UserRegionMsg.Size(m)
}
func (m *UserRegionMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRegionMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UserRegionMsg proto.InternalMessageInfo

func (m *UserRegionMsg) GetUserRegionInfo() []*UserRegionMsg_UserRegionInfo {
	if m != nil {
		return m.UserRegionInfo
	}
	return nil
}

type UserRegionMsg_UserRegionInfo struct {
	Country              string   `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	Province             string   `protobuf:"bytes,2,opt,name=province,proto3" json:"province,omitempty"`
	City                 string   `protobuf:"bytes,3,opt,name=city,proto3" json:"city,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRegionMsg_UserRegionInfo) Reset()         { *m = UserRegionMsg_UserRegionInfo{} }
func (m *UserRegionMsg_UserRegionInfo) String() string { return proto.CompactTextString(m) }
func (*UserRegionMsg_UserRegionInfo) ProtoMessage()    {}
func (*UserRegionMsg_UserRegionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_report_msg_aef0cfeac328f8e8, []int{4, 0}
}
func (m *UserRegionMsg_UserRegionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRegionMsg_UserRegionInfo.Unmarshal(m, b)
}
func (m *UserRegionMsg_UserRegionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRegionMsg_UserRegionInfo.Marshal(b, m, deterministic)
}
func (dst *UserRegionMsg_UserRegionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRegionMsg_UserRegionInfo.Merge(dst, src)
}
func (m *UserRegionMsg_UserRegionInfo) XXX_Size() int {
	return xxx_messageInfo_UserRegionMsg_UserRegionInfo.Size(m)
}
func (m *UserRegionMsg_UserRegionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRegionMsg_UserRegionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserRegionMsg_UserRegionInfo proto.InternalMessageInfo

func (m *UserRegionMsg_UserRegionInfo) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *UserRegionMsg_UserRegionInfo) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *UserRegionMsg_UserRegionInfo) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *UserRegionMsg_UserRegionInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func init() {
	proto.RegisterType((*CommonLogMsg)(nil), "rcmd.common.CommonLogMsg")
	proto.RegisterType((*UserEventMsg)(nil), "rcmd.common.UserEventMsg")
	proto.RegisterType((*UserFollowMsg)(nil), "rcmd.common.UserFollowMsg")
	proto.RegisterType((*UserBehaviorMsg)(nil), "rcmd.common.UserBehaviorMsg")
	proto.RegisterType((*UserRegionMsg)(nil), "rcmd.common.UserRegionMsg")
	proto.RegisterType((*UserRegionMsg_UserRegionInfo)(nil), "rcmd.common.UserRegionMsg.UserRegionInfo")
	proto.RegisterEnum("rcmd.common.UserFollowMsg_Source", UserFollowMsg_Source_name, UserFollowMsg_Source_value)
}

func init() {
	proto.RegisterFile("rcmd/common/report/report_msg.proto", fileDescriptor_report_msg_aef0cfeac328f8e8)
}

var fileDescriptor_report_msg_aef0cfeac328f8e8 = []byte{
	// 553 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x53, 0xcd, 0x8e, 0xd3, 0x30,
	0x10, 0x26, 0xdb, 0xd2, 0x4d, 0x67, 0xdb, 0x52, 0x2c, 0x0e, 0x61, 0x01, 0xa9, 0x64, 0x2f, 0xe5,
	0x40, 0x83, 0x16, 0x71, 0xe0, 0xb8, 0x3f, 0xa9, 0x36, 0x12, 0x74, 0xc1, 0xed, 0x22, 0xed, 0x5e,
	0xac, 0x6e, 0xe2, 0x06, 0x4b, 0x49, 0x26, 0xb2, 0x9d, 0xa2, 0x3e, 0x0d, 0xef, 0xc4, 0x13, 0x21,
	0x3b, 0x69, 0xd5, 0x0a, 0x4e, 0x9e, 0xf9, 0xe6, 0x9b, 0xf1, 0x37, 0x9e, 0x31, 0x9c, 0xc9, 0x38,
	0x4f, 0x82, 0x18, 0xf3, 0x1c, 0x8b, 0x40, 0xf2, 0x12, 0xa5, 0x6e, 0x0e, 0x96, 0xab, 0x74, 0x52,
	0x4a, 0xd4, 0x48, 0x4e, 0x0c, 0x69, 0x52, 0x93, 0xfc, 0x11, 0xf4, 0xae, 0xac, 0xf5, 0x05, 0xd3,
	0xaf, 0x2a, 0x25, 0x43, 0x68, 0x65, 0x98, 0x7a, 0xce, 0xc8, 0x19, 0x77, 0xa9, 0x31, 0xfd, 0xef,
	0xd0, 0xbb, 0x53, 0x5c, 0x86, 0x6b, 0x5e, 0xe8, 0x86, 0x51, 0x89, 0xc4, 0x32, 0xfa, 0xd4, 0x98,
	0x84, 0x40, 0x5b, 0x6f, 0x4a, 0xee, 0x1d, 0x59, 0xc8, 0xda, 0xe4, 0x15, 0x74, 0xab, 0x32, 0x59,
	0x6a, 0xce, 0xb4, 0xf2, 0x5a, 0x36, 0xe0, 0xd6, 0xc0, 0x42, 0xf9, 0xbf, 0x5b, 0xd0, 0x37, 0x35,
	0xa7, 0x98, 0x65, 0xf8, 0xcb, 0x14, 0x1d, 0x41, 0x6f, 0x25, 0x31, 0x67, 0x95, 0xe2, 0x92, 0xed,
	0xaa, 0x83, 0xc1, 0x0c, 0x31, 0x4a, 0xc8, 0x6b, 0x00, 0x8d, 0xbb, 0x78, 0x7d, 0x95, 0xab, 0xb1,
	0x89, 0xfa, 0xd0, 0x17, 0x8a, 0xad, 0x84, 0x54, 0x9a, 0x69, 0x91, 0x73, 0x7b, 0xa5, 0x4b, 0x4f,
	0x84, 0x9a, 0x1a, 0x6c, 0x21, 0x72, 0x2b, 0x29, 0x96, 0xdc, 0x48, 0x5a, 0x6a, 0xaf, 0x3d, 0x72,
	0xc6, 0x6d, 0xea, 0xd6, 0xc0, 0x85, 0x26, 0x6f, 0x00, 0x84, 0x62, 0x09, 0xcf, 0xb8, 0xe6, 0x89,
	0xf7, 0xd4, 0x66, 0x77, 0x85, 0xba, 0xae, 0x01, 0xf2, 0x19, 0x3a, 0x0a, 0x2b, 0x19, 0x73, 0xaf,
	0x33, 0x72, 0xc6, 0x83, 0xf3, 0xb7, 0x93, 0xbd, 0x47, 0x9c, 0x1c, 0xf4, 0x32, 0x99, 0x5b, 0x22,
	0x6d, 0x12, 0xc8, 0x19, 0xf4, 0xe3, 0x4c, 0xf0, 0x42, 0xb3, 0xa6, 0xc2, 0xb1, 0xd5, 0xde, 0xab,
	0xc1, 0x9a, 0x4c, 0x3e, 0xc0, 0x8b, 0x86, 0x14, 0x57, 0x4a, 0x63, 0xbe, 0xe5, 0xba, 0x76, 0x0e,
	0xa4, 0x8e, 0x5d, 0xd9, 0x50, 0x93, 0xf1, 0x12, 0x5c, 0xa1, 0x98, 0xc4, 0x47, 0xd4, 0x5e, 0xd7,
	0xca, 0x3d, 0x16, 0x8a, 0x1a, 0xd7, 0xbf, 0x80, 0x4e, 0x43, 0x22, 0x30, 0xb8, 0x9b, 0x87, 0x94,
	0xdd, 0x7e, 0x0b, 0xe9, 0xc5, 0x22, 0xba, 0x9d, 0x0d, 0x9f, 0x90, 0x21, 0xf4, 0xe6, 0xf7, 0xb3,
	0xab, 0x9b, 0x5b, 0x3a, 0x8b, 0x1e, 0x42, 0x3a, 0x74, 0xc8, 0x73, 0xe8, 0x4f, 0x69, 0x14, 0xce,
	0xae, 0xd9, 0x8f, 0x90, 0x46, 0xd3, 0xfb, 0xe1, 0x91, 0x7f, 0x03, 0xcf, 0x4c, 0x53, 0x97, 0xfc,
	0xe7, 0x72, 0x2d, 0x50, 0xfe, 0x7f, 0xee, 0x67, 0xd0, 0x7f, 0x6c, 0x08, 0x6c, 0x6f, 0x01, 0x7a,
	0x5b, 0x70, 0xb1, 0x29, 0xb9, 0xff, 0xc7, 0xa9, 0x67, 0x4d, 0x79, 0x2a, 0xb0, 0x30, 0x85, 0xe6,
	0x30, 0xb4, 0x63, 0x94, 0x16, 0x61, 0xa2, 0x58, 0xa1, 0xe7, 0x8c, 0x5a, 0xe3, 0x93, 0xf3, 0x77,
	0xff, 0xbc, 0xea, 0x2e, 0x6b, 0xcf, 0x8b, 0x8a, 0x15, 0xd2, 0x41, 0x75, 0xe0, 0x9f, 0x66, 0x30,
	0x38, 0x64, 0x10, 0x0f, 0x8e, 0x63, 0xac, 0x0a, 0x2d, 0x37, 0xcd, 0x36, 0x6f, 0x5d, 0x72, 0x0a,
	0x6e, 0x29, 0x71, 0x2d, 0x8a, 0xb8, 0x96, 0xdc, 0xa5, 0x3b, 0xdf, 0xec, 0x72, 0x2c, 0xf4, 0xc6,
	0xee, 0x4f, 0x97, 0x5a, 0x7b, 0xdb, 0x79, 0x7b, 0xd7, 0xf9, 0x65, 0xf0, 0xf0, 0x3e, 0xc5, 0x6c,
	0x59, 0xa4, 0x93, 0x4f, 0xe7, 0x5a, 0x1b, 0xc1, 0x81, 0xfd, 0x5b, 0x31, 0x66, 0x81, 0xe2, 0x72,
	0x2d, 0x62, 0xae, 0x82, 0xbd, 0xbf, 0xf8, 0xd8, 0xb1, 0xe1, 0x8f, 0x7f, 0x03, 0x00, 0x00, 0xff,
	0xff, 0xaf, 0xdd, 0x11, 0x68, 0xa1, 0x03, 0x00, 0x00,
}
