// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/rcmd-post-stream/common.proto

package common // import "golang.52tt.com/protocol/services/rcmd/rcmd_post_stream/common"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Pool int32

const (
	Pool_PoolUnknown          Pool = 0
	Pool_PoolNew              Pool = 1
	Pool_PoolNewMid           Pool = 2
	Pool_PoolHighInteract     Pool = 3
	Pool_PoolIndividualize    Pool = 4
	Pool_PoolHighQuality      Pool = 5
	Pool_PoolIndividualizeHot Pool = 6
	Pool_PoolIndividualize11  Pool = 7
	Pool_PoolNewAlgo          Pool = 8
)

var Pool_name = map[int32]string{
	0: "PoolUnknown",
	1: "PoolNew",
	2: "PoolNewMid",
	3: "PoolHighInteract",
	4: "PoolIndividualize",
	5: "PoolHighQuality",
	6: "PoolIndividualizeHot",
	7: "PoolIndividualize11",
	8: "PoolNewAlgo",
}
var Pool_value = map[string]int32{
	"PoolUnknown":          0,
	"PoolNew":              1,
	"PoolNewMid":           2,
	"PoolHighInteract":     3,
	"PoolIndividualize":    4,
	"PoolHighQuality":      5,
	"PoolIndividualizeHot": 6,
	"PoolIndividualize11":  7,
	"PoolNewAlgo":          8,
}

func (x Pool) String() string {
	return proto.EnumName(Pool_name, int32(x))
}
func (Pool) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_common_f8172c45324129c9, []int{0}
}

func init() {
	proto.RegisterEnum("rcmd.rcmd_post_stream.common.Pool", Pool_name, Pool_value)
}

func init() {
	proto.RegisterFile("rcmd/rcmd-post-stream/common.proto", fileDescriptor_common_f8172c45324129c9)
}

var fileDescriptor_common_f8172c45324129c9 = []byte{
	// 248 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x64, 0x90, 0xbf, 0x4b, 0x3b, 0x41,
	0x10, 0xc5, 0xbf, 0xf9, 0x1a, 0x13, 0x99, 0x80, 0x19, 0x27, 0x11, 0x2d, 0xac, 0x2c, 0x85, 0xdc,
	0x12, 0xc5, 0x56, 0xd4, 0x2a, 0x29, 0x0c, 0x5a, 0xd8, 0xd8, 0x84, 0x73, 0x6f, 0x39, 0x17, 0xf7,
	0x76, 0xc2, 0xde, 0x98, 0x43, 0xff, 0x34, 0xff, 0x3a, 0x99, 0xfc, 0x10, 0x21, 0xcd, 0x2c, 0xef,
	0xbd, 0x4f, 0xb1, 0xef, 0xc1, 0x79, 0xb2, 0x55, 0x61, 0xf4, 0x8c, 0x16, 0x5c, 0xcb, 0xa8, 0x96,
	0xe4, 0xf2, 0xca, 0x58, 0xae, 0x2a, 0x8e, 0xd9, 0x22, 0xb1, 0x30, 0x9d, 0x69, 0x9c, 0xe9, 0x99,
	0x2b, 0x33, 0x5f, 0x33, 0xd9, 0x9a, 0xb9, 0xf8, 0x6e, 0x41, 0xfb, 0x91, 0x39, 0x50, 0x1f, 0x7a,
	0xfa, 0x3e, 0xc7, 0xf7, 0xc8, 0x4d, 0xc4, 0x7f, 0xd4, 0x83, 0xae, 0x1a, 0x33, 0xd7, 0x60, 0x8b,
	0x0e, 0x01, 0x36, 0xe2, 0xc1, 0x17, 0xf8, 0x9f, 0x86, 0x80, 0xaa, 0x27, 0xbe, 0x7c, 0x9b, 0x46,
	0x71, 0x29, 0xb7, 0x82, 0x7b, 0x74, 0x0c, 0x47, 0xea, 0x4e, 0x63, 0xe1, 0x97, 0xbe, 0xf8, 0xc8,
	0x83, 0xff, 0x72, 0xd8, 0xa6, 0x01, 0xf4, 0xb7, 0xf0, 0x93, 0x9a, 0xf2, 0x89, 0xfb, 0x74, 0x0a,
	0xc3, 0x1d, 0x76, 0xc2, 0x82, 0x1d, 0x3a, 0x81, 0xc1, 0x4e, 0x32, 0x1e, 0x63, 0x77, 0xfb, 0xc5,
	0x99, 0x6b, 0xee, 0x42, 0xc9, 0x78, 0x70, 0x7f, 0xfb, 0x72, 0x53, 0x72, 0xc8, 0x63, 0x99, 0x5d,
	0x5f, 0x8a, 0x68, 0x25, 0xb3, 0xea, 0x6c, 0x39, 0x98, 0xda, 0xa5, 0xa5, 0xb7, 0xae, 0x36, 0xbf,
	0x13, 0xfd, 0xad, 0xbf, 0x99, 0xe8, 0xb5, 0xb3, 0xe2, 0xaf, 0x7e, 0x02, 0x00, 0x00, 0xff, 0xff,
	0xe1, 0x64, 0x05, 0x47, 0x49, 0x01, 0x00, 0x00,
}
