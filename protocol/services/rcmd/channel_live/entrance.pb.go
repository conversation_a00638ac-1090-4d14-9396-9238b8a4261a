// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/channel_live/entrance.proto

package channel_live // import "golang.52tt.com/protocol/services/rcmd/channel_live"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetChannelLiveListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelLiveListReq) Reset()         { *m = GetChannelLiveListReq{} }
func (m *GetChannelLiveListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelLiveListReq) ProtoMessage()    {}
func (*GetChannelLiveListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entrance_993c50d72ea43834, []int{0}
}
func (m *GetChannelLiveListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLiveListReq.Unmarshal(m, b)
}
func (m *GetChannelLiveListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLiveListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelLiveListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLiveListReq.Merge(dst, src)
}
func (m *GetChannelLiveListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelLiveListReq.Size(m)
}
func (m *GetChannelLiveListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLiveListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLiveListReq proto.InternalMessageInfo

func (m *GetChannelLiveListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetChannelLiveListRsp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelLiveListRsp) Reset()         { *m = GetChannelLiveListRsp{} }
func (m *GetChannelLiveListRsp) String() string { return proto.CompactTextString(m) }
func (*GetChannelLiveListRsp) ProtoMessage()    {}
func (*GetChannelLiveListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entrance_993c50d72ea43834, []int{1}
}
func (m *GetChannelLiveListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLiveListRsp.Unmarshal(m, b)
}
func (m *GetChannelLiveListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLiveListRsp.Marshal(b, m, deterministic)
}
func (dst *GetChannelLiveListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLiveListRsp.Merge(dst, src)
}
func (m *GetChannelLiveListRsp) XXX_Size() int {
	return xxx_messageInfo_GetChannelLiveListRsp.Size(m)
}
func (m *GetChannelLiveListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLiveListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLiveListRsp proto.InternalMessageInfo

func (m *GetChannelLiveListRsp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func init() {
	proto.RegisterType((*GetChannelLiveListReq)(nil), "rcmd.channel_live.GetChannelLiveListReq")
	proto.RegisterType((*GetChannelLiveListRsp)(nil), "rcmd.channel_live.GetChannelLiveListRsp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelLiveClient is the client API for ChannelLive service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelLiveClient interface {
	// 获取推荐直播房间
	GetChannelLive(ctx context.Context, in *GetChannelLiveListReq, opts ...grpc.CallOption) (*GetChannelLiveListRsp, error)
}

type channelLiveClient struct {
	cc *grpc.ClientConn
}

func NewChannelLiveClient(cc *grpc.ClientConn) ChannelLiveClient {
	return &channelLiveClient{cc}
}

func (c *channelLiveClient) GetChannelLive(ctx context.Context, in *GetChannelLiveListReq, opts ...grpc.CallOption) (*GetChannelLiveListRsp, error) {
	out := new(GetChannelLiveListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.channel_live.ChannelLive/GetChannelLive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelLiveServer is the server API for ChannelLive service.
type ChannelLiveServer interface {
	// 获取推荐直播房间
	GetChannelLive(context.Context, *GetChannelLiveListReq) (*GetChannelLiveListRsp, error)
}

func RegisterChannelLiveServer(s *grpc.Server, srv ChannelLiveServer) {
	s.RegisterService(&_ChannelLive_serviceDesc, srv)
}

func _ChannelLive_GetChannelLive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelLiveListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveServer).GetChannelLive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.channel_live.ChannelLive/GetChannelLive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveServer).GetChannelLive(ctx, req.(*GetChannelLiveListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelLive_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.channel_live.ChannelLive",
	HandlerType: (*ChannelLiveServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelLive",
			Handler:    _ChannelLive_GetChannelLive_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd/channel_live/entrance.proto",
}

func init() {
	proto.RegisterFile("rcmd/channel_live/entrance.proto", fileDescriptor_entrance_993c50d72ea43834)
}

var fileDescriptor_entrance_993c50d72ea43834 = []byte{
	// 191 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x28, 0x4a, 0xce, 0x4d,
	0xd1, 0x4f, 0xce, 0x48, 0xcc, 0xcb, 0x4b, 0xcd, 0x89, 0xcf, 0xc9, 0x2c, 0x4b, 0xd5, 0x4f, 0xcd,
	0x2b, 0x29, 0x4a, 0xcc, 0x4b, 0x4e, 0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x12, 0x04, 0xa9,
	0xd0, 0x43, 0x56, 0xa1, 0xa4, 0xc9, 0x25, 0xea, 0x9e, 0x5a, 0xe2, 0x0c, 0x11, 0xf2, 0xc9, 0x2c,
	0x4b, 0xf5, 0xc9, 0x2c, 0x2e, 0x09, 0x4a, 0x2d, 0x14, 0x12, 0xe0, 0x62, 0x2e, 0xcd, 0x4c, 0x91,
	0x60, 0x54, 0x60, 0xd4, 0xe0, 0x0d, 0x02, 0x31, 0x95, 0xcc, 0xb0, 0x2a, 0x2d, 0x2e, 0x10, 0x92,
	0xe5, 0xe2, 0x82, 0x99, 0x09, 0xd7, 0xc1, 0x09, 0x15, 0xf1, 0x4c, 0x31, 0x2a, 0xe6, 0xe2, 0x46,
	0xd2, 0x24, 0x94, 0xc2, 0xc5, 0x87, 0x6a, 0x8c, 0x90, 0x86, 0x1e, 0x86, 0xbb, 0xf4, 0xb0, 0x3a,
	0x4a, 0x8a, 0x48, 0x95, 0xc5, 0x05, 0x4e, 0xa6, 0x51, 0xc6, 0xe9, 0xf9, 0x39, 0x89, 0x79, 0xe9,
	0x7a, 0xa6, 0x46, 0x25, 0x25, 0x7a, 0xc9, 0xf9, 0xb9, 0xfa, 0xe0, 0x30, 0x48, 0xce, 0xcf, 0xd1,
	0x2f, 0x4e, 0x2d, 0x2a, 0xcb, 0x4c, 0x4e, 0x2d, 0xd6, 0xc7, 0x08, 0xb0, 0x24, 0x36, 0xb0, 0x22,
	0x63, 0x40, 0x00, 0x00, 0x00, 0xff, 0xff, 0x69, 0x00, 0x41, 0x52, 0x4c, 0x01, 0x00, 0x00,
}
