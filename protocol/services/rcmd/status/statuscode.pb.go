// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/status/statuscode.proto

package status // import "golang.52tt.com/protocol/services/rcmd/status"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type StatusCode int32

const (
	StatusCode_Successful StatusCode = 0
	StatusCode_OK         StatusCode = 200
	// Client error responses
	StatusCode_BadRequest       StatusCode = 400
	StatusCode_Unauthorized     StatusCode = 401
	StatusCode_Forbidden        StatusCode = 403
	StatusCode_NotFound         StatusCode = 404
	StatusCode_MethodNotAllowed StatusCode = 405
	StatusCode_NotAcceptable    StatusCode = 406
	StatusCode_RequestTimeout   StatusCode = 408
	StatusCode_Conflict         StatusCode = 409
	StatusCode_PayloadTooLarge  StatusCode = 413
	StatusCode_UnsupportedType  StatusCode = 415
	StatusCode_TooManyRequests  StatusCode = 429
	// Server error responses
	StatusCode_InternalServerError StatusCode = 500
	StatusCode_NotImplemented      StatusCode = 501
	StatusCode_BadGateway          StatusCode = 502
	StatusCode_ServiceUnavailable  StatusCode = 503
	StatusCode_GatewayTimeout      StatusCode = 504
	StatusCode_VersionNotSupported StatusCode = 505
)

var StatusCode_name = map[int32]string{
	0:   "Successful",
	200: "OK",
	400: "BadRequest",
	401: "Unauthorized",
	403: "Forbidden",
	404: "NotFound",
	405: "MethodNotAllowed",
	406: "NotAcceptable",
	408: "RequestTimeout",
	409: "Conflict",
	413: "PayloadTooLarge",
	415: "UnsupportedType",
	429: "TooManyRequests",
	500: "InternalServerError",
	501: "NotImplemented",
	502: "BadGateway",
	503: "ServiceUnavailable",
	504: "GatewayTimeout",
	505: "VersionNotSupported",
}
var StatusCode_value = map[string]int32{
	"Successful":          0,
	"OK":                  200,
	"BadRequest":          400,
	"Unauthorized":        401,
	"Forbidden":           403,
	"NotFound":            404,
	"MethodNotAllowed":    405,
	"NotAcceptable":       406,
	"RequestTimeout":      408,
	"Conflict":            409,
	"PayloadTooLarge":     413,
	"UnsupportedType":     415,
	"TooManyRequests":     429,
	"InternalServerError": 500,
	"NotImplemented":      501,
	"BadGateway":          502,
	"ServiceUnavailable":  503,
	"GatewayTimeout":      504,
	"VersionNotSupported": 505,
}

func (x StatusCode) String() string {
	return proto.EnumName(StatusCode_name, int32(x))
}
func (StatusCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_statuscode_20d65ed3321b74bc, []int{0}
}

func init() {
	proto.RegisterEnum("status.code.StatusCode", StatusCode_name, StatusCode_value)
}

func init() {
	proto.RegisterFile("rcmd/status/statuscode.proto", fileDescriptor_statuscode_20d65ed3321b74bc)
}

var fileDescriptor_statuscode_20d65ed3321b74bc = []byte{
	// 384 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x4c, 0x91, 0x4b, 0x6e, 0x14, 0x31,
	0x10, 0x86, 0x01, 0x4b, 0x3c, 0x0a, 0x32, 0x29, 0x1c, 0x10, 0x2c, 0x38, 0x01, 0x12, 0xd3, 0x12,
	0x88, 0x03, 0x90, 0x88, 0xa0, 0x08, 0x32, 0x20, 0xba, 0x87, 0x05, 0x3b, 0x8f, 0x5d, 0x99, 0xb4,
	0xe4, 0x76, 0x35, 0x76, 0x39, 0x51, 0xb3, 0xe3, 0x06, 0x20, 0x1e, 0x82, 0x05, 0xe2, 0x04, 0xdc,
	0x83, 0x03, 0xf1, 0xdc, 0xa1, 0x71, 0x7a, 0xa4, 0xac, 0x2c, 0x7f, 0x76, 0xd5, 0xf7, 0xab, 0x0a,
	0x6e, 0x45, 0xdb, 0xb9, 0x2a, 0x89, 0x91, 0x9c, 0xc6, 0xc3, 0xb2, 0xa3, 0x69, 0x1f, 0x59, 0x58,
	0x5f, 0x3e, 0x21, 0xd3, 0x15, 0xba, 0xfd, 0x46, 0x01, 0xd4, 0xe5, 0xbe, 0xc3, 0x8e, 0xf4, 0x04,
	0xa0, 0xce, 0xd6, 0x52, 0x4a, 0x07, 0xd9, 0xe3, 0x19, 0x7d, 0x01, 0xce, 0x3d, 0x7d, 0x8c, 0x3f,
	0xce, 0xea, 0x4d, 0x80, 0x6d, 0xe3, 0x9e, 0xd3, 0xab, 0x4c, 0x49, 0xf0, 0xad, 0xd2, 0x57, 0xe1,
	0xca, 0x3c, 0x98, 0x2c, 0x87, 0x1c, 0xdb, 0xd7, 0xe4, 0xf0, 0x9d, 0xd2, 0x13, 0xb8, 0xb4, 0xcb,
	0x71, 0xd1, 0x3a, 0x47, 0x01, 0xdf, 0x2b, 0xbd, 0x01, 0x17, 0x67, 0x2c, 0xbb, 0x9c, 0x83, 0xc3,
	0x0f, 0x4a, 0x5f, 0x07, 0xdc, 0x27, 0x39, 0x64, 0x37, 0x63, 0x79, 0xe0, 0x3d, 0x1f, 0x93, 0xc3,
	0x8f, 0x4a, 0x6b, 0xd8, 0x58, 0x01, 0x6b, 0xa9, 0x17, 0xb3, 0xf0, 0x84, 0x9f, 0x94, 0xde, 0x82,
	0xc9, 0xa8, 0x6a, 0xda, 0x8e, 0x38, 0x0b, 0x7e, 0x2e, 0xed, 0x76, 0x38, 0x1c, 0xf8, 0xd6, 0x0a,
	0x7e, 0x51, 0xfa, 0x1a, 0x6c, 0x3e, 0x33, 0x83, 0x67, 0xe3, 0x1a, 0xe6, 0x27, 0x26, 0x2e, 0x09,
	0xbf, 0x16, 0x3a, 0x0f, 0x29, 0xf7, 0x3d, 0x47, 0x21, 0xd7, 0x0c, 0x3d, 0xe1, 0xb7, 0x42, 0x1b,
	0xe6, 0x7d, 0x13, 0x86, 0xb1, 0x6d, 0xc2, 0xef, 0x4a, 0xdf, 0x84, 0xad, 0xbd, 0x20, 0x14, 0x83,
	0xf1, 0x35, 0xc5, 0x23, 0x8a, 0x0f, 0x63, 0xe4, 0x88, 0x3f, 0x8b, 0x7f, 0xc6, 0xb2, 0xd7, 0xf5,
	0x9e, 0x3a, 0x0a, 0x42, 0x0e, 0x7f, 0xa9, 0x71, 0x04, 0x8f, 0x8c, 0xd0, 0xb1, 0x19, 0xf0, 0xb7,
	0xd2, 0x37, 0x40, 0xaf, 0xea, 0x5a, 0x4b, 0xf3, 0x60, 0x8e, 0x4c, 0xeb, 0x4b, 0xfc, 0x3f, 0xa5,
	0x7c, 0xfc, 0xb6, 0x8e, 0xff, 0xb7, 0xd8, 0x5e, 0x50, 0x4c, 0x2d, 0x87, 0x19, 0x4b, 0xbd, 0x4e,
	0x88, 0xff, 0xd4, 0x76, 0xf5, 0xf2, 0xce, 0x92, 0xbd, 0x09, 0xcb, 0xe9, 0xfd, 0xbb, 0x22, 0x53,
	0xcb, 0x5d, 0x55, 0x36, 0x65, 0xd9, 0x57, 0xe9, 0x44, 0x90, 0xaa, 0x53, 0x2b, 0x5d, 0x9c, 0x2f,
	0xcf, 0xf7, 0xfe, 0x07, 0x00, 0x00, 0xff, 0xff, 0xb7, 0xeb, 0x60, 0xb8, 0xe8, 0x01, 0x00, 0x00,
}
