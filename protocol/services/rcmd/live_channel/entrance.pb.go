// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/live-channel/entrance.proto

package live_channel // import "golang.52tt.com/protocol/services/rcmd/live_channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetLiveChannelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveChannelReq) Reset()         { *m = GetLiveChannelReq{} }
func (m *GetLiveChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelReq) ProtoMessage()    {}
func (*GetLiveChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_entrance_07cd76951d9dbe0d, []int{0}
}
func (m *GetLiveChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveChannelReq.Unmarshal(m, b)
}
func (m *GetLiveChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveChannelReq.Merge(dst, src)
}
func (m *GetLiveChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveChannelReq.Size(m)
}
func (m *GetLiveChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveChannelReq proto.InternalMessageInfo

func (m *GetLiveChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetLiveChannelRsp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveChannelRsp) Reset()         { *m = GetLiveChannelRsp{} }
func (m *GetLiveChannelRsp) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelRsp) ProtoMessage()    {}
func (*GetLiveChannelRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_entrance_07cd76951d9dbe0d, []int{1}
}
func (m *GetLiveChannelRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveChannelRsp.Unmarshal(m, b)
}
func (m *GetLiveChannelRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveChannelRsp.Marshal(b, m, deterministic)
}
func (dst *GetLiveChannelRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveChannelRsp.Merge(dst, src)
}
func (m *GetLiveChannelRsp) XXX_Size() int {
	return xxx_messageInfo_GetLiveChannelRsp.Size(m)
}
func (m *GetLiveChannelRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveChannelRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveChannelRsp proto.InternalMessageInfo

func (m *GetLiveChannelRsp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func init() {
	proto.RegisterType((*GetLiveChannelReq)(nil), "rcmd.live_channel.GetLiveChannelReq")
	proto.RegisterType((*GetLiveChannelRsp)(nil), "rcmd.live_channel.GetLiveChannelRsp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// LiveChannelClient is the client API for LiveChannel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LiveChannelClient interface {
	// 获取推荐的直播房间
	GetLiveChannel(ctx context.Context, in *GetLiveChannelReq, opts ...grpc.CallOption) (*GetLiveChannelRsp, error)
}

type liveChannelClient struct {
	cc *grpc.ClientConn
}

func NewLiveChannelClient(cc *grpc.ClientConn) LiveChannelClient {
	return &liveChannelClient{cc}
}

func (c *liveChannelClient) GetLiveChannel(ctx context.Context, in *GetLiveChannelReq, opts ...grpc.CallOption) (*GetLiveChannelRsp, error) {
	out := new(GetLiveChannelRsp)
	err := c.cc.Invoke(ctx, "/rcmd.live_channel.LiveChannel/GetLiveChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LiveChannelServer is the server API for LiveChannel service.
type LiveChannelServer interface {
	// 获取推荐的直播房间
	GetLiveChannel(context.Context, *GetLiveChannelReq) (*GetLiveChannelRsp, error)
}

func RegisterLiveChannelServer(s *grpc.Server, srv LiveChannelServer) {
	s.RegisterService(&_LiveChannel_serviceDesc, srv)
}

func _LiveChannel_GetLiveChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiveChannelServer).GetLiveChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.live_channel.LiveChannel/GetLiveChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiveChannelServer).GetLiveChannel(ctx, req.(*GetLiveChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _LiveChannel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.live_channel.LiveChannel",
	HandlerType: (*LiveChannelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLiveChannel",
			Handler:    _LiveChannel_GetLiveChannel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd/live-channel/entrance.proto",
}

func init() {
	proto.RegisterFile("rcmd/live-channel/entrance.proto", fileDescriptor_entrance_07cd76951d9dbe0d)
}

var fileDescriptor_entrance_07cd76951d9dbe0d = []byte{
	// 192 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x28, 0x4a, 0xce, 0x4d,
	0xd1, 0xcf, 0xc9, 0x2c, 0x4b, 0xd5, 0x4d, 0xce, 0x48, 0xcc, 0xcb, 0x4b, 0xcd, 0xd1, 0x4f, 0xcd,
	0x2b, 0x29, 0x4a, 0xcc, 0x4b, 0x4e, 0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x12, 0x04, 0xa9,
	0xd0, 0x03, 0xa9, 0x88, 0x87, 0xaa, 0x50, 0x52, 0xe5, 0x12, 0x74, 0x4f, 0x2d, 0xf1, 0xc9, 0x2c,
	0x4b, 0x75, 0x86, 0x88, 0x04, 0xa5, 0x16, 0x0a, 0x09, 0x70, 0x31, 0x97, 0x66, 0xa6, 0x48, 0x30,
	0x2a, 0x30, 0x6a, 0xf0, 0x06, 0x81, 0x98, 0x4a, 0x46, 0x18, 0xca, 0x8a, 0x0b, 0x84, 0x64, 0xb9,
	0xb8, 0xa0, 0xc6, 0xc4, 0xc3, 0x55, 0x73, 0x42, 0x45, 0x3c, 0x53, 0x8c, 0xb2, 0xb9, 0xb8, 0x91,
	0x34, 0x08, 0xc5, 0x70, 0xf1, 0xa1, 0x1a, 0x21, 0xa4, 0xa2, 0x87, 0xe1, 0x1e, 0x3d, 0x0c, 0xc7,
	0x48, 0x11, 0xa1, 0xaa, 0xb8, 0xc0, 0xc9, 0x34, 0xca, 0x38, 0x3d, 0x3f, 0x27, 0x31, 0x2f, 0x5d,
	0xcf, 0xd4, 0xa8, 0xa4, 0x44, 0x2f, 0x39, 0x3f, 0x57, 0x1f, 0xec, 0xe7, 0xe4, 0xfc, 0x1c, 0xfd,
	0xe2, 0xd4, 0xa2, 0xb2, 0xcc, 0xe4, 0xd4, 0x62, 0x7d, 0x78, 0x00, 0xc1, 0x0c, 0x4a, 0x62, 0x03,
	0x2b, 0x32, 0x06, 0x04, 0x00, 0x00, 0xff, 0xff, 0xd4, 0xe8, 0x6c, 0x58, 0x3c, 0x01, 0x00, 0x00,
}
