// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/ttc-room/server.proto

package server // import "golang.52tt.com/protocol/services/rcmd/ttc_room/server"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetRecommendedReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsRefresh            bool     `protobuf:"varint,2,opt,name=is_refresh,json=isRefresh,proto3" json:"is_refresh,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendedReq) Reset()         { *m = GetRecommendedReq{} }
func (m *GetRecommendedReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendedReq) ProtoMessage()    {}
func (*GetRecommendedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_server_e4aca9d8c55f6d5c, []int{0}
}
func (m *GetRecommendedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendedReq.Unmarshal(m, b)
}
func (m *GetRecommendedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendedReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendedReq.Merge(dst, src)
}
func (m *GetRecommendedReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendedReq.Size(m)
}
func (m *GetRecommendedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendedReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendedReq proto.InternalMessageInfo

func (m *GetRecommendedReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendedReq) GetIsRefresh() bool {
	if m != nil {
		return m.IsRefresh
	}
	return false
}

func (m *GetRecommendedReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRecommendedRsp struct {
	ChannelList          []uint64 `protobuf:"varint,1,rep,packed,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendedRsp) Reset()         { *m = GetRecommendedRsp{} }
func (m *GetRecommendedRsp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendedRsp) ProtoMessage()    {}
func (*GetRecommendedRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_server_e4aca9d8c55f6d5c, []int{1}
}
func (m *GetRecommendedRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendedRsp.Unmarshal(m, b)
}
func (m *GetRecommendedRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendedRsp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendedRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendedRsp.Merge(dst, src)
}
func (m *GetRecommendedRsp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendedRsp.Size(m)
}
func (m *GetRecommendedRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendedRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendedRsp proto.InternalMessageInfo

func (m *GetRecommendedRsp) GetChannelList() []uint64 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func init() {
	proto.RegisterType((*GetRecommendedReq)(nil), "rcmd.ttc_room.server.GetRecommendedReq")
	proto.RegisterType((*GetRecommendedRsp)(nil), "rcmd.ttc_room.server.GetRecommendedRsp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RcmdTtcRoomServerClient is the client API for RcmdTtcRoomServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RcmdTtcRoomServerClient interface {
	GetRecommendedList(ctx context.Context, in *GetRecommendedReq, opts ...grpc.CallOption) (*GetRecommendedRsp, error)
}

type rcmdTtcRoomServerClient struct {
	cc *grpc.ClientConn
}

func NewRcmdTtcRoomServerClient(cc *grpc.ClientConn) RcmdTtcRoomServerClient {
	return &rcmdTtcRoomServerClient{cc}
}

func (c *rcmdTtcRoomServerClient) GetRecommendedList(ctx context.Context, in *GetRecommendedReq, opts ...grpc.CallOption) (*GetRecommendedRsp, error) {
	out := new(GetRecommendedRsp)
	err := c.cc.Invoke(ctx, "/rcmd.ttc_room.server.RcmdTtcRoomServer/GetRecommendedList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RcmdTtcRoomServerServer is the server API for RcmdTtcRoomServer service.
type RcmdTtcRoomServerServer interface {
	GetRecommendedList(context.Context, *GetRecommendedReq) (*GetRecommendedRsp, error)
}

func RegisterRcmdTtcRoomServerServer(s *grpc.Server, srv RcmdTtcRoomServerServer) {
	s.RegisterService(&_RcmdTtcRoomServer_serviceDesc, srv)
}

func _RcmdTtcRoomServer_GetRecommendedList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdTtcRoomServerServer).GetRecommendedList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.ttc_room.server.RcmdTtcRoomServer/GetRecommendedList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdTtcRoomServerServer).GetRecommendedList(ctx, req.(*GetRecommendedReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RcmdTtcRoomServer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.ttc_room.server.RcmdTtcRoomServer",
	HandlerType: (*RcmdTtcRoomServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecommendedList",
			Handler:    _RcmdTtcRoomServer_GetRecommendedList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd/ttc-room/server.proto",
}

func init() { proto.RegisterFile("rcmd/ttc-room/server.proto", fileDescriptor_server_e4aca9d8c55f6d5c) }

var fileDescriptor_server_e4aca9d8c55f6d5c = []byte{
	// 256 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x90, 0xb1, 0x4b, 0x03, 0x31,
	0x14, 0xc6, 0x89, 0x57, 0x45, 0xa3, 0x82, 0x0d, 0x1d, 0x8e, 0x82, 0x70, 0x76, 0xf1, 0x16, 0x73,
	0x50, 0xb1, 0x38, 0xbb, 0xb8, 0x38, 0x45, 0xa7, 0x2e, 0x47, 0x7d, 0x79, 0x6d, 0x03, 0xc9, 0xbd,
	0x33, 0x79, 0xba, 0xf8, 0xcf, 0xcb, 0xdd, 0x29, 0x28, 0x75, 0xe8, 0x96, 0x7c, 0xdf, 0xc7, 0xc7,
	0xfb, 0x7e, 0x72, 0x1a, 0x21, 0xd8, 0x8a, 0x19, 0x6e, 0x22, 0x51, 0xa8, 0x12, 0xc6, 0x0f, 0x8c,
	0xba, 0x8d, 0xc4, 0xa4, 0x26, 0x9d, 0xa7, 0x99, 0xa1, 0xee, 0x3c, 0x3d, 0x78, 0xb3, 0xa5, 0x1c,
	0x3f, 0x22, 0x1b, 0x04, 0x0a, 0x01, 0x1b, 0x8b, 0xd6, 0xe0, 0x9b, 0xba, 0x90, 0xd9, 0xbb, 0xb3,
	0xb9, 0x28, 0x44, 0x39, 0x32, 0xdd, 0x53, 0x5d, 0x4a, 0xe9, 0x52, 0x1d, 0x71, 0x1d, 0x31, 0x6d,
	0xf3, 0x83, 0x42, 0x94, 0xc7, 0xe6, 0xc4, 0x25, 0x33, 0x08, 0x6a, 0x22, 0x0f, 0xbd, 0x0b, 0x8e,
	0xf3, 0xac, 0x10, 0xe5, 0xb9, 0x19, 0x3e, 0xb3, 0xc5, 0x4e, 0x77, 0x6a, 0xd5, 0x95, 0x3c, 0x83,
	0xed, 0xaa, 0x69, 0xd0, 0xd7, 0xde, 0x25, 0xce, 0x45, 0x91, 0x95, 0x23, 0x73, 0xfa, 0xad, 0x3d,
	0xb9, 0xc4, 0xf3, 0x4f, 0x39, 0x36, 0x10, 0xec, 0x0b, 0x83, 0x21, 0x0a, 0xcf, 0xfd, 0xa1, 0x6a,
	0x2d, 0xd5, 0xdf, 0xb2, 0x2e, 0xaa, 0xae, 0xf5, 0x7f, 0xab, 0xf4, 0xce, 0xa4, 0xe9, 0x7e, 0xc1,
	0xd4, 0x3e, 0xdc, 0x2f, 0x17, 0x1b, 0xf2, 0xab, 0x66, 0xa3, 0xef, 0xe6, 0xcc, 0x1a, 0x28, 0x54,
	0x3d, 0x3f, 0x20, 0xdf, 0xf3, 0x74, 0x80, 0xa9, 0xfa, 0xc1, 0x5c, 0xff, 0xc2, 0xfc, 0x7a, 0xd4,
	0xe7, 0x6e, 0xbf, 0x02, 0x00, 0x00, 0xff, 0xff, 0x0d, 0x7a, 0x28, 0xfc, 0x85, 0x01, 0x00, 0x00,
}
