// Code generated by protoc-gen-gogo.
// source: src/wa/observer-cpp/waobserver.proto
// DO NOT EDIT!

/*
	Package wa_observer is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/wa/observer-cpp/waobserver.proto

	It has these top-level messages:
*/
package wa_observer

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

func init() { proto.RegisterFile("src/wa/observer-cpp/waobserver.proto", fileDescriptorWaobserver) }

var fileDescriptorWaobserver = []byte{
	// 132 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x29, 0x2e, 0x4a, 0xd6,
	0x2f, 0x4f, 0xd4, 0xcf, 0x4f, 0x2a, 0x4e, 0x2d, 0x2a, 0x4b, 0x2d, 0xd2, 0x4d, 0x2e, 0x28, 0xd0,
	0x2f, 0x4f, 0x84, 0x71, 0xf5, 0x0a, 0x8a, 0xf2, 0x4b, 0xf2, 0x85, 0xb8, 0xcb, 0x13, 0xf5, 0x60,
	0x42, 0x52, 0x2a, 0xc9, 0xf9, 0xb9, 0xb9, 0xf9, 0x79, 0xfa, 0x25, 0x39, 0x65, 0x05, 0x99, 0xc9,
	0xd9, 0x39, 0xa9, 0xfa, 0xc5, 0xd9, 0x49, 0xa5, 0x99, 0x39, 0x25, 0x99, 0x79, 0x25, 0x95, 0x05,
	0xa9, 0x10, 0x2d, 0x46, 0x22, 0x5c, 0x5c, 0xe1, 0x89, 0xfe, 0x30, 0x3d, 0x6c, 0x1d, 0x4b, 0x5e,
	0x30, 0xaf, 0xab, 0x72, 0x12, 0x38, 0xf1, 0x48, 0x8e, 0xf1, 0xc2, 0x23, 0x39, 0xc6, 0x07, 0x8f,
	0xe4, 0x18, 0x27, 0x3c, 0x96, 0x63, 0x00, 0x04, 0x00, 0x00, 0xff, 0xff, 0x35, 0xbc, 0xb7, 0x84,
	0x81, 0x00, 0x00, 0x00,
}
