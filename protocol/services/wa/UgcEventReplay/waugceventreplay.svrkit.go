package wa

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: src/wa/UgcEventReplay/waugceventreplay.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for WaUgcEventReplay service
const WaUgcEventReplayMagic = uint16(15664)

// Client API for WaUgcEventReplay service

type WaUgcEventReplayClientInterface interface {
}

type WaUgcEventReplayClient struct {
	cc *svrkit.ClientConn
}

func NewWaUgcEventReplayClient(cc *svrkit.ClientConn) WaUgcEventReplayClientInterface {
	return &WaUgcEventReplayClient{cc}
}

const (
	commandWaUgcEventReplayGetSelfSvnInfo = 9995
	commandWaUgcEventReplayEcho           = 9999
)
