// Code generated by protoc-gen-go. DO NOT EDIT.
// source: client-gen-push/client-gen-push.proto

package client_gen_push // import "golang.52tt.com/protocol/services/client-gen-push"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 拉取推送厂商的私信模板
type E_DEVICE_TYPE int32

const (
	E_DEVICE_TYPE_E_DEVICE_NULL E_DEVICE_TYPE = 0
	E_DEVICE_TYPE_E_OPPO        E_DEVICE_TYPE = 1
	E_DEVICE_TYPE_E_HUAWEI      E_DEVICE_TYPE = 2
	E_DEVICE_TYPE_E_VIVO        E_DEVICE_TYPE = 3
	E_DEVICE_TYPE_E_XIAOMI      E_DEVICE_TYPE = 4
	E_DEVICE_TYPE_E_HONOR       E_DEVICE_TYPE = 5
)

var E_DEVICE_TYPE_name = map[int32]string{
	0: "E_DEVICE_NULL",
	1: "E_OPPO",
	2: "E_HUAWEI",
	3: "E_VIVO",
	4: "E_XIAOMI",
	5: "E_HONOR",
}
var E_DEVICE_TYPE_value = map[string]int32{
	"E_DEVICE_NULL": 0,
	"E_OPPO":        1,
	"E_HUAWEI":      2,
	"E_VIVO":        3,
	"E_XIAOMI":      4,
	"E_HONOR":       5,
}

func (x E_DEVICE_TYPE) String() string {
	return proto.EnumName(E_DEVICE_TYPE_name, int32(x))
}
func (E_DEVICE_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{0}
}

type GetGenPushSwitchReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ESwitchType          []uint32 `protobuf:"varint,2,rep,packed,name=e_switch_type,json=eSwitchType,proto3" json:"e_switch_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGenPushSwitchReq) Reset()         { *m = GetGenPushSwitchReq{} }
func (m *GetGenPushSwitchReq) String() string { return proto.CompactTextString(m) }
func (*GetGenPushSwitchReq) ProtoMessage()    {}
func (*GetGenPushSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{0}
}
func (m *GetGenPushSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGenPushSwitchReq.Unmarshal(m, b)
}
func (m *GetGenPushSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGenPushSwitchReq.Marshal(b, m, deterministic)
}
func (dst *GetGenPushSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGenPushSwitchReq.Merge(dst, src)
}
func (m *GetGenPushSwitchReq) XXX_Size() int {
	return xxx_messageInfo_GetGenPushSwitchReq.Size(m)
}
func (m *GetGenPushSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGenPushSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGenPushSwitchReq proto.InternalMessageInfo

func (m *GetGenPushSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGenPushSwitchReq) GetESwitchType() []uint32 {
	if m != nil {
		return m.ESwitchType
	}
	return nil
}

type SPushSw struct {
	EPushType            uint32   `protobuf:"varint,1,opt,name=e_push_type,json=ePushType,proto3" json:"e_push_type,omitempty"`
	U32DailyLimitCnt     uint32   `protobuf:"varint,2,opt,name=u32_daily_limit_cnt,json=u32DailyLimitCnt,proto3" json:"u32_daily_limit_cnt,omitempty"`
	U32PushPeriodTs      []uint32 `protobuf:"varint,3,rep,packed,name=u32_push_period_ts,json=u32PushPeriodTs,proto3" json:"u32_push_period_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SPushSw) Reset()         { *m = SPushSw{} }
func (m *SPushSw) String() string { return proto.CompactTextString(m) }
func (*SPushSw) ProtoMessage()    {}
func (*SPushSw) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{1}
}
func (m *SPushSw) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SPushSw.Unmarshal(m, b)
}
func (m *SPushSw) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SPushSw.Marshal(b, m, deterministic)
}
func (dst *SPushSw) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SPushSw.Merge(dst, src)
}
func (m *SPushSw) XXX_Size() int {
	return xxx_messageInfo_SPushSw.Size(m)
}
func (m *SPushSw) XXX_DiscardUnknown() {
	xxx_messageInfo_SPushSw.DiscardUnknown(m)
}

var xxx_messageInfo_SPushSw proto.InternalMessageInfo

func (m *SPushSw) GetEPushType() uint32 {
	if m != nil {
		return m.EPushType
	}
	return 0
}

func (m *SPushSw) GetU32DailyLimitCnt() uint32 {
	if m != nil {
		return m.U32DailyLimitCnt
	}
	return 0
}

func (m *SPushSw) GetU32PushPeriodTs() []uint32 {
	if m != nil {
		return m.U32PushPeriodTs
	}
	return nil
}

type GetGenPushSwitchResp struct {
	ListSwitchItems      []*SPushSw `protobuf:"bytes,1,rep,name=listSwitchItems,proto3" json:"listSwitchItems,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetGenPushSwitchResp) Reset()         { *m = GetGenPushSwitchResp{} }
func (m *GetGenPushSwitchResp) String() string { return proto.CompactTextString(m) }
func (*GetGenPushSwitchResp) ProtoMessage()    {}
func (*GetGenPushSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{2}
}
func (m *GetGenPushSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGenPushSwitchResp.Unmarshal(m, b)
}
func (m *GetGenPushSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGenPushSwitchResp.Marshal(b, m, deterministic)
}
func (dst *GetGenPushSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGenPushSwitchResp.Merge(dst, src)
}
func (m *GetGenPushSwitchResp) XXX_Size() int {
	return xxx_messageInfo_GetGenPushSwitchResp.Size(m)
}
func (m *GetGenPushSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGenPushSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGenPushSwitchResp proto.InternalMessageInfo

func (m *GetGenPushSwitchResp) GetListSwitchItems() []*SPushSw {
	if m != nil {
		return m.ListSwitchItems
	}
	return nil
}

type RandomGetPushDocsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	U32GetCnt            uint32   `protobuf:"varint,2,opt,name=u32_get_cnt,json=u32GetCnt,proto3" json:"u32_get_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RandomGetPushDocsReq) Reset()         { *m = RandomGetPushDocsReq{} }
func (m *RandomGetPushDocsReq) String() string { return proto.CompactTextString(m) }
func (*RandomGetPushDocsReq) ProtoMessage()    {}
func (*RandomGetPushDocsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{3}
}
func (m *RandomGetPushDocsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RandomGetPushDocsReq.Unmarshal(m, b)
}
func (m *RandomGetPushDocsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RandomGetPushDocsReq.Marshal(b, m, deterministic)
}
func (dst *RandomGetPushDocsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RandomGetPushDocsReq.Merge(dst, src)
}
func (m *RandomGetPushDocsReq) XXX_Size() int {
	return xxx_messageInfo_RandomGetPushDocsReq.Size(m)
}
func (m *RandomGetPushDocsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RandomGetPushDocsReq.DiscardUnknown(m)
}

var xxx_messageInfo_RandomGetPushDocsReq proto.InternalMessageInfo

func (m *RandomGetPushDocsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RandomGetPushDocsReq) GetU32GetCnt() uint32 {
	if m != nil {
		return m.U32GetCnt
	}
	return 0
}

type SPushDoc struct {
	STitle               string   `protobuf:"bytes,1,opt,name=s_title,json=sTitle,proto3" json:"s_title,omitempty"`
	SContent             string   `protobuf:"bytes,2,opt,name=s_content,json=sContent,proto3" json:"s_content,omitempty"`
	SDeeplink            string   `protobuf:"bytes,3,opt,name=s_deeplink,json=sDeeplink,proto3" json:"s_deeplink,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SPushDoc) Reset()         { *m = SPushDoc{} }
func (m *SPushDoc) String() string { return proto.CompactTextString(m) }
func (*SPushDoc) ProtoMessage()    {}
func (*SPushDoc) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{4}
}
func (m *SPushDoc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SPushDoc.Unmarshal(m, b)
}
func (m *SPushDoc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SPushDoc.Marshal(b, m, deterministic)
}
func (dst *SPushDoc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SPushDoc.Merge(dst, src)
}
func (m *SPushDoc) XXX_Size() int {
	return xxx_messageInfo_SPushDoc.Size(m)
}
func (m *SPushDoc) XXX_DiscardUnknown() {
	xxx_messageInfo_SPushDoc.DiscardUnknown(m)
}

var xxx_messageInfo_SPushDoc proto.InternalMessageInfo

func (m *SPushDoc) GetSTitle() string {
	if m != nil {
		return m.STitle
	}
	return ""
}

func (m *SPushDoc) GetSContent() string {
	if m != nil {
		return m.SContent
	}
	return ""
}

func (m *SPushDoc) GetSDeeplink() string {
	if m != nil {
		return m.SDeeplink
	}
	return ""
}

type RandomGetPushDocsResp struct {
	ListDocs             []*SPushDoc `protobuf:"bytes,1,rep,name=list_docs,json=listDocs,proto3" json:"list_docs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *RandomGetPushDocsResp) Reset()         { *m = RandomGetPushDocsResp{} }
func (m *RandomGetPushDocsResp) String() string { return proto.CompactTextString(m) }
func (*RandomGetPushDocsResp) ProtoMessage()    {}
func (*RandomGetPushDocsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{5}
}
func (m *RandomGetPushDocsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RandomGetPushDocsResp.Unmarshal(m, b)
}
func (m *RandomGetPushDocsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RandomGetPushDocsResp.Marshal(b, m, deterministic)
}
func (dst *RandomGetPushDocsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RandomGetPushDocsResp.Merge(dst, src)
}
func (m *RandomGetPushDocsResp) XXX_Size() int {
	return xxx_messageInfo_RandomGetPushDocsResp.Size(m)
}
func (m *RandomGetPushDocsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RandomGetPushDocsResp.DiscardUnknown(m)
}

var xxx_messageInfo_RandomGetPushDocsResp proto.InternalMessageInfo

func (m *RandomGetPushDocsResp) GetListDocs() []*SPushDoc {
	if m != nil {
		return m.ListDocs
	}
	return nil
}

type GetPushFactoryPrivateTemplateReq struct {
	DeviceType           E_DEVICE_TYPE `protobuf:"varint,1,opt,name=deviceType,proto3,enum=client_gen_push.E_DEVICE_TYPE" json:"deviceType,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetPushFactoryPrivateTemplateReq) Reset()         { *m = GetPushFactoryPrivateTemplateReq{} }
func (m *GetPushFactoryPrivateTemplateReq) String() string { return proto.CompactTextString(m) }
func (*GetPushFactoryPrivateTemplateReq) ProtoMessage()    {}
func (*GetPushFactoryPrivateTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{6}
}
func (m *GetPushFactoryPrivateTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPushFactoryPrivateTemplateReq.Unmarshal(m, b)
}
func (m *GetPushFactoryPrivateTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPushFactoryPrivateTemplateReq.Marshal(b, m, deterministic)
}
func (dst *GetPushFactoryPrivateTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPushFactoryPrivateTemplateReq.Merge(dst, src)
}
func (m *GetPushFactoryPrivateTemplateReq) XXX_Size() int {
	return xxx_messageInfo_GetPushFactoryPrivateTemplateReq.Size(m)
}
func (m *GetPushFactoryPrivateTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPushFactoryPrivateTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPushFactoryPrivateTemplateReq proto.InternalMessageInfo

func (m *GetPushFactoryPrivateTemplateReq) GetDeviceType() E_DEVICE_TYPE {
	if m != nil {
		return m.DeviceType
	}
	return E_DEVICE_TYPE_E_DEVICE_NULL
}

type SPrivateTemplateInfo struct {
	TmplChannelId        string   `protobuf:"bytes,1,opt,name=tmplChannelId,proto3" json:"tmplChannelId,omitempty"`
	SupportMarketIds     []uint32 `protobuf:"varint,2,rep,packed,name=supportMarketIds,proto3" json:"supportMarketIds,omitempty"`
	TmplCategory         string   `protobuf:"bytes,3,opt,name=tmplCategory,proto3" json:"tmplCategory,omitempty"`
	TmplImportance       string   `protobuf:"bytes,4,opt,name=tmplImportance,proto3" json:"tmplImportance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SPrivateTemplateInfo) Reset()         { *m = SPrivateTemplateInfo{} }
func (m *SPrivateTemplateInfo) String() string { return proto.CompactTextString(m) }
func (*SPrivateTemplateInfo) ProtoMessage()    {}
func (*SPrivateTemplateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{7}
}
func (m *SPrivateTemplateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SPrivateTemplateInfo.Unmarshal(m, b)
}
func (m *SPrivateTemplateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SPrivateTemplateInfo.Marshal(b, m, deterministic)
}
func (dst *SPrivateTemplateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SPrivateTemplateInfo.Merge(dst, src)
}
func (m *SPrivateTemplateInfo) XXX_Size() int {
	return xxx_messageInfo_SPrivateTemplateInfo.Size(m)
}
func (m *SPrivateTemplateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SPrivateTemplateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SPrivateTemplateInfo proto.InternalMessageInfo

func (m *SPrivateTemplateInfo) GetTmplChannelId() string {
	if m != nil {
		return m.TmplChannelId
	}
	return ""
}

func (m *SPrivateTemplateInfo) GetSupportMarketIds() []uint32 {
	if m != nil {
		return m.SupportMarketIds
	}
	return nil
}

func (m *SPrivateTemplateInfo) GetTmplCategory() string {
	if m != nil {
		return m.TmplCategory
	}
	return ""
}

func (m *SPrivateTemplateInfo) GetTmplImportance() string {
	if m != nil {
		return m.TmplImportance
	}
	return ""
}

type SPrivateTemplateInfoList struct {
	PrivateTemplateInfos []*SPrivateTemplateInfo `protobuf:"bytes,1,rep,name=private_template_infos,json=privateTemplateInfos,proto3" json:"private_template_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SPrivateTemplateInfoList) Reset()         { *m = SPrivateTemplateInfoList{} }
func (m *SPrivateTemplateInfoList) String() string { return proto.CompactTextString(m) }
func (*SPrivateTemplateInfoList) ProtoMessage()    {}
func (*SPrivateTemplateInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{8}
}
func (m *SPrivateTemplateInfoList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SPrivateTemplateInfoList.Unmarshal(m, b)
}
func (m *SPrivateTemplateInfoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SPrivateTemplateInfoList.Marshal(b, m, deterministic)
}
func (dst *SPrivateTemplateInfoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SPrivateTemplateInfoList.Merge(dst, src)
}
func (m *SPrivateTemplateInfoList) XXX_Size() int {
	return xxx_messageInfo_SPrivateTemplateInfoList.Size(m)
}
func (m *SPrivateTemplateInfoList) XXX_DiscardUnknown() {
	xxx_messageInfo_SPrivateTemplateInfoList.DiscardUnknown(m)
}

var xxx_messageInfo_SPrivateTemplateInfoList proto.InternalMessageInfo

func (m *SPrivateTemplateInfoList) GetPrivateTemplateInfos() []*SPrivateTemplateInfo {
	if m != nil {
		return m.PrivateTemplateInfos
	}
	return nil
}

type SPushFacItem struct {
	DeviceType E_DEVICE_TYPE `protobuf:"varint,1,opt,name=deviceType,proto3,enum=client_gen_push.E_DEVICE_TYPE" json:"deviceType,omitempty"`
	// 改用 push_type_tmpl_info_list_map 字段
	PushMsgType2TmplInfo    map[uint32]*SPrivateTemplateInfo     `protobuf:"bytes,2,rep,name=pushMsgType2TmplInfo,proto3" json:"pushMsgType2TmplInfo,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	PushTypeTmplInfoListMap map[uint32]*SPrivateTemplateInfoList `protobuf:"bytes,3,rep,name=push_type_tmpl_info_list_map,json=pushTypeTmplInfoListMap,proto3" json:"push_type_tmpl_info_list_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral    struct{}                             `json:"-"`
	XXX_unrecognized        []byte                               `json:"-"`
	XXX_sizecache           int32                                `json:"-"`
}

func (m *SPushFacItem) Reset()         { *m = SPushFacItem{} }
func (m *SPushFacItem) String() string { return proto.CompactTextString(m) }
func (*SPushFacItem) ProtoMessage()    {}
func (*SPushFacItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{9}
}
func (m *SPushFacItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SPushFacItem.Unmarshal(m, b)
}
func (m *SPushFacItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SPushFacItem.Marshal(b, m, deterministic)
}
func (dst *SPushFacItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SPushFacItem.Merge(dst, src)
}
func (m *SPushFacItem) XXX_Size() int {
	return xxx_messageInfo_SPushFacItem.Size(m)
}
func (m *SPushFacItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SPushFacItem.DiscardUnknown(m)
}

var xxx_messageInfo_SPushFacItem proto.InternalMessageInfo

func (m *SPushFacItem) GetDeviceType() E_DEVICE_TYPE {
	if m != nil {
		return m.DeviceType
	}
	return E_DEVICE_TYPE_E_DEVICE_NULL
}

func (m *SPushFacItem) GetPushMsgType2TmplInfo() map[uint32]*SPrivateTemplateInfo {
	if m != nil {
		return m.PushMsgType2TmplInfo
	}
	return nil
}

func (m *SPushFacItem) GetPushTypeTmplInfoListMap() map[uint32]*SPrivateTemplateInfoList {
	if m != nil {
		return m.PushTypeTmplInfoListMap
	}
	return nil
}

type GetPushFactoryPrivateTemplateResp struct {
	PushFacItems         []*SPushFacItem `protobuf:"bytes,1,rep,name=pushFacItems,proto3" json:"pushFacItems,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetPushFactoryPrivateTemplateResp) Reset()         { *m = GetPushFactoryPrivateTemplateResp{} }
func (m *GetPushFactoryPrivateTemplateResp) String() string { return proto.CompactTextString(m) }
func (*GetPushFactoryPrivateTemplateResp) ProtoMessage()    {}
func (*GetPushFactoryPrivateTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{10}
}
func (m *GetPushFactoryPrivateTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPushFactoryPrivateTemplateResp.Unmarshal(m, b)
}
func (m *GetPushFactoryPrivateTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPushFactoryPrivateTemplateResp.Marshal(b, m, deterministic)
}
func (dst *GetPushFactoryPrivateTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPushFactoryPrivateTemplateResp.Merge(dst, src)
}
func (m *GetPushFactoryPrivateTemplateResp) XXX_Size() int {
	return xxx_messageInfo_GetPushFactoryPrivateTemplateResp.Size(m)
}
func (m *GetPushFactoryPrivateTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPushFactoryPrivateTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPushFactoryPrivateTemplateResp proto.InternalMessageInfo

func (m *GetPushFactoryPrivateTemplateResp) GetPushFacItems() []*SPushFacItem {
	if m != nil {
		return m.PushFacItems
	}
	return nil
}

type GetNewUserPushCntLimitConfigReq struct {
	// 不传拉取全部
	CpId                 string   `protobuf:"bytes,1,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,2,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewUserPushCntLimitConfigReq) Reset()         { *m = GetNewUserPushCntLimitConfigReq{} }
func (m *GetNewUserPushCntLimitConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetNewUserPushCntLimitConfigReq) ProtoMessage()    {}
func (*GetNewUserPushCntLimitConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{11}
}
func (m *GetNewUserPushCntLimitConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigReq.Unmarshal(m, b)
}
func (m *GetNewUserPushCntLimitConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetNewUserPushCntLimitConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewUserPushCntLimitConfigReq.Merge(dst, src)
}
func (m *GetNewUserPushCntLimitConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigReq.Size(m)
}
func (m *GetNewUserPushCntLimitConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewUserPushCntLimitConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewUserPushCntLimitConfigReq proto.InternalMessageInfo

func (m *GetNewUserPushCntLimitConfigReq) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *GetNewUserPushCntLimitConfigReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

type GetNewUserPushCntLimitConfigResp struct {
	GetAllConfigList     []*GetNewUserPushCntLimitConfigInfo `protobuf:"bytes,1,rep,name=get_all_config_list,json=getAllConfigList,proto3" json:"get_all_config_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetNewUserPushCntLimitConfigResp) Reset()         { *m = GetNewUserPushCntLimitConfigResp{} }
func (m *GetNewUserPushCntLimitConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetNewUserPushCntLimitConfigResp) ProtoMessage()    {}
func (*GetNewUserPushCntLimitConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{12}
}
func (m *GetNewUserPushCntLimitConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigResp.Unmarshal(m, b)
}
func (m *GetNewUserPushCntLimitConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetNewUserPushCntLimitConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewUserPushCntLimitConfigResp.Merge(dst, src)
}
func (m *GetNewUserPushCntLimitConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigResp.Size(m)
}
func (m *GetNewUserPushCntLimitConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewUserPushCntLimitConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewUserPushCntLimitConfigResp proto.InternalMessageInfo

func (m *GetNewUserPushCntLimitConfigResp) GetGetAllConfigList() []*GetNewUserPushCntLimitConfigInfo {
	if m != nil {
		return m.GetAllConfigList
	}
	return nil
}

type GetNewUserPushCntLimitConfigInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CpId                 string   `protobuf:"bytes,2,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	MarketId             uint32   `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,4,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	LimitTime            uint32   `protobuf:"varint,5,opt,name=limit_time,json=limitTime,proto3" json:"limit_time,omitempty"`
	LimitCnt             uint32   `protobuf:"varint,6,opt,name=limit_cnt,json=limitCnt,proto3" json:"limit_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewUserPushCntLimitConfigInfo) Reset()         { *m = GetNewUserPushCntLimitConfigInfo{} }
func (m *GetNewUserPushCntLimitConfigInfo) String() string { return proto.CompactTextString(m) }
func (*GetNewUserPushCntLimitConfigInfo) ProtoMessage()    {}
func (*GetNewUserPushCntLimitConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{13}
}
func (m *GetNewUserPushCntLimitConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigInfo.Unmarshal(m, b)
}
func (m *GetNewUserPushCntLimitConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigInfo.Marshal(b, m, deterministic)
}
func (dst *GetNewUserPushCntLimitConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewUserPushCntLimitConfigInfo.Merge(dst, src)
}
func (m *GetNewUserPushCntLimitConfigInfo) XXX_Size() int {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigInfo.Size(m)
}
func (m *GetNewUserPushCntLimitConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewUserPushCntLimitConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewUserPushCntLimitConfigInfo proto.InternalMessageInfo

func (m *GetNewUserPushCntLimitConfigInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetNewUserPushCntLimitConfigInfo) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *GetNewUserPushCntLimitConfigInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetNewUserPushCntLimitConfigInfo) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *GetNewUserPushCntLimitConfigInfo) GetLimitTime() uint32 {
	if m != nil {
		return m.LimitTime
	}
	return 0
}

func (m *GetNewUserPushCntLimitConfigInfo) GetLimitCnt() uint32 {
	if m != nil {
		return m.LimitCnt
	}
	return 0
}

// *
// 前端接口
type UpdateNewUserPushCntLimitConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CpId                 string   `protobuf:"bytes,2,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	CpLabel              string   `protobuf:"bytes,3,opt,name=cp_label,json=cpLabel,proto3" json:"cp_label,omitempty"`
	MarketIdName         string   `protobuf:"bytes,4,opt,name=market_id_name,json=marketIdName,proto3" json:"market_id_name,omitempty"`
	ClientVersion        string   `protobuf:"bytes,5,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	ConfigSw             bool     `protobuf:"varint,6,opt,name=config_sw,json=configSw,proto3" json:"config_sw,omitempty"`
	IsDelete             bool     `protobuf:"varint,7,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNewUserPushCntLimitConfigReq) Reset()         { *m = UpdateNewUserPushCntLimitConfigReq{} }
func (m *UpdateNewUserPushCntLimitConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateNewUserPushCntLimitConfigReq) ProtoMessage()    {}
func (*UpdateNewUserPushCntLimitConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{14}
}
func (m *UpdateNewUserPushCntLimitConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNewUserPushCntLimitConfigReq.Unmarshal(m, b)
}
func (m *UpdateNewUserPushCntLimitConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNewUserPushCntLimitConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateNewUserPushCntLimitConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNewUserPushCntLimitConfigReq.Merge(dst, src)
}
func (m *UpdateNewUserPushCntLimitConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateNewUserPushCntLimitConfigReq.Size(m)
}
func (m *UpdateNewUserPushCntLimitConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNewUserPushCntLimitConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNewUserPushCntLimitConfigReq proto.InternalMessageInfo

func (m *UpdateNewUserPushCntLimitConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateNewUserPushCntLimitConfigReq) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *UpdateNewUserPushCntLimitConfigReq) GetCpLabel() string {
	if m != nil {
		return m.CpLabel
	}
	return ""
}

func (m *UpdateNewUserPushCntLimitConfigReq) GetMarketIdName() string {
	if m != nil {
		return m.MarketIdName
	}
	return ""
}

func (m *UpdateNewUserPushCntLimitConfigReq) GetClientVersion() string {
	if m != nil {
		return m.ClientVersion
	}
	return ""
}

func (m *UpdateNewUserPushCntLimitConfigReq) GetConfigSw() bool {
	if m != nil {
		return m.ConfigSw
	}
	return false
}

func (m *UpdateNewUserPushCntLimitConfigReq) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

type UpdateNewUserPushCntLimitConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNewUserPushCntLimitConfigResp) Reset()         { *m = UpdateNewUserPushCntLimitConfigResp{} }
func (m *UpdateNewUserPushCntLimitConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateNewUserPushCntLimitConfigResp) ProtoMessage()    {}
func (*UpdateNewUserPushCntLimitConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{15}
}
func (m *UpdateNewUserPushCntLimitConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNewUserPushCntLimitConfigResp.Unmarshal(m, b)
}
func (m *UpdateNewUserPushCntLimitConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNewUserPushCntLimitConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateNewUserPushCntLimitConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNewUserPushCntLimitConfigResp.Merge(dst, src)
}
func (m *UpdateNewUserPushCntLimitConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateNewUserPushCntLimitConfigResp.Size(m)
}
func (m *UpdateNewUserPushCntLimitConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNewUserPushCntLimitConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNewUserPushCntLimitConfigResp proto.InternalMessageInfo

type GetNewUserPushCntLimitConfigByParamReq struct {
	MarketIdName         string   `protobuf:"bytes,1,opt,name=market_id_name,json=marketIdName,proto3" json:"market_id_name,omitempty"`
	CpLabel              string   `protobuf:"bytes,2,opt,name=cp_label,json=cpLabel,proto3" json:"cp_label,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewUserPushCntLimitConfigByParamReq) Reset() {
	*m = GetNewUserPushCntLimitConfigByParamReq{}
}
func (m *GetNewUserPushCntLimitConfigByParamReq) String() string { return proto.CompactTextString(m) }
func (*GetNewUserPushCntLimitConfigByParamReq) ProtoMessage()    {}
func (*GetNewUserPushCntLimitConfigByParamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{16}
}
func (m *GetNewUserPushCntLimitConfigByParamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigByParamReq.Unmarshal(m, b)
}
func (m *GetNewUserPushCntLimitConfigByParamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigByParamReq.Marshal(b, m, deterministic)
}
func (dst *GetNewUserPushCntLimitConfigByParamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewUserPushCntLimitConfigByParamReq.Merge(dst, src)
}
func (m *GetNewUserPushCntLimitConfigByParamReq) XXX_Size() int {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigByParamReq.Size(m)
}
func (m *GetNewUserPushCntLimitConfigByParamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewUserPushCntLimitConfigByParamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewUserPushCntLimitConfigByParamReq proto.InternalMessageInfo

func (m *GetNewUserPushCntLimitConfigByParamReq) GetMarketIdName() string {
	if m != nil {
		return m.MarketIdName
	}
	return ""
}

func (m *GetNewUserPushCntLimitConfigByParamReq) GetCpLabel() string {
	if m != nil {
		return m.CpLabel
	}
	return ""
}

func (m *GetNewUserPushCntLimitConfigByParamReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetNewUserPushCntLimitConfigByParamReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetNewUserPushCntLimitConfigByParamResp struct {
	ConfigInfoList       []*GetNewUserPushCntLimitConfigByParamInfo `protobuf:"bytes,1,rep,name=config_info_list,json=configInfoList,proto3" json:"config_info_list,omitempty"`
	Total                uint32                                     `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *GetNewUserPushCntLimitConfigByParamResp) Reset() {
	*m = GetNewUserPushCntLimitConfigByParamResp{}
}
func (m *GetNewUserPushCntLimitConfigByParamResp) String() string { return proto.CompactTextString(m) }
func (*GetNewUserPushCntLimitConfigByParamResp) ProtoMessage()    {}
func (*GetNewUserPushCntLimitConfigByParamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{17}
}
func (m *GetNewUserPushCntLimitConfigByParamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigByParamResp.Unmarshal(m, b)
}
func (m *GetNewUserPushCntLimitConfigByParamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigByParamResp.Marshal(b, m, deterministic)
}
func (dst *GetNewUserPushCntLimitConfigByParamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewUserPushCntLimitConfigByParamResp.Merge(dst, src)
}
func (m *GetNewUserPushCntLimitConfigByParamResp) XXX_Size() int {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigByParamResp.Size(m)
}
func (m *GetNewUserPushCntLimitConfigByParamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewUserPushCntLimitConfigByParamResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewUserPushCntLimitConfigByParamResp proto.InternalMessageInfo

func (m *GetNewUserPushCntLimitConfigByParamResp) GetConfigInfoList() []*GetNewUserPushCntLimitConfigByParamInfo {
	if m != nil {
		return m.ConfigInfoList
	}
	return nil
}

func (m *GetNewUserPushCntLimitConfigByParamResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetNewUserPushCntLimitConfigByParamInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CpId                 string   `protobuf:"bytes,2,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	CpLabel              string   `protobuf:"bytes,3,opt,name=cp_label,json=cpLabel,proto3" json:"cp_label,omitempty"`
	MarketIdName         string   `protobuf:"bytes,4,opt,name=market_id_name,json=marketIdName,proto3" json:"market_id_name,omitempty"`
	ClientVersion        string   `protobuf:"bytes,5,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	ConfigSw             bool     `protobuf:"varint,6,opt,name=config_sw,json=configSw,proto3" json:"config_sw,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewUserPushCntLimitConfigByParamInfo) Reset() {
	*m = GetNewUserPushCntLimitConfigByParamInfo{}
}
func (m *GetNewUserPushCntLimitConfigByParamInfo) String() string { return proto.CompactTextString(m) }
func (*GetNewUserPushCntLimitConfigByParamInfo) ProtoMessage()    {}
func (*GetNewUserPushCntLimitConfigByParamInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_client_gen_push_bd312662136e1b5f, []int{18}
}
func (m *GetNewUserPushCntLimitConfigByParamInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigByParamInfo.Unmarshal(m, b)
}
func (m *GetNewUserPushCntLimitConfigByParamInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigByParamInfo.Marshal(b, m, deterministic)
}
func (dst *GetNewUserPushCntLimitConfigByParamInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewUserPushCntLimitConfigByParamInfo.Merge(dst, src)
}
func (m *GetNewUserPushCntLimitConfigByParamInfo) XXX_Size() int {
	return xxx_messageInfo_GetNewUserPushCntLimitConfigByParamInfo.Size(m)
}
func (m *GetNewUserPushCntLimitConfigByParamInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewUserPushCntLimitConfigByParamInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewUserPushCntLimitConfigByParamInfo proto.InternalMessageInfo

func (m *GetNewUserPushCntLimitConfigByParamInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetNewUserPushCntLimitConfigByParamInfo) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *GetNewUserPushCntLimitConfigByParamInfo) GetCpLabel() string {
	if m != nil {
		return m.CpLabel
	}
	return ""
}

func (m *GetNewUserPushCntLimitConfigByParamInfo) GetMarketIdName() string {
	if m != nil {
		return m.MarketIdName
	}
	return ""
}

func (m *GetNewUserPushCntLimitConfigByParamInfo) GetClientVersion() string {
	if m != nil {
		return m.ClientVersion
	}
	return ""
}

func (m *GetNewUserPushCntLimitConfigByParamInfo) GetConfigSw() bool {
	if m != nil {
		return m.ConfigSw
	}
	return false
}

func (m *GetNewUserPushCntLimitConfigByParamInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func init() {
	proto.RegisterType((*GetGenPushSwitchReq)(nil), "client_gen_push.GetGenPushSwitchReq")
	proto.RegisterType((*SPushSw)(nil), "client_gen_push.SPushSw")
	proto.RegisterType((*GetGenPushSwitchResp)(nil), "client_gen_push.GetGenPushSwitchResp")
	proto.RegisterType((*RandomGetPushDocsReq)(nil), "client_gen_push.RandomGetPushDocsReq")
	proto.RegisterType((*SPushDoc)(nil), "client_gen_push.SPushDoc")
	proto.RegisterType((*RandomGetPushDocsResp)(nil), "client_gen_push.RandomGetPushDocsResp")
	proto.RegisterType((*GetPushFactoryPrivateTemplateReq)(nil), "client_gen_push.GetPushFactoryPrivateTemplateReq")
	proto.RegisterType((*SPrivateTemplateInfo)(nil), "client_gen_push.SPrivateTemplateInfo")
	proto.RegisterType((*SPrivateTemplateInfoList)(nil), "client_gen_push.SPrivateTemplateInfoList")
	proto.RegisterType((*SPushFacItem)(nil), "client_gen_push.SPushFacItem")
	proto.RegisterMapType((map[uint32]*SPrivateTemplateInfo)(nil), "client_gen_push.SPushFacItem.PushMsgType2TmplInfoEntry")
	proto.RegisterMapType((map[uint32]*SPrivateTemplateInfoList)(nil), "client_gen_push.SPushFacItem.PushTypeTmplInfoListMapEntry")
	proto.RegisterType((*GetPushFactoryPrivateTemplateResp)(nil), "client_gen_push.GetPushFactoryPrivateTemplateResp")
	proto.RegisterType((*GetNewUserPushCntLimitConfigReq)(nil), "client_gen_push.GetNewUserPushCntLimitConfigReq")
	proto.RegisterType((*GetNewUserPushCntLimitConfigResp)(nil), "client_gen_push.GetNewUserPushCntLimitConfigResp")
	proto.RegisterType((*GetNewUserPushCntLimitConfigInfo)(nil), "client_gen_push.GetNewUserPushCntLimitConfigInfo")
	proto.RegisterType((*UpdateNewUserPushCntLimitConfigReq)(nil), "client_gen_push.UpdateNewUserPushCntLimitConfigReq")
	proto.RegisterType((*UpdateNewUserPushCntLimitConfigResp)(nil), "client_gen_push.UpdateNewUserPushCntLimitConfigResp")
	proto.RegisterType((*GetNewUserPushCntLimitConfigByParamReq)(nil), "client_gen_push.GetNewUserPushCntLimitConfigByParamReq")
	proto.RegisterType((*GetNewUserPushCntLimitConfigByParamResp)(nil), "client_gen_push.GetNewUserPushCntLimitConfigByParamResp")
	proto.RegisterType((*GetNewUserPushCntLimitConfigByParamInfo)(nil), "client_gen_push.GetNewUserPushCntLimitConfigByParamInfo")
	proto.RegisterEnum("client_gen_push.E_DEVICE_TYPE", E_DEVICE_TYPE_name, E_DEVICE_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ClientGenPushClient is the client API for ClientGenPush service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ClientGenPushClient interface {
	GetGenPushSwitch(ctx context.Context, in *GetGenPushSwitchReq, opts ...grpc.CallOption) (*GetGenPushSwitchResp, error)
	RandomGetPushDocs(ctx context.Context, in *RandomGetPushDocsReq, opts ...grpc.CallOption) (*RandomGetPushDocsResp, error)
	GetPushFactoryPrivateTemplate(ctx context.Context, in *GetPushFactoryPrivateTemplateReq, opts ...grpc.CallOption) (*GetPushFactoryPrivateTemplateResp, error)
	// 获取配置
	GetNewUserPushCntLimitConfig(ctx context.Context, in *GetNewUserPushCntLimitConfigReq, opts ...grpc.CallOption) (*GetNewUserPushCntLimitConfigResp, error)
	// 更新配置
	UpdateNewUserPushCntLimitConfig(ctx context.Context, in *UpdateNewUserPushCntLimitConfigReq, opts ...grpc.CallOption) (*UpdateNewUserPushCntLimitConfigResp, error)
	// 获取配置
	GetNewUserPushCntLimitConfigByParma(ctx context.Context, in *GetNewUserPushCntLimitConfigByParamReq, opts ...grpc.CallOption) (*GetNewUserPushCntLimitConfigByParamResp, error)
}

type clientGenPushClient struct {
	cc *grpc.ClientConn
}

func NewClientGenPushClient(cc *grpc.ClientConn) ClientGenPushClient {
	return &clientGenPushClient{cc}
}

func (c *clientGenPushClient) GetGenPushSwitch(ctx context.Context, in *GetGenPushSwitchReq, opts ...grpc.CallOption) (*GetGenPushSwitchResp, error) {
	out := new(GetGenPushSwitchResp)
	err := c.cc.Invoke(ctx, "/client_gen_push.ClientGenPush/GetGenPushSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientGenPushClient) RandomGetPushDocs(ctx context.Context, in *RandomGetPushDocsReq, opts ...grpc.CallOption) (*RandomGetPushDocsResp, error) {
	out := new(RandomGetPushDocsResp)
	err := c.cc.Invoke(ctx, "/client_gen_push.ClientGenPush/RandomGetPushDocs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientGenPushClient) GetPushFactoryPrivateTemplate(ctx context.Context, in *GetPushFactoryPrivateTemplateReq, opts ...grpc.CallOption) (*GetPushFactoryPrivateTemplateResp, error) {
	out := new(GetPushFactoryPrivateTemplateResp)
	err := c.cc.Invoke(ctx, "/client_gen_push.ClientGenPush/GetPushFactoryPrivateTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientGenPushClient) GetNewUserPushCntLimitConfig(ctx context.Context, in *GetNewUserPushCntLimitConfigReq, opts ...grpc.CallOption) (*GetNewUserPushCntLimitConfigResp, error) {
	out := new(GetNewUserPushCntLimitConfigResp)
	err := c.cc.Invoke(ctx, "/client_gen_push.ClientGenPush/GetNewUserPushCntLimitConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientGenPushClient) UpdateNewUserPushCntLimitConfig(ctx context.Context, in *UpdateNewUserPushCntLimitConfigReq, opts ...grpc.CallOption) (*UpdateNewUserPushCntLimitConfigResp, error) {
	out := new(UpdateNewUserPushCntLimitConfigResp)
	err := c.cc.Invoke(ctx, "/client_gen_push.ClientGenPush/UpdateNewUserPushCntLimitConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientGenPushClient) GetNewUserPushCntLimitConfigByParma(ctx context.Context, in *GetNewUserPushCntLimitConfigByParamReq, opts ...grpc.CallOption) (*GetNewUserPushCntLimitConfigByParamResp, error) {
	out := new(GetNewUserPushCntLimitConfigByParamResp)
	err := c.cc.Invoke(ctx, "/client_gen_push.ClientGenPush/GetNewUserPushCntLimitConfigByParma", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ClientGenPushServer is the server API for ClientGenPush service.
type ClientGenPushServer interface {
	GetGenPushSwitch(context.Context, *GetGenPushSwitchReq) (*GetGenPushSwitchResp, error)
	RandomGetPushDocs(context.Context, *RandomGetPushDocsReq) (*RandomGetPushDocsResp, error)
	GetPushFactoryPrivateTemplate(context.Context, *GetPushFactoryPrivateTemplateReq) (*GetPushFactoryPrivateTemplateResp, error)
	// 获取配置
	GetNewUserPushCntLimitConfig(context.Context, *GetNewUserPushCntLimitConfigReq) (*GetNewUserPushCntLimitConfigResp, error)
	// 更新配置
	UpdateNewUserPushCntLimitConfig(context.Context, *UpdateNewUserPushCntLimitConfigReq) (*UpdateNewUserPushCntLimitConfigResp, error)
	// 获取配置
	GetNewUserPushCntLimitConfigByParma(context.Context, *GetNewUserPushCntLimitConfigByParamReq) (*GetNewUserPushCntLimitConfigByParamResp, error)
}

func RegisterClientGenPushServer(s *grpc.Server, srv ClientGenPushServer) {
	s.RegisterService(&_ClientGenPush_serviceDesc, srv)
}

func _ClientGenPush_GetGenPushSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGenPushSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientGenPushServer).GetGenPushSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/client_gen_push.ClientGenPush/GetGenPushSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientGenPushServer).GetGenPushSwitch(ctx, req.(*GetGenPushSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientGenPush_RandomGetPushDocs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RandomGetPushDocsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientGenPushServer).RandomGetPushDocs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/client_gen_push.ClientGenPush/RandomGetPushDocs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientGenPushServer).RandomGetPushDocs(ctx, req.(*RandomGetPushDocsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientGenPush_GetPushFactoryPrivateTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPushFactoryPrivateTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientGenPushServer).GetPushFactoryPrivateTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/client_gen_push.ClientGenPush/GetPushFactoryPrivateTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientGenPushServer).GetPushFactoryPrivateTemplate(ctx, req.(*GetPushFactoryPrivateTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientGenPush_GetNewUserPushCntLimitConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewUserPushCntLimitConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientGenPushServer).GetNewUserPushCntLimitConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/client_gen_push.ClientGenPush/GetNewUserPushCntLimitConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientGenPushServer).GetNewUserPushCntLimitConfig(ctx, req.(*GetNewUserPushCntLimitConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientGenPush_UpdateNewUserPushCntLimitConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNewUserPushCntLimitConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientGenPushServer).UpdateNewUserPushCntLimitConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/client_gen_push.ClientGenPush/UpdateNewUserPushCntLimitConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientGenPushServer).UpdateNewUserPushCntLimitConfig(ctx, req.(*UpdateNewUserPushCntLimitConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientGenPush_GetNewUserPushCntLimitConfigByParma_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewUserPushCntLimitConfigByParamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientGenPushServer).GetNewUserPushCntLimitConfigByParma(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/client_gen_push.ClientGenPush/GetNewUserPushCntLimitConfigByParma",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientGenPushServer).GetNewUserPushCntLimitConfigByParma(ctx, req.(*GetNewUserPushCntLimitConfigByParamReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ClientGenPush_serviceDesc = grpc.ServiceDesc{
	ServiceName: "client_gen_push.ClientGenPush",
	HandlerType: (*ClientGenPushServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGenPushSwitch",
			Handler:    _ClientGenPush_GetGenPushSwitch_Handler,
		},
		{
			MethodName: "RandomGetPushDocs",
			Handler:    _ClientGenPush_RandomGetPushDocs_Handler,
		},
		{
			MethodName: "GetPushFactoryPrivateTemplate",
			Handler:    _ClientGenPush_GetPushFactoryPrivateTemplate_Handler,
		},
		{
			MethodName: "GetNewUserPushCntLimitConfig",
			Handler:    _ClientGenPush_GetNewUserPushCntLimitConfig_Handler,
		},
		{
			MethodName: "UpdateNewUserPushCntLimitConfig",
			Handler:    _ClientGenPush_UpdateNewUserPushCntLimitConfig_Handler,
		},
		{
			MethodName: "GetNewUserPushCntLimitConfigByParma",
			Handler:    _ClientGenPush_GetNewUserPushCntLimitConfigByParma_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "client-gen-push/client-gen-push.proto",
}

func init() {
	proto.RegisterFile("client-gen-push/client-gen-push.proto", fileDescriptor_client_gen_push_bd312662136e1b5f)
}

var fileDescriptor_client_gen_push_bd312662136e1b5f = []byte{
	// 1358 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0xcb, 0x6f, 0x1b, 0xd5,
	0x1a, 0xef, 0xd8, 0x71, 0x62, 0x7f, 0x8e, 0x13, 0xf7, 0xc4, 0xb7, 0x75, 0xdd, 0x47, 0x72, 0xa7,
	0x4d, 0x6f, 0x6f, 0xaf, 0x9a, 0xdc, 0xda, 0x40, 0xab, 0x22, 0x81, 0xd2, 0xc4, 0xa4, 0x16, 0x49,
	0x6c, 0x4d, 0x9c, 0x00, 0x45, 0xe8, 0x30, 0x99, 0x39, 0x76, 0x46, 0x99, 0xc7, 0xc1, 0xe7, 0x38,
	0xc1, 0x7b, 0x2a, 0xb1, 0x62, 0x8b, 0x84, 0xc4, 0x8a, 0xbf, 0x00, 0x09, 0x89, 0x3f, 0x81, 0x0d,
	0xff, 0x09, 0x5b, 0x56, 0xac, 0xd0, 0x79, 0x38, 0x8d, 0xed, 0xc1, 0x76, 0xca, 0x86, 0x95, 0x7d,
	0xbe, 0xd7, 0xf9, 0xbe, 0xdf, 0xf7, 0x3a, 0x03, 0xab, 0x8e, 0xef, 0x91, 0x90, 0x3f, 0x6a, 0x93,
	0xf0, 0x11, 0xed, 0xb2, 0xe3, 0xf5, 0xa1, 0xf3, 0x1a, 0xed, 0x44, 0x3c, 0x42, 0x8b, 0x8a, 0x8c,
	0xdb, 0x24, 0xc4, 0x82, 0x5c, 0x5a, 0x26, 0x5f, 0x72, 0x12, 0x32, 0x2f, 0x0a, 0xd7, 0x23, 0xca,
	0xbd, 0x28, 0x64, 0xfd, 0x5f, 0xa5, 0x61, 0x7e, 0x08, 0x4b, 0xdb, 0x84, 0x6f, 0x93, 0xb0, 0xd1,
	0x65, 0xc7, 0xfb, 0x67, 0x1e, 0x77, 0x8e, 0x2d, 0xf2, 0x05, 0xca, 0x43, 0xb2, 0xeb, 0xb9, 0x45,
	0x63, 0xc5, 0x78, 0x90, 0xb3, 0xc4, 0x5f, 0x64, 0x42, 0x8e, 0x60, 0x26, 0x05, 0x30, 0xef, 0x51,
	0x52, 0x4c, 0xac, 0x24, 0x1f, 0xe4, 0xac, 0x2c, 0x51, 0x4a, 0xcd, 0x1e, 0x25, 0xe6, 0x2b, 0x03,
	0xe6, 0xf6, 0x95, 0x21, 0x74, 0x07, 0xb2, 0x44, 0xfa, 0xa0, 0xa4, 0x95, 0xa5, 0x0c, 0x11, 0x5c,
	0x21, 0x8b, 0x1e, 0xc1, 0x52, 0xb7, 0x52, 0xc6, 0xae, 0xed, 0xf9, 0x3d, 0xec, 0x7b, 0x81, 0xc7,
	0xb1, 0x13, 0xf2, 0x62, 0x42, 0xca, 0xe5, 0xbb, 0x95, 0xf2, 0x96, 0xe0, 0xec, 0x08, 0xc6, 0x66,
	0xc8, 0xd1, 0xff, 0x00, 0x09, 0x71, 0x69, 0x90, 0x92, 0x8e, 0x17, 0xb9, 0x98, 0xb3, 0x62, 0x52,
	0xfa, 0xb0, 0xd8, 0xad, 0x94, 0x85, 0xdd, 0x86, 0xa4, 0x37, 0x99, 0xf9, 0x12, 0x0a, 0xa3, 0x41,
	0x31, 0x8a, 0x9e, 0xc3, 0xa2, 0xef, 0x31, 0xae, 0x28, 0x35, 0x4e, 0x02, 0x56, 0x34, 0x56, 0x92,
	0x0f, 0xb2, 0xe5, 0xe2, 0xda, 0x10, 0x70, 0x6b, 0x3a, 0x0c, 0x6b, 0x58, 0xc1, 0x7c, 0x01, 0x05,
	0xcb, 0x0e, 0xdd, 0x28, 0xd8, 0x26, 0x5c, 0xc8, 0x6c, 0x45, 0x0e, 0x8b, 0x47, 0xec, 0x0e, 0x64,
	0x85, 0xcb, 0x6d, 0x72, 0x31, 0xb2, 0x4c, 0xb7, 0x52, 0xde, 0x26, 0x22, 0x24, 0x13, 0x43, 0x7a,
	0x5f, 0x5b, 0x40, 0xd7, 0x61, 0x8e, 0x61, 0xee, 0x71, 0x5f, 0x21, 0x95, 0xb1, 0x66, 0x59, 0x53,
	0x9c, 0xd0, 0x4d, 0xc8, 0x30, 0xec, 0x44, 0x21, 0x27, 0xda, 0x44, 0xc6, 0x4a, 0xb3, 0x4d, 0x75,
	0x46, 0xb7, 0x01, 0x18, 0x76, 0x09, 0xa1, 0xbe, 0x17, 0x9e, 0x14, 0x93, 0x92, 0x9b, 0x61, 0x5b,
	0x9a, 0x60, 0xd6, 0xe1, 0x5f, 0x31, 0xae, 0x32, 0x8a, 0xde, 0x81, 0x8c, 0x08, 0x0b, 0xbb, 0x91,
	0xd3, 0x47, 0xe0, 0x46, 0x3c, 0x02, 0x5b, 0x91, 0x63, 0xa5, 0x85, 0xac, 0xd0, 0x35, 0x8f, 0x60,
	0x45, 0x9b, 0xfa, 0xc0, 0x76, 0x78, 0xd4, 0xe9, 0x35, 0x3a, 0xde, 0xa9, 0xcd, 0x49, 0x93, 0x04,
	0xd4, 0xb7, 0x39, 0x11, 0x38, 0xbc, 0x07, 0xe0, 0x92, 0x53, 0xcf, 0x21, 0xcd, 0x7e, 0xda, 0x17,
	0xca, 0x77, 0x46, 0x8c, 0x57, 0xf1, 0x56, 0xf5, 0xb0, 0xb6, 0x59, 0xc5, 0xcd, 0x4f, 0x1a, 0x55,
	0xeb, 0x82, 0x86, 0xf9, 0x93, 0x01, 0x85, 0xfd, 0x21, 0xbb, 0xb5, 0xb0, 0x15, 0xa1, 0x7b, 0x90,
	0xe3, 0x01, 0xf5, 0x37, 0x8f, 0xed, 0x30, 0x24, 0x7e, 0xcd, 0xd5, 0x40, 0x0d, 0x12, 0xd1, 0x43,
	0xc8, 0xb3, 0x2e, 0xa5, 0x51, 0x87, 0xef, 0xda, 0x9d, 0x13, 0xc2, 0x6b, 0x2e, 0xd3, 0x95, 0x3a,
	0x42, 0x47, 0x26, 0xcc, 0x4b, 0x65, 0x9b, 0x93, 0x76, 0xd4, 0xe9, 0x69, 0x00, 0x07, 0x68, 0xe8,
	0x3e, 0x2c, 0x88, 0x73, 0x2d, 0x10, 0xaa, 0x76, 0xe8, 0x90, 0xe2, 0x8c, 0x94, 0x1a, 0xa2, 0x9a,
	0x67, 0x50, 0x8c, 0xf3, 0x7a, 0xc7, 0x63, 0x1c, 0x7d, 0x0a, 0xd7, 0xa8, 0x62, 0x61, 0xae, 0x79,
	0xd8, 0x0b, 0x5b, 0x51, 0x1f, 0xfb, 0xd5, 0x18, 0xec, 0x47, 0x4d, 0x59, 0x05, 0x3a, 0x4a, 0x64,
	0xe6, 0xf7, 0x33, 0x30, 0xbf, 0xaf, 0x53, 0x22, 0x2a, 0xf4, 0xef, 0x26, 0x00, 0x9d, 0x40, 0x41,
	0x48, 0xec, 0xb2, 0xb6, 0x38, 0x96, 0x9b, 0x22, 0xce, 0xb0, 0x15, 0x49, 0x14, 0xb3, 0xe5, 0x27,
	0xf1, 0x75, 0xa2, 0x2f, 0x5f, 0x6b, 0xc4, 0x68, 0x56, 0x43, 0xde, 0xe9, 0x59, 0xb1, 0x46, 0x51,
	0x0f, 0x6e, 0x9d, 0xcf, 0x08, 0x2c, 0x20, 0x95, 0xc0, 0x60, 0x59, 0x9d, 0x81, 0x4d, 0x65, 0x83,
	0x67, 0xcb, 0xcf, 0x26, 0x5f, 0x2a, 0xcc, 0xf6, 0xad, 0x0a, 0xd0, 0x77, 0x6d, 0xaa, 0xee, 0xbd,
	0x4e, 0xe3, 0xb9, 0xa5, 0x10, 0x6e, 0xfc, 0xa5, 0xb7, 0xa2, 0x9b, 0x4f, 0x48, 0xaf, 0xdf, 0xcd,
	0x27, 0xa4, 0x87, 0xde, 0x85, 0xd4, 0xa9, 0xed, 0x77, 0x89, 0x6c, 0xc2, 0xa9, 0x73, 0xa6, 0x74,
	0x9e, 0x25, 0x9e, 0x1a, 0xa5, 0x2e, 0xdc, 0x1a, 0xe7, 0x68, 0xcc, 0x95, 0xef, 0x0f, 0x5e, 0xf9,
	0xdf, 0xa9, 0xae, 0x14, 0x36, 0x2f, 0x5c, 0x6b, 0xb6, 0xe0, 0xdf, 0x13, 0x7a, 0x96, 0x51, 0xb4,
	0x01, 0xf3, 0xf4, 0x35, 0xa0, 0xfd, 0xba, 0xbc, 0x3d, 0x16, 0x76, 0x6b, 0x40, 0xc5, 0xfc, 0x0c,
	0x96, 0xb7, 0x09, 0xdf, 0x23, 0x67, 0x07, 0x8c, 0x74, 0x84, 0xd8, 0x66, 0xc8, 0xd5, 0xf4, 0x8e,
	0xc2, 0x96, 0xd7, 0x16, 0xa3, 0x61, 0x09, 0x52, 0x0e, 0xc5, 0x5e, 0xbf, 0x73, 0x67, 0x1c, 0x5a,
	0x73, 0xd1, 0x2a, 0x2c, 0xe8, 0x5b, 0x4e, 0x49, 0x47, 0x2c, 0x2a, 0x3d, 0x28, 0x73, 0x8a, 0x7a,
	0xa8, 0x88, 0xe6, 0x57, 0x86, 0x9c, 0x3d, 0x63, 0xec, 0x33, 0x8a, 0x3e, 0x87, 0x25, 0x31, 0x6d,
	0x6d, 0xdf, 0x17, 0x23, 0xb3, 0xe5, 0xb5, 0x65, 0x21, 0xe9, 0x68, 0x1e, 0x8f, 0x44, 0x33, 0xce,
	0x9e, 0xcc, 0x5e, 0xbe, 0x4d, 0xf8, 0x86, 0xef, 0x2b, 0x8a, 0x00, 0xd6, 0xfc, 0x65, 0x82, 0x1b,
	0xb2, 0xa8, 0x17, 0x20, 0x71, 0xbe, 0x09, 0x12, 0x9e, 0xfb, 0x3a, 0xee, 0xc4, 0x85, 0xb8, 0x6f,
	0x42, 0x26, 0x90, 0x93, 0x48, 0x30, 0x92, 0x52, 0x36, 0x1d, 0xe8, 0xd1, 0x14, 0x03, 0xca, 0x4c,
	0x0c, 0x28, 0x62, 0xfe, 0xab, 0xcd, 0xc9, 0xbd, 0x80, 0x14, 0x53, 0x6a, 0xc1, 0x48, 0x4a, 0xd3,
	0x0b, 0xe4, 0xee, 0x78, 0xbd, 0x58, 0x67, 0xd5, 0x15, 0xbe, 0x5e, 0xa8, 0xe6, 0x6f, 0x06, 0x98,
	0x07, 0xd4, 0xb5, 0x39, 0x19, 0x9b, 0xb3, 0xa9, 0x62, 0xb9, 0x01, 0x69, 0x87, 0x62, 0xdf, 0x3e,
	0x22, 0xbe, 0x1e, 0xa2, 0x73, 0x0e, 0xdd, 0x11, 0x47, 0x74, 0x0f, 0x16, 0xce, 0xc3, 0xc4, 0xa1,
	0x1d, 0xf4, 0xe7, 0xe7, 0x7c, 0x3f, 0xd6, 0x3d, 0x3b, 0x20, 0x31, 0xf1, 0xa6, 0xd4, 0x70, 0x1f,
	0x8c, 0xf7, 0x26, 0x64, 0x74, 0x5e, 0xd9, 0x99, 0x0c, 0x28, 0x6d, 0xa5, 0x15, 0x61, 0xff, 0x4c,
	0x30, 0x3d, 0xb1, 0x0d, 0x7d, 0xc2, 0x49, 0x71, 0x4e, 0x31, 0x3d, 0xb6, 0x25, 0xcf, 0xe6, 0x2a,
	0xdc, 0x9d, 0x18, 0x2c, 0xa3, 0xe6, 0xb7, 0x06, 0xdc, 0x1f, 0x97, 0xde, 0xe7, 0xbd, 0x86, 0xdd,
	0xb1, 0x03, 0x01, 0xcc, 0x68, 0x60, 0x46, 0x4c, 0x60, 0x17, 0x91, 0x49, 0x0c, 0x22, 0x73, 0x0d,
	0x66, 0xa3, 0x56, 0x8b, 0x11, 0xae, 0xb3, 0xaf, 0x4f, 0xa8, 0x00, 0x29, 0x99, 0x24, 0x9d, 0x72,
	0x75, 0x30, 0x7f, 0x30, 0xe0, 0x3f, 0x53, 0x79, 0xc6, 0x28, 0x3a, 0x82, 0xbc, 0x86, 0xe9, 0x7c,
	0x98, 0xea, 0x1e, 0x78, 0x7a, 0xa9, 0x1e, 0xd0, 0x36, 0x65, 0x2b, 0x2c, 0x38, 0xe7, 0xf5, 0x2d,
	0x77, 0x5a, 0x01, 0x52, 0x3c, 0xe2, 0xb6, 0xaf, 0xbb, 0x55, 0x1d, 0xcc, 0xdf, 0xa7, 0xf3, 0x72,
	0xfa, 0x2e, 0xf9, 0x47, 0x54, 0xd6, 0x32, 0x64, 0xbb, 0xb2, 0x78, 0x54, 0x9f, 0xcd, 0x49, 0x97,
	0x41, 0x91, 0x44, 0xa3, 0x3d, 0x74, 0x20, 0x37, 0xb0, 0x4f, 0xd1, 0xd5, 0x0b, 0x84, 0xbd, 0x83,
	0x9d, 0x9d, 0xfc, 0x15, 0x04, 0x30, 0x5b, 0xc5, 0xf5, 0x46, 0xa3, 0x9e, 0x37, 0xd0, 0x3c, 0xa4,
	0xab, 0xf8, 0xc5, 0xc1, 0xc6, 0x47, 0xd5, 0x5a, 0x3e, 0xa1, 0x38, 0x87, 0xb5, 0xc3, 0x7a, 0x3e,
	0xa9, 0x38, 0x1f, 0xd7, 0x36, 0xea, 0xbb, 0xb5, 0xfc, 0x0c, 0xca, 0xc2, 0x5c, 0x15, 0xbf, 0xa8,
	0xef, 0xd5, 0xad, 0x7c, 0xaa, 0xfc, 0xe3, 0x2c, 0xe4, 0x36, 0xa5, 0xd3, 0xfa, 0x61, 0x8b, 0x6c,
	0xc8, 0x0f, 0x3f, 0x73, 0xd1, 0xbd, 0xb8, 0x0c, 0x0f, 0x3f, 0xef, 0x4b, 0xab, 0x53, 0x48, 0x31,
	0x6a, 0x5e, 0x41, 0x2e, 0x5c, 0x1d, 0x79, 0x42, 0xa2, 0x51, 0xed, 0xb8, 0x17, 0x71, 0xe9, 0xfe,
	0x34, 0x62, 0xf2, 0x96, 0xaf, 0x0d, 0xb8, 0x3d, 0x76, 0x49, 0xa1, 0xd8, 0xe1, 0x3d, 0xf6, 0x21,
	0x5a, 0x2a, 0x5f, 0x56, 0x45, 0xba, 0xf2, 0xca, 0x80, 0x5b, 0xe3, 0x2a, 0x18, 0xfd, 0xff, 0x52,
	0x2d, 0x24, 0x1c, 0x79, 0x7c, 0x49, 0x0d, 0xe9, 0xc7, 0x37, 0x06, 0x2c, 0x4f, 0x98, 0x58, 0xa8,
	0x32, 0x62, 0x78, 0xf2, 0x40, 0x2f, 0xbd, 0x75, 0x79, 0x25, 0xe9, 0xd0, 0x77, 0x06, 0xdc, 0x9d,
	0xd8, 0xda, 0x81, 0x8d, 0x9e, 0xbc, 0xc9, 0x88, 0x11, 0x8e, 0x3d, 0x7d, 0x33, 0x45, 0xe1, 0x5c,
	0xe9, 0xda, 0x1f, 0x3f, 0xff, 0xda, 0xbc, 0x0a, 0x8b, 0x43, 0x5f, 0xc5, 0xcf, 0x2b, 0x2f, 0x1f,
	0xb7, 0x23, 0xdf, 0x0e, 0xdb, 0x6b, 0x6f, 0x97, 0x39, 0x5f, 0x73, 0xa2, 0x60, 0x5d, 0x7e, 0xf6,
	0x3a, 0x91, 0xbf, 0xce, 0x48, 0x47, 0xbc, 0x79, 0xd9, 0xf0, 0xa7, 0xf4, 0xd1, 0xac, 0x14, 0xa9,
	0xfc, 0x19, 0x00, 0x00, 0xff, 0xff, 0x5a, 0x47, 0x30, 0xc1, 0x74, 0x0f, 0x00, 0x00,
}
