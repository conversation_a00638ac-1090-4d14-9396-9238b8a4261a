// Code generated by protoc-gen-gogo.
// source: src/albumsvr/album.proto
// DO NOT EDIT!

/*
	Package Album is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/albumsvr/album.proto

	It has these top-level messages:
		StPhoto
		ChildAlbum
		CreateAlbumReq
		CreateAlbumResp
		GetNextAlbumIdResp
		CreatePhotoReq
		CreatePhotoResp
		GetChildAlbumListReq
		GetChildAlbumListResp
		GetChildAlbumPhotoCountReq
		GetChildAlbumPhotoCountResp
		GetChildAlbumPhotoListReq
		GetChildAlbumPhotoListResp
		DeleteChildAlbumReq
		DeleteChildAlbumResp
		DeletePhotoReq
		DeletePhotoResp
		GetPhotoReq
		GetPhotoResp
		GetChildAlbumReq
		GetChildAlbumResp
		ModifyAlbumNameReq
		ModifyAlbumThumbReq
		ModifyAlbumThumbResp
*/
package Album

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type UrlVersion int32

const (
	UrlVersion_URL_VERSION_DEFAULT UrlVersion = 0
	UrlVersion_URL_VERSION_QINIU   UrlVersion = 1
	UrlVersion_URL_VERSION_OBS     UrlVersion = 2
)

var UrlVersion_name = map[int32]string{
	0: "URL_VERSION_DEFAULT",
	1: "URL_VERSION_QINIU",
	2: "URL_VERSION_OBS",
}
var UrlVersion_value = map[string]int32{
	"URL_VERSION_DEFAULT": 0,
	"URL_VERSION_QINIU":   1,
	"URL_VERSION_OBS":     2,
}

func (x UrlVersion) Enum() *UrlVersion {
	p := new(UrlVersion)
	*p = x
	return p
}
func (x UrlVersion) String() string {
	return proto.EnumName(UrlVersion_name, int32(x))
}
func (x *UrlVersion) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UrlVersion_value, data, "UrlVersion")
	if err != nil {
		return err
	}
	*x = UrlVersion(value)
	return nil
}
func (UrlVersion) EnumDescriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{0} }

type StPhoto struct {
	ParentAlbumId uint32 `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
	ChildAlbumId  uint32 `protobuf:"varint,2,req,name=child_album_id,json=childAlbumId" json:"child_album_id"`
	Creator       uint32 `protobuf:"varint,3,req,name=creator" json:"creator"`
	ThumbUrl      string `protobuf:"bytes,4,req,name=thumb_url,json=thumbUrl" json:"thumb_url"`
	PhotoUrl      string `protobuf:"bytes,5,req,name=photo_url,json=photoUrl" json:"photo_url"`
	CreateAt      uint32 `protobuf:"varint,6,req,name=create_at,json=createAt" json:"create_at"`
	PhotoId       uint32 `protobuf:"varint,7,req,name=photo_id,json=photoId" json:"photo_id"`
	UrlVersion    uint32 `protobuf:"varint,8,opt,name=url_version,json=urlVersion" json:"url_version"`
}

func (m *StPhoto) Reset()                    { *m = StPhoto{} }
func (m *StPhoto) String() string            { return proto.CompactTextString(m) }
func (*StPhoto) ProtoMessage()               {}
func (*StPhoto) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{0} }

func (m *StPhoto) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

func (m *StPhoto) GetChildAlbumId() uint32 {
	if m != nil {
		return m.ChildAlbumId
	}
	return 0
}

func (m *StPhoto) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *StPhoto) GetThumbUrl() string {
	if m != nil {
		return m.ThumbUrl
	}
	return ""
}

func (m *StPhoto) GetPhotoUrl() string {
	if m != nil {
		return m.PhotoUrl
	}
	return ""
}

func (m *StPhoto) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *StPhoto) GetPhotoId() uint32 {
	if m != nil {
		return m.PhotoId
	}
	return 0
}

func (m *StPhoto) GetUrlVersion() uint32 {
	if m != nil {
		return m.UrlVersion
	}
	return 0
}

type ChildAlbum struct {
	ChildAlbumId   uint32 `protobuf:"varint,1,req,name=child_album_id,json=childAlbumId" json:"child_album_id"`
	Name           string `protobuf:"bytes,2,req,name=name" json:"name"`
	ThumbUrl       string `protobuf:"bytes,3,req,name=thumb_url,json=thumbUrl" json:"thumb_url"`
	IsDefault      bool   `protobuf:"varint,4,req,name=is_default,json=isDefault" json:"is_default"`
	CreateAt       uint32 `protobuf:"varint,5,req,name=create_at,json=createAt" json:"create_at"`
	PhotoCount     uint32 `protobuf:"varint,6,req,name=photo_count,json=photoCount" json:"photo_count"`
	LastUpdateTime uint32 `protobuf:"varint,7,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	Creator        uint32 `protobuf:"varint,8,req,name=creator" json:"creator"`
	UrlVersion     uint32 `protobuf:"varint,9,opt,name=url_version,json=urlVersion" json:"url_version"`
}

func (m *ChildAlbum) Reset()                    { *m = ChildAlbum{} }
func (m *ChildAlbum) String() string            { return proto.CompactTextString(m) }
func (*ChildAlbum) ProtoMessage()               {}
func (*ChildAlbum) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{1} }

func (m *ChildAlbum) GetChildAlbumId() uint32 {
	if m != nil {
		return m.ChildAlbumId
	}
	return 0
}

func (m *ChildAlbum) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChildAlbum) GetThumbUrl() string {
	if m != nil {
		return m.ThumbUrl
	}
	return ""
}

func (m *ChildAlbum) GetIsDefault() bool {
	if m != nil {
		return m.IsDefault
	}
	return false
}

func (m *ChildAlbum) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *ChildAlbum) GetPhotoCount() uint32 {
	if m != nil {
		return m.PhotoCount
	}
	return 0
}

func (m *ChildAlbum) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *ChildAlbum) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *ChildAlbum) GetUrlVersion() uint32 {
	if m != nil {
		return m.UrlVersion
	}
	return 0
}

// --------------------------------------
// 创建子相册
type CreateAlbumReq struct {
	ParentId  uint32 `protobuf:"varint,1,req,name=parent_id,json=parentId" json:"parent_id"`
	IsDefault bool   `protobuf:"varint,2,req,name=is_default,json=isDefault" json:"is_default"`
	Name      string `protobuf:"bytes,3,req,name=name" json:"name"`
	Creator   uint32 `protobuf:"varint,4,req,name=creator" json:"creator"`
}

func (m *CreateAlbumReq) Reset()                    { *m = CreateAlbumReq{} }
func (m *CreateAlbumReq) String() string            { return proto.CompactTextString(m) }
func (*CreateAlbumReq) ProtoMessage()               {}
func (*CreateAlbumReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{2} }

func (m *CreateAlbumReq) GetParentId() uint32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *CreateAlbumReq) GetIsDefault() bool {
	if m != nil {
		return m.IsDefault
	}
	return false
}

func (m *CreateAlbumReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateAlbumReq) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

type CreateAlbumResp struct {
	ParentId uint32 `protobuf:"varint,1,req,name=parent_id,json=parentId" json:"parent_id"`
	SecondId uint32 `protobuf:"varint,2,req,name=second_id,json=secondId" json:"second_id"`
}

func (m *CreateAlbumResp) Reset()                    { *m = CreateAlbumResp{} }
func (m *CreateAlbumResp) String() string            { return proto.CompactTextString(m) }
func (*CreateAlbumResp) ProtoMessage()               {}
func (*CreateAlbumResp) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{3} }

func (m *CreateAlbumResp) GetParentId() uint32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *CreateAlbumResp) GetSecondId() uint32 {
	if m != nil {
		return m.SecondId
	}
	return 0
}

// --------------------------------------
// 生成主相册id
type GetNextAlbumIdResp struct {
	AlbumId uint32 `protobuf:"varint,1,req,name=album_id,json=albumId" json:"album_id"`
}

func (m *GetNextAlbumIdResp) Reset()                    { *m = GetNextAlbumIdResp{} }
func (m *GetNextAlbumIdResp) String() string            { return proto.CompactTextString(m) }
func (*GetNextAlbumIdResp) ProtoMessage()               {}
func (*GetNextAlbumIdResp) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{4} }

func (m *GetNextAlbumIdResp) GetAlbumId() uint32 {
	if m != nil {
		return m.AlbumId
	}
	return 0
}

// --------------------------------------
// 创建相片
type CreatePhotoReq struct {
	ParentAlbumId uint32     `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
	ChildAlbumId  uint32     `protobuf:"varint,2,req,name=child_album_id,json=childAlbumId" json:"child_album_id"`
	PhotoList     []*StPhoto `protobuf:"bytes,3,rep,name=photo_list,json=photoList" json:"photo_list,omitempty"`
}

func (m *CreatePhotoReq) Reset()                    { *m = CreatePhotoReq{} }
func (m *CreatePhotoReq) String() string            { return proto.CompactTextString(m) }
func (*CreatePhotoReq) ProtoMessage()               {}
func (*CreatePhotoReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{5} }

func (m *CreatePhotoReq) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

func (m *CreatePhotoReq) GetChildAlbumId() uint32 {
	if m != nil {
		return m.ChildAlbumId
	}
	return 0
}

func (m *CreatePhotoReq) GetPhotoList() []*StPhoto {
	if m != nil {
		return m.PhotoList
	}
	return nil
}

type CreatePhotoResp struct {
	PhotoIdList []uint32 `protobuf:"varint,1,rep,name=photo_id_list,json=photoIdList" json:"photo_id_list,omitempty"`
}

func (m *CreatePhotoResp) Reset()                    { *m = CreatePhotoResp{} }
func (m *CreatePhotoResp) String() string            { return proto.CompactTextString(m) }
func (*CreatePhotoResp) ProtoMessage()               {}
func (*CreatePhotoResp) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{6} }

func (m *CreatePhotoResp) GetPhotoIdList() []uint32 {
	if m != nil {
		return m.PhotoIdList
	}
	return nil
}

// --------------------------------------
// 查相册列表
type GetChildAlbumListReq struct {
	ParentAlbumId uint32 `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
	Offset        uint32 `protobuf:"varint,2,req,name=offset" json:"offset"`
	Size_         uint32 `protobuf:"varint,3,req,name=size" json:"size"`
}

func (m *GetChildAlbumListReq) Reset()                    { *m = GetChildAlbumListReq{} }
func (m *GetChildAlbumListReq) String() string            { return proto.CompactTextString(m) }
func (*GetChildAlbumListReq) ProtoMessage()               {}
func (*GetChildAlbumListReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{7} }

func (m *GetChildAlbumListReq) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

func (m *GetChildAlbumListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetChildAlbumListReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

type GetChildAlbumListResp struct {
	ChildAlbumList []*ChildAlbum `protobuf:"bytes,1,rep,name=child_album_list,json=childAlbumList" json:"child_album_list,omitempty"`
}

func (m *GetChildAlbumListResp) Reset()                    { *m = GetChildAlbumListResp{} }
func (m *GetChildAlbumListResp) String() string            { return proto.CompactTextString(m) }
func (*GetChildAlbumListResp) ProtoMessage()               {}
func (*GetChildAlbumListResp) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{8} }

func (m *GetChildAlbumListResp) GetChildAlbumList() []*ChildAlbum {
	if m != nil {
		return m.ChildAlbumList
	}
	return nil
}

// --------------------------------------
// 查相册照片数量
type GetChildAlbumPhotoCountReq struct {
	ParentAlbumId uint32 `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
}

func (m *GetChildAlbumPhotoCountReq) Reset()                    { *m = GetChildAlbumPhotoCountReq{} }
func (m *GetChildAlbumPhotoCountReq) String() string            { return proto.CompactTextString(m) }
func (*GetChildAlbumPhotoCountReq) ProtoMessage()               {}
func (*GetChildAlbumPhotoCountReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{9} }

func (m *GetChildAlbumPhotoCountReq) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

type GetChildAlbumPhotoCountResp struct {
	PhotoCount uint32 `protobuf:"varint,1,req,name=photo_count,json=photoCount" json:"photo_count"`
}

func (m *GetChildAlbumPhotoCountResp) Reset()         { *m = GetChildAlbumPhotoCountResp{} }
func (m *GetChildAlbumPhotoCountResp) String() string { return proto.CompactTextString(m) }
func (*GetChildAlbumPhotoCountResp) ProtoMessage()    {}
func (*GetChildAlbumPhotoCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAlbum, []int{10}
}

func (m *GetChildAlbumPhotoCountResp) GetPhotoCount() uint32 {
	if m != nil {
		return m.PhotoCount
	}
	return 0
}

// --------------------------------------
// 查相片列表
type GetChildAlbumPhotoListReq struct {
	ParentAlbumId uint32 `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
	ChildAlbumId  uint32 `protobuf:"varint,2,req,name=child_album_id,json=childAlbumId" json:"child_album_id"`
	Offset        uint32 `protobuf:"varint,3,req,name=offset" json:"offset"`
	Size_         uint32 `protobuf:"varint,4,req,name=size" json:"size"`
}

func (m *GetChildAlbumPhotoListReq) Reset()                    { *m = GetChildAlbumPhotoListReq{} }
func (m *GetChildAlbumPhotoListReq) String() string            { return proto.CompactTextString(m) }
func (*GetChildAlbumPhotoListReq) ProtoMessage()               {}
func (*GetChildAlbumPhotoListReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{11} }

func (m *GetChildAlbumPhotoListReq) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

func (m *GetChildAlbumPhotoListReq) GetChildAlbumId() uint32 {
	if m != nil {
		return m.ChildAlbumId
	}
	return 0
}

func (m *GetChildAlbumPhotoListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetChildAlbumPhotoListReq) GetSize_() uint32 {
	if m != nil {
		return m.Size_
	}
	return 0
}

type GetChildAlbumPhotoListResp struct {
	PhotoList []*StPhoto `protobuf:"bytes,1,rep,name=photo_list,json=photoList" json:"photo_list,omitempty"`
}

func (m *GetChildAlbumPhotoListResp) Reset()                    { *m = GetChildAlbumPhotoListResp{} }
func (m *GetChildAlbumPhotoListResp) String() string            { return proto.CompactTextString(m) }
func (*GetChildAlbumPhotoListResp) ProtoMessage()               {}
func (*GetChildAlbumPhotoListResp) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{12} }

func (m *GetChildAlbumPhotoListResp) GetPhotoList() []*StPhoto {
	if m != nil {
		return m.PhotoList
	}
	return nil
}

// ---------------------------------------
// 删除子相册，同时删除子相册内的所有照片
type DeleteChildAlbumReq struct {
	ParentAlbumId uint32 `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
	ChildAlbumId  uint32 `protobuf:"varint,2,req,name=child_album_id,json=childAlbumId" json:"child_album_id"`
}

func (m *DeleteChildAlbumReq) Reset()                    { *m = DeleteChildAlbumReq{} }
func (m *DeleteChildAlbumReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteChildAlbumReq) ProtoMessage()               {}
func (*DeleteChildAlbumReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{13} }

func (m *DeleteChildAlbumReq) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

func (m *DeleteChildAlbumReq) GetChildAlbumId() uint32 {
	if m != nil {
		return m.ChildAlbumId
	}
	return 0
}

type DeleteChildAlbumResp struct {
}

func (m *DeleteChildAlbumResp) Reset()                    { *m = DeleteChildAlbumResp{} }
func (m *DeleteChildAlbumResp) String() string            { return proto.CompactTextString(m) }
func (*DeleteChildAlbumResp) ProtoMessage()               {}
func (*DeleteChildAlbumResp) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{14} }

// ---------------------------------------
// 删除照片
type DeletePhotoReq struct {
	ParentAlbumId uint32 `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
	ChildAlbumId  uint32 `protobuf:"varint,2,req,name=child_album_id,json=childAlbumId" json:"child_album_id"`
	PhotoId       uint32 `protobuf:"varint,3,req,name=photo_id,json=photoId" json:"photo_id"`
}

func (m *DeletePhotoReq) Reset()                    { *m = DeletePhotoReq{} }
func (m *DeletePhotoReq) String() string            { return proto.CompactTextString(m) }
func (*DeletePhotoReq) ProtoMessage()               {}
func (*DeletePhotoReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{15} }

func (m *DeletePhotoReq) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

func (m *DeletePhotoReq) GetChildAlbumId() uint32 {
	if m != nil {
		return m.ChildAlbumId
	}
	return 0
}

func (m *DeletePhotoReq) GetPhotoId() uint32 {
	if m != nil {
		return m.PhotoId
	}
	return 0
}

type DeletePhotoResp struct {
}

func (m *DeletePhotoResp) Reset()                    { *m = DeletePhotoResp{} }
func (m *DeletePhotoResp) String() string            { return proto.CompactTextString(m) }
func (*DeletePhotoResp) ProtoMessage()               {}
func (*DeletePhotoResp) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{16} }

// ---------------------------------------
// 单查照片信息
type GetPhotoReq struct {
	ParentAlbumId uint32 `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
	ChildAlbumId  uint32 `protobuf:"varint,2,req,name=child_album_id,json=childAlbumId" json:"child_album_id"`
	PhotoId       uint32 `protobuf:"varint,3,req,name=photo_id,json=photoId" json:"photo_id"`
}

func (m *GetPhotoReq) Reset()                    { *m = GetPhotoReq{} }
func (m *GetPhotoReq) String() string            { return proto.CompactTextString(m) }
func (*GetPhotoReq) ProtoMessage()               {}
func (*GetPhotoReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{17} }

func (m *GetPhotoReq) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

func (m *GetPhotoReq) GetChildAlbumId() uint32 {
	if m != nil {
		return m.ChildAlbumId
	}
	return 0
}

func (m *GetPhotoReq) GetPhotoId() uint32 {
	if m != nil {
		return m.PhotoId
	}
	return 0
}

type GetPhotoResp struct {
	Photo *StPhoto `protobuf:"bytes,1,opt,name=photo" json:"photo,omitempty"`
}

func (m *GetPhotoResp) Reset()                    { *m = GetPhotoResp{} }
func (m *GetPhotoResp) String() string            { return proto.CompactTextString(m) }
func (*GetPhotoResp) ProtoMessage()               {}
func (*GetPhotoResp) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{18} }

func (m *GetPhotoResp) GetPhoto() *StPhoto {
	if m != nil {
		return m.Photo
	}
	return nil
}

// ---------------------------------------
// 单查相册信息
type GetChildAlbumReq struct {
	ParentAlbumId uint32 `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
	ChildAlbumId  uint32 `protobuf:"varint,2,req,name=child_album_id,json=childAlbumId" json:"child_album_id"`
}

func (m *GetChildAlbumReq) Reset()                    { *m = GetChildAlbumReq{} }
func (m *GetChildAlbumReq) String() string            { return proto.CompactTextString(m) }
func (*GetChildAlbumReq) ProtoMessage()               {}
func (*GetChildAlbumReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{19} }

func (m *GetChildAlbumReq) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

func (m *GetChildAlbumReq) GetChildAlbumId() uint32 {
	if m != nil {
		return m.ChildAlbumId
	}
	return 0
}

type GetChildAlbumResp struct {
	Album *ChildAlbum `protobuf:"bytes,1,opt,name=album" json:"album,omitempty"`
}

func (m *GetChildAlbumResp) Reset()                    { *m = GetChildAlbumResp{} }
func (m *GetChildAlbumResp) String() string            { return proto.CompactTextString(m) }
func (*GetChildAlbumResp) ProtoMessage()               {}
func (*GetChildAlbumResp) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{20} }

func (m *GetChildAlbumResp) GetAlbum() *ChildAlbum {
	if m != nil {
		return m.Album
	}
	return nil
}

type ModifyAlbumNameReq struct {
	ParentAlbumId uint32 `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
	ChildAlbumId  uint32 `protobuf:"varint,2,req,name=child_album_id,json=childAlbumId" json:"child_album_id"`
	Name          string `protobuf:"bytes,3,req,name=name" json:"name"`
}

func (m *ModifyAlbumNameReq) Reset()                    { *m = ModifyAlbumNameReq{} }
func (m *ModifyAlbumNameReq) String() string            { return proto.CompactTextString(m) }
func (*ModifyAlbumNameReq) ProtoMessage()               {}
func (*ModifyAlbumNameReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{21} }

func (m *ModifyAlbumNameReq) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

func (m *ModifyAlbumNameReq) GetChildAlbumId() uint32 {
	if m != nil {
		return m.ChildAlbumId
	}
	return 0
}

func (m *ModifyAlbumNameReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type ModifyAlbumThumbReq struct {
	ParentAlbumId uint32 `protobuf:"varint,1,req,name=parent_album_id,json=parentAlbumId" json:"parent_album_id"`
	ChildAlbumId  uint32 `protobuf:"varint,2,req,name=child_album_id,json=childAlbumId" json:"child_album_id"`
	ThumbUrl      string `protobuf:"bytes,3,req,name=thumb_url,json=thumbUrl" json:"thumb_url"`
	UrlVersion    uint32 `protobuf:"varint,4,opt,name=url_version,json=urlVersion" json:"url_version"`
}

func (m *ModifyAlbumThumbReq) Reset()                    { *m = ModifyAlbumThumbReq{} }
func (m *ModifyAlbumThumbReq) String() string            { return proto.CompactTextString(m) }
func (*ModifyAlbumThumbReq) ProtoMessage()               {}
func (*ModifyAlbumThumbReq) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{22} }

func (m *ModifyAlbumThumbReq) GetParentAlbumId() uint32 {
	if m != nil {
		return m.ParentAlbumId
	}
	return 0
}

func (m *ModifyAlbumThumbReq) GetChildAlbumId() uint32 {
	if m != nil {
		return m.ChildAlbumId
	}
	return 0
}

func (m *ModifyAlbumThumbReq) GetThumbUrl() string {
	if m != nil {
		return m.ThumbUrl
	}
	return ""
}

func (m *ModifyAlbumThumbReq) GetUrlVersion() uint32 {
	if m != nil {
		return m.UrlVersion
	}
	return 0
}

type ModifyAlbumThumbResp struct {
}

func (m *ModifyAlbumThumbResp) Reset()                    { *m = ModifyAlbumThumbResp{} }
func (m *ModifyAlbumThumbResp) String() string            { return proto.CompactTextString(m) }
func (*ModifyAlbumThumbResp) ProtoMessage()               {}
func (*ModifyAlbumThumbResp) Descriptor() ([]byte, []int) { return fileDescriptorAlbum, []int{23} }

func init() {
	proto.RegisterType((*StPhoto)(nil), "Album.stPhoto")
	proto.RegisterType((*ChildAlbum)(nil), "Album.ChildAlbum")
	proto.RegisterType((*CreateAlbumReq)(nil), "Album.CreateAlbumReq")
	proto.RegisterType((*CreateAlbumResp)(nil), "Album.CreateAlbumResp")
	proto.RegisterType((*GetNextAlbumIdResp)(nil), "Album.GetNextAlbumIdResp")
	proto.RegisterType((*CreatePhotoReq)(nil), "Album.CreatePhotoReq")
	proto.RegisterType((*CreatePhotoResp)(nil), "Album.CreatePhotoResp")
	proto.RegisterType((*GetChildAlbumListReq)(nil), "Album.GetChildAlbumListReq")
	proto.RegisterType((*GetChildAlbumListResp)(nil), "Album.GetChildAlbumListResp")
	proto.RegisterType((*GetChildAlbumPhotoCountReq)(nil), "Album.GetChildAlbumPhotoCountReq")
	proto.RegisterType((*GetChildAlbumPhotoCountResp)(nil), "Album.GetChildAlbumPhotoCountResp")
	proto.RegisterType((*GetChildAlbumPhotoListReq)(nil), "Album.GetChildAlbumPhotoListReq")
	proto.RegisterType((*GetChildAlbumPhotoListResp)(nil), "Album.GetChildAlbumPhotoListResp")
	proto.RegisterType((*DeleteChildAlbumReq)(nil), "Album.DeleteChildAlbumReq")
	proto.RegisterType((*DeleteChildAlbumResp)(nil), "Album.DeleteChildAlbumResp")
	proto.RegisterType((*DeletePhotoReq)(nil), "Album.DeletePhotoReq")
	proto.RegisterType((*DeletePhotoResp)(nil), "Album.DeletePhotoResp")
	proto.RegisterType((*GetPhotoReq)(nil), "Album.GetPhotoReq")
	proto.RegisterType((*GetPhotoResp)(nil), "Album.GetPhotoResp")
	proto.RegisterType((*GetChildAlbumReq)(nil), "Album.GetChildAlbumReq")
	proto.RegisterType((*GetChildAlbumResp)(nil), "Album.GetChildAlbumResp")
	proto.RegisterType((*ModifyAlbumNameReq)(nil), "Album.ModifyAlbumNameReq")
	proto.RegisterType((*ModifyAlbumThumbReq)(nil), "Album.ModifyAlbumThumbReq")
	proto.RegisterType((*ModifyAlbumThumbResp)(nil), "Album.ModifyAlbumThumbResp")
	proto.RegisterEnum("Album.UrlVersion", UrlVersion_name, UrlVersion_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Album service

type AlbumClient interface {
	CreateAlbum(ctx context.Context, in *CreateAlbumReq, opts ...grpc.CallOption) (*CreateAlbumResp, error)
	GetNextAlbumId(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetNextAlbumIdResp, error)
	CreatePhoto(ctx context.Context, in *CreatePhotoReq, opts ...grpc.CallOption) (*CreatePhotoResp, error)
	GetChildAlbumList(ctx context.Context, in *GetChildAlbumListReq, opts ...grpc.CallOption) (*GetChildAlbumListResp, error)
	GetChildAlbumPhotoList(ctx context.Context, in *GetChildAlbumPhotoListReq, opts ...grpc.CallOption) (*GetChildAlbumPhotoListResp, error)
	DeleteChildAlbum(ctx context.Context, in *DeleteChildAlbumReq, opts ...grpc.CallOption) (*DeleteChildAlbumResp, error)
	DeletePhoto(ctx context.Context, in *DeletePhotoReq, opts ...grpc.CallOption) (*DeletePhotoResp, error)
	GetPhoto(ctx context.Context, in *GetPhotoReq, opts ...grpc.CallOption) (*GetPhotoResp, error)
	GetChildAlbum(ctx context.Context, in *GetChildAlbumReq, opts ...grpc.CallOption) (*GetChildAlbumResp, error)
	ModifyAlbumName(ctx context.Context, in *ModifyAlbumNameReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChildAlbumPhotoCount(ctx context.Context, in *GetChildAlbumPhotoCountReq, opts ...grpc.CallOption) (*GetChildAlbumPhotoCountResp, error)
	ModifyAlbumThumb(ctx context.Context, in *ModifyAlbumThumbReq, opts ...grpc.CallOption) (*ModifyAlbumThumbResp, error)
}

type albumClient struct {
	cc *grpc.ClientConn
}

func NewAlbumClient(cc *grpc.ClientConn) AlbumClient {
	return &albumClient{cc}
}

func (c *albumClient) CreateAlbum(ctx context.Context, in *CreateAlbumReq, opts ...grpc.CallOption) (*CreateAlbumResp, error) {
	out := new(CreateAlbumResp)
	err := grpc.Invoke(ctx, "/Album.Album/CreateAlbum", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) GetNextAlbumId(ctx context.Context, in *tlvpickle.SKBuiltinEmpty_PB, opts ...grpc.CallOption) (*GetNextAlbumIdResp, error) {
	out := new(GetNextAlbumIdResp)
	err := grpc.Invoke(ctx, "/Album.Album/GetNextAlbumId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) CreatePhoto(ctx context.Context, in *CreatePhotoReq, opts ...grpc.CallOption) (*CreatePhotoResp, error) {
	out := new(CreatePhotoResp)
	err := grpc.Invoke(ctx, "/Album.Album/CreatePhoto", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) GetChildAlbumList(ctx context.Context, in *GetChildAlbumListReq, opts ...grpc.CallOption) (*GetChildAlbumListResp, error) {
	out := new(GetChildAlbumListResp)
	err := grpc.Invoke(ctx, "/Album.Album/GetChildAlbumList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) GetChildAlbumPhotoList(ctx context.Context, in *GetChildAlbumPhotoListReq, opts ...grpc.CallOption) (*GetChildAlbumPhotoListResp, error) {
	out := new(GetChildAlbumPhotoListResp)
	err := grpc.Invoke(ctx, "/Album.Album/GetChildAlbumPhotoList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) DeleteChildAlbum(ctx context.Context, in *DeleteChildAlbumReq, opts ...grpc.CallOption) (*DeleteChildAlbumResp, error) {
	out := new(DeleteChildAlbumResp)
	err := grpc.Invoke(ctx, "/Album.Album/DeleteChildAlbum", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) DeletePhoto(ctx context.Context, in *DeletePhotoReq, opts ...grpc.CallOption) (*DeletePhotoResp, error) {
	out := new(DeletePhotoResp)
	err := grpc.Invoke(ctx, "/Album.Album/DeletePhoto", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) GetPhoto(ctx context.Context, in *GetPhotoReq, opts ...grpc.CallOption) (*GetPhotoResp, error) {
	out := new(GetPhotoResp)
	err := grpc.Invoke(ctx, "/Album.Album/GetPhoto", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) GetChildAlbum(ctx context.Context, in *GetChildAlbumReq, opts ...grpc.CallOption) (*GetChildAlbumResp, error) {
	out := new(GetChildAlbumResp)
	err := grpc.Invoke(ctx, "/Album.Album/GetChildAlbum", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) ModifyAlbumName(ctx context.Context, in *ModifyAlbumNameReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Album.Album/ModifyAlbumName", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) GetChildAlbumPhotoCount(ctx context.Context, in *GetChildAlbumPhotoCountReq, opts ...grpc.CallOption) (*GetChildAlbumPhotoCountResp, error) {
	out := new(GetChildAlbumPhotoCountResp)
	err := grpc.Invoke(ctx, "/Album.Album/GetChildAlbumPhotoCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *albumClient) ModifyAlbumThumb(ctx context.Context, in *ModifyAlbumThumbReq, opts ...grpc.CallOption) (*ModifyAlbumThumbResp, error) {
	out := new(ModifyAlbumThumbResp)
	err := grpc.Invoke(ctx, "/Album.Album/ModifyAlbumThumb", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Album service

type AlbumServer interface {
	CreateAlbum(context.Context, *CreateAlbumReq) (*CreateAlbumResp, error)
	GetNextAlbumId(context.Context, *tlvpickle.SKBuiltinEmpty_PB) (*GetNextAlbumIdResp, error)
	CreatePhoto(context.Context, *CreatePhotoReq) (*CreatePhotoResp, error)
	GetChildAlbumList(context.Context, *GetChildAlbumListReq) (*GetChildAlbumListResp, error)
	GetChildAlbumPhotoList(context.Context, *GetChildAlbumPhotoListReq) (*GetChildAlbumPhotoListResp, error)
	DeleteChildAlbum(context.Context, *DeleteChildAlbumReq) (*DeleteChildAlbumResp, error)
	DeletePhoto(context.Context, *DeletePhotoReq) (*DeletePhotoResp, error)
	GetPhoto(context.Context, *GetPhotoReq) (*GetPhotoResp, error)
	GetChildAlbum(context.Context, *GetChildAlbumReq) (*GetChildAlbumResp, error)
	ModifyAlbumName(context.Context, *ModifyAlbumNameReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChildAlbumPhotoCount(context.Context, *GetChildAlbumPhotoCountReq) (*GetChildAlbumPhotoCountResp, error)
	ModifyAlbumThumb(context.Context, *ModifyAlbumThumbReq) (*ModifyAlbumThumbResp, error)
}

func RegisterAlbumServer(s *grpc.Server, srv AlbumServer) {
	s.RegisterService(&_Album_serviceDesc, srv)
}

func _Album_CreateAlbum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAlbumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).CreateAlbum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/CreateAlbum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).CreateAlbum(ctx, req.(*CreateAlbumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_GetNextAlbumId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(tlvpickle.SKBuiltinEmpty_PB)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).GetNextAlbumId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/GetNextAlbumId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).GetNextAlbumId(ctx, req.(*tlvpickle.SKBuiltinEmpty_PB))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_CreatePhoto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePhotoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).CreatePhoto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/CreatePhoto",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).CreatePhoto(ctx, req.(*CreatePhotoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_GetChildAlbumList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChildAlbumListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).GetChildAlbumList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/GetChildAlbumList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).GetChildAlbumList(ctx, req.(*GetChildAlbumListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_GetChildAlbumPhotoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChildAlbumPhotoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).GetChildAlbumPhotoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/GetChildAlbumPhotoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).GetChildAlbumPhotoList(ctx, req.(*GetChildAlbumPhotoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_DeleteChildAlbum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteChildAlbumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).DeleteChildAlbum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/DeleteChildAlbum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).DeleteChildAlbum(ctx, req.(*DeleteChildAlbumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_DeletePhoto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePhotoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).DeletePhoto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/DeletePhoto",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).DeletePhoto(ctx, req.(*DeletePhotoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_GetPhoto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPhotoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).GetPhoto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/GetPhoto",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).GetPhoto(ctx, req.(*GetPhotoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_GetChildAlbum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChildAlbumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).GetChildAlbum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/GetChildAlbum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).GetChildAlbum(ctx, req.(*GetChildAlbumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_ModifyAlbumName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyAlbumNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).ModifyAlbumName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/ModifyAlbumName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).ModifyAlbumName(ctx, req.(*ModifyAlbumNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_GetChildAlbumPhotoCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChildAlbumPhotoCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).GetChildAlbumPhotoCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/GetChildAlbumPhotoCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).GetChildAlbumPhotoCount(ctx, req.(*GetChildAlbumPhotoCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Album_ModifyAlbumThumb_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyAlbumThumbReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlbumServer).ModifyAlbumThumb(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Album.Album/ModifyAlbumThumb",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlbumServer).ModifyAlbumThumb(ctx, req.(*ModifyAlbumThumbReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Album_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Album.Album",
	HandlerType: (*AlbumServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAlbum",
			Handler:    _Album_CreateAlbum_Handler,
		},
		{
			MethodName: "GetNextAlbumId",
			Handler:    _Album_GetNextAlbumId_Handler,
		},
		{
			MethodName: "CreatePhoto",
			Handler:    _Album_CreatePhoto_Handler,
		},
		{
			MethodName: "GetChildAlbumList",
			Handler:    _Album_GetChildAlbumList_Handler,
		},
		{
			MethodName: "GetChildAlbumPhotoList",
			Handler:    _Album_GetChildAlbumPhotoList_Handler,
		},
		{
			MethodName: "DeleteChildAlbum",
			Handler:    _Album_DeleteChildAlbum_Handler,
		},
		{
			MethodName: "DeletePhoto",
			Handler:    _Album_DeletePhoto_Handler,
		},
		{
			MethodName: "GetPhoto",
			Handler:    _Album_GetPhoto_Handler,
		},
		{
			MethodName: "GetChildAlbum",
			Handler:    _Album_GetChildAlbum_Handler,
		},
		{
			MethodName: "ModifyAlbumName",
			Handler:    _Album_ModifyAlbumName_Handler,
		},
		{
			MethodName: "GetChildAlbumPhotoCount",
			Handler:    _Album_GetChildAlbumPhotoCount_Handler,
		},
		{
			MethodName: "ModifyAlbumThumb",
			Handler:    _Album_ModifyAlbumThumb_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/albumsvr/album.proto",
}

func (m *StPhoto) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StPhoto) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ChildAlbumId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.Creator))
	dAtA[i] = 0x22
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(len(m.ThumbUrl)))
	i += copy(dAtA[i:], m.ThumbUrl)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(len(m.PhotoUrl)))
	i += copy(dAtA[i:], m.PhotoUrl)
	dAtA[i] = 0x30
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.CreateAt))
	dAtA[i] = 0x38
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.PhotoId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.UrlVersion))
	return i, nil
}

func (m *ChildAlbum) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChildAlbum) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ChildAlbumId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(len(m.ThumbUrl)))
	i += copy(dAtA[i:], m.ThumbUrl)
	dAtA[i] = 0x20
	i++
	if m.IsDefault {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.CreateAt))
	dAtA[i] = 0x30
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.PhotoCount))
	dAtA[i] = 0x38
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.Creator))
	dAtA[i] = 0x48
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.UrlVersion))
	return i, nil
}

func (m *CreateAlbumReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateAlbumReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentId))
	dAtA[i] = 0x10
	i++
	if m.IsDefault {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.Creator))
	return i, nil
}

func (m *CreateAlbumResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateAlbumResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.SecondId))
	return i, nil
}

func (m *GetNextAlbumIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNextAlbumIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.AlbumId))
	return i, nil
}

func (m *CreatePhotoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreatePhotoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ChildAlbumId))
	if len(m.PhotoList) > 0 {
		for _, msg := range m.PhotoList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintAlbum(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CreatePhotoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreatePhotoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PhotoIdList) > 0 {
		for _, num := range m.PhotoIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintAlbum(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetChildAlbumListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChildAlbumListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.Size_))
	return i, nil
}

func (m *GetChildAlbumListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChildAlbumListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChildAlbumList) > 0 {
		for _, msg := range m.ChildAlbumList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAlbum(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetChildAlbumPhotoCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChildAlbumPhotoCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	return i, nil
}

func (m *GetChildAlbumPhotoCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChildAlbumPhotoCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.PhotoCount))
	return i, nil
}

func (m *GetChildAlbumPhotoListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChildAlbumPhotoListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ChildAlbumId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x20
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.Size_))
	return i, nil
}

func (m *GetChildAlbumPhotoListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChildAlbumPhotoListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PhotoList) > 0 {
		for _, msg := range m.PhotoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAlbum(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DeleteChildAlbumReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteChildAlbumReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ChildAlbumId))
	return i, nil
}

func (m *DeleteChildAlbumResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteChildAlbumResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DeletePhotoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeletePhotoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ChildAlbumId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.PhotoId))
	return i, nil
}

func (m *DeletePhotoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeletePhotoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPhotoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPhotoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ChildAlbumId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.PhotoId))
	return i, nil
}

func (m *GetPhotoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPhotoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Photo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAlbum(dAtA, i, uint64(m.Photo.Size()))
		n1, err := m.Photo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetChildAlbumReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChildAlbumReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ChildAlbumId))
	return i, nil
}

func (m *GetChildAlbumResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChildAlbumResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Album != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAlbum(dAtA, i, uint64(m.Album.Size()))
		n2, err := m.Album.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *ModifyAlbumNameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyAlbumNameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ChildAlbumId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	return i, nil
}

func (m *ModifyAlbumThumbReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyAlbumThumbReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ParentAlbumId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.ChildAlbumId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(len(m.ThumbUrl)))
	i += copy(dAtA[i:], m.ThumbUrl)
	dAtA[i] = 0x20
	i++
	i = encodeVarintAlbum(dAtA, i, uint64(m.UrlVersion))
	return i, nil
}

func (m *ModifyAlbumThumbResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyAlbumThumbResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Album(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Album(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAlbum(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *StPhoto) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	n += 1 + sovAlbum(uint64(m.ChildAlbumId))
	n += 1 + sovAlbum(uint64(m.Creator))
	l = len(m.ThumbUrl)
	n += 1 + l + sovAlbum(uint64(l))
	l = len(m.PhotoUrl)
	n += 1 + l + sovAlbum(uint64(l))
	n += 1 + sovAlbum(uint64(m.CreateAt))
	n += 1 + sovAlbum(uint64(m.PhotoId))
	n += 1 + sovAlbum(uint64(m.UrlVersion))
	return n
}

func (m *ChildAlbum) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ChildAlbumId))
	l = len(m.Name)
	n += 1 + l + sovAlbum(uint64(l))
	l = len(m.ThumbUrl)
	n += 1 + l + sovAlbum(uint64(l))
	n += 2
	n += 1 + sovAlbum(uint64(m.CreateAt))
	n += 1 + sovAlbum(uint64(m.PhotoCount))
	n += 1 + sovAlbum(uint64(m.LastUpdateTime))
	n += 1 + sovAlbum(uint64(m.Creator))
	n += 1 + sovAlbum(uint64(m.UrlVersion))
	return n
}

func (m *CreateAlbumReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentId))
	n += 2
	l = len(m.Name)
	n += 1 + l + sovAlbum(uint64(l))
	n += 1 + sovAlbum(uint64(m.Creator))
	return n
}

func (m *CreateAlbumResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentId))
	n += 1 + sovAlbum(uint64(m.SecondId))
	return n
}

func (m *GetNextAlbumIdResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.AlbumId))
	return n
}

func (m *CreatePhotoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	n += 1 + sovAlbum(uint64(m.ChildAlbumId))
	if len(m.PhotoList) > 0 {
		for _, e := range m.PhotoList {
			l = e.Size()
			n += 1 + l + sovAlbum(uint64(l))
		}
	}
	return n
}

func (m *CreatePhotoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PhotoIdList) > 0 {
		for _, e := range m.PhotoIdList {
			n += 1 + sovAlbum(uint64(e))
		}
	}
	return n
}

func (m *GetChildAlbumListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	n += 1 + sovAlbum(uint64(m.Offset))
	n += 1 + sovAlbum(uint64(m.Size_))
	return n
}

func (m *GetChildAlbumListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChildAlbumList) > 0 {
		for _, e := range m.ChildAlbumList {
			l = e.Size()
			n += 1 + l + sovAlbum(uint64(l))
		}
	}
	return n
}

func (m *GetChildAlbumPhotoCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	return n
}

func (m *GetChildAlbumPhotoCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.PhotoCount))
	return n
}

func (m *GetChildAlbumPhotoListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	n += 1 + sovAlbum(uint64(m.ChildAlbumId))
	n += 1 + sovAlbum(uint64(m.Offset))
	n += 1 + sovAlbum(uint64(m.Size_))
	return n
}

func (m *GetChildAlbumPhotoListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PhotoList) > 0 {
		for _, e := range m.PhotoList {
			l = e.Size()
			n += 1 + l + sovAlbum(uint64(l))
		}
	}
	return n
}

func (m *DeleteChildAlbumReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	n += 1 + sovAlbum(uint64(m.ChildAlbumId))
	return n
}

func (m *DeleteChildAlbumResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DeletePhotoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	n += 1 + sovAlbum(uint64(m.ChildAlbumId))
	n += 1 + sovAlbum(uint64(m.PhotoId))
	return n
}

func (m *DeletePhotoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPhotoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	n += 1 + sovAlbum(uint64(m.ChildAlbumId))
	n += 1 + sovAlbum(uint64(m.PhotoId))
	return n
}

func (m *GetPhotoResp) Size() (n int) {
	var l int
	_ = l
	if m.Photo != nil {
		l = m.Photo.Size()
		n += 1 + l + sovAlbum(uint64(l))
	}
	return n
}

func (m *GetChildAlbumReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	n += 1 + sovAlbum(uint64(m.ChildAlbumId))
	return n
}

func (m *GetChildAlbumResp) Size() (n int) {
	var l int
	_ = l
	if m.Album != nil {
		l = m.Album.Size()
		n += 1 + l + sovAlbum(uint64(l))
	}
	return n
}

func (m *ModifyAlbumNameReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	n += 1 + sovAlbum(uint64(m.ChildAlbumId))
	l = len(m.Name)
	n += 1 + l + sovAlbum(uint64(l))
	return n
}

func (m *ModifyAlbumThumbReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAlbum(uint64(m.ParentAlbumId))
	n += 1 + sovAlbum(uint64(m.ChildAlbumId))
	l = len(m.ThumbUrl)
	n += 1 + l + sovAlbum(uint64(l))
	n += 1 + sovAlbum(uint64(m.UrlVersion))
	return n
}

func (m *ModifyAlbumThumbResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovAlbum(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAlbum(x uint64) (n int) {
	return sovAlbum(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *StPhoto) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stPhoto: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stPhoto: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumId", wireType)
			}
			m.ChildAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChildAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			m.Creator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Creator |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThumbUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ThumbUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhotoUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateAt", wireType)
			}
			m.CreateAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoId", wireType)
			}
			m.PhotoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhotoId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UrlVersion", wireType)
			}
			m.UrlVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UrlVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("child_album_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("thumb_url")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("photo_url")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("create_at")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("photo_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChildAlbum) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChildAlbum: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChildAlbum: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumId", wireType)
			}
			m.ChildAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChildAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThumbUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ThumbUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDefault", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDefault = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateAt", wireType)
			}
			m.CreateAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoCount", wireType)
			}
			m.PhotoCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhotoCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			m.Creator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Creator |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UrlVersion", wireType)
			}
			m.UrlVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UrlVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("child_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("thumb_url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_default")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("create_at")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("photo_count")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("creator")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateAlbumReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateAlbumReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateAlbumReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentId", wireType)
			}
			m.ParentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDefault", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDefault = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			m.Creator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Creator |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_default")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("creator")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateAlbumResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateAlbumResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateAlbumResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentId", wireType)
			}
			m.ParentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SecondId", wireType)
			}
			m.SecondId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SecondId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("second_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNextAlbumIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNextAlbumIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNextAlbumIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AlbumId", wireType)
			}
			m.AlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("album_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreatePhotoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreatePhotoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreatePhotoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumId", wireType)
			}
			m.ChildAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChildAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhotoList = append(m.PhotoList, &StPhoto{})
			if err := m.PhotoList[len(m.PhotoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("child_album_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreatePhotoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreatePhotoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreatePhotoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAlbum
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.PhotoIdList = append(m.PhotoIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowAlbum
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthAlbum
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowAlbum
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.PhotoIdList = append(m.PhotoIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChildAlbumListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChildAlbumListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChildAlbumListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChildAlbumListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChildAlbumListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChildAlbumListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChildAlbumList = append(m.ChildAlbumList, &ChildAlbum{})
			if err := m.ChildAlbumList[len(m.ChildAlbumList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChildAlbumPhotoCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChildAlbumPhotoCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChildAlbumPhotoCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChildAlbumPhotoCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChildAlbumPhotoCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChildAlbumPhotoCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoCount", wireType)
			}
			m.PhotoCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhotoCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("photo_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChildAlbumPhotoListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChildAlbumPhotoListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChildAlbumPhotoListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumId", wireType)
			}
			m.ChildAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChildAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("child_album_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChildAlbumPhotoListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChildAlbumPhotoListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChildAlbumPhotoListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhotoList = append(m.PhotoList, &StPhoto{})
			if err := m.PhotoList[len(m.PhotoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteChildAlbumReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteChildAlbumReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteChildAlbumReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumId", wireType)
			}
			m.ChildAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChildAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("child_album_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteChildAlbumResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteChildAlbumResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteChildAlbumResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeletePhotoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeletePhotoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeletePhotoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumId", wireType)
			}
			m.ChildAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChildAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoId", wireType)
			}
			m.PhotoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhotoId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("child_album_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("photo_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeletePhotoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeletePhotoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeletePhotoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPhotoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPhotoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPhotoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumId", wireType)
			}
			m.ChildAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChildAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoId", wireType)
			}
			m.PhotoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhotoId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("child_album_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("photo_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPhotoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPhotoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPhotoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Photo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Photo == nil {
				m.Photo = &StPhoto{}
			}
			if err := m.Photo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChildAlbumReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChildAlbumReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChildAlbumReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumId", wireType)
			}
			m.ChildAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChildAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("child_album_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChildAlbumResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChildAlbumResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChildAlbumResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Album", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Album == nil {
				m.Album = &ChildAlbum{}
			}
			if err := m.Album.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyAlbumNameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyAlbumNameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyAlbumNameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumId", wireType)
			}
			m.ChildAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChildAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("child_album_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyAlbumThumbReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyAlbumThumbReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyAlbumThumbReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentAlbumId", wireType)
			}
			m.ParentAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChildAlbumId", wireType)
			}
			m.ChildAlbumId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChildAlbumId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThumbUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAlbum
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ThumbUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UrlVersion", wireType)
			}
			m.UrlVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UrlVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("parent_album_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("child_album_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("thumb_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyAlbumThumbResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyAlbumThumbResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyAlbumThumbResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAlbum(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAlbum
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAlbum(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAlbum
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAlbum
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAlbum
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAlbum
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAlbum(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAlbum = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAlbum   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/albumsvr/album.proto", fileDescriptorAlbum) }

var fileDescriptorAlbum = []byte{
	// 1335 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0x4f, 0x6f, 0x1b, 0x45,
	0x14, 0xcf, 0x78, 0x9d, 0xc4, 0x79, 0x6e, 0x1c, 0x67, 0x92, 0xa6, 0xae, 0x5b, 0xa5, 0xd3, 0xa1,
	0x15, 0xa1, 0x22, 0xa9, 0x14, 0x51, 0x24, 0x5c, 0xcb, 0xa8, 0x69, 0x42, 0x64, 0x5a, 0xd2, 0xd6,
	0x8d, 0x8b, 0x84, 0x90, 0x2c, 0xd7, 0xbb, 0x51, 0x56, 0x5d, 0x7b, 0x07, 0xcf, 0x6e, 0x45, 0x11,
	0x87, 0xde, 0x28, 0x7f, 0x24, 0xa0, 0x9c, 0xe0, 0x9c, 0x2b, 0xb7, 0x5e, 0xf8, 0x06, 0x3d, 0x72,
	0x47, 0x42, 0xa8, 0x5c, 0x72, 0xe4, 0x23, 0xa0, 0x99, 0xd9, 0x5d, 0xef, 0xae, 0xc7, 0x8d, 0x8b,
	0x08, 0xe2, 0x66, 0xbf, 0xf7, 0xe6, 0xcd, 0xef, 0xfd, 0xde, 0x7b, 0xf3, 0xde, 0x42, 0x89, 0xf7,
	0x3b, 0x97, 0xdb, 0xce, 0x7d, 0xbf, 0xcb, 0x1f, 0xf6, 0xd5, 0x8f, 0x35, 0xd6, 0x77, 0x3d, 0x17,
	0x4f, 0x5e, 0x13, 0x7f, 0xca, 0x17, 0x3a, 0x6e, 0xb7, 0xeb, 0xf6, 0x2e, 0x7b, 0xce, 0x43, 0x66,
	0x77, 0x1e, 0x38, 0xd6, 0x65, 0xfe, 0xe0, 0xbe, 0x6f, 0x3b, 0x9e, 0xdd, 0xf3, 0x1e, 0x31, 0x4b,
	0x19, 0xd3, 0x67, 0x19, 0x98, 0xe6, 0xde, 0xed, 0x7d, 0x71, 0xf0, 0x4d, 0x98, 0x63, 0xed, 0xbe,
	0xd5, 0xf3, 0x5a, 0xd2, 0x5d, 0xcb, 0x36, 0x4b, 0x88, 0x64, 0x56, 0x66, 0x37, 0xb2, 0xcf, 0x7f,
	0x3f, 0x37, 0xd1, 0x98, 0x55, 0x4a, 0xe9, 0xbd, 0x6e, 0xe2, 0x4b, 0x50, 0xe8, 0xec, 0xdb, 0x8e,
	0x39, 0x30, 0xce, 0xc4, 0x8c, 0x4f, 0x48, 0x5d, 0x68, 0xbb, 0x0c, 0xd3, 0x9d, 0xbe, 0xd5, 0xf6,
	0xdc, 0x7e, 0xc9, 0x88, 0x19, 0x85, 0x42, 0x7c, 0x1e, 0x66, 0xbc, 0x7d, 0xbf, 0x7b, 0xbf, 0xe5,
	0xf7, 0x9d, 0x52, 0x96, 0x64, 0x56, 0x66, 0x02, 0x8b, 0x9c, 0x14, 0x37, 0xfb, 0x8e, 0x30, 0x61,
	0x02, 0xa5, 0x34, 0x99, 0x8c, 0x9b, 0x48, 0x71, 0x60, 0x22, 0x1d, 0x5a, 0xad, 0xb6, 0x57, 0x9a,
	0x8a, 0xdd, 0x93, 0x53, 0xe2, 0x6b, 0x1e, 0x3e, 0x07, 0xca, 0x5c, 0xc0, 0x9d, 0x8e, 0x23, 0x91,
	0xd2, 0xba, 0x89, 0x2f, 0x42, 0xde, 0xef, 0x3b, 0xad, 0x87, 0x56, 0x9f, 0xdb, 0x6e, 0xaf, 0x94,
	0x23, 0x28, 0xb2, 0x01, 0xbf, 0xef, 0xdc, 0x53, 0x72, 0xfa, 0x5b, 0x06, 0xe0, 0x7a, 0x14, 0xa1,
	0x86, 0x0b, 0x34, 0x92, 0x8b, 0x12, 0x64, 0x7b, 0xed, 0xae, 0x25, 0xd9, 0x0a, 0x63, 0x90, 0x92,
	0x24, 0x0b, 0x86, 0x96, 0x85, 0xd7, 0x00, 0x6c, 0xde, 0x32, 0xad, 0xbd, 0xb6, 0xef, 0x78, 0x92,
	0xa9, 0x5c, 0x60, 0x33, 0x63, 0xf3, 0x4d, 0x25, 0x4e, 0xf2, 0x30, 0xa9, 0xe5, 0xe1, 0x22, 0xe4,
	0x15, 0x0f, 0x1d, 0xd7, 0xef, 0x25, 0xc9, 0x02, 0xa9, 0xb8, 0x2e, 0xe4, 0x78, 0x0d, 0x8a, 0x4e,
	0x9b, 0x7b, 0x2d, 0x9f, 0x99, 0xc2, 0x9d, 0x67, 0x77, 0xad, 0x04, 0x6d, 0x05, 0xa1, 0x6d, 0x4a,
	0xe5, 0xae, 0xdd, 0xb5, 0xe2, 0x79, 0xce, 0xe9, 0xf2, 0x9c, 0x62, 0x77, 0x66, 0x04, 0xbb, 0x4f,
	0x11, 0x14, 0xae, 0x2b, 0xa8, 0x82, 0xb4, 0x86, 0xf5, 0x89, 0x4c, 0xbf, 0xaa, 0xcd, 0x14, 0xb9,
	0x39, 0x25, 0xae, 0x9b, 0x29, 0x6e, 0x32, 0x7a, 0x6e, 0x42, 0xf6, 0x8d, 0x21, 0xf6, 0x63, 0xd8,
	0xb3, 0x1a, 0xec, 0xf4, 0x43, 0x98, 0x4b, 0x60, 0xe2, 0x6c, 0x1c, 0x50, 0xe7, 0x61, 0x86, 0x5b,
	0x1d, 0xb7, 0x67, 0xa6, 0x1b, 0x24, 0xa7, 0xc4, 0x75, 0x93, 0x5e, 0x01, 0xbc, 0x6d, 0x79, 0x3b,
	0xd6, 0xa7, 0x61, 0x6b, 0x49, 0xdf, 0xe7, 0x20, 0xa7, 0x2d, 0xa6, 0xe9, 0xb6, 0x32, 0xa2, 0x3f,
	0x46, 0x24, 0xc9, 0xee, 0x15, 0x24, 0x1d, 0x5f, 0x03, 0xaf, 0x82, 0x2a, 0x8b, 0x96, 0x63, 0x73,
	0xaf, 0x64, 0x10, 0x63, 0x25, 0xbf, 0x5e, 0x58, 0x93, 0xda, 0xb5, 0xe0, 0xf9, 0x68, 0xa8, 0xfe,
	0xbc, 0x69, 0x73, 0x8f, 0x5e, 0x09, 0xb9, 0x0a, 0xa0, 0x71, 0x86, 0x29, 0xcc, 0x86, 0x9d, 0xa7,
	0x9c, 0x20, 0x62, 0xac, 0xcc, 0x36, 0xf2, 0x41, 0xe3, 0xc9, 0x63, 0x9f, 0xc3, 0xe2, 0xb6, 0xe5,
	0x0d, 0xfa, 0x4a, 0x08, 0x5f, 0x3d, 0xae, 0xb3, 0x30, 0xe5, 0xee, 0xed, 0x71, 0xcb, 0x4b, 0xc4,
	0x13, 0xc8, 0x44, 0x01, 0x70, 0xfb, 0x33, 0x2b, 0xf1, 0x0e, 0x49, 0x09, 0xdd, 0x85, 0x93, 0x9a,
	0xdb, 0x39, 0xc3, 0x57, 0xa1, 0x18, 0x27, 0x2a, 0x42, 0x9f, 0x5f, 0x9f, 0x0f, 0x28, 0x18, 0x1c,
	0x6a, 0x14, 0x3a, 0x09, 0x07, 0xf4, 0x7d, 0x28, 0x27, 0xbc, 0xde, 0x8e, 0xba, 0xeb, 0x95, 0x23,
	0xa3, 0x9b, 0x70, 0x66, 0xa4, 0x2f, 0xce, 0xd2, 0x4d, 0x8d, 0xf4, 0x4d, 0x4d, 0x7f, 0x46, 0x70,
	0x7a, 0xd8, 0xcd, 0x3f, 0xe3, 0xfa, 0x55, 0x6a, 0x68, 0x90, 0x17, 0xe3, 0x25, 0x79, 0xc9, 0x0e,
	0xe5, 0xe5, 0x86, 0x8e, 0xc1, 0x28, 0x39, 0xc9, 0xca, 0x44, 0x47, 0x55, 0xa6, 0x0b, 0x0b, 0x9b,
	0x96, 0x63, 0x79, 0x56, 0x2c, 0x65, 0xc7, 0x19, 0x35, 0x5d, 0x82, 0xc5, 0xe1, 0x0b, 0x39, 0xa3,
	0x5f, 0x23, 0x28, 0x28, 0xc5, 0x7f, 0xd0, 0xbe, 0xf1, 0xb1, 0x67, 0x68, 0xc6, 0x1e, 0x9d, 0x87,
	0xb9, 0x04, 0x18, 0xce, 0xe8, 0x13, 0x04, 0xf9, 0x6d, 0xcb, 0xfb, 0x3f, 0xa0, 0x7b, 0x0b, 0x4e,
	0x0c, 0x90, 0x70, 0x86, 0x2f, 0xc0, 0xa4, 0x54, 0x95, 0x10, 0x41, 0x9a, 0x74, 0x2b, 0x25, 0x75,
	0xa0, 0x98, 0xa8, 0x9b, 0xe3, 0xcd, 0x73, 0x15, 0xe6, 0x53, 0xb7, 0x71, 0x86, 0x5f, 0x87, 0x49,
	0x79, 0x34, 0x00, 0xaa, 0x79, 0x2e, 0x94, 0x5e, 0x90, 0x8d, 0x3f, 0x70, 0x4d, 0x7b, 0xef, 0x91,
	0x14, 0xef, 0xb4, 0xbb, 0xd6, 0xf1, 0x72, 0x3e, 0x72, 0x0e, 0xd2, 0x5f, 0x10, 0x2c, 0xc4, 0xa0,
	0xec, 0x8a, 0xd5, 0xe3, 0x78, 0xb1, 0x8c, 0xb1, 0xf7, 0xa4, 0x16, 0x87, 0xec, 0x88, 0xc5, 0x61,
	0x09, 0x16, 0x87, 0xa1, 0x73, 0x76, 0xe9, 0x0e, 0x40, 0x33, 0xb2, 0xc2, 0xa7, 0x60, 0xa1, 0xd9,
	0xb8, 0xd9, 0xba, 0xb7, 0xd5, 0xb8, 0x5b, 0xbf, 0xb5, 0xd3, 0xda, 0xdc, 0x7a, 0xef, 0x5a, 0xf3,
	0xe6, 0x6e, 0x71, 0x02, 0x9f, 0x84, 0xf9, 0xb8, 0xe2, 0x4e, 0x7d, 0xa7, 0xde, 0x2c, 0x22, 0xbc,
	0x00, 0x73, 0x71, 0xf1, 0xad, 0x8d, 0xbb, 0xc5, 0xcc, 0xfa, 0xf7, 0xb3, 0xa0, 0x16, 0x6d, 0xfc,
	0x13, 0x82, 0x7c, 0x6c, 0x33, 0xc0, 0x27, 0xc3, 0x2c, 0x27, 0x36, 0x98, 0xf2, 0x92, 0x4e, 0xcc,
	0x19, 0xfd, 0xf8, 0xf1, 0xc1, 0xa1, 0x81, 0xbe, 0x3a, 0x38, 0x34, 0x72, 0xac, 0x62, 0x56, 0x7a,
	0x15, 0xbf, 0xf2, 0xf4, 0xe0, 0xd0, 0xd8, 0x5a, 0x65, 0xa4, 0xaa, 0x38, 0x25, 0xb6, 0x59, 0x23,
	0xab, 0x26, 0xa9, 0xda, 0x9c, 0x04, 0xeb, 0x0d, 0x91, 0xb4, 0xd6, 0xc8, 0x6a, 0x8f, 0x54, 0xe5,
	0x4f, 0x22, 0x72, 0x57, 0x23, 0xab, 0x3e, 0xa9, 0x06, 0x0b, 0x0b, 0xf1, 0x6d, 0xb3, 0x86, 0x3f,
	0x82, 0x42, 0x72, 0xb9, 0xc0, 0x67, 0xd7, 0xa2, 0x2f, 0x82, 0xb5, 0xbb, 0x37, 0x36, 0xd4, 0x17,
	0xc1, 0x56, 0x97, 0x79, 0x8f, 0x5a, 0xb7, 0x37, 0xca, 0xa7, 0x03, 0x94, 0xc3, 0x1b, 0x09, 0x9d,
	0x13, 0x40, 0x33, 0x02, 0xe8, 0x84, 0x00, 0x38, 0x81, 0xbf, 0x89, 0x02, 0x57, 0xdf, 0x0f, 0xc9,
	0xc0, 0xc3, 0x87, 0x23, 0x15, 0xf8, 0xe0, 0x81, 0xd9, 0x11, 0xfe, 0x0c, 0xe1, 0x6f, 0x8a, 0x55,
	0x3a, 0x41, 0xd8, 0xef, 0xc4, 0xc2, 0x56, 0x81, 0xc9, 0xe0, 0x3b, 0xa4, 0x2a, 0x4b, 0x26, 0x2e,
	0x4b, 0x87, 0xfa, 0x2d, 0x4a, 0xb5, 0xa0, 0x78, 0xf0, 0xf1, 0x99, 0x41, 0x40, 0x43, 0x8b, 0x45,
	0xf9, 0xec, 0x68, 0x25, 0x67, 0xf4, 0x5d, 0x01, 0x30, 0x1b, 0x00, 0x74, 0x2b, 0x5c, 0x02, 0xbc,
	0xa4, 0x07, 0xe8, 0x92, 0xaa, 0x1a, 0x64, 0x35, 0xb2, 0xca, 0x49, 0x55, 0x0c, 0xae, 0x1a, 0x7e,
	0x86, 0x60, 0x49, 0x3f, 0xba, 0x30, 0xd1, 0xdd, 0x1c, 0x1f, 0xc4, 0xe5, 0xf3, 0x47, 0x58, 0x70,
	0x46, 0x9b, 0x02, 0xe0, 0x64, 0x50, 0x3a, 0x9d, 0x08, 0x62, 0x6d, 0x7c, 0x0e, 0xb5, 0xb0, 0x9f,
	0x20, 0x28, 0xa6, 0x67, 0x16, 0x2e, 0x07, 0x70, 0x34, 0xd3, 0xb3, 0x7c, 0x66, 0xa4, 0x8e, 0x33,
	0x7a, 0x55, 0x80, 0x9c, 0x12, 0x20, 0xb3, 0x02, 0xa4, 0x00, 0xb8, 0x32, 0x2e, 0x40, 0xfc, 0x25,
	0x82, 0x7c, 0x6c, 0x30, 0x45, 0x25, 0x96, 0x9c, 0x9c, 0x51, 0x89, 0xa5, 0x67, 0xd8, 0x0d, 0x71,
	0xf7, 0x74, 0x54, 0x62, 0xfb, 0xf2, 0xf6, 0xb7, 0xc7, 0xa7, 0x67, 0x9f, 0x54, 0xe5, 0x2c, 0x91,
	0x58, 0x1e, 0x23, 0xc8, 0x85, 0x63, 0x08, 0xe3, 0x41, 0x76, 0x22, 0x14, 0x0b, 0x43, 0xb2, 0x10,
	0x42, 0xee, 0xdf, 0x83, 0x30, 0x9b, 0xa8, 0x07, 0x7c, 0x4a, 0x57, 0x25, 0x02, 0x4c, 0x49, 0xaf,
	0xe0, 0x8c, 0x56, 0x05, 0xa2, 0x99, 0x44, 0x42, 0xde, 0x18, 0x1b, 0x0f, 0xfe, 0x01, 0xc1, 0x5c,
	0x6a, 0x52, 0xe1, 0xf0, 0xd1, 0x18, 0x9e, 0x60, 0xe5, 0x97, 0xbe, 0x36, 0x74, 0x5b, 0x40, 0x81,
	0x88, 0x9c, 0x9e, 0x04, 0xb3, 0x3e, 0x3e, 0x39, 0x3d, 0x52, 0x95, 0xef, 0x1e, 0xfe, 0x02, 0xc1,
	0xa9, 0x11, 0xab, 0x31, 0x1e, 0xdd, 0x48, 0xe1, 0x1a, 0x5e, 0xa6, 0x47, 0x99, 0x70, 0x46, 0x2f,
	0x0a, 0xac, 0x79, 0x81, 0x35, 0xc3, 0x24, 0xce, 0x45, 0x1d, 0x4e, 0xdc, 0x87, 0x62, 0x7a, 0x04,
	0x45, 0xbd, 0xa3, 0x19, 0xab, 0x51, 0xef, 0xe8, 0xe6, 0x96, 0xba, 0xf3, 0xc4, 0x51, 0x77, 0x96,
	0xa7, 0x9e, 0x1c, 0x1c, 0x1a, 0x7f, 0x79, 0x1b, 0xc5, 0xe7, 0x2f, 0x96, 0xd1, 0xaf, 0x2f, 0x96,
	0xd1, 0x1f, 0x2f, 0x96, 0xd1, 0x77, 0x7f, 0x2e, 0x4f, 0xfc, 0x1d, 0x00, 0x00, 0xff, 0xff, 0x49,
	0xb3, 0x51, 0xd5, 0x26, 0x12, 0x00, 0x00,
}
