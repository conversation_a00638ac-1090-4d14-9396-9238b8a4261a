// Code generated by protoc-gen-go. DO NOT EDIT.
// source: newbiesvr/newbiesvr.proto

package newbiesvr // import "golang.52tt.com/protocol/services/newbiesvr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CheckUserRegDevReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserRegDevReq) Reset()         { *m = CheckUserRegDevReq{} }
func (m *CheckUserRegDevReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserRegDevReq) ProtoMessage()    {}
func (*CheckUserRegDevReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_newbiesvr_3dd145b62535519c, []int{0}
}
func (m *CheckUserRegDevReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserRegDevReq.Unmarshal(m, b)
}
func (m *CheckUserRegDevReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserRegDevReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserRegDevReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserRegDevReq.Merge(dst, src)
}
func (m *CheckUserRegDevReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserRegDevReq.Size(m)
}
func (m *CheckUserRegDevReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserRegDevReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserRegDevReq proto.InternalMessageInfo

func (m *CheckUserRegDevReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUserRegDevResp struct {
	DevId                string   `protobuf:"bytes,1,opt,name=dev_id,json=devId,proto3" json:"dev_id,omitempty"`
	IsNewDev             bool     `protobuf:"varint,2,opt,name=is_new_dev,json=isNewDev,proto3" json:"is_new_dev,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserRegDevResp) Reset()         { *m = CheckUserRegDevResp{} }
func (m *CheckUserRegDevResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserRegDevResp) ProtoMessage()    {}
func (*CheckUserRegDevResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_newbiesvr_3dd145b62535519c, []int{1}
}
func (m *CheckUserRegDevResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserRegDevResp.Unmarshal(m, b)
}
func (m *CheckUserRegDevResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserRegDevResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserRegDevResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserRegDevResp.Merge(dst, src)
}
func (m *CheckUserRegDevResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserRegDevResp.Size(m)
}
func (m *CheckUserRegDevResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserRegDevResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserRegDevResp proto.InternalMessageInfo

func (m *CheckUserRegDevResp) GetDevId() string {
	if m != nil {
		return m.DevId
	}
	return ""
}

func (m *CheckUserRegDevResp) GetIsNewDev() bool {
	if m != nil {
		return m.IsNewDev
	}
	return false
}

// 查询设备是否新设备
type CheckRegDevIsNewReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DevId                string   `protobuf:"bytes,2,opt,name=dev_id,json=devId,proto3" json:"dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckRegDevIsNewReq) Reset()         { *m = CheckRegDevIsNewReq{} }
func (m *CheckRegDevIsNewReq) String() string { return proto.CompactTextString(m) }
func (*CheckRegDevIsNewReq) ProtoMessage()    {}
func (*CheckRegDevIsNewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_newbiesvr_3dd145b62535519c, []int{2}
}
func (m *CheckRegDevIsNewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRegDevIsNewReq.Unmarshal(m, b)
}
func (m *CheckRegDevIsNewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRegDevIsNewReq.Marshal(b, m, deterministic)
}
func (dst *CheckRegDevIsNewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRegDevIsNewReq.Merge(dst, src)
}
func (m *CheckRegDevIsNewReq) XXX_Size() int {
	return xxx_messageInfo_CheckRegDevIsNewReq.Size(m)
}
func (m *CheckRegDevIsNewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRegDevIsNewReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRegDevIsNewReq proto.InternalMessageInfo

func (m *CheckRegDevIsNewReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckRegDevIsNewReq) GetDevId() string {
	if m != nil {
		return m.DevId
	}
	return ""
}

type CheckRegDevIsNewResp struct {
	IsNew                bool     `protobuf:"varint,1,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckRegDevIsNewResp) Reset()         { *m = CheckRegDevIsNewResp{} }
func (m *CheckRegDevIsNewResp) String() string { return proto.CompactTextString(m) }
func (*CheckRegDevIsNewResp) ProtoMessage()    {}
func (*CheckRegDevIsNewResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_newbiesvr_3dd145b62535519c, []int{3}
}
func (m *CheckRegDevIsNewResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRegDevIsNewResp.Unmarshal(m, b)
}
func (m *CheckRegDevIsNewResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRegDevIsNewResp.Marshal(b, m, deterministic)
}
func (dst *CheckRegDevIsNewResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRegDevIsNewResp.Merge(dst, src)
}
func (m *CheckRegDevIsNewResp) XXX_Size() int {
	return xxx_messageInfo_CheckRegDevIsNewResp.Size(m)
}
func (m *CheckRegDevIsNewResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRegDevIsNewResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRegDevIsNewResp proto.InternalMessageInfo

func (m *CheckRegDevIsNewResp) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

// 更新
type UpdateDevRegCntReq struct {
	DevId                string   `protobuf:"bytes,1,opt,name=dev_id,json=devId,proto3" json:"dev_id,omitempty"`
	RegCnt               uint32   `protobuf:"varint,2,opt,name=reg_cnt,json=regCnt,proto3" json:"reg_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDevRegCntReq) Reset()         { *m = UpdateDevRegCntReq{} }
func (m *UpdateDevRegCntReq) String() string { return proto.CompactTextString(m) }
func (*UpdateDevRegCntReq) ProtoMessage()    {}
func (*UpdateDevRegCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_newbiesvr_3dd145b62535519c, []int{4}
}
func (m *UpdateDevRegCntReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDevRegCntReq.Unmarshal(m, b)
}
func (m *UpdateDevRegCntReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDevRegCntReq.Marshal(b, m, deterministic)
}
func (dst *UpdateDevRegCntReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDevRegCntReq.Merge(dst, src)
}
func (m *UpdateDevRegCntReq) XXX_Size() int {
	return xxx_messageInfo_UpdateDevRegCntReq.Size(m)
}
func (m *UpdateDevRegCntReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDevRegCntReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDevRegCntReq proto.InternalMessageInfo

func (m *UpdateDevRegCntReq) GetDevId() string {
	if m != nil {
		return m.DevId
	}
	return ""
}

func (m *UpdateDevRegCntReq) GetRegCnt() uint32 {
	if m != nil {
		return m.RegCnt
	}
	return 0
}

type UpdateDevRegCntResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDevRegCntResp) Reset()         { *m = UpdateDevRegCntResp{} }
func (m *UpdateDevRegCntResp) String() string { return proto.CompactTextString(m) }
func (*UpdateDevRegCntResp) ProtoMessage()    {}
func (*UpdateDevRegCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_newbiesvr_3dd145b62535519c, []int{5}
}
func (m *UpdateDevRegCntResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDevRegCntResp.Unmarshal(m, b)
}
func (m *UpdateDevRegCntResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDevRegCntResp.Marshal(b, m, deterministic)
}
func (dst *UpdateDevRegCntResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDevRegCntResp.Merge(dst, src)
}
func (m *UpdateDevRegCntResp) XXX_Size() int {
	return xxx_messageInfo_UpdateDevRegCntResp.Size(m)
}
func (m *UpdateDevRegCntResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDevRegCntResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDevRegCntResp proto.InternalMessageInfo

// 查询设备信息
type GetDevInfoReq struct {
	DevId                string   `protobuf:"bytes,1,opt,name=dev_id,json=devId,proto3" json:"dev_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDevInfoReq) Reset()         { *m = GetDevInfoReq{} }
func (m *GetDevInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetDevInfoReq) ProtoMessage()    {}
func (*GetDevInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_newbiesvr_3dd145b62535519c, []int{6}
}
func (m *GetDevInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDevInfoReq.Unmarshal(m, b)
}
func (m *GetDevInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDevInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetDevInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDevInfoReq.Merge(dst, src)
}
func (m *GetDevInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetDevInfoReq.Size(m)
}
func (m *GetDevInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDevInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDevInfoReq proto.InternalMessageInfo

func (m *GetDevInfoReq) GetDevId() string {
	if m != nil {
		return m.DevId
	}
	return ""
}

type GetDevInfoResp struct {
	RegCnt               uint32   `protobuf:"varint,1,opt,name=reg_cnt,json=regCnt,proto3" json:"reg_cnt,omitempty"`
	FirstRegUid          uint32   `protobuf:"varint,2,opt,name=first_reg_uid,json=firstRegUid,proto3" json:"first_reg_uid,omitempty"`
	FirstRegTime         uint32   `protobuf:"varint,3,opt,name=first_reg_time,json=firstRegTime,proto3" json:"first_reg_time,omitempty"`
	FirstLoginUid        uint32   `protobuf:"varint,4,opt,name=first_login_uid,json=firstLoginUid,proto3" json:"first_login_uid,omitempty"`
	FirstLoginTime       uint32   `protobuf:"varint,5,opt,name=first_login_time,json=firstLoginTime,proto3" json:"first_login_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDevInfoResp) Reset()         { *m = GetDevInfoResp{} }
func (m *GetDevInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetDevInfoResp) ProtoMessage()    {}
func (*GetDevInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_newbiesvr_3dd145b62535519c, []int{7}
}
func (m *GetDevInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDevInfoResp.Unmarshal(m, b)
}
func (m *GetDevInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDevInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetDevInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDevInfoResp.Merge(dst, src)
}
func (m *GetDevInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetDevInfoResp.Size(m)
}
func (m *GetDevInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDevInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDevInfoResp proto.InternalMessageInfo

func (m *GetDevInfoResp) GetRegCnt() uint32 {
	if m != nil {
		return m.RegCnt
	}
	return 0
}

func (m *GetDevInfoResp) GetFirstRegUid() uint32 {
	if m != nil {
		return m.FirstRegUid
	}
	return 0
}

func (m *GetDevInfoResp) GetFirstRegTime() uint32 {
	if m != nil {
		return m.FirstRegTime
	}
	return 0
}

func (m *GetDevInfoResp) GetFirstLoginUid() uint32 {
	if m != nil {
		return m.FirstLoginUid
	}
	return 0
}

func (m *GetDevInfoResp) GetFirstLoginTime() uint32 {
	if m != nil {
		return m.FirstLoginTime
	}
	return 0
}

func init() {
	proto.RegisterType((*CheckUserRegDevReq)(nil), "newbiesvr.CheckUserRegDevReq")
	proto.RegisterType((*CheckUserRegDevResp)(nil), "newbiesvr.CheckUserRegDevResp")
	proto.RegisterType((*CheckRegDevIsNewReq)(nil), "newbiesvr.CheckRegDevIsNewReq")
	proto.RegisterType((*CheckRegDevIsNewResp)(nil), "newbiesvr.CheckRegDevIsNewResp")
	proto.RegisterType((*UpdateDevRegCntReq)(nil), "newbiesvr.UpdateDevRegCntReq")
	proto.RegisterType((*UpdateDevRegCntResp)(nil), "newbiesvr.UpdateDevRegCntResp")
	proto.RegisterType((*GetDevInfoReq)(nil), "newbiesvr.GetDevInfoReq")
	proto.RegisterType((*GetDevInfoResp)(nil), "newbiesvr.GetDevInfoResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// NewbieSvrClient is the client API for NewbieSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type NewbieSvrClient interface {
	CheckUserRegDev(ctx context.Context, in *CheckUserRegDevReq, opts ...grpc.CallOption) (*CheckUserRegDevResp, error)
	CheckRegDevIsNew(ctx context.Context, in *CheckRegDevIsNewReq, opts ...grpc.CallOption) (*CheckRegDevIsNewResp, error)
	UpdateDevRegCnt(ctx context.Context, in *UpdateDevRegCntReq, opts ...grpc.CallOption) (*UpdateDevRegCntResp, error)
	GetDevInfo(ctx context.Context, in *GetDevInfoReq, opts ...grpc.CallOption) (*GetDevInfoResp, error)
}

type newbieSvrClient struct {
	cc *grpc.ClientConn
}

func NewNewbieSvrClient(cc *grpc.ClientConn) NewbieSvrClient {
	return &newbieSvrClient{cc}
}

func (c *newbieSvrClient) CheckUserRegDev(ctx context.Context, in *CheckUserRegDevReq, opts ...grpc.CallOption) (*CheckUserRegDevResp, error) {
	out := new(CheckUserRegDevResp)
	err := c.cc.Invoke(ctx, "/newbiesvr.NewbieSvr/CheckUserRegDev", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newbieSvrClient) CheckRegDevIsNew(ctx context.Context, in *CheckRegDevIsNewReq, opts ...grpc.CallOption) (*CheckRegDevIsNewResp, error) {
	out := new(CheckRegDevIsNewResp)
	err := c.cc.Invoke(ctx, "/newbiesvr.NewbieSvr/CheckRegDevIsNew", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newbieSvrClient) UpdateDevRegCnt(ctx context.Context, in *UpdateDevRegCntReq, opts ...grpc.CallOption) (*UpdateDevRegCntResp, error) {
	out := new(UpdateDevRegCntResp)
	err := c.cc.Invoke(ctx, "/newbiesvr.NewbieSvr/UpdateDevRegCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newbieSvrClient) GetDevInfo(ctx context.Context, in *GetDevInfoReq, opts ...grpc.CallOption) (*GetDevInfoResp, error) {
	out := new(GetDevInfoResp)
	err := c.cc.Invoke(ctx, "/newbiesvr.NewbieSvr/GetDevInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NewbieSvrServer is the server API for NewbieSvr service.
type NewbieSvrServer interface {
	CheckUserRegDev(context.Context, *CheckUserRegDevReq) (*CheckUserRegDevResp, error)
	CheckRegDevIsNew(context.Context, *CheckRegDevIsNewReq) (*CheckRegDevIsNewResp, error)
	UpdateDevRegCnt(context.Context, *UpdateDevRegCntReq) (*UpdateDevRegCntResp, error)
	GetDevInfo(context.Context, *GetDevInfoReq) (*GetDevInfoResp, error)
}

func RegisterNewbieSvrServer(s *grpc.Server, srv NewbieSvrServer) {
	s.RegisterService(&_NewbieSvr_serviceDesc, srv)
}

func _NewbieSvr_CheckUserRegDev_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserRegDevReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewbieSvrServer).CheckUserRegDev(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/newbiesvr.NewbieSvr/CheckUserRegDev",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewbieSvrServer).CheckUserRegDev(ctx, req.(*CheckUserRegDevReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewbieSvr_CheckRegDevIsNew_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRegDevIsNewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewbieSvrServer).CheckRegDevIsNew(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/newbiesvr.NewbieSvr/CheckRegDevIsNew",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewbieSvrServer).CheckRegDevIsNew(ctx, req.(*CheckRegDevIsNewReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewbieSvr_UpdateDevRegCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDevRegCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewbieSvrServer).UpdateDevRegCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/newbiesvr.NewbieSvr/UpdateDevRegCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewbieSvrServer).UpdateDevRegCnt(ctx, req.(*UpdateDevRegCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewbieSvr_GetDevInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDevInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewbieSvrServer).GetDevInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/newbiesvr.NewbieSvr/GetDevInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewbieSvrServer).GetDevInfo(ctx, req.(*GetDevInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NewbieSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "newbiesvr.NewbieSvr",
	HandlerType: (*NewbieSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckUserRegDev",
			Handler:    _NewbieSvr_CheckUserRegDev_Handler,
		},
		{
			MethodName: "CheckRegDevIsNew",
			Handler:    _NewbieSvr_CheckRegDevIsNew_Handler,
		},
		{
			MethodName: "UpdateDevRegCnt",
			Handler:    _NewbieSvr_UpdateDevRegCnt_Handler,
		},
		{
			MethodName: "GetDevInfo",
			Handler:    _NewbieSvr_GetDevInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "newbiesvr/newbiesvr.proto",
}

func init() {
	proto.RegisterFile("newbiesvr/newbiesvr.proto", fileDescriptor_newbiesvr_3dd145b62535519c)
}

var fileDescriptor_newbiesvr_3dd145b62535519c = []byte{
	// 440 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x93, 0x4f, 0x6f, 0xd3, 0x40,
	0x10, 0xc5, 0xeb, 0x94, 0x84, 0x64, 0xc0, 0x49, 0xb4, 0xa5, 0xc2, 0x8d, 0xf8, 0x53, 0xad, 0x50,
	0x14, 0x09, 0xd5, 0x91, 0x8a, 0xb8, 0x72, 0x20, 0x91, 0x50, 0x11, 0xea, 0x61, 0xc1, 0x17, 0x2e,
	0x56, 0x6b, 0x4f, 0xcd, 0x8a, 0xc4, 0x36, 0xbb, 0x9b, 0xcd, 0x27, 0xe4, 0xcc, 0x57, 0x42, 0xbb,
	0x4e, 0x63, 0x3b, 0x4e, 0x72, 0xb3, 0x66, 0xde, 0xfc, 0xde, 0x58, 0xf3, 0x16, 0x2e, 0x52, 0x5c,
	0xdf, 0x73, 0x94, 0x5a, 0x4c, 0xb7, 0x5f, 0x7e, 0x2e, 0x32, 0x95, 0x91, 0xde, 0xb6, 0x40, 0xc7,
	0x40, 0x66, 0xbf, 0x30, 0xfa, 0x1d, 0x48, 0x14, 0x0c, 0x93, 0x39, 0x6a, 0x86, 0x7f, 0xc8, 0x10,
	0x4e, 0x57, 0x3c, 0xf6, 0x9c, 0x4b, 0x67, 0xe2, 0x32, 0xf3, 0x49, 0xbf, 0xc2, 0x59, 0x43, 0x27,
	0x73, 0x72, 0x0e, 0x9d, 0x18, 0x75, 0xb8, 0xd1, 0xf6, 0x58, 0x3b, 0x46, 0x7d, 0x13, 0x93, 0x57,
	0x00, 0x5c, 0x86, 0x29, 0xae, 0xc3, 0x18, 0xb5, 0xd7, 0xba, 0x74, 0x26, 0x5d, 0xd6, 0xe5, 0xf2,
	0x16, 0xd7, 0x73, 0xd4, 0xf4, 0xd3, 0x86, 0x55, 0x70, 0x6e, 0x4c, 0x79, 0xaf, 0x69, 0x85, 0xde,
	0xaa, 0xd0, 0xe9, 0x15, 0xbc, 0x68, 0xce, 0x17, 0xcb, 0x14, 0xae, 0x96, 0xd1, 0x65, 0x6d, 0xeb,
	0x48, 0xe7, 0x40, 0x82, 0x3c, 0xbe, 0x53, 0x68, 0x97, 0x4e, 0x66, 0xa9, 0x32, 0x6e, 0x07, 0x36,
	0x7f, 0x09, 0x4f, 0x05, 0x26, 0x61, 0x94, 0x2a, 0xeb, 0xe9, 0xb2, 0x8e, 0xb0, 0x23, 0xf4, 0x1c,
	0xce, 0x1a, 0x14, 0x99, 0xd3, 0x31, 0xb8, 0x5f, 0x50, 0x99, 0x35, 0xd2, 0x87, 0xec, 0x30, 0x97,
	0xfe, 0x75, 0xa0, 0x5f, 0x15, 0xca, 0xbc, 0x6a, 0xe5, 0x54, 0xad, 0x08, 0x05, 0xf7, 0x81, 0x0b,
	0xa9, 0x42, 0xd3, 0x5e, 0x6d, 0xfe, 0xde, 0x65, 0xcf, 0x6c, 0x91, 0x61, 0x12, 0xf0, 0x98, 0xbc,
	0x83, 0x7e, 0xa9, 0x51, 0x7c, 0x89, 0xde, 0xa9, 0x15, 0x3d, 0x7f, 0x14, 0xfd, 0xe0, 0x4b, 0x24,
	0x63, 0x18, 0x14, 0xaa, 0x45, 0x96, 0xf0, 0xd4, 0xb2, 0x9e, 0x58, 0x59, 0x61, 0xf0, 0xcd, 0x54,
	0x0d, 0x6d, 0x02, 0xc3, 0xaa, 0xce, 0xf2, 0xda, 0x56, 0xd8, 0x2f, 0x85, 0x86, 0x78, 0xfd, 0xaf,
	0x05, 0xbd, 0x5b, 0x9b, 0x9e, 0xef, 0x5a, 0x10, 0x06, 0x83, 0x9d, 0x54, 0x90, 0xd7, 0x7e, 0x99,
	0xb6, 0x66, 0xb2, 0x46, 0x6f, 0x8e, 0xb5, 0x65, 0x4e, 0x4f, 0x48, 0x00, 0xc3, 0xdd, 0xeb, 0x92,
	0xc6, 0x54, 0x3d, 0x3a, 0xa3, 0xb7, 0x47, 0xfb, 0x16, 0xcb, 0x60, 0xb0, 0x73, 0xbf, 0xda, 0xaa,
	0xcd, 0x84, 0xd4, 0x56, 0xdd, 0x77, 0xfa, 0x13, 0x32, 0x03, 0x28, 0x6f, 0x4a, 0xbc, 0x8a, 0xbe,
	0x96, 0x89, 0xd1, 0xc5, 0x81, 0x8e, 0x81, 0x7c, 0xbe, 0xfa, 0xf9, 0x3e, 0xc9, 0x16, 0x77, 0x69,
	0xe2, 0x7f, 0xbc, 0x56, 0xca, 0x8f, 0xb2, 0xe5, 0xd4, 0xbe, 0xd2, 0x28, 0x5b, 0x4c, 0x25, 0x0a,
	0xcd, 0x23, 0x94, 0xe5, 0x0b, 0xbe, 0xef, 0xd8, 0xe6, 0x87, 0xff, 0x01, 0x00, 0x00, 0xff, 0xff,
	0xd0, 0xd0, 0xc6, 0x07, 0xdf, 0x03, 0x00, 0x00,
}
