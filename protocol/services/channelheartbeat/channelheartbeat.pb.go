// Code generated by protoc-gen-gogo.
// source: src/channelheartbeat/channelheartbeat.proto
// DO NOT EDIT!

/*
Package channelheartbeat is a generated protocol buffer package.

namespace

It is generated from these files:
	src/channelheartbeat/channelheartbeat.proto

It has these top-level messages:
	UserHeartbeatInfo
	UpdateUserHeartbeatReq
	UpdateUserHeartbeatResp
	RemoveUserHeartbeatReq
	RemoveUserHeartbeatResp
	CheckUserHeartRecordExistReq
	CheckUserHeartRecordExistResp
*/
package channelheartbeat

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type UserHeartbeatInfo struct {
	Uid       uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId uint32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
}

func (m *UserHeartbeatInfo) Reset()         { *m = UserHeartbeatInfo{} }
func (m *UserHeartbeatInfo) String() string { return proto.CompactTextString(m) }
func (*UserHeartbeatInfo) ProtoMessage()    {}
func (*UserHeartbeatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelheartbeat, []int{0}
}

func (m *UserHeartbeatInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserHeartbeatInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type UpdateUserHeartbeatReq struct {
	Info *UserHeartbeatInfo `protobuf:"bytes,1,opt,name=info" json:"info,omitempty"`
}

func (m *UpdateUserHeartbeatReq) Reset()         { *m = UpdateUserHeartbeatReq{} }
func (m *UpdateUserHeartbeatReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserHeartbeatReq) ProtoMessage()    {}
func (*UpdateUserHeartbeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelheartbeat, []int{1}
}

func (m *UpdateUserHeartbeatReq) GetInfo() *UserHeartbeatInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateUserHeartbeatResp struct {
}

func (m *UpdateUserHeartbeatResp) Reset()         { *m = UpdateUserHeartbeatResp{} }
func (m *UpdateUserHeartbeatResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserHeartbeatResp) ProtoMessage()    {}
func (*UpdateUserHeartbeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelheartbeat, []int{2}
}

type RemoveUserHeartbeatReq struct {
	Info *UserHeartbeatInfo `protobuf:"bytes,1,opt,name=info" json:"info,omitempty"`
}

func (m *RemoveUserHeartbeatReq) Reset()         { *m = RemoveUserHeartbeatReq{} }
func (m *RemoveUserHeartbeatReq) String() string { return proto.CompactTextString(m) }
func (*RemoveUserHeartbeatReq) ProtoMessage()    {}
func (*RemoveUserHeartbeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelheartbeat, []int{3}
}

func (m *RemoveUserHeartbeatReq) GetInfo() *UserHeartbeatInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type RemoveUserHeartbeatResp struct {
}

func (m *RemoveUserHeartbeatResp) Reset()         { *m = RemoveUserHeartbeatResp{} }
func (m *RemoveUserHeartbeatResp) String() string { return proto.CompactTextString(m) }
func (*RemoveUserHeartbeatResp) ProtoMessage()    {}
func (*RemoveUserHeartbeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelheartbeat, []int{4}
}

type CheckUserHeartRecordExistReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *CheckUserHeartRecordExistReq) Reset()         { *m = CheckUserHeartRecordExistReq{} }
func (m *CheckUserHeartRecordExistReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserHeartRecordExistReq) ProtoMessage()    {}
func (*CheckUserHeartRecordExistReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelheartbeat, []int{5}
}

func (m *CheckUserHeartRecordExistReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type CheckUserHeartRecordExistResp struct {
	ExistUidList []uint32 `protobuf:"varint,1,rep,packed,name=exist_uid_list,json=existUidList" json:"exist_uid_list,omitempty"`
}

func (m *CheckUserHeartRecordExistResp) Reset()         { *m = CheckUserHeartRecordExistResp{} }
func (m *CheckUserHeartRecordExistResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserHeartRecordExistResp) ProtoMessage()    {}
func (*CheckUserHeartRecordExistResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelheartbeat, []int{6}
}

func (m *CheckUserHeartRecordExistResp) GetExistUidList() []uint32 {
	if m != nil {
		return m.ExistUidList
	}
	return nil
}

func init() {
	proto.RegisterType((*UserHeartbeatInfo)(nil), "channelheartbeat.UserHeartbeatInfo")
	proto.RegisterType((*UpdateUserHeartbeatReq)(nil), "channelheartbeat.UpdateUserHeartbeatReq")
	proto.RegisterType((*UpdateUserHeartbeatResp)(nil), "channelheartbeat.UpdateUserHeartbeatResp")
	proto.RegisterType((*RemoveUserHeartbeatReq)(nil), "channelheartbeat.RemoveUserHeartbeatReq")
	proto.RegisterType((*RemoveUserHeartbeatResp)(nil), "channelheartbeat.RemoveUserHeartbeatResp")
	proto.RegisterType((*CheckUserHeartRecordExistReq)(nil), "channelheartbeat.CheckUserHeartRecordExistReq")
	proto.RegisterType((*CheckUserHeartRecordExistResp)(nil), "channelheartbeat.CheckUserHeartRecordExistResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ChannelHeartbeat service

type ChannelHeartbeatClient interface {
	UpdateUserHeartbeat(ctx context.Context, in *UpdateUserHeartbeatReq, opts ...grpc.CallOption) (*UpdateUserHeartbeatResp, error)
	RemoveUserHeartbeat(ctx context.Context, in *RemoveUserHeartbeatReq, opts ...grpc.CallOption) (*RemoveUserHeartbeatResp, error)
	CheckUserHeartRecordExist(ctx context.Context, in *CheckUserHeartRecordExistReq, opts ...grpc.CallOption) (*CheckUserHeartRecordExistResp, error)
}

type channelHeartbeatClient struct {
	cc *grpc.ClientConn
}

func NewChannelHeartbeatClient(cc *grpc.ClientConn) ChannelHeartbeatClient {
	return &channelHeartbeatClient{cc}
}

func (c *channelHeartbeatClient) UpdateUserHeartbeat(ctx context.Context, in *UpdateUserHeartbeatReq, opts ...grpc.CallOption) (*UpdateUserHeartbeatResp, error) {
	out := new(UpdateUserHeartbeatResp)
	err := grpc.Invoke(ctx, "/channelheartbeat.ChannelHeartbeat/UpdateUserHeartbeat", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelHeartbeatClient) RemoveUserHeartbeat(ctx context.Context, in *RemoveUserHeartbeatReq, opts ...grpc.CallOption) (*RemoveUserHeartbeatResp, error) {
	out := new(RemoveUserHeartbeatResp)
	err := grpc.Invoke(ctx, "/channelheartbeat.ChannelHeartbeat/RemoveUserHeartbeat", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelHeartbeatClient) CheckUserHeartRecordExist(ctx context.Context, in *CheckUserHeartRecordExistReq, opts ...grpc.CallOption) (*CheckUserHeartRecordExistResp, error) {
	out := new(CheckUserHeartRecordExistResp)
	err := grpc.Invoke(ctx, "/channelheartbeat.ChannelHeartbeat/CheckUserHeartRecordExist", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ChannelHeartbeat service

type ChannelHeartbeatServer interface {
	UpdateUserHeartbeat(context.Context, *UpdateUserHeartbeatReq) (*UpdateUserHeartbeatResp, error)
	RemoveUserHeartbeat(context.Context, *RemoveUserHeartbeatReq) (*RemoveUserHeartbeatResp, error)
	CheckUserHeartRecordExist(context.Context, *CheckUserHeartRecordExistReq) (*CheckUserHeartRecordExistResp, error)
}

func RegisterChannelHeartbeatServer(s *grpc.Server, srv ChannelHeartbeatServer) {
	s.RegisterService(&_ChannelHeartbeat_serviceDesc, srv)
}

func _ChannelHeartbeat_UpdateUserHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserHeartbeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelHeartbeatServer).UpdateUserHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelheartbeat.ChannelHeartbeat/UpdateUserHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelHeartbeatServer).UpdateUserHeartbeat(ctx, req.(*UpdateUserHeartbeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelHeartbeat_RemoveUserHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveUserHeartbeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelHeartbeatServer).RemoveUserHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelheartbeat.ChannelHeartbeat/RemoveUserHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelHeartbeatServer).RemoveUserHeartbeat(ctx, req.(*RemoveUserHeartbeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelHeartbeat_CheckUserHeartRecordExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserHeartRecordExistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelHeartbeatServer).CheckUserHeartRecordExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelheartbeat.ChannelHeartbeat/CheckUserHeartRecordExist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelHeartbeatServer).CheckUserHeartRecordExist(ctx, req.(*CheckUserHeartRecordExistReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelHeartbeat_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelheartbeat.ChannelHeartbeat",
	HandlerType: (*ChannelHeartbeatServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateUserHeartbeat",
			Handler:    _ChannelHeartbeat_UpdateUserHeartbeat_Handler,
		},
		{
			MethodName: "RemoveUserHeartbeat",
			Handler:    _ChannelHeartbeat_RemoveUserHeartbeat_Handler,
		},
		{
			MethodName: "CheckUserHeartRecordExist",
			Handler:    _ChannelHeartbeat_CheckUserHeartRecordExist_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelheartbeat/channelheartbeat.proto",
}

func (m *UserHeartbeatInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserHeartbeatInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintChannelheartbeat(dAtA, i, uint64(m.Uid))
	}
	if m.ChannelId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintChannelheartbeat(dAtA, i, uint64(m.ChannelId))
	}
	return i, nil
}

func (m *UpdateUserHeartbeatReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserHeartbeatReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Info != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelheartbeat(dAtA, i, uint64(m.Info.Size()))
		n1, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *UpdateUserHeartbeatResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserHeartbeatResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RemoveUserHeartbeatReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveUserHeartbeatReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Info != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelheartbeat(dAtA, i, uint64(m.Info.Size()))
		n2, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *RemoveUserHeartbeatResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveUserHeartbeatResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CheckUserHeartRecordExistReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserHeartRecordExistReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		dAtA4 := make([]byte, len(m.UidList)*10)
		var j3 int
		for _, num := range m.UidList {
			for num >= 1<<7 {
				dAtA4[j3] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j3++
			}
			dAtA4[j3] = uint8(num)
			j3++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelheartbeat(dAtA, i, uint64(j3))
		i += copy(dAtA[i:], dAtA4[:j3])
	}
	return i, nil
}

func (m *CheckUserHeartRecordExistResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserHeartRecordExistResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ExistUidList) > 0 {
		dAtA6 := make([]byte, len(m.ExistUidList)*10)
		var j5 int
		for _, num := range m.ExistUidList {
			for num >= 1<<7 {
				dAtA6[j5] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j5++
			}
			dAtA6[j5] = uint8(num)
			j5++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelheartbeat(dAtA, i, uint64(j5))
		i += copy(dAtA[i:], dAtA6[:j5])
	}
	return i, nil
}

func encodeFixed64Channelheartbeat(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channelheartbeat(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelheartbeat(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UserHeartbeatInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovChannelheartbeat(uint64(m.Uid))
	}
	if m.ChannelId != 0 {
		n += 1 + sovChannelheartbeat(uint64(m.ChannelId))
	}
	return n
}

func (m *UpdateUserHeartbeatReq) Size() (n int) {
	var l int
	_ = l
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovChannelheartbeat(uint64(l))
	}
	return n
}

func (m *UpdateUserHeartbeatResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RemoveUserHeartbeatReq) Size() (n int) {
	var l int
	_ = l
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovChannelheartbeat(uint64(l))
	}
	return n
}

func (m *RemoveUserHeartbeatResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CheckUserHeartRecordExistReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		l = 0
		for _, e := range m.UidList {
			l += sovChannelheartbeat(uint64(e))
		}
		n += 1 + sovChannelheartbeat(uint64(l)) + l
	}
	return n
}

func (m *CheckUserHeartRecordExistResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ExistUidList) > 0 {
		l = 0
		for _, e := range m.ExistUidList {
			l += sovChannelheartbeat(uint64(e))
		}
		n += 1 + sovChannelheartbeat(uint64(l)) + l
	}
	return n
}

func sovChannelheartbeat(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelheartbeat(x uint64) (n int) {
	return sovChannelheartbeat(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *UserHeartbeatInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelheartbeat
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserHeartbeatInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserHeartbeatInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelheartbeat
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelheartbeat
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelheartbeat(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelheartbeat
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserHeartbeatReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelheartbeat
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserHeartbeatReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserHeartbeatReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelheartbeat
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelheartbeat
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &UserHeartbeatInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelheartbeat(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelheartbeat
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserHeartbeatResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelheartbeat
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserHeartbeatResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserHeartbeatResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelheartbeat(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelheartbeat
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveUserHeartbeatReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelheartbeat
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveUserHeartbeatReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveUserHeartbeatReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelheartbeat
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelheartbeat
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &UserHeartbeatInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelheartbeat(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelheartbeat
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveUserHeartbeatResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelheartbeat
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveUserHeartbeatResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveUserHeartbeatResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelheartbeat(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelheartbeat
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserHeartRecordExistReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelheartbeat
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserHeartRecordExistReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserHeartRecordExistReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelheartbeat
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelheartbeat
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelheartbeat
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelheartbeat
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelheartbeat(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelheartbeat
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserHeartRecordExistResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelheartbeat
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserHeartRecordExistResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserHeartRecordExistResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelheartbeat
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ExistUidList = append(m.ExistUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelheartbeat
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelheartbeat
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelheartbeat
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ExistUidList = append(m.ExistUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExistUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelheartbeat(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelheartbeat
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelheartbeat(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelheartbeat
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelheartbeat
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelheartbeat
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelheartbeat
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelheartbeat
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelheartbeat(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelheartbeat = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelheartbeat   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/channelheartbeat/channelheartbeat.proto", fileDescriptorChannelheartbeat)
}

var fileDescriptorChannelheartbeat = []byte{
	// 431 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xd2, 0x2e, 0x2e, 0x4a, 0xd6,
	0x4f, 0xce, 0x48, 0xcc, 0xcb, 0x4b, 0xcd, 0xc9, 0x48, 0x4d, 0x2c, 0x2a, 0x49, 0x4a, 0x4d, 0x2c,
	0xc1, 0x10, 0xd0, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x12, 0x40, 0x17, 0x97, 0x52, 0x49, 0xce,
	0xcf, 0xcd, 0xcd, 0xcf, 0xd3, 0x2f, 0xc9, 0x29, 0x2b, 0xc8, 0x4c, 0xce, 0xce, 0x49, 0xd5, 0x2f,
	0xce, 0x4e, 0x2a, 0xcd, 0xcc, 0x29, 0xc9, 0xcc, 0x2b, 0xa9, 0x2c, 0x48, 0x85, 0xe8, 0x53, 0x72,
	0xe1, 0x12, 0x0c, 0x2d, 0x4e, 0x2d, 0xf2, 0x80, 0x69, 0xf3, 0xcc, 0x4b, 0xcb, 0x17, 0x12, 0xe0,
	0x62, 0x2e, 0xcd, 0x4c, 0x91, 0x60, 0x54, 0x60, 0xd4, 0xe0, 0x0d, 0x02, 0x31, 0x85, 0x64, 0xb9,
	0xb8, 0xa0, 0x16, 0xc4, 0x67, 0xa6, 0x48, 0x30, 0x81, 0x25, 0x38, 0xa1, 0x22, 0x9e, 0x29, 0x4a,
	0x81, 0x5c, 0x62, 0xa1, 0x05, 0x29, 0x89, 0x25, 0xa9, 0x28, 0x66, 0x05, 0xa5, 0x16, 0x0a, 0x99,
	0x73, 0xb1, 0x64, 0xe6, 0xa5, 0xe5, 0x83, 0xcd, 0xe2, 0x36, 0x52, 0xd6, 0xc3, 0x70, 0x3e, 0x86,
	0xed, 0x41, 0x60, 0x0d, 0x4a, 0x92, 0x5c, 0xe2, 0x58, 0x8d, 0x2c, 0x2e, 0x00, 0xd9, 0x16, 0x94,
	0x9a, 0x9b, 0x5f, 0x46, 0x5d, 0xdb, 0xb0, 0x1a, 0x59, 0x5c, 0xa0, 0x64, 0xc9, 0x25, 0xe3, 0x9c,
	0x91, 0x9a, 0x9c, 0x0d, 0x97, 0x09, 0x4a, 0x4d, 0xce, 0x2f, 0x4a, 0x71, 0xad, 0xc8, 0x2c, 0x06,
	0xdb, 0x29, 0xc9, 0xc5, 0x51, 0x9a, 0x99, 0x12, 0x9f, 0x93, 0x59, 0x5c, 0x22, 0xc1, 0xa8, 0xc0,
	0xac, 0xc1, 0x1b, 0xc4, 0x5e, 0x9a, 0x99, 0xe2, 0x93, 0x59, 0x5c, 0xa2, 0xe4, 0xca, 0x25, 0x8b,
	0x47, 0x6b, 0x71, 0x81, 0x90, 0x0a, 0x17, 0x5f, 0x2a, 0x88, 0x13, 0x8f, 0x66, 0x02, 0x0f, 0x58,
	0x34, 0x14, 0x62, 0x8c, 0xd1, 0x35, 0x66, 0x2e, 0x01, 0x67, 0x88, 0x4f, 0xe0, 0x4e, 0x13, 0x9a,
	0xce, 0xc8, 0x25, 0x8c, 0x25, 0x80, 0x84, 0x34, 0xb0, 0x78, 0x1a, 0x6b, 0xd4, 0x48, 0x69, 0x12,
	0xa9, 0xb2, 0xb8, 0x40, 0x49, 0xbb, 0x61, 0xc9, 0x0b, 0x66, 0xc6, 0xae, 0x25, 0x2f, 0x98, 0x59,
	0x4a, 0xad, 0x2a, 0xac, 0x26, 0x2d, 0x79, 0xc1, 0x2c, 0xa1, 0x5b, 0xaa, 0x60, 0x53, 0x9a, 0x99,
	0x62, 0xa7, 0xa0, 0x5b, 0xa1, 0x60, 0x83, 0x48, 0x1c, 0x76, 0x42, 0xad, 0x8c, 0x5c, 0xc2, 0x58,
	0x02, 0x13, 0x9b, 0xcb, 0xb0, 0x47, 0x23, 0x36, 0x97, 0xe1, 0x8a, 0x1d, 0x49, 0x90, 0xcb, 0x98,
	0x40, 0x2e, 0x63, 0x2a, 0x05, 0xbb, 0x8b, 0x03, 0xe6, 0x2e, 0x50, 0x08, 0x49, 0xe2, 0x0c, 0x7e,
	0x21, 0x3d, 0x4c, 0x3b, 0xf0, 0x45, 0xb3, 0x94, 0x3e, 0x49, 0xea, 0x61, 0x2e, 0x63, 0xc6, 0xe6,
	0x32, 0x29, 0xb6, 0x8e, 0x25, 0x2f, 0x98, 0x9f, 0x97, 0x39, 0x09, 0x9c, 0x78, 0x24, 0xc7, 0x78,
	0xe1, 0x91, 0x1c, 0xe3, 0x83, 0x47, 0x72, 0x8c, 0x13, 0x1e, 0xcb, 0x31, 0x24, 0xb1, 0x81, 0x73,
	0xa5, 0x31, 0x20, 0x00, 0x00, 0xff, 0xff, 0x1e, 0xd5, 0x4c, 0xa6, 0xfc, 0x03, 0x00, 0x00,
}
