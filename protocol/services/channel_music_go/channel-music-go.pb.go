// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-music-go/channel-music-go.proto

package channel_music_go // import "golang.52tt.com/protocol/services/channel_music_go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelMusicStatusType int32

const (
	ChannelMusicStatusType_EMUSIC_STATUS_UNKNOWN         ChannelMusicStatusType = 0
	ChannelMusicStatusType_EMUSIC_STATUS_PLAY_MODE       ChannelMusicStatusType = 1
	ChannelMusicStatusType_EMUSIC_STATUS_VOLUME          ChannelMusicStatusType = 2
	ChannelMusicStatusType_EMUSIC_STATUS_CAN_SHARE       ChannelMusicStatusType = 3
	ChannelMusicStatusType_EMUSIC_STATUS_FREE_MODE       ChannelMusicStatusType = 4
	ChannelMusicStatusType_EMUSIC_STATUS_NEXT_MUSIC      ChannelMusicStatusType = 5
	ChannelMusicStatusType_EMUSIC_STATUS_PERCENT         ChannelMusicStatusType = 6
	ChannelMusicStatusType_EMUSIC_STATUS_PLAY_STATUS     ChannelMusicStatusType = 7
	ChannelMusicStatusType_EMUSIC_STATUS_CURRENT_PLAYING ChannelMusicStatusType = 8
	ChannelMusicStatusType_EMUSIC_STATUS_TIMEOUT_COUNT   ChannelMusicStatusType = 9
)

var ChannelMusicStatusType_name = map[int32]string{
	0: "EMUSIC_STATUS_UNKNOWN",
	1: "EMUSIC_STATUS_PLAY_MODE",
	2: "EMUSIC_STATUS_VOLUME",
	3: "EMUSIC_STATUS_CAN_SHARE",
	4: "EMUSIC_STATUS_FREE_MODE",
	5: "EMUSIC_STATUS_NEXT_MUSIC",
	6: "EMUSIC_STATUS_PERCENT",
	7: "EMUSIC_STATUS_PLAY_STATUS",
	8: "EMUSIC_STATUS_CURRENT_PLAYING",
	9: "EMUSIC_STATUS_TIMEOUT_COUNT",
}
var ChannelMusicStatusType_value = map[string]int32{
	"EMUSIC_STATUS_UNKNOWN":         0,
	"EMUSIC_STATUS_PLAY_MODE":       1,
	"EMUSIC_STATUS_VOLUME":          2,
	"EMUSIC_STATUS_CAN_SHARE":       3,
	"EMUSIC_STATUS_FREE_MODE":       4,
	"EMUSIC_STATUS_NEXT_MUSIC":      5,
	"EMUSIC_STATUS_PERCENT":         6,
	"EMUSIC_STATUS_PLAY_STATUS":     7,
	"EMUSIC_STATUS_CURRENT_PLAYING": 8,
	"EMUSIC_STATUS_TIMEOUT_COUNT":   9,
}

func (x ChannelMusicStatusType) String() string {
	return proto.EnumName(ChannelMusicStatusType_name, int32(x))
}
func (ChannelMusicStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{0}
}

type MusicChangedType int32

const (
	MusicChangedType_EMUSIC_CHANGED_UNKNOWN               MusicChangedType = 0
	MusicChangedType_EMUSIC_CHANGED_AUTO_NORMAL           MusicChangedType = 1
	MusicChangedType_EMUSIC_CHANGED_AUTO_NOT_PLAY_NETWORK MusicChangedType = 2
	MusicChangedType_EMUSIC_CAHNGED_AUTO_NOT_PLAY_FLIE    MusicChangedType = 3
	MusicChangedType_EMUSIC_CHANGED_MANUAL                MusicChangedType = 4
	MusicChangedType_EMUSIC_STOPED_DELETED                MusicChangedType = 5
	MusicChangedType_EMUSIC_STOPED                        MusicChangedType = 6
	MusicChangedType_EMUSIC_STOPED_LIST_EMPTY             MusicChangedType = 7
	MusicChangedType_EMUSIC_CHANGED_AUTO_NOT_HEARTBEAT    MusicChangedType = 8
)

var MusicChangedType_name = map[int32]string{
	0: "EMUSIC_CHANGED_UNKNOWN",
	1: "EMUSIC_CHANGED_AUTO_NORMAL",
	2: "EMUSIC_CHANGED_AUTO_NOT_PLAY_NETWORK",
	3: "EMUSIC_CAHNGED_AUTO_NOT_PLAY_FLIE",
	4: "EMUSIC_CHANGED_MANUAL",
	5: "EMUSIC_STOPED_DELETED",
	6: "EMUSIC_STOPED",
	7: "EMUSIC_STOPED_LIST_EMPTY",
	8: "EMUSIC_CHANGED_AUTO_NOT_HEARTBEAT",
}
var MusicChangedType_value = map[string]int32{
	"EMUSIC_CHANGED_UNKNOWN":               0,
	"EMUSIC_CHANGED_AUTO_NORMAL":           1,
	"EMUSIC_CHANGED_AUTO_NOT_PLAY_NETWORK": 2,
	"EMUSIC_CAHNGED_AUTO_NOT_PLAY_FLIE":    3,
	"EMUSIC_CHANGED_MANUAL":                4,
	"EMUSIC_STOPED_DELETED":                5,
	"EMUSIC_STOPED":                        6,
	"EMUSIC_STOPED_LIST_EMPTY":             7,
	"EMUSIC_CHANGED_AUTO_NOT_HEARTBEAT":    8,
}

func (x MusicChangedType) String() string {
	return proto.EnumName(MusicChangedType_name, int32(x))
}
func (MusicChangedType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{1}
}

// 战歌列表类型
type MusicListType int32

const (
	MusicListType_MusicListType_Default MusicListType = 0
	MusicListType_MusicListType_NC      MusicListType = 1
)

var MusicListType_name = map[int32]string{
	0: "MusicListType_Default",
	1: "MusicListType_NC",
}
var MusicListType_value = map[string]int32{
	"MusicListType_Default": 0,
	"MusicListType_NC":      1,
}

func (x MusicListType) String() string {
	return proto.EnumName(MusicListType_name, int32(x))
}
func (MusicListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{2}
}

type MusicInfo struct {
	ClientKey            string   `protobuf:"bytes,1,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Author               string   `protobuf:"bytes,3,opt,name=author,proto3" json:"author,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Volume               uint32   `protobuf:"varint,5,opt,name=volume,proto3" json:"volume,omitempty"`
	Key                  int64    `protobuf:"varint,6,opt,name=key,proto3" json:"key,omitempty"`
	IsLocal              uint32   `protobuf:"varint,8,opt,name=is_local,json=isLocal,proto3" json:"is_local,omitempty"`
	Status               uint32   `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	MusicType            uint32   `protobuf:"varint,10,opt,name=music_type,json=musicType,proto3" json:"music_type,omitempty"`
	StartTime            uint32   `protobuf:"varint,11,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicInfo) Reset()         { *m = MusicInfo{} }
func (m *MusicInfo) String() string { return proto.CompactTextString(m) }
func (*MusicInfo) ProtoMessage()    {}
func (*MusicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{0}
}
func (m *MusicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicInfo.Unmarshal(m, b)
}
func (m *MusicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicInfo.Marshal(b, m, deterministic)
}
func (dst *MusicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicInfo.Merge(dst, src)
}
func (m *MusicInfo) XXX_Size() int {
	return xxx_messageInfo_MusicInfo.Size(m)
}
func (m *MusicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusicInfo proto.InternalMessageInfo

func (m *MusicInfo) GetClientKey() string {
	if m != nil {
		return m.ClientKey
	}
	return ""
}

func (m *MusicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MusicInfo) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *MusicInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MusicInfo) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *MusicInfo) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *MusicInfo) GetIsLocal() uint32 {
	if m != nil {
		return m.IsLocal
	}
	return 0
}

func (m *MusicInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MusicInfo) GetMusicType() uint32 {
	if m != nil {
		return m.MusicType
	}
	return 0
}

func (m *MusicInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

// 播放器状态
type GetChannelMusicStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StatusTypeList       []uint32 `protobuf:"varint,3,rep,packed,name=status_type_list,json=statusTypeList,proto3" json:"status_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMusicStatusRequest) Reset()         { *m = GetChannelMusicStatusRequest{} }
func (m *GetChannelMusicStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelMusicStatusRequest) ProtoMessage()    {}
func (*GetChannelMusicStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{1}
}
func (m *GetChannelMusicStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMusicStatusRequest.Unmarshal(m, b)
}
func (m *GetChannelMusicStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMusicStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelMusicStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMusicStatusRequest.Merge(dst, src)
}
func (m *GetChannelMusicStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelMusicStatusRequest.Size(m)
}
func (m *GetChannelMusicStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMusicStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMusicStatusRequest proto.InternalMessageInfo

func (m *GetChannelMusicStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelMusicStatusRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelMusicStatusRequest) GetStatusTypeList() []uint32 {
	if m != nil {
		return m.StatusTypeList
	}
	return nil
}

type GetChannelMusicStatusResponse struct {
	PlayMode             uint32   `protobuf:"varint,1,opt,name=play_mode,json=playMode,proto3" json:"play_mode,omitempty"`
	Volume               uint32   `protobuf:"varint,2,opt,name=volume,proto3" json:"volume,omitempty"`
	CanShare             bool     `protobuf:"varint,3,opt,name=can_share,json=canShare,proto3" json:"can_share,omitempty"`
	IsPlaying            bool     `protobuf:"varint,4,opt,name=is_playing,json=isPlaying,proto3" json:"is_playing,omitempty"`
	IsFreeMode           bool     `protobuf:"varint,5,opt,name=is_free_mode,json=isFreeMode,proto3" json:"is_free_mode,omitempty"`
	CurrentPlaying       []byte   `protobuf:"bytes,6,opt,name=current_playing,json=currentPlaying,proto3" json:"current_playing,omitempty"`
	Percent              uint32   `protobuf:"varint,7,opt,name=percent,proto3" json:"percent,omitempty"`
	NextMusicKey         string   `protobuf:"bytes,8,opt,name=next_music_key,json=nextMusicKey,proto3" json:"next_music_key,omitempty"`
	TimeoutCount         uint32   `protobuf:"varint,9,opt,name=timeout_count,json=timeoutCount,proto3" json:"timeout_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMusicStatusResponse) Reset()         { *m = GetChannelMusicStatusResponse{} }
func (m *GetChannelMusicStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelMusicStatusResponse) ProtoMessage()    {}
func (*GetChannelMusicStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{2}
}
func (m *GetChannelMusicStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMusicStatusResponse.Unmarshal(m, b)
}
func (m *GetChannelMusicStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMusicStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelMusicStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMusicStatusResponse.Merge(dst, src)
}
func (m *GetChannelMusicStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelMusicStatusResponse.Size(m)
}
func (m *GetChannelMusicStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMusicStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMusicStatusResponse proto.InternalMessageInfo

func (m *GetChannelMusicStatusResponse) GetPlayMode() uint32 {
	if m != nil {
		return m.PlayMode
	}
	return 0
}

func (m *GetChannelMusicStatusResponse) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *GetChannelMusicStatusResponse) GetCanShare() bool {
	if m != nil {
		return m.CanShare
	}
	return false
}

func (m *GetChannelMusicStatusResponse) GetIsPlaying() bool {
	if m != nil {
		return m.IsPlaying
	}
	return false
}

func (m *GetChannelMusicStatusResponse) GetIsFreeMode() bool {
	if m != nil {
		return m.IsFreeMode
	}
	return false
}

func (m *GetChannelMusicStatusResponse) GetCurrentPlaying() []byte {
	if m != nil {
		return m.CurrentPlaying
	}
	return nil
}

func (m *GetChannelMusicStatusResponse) GetPercent() uint32 {
	if m != nil {
		return m.Percent
	}
	return 0
}

func (m *GetChannelMusicStatusResponse) GetNextMusicKey() string {
	if m != nil {
		return m.NextMusicKey
	}
	return ""
}

func (m *GetChannelMusicStatusResponse) GetTimeoutCount() uint32 {
	if m != nil {
		return m.TimeoutCount
	}
	return 0
}

type SetChannelMusicStatusRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ModifyTypeList       []uint32 `protobuf:"varint,3,rep,packed,name=modify_type_list,json=modifyTypeList,proto3" json:"modify_type_list,omitempty"`
	TimeoutCount         uint32   `protobuf:"varint,4,opt,name=timeout_count,json=timeoutCount,proto3" json:"timeout_count,omitempty"`
	Percent              uint32   `protobuf:"varint,5,opt,name=percent,proto3" json:"percent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMusicStatusRequest) Reset()         { *m = SetChannelMusicStatusRequest{} }
func (m *SetChannelMusicStatusRequest) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicStatusRequest) ProtoMessage()    {}
func (*SetChannelMusicStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{3}
}
func (m *SetChannelMusicStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMusicStatusRequest.Unmarshal(m, b)
}
func (m *SetChannelMusicStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMusicStatusRequest.Marshal(b, m, deterministic)
}
func (dst *SetChannelMusicStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMusicStatusRequest.Merge(dst, src)
}
func (m *SetChannelMusicStatusRequest) XXX_Size() int {
	return xxx_messageInfo_SetChannelMusicStatusRequest.Size(m)
}
func (m *SetChannelMusicStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMusicStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMusicStatusRequest proto.InternalMessageInfo

func (m *SetChannelMusicStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMusicStatusRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMusicStatusRequest) GetModifyTypeList() []uint32 {
	if m != nil {
		return m.ModifyTypeList
	}
	return nil
}

func (m *SetChannelMusicStatusRequest) GetTimeoutCount() uint32 {
	if m != nil {
		return m.TimeoutCount
	}
	return 0
}

func (m *SetChannelMusicStatusRequest) GetPercent() uint32 {
	if m != nil {
		return m.Percent
	}
	return 0
}

type SetChannelMusicStatusResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMusicStatusResponse) Reset()         { *m = SetChannelMusicStatusResponse{} }
func (m *SetChannelMusicStatusResponse) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicStatusResponse) ProtoMessage()    {}
func (*SetChannelMusicStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{4}
}
func (m *SetChannelMusicStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMusicStatusResponse.Unmarshal(m, b)
}
func (m *SetChannelMusicStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMusicStatusResponse.Marshal(b, m, deterministic)
}
func (dst *SetChannelMusicStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMusicStatusResponse.Merge(dst, src)
}
func (m *SetChannelMusicStatusResponse) XXX_Size() int {
	return xxx_messageInfo_SetChannelMusicStatusResponse.Size(m)
}
func (m *SetChannelMusicStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMusicStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMusicStatusResponse proto.InternalMessageInfo

// 控制
type ChannelMusicCtrlRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelCtrl          uint32   `protobuf:"varint,3,opt,name=channel_ctrl,json=channelCtrl,proto3" json:"channel_ctrl,omitempty"`
	MusicChangedType     uint32   `protobuf:"varint,4,opt,name=music_changed_type,json=musicChangedType,proto3" json:"music_changed_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMusicCtrlRequest) Reset()         { *m = ChannelMusicCtrlRequest{} }
func (m *ChannelMusicCtrlRequest) String() string { return proto.CompactTextString(m) }
func (*ChannelMusicCtrlRequest) ProtoMessage()    {}
func (*ChannelMusicCtrlRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{5}
}
func (m *ChannelMusicCtrlRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMusicCtrlRequest.Unmarshal(m, b)
}
func (m *ChannelMusicCtrlRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMusicCtrlRequest.Marshal(b, m, deterministic)
}
func (dst *ChannelMusicCtrlRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMusicCtrlRequest.Merge(dst, src)
}
func (m *ChannelMusicCtrlRequest) XXX_Size() int {
	return xxx_messageInfo_ChannelMusicCtrlRequest.Size(m)
}
func (m *ChannelMusicCtrlRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMusicCtrlRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMusicCtrlRequest proto.InternalMessageInfo

func (m *ChannelMusicCtrlRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMusicCtrlRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMusicCtrlRequest) GetChannelCtrl() uint32 {
	if m != nil {
		return m.ChannelCtrl
	}
	return 0
}

func (m *ChannelMusicCtrlRequest) GetMusicChangedType() uint32 {
	if m != nil {
		return m.MusicChangedType
	}
	return 0
}

type ChannelMusicCtrlResponse struct {
	NextMusic            *MusicInfo `protobuf:"bytes,1,opt,name=next_music,json=nextMusic,proto3" json:"next_music,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ChannelMusicCtrlResponse) Reset()         { *m = ChannelMusicCtrlResponse{} }
func (m *ChannelMusicCtrlResponse) String() string { return proto.CompactTextString(m) }
func (*ChannelMusicCtrlResponse) ProtoMessage()    {}
func (*ChannelMusicCtrlResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{6}
}
func (m *ChannelMusicCtrlResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMusicCtrlResponse.Unmarshal(m, b)
}
func (m *ChannelMusicCtrlResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMusicCtrlResponse.Marshal(b, m, deterministic)
}
func (dst *ChannelMusicCtrlResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMusicCtrlResponse.Merge(dst, src)
}
func (m *ChannelMusicCtrlResponse) XXX_Size() int {
	return xxx_messageInfo_ChannelMusicCtrlResponse.Size(m)
}
func (m *ChannelMusicCtrlResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMusicCtrlResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMusicCtrlResponse proto.InternalMessageInfo

func (m *ChannelMusicCtrlResponse) GetNextMusic() *MusicInfo {
	if m != nil {
		return m.NextMusic
	}
	return nil
}

type SetChannelMusicCurrentRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicId              int64    `protobuf:"varint,3,opt,name=music_id,json=musicId,proto3" json:"music_id,omitempty"`
	MusicChangedType     uint32   `protobuf:"varint,4,opt,name=music_changed_type,json=musicChangedType,proto3" json:"music_changed_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMusicCurrentRequest) Reset()         { *m = SetChannelMusicCurrentRequest{} }
func (m *SetChannelMusicCurrentRequest) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicCurrentRequest) ProtoMessage()    {}
func (*SetChannelMusicCurrentRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{7}
}
func (m *SetChannelMusicCurrentRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMusicCurrentRequest.Unmarshal(m, b)
}
func (m *SetChannelMusicCurrentRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMusicCurrentRequest.Marshal(b, m, deterministic)
}
func (dst *SetChannelMusicCurrentRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMusicCurrentRequest.Merge(dst, src)
}
func (m *SetChannelMusicCurrentRequest) XXX_Size() int {
	return xxx_messageInfo_SetChannelMusicCurrentRequest.Size(m)
}
func (m *SetChannelMusicCurrentRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMusicCurrentRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMusicCurrentRequest proto.InternalMessageInfo

func (m *SetChannelMusicCurrentRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMusicCurrentRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMusicCurrentRequest) GetMusicId() int64 {
	if m != nil {
		return m.MusicId
	}
	return 0
}

func (m *SetChannelMusicCurrentRequest) GetMusicChangedType() uint32 {
	if m != nil {
		return m.MusicChangedType
	}
	return 0
}

type SetChannelMusicCurrentResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMusicCurrentResponse) Reset()         { *m = SetChannelMusicCurrentResponse{} }
func (m *SetChannelMusicCurrentResponse) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicCurrentResponse) ProtoMessage()    {}
func (*SetChannelMusicCurrentResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{8}
}
func (m *SetChannelMusicCurrentResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMusicCurrentResponse.Unmarshal(m, b)
}
func (m *SetChannelMusicCurrentResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMusicCurrentResponse.Marshal(b, m, deterministic)
}
func (dst *SetChannelMusicCurrentResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMusicCurrentResponse.Merge(dst, src)
}
func (m *SetChannelMusicCurrentResponse) XXX_Size() int {
	return xxx_messageInfo_SetChannelMusicCurrentResponse.Size(m)
}
func (m *SetChannelMusicCurrentResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMusicCurrentResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMusicCurrentResponse proto.InternalMessageInfo

type AddChannelMusicRequest struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicInfoList        []*MusicInfo `protobuf:"bytes,3,rep,name=music_info_list,json=musicInfoList,proto3" json:"music_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddChannelMusicRequest) Reset()         { *m = AddChannelMusicRequest{} }
func (m *AddChannelMusicRequest) String() string { return proto.CompactTextString(m) }
func (*AddChannelMusicRequest) ProtoMessage()    {}
func (*AddChannelMusicRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{9}
}
func (m *AddChannelMusicRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelMusicRequest.Unmarshal(m, b)
}
func (m *AddChannelMusicRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelMusicRequest.Marshal(b, m, deterministic)
}
func (dst *AddChannelMusicRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelMusicRequest.Merge(dst, src)
}
func (m *AddChannelMusicRequest) XXX_Size() int {
	return xxx_messageInfo_AddChannelMusicRequest.Size(m)
}
func (m *AddChannelMusicRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelMusicRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelMusicRequest proto.InternalMessageInfo

func (m *AddChannelMusicRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddChannelMusicRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddChannelMusicRequest) GetMusicInfoList() []*MusicInfo {
	if m != nil {
		return m.MusicInfoList
	}
	return nil
}

type AddChannelMusicResponse struct {
	MusicAddedList       []*MusicInfo `protobuf:"bytes,1,rep,name=music_added_list,json=musicAddedList,proto3" json:"music_added_list,omitempty"`
	MaxMusicCount        uint32       `protobuf:"varint,2,opt,name=max_music_count,json=maxMusicCount,proto3" json:"max_music_count,omitempty"`
	CurrentMusicCount    uint32       `protobuf:"varint,3,opt,name=current_music_count,json=currentMusicCount,proto3" json:"current_music_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddChannelMusicResponse) Reset()         { *m = AddChannelMusicResponse{} }
func (m *AddChannelMusicResponse) String() string { return proto.CompactTextString(m) }
func (*AddChannelMusicResponse) ProtoMessage()    {}
func (*AddChannelMusicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{10}
}
func (m *AddChannelMusicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelMusicResponse.Unmarshal(m, b)
}
func (m *AddChannelMusicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelMusicResponse.Marshal(b, m, deterministic)
}
func (dst *AddChannelMusicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelMusicResponse.Merge(dst, src)
}
func (m *AddChannelMusicResponse) XXX_Size() int {
	return xxx_messageInfo_AddChannelMusicResponse.Size(m)
}
func (m *AddChannelMusicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelMusicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelMusicResponse proto.InternalMessageInfo

func (m *AddChannelMusicResponse) GetMusicAddedList() []*MusicInfo {
	if m != nil {
		return m.MusicAddedList
	}
	return nil
}

func (m *AddChannelMusicResponse) GetMaxMusicCount() uint32 {
	if m != nil {
		return m.MaxMusicCount
	}
	return 0
}

func (m *AddChannelMusicResponse) GetCurrentMusicCount() uint32 {
	if m != nil {
		return m.CurrentMusicCount
	}
	return 0
}

type GetChannelMusicListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMusicListRequest) Reset()         { *m = GetChannelMusicListRequest{} }
func (m *GetChannelMusicListRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelMusicListRequest) ProtoMessage()    {}
func (*GetChannelMusicListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{11}
}
func (m *GetChannelMusicListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMusicListRequest.Unmarshal(m, b)
}
func (m *GetChannelMusicListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMusicListRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelMusicListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMusicListRequest.Merge(dst, src)
}
func (m *GetChannelMusicListRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelMusicListRequest.Size(m)
}
func (m *GetChannelMusicListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMusicListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMusicListRequest proto.InternalMessageInfo

func (m *GetChannelMusicListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelMusicListRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMusicListResponse struct {
	MusicList            []*MusicInfo `protobuf:"bytes,1,rep,name=music_list,json=musicList,proto3" json:"music_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelMusicListResponse) Reset()         { *m = GetChannelMusicListResponse{} }
func (m *GetChannelMusicListResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelMusicListResponse) ProtoMessage()    {}
func (*GetChannelMusicListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{12}
}
func (m *GetChannelMusicListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMusicListResponse.Unmarshal(m, b)
}
func (m *GetChannelMusicListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMusicListResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelMusicListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMusicListResponse.Merge(dst, src)
}
func (m *GetChannelMusicListResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelMusicListResponse.Size(m)
}
func (m *GetChannelMusicListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMusicListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMusicListResponse proto.InternalMessageInfo

func (m *GetChannelMusicListResponse) GetMusicList() []*MusicInfo {
	if m != nil {
		return m.MusicList
	}
	return nil
}

type SetChannelMusicListTypeRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MusicListType        uint32   `protobuf:"varint,3,opt,name=music_list_type,json=musicListType,proto3" json:"music_list_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMusicListTypeRequest) Reset()         { *m = SetChannelMusicListTypeRequest{} }
func (m *SetChannelMusicListTypeRequest) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicListTypeRequest) ProtoMessage()    {}
func (*SetChannelMusicListTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{13}
}
func (m *SetChannelMusicListTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMusicListTypeRequest.Unmarshal(m, b)
}
func (m *SetChannelMusicListTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMusicListTypeRequest.Marshal(b, m, deterministic)
}
func (dst *SetChannelMusicListTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMusicListTypeRequest.Merge(dst, src)
}
func (m *SetChannelMusicListTypeRequest) XXX_Size() int {
	return xxx_messageInfo_SetChannelMusicListTypeRequest.Size(m)
}
func (m *SetChannelMusicListTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMusicListTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMusicListTypeRequest proto.InternalMessageInfo

func (m *SetChannelMusicListTypeRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMusicListTypeRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMusicListTypeRequest) GetMusicListType() uint32 {
	if m != nil {
		return m.MusicListType
	}
	return 0
}

type SetChannelMusicListTypeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelMusicListTypeResponse) Reset()         { *m = SetChannelMusicListTypeResponse{} }
func (m *SetChannelMusicListTypeResponse) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicListTypeResponse) ProtoMessage()    {}
func (*SetChannelMusicListTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{14}
}
func (m *SetChannelMusicListTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMusicListTypeResponse.Unmarshal(m, b)
}
func (m *SetChannelMusicListTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMusicListTypeResponse.Marshal(b, m, deterministic)
}
func (dst *SetChannelMusicListTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMusicListTypeResponse.Merge(dst, src)
}
func (m *SetChannelMusicListTypeResponse) XXX_Size() int {
	return xxx_messageInfo_SetChannelMusicListTypeResponse.Size(m)
}
func (m *SetChannelMusicListTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMusicListTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMusicListTypeResponse proto.InternalMessageInfo

type GetChannelMusicListTypeRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMusicListTypeRequest) Reset()         { *m = GetChannelMusicListTypeRequest{} }
func (m *GetChannelMusicListTypeRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelMusicListTypeRequest) ProtoMessage()    {}
func (*GetChannelMusicListTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{15}
}
func (m *GetChannelMusicListTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMusicListTypeRequest.Unmarshal(m, b)
}
func (m *GetChannelMusicListTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMusicListTypeRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelMusicListTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMusicListTypeRequest.Merge(dst, src)
}
func (m *GetChannelMusicListTypeRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelMusicListTypeRequest.Size(m)
}
func (m *GetChannelMusicListTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMusicListTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMusicListTypeRequest proto.InternalMessageInfo

func (m *GetChannelMusicListTypeRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelMusicListTypeRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMusicListTypeResponse struct {
	MusicListType        uint32   `protobuf:"varint,1,opt,name=music_list_type,json=musicListType,proto3" json:"music_list_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMusicListTypeResponse) Reset()         { *m = GetChannelMusicListTypeResponse{} }
func (m *GetChannelMusicListTypeResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelMusicListTypeResponse) ProtoMessage()    {}
func (*GetChannelMusicListTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{16}
}
func (m *GetChannelMusicListTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMusicListTypeResponse.Unmarshal(m, b)
}
func (m *GetChannelMusicListTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMusicListTypeResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelMusicListTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMusicListTypeResponse.Merge(dst, src)
}
func (m *GetChannelMusicListTypeResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelMusicListTypeResponse.Size(m)
}
func (m *GetChannelMusicListTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMusicListTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMusicListTypeResponse proto.InternalMessageInfo

func (m *GetChannelMusicListTypeResponse) GetMusicListType() uint32 {
	if m != nil {
		return m.MusicListType
	}
	return 0
}

type ClearChannelMusicListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearChannelMusicListRequest) Reset()         { *m = ClearChannelMusicListRequest{} }
func (m *ClearChannelMusicListRequest) String() string { return proto.CompactTextString(m) }
func (*ClearChannelMusicListRequest) ProtoMessage()    {}
func (*ClearChannelMusicListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{17}
}
func (m *ClearChannelMusicListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearChannelMusicListRequest.Unmarshal(m, b)
}
func (m *ClearChannelMusicListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearChannelMusicListRequest.Marshal(b, m, deterministic)
}
func (dst *ClearChannelMusicListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearChannelMusicListRequest.Merge(dst, src)
}
func (m *ClearChannelMusicListRequest) XXX_Size() int {
	return xxx_messageInfo_ClearChannelMusicListRequest.Size(m)
}
func (m *ClearChannelMusicListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearChannelMusicListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ClearChannelMusicListRequest proto.InternalMessageInfo

func (m *ClearChannelMusicListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ClearChannelMusicListRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ClearChannelMusicListResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearChannelMusicListResponse) Reset()         { *m = ClearChannelMusicListResponse{} }
func (m *ClearChannelMusicListResponse) String() string { return proto.CompactTextString(m) }
func (*ClearChannelMusicListResponse) ProtoMessage()    {}
func (*ClearChannelMusicListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_music_go_e46938c103a7947e, []int{18}
}
func (m *ClearChannelMusicListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearChannelMusicListResponse.Unmarshal(m, b)
}
func (m *ClearChannelMusicListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearChannelMusicListResponse.Marshal(b, m, deterministic)
}
func (dst *ClearChannelMusicListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearChannelMusicListResponse.Merge(dst, src)
}
func (m *ClearChannelMusicListResponse) XXX_Size() int {
	return xxx_messageInfo_ClearChannelMusicListResponse.Size(m)
}
func (m *ClearChannelMusicListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearChannelMusicListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ClearChannelMusicListResponse proto.InternalMessageInfo

func init() {
	proto.RegisterType((*MusicInfo)(nil), "channel_music_go.MusicInfo")
	proto.RegisterType((*GetChannelMusicStatusRequest)(nil), "channel_music_go.GetChannelMusicStatusRequest")
	proto.RegisterType((*GetChannelMusicStatusResponse)(nil), "channel_music_go.GetChannelMusicStatusResponse")
	proto.RegisterType((*SetChannelMusicStatusRequest)(nil), "channel_music_go.SetChannelMusicStatusRequest")
	proto.RegisterType((*SetChannelMusicStatusResponse)(nil), "channel_music_go.SetChannelMusicStatusResponse")
	proto.RegisterType((*ChannelMusicCtrlRequest)(nil), "channel_music_go.ChannelMusicCtrlRequest")
	proto.RegisterType((*ChannelMusicCtrlResponse)(nil), "channel_music_go.ChannelMusicCtrlResponse")
	proto.RegisterType((*SetChannelMusicCurrentRequest)(nil), "channel_music_go.SetChannelMusicCurrentRequest")
	proto.RegisterType((*SetChannelMusicCurrentResponse)(nil), "channel_music_go.SetChannelMusicCurrentResponse")
	proto.RegisterType((*AddChannelMusicRequest)(nil), "channel_music_go.AddChannelMusicRequest")
	proto.RegisterType((*AddChannelMusicResponse)(nil), "channel_music_go.AddChannelMusicResponse")
	proto.RegisterType((*GetChannelMusicListRequest)(nil), "channel_music_go.GetChannelMusicListRequest")
	proto.RegisterType((*GetChannelMusicListResponse)(nil), "channel_music_go.GetChannelMusicListResponse")
	proto.RegisterType((*SetChannelMusicListTypeRequest)(nil), "channel_music_go.SetChannelMusicListTypeRequest")
	proto.RegisterType((*SetChannelMusicListTypeResponse)(nil), "channel_music_go.SetChannelMusicListTypeResponse")
	proto.RegisterType((*GetChannelMusicListTypeRequest)(nil), "channel_music_go.GetChannelMusicListTypeRequest")
	proto.RegisterType((*GetChannelMusicListTypeResponse)(nil), "channel_music_go.GetChannelMusicListTypeResponse")
	proto.RegisterType((*ClearChannelMusicListRequest)(nil), "channel_music_go.ClearChannelMusicListRequest")
	proto.RegisterType((*ClearChannelMusicListResponse)(nil), "channel_music_go.ClearChannelMusicListResponse")
	proto.RegisterEnum("channel_music_go.ChannelMusicStatusType", ChannelMusicStatusType_name, ChannelMusicStatusType_value)
	proto.RegisterEnum("channel_music_go.MusicChangedType", MusicChangedType_name, MusicChangedType_value)
	proto.RegisterEnum("channel_music_go.MusicListType", MusicListType_name, MusicListType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelMusicGoClient is the client API for ChannelMusicGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelMusicGoClient interface {
	GetChannelMusicStatus(ctx context.Context, in *GetChannelMusicStatusRequest, opts ...grpc.CallOption) (*GetChannelMusicStatusResponse, error)
	SetChannelMusicStatus(ctx context.Context, in *SetChannelMusicStatusRequest, opts ...grpc.CallOption) (*SetChannelMusicStatusResponse, error)
	ChannelMusicCtrl(ctx context.Context, in *ChannelMusicCtrlRequest, opts ...grpc.CallOption) (*ChannelMusicCtrlResponse, error)
	SetChannelMusicCurrent(ctx context.Context, in *SetChannelMusicCurrentRequest, opts ...grpc.CallOption) (*SetChannelMusicCurrentResponse, error)
	AddChannelMusic(ctx context.Context, in *AddChannelMusicRequest, opts ...grpc.CallOption) (*AddChannelMusicResponse, error)
	GetChannelMusicList(ctx context.Context, in *GetChannelMusicListRequest, opts ...grpc.CallOption) (*GetChannelMusicListResponse, error)
	SetChannelMusicListType(ctx context.Context, in *SetChannelMusicListTypeRequest, opts ...grpc.CallOption) (*SetChannelMusicListTypeResponse, error)
	GetChannelMusicListType(ctx context.Context, in *GetChannelMusicListTypeRequest, opts ...grpc.CallOption) (*GetChannelMusicListTypeResponse, error)
	ClearChannelMusicList(ctx context.Context, in *ClearChannelMusicListRequest, opts ...grpc.CallOption) (*ClearChannelMusicListResponse, error)
}

type channelMusicGoClient struct {
	cc *grpc.ClientConn
}

func NewChannelMusicGoClient(cc *grpc.ClientConn) ChannelMusicGoClient {
	return &channelMusicGoClient{cc}
}

func (c *channelMusicGoClient) GetChannelMusicStatus(ctx context.Context, in *GetChannelMusicStatusRequest, opts ...grpc.CallOption) (*GetChannelMusicStatusResponse, error) {
	out := new(GetChannelMusicStatusResponse)
	err := c.cc.Invoke(ctx, "/channel_music_go.ChannelMusicGo/GetChannelMusicStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicGoClient) SetChannelMusicStatus(ctx context.Context, in *SetChannelMusicStatusRequest, opts ...grpc.CallOption) (*SetChannelMusicStatusResponse, error) {
	out := new(SetChannelMusicStatusResponse)
	err := c.cc.Invoke(ctx, "/channel_music_go.ChannelMusicGo/SetChannelMusicStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicGoClient) ChannelMusicCtrl(ctx context.Context, in *ChannelMusicCtrlRequest, opts ...grpc.CallOption) (*ChannelMusicCtrlResponse, error) {
	out := new(ChannelMusicCtrlResponse)
	err := c.cc.Invoke(ctx, "/channel_music_go.ChannelMusicGo/ChannelMusicCtrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicGoClient) SetChannelMusicCurrent(ctx context.Context, in *SetChannelMusicCurrentRequest, opts ...grpc.CallOption) (*SetChannelMusicCurrentResponse, error) {
	out := new(SetChannelMusicCurrentResponse)
	err := c.cc.Invoke(ctx, "/channel_music_go.ChannelMusicGo/SetChannelMusicCurrent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicGoClient) AddChannelMusic(ctx context.Context, in *AddChannelMusicRequest, opts ...grpc.CallOption) (*AddChannelMusicResponse, error) {
	out := new(AddChannelMusicResponse)
	err := c.cc.Invoke(ctx, "/channel_music_go.ChannelMusicGo/AddChannelMusic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicGoClient) GetChannelMusicList(ctx context.Context, in *GetChannelMusicListRequest, opts ...grpc.CallOption) (*GetChannelMusicListResponse, error) {
	out := new(GetChannelMusicListResponse)
	err := c.cc.Invoke(ctx, "/channel_music_go.ChannelMusicGo/GetChannelMusicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicGoClient) SetChannelMusicListType(ctx context.Context, in *SetChannelMusicListTypeRequest, opts ...grpc.CallOption) (*SetChannelMusicListTypeResponse, error) {
	out := new(SetChannelMusicListTypeResponse)
	err := c.cc.Invoke(ctx, "/channel_music_go.ChannelMusicGo/SetChannelMusicListType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicGoClient) GetChannelMusicListType(ctx context.Context, in *GetChannelMusicListTypeRequest, opts ...grpc.CallOption) (*GetChannelMusicListTypeResponse, error) {
	out := new(GetChannelMusicListTypeResponse)
	err := c.cc.Invoke(ctx, "/channel_music_go.ChannelMusicGo/GetChannelMusicListType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicGoClient) ClearChannelMusicList(ctx context.Context, in *ClearChannelMusicListRequest, opts ...grpc.CallOption) (*ClearChannelMusicListResponse, error) {
	out := new(ClearChannelMusicListResponse)
	err := c.cc.Invoke(ctx, "/channel_music_go.ChannelMusicGo/ClearChannelMusicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelMusicGoServer is the server API for ChannelMusicGo service.
type ChannelMusicGoServer interface {
	GetChannelMusicStatus(context.Context, *GetChannelMusicStatusRequest) (*GetChannelMusicStatusResponse, error)
	SetChannelMusicStatus(context.Context, *SetChannelMusicStatusRequest) (*SetChannelMusicStatusResponse, error)
	ChannelMusicCtrl(context.Context, *ChannelMusicCtrlRequest) (*ChannelMusicCtrlResponse, error)
	SetChannelMusicCurrent(context.Context, *SetChannelMusicCurrentRequest) (*SetChannelMusicCurrentResponse, error)
	AddChannelMusic(context.Context, *AddChannelMusicRequest) (*AddChannelMusicResponse, error)
	GetChannelMusicList(context.Context, *GetChannelMusicListRequest) (*GetChannelMusicListResponse, error)
	SetChannelMusicListType(context.Context, *SetChannelMusicListTypeRequest) (*SetChannelMusicListTypeResponse, error)
	GetChannelMusicListType(context.Context, *GetChannelMusicListTypeRequest) (*GetChannelMusicListTypeResponse, error)
	ClearChannelMusicList(context.Context, *ClearChannelMusicListRequest) (*ClearChannelMusicListResponse, error)
}

func RegisterChannelMusicGoServer(s *grpc.Server, srv ChannelMusicGoServer) {
	s.RegisterService(&_ChannelMusicGo_serviceDesc, srv)
}

func _ChannelMusicGo_GetChannelMusicStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMusicStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicGoServer).GetChannelMusicStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_music_go.ChannelMusicGo/GetChannelMusicStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicGoServer).GetChannelMusicStatus(ctx, req.(*GetChannelMusicStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusicGo_SetChannelMusicStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMusicStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicGoServer).SetChannelMusicStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_music_go.ChannelMusicGo/SetChannelMusicStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicGoServer).SetChannelMusicStatus(ctx, req.(*SetChannelMusicStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusicGo_ChannelMusicCtrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelMusicCtrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicGoServer).ChannelMusicCtrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_music_go.ChannelMusicGo/ChannelMusicCtrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicGoServer).ChannelMusicCtrl(ctx, req.(*ChannelMusicCtrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusicGo_SetChannelMusicCurrent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMusicCurrentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicGoServer).SetChannelMusicCurrent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_music_go.ChannelMusicGo/SetChannelMusicCurrent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicGoServer).SetChannelMusicCurrent(ctx, req.(*SetChannelMusicCurrentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusicGo_AddChannelMusic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelMusicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicGoServer).AddChannelMusic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_music_go.ChannelMusicGo/AddChannelMusic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicGoServer).AddChannelMusic(ctx, req.(*AddChannelMusicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusicGo_GetChannelMusicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMusicListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicGoServer).GetChannelMusicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_music_go.ChannelMusicGo/GetChannelMusicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicGoServer).GetChannelMusicList(ctx, req.(*GetChannelMusicListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusicGo_SetChannelMusicListType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMusicListTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicGoServer).SetChannelMusicListType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_music_go.ChannelMusicGo/SetChannelMusicListType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicGoServer).SetChannelMusicListType(ctx, req.(*SetChannelMusicListTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusicGo_GetChannelMusicListType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMusicListTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicGoServer).GetChannelMusicListType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_music_go.ChannelMusicGo/GetChannelMusicListType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicGoServer).GetChannelMusicListType(ctx, req.(*GetChannelMusicListTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusicGo_ClearChannelMusicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearChannelMusicListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicGoServer).ClearChannelMusicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_music_go.ChannelMusicGo/ClearChannelMusicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicGoServer).ClearChannelMusicList(ctx, req.(*ClearChannelMusicListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelMusicGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_music_go.ChannelMusicGo",
	HandlerType: (*ChannelMusicGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelMusicStatus",
			Handler:    _ChannelMusicGo_GetChannelMusicStatus_Handler,
		},
		{
			MethodName: "SetChannelMusicStatus",
			Handler:    _ChannelMusicGo_SetChannelMusicStatus_Handler,
		},
		{
			MethodName: "ChannelMusicCtrl",
			Handler:    _ChannelMusicGo_ChannelMusicCtrl_Handler,
		},
		{
			MethodName: "SetChannelMusicCurrent",
			Handler:    _ChannelMusicGo_SetChannelMusicCurrent_Handler,
		},
		{
			MethodName: "AddChannelMusic",
			Handler:    _ChannelMusicGo_AddChannelMusic_Handler,
		},
		{
			MethodName: "GetChannelMusicList",
			Handler:    _ChannelMusicGo_GetChannelMusicList_Handler,
		},
		{
			MethodName: "SetChannelMusicListType",
			Handler:    _ChannelMusicGo_SetChannelMusicListType_Handler,
		},
		{
			MethodName: "GetChannelMusicListType",
			Handler:    _ChannelMusicGo_GetChannelMusicListType_Handler,
		},
		{
			MethodName: "ClearChannelMusicList",
			Handler:    _ChannelMusicGo_ClearChannelMusicList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-music-go/channel-music-go.proto",
}

func init() {
	proto.RegisterFile("channel-music-go/channel-music-go.proto", fileDescriptor_channel_music_go_e46938c103a7947e)
}

var fileDescriptor_channel_music_go_e46938c103a7947e = []byte{
	// 1307 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0x4b, 0x53, 0xdb, 0x56,
	0x14, 0x8e, 0x6c, 0x1e, 0xf6, 0x09, 0x86, 0x9b, 0x9b, 0x00, 0xc2, 0x40, 0x00, 0x37, 0x4d, 0x1c,
	0x26, 0x31, 0x2d, 0x6d, 0x37, 0x5d, 0x55, 0x11, 0x8a, 0xf1, 0xc4, 0x96, 0xa9, 0x2c, 0x27, 0x4d,
	0x37, 0x1a, 0x55, 0xba, 0x10, 0x4d, 0x65, 0x89, 0x4a, 0x72, 0x06, 0x4f, 0x67, 0xba, 0xe9, 0xbe,
	0xfb, 0x4e, 0x7f, 0x43, 0xf7, 0x59, 0x66, 0xfa, 0xcb, 0x3a, 0xf7, 0xe1, 0xf8, 0x21, 0x01, 0x86,
	0xc9, 0xce, 0xf7, 0x3b, 0x47, 0xe7, 0x7c, 0xe7, 0x3b, 0xe7, 0x3e, 0xc6, 0xf0, 0xc4, 0x79, 0x67,
	0x07, 0x01, 0xf1, 0x9f, 0xf7, 0xfa, 0xb1, 0xe7, 0x3c, 0x3f, 0x0b, 0x0f, 0xa6, 0x81, 0xda, 0x79,
	0x14, 0x26, 0x21, 0x46, 0x02, 0xb7, 0x18, 0x6e, 0x9d, 0x85, 0x95, 0x3f, 0x73, 0x50, 0x6c, 0xd1,
	0x45, 0x23, 0x38, 0x0d, 0xf1, 0x36, 0x80, 0xe3, 0x7b, 0x24, 0x48, 0xac, 0x5f, 0xc9, 0x40, 0x96,
	0x76, 0xa5, 0x6a, 0xd1, 0x28, 0x72, 0xe4, 0x15, 0x19, 0x60, 0x0c, 0x73, 0x81, 0xdd, 0x23, 0x72,
	0x8e, 0x19, 0xd8, 0x6f, 0xbc, 0x06, 0x0b, 0x76, 0x3f, 0x79, 0x17, 0x46, 0x72, 0x9e, 0xa1, 0x62,
	0x85, 0x11, 0xe4, 0xfb, 0x9e, 0x2b, 0xcf, 0xed, 0x4a, 0xd5, 0x92, 0x41, 0x7f, 0x52, 0xcf, 0xf7,
	0xa1, 0xdf, 0xef, 0x11, 0x79, 0x9e, 0x81, 0x62, 0x45, 0x3d, 0x69, 0xb6, 0x85, 0x5d, 0xa9, 0x9a,
	0x37, 0xe8, 0x4f, 0xbc, 0x01, 0x05, 0x2f, 0xb6, 0xfc, 0xd0, 0xb1, 0x7d, 0xb9, 0xc0, 0x7c, 0x17,
	0xbd, 0xb8, 0x49, 0x97, 0x34, 0x48, 0x9c, 0xd8, 0x49, 0x3f, 0x96, 0x8b, 0x3c, 0x08, 0x5f, 0x51,
	0xe6, 0xbc, 0xa6, 0x64, 0x70, 0x4e, 0x64, 0x60, 0xb6, 0x22, 0x43, 0xcc, 0xc1, 0x39, 0xa1, 0xe6,
	0x38, 0xb1, 0xa3, 0xc4, 0x4a, 0xbc, 0x1e, 0x91, 0xef, 0x72, 0x33, 0x43, 0x4c, 0xaf, 0x47, 0x2a,
	0x03, 0xd8, 0xaa, 0x93, 0x44, 0xe5, 0xe2, 0x30, 0x39, 0x3a, 0x2c, 0xac, 0x41, 0x7e, 0xeb, 0x93,
	0x38, 0x19, 0x16, 0x23, 0x8d, 0x8a, 0xa1, 0x4a, 0x09, 0x2d, 0x3d, 0x97, 0x09, 0x52, 0x32, 0x8a,
	0x02, 0x69, 0xb8, 0xb8, 0x0a, 0x88, 0x13, 0x63, 0x7c, 0x2c, 0xdf, 0x8b, 0x13, 0x39, 0xbf, 0x9b,
	0xaf, 0x96, 0x8c, 0x65, 0x8e, 0x53, 0x56, 0x4d, 0x2f, 0x4e, 0x2a, 0x1f, 0x73, 0xb0, 0x7d, 0x49,
	0xee, 0xf8, 0x3c, 0x0c, 0x62, 0x82, 0x37, 0xa1, 0x78, 0xee, 0xdb, 0x03, 0xab, 0x17, 0xba, 0x44,
	0x50, 0x28, 0x50, 0xa0, 0x15, 0xba, 0x64, 0x4c, 0xd4, 0xdc, 0x84, 0xa8, 0x9b, 0x50, 0x74, 0xec,
	0xc0, 0x8a, 0xdf, 0xd9, 0x11, 0x61, 0x9d, 0x29, 0x18, 0x05, 0xc7, 0x0e, 0x3a, 0x74, 0x4d, 0xc9,
	0x7b, 0xb1, 0x45, 0x63, 0x78, 0xc1, 0x19, 0x6b, 0x51, 0xc1, 0x28, 0x7a, 0xf1, 0x09, 0x07, 0xf0,
	0x2e, 0x2c, 0x79, 0xb1, 0x75, 0x1a, 0x11, 0xc2, 0x73, 0xce, 0x33, 0x07, 0xf0, 0xe2, 0x97, 0x11,
	0x21, 0x2c, 0xeb, 0x13, 0x58, 0x71, 0xfa, 0x51, 0x44, 0x07, 0x65, 0x18, 0x85, 0xb6, 0x6f, 0xc9,
	0x58, 0x16, 0xf0, 0x30, 0x94, 0x0c, 0x8b, 0xe7, 0x24, 0x72, 0x48, 0x90, 0xc8, 0x8b, 0xbc, 0x91,
	0x62, 0x89, 0x1f, 0xc1, 0x72, 0x40, 0x2e, 0x12, 0x31, 0x89, 0x74, 0x00, 0x0a, 0x6c, 0x7e, 0x96,
	0x28, 0xca, 0x64, 0xa0, 0x13, 0xf7, 0x05, 0x94, 0x68, 0xc7, 0xc2, 0x7e, 0x62, 0x39, 0x61, 0x3f,
	0x48, 0x44, 0xd7, 0x97, 0x04, 0xa8, 0x52, 0xac, 0xf2, 0x41, 0x82, 0xad, 0xce, 0xe7, 0x6e, 0x5f,
	0x2f, 0x74, 0xbd, 0xd3, 0x41, 0xba, 0x7d, 0x1c, 0x1f, 0xb6, 0x2f, 0x4d, 0x70, 0x2e, 0x4d, 0x70,
	0x5c, 0x85, 0xf9, 0x09, 0x15, 0x2a, 0x3b, 0xb0, 0xdd, 0xb9, 0xaa, 0xf9, 0x95, 0x7f, 0x24, 0x58,
	0x1f, 0x37, 0xab, 0x49, 0xe4, 0xdf, 0xba, 0xac, 0x3d, 0x58, 0x1a, 0x9a, 0x9d, 0x24, 0xf2, 0xd9,
	0x5c, 0x94, 0x8c, 0xbb, 0x02, 0xa3, 0xa1, 0xf1, 0x33, 0xc0, 0xbc, 0x23, 0x14, 0x3c, 0x23, 0x2e,
	0xdf, 0x4f, 0xbc, 0x28, 0xc4, 0x2c, 0x2a, 0x37, 0x50, 0x05, 0x2a, 0xaf, 0x41, 0x4e, 0x93, 0x13,
	0x63, 0xfb, 0x3d, 0xc0, 0xa8, 0xc1, 0x8c, 0xe4, 0xdd, 0xc3, 0xcd, 0xda, 0xf4, 0x01, 0x54, 0xfb,
	0x74, 0xf8, 0x18, 0xc5, 0x4f, 0x9d, 0xaf, 0xfc, 0x2d, 0xa5, 0x74, 0x51, 0xf9, 0x60, 0xdd, 0xba,
	0xf6, 0x0d, 0x28, 0xf0, 0x9c, 0x9e, 0xcb, 0xea, 0xce, 0x1b, 0x8b, 0x6c, 0xdd, 0x70, 0x6f, 0x58,
	0xf3, 0x2e, 0x3c, 0xbc, 0x8c, 0x9a, 0xe8, 0xd9, 0x5f, 0x12, 0xac, 0x29, 0xae, 0x3b, 0xee, 0x72,
	0x6b, 0xda, 0x2a, 0xac, 0x08, 0xda, 0xc1, 0x69, 0x38, 0x1a, 0xc4, 0x6b, 0xa4, 0x2c, 0xf5, 0x86,
	0x3f, 0xd9, 0x19, 0xf3, 0x41, 0x82, 0xf5, 0x14, 0x21, 0xd1, 0x26, 0x0d, 0x78, 0x89, 0x96, 0xed,
	0xba, 0xc4, 0xe5, 0x19, 0xa4, 0xeb, 0x33, 0x2c, 0x33, 0x4c, 0xa1, 0xdf, 0xb0, 0x7d, 0xf0, 0x18,
	0x56, 0x7a, 0xf6, 0x85, 0xf0, 0xe4, 0x3b, 0x81, 0xd7, 0x52, 0xea, 0xd9, 0x17, 0x5c, 0x25, 0xb6,
	0x15, 0x6a, 0x70, 0x7f, 0x78, 0x72, 0x8c, 0xfb, 0xf2, 0x49, 0xbc, 0x27, 0x4c, 0x23, 0xff, 0x4a,
	0x0b, 0xca, 0x53, 0xa7, 0x23, 0x4d, 0x77, 0x5b, 0x39, 0x2b, 0x6f, 0x61, 0x33, 0x33, 0xdc, 0x68,
	0x66, 0x39, 0xab, 0x59, 0x65, 0xe0, 0x57, 0x0c, 0x13, 0x79, 0x90, 0x9a, 0x0b, 0x0a, 0xd3, 0x91,
	0xb9, 0x75, 0xf3, 0x1f, 0x0f, 0x9b, 0x4f, 0xe9, 0xf0, 0xa9, 0xcc, 0x0b, 0x51, 0xc7, 0xe3, 0x57,
	0xf6, 0x60, 0xe7, 0xd2, 0xd4, 0x62, 0x26, 0x7f, 0x84, 0x87, 0xf5, 0xcf, 0xcb, 0xae, 0xd2, 0x80,
	0x9d, 0xfa, 0xd5, 0x59, 0xb3, 0x0a, 0x90, 0xb2, 0x0a, 0x68, 0xc3, 0x96, 0xea, 0x13, 0x3b, 0xfa,
	0x6c, 0x7d, 0xde, 0x81, 0xed, 0x4b, 0x02, 0x72, 0x66, 0xfb, 0x1f, 0x73, 0xb0, 0x96, 0x3e, 0x76,
	0xd9, 0x5b, 0x61, 0x03, 0x56, 0xb5, 0x56, 0xb7, 0xd3, 0x50, 0xad, 0x8e, 0xa9, 0x98, 0xdd, 0x8e,
	0xd5, 0xd5, 0x5f, 0xe9, 0xed, 0x37, 0x3a, 0xba, 0x83, 0x37, 0x61, 0x7d, 0xd2, 0x74, 0xd2, 0x54,
	0xde, 0x5a, 0xad, 0xf6, 0x91, 0x86, 0x24, 0x2c, 0xc3, 0x83, 0x49, 0xe3, 0xeb, 0x76, 0xb3, 0xdb,
	0xd2, 0x50, 0x2e, 0xfd, 0x99, 0xaa, 0xe8, 0x56, 0xe7, 0x58, 0x31, 0x34, 0x94, 0x4f, 0x1b, 0x5f,
	0x1a, 0x9a, 0xc6, 0x63, 0xce, 0xe1, 0x2d, 0x90, 0x27, 0x8d, 0xba, 0xf6, 0x93, 0x69, 0x31, 0x04,
	0xcd, 0xa7, 0x99, 0x9e, 0x68, 0x86, 0xaa, 0xe9, 0x26, 0x5a, 0xc0, 0xdb, 0xb0, 0x91, 0xc1, 0x94,
	0xff, 0x46, 0x8b, 0x78, 0x0f, 0xb6, 0xa7, 0x18, 0x75, 0x0d, 0x43, 0xd3, 0x4d, 0xe6, 0xd6, 0xd0,
	0xeb, 0xa8, 0x80, 0x77, 0x60, 0x73, 0xd2, 0xc5, 0x6c, 0xb4, 0xb4, 0x76, 0xd7, 0xb4, 0xd4, 0x76,
	0x57, 0x37, 0x51, 0x71, 0xff, 0xdf, 0x1c, 0xa0, 0xd6, 0xd4, 0xe9, 0x88, 0xcb, 0xb0, 0x26, 0xbe,
	0x52, 0x8f, 0x15, 0xbd, 0xae, 0x1d, 0x8d, 0xa9, 0xf7, 0x10, 0xca, 0x53, 0x36, 0xa5, 0x6b, 0xb6,
	0x2d, 0xbd, 0x6d, 0xb4, 0x94, 0x26, 0x92, 0x70, 0x15, 0x1e, 0x65, 0xdb, 0x39, 0x2d, 0x4b, 0xd7,
	0xcc, 0x37, 0x6d, 0xe3, 0x15, 0xca, 0xe1, 0x2f, 0x61, 0x6f, 0xe8, 0xa9, 0x1c, 0x67, 0x78, 0xbe,
	0x6c, 0x36, 0xa8, 0xb4, 0x23, 0x7d, 0x86, 0x01, 0x5b, 0x8a, 0xde, 0x55, 0x9a, 0x68, 0x6e, 0x42,
	0xba, 0xf6, 0x89, 0x76, 0x64, 0x1d, 0x69, 0x4d, 0xcd, 0xd4, 0x8e, 0xd0, 0x3c, 0xbe, 0x07, 0xa5,
	0x09, 0x13, 0x5a, 0x98, 0x68, 0x03, 0xf3, 0x6e, 0x36, 0x3a, 0xa6, 0xa5, 0xb5, 0x4e, 0xcc, 0xb7,
	0x68, 0x71, 0x9c, 0xcd, 0x34, 0xef, 0x63, 0x4d, 0x31, 0xcc, 0x17, 0x9a, 0x62, 0xa2, 0xc2, 0xfe,
	0x0f, 0x50, 0x9a, 0xd8, 0x25, 0x94, 0xc3, 0x04, 0x60, 0x1d, 0x91, 0x53, 0xbb, 0xef, 0x27, 0xe8,
	0x0e, 0x7e, 0x20, 0xa4, 0xfd, 0x64, 0xd2, 0x55, 0x24, 0x1d, 0xfe, 0x57, 0x80, 0xe5, 0xf1, 0xa1,
	0xad, 0x87, 0xf8, 0x02, 0x56, 0x33, 0x5f, 0x8f, 0xb8, 0x96, 0x3e, 0xb6, 0xae, 0x7a, 0xe2, 0x96,
	0x0f, 0x66, 0xf6, 0x17, 0x7b, 0xfb, 0x02, 0x56, 0x3b, 0xb3, 0x66, 0xee, 0xdc, 0x30, 0xf3, 0x95,
	0x6f, 0x22, 0xec, 0x01, 0x9a, 0x7e, 0x75, 0xe0, 0xa7, 0xe9, 0x20, 0x97, 0x3c, 0x9b, 0xca, 0xfb,
	0xb3, 0xb8, 0x8a, 0x54, 0xbf, 0xc3, 0x5a, 0xf6, 0x65, 0x8f, 0xaf, 0x67, 0x3d, 0xf9, 0x62, 0x29,
	0x7f, 0x35, 0xfb, 0x07, 0x22, 0xf9, 0x29, 0xac, 0x4c, 0xdd, 0xda, 0xb8, 0x9a, 0x0e, 0x92, 0xfd,
	0xd2, 0x28, 0x3f, 0x9d, 0xc1, 0x53, 0xe4, 0x89, 0xe0, 0x7e, 0xc6, 0x41, 0x8e, 0x9f, 0x5d, 0x3b,
	0x11, 0x63, 0x47, 0x74, 0xf9, 0xf9, 0x8c, 0xde, 0x22, 0xe7, 0x1f, 0xb0, 0x7e, 0xc9, 0x95, 0x85,
	0xaf, 0x17, 0x6a, 0xea, 0xea, 0x2a, 0x7f, 0x7d, 0x83, 0x2f, 0x46, 0xf9, 0xeb, 0xb3, 0xe7, 0xaf,
	0xdf, 0x38, 0xff, 0x75, 0x37, 0xe3, 0x05, 0xac, 0x66, 0x5e, 0x50, 0x59, 0xbb, 0xe7, 0xaa, 0xab,
	0x31, 0x6b, 0xf7, 0x5c, 0x79, 0xf3, 0xbd, 0xf8, 0xf6, 0xe7, 0xc3, 0xb3, 0xd0, 0xb7, 0x83, 0xb3,
	0xda, 0x77, 0x87, 0x49, 0x52, 0x73, 0xc2, 0xde, 0x01, 0xfb, 0x73, 0xc0, 0x09, 0xfd, 0x83, 0x98,
	0x44, 0xef, 0x3d, 0x87, 0xc4, 0x07, 0xd3, 0x31, 0x7f, 0x59, 0x60, 0x3e, 0xdf, 0xfc, 0x1f, 0x00,
	0x00, 0xff, 0xff, 0xba, 0xe8, 0x53, 0xd4, 0x6b, 0x10, 0x00, 0x00,
}
