// Code generated by protoc-gen-go. DO NOT EDIT.
// source: anchor-check/anchor-check.proto

package anchor_check // import "golang.52tt.com/protocol/services/anchor-check"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type COMPARE_TYPE int32

const (
	COMPARE_TYPE_COMPARE_TYPE_INVALID COMPARE_TYPE = 0
	COMPARE_TYPE_COMPARE_TYPE_GT      COMPARE_TYPE = 1
	COMPARE_TYPE_COMPARE_TYPE_GE      COMPARE_TYPE = 2
	COMPARE_TYPE_COMPARE_TYPE_LT      COMPARE_TYPE = 3
	COMPARE_TYPE_COMPARE_TYPE_LE      COMPARE_TYPE = 4
	COMPARE_TYPE_COMPARE_TYPE_EQ      COMPARE_TYPE = 5
)

var COMPARE_TYPE_name = map[int32]string{
	0: "COMPARE_TYPE_INVALID",
	1: "COMPARE_TYPE_GT",
	2: "COMPARE_TYPE_GE",
	3: "COMPARE_TYPE_LT",
	4: "COMPARE_TYPE_LE",
	5: "COMPARE_TYPE_EQ",
}
var COMPARE_TYPE_value = map[string]int32{
	"COMPARE_TYPE_INVALID": 0,
	"COMPARE_TYPE_GT":      1,
	"COMPARE_TYPE_GE":      2,
	"COMPARE_TYPE_LT":      3,
	"COMPARE_TYPE_LE":      4,
	"COMPARE_TYPE_EQ":      5,
}

func (x COMPARE_TYPE) String() string {
	return proto.EnumName(COMPARE_TYPE_name, int32(x))
}
func (COMPARE_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{0}
}

type AudioType int32

const (
	AudioType_AudioType_Check AudioType = 0
	AudioType_AudioType_Live  AudioType = 1
)

var AudioType_name = map[int32]string{
	0: "AudioType_Check",
	1: "AudioType_Live",
}
var AudioType_value = map[string]int32{
	"AudioType_Check": 0,
	"AudioType_Live":  1,
}

func (x AudioType) String() string {
	return proto.EnumName(AudioType_name, int32(x))
}
func (AudioType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{1}
}

type CheckType int32

const (
	CheckType_CheckType_Invalid CheckType = 0
	CheckType_CheckType_Auto    CheckType = 1
	CheckType_CheckType_Manual  CheckType = 2
)

var CheckType_name = map[int32]string{
	0: "CheckType_Invalid",
	1: "CheckType_Auto",
	2: "CheckType_Manual",
}
var CheckType_value = map[string]int32{
	"CheckType_Invalid": 0,
	"CheckType_Auto":    1,
	"CheckType_Manual":  2,
}

func (x CheckType) String() string {
	return proto.EnumName(CheckType_name, int32(x))
}
func (CheckType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{2}
}

type AnchorCheckStatusType int32

const (
	AnchorCheckStatusType_AnchorCheckStatusType_Init    AnchorCheckStatusType = 0
	AnchorCheckStatusType_AnchorCheckStatusType_Submit  AnchorCheckStatusType = 1
	AnchorCheckStatusType_AnchorCheckStatusType_Cancel  AnchorCheckStatusType = 2
	AnchorCheckStatusType_AnchorCheckStatusType_Pass    AnchorCheckStatusType = 3
	AnchorCheckStatusType_AnchorCheckStatusType_NotPass AnchorCheckStatusType = 4
)

var AnchorCheckStatusType_name = map[int32]string{
	0: "AnchorCheckStatusType_Init",
	1: "AnchorCheckStatusType_Submit",
	2: "AnchorCheckStatusType_Cancel",
	3: "AnchorCheckStatusType_Pass",
	4: "AnchorCheckStatusType_NotPass",
}
var AnchorCheckStatusType_value = map[string]int32{
	"AnchorCheckStatusType_Init":    0,
	"AnchorCheckStatusType_Submit":  1,
	"AnchorCheckStatusType_Cancel":  2,
	"AnchorCheckStatusType_Pass":    3,
	"AnchorCheckStatusType_NotPass": 4,
}

func (x AnchorCheckStatusType) String() string {
	return proto.EnumName(AnchorCheckStatusType_name, int32(x))
}
func (AnchorCheckStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{3}
}

// 考核来源类型
type AnchorCheckSourceType int32

const (
	AnchorCheckSourceType_E_CHECK_SOURCE_DEFLAUTE AnchorCheckSourceType = 0
	AnchorCheckSourceType_E_CHECK_SOURCE_NEW_SIGN AnchorCheckSourceType = 1
	AnchorCheckSourceType_E_CHECK_SOURCE_WHITE    AnchorCheckSourceType = 2
	AnchorCheckSourceType_E_CHECK_SOURCE_UPGRADE  AnchorCheckSourceType = 3
)

var AnchorCheckSourceType_name = map[int32]string{
	0: "E_CHECK_SOURCE_DEFLAUTE",
	1: "E_CHECK_SOURCE_NEW_SIGN",
	2: "E_CHECK_SOURCE_WHITE",
	3: "E_CHECK_SOURCE_UPGRADE",
}
var AnchorCheckSourceType_value = map[string]int32{
	"E_CHECK_SOURCE_DEFLAUTE": 0,
	"E_CHECK_SOURCE_NEW_SIGN": 1,
	"E_CHECK_SOURCE_WHITE":    2,
	"E_CHECK_SOURCE_UPGRADE":  3,
}

func (x AnchorCheckSourceType) String() string {
	return proto.EnumName(AnchorCheckSourceType_name, int32(x))
}
func (AnchorCheckSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{4}
}

type UidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UidReq) Reset()         { *m = UidReq{} }
func (m *UidReq) String() string { return proto.CompactTextString(m) }
func (*UidReq) ProtoMessage()    {}
func (*UidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{0}
}
func (m *UidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UidReq.Unmarshal(m, b)
}
func (m *UidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UidReq.Marshal(b, m, deterministic)
}
func (dst *UidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UidReq.Merge(dst, src)
}
func (m *UidReq) XXX_Size() int {
	return xxx_messageInfo_UidReq.Size(m)
}
func (m *UidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UidReq.DiscardUnknown(m)
}

var xxx_messageInfo_UidReq proto.InternalMessageInfo

func (m *UidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type WhiteData struct {
	ExpireTime           uint32   `protobuf:"varint,1,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	SourceType           uint32   `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WhiteData) Reset()         { *m = WhiteData{} }
func (m *WhiteData) String() string { return proto.CompactTextString(m) }
func (*WhiteData) ProtoMessage()    {}
func (*WhiteData) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{1}
}
func (m *WhiteData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WhiteData.Unmarshal(m, b)
}
func (m *WhiteData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WhiteData.Marshal(b, m, deterministic)
}
func (dst *WhiteData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WhiteData.Merge(dst, src)
}
func (m *WhiteData) XXX_Size() int {
	return xxx_messageInfo_WhiteData.Size(m)
}
func (m *WhiteData) XXX_DiscardUnknown() {
	xxx_messageInfo_WhiteData.DiscardUnknown(m)
}

var xxx_messageInfo_WhiteData proto.InternalMessageInfo

func (m *WhiteData) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *WhiteData) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

type StartRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoomId               string   `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	StreamId             string   `protobuf:"bytes,3,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	SourceType           uint32   `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartRecordReq) Reset()         { *m = StartRecordReq{} }
func (m *StartRecordReq) String() string { return proto.CompactTextString(m) }
func (*StartRecordReq) ProtoMessage()    {}
func (*StartRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{2}
}
func (m *StartRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartRecordReq.Unmarshal(m, b)
}
func (m *StartRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartRecordReq.Marshal(b, m, deterministic)
}
func (dst *StartRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartRecordReq.Merge(dst, src)
}
func (m *StartRecordReq) XXX_Size() int {
	return xxx_messageInfo_StartRecordReq.Size(m)
}
func (m *StartRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartRecordReq proto.InternalMessageInfo

func (m *StartRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartRecordReq) GetRoomId() string {
	if m != nil {
		return m.RoomId
	}
	return ""
}

func (m *StartRecordReq) GetStreamId() string {
	if m != nil {
		return m.StreamId
	}
	return ""
}

func (m *StartRecordReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

type StartRecordResp struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	MaxRecordTime        uint32   `protobuf:"varint,2,opt,name=max_record_time,json=maxRecordTime,proto3" json:"max_record_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartRecordResp) Reset()         { *m = StartRecordResp{} }
func (m *StartRecordResp) String() string { return proto.CompactTextString(m) }
func (*StartRecordResp) ProtoMessage()    {}
func (*StartRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{3}
}
func (m *StartRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartRecordResp.Unmarshal(m, b)
}
func (m *StartRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartRecordResp.Marshal(b, m, deterministic)
}
func (dst *StartRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartRecordResp.Merge(dst, src)
}
func (m *StartRecordResp) XXX_Size() int {
	return xxx_messageInfo_StartRecordResp.Size(m)
}
func (m *StartRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartRecordResp proto.InternalMessageInfo

func (m *StartRecordResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *StartRecordResp) GetMaxRecordTime() uint32 {
	if m != nil {
		return m.MaxRecordTime
	}
	return 0
}

type StopRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskId               string   `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Submit               bool     `protobuf:"varint,3,opt,name=submit,proto3" json:"submit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopRecordReq) Reset()         { *m = StopRecordReq{} }
func (m *StopRecordReq) String() string { return proto.CompactTextString(m) }
func (*StopRecordReq) ProtoMessage()    {}
func (*StopRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{4}
}
func (m *StopRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopRecordReq.Unmarshal(m, b)
}
func (m *StopRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopRecordReq.Marshal(b, m, deterministic)
}
func (dst *StopRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopRecordReq.Merge(dst, src)
}
func (m *StopRecordReq) XXX_Size() int {
	return xxx_messageInfo_StopRecordReq.Size(m)
}
func (m *StopRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopRecordReq proto.InternalMessageInfo

func (m *StopRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StopRecordReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *StopRecordReq) GetSubmit() bool {
	if m != nil {
		return m.Submit
	}
	return false
}

type EmptyRes struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyRes) Reset()         { *m = EmptyRes{} }
func (m *EmptyRes) String() string { return proto.CompactTextString(m) }
func (*EmptyRes) ProtoMessage()    {}
func (*EmptyRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{5}
}
func (m *EmptyRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyRes.Unmarshal(m, b)
}
func (m *EmptyRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyRes.Marshal(b, m, deterministic)
}
func (dst *EmptyRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyRes.Merge(dst, src)
}
func (m *EmptyRes) XXX_Size() int {
	return xxx_messageInfo_EmptyRes.Size(m)
}
func (m *EmptyRes) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyRes.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyRes proto.InternalMessageInfo

type CheckData struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	FileUrl              string   `protobuf:"bytes,3,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	CyScore              uint32   `protobuf:"varint,4,opt,name=cy_score,json=cyScore,proto3" json:"cy_score,omitempty"`
	SxScore              uint32   `protobuf:"varint,5,opt,name=sx_score,json=sxScore,proto3" json:"sx_score,omitempty"`
	YzScore              uint32   `protobuf:"varint,6,opt,name=yz_score,json=yzScore,proto3" json:"yz_score,omitempty"`
	KcScore              uint32   `protobuf:"varint,7,opt,name=kc_score,json=kcScore,proto3" json:"kc_score,omitempty"`
	Status               uint32   `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	BeginTime            uint32   `protobuf:"varint,9,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Remark               string   `protobuf:"bytes,11,opt,name=remark,proto3" json:"remark,omitempty"`
	CreateTime           uint32   `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckData) Reset()         { *m = CheckData{} }
func (m *CheckData) String() string { return proto.CompactTextString(m) }
func (*CheckData) ProtoMessage()    {}
func (*CheckData) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{6}
}
func (m *CheckData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckData.Unmarshal(m, b)
}
func (m *CheckData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckData.Marshal(b, m, deterministic)
}
func (dst *CheckData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckData.Merge(dst, src)
}
func (m *CheckData) XXX_Size() int {
	return xxx_messageInfo_CheckData.Size(m)
}
func (m *CheckData) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckData.DiscardUnknown(m)
}

var xxx_messageInfo_CheckData proto.InternalMessageInfo

func (m *CheckData) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *CheckData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckData) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *CheckData) GetCyScore() uint32 {
	if m != nil {
		return m.CyScore
	}
	return 0
}

func (m *CheckData) GetSxScore() uint32 {
	if m != nil {
		return m.SxScore
	}
	return 0
}

func (m *CheckData) GetYzScore() uint32 {
	if m != nil {
		return m.YzScore
	}
	return 0
}

func (m *CheckData) GetKcScore() uint32 {
	if m != nil {
		return m.KcScore
	}
	return 0
}

func (m *CheckData) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CheckData) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *CheckData) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *CheckData) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *CheckData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type GetCheckListReq struct {
	Status               int32    `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCheckListReq) Reset()         { *m = GetCheckListReq{} }
func (m *GetCheckListReq) String() string { return proto.CompactTextString(m) }
func (*GetCheckListReq) ProtoMessage()    {}
func (*GetCheckListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{7}
}
func (m *GetCheckListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCheckListReq.Unmarshal(m, b)
}
func (m *GetCheckListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCheckListReq.Marshal(b, m, deterministic)
}
func (dst *GetCheckListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCheckListReq.Merge(dst, src)
}
func (m *GetCheckListReq) XXX_Size() int {
	return xxx_messageInfo_GetCheckListReq.Size(m)
}
func (m *GetCheckListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCheckListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCheckListReq proto.InternalMessageInfo

func (m *GetCheckListReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetCheckListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCheckListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCheckListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCheckListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetCheckListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetCheckListResp struct {
	List                 []*CheckData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count                uint32       `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCheckListResp) Reset()         { *m = GetCheckListResp{} }
func (m *GetCheckListResp) String() string { return proto.CompactTextString(m) }
func (*GetCheckListResp) ProtoMessage()    {}
func (*GetCheckListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{8}
}
func (m *GetCheckListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCheckListResp.Unmarshal(m, b)
}
func (m *GetCheckListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCheckListResp.Marshal(b, m, deterministic)
}
func (dst *GetCheckListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCheckListResp.Merge(dst, src)
}
func (m *GetCheckListResp) XXX_Size() int {
	return xxx_messageInfo_GetCheckListResp.Size(m)
}
func (m *GetCheckListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCheckListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCheckListResp proto.InternalMessageInfo

func (m *GetCheckListResp) GetList() []*CheckData {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetCheckListResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type SetCheckDataReq struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	CyScore              uint32   `protobuf:"varint,2,opt,name=cy_score,json=cyScore,proto3" json:"cy_score,omitempty"`
	SxScore              uint32   `protobuf:"varint,3,opt,name=sx_score,json=sxScore,proto3" json:"sx_score,omitempty"`
	YzScore              uint32   `protobuf:"varint,4,opt,name=yz_score,json=yzScore,proto3" json:"yz_score,omitempty"`
	KcScore              uint32   `protobuf:"varint,5,opt,name=kc_score,json=kcScore,proto3" json:"kc_score,omitempty"`
	Remark               string   `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCheckDataReq) Reset()         { *m = SetCheckDataReq{} }
func (m *SetCheckDataReq) String() string { return proto.CompactTextString(m) }
func (*SetCheckDataReq) ProtoMessage()    {}
func (*SetCheckDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{9}
}
func (m *SetCheckDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCheckDataReq.Unmarshal(m, b)
}
func (m *SetCheckDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCheckDataReq.Marshal(b, m, deterministic)
}
func (dst *SetCheckDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCheckDataReq.Merge(dst, src)
}
func (m *SetCheckDataReq) XXX_Size() int {
	return xxx_messageInfo_SetCheckDataReq.Size(m)
}
func (m *SetCheckDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCheckDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCheckDataReq proto.InternalMessageInfo

func (m *SetCheckDataReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *SetCheckDataReq) GetCyScore() uint32 {
	if m != nil {
		return m.CyScore
	}
	return 0
}

func (m *SetCheckDataReq) GetSxScore() uint32 {
	if m != nil {
		return m.SxScore
	}
	return 0
}

func (m *SetCheckDataReq) GetYzScore() uint32 {
	if m != nil {
		return m.YzScore
	}
	return 0
}

func (m *SetCheckDataReq) GetKcScore() uint32 {
	if m != nil {
		return m.KcScore
	}
	return 0
}

func (m *SetCheckDataReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type SetWhiteReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetWhiteReq) Reset()         { *m = SetWhiteReq{} }
func (m *SetWhiteReq) String() string { return proto.CompactTextString(m) }
func (*SetWhiteReq) ProtoMessage()    {}
func (*SetWhiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{10}
}
func (m *SetWhiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetWhiteReq.Unmarshal(m, b)
}
func (m *SetWhiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetWhiteReq.Marshal(b, m, deterministic)
}
func (dst *SetWhiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetWhiteReq.Merge(dst, src)
}
func (m *SetWhiteReq) XXX_Size() int {
	return xxx_messageInfo_SetWhiteReq.Size(m)
}
func (m *SetWhiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetWhiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetWhiteReq proto.InternalMessageInfo

func (m *SetWhiteReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetWhiteExpireUidListReq struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteExpireUidListReq) Reset()         { *m = GetWhiteExpireUidListReq{} }
func (m *GetWhiteExpireUidListReq) String() string { return proto.CompactTextString(m) }
func (*GetWhiteExpireUidListReq) ProtoMessage()    {}
func (*GetWhiteExpireUidListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{11}
}
func (m *GetWhiteExpireUidListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteExpireUidListReq.Unmarshal(m, b)
}
func (m *GetWhiteExpireUidListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteExpireUidListReq.Marshal(b, m, deterministic)
}
func (dst *GetWhiteExpireUidListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteExpireUidListReq.Merge(dst, src)
}
func (m *GetWhiteExpireUidListReq) XXX_Size() int {
	return xxx_messageInfo_GetWhiteExpireUidListReq.Size(m)
}
func (m *GetWhiteExpireUidListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteExpireUidListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteExpireUidListReq proto.InternalMessageInfo

func (m *GetWhiteExpireUidListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetWhiteExpireUidListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetWhiteExpireUidListResp struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteExpireUidListResp) Reset()         { *m = GetWhiteExpireUidListResp{} }
func (m *GetWhiteExpireUidListResp) String() string { return proto.CompactTextString(m) }
func (*GetWhiteExpireUidListResp) ProtoMessage()    {}
func (*GetWhiteExpireUidListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{12}
}
func (m *GetWhiteExpireUidListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteExpireUidListResp.Unmarshal(m, b)
}
func (m *GetWhiteExpireUidListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteExpireUidListResp.Marshal(b, m, deterministic)
}
func (dst *GetWhiteExpireUidListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteExpireUidListResp.Merge(dst, src)
}
func (m *GetWhiteExpireUidListResp) XXX_Size() int {
	return xxx_messageInfo_GetWhiteExpireUidListResp.Size(m)
}
func (m *GetWhiteExpireUidListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteExpireUidListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteExpireUidListResp proto.InternalMessageInfo

func (m *GetWhiteExpireUidListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type LastCreateScore struct {
	CyScore              uint32   `protobuf:"varint,1,opt,name=cy_score,json=cyScore,proto3" json:"cy_score,omitempty"`
	SxScore              uint32   `protobuf:"varint,2,opt,name=sx_score,json=sxScore,proto3" json:"sx_score,omitempty"`
	YzScore              uint32   `protobuf:"varint,3,opt,name=yz_score,json=yzScore,proto3" json:"yz_score,omitempty"`
	KcScore              uint32   `protobuf:"varint,4,opt,name=kc_score,json=kcScore,proto3" json:"kc_score,omitempty"`
	Level                string   `protobuf:"bytes,5,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LastCreateScore) Reset()         { *m = LastCreateScore{} }
func (m *LastCreateScore) String() string { return proto.CompactTextString(m) }
func (*LastCreateScore) ProtoMessage()    {}
func (*LastCreateScore) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{13}
}
func (m *LastCreateScore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LastCreateScore.Unmarshal(m, b)
}
func (m *LastCreateScore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LastCreateScore.Marshal(b, m, deterministic)
}
func (dst *LastCreateScore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LastCreateScore.Merge(dst, src)
}
func (m *LastCreateScore) XXX_Size() int {
	return xxx_messageInfo_LastCreateScore.Size(m)
}
func (m *LastCreateScore) XXX_DiscardUnknown() {
	xxx_messageInfo_LastCreateScore.DiscardUnknown(m)
}

var xxx_messageInfo_LastCreateScore proto.InternalMessageInfo

func (m *LastCreateScore) GetCyScore() uint32 {
	if m != nil {
		return m.CyScore
	}
	return 0
}

func (m *LastCreateScore) GetSxScore() uint32 {
	if m != nil {
		return m.SxScore
	}
	return 0
}

func (m *LastCreateScore) GetYzScore() uint32 {
	if m != nil {
		return m.YzScore
	}
	return 0
}

func (m *LastCreateScore) GetKcScore() uint32 {
	if m != nil {
		return m.KcScore
	}
	return 0
}

func (m *LastCreateScore) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

type WhiteReqElem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Operator             string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WhiteReqElem) Reset()         { *m = WhiteReqElem{} }
func (m *WhiteReqElem) String() string { return proto.CompactTextString(m) }
func (*WhiteReqElem) ProtoMessage()    {}
func (*WhiteReqElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{14}
}
func (m *WhiteReqElem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WhiteReqElem.Unmarshal(m, b)
}
func (m *WhiteReqElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WhiteReqElem.Marshal(b, m, deterministic)
}
func (dst *WhiteReqElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WhiteReqElem.Merge(dst, src)
}
func (m *WhiteReqElem) XXX_Size() int {
	return xxx_messageInfo_WhiteReqElem.Size(m)
}
func (m *WhiteReqElem) XXX_DiscardUnknown() {
	xxx_messageInfo_WhiteReqElem.DiscardUnknown(m)
}

var xxx_messageInfo_WhiteReqElem proto.InternalMessageInfo

func (m *WhiteReqElem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WhiteReqElem) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type WhiteListReq struct {
	UidList              []*WhiteReqElem `protobuf:"bytes,1,rep,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *WhiteListReq) Reset()         { *m = WhiteListReq{} }
func (m *WhiteListReq) String() string { return proto.CompactTextString(m) }
func (*WhiteListReq) ProtoMessage()    {}
func (*WhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{15}
}
func (m *WhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WhiteListReq.Unmarshal(m, b)
}
func (m *WhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WhiteListReq.Marshal(b, m, deterministic)
}
func (dst *WhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WhiteListReq.Merge(dst, src)
}
func (m *WhiteListReq) XXX_Size() int {
	return xxx_messageInfo_WhiteListReq.Size(m)
}
func (m *WhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_WhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_WhiteListReq proto.InternalMessageInfo

func (m *WhiteListReq) GetUidList() []*WhiteReqElem {
	if m != nil {
		return m.UidList
	}
	return nil
}

type AnchorCheckGetCheckListReq struct {
	Status               int32    `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	AudioCheckType       uint32   `protobuf:"varint,7,opt,name=audio_check_type,json=audioCheckType,proto3" json:"audio_check_type,omitempty"`
	AudioCheckScore      float64  `protobuf:"fixed64,8,opt,name=audio_check_score,json=audioCheckScore,proto3" json:"audio_check_score,omitempty"`
	LiveAudioCheckType   uint32   `protobuf:"varint,9,opt,name=live_audio_check_type,json=liveAudioCheckType,proto3" json:"live_audio_check_type,omitempty"`
	LiveAudioCheckScore  float64  `protobuf:"fixed64,10,opt,name=live_audio_check_score,json=liveAudioCheckScore,proto3" json:"live_audio_check_score,omitempty"`
	SourceType           uint32   `protobuf:"varint,31,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCheckGetCheckListReq) Reset()         { *m = AnchorCheckGetCheckListReq{} }
func (m *AnchorCheckGetCheckListReq) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckGetCheckListReq) ProtoMessage()    {}
func (*AnchorCheckGetCheckListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{16}
}
func (m *AnchorCheckGetCheckListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckGetCheckListReq.Unmarshal(m, b)
}
func (m *AnchorCheckGetCheckListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckGetCheckListReq.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckGetCheckListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckGetCheckListReq.Merge(dst, src)
}
func (m *AnchorCheckGetCheckListReq) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckGetCheckListReq.Size(m)
}
func (m *AnchorCheckGetCheckListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckGetCheckListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckGetCheckListReq proto.InternalMessageInfo

func (m *AnchorCheckGetCheckListReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AnchorCheckGetCheckListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *AnchorCheckGetCheckListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *AnchorCheckGetCheckListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorCheckGetCheckListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *AnchorCheckGetCheckListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *AnchorCheckGetCheckListReq) GetAudioCheckType() uint32 {
	if m != nil {
		return m.AudioCheckType
	}
	return 0
}

func (m *AnchorCheckGetCheckListReq) GetAudioCheckScore() float64 {
	if m != nil {
		return m.AudioCheckScore
	}
	return 0
}

func (m *AnchorCheckGetCheckListReq) GetLiveAudioCheckType() uint32 {
	if m != nil {
		return m.LiveAudioCheckType
	}
	return 0
}

func (m *AnchorCheckGetCheckListReq) GetLiveAudioCheckScore() float64 {
	if m != nil {
		return m.LiveAudioCheckScore
	}
	return 0
}

func (m *AnchorCheckGetCheckListReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

type AnchorCheckData struct {
	TaskId                   string       `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Uid                      uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	FileUrl                  string       `protobuf:"bytes,3,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	CyScore                  uint32       `protobuf:"varint,4,opt,name=cy_score,json=cyScore,proto3" json:"cy_score,omitempty"`
	SxScore                  uint32       `protobuf:"varint,5,opt,name=sx_score,json=sxScore,proto3" json:"sx_score,omitempty"`
	YzScore                  uint32       `protobuf:"varint,6,opt,name=yz_score,json=yzScore,proto3" json:"yz_score,omitempty"`
	KcScore                  uint32       `protobuf:"varint,7,opt,name=kc_score,json=kcScore,proto3" json:"kc_score,omitempty"`
	Status                   uint32       `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	BeginTime                uint32       `protobuf:"varint,9,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime                  uint32       `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Remark                   string       `protobuf:"bytes,11,opt,name=remark,proto3" json:"remark,omitempty"`
	CreateTime               uint32       `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Ttid                     string       `protobuf:"bytes,13,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nick                     string       `protobuf:"bytes,14,opt,name=nick,proto3" json:"nick,omitempty"`
	TagName                  string       `protobuf:"bytes,15,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	GuildId                  uint32       `protobuf:"varint,16,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId             uint32       `protobuf:"varint,17,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	GuildName                string       `protobuf:"bytes,18,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	DisplayId                uint32       `protobuf:"varint,19,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	Operator                 string       `protobuf:"bytes,20,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdateTime               uint32       `protobuf:"varint,21,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Level                    string       `protobuf:"bytes,22,opt,name=level,proto3" json:"level,omitempty"`
	VociePercentage          float64      `protobuf:"fixed64,23,opt,name=vocie_percentage,json=vociePercentage,proto3" json:"vocie_percentage,omitempty"`
	VociePercentageTimeRange []*TimeRange `protobuf:"bytes,24,rep,name=vocie_percentage_time_range,json=vociePercentageTimeRange,proto3" json:"vocie_percentage_time_range,omitempty"`
	AudioCheckScore          float64      `protobuf:"fixed64,25,opt,name=audio_check_score,json=audioCheckScore,proto3" json:"audio_check_score,omitempty"`
	LiveAudioCheckScore      float64      `protobuf:"fixed64,26,opt,name=live_audio_check_score,json=liveAudioCheckScore,proto3" json:"live_audio_check_score,omitempty"`
	CheckType                uint32       `protobuf:"varint,27,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	AudioCheckList           []*CheatInfo `protobuf:"bytes,28,rep,name=audio_check_list,json=audioCheckList,proto3" json:"audio_check_list,omitempty"`
	LiveAudioCheckList       []*CheatInfo `protobuf:"bytes,29,rep,name=live_audio_check_list,json=liveAudioCheckList,proto3" json:"live_audio_check_list,omitempty"`
	SentenceLength           uint32       `protobuf:"varint,30,opt,name=sentence_length,json=sentenceLength,proto3" json:"sentence_length,omitempty"`
	SourceType               uint32       `protobuf:"varint,31,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	ChannelViewId            string       `protobuf:"bytes,32,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}     `json:"-"`
	XXX_unrecognized         []byte       `json:"-"`
	XXX_sizecache            int32        `json:"-"`
}

func (m *AnchorCheckData) Reset()         { *m = AnchorCheckData{} }
func (m *AnchorCheckData) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckData) ProtoMessage()    {}
func (*AnchorCheckData) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{17}
}
func (m *AnchorCheckData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckData.Unmarshal(m, b)
}
func (m *AnchorCheckData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckData.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckData.Merge(dst, src)
}
func (m *AnchorCheckData) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckData.Size(m)
}
func (m *AnchorCheckData) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckData.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckData proto.InternalMessageInfo

func (m *AnchorCheckData) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *AnchorCheckData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorCheckData) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *AnchorCheckData) GetCyScore() uint32 {
	if m != nil {
		return m.CyScore
	}
	return 0
}

func (m *AnchorCheckData) GetSxScore() uint32 {
	if m != nil {
		return m.SxScore
	}
	return 0
}

func (m *AnchorCheckData) GetYzScore() uint32 {
	if m != nil {
		return m.YzScore
	}
	return 0
}

func (m *AnchorCheckData) GetKcScore() uint32 {
	if m != nil {
		return m.KcScore
	}
	return 0
}

func (m *AnchorCheckData) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *AnchorCheckData) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *AnchorCheckData) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *AnchorCheckData) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *AnchorCheckData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *AnchorCheckData) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AnchorCheckData) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

func (m *AnchorCheckData) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *AnchorCheckData) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AnchorCheckData) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *AnchorCheckData) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *AnchorCheckData) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *AnchorCheckData) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *AnchorCheckData) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *AnchorCheckData) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

func (m *AnchorCheckData) GetVociePercentage() float64 {
	if m != nil {
		return m.VociePercentage
	}
	return 0
}

func (m *AnchorCheckData) GetVociePercentageTimeRange() []*TimeRange {
	if m != nil {
		return m.VociePercentageTimeRange
	}
	return nil
}

func (m *AnchorCheckData) GetAudioCheckScore() float64 {
	if m != nil {
		return m.AudioCheckScore
	}
	return 0
}

func (m *AnchorCheckData) GetLiveAudioCheckScore() float64 {
	if m != nil {
		return m.LiveAudioCheckScore
	}
	return 0
}

func (m *AnchorCheckData) GetCheckType() uint32 {
	if m != nil {
		return m.CheckType
	}
	return 0
}

func (m *AnchorCheckData) GetAudioCheckList() []*CheatInfo {
	if m != nil {
		return m.AudioCheckList
	}
	return nil
}

func (m *AnchorCheckData) GetLiveAudioCheckList() []*CheatInfo {
	if m != nil {
		return m.LiveAudioCheckList
	}
	return nil
}

func (m *AnchorCheckData) GetSentenceLength() uint32 {
	if m != nil {
		return m.SentenceLength
	}
	return 0
}

func (m *AnchorCheckData) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *AnchorCheckData) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type TimeRange struct {
	BeginTs              uint32   `protobuf:"varint,1,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,2,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeRange) Reset()         { *m = TimeRange{} }
func (m *TimeRange) String() string { return proto.CompactTextString(m) }
func (*TimeRange) ProtoMessage()    {}
func (*TimeRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{18}
}
func (m *TimeRange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeRange.Unmarshal(m, b)
}
func (m *TimeRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeRange.Marshal(b, m, deterministic)
}
func (dst *TimeRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeRange.Merge(dst, src)
}
func (m *TimeRange) XXX_Size() int {
	return xxx_messageInfo_TimeRange.Size(m)
}
func (m *TimeRange) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeRange.DiscardUnknown(m)
}

var xxx_messageInfo_TimeRange proto.InternalMessageInfo

func (m *TimeRange) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *TimeRange) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type CheatInfo struct {
	TargetUid uint32  `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Score     float64 `protobuf:"fixed64,2,opt,name=score,proto3" json:"score,omitempty"`
	FileUrl   string  `protobuf:"bytes,3,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	// 相似主播的taskid 公会名 公会id ttid uid
	TaskId               string   `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	GuildName            string   `protobuf:"bytes,5,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	GuildShortId         uint32   `protobuf:"varint,6,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	GuildId              uint32   `protobuf:"varint,7,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Ttid                 string   `protobuf:"bytes,8,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheatInfo) Reset()         { *m = CheatInfo{} }
func (m *CheatInfo) String() string { return proto.CompactTextString(m) }
func (*CheatInfo) ProtoMessage()    {}
func (*CheatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{19}
}
func (m *CheatInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheatInfo.Unmarshal(m, b)
}
func (m *CheatInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheatInfo.Marshal(b, m, deterministic)
}
func (dst *CheatInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheatInfo.Merge(dst, src)
}
func (m *CheatInfo) XXX_Size() int {
	return xxx_messageInfo_CheatInfo.Size(m)
}
func (m *CheatInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CheatInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CheatInfo proto.InternalMessageInfo

func (m *CheatInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *CheatInfo) GetScore() float64 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *CheatInfo) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *CheatInfo) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *CheatInfo) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *CheatInfo) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *CheatInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CheatInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type AnchorCheckGetCheckListResp struct {
	List                 []*AnchorCheckData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count                uint32             `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AnchorCheckGetCheckListResp) Reset()         { *m = AnchorCheckGetCheckListResp{} }
func (m *AnchorCheckGetCheckListResp) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckGetCheckListResp) ProtoMessage()    {}
func (*AnchorCheckGetCheckListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{20}
}
func (m *AnchorCheckGetCheckListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckGetCheckListResp.Unmarshal(m, b)
}
func (m *AnchorCheckGetCheckListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckGetCheckListResp.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckGetCheckListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckGetCheckListResp.Merge(dst, src)
}
func (m *AnchorCheckGetCheckListResp) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckGetCheckListResp.Size(m)
}
func (m *AnchorCheckGetCheckListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckGetCheckListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckGetCheckListResp proto.InternalMessageInfo

func (m *AnchorCheckGetCheckListResp) GetList() []*AnchorCheckData {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *AnchorCheckGetCheckListResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type AnchorCheckSetCheckData struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	CyScore              uint32   `protobuf:"varint,2,opt,name=cy_score,json=cyScore,proto3" json:"cy_score,omitempty"`
	SxScore              uint32   `protobuf:"varint,3,opt,name=sx_score,json=sxScore,proto3" json:"sx_score,omitempty"`
	YzScore              uint32   `protobuf:"varint,4,opt,name=yz_score,json=yzScore,proto3" json:"yz_score,omitempty"`
	KcScore              uint32   `protobuf:"varint,5,opt,name=kc_score,json=kcScore,proto3" json:"kc_score,omitempty"`
	Remark               string   `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark,omitempty"`
	Operator             string   `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCheckSetCheckData) Reset()         { *m = AnchorCheckSetCheckData{} }
func (m *AnchorCheckSetCheckData) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckSetCheckData) ProtoMessage()    {}
func (*AnchorCheckSetCheckData) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{21}
}
func (m *AnchorCheckSetCheckData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckSetCheckData.Unmarshal(m, b)
}
func (m *AnchorCheckSetCheckData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckSetCheckData.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckSetCheckData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckSetCheckData.Merge(dst, src)
}
func (m *AnchorCheckSetCheckData) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckSetCheckData.Size(m)
}
func (m *AnchorCheckSetCheckData) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckSetCheckData.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckSetCheckData proto.InternalMessageInfo

func (m *AnchorCheckSetCheckData) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *AnchorCheckSetCheckData) GetCyScore() uint32 {
	if m != nil {
		return m.CyScore
	}
	return 0
}

func (m *AnchorCheckSetCheckData) GetSxScore() uint32 {
	if m != nil {
		return m.SxScore
	}
	return 0
}

func (m *AnchorCheckSetCheckData) GetYzScore() uint32 {
	if m != nil {
		return m.YzScore
	}
	return 0
}

func (m *AnchorCheckSetCheckData) GetKcScore() uint32 {
	if m != nil {
		return m.KcScore
	}
	return 0
}

func (m *AnchorCheckSetCheckData) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *AnchorCheckSetCheckData) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type AnchorCheckSetCheckDataReq struct {
	List                 []*AnchorCheckSetCheckData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *AnchorCheckSetCheckDataReq) Reset()         { *m = AnchorCheckSetCheckDataReq{} }
func (m *AnchorCheckSetCheckDataReq) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckSetCheckDataReq) ProtoMessage()    {}
func (*AnchorCheckSetCheckDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{22}
}
func (m *AnchorCheckSetCheckDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckSetCheckDataReq.Unmarshal(m, b)
}
func (m *AnchorCheckSetCheckDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckSetCheckDataReq.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckSetCheckDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckSetCheckDataReq.Merge(dst, src)
}
func (m *AnchorCheckSetCheckDataReq) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckSetCheckDataReq.Size(m)
}
func (m *AnchorCheckSetCheckDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckSetCheckDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckSetCheckDataReq proto.InternalMessageInfo

func (m *AnchorCheckSetCheckDataReq) GetList() []*AnchorCheckSetCheckData {
	if m != nil {
		return m.List
	}
	return nil
}

type ExportWhiteExpireListReq struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExportWhiteExpireListReq) Reset()         { *m = ExportWhiteExpireListReq{} }
func (m *ExportWhiteExpireListReq) String() string { return proto.CompactTextString(m) }
func (*ExportWhiteExpireListReq) ProtoMessage()    {}
func (*ExportWhiteExpireListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{23}
}
func (m *ExportWhiteExpireListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportWhiteExpireListReq.Unmarshal(m, b)
}
func (m *ExportWhiteExpireListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportWhiteExpireListReq.Marshal(b, m, deterministic)
}
func (dst *ExportWhiteExpireListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportWhiteExpireListReq.Merge(dst, src)
}
func (m *ExportWhiteExpireListReq) XXX_Size() int {
	return xxx_messageInfo_ExportWhiteExpireListReq.Size(m)
}
func (m *ExportWhiteExpireListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportWhiteExpireListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExportWhiteExpireListReq proto.InternalMessageInfo

func (m *ExportWhiteExpireListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ExportWhiteExpireListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type WhiteUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nick                 string   `protobuf:"bytes,3,opt,name=nick,proto3" json:"nick,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WhiteUserInfo) Reset()         { *m = WhiteUserInfo{} }
func (m *WhiteUserInfo) String() string { return proto.CompactTextString(m) }
func (*WhiteUserInfo) ProtoMessage()    {}
func (*WhiteUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{24}
}
func (m *WhiteUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WhiteUserInfo.Unmarshal(m, b)
}
func (m *WhiteUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WhiteUserInfo.Marshal(b, m, deterministic)
}
func (dst *WhiteUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WhiteUserInfo.Merge(dst, src)
}
func (m *WhiteUserInfo) XXX_Size() int {
	return xxx_messageInfo_WhiteUserInfo.Size(m)
}
func (m *WhiteUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WhiteUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WhiteUserInfo proto.InternalMessageInfo

func (m *WhiteUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WhiteUserInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *WhiteUserInfo) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

type ExportWhiteExpireListResp struct {
	List                 []*WhiteUserInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ExportWhiteExpireListResp) Reset()         { *m = ExportWhiteExpireListResp{} }
func (m *ExportWhiteExpireListResp) String() string { return proto.CompactTextString(m) }
func (*ExportWhiteExpireListResp) ProtoMessage()    {}
func (*ExportWhiteExpireListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{25}
}
func (m *ExportWhiteExpireListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportWhiteExpireListResp.Unmarshal(m, b)
}
func (m *ExportWhiteExpireListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportWhiteExpireListResp.Marshal(b, m, deterministic)
}
func (dst *ExportWhiteExpireListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportWhiteExpireListResp.Merge(dst, src)
}
func (m *ExportWhiteExpireListResp) XXX_Size() int {
	return xxx_messageInfo_ExportWhiteExpireListResp.Size(m)
}
func (m *ExportWhiteExpireListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportWhiteExpireListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExportWhiteExpireListResp proto.InternalMessageInfo

func (m *ExportWhiteExpireListResp) GetList() []*WhiteUserInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type EmptyMsg struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyMsg) Reset()         { *m = EmptyMsg{} }
func (m *EmptyMsg) String() string { return proto.CompactTextString(m) }
func (*EmptyMsg) ProtoMessage()    {}
func (*EmptyMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{26}
}
func (m *EmptyMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyMsg.Unmarshal(m, b)
}
func (m *EmptyMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyMsg.Marshal(b, m, deterministic)
}
func (dst *EmptyMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyMsg.Merge(dst, src)
}
func (m *EmptyMsg) XXX_Size() int {
	return xxx_messageInfo_EmptyMsg.Size(m)
}
func (m *EmptyMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyMsg.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyMsg proto.InternalMessageInfo

type AnchorScore struct {
	CyScore              uint32   `protobuf:"varint,1,opt,name=cy_score,json=cyScore,proto3" json:"cy_score,omitempty"`
	SxScore              uint32   `protobuf:"varint,2,opt,name=sx_score,json=sxScore,proto3" json:"sx_score,omitempty"`
	YzScore              uint32   `protobuf:"varint,3,opt,name=yz_score,json=yzScore,proto3" json:"yz_score,omitempty"`
	KcScore              uint32   `protobuf:"varint,4,opt,name=kc_score,json=kcScore,proto3" json:"kc_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorScore) Reset()         { *m = AnchorScore{} }
func (m *AnchorScore) String() string { return proto.CompactTextString(m) }
func (*AnchorScore) ProtoMessage()    {}
func (*AnchorScore) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{27}
}
func (m *AnchorScore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorScore.Unmarshal(m, b)
}
func (m *AnchorScore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorScore.Marshal(b, m, deterministic)
}
func (dst *AnchorScore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorScore.Merge(dst, src)
}
func (m *AnchorScore) XXX_Size() int {
	return xxx_messageInfo_AnchorScore.Size(m)
}
func (m *AnchorScore) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorScore.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorScore proto.InternalMessageInfo

func (m *AnchorScore) GetCyScore() uint32 {
	if m != nil {
		return m.CyScore
	}
	return 0
}

func (m *AnchorScore) GetSxScore() uint32 {
	if m != nil {
		return m.SxScore
	}
	return 0
}

func (m *AnchorScore) GetYzScore() uint32 {
	if m != nil {
		return m.YzScore
	}
	return 0
}

func (m *AnchorScore) GetKcScore() uint32 {
	if m != nil {
		return m.KcScore
	}
	return 0
}

type LevelData struct {
	Level                string   `protobuf:"bytes,1,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelData) Reset()         { *m = LevelData{} }
func (m *LevelData) String() string { return proto.CompactTextString(m) }
func (*LevelData) ProtoMessage()    {}
func (*LevelData) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{28}
}
func (m *LevelData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelData.Unmarshal(m, b)
}
func (m *LevelData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelData.Marshal(b, m, deterministic)
}
func (dst *LevelData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelData.Merge(dst, src)
}
func (m *LevelData) XXX_Size() int {
	return xxx_messageInfo_LevelData.Size(m)
}
func (m *LevelData) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelData.DiscardUnknown(m)
}

var xxx_messageInfo_LevelData proto.InternalMessageInfo

func (m *LevelData) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

type AnchorCheckGetWhiteListReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Status               int32    `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCheckGetWhiteListReq) Reset()         { *m = AnchorCheckGetWhiteListReq{} }
func (m *AnchorCheckGetWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckGetWhiteListReq) ProtoMessage()    {}
func (*AnchorCheckGetWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{29}
}
func (m *AnchorCheckGetWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckGetWhiteListReq.Unmarshal(m, b)
}
func (m *AnchorCheckGetWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckGetWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckGetWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckGetWhiteListReq.Merge(dst, src)
}
func (m *AnchorCheckGetWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckGetWhiteListReq.Size(m)
}
func (m *AnchorCheckGetWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckGetWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckGetWhiteListReq proto.InternalMessageInfo

func (m *AnchorCheckGetWhiteListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *AnchorCheckGetWhiteListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *AnchorCheckGetWhiteListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorCheckGetWhiteListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *AnchorCheckGetWhiteListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *AnchorCheckGetWhiteListReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type WhiteDetailInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nick                 string   `protobuf:"bytes,3,opt,name=nick,proto3" json:"nick,omitempty"`
	TagName              string   `protobuf:"bytes,4,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	GuildId              uint32   `protobuf:"varint,5,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId         uint32   `protobuf:"varint,6,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	GuildName            string   `protobuf:"bytes,7,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	DisplayId            uint32   `protobuf:"varint,8,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	Status               uint32   `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	CreateTime           uint32   `protobuf:"varint,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string   `protobuf:"bytes,11,opt,name=operator,proto3" json:"operator,omitempty"`
	SourceType           uint32   `protobuf:"varint,12,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,13,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WhiteDetailInfo) Reset()         { *m = WhiteDetailInfo{} }
func (m *WhiteDetailInfo) String() string { return proto.CompactTextString(m) }
func (*WhiteDetailInfo) ProtoMessage()    {}
func (*WhiteDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{30}
}
func (m *WhiteDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WhiteDetailInfo.Unmarshal(m, b)
}
func (m *WhiteDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WhiteDetailInfo.Marshal(b, m, deterministic)
}
func (dst *WhiteDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WhiteDetailInfo.Merge(dst, src)
}
func (m *WhiteDetailInfo) XXX_Size() int {
	return xxx_messageInfo_WhiteDetailInfo.Size(m)
}
func (m *WhiteDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WhiteDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WhiteDetailInfo proto.InternalMessageInfo

func (m *WhiteDetailInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WhiteDetailInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *WhiteDetailInfo) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

func (m *WhiteDetailInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *WhiteDetailInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *WhiteDetailInfo) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *WhiteDetailInfo) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *WhiteDetailInfo) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *WhiteDetailInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *WhiteDetailInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *WhiteDetailInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *WhiteDetailInfo) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *WhiteDetailInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type AnchorCheckGetWhiteListResp struct {
	List                 []*WhiteDetailInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count                uint32             `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AnchorCheckGetWhiteListResp) Reset()         { *m = AnchorCheckGetWhiteListResp{} }
func (m *AnchorCheckGetWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckGetWhiteListResp) ProtoMessage()    {}
func (*AnchorCheckGetWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{31}
}
func (m *AnchorCheckGetWhiteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckGetWhiteListResp.Unmarshal(m, b)
}
func (m *AnchorCheckGetWhiteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckGetWhiteListResp.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckGetWhiteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckGetWhiteListResp.Merge(dst, src)
}
func (m *AnchorCheckGetWhiteListResp) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckGetWhiteListResp.Size(m)
}
func (m *AnchorCheckGetWhiteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckGetWhiteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckGetWhiteListResp proto.InternalMessageInfo

func (m *AnchorCheckGetWhiteListResp) GetList() []*WhiteDetailInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *AnchorCheckGetWhiteListResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetAnchorCheckHistoryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorCheckHistoryReq) Reset()         { *m = GetAnchorCheckHistoryReq{} }
func (m *GetAnchorCheckHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorCheckHistoryReq) ProtoMessage()    {}
func (*GetAnchorCheckHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{32}
}
func (m *GetAnchorCheckHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorCheckHistoryReq.Unmarshal(m, b)
}
func (m *GetAnchorCheckHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorCheckHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorCheckHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorCheckHistoryReq.Merge(dst, src)
}
func (m *GetAnchorCheckHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorCheckHistoryReq.Size(m)
}
func (m *GetAnchorCheckHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorCheckHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorCheckHistoryReq proto.InternalMessageInfo

func (m *GetAnchorCheckHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAnchorCheckHistoryResp struct {
	List                 []*AnchorCheckData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAnchorCheckHistoryResp) Reset()         { *m = GetAnchorCheckHistoryResp{} }
func (m *GetAnchorCheckHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorCheckHistoryResp) ProtoMessage()    {}
func (*GetAnchorCheckHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{33}
}
func (m *GetAnchorCheckHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorCheckHistoryResp.Unmarshal(m, b)
}
func (m *GetAnchorCheckHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorCheckHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorCheckHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorCheckHistoryResp.Merge(dst, src)
}
func (m *GetAnchorCheckHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorCheckHistoryResp.Size(m)
}
func (m *GetAnchorCheckHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorCheckHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorCheckHistoryResp proto.InternalMessageInfo

func (m *GetAnchorCheckHistoryResp) GetList() []*AnchorCheckData {
	if m != nil {
		return m.List
	}
	return nil
}

type GetLastAnchorCheckUpgradeResp struct {
	ApplyTs              uint32   `protobuf:"varint,1,opt,name=apply_ts,json=applyTs,proto3" json:"apply_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLastAnchorCheckUpgradeResp) Reset()         { *m = GetLastAnchorCheckUpgradeResp{} }
func (m *GetLastAnchorCheckUpgradeResp) String() string { return proto.CompactTextString(m) }
func (*GetLastAnchorCheckUpgradeResp) ProtoMessage()    {}
func (*GetLastAnchorCheckUpgradeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{34}
}
func (m *GetLastAnchorCheckUpgradeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLastAnchorCheckUpgradeResp.Unmarshal(m, b)
}
func (m *GetLastAnchorCheckUpgradeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLastAnchorCheckUpgradeResp.Marshal(b, m, deterministic)
}
func (dst *GetLastAnchorCheckUpgradeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastAnchorCheckUpgradeResp.Merge(dst, src)
}
func (m *GetLastAnchorCheckUpgradeResp) XXX_Size() int {
	return xxx_messageInfo_GetLastAnchorCheckUpgradeResp.Size(m)
}
func (m *GetLastAnchorCheckUpgradeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastAnchorCheckUpgradeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastAnchorCheckUpgradeResp proto.InternalMessageInfo

func (m *GetLastAnchorCheckUpgradeResp) GetApplyTs() uint32 {
	if m != nil {
		return m.ApplyTs
	}
	return 0
}

type ApplyAnchorCheckUpgradeReqResp struct {
	RemainDay            uint32   `protobuf:"varint,1,opt,name=remain_day,json=remainDay,proto3" json:"remain_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyAnchorCheckUpgradeReqResp) Reset()         { *m = ApplyAnchorCheckUpgradeReqResp{} }
func (m *ApplyAnchorCheckUpgradeReqResp) String() string { return proto.CompactTextString(m) }
func (*ApplyAnchorCheckUpgradeReqResp) ProtoMessage()    {}
func (*ApplyAnchorCheckUpgradeReqResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_anchor_check_0bbc57702a386d1c, []int{35}
}
func (m *ApplyAnchorCheckUpgradeReqResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyAnchorCheckUpgradeReqResp.Unmarshal(m, b)
}
func (m *ApplyAnchorCheckUpgradeReqResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyAnchorCheckUpgradeReqResp.Marshal(b, m, deterministic)
}
func (dst *ApplyAnchorCheckUpgradeReqResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyAnchorCheckUpgradeReqResp.Merge(dst, src)
}
func (m *ApplyAnchorCheckUpgradeReqResp) XXX_Size() int {
	return xxx_messageInfo_ApplyAnchorCheckUpgradeReqResp.Size(m)
}
func (m *ApplyAnchorCheckUpgradeReqResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyAnchorCheckUpgradeReqResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyAnchorCheckUpgradeReqResp proto.InternalMessageInfo

func (m *ApplyAnchorCheckUpgradeReqResp) GetRemainDay() uint32 {
	if m != nil {
		return m.RemainDay
	}
	return 0
}

func init() {
	proto.RegisterType((*UidReq)(nil), "anchor_check.UidReq")
	proto.RegisterType((*WhiteData)(nil), "anchor_check.WhiteData")
	proto.RegisterType((*StartRecordReq)(nil), "anchor_check.StartRecordReq")
	proto.RegisterType((*StartRecordResp)(nil), "anchor_check.StartRecordResp")
	proto.RegisterType((*StopRecordReq)(nil), "anchor_check.StopRecordReq")
	proto.RegisterType((*EmptyRes)(nil), "anchor_check.EmptyRes")
	proto.RegisterType((*CheckData)(nil), "anchor_check.CheckData")
	proto.RegisterType((*GetCheckListReq)(nil), "anchor_check.GetCheckListReq")
	proto.RegisterType((*GetCheckListResp)(nil), "anchor_check.GetCheckListResp")
	proto.RegisterType((*SetCheckDataReq)(nil), "anchor_check.SetCheckDataReq")
	proto.RegisterType((*SetWhiteReq)(nil), "anchor_check.SetWhiteReq")
	proto.RegisterType((*GetWhiteExpireUidListReq)(nil), "anchor_check.GetWhiteExpireUidListReq")
	proto.RegisterType((*GetWhiteExpireUidListResp)(nil), "anchor_check.GetWhiteExpireUidListResp")
	proto.RegisterType((*LastCreateScore)(nil), "anchor_check.LastCreateScore")
	proto.RegisterType((*WhiteReqElem)(nil), "anchor_check.WhiteReqElem")
	proto.RegisterType((*WhiteListReq)(nil), "anchor_check.WhiteListReq")
	proto.RegisterType((*AnchorCheckGetCheckListReq)(nil), "anchor_check.AnchorCheckGetCheckListReq")
	proto.RegisterType((*AnchorCheckData)(nil), "anchor_check.AnchorCheckData")
	proto.RegisterType((*TimeRange)(nil), "anchor_check.TimeRange")
	proto.RegisterType((*CheatInfo)(nil), "anchor_check.CheatInfo")
	proto.RegisterType((*AnchorCheckGetCheckListResp)(nil), "anchor_check.AnchorCheckGetCheckListResp")
	proto.RegisterType((*AnchorCheckSetCheckData)(nil), "anchor_check.AnchorCheckSetCheckData")
	proto.RegisterType((*AnchorCheckSetCheckDataReq)(nil), "anchor_check.AnchorCheckSetCheckDataReq")
	proto.RegisterType((*ExportWhiteExpireListReq)(nil), "anchor_check.ExportWhiteExpireListReq")
	proto.RegisterType((*WhiteUserInfo)(nil), "anchor_check.WhiteUserInfo")
	proto.RegisterType((*ExportWhiteExpireListResp)(nil), "anchor_check.ExportWhiteExpireListResp")
	proto.RegisterType((*EmptyMsg)(nil), "anchor_check.EmptyMsg")
	proto.RegisterType((*AnchorScore)(nil), "anchor_check.AnchorScore")
	proto.RegisterType((*LevelData)(nil), "anchor_check.LevelData")
	proto.RegisterType((*AnchorCheckGetWhiteListReq)(nil), "anchor_check.AnchorCheckGetWhiteListReq")
	proto.RegisterType((*WhiteDetailInfo)(nil), "anchor_check.WhiteDetailInfo")
	proto.RegisterType((*AnchorCheckGetWhiteListResp)(nil), "anchor_check.AnchorCheckGetWhiteListResp")
	proto.RegisterType((*GetAnchorCheckHistoryReq)(nil), "anchor_check.GetAnchorCheckHistoryReq")
	proto.RegisterType((*GetAnchorCheckHistoryResp)(nil), "anchor_check.GetAnchorCheckHistoryResp")
	proto.RegisterType((*GetLastAnchorCheckUpgradeResp)(nil), "anchor_check.GetLastAnchorCheckUpgradeResp")
	proto.RegisterType((*ApplyAnchorCheckUpgradeReqResp)(nil), "anchor_check.ApplyAnchorCheckUpgradeReqResp")
	proto.RegisterEnum("anchor_check.COMPARE_TYPE", COMPARE_TYPE_name, COMPARE_TYPE_value)
	proto.RegisterEnum("anchor_check.AudioType", AudioType_name, AudioType_value)
	proto.RegisterEnum("anchor_check.CheckType", CheckType_name, CheckType_value)
	proto.RegisterEnum("anchor_check.AnchorCheckStatusType", AnchorCheckStatusType_name, AnchorCheckStatusType_value)
	proto.RegisterEnum("anchor_check.AnchorCheckSourceType", AnchorCheckSourceType_name, AnchorCheckSourceType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AnchorCheckClient is the client API for AnchorCheck service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AnchorCheckClient interface {
	// 是否是白名单
	CheckInWhite(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*WhiteData, error)
	// 录入白名单
	SetWhite(ctx context.Context, in *SetWhiteReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// 白名单提交音频
	SubmitWhite(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// 开始录制
	StartRecord(ctx context.Context, in *StartRecordReq, opts ...grpc.CallOption) (*StartRecordResp, error)
	// 结束录制
	StopRecord(ctx context.Context, in *StopRecordReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// 获取审核列表
	GetCheckList(ctx context.Context, in *GetCheckListReq, opts ...grpc.CallOption) (*GetCheckListResp, error)
	// 录入考核
	SetCheckData(ctx context.Context, in *SetCheckDataReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// 获取时间范围内白名单提交过期的uid列表
	GetWhiteExpireUidList(ctx context.Context, in *GetWhiteExpireUidListReq, opts ...grpc.CallOption) (*GetWhiteExpireUidListResp, error)
	// 获取最后一次提交考核的分数
	GetLastCreateScore(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*LastCreateScore, error)
	// 主播考核接口/////////////////////////////////////////////////////
	// 上传白名单
	AnchorCheckUploadWhiteList(ctx context.Context, in *WhiteListReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 查询考核列表
	AnchorCheckGetCheckList(ctx context.Context, in *AnchorCheckGetCheckListReq, opts ...grpc.CallOption) (*AnchorCheckGetCheckListResp, error)
	// 录入考核
	AnchorCheckSetCheckData(ctx context.Context, in *AnchorCheckSetCheckDataReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 导出某个时间段白名单过期的用户
	ExportWhiteExpireList(ctx context.Context, in *ExportWhiteExpireListReq, opts ...grpc.CallOption) (*ExportWhiteExpireListResp, error)
	// 获取考核等级
	GetLevel(ctx context.Context, in *AnchorScore, opts ...grpc.CallOption) (*LevelData, error)
	// 查看白名单列表
	AnchorCheckGetWhiteList(ctx context.Context, in *AnchorCheckGetWhiteListReq, opts ...grpc.CallOption) (*AnchorCheckGetWhiteListResp, error)
	// 查看历史考核结果
	GetAnchorCheckHistory(ctx context.Context, in *GetAnchorCheckHistoryReq, opts ...grpc.CallOption) (*GetAnchorCheckHistoryResp, error)
	// 获取最近一次考核升级申请时间
	GetLastAnchorCheckUpgrade(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetLastAnchorCheckUpgradeResp, error)
	// 申请考核升级
	ApplyAnchorCheckUpgrade(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*ApplyAnchorCheckUpgradeReqResp, error)
	ManualRegisterCheckAudio(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*EmptyMsg, error)
	ManualGetAudioLiveCheckResult(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*EmptyMsg, error)
}

type anchorCheckClient struct {
	cc *grpc.ClientConn
}

func NewAnchorCheckClient(cc *grpc.ClientConn) AnchorCheckClient {
	return &anchorCheckClient{cc}
}

func (c *anchorCheckClient) CheckInWhite(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*WhiteData, error) {
	out := new(WhiteData)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/CheckInWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) SetWhite(ctx context.Context, in *SetWhiteReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/SetWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) SubmitWhite(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/SubmitWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) StartRecord(ctx context.Context, in *StartRecordReq, opts ...grpc.CallOption) (*StartRecordResp, error) {
	out := new(StartRecordResp)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/StartRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) StopRecord(ctx context.Context, in *StopRecordReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/StopRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) GetCheckList(ctx context.Context, in *GetCheckListReq, opts ...grpc.CallOption) (*GetCheckListResp, error) {
	out := new(GetCheckListResp)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/GetCheckList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) SetCheckData(ctx context.Context, in *SetCheckDataReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/SetCheckData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) GetWhiteExpireUidList(ctx context.Context, in *GetWhiteExpireUidListReq, opts ...grpc.CallOption) (*GetWhiteExpireUidListResp, error) {
	out := new(GetWhiteExpireUidListResp)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/GetWhiteExpireUidList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) GetLastCreateScore(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*LastCreateScore, error) {
	out := new(LastCreateScore)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/GetLastCreateScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) AnchorCheckUploadWhiteList(ctx context.Context, in *WhiteListReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/AnchorCheckUploadWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) AnchorCheckGetCheckList(ctx context.Context, in *AnchorCheckGetCheckListReq, opts ...grpc.CallOption) (*AnchorCheckGetCheckListResp, error) {
	out := new(AnchorCheckGetCheckListResp)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/AnchorCheckGetCheckList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) AnchorCheckSetCheckData(ctx context.Context, in *AnchorCheckSetCheckDataReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/AnchorCheckSetCheckData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) ExportWhiteExpireList(ctx context.Context, in *ExportWhiteExpireListReq, opts ...grpc.CallOption) (*ExportWhiteExpireListResp, error) {
	out := new(ExportWhiteExpireListResp)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/ExportWhiteExpireList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) GetLevel(ctx context.Context, in *AnchorScore, opts ...grpc.CallOption) (*LevelData, error) {
	out := new(LevelData)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/GetLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) AnchorCheckGetWhiteList(ctx context.Context, in *AnchorCheckGetWhiteListReq, opts ...grpc.CallOption) (*AnchorCheckGetWhiteListResp, error) {
	out := new(AnchorCheckGetWhiteListResp)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/AnchorCheckGetWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) GetAnchorCheckHistory(ctx context.Context, in *GetAnchorCheckHistoryReq, opts ...grpc.CallOption) (*GetAnchorCheckHistoryResp, error) {
	out := new(GetAnchorCheckHistoryResp)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/GetAnchorCheckHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) GetLastAnchorCheckUpgrade(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetLastAnchorCheckUpgradeResp, error) {
	out := new(GetLastAnchorCheckUpgradeResp)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/GetLastAnchorCheckUpgrade", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) ApplyAnchorCheckUpgrade(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*ApplyAnchorCheckUpgradeReqResp, error) {
	out := new(ApplyAnchorCheckUpgradeReqResp)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/ApplyAnchorCheckUpgrade", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) ManualRegisterCheckAudio(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/ManualRegisterCheckAudio", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *anchorCheckClient) ManualGetAudioLiveCheckResult(ctx context.Context, in *EmptyMsg, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/anchor_check.AnchorCheck/ManualGetAudioLiveCheckResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnchorCheckServer is the server API for AnchorCheck service.
type AnchorCheckServer interface {
	// 是否是白名单
	CheckInWhite(context.Context, *UidReq) (*WhiteData, error)
	// 录入白名单
	SetWhite(context.Context, *SetWhiteReq) (*EmptyRes, error)
	// 白名单提交音频
	SubmitWhite(context.Context, *UidReq) (*EmptyRes, error)
	// 开始录制
	StartRecord(context.Context, *StartRecordReq) (*StartRecordResp, error)
	// 结束录制
	StopRecord(context.Context, *StopRecordReq) (*EmptyRes, error)
	// 获取审核列表
	GetCheckList(context.Context, *GetCheckListReq) (*GetCheckListResp, error)
	// 录入考核
	SetCheckData(context.Context, *SetCheckDataReq) (*EmptyRes, error)
	// 获取时间范围内白名单提交过期的uid列表
	GetWhiteExpireUidList(context.Context, *GetWhiteExpireUidListReq) (*GetWhiteExpireUidListResp, error)
	// 获取最后一次提交考核的分数
	GetLastCreateScore(context.Context, *UidReq) (*LastCreateScore, error)
	// 主播考核接口/////////////////////////////////////////////////////
	// 上传白名单
	AnchorCheckUploadWhiteList(context.Context, *WhiteListReq) (*EmptyMsg, error)
	// 查询考核列表
	AnchorCheckGetCheckList(context.Context, *AnchorCheckGetCheckListReq) (*AnchorCheckGetCheckListResp, error)
	// 录入考核
	AnchorCheckSetCheckData(context.Context, *AnchorCheckSetCheckDataReq) (*EmptyMsg, error)
	// 导出某个时间段白名单过期的用户
	ExportWhiteExpireList(context.Context, *ExportWhiteExpireListReq) (*ExportWhiteExpireListResp, error)
	// 获取考核等级
	GetLevel(context.Context, *AnchorScore) (*LevelData, error)
	// 查看白名单列表
	AnchorCheckGetWhiteList(context.Context, *AnchorCheckGetWhiteListReq) (*AnchorCheckGetWhiteListResp, error)
	// 查看历史考核结果
	GetAnchorCheckHistory(context.Context, *GetAnchorCheckHistoryReq) (*GetAnchorCheckHistoryResp, error)
	// 获取最近一次考核升级申请时间
	GetLastAnchorCheckUpgrade(context.Context, *UidReq) (*GetLastAnchorCheckUpgradeResp, error)
	// 申请考核升级
	ApplyAnchorCheckUpgrade(context.Context, *UidReq) (*ApplyAnchorCheckUpgradeReqResp, error)
	ManualRegisterCheckAudio(context.Context, *EmptyMsg) (*EmptyMsg, error)
	ManualGetAudioLiveCheckResult(context.Context, *EmptyMsg) (*EmptyMsg, error)
}

func RegisterAnchorCheckServer(s *grpc.Server, srv AnchorCheckServer) {
	s.RegisterService(&_AnchorCheck_serviceDesc, srv)
}

func _AnchorCheck_CheckInWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).CheckInWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/CheckInWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).CheckInWhite(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_SetWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetWhiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).SetWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/SetWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).SetWhite(ctx, req.(*SetWhiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_SubmitWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).SubmitWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/SubmitWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).SubmitWhite(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_StartRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).StartRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/StartRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).StartRecord(ctx, req.(*StartRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_StopRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).StopRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/StopRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).StopRecord(ctx, req.(*StopRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_GetCheckList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCheckListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).GetCheckList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/GetCheckList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).GetCheckList(ctx, req.(*GetCheckListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_SetCheckData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCheckDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).SetCheckData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/SetCheckData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).SetCheckData(ctx, req.(*SetCheckDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_GetWhiteExpireUidList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhiteExpireUidListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).GetWhiteExpireUidList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/GetWhiteExpireUidList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).GetWhiteExpireUidList(ctx, req.(*GetWhiteExpireUidListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_GetLastCreateScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).GetLastCreateScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/GetLastCreateScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).GetLastCreateScore(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_AnchorCheckUploadWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).AnchorCheckUploadWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/AnchorCheckUploadWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).AnchorCheckUploadWhiteList(ctx, req.(*WhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_AnchorCheckGetCheckList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnchorCheckGetCheckListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).AnchorCheckGetCheckList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/AnchorCheckGetCheckList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).AnchorCheckGetCheckList(ctx, req.(*AnchorCheckGetCheckListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_AnchorCheckSetCheckData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnchorCheckSetCheckDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).AnchorCheckSetCheckData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/AnchorCheckSetCheckData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).AnchorCheckSetCheckData(ctx, req.(*AnchorCheckSetCheckDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_ExportWhiteExpireList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportWhiteExpireListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).ExportWhiteExpireList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/ExportWhiteExpireList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).ExportWhiteExpireList(ctx, req.(*ExportWhiteExpireListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_GetLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnchorScore)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).GetLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/GetLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).GetLevel(ctx, req.(*AnchorScore))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_AnchorCheckGetWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnchorCheckGetWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).AnchorCheckGetWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/AnchorCheckGetWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).AnchorCheckGetWhiteList(ctx, req.(*AnchorCheckGetWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_GetAnchorCheckHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorCheckHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).GetAnchorCheckHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/GetAnchorCheckHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).GetAnchorCheckHistory(ctx, req.(*GetAnchorCheckHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_GetLastAnchorCheckUpgrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).GetLastAnchorCheckUpgrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/GetLastAnchorCheckUpgrade",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).GetLastAnchorCheckUpgrade(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_ApplyAnchorCheckUpgrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).ApplyAnchorCheckUpgrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/ApplyAnchorCheckUpgrade",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).ApplyAnchorCheckUpgrade(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_ManualRegisterCheckAudio_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyMsg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).ManualRegisterCheckAudio(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/ManualRegisterCheckAudio",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).ManualRegisterCheckAudio(ctx, req.(*EmptyMsg))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnchorCheck_ManualGetAudioLiveCheckResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyMsg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnchorCheckServer).ManualGetAudioLiveCheckResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/anchor_check.AnchorCheck/ManualGetAudioLiveCheckResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnchorCheckServer).ManualGetAudioLiveCheckResult(ctx, req.(*EmptyMsg))
	}
	return interceptor(ctx, in, info, handler)
}

var _AnchorCheck_serviceDesc = grpc.ServiceDesc{
	ServiceName: "anchor_check.AnchorCheck",
	HandlerType: (*AnchorCheckServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckInWhite",
			Handler:    _AnchorCheck_CheckInWhite_Handler,
		},
		{
			MethodName: "SetWhite",
			Handler:    _AnchorCheck_SetWhite_Handler,
		},
		{
			MethodName: "SubmitWhite",
			Handler:    _AnchorCheck_SubmitWhite_Handler,
		},
		{
			MethodName: "StartRecord",
			Handler:    _AnchorCheck_StartRecord_Handler,
		},
		{
			MethodName: "StopRecord",
			Handler:    _AnchorCheck_StopRecord_Handler,
		},
		{
			MethodName: "GetCheckList",
			Handler:    _AnchorCheck_GetCheckList_Handler,
		},
		{
			MethodName: "SetCheckData",
			Handler:    _AnchorCheck_SetCheckData_Handler,
		},
		{
			MethodName: "GetWhiteExpireUidList",
			Handler:    _AnchorCheck_GetWhiteExpireUidList_Handler,
		},
		{
			MethodName: "GetLastCreateScore",
			Handler:    _AnchorCheck_GetLastCreateScore_Handler,
		},
		{
			MethodName: "AnchorCheckUploadWhiteList",
			Handler:    _AnchorCheck_AnchorCheckUploadWhiteList_Handler,
		},
		{
			MethodName: "AnchorCheckGetCheckList",
			Handler:    _AnchorCheck_AnchorCheckGetCheckList_Handler,
		},
		{
			MethodName: "AnchorCheckSetCheckData",
			Handler:    _AnchorCheck_AnchorCheckSetCheckData_Handler,
		},
		{
			MethodName: "ExportWhiteExpireList",
			Handler:    _AnchorCheck_ExportWhiteExpireList_Handler,
		},
		{
			MethodName: "GetLevel",
			Handler:    _AnchorCheck_GetLevel_Handler,
		},
		{
			MethodName: "AnchorCheckGetWhiteList",
			Handler:    _AnchorCheck_AnchorCheckGetWhiteList_Handler,
		},
		{
			MethodName: "GetAnchorCheckHistory",
			Handler:    _AnchorCheck_GetAnchorCheckHistory_Handler,
		},
		{
			MethodName: "GetLastAnchorCheckUpgrade",
			Handler:    _AnchorCheck_GetLastAnchorCheckUpgrade_Handler,
		},
		{
			MethodName: "ApplyAnchorCheckUpgrade",
			Handler:    _AnchorCheck_ApplyAnchorCheckUpgrade_Handler,
		},
		{
			MethodName: "ManualRegisterCheckAudio",
			Handler:    _AnchorCheck_ManualRegisterCheckAudio_Handler,
		},
		{
			MethodName: "ManualGetAudioLiveCheckResult",
			Handler:    _AnchorCheck_ManualGetAudioLiveCheckResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "anchor-check/anchor-check.proto",
}

func init() {
	proto.RegisterFile("anchor-check/anchor-check.proto", fileDescriptor_anchor_check_0bbc57702a386d1c)
}

var fileDescriptor_anchor_check_0bbc57702a386d1c = []byte{
	// 2168 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x59, 0x4f, 0x73, 0xdb, 0xc6,
	0x15, 0x27, 0x48, 0x8a, 0x22, 0x9f, 0x44, 0x13, 0x5e, 0x5b, 0x12, 0x45, 0x99, 0xb6, 0x83, 0x69,
	0x13, 0x59, 0x49, 0xa5, 0xc6, 0x69, 0x3a, 0xd3, 0x4e, 0x33, 0xae, 0x2a, 0xb1, 0x32, 0x1b, 0x5a,
	0x56, 0x20, 0xd1, 0x9e, 0x76, 0xa6, 0x83, 0xc2, 0xc0, 0x8a, 0x42, 0x04, 0x02, 0x30, 0xb0, 0x54,
	0xc4, 0x5c, 0x7b, 0xe8, 0xa9, 0x87, 0x4e, 0x3f, 0x42, 0xef, 0xf9, 0x08, 0x3d, 0xf7, 0x9c, 0x0f,
	0xd1, 0xf6, 0x5b, 0xb4, 0xb3, 0x6f, 0x01, 0x10, 0x00, 0x01, 0x4a, 0xad, 0x67, 0x3a, 0x39, 0xf4,
	0x24, 0xee, 0x7b, 0x6f, 0x77, 0xdf, 0xff, 0xf7, 0xc3, 0x0a, 0x1e, 0xe9, 0x8e, 0x71, 0xe1, 0xfa,
	0x3f, 0x30, 0x2e, 0xa8, 0x71, 0xb9, 0x97, 0x5c, 0xec, 0x7a, 0xbe, 0xcb, 0x5c, 0xb2, 0x2a, 0x68,
	0x1a, 0xd2, 0x3a, 0x8f, 0xe8, 0x35, 0xa3, 0x4e, 0x60, 0xb9, 0xce, 0x9e, 0xeb, 0x31, 0xcb, 0x75,
	0x82, 0xe8, 0xaf, 0x10, 0x57, 0x3a, 0x50, 0x1b, 0x5a, 0xa6, 0x4a, 0xdf, 0x12, 0x19, 0x2a, 0x13,
	0xcb, 0x6c, 0x4b, 0x8f, 0xa5, 0xed, 0xa6, 0xca, 0x7f, 0x2a, 0x2f, 0xa0, 0xf1, 0xfa, 0xc2, 0x62,
	0xf4, 0x50, 0x67, 0x3a, 0x79, 0x04, 0x2b, 0xf4, 0xda, 0xb3, 0x7c, 0xaa, 0x31, 0x6b, 0x4c, 0x43,
	0x31, 0x10, 0xa4, 0x33, 0x6b, 0x4c, 0xb9, 0x40, 0xe0, 0x4e, 0x7c, 0x83, 0x6a, 0x6c, 0xea, 0xd1,
	0x76, 0x59, 0x08, 0x08, 0xd2, 0xd9, 0xd4, 0xa3, 0xca, 0x14, 0xee, 0x9c, 0x32, 0xdd, 0x67, 0x2a,
	0x35, 0x5c, 0x3f, 0xff, 0x4a, 0xb2, 0x01, 0xcb, 0xbe, 0xeb, 0x8e, 0x35, 0xcb, 0xc4, 0x03, 0x1a,
	0x6a, 0x8d, 0x2f, 0xfb, 0x26, 0xd9, 0x82, 0x46, 0xc0, 0x7c, 0xaa, 0x23, 0xab, 0x82, 0xac, 0xba,
	0x20, 0xf4, 0xcd, 0xec, 0xd5, 0xd5, 0xb9, 0xab, 0x55, 0x68, 0xa5, 0xae, 0x0e, 0x3c, 0x7e, 0x13,
	0xd3, 0x83, 0x4b, 0x2d, 0xbc, 0xbf, 0xa1, 0xd6, 0xf8, 0xb2, 0x6f, 0x92, 0xf7, 0xa1, 0x35, 0xd6,
	0xaf, 0x35, 0x1f, 0x45, 0x85, 0xb1, 0xc2, 0x96, 0xe6, 0x58, 0xbf, 0x16, 0x07, 0x70, 0x7b, 0x15,
	0x15, 0x9a, 0xa7, 0xcc, 0xf5, 0x6e, 0xb0, 0x26, 0xba, 0xa3, 0x9c, 0xba, 0x63, 0x1d, 0x6a, 0xc1,
	0xe4, 0xcd, 0xd8, 0x62, 0x68, 0x4a, 0x5d, 0x0d, 0x57, 0x0a, 0x40, 0xbd, 0x37, 0xf6, 0xd8, 0x54,
	0xa5, 0x81, 0xf2, 0xb7, 0x32, 0x34, 0x0e, 0x78, 0x10, 0xd1, 0xfd, 0x85, 0xea, 0x86, 0xb7, 0x96,
	0x67, 0xb7, 0x6e, 0x42, 0xfd, 0xdc, 0xb2, 0xa9, 0x36, 0xf1, 0xed, 0xd0, 0x53, 0xcb, 0x7c, 0x3d,
	0xf4, 0x6d, 0xce, 0x32, 0xa6, 0x5a, 0x60, 0xb8, 0x7e, 0xe4, 0xa5, 0x65, 0x63, 0x7a, 0xca, 0x97,
	0x9c, 0x15, 0x5c, 0x87, 0xac, 0x25, 0xc1, 0x0a, 0xae, 0x63, 0xd6, 0xf4, 0xeb, 0x90, 0x55, 0x13,
	0xac, 0xe9, 0xd7, 0x31, 0xeb, 0xd2, 0x08, 0x59, 0xcb, 0x82, 0x75, 0x69, 0x08, 0x16, 0xb7, 0x91,
	0xe9, 0x6c, 0x12, 0xb4, 0xeb, 0xc8, 0x08, 0x57, 0xa4, 0x0b, 0xf0, 0x86, 0x8e, 0x2c, 0x47, 0xb8,
	0xb6, 0x81, 0xbc, 0x06, 0x52, 0x30, 0x8d, 0x36, 0xa1, 0x4e, 0x9d, 0xd0, 0xef, 0x20, 0x4e, 0xa4,
	0x0e, 0x7a, 0x9c, 0x9f, 0xe8, 0xd3, 0xb1, 0xee, 0x5f, 0xb6, 0x57, 0xc2, 0xdc, 0xc0, 0x15, 0x0f,
	0xbf, 0xe1, 0x53, 0x9d, 0x85, 0xa9, 0xb9, 0x2a, 0xc2, 0x2f, 0x48, 0x18, 0xaa, 0xbf, 0x48, 0xd0,
	0x3a, 0xa2, 0x0c, 0xbd, 0x39, 0xb0, 0x02, 0xc6, 0xa3, 0x35, 0x53, 0x8f, 0xfb, 0x73, 0x29, 0x56,
	0x6f, 0x1d, 0x6a, 0xee, 0xf9, 0x79, 0x40, 0x59, 0xe8, 0xd2, 0x70, 0x45, 0xee, 0xc3, 0x92, 0x6d,
	0x45, 0x11, 0x6b, 0xaa, 0x62, 0x11, 0x79, 0xbf, 0x3a, 0xf3, 0x7e, 0xda, 0xbc, 0xa5, 0x45, 0xe6,
	0xd5, 0x52, 0xe6, 0x29, 0x43, 0x90, 0xd3, 0x4a, 0x06, 0x1e, 0xf9, 0x10, 0xaa, 0xb6, 0x15, 0xb0,
	0xb6, 0xf4, 0xb8, 0xb2, 0xbd, 0xf2, 0x74, 0x63, 0x37, 0x59, 0xdc, 0xbb, 0x71, 0x76, 0xa8, 0x28,
	0xc4, 0x55, 0x34, 0xdc, 0x89, 0x13, 0x69, 0x2e, 0x16, 0xca, 0x37, 0x12, 0xb4, 0x4e, 0xc3, 0x73,
	0x51, 0x98, 0xbe, 0x2d, 0xce, 0xa6, 0x64, 0x82, 0x94, 0x8b, 0x13, 0xa4, 0x52, 0x9c, 0x20, 0xd5,
	0xe2, 0x04, 0x59, 0x9a, 0x4b, 0x90, 0x30, 0x9c, 0xb5, 0x64, 0x38, 0x95, 0x6d, 0x58, 0x39, 0xa5,
	0x0c, 0x3b, 0x0f, 0xd7, 0x75, 0x13, 0xea, 0x13, 0xcb, 0xd4, 0x62, 0x37, 0x34, 0xd5, 0xe5, 0x89,
	0x65, 0x72, 0x0f, 0x29, 0x2a, 0xb4, 0x8f, 0x42, 0xc9, 0x1e, 0x36, 0xa2, 0xa1, 0x60, 0xf0, 0x6d,
	0x0f, 0x60, 0xe6, 0xf5, 0xb0, 0x26, 0x13, 0x61, 0x68, 0x43, 0xe4, 0xf6, 0xc8, 0xcc, 0x28, 0x0a,
	0x3f, 0x86, 0xcd, 0x82, 0x33, 0x03, 0x6f, 0x91, 0x2e, 0x7f, 0x96, 0xa0, 0x35, 0xd0, 0x03, 0x76,
	0x80, 0x69, 0x17, 0x1b, 0x1f, 0x7b, 0x53, 0x2a, 0xf6, 0x66, 0xb9, 0xd8, 0x9b, 0x95, 0x62, 0x6f,
	0x56, 0xd3, 0xde, 0xe4, 0xf9, 0x49, 0xaf, 0xa8, 0x8d, 0x5e, 0x6e, 0xa8, 0x62, 0xa1, 0xfc, 0x0c,
	0x56, 0x23, 0x47, 0xf6, 0x6c, 0x3a, 0xce, 0xe9, 0x51, 0x1d, 0xa8, 0xbb, 0x1e, 0xf5, 0x75, 0xe6,
	0xfa, 0x61, 0x93, 0x8a, 0xd7, 0x4a, 0x2f, 0xdc, 0x1d, 0xf9, 0xf4, 0xd3, 0x8c, 0xf9, 0x2b, 0x4f,
	0x3b, 0xe9, 0x8c, 0x4c, 0xde, 0x35, 0x73, 0xcd, 0x1f, 0x2b, 0xd0, 0xd9, 0x47, 0x31, 0x4c, 0xc2,
	0xef, 0x7c, 0x25, 0x92, 0x6d, 0x90, 0xf5, 0x89, 0x69, 0xb9, 0xc2, 0x2a, 0x31, 0x54, 0x44, 0x77,
	0xbb, 0x83, 0x74, 0x54, 0x9f, 0x0f, 0x16, 0xb2, 0x03, 0x77, 0x93, 0x92, 0x22, 0x32, 0xbc, 0xdf,
	0x49, 0x6a, 0x6b, 0x26, 0x2a, 0x22, 0xf4, 0x31, 0xac, 0xd9, 0xd6, 0x15, 0xd5, 0xe6, 0x8e, 0x16,
	0x3d, 0x90, 0x70, 0xe6, 0x7e, 0xfa, 0xf8, 0x4f, 0x60, 0x7d, 0x6e, 0x8b, 0xb8, 0x03, 0xf0, 0x8e,
	0x7b, 0xe9, 0x3d, 0xe2, 0x9e, 0xcc, 0x34, 0x7c, 0x34, 0x37, 0x0d, 0xff, 0x51, 0x87, 0x56, 0x22,
	0x1e, 0xff, 0x9f, 0x2f, 0xef, 0x38, 0x5f, 0x08, 0x81, 0x2a, 0x63, 0x96, 0xd9, 0x6e, 0xe2, 0x36,
	0xfc, 0xcd, 0x69, 0x8e, 0x65, 0x5c, 0xb6, 0xef, 0x08, 0x1a, 0xff, 0xcd, 0xef, 0x66, 0xfa, 0x48,
	0x73, 0xf4, 0x31, 0x6d, 0xb7, 0x84, 0xe7, 0x98, 0x3e, 0x3a, 0xd6, 0x85, 0x5a, 0xa3, 0x89, 0x65,
	0x9b, 0x3c, 0x00, 0xb2, 0x50, 0x0b, 0xd7, 0x7d, 0x93, 0x7c, 0x0f, 0xee, 0x08, 0x56, 0x70, 0xe1,
	0xfa, 0x8c, 0x0b, 0xdc, 0x45, 0x81, 0x55, 0xa4, 0x9e, 0x72, 0x62, 0x1f, 0xb3, 0x5d, 0x48, 0xe1,
	0xe9, 0x04, 0x4f, 0x6f, 0x20, 0x05, 0xcf, 0xef, 0x02, 0x98, 0x56, 0xe0, 0xd9, 0xfa, 0x94, 0x1f,
	0x70, 0x4f, 0x78, 0x25, 0xa4, 0xf4, 0xd3, 0x5d, 0xe0, 0x7e, 0xba, 0x0b, 0x70, 0xf3, 0x27, 0x9e,
	0x19, 0x9b, 0xbf, 0x26, 0xcc, 0x17, 0x24, 0x34, 0x3f, 0x6e, 0x3d, 0xeb, 0x89, 0xd6, 0x43, 0x9e,
	0x80, 0x7c, 0xe5, 0x1a, 0x16, 0xd5, 0x3c, 0xea, 0x1b, 0xd4, 0x61, 0xfa, 0x88, 0xb6, 0x37, 0x44,
	0x65, 0x20, 0xfd, 0x24, 0x26, 0x93, 0x57, 0xb0, 0x95, 0x15, 0xc5, 0xbb, 0x34, 0x5f, 0x77, 0x46,
	0xb4, 0xdd, 0xce, 0x1b, 0x7e, 0xfc, 0x66, 0x95, 0xb3, 0xd5, 0x76, 0xe6, 0xb8, 0x98, 0x93, 0x5f,
	0x9d, 0x9b, 0xf9, 0xd5, 0x59, 0x5c, 0x6a, 0x9d, 0xe2, 0x52, 0xeb, 0x02, 0x24, 0xea, 0x78, 0x4b,
	0x78, 0xd5, 0x88, 0xcb, 0x77, 0x3f, 0xdd, 0x47, 0xb0, 0x6f, 0x3e, 0x28, 0x98, 0xe4, 0x3a, 0xeb,
	0x3b, 0xe7, 0x6e, 0xb2, 0xc1, 0xf0, 0xfe, 0x48, 0x7e, 0x95, 0xd3, 0x34, 0xf0, 0x9c, 0xee, 0xe2,
	0x73, 0x32, 0xdd, 0x04, 0xcf, 0xfa, 0x00, 0x5a, 0x01, 0x75, 0x18, 0x75, 0x0c, 0xaa, 0xd9, 0xd4,
	0x19, 0xb1, 0x8b, 0xf6, 0x43, 0xd1, 0xd5, 0x22, 0xf2, 0x00, 0xa9, 0x37, 0x76, 0x10, 0x8e, 0x91,
	0x8d, 0x0b, 0xdd, 0x71, 0xa8, 0xad, 0x5d, 0x59, 0xf4, 0x2b, 0x9e, 0x52, 0x8f, 0x31, 0xf6, 0xcd,
	0x90, 0xfc, 0xca, 0xa2, 0x5f, 0xf5, 0x4d, 0xe5, 0x33, 0x68, 0xcc, 0xa2, 0xb1, 0x09, 0xf5, 0xb0,
	0x30, 0x83, 0x68, 0x1a, 0x8a, 0xb2, 0x0c, 0xc8, 0x1a, 0xd4, 0xb0, 0x28, 0x83, 0x08, 0xba, 0xf0,
	0x92, 0x0c, 0x94, 0x7f, 0x4a, 0x08, 0x81, 0x85, 0x49, 0xdc, 0xd9, 0x4c, 0xf7, 0x47, 0x94, 0x69,
	0xb3, 0x11, 0xd6, 0x10, 0x94, 0xa1, 0x65, 0xf2, 0x2c, 0x9c, 0x8d, 0x53, 0x49, 0x15, 0x8b, 0x45,
	0xcd, 0x2a, 0xd1, 0xf2, 0xaa, 0xa9, 0x96, 0x97, 0x2e, 0xa5, 0xa5, 0x6c, 0x29, 0xcd, 0xd7, 0x63,
	0x2d, 0xa7, 0x1e, 0x93, 0x05, 0xbd, 0x9c, 0x2e, 0xe8, 0xa8, 0x5d, 0xd4, 0x67, 0xed, 0x42, 0x39,
	0x87, 0xad, 0xc2, 0x11, 0x19, 0x78, 0xe4, 0xe3, 0x14, 0x0e, 0xec, 0xa6, 0xa3, 0x9e, 0xe9, 0xe5,
	0x0b, 0xd1, 0xe0, 0xb7, 0x12, 0x6c, 0x24, 0xe4, 0x93, 0xc0, 0xf0, 0x3b, 0x8d, 0x0a, 0x53, 0x1d,
	0x6a, 0x39, 0x83, 0x53, 0x5e, 0xa7, 0xf0, 0x45, 0x16, 0xec, 0xfe, 0x24, 0xe5, 0xbb, 0xef, 0x17,
	0xfa, 0x2e, 0xb5, 0x0f, 0xb7, 0x70, 0x80, 0xd9, 0xbb, 0xf6, 0x5c, 0x3f, 0x89, 0x07, 0xdf, 0x15,
	0x60, 0xf6, 0xa1, 0x89, 0xa7, 0x0d, 0x03, 0xea, 0x63, 0x5e, 0xcf, 0x63, 0xb2, 0x28, 0x41, 0xca,
	0x39, 0xf3, 0xa4, 0x32, 0x9b, 0x27, 0xca, 0x00, 0x36, 0x0b, 0xd4, 0x0b, 0x3c, 0xb2, 0x97, 0x32,
	0x7b, 0x2b, 0x07, 0xa8, 0x45, 0x1a, 0x84, 0xc6, 0x46, 0x1f, 0x9f, 0x2f, 0x82, 0x91, 0x32, 0x85,
	0x15, 0xe1, 0x99, 0xff, 0x39, 0x90, 0x55, 0xde, 0x83, 0xc6, 0x80, 0x0f, 0x10, 0x4c, 0xc9, 0x78,
	0xb4, 0x48, 0x49, 0x54, 0xfb, 0x8d, 0x94, 0x05, 0x94, 0x29, 0x98, 0x3a, 0x03, 0x8e, 0x52, 0x3e,
	0x70, 0x2c, 0xe7, 0x00, 0xc7, 0x4a, 0x11, 0x70, 0xac, 0x2e, 0x42, 0x10, 0x4b, 0x73, 0x08, 0x22,
	0xc4, 0x24, 0xb5, 0x24, 0x94, 0x55, 0x7e, 0x5f, 0x81, 0x96, 0x78, 0x4a, 0xa1, 0x4c, 0xb7, 0xec,
	0x77, 0x0b, 0x7b, 0x0a, 0x46, 0x54, 0x8b, 0x61, 0xc4, 0xd2, 0x4d, 0x30, 0xa2, 0x76, 0x23, 0x8c,
	0x58, 0x5e, 0x0c, 0x23, 0xea, 0x59, 0x18, 0x31, 0xb3, 0xbf, 0x91, 0xc2, 0x64, 0x19, 0x04, 0x05,
	0x73, 0x08, 0x2a, 0x59, 0xdd, 0x2b, 0xf3, 0xf8, 0x23, 0x39, 0x8d, 0x56, 0x6f, 0x33, 0x8d, 0x9a,
	0x79, 0xd3, 0x68, 0xae, 0xc7, 0x26, 0xb2, 0xe6, 0xa6, 0x1e, 0x9b, 0x89, 0xde, 0xc2, 0x1e, 0xfb,
	0x11, 0x7e, 0x96, 0x26, 0xae, 0x7a, 0x6e, 0x05, 0xcc, 0xf5, 0xa7, 0xf9, 0xaf, 0x6c, 0xc7, 0xf8,
	0xc1, 0x99, 0x27, 0xfd, 0x5f, 0xf5, 0x7d, 0xe5, 0xa7, 0xd0, 0x3d, 0xa2, 0x8c, 0x7f, 0x8a, 0x26,
	0xf8, 0x43, 0x6f, 0xe4, 0xeb, 0x26, 0x8d, 0x3e, 0x62, 0x75, 0xcf, 0xb3, 0xa7, 0x89, 0x39, 0x8c,
	0xeb, 0xb3, 0x40, 0x79, 0x06, 0x0f, 0xf7, 0xf9, 0xcf, 0xbc, 0x9d, 0x6f, 0x71, 0x73, 0x17, 0x80,
	0x37, 0x64, 0xcb, 0xd1, 0x4c, 0x7d, 0x1a, 0xb5, 0x3d, 0x41, 0x39, 0xd4, 0xa7, 0x3b, 0x7f, 0x92,
	0x60, 0xf5, 0xe0, 0xe5, 0x8b, 0x93, 0x7d, 0xb5, 0xa7, 0x9d, 0xfd, 0xfa, 0xa4, 0x47, 0xda, 0x70,
	0x3f, 0xb9, 0xd6, 0xfa, 0xc7, 0xaf, 0xf6, 0x07, 0xfd, 0x43, 0xb9, 0x44, 0xee, 0x41, 0x2b, 0xc5,
	0x39, 0x3a, 0x93, 0xa5, 0x79, 0x62, 0x4f, 0x2e, 0xcf, 0x11, 0x07, 0x67, 0x72, 0x65, 0x9e, 0xd8,
	0x93, 0xab, 0x73, 0xc4, 0xde, 0x17, 0xf2, 0xd2, 0xce, 0x8f, 0xa0, 0x81, 0x40, 0x08, 0x73, 0xe5,
	0x1e, 0xb4, 0xe2, 0x85, 0x86, 0x06, 0xca, 0x25, 0x42, 0xe0, 0xce, 0x8c, 0x38, 0xb0, 0xae, 0xa8,
	0x2c, 0xed, 0x0c, 0xc2, 0xd7, 0x37, 0xdc, 0xb5, 0x06, 0x77, 0xe3, 0x85, 0xd6, 0x77, 0xae, 0x74,
	0xdb, 0x32, 0xc5, 0xbe, 0x19, 0x79, 0x7f, 0xc2, 0x5c, 0x59, 0x22, 0xf7, 0x41, 0x9e, 0xd1, 0x5e,
	0xe8, 0xce, 0x44, 0xb7, 0xe5, 0xf2, 0xce, 0x5f, 0x25, 0x58, 0x4b, 0x8e, 0x1a, 0x2c, 0x0b, 0x3c,
	0xfa, 0x61, 0x7a, 0x76, 0xc5, 0x0c, 0xad, 0xef, 0x58, 0x4c, 0x2e, 0x91, 0xc7, 0xf0, 0x20, 0x9f,
	0x7f, 0x8a, 0x4f, 0x86, 0xb2, 0x54, 0x2c, 0x71, 0xa0, 0x3b, 0x06, 0xb5, 0xe5, 0x72, 0xf1, 0x1d,
	0x27, 0x7a, 0x10, 0xc8, 0x15, 0xf2, 0x1e, 0x74, 0xf3, 0xf9, 0xc7, 0x2e, 0x43, 0x91, 0xea, 0xce,
	0x1f, 0x32, 0x06, 0xcc, 0xaa, 0x6f, 0x0b, 0x36, 0x7a, 0xda, 0xc1, 0xf3, 0xde, 0xc1, 0xe7, 0xda,
	0xe9, 0xcb, 0xa1, 0x7a, 0xd0, 0xd3, 0x0e, 0x7b, 0xbf, 0x1c, 0xec, 0x0f, 0xcf, 0x7a, 0x72, 0x29,
	0x87, 0x79, 0xdc, 0x7b, 0xad, 0x9d, 0xf6, 0x8f, 0x8e, 0x65, 0x89, 0xe7, 0x46, 0x86, 0xf9, 0xfa,
	0x79, 0xff, 0x8c, 0x47, 0xbc, 0x03, 0xeb, 0x19, 0xce, 0xf0, 0xe4, 0x48, 0xdd, 0x3f, 0xec, 0xc9,
	0x95, 0xa7, 0x7f, 0x6f, 0x46, 0xb3, 0x09, 0x35, 0x21, 0xcf, 0x60, 0x15, 0x7f, 0xf4, 0x1d, 0xac,
	0x51, 0x72, 0x3f, 0x5d, 0x24, 0xe2, 0x75, 0xbb, 0xb3, 0x91, 0x57, 0xce, 0x3a, 0xd3, 0x95, 0x12,
	0x79, 0x06, 0xf5, 0xe8, 0xbd, 0x89, 0x6c, 0xa6, 0xc5, 0x12, 0xef, 0x50, 0x9d, 0xf5, 0x34, 0x2b,
	0x7e, 0xa7, 0x2d, 0x91, 0xcf, 0x60, 0x45, 0x04, 0x63, 0x91, 0x02, 0xc5, 0xdb, 0x07, 0xb0, 0x92,
	0x78, 0x9c, 0x26, 0x0f, 0x32, 0x2a, 0xa4, 0x9e, 0xcc, 0x3b, 0xdd, 0x05, 0xdc, 0xc0, 0x53, 0x4a,
	0xe4, 0x00, 0x60, 0xf6, 0x2c, 0x4d, 0xb6, 0xb2, 0xe2, 0x89, 0x07, 0xeb, 0x05, 0x2a, 0xbd, 0x84,
	0xd5, 0x24, 0x04, 0x25, 0x99, 0x5b, 0x33, 0x2f, 0x38, 0x9d, 0x87, 0x8b, 0xd8, 0xa8, 0xd5, 0x11,
	0xac, 0xa6, 0xa0, 0x66, 0x77, 0xce, 0xcf, 0x49, 0xc8, 0xb6, 0x40, 0xb3, 0x2f, 0x61, 0x2d, 0xf7,
	0x79, 0x8e, 0xbc, 0x3f, 0xa7, 0x43, 0xee, 0xbb, 0x60, 0xe7, 0x83, 0x5b, 0xc9, 0xa1, 0xd2, 0x9f,
	0x03, 0x09, 0x3b, 0x69, 0xf2, 0x51, 0x2f, 0x3f, 0xbc, 0x19, 0x83, 0x32, 0x9b, 0x94, 0x12, 0x51,
	0x53, 0x35, 0x38, 0xf4, 0x6c, 0x57, 0x37, 0xe3, 0xf9, 0x43, 0xf2, 0xde, 0xd1, 0x22, 0x8d, 0xf3,
	0x9c, 0xc1, 0x31, 0x5a, 0x89, 0x78, 0x29, 0x2c, 0x9f, 0x8a, 0xd8, 0x76, 0xe1, 0xa8, 0xc8, 0x06,
	0xef, 0xc9, 0x2d, 0x25, 0xd1, 0x25, 0xbf, 0x2d, 0xfe, 0x7a, 0xd8, 0xbe, 0x1d, 0xb0, 0x5e, 0x68,
	0xd0, 0x97, 0xb0, 0x96, 0x0b, 0x68, 0xb3, 0xd1, 0x2d, 0x02, 0xe5, 0xd9, 0xe8, 0x16, 0xa2, 0x63,
	0xa5, 0x44, 0x7e, 0x0e, 0x75, 0x1e, 0x5d, 0x7c, 0xab, 0xd8, 0xcc, 0xd3, 0x1d, 0x23, 0x97, 0x6d,
	0x1c, 0x31, 0x34, 0xcd, 0x73, 0xff, 0x2c, 0x9e, 0x0b, 0xdd, 0x9f, 0x8a, 0xee, 0x93, 0x5b, 0x4a,
	0xa2, 0xce, 0x22, 0xfb, 0xe7, 0xb1, 0x42, 0x4e, 0xf6, 0xe7, 0xc2, 0x8f, 0x9c, 0xec, 0xcf, 0x07,
	0x1e, 0x4a, 0x89, 0xfc, 0x0e, 0x71, 0x49, 0x3e, 0x8e, 0x28, 0x28, 0x82, 0x0f, 0xe7, 0x4e, 0x2f,
	0x86, 0x21, 0x4a, 0x89, 0x68, 0xb0, 0x51, 0x80, 0x36, 0x0a, 0xce, 0xff, 0x28, 0xe3, 0xab, 0x85,
	0x50, 0x05, 0x3b, 0x6b, 0x5b, 0x4c, 0x60, 0x95, 0x8e, 0xac, 0x80, 0x51, 0x21, 0x86, 0xa3, 0x9e,
	0x14, 0x24, 0xe1, 0x82, 0xe4, 0xfc, 0x02, 0xba, 0xe2, 0x34, 0xee, 0x35, 0x7e, 0x06, 0x47, 0x0a,
	0x78, 0xa2, 0x4a, 0x83, 0x89, 0xcd, 0xfe, 0xf3, 0x23, 0x3b, 0x5b, 0xdf, 0xfe, 0xeb, 0x51, 0x32,
	0x87, 0x92, 0xe1, 0xff, 0xc5, 0x0f, 0x7f, 0xb3, 0x3b, 0x72, 0x6d, 0xdd, 0x19, 0xed, 0x7e, 0xfa,
	0x94, 0xb1, 0x5d, 0xc3, 0x1d, 0xef, 0xe1, 0xff, 0x6c, 0x0d, 0xd7, 0xde, 0x0b, 0xa8, 0x7f, 0x65,
	0x19, 0x34, 0x48, 0xfd, 0x07, 0xf8, 0x4d, 0x0d, 0xf9, 0x9f, 0xfc, 0x3b, 0x00, 0x00, 0xff, 0xff,
	0x94, 0xa6, 0x02, 0x06, 0x25, 0x1e, 0x00, 0x00,
}
