// Code generated by protoc-gen-go. DO NOT EDIT.
// source: perfect-match/perfect-match.proto

package perfect_match // import "golang.52tt.com/protocol/services/perfect-match"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import perfect_match "golang.52tt.com/protocol/services/rcmd/perfect_match"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetPerfectMatchEntryResp_Phase int32

const (
	GetPerfectMatchEntryResp_PHASE_UNSPECIFIED     GetPerfectMatchEntryResp_Phase = 0
	GetPerfectMatchEntryResp_PHASE_ANSWER_QUESTION GetPerfectMatchEntryResp_Phase = 1
	GetPerfectMatchEntryResp_PHASE_IN_MATCH        GetPerfectMatchEntryResp_Phase = 2
	GetPerfectMatchEntryResp_PHASE_MATCHED         GetPerfectMatchEntryResp_Phase = 3
)

var GetPerfectMatchEntryResp_Phase_name = map[int32]string{
	0: "PHASE_UNSPECIFIED",
	1: "PHASE_ANSWER_QUESTION",
	2: "PHASE_IN_MATCH",
	3: "PHASE_MATCHED",
}
var GetPerfectMatchEntryResp_Phase_value = map[string]int32{
	"PHASE_UNSPECIFIED":     0,
	"PHASE_ANSWER_QUESTION": 1,
	"PHASE_IN_MATCH":        2,
	"PHASE_MATCHED":         3,
}

func (x GetPerfectMatchEntryResp_Phase) String() string {
	return proto.EnumName(GetPerfectMatchEntryResp_Phase_name, int32(x))
}
func (GetPerfectMatchEntryResp_Phase) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{1, 0}
}

// 获取天配匹配入口请求
type GetPerfectMatchEntryReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPerfectMatchEntryReq) Reset()         { *m = GetPerfectMatchEntryReq{} }
func (m *GetPerfectMatchEntryReq) String() string { return proto.CompactTextString(m) }
func (*GetPerfectMatchEntryReq) ProtoMessage()    {}
func (*GetPerfectMatchEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{0}
}
func (m *GetPerfectMatchEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPerfectMatchEntryReq.Unmarshal(m, b)
}
func (m *GetPerfectMatchEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPerfectMatchEntryReq.Marshal(b, m, deterministic)
}
func (dst *GetPerfectMatchEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPerfectMatchEntryReq.Merge(dst, src)
}
func (m *GetPerfectMatchEntryReq) XXX_Size() int {
	return xxx_messageInfo_GetPerfectMatchEntryReq.Size(m)
}
func (m *GetPerfectMatchEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPerfectMatchEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPerfectMatchEntryReq proto.InternalMessageInfo

// 获取匹配入口响应
type GetPerfectMatchEntryResp struct {
	IsOpen                bool                      `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	CostTbean             uint32                    `protobuf:"varint,2,opt,name=cost_tbean,json=costTbean,proto3" json:"cost_tbean,omitempty"`
	GiftName              string                    `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftIcon              string                    `protobuf:"bytes,4,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	Phase                 uint32                    `protobuf:"varint,5,opt,name=phase,proto3" json:"phase,omitempty"`
	ExpectWait            string                    `protobuf:"bytes,6,opt,name=expect_wait,json=expectWait,proto3" json:"expect_wait,omitempty"`
	MatchStart            uint32                    `protobuf:"varint,7,opt,name=match_start,json=matchStart,proto3" json:"match_start,omitempty"`
	ChannelId             uint32                    `protobuf:"varint,8,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicId                 uint32                    `protobuf:"varint,9,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Questions             []*perfect_match.Question `protobuf:"bytes,10,rep,name=questions,proto3" json:"questions,omitempty"`
	RemainCancelEnroll    uint32                    `protobuf:"varint,11,opt,name=remain_cancel_enroll,json=remainCancelEnroll,proto3" json:"remain_cancel_enroll,omitempty"`
	RemainRefreshQuestion uint32                    `protobuf:"varint,12,opt,name=remain_refresh_question,json=remainRefreshQuestion,proto3" json:"remain_refresh_question,omitempty"`
	HeartbeatInterval     uint32                    `protobuf:"varint,13,opt,name=heartbeat_interval,json=heartbeatInterval,proto3" json:"heartbeat_interval,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                  `json:"-"`
	XXX_unrecognized      []byte                    `json:"-"`
	XXX_sizecache         int32                     `json:"-"`
}

func (m *GetPerfectMatchEntryResp) Reset()         { *m = GetPerfectMatchEntryResp{} }
func (m *GetPerfectMatchEntryResp) String() string { return proto.CompactTextString(m) }
func (*GetPerfectMatchEntryResp) ProtoMessage()    {}
func (*GetPerfectMatchEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{1}
}
func (m *GetPerfectMatchEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPerfectMatchEntryResp.Unmarshal(m, b)
}
func (m *GetPerfectMatchEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPerfectMatchEntryResp.Marshal(b, m, deterministic)
}
func (dst *GetPerfectMatchEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPerfectMatchEntryResp.Merge(dst, src)
}
func (m *GetPerfectMatchEntryResp) XXX_Size() int {
	return xxx_messageInfo_GetPerfectMatchEntryResp.Size(m)
}
func (m *GetPerfectMatchEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPerfectMatchEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPerfectMatchEntryResp proto.InternalMessageInfo

func (m *GetPerfectMatchEntryResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *GetPerfectMatchEntryResp) GetCostTbean() uint32 {
	if m != nil {
		return m.CostTbean
	}
	return 0
}

func (m *GetPerfectMatchEntryResp) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *GetPerfectMatchEntryResp) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *GetPerfectMatchEntryResp) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *GetPerfectMatchEntryResp) GetExpectWait() string {
	if m != nil {
		return m.ExpectWait
	}
	return ""
}

func (m *GetPerfectMatchEntryResp) GetMatchStart() uint32 {
	if m != nil {
		return m.MatchStart
	}
	return 0
}

func (m *GetPerfectMatchEntryResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPerfectMatchEntryResp) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *GetPerfectMatchEntryResp) GetQuestions() []*perfect_match.Question {
	if m != nil {
		return m.Questions
	}
	return nil
}

func (m *GetPerfectMatchEntryResp) GetRemainCancelEnroll() uint32 {
	if m != nil {
		return m.RemainCancelEnroll
	}
	return 0
}

func (m *GetPerfectMatchEntryResp) GetRemainRefreshQuestion() uint32 {
	if m != nil {
		return m.RemainRefreshQuestion
	}
	return 0
}

func (m *GetPerfectMatchEntryResp) GetHeartbeatInterval() uint32 {
	if m != nil {
		return m.HeartbeatInterval
	}
	return 0
}

// 匹配报名请求
type EnrollPerfectMatchReq struct {
	Sex                  uint32   `protobuf:"varint,1,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnrollPerfectMatchReq) Reset()         { *m = EnrollPerfectMatchReq{} }
func (m *EnrollPerfectMatchReq) String() string { return proto.CompactTextString(m) }
func (*EnrollPerfectMatchReq) ProtoMessage()    {}
func (*EnrollPerfectMatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{2}
}
func (m *EnrollPerfectMatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnrollPerfectMatchReq.Unmarshal(m, b)
}
func (m *EnrollPerfectMatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnrollPerfectMatchReq.Marshal(b, m, deterministic)
}
func (dst *EnrollPerfectMatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnrollPerfectMatchReq.Merge(dst, src)
}
func (m *EnrollPerfectMatchReq) XXX_Size() int {
	return xxx_messageInfo_EnrollPerfectMatchReq.Size(m)
}
func (m *EnrollPerfectMatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EnrollPerfectMatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_EnrollPerfectMatchReq proto.InternalMessageInfo

func (m *EnrollPerfectMatchReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 匹配报名响应
type EnrollPerfectMatchResp struct {
	RemainCancelEnroll    uint32   `protobuf:"varint,1,opt,name=remain_cancel_enroll,json=remainCancelEnroll,proto3" json:"remain_cancel_enroll,omitempty"`
	RemainRefreshQuestion uint32   `protobuf:"varint,2,opt,name=remain_refresh_question,json=remainRefreshQuestion,proto3" json:"remain_refresh_question,omitempty"`
	HeartbeatInterval     uint32   `protobuf:"varint,3,opt,name=heartbeat_interval,json=heartbeatInterval,proto3" json:"heartbeat_interval,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *EnrollPerfectMatchResp) Reset()         { *m = EnrollPerfectMatchResp{} }
func (m *EnrollPerfectMatchResp) String() string { return proto.CompactTextString(m) }
func (*EnrollPerfectMatchResp) ProtoMessage()    {}
func (*EnrollPerfectMatchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{3}
}
func (m *EnrollPerfectMatchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnrollPerfectMatchResp.Unmarshal(m, b)
}
func (m *EnrollPerfectMatchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnrollPerfectMatchResp.Marshal(b, m, deterministic)
}
func (dst *EnrollPerfectMatchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnrollPerfectMatchResp.Merge(dst, src)
}
func (m *EnrollPerfectMatchResp) XXX_Size() int {
	return xxx_messageInfo_EnrollPerfectMatchResp.Size(m)
}
func (m *EnrollPerfectMatchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EnrollPerfectMatchResp.DiscardUnknown(m)
}

var xxx_messageInfo_EnrollPerfectMatchResp proto.InternalMessageInfo

func (m *EnrollPerfectMatchResp) GetRemainCancelEnroll() uint32 {
	if m != nil {
		return m.RemainCancelEnroll
	}
	return 0
}

func (m *EnrollPerfectMatchResp) GetRemainRefreshQuestion() uint32 {
	if m != nil {
		return m.RemainRefreshQuestion
	}
	return 0
}

func (m *EnrollPerfectMatchResp) GetHeartbeatInterval() uint32 {
	if m != nil {
		return m.HeartbeatInterval
	}
	return 0
}

// 获取答案请求
type GetPerfectMatchQuestionsReq struct {
	Refresh              bool     `protobuf:"varint,1,opt,name=refresh,proto3" json:"refresh,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPerfectMatchQuestionsReq) Reset()         { *m = GetPerfectMatchQuestionsReq{} }
func (m *GetPerfectMatchQuestionsReq) String() string { return proto.CompactTextString(m) }
func (*GetPerfectMatchQuestionsReq) ProtoMessage()    {}
func (*GetPerfectMatchQuestionsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{4}
}
func (m *GetPerfectMatchQuestionsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPerfectMatchQuestionsReq.Unmarshal(m, b)
}
func (m *GetPerfectMatchQuestionsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPerfectMatchQuestionsReq.Marshal(b, m, deterministic)
}
func (dst *GetPerfectMatchQuestionsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPerfectMatchQuestionsReq.Merge(dst, src)
}
func (m *GetPerfectMatchQuestionsReq) XXX_Size() int {
	return xxx_messageInfo_GetPerfectMatchQuestionsReq.Size(m)
}
func (m *GetPerfectMatchQuestionsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPerfectMatchQuestionsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPerfectMatchQuestionsReq proto.InternalMessageInfo

func (m *GetPerfectMatchQuestionsReq) GetRefresh() bool {
	if m != nil {
		return m.Refresh
	}
	return false
}

// 获取答案响应
type GetPerfectMatchQuestionsResp struct {
	Questions             []*perfect_match.Question `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
	RemainRefreshQuestion uint32                    `protobuf:"varint,2,opt,name=remain_refresh_question,json=remainRefreshQuestion,proto3" json:"remain_refresh_question,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                  `json:"-"`
	XXX_unrecognized      []byte                    `json:"-"`
	XXX_sizecache         int32                     `json:"-"`
}

func (m *GetPerfectMatchQuestionsResp) Reset()         { *m = GetPerfectMatchQuestionsResp{} }
func (m *GetPerfectMatchQuestionsResp) String() string { return proto.CompactTextString(m) }
func (*GetPerfectMatchQuestionsResp) ProtoMessage()    {}
func (*GetPerfectMatchQuestionsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{5}
}
func (m *GetPerfectMatchQuestionsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPerfectMatchQuestionsResp.Unmarshal(m, b)
}
func (m *GetPerfectMatchQuestionsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPerfectMatchQuestionsResp.Marshal(b, m, deterministic)
}
func (dst *GetPerfectMatchQuestionsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPerfectMatchQuestionsResp.Merge(dst, src)
}
func (m *GetPerfectMatchQuestionsResp) XXX_Size() int {
	return xxx_messageInfo_GetPerfectMatchQuestionsResp.Size(m)
}
func (m *GetPerfectMatchQuestionsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPerfectMatchQuestionsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPerfectMatchQuestionsResp proto.InternalMessageInfo

func (m *GetPerfectMatchQuestionsResp) GetQuestions() []*perfect_match.Question {
	if m != nil {
		return m.Questions
	}
	return nil
}

func (m *GetPerfectMatchQuestionsResp) GetRemainRefreshQuestion() uint32 {
	if m != nil {
		return m.RemainRefreshQuestion
	}
	return 0
}

// 匹配心跳请求
type SendPerfectMatchHeartbeatReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendPerfectMatchHeartbeatReq) Reset()         { *m = SendPerfectMatchHeartbeatReq{} }
func (m *SendPerfectMatchHeartbeatReq) String() string { return proto.CompactTextString(m) }
func (*SendPerfectMatchHeartbeatReq) ProtoMessage()    {}
func (*SendPerfectMatchHeartbeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{6}
}
func (m *SendPerfectMatchHeartbeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPerfectMatchHeartbeatReq.Unmarshal(m, b)
}
func (m *SendPerfectMatchHeartbeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPerfectMatchHeartbeatReq.Marshal(b, m, deterministic)
}
func (dst *SendPerfectMatchHeartbeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPerfectMatchHeartbeatReq.Merge(dst, src)
}
func (m *SendPerfectMatchHeartbeatReq) XXX_Size() int {
	return xxx_messageInfo_SendPerfectMatchHeartbeatReq.Size(m)
}
func (m *SendPerfectMatchHeartbeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPerfectMatchHeartbeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendPerfectMatchHeartbeatReq proto.InternalMessageInfo

// 匹配心跳响应
type SendPerfectMatchHeartbeatResp struct {
	HeartbeatInterval    uint32   `protobuf:"varint,1,opt,name=heartbeat_interval,json=heartbeatInterval,proto3" json:"heartbeat_interval,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendPerfectMatchHeartbeatResp) Reset()         { *m = SendPerfectMatchHeartbeatResp{} }
func (m *SendPerfectMatchHeartbeatResp) String() string { return proto.CompactTextString(m) }
func (*SendPerfectMatchHeartbeatResp) ProtoMessage()    {}
func (*SendPerfectMatchHeartbeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{7}
}
func (m *SendPerfectMatchHeartbeatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPerfectMatchHeartbeatResp.Unmarshal(m, b)
}
func (m *SendPerfectMatchHeartbeatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPerfectMatchHeartbeatResp.Marshal(b, m, deterministic)
}
func (dst *SendPerfectMatchHeartbeatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPerfectMatchHeartbeatResp.Merge(dst, src)
}
func (m *SendPerfectMatchHeartbeatResp) XXX_Size() int {
	return xxx_messageInfo_SendPerfectMatchHeartbeatResp.Size(m)
}
func (m *SendPerfectMatchHeartbeatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPerfectMatchHeartbeatResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendPerfectMatchHeartbeatResp proto.InternalMessageInfo

func (m *SendPerfectMatchHeartbeatResp) GetHeartbeatInterval() uint32 {
	if m != nil {
		return m.HeartbeatInterval
	}
	return 0
}

// 发送答案请求
type SendPerfectMatchAnswerReq struct {
	MapAnswer            map[string]string `protobuf:"bytes,1,rep,name=map_answer,json=mapAnswer,proto3" json:"map_answer,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SendPerfectMatchAnswerReq) Reset()         { *m = SendPerfectMatchAnswerReq{} }
func (m *SendPerfectMatchAnswerReq) String() string { return proto.CompactTextString(m) }
func (*SendPerfectMatchAnswerReq) ProtoMessage()    {}
func (*SendPerfectMatchAnswerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{8}
}
func (m *SendPerfectMatchAnswerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPerfectMatchAnswerReq.Unmarshal(m, b)
}
func (m *SendPerfectMatchAnswerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPerfectMatchAnswerReq.Marshal(b, m, deterministic)
}
func (dst *SendPerfectMatchAnswerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPerfectMatchAnswerReq.Merge(dst, src)
}
func (m *SendPerfectMatchAnswerReq) XXX_Size() int {
	return xxx_messageInfo_SendPerfectMatchAnswerReq.Size(m)
}
func (m *SendPerfectMatchAnswerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPerfectMatchAnswerReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendPerfectMatchAnswerReq proto.InternalMessageInfo

func (m *SendPerfectMatchAnswerReq) GetMapAnswer() map[string]string {
	if m != nil {
		return m.MapAnswer
	}
	return nil
}

// 发送答案响应
type SendPerfectMatchAnswerResp struct {
	ExpectWait            string   `protobuf:"bytes,1,opt,name=expect_wait,json=expectWait,proto3" json:"expect_wait,omitempty"`
	WaitStart             uint32   `protobuf:"varint,2,opt,name=wait_start,json=waitStart,proto3" json:"wait_start,omitempty"`
	RemainRefreshQuestion uint32   `protobuf:"varint,3,opt,name=remain_refresh_question,json=remainRefreshQuestion,proto3" json:"remain_refresh_question,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *SendPerfectMatchAnswerResp) Reset()         { *m = SendPerfectMatchAnswerResp{} }
func (m *SendPerfectMatchAnswerResp) String() string { return proto.CompactTextString(m) }
func (*SendPerfectMatchAnswerResp) ProtoMessage()    {}
func (*SendPerfectMatchAnswerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{9}
}
func (m *SendPerfectMatchAnswerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPerfectMatchAnswerResp.Unmarshal(m, b)
}
func (m *SendPerfectMatchAnswerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPerfectMatchAnswerResp.Marshal(b, m, deterministic)
}
func (dst *SendPerfectMatchAnswerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPerfectMatchAnswerResp.Merge(dst, src)
}
func (m *SendPerfectMatchAnswerResp) XXX_Size() int {
	return xxx_messageInfo_SendPerfectMatchAnswerResp.Size(m)
}
func (m *SendPerfectMatchAnswerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPerfectMatchAnswerResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendPerfectMatchAnswerResp proto.InternalMessageInfo

func (m *SendPerfectMatchAnswerResp) GetExpectWait() string {
	if m != nil {
		return m.ExpectWait
	}
	return ""
}

func (m *SendPerfectMatchAnswerResp) GetWaitStart() uint32 {
	if m != nil {
		return m.WaitStart
	}
	return 0
}

func (m *SendPerfectMatchAnswerResp) GetRemainRefreshQuestion() uint32 {
	if m != nil {
		return m.RemainRefreshQuestion
	}
	return 0
}

// 取消匹配报名请求
type CancelPerfectMatchReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelPerfectMatchReq) Reset()         { *m = CancelPerfectMatchReq{} }
func (m *CancelPerfectMatchReq) String() string { return proto.CompactTextString(m) }
func (*CancelPerfectMatchReq) ProtoMessage()    {}
func (*CancelPerfectMatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{10}
}
func (m *CancelPerfectMatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelPerfectMatchReq.Unmarshal(m, b)
}
func (m *CancelPerfectMatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelPerfectMatchReq.Marshal(b, m, deterministic)
}
func (dst *CancelPerfectMatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelPerfectMatchReq.Merge(dst, src)
}
func (m *CancelPerfectMatchReq) XXX_Size() int {
	return xxx_messageInfo_CancelPerfectMatchReq.Size(m)
}
func (m *CancelPerfectMatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelPerfectMatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelPerfectMatchReq proto.InternalMessageInfo

// 取消匹配报名响应
type CancelPerfectMatchResp struct {
	Toast                string   `protobuf:"bytes,1,opt,name=toast,proto3" json:"toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelPerfectMatchResp) Reset()         { *m = CancelPerfectMatchResp{} }
func (m *CancelPerfectMatchResp) String() string { return proto.CompactTextString(m) }
func (*CancelPerfectMatchResp) ProtoMessage()    {}
func (*CancelPerfectMatchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{11}
}
func (m *CancelPerfectMatchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelPerfectMatchResp.Unmarshal(m, b)
}
func (m *CancelPerfectMatchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelPerfectMatchResp.Marshal(b, m, deterministic)
}
func (dst *CancelPerfectMatchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelPerfectMatchResp.Merge(dst, src)
}
func (m *CancelPerfectMatchResp) XXX_Size() int {
	return xxx_messageInfo_CancelPerfectMatchResp.Size(m)
}
func (m *CancelPerfectMatchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelPerfectMatchResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelPerfectMatchResp proto.InternalMessageInfo

func (m *CancelPerfectMatchResp) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

// 设置房间匹配状态
type SetChannelPerfectMatchStatusReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelPerfectMatchStatusReq) Reset()         { *m = SetChannelPerfectMatchStatusReq{} }
func (m *SetChannelPerfectMatchStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelPerfectMatchStatusReq) ProtoMessage()    {}
func (*SetChannelPerfectMatchStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{12}
}
func (m *SetChannelPerfectMatchStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPerfectMatchStatusReq.Unmarshal(m, b)
}
func (m *SetChannelPerfectMatchStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPerfectMatchStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelPerfectMatchStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPerfectMatchStatusReq.Merge(dst, src)
}
func (m *SetChannelPerfectMatchStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelPerfectMatchStatusReq.Size(m)
}
func (m *SetChannelPerfectMatchStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPerfectMatchStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPerfectMatchStatusReq proto.InternalMessageInfo

func (m *SetChannelPerfectMatchStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelPerfectMatchStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

//
type SetChannelPerfectMatchStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelPerfectMatchStatusResp) Reset()         { *m = SetChannelPerfectMatchStatusResp{} }
func (m *SetChannelPerfectMatchStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelPerfectMatchStatusResp) ProtoMessage()    {}
func (*SetChannelPerfectMatchStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{13}
}
func (m *SetChannelPerfectMatchStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPerfectMatchStatusResp.Unmarshal(m, b)
}
func (m *SetChannelPerfectMatchStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPerfectMatchStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelPerfectMatchStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPerfectMatchStatusResp.Merge(dst, src)
}
func (m *SetChannelPerfectMatchStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelPerfectMatchStatusResp.Size(m)
}
func (m *SetChannelPerfectMatchStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPerfectMatchStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPerfectMatchStatusResp proto.InternalMessageInfo

type SetChannelPerfectMatchGameEndReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  []uint32 `protobuf:"varint,2,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelPerfectMatchGameEndReq) Reset()         { *m = SetChannelPerfectMatchGameEndReq{} }
func (m *SetChannelPerfectMatchGameEndReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelPerfectMatchGameEndReq) ProtoMessage()    {}
func (*SetChannelPerfectMatchGameEndReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{14}
}
func (m *SetChannelPerfectMatchGameEndReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPerfectMatchGameEndReq.Unmarshal(m, b)
}
func (m *SetChannelPerfectMatchGameEndReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPerfectMatchGameEndReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelPerfectMatchGameEndReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPerfectMatchGameEndReq.Merge(dst, src)
}
func (m *SetChannelPerfectMatchGameEndReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelPerfectMatchGameEndReq.Size(m)
}
func (m *SetChannelPerfectMatchGameEndReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPerfectMatchGameEndReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPerfectMatchGameEndReq proto.InternalMessageInfo

func (m *SetChannelPerfectMatchGameEndReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelPerfectMatchGameEndReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

type SetChannelPerfectMatchGameEndResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelPerfectMatchGameEndResp) Reset()         { *m = SetChannelPerfectMatchGameEndResp{} }
func (m *SetChannelPerfectMatchGameEndResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelPerfectMatchGameEndResp) ProtoMessage()    {}
func (*SetChannelPerfectMatchGameEndResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_perfect_match_44213671ba3ecb74, []int{15}
}
func (m *SetChannelPerfectMatchGameEndResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelPerfectMatchGameEndResp.Unmarshal(m, b)
}
func (m *SetChannelPerfectMatchGameEndResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelPerfectMatchGameEndResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelPerfectMatchGameEndResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelPerfectMatchGameEndResp.Merge(dst, src)
}
func (m *SetChannelPerfectMatchGameEndResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelPerfectMatchGameEndResp.Size(m)
}
func (m *SetChannelPerfectMatchGameEndResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelPerfectMatchGameEndResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelPerfectMatchGameEndResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetPerfectMatchEntryReq)(nil), "perfect_match.GetPerfectMatchEntryReq")
	proto.RegisterType((*GetPerfectMatchEntryResp)(nil), "perfect_match.GetPerfectMatchEntryResp")
	proto.RegisterType((*EnrollPerfectMatchReq)(nil), "perfect_match.EnrollPerfectMatchReq")
	proto.RegisterType((*EnrollPerfectMatchResp)(nil), "perfect_match.EnrollPerfectMatchResp")
	proto.RegisterType((*GetPerfectMatchQuestionsReq)(nil), "perfect_match.GetPerfectMatchQuestionsReq")
	proto.RegisterType((*GetPerfectMatchQuestionsResp)(nil), "perfect_match.GetPerfectMatchQuestionsResp")
	proto.RegisterType((*SendPerfectMatchHeartbeatReq)(nil), "perfect_match.SendPerfectMatchHeartbeatReq")
	proto.RegisterType((*SendPerfectMatchHeartbeatResp)(nil), "perfect_match.SendPerfectMatchHeartbeatResp")
	proto.RegisterType((*SendPerfectMatchAnswerReq)(nil), "perfect_match.SendPerfectMatchAnswerReq")
	proto.RegisterMapType((map[string]string)(nil), "perfect_match.SendPerfectMatchAnswerReq.MapAnswerEntry")
	proto.RegisterType((*SendPerfectMatchAnswerResp)(nil), "perfect_match.SendPerfectMatchAnswerResp")
	proto.RegisterType((*CancelPerfectMatchReq)(nil), "perfect_match.CancelPerfectMatchReq")
	proto.RegisterType((*CancelPerfectMatchResp)(nil), "perfect_match.CancelPerfectMatchResp")
	proto.RegisterType((*SetChannelPerfectMatchStatusReq)(nil), "perfect_match.SetChannelPerfectMatchStatusReq")
	proto.RegisterType((*SetChannelPerfectMatchStatusResp)(nil), "perfect_match.SetChannelPerfectMatchStatusResp")
	proto.RegisterType((*SetChannelPerfectMatchGameEndReq)(nil), "perfect_match.SetChannelPerfectMatchGameEndReq")
	proto.RegisterType((*SetChannelPerfectMatchGameEndResp)(nil), "perfect_match.SetChannelPerfectMatchGameEndResp")
	proto.RegisterEnum("perfect_match.GetPerfectMatchEntryResp_Phase", GetPerfectMatchEntryResp_Phase_name, GetPerfectMatchEntryResp_Phase_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PerfectMatchClient is the client API for PerfectMatch service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PerfectMatchClient interface {
	// 获取天配匹配入口请求
	GetPerfectMatchEntry(ctx context.Context, in *GetPerfectMatchEntryReq, opts ...grpc.CallOption) (*GetPerfectMatchEntryResp, error)
	// 匹配报名请求
	EnrollPerfectMatch(ctx context.Context, in *EnrollPerfectMatchReq, opts ...grpc.CallOption) (*EnrollPerfectMatchResp, error)
	// 获取答案请求
	GetPerfectMatchQuestions(ctx context.Context, in *GetPerfectMatchQuestionsReq, opts ...grpc.CallOption) (*GetPerfectMatchQuestionsResp, error)
	// 匹配心跳请求
	SendPerfectMatchHeartbeat(ctx context.Context, in *SendPerfectMatchHeartbeatReq, opts ...grpc.CallOption) (*SendPerfectMatchHeartbeatResp, error)
	// 上报答案
	SendPerfectMatchAnswer(ctx context.Context, in *SendPerfectMatchAnswerReq, opts ...grpc.CallOption) (*SendPerfectMatchAnswerResp, error)
	// 取消匹配
	CancelPerfectMatch(ctx context.Context, in *CancelPerfectMatchReq, opts ...grpc.CallOption) (*CancelPerfectMatchResp, error)
	// 设置房间匹配状态
	SetChannelPerfectMatchStatus(ctx context.Context, in *SetChannelPerfectMatchStatusReq, opts ...grpc.CallOption) (*SetChannelPerfectMatchStatusResp, error)
	// 通知匹配结束
	SetChannelPerfectMatchGameEnd(ctx context.Context, in *SetChannelPerfectMatchGameEndReq, opts ...grpc.CallOption) (*SetChannelPerfectMatchGameEndResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error)
}

type perfectMatchClient struct {
	cc *grpc.ClientConn
}

func NewPerfectMatchClient(cc *grpc.ClientConn) PerfectMatchClient {
	return &perfectMatchClient{cc}
}

func (c *perfectMatchClient) GetPerfectMatchEntry(ctx context.Context, in *GetPerfectMatchEntryReq, opts ...grpc.CallOption) (*GetPerfectMatchEntryResp, error) {
	out := new(GetPerfectMatchEntryResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/GetPerfectMatchEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectMatchClient) EnrollPerfectMatch(ctx context.Context, in *EnrollPerfectMatchReq, opts ...grpc.CallOption) (*EnrollPerfectMatchResp, error) {
	out := new(EnrollPerfectMatchResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/EnrollPerfectMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectMatchClient) GetPerfectMatchQuestions(ctx context.Context, in *GetPerfectMatchQuestionsReq, opts ...grpc.CallOption) (*GetPerfectMatchQuestionsResp, error) {
	out := new(GetPerfectMatchQuestionsResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/GetPerfectMatchQuestions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectMatchClient) SendPerfectMatchHeartbeat(ctx context.Context, in *SendPerfectMatchHeartbeatReq, opts ...grpc.CallOption) (*SendPerfectMatchHeartbeatResp, error) {
	out := new(SendPerfectMatchHeartbeatResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/SendPerfectMatchHeartbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectMatchClient) SendPerfectMatchAnswer(ctx context.Context, in *SendPerfectMatchAnswerReq, opts ...grpc.CallOption) (*SendPerfectMatchAnswerResp, error) {
	out := new(SendPerfectMatchAnswerResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/SendPerfectMatchAnswer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectMatchClient) CancelPerfectMatch(ctx context.Context, in *CancelPerfectMatchReq, opts ...grpc.CallOption) (*CancelPerfectMatchResp, error) {
	out := new(CancelPerfectMatchResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/CancelPerfectMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectMatchClient) SetChannelPerfectMatchStatus(ctx context.Context, in *SetChannelPerfectMatchStatusReq, opts ...grpc.CallOption) (*SetChannelPerfectMatchStatusResp, error) {
	out := new(SetChannelPerfectMatchStatusResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/SetChannelPerfectMatchStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectMatchClient) SetChannelPerfectMatchGameEnd(ctx context.Context, in *SetChannelPerfectMatchGameEndReq, opts ...grpc.CallOption) (*SetChannelPerfectMatchGameEndResp, error) {
	out := new(SetChannelPerfectMatchGameEndResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/SetChannelPerfectMatchGameEnd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectMatchClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/GetConsumeTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectMatchClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/GetConsumeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectMatchClient) GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error) {
	out := new(reconcile_v2.GenFinancialFileResp)
	err := c.cc.Invoke(ctx, "/perfect_match.PerfectMatch/GenFinancialFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfectMatchServer is the server API for PerfectMatch service.
type PerfectMatchServer interface {
	// 获取天配匹配入口请求
	GetPerfectMatchEntry(context.Context, *GetPerfectMatchEntryReq) (*GetPerfectMatchEntryResp, error)
	// 匹配报名请求
	EnrollPerfectMatch(context.Context, *EnrollPerfectMatchReq) (*EnrollPerfectMatchResp, error)
	// 获取答案请求
	GetPerfectMatchQuestions(context.Context, *GetPerfectMatchQuestionsReq) (*GetPerfectMatchQuestionsResp, error)
	// 匹配心跳请求
	SendPerfectMatchHeartbeat(context.Context, *SendPerfectMatchHeartbeatReq) (*SendPerfectMatchHeartbeatResp, error)
	// 上报答案
	SendPerfectMatchAnswer(context.Context, *SendPerfectMatchAnswerReq) (*SendPerfectMatchAnswerResp, error)
	// 取消匹配
	CancelPerfectMatch(context.Context, *CancelPerfectMatchReq) (*CancelPerfectMatchResp, error)
	// 设置房间匹配状态
	SetChannelPerfectMatchStatus(context.Context, *SetChannelPerfectMatchStatusReq) (*SetChannelPerfectMatchStatusResp, error)
	// 通知匹配结束
	SetChannelPerfectMatchGameEnd(context.Context, *SetChannelPerfectMatchGameEndReq) (*SetChannelPerfectMatchGameEndResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	GenFinancialFile(context.Context, *reconcile_v2.GenFinancialFileReq) (*reconcile_v2.GenFinancialFileResp, error)
}

func RegisterPerfectMatchServer(s *grpc.Server, srv PerfectMatchServer) {
	s.RegisterService(&_PerfectMatch_serviceDesc, srv)
}

func _PerfectMatch_GetPerfectMatchEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPerfectMatchEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).GetPerfectMatchEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/GetPerfectMatchEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).GetPerfectMatchEntry(ctx, req.(*GetPerfectMatchEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectMatch_EnrollPerfectMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnrollPerfectMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).EnrollPerfectMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/EnrollPerfectMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).EnrollPerfectMatch(ctx, req.(*EnrollPerfectMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectMatch_GetPerfectMatchQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPerfectMatchQuestionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).GetPerfectMatchQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/GetPerfectMatchQuestions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).GetPerfectMatchQuestions(ctx, req.(*GetPerfectMatchQuestionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectMatch_SendPerfectMatchHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPerfectMatchHeartbeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).SendPerfectMatchHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/SendPerfectMatchHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).SendPerfectMatchHeartbeat(ctx, req.(*SendPerfectMatchHeartbeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectMatch_SendPerfectMatchAnswer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPerfectMatchAnswerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).SendPerfectMatchAnswer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/SendPerfectMatchAnswer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).SendPerfectMatchAnswer(ctx, req.(*SendPerfectMatchAnswerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectMatch_CancelPerfectMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelPerfectMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).CancelPerfectMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/CancelPerfectMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).CancelPerfectMatch(ctx, req.(*CancelPerfectMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectMatch_SetChannelPerfectMatchStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelPerfectMatchStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).SetChannelPerfectMatchStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/SetChannelPerfectMatchStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).SetChannelPerfectMatchStatus(ctx, req.(*SetChannelPerfectMatchStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectMatch_SetChannelPerfectMatchGameEnd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelPerfectMatchGameEndReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).SetChannelPerfectMatchGameEnd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/SetChannelPerfectMatchGameEnd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).SetChannelPerfectMatchGameEnd(ctx, req.(*SetChannelPerfectMatchGameEndReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectMatch_GetConsumeTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).GetConsumeTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/GetConsumeTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).GetConsumeTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectMatch_GetConsumeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).GetConsumeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/GetConsumeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).GetConsumeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectMatch_GenFinancialFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.GenFinancialFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectMatchServer).GenFinancialFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/perfect_match.PerfectMatch/GenFinancialFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectMatchServer).GenFinancialFile(ctx, req.(*reconcile_v2.GenFinancialFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PerfectMatch_serviceDesc = grpc.ServiceDesc{
	ServiceName: "perfect_match.PerfectMatch",
	HandlerType: (*PerfectMatchServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPerfectMatchEntry",
			Handler:    _PerfectMatch_GetPerfectMatchEntry_Handler,
		},
		{
			MethodName: "EnrollPerfectMatch",
			Handler:    _PerfectMatch_EnrollPerfectMatch_Handler,
		},
		{
			MethodName: "GetPerfectMatchQuestions",
			Handler:    _PerfectMatch_GetPerfectMatchQuestions_Handler,
		},
		{
			MethodName: "SendPerfectMatchHeartbeat",
			Handler:    _PerfectMatch_SendPerfectMatchHeartbeat_Handler,
		},
		{
			MethodName: "SendPerfectMatchAnswer",
			Handler:    _PerfectMatch_SendPerfectMatchAnswer_Handler,
		},
		{
			MethodName: "CancelPerfectMatch",
			Handler:    _PerfectMatch_CancelPerfectMatch_Handler,
		},
		{
			MethodName: "SetChannelPerfectMatchStatus",
			Handler:    _PerfectMatch_SetChannelPerfectMatchStatus_Handler,
		},
		{
			MethodName: "SetChannelPerfectMatchGameEnd",
			Handler:    _PerfectMatch_SetChannelPerfectMatchGameEnd_Handler,
		},
		{
			MethodName: "GetConsumeTotalCount",
			Handler:    _PerfectMatch_GetConsumeTotalCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderIds",
			Handler:    _PerfectMatch_GetConsumeOrderIds_Handler,
		},
		{
			MethodName: "GenFinancialFile",
			Handler:    _PerfectMatch_GenFinancialFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "perfect-match/perfect-match.proto",
}

func init() {
	proto.RegisterFile("perfect-match/perfect-match.proto", fileDescriptor_perfect_match_44213671ba3ecb74)
}

var fileDescriptor_perfect_match_44213671ba3ecb74 = []byte{
	// 1059 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x57, 0x5d, 0x53, 0xdb, 0x46,
	0x17, 0x46, 0x38, 0x7c, 0xf8, 0x10, 0x32, 0x66, 0x07, 0x83, 0x50, 0xe0, 0xc5, 0xe8, 0xed, 0x07,
	0x29, 0x45, 0x4e, 0xe9, 0xb4, 0xe9, 0x64, 0x7a, 0x43, 0x1d, 0x03, 0x9e, 0x16, 0x43, 0x64, 0x92,
	0xb4, 0xbd, 0x51, 0x37, 0xf2, 0x82, 0x77, 0x2a, 0xad, 0x84, 0x76, 0x4d, 0xc8, 0x65, 0x67, 0xfa,
	0x0b, 0x3a, 0xfd, 0x0d, 0xbd, 0xcf, 0x4f, 0xe9, 0x3f, 0xea, 0xec, 0xae, 0x0c, 0x96, 0x6d, 0x19,
	0x93, 0x3b, 0xce, 0x79, 0x9e, 0xf3, 0xb1, 0x67, 0xf7, 0x3c, 0x32, 0xb0, 0x15, 0x93, 0xe4, 0x9c,
	0xf8, 0x62, 0x37, 0xc4, 0xc2, 0xef, 0x54, 0x33, 0x96, 0x13, 0x27, 0x91, 0x88, 0xd0, 0x62, 0xea,
	0xf4, 0x94, 0xd3, 0xda, 0x49, 0xfc, 0xb0, 0x9d, 0x25, 0x56, 0xa5, 0x6b, 0x77, 0x44, 0xac, 0xb5,
	0x99, 0x10, 0x3f, 0x62, 0x3e, 0x0d, 0xc8, 0xee, 0xd5, 0x5e, 0xb5, 0xdf, 0xd0, 0x04, 0x7b, 0x0d,
	0x56, 0x0f, 0x89, 0x38, 0xd5, 0xa1, 0xc7, 0x32, 0xb2, 0xce, 0x44, 0xf2, 0xde, 0x25, 0x97, 0xf6,
	0xbf, 0x0f, 0xc0, 0x1c, 0x8d, 0xf1, 0x18, 0xad, 0xc2, 0x1c, 0xe5, 0x5e, 0x14, 0x13, 0x66, 0x1a,
	0x15, 0x63, 0x7b, 0xde, 0x9d, 0xa5, 0xfc, 0x24, 0x26, 0x0c, 0x6d, 0x00, 0xf8, 0x11, 0x17, 0x9e,
	0x78, 0x4b, 0x30, 0x33, 0xa7, 0x2b, 0xc6, 0xf6, 0xa2, 0x5b, 0x94, 0x9e, 0x33, 0xe9, 0x40, 0x8f,
	0xa1, 0x78, 0x41, 0xcf, 0x85, 0xc7, 0x70, 0x48, 0xcc, 0x42, 0xc5, 0xd8, 0x2e, 0xba, 0xf3, 0xd2,
	0xd1, 0xc4, 0x21, 0xb9, 0x01, 0xa9, 0x1f, 0x31, 0xf3, 0xc1, 0x2d, 0xd8, 0xf0, 0x23, 0x86, 0x96,
	0x61, 0x26, 0xee, 0x60, 0x4e, 0xcc, 0x19, 0x95, 0x53, 0x1b, 0x68, 0x13, 0x16, 0xc8, 0x75, 0x2c,
	0xa7, 0xf3, 0x0e, 0x53, 0x61, 0xce, 0xaa, 0x20, 0xd0, 0xae, 0x37, 0x98, 0x0a, 0x49, 0x50, 0x03,
	0xf1, 0xb8, 0xc0, 0x89, 0x30, 0xe7, 0x54, 0x30, 0x28, 0x57, 0x4b, 0x7a, 0x54, 0xc3, 0x1d, 0xcc,
	0x18, 0x09, 0x3c, 0xda, 0x36, 0xe7, 0xd3, 0x86, 0xb5, 0xa7, 0xd1, 0x46, 0x65, 0x98, 0x0d, 0xa9,
	0x2f, 0xa1, 0xa2, 0xae, 0x1b, 0x52, 0xbf, 0xd1, 0x46, 0xcf, 0xa1, 0x78, 0xd9, 0x25, 0x5c, 0xd0,
	0x88, 0x71, 0x13, 0x2a, 0x85, 0xed, 0x85, 0xbd, 0x75, 0x47, 0x5e, 0x83, 0x93, 0xb9, 0x2d, 0xe7,
	0x65, 0x4a, 0x72, 0x6f, 0xe9, 0xe8, 0x29, 0x2c, 0x27, 0x24, 0xc4, 0x94, 0x79, 0x3e, 0x66, 0x3e,
	0x09, 0x3c, 0xc2, 0x92, 0x28, 0x08, 0xcc, 0x05, 0x55, 0x00, 0x69, 0xac, 0xa6, 0xa0, 0xba, 0x42,
	0xd0, 0xb7, 0xb0, 0x9a, 0x46, 0x24, 0xe4, 0x3c, 0x21, 0xbc, 0xe3, 0xf5, 0xb2, 0x99, 0x0f, 0x55,
	0x50, 0x59, 0xc3, 0xae, 0x46, 0x7b, 0x45, 0xd1, 0x2e, 0xa0, 0x0e, 0xc1, 0x89, 0xbc, 0x0b, 0xe1,
	0x51, 0x26, 0x48, 0x72, 0x85, 0x03, 0x73, 0x51, 0x85, 0x2c, 0xdd, 0x20, 0x8d, 0x14, 0xb0, 0x7f,
	0x83, 0x99, 0x53, 0x35, 0xd5, 0x32, 0x2c, 0x9d, 0x1e, 0xed, 0xb7, 0xea, 0xde, 0xab, 0x66, 0xeb,
	0xb4, 0x5e, 0x6b, 0x1c, 0x34, 0xea, 0x2f, 0x4a, 0x53, 0x68, 0x0d, 0xca, 0xda, 0xbd, 0xdf, 0x6c,
	0xbd, 0xa9, 0xbb, 0xde, 0xcb, 0x57, 0xf5, 0xd6, 0x59, 0xe3, 0xa4, 0x59, 0x32, 0x10, 0x82, 0x47,
	0x1a, 0x6a, 0x34, 0xbd, 0xe3, 0xfd, 0xb3, 0xda, 0x51, 0x69, 0x1a, 0x2d, 0xc1, 0xa2, 0xf6, 0x29,
	0x47, 0xfd, 0x45, 0xa9, 0x60, 0x3f, 0x81, 0xb2, 0x3e, 0x52, 0xff, 0xab, 0x72, 0xc9, 0x25, 0x2a,
	0x41, 0x81, 0x93, 0x6b, 0xf5, 0x96, 0x16, 0x5d, 0xf9, 0xa7, 0xfd, 0xc1, 0x80, 0x95, 0x51, 0x5c,
	0x1e, 0xe7, 0x0e, 0xd0, 0xf8, 0x98, 0x01, 0x4e, 0xdf, 0x7f, 0x80, 0x85, 0xbc, 0x01, 0x3e, 0x83,
	0xc7, 0x03, 0x1b, 0xd3, 0xcb, 0xc4, 0xe5, 0x21, 0x4d, 0x98, 0x4b, 0xcb, 0xa7, 0x4b, 0xd3, 0x33,
	0xed, 0xbf, 0x0c, 0x58, 0xcf, 0x8f, 0xe4, 0x71, 0xf6, 0xbd, 0x19, 0xf7, 0x7b, 0x6f, 0x1f, 0x79,
	0x78, 0xfb, 0x7f, 0xb0, 0xde, 0x22, 0xac, 0xdd, 0xdf, 0xd4, 0x51, 0xef, 0xc8, 0x52, 0x20, 0x9a,
	0xb0, 0x31, 0x06, 0xe7, 0x71, 0xce, 0xf4, 0x8c, 0xbc, 0xe9, 0x7d, 0x30, 0x60, 0x6d, 0x30, 0xe1,
	0x3e, 0xe3, 0xef, 0x48, 0x22, 0x87, 0xf7, 0x1a, 0x20, 0xc4, 0xb1, 0x87, 0x95, 0x23, 0x1d, 0xc1,
	0xb3, 0x81, 0xd3, 0xe7, 0x46, 0x3b, 0xc7, 0x38, 0xd6, 0x86, 0x96, 0xb0, 0x62, 0xd8, 0xb3, 0xad,
	0xef, 0xe1, 0x51, 0x16, 0x94, 0x6f, 0xf1, 0x77, 0xf2, 0x5e, 0xf5, 0x59, 0x74, 0xe5, 0x9f, 0x52,
	0x7b, 0xae, 0x70, 0xd0, 0x25, 0x6a, 0x5e, 0x45, 0x57, 0x1b, 0xcf, 0xa7, 0xbf, 0x33, 0xec, 0xbf,
	0x0d, 0xb0, 0xf2, 0xaa, 0xf2, 0x78, 0x50, 0x9e, 0x8c, 0x21, 0x79, 0xda, 0x00, 0x90, 0x48, 0xaa,
	0x4e, 0xa9, 0x5c, 0x4a, 0x8f, 0x16, 0xa7, 0x31, 0x57, 0x57, 0x18, 0x77, 0x75, 0xab, 0x50, 0xd6,
	0xef, 0x7f, 0x60, 0xcf, 0x6c, 0x07, 0x56, 0x46, 0x01, 0x3c, 0x96, 0x67, 0x14, 0x11, 0xe6, 0xbd,
	0x26, 0xb5, 0x61, 0xff, 0x0c, 0x9b, 0x2d, 0x22, 0x6a, 0x5a, 0x0e, 0xfb, 0x63, 0x5a, 0x02, 0x8b,
	0xae, 0x7a, 0xd5, 0x59, 0x01, 0x35, 0x06, 0x05, 0x74, 0x05, 0x66, 0xb9, 0xe2, 0xa6, 0xa7, 0x4b,
	0x2d, 0xdb, 0x86, 0xca, 0xf8, 0xcc, 0x3c, 0xb6, 0x5b, 0x79, 0x9c, 0x43, 0x1c, 0x92, 0x3a, 0x6b,
	0x4f, 0x50, 0xbe, 0x04, 0x85, 0x2e, 0x6d, 0x9b, 0xd3, 0x95, 0x82, 0x14, 0x96, 0x2e, 0x6d, 0xdb,
	0xff, 0x87, 0xad, 0x3b, 0x92, 0xf2, 0x78, 0xef, 0x9f, 0x22, 0x3c, 0xec, 0xc7, 0x10, 0x85, 0xe5,
	0x51, 0x1f, 0x43, 0xf4, 0xd9, 0xc0, 0x13, 0xcc, 0xf9, 0x9a, 0x5a, 0x9f, 0x4f, 0xc4, 0xe3, 0xb1,
	0x3d, 0x85, 0x7c, 0x40, 0xc3, 0xc2, 0x87, 0x3e, 0x19, 0x48, 0x30, 0x52, 0x47, 0xad, 0x4f, 0x27,
	0x60, 0xa9, 0x22, 0xdd, 0xa1, 0x8f, 0xfb, 0x8d, 0xe0, 0xa0, 0x2f, 0xc6, 0xf7, 0xda, 0xaf, 0x69,
	0xd6, 0xce, 0xc4, 0x5c, 0x55, 0xf6, 0x7a, 0x78, 0xc5, 0x6f, 0x34, 0x03, 0xed, 0xdc, 0xb1, 0xce,
	0xfd, 0xea, 0x63, 0x7d, 0x39, 0x39, 0x59, 0x55, 0x8e, 0x60, 0x65, 0xf4, 0xa2, 0xa2, 0xed, 0x49,
	0x55, 0xc4, 0x7a, 0x32, 0x21, 0xb3, 0x77, 0x8d, 0xc3, 0xab, 0x36, 0x74, 0x8d, 0x23, 0xd7, 0x74,
	0xe8, 0x1a, 0x47, 0xef, 0xac, 0x3d, 0x85, 0xfe, 0x30, 0xa4, 0x48, 0xe7, 0xaf, 0x11, 0x72, 0x86,
	0x5a, 0x1e, 0xbb, 0xcd, 0x56, 0xf5, 0x5e, 0x7c, 0xd5, 0xc3, 0x9f, 0x86, 0xfc, 0x10, 0x8c, 0xd9,
	0x28, 0x34, 0x59, 0xd2, 0xdb, 0xa5, 0xb6, 0x9e, 0xde, 0x2f, 0x40, 0xb5, 0xf1, 0xa3, 0xda, 0xd0,
	0x5a, 0xc4, 0x78, 0x37, 0x24, 0x67, 0x91, 0xc0, 0x41, 0x2d, 0xea, 0x32, 0x81, 0xd6, 0x1c, 0xb7,
	0xf7, 0xbb, 0xf7, 0xf5, 0x9e, 0x73, 0x46, 0x43, 0xe2, 0x62, 0x76, 0x41, 0x64, 0x99, 0x95, 0x0c,
	0xa4, 0xe8, 0x69, 0xb2, 0x9f, 0x00, 0xdd, 0x26, 0x3b, 0x49, 0xda, 0x24, 0x69, 0xb4, 0xf9, 0xb8,
	0x54, 0x59, 0xa8, 0x17, 0x91, 0x66, 0xfb, 0x05, 0x4a, 0x87, 0x84, 0x1d, 0x50, 0x86, 0x99, 0x4f,
	0x71, 0x70, 0x40, 0x03, 0x82, 0x2a, 0x99, 0x80, 0x41, 0x58, 0xa6, 0xdc, 0xba, 0x83, 0x21, 0x53,
	0xff, 0xf0, 0xd5, 0xaf, 0xd5, 0x8b, 0x28, 0xc0, 0xec, 0xc2, 0xf9, 0x66, 0x4f, 0x08, 0xc7, 0x8f,
	0xc2, 0xaa, 0xfa, 0x65, 0xef, 0x47, 0x41, 0x95, 0x93, 0xe4, 0x8a, 0xfa, 0x84, 0x67, 0xff, 0x5b,
	0x78, 0x3b, 0xab, 0x08, 0x5f, 0xff, 0x17, 0x00, 0x00, 0xff, 0xff, 0x20, 0xcb, 0xf5, 0x0c, 0x7c,
	0x0c, 0x00, 0x00,
}
