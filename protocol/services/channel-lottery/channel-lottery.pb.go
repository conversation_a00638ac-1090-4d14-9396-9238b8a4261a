// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-lottery/channel-lottery.proto

package channel_lottery // import "golang.52tt.com/protocol/services/channel-lottery"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ConditionBingoType int32

const (
	ConditionBingoType_RandomBingo ConditionBingoType = 0
	ConditionBingoType_MustBingo   ConditionBingoType = 1
)

var ConditionBingoType_name = map[int32]string{
	0: "RandomBingo",
	1: "MustBingo",
}
var ConditionBingoType_value = map[string]int32{
	"RandomBingo": 0,
	"MustBingo":   1,
}

func (x ConditionBingoType) String() string {
	return proto.EnumName(ConditionBingoType_name, int32(x))
}
func (ConditionBingoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{0}
}

type LotteryCondition int32

const (
	LotteryCondition_UNKNOWN                 LotteryCondition = 0
	LotteryCondition_JOIN_FANS_GROUP         LotteryCondition = 1
	LotteryCondition_SEND_MSG                LotteryCondition = 2
	LotteryCondition_SEND_PRESENT            LotteryCondition = 3
	LotteryCondition_SEND_PRESENT_TOP1       LotteryCondition = 4
	LotteryCondition_SEND_PRESENT_CHOSE_RANK LotteryCondition = 5
	LotteryCondition_FOLLOW_MIC_USER         LotteryCondition = 6
	LotteryCondition_SHARE_CHANNEL           LotteryCondition = 7
	LotteryCondition_SEND_PRESENT_TOP_N      LotteryCondition = 8
)

var LotteryCondition_name = map[int32]string{
	0: "UNKNOWN",
	1: "JOIN_FANS_GROUP",
	2: "SEND_MSG",
	3: "SEND_PRESENT",
	4: "SEND_PRESENT_TOP1",
	5: "SEND_PRESENT_CHOSE_RANK",
	6: "FOLLOW_MIC_USER",
	7: "SHARE_CHANNEL",
	8: "SEND_PRESENT_TOP_N",
}
var LotteryCondition_value = map[string]int32{
	"UNKNOWN":                 0,
	"JOIN_FANS_GROUP":         1,
	"SEND_MSG":                2,
	"SEND_PRESENT":            3,
	"SEND_PRESENT_TOP1":       4,
	"SEND_PRESENT_CHOSE_RANK": 5,
	"FOLLOW_MIC_USER":         6,
	"SHARE_CHANNEL":           7,
	"SEND_PRESENT_TOP_N":      8,
}

func (x LotteryCondition) String() string {
	return proto.EnumName(LotteryCondition_name, int32(x))
}
func (LotteryCondition) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{1}
}

// 参与人群枚举
type LotteryPersonType int32

const (
	LotteryPersonType_LOTTERY_PERSON_TYPE_UNKNOWN LotteryPersonType = 0
	LotteryPersonType_LOTTERY_PERSON_TYPE_ALL     LotteryPersonType = 1
	LotteryPersonType_LOTTERY_PERSON_TYPE_FANS    LotteryPersonType = 2
)

var LotteryPersonType_name = map[int32]string{
	0: "LOTTERY_PERSON_TYPE_UNKNOWN",
	1: "LOTTERY_PERSON_TYPE_ALL",
	2: "LOTTERY_PERSON_TYPE_FANS",
}
var LotteryPersonType_value = map[string]int32{
	"LOTTERY_PERSON_TYPE_UNKNOWN": 0,
	"LOTTERY_PERSON_TYPE_ALL":     1,
	"LOTTERY_PERSON_TYPE_FANS":    2,
}

func (x LotteryPersonType) String() string {
	return proto.EnumName(LotteryPersonType_name, int32(x))
}
func (LotteryPersonType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{2}
}

// 抽奖礼物送出类型
type LotteryGiftSendType int32

const (
	LotteryGiftSendType_LOTTERY_GIFT_TYPE_HAND        LotteryGiftSendType = 0
	LotteryGiftSendType_LOTTERY_GIFT_TYPE_BACKPACK    LotteryGiftSendType = 1
	LotteryGiftSendType_LOTTERY_GIFT_TYPE_LUCK        LotteryGiftSendType = 2
	LotteryGiftSendType_LOTTERY_GIFT_TYPE_LEVELUP     LotteryGiftSendType = 3
	LotteryGiftSendType_LOTTERY_GIFT_TYPE_COMMON      LotteryGiftSendType = 4
	LotteryGiftSendType_LOTTERY_GIFT_TYPE_CUSTOM_HAND LotteryGiftSendType = 5
)

var LotteryGiftSendType_name = map[int32]string{
	0: "LOTTERY_GIFT_TYPE_HAND",
	1: "LOTTERY_GIFT_TYPE_BACKPACK",
	2: "LOTTERY_GIFT_TYPE_LUCK",
	3: "LOTTERY_GIFT_TYPE_LEVELUP",
	4: "LOTTERY_GIFT_TYPE_COMMON",
	5: "LOTTERY_GIFT_TYPE_CUSTOM_HAND",
}
var LotteryGiftSendType_value = map[string]int32{
	"LOTTERY_GIFT_TYPE_HAND":        0,
	"LOTTERY_GIFT_TYPE_BACKPACK":    1,
	"LOTTERY_GIFT_TYPE_LUCK":        2,
	"LOTTERY_GIFT_TYPE_LEVELUP":     3,
	"LOTTERY_GIFT_TYPE_COMMON":      4,
	"LOTTERY_GIFT_TYPE_CUSTOM_HAND": 5,
}

func (x LotteryGiftSendType) String() string {
	return proto.EnumName(LotteryGiftSendType_name, int32(x))
}
func (LotteryGiftSendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{3}
}

type Condition_OpType int32

const (
	Condition_None     Condition_OpType = 0
	Condition_Input    Condition_OpType = 1
	Condition_ChoseOne Condition_OpType = 2
)

var Condition_OpType_name = map[int32]string{
	0: "None",
	1: "Input",
	2: "ChoseOne",
}
var Condition_OpType_value = map[string]int32{
	"None":     0,
	"Input":    1,
	"ChoseOne": 2,
}

func (x Condition_OpType) String() string {
	return proto.EnumName(Condition_OpType_name, int32(x))
}
func (Condition_OpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{5, 0}
}

type JoinUserInfo_Flag int32

const (
	JoinUserInfo_Common JoinUserInfo_Flag = 0
	JoinUserInfo_Bingo  JoinUserInfo_Flag = 1
)

var JoinUserInfo_Flag_name = map[int32]string{
	0: "Common",
	1: "Bingo",
}
var JoinUserInfo_Flag_value = map[string]int32{
	"Common": 0,
	"Bingo":  1,
}

func (x JoinUserInfo_Flag) String() string {
	return proto.EnumName(JoinUserInfo_Flag_name, int32(x))
}
func (JoinUserInfo_Flag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{12, 0}
}

type ConditionMissionProgress_MissionStatus int32

const (
	ConditionMissionProgress_NotJoin   ConditionMissionProgress_MissionStatus = 0
	ConditionMissionProgress_NotFinish ConditionMissionProgress_MissionStatus = 1
	ConditionMissionProgress_Finish    ConditionMissionProgress_MissionStatus = 2
)

var ConditionMissionProgress_MissionStatus_name = map[int32]string{
	0: "NotJoin",
	1: "NotFinish",
	2: "Finish",
}
var ConditionMissionProgress_MissionStatus_value = map[string]int32{
	"NotJoin":   0,
	"NotFinish": 1,
	"Finish":    2,
}

func (x ConditionMissionProgress_MissionStatus) String() string {
	return proto.EnumName(ConditionMissionProgress_MissionStatus_name, int32(x))
}
func (ConditionMissionProgress_MissionStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{13, 0}
}

type ChannelLotteryInfo_LotteryStatus int32

const (
	ChannelLotteryInfo_SETTING      ChannelLotteryInfo_LotteryStatus = 0
	ChannelLotteryInfo_RECORD       ChannelLotteryInfo_LotteryStatus = 1
	ChannelLotteryInfo_RESULT       ChannelLotteryInfo_LotteryStatus = 2
	ChannelLotteryInfo_FINISH       ChannelLotteryInfo_LotteryStatus = 3
	ChannelLotteryInfo_BREAK        ChannelLotteryInfo_LotteryStatus = 4
	ChannelLotteryInfo_RESULT_ERROR ChannelLotteryInfo_LotteryStatus = 5
)

var ChannelLotteryInfo_LotteryStatus_name = map[int32]string{
	0: "SETTING",
	1: "RECORD",
	2: "RESULT",
	3: "FINISH",
	4: "BREAK",
	5: "RESULT_ERROR",
}
var ChannelLotteryInfo_LotteryStatus_value = map[string]int32{
	"SETTING":      0,
	"RECORD":       1,
	"RESULT":       2,
	"FINISH":       3,
	"BREAK":        4,
	"RESULT_ERROR": 5,
}

func (x ChannelLotteryInfo_LotteryStatus) String() string {
	return proto.EnumName(ChannelLotteryInfo_LotteryStatus_name, int32(x))
}
func (ChannelLotteryInfo_LotteryStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{14, 0}
}

// 发放来源
type SetLotteryOpenListReq_SetSource int32

const (
	SetLotteryOpenListReq_SetSource_Default             SetLotteryOpenListReq_SetSource = 0
	SetLotteryOpenListReq_SetSource_ANCHOR_LEVEL_SETTLE SetLotteryOpenListReq_SetSource = 1
)

var SetLotteryOpenListReq_SetSource_name = map[int32]string{
	0: "SetSource_Default",
	1: "SetSource_ANCHOR_LEVEL_SETTLE",
}
var SetLotteryOpenListReq_SetSource_value = map[string]int32{
	"SetSource_Default":             0,
	"SetSource_ANCHOR_LEVEL_SETTLE": 1,
}

func (x SetLotteryOpenListReq_SetSource) String() string {
	return proto.EnumName(SetLotteryOpenListReq_SetSource_name, int32(x))
}
func (SetLotteryOpenListReq_SetSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{46, 0}
}

type ShowChannelLotterySettingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowChannelLotterySettingReq) Reset()         { *m = ShowChannelLotterySettingReq{} }
func (m *ShowChannelLotterySettingReq) String() string { return proto.CompactTextString(m) }
func (*ShowChannelLotterySettingReq) ProtoMessage()    {}
func (*ShowChannelLotterySettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{0}
}
func (m *ShowChannelLotterySettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowChannelLotterySettingReq.Unmarshal(m, b)
}
func (m *ShowChannelLotterySettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowChannelLotterySettingReq.Marshal(b, m, deterministic)
}
func (dst *ShowChannelLotterySettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowChannelLotterySettingReq.Merge(dst, src)
}
func (m *ShowChannelLotterySettingReq) XXX_Size() int {
	return xxx_messageInfo_ShowChannelLotterySettingReq.Size(m)
}
func (m *ShowChannelLotterySettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowChannelLotterySettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_ShowChannelLotterySettingReq proto.InternalMessageInfo

func (m *ShowChannelLotterySettingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ShowChannelLotterySettingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ShowChannelLotterySettingResp struct {
	Show                 bool     `protobuf:"varint,1,opt,name=show,proto3" json:"show,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowChannelLotterySettingResp) Reset()         { *m = ShowChannelLotterySettingResp{} }
func (m *ShowChannelLotterySettingResp) String() string { return proto.CompactTextString(m) }
func (*ShowChannelLotterySettingResp) ProtoMessage()    {}
func (*ShowChannelLotterySettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{1}
}
func (m *ShowChannelLotterySettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowChannelLotterySettingResp.Unmarshal(m, b)
}
func (m *ShowChannelLotterySettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowChannelLotterySettingResp.Marshal(b, m, deterministic)
}
func (dst *ShowChannelLotterySettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowChannelLotterySettingResp.Merge(dst, src)
}
func (m *ShowChannelLotterySettingResp) XXX_Size() int {
	return xxx_messageInfo_ShowChannelLotterySettingResp.Size(m)
}
func (m *ShowChannelLotterySettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowChannelLotterySettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_ShowChannelLotterySettingResp proto.InternalMessageInfo

func (m *ShowChannelLotterySettingResp) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

type OptionButton struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	OptionVal            uint32   `protobuf:"varint,2,opt,name=option_val,json=optionVal,proto3" json:"option_val,omitempty"`
	FixedPeopleNum       uint32   `protobuf:"varint,3,opt,name=fixed_people_num,json=fixedPeopleNum,proto3" json:"fixed_people_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OptionButton) Reset()         { *m = OptionButton{} }
func (m *OptionButton) String() string { return proto.CompactTextString(m) }
func (*OptionButton) ProtoMessage()    {}
func (*OptionButton) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{2}
}
func (m *OptionButton) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OptionButton.Unmarshal(m, b)
}
func (m *OptionButton) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OptionButton.Marshal(b, m, deterministic)
}
func (dst *OptionButton) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OptionButton.Merge(dst, src)
}
func (m *OptionButton) XXX_Size() int {
	return xxx_messageInfo_OptionButton.Size(m)
}
func (m *OptionButton) XXX_DiscardUnknown() {
	xxx_messageInfo_OptionButton.DiscardUnknown(m)
}

var xxx_messageInfo_OptionButton proto.InternalMessageInfo

func (m *OptionButton) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *OptionButton) GetOptionVal() uint32 {
	if m != nil {
		return m.OptionVal
	}
	return 0
}

func (m *OptionButton) GetFixedPeopleNum() uint32 {
	if m != nil {
		return m.FixedPeopleNum
	}
	return 0
}

type GetChannelLotterySettingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelLotterySettingReq) Reset()         { *m = GetChannelLotterySettingReq{} }
func (m *GetChannelLotterySettingReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelLotterySettingReq) ProtoMessage()    {}
func (*GetChannelLotterySettingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{3}
}
func (m *GetChannelLotterySettingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLotterySettingReq.Unmarshal(m, b)
}
func (m *GetChannelLotterySettingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLotterySettingReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelLotterySettingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLotterySettingReq.Merge(dst, src)
}
func (m *GetChannelLotterySettingReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelLotterySettingReq.Size(m)
}
func (m *GetChannelLotterySettingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLotterySettingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLotterySettingReq proto.InternalMessageInfo

func (m *GetChannelLotterySettingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelLotterySettingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelLotterySettingResp struct {
	ConditionList        []*Condition                                       `protobuf:"bytes,1,rep,name=condition_list,json=conditionList,proto3" json:"condition_list,omitempty"`
	Limit                uint32                                             `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	LimitV2              uint32                                             `protobuf:"varint,3,opt,name=limit_v2,json=limitV2,proto3" json:"limit_v2,omitempty"`
	ConditionListV2      []*Condition                                       `protobuf:"bytes,4,rep,name=condition_list_v2,json=conditionListV2,proto3" json:"condition_list_v2,omitempty"`
	PeopleList           []*GetChannelLotterySettingResp_TargetPeopleOption `protobuf:"bytes,5,rep,name=people_list,json=peopleList,proto3" json:"people_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                           `json:"-"`
	XXX_unrecognized     []byte                                             `json:"-"`
	XXX_sizecache        int32                                              `json:"-"`
}

func (m *GetChannelLotterySettingResp) Reset()         { *m = GetChannelLotterySettingResp{} }
func (m *GetChannelLotterySettingResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelLotterySettingResp) ProtoMessage()    {}
func (*GetChannelLotterySettingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{4}
}
func (m *GetChannelLotterySettingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLotterySettingResp.Unmarshal(m, b)
}
func (m *GetChannelLotterySettingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLotterySettingResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelLotterySettingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLotterySettingResp.Merge(dst, src)
}
func (m *GetChannelLotterySettingResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelLotterySettingResp.Size(m)
}
func (m *GetChannelLotterySettingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLotterySettingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLotterySettingResp proto.InternalMessageInfo

func (m *GetChannelLotterySettingResp) GetConditionList() []*Condition {
	if m != nil {
		return m.ConditionList
	}
	return nil
}

func (m *GetChannelLotterySettingResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetChannelLotterySettingResp) GetLimitV2() uint32 {
	if m != nil {
		return m.LimitV2
	}
	return 0
}

func (m *GetChannelLotterySettingResp) GetConditionListV2() []*Condition {
	if m != nil {
		return m.ConditionListV2
	}
	return nil
}

func (m *GetChannelLotterySettingResp) GetPeopleList() []*GetChannelLotterySettingResp_TargetPeopleOption {
	if m != nil {
		return m.PeopleList
	}
	return nil
}

type GetChannelLotterySettingResp_TargetPeopleOption struct {
	Ty                   LotteryPersonType `protobuf:"varint,1,opt,name=ty,proto3,enum=channel_lottery.LotteryPersonType" json:"ty,omitempty"`
	Name                 string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LimitOptions         []*OptionButton   `protobuf:"bytes,3,rep,name=limit_options,json=limitOptions,proto3" json:"limit_options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChannelLotterySettingResp_TargetPeopleOption) Reset() {
	*m = GetChannelLotterySettingResp_TargetPeopleOption{}
}
func (m *GetChannelLotterySettingResp_TargetPeopleOption) String() string {
	return proto.CompactTextString(m)
}
func (*GetChannelLotterySettingResp_TargetPeopleOption) ProtoMessage() {}
func (*GetChannelLotterySettingResp_TargetPeopleOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{4, 0}
}
func (m *GetChannelLotterySettingResp_TargetPeopleOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLotterySettingResp_TargetPeopleOption.Unmarshal(m, b)
}
func (m *GetChannelLotterySettingResp_TargetPeopleOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLotterySettingResp_TargetPeopleOption.Marshal(b, m, deterministic)
}
func (dst *GetChannelLotterySettingResp_TargetPeopleOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLotterySettingResp_TargetPeopleOption.Merge(dst, src)
}
func (m *GetChannelLotterySettingResp_TargetPeopleOption) XXX_Size() int {
	return xxx_messageInfo_GetChannelLotterySettingResp_TargetPeopleOption.Size(m)
}
func (m *GetChannelLotterySettingResp_TargetPeopleOption) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLotterySettingResp_TargetPeopleOption.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLotterySettingResp_TargetPeopleOption proto.InternalMessageInfo

func (m *GetChannelLotterySettingResp_TargetPeopleOption) GetTy() LotteryPersonType {
	if m != nil {
		return m.Ty
	}
	return LotteryPersonType_LOTTERY_PERSON_TYPE_UNKNOWN
}

func (m *GetChannelLotterySettingResp_TargetPeopleOption) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetChannelLotterySettingResp_TargetPeopleOption) GetLimitOptions() []*OptionButton {
	if m != nil {
		return m.LimitOptions
	}
	return nil
}

type Condition struct {
	Id                   uint32           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LimitOptions         []*OptionButton  `protobuf:"bytes,3,rep,name=limit_options,json=limitOptions,proto3" json:"limit_options,omitempty"`
	OpType               Condition_OpType `protobuf:"varint,4,opt,name=op_type,json=opType,proto3,enum=channel_lottery.Condition_OpType" json:"op_type,omitempty"`
	FixedPeopleNum       uint32           `protobuf:"varint,5,opt,name=fixed_people_num,json=fixedPeopleNum,proto3" json:"fixed_people_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *Condition) Reset()         { *m = Condition{} }
func (m *Condition) String() string { return proto.CompactTextString(m) }
func (*Condition) ProtoMessage()    {}
func (*Condition) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{5}
}
func (m *Condition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Condition.Unmarshal(m, b)
}
func (m *Condition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Condition.Marshal(b, m, deterministic)
}
func (dst *Condition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Condition.Merge(dst, src)
}
func (m *Condition) XXX_Size() int {
	return xxx_messageInfo_Condition.Size(m)
}
func (m *Condition) XXX_DiscardUnknown() {
	xxx_messageInfo_Condition.DiscardUnknown(m)
}

var xxx_messageInfo_Condition proto.InternalMessageInfo

func (m *Condition) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Condition) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Condition) GetLimitOptions() []*OptionButton {
	if m != nil {
		return m.LimitOptions
	}
	return nil
}

func (m *Condition) GetOpType() Condition_OpType {
	if m != nil {
		return m.OpType
	}
	return Condition_None
}

func (m *Condition) GetFixedPeopleNum() uint32 {
	if m != nil {
		return m.FixedPeopleNum
	}
	return 0
}

type SetChannelLotteryInfoReq struct {
	Uid           uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId     uint32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ConditionId   uint32 `protobuf:"varint,3,opt,name=condition_id,json=conditionId,proto3" json:"condition_id,omitempty"`
	ConditionText string `protobuf:"bytes,4,opt,name=condition_text,json=conditionText,proto3" json:"condition_text,omitempty"`
	Number        uint32 `protobuf:"varint,5,opt,name=number,proto3" json:"number,omitempty"`
	AwardGiftId   uint32 `protobuf:"varint,6,opt,name=award_gift_id,json=awardGiftId,proto3" json:"award_gift_id,omitempty"`
	IpAddr        string `protobuf:"bytes,7,opt,name=ip_addr,json=ipAddr,proto3" json:"ip_addr,omitempty"`
	DeviceId      []byte `protobuf:"bytes,8,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	SmDeviceId    []byte `protobuf:"bytes,9,opt,name=sm_device_id,json=smDeviceId,proto3" json:"sm_device_id,omitempty"`
	// 自定义礼物id
	CustomGiftId uint32 `protobuf:"varint,10,opt,name=custom_gift_id,json=customGiftId,proto3" json:"custom_gift_id,omitempty"`
	// TT6.11.0 新增
	ConditionGoalVal     uint32              `protobuf:"varint,11,opt,name=condition_goal_val,json=conditionGoalVal,proto3" json:"condition_goal_val,omitempty"`
	GiftSendType         LotteryGiftSendType `protobuf:"varint,12,opt,name=gift_send_type,json=giftSendType,proto3,enum=channel_lottery.LotteryGiftSendType" json:"gift_send_type,omitempty"`
	TargetPeopleInfo     *TargetPeopleInfo   `protobuf:"bytes,13,opt,name=target_people_info,json=targetPeopleInfo,proto3" json:"target_people_info,omitempty"`
	LevelUpGiftVersion   uint32              `protobuf:"varint,14,opt,name=level_up_gift_version,json=levelUpGiftVersion,proto3" json:"level_up_gift_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SetChannelLotteryInfoReq) Reset()         { *m = SetChannelLotteryInfoReq{} }
func (m *SetChannelLotteryInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelLotteryInfoReq) ProtoMessage()    {}
func (*SetChannelLotteryInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{6}
}
func (m *SetChannelLotteryInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelLotteryInfoReq.Unmarshal(m, b)
}
func (m *SetChannelLotteryInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelLotteryInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelLotteryInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelLotteryInfoReq.Merge(dst, src)
}
func (m *SetChannelLotteryInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelLotteryInfoReq.Size(m)
}
func (m *SetChannelLotteryInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelLotteryInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelLotteryInfoReq proto.InternalMessageInfo

func (m *SetChannelLotteryInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelLotteryInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelLotteryInfoReq) GetConditionId() uint32 {
	if m != nil {
		return m.ConditionId
	}
	return 0
}

func (m *SetChannelLotteryInfoReq) GetConditionText() string {
	if m != nil {
		return m.ConditionText
	}
	return ""
}

func (m *SetChannelLotteryInfoReq) GetNumber() uint32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *SetChannelLotteryInfoReq) GetAwardGiftId() uint32 {
	if m != nil {
		return m.AwardGiftId
	}
	return 0
}

func (m *SetChannelLotteryInfoReq) GetIpAddr() string {
	if m != nil {
		return m.IpAddr
	}
	return ""
}

func (m *SetChannelLotteryInfoReq) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *SetChannelLotteryInfoReq) GetSmDeviceId() []byte {
	if m != nil {
		return m.SmDeviceId
	}
	return nil
}

func (m *SetChannelLotteryInfoReq) GetCustomGiftId() uint32 {
	if m != nil {
		return m.CustomGiftId
	}
	return 0
}

func (m *SetChannelLotteryInfoReq) GetConditionGoalVal() uint32 {
	if m != nil {
		return m.ConditionGoalVal
	}
	return 0
}

func (m *SetChannelLotteryInfoReq) GetGiftSendType() LotteryGiftSendType {
	if m != nil {
		return m.GiftSendType
	}
	return LotteryGiftSendType_LOTTERY_GIFT_TYPE_HAND
}

func (m *SetChannelLotteryInfoReq) GetTargetPeopleInfo() *TargetPeopleInfo {
	if m != nil {
		return m.TargetPeopleInfo
	}
	return nil
}

func (m *SetChannelLotteryInfoReq) GetLevelUpGiftVersion() uint32 {
	if m != nil {
		return m.LevelUpGiftVersion
	}
	return 0
}

type SetChannelLotteryInfoResp struct {
	ConditionName        string   `protobuf:"bytes,1,opt,name=condition_name,json=conditionName,proto3" json:"condition_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelLotteryInfoResp) Reset()         { *m = SetChannelLotteryInfoResp{} }
func (m *SetChannelLotteryInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelLotteryInfoResp) ProtoMessage()    {}
func (*SetChannelLotteryInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{7}
}
func (m *SetChannelLotteryInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelLotteryInfoResp.Unmarshal(m, b)
}
func (m *SetChannelLotteryInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelLotteryInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelLotteryInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelLotteryInfoResp.Merge(dst, src)
}
func (m *SetChannelLotteryInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelLotteryInfoResp.Size(m)
}
func (m *SetChannelLotteryInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelLotteryInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelLotteryInfoResp proto.InternalMessageInfo

func (m *SetChannelLotteryInfoResp) GetConditionName() string {
	if m != nil {
		return m.ConditionName
	}
	return ""
}

type JoinChannelLotteryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinChannelLotteryReq) Reset()         { *m = JoinChannelLotteryReq{} }
func (m *JoinChannelLotteryReq) String() string { return proto.CompactTextString(m) }
func (*JoinChannelLotteryReq) ProtoMessage()    {}
func (*JoinChannelLotteryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{8}
}
func (m *JoinChannelLotteryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelLotteryReq.Unmarshal(m, b)
}
func (m *JoinChannelLotteryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelLotteryReq.Marshal(b, m, deterministic)
}
func (dst *JoinChannelLotteryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelLotteryReq.Merge(dst, src)
}
func (m *JoinChannelLotteryReq) XXX_Size() int {
	return xxx_messageInfo_JoinChannelLotteryReq.Size(m)
}
func (m *JoinChannelLotteryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelLotteryReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelLotteryReq proto.InternalMessageInfo

func (m *JoinChannelLotteryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinChannelLotteryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type JoinChannelLotteryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinChannelLotteryResp) Reset()         { *m = JoinChannelLotteryResp{} }
func (m *JoinChannelLotteryResp) String() string { return proto.CompactTextString(m) }
func (*JoinChannelLotteryResp) ProtoMessage()    {}
func (*JoinChannelLotteryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{9}
}
func (m *JoinChannelLotteryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelLotteryResp.Unmarshal(m, b)
}
func (m *JoinChannelLotteryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelLotteryResp.Marshal(b, m, deterministic)
}
func (dst *JoinChannelLotteryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelLotteryResp.Merge(dst, src)
}
func (m *JoinChannelLotteryResp) XXX_Size() int {
	return xxx_messageInfo_JoinChannelLotteryResp.Size(m)
}
func (m *JoinChannelLotteryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelLotteryResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelLotteryResp proto.InternalMessageInfo

type GetChannelLotteryInfoListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelLotteryInfoListReq) Reset()         { *m = GetChannelLotteryInfoListReq{} }
func (m *GetChannelLotteryInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelLotteryInfoListReq) ProtoMessage()    {}
func (*GetChannelLotteryInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{10}
}
func (m *GetChannelLotteryInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLotteryInfoListReq.Unmarshal(m, b)
}
func (m *GetChannelLotteryInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLotteryInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelLotteryInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLotteryInfoListReq.Merge(dst, src)
}
func (m *GetChannelLotteryInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelLotteryInfoListReq.Size(m)
}
func (m *GetChannelLotteryInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLotteryInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLotteryInfoListReq proto.InternalMessageInfo

func (m *GetChannelLotteryInfoListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelLotteryInfoListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelLotteryInfoListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetChannelLotteryInfoListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetChannelLotteryInfoListResp struct {
	LotteryInfoList      []*ChannelLotteryInfo `protobuf:"bytes,1,rep,name=lottery_info_list,json=lotteryInfoList,proto3" json:"lottery_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetChannelLotteryInfoListResp) Reset()         { *m = GetChannelLotteryInfoListResp{} }
func (m *GetChannelLotteryInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelLotteryInfoListResp) ProtoMessage()    {}
func (*GetChannelLotteryInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{11}
}
func (m *GetChannelLotteryInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLotteryInfoListResp.Unmarshal(m, b)
}
func (m *GetChannelLotteryInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLotteryInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelLotteryInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLotteryInfoListResp.Merge(dst, src)
}
func (m *GetChannelLotteryInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelLotteryInfoListResp.Size(m)
}
func (m *GetChannelLotteryInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLotteryInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLotteryInfoListResp proto.InternalMessageInfo

func (m *GetChannelLotteryInfoListResp) GetLotteryInfoList() []*ChannelLotteryInfo {
	if m != nil {
		return m.LotteryInfoList
	}
	return nil
}

type JoinUserInfo struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CurrVal              uint32            `protobuf:"varint,2,opt,name=curr_val,json=currVal,proto3" json:"curr_val,omitempty"`
	ShowFlag             JoinUserInfo_Flag `protobuf:"varint,3,opt,name=show_flag,json=showFlag,proto3,enum=channel_lottery.JoinUserInfo_Flag" json:"show_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *JoinUserInfo) Reset()         { *m = JoinUserInfo{} }
func (m *JoinUserInfo) String() string { return proto.CompactTextString(m) }
func (*JoinUserInfo) ProtoMessage()    {}
func (*JoinUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{12}
}
func (m *JoinUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinUserInfo.Unmarshal(m, b)
}
func (m *JoinUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinUserInfo.Marshal(b, m, deterministic)
}
func (dst *JoinUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinUserInfo.Merge(dst, src)
}
func (m *JoinUserInfo) XXX_Size() int {
	return xxx_messageInfo_JoinUserInfo.Size(m)
}
func (m *JoinUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_JoinUserInfo proto.InternalMessageInfo

func (m *JoinUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinUserInfo) GetCurrVal() uint32 {
	if m != nil {
		return m.CurrVal
	}
	return 0
}

func (m *JoinUserInfo) GetShowFlag() JoinUserInfo_Flag {
	if m != nil {
		return m.ShowFlag
	}
	return JoinUserInfo_Common
}

type ConditionMissionProgress struct {
	Status               ConditionMissionProgress_MissionStatus `protobuf:"varint,1,opt,name=status,proto3,enum=channel_lottery.ConditionMissionProgress_MissionStatus" json:"status,omitempty"`
	CurrMissionVal       uint32                                 `protobuf:"varint,2,opt,name=curr_mission_val,json=currMissionVal,proto3" json:"curr_mission_val,omitempty"`
	ProgressDesc         string                                 `protobuf:"bytes,3,opt,name=progress_desc,json=progressDesc,proto3" json:"progress_desc,omitempty"`
	UserList             []*JoinUserInfo                        `protobuf:"bytes,4,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *ConditionMissionProgress) Reset()         { *m = ConditionMissionProgress{} }
func (m *ConditionMissionProgress) String() string { return proto.CompactTextString(m) }
func (*ConditionMissionProgress) ProtoMessage()    {}
func (*ConditionMissionProgress) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{13}
}
func (m *ConditionMissionProgress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConditionMissionProgress.Unmarshal(m, b)
}
func (m *ConditionMissionProgress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConditionMissionProgress.Marshal(b, m, deterministic)
}
func (dst *ConditionMissionProgress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConditionMissionProgress.Merge(dst, src)
}
func (m *ConditionMissionProgress) XXX_Size() int {
	return xxx_messageInfo_ConditionMissionProgress.Size(m)
}
func (m *ConditionMissionProgress) XXX_DiscardUnknown() {
	xxx_messageInfo_ConditionMissionProgress.DiscardUnknown(m)
}

var xxx_messageInfo_ConditionMissionProgress proto.InternalMessageInfo

func (m *ConditionMissionProgress) GetStatus() ConditionMissionProgress_MissionStatus {
	if m != nil {
		return m.Status
	}
	return ConditionMissionProgress_NotJoin
}

func (m *ConditionMissionProgress) GetCurrMissionVal() uint32 {
	if m != nil {
		return m.CurrMissionVal
	}
	return 0
}

func (m *ConditionMissionProgress) GetProgressDesc() string {
	if m != nil {
		return m.ProgressDesc
	}
	return ""
}

func (m *ConditionMissionProgress) GetUserList() []*JoinUserInfo {
	if m != nil {
		return m.UserList
	}
	return nil
}

type ChannelLotteryInfo struct {
	LotteryId     uint32                         `protobuf:"varint,1,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id,omitempty"`
	SponsorUid    uint32                         `protobuf:"varint,2,opt,name=sponsor_uid,json=sponsorUid,proto3" json:"sponsor_uid,omitempty"`
	OfficialId    uint32                         `protobuf:"varint,3,opt,name=official_id,json=officialId,proto3" json:"official_id,omitempty"`
	ChannelId     uint32                         `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ConditionId   uint32                         `protobuf:"varint,5,opt,name=condition_id,json=conditionId,proto3" json:"condition_id,omitempty"`
	ConditionText string                         `protobuf:"bytes,6,opt,name=condition_text,json=conditionText,proto3" json:"condition_text,omitempty"`
	Limit         uint32                         `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	AwardGiftId   uint32                         `protobuf:"varint,8,opt,name=award_gift_id,json=awardGiftId,proto3" json:"award_gift_id,omitempty"`
	AwardGiftName string                         `protobuf:"bytes,9,opt,name=award_gift_name,json=awardGiftName,proto3" json:"award_gift_name,omitempty"`
	AwardGiftImg  string                         `protobuf:"bytes,10,opt,name=award_gift_img,json=awardGiftImg,proto3" json:"award_gift_img,omitempty"`
	UserList      []*ChannelLotteryInfo_UserInfo `protobuf:"bytes,11,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	BeginTime     int64                          `protobuf:"varint,12,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	Countdown     uint32                         `protobuf:"varint,13,opt,name=countdown,proto3" json:"countdown,omitempty"`
	Status        uint32                         `protobuf:"varint,14,opt,name=status,proto3" json:"status,omitempty"`
	// 自定义礼物id
	CustomGiftId   uint32 `protobuf:"varint,15,opt,name=custom_gift_id,json=customGiftId,proto3" json:"custom_gift_id,omitempty"`
	CustomGiftName string `protobuf:"bytes,16,opt,name=custom_gift_name,json=customGiftName,proto3" json:"custom_gift_name,omitempty"`
	CustomGiftImg  string `protobuf:"bytes,17,opt,name=custom_gift_img,json=customGiftImg,proto3" json:"custom_gift_img,omitempty"`
	ConditionName  string `protobuf:"bytes,18,opt,name=condition_name,json=conditionName,proto3" json:"condition_name,omitempty"`
	// TT6.11.0 新增
	ConditionGoalVal uint32              `protobuf:"varint,19,opt,name=condition_goal_val,json=conditionGoalVal,proto3" json:"condition_goal_val,omitempty"`
	GiftSendType     LotteryGiftSendType `protobuf:"varint,20,opt,name=gift_send_type,json=giftSendType,proto3,enum=channel_lottery.LotteryGiftSendType" json:"gift_send_type,omitempty"`
	TargetPeopleInfo *TargetPeopleInfo   `protobuf:"bytes,21,opt,name=target_people_info,json=targetPeopleInfo,proto3" json:"target_people_info,omitempty"`
	// TT6.16.0 新增
	ConditionBingoType     ConditionBingoType        `protobuf:"varint,22,opt,name=condition_bingo_type,json=conditionBingoType,proto3,enum=channel_lottery.ConditionBingoType" json:"condition_bingo_type,omitempty"`
	MissionProgress        *ConditionMissionProgress `protobuf:"bytes,23,opt,name=mission_progress,json=missionProgress,proto3" json:"mission_progress,omitempty"`
	ConditionNameHighlight string                    `protobuf:"bytes,24,opt,name=condition_name_highlight,json=conditionNameHighlight,proto3" json:"condition_name_highlight,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                  `json:"-"`
	XXX_unrecognized       []byte                    `json:"-"`
	XXX_sizecache          int32                     `json:"-"`
}

func (m *ChannelLotteryInfo) Reset()         { *m = ChannelLotteryInfo{} }
func (m *ChannelLotteryInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelLotteryInfo) ProtoMessage()    {}
func (*ChannelLotteryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{14}
}
func (m *ChannelLotteryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLotteryInfo.Unmarshal(m, b)
}
func (m *ChannelLotteryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLotteryInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelLotteryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLotteryInfo.Merge(dst, src)
}
func (m *ChannelLotteryInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelLotteryInfo.Size(m)
}
func (m *ChannelLotteryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLotteryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLotteryInfo proto.InternalMessageInfo

func (m *ChannelLotteryInfo) GetLotteryId() uint32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

func (m *ChannelLotteryInfo) GetSponsorUid() uint32 {
	if m != nil {
		return m.SponsorUid
	}
	return 0
}

func (m *ChannelLotteryInfo) GetOfficialId() uint32 {
	if m != nil {
		return m.OfficialId
	}
	return 0
}

func (m *ChannelLotteryInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelLotteryInfo) GetConditionId() uint32 {
	if m != nil {
		return m.ConditionId
	}
	return 0
}

func (m *ChannelLotteryInfo) GetConditionText() string {
	if m != nil {
		return m.ConditionText
	}
	return ""
}

func (m *ChannelLotteryInfo) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ChannelLotteryInfo) GetAwardGiftId() uint32 {
	if m != nil {
		return m.AwardGiftId
	}
	return 0
}

func (m *ChannelLotteryInfo) GetAwardGiftName() string {
	if m != nil {
		return m.AwardGiftName
	}
	return ""
}

func (m *ChannelLotteryInfo) GetAwardGiftImg() string {
	if m != nil {
		return m.AwardGiftImg
	}
	return ""
}

func (m *ChannelLotteryInfo) GetUserList() []*ChannelLotteryInfo_UserInfo {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *ChannelLotteryInfo) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ChannelLotteryInfo) GetCountdown() uint32 {
	if m != nil {
		return m.Countdown
	}
	return 0
}

func (m *ChannelLotteryInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelLotteryInfo) GetCustomGiftId() uint32 {
	if m != nil {
		return m.CustomGiftId
	}
	return 0
}

func (m *ChannelLotteryInfo) GetCustomGiftName() string {
	if m != nil {
		return m.CustomGiftName
	}
	return ""
}

func (m *ChannelLotteryInfo) GetCustomGiftImg() string {
	if m != nil {
		return m.CustomGiftImg
	}
	return ""
}

func (m *ChannelLotteryInfo) GetConditionName() string {
	if m != nil {
		return m.ConditionName
	}
	return ""
}

func (m *ChannelLotteryInfo) GetConditionGoalVal() uint32 {
	if m != nil {
		return m.ConditionGoalVal
	}
	return 0
}

func (m *ChannelLotteryInfo) GetGiftSendType() LotteryGiftSendType {
	if m != nil {
		return m.GiftSendType
	}
	return LotteryGiftSendType_LOTTERY_GIFT_TYPE_HAND
}

func (m *ChannelLotteryInfo) GetTargetPeopleInfo() *TargetPeopleInfo {
	if m != nil {
		return m.TargetPeopleInfo
	}
	return nil
}

func (m *ChannelLotteryInfo) GetConditionBingoType() ConditionBingoType {
	if m != nil {
		return m.ConditionBingoType
	}
	return ConditionBingoType_RandomBingo
}

func (m *ChannelLotteryInfo) GetMissionProgress() *ConditionMissionProgress {
	if m != nil {
		return m.MissionProgress
	}
	return nil
}

func (m *ChannelLotteryInfo) GetConditionNameHighlight() string {
	if m != nil {
		return m.ConditionNameHighlight
	}
	return ""
}

type ChannelLotteryInfo_UserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Ttid                 string   `protobuf:"bytes,5,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelLotteryInfo_UserInfo) Reset()         { *m = ChannelLotteryInfo_UserInfo{} }
func (m *ChannelLotteryInfo_UserInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelLotteryInfo_UserInfo) ProtoMessage()    {}
func (*ChannelLotteryInfo_UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{14, 0}
}
func (m *ChannelLotteryInfo_UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLotteryInfo_UserInfo.Unmarshal(m, b)
}
func (m *ChannelLotteryInfo_UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLotteryInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelLotteryInfo_UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLotteryInfo_UserInfo.Merge(dst, src)
}
func (m *ChannelLotteryInfo_UserInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelLotteryInfo_UserInfo.Size(m)
}
func (m *ChannelLotteryInfo_UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLotteryInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLotteryInfo_UserInfo proto.InternalMessageInfo

func (m *ChannelLotteryInfo_UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelLotteryInfo_UserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelLotteryInfo_UserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChannelLotteryInfo_UserInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ChannelLotteryInfo_UserInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

// 目标人群信息
type TargetPeopleInfo struct {
	PersonType           LotteryPersonType `protobuf:"varint,1,opt,name=person_type,json=personType,proto3,enum=channel_lottery.LotteryPersonType" json:"person_type,omitempty"`
	LimitVal             uint32            `protobuf:"varint,2,opt,name=limit_val,json=limitVal,proto3" json:"limit_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TargetPeopleInfo) Reset()         { *m = TargetPeopleInfo{} }
func (m *TargetPeopleInfo) String() string { return proto.CompactTextString(m) }
func (*TargetPeopleInfo) ProtoMessage()    {}
func (*TargetPeopleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{15}
}
func (m *TargetPeopleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TargetPeopleInfo.Unmarshal(m, b)
}
func (m *TargetPeopleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TargetPeopleInfo.Marshal(b, m, deterministic)
}
func (dst *TargetPeopleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TargetPeopleInfo.Merge(dst, src)
}
func (m *TargetPeopleInfo) XXX_Size() int {
	return xxx_messageInfo_TargetPeopleInfo.Size(m)
}
func (m *TargetPeopleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TargetPeopleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TargetPeopleInfo proto.InternalMessageInfo

func (m *TargetPeopleInfo) GetPersonType() LotteryPersonType {
	if m != nil {
		return m.PersonType
	}
	return LotteryPersonType_LOTTERY_PERSON_TYPE_UNKNOWN
}

func (m *TargetPeopleInfo) GetLimitVal() uint32 {
	if m != nil {
		return m.LimitVal
	}
	return 0
}

type BeginChannelLotteryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BeginChannelLotteryReq) Reset()         { *m = BeginChannelLotteryReq{} }
func (m *BeginChannelLotteryReq) String() string { return proto.CompactTextString(m) }
func (*BeginChannelLotteryReq) ProtoMessage()    {}
func (*BeginChannelLotteryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{16}
}
func (m *BeginChannelLotteryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeginChannelLotteryReq.Unmarshal(m, b)
}
func (m *BeginChannelLotteryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeginChannelLotteryReq.Marshal(b, m, deterministic)
}
func (dst *BeginChannelLotteryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeginChannelLotteryReq.Merge(dst, src)
}
func (m *BeginChannelLotteryReq) XXX_Size() int {
	return xxx_messageInfo_BeginChannelLotteryReq.Size(m)
}
func (m *BeginChannelLotteryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BeginChannelLotteryReq.DiscardUnknown(m)
}

var xxx_messageInfo_BeginChannelLotteryReq proto.InternalMessageInfo

func (m *BeginChannelLotteryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BeginChannelLotteryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type BeginChannelLotteryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BeginChannelLotteryResp) Reset()         { *m = BeginChannelLotteryResp{} }
func (m *BeginChannelLotteryResp) String() string { return proto.CompactTextString(m) }
func (*BeginChannelLotteryResp) ProtoMessage()    {}
func (*BeginChannelLotteryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{17}
}
func (m *BeginChannelLotteryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeginChannelLotteryResp.Unmarshal(m, b)
}
func (m *BeginChannelLotteryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeginChannelLotteryResp.Marshal(b, m, deterministic)
}
func (dst *BeginChannelLotteryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeginChannelLotteryResp.Merge(dst, src)
}
func (m *BeginChannelLotteryResp) XXX_Size() int {
	return xxx_messageInfo_BeginChannelLotteryResp.Size(m)
}
func (m *BeginChannelLotteryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BeginChannelLotteryResp.DiscardUnknown(m)
}

var xxx_messageInfo_BeginChannelLotteryResp proto.InternalMessageInfo

type SendChannelLotteryPresentReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32           `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LotteryId            uint32           `protobuf:"varint,3,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id,omitempty"`
	AppId                uint32           `protobuf:"varint,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MarketId             uint32           `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ServiceInfo          *ServiceCtrlInfo `protobuf:"bytes,6,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SendChannelLotteryPresentReq) Reset()         { *m = SendChannelLotteryPresentReq{} }
func (m *SendChannelLotteryPresentReq) String() string { return proto.CompactTextString(m) }
func (*SendChannelLotteryPresentReq) ProtoMessage()    {}
func (*SendChannelLotteryPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{18}
}
func (m *SendChannelLotteryPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelLotteryPresentReq.Unmarshal(m, b)
}
func (m *SendChannelLotteryPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelLotteryPresentReq.Marshal(b, m, deterministic)
}
func (dst *SendChannelLotteryPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelLotteryPresentReq.Merge(dst, src)
}
func (m *SendChannelLotteryPresentReq) XXX_Size() int {
	return xxx_messageInfo_SendChannelLotteryPresentReq.Size(m)
}
func (m *SendChannelLotteryPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelLotteryPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelLotteryPresentReq proto.InternalMessageInfo

func (m *SendChannelLotteryPresentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendChannelLotteryPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendChannelLotteryPresentReq) GetLotteryId() uint32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

func (m *SendChannelLotteryPresentReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SendChannelLotteryPresentReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SendChannelLotteryPresentReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

type SendChannelLotteryPresentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendChannelLotteryPresentResp) Reset()         { *m = SendChannelLotteryPresentResp{} }
func (m *SendChannelLotteryPresentResp) String() string { return proto.CompactTextString(m) }
func (*SendChannelLotteryPresentResp) ProtoMessage()    {}
func (*SendChannelLotteryPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{19}
}
func (m *SendChannelLotteryPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelLotteryPresentResp.Unmarshal(m, b)
}
func (m *SendChannelLotteryPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelLotteryPresentResp.Marshal(b, m, deterministic)
}
func (dst *SendChannelLotteryPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelLotteryPresentResp.Merge(dst, src)
}
func (m *SendChannelLotteryPresentResp) XXX_Size() int {
	return xxx_messageInfo_SendChannelLotteryPresentResp.Size(m)
}
func (m *SendChannelLotteryPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelLotteryPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelLotteryPresentResp proto.InternalMessageInfo

type ServiceCtrlInfo struct {
	ClientIp             string   `protobuf:"bytes,1,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	TerminalType         uint32   `protobuf:"varint,4,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,5,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ServiceCtrlInfo) Reset()         { *m = ServiceCtrlInfo{} }
func (m *ServiceCtrlInfo) String() string { return proto.CompactTextString(m) }
func (*ServiceCtrlInfo) ProtoMessage()    {}
func (*ServiceCtrlInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{20}
}
func (m *ServiceCtrlInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceCtrlInfo.Unmarshal(m, b)
}
func (m *ServiceCtrlInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceCtrlInfo.Marshal(b, m, deterministic)
}
func (dst *ServiceCtrlInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceCtrlInfo.Merge(dst, src)
}
func (m *ServiceCtrlInfo) XXX_Size() int {
	return xxx_messageInfo_ServiceCtrlInfo.Size(m)
}
func (m *ServiceCtrlInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceCtrlInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceCtrlInfo proto.InternalMessageInfo

func (m *ServiceCtrlInfo) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *ServiceCtrlInfo) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *ServiceCtrlInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ServiceCtrlInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *ServiceCtrlInfo) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

type BreakChannelLotteryReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BreakChannelLotteryReq) Reset()         { *m = BreakChannelLotteryReq{} }
func (m *BreakChannelLotteryReq) String() string { return proto.CompactTextString(m) }
func (*BreakChannelLotteryReq) ProtoMessage()    {}
func (*BreakChannelLotteryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{21}
}
func (m *BreakChannelLotteryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BreakChannelLotteryReq.Unmarshal(m, b)
}
func (m *BreakChannelLotteryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BreakChannelLotteryReq.Marshal(b, m, deterministic)
}
func (dst *BreakChannelLotteryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BreakChannelLotteryReq.Merge(dst, src)
}
func (m *BreakChannelLotteryReq) XXX_Size() int {
	return xxx_messageInfo_BreakChannelLotteryReq.Size(m)
}
func (m *BreakChannelLotteryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BreakChannelLotteryReq.DiscardUnknown(m)
}

var xxx_messageInfo_BreakChannelLotteryReq proto.InternalMessageInfo

func (m *BreakChannelLotteryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type BreakChannelLotteryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BreakChannelLotteryResp) Reset()         { *m = BreakChannelLotteryResp{} }
func (m *BreakChannelLotteryResp) String() string { return proto.CompactTextString(m) }
func (*BreakChannelLotteryResp) ProtoMessage()    {}
func (*BreakChannelLotteryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{22}
}
func (m *BreakChannelLotteryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BreakChannelLotteryResp.Unmarshal(m, b)
}
func (m *BreakChannelLotteryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BreakChannelLotteryResp.Marshal(b, m, deterministic)
}
func (dst *BreakChannelLotteryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BreakChannelLotteryResp.Merge(dst, src)
}
func (m *BreakChannelLotteryResp) XXX_Size() int {
	return xxx_messageInfo_BreakChannelLotteryResp.Size(m)
}
func (m *BreakChannelLotteryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BreakChannelLotteryResp.DiscardUnknown(m)
}

var xxx_messageInfo_BreakChannelLotteryResp proto.InternalMessageInfo

type GetChannelLotteryInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelLotteryInfoReq) Reset()         { *m = GetChannelLotteryInfoReq{} }
func (m *GetChannelLotteryInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelLotteryInfoReq) ProtoMessage()    {}
func (*GetChannelLotteryInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{23}
}
func (m *GetChannelLotteryInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLotteryInfoReq.Unmarshal(m, b)
}
func (m *GetChannelLotteryInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLotteryInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelLotteryInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLotteryInfoReq.Merge(dst, src)
}
func (m *GetChannelLotteryInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelLotteryInfoReq.Size(m)
}
func (m *GetChannelLotteryInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLotteryInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLotteryInfoReq proto.InternalMessageInfo

func (m *GetChannelLotteryInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelLotteryInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelLotteryInfoResp struct {
	LotteryInfo          *ChannelLotteryInfo `protobuf:"bytes,1,opt,name=lottery_info,json=lotteryInfo,proto3" json:"lottery_info,omitempty"`
	Joined               bool                `protobuf:"varint,2,opt,name=joined,proto3" json:"joined,omitempty"`
	CurrConditionVal     uint32              `protobuf:"varint,3,opt,name=curr_condition_val,json=currConditionVal,proto3" json:"curr_condition_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetChannelLotteryInfoResp) Reset()         { *m = GetChannelLotteryInfoResp{} }
func (m *GetChannelLotteryInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelLotteryInfoResp) ProtoMessage()    {}
func (*GetChannelLotteryInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{24}
}
func (m *GetChannelLotteryInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLotteryInfoResp.Unmarshal(m, b)
}
func (m *GetChannelLotteryInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLotteryInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelLotteryInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLotteryInfoResp.Merge(dst, src)
}
func (m *GetChannelLotteryInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelLotteryInfoResp.Size(m)
}
func (m *GetChannelLotteryInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLotteryInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLotteryInfoResp proto.InternalMessageInfo

func (m *GetChannelLotteryInfoResp) GetLotteryInfo() *ChannelLotteryInfo {
	if m != nil {
		return m.LotteryInfo
	}
	return nil
}

func (m *GetChannelLotteryInfoResp) GetJoined() bool {
	if m != nil {
		return m.Joined
	}
	return false
}

func (m *GetChannelLotteryInfoResp) GetCurrConditionVal() uint32 {
	if m != nil {
		return m.CurrConditionVal
	}
	return 0
}

type LotterySimple struct {
	LotteryId            uint32   `protobuf:"varint,1,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GiftName             string   `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LotterySimple) Reset()         { *m = LotterySimple{} }
func (m *LotterySimple) String() string { return proto.CompactTextString(m) }
func (*LotterySimple) ProtoMessage()    {}
func (*LotterySimple) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{25}
}
func (m *LotterySimple) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotterySimple.Unmarshal(m, b)
}
func (m *LotterySimple) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotterySimple.Marshal(b, m, deterministic)
}
func (dst *LotterySimple) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotterySimple.Merge(dst, src)
}
func (m *LotterySimple) XXX_Size() int {
	return xxx_messageInfo_LotterySimple.Size(m)
}
func (m *LotterySimple) XXX_DiscardUnknown() {
	xxx_messageInfo_LotterySimple.DiscardUnknown(m)
}

var xxx_messageInfo_LotterySimple proto.InternalMessageInfo

func (m *LotterySimple) GetLotteryId() uint32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

func (m *LotterySimple) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LotterySimple) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

type BatchGetChannelLotteryIdReq struct {
	ChannelIdList        []uint32 `protobuf:"varint,1,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelLotteryIdReq) Reset()         { *m = BatchGetChannelLotteryIdReq{} }
func (m *BatchGetChannelLotteryIdReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelLotteryIdReq) ProtoMessage()    {}
func (*BatchGetChannelLotteryIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{26}
}
func (m *BatchGetChannelLotteryIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelLotteryIdReq.Unmarshal(m, b)
}
func (m *BatchGetChannelLotteryIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelLotteryIdReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelLotteryIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelLotteryIdReq.Merge(dst, src)
}
func (m *BatchGetChannelLotteryIdReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelLotteryIdReq.Size(m)
}
func (m *BatchGetChannelLotteryIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelLotteryIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelLotteryIdReq proto.InternalMessageInfo

func (m *BatchGetChannelLotteryIdReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetChannelLotteryIdResp struct {
	LotteryIdMap         map[uint32]uint32         `protobuf:"bytes,1,rep,name=lottery_id_map,json=lotteryIdMap,proto3" json:"lottery_id_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	LotterySimpleMap     map[uint32]*LotterySimple `protobuf:"bytes,2,rep,name=lottery_simple_map,json=lotterySimpleMap,proto3" json:"lottery_simple_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *BatchGetChannelLotteryIdResp) Reset()         { *m = BatchGetChannelLotteryIdResp{} }
func (m *BatchGetChannelLotteryIdResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelLotteryIdResp) ProtoMessage()    {}
func (*BatchGetChannelLotteryIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{27}
}
func (m *BatchGetChannelLotteryIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelLotteryIdResp.Unmarshal(m, b)
}
func (m *BatchGetChannelLotteryIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelLotteryIdResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelLotteryIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelLotteryIdResp.Merge(dst, src)
}
func (m *BatchGetChannelLotteryIdResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelLotteryIdResp.Size(m)
}
func (m *BatchGetChannelLotteryIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelLotteryIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelLotteryIdResp proto.InternalMessageInfo

func (m *BatchGetChannelLotteryIdResp) GetLotteryIdMap() map[uint32]uint32 {
	if m != nil {
		return m.LotteryIdMap
	}
	return nil
}

func (m *BatchGetChannelLotteryIdResp) GetLotterySimpleMap() map[uint32]*LotterySimple {
	if m != nil {
		return m.LotterySimpleMap
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type ChangeOfficialInfoReq struct {
	OfficialChannelId    uint32   `protobuf:"varint,1,opt,name=officialChannelId,proto3" json:"officialChannelId,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeOfficialInfoReq) Reset()         { *m = ChangeOfficialInfoReq{} }
func (m *ChangeOfficialInfoReq) String() string { return proto.CompactTextString(m) }
func (*ChangeOfficialInfoReq) ProtoMessage()    {}
func (*ChangeOfficialInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{28}
}
func (m *ChangeOfficialInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeOfficialInfoReq.Unmarshal(m, b)
}
func (m *ChangeOfficialInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeOfficialInfoReq.Marshal(b, m, deterministic)
}
func (dst *ChangeOfficialInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeOfficialInfoReq.Merge(dst, src)
}
func (m *ChangeOfficialInfoReq) XXX_Size() int {
	return xxx_messageInfo_ChangeOfficialInfoReq.Size(m)
}
func (m *ChangeOfficialInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeOfficialInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeOfficialInfoReq proto.InternalMessageInfo

func (m *ChangeOfficialInfoReq) GetOfficialChannelId() uint32 {
	if m != nil {
		return m.OfficialChannelId
	}
	return 0
}

func (m *ChangeOfficialInfoReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type ChangeOfficialInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeOfficialInfoResp) Reset()         { *m = ChangeOfficialInfoResp{} }
func (m *ChangeOfficialInfoResp) String() string { return proto.CompactTextString(m) }
func (*ChangeOfficialInfoResp) ProtoMessage()    {}
func (*ChangeOfficialInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{29}
}
func (m *ChangeOfficialInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeOfficialInfoResp.Unmarshal(m, b)
}
func (m *ChangeOfficialInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeOfficialInfoResp.Marshal(b, m, deterministic)
}
func (dst *ChangeOfficialInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeOfficialInfoResp.Merge(dst, src)
}
func (m *ChangeOfficialInfoResp) XXX_Size() int {
	return xxx_messageInfo_ChangeOfficialInfoResp.Size(m)
}
func (m *ChangeOfficialInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeOfficialInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeOfficialInfoResp proto.InternalMessageInfo

// 自定义礼物
type GetCustomGiftReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCustomGiftReq) Reset()         { *m = GetCustomGiftReq{} }
func (m *GetCustomGiftReq) String() string { return proto.CompactTextString(m) }
func (*GetCustomGiftReq) ProtoMessage()    {}
func (*GetCustomGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{30}
}
func (m *GetCustomGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomGiftReq.Unmarshal(m, b)
}
func (m *GetCustomGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomGiftReq.Marshal(b, m, deterministic)
}
func (dst *GetCustomGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomGiftReq.Merge(dst, src)
}
func (m *GetCustomGiftReq) XXX_Size() int {
	return xxx_messageInfo_GetCustomGiftReq.Size(m)
}
func (m *GetCustomGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomGiftReq proto.InternalMessageInfo

func (m *GetCustomGiftReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetCustomGiftResp struct {
	Gift                 *CustomGift `protobuf:"bytes,1,opt,name=gift,proto3" json:"gift,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetCustomGiftResp) Reset()         { *m = GetCustomGiftResp{} }
func (m *GetCustomGiftResp) String() string { return proto.CompactTextString(m) }
func (*GetCustomGiftResp) ProtoMessage()    {}
func (*GetCustomGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{31}
}
func (m *GetCustomGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomGiftResp.Unmarshal(m, b)
}
func (m *GetCustomGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomGiftResp.Marshal(b, m, deterministic)
}
func (dst *GetCustomGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomGiftResp.Merge(dst, src)
}
func (m *GetCustomGiftResp) XXX_Size() int {
	return xxx_messageInfo_GetCustomGiftResp.Size(m)
}
func (m *GetCustomGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomGiftResp proto.InternalMessageInfo

func (m *GetCustomGiftResp) GetGift() *CustomGift {
	if m != nil {
		return m.Gift
	}
	return nil
}

type SetCustomGiftReq struct {
	Gift                 *CustomGift `protobuf:"bytes,1,opt,name=gift,proto3" json:"gift,omitempty"`
	Delete               bool        `protobuf:"varint,2,opt,name=delete,proto3" json:"delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetCustomGiftReq) Reset()         { *m = SetCustomGiftReq{} }
func (m *SetCustomGiftReq) String() string { return proto.CompactTextString(m) }
func (*SetCustomGiftReq) ProtoMessage()    {}
func (*SetCustomGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{32}
}
func (m *SetCustomGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCustomGiftReq.Unmarshal(m, b)
}
func (m *SetCustomGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCustomGiftReq.Marshal(b, m, deterministic)
}
func (dst *SetCustomGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCustomGiftReq.Merge(dst, src)
}
func (m *SetCustomGiftReq) XXX_Size() int {
	return xxx_messageInfo_SetCustomGiftReq.Size(m)
}
func (m *SetCustomGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCustomGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCustomGiftReq proto.InternalMessageInfo

func (m *SetCustomGiftReq) GetGift() *CustomGift {
	if m != nil {
		return m.Gift
	}
	return nil
}

func (m *SetCustomGiftReq) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

type SetCustomGiftResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCustomGiftResp) Reset()         { *m = SetCustomGiftResp{} }
func (m *SetCustomGiftResp) String() string { return proto.CompactTextString(m) }
func (*SetCustomGiftResp) ProtoMessage()    {}
func (*SetCustomGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{33}
}
func (m *SetCustomGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCustomGiftResp.Unmarshal(m, b)
}
func (m *SetCustomGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCustomGiftResp.Marshal(b, m, deterministic)
}
func (dst *SetCustomGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCustomGiftResp.Merge(dst, src)
}
func (m *SetCustomGiftResp) XXX_Size() int {
	return xxx_messageInfo_SetCustomGiftResp.Size(m)
}
func (m *SetCustomGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCustomGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCustomGiftResp proto.InternalMessageInfo

func (m *SetCustomGiftResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type SearchCustomGiftsReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchCustomGiftsReq) Reset()         { *m = SearchCustomGiftsReq{} }
func (m *SearchCustomGiftsReq) String() string { return proto.CompactTextString(m) }
func (*SearchCustomGiftsReq) ProtoMessage()    {}
func (*SearchCustomGiftsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{34}
}
func (m *SearchCustomGiftsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchCustomGiftsReq.Unmarshal(m, b)
}
func (m *SearchCustomGiftsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchCustomGiftsReq.Marshal(b, m, deterministic)
}
func (dst *SearchCustomGiftsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchCustomGiftsReq.Merge(dst, src)
}
func (m *SearchCustomGiftsReq) XXX_Size() int {
	return xxx_messageInfo_SearchCustomGiftsReq.Size(m)
}
func (m *SearchCustomGiftsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchCustomGiftsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchCustomGiftsReq proto.InternalMessageInfo

func (m *SearchCustomGiftsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchCustomGiftsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchCustomGiftsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SearchCustomGiftsResp struct {
	Gifts                []*CustomGift `protobuf:"bytes,1,rep,name=gifts,proto3" json:"gifts,omitempty"`
	Count                uint32        `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SearchCustomGiftsResp) Reset()         { *m = SearchCustomGiftsResp{} }
func (m *SearchCustomGiftsResp) String() string { return proto.CompactTextString(m) }
func (*SearchCustomGiftsResp) ProtoMessage()    {}
func (*SearchCustomGiftsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{35}
}
func (m *SearchCustomGiftsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchCustomGiftsResp.Unmarshal(m, b)
}
func (m *SearchCustomGiftsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchCustomGiftsResp.Marshal(b, m, deterministic)
}
func (dst *SearchCustomGiftsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchCustomGiftsResp.Merge(dst, src)
}
func (m *SearchCustomGiftsResp) XXX_Size() int {
	return xxx_messageInfo_SearchCustomGiftsResp.Size(m)
}
func (m *SearchCustomGiftsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchCustomGiftsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchCustomGiftsResp proto.InternalMessageInfo

func (m *SearchCustomGiftsResp) GetGifts() []*CustomGift {
	if m != nil {
		return m.Gifts
	}
	return nil
}

func (m *SearchCustomGiftsResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type CustomGift struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,4,opt,name=img,proto3" json:"img,omitempty"`
	UpdateTime           int64    `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomGift) Reset()         { *m = CustomGift{} }
func (m *CustomGift) String() string { return proto.CompactTextString(m) }
func (*CustomGift) ProtoMessage()    {}
func (*CustomGift) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{36}
}
func (m *CustomGift) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomGift.Unmarshal(m, b)
}
func (m *CustomGift) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomGift.Marshal(b, m, deterministic)
}
func (dst *CustomGift) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomGift.Merge(dst, src)
}
func (m *CustomGift) XXX_Size() int {
	return xxx_messageInfo_CustomGift.Size(m)
}
func (m *CustomGift) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomGift.DiscardUnknown(m)
}

var xxx_messageInfo_CustomGift proto.InternalMessageInfo

func (m *CustomGift) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CustomGift) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CustomGift) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CustomGift) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *CustomGift) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 自定义开放抽奖
type GetLotteryOpenReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLotteryOpenReq) Reset()         { *m = GetLotteryOpenReq{} }
func (m *GetLotteryOpenReq) String() string { return proto.CompactTextString(m) }
func (*GetLotteryOpenReq) ProtoMessage()    {}
func (*GetLotteryOpenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{37}
}
func (m *GetLotteryOpenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLotteryOpenReq.Unmarshal(m, b)
}
func (m *GetLotteryOpenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLotteryOpenReq.Marshal(b, m, deterministic)
}
func (dst *GetLotteryOpenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLotteryOpenReq.Merge(dst, src)
}
func (m *GetLotteryOpenReq) XXX_Size() int {
	return xxx_messageInfo_GetLotteryOpenReq.Size(m)
}
func (m *GetLotteryOpenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLotteryOpenReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLotteryOpenReq proto.InternalMessageInfo

func (m *GetLotteryOpenReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetLotteryOpenResp struct {
	Data                 *LotteryOpen `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetLotteryOpenResp) Reset()         { *m = GetLotteryOpenResp{} }
func (m *GetLotteryOpenResp) String() string { return proto.CompactTextString(m) }
func (*GetLotteryOpenResp) ProtoMessage()    {}
func (*GetLotteryOpenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{38}
}
func (m *GetLotteryOpenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLotteryOpenResp.Unmarshal(m, b)
}
func (m *GetLotteryOpenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLotteryOpenResp.Marshal(b, m, deterministic)
}
func (dst *GetLotteryOpenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLotteryOpenResp.Merge(dst, src)
}
func (m *GetLotteryOpenResp) XXX_Size() int {
	return xxx_messageInfo_GetLotteryOpenResp.Size(m)
}
func (m *GetLotteryOpenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLotteryOpenResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLotteryOpenResp proto.InternalMessageInfo

func (m *GetLotteryOpenResp) GetData() *LotteryOpen {
	if m != nil {
		return m.Data
	}
	return nil
}

type SetLotteryOpenReq struct {
	Data                 *LotteryOpen `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Delete               bool         `protobuf:"varint,2,opt,name=delete,proto3" json:"delete,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetLotteryOpenReq) Reset()         { *m = SetLotteryOpenReq{} }
func (m *SetLotteryOpenReq) String() string { return proto.CompactTextString(m) }
func (*SetLotteryOpenReq) ProtoMessage()    {}
func (*SetLotteryOpenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{39}
}
func (m *SetLotteryOpenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLotteryOpenReq.Unmarshal(m, b)
}
func (m *SetLotteryOpenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLotteryOpenReq.Marshal(b, m, deterministic)
}
func (dst *SetLotteryOpenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLotteryOpenReq.Merge(dst, src)
}
func (m *SetLotteryOpenReq) XXX_Size() int {
	return xxx_messageInfo_SetLotteryOpenReq.Size(m)
}
func (m *SetLotteryOpenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLotteryOpenReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetLotteryOpenReq proto.InternalMessageInfo

func (m *SetLotteryOpenReq) GetData() *LotteryOpen {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *SetLotteryOpenReq) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

type SetLotteryOpenResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLotteryOpenResp) Reset()         { *m = SetLotteryOpenResp{} }
func (m *SetLotteryOpenResp) String() string { return proto.CompactTextString(m) }
func (*SetLotteryOpenResp) ProtoMessage()    {}
func (*SetLotteryOpenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{40}
}
func (m *SetLotteryOpenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLotteryOpenResp.Unmarshal(m, b)
}
func (m *SetLotteryOpenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLotteryOpenResp.Marshal(b, m, deterministic)
}
func (dst *SetLotteryOpenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLotteryOpenResp.Merge(dst, src)
}
func (m *SetLotteryOpenResp) XXX_Size() int {
	return xxx_messageInfo_SetLotteryOpenResp.Size(m)
}
func (m *SetLotteryOpenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLotteryOpenResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetLotteryOpenResp proto.InternalMessageInfo

func (m *SetLotteryOpenResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type SearchLotteryOpensReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	DisplayId            uint32   `protobuf:"varint,3,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchLotteryOpensReq) Reset()         { *m = SearchLotteryOpensReq{} }
func (m *SearchLotteryOpensReq) String() string { return proto.CompactTextString(m) }
func (*SearchLotteryOpensReq) ProtoMessage()    {}
func (*SearchLotteryOpensReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{41}
}
func (m *SearchLotteryOpensReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchLotteryOpensReq.Unmarshal(m, b)
}
func (m *SearchLotteryOpensReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchLotteryOpensReq.Marshal(b, m, deterministic)
}
func (dst *SearchLotteryOpensReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchLotteryOpensReq.Merge(dst, src)
}
func (m *SearchLotteryOpensReq) XXX_Size() int {
	return xxx_messageInfo_SearchLotteryOpensReq.Size(m)
}
func (m *SearchLotteryOpensReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchLotteryOpensReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchLotteryOpensReq proto.InternalMessageInfo

func (m *SearchLotteryOpensReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchLotteryOpensReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchLotteryOpensReq) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *SearchLotteryOpensReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SearchLotteryOpensResp struct {
	DataList             []*LotteryOpen `protobuf:"bytes,1,rep,name=data_list,json=dataList,proto3" json:"data_list,omitempty"`
	Count                uint32         `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SearchLotteryOpensResp) Reset()         { *m = SearchLotteryOpensResp{} }
func (m *SearchLotteryOpensResp) String() string { return proto.CompactTextString(m) }
func (*SearchLotteryOpensResp) ProtoMessage()    {}
func (*SearchLotteryOpensResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{42}
}
func (m *SearchLotteryOpensResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchLotteryOpensResp.Unmarshal(m, b)
}
func (m *SearchLotteryOpensResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchLotteryOpensResp.Marshal(b, m, deterministic)
}
func (dst *SearchLotteryOpensResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchLotteryOpensResp.Merge(dst, src)
}
func (m *SearchLotteryOpensResp) XXX_Size() int {
	return xxx_messageInfo_SearchLotteryOpensResp.Size(m)
}
func (m *SearchLotteryOpensResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchLotteryOpensResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchLotteryOpensResp proto.InternalMessageInfo

func (m *SearchLotteryOpensResp) GetDataList() []*LotteryOpen {
	if m != nil {
		return m.DataList
	}
	return nil
}

func (m *SearchLotteryOpensResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type LotteryOpen struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DisplayId            uint32   `protobuf:"varint,2,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeginTime            int64    `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	UpdateTime           int64    `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Conditions           []uint32 `protobuf:"varint,7,rep,packed,name=conditions,proto3" json:"conditions,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LotteryOpen) Reset()         { *m = LotteryOpen{} }
func (m *LotteryOpen) String() string { return proto.CompactTextString(m) }
func (*LotteryOpen) ProtoMessage()    {}
func (*LotteryOpen) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{43}
}
func (m *LotteryOpen) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryOpen.Unmarshal(m, b)
}
func (m *LotteryOpen) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryOpen.Marshal(b, m, deterministic)
}
func (dst *LotteryOpen) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryOpen.Merge(dst, src)
}
func (m *LotteryOpen) XXX_Size() int {
	return xxx_messageInfo_LotteryOpen.Size(m)
}
func (m *LotteryOpen) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryOpen.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryOpen proto.InternalMessageInfo

func (m *LotteryOpen) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LotteryOpen) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *LotteryOpen) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LotteryOpen) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *LotteryOpen) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *LotteryOpen) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *LotteryOpen) GetConditions() []uint32 {
	if m != nil {
		return m.Conditions
	}
	return nil
}

type FreezeOrderResultReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeOrderResultReq) Reset()         { *m = FreezeOrderResultReq{} }
func (m *FreezeOrderResultReq) String() string { return proto.CompactTextString(m) }
func (*FreezeOrderResultReq) ProtoMessage()    {}
func (*FreezeOrderResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{44}
}
func (m *FreezeOrderResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeOrderResultReq.Unmarshal(m, b)
}
func (m *FreezeOrderResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeOrderResultReq.Marshal(b, m, deterministic)
}
func (dst *FreezeOrderResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeOrderResultReq.Merge(dst, src)
}
func (m *FreezeOrderResultReq) XXX_Size() int {
	return xxx_messageInfo_FreezeOrderResultReq.Size(m)
}
func (m *FreezeOrderResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeOrderResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeOrderResultReq proto.InternalMessageInfo

func (m *FreezeOrderResultReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type FreezeOrderResultResp struct {
	Confirm              bool     `protobuf:"varint,1,opt,name=confirm,proto3" json:"confirm,omitempty"`
	CommitCount          uint32   `protobuf:"varint,2,opt,name=commit_count,json=commitCount,proto3" json:"commit_count,omitempty"`
	CommitTime           uint32   `protobuf:"varint,3,opt,name=commit_time,json=commitTime,proto3" json:"commit_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FreezeOrderResultResp) Reset()         { *m = FreezeOrderResultResp{} }
func (m *FreezeOrderResultResp) String() string { return proto.CompactTextString(m) }
func (*FreezeOrderResultResp) ProtoMessage()    {}
func (*FreezeOrderResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{45}
}
func (m *FreezeOrderResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreezeOrderResultResp.Unmarshal(m, b)
}
func (m *FreezeOrderResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreezeOrderResultResp.Marshal(b, m, deterministic)
}
func (dst *FreezeOrderResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreezeOrderResultResp.Merge(dst, src)
}
func (m *FreezeOrderResultResp) XXX_Size() int {
	return xxx_messageInfo_FreezeOrderResultResp.Size(m)
}
func (m *FreezeOrderResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FreezeOrderResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_FreezeOrderResultResp proto.InternalMessageInfo

func (m *FreezeOrderResultResp) GetConfirm() bool {
	if m != nil {
		return m.Confirm
	}
	return false
}

func (m *FreezeOrderResultResp) GetCommitCount() uint32 {
	if m != nil {
		return m.CommitCount
	}
	return 0
}

func (m *FreezeOrderResultResp) GetCommitTime() uint32 {
	if m != nil {
		return m.CommitTime
	}
	return 0
}

type SetLotteryOpenListReq struct {
	Data                 []*LotteryOpen `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Delete               bool           `protobuf:"varint,2,opt,name=delete,proto3" json:"delete,omitempty"`
	SetSource            uint32         `protobuf:"varint,3,opt,name=set_source,json=setSource,proto3" json:"set_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetLotteryOpenListReq) Reset()         { *m = SetLotteryOpenListReq{} }
func (m *SetLotteryOpenListReq) String() string { return proto.CompactTextString(m) }
func (*SetLotteryOpenListReq) ProtoMessage()    {}
func (*SetLotteryOpenListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{46}
}
func (m *SetLotteryOpenListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLotteryOpenListReq.Unmarshal(m, b)
}
func (m *SetLotteryOpenListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLotteryOpenListReq.Marshal(b, m, deterministic)
}
func (dst *SetLotteryOpenListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLotteryOpenListReq.Merge(dst, src)
}
func (m *SetLotteryOpenListReq) XXX_Size() int {
	return xxx_messageInfo_SetLotteryOpenListReq.Size(m)
}
func (m *SetLotteryOpenListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLotteryOpenListReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetLotteryOpenListReq proto.InternalMessageInfo

func (m *SetLotteryOpenListReq) GetData() []*LotteryOpen {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *SetLotteryOpenListReq) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

func (m *SetLotteryOpenListReq) GetSetSource() uint32 {
	if m != nil {
		return m.SetSource
	}
	return 0
}

type SetLotteryOpenListResp struct {
	TimeConflictId       []uint32 `protobuf:"varint,1,rep,packed,name=time_conflict_id,json=timeConflictId,proto3" json:"time_conflict_id,omitempty"`
	WrongChannelType     []uint32 `protobuf:"varint,2,rep,packed,name=wrong_channel_type,json=wrongChannelType,proto3" json:"wrong_channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLotteryOpenListResp) Reset()         { *m = SetLotteryOpenListResp{} }
func (m *SetLotteryOpenListResp) String() string { return proto.CompactTextString(m) }
func (*SetLotteryOpenListResp) ProtoMessage()    {}
func (*SetLotteryOpenListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{47}
}
func (m *SetLotteryOpenListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLotteryOpenListResp.Unmarshal(m, b)
}
func (m *SetLotteryOpenListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLotteryOpenListResp.Marshal(b, m, deterministic)
}
func (dst *SetLotteryOpenListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLotteryOpenListResp.Merge(dst, src)
}
func (m *SetLotteryOpenListResp) XXX_Size() int {
	return xxx_messageInfo_SetLotteryOpenListResp.Size(m)
}
func (m *SetLotteryOpenListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLotteryOpenListResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetLotteryOpenListResp proto.InternalMessageInfo

func (m *SetLotteryOpenListResp) GetTimeConflictId() []uint32 {
	if m != nil {
		return m.TimeConflictId
	}
	return nil
}

func (m *SetLotteryOpenListResp) GetWrongChannelType() []uint32 {
	if m != nil {
		return m.WrongChannelType
	}
	return nil
}

// 通过分享链接进房上报接口
type ReportEnterShareChannelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LotteryId            uint32   `protobuf:"varint,3,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id,omitempty"`
	ShareFromUid         uint32   `protobuf:"varint,4,opt,name=share_from_uid,json=shareFromUid,proto3" json:"share_from_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportEnterShareChannelReq) Reset()         { *m = ReportEnterShareChannelReq{} }
func (m *ReportEnterShareChannelReq) String() string { return proto.CompactTextString(m) }
func (*ReportEnterShareChannelReq) ProtoMessage()    {}
func (*ReportEnterShareChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{48}
}
func (m *ReportEnterShareChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportEnterShareChannelReq.Unmarshal(m, b)
}
func (m *ReportEnterShareChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportEnterShareChannelReq.Marshal(b, m, deterministic)
}
func (dst *ReportEnterShareChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportEnterShareChannelReq.Merge(dst, src)
}
func (m *ReportEnterShareChannelReq) XXX_Size() int {
	return xxx_messageInfo_ReportEnterShareChannelReq.Size(m)
}
func (m *ReportEnterShareChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportEnterShareChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportEnterShareChannelReq proto.InternalMessageInfo

func (m *ReportEnterShareChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportEnterShareChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportEnterShareChannelReq) GetLotteryId() uint32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

func (m *ReportEnterShareChannelReq) GetShareFromUid() uint32 {
	if m != nil {
		return m.ShareFromUid
	}
	return 0
}

type ReportEnterShareChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportEnterShareChannelResp) Reset()         { *m = ReportEnterShareChannelResp{} }
func (m *ReportEnterShareChannelResp) String() string { return proto.CompactTextString(m) }
func (*ReportEnterShareChannelResp) ProtoMessage()    {}
func (*ReportEnterShareChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{49}
}
func (m *ReportEnterShareChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportEnterShareChannelResp.Unmarshal(m, b)
}
func (m *ReportEnterShareChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportEnterShareChannelResp.Marshal(b, m, deterministic)
}
func (dst *ReportEnterShareChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportEnterShareChannelResp.Merge(dst, src)
}
func (m *ReportEnterShareChannelResp) XXX_Size() int {
	return xxx_messageInfo_ReportEnterShareChannelResp.Size(m)
}
func (m *ReportEnterShareChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportEnterShareChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportEnterShareChannelResp proto.InternalMessageInfo

// 获取抽奖期间进房人数
type GetEnterChannelUserCntReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LotteryId            uint32   `protobuf:"varint,3,opt,name=lottery_id,json=lotteryId,proto3" json:"lottery_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEnterChannelUserCntReq) Reset()         { *m = GetEnterChannelUserCntReq{} }
func (m *GetEnterChannelUserCntReq) String() string { return proto.CompactTextString(m) }
func (*GetEnterChannelUserCntReq) ProtoMessage()    {}
func (*GetEnterChannelUserCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{50}
}
func (m *GetEnterChannelUserCntReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEnterChannelUserCntReq.Unmarshal(m, b)
}
func (m *GetEnterChannelUserCntReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEnterChannelUserCntReq.Marshal(b, m, deterministic)
}
func (dst *GetEnterChannelUserCntReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEnterChannelUserCntReq.Merge(dst, src)
}
func (m *GetEnterChannelUserCntReq) XXX_Size() int {
	return xxx_messageInfo_GetEnterChannelUserCntReq.Size(m)
}
func (m *GetEnterChannelUserCntReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEnterChannelUserCntReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEnterChannelUserCntReq proto.InternalMessageInfo

func (m *GetEnterChannelUserCntReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetEnterChannelUserCntReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetEnterChannelUserCntReq) GetLotteryId() uint32 {
	if m != nil {
		return m.LotteryId
	}
	return 0
}

type GetEnterChannelUserCntResp struct {
	Cnt                  uint32   `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEnterChannelUserCntResp) Reset()         { *m = GetEnterChannelUserCntResp{} }
func (m *GetEnterChannelUserCntResp) String() string { return proto.CompactTextString(m) }
func (*GetEnterChannelUserCntResp) ProtoMessage()    {}
func (*GetEnterChannelUserCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_lottery_7257bac15b05f78d, []int{51}
}
func (m *GetEnterChannelUserCntResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEnterChannelUserCntResp.Unmarshal(m, b)
}
func (m *GetEnterChannelUserCntResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEnterChannelUserCntResp.Marshal(b, m, deterministic)
}
func (dst *GetEnterChannelUserCntResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEnterChannelUserCntResp.Merge(dst, src)
}
func (m *GetEnterChannelUserCntResp) XXX_Size() int {
	return xxx_messageInfo_GetEnterChannelUserCntResp.Size(m)
}
func (m *GetEnterChannelUserCntResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEnterChannelUserCntResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEnterChannelUserCntResp proto.InternalMessageInfo

func (m *GetEnterChannelUserCntResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func init() {
	proto.RegisterType((*ShowChannelLotterySettingReq)(nil), "channel_lottery.ShowChannelLotterySettingReq")
	proto.RegisterType((*ShowChannelLotterySettingResp)(nil), "channel_lottery.ShowChannelLotterySettingResp")
	proto.RegisterType((*OptionButton)(nil), "channel_lottery.OptionButton")
	proto.RegisterType((*GetChannelLotterySettingReq)(nil), "channel_lottery.GetChannelLotterySettingReq")
	proto.RegisterType((*GetChannelLotterySettingResp)(nil), "channel_lottery.GetChannelLotterySettingResp")
	proto.RegisterType((*GetChannelLotterySettingResp_TargetPeopleOption)(nil), "channel_lottery.GetChannelLotterySettingResp.TargetPeopleOption")
	proto.RegisterType((*Condition)(nil), "channel_lottery.Condition")
	proto.RegisterType((*SetChannelLotteryInfoReq)(nil), "channel_lottery.SetChannelLotteryInfoReq")
	proto.RegisterType((*SetChannelLotteryInfoResp)(nil), "channel_lottery.SetChannelLotteryInfoResp")
	proto.RegisterType((*JoinChannelLotteryReq)(nil), "channel_lottery.JoinChannelLotteryReq")
	proto.RegisterType((*JoinChannelLotteryResp)(nil), "channel_lottery.JoinChannelLotteryResp")
	proto.RegisterType((*GetChannelLotteryInfoListReq)(nil), "channel_lottery.GetChannelLotteryInfoListReq")
	proto.RegisterType((*GetChannelLotteryInfoListResp)(nil), "channel_lottery.GetChannelLotteryInfoListResp")
	proto.RegisterType((*JoinUserInfo)(nil), "channel_lottery.JoinUserInfo")
	proto.RegisterType((*ConditionMissionProgress)(nil), "channel_lottery.ConditionMissionProgress")
	proto.RegisterType((*ChannelLotteryInfo)(nil), "channel_lottery.ChannelLotteryInfo")
	proto.RegisterType((*ChannelLotteryInfo_UserInfo)(nil), "channel_lottery.ChannelLotteryInfo.UserInfo")
	proto.RegisterType((*TargetPeopleInfo)(nil), "channel_lottery.TargetPeopleInfo")
	proto.RegisterType((*BeginChannelLotteryReq)(nil), "channel_lottery.BeginChannelLotteryReq")
	proto.RegisterType((*BeginChannelLotteryResp)(nil), "channel_lottery.BeginChannelLotteryResp")
	proto.RegisterType((*SendChannelLotteryPresentReq)(nil), "channel_lottery.SendChannelLotteryPresentReq")
	proto.RegisterType((*SendChannelLotteryPresentResp)(nil), "channel_lottery.SendChannelLotteryPresentResp")
	proto.RegisterType((*ServiceCtrlInfo)(nil), "channel_lottery.ServiceCtrlInfo")
	proto.RegisterType((*BreakChannelLotteryReq)(nil), "channel_lottery.BreakChannelLotteryReq")
	proto.RegisterType((*BreakChannelLotteryResp)(nil), "channel_lottery.BreakChannelLotteryResp")
	proto.RegisterType((*GetChannelLotteryInfoReq)(nil), "channel_lottery.GetChannelLotteryInfoReq")
	proto.RegisterType((*GetChannelLotteryInfoResp)(nil), "channel_lottery.GetChannelLotteryInfoResp")
	proto.RegisterType((*LotterySimple)(nil), "channel_lottery.LotterySimple")
	proto.RegisterType((*BatchGetChannelLotteryIdReq)(nil), "channel_lottery.BatchGetChannelLotteryIdReq")
	proto.RegisterType((*BatchGetChannelLotteryIdResp)(nil), "channel_lottery.BatchGetChannelLotteryIdResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_lottery.BatchGetChannelLotteryIdResp.LotteryIdMapEntry")
	proto.RegisterMapType((map[uint32]*LotterySimple)(nil), "channel_lottery.BatchGetChannelLotteryIdResp.LotterySimpleMapEntry")
	proto.RegisterType((*ChangeOfficialInfoReq)(nil), "channel_lottery.ChangeOfficialInfoReq")
	proto.RegisterType((*ChangeOfficialInfoResp)(nil), "channel_lottery.ChangeOfficialInfoResp")
	proto.RegisterType((*GetCustomGiftReq)(nil), "channel_lottery.GetCustomGiftReq")
	proto.RegisterType((*GetCustomGiftResp)(nil), "channel_lottery.GetCustomGiftResp")
	proto.RegisterType((*SetCustomGiftReq)(nil), "channel_lottery.SetCustomGiftReq")
	proto.RegisterType((*SetCustomGiftResp)(nil), "channel_lottery.SetCustomGiftResp")
	proto.RegisterType((*SearchCustomGiftsReq)(nil), "channel_lottery.SearchCustomGiftsReq")
	proto.RegisterType((*SearchCustomGiftsResp)(nil), "channel_lottery.SearchCustomGiftsResp")
	proto.RegisterType((*CustomGift)(nil), "channel_lottery.CustomGift")
	proto.RegisterType((*GetLotteryOpenReq)(nil), "channel_lottery.GetLotteryOpenReq")
	proto.RegisterType((*GetLotteryOpenResp)(nil), "channel_lottery.GetLotteryOpenResp")
	proto.RegisterType((*SetLotteryOpenReq)(nil), "channel_lottery.SetLotteryOpenReq")
	proto.RegisterType((*SetLotteryOpenResp)(nil), "channel_lottery.SetLotteryOpenResp")
	proto.RegisterType((*SearchLotteryOpensReq)(nil), "channel_lottery.SearchLotteryOpensReq")
	proto.RegisterType((*SearchLotteryOpensResp)(nil), "channel_lottery.SearchLotteryOpensResp")
	proto.RegisterType((*LotteryOpen)(nil), "channel_lottery.LotteryOpen")
	proto.RegisterType((*FreezeOrderResultReq)(nil), "channel_lottery.FreezeOrderResultReq")
	proto.RegisterType((*FreezeOrderResultResp)(nil), "channel_lottery.FreezeOrderResultResp")
	proto.RegisterType((*SetLotteryOpenListReq)(nil), "channel_lottery.SetLotteryOpenListReq")
	proto.RegisterType((*SetLotteryOpenListResp)(nil), "channel_lottery.SetLotteryOpenListResp")
	proto.RegisterType((*ReportEnterShareChannelReq)(nil), "channel_lottery.ReportEnterShareChannelReq")
	proto.RegisterType((*ReportEnterShareChannelResp)(nil), "channel_lottery.ReportEnterShareChannelResp")
	proto.RegisterType((*GetEnterChannelUserCntReq)(nil), "channel_lottery.GetEnterChannelUserCntReq")
	proto.RegisterType((*GetEnterChannelUserCntResp)(nil), "channel_lottery.GetEnterChannelUserCntResp")
	proto.RegisterEnum("channel_lottery.ConditionBingoType", ConditionBingoType_name, ConditionBingoType_value)
	proto.RegisterEnum("channel_lottery.LotteryCondition", LotteryCondition_name, LotteryCondition_value)
	proto.RegisterEnum("channel_lottery.LotteryPersonType", LotteryPersonType_name, LotteryPersonType_value)
	proto.RegisterEnum("channel_lottery.LotteryGiftSendType", LotteryGiftSendType_name, LotteryGiftSendType_value)
	proto.RegisterEnum("channel_lottery.Condition_OpType", Condition_OpType_name, Condition_OpType_value)
	proto.RegisterEnum("channel_lottery.JoinUserInfo_Flag", JoinUserInfo_Flag_name, JoinUserInfo_Flag_value)
	proto.RegisterEnum("channel_lottery.ConditionMissionProgress_MissionStatus", ConditionMissionProgress_MissionStatus_name, ConditionMissionProgress_MissionStatus_value)
	proto.RegisterEnum("channel_lottery.ChannelLotteryInfo_LotteryStatus", ChannelLotteryInfo_LotteryStatus_name, ChannelLotteryInfo_LotteryStatus_value)
	proto.RegisterEnum("channel_lottery.SetLotteryOpenListReq_SetSource", SetLotteryOpenListReq_SetSource_name, SetLotteryOpenListReq_SetSource_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelLotteryClient is the client API for ChannelLottery service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelLotteryClient interface {
	// -------------------- 官频-抽奖 --------------------
	// 获取是否有抽奖功能
	ShowChannelLotterySetting(ctx context.Context, in *ShowChannelLotterySettingReq, opts ...grpc.CallOption) (*ShowChannelLotterySettingResp, error)
	// 获取抽奖设置信息
	GetChannelLotterySetting(ctx context.Context, in *GetChannelLotterySettingReq, opts ...grpc.CallOption) (*GetChannelLotterySettingResp, error)
	// 设置抽奖信息
	SetChannelLotteryInfo(ctx context.Context, in *SetChannelLotteryInfoReq, opts ...grpc.CallOption) (*SetChannelLotteryInfoResp, error)
	// 点击参与抽奖
	JoinChannelLottery(ctx context.Context, in *JoinChannelLotteryReq, opts ...grpc.CallOption) (*JoinChannelLotteryResp, error)
	// 获取本次排班所有抽奖配置和抽奖结果
	GetChannelLotteryInfoList(ctx context.Context, in *GetChannelLotteryInfoListReq, opts ...grpc.CallOption) (*GetChannelLotteryInfoListResp, error)
	// 获取本次排班所有抽奖配置和抽奖结果
	BeginChannelLottery(ctx context.Context, in *BeginChannelLotteryReq, opts ...grpc.CallOption) (*BeginChannelLotteryResp, error)
	// 确定并开始抽奖
	BreakChannelLottery(ctx context.Context, in *BreakChannelLotteryReq, opts ...grpc.CallOption) (*BreakChannelLotteryResp, error)
	// 确认送礼
	SendChannelLotteryPresent(ctx context.Context, in *SendChannelLotteryPresentReq, opts ...grpc.CallOption) (*SendChannelLotteryPresentResp, error)
	// 获取开始抽奖信息
	GetChannelLotteryInfo(ctx context.Context, in *GetChannelLotteryInfoReq, opts ...grpc.CallOption) (*GetChannelLotteryInfoResp, error)
	// 批量获取开始抽奖信息
	BatchGetChannelLotteryId(ctx context.Context, in *BatchGetChannelLotteryIdReq, opts ...grpc.CallOption) (*BatchGetChannelLotteryIdResp, error)
	// 修改官频转播信息，目前只是修改结束时间
	ChangeOfficialInfo(ctx context.Context, in *ChangeOfficialInfoReq, opts ...grpc.CallOption) (*ChangeOfficialInfoResp, error)
	GetCustomGift(ctx context.Context, in *GetCustomGiftReq, opts ...grpc.CallOption) (*GetCustomGiftResp, error)
	SetCustomGift(ctx context.Context, in *SetCustomGiftReq, opts ...grpc.CallOption) (*SetCustomGiftResp, error)
	SearchCustomGifts(ctx context.Context, in *SearchCustomGiftsReq, opts ...grpc.CallOption) (*SearchCustomGiftsResp, error)
	GetLotteryOpen(ctx context.Context, in *GetLotteryOpenReq, opts ...grpc.CallOption) (*GetLotteryOpenResp, error)
	SetLotteryOpen(ctx context.Context, in *SetLotteryOpenReq, opts ...grpc.CallOption) (*SetLotteryOpenResp, error)
	SearchLotteryOpens(ctx context.Context, in *SearchLotteryOpensReq, opts ...grpc.CallOption) (*SearchLotteryOpensResp, error)
	ReportEnterShareChannel(ctx context.Context, in *ReportEnterShareChannelReq, opts ...grpc.CallOption) (*ReportEnterShareChannelResp, error)
	GetEnterChannelUserCnt(ctx context.Context, in *GetEnterChannelUserCntReq, opts ...grpc.CallOption) (*GetEnterChannelUserCntResp, error)
	// 背包冻结回调
	FreezeOrderResult(ctx context.Context, in *FreezeOrderResultReq, opts ...grpc.CallOption) (*FreezeOrderResultResp, error)
	SetLotteryOpenList(ctx context.Context, in *SetLotteryOpenListReq, opts ...grpc.CallOption) (*SetLotteryOpenListResp, error)
	// 奖励数据对账
	GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 扣除/消费数据对账
	GetCostTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetCostOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 回滚的数据对账
	GetRollbackTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 补发礼物
	ReissueGift(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 奖励数据对账(父订单)
	GetAwardBaseOrderTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardBaseOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 整笔订单补发
	ReissueBaseOrderGift(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error)
}

type channelLotteryClient struct {
	cc *grpc.ClientConn
}

func NewChannelLotteryClient(cc *grpc.ClientConn) ChannelLotteryClient {
	return &channelLotteryClient{cc}
}

func (c *channelLotteryClient) ShowChannelLotterySetting(ctx context.Context, in *ShowChannelLotterySettingReq, opts ...grpc.CallOption) (*ShowChannelLotterySettingResp, error) {
	out := new(ShowChannelLotterySettingResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/ShowChannelLotterySetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetChannelLotterySetting(ctx context.Context, in *GetChannelLotterySettingReq, opts ...grpc.CallOption) (*GetChannelLotterySettingResp, error) {
	out := new(GetChannelLotterySettingResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetChannelLotterySetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) SetChannelLotteryInfo(ctx context.Context, in *SetChannelLotteryInfoReq, opts ...grpc.CallOption) (*SetChannelLotteryInfoResp, error) {
	out := new(SetChannelLotteryInfoResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/SetChannelLotteryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) JoinChannelLottery(ctx context.Context, in *JoinChannelLotteryReq, opts ...grpc.CallOption) (*JoinChannelLotteryResp, error) {
	out := new(JoinChannelLotteryResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/JoinChannelLottery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetChannelLotteryInfoList(ctx context.Context, in *GetChannelLotteryInfoListReq, opts ...grpc.CallOption) (*GetChannelLotteryInfoListResp, error) {
	out := new(GetChannelLotteryInfoListResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetChannelLotteryInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) BeginChannelLottery(ctx context.Context, in *BeginChannelLotteryReq, opts ...grpc.CallOption) (*BeginChannelLotteryResp, error) {
	out := new(BeginChannelLotteryResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/BeginChannelLottery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) BreakChannelLottery(ctx context.Context, in *BreakChannelLotteryReq, opts ...grpc.CallOption) (*BreakChannelLotteryResp, error) {
	out := new(BreakChannelLotteryResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/BreakChannelLottery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) SendChannelLotteryPresent(ctx context.Context, in *SendChannelLotteryPresentReq, opts ...grpc.CallOption) (*SendChannelLotteryPresentResp, error) {
	out := new(SendChannelLotteryPresentResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/SendChannelLotteryPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetChannelLotteryInfo(ctx context.Context, in *GetChannelLotteryInfoReq, opts ...grpc.CallOption) (*GetChannelLotteryInfoResp, error) {
	out := new(GetChannelLotteryInfoResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetChannelLotteryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) BatchGetChannelLotteryId(ctx context.Context, in *BatchGetChannelLotteryIdReq, opts ...grpc.CallOption) (*BatchGetChannelLotteryIdResp, error) {
	out := new(BatchGetChannelLotteryIdResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/BatchGetChannelLotteryId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) ChangeOfficialInfo(ctx context.Context, in *ChangeOfficialInfoReq, opts ...grpc.CallOption) (*ChangeOfficialInfoResp, error) {
	out := new(ChangeOfficialInfoResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/ChangeOfficialInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetCustomGift(ctx context.Context, in *GetCustomGiftReq, opts ...grpc.CallOption) (*GetCustomGiftResp, error) {
	out := new(GetCustomGiftResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetCustomGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) SetCustomGift(ctx context.Context, in *SetCustomGiftReq, opts ...grpc.CallOption) (*SetCustomGiftResp, error) {
	out := new(SetCustomGiftResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/SetCustomGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) SearchCustomGifts(ctx context.Context, in *SearchCustomGiftsReq, opts ...grpc.CallOption) (*SearchCustomGiftsResp, error) {
	out := new(SearchCustomGiftsResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/SearchCustomGifts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetLotteryOpen(ctx context.Context, in *GetLotteryOpenReq, opts ...grpc.CallOption) (*GetLotteryOpenResp, error) {
	out := new(GetLotteryOpenResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetLotteryOpen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) SetLotteryOpen(ctx context.Context, in *SetLotteryOpenReq, opts ...grpc.CallOption) (*SetLotteryOpenResp, error) {
	out := new(SetLotteryOpenResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/SetLotteryOpen", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) SearchLotteryOpens(ctx context.Context, in *SearchLotteryOpensReq, opts ...grpc.CallOption) (*SearchLotteryOpensResp, error) {
	out := new(SearchLotteryOpensResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/SearchLotteryOpens", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) ReportEnterShareChannel(ctx context.Context, in *ReportEnterShareChannelReq, opts ...grpc.CallOption) (*ReportEnterShareChannelResp, error) {
	out := new(ReportEnterShareChannelResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/ReportEnterShareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetEnterChannelUserCnt(ctx context.Context, in *GetEnterChannelUserCntReq, opts ...grpc.CallOption) (*GetEnterChannelUserCntResp, error) {
	out := new(GetEnterChannelUserCntResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetEnterChannelUserCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) FreezeOrderResult(ctx context.Context, in *FreezeOrderResultReq, opts ...grpc.CallOption) (*FreezeOrderResultResp, error) {
	out := new(FreezeOrderResultResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/FreezeOrderResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) SetLotteryOpenList(ctx context.Context, in *SetLotteryOpenListReq, opts ...grpc.CallOption) (*SetLotteryOpenListResp, error) {
	out := new(SetLotteryOpenListResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/SetLotteryOpenList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetAwardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetAwardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetCostTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetCostTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetCostOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetCostOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetRollbackTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetRollbackTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) ReissueGift(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/ReissueGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetAwardBaseOrderTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetAwardBaseOrderTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GetAwardBaseOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GetAwardBaseOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) ReissueBaseOrderGift(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/ReissueBaseOrderGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryClient) GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error) {
	out := new(reconcile_v2.GenFinancialFileResp)
	err := c.cc.Invoke(ctx, "/channel_lottery.ChannelLottery/GenFinancialFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelLotteryServer is the server API for ChannelLottery service.
type ChannelLotteryServer interface {
	// -------------------- 官频-抽奖 --------------------
	// 获取是否有抽奖功能
	ShowChannelLotterySetting(context.Context, *ShowChannelLotterySettingReq) (*ShowChannelLotterySettingResp, error)
	// 获取抽奖设置信息
	GetChannelLotterySetting(context.Context, *GetChannelLotterySettingReq) (*GetChannelLotterySettingResp, error)
	// 设置抽奖信息
	SetChannelLotteryInfo(context.Context, *SetChannelLotteryInfoReq) (*SetChannelLotteryInfoResp, error)
	// 点击参与抽奖
	JoinChannelLottery(context.Context, *JoinChannelLotteryReq) (*JoinChannelLotteryResp, error)
	// 获取本次排班所有抽奖配置和抽奖结果
	GetChannelLotteryInfoList(context.Context, *GetChannelLotteryInfoListReq) (*GetChannelLotteryInfoListResp, error)
	// 获取本次排班所有抽奖配置和抽奖结果
	BeginChannelLottery(context.Context, *BeginChannelLotteryReq) (*BeginChannelLotteryResp, error)
	// 确定并开始抽奖
	BreakChannelLottery(context.Context, *BreakChannelLotteryReq) (*BreakChannelLotteryResp, error)
	// 确认送礼
	SendChannelLotteryPresent(context.Context, *SendChannelLotteryPresentReq) (*SendChannelLotteryPresentResp, error)
	// 获取开始抽奖信息
	GetChannelLotteryInfo(context.Context, *GetChannelLotteryInfoReq) (*GetChannelLotteryInfoResp, error)
	// 批量获取开始抽奖信息
	BatchGetChannelLotteryId(context.Context, *BatchGetChannelLotteryIdReq) (*BatchGetChannelLotteryIdResp, error)
	// 修改官频转播信息，目前只是修改结束时间
	ChangeOfficialInfo(context.Context, *ChangeOfficialInfoReq) (*ChangeOfficialInfoResp, error)
	GetCustomGift(context.Context, *GetCustomGiftReq) (*GetCustomGiftResp, error)
	SetCustomGift(context.Context, *SetCustomGiftReq) (*SetCustomGiftResp, error)
	SearchCustomGifts(context.Context, *SearchCustomGiftsReq) (*SearchCustomGiftsResp, error)
	GetLotteryOpen(context.Context, *GetLotteryOpenReq) (*GetLotteryOpenResp, error)
	SetLotteryOpen(context.Context, *SetLotteryOpenReq) (*SetLotteryOpenResp, error)
	SearchLotteryOpens(context.Context, *SearchLotteryOpensReq) (*SearchLotteryOpensResp, error)
	ReportEnterShareChannel(context.Context, *ReportEnterShareChannelReq) (*ReportEnterShareChannelResp, error)
	GetEnterChannelUserCnt(context.Context, *GetEnterChannelUserCntReq) (*GetEnterChannelUserCntResp, error)
	// 背包冻结回调
	FreezeOrderResult(context.Context, *FreezeOrderResultReq) (*FreezeOrderResultResp, error)
	SetLotteryOpenList(context.Context, *SetLotteryOpenListReq) (*SetLotteryOpenListResp, error)
	// 奖励数据对账
	GetAwardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 扣除/消费数据对账
	GetCostTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetCostOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 回滚的数据对账
	GetRollbackTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 补发礼物
	ReissueGift(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 奖励数据对账(父订单)
	GetAwardBaseOrderTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardBaseOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 整笔订单补发
	ReissueBaseOrderGift(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	GenFinancialFile(context.Context, *reconcile_v2.GenFinancialFileReq) (*reconcile_v2.GenFinancialFileResp, error)
}

func RegisterChannelLotteryServer(s *grpc.Server, srv ChannelLotteryServer) {
	s.RegisterService(&_ChannelLottery_serviceDesc, srv)
}

func _ChannelLottery_ShowChannelLotterySetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShowChannelLotterySettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).ShowChannelLotterySetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/ShowChannelLotterySetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).ShowChannelLotterySetting(ctx, req.(*ShowChannelLotterySettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetChannelLotterySetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelLotterySettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetChannelLotterySetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetChannelLotterySetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetChannelLotterySetting(ctx, req.(*GetChannelLotterySettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_SetChannelLotteryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelLotteryInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).SetChannelLotteryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/SetChannelLotteryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).SetChannelLotteryInfo(ctx, req.(*SetChannelLotteryInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_JoinChannelLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinChannelLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).JoinChannelLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/JoinChannelLottery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).JoinChannelLottery(ctx, req.(*JoinChannelLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetChannelLotteryInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelLotteryInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetChannelLotteryInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetChannelLotteryInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetChannelLotteryInfoList(ctx, req.(*GetChannelLotteryInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_BeginChannelLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BeginChannelLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).BeginChannelLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/BeginChannelLottery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).BeginChannelLottery(ctx, req.(*BeginChannelLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_BreakChannelLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BreakChannelLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).BreakChannelLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/BreakChannelLottery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).BreakChannelLottery(ctx, req.(*BreakChannelLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_SendChannelLotteryPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendChannelLotteryPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).SendChannelLotteryPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/SendChannelLotteryPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).SendChannelLotteryPresent(ctx, req.(*SendChannelLotteryPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetChannelLotteryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelLotteryInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetChannelLotteryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetChannelLotteryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetChannelLotteryInfo(ctx, req.(*GetChannelLotteryInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_BatchGetChannelLotteryId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelLotteryIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).BatchGetChannelLotteryId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/BatchGetChannelLotteryId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).BatchGetChannelLotteryId(ctx, req.(*BatchGetChannelLotteryIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_ChangeOfficialInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeOfficialInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).ChangeOfficialInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/ChangeOfficialInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).ChangeOfficialInfo(ctx, req.(*ChangeOfficialInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetCustomGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetCustomGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetCustomGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetCustomGift(ctx, req.(*GetCustomGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_SetCustomGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCustomGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).SetCustomGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/SetCustomGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).SetCustomGift(ctx, req.(*SetCustomGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_SearchCustomGifts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCustomGiftsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).SearchCustomGifts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/SearchCustomGifts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).SearchCustomGifts(ctx, req.(*SearchCustomGiftsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetLotteryOpen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotteryOpenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetLotteryOpen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetLotteryOpen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetLotteryOpen(ctx, req.(*GetLotteryOpenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_SetLotteryOpen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLotteryOpenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).SetLotteryOpen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/SetLotteryOpen",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).SetLotteryOpen(ctx, req.(*SetLotteryOpenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_SearchLotteryOpens_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchLotteryOpensReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).SearchLotteryOpens(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/SearchLotteryOpens",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).SearchLotteryOpens(ctx, req.(*SearchLotteryOpensReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_ReportEnterShareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportEnterShareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).ReportEnterShareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/ReportEnterShareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).ReportEnterShareChannel(ctx, req.(*ReportEnterShareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetEnterChannelUserCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterChannelUserCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetEnterChannelUserCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetEnterChannelUserCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetEnterChannelUserCnt(ctx, req.(*GetEnterChannelUserCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_FreezeOrderResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeOrderResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).FreezeOrderResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/FreezeOrderResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).FreezeOrderResult(ctx, req.(*FreezeOrderResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_SetLotteryOpenList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLotteryOpenListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).SetLotteryOpenList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/SetLotteryOpenList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).SetLotteryOpenList(ctx, req.(*SetLotteryOpenListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetAwardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetAwardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetAwardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetAwardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetAwardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetAwardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetAwardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetAwardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetCostTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetCostTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetCostTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetCostTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetCostOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetCostOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetCostOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetCostOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetRollbackTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetRollbackTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetRollbackTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetRollbackTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_ReissueGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).ReissueGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/ReissueGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).ReissueGift(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetAwardBaseOrderTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetAwardBaseOrderTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetAwardBaseOrderTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetAwardBaseOrderTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GetAwardBaseOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GetAwardBaseOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GetAwardBaseOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GetAwardBaseOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_ReissueBaseOrderGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).ReissueBaseOrderGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/ReissueBaseOrderGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).ReissueBaseOrderGift(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLottery_GenFinancialFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.GenFinancialFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryServer).GenFinancialFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_lottery.ChannelLottery/GenFinancialFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryServer).GenFinancialFile(ctx, req.(*reconcile_v2.GenFinancialFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelLottery_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_lottery.ChannelLottery",
	HandlerType: (*ChannelLotteryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ShowChannelLotterySetting",
			Handler:    _ChannelLottery_ShowChannelLotterySetting_Handler,
		},
		{
			MethodName: "GetChannelLotterySetting",
			Handler:    _ChannelLottery_GetChannelLotterySetting_Handler,
		},
		{
			MethodName: "SetChannelLotteryInfo",
			Handler:    _ChannelLottery_SetChannelLotteryInfo_Handler,
		},
		{
			MethodName: "JoinChannelLottery",
			Handler:    _ChannelLottery_JoinChannelLottery_Handler,
		},
		{
			MethodName: "GetChannelLotteryInfoList",
			Handler:    _ChannelLottery_GetChannelLotteryInfoList_Handler,
		},
		{
			MethodName: "BeginChannelLottery",
			Handler:    _ChannelLottery_BeginChannelLottery_Handler,
		},
		{
			MethodName: "BreakChannelLottery",
			Handler:    _ChannelLottery_BreakChannelLottery_Handler,
		},
		{
			MethodName: "SendChannelLotteryPresent",
			Handler:    _ChannelLottery_SendChannelLotteryPresent_Handler,
		},
		{
			MethodName: "GetChannelLotteryInfo",
			Handler:    _ChannelLottery_GetChannelLotteryInfo_Handler,
		},
		{
			MethodName: "BatchGetChannelLotteryId",
			Handler:    _ChannelLottery_BatchGetChannelLotteryId_Handler,
		},
		{
			MethodName: "ChangeOfficialInfo",
			Handler:    _ChannelLottery_ChangeOfficialInfo_Handler,
		},
		{
			MethodName: "GetCustomGift",
			Handler:    _ChannelLottery_GetCustomGift_Handler,
		},
		{
			MethodName: "SetCustomGift",
			Handler:    _ChannelLottery_SetCustomGift_Handler,
		},
		{
			MethodName: "SearchCustomGifts",
			Handler:    _ChannelLottery_SearchCustomGifts_Handler,
		},
		{
			MethodName: "GetLotteryOpen",
			Handler:    _ChannelLottery_GetLotteryOpen_Handler,
		},
		{
			MethodName: "SetLotteryOpen",
			Handler:    _ChannelLottery_SetLotteryOpen_Handler,
		},
		{
			MethodName: "SearchLotteryOpens",
			Handler:    _ChannelLottery_SearchLotteryOpens_Handler,
		},
		{
			MethodName: "ReportEnterShareChannel",
			Handler:    _ChannelLottery_ReportEnterShareChannel_Handler,
		},
		{
			MethodName: "GetEnterChannelUserCnt",
			Handler:    _ChannelLottery_GetEnterChannelUserCnt_Handler,
		},
		{
			MethodName: "FreezeOrderResult",
			Handler:    _ChannelLottery_FreezeOrderResult_Handler,
		},
		{
			MethodName: "SetLotteryOpenList",
			Handler:    _ChannelLottery_SetLotteryOpenList_Handler,
		},
		{
			MethodName: "GetAwardTotalCount",
			Handler:    _ChannelLottery_GetAwardTotalCount_Handler,
		},
		{
			MethodName: "GetAwardOrderIds",
			Handler:    _ChannelLottery_GetAwardOrderIds_Handler,
		},
		{
			MethodName: "GetCostTotalCount",
			Handler:    _ChannelLottery_GetCostTotalCount_Handler,
		},
		{
			MethodName: "GetCostOrderIds",
			Handler:    _ChannelLottery_GetCostOrderIds_Handler,
		},
		{
			MethodName: "GetRollbackTotalCount",
			Handler:    _ChannelLottery_GetRollbackTotalCount_Handler,
		},
		{
			MethodName: "ReissueGift",
			Handler:    _ChannelLottery_ReissueGift_Handler,
		},
		{
			MethodName: "GetAwardBaseOrderTotalCount",
			Handler:    _ChannelLottery_GetAwardBaseOrderTotalCount_Handler,
		},
		{
			MethodName: "GetAwardBaseOrderIds",
			Handler:    _ChannelLottery_GetAwardBaseOrderIds_Handler,
		},
		{
			MethodName: "ReissueBaseOrderGift",
			Handler:    _ChannelLottery_ReissueBaseOrderGift_Handler,
		},
		{
			MethodName: "GenFinancialFile",
			Handler:    _ChannelLottery_GenFinancialFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-lottery/channel-lottery.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-lottery/channel-lottery.proto", fileDescriptor_channel_lottery_7257bac15b05f78d)
}

var fileDescriptor_channel_lottery_7257bac15b05f78d = []byte{
	// 3376 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x3a, 0x4d, 0x73, 0xdb, 0xc8,
	0x95, 0x22, 0xa9, 0x0f, 0xf2, 0x91, 0x92, 0xa0, 0xb6, 0x25, 0x53, 0x94, 0x34, 0xb6, 0x61, 0x8f,
	0xd7, 0xe3, 0xb1, 0xe9, 0xb5, 0x66, 0xb6, 0x66, 0x76, 0x2e, 0xb3, 0x12, 0x4d, 0x49, 0xb4, 0x25,
	0x52, 0x05, 0x50, 0x9e, 0x9d, 0x9d, 0x9a, 0xc2, 0xc2, 0x44, 0x8b, 0xc2, 0x08, 0x5f, 0x46, 0x83,
	0xb2, 0x95, 0x9a, 0x5c, 0x92, 0xaa, 0xfc, 0x80, 0x5c, 0x72, 0x48, 0x55, 0x2a, 0xd7, 0x9c, 0x92,
	0x63, 0xfe, 0x41, 0x72, 0x99, 0x6b, 0xce, 0xb9, 0xe5, 0x6f, 0xa4, 0xba, 0x1b, 0x20, 0x40, 0xa0,
	0x49, 0xd1, 0x1f, 0x73, 0x22, 0xfa, 0xf5, 0xc3, 0x7b, 0xfd, 0xfa, 0x7d, 0x3f, 0x10, 0x3e, 0x0f,
	0x82, 0xc7, 0xaf, 0x06, 0x66, 0xef, 0x9c, 0x98, 0xd6, 0x05, 0xf6, 0x1f, 0xf7, 0xce, 0x74, 0xc7,
	0xc1, 0xd6, 0x23, 0xcb, 0x0d, 0x02, 0xec, 0x5f, 0xa6, 0xd7, 0x75, 0xcf, 0x77, 0x03, 0x17, 0x2d,
	0x87, 0x60, 0x2d, 0x04, 0xd7, 0xea, 0x29, 0x32, 0x3e, 0xee, 0xb9, 0x4e, 0xcf, 0xb4, 0xf0, 0xa3,
	0x8b, 0xed, 0x91, 0x05, 0x27, 0x20, 0x77, 0x60, 0x53, 0x3d, 0x73, 0x5f, 0x37, 0x38, 0x99, 0x43,
	0x4e, 0x45, 0xc5, 0x41, 0x60, 0x3a, 0x7d, 0x05, 0xbf, 0x42, 0x12, 0x14, 0x06, 0xa6, 0x51, 0xcd,
	0xdd, 0xca, 0xdd, 0x5f, 0x54, 0xe8, 0x23, 0xda, 0x02, 0x88, 0x98, 0x9a, 0x46, 0x35, 0xcf, 0x36,
	0x4a, 0x21, 0xa4, 0x65, 0xc8, 0x9f, 0xc1, 0xd6, 0x04, 0x82, 0xc4, 0x43, 0x08, 0x66, 0xc9, 0x99,
	0xfb, 0x9a, 0x91, 0x2c, 0x2a, 0xec, 0x59, 0x3e, 0x87, 0x4a, 0xc7, 0x0b, 0x4c, 0xd7, 0xd9, 0x1d,
	0x04, 0x81, 0xeb, 0x50, 0x1c, 0x47, 0xb7, 0x31, 0xc3, 0x29, 0x29, 0xec, 0x99, 0xf2, 0x75, 0x19,
	0x8e, 0x76, 0xa1, 0x5b, 0x11, 0x5f, 0x0e, 0x79, 0xa1, 0x5b, 0xe8, 0x3e, 0x48, 0xa7, 0xe6, 0x1b,
	0x6c, 0x68, 0x1e, 0x76, 0x3d, 0x0b, 0x6b, 0xce, 0xc0, 0xae, 0x16, 0x18, 0xd2, 0x12, 0x83, 0x1f,
	0x33, 0x70, 0x7b, 0x60, 0xcb, 0x6d, 0xd8, 0xd8, 0xc7, 0xc1, 0x87, 0x93, 0xf8, 0x9f, 0x05, 0xd8,
	0x1c, 0x4f, 0x90, 0x78, 0x68, 0x07, 0x96, 0x7a, 0xae, 0x63, 0x98, 0xec, 0xf0, 0x96, 0x49, 0x82,
	0x6a, 0xee, 0x56, 0xe1, 0x7e, 0x79, 0xbb, 0x56, 0x4f, 0x69, 0xaf, 0xde, 0x88, 0xd0, 0x94, 0xc5,
	0xe1, 0x1b, 0x87, 0x26, 0x09, 0xd0, 0x75, 0x98, 0xb3, 0x4c, 0xdb, 0x0c, 0x42, 0xee, 0x7c, 0x81,
	0xd6, 0xa1, 0xc8, 0x1e, 0xb4, 0x8b, 0xed, 0x50, 0xd6, 0x05, 0xb6, 0x7e, 0xb1, 0x8d, 0xf6, 0x60,
	0x65, 0x94, 0x27, 0xc5, 0x99, 0xbd, 0x92, 0xed, 0xf2, 0x08, 0xdb, 0x17, 0xdb, 0x48, 0x87, 0x72,
	0x78, 0xa1, 0xec, 0xe0, 0x73, 0x8c, 0xc2, 0xff, 0x64, 0x28, 0x4c, 0x92, 0xbf, 0xde, 0xd5, 0xfd,
	0x3e, 0x0e, 0xb8, 0x02, 0xb8, 0x9a, 0x15, 0xe0, 0x44, 0x29, 0x93, 0xda, 0x1f, 0x73, 0x80, 0xb2,
	0x28, 0x68, 0x1b, 0xf2, 0xc1, 0x25, 0x53, 0xc3, 0xd2, 0xb6, 0x9c, 0x61, 0x18, 0xb2, 0x39, 0xc6,
	0x3e, 0x71, 0x9d, 0xee, 0xa5, 0x87, 0x95, 0x7c, 0x70, 0x39, 0xb4, 0x9b, 0x7c, 0xc2, 0x6e, 0x76,
	0x61, 0x91, 0x5f, 0x12, 0xb7, 0x15, 0x52, 0x2d, 0x30, 0x19, 0xb6, 0x32, 0x24, 0x93, 0x16, 0xa8,
	0x54, 0xd8, 0x3b, 0x1c, 0x44, 0xe4, 0xdf, 0xe4, 0xa1, 0x34, 0xbc, 0x24, 0xb4, 0x04, 0xf9, 0xa1,
	0x81, 0xe4, 0x4d, 0xe3, 0xe7, 0xe2, 0x8a, 0xbe, 0x82, 0x05, 0xd7, 0xd3, 0x82, 0x4b, 0x0f, 0x57,
	0x67, 0xd9, 0x35, 0xdc, 0x1e, 0xaf, 0xb9, 0x7a, 0xc7, 0x63, 0xb7, 0x30, 0xef, 0xb2, 0x5f, 0xa1,
	0x3b, 0xcc, 0x09, 0xdd, 0xe1, 0x53, 0x98, 0xe7, 0xef, 0xa2, 0x22, 0xcc, 0xb6, 0x5d, 0x07, 0x4b,
	0x33, 0xa8, 0x04, 0x73, 0x2d, 0xc7, 0x1b, 0x04, 0x52, 0x0e, 0x55, 0xa0, 0xd8, 0x38, 0x73, 0x09,
	0xee, 0x38, 0x58, 0xca, 0xcb, 0x3f, 0xcd, 0x42, 0x55, 0x4d, 0xeb, 0xba, 0xe5, 0x9c, 0xba, 0xef,
	0xe2, 0x39, 0xe8, 0x36, 0x54, 0x62, 0x23, 0x35, 0x8d, 0xd0, 0x86, 0xcb, 0x43, 0x58, 0xcb, 0x40,
	0x1f, 0x27, 0x7d, 0x27, 0xc0, 0x6f, 0x02, 0x76, 0x15, 0xa5, 0x84, 0x7f, 0x74, 0xf1, 0x9b, 0x00,
	0xad, 0xc1, 0xbc, 0x33, 0xb0, 0x5f, 0x62, 0x3f, 0x14, 0x32, 0x5c, 0x21, 0x19, 0x16, 0xf5, 0xd7,
	0xba, 0x6f, 0x68, 0x7d, 0xf3, 0x34, 0xa0, 0x2c, 0xe6, 0x39, 0x0b, 0x06, 0xdc, 0x37, 0x4f, 0x83,
	0x96, 0x81, 0x6e, 0xc0, 0x82, 0xe9, 0x69, 0xba, 0x61, 0xf8, 0xd5, 0x05, 0x46, 0x7b, 0xde, 0xf4,
	0x76, 0x0c, 0xc3, 0x47, 0x1b, 0x50, 0x32, 0xf0, 0x85, 0xd9, 0xc3, 0xf4, 0xc5, 0xe2, 0xad, 0xdc,
	0xfd, 0x8a, 0x52, 0xe4, 0x80, 0x96, 0x81, 0x6e, 0x41, 0x85, 0xd8, 0x5a, 0xbc, 0x5f, 0x62, 0xfb,
	0x40, 0xec, 0xa7, 0x11, 0xc6, 0x5d, 0x58, 0xea, 0x0d, 0x48, 0xe0, 0xda, 0x43, 0xe6, 0xc0, 0x98,
	0x57, 0x38, 0x34, 0xe4, 0xfe, 0x10, 0x50, 0x2c, 0x60, 0xdf, 0xd5, 0x2d, 0x16, 0xde, 0xca, 0x0c,
	0x53, 0x1a, 0xee, 0xec, 0xbb, 0xba, 0x45, 0xa3, 0xdc, 0x33, 0x58, 0x62, 0xc4, 0x08, 0x76, 0x0c,
	0x6e, 0x19, 0x15, 0x66, 0x19, 0x77, 0xc7, 0x39, 0x08, 0xe5, 0xa2, 0x62, 0xc7, 0x60, 0xc6, 0x51,
	0xe9, 0x27, 0x56, 0xa8, 0x03, 0x28, 0x60, 0x6e, 0x17, 0xd9, 0x88, 0xe9, 0x9c, 0xba, 0xd5, 0xc5,
	0x5b, 0xb9, 0xfb, 0x65, 0x81, 0xa5, 0x25, 0x3d, 0x94, 0x29, 0x5c, 0x0a, 0x52, 0x10, 0xf4, 0x04,
	0x56, 0x2d, 0x7c, 0x81, 0x2d, 0x6d, 0xe0, 0x71, 0x91, 0x2f, 0xb0, 0x4f, 0x4c, 0xd7, 0xa9, 0x2e,
	0x31, 0x69, 0x10, 0xdb, 0x3c, 0xf1, 0xe8, 0x91, 0x5e, 0xf0, 0x1d, 0x79, 0x17, 0xd6, 0xc7, 0x98,
	0x13, 0xf1, 0x46, 0x75, 0x9f, 0xc8, 0x07, 0xb1, 0xee, 0xdb, 0xba, 0x8d, 0xe5, 0x03, 0x58, 0x7d,
	0xe6, 0x9a, 0xce, 0x28, 0x91, 0x77, 0x8a, 0xe4, 0x55, 0x58, 0x13, 0x51, 0x22, 0x9e, 0xfc, 0x4b,
	0x41, 0x88, 0xa7, 0xe7, 0xa4, 0x01, 0xec, 0x9d, 0x4c, 0x7f, 0x0d, 0xe6, 0xdd, 0xd3, 0x53, 0x82,
	0x83, 0xd0, 0xe8, 0xc3, 0x55, 0x1c, 0xe8, 0x67, 0x13, 0x81, 0x5e, 0xf6, 0x60, 0x6b, 0x02, 0x7b,
	0xe2, 0xa1, 0x0e, 0xac, 0x84, 0x8a, 0x62, 0x5a, 0x4c, 0x66, 0x99, 0x3b, 0xd9, 0xa0, 0x91, 0xbd,
	0xee, 0x65, 0x6b, 0x94, 0xa8, 0xfc, 0xfb, 0x1c, 0x54, 0xe8, 0x5d, 0x9c, 0x10, 0xec, 0x33, 0xe5,
	0x66, 0x25, 0x5c, 0x87, 0x62, 0x6f, 0xe0, 0xfb, 0x89, 0x74, 0xbc, 0x40, 0xd7, 0xd4, 0x4c, 0xbf,
	0x86, 0x12, 0xcd, 0xeb, 0xda, 0xa9, 0xa5, 0xf7, 0x99, 0x80, 0xa2, 0x10, 0x9e, 0x24, 0x5f, 0xdf,
	0xb3, 0xf4, 0xbe, 0x52, 0xa4, 0x2f, 0xd1, 0x27, 0x79, 0x0b, 0x66, 0xe9, 0x2f, 0x02, 0x98, 0x6f,
	0xb8, 0xb6, 0xed, 0x3a, 0x3c, 0x28, 0xed, 0x9a, 0x4e, 0xdf, 0x95, 0x72, 0xf2, 0x9f, 0xf3, 0x50,
	0x1d, 0x86, 0xbe, 0x23, 0x93, 0x50, 0x5b, 0x3a, 0xf6, 0xdd, 0xbe, 0x8f, 0x09, 0x41, 0x1d, 0x98,
	0x27, 0x81, 0x1e, 0x0c, 0x48, 0x98, 0x3c, 0xbe, 0x18, 0x1f, 0x35, 0x53, 0xaf, 0xd6, 0xc3, 0xb5,
	0xca, 0x5e, 0x57, 0x42, 0x32, 0x34, 0x96, 0x32, 0x41, 0x6d, 0xbe, 0x9b, 0x10, 0x78, 0x89, 0xc2,
	0xc3, 0x97, 0xa8, 0xdc, 0x77, 0x60, 0xd1, 0x0b, 0x69, 0x69, 0x06, 0x26, 0x3d, 0x26, 0x7b, 0x49,
	0xa9, 0x44, 0xc0, 0xa7, 0x98, 0xf4, 0xd0, 0x57, 0x50, 0x1a, 0x10, 0xec, 0x73, 0x1d, 0xcd, 0x8e,
	0x49, 0x0b, 0xc9, 0xcb, 0x51, 0x8a, 0x14, 0x9f, 0xa9, 0xe5, 0x0b, 0x58, 0x1c, 0x39, 0x23, 0x2a,
	0xc3, 0x42, 0xdb, 0x0d, 0x28, 0xb6, 0x34, 0x83, 0x16, 0xa1, 0xd4, 0x76, 0x83, 0x3d, 0xd3, 0x31,
	0xc9, 0x99, 0x94, 0xa3, 0x97, 0x17, 0x3e, 0xe7, 0xe5, 0xbf, 0x00, 0xa0, 0xac, 0xde, 0xa9, 0x95,
	0x0e, 0xed, 0x26, 0x52, 0x6e, 0x29, 0xb2, 0x05, 0x03, 0xdd, 0x84, 0x32, 0xf1, 0x5c, 0x87, 0xb8,
	0xbe, 0x36, 0x18, 0x5a, 0x31, 0x84, 0xa0, 0x13, 0x93, 0x21, 0xb8, 0xa7, 0xa7, 0x66, 0xcf, 0xd4,
	0xad, 0x38, 0x80, 0x43, 0x04, 0x6a, 0xa5, 0xdd, 0x60, 0xf6, 0xaa, 0x0c, 0x30, 0x37, 0x4d, 0x06,
	0x98, 0x17, 0x65, 0x80, 0xa1, 0xe3, 0x2c, 0x24, 0x2b, 0xa4, 0x4c, 0xfc, 0x2f, 0x66, 0xe3, 0xff,
	0x3d, 0x58, 0x4e, 0xe0, 0xb0, 0x38, 0x53, 0xe2, 0x1c, 0x86, 0x58, 0x34, 0xce, 0xd0, 0x78, 0x9e,
	0xa4, 0x65, 0xf7, 0x59, 0x3c, 0x2f, 0x29, 0x95, 0x98, 0x98, 0xdd, 0x47, 0xad, 0xa4, 0x76, 0xcb,
	0x4c, 0xbb, 0x0f, 0xa7, 0xf0, 0xc0, 0x7a, 0x56, 0xd9, 0xf4, 0xee, 0x5e, 0xe2, 0xbe, 0xe9, 0x68,
	0x81, 0x69, 0xf3, 0x40, 0x5f, 0x50, 0x4a, 0x0c, 0xd2, 0x35, 0x6d, 0x8c, 0x36, 0xa1, 0xd4, 0x73,
	0x07, 0x4e, 0x60, 0xb8, 0xaf, 0x1d, 0x16, 0xb6, 0xe9, 0xcd, 0x46, 0x00, 0x1a, 0x60, 0x42, 0x2f,
	0xe0, 0xd1, 0x37, 0x32, 0xe6, 0x6c, 0x56, 0x5a, 0x16, 0x64, 0x25, 0x66, 0xf2, 0x31, 0x16, 0xbb,
	0x14, 0x89, 0x49, 0xbb, 0x14, 0xe3, 0xb1, 0x5b, 0xb9, 0x07, 0xcb, 0x23, 0xf4, 0xec, 0x7e, 0x75,
	0x25, 0xd4, 0x4f, 0x4c, 0xd0, 0xee, 0x0b, 0x82, 0x39, 0x12, 0x04, 0xf3, 0x31, 0xe9, 0xf0, 0xda,
	0xd4, 0xe9, 0xf0, 0xfa, 0x07, 0x4e, 0x87, 0xab, 0xef, 0x9e, 0x0e, 0x4f, 0xe0, 0x7a, 0x2c, 0xca,
	0x4b, 0x1a, 0xb9, 0xf8, 0x11, 0xd7, 0xd8, 0x11, 0xef, 0x8c, 0x8f, 0x4a, 0x2c, 0xca, 0xb1, 0x13,
	0xc6, 0x77, 0x31, 0x84, 0xa1, 0x2e, 0x48, 0x51, 0x20, 0x8a, 0xc2, 0x4a, 0xf5, 0x06, 0x3b, 0xe5,
	0x27, 0x53, 0x07, 0x3a, 0x65, 0xd9, 0x4e, 0x05, 0xcd, 0x2f, 0xa1, 0x3a, 0xaa, 0x1e, 0xed, 0xcc,
	0xec, 0x9f, 0x59, 0x66, 0xff, 0x2c, 0xa8, 0x56, 0x99, 0xa2, 0xd6, 0x46, 0x14, 0x75, 0x10, 0xed,
	0xd6, 0xde, 0x40, 0x71, 0x42, 0x92, 0xa8, 0xc2, 0x82, 0xde, 0x63, 0x56, 0x19, 0x96, 0xc7, 0xd1,
	0x12, 0xd5, 0xa0, 0xe8, 0x98, 0xbd, 0x73, 0x66, 0x0a, 0x3c, 0x4c, 0x0e, 0xd7, 0x94, 0x0e, 0xc1,
	0x6f, 0x58, 0xb8, 0x98, 0x53, 0xe8, 0x23, 0xad, 0xb1, 0x83, 0x20, 0x0c, 0x10, 0x25, 0x85, 0x3d,
	0xcb, 0xdf, 0xc3, 0x62, 0xd4, 0x6d, 0x0c, 0x83, 0xa1, 0xda, 0xec, 0x76, 0x5b, 0xed, 0x7d, 0x69,
	0x86, 0x46, 0x3f, 0xa5, 0xd9, 0xe8, 0x28, 0x4f, 0x79, 0x24, 0x54, 0x9a, 0xea, 0xc9, 0x61, 0x57,
	0xca, 0xb3, 0xa8, 0xd8, 0x6a, 0xb7, 0xd4, 0x03, 0xa9, 0xc0, 0x52, 0x8a, 0xd2, 0xdc, 0x79, 0x2e,
	0xcd, 0x22, 0x09, 0x2a, 0x1c, 0x45, 0x6b, 0x2a, 0x4a, 0x47, 0x91, 0xe6, 0xe4, 0x00, 0xa4, 0xb4,
	0x96, 0x51, 0x83, 0xb6, 0x43, 0xb4, 0xe5, 0xe0, 0xaa, 0x9c, 0xbe, 0x3b, 0x01, 0x6f, 0xf8, 0x4c,
	0xeb, 0xca, 0xb0, 0x6d, 0x1b, 0x26, 0x12, 0xde, 0xc7, 0xbd, 0xd0, 0x2d, 0xb9, 0x05, 0x6b, 0xbb,
	0xd4, 0xc5, 0x3f, 0x40, 0x39, 0xb3, 0x0e, 0x37, 0x84, 0xa4, 0x88, 0x27, 0xff, 0x2b, 0x07, 0x9b,
	0xd4, 0xf2, 0x47, 0xb7, 0x8e, 0x7d, 0x4c, 0xb0, 0xf3, 0x6e, 0x05, 0xcd, 0x68, 0x26, 0x29, 0xa4,
	0x33, 0xc9, 0x2a, 0xcc, 0xeb, 0x9e, 0x17, 0xe7, 0x80, 0x39, 0xdd, 0xf3, 0x5a, 0x06, 0xbd, 0x0a,
	0x5b, 0xf7, 0xcf, 0x71, 0x10, 0x07, 0xff, 0x22, 0x07, 0xb4, 0x0c, 0xd4, 0x80, 0x0a, 0xc1, 0x3e,
	0x2f, 0xb0, 0xa9, 0x2f, 0xce, 0x33, 0x2b, 0xbf, 0x95, 0xb9, 0x6d, 0x95, 0x23, 0x35, 0x02, 0xdf,
	0x62, 0xae, 0x58, 0x0e, 0xdf, 0xa2, 0x0b, 0xf9, 0x26, 0x6c, 0x4d, 0x10, 0x94, 0x78, 0xf2, 0x5f,
	0x73, 0xb0, 0x9c, 0xa2, 0x40, 0x8f, 0xd5, 0xb3, 0x4c, 0xec, 0x04, 0x9a, 0xe9, 0x85, 0x45, 0x67,
	0x91, 0x03, 0x5a, 0xde, 0x68, 0x5b, 0x90, 0x4f, 0xb5, 0x05, 0x37, 0xa1, 0x1c, 0xbe, 0xc9, 0x0c,
	0x24, 0x4c, 0x88, 0x1c, 0xc4, 0x94, 0x7f, 0x07, 0x16, 0x03, 0xec, 0xdb, 0xa6, 0xa3, 0x5b, 0x71,
	0x6b, 0xb7, 0xa8, 0x54, 0x22, 0x20, 0x43, 0xa2, 0xc1, 0x92, 0x53, 0x89, 0x4a, 0x68, 0x7e, 0x37,
	0x8b, 0x1c, 0x1a, 0x55, 0xcf, 0x5f, 0xc0, 0xda, 0xae, 0x8f, 0xf5, 0xf3, 0xac, 0xad, 0x8c, 0x2a,
	0x2b, 0x27, 0xb2, 0x0c, 0xd1, 0x8b, 0xc4, 0x93, 0x9f, 0x43, 0x75, 0xff, 0x43, 0x35, 0x78, 0xf2,
	0x9f, 0x72, 0xb0, 0xbe, 0x3f, 0xb6, 0xbe, 0xdf, 0x83, 0x4a, 0xb2, 0x68, 0x65, 0x74, 0xa7, 0xac,
	0x57, 0xcb, 0x89, 0x7a, 0x95, 0xa6, 0xba, 0x1f, 0x5c, 0xd3, 0xc1, 0xfc, 0x00, 0x45, 0x25, 0x5c,
	0xb1, 0x5c, 0x42, 0xeb, 0xb6, 0x38, 0xb0, 0x51, 0x87, 0x2b, 0x84, 0xb9, 0x64, 0xe0, 0xfb, 0xc3,
	0xf0, 0x48, 0x1d, 0xef, 0x87, 0x38, 0x9a, 0x98, 0xb6, 0x67, 0xe1, 0xab, 0x6a, 0xa3, 0x2b, 0xfc,
	0x61, 0x03, 0x4a, 0x71, 0xea, 0x0c, 0xe3, 0x5b, 0x3f, 0x4c, 0x9a, 0x72, 0x13, 0x36, 0x76, 0xf5,
	0xa0, 0x77, 0x96, 0xbd, 0x1b, 0x83, 0xde, 0x33, 0xcd, 0xa9, 0x43, 0xd2, 0x71, 0x2d, 0x4f, 0xf5,
	0x1f, 0xd1, 0x67, 0xd5, 0xe0, 0x1f, 0x0a, 0xb0, 0x39, 0x9e, 0x0e, 0xf1, 0x10, 0x86, 0xa5, 0x58,
	0x04, 0xcd, 0xd6, 0xbd, 0xb0, 0x27, 0xf8, 0x3a, 0x73, 0xc7, 0x93, 0xc8, 0xd4, 0x87, 0xab, 0x23,
	0xdd, 0x6b, 0x3a, 0x81, 0x7f, 0xa9, 0x54, 0xac, 0x04, 0x08, 0xbd, 0x02, 0x14, 0xb1, 0x21, 0xec,
	0xee, 0x18, 0xab, 0x3c, 0x63, 0xd5, 0x78, 0x27, 0x56, 0x5c, 0x05, 0x43, 0x76, 0x92, 0x95, 0x02,
	0xd7, 0xbe, 0x86, 0x95, 0xcc, 0xa9, 0xa8, 0x7d, 0x9e, 0xe3, 0xcb, 0xc8, 0x3e, 0xcf, 0xf1, 0x25,
	0xad, 0x0a, 0x2f, 0x74, 0x6b, 0x80, 0xa3, 0xb9, 0x19, 0x5b, 0x7c, 0x95, 0xff, 0x32, 0x57, 0xeb,
	0xc1, 0xaa, 0x90, 0x97, 0x80, 0xc8, 0xe7, 0x49, 0x22, 0xe5, 0xed, 0x8f, 0xc6, 0x85, 0x7b, 0x4e,
	0x28, 0xc1, 0x44, 0xd6, 0x60, 0x95, 0x4a, 0xd9, 0xc7, 0x9d, 0xa8, 0x22, 0x0e, 0x3d, 0xe9, 0x21,
	0xac, 0x44, 0x45, 0x72, 0x23, 0x52, 0x69, 0xc8, 0x32, 0xbb, 0x41, 0x93, 0x28, 0xad, 0x52, 0xcc,
	0x70, 0xc6, 0x54, 0x50, 0xa2, 0x25, 0xed, 0x58, 0x45, 0x0c, 0x88, 0x27, 0xcb, 0x20, 0xd1, 0x3b,
	0x1e, 0xd6, 0x60, 0x94, 0x6b, 0x6a, 0x70, 0x25, 0x3f, 0x85, 0x95, 0x14, 0x0e, 0xf1, 0xd0, 0x63,
	0x98, 0xa5, 0x76, 0x1a, 0x7a, 0xe3, 0x46, 0xd6, 0x1b, 0x63, 0x74, 0x86, 0x28, 0x7f, 0x07, 0x92,
	0x9a, 0xe6, 0xf4, 0xb6, 0x44, 0xa8, 0x0f, 0x1b, 0xd8, 0xc2, 0x01, 0x8e, 0x7c, 0x98, 0xaf, 0xe4,
	0x3b, 0xb0, 0xa2, 0x66, 0x8e, 0x98, 0x96, 0xa3, 0x07, 0xd7, 0x55, 0xac, 0xfb, 0xbd, 0xb3, 0x18,
	0x8f, 0xd0, 0x53, 0xc4, 0x4d, 0x76, 0x4e, 0xdc, 0x64, 0x8f, 0x4c, 0x53, 0x47, 0x1d, 0xba, 0x90,
	0x8e, 0x65, 0xff, 0x0f, 0xab, 0x02, 0x26, 0xc4, 0x43, 0x4f, 0x60, 0x8e, 0x8a, 0x40, 0x42, 0xdf,
	0x9a, 0x28, 0x2c, 0xc7, 0xa4, 0x07, 0x88, 0x6b, 0xa2, 0x45, 0x85, 0x2f, 0xe4, 0x5f, 0xe5, 0x00,
	0x62, 0xdc, 0xcc, 0x98, 0xf1, 0x8a, 0x80, 0x13, 0x4d, 0x21, 0x0b, 0x89, 0x29, 0xa4, 0x04, 0x05,
	0x5a, 0x90, 0xf3, 0x91, 0x19, 0x7d, 0xa4, 0xf9, 0x69, 0xe0, 0x19, 0x7a, 0x80, 0x79, 0x53, 0x31,
	0xc7, 0xcc, 0x09, 0x38, 0x88, 0x59, 0xd4, 0x1d, 0x66, 0x13, 0xa1, 0x45, 0x77, 0x3c, 0xec, 0x88,
	0x0c, 0x67, 0x0f, 0x50, 0x1a, 0x89, 0x78, 0xe8, 0x3f, 0x61, 0xd6, 0xd0, 0x03, 0x3d, 0x54, 0xfa,
	0xe6, 0x38, 0x37, 0x61, 0xf8, 0x0c, 0x53, 0xfe, 0x9e, 0x69, 0x37, 0xc5, 0xec, 0xad, 0xc9, 0x8c,
	0x35, 0x9e, 0xbb, 0x80, 0xd4, 0xec, 0x31, 0xd3, 0xc2, 0xfc, 0x3a, 0x17, 0x69, 0x36, 0x81, 0xf9,
	0x6e, 0xf6, 0x63, 0x98, 0xc4, 0xb3, 0xf4, 0x64, 0x05, 0x14, 0x42, 0xae, 0xec, 0x84, 0x65, 0x13,
	0xd6, 0x44, 0x87, 0x20, 0x1e, 0xfa, 0x6f, 0x28, 0x51, 0x29, 0x93, 0x33, 0x9d, 0xc9, 0x97, 0x52,
	0xa4, 0xe8, 0xd1, 0x67, 0x03, 0x81, 0x9d, 0xfd, 0x23, 0x07, 0xe5, 0x04, 0xbe, 0xc8, 0xd0, 0x12,
	0x82, 0xe4, 0x27, 0x0b, 0x52, 0x10, 0x14, 0x82, 0x89, 0xae, 0x75, 0x36, 0xdd, 0xb5, 0xae, 0x43,
	0x91, 0x35, 0x6b, 0xb1, 0xf5, 0x45, 0xc1, 0x2c, 0x6d, 0x9b, 0xf3, 0x69, 0xdb, 0x44, 0x1f, 0x01,
	0x0c, 0x73, 0x39, 0xa9, 0x2e, 0xb0, 0x94, 0x98, 0x80, 0xc8, 0x4f, 0xe0, 0xfa, 0x9e, 0x8f, 0xf1,
	0x2f, 0x70, 0xc7, 0x37, 0xb0, 0xaf, 0x60, 0x32, 0xb0, 0x58, 0x34, 0x5a, 0x87, 0xa2, 0x4b, 0x21,
	0x51, 0x1e, 0x2f, 0x29, 0x0b, 0x6c, 0xdd, 0x32, 0xe4, 0x01, 0xac, 0x0a, 0x5e, 0x21, 0x1e, 0x8d,
	0xb9, 0x3d, 0xd7, 0x39, 0x35, 0x7d, 0x3b, 0xfc, 0x52, 0x15, 0x2d, 0xf9, 0xcc, 0xc2, 0xa6, 0xf5,
	0x7b, 0xf2, 0x6e, 0xcb, 0x1c, 0xd6, 0x60, 0xbd, 0x0d, 0xad, 0x02, 0x39, 0x0a, 0x93, 0x24, 0xaa,
	0x02, 0x19, 0x88, 0x79, 0xd9, 0xdf, 0x98, 0xcd, 0x25, 0x4d, 0x33, 0x9a, 0x24, 0xc6, 0xd6, 0x5f,
	0x78, 0x3f, 0xeb, 0xa7, 0x8a, 0x20, 0x38, 0xd0, 0x88, 0x3b, 0xf0, 0x7b, 0xd1, 0x19, 0x4a, 0x04,
	0x07, 0x2a, 0x03, 0xc8, 0x4d, 0x28, 0xa9, 0xd1, 0x02, 0xad, 0x32, 0x47, 0xe4, 0x0b, 0xed, 0x29,
	0x3e, 0xd5, 0x07, 0x56, 0x20, 0xcd, 0xa0, 0xdb, 0xb4, 0x78, 0x8e, 0xc0, 0x3b, 0xed, 0xc6, 0x41,
	0x47, 0xd1, 0x0e, 0x9b, 0x2f, 0x9a, 0x87, 0x1a, 0xed, 0xb3, 0x0e, 0x9b, 0x52, 0x4e, 0xf6, 0xa8,
	0xdd, 0x66, 0x05, 0x21, 0x1e, 0xba, 0x0f, 0x12, 0x95, 0x9e, 0x96, 0x5f, 0xa7, 0x96, 0xd9, 0x0b,
	0xf8, 0xed, 0x53, 0x9d, 0x2d, 0x51, 0x78, 0x23, 0x04, 0xf3, 0x19, 0xf8, 0x6b, 0xdf, 0x75, 0xfa,
	0x5a, 0x24, 0x2c, 0x2b, 0x8c, 0xf3, 0x0c, 0x57, 0x62, 0x3b, 0x61, 0x2e, 0xa4, 0xc5, 0xb1, 0xfc,
	0xdb, 0x1c, 0xd4, 0x14, 0xec, 0xb9, 0x7e, 0xd0, 0x74, 0x02, 0xec, 0xab, 0x67, 0xba, 0x8f, 0xc3,
	0xfd, 0x9f, 0xa3, 0x73, 0xb9, 0x0b, 0x4b, 0x84, 0xb2, 0xd0, 0x4e, 0x7d, 0xd7, 0x66, 0x63, 0xb0,
	0xb0, 0x62, 0x67, 0xd0, 0x3d, 0xdf, 0xb5, 0x4f, 0x4c, 0x43, 0xde, 0x82, 0x8d, 0xb1, 0x67, 0x22,
	0x9e, 0x7c, 0xce, 0xea, 0x60, 0xb6, 0x17, 0x82, 0x69, 0xcf, 0xdc, 0xf8, 0x59, 0x7a, 0x2d, 0xb9,
	0x0e, 0xb5, 0x71, 0xcc, 0x88, 0x47, 0xb9, 0xf5, 0x9c, 0x28, 0xa2, 0xd1, 0xc7, 0x07, 0x9f, 0x03,
	0xca, 0xce, 0x1e, 0xd0, 0x32, 0x94, 0x15, 0xdd, 0x31, 0x5c, 0x9b, 0x0f, 0x5d, 0xd9, 0x74, 0xf1,
	0x68, 0x40, 0x82, 0x70, 0x06, 0xfb, 0xe0, 0xef, 0x39, 0x90, 0x42, 0xb5, 0xc7, 0x9f, 0xc6, 0xca,
	0xb0, 0x70, 0xd2, 0x7e, 0xde, 0xee, 0x7c, 0xd3, 0x96, 0x66, 0xd0, 0x35, 0x58, 0x7e, 0xd6, 0x69,
	0xb5, 0xb5, 0xbd, 0x9d, 0xb6, 0xaa, 0xed, 0x2b, 0x9d, 0x93, 0x63, 0xfe, 0x3d, 0x49, 0x6d, 0xb6,
	0x9f, 0x6a, 0x47, 0xea, 0xbe, 0x94, 0xa7, 0x5d, 0x37, 0x5b, 0x1d, 0x2b, 0x4d, 0xb5, 0xd9, 0xee,
	0x4a, 0x05, 0x66, 0x89, 0x09, 0x88, 0xd6, 0xed, 0x1c, 0x3f, 0x91, 0x66, 0xd1, 0x06, 0xdc, 0x18,
	0x01, 0x37, 0x0e, 0x3a, 0x6a, 0x53, 0x53, 0x76, 0xda, 0xcf, 0xa5, 0x39, 0xca, 0x68, 0xaf, 0x73,
	0x78, 0xd8, 0xf9, 0x46, 0x3b, 0x6a, 0x35, 0xb4, 0x13, 0xb5, 0xa9, 0x48, 0xf3, 0x68, 0x05, 0x16,
	0xd5, 0x83, 0x1d, 0xa5, 0xa9, 0x35, 0x0e, 0x76, 0xda, 0xed, 0xe6, 0xa1, 0xb4, 0x80, 0xd6, 0x00,
	0xa5, 0x69, 0x6b, 0x6d, 0xa9, 0xf8, 0xc0, 0x1d, 0x16, 0x93, 0x71, 0xc7, 0x8e, 0x6e, 0xc2, 0xc6,
	0x61, 0xa7, 0xdb, 0x6d, 0x2a, 0xdf, 0x6a, 0xc7, 0x4d, 0x45, 0xed, 0xb4, 0xb5, 0xee, 0xb7, 0xc7,
	0x4d, 0x2d, 0x16, 0x6f, 0x03, 0x6e, 0x88, 0x10, 0x76, 0x0e, 0x0f, 0xa5, 0x1c, 0xda, 0x84, 0xaa,
	0x68, 0x93, 0x5e, 0x85, 0x94, 0x7f, 0xf0, 0x53, 0x0e, 0xae, 0x09, 0x26, 0x52, 0xa8, 0x06, 0x6b,
	0xd1, 0x5b, 0xfb, 0xad, 0xbd, 0x2e, 0x7f, 0xe7, 0x60, 0xa7, 0xfd, 0x54, 0x9a, 0x41, 0x1f, 0x41,
	0x2d, 0xbb, 0xb7, 0xbb, 0xd3, 0x78, 0x7e, 0xbc, 0xd3, 0x78, 0x2e, 0xe5, 0xc4, 0xef, 0x1e, 0x9e,
	0x34, 0x9e, 0x4b, 0x79, 0xb4, 0x05, 0xeb, 0x82, 0x3d, 0xea, 0xc8, 0x27, 0xc7, 0x52, 0x21, 0x79,
	0xd8, 0x78, 0xbb, 0xd1, 0x39, 0x3a, 0xea, 0xb4, 0xa5, 0x59, 0x1a, 0x04, 0x04, 0xbb, 0x27, 0x6a,
	0xb7, 0x73, 0xc4, 0xcf, 0x36, 0xb7, 0xfd, 0xbb, 0x2a, 0x2c, 0x8d, 0x96, 0xf3, 0xe8, 0x47, 0x58,
	0x1f, 0xfb, 0x3f, 0x00, 0xf4, 0x28, 0xdb, 0xc3, 0x4f, 0xf8, 0x13, 0x42, 0xad, 0xfe, 0x36, 0xe8,
	0xc4, 0x93, 0x67, 0xd0, 0xa5, 0xa0, 0x8b, 0x8d, 0x98, 0x3f, 0x7c, 0x8b, 0xaf, 0xd7, 0xaf, 0x6a,
	0x8f, 0xde, 0xea, 0x5b, 0xb7, 0x3c, 0x83, 0x3c, 0x16, 0xd9, 0x05, 0xb3, 0xf6, 0x4f, 0x04, 0x83,
	0x0b, 0x71, 0xa3, 0x5d, 0x7b, 0x30, 0x2d, 0x2a, 0xe3, 0xd8, 0x07, 0x94, 0xfd, 0x6c, 0x85, 0xee,
	0x09, 0xbf, 0x29, 0x64, 0x46, 0x05, 0xb5, 0xff, 0x98, 0x0a, 0x8f, 0x31, 0xfa, 0x71, 0x4c, 0x37,
	0xcf, 0x6a, 0x8d, 0x29, 0x2e, 0x2a, 0xf1, 0xc5, 0x4c, 0xa0, 0xd3, 0x89, 0x5f, 0xb8, 0xe4, 0x19,
	0xf4, 0x03, 0x5c, 0x13, 0x8c, 0xb3, 0x50, 0xf6, 0xfc, 0xe2, 0xf9, 0x59, 0xed, 0xfe, 0x74, 0x88,
	0x43, 0x5e, 0xd9, 0x01, 0x89, 0x88, 0x97, 0x70, 0xfe, 0x22, 0xe2, 0x35, 0x66, 0xde, 0xc2, 0x6e,
	0x75, 0xec, 0x84, 0x4a, 0xe4, 0x29, 0x13, 0xc6, 0x76, 0x22, 0x4f, 0x99, 0x38, 0xfc, 0x62, 0xe6,
	0xba, 0x3f, 0xa5, 0xb9, 0xee, 0x4f, 0x6f, 0xae, 0xfb, 0x13, 0xcc, 0xf5, 0x12, 0xaa, 0xe3, 0x46,
	0x00, 0x02, 0xdf, 0x9c, 0x30, 0x27, 0x11, 0xf8, 0xe6, 0xa4, 0xd9, 0x02, 0xf7, 0x94, 0x6c, 0xbb,
	0x2c, 0xf0, 0x14, 0x61, 0xd3, 0x2e, 0xf0, 0x94, 0x31, 0xbd, 0xf7, 0x0c, 0xfa, 0x5f, 0x58, 0x1c,
	0xe9, 0xac, 0xd1, 0x6d, 0xe1, 0x15, 0x25, 0x7b, 0xe6, 0x9a, 0x7c, 0x15, 0x4a, 0x44, 0x59, 0xbd,
	0x82, 0xb2, 0x7a, 0x35, 0x65, 0x55, 0x40, 0xd9, 0xa0, 0x35, 0x60, 0xaa, 0xc1, 0x45, 0x1f, 0x0b,
	0x5e, 0xcd, 0x76, 0xda, 0xb5, 0x7b, 0xd3, 0xa0, 0x31, 0x2e, 0xdf, 0xc1, 0xd2, 0x68, 0xeb, 0x88,
	0x84, 0x72, 0x8f, 0xf6, 0x84, 0xb5, 0x3b, 0x57, 0xe2, 0x44, 0xc4, 0xd5, 0xab, 0x88, 0xab, 0x53,
	0x10, 0x57, 0x45, 0xc4, 0xfb, 0xb4, 0x9b, 0x4c, 0x77, 0x68, 0x68, 0x9c, 0xe4, 0xa9, 0x5e, 0x52,
	0x60, 0x3c, 0xe2, 0x76, 0x4f, 0x9e, 0x41, 0x17, 0x70, 0x63, 0x4c, 0x2d, 0x89, 0x3e, 0xcd, 0x50,
	0x19, 0x5f, 0x09, 0xd7, 0x1e, 0x4e, 0x8f, 0xcc, 0xf8, 0x12, 0x58, 0x13, 0xd7, 0x8d, 0x48, 0xe8,
	0xe0, 0xe2, 0x6a, 0xb6, 0xf6, 0xe9, 0xd4, 0xb8, 0x91, 0xd5, 0x65, 0x1a, 0x30, 0x81, 0xd5, 0x89,
	0xfa, 0x3a, 0x81, 0xd5, 0x09, 0x7b, 0xb9, 0x48, 0x77, 0xe9, 0x2e, 0x45, 0xa8, 0x3b, 0x41, 0x4f,
	0x26, 0xd4, 0x9d, 0xa8, 0xe5, 0x91, 0x67, 0x50, 0x8b, 0x4d, 0x46, 0x76, 0x5e, 0xeb, 0xbe, 0xd1,
	0x75, 0x03, 0xdd, 0xe2, 0xfd, 0xe0, 0x7a, 0x5d, 0x89, 0xfe, 0x7a, 0xf9, 0x62, 0xbb, 0x4e, 0x3b,
	0x40, 0x85, 0x06, 0x0e, 0x4a, 0x7b, 0x6d, 0x64, 0x8b, 0xa1, 0x87, 0xa4, 0x9e, 0xb1, 0x09, 0x1e,
	0x23, 0xd5, 0xe1, 0xdd, 0x2a, 0x99, 0x44, 0x68, 0x74, 0x2b, 0x7a, 0x23, 0xa4, 0x75, 0xc0, 0x27,
	0x7d, 0x2e, 0x09, 0xde, 0xf7, 0x54, 0x2d, 0x58, 0x0e, 0x29, 0xbd, 0xf7, 0xa1, 0x0e, 0x59, 0xea,
	0x51, 0x5c, 0xcb, 0x7a, 0xa9, 0xf7, 0xce, 0xdf, 0xf7, 0x60, 0x4d, 0x28, 0x2b, 0xd8, 0x24, 0x64,
	0x80, 0x59, 0x58, 0xdc, 0x1c, 0x41, 0x54, 0xb0, 0x67, 0xe9, 0xbd, 0xc8, 0x30, 0xd2, 0x64, 0x9a,
	0xb6, 0x17, 0x44, 0xd9, 0x58, 0x61, 0xff, 0x0e, 0x65, 0xb7, 0xbe, 0xab, 0x13, 0xfe, 0xc6, 0xfb,
	0x1e, 0xad, 0x0d, 0xd7, 0x33, 0x34, 0xdf, 0xe7, 0xe2, 0xda, 0x70, 0x3d, 0x14, 0x75, 0x48, 0xee,
	0xbd, 0x64, 0xfe, 0x96, 0x5a, 0x9a, 0xb3, 0x67, 0x3a, 0xba, 0x43, 0xf3, 0xd8, 0x9e, 0x69, 0x61,
	0x74, 0x6b, 0x04, 0x3b, 0xbd, 0x4d, 0xe9, 0xdd, 0xbe, 0x02, 0x83, 0x92, 0xde, 0xfd, 0xec, 0xff,
	0x9e, 0xf4, 0x5d, 0x4b, 0x77, 0xfa, 0xf5, 0xff, 0xda, 0x0e, 0x82, 0x7a, 0xcf, 0xb5, 0x1f, 0xb3,
	0x3f, 0x1e, 0xf7, 0x5c, 0xeb, 0x71, 0xf8, 0x99, 0x8e, 0xa4, 0xff, 0xdb, 0xfc, 0x72, 0x9e, 0xa1,
	0x7c, 0xf6, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0x9e, 0x8c, 0x4f, 0xc7, 0x14, 0x2d, 0x00, 0x00,
}
